[{"primary_key": "1258827", "vector": [], "sparse_vector": [], "title": "Approximate Trace Reconstruction from a Single Trace.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The well-known trace reconstruction problem is the problem of inferring an unknown source string x ∈ {0,1}n from independent \"traces\", i.e. copies of x that have been corrupted by a δ-deletion channel which independently deletes each bit of x with probability δ and concatenates the surviving bits. The current paper considers the extreme data-limited regime in which only a single trace is provided to the reconstruction algorithm. In this setting exact reconstruction is of course impossible, and the question is to what accuracy the source string x can be approximately reconstructed.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH27"}, {"primary_key": "1258828", "vector": [], "sparse_vector": [], "title": "Improved Distributed Network Decomposition, Hitting Sets, and Spanners, via Derandomization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents significantly improved deterministic algorithms for some of the key problems in the area of distributed graph algorithms, including network decomposition, hitting sets, and spanners. As the main ingredient in these results, we develop novel randomized distributed algorithms that we can analyze using only pairwise independence, and we can thus derandomize efficiently. As our most prominent end-result, we obtain a deterministic construction for O(log n)-color O(log n · log log log n)- strong diameter network decomposition in Õ(log3 n) rounds. This is the first construction that achieves almost log n in both parameters, and it improves on a recent line of exciting progress on deterministic distributed network decompositions [<PERSON><PERSON><PERSON><PERSON>, Ghaffari STOC'20; <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Rozhoň SODA'21; <PERSON>, Gha<PERSON>ari PODC'21; <PERSON><PERSON>, <PERSON><PERSON>, Roz<PERSON>ň, Grunau FOCS'22].", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH97"}, {"primary_key": "1258829", "vector": [], "sparse_vector": [], "title": "Towards Multi-Pass Streaming Lower Bounds for Optimal Approximation of Max-Cut.", "authors": ["<PERSON><PERSON><PERSON>", "Gillat Kol", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Huacheng Yu"], "summary": "We consider the Max-Cut problem, asking how much space is needed by a streaming algorithm in order to estimate the value of the maximum cut in a graph. This problem has been extensively studied over the last decade, and we now have a near-optimal lower bound for one-pass streaming algorithms, showing that they require linear space to guarantee a better-than-2 approximation [50, 52]. This result relies on a lower bound for the cycle-finding problem, showing that it is hard for a one-pass streaming algorithm to find a cycle in a union of matchings.The end-goal of our research is to prove a similar lower bound for multi-pass streaming algorithms that guarantee a better-than-2 approximation for Max-Cut, a highly challenging open problem. In this paper, we take a significant step in this direction, showing that even o(log n)-pass streaming algorithms need nΩ(1) space to solve the cycle-finding problem. Our proof is quite involved, dividing the cycles in the graph into \"short\" and \"long\" cycles, and using tailor-made lower bound techniques to handle each case.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH35"}, {"primary_key": "1258830", "vector": [], "sparse_vector": [], "title": "Parameterized Approximation Scheme for Biclique-free Max k-Weight SAT and Max Coverage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "MAX-SAT with cardinality constraint (CC-MAX-SAT) is one of the classical NP-complete problems, that generalizes MAXIMUM COVERAGE, PARTIAL VERTEX COYER, MAX-2-SAT with bisection constraints, and has been extensively studied across all algorithmic paradigms. In this problem, we are given a CNF-formula Φ, and a positive integer k, and the goal is to find an assignment β with at most k variables set to true (also called a weight k-assignment) such that the number of clauses satisfied by β is maximized. The problem is known to admit an approximation algorithm with factor , which is probably optimal. In fact, the problem is hard to approximate within 0.944, assuming Unique Games Conjecture, even when the input formula is 2-CNF. Furthermore, assuming Gap-Exponential Time Hypothesis (Gap-ETH), for any ε > 0 and any function h, no h(k)(n + m)o(k) time algorithm can approximate MAXIMUM COVERAGE (a monotone version of CC-MAX-SAT) with n elements and m sets to within a factor , even with a promise that there exist k sets that fully cover the whole universe.These intractable results lead us to explore families of formula, where we can circumvent these barriers. Towards this we consider Kd,d-free formulas (that is, the clause-variable incidence bipartite graph of the formula excludes Kd,d as an induced subgraph). We show that for every ε > 0, there exists an algorithm for CC-MAX-SAT on Kd,d-free formulas with approximation ratio (1 — ε) and running in time (these algorithms are called FPT-AS). For, MAXIMUM COVERAGE on Kd,d-free set families, we obtain FPT-AS with running time .Our second result considers “optimizing k”, with fixed covering constraint for the Maximum Coverage problem. To explain our result, we first recast the MAXIMUM COVERAGE problem as the MAX RED BLUE DOMINATING SET WITH COVERING CONSTRAINT problem. Here, input is a bipartite graph G = (A, B, E), a positive integer t, and the objective is to find a minimum sized subset S ⊆ A, such that |N(S)| (the size of the set of neighbors of S) is at least t. We design an additive approximation algorithm for MAX RED BLUE DOMINATING SET WITH COVERING CONSTRAINT, on Kd,d-free bipartite graphs, running in FPT time. In particular, if k denotes the minimum size of S ⊆ A, such that |N(S)| ≥ t, then our algorithm runs in time (kd)O(kd)nO(1) and returns a set S' such that |N(S')| ≥ t and |S'| ≤ k +1. This is in sharp contrast to the fact that, even a special case of our problem, namely, the PARTIAL VERTEX COVER problem (or MAX k-VC) is W[1]-hard, parameterized by k. Thus, we get the best possible parameterized approximation algorithm for the MAXIMUM COVERAGE problem on Kd,d-free bipartite graphs.* Pallavi Jain is supported by Seed Grant (IITJ/R&D/2022-23/07) and SERB-SUPRA Grant(SPR/2021/000860). Lawqueen Kanesh is supported by EPSRC Standard Research Grant (EP/V044621/1). Saket Saurabh is supported by the European Research Council (ERC) under the European Union's Horizon 2020 research and innovation programme (grant agreement No. 819416); and he also acknowledges the support of Swarnajayanti Fellowship grant DST/SJF/MSA-01/2017-18.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH143"}, {"primary_key": "1258831", "vector": [], "sparse_vector": [], "title": "Minimizing Completion Times for Stochastic Jobs via Batched Free Times.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the classic problem of minimizing the expected total completion time of jobs on m identical machines in the setting where the sizes of the jobs are stochastic. Specifically, the size of each job is a random variable whose distribution is known to the algorithm, but whose realization is revealed only after the job is scheduled. While minimizing the total completion time is easy in the deterministic setting, the stochastic problem has long been notorious: all known algorithms have approximation ratios that either depend on the variances, or depend linearly on the number of machines.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH73"}, {"primary_key": "1258832", "vector": [], "sparse_vector": [], "title": "The Exact Bipartite Matching Polytope Has Exponential Extension Complexity.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>qiang Yuan"], "summary": "Given a graph with edges colored red or blue and an integer k, the exact perfect matching problem asks if there exists a perfect matching with exactly k red edges. There exists a randomized polylogarithmic-time parallel algorithm to solve this problem, dating back to the eighties, but no deterministic polynomial-time algorithm is known, even for bipartite graphs. In this paper we show that there is no sub-exponential sized linear program that can describe the convex hull of exact matchings in bipartite graphs. In fact, we prove something stronger, that there is no sub-exponential sized linear program to describe the convex hull of perfect matchings with an odd number of red edges.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH61"}, {"primary_key": "1258833", "vector": [], "sparse_vector": [], "title": "Simple, deterministic, fast (but weak) approximations to edit distance and <PERSON><PERSON><PERSON> edit distance.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of obtaining approximation algorithms for standard edit distance and Dyck edit distance that are simple, deterministic and fast, but whose approximation factor may be high. For the standard edit distance of two strings, we introduce a class of simple and fast algorithms called basic single pass algorithms. <PERSON><PERSON> (2014) gave a randomized algorithm in this class that achieves an O(d) approximation on inputs x,y whose edit distance is O(d). In this paper, we (1) present a deterministic algorithm in this class that achieves similar performance and (2) prove that no algorithm (even randomized) in this class can give a better approximation factor. For the Dyck edit distance problem, <PERSON><PERSON> gave a randomized reduction from Dyck edit distance to standard two string edit distance at a cost of a O(log d) factor where d is the Dyck edit distance. We give a deterministic reduction whose description and proof are very simple.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH188"}, {"primary_key": "1258834", "vector": [], "sparse_vector": [], "title": "Sublinear-Time Algorithms for Max Cut, Max E2Lin(q), and Unique Label Cover on Expanders.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We show sublinear-time algorithms for MAX CUT and MAX E2LIN(q) on expanders in the adjacency list model that distinguishes instances with the optimal value more than 1 − ε from those with the optimal value less than 1 − ρ for ρ ≫ ε. The time complexities for MAX CUT and MAX 2LIN(q) are and , respectively, where m is the number of edges in the underlying graph and ϕ is its conductance. Then, we show a sublinear-time algorithm for UNIQUE LABEL COVER on expanders with ϕ ≫ ε in the bounded-degree model. The time complexity of our algorithm is Õd(2qO(1)·ϕ1/q·ε-1/2 · n1/2+qO(q)·ε41.5-q ·ϕ-2), where n is the number of variables. We complement these algorithmic results by showing that testing 3-colorability requires Ω(n) queries even on expanders.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH180"}, {"primary_key": "1258835", "vector": [], "sparse_vector": [], "title": "Approximation Algorithms for Steiner Tree Augmentation Problems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the Steiner Tree Augmentation Problem (STAP), we are given a graph G = (V,E), a set of terminals R ⊆ V, and a Steiner tree T spanning R. The edges L: = E\\E(T) are called links and have non-negative costs. The goal is to augment T by adding a minimum cost set of links, so that there are 2 edge-disjoint paths between each pair of vertices in R. This problem is a special case of the Survivable Network Design Problem, which can be approximated to within a factor of 2 using iterative rounding [13].We give the first polynomial time algorithm for STAP with approximation ratio better than 2. In particular, we achieve an approximation ratio of (1.5 + ε). To do this, we employ the Local Search approach of [24] for the Tree Augmentation Problem and generalize their main decomposition theorem from links (of size two) to hyper-links.We also consider the Node-Weighted Steiner Tree Augmentation Problem (NW-STAP) in which the non-terminal nodes have non-negative costs. We seek a cheapest subset S ⊆ V\\R so that G[R ∪ S] is 2-edge-connected. Using a result of <PERSON><PERSON><PERSON> [18], there exists an O(log |R|)-approximation for this problem. We provide an O(log2(|R|))-approximation algorithm for NW-STAP using a greedy algorithm leveraging the spider decomposition of optimal solutions.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH94"}, {"primary_key": "1258836", "vector": [], "sparse_vector": [], "title": "Player-optimal Stable Regret for Bandit Learning in Matching Markets.", "authors": ["Fang Kong", "<PERSON><PERSON>"], "summary": "The problem of matching markets has been studied for a long time in the literature due to its wide range of applications. Finding a stable matching is a common equilibrium objective in this problem. Since market participants are usually uncertain of their preferences, a rich line of recent works study the online setting where one-side participants (players) learn their unknown preferences from iterative interactions with the other side (arms). Most previous works in this line are only able to derive theoretical guarantees for player-pessimal stable regret, which is defined compared with the players' least-preferred stable matching. However, under the pessimal stable matching, players only obtain the least reward among all stable matchings. To maximize players' profits, player-optimal stable matching would be the most desirable. Though <PERSON><PERSON> et al. [2021] successfully bring an upper bound for player-optimal stable regret, their result can be exponentially large if players' preference gap is small. Whether a polynomial guarantee for this regret exists is a significant but still open problem. In this work, we provide a new algorithm named explore-then-Gale-<PERSON>y (ETGS) and show that the optimal stable regret of each player can be upper bounded by O(K log T/Δ2) where K is the number of arms, T is the horizon and Δ is the players' minimum preference gap. This result significantly improves previous works which either have a weaker player-pessimal stable matching objective or apply only to markets with special assumptions. When the preferences of participants satisfy some special conditions, our regret upper bound also matches the previously derived lower bound.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH55"}, {"primary_key": "1258837", "vector": [], "sparse_vector": [], "title": "Low Degree Testing over the Reals.", "authors": ["Vipul Arora", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of testing whether a function f : ℝn → ℝ is a polynomial of degree at most d in the distribution-free testing model. Here, the distance between functions is measured with respect to an unknown distribution D over ℝn from which we can draw samples. In contrast to previous work, we do not assume that D has finite support.We design a tester that given query access to f, and sample access to D, makes poly(d/ε) many queries to f, accepts with probability 1 if f is a polynomial of degree d, and rejects with probability at least 2/3 if every degree-d polynomial P disagrees with f on a set of mass at least ε with respect to D. Our result also holds under mild assumptions when we receive only a polynomial number of bits of precision for each query to f, or when f can only be queried on rational points representable using a logarithmic number of bits. Along the way, we prove a new stability theorem for multivariate polynomials that may be of independent interest.* The arXiv version of the paper can be accessed at https://arxiv.org/abs/2204.08404", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH31"}, {"primary_key": "1258838", "vector": [], "sparse_vector": [], "title": "Flow-augmentation III: Complexity dichotomy for Boolean CSPs parameterized by the number of unsatisfied constraints.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the parameterized problem of satisfying \"almost all\" constraints of a given formula F over a fixed, finite Boolean constraint language Γ, with or without weights. More precisely, for each finite Boolean constraint language Γ, we consider the following two problems. In MIN SAT(T), the input is a formula F over Γ and an integer k, and the task is to find an assignment α : V(F) → {0,1} that satisfies all but at most k constraints of F, or determine that no such assignment exists. In WEIGHTED MIN SAT(Γ), the input additionally contains a weight function ω : F → ℤ+ and an integer W, and the task is to find an assignment α such that (1) α satisfies all but at most k constraints of F, and (2) the total weight of the violated constraints is at most W. We give a complete dichotomy for the fixed-parameter tractability of these problems: We show that for every Boolean constraint language Γ, either WEIGHTED MIN SAT(Γ) is FPT; or WEIGHTED MIN SAT(Γ) is W[1]-hard but MIN SAT(Γ) is FPT; or MIN SAT (Γ) is W[1]-hard. This generalizes recent work of <PERSON> et al. (SODA 2021) which did not consider weighted problems, and only considered languages Γ that cannot express implications (u → v) (as is used to, e.g., model digraph cut problems). Our result generalizes and subsumes multiple previous results, including the FPT algorithms for WEIGHTED Almost 2-SAT, weighted and unweighted ℓ-CHAIN SAT, and COUPLED MIN-CUT, as well as weighted and directed versions of the latter. The main tool used in our algorithms is the recently developed method of directed flow-augmentation (Kim et al., STOC 2022).* This research is a part of a project that have received funding from the European Research Council (ERC) under the European Union's Horizon 2020 research and innovation programme Grant Agreement 714704 (M. Pilipczuk). Eun Jung Kim is supported by the grant from French National Research Agency under JCJC program (ASSK: ANR-18-CE40-0025-01). The full version of the paper can be accessed at https://arxiv.org/abs/2207.07422.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH122"}, {"primary_key": "1258839", "vector": [], "sparse_vector": [], "title": "The ℓp-Subspace Sketch Problem in Small Dimensions with Applications to Support Vector Machines.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the ℓp-subspace sketch problem, we are given an n × d matrix A with n > d, and asked to build a small memory data structure Q(A,ε) so that, for any query vector x ∈ ℝd, we can output a number in given only Q(A,ε). This problem is known to require bits of memory for d = Ω(log (1/ε)). However, for d = o(log(l/ε)), no data structure lower bounds were known. Small constant values of d are particularly important for estimating point queries for support vector machines (SVMs) in a stream (<PERSON><PERSON> et al. 2020), where only tight bounds for d = 1 were known.We resolve the memory required to solve the ℓp-subspace sketch problem for any constant d and integer p, showing that it is bits and words, where the Õ(·) notation hides poly(log(1/ε)) factors. This shows that one can beat the Ω(ε-2) lower bound, which holds for d = Ω(log(1/ε)), for any constant d. Further, we show how to implement the upper bound in a single pass stream, with an additional multiplicative poly(log log n) factor and an additive poly(log n) cost in the memory. Our bounds extend to loss functions other than the ℓp-norm, and notably they apply to point queries for SVMs with additive error, where we show an optimal bound of for every constant d. This is a near-quadratic improvement over the lower bound of <PERSON><PERSON> et al. Further, previous upper bounds for SVM point query were noticeably lacking: for d =1 the bound was Õ(e-1/2) and for d = 2 the bound was Õ(ε-4//5), but all existing techniques failed to give any upper bound better than Õ(ε-2) for any other value of d. Our techniques, which rely on a novel connection to low dimensional techniques from geometric functional analysis, completely close this gap.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH34"}, {"primary_key": "1258840", "vector": [], "sparse_vector": [], "title": "Improved Approximation for Two-Edge-Connectivity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The basic goal of survivable network design is to construct low-cost networks which preserve a sufficient level of connectivity despite the failure or removal of a few nodes or edges. One of the most basic problems in this area is the 2-Edge-Connected Spanning Subgraph problem (2-ECSS): given an undirected graph G, find a 2-edge-connected spanning subgraph H of G with the minimum number of edges (in particular, <PERSON> remains connected after the removal of one arbitrary edge).2-ECSS is NP-hard and the best-known (polynomial-time) approximation factor for this problem is 4/3. Interestingly, this factor was achieved with drastically different techniques by [<PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON>, <PERSON>empala and Vetta '00,'19] and [<PERSON><PERSON><PERSON> and Vygen, '14]. In this paper we present an improved approximation for 2-ECSS.The key ingredient in our approach (which might also be helpful in future work) is a reduction to a special type of structured graphs: our reduction preserves approximation factors up to 6/5. While reducing to 2-vertex-connected graphs is trivial (and heavily used in prior work), our structured graphs are \"almost\" 3-vertex-connected: more precisely, given any 2-vertex-cut {u, v} of a structured graph G = (V, E), G[V \\ {u, v}] has exactly 2 connected components, one of which contains exactly one node of degree 2 in G.* Partially supported by the SNSF Excellence Grant 200020B 182865/1 and the SNSF Grant 200021 200731/1.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH92"}, {"primary_key": "1258841", "vector": [], "sparse_vector": [], "title": "Time-Space Tradeoffs for Element Distinctness and Set Intersection via Pseudorandomness.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the ELEMENT DISTINCTNESS problem, one is given an array a1,…, an of integers from [poly(n)] and is tasked to decide if {ai} are mutually distinct. <PERSON><PERSON>, <PERSON> and <PERSON><PERSON> (FOCS 2013) gave a low-space algorithm for this problem that runs in space S(n) and time T(n) where T(n) ≤ Õ(n3/2/S(n)1/2), assuming a random oracle (i.e., random access to polynomially many random bits). A recent breakthrough by <PERSON>, <PERSON>, <PERSON> and <PERSON> (SODA 2022) showed how to remove the random oracle assumption in the regime S(n) = polylog(n) and T(n) = Õ(n3/2). They designed the first truly polylog(n)-space, Õ(n3/2)-time algorithm by constructing a small family of hash functions H ⊆ {h|h : [poly(n)] → [n]} with a certain pseudorandom property.In this paper, we give a significantly simplified analysis of the pseudorandom hash family by <PERSON> et al. Our analysis clearly identifies the key pseudorandom property required to fool the BCM algorithm, allowing us to explore the full potential of this construction. Based on our new analysis, we show the following.• As our main result, we give a time-space tradeoff for ELEMENT DISTINCTNESS without random oracle. Namely, for every S(n),T(n) such that T ≈ Õ(n3/2/S(n)1/2), our algorithm can solve the problem in space S(n) and time T(n). Our algorithm also works for a related problem SET INTERSECTION, for which this tradeoff is tight due to a matching lower bound by Dinur (Eurocrypt 2020).• As a direct application of our technique, we show a more general pseudorandom property of the hash family, which we call the \"c-connecting\" property. It might be of independent interest.• The construction by Chen et al. needs O(log3 n log log n) random bits to sample the pseudorandom hash function. We slightly improve the seed length to O (log3 n).* The full version of the paper can be accessed at https://arxiv.org/abs/2210.07534", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH190"}, {"primary_key": "1258842", "vector": [], "sparse_vector": [], "title": "Nonlinear codes exceeding the Gilbert-<PERSON><PERSON><PERSON><PERSON> and Tsfasman-Vlăduţ-Zink bounds.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Gilbert-<PERSON><PERSON>hamov (GV for short) bound has been a benchmark for good Hamming-metric codes. It was even conjectured by some coding theorists that the asymptotic Gilbert-V<PERSON>hamov bound is tight. The GV bound had remained to be the best asymptotic lower bound for thirty years before it was broken by the Tsfasman-<PERSON><PERSON><PERSON>-<PERSON> bound via algebraic geometry codes. The discovery of algebraic geometry codes by <PERSON><PERSON> was a breakthrough in coding theory. After another twenty years, no any improvements on the Tsfasman-Vlăduţ-Zink bound took place before the work by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [14, 15, 1, 2] in the early of 2000 via tools from algebraic geometry. By using the similar ideas as in [14, 15, 9], some further improvements were given in [7, 17]. Since then, no further progress on asymptotic lower bounds has been made. The main result of this paper is to show that all previous asymptotic lower bounds can be improved in an interval.We present two types of constructions of Hamming-metric codes. Both constructions involve algebraic geometry and need insights on applications of algebraic geometry to coding theory. In order to obtain good codes, one construction requires a larger number of positive divisors of fixed degree, while other construction requires a smaller number of positive divisors of fixed degree. As a result, no matter how large the number of positive divisors of fixed degree is, we can always obtain codes with good parameters. It turns out that all previous asymptotic lower bounds are improved.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH157"}, {"primary_key": "1258843", "vector": [], "sparse_vector": [], "title": "Near-Linear Time Approximations for Cut Problems via Fair Cuts.", "authors": ["<PERSON>", "Danupon <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>"], "summary": "We introduce the notion of fair cuts as an approach to leverage approximate (s, t)-mincut (equivalently (s, t)-maxflow) algorithms in undirected graphs to obtain near-linear time approximation algorithms for several cut problems. Informally, for any α ≥ 1, an α-fair (s, t)-cut is an (s, t)-cut such that there exists an (s, t)-flow that uses 1/α fraction of the capacity of every edge in the cut. (So, any α-fair cut is also an α-approximate mincut, but not vice-versa.) We give an algorithm for (1 + ε)-fair (s, t)-cut in Õ(m)-time, thereby matching the best runtime for (1 + ε)-approximate (s, t)-mincut [Peng, SODA '16]. We then demonstrate the power of this approach by showing that this result almost immediately leads to several applications:• the first nearly-linear time (1 + ε)-approximation algorithm that computes all-pairs maxflow values (by constructing an approximate Gomory-Hu tree). Prior to our work, such a result was not known even for the special case of Steiner mincut [<PERSON> and <PERSON>, STOC '94; <PERSON> and <PERSON>, STOC '03];• the first almost-linear-work subpolynomial-depth parallel algorithms for computing (1+ε)-approximations for all-pairs maxflow values (again via an approximate Gomory-Hu tree) in unweighted graphs;• the first near-linear time expander decomposition algorithm that works even when the expansion parameter is polynomially small; this subsumes previous incomparable algorithms [<PERSON>ongkai and <PERSON>nurak, FOCS '17; Wulff-Nilsen, FOCS '17; <PERSON>nurak and Wang, SODA '19].* The full version of the paper can be accessed at https://arxiv.org/abs/2203.00751", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH10"}, {"primary_key": "1258844", "vector": [], "sparse_vector": [], "title": "Equivalence Test for Read-Once Arithmetic Formulas.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the polynomial equivalence problem for orbits of read-once arithmetic formulas (ROFs). Read- once formulas have received considerable attention in both algebraic and Boolean complexity and have served as a testbed for developing effective tools and techniques for analyzing circuits. Two n-variate polynomials f,g ∈𝔽 [x] are equivalent, denoted as f ~ g, if there is an A ∈ GL(n, 𝔽) such that f = g (Ax). The orbit of f is the set of all polynomials equivalent to f. We investigate the complexity of the following two natural problems on ROFs:• Equivalence test for ROFs: Given black-box access to f, check if it is in the orbit of an ROF. If yes, output an ROF C and an A ∈ GL(n, 𝔽) such that f = C (Ax).• Polynomial equivalence for orbits of ROFs: Given black-box access to f and g in the orbits of two unknown ROFs, check if f ~ g. If yes, output an A ∈ GL(n, 𝔽) such that f = g(Ax).These problems are significant generalizations of two well-studied problems in algebraic complexity, namely reconstruction of ROFs and quadratic form equivalence. In this work, we give the first randomized polynomial-time algorithms (with oracle access to quadratic form equivalence) to solve the two problems. The equivalence test works for general ROFs; it also implies an efficient learning algorithm for random arithmetic formulas of unbounded depth and fan-in (in the high number of variables setting). The algorithm for the second problem, which invokes the equivalence test, works for mildly restricted ROFs, namely additive-constant-free ROFs.The equivalence test is based on a novel interplay between the factors and the essential variables of the Hessian determinant of an ROF, the essential variables of the ROF, and certain special structures in the ROF that we call \"skewed paths\". To our knowledge, the Hessian of a general ROF (or even a depth-4 ROF) has not been analyzed before. Analyzing the Hessian and combining the knowledge gained from it with the skewed paths to recursively discover formulas in the orbits of sub-ROFs of lower depth (without incurring an exponential blow-up due to unbounded depth) constitute the main technical contributions of this work.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH162"}, {"primary_key": "1258845", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: Beyond Shearer&apos;s <PERSON><PERSON>.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Sun"], "summary": "In a seminal paper (<PERSON><PERSON> and <PERSON><PERSON><PERSON>, JACM'10), <PERSON><PERSON> and <PERSON><PERSON><PERSON> developed a simple and powerful algorithm to find solutions to constraint satisfaction problems. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, STOC'11) proved that the Moser-Tardos algorithm is efficient up to the tight condition of the abstract Lovász Local Lemma, known as <PERSON><PERSON>'s bound. A fundamental problem around the LLL is whether the efficient region of the Moser-Tardos algorithm can be further extended.In this paper, we give a positive answer to this problem. We show that the efficient region of the Moser-Tardos algorithm indeed goes beyond the Shea<PERSON>'s bound of the underlying dependency graph, if the graph is not chordal. This \"chordal condition\" is sufficient and necessary, since it has been shown that <PERSON><PERSON>'s bound exactly characterizes the efficient region for chordal dependency graph (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, STOC'11; <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, F<PERSON>S'17). Moreover, we demonstrate that the efficient region can exceed <PERSON><PERSON>'s bound by a constant amount by explicitly calculating the gaps on several infinite lattices.The core of our proof is a new criterion on the efficiency of the Moser-Tardos algorithm which takes the intersection between dependent events into consideration. Our criterion is strictly larger than <PERSON><PERSON>'s bound whenever there exist two dependent events with non-empty intersection. Meanwhile, if any two dependent events are mutually exclusive, our criterion becomes the <PERSON><PERSON>'s bound, which is known to be tight in this situation for the Moser-Tardos algorithm (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, ST<PERSON>'11; <PERSON>, <PERSON>rrum and <PERSON>, JACM'19).* The full version of the paper can be accessed at https://arxiv.org/abs/2111.06527", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH129"}, {"primary_key": "1258846", "vector": [], "sparse_vector": [], "title": "Improved Bounds for Sampling Solutions of Random CNF Formulas.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let Φ be a random k-CNF formula on n variables and m clauses, where each clause is a disjunction of k literals chosen independently and uniformly. Our goal is, for most Φ, to (approximately) uniformly sample from its solution space.Let α = m/n be the density. The previous best algorithm runs in time npoly(k,α) for any α ≲ 2k/300 [<PERSON>, <PERSON>, <PERSON>, and <PERSON>, SIAM J<PERSON>.'21]. In contrast, our algorithm runs in almost-linear time for any α ≲ 2k/3.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH128"}, {"primary_key": "1258847", "vector": [], "sparse_vector": [], "title": "Deterministic counting Lovász local lemma beyond linear programming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>"], "summary": "We give a simple combinatorial algorithm to deterministically approximately count the number of satisfying assignments of general constraint satisfaction problems (CSPs). Suppose that the CSP has domain size q = O(1), each constraint contains at most k = O(1) variables, shares variables with at most Δ = O(1) constraints, and is violated with probability at most p by a uniform random assignment. The algorithm returns in polynomial time in an improved local lemma regime:q2 · κ · p · Δ 5 ≤ C0 for a suitably small absolute constant C0.Here the key term Δ5 improves the previously best known Δ7 for general CSPs [21] and Δ5.714 for the special case of k-CNF [20, 16].Our deterministic counting algorithm is a derandomization of the very recent fast sampling algorithm in [17]. It departs substantially from all previous deterministic counting Lovasz local lemma algorithms which relied on linear programming, and gives a deterministic approximate counting algorithm that straightforwardly derandomizes a fast sampling algorithm, hence unifying the fast sampling and deterministic approximate counting in the same algorithmic framework.To obtain the improved regime, in our analysis we develop a refinement of the {2, 3}-trees that were used in the previous analyses of counting/sampling LLL. Similar techniques can be applied to the previous LP-based algorithms to obtain the same improved regime and may be of independent interests.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH130"}, {"primary_key": "1258848", "vector": [], "sparse_vector": [], "title": "Query Complexity of the Metric Steiner Tree Problem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the metric Steiner Tree problem, we are given an n × n metric w on a set V of vertices along with a set T ⊆ V of k terminals, and the goal is to find a tree of minimum cost that contains all terminals in T. This is a well-known NP-hard problem and much of the previous work has focused on understanding its polynomial-time approximability. In this work, we initiate a study of the query complexity of the metric Steiner Tree problem. Specifically, if we desire an α-approximate estimate of the metric Steiner Tree cost, how many entries need to be queried in the metric w? For the related minimum spanning tree (MST) problem, this question is well-understood. For any fixed ε > 0, one can estimate the MST cost to within a (1 + ε)-factor using only Õ(n) queries, and this is known to be essentially tight. Can one obtain a similar result for Steiner Tree cost? Note that a (2 + ε)-approximate estimate of Steiner Tree cost can be obtained with Õ(k) queries by simply applying the MST cost estimation algorithm on the metric induced by the terminals.Our first result shows that the Steiner Tree problem behaves in a fundamentally different manner from MST: any (randomized) algorithm that estimates the Steiner Tree cost to within a (5/3 — ε)-factor requires Ω(n2) queries, even if k is a constant. This lower bound is in sharp contrast to an upper bound of O(nk) queries for computing a (5/3)-approximate Steiner Tree, which follows from previous work by <PERSON> and <PERSON>.Our second main result, and the main technical contribution of this work, is a sublinear query algorithm for estimating the Steiner Tree cost to within a strictly better-than-2 factor. We give an algorithm that achieves this goal, with a query complexity of Õ(n12/7 + n6/7 · k); since k ≤ n, the algorithm performs at most O(n13/7) = o(n2) queries in the worst-case. Our estimation algorithm reduces this task to that of designing a sublinear query algorithm for a suitable set cover problem. We complement this result by showing an query lower bound for any algorithm that estimates Steiner Tree cost to a strictly better than 2 factor. Thus queries are needed to just beat 2-approximation when k = Ω(n); a sharp contrast to MST cost estimation where a (1 + o(1))-approximate estimate of cost is achievable with only Õ(n) queries.* The full version of the paper can be accessed at https://arxiv.org/abs/2211.03893", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH179"}, {"primary_key": "1258849", "vector": [], "sparse_vector": [], "title": "Online Sorting and Translational Packing of Convex Polygons.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We investigate several online packing problems in which convex polygons arrive one by one and have to be placed irrevocably into a container, while the aim is to minimize the used space. Among other variants, we consider strip packing and bin packing, where the container is the infinite horizontal strip [0, ∞) × [0,1] or a collection of 1 × 1 bins, respectively. If polygons may be rotated, there exist O(1)-competitive online algorithms for all problems at hand [<PERSON> and <PERSON>, SIAM J. Comp<PERSON>., 1983]. Likewise, if the polygons may not be rotated but only translated, then using a result from [<PERSON>, <PERSON> and <PERSON>, JoCG, 2017] we can derive O(1)-approximation algorithms for all problems at hand. Thus, it is natural to conjecture that the online version of these problems, in which only translations are allowed, also admits a O(1)-competitive algorithm. We disprove this conjecture by showing a superconstant lower bound on the competitive ratio for several online packing problems. The offline approximation algorithm for translation-only packing sorts the convex polygons by their “natural slope”, so that they form a fan-like pattern. We prove that this step is essential, in the sense that packing polygons without rotating them is as hard as sorting numbers online. Technically, we prove lower bounds on the competitive ratio of translation-only online packing problems by reducing from a purpose-built novel and natural combinatorial problem that we call online sorting. In a nutshell, the problem requires us to place n numbers x1,…, xn coming online into an oversized array of length γn for γ ≥ 1, while minimizing the sum of absolute differences of consecutive numbers. Note that the offline optimum is achieved by sorting x1,…, xn. We show a superconstant lower bound on the competitive ratio of online sorting, for any constant γ. We prove that this yields superconstant lower bounds for all packing problems at hand. We believe that this technique is of independent interest since it uncovers a deep connection between inherently geometrical and purely combinatorial problems. As a complement, we also include algorithms for both online sorting and translation-only online strip packing with non-trivial competitive ratios. Our algorithm for strip packing relies on a new technique for recursively subdividing the strip into parallelograms of varying height, thickness and slope.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH69"}, {"primary_key": "1258850", "vector": [], "sparse_vector": [], "title": "Computing Square Colorings on Bounded-Treewidth and Planar Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A square coloring of a graph G is a coloring of the square G2 of G, that is, a coloring of the vertices of G such that any two vertices that are at distance at most 2 in G receive different colors. We investigate the complexity of finding a square coloring with a given number of q colors. We show that the problem is polynomial-time solvable on graphs of bounded treewidth by presenting an algorithm with running time for graphs of treewidth at most tw. The somewhat unusual exponent 2tw in the running time is essentially optimal: we show that for any ε > 0, there is no algorithm with running time f (tw)n(2-ε)tw unless the Exponential-Time Hypothesis (ETH) fails.We also show that the square coloring problem is NP-hard on planar graphs for any fixed number q ≥ 4 of colors. Our main algorithmic result is showing that the problem (when the number of colors q is part of the input) can be solved in subexponential time on planar graphs. The result follows from the combination of two algorithms. If the number q of colors is small (≤ n1/3), then we can exploit a treewidth bound on the square of the graph to solve the problem in time . If the number of colors is large (≥ n1/3), then an algorithm based on protrusion decompositions and building on our result for the bounded- treewidth case solves the problem in time .* The full version of the paper can be accessed at https://arxiv.org/abs/2211.04458. Research supported by the European Research Council (ERC) consolidator grant No. 725978 SYSTEMATICGRAPH.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH80"}, {"primary_key": "1258851", "vector": [], "sparse_vector": [], "title": "Smaller Low-Depth Circuits for Kronecker Powers.", "authors": ["<PERSON>", "Yunfeng Guan", "<PERSON><PERSON>"], "summary": "A linear circuit for computing an N × N matrix M is a circuit with N inputs corresponding to the entries of a vector x and N outputs corresponding to the entries of the transformed vector Mx, and where each gate computes a linear combination of its inputs. Each gate may have unbounded fan-in, and the size of the circuit is the number of wires. This model captures most known algorithms for computing linear transforms, and (in the constant-depth or 'synchronous' settings) is equivalent to factoring M as the product of sparse matrices.We give new, smaller constructions of constant-depth linear circuits for computing any matrix which is the Kronecker power of a fixed matrix. A standard argument (e.g., the mixed product property of Kronecker products, or a generalization of the Fast <PERSON>-<PERSON> transform) shows that any such N × N matrix has a depth-2 circuit of size O(N1.5). We improve on this for all such matrices, and especially for some such matrices of particular interest:• For any integer q > 1 and any matrix which is the Kronecker power of a fixed q × q matrix, we construct a depth-2 circuit of size O(N1.5-aq), where aq > 0 is a positive constant depending only on q. No bound beating size O(N1.5) was previously known for any q > 2.• For the case q = 2, i.e., for any matrix which is the Kronecker power of a fixed 2 × 2 matrix, we construct a depth-2 circuit of size O(N1.446), improving the prior best size O(N1.493) [Alman, 2021].• For the <PERSON><PERSON> transform, we construct a depth-2 circuit of size O(N1.443), improving the prior best size O(N1.476) [Alman, 2021].• For the disjointness matrix (the communication matrix of set disjointness, or equivalently, the matrix for the linear transform that evaluates a multilinear polynomial on all 0/1 inputs), we construct a depth-2 circuit of size O(N1.258), improving the prior best size O(N1.272) [Jukna and Sergeev, 2013].Our constructions also generalize to improving the standard construction for any depth ≤ O (log N). Our main technical tool is an improved way to convert a nontrivial circuit for any matrix into a circuit for its Kronecker powers. Our new bounds provably could not be achieved using the approaches of prior work.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH160"}, {"primary_key": "1258852", "vector": [], "sparse_vector": [], "title": "The Need for Seed (in the abstract Tile Assembly Model).", "authors": ["<PERSON>", "<PERSON>"], "summary": "In the abstract Tile Assembly Model (aTAM) square tiles self-assemble, autonomously binding via glues on their edges, to form structures. Algorithmic aTAM systems can be designed in which the patterns of tile attachments are forced to follow the execution of targeted algorithms. Such systems have been proven to be computationally universal as well as intrinsically universal (IU), a notion borrowed and adapted from cellular automata showing that a single tile set exists which is capable of simulating all aTAM systems (FOCS 2012). The input to an algorithmic aTAM system can be provided in a variety of ways, with a common method being via the \"seed\" assembly, which is a pre-formed assembly from which all growth propagates. Arbitrary amounts of information can be encoded into seed assemblies by both (1) the types and patterns of glues exposed on their exteriors, and (2) their shapes. Since a common metric by which aTAM systems are measured is their tile complexity (i.e. the number of unique types of tiles they utilize), in order to provide a fair basis for comparison, systems are often designed with seed assemblies consisting of only a single seed tile, a.k.a. single-tile seeds. (For instance, in STOC 2000 and 2001 information theoretically optimal tile complexity was shown possible for the self-assembly of squares.) This requires the transferring of any information that may be encoded in a multi-tile seed assembly into tile complexity. In this paper, we explore this process to show when and how such transformations are possible while ensuring that a derived system with a single-tile seed faithfully replicates the behaviors of the original system.We first show that a trivial transformation, in which the locations of a multi-tile seed are tiled by \"hard- coded\" tiles that can grow to represent that seed from a single tile, can succeed only if (1) there are not tile locations in the seed such that there exist growth sequences where those locations could block future growth, or (2) an ordering of growth can be enforced for the growth of the seed from a single tile to ensure that such blocking locations are tiled before collisions are possible. However, we show that knowing if this is the case is uncomputable. Therefore, we examine what is possible if the scale factor of the original system is increased and show that all systems with multi-tile seeds can be transformed into systems with single-tile seeds at scale factor 3 (i.e. each tile of the original system is replaced by a 3 × 3 square of tiles), such that the transformed systems faithfully replicate the dynamics of the original systems. We also prove that this scale factor is optimal, and that in fact there exist systems with multi-tile seeds for which no systems at scale factors 1 or 2 (or scale factor 3 when a more restrictive form of simulation is required) with single-tile seeds exist that can even produce the same sets of terminal output shapes. Since the scale 3 transformation results in a tile complexity which is proportional to the size of the original tile set plus the size of the multi-tile seed multiplied by the scale factor, we then also provide a transformation that yields an asymptotically optimal tile complexity proportional to the Kolmogorov complexity of the original system and which is based on the IU construction from FOCS 2012. Additionally, we are able to make simple modifications to that construction to provide a single aTAM system which simultaneously and in parallel simulates all aTAM systems, and provide a connection between that system and the existence of systems within models other than the aTAM which are IU for the aTAM. This set of results provides a full characterization of the tradeoff's between systems with multi-tile seeds and those with single-tile seeds, which is fundamental to the measure of complexity of aTAM systems.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH172"}, {"primary_key": "1258853", "vector": [], "sparse_vector": [], "title": "Fast algorithms for solving the Hamilton Cycle problem with high probability.", "authors": ["<PERSON>"], "summary": "We study the Hamilton cycle problem with input a random graph G ~ G(n,p) in two different settings. In the first one, G is given to us in the form of randomly ordered adjacency lists while in the second one, we are given the adjacency matrix of G. In each of the two settings we derive a deterministic algorithm that w.h.p. either finds a Hamilton cycle or returns a certificate that such a cycle does not exist for p = p(n) ≥ 0. The running times of our algorithms are O(n) and respectively, each being best possible in its own setting.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH88"}, {"primary_key": "1258854", "vector": [], "sparse_vector": [], "title": "Quantum tomography using state-preparation unitaries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We describe algorithms to obtain an approximate classical description of a d-dimensional quantum state when given access to a unitary (and its inverse) that prepares it. For pure states we characterize the query complexity for ℓq-norm error up to logarithmic factors. As a special case, we show that it takes applications of the unitaries to obtain an ε-ℓ2-approximation of the state.For mixed states we consider a similar model, where the unitary prepares a purification of the state. We characterize the query complexity for obtaining Schatten q-norm estimates of a rank-r mixed state, up to polylogarithmic factors. In particular, we show that a trace-norm (q = 1) estimate can be obtained with queries. This improves (assuming our stronger input model) the ε-dependence over the works of <PERSON><PERSON><PERSON> and <PERSON> (STOC 2016) and <PERSON><PERSON> et al. (IEEE Trans. Inf. Theory, 63.9, 2017), that use a joint measurement on copies of the state.To our knowledge, the most sample-efficient results for pure-state tomography come from setting the rank to 1 in generic mixed-state tomography algorithms, which can require a large amount of computing resources. We describe sample-optimal algorithms for pure states that are simple and fast to implement.Along the way we show that an ℓ∞-norm estimate of a normalized vector induces a (slightly worse) ℓq-norm estimate for that vector, without losing a dimension-dependent factor in the precision. We also develop an unbiased and symmetric version of phase estimation, where the probability distribution of the estimate is centered around the true value. Finally, we give an efficient method for estimating multiple expectation values, improving over the recent result by <PERSON> et al. (arXiv:2111.09283) when the measurement operators do not fully overlap. More specifically, we show that for E1,…, Em normalized measurement operators, all expectation values Tr(Ejρ) can be efficiently learned up to error ε with applications of a state-preparation unitary for a purification of ρ.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.08800", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH47"}, {"primary_key": "1258855", "vector": [], "sparse_vector": [], "title": "Economical Convex Coverings and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Fonseca", "<PERSON>"], "summary": "Coverings of convex bodies have emerged as a central component in the design of efficient solutions to approximation problems involving convex bodies. Intuitively, given a convex body K and ε > 0, a covering is a collection of convex bodies whose union covers K such that a constant factor expansion of each body lies within an ε expansion of K. Coverings have been employed in many applications, such as approximations for diameter, width, and ε-kernels of point sets, approximate nearest neighbor searching, polytope approximations with low combinatorial complexity, and approximations to the Closest Vector Problem (CVP).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH70"}, {"primary_key": "1258856", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Monotone Minimal Perfect Hashing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>"], "summary": "The monotone minimal perfect hash function (MMPHF) problem is the following indexing problem. Given a set S = {sı,…, sn} of n distinct keys from a universe U of size u, create a data structure D that answers the following query:", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH20"}, {"primary_key": "1258857", "vector": [], "sparse_vector": [], "title": "Distributed Maximal Matching and Maximal Independent Set on Hypergraphs.", "authors": ["Alki<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We investigate the distributed complexity of maximal matching and maximal independent set (MIS) in hypergraphs in the LOCAL model. A maximal matching of a hypergraph H = (VH, EH) is a maximal disjoint set M ⊆ Eh of hyperedges and an MIS S ⊆ VH is a maximal set of nodes such that no hyperedge is fully contained in S. Both problems can be solved by a simple sequential greedy algorithm, which can be implemented naïvely in O (Δr + log* n) rounds, where Δ is the maximum degree, r is the rank, and n is the number of nodes of the hypergraph.We show that for maximal matching, this naive algorithm is optimal in the following sense. Any deterministic algorithm for solving the problem requires Ω(min {Δr,logΔr n}) rounds, and any randomized one requires Ω(min {Δr, logΔr log n}) rounds. Hence, for any algorithm with a complexity of the form O(f (Δ,r) + g(n)), we have f (Δ,r) ∈ Ω(Δr) if g(n) is not too large, and in particular if g(n) = log* n (which is the optimal asymptotic dependency on n due to <PERSON><PERSON>'s lower bound [FOCS'87]). Our lower bound proof is based on the round elimination framework, and its structure is inspired by a new round elimination fixed point that we give for the Δ-vertex coloring problem in hypergraphs, where nodes need to be colored such that there are no monochromatic hyperedges.For the MIS problem on hypergraphs, we show that for Δ ≪ r, there are significant improvements over the naive O(Δr + log* n)-round algorithm. We give two deterministic algorithms for the problem. We show that a hypergraph MIS can be computed in O(Δ2 · log r + Δ · log r · log* r + log* n) rounds. We further show that at the cost of a much worse dependency on Δ, the dependency on r can be removed almost entirely, by giving an algorithm with round complexity ΔO(Δ) · log* r + 0(log* n).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH100"}, {"primary_key": "1258858", "vector": [], "sparse_vector": [], "title": "Optimal Deterministic Massively Parallel Connectivity on Forests.", "authors": ["Alki<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We show fast deterministic algorithms for fundamental problems on forests in the challenging low-space regime of the well-known Massive Parallel Computation (MPC) model. A recent breakthrough result by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [STOC'22] shows that, in this setting, it is possible to deterministically identify connected components on graphs in O (log D + log log n) rounds, where D is the diameter of the graph and n the number of nodes. The authors left open a major question: is it possible to get rid of the additive log log n factor and deterministically identify connected components in a runtime that is completely independent of n?", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH99"}, {"primary_key": "1258859", "vector": [], "sparse_vector": [], "title": "Optimal Fully Dynamic k-Center Clustering for Adaptive and Oblivious Adversaries.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In fully dynamic clustering problems, a clustering of a given data set in a metric space must be maintained while it is modified through insertions and deletions of individual points. In this paper, we resolve the complexity of fully dynamic k-center clustering against both adaptive and oblivious adversaries. Against oblivious adversaries, we present the first algorithm for fully dynamic k-center in an arbitrary metric space that maintains an optimal (2 + ε)-approximation in O(k · polylog(n, Δ)) amortized update time. Here, n is an upper bound on the number of active points at any time, and Δ is the aspect ratio of the metric space. Previously, the best known amortized update time was O(k2 · polylog(n, Δ)), and is due to <PERSON>, <PERSON>, and <PERSON> (2018). Moreover, we demonstrate that our runtime is optimal up to polylog(n, Δ) factors. In fact, we prove that even offline algorithms for k-clustering tasks in arbitrary metric spaces, including k-medians, k-means, and k-center, must make at least Ω(nk) distance queries to achieve any non-trivial approximation factor. This implies a lower bound of Ω(k) which holds even for the insertions-only setting.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH101"}, {"primary_key": "1258860", "vector": [], "sparse_vector": [], "title": "Sampling Equilibria: Fast No-Regret Learning in Structured Games.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Learning and equilibrium computation in games are fundamental problems across computer science and economics, with applications ranging from politics to machine learning. Much of the work in this area revolves around a simple algorithm termed randomized weighted majority (RWM), also known as \"Hedge\" or \"Multiplicative Weights Update,\" which is well known to achieve statistically optimal rates in adversarial settings (Littlestone and Warmuth '94, Freund and Schapire '99). Unfortunately, RWM comes with an inherent computational barrier: it requires maintaining and sampling from a distribution over all possible actions. In typical settings of interest the action space is exponentially large, seemingly rendering RWM useless in practice.In this work, we refute this notion for a broad variety of structured games, showing it is possible to efficiently (approximately) sample the action space in RWM in polylogarithmic time. This gives the first efficient no-regret algorithms for problems such as the (discrete) Colonel Blotto game, matroid congestion, matroid security, and basic dueling games. As an immediate corollary, we give a polylogarithmic time meta-algorithm to compute approximate Nash Equilibria for these games that is exponentially faster than prior methods in several important settings. Further, our algorithm is the first to efficiently compute equilibria for more involved variants of these games with general sums, more than two players, and, for <PERSON>, multiple resource types. Our results also greatly generalize earlier work on efficient RWM-based techniques for exponential strategy sets from (Cesa-Bianchi and Lugosi '09).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH149"}, {"primary_key": "1258861", "vector": [], "sparse_vector": [], "title": "Dynamic Algorithms for Maximum Matching Size.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We study fully dynamic algorithms for maximum matching. This is a well-studied problem, known to admit several update-time/approximation trade-offs. For instance, it is known how to maintain a 1/2-approximate matching in (poly log n) update time or a 2/3-approximate match­ing in update time, where n is the number of vertices. It has been a long-standing open problem to determine whether either of these bounds can be improved.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH6"}, {"primary_key": "1258862", "vector": [], "sparse_vector": [], "title": "Single-Pass Streaming Algorithms for Correlation Clustering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study correlation clustering in the streaming setting. This problem has been studied extensively and numerous algorithms have been developed, most requiring multiple passes over the stream. For the important case of single-pass algorithms, recent work of <PERSON><PERSON><PERSON> and <PERSON> [8] obtains a c-approximation using Õ(n) space where c > 105 is a constant and n is the number of vertices to be clustered.We present a single-pass algorithm that obtains a 5-approximation using O(n) space. The algorithm itself is extremely simple and has implications beyond the streaming setting (such as for dynamic and local computation algorithms). The approximation analysis, on the other hand, is delicate and in fact tight.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH33"}, {"primary_key": "1258863", "vector": [], "sparse_vector": [], "title": "Beating Greedy Matching in Sublinear Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>via<PERSON>", "<PERSON><PERSON>"], "summary": "We study sublinear time algorithms for estimating the size of maximum matching in graphs. Our main result is a (½ + Ω(1))-approximation algorithm which can be implemented in O(n1+ε) time, where n is the number of vertices and the constant ε > 0 can be made arbitrarily small. The best known lower bound for the problem is Ω(n), which holds for any constant approximation.Existing algorithms either obtain the greedy bound of ½-approximation [<PERSON><PERSON><PERSON><PERSON> FOCS'21], or require some assumption on the maximum degree to run in o(n2)-time [<PERSON><PERSON><PERSON>, <PERSON>, and Ito STOC'09]. We improve over these by designing a less \"adaptive\" augmentation algorithm for maximum matching that might be of independent interest.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH151"}, {"primary_key": "1258864", "vector": [], "sparse_vector": [], "title": "Bidder Subset Selection Problem in Auction Design.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by practical concerns in the online advertising industry, we study a bidder subset selection problem in single-item auctions. In this problem, a large pool of candidate bidders have independent values sampled from known prior distributions. The seller needs to pick a subset of bidders and run a given auction format on the selected subset to maximize her expected revenue. We propose two frameworks for the subset restrictions: (i) capacity constraint on the set of selected bidders; and (ii) incurred costs for the bidders invited to the auction. For the second-price auction with anonymous reserve (SPA-AR), we give constant approximation polynomial time algorithms in both frameworks (in the latter framework under mild assumptions about the market). Our results are in stark contrast to the previous work of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> [NeurIPS 2020], who showed hardness of approximation for the SPA without a reserve price. We also give complimentary approximation results for other well-studied auction formats such as anonymous posted pricing and sequential posted pricing. On a technical level, we find that the revenue of SPA-AR as a set function f(S) of its bidders S is fractionally-subadditive but not submodular. Our bidder selection problem with invitation costs is a natural question about (approximately) answering a demand oracle for f(·) under a given vector of costs, a common computational assumption in the literature on combinatorial auctions.* This work is supported by Science and Technology Innovation 2030 –\"New Generation of Artificial Intelligence\" Major Project No.(2018AAA0100903), Innovation Program of Shanghai Municipal Education Commission, Program for Innovative Research Team of Shanghai University of Finance and Economics (IRTSHUFE) and the Fundamental Research Funds for the Central Universities. Zhihao Gavin Tang is supported by NSFC grant 61902233. Nick Gravin is supported by NSFC grant 62150610500.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH147"}, {"primary_key": "1258865", "vector": [], "sparse_vector": [], "title": "Polynomial formulations as a barrier for reduction-based hardness proofs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Strong Exponential Time Hypothesis (SETH) asserts that for every ε > 0 there exists k such that k-SAT requires time (2 — ε)n. The field of fine-grained complexity has leveraged SETH to prove quite tight conditional lower bounds for dozens of problems in various domains and complexity classes, including Edit Distance, Graph Diameter, Hitting Set, Independent Set, and Orthogonal Vectors. Yet, it has been repeatedly asked in the literature whether SETH-hardness results can be proven for other fundamental problems such as Hamiltonian Path, Independent Set, Chromatic Number, MAX-k-SAT, and Set Cover.In this paper, we show that fine-grained reductions implying even λn-hardness of these problems from SETH for any λ > 1, would imply new circuit lower bounds: super-linear lower bounds for Boolean series-parallel circuits or polynomial lower bounds for arithmetic circuits (each of which is a four-decade open question).We also extend this barrier result to the class of parameterized problems. Namely, for every λ > 1, we conditionally rule out fine-grained reductions implying SETH-based lower bounds of λk: for a number of problems parameterized by the solution size k.Our main technical tool is a new concept called polynomial formulations. In particular, we show that many problems can be represented by relatively succinct low-degree polynomials, and that any problem with such a representation cannot be proven SETH-hard (without proving new circuit lower bounds).* The full version of the paper can be accessed at https://arxiv.org/abs/2205.07709", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH124"}, {"primary_key": "1258867", "vector": [], "sparse_vector": [], "title": "On complex roots of the independence polynomial.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The independence polynomial of a graph is the generating polynomial of all its independent sets. Formally, given a graph G, its independence polynomial ZG (λ) is given by ΣIλ|I|, where the sum is over all independent sets I of G. The independence polynomial has been an important object of study in both combinatorics and computer science. In particular, the algorithmic problem of estimating ZG(λ) for a fixed positive λ on an input graph G is a natural generalization of the problem of counting independent sets, and its study has led to some of the most striking connections between computational complexity and the theory of phase transitions. More surprisingly, the independence polynomial for negative and complex values of λ also turns out to be related to problems in statistical physics and combinatorics. In particular, the locations of the complex roots of the independence polynomial of bounded degree graphs turn out to be very closely related to the Lovász local lemma, and also to the questions in the computational complexity of counting. Consequently, the locations of such zeros have been studied in many works. In this direction, it is known from the work of <PERSON><PERSON> [29] and of <PERSON> and <PERSON> [27] - inspired by the study of the Lovász local lemma - that the independence polynomial ZG (λ) of a graph G of maximum degree at most d + 1 does not vanish provided that . Significant extensions of this result have recently been given in the case when λ is in the right half-plane (i.e., when ℜλ ≥ 0) by <PERSON> and <PERSON> [26] and <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> [9]. In this paper, our motivation is to further extend these results to find new zero free regions not only in the right half plane, but also in the left half-plane, that is, when ℜλ ≤ 0.We give new geometric criterions for establishing zero-free regions as well as for carrying out semi-rigorous numerical explorations. We then provide two examples of the (rigorous) use of these criterions, by establishing two new zero-free regions in the left-half plane. We also extend the results of Bencs and Csikvári [9] for the right half-plane using our framework. By a direct application of the interpolation method of Barvinok [5], combined with extensions due to Patel and Regts [25], our results also imply deterministic polynomial time approximation algorithms for the independence polynomial of bounded degree graphs in the new zero-free regions.* The arXiv version of the paper can be accessed at https://arxiv.org/abs/2204.04868.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH29"}, {"primary_key": "1258868", "vector": [], "sparse_vector": [], "title": "<PERSON>.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper introduces a new data-structural object that we call the tiny pointer. In many applications, traditional log n-bit pointers can be replaced with o(log n)-bit tiny pointers at the cost of only a constant-factor time overhead and a small probability of failure. We develop a comprehensive theory of tiny pointers, and give optimal constructions for both fixed-size tiny pointers (i.e., settings in which all of the tiny pointers must be the same size) and variable-size tiny pointers (i.e., settings in which the average tiny-pointer size must be small, but some tiny pointers can be larger). If a tiny pointer references an element in an array filled to load factor 1 — δ, then the optimal tiny-pointer size is Θ(log log log n + log δ-1) bits in the fixed-size case, and Θ(log δ-1) expected bits in the variable-size case.Our tiny-pointer constructions also require us to revisit several classic problems having to do with balls and bins; these results may be of independent interest.Using tiny pointers, we revisit five classic data-structure problems. We show that:• A data structure storing n v-bit values for n keys with constant-time modifications/queries can be implemented to take space nv + O(n log(r) n) bits, for any constant r > 0, as long as the user stores a tiny pointer of expected size O(1) with each key—here, log(r) n is the r-th iterated logarithm.• Any binary search tree can be made succinct with constant-factor time overhead, and can even be made to be within O(n) bits of optimal if we allow for O(log* n)-time modifications—this holds even for rotation-based trees such as the splay tree and the red-black tree.• Any fixed-capacity key-value dictionary can be made stable (i.e., items do not move once inserted) with constant-time overhead and 1 + o(1) space overhead.• Any key-value dictionary that requires uniform-size values can be made to support arbitrary-size values with constant-time overhead and with an additional space consumption of log(r) n + O(log j) bits per j-bit value for an arbitrary constant r > 0 of our choice.• Given an external-memory array A of size (1 + ε)n containing a dynamic set of up to n key-value pairs, it is possible to maintain an internal-memory stash of size O(n log ε-1) bits so that the location of any key-value pair in A can be computed in constant time (and with no IOs).These are all well studied and classic problems, and in each case tiny pointers allow for us to take a natural space-inefficient solution that uses pointers and make it space-efficient for free.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH21"}, {"primary_key": "1258869", "vector": [], "sparse_vector": [], "title": "A logic-based algorithmic meta-theorem for mim-width.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a logic called distance neighborhood logic with acyclicity and connectivity constraints (A&C DN for short) which extends existential MSO1 with predicates for querying neighborhoods of vertex sets in various powers of a graph and for verifying connectivity and acyclicity of vertex sets. Building upon [<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, ESA 2019; SIDMA 2021], we show that the model checking problem for every fixed A&C DN formula is solvable in nO(w) time when the input graph is given together with a branch decomposition of mim-width W. Nearly all problems that are known to be solvable in polynomial time given a branch decomposition of constant mim-width can be expressed in this framework. We add several natural problems to this list, including problems asking for diverse sets of solutions.Our model checking algorithm is efficient whenever the given branch decomposition of the input graph has small index in terms of the d-neighborhood equivalence [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, TCS 2013]. We therefore unify and extend known algorithms for tree-width, clique-width and rank-width. Our algorithm has a single-exponential dependence on these three width measures and asymptotically matches run times of the fastest known algorithms for several problems. This results in algorithms with tight run times under the Exponential Time Hypothesis (ETH) for tree-width, clique-width and rank-width; the above mentioned run time for mim-width is nearly tight under the ETH for several problems as well. Our results are also tight in terms of the expressive power of the logic: we show that already slight extensions of our logic make the model checking problem para-NP-hard when parameterized by mim-width plus formula length.* The full version of the paper can be accessed at https://arxiv.org/abs/2202.13335. This research is part of a project that has received funding from the Research Council of Norway Grant Agreement 274526 (LJ).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH125"}, {"primary_key": "1258870", "vector": [], "sparse_vector": [], "title": "Closing the Gap Between Directed Hopsets and Shortcut Sets.", "authors": ["<PERSON>", "<PERSON>"], "summary": "For an n-vertex directed graph G = (V, E), a β-shortcut set H is a set of additional edges H ⊆ V × V such that G U H has the same transitive closure as G, and for every pair u, v ∈ V, there is a uv-path in G U H with at most β edges. A natural generalization of shortcut sets to distances is a (β, ε)-hopset H ⊆ V × V, where the requirement is that <PERSON> and G U H have the same shortest-path distances, and for every u, v ε V, there is a (1 + ε)-approximate shortest path in G U H with at most β edges.There is a large literature on the question of the tradeoff between the optimal size of a shortcut set / hopset and the value of β. In particular we highlight the most natural point on this tradeoff: what is the minimum value of β, such that for any graph G, there exists a β-shortcut set H with O(n) edges? Similarly, what is the minimum value of β such that there exists a (β, ε)-hopset with O(n) edges? Not only is this a very natural structural question in its own right, but shortcuts sets / hopsets form the core of a large number of distributed, parallel, and dynamic algorithms for reachability / shortest paths.A lower bound of Hesse [SODA 2003] for directed graphs shows that if we restrict ourselves to hopsets with O(n) edges, the best we can guarantee is β = Ω(n1/17) for both shortcut sets and hopsets [SODA 2003]; this was later improved to β = Ω(n1/6) by <PERSON> and <PERSON><PERSON> [SWAT 2018]. Until very recently the best known upper bound was a folklore construction showing β = O(n1/2), but in a breakthrough result Kogan and Parter [SODA 2022] improve this to β = Õ(n1/3) for shortcut sets and Õ(n2/5) for hopsets.Our result in this paper is to close the gap between shortcut sets and hopsets introduced by the result of Kogan and Parter. That is, we show that for any graph G and any fixed ε there is a (Õ(n1/3), ε) hopset with O(n) edges. Our hopset improves upon the (Õ(n2/5), ε) hopset of Kogan and Parter. More generally, we achieve a smooth tradeoff between hopset size and β which exactly matches the tradeoff of Kogan and Parter for the simpler problem of shortcut sets (up to polylog factors).Additionally, using a very recent black-box reduction of Kogan and Parter, our new hopset immediately implies improved bounds for approximate distance preservers.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH7"}, {"primary_key": "1258871", "vector": [], "sparse_vector": [], "title": "Kernelization for Graph Packing Problems via Rainbow Matching.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Dimitrios M. Thilikos", "<PERSON>"], "summary": "We introduce a new kernelization tool, called rainbow matching technique, that is appropriate for the design of polynomial kernels for packing problems. Our technique capitalizes on the powerful combinatorial results of [<PERSON>, <PERSON>, <PERSON>, SODA 2021]. We apply the rainbow matching technique on two (di)graph packing problems, namely the TRIANGLE-PACKING IN TOURNAMENT problem (TPT), where we ask for a packing of k directed triangles in a tournament, and the INDUCED 2-PATH-PACKING (I2PP) where we ask for a packing of k induced paths of length two in a graph. The existence of a sub-quadratic kernels for these problems was proven for the first time in [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Zehavi. ACM Trans. Algorithms, 2019], where they gave a kernel of 𝒪(k3/2) vertices and 𝒪(k5/3) vertices respectively. In the same paper it was questioned whether these bounds can be (optimally) improved to linear ones. Motivated by this question, we apply the rainbow matching technique and prove that TPT admits an (almost linear) kernel of vertices and that I2PP admits a kernel of 𝒪(k) vertices.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH139"}, {"primary_key": "1258872", "vector": [], "sparse_vector": [], "title": "Algorithmizing the Multiplicity Schwartz-<PERSON><PERSON><PERSON> Lemma.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The multiplicity Schwartz-Z<PERSON>pel lemma asserts that over a field, a low-degree polynomial cannot vanish with high multiplicity very often on a sufficiently large product set. Since its discovery in a work of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and Sudan [DKSS13], the lemma has found numerous applications in both math and computer science; in particular, in the definition and properties of multiplicity codes by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> [KSY14].In this work, we show how to algorithmize the multiplicity Schwartz-Zippel lemma for arbitrary product sets over any field. In other words, we give an efficient algorithm for unique decoding of multivariate multiplicity codes from half their minimum distance on arbitrary product sets over all fields. Previously, such an algorithm was known either when the underlying product set had a nice algebraic structure (for instance, was a subfield) [Kop15] or when the underlying field had large (or zero) characteristic, the multiplicity parameter was sufficiently large and the multiplicity code had distance bounded away from 1 [BHKS21b]. In particular, even unique decoding of bivariate multiplicity codes with multiplicity two from half their minimum distance was not known over arbitrary product sets over any field.Our algorithm builds upon a result of <PERSON> <PERSON> [<PERSON><PERSON>17] who gave an algorithmic version of the Schwartz-Zippel lemma (without multiplicities) or equivalently, an efficient algorithm for unique decoding of Reed-Muller codes over arbitrary product sets. We introduce a refined notion of distance based on the multiplicity Schwartz-Zippel lemma and design a unique decoding algorithm for this distance measure. On the way, we give an alternate analysis of Forney's classical generalized minimum distance decoder that might be of independent interest.* The full version of the paper which includes the missing proofs can be accessed at [BHKS21a]. Research of the first, second and fourth authors supported by the Department of Atomic Energy, Government of India, under project 12-R&D-TFR-5.01-0500. This work was done while the first author was at TIFR, where he was supported in part by the Google PhD Fellowship and at the Simons Institute for the Theory of Computing where he was supported by the Simons-Berkeley Postdoctoral Fellowship. Research of the second author supported in part by the Swarnajayanti Fellowship.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH106"}, {"primary_key": "1258873", "vector": [], "sparse_vector": [], "title": "Dynamic Algorithms for Packing-Covering LPs via Multiplicative Weight Updates.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Thatchaphol <PERSON>"], "summary": "In the dynamic linear program (LP) problem, we are given an LP undergoing updates and we need to maintain an approximately optimal solution. Recently, significant attention (e.g. [<PERSON> et al. STOC'17; <PERSON><PERSON> et al. ICALP'18, Wajc STOC'20]) has been devoted to the study of special cases of dynamic packing and covering LPs, such as the dynamic fractional matching and set cover problems. But until now, there is no non-trivial dynamic algorithm for general packing and covering LPs.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH1"}, {"primary_key": "1258874", "vector": [], "sparse_vector": [], "title": "Dynamic Matching with Better-than-2 Approximation in Polylogarithmic Update Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Thatchaphol <PERSON>", "<PERSON>"], "summary": "We present dynamic algorithms with polylogarithmic update time for estimating the size of the maximum matching of a graph undergoing edge insertions and deletions with approximation ratio strictly better than 2. Specifically, we obtain a approximation in bipartite graphs and a 1.973 + ε approximation in general graphs. We thus answer in the affirmative the value version of the major open question repeatedly asked in the dynamic graph algorithms literature. Our randomized algorithms' approximation and worst-case update time bounds both hold w.h.p. against adaptive adversaries.Our algorithms are based on simulating new two-pass streaming matching algorithms in the dynamic setting. Our key new idea is to invoke the recent sublinear-time matching algorithm of Be<PERSON>ezhad (FOCS'21) in a white-box manner to efficiently simulate the second pass of our streaming algorithms, while bypassing the well-known vertex-update barrier.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH5"}, {"primary_key": "1258875", "vector": [], "sparse_vector": [], "title": "Small Shadows of <PERSON><PERSON><PERSON>.", "authors": ["<PERSON>"], "summary": "The diameter of the graph of a d-dimensional lattice polytope P ⊆ [0, k]n is known to be at most dk due to work by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>. However, it is an open question whether the monotone diameter, the shortest guaranteed length of a monotone path, of a d-dimensional lattice polytope P = {x : Ax ≤ b} ⊆ [0,k]n is bounded by a polynomial in d and k. This question is of particular interest in linear optimization, since paths traced by the Simplex method must be monotone. We introduce partial results in this direction including a monotone diameter bound of 3d for k = 2, a monotone diameter bound of (d — 1)m + 1 for d-dimensional (ℓ + 1)-level polytopes, a pivot rule such that the Simplex method is guaranteed to take at most dnk||A||∞ non-degenerate steps to solve a LP on P, and a bound of dk for lengths of paths from certain fixed starting points. Finally, we present a constructive approach to a diameter bound of (3/2)dk and describe how to translate this final bound into an algorithm that solves a linear program by tracing such a path.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH63"}, {"primary_key": "1258876", "vector": [], "sparse_vector": [], "title": "Sparse graphs with bounded induced cycle packing number have logarithmic treewidth.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A graph is Ok-free if it does not contain k pairwise vertex-disjoint and non-adjacent cycles. We show that MAXIMUM INDEPENDENT SET and 3-COLORING in Ok-free graphs can be solved in quasi-polynomial time. As a main technical result, we establish that \"sparse\" (here, not containing large complete bipartite graphs as subgraphs) Ok-free graphs have treewidth (even, feedback vertex set number) at most logarithmic in the number of vertices. This is proven sharp as there is an infinite family of O2-free graphs without K3,3-subgraph and whose treewidth is (at least) logarithmic.Other consequences include that most of the central NP-complete problems (such as MAXIMUM INDEPENDENT SET, MINIMUM VERTEX COVER, MINIMUM DOMINATING SET, MINIMUM COLORING) can be solved in polynomial time in sparse Ok-free graphs, and that deciding the Ok-freeness of sparse graphs is polynomial time solvable.* This work was supported by the ANR projects DISTANCIA (ANR-17-CE40-0015), DIGRAPHS (ANR-19-CE48-0013-01), and TWIN-WIDTH (ANR-21-CE48-0014-01), by the LabEx PERSYVAL-lab (ANR-11-LABX-0025), and by the Vanier Canada Graduate Scholarships program.† The full version of the paper can be accessed at https://arxiv.org/abs/2206.00594", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH116"}, {"primary_key": "1258877", "vector": [], "sparse_vector": [], "title": "Integrality Gaps for Random Integer Programs via Discrepancy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we prove new bounds on the additive gap between the value of a random integer program max cTx, Ax ≤ b, x ∈ {0,1}n with m constraints and that of its linear programming relaxation for a wide range of distributions on (A,b,c). Our investigation is motivated by the work of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (SODA'21), who gave a framework for relating the size of Branch-and-Bound (B&B) trees to additive integrality gaps.<PERSON> and <PERSON> (MOR '89) and <PERSON><PERSON> et al. (Mathematical Programming '22), respectively, showed that for certain random packing and Gaussian IPs, where the entries of A, c are independently distributed according to either the uniform distribution on [0,1] or the Gaussian distribution N(0,1), the integrality gap is bounded by Om(log2 n/n) with probability at least 1 − 1/n - e−Ωm(1). In this paper, we generalize these results to the cases where the entries of A are uniformly distributed on an integer interval (e.g., entries in {-1,0,1}), and where the columns of A are distributed according to an isotropic logconcave distribution. Second, we substantially improve the success probability to 1 - 1/poly(n), compared to constant probability in prior works (depending on m). Leveraging the connection to Branch-and-Bound, our gap results imply that for these IPs B&B trees have size npoly(m) with high probability (i.e., polynomial for fixed m), which significantly extends the class of IPs for which B&B is known to be polynomial.Our main technical contribution and the key to achieving the above results is a new linear discrepancy theorem for random matrices. Our theorem gives general conditions under which a target vector is equal to or very close to a {0,1} combination of the columns of a random matrix A. Compared to prior results, our theorem handles a much wider range of distributions on A, both continuous and discrete, and achieves success probability exponentially close to 1, as opposed to the constant probability shown in earlier results. Our proof uses a Fourier analytic approach, building on the work of Hoberg and Rothvoss (SODA '19) and Franks and Saks (RSA '20) who studied the discrepancy of random set systems and matrices respectively.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH65"}, {"primary_key": "1258878", "vector": [], "sparse_vector": [], "title": "Traversing the FFT Computation Tree for Dimension-Independent Sparse Fourier Transforms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We are interested in the well-studied Sparse Fourier transform problem, where one aims to quickly recover an approximately Fourier k-sparse domain vector from observing its time domain representation x. In the exact k-sparse case the best known dimension-independent algorithm runs in near cubic time in k and it is unclear whether a faster algorithm like in low dimensions is possible. Beyond that, all known approaches either suffer from an exponential dependence of their runtime on the dimension d or can only tolerate a trivial amount of noise. This is in sharp contrast with the classical FFT algorithm of <PERSON>ey and Tukey, which is stable and completely insensitive to the dimension of the input vector: its runtime is O(N log N) in any dimension d for N = nd. Our work aims to address the above issues.First, we provide a translation/reduction of the exactly k-sparse Sparse FT problem to a concrete tree exploration task which asks to recover k leaves in a full binary tree under certain exploration rules. Subsequently, we provide (a) an almost quadratic in k time algorithm for the latter task, and (b) evidence that obtaining a strongly subquadratic time for Sparse FT via this approach is likely to be impossible. We achieve the latter by proving a conditional quadratic time lower bound on sparse polynomial multipoint evaluation (the classical non-equispaced sparse Fourier transform problem) which is a core routine in the aforementioned translation. Thus, our results combined can be viewed as an almost complete understanding of this approach, which is the only known approach that yields sublinear time dimension-independent Sparse FT algorithms.Subsequently, we provide a robustification of our algorithm, yielding a robust cubic time algorithm under bounded ℓ2 noise. This requires proving new structural properties of the recently introduced adaptive aliasing filters combined with a variety of new techniques and ideas. Lastly, we provide a preliminary experimental evaluation comparing the runtime of our algorithm to FFTW and SFFT 2.0.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH177"}, {"primary_key": "1258879", "vector": [], "sparse_vector": [], "title": "Lossless Online Rounding for Online Bipartite Matching (Despite its Impossibility).", "authors": ["<PERSON><PERSON>", "<PERSON> (<PERSON><PERSON><PERSON>) <PERSON><PERSON>", "<PERSON>"], "summary": "For numerous online bipartite matching problems, such as edge-weighted matching and matching under two-sided vertex arrivals, the state-of-the-art fractional algorithms outperform their randomized integral counterparts. This gap is surprising, given that the bipartite fractional matching polytope is integral, and so lossless rounding is possible. This gap was explained by <PERSON><PERSON><PERSON> et al. (SODA'13), who showed that online lossless rounding is impossible.Despite the above, we initiate the study of lossless online rounding for online bipartite matching problems. Our key observation is that while lossless online rounding is impossible in general, randomized algorithms induce fractional algorithms of the same competitive ratio which by definition are losslessly roundable online. This motivates the addition of constraints that decrease the \"online integrality gap\", thus allowing for lossless online rounding. We characterize a set of non-convex constraints which allow for such lossless online rounding, and better competitive ratios than yielded by deterministic algorithms.As applications of our lossless online rounding approach, we obtain two results of independent interest: (i) a doubly-exponential improvement, and a sharp threshold for the amount of randomness (or advice) needed to outperform deterministic online (vertex-weighted) bipartite matching algorithms, and (ii) an optimal semi-OCS, matching a recent result of <PERSON> et al. (FOCS'21) answering a question of <PERSON><PERSON><PERSON><PERSON> et al. (FOCS'20).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH78"}, {"primary_key": "1258880", "vector": [], "sparse_vector": [], "title": "Parallel Exact Shortest Paths in Almost Linear Work and Square Root Depth.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a randomized parallel single-source shortest paths (SSSP) algorithm for directed graphs with non-negative integer edge weights that solves the problem exactly in O(m) work and n1/2+o(1) span, with high probability. All previous exact SSSP algorithms with nearly linear work have linear span, even for undirected unweighted graphs. Our main technical contribution is to show a reduction from the exact SSSP to directed hopsets [6] using the iterative gradual rounding technique [9]. An (h, ε)-hopset is a set of weighted edges (sometimes called shortcuts) that when added to the graph admit h-hop paths with weights no more than (1 + ε) times the true shortest path distances.Furthermore, we show how to combine this algorithm with <PERSON> and <PERSON>'s framework [15] to improve the distributed exact SSSP algorithm. Specifically, we obtain an -round algorithm in the CONGEST model for exact SSSP in directed graphs with non-negative integer edge weights, where D is the unweighted diameter of the underlying undirected graph.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH166"}, {"primary_key": "1258881", "vector": [], "sparse_vector": [], "title": "Zigzagging through acyclic orientations of chordal graphs and hypergraphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In 1993, <PERSON>, <PERSON><PERSON>, and <PERSON> described an inductive construction for generating every acyclic orientation of a chordal graph exactly once, flipping one arc at a time. We provide two generalizations of this result. Firstly, we describe Gray codes for acyclic orientations of hypergraphs that satisfy a simple ordering condition, which generalizes the notion of perfect elimination order of graphs. This unifies the Savage-Squire-West construction with a recent algorithm for generating elimination trees of chordal graphs (SODA 2022). Secondly, we consider quotients of lattices of acyclic orientations of chordal graphs, and we provide a Gray code for them, addressing a question raised by <PERSON><PERSON><PERSON> (FPSAC 2022). This also generalizes a recent algorithm for generating lattice congruences of the weak order on the symmetric group (SODA 2020). Our algorithms are derived from the <PERSON><PERSON>-<PERSON><PERSON>-<PERSON>-<PERSON> combinatorial generation framework, and they yield simple algorithms for computing Hamilton paths and cycles on large classes of polytopes, including chordal nestohedra and quotientopes. In particular, we derive an efficient implementation of the Savage-Squire-West construction. Along the way, we give an overview of old and recent results about the polyhedral and order-theoretic aspects of acyclic orientations of graphs and hypergraphs.* <PERSON> was supported by ANID Becas Chile 2019-72200522. <PERSON><PERSON> was supported by Czech Science Foundation grant GA 22-15272S. <PERSON> and <PERSON><PERSON> were also supported by German Science Foundation grant 413902284.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH117"}, {"primary_key": "1258882", "vector": [], "sparse_vector": [], "title": "Steiner Connectivity Augmentation and Splitting-off in Poly-logarithmic Maximum Flows.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We give an almost-linear time algorithm for the Steiner connectivity augmentation problem: given an undirected graph, find a smallest (or minimum weight) set of edges whose addition makes a given set of terminals τ-connected (for any given τ > 0). The running time of our algorithm is dominated by polylogarithmic calls to any maximum flow subroutine; using the recent almost-linear time maximum flow algorithm (<PERSON> et al., FOCS 2022), we get an almost-linear running time for our algorithm as well. This is tight up to the polylogarithmic factor even for just two terminals. Prior to our work, an almost-linear (in fact, near-linear) running time was known only for the special case of global connectivity augmentation, i.e., when all vertices are terminals (<PERSON><PERSON> et al., STOC 2022).We also extend our algorithm to the closely related Steiner splitting-off problem, where the edges incident on a vertex have to be split-off while maintaining the (Steiner) connectivity of a given set of terminals. Prior to our work, a nearly-linear time algorithm was known only for the special case of global connectivity (<PERSON><PERSON> et al., STOC 2022). The only known generalization beyond global connectivity was to preserve all pairwise connectivities using a much slower algorithm that makes n calls to an all-pairs maximum flow (or Gomory-Hu tree) subroutine (<PERSON> and <PERSON>, SICOMP 2013), as against polylog(n) calls to a (single-pair) maximum flow subroutine in this work.* <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> were supported in part by NSF grants CCF-1750140 (CAREER Award) and CCF-1955703.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH95"}, {"primary_key": "1258883", "vector": [], "sparse_vector": [], "title": "Improved Pattern-Avoidance Bounds for Greedy BSTs via Matrix Decomposition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nidia Obscura <PERSON>", "<PERSON><PERSON><PERSON>", "Sorrachai <PERSON>i"], "summary": "Greedy BST (or simply Greedy) is an online self-adjusting binary search tree defined in the geometric view ([<PERSON>, 1988; <PERSON>, 2000; <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, SODA 2009). Along with Splay trees (<PERSON><PERSON><PERSON>, <PERSON><PERSON> 1985), Greedy is considered the most promising candidate for being dynamically optimal, i.e., starting with any initial tree, their access costs on any sequence is conjectured to be within O(1) factor of the offline optimal. However, despite having received a lot of attention in the past four decades, the question has remained elusive even for highly restricted input.In this paper, we prove new bounds on the cost of Greedy in the \"pattern avoidance\" regime. Our new results include:• The (preorder) traversal conjecture for Greedy holds up to a factor of O(2α(n)), improving upon the bound of 2α(n)O(1) in (<PERSON><PERSON><PERSON><PERSON> et al., FOCS 2015) where α(n) is the inverse Ackermann function of n. This is the best known bound obtained by any online BSTs.• We settle the postorder traversal conjecture for Greedy. Previously this was shown for Splay trees only in certain special cases (<PERSON> and <PERSON><PERSON><PERSON>, <PERSON>DS 2019).• The deque conjecture for <PERSON><PERSON><PERSON> holds up to a factor of O(α(n)), improving upon the bound 2O(α(n)) in (<PERSON><PERSON><PERSON><PERSON>, et al., <PERSON>DS 2015). This is arguably \"one step away\" from the bound O(α*(n)) for Splay trees (<PERSON>tie, SODA 2010).• The split conjecture holds for Greedy up to a factor of O(2α(n)). Previously the factor of O(α(n)) was shown for Splay trees only in a special case (Lucas, 1988).The input sequences in traversal and deque conjectures are perhaps \"easiest\" in the pattern-avoiding input classes and yet among the most notorious special cases of the dynamic optimality conjecture. Key to all these results is to partition (based on the input structures) the execution log of Greedy into several simpler-to-analyze subsets for which classical forbidden submatrix bounds can be leveraged. We believe that this simple method will find further applications in doing amortized analysis of data structures via extremal combinatorics. Finally, we show the applicability of this technique to handle a class of increasingly complex pattern-avoiding input sequences, called k-increasing sequences.As a bonus, we discover a new class of permutation matrices whose extremal bounds are polynomially bounded. This gives a partial progress on an open question by Jacob Fox (2013).* The full version of the paper can be accessed at https://arxiv.org/abs/2211.04112", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH22"}, {"primary_key": "1258884", "vector": [], "sparse_vector": [], "title": "Finding Triangles and Other Small Subgraphs in Geometric Intersection Graphs.", "authors": ["<PERSON>"], "summary": "We consider problems related to finding short cycles, small cliques, small independent sets, and small subgraphs in geometric intersection graphs. We obtain a plethora of new results. For example:", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH68"}, {"primary_key": "1258885", "vector": [], "sparse_vector": [], "title": "On the Number of Incidences When Avoiding an Induced Biclique in Geometric Settings.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Given a set of points $P$ and a set of regions $\\mathcal{O}$, an incidence is a pair $(p,o ) \\in P \\times \\mathcal{O}$ such that $p \\in o$. We obtain a number of new results on a classical question in combinatorial geometry: What is the number of incidences (under certain restrictive conditions)? We prove a bound of $O\\bigl( k n(\\log n/\\log\\log n)^{d-1} \\bigr)$ on the number of incidences between $n$ points and $n$ axis-parallel boxes in $\\mathbb{R}^d$, if no $k$ boxes contain $k$ common points, that is, if the incidence graph between the points and the boxes does not contain $K_{k,k}$ as a subgraph. This new bound improves over previous work, by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> (2021), by more than a factor of $\\log^d n$ for $d>2$. Furthermore, it matches a lower bound implied by the work of <PERSON><PERSON><PERSON> (1990), for $k=2$, thus settling the question for points and boxes. We also study several other variants of the problem. For halfspaces, using shallow cuttings, we get a linear bound in two and three dimensions. We also present linear (or near linear) bounds for shapes with low union complexity, such as pseudodisks and fat triangles.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH50"}, {"primary_key": "1258886", "vector": [], "sparse_vector": [], "title": "Simplex Range Searching Revisited: How to Shave Logs in Multi-Level Data Structures.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We revisit the classic problem of simplex range searching and related problems in computational geometry. We present a collection of new results which improve previous bounds by multiple logarithmic factors that were caused by the use of multi-level data structures. Highlights include the following:", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH54"}, {"primary_key": "1258887", "vector": [], "sparse_vector": [], "title": "Short Synchronizing Words for Random Automata.", "authors": ["<PERSON>", "Guillem Perarnau"], "summary": "We prove that a uniformly random automaton with n states on a 2-letter alphabet has a synchronizing word of length with high probability (w.h.p.). That is to say, w.h.p. there exists a word ω of such length, and a state v0, such that ω sends all states to v0. This confirms a conjecture of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> [KKS13] based on numerical simulations, up to a log factor - the previous best partial result towards the conjecture was the quasilinear bound O(n log3 n) due to <PERSON><PERSON><PERSON> [Nic19]. Moreover, the synchronizing word ω we obtain has small entropy, in the sense that it can be encoded with only O(log(n)) bits w.h.p.Our proof introduces the concept of ω-trees, for a word ω, that is, automata in which the ω-transitions induce a (loop-rooted) tree. We prove a strong structure result that says that, w.h.p., a random automaton on n states is a ω-tree for some word ω of length at most (1 + ε) log2(n), for any ε > 0. The existence of the (random) word ω is proved by the probabilistic method. This structure result is key to proving that a short synchronizing word exists.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH26"}, {"primary_key": "1258888", "vector": [], "sparse_vector": [], "title": "Faster <PERSON> for Turn-based Stochastic Games with Bounded Treewidth.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Turn-based stochastic games (aka simple stochastic games) are two-player zero-sum games played on directed graphs with probabilistic transitions. The goal of player-max is to maximize the probability to reach a target state against the adversarial player-min. These games lie in NP ∩ coNP and are among the rare combinatorial problems that belong to this complexity class for which the existence of polynomial-time algorithm is a major open question. While randomized sub-exponential time algorithm exists, all known deterministic algorithms require exponential time in the worst-case. An important open question has been whether faster algorithms can be obtained parametrized by the treewidth of the game graph. Even deterministic sub-exponential time algorithm for constant treewidth turn-based stochastic games has remain elusive. In this work our main result is a deterministic algorithm to solve turn-based stochastic games that, given a game with n states, treewidth at most t, and the bit-complexity of the probabilistic transition function log D, has running time O ((tn2 log D)t log n). In particular, our algorithm is quasi-polynomial time for games with constant or poly-logarithmic treewidth.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH173"}, {"primary_key": "1258889", "vector": [], "sparse_vector": [], "title": "Faster Deterministic Worst-Case Fully Dynamic All-Pairs Shortest Paths via Decremental Hop-Restricted Shortest Paths.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dynamic all-pairs shortest paths is a well-studied problem in the field of dynamic graph algorithms. More specifically, given a directed weighted graph G = (V, E, ω) on n vertices which undergoes a sequence of vertex or edge updates, the goal is to maintain distances between any pair of vertices in V. In a classical work by [<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, 2004], the authors showed that all-pairs shortest paths can be maintained deterministically in amortized Õ(n2) time1, which is nearly optimal. For worst-case update time guarantees, so far the best randomized algorithm has Õ(n3-1/3) time [<PERSON>, <PERSON>, <PERSON>, 2017], and the best deterministic algorithm needs Õ(n3-2/7) time [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 2020].We provide a faster deterministic worst-case update time of Õ(n3-20/61) for fully dynamic all-pairs shortest paths. To achieve this improvement, we study a natural variant of this problem where a hop constraint is imposed on shortest paths between vertices; that is, given a parameter h, the h-hop shortest path between any pair of vertices s,t ∈ V is a path from s to t with at most h edges whose total weight is minimized. As a result which might be of independent interest, we give a deterministic algorithm that maintains all-pairs h-hop shortest paths under vertex deletions in total update time Õ(n3h + Kn2n2), where K bounds the total number of vertex deletions.* This publication is part of a project that has received funding from the European Research Council (ERC) under the European Union's Horizon 2020 research and innovation programme (grant agreement No 803118 UncertainENV).1 Õ(·) hides poly-log factors.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH4"}, {"primary_key": "1258890", "vector": [], "sparse_vector": [], "title": "Timeliness Through Telephones: Approximating Information Freshness in Vector Clock Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider an information dissemination problem where the root node in an undirected graph constantly updates its information. The goal is to keep every other node in the graph as freshly informed about the root as possible. Our synchronous information spreading model uses telephone calls at each time step, in which any node can communicate with at most one neighbor, thus forming a matching over which information is transmitted at each step. We introduce two problems in minimizing two natural objectives (Maximum and Average) of the latency of the root's information at all nodes in the network.After deriving a simple reduction from the maximum rooted latency problem to the well-studied minimum broadcast time problem, we focus on the average rooted latency version. We introduce a natural problem of finding a finite schedule that minimizes the average broadcast time from a root. We show that any average rooted latency scheme induces a solution to this average broadcast problem within a constant factor and conversely, this average broadcast time is within a logarithmic factor of the average rooted latency. Then, we derive a log-squared approximation algorithm for the average broadcast time problem via rounding a time-indexed linear programming relaxation, resulting in a log-cubed approximation for the average latency problem.Surprisingly, we show that using the average broadcast time for average rooted latency introduces a necessary logarithmic factor overhead even in trees. We overcome this hurdle and give a 40-approximation for trees. For this, we design an algorithm to find near-optimal locally-periodic schedules in trees where each vertex receives information from its parent in regular intervals. On the other side, we show how such well-behaved schedules approximate the optimal schedule within a constant factor.* This material is based upon work supported in part by the U. S. Office of Naval Research under award number N00014-21-1-2243 and the Air Force Office of Scientific Research under award number FA9550-20-1-0080.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH93"}, {"primary_key": "1258891", "vector": [], "sparse_vector": [], "title": "Differentially Private All-Pairs Shortest Path Distances: Improved Algorithms and Lower Bounds.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xu"], "summary": "We study the problem of releasing the weights of all-pairs shortest paths in a weighted undirected graph with differential privacy (DP). In this setting, the underlying graph is fixed and two graphs are neighbors if their edge weights differ by at most 1 in the ℓ1-distance. We give an algorithm with additive error Õ(n2/3/ε) in the ε-DP case and an algorithm with additive error in the (ε,δ)-DP case, where n denotes the number of vertices. This positively answers a question of <PERSON><PERSON><PERSON> [Sea16, Sea20], who asked whether a o(n)- error algorithm exists. We also show that an additive error of Ω(n1/6) is necessary for any sufficiently small ε,δ > 0.Furthermore, we show that if the graph is promised to have reasonably bounded weights, one can improve the error further to roughly in the ε-DP case and roughly in the (ε, δ)-DP case. Previously, it was only known how to obtain Õ(n2/3/ε1/3) additive error in the ε-DP case and additive error in the (ε,δ)-DP case for bounded-weight graphs [Sea16].Finally, we consider a relaxation where a multiplicative approximation is allowed. We show that, with a multiplicative approximation factor k, the additive error can be reduced to Õ(n1/2+O(1/k)/ε) in the ε-DP case and Õ(n1/3+O(1/k)/ε) in the (ε,δ)-DP case.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH184"}, {"primary_key": "1258892", "vector": [], "sparse_vector": [], "title": "From Algorithms to Connectivity and Back: Finding a Giant Component in Random k-SAT.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We take an algorithmic approach to studying the solution space geometry of relatively sparse random and bounded degree k-CNFs for large k. In the course of doing so, we establish that with high probability, a random k-CNF Φ with n variables and clause density α = m/n ≲ 2k/6 has a giant component of solutions that are connected in a graph where solutions are adjacent if they have Hamming distance Ok(log n) and that a similar result holds for bounded degree k-CNFs at similar densities. We are also able to deduce looseness results for random and bounded degree k-CNFs in a similar regime.Although our main motivation was understanding the geometry of the solution space, our methods have algorithmic implications. Towards that end, we construct an idealized block dynamics that samples solutions from a random k-CNF Φ with density α = m/n ≲ 2k/52. We show this Markov chain can with high probability be implemented in polynomial time and by leveraging spectral independence, we also observe that it mixes relatively fast, giving a polynomial time algorithm to with high probability sample a uniformly random solution to a random k-CNF. Our work suggests that the natural route to pinning down when a giant component exists is to develop sharper algorithms for sampling solutions to random k-CNFs.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH132"}, {"primary_key": "1258893", "vector": [], "sparse_vector": [], "title": "Almost-Linear Planted Cliques Elude the Metropolis Process.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A seminal work of <PERSON><PERSON><PERSON> (1992) showed that large cliques elude the Metropolis process. More specifically, <PERSON><PERSON><PERSON> showed that the Metropolis algorithm cannot find a clique of size k = Θ(nα) for α ∈ (0,1/2), which is planted in the Erdős-R<PERSON>yi random graph G(n, 1/2), in polynomial time. Information theoretically it is possible to find such planted cliques as soon as k ≥ (2 + ε) log n. Since the work of <PERSON><PERSON><PERSON>, the computational problem of finding a planted clique in G(n, 1/2) was studied extensively and many polynomial time algorithms were shown to find the planted clique if it is of size , while no polynomial-time algorithm is known to work when . The computational problem of finding a planted clique of size is now widely considered as a foundational problem in the study of computational-statistical gaps. Notably, the first evidence of the problem's algorithmic hardness is commonly attributed to the result of <PERSON><PERSON><PERSON> from 1992.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH171"}, {"primary_key": "1258894", "vector": [], "sparse_vector": [], "title": "Testing and Learning Quantum Juntas Nearly Optimally.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of testing and learning quantum k-juntas: n-qubit unitary matrices which act non-trivially on just k of the n qubits and as the identity on the rest. As our main algorithmic results, we give1. A -query quantum algorithm that can distinguish quantum k-juntas from unitary matrices that are \"far\" from every quantum k-junta; and2. A O(4k)-query algorithm to learn quantum k-juntas.We complement our upper bounds for testing and learning quantum k-juntas with near-matching lower bounds of and Ω(4k/k), respectively. Our techniques are Fourier-analytic and make use of a notion of influence of qubits on unitaries.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.05898", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH43"}, {"primary_key": "1258895", "vector": [], "sparse_vector": [], "title": "A Near-Linear Time Sampler for the Ising Model with External Field.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a near-linear time sampler for the Gibbs distribution of the ferromagnetic Ising models with edge activities β > 1 and external fields λ 1) on general graphs with bounded or unbounded maximum degree.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH170"}, {"primary_key": "1258896", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>ve Simplification and Clustering under <PERSON><PERSON><PERSON>.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present new approximation results on curve simplification and clustering under <PERSON><PERSON><PERSON> distance. Let T = {ti : i ∈ [n]} be polygonal curves in ℝd of m vertices each. Let ℓ be any integer from [m]. We study a generalized curve simplification problem: given error bounds δi > 0 for i ∈ [n], find a curve σ of at most ℓ vertices such that dF (σ, ti) ≤ δi for i ∈ [n]. We present an algorithm that returns a null output or a curve σ of at most ℓ vertices such that dF(σ,τi) 0 and any fixed α,ε ∈ (0,1). The running time is Õ(mO(1/α) · (d/(αε))O(d/α)). By combining our technique with some previous results in the literature, we obtain an approximation algorithm for (k,ℓ)-median clustering. Given T, it computes a set Σ of k curves, each of ℓ vertices, such that is within a factor 1 + ε of the optimum with probability at least 1 — μ for any given μ, ε ∈ (0,1). The running time is † The full version of the paper can be accessed at https://arxiv.org/abs/2207.07809", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH51"}, {"primary_key": "1258897", "vector": [], "sparse_vector": [], "title": "Optimal Algorithms for Linear Algebra in the Current Matrix Multiplication Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study fundamental problems in linear algebra, such as finding a maximal linearly independent subset of rows or columns (a basis), solving linear regression, or computing a subspace embedding. For these problems, we consider input matrices A ∈ ℝn×d with n > d. The input can be read in nnz(A) time, which denotes the number of nonzero entries of A. In this paper, we show that beyond the time required to read the input matrix, these fundamental linear algebra problems can be solved in dω time, i.e., where ω ≈ 2.37 is the current matrix-multiplication exponent.To do so, we introduce a constant-factor subspace embedding with the optimal m = O (d) number of rows, and which can be applied in time for any trade-off parameter α > 0, tightening a recent result by <PERSON><PERSON><PERSON><PERSON> et. al. [SODA 2022] that achieves an exp(poly(log log n)) distortion with m = d · poly(log log d) rows in time. Our subspace embedding uses a recently shown property of stacked Subsampled Randomized Hadamard Transforms (SRHT), which actually increase the input dimension, to \"spread\" the mass of an input vector among a large number of coordinates, followed by random sampling. To control the effects of random sampling, we use fast semidefinite programming to reweight the rows. We then use our constant-factor subspace embedding to give the first optimal runtime algorithms for finding a maximal linearly independent subset of columns, regression, and leverage score sampling. To do so, we also introduce a novel subroutine that iteratively grows a set of independent rows, which may be of independent interest.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH154"}, {"primary_key": "1258898", "vector": [], "sparse_vector": [], "title": "Online Min-Max Paging.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by fairness requirements in communication networks, we introduce a natural variant of the online paging problem, called min-max paging, where the objective is to minimize the maximum number of faults on any page. While the classical paging problem, whose objective is to minimize the total number of faults, admits k-competitive deterministic and O(log k)-competitive randomized algorithms, we show that min-max paging does not admit a c(k)-competitive algorithm for any function c. Specifically, we prove that the randomized competitive ratio of min-max paging is Ω(log(n)) and its deterministic competitive ratio is Ω(k log(n)/log(k)), where n is the total number of pages ever requested.We design a fractional algorithm for paging with a more general objective - minimize the value of an n-variate differentiable convex function applied to the vector of the number of faults on each page. This gives an O(log(n) log(k))-competitive fractional algorithm for min-max paging. We show how to round such a fractional algorithm with at most a k factor loss in the competitive ratio, resulting in a deterministic O(k log(n) log(k))-competitive algorithm for min-max paging. This matches our lower bound modulo a poly(log(k)) factor. We also give a randomized rounding algorithm that results in a O(log2 n log k)-competitive algorithm.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH57"}, {"primary_key": "1258899", "vector": [], "sparse_vector": [], "title": "Parameterized Algorithm for the Disjoint Path Problem on Planar Graphs: Exponential in k2 and Linear in n.", "authors": ["Kyungjin Cho", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we study the Planar Disjoint Paths problem: Given an undirected planar graph G with n vertices and a set T of k pairs (si, ti)ki=1 of vertices, the goal is to find a set P of k pairwise vertex-disjoint paths connecting si and ti for all indices i ∈ {1,…, k}. We present a 2O(k2)n-time algorithm for the Planar Disjoint Paths problem. This improves the two previously best-known algorithms: 22O(k)-time algorithm [Discrete Applied Mathematics 1995] and 2O(k2)n6-time algorithm [STOC 2020].* The full version of the paper can be accessed at https://arxiv.org/abs/2211.03341† This work was supported by the National Research Foundation of Korea (NRF) grant funded by the Korea government (MSIT) (No.2020R1C1C1012742).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH144"}, {"primary_key": "1258900", "vector": [], "sparse_vector": [], "title": "Stronger 3SUM-Indexing Lower Bounds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The 3SUM-Indexing problem was introduced as a data structure version of the 3SUM problem, with the goal of proving strong conditional lower bounds for static data structures via reductions. Ideally, the conjectured hardness of 3SUM-Indexing should be replaced by an unconditional lower bound. Unfortunately, we are far from proving this, with the strongest current lower bound being a logarithmic query time lower bound by <PERSON><PERSON><PERSON><PERSON> et al. from STOC'20. Moreover, their lower bound holds only for non-adaptive data structures and they explicitly asked for a lower bound for adaptive data structures. Our main contribution is precisely such a lower bound against adaptive data structures. As a secondary result, we also strengthen the non-adaptive lower bound of <PERSON><PERSON><PERSON><PERSON> et al. and prove strong lower bounds for 2-bit-probe non-adaptive 3SUM-Indexing data structures via a completely new approach that we find interesting in its own right.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH19"}, {"primary_key": "1258901", "vector": [], "sparse_vector": [], "title": "Foundations of Transaction Fee Mechanism Design.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In blockchains such as Bitcoin and Ethereum, users compete in a transaction fee auction to get their transactions confirmed in the next block. A line of recent works set forth the desiderata for a \"dream\" transaction fee mechanism (TFM), and explored whether such a mechanism existed. A dream TFM should satisfy 1) user incentive compatibility (UIC), i.e., truthful bidding should be a user's dominant strategy; 2) miner incentive compatibility (MIC), i.e., the miner's dominant strategy is to faithfully implement the prescribed mechanism; and 3) miner-user side contract proofness (SCP), i.e., no coalition of the miner and one or more user(s) can increase their joint utility by deviating from the honest behavior. The weakest form of SCP is called 1-SCP, where we only aim to provide resilience against the collusion of the miner and a single user. Sadly, despite the various attempts, to the best of knowledge, no existing mechanism can satisfy all three properties in all situations.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH150"}, {"primary_key": "1258902", "vector": [], "sparse_vector": [], "title": "A Distanced Matching Game, Decremental APSP in Expanders, and Faster Deterministic Algorithms for Graph Cut Problems.", "authors": ["<PERSON>"], "summary": "Expander graphs play a central role in graph theory and algorithms. With a number of powerful algorithmic tools developed around them, such as the Cut-Matching game, expander pruning, expander decomposition, and algorithms for decremental All-Pairs Shortest Paths (APSP) in expanders, to name just a few, the use of expanders in the design of graph algorithms has become ubiquitous. Specific applications of interest to us are fast deterministic algorithms for cut problems in static graphs, and algorithms for dynamic distance-based graph problems, such as APSP.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH82"}, {"primary_key": "1258903", "vector": [], "sparse_vector": [], "title": "Hierarchies of Minion Tests for PCSPs through Tensors.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We provide a unified framework to study hierarchies of relaxations for Constraint Satisfaction Problems and their Promise variant. The idea is to split the description of a hierarchy into an algebraic part, depending on a minion capturing the \"base level\" of the hierarchy, and a geometric part - which we call tensorisation - inspired by multilinear algebra.We show that the hierarchies of minion tests obtained in this way are general enough to capture the (combinatorial) bounded width and also the Sherali-Adams LP, Sum-of-Squares SDP, and affine IP hierarchies. We exploit the geometry of the tensor spaces arising from our construction to prove general properties of such hierarchies. We identify certain classes of minions, which we call linear and conic, whose corresponding hierarchies have particularly fine features. Finally, in order to analyse the Sum-of-Squares SDP hierarchy we also characterise the solvability of the standard SDP relaxation through a new minion.* The research leading to these results has received funding from the European Research Council (ERC) under the European Union's Horizon 2020 research and innovation programme (grant agreement No 714532). The paper reflects only the authors' views and not the views of the ERC or the European Commission. The European Union is not liable for any use that may be made of the information contained therein. This work was also supported by UKRI EP/X024431/1.† The full version of the paper can be accessed at https://arxiv.org/abs/2207.02277.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH25"}, {"primary_key": "1258904", "vector": [], "sparse_vector": [], "title": "Approximate Graph Colouring and Crystals.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We show that approximate graph colouring is not solved by any level of the affine integer programming (AIP) hierarchy. To establish the result, we translate the problem of exhibiting a graph fooling a level of the AIP hierarchy into the problem of constructing a highly symmetric crystal tensor. In order to prove the existence of crystals in arbitrary dimension, we provide a combinatorial characterisation for realisable systems of tensors; i.e., sets of low-dimensional tensors that can be realised as the projections of a single high-dimensional tensor.* The research leading to these results has received funding from the European Research Council (ERC) under the European Union's Horizon 2020 research and innovation programme (grant agreement No 714532). The paper reects only the authors' views and not the views of the ERC or the European Commission. The European Union is not liable for any use that may be made of the information contained therein. This work was also supported by UKRI EP/X024431/1.† The full version of the paper can be accessed at https://arxiv.org/abs/2210.08293", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH86"}, {"primary_key": "1258905", "vector": [], "sparse_vector": [], "title": "Breaching the 2 LMP Approximation Barrier for Facility Location with Applications to k-Median.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>"], "summary": "The Uncapacitated Facility Location (UFL) problem is one of the most fundamental clustering problems: Given a set of clients C and a set of facilities F in a metric space (C ∪ F, dist) with facility costs open : F→ ℝ+, the goal is to find a set of facilities S ⊆ F to minimize the sum of the opening cost open(S) and the connection cost d(S) := Σp∈C minc∈S dist(p,c). An algorithm for UFL is called a Lagrangian Multiplier Preserving (LMP) α approximation if it outputs a solution S ⊆ F satisfying open(S) + d(S) ≤ open(S*) + αd(S*) for any S* ⊆ F. The best-known LMP approximation ratio for UFL is at most 2 by the JMS algorithm of <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [STOC'02, J.ACM'03] based on the Dual-Fitting technique. The lack of progress on improving the upper bound on αLMP in the last two decades raised the natural question whether αLMP = 2.We answer this question negatively by presenting a (slightly) improved LMP approximation algorithm for UFL. This is achieved by combining the Dual-Fitting technique with Local Search, another popular technique to address clustering problems. In more detail, we use the LMP solution S produced by JMS to seed a local search algorithm. We show that local search substantially improves S unless a big fraction of the connection cost of S is associated with facilities of relatively small opening costs. In the latter case however the analysis of <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> can be improved (i.e., S is cheaper than expected). To summarize: Either S is close enough to the optimum, or it must belong to the local neighborhood of a good enough local optimum. From a conceptual viewpoint, our result gives a theoretical evidence that local search can be enhanced so as to avoid bad local optima by choosing the initial feasible solution with LP-based techniques.Our result directly implies a (slightly) improved approximation for the related k-Median problem, another fundamental clustering problem: Given (C ∪ F, dist) as in a UFL instance and an integer k ∈ ℕ, find S ⊆ F with |S| = k that minimizes d(S). The current best approximation algorithms for k-Median are based on the following framework: use an LMP α approximation algorithm for UFL to build an α approximate bipoint solution for k-Median, and then round it with a ρBR approximate bipoint rounding algorithm. This implies an α · ρBR approximation. The current-best value of ρBR is 1.338 by Byrka, Pensyl, Rybicki, Srinivasan, and Trinh [SODA'15, TALG'17], which yields 2.6742-approximation. Combining their algorithm with our refined LMP algorithm for UFL (replacing JMS) gives a 2.67059-approximation.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.05150", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH37"}, {"primary_key": "1258906", "vector": [], "sparse_vector": [], "title": "Interdependent Public Projects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>-<PERSON>"], "summary": "In the interdependent values (IDV) model introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON> [1982], agents have private signals that capture their information about different social alternatives, and the valuation of every agent is a function of all agent signals. While interdependence has been mainly studied for auctions, it is extremely relevant for a large variety of social choice settings, including the canonical and practically important setting of public projects. The IDV model is much more realistic but also very challenging relative to the standard independent private values model. Welfare guarantees for IDV have been achieved mainly through two alternative conditions known as single-crossing and submodularity over signals (SOS). In either case, the existing theory falls short of solving the public projects setting.Our contribution is twofold: (i) We give a useful characterization of truthfulness for IDV public projects, parallel to the known characterization for independent private values, and identify the domain frontier for which this characterization applies; (ii) Using this characterization, we provide possibility and impossibility results for welfare approximation in public projects with SOS valuations. Our main impossibility result is that, in contrast to auctions, no universally truthful mechanism performs better for public projects with SOS valuations than choosing a project at random. Our main positive result applies to excludable public projects with SOS, for which we establish a constant factor approximation similar to auctions. Our results suggest that exclusion may be a key tool for achieving welfare guarantees in the IDV model.* The full version of the paper can be accessed at https://arxiv.org/abs/2204.08044. This project has received funding from the European Research Council (ERC) under the European Union's Horizon 2020 research and innovation program (grant agreement no. 866132), by the Israel Science Foundation (ISF grant nos. 317/17 and 336/18), by an Amazon Research Award, and by the NSF-BSF (grant no. 2020788).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH18"}, {"primary_key": "1258907", "vector": [], "sparse_vector": [], "title": "A Sublinear-Time Quantum Algorithm for Approximating Partition Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a novel quantum algorithm for estimating Gibbs partition functions in sublinear time with respect to the logarithm of the size of the state space. This is the first speed-up of this type to be obtained over the seminal nearly-linear time algorithm of <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>pal<PERSON> and Vigoda [45]. Our result also preserves the quadratic speed-up in precision and spectral gap achieved in previous work by exploiting the properties of quantum Markov chains. As an application, we obtain new polynomial improvements over the best-known algorithms for computing the partition function of the Ising model, counting the number of k-colorings, matchings or independent sets of a graph, and estimating the volume of a convex body.Our approach relies on developing new variants of the quantum phase and amplitude estimation algorithms that return nearly unbiased estimates with low variance and without destroying their initial quantum state. We extend these subroutines into a nearly unbiased quantum mean estimator that reduces the variance quadratically faster than the classical empirical mean. No such estimator was known to exist prior to our work. These properties, which are of general interest, lead to better convergence guarantees within the paradigm of simulated annealing for computing partition functions.* The full version of the paper can be accessed at http://arxiv.org/abs/2207.08643.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH46"}, {"primary_key": "1258908", "vector": [], "sparse_vector": [], "title": "Almost Consistent Systems of Linear Equations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Checking whether a system of linear equations is consistent is a basic computational problem with ubiquitous applications. When dealing with inconsistent systems, one may seek an assignment that minimizes the number of unsatisfied equations. This problem is NP-hard and UGC-hard to approximate within any constant even for two-variable equations over the two-element field. We study this problem from the point of view of parameterized complexity, with the parameter being the number of unsatisfied equations. We consider equations defined over Euclidean domains—a family of commutative rings that generalize finite and infinite fields including the rationals, the ring of integers and many other structures. We show that if every equation contains at most two variables, the problem is fixed-parameter tractable. This generalizes many eminent graph separation problems such as Bipartization, Multiway Cut and Multicut parameterized by the size of the cutset. To complement this, we show that the problem is W[1]-hard when three or more variables are allowed in an equation, as well as for many commutative rings that are not Euclidean domains. On the technical side, we introduce the notion of important balanced subgraphs, generalizing important separators of Marx [Theor. Comput. Sci. 2006] to the setting of biased graphs. Furthermore, we use recent results on parameterized MinCSP [<PERSON> et al., SODA 2021] to efficiently solve a generalization of Multicut with disjunctive cut requests.* The full version of the paper can be accessed at https://arxiv.org/abs/2208.02732", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH121"}, {"primary_key": "1258909", "vector": [], "sparse_vector": [], "title": "Improved Distributed Algorithms for the Lovász Local Lemma and Edge Coloring.", "authors": ["<PERSON>"], "summary": "The Lovász Local Lemma is a classic result in probability theory that is often used to prove the existence of combinatorial objects via the probabilistic method. In its simplest form, it states that if we have n 'bad events', each of which occurs with probability at most p and is independent of all but d other events, then under certain criteria on p and d, all of the bad events can be avoided with positive probability.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH163"}, {"primary_key": "1258910", "vector": [], "sparse_vector": [], "title": "Testing Convex Truncation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the basic statistical problem of testing whether normally distributed n-dimensional data has been truncated, i.e. altered by only retaining points that lie in some unknown truncation set S ⊆ ℝn. As our main algorithmic results1. We give a computationally efficient O(n)-sample algorithm that can distinguish the standard normal distribution N(0,In) from N(0,In) conditioned on an unknown and arbitrary convex set S.2. We give a different computationally efficient O(n)-sample algorithm that can distinguish N(0,In) from N(0,In) conditioned on an unknown and arbitrary mixture of symmetric convex sets.These results stand in sharp contrast with known results for learning or testing convex bodies with respect to the normal distribution or learning convex-truncated normal distributions, where state-of-the-art algorithms require essentially samples. An easy argument shows that no finite number of samples suffices to distinguish N(0,In) from an unknown and arbitrary mixture of general (not necessarily symmetric) convex sets, so no common generalization of results (1) and (2) above is possible.We also prove lower bounds on the sample complexity of distinguishing algorithms (computationally efficient or otherwise) for various classes of convex truncations; in some cases these lower bounds match our algorithms up to logarithmic or even constant factors.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH155"}, {"primary_key": "1258911", "vector": [], "sparse_vector": [], "title": "A Polynomial-Time Algorithm for 1/2-Well-Supported Nash Equilibria in Bimatrix Games.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Since the seminal PPAD-completeness result for computing a Nash equilibrium even in two-player games, an important line of research has focused on relaxations achievable in polynomial time. In this paper, we consider the notion of ε-well-supported Nash equilibrium, where ε ∈ [0,1] corresponds to the approximation guarantee. Put simply, in an ε-well-supported equilibrium, every player chooses with positive probability actions that are within ε of the maximum achievable payoff, against the other player's strategy. Ever since the initial approximation guarantee of 2/3 for well-supported equilibria, which was established more than a decade ago, the progress on this problem has been extremely slow and incremental. Notably, the small improvements to 0.6608, and finally to 0.6528, were achieved by algorithms of growing complexity. Our main result is a simple and intuitive algorithm, that improves the approximation guarantee to 1/2. Our algorithm is based on linear programming and in particular on exploiting suitably defined zero-sum games that arise from the payoff matrices of the two players. As a byproduct, we show how to achieve the same approximation guarantee in a query-efficient way.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.07007", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH146"}, {"primary_key": "1258912", "vector": [], "sparse_vector": [], "title": "Generalized Unrelated Machine Scheduling Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the generalized load-balancing (GLB) problem, where we are given n jobs, each of which needs to be assigned to one of m unrelated machines with processing times {pij}. Under a job assignment σ, the load of each machine i is Ψi(pi[σ]) where ψi: ℝn → ℝ≥0 is a symmetric monotone norm and pi[σ] is the n- dimensional vector {pij · 𝕀 [σ(j) = i]}j∈[n]. Our goal is to minimize the generalized makespan ø(load(σ)), where φ : ℝm → ℝ≥0 is another symmetric monotone norm and load(σ) is the m-dimensional machine load vector. This problem significantly generalizes many classic optimization problems, e.g., makespan minimization, set cover, minimum-norm load-balancing, etc. We also study the special case of identical machine scheduling, i.e., Pij = pj for all machine i.Our main result is a polynomial time randomized algorithm that achieves an approximation factor of O(log n), matching the lower bound of set cover (which is special case of GLB) up to constant factor. We achieve this by rounding a novel configuration LP relaxation with exponential number of variables. To approximately solve the configuration LP, we design an approximate separation oracle for its dual program. In particular, the separation oracle can be reduced to the norm minimization with a linear constraint (NormLin) problem and we devise a polynomial time approximation scheme (PTAS) for it, which may be of independent interest. We also obtain constant factor approximation algorithms for some special cases.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH110"}, {"primary_key": "1258913", "vector": [], "sparse_vector": [], "title": "Approximating Knapsack and Partition via Dense Subset Sums.", "authors": ["<PERSON><PERSON> Den<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Knapsack and Partition are two important additive problems whose fine-grained complexities in the (1 — ε)-approximation setting are not yet settled. In this work, we make progress on both problems by giving improved algorithms.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH113"}, {"primary_key": "1258914", "vector": [], "sparse_vector": [], "title": "Optimal Pricing Schemes for an Impatient Buyer.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Balasubramanian <PERSON>", "<PERSON><PERSON>"], "summary": "A patient seller aims to sell a good to an impatient buyer (i.e., one who discounts utility over time). The buyer will remain in the market for a period of time T, and her private value is drawn from a publicly known distribution. What is the revenue-optimal pricing-curve (sequence of (price, time) pairs) for the seller? Is randomization of help here? Is the revenue-optimal pricing-curve computable in polynomial time? We answer these questions in this paper. We give an efficient algorithm for computing the revenue-optimal pricing curve. We show that pricing curves, that post a price at each point of time and let the buyer pick her utility maximizing time to buy, are revenue-optimal among a much broader class of sequential lottery mechanisms: namely, mechanisms that allow the seller to post a menu of lotteries at each point of time cannot get any higher revenue than pricing curves. We also show that the even broader class of mechanisms that allow the menu of lotteries to be adaptively set, can earn strictly higher revenue than that of pricing curves, and the revenue gap can be as big as the support size of the buyer's value distribution.* The full version of the paper can be accessed at https://arxiv.org/abs/2106.02149.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH16"}, {"primary_key": "1258915", "vector": [], "sparse_vector": [], "title": "On Problems Related to Unbounded SubsetSum: A Unified Combinatorial Approach.", "authors": ["<PERSON><PERSON> Den<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unbounded SubsetSum is a classical textbook problem: given integers w1,w2, …, wn∈[1,u], c,u, we need to find if there exists m1,m2, …, mn ∈ ℕ satisfying c =Σni=1 wimi. In its all-target version, t ∈ ℤ+ is given and the answers for all integers c ∈ [0, t] are required. In this paper, we study three generalizations of this simple problem: All-Target Unbounded Knapsack, All-Target CoinChange and Residue Table. With new combinatorial insights into the structures of solutions, we present a novel two-phase approach. As a result, we show that:• All-Target CoinChange can be solved in Õ(u +t) time deterministically, improving the previous Õ(t4/3) time algorithm [<PERSON> and <PERSON>, ESA 2020].• Residue Table can be solved in Õ(u) time deterministically, improving the previous Õ(u3/2) time algorithm [Klein, 2021].•All-Target Unbounded Knapsack can be solved in Õ(T(u) + t) time, where is the running time for (min, +) convolution for length-n arrays, improving the previous O(u2 log u + t) time algorithm [<PERSON> and <PERSON>, ESA 2020].", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH114"}, {"primary_key": "1258916", "vector": [], "sparse_vector": [], "title": "Beating (1 - 1/e)-Approximation for Weighted Stochastic Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the stochastic weighted matching problem, the goal is to find a large-weight matching of a graph when we are uncertain about the existence of its edges. In particular, each edge e has a known weight we but is realized independently with some probability pe. The algorithm may query an edge to see whether it is realized. We consider the well-studied query commit version of the problem, in which any queried edge that happens to be realized must be included in the solution.<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> [SODA'19] showed that when the input graph is bipartite, the problem admits a (1 — 1/e)-approximation. In this paper, we give an algorithm that obtains a (1 — 1/e + δ)-approximation for an absolute constant δ > 0.0014, therefore breaking this prevalent bound.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH74"}, {"primary_key": "1258917", "vector": [], "sparse_vector": [], "title": "Competitive Information Design for Pandora&apos;s Box.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Haifeng Xu"], "summary": "We study a natural competitive-information-design variant for the Pandora's Box problem [31], where each box is associated with a strategic information sender who can design what information about the box's prize value to be revealed to the agent when she inspects the box. This variant with strategic boxes is motivated by a wide range of real-world economic applications for Pandora's box. The main contributions of this article are two-fold: (1) we study informational properties of Pandora's Box by analyzing how a box's partial information revelation affects the search agent's optimal decisions; and (2) we fully characterize the pure symmetric equilibrium for the boxes' competitive information revelation, which reveals various insights regarding information competition and the resultant agent utility at equilibrium.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH15"}, {"primary_key": "1258918", "vector": [], "sparse_vector": [], "title": "A Nearly Time-Optimal Distributed Approximation of Minimum Cost k-Edge-Connected Spanning Subgraph.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The minimum-cost k-edge-connected spanning subgraph (k-ECSS) problem is a generalization and strengthening of the well-studied minimum-cost spanning tree (MST) problem. While the round complexity of distributedly computing the latter has been well-understood, the former remains mostly open, especially as soon as k ≥ 3.In this paper, we present the first distributed algorithm that computes an approximation of k-ECSS in sublinear time for general k. <PERSON><PERSON><PERSON><PERSON>, we describe a randomized distributed algorithm that, in rounds, computes a k-edge-connected spanning subgraph whose cost is within an O(log n log k) factor of optimal. Here, n and D denote the number of vertices and diameter of the graph, respectively. This time complexity is nearly optimal for any k = poly(log n), almost matching an lower bound. Our algorithm is the first to achieve a sublinear round complexity for k ≥ 3. We note that this case is considerably more challenging than the well-studied and well-understood k =1 case—better known as MST—and the closely related k = 2 case.Our algorithm is based on reducing the k-ECSS problem to k set cover instances, in which we gradually augment the connectivity of the spanning subgraph. To solve each set cover instance, we combine new structural observations on minimum cuts with graph sketching ideas. One key ingredient in our algorithm is a novel structural lemma that allows us to compress the information about all minimum cuts in a graph into a succinct representation, which is computed in a decentralized fashion. We hope that this succinct representation may find applications in other computational settings or for other problems.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH164"}, {"primary_key": "1258919", "vector": [], "sparse_vector": [], "title": "Gap-ETH-Tight Approximation Schemes for Red-Green-Blue Separation and Bicolored Noncrossing Euclidean Travelling Salesman Tours.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we study problems of connecting classes of points via noncrossing structures. Given a set of colored terminal points, we want to find a graph for each color that connects all terminals of its color with the restriction that no two graphs cross each other. We consider these problems both on the Euclidean plane and in planar graphs.On the algorithmic side, we give a Gap-ETH-tight EPTAS for the bicolored noncrossing travelling salesman tours problem as well as for the red-blue-green separation problem (in which we want to separate terminals of three colors with two noncrossing polygons of minimum length), both on the Euclidean plane. This improves the work of <PERSON><PERSON><PERSON> and <PERSON> (ICALP 2003) who gave a slower PTAS for the simpler red-blue separation problem. For the case of unweighted plane graphs, we also show a PTAS for the bicolored noncrossing travelling salesman tours problem. All these results are based on our new patching procedure that might be of independent interest.On the negative side, we show that the problem of connecting terminal pairs with noncrossing paths is NP-hard on the Euclidean plane, and that the problem of finding two noncrossing spanning trees is NP-hard in plane graphs.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH52"}, {"primary_key": "1258920", "vector": [], "sparse_vector": [], "title": "Interactive Coding with Small Memory.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Gillat Kol", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this work, we design an interactive coding scheme that converts any two party interactive protocol Π into another interactive protocol Π', such that even if errors are introduced during the execution of Π', the parties are able to determine what the outcome of running Π would be in an error-free setting.Importantly, our scheme preserves the space complexity of the protocol, in addition to the communication and computational complexities. Specifically, if the protocol Π has communication complexity T, computational complexity t, and space complexity s, the resulting protocol Π' is resilient to a constant ε > 0 fraction of adversarial errors, and has communication complexity approaching T as ε approaches 0, computational complexity poly(t), and space complexity 𝒪(s log T).Prior to this work, all known interactive coding schemes required the parties to use at least Ω(T) space, as the parties were required to remember the transcript of the conversation thus far, or considered weaker error models.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH137"}, {"primary_key": "1258921", "vector": [], "sparse_vector": [], "title": "Optimal Square Detection Over General Alphabets.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Squares (fragments of the form xx, for some string x) are arguably the most natural type of repetition in strings. The basic algorithmic question concerning squares is to check if a given string of length n is square-free, that is, does not contain a fragment of such form. <PERSON> and <PERSON><PERSON> [<PERSON><PERSON> 1984] designed an 𝒪(n log n) time algorithm for this problem, and proved a matching lower bound assuming the so-called general alphabet, meaning that the algorithm is only allowed to check if two characters are equal. As an open question, they asked if there is a faster algorithm if one restricts the size of the alphabet. C<PERSON><PERSON>more [Theor. Comput. Sci. 1986] designed a linear-time algorithm for constant-size alphabets, and combined with the more recent results his approach in fact implies such an algorithm for linearly-sortable alphabets. Very recently, <PERSON><PERSON> and <PERSON> [ICALP 2021] significantly relaxed this assumption by designing a linear-time algorithm for general ordered alphabets, that is, assuming a linear order on the characters. However, the open question of <PERSON> and Lorentz from 1984 remained unresolved for general (unordered) alphabets. In this paper, we show that testing square-freeness of a length-n string over general alphabet of size σ can be done with 𝒪(n log σ) comparisons, and cannot be done with o(n log σ) comparisons. We complement this result with an 𝒪(n log σ) time algorithm in the Word RAM model.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH189"}, {"primary_key": "1258922", "vector": [], "sparse_vector": [], "title": "&quot;Who is Next in Line?&quot; On the Significance of Knowing the Arrival Order in Bayesian Online Settings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a new measure for the performance of online algorithms in Bayesian settings, where the input is drawn from a known prior, but the realizations are revealed one-by-one in an online fashion. Our new measure is called order-competitive ratio. It is defined as the worst case (over all distribution sequences) ratio between the performance of the best order-unaware and order-aware algorithms, and quantifies the loss that is incurred due to lack of knowledge of the arrival order. Despite the growing interest in the role of the arrival order on the performance of online algorithms, this loss has been overlooked thus far.We study the order-competitive ratio in the paradigmatic prophet inequality problem, for the two common objective functions of (i) maximizing the expected value, and (ii) maximizing the probability of obtaining the largest value; and with respect to two families of algorithms, namely (i) adaptive algorithms, and (ii) single-threshold algorithms. We provide tight bounds for all four combinations, with respect to deterministic algorithms. Our analysis requires new ideas and departs from standard techniques. In particular, our adaptive algorithms inevitably go beyond single-threshold algorithms. The results with respect to the order-competitive ratio measure capture the intuition that adaptive algorithms are stronger than single-threshold ones, and may lead to a better algorithmic advice than the classical competitive ratio measure.* This work is supported by Science and Technology Innovation 2030 –\"New Generation of Artificial Intelligence\" Major Project No.(2018AAA0100903), Innovation Program of Shanghai Municipal Education Commission, Program for Innovative Research Team of Shanghai University of Finance and Economics (IRTSHUFE) and the Fundamental Research Funds for the Central Universities. This project has received funding from the European Research Council (ERC) under the European Union's Horizon 2020 research and innovation program (grant agreement No. 866132), by the Israel Science Foundation (grant number 317/17), by an Amazon Research Award, and by the NSF-BSF (grant number 2020788). Tomer Ezra was partially supported by the ERC Advanced Grant 788893 AMDROMA \"Algorithmic and Mechanism Design Research in Online Markets\" and MIUR PRIN project ALGADIMAR \"Algorithms, Games, and Digital Markets\". Zhihao Gavin Tang is supported by NSFC grant 61902233. Nick Gravin is supported by NSFC grant 62150610500.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH145"}, {"primary_key": "1258923", "vector": [], "sparse_vector": [], "title": "Local Distributed Rounding: Generalized to MIS, Matching, Set Cover, and Beyond.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We develop a general deterministic distributed method for locally rounding fractional solutions of graph problems for which the analysis can be broken down into analyzing pairs of vertices. Roughly speaking, the method can transform fractional/probabilistic label assignments of the vertices into integral/deterministic label assignments for the vertices, while approximately preserving a potential function that is a linear combination of functions, each of which depends on at most two vertices (subject to some conditions usually satisfied in pairwise analyses). The method unifies and significantly generalizes prior work on deterministic local rounding techniques [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> FOCS'21; <PERSON>'19; <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>OCS'17; <PERSON>'17] to obtain polylogarithmic-time deterministic distributed solutions for combinatorial graph problems. Our general rounding result enables us to locally and efficiently derandomize a range of distributed algorithms for local graph problems, including maximal independent set (MIS), maximum-weight independent set approximation, and minimum-cost set cover approximation. As highlights, we in particular obtain the following results.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH168"}, {"primary_key": "1258924", "vector": [], "sparse_vector": [], "title": "Stronger Privacy Amplification by Shuffling for <PERSON><PERSON> and Approximate Differential Privacy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The shuffle model of differential privacy has gained significant interest as an intermediate trust model between the standard local and central models [18, 12]. A key result in this model is that randomly shuffling locally randomized data amplifies differential privacy guarantees. Such amplification implies substantially stronger privacy guarantees for systems in which data is contributed anonymously [8].", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH181"}, {"primary_key": "1258925", "vector": [], "sparse_vector": [], "title": "Simple Mechanisms for Non-linear Agents.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Yingkai Li"], "summary": "We show that economic conclusions derived from <PERSON><PERSON><PERSON> and <PERSON> (1989) for linear utility models approximately extend to non-linear utility models. Specifically, we quantify the extent to which agents with non-linear utilities resemble agents with linear utilities, and we show that the approximation of mechanisms for agents with linear utilities approximately extend for agents with non-linear utilities. We illustrate the framework for the objectives of revenue and welfare on non-linear models that include agents with budget constraints, agents with risk aversion, and agents with endogenous valuations. We derive bounds on how much these models resemble the linear utility model and combine these bounds with well-studied approximation results for linear utility models. We conclude that simple mechanisms are approximately optimal for these non-linear agent models.* The full version of the paper can be accessed at https://arxiv.org/abs/2003.00545. This work is support by NSF CCF AF #1618502.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH148"}, {"primary_key": "1258926", "vector": [], "sparse_vector": [], "title": "Fast Distributed Brooks&apos; Theorem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give a randomized Δ-coloring algorithm in the LOCAL model that runs in poly log log n rounds, where n is the number of nodes of the input graph and Δ is its maximum degree. This means that randomized Δ-coloring is a rare distributed coloring problem with an upper and lower bound in the same ballpark, poly log log n, given the known Ω(logΔ logn) lower bound [<PERSON> et al., STOC '16].", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH98"}, {"primary_key": "1258927", "vector": [], "sparse_vector": [], "title": "Tight Complexity Bounds for Counting Generalized Dominating Sets in Bounded-Treewidth Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>erney", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We investigate how efficiently a well-studied family of domination-type problems can be solved on bounded-treewidth graphs. For sets σ, ρ of non-negative integers, a (σ, ρ)-set of a graph G is a set S of vertices such that | N (u) ∩ S| ∈ σ for every u ∈ S, and | N (v) ∩ S| ∈ ρ for every v ∉ S. The problem of finding a (σ, ρ)-set (of a certain size) unifies standard problems such as INDEPENDENT SET, DOMINATING SET, INDEPENDENT DOMINATING SET, and many others.For all pairs of finite or cofinite sets (σ, ρ), we determine (under standard complexity assumptions) the best possible value cσ,ρ such that there is an algorithm that counts (σ, ρ)-sets in time ctwσ,ρ · nO(1) (if a tree decomposition of width tw is given in the input). Let stop denote the largest element of σ if σ is finite, or the largest missing integer +1 if σ is cofinite; rtop is defined analogously for ρ. Surprisingly, cσ,ρ is often significantly smaller than the natural bound stop + rtop + 2 achieved by existing algorithms [van <PERSON>, 2020]. Toward defining cσ,ρ, we say that (σ,ρ) is m-structured if there is a pair (α,β) such that every integer in σ equals α mod m, and every integer in ρ equals β mod m. Then, setting• cσ,ρ = stop + rtop +2 if (σ, ρ) is not m-structured for any m ≥ 2• cσ,ρ = max{stop,rtop} + 2 if (σ,ρ) is 2-structured, but not m-structured for any m ≥ 3, and stop = rtop is even, and• cσ,ρ = max{stop, rtop} + 1, otherwisewe provide algorithms counting (σ, ρ)-sets in time ctwσ,ρ · nO(1). For example, for the EXACT INDEPENDENT DOMINATING SET problem (also known as PERFECT CODE) corresponding to σ = {0} and ρ = {1}, this improves the 3tw · nO(1) algorithm of van Rooij to 2tw· nO(1).Despite the unusually delicate definition of cσ,ρ, we show that our algorithms are most likely optimal, i.e., for any pair (σ, ρ) of finite or cofinite sets where the problem is non-trivial, and any ε > 0, a (cσ,ρ — ε)tw · nO(1)- algorithm counting the number of (σ, ρ)-sets would violate the COUNTING STRONG EXPONENTIAL-TIME HYPOTHESIS (#SETH). For finite sets σ and ρ, our lower bounds also extend to the decision version, showing that our algorithms are optimal in this setting as well. In contrast, for many cofinite sets, we show that further significant improvements for the decision and optimization versions are possible using the technique of representative sets.* The full version of this work can be accessed at https://arxiv.org/abs/2211.04278. Research supported by the European Research Council (ERC) consolidator grant No. 725978 SYSTEMATICGRAPH.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH140"}, {"primary_key": "1258928", "vector": [], "sparse_vector": [], "title": "Shortest Cycles With Monotone Submodular Costs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce the following submodular generalization of the SHORTEST CYCLE problem. For a nonnegative monotone submodular cost function f defined on the edges (or the vertices) of an undirected graph G, we seek for a cycle C in G of minimum cost OPT = f(C). We give an algorithm that given an n-vertex graph G, parameter ε > 0, and the function f represented by an oracle, in time n𝒪(log 1/ε) finds a cycle C in G with f (C) ≤ (1 + ε) · OPT. This is in sharp contrast with the non-approximability of the closely related MONOTONE SUBMODULAR SHORTEST (s,t)-PATH problem, which requires exponentially many queries to the oracle for finding an n2//3-ε-approximation [<PERSON><PERSON> et al., FOCS 2009]. We complement our algorithm with a matching lower bound. We show that for every ε > 0, obtaining a (1 + ε)-approximation requires at least nΩ(log 1/ε) queries to the oracle.When the function f is integer-valued, our algorithm yields that a cycle of cost OPT can be found in time n𝒪(log OPT). In particular, for OPT = n𝒪(1) this gives a quasipolynomial-time algorithm computing a cycle of minimum submodular cost. Interestingly, while a quasipolynomial-time algorithm often serves as a good indication that a polynomial time complexity could be achieved, we show a lower bound that n𝒪(log n) queries are required even when OPT = 𝒪(n).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH83"}, {"primary_key": "1258929", "vector": [], "sparse_vector": [], "title": "Fixed-Parameter Tractability of Maximum Colored Path and Beyond.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a general method for obtaining fixed-parameter algorithms for problems about finding paths in undirected graphs, where the length of the path could be unbounded in the parameter. The first application of our method is a randomized algorithm, that given a colored n-vertex undirected graph, vertices s and t, and an integer k, finds an (s,t)-path containing at least k different colors in time 2kn𝒪(1). This is the first FPT algorithm for this problem, and it generalizes the algorithm of <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, and Taslaman [SODA 2012] on finding a path through k specified vertices. It also implies the first 2kn𝒪(1) time algorithm for finding an (s, t)-path of length at least k.Our method yields FPT algorithms for even more general problems. For example, we consider the problem where the input consists of an n-vertex undirected graph G, a matroid M whose elements correspond to the vertices of G and which is represented over a finite field of order q, a positive integer weight function on the vertices of G, two sets of vertices S, T ⊆ V (G), and integers p,k,w, and the task is to find p vertex-disjoint paths from S to T so that the union of the vertices of these paths contains an independent set of M of cardinality k and weight w, while minimizing the sum of the lengths of the paths. We give a 2p+𝒪(k2 log (q+k))n𝒪(1)w time randomized algorithm for this problem.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.07449. The research leading to these results has received funding from the Research Council of Norway via the project BWCA (grant no. 314528). Kirill Simonov acknowledges support by DFG Research Group ADYN under grant DFG 411362735. Giannos Stamoulis acknowledges support by the ANR project ESIGMA (ANR-17-CE23-0010) and the French-German Collaboration ANR/DFG Project UTMA (ANR-20-CE92-0027).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH142"}, {"primary_key": "1258930", "vector": [], "sparse_vector": [], "title": "Shrunk subspaces via operator <PERSON>kh<PERSON> iteration.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A recent breakthrough in <PERSON><PERSON>' problem showed that the noncommutative rank can be computed in deterministic polynomial time, and various algorithms for it were devised. However, only quite complicated algorithms are known for finding a so-called shrunk subspace, which acts as a dual certificate for the value of the noncommutative rank. In particular, the operator <PERSON><PERSON><PERSON> algorithm, perhaps the simplest algorithm to compute the noncommutative rank with operator scaling, does not find a shrunk subspace. Finding a shrunk subspace plays a key role in applications, such as separation in the Brascamp-Lieb polytope, one-parameter subgroups in the null-cone membership problem, and primal-dual algorithms for matroid intersection and fractional matroid matching.In this paper, we provide a simple Sinkhorn-style algorithm to find the smallest shrunk subspace over the complex field in deterministic polynomial time. To this end, we introduce a generalization of the operator scaling problem, where the spectra of the marginals must be majorized by specified vectors. Then we design an efficient Sinkhorn-style algorithm for the generalized operator scaling problem. Applying this to the shrunk subspace problem, we show that a sufficiently long run of the algorithm also finds an approximate shrunk subspace close to the minimum exact shrunk subspace. Finally, we show that the approximate shrunk subspace can be rounded if it is sufficiently close. Along the way, we also provide a simple randomized algorithm to find the smallest shrunk subspace.As applications, we design a faster algorithm for fractional linear matroid matching and efficient weak membership and optimization algorithms for the rank-2 Brascamp-Lieb polytope.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.08311", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH62"}, {"primary_key": "1258931", "vector": [], "sparse_vector": [], "title": "Subexponential mixing for partition chains on grid-like graphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the problem of generating uniformly random partitions of the vertex set of a graph such that every piece induces a connected subgraph. For the case where we want to have partitions with linearly many pieces of bounded size, we obtain approximate sampling algorithms based on Glauber dynamics which are fixed-parameter tractable with respect to the bandwidth of G, with simple-exponential dependence on the bandwidth. For example, for rectangles of constant or logarithmic width this gives polynomial-time sampling algorithms. More generally, this gives sub-exponential algorithms for bounded-degree graphs without large expander sub-graphs (for example, we obtain time algorithms for square grids).In the case where we instead want partitions with a small number of pieces of linear size, we show that Glauber dynamics can have exponential mixing time, even just for the case of 2 pieces, and even for 2-connected sub-graphs of the grid with bounded bandwidth.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH127"}, {"primary_key": "1258932", "vector": [], "sparse_vector": [], "title": "Graph Classes with Few Minimal Separators. I. Finite Forbidden Induced Subgraphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A vertex set S in a graph G is a minimal separator if there exist vertices u and v that are in distinct connected components of G — S, but in the same connected component of G — S' for every S' ⊂ S. A class F of graphs is called tame if there exists a constant c so that every graph in F on n vertices contains at most O(nc) minimal separators. If there exists a constant c so that every graph in F on n vertices contains at most O(nclog n) minimal separators the class is strongly-quasi-tame. If there exists a constant c > 1 so that F contains n-vertex graphs with at least cn minimal separators for arbitrarily large n then F is called feral. The classification of graph classes into tame or feral has numerous algorithmic consequences, and has recently received considerable attention.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH119"}, {"primary_key": "1258933", "vector": [], "sparse_vector": [], "title": "Graph Classes with Few Minimal Separators. II. A Dichotomy.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A class F of graphs is called tame if every graph in F on n vertices contains at most nO(1) minimal separators, quasi-tame if every graph in F on n vertices contains at most 2logO(1)(n) minimal separators, and feral if there exists a constant c > 1 so that F contains n-vertex graphs with at least cn minimal separators for arbitrarily large n. The classification of graph classes into (quasi-) tame or feral has numerous algorithmic consequences, and has recently received considerable attention.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH120"}, {"primary_key": "1258934", "vector": [], "sparse_vector": [], "title": "Faster Computation of 3-Edge-Connected Components in Digraphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an Õ(m3/2) time randomized (<PERSON>) algorithm for computing the 3-edge-connected components of a digraph with m edges and n vertices. This constitutes the first improvement since the algorithm of Nagamochi & Watanabe from 1993, which runs in O(m · n) time. Thus, our algorithm is the first that overcomes the run-time of O(n) computations of 3-bounded max-flows (that is, computations of the value min{Flow(s,t), 3} for O(n) pairs s-t).Our algorithm involves a combination of known and new techniques together with new structural insights on the interactions between directed min-cuts. One novel aspect that we introduce is an efficient graph operation G for replacing a set of vertices S that is disconnected from V\\S by an edge-cut of size 2 (2-out set), with a gadget of small size that preserves the pairwise connectivity among the vertices of V\\S. Another main ingredient of our approach is an extension of the framework for computing the vertex-connectivity (or edge-connectivity) in a digraph [<PERSON><PERSON> et al., STOC'19]. This extension allows us to efficiently identify either all small 2-out sets of vertices, or identify enough 2-out sets whose total internal volume is a constant fraction of the edges of the graph. Repeatedly replacing each identified 2-out set S with a small gadget (using the G and G operations) either shrinks the size of the graph by a constant fraction, or concludes that no small 2-out set exists. We believe that our techniques may be of independent interest. Finally, we augment our algorithm with a data structure that can report in constant time the edges of some edge-cut of size at most 2 that disconnects any two query vertices u,v, or report in constant time that no such edge-cut exists.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH96"}, {"primary_key": "1258935", "vector": [], "sparse_vector": [], "title": "Spatial mixing and the random-cluster dynamics on lattices.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "An important paradigm in the understanding of mixing times of Glauber dynamics for spin systems is the correspondence between spatial mixing properties of the models and bounds on the mixing time of the dynamics. This includes, in particular, the classical notions of weak and strong spatial mixing, which have been used to show the best known mixing time bounds in the high-temperature regime for the G<PERSON>ber dynamics for the Ising and <PERSON>tts models.Glauber dynamics for the random-cluster model does not naturally fit into this spin systems framework because its transition rules are not local. In this paper, we present various implications between weak spatial mixing, strong spatial mixing, and the newer notion of spatial mixing within a phase, and mixing time bounds for the random-cluster dynamics in finite subsets of ℤd for general d  2. These imply a host of new results, including optimal O(N log N) mixing for the random cluster dynamics on torii and boxes on N vertices in ℤd at all high temperatures and at sufficiently low temperatures, and for large values of q quasi-polynomial (or quasi-linear when d = 2) mixing time bounds from random phase initializations on torii at the critical point (where by contrast the mixing time from worst-case initializations is exponentially large). In the same parameter regimes, these results translate to fast sampling algorithms for the <PERSON>tts model on ℤd for general d.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.11195", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH174"}, {"primary_key": "1258936", "vector": [], "sparse_vector": [], "title": "Excluding Single-Crossing Matching Minors in Bipartite Graphs.", "authors": ["Archontia <PERSON>", "Dimitrios M. Thilikos", "<PERSON>"], "summary": "By a seminal result of Val<PERSON>, computing the permanent of (0,1)-matrices is, in general, #P-hard. In 1913 <PERSON><PERSON> asked for which (0,1)-matrices A it is possible to change some signs such that the permanent of A equals the determinant of the resulting matrix. In 1975, <PERSON> showed these matrices to be exactly the biadjacency matrices of bipartite graphs excluding K3,3 as a matching minor. This was turned into a polynomial time algorithm by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> in 1999. However, the relation between the exclusion of some matching minor in a bipartite graph and the tractability of the permanent extends beyond K3,3. Recently it was shown that the exclusion of any planar bipartite graph as a matching minor yields a class of bipartite graphs on which the permanent of the corresponding (0,1)-matrices can be computed efficiently. In this paper we unify the two results above into a single, more general result in the style of the celebrated structure theorem for single-crossing-minor-free graphs. We identify a class of bipartite graphs strictly generalising planar bipartite graphs and K3,3 which includes infinitely many non-Pfaffian graphs. The exclusion of any member of this class as a matching minor yields a structure that allows for the efficient evaluation of the permanent. Moreover, we show that the evaluation of the permanent remains #P-hard on bipartite graphs which exclude K5,5 as a matching minor. This establishes a first computational lower bound for the problem of counting perfect matchings on matching minor closed classes. As another application of our structure theorem, we obtain a strict generalisation of the algorithm for the k-vertex disjoint directed paths problem on digraphs of bounded directed treewidth.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH81"}, {"primary_key": "1258937", "vector": [], "sparse_vector": [], "title": "Conflict-free hypergraph matchings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A celebrated theorem of <PERSON><PERSON><PERSON>, and <PERSON><PERSON> and <PERSON><PERSON> states that every almost-regular, uniform hypergraph H with small maximum codegree has an almost-perfect matching. We extend this result by obtaining a conflict-free matching, where conflicts are encoded via a collection C of subsets C ⊆ E (H). We say that a matching M ⊆ E (H) is conflict-free if M does not contain an element of C as a subset. Under natural assumptions on C, we prove that H has a conflict-free, almost-perfect matching. This has many applications, one of which yields new asymptotic results for so-called “high-girth” Steiner systems. Our main tool is a polynomial time random greedy algorithm which we call the “conflict-free matching process”.* The full version of the paper can be accessed at https://arxiv.org/abs/2205.05564", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH115"}, {"primary_key": "1258938", "vector": [], "sparse_vector": [], "title": "Instability of backoff protocols with arbitrary arrival rates.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In contention resolution, multiple processors are trying to coordinate to send discrete messages through a shared channel with sharply limited communication. If two processors inadvertently send at the same time, the messages collide and are not transmitted successfully. An important case is acknowledgement-based contention resolution, in which processors cannot listen to the channel at all; all they know is whether or not their own messages have got through. This situation arises frequently in both networking and cloud computing. The most common acknowledgement-based protocols in practice are backoff protocols — variants of binary exponential backoff are used in both Ethernet and TCP/IP, and both Google Drive and AWS instruct their users to implement it to handle busy periods.In queueing models, where each processor has a queue of messages, stable backoff protocols are already known (<PERSON><PERSON><PERSON> et al., SICOMP 1996). In queue-free models, where each processor has a single message but processors arrive randomly, it is a long-standing conjecture of <PERSON><PERSON><PERSON> (IEEE Trans. Inf. Theory 1987) that no stable backoff protocols exist for any positive arrival rate of processors. Despite exciting recent results for full-sensing protocols which assume far greater listening capabilities of the processors (see e.g. <PERSON> et al. STOC 2020 or <PERSON> et al. PODC 2021), this foundational question remains open; here instability is only known in general when the arrival rate of processors is at least 0.42 (<PERSON> et al. <PERSON>ICOMP 2004). We prove <PERSON><PERSON><PERSON>'s conjecture for all backoff protocols outside of a tightly-constrained special case using a new domination technique to get around the main difficulty, which is the strong dependencies between messages.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH131"}, {"primary_key": "1258939", "vector": [], "sparse_vector": [], "title": "Weak Bisimulation Finiteness of Pushdown Systems With Deterministic ε-Transitions Is 2-EXPTIME-Complete.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of deciding whether a given pushdown system all of whose ε-transitions are deterministic is weakly bisimulation finite, that is, whether it is weakly bisimulation equivalent to a finite system. We prove that this problem is 2-EXPTIME-complete. This consists of three elements: First, we prove that the smallest finite system that is weakly bisimulation equivalent to a fixed pushdown system, if exists, has size at most doubly exponential in the description size of the pushdown system. Second, we propose a fast algorithm deciding whether a given pushdown system is weakly bisimulation equivalent to a finite system of a given size. Third, we prove 2-EXPTIME-hardness of the problem. The problem was known to be decidable, but the previous algorithm had Ackermannian complexity (6-EXPSPACE in the easier case of pushdown systems without ε-transitions); concerning lower bounds, only EXPTIME-hardness was known.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH105"}, {"primary_key": "1258940", "vector": [], "sparse_vector": [], "title": "Model-Checking for First-Order Logic with Disjoint Paths Predicates in Proper Minor-Closed Graph Classes.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dimitrios M. Thilikos"], "summary": "The disjoint paths logic, FOL+DP, is an extension of First-Order Logic (FOL) with the extra atomic predicate dpk(x1,y1,…, xk,yk), expressing the existence of internally vertex-disjoint paths between Xi and yi, for i ∈ {1,…, k}. This logic can express a wide variety of problems that escape the expressibility potential of FOL. We prove that for every proper minor-closed graph class, model-checking for FOL+DP can be done in quadratic time. We also introduce an extension of FOL+DP, namely the scattered disjoint paths logic, FOL+SDP, where we further consider the atomic predicate s-sdpk(x1,y1,…,xk,yk), demanding that the disjoint paths are within distance bigger than some fixed value s. Using the same technique we prove that model-checking for FOL+SDP can be done in quadratic time on classes of graphs with bounded Euler genus.* The full version of the paper can be accessed at https://arxiv.org/abs/2211.01723", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH141"}, {"primary_key": "1258941", "vector": [], "sparse_vector": [], "title": "Private Convex Optimization in General Norms.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a new framework for differentially private optimization of convex functions which are Lipschitz in an arbitrary norm ||·||x. Our algorithms are based on a regularized exponential mechanism which samples from the density ∞ exp(-k(F + μr)) where F is the empirical loss and τ is a regularizer which is strongly convex with respect to ||·||x, generalizing a recent work of [GLL22] to non-Euclidean settings. We show that this mechanism satisfies Gaussian differential privacy and solves both DP-ERM (empirical risk minimization) and DP-SCO (stochastic convex optimization), by using localization tools from convex geometry. Our framework is the first to apply to private convex optimization in general normed spaces, and directly recovers non-private SCO rates achieved by mirror descent, as the privacy parameter ε → ∞. As applications, for Lipschitz optimization in ℓp norms for all p ∈ (1, 2), we obtain the first optimal privacy-utility tradeoffs; for p = 1, we improve tradeoffs obtained by the recent works [AFKT21, BGN21] by at least a logarithmic factor. Our ℓp norm and Schatten-p norm optimization frameworks are complemented with polynomial-time samplers whose query complexity we explicitly bound.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH185"}, {"primary_key": "1258942", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Exact Edge Connectivity in Sublinear Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Danupon <PERSON>", "Thatchaphol <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given a simple n-vertex, m-edge graph G undergoing edge insertions and deletions, we give two new fully dynamic algorithms for exactly maintaining the edge connectivity of G in Õ(n) worst-case update time and Õ(m1-1/16) amortized update time, respectively. Prior to our work, all dynamic edge connectivity algorithms assumed bounded edge connectivity, guaranteed approximate solutions, or were restricted to edge insertions only. Our results answer in the affirmative an open question posed by <PERSON><PERSON> [Combinatorica'07].", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH3"}, {"primary_key": "1258943", "vector": [], "sparse_vector": [], "title": "Fair allocation of a multiset of indivisible items.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the problem of fairly allocating a multiset M of m indivisible items among n agents with additive valuations. Specifically, we introduce a parameter t for the number of distinct types of items and study fair allocations of multisets that contain only items of these t types, under two standard notions of fairness:1. Envy-freeness (EF): For arbitrary n, t, we show that a complete EF allocation exists when at least one agent has a unique valuation and the number of items of each type exceeds a particular finite threshold. We give explicit upper and lower bounds on this threshold in some special cases.2. Envy-freeness up to any good (EFX): For arbitrary n, m, and for t ≤ 2, we show that a complete EFX allocation always exists. We give two different proofs of this result. One proof is constructive and runs in polynomial time; the other is geometrically inspired.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH13"}, {"primary_key": "1258944", "vector": [], "sparse_vector": [], "title": "Improved Bi-point Rounding Algorithms and a Golden Barrier for k-Median.", "authors": ["Kishen N. Gowda", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The current best approximation algorithms for k-median rely on first obtaining a structured fractional solution known as a bi-point solution, and then rounding it to an integer solution. We improve this second step by unifying and refining previous approaches. We describe a hierarchy of increasingly-complex partitioning schemes for the facilities, along with corresponding sets of algorithms and factor-revealing non-linear programs. We prove that the third layer of this hierarchy is a 2.613-approximation, improving upon the current best ratio of 2.675, while no layer can be proved better than 2.588 under the proposed analysis.On the negative side, we give a family of bi-point solutions which cannot be approximated better than the square root of the golden ratio, even if allowed to open k + o(k) facilities. This gives a barrier to current approaches for obtaining an approximation better than . Altogether we reduce the approximation gap of bi-point solutions by two thirds.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH38"}, {"primary_key": "1258945", "vector": [], "sparse_vector": [], "title": "A Nearly Tight Analysis of Greedy k-means++.", "authors": ["<PERSON>", "Ahmet Alper Özüdogru", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The famous k-means++ algorithm of <PERSON> and <PERSON> [SODA 2007] is the most popular way of solving the k-means problem in practice. The algorithm is very simple: it samples the first center uniformly at random and each of the following k — 1 centers is then always sampled proportional to its squared distance to the closest center so far. Afterward, <PERSON>'s iterative algorithm is run. The k-means++ algorithm is known to return Θ(log k) approximate solution in expectation. In their seminal work, <PERSON> and V<PERSON>ilvitskii [SODA 2007] asked about the guarantees for its following greedy variant: in every step, we sample ℓ candidate centers instead of one and then pick the one that minimizes the new cost. This is also how k-means++ is implemented in e.g. the popular Scikit-learn library [<PERSON><PERSON><PERSON><PERSON><PERSON> et al.; JMLR 2011]. We present nearly matching lower and upper bounds for the greedy k-means++: We prove that it is an O(ℓ3 log3 k)-approximation algorithm. On the other hand, we prove a lower bound of Ω(ℓ3 log3 k/ log2 (ℓ log k)). Previously, only an Ω(ℓ log k) lower bound was known [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>; ESA 2020] and there was no known upper bound.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH39"}, {"primary_key": "1258946", "vector": [], "sparse_vector": [], "title": "Map matching queries on realistic input graphs under the <PERSON><PERSON><PERSON> distance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Map matching is a common preprocessing step for analysing vehicle trajectories. In the theory community, the most popular approach for map matching is to compute a path on the road network that is the most spatially similar to the trajectory, where spatial similarity is measured using the Fréchet distance. A shortcoming of existing map matching algorithms under the Fréchet distance is that every time a trajectory is matched, the entire road network needs to be reprocessed from scratch. An open problem is whether one can preprocess the road network into a data structure, so that map matching queries can be answered in sublinear time.In this paper, we investigate map matching queries under the Fréchet distance. We provide a negative result for geometric planar graphs. We show that, unless SETH fails, there is no data structure that can be constructed in polynomial time that answers map matching queries in O((pq)1-δ) query time for any δ > 0, where p and q are the complexities of the geometric planar graph and the query trajectory, respectively. We provide a positive result for realistic input graphs, which we regard as the main result of this paper. We show that for c-packed graphs, one can construct a data structure of Õ(cp) size that can answer (1 + ε)-approximate map matching queries in Õ(c4q log4p) time, where Õ(·) hides lower-order factors and dependence of ε.* The full version of the paper can be accessed at https://arxiv.org/abs/2211.02951", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH53"}, {"primary_key": "1258947", "vector": [], "sparse_vector": [], "title": "Halving by a Thousand Cuts or Punctures.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "For point sets P1,…, Pk, a set of lines L is halving if any face of the arrangement A(L) contains at most |Pi|/2 points of Pi, for all i. We study the problem of computing a halving set of lines of minimal size. Surprisingly, we show a polynomial time algorithm that outputs a halving set of size O(ø3/2), where θ is the size of the optimal solution - this is of interest when ø = o(log2 n). Our solution relies on solving a new variant of the weak ε-net problem for corridors, which we believe to be of independent interest.We also study other variants of this problem, including an alternative \"dual\" settings, where one needs to introduce a set of guards (i.e., points), such that no convex set avoiding the guards contains more than half the points of each point set.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH49"}, {"primary_key": "1258948", "vector": [], "sparse_vector": [], "title": "Fixed-parameter tractability of DIRECTED MULTICUT with three terminal pairs parameterized by the size of the cutset: twin-width meets flow-augmentation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Paloma T. Lima", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show fixed-parameter tractability of the DIRECTED MULTICUT problem with three terminal pairs (with a randomized algorithm). In this problem we are given a directed graph G, three pairs of vertices (called terminals) (s1, t1), (s2, t2), (s3, t3), and an integer k and we want to find a set of at most k non-terminal vertices in G that intersect all s1t1-paths, all s2t2-paths, and all s3t3-paths. The parameterized complexity of this problem has been open since <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON> proved fixed-parameter tractability of the two-terminal-pairs case at SODA 2012, and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> proved the W[1]-hardness of the four-terminal-pairs case at SODA 2016.On the technical side, we use two recent developments in parameterized algorithms. Using the technique of directed flow-augmentation [<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, STOC 2022] we cast the problem as a CSP problem with few variables and constraints over a large ordered domain. We observe that this problem can be in turn encoded as an FO model-checking task over a structure consisting of a few 0-1 matrices. We look at this problem through the lenses of twin-width, a recently introduced structural parameter [<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, FOCS 2020]: By a recent characterization [<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, ST<PERSON> 2022] the said FO model-checking task can be done in FPT time if the said matrices have bounded grid rank. To complete the proof, we show an irrelevant vertex rule: If any of the matrices in the said encoding has a large grid minor, a vertex corresponding to the \"middle\" box in the grid minor can be proclaimed irrelevant — not contained in the sought solution — and thus reduced.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.07425. The research leading to the results presented in this paper was partially carried out during the Parameterized Algorithms Retreat of the University of Warsaw, PARUW 2022, held in Bedlewo in April 2022. This research is a part of projects that have received funding from the European Research Council (ERC) under the European Union's Horizon 2020 research and innovation programme Grant Agreement 714704 (TM, MP) and 648527 (MH), from the Alexander von Humboldt Foundation (MS), from the Research Council of Norway (LJ), and by the Federal Ministry of Education and Research (BMBF) and by a fellowship within the IFI programme of the German Academic Exchange Service (DAAD). (MH).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH123"}, {"primary_key": "1258949", "vector": [], "sparse_vector": [], "title": "Improved Integrality Gap in Max-Min Allocation: or Topology at the North Pole.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the max-min allocation problem a set P of players are to be allocated disjoint subsets of a set R of indivisible resources, such that the minimum utility among all players is maximized. We study the restricted variant, also known as the Santa Claus problem, where each resource has an intrinsic positive value, and each player covets a subset of the resources. <PERSON><PERSON><PERSON> and <PERSON> [15] showed that this problem is NP-hard to approximate within a factor less than 2, consequently a great deal of work has focused on approximate solutions. The principal approach for obtaining approximation algorithms has been via the Configuration LP (CLP) of Bansal and Sviridenko [12]. Accordingly, there has been much interest in bounding the integrality gap of this CLP. The existing algorithms and integrality gap estimations are all based one way or another on the combinatorial augmenting tree argument of <PERSON>xell [26] for finding perfect matchings in certain hypergraphs.Our main innovation in this paper is to introduce the use of topological methods, to replace the combinatorial argument of [26] for the restricted max-min allocation problem. This approach yields substantial improvements in the integrality gap of the CLP. In particular we improve the previously best known bound of 3.808 to 3.534. We also study the (1, ε)-restricted version, in which resources can take only two values, and improve the integrality gap in most cases. Our approach applies a criterion of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, for the existence of independent transversals in graphs, which involves the connectedness of the independence complex. This is complemented by a graph process of <PERSON><PERSON><PERSON> that decreases the connectedness of the independence complex in a controlled fashion and hence, tailored appropriately to the problem, can verify the criterion. In our applications we aim to establish the flexibility of the approach and hence argue for it to be a potential asset in other optimization problems involving hypergraph matchings.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH109"}, {"primary_key": "1258950", "vector": [], "sparse_vector": [], "title": "Almost Tight Error Bounds on Differentially Private Continual Counting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The first large-scale deployment of private federated learning uses differentially private counting in the continual release model as a subroutine (Google AI blog titled “Federated Learning with Formal Differential Privacy Guarantees” on February 28, 2022). For this and several other applications, it is crucial to use a continual counting mechanism with small mean squared error. In this case, a concrete (or non-asymptotic) bound on the error is very relevant to reduce the privacy parameter ε as much as possible, and hence, it is important to improve upon the constant factor in the error term. The standard mechanism for continual counting, and the one used in the above deployment, is the binary mechanism. We present a novel mechanism and show that its mean squared error is both asymptotically optimal and a factor 10 smaller than the error of the binary mechanism. We also show that the constants in our analysis are almost tight by giving non-asymptotic lower and upper bounds that differ only in the constants of lower-order terms. Our mechanism also has the advantage of taking only constant time per release, while the binary mechanism takes O(log n) time, where n is the total number of released data values. Our algorithm is a matrix mechanism for the counting matrix. We also use our explicit factorization of the counting matrix to give an upper bound on the excess risk of the matrix mechanism-based private learning algorithm of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> (NeurIPS 2022).Our lower bound for any continual counting mechanism is the first tight lower bound on continual counting under (ε, δ) -differential privacy and it holds against a non-adaptive adversary. It is achieved using a new lower bound on a certain factorization norm, denoted by γ f (·), in terms of the singular values of the matrix. In particular, we show that for any complex matrix, A ∊ℂm × n,where ||·|| denotes the Schatten-1 norm. We believe this technique will be useful in proving lower bounds for a larger class of linear queries. To illustrate the power of this technique, we show the first lower bound on the mean squared error for answering parity queries. This bound applies to the non-continual setting and is asymptotically tight.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH183"}, {"primary_key": "1258951", "vector": [], "sparse_vector": [], "title": "A Polynomial Time Algorithm for Finding a Minimum 4-Partition of a Submodular Function.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Ke Shi", "<PERSON>"], "summary": "In this paper, we study the minimum k-partition problem of submodular functions, i.e., given a finite set V and a submodular function f: 2V → ℝ, computing a k-partition {V1,…, Vk} of V with minimum . The problem is a natural generalization of the minimum k-cut problem in graphs and hypergraphs. It is known that the problem is NP-hard for general k, and solvable in polynomial time for k ≤ 3. In this paper, we construct the first polynomial-time algorithm for the minimum 4-partition problem.* Authors are ordered alphabetically.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH64"}, {"primary_key": "1258952", "vector": [], "sparse_vector": [], "title": "Massively Parallel Computation on Embedded Planar Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many of the classic graph problems cannot be solved in the Massively Parallel Computation setting (MPC) with strongly sublinear space per machine and o(logn) rounds, unless the 1-vs-2 cycles conjecture is false. This is true even on planar graphs. Such problems include, for example, counting connected components, bipartition, minimum spanning tree problem, (approximate) shortest paths, and (approximate) diameter/radius. In this paper, we show a way to get around this limitation. Specifically, we show that if we have a ``nice'' (for example, straight-line) embedding of the input graph, all the mentioned problems can be solved with O(n2/3+ϵ) space per machine in O(1) rounds. In conjunction with existing algorithms for computing the Delaunay triangulation, our results imply an MPC algorithm for exact Euclidean minimum spanning thee (EMST) that uses O(n2/3+ϵ) space per machine and finishes in O(1) rounds. This is the first improvement over a straightforward use of the standard <PERSON><PERSON><PERSON><PERSON><PERSON>'s algorithm with the Dauleanay triangulation algorithm of Goodrich [SODA 1997] which results in Θ(logn) rounds. This also partially negatively answers a question of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> [STOC 2014], asking for lower bounds for exact EMST. We extend our algorithms to work with embeddings consisting of curves that are not ``too squiggly\" (as formalized by the total absolute curvature). We do this via a new lemma which we believe is of independent interest and could be used to parameterize other geometric problems by the total absolute curvature. We also state several open problems regarding massively parallel computation on planar graphs.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH167"}, {"primary_key": "1258953", "vector": [], "sparse_vector": [], "title": "A Subquadratic nε-approximation for the Continuous Fréchet Distance.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Fréchet distance is a commonly used similarity measure between curves. It is known how to compute the continuous Fréchet distance between two polylines with m and n vertices in ℝd in O(mn(log log n)2) time; doing so in strongly subquadratic time is a longstanding open problem. Recent conditional lower bounds suggest that it is unlikely that a strongly subquadratic algorithm exists. Moreover, it is unlikely that we can approximate the Fréchet distance to within a factor 3 in strongly subquadratic time, even if d = 1. The best current results establish a tradeoff between approximation quality and running time. Specifically, <PERSON><PERSON><PERSON> and <PERSON> (SoCG, 2021) give an O(α)-approximate algorithm that runs in O((n3/α2) log n) time for any , assuming m ≤ n. In this paper, we improve this result with an O(α)-approximate algorithm that runs in O((n + mn/α) log3 n) time for any α ∈ [1, n], assuming m ≤ n and constant dimension d.* The full version of the paper can be accessed at https://arxiv.org/abs/2208.12721", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH67"}, {"primary_key": "1258954", "vector": [], "sparse_vector": [], "title": "A simple and sharper proof of the hypergraph <PERSON> bound.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The hypergraph Moore bound characterizes the extremal trade-off between the girth — the number of hyperedges in the smallest cycle or even cover (a subhypergraph with all degrees even) and size — the number of hyperedges in a hypergraph. For graphs, a bound tight up to the leading constant was proven in a classical work of <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> [3]. For hypergraphs of uniformity k > 2, an appropriate generalization was conjectured by <PERSON><PERSON> [14]. The conjecture was settled up to an additional log4k+1 n factor in the size in a recent work of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [16]. Their argument relies on a connection between the existence of short even covers and the spectrum of a certain randomly signed Kikuchi matrix. Their analysis, especially for the case of odd k, is significantly complicated.In this work, we present a substantially simpler and shorter proof of the hypergraph Moore bound. Our key idea is the use of a new reweighted Kikuchi matrix and an edge deletion trick that allows us to drop several involved steps in [16]'s analysis such as combinatorial bucketing of rows of the Kikuchi matrix and the use of the Schudy-<PERSON>vir<PERSON> polynomial concentration. Our simpler proof also obtains tighter parameters: in particular, the argument gives a new proof of the classical Moore bound of [3] with no loss (the proof in [16] loses a log3n factor), and loses only a single logarithmic factor for all k > 2-uniform hypergraphs.As in [16], our ideas naturally extend to yield a simpler proof of the full trade-off for strongly refuting smoothed instances of constraint satisfaction problems with similarly improved parameters.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.10850", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH89"}, {"primary_key": "1258955", "vector": [], "sparse_vector": [], "title": "Query Complexity of Inversion Minimization on Trees.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the following computational problem: Given a rooted tree and a ranking of its leaves, what is the minimum number of inversions of the leaves that can be attained by ordering the tree? This variation of the well-known problem of counting inversions in arrays originated in mathematical psychology. It has the evaluation of the Mann-Whitney statistic for detecting differences between distributions as a special case.We study the complexity of the problem in the comparison-query model, the standard model for problems like sorting, selection, and heap construction. The complexity depends heavily on the shape of the tree: for trees of unit depth, the problem is trivial; for many other shapes, we establish lower bounds close to the strongest known in the model, namely the lower bound of log2(n!) for sorting n items. For trees with n leaves we show, in increasing order of closeness to the sorting lower bound:(a) log2((α(1 — α)n)!) — O(log n) queries are needed whenever the tree has a subtree that contains a fraction α of the leaves. This implies a lower bound of for trees of degree k.(b) log2(n!) — O(log n) queries are needed in case the tree is binary.(c) log2(n!) — O(k log k) queries are needed for certain classes of trees of degree k, including perfect trees with even k.The lower bounds are obtained by developing two novel techniques for a generic problem Π in the comparison-query model and applying them to inversion minimization on trees. Both techniques can be described in terms of the <PERSON><PERSON><PERSON> graph of the symmetric group with adjacent-rank transpositions as the generating set, or equivalently, in terms of the edge graph of the permutahedron, the polytope spanned by all permutations of the vector (1, 2,…, n). Consider the subgraph consisting of the edges between vertices with the same value under Π. We show that the size of any decision tree for Π must be at least:(i) the number of connected components of this subgraph, and(ii) the factorial of the average degree of the complementary subgraph, divided by n.Lower bounds on query complexity then follow by taking the base-2 logarithm. Technique (i) represents a discrete analog of a classical technique in algebraic complexity and allows us to establish (c) and a tight lower bound for counting cross inversions, as well as unify several of the known lower bounds in the comparison-query model. Technique (ii) represents an analog of sensitivity arguments in Boolean complexity and allows us to establish (a) and (b).Along the way to proving (b), we derive a tight upper bound on the maximum probability of the distribution of cross inversions, which is the distribution of the Mann-Whitney statistic in the case of the null hypothesis. Up to normalization the probabilities alternately appear in the literature as the coefficients of polynomials formed by the Gaussian binomial coefficients, also known as Gaussian polynomials.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH107"}, {"primary_key": "1258956", "vector": [], "sparse_vector": [], "title": "Maintaining Expander Decompositions via Sparse Cuts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this article, we show that the algorithm of maintaining expander decompositions in graphs undergoing edge deletions directly by removing sparse cuts repeatedly can be made efficient.Formally, for an m-edge undirected graph G, we say a cut is ϕ-sparse if . A ϕ-expander decomposition of G is a partition of V into sets X1,X2,…, Xk such that each cluster G[X1] contains no ϕ-sparse cut (meaning it is a ϕ-expander) with Õ(ϕm) edges crossing between clusters. A natural way to compute a ϕ-expander decomposition is to decompose clusters by ϕ-sparse cuts until no such cut is contained in any cluster. We show that even in graphs undergoing edge deletions, a slight relaxation of this meta-algorithm can be implemented efficiently with amortized update time mo(1)/ϕ2.Our approach naturally extends to maintaining directed ϕ-expander decompositions and ϕ-expander hierarchies and thus gives a unifying framework while having simpler proofs than previous state-of-the-art work. In all settings, our algorithm matches the run-times of previous algorithms up to subpolynomial factors. Moreover, our algorithm provides stronger guarantees for ϕ-expander decompositions. For example, for graphs undergoing edge deletions, our approach is the first to maintain a dynamic expander decomposition where each updated decomposition is a refinement of the previous decomposition, and our approach is the first to guarantee a sublinear ϕm1+ο(1) bound on the total number of edges that cross between clusters across the entire sequence of dynamic updates. Our techniques also give by far the simplest, deterministic algorithms for maintaining Strongly-Connected Components (SCCs) in directed graphs undergoing edge deletions, and for maintaining connectivity in undirected fully-dynamic graphs, both matching the current state-of-the art run-times up to subpolynomial factors.* The full version of the paper can be accessed at https://arxiv.org/abs/2204.02519", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH2"}, {"primary_key": "1258957", "vector": [], "sparse_vector": [], "title": "Byzantine Agreement with Optimal Resilience via Statistical Fraud Detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Since the mid-1980s it has been known that Byzantine Agreement can be solved with probability 1 asynchronously, even against an omniscient, computationally unbounded adversary that can adaptively corrupt up to f < n/3 parties. Moreover, the problem is insoluble with f ≥ n/3 corruptions. However, <PERSON><PERSON><PERSON>'s [Bra87] 1984 protocol (see also Ben-Or [Ben83]) achieved f < n/3 resilience at the cost of exponential expected latency 2θ(n), a bound that has never been improved in this model with f = ⌊(n- 1)/3⌋ corruptions.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH165"}, {"primary_key": "1258958", "vector": [], "sparse_vector": [], "title": "Unique Games hardness of Quantum Max-Cut, and a conjectured vector-valued Borell&apos;s inequality.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Gaussian noise stability of a function f: ℝn → {-1,1} is the expected value of f (x) · f (y) over ρ-correlated Gaussian random variables x and y. <PERSON><PERSON>'s inequality states that for —1 ≤ ρ ≤ 0, this is minimized by the mean-zero halfspace f (x) = sign(x1). In this work, we conjecture that a natural generalization of this result holds for functions f: ℝn → Sk-1 which output k-dimensional unit vectors. Our main conjecture, which we call the vector-valued <PERSON><PERSON>'s inequality, asserts that the expectation Ex~ρy 〈f(x), f(y)〉 is minimized by the function f (x) = x≤k/||x≤k||, where x≤k = (x1,…, xk). We give several pieces of evidence in favor of this conjecture, including a proof that it does indeed hold in the special case of n = k.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH48"}, {"primary_key": "1258959", "vector": [], "sparse_vector": [], "title": "Positivity of the symmetric group characters is as hard as the polynomial time hierarchy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove that deciding the vanishing of the character of the symmetric group is C=P-complete. We use this hardness result to prove that the absolute value and also the square of the character are not contained in #P, unless the polynomial hierarchy collapses to the second level. This rules out the existence of any (unsigned) combinatorial description for the square of the characters. As a byproduct of our proof we conclude that deciding positivity of the character is PP-complete under many-one reductions, and hence PH-hard under Turing-reductions.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH136"}, {"primary_key": "1258960", "vector": [], "sparse_vector": [], "title": "Improved Approximations for Unrelated Machine Scheduling.", "authors": ["Sungjin Im", "<PERSON>"], "summary": "We revisit two well-studied scheduling problems in the unrelated machines setting where each job can have a different processing time on each machine. For minimizing total weighted completion time we give a 1.45-approximation, which improves upon the previous 1.488-approximation [Im and Shadloo SODA 2020]. The key technical ingredient in this improvement lies in a new rounding scheme that gives strong negative correlation with less restrictions. For minimizing Lk-norms of machine loads, inspired by [<PERSON><PERSON><PERSON><PERSON> et al. SODA 2017], we give better approximation algorithms. In particular we give a -approximation for the L2-norm which improves upon the former -approximations due to [<PERSON><PERSON>-<PERSON><PERSON><PERSON> STOC 2005] and [<PERSON> et al. JACM 2009].", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH111"}, {"primary_key": "1258961", "vector": [], "sparse_vector": [], "title": "Efficient resilient functions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An n-bit boolean function is resilient to coalitions of size q if no fixed set of q bits is likely to influence the value of the function when the other n — q bits are chosen uniformly at random, even though the function is nearly balanced. We construct explicit functions resilient to coalitions of size q = n/(log n)O(log log n) = n1-o(1) computable by linear-size circuits and linear-time algorithms. We also obtain a tight size-depth tradeoff for computing such resilient functions.Constructions such as ours were not available even non-explicitly. It was known that functions resilient to coalitions of size q = n0.63… can be computed by linear-size circuits [BL85], and functions resilient to coalitions of size q = Θ(n/ log2 n) can be computed by quadratic-size circuits [AL93].One component of our proofs is a new composition theorem for resilient functions.* This paper subsumes an unpublished work by Meka. PI and EV are partially supported by NSF grant CCF-2114116.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH108"}, {"primary_key": "1258962", "vector": [], "sparse_vector": [], "title": "On the orbit closure intersection problems for matrix tuples under conjugation and left-right actions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let G be a linear algebraic group acting on the vector space V. Given v, v' ∈ V, the orbit closure intersection problem asks to decide if the orbit closures of v and v' under G intersect. Due to connections with polynomial identity testing, the orbit closure intersection problems for the conjugation and left-right actions on matrix tuples received considerable attention in computational complexity and computational invariant theory, as seen in the works of <PERSON><PERSON><PERSON> (RANDOM 2013), <PERSON><PERSON> (STOC 2018), and <PERSON><PERSON><PERSON><PERSON> (Algebra & Number Theory 2020). In this paper, we present new algorithms for the orbit closure problem for the conjugation and left-right actions on matrix tuples. The main novel feature is that in the case of intersecting orbit closures, our algorithm outputs cosets of one-parameter subgroups that drive the matrix tuples to a tuple in the intersection of the orbit closures.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH158"}, {"primary_key": "1258963", "vector": [], "sparse_vector": [], "title": "A tight quasi-polynomial bound for Global Label Min-Cut.", "authors": ["<PERSON>", "Paloma T. Lima", "<PERSON><PERSON>", "<PERSON><PERSON>", "Uéverton S. Souza"], "summary": "We study a generalization of the classic GLOBAL MIN-CUT problem, called GLOBAL LABEL MIN-CUT (or sometimes GLOBAL HEDGE MIN-CUT): the edges of the input (multi)graph are labeled (or partitioned into color classes or hedges), and removing all edges of the same label (color or from the same hedge) costs one. The problem asks to disconnect the graph at minimum cost.While the st-cut version of the problem is known to be NP-hard, the above global cut version is known to admit a quasi-polynomial randomized nO(log OPT)-time algorithm due to <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> [SODA 2017]. They consider this as \"strong evidence that this problem is in P\". We show that this is actually not the case. We complete the study of the complexity of the Global Label Min-Cut problem by showing that the quasi-polynomial running time is probably optimal: We show that the existence of an algorithm with running time (np)o(log n/(log log n)2) would contradict the Randomized Exponential Time Hypothesis, where n is the number of vertices, and p is the number of labels in the input. The key step for the lower bound is a proof that Global Label Min-Cut is W[1]-hard when parameterized by the number of uncut labels. In other words, the problem is difficult in the regime where almost all labels need to be cut to disconnect the graph. To turn this lower bound into a quasi-polynomial-time lower bound, we also needed to revisit the framework due to Marx [Theory Comput. 2010] of proving lower bounds assuming Exponential Time Hypothesis through the SUBGRAPH ISOMORPHISM problem parameterized by the number of edges of the pattern. Here, we provide an alternative simplified proof of the hardness of this problem that is more versatile with respect to the choice of the regimes of the parameters.* This research is a part of a project that has received funding from the European Research Council (ERC) under the European Union's Horizon 2020 research and innovation programme Grant Agreement 714704 (LJ, TM, MP, US) and from the Research Council of Norway (LJ).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH12"}, {"primary_key": "1258964", "vector": [], "sparse_vector": [], "title": "Spencer&apos;s theorem in nearly input-sparsity time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A celebrated theorem of <PERSON> states that for every set system S1,…, Sm ⊆ [n], there is a coloring of the ground set with {±1} with discrepancy . We provide an algorithm to find such a coloring in near input-sparsity time Õ(n + Σi=1m|Si|)", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH152"}, {"primary_key": "1258965", "vector": [], "sparse_vector": [], "title": "Small subgraphs with large average degree.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we study the fundamental problem of finding small dense subgraphs in a given graph. For a real number s > 2, we prove that every graph on n vertices with average degree at least d contains a subgraph of average degree at least s on at most vertices. This is optimal up to the polylogarithmic factor, and resolves a conjecture of <PERSON><PERSON> and <PERSON>.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.02170", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH90"}, {"primary_key": "1258966", "vector": [], "sparse_vector": [], "title": "Super-resolution and Robust Sparse Continuous Fourier Transform in Any Constant Dimension: Nearly Linear Time and Sample Complexity.", "authors": ["Yaonan Jin", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The ability to resolve detail in the object that is being imaged, named by resolution, is the core parameter of an imaging system. Super-resolution is a class of techniques that can enhance the resolution of an imaging system and even transcend the diffraction limit of systems. Despite huge success in the application, super-resolution is not well understood on the theoretical side, especially for any dimension d ≥ 2. In particular, in order to recover a k-sparse signal, all previous results suffer from either/both poly(k) samples or running time.We design robust algorithms for any (constant) dimension under a strong noise model based on developing some new techniques in Sparse Fourier transform (Sparse FT), such as inverting a robust linear system, \"eggshell\" sampling schemes, and partition and voting methods in high dimension. These algorithms are the first to achieve running time and sample complexity (nearly) linear in the number of source points and logarithmic in bandwidth for any constant dimension, and we believe the techniques developed in the work can find their further applications on the Super-resolution and Sparse FT problem.* The full version of the paper can be accessed at https://arxiv.org/abs/2005.06156", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH176"}, {"primary_key": "1258967", "vector": [], "sparse_vector": [], "title": "The Price of Stability for First Price Auction.", "authors": ["Yaonan Jin", "<PERSON><PERSON><PERSON>"], "summary": "This paper establishes the Price of Stability (PoS) for First Price Auctions, for all equilibrium concepts that have been studied in the literature: Bayesian Nash Equilibrium ⊊ Bayesian Correlated Equilibrium ⊊ Bayesian Coarse Correlated Equilibrium.• Bayesian Nash Equilibrium: For independent valuations, the tight PoS is 1 − 1/e2 ≈ 0.8647, matching the counterpart Price of Anarchy (PoA) bound [JL22]. For correlated valuations, the tight PoS is 1 − 1/e ≈ 0.6321, matching the counterpart PoA bound [ST13, Syr14].This result indicates that, in the worst cases, efficiency degradation depends not on different selections among Bayesian Nash Equilibria.• Bayesian (Coarse) Correlated Equilibrium: For independent or correlated valuations, the tight PoS is always 1 = 100%, i.e., no efficiency degradation.This result indicates that First Price Auctions can be fully efficient when we allow the more general equilibrium concepts.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.04455", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH14"}, {"primary_key": "1258968", "vector": [], "sparse_vector": [], "title": "Quantum Speed-ups for String Synchronizing Sets, Longest Common Substring, and k-mismatch Matching.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Longest Common Substring (LCS) is an important text processing problem, which has recently been investigated in the quantum query model. The decisional version of this problem, LCS with threshold d, asks whether two length-n input strings have a common substring of length d. The two extreme cases, d = 1 and d = n, correspond respectively to Element Distinctness and Unstructured Search, two fundamental problems in quantum query complexity. However, the intermediate case 1 ≪ d ≪ n was not fully understood.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH186"}, {"primary_key": "1258969", "vector": [], "sparse_vector": [], "title": "Improved girth approximation in weighted undirected graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Virginia Vassilevska Williams", "<PERSON><PERSON>"], "summary": "Let G = (V,E,ℓ) be a n-nodes m-edges weighted undirected graph, where ℓ : E → (0, ∞) is a real length function defined on its edges. Let g be the length of the shortest cycle in G.We present an algorithm that in O(kn1+1/k log n + m(k + log n)) expected running time finds a cycle of length at most , for every integer k ≥ 1. This improves upon the previous best algorithm that in O((n1+1/k log n + m) log(nM)) time, where ℓ : E → [1, M] is an integral length function, finds a cycle of length at most 2kg [KRS+22]. For k = 1 our algorithm also improves the result of <PERSON><PERSON><PERSON> and <PERSON>v [RT13].", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH85"}, {"primary_key": "1258970", "vector": [], "sparse_vector": [], "title": "On the Integrality Gap of MFN Relaxation for the Capacitated Facility Location Problem.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Multicommodity Flow Network (MFN) relaxation, developed in [<PERSON>, <PERSON>, <PERSON>, <PERSON> 2014], is the only polynomial-time solvable relaxation that is known to provide a bounded integrality gap for the classic capacitated facility location (CFL) problem. The best upper-bound known for the integrality gap of this strong LP relaxation, however, is in the order of 288.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH40"}, {"primary_key": "1258971", "vector": [], "sparse_vector": [], "title": "Almost Tight Bounds for Online Facility Location in the Random-Order Model.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the online facility location problem with uniform facility costs in the random-order model. <PERSON><PERSON>'s algorithm [FOCS'01] is arguably the most natural and simple online algorithm for the problem with several advantages and appealing properties. Its analysis in the random-order model is one of the cornerstones of random-order analysis beyond the secretary problem. <PERSON><PERSON>'s algorithm was shown to be (asymptotically) optimal in the standard worst-case adversarial-order model and 8-competitive in the random order model. While this bound in the random-order model is the long-standing state-of-the-art, it is not known to be tight, and the true competitive-ratio of <PERSON><PERSON>'s algorithm remained an open question for more than two decades.We resolve this question and prove tight bounds on the competitive-ratio of <PERSON><PERSON>'s algorithm in the random-order model, showing that it is exactly 4-competitive. Following our tight analysis, we introduce a generic parameterized version of <PERSON><PERSON>'s algorithm that retains all the advantages of the original version. We show that the best algorithm in this family is exactly 3-competitive. On the other hand, we show that no online algorithm for this problem can achieve a competitive-ratio better than 2. Finally, we prove that the algorithms in this family are robust to partial adversarial arrival orders.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH56"}, {"primary_key": "1258972", "vector": [], "sparse_vector": [], "title": "Learning Hierarchical Cluster Structure of Graphs in Sublinear Time.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Learning graph cluster structure using few queries is a classical question in property testing, with the fundamental special case, namely expansion testing, considered in the seminal work of <PERSON><PERSON><PERSON> and <PERSON>[STOC'96]. The most recent results in this line of work design clustering oracles for (k, ε)-clusterable graphs, which are graphs that can be partitioned into k induced expanders with outer conductance bounded by ε ≪ 1. These oracles, given a graph whose vertex set can be partitioned into a disjoint union of k clusters (i.e., good expanders) with outer conductances bounded by ε ≪ 1, provide query access to an O(ε log k)- approximation to this ground truth clustering in time ≈ 2poly(k/ε)n1/2+O(ε) per query.Motivated by the rising interest in learning hierarchical structures in large networks, in this paper we introduce (k, γ)-hierarchically clusterable graphs, a natural hierarchical analog of classical (k, ε)-clusterable graphs; intuitively, these are graphs that exhibit pronounced hierarchical structure. We give a hierarchical clustering oracle for this model, i.e. a small space data structure that provides query access to a good hierarchical clustering at cost ≈ poly(k) · n1/2+O(γ) per query; notably, the dependence on k is polynomial, in contrast to best known flat clustering oracles. The result relies on several structural properties of hierarchically clusterable graphs that we hope will be of independent interest in sublinear time spectral graph algorithms.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH36"}, {"primary_key": "1258973", "vector": [], "sparse_vector": [], "title": "Toeplitz Low-Rank Approximation with Sublinear Query Complexity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a sublinear query algorithm for outputting a near-optimal low-rank approximation to any positive semidefinite Toeplitz matrix T ∈ ℝd×d. In particular, for any integer rank k ≤ d and ε, δ > 0, our algorithm makes Õ (k2 · log(1/δ) · poly(1/ε)) queries to the entries of T and outputs a rank Õ (k · log(1/δ)/ε) matrix d×d such that ||T – ||F ≤ (1 + ε) · ||T - Tk ||F + δ||Τ||F. Here, || · ||F is the Frobenius norm and Tk is the optimal rank-k approximation to T, given by projection onto its top k eigenvectors. Õ(·) hides polylog(d) factors. Our algorithm is structure-preserving, in that the approximation is also <PERSON><PERSON>litz. A key technical contribution is a proof that any positive semidefinite Toeplitz matrix in fact has a near-optimal low-rank approximation which is itself Toeplitz. Surprisingly, this basic existence result was not previously known. Building on this result, along with the well-established off-grid Fourier structure of Toeplitz matrices [Cybenko'82], we show that Toeplitz with near optimal error can be recovered with a small number of random queries via a leverage-score-based off-grid sparse Fourier sampling scheme.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH159"}, {"primary_key": "1258974", "vector": [], "sparse_vector": [], "title": "A half-integral <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> theorem for directed odd cycles.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that there exists a function f : ℕ → ℝ such that every directed graph G contains either k directed odd cycles where every vertex of G is contained in at most two of them, or a set of at most f(k) vertices meeting all directed odd cycles. We also give a polynomial-time algorithm for fixed k which outputs one of the two outcomes. Using this algorithmic result, we give a polynomial-time algorithm for fixed k to decide whether such k directed odd cycles exist, or there are no k vertex-disjoint directed odd cycles.This extends the half-integral <PERSON><PERSON><PERSON><PERSON><PERSON> theorem for undirected odd cycles by <PERSON> [Combinatorica 1999] to directed graphs.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH118"}, {"primary_key": "1258975", "vector": [], "sparse_vector": [], "title": "Breaking the 𝒪(n)-Barrier in the Construction of Compressed Suffix Arrays and Suffix Trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The suffix array, describing the lexicographical order of suffixes of a given text, and the suffix tree, a path-compressed trie of all suffixes, are the two most fundamental data structures for string processing, with plethora of applications in data compression, bioinformatics, and information retrieval. For a length-n text, however, they use Θ(n log n) bits of space, which is often too costly. To address this, <PERSON><PERSON> and <PERSON> [STOC 2000] and, independently, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [FOCS 2000] introduced space-efficient versions of the suffix array, known as the compressed suffix array (CSA) and the FM-index. <PERSON><PERSON><PERSON> [SODA 2002] then showed how to augment them to obtain the compressed suffix tree (CST). For a length-n text over an alphabet of size σ, these structures use only 𝒪(n log σ) bits. Nowadays, these structures are part of the standard toolbox: modern textbooks spend dozens of pages describing their applications, and they almost completely replaced suffix arrays and suffix trees in space-critical applications. The biggest remaining open question is how efficiently they can be constructed. After two decades, the fastest algorithms still run in 𝒪(n) time [<PERSON> et al., FOCS 2003], which is Θ(logσ n) factor away from the lower bound of Ω(n/ logσ n) (following from the necessity to read the input).In this paper, we make the first in 20 years improvement in n for this problem by proposing a new compressed suffix array and a new compressed suffix tree which admit o(n)-time construction algorithms while matching the space bounds and the query times of the original CSA/CST and the FM-index. More precisely, our structures take 𝒪(n log σ) bits, support SA queries and full suffix tree functionality in 𝒪(logε n) time per operation, and can be constructed in time using 𝒪(n log σ) bits of working space. (For example, if σ = 2, the construction time is We derive this result as a corollary from a much more general reduction: We prove that all parameters of a compressed suffix array/tree (query time, space, construction time, and construction working space) can essentially be reduced to those of a data structure answering new query types that we call prefix rank and prefix selection. Using the novel techniques, we also develop a new index for pattern matching.* The full version of the paper can be accessed at https://arxiv.org/abs/2106.12725.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH187"}, {"primary_key": "1258976", "vector": [], "sparse_vector": [], "title": "Online and Bandit Algorithms Beyond ℓp Norms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Vector norms play a fundamental role in computer science and optimization, so there is an ongoing effort to generalize existing algorithms to settings beyond ℓ∞ and ℓp norms. We show that many online and bandit applications for general norms admit good algorithms as long as the norm can be approximated by a function that is \"gradient-stable\", a notion that we introduce. Roughly it says that the gradient of the function should not drastically decrease (multiplicatively) in any component as we increase the input vector. We prove that several families of norms, including all monotone symmetric norms, admit a gradient-stable approximation, giving us the first online and bandit algorithms for these norm families.In particular, our notion of gradient-stability gives O (log2 (dimension))-competitive algorithms for the symmetric norm generalizations of Online Generalized Load Balancing and Bandits with Knapsacks. Our techniques extend to applications beyond symmetric norms as well, e.g., to Online Vector Scheduling and to Online Generalized Assignment with Convex Costs. Some key properties underlying our applications that are implied by gradient-stable approximations are a \"smooth game inequality\" and an approximate converse to <PERSON>'s inequality.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH58"}, {"primary_key": "1258978", "vector": [], "sparse_vector": [], "title": "On Minimizing Tardy Processing Time, Max-Min Skewed Convolution, and Triangular Structured ILPs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The starting point of this paper is the problem of scheduling n jobs with processing times and due dates on a single machine so as to minimize the total processing time of tardy jobs, i.e., 1 || ΣpjUj. This problem was identified by <PERSON><PERSON> et al. (Algorithmica 2022) as a natural subquadratic-time special case of the classic 1 || Σ wjUj problem, which likely requires time quadratic in the total processing time P, because of a fine-grained lower bound. <PERSON><PERSON> et al. obtain their Õ(P7/4) time scheduling algorithm through a new variant of convolution, dubbed Max-Min Skewed Convolution, which they solve in Õ(n7/4) time. Our main technical contribution is a faster and simpler convolution algorithm running in Õ(n5/3) time. It implies an Õ(P5/3) time algorithm for 11 | ΣpjUj, but may also be of independent interest.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH112"}, {"primary_key": "1258979", "vector": [], "sparse_vector": [], "title": "Superpolynomial lower bounds for decision tree learning and testing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We establish new hardness results for decision tree optimization problems, adding to a line of work that dates back to <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> in 1976. We prove, under the randomized exponential time hypothesis, superpolynomial runtime lower bounds for two basic problems: given an explicit representation of a function f and a generator for a distribution D", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH75"}, {"primary_key": "1258980", "vector": [], "sparse_vector": [], "title": "Faster and Unified Algorithms for Diameter Reducing Shortcuts and Minimum Chain Covers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For an n-vertex m-edge digraph G, a D-shortcut is a small set H of directed edges taken from the transitive closure of G, satisfying that the diameter of G ∪ H is at most D. In a sequence of works [<PERSON><PERSON> and <PERSON>er, SODA 2022 & ICALP 2022] provided shortcut algorithms with improved diameter vs. size tradeoffs. In this paper, we present faster and unified shortcut algorithms for general digraphs. These algorithms also yield improved tradeoffs for the family of bounded-width DAGs. We show:", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH9"}, {"primary_key": "1258981", "vector": [], "sparse_vector": [], "title": "Mean estimation when you have the source code; or, quantum Monte Carlo methods.", "authors": ["<PERSON>", "Ryan <PERSON>&<PERSON>;Donnell"], "summary": "Suppose y is a real random variable, and one is given access to \"the code\" that generates it (for example, a randomized or quantum circuit whose output is y). We give a quantum procedure that runs the code O(n) times and returns an estimate for μ = E[y] that with high probability satisfies , where σ = stddev[y]. This dependence on n is optimal for quantum algorithms. One may compare with classical algorithms, which can only achieve the quadratically worse . Our method improves upon previous works, which either made additional assumptions about y, and/or assumed the algorithm knew an a priori bound on σ, and/or used additional logarithmic factors beyond O(n). The central subroutine for our result is essentially <PERSON><PERSON>'s algorithm but with complex phases.* The full version of the paper can be accessed at https://arxiv.org/abs/2208.07544", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH44"}, {"primary_key": "1258982", "vector": [], "sparse_vector": [], "title": "Exact Flow Sparsification Requires Unbounded Size.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Given a large edge-capacitated network G and a subset of k vertices called terminals, an (exact) flow sparsifier is a small network G' that preserves (exactly) all multicommodity flows that can be routed between the terminals. Flow sparsifiers were introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [STOC 2010], and have been studied and used in many algorithmic contexts.A fundamental question that remained open for over a decade, asks whether every k-terminal network admits an exact flow sparsifier whose size is bounded by some function f (k) (regardless of the size of G or its capacities). We resolve this question in the negative by proving that there exist 6-terminal networks G whose flow sparsifiers G' must have arbitrarily large size. This unboundedness is perhaps surprising, since the analogous sparsification that preserves all terminal cuts (called exact cut sparsifier or mimicking network) admits sparsifiers of size fo(k) ≤ 22k [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, J<PERSON> 1998].We prove our results by analyzing the set of all feasible demands in the network, known as the demand polytope. We identify an invariant of this polytope, essentially the slope of certain facets, that can be made arbitrarily large even for k = 6, and implies an explicit lower bound on the size of the network. We further use this technique to answer, again in the negative, an open question of <PERSON> [JCTB 2015] regarding flow-sparsification that uses only contractions and preserves the infeasibility of one demand vector.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.07363† The first version of this paper proved a weaker statement of Theorem 1.2 with 4 commodities. The current statement has only 3 commodities, and now fully refutes Seymour's conjectures. In addition, the current version describes implications to the 0-extension problem, see Section 1.4.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH91"}, {"primary_key": "1258983", "vector": [], "sparse_vector": [], "title": "Fast Discrepancy Minimization with Hereditary Guarantees.", "authors": ["<PERSON><PERSON>"], "summary": "Efficiently computing low discrepancy colorings of various set systems, has been studied extensively since the breakthrough work by <PERSON><PERSON> (FOCS 2010), who gave the first polynomial time algorithms for several important settings, including for general set systems, sparse set systems and for set systems with bounded hereditary discrepancy. The hereditary discrepancy of a set system, is the maximum discrepancy over all set systems obtainable by deleting a subset of the ground elements. While being polynomial time, <PERSON><PERSON>'s algorithms were not practical, with e.g. his algorithm for the hereditary setup running in time Ω(mn 4.5) for set systems with m sets over a ground set of n elements. More efficient algorithms have since then been developed for general and sparse set systems, however, for the hereditary case, <PERSON><PERSON>'s algorithm remains state-of-the-art. In this work, we give a significantly faster algorithm with hereditary guarantees, running in O(mn 2 lg(2 + m/n) + n 3) time. Our algorithm is based on new structural insights into set systems with bounded hereditary discrepancy. We also implement our algorithm and show experimentally that it computes colorings that are significantly better than random and finishes in a reasonable amount of time, even on set systems with thousands of sets over a ground set of thousands of elements.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH11"}, {"primary_key": "1258984", "vector": [], "sparse_vector": [], "title": "Approximate Distance Oracles for Planar Graphs with Subpolynomial Error Dependency.", "authors": ["<PERSON>"], "summary": "<PERSON><PERSON> [FOCS'01, JACM'04] and <PERSON> [SODA'01] independently showed that there exists a (1 + ε)-approximate distance oracle for planar graphs with O(n(log n)ε-1) space and O(ε-1) query time. While the dependency on n is nearly linear, the space-query product of their oracles depend quadratically on 1/ε. Many follow-up results either improved the space or the query time of the oracles while having the same, sometimes worst, dependency on 1/ε. <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> [SODA'13] were the first to improve the dependency on 1/ε from quadratic to nearly linear (at the cost of log*(n) factors). It is plausible to conjecture that the linear dependency on 1/ε is optimal: for many known distance-related problems in planar graphs, it was proved that the dependency on 1/ε is at least linear.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH72"}, {"primary_key": "1258985", "vector": [], "sparse_vector": [], "title": "The Power of Clairvoyance for Multi-Level Aggregation and Set Cover with Delay.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xie"], "summary": "Most online problems with delay require clairvoyance, the future delay of a request is known upon its arrival, to achieve polylogarithmic competitiveness. An exception is Set Cover with Delay: <PERSON><PERSON> et al. (ESA 2020) gave a non-clairvoyant randomized algorithm with polylogarithmic competitive ratio. However, no non-trivial algorithms are known for other non-clairvoyant online problems with delay and it is also unclear if non-clairvoyance requires randomization. In this work, we make progress towards understanding the power of clairvoyance for online problems with delay by providing deterministic non-clairvoyant algorithms for Multi-Level Aggregation and Set Cover with Delay.Our main contribution is a deterministic -competitive algorithm for Multi-Level Aggregation, where D is the depth of the aggregation tree. For the special case of Joint Replenishment (D = 1), we give an lower bound against non-clairvoyant randomized algorithms. Thus, we get a tight characterization of the competitive ratio for non-clairvoyant Joint Replenishment. Finally, we show that clairvoyance is not required at all for Set Cover with Delay by derandomizing the algorithm of <PERSON><PERSON> et al. losing at most a constant factor in the competitiveness. Together with the above bounds, this also implies that randomization does not help in the non-clairvoyant setting.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH59"}, {"primary_key": "1258986", "vector": [], "sparse_vector": [], "title": "Pricing Query Complexity of Revenue Maximization.", "authors": ["<PERSON><PERSON>", "Balasubramanian <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The common way to optimize auction and pricing systems is to set aside a small fraction of the traffic to run experiments. This leads to the question: how can we learn the most with the smallest amount of data? For truthful auctions, this is the sample complexity problem. For posted price auctions, we no longer have access to samples. Instead, the algorithm is allowed to choose a price pt; then for a fresh sample vt ~ D we learn the sign st = sign(pt — vt) ∈ {-1, +1}. How many pricing queries are needed to estimate a given parameter of the underlying distribution?We give tight upper and lower bounds on the number of pricing queries required to find an approximately optimal reserve price for general, regular and MHR distributions. Interestingly, for regular distributions, the pricing query and sample complexities match. But for general and MHR distributions, we show a strict separation between them.All known results on sample complexity for revenue optimization follow from a variant of using the optimal reserve price of the empirical distribution. In the pricing query complexity setting, we show that learning the entire distribution within an error of ε in Levy distance requires strictly more pricing queries than to estimate the reserve. Instead, our algorithm uses a new property we identify called relative flatness to quickly zoom into the right region of the distribution to get the optimal pricing query complexity.* The full version of the paper can be accessed at https://arxiv.org/abs/2111.03158", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH17"}, {"primary_key": "1258987", "vector": [], "sparse_vector": [], "title": "Efficient decoding up to a constant fraction of the code length for asymptotically good quantum codes.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce and analyse an efficient decoder for quantum Tanner codes that can correct adversarial errors of linear weight. Previous decoders for quantum low-density parity-check codes could only handle adversarial errors of weight . We also work on the link between quantum Tanner codes and the Lifted Product codes of Panteleev and <PERSON><PERSON>chev, and show that our decoder can be adapted to the latter. The decoding algorithm alternates between sequential and parallel procedures and converges in linear time.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH45"}, {"primary_key": "1258988", "vector": [], "sparse_vector": [], "title": "A New Approach to Estimating Effective Resistances and Counting Spanning Trees in Expander Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We demonstrate that for expander graphs, for all ε > 0, there exists a data structure of size Õ(nε-1) which can be used to return (1 + ε)-approximations to effective resistances in Õ(1) time per query. Short of storing all effective resistances, previous best approaches could achieve Õ(nε-2) size and Õ (ε-2) time per query by storing <PERSON><PERSON> vectors for each vertex, or Õ (nε-1) size and Õ (nε-1) time per query by storing a spectral sketch.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH102"}, {"primary_key": "1258989", "vector": [], "sparse_vector": [], "title": "Constant Approximating Parameterized k-SETCOVER is W[2]-hard.", "authors": ["Bing<PERSON> Lin", "<PERSON><PERSON><PERSON>", "Yican Sun", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we prove that it is W[2]-hard to approximate k-SETCOVER within any constant ratio. Our proof is built upon the recently developed threshold graph composition technique. We propose a strong notion of threshold graphs and use a new composition method to prove this result. Our technique could also be applied to rule out polynomial time ratio approximation algorithms for the non-parameterized k-SETCOVER problem with k as small as , assuming W[1] ≠ FPT. We highlight that our proof does not depend on the well-known PCP theorem, and only involves simple combinatorial objects.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH126"}, {"primary_key": "1258990", "vector": [], "sparse_vector": [], "title": "Robust Voting Rules from Algorithmic Robust Statistics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Maximum likelihood estimation furnishes powerful insights into voting theory, and the design of voting rules. However the MLE can usually be badly corrupted by a single outlying sample. This means that a single voter or a group of colluding voters can vote strategically and drastically affect the outcome. Motivated by recent progress in algorithmic robust statistics, we revisit the fundamental problem of estimating the central ranking in a Mallows model, but ask for an estimator that is provably robust, unlike the MLE.Our main result is an efficiently computable estimator that achieves nearly optimal robustness guarantees. In particular the robustness guarantees are dimension-independent in the sense that our overall accuracy does not depend on the number of alternatives being ranked. As an immediate consequence, we show that while the landmark Gibbard-<PERSON><PERSON>wai<PERSON> theorem tells us a strong impossibility result about designing strategy-proof voting rules, there are quantitatively strong ways to protect against large coalitions if we assume that the remaining voters are honest and their preferences are sampled from a Mallows model. Our work also makes technical contributions to algorithmic robust statistics by designing new spectral filtering techniques that can exploit the intricate combinatorial dependencies in the Mallows model.* The full version of the paper can be accessed at https://arxiv.org/abs/2112.06380", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH133"}, {"primary_key": "1258991", "vector": [], "sparse_vector": [], "title": "A Framework for Approximation Schemes on Disk Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We initiate a systematic study of approximation schemes for fundamental optimization problems on disk graphs, a common generalization of both planar graphs and unit-disk graphs. Our main contribution is a general framework for designing efficient polynomial-time approximation schemes (EPTASes) for vertex- deletion problems on disk graphs, which results in EPTASes for many fundamental problems including VERTEX COVER, FEEDBACK VERTEX SET, SMALL CYCLE HITTING (in particular, TRIANGLE HITTING), Pk-VERTEX DELETION for k ∈ {3,4,5}, PATH DELETION, PATHWIDTH 1-DELETION, COMPONENT ORDER CONNECTIVITY, BOUNDED DEGREE DELETION, PSEUDOFOREST DELETION, FINITE-TYPE COMPONENT DELETION, etc. All EPTASes obtained using our framework are robust in the sense that they do not require a realization of the input disk graph (in fact, we allow the input to be any graph, and our algorithms either output a correct approximation solution for the problem or conclude that the input graph is not a disk graph). To the best of our knowledge, prior to this work, the only problems known to admit PTASes or EPTASes on disk graphs are MAXIMUM CLIQUE, INDEPENDENT SET, DOMINATING SET, and VERTEX COVER, among which the existing PTAS [<PERSON> et al., <PERSON><PERSON><PERSON><PERSON>'05] and <PERSON>TAS [<PERSON>uwen, SWAT'06] for VERTEX COVER require a realization of the input disk graph (while ours does not).The core of our framework is a reduction for a broad class of (approximation) vertex-deletion problems from (general) disk graphs to disk graphs of bounded local radius, which is a new invariant of disk graphs introduced in this work. Disk graphs of bounded local radius can be viewed as a \"mild\" generalization of planar graphs, which preserves certain nice properties of planar graphs. Specifically, we prove that disk graphs of bounded local radius admit the Excluded Grid Minor property and have locally bounded treewidth. This allows existing techniques for designing approximation schemes on planar graphs (e.g., bidimensionality and Baker's technique) to be directly applied to disk graphs of bounded local radius.* The full version of the paper can be accessed at https://arxiv.org/abs/2211.02717", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH84"}, {"primary_key": "1258992", "vector": [], "sparse_vector": [], "title": "Balanced Allocations with Heterogeneous Bins: The Power of Memory.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the allocation of m balls (jobs) into n bins (servers). In the standard TWO-CHOICE process, at each step t = 1, 2,…, m we first sample two bins uniformly at random and place a ball in the least loaded bin. It is well-known that for any m  n, this results in a gap (difference between the maximum and average load) of log2 log n + θ(1) (with high probability). In this work, we consider the MEMORY process [27] where instead of two choices, we only sample one bin per step but we have access to a cache which can store the location of one bin. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON> [23] showed that in the lightly loaded case (m = n), the MEMORY process achieves a gap of 𝒪 (log log n).Extending the setting of <PERSON><PERSON><PERSON><PERSON><PERSON> et al. in two ways, we first allow the number of balls m to be arbitrary, which includes the challenging heavily loaded case where m  n. Secondly, we follow the heterogeneous bins model of <PERSON><PERSON><PERSON> [30], where the sampling distribution of bins can be biased up to some arbitrary multiplicative constant. Somewhat surprisingly, we prove that even in this setting, the MEMORY process still achieves an 𝒪(loglog n) gap bound. This is in stark contrast with the TWO-CHOICE (or any d-CHOICE with d = 𝒪(1)) process, where it is known that the gap diverges as m → ∞ [30]. Further, we show that for any sampling distribution independent of m (but possibly dependent on n) the MEMORY process has a gap that can be bounded independently of m. Finally, we prove a tight gap bound of 𝒪(log n) for MEMORY in another relaxed setting with heterogeneous (weighted) balls and a cache which can only be maintained for two steps.* The full version of the paper can be accessed at [20]", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH169"}, {"primary_key": "1258993", "vector": [], "sparse_vector": [], "title": "On (Random-order) Online Contention Resolution Schemes for the Matching Polytope of (Bipartite) Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present new results for online contention resolution schemes for the matching polytope of graphs, in the random-order (RCRS) and adversarial (OCRS) arrival models. Our results include improved selectability guarantees (i.e., lower bounds), as well as new impossibility results (i.e., upper bounds). By well-known reductions to the prophet (secretary) matching problem, a c-selectable OCRS (RCRS) implies a c-competitive algorithm for adversarial (random order) edge arrivals. Similar reductions are also known for the query-commit matching problem. For the adversarial arrival model, we present a new analysis of the OCRS of <PERSON> et al. (EC, 2020). We show that this scheme is 0.344-selectable for general graphs and 0.349-selectable for bipartite graphs, improving on the previous 0.337 selectability result for this algorithm. We also show that the selectability of this scheme cannot be greater than 0.361 for general graphs and 0.382 for bipartite graphs. We further show that no OCRS can achieve a selectability greater than 0.4 for general graphs, and 0.433 for bipartite graphs.For random-order arrivals, we present two attenuation-based schemes which use new attenuation functions. Our first RCRS is 0.474-selectable for general graphs, and our second is 0.476-selectable for bipartite graphs. These results improve upon the recent 0.45 (and 0.456) selectability results for general graphs (respectively, bipartite graphs) due to <PERSON><PERSON> et al. (EC, 2022). On general graphs, our 0.474-selectable RCRS provides the best known positive result even for offline contention resolution, and also for the correlation gap. We conclude by proving a fundamental upper bound of 0.5 on the selectability of RCRS, using bipartite graphs.* The full version of the paper can be accessed at https://arxiv.org/abs/2209.07520", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH76"}, {"primary_key": "1258994", "vector": [], "sparse_vector": [], "title": "Near-Linear Sample Complexity for Lp Polynomial Regression.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study Lp polynomial regression. Given query access to a function f : [−1,1]→ℝ, the goal is to find a degree d polynomial q̂ such that, for a given parameter ε > 0Here || · ||p is the Lp norm, ‖g‖p = (∫1−1|g(t)|p dt)1/p. We show that querying f at points randomly drawn from the Chebyshev measure on [-1,1] is a near-optimal strategy for polynomial regression in all Lp norms. In particular, to find q̂, it suffices to sample points from [-1,1] with probabilities proportional to this measure. While the optimal sample complexity for polynomial regression was well understood for L2 and L∞, our result is the first that achieves sample complexity linear in d and error (1 + ε) for other values of p without any assumptions.Our result requires two main technical contributions. The first concerns p ≤ 2, for which we provide explicit bounds on the Lp Lewis weight function of the infinite linear operator underlying polynomial regression. Using tools from the orthogonal polynomial literature, we show that this function is bounded by the Chebyshev density. Our second key contribution is to take advantage of the structure of polynomials to reduce the p > 2 case to the p ≤ 2 case. By doing so, we obtain a better sample complexity than what is possible for general p-norm linear regression problems, for which Ω(dp/2) samples are required.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH153"}, {"primary_key": "1258996", "vector": [], "sparse_vector": [], "title": "Sharp threshold sequence and universality for Ising perceptron models.", "authors": ["<PERSON><PERSON>", "Nike Sun"], "summary": "We study a family of Ising perceptron models with {0,1}-valued activation functions. This includes the classical half-space models, as well as some of the symmetric models considered in recent works. For each of these models we show that the free energy is self-averaging, there is a sharp threshold sequence, and the free energy is universal with respect to the disorder. A prior work of <PERSON><PERSON> (2019) used very different methods to show a sharp threshold sequence in the half-space Ising perceptron with <PERSON><PERSON><PERSON> disorder. Recent works of <PERSON> (2021) and <PERSON><PERSON><PERSON> (2021) determined the sharp threshold and limiting free energy in a symmetric perceptron model. The results of this paper apply in more general settings, and are based on new \"add one constraint\" estimates extending <PERSON><PERSON><PERSON>'s estimates for the half-space model (1999, 2011).* The full version of the paper can be accessed at https://arxiv.org/abs/2204.03469.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH28"}, {"primary_key": "1258997", "vector": [], "sparse_vector": [], "title": "Maximal k-Edge-Connected Subgraphs in Weighted Graphs via Local Random Contraction.", "authors": ["Chaitanya Nalam", "Thatchaphol <PERSON>"], "summary": "The maximal k-edge-connected subgraphs problem is a classical graph clustering problem studied since the 70's. Surprisingly, no non-trivial technique for this problem in weighted graphs is known: a very straightforward recursive-min-cut algorithm with Ω(mn) time has remained the fastest algorithm until now. All previous progress gives a speed-up only when the graph is unweighted, and k is small enough (e.g. <PERSON> et al. (ICALP'15), <PERSON><PERSON><PERSON> et al. (SODA'17), and <PERSON> et al. (SODA'20)).We give the first algorithm that breaks through the long-standing Õ(mn)-time barrier in weighted undirected graphs. More specifically, we show a maximal k-edge-connected subgraphs algorithm that takes only Õ(m · min{m3/4,n4/5}) time. As an immediate application, we can (1 + ε)-approximate the strength of all edges in undirected graphs in the same running time.Our key technique is the first local cut algorithm with exact cut-value guarantees whose running time depends only on the output size. All previous local cut algorithms either have running time depending on the cut value of the output, which can be arbitrarily slow in weighted graphs or have approximate cut guarantees.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH8"}, {"primary_key": "1258998", "vector": [], "sparse_vector": [], "title": "4D Range Reporting in the Pointer Machine Model in Almost-Optimal Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the orthogonal range reporting problem we must pre-process a set P of multi-dimensional points, so that for any axis-parallel query rectangle q all points from q ∩ P can be reported efficiently. In this paper we study the query complexity of multi-dimensional orthogonal range reporting in the pointer machine model. We present a data structure that answers four-dimensional orthogonal range reporting queries in almost-optimal time O(log n log log n + k) and uses O(n log4 n) space, where n is the number of points in P and k is the number of points in q ∩ P. This is the first data structure with nearly-linear space usage that achieves almost-optimal query time in 4d. This result can be immediately generalized to d ≥ 4 dimensions: we show that there is a data structure supporting d-dimensional range reporting queries in time O(logd-3 n log log n + k) for any constant d ≥ 4.* The full version of the paper can be accessed at https://arxiv.org/abs/2211.03161", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH71"}, {"primary_key": "1258999", "vector": [], "sparse_vector": [], "title": "Passing the Limits of Pure Local Search for Weighted k-Set Packing.", "authors": ["<PERSON><PERSON>"], "summary": "We study the weighted k-Set Packing problem, which is defined as follows: Given a collection S of sets, each of cardinality at most k, together with a positive weight function , the task is to compute a sub-collection A ⊆ S of maximum total weight such that the sets in A are pairwise disjoint. For k ≤ 2, the weighted k-Set Packing problem reduces to the Maximum Weight Matching problem, and can thus be solved in polynomial time [5]. However, for k ≥ 3, already the special case of unit weights, the unweighted k-Set Packing problem, becomes NP-hard as it generalizes the 3D-matching problem [8]. The state-of-the-art algorithms for both the unweighted and the weighted k-Set Packing problem rely on local search. In the unweighted setting, the best known approximation guarantee is [6]. For general weights, <PERSON><PERSON>'s algorithm SquareImp, which yields a , has remained unchallenged for twenty years [1]. Only recently, <PERSON><PERSON><PERSON><PERSON><PERSON> managed to improve on this by obtaining approximation guarantees of with limk → ∞ ∊k =0 [10]. She further showed her result to be asymptotically best possible in that no algorithm considering local improvements of logarithmically bounded size with respect to some fixed power of the weight function can yield an approximation guarantee better than [10].In this paper, we finally show how to beat the threshold of for the weighted k-Set Packing problem by Ω(k). We achieve this by combining local search with the application of a black box algorithm for the unweighted k-Set Packing problem to carefully chosen sub-instances. In doing so, we do not only manage to link the approximation ratio for general weights to the one achievable in the unweighted case: In contrast to previous works, which yield an improvement over <PERSON>rman's long-standing result of either only for large values of k ≥ 2 · 105 [10], or by less than 6 · 10-7 [9], we achieve guarantees of at most for all k ≥ 4.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH41"}, {"primary_key": "1259000", "vector": [], "sparse_vector": [], "title": "Private Query Release via the Johnson-Lindenstrauss Transform.", "authors": ["Aleksan<PERSON>"], "summary": "We introduce a new method for releasing answers to statistical queries with differential privacy, based on the <PERSON><PERSON><PERSON> lemma. The key idea is to randomly project the query answers to a lower dimensional space so that the distance between any two vectors of feasible query answers is preserved up to an additive error. Then we answer the projected queries using a simple noise-adding mechanism, and lift the answers up to the original dimension. Using this method, we give, for the first time, purely differentially private mechanisms with optimal worst case sample complexity under average error for answering a workload of k queries over a universe of size N. As other applications, we give the first purely private efficient mechanisms with optimal sample complexity for computing the covariance of a bounded high-dimensional distribution, and for answering 2-way marginal queries. We also show that, up to the dependence on the error, a variant of our mechanism is nearly optimal for every given query workload.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH182"}, {"primary_key": "1259001", "vector": [], "sparse_vector": [], "title": "Secretary Problems: The Power of a Single Sample.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we investigate two variants of the secretary problem. In these variants, we are presented with a sequence of numbers Xi that come from distributions Di, and that arrive in either random or adversarial order. We do not know what the distributions are, but we have access to a single sample Yi from each distribution Di. After observing each number, we have to make an irrevocable decision about whether we would like to accept it or not with the goal of maximizing the probability of selecting the largest number.The random order version of this problem was first studied by <PERSON><PERSON><PERSON> et al. [SODA 2020] who managed to construct an algorithm that achieves a probability of 0.4529. In this paper, we improve this probability to 0.5009, almost matching an upper bound of ≃ 0.5024 which we show follows from earlier work. We also show that there is an algorithm which achieves the probability of ≃ 0.5024 asymptotically if no particular distribution is especially likely to yield the largest number. For the adversarial order version of the problem, we show that we can select the maximum number with a probability of 1/4, and that this is best possible. Our work demonstrates that unlike in the case of the expected value objective studied by <PERSON><PERSON> et al. [ITCS 2020], knowledge of a single sample is not enough to recover the factor of success guaranteed by full knowledge of the distribution.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH77"}, {"primary_key": "1259002", "vector": [], "sparse_vector": [], "title": "Algebraic Algorithms for Fractional Linear Matroid Parity via Non-commutative Rank.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Matrix representations are a powerful tool for designing efficient algorithms for combinatorial optimization problems such as matching, and linear matroid intersection and parity. In this paper, we initiate the study of matrix representations using the concept of non-commutative rank (nc-rank), which has recently attracted attention in the research of <PERSON><PERSON>' problem. We reveal that the nc-rank of the matrix representation of linear matroid parity corresponds to the optimal value of fractional linear matroid parity: a half-integral relaxation of linear matroid parity. Based on our representation, we present an algebraic algorithm for the fractional linear matroid parity problem by building a new technique to incorporate the search-to-decision reduction into the half-integral problem represented via the nc-rank. We further present a faster divide-and- conquer algorithm for finding a maximum fractional matroid matching and an algebraic algorithm for finding a dual optimal solution. They together lead to an algebraic algorithm for the weighted fractional linear matroid parity problem. Our algorithms are significantly simpler and faster than the existing algorithms.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.07946", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH161"}, {"primary_key": "1259003", "vector": [], "sparse_vector": [], "title": "Non-Stochastic CDF Estimation Using Threshold Queries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Estimating the empirical distribution of a scalar-valued data set is a basic and fundamental task. In this paper, we tackle the problem of estimating an empirical distribution in a setting with two challenging features. First, the algorithm does not directly observe the data; instead, it only asks a limited number of threshold queries about each sample. Second, the data are not assumed to be independent and identically distributed; instead, we allow for an arbitrary process generating the samples, including an adaptive adversary. These considerations are relevant, for example, when modeling a seller experimenting with posted prices to estimate the distribution of consumers' willingness to pay for a product: offering a price and observing a consumer's purchase decision is equivalent to asking a single threshold query about their value, and the distribution of consumers' values may be non-stationary over time, as early adopters may differ markedly from late adopters.Our main result quantifies, to within a constant factor, the sample complexity of estimating the empirical CDF of a sequence of elements of [n], up to ε additive error, using one threshold query per sample. The complexity depends only logarithmically on n, and our result can be interpreted as extending the existing logarithmic-complexity results for noisy binary search to the more challenging setting where noise is non-stochastic. Along the way to designing our algorithm, we consider a more general model in which the algorithm is allowed to make a limited number of simultaneous threshold queries on each sample. We solve this problem using <PERSON>'s Approachability Theorem and the exponential weights method. As a side result of independent interest, we characterize the minimum number of simultaneous threshold queries required by deterministic CDF estimation algorithms.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH135"}, {"primary_key": "1259004", "vector": [], "sparse_vector": [], "title": "Online Prediction in Sub-linear Space.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We provide the first sub-linear space and sub-linear regret algorithm for online learning with expert advice (against an oblivious adversary), addressing an open question raised recently by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON> (STOC 2022). We also demonstrate a separation between oblivious and (strong) adaptive adversaries by proving a linear memory lower bound of any sub-linear regret algorithm against an adaptive adversary. Our algorithm is based on a novel pool selection procedure that bypasses the traditional wisdom of leader selection for online learning, and a generic reduction that transforms any weakly sub-linear regret o(T) algorithm to T1-α regret algorithm, which may be of independent interest. Our lower bound utilizes the connection of no-regret learning and equilibrium computation in zero-sum games, leading to a proof of a strong lower bound against an adaptive adversary.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.07974", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH60"}, {"primary_key": "1259005", "vector": [], "sparse_vector": [], "title": "Discrepancy Minimization via Regularization.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce a new algorithmic framework for discrepancy minimization based on regularization. We demonstrate how varying the regularizer allows us to re-interpret several breakthrough works in algorithmic discrepancy, ranging from <PERSON>'s theorem [34, 6] to <PERSON><PERSON><PERSON><PERSON><PERSON>'s bounds [5, 8]. Using our techniques, we also show that the <PERSON><PERSON> and <PERSON> conjectures are true in a new regime of pseudorandom instances.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH66"}, {"primary_key": "1259006", "vector": [], "sparse_vector": [], "title": "Concentration of polynomial random matrices via Efron-Stein inequalities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Analyzing concentration of large random matrices is a common task in a wide variety of fields. Given independent random variables, several tools are available to bound the norms of random matrices whose entries are linear in the variables, such as the matrix-Bernstein inequality. However, for many recent applications, we need to bound the norms of random matrices whose entries are polynomials in the variables. Such matrices arise naturally in the analysis of spectral algorithms (e.g., <PERSON> et al. [STOC 2016], <PERSON><PERSON><PERSON> and <PERSON> [STOC 2019]), and in lower bounds for semidefinite programs based on the Sum-of-Squares (SoS) hierarchy (e.g. <PERSON> et al. [FOCS 2016], <PERSON> et al. [FOCS 2021]).In this work, we present a general framework to obtain such bounds, based on the beautiful matrix Efron-Stein inequalities developed by <PERSON><PERSON>, <PERSON> and <PERSON> [Annals of Probability 2016]. The Efron- Stein inequality bounds the norm of a random matrix by the norm of another potentially simpler (but still random) matrix. We view the latter matrix as arising by \"differentiating\" the starting matrix. By recursively differentiating, our framework reduces the main task to bounding the norms of far simpler matrices. These simpler matrices are in fact deterministic matrices in the case of Radema<PERSON> random variables and hence, bounding their norm is a far easier task. In general for non-Rademacher random variables, the task reduces to the much easier task of scalar concentration. Moreover, in the setting of polynomial matrices, our main result also generalizes the work of <PERSON><PERSON>, <PERSON> and <PERSON>.As applications of our basic framework, we recover known bounds in the literature, especially for simple \"tensor networks\" and \"dense graph matrices\". As applications of our general framework, we derive bounds for \"sparse graph matrices\". The sparse graph matrix bounds were obtained only recently by Jones et al. [FOCS 2021] using a nontrivial application of the trace power method, and was a core component in their work. We expect this framework will also be helpful for other applications involving concentration phenomena for nonlinear random matrices.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH138"}, {"primary_key": "1259007", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>feiler-<PERSON><PERSON> and Graph Spectra.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Two simple undirected graphs are cospectral if their respective adjacency matrices have the same multiset of eigenvalues. Cospectrality yields an equivalence relation on the family of graphs which is provably weaker than isomorphism. In this paper, we study cospectrality in relation to another well-studied relaxation of isomorphism, namely k-dimensional Wei<PERSON><PERSON><PERSON><PERSON> (k-WL) indistinguishability.Cospectrality with respect to standard graph matrices such as the adjacency or the Laplacian matrix yields a strictly finer equivalence relation than 2-WL indistinguishability. We show that individualising one vertex plus running 1-WL already subsumes cospectrality with respect to all such graph matrices. Building on this result, we resolve an open problem of <PERSON><PERSON><PERSON> (2010) about spectral invariants and strengthen a result of <PERSON><PERSON> (1981) about commute distances.Looking beyond 2-WL, we devise a hierarchy of graph matrices generalising the adjacency matrix such that k-WL indistinguishability after a fixed number of iterations can be captured as a spectral condition on these matrices. Precisely, we provide a spectral characterisation of k-WL indistinguishability after d iterations, for k,d ∈ ℕ. Our results can be viewed as characterisations of homomorphism indistinguishability over certain graph classes in terms of matrix equations. The study of homomorphism indistinguishability is an emerging field, to which we contribute by extending the algebraic framework of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2020) and <PERSON><PERSON><PERSON> et al. (2022).* The full version [45] of the paper can be accessed at https://arxiv.org/abs/2103.02972", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH87"}, {"primary_key": "1259008", "vector": [], "sparse_vector": [], "title": "Streaming complexity of CSPs with randomly ordered constraints.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Madhu <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We initiate a study of the streaming complexity of constraint satisfaction problems (CSPs) when the constraints arrive in a random order. We show that there exists a CSP, namely $\\textsf{Max-DICUT}$, for which random ordering makes a provable difference. Whereas a $4/9 \\approx 0.445$ approximation of $\\textsf{DICUT}$ requires $\\Omega(\\sqrt{n})$ space with adversarial ordering, we show that with random ordering of constraints there exists a $0.48$-approximation algorithm that only needs $O(\\log n)$ space. We also give new algorithms for $\\textsf{Max-DICUT}$ in variants of the adversarial ordering setting. Specifically, we give a two-pass $O(\\log n)$ space $0.48$-approximation algorithm for general graphs and a single-pass $\\tilde{O}(\\sqrt{n})$ space $0.48$-approximation algorithm for bounded degree graphs. On the negative side, we prove that CSPs where the satisfying assignments of the constraints support a one-wise independent distribution require $\\Omega(\\sqrt{n})$-space for any non-trivial approximation, even when the constraints are randomly ordered. This was previously known only for adversarially ordered constraints. Extending the results to randomly ordered constraints requires switching the hard instances from a union of random matchings to simple Erd\\\"os-Renyi random (hyper)graphs and extending tools that can perform Fourier analysis on such instances. The only CSP to have been considered previously with random ordering is $\\textsf{Max-CUT}$ where the ordering is not known to change the approximability. Specifically it is known to be as hard to approximate with random ordering as with adversarial ordering, for $o(\\sqrt{n})$ space algorithms. Our results show a richer variety of possibilities and motivate further study of CSPs with randomly ordered constraints.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH156"}, {"primary_key": "1259009", "vector": [], "sparse_vector": [], "title": "Packing cycles in planar and bounded-genus graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We devise constant-factor approximation algorithms for finding as many disjoint cycles as possible from a certain family of cycles in a given planar or bounded-genus graph. Here disjoint can mean vertex-disjoint or edge-disjoint, and the graph can be undirected or directed. The family of cycles under consideration must satisfy two properties: it must be uncrossable and allow for an oracle access that finds a weight-minimal cycle in that family for given nonnegative edge weights or (in planar graphs) the union of all remaining cycles in that family after deleting a given subset of edges.Our setting generalizes many problems that were studied separately in the past. For example, three families that satisfy the above properties are (i) all cycles in a directed or undirected graph, (ii) all odd cycles in an undirected graph, and (iii) all cycles in an undirected graph that contain precisely one demand edge, where the demand edges form a subset of the edge set. The latter family (iii) corresponds to the classical disjoint paths problem in fully planar and bounded-genus instances. While constant-factor approximation algorithms were known for edge-disjoint paths in such instances, we improve the constant in the planar case and obtain the first such algorithms for vertex-disjoint paths. We also obtain approximate min-max theorems of the Erdős-Póosa type. For example, the minimum feedback vertex set in a planar digraph is at most 12 times the maximum number of vertex-disjoint cycles.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH79"}, {"primary_key": "1259010", "vector": [], "sparse_vector": [], "title": "A Nearly-Tight Analysis of Multipass Pairing Heaps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The pairing heap, introduced by <PERSON><PERSON> et al. [3], is a self-adjusting heap data structure that is both simple and efficient. A variant introduced in the same paper is the multipass pairing heap. Standard pairing heaps do just two linking passes during delete-min, a pairing pass and an assembly pass. In contrast, multipass pairing heaps do repeated pairing passes, in which nodes are linked in adjacent pairs, until only a minimum-key node remains.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH23"}, {"primary_key": "1259011", "vector": [], "sparse_vector": [], "title": "A Tight Analysis of Slim Heaps and Smooth Heaps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The smooth heap and the closely related slim heap are recently invented self-adjusting implementations of the heap (priority queue) data structure. They are simple to describe and efficient in practice. For both slim and smooth heaps, we derive the following tight bounds on the amortized time per operation: O(log n) for delete-min and delete; O(log log n) for decrease-key; and O(1) for make-heap, find-min, insert, and meld, where n is the current number of items in the heap. These bounds are tight not only for slim and smooth heaps, but for any heap in Iacono and Özkan's pure heap model, intended to capture all \"self-adjusting\" heap implementations. Slim and smooth heaps are the first known data structures to match Iacono and Özkan's lower bounds while satisying the constraints of their model.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH24"}, {"primary_key": "1259012", "vector": [], "sparse_vector": [], "title": "Streaming algorithms for the missing item finding problem.", "authors": ["<PERSON>"], "summary": "Many problems on data streams have been studied at two extremes of difficulty: either allowing randomized algorithms, in the static setting (where they should err with bounded probability on the worst case stream); or when only deterministic and infallible algorithms are required. Some recent works have considered the adversarial setting, in which a randomized streaming algorithm must succeed even on data streams provided by an adaptive adversary that can see the intermediate outputs of the algorithm.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH32"}, {"primary_key": "1259013", "vector": [], "sparse_vector": [], "title": "An Improved Approximation for Maximum Weighted k-Set Packing.", "authors": ["Theophile <PERSON>", "<PERSON>"], "summary": "We consider the weighted k-set packing problem, in which we are given a collection of weighted sets, each with at most k elements and must return a collection of pairwise disjoint sets with maximum total weight. For k = 3, this problem generalizes the classical 3-dimensional matching problem listed as one of the <PERSON><PERSON>'s original 21 NP-complete problems. We give an algorithm attaining an approximation factor of 1.786 for 3-set packing, improving on the recent best result of due to <PERSON><PERSON><PERSON><PERSON><PERSON>.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH42"}, {"primary_key": "1259014", "vector": [], "sparse_vector": [], "title": "Online Lewis Weight Sampling.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The seminal work of <PERSON> and <PERSON><PERSON> [CP15] (STOC 2015) introduced Lewis weight sampling to the theoretical computer science community, which yields fast row sampling algorithms for approximating d-dimensional subspaces of ℓp up to (1 + ε) relative error. Several works have extended this important primitive to other settings, including the online coreset and sliding window models [BDM+20] (FOCS 2020) as well as the adversarial streaming model [BHM+21] (NeurIPS 2021). However, these results are only for p ∈ {1, 2}, and results for p = 1 require a suboptimal Õ(d2/ε2) samples.In this work, we design the first nearly optimal ℓp subspace embeddings for all p ∈ (0, ∞) in the online coreset, sliding window, and the adversarial streaming models. In all three models, our algorithms store Õ(d/ε2) rows for p ∈ (0, 2) and Õ(dp/2/ε2) rows for p ∈ (2, ∞). This answers a substantial generalization of the main open question of [BDM+20], and gives the first results for all p ∉ {1, 2} and achieves nearly optimal sample complexities for all p.Towards our result, we give the first analysis of \"one-shot\" Lewis weight sampling of sampling rows proportionally to their Lewis weights, which gives a sample complexity of Õ(dp/2/ε2) rows for p > 2. Previously, such a sampling scheme was only known to have a sample complexity of Õ(dp/2/ε5) [CP15], whereas a bound of Õ(dp/2/ε2) is known if a more sophisticated recursive sampling algorithm is used [MMWY21, LT91]. Note that the recursive sampling strategy cannot be implemented in an online setting, thus necessitating an analysis of one-shot Lewis weight sampling. Perhaps surprisingly, our analysis crucially uses a novel connection to online numerical linear algebra, even for offline Lewis weight sampling.As an application, we obtain the first one-pass streaming coreset algorithms for (1 + ε) approximation of important generalized linear models, such as logistic regression and p-probit regression. Our upper bounds are parameterized by a complexity parameter μ introduced by [MSSW18], and we also provide the first lower bounds showing that a linear dependence on μ is necessary.* The full version of the paper can be accessed at https://arxiv.org/abs/2207.08268", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH175"}, {"primary_key": "1259015", "vector": [], "sparse_vector": [], "title": "The complete classification for quantified equality constraints.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove that QCSP(ℕ; x = y → y = z) is PSpace-complete, settling a question open for more than ten years. This completes the complexity classification for the QCSP over equality languages as a trichotomy between Logspace, NP-complete and PSpace-complete.We additionally settle the classification for bounded alternation QCSP(Γ), for Γ an equality language. Such problems are either in Logspace, NP-complete, co-NP-complete or rise in complexity in the Polynomial Hierarchy.", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH103"}, {"primary_key": "1259016", "vector": [], "sparse_vector": [], "title": "Higher degree sum-of-squares relaxations robust against oblivious outliers.", "authors": ["Tommaso d&apos;<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider estimation models of the form Y = X* + N, where X* is some m-dimensional structured signal we wish to recover, and N is symmetrically distributed noise that may be unbounded in all but a small α fraction of the entries. This setting captures problems such as (sparse) linear regression, (sparse) principal component analysis (PCA), and tensor PCA, even in the presence of oblivious outliers and heavy-tailed noise.We introduce a family of algorithms that under mild assumptions recover the signal X* in all estimation problems for which there exists a sum-of-squares algorithm that succeeds in recovering the signal X* when the noise N is Gaussian. This essentially shows that it is enough to design a sum-of-squares algorithm for an estimation problem with Gaussian additive noise in order to get the algorithm that works with the symmetric noise model.Our framework extends far beyond previous results on symmetric noise models and is even robust to an ε-fraction of adversarial perturbations. As concrete examples, we investigate two problems for which no efficient algorithms were known to work for heavy-tailed noise: tensor PCA and sparse PCA.For the former, our algorithm recovers the principal component in polynomial time when the signal-to-noise ratio is at least Õ(np/4/ α), that matches (up to logarithmic factors) current best known algorithmic guarantees for Gaussian noise. For the latter, our algorithm runs in quasipolynomial time and matches the state-of-the-art guarantees for quasipolynomial time algorithms in the case of Gaussian noise. Using a reduction from the planted clique problem, we provide evidence that the quasipolynomial time is likely to be necessary for sparse PCA with symmetric noise.In our proofs we use bounds on the covering numbers of sets of pseudo-expectations, which we obtain by certifying in sum-of-squares upper bounds on the Gaussian complexities of sets of solutions. This approach for bounding the covering numbers of sets of pseudo-expectations may be interesting in its own right and may find other application in future works.* This project has received funding from the European Research Council (ERC) under the European Union's Horizon 2020 research and innovation programme (grant agreement No 815464).", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554.CH134"}, {"primary_key": "1278575", "vector": [], "sparse_vector": [], "title": "Proceedings of the 2023 ACM-SIAM Symposium on Discrete Algorithms, SODA 2023, Florence, Italy, January 22-25, 2023", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this work, we prove new bounds on the additive gap between the value of a random integer program max c T x, Ax ≤ b, x ∈ {0, 1} n with m constraints and that of its linear programming relaxation for a wide range of distributions on (A, b, c).Our investigation is motivated by the work of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (SODA '21), who gave a framework for relating the size of Branch-and-Bound (B&B) trees to additive integrality gaps.<PERSON> and <PERSON> (MOR '89) and <PERSON> et al. (Mathematical Programming '22), respectively, showed that for certain random packing and Gaussian IPs, where the entries of A, c are independently distributed according to either the uniform distribution on [0, 1] or the Gaussian distribution N (0, 1), the integrality gap is bounded by O m (log 2 n/n) with probability at least 1 -1/ne -Ω m (1) .In this paper, we generalize these results to the cases where the entries of A are uniformly distributed on an integer interval (e.g., entries in {-1, 0, 1}), and where the columns of A are distributed according to an isotropic logconcave distribution.Second, we substantially improve the success probability to 1 -1/ poly(n), compared to constant probability in prior works (depending on m).Leveraging the connection to Branchand-Bound, our gap results imply that for these IPs B&B trees have size n poly(m) with high probability (i.e., polynomial for fixed m), which significantly extends the class of IPs for which B&B is known to be polynomial.Our main technical contribution and the key to achieving the above results is a new linear discrepancy theorem for random matrices.Our theorem gives general conditions under which a target vector is equal to or very close to a {0, 1} combination of the columns of a random matrix A. Compared to prior results, our theorem handles a much wider range of distributions on A, both continuous and discrete, and achieves success probability exponentially close to 1, as opposed to the constant probability shown in earlier results.Our proof uses a Fourier analytic", "published": "2023-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611977554"}]