[{"primary_key": "3121636", "vector": [], "sparse_vector": [], "title": "Lower bounds for external memory integer sorting via network coding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sorting extremely large datasets is a frequently occuring task in practice. These datasets are usually much larger than the computer’s main memory; thus external memory sorting algorithms, first introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (1988), are often used. The complexity of comparison based external memory sorting has been understood for decades by now, however the situation remains elusive if we assume the keys to be sorted are integers. In internal memory, one can sort a set of n integer keys of Θ(lgn) bits each in O(n) time using the classic Radix Sort algorithm, however in external memory, there are no faster integer sorting algorithms known than the simple comparison based ones. Whether such algorithms exist has remained a central open problem in external memory algorithms for more than three decades. In this paper, we present a tight conditional lower bound on the complexity of external memory sorting of integers. Our lower bound is based on a famous conjecture in network coding by <PERSON> and <PERSON> (2004), who conjectured that network coding cannot help anything beyond the standard multicommodity flow rate in undirected graphs. The only previous work connecting the <PERSON> and <PERSON> conjecture to lower bounds for algorithms is due to <PERSON> et al. (2006). <PERSON> et al. indeed obtain relatively simple lower bounds for oblivious algorithms (the memory access pattern is fixed and independent of the input data). Unfortunately obliviousness is a strong limitations, especially for integer sorting: we show that the <PERSON> and <PERSON> conjecture implies an Ω(nlgn) lower bound for internal memory oblivious sorting when the keys are Θ(lgn) bits. This is in sharp contrast to the classic (non-oblivious) Radi<PERSON> algorithm. Indeed going beyond obliviousness is highly non-trivial; we need to introduce several new methods and involved techniques, which are of their own interest, to obtain our tight lower bound for external memory integer sorting.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316337"}, {"primary_key": "3121637", "vector": [], "sparse_vector": [], "title": "O(log2 k / log log k)-approximation algorithm for directed Steiner tree: a tight quasi-polynomial-time algorithm.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Bundit Laekhanukit", "<PERSON>"], "summary": "In the Directed Steiner Tree (DST) problem we are given an n-vertex directed edge-weighted graph, a root r , and a collection of k terminal nodes. Our goal is to find a minimum-cost subgraph that contains a directed path from r to every terminal. We present an O(log^2 k /log log k )-approximation algorithm for DST that runs in quasi-polynomial-time, i.e., in time n^polylog(k). By making standard complexity assumptions, we show the matching lower bound of Omega(log^2 k/loglogk) for the class of quasi-polynomial time algorithms, meaning that our approximation ratio is asymptotically the best possible. This is the first improvement on the DST problem since the classical quasi-polynomial-time O (log^3 k ) approximation algorithm by <PERSON><PERSON><PERSON> et al. [SODA'98J. Algorithms'99]. (The paper erroneously claims an O (log^2 k ) approximation due to a mistake in prior work.)", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316349"}, {"primary_key": "3121638", "vector": [], "sparse_vector": [], "title": "Private selection from private candidates.", "authors": ["Jingcheng Liu", "<PERSON><PERSON>"], "summary": "Differentially Private algorithms often need to select the best amongst many candidate options. Classical works on this selection problem require that the candidates' goodness, measured as a real-valued score function, does not change by much when one person's data changes. In many applications such as hyperparameter optimization, this stability assumption is much too strong. In this work, we consider the selection problem under a much weaker stability assumption on the candidates, namely that the score functions are differentially private. Under this assumption, we present algorithms that are near-optimal along the three relevant dimensions: privacy, utility and computational efficiency.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316377"}, {"primary_key": "3121639", "vector": [], "sparse_vector": [], "title": "Unconstrained submodular maximization with constant adaptive complexity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we consider the unconstrained submodular maximization problem. We propose the first algorithm for this problem that achieves a tight (1/2−ε)-approximation guarantee using Õ(ε−1) adaptive rounds and a linear number of function evaluations. No previously known algorithm for this problem achieves an approximation ratio better than 1/3 using less than Ω(n) rounds of adaptivity, where n is the size of the ground set. Moreover, our algorithm easily extends to the maximization of a non-negative continuous DR-submodular function subject to a box constraint, and achieves a tight (1/2−ε)-approximation guarantee for this problem while keeping the same adaptive and query complexities.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316327"}, {"primary_key": "3121640", "vector": [], "sparse_vector": [], "title": "Random walks and forbidden minors II: a poly(d ε-1)-query tester for minor-closed properties of bounded degree graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Let G be a graph with n vertices and maximum degree d. Fix some minor-closed property P (such as planarity). We say that G is ε-far from P if one has to remove ε dn edges to make it have P. The problem of property testing P was introduced in the seminal work of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (STOC 2008) that gave a tester with query complexity triply exponential in ε−1. <PERSON><PERSON><PERSON> (TALG 2015) have given the best tester to date, with a quasipolynomial (in ε−1) query complexity. It is an open problem to get property testers whose query complexity is (dε−1), even for planarity.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316330"}, {"primary_key": "3121641", "vector": [], "sparse_vector": [], "title": "Quantum Lovász local lemma: Shearer&a<PERSON>s;s bound is tight.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON>"], "summary": "Lovász Local Lemma (LLL) is a very powerful tool in combinatorics and probability theory to show the possibility of avoiding all \"bad\" events under some \"weakly dependent\" condition. Over the last decades, the algorithmic aspect of LLL has also attracted lots of attention in theoretical computer science. A tight criterion under which the abstract version LLL (ALLL) holds was given by <PERSON><PERSON>. It turns out that <PERSON><PERSON>'s bound is generally not tight for variable version LLL (VLLL). Recently, <PERSON><PERSON><PERSON><PERSON> et al. introduced a quantum version LLL (QLLL), which was then shown to be powerful for the quantum satisfiability problem.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316392"}, {"primary_key": "3121642", "vector": [], "sparse_vector": [], "title": "Gentle measurement of quantum states and differential privacy.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In differential privacy (DP), we want to query a database about n users, in a way that “leaks at most ε about any individual user,” even conditioned on any outcome of the query. Meanwhile, in gentle measurement, we want to measure n quantum states, in a way that “damages the states by at most α,” even conditioned on any outcome of the measurement. In both cases, we can achieve the goal by techniques like deliberately adding noise to the outcome before returning it. This paper proves a new and general connection between the two subjects. Specifically, we show that on products of n quantum states, any measurement that is α-gentle for small α is also O( α) -DP, and any product measurement that is ε-DP is also O( ε√n) -gentle.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316378"}, {"primary_key": "3121643", "vector": [], "sparse_vector": [], "title": "Dynamic set cover: improved algorithms and lower bounds.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give new upper and lower bounds for the dynamic set cover problem. First, we give a (1+є) f-approximation for fully dynamic set cover in O(f2logn/є5) (amortized) update time, for any є > 0, where f is the maximum number of sets that an element belongs to. In the decremental setting, the update time can be improved to O(f2/є5), while still obtaining an (1+є) f-approximation. These are the first algorithms that obtain an approximation factor linear in f for dynamic set cover, thereby almost matching the best bounds known in the offline setting and improving upon the previous best approximation of O(f2) in the dynamic setting.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316376"}, {"primary_key": "3121644", "vector": [], "sparse_vector": [], "title": "Why extension-based proofs fail.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It is impossible to deterministically solve wait-free consensus in an asynchronous system. The classic proof uses a valency argument, which constructs an infinite execution by repeatedly extending a finite execution. We introduce extension-based proofs, a class of impossibility proofs that are modelled as an interaction between a prover and a protocol and that include valency arguments.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316407"}, {"primary_key": "3121645", "vector": [], "sparse_vector": [], "title": "Private PAC learning implies finite Littlestone dimension.", "authors": ["Noga Alon", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that every approximately differentially private learning algorithm (possibly improper) for a class H with Littlestone dimension d requires Ω(log*(d)) examples. As a corollary it follows that the class of thresholds over ℕ can not be learned in a private manner; this resolves open questions due to [<PERSON><PERSON> et al. 2015] and [<PERSON><PERSON><PERSON> and <PERSON>, 2015]. We leave as an open question whether every class with a finite Littlestone dimension can be learned by an approximately differentially private algorithm.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316312"}, {"primary_key": "3121646", "vector": [], "sparse_vector": [], "title": "An exponential lower bound on the sub-packetization of MSR codes.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami"], "summary": "An (n,k,ℓ)-vector MDS code is a F-linear subspace of (Fℓ)n (for some field F) of dimension kℓ, such that any k (vector) symbols of the codeword suffice to determine the remaining r=n−k (vector) symbols. The length ℓ of each codeword symbol is called the Sub-Packetization of the code. Such a code is called minimum storage regenerating (MSR), if any single symbol of a codeword can be recovered by downloading ℓ/r field elements (which is known to be the least possible) from each of the other symbols.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316387"}, {"primary_key": "3121647", "vector": [], "sparse_vector": [], "title": "Log-concave polynomials II: high-dimensional walks and an FPRAS for counting bases of a matroid.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We design an FPRAS to count the number of bases of any matroid given by an independent set oracle, and to estimate the partition function of the random cluster model of any matroid in the regime where 0<q<1. Consequently, we can sample random spanning forests in a graph and estimate the reliability polynomial of any matroid. We also prove the thirty year old conjecture of <PERSON><PERSON><PERSON> and <PERSON><PERSON> that the bases exchange graph of any matroid has edge expansion at least 1.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316385"}, {"primary_key": "3121648", "vector": [], "sparse_vector": [], "title": "Quantum weak coin flipping.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We investigate weak coin flipping, a fundamental cryptographic primitive where two distrustful parties need to remotely establish a shared random bit. A cheating player can try to bias the output bit towards a preferred value. For weak coin flipping the players have known opposite preferred values. A weak coin-flipping protocol has a bias $\\epsilon$ if neither player can force the outcome towards their preferred value with probability more than $\\frac{1}{2}+\\epsilon$. While it is known that all classical protocols have $\\epsilon=\\frac{1}{2}$, <PERSON><PERSON><PERSON> showed in 2007 [arXiv:0711.4114] that quantumly weak coin flipping can be achieved with arbitrarily small bias (near perfect) but the best known explicit protocol has bias $1/6$ (also due to <PERSON><PERSON><PERSON>, 2005 [Phys. Rev. A 72, 022341]). We propose a framework to construct new explicit protocols achieving biases below $1/6$. In particular, we construct explicit unitaries for protocols with bias approaching $1/10$. To go below, we introduce what we call the Elliptic Monotone Align (EMA) algorithm which, together with the framework, allows us to numerically construct protocols with arbitrarily small biases.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316306"}, {"primary_key": "3121649", "vector": [], "sparse_vector": [], "title": "Polynomial pass lower bounds for graph streaming algorithms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present new lower bounds that show that a polynomial number of passes are necessary for solving some fundamental graph problems in the streaming model of computation. For instance, we show that any streaming algorithm that finds a weighted minimum s-t cut in an n-vertex undirected graph requires n2−o(1) space unless it makes nΩ(1) passes over the stream.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316361"}, {"primary_key": "3121650", "vector": [], "sparse_vector": [], "title": "A universal sampling method for reconstructing signals with simple Fourier transforms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Reconstructing continuous signals based on a small number of discrete samples is a fundamental problem across science and engineering. We are often interested in signals with \"simple'' Fourier structure -- e.g., those involving frequencies within a bounded range, a small number of frequencies, or a few blocks of frequencies -- i.e., bandlimited, sparse, and multiband signals, respectively. More broadly, any prior knowledge on a signal's Fourier power spectrum can constrain its complexity. Intuitively, signals with more highly constrained Fourier structure require fewer samples to reconstruct.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316363"}, {"primary_key": "3121651", "vector": [], "sparse_vector": [], "title": "Canonical form for graphs in quasipolynomial time: preliminary report.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We outline how to turn the author's quasipolynomial-time graph isomorphism test into a construction of a canonical form within the same time bound. The proof involves a nontrivial modification of the central symmetry-breaking tool, the construction of a canonical relational structure of logarithmic arity on the ideal domain based on local certificates.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316356"}, {"primary_key": "3121652", "vector": [], "sparse_vector": [], "title": "The communication complexity of local search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study a communication variant of local search. There is some fixed, commonly known graph G. <PERSON> holds fA and <PERSON> holds fB, both are functions that specify a value for each vertex. The goal is to find a local maximum of fA+fB with respect to G, i.e., a vertex v for which (fA+fB)(v)≥ (fA+fB)(u) for each neighbor u of v.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316354"}, {"primary_key": "3121653", "vector": [], "sparse_vector": [], "title": "Quantum state certification.", "authors": ["<PERSON><PERSON><PERSON>", "Ryan <PERSON>&<PERSON>;Donnell", "<PERSON>"], "summary": "We consider the problem of quantum state certification, where one is given n copies of an unknown d-dimensional quantum mixed state ρ, and one wants to test whether ρ is equal to some known mixed state σ or else is є-far from σ. The goal is to use notably fewer copies than the Ω(d2) needed for full tomography on ρ (i.e., density estimation). We give two robust state certification algorithms: one with respect to fidelity using n = O(d/є) copies, and one with respect to trace distance using n = O(d/є2) copies. The latter algorithm also applies when σ is unknown as well. These copy complexities are optimal up to constant factors.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316344"}, {"primary_key": "3121654", "vector": [], "sparse_vector": [], "title": "An optimal approximation for submodular maximization under a matroid constraint in the adaptive complexity model.", "authors": ["<PERSON>", "<PERSON>via<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we study submodular maximization under a matroid constraint in the adaptive complexity model. This model was recently introduced in the context of submodular optimization to quantify the information theoretic complexity of black-box optimization in a parallel computation model. Informally, the adaptivity of an algorithm is the number of sequential rounds it makes when each round can execute polynomially-many function evaluations in parallel. Since submodular optimization is regularly applied on large datasets we seek algorithms with low adaptivity to enable speedups via parallelization. Consequently, a recent line of work has been devoted to designing constant factor approximation algorithms for maximizing submodular functions under various constraints in the adaptive complexity model.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316304"}, {"primary_key": "3121655", "vector": [], "sparse_vector": [], "title": "On a generalization of iterated and randomized rounding.", "authors": ["<PERSON><PERSON>"], "summary": "We give a general method for rounding linear programs that combines the commonly used iterated rounding and randomized rounding techniques. In particular, we show that whenever iterated rounding can be applied to a problem with some slack, there is a randomized procedure that returns an integral solution that satisfies the guarantees of iterated rounding and also has concentration properties. We use this to give new results for several classic problems where iterated rounding has been useful.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316313"}, {"primary_key": "3121656", "vector": [], "sparse_vector": [], "title": "Oblivious dimension reduction for k-means: beyond subspaces and the <PERSON> lemma.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that for n points in d-dimensional Euclidean space, a data oblivious random projection of the columns onto m∈ O((logk+loglogn)ε−6log1/ε) dimensions is sufficient to approximate the cost of all k-means clusterings up to a multiplicative (1±ε) factor. The previous-best upper bounds on m are O(logn· ε−2) given by a direct application of the Johnson-<PERSON>uss Lemma, and O(kε−2) given by [<PERSON> et al.-STOC'15].", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316318"}, {"primary_key": "3121657", "vector": [], "sparse_vector": [], "title": "Planar graphs of bounded degree have bounded queue number.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A queue layout of a graph consists of a linear order of its vertices and a partition of its edges into queues, so that no two independent edges of the same queue are nested. The queue number of a graph is the minimum number of queues required by any of its queue layouts. A long-standing conjecture by <PERSON>, <PERSON> and <PERSON> states that the queue number of planar graphs is bounded.This conjecture has been partially settled in the positive for several sub- families of planar graphs (most of which have bounded treewidth).", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316324"}, {"primary_key": "3121658", "vector": [], "sparse_vector": [], "title": "Achieving optimal backlog in multi-processor cup games.", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON>"], "summary": "Many problems in processor scheduling, deamortization, and buffer management can be modeled as single- and multi-processor cup games.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316342"}, {"primary_key": "3121659", "vector": [], "sparse_vector": [], "title": "Distributed exact weighted all-pairs shortest paths in near-linear time.", "authors": ["<PERSON>", "Danupon <PERSON>"], "summary": "In the distributed all-pairs shortest paths problem (APSP), every node in the weighted undirected distributed network (the CONGEST model) needs to know the distance from every other node using least number of communication rounds (typically called time complexity). The problem admits (1+o(1))-approximation Θ(n)-time algorithm and a nearly-tight Ω(n) lower bound [<PERSON><PERSON><PERSON>, STOC'14; <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>ir <PERSON>'15]. For the exact case, <PERSON><PERSON> [STOC'17] presented an O(n5/3 log2/3 n) time bound, which was later improved to Õ(n5/4) in [Huang, Nanongkai, Saranurak FOCS'17].It was shown that any super-linear lower bound (in n) requires a new technique [<PERSON><PERSON>r<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, DISC'17], but otherwise it remained widely open whether there exists a Õ(n)-time algorithm for the exact case, which would match the best possible approximation algorithm. This paper resolves this question positively: we present a randomized (Las Vegas) Õ(n)-time algorithm, matching the lower bound up to polylogarithmic factors. Like the previous Õ(n5/4) bound, our result works for directed graphs with zero (and even negative) edge weights. In addition to the improved running time, our algorithm works in a more general setting than that required by the previous Õ(n5/4) bound; in our setting (i) the communication is only along edge directions (as opposed to bidirectional), and (ii) edge weights are arbitrary (as opposed to integers in {1, 2, ... poly(n)}). The previously best algorithm for this more difficult setting required Õ(n3/2) time [Agarwal and Ramachandran, ArXiv'18] (this can be improved to Õ(n4/3) if one allows bidirectional communication).", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316326"}, {"primary_key": "3121660", "vector": [], "sparse_vector": [], "title": "Decremental strongly-connected components and single-source reachability in near-linear time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Computing the Strongly-Connected Components (SCCs) in a graph G = (V, E) is known to take only O(m + n) time using an algorithm by <PERSON><PERSON><PERSON> from 1972[SICOMP 72] where m = |E|, n = |V |. For fully-dynamic graphs, conditional lower bounds provide evidence that the update time cannot be improved by polynomial factors over recomputing the SCCs from scratch after every update. Nevertheless, substantial progress has been made to find algorithms with fast update time for decremental graphs, i.e. graphs that undergo edge deletions. In this paper, we present the first algorithm for general decremental graphs that maintains the SCCs in total update time Õ(m)1, thus only a polylogarithmic factor from the optimal running time. Previously such a result was only known for the special case of planar graphs [<PERSON><PERSON> et al, STOC 17]. Our result should be compared to the formerly best algorithm for general graphs achieving Õ(mn) total update time by <PERSON><PERSON><PERSON> et.al. [FOCS 16] which improved upon a breakthrough result leading to O(mn0.9+o(1)) total update time by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [STOC 14, ICALP 15]; these results in turn improved upon the longstanding bound of O(mn) by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [STOC 04]. All of the above results also apply to the decremental Single-Source Reachability (SSR) problem, which can be reduced to decrementally maintaining SCCs. A bound of O(mn) total update time for decremental SSR was established already in 1981 by Even and <PERSON><PERSON><PERSON> [JACM 81].", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316335"}, {"primary_key": "3121661", "vector": [], "sparse_vector": [], "title": "Optimal (and benchmark-optimal) competition complexity for additive buyers over independent items.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Competition Complexity of an auction setting refers to the number of additional bidders necessary in order for the (deterministic, prior-independent, dominant strategy truthful) Vick<PERSON>-Clarke-<PERSON>s mechanism to achieve greater revenue than the (randomized, prior-dependent, Bayesian-truthful) optimal mechanism without the additional bidders.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316405"}, {"primary_key": "3121662", "vector": [], "sparse_vector": [], "title": "Weak zero-knowledge beyond the black-box barrier.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The round complexity of zero-knowledge protocols is a long-standing open question, yet to be settled under standard assumptions. So far, the question has appeared equally challenging for relaxations such as weak zero-knowledge and witness hiding. Protocols satisfying these relaxed notions under standard assumptions have at least four messages, just like full-fledged zero-knowledge. The difficulty in improving round complexity stems from a fundamental barrier: none of these notions can be achieved in three messages via reductions (or simulators) that treat the verifier as a black box.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316382"}, {"primary_key": "3121663", "vector": [], "sparse_vector": [], "title": "Good approximate quantum LDPC codes from spacetime circuit Hamiltonians.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study approximate quantum low-density parity-check (QLDPC) codes, which are approximate quantum error-correcting codes specified as the ground space of a frustration-free local Hamiltonian, whose terms do not necessarily commute.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316384"}, {"primary_key": "3121664", "vector": [], "sparse_vector": [], "title": "1+ε approximation of tree edit distance in quadratic time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Edit distance is one of the most fundamental problems in computer science. Tree edit distance is a natural generalization of edit distance to ordered rooted trees. Such a generalization extends the applications of edit distance to areas such as computational biology, structured data analysis (e.g., XML), image analysis, and compiler optimization. Perhaps the most notable application of tree edit distance is in the analysis of RNA molecules in computational biology where the secondary structure of RNA is typically represented as a rooted tree.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316388"}, {"primary_key": "3121665", "vector": [], "sparse_vector": [], "title": "Bridging between 0/1 and linear programming via random walks.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami"], "summary": "Under the Strong Exponential Time Hypothesis, an integer linear program with n Boolean-valued variables and m equations cannot be solved in cn time for any constant c < 2. If the domain of the variables is relaxed to [0,1], the associated linear program can of course be solved in polynomial time. In this work, we give a natural algorithmic bridging between these extremes of 0-1 and linear programming. Specifically, for any subset (finite union of intervals) E ⊂ [0,1] containing {0,1}, we give a random-walk based algorithm with runtime OE((2−measure(E))npoly(n,m)) that finds a solution in En to any n-variable linear program with m constraints that is feasible over {0,1}n. Note that as E expands from {0,1} to [0,1], the runtime improves smoothly from 2n to polynomial.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316347"}, {"primary_key": "3121666", "vector": [], "sparse_vector": [], "title": "CSPs with global modular constraints: algorithms and hardness via polynomial representations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>en<PERSON><PERSON>wami"], "summary": "We study the complexity of Boolean constraint satisfaction problems (CSPs) when the assignment must have Hamming weight in some congruence class modulo M, for various choices of the modulus M. Due to the known classification of tractable Boolean CSPs, this mainly reduces to the study of three cases: 2-SAT, HORN-SAT, and LIN-2 (linear equations mod 2). We classify the moduli M for which these respective problems are polynomial time solvable, and when they are not (assuming the ETH). Our study reveals that this modular constraint lends a surprising richness to these classic, well-studied problems, with interesting broader connections to complexity theory and coding theory. The HORN-SAT case is connected to the covering complexity of polynomials representing the NAND function mod M. The LIN-2 case is tied to the sparsity of polynomials representing the OR function mod M, which in turn has connections to modular weight distribution properties of linear codes and locally decodable codes. In both cases, the analysis of our algorithm as well as the hardness reduction rely on these polynomial representations, highlighting an interesting algebraic common ground between hard cases for our algorithms and the gadgets which show hardness. These new complexity measures of polynomial representations merit further study.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316401"}, {"primary_key": "3121667", "vector": [], "sparse_vector": [], "title": "Learning restricted <PERSON><PERSON>mann machines via influence maximization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graphical models are a rich language for describing high-dimensional distributions in terms of their dependence structure. While there are algorithms with provable guarantees for learning undirected graphical models in a variety of settings, there has been much less progress in the important scenario when there are latent variables. Here we study Restricted Boltzmann Machines (or RBMs), which are a popular model with wide-ranging applications in dimensionality reduction, collaborative filtering, topic modeling, feature extraction and deep learning.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316372"}, {"primary_key": "3121668", "vector": [], "sparse_vector": [], "title": "Approximating APSP without scaling: equivalence of approximate min-plus and exact min-max.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>'s (1+ε)-approximation algorithm for the All Pairs Shortest Path (APSP) problem runs in time Õ(nω/ε logW), where ω ≤ 2.373 is the exponent of matrix multiplication and W denotes the largest weight. This can be used to approximate several graph characteristics including the diameter, radius, median, minimum-weight triangle, and minimum-weight cycle in the same time bound.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316373"}, {"primary_key": "3121669", "vector": [], "sparse_vector": [], "title": "Competitively chasing convex bodies.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>zhi Li", "<PERSON>"], "summary": "Let F be a family of sets in some metric space. In the F-chasing problem, an online algorithm observes a request sequence of sets in F and responds (online) by giving a sequence of points in these sets. The movement cost is the distance between consecutive such points. The competitive ratio is the worst case ratio (over request sequences) between the total movement of the online algorithm and the smallest movement one could have achieved by knowing in advance the request sequence. The family F is said to be chaseable if there exists an online algorithm with finite competitive ratio. In 1991, <PERSON><PERSON> and <PERSON> conjectured that the family of convex sets in Euclidean space is chaseable. We prove this conjecture.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316314"}, {"primary_key": "3121670", "vector": [], "sparse_vector": [], "title": "Algebraic approach to promise constraint satisfaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The complexity and approximability of the constraint satisfaction problem (CSP) has been actively studied over the last 20 years. A new version of the CSP, the promise CSP (PCSP) has recently been proposed, motivated by open questions about the approximability of variants of satisfiability and graph colouring. The PCSP significantly extends the standard decision CSP. The complexity of CSPs with a fixed constraint language on a finite domain has recently been fully classified, greatly guided by the algebraic approach, which uses polymorphisms — high-dimensional symmetries of solution spaces — to analyse the complexity of problems. The corresponding classification for PCSPs is wide open and includes some long-standing open questions, such as the complexity of approximate graph colouring, as special cases.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316300"}, {"primary_key": "3121671", "vector": [], "sparse_vector": [], "title": "Fiat-Shamir: from practice to theory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give new instantiations of the <PERSON><PERSON><PERSON><PERSON><PERSON> transform using explicit, efficiently computable hash functions. We improve over prior work by reducing the security of these protocols to qualitatively simpler and weaker computational hardness assumptions. As a consequence of our framework, we obtain the following concrete results.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316380"}, {"primary_key": "3121672", "vector": [], "sparse_vector": [], "title": "The structure of optimal private tests for simple hypotheses.", "authors": ["Clément L. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hypothesis testing plays a central role in statistical inference, and is used in many settings where privacy concerns are paramount. This work answers a basic question about privately testing simple hypotheses: given two distributions P and Q, and a privacy level ε, how many i.i.d. samples are needed to distinguish P from Q subject to ε-differential privacy, and what sort of tests have optimal sample complexity? Specifically, we characterize this sample complexity up to constant factors in terms of the structure of P and Q and the privacy level ε, and show that this sample complexity is achieved by a certain randomized and clamped variant of the log-likelihood ratio test. Our result is an analogue of the classical Neyman-Pearson lemma in the setting of private hypothesis testing. We also give an application of our result to the private change-point detection. Our characterization applies more generally to hypothesis tests satisfying essentially any notion of algorithmic stability, which is known to imply strong generalization bounds in adaptive data analysis, and thus our results have applications even when privacy is not a primary concern.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316336"}, {"primary_key": "3121673", "vector": [], "sparse_vector": [], "title": "Explicit N-vertex graphs with maximum degree K and diameter [1+o(1)] logK-1 N for each K-1 a prime power.", "authors": ["<PERSON>"], "summary": "Here we first present the solution of a long-standing open question–the explicit construction of an infinite family of N-vertex cubic graphs that have diameter [1+o(1)]log2 N. We then extend the techniques to construct, for each K of the form 2s+1 or K=ps+1; s an integer and p a prime, an infinite family of K-regular graphs on N vertices with diameter [1+o(1)]logK−1 N.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316399"}, {"primary_key": "3121674", "vector": [], "sparse_vector": [], "title": "Approximation algorithms for minimum norm and ordered optimization problems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In many optimization problems, a feasible solution induces a multi-dimensional cost vector. For example, in load-balancing a schedule induces a load vector across the machines. In k-clustering, opening k facilities induces an assignment cost vector across the clients. Typically, one seeks a solution which either minimizes the sum- or the max- of this vector, and these problems (makespan minimization, k-median, and k-center) are classic NP-hard problems which have been extensively studied.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316322"}, {"primary_key": "3121675", "vector": [], "sparse_vector": [], "title": "Almost optimal distance oracles for planar graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present new tradeoffs between space and query-time for exact distance oracles in directed weighted planar graphs. These tradeoffs are almost optimal in the sense that they are within polylogarithmic, subpolynomial or arbitrarily small polynomial factors from the naïve linear space, constant query-time lower bound. These tradeoffs include: (i) an oracle with space O(n1+є) and query-time Õ(1) for any constant є>0, (ii) an oracle with space Õ(n) and query-time O(nє) for any constant є>0, and (iii) an oracle with space n1+o(1) and query-time no(1).", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316316"}, {"primary_key": "3121676", "vector": [], "sparse_vector": [], "title": "Efficient profile maximum likelihood for universal symmetric property estimation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Estimating symmetric properties of a distribution, e.g. support size, coverage, entropy, distance to uniformity, are among the most fundamental problems in algorithmic statistics. While these properties have been studied extensively and separate optimal estimators have been produced, in striking recent work <PERSON> et al. provided a single estimator that is competitive for each. They showed that the value of the property on the distribution that approximately maximizes profile likelihood (PML), i.e. the probability of observed frequency of frequencies, is sample competitive with respect to a broad class of estimators. Unfortunately, prior to this work, there was no known polynomial time algorithm to compute such an approximation or use PML to obtain a universal plug-in estimator.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316398"}, {"primary_key": "3121677", "vector": [], "sparse_vector": [], "title": "The log-approximate-rank conjecture is false.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct a simple and total XOR function F on 2n variables that has only O(√n) spectral norm, O(n2) approximate rank and O(n2.5) approximate nonnegative rank. We show it has polynomially large randomized bounded-error communication complexity of Ω(√n). This yields the first exponential gap between the logarithm of the approximate rank and randomized communication complexity for total functions. Thus F witnesses a refutation of the Log-Approximate-Rank Conjecture (LARC) which was posed by <PERSON> and <PERSON> as a very natural analogue for randomized communication of the still unresolved Log-Rank Conjecture for deterministic communication. The best known previous gap for any total function between the two measures is a recent 4th-power separation by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316353"}, {"primary_key": "3121678", "vector": [], "sparse_vector": [], "title": "Parallelizing greedy for submodular set function maximization in matroids and beyond.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider parallel, or low adaptivity, algorithms for submodular function maximization. This line of work was recently initiated by <PERSON><PERSON> and <PERSON> and has already led to several interesting results on the cardinality constraint and explicit packing constraints. An important open problem is the classical setting of matroid constraint, which has been instrumental for developments in submodular function maximization. In this paper we develop a general strategy to parallelize the well-studied greedy algorithm and use it to obtain a randomized (1 / 2 − є)-approximation in O( log2(n) / 2 ) rounds of adaptivity. We rely on this algorithm, and an elegant amplification approach due to <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> to obtain a fractional solution that yields a near-optimal randomized ( 1 − 1/e − є )-approximation in O( log2(n) / є3 ) rounds of adaptivity. For non-negative functions we obtain a ( 3−2√2 − є )-approximation and a fractional solution that yields a ( 1 / e − є)-approximation. Our approach for parallelizing greedy yields approximations for intersections of matroids and matchoids, and the approximation ratios are comparable to those known for sequential greedy.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316406"}, {"primary_key": "3121679", "vector": [], "sparse_vector": [], "title": "Beyond the low-degree algorithm: mixtures of subcubes and their applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce the problem of learning mixtures of k subcubes over {0,1}n, which contains many classic learning theory problems as a special case (and is itself a special case of others). We give a surprising nO(logk)-time learning algorithm based on higher-order multilinear moments. It is not possible to learn the parameters because the same distribution can be represented by quite different models. Instead, we develop a framework for reasoning about how multilinear moments can pinpoint essential features of the mixture, like the number of components.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316375"}, {"primary_key": "3121680", "vector": [], "sparse_vector": [], "title": "Bootstrapping results for threshold circuits &quot;just beyond&quot; known lower bounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The best known lower bounds for the circuit class TC0 are only slightly super-linear. Similarly, the best known algorithm for derandomization of this class is an algorithm for quantified derandomization (i.e., a weak type of derandomization) of circuits of slightly super-linear size. In this paper we show that even very mild quantitative improvements of either of the two foregoing results would already imply super-polynomial lower bounds for TC0. Specifically:", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316333"}, {"primary_key": "3121681", "vector": [], "sparse_vector": [], "title": "Testing unateness nearly optimally.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present an Õ(n2/3/є2)-query algorithm that tests whether an unknown Boolean function f∶{0,1}n→ {0,1} is unate (i.e., every variable is either non-decreasing or non-increasing) or є-far from unate. The upper bound is nearly optimal given the Ω(n2/3) lower bound of <PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (2017). The algorithm builds on a novel use of the binary search procedure and its analysis over long random paths.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316351"}, {"primary_key": "3121682", "vector": [], "sparse_vector": [], "title": "Finding a Nash equilibrium is no easier than breaking Fiat-Shamir.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Fiat-Shamir heuristic transforms a public-coin interactive proof into a non-interactive argument, by replacing the verifier with a cryptographic hash function that is applied to the protocol's transcript. Constructing hash functions for which this transformation is sound is a central and long-standing open question in cryptography.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316400"}, {"primary_key": "3121683", "vector": [], "sparse_vector": [], "title": "A new algorithm for decremental single-source shortest paths with applications to vertex-capacitated flow and cut problems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the vertex-decremental Single-Source Shortest Paths (SSSP) problem: given an undirected graph G=(V,E) with lengths ℓ(e)≥ 1 on its edges that undergoes vertex deletions, and a source vertex s, we need to support (approximate) shortest-path queries in G: given a vertex v, return a path connecting s to v, whose length is at most (1+є) times the length of the shortest such path, where є is a given accuracy parameter. The problem has many applications, for example to flow and cut problems in vertex-capacitated graphs. Decremental SSSP is a fundamental problem in dynamic algorithms that has been studied extensively, especially in the more standard edge-decremental setting, where the input graph G undergoes edge deletions. The classical algorithm of Even and Shiloach supports exact shortest-path queries in O(mn) total update time. A series of recent results have improved this bound to O(m1+o(1)logL), where L is the largest length of any edge. However, these improved results are randomized algorithms that assume an oblivious adversary. To go beyond the oblivious adversary restriction, recently, <PERSON>, and <PERSON> and Chechik designed deterministic algorithms for the problem, with total update time Õ(n2logL), that by definition work against an adaptive adversary. Unfortunately, their algorithms introduce a new limitation, namely, they can only return the approximate length of a shortest path, and not the path itself. Many applications of the decremental SSSP problem, including the ones considered in this paper, crucially require both that the algorithm returns the approximate shortest paths themselves and not just their lengths, and that it works against an adaptive adversary. Our main result is a randomized algorithm for vertex-decremental SSSP with total expected update time O(n2+o(1)logL), that responds to each shortest-path query in Õ(nlogL) time in expectation, returning a (1+є)-approximate shortest path. The algorithm works against an adaptive adversary. The main technical ingredient of our algorithm is an Õ(|E(G)|+ n1+o(1))-time algorithm to compute a core decomposition of a given dense graph G, which allows us to compute short paths between pairs of query vertices in G efficiently. We use our result for vertex-decremental SSSP to obtain (1+є)-approximation algorithms for maximum s-t flow and minimum s-t cut in vertex-capacitated graphs, in expected time n2+o(1), and an O(log4n)-approximation algorithm for the vertex version of the sparsest cut problem with expected running time n2+o(1). These results improve upon the previous best known algorithms for these problems in the regime where m= ω(n1.5 + o(1)).", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316320"}, {"primary_key": "3121684", "vector": [], "sparse_vector": [], "title": "The online k-taxi problem.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the online k-taxi problem, a generalization of the k-server problem, in which k taxis serve a sequence of requests in a metric space. A request consists of two points s and t, representing a passenger that wants to be carried by a taxi from s to t. The goal is to serve all requests while minimizing the total distance traveled by all taxis. The problem comes in two flavors, called the easy and the hard k-taxi problem: In the easy k-taxi problem, the cost is defined as the total distance traveled by the taxis; in the hard k-taxi problem, the cost is only the distance of empty runs.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316370"}, {"primary_key": "3121685", "vector": [], "sparse_vector": [], "title": "Solving linear programs in the current matrix multiplication time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper shows how to solve linear programs of the form minAx=b,x≥0 c⊤x with n variables in time O*((nω+n2.5−α/2+n2+1/6) log(n/δ)) where ω is the exponent of matrix multiplication, α is the dual exponent of matrix multiplication, and δ is the relative accuracy. For the current value of ω∼2.37 and α∼0.31, our algorithm takes O*(nω log(n/δ)) time. When ω = 2, our algorithm takes O*(n2+1/6 log(n/δ)) time.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316303"}, {"primary_key": "3121686", "vector": [], "sparse_vector": [], "title": "New polynomial delay bounds for maximal subgraph enumeration by proximity search.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we propose polynomial delay algorithms for several maximal subgraph listing problems, by means of a seemingly novel technique which we call proximity search. Our result involves modeling the space of solutions as an implicit directed graph called \"solution graph\", a method common to other enumeration paradigms such as reverse search. Such methods, however, can become inefficient due to this graph having vertices with high (potentially exponential) degree. The novelty of our algorithm consists in providing a technique for generating better solution graphs, reducing the out-degree of its vertices with respect to existing approaches, and proving that it remains strongly connected. Applying this technique, we obtain polynomial delay listing algorithms for several problems for which output-sensitive results were, to the best of our knowledge, not known. These include Maximal Bipartite Subgraphs, Maximal k-Degenerate Subgraphs (for bounded k), Maximal Induced Chordal Subgraphs, and Maximal Induced Trees. We present these algorithms, and give insight on how this general technique can be applied to other problems.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316402"}, {"primary_key": "3121687", "vector": [], "sparse_vector": [], "title": "The reachability problem for Petri nets is not elementary.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Petri nets, also known as vector addition systems, are a long established model of concurrency with extensive applications in modelling and analysis of hardware, software and database systems, as well as chemical, biological and business processes. The central algorithmic problem for Petri nets is reachability: whether from the given initial configuration there exists a sequence of valid execution steps that reaches the given final configuration. The complexity of the problem has remained unsettled since the 1960s, and it is one of the most prominent open questions in the theory of verification. Decidability was proved by <PERSON><PERSON> in his seminal STOC 1981 work, and the currently best published upper bound is non-primitive recursive A<PERSON>mannian of <PERSON><PERSON> and <PERSON> from LICS 2019. We establish a non-elementary lower bound, i.e. that the reachability problem needs a tower of exponentials of time and space. Until this work, the best lower bound has been exponential space, due to <PERSON><PERSON> in 1976. The new lower bound is a major breakthrough for several reasons. Firstly, it shows that the reachability problem is much harder than the coverability (i.e., state reachability) problem, which is also ubiquitous but has been known to be complete for exponential space since the late 1970s. Secondly, it implies that a plethora of problems from formal languages, logic, concurrent systems, process calculi and other areas, that are known to admit reductions from the Petri nets reachability problem, are also not elementary. Thirdly, it makes obsolete the currently best lower bounds for the reachability problems for two key extensions of Petri nets: with branching and with a pushdown stack.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316369"}, {"primary_key": "3121688", "vector": [], "sparse_vector": [], "title": "On approximating the covering radius and finding dense lattice subspaces.", "authors": ["<PERSON>"], "summary": "In this work, we give a novel algorithm for computing dense lattice subspaces, a conjecturally tight characterization of the lattice covering radius, and provide a bound on the slicing constant of lattice Voronoi cells. Our work is motivated by the pursuit of faster algorithms for integer programming, for which we give a conditional speedup based on the recent resolution of the ℓ2 Ka<PERSON> conjecture. Through these results, we hope to motivate further study of the interplay between the recently developed reverse Minkowski theory, lattice algorithms and convex geometry.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316397"}, {"primary_key": "3121689", "vector": [], "sparse_vector": [], "title": "Distributed edge connectivity in sublinear time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Danupon <PERSON>", "Thatchaphol <PERSON>"], "summary": "We present the first sublinear-time algorithm for a distributed message-passing network sto compute its edge connectivity $\\lambda$ exactly in the CONGEST model, as long as there are no parallel edges. Our algorithm takes $\\tilde O(n^{1-1/353}D^{1/353}+n^{1-1/706})$ time to compute $\\lambda$ and a cut of cardinality $\\lambda$ with high probability, where $n$ and $D$ are the number of nodes and the diameter of the network, respectively, and $\\tilde O$ hides polylogarithmic factors. This running time is sublinear in $n$ (i.e. $\\tilde O(n^{1-\\epsilon})$) whenever $D$ is. Previous sublinear-time distributed algorithms can solve this problem either (i) exactly only when $\\lambda=O(n^{1/8-\\epsilon})$ [Thurimella PODC'95; Pritchard, Thurimella, ACM Trans. Algorithms'11; Nanongkai, Su, DISC'14] or (ii) approximately [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, DISC'13; Nanongkai, Su, DISC'14]. To achieve this we develop and combine several new techniques. First, we design the first distributed algorithm that can compute a $k$-edge connectivity certificate for any $k=O(n^{1-\\epsilon})$ in time $\\tilde O(\\sqrt{nk}+D)$. Second, we show that by combining the recent distributed expander decomposition technique of [<PERSON>, <PERSON><PERSON>, <PERSON>, S<PERSON><PERSON>'19] with techniques from the sequential deterministic edge connectivity algorithm of [Kawara<PERSON>, Thorup, STOC'15], we can decompose the network into a sublinear number of clusters with small average diameter and without any mincut separating a cluster (except the `trivial' ones). Finally, by extending the tree packing technique from [Karger STOC'96], we can find the minimum cut in time proportional to the number of components. As a byproduct of this technique, we obtain an $\\tilde O(n)$-time algorithm for computing exact minimum cut for weighted graphs.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316346"}, {"primary_key": "3121690", "vector": [], "sparse_vector": [], "title": "Graph pattern detection: hardness for all induced patterns and faster non-induced cycles.", "authors": ["<PERSON>", "Thuy-<PERSON>ng <PERSON>", "Virginia Vassilevska Williams"], "summary": "We consider the pattern detection problem in graphs: given a constant size pattern graph H and a host graph G, determine whether G contains a subgraph isomorphic to H. We present the following new improved upper and lower bounds:", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316329"}, {"primary_key": "3121691", "vector": [], "sparse_vector": [], "title": "Regression from dependent observations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The standard linear and logistic regression models assume that the response variables are independent, but share the same linear relationship to their corresponding vectors of covariates. The assumption that the response variables are independent is, however, too strong. In many applications, these responses are collected on nodes of a network, or some spatial or temporal domain, and are dependent. Examples abound in financial and meteorological applications, and dependencies naturally arise in social networks through peer effects. Regression with dependent responses has thus received a lot of attention in the Statistics and Economics literature, but there are no strong consistency results unless multiple independent samples of the vectors of dependent responses can be collected from these models. We present computationally and statistically efficient methods for linear and logistic regression models when the response variables are dependent on a network. Given one sample from a networked linear or logistic regression model and under mild assumptions, we prove strong consistency results for recovering the vector of coefficients and the strength of the dependencies, recovering the rates of standard regression under independent observations. We use projected gradient descent on the negative log-likelihood, or negative log-pseudolikelihood, and establish their strong convexity and consistency using concentration of measure for dependent random variables.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316362"}, {"primary_key": "3121692", "vector": [], "sparse_vector": [], "title": "Degree-푑 chow parameters robustly determine degree-푑 PTFs (and algorithmic applications).", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The degree-d Chow parameters of a Boolean function are its degree at most d Fourier coefficients. It is well-known that degree-d Chow parameters uniquely characterize degree-d polynomial threshold functions (PTFs) within the space of all bounded functions. In this paper, we prove a robust version of this theorem: For f any Boolean degree-d PTF and g any bounded function, if the degree-d Chow parameters of f are close to the degree-d Chow parameters of g in ℓ2-norm, then f is close to g in ℓ1-distance. Notably, our bound relating the two distances is independent of the dimension. That is, we show that Boolean degree-d PTFs are robustly identifiable from their degree-d Chow parameters. No non-trivial bound was previously known for d >1.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316301"}, {"primary_key": "3121693", "vector": [], "sparse_vector": [], "title": "Capacity lower bound for the <PERSON>ing perceptron.", "authors": ["<PERSON><PERSON>", "Nike Sun"], "summary": "We consider the Ising perceptron with gaussian disorder, which is equivalent to the discrete cube {−1,+1}N intersected by M random half-spaces. The perceptron's capacity is the largest integer MN for which the intersection is nonempty. It is conjectured by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (1989) that the (random) ratio MN/N converges in probability to an explicit constant α⋆≐ 0.83. <PERSON> and <PERSON> (1998) proved the existence of a positive constant γ such that γ ≤ MN/N ≤ 1−γ with high probability; see also <PERSON><PERSON><PERSON> (1999). In this paper we show that the <PERSON><PERSON><PERSON>–<PERSON> conjecture α⋆ is a lower bound with positive probability, under the condition that an explicit univariate function S(λ) is maximized at λ=0. Our proof is an application of the second moment method to a certain slice of perceptron configurations, as selected by the so-called TAP (<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, 1977) or AMP (approximate message passing) iteration, whose scaling limit has been characterized by <PERSON><PERSON> and <PERSON> (2011) and <PERSON><PERSON> (2012). For verifying the condition on S(λ) we outline one approach, which is implemented in the current version using (nonrigorous) numerical integration packages. In a future version of this paper we intend to complete the verification by implementing a rigorous numerical method.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316383"}, {"primary_key": "3121694", "vector": [], "sparse_vector": [], "title": "Computing quartet distance is equivalent to counting 4-cycles.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The quartet distance is a measure of similarity used to compare two unrooted phylogenetic trees on the same set of n leaves, defined as the number of subsets of four leaves related by a different topology in both trees. After a series of previous results, <PERSON><PERSON><PERSON> et al. [SODA 2013] presented an algorithm that computes this number in O(ndlogn) time, where d is the maximum degree of a node. For the related triplet distance between rooted phylogenetic trees, the same authors were able to design an O(nlogn) time algorithm, that is, with running time independent of d. This raises the question of achieving such complexity for computing the quartet distance, or at least improving the dependency on d.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316390"}, {"primary_key": "3121695", "vector": [], "sparse_vector": [], "title": "Fully dynamic spectral vertex sparsifiers and applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study dynamic algorithms for maintaining spectral vertex sparsifiers of graphs with respect to a set of terminals T of our choice. Such objects preserve pairwise resistances, solutions to systems of linear equations, and energy of electrical flows between the terminals in T. We give a data structure that supports insertions and deletions of edges, and terminal additions, all in sublinear time. We then show the applicability of our result to the following problems.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316379"}, {"primary_key": "3121696", "vector": [], "sparse_vector": [], "title": "Static data structure lower bounds imply rigidity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that static data structure lower bounds in the group (linear) model imply semi-explicit lower bounds on matrix rigidity. In particular, we prove that an explicit lower bound of t ≥ ω(log2 n) on the cell-probe complexity of linear data structures in the group model, even against arbitrarily small linear space (s= (1+)n), would already imply a semi-explicit (PNP) construction of rigid matrices with significantly better parameters than the current state of art (<PERSON>, <PERSON> and <PERSON>, 2009). Our results further assert that polynomial (t≥ nδ) data structure lower bounds against near-optimal space, would imply super-linear circuit lower bounds for log-depth linear circuits (a four-decade open question). In the succinct space regime (s=n+o(n)), we show that any improvement on current cell-probe lower bounds in the linear model would also imply new rigidity bounds. Our results rely on a new connection between the \"inner\" and \"outer\" dimensions of a matrix (<PERSON><PERSON> and <PERSON>, 2006), and on a new reduction from worst-case to average-case rigidity, which is of independent interest.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316348"}, {"primary_key": "3121697", "vector": [], "sparse_vector": [], "title": "Submodular maximization with matroid and packing constraints in parallel.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of maximizing the multilinear extension of a submodular function subject a single matroid constraint or multiple packing constraints with a small number of adaptive rounds of evaluation queries.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316389"}, {"primary_key": "3121698", "vector": [], "sparse_vector": [], "title": "Dynamic sampling from graphical models.", "authors": ["<PERSON><PERSON>", "Nisheeth K. <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we study the problem of sampling from a graphical model when the model itself is changing dynamically with time. This problem derives its interest from a variety of inference, learning, and sampling settings in machine learning, computer vision, statistical physics, and theoretical computer science. While the problem of sampling from a static graphical model has received considerable attention, theoretical works for its dynamic variants have been largely lacking. The main contribution of this paper is an algorithm that can sample dynamically from a broad class of graphical models over discrete random variables. Our algorithm is parallel and Las Vegas: it knows when to stop and it outputs samples from the exact distribution. We also provide sufficient conditions under which this algorithm runs in time proportional to the size of the update, on general graphical models as well as well-studied specific spin systems. In particular we obtain, for the Ising model (ferromagnetic or anti-ferromagnetic) and for the hardcore model the first dynamic sampling algorithms that can handle both edge and vertex updates (addition, deletion, change of functions), both efficient within regimes that are close to the respective uniqueness regimes, beyond which, even for the static and approximate sampling, no local algorithms were known or the problem itself is intractable. Our dynamic sampling algorithm relies on a local resampling algorithm and a new ``equilibrium'' property that is shown to be satisfied by our algorithm at each step, and enables us to prove its correctness. This equilibrium property is robust enough to guarantee the correctness of our algorithm, helps us improve bounds on fast convergence on specific models, and should be of independent interest.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316365"}, {"primary_key": "3121699", "vector": [], "sparse_vector": [], "title": "The complexity of splitting necklaces and bisecting ham sandwiches.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We resolve the computational complexity of two problems known as Necklace Splitting and Discrete <PERSON> Sandwich, showing that they are PPA-complete. For Necklace Splitting, this result is specific to the important special case in which two thieves share the necklace. We do this via a PPA-completeness result for an approximate version of the Consensus Halving problem, strengthening our recent result that the problem is PPA-complete for inverse-exponential precision. At the heart of our construction is a smooth embedding of the high-dimensional Mobius strip in the Consensus Halving problem. These results settle the status of PPA as a class that captures the complexity of \"natural\" problems whose definitions do not incorporate a circuit.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316334"}, {"primary_key": "3121700", "vector": [], "sparse_vector": [], "title": "Quantum proof systems for iterated exponential time, and beyond.", "authors": ["<PERSON>", "Zhengfeng Ji", "<PERSON>", "<PERSON>"], "summary": "We show that any language solvable in nondeterministic time exp( exp(⋯exp(n))), where the number of iterated exponentials is an arbitrary function R(n), can be decided by a multiprover interactive proof system with a classical polynomial-time verifier and a constant number of quantum entangled provers, with completeness 1 and soundness 1 − exp(−Cexp(⋯exp(n))), where the number of iterated exponentials is R(n)−1 and C>0 is a universal constant. The result was previously known for R=1 and R=2; we obtain it for any time-constructible function R.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316343"}, {"primary_key": "3121701", "vector": [], "sparse_vector": [], "title": "Dynamic low-stretch trees via dynamic low-diameter decompositions.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Spanning trees of low average stretch on the non-tree edges, as introduced by <PERSON><PERSON> et al. [S<PERSON>OM<PERSON> 1995], are a natural graph-theoretic object. In recent years, they have found significant applications in solvers for symmetric diagonally dominant (SDD) linear systems. In this work, we provide the first dynamic algorithm for maintaining such trees under edge insertions and deletions to the input graph. Our algorithm has update time $ n^{1/2 + o(1)} $ and the average stretch of the maintained tree is $ n^{o(1)} $, which matches the stretch in the seminal result of <PERSON><PERSON> et al. Similar to <PERSON><PERSON> et al., our dynamic low-stretch tree algorithm employs a dynamic hierarchy of low-diameter decompositions (LDDs). As a major building block we use a dynamic LDD that we obtain by adapting the random-shift clustering of <PERSON> et al. [SPAA 2013] to the dynamic setting. The major technical challenge in our approach is to control the propagation of updates within our hierarchy of LDDs: each update to one level of the hierarchy could potentially induce several insertions and deletions to the next level of the hierarchy. We achieve this goal by a sophisticated amortization approach. We believe that the dynamic random-shift clustering might be useful for independent applications. One of these applications is the dynamic spanner problem. By combining the random-shift clustering with the recent spanner construction of <PERSON><PERSON> and Neiman [SODA 2017]. We obtain a fully dynamic algorithm for maintaining a spanner of stretch $ 2k - 1 $ and size $ O (n^{1 + 1/k} \\log{n}) $ with amortized update time $ O (k \\log^2 n) $ for any integer $ 2 \\leq k \\leq \\log n $. Compared to the state-of-the art in this regime [Baswana et al. TALG '12], we improve upon the size of the spanner and the update time by a factor of $ k $.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316381"}, {"primary_key": "3121702", "vector": [], "sparse_vector": [], "title": "Optimal sequence length requirements for phylogenetic tree reconstruction with indels.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> (<PERSON>) <PERSON>"], "summary": "We consider the phylogenetic tree reconstruction problem with insertions and deletions (indels). Phylogenetic algorithms proceed under a model where sequences evolve down the model tree, and given sequences at the leaves, the problem is to reconstruct the model tree with high probability. Traditionally, sequences mutate by substitution-only processes, although some recent work considers evolutionary processes with insertions and deletions. In this paper, we improve on previous work by giving a reconstruction algorithm that simultaneously has O(poly logn) sequence length and tolerates constant indel probabilities on each edge. Our recursively-reconstructed distance-based technique provably outputs the model tree when the model tree has O(poly logn) diameter and discretized branch lengths, allowing for the probability of insertion and deletion to be non-uniform and asymmetric on each edge. Our polylogarithmic sequence length bounds improve significantly over previous polynomial sequence length bounds and match sequence length bounds in the substitution-only models of phylogenetic evolution, thereby challenging the idea that many global misalignments caused by insertions and deletions when pindel is large are a fundamental obstruction to reconstruction with short sequences.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316345"}, {"primary_key": "3121703", "vector": [], "sparse_vector": [], "title": "A strongly polynomial algorithm for linear exchange markets.", "authors": ["<PERSON><PERSON>", "László A. <PERSON>"], "summary": "We present a strongly polynomial algorithm for computing an equilibrium in Arrow-Debreu exchange markets with linear utilities. Our algorithm is based on a variant of the weakly-polynomial <PERSON><PERSON><PERSON> (DM) algorithm. We use the DM algorithm as a subroutine to identify revealed edges, i.e., pairs of agents and goods that must correspond to best bang-per-buck transactions in every equilibrium solution. Every time a new revealed edge is found, we use another subroutine that decides if there is an optimal solution using the current set of revealed edges, or if none exists, finds the solution that approximately minimizes the violation of the demand and supply constraints. This task can be reduced to solving a linear program (LP). Even though we are unable to solve this LP in strongly polynomial time, we show that it can be approximated by a simpler LP with two variables per inequality that is solvable in strongly polynomial time.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316340"}, {"primary_key": "3121704", "vector": [], "sparse_vector": [], "title": "Quantum singular value transformation and beyond: exponential improvements for quantum matrix arithmetics.", "authors": ["<PERSON><PERSON><PERSON>", "Yuan Su", "<PERSON><PERSON>", "<PERSON>"], "summary": "Quantum computing is powerful because unitary operators describing the time-evolution of a quantum system have exponential size in terms of the number of qubits present in the system. We develop a new \"Singular value transformation\" algorithm capable of harnessing this exponential advantage, that can apply polynomial transformations to the singular values of a block of a unitary, generalizing the optimal Hamiltonian simulation results of <PERSON> and <PERSON><PERSON>. The proposed quantum circuits have a very simple structure, often give rise to optimal algorithms and have appealing constant factors, while usually only use a constant number of ancilla qubits. We show that singular value transformation leads to novel algorithms. We give an efficient solution to a certain \"non-commutative\" measurement problem and propose a new method for singular value estimation. We also show how to exponentially improve the complexity of implementing fractional queries to unitaries with a gapped spectrum. Finally, as a quantum machine learning application we show how to efficiently implement principal component regression. \"Singular value transformation\" is conceptually simple and efficient, and leads to a unified framework of quantum algorithms incorporating a variety of quantum speed-ups. We illustrate this by showing how it generalizes a number of prominent quantum algorithms, including: optimal Hamiltonian simulation, implementing the Moore-Penrose pseudoinverse with exponential precision, fixed-point amplitude amplification, robust oblivious amplitude amplification, fast QMA amplification, fast quantum OR lemma, certain quantum walk results and several quantum machine learning algorithms. In order to exploit the strengths of the presented method it is useful to know its limitations too, therefore we also prove a lower bound on the efficiency of singular value transformation, which often gives optimal bounds.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316366"}, {"primary_key": "3121705", "vector": [], "sparse_vector": [], "title": "Testing graphs against an unknown distribution.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The classical model of graph property testing, introduced by <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, assumes that the algorithm can obtain uniformly distributed vertices from the input graph. <PERSON><PERSON><PERSON> introduced a more general model, called the Vertex-Distribution-Free model (or VDF for short) in which the testing algorithm obtains vertices drawn from an arbitrary and unknown distribution. The main motivation for this investigation is that it can allow one to give different weight/importance to different parts of the input graph, as well as handle situations where one cannot obtain uniformly selected vertices from the input. <PERSON><PERSON><PERSON> proved that any property which is testable in this model must (essentially) be hereditary, and that several hereditary properties can indeed be tested in this model. He further asked which properties are testable in this model.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316308"}, {"primary_key": "3121706", "vector": [], "sparse_vector": [], "title": "Testing graphs in vertex-distribution-free models.", "authors": ["<PERSON><PERSON>"], "summary": "Prior studies of testing graph properties presume that the tester can obtain uniformly distributed vertices in the tested graph (in addition to obtaining answers to the some type of graph-queries). Here we envision settings in which it is only feasible to obtain random vertices drawn according to an arbitrary distribution (and, in addition, obtain answers to the usual graph-queries). We initiate a study of testing graph properties in such settings, while adapting the definition of distance between graphs so that it reflects the different probability weight of different vertices. Hence, the distance to the property represents the relative importance of the \"part of the graph\" that violates the property. We consider such \"vertex-distribution free\" (VDF) versions of the two most-studied models of testing graph properties (i.e., the dense graph model and the bounded-degree model).", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316302"}, {"primary_key": "3121707", "vector": [], "sparse_vector": [], "title": "Non-Gaussian component analysis using entropy methods.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Non-Gaussian component analysis (NGCA) is a problem in multidimensional data analysis which, since its formulation in 2006, has attracted considerable attention in statistics and machine learning. In this problem, we have a random variable $X$ in $n$-dimensional Euclidean space. There is an unknown subspace $\\Gamma$ of the $n$-dimensional Euclidean space such that the orthogonal projection of $X$ onto $\\Gamma$ is standard multidimensional Gaussian and the orthogonal projection of $X$ onto $\\Gamma^{\\perp}$, the orthogonal complement of $\\Gamma$, is non-Gaussian, in the sense that all its one-dimensional marginals are different from the Gaussian in a certain metric defined in terms of moments. The NGCA problem is to approximate the non-Gaussian subspace $\\Gamma^{\\perp}$ given samples of $X$. Vectors in $\\Gamma^{\\perp}$ correspond to `interesting' directions, whereas vectors in $\\Gamma$ correspond to the directions where data is very noisy. The most interesting applications of the NGCA model is for the case when the magnitude of the noise is comparable to that of the true signal, a setting in which traditional noise reduction techniques such as PCA don't apply directly. NGCA is also related to dimension reduction and to other data analysis problems such as ICA. NGCA-like problems have been studied in statistics for a long time using techniques such as projection pursuit. We give an algorithm that takes polynomial time in the dimension $n$ and has an inverse polynomial dependence on the error parameter measuring the angle distance between the non-Gaussian subspace and the subspace output by the algorithm. Our algorithm is based on relative entropy as the contrast function and fits under the projection pursuit framework. The techniques we develop for analyzing our algorithm maybe of use for other related problems.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316309"}, {"primary_key": "3121708", "vector": [], "sparse_vector": [], "title": "Settling the sample complexity of single-parameter revenue maximization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper settles the sample complexity of single-parameter revenue maximization by showing matching upper and lower bounds, up to a poly-logarithmic factor, for all families of value distributions that have been considered in the literature. The upper bounds are unified under a novel framework, which builds on the strong revenue monotonicity by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> (STOC 2016), and an information theoretic argument. This is fundamentally different from the previous approaches that rely on either constructing an $\\epsilon$-net of the mechanism space, explicitly or implicitly via statistical learning theory, or learning an approximately accurate version of the virtual values. To our knowledge, it is the first time information theoretical arguments are used to show sample complexity upper bounds, instead of lower bounds. Our lower bounds are also unified under a meta construction of hard instances.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316325"}, {"primary_key": "3121709", "vector": [], "sparse_vector": [], "title": "The number of minimum k-cuts: improving the Karger-Stein bound.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>"], "summary": "Given an edge-weighted graph, how many minimum k-cuts can it have? This is a fundamental question in the intersection of algorithms, extremal combinatorics, and graph theory. It is particularly interesting in that the best known bounds are algorithmic: they stem from algorithms that compute the minimum k-cut.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316395"}, {"primary_key": "3121710", "vector": [], "sparse_vector": [], "title": "Communication complexity of estimating correlations.", "authors": ["<PERSON><PERSON>", "Jing<PERSON> Liu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We characterize the communication complexity of the following distributed estimation problem. <PERSON> and <PERSON> observe infinitely many iid copies of ρ-correlated unit-variance (Gaussian or ±1 binary) random variables, with unknown ρ∈[−1,1]. By interactively exchanging k bits, <PERSON> wants to produce an estimate ρ of ρ. We show that the best possible performance (optimized over interaction protocol Π and estimator ρ) satisfies infΠ ρsupρE [|ρ−ρ|2] = k−1 (1/2 ln2 + o(1)). Curiously, the number of samples in our achievability scheme is exponential in k; by contrast, a naive scheme exchanging k samples achieves the same Ω(1/k) rate but with a suboptimal prefactor. Our protocol achieving optimal performance is one-way (non-interactive). We also prove the Ω(1/k) bound even when ρ is restricted to any small open sub-interval of [−1,1] (i.e. a local minimax lower bound). Our proof techniques rely on symmetric strong data-processing inequalities and various tensorization techniques from information-theoretic interactive common-randomness extraction. Our results also imply an Ω(n) lower bound on the information complexity of the Gap-Hamming problem, for which we show a direct information-theoretic proof.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316332"}, {"primary_key": "3121711", "vector": [], "sparse_vector": [], "title": "Near-linear time insertion-deletion codes and (1+ε)-approximating edit distance via indexing.", "authors": ["<PERSON>", "<PERSON>via<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce fast-decodable indexing schemes for edit distance which can be used to speed up edit distance computations to near-linear time if one of the strings is indexed by an indexing string $I$. In particular, for every length $n$ and every $\\varepsilon >0$, one can in near linear time construct a string $I \\in \\Sigma'^n$ with $|\\Sigma'| = O_{\\varepsilon}(1)$, such that, indexing any string $S \\in \\Sigma^n$, symbol-by-symbol, with $I$ results in a string $S' \\in \\Sigma''^n$ where $\\Sigma'' = \\Sigma \\times \\Sigma'$ for which edit distance computations are easy, i.e., one can compute a $(1+\\varepsilon)$-approximation of the edit distance between $S'$ and any other string in $O(n \\text{poly}(\\log n))$ time. Our indexing schemes can be used to improve the decoding complexity of state-of-the-art error correcting codes for insertions and deletions. In particular, they lead to near-linear time decoding algorithms for the insertion-deletion codes of [Haeupler, Shahrasbi; STOC `17] and faster decoding algorithms for list-decodable insertion-deletion codes of [Haeupler, Shahrasbi, Sudan; ICALP `18]. Interestingly, the latter codes are a crucial ingredient in the construction of fast-decodable indexing schemes.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316371"}, {"primary_key": "3121712", "vector": [], "sparse_vector": [], "title": "Faster k-SAT algorithms using biased-PPSZ.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The PPSZ algorithm, due to <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, is currently the fastest known algorithm for the k-SAT problem, for every k>3. For 3-SAT, a tiny improvement over PPSZ was obtained by <PERSON><PERSON><PERSON>. We introduce a biased version of the PPSZ algorithm using which we obtain an improvement over PPSZ for every k≥ 3. For k=3 we also improve on <PERSON><PERSON>'s result and get a much more noticeable improvement over PPSZ, though still relatively small. In particular, for Unique 3-SAT, we improve the current bound from 1.308n to 1.307n.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316359"}, {"primary_key": "3121713", "vector": [], "sparse_vector": [], "title": "Algorithmic Pirogov-Sinai theory.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We develop an efficient algorithmic approach for approximate counting and sampling in the low-temperature regime of a broad class of statistical physics models on finite subsets of the lattice ℤd and on the torus (ℤ/n ℤ)d. Our approach is based on combining contour representations from <PERSON><PERSON>ov–<PERSON> theory with <PERSON><PERSON><PERSON>'s approach to approximate counting using truncated Taylor series. Some consequences of our main results include an FPTAS for approximating the partition function of the hard-core model at sufficiently high fugacity on subsets of ℤd with appropriate boundary conditions and an efficient sampling algorithm for the ferromagnetic Potts model on the discrete torus (ℤ/n ℤ)d at sufficiently low temperature.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316305"}, {"primary_key": "3121714", "vector": [], "sparse_vector": [], "title": "The parallel repetition of non-signaling games: counterexamples and dichotomy.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Non-signaling games are an important object of study in the theory of computation, for their role both in quantum information and in (classical) cryptography. In this work, we study the behavior of these games under parallel repetition.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316367"}, {"primary_key": "3121715", "vector": [], "sparse_vector": [], "title": "Mean-field approximation, convex hierarchies, and the optimality of correlation rounding: a unified perspective.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The free energy is a key quantity of interest in Ising models, but unfortunately, computing it in general is computationally intractable. Two popular (variational) approximation schemes for estimating the free energy of general Ising models (in particular, even in regimes where correlation decay does not hold) are: (i) the mean-field approximation with roots in statistical physics, which estimates the free energy from below, and (ii) hierarchies of convex relaxations with roots in theoretical computer science, which estimate the free energy from above. We show, surprisingly, that the tight regime for both methods to compute the free energy to leading order is identical.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316299"}, {"primary_key": "3121716", "vector": [], "sparse_vector": [], "title": "Tight approximation ratio of anonymous pricing.", "authors": ["Yaonan Jin", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper considers two canonical Bayesian mechanism design settings. In the single-item setting, the tight approximation ratio of Anonymous Pricing is obtained: (1) compared to <PERSON>on Auction, Anonymous Pricing always generates at least a 1/2.62-fraction of the revenue; (2) there is a matching lower-bound instance.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316331"}, {"primary_key": "3121717", "vector": [], "sparse_vector": [], "title": "How to delegate computations publicly.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We construct a delegation scheme for all polynomial time computations. Our scheme is publicly verifiable and completely non-interactive in the common reference string (CRS) model.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316411"}, {"primary_key": "3121718", "vector": [], "sparse_vector": [], "title": "An optimal space lower bound for approximating MAX-CUT.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the problem of estimating the value of MAX-CUT in a graph in the streaming model of computation. At one extreme, there is a trivial 2-approximation for this problem that uses only O(log n) space, namely, count the number of edges and output half of this value as the estimate for the size of the MAX-CUT. On the other extreme, for any fixed є > 0, if one allows Õ(n) space, a (1+є)-approximate solution to the MAX-CUT value can be obtained by storing an Õ(n)-size sparsifier that essentially preserves MAX-CUT value.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316364"}, {"primary_key": "3121719", "vector": [], "sparse_vector": [], "title": "Polylogarithmic approximation for Euler genus on bounded degree graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Computing the Euler genus of a graph is a fundamental problem in algorithmic graph theory. It has been shown to be NP-hard by [<PERSON><PERSON> '89, <PERSON><PERSON> '97], even for cubic graphs, and a linear-time fixed-parameter algorithm has been obtained by [<PERSON><PERSON> '99]. Despite extensive study, the approximability of the Euler genus remains wide open. While the existence of an O(1)-approximation is not ruled out, the currently best-known upper bound is a O(n1−α)-approximation, for some universal constant α>0 [<PERSON> and <PERSON><PERSON> 2017].", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316409"}, {"primary_key": "3121720", "vector": [], "sparse_vector": [], "title": "Reconstruction of non-degenerate homogeneous depth three circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A homogeneous depth three circuit C computes a polynomial f = T1 + T2 + ... + Ts, where each Ti is a product of d linear forms in n variables over some underlying field F. Given black-box access to f, can we efficiently reconstruct (i.e. proper learn) a homogeneous depth three circuit computing f? Learning various subclasses of circuits is natural and interesting from both theoretical and practical standpoints and in particular, properly learning homogeneous depth three circuits efficiently is stated as an open problem in a work by <PERSON><PERSON><PERSON> and <PERSON> (COLT 2003) and is well-studied. Unfortunately, there is substantial amount of evidence to show that this is a hard problem in the worst case. We give a (randomized) poly(n,d,s)-time algorithm to reconstruct non-degenerate homogeneous depth three circuits for n = Ω(d2) (with some additional mild requirements on s and the characteristic of F). We call a circuit C as non-degenerate if the dimension of the partial derivative space of f equals the sum of the dimensions of the partial derivative spaces of the terms T1, T2, …, Ts. In this sense, the terms are \"independent\" of each other in a non-degenerate circuit. A random homogeneous depth three circuit (where the coefficients of the linear forms are chosen according to the uniform distribution or any other reasonable distribution) is almost surely non-degenerate. In comparison, previous learning algorithms for this circuit class were either improper (with an exponential dependence on d), or they only worked for s < n (with a doubly exponential dependence of the running time on s). The main contribution of this work is to formulate the following paradigm for efficiently handling addition gates and to successfully implement it for the class of homogeneous depth three circuits. The problem of finding the children of an addition gate with large fan-in s is first reduced to the problem of decomposing a suitable vector space U into a (direct) sum of simpler subspaces U1, U2, …, Us. One then constructs a suitable space of operators S consisting of linear maps acting on U such that analyzing the simultaneous global structure of S enables us to efficiently decompose U. In our case, we exploit the structure of the set of low rank matrices in S and of the invariant subspaces of U induced by S. We feel that this paradigm is novel and powerful: it should lead to efficient reconstruction of many other subclasses of circuits for which the efficient reconstruction problem had hitherto looked unapproachable because of the presence of large fan-in addition gates.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316360"}, {"primary_key": "3121721", "vector": [], "sparse_vector": [], "title": "String synchronizing sets: sublinear-time BWT construction and optimal LCE data structure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>ows–<PERSON> transform (BWT) is an invertible text transformation that, given a text T of length n, permutes its symbols according to the lexicographic order of suffixes of T. BWT is one of the most heavily studied algorithms in data compression with numerous applications in indexing, sequence analysis, and bioinformatics. Its construction is a bottleneck in many scenarios, and settling the complexity of this task is one of the most important unsolved problems in sequence analysis that has remained open for 25 years. Given a binary string of length n, occupying O(n/logn) machine words, the BWT construction algorithm due to <PERSON> et al. (SIAM J. Comput., 2009) runs in O(n) time and O(n/logn) space. Recent advancements (Belazzougui, STOC 2014, and <PERSON> et al., SODA 2017) focus on removing the alphabet-size dependency in the time complexity, but they still require Ω(n) time. Despite the clearly suboptimal running time, the existing techniques appear to have reached their limits.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316368"}, {"primary_key": "3121722", "vector": [], "sparse_vector": [], "title": "Flows in almost linear time via adaptive preconditioning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present algorithms for solving a large class of flow and regression problems on unit weighted graphs to (1 + 1 / poly(n)) accuracy in almost-linear time. These problems include ℓp-norm minimizing flow for p large (p ∈ [ω(1), o(log2/3 n) ]), and their duals, ℓp-norm semi-supervised learning for p close to 1.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316410"}, {"primary_key": "3121723", "vector": [], "sparse_vector": [], "title": "Planar diameter via metric compression.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We develop a new approach for distributed distance computation in planar graphs that is based on a variant of the metric compression problem recently introduced by <PERSON><PERSON><PERSON> et al. [SODA'18]. In our variant of the Planar Graph Metric Compression Problem, one is given an n-vertex planar graph G=(V,E), a set of S ⊆ V source terminals lying on a single face, and a subset of target terminals T ⊆ V. The goal is to compactly encode the S× T distances.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316358"}, {"primary_key": "3121724", "vector": [], "sparse_vector": [], "title": "A fixed-depth size-hierarchy theorem for AC0[⊕] via the coin problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work we prove the first Fixed-depth Size-Hierarchy Theorem for uniform AC0[⊕]. In particular, we show that for any fixed d, the class Cd,k of functions that have uniform AC0[⊕] formulas of depth d and size nk form an infinite hierarchy. We show this by exhibiting the first class of explicit functions where we have nearly (up to a polynomial factor) matching upper and lower bounds for the class of AC0[⊕] formulas.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316339"}, {"primary_key": "3121725", "vector": [], "sparse_vector": [], "title": "Approximation algorithms for distributionally-robust stochastic optimization with black-box distributions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Two-stage stochastic optimization is a widely used framework for modeling uncertainty, where we have a probability distribution over possible realizations of the data, called scenarios, and decisions are taken in two stages: we make first-stage decisions knowing only the underlying distribution and before a scenario is realized, and may take additional second-stage recourse actions after a scenario is realized. The goal is typically to minimize the total expected cost. A common criticism levied at this model is that the underlying probability distribution is itself often imprecise! To address this, an approach that is quite versatile and has gained popularity in the stochastic-optimization literature is the distributionally robust 2-stage model: given a collection D of probability distributions, our goal now is to minimize the maximum expected total cost with respect to a distribution in D.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316391"}, {"primary_key": "3121726", "vector": [], "sparse_vector": [], "title": "DNF sparsification beyond sunflowers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There are two natural complexity measures associated with DNFs: their size, which is the number of clauses; and their width, which is the maximal number of variables in a clause. It is a folklore result that DNFs of small size can be approximated by DNFs of small width (logarithmic in the size). The other direction is much less clear.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316323"}, {"primary_key": "3121727", "vector": [], "sparse_vector": [], "title": "Hamiltonian simulation with nearly optimal dependence on spectral norm.", "authors": ["<PERSON><PERSON>"], "summary": "We present a quantum algorithm for approximating the real time evolution $e^{-iHt}$ of an arbitrary $d$-sparse Hamiltonian to error $\\epsilon$, given black-box access to the positions and $b$-bit values of its non-zero matrix entries. The complexity of our algorithm is $\\mathcal{O}((t\\sqrt{d}\\|H\\|_{1 \\rightarrow 2})^{1+o(1)}/\\epsilon^{o(1)})$ queries and a factor $\\mathcal{O}(b)$ more gates, which is shown to be optimal up to subpolynomial factors through a matching query lower bound. This provides a polynomial speedup in sparsity for the common case where the spectral norm $\\|H\\|\\ge\\|H\\|_{1 \\rightarrow 2}$ is known, and generalizes previous approaches which achieve optimal scaling, but with respect to more restrictive parameters. By exploiting knowledge of the spectral norm, our algorithm solves the black-box unitary implementation problem -- $\\mathcal{O}(d^{1/2+o(1)})$ queries suffice to approximate any $d$-sparse unitary in the black-box setting, which matches the quantum search lower bound of $\\Omega(\\sqrt{d})$ queries and improves upon prior art [<PERSON> and <PERSON>, QIP 2010] of $\\tilde{\\mathcal{O}}(d^{2/3})$ queries. Combined with known techniques, we also solve systems of sparse linear equations with condition number $\\kappa$ using $\\mathcal{O}((\\kappa \\sqrt{d})^{1+o(1)}/\\epsilon^{o(1)})$ queries, which is a quadratic improvement in sparsity.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316386"}, {"primary_key": "3121728", "vector": [], "sparse_vector": [], "title": "Performance of <PERSON><PERSON><PERSON><PERSON><PERSON> transform for k-means and k-medians clustering.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Ilya <PERSON>"], "summary": "Consider an instance of Euclidean k-means or k-medians clustering. We show that the cost of the optimal solution is preserved up to a factor of (1+ε) under a projection onto a random O(log(k /ε) / ε2)-dimensional subspace. Further, the cost of every clustering is preserved within (1+ε). More generally, our result applies to any dimension reduction map satisfying a mild sub-Gaussian-tail condition. Our bound on the dimension is nearly optimal. Additionally, our result applies to Euclidean k-clustering with the distances raised to the p-th power for any constant p.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316350"}, {"primary_key": "3121729", "vector": [], "sparse_vector": [], "title": "Weak lower bounds on resource-bounded compression imply strong separations of complexity classes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Minimum Circuit Size Problem (MCSP) asks to determine the minimum size of a circuit computing a given truth table. MCSP is a natural and powerful string compression problem using bounded-size circuits. Recently, <PERSON> and <PERSON> [FOCS 2018] and <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> [ECCC 2018] demonstrated a \"hardness magnification\" phenomenon for MCSP in restricted settings. Letting MCSP[s(n)] be the problem of deciding if a truth table of length 2n has circuit complexity at most s(n), they proved that small (fixed-polynomial) average case circuit/formula lower bounds for MCSP[2√n], or lower bounds for approximating MCSP[2o(n)], would imply major separations such as NP ⊄BPP and NP ⊄P/poly.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316396"}, {"primary_key": "3121730", "vector": [], "sparse_vector": [], "title": "Pseudorandom generators for width-3 branching programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct pseudorandom generators of seed length Õ(log(n)· log(1/є)) that є-fool ordered read-once branching programs (ROBPs) of width 3 and length n. For unordered ROBPs, we construct pseudorandom generators with seed length Õ(log(n) · poly(1/є)). This is the first improvement for pseudorandom generators fooling width 3 ROBPs since the work of <PERSON><PERSON> [Combinatorica, 1992].", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316319"}, {"primary_key": "3121731", "vector": [], "sparse_vector": [], "title": "Spectral methods from tensor networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A tensor network is a diagram that specifies a way to ``multiply'' a collection of tensors together to produce another tensor (or matrix). Many existing algorithms for tensor problems (such as tensor decomposition and tensor PCA), although they are not presented this way, can be viewed as spectral methods on matrices built from simple tensor networks. In this work we leverage the full power of this abstraction to design new algorithms for certain continuous tensor decomposition problems.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316357"}, {"primary_key": "3121732", "vector": [], "sparse_vector": [], "title": "Stronger l2/l2 compressed sensing; without iterating.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the extensively studied problem of ℓ2/ℓ2 compressed sensing. The main contribution of our work is an improvement over [<PERSON>, <PERSON>, <PERSON> and <PERSON>, STOC 2010] with faster decoding time and significantly smaller column sparsity, answering two open questions of the aforementioned work.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316355"}, {"primary_key": "3121733", "vector": [], "sparse_vector": [], "title": "Breaking quadratic time for small vertex connectivity and an approximation scheme.", "authors": ["Danupon <PERSON>", "Thatchaphol <PERSON>", "Sorrachai <PERSON>i"], "summary": "Vertex connectivity a classic extensively-studied problem. Given an integer k, its goal is to decide if an n-node m-edge graph can be disconnected by removing k vertices. Although a linear-time algorithm was postulated since 1974 [<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>], and despite its sibling problem of edge connectivity being resolved over two decades ago [Karger STOC'96], so far no vertex connectivity algorithms are faster than O(n2) time even for k=4 and m=O(n). In the simplest case where m=O(n) and k=O(1), the O(n2) bound dates five decades back to [Kleitman IEEE Trans. Circuit Theory'69]. For higher m, O(m) time is known for k≤ 3 [<PERSON><PERSON><PERSON>'71; <PERSON><PERSON>, <PERSON><PERSON><PERSON>OM<PERSON>'73], the first O(n2) time is from [<PERSON><PERSON>, <PERSON>, FOCS'87] for k=4 and from [Nagamochi, Ibaraki, Algorithmica'92] for k=O(1). For general k and m, the best bound is Õ(min(kn2, nω+nkω)) [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> FOCS'96; <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> FOCS'86] where Õ hides polylogarithmic terms and ω<2.38 is the matrix multiplication exponent.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316394"}, {"primary_key": "3121734", "vector": [], "sparse_vector": [], "title": "Optimal terminal dimensionality reduction in Euclidean space.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let ε∈(0,1) and X⊂d be arbitrary with |X| having size n>1. The Johnson-<PERSON> lemma states there exists f:X→m with m = O(ε−2logn) such that ∀ x∈ X ∀ y∈ X, ||x−y||2 ≤ ||f(x)−f(y)||2 ≤ (1+ε)||x−y||2 . We show that a strictly stronger version of this statement holds, answering one of the main open questions posed by <PERSON><PERSON><PERSON> et al. in STOC 2018: \"∀ y∈ X\" in the above statement may be replaced with \"∀ y∈d\", so that f not only preserves distances within X, but also distances to X from the rest of space. Previously this stronger version was only known with the worse bound m = O(ε−4logn). Our proof is via a tighter analysis of (a specific instantiation of) the embedding recipe of <PERSON><PERSON><PERSON> et al.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316307"}, {"primary_key": "3121735", "vector": [], "sparse_vector": [], "title": "Fooling polytopes.", "authors": ["Ryan <PERSON>&<PERSON>;Donnell", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a pseudorandom generator that fools m-facet polytopes over {0,1}n with seed length polylog(m) · log(n). The previous best seed length had superlinear dependence on m. An immediate consequence is a deterministic quasipolynomial time algorithm for approximating the number of solutions to any {0,1}-integer program.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316321"}, {"primary_key": "3121736", "vector": [], "sparse_vector": [], "title": "Planar point sets determine many pairwise crossing segments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that any set of n points in general position in the plane determines n1−o(1) pairwise crossing segments. The best previously known lower bound, Ω(√n), was proved more than 25 years ago by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Our proof is fully constructive, and extends to dense geometric graphs.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316328"}, {"primary_key": "3121737", "vector": [], "sparse_vector": [], "title": "On the approximation resistance of balanced linear threshold functions.", "authors": ["<PERSON>"], "summary": "In this paper, we show that there exists a balanced linear threshold function (LTF) which is unique games hard to approximate, refuting a conjecture of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. We also show that the almost monarchy predicate P(x) = sign((k−4)x1 + ∑i=2kxi) is approximable for sufficiently large k.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316374"}, {"primary_key": "3121738", "vector": [], "sparse_vector": [], "title": "Oracle separation of BQP and PH.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a distribution D over inputs in {−1,1}2N, such that: (1) There exists a quantum algorithm that makes one (quantum) query to the input, and runs in time O(logN), that distinguishes between D and the uniform distribution with advantage Ω(1/logN). (2) No Boolean circuit of quasi-polynomial size and constant depth distinguishes between D and the uniform distribution with advantage better than polylog(N)/√N.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316315"}, {"primary_key": "3121739", "vector": [], "sparse_vector": [], "title": "A unifying method for the design of algorithms canonizing combinatorial objects.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We devise a unified framework for the design of canonization algorithms. Using hereditarily finite sets, we define a general notion of combinatorial objects that includes graphs, hypergraphs, relational structures, codes, permutation groups, tree decompositions, and so on.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316338"}, {"primary_key": "3121740", "vector": [], "sparse_vector": [], "title": "Memory-sample tradeoffs for linear regression with small error.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of performing linear regression over a stream of d-dimensional examples, and show that any algorithm that uses a subquadratic amount of memory exhibits a slower rate of convergence than can be achieved without memory constraints. Specifically, consider a sequence of labeled examples (a1,b1), (a2,b2)…, with ai drawn independently from a d-dimensional isotropic Gaussian, and where bi = ⟨ ai, x⟩ + ηi, for a fixed x ∈ ℝd with ||x||2 = 1 and with independent noise ηi drawn uniformly from the interval [−2−d/5,2−d/5]. We show that any algorithm with at most d2/4 bits of memory requires at least Ω(d loglog1/є) samples to approximate x to ℓ2 error є with probability of success at least 2/3, for є sufficiently small as a function of d. In contrast, for such є, x can be recovered to error є with probability 1−o(1) with memory O(d2 log(1/є)) using d examples. This represents the first nontrivial lower bounds for regression with super-linear memory, and may open the door for strong memory/sample tradeoffs for continuous optimization.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316403"}, {"primary_key": "3121741", "vector": [], "sparse_vector": [], "title": "Near-optimal lower bounds on the threshold degree and sign-rank of AC0.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The threshold degree of a Boolean function f∶{0,1}n→{0,1} is the minimum degree of a real polynomial p that represents f in sign: sgn p(x)=(−1)f(x). A related notion is sign-rank, defined for a Boolean matrix F=[Fij] as the minimum rank of a real matrix M with sgn Mij=(−1)Fij. Determining the maximum threshold degree and sign-rank achievable by constant-depth circuits (AC0) is a well-known and extensively studied open problem, with complexity-theoretic and algorithmic applications.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316408"}, {"primary_key": "3121742", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON> type theorems for quadratic polynomials.", "authors": ["<PERSON>"], "summary": "We prove Sylvester-<PERSON><PERSON> type theorems for quadratic polynomials. Specifically, we prove that if a finite collection Q, of irreducible polynomials of degree at most 2, satisfy that for every two polynomials Q1,Q2∈ Q there is a third polynomial Q3∈Q so that whenever Q1 and Q2 vanish then also Q3 vanishes, then the linear span of the polynomials in Q has dimension O(1). We also prove a colored version of the theorem: If three finite sets of quadratic polynomials satisfy that for every two polynomials from distinct sets there is a polynomial in the third set satisfying the same vanishing condition then all polynomials are contained in an O(1)-dimensional space.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316341"}, {"primary_key": "3121743", "vector": [], "sparse_vector": [], "title": "Local decodability of the <PERSON><PERSON>-<PERSON> transform.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Burrows-Wheeler Transform (BWT) is among the most influential discoveries in text compression and DNA storage. It is a reversible preprocessing step that rearranges an n-letter string into runs of identical characters (by exploiting context regularities), resulting in highly compressible strings, and is the basis of the bzip compression program. Alas, the decoding process of BWT is inherently sequential and requires Ω(n) time even to retrieve a single character.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316317"}, {"primary_key": "3121744", "vector": [], "sparse_vector": [], "title": "Towards the locality of Vizing&apos;s theorem.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hoa T. Vu"], "summary": "Vizing showed that it suffices to color the edges of a simple graph using Δ + 1 colors, where Δ is the maximum degree of the graph. However, up to this date, no efficient distributed edge-coloring algorithm is known for obtaining such coloring, even for constant degree graphs. The current algorithms that get closest to this number of colors are the randomized (Δ + Θ(√Δ))-edge-coloring algorithm that runs in (n) rounds by <PERSON> et al. [SODA 2018] and the deterministic (Δ + (n))-edge-coloring algorithm that runs in (Δ, logn) rounds by <PERSON><PERSON><PERSON><PERSON> et al. [STOC 2018].", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316393"}, {"primary_key": "3121745", "vector": [], "sparse_vector": [], "title": "A quantum-inspired classical algorithm for recommendation systems.", "authors": ["<PERSON><PERSON>"], "summary": "We give a classical analogue to <PERSON><PERSON><PERSON><PERSON> and <PERSON>'s quantum recommendation system, previously believed to be one of the strongest candidates for provably exponential speedups in quantum machine learning. Our main result is an algorithm that, given an $m \\times n$ matrix in a data structure supporting certain $\\ell^2$-norm sampling operations, outputs an $\\ell^2$-norm sample from a rank-$k$ approximation of that matrix in time $O(\\text{poly}(k)\\log(mn))$, only polynomially slower than the quantum algorithm. As a consequence, <PERSON><PERSON><PERSON><PERSON> and <PERSON>'s algorithm does not in fact give an exponential speedup over classical algorithms. Further, under strong input assumptions, the classical recommendation system resulting from our algorithm produces recommendations exponentially faster than previous classical systems, which run in time linear in $m$ and $n$. The main insight of this work is the use of simple routines to manipulate $\\ell^2$-norm sampling distributions, which play the role of quantum superpositions in the classical setting. This correspondence indicates a potentially fruitful framework for formally comparing quantum machine learning algorithms to classical machine learning algorithms.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316310"}, {"primary_key": "3121746", "vector": [], "sparse_vector": [], "title": "Exponential separation between shallow quantum circuits and unbounded fan-in shallow classical circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>\\\"{o}nig (Science, 2018) exhibited a search problem called the 2D Hidden Linear Function (2D HLF) problem that can be solved exactly by a constant-depth quantum circuit using bounded fan-in gates (or QNC^0 circuits), but cannot be solved by any constant-depth classical circuit using bounded fan-in AND, OR, and NOT gates (or NC^0 circuits). In other words, they exhibited a search problem in QNC^0 that is not in NC^0. We strengthen their result by proving that the 2D HLF problem is not contained in AC^0, the class of classical, polynomial-size, constant-depth circuits over the gate set of unbounded fan-in AND and OR gates, and NOT gates. We also supplement this worst-case lower bound with an average-case result: There exists a simple distribution under which any AC^0 circuit (even of nearly exponential size) has exponentially small correlation with the 2D HLF problem. Our results are shown by constructing a new problem in QNC^0, which we call the Relaxed Parity Halving Problem, which is easier to work with. We prove our AC^0 lower bounds for this problem, and then show that it reduces to the 2D HLF problem. As a step towards even stronger lower bounds, we present a search problem that we call the Parity Bending Problem, which is in QNC^0/qpoly (QNC^0 circuits that are allowed to start with a quantum state of their choice that is independent of the input), but is not even in AC^0[2] (the class AC^0 with unbounded fan-in XOR gates). All the quantum circuits in our paper are simple, and the main difficulty lies in proving the classical lower bounds. For this we employ a host of techniques, including a refinement of H{\\aa}stad's switching lemmas for multi-output circuits that may be of independent interest, the Razborov-Smolensky AC^0[2] lower bound, Vazirani's XOR lemma, and lower bounds for non-local games.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316404"}, {"primary_key": "3121747", "vector": [], "sparse_vector": [], "title": "Separating monotone VP and VNP.", "authors": ["<PERSON>"], "summary": "This work is about the monotone versions of the algebraic complexity classes VP and VNP. The main result is that monotone VNP is strictly stronger than monotone VP.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316311"}, {"primary_key": "3121748", "vector": [], "sparse_vector": [], "title": "Optimal succinct rank data structure via approximate nonnegative tensor decomposition.", "authors": ["Huacheng Yu"], "summary": "Given an n-bit array A, the succinct rank data structure problem asks to construct a data structure using space n+r bits for r≪ n, supporting rank queries of form rank(u)=∑i=0u−1 A[i]. In this paper, we design a new succinct rank data structure with r=n/(logn)Ω(t)+n1−c and query time O(t) for some constant c>0, improving the previous best-known by <PERSON><PERSON><PERSON><PERSON><PERSON>, which has r=n/(logn/t)Ω(t)+Õ(n3/4) bits of redundancy. For r>n1−c, our space-time tradeoff matches the cell-probe lower bound by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>, which asserts that r must be at least n/(logn)O(t). Moreover, one can avoid an n1−c-bit lookup table when the data structure is implemented in the cell-probe model, achieving r=⌈ n/(logn)Ω(t)⌉. It matches the lower bound for the full range of parameters.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3313276.3316352"}, {"primary_key": "3140745", "vector": [], "sparse_vector": [], "title": "Proceedings of the 51st Annual ACM SIGACT Symposium on Theory of Computing, STOC 2019, Phoenix, AZ, USA, June 23-26, 2019.", "authors": ["<PERSON>", "<PERSON>"], "summary": "No abstract available.", "published": "2019-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": ""}]