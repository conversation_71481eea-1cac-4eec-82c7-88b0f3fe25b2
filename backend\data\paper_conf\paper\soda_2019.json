[{"primary_key": "3118882", "vector": [], "sparse_vector": [], "title": "Zeros of Holant problems: locations and algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present fully polynomial-time (deterministic or randomised) approximation schemes for Holant problems, defined by a non-negative constraint function satisfying a generalised second order recurrence modulo a couple of exceptional cases. As a consequence, any non-negative Holant problem on cubic graphs has an efficient approximation algorithm unless the problem is equivalent to approximately counting perfect matchings, a central open problem in the area. This is in sharp contrast to the computational phase transition shown by 2-state spin systems on cubic graphs. Our main technique is the recently established connection between zeros of graph polynomials and approximate counting. We also use the \"winding\" technique to deduce the second result on cubic graphs.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.137"}, {"primary_key": "3118883", "vector": [], "sparse_vector": [], "title": "(1 + ε)-Approximate Incremental Matching in Constant Deterministic Amortized Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the matching problem in the incremental setting, where we are given a sequence of edge insertions and aim at maintaining a near-maximum cardinality matching of the graph with small update time. We present a deterministic algorithm that, for any constant ε > 0, maintains a (1 + ε)-approximate matching with constant amortized update time per insertion.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.114"}, {"primary_key": "3118884", "vector": [], "sparse_vector": [], "title": "Pricing for Online Resource Allocation: Intervals and Paths.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present pricing mechanisms for several online resource allocation problems which obtain tight or nearly tight approximations to social welfare. In our settings, buyers arrive online and purchase bundles of items; buyers’ values for the bundles are drawn from known distributions. This problem is closely related to the so-called prophet-inequality of <PERSON><PERSON><PERSON> and <PERSON><PERSON> [23] and its extensions in recent literature. Motivated by applications to cloud economics, we consider two kinds of buyer preferences. In the first, items correspond to different units of time at which a resource is available; the items are arranged in a total order and buyers desire intervals of items. The second corresponds to bandwidth allocation over a tree network; the items are edges in the network and buyers desire paths.Because buyers’ preferences have complementarities in the settings we consider, recent constant-factor approximations via item prices do not apply, and indeed strong negative results are known. We develop static, anonymous bundle pricing mechanisms.For the interval preferences setting, we show that static, anonymous bundle pricings achieve a sublogarithmic competitive ratio, which is optimal (within constant factors) over the class of all online allocation algorithms, truthful or not. For the path preferences setting, we obtain a nearly-tight logarithmic competitive ratio. Both of these results exhibit an exponential improvement over item pricings for these settings. Our results extend to settings where the seller has multiple copies of each item, with the competitive ratio decreasing linearly with supply. Such a gradual tradeoff between supply and the competitive ratio for welfare was previously known only for the single item prophet inequality.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.119"}, {"primary_key": "3118885", "vector": [], "sparse_vector": [], "title": "Near-optimal Bootstrapping of Hitting Sets for Algebraic Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The classical lemma of Ore-DeMillo-Lipton-Schwartz-Zippel states that any nonzero polynomial f(xi, …, xn) of degree at most s will evaluate to a nonzero value at some point on a grid with |S| > s. Thus, there is a deterministic polynomial identity test (PIT) for all degrees size-s algebraic circuits in n variables that runs in time poly(s) · (s + 1)n. In a surprising recent result, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (STOC 2018) showed any deterministic blackbox PIT algorithm for degree-s, size-s, n-variate circuits with running time as bad as (sn0.5−δ) Huge(n), where δ > 0 and Huge(n) is an arbitrary function, can be used to construct blackbox PIT algorithms for degree-s size s circuits with running time sexp(exp(O(log* s))). <PERSON><PERSON><PERSON> et al. asked if a similar conclusion followed if their hypothesis was weakened to having deterministic PIT with running time so(n) · Huge(n). In this paper, we answer their question in the affirmative. We show that, given a deterministic blackbox PIT that runs in time so(n) · Huge(n) for all degree-s size-s algebraic circuits over n variables, we can obtain a deterministic blackbox PIT that runs in time sexp(exp(O(log* s))) for all degree-s size-s algebraic circuits over n variables. In other words, any blackbox PIT with just a slightly nontrivial exponent of s compared to the trivial sO(n) test can be used to give a nearly polynomial time blackbox PIT algorithm.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.40"}, {"primary_key": "3118886", "vector": [], "sparse_vector": [], "title": "High-Dimensional Robust Mean Estimation in Nearly-Linear Time.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Ge"], "summary": "We study the fundamental problem of high-dimensional mean estimation in a robust model where a constant fraction of the samples are adversarially corrupted. Recent work gave the first polynomial time algorithms for this problem with dimension-independent error guarantees for several families of structured distributions.In this work, we give the first nearly-linear time algorithms for high-dimensional robust mean estimation. Specifically, we focus on distributions with (i) known covariance and sub-gaussian tails, and (ii) unknown bounded covariance. Given N samples on ℝd, an ∊-fraction of which may be arbitrarily corrupted, our algorithms run in time Õ(Nd)/poly(∊) and approximate the true mean within the information-theoretically optimal error, up to constant factors. Previous robust algorithms with comparable error guarantees have running times , for ∊ = Ω(1).Our algorithms rely on a natural family of SDPs parameterized by our current guess v for the unknown mean µ*. We give a win-win analysis establishing the following: either a near-optimal solution to the primal SDP yields a good candidate for µ* — independent of our current guess v — or a near-optimal solution to the dual SDP yields a new guess v' whose distance from µ* is smaller by a constant factor. We exploit the special structure of the corresponding SDPs to show that they are approximately solvable in nearly-linear time. Our approach is quite general, and we believe it can also be applied to obtain nearly-linear time algorithms for other high-dimensional robust learning problems.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.171"}, {"primary_key": "3118887", "vector": [], "sparse_vector": [], "title": "Extremal and probabilistic results for order types.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A configuration is a finite set of points in the plane. Two configurations A and B have the same order type if there exists a bijection between them preserving the orientation of every ordered triple. We investigate extremal and probabilistic problems related to configurations in general position. We focus on problems involving forbidden configurations or monotone/hereditary properties. Thus, we typically have a given configuration B and we consider the property of being \"B-free\": a configuration A is B-free if no subset of points of A has the same order type as B. We prove a significant bound on the number of B-free N-point configurations contained in the m × m grid [m]2 for arbitrary configurations B. We consider random N-point configurations UN in the unit square, in which each of the N points is chosen uniformly at random and independently of all other points. The above-mentioned enumeration result for B-free configurations in the grid is then used to prove strong bounds for the probability that the random set UN should be B-free for any given B. We also investigate the threshold function N0 = N0(n) for the property that UN should be n-universal, that is, should contain all n-point configurations in general position. As it turns out, N0 = N0(n) is doubly exponential in n; we prove that log log N0 = Θ(n). Our arguments are mostly geometric and combinatorial, with the recent container method playing an important role. Also important for us is how large a grid one needs to consider when representing n-point configurations in general position.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.27"}, {"primary_key": "3118888", "vector": [], "sparse_vector": [], "title": "Tight Competitive Ratios of Classic Matching Algorithms in the Fully Online Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Runzhou Tao", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON> et al. (STOC 2018) introduced the fully online matching problem, a generalization of the classic online bipartite matching problem in that it allows all vertices to arrive online and considers general graphs. They showed that the ranking algorithm by <PERSON><PERSON> et al. (STOC 1990) is strictly better than 0.5-competitive and the problem is strictly harder than the online bipartite matching problem in that no algorithms can be (1 – 1/e)-competitive.This paper pins down two tight competitive ratios of classic algorithms for the fully online matching problem. For the fractional version of the problem, we show that a natural instantiation of the water-filling algorithm is 2 – ≈ 0.585-competitive, together with a matching hardness result. Interestingly, our hardness result applies to arbitrary algorithms in the edge-arrival models of the online matching problem, improving the state-of-art upper bound. For integral algorithms, we show a tight competitive ratio of ≈ 0.567 for the ranking algorithm on bipartite graphs, matching a hardness result by <PERSON> et al. (STOC 2018).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.178"}, {"primary_key": "3118889", "vector": [], "sparse_vector": [], "title": "Non-empty Bins with Simple Tabulation Hashing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the hashing of a set X ⊆ U with |X| = m using a simple tabulation hash function h : U → [n] = {0, …, n – 1} and analyse the number of non-empty bins, that is, the size of h(X). We show that the expected size of h(X) matches that with fully random hashing to within low-order terms. We also provide concentration bounds. The number of non-empty bins is a fundamental measure in the balls and bins paradigm, and it is critical in applications such as Bloom filters and Filter hashing. For example, normally Bloom filters are proportioned for a desired low false-positive probability assuming fully random hashing. Our results imply that if we implement the hashing with simple tabulation, we obtain the same low false-positive probability for any possible input.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.153"}, {"primary_key": "3118890", "vector": [], "sparse_vector": [], "title": "SETH-Based Lower Bounds for Subset Sum and Bicriteria Path.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Subset Sum and k-SAT are two of the most extensively studied problems in computer science, and conjectures about their hardness are among the cornerstones of fine-grained complexity. An important open problem in this area is to base the hardness of one of these problems on the other.Our main result is a tight reduction from k-SAT to Subset Sum on dense instances, proving that <PERSON><PERSON>'s 1962 pseudo-polynomial O*(T)-time algorithm for Subset Sum on n numbers and target T cannot be improved to time T1–ε · 2o(n) for any ε > 0, unless the Strong Exponential Time Hypothesis (SETH) fails.As a corollary, we prove a \"Direct-OR\" theorem for Subset Sum under SETH, offering a new tool for proving conditional lower bounds: It is now possible to assume that deciding whether one out of N given instances of Subset Sum is a YES instance requires time (NT)1–o(1). As an application of this corollary, we prove a tight SETH-based lower bound for the classical Bicriteria s, t-PATH problem, which is extensively studied in Operations Research. We separate its complexity from that of Subset Sum: On graphs with m edges and edge lengths bounded by L, we show that the O(Lm) pseudo-polynomial time algorithm by <PERSON><PERSON><PERSON> from 1966 cannot be improved to Õ(L + m), in contrast to a recent improvement for Subset Sum (<PERSON><PERSON>, <PERSON>OD<PERSON> 2017).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.3"}, {"primary_key": "3118891", "vector": [], "sparse_vector": [], "title": "Approximate Nearest Neighbor Searching with Non-Euclidean and Weighted Distances.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Fonseca", "<PERSON>"], "summary": "We present a new approach to ε-approximate nearest-neighbor queries in fixed dimension under a variety of non-Euclidean distances. We consider two families of distance functions: (a) convex scaling distance functions including the <PERSON><PERSON><PERSON><PERSON> distance, the Minkowski metric and multiplicative weights, and (b) Bregman divergences including the <PERSON><PERSON><PERSON>-<PERSON> divergence and the <PERSON><PERSON><PERSON>-Sai<PERSON> distance.As the fastest known data structures rely on the lifting transformation, their application is limited to the Euclidean metric, and alternative approaches for other distance functions are much less efficient. We circumvent the reliance on the lifting transformation by a careful application of convexification, which appears to be relatively new to computational geometry.We are given n points in ℝd, each a site possibly defining its own distance function. Under mild assumptions on the growth rates of these functions, the proposed data structures answer queries in logarithmic time using O(n log(1/ε)/εd/2) space, which nearly matches the best known results for the Euclidean metric.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.23"}, {"primary_key": "3118892", "vector": [], "sparse_vector": [], "title": "Iterative Refinement for ℓp-norm Regression.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give improved algorithms for the ℓp-regression problem, minx ‖x‖p such that Ax = b, for all p ∊ (1, 2) ∪ (2, ∞). Our algorithms obtain a high accuracy solution in iterations, where each iteration requires solving an m × m linear system, with m being the dimension of the ambient space.Incorporating a procedure for maintaining an approximate inverse of the linear systems that we need to solve at each iteration, we give algorithms for solving ℓp-regression to 1/poly(n) accuracy that runs in time Õp(mmax{ω, 7/3}), where ω is the matrix multiplication constant. For the current best value of ω > 2.37, this means that we can solve ℓp regression as fast as ℓ2 regression, for all constant p bounded away from 1.Our algorithms can be combined with nearly-linear time solvers for linear systems in graph Laplacians to give minimum ℓp-norm flow / voltage solutions to 1/poly(n) accuracy on an undirected graph with m edges in time.For sparse graphs and for matrices with similar dimensions, our iteration counts and running times improve upon the p-norm regression algorithm by [<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-Li STOC'18], as well as general purpose convex optimization algorithms. At the core of our algorithms is an iterative refinement scheme for ℓp-norms, using the quadratically-smoothed ℓp-norms introduced in the work of <PERSON><PERSON><PERSON> et al. Formally, given an initial solution, we construct a problem that seeks to minimize a quadratically-smoothed ℓp norm over a subspace, such that a crude solution to this problem allows us to improve the initial solution by a constant factor, leading to algorithms with fast convergence.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.86"}, {"primary_key": "3118893", "vector": [], "sparse_vector": [], "title": "Theorems of Carathéodory, Helly, and Tverberg without dimension.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by <PERSON><PERSON> [6], we initiate a systematic study of the 'no-dimensional' analogues of some basic theorems in combinatorial and convex geometry, including the colorful <PERSON><PERSON><PERSON><PERSON><PERSON>'s theorem, <PERSON><PERSON><PERSON>'s theorem, <PERSON><PERSON>'s theorem as well as their fractional and colorful extensions.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.143"}, {"primary_key": "3118894", "vector": [], "sparse_vector": [], "title": "Stochastic Submodular Cover with Limited Adaptivity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the submodular cover problem, we are given a non-negative monotone submodular function f over a ground set E of items, and the goal is to choose a smallest subset S ⊆ E such that f(S) = Q where Q = f(E). In the stochastic version of the problem, we are given m stochastic items which are different random variables that independently realize to some item in E, and the goal is to find a smallest set of stochastic items whose realization R satisfies f(R) = Q. The problem captures as a special case the stochastic set cover problem and more generally, stochastic covering integer programs.A fully adaptive algorithm for stochastic submodular cover chooses an item to realize and based on its realization, decides which item to realize next. A non-adaptive algorithm on the other hand needs to choose a permutation of items beforehand and realize them one by one in the order specified by this permutation until the function value reaches Q. The cost of the algorithm in both case is the number (or costs) of items realized by the algorithm. It is not difficult to show that even for the coverage function there exist instances where the expected cost of a fully adaptive algorithm and a non-adaptive algorithm are separated by Ω(Q). This strong separation, often referred to as the adaptivity gap, is in sharp contrast to the separations observed in the framework of stochastic packing problems where the performance gap for many natural problem is close to the poly-time approximability of the non-stochastic version of the problem. Motivated by this striking gap between the power of adaptive and non-adaptive algorithms, we consider the following question in this work: does one need full power of adaptivity to obtain a near-optimal solution to stochastic submodular cover? In particular, how does the performance guarantees change when an algorithm interpolates between these two extremes using a few rounds of adaptivity.Towards this end, we define an r-round adaptive algorithm to be an algorithm that chooses a permutation of all available items in each round k ∊ [r], and a threshold τk, and realizes items in the order specified by the permutation until the function value is at least τk. The permutation for each round k is chosen adaptively based on the realization in the previous rounds, but the ordering inside each round remains fixed regardless of the realizations seen inside the round. Our main result is that for any integer r, there exists a poly-time r-round adaptive algorithm for stochastic submodular cover whose expected cost is Õ(Q1/r) times the expected cost of a fully adaptive algorithm. Prior to our work, such a result was not known even for the case of r = 1 and when f is the coverage function. On the other hand, we show that for any r, there exist instances of the stochastic submodular cover problem where no r-round adaptive algorithm can achieve better than Ω(Q1/r) approximation to the expected cost of a fully adaptive algorithm. Our lower bound result holds even for coverage function and for algorithms with unbounded computational power. Thus our work shows that logarithmic rounds of adaptivity are necessary and sufficient to obtain near-optimal solutions to the stochastic submodular cover problem, and even few rounds of adaptivity are sufficient to sharply reduce the adaptivity gap.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.21"}, {"primary_key": "3118895", "vector": [], "sparse_vector": [], "title": "Interval Vertex Deletion Admits a Polynomial Kernel.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Pranaben<PERSON> Mi<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a graph G and an integer k, the Interval Vertex Deletion (IVD) problem asks whether there exists a subset S ⊆ V(G) of size at most k such that G–S is an interval graph. This problem is known to be NP-complete [<PERSON><PERSON><PERSON><PERSON>, STOC'78]. Originally in 2012, <PERSON> and <PERSON> showed that IVD is fixed parameter tractable: they exhibited an algorithm with running time 10knO(1) [<PERSON> and <PERSON>, SODA'14]. The existence of a polynomial kernel for IVD remained a well-known open problem in Parameterized Complexity. In this paper, we settle this problem in the affirmative. We also introduce a \"bounded intersection\" variant of the classical Two Families theorem of Bollobás. We believe this result will find further applications in combinatorics and algorithm design.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.103"}, {"primary_key": "3118896", "vector": [], "sparse_vector": [], "title": "Perron-Frobenius Theory in Nearly Linear Time: Positive Eigenvectors, M-matrices, Graph Kernels, and Other Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we provide nearly linear time algorithms for several problems closely associated with the classic Perron-Frobenius theorem, including computing Perron vectors, i.e. entrywise non-negative eigenvectors of non-negative matrices, and solving linear systems in asymmetric M-matrices, a generalization of Laplacian systems. The running times of our algorithms depend nearly linearly on the input size and polylogarithmically on the desired accuracy and problem condition number.Leveraging these results we also provide improved running times for a broader range of problems including computing random walk-based graph kernels, computing Katz centrality, and more. The running times of our algorithms improve upon previously known results which either depended polynomially on the condition number of the problem, required quadratic time, or only applied to special cases.We obtain these results by providing new iterative methods for reducing these problems to solving linear systems in Row-Column Diagonally Dominant (RCDD) matrices. Our methods are related to the classic shift-and-invert preconditioning technique for eigenvector computation and constitute the first alternative to the result in <PERSON> et al. (2016) for reducing stationary distribution computation and solving directed Laplacian systems to solving RCDD systems.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.85"}, {"primary_key": "3118898", "vector": [], "sparse_vector": [], "title": "Rapid Mixing of the Switch Markov Chain for Strongly Stable Degree Sequences and 2-Class Joint Degree Matrices.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "PREVIOUS ARTICLEOn coalescence time in graphs: When is coalescing as fast as meeting?: Extended Abstract", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.60"}, {"primary_key": "3118899", "vector": [], "sparse_vector": [], "title": "Quantum Speedups for Exponential-Time Dynamic Programming Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper we study quantum algorithms for NP-complete problems whose best classical algorithm is an exponential time application of dynamic programming. We introduce the path in the hypercube problem that models many of these dynamic programming algorithms. In this problem we are asked whether there is a path from 0n to 1n in a given subgraph of the Boolean hypercube, where the edges are all directed from smaller to larger Hamming weight. We give a quantum algorithm that solves path in the hypercube in time O*(1.817n). The technique combines <PERSON><PERSON>'s search with computing a partial dynamic programming table. We use this approach to solve a variety of vertex ordering problems on graphs in the same time O*(1.817n), and graph bandwidth in time O*(2.946n). Then we use similar ideas to solve the travelling salesman problem and minimum set cover in time O*(1.728n).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.107"}, {"primary_key": "3118900", "vector": [], "sparse_vector": [], "title": "A PTAS for Euclidean TSP with Hyperplane Neighborhoods.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the Traveling Salesperson Problem with Neighborhoods (TSPN), we are given a collection of geometric regions in some space. The goal is to output a tour of minimum length that visits at least one point in each region. Even in the Euclidean plane, TSPN is known to be APX-hard [20], which gives rise to studying more tractable special cases of the problem. In this paper, we focus on the fundamental special case of regions that are hyperplanes in the d-dimensional Euclidean space. This case contrasts the much-better understood case of so-called fat regions [16, 34].While for d = 2 an exact algorithm with running time O(n5) is known [28], settling the exact approximability of the problem for d = 3 has been repeatedly posed as an open question [23, 24, 34, 40]. To date, only an approximation algorithm with guarantee exponential in d is known [24], and NP-hardness remains open.For arbitrary fixed d, we develop a Polynomial Time Approximation Scheme (PTAS) that works for both the tour and path version of the problem. Our algorithm is based on approximating the convex hull of the optimal tour by a convex polytope of bounded complexity. Such polytopes are represented as solutions of a sophisticated LP formulation, which we combine with the enumeration of crucial properties of the tour. As the approximation guarantee approaches 1, our scheme adjusts the complexity of the considered polytopes accordingly.In the analysis of our approximation scheme, we show that our search space includes a sufficiently good approximation of the optimum. To do so, we develop a novel and general sparsification technique to transform an arbitrary convex polytope into one with a constant number of vertices and, in turn, into one of bounded complexity in the above sense. Hereby, we maintain important properties of the polytope.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.67"}, {"primary_key": "3118901", "vector": [], "sparse_vector": [], "title": "A Nearly-Linear Bound for Chasing Nested Convex Bodies.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON> and <PERSON><PERSON> [8] introduced the convex body chasing problem to explore the interplay between geometry and competitive ratio in metrical task systems. In convex body chasing, at each time step t ∊ ℕ, the online algorithm receives a request in the form of a convex body Kt ⊂ ℝ and must output a point xt ∊ Kt. The goal is to minimize the total movement between consecutive output points, where the distance is measured in some given norm. This problem is still far from being understood. Recently <PERSON><PERSON> et al. [4] gave an 6d(d!)2-competitive algorithm for the nested version, where each convex body is contained within the previous one. We propose a different strategy which is O(d log d)-competitive algorithm for this nested convex body chasing problem. Our algorithm works for any norm. This result is almost tight, given an Ω(d) lower bound for the ℓ∞ norm [8].", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.8"}, {"primary_key": "3118902", "vector": [], "sparse_vector": [], "title": "Constructive Polynomial Partitioning for Algebraic Curves in R3 with Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In 2015, <PERSON><PERSON> proved that, for any set of k-dimensional varieties in ℝ3 and for any positive integer D, there exists a polynomial of degree at most D whose zero-set divides ℝ3 into open connected \"cells,\" so that only a small fraction of the given varieties intersect each cell. <PERSON><PERSON>'s result generalized an earlier result of <PERSON><PERSON> and <PERSON> for points.<PERSON><PERSON>'s proof relies on a variant of the <PERSON><PERSON><PERSON><PERSON> theorem, and for k > 0, it is unknown how to obtain an explicit representation of such a partitioning polynomial and how to construct it efficiently. In particular, it is unknown how to effectively construct such a polynomial for curves (or even lines) in ℝ3.We present an efficient algorithmic construction for this setting. Given a set of n input curves and a positive integer D, we efficiently construct a decomposition of space into O(D3 log3 D) open cells, each of which meets at most O(n/D2) curves from the input. The construction time is O(n2), where the constant of proportionality depends on D and the maximum degree of the polynomials defining the input curves. For the case of lines in 3-space we present an improved implementation, whose running time is O(n4/3 polylog n).As an application, we revisit the problem of eliminating depth cycles among non-vertical pairwise disjoint triangles in 3-space, recently studied by <PERSON><PERSON><PERSON> et al. (2017) and <PERSON> (2017). Our main result is an algorithm that cuts n triangles into O(n3/2+ε) pieces that are depth cycle free, for any ε > 0. The algorithm runs in O(n3/2+ε) time, which is nearly worst-case optimal. We also sketch several other applications of our effective partitioning for curves in ℝ3.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.163"}, {"primary_key": "3118903", "vector": [], "sparse_vector": [], "title": "Fully Polynomial-Time Approximation Schemes for Fair Rent Division.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of fair rent division that entails splitting the rent and allocating the rooms of an apartment among roommates (agents) in a fair manner. In this setup, a distribution of the rent and an accompanying allocation is said to be fair if it is envy free, i.e., under the imposed rents, no agent has a strictly stronger preference for any other agent's room. The cardinal preferences of the agents are expressed via functions which specify the utilities of the agents for the rooms for every possible room rent/price. While envy-free solutions are guaranteed to exist under reasonably general utility functions, efficient algorithms for finding them were known only for quasilinear utilities. This work addresses this notable gap and develops approximation algorithms for fair rent division with minimal assumptions on the utility functions.Specifically, we show that if the agents have continuous, monotone decreasing, and piecewise-linear utilities, then the fair rent-division problem admits a fully polynomial-time approximation scheme (FPTAS). That is, we develop algorithms that find allocations and prices of the rooms such that for each agent a the utility of the room assigned to it is within a factor of (1 + ε) of the utility of the room most preferred by a. Here, ε > 0 is an approximation parameter, and the running time of the algorithms is polynomial in 1/ε and the input size. In addition, we show that the methods developed in this work provide efficient, truthful mechanisms for special cases of the rent-division problem. Envy-free solutions correspond to equilibria of a two-sided matching market with monetary transfers; hence, this work also provides efficient algorithms for finding approximate equilibria in such markets. We complement the algorithmic results by proving that the fair rent division problem (under continuous, monotone decreasing, and piecewise-linear utilities) lies in the intersection of the complexity classes PPAD and PLS.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.121"}, {"primary_key": "3118904", "vector": [], "sparse_vector": [], "title": "Assignment Mechanisms under Distributional Constraints.", "authors": ["Itai Ashlagi", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the assignment problem of objects to agents with heterogeneous preferences under distributional constraints. Each agent is associated with a publicly known type and has a private ordinal ranking over objects. We are interested in assigning as many agents as possible. Our first contribution is a generalization of the well-known and widely used serial dictatorship. Our mechanism maintains several desirable properties of serial dictatorship, including strategyproofness, Pareto efficiency, and computational tractability while satisfying the distributional constraints with a small error. We also propose a generalization of the probabilistic serial algorithm, which finds an ordinally efficient and envy-free assignment, and also satisfies the distributional constraints with a small error. We show, however, that no ordinally efficient and envy-free mechanism is also weakly strategyproof. Both of our algorithms assign at least the same number of students as the optimum fractional assignment.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.15"}, {"primary_key": "3118906", "vector": [], "sparse_vector": [], "title": "Coresets Meet EDCS: Algorithms for Matching and Vertex Cover on Massive Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "There is a rapidly growing need for scalable algorithms that solve classical graph problems, such as maximum matching and minimum vertex cover, on massive graphs. For massive inputs, several different computational models have been introduced, including the streaming model, the distributed communication model, and the massively parallel computation (MPC) model that is a common abstraction of MapReduce-style computation. In each model, algorithms are analyzed in terms of resources such as space used or rounds of communication needed, in addition to the more traditional approximation ratio.In this paper, we give a single unified approach that yields better approximation algorithms for matching and vertex cover in all these models. The highlights include:•The first one pass, significantly-better-than-2-approximation for matching in random arrival streams that uses subquadratic space, namely a (1.5 + ε)-approximation streaming algorithm that uses Õ(n15) space for constant ε > 0.•The first 2-round, better-than-2-approximation for matching in the MPC model that uses subquadratic space per machine, namely a (1.5 + ε)-approximation algorithm with memory per machine for constant ε > 0.By building on our unified approach, we further develop parallel algorithms in the MPC model that give a (1+∊)-approximation to matching and an O(1)-approximation to vertex cover in only O(log log n) MPC rounds and O(n/polylog(n)) memory per machine. These results settle multiple open questions posed by <PERSON><PERSON><PERSON><PERSON> et al. [STOC 2018].We obtain our results by a novel combination of two previously disjoint set of techniques, namely randomized composable coresets and edge degree constrained subgraphs (EDCS). We significantly extend the power of these techniques and prove several new structural results. For example, we show that an EDCS is a sparse certificate for large matchings and small vertex covers that is quite robust to sampling and composition.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.98"}, {"primary_key": "3118907", "vector": [], "sparse_vector": [], "title": "Sublinear Algorithms for (Δ + 1) Vertex Coloring.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Any graph with maximum degree Δ admits a proper vertex coloring with Δ+1 colors that can be found via a simple sequential greedy algorithm in linear time and space. But can one find such a coloring via a sublinear algorithm?We answer this fundamental question in the affirmative for several canonical classes of sublinear algorithms including graph streaming, sublinear time, and massively parallel computation (MPC) algorithms. In particular, we design:•A single-pass semi-streaming algorithm in dynamic streams using Õ(n) space. The only known semi-streaming algorithm prior to our work was a folklore O(log n)-pass algorithm obtained by simulating classical distributed algorithms in the streaming model.•A sublinear-time algorithm in the standard query model that allows neighbor queries and pair queries using time. We further show that any algorithm that outputs a valid coloring with sufficiently large constant probability requires time. No non-trivial sublinear time algorithms were known prior to our work.•A parallel algorithm in the massively parallel computation (MPC) model using Õ(n) memory per machine and O(1) MPC rounds. Our number of rounds significantly improves upon the recent O(log log Δ · log* (n))-round algorithm of Parter [ICALP 2018].At the core of our results is a remarkably simple meta-algorithm for the (Δ + 1) coloring problem: Sample O(log n) colors for each vertex independently and uniformly at random from the Δ + 1 colors; find a proper coloring of the graph using only the sampled colors of each vertex. As our main result, we prove that the sampled set of colors with high probability contains a proper coloring of the input graph. The sublinear algorithms are then obtained by designing efficient algorithms for finding a proper coloring of the graph from the sampled colors in each model.We note that all our upper bound results for (Δ + 1) coloring are either optimal or close to best possible in each model studied. We also establish new lower bounds that rule out the possibility of achieving similar results in these models for the closely related problems of maximal independent set and maximal matching. Collectively, our results highlight a sharp contrast between the complexity of (Δ+1) coloring vs maximal independent set and maximal matching in various models of sublinear computation even though all three problems are solvable by a simple greedy algorithm in the classical setting.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.48"}, {"primary_key": "3118908", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Maximal Independent Set with Sublinear in n Update Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The first fully dynamic algorithm for maintaining a maximal independent set (MIS) with update time that is sublinear in the number of edges was presented recently by the authors of this paper [<PERSON><PERSON><PERSON> et al., STOC'18]. The algorithm is deterministic and its update time is O(m3/4), where m is the (dynamically changing) number of edges. Subsequently, <PERSON> and <PERSON> and independently <PERSON> and <PERSON> [arXiv, April 2018] presented deterministic algorithms for dynamic MIS with update times of O(m2/3) and O(m2/3 ), respectively. <PERSON> and <PERSON> also gave a randomized algorithm with update time . Moreover, they provided some partial (conditional) hardness results hinting that the update time of m1/2–ε, and in particular n1–ε for n-vertex dense graphs, is a natural barrier for this problem for any constant ε > 0, for deterministic and randomized algorithms that satisfy a certain natural property.In this paper, we break this natural barrier and present the first fully dynamic (randomized) algorithm for maintaining an MIS with update time that is always sublinear in the number of vertices, namely, an expected amortized update. We also show that a simpler variant of our algorithm can already achieve an Õ(m1/3) expected amortized update time, which results in an improved performance over our update time algorithm for sufficiently sparse graphs, and breaks the m1/2 barrier of <PERSON> and <PERSON> for all values of m.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.116"}, {"primary_key": "3118909", "vector": [], "sparse_vector": [], "title": "Fast Modular Subset Sum using Linear Sketching.", "authors": ["Kyriakos Axiotis", "Arturs Backurs", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given n positive integers, the Modular Subset Sum problem asks if a subset adds up to a given target t modulo a given integer m. This is a natural generalization of the Subset Sum problem (where m = +∞) with ties to additive combinatorics and cryptography.Recently, in [Bri17, KX17], efficient algorithms have been developed for the non-modular case, running in near-linear pseudo-polynomial time. For the modular case, however, the best known algorithm by <PERSON><PERSON><PERSON> and <PERSON> [KX17] runs in time Õ(m5/4).In this paper, we present an algorithm running in Õ(m) randomized time, which matches a recent conditional lower bound of [ABHS17] based on the Strong Exponential Time Hypothesis. Interestingly, in contrast to most previous results on Subset Sum, our algorithm does not use the Fast Fourier Transform. Instead, it is able to simulate the \"textbook\" Dynamic Programming algorithm much faster, using ideas from linear sketching. This is one of the first applications of sketching-based techniques to obtain fast algorithms for exact combinatorial problems in an offline setting.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.4"}, {"primary_key": "3118910", "vector": [], "sparse_vector": [], "title": "Full Tilt: Universal Constructors for General Shapes with Uniform External Forces.", "authors": ["<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We investigate the problem of assembling general shapes and patterns in a model in which particles move based on uniform external forces until they encounter an obstacle. In this model, corresponding particles may bond when adjacent with one another. Succinctly, this model considers a 2D grid of \"open\" and \"blocked\" spaces, along with a set of slidable polyominoes placed at open locations on the board. The board may be tilted in any of the 4 cardinal directions, causing all slidable polyominoes to move maximally in the specified direction until blocked. By successively applying a sequence of such tilts, along with allowing different polyominoes to stick when adjacent, tilt sequences provide a method to reconfigure an initial board configuration so as to assemble a collection of previous separate polyominoes into a larger shape.While previous work within this model of assembly has focused on designing a specific board configuration for the assembly of a specific given shape, we propose the problem of designing universal configurations that are capable of constructing a large class of shapes and patterns. For these constructions, we present the notions of weak and strong universality which indicate the presence of \"excess\" polyominoes after the shape is constructed. In particular, for given integers h, w, we show that there exists a weakly universal configuration with O(hw) 1 × 1 slidable particles that can be reconfigured to build any h × w patterned rectangle. We then expand this result to show that there exists a weakly universal configuration that can build any h × w-bounded size connected shape. Following these results, which require an admittedly relaxed assembly definition, we go on to show the existence of a strongly universal configuration (no excess particles) which can assemble any shape within a previously studied \"drop\" class, while using quadratically less space than previous results.Finally, we include a study of the complexity of deciding if a particle within a configuration may be relocated to another position, and deciding if a given configuration may be transformed into a second given configuration. We show both problems to be PSPACE-complete even when no particles stick to one another and movable particles are restricted to 1 × 1 tiles and a single 2 × 2 polyomino.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.167"}, {"primary_key": "3118911", "vector": [], "sparse_vector": [], "title": "Testing Matrix Rank, Optimally.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Hong<PERSON> Zhang"], "summary": "We show that for the problem of testing if a matrix has rank at most d, or requires changing an ∊-fraction of entries to have rank at most d, there is a non-adaptive query algorithm making Õ(d2/∊) queries. Our algorithm works for any field . This improves upon the previous Õ(d2/∊2) bound (<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>, SODA ′03), and bypasses an Ω(d2/∊2) lower bound of (<PERSON>, <PERSON>, and <PERSON>, KDD ′14) which holds if the algorithm is required to read a submatrix. Our algorithm is the first such algorithm which does not read a submatrix, and instead reads a carefully selected non-adaptive pattern of entries in rows and columns of A. We complement our algorithm with a matching query complexity lower bound for non-adaptive testers over any field. We also give tight bounds of Õ(d2) queries in the sensing model for which query access comes in the form of 〈Xi, A〉 := tr(Xi⊺ A); perhaps surprisingly these bounds do not depend on ∊.Testing rank is only one of many tasks in determining if a matrix has low intrinsic dimensionality. We next develop a novel property testing framework for testing numerical properties of a real-valued matrix A more generally, which includes the stable rank, Schatten-p norms, and SVD entropy. Specifically, we propose a bounded entry model, where A is required to have entries bounded by 1 in absolute value. Such a model provides a meaningful framework for testing numerical quantities and avoids trivialities caused by single entries being arbitrarily large. It is also well-motivated by recommendation systems. We give upper and lower bounds for a wide range of problems in this model, and discuss connections to the sensing model above. We obtain several results for estimating the operator norm that may be of independent interest. For example, we show that if the stable rank is constant, ‖A‖F = Ω(n), and the singular value gap σ1(A)/σ2(A) = (1/∊)γ for any constant γ > 0, then the operator norm can be estimated up to a (1 ± ∊)-factor non-adaptively by querying O(1/∊2) entries. This should be contrasted to adaptive methods such as the power method, or previous non-adaptive sampling schemes based on matrix Bernstein inequalities which read a 1/∊2 × 1/∊2 submatrix and thus make Ω(1/∊4) queries. Similar to our non-adaptive algorithm for testing rank, our scheme instead reads a carefully selected pattern of entries.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.46"}, {"primary_key": "3118912", "vector": [], "sparse_vector": [], "title": "An Exponential Speedup in Parallel Running Time for Submodular Maximization without Loss in Approximation.", "authors": ["<PERSON>", "<PERSON>via<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we study the adaptivity of submodular maximization. Adaptivity quantifies the number of sequential rounds that an algorithm makes when function evaluations can be executed in parallel. Adaptivity is a fundamental concept that is heavily studied across a variety of areas in computer science, largely due to the need for parallelizing computation. For the canonical problem of maximizing a monotone submodular function under a cardinality constraint, it is well known that a simple greedy algorithm achieves a 1 – 1/e approximation [NWF78] and that this approximation is optimal for polynomial-time algorithms [NW78]. Somewhat surprisingly, despite extensive efforts on submodular optimization for large-scale datasets, until very recently there was no known algorithm that achieves a constant factor approximation for this problem whose adaptivity is sublinear in the size of the ground set n.Recent work by [BS18] describes an algorithm that obtains an approximation arbitrarily close to 1/3 in O(log n) adaptive rounds and shows that no algorithm can obtain a constant factor approximation in õ(log n) adaptive rounds. This approach achieves an exponential speedup in adaptivity (and parallel running time) at the expense of approximation quality.In this paper we describe a novel approach that yields an algorithm whose approximation is arbitrarily close to the optimal 1 – 1/e guarantee in O(log n) adaptive rounds. This algorithm therefore achieves an exponential speedup in parallel running time for submodular maximization at the expense of an arbitrarily small loss in approximation quality. This guarantee is optimal in both approximation and adaptivity, up to lower order terms.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.19"}, {"primary_key": "3118913", "vector": [], "sparse_vector": [], "title": "Dynamic Double Auctions: Towards First Best.", "authors": ["Santiago R. <PERSON>eiro", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of designing dynamic double auctions for two-sided markets in which a platform intermediates the trade between one seller offering independent items to multiple buyers, repeatedly over a finite horizon, when agents have private values. Motivated by online advertising and ride-hailing markets, we seek to design mechanisms satisfying the following properties: no positive transfers, i.e., the platform never asks the seller to make payments nor buyers are ever paid and periodic individual rationality, i.e., every agent should derive a non-negative utility from every trade opportunity. We provide mechanisms satisfying these requirements that are asymptotically efficient and budget-balanced with high probability as the number of trading opportunities grows. Moreover, we show that the average expected profit obtained by the platform under these mechanisms asymptotically approaches first best (the maximum possible welfare generated by the market).MSC codesDouble auctionstwo-sided marketsdynamic mechanism designinternet advertisingrevenue management", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.11"}, {"primary_key": "3118914", "vector": [], "sparse_vector": [], "title": "A PTAS for ℓp-Low Rank Approximation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>"], "summary": "A number of recent works have studied algorithms for entrywise ℓp-low rank approximation, namely algorithms which given an n × d matrix A (with n ≥ d), output a rank-k matrix B minimizing ‖A – B‖pp = ∑i, j|<PERSON>, j – Bi, j|p when p > 0; and ‖A – B‖0 = ∑i, j[<PERSON>, j ≠ <PERSON>i, j] for p = 0, where [·] is the <PERSON><PERSON><PERSON> bracket, that is, ‖A – B‖0 denotes the number of entries (i, j) for which Ai, j ≠ Bi, j. For p = 1, this is often considered more robust than the SVD, while for p = 0 this corresponds to minimizing the number of disagreements, or robust PCA. This problem is known to be NP-hard for p ∊ {0, 1}, already for k = 1, and while there are polynomial time approximation algorithms, their approximation factor is at best poly(k). It was left open if there was a polynomial-time approximation scheme (PTAS) for ℓp-approximation for any p ≥ 0. We show the following:1.On the algorithmic side, for p ∊ (0, 2), we give the first npoly(k/ε) time (1 + ε)-approximation algorithm. For p = 0, there are various problem formulations, a common one being the binary setting in which A ∊ {0, 1}n×d and B = U · V, where U ∊ {0, 1}n×k and V ∊ {0, 1}k×d. There are also various notions of multiplication U · V, such as a matrix product over the reals, over a finite field, or over a Boolean semiring. We give the first almost-linear time approximation scheme for what we call the Generalized Binary ℓ0-Rank-k problem, for which these variants are special cases. Our algorithm computes (1 + ε)-approximation in time (1/ε)2O(k)/ε2 · nd1+o(1), where o(1) hides a factor (log log d)1.1 / log d. In addition, for the case of finite fields of constant size, we obtain an alternate PTAS running in time n · dpoly(k/ε).2.On the hardness front, for p ∊ (1, 2), we show under the Small Set Expansion Hypothesis and Exponential Time Hypothesis (ETH), there is no constant factor approximation algorithm running in time 2kδ for a constant δ > 0, showing an exponential dependence on k is necessary. For p = 0, we observe that there is no approximation algorithm for the Generalized Binary ℓ0-Rank-k problem running in time 22δk for a constant δ > 0. We also show for finite fields of constant size, under the ETH, that any fixed constant factor approximation algorithm requires 2kδ time for a constant δ > 0.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.47"}, {"primary_key": "3118915", "vector": [], "sparse_vector": [], "title": "On the discrepancy of random low degree set systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by the celebrated <PERSON><PERSON><PERSON> conjecture, we consider the random setting where there are n elements and m sets and each element lies in t randomly chosen sets. In this setting, <PERSON> and <PERSON> showed an O((t log t)1/2) discrepancy bound in the regime when n ≤ m and an O(1) bound when n ≫ mt. In this paper, we give a tight bound for the entire range of n and m, under a mild assumption that t = Ω(log log m)2. The result is based on two steps. First, applying the partial coloring method to the case when n = m logO(1) m and using the properties of the random set system we show that the overall discrepancy incurred is at most . Second, we reduce the general case to that of n ≤ m logO(1) m using LP duality and a careful counting argument.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.157"}, {"primary_key": "3118917", "vector": [], "sparse_vector": [], "title": "A tight Erdős-<PERSON><PERSON><PERSON> function for planar minors.", "authors": ["Wouter <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Let H be a planar graph. By a classical result of <PERSON> and <PERSON>, there is a function f : ℕ → ℝ such that for all k ∊ ℕ and all graphs G, either G contains k vertex-disjoint subgraphs each containing H as a minor, or there is a subset X of at most f(k) vertices such that G–X has no H-minor. We prove that this remains true with f(k) = ck log k for some constant c = c(H). This bound is best possible, up to the value of c, and improves upon a recent result of <PERSON><PERSON><PERSON> and <PERSON> [STOC 2013], who established this with f(k) = ck logd k for some universal constant d. The proof is constructive and yields a polynomial-time O(log OPT)-approximation algorithm for packing subgraphs containing an H-minor.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.90"}, {"primary_key": "3118918", "vector": [], "sparse_vector": [], "title": "Polynomial-time Approximation Scheme for Minimum k-cut in Planar and Minor-free Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The k-cut problem asks, given a connected graph G and a positive integer k, to find a minimum-weight set of edges whose removal splits G into k connected components. We give the first polynomial-time algorithm with approximation factor 2 – ∊ (with constant ∊ > 0) for the k-cut problem in planar and minor-free graphs. Applying more complex techniques, we further improve our method and give a polynomial-time approximation scheme for the k-cut problem in both planar and minor-free graphs. Despite persistent effort, to the best of our knowledge, this is the first improvement for the k-cut problem over standard approximation factor of 2 in any major class of graphs.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.65"}, {"primary_key": "3118919", "vector": [], "sparse_vector": [], "title": "Hardness of Approximation for Morse Matching.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Discrete Morse theory has emerged as a powerful tool for a wide range of problems, including the computation of (persistent) homology. In this context, discrete Morse theory is used to reduce the problem of computing a topological invariant of an input simplicial complex to computing the same topological invariant of a (significantly smaller) collapsed cell or chain complex. Consequently, devising methods for obtaining gradient vector fields on complexes to reduce the size of the problem instance has become an emerging theme over the last decade. While computing the optimal gradient vector field on a simplicial complex is NP-hard, several heuristics have been observed to compute near-optimal gradient vector fields on a wide variety of datasets. Understanding the theoretical limits of these strategies is therefore a fundamental problem in computational topology. In this paper, we consider the approximability of maximization and minimization variants of the Morse matching problem. We establish hardness results for Max-Morse matching and Min-Morse matching, settling an open problem posed by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [20]. In particular, we show that, for a simplicial complex of dimension d ≥ 3 with n simplices, it is NP-hard to approximate Min-Morse matching within a factor of O(n1–∊), for any ∊ > 0. Moreover, we establish hardness of approximation results for Max-Morse matching for simplicial complexes of dimension d ≥ 2, using an L-reduction from Degree 3 Max-Acyclic Subgraph to Max-Morse matching.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.165"}, {"primary_key": "3118920", "vector": [], "sparse_vector": [], "title": "Stochastic Matching with Few Queries: New Algorithms and Tools.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the following stochastic matching problem on both weighted and unweighted graphs: A graph G(V, E) along with a parameter p ∊ (0, 1) is given in the input. Each edge of G is realized independently with probability p. The goal is to select a degree bounded (dependent only on p) subgraph H of G such that the expected maximum realized matching of H is close to that of G.This model of stochastic matching has attracted significant attention over the recent years due to its various applications in kidney exchange, online labor markets, and other matching markets. The most fundamental open question is the best approximation factor achievable for such algorithms that, in the literature, are referred to as non-adaptive algorithms. Prior work has identified breaking (near) half-approximation as a barrier for both weighted and unweighted graphs. Our main results are as follows:•We analyze a simple and clean algorithm and show that for unweighted graphs, it finds an (almost) (≈ 0.6568) approximation by querying Õ(1/p) edges per vertex. This improves over the state-of-the-art 0.5001 approximation of <PERSON><PERSON> et al. [EC'17].•We show that the same algorithm achieves a 0.501 approximation for weighted graphs by querying Õ(1/p) edges per vertex. This improves both, the approximation factor and the per-vertex queries of the 0.5 – ∊ approximations of Yamaguchi and Maehara [SODA'18] and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [EC'18] which respectively required up to O(W log n/∊p) and O(1/p4/∊) queries.1Interestingly, prior results were all based on similar algorithms and differed only in the analysis. Our algorithms are fundamentally different, yet very simple and natural. For the analysis, we introduce a number of procedures that construct heavy fractional matchings. We consider the new algorithms and our analytical tools to be the main contributions of this paper.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.177"}, {"primary_key": "3118921", "vector": [], "sparse_vector": [], "title": "Correlation-Robust Analysis of Single Item Auction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate the problem of revenue maximization in single-item auction within the new correlation-robust framework proposed by <PERSON> [2017] and further developed by <PERSON><PERSON><PERSON> and <PERSON> [2018]. In this framework the auctioneer is assumed to have only partial information about marginal distributions, but does not know the dependency structure of the joint distribution. The auctioneer's revenue is evaluated in the worst-case over the uncertainty of possible joint distribution.For the problem of optimal auction design in the correlation robust-framework we observe that in most cases the optimal auction does not admit a simple form like the celebrated <PERSON><PERSON>'s auction for independent valuations. We analyze and compare performances of several DSIC mechanisms used in practice. Our main set of results concern the sequential posted-price mechanism (SPM). We show that SPM achieves a constant (4.78) approximation to the optimal correlation-robust mechanism. We also show that in the symmetric (anonymous) case when all bidders have the same marginal distribution, (i) SPM has almost matching worst-correlation revenue as any second price auction with common reserve price, and (ii) when the number of bidders is large, SPM converges to optimum. In addition, we extend some results on approximation and computational tractability for lookahead auctions to the correlation-robust framework.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.13"}, {"primary_key": "3118922", "vector": [], "sparse_vector": [], "title": "Optimal Ball Recycling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Tsai"], "summary": "Balls-and-bins games have been a successful tool for modeling load balancing problems. In this paper, we study a new scenario, which we call the ball-recycling game, defined as follows:Throw m balls into n bins i.i.d. according to a given probability distribution p. Then, at each time step, pick a non-empty bin and recycle its balls: take the balls from the selected bin and re-throw them according to p.This balls-and-bins game closely models memory-access heuristics in databases. The goal is to have a bin-picking method that maximizes the recycling rate, defined to be the expected number of balls recycled per step in the stationary distribution.We study two natural strategies for ball recycling: Fullest Bin, which greedily picks the bin with the maximum number of balls, and Random Ball, which picks a ball at random and recycles its bin. We show that for general p, random Ball is Θ(1)-optimal, whereas Fullest Bin can be pessimal. However, when p = u, the uniform distribution, Fullest Bin is optimal to within an additive constant.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.155"}, {"primary_key": "3118923", "vector": [], "sparse_vector": [], "title": "A time- and space-optimal algorithm for the many-visits TSP.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The many-visits traveling salesperson problem (MV-TSP) asks for an optimal tour of n cities that visits each city c a prescribed number kc of times. Travel costs may be asymmetric, and visiting a city twice in a row may incur a non-zero cost. The MV-TSP problem finds applications in scheduling, geometric approximation, and Hamiltonicity of certain graph families.The fastest known algorithm for MV-TSP is due to <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>P, 1984). It runs in time nO(n) + O(n3 log Σc kc) and requires nO(n) space. The interesting feature of the Cosmadakis-Papadimitriou algorithm is its logarithmic dependence on the total length Σc kc of the tour, allowing the algorithm to handle instances with very long tours, beyond what is tractable in the standard TSP setting. However, its superexponential dependence on the number of cities in both its time and space complexity renders the algorithm impractical for all but the narrowest range of this parameter.In this paper we significantly improve on the Cosmadakis-Papadimitriou algorithm, giving an MV-TSP algorithm that runs in time 2O(n), i.e. single-exponential in the number of cities, with polynomial space. The space requirement of our algorithm is (essentially) the size of the output, and assuming the Exponential-time Hypothesis (ETH), the time requirement is optimal. Our algorithm is deterministic, and arguably both simpler and easier to analyse than the original approach of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>. It involves an optimization over directed spanning trees and a recursive, centroid-based decomposition of trees.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.106"}, {"primary_key": "3118924", "vector": [], "sparse_vector": [], "title": "A Deamortization Approach for Dynamic Spanner and Dynamic Maximal Matching.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many dynamic graph algorithms have an amortized update time, rather than a stronger worst-case guarantee. But amortized data structures are not suitable for real-time systems, where each individual operation has to be executed quickly. For this reason, there exist many recent randomized results that aim to provide a guarantee stronger than amortized expected. The strongest possible guarantee for a randomized algorithm is that it is always correct (Las Vegas), and has high-probability worst-case update time, which gives a bound on the time for each individual operation that holds with high probability. In this paper we present the first polylogarithmic high-probability worst-case time bounds for the dynamic spanner and the dynamic maximal matching problem. 1. For dynamic spanner, the only known $o(n)$ worst-case bounds were $O(n^{3/4})$ high-probability worst-case update time for maintaining a 3-spanner and $O(n^{5/9})$ for maintaining a 5-spanner. We give a $O(1)^k \\log^3(n)$ high-probability worst-case time bound for maintaining a $(2k-1)$-spanner, which yields the first worst-case polylog update time for all constant $k$. (All the results above maintain the optimal tradeoff of stretch $2k-1$ and $\\tilde{O}(n^{1+1/k})$ edges.) 2. For dynamic maximal matching, or dynamic $2$-approximate maximum matching, no algorithm with $o(n)$ worst-case time bound was known and we present an algorithm with $O(\\log^5(n))$ high-probability worst-case time; similar worst-case bounds existed only for maintaining a matching that was $(2+\\epsilon)$-approximate, and hence not maximal. Our results are achieved using a new black-box reduction that converts any data structure with worst-case expected update time into one with a high-probability worst-case update time: the query time remains the same, while the update time increases by a factor of $O(\\log^2(n))$.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.115"}, {"primary_key": "3118925", "vector": [], "sparse_vector": [], "title": "A Deterministic PTAS for the Algebraic Rank of Bounded Degree Polynomials.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a deterministic polynomial time approximation scheme (PTAS) for computing the algebraic rank of a set of bounded degree polynomials. The notion of algebraic rank naturally generalizes the notion of rank in linear algebra, i.e., instead of considering only the linear dependencies, we also consider higher degree algebraic dependencies among the input polynomials.More specifically, we give an algorithm that takes as input a set of polynomials with degrees bounded by d, and a rational number ∊ > 0 and runs in time , where M(n) is the time required to compute the rank of an n × n matrix (with field entries), and finally outputs a number r, such that r is at least (1 – ∊) times the algebraic rank of f.Our key contribution is a new technique which allows us to achieve the higher degree generalization of the results by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> (CCC'17) who gave a deterministic PTAS for computing the rank of a matrix with homogeneous linear entries. It is known that a deterministic algorithm for exactly computing the rank in the linear case is already equivalent to the celebrated Polynomial Identity Testing (PIT) problem which itself would imply circuit complexity lower bounds (<PERSON><PERSON><PERSON>, <PERSON><PERSON>ag<PERSON><PERSON>, ST<PERSON>'03).Such a higher degree generalization is already known to a much stronger extent in the non-commutative world, where the more general case in which the entries of the matrix are given by polysized formulas reduces to the case where the entries are given by linear polynomials using <PERSON><PERSON>'s trick, and in the latter case, one can also compute the exact rank in polynomial time (<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>'16, <PERSON>yos, <PERSON>, <PERSON>rahmanyam, IT<PERSON>'17). <PERSON>gman's trick only preserves the co-rank, hence it cannot be used to reduce the problem of rank approximation to the case when the matrix entries are linear polynomials. Thus our work can also be seen as a step towards bridging the knowledge gap between the non-commutative world and the commutative world.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.41"}, {"primary_key": "3118926", "vector": [], "sparse_vector": [], "title": "Deterministically Maintaining a (2 + ∊)-Approximate Minimum Vertex Cover in O(1/∊2) Amortized Update Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of maintaining an (approximately) minimum vertex cover in an n-node graph G = (V, E) that is getting updated dynamically via a sequence of edge insertions/deletions. We show how to maintain a (2 + ∊)-approximate minimum vertex cover, deterministically, in this setting in O(1/∊2) amortized update time.Prior to our work, the best known deterministic algorithm for maintaining a (2 + ∊)-approximate minimum vertex cover was due to <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> [SODA 2015]. Their algorithm has an update time of O(log n/∊2). Recently, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> [IPCO 2017] and <PERSON>, <PERSON>, <PERSON>, <PERSON> [STOC 2017] showed how to maintain an O(1)-approximation in O(1)-amortized update time for the same problem. Our result gives an exponential improvement over the update time of <PERSON><PERSON><PERSON> et al. [SODA 2015], and nearly matches the performance of the randomized algorithm of Solomon [FOCS 2016] who gets an approximation ratio of 2 and an expected amortized update time of O(1).We derive our result by analyzing, via a novel technique, a variant of the algorithm by <PERSON><PERSON><PERSON> et al. We consider an idealized setting where the update time of an algorithm can take any arbitrary fractional value, and use insights from this setting to come up with an appropriate potential function. Conceptually, this framework mimics the idea of an LP-relaxation for an optimization problem. The difference is that instead of relaxing an integral objective function, we relax the update time of an algorithm itself. We believe that this technique will find further applications in the analysis of dynamic algorithms.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.113"}, {"primary_key": "3118927", "vector": [], "sparse_vector": [], "title": "Approximability of p → q Matrix Norms: Generalized Krivine Rounding and Hypercontractive Hardness.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON>"], "summary": "We study the problem of computing the p → q operator norm of a matrix A in ℝm×n, defined as ‖A‖p→q : = supx∊ℝn\\{0} ‖Ax‖q/‖x‖p. This problem generalizes the spectral norm of a matrix (p = q = 2) and the G<PERSON><PERSON><PERSON>ck problem (p = ∞, q = 1), and has been widely studied in various regimes.When p ≥ q, the problem exhibits a dichotomy: constant factor approximation algorithms are known if 2 is in [q, p], and the problem is hard to approximate within almost polynomial factors when 2 is not in [q,p]. For the case when 2 is in [q, p] we prove almost matching approximation and NP-hardness results.The regime when p 2 was studied by [<PERSON><PERSON> et. al., STOC'12] who gave sub-exponential algorithms for a promise version of the problem (which captures small-set expansion) and also proved hardness of approximation results based on the Exponential Time Hypothesis. However, no NP-hardness of approximation is known for these problems for any p < q.We prove the first NP-hardness result for approximating hypercontractive norms. We show that for any 1 < p < q < ∞ with 2 not in [p, q], ‖A‖p→q is hard to approximate within 2O(log1−∊ n) assuming NP is not contained in BPTIME(2logO(1) n)).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.83"}, {"primary_key": "3118928", "vector": [], "sparse_vector": [], "title": "Improving the smoothed complexity of FLIP for max cut problems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Finding locally optimal solutions for MAX-CUT and MAX-k-CUT are well-known PLS-complete problems. An instinctive approach to finding such a locally optimum solution is the FLIP method. Even though FLIP requires exponential time in worst-case instances, it tends to terminate quickly in practical instances. To explain this discrepancy, the run-time of FLIP has been studied in the smoothed complexity framework. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [ER17] showed that the smoothed complexity of FLIP for MAX-CUT in arbitrary graphs is quasi-polynomial. <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> [ABPW17] showed that the smoothed complexity of FLIP for MAX-CUT in complete graphs is O(ϕ5n15.1), where ϕ is an upper bound on the random edge-weight density and n is the number of vertices in the input graph.While <PERSON> et al.'s result showed the first polynomial smoothed complexity, they also conjectured that their run-time bound is far from optimal. In this work, we make substantial progress towards improving the run-time bound. We prove that the smoothed complexity of FLIP in complete graphs is O(ϕn7.83). Our results are based on a carefully chosen matrix whose rank captures the run-time of the method along with improved rank bounds for this matrix and an improved union bound based on this matrix. In addition, our techniques provide a general framework for analyzing FLIP in the smoothed framework. We illustrate this general framework by showing that the smoothed complexity of FLIP for MAX-3-CUT in complete graphs is polynomial and for MAX-k-CUT in arbitrary graphs is quasi-polynomial. We believe that our techniques should also be of interest towards showing smoothed polynomial complexity of FLIP for MAX-k-CUT in complete graphs for larger constants k.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.55"}, {"primary_key": "3118929", "vector": [], "sparse_vector": [], "title": "The I/O complexity of Toom-Cook integer multiplication.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Nearly matching upper and lower bounds are derived for the I/O complexity of the Toom-Cook-k (or Toom-k) algorithm computing the products of two integers, each represented with n digits in a given base s, in a two-level storage hierarchy with M words of fast memory, with different digits stored in different memory words.An IOAk (n, M) = Ω ([n/M)logk(2k–1) M) lower bound on the I/O complexity is established, by a technique that combines an analysis of the size of the dominators of suitable sub-CDAGs of the Toom-Cook-k CDAG (Computational Directed Acyclic Graph) and the analysis of a function, which we call \"Partial Grigoriev's flow\", which captures the amount of information to be transferred between specific subsets of input and output variables, by any algorithm that solves the integer multiplication problem. The lower bound applies even if the recomputation of partial results is allowed.A careful implementation of the Toom-Cook-fc algorithm, assuming that M = Ω (k3 logs k), is also developed and analyzed, leading to an I/O complexity upper bound that is within a factor O(k2) of the corresponding lower bound, hence asymptotically optimal for fixed k.Both the lower and the upper bounds are actually derived in the more general case where the value of k is allowed to vary with the level of recursion, although the quantitative expressions become more involved. Extensions of the lower bound for a parallel model with P processors are also discussed.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.123"}, {"primary_key": "3118930", "vector": [], "sparse_vector": [], "title": "Towards Instance-Optimal Private Query Release.", "authors": ["Jaroslaw Blasiok", "<PERSON>", "Aleksan<PERSON>", "<PERSON>"], "summary": "We study efficient mechanisms for the query release problem in differential privacy: given a workload of m statistical queries, output approximate answers to the queries while satisfying the constraints of differential privacy. In particular, we are interested in mechanisms that optimally adapt to the given workload. Building on the projection mechanism of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, and using the ideas behind <PERSON>'s chaining inequality, we propose new efficient algorithms for the query release problem, and prove that they achieve optimal sample complexity for the given workload (up to constant factors, in certain parameter regimes) with respect to the class of mechanisms that satisfy concentrated differential privacy. We also give variants of our algorithms that satisfy local differential privacy, and prove that they also achieve optimal sample complexity among all local sequentially interactive private mechanisms.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.152"}, {"primary_key": "3118931", "vector": [], "sparse_vector": [], "title": "On the Structure of Unique Shortest Paths in Graphs.", "authors": ["<PERSON>"], "summary": "This paper develops a structural theory of unique shortest paths in real-weighted graphs. Our main goal is to characterize exactly which sets of node sequences, which we call path systems, can be realized as unique shortest paths in a graph with arbitrary real edge weights. We say that such a path system is strongly metrizable.An easy fact implicit in the literature is that a strongly metrizable path system must be consistent, meaning that no two of its paths may intersect, split apart, and then intersect again. Our main result characterizes strong metrizability via some new forbidden intersection patterns along these lines. In other words, we describe a family of forbidden patterns beyond consistency, and we prove that a path system is strongly metrizable if and only if it is consistent and it avoids all of the patterns in this family. We offer separate (but closely related) characterizations in this way for the settings of directed, undirected, and directed acyclic graphs.Our characterizations are based on a new connection between shortest paths and topology; in particular, our new forbidden patterns are in natural correspondence with two-colored topological 2-manifolds, which we visualize as polyhedra. We believe that this connection may be of independent interest, and we further show that it implies some additional structural corollaries that seem to suggest new and possibly deep-rooted connections between these areas.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.125"}, {"primary_key": "3118932", "vector": [], "sparse_vector": [], "title": "XOR Codes and Sparse Learning Parity with Noise.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A k-LIN instance is a system of m equations over n variables of the form si1 + · · · + sik = 0 or 1 modulo 2 (each involving k variables). We consider two distributions on instances in which the variables are chosen independently and uniformly but the right-hand sides are different. In a noisy planted instance, the right-hand side is obtained by evaluating the system on a random planted solution and adding independent noise with some constant bias to each equation; whereas in a random instance, the right-hand side is uniformly random. <PERSON><PERSON><PERSON><PERSON> (FOCS 2003) conjectured that the two are hard to distinguish when k = 3 and m = O(n).We give a sample-efficient reduction from solving noisy planted k-LIN instances (a sparse-equation version of the Learning Parity with Noise problem) to distinguishing them from random instances. Suppose that m-equation, n-variable instances of the two types are efficiently distinguishable with advantage ε. Then, we show that O(m · (m/ε)2/k)-equation, n-variable noisy planted k-LIN instances are efficiently solvable with probability exp –Õ((m/ε)6/k). Our solver has worse success probability but better sample complexity than <PERSON><PERSON>'s (SICOMP 2013). We extend our techniques to show that this can generalize to (possibly non-linear) k-CSPs.The solver is based on a new approximate local list-decoding algorithm for the k-XOR code at large distances. The k-XOR encoding of a function F: ∑ → {–1, 1} is its k-th tensor power Fk(x1, …, xk) = F(x1) · · · F(xk). Given oracle access to a function G that µ-correlates with Fk, our algorithm, say for constant k, outputs the description of a message that Ω(µ1/k)-correlates with F with probability exp(–Õ(µ−4/k)). Previous decoders, for such k, have a worse dependence on µ (Levin, Combinatorica 1987) or do not apply to subconstant µ1/k. We also prove a new XOR lemma for this parameter regime.The decoder and its analysis rely on a new structure-versus-randomness dichotomy for general Boolean-valued functions over product sets, which may be of independent interest.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.61"}, {"primary_key": "3118933", "vector": [], "sparse_vector": [], "title": "Greedy spanners are optimal in doubling metrics.", "authors": ["Glencora Borradaile", "<PERSON>", "<PERSON><PERSON>"], "summary": "We show that the greedy spanner algorithm constructs a (1 + ∊)-spanner of weight ∊−O(d)w(MST) for a point set in metrics of doubling dimension d, resolving an open problem posed by <PERSON><PERSON><PERSON><PERSON> [10]. Our result generalizes the result by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [13] who showed that a point set in d-dimension Euclidean space has a (1 + ∊)-spanner of weight at most ∊−O(d)w(MST). Our proof only uses the packing property of doubling metrics and greatly simplifies the proof of the same result in Euclidean space.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.145"}, {"primary_key": "3118934", "vector": [], "sparse_vector": [], "title": "On the Spanning and Routing <PERSON><PERSON> of Theta-Four.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a routing algorithm for the Θ4-graph that computes a path between any two vertices s and t having length at most 17 times the Euclidean distance between s and t. To compute this path, at each step, the algorithm only uses knowledge of the location of the current vertex, its (at most four) outgoing edges, the destination vertex, and one additional bit of information in order to determine the next edge to follow. This provides the first known online, local, competitive routing algorithm with constant routing ratio for the Θ4-graph, as well as improving the best known upper bound on the spanning ratio of these graphs from 237 to 17. We also show that without this additional bit of information, the routing ratio increases to ≈ 17.03.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.144"}, {"primary_key": "3118935", "vector": [], "sparse_vector": [], "title": "An Algorithmic Blend of LPs and Ring Equations for Promise CSPs.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami"], "summary": "Promise CSPs are a relaxation of constraint satisfaction problems where the goal is to find an assignment satisfying a relaxed version of the constraints. Several well known problems can be cast as promise CSPs including approximate graph and hypergraph coloring, discrepancy minimization, and interesting variants of satisfiability. Similar to CSPs, the tractability of promise CSPs can be tied to the structure of associated operations on the solution space called (weak) polymorphisms. However, compared to CSPs whose polymorphisms are well-structured algebraic objects called clones, polymorphisms in the promise world are much less constrained — essentially any infinite family of functions obeying mild conditions can arise as polymorphisms. Under the thesis that non-trivial polymorphisms govern tractability, promise CSPs therefore provide a fertile ground for the discovery of novel algorithms.In previous work, we classified all tractable cases of Boolean promise CSPs when the constraint predicates are symmetric. The algorithms were governed by three kinds of polymorphism families: (i) parity functions, (ii) majority functions, or (iii) a non-symmetric (albeit block-symmetric) family we called alternating threshold. In this work, we provide a vast generalization of these algorithmic results. Specifically, we show that promise CSPs that admit a family of \"regional-periodic\" polymorphisms are solvable in polynomial time, assuming that determining which region a point is in can be computed in polynomial time. Such polymorphisms are quite general and are obtained by gluing together several functions that are periodic in the Hamming weights in different blocks of the input. For example, we can have functions that equal parity for relative Hamming weights up to 1/2, and Majority (so identically 1) for weights above 1/2.Our algorithm is based on a novel combination of linear programming and solving linear systems over rings. We also abstract a framework based on reducing a promise CSP to a CSP over an infinite domain, solving it there (via the said combination of LPs and ring equations), and then rounding the solution to an assignment for the promise CSP instance. The rounding step is intimately tied to the family of polymorphisms, and clarifies the connection between polymorphisms and algorithms in this context. As a key ingredient, we introduce the technique of finding a solution to a linear program with integer coefficients that lies in a different ring (such as ℤ) to bypass ad-hoc adjustments for lying on a rounding boundary.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.28"}, {"primary_key": "3118936", "vector": [], "sparse_vector": [], "title": "Fréchet Distance Under Translation: Conditional Hardness and an Algorithm via Offline Dynamic Grid Reachability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The discrete Fréchet distance is a popular measure for comparing polygonal curves. An important variant is the discrete Fréchet distance under translation, which enables detection of similar movement patterns in different spatial domains. For polygonal curves of length n in the plane, the fastest known algorithm runs in time Õ(n5) [<PERSON>, <PERSON>, <PERSON> ′15]. This is achieved by constructing an arrangement of disks of size O(n4), and then traversing its faces while updating reachability in a directed grid graph of size N = O(n2), which can be done in time per update [<PERSON><PERSON>, <PERSON>kowski ′07]. The contribution of this paper is two-fold.First, although it is an open problem to solve dynamic reachability in directed grid graphs faster than , we improve this part of the algorithm: We observe that an offline variant of dynamic s-t-reachability in directed grid graphs suffices, and we solve this variant in amortized time Õ(N1/3) per update, resulting in an improved running time of Õ(n4.66 …) for the discrete Fréchet distance under translation. Second, we provide evidence that constructing the arrangement of size O(n4) is necessary in the worst case, by proving a conditional lower bound of n4–o(1) on the running time for the discrete Fréchet distance under translation, assuming the Strong Exponential Time Hypothesis.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.180"}, {"primary_key": "3118937", "vector": [], "sparse_vector": [], "title": "Few Matches or Almost Periodicity: <PERSON><PERSON> Matching with Mismatches in Compressed Texts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A fundamental problem on strings in the realm of approximate string matching is pattern matching with mismatches: Given a text t, a pattern p, and a number k, determine whether some substring of t has Hamming distance at most k to p; such a substring is called a k-match.As real-world texts often come in compressed form, we study the case of searching for a small pattern p in a text t that is compressed by a straight-line program. This grammar compression is popular in the string community, since it is mathematically elegant and unifies many practically relevant compression schemes such as the Lempel-Ziv family, dictionary methods, and others. We denote by m the length of p and by n the compressed size of t. While exact pattern matching, that is, the case k = 0, is known to be solvable in near-linear time Õ(n + m) [Jeż TALG'15], despite considerable interest in the string community, the fastest known algorithm for pattern matching with mismatches runs in time [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ISAAC'13], which is far from linear even for very small k.In this paper, we obtain an algorithm for pattern matching with mismatches running in time Õ((n + m) poly(k)). This is near-linear in the input size for any constant (or slightly superconstant) k. We obtain analogous running time for counting and enumerating all k-matches.Our algorithm is based on a new structural insight for approximate pattern matching, essentially showing that either the number of k-matches is very small or both text and pattern must be almost periodic. While intuitive and simple for exact matches, such a characterization is surprising when allowing k mismatches.MSC codesstringologystring algorithmsapprox. pattern matchinggrammar compression", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.69"}, {"primary_key": "3118938", "vector": [], "sparse_vector": [], "title": "Metrical task systems on trees via mirror descent and unfair gluing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider metrical task systems on tree metrics, and present an O(depth×log n)-competitive randomized algorithm based on the mirror descent framework introduced in our prior work on the k-server problem. For the special case of hierarchically separated trees (HSTs), we use mirror descent to refine the standard approach based on gluing unfair metrical task systems. This yields an O(log n)-competitive algorithm for HSTs, thus removing an extraneous log log n in the bound of <PERSON> and <PERSON> (2003). Combined with well-known HST embedding theorems, this also gives an O((log n)2)-competitive randomized algorithm for every n-point metric space.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.6"}, {"primary_key": "3118939", "vector": [], "sparse_vector": [], "title": "Deterministic (½ + ε)-Approximation for Submodular Maximization over a Matroid.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of maximizing a monotone submodular function subject to a matroid constraint and present a deterministic algorithm that achieves (½ + ε)-approximation for the problem. This algorithm is the first deterministic algorithm known to improve over the ½-approximation ratio of the classical greedy algorithm proved by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> in 1978.MSC codesSubmodular optimizationmatroiddeterministic algorithms", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.16"}, {"primary_key": "3118940", "vector": [], "sparse_vector": [], "title": "k-Servers with a Smile: Online Algorithms via Projections.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> (<PERSON><PERSON><PERSON>) <PERSON><PERSON>"], "summary": "We consider the k-server problem on trees and HSTs. We give an algorithm based on Bregman projections. This algorithm has a competitive ratios that match some of the recent results given by <PERSON><PERSON><PERSON> et al. (STOC 2018), whose algorithm was based on mirror-descent-based continuous dynamics prescribed via a differential inclusion.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.7"}, {"primary_key": "3118941", "vector": [], "sparse_vector": [], "title": "Approximating (k, ℓ)-center clustering for curves.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Euclidean k-Center problem is a classical problem that has been extensively studied in computer science. Given a set G of n points in Euclidean space, the problem is to determine a set C of k centers (not necessarily part of G) such that the maximum distance between a point in G and its nearest neighbor in C is minimized. In this paper we study the corresponding (k, ℓ)-CENTER problem for polygonal curves under the Fréchet distance, that is, given a set G of n polygonal curves in ℝd, each of complexity m, determine a set C of k polygonal curves in ℝd, each of complexity ℓ, such that the maximum Fréchet distance of a curve in G to its closest curve in C is minimized. In their 2016 paper, <PERSON><PERSON><PERSON>, Krivoš<PERSON>, and <PERSON><PERSON> give a near-linear time (1 + ε-approximation algorithm for one-dimensional curves, assuming that k and ℓ are constants. In this paper, we substantially extend and improve the known approximation bounds for curves in dimension 2 and higher. Our analysis thus extends to application-relevant input data such as GPS-trajectories and protein backbones. We show that, if ℓ is part of the input, then there is no polynomial-time approximation scheme unless P = NP. Our constructions yield different bounds for one and two-dimensional curves and the discrete and continuous Fréchet distance. In the case of the discrete Fréchet distance on two-dimensional curves, we show hardness of approximation within a factor close to 2.598. This result also holds when k = 1, and the NP-hardness extends to the case that ℓ = ∞, i.e., for the problem of computing the minimum-enclosing ball under the Fréchet distance. Finally, we observe that a careful adaptation of <PERSON>' algorithm in combination with a curve simplification yields a 3-approximation in any dimension, provided that an optimal simplification can be computed exactly. We conclude that our approximation bounds are close to being tight.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.181"}, {"primary_key": "3118942", "vector": [], "sparse_vector": [], "title": "SETH Says: Weak <PERSON> Distance is Faster, but only if it is Continuous and in One Dimension.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We show by reduction from the Orthogonal Vectors problem that algorithms with strongly subquadratic running time cannot approximate the Fréchet distance between curves better than a factor 3 unless SETH fails. We show that similar reductions cannot achieve a lower bound with a factor better than 3. Our lower bound holds for the continuous, the discrete, and the weak discrete Fréchet distance even for curves in one dimension. Interestingly, the continuous weak Fréchet distance behaves differently. Our lower bound still holds for curves in two dimensions and higher. However, for curves in one dimension, we provide an exact algorithm to compute the weak Fréchet distance in linear time.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.179"}, {"primary_key": "3118943", "vector": [], "sparse_vector": [], "title": "Quantum algorithms and approximating polynomials for composed functions with shared inputs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give new quantum algorithms for evaluating composed functions whose inputs may be shared between bottom-level gates. Let f be a Boolean function and consider a function F obtained by applying f to conjunctions of possibly overlapping subsets of n variables. If f has quantum query complexity Q(f), we give an algorithm for evaluating F using quantum queries. This improves on the bound of that follows by treating each conjunction independently, and is tight for worst-case choices of f. Using completely different techniques, we prove a similar tight composition theorem for the approximate degree of f.By recursively applying our composition theorems, we obtain a nearly optimal Õ(n1–2–d) upper bound on the quantum query complexity and approximate degree of linear-size depth-d AC0 circuits. As a consequence, such circuits can be PAC learned in subexponential time, even in the challenging agnostic setting. Prior to our work, a subexponential-time algorithm was not known even for linear-size depth-3 AC0 circuits. We also show that any substantially faster learning algorithm will require fundamentally new techniques.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.42"}, {"primary_key": "3118944", "vector": [], "sparse_vector": [], "title": "Strategies for Stable Merge Sorting.", "authors": ["Sam Buss", "<PERSON>"], "summary": "We introduce new stable natural merge sort algorithms, called 2-merge sort and α-merge sort. We prove upper and lower bounds for several merge sort algorithms, including <PERSON><PERSON><PERSON>, <PERSON><PERSON>'s sort, α-stack sorts, and our new 2-merge and α-merge sorts. The upper and lower bounds have the forms c · n log m and c · n log n for inputs of length n comprising m runs. For <PERSON><PERSON><PERSON>, we prove a lower bound of (1.5 – o(1))n log n. For 2-merge sort, we prove optimal upper and lower bounds of approximately (1.089 ± o(1))n log m. We state similar asymptotically matching upper and lower bounds for α-merge sort, when ϕ < α < 2, where ϕ is the golden ratio.Our bounds are in terms of merge cost; this upper bounds the number of comparisons and accurately models runtime. The merge strategies can be used for any stable merge sort, not just natural merge sorts. The new 2-merge and α-merge sorts have better worst-case merge cost upper bounds and are slightly simpler to implement than the widely-used Timsort; they also perform better in experiments.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.78"}, {"primary_key": "3118945", "vector": [], "sparse_vector": [], "title": "Minimizing Interference Potential Among Moving Entities.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of monitoring the interference among a collection of entities moving with bounded speed in d-dimensional Euclidean space. Uncertainty in entity locations due to unmonitored and unpredictable motion gives rise to a space of possible entity configurations at each moment in time, with possibly very different interference properties. We define different measures of what we call the interference potential of such spaces to describe the interference that might actually occur.We study the extent to which restricted monitoring frequency impacts interference potential, through the analysis of a clairvoyant scheme (one that knows the trajectories of all entities) subject to the same monitoring frequency restriction. This forms a benchmark for the analysis of uninformed schemes. In this framework, we describe and analyse an adaptive monitoring scheme for minimizing interference potential over time that is competitive (to within a constant factor) with any other scheme (in particular, a clairvoyant scheme) over modest sized time intervals.As a natural application, imagine that the entities are transmission sources, with associated broadcast ranges, moving in three dimensions. Two such entities, transmitting on the same channel, interfere if their broadcast ranges intersect. Uncertainty in the location of a transmission source effectively expands its broadcast range to a potential broadcast range. The chromatic number of the intersection graph of these potential broadcast ranges, one of our interference potential measures, gives the minimum number of broadcast channels required to avoid interference. Our scheme provides the foundation of an adaptive, locally updated, channel assignment algorithm that is competitive over time, in terms of the number of broadcast channels used with a fixed monitoring frequency, with any other such scheme.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.147"}, {"primary_key": "3118946", "vector": [], "sparse_vector": [], "title": "Perfect Matchings, Rank of Connection Tensors and Graph Homomorphisms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We develop a theory of graph algebras over general fields. This is modeled after the theory developed by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> in [22] for connection matrices, in the study of graph homomorphism functions over real edge weight and positive vertex weight. We introduce connection tensors for graph properties. This notion naturally generalizes the concept of connection matrices. It is shown that counting perfect matchings, and a host of other graph properties naturally defined as Holant problems (edge models), cannot be expressed by graph homomorphism functions over the complex numbers (or even more general fields). Our necessary and sufficient condition in terms of connection tensors is a simple exponential rank bound. It shows that positive semidefiniteness is not needed in the more general setting.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.30"}, {"primary_key": "3118947", "vector": [], "sparse_vector": [], "title": "Approximability of the Six-vertex Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We take the first step toward a classification of the approximation complexity of the six-vertex model. This is a subject of extensive research in statistical physics. Our result concerns the approximability of the partition function on 4-regular graphs, classified according to the parameters of the model. Our complexity results conform to the phase transition phenomenon from physics. We show that the approximation complexity of the six-vertex model behaves dramatically differently on the two sides separated by the phase transition threshold. Furthermore, we present structural properties of the six-vertex model on planar graphs for parameter settings that have known relations to the Tutte polynomial T(G; x, y).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.136"}, {"primary_key": "3118948", "vector": [], "sparse_vector": [], "title": "I/O-Efficient Algorithms for Topological Sort and Related Problems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents I/O-efficient algorithms for topologically sorting a directed acyclic graph and for the more general problem identifying and topologically sorting the strongly connected components of a directed graph G = (V, E). Both algorithms are randomized and have I/O-costs O(sort(E) · poly(log V)), with high probability, where sort(E) = O( logM/B(E/B)) is the I/O cost of sorting an |E|-element array on a machine with size-B blocks and size-M cache/internal memory. These are the first algorithms for these problems that do not incur at least one I/O per vertex, and as such these are the first I/O-efficient algorithms for sparse graphs. By applying the technique of time-forward processing, these algorithms also imply I/O-efficient algorithms for most problems on directed acyclic graphs, such as shortest paths, as well as the single-source reachability problem on arbitrary directed graphs.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.124"}, {"primary_key": "3118949", "vector": [], "sparse_vector": [], "title": "Optimal Lower Bounds for Sketching Graph Cuts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the space complexity of sketching cuts and Laplacian quadratic forms of graphs. We show that any data structure which approximately stores the sizes of all cuts in an undirected graph on n vertices up to a 1 + ∊ error must use Ω(n log n/∊2) bits of space in the worst case, improving the Ω(n/∊2) bound of [ACK+16] and matching the best known upper bound achieved by spectral sparsifiers [BSS12]. Our proof is based on a rigidity phenomenon for cut (and spectral) approximation which may be of independent interest: any two d–regular graphs which approximate each other's cuts significantly better than a random graph approximates the complete graph must overlap in a constant fraction of their edges.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.158"}, {"primary_key": "3118950", "vector": [], "sparse_vector": [], "title": "Foundations of Differentially Oblivious Algorithms.", "authors": ["T.<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is well-known that a program's memory access pattern can leak information about its input. To thwart such leakage, most existing works adopt the technique of oblivious RAM (ORAM) simulation. Such an obliviousness notion has stimulated much debate. Although ORAM techniques have significantly improved over the past few years, the concrete overheads are arguably still undesirable for real-world systems — part of this overhead is in fact inherent due to a well-known logarithmic ORAM lower bound by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. To make matters worse, when the program's runtime or output length depend on secret inputs, it may be necessary to perform worst-case padding to achieve full obliviousness and thus incur possibly super-linear overheads.Inspired by the elegant notion of differential privacy, we initiate the study of a new notion of access pattern privacy, which we call \"(∊, δ)-differential obliviousness\". We separate the notion of (∊, δ)-differential obliviousness from classical obliviousness by considering several fundamental algorithmic abstractions including sorting small-length keys, merging two sorted lists, and range query data structures (akin to binary search trees). We show that by adopting differential obliviousness with reasonable choices of ∊ and δ, not only can one circumvent several impossibilities pertaining to full obliviousness, one can also, in several cases, obtain meaningful privacy with little overhead relative to the non-private baselines (i.e., having privacy \"almost for free\"). On the other hand, we show that for very demanding choices of ∊ and δ, the same lower bounds for oblivious algorithms would be preserved for (∊, δ)-differential obliviousness.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.150"}, {"primary_key": "3118952", "vector": [], "sparse_vector": [], "title": "Distributed Triangle Detection via Expander Decomposition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present improved distributed algorithms for triangle detection and its variants in the CONGEST model. We show that Triangle Detection, Counting, and Enumeration can be solved in Õ(n1/2) rounds. In contrast, the previous state-of-the-art bounds for Triangle Detection and Enumeration were Õ(n2/3) and Õ(n3/4), respectively, due to <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (PODC 2017).The main technical novelty in this work is a distributed graph partitioning algorithm. We show that in Õ(n1–δ) rounds we can partition the edge set of the network G = (V, E) into three parts E = Em ∪ Es ∪ Er such that•Each connected component induced by <PERSON> has minimum degree Ω(nδ) and conductance Ω(1/polylog(n)). As a consequence the mixing time of a random walk within the component is O(polylog(n)).•The subgraph induced by <PERSON><PERSON> has arboricity at most nδ.•|Er| ≤ |E|/6.All of our algorithms are based on the following generic framework, which we believe is of interest beyond this work. Roughly, we deal with the set Es by an algorithm that is efficient for low-arboricity graphs, and deal with the set Er using recursive calls. For each connected component induced by Em, we are able to simulate CONGESTED-CLIQUE algorithms with small overhead by applying a routing algorithm due to <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (PODC 2017) for high conductance graphs.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.51"}, {"primary_key": "3118953", "vector": [], "sparse_vector": [], "title": "Exact Distance Oracles for Planar Graphs with Failing Vertices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "NEXT ARTICLEAnalyzing Boolean functions on the biased hypercube via higher-dimensional agreement tests: [Extended abstract]", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.127"}, {"primary_key": "3118954", "vector": [], "sparse_vector": [], "title": "Hierarchical Clustering better than Average-Linkage.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Hierarchical Clustering (HC) is a widely studied problem in exploratory data analysis, usually tackled by simple agglomerative procedures like average-linkage, single-linkage or complete-linkage. In this paper we focus on two objectives, introduced recently to give insight into the performance of average-linkage clustering: a similarity based HC objective proposed by [21] and a dissimilarity based HC objective proposed by [9]. In both cases, we present tight counterexamples showing that average-linkage cannot obtain better than ⅓ and ⅔ approximations respectively (in the worst-case), settling an open question raised in [21]. This matches the approximation ratio of a random solution, raising a natural question: can we beat average-linkage for these objectives? We answer this in the affirmative, giving two new algorithms based on semidefinite programming with provably better guarantees.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.139"}, {"primary_key": "3118955", "vector": [], "sparse_vector": [], "title": "Near Optimal Algorithms For The Single Source Replacement Paths Problem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Single Source Replacement Paths (SSRP) problem is as follows; Given a graph G = (V, E), a source vertex s and a shortest paths tree Ts rooted in s, output for every vertex t ∊ V and for every edge e in Ts the length of the shortest path from s to t avoiding e.We present near optimal upper bounds, by providing time randomized combinatorial algorithm 1 for unweighted undirected graphs, and matching conditional lower bounds for the SSRP problem.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.126"}, {"primary_key": "3118956", "vector": [], "sparse_vector": [], "title": "Optimal Distributed Coloring Algorithms for Planar Graphs in the LOCAL model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we consider distributed coloring for planar graphs with a small number of colors. Our main result is an optimal (up to a constant factor) O(log n) time algorithm for 6-coloring planar graphs. Our algorithm is based on a novel technique that in a nutshell detects small structures that can be easily colored given a proper coloring of the rest of the vertices and removes them from the graph until the graph contains a small enough number of edges. We believe this technique might be of independent interest.In addition, we present a lower bound for 4-coloring planar graphs that essentially shows that any algorithm (deterministic or randomized) for 4-coloring planar graphs requires Ω(n) rounds. We therefore completely resolve the problems of 4-coloring and 6-coloring for planar graphs in the LOCAL model.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.49"}, {"primary_key": "3118957", "vector": [], "sparse_vector": [], "title": "Binary Robust Positioning Patterns with Low Redundancy and Efficient Locating Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "San Ling", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A robust positioning pattern is a large array that allows a mobile device to locate its position by reading a possibly corrupted small window around it. This paper provides constructions of binary positioning patterns, equipped with efficient locating algorithms, that are robust to a constant number of errors and have redundancy within a constant factor of optimality.Our construction of binary robust positioning sequences has the least known redundancy amongst those explicit constructions with efficient locating algorithms. On the other hand, for binary robust positioning arrays, our construction is the first explicit construction whose redundancy is within a constant factor of optimality. The locating algorithms accompanying our constructions run in time cubic in sequence length or array dimensions.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.131"}, {"primary_key": "3118959", "vector": [], "sparse_vector": [], "title": "Submodular Function Maximization in Parallel via the Multilinear Relaxation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON> and <PERSON> [4] recently initiated the study of adaptivity (or parallelism) for constrained submodular function maximization, and studied the setting of a cardinality constraint. Subsequent improvements for this problem by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> [6] and <PERSON><PERSON> and <PERSON><PERSON><PERSON> [21] resulted in a near-optimal (1 – 1/e – ∊)-approximation in O(log n/∊2) rounds of adaptivity. Partly motivated by the goal of extending these results to more general constraints, we describe parallel algorithms for approximately maximizing the multilinear relaxation of a monotone submodular function subject to packing constraints. Formally our problem is to maximize F(x) over x ∊ [0, 1]n subject to where F is the multilinear relaxation of a monotone submodular function. Our algorithm achieves a near-optimal (1 – 1/e – ∊)-approximation in O(log2 m log n/∊4) rounds where n is the cardinality of the ground set and m is the number of packing constraints. For many constraints of interest, the resulting fractional solution can be rounded via known randomized rounding schemes that are oblivious to the specific submodular function. We thus derive randomized algorithms with poly-logarithmic adaptivity for a number of constraints including partition and laminar matroids, matchings, knapsack constraints, and their intersections.Our algorithm takes a continuous view point and combines several ideas ranging from the continuous greedy algorithm of [38, 13], its adaptation to the MWU framework for packing constraints [20], and parallel algorithms for packing LPs [31, 41]. For the basic setting of cardinality constraints, this viewpoint gives rise to an alternative, simple to understand algorithm that matches recent results [6, 21]. Our algorithm to solve the multilinear relaxation is deterministic if it is given access to a value oracle for the multilinear extension and its gradient; this is possible in some interesting cases such as the coverage function of an explicitly given set system.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.20"}, {"primary_key": "3118960", "vector": [], "sparse_vector": [], "title": "On Approximating (Sparse) Covering Integer Programs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider approximation algorithms for covering integer programs of the form min 〈c, x〉 over x ∊ ℤ≥0n s.t. Ax ≥ b and x ≤ d; where A ∊ ℝ≥0m×n, b ∊ ℝ≥0m, and c, d ∊ ℝ≥0n all have nonnegative entries. We refer to this problem as CIP, and the special case without the multiplicity constraints x < d as CIP∞. These problems generalize the well-studied Set Cover problem. We make two algorithmic contributions.First, we show that a simple algorithm based on randomized rounding with alteration improves or matches the best known approximation algorithms for CIP and CIP∞ in a wide range of parameter settings, and these bounds are essentially optimal. As a byproduct of the simplicity of the alteration algorithm and analysis, we can derandomize the algorithm without any loss in the approximation guarantee or efficiency. Previous work by <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON> [13] which obtained near-tight bounds is based on a resampling-based randomized algorithm whose analysis is complex.Non-trivial approximation algorithms for CIP are based on solving the natural LP relaxation strengthened with knapsack cover (KC) inequalities [5, 26, 13]. Our second contribution is a fast (essentially near-linear time) approximation scheme for solving the strengthened LP with a factor of n speed up over the previous best running time [5]. To achieve this fast algorithm we combine recent work on accelerating the multiplicative weight update framework with a partially dynamic approach to the knapsack covering problem.Together, our contributions lead to near-optimal (deterministic) approximation bounds with near-linear running times for CIP and CIP∞.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.97"}, {"primary_key": "3118961", "vector": [], "sparse_vector": [], "title": "Derandomized Balanced Allocation.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper, we study the maximum loads of explicit hash families in the d-choice schemes when allocating sequentially n balls into n bins. We consider the Uniform-Greedy scheme [ABKU99], which provides d independent bins for each ball and places the ball into the bin with the least load, and its non-uniform variant — the Always-Go-Left scheme introduced by <PERSON><PERSON><PERSON> [Vöc03]. We construct a hash family with O(log n log log n) random bits based on the previous work of <PERSON><PERSON> et al. [CRSW13] and show the following results.1.With high probability, this hash family has a maximum load of in the Uniform-Greedy scheme.2.With high probability, it has a maximum load of in the Always-Go-Left scheme for a constant ϕd > 1.61.The maximum loads of our hash family match the maximum loads of a perfectly random hash function [ABKU99, Vöc03] in the Uniform-Greedy and Always-Go-Left scheme separately, up to the low order term of constants. Previously, the best known hash families matching the same maximum loads of a perfectly random hash function in d-choice schemes were O(log n)-wise independent functions [Vöc03], which needs Θ(log2 n) random bits.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.154"}, {"primary_key": "3118962", "vector": [], "sparse_vector": [], "title": "Improved Bounds for Randomly Sampling Colorings via Linear Programming.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Guillem Perarnau", "<PERSON>"], "summary": "A well-known conjecture in computer science and statistical physics is that Glauber dynamics on the set of k-colorings of a graph G on n vertices with maximum degree Δ is rapidly mixing for k ≥ Δ + 2. In FOCS 1999, <PERSON><PERSON><PERSON> [43] showed that the flip dynamics (and therefore also Glauber dynamics) is rapidly mixing for any . It turns out that there is a natural barrier at , below which there is no one-step coupling that is contractive with respect to the Hamming metric, even for the flip dynamics. We use linear programming and duality arguments to fully characterize the obstructions to going beyond . These extremal configurations turn out to be quite brittle, and in this paper we use this to give two proofs that the Glauber dynamics is rapidly mixing for any for some absolute constant ε0 > 0. This is the first improvement to <PERSON><PERSON><PERSON>'s result that holds for general graphs. Our first approach analyzes a variable-length coupling in which these configurations break apart with high probability before the coupling terminates, and our other approach analyzes a one-step path coupling with a new metric that counts the extremal configurations. Additionally, our results extend to list coloring, a widely studied generalization of coloring, where the previously best known results required k > 2Δ.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.134"}, {"primary_key": "3118963", "vector": [], "sparse_vector": [], "title": "Fine-grained Complexity Meets IP = PSPACE.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kaifeng Lyu", "<PERSON>", "<PERSON>via<PERSON>"], "summary": "In this paper we study the fine-grained complexity of finding exact and approximate solutions to problems in P. Our main contribution is showing reductions from an exact to an approximate solution for a host of such problems.As one (notable) example, we show that the Closest-LCS-Pair problem (Given two sets of strings A and B, compute exactly the maximum LCS(a, b) with (a, b) ∊ A × B) is equivalent to its approximation version (under near-linear time reductions, and with a constant approximation factor). More generally, we identify a class of problems, which we call BP-Pair-Class, comprising both exact and approximate solutions, and show that they are all equivalent under near-linear time reductions.Exploring this class and its properties, we also show:•Under the NC-SETH assumption (a significantly more relaxed assumption than SETH), solving any of the problems in this class requires essentially quadratic time.•Modest improvements on the running time of known algorithms (shaving log factors) would imply that NEXP is not in non-uniform NC1.•Finally, we leverage our techniques to show new barriers for deterministic approximation algorithms for LCS.A very important consequence of our results is that they continue to hold in the data structure setting. In particular, it shows that a data structure for approximate Nearest Neighbor Search for LCS (NNSLCS) implies a data structure for exact NNSLCS and a data structure for answering regular expression queries with essentially the same complexity.At the heart of these new results is a deep connection between interactive proof systems for bounded-space computations and the fine-grained complexity of exact and approximate solutions to problems in P. In particular, our results build on the proof techniques from the classical IP = PSPACE result.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.1"}, {"primary_key": "3118964", "vector": [], "sparse_vector": [], "title": "An Equivalence Class for Orthogonal Vectors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Orthogonal Vectors problem (OV) asks: given n vectors in {0, 1}O(log n), are two of them orthogonal? OV is easily solved in O(n2 log n) time, and it is a central problem in fine-grained complexity: dozens of conditional lower bounds are based on the popular hypothesis that OV cannot be solved in (say) n1.99 time. However, unlike the APSP problem, few other problems are known to be non-trivially equivalent to OV.We show OV is truly-subquadratic equivalent to several fundamental problems, all of which (a priori) look harder than OV. A partial list is given below:1.(Min-IP/Max-IP) Find a red-blue pair of vectors with minimum (respectively, maximum) inner product, among n vectors in {0, 1}O(log n).2.(Exact-IP) Find a red-blue pair of vectors with inner product equal to a given target integer, among n vectors in {0, 1}O(log n).3.(Apx-Min-IP/Apx-Max-IP) Find a red-blue pair of vectors that is a 100-approximation to the minimum (resp. maximum) inner product, among n vectors in {0, l}O(log n).4.(Approximate Bichrom.-ℓp-Closest-Pair) Compute a (1+ Ω(1))-approximation to the ℓp-closest red-blue pair (for a constant p ∊ [1, 2]), among n points in ℝ, d ≤ no(1).5.(Approximate ℓp-Furthest-Pair) Compute a (1 + Ω(1))-approximation to the ℓp-furthest pair (for a constant p ∊ [1, 2]), among n points in ℝ, d ≤ no(1).Therefore, quick constant-factor approximations to maximum inner product imply quick exact solutions to maximum inner product, in the O(log n)-dimensional setting. Another consequence is that the ability to find vectors with zero inner product suffices for finding vectors with maximum inner product.Our equivalence results are robust enough that they continue to hold in the data structure setting. In particular, we show that there is a poly(n) space, n1–ε query time data structure for Partial Match with vectors from {0, 1}O(log n) if and only if such a data structure exists for 1 + Ω(1) Approximate Nearest Neighbor Search in Euclidean space.To establish the equivalences, we introduce two general frameworks for reductions to OV: one based on ∑2 communication protocols, and another based on locality-sensitive hashing families.In addition, we obtain an n2–1/O(log c) time algorithm for Apx-Min-IP with n vectors from {0, 1}c log n, matching state-of-the-art algorithms for OV and Apx-Max-IP. As an application, we obtain a faster algorithm for approximating \"almost solvable\" MAX-SAT instances.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.2"}, {"primary_key": "3118965", "vector": [], "sparse_vector": [], "title": "Synchronization Strings: Highly Efficient Deterministic Constructions over Small Alphabets.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Synchronization strings are recently introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [1] in the study of codes for correcting insertion and deletion errors (insdel codes). A synchronization string is an encoding of the indices of the symbols in a string, and together with an appropriate decoding algorithm it can transform insertion and deletion errors into standard symbol erasures and corruptions. This reduces the problem of constructing insdel codes to the problem of constructing standard error correcting codes, which is much better understood. Besides this, synchronization strings are also useful in other applications such as synchronization sequences and interactive coding schemes. For all such applications, synchronization strings are desired to be over alphabets that are as small as possible, since a larger alphabet size corresponds to more redundant information added.<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [1] showed that for any parameter ε > 0, synchronization strings of arbitrary length exist over an alphabet whose size depends only on ε. Specifically, [1] obtained an alphabet size of O(ε−4), which left an open question on where the minimal size of such alphabets lies between Ω(ε−1) and O(ε−4). In this work, we partially bridge this gap by providing an improved lower bound of Ω (ε−3/2), and an improved upper bound of O (ε−2). We also provide fast explicit constructions of synchronization strings over small alphabets.Further, along the lines of previous work on similar combinatorial objects, we study the extremal question of the smallest possible alphabet size over which synchronization strings can exist for some constant ε < 1. We show that one can construct ε-synchronization strings over alphabets of size four while no such string exists over binary alphabets. This reduces the extremal question to whether synchronization strings exist over ternary alphabets.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.132"}, {"primary_key": "3118966", "vector": [], "sparse_vector": [], "title": "Improved Topological Approximations by Digitization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Čech complexes are useful simplicial complexes for computing and analyzing topological features of data that lies in Euclidean space. Unfortunately, computing these complexes becomes prohibitively expensive for large-sized data sets even for medium-to-low dimensional data. We present an approximation scheme for (1 + ε)-approximating the topological information of the Čech complexes for n points in ℝd, for ε ∊ (0, 1]. Our approximation has a total size of for constant dimension d, improving all the currently available (1 + ε)-approximation schemes of simplicial filtrations in Euclidean space. Perhaps counter-intuitively, we arrive at our result by adding additional sample points to the input. We achieve a bound that is independent of the spread of the point set by pre-identifying the scales at which the Čech complexes changes and sampling accordingly.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.166"}, {"primary_key": "3118967", "vector": [], "sparse_vector": [], "title": "Towards Tight(er) Bounds for the Excluded Grid Theorem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the Excluded Grid Theorem, a fundamental structural result in graph theory, that was proved by <PERSON> and <PERSON> in their seminal work on graph minors. The theorem states that there is a function f : ℤ+ → ℤ+, such that for every integer g > 0, every graph of treewidth at least f(g) contains the (g × g)-grid as a minor. For every integer g > 0, let f(g) be the smallest value for which the theorem holds. Establishing tight bounds on f(g) is an important graph-theoretic question. <PERSON> and <PERSON> showed that f(g) = Ω(g2 log g) must hold. For a long time, the best known upper bounds on f(g) were super-exponential in g. The first polynomial upper bound of f(g) = O(g98 poly log g) was proved by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. It was later improved to f(g) = O(g36 poly log g), and then to f(g) = O(g19 poly log g). In this paper we further improve this bound to f(g) = O(g9 poly log g). We believe that our proof is significantly simpler than the proofs of the previous bounds. Moreover, while there are natural barriers that seem to prevent the previous methods from yielding tight bounds for the theorem, it seems conceivable that the techniques proposed in this paper can lead to even tighter bounds on f(g).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.88"}, {"primary_key": "3118968", "vector": [], "sparse_vector": [], "title": "The streaming k-mismatch problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the streaming complexity of a fundamental task in approximate pattern matching: the k-mismatch problem. In this problem, we must compute Hamming distances between a pattern of length n and all length-n substrings of a text for which the Hamming distance does not exceed a given threshold k. In our problem formulation, we report not only the Hamming distance but also, on demand, the full mismatch information, that is the list of mismatched pairs of symbols and their indices. The twin challenges of streaming pattern matching derive from the need both to achieve small working space and also to guarantee that every arriving input symbol is processed quickly.We present a streaming algorithm for the k-mismatch problem which uses bits of space and spends time on each symbol of the input stream. In our formulation, the pattern is also in the stream, arriving directly before the text. The running time almost matches the classic offline solution [5] and the space usage is within a logarithmic factor of optimal. Our new algorithm therefore effectively resolves and also extends a problem first introduced in FOCS'09 [38]. En route to this solution, we also give a deterministic -bit encoding of all the alignments with Hamming distance at most k of a length-n pattern within a text of length O(n). This secondary result provides an optimal solution to a natural encoding problem which may be of independent interest.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.68"}, {"primary_key": "3118969", "vector": [], "sparse_vector": [], "title": "Lower bounds for text indexing with mismatches and differences.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we study lower bounds for the fundamental problem of text indexing with mismatches and differences. In this problem we are given a long string of length n, the “text”, and the task is to preprocess it into a data structure such that given a query string Q, one can quickly identify substrings that are within Hamming or edit distance at most k from Q. This problem is at the core of various problems arising in biology and text processing.While exact text indexing allows linear-size data structures with linear query time, text indexing with k mismatches (or k differences) seems to be much harder: All known data structures have exponential dependency on k either in the space, or in the time bound. We provide conditional and pointer-machine lower bounds that make a step toward explaining this phenomenon.We start by demonstrating lower bounds for k = Θ(log n). We show that assuming the Strong Exponential Time Hypothesis, any data structure for text indexing that can be constructed in polynomial time cannot have O(n1–δ) query time, for any δ > 0. This bound also extends to the setting where we only ask for (1 + ε)-approximate solutions for text indexing.However, in many applications the value of k is rather small, and one might hope that for small k we can develop more efficient solutions. We show that this would require a radically new approach as using the current methods one cannot avoid exponential dependency on k either in the space, or in the time bound for all even . Our lower bounds also apply to the dictionary look-up problem, where instead of a text one is given a set of strings.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.70"}, {"primary_key": "3118970", "vector": [], "sparse_vector": [], "title": "On the rank of a random binary matrix.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the rank of a random n × m matrix An, m; k with entries from GF(2), and exactly k unit entries in each column, the other entries being zero. The columns are chosen independently and uniformly at random from the set of all (nk) such columns.We obtain an asymptotically correct estimate for the rank as a function of the number of columns m in terms of c, n, k, and where m = cn/k. The matrix An, m; k forms the vertex-edge incidence matrix of a k-uniform random hypergraph H. The rank of An, m; k can be expressed as follows. Let |C2| be the number of vertices of the 2-core of H, and | E (C2)| the number of edges. Let m* be the value of m for which |C2| = |E(C2)|. Then w.h.p. for m < m* the rank of An, m; k is asymptotic to m, and for m ≥ m* the rank is asymptotic to m – |E(C2)| + |C2|.In addition, assign i.i.d. U[0, 1] weights Xi, i ∊ 1, 2, … m to the columns, and define the weight of a set of columns S as X(S) = ∑j∊S Xj. Define a basis as a set of n – 1 (k even) linearly independent columns. We obtain an asymptotically correct estimate for the minimum weight basis. This generalises the well-known result of <PERSON><PERSON><PERSON> [On the value of a random minimum spanning tree problem, Discrete Applied Mathematics, (1985)] that, for k = 2, the expected length of a minimum weight spanning tree tends to ζ(3) ∼ 1.202.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.58"}, {"primary_key": "3118971", "vector": [], "sparse_vector": [], "title": "Prophet Secretary Through Blind Strategies.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the classic prophet inequality, a problem in optimal stopping theory, samples from independent random variables (possibly differently distributed) arrive online. A gambler that knows the distributions, but cannot see the future, must decide at each point in time whether to stop and pick the current sample or to continue and lose that sample forever. The goal of the gambler is to maximize the expected value of what she picks and the performance measure is the worst case ratio between the expected value the gambler gets and what a prophet, that sees all the realizations in advance, gets. In the late seventies, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [16], established that this worst case ratio is a constant and that 1/2 is the best possible such constant. In the last decade the theory of prophet inequalities has resurged as an important problem due to its connections to posted price mechanisms, frequently used in online sales. A particularly interesting variant is the so-called Prophet Secretary problem, in which the only difference is that the samples arrive in a uniformly random order. For this variant several algorithms are known to achieve a constant of 1 – 1/e and very recently this barrier was slightly improved by <PERSON><PERSON> et al. [3].In this paper we derive a way of analyzing multithreshold strategies that basically sets a nonincreasing sequence of thresholds to be applied at different times. The gambler will thus stop the first time a sample surpasses the corresponding threshold. Specifically we consider a class of very robust strategies that we call blind quantile strategies. These constitute a clever generalization of single threshold strategies and consist in fixing a function which is used to define a sequence of thresholds once the instance is revealed. Our main result shows that these strategies can achieve a constant of 0.669 in the Prophet Secretary problem, improving upon the best known result of Azar et al. [3], and even that of Beyhaghi et al. [4] that works in the case the gambler can select the order of the samples. The crux of the analysis is a very precise analysis of the underlying stopping time distribution for the gambler's strategy that is inspired by the theory of Schur convex functions. We further prove that our family of blind strategies cannot lead to a constant better than 0.675.Finally we prove that no nonadaptive algorithm for the gambler can achieve a constant better than 0.732, which also improves upon a recent result of Azar et al. [3]. Here, a nonadaptive algorithm is an algorithm whose decision to stop can depend on the index of the random variable being sampled, on the value sampled, and on the time, but not on the history that has been observed.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.118"}, {"primary_key": "3118972", "vector": [], "sparse_vector": [], "title": "Universal trees grow inside separating automata: Quasi-polynomial lower bounds for parity games.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Several distinct techniques have been proposed to design quasi-polynomial algorithms for solving parity games since the breakthrough result of <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON> (2017): play summaries, progress measures and register games. We argue that all those techniques can be viewed as instances of the separation approach to solving parity games, a key technical component of which is constructing (explicitly or implicitly) an automaton that separates languages of words encoding plays that are (decisively) won by either of the two players. Our main technical result is a quasi-polynomial lower bound on the size of such separating automata that nearly matches the current best upper bounds. This forms a barrier that all existing approaches must overcome in the ongoing quest for a polynomial-time algorithm for solving parity games. The key and fundamental concept that we introduce and study is a universal ordered tree. The technical highlights are a quasi-polynomial lower bound on the size of universal ordered trees and a proof that every separating safety automaton has a universal tree hidden in its state space.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.142"}, {"primary_key": "3118973", "vector": [], "sparse_vector": [], "title": "Multi-unit Supply-monotone Auctions with Bayesian Valuations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We design multi-unit auctions for budget-constrained bidders in the Bayesian setting. Our auctions are supply-monotone, which allows the auction to be run online without knowing the number of items in advance, and achieve asymptotic revenue optimality. We also give an efficient algorithm for implementing our auction by using a succinct and efficiently implementable characterization of supply-monotonicity in the Bayesian setting.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.12"}, {"primary_key": "3118975", "vector": [], "sparse_vector": [], "title": "The threshold for SDP-refutation of random regular NAE-3SAT.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Ryan <PERSON>&<PERSON>;Donnell", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unlike its cousin 3SAT, the NAE-3SAT (not-all-equal-3SAT) problem has the property that spectral/SDP algorithms can efficiently refute random instances when the constraint density is a large constant (with high probability). But do these methods work immediately above the \"satisfiability threshold\", or is there still a range of constraint densities for which random NAE-3SAT instances are unsatisfiable but hard to refute?We show that the latter situation prevails, at least in the context of random regular instances and SDP-based refutation. More precisely, whereas a random d-regular instance of NAE-3SAT is easily shown to be unsatisfiable (whp) once d ≥ 8, we establish the following sharp threshold result regarding efficient refutation: If d 13.5 then even the most basic spectral algorithm refutes satisfiability (whp).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.140"}, {"primary_key": "3118976", "vector": [], "sparse_vector": [], "title": "Computing Height Persistence and Homology Generators in R3 Efficiently.", "authors": ["<PERSON><PERSON>"], "summary": "Recently it has been shown that computing the dimension of the first homology group H 1 (K) of a simplicial 2-complex K embedded linearly in R 4 is as hard as computing the rank of a sparse 0 -1 matrix.This puts a major roadblock to computing persistence and a homology basis (generators) for complexes embedded in R 4 and beyond in less than quadratic or even near-quadratic time.But, what about dimension three?It is known that when K is a graph or a surface with n simplices linearly embedded in R 3 , the persistence for piecewise linear functions on K can be computed in O(n log n) time and a set of generators of total size k can be computed in O(n + k) time .However, the question for general simplicial complexes K linearly embedded in R 3 is not completely settled.No algorithm with a complexity better than that of the matrix multiplication is known for this important case.We show that the persistence for height functions on such complexes, hence called height persistence, can be computed in O(n log n) time.This allows us to compute a basis (generators) of H i (K), i = 1, 2, in O(n log n + k) time where k is the size of the output.This improves significantly the current best bound of O(n ω ), ω being the exponent of matrix multiplication.We achieve these improved bounds by leveraging recent results on zigzag persistence in computational topology, new observations about Reeb graphs, and some efficient geometric data structures.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.164"}, {"primary_key": "3118977", "vector": [], "sparse_vector": [], "title": "Efficient Algorithms and Lower Bounds for Robust Linear Regression.", "authors": ["<PERSON><PERSON>", "Weihao Kong", "<PERSON>"], "summary": "We study the prototypical problem of high-dimensional linear regression in a robust model where an ε-fraction of the samples can be adversarially corrupted. We focus on the fundamental setting where the covariates of the uncorrupted samples are drawn from a Gaussian distribution N(0, ∑) on ℝd. We give nearly tight upper bounds and computational lower bounds for this problem. Specifically, our main contributions are as follows:•For the case that the covariance matrix is known to be the identity, we give a sample near-optimal and computationally efficient algorithm that draws Õ(d/ε2) labeled examples and outputs a candidate hypothesis vector that approximates the unknown regression vector β within ℓ2-norm O(ε log(1/ε)σ), where σ is the standard deviation of the random observation noise. An error of Ω(εσ) is information-theoretically necessary, even with infinite sample size. Hence, the error guarantee of our algorithm is optimal, up to a logarithmic factor in 1/ε. Prior work gave an algorithm for this problem with sample complexity whose error guarantee scales with the ℓ2-norm of β.•For the case of unknown covariance ∑, we show that we can efficiently achieve the same error guarantee of O(ε log(1/ε)σ), as in the known covariance case, using an additional Õ(d2/ε2) unlabeled examples. On the other hand, an error of O(εσ) can be information-theoretically attained with O(d/ε2) samples. We prove a Statistical Query (SQ) lower bound providing evidence that this quadratic tradeoff in the sample size is inherent. More specifically, we show that any polynomial time SQ learning algorithm for robust linear regression (in <PERSON>ber's contamination model) with estimation complexity O(d2–c), where c > 0 is an arbitrarily small constant, must incur an error of .", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.170"}, {"primary_key": "3118978", "vector": [], "sparse_vector": [], "title": "Analyzing Boolean functions on the biased hypercube via higher-dimensional agreement tests: [Extended abstract].", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a new paradigm for studying the structure of Boolean functions on the biased Boolean hypercube, i.e. when the measure is µp and p is potentially very small, e.g. as small as O(1/n). Our paradigm is based on the following simple fact: the p-biased hypercube is expressible as a convex combination of many small-dimensional copies of the uniform hypercube. To uncover structure for µp, we invoke known structure theorems for µ1/2, obtaining a structured approximation for each copy separately. We then sew these approximations together using a novel \"agreement theorem\". This strategy allows us to lift structure theorems from µ1/2 to µp.We provide two applications of this paradigm:•Our main application is a structure theorem for functions that are nearly low degree in the Fourier sense. The structure we uncover in the biased hypercube is not at all the same as for the uniform hypercube, despite using the structure theorem for the uniform hypercube as a black box. Rather, new phenomena emerge: whereas nearly low degree functions on the uniform hypercube are close to juntas, when p becomes small, non-juntas arise as well. For example, the function max(y1, · · ·, yε/p) (where yi ∊ {0, 1}) is nearly degree 1 despite not being close to any junta.•A second (technically simpler) application is a test for being low degree in the GF(2) sense, in the setting of the biased hypercube.In both cases, we use as a black box the corresponding result for p = 1/2. In the first case, it is the junta theorem of Kindler and Safra, and in the second case, the low degree testing theorem of Alon et al. [IEEE Trans. Inform. Theory, 2005] and Bhattacharyya et al. [Proc. 51st FOCS, 2010].A key component of our proof is a new local-to-global agreement theorem for higher dimensions, which extends the work of Dinur and Steurer [Proc. 29th CCC, 2014]. Whereas their result sews together vectors, our agreement theorem sews together labeled graphs and hypergraphs.The proof of our agreement theorem uses a novel pruning lemma for hypergraphs, which may be of independent interest. The pruning lemma trims a given hypergraph so that the number of hyperedges in a random induced subhypergraph has roughly a Poisson distribution, while maintaining the expected number of hyperedges.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.128"}, {"primary_key": "3118979", "vector": [], "sparse_vector": [], "title": "List Decoding with <PERSON> Samplers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "PREVIOUS ARTICLEAnalyzing Boolean functions on the biased hypercube via higher-dimensional agreement tests: [Extended abstract]", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.129"}, {"primary_key": "3118980", "vector": [], "sparse_vector": [], "title": "Dynamic Edge Coloring with Improved Approximation.", "authors": ["<PERSON><PERSON>", "Hao<PERSON> He", "<PERSON><PERSON><PERSON>"], "summary": "Given an undirected simple graph G = (V, E) that undergoes edge insertions and deletions, we wish to efficiently maintain an edge coloring with only a few colors. The previous best dynamic algorithm by [3] could deterministically maintain a valid edge coloring using 2Δ – 1 colors with O(log Δ) update time, where Δ stands for the current maximum vertex degree of graph G. In this paper, we first propose a new static (1 + ∊)Δ edge coloring algorithm that runs in near-linear time. Based on this static algorithm, we show that there is a randomized dynamic algorithm for this problem that only uses (1 + ∊)Δ colors with O(log8 n/∊4) amortized update time when Δ ≥ Ω(log2 n/∊2), where ∊ > 0 is an arbitrarily small constant.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.117"}, {"primary_key": "3118982", "vector": [], "sparse_vector": [], "title": "Every Collinear Set in a Planar Graph Is Free.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that if a planar graph G has a plane straight-line drawing in which a subset S of its vertices are collinear, then for any set of points, X, in the plane with |X| = |S|, there is a plane straight-line drawing of G in which the vertices in S are mapped to the points in X. This solves an open problem posed by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> in 2008. In their terminology, we show that every collinear set is free.This result has applications in graph drawing, including untangling, column planarity, universal point subsets, and partial simultaneous drawings.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.92"}, {"primary_key": "3118983", "vector": [], "sparse_vector": [], "title": "New Lower Bounds for the Number of Pseudoline Arrangements.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Arrangements of lines and pseudolines are fundamental objects in discrete and computational geometry. They also appear in other areas of computer science, such as the study of sorting networks. Let Bn be the number of nonisomorphic arrangements of n pseudolines and let bn = log2 Bn. The problem of estimating Bn was posed by <PERSON><PERSON><PERSON> in 1992. <PERSON><PERSON><PERSON> conjectured that and also derived the first upper and lower bounds: bn ≤ 0.7924(n2 + n) and bn ≥ n2/6 – O(n). The upper bound underwent several improvements, bn ≤ 0.6988n2 (<PERSON><PERSON><PERSON>, 1997), and bn ≤ 0.6571n2 (<PERSON><PERSON><PERSON> and <PERSON>, 2011), for large n. Here we show that bn ≥ cn2 – O(n log n) for some constant c > 0.2053. In particular, bn ≥ 0.2053 n2 for large n. This improves the previous best lower bound, bn ≥ 0.1887n2, due to <PERSON><PERSON><PERSON> and <PERSON><PERSON> (2011). Our arguments are elementary and geometric in nature. Further, our constructions are likely to spur new developments and improved lower bounds for related problems, such as in topological graph drawings.MSC codescountingpseudoline arrangementrecursive construction", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.26"}, {"primary_key": "3118984", "vector": [], "sparse_vector": [], "title": "Submodular Maximization with Nearly-optimal Approximation and Adaptivity in Nearly-linear Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we study the tradeoff between the approximation guarantee and adaptivity for the problem of maximizing a monotone submodular function subject to a cardinality constraint. The adaptivity of an algorithm is the number of sequential rounds of queries it makes to the evaluation oracle of the function, where in every round the algorithm is allowed to make polynomially-many parallel queries. Adaptivity is an important consideration in settings where the objective function is estimated using samples and in applications where adaptivity is the main running time bottleneck. Previous algorithms achieving a nearly-optimal 1 – 1/e – ∊ approximation require Ω(n) rounds of adaptivity. In this work, we give the first algorithm that achieves a 1 – 1/e – ∊ approximation using O(ln n/∊2) rounds of adaptivity. The number of function evaluations and additional running time of the algorithm are O(n poly(log n, 1/∊)).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.18"}, {"primary_key": "3118985", "vector": [], "sparse_vector": [], "title": "Finding Maximal Sets of Laminar 3-Separators in Planar Graphs in Linear Time.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider decomposing a 3-connected planar graph G using laminar separators of size three. We show how to find a maximal set of laminar 3-separators in such a graph in linear time. We also discuss how to find maximal laminar set of 3-separators from special families. For example we discuss non-trivial cuts, ie. cuts which split G into two components of size at least two. For any vertex v, we also show how to find a maximal set of 3-separators disjoint from v which are laminar and satisfy: every vertex in a separator X has two neighbours not in the unique component of G – X containing v. In all cases, we show how to construct a corresponding tree decomposition of adhesion three. Our new algorithms form an important component of recent methods for finding disjoint paths in nonplanar graphs.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.37"}, {"primary_key": "3118986", "vector": [], "sparse_vector": [], "title": "Amplification by Shuffling: From Local to Central Differential Privacy via Anonymity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Sensitive statistics are often collected across sets of users, with repeated collection of reports done over time. For example, trends in users’ private preferences or software usage may be monitored via such reports. We study the collection of such statistics in the local differential privacy (LDP) model, and describe an algorithm whose privacy cost is polylogarithmic in the number of changes to a user's value.More fundamentally—by building on anonymity of the users’ reports—we also demonstrate how the privacy cost of our LDP algorithm can actually be much lower when viewed in the central model of differential privacy. We show, via a new and general privacy amplification technique, that any permutation-invariant algorithm satisfying ε-local differential privacy will satisfy -central differential privacy. By this, we explain how the high noise and overhead of LDP protocols is a consequence of them being significantly more private in the central model. As a practical corollary, our results imply that several LDP-based industrial deployments may have much lower privacy cost than their advertised ε would indicate—at least if reports are anonymized.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.151"}, {"primary_key": "3118987", "vector": [], "sparse_vector": [], "title": "Popular Matchings and Limits to Tractability.", "authors": ["<PERSON>", "Telike<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider popular matching problems in both bipartite and non-bipartite graphs with strict preference lists. It is known that every stable matching is a min-size popular matching. A subclass of max-size popular matchings called dominant matchings has been well-studied in bipartite graphs: they always exist and there is a simple linear time algorithm to find one.We show that it is NP-complete to decide if a bipartite graph admits a popular matching that is neither stable nor dominant. This gives rise to the anomaly that though it is easy to find min-size and max-size popular matchings in bipartite graphs, it is NP-complete to decide if there exists any popular matching whose size is sandwiched between the two extremes. We also show a number of related hardness results, such as (tight) 1/2-inapproximability of the maximum cost popular matching problem when costs are nonnegative. In non-bipartite graphs, we show a strong negative result: it is NP-hard to decide whether a popular matching exists or not, and the same result holds if we replace popular with dominant. On the positive side, we show the following results in any graph:•we identify a subclass of dominant matchings called strongly dominant matchings and show a linear time algorithm to decide if a strongly dominant matching exists or not;•we show an efficient algorithm to compute a popular matching of minimum cost in a graph with edge costs and bounded treewidth, or decide there is no popular matching.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.173"}, {"primary_key": "3118988", "vector": [], "sparse_vector": [], "title": "Submodular Maximization with Nearly Optimal Approximation, Adaptivity and Query Complexity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Submodular optimization generalizes many classic problems in combinatorial optimization and has recently found a wide range of applications in machine learning (e.g., feature engineering and active learning). For many large-scale optimization problems, we are often concerned with the adaptivity complexity of an algorithm, which quantifies the number of sequential rounds where polynomially-many independent function evaluations can be executed in parallel. While low adaptivity is ideal, it is not sufficient for a distributed algorithm to be efficient, since in many practical applications of submodular optimization the number of function evaluations becomes prohibitively expensive. Motivated by these applications, we study the adaptivity and query complexity of adaptive submodular optimization. Our main result is a distributed algorithm for maximizing a monotone submodular function with cardinality constraint $k$ that achieves a $(1-1/e-\\varepsilon)$-approximation in expectation. This algorithm runs in $O(\\log(n))$ adaptive rounds and makes $O(n)$ calls to the function evaluation oracle in expectation. The approximation guarantee and query complexity are optimal, and the adaptivity is nearly optimal. Moreover, the number of queries is substantially less than in previous works. Last, we extend our results to the submodular cover problem to demonstrate the generality of our algorithm and techniques.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.17"}, {"primary_key": "3118989", "vector": [], "sparse_vector": [], "title": "A Polynomial Time Constant Approximation For Minimizing Total Weighted Flow-time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the classic scheduling problem of minimizing the total weighted flow-time on a single machine (min-WPFT), when preemption is allowed. In this problem, we are given a set of n jobs, each job having a release time rj, a processing time pj, and a weight wj. The flow-time of a job is defined as the amount of time the job spends in the system before it completes; that is, Fj = Cj – rj, where Cj is the completion time of job. The objective is to minimize the total weighted flow-time of jobs.This NP-hard problem has been studied quite extensively for decades. In a recent breakthrough, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> [6] presented a pseudo-polynomial time algorithm that has an O(1) approximation ratio. The design of a truly polynomial time algorithm, however, remained an open problem. In this paper, we show a transformation from pseudo-polynomial time algorithms to polynomial time algorithms in the context of min-WPFT. Our result combined with the result of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> [6] settles the long standing conjecture that there is a polynomial time algorithm with O(1)-approximation for min-WPFT.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.96"}, {"primary_key": "3118991", "vector": [], "sparse_vector": [], "title": "Every Testable (Infinite) Property of Bounded-Degree Graphs Contains an Infinite Hyperfinite Subproperty.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "One of the most fundamental questions in graph property testing is to characterize the combinatorial structure of properties that are testable with a constant number of queries. We work towards an answer to this question for the bounded-degree graph model introduced in [GR02], where the input graphs have maximum degree bounded by a constant d. In this model, it is known (among other results) that every hyperfinite property is constant-query testable [NS13], where, informally, a graph property is hyperfinite, if for every δ > 0 every graph in the property can be partitioned into small connected components by removing δn edges.In this paper we show that hyperfiniteness plays a role in every testable property, i.e. we show that every testable property is either finite (which trivially implies hyperfiniteness and testability) or contains an infinite hyperfinite subproperty. A simple consequence of our result is that no infinite graph property that only consists of expander graphs is constant-query testable.Based on the above findings, one could ask if every infinite testable non-hyperfinite property might contain an infinite family of expander (or near-expander) graphs. We show that this is not true. Motivated by our counterexample we develop a theorem that shows that we can partition the set of vertices of every bounded degree graph into a constant number of subsets and a separator set, such that the separator set is small and the distribution of k-discs on every subset of a partition class, is roughly the same as that of the partition class if the subset has small expansion.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.45"}, {"primary_key": "3118993", "vector": [], "sparse_vector": [], "title": "The Complexity of Approximately Counting Retractions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Let G be a graph that contains an induced subgraph H. A retraction from G to H is a homomorphism from G to H that is the identity function on H. Retractions are very well-studied: Given H, the complexity of deciding whether there is a retraction from an input graph G to H is completely classified, in the sense that it is known for which H this problem is tractable (assuming P ≠ NP). Similarly, the complexity of (exactly) counting retractions from G to H is classified (assuming FP ≠ #P). However, almost nothing is known about approximately counting retractions. Our first contribution is to give a complete trichotomy for approximately counting retractions to trees. The result is as follows: (1) Approximately counting retractions to a tree H is in FP if H is a star, a single looped vertex, or an edge with two loops. (2) Otherwise, if H is an irreflexive caterpillar or a partially bristled reflexive path, then approximately counting retractions to H is equivalent to approximately counting the independent sets of a bipartite graph — a problem which is complete in the approximate counting complexity class RHπ1. (3) Finally, if none of these hold, then approximately counting retractions to H is #P-complete under approximation-preserving reductions. Our second contribution is to locate the retraction counting problem in the complexity landscape of related approximate counting problems. Interestingly, our results are in contrast to the situation in the exact counting context. We show that the problem of approximately counting retractions is separated both from the problem of approximately counting homomorphisms and from the problem of approximately counting list homomorphisms — whereas for exact counting all three of these problems are interreducible. We also show that the number of retractions is at least as hard to approximate as both the number of surjective homomorphisms and the number of compactions. In contrast, exactly counting compactions is the hardest of these problems.The full version containing detailed proofs is available at https://arxiv.org/abs/1807.00590v1 (version from 2 July 2018). The theorem numbering here matches the full version.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.133"}, {"primary_key": "3118994", "vector": [], "sparse_vector": [], "title": "Embedding Planar Graphs into Low-Treewidth Graphs with Applications to Efficient Approximation Schemes for Metric Problems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We show that, for any ∊ > 0, there is a deterministic embedding of edge-weighted planar graphs of diameter D into bounded-treewidth graphs. The embedding has additive error ∊D. We use this construction to obtain the first efficient bicriteria approximation schemes for weighted planar graphs addressing k-Center (equivalently d-Domination), and a metric generalization of independent set, d-independent SET. The approximation schemes employ a metric generalization of <PERSON>'s framework that is based on our embedding result.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.66"}, {"primary_key": "3118995", "vector": [], "sparse_vector": [], "title": "Minimum Cut and Minimum k-Cut in Hypergraphs via Branching Contractions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "On hypergraphs with m hyperedges and n vertices, where p denotes the total size of the hyperedges, we provide the following results:•We give an algorithm that runs in Õ(mn2k–2) time for finding a minimum k-cut in hypergraphs of arbitrary rank. This algorithm betters the previous best running time for the minimum k-cut problem, for k > 2.•We give an algorithm that runs in Õ(nmax{r, 2k–2}) time for finding a minimum k-cut in hypergraphs of constant rank r. This algorithm betters the previous best running times for both the minimum cut and minimum k-cut problems for dense hypergraphs.Both of our algorithms are Monte Carlo, i.e., they return a minimum k-cut (or minimum cut) with high probability. These algorithms are obtained as instantiations of a generic branching randomized contraction technique on hypergraphs, which extends the celebrated work of <PERSON><PERSON> and <PERSON> on recursive contractions in graphs. Our techniques and results also extend to the problems of minimum hedge-cut and minimum hedge-k-cut on hedgegraphs, which generalize hypergraphs.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.54"}, {"primary_key": "3118996", "vector": [], "sparse_vector": [], "title": "Exact Algorithms and Lower Bounds for Stable Instances of Euclidean k-MEANS.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We investigate the complexity of solving stable or perturbation-resilient instances of k-means and k-median clustering in fixed dimension Euclidean metrics (or more generally doubling metrics). The notion of stable or perturbation resilient instances was introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [2010] and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> [2012]. In our context, we say a k-MEANS instance is α-stable if there is a unique optimum solution which remains unchanged if distances are (non-uniformly) stretched by a factor of at most α. Stable clustering instances have been studied to explain why heuristics such as <PERSON>'s algorithm perform well in practice. In this work we show that for any fixed ∊ > 0, (1 + ∊)-stable instances of k-MEANS in doubling metrics, which include fixed-dimensional Euclidean metrics, can be solved in polynomial time. More precisely, we show a natural multi-swap local-search algorithm in fact finds the optimum solution for (1 + ∊)-stable instances of k-MEANS and k-median in a polynomial number of iterations.We complement this result by showing that under a plausible PCP hypothesis this is essentially tight: that when the dimension d is part of the input, there is a fixed ∊0 > 0 such there is not even a PTAS for (1 + ∊0)-stable k-MEANS in ℝd unless NP=RP. To do this, we consider a robust property of CSPs; call an instance stable if there is a unique optimum solution x* and for any other solution x', the number of unsatisfied clauses is proportional to the Hamming distance between x* and x'. <PERSON>ur, <PERSON>re<PERSON>, and Gur have already shown stable QSAT is hard to approximation for some constant Q [16], our hypothesis is simply that stable QSAT with bounded variable occurrence is also hard (there is in fact work in progress to prove this hypothesis). Given this hypothesis, we consider \"stability-preserving\" reductions to prove our hardness for stable k-MEANS. Such reductions seem to be more fragile and intricate than standard L-reductions and may be of further use to demonstrate other stable optimization problems are hard to solve.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.183"}, {"primary_key": "3118997", "vector": [], "sparse_vector": [], "title": "Beating <PERSON>reed<PERSON> for Stochastic Bipartite Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the maximum bipartite matching problem in stochastic settings, namely the query-commit and price-of-information models. In the query-commit model, an edge e independently exists with probability pe. We can query whether an edge exists or not, but if it does exist, then we have to take it into our solution. In the unweighted case, one can query edges in the order given by the classical online algorithm of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> [20] to get a (1 – 1/e)-approximation. In contrast, the previously best known algorithm in the weighted case is the (1/2)-approximation achieved by the greedy algorithm that sorts the edges according to their weights and queries in that order.Improving upon the basic greedy, we give a (1 – 1/e)-approximation algorithm in the weighted query-commit model. We use a linear program (LP) to upper bound the optimum achieved by any strategy. The proposed LP admits several structural properties that play a crucial role in the design and analysis of our algorithm. We also extend these techniques to get a (1 – 1/e)-approximation algorithm for maximum bipartite matching in the price-of-information model introduced by <PERSON><PERSON> [25], who also used the basic greedy algorithm to give a (1/2)-approximation.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.176"}, {"primary_key": "3118998", "vector": [], "sparse_vector": [], "title": "Lift and Project Algorithms for Precedence Constrained Scheduling to Minimize Completion Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Garg", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the classic problem of scheduling jobs with precedence constraints on a set of identical machines to minimize the weighted completion time objective. Understanding the exact approximability of the problem when job lengths are uniform is a well known open problem in scheduling theory. In this paper, we show an optimal algorithm that runs in polynomial time and achieves an approximation factor of (2 + ∊) for the weighted completion time objective when the number of machines is a constant. The result is obtained by building on the lift and project approach introduced in a breakthrough work by <PERSON><PERSON> and <PERSON> [15] for the makespan minimization problem.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.95"}, {"primary_key": "3119000", "vector": [], "sparse_vector": [], "title": "Distributed Maximal Independent Set using Small Messages.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Maximal Independent Set (MIS) is one of the central problems in distributed graph algorithms. The celebrated works of <PERSON><PERSON> [STOC'85] and <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> [JALG'86] provide O(log n)-round randomized distributed MIS algorithms, which work with O(log n)-bit messages. This round complexity was improved to in a breakthrough of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON> [FOCS'11; JACM'16] and then to by <PERSON><PERSON><PERSON><PERSON> [SODA'16], where Δ denotes the maximum degree. However, these improvements have one drawback: they require much larger messages, up to poly (Δ log n) bits. Indeed, the question of improving the O(log n) round complexity using small messages has remained open for three decades, for essentially all values of Δ, except for Δ = o(log n) where there are O(Δ + log* n)-round deterministic algorithms.We present a randomized distributed MIS algorithm, with O(log n)-bit messages, that achieves a round complexity of min{log Δ · , O(log Δ · log log n) + }. This is the first algorithm with small messages that improves on the O(log n) round complexity of <PERSON><PERSON> and <PERSON><PERSON> et al. for a wide range of A, and its complexity almost matches that of the best known algorithm using unbounded message sizes. As applications of this MIS algorithm or along the way to it, we obtain improved distributed algorithms with small messages for some other well-studied problems including network decompositions, (Δ + 1)-vertex coloring, and ruling sets.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.50"}, {"primary_key": "3119001", "vector": [], "sparse_vector": [], "title": "Sparsifying Distributed Algorithms with Ramifications in Massively Parallel Computation and Centralized Local Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a method for “sparsifying” distributed algorithms and exhibit how it leads to improvements that go past known barriers in two algorithmic settings of large-scale graph processing: Massively Parallel Computation (MPC), and Local Computation Algorithms (LCA).•MPC with Strongly Sublinear Memory: Recently, there has been growing interest in obtaining MPC algorithms that are faster than their classic O(log n)-round parallel (PRAM) counterparts for problems such as Maximal Independent Set (MIS), Maximal Matching, 2-Approximation of Minimum Vertex Cover, and (1 + ∊)-Approximation of Maximum Matching. Currently, all such MPC algorithms require memory of per machine: <PERSON><PERSON><PERSON><PERSON> et al. [STOC'18] were the first to handle memory, running in O((log log n)2) rounds, who improved on the n1+Ω(1) memory requirement of the O(1)-round algorithm of <PERSON><PERSON><PERSON><PERSON> et al [SPAA'11]. We obtain -round MPC algorithms for all these four problems that work even when each machine has strongly sublinear memory, e.g., nα for any constant α ∊ (0, 1). Here, Δ denotes the maximum degree. These are the first sublogarithmictime MPC algorithms for (the general case of) these problems that break the linear memory barrier.•LCAs with Query Complexity Below the Parnas-Ron Paradigm: Currently, the best known LCA for MIS has query complexity ΔO(log Δ) poly(log n), by <PERSON><PERSON><PERSON><PERSON> [SODA'16], which improved over the ΔO(log2 Δ) poly(log n) bound of Levi et al. [Algorithmica'17]. As pointed out by <PERSON><PERSON>, obtaining a query complexity of poly(Δ log n) remains a central open question. Ghaffari's bound almost reaches a barrier common to all known MIS LCAs, which sim-ulate a distributed algorithm by learning the full local topology, à la Parnas-Ron [TCS'07]. There is a barrier because the distributed complexity of MIS has a lower bound of , by results of Kuhn, et al. [JACM'16], which means this methodology cannot go below query complexity . We break this barrier and obtain an LCA for MIS that has a query complexity ΔO(log log Δ) poly(log n).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.99"}, {"primary_key": "3119003", "vector": [], "sparse_vector": [], "title": "Optimizing quantum optimization algorithms via faster quantum gradient computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider a generic framework of optimization algorithms based on gradient descent. We develop a quantum algorithm that computes the gradient of a multi-variate real-valued function $f:\\mathbb{R}^d\\rightarrow \\mathbb{R}$ by evaluating it at only a logarithmic number of points in superposition. Our algorithm is an improved version of <PERSON>'s gradient computation algorithm, providing an approximation of the gradient $\\nabla f$ with quadratically better dependence on the evaluation accuracy of $f$, for an important class of smooth functions. Furthermore, we show that most objective functions arising from quantum optimization procedures satisfy the necessary smoothness conditions, hence our algorithm provides a quadratic improvement in the complexity of computing their gradient. We also show that in a continuous phase-query model, our gradient computation algorithm has optimal query complexity up to poly-logarithmic factors, for a particular class of smooth functions. Moreover, we show that for low-degree multivariate polynomials our algorithm can provide exponential speedups compared to <PERSON>'s algorithm in terms of the dimension $d$. One of the technical challenges in applying our gradient computation procedure for quantum optimization problems is the need to convert between a probability oracle (which is common in quantum optimization procedures) and a phase oracle (which is common in quantum algorithms) of the objective function $f$. We provide efficient subroutines to perform this delicate interconversion between the two types of oracles incurring only a logarithmic overhead, which might be of independent interest. Finally, using these tools we improve the runtime of prior approaches for training quantum auto-encoders, variational quantum eigensolvers (VQE), and quantum approximate optimization algorithms (QAOA).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.87"}, {"primary_key": "3119004", "vector": [], "sparse_vector": [], "title": "Maximally Recoverable LRCs: A field size lower bound and constructions for few heavy parities.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "The explosion in the volumes of data being stored online has resulted in distributed storage systems transitioning to erasure coding based schemes. Local Reconstruction Codes (LRCs) have emerged as the codes of choice for these applications. These codes can correct a small number of erasures (which is the typical case) by accessing only a small number of remaining coordinates. An (n, r, h, a, q)-LRC is a linear code over of length n, whose codeword symbols are partitioned into g = n/r local groups each of size r. Each local group has a local parity checks that allow recovery of up to a erasures within the group by reading the unerased symbols in the group. There are a further h \"heavy\" parity checks to provide fault tolerance from more global erasure patterns. Such an LRC is Maximally Recoverable (MR), if it corrects all erasure patterns which are information-theoretically correctable under the stipulated structure of local and global parity checks, namely patterns with up to a erasures in each local group and an additional h (or fewer) erasures anywhere in the codeword.The existing constructions require fields of size nΩ(h) while no superlinear lower bounds were known for any setting of parameters. Is it possible to get linear field size similar to the related MDS codes (e.g. Reed-Solomon codes)? In this work, we answer this question by showing superlinear lower bounds on the field size of MR LRCs. When a, h are constant and the number of local groups g  h, while r may grow with n, our lower bound simplifies toMR LRCs deployed in practice have a small number of global parities, typically h = 2, 3 [HSX+12]. We complement our lower bounds by giving constructions with small field size for h  3. When h = 2, we give a linear field size construction, whereas previous constructions required quadratic field size in some parameter ranges. Note that our lower bound is superlinear only if h  3. When h = 3, we give a construction with O(n3) field size, whereas previous constructions needed nΘ(a) field size. Our construction for h = 2 makes the choices r = 3, a = 1, h = 3 the next smallest setting to investigate regarding the existence of MR LRCs over fields of near-linear size. We answer this question in the positive via a novel approach based on elliptic curves and arithmetic progression free sets.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.130"}, {"primary_key": "3119005", "vector": [], "sparse_vector": [], "title": "Reproducibility and Pseudo-Determinism in Log-Space.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "A curious property of randomized log-space search algorithms is that their outputs are often longer than their workspace. This leads to the question: how can we reproduce the results of a randomized log space computation without storing the output or randomness verbatim? Running the algorithm again with new random bits may result in a new (and potentially different) output.We show that every problem in search-RL has a randomized log-space algorithm where the output can be reproduced. Specifically, we show that for every problem in search-RL, there are a pair of log-space randomized algorithms A and B where for every input x, A will output some string tx of size O(log n), such that B when running on (x, tx) will be pseudo-deterministic: that is, running B multiple times on the same input (x, tx) will result in the same output on all executions with high probability. Thus, by storing only O(log n) bits in memory, it is possible to reproduce the output of a randomized log-space algorithm.An algorithm is reproducible without storing any bits in memory (i.e., |tx| = 0) if and only if it is pseudo-deterministic. We show pseudo-deterministic algorithms for finding paths in undirected graphs and Eulerian graphs using logarithmic space. Our algorithms are substantially faster than the best known deterministic algorithms for finding paths in such graphs in log-space.The algorithm for search-RL has the additional property that its output, when viewed as a random variable depending on the randomness used by the algorithm, has entropy O(log n).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.38"}, {"primary_key": "3119006", "vector": [], "sparse_vector": [], "title": "Analysis of Ward&apos;s Method.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study <PERSON>'s method for the hierarchical k-means problem. This popular greedy heuristic is based on the complete linkage paradigm: Starting with all data points as singleton clusters, it successively merges two clusters to form a clustering with one cluster less. The pair of clusters is chosen to (locally) minimize the k-means cost of the clustering in the next step.Complete linkage algorithms are very popular for hierarchical clustering problems, yet their theoretical properties have been studied relatively little. For the Euclidean k-center problem, <PERSON><PERSON><PERSON> et al. [1] show that the k-clustering in the hierarchy computed by complete linkage has a worst-case approximation ratio of Θ(log k). If the data lies in ℝd for constant dimension d, the guarantee improves to O(1) [23], but the O-notation hides a linear dependence on d. Complete linkage for k-median or k-means has not been analyzed so far.In this paper, we show that <PERSON>'s method computes a 2-approximation with respect to the k-means objective function if the optimal k-clustering is well separated. If additionally the optimal clustering also satisfies a balance condition, then <PERSON>'s method fully recovers the optimum solution. These results hold in arbitrary dimension. We accompany our positive results with a lower bound of Ω((3/2)d) for data sets in ℝd that holds if no separation is guaranteed, and with lower bounds when the guaranteed separation is not sufficiently strong. Finally, we show that <PERSON> produces an O(1)-approximative clustering for one-dimensional data sets.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.182"}, {"primary_key": "3119007", "vector": [], "sparse_vector": [], "title": "Polynomial-time algorithm for Maximum Weight Independent Set on P6-free graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the classic Maximum Weight Independent Set problem we are given a graph G with a nonnegative weight function on vertices, and the goal is to find an independent set in G of maximum possible weight. While the problem is NP-hard in general, we give a polynomial-time algorithm working on any P6-free graph, that is, a graph that has no path on 6 vertices as an induced subgraph. This improves the polynomial-time algorithm on P5-free graphs of <PERSON><PERSON><PERSON><PERSON> et al. [11], and the quasipolynomial-time algorithm on P6-free graphs of <PERSON><PERSON><PERSON><PERSON> et al. [12]. The main technical contribution leading to our main result is enumeration of a polynomial-size family ℱ of vertex subsets with the following property: for every maximal independent set I in the graph, ℱ contains all maximal cliques of some minimal chordal completion of G that does not add any edge incident to a vertex of I.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.77"}, {"primary_key": "3119008", "vector": [], "sparse_vector": [], "title": "Elastic Caching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Motivated by applications in cloud computing, we study the classical online caching problem for a cache of variable size, where the algorithm pays a maintenance cost that monotonically increases with cache size. This captures not only the classical setting of a fixed cache size, which corresponds to a maintenance cost of 0 for a cache of size at most k and ∞ otherwise, but also other natural settings in the context of cloud computing such as a concave rental cost on cache size. We call this the elastic caching problem.Our results are: (a) a randomized algorithm with a competitive ratio of O(log n) for maintenance cost that is an arbitrary function of cache size, (b) a deterministic algorithm with a competitive ratio of 2 for concave, or more generally submodular maintenance costs, (c) a deterministic n-competitive algorithm when the cost function is any monotone non-negative set function, and (d) a randomized constant-factor approximation algorithm for the offline version of the problem. Our algorithms are based on a configuration LP formulation of the problem, for which our main technical contribution is to maintain online a feasible fractional solution that can be converted to an integer solution using existing rounding techniques.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.10"}, {"primary_key": "3119009", "vector": [], "sparse_vector": [], "title": "Losing Treewidth by Separating Subsets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of deleting the smallest set S of vertices (resp. edges) from a given graph G such that the induced subgraph (resp. subgraph) G\\S belongs to some class ℋ. We consider the case where graphs in ℋ have treewidth bounded by t, and give a general framework to obtain approximation algorithms for both vertex and edge-deletion settings from approximation algorithms for certain natural graph partitioning problems called k-Subset Vertex Separator and k-Subset Edge Separator, respectively.For the vertex deletion setting, our framework combined with the current best result for k-Subset Vertex Separator, improves approximation ratios for basic problems such as k-Treewidth Vertex Deletion and Planar-ℱ Vertex Deletion. Our algorithms are simpler than previous works and give the first deterministic and uniform approximation algorithms under the natural parameterization.For the edge deletion setting, we give improved approximation algorithms for k-Subset Edge Separator combining ideas from LP relaxations and important separators. We present their applications in bounded-degree graphs, and also give an APX-hardness result for the edge deletion problems.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.104"}, {"primary_key": "3119010", "vector": [], "sparse_vector": [], "title": "Popular Matching in Roommates Setting is NP-hard.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Pranaben<PERSON> Mi<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An input to the Popular Matching problem, in the roommates setting, consists of a graph G where each vertex ranks its neighbors in strict order, known as its preference. In the Popular Matching problem the objective is to test whether there exists a matching M* such that there is no matching M where more people (vertices) are happier (in terms of the preferences) with M than with M*. In this paper we settle the computational complexity of the Popular Matching problem in the roommates setting by showing that the problem is NP-complete. Thus, we resolve an open question that has been repeatedly and explicitly asked over the last decade.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.174"}, {"primary_key": "3119011", "vector": [], "sparse_vector": [], "title": "On the Number of Circuits in Regular Matroids (with Connections to Lattices and Codes).", "authors": ["<PERSON><PERSON><PERSON>", "Nisheeth K. <PERSON>"], "summary": "We show that for any regular matroid on m elements and any α ≥ 1, the number of α-minimum circuits, or circuits whose size is at most an α-multiple of the minimum size of a circuit in the matroid is bounded by mO(α2). This generalizes a result of <PERSON><PERSON> for the number of α-minimum cuts in a graph. As a consequence, we obtain similar bounds on the number of α-shortest vectors in \"totally unimodular\" lattices and on the number of α-minimum weight codewords in \"regular\" codes.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.53"}, {"primary_key": "3119012", "vector": [], "sparse_vector": [], "title": "On r-Simple k-Path and Related Problems Parameterized by k/r.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> et al. (2014) introduced the following two problems. In the r-Simple k-Path problem, given a digraph G on n vertices and positive integers r, k, decide whether G has an r-simple k-path, which is a walk where every vertex occurs at most r times and the total number of vertex occurrences is k. In the (r, k)-Monomial Detection problem, given an arithmetic circuit that succinctly encodes some polynomial P on n variables and positive integers k, r, decide whether P has a monomial of total degree k where the degree of each variable is at most r. <PERSON><PERSON><PERSON> et al. obtained randomized algorithms of running time 4(k/r)log r ·nO(1) for both problems. <PERSON><PERSON><PERSON> et al. (2015) designed deterministic 2O((k/r)log r) · nO(1)-time algorithms for both problems (however, for the (r, k)-Monomial Detection problem the input circuit is restricted to be noncanceling). <PERSON><PERSON><PERSON> et al. also studied the following problem. In the p-Set (r, q)-Packing problem, given a universe V, positive integers p, q, r, and a collection ℋ of sets of size p whose elements belong to V, decide whether there exists a subcollection ℋ' of ℋ of size q where each element occurs in at most r sets of ℋ'. <PERSON><PERSON><PERSON> et al. obtained a deterministic 2O((pq/r)log r) ·nO(1)-time algorithm for p-Set (r, q)-Packing.The above results prove that the three problems are single-exponentially fixed-parameter tractable (FPT) when parameterized by the product of two parameters, that is, k/r and log r, where k = pq for p-Set (r, q)-Packing. Abasi et al. and Gabizon et al. asked whether the log r factor in the exponent can be avoided. Bonamy et al. (2017) answered the question for (r, k)-Monomial Detection by proving that unless the Exponential Time Hypothesis (ETH) fails there is no 2o((k/r) log r) · (n + log k)O(1)-time algorithm for (r, k)-Monomial Detection, i.e. (r, k)-Monomial Detection is highly unlikely to be single-exponentially FPT when parameterized by k/r alone. The question remains open for r-Simple k-Path and p-Set (r, q)-Packing.We consider the question from a wider perspective: are the above problems FPT when parameterized by k/r only, i.e. whether there exists a computable function f such that the problems admit a f(k/r)(n + log k)O(1)-time algorithm? Since r can be substantially larger than the input size, the algorithms of Abasi et al. and Gabizon zon et al. do not even show that any of these three problems is in XP parameterized by k/r alone. We resolve the wider question by (a) obtaining a 2O((k/r)2 log(k/r)) · (n + log k)O(1)-time algorithm for r-Simple k-Peth on digraphs and a 2O(k/r) ·(n+log k)O(1)-time algorithm for r-Simple k-Path on undirected graphs (i.e., for undirected graphs we answer the original question in affirmative), (b) showing that p-Set (r, q)-Packing is FPT (in contrast, we prove that p-Multiset (r, q)-Packing is W[1]-hard), and (c) proving that (r, k)-Monomial Detrction is para-NP-hard even if only two distinct variables are in polynomial P and the circuit is noncanceling. For the special case of (r, k)-Monomial Detection here k is polynomially bounded by the input size (which is in XP), we show W[1]-hardness. Along the way to solve p-Set (r, q)-Packing, we obtain a polynomial kernel for any fixed p, which resolves a question posed by Gabizon et al. regarding the existence of polynomial kernels for problems with relaxed disjointness constraints. All our algorithms are deterministic.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.105"}, {"primary_key": "3119013", "vector": [], "sparse_vector": [], "title": "Massively Parallel Approximation Algorithms for Edit Distance and Longest Common Subsequence.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "String similarity measures are among the most fundamental problems in computer science. The notable examples are edit distance (ED) and longest common subsequence (LCS). These problems find their applications in various contexts such as computational biology, text processing, compiler optimization, data analysis, image analysis, etc. In this work, we revisit edit distance and longest common subsequence in the parallel settings. We present massively parallel algorithms for both problems that are optimal in the following senses:•The approximation factor of our algorithms is 1 + ∊.•The round complexity of our algorithms is constant.•The total running time of our algorithms over all machines is Õ(n2). This matches the running time of the best-known solutions for approximating edit distance and longest common subsequence within a 1 + ∊ factor in the sequential setting.Our result for edit distance substantially improves the massively parallel algorithm of [15] in terms of approximation factor, round complexity, number of machines, and total running time. Our unified approach to tackle both problems is to divide one of the strings into smaller blocks and try to locally predict which intervals of the other string correspond to each block in an optimal solution.Our main technical contribution is a novel parallel algorithm for computing a set of compositions, and recursively decomposing each function into a set of smaller iterative compositions (in terms of memory needed to solve the problem). These two methods together give us a strong tool for approximating combinatorial problems. For instance, LCS can be formulated as a recursive composition of functions and therefore this tool enables us to approximate LCS within a factor 1 + ∊. Indeed, we recursively decompose the problem until we are able to compute the solution on a single machine. Since our methods are quite general, we expect this technique to find its applications in other combinatorial problems as well.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.100"}, {"primary_key": "3119014", "vector": [], "sparse_vector": [], "title": "Approximating LCS in Linear Time: Beating the √n Barrier.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Longest common subsequence (LCS) is one of the most fundamental problems in combinatorial optimization. Apart from theoretical importance, LCS has enormous applications in bioinformatics, revision control systems, and data comparison programs1. Although a simple dynamic program computes LCS in quadratic time, it has been recently proven that the problem admits a conditional lower bound and may not be solved in truly subquadratic time [2]. In addition to this, LCS is notoriously hard with respect to approximation algorithms. Apart from a trivial sampling technique that obtains a nx approximation solution in time O(n2–2x) nothing else is known for LCS. This is in sharp contrast to its dual problem edit distance for which several linear time solutions are obtained in the past two decades [4, 5, 9, 10, 16].In this work, we present the first nontrivial algorithm for approximating LCS in linear time. Our main result is a linear time algorithm for the longest common subsequence which has an approximation factor of O(n0.497956). This beats the barrier for approximating LCS in linear time.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.72"}, {"primary_key": "3119015", "vector": [], "sparse_vector": [], "title": "Testing Halfspaces over Rotation-Invariant Distributions.", "authors": ["<PERSON>"], "summary": "We present an algorithm for testing halfspaces over arbitrary, unknown rotation-invariant distributions. Using random examples of an unknown function f, the algorithm determines with high probability whether f is of the form f(x) = sign(∑i ωixi – t) or is ∊-far from all such functions. This sample size is significantly smaller than the well-known requirement of Θ(n) samples for learning halfspaces, and known lower bounds imply that our sample size is optimal (in its dependence on n) up to logarithmic factors. The algorithm is distribution-free in the sense that it requires no knowledge of the distribution aside from the promise of rotation invariance. To prove the correctness of this algorithm we present a theorem relating the distance between a function and a halfspace to the distance between their centers of mass, that applies to arbitrary distributions.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.44"}, {"primary_key": "3119016", "vector": [], "sparse_vector": [], "title": "Oblivious resampling oracles and parallel algorithms for the Lopsided Lovász Local Lemma.", "authors": ["<PERSON>"], "summary": "The Lovász Local Lemma (LLL) is a probabilistic tool which shows that, if a collection of \"bad\" events B in a probability space are not too likely and not too interdependent, then there is a positive probability that no bad-events in B occur. <PERSON><PERSON> <PERSON> (2010) gave sequential and parallel algorithms which transformed most applications of the variable-assignment LLL into efficient algorithms. A framework of Harvey & Vondrák (2015) based on \"resampling oracles\" extended this give very general sequential algorithms for other probability spaces satisfying the Lopsided Lovász Local Lemma (LLLL).We describe a new structural property of resampling oracles which holds for all known resampling oracles, which we call \"obliviousness.\" Essentially, it means that the interaction between two bad-events B, B' depends only on the randomness used to resample B, and not on the precise state within B itself.This property has two major consequences. First, it is the key to achieving a unified parallel LLLL algorithm, which is faster than previous, problem-specific algorithms of <PERSON> (2016) for the variable-assignment LLLL algorithm and of <PERSON> (2014) for permutations. This new algorithm extends a framework of <PERSON><PERSON><PERSON><PERSON> (2016), and gives the first RNC algorithms for rainbow perfect matchings and rainbow hamiltonian cycles of Kn.Second, this property allows us to build LLLL probability spaces out of a relatively simple \"atomic\" set of events. It was intuitively clear that existing LLLL spaces were built in this way; but the obliviousness property formalizes this and gives a way of automatically turning a resampling oracle for atomic events into a resampling oracle for conjunctions of them. Using this framework, we get the first sequential resampling oracle for rainbow perfect matchings on the complete s-uniform hypergraph Kn(s), and the first commutative resampling oracle for hamiltonian cycles of Kn.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.52"}, {"primary_key": "3119017", "vector": [], "sparse_vector": [], "title": "Polynomial Planar Directed Grid <PERSON>.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The grid theorem, originally proved by <PERSON> and <PERSON> in 1986 [RS10, Graph Minors V], is one of the most central results in the study of graph minors and has found many algorithmic applications, especially in the analysis of routing problems. The relation between treewidth and grid minors is particularly tight for planar graphs, as every planar graph of treewidth at least 6k contains a grid of order k as a minor [RST94]. This polynomial, in fact linear, bound on the size of grid minors has enabled many important consequences, such as sublinear separators and subexponential algorithms for many NP-hard problems on planar graphs. In the mid-90s, <PERSON> and <PERSON>, <PERSON>, <PERSON> and <PERSON> proposed a notion of directed treewidth and conjectured an excluded grid theorem for directed graphs. This theorem was proved in 2015 [KK15] by the latter two authors but the function relating directed treewidth and grid minors is very big, even in the planar case.Directed grids have found several algorithmic applications such as low-congestion routing. See e.g. [CE15, CEP16, KKK14, EMW16, AKKW16]. However, in the undirected case the polynomial, in fact linear, bound on the size of grid minors in planar graphs have made this tool so extremely successful. Consequently, the lack of polynomial bounds for directed grid minors in planar digraphs has so far prevented further applications of this technique in the directed setting. The main result of this paper is to close this gap and to establish a polynomial bound for the directed grid theorem on planar digraphs. We are optimistic that this will enable further applications of directed treewidth and its dual notion of directed grids in the context of planar digraphs.Towards the end, we also give \"treewidth sparsifier\" for directed graphs, which has been already considered in undirected graphs. This result allows us to obtain an Eulerian subgraph of bounded degree in D that still has high directed treewidth. We believe this result is of independent interest for structural graph theory.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.89"}, {"primary_key": "3119018", "vector": [], "sparse_vector": [], "title": "A Fourier-Analytic Approach for the Discrepancy of Random Set Systems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "One of the prominent open problems in combinatorics is the discrepancy of set systems where each element lies in at most t sets. The <PERSON><PERSON><PERSON><PERSON> conjecture suggests that the right bound is , but for three decades the only known bound not depending on the size of set system has been O(t). Arguably we currently lack techniques for breaking that barrier.In this paper we introduce discrepancy bounds based on Fourier analysis. We demonstrate our method on random set systems. Suppose one has n elements and m sets containing each element independently with probability p. We prove that in the regime of n ≥ Θ(m2 log(m)), the discrepancy is at most 1 with high probability. Previously, a result of <PERSON> and <PERSON> gave a bound of O(1) under the stricter assumption that n ≫ mt.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.156"}, {"primary_key": "3119019", "vector": [], "sparse_vector": [], "title": "Lower Bounds for Oblivious Data Structures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An oblivious data structure is a data structure where the memory access patterns reveals no information about the operations performed on it. Such data structures were introduced by <PERSON> et al. [ACM SIGSAC’14] and are intended for situations where one wishes to store the data structure at an untrusted server. One way to obtain an oblivious data structure is simply to run a classic data structure on an oblivious RAM (ORAM). Until very recently, this resulted in an overhead of ω(lg n) for the most natural setting of parameters. Moreover, a recent lower bound for ORAMs by <PERSON> and <PERSON> [CRYPTO’18] show that they always incur an overhead of at least Ω(lg n) if used in a black box manner. To circumvent the ω(lg n) overhead, researchers have instead studied classic data structure problems more directly and have obtained efficient solutions for many such problems such as stacks, queues, deques, priority queues and search trees. However, none of these data structures process operations faster than Θ(lg n), leaving open the question of whether even faster solutions exist. In this paper, we rule out this possibility by proving Ω(lg n) lower bounds for oblivious stacks, queues, deques, priority queues and search trees.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.149"}, {"primary_key": "3119020", "vector": [], "sparse_vector": [], "title": "Algorithms for #BIS-hard problems on expander graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give an FPTAS and an efficient sampling algorithm for the high-fugacity hard-core model on bounded-degree bipartite expander graphs and the low-temperature ferromagnetic Potts model on bounded-degree expander graphs. The results apply, for example, to random (bipartite) Δ-regular graphs, for which no efficient algorithms were known for these problems (with the exception of the <PERSON><PERSON> model) in the non-uniqueness regime of the infinite Δ-regular tree.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.135"}, {"primary_key": "3119021", "vector": [], "sparse_vector": [], "title": "A Faster External Memory Priority Queue with DecreaseKeys.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A priority queue is a fundamental data structure that maintains a dynamic set of (key, priority)-pairs and supports Insert, Delete, ExtractMin and DecreaseKey operations. In the external memory model, the current best priority queue supports each operation in amortized I/Os. If the DecreaseKey operation does not need to be supported, one can design a more efficient data structure that supports the Insert, Delete and ExtractMin operations in I/Os. A recent result shows that a degradation in performance is inevitable by proving a lower bound of I/Os for priority queues with DecreaseKeys. In this paper we tighten the gap between the lower bound and the upper bound by proposing a new priority queue which supports the DecreaseKey operation and has an expected amortized I/O complexity of . Our result improves the external memory priority queue with DecreaseKeys for the first time in over a decade, and also gives the fastest external memory single source shortest path algorithm.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.81"}, {"primary_key": "3119022", "vector": [], "sparse_vector": [], "title": "How to guess an n-digit number.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In a deductive game for two players, SF and PGOM, SF conceals an n-digit number x = x1, …, xn in base q, and PGOM, who knows n and q, tries to identify x by asking a number of questions, which are answered by SF. Each question is an n-digit number y = y1, …, yn in base q; each answer is the number of subscripts i such that xi = yi. Moreover, we require PGOM send all the questions at once. We show that the minimum number of questions required to determine x is (2+oq(1))n/ logq n. Our result closes the gap between the lower bound attributed to <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and the upper bounds developed subsequently by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON>. A more general problem is to determine the asymptotic formula of the metric dimension of Cartesian powers of a graph. We state the class of graphs for which the formula can be determined, and the smallest graphs for which we did not manage to settle.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.74"}, {"primary_key": "3119023", "vector": [], "sparse_vector": [], "title": "Tight Revenue Gaps among Simple Mechanisms.", "authors": ["Yaonan Jin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider a fundamental problem in microeconomics: Selling a single item among a number of buyers whose values are drawn from known independent and regular distributions. There are four widely-used and widely-studied mechanisms in this literature: Anonymous Posted-Pricing (AP), Second-Price Auction with Anonymous Reserve (AR), Sequential Posted-Pricing (SPM), and <PERSON>on Auction (OPT). Myerson Auction is optimal but complicated, which also suffers a few issues in practice such as fairness; AP is the simplest mechanism, but its revenue is also the lowest among these four; AR and SPM are of intermediate complexity and revenue. We study the revenue gaps among these four mechanisms, which is defined as the largest ratio between revenues from two mechanisms. We establish two tight ratios and one tighter bound:1.SPM/AP. This ratio studies the power of discrimination in pricing schemes. We obtain the tight ratio of roughly 2.62, closing the previous known bounds [e/(e – 1), e].2.AR/AP. This ratio studies the relative power of auction vs. pricing schemes, when no discrimination is allowed. We get the tight ratio of π2/6 ≈ 1.64, closing the previous known bounds [e/(e – 1), e].3.OPT/AR. This ratio studies the power of discrimination in auctions. Previously, the revenue gap is known to be in interval [2, e], and the lower-bound of 2 is conjectured to be tight [38, 37, 4]. We disprove this conjecture by obtaining a better lower-bound of 2.15.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.14"}, {"primary_key": "3119025", "vector": [], "sparse_vector": [], "title": "Adaptive Sparse Recovery with Limited Adaptivity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The goal of adaptive sparse recovery is to estimate an approximately sparse vector x from a series of linear measurements A1x, A2x, …, ARx, where each matrix Ai may depend on the previous observations. With an unlimited number of rounds R, it is known that O(k log log n) measurements suffice for O(1)-approximate k-sparse recovery in ℝn, and that Ω(k + log log n) measurements are necessary. We initiate the study of what happens with a constant number of rounds of adaptivity. Previous techniques could not give nontrivial bounds using less than 5 rounds of adaptivity, and were inefficient for any constant R.We give nearly matching upper and lower bounds for any constant number of rounds R. Our lower bound shows that measurements are necessary for any k < ; significantly, this is the first lower bound that combines k and n in an adaptive setting.Our upper bound shows that measurements suffice. The O(log* k) gap between the two bounds comes from a similar gap for nonadaptive sparse recovery in the high-SNR regime, and would be reduced to constant factors with improvements to nonadaptive high-SNR sparse recovery.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.169"}, {"primary_key": "3119026", "vector": [], "sparse_vector": [], "title": "Anaconda: A Non-Adaptive Conditional Sampling Algorithm for Distribution Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We investigate distribution testing with access to non-adaptive conditional samples. In the conditional sampling model, the algorithm is given the following access to a distribution: it submits a query set S to an oracle, which returns a sample from the distribution conditioned on being from S. In the non-adaptive setting, all query sets must be specified in advance of viewing the outcomes.Our main result is the first polylogarithmic-query algorithm for equivalence testing, deciding whether two unknown distributions are equal to or far from each other. This is an exponential improvement over the previous best upper bound, and demonstrates that the complexity of the problem in this model is intermediate to the the complexity of the problem in the standard sampling model and the adaptive conditional sampling model. We also significantly improve the sample complexity for the easier problems of uniformity and identity testing. For the former, our algorithm requires only Õ(log n) queries, matching the information-theoretic lower bound up to a O(log log n)-factor.Our algorithm works by reducing the problem from ℓ1-testing to ℓ∞-testing, which enjoys a much cheaper sample complexity. Necessitated by the limited power of the non-adaptive model, our algorithm is very simple to state. However, there are significant challenges in the analysis, due to the complex structure of how two arbitrary distributions may differ.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.43"}, {"primary_key": "3119027", "vector": [], "sparse_vector": [], "title": "On coalescence time in graphs: When is coalescing as fast as meeting?: Extended Abstract.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Coalescing random walks is a fundamental stochastic process, where a set of particles perform independent discrete-time random walks on an undirected graph. Whenever two or more particles meet at a given node, they merge and continue as a single random walk. The coalescence time is defined as the expected time until only one particle remains, starting from one particle at every node. Despite recent progress such as by <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> [13] and <PERSON>, <PERSON> and <PERSON><PERSON><PERSON> [12], the coalescence time for graphs such as binary trees, d-dimensional tori, hypercubes and more generally, vertex-transitive graphs, remains unresolved.We provide a powerful toolkit that results in tight bounds for various topologies including the aforementioned ones. The meeting time is defined as the worst-case expected time required for two random walks to arrive at the same node at the same time. As a general result, we establish that for graphs whose meeting time is only marginally larger than the mixing time (a factor of log2 n), the coalescence time of n random walks equals the meeting time up to constant factors. This upper bound is complemented by the construction of a graph family demonstrating that this result is the best possible up to constant factors. For almost-regular graphs, we bound the coalescence time by the hitting time, resolving the discrete-time variant of a conjecture by <PERSON><PERSON><PERSON> for this class of graphs. Finally, we prove that for any graph the coalescence time is bounded by O(n3) (which is tight for the <PERSON><PERSON> graph); surprisingly even such a basic question about the coalescing time was not answered before this work. By duality, our results give bounds on the voter model and therefore give bounds on the consensus time in arbitrary undirected graphs.We also establish a new bound on the hitting time and cover time of regular graphs, improving and tightening previous results by Broder and Karlin [10], as well as those by Aldous and Fill [1].", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.59"}, {"primary_key": "3119029", "vector": [], "sparse_vector": [], "title": "A sort of an adversary.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We describe an efficient deterministic adversary that forces any comparison-based sorting algorithm to perform at least n log n comparisons. This improves on previous efficient adversaries of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (1981), <PERSON> and <PERSON> (1988), and of <PERSON><PERSON> et al. (1996) that force any sorting algorithm to perform at least ψn log n comparisons.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.79"}, {"primary_key": "3119030", "vector": [], "sparse_vector": [], "title": "Dimension-independent Sparse Fourier Transform.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Discrete Fourier Transform (DFT) is a fundamental computational primitive, and the fastest known algorithm for computing the DFT is the FFT (Fast Fourier Transform) algorithm. One remarkable feature of FFT is the fact that its runtime depends only on the size N of the input vector, but not on the dimensionality of the input domain: FFT runs in time O(N log N) irrespective of whether the DFT in question is on ℤN or ℤnd for some d > 1, where N = nd.The state of the art for Sparse FFT, i.e. the problem of computing the DFT of a signal that has at most k nonzeros in Fourier domain, is very different: all current techniques for sublinear time computation of Sparse FFT incur an exponential dependence on the dimension d in the runtime. In this paper we give the first algorithm that computes the DFT of a k-sparse signal in time poly(k, log N) in any dimension d, avoiding the curse of dimensionality inherent in all previously known techniques. Our main tool is a new class of filters that we refer to as adaptive aliasing filters: these filters allow isolating frequencies of a k-Fourier sparse signal using O(k) samples in time domain and O(k log N) runtime per frequency, in any dimension d.We also investigate natural average case models of the input signal: worst case support in Fourier domain with randomized values and random locations in Fourier domain with worst case signal values. Our techniques lead to an Õ(k2) time algorithm for the former and an Õ(k) time algorithm for the latter.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.168"}, {"primary_key": "3119032", "vector": [], "sparse_vector": [], "title": "Probabilistic Tensors and Opportunistic Boolean Matrix Multiplication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce probabilistic extensions of classical deterministic measures of algebraic complexity of a tensor, such as the rank and the border rank. We show that these probabilistic extensions satisfy various natural and algorithmically serendipitous properties, such as submultiplicativity under taking of Kronecker products. Furthermore, the probabilistic extensions enable improvements over their deterministic counterparts for specific tensors of interest, starting from the tensor 〈2, 2, 2〉 that represents 2 × 2 matrix multiplication. While it is well known that the (deterministic) tensor rank and border rank satisfy[<PERSON><PERSON>, Numer. Math. 13 (1969); <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON>, SIAM J. Appl. Math. 20 (1971); <PERSON><PERSON>, Linear Algebra Appl. 4 (1971); <PERSON><PERSON> <PERSON><PERSON>, <PERSON> (2006)], we show that the probabilistic tensor rank and border rank satisfyBy submultiplicativity, this leads immediately to novel randomized algorithm designs, such as algorithms for Boolean matrix multiplication as well as detecting and estimating the number of triangles in graphs.Our algorithms are opportunistic in the sense that their worst-case scaling is essentially governed by the probabilistic rank, yet their result is accumulated through independent repetitions, where the partial result can be inspected at each repeat for possible early termination, and each repeat scales according to the rank of the outcome-tensors. For example, representing 〈2, 2, 2〉 probabilistically using an ensemble of tensors of rank 6, we obtain an algorithm that, with high probability, multiplies two 2d × 2d Boolean matrices in operations. This algorithm consists of independent repeats that each run in O(6d) operations and enable inspection of the partial result at each repeat. Analogously, a probabilistic representation of 〈2, 2, 2〉 using tensors of border rank 5 gives an algorithm that runs in operations, consisting of repeats that run in Õ(5d) operations each.Asymptotically, we use Adleman's argument to show that, over the complex field, the support rank exponent ωs of matrix multiplication [H. Cohn and C. Umans, SODA'12] gives the lower bound for probabilistic tensor rank. While this enables an approach to obtaining asymptotically faster algorithm designs for matrix multiplication via the Cohn–Umans inequality , the main motivation for the present paper is to enable an approach towards fast practical algorithms using small probabilistic tensors.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.31"}, {"primary_key": "3119033", "vector": [], "sparse_vector": [], "title": "Optimal Construction of Compressed Indexes for Highly Repetitive Texts.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We propose algorithms that, given the input string of length $n$ over integer alphabet of size $\\sigma$, construct the Burrows-Wheeler transform (BWT), the permuted longest-common-prefix (PLCP) array, and the LZ77 parsing in $O(n/\\log_{\\sigma}n+r\\,{\\rm polylog}\\,n)$ time and working space, where $r$ is the number of runs in the BWT of the input. These are the essential components of many compressed indexes such as compressed suffix tree, FM-index, and grammar and LZ77-based indexes, but also find numerous applications in sequence analysis and data compression. The value of $r$ is a common measure of repetitiveness that is significantly smaller than $n$ if the string is highly repetitive. Since just accessing every symbol of the string requires $\\Omega(n/\\log_{\\sigma}n)$ time, the presented algorithms are time and space optimal for inputs satisfying the assumption $n/r\\in\\Omega({\\rm polylog}\\,n)$ on the repetitiveness. For such inputs our result improves upon the currently fastest general algorithms of Belazzougui (STOC 2014) and <PERSON> et al. (SODA 2017) which run in $O(n)$ time and use $O(n/\\log_{\\sigma} n)$ working space. We also show how to use our techniques to obtain optimal solutions on highly repetitive data for other fundamental string processing problems such as: Lyndon factorization, construction of run-length compressed suffix arrays, and some classical \"textbook\" problems such as computing the longest substring occurring at least some fixed number of times.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.82"}, {"primary_key": "3119034", "vector": [], "sparse_vector": [], "title": "The Andoni-Krauthgamer-<PERSON><PERSON><PERSON>yn characterization of sketchable norms fails for sketchable metrics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (AKR) proved (STOC'15) that a finite-dimensional normed space (X, ‖·‖x) admits a O(1) sketching algorithm (namely, with O(1) sketch size and O(1) approximation) if and only if for every ε ∊ (0, 1) there exist α  1 and an embedding f : X → ℓ1–ε such that ‖x – y‖x  ‖f(x) – f(y)‖1–ε  α‖x – y‖x for all x, y ∊ X. The \"if part\" of this theorem follows from a sketching algorithm of <PERSON><PERSON> (FOCS 2000). The contribution of AKR is therefore to demonstrate that the mere availability of a sketching algorithm implies the existence of the aforementioned geometric realization. <PERSON><PERSON>'s algorithm shows that the \"if part\" of the AKR characterization holds true for any metric space whatsoever, i.e., the existence of an embedding as above implies sketchability even when X is not a normed space. Due to this, a natural question that AKR posed was whether the assumption that the underlying space is a normed space is needed for their characterization of sketchability. We resolve this question by proving that for arbitrarily large n ∊ ℕ there is an n-point metric space (M(n), dM(n)) which is O(1)-sketchable yet for every ε ∊ (0, ψ), if α(n)  1 and fn : M(n) → ℓ1–ε are such that dM(n)(x, y)  ‖fn(x) – fn(y)‖1–ε  α(n)dM(n)(x, y) for all x, y ∊ M(n), then necessarily limn→∞ α(n) = ∞.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.109"}, {"primary_key": "3119035", "vector": [], "sparse_vector": [], "title": "Nearly ETH-tight algorithms for Planar Steiner Tree with Terminals on Few Faces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Steiner Tree problem is one of the most fundamental NP-complete problems as it models many network design problems. Recall that an instance of this problem consists of a graph with edge weights, and a subset of vertices (often called terminals); the goal is to find a subtree of the graph of minimum total weight that connects all terminals. A seminal paper by <PERSON><PERSON><PERSON> et al. [Math. Oper. Res., 1987] considers instances where the underlying graph is planar and all terminals can be covered by the boundary of k faces. <PERSON><PERSON><PERSON> et al. show that the problem can be solved by an algorithm using nO(k) time and nO(k) space, where n denotes the number of vertices of the input graph. In the past 30 years there has been no significant improvement of this algorithm, despite several efforts.In this work, we give an algorithm for Planar Steiner Tree with running time using only polynomial space. Furthermore, we show the running time of our algorithm is almost tight: we prove that there is no algorithm for Planar Steiner Tree for any computable function f, unless the Exponential Time Hypothesis fails.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.63"}, {"primary_key": "3119036", "vector": [], "sparse_vector": [], "title": "Computing all Wardrop Equilibria parametrized by the Flow Demand.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We develop an algorithm that computes for a given undirected or directed network with flow-dependent piece-wise linear edge cost functions all Wardrop equilibria as a function of the flow demand. Our algorithm is based on <PERSON><PERSON><PERSON>'s homotopy method for electrical networks. The algorithm uses a bijection between vertex potentials and flow excess vectors that is piecewise linear in the potential space and where each linear segment can be interpreted as an augmenting flow in a residual network. The algorithm iteratively increases the excess of one or more vertex pairs until the bijection reaches a point of non-differentiability. Then, the next linear region is chosen in a simplex-like pivot step and the algorithm proceeds. We first show that this algorithm correctly computes all Wardrop equilibria in undirected single-commodity networks along the chosen path of excess vectors. We then adapt our algorithm to also work for discontinuous cost functions which allows to model directed edges and/or edge capacities. Our algorithm is output-polynomial in non-degenerate instances where the solution curve never hits a point where the cost function of more than one edge becomes non-differentiable. For degenerate instances we still obtain an output-polynomial algorithm computing the linear segments of the bijection by a convex program. The latter technique also allows to handle multiple commodities.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.56"}, {"primary_key": "3119038", "vector": [], "sparse_vector": [], "title": "Flow-Cut Gaps and Face Covers in Planar Graphs.", "authors": ["<PERSON>", "<PERSON>", "Havana Rika"], "summary": "The relationship between the sparsest cut and the maximum concurrent multi-flow in graphs has been studied extensively. For general graphs, the worst-case gap between these two quantities is now settled: When there are k terminal pairs, the flow-cut gap is O(log k), and this is tight. But when topological restrictions are placed on the flow network, the situation is far less clear. In particular, it has been conjectured that the flow-cut gap in planar networks is O(1), while the known bounds place the gap somewhere between 2 (<PERSON> and <PERSON>, 2003) and (<PERSON>, 1999).A seminal result of <PERSON><PERSON><PERSON> and <PERSON> (1981) shows that when all the terminals of a planar network lie on a single face, the flow-cut gap is exactly 1. This setting can be generalized by considering planar networks where the terminals lie on one of γ > 1 faces in some fixed planar drawing. <PERSON> and <PERSON> (2009) proved that the flow-cut gap is bounded by a function of γ, and <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (2013) showed that the gap is at most 3γ. We significantly improve these asymptotics by establishing that the flow-cut gap is O(log γ). This is achieved by showing that the edge-weighted shortest-path metric induced on the terminals admits a stochastic embedding into trees with distortion O(log γ). The latter result is tight, e.g., for a square planar lattice on Θ(γ) vertices.The preceding results refer to the setting of edge-capacitated networks. For vertex-capacitated networks, it can be significantly more challenging to control flow-cut gaps. While there is no exact vertex-capacitated version of the Ok<PERSON>ura-Seymour <PERSON>rem, an approximate version holds; <PERSON>, <PERSON>del, and <PERSON><PERSON>rami (2015) showed that the vertex-capacitated flow-cut gap is O(1) on planar networks whose terminals lie on a single face. We prove that the flow-cut gap is O(γ) for vertex-capacitated instances when the terminals lie on at most γ faces. In fact, this result holds in the more general setting of submodular vertex capacities.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.33"}, {"primary_key": "3119039", "vector": [], "sparse_vector": [], "title": "Efficiently Approximating Edit Distance Between Pseudorandom Strings.", "authors": ["<PERSON>"], "summary": "We present an algorithm for approximating the edit distance ed(x, y) between two strings x and y in time parameterized by the degree to which one of the strings x satisfies a natural pseudorandomness property. The pseudorandomness model is asymmetric in that no requirements are placed on the second string y, which may be constructed by an adversary with full knowledge of x.We say that x is (p, B)-pseudorandom if all pairs a and b of disjoint B-letter substrings of x satisfy ed(a, b) ≥ pB. Given parameters p and B, our algorithm computes the edit distance between a (p, B)-pseudorandom string x and an arbitrary string y within a factor of O(1/p) in time Õ(nB), with high probability. If x is generated at random, then with high probability it will be (Ω(1), O(log n))-pseudorandom, allowing us to compute ed(x, y) within a constant factor in near linear time. For strings x of varying degrees of pseudorandomness, our algorithm offers a continuum of runtimes.Our algorithm is robust in the sense that it can handle a small portion of x being adversarial (i.e., not satisfying the pseudorandomness property). In this case, the algorithm incurs an additive approximation error proportional to the fraction of x which behaves maliciously.The asymmetry of our pseudorandomness model has particular appeal for the case where x is a source string, meaning that ed(x, y) will be computed for many strings y. Suppose that one wishes to achieve an O(α)-approximation for each ed(x, y) computation, and that B is the smallest block-size for which the string x is (1/α, B)-pseudorandom. We show that without knowing B beforehand, x may be preprocessed in time , so that all future computations of the form ed(x, y) may be O(α)-approximated in time Õ(nB). Furthermore, for the special case where only a single ed(x, y) computation will be performed, we show how to achieve an O(α)-approximation in time Õ(n4/3 B2/3).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.71"}, {"primary_key": "3119040", "vector": [], "sparse_vector": [], "title": "A Faster Algorithm for Minimum-Cost Bipartite Matching in Minor-Free Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give an Õ(n7/5 log(nC)) time1 algorithm to compute a minimum-cost maximum cardinality matching (optimal matching) in Kh-minor free graphs with h = O(1) and integer edge weights having magnitude at most C. This improves upon the Õ(n10/7 log C) algorithm of <PERSON> et al‥ [SODA 2017] and the O(n3/2 log(nC)) algorithm of <PERSON><PERSON><PERSON> and <PERSON>jan [SIAM J. Co<PERSON>. 1989]. For a graph with m edges and n vertices, the well-known Hungarian Algorithm computes a shortest augmenting path in each phase in O(m) time, yielding an optimal matching in O(mn) time. The Gabow-Tarjan [SIAM J. Comput. 1989] algorithm computes, in each phase, a maximal set of vertex-disjoint shortest augmenting paths (for appropriately defined costs) in O(m) time. This reduces the number of phases from n to and the total execution time to . To obtain our speed-up, we relax the conditions on the augmenting paths and iteratively compute, in each phase, a set of carefully selected augmenting paths that are not restricted to be shortest or vertex-disjoint. As a result, our algorithm computes substantially more augmenting paths in each phase, reducing the number of phases from to O(n2/5). By using small vertex separators, the execution of each phase takes Õ(m) time on average. For planar graphs, we combine our algorithm with efficient shortest path data structures to obtain a minimum-cost perfect matching in Õ(n6/5 log (nC)) time. This improves upon the recent Õ(n4/3 log (nC)) time algorithm by Asathulla et al. [SODA 2018].", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.36"}, {"primary_key": "3119041", "vector": [], "sparse_vector": [], "title": "A (1 + 1/e)-Approximation Algorithm for Maximum Stable Matching with One-Sided Ties and Incomplete Lists.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of finding large weakly stable matchings when preference lists are incomplete and contain one-sided ties. Computing maximum weakly stable matchings is known to be NP-hard. We present a polynomial-time algorithm that achieves an improved approximation ratio of 1 + 1/e. Like a number of existing approximation algorithms for this problem, our algorithm is based on a proposal process in which numerical priorities are adjusted according to the solution of a linear program, and are used for tiebreaking purposes. Our main idea is to use an infinitesimally small step size for incrementing the priorities. Our analysis involves solving an infinite-dimensional factor-revealing linear program. We also show that the ratio 1 + 1/e is an upper bound for the integrality gap, which matches the known lower bound.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.175"}, {"primary_key": "3119042", "vector": [], "sparse_vector": [], "title": "A New Path from Splay to Dynamic Optimality.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Consider the task of performing a sequence of searches in a binary search tree. After each search, an algorithm is allowed to arbitrarily restructure the tree, at a cost proportional to the amount of restructuring performed. The cost of an execution is the sum of the time spent searching and the time spent optimizing those searches with restructuring operations. This notion was introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> in 1985 [27], along with an algorithm and a conjecture. The algorithm, Splay, is an elegant procedure for performing adjustments while moving searched items to the top of the tree. The conjecture, called dynamic optimality, is that the cost of splaying is always within a constant factor of the optimal algorithm for performing searches. The conjecture stands to this day.We offer the first systematic proposal for settling the dynamic optimality conjecture. At the heart of our methods is what we term a simulation embedding: a mapping from executions to lists of keys that induces a target algorithm to simulate the execution. We build a simulation embedding for Splay by inducing it to perform arbitrary subtree transformations, and use this to show that if the cost of splaying a sequence of items is an upper bound on the cost of splaying every subsequence thereof, then Splay is dynamically optimal. We call this the subsequence property. Building on this machinery, we show that if Splay is dynamically optimal, then with respect to optimal costs, its additive overhead is at most linear in the sum of initial tree size and number of requests. As a corollary, the subsequence property is also a necessary condition for dynamic optimality. The subsequence property also implies both the traversal [27] and deque [30] conjectures.The notions of simulation embeddings and bounding additive overheads should be of general interest in competitive analysis. For readers especially interested in dynamic optimality, we provide an outline of a proof that a lower bound on search costs by Wilber [32] has the subsequence property, and extensive suggestions for adapting this proof to Splay.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.80"}, {"primary_key": "3119043", "vector": [], "sparse_vector": [], "title": "On Facility Location with General Lower Bounds.", "authors": ["<PERSON>"], "summary": "In this paper, we give the first constant approximation algorithm for the lower bounded facility location (LBFL) problem with general lower bounds. Prior to our work, such algorithms were only known for the special case where all facilities have the same lower bound: <PERSON><PERSON><PERSON><PERSON><PERSON> [27] gave a 448-approximation for the special case, and subsequently <PERSON><PERSON> and <PERSON><PERSON> [2] improved the approximation factor to 82.6.As in [27] and [2], our algorithm for LBFL with general lower bounds works by reducing the problem to the capacitated facility location (CFL) problem. To handle the challenges raised by the general lower bounds, it involves more reduction steps. One main complication is that after aggregating the clients and facilities at a few locations, each of these locations may contain many facilities with different opening costs and lower bounds. To address this issue, we introduce and reduce the LBFL problem to two intermediate problems called the LBFL with penalty (LBFL-P) and the transportation with configurable supplies and demands (TCSD) problems, which in turn can be reduced to the CFL problem.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.138"}, {"primary_key": "3119044", "vector": [], "sparse_vector": [], "title": "Can We Overcome the n log n Barrier for Oblivious Sorting?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Tiancheng Xie"], "summary": "It is well-known that non-comparison-based techniques can allow us to sort n elements in o(n log n) time on a Random-Access Machine (RAM). On the other hand, it is a long-standing open question whether (non-comparison-based) circuits can sort n elements from the domain [1‥2k] with o(kn log n) boolean gates. We consider weakened forms of this question: first, we consider a restricted class of sorting where the number of distinct keys is much smaller than the input length; and second, we explore Oblivious RAMs and probabilistic circuit families, i.e., computational models that are somewhat more powerful than circuits but much weaker than RAM. We show that Oblivious RAMs and probabilistic circuit families can sort o(log n)-bit keys in o(n log n) time or o(kn log n) circuit complexity. Our algorithms work in the indivisible model, i.e., not only can they sort an array of numerical keys — if each key additionally carries an opaque ball, our algorithms can also move the balls into the correct order. We further show that in such an indivisible model, it is impossible to sort Ω(log n)-bit keys in o(n log n) time, and thus the o(log n)-bit-key assumption is necessary for overcoming the n log n barrier.Finally, after optimizing the IO efficiency, we show that even the 1-bit special case can solve open questions: our oblivious algorithms solve tight compaction and selection with optimal IO efficiency for the first time.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.148"}, {"primary_key": "3119045", "vector": [], "sparse_vector": [], "title": "Short Cycles via Low-Diameter Decompositions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present improved algorithms for short cycle decomposition of a graph – a decomposition of an undirected, unweighted graph into edge-disjoint cycles, plus a small number of additional edges. Short cycle decompositions were introduced in the recent work of <PERSON> et al. (FOCS 2018), and were used to make progress on several questions in graph sparsification.For all constants δ ∊ (0, 1], we give an O(mnδ) time algorithm that, given a graph G, partitions its edges into cycles of length , with O(n) extra edges not in any cycle. This gives the first subquadratic, in fact almost linear time, algorithm achieving polylogarithmic cycle lengths. We also give an m · time algorithm that partitions the edges of a graph into cycles of length , with O(n) extra edges not in any cycle. This improves on the short cycle decomposition algorithms given by <PERSON> et al. in terms of all parameters, and is significantly simpler.As a result, we obtain faster algorithms and improved guarantees for several problems in graph sparsification – construction of resistance sparsifiers, graphical spectral sketches, degree preserving sparsifiers, and approximating the effective resistances of all edges.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.161"}, {"primary_key": "3119049", "vector": [], "sparse_vector": [], "title": "The Complexity of the Ideal Membership Problem for Constrained Problems Over the Boolean Domain.", "authors": ["<PERSON><PERSON>"], "summary": "Given an ideal I and a polynomial f the Ideal Membership Problem is to test if f ∊ I. This problem is a fundamental algorithmic problem with important applications and notoriously intractable.We study the complexity of the Ideal Membership Problem for combinatorial ideals that arise from constrained problems over the Boolean domain. As our main result, we identify the precise borderline of tractability. By using <PERSON><PERSON><PERSON><PERSON> bases techniques, we generalize <PERSON><PERSON><PERSON><PERSON>'s dichotomy theorem [<PERSON><PERSON>, 1978] which classifies all Constraint Satisfaction Problems over the Boolean domain to be either in P or NP-hard.This paper is motivated by the pursuit of understanding the recently raised issue of bit complexity of Sum-of-Squares proofs [<PERSON><PERSON><PERSON>, ITCS, 2017]. <PERSON><PERSON><PERSON><PERSON> and <PERSON> [ICALP, 2017] show how the Ideal Membership Problem tractability for combinatorial ideals implies bounded coefficients in Sum-of-Squares proofs.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.29"}, {"primary_key": "3119050", "vector": [], "sparse_vector": [], "title": "Stochastic ℓp Load Balancing and Moment Problems via the L-Function Method.", "authors": ["<PERSON>"], "summary": "This paper considers stochastic optimization problems whose objective functions involve powers of random variables. For a concrete example, consider the classic Stochastic ℓp Load Balancing Problem (StochLoadBalp): There are m machines and n jobs, and we are given independent random variables Yij describing the distribution of the load incurred on machine i if we assign job j to it. The goal is to assign each job to the machines in order to minimize the expected ℓp-norm of the total load incurred over the machines. That is, letting Ji denote the jobs assigned to machine i, we want to minimize . While convex relaxations represent one of the most powerful algorithmic tools, in problems such as StochLoadBalp the main difficulty is to capture such objective function in a way that only depends on each random variable separately.In this paper, show how to capture p-power-type objectives in such separable way by using the L-function method. This method was precisely introduced by <PERSON><PERSON><PERSON> to capture in a sharp way the moment of sums of random variables through the individual marginals. We first show how this quickly leads to a constant-factor approximation for very general subset selection problem with p-moment objective.Moreover, we give a constant-factor approximation for StochLoadBalp, improving on the recent O(p/ ln p)-approximation of [<PERSON> et al., SODA 18]. Here the application of the method is much more involved. In particular, we need to prove structural results connecting the expected ℓp-norm of a random vector with the p-moments of its coordinate-marginals (machine loads) in a sharp way, taking into account simultaneously the different scales of the loads that are incurred in the different machines by an unknown assignment. Moreover, our starting convex (indeed linear) relaxation has exponentially many constraints that are not conducive to integral rounding; we need to use the solution of this LP to obtain a reduced LP which can then be used to obtain the desired assignment.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.22"}, {"primary_key": "3119051", "vector": [], "sparse_vector": [], "title": "Seeded Graph Matching via Large Neighborhood Statistics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study a well known noisy model of the graph isomorphism problem. In this model, the goal is to perfectly recover the vertex correspondence between two edge-correlated graphs, with an initial seed set of correctly matched vertex pairs revealed as side information. Specifically, the model first generates a parent graph G0 from Erdős-Rényi random graph G(n, p) and then obtains two children graphs G1 and G2 by subsampling the edge set of G0 twice independently with probability s = Θ(1). The vertex correspondence between G1 and G2 is obscured by randomly permuting the vertex labels of G1 according to a latent permutation π*. Finally, for each i, π* (i) is revealed independently with probability α as seeds.In the sparse graph regime where np ≤ n∊ for any ∊ < 1/6, we give a polynomial-time algorithm which perfectly recovers π*, provided that nps2 – log n → +∞ and α ≥ n−1+3∊. This further leads to a subexponential-time, exp (nO(∊)), matching algorithm even without seeds. On the contrary, if nps2 – log n = O(1), then perfect recovery is information-theoretically impossible as long as α is bounded away from 1.In the dense graph regime, where np = bna, for fixed constants a, b ∊ (0, 1], we give a polynomial-time algorithm which succeeds when b = O(s) and a = Ω ((np)−[1/α] log n). In particular, when a = 1/k for an integer k ≥ 1, α = Ω(log n/n) suffices, yielding a quasi-polynomial-time nO(log n) algorithm matching the best known algorithm by Barak et al. for the problem of graph matching without seeds when k ≥ 153 and extending their result to new values of p for k = 2, …, 152.Unlike previous work on graph matching, which used small neighborhoods or small subgraphs with a logarithmic number of vertices in order to match vertices, our algorithms match vertices if their large neighborhoods have a significant overlap in the number of seeds.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.62"}, {"primary_key": "3119052", "vector": [], "sparse_vector": [], "title": "A Subquadratic Approximation Scheme for Partition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The subject of this paper is the time complexity of approximating Knapsack, Subset Sum, Partition, and some other related problems. The main result is an $\\widetilde{O}(n+1/\\varepsilon^{5/3})$ time randomized FPTAS for Partition, which is derived from a certain relaxed form of a randomized FPTAS for Subset Sum. To the best of our knowledge, this is the first NP-hard problem that has been shown to admit a subquadratic time approximation scheme, i.e., one with time complexity of $O((n+1/\\varepsilon)^{2-\\delta})$ for some $\\delta>0$. To put these developments in context, note that a quadratic FPTAS for \\partition has been known for 40 years. Our main contribution lies in designing a mechanism that reduces an instance of Subset Sum to several simpler instances, each with some special structure, and keeps track of interactions between them. This allows us to combine techniques from approximation algorithms, pseudo-polynomial algorithms, and additive combinatorics. We also prove several related results. Notably, we improve approximation schemes for 3SUM, (min,+)-convolution, and Tree Sparsity. Finally, we argue why breaking the quadratic barrier for approximate Knapsack is unlikely by giving an $\\Omega((n+1/\\varepsilon)^{2-o(1)})$ conditional lower bound.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.5"}, {"primary_key": "3119053", "vector": [], "sparse_vector": [], "title": "A New Dynamic Programming Approach for Spanning Trees with Chain Constraints and Beyond.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Short spanning trees subject to additional constraints are important building blocks in various approximation algorithms, and, moreover, they capture interesting problem settings on their own. Especially in the context of the Traveling Salesman Problem (TSP), new techniques for finding spanning trees with well-defined properties have been crucial in recent progress. We consider the problem of finding a spanning tree subject to constraints on the edges in a family of cuts forming a laminar family of small width. Our main contribution is a new dynamic programming approach where the value of a table entry does not only depend on the values of previous table entries, as it is usually the case, but also on a specific representative solution saved together with each table entry. This allows for handling a broad range of constraint types.In combination with other techniques—including negatively correlated rounding and a polyhedral approach that, in the problems we consider, allows for avoiding potential losses in the objective through the randomized rounding—we obtain several new results. We first present a quasi-polynomial time algorithm for the Minimum Chain-Constrained Spanning Tree Problem with an essentially optimal guarantee. More precisely, each chain constraint is violated by a factor of at most 1 + ε, and the cost is no larger than that of an optimal solution not violating any chain constraint. The best previous procedure is a bicriteria approximation violating each chain constraint by up to a constant factor and losing another factor in the objective. Moreover, our approach can naturally handle lower bounds on the chain constraints, and it can be extended to constraints on cuts forming a laminar family of constant width.Furthermore, we show how our approach can also handle parity constraints as used in the context of (path) TSP and a generalization thereof, and discuss implications in this context.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.94"}, {"primary_key": "3119054", "vector": [], "sparse_vector": [], "title": "Viewing the Rings of a Tree: Minimum Distortion Embeddings into Trees.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We describe a (1 + ε) approximation algorithm for finding the minimum distortion embedding of an n-point metric space, (X, dX), into a tree with vertex set X. The running time of our algorithm is n2 · (Δ/ε)(O(δopt/ε))2λ+1 parameterized with respect to the spread of X, denoted by Δ, the minimum possible distortion for embedding X into any tree, denoted by δopt, and the doubling dimension of X, denoted by λ. Hence we obtain a PTAS, provided δopt is a constant and X is a finite doubling metric space with polynomially bounded spread, for example, a point set with polynomially bounded spread in constant dimensional Euclidean space. Our algorithm implies a constant factor approximation with the same running time when Steiner vertices are allowed.Moreover, we describe a similar (1 + ε) approximation algorithm for finding a tree spanner of (X, dX) that minimizes the maximum stretch. The running time of our algorithm stays the same, except that δopt must be interpreted as the minimum stretch of any spanning tree of X. Finally, we generalize our tree spanner algorithm to a (1 + ε) approximation algorithm for computing a minimum stretch tree spanner of a weighted graph, where the running time is parameterized with respect to the maximum degree, in addition to the other parameters above. In particular, we obtain a PTAS for computing minimum stretch tree spanners of weighted graphs, with polynomially bounded spread, constant doubling dimension, and constant maximum degree, when a tree spanner with constant stretch exists.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.146"}, {"primary_key": "3119055", "vector": [], "sparse_vector": [], "title": "Optimal Lower Bounds for Distributed and Streaming Spanning Forest Computation.", "authors": ["<PERSON><PERSON>", "Huacheng Yu"], "summary": "We show optimal lower bounds for spanning forest computation in two different models:•One wants a data structure for fully dynamic spanning forest in which updates can insert or delete edges amongst a base set of n vertices. The sole allowed query asks for a spanning forest, which the data structure should successfully answer with some given (potentially small) constant probability ∊ > 0. We prove that any such data structure must use Ω(n log3 n) bits of memory.•There is a referee and n vertices in a network sharing public randomness, and each vertex knows only its neighborhood; the referee receives no input. The vertices each send a message to the referee who then computes a spanning forest of the graph with constant probability ∊ > 0. We prove the average message length must be Ω(log3 n) bits.Both our lower bounds are optimal, with matching upper bounds provided by the AGM sketch [AGM12] (which even succeeds with probability 1 – 1/poly(n)). Furthermore, for the first setting we show optimal lower bounds even for low failure probability δ, as long as δ > 2−n1−∊.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.111"}, {"primary_key": "3119056", "vector": [], "sparse_vector": [], "title": "Fast greedy for linear matroids.", "authors": ["<PERSON><PERSON>"], "summary": "A fundamental algorithmic result for matroids is that the maximum weight base can be computed using the greedy algorithm. For explicitly represented matroids an important question is the time complexity of computing such a base. It is known that one can compute it in time almost linear in the number of non-zero entries of the linear representation plus rω, where r is the rank of the matroid and ω is the matrix multiplication exponent. In this work, we give an alternative algorithm for the same task.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.32"}, {"primary_key": "3119057", "vector": [], "sparse_vector": [], "title": "Proportional Volume Sampling and Approximation Algorithms for A-Optimal Design.", "authors": ["Aleksan<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the A-optimal design problem where we are given vectors υ1, …, υn ∊ ℝd, an integer k ≥ d, and the goal is to select a set S of k vectors that minimizes the trace of (∑i∊Svivi⊺)−1. Traditionally, the problem is an instance of optimal design of experiments in statistics [35] where each vector corresponds to a linear measurement of an unknown vector and the goal is to pick k of them that minimize the average variance of the error in the maximum likelihood estimate of the vector being measured. The problem also finds applications in sensor placement in wireless networks [22], sparse least squares regression [8], feature selection for k-means clustering [9], and matrix approximation [13, 14, 5]. In this paper, we introduce proportional volume sampling to obtain improved approximation algorithms for A-optimal design.Given a matrix, proportional volume sampling involves picking a set of columns S of size k with probability proportional to µ(S) times det(∑i∊Svivi⊺) for some measure µ. Our main result is to show the approximability of the A-optimal design problem can be reduced to approximate independence properties of the measure µ. We appeal to hardcore distributions as candidate distributions µ that allow us to obtain improved approximation algorithms for the A-optimal design. Our results include a d-approximation when k = d, an (1 + ∊)-approximation when and -approximation when repetitions of vectors are allowed in the solution. We also consider generalization of the problem for k ≤ d and obtain a k-approximation.We also show that the proportional volume sampling algorithm gives approximation algorithms for other optimal design objectives (such as D-optimal design [36] and generalized ratio objective [27]) matching or improving previous best known results. Interestingly, we show that a similar guarantee cannot be obtained for the E-optimal design problem. We also show that the A-optimal design problem is NP-hard to approximate within a fixed constant when k = d.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.84"}, {"primary_key": "3119058", "vector": [], "sparse_vector": [], "title": "Optimal Algorithm for Geodesic Nearest-point Voronoi Diagrams in Simple Polygons.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Given a set of m point sites in a simple polygon, the geodesic nearest-point Voronoi diagram of the sites partitions the polygon into m Voronoi cells, one cell per site, such that every point in a cell has the same nearest site under the geodesic metric. In this paper, we present an O(n + m log m)-time algorithm for computing the geodesic nearest-point Voronoi diagram of m points in a simple n-gon. This matches the best known lower bound of Ω(n + m log m) as well as improving the previously best known algorithms which take time O(n + m log m + m log2 n) and O(n log n + m log m). This answers the longstanding question whether the geodesic nearest-point Voronoi diagram can be computed optimally, which was explicitly posed by <PERSON><PERSON><PERSON> [Algorithmica, 1989] and <PERSON> [Handbook of Computational Geometry, 2000].", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.25"}, {"primary_key": "3119059", "vector": [], "sparse_vector": [], "title": "Contraction Decomposition in Unit Disk Graphs and Algorithmic Applications in Parameterized Complexity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a new decomposition theorem in unit disk graphs (UDGs) and demonstrate its applicability in the fields of Structural Graph Theory and Parameterized Complexity. First, our new decomposition theorem shows that the class of UDGs admits a Contraction Decomposition Theorem. Prior studies on this topic exhibited that the classes of planar graphs [<PERSON>, SICOMP, 2008], graphs of bounded genus [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>, Combinatorica 2010] and H-minor free graphs [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, STOC 2011] admit a Contraction Decomposition Theorem. Even bounded-degree UDGs can contain arbitrarily large cliques as minors, therefore our result is a significant advance in the study of contraction decompositions. Additionally, this result answers an open question posed by <PERSON><PERSON><PERSON><PERSON><PERSON> (www.youtube.com/watch?v=2Bq2gy1N01w) regarding the existence of contraction decompositions for classes of graphs beyond H-minor free graphs.Second, we present a \"parameteric version\" of our new decomposition theorem. We prove that there is an algorithm that given a UDG G and a positive integer k, runs in polynomial time and outputs a collection of O(k) tree decompositions of G with the following properties. Each bag in any of these tree decompositions can be partitioned into O(k) connected pieces (we call this measure the chunkiness of the tree decomposition). Moreover, for any subset S of at most k edges in G, there is a tree decomposition in the collection such that S is well preserved in the decomposition in the following sense. For any bag in the tree decomposition and any edge in S with both endpoints in the bag, either its endpoints lie in different pieces or they lie in a piece which is a clique. Having this decomposition at hand, we show that the design of parameterized algorithms for some cut problems becomes elementary. In particular, our algorithmic applications include single-exponential (or slightly superexponential) algorithms for well-studied problems such as Min Bisection, Steiner Cut, s-Way Cut, and Edge Multiway Cut-Uncut on UDGs; these algorithms are substantially faster than the best known algorithms for these problems on general graphs.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.64"}, {"primary_key": "3119060", "vector": [], "sparse_vector": [], "title": "Low Congestion Cycle Covers and Their Applications.", "authors": ["<PERSON><PERSON><PERSON>", "E<PERSON>"], "summary": "A cycle cover of a bridgeless graph G is a collection of simple cycles in G such that each edge e appears on at least one cycle. The common objective in cycle cover computation is to minimize the total lengths of all cycles. Motivated by applications to distributed computation, we introduce the notion of low-congestion cycle covers, in which all cycles in the cycle collection are both short and nearly edge-disjoint. Formally, a (d, c)-cycle cover of a graph G is a collection of cycles in G in which each cycle is of length at most d and each edge participates in at least one cycle and at most c cycles.A-priori, it is not clear that cycle covers that enjoy both a small overlap and a short cycle length even exist, nor if it is possible to efficiently find them. Perhaps quite surprisingly, we prove the following: Every bridgeless graph of diameter D admits a (d, c)-cycle cover where d = Õ(D) and c = Õ(1). That is, the edges of G can be covered by cycles such that each cycle is of length at most Õ(D) and each edge participates in at most Õ(1) cycles. These parameters are existentially tight up to polylogarithmic terms.Furthermore, we show how to extend our result to achieve universally optimal cycle covers. Let Ce is the shortest cycle that covers e, and let OPT(G) = maxe∊G |Ce|. We show that every bridgeless graph admits a (d, c)-cycle cover where d = Õ(OPT(G)) and c = Õ(1).We demonstrate the usefulness of low congestion cycle covers in different settings of resilient computation. For instance, we consider a Byzantine fault model where in each round, the adversary chooses a single message and corrupt in an arbitrarily manner. We provide a compiler that turns any r-round distributed algorithm for a graph G with diameter D, into an equivalent fault tolerant algorithm with r·poly(D) rounds.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.101"}, {"primary_key": "3119061", "vector": [], "sparse_vector": [], "title": "Distributed Algorithms Made Secure: A Graph Theoretic Approach.", "authors": ["<PERSON><PERSON><PERSON>", "E<PERSON>"], "summary": "In the area of distributed graph algorithms a number of network's entities with local views solve some computational task by exchanging messages with their neighbors. Quite unfortunately, an inherent property of most existing distributed algorithms is that throughout the course of their execution, the nodes get to learn not only their own output but rather learn quite a lot on the inputs or outputs of many other entities. This leakage of information might be a major obstacle in settings where the output (or input) of network's individual is a private information (e.g., distributed networks of selfish agents, decentralized digital currency such as Bitcoin).While being quite an unfamiliar notion in the classical distributed setting, the notion of secure multi-party computation (MPC) is one of the main themes in the Cryptographic community. The existing secure MPC protocols do not quite fit the framework of classical distributed models in which only messages of bounded size are sent on graph edges in each round. In this paper, we introduce a new framework for secure distributed graph algorithms and provide the first general compiler that takes any \"natural\" non-secure distributed algorithm that runs in r rounds, and turns it into a secure algorithm that runs in Õ(r · D · poly(Δ)) rounds where Δ is the maximum degree in the graph and D is its diameter. A \"natural\" distributed algorithm is one where the local computation at each node can be performed in polynomial time. An interesting advantage of our approach is that it allows one to decouple between the price of locality and the price of security of a given graph function f. The security of the compiled algorithm is information-theoretic but holds only against a semi-honest adversary that controls a single node in the network.This compiler is made possible due to a new combinatorial structure called private neighborhood trees: a collection of n trees T(u1), …, T(un), one for each vertex ui ∊ V(G), such that each tree T(ui) spans the neighbors of ui without going through ui. Intuitively, each tree T(ui) allows all neighbors of ui to exchange a secret that is hidden from ui, which is the basic graph infrastructure of the compiler. In a (d, c)-private neighborhood trees each tree T(ui) has depth at most d and each edge e ∊ G appears in at most c different trees. We show a construction of private neighborhood trees with d = Õ(Δ · D) and c = Õ(D), both these bounds are existentially optimal.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.102"}, {"primary_key": "3119062", "vector": [], "sparse_vector": [], "title": "Polynomial bounds for centered colorings on proper minor-closed graph classes.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "For p ∊ ℕ, a coloring λ of the vertices of a graph G is p-centered if for every connected subgraph H of G, either H receives more than p colors under λ or there is a color that appears exactly once in H. Centered colorings play an important role in the theory of sparse graphs introduced by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [27], as they structurally characterize classes of bounded expansion, one of the key notions in this theory. More precisely, a class of graphs ℒ has bounded expansion if and only if there is a function f : ℕ → ℕ such that every graph G ∊ ℒ for every p ∊ ℕ admits a p-centered coloring with at most f(p) colors. Unfortunately, known proofs of the existence of such colorings yield large upper bounds on the function f governing the number of colors needed, even for as simple classes as planar graphs.We prove that every Kt-minor-free graph admits a p-centered coloring with O(pg(t)) colors for some function g. In the special case that the graph is embeddable in a fixed surface Σ we show that it admits a p-centered coloring with O(p19) colors, with the degree of the polynomial independent of the genus of Σ. This provides the first polynomial upper bounds on the number of colors needed in p-centered colorings of graphs drawn from proper minor-closed classes, which answers an open problem posed by <PERSON><PERSON><PERSON><PERSON> [1].As an algorithmic application, we use our main result to prove that if ℒ is a fixed proper minor-closed class of graphs, then given graphs H and G, on p and n vertices, respectively, where G ∊ ℒ, it can be decided whether H is a subgraph of G in time 2O(p log p). nO(1) and space nO(1).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.91"}, {"primary_key": "3119063", "vector": [], "sparse_vector": [], "title": "Communication Complexity of Discrete Fair Division.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We initiate the study of the communication complexity of fair division with indivisible goods. We focus on some of the most well-studied fairness notions (envy-freeness, proportionality, and approximations thereof) and valuation classes (submodular, subadditive and unrestricted). Within these parameters, our results completely resolve whether the communication complexity of computing a fair allocation (or determining that none exist) is polynomial or exponential (in the number of goods), for every combination of fairness notion, valuation class, and number of players, for both deterministic and randomized protocols.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.122"}, {"primary_key": "3119065", "vector": [], "sparse_vector": [], "title": "Exponential Lower Bounds on Spectrahedral Representations of Hyperbolicity Cones.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Hyperbolic programming is a generalization of semidefinite programming in which one optimizes over linear sections of hyperbolicity cones rather than semidefinite ones. It is not known whether this generalization is strict: the Generalized Lax Conjecture asks whether every hyperbolicity cone is a section of a semidefinite cone of sufficiently high dimension. We study a quantitative version of this question, and prove that the space of hyperbolicity cones of hyperbolic polynomials of degree d in n variables contains (n/d)Ω(d) pairwise distant cones in the Hausdorff metric, implying that any semidefinite representation of such cones must have dimension at least (n/d)Ω(d) (even allowing a small approximation error). The cones are perturbations of the hyperbolicity cones of elementary symmetric polynomials. Our proof contains several ingredients of independent interest, including the identification of a large subspace in which the elementary symmetric polynomials lie in the relative interior of the set of hyperbolic polynomials, and a quantitative generalization of the fact that a real-rooted polynomial with two consecutive zero coefficients must have a high multiplicity root at zero.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.141"}, {"primary_key": "3119066", "vector": [], "sparse_vector": [], "title": "An Optimal Truthful Mechanism for the Online Weighted Bipartite Matching Problem.", "authors": ["<PERSON>"], "summary": "In the weighted bipartite matching problem, the goal is to find a maximum-weight matching in a bipartite graph with nonnegative edge weights. We consider its online version where the first vertex set is known beforehand, but vertices of the second set appear one after another. Vertices of the first set are interpreted as items, and those of the second set as bidders. On arrival, each bidder vertex reveals the weights of all adjacent edges and the algorithm has to decide which of those to add to the matching. We introduce an optimal, e-competitive truthful mechanism under the assumption that bidders arrive in random order (secretary model).It has been shown that the upper and lower bound of e for the original secretary problem extends to various other problems even with rich combinatorial structure, one of them being weighted bipartite matching. But truthful mechanisms so far fall short of reasonable competitive ratios once respective algorithms deviate from the original, simple threshold form. The best known mechanism for weighted bipartite matching by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [19] offers only a ratio logarithmic in the number of online vertices. We close this gap, showing that truthfulness does not impose any additional bounds. The proof technique is new in this surrounding, and based on the observation of an independency inherent to the mechanism. The insights provided hereby are interesting in their own right and appear to offer promising tools for other problems, with or without truthfulness.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.120"}, {"primary_key": "3119068", "vector": [], "sparse_vector": [], "title": "The Maximum Number of Minimal Dominating Sets in a Tree.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "A tree with n vertices has at most 95n/13 minimal dominating sets. The growth constant is best possible. It is obtained in a semi-automatic way as a kind of \"dominant eigenvalue\" of a bilinear operation on sixtuples that is derived from the dynamic-programming recursion for computing the number of minimal dominating sets of a tree. We also derive an output-sensitive algorithm for listing all minimal dominating sets with linear set-up time and linear delay between successive solutions.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.73"}, {"primary_key": "3119069", "vector": [], "sparse_vector": [], "title": "On Constant Multi-Commodity Flow-Cut Gaps for Families of Directed Minor-Free Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The multi-commodity flow-cut gap is a fundamental parameter that affects the performance of several divide & conquer algorithms, and has been extensively studied for various classes of undirected graphs. It has been shown by <PERSON><PERSON>, <PERSON> and <PERSON> [15] and by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [3] that for general n-vertex graphs it is bounded by O(log n) and the <PERSON><PERSON><PERSON> conjecture [9] asserts that it is O(1) for any family of graphs that excludes some fixed minor.The flow-cut gap is poorly understood for the case of directed graphs. We show that for uniform demands it is O(1) on directed series-parallel graphs, and on directed graphs of bounded pathwidth. These are the first constant upper bounds of this type for some non-trivial family of directed graphs. We also obtain O(1) upper bounds for the general multi-commodity flow-cut gap on directed trees and cycles. These bounds are obtained via new embeddings and Lipschitz quasipartitions for quasimetric spaces, which generalize analogous results form the metric case, and could be of independent interest. Finally, we discuss limitations of methods that were developed for undirected graphs, such as random partitions, and random embeddings.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.34"}, {"primary_key": "3119070", "vector": [], "sparse_vector": [], "title": "Expander Decomposition and Pruning: Faster, Stronger, and Simpler.", "authors": ["Thatchaphol <PERSON>", "<PERSON>"], "summary": "We study the problem of graph clustering where the goal is to partition a graph into clusters, i.e. disjoint subsets of vertices, such that each cluster is well connected internally while sparsely connected to the rest of the graph. In particular, we use a natural bicriteria notion motivated by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> [27] which we refer to as expander decomposition. Expander decomposition has become one of the building blocks in the design of fast graph algorithms, most notably in the nearly linear time Laplacian solver by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [48], and it also has wide applications in practice.We design algorithm for the parametrized version of expander decomposition, where given a graph G of m edges and a parameter ϕ, our algorithm finds a partition of the vertices into clusters such that each cluster induces a subgraph of conductance at least ϕ (i.e. a ϕ expander), and only a Õ(ϕ) fraction of the edges in G have endpoints across different clusters. Our algorithm runs in Õ(m/ϕ) time, and is the first nearly linear time algorithm when ϕ is at least 1/logO(1) m, which is the case in most practical settings and theoretical applications. Previous results either take Ω(m1+o(1)) time (e.g. [34, 54]), or attain nearly linear time but with a weaker expansion guarantee where each output cluster is guaranteed to be contained inside some unknown b expander (e.g. [50, 2]). Our result achieve both nearly linear running time and the strong expander guarantee for clusters. Moreover, a main technique we develop for our result can be applied to obtain a much better expander pruning algorithm, which is the key tool for maintaining an expander decomposition on dynamic graphs. Finally, we note that our algorithm is developed from first principles based on relatively simple and basic techniques, thus making it very likely to be practical.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.162"}, {"primary_key": "3119071", "vector": [], "sparse_vector": [], "title": "<PERSON> Flows Over Time with <PERSON><PERSON><PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Modeling traffic in road networks is a widely studied but challenging problem, especially under the assumption that drivers act selfishly. A common approach used in simulation software is the deterministic queuing model, for which the structure of dynamic equilibria has been studied extensively in the last couple of years. The basic idea is to model traffic by a continuous flow that travels over time from a source to a sink through a network, in which the arcs are endowed with transit times and capacities. Whenever the flow rate exceeds the capacity a queue builds up and the infinitesimally small flow particles wait in line in front of the bottleneck. Since the queues have no physical dimension, it was not possible, until now, to represent spillback in this model. This was a big drawback, since spillback can be regularly observed in real traffic situations and has a huge impact on travel times in highly congested regions. We extend the deterministic queuing model by introducing a storage capacity that bounds the total amount of flow on each arc. If an arc gets full, the inflow capacity is reduced to the current outflow rate, which can cause queues on previous arcs and blockages of intersections, i.e., spillback. We carry over the main results of the original model to our generalization and characterize dynamic equilibria, called Nash flows over time, by sequences of particular static flows, we call spillback thin flows. Furthermore, we give a constructive proof for the existence of dynamic equilibria, which suggests an algorithm for their computation. This solves an open problem stated by <PERSON> and <PERSON><PERSON> in 2010 [13].", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.57"}, {"primary_key": "3119072", "vector": [], "sparse_vector": [], "title": "Pseudorandomness for read-k DNF formulas.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The design of pseudorandom generators and deterministic approximate counting algorithms for DNF formulas are important challenges in unconditional derandomization. Numerous works on these problems have focused on the subclass of small-read DNF formulas, which are formulas in which each variable occurs a bounded number of times.Our first main result is a pseudorandom generator which ε-fools M-term read-k DNFs using seed length poly(k, log(1/ε))·log M + O(log n). This seed length is exponentially shorter, as a function of both k and 1/ε, than the best previous PRG for read-k DNFs. We also give a deterministic algorithm that approximates the number of satisfying assignments of an M-term read-k DNF to any desired (1 + ε)-multiplicative accuracy in timepoly(n)·min {(M/ε)poly(k, log(k/ε)), (M/ε)Õ(log((k log M)/ε))}.For any constant k this is a PTAS, and our runtime remains almost-polynomial (M Õ(log log M)) for k as large as any polylog(M). Prior to our work, the fastest deterministic algorithm ran in time even for k = 2, and no PTAS was known for any non-trivial subclass of DNFs. The common essential ingredients in these pseudorandomness results are new analytic inequalities for read-k DNFs. These inequalities may be of independent interest and utility; as an example application, we use them to obtain a significant improvement on the previous state of the art for agnostically learning read-k DNFs.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.39"}, {"primary_key": "3119073", "vector": [], "sparse_vector": [], "title": "Spectral Sparsification of Hypergraphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For an undirected/directed hypergraph G = (V, E), its Laplacian LG : ℝv → ℝv is defined such that its \"quadratic form\" x⊺LG (x) captures the cut information of G. In particular, 1S⊺LG(1S) coincides with the cut size of S ⊆ V, where 1S ∊ ℝV is the characteristic vector of S.A weighted subgraph H of a hypergraph G on a vertex set V is said to be an ∊-spectral sparsifier of G if (1 – ∊)x⊺ LH(x) ≤ x⊺ LG(x) ≤ (1 + ∊)x⊺ LH(x) holds for every x ∊ ℝV. In this paper, we present a polynomial-time algorithm that, given an undirected/directed hypergraph G on n vertices, constructs an ∊-spectral sparsifier of G with O(n3 log n/∊2) hyperedges/hyperarcs.The proposed spectral sparsification can be used to improve the time and space complexities of algorithms for solving problems that involve the quadratic form, such as computing the eigenvalues of LG, computing the effective resistance between a pair of vertices in G, semi-supervised learning based on LG, and cut problems on G. In addition, our sparsification result implies that any nonnegative hypernetwork type submodular function can be concisely represented by a directed hypergraph of polynomial size, even if the original representation is of exponential size. Accordingly, we show that, for any distribution, we can properly and agnostically learn nonnegative hypernetwork type submodular functions with O(n4 log(n/∊)/∊4) samples.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.159"}, {"primary_key": "3119074", "vector": [], "sparse_vector": [], "title": "Relative Error Tensor Low Rank Approximation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider relative error low rank approximation of tensors with respect to the Frobenius norm. Namely, given an order-q tensor A ∊ ℝ∏i=1qni, output a rank-k tensor B for which ‖A – B‖F2 ≤ (1 + ∊) OPT, where OPT = infrank-k A' ‖A – A'‖F2. Despite much success on obtaining relative error low rank approximations for matrices, no such results were known for tensors for arbitrary (1 + ∊)-approximations. One structural issue is that there may be no rank-k tensor Ak achieving the above infinum. Another, computational issue, is that an efficient relative error low rank approximation algorithm for tensors would allow one to compute the rank of a tensor, which is NP-hard. We bypass these two issues via (1) bicriteria and (2) parameterized complexity solutions:1.We give an algorithm which outputs a rank k' = O((k/∊)q–1) tensor B for which ‖A–B‖F2 ≤ (1+∊) OPT in nnz(A) + n · poly(k/∊) time in the real RAM model, whenever either Ak exists or OPT > 0. Here nnz(A) denotes the number of non-zero entries in A. If both Ak does not exist and OPT = 0, then B instead satisfies ‖A–B‖F2 0 which outputs a rank k tensor B for which ‖A – B‖F2 ≤ (1 + ∊) OPT and runs in (nnz(A)+n poly(k/∊)+exp(k2/∊))·nδ time in the unit cost RAM model, whenever OPT > 2–O(nδ) and there is a rank-k tensor B = ∑i=1k ui ⊗ vi ⊗ wi for which ‖A – B‖F2 ≤ (1 + ∊/2) OPT and ‖ui‖2, ‖vi‖2, ‖wi‖2 ≤ 2O(nδ). If OPT ≤ 2–Ω(nδ), then B instead satisfies ‖A – B‖F2 ≤ 2−Ω(nδ).Our first result is polynomial time, and in fact input sparsity time, in n, k, and 1/∊, for any k ≥ 1 and any 0 1, we show a 2Ω(k1−o(1)) time lower bound under the Exponential Time Hypothesis.Our results are based on an \"iterative existential argument\", and also give the first relative error low rank approximations for tensors for a large number of error measures for which nothing was known. In particular, we give the first relative error approximation algorithms on tensors for: column row and tube subset selection, entrywise ℓp-low rank approximation for 1 ≤ p < 2, low rank approximation with respect to sum of Euclidean norms of faces or tubes, weighted low rank approximation, and low rank approximation in distributed and streaming models. We also obtain several new results for matrices, such as nnz(A)-time CUR decompositions, improving the previous nnz(A)log n-time CUR decompositions, which may be of independent interest.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.172"}, {"primary_key": "3119075", "vector": [], "sparse_vector": [], "title": "Four-coloring P6-free graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we present a polynomial time algorithm for the 4-COLORING PROBLEM and the 4-PRECOLORING EXTENSION problem restricted to the class of graphs with no induced six-vertex path, thus proving a conjecture of <PERSON>. Combined with previously known results this completes the classification of the complexity of the 4-coloring problem for graphs with a connected forbidden induced subgraph.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.76"}, {"primary_key": "3119076", "vector": [], "sparse_vector": [], "title": "Communication-Rounds Tradeoffs for Common Randomness and Secret Key Generation.", "authors": ["Madhu <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the role of interaction in the Common Randomness Generation (CRG) and Secret Key Generation (SKG) problems. In the CRG problem, two players, <PERSON> and <PERSON>, respectively get samples X1, X2, … and Y1, Y2, … with the pairs (X1, Y1), (X2, Y2), … being drawn independently from some known probability distribution µ. They wish to communicate so as to agree on L bits of randomness. The SKG problem is the restriction of the CRG problem to the case where the key is required to be close to random even to an eavesdropper who can listen to their communication (but does not have access to the inputs of <PERSON> and <PERSON>). In this work, we study the relationship between the amount of communication and the number of rounds of interaction in both the CRG and the SKG problems. Specifically, we construct a family of distributions µ = µr,n,L, parametrized by integers r, n and L, such that for every r there exists a constant b = b(r) for which CRG (respectively SKG) is feasible when (Xi, Yi) ∼ µr,n,L with r + 1 rounds of communication, each consisting of O(log n) bits, but when restricted to r/2 – 2 rounds of interaction, the total communication must exceed Ω(n/ logb(n)) bits. Prior to our work no separations were known for r ≥ 2.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.112"}, {"primary_key": "3119077", "vector": [], "sparse_vector": [], "title": "A ϕ-Competitive Algorithm for Scheduling Packets with Deadlines.", "authors": ["<PERSON>", "Marek <PERSON>", "Lukasz Jez", "<PERSON><PERSON><PERSON>"], "summary": "In the online packet scheduling problem with deadlines (PacketScheduling, for short), the goal is to schedule transmissions of packets that arrive over time in a network switch and need to be sent across a link. Each packet has a deadline, representing its urgency, and a non-negative weight, that represents its priority. Only one packet can be transmitted in any time slot, so, if the system is overloaded, some packets will inevitably miss their deadlines and be dropped. In this scenario, the natural objective is to compute a transmission schedule that maximizes the total weight of packets which are successfully transmitted. The problem is inherently online, with the scheduling decisions made without the knowledge of future packet arrivals. The central problem concerning PacketScheduling, that has been a subject of intensive study since 2001, is to determine the optimal competitive ratio of online algorithms, namely the worst-case ratio between the optimum total weight of a schedule (computed by an offline algorithm) and the weight of a schedule computed by a (deterministic) online algorithm. We solve this open problem by presenting a ϕ-competitive online algorithm for PacketScheduling (where ϕ ≈ 1.618 is the golden ratio), matching the previously established lower bound.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.9"}, {"primary_key": "3119078", "vector": [], "sparse_vector": [], "title": "Maximum Integer Flows in Directed Planar Graphs with Vertex Capacities and Multiple Sources and Sinks.", "authors": ["<PERSON><PERSON>"], "summary": "We consider the maximum flow problem in directed planar graphs with capacities on both vertices and arcs and with multiple sources and sinks. We present three algorithms when the capacities are integers. The first algorithm runs in O(min{k2 n log n, n log3 n+kn}) time when all capacities are bounded by a constant, where n is the number of vertices in the graph and k is the number of terminals. This algorithm is the first to solve the vertex-disjoint paths problem in near-linear time when k is fixed but larger than 2. The second algorithm runs in O(k5n polylog(nU)) time, where U is the largest finite capacity of a single vertex. Finally, when k = 3, we present an algorithm that runs in O(n log n) time; this algorithm works even when the capacities are arbitrary reals. Our algorithms improve on the fastest previously known algorithms when k is fixed and U is bounded by a polynomial in n. Prior to this result, the fastest algorithms ran in O(n2/log n) time for real capacities, O(n3/2 log n log U) for integer capacities, and Õ(n10/7) for unit capacities, even when k = 3.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.35"}, {"primary_key": "3119079", "vector": [], "sparse_vector": [], "title": "Tight Bounds for ℓp Oblivious Subspace Embeddings.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "An ℓp oblivious subspace embedding is a distribution over r × n matrices n such that for any fixed n × d matrix A,where r is the dimension of the embedding, κ is the distortion of the embedding, and for an n-dimensional vector y, ‖y‖p = (∑i=1n |yi|)1/p is the ℓp-norm. Another important property is the sparsity of Π, that is, the maximum number of nonzero entries per column, as this determines the running time of computing Π · A. While for p = 2 there are nearly optimal tradeoffs in terms of the dimension, distortion, and sparsity, for the important case of 1 ≤ p 0, and (2) the first oblivious subspace embeddings for 1 ≤ p < 2 with O(1)-distortion and dimension independent of n. Oblivious subspace embeddings are crucial for distributed and streaming environments, as well as entrywise ℓp low rank approximation. Our results give improved algorithms for these applications.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.110"}, {"primary_key": "3119080", "vector": [], "sparse_vector": [], "title": "Optimal Las Vegas Approximate Near Neighbors in ℓp.", "authors": ["<PERSON>"], "summary": "We show that approximate near neighbor search in high dimensions can be solved in a Las Vegas fashion (i.e., without false negatives) for ℓp (1 ≤ p ≤ 2) while matching the performance of optimal locality-sensitive hashing. Specifically, we construct a data-independent Las Vegas data structure with query time O(dnρ) and space usage O(dn1+ρ) for (r, cr)-approximate near neighbors in ℝd under the ℓp norm, where ρ = 1/cp+o(1). Furthermore, we give a Las Vegas locality-sensitive filter construction for the unit sphere that can be used with the data-dependent data structure of <PERSON><PERSON> et al. (SODA 2017) to achieve optimal space-time tradeoffs in the data-dependent setting. For the symmetric case, this gives us a data-dependent Las Vegas data structure with query time O(dnρ) and space usage O(dn1+ρ) for (r, cr)-approximate near neighbors in ℝd under the ℓp norm, where ρ = 1/(2cp – 1) + o(1).Our data-independent construction improves on the recent Las Vegas data structure of Ahle (FOCS 2017) for ℓp when 1 ≤ p ≤ 2. Our data-dependent construction does even better for ℓp for all p ∊ [1, 2] and is the first Las Vegas approximate near neighbors data structure to make use of data-dependent approaches. We also answer open questions of <PERSON><PERSON> (SODA 2000), <PERSON><PERSON> (SODA 2016), and <PERSON><PERSON> by showing that for approximate near neighbors, Las Vegas data structures can match state-of-the-art Monte Carlo data structures in performance for both the data-independent and data-dependent settings and across space-time tradeoffs.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.108"}, {"primary_key": "3119082", "vector": [], "sparse_vector": [], "title": "Front Matter.", "authors": [], "summary": "For decades, the computer architecture research community has focused mostly on fundamental infrastructure, providing innovations and breakthroughs that improved the performance and efficiency of computer systems, permitting application developers to produce progressively more exciting applications and capabilities. That paradigm is under increasing stress as we race to the end of <PERSON>’s Law. I will argue that as our field diversifies with an unavoidable and increasing focus on specialization and applications, we should include novel sensors and actuators as part of our system designs, and ultimately humans as well.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.FM"}, {"primary_key": "3119083", "vector": [], "sparse_vector": [], "title": "Colored range closest-pair problem under general distance functions.", "authors": ["<PERSON><PERSON>"], "summary": "The range closest-pair (RCP) problem is the range-search version of the classical closest-pair problem, which aims to store a given dataset of points in some data structure such that when a query range X is specified, the closest pair of points contained in X can be reported efficiently. A natural generalization of the RCP problem is the colored range closest-pair (CRCP) problem in which the given data points are colored and the goal is to find the closest bichromatic pair contained in the query range. All the previous work on the RCP problem was restricted to the uncolored version and the Euclidean distance function. In this paper, we make the first progress on the CRCP problem. We investigate the problem under a general distance function induced by a monotone norm; in particular, this covers all the Lp-metrics for p ≥ 1 and the L∞-metric. We design efficient (1+ε)-approximate CRCP data structures for orthogonal queries in ℝ, where ε > 0 is a pre-specified parameter. The highlights are two data structures for answering rectangle queries, one of which uses O(ε−1 n log4 n) space and O(log4 n + ε−1 log3 n + ε−2 log n) query time while the other uses O(ε−1 n log3 n) space and O(log5 n + ε−1 log4 n + ε−2 log2 n) query time, where n is the size of the input dataset. In addition, we also apply our techniques to the CRCP problem in higher dimensions, obtaining efficient data structures for slab, 2-box, and 3D dominance queries. Before this paper, almost all the existing results for the RCP problem were achieved in ℝ2.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.24"}, {"primary_key": "3119084", "vector": [], "sparse_vector": [], "title": "Cheeger Inequalities for Submodular Transformations.", "authors": ["<PERSON><PERSON>"], "summary": "The <PERSON><PERSON>ger inequality for undirected graphs, which relates the conductance of an undirected graph and the second smallest eigenvalue of its normalized Laplacian, is a cornerstone of spectral graph theory. The <PERSON><PERSON>ger inequality has been extended to directed graphs and hypergraphs using normalized Laplacians for those, that are no longer linear but piecewise linear transformations.In this paper, we introduce the notion of a submodular transformation F : {0, 1}n → ℝm, which applies m submodular functions to the n-dimensional input vector, and then introduce the notions of its Laplacian and normalized Laplacian. With these notions, we unify and generalize the existing <PERSON><PERSON><PERSON> inequalities by showing a <PERSON><PERSON><PERSON> inequality for submodular transformations, which relates the conductance of a submodular transformation and the smallest non-trivial eigenvalue of its normalized Laplacian. This result recovers the <PERSON><PERSON><PERSON> inequalities for undirected graphs, directed graphs, and hypergraphs, and derives novel Cheeger inequalities for mutual information and directed information.Computing the smallest non-trivial eigenvalue of a normalized Laplacian of a submodular transformation is NP-hard under the small set expansion hypothesis. In this paper, we present a polynomial-time O(log n)-approximation algorithm for the symmetric case, which is tight, and a polynomial-time O(log2 n + log n · log m)-approximation algorithm for the general case.We expect the algebra concerned with submodular transformations, or submodular algebra, to be useful in the future not only for generalizing spectral graph theory but also for analyzing other problems that involve piecewise linear transformations, e.g., deep learning.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.160"}, {"primary_key": "3119085", "vector": [], "sparse_vector": [], "title": "Vector clique decompositions.", "authors": ["<PERSON>"], "summary": "Let ℱk be the set of all graphs on k vertices. For a graph G, a k-decomposition is a set of induced subgraphs of G, each isomorphic to an element of ℱk, such that each pair of vertices of G is in exactly one element of the set. It is a fundamental result of <PERSON> that for all n = |V(G)| sufficiently large, G has a k-decomposition if and only if G is k-divisible, namely k – 1 divides n – 1 and (2k) divides (2n).Let v ∊ ℝ|ℱk| be indexed by ℱk. For a k-decomposition L of G, let νv(L) = Σf∊ℱk vfdL,F where dL,F is the fraction of elements of L that are isomorphic to F. Let νv(G) = maxL νv(L) and νv(n) = min{νv (G) : |V(G)| = n} The sequence νv(n) has a limit so let νv = limn→∞ νv (n). Replacing k-decompositions with their fractional relaxations, one obtains the (polynomial time computable) fractional analogue νv*(G) and the corresponding fractional values νv*(n) and νv*. Our first main result is that for each v ∊ ℝ|ℱk|Furthermore, there is a polynomial time algorithm that produces a decomposition L of a k-decomposable graph such that νv(L) ≥ νv – on(1).A similar result holds when ℱk is the family of all tournaments on k vertices or when ℱk is the family of all edge-colorings of Kk.We use these results to obtain new and improved bounds on several decomposition results. For example, we prove that every n-vertex tournament which is 3-divisible (namely n = 1, 3 mod 6) has a triangle decomposition in which the number of directed triangles is less than 0.0222n2(1 + o(1)) and that every 5-decomposable n-vertex graph has a 5-decomposition in which the fraction of cycles of length 5 is on(1).", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.75"}, {"primary_key": "3119086", "vector": [], "sparse_vector": [], "title": "A 1.5-Approximation for Path TSP.", "authors": ["<PERSON>"], "summary": "We present a 1.5-approximation for the Metric Path Traveling Salesman Problem (Path TSP). All recent improvements on Path TSP crucially exploit a structural property shown by <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> [Journal of the ACM, 2015], namely that narrow cuts with respect to a Held-Ka<PERSON> solution form a chain. We significantly deviate from these approaches by showing the benefit of dealing with larger s-t cuts, even though they are much less structured. More precisely, we show that a variation of the dynamic programming idea recently introduced by <PERSON><PERSON><PERSON> and <PERSON>ygen [SODA, 2018] is versatile enough to deal with larger size cuts, by exploiting a seminal result of <PERSON><PERSON> on the number of near-minimum cuts. This avoids a recursive application of dynamic programming as used by T<PERSON>ub and Vygen, and leads to a considerably simpler algorithm avoiding an additional error term in the approximation guarantee. We match the still unbeaten 1.5-approximation guarantee of <PERSON><PERSON><PERSON>' algorithm for TSP. Hence, any further progress on the approximability of Path TSP will also lead to an improvement for TSP.", "published": "2019-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.93"}]