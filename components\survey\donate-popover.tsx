import Image from "next/image";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { SiBitcoincash } from "react-icons/si";
export default function DonatePopover() {
  return (
    <Popover>
      <PopoverTrigger>
        {/* <SiBitcoincash className="w-6 h-6 text-gray-700 hover:text-blue-400 transition-colors cursor-pointer" /> */}
        支持我
      </PopoverTrigger>
      <PopoverContent className="w-auto p-4">
        <div className="flex justify-center items-center space-x-4">
          {/* 支付宝收款码 */}
          <div className="text-center">
            <Image 
              src="/images/Ali_Pay.jpg"
              alt="支付宝收款码"
              width={100}
              height={100}
              className="mx-auto"
            />
            <p className="text-sm text-gray-500 mt-2">支付宝</p>
          </div>

          {/* 微信收款码 */}
          <div className="text-center">
            <Image 
              src="/images/Wechat_Pay.jpg"
              alt="微信收款码"
              width={100}
              height={100}
              className="mx-auto"
            />
            <p className="text-sm text-gray-500 mt-2">微信</p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}