import os
import sys

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path: sys.path.append(ROOT_DIR)

import os
import pickle
import json
import sqlite3
import re

from tqdm import tqdm
from typing import List, Dict, Tuple
from llmlingua import PromptCompressor
from utils.llm import BaseEmbedding, ModelSelector

from paper_get import ArxivPaper, ACLPaper

class PaperDeal:
    def __init__(self,
                 year: int,
                 db_path: str,
                 paper_path: str,
                 zilliz_path: str,
                 embedding_model: BaseEmbedding = None,
                 compressor: PromptCompressor = None,
                 ):
        '''
        初始化 PaperDeal 类
        Args:
            compressor: PromptCompressor 类实例 (不用输入, 会在方法内部 进行初始化， 使用了微软的 lingua 模型)
            paper_path: 论文路径 (路径下需要包含多个 json 文件，每个 json 文件包含若干论文)
            db_path: 数据库路径 (用于存储处理后的论文 json 文件, 过渡用)
            zilliz_path: zilliz 路径 (用于存储 zilliz 格式论文)
        '''
        self.year = year
        self.db_path = db_path
        self.paper_path = paper_path
        self.zilliz_path = zilliz_path

        # 检查路径是否都存在，不存在则创建(db_path 是一个文件，所以创建他的父目录即可)
        if not os.path.exists(os.path.dirname(self.db_path)):
            os.makedirs(os.path.dirname(self.db_path))
        if not os.path.exists(self.paper_path):
            os.makedirs(self.paper_path)
        if not os.path.exists(self.zilliz_path):
            os.makedirs(self.zilliz_path)

        self.embedding_model = embedding_model
        self.compressor = compressor

        # 初始化 embedding 模型
        if embedding_model is None:
            self.embedding_model = BaseEmbedding(**ModelSelector.sc_bge_m3)

        # 初始化 PromptCompressor 类
        if compressor is None:
            self.compressor = PromptCompressor(
                model_name="microsoft/llmlingua-2-bert-base-multilingual-cased-meetingbank",
                use_llmlingua2=True,
                device_map="cuda"
            )

    # =============== 一、创建数据库，存储论文原始数据 ===============
    def create_database(self) -> None:
        """
        创建 SQLite 数据库和表结构, 如果数据库不存在则创建
        Args:
            db_path: 数据库路径
        """
        # 创建数据库目录
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        # 创建数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建表结构
        cursor.execute(f'''
            CREATE TABLE IF NOT EXISTS papers (
                primary_key TEXT PRIMARY KEY,
                vector BLOB,
                sparse_vector BLOB,
                title TEXT,
                authors TEXT,
                summary TEXT,
                published TEXT,
                category TEXT,
                pdf_url TEXT,
                sub_summary TEXT,
                source TEXT,
                doi TEXT
            )
        ''')
        
        conn.commit()
        conn.close()

    def insert_papers(self, papers: List[Dict], batch_size: int = 1000) -> None:
        """
        批量插入论文数据
        具体而言，会先检索所有 key 出来，然后找到现在论文中有哪些 key 还没存进去再存（防止数据重复）
        """

        def prepare_paper_data(papers: List[Dict]) -> List[tuple]:
            """预处理论文数据，返回适合批量插入的元组列表"""
            prepared_data = []
            for paper in papers:
                paper_id = paper['pdf_url'].split('/')[-1]
                prepared_data.append((
                    paper_id,
                    None,  # embedding 初始为 None
                    None,  # sparse_vector 初始为 None
                    paper['title'],
                    json.dumps(paper['authors']),
                    paper['summary'],
                    paper['published'],
                    paper['category'],
                    paper['pdf_url'],
                    None, # sub_summary 初始为 None
                    paper['source'],
                    paper['doi']
                ))
            return prepared_data

        self.create_database()
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 获取已存在的 primary_key
        cursor.execute(f'SELECT primary_key FROM papers')
        existing_keys = set(row[0] for row in cursor.fetchall())
        
        # 预处理数据
        prepared_data = prepare_paper_data(papers)

        # 过滤掉已经存在的 primary_key
        filtered_data = [record for record in prepared_data if record[0] not in existing_keys]

        # 使用事务批量插入
        try:
            # 分批次插入
            for i in tqdm(range(0, len(filtered_data), batch_size)):
                batch = filtered_data[i:i + batch_size]
                cursor.executemany(f'''
                    INSERT OR REPLACE INTO papers (
                        primary_key, vector, sparse_vector, title, authors, summary, published, category, 
                        pdf_url, sub_summary, source, doi
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', batch)
                conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    def main_create_insert(self):
        papers = []
        # 读取所有 json 文件
        for file in os.listdir(self.paper_path):
            if file.endswith('.json') and f'_{self.year}' in file:
                with open(f'{self.paper_path}/{file}', 'r', encoding='utf-8') as f:
                    papers.extend(json.load(f))
        self.insert_papers(papers)

    # =============== 二、更新数据库中的 sub_summary =================
    def get_papers_without_sub_summary(self) -> List[Tuple[str, str]]:
        """获取数据库中没有 sub_summary 的文章"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f'''
            SELECT primary_key, summary, source, published
            FROM papers
            WHERE sub_summary IS NULL
        ''')
        
        papers = cursor.fetchall()
        conn.close()
        return papers

    def update_paper_sub_summary(self, paper_id: str, sub_summary: str) -> None:
        """更新指定文章的 sub_summary"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f'''
            UPDATE papers
            SET sub_summary = ?
            WHERE primary_key = ?
        ''', (sub_summary, paper_id))
        
        conn.commit()
        conn.close()

    def generate_sub_summaries(self) -> None:
        """生成并更新数据库中的 sub_summary"""
        papers = self.get_papers_without_sub_summary()
    
        if self.compressor is None:
            self.compressor = PromptCompressor(
                model_name="microsoft/llmlingua-2-bert-base-multilingual-cased-meetingbank",
                use_llmlingua2=True,
                device_map="cuda"
            )

        def remove_special_characters(text):
            # 只保留字母、数字、空格和常用标点符号
            pattern = r'[^a-zA-Z0-9\s.,!?\'"]'
            return re.sub(pattern, '', text)
        
        for paper_id, summary, source, published in tqdm(papers):
            dealed_summary = remove_special_characters(summary)
            if len(dealed_summary) < 2: 
                print(f'摘要缺失：source={source}, published={published}')
                dealed_summary = 'none'
            results = self.compressor.compress_prompt_llmlingua2(
                dealed_summary,
                target_token=66,
                force_tokens=['.', '!', '?'],
                chunk_end_tokens=['.'],
                return_word_label=False,
                drop_consecutive=True
            )
            sub_summary = results['compressed_prompt']
            self.update_paper_sub_summary(paper_id, sub_summary)

    # =============== 三、更新数据库中的 embedding =================
    def get_papers_without_embedding(self) -> List[Tuple[str, str, str]]:
        """获取数据库中没有 embedding 的文章"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f'''
            SELECT primary_key, title, sub_summary
            FROM papers
            WHERE vector IS NULL
        ''')
        
        papers = cursor.fetchall()
        conn.close()
        return papers

    def update_paper_embedding(self, paper_id: str, embedding: List[float]) -> None:
        """更新指定文章的 embedding"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 将 embedding 列表转换为二进制数据
        embedding_blob = pickle.dumps(embedding)
        
        cursor.execute(f'''
            UPDATE papers
            SET vector = ?
            WHERE primary_key = ?
        ''', (embedding_blob, paper_id))
        
        conn.commit()
        conn.close()

    def batch_generate_embedding(self, papers: List[Tuple[str, str, str]], 
                            batch_size: int = 16) -> Dict[str, List[float]]:
        """ 批量生成 embedding """
        paper_embeddings = {}
        
        for i in range(0, len(papers), batch_size):
            batch = papers[i:i + batch_size]
            # 将 title 和 sub_summary 拼接
            texts = [f"{title}\n{sub_summary}" for _, title, sub_summary in batch]
            
            # 生成 embedding
            embeddings = self.embedding_model.embedding(texts)
            
            # 将结果与 paper_id 对应存储
            for j, (paper_id, _, _) in enumerate(batch):
                if j < len(embeddings):  # 确保有对应的 embedding
                    paper_embeddings[paper_id] = embeddings[j]
        
        return paper_embeddings

    def process_papers_embedding(self, batch_size: int = 16) -> None:
        """处理数据库中所有没有 embedding 的文章"""
        # 获取所有没有 embedding 的文章
        papers = self.get_papers_without_embedding()
        print(f"找到 {len(papers)} 篇需要处理的文章")
        
        # 批量生成 embedding
        for i in tqdm(range(0, len(papers), batch_size)):
            batch = papers[i:i + batch_size]
            paper_embeddings = self.batch_generate_embedding(batch, batch_size)
            
            # 更新数据库
            for paper_id, embedding in paper_embeddings.items():
                self.update_paper_embedding(paper_id, embedding)

    # =============== 三、更新数据库中的 稀疏向量 =================
    def get_papers_without_sparse_vector(self) -> List[Tuple[str, str, str]]:
        """获取数据库中没有 sparse_vector 的文章"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f'''
            SELECT primary_key, title, sub_summary
            FROM papers
            WHERE sparse_vector IS NULL
        ''')
        
        papers = cursor.fetchall()
        conn.close()
        return papers

    def update_paper_sparse_vector(self, paper_id: str, sparse_vector: List[float]) -> None:
        """更新指定文章的 sparse_vector"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 将 sparse_vector 列表转换为二进制数据
        sparse_vector_blob = pickle.dumps(sparse_vector)
        
        cursor.execute(f'''
            UPDATE papers
            SET sparse_vector = ?
            WHERE primary_key = ?
        ''', (sparse_vector_blob, paper_id))
        
        conn.commit()
        conn.close()

    def batch_generate_sparse_vector(self, papers: List[Tuple[str, str, str]], 
                            batch_size: int = 16) -> Dict[str, List[float]]:
        """ 批量生成 sparse_vector """
        paper_sparse_vectors = {}
        
        for i in range(0, len(papers), batch_size):
            batch = papers[i:i + batch_size]
            # 将 title 和 sub_summary 拼接
            texts = [f"{title}\n{sub_summary}" for _, title, sub_summary in batch]
            
            # 生成 sparse_vector
            sparse_vectors = self.embedding_model.embedding(texts)
            
            # 将结果与 paper_id 对应存储
            for j, (paper_id, _, _) in enumerate(batch):
                if j < len(sparse_vectors):  # 确保有对应的 sparse_vector
                    paper_sparse_vectors[paper_id] = sparse_vectors[j]
        
        return paper_sparse_vectors

    def process_papers_sparse_vector(self, batch_size: int = 16) -> None:
        """处理数据库中所有没有 sparse_vector 的文章"""
        # 获取所有没有 sparse_vector 的文章
        papers = self.get_papers_without_sparse_vector()
        print(f"找到 {len(papers)} 篇需要处理的文章")
        
        # 批量生成 embedding
        for i in tqdm(range(0, len(papers), batch_size)):
            batch = papers[i:i + batch_size]
            paper_sparse_vectors = self.batch_generate_sparse_vector(batch, batch_size)
            
            # 更新数据库
            for paper_id, sparse_vector in paper_sparse_vectors.items():
                self.update_paper_sparse_vector(paper_id, sparse_vector)

    # =============== 四、将数据库中数据转换为 zilliz 导入格式 ===============
    def get_papers_by_like(self, like_str: str = '2025-01-%'):
        """根据 like 信息筛选数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 初始化搜索列名
        search_columns = ['primary_key', 'vector', 'sparse_vector', 'title', 'authors', 'summary', 'published', 'category', 'pdf_url', 'sub_summary', 'source', 'doi']
        
        cursor.execute(f'''
            SELECT {', '.join(search_columns)}
            FROM papers
            WHERE vector IS NOT NULL AND sparse_vector IS NOT NULL
            AND published like '{like_str}'
        ''')
        
        papers = cursor.fetchall()
        conn.close()

        # 将搜索结果中每一个 paper 的 embedding 字段从二进制数据转换为 list 类型
        # 同时将 paper 转换为 map 类型，方便后续处理
        papers = [dict(zip(search_columns, paper)) for paper in papers]

        return papers

    def trans_to_zilliz(self, papers) -> list[dict]:
        # 转换为 zilliz 格式
        rows = []
        for paper in papers:
            try:
                # 获取向量表示
                # 这里的 paper['embedding'] 是一个 BLOB 类型的二进制数据
                # 转换为 list 类型的向量
                
                # 构建数据字典
                # 根据 Milvus schema 构建数据字典
                data_dict = {
                    "primary_key": str(paper.get('primary_key', ''))[:1000],  # VARCHAR(1000)
                    "vector": pickle.loads(paper.get('vector')),  # FLOAT_VECTOR(1024)
                    "sparse_vector": pickle.loads(paper.get('sparse_vector')),  # FLOAT_VECTOR(1024)
                    "title": str(paper.get('title', ''))[:500],  # VARCHAR(500)
                    "authors": json.loads(paper.get('authors', []))[:500],  # Array<VARCHAR(100)>[500]
                    "summary": str(paper.get('summary', ''))[:5000],  # VARCHAR(5000)
                    "published": str(paper.get('published', ''))[:100],  # VARCHAR(100)
                    "category": str(paper.get('category', ''))[:100],  # VARCHAR(100)
                    "pdf_url": str(paper.get('pdf_url', ''))[:200],  # VARCHAR(200)
                    "sub_summary": str(paper.get('sub_summary', ''))[:2000],  # VARCHAR(2000)
                    "source": str(paper.get('source', ''))[:100],  # VARCHAR(100)
                    "doi": str(paper.get('doi', ''))[:200]  # VARCHAR(200)
                }
                rows.append(data_dict)
            except Exception as e:
                print(f"处理文档 {paper.get('primary_key', '')} 时出错: {e}")
                continue
                
        return rows

    def main_trans_to_zilliz(self, year: int, sources: list[str] = [], save_type: str = 'month'):
        # 检查文件夹
        if not os.path.exists(self.zilliz_path):
            os.makedirs(self.zilliz_path)

        def save_to_json(rows: list[dict], file_name: str):
            if len(rows) == 0:
                print(f"没有数据，跳过保存")
                return
            path = f'{self.zilliz_path}/{file_name}'
            json_file = {'rows': rows}
            with open(path, 'w') as f:
                json.dump(json_file, f)
            print(f"数据已保存，共 {len(rows)} 条记录")

        if save_type == 'month':
            # 1. 12 个月分开存储
            for i in tqdm(range(12)):
                # 按照月份获取数据
                like_str = f'{year}-{0 if i+1 < 10 else ""}{i+1}-%'
                papers = self.get_papers_by_like(like_str)
                if len(papers) == 0: continue

                # 根据 sources 筛选数据
                if len(sources) > 0:
                    papers = [paper for paper in papers if paper['source'].lower() in sources]
                    if len(papers) == 0: continue

                rows = self.trans_to_zilliz(papers)

                # 使用 save_to_json 函数存储
                save_to_json(rows, f'papers_y{year}_m{i}.json')

        elif save_type == 'half':
            # 2. 半年半年存储
            rows_first_half = []
            rows_second_half = []
            for i in range(12):
                # 按照月份获取数据
                like_str = f'{year}-{0 if i+1 < 10 else ""}{i+1}-%'
                papers = self.get_papers_by_like(like_str) 
                if len(papers) == 0: continue

                # 根据 sources 筛选数据
                if len(sources) > 0:
                    papers = [paper for paper in papers if paper['source'].lower() in sources]
                    if len(papers) == 0: continue

                rows = self.trans_to_zilliz(papers)
                
                # 将论文分为上半年和下半年
                if i < 6:  # 1-6月
                    rows_first_half.extend(rows)
                else:  # 7-12月
                    rows_second_half.extend(rows)
            
            # 存储上下半年数据
            save_to_json(rows_first_half, f'papers_y{year}_first_half.json')
            save_to_json(rows_second_half, f'papers_y{year}_second_half.json')
        else:
            # 3. 直接一整年存到一起
            all_rows = []
            for i in range(12):
                # 按照月份获取数据
                like_str = f'{year}-{0 if i+1 < 10 else ""}{i+1}-%'
                papers = self.get_papers_by_like(like_str)
                if len(papers) == 0: continue

                # 根据 sources 筛选数据
                if len(sources) > 0:
                    papers = [paper for paper in papers if paper['source'].lower() in sources]
                    if len(papers) == 0: continue

                rows = self.trans_to_zilliz(papers)
                all_rows.extend(rows)
            
            # 存储全年数据
            save_to_json(all_rows, f'papers_y{year}_all.json')

def deal_arxiv_paper(year: int):
    # 1.2 初始化路径
    root_path_list = ['data', 'paper_arxiv']
    db_path = os.path.join(ROOT_DIR, *root_path_list, 'db', f'papers_{year}.db')
    paper_path = os.path.join(ROOT_DIR, *root_path_list, 'paper')
    zilliz_path = os.path.join(ROOT_DIR, *root_path_list, 'zilliz')

    # 1.3 初始化模型
    embedding_model=BaseEmbedding(**ModelSelector.sc_bge_m3_pro) # 初始化 embedding 模型
    compressor=PromptCompressor(
        model_name="microsoft/llmlingua-2-bert-base-multilingual-cased-meetingbank",
        use_llmlingua2=True,
        device_map="cuda") # 初始化 PromptCompressor 类(使用微软的 lingua 模型， 用于进一步压缩摘要为 66 个 token)
    
    # 1.4 初始化 PaperDeal 类
    paper_deal = PaperDeal(year=year,
                           db_path=db_path,
                           paper_path=paper_path,
                           zilliz_path=zilliz_path,
                           # init model
                           embedding_model=embedding_model,
                           compressor=compressor)

    # 2. 获取并保存 Arxiv 系列论文
    arxiv_paper = ArxivPaper()
    arxiv_paper.get_paper_by_year(year=year, save_path=paper_path)
    
    # 3. 创建数据库
    paper_deal.create_database()

    # 4. 插入论文
    print(f"1. 插入论文到数据库中")
    paper_deal.main_create_insert()

    # 5. 更新 sub_summary (可重复执行)
    print(f"2. 更新 sub_summary")
    paper_deal.generate_sub_summaries()

    # 6. 更新 embedding  (可重复执行)
    print(f"3. 更新 embedding")
    paper_deal.process_papers_embedding(64)
    
    # 7. 转换为 zilliz 格式 (可重复执行)
    print(f"4. 转换为 zilliz 格式")
    paper_deal.main_trans_to_zilliz(year=year, save_type='month')

def deal_acl_paper(sources: list[str], year: int):

    # 1.2 初始化路径
    root_path_list = ['data', 'paper_acl']
    db_path = os.path.join(ROOT_DIR, *root_path_list, 'db', f'papers_{year}.db')
    paper_path = os.path.join(ROOT_DIR, *root_path_list, 'paper')
    zilliz_path = os.path.join(ROOT_DIR, *root_path_list, 'zilliz')

    # 1.3 初始化模型
    embedding_model=BaseEmbedding(**ModelSelector.sc_bge_m3) # 初始化 embedding 模型
    compressor=PromptCompressor(
        model_name="microsoft/llmlingua-2-bert-base-multilingual-cased-meetingbank",
        use_llmlingua2=True,
        device_map="cuda") # 初始化 PromptCompressor 类(使用微软的 lingua 模型， 用于进一步压缩摘要为 66 个 token)
    
    # 1.4 初始化 PaperDeal 类
    paper_deal = PaperDeal(year=year,
                           db_path=db_path,
                           paper_path=paper_path,
                           zilliz_path=zilliz_path,
                           # init model
                           embedding_model=embedding_model,
                           compressor=compressor)

    # 2. 获取并保存 ACL 系列论文
    acl_paper = ACLPaper()
    acl_paper.get_and_save_papers(sources=sources, save_path=paper_path, year=year)

    # 3. 创建数据库
    paper_deal.create_database()

    # 4. 插入论文
    print(f"1. 插入论文到数据库中")
    paper_deal.main_create_insert()

    # 5. 更新 sub_summary (可重复执行)
    print(f"2. 更新 sub_summary")
    paper_deal.generate_sub_summaries()

    # 6. 更新 embedding  (可重复执行)
    print(f"3. 更新 embedding")
    paper_deal.process_papers_embedding()
    
    # 7. 转换为 zilliz 格式 (可重复执行)
    print(f"4. 转换为 zilliz 格式")
    paper_deal.main_trans_to_zilliz(year=year, save_type='year')

def deal_conf_paper(year: int, sources: list[str] = []):
    '''
    处理 会议 论文
    Args:
        year: 年份
        sources: 会议列表
    '''
    print(f"处理 {year} 年数据")

    # 1.2 初始化路径
    root_path_list = ['data', 'paper_conf']
    db_path = os.path.join(ROOT_DIR, *root_path_list, 'db', f'papers_{year}.db')
    paper_path = os.path.join(ROOT_DIR, *root_path_list, 'paper')
    zilliz_path = os.path.join(ROOT_DIR, *root_path_list, 'zilliz')

    # 1.3 初始化模型
    embedding_model=BaseEmbedding(**ModelSelector.sc_bge_m3) # 初始化 embedding 模型
    compressor=PromptCompressor(
        model_name="microsoft/llmlingua-2-bert-base-multilingual-cased-meetingbank",
        use_llmlingua2=True,
        device_map="cuda") # 初始化 PromptCompressor 类(使用微软的 lingua 模型， 用于进一步压缩摘要为 66 个 token)
    
    # 1.4 初始化 PaperDeal 类
    paper_deal = PaperDeal(year=year,
                           db_path=db_path,
                           paper_path=paper_path,
                           zilliz_path=zilliz_path,
                           # init model
                           embedding_model=embedding_model,
                           compressor=compressor)

    # 3. 创建数据库
    paper_deal.create_database()

    # 4. 插入论文
    print(f"1. 插入论文到数据库中")
    paper_deal.main_create_insert()

    # 5. 更新 sub_summary (可重复执行)
    print(f"2. 更新 sub_summary")
    paper_deal.generate_sub_summaries()

    # 6. 更新 embedding  (可重复执行)
    print(f"3. 更新 embedding")
    paper_deal.process_papers_embedding()

    # 7. 更新 稀疏向量  (可重复执行)
    print(f"4. 更新 稀疏向量")
    paper_deal.process_papers_sparse_vector()
    
    # 8. 转换为 zilliz 格式 (可重复执行)
    print(f"5. 转换为 zilliz 格式")
    paper_deal.main_trans_to_zilliz(year=year, sources=sources, save_type='year')

def deal_jn_paper(year: int, sources: list[str] = []):
    '''
    处理 期刊 论文
    Args:
        year: 年份
        sources: 期刊列表
    '''
    print(f"处理 {year} 年数据")

    # 1.2 初始化路径
    root_path_list = ['data', 'paper_jn']
    db_path = os.path.join(ROOT_DIR, *root_path_list, 'db', f'papers_{year}.db')
    paper_path = os.path.join(ROOT_DIR, *root_path_list, 'paper')
    zilliz_path = os.path.join(ROOT_DIR, *root_path_list, 'zilliz')

    # 1.3 初始化模型
    embedding_model=BaseEmbedding(**ModelSelector.sc_bge_m3) # 初始化 embedding 模型
    compressor=PromptCompressor(
        model_name="microsoft/llmlingua-2-bert-base-multilingual-cased-meetingbank",
        use_llmlingua2=True,
        device_map="cuda") # 初始化 PromptCompressor 类(使用微软的 lingua 模型， 用于进一步压缩摘要为 66 个 token)
    
    # 1.4 初始化 PaperDeal 类
    paper_deal = PaperDeal(year=year,
                           db_path=db_path,
                           paper_path=paper_path,
                           zilliz_path=zilliz_path,
                           # init model
                           embedding_model=embedding_model,
                           compressor=compressor)

    # 3. 创建数据库
    paper_deal.create_database()

    # 4. 插入论文
    print(f"1. 插入论文到数据库中")
    paper_deal.main_create_insert()

    # 5. 更新 sub_summary (可重复执行)
    print(f"2. 更新 sub_summary")
    paper_deal.generate_sub_summaries()

    # 6. 更新 embedding  (可重复执行)
    print(f"3. 更新 embedding")
    paper_deal.process_papers_embedding()
    
    # 7. 转换为 zilliz 格式 (可重复执行)
    print(f"4. 转换为 zilliz 格式")
    paper_deal.main_trans_to_zilliz(year=year, sources=sources, save_type='year')

def add_source_to_papers(paper_path: str):
    """
    为指定路径下的所有论文 JSON 文件添加 source 字段
    Args:
        paper_path: 论文 JSON 文件所在的路径
    """
    # 检查路径是否存在
    if not os.path.exists(paper_path):
        print(f"路径 {paper_path} 不存在")
        return

    # 遍历路径下的所有文件
    for filename in tqdm(os.listdir(paper_path)):
        if not filename.endswith('.json'):
            continue

        file_path = os.path.join(paper_path, filename)
        print(f"处理文件: {filename}")

        try:
            # 读取 JSON 文件
            with open(file_path, 'r', encoding='utf-8') as f:
                papers = json.load(f)

            # 为每篇论文添加 source 字段
            for paper in papers:
                if 'source' not in paper:
                    paper['source'] = 'arxiv'

            # 保存修改后的文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(papers, f, ensure_ascii=False, indent=2)

            print(f"成功处理文件: {filename}")
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {e}")

if __name__ == "__main__":
    # 1. 处理 ACL 系列论文
    # 全称，简介，CCF 分区，影响力
    # acl_map = {
    #     'acl': ['Annual Meeting of the Association for Computational Linguistics', 'NLP领域最顶级国际会议之一，涵盖所有NLP方向', 'A', '最高，全球顶级NLP会议'],
    #     'aacl': ['Asia-Pacific Chapter of the ACL Conference', 'ACL亚太分会，亚洲/太平洋区域顶级NLP会议', 'B', '高，影响力逐年上升'],
    #     'naacl': ['North American Chapter of the ACL Conference', 'ACL北美分会，北美区域顶级NLP会议', 'A', '顶级，和EMNLP、EACL齐名'],
    #     'eacl': ['European Chapter of the ACL Conference', 'ACL欧洲分会，欧洲区域顶级NLP会议', 'A', '顶级，和NAACL、EMNLP齐名'],
    #     'emnlp': ['Empirical Methods in Natural Language Processing', 'NLP顶级会议，强调实证、实验方法', 'A', '最高，全球顶级NLP会议'],
    #     'coling': ['International Conference on Computational Linguistics', '历史悠久的NLP国际大会，涵盖计算语言学各方向', 'B', '高，老牌NLP会议'],
    #     'ccl': ['China National Conference on Computational Linguistics', '中国计算语言学大会，中文NLP最重要会议', '无', '国内最高，中文NLP首选'],
    #     'lrec': ['Language Resources and Evaluation Conference', '侧重语言资源、评测与数据集建设，在相关细分方向影响力大', '无', '资源型NLP领域影响力大'],
    #     'findings': ['Findings of Association for Computational Linguistics', 'ACL体系论文集，收录主会未录用但质量较高的论文，以及部分workshop、direct submission', '无', '高，略低于主会，认可度高'],
    #     'semeval': ['Semantic Evaluation Workshop and Shared Task', 'NLP领域著名的国际语义评测竞赛与workshop，与NAACL/ACL/EMNLP等主会合办，论文多为系统描述或任务报告，含金量略低于主会但在任务竞赛领域极有影响力', '无', '领域内极有影响力，尤其评测任务'],
    #     'wmt': ['Workshop on Machine Translation', '机器翻译领域最有影响力的国际评测竞赛与研讨会', '无', 'MT领域影响力极高'],
    #     'ranlp': ['Recent Advances in Natural Language Processing', '欧洲区域知名NLP会议，涵盖应用与理论', '无', '欧洲有影响力，略低于EACL/EMNLP等'],
    # }
    # sources = list(acl_map.keys())
    # for year in range(2019, 2026):
    #     deal_acl_paper(sources=sources, year=year)

    # 2. 处理 Arxiv 系列论文
    # for i in range(2010, 2026):
    #     deal_arxiv_paper(year=i)
    deal_arxiv_paper(year=2025)

    # 3. 处理 会议 论文 (aaai, cvpr, eccv, iccv, icdm, icip, iclr, nips)
    # confs = ['aaai', 'cvpr', 'eccv', 'iccv', 'icdm', 'icip', 'iclr', 'nips']
    # confs = ['cvpr', 'icml', 'ijcai']
    # for i in range(2010, 2026):
    #     deal_cv_paper(year=i, sources=confs)
    # deal_cv_paper(year=2025, sources=confs)

    # 4. 处理 期刊 论文
    # jns = ['tdsc', 'tifs', 'tvcg', 'tip', 'ijcv', 'tpami', 'jmlr', 'ai', 'tkde', 'jmlr']
    # for i in range(2010, 2026):
    #     deal_jn_paper(year=i, sources=jns)

    # 3. 为指定路径下的所有论文 JSON 文件添加 source 字段
    # paper_path = "E:\\project\\arxiv-insight\\backend\\data\\paper_arxiv\\paper"
    # add_source_to_papers(paper_path)