[{"primary_key": "2134249", "vector": [], "sparse_vector": [], "title": "A Hitting Set Relaxation for $k$-Server and an Extension to Time-Windows.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the $k$ -server problem with time-windows. In this problem, each request $i$ arrives at some point $v_{i}$ of an $n$ -point metric space at time $b_{i}$ and comes with a deadline $e_{i}$ . One of the $k$ servers must be moved to $v_{i}$ at some time in the interval [ $b_{i}, e_{i}$ ] to satisfy this request. We give an online algorithm for this problem with a competitive ratio of $\\text{poly}\\log(n, \\Delta)$ , where $\\Delta$ is the aspect ratio of the metric space. Prior to our work, the best competitive ratio known for this problem was $O(k\\ \\text{poly}\\log(n))$ given by <PERSON><PERSON> et al. (STOC 2017). Our algorithm is based on a new covering linear program relaxation for $k$ -server on HSTs. This LP naturally corresponds to the min-cost flow formulation of $k$ -server, and easily extends to the case of time-windows. We give an online algorithm for obtaining a feasible fractional solution for this LP, and a primal dual analysis framework for accounting the cost of the solution. Together, they yield a new $k$ -server algorithm with poly-logarithmic competitive ratio, and extend to the time-windows case as well. Our principal technical contribution lies in thinking of the covering LP as yielding a truncated covering LP at each internal node of the tree, which allows us to keep account of server movements across subtrees. We hope that this LP relaxation and the algorithm/analysis will be a useful tool for addressing $k$ -server and related problems.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00057"}, {"primary_key": "2134250", "vector": [], "sparse_vector": [], "title": "Constructive Separations and Their Consequences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For a complexity class C and language L, a constructive separation of \"L is not in C\" gives an efficient algorithm (also called a refuter) to find counterexamples (bad inputs) for every C-algorithm attempting to decide L. We study the questions: Which lower bounds can be made constructive? What are the consequences of constructive separations? We build a case that \"constructiveness\" serves as a dividing line between many weak lower bounds we know how to prove, and strong lower bounds against P, ZPP, and BPP. Put another way, constructiveness is the opposite of a complexity barrier: it is a property we want lower bounds to have. Our results fall into three broad categories. 1. For many separations, making them constructive would imply breakthrough lower bounds. Our first set of results shows that, for many well-known lower bounds against streaming algorithms, one-tape Turing machines, and query complexity, as well as lower bounds for the Minimum Circuit Size Problem, making these lower bounds constructive would imply break-through separations ranging from \"EXP not equal to BPP\" to even \"P not equal to NP\". 2. Most conjectured uniform separations can be made constructive. Our second set of results shows that for most major open problems in lower bounds against P, ZPP, and BPP, including \"P not equal to NP\", \"P not equal to PSPACE\", \"P not equal to PP\", \"ZPP not equal to EXP\", and \"BPP not equal to NEXP\", any proof of the separation would further imply a constructive separation. Our results generalize earlier results for \"P not equal to NP\" [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and Ta-Shma, CCC 2005] and \"BPP not equal to NEXP\" [Dolev, Fandina and Gutfreund, CIAC 2013]. Thus any proof of these strong lower bounds must also yield a constructive version, compared to many weak lower bounds we currently know. 3. Some separations cannot be made constructive. Our third set of results shows that certain complexity separations cannot be made constructive. We observe that for all super-polynomially growing functions $\\mathbf{t}$ , there are no constructive separations for detecting high t-time Kolmogorov complexity (a task which is known to be not in P) from any complexity class, unconditionally. We also show that under plausible conjectures, there are languages in NP - $\\mathbf{P}$ for which there are no constructive separations from any complexity class.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00069"}, {"primary_key": "2134251", "vector": [], "sparse_vector": [], "title": "A direct product theorem for quantum communication complexity with applications to device-independent QKD.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a direct product theorem for the entanglement-assisted interactive quantum communication complexity in terms of the quantum partition bound for product distributions. The quantum partition or efficiency bound is a lower bound on communication complexity, a non-distributional version of which was introduced by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (2012). For a two-input boolean function, the best result for interactive quantum communication complexity known previously was due to <PERSON><PERSON><PERSON> (2018), who showed a direct product theorem in terms of the generalized discrepancy. While there is no direct relationship between the maximum distributional quantum partition bound for product distributions, and the generalized discrepancy method, unlike <PERSON><PERSON><PERSON>'s result, our result works for two-input functions or relations whose outputs are non-boolean as well. As an application of our result, we show that it is possible to do device-independent quantum key distribution (DIQKD) without the assumption that devices do not leak any information after inputs are provided to them. We analyze the DIQKD protocol given by <PERSON>, <PERSON> and <PERSON> (2020), and show that when the protocol is carried out with devices that are compatible with several copies of the Magic Square game, it is possible to extract a linear (in the number of copies of the game) amount of key from it, even in the presence of a linear amount of leakage. Our security proof is parallel, i.e., the honest parties can enter all their inputs into their devices at once, and works for a leakage model that is arbitrarily interactive, i.e., the devices of the honest parties <PERSON> and <PERSON> can exchange information with each other and with the eavesdropper Eve in any number of rounds, as long as the total number of bits or qubits communicated is bounded.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00125"}, {"primary_key": "2134252", "vector": [], "sparse_vector": [], "title": "On the <PERSON><PERSON> conjecture.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The <PERSON><PERSON><PERSON> conjecture states that no truthful mechanism for makespan-minimization when allocating $m$ tasks to $n$ unrelated machines can have approximation ratio less than n. Over more than two decades since its formulation, little progress has been made in resolving it and the best known lower bound is still a small constant. This work makes progress towards validating the conjecture by showing a lower bound of 1+ ✓ $n$ -1.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00086"}, {"primary_key": "2134253", "vector": [], "sparse_vector": [], "title": "Random Order Online Set Cover is as Easy as Offline.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a polynomial-time algorithm for Online-SetCover with a competitive ratio of $O(\\log mn)$ when the elements are revealed in random order, matching the best possible offline bound of $O(\\log n)$ when the number of sets $m$ is polynomial in the number of elements $n$ , and circumventing the $\\Omega(\\log m \\log n)$ lower bound known in adversarial order. We also extend the result to solving pure covering IPs when constraints arrive in random order. The algorithm is a multiplicative-weights-based round-and-solve approach we call LearnOrCover. We maintain a coarse fractional solution that is neither feasible nor monotone increasing, but can nevertheless be rounded online to achieve the claimed guarantee (in the random order model). This gives a new offline algorithm for Setcover that performs a single pass through the elements, which may be of independent interest.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00122"}, {"primary_key": "2134254", "vector": [], "sparse_vector": [], "title": "Fully Dynamic s-t Edge Connectivity in Subpolynomial Time (Extended Abstract).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a deterministic fully dynamic algorithm to answer c-edge connectivity queries on pairs of vertices in n°(1) worst case update and query time for any positive integer $c$ = (log n)° (1) for a graph with $n$ vertices. Previously, only polylogarithmic, O(√n), and O(n 2 / 3 ) worst case update time fully dynamic algorithms were known for answering 1, 2 and 3-edge connectivity queries respectively [<PERSON><PERSON><PERSON> 1995, <PERSON><PERSON><PERSON><PERSON> 1997, <PERSON><PERSON><PERSON> and <PERSON><PERSON> 1991]. Our result extends the c-edge connectivity vertex sparsifier [<PERSON><PERSON><PERSON><PERSON> et al. 2021] to a multi-level sparsification framework. As our main technical contribution, we present a novel update algorithm for the multi-level c-edge connectivity vertex sparsifier with subpolynomial update time. See https://arxiv.org/abs/2004.07650 for the full version of this paper.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00088"}, {"primary_key": "2134255", "vector": [], "sparse_vector": [], "title": "Hardness vs Randomness, Revised: Uniform, Non-Black-Box, and Instance-Wise.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new approach to the hardness-to-randomness framework and to the $promise-\\mathcal{BPP}\\ = promise-\\mathcal{P}$ conjecture. Classical results rely on non-uniform hardness assumptions to construct derandomization algorithms that work in the worst-case, or rely on uniform hardness assumptions to construct derandomization algorithms that work only in the average-case. In both types of results, the derandomization algorithm is \"black-box\" and uses the standard PRG approach. In this work we present results that closely relate new and natural uniform hardness assumptions to worst-case derandomization of $promise-\\mathcal{BPP}$ , where the algorithms underlying the latter derandomization are non-black-box. In our main result, we show that $promise-\\mathcal{BPP}\\ = promise-\\mathcal{P}$ if the following holds: There exists a multi-output function computable by logspace-uniform circuits of polynomial size and depth $n^{2}$ that cannot be computed by uniform probabilistic algorithms in time $n^{c}$ , for some universal constant $c &gt; 1$ , on almost all inputs. The required failure on \"almost all inputs\" is stronger than the standard requirement of failing on one input of each length; however, the same assumption without the depth restriction on $f$ is necessary for the conclusion. This suggests a potential equivalence between worst-case derandomization of $promise-\\mathcal{BPP}$ of any form (i.e., not necessarily by a black-box algorithm) and the existence of efficiently-computable functions that are hard for probabilistic algorithms on almost all inputs. In our second result, we introduce a new and uniform hardness-to-randomness tradeoff for the setting of superfast average-case derandomization: prior to this work, superfast average-case derandomization was known only under non-uniform hardness assumptions. In an extreme instantiation of our new tradeoff, under appealing uniform hardness assumptions, we show that for every polynomial $T(n)$ and constant $\\epsilon &gt; 0$ it holds that $\\mathcal{BPTIME}[T]\\subseteq \\mathrm{heur}-\\mathcal{DTIME}[T\\cdot n^{\\epsilon}]$ , where the \"heur\" prefix means that no polynomial-time algorithm can find, with non-negligible probability, an input on which the deterministic simulation errs. Technically, our approach is to design targeted PRGs and HSGs, as introduced by Goldreich (LNCS, 2011). The targeted PRGs/HSGs \"produce randomness from the input\", as sug-gested by Goldreich and Wigderson (RANDOM 2002); and their analysis relies on non-black-box versions of the reconstruction procedure of Impagliazzo and Wigderson (FOCS 1998). Our main reconstruction procedure crucially relies on the ideas underlying the proof system of Goldwasser, Kalai, and Rothblum (J. ACM 2015).", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00021"}, {"primary_key": "2134256", "vector": [], "sparse_vector": [], "title": "Non-adaptive vs Adaptive Queries in the Dense Graph Testing Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the relation between the query complexity of adaptive and non-adaptive testers in the dense graph model. It has been known for a couple of decades that the query complexity of non-adaptive testers is at most quadratic in the query complexity of adaptive testers. We show that this general result is essentially tight; that is, there exist graph properties for which any non-adaptive tester must have query complexity that is almost quadratic in the query complexity of the best general (i.e., adaptive) tester. More generally, for every $q$ : $\\mathbb{N}\\rightarrow \\mathbb{N}$ such that $q(n)\\leq \\sqrt{n}$ and constant $c\\in[1,2]$ , we show a graph property that is testable in $\\Theta(q(n))$ queries, but its non-adaptive query complexity is $\\Theta(q(n)^{c})$ , omitting poly(log $n$ ) factors and ignoring the effect of the proximity parameter $\\epsilon$ . Furthermore, the upper bounds hold for one-sided error testers, and are at most quadratic in $1/\\epsilon$ . These results are obtained through the use of general reductions that transport properties of ordered structured (like bit strings) to those of unordered structures (like unlabeled graphs). The main features of these reductions are query-efficiency and preservation of distance to the properties. This method was initiated in our prior work (ECCC, TR20-149), and we significantly extend it here.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00035"}, {"primary_key": "2134257", "vector": [], "sparse_vector": [], "title": "Exact and Approximate <PERSON><PERSON> Counting in Degenerate Graphs: New Algorithms, Hardness Results, and Complexity Dichotomies.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study the problems of counting the homomorphisms, the copies, and the induced copies of a $k$ -vertex graph $H$ in a $d$ -degenerate $n$ -vertex graph $G$ . By leveraging a new family of graph-minor obstructions called F-gadgets, we establish explicit and exhaustive complexity classifications for counting copies and induced copies. For instance., we show that the copies of $H$ in $G$ can be counted in time $f(k, d)n^{\\max(1,\\mathsf{imn}(H))} \\log n$ , where $f$ is some computable function and $\\mathsf{imn} (H)$ is the size of the largest induced matching of $H$ ; and that whenever the class of allowed patterns has arbitrarily large induced matchings, no algorithm runs in time $f(k, d)n^{o(\\mathsf{imn}(H)/\\log \\mathsf{imn}(H))}$ for any function $f$ , unless the Exponential Time Hypothesis fails. A similar result holds for counting induced copies, with the independence number $\\alpha(H)$ in place of $\\mathsf{imn}(H)$ . These results imply complexity dichotomies, into fixed-parameter tractable versus #W[1]-hard cases, which parallel the well-known dichotomies when $d$ is not a parameter. Our results also imply the #W[1]-hardness of counting several patterns, such as $k$ -matchings and $k$ -trees, in $d$ - degenerate graphs. We also give new hardness results and approximation algorithms for generalized pattern counting (i.e., counting patterns with a given property) in degenerate graphs.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00036"}, {"primary_key": "2134258", "vector": [], "sparse_vector": [], "title": "Random walks and forbidden minors III: $\\text{poly}\\left(d\\varepsilon {-1}\\right)$-time partition oracles for minor-free graph classes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Consider the family of bounded degree graphs in any minor-closed family (such as planar graphs). Let d be the degree bound and $n$ be the number of vertices of such a graph. Graphs in these classes have hyperfinite decompositions, where, one removes a small fraction of edges of the graph controlled by a proximity parameter to get connected components of size independent of $n$ . An important tool for sublinear algorithms and property testing for such classes is the partition oracle, introduced by the seminal work of <PERSON><PERSON><PERSON><PERSON><PERSON> (FOCS 2009). A partition oracle is a local procedure that gives consistent access to a hyperfinite decomposition, without any preprocessing. Given a query vertex v, the partition oracle outputs the component containing v in time independent of n. All the answers are consistent with a single hyperfinite decomposition. The partition oracle of <PERSON><PERSON><PERSON><PERSON> et al. runs in time exponential in the proximity parameter per query. They pose the open problem of whether partition oracles which run in time polynomial in reciprocal of proximity parameter can be built. <PERSON><PERSON><PERSON> (ICALP 2013) give a refinement of the previous approach, to get a partition oracle that runs in quasipolynomial time per query. In this paper, we resolve this open problem and give polynomial time partition oracles (in reciprocal of proximity parameter) for bounded degree graphs in any minor-closed family. Unlike the previous line of work based on combinatorial methods, we employ techniques from spectral graph theory. We build on a recent spectral graph theoretical toolkit for minor-closed graph families, introduced by the authors to develop efficient property testers. A consequence of our result is an efficient property tester for any monotone and additive with running time property of minor-closed families (such as bipartite planar graphs). Our result also gives query efficient algorithms for additive approximations for problems such as maximum matching, minimum vertex cover, maximum independent set, and minimum dominating set for these graph families.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00034"}, {"primary_key": "2134259", "vector": [], "sparse_vector": [], "title": "A Nearly Optimal All-Pairs Min-Cuts Algorithm in Simple Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>"], "summary": "We give an $n^{2+o(1)}$ -time algorithm for finding $s-t$ min-cuts for all pairs of vertices $s$ and $t$ in a simple, undirected graph on $n$ vertices. We do so by constructing a Gomory-Hu tree (or cut equivalent tree) in the same running time, thereby improving on the recent bound of $\\tilde{O}(n^{2.5})$ by <PERSON><PERSON><PERSON> et al. (STOC 2021). Our running time is nearly optimal as a function of $n$ .", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00111"}, {"primary_key": "2134260", "vector": [], "sparse_vector": [], "title": "Proof of the Contiguity Conjecture and Lognormal Limit for the Symmetric Perceptron.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the symmetric binary perceptron model, a simple model of neural networks that has gathered significant attention in the statistical physics, information theory and probability theory communities, with recent connections made to the performance of learning algorithms in <PERSON><PERSON><PERSON> et al. '15. We establish that the partition function of this model, normalized by its expected value, converges to a log-normal distribution. As a consequence, this allows us to establish several conjectures for this model: (i) it proves the contiguity conjecture of <PERSON><PERSON> et al. '19 between the planted and unplanted models in the satisfiable regime; (ii) it establishes the sharp threshold conjecture; (iii) it proves the frozen 1-RSB conjecture in the symmetric case, conjectured first by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> '<PERSON> in the asymmetric case. In a recent work of <PERSON><PERSON><PERSON> '21, the last two conjectures were also established by proving that the partition function concentrates on an exponential scale, under an analytical assumption on a real-valued function. This left open the contiguity conjecture and the lognor-mal limit characterization, which are established here unconditionally, with the analytical assumption verified. In particular, our proof technique relies on a dense counter-part of the small graph conditioning method, which was developed for sparse models in the celebrated work of <PERSON> and <PERSON><PERSON><PERSON>.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00041"}, {"primary_key": "2134261", "vector": [], "sparse_vector": [], "title": "APMF &lt; APSP? Gomory-<PERSON> for Unweighted Graphs in Almost-Quadratic Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We design an $n^{2+o(1)}$ -time algorithm that constructs a cut-equivalent (Gomory-Hu) tree of a simple graph on $n$ nodes. This bound is almost-optimal in terms of $n$ , and it improves on the recent $\\tilde{O}(n^{2.5})$ bound by the authors (STOC 2021), which was the first to break the cubic barrier. Consequently, the All-Pairs Maximum-Flow (APMF) problem has time complexity $n^{2+o(1)}$ , and for the first time in history, this problem can be solved faster than All-Pairs Shortest Paths (APSP). We further observe that an almost-linear time algorithm (in terms of the number of edges $m$ ) is not possible without first obtaining a subcubic algorithm for multigraphs. Finally, we derandomize our algorithm, obtaining the first subcubic deterministic algorithm for Gomory-Hu Tree in simple graphs, showing that randomness is not necessary for beating the $n-1$ times max-flow bound from 1961. The upper bound is $\\tilde{O}(n^{2\\frac{2}{3}})$ and it would improve to $n^{2+o(1)}\\ \\mathbf{i}\\mathbf{f}$ there is a deterministic single-pair maximum-flow algorithm that is almost-linear. The key novelty is in using a \"dynamic pivot\" technique instead of the randomized pivot selection that was central in recent works.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00112"}, {"primary_key": "2134262", "vector": [], "sparse_vector": [], "title": "A Matrix Trickle-Down Theorem on Simplicial Complexes and Applications to Sampling Colorings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that the natural Glauber dynamics mixes rapidly and generates a random proper edge-coloring of a graph with maximum degree $\\Delta$ whenever the number of colors is at least $q\\geq(\\frac{10}{3}+\\epsilon)\\Delta$ , where $\\epsilon &gt; 0$ is arbitrary and the maximum degree satisfies $\\Delta\\geq C$ for a constant $C=C(\\epsilon)$ depending only on $\\epsilon$ , For edge-colorings, this improves upon prior work [Vig99; Che+19] which show rapid mixing when $q\\geq(\\frac{11}{3}-\\epsilon_{0})\\Delta$ , where $\\epsilon_{0}\\approx 10^{-5}$ is a small fixed constant. At the heart of our proof, we establish a matrix trickle-down theorem, generalizing <PERSON><PERSON><PERSON>'s influential result, as a new technique to prove that a high dimensional simplicial complex is a local spectral expander.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00024"}, {"primary_key": "2134263", "vector": [], "sparse_vector": [], "title": "Covering Polygons is Even Harder.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In the Minimum Convex Cover (MCC) problem, we are given a simple polygon P and an integer k , and the question is if there exist k convex polygons whose union is P . It is known that MCC is NP-hard [<PERSON><PERSON><PERSON><PERSON> & <PERSON>ckhow: Covering polygons is hard, FOCS 1988/Journal of Algorithms 1994] and in ∃R [<PERSON><PERSON><PERSON>: The complexity of computing minimum convex covers for polygons, <PERSON> 1982]. We prove that MCC is ∃R -hard, and the problem is thus ∃R -complete. In other words, the problem is equivalent to deciding whether a system of polynomial equations and inequalities with integer coefficients has a real solution. If a cover for our constructed polygon exists, then so does a cover consisting entirely of triangles. As a byproduct, we therefore also establish that it is ∃R -complete to decide whether k triangles cover a given polygon. The issue that it was not known if finding a minimum cover is in N P has repeatedly been raised in the literature, and it was mentioned as a “long-standing open question” already in 2001 [Eidenbenz & Widmayer: An approximation algorithm for minimum convex cover with logarithmic performance guarantee, ESA 2001/SIAM Journal on Computing 2003]. We prove that assuming the widespread belief that NP≠∃R , the problem is not in N P. An implication of the result is that many natural approaches to finding small covers are bound to give suboptimal solutions in some cases, since irrational coordinates of arbitrarily high algebraic degree can be needed for the corners of the pieces in an optimal solution.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00045"}, {"primary_key": "2134264", "vector": [], "sparse_vector": [], "title": "MAJORITY-3SAT (and Related Problems) in Polynomial Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Majority-SAT (a.k.a. MAJ-SAT) is the problem of determining whether an input n-variable formula in conjunctive normal form (CNF) has at least 2^(n-1) satisfying assignments. Majority-SAT and related problems have been studied extensively in various AI communities interested in the complexity of probabilistic planning and inference. Although Majority-SAT has been known to be PP-complete for over 40 years, the complexity of a natural variant has remained open: Majority-kSAT, where the input CNF formula is restricted to have clause width at most k. We prove that for every k, Majority-kSAT is in P; in fact, the problem can be solved in linear time (whereas the previous best-known algorithm ran in exponential time). More generally, for any positive integer k and constant p in (0,1) with bounded denominator, we give an algorithm that can determine whether a given k-CNF has at least p(2^n) satisfying assignments, in deterministic linear time. We find these results surprising, as many analogous problems which are hard for CNF formulas remain hard when restricted to 3-CNFs. Our algorithms have interesting positive implications for counting complexity and the complexity of inference, significantly reducing the known complexities of related problems such as E-MAJ-kSAT and MAJ-MAJ-kSAT. Our results immediately extend to arbitrary Boolean CSPs with constraints of arity k. At the heart of our approach is an efficient method for solving threshold counting problems by extracting and analyzing various sunflowers found in the corresponding set system of a k-CNF. Exploring the implications of our results, we find that the tractability of Majority-kSAT is somewhat fragile, in intriguing ways. For the closely related GtMajority-SAT problem (where we ask whether a given formula has greater than 2^(n-1) satisfying assignments) which is also known to be PP-complete, we show that GtMajority-kSAT is in P for k at most 3, but becomes NP-complete for k at least 4. We also show that for Majority-SAT on k-CNFs with one additional clause of arbitrary width, the problem is PP-complete for k at least 4, is NP-hard for k=3, and remains in P for k=2. These results are counterintuitive, because the \"natural\" classifications of these problems would have been PP-completeness, and because there is a stark difference in the complexity of GtMajority-kSAT and Majority-kSAT for all k at least 4.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00103"}, {"primary_key": "2134265", "vector": [], "sparse_vector": [], "title": "Feature Purification: How Adversarial Training Performs Robust Deep Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>zhi Li"], "summary": "Despite the empirical success of using adversarial training to defend deep learning models against adversarial perturbations, so far, it still remains rather unclear what the principles are behind the existence of adversarial perturbations, and what adversarial training does to the neural network to remove them. In this paper, we present a principle that we call feature purification, where we show one of the causes of the existence of adversarial examples is the accumulation of certain small dense mixtures in the hidden weights during the training process of a neural network; and more importantly, one of the goals of adversarial training is to remove such mixtures to purify hidden weights. We present both experiments on the CIFAR-10 dataset to illustrate this principle, and a theoretical result proving that for certain natural classification tasks, training a two-layer neural network with ReLU activation using randomly initialized gradient descent indeed satisfies this principle. Technically, we give, to the best of our knowledge, the first re-sult proving that the following two can hold simultaneously for training a neural network with ReLU activation. (1) Training over the original data is indeed non-robust to small adversarial perturbations of some radius. (2) Adversarial training, even with an empirical perturbation algorithm such as FGM, can in fact be provably robust against any perturbations of the same radius. Finally, we also prove a complexity lower bound, showing that low complexity models such as linear classifiers, low-degree polynomials, or even the neural tangent kernel for this network, cannot defend against perturbations of this same radius, no matter what algorithms are used to train them. 1 1 The full version of this paper can be found at https://arxiv.org/abs/-2005.l0190.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00098"}, {"primary_key": "2134266", "vector": [], "sparse_vector": [], "title": "A Theory of PAC Learnability of Partial Concept Classes.", "authors": ["Noga Alon", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We extend the classical theory of PAC learning in a way which allows to model a rich variety of practical learning tasks where the data satisfy special properties that ease the learning process. For example, tasks where the distance of the data from the decision boundary is bounded away from zero, or tasks where the data lie on a lower dimensional surface. The basic and simple idea is to consider partial concepts: these are functions that can be undefined on certain parts of the space. When learning a partial concept, we assume that the source distribution is supported only on points where the partial concept is defined. This way, one can naturally express assumptions on the data such as lying on a lower dimensional surface, or that it satisfies margin conditions. In contrast, it is not at all clear that such assumptions can be expressed by the traditional PAC theory using learnable total concept classes, and in fact we exhibit easy-to-learn partial concept classes which provably cannot be captured by the traditional PAC theory. This also resolves, in a strong negative sense, a question posed by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (2019). We characterize PAC learnability of partial concept classes and reveal an algorithmic landscape which is fundamentally different than the classical one. For example, in the classical PAC model, learning boils down to Empirical Risk Minimization (ERM). This basic principle follows from Uniform Convergence and the Fundamental Theorem of PAC Learning (Vapnik and Chervon<PERSON>kis, 1971, 1974b; <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, 1989; <PERSON>, 1993). In stark contrast, we show that the ERM principle fails spectacularly in explaining learnability of partial concept classes. In fact, we demonstrate classes that are incredibly easy to learn, but such that any algorithm that learns them must use an hypothesis space with unbounded VC dimension. We also find that the sample compression conjecture of Littlestone and Warmuth fails in this setting. Our impossibility results hinge on the recent breakthroughs in communication complexity and graph theory by Göös (2015); Ben-David, Hatami, and Tal (2017); Balodis, Ben-David, Göös, Jain, and Kothari (2021). Thus, this theory features problems that cannot be represented in the traditional way and cannot be solved in the traditional way. We view this as evidence that it might provide insights on the nature of learnability in realistic scenarios which the classical theory fails to explain. We include in the paper suggestions for future research and open problems in several contexts, including combinatorics, geometry, and learning theory.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00070"}, {"primary_key": "2134267", "vector": [], "sparse_vector": [], "title": "Quantum learning algorithms imply circuit lower bounds.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Alex <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We establish the first general connection between the design of quantum algorithms and circuit lower bounds. Specifically, let $\\mathfrak{C}$ be a class of polynomial-size concepts, and suppose that $\\mathfrak{C}$ can be PAC-learned with membership queries under the uniform distribution with error $1/2 -\\gamma$ by a time $T$ quantum algorithm. We prove that if $\\gamma^{2}\\cdot T \\ll 2^{n} /n$ , then $\\mathsf{BQE}\\not\\subset \\mathfrak{C}$ , where $\\mathsf{BQE} = \\mathsf{BQTIME}[2^{O(n)}]$ is an exponential-time analogue of $\\mathsf{BQP}$ . This result is optimal in both $\\gamma$ and $T$ , since it is not hard to learn any class $\\mathfrak{C}$ of functions in (classical) time $T=2^{n}$ (with no error), or in quantum time $T= \\mathsf{poly}(n)$ with error at most $1/2-\\Omega(2^{-n/2})$ via Fourier sampling. In other words, even a marginal quantum speedup over these generic learning algorithms would lead to major consequences in complexity lower bounds. As a consequence, our result shows that the study of quantum learning speedups is intimately connected to fundamental open problems about algorithms, quantum computing, and complexity theory. Our proof builds on several works in learning theory, pseudorandomness, and computational complexity, and on a connection between non-trivial classical learning algorithms and circuit lower bounds established by <PERSON> and <PERSON>hanam (CCC 2017). Extending their approach to quantum learning algorithms turns out to create significant challenges, since extracting computational hardness from a quantum computation is inherently more complicated. To achieve that, we show among other results how pseudorandom generators imply learning-to-lower-bound connections in a generic fashion, construct the first conditional pseudorandom generator secure against uniform quantum computations, and extend the local list-decoding algorithm of Impagliazzo, Jaiswal, Kabanets and Wigderson (SICOMP 2010) to quantum circuits via a delicate analysis. We believe that these contributions are of independent interest and might find other applications.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00062"}, {"primary_key": "2134268", "vector": [], "sparse_vector": [], "title": "Faster Sparse Minimum Cost Flow by Electrical Flow Localization.", "authors": ["Kyriakos Axiotis", "Aleksander Madry", "<PERSON>"], "summary": "We give an $\\tilde{O}(m^{3/2-1/762}\\log(U+W))$ time algorithm for minimum cost flow with capacities bounded by $U$ and costs bounded by $W$ . For sparse graphs with general capacities, this is the first algorithm to improve over the $\\tilde{O}(m^{3/2}\\log^{O(1)}(U+W))$ running time obtained by an appropriate instantiation of an interior point method [<PERSON><PERSON><PERSON>, 2008]. Our approach is extending the framework put forth in [Gao-Liu-Pen<PERSON>, 2021] for computing the maximum flow in graphs with large capacities and, in particular, demonstrates how to reduce the problem of computing an electrical flow with general demands to the same problem on a sublinear-sized set of vertices—even if the demand is supported on the entire graph. Along the way, we develop new machinery to assess the importance of the graph's edges at each phase of the interior point method optimization process. This capability relies on establishing a new connections between the electrical flows arising inside that optimization process and vertex distances in the corresponding effective resistance metric.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00059"}, {"primary_key": "2134269", "vector": [], "sparse_vector": [], "title": "Unambiguous DNFs and Alon-Saks-Seymour.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We exhibit an unambiguous $k$ -DNF formula that requires CNF width $\\tilde\\Omega(k^{2})$ , which is optimal up to logarithmic factors. As a consequence, we get a near-optimal solution to the <PERSON><PERSON> problem in graph theory (posed in 1991), which asks: How large a gap can there be between the chromatic number of a graph and its biclique partition number? Our result is also known to imply several other improved separations in query and communication complexity.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00020"}, {"primary_key": "2134270", "vector": [], "sparse_vector": [], "title": "Harmonic Persistent Homology (extended abstract).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce harmonic persistent homology spaces for filtrations of finite simplicial complexes. As a result we can associate concrete subspaces of cycles to each bar of the barcode of the filtration. We prove stability of the harmonic persistent homology subspaces under small perturbations of functions defining them. We relate the notion of \"essential simplices,\" introduced in an earlier work to identify simplices which play a significant role in the birth of a bar, with that of harmonic persistent homology. We prove that the harmonic representatives of simple bars maximizes the \"relative essential content\" amongst all representatives of the bar, where the relative essential content is the weight a particular cycle puts on the set of essential simplices.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00110"}, {"primary_key": "2134271", "vector": [], "sparse_vector": [], "title": "Testability of relations between permutations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We initiate the study of property testing problems concerning relations between permutations. In such problems, the input is a tuple (σ 1 , …, σ d ) of permutations on \\{1, \\ldots, n\\}, and one wishes to determine whether this tuple satisfies a certain system of relations E, or is far from every tuple that satisfies E. If this computational problem can be solved by querying only a small number of entries of the given permutations, we say that E is testable. For example, when d=2 and E consists of the single relation \\mathrm{XY}= \\mathrm{YX}, this corresponds to testing whether σ 1 σ 2 =σ 2 σ 1 , where σ 1 σ 2 and σ 2 σ 1 denote composition of permutations. We define a collection of graphs, naturally associated with the system E, that encodes all the information relevant to the testability of E. We then prove two theorems that provide criteria for testability and non-testability in terms of expansion properties of these graphs. By virtue of a deep connection with group theory, both theorems are applicable to wide classes of systems of relations. In addition, we formulate the well-studied group-theoretic notion of stability in permutations as a special case of the testa-bility notion above, interpret all previous works on stability as testability results, survey previous results on stability from a computational perspective, and describe many directions for future research on stability and testability. This is an extended abstract. The full version is available at https://arxiv.org/abs/2011.05234. All references beyond Sections I and II refer to the full version.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00037"}, {"primary_key": "2134272", "vector": [], "sparse_vector": [], "title": "Time-Optimal Sublinear Algorithms for Matching and Vertex Cover.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We study the problem of estimating the size of maximum matching and minimum vertex cover in sub linear time. Denoting the number of vertices by $n$ and the average degree in the graph by $\\overline{d}$ , we obtain the following results for both problems which are all provably time-optimal up to polylogarithmic factors: 1 1 The $\\tilde{O}(\\cdot)$ notation hides polylog $n$ factors throughout the paper. •A multiplicative $(2+\\varepsilon)$ -approximation that takes $\\tilde{O}(n/\\varepsilon^{2})$ time using adjacency list queries. •A multiplicative-additive $(2,\\ \\varepsilon n)$ -approximation that takes $\\tilde{O}((\\overline{d}+1)/\\varepsilon^{2})$ time using adjacency list queries. •A multiplicative-additive $(2,\\ \\varepsilon n)$ -approximation that takes $\\tilde{O}(n/\\varepsilon^{3})$ time using adjacency matrix queries. Our main contribution and the key ingredient of the bounds above is a near-tight analysis of the average query complexity of randomized greedy maximal matching which improves upon a seminal result of <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> $[\\text{STOC}^{\\prime} 09]$ .", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00089"}, {"primary_key": "2134273", "vector": [], "sparse_vector": [], "title": "Linear Probing Revisited: Tombstones Mark the Demise of Primary Clustering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The linear-probing hash table is one of the oldest and most widely used data structures in computer science. However, linear probing famously comes with a major draw-back: as soon as the hash table reaches a high memory utilization, elements within the hash table begin to cluster together, causing insertions to become slow. This phenomenon, now known as primary clustering, was first captured by <PERSON> in 1963; at a load factor of $1 -1/x$ , the expected time per insertion is $\\Theta(x^{2})$ , rather than the more desirable $\\Theta(x)$ . We show that there is more to the story than the classic analysis would seem to suggest. It turns out that small design decisions in how deletions are implemented have dramatic effects on the asymptotic performance of insertions. If these design decisions are made correctly, then even a hash table that is continuously at a load factor $1-\\Theta(1/x)$ can achieve average insertion time $\\tilde{O}(x)$ . A key insight is that the tombstones left behind by deletions cause a surprisingly strong \"anti-clustering\" effect, and that when insertions and deletions are one-for-one, the anti-clustering effects of deletions actually overpower the clustering effects of insertions. We also present a new variant of linear probing, which we call graveyard hashing, that completely eliminates primary clustering on any sequence of operations. If, when an operation is performed, the current load factor is $1 -1/x$ for some $x$ , then the expected cost of the operation is $O(x)$ . One corollary is that, in the external-memory model with a data block size of $B$ , graveyard hashing offers the following remarkable guarantee: at any load factor $1 -1/x$ satisfying $x=o(B)$ , graveyard hashing achieves $1 +o(1)$ expected block transfers per operation. Past external-memory hash tables have only been able to offer a $1 +o(1)$ guarantee when the block size $B$ is at least $\\Omega(x^{2})$ . Our results come with actionable lessons for both theoreticians and practitioners, in particular, that well-designed use of tombstones can completely change the asymptotic landscape of how the linear probing behaves (and if there are no deletions).", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00115"}, {"primary_key": "2134274", "vector": [], "sparse_vector": [], "title": "Deterministic Decremental SSSP and Approximate Min-Cost Flow in Almost-Linear Time.", "authors": ["<PERSON>", "<PERSON>", "Thatchaphol <PERSON>"], "summary": "In the decremental single-source shortest paths problem, the goal is to maintain distances from a fixed source $s$ to every vertex $v$ in an m-edge graph undergoing edge deletions. In this paper, we conclude a long line of research on this problem by showing a near-optimal deterministic data structure that maintains (1 + E) -approximate distance estimates and runs in m 1+o(1) total update time. Our result, in particular, removes the oblivious adversary assumption required by the previous breakthrough result by <PERSON><PERSON><PERSON> et al. [FOCS'14], which leads to our second result: the first almost-linear time algorithm for (1 - E) -approximate min-cost flow in undirected graphs where capacities and costs can be taken over edges and vertices. Previously, algorithms for max flow with vertex capacities, or min-cost flow with any capacities required super-linear time. Our result essentially completes the picture for approximate flow in undirected graphs. The key technique of the first result is a novel framework that allows us to treat low-diameter graphs like expanders. This allows us to harness expander properties while bypassing shortcomings of expander decomposition, which almost all previous expander-based algorithms needed to deal with. For the second result, we break the notorious flow-decomposition barrier from the multiplicative-weight-update framework using randomization.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00100"}, {"primary_key": "2134275", "vector": [], "sparse_vector": [], "title": "Multiway Online Correlated Selection.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give a 0.5368-competitive algorithm for edge-weighted online bipartite matching. Prior to our work, the best competitive ratio was 0.5086 due to <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> (FOCS 2020). They achieved their breakthrough result by developing a subroutine called online correlated selection (OCS) which takes as input a sequence of pairs and selects one item from each pair. Importantly, the selections the OCS makes are negatively correlated. We achieve our result by defining multiway OCSes which receive arbitrarily many elements at each step, rather than just two. In addition to better competitive ratios, our formulation allows for a simpler reduction from edge-weighted online bipartite matching to OCSes. While <PERSON><PERSON><PERSON><PERSON> et al. used a factor-revealing linear program to optimize the competitive ratio, our analysis directly connects the competitive ratio to the parameters of the multiway OCS. Finally, we show that the formulation of <PERSON> et al. can achieve a competitive ratio of at most 0.5239, confirming that multiway OCSes are strictly more powerful.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00124"}, {"primary_key": "2134276", "vector": [], "sparse_vector": [], "title": "Properly learning decision trees in almost polynomial time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give an $n^{O(\\log\\log n)}$ -time membership query algorithm for properly and agnostically learning decision trees under the uniform distribution over $\\{\\pm 1\\}^{n}$ . Even in the realizable setting, the previous fastest runtime was $n^{O(\\log n)}$ , a consequence of a classic algorithm of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>. Our algorithm shares similarities with practical heuristics for learning decision trees, which we augment with additional ideas to circumvent known lower bounds against these heuristics. To analyze our algorithm, we prove a new structural result for decision trees that strengthens a theorem of <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. While the OSSS theorem says that every decision tree has an influential variable, we show how every decision tree can be \"pruned\" so that every variable in the resulting tree is influential.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00093"}, {"primary_key": "2134277", "vector": [], "sparse_vector": [], "title": "Exponential Lower Bounds for Locally Decodable and Correctable Codes for Insertions and Deletions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Locally Decodable Codes (LDCs) are error-correcting codes for which individual message symbols can be quickly recovered despite errors in the codeword. LDCs for Hamming errors have been studied extensively in the past few decades, where a major goal is to understand the amount of redundancy that is necessary and sufficient to decode from large amounts of error, with small query complexity. Despite exciting progress, we still don't have satisfactory answers in several important parameter regimes. For example, in the case of 3-query LDCs, the gap between existing constructions and lower bounds is superpolynomial in the message length. In this work we study LDCs for insertion and deletion errors, called Insdel LDCs. Their study was initiated by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (Information Theoretic Security, 2015), who gave a reduction from Hamming LDCs to Insdel LDCs with a small blowup in the code parameters. On the other hand, the only known lower bounds for Insdel LDCs come from those for Hamming LDCs, thus there is no separation between them. Here we prove new, strong lower bounds for the existence of Insdel LDCs. In particular, we show that 2-query linear Insdel LDCs do not exist, and give an exponential lower bound for the length of all q-query Insdel LDCs with constant q. For $q$ ≥ 3 our bounds are exponential in the existing lower bounds for Hamming LDCs. Furthermore, our exponential lower bounds continue to hold for adaptive decoders, and even in private-key settings where the encoder and decoder share secret randomness. This exhibits a strict separation between Hamming LDCs and Insdel LDCs. Our strong lower bounds also hold for the related notion of Insdel LCCs (except in the private-key setting), due to an analogue to the Insdel notions of a reduction from Hamming LCCs to LDCs. Our techniques are based on a delicate design and analysis of hard distributions of insertion and deletion errors, which depart significantly from typical techniques used in analyzing Hamming LDCs.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00077"}, {"primary_key": "2134278", "vector": [], "sparse_vector": [], "title": "Parameterized Problems Complete for Nondeterministic FPT time and Logarithmic Space.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Céline M. F. <PERSON>"], "summary": "Let XNLP be the class of parameterized prob-lems such that an instance of size $n$ with parameter $k$ can be solved nondeterministically in time $f$ ( $k$ ) nO (1) and space f (k) log(n) (for some computable function f). We give a wide variety of XNLP-complete problems, such as List Coloringand Precoloring Extensionwith pathwidth as parameter, Scheduling Of Jobs With Precedence Constraints, with both number of machines and partial order width as parameter, Bandwidthand variants of Weighted Cnf-satisfiability and reconfiguration problems. In particular, this implies that all these problems are W[ $t$ ]-hard for all t. This also answers a long standing question on the parameterized complexity of the Bandwidth problem.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00027"}, {"primary_key": "2134279", "vector": [], "sparse_vector": [], "title": "Chow-Liu++: Optimal Prediction-Centric Learning of Tree Ising Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of learning a tree-structured Ising model from data, such that subsequent predictions computed using the model are accurate. Con-cretely, we aim to learn a model such that posteriors $p$ (Xi| X s) for small sets of variables $S$ are accurate. Since its introduction more than 50 years ago, the <PERSON><PERSON><PERSON> algorithm, which efficiently computes the maximum likelihood tree, has been the benchmark algorithm for learning tree-structured graphical models. A bound on the sample complexity of the <PERSON><PERSON><PERSON> algorithm with respect to the prediction-centric local total variation loss was shown in [7]. While those results demonstrated that it is possible to learn a useful model even when recovering the true underlying graph is impossible, their bound depends on the maximum strength of interactions and thus does not achieve the information-theoretic optimum. In this paper, we introduce a new algorithm that carefully combines elements of the <PERSON><PERSON><PERSON> algorithm with tree metric reconstruction methods to efficiently and optimally learn tree Ising models under a prediction-centric loss. Our algorithm is robust to model misspecification and adver-sarial corruptions. In contrast, we show that the celebrated Chow- Liu algorithm can be arbitrarily suboptimal.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00049"}, {"primary_key": "2134280", "vector": [], "sparse_vector": [], "title": "Noise and the Frontier of Quantum Supremacy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Noise is the defining feature of the NISQ era, but it remains unclear if noisy quantum devices are capable of quantum speedups. Quantum supremacy experiments have been a major step forward, but gaps remain between the theory behind these experiments and their actual implementations. In this work we initiate the study of the complexity of quantum random circuit sampling experiments with realistic amounts of noise. Actual quantum supremacy experiments have high levels of uncorrected noise and exponentially decaying fidelities. It is natural to ask if there is any signal of exponential complexity in these highly noisy devices. Surprisingly, we show that it remains hard to compute the output probabilities of noisy random quantum circuits without error correction. More formally, so long as the noise rate of the device is below the error detection threshold, we show it is #P-hard to compute the output probabilities of random circuits with a constant rate of noise per gate. This hardness persists even though these probabilities are exponentially close to uniform. Interestingly these hardness results also have implications for the complexity of experiments in a low-noise setting. The issue here is that prior hardness results for computing output probabilities of random circuits are not robust enough to imprecision to connect with the <PERSON> argument for hardness of sampling from circuits with constant fidelity. We exponentially improve the robustness of prior results to imprecision, both in the cases of Random Circuit Sampling and BosonSampling. In the latter case we bring the proven hardness within a constant factor in the exponent of the robustness required for hardness of sampling for the first time. We then show that our results are in tension with one another -- the high-noise result implies the low-noise result is essentially optimal, even with generalizations of our techniques.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00127"}, {"primary_key": "2134281", "vector": [], "sparse_vector": [], "title": "Statistically Near-Optimal Hypothesis Selection.", "authors": ["<PERSON>", "<PERSON>", "Gillat Kol", "<PERSON><PERSON>", "<PERSON>"], "summary": "Hypothesis Selection is a fundamental distribution learning problem where given a comparator-class $\\mathcal{Q}=\\{q_{1}, \\ldots, q_{n}\\}$ of distributions, and a sampling access to an unknown target distribution $p$ , the goal is to output a distribution $q$ such that $\\mathsf{TV}(p, q)$ is close to opt, where $\\mathsf{opt}=\\min\\nolimits_{i}\\{\\mathsf{TV}(p, q_{i})\\}$ and TV (.,.) denotes the total-variation distance. Despite the fact that this problem has been studied since the 19th century, its complexity in terms of basic resources, such as number of samples and approximation guarantees, remains unsettled (this is discussed, e.g., in the charming book by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> <PERSON><PERSON>). This is in stark contrast with other (younger) learning settings, such as PAC learning, for which these complexities are well understood. We derive an optimal 2-approximation learning strategy for the Hypothesis Selection problem, outputting $q$ such that, $\\mathsf{TV}(p, q)\\leq 2\\cdot\\text{opt}+\\varepsilon$ , with a (nearly) optimal sample complexity of $\\tilde{O}(\\log n/\\varepsilon^{2})$ . This is the first algorithm that simultaneously achieves the best approximation factor and sample complexity: previously, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (COLT '19) gave a learner achieving the optimal 2-approximation, but with an exponentially worse sample complexity of $\\tilde{O}(\\sqrt{n}/\\varepsilon^{2.5})$ , and Yatracos (Annals of Statistics '85) gave a learner with optimal sample complexity of $O(\\log n/\\varepsilon^{2})$ but with a sub-optimal approximation factor of 3. We mention that many works in the Density Estimation (a.k.a., Distribution Learning) literature use Hypothesis Selection as a black box subroutine. Our result therefore implies an improvement on the approximation factors obtained by these works, while keeping their sample complexity intact. For example, our result improves the approximation factor of the algorithm of Ashtiani, Ben-David, Harvey, Liaw, and Mehrabian (JACM '20) for agnostic learning of mixtures of gaussians from 9 to 6, while maintaining its nearly-tight sample complexity.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00092"}, {"primary_key": "2134282", "vector": [], "sparse_vector": [], "title": "Tight Space Complexity of the Coin Problem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the coin problem we are asked to distinguish, with probability at least 2/3, between $n\\ i.i.d$ . coins which are heads with probability $\\frac{1}{2}+\\beta$ from ones which are heads with probability $\\frac{1}{2}-\\beta$ . We are interested in the space complexity of the coin problem, corresponding to the width of a read-once branching program solving the problem. The coin problem becomes more difficult as $\\beta$ becomes smaller. Statistically, it can be solved whenever $\\beta= \\Omega(n^{-1/2})$ , using counting. It has been previously shown that for $\\beta=O(n^{-1/2})$ , counting is essentially optimal (equivalently, width $poly (n)$ is necessary [Braverman-Garg-Woodruff FOCS'20]). On the other hand, the coin problem only requires $O(\\log n)$ width for $\\beta &gt; n^{-c}$ for any constant $c &gt; \\log_{2}(\\sqrt{5}-1)\\approx 0.306$ (following low-width simulation of AND-OR tree of [Valiant Journal of Algorithms'84]). In this paper, we close the gap between the bounds, showing a tight threshold between the values of $\\beta=n^{-c}$ where $O(\\log n)$ width suffices and the regime where $poly (n)$ width is needed, with a transition at $c=1/3$ . This gives a complete characterization (up to constant factors) of the memory complexity of solving the coin problem, for all values of bias $\\beta$ . We introduce new techniques in both bounds. For the upper bound, we give a construction based on recursive majority that does not require a memory stack of size $\\log n$ bits. For the lower bound, we introduce new combinatorial techniques for analyzing progression of the success probabilities in read-once branching programs.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00106"}, {"primary_key": "2134283", "vector": [], "sparse_vector": [], "title": "An Invariance Principle for the Multi-slice, with Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given an alphabet size $m\\in\\mathbb{N}$ thought of as a constant, and $\\vec{k}=(k_{1}, \\ldots, k_{m})$ whose entries sum of up $n$ , the $\\vec{k}$ -multi-slice is the set of vectors $x\\in[m]^{n}$ in which each symbol $i\\in[m]$ appears precisely $k_{i}$ times. We show an invariance principle for low-degree functions over the multi-slice, to functions over the product space ( $[m]^{n}, \\mu^{n}$ ) in which $\\mu(i)=k_{i}/n$ . This answers a question raised by [21]. As applications of the invariance principle, we show: 1)An analogue of the \"dictatorship test implies computational hardness\" paradigm for problems with perfect completeness, for a certain class of dictatorship tests. Our computational hardness is proved assuming a recent strengthening of the Unique-Games Conjecture, called the Rich 2-to-1 Games Conjecture. Using this analogue, we show that assuming the Rich 2-to-1 Games Conjecture, (a) there is an $r$ -ary CSP $\\mathcal{P}_{r}$ for which it is NP-hard to distinguish satisfiable instances of the CSP and instances that are at most $\\frac{2r+1}{2^{r}}+o(1)$ satisfiable, and (b) hardness of distinguishing 3-colorable graphs, and graphs that do not contain an independent set of size $o(1)$ . 2)A reduction of the problem of studying expectations of products of functions on the multi-slice to studying expectations of products of functions on correlated, product spaces. In particular, we are able to deduce analogues of the Gaussian bounds from [38] for the multi-slice. 3)In a companion paper, we show further applications of our invariance principle in extremal combinatorics, and more specifically to proving removal lemmas of a wide family of hypergraphs $H$ called $\\zeta$ -forests, which is a natural extension of the well-studied case of matchings.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00030"}, {"primary_key": "2134284", "vector": [], "sparse_vector": [], "title": "The Algorithmic Phase Transition of Random k-SAT for Low Degree Polynomials.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Let $\\Phi$ be a uniformly random $k$ -SAT formula with $n$ variables and $m$ clauses. We study the algorithmic task of finding a satisfying assignment of $\\Phi$ . It is known that satisfying assignments exist with high probability up to clause density $m/n=2^{k} \\log 2-\\frac{1}{2}(\\log 2+1)+o_{k}(1)$ , while the best polynomial-time algorithm known, the Fix algorithm of Coja-Oghlan [1], finds a satisfying assignment at the much lower clause density $(1-o_{k}(1))2^{k}\\log k/k$ . This prompts the question: is it possible to efficiently find a satisfying assignment at higher clause densities? We prove that the class of low degree polynomial algorithms cannot find a satisfying assignment at clause density $(1+o_{k}(1))\\kappa^{\\ast}2^{k}\\log k/k$ for a universal constant $\\kappa^{\\ast}\\approx 4.911$ . This class encompasses Fix, message passing algorithms including Belief and Survey Propagation guided decimation (with bounded or mildly growing number of rounds), and local algorithms on the factor graph. This is the first hardness result for any class of algorithms at clause density within a constant factor of that achieved by Fix. Our proof establishes and leverages a new many-way overlap gap property tailored to random $k$ -SAT.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00038"}, {"primary_key": "2134285", "vector": [], "sparse_vector": [], "title": "Applications of Random Algebraic Constructions to Hardness of Approximation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> C. S.", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we show how one may (efficiently) construct two types of extremal combinatorial objects whose existence was previously conjectural. •Panchromatic Graphs: For fixed $k\\in \\mathbb{N}$ , a $k$ -panchromatic graph is, roughly speaking, a balanced bipartite graph with one partition class equipartitioned into $k$ colour classes in which the common neighbourhoods of panchromatic $k$ -sets of vertices are much larger than those of $k$ -sets that repeat a colour. The question of their existence was raised by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [Combinatorica 2020]. •Threshold Graphs: For fixed $k\\in \\mathbb{N}$ , a $k$ -threshold graph is, roughly speaking, a balanced bipartite graph in which the common neighbourhoods of $k$ -sets of vertices on one side are much larger than those of ( $k+1$ )-sets. The question of their existence was raised by <PERSON> [JACM 2018]. Concretely, we provide probability distributions over graphs from which we can efficiently sample these objects in near linear time. These probability distributions are defined via varieties cut out by (carefully chosen) random polynomials, and the analysis of these constructions relies on machinery from algebraic geometry (such as the Lang-Wei<PERSON> estimate, for example). The technical tools developed to accomplish this might be of independent interest. As applications of our constructions, we show the following conditional time lower bounds on the parameterized set intersection problem where, given a collection of $n$ sets over universe [ $n$ ] and a parameter $k$ , the goal is to find $k$ sets with the largest intersection. •Assuming ETH, for any computable function $F:\\mathbb{N}\\rightarrow \\mathbb{N}$ , no $n^{o(k)}$ -time algorithm can approximate the parameterized set intersection problem up to factor $F(k)$ . This improves considerably on the previously best-known result under ETH due to Lin [JACM 2018], who ruled out any $n^{o(\\sqrt{k})}$ time approximation algorithm for this problem. •Assuming SETH, for every $\\varepsilon &gt; 0$ and any computable function $F:\\mathbb{N} \\rightarrow \\mathbb{N}$ , no $n^{k-\\varepsilon}$ -time algorithm can approximate the parameterized set intersection problem up to factor $F(k)$ . No result of comparable strength was previously known under SETH, even for solving this problem exactly. Both these time lower bounds are obtained by composing panchromatic graphs with instances of the coloured variant of the parameterized set intersection problem (for which tight lower bounds were previously known).", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00032"}, {"primary_key": "2134286", "vector": [], "sparse_vector": [], "title": "Winning the War by (Strategically) Losing Battles: Settling the Complexity of Grundy-Values in Undirected Geography.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We settle two long-standing complexity-theoretical questions—open since 1981 and 1993—in combinatorial game theory (CGT). We prove that the Grundy value of Undirected Geography is PSPACE-complete to compute. This exhibits a stark contrast with a result from 1993 that Undirected Geography is polynomial-time solvable. By distilling to a simple reduction, our proof further establishes a dichotomy theorem, providing a sharp \"phase transition to intractability\": The Grundy value of the game over any degree-three graph is polynomial-time computable, but over degree-four graphs—even when planar & bipartite—is PSPACE-hard. Additionally, we show, for the first time, how to construct Undirected Geography instances with Grundy value *n and size polynomial in n. We strengthen a result from 1981 showing that sums of tractable partisan games are PSPACE-complete in two fundamental ways. First, we extend the result to impartial games, a strict subset of partisan. Second, the 1981 construction is not built from a natural ruleset, instead using a long sum of tailored short-depth game positions. We use the sum of two Undirected Geography positions. Our result also has computational ramification to Sprague-Grundy Theory (1930s) which shows that the Grundy value of the disjunctive sum of any two impartial games can be computed—in polynomial time—from their Grundy values. In contrast, we prove that, assuming PSPACE is not equal to P, there is no general polynomial-time method to summarize two polynomial-time solvable impartial games to efficiently solve their disjunctive sum. Our proof enables us to answer another long-term structural question in the field. We establish the following complexity independence: Unless $\\mathrm{P}= \\text{PSPACE}$ , there is no polynomial-time reduction from winnability in misere-play setting to the Grundy value, and vice versa (in Undirected Geography).", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00119"}, {"primary_key": "2134287", "vector": [], "sparse_vector": [], "title": "LEARN-Uniform Circuit Lower Bounds and Provability in Bounded Arithmetic.", "authors": ["<PERSON>", "Valentine <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We investigate randomized LEARN-uniformity, which captures the power of randomness and equivalence queries (EQ) in the construction of Boolean circuits for an explicit problem. This is an intermediate notion between P-uniformity and non-uniformity motivated by connections to learning, complexity, and logic. Building on a number of techniques, we establish the first unconditional lower bounds against LEARN-uniform circuits: –For all $c\\geq 1$ , there is $L\\in \\mathsf{P}$ that is not computable by circuits of size $n\\cdot(\\log n)^{c}$ generated in deterministic polynomial time with $o(\\log n/\\log\\log n)$ equivalence queries to $L$ . In other words, small circuits for $L$ cannot be efficiently learned using a bounded number of EQs. –For each $k\\geq 1$ , there is $L\\in \\mathsf{NP}$ such that circuits for $L$ of size $O(n^{k})$ cannot be learned in deterministic polynomial time with access to $n^{o(1)}$ EQs. –For each $k\\geq 1$ , there is a problem in promise-ZPP that is not in FZPP-uniform $\\mathsf{SIZE}[n^{k}]$ . –Conditional and unconditional lower bounds against LEARN-uniform circuits in the general setting with randomized uniformity and access to EQs. In all these lower bounds, the learning algorithm may run in arbitrary polynomial time, while the hard problem is computed in some fixed polynomial time. We employ these results to investigate the (un)provability of non-uniform circuit upper bounds (e.g., Is N P contained in $\\mathsf{SIZE}[n^{3}]?)$ in theories of bounded arithmetic. Some questions of this form have been addressed in recent papers of Krajíček-Oliveira (2017), Müller-Bydzovsky (2020), and Bydzovsky-Krajíček-Oliveira (2020) via a mixture of techniques from proof theory, complexity theory, and model theory. In contrast, by extracting computational information from proofs via a direct translation to LEARN-uniformity, we establish robust unprovability theorems that unify, simplify, and extend nearly all previous results. In addition, our lower bounds against randomized LEARN-uniformity yield unprovability results for theories augmented with the dual weak pigeonhole principle, such as APC 1 (Jeřábek, 2007), which is known to formalize a large fragment of modern complexity theory. Finally, we make precise potential limitations of theories of bounded arithmetic such as PV (Cook, 1975) and Jeřábek's theory APC 1 , by showing unconditionally that these theories cannot prove statements like \" $\\mathsf{NP}\\not\\subseteq \\mathsf{BPP}\\wedge \\mathsf{NP}\\subset \\mathsf{io}-\\mathsf{P}/\\mathsf{poly}$ \", i.e., that N P is uniformly \"hard\" but non-uniformly \"easy\" on infinitely many input lengths. In other words, if we live in such a complexity world, then this cannot be established feasibly.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00080"}, {"primary_key": "2134288", "vector": [], "sparse_vector": [], "title": "Sharp Thresholds in Random Simple Temporal Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Malte Renken", "<PERSON>"], "summary": "A graph whose edges only appear at certain points in time is called a temporal graph (among other names). Such a graph is temporally connected if each ordered pair of vertices is connected by a path which traverses edges in chronological order (i.e., a temporal path). In this paper, we consider a simple model of random temporal graph, obtained from an Erdös-R<PERSON> random graph G ~ Gn,p by considering a random permutation π of the edges and interpreting the ranks in π as presence times. We give a thorough study of the temporal connectivity of such graphs and derive implications for the existence of several kinds of sparse spanners. It turns out that temporal reachability in this model exhibits a surprisingly regular sequence of thresholds. In particular, we show that, at p = log $n$ /n, any fixed pair of vertices can a.a.s. reach each other; at 2 log $n$ /n, at least one vertex (and in fact, any fixed vertex) can a.a.s. reach all others; and at 3 log $n$ /n, all the vertices can a.a.s. reach each other, i.e., the graph is temporally connected. Furthermore, the graph admits a temporal spanner of size 2n + o(n) as soon as it becomes temporally connected, which is nearly optimal as 2n - 4 is a lower bound. This result is quite significant because temporal graphs do not admit spanners of size O(n) in general (<PERSON>, <PERSON>, <PERSON>, <PERSON> 2000). In fact, they do not even always admit spanners of size o( $n$ 2 ) (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> 2016). Thus, our result implies that the obstructions found in these works, and more generally, any non-negligible obstruction is statistically insignificant: nearly optimal spanners always exist in random temporal graphs. All the above thresholds are sharp. Carrying the study of temporal spanners a step further, we show that pivotal spanners-i.e., spanners of size 2n - 2 made of two spanning trees glued at a single vertex (one descending in time, the other ascending subsequently)-exist a.a.s. at 4 log $n$ / n, this threshold being also sharp. Finally, we show that optimal spanners (of size 2n - 4) also exist a.a.s. at p = 4 log $n$ /n, Whether this value is a sharp threshold is open, we conjecture that it is. For completeness, we compare the above results to existing results in related areas, including edge-ordered graphs, gossip theory, and population protocols, showing that our results can be interpreted in these settings as well, and that in some cases, they improve known results therein. Finally, we discuss an intriguing connection between our results and Janson's celebrated results on percolation in weighted graphs.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00040"}, {"primary_key": "2134289", "vector": [], "sparse_vector": [], "title": "Minimum Cuts in Directed Graphs via Partial Sparsification.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Danupon <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>", "<PERSON>"], "summary": "We give an algorithm to find a minimum cut in an edge-weighted directed graph with n vertices and m edges in tildeO(n\\max\\{m 2/3}, n\\}) time. This improves on the 30 year old bound of tildeO(nm) obtained by <PERSON><PERSON> and <PERSON><PERSON> for this problem. Using similar techniques, we also obtain tildeO(n 2ϵ 2})-time (1+ϵ)-approximation algorithms for both the minimum edge and minimum vertex cuts in directed graphs, for any fixed ϵ. Before our work, no (1 + ϵ)-approximation algorithm better than the exact runtime of tildeO(nm) is known for either problem. Our algorithms follow a two-step template. In the first step, we employ a partial sparsification of the input graph to preserve a critical subset of cut values approximately. In the second step, we design algorithms to find the (edge/vertex) mincut among the preserved cuts from the first step. For edge mincut, we give a new reduction to tildeO(minn/m 1/3, √n)-calls of any maxflow subroutine, via packing arborescences in the sparsifier. For vertex mincut, we develop new local flow algorithms to identify small unbalanced cuts in the sparsified graph.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00113"}, {"primary_key": "2134290", "vector": [], "sparse_vector": [], "title": "A Polynomial Lower Bound on the Number of Rounds for Parallel Submodular Function Minimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The problem of minimizing a submodular function (SFM) is a common generalization of several fundamental combinatorial optimization problems, including minimum $s-t$ cuts in graphs and matroid intersection. It is well-known that a submodular function can be minimized with only $\\text{poly} (N)$ function evaluation queries where $N$ denotes the universe size. However, all known polynomial query algorithms for SFM are highly adaptive, requiring at least $N$ rounds of adaptivity. A natural question is if SFM can be efficiently solved in a highly parallel manner, namely, with $\\text{poly} (N)$ queries using only poly-logarithmic rounds of adaptivity. An important step towards understanding the adaptivity needed to solve SFM efficiently was taken in the very recent work of <PERSON><PERSON> and <PERSON> who showed that any SFM algorithm with $\\text{poly} (N)$ queries. This left open the possibility of efficient SFM algorithms with poly-logarithmic rounds of adaptivity. In this work, we strongly rule out this possibility by showing that any, possibly randomized, algorithm for submodular function minimization making $\\text{poly} (N)$ queries requires $\\tilde{\\Omega}(N^{1/3})$ rounds of adaptivity. In fact, we show a polynomial lower bound on the number of rounds of adaptivity even for algorithms that make up to $2^{N^{1-\\delta}}$ queries, for any constant $\\delta &gt; 0$ .", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00013"}, {"primary_key": "2134291", "vector": [], "sparse_vector": [], "title": "Improved Extractors for Small-Space Sources.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of extracting random bits from weak sources that are sampled by algorithms with limited memory. This model of small-space sources was introduced by <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (STOC'06), and falls into a line of research initiated by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (FOCS'00) on extracting randomness from weak sources that are sampled by computationally bounded algorithms. Our main results are the following. 1) We obtain near-optimal extractors for small-space sources in the polynomial error regime. For space $s$ sources over $n$ bits, our extractors require just $k\\geq s. \\text{polylog} (n)$ entropy. This is an exponential improvement over the previous best result, which required entropy $k\\geq s^{1,1}\\cdot 2^{\\log^{0.51}n}$ (<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>, STOC'16). 2) We obtain improved extractors for small-space sources in the negligible error regime. For space $s$ sources over $n$ bits, our extractors require entropy $k &gt; n^{1/2+\\delta}\\cdot s^{1/2-\\delta}$ , whereas the previous best result required $k &gt; n^{2/3+\\delta}\\cdot s^{1/3-\\delta}$ (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>, STOC'20). To obtain our first result, the key ingredient is a new reduction from small-space sources to affine sources, allowing us to simply apply a good affine extractor. To obtain our second result, we must develop some new machinery, since we do not have low-error affine extractors that work for low entropy. Our main tool is a significantly improved extractor for adversarial sources, which is built via a simple framework that makes novel use of a certain kind of leakage-resilient extractors (known as cylinder intersection extractors), by combining them with a general type of extremal designs. Our key ingredient is the first derandomization of these designs, which we obtain using new connections to coding theory and additive combinatorics.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00066"}, {"primary_key": "2134292", "vector": [], "sparse_vector": [], "title": "Affine Extractors for Almost Logarithmic Entropy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We give an explicit construction of an affine extractor (over $\\mathbb{F}_{2}$ ) that works for affine sources on $n$ bits with min-entropy $k\\geq\\log n\\cdot(\\log\\log n)^{1+o(1)}$ . This improves prior work of Li (FOCS'16) that requires min-entropy at least $\\text{poly} (\\log n)$ . Our construction is based on the framework of using correlation breakers and resilient functions, a paradigm that was also used by Li. On a high level, the key sources of our improvement are based on the following new ingredients: (i) A new construction of an affine somewhere random extractor, that we use in a crucial step instead of a linear seeded extractor (for which optimal constructions are not known) that was used by Li. (ii) A near optimal construction of a correlation breaker for linearly correlated sources. The construction of our correlation breaker takes inspiration from an exciting line of recent work that constructs two-source extractors for near logarithmic min-entropy.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00067"}, {"primary_key": "2134293", "vector": [], "sparse_vector": [], "title": "Exponential Separations Between Learning With and Without Quantum Memory.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the power of quantum memory for learning properties of quantum systems and dynamics, which is of great importance in physics and chemistry. Many state-of-the-art learning algorithms require access to an additional external quantum memory. While such a quantum memory is not required a priori, in many cases, algorithms that do not utilize quantum memory require much more data than those which do. We show that this trade-off is inherent in a wide range of learning problems. Our results include the following: •We show that to perform shadow tomography on an $n$ -qubit state $\\rho$ with $M$ observables, any algorithm without quantum memory requires $\\tilde{\\Omega}(\\min(M, 2^{n}))$ samples of $\\rho$ in the worst case. Up to log factors, this matches the upper bound of [1], and completely resolves an open question in [2], [3]. •We establish exponential separations between algorithms with and without quantum memory for purity testing, distinguishing scrambling and depolarizing evolutions, and uncovering symmetry in physical dynamics. Our separations improve and generalize prior work of [4] by allowing for a broader class of algorithms without quantum memory. •We give the first tradeoff between quantum memory and sample complexity. More precisely, we prove that to estimate absolute values of all $n$ -qubit Pauli observables, algorithms with $k &lt; n$ qubits of quantum memory require at least $\\Omega(2^{(n-k)/3})$ samples, but there is an algorithm using $n$ -qubit quantum memory which only requires $\\mathcal{O}(n)$ samples. The separations we show are sufficiently large and could already be evident, for instance, with tens of qubits. This provides a concrete path towards demonstrating real-world advantage for learning algorithms with quantum memory.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00063"}, {"primary_key": "2134294", "vector": [], "sparse_vector": [], "title": "Rapid mixing of Glauber dynamics via spectral independence for all degrees.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove an optimal $\\Omega(n^{-1})$ lower bound on the spectral gap of Glauber dynamics for anti-ferromagnetic two-spin systems with $n$ vertices in the tree uniqueness regime. This spectral gap holds for any, including unbounded, maximum degree Δ. Consequently, we have the following mixing time bounds for the models satisfying the uniqueness condition with a slack $\\delta\\in(0,1)$ : • $C(\\delta)n^{2}\\log n$ mixing time for the hardcore model with fugacity $\\leq(1-\\delta)\\lambda_{c}(\\Delta)=(1-\\delta)\\frac{(\\Delta-1)^{\\Delta-1}}{(\\Delta-2)^{\\Delta}}$ ; • $C(\\delta)n^{2}$ mixing time for the Ising model with edge activity $\\beta\\in[\\frac{\\Delta-2+\\delta}{\\Delta-\\delta},\\frac{\\Delta-\\delta}{\\Delta-2+\\delta}]$ ; where the maximum degree Δ may depend on the number of vertices n, and C(δ) depends only on δ. Our proof is built upon the recently developed connections between the Glauber dynamics for spin systems and the high-dimensional expander walks. In particular, we prove a stronger notion of spectral independence, called the complete spectral independence, and use a novel Markov chain called the field dynamics to connect this stronger spectral independence to the rapid mixing of Glauber dynamics for all degrees.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00022"}, {"primary_key": "2134295", "vector": [], "sparse_vector": [], "title": "Learning Deep ReLU Networks Is Fixed-Parameter Tractable.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of learning an unknown ReLU network with respect to Gaussian inputs and obtain the first nontrivial results for networks of depth more than two. We give an algorithm whose running time is a fixed polynomial in the ambient dimension and some (exponentially large) function of only the network's parameters. Our results provably cannot be obtained using gradient-based methods and give the first example of a class of efficiently learnable neural networks that gradient descent will fail to learn. Our bounds depend on the number of hidden units, depth, spectral norm of the weight matrices, and Lipschitz constant of the overall network (we show that some dependence on the Lipschitz constant is necessary). We also give a bound that is doubly exponential in the size of the network but is independent of spectral norm. In contrast, prior work for learning networks of depth three or higher requires exponential time in the ambient dimension, even when the above parameters are bounded by a constant. Additionally, all prior work for the depth-two case requires well-conditioned weights and/or positive coefficients to obtain efficient run-times. Our algorithm does not require these assumptions. Our main technical tool is a type of filtered PCA that can be used to iteratively recover an approximate basis for the subspace spanned by the hidden units in the first layer. Our analysis leverages new structural results on lattice polynomials from tropical geometry.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00073"}, {"primary_key": "2134296", "vector": [], "sparse_vector": [], "title": "Online and Distribution-Free Robustness: Regression and Contextual Bandits with Huber Contamination.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this work we revisit two classic high-dimensional online learning problems, namely linear regression and contextual bandits, from the perspective of adversarial robustness. Existing works in algorithmic robust statistics make strong distributional assumptions that ensure that the input data is evenly spread out or comes from a nice generative model. Is it possible to achieve strong robustness guarantees even without distributional assumptions altogether, where the sequence of tasks we are asked to solve is adaptively and adversarially chosen? We answer this question in the affirmative for both linear regression and contextual bandits. In fact our algorithms succeed where conventional methods fail. In particular we show strong lower bounds against Huber regression and more generally any convex $M$ -estimator. Our approach is based on a novel alternating minimization scheme that interleaves ordinary least-squares with a simple convex program that finds the optimal reweighting of the distribution under a spectral constraint. Our results obtain essentially optimal dependence on the contamination level η, reach the optimal breakdown point, and naturally apply to infinite dimensional settings where the feature vectors are represented implicitly via a kernel map.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00072"}, {"primary_key": "2134297", "vector": [], "sparse_vector": [], "title": "Spectral Independence via Stability and Applications to Holant-Type Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper formalizes connections between stability of polynomials and convergence rates of Markov Chain Monte Carlo (MCMC) algorithms. We prove that if a (multivariate) partition function is nonzero in a region around a real point $\\lambda$ then spectral independence holds at $\\lambda$ . As a consequence, for Holant-type problems (e.g., spin systems) on bounded-degree graphs, we obtain optimal $O(n\\ \\text{log}\\ n)$ mixing time bounds for the single-site update Markov chain known as the Glauber dynamics. Our result significantly improves the running time guarantees obtained via the polynomial interpolation method of Barvi-nok (2017), refined by <PERSON> and <PERSON> (2017). There are a variety of applications of our results. In this paper, we focus on Holant-type (i.e., edge-coloring) problems, including weighted edge covers and weighted even subgraphs. For the weighted edge cover problem (and several natural generalizations) we obtain an $O$ ( $n$ log n) sampling algorithm on bounded-degree graphs. The even subgraphs problem corresponds to the high-temperature expansion of the ferromagnetic Ising model. We obtain an $O$ ( $n$ log n) sampling algorithm for the ferromagnetic Ising model with a nonzero external field on bounded-degree graphs, which improves upon the classical result of <PERSON><PERSON><PERSON> and <PERSON> (1993) for this class of graphs. We obtain further applications to antiferromagnetic two-spin models on line graphs, weighted graph homomorphisms, tensor networks, and more.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00023"}, {"primary_key": "2134298", "vector": [], "sparse_vector": [], "title": "2-norm Flow Diffusion in Near-Linear Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Diffusion is a fundamental graph procedure and has been a basic building block in a wide range of theoretical and empirical applications such as graph partitioning and semi-supervised learning on graphs. In this paper, we study computationally efficient diffusion primitives beyond random walk. We design an near-linear time randomized algorithm for the 2-norm flow diffusion problem, a recently proposed diffusion model based on network flow with demonstrated graph clustering related applications both in theory and in practice. Examples include finding locally-biased low conductance cuts. Using a known connection between the optimal dual solution of the flow diffusion problem and the local cut structure, our algorithm gives an alternative approach for finding such cuts in nearly linear time. From a technical point of view, our algorithm contributes a novel way of dealing with inequality constraints in graph optimization problems. It adapts the high-level algorithmic framework of nearly linear time Laplacian system solvers, but requires several new tools: vertex elimination under constraints, a new family of graph ultra-sparsifiers, and ac-celerated proximal gradient methods with inexact proximal mapping computation. See https://arxiv.org/abs/2105.14629 for the full version of this paper.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00060"}, {"primary_key": "2134299", "vector": [], "sparse_vector": [], "title": "Terminal Embeddings in Sublinear Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently (<PERSON><PERSON>, <PERSON>, <PERSON> 2017) intro-duced the concept of a terminal embedding from one met-ric space to another with a set of designated terminals. In the case where both metric spaces are Euclidean, recently (<PERSON><PERSON>, <PERSON> 2019), following work of (<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> 2018), constructed a terminal embedding with optimal embedding dimension. This generalizes the <PERSON><PERSON><PERSON> lemma, which only preserves distances within the terminal set and not to the set from the rest of space. The downside is that contructing the embedding for a new point required solving a semidefinite program incurring large runtime. Our main contribution in this work is to give a new data structure for computing terminal embeddings. We show how to pre-process the terminal set to obtain an almost linear-space data structure that supports computing the terminal embedding image of any input point in sublinear time. To accomplish this, we leverage tools developed in the context of approximate nearest neighbor search.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00118"}, {"primary_key": "2134300", "vector": [], "sparse_vector": [], "title": "On the Impossibility of Post-Quantum Black-Box Zero-Knowledge in Constant Round.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate the existence of constant-round post-quantum black-box zero-knowledge protocols for NP. As a main result, we show that there is no constant-round post-quantum black-box zero-knowledge argument for NP unless $\\text{NP} \\subseteq \\text{BQP}$ . As constant-round black-box zero-knowledge arguments for NP exist in the classical setting, our main result points out a fundamental difference between post-quantum and classical zero-knowledge protocols. Combining previous results, we conclude that unless $\\text{NP} \\subseteq \\text{BQP}$ , constant-round post-quantum zero-knowledge protocols for NP exist if and only if we use non-black-box techniques or relax certain security requirements such as relaxing standard zero-knowledge to $\\epsilon$ -zero-knowledge. Additionally, we also prove that three-round and public-coin constant-round post-quantum black-box $\\epsilon$ -zero-knowledge arguments for NP do not exist unless $\\text{NP} \\subseteq \\text{BQP}$ .", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00015"}, {"primary_key": "2134301", "vector": [], "sparse_vector": [], "title": "Post-Quantum Succinct Arguments: Breaking the Quantum Rewinding Barrier.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove that <PERSON><PERSON>'s four-message succinct argument system is post-quantum secure in the standard model when instantiated with any probabilistically checkable proof and any collapsing hash function (which in turn exist based on the post-quantum hardness of Learning with Errors). This yields the first post-quantum succinct argument system from any falsifiable assumption. At the heart of our proof is a new quantum rewinding procedure that enables a reduction to repeatedly query a quantum adversary for accepting transcripts as many times as desired. Prior techniques were limited to a constant number of accepting transcripts.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00014"}, {"primary_key": "2134302", "vector": [], "sparse_vector": [], "title": "Approximability of all finite CSPs with linear sketches.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Madhu <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A constraint satisfaction problem (CSP), $\\text{Max}-\\text{CSP}(\\mathcal{F})$ , is specified by a finite set of constraints $\\mathcal{F}\\subseteq\\{[q]^{k}\\rightarrow\\{0,1\\}\\}$ for positive integers $q$ and $k$ . An instance of the problem on $n$ variables is given by $m$ applications of constraints from $\\mathcal{F}$ to subsequences of the $n$ variables, and the goal is to find an assignment to the variables that satisfies the maximum number of constraints. In the ( $\\gamma, \\beta$ )-approximation version of the problem, for parameters $0\\leq\\beta &lt; \\gamma\\leq 1$ , the goal is to distinguish instances where at least $\\gamma$ fraction of the constraints can be satisfied from instances where at most $\\beta$ fraction of the constraints can be satisfied. In this work we consider the approximability of this problem in the context of sketching algorithms and give a dichotomy result. Specifically, for every family $\\mathcal{F}$ and every $\\beta &lt; \\gamma$ , we show that either a linear sketching algorithm solves the problem in polylogarithmic space, or the problem is not solvable by any sketching algorithm in $o(\\sqrt{n})$ space. We also extend previously known lower bounds for general streaming algorithms to a wide variety of problems, and in particular the case of $q=k=2$ where we get a dichotomy and the case when the satisfying assignments of $f$ support a distribution on $[q]^{k}$ with uniform marginals. Prior to this work, other than sporadic examples, the only systematic class of CSPs that were analyzed considered the setting of Boolean variables $q=2$ , binary constraints $k=2$ , singleton families $\\vert \\mathcal{F}\\vert =1$ and only considered the setting where constraints are placed on literals rather than variables. Our positive results show wide applicability of bias-based algorithms used previously by [2] and [3], which we extend to include richer norm estimation algorithms, by giving a systematic way to discover biases. Our negative results combine the Fourier analytic methods of [4], which we extend to a wider class of CSPs, with a rich collection of reductions among communication complexity problems that lie at the heart of the negative results. In particular, previous works used Fourier analysis over the Boolean cube to initiate their results and the results seemed particularly tailored to functions on Boolean literals (i.e., with negations). Our techniques surprisingly allow us to get to general $q$ -ary CSPs without negations by appealing to the same Fourier analytic starting point over Boolean hypercubes.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00117"}, {"primary_key": "2134303", "vector": [], "sparse_vector": [], "title": "SNARGs for $\\mathcal{P}$ from LWE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zhengzhong Jin"], "summary": "We provide the first construction of a succinct non-interactive argument (SNARG) for all polynomial time deterministic computations based on standard assumptions. For $T$ steps of computation, the size of the proof and the common random string (CRS) as well as the verification time are poly-logarithmic in $T$ . The security of our scheme relies on the hardness of the Learning with Errors (LWE) problem against polynomial-time adversaries. Previously, SNARGs based on standard assumptions could support bounded-depth computations and required sub-exponential hardness assumptions [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, STOC'21]. Along the way, we also provide the first construction of non-interactive batch arguments for N P based solely on the LWE assumption.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00016"}, {"primary_key": "2134304", "vector": [], "sparse_vector": [], "title": "Quasi-polynomial Time Approximation of Output Probabilities of Geometrically-local, Shallow Quantum Circuits.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present a classical algorithm that, for any 3D geometrically-local, polylogarithmic-depth quantum circuit $C$ , and any bit string $x\\in\\{0,1\\}^{n}$ , can compute the quantity $\\vert \\langle x\\vert C\\vert 0^{\\otimes n}\\rangle\\vert ^{2}$ to within any inverse-polynomial additive error in quasi-polynomial time. It is known that it is $\\# P$ -hard to compute this same quantity to within $2^{-n^{2}}$ additive error [1], [2], and worst-case hardness results for this task date back to [3]. The previous best known algorithm for this problem used $O(2^{n^{1/3}}\\text{poly}(1/\\epsilon))$ time to compute probabilities to within additive error $\\epsilon$ [4]. Notably, that paper, [4], included an elegant polynomial time algorithm for the same estimation task with 2D circuits, which makes a novel use of 1D Matrix Product States carefully tailored to the 2D geometry of the circuit in question. Surprisingly, it is not clear that it is possible to extend this use of MPS to address the case of 3D circuits in polynomial time. This raises a natural question as to whether the computational complexity of the 3D problem might be drastically higher than that of the 2D problem. In this work we address this question by exhibiting a quasi-polynomial time algorithm for the 3D case. In order to surpass the technical barriers encountered by previously known techniques we are forced to pursue a novel approach: constructing a recursive sub-division of the given 3D circuit using carefully designed block-encodings. To our knowledge this is the first use of the block-encoding technique in a purely classical algorithm. Our algorithm has a Divide-and-Conquer structure, demonstrating how to approximate the desired quantity via several instantiations of the same problem type, each involving 3D-local circuits on about half the number of qubits as the original. This division step is then applied recursively, expressing the original quantity as a weighted sum of smaller and smaller 3D-local quantum circuits. A central technical challenge is to control correlations arising from entanglement that may exist between the different circuit \"pieces\" produced this way. We believe that the division step, which makes a novel use of block-encodings [5], together with an Inclusion-Exclusion argument to reduce error in each recursive approximation, may be of independent interest. 1 1 The full version of this paper is available at arXiv:2012.05460. We refer the reader to the full text for proofs of all Lemmas and Theorems.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00065"}, {"primary_key": "2134305", "vector": [], "sparse_vector": [], "title": "Fitting Distances by Tree Metrics Minimizing the Total Error within a Constant Factor.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the numerical taxonomy problem of fitting a positive distance function D:(S2)→R>0 by a tree metric. We want a tree T with positive edge weights and including S among the vertices so that their distances in T match those in D. A nice application is in evolutionary biology where the tree T aims to approximate the branching process leading to the observed distances in D [<PERSON><PERSON><PERSON> and <PERSON> 1967]. We consider the total error, that is the sum of distance errors over all pairs of points. We present a deterministic polynomial time algorithm minimizing the total error within a constant factor. We can do this both for general trees, and for the special case of ultrametrics with a root having the same distance to all vertices in S. The problems are APX-hard, so a constant factor is the best we can hope for in polynomial time. The best previous approximation factor was O((logn)(loglogn)) by <PERSON><PERSON> and <PERSON> [2005] who wrote \"Determining whether an O(1) approximation can be obtained is a fascinating question\".", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00054"}, {"primary_key": "2134306", "vector": [], "sparse_vector": [], "title": "Reachability in Vector Addition Systems is Ackermann-complete.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vector Addition Systems and equivalent Petri nets are a well established models of concurrency. The central algorithmic problem for Vector Addition Systems with a long research history is the reachability problem asking whether there exists a run from one given configuration to another. We settle its complexity to be Ackermann-complete thus closing the problem open for 45 years. In particular we prove that the problem is $\\mathcal{F}_{k}$ -hard for Vector Addition Systems with States in dimension 6k, where $\\mathcal{F}_{k}$ is the $k$ -th complexity class from the hierarchy of fast-growing complexity classes.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00120"}, {"primary_key": "2134307", "vector": [], "sparse_vector": [], "title": "Hardness of Approximate Diameter: Now for Undirected Graphs.", "authors": ["<PERSON>", "<PERSON>", "Virginia Vassilevska Williams"], "summary": "Approximating the graph diameter is a basic task of both theoretical and practical interest. A simple folklore algorithm can output a 2-approximation to the diameter in linear time by running BFS from an arbitrary vertex. It has been open whether a better approximation is possible in near-linear time. A series of papers on fine-grained complexity have led to strong hardness results for diameter in directed graphs, culminating in a recent tradeoff curve independently discovered by [<PERSON>, STOC'21] and [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>, STOC'21], showing that under the Strong Exponential Time Hypothesis (SETH), for any integer $k\\geq 2$ and $\\delta &gt; 0$ , a $2-\\frac{1}{k}-\\delta$ approximation for diameter in directed $m$ -edge graphs requires $mn^{1+1/(k-1)-o(1)}$ time. In particular, the simple linear time 2-approximation algorithm is optimal for directed graphs. In this paper we prove that the same tradeoff lower bound curve is possible for undirected graphs as well, extending results of [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> W., STOC'13], [Li'20] and [<PERSON><PERSON>, ICALP'21] who proved the first few cases of the curve, $k=2,3$ and 4, respectively. Our result shows in particular that the simple linear time 2-approximation algorithm is also optimal for undirected graphs. To obtain our result we develop new tools for fine-grained reductions that could be useful for proving SETH-based hardness for other problems in undirected graphs related to distance computation.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00102"}, {"primary_key": "2134308", "vector": [], "sparse_vector": [], "title": "Group isomorphism is nearly-linear time for most orders.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We show that there is a dense set of group orders such that for every such order we can decide in nearly-linear time whether two multiplication tables describe isomorphic groups. This improves significantly over the general quasi-polynomial time complexity and shows that group isomorphism can be tested efficiently for almost all group orders. We also show that in nearly-linear time it can be decided whether a multiplication table describes a group; this improves over the known super-linear complexity. Our complexities are calculated for a deterministic multi-tape Turing machine model, but we give the implications to a RAM model in the promise hierarchy as well.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00053"}, {"primary_key": "2134309", "vector": [], "sparse_vector": [], "title": "Robust recovery for stochastic block models.", "authors": ["<PERSON><PERSON><PERSON>", "Tommaso d&apos;<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We develop an efficient algorithm for weak recovery in a robust version of the stochastic block model. The algorithm matches the statistical guarantees of the best known algorithms for the vanilla version of the stochastic block model. In this sense, our results show that there is no price of robustness in the stochastic block model. Our work is heavily inspired by recent work of <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (SODA 2021) that provided an efficient algorithm for the corresponding distinguishing problem. Our algorithm and its analysis significantly depart from previous ones for robust recovery. A key challenge is the peculiar optimization landscape underlying our algorithm: The planted partition may be far from optimal in the sense that completely unrelated solutions could achieve the same objective value. This phenomenon is related to the push-out effect at the BBP phase transition for PCA. To the best of our knowledge, our algorithm is the first to achieve robust recovery in the presense of such a push-out effect in a non-asymptotic setting. Our algorithm is an instantiation of a framework based on convex optimization (related to but distinct from sum-of-squares), which may be useful for other robust matrix estimation problems. A by-product of our analysis is a general technique that boosts the probability of success (over the randomness of the input) of an arbitrary robust weak-recovery algorithm from constant (or slowly vanishing) probability to exponentially high probability.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00046"}, {"primary_key": "2134310", "vector": [], "sparse_vector": [], "title": "Fine-Grained Cryptanalysis: Tight Conditional Bounds for Dense k-SUM and k-XOR.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "An average-case variant of the $k$ -SUM conjecture asserts that finding $k$ numbers that sum to 0 in a list of $r$ random numbers, each of the order $r^{k}$ , cannot be done in much less than $r^{\\lceil k/2\\rceil}$ time. On the other hand, in the dense regime of parameters, where the list contains more numbers and many solutions exist, the complexity of finding one of them can be significantly improved by <PERSON>'s $k$ -tree algorithm. Such algorithms for $k$ -SUM in the dense regime have many applications, notably in cryptanalysis. In this paper, assuming the average-case $k$ -SUM conjecture, we prove that known algorithms are essentially optimal for $k=3,4,5$ . For $k &gt; 5$ , we prove the optimality of the $k$ -tree algorithm for a limited range of parameters. We also prove similar results for $k$ -XOR, where the sum is replaced with exclusive or. Our results are obtained by a self-reduction that, given an instance of $k$ -SUM which has a few solutions, produces from it many instances in the dense regime. We solve each of these instances using the dense $k$ -SUM oracle, and hope that a solution to a dense instance also solves the original problem. We deal with potentially malicious oracles (that repeatedly output correlated useless solutions) by an obfuscation process that adds noise to the dense instances. Using discrete Fourier analysis, we show that the obfuscation eliminates correlations among the oracle's solutions, even though its inputs are highly correlated.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00017"}, {"primary_key": "2134311", "vector": [], "sparse_vector": [], "title": "A time and space optimal stable population protocol solving exact majority.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Leszek Gasieniec", "<PERSON>", "Przemys<PERSON>nanski", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study population protocols, a model of distributed computing appropriate for modeling well-mixed chemical reaction networks and other physical systems where agents exchange information in pairwise interactions, but have no control over their schedule of interaction partners. The majority problem is that of determining in an initial population of $n$ agents, each with one of two opinions $A$ or B, whether there are more A, more B, or a tie. A stable protocol solves this problem with probability 1 by eventually entering a configuration in which all agents agree on a correct consensus decision of A, B, or T, from which the consensus cannot change. We describe a protocol solving this problem using O(log n) states (log log $n$ + O(1) bits of memory) and optimal expected time $O$ (log $n$ ). The number of states $O$ (log $n$ ) is known to be optimal for polylogarithmic time stable protocols that are \"output dominant\" and \"monotone\" [1]. These are two natural constraints satisfied by our protocol, making it simultaneously time- and state-optimal for that class. We introduce a key technique called a \"fixed resolution clock\" to achieve partial synchronization. Our protocol is nonuniform: the transition function has the value [log $n$ ] encoded in it. We show that the protocol can be modified to be uniform, while increasing the state complexity to Θ (log $n$ log log n).", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00104"}, {"primary_key": "2134312", "vector": [], "sparse_vector": [], "title": "Demystifying the border of depth-3 algebraic circuits.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Prateek <PERSON>wivedi", "<PERSON><PERSON>"], "summary": "Border complexity of polynomials plays an integral role in GCT (Geometric Complexity Theory) approach to P versus NP. It tries to formalize the notion of 'approximating a polynomial' via limits (Bürgisser FOCS'01). This raises the open question whether border of VP is same as VP or not; as the approximation involves exponential precision, which may not be efficiently simulable. Recently (<PERSON>'20) proved the universal power of the border of top-fanin-2 depth-3 circuits. Here we answer some of the related open questions. We show that the border of bounded top-fanin-k depth-3 circuits, for constant k, is relatively easy- it can be computed by a polynomial size algebraic branching program (ABP). There were hardly any de-bordering results known for prominent models before our result. Moreover, we give the first quasipolynomial-time black-box identity test for the same. Prior best was in PSPACE (Forbes,Shpilka STOC'18). Also, with more technical work, we extend our results to depth-4. Our de-bordering paradigm is a multi-step process; in short we call it DiDIL -divide, derive, induct, with limit. It 'almost' reduces border top-fanin-k depth-3 circuits to special cases of read-once oblivious algebraic branching programs (ROABPs) in any-order. Full version: https://www.cse.iitk.ac.in/users/nitin/papers/border-depth3.pdf", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00018"}, {"primary_key": "2134313", "vector": [], "sparse_vector": [], "title": "Combinatorial Contracts.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a new model of combinatorial contracts in which a principal delegates the execution of a costly task to an agent. To complete the task, the agent can take any subset of a given set of unobservable actions, each of which has an associated cost. The cost of a set of actions is the sum of the costs of the individual actions, and the principal's reward as a function of the chosen actions satisfies some form of diminishing returns. The principal incentivizes the agents through a contract, based on the observed outcome. Our main results are for the case where the task delegated to the agent is a project, which can be successful or not. We show that if the success probability as a function of the set of actions is gross substitutes, then an optimal contract can be computed with polynomially many value queries, whereas if it is submodular, the optimal contract is NP-hard. All our results extend to linear contracts for higher-dimensional outcome spaces, which we show to be robustly optimal given first moment constraints. Our analysis uncovers a new property of gross substitutes functions, and reveals many interesting connections between combinatorial contracts and combinatorial auctions, where gross substitutes is known to be the frontier for efficient computation.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00084"}, {"primary_key": "2134314", "vector": [], "sparse_vector": [], "title": "Tight Bounds for General Computation in Noisy Broadcast Networks.", "authors": ["<PERSON><PERSON>", "Gillat Kol", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Let II be a protocol over the n-party broadcast channel, where in each round, a pre-specified party broadcasts a symbol to all other parties. We wish to design a scheme that takes such a protocol II as input and outputs a noise resilient protocol II' that simulates II over the noisy broadcast channel, where each received symbol is flipped with a fixed constant probability, independently. What is the minimum overhead in the number of rounds that is incurred by any such simulation scheme? A classical result by <PERSON><PERSON><PERSON> from the 80's shows that non-interactive T-round protocols, where the bit communicated in every round is independent of the communication history, can be converted to noise resilient ones with only an $\\mathrm{O}(\\log\\log T$ ) multiplicative overhead in the number of rounds. Can the same be proved for any protocol? Or, are there protocols whose simulation requires an $\\Omega(\\log T)$ overhead (which always suffices)? We answer both the above questions in the negative: We give a simulation scheme with an $\\tilde{O}(\\sqrt{\\log T})$ overhead for every protocol and channel alphabet. We also prove an (almost) matching lower bound of $\\Omega(\\sqrt{\\log T})$ on the overhead required to simulate the pointer chasing protocol with T = n and polynomial alphabet.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00068"}, {"primary_key": "2134315", "vector": [], "sparse_vector": [], "title": "Hiding Among the Clones: A Simple and Nearly Optimal Analysis of Privacy Amplification by Shuffling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent work of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [1] demonstrates that random shuffling amplifies differential privacy guarantees of locally randomized data. Such amplification implies substan-tially stronger privacy guarantees for systems in which data is contributed anonymously [2] and has lead to significant interest in the shuffle model of privacy [3], [1]. We give a characterization of the privacy guarantee of the random shuffling of $\\mathbf{n}$ data records input to epsilon-differentially private local randomizers that significantly im-proves over previous work and achieves the asymptotically optimal dependence in epsilon. Our result is based on a new approach that is simpler than previous work and extends to approximate differential privacy with nearly the same guarantees. Importantly, our work also yields an algorithm for deriving tighter bounds on the resulting epsilon and delta as well as Rényi differential privacy guarantees. We show numerically that our algorithm gets to within a small constant factor of the optimal bound. As a direct corollary of our analysis we derive a simple and nearly optimal algorithm for frequency estimation in the shuffle model of privacy. We also observe that our result implies the first asymptotically optimal privacy analysis of noisy stochastic gradient descent that applies to sampling without replacement.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00096"}, {"primary_key": "2134316", "vector": [], "sparse_vector": [], "title": "List-decodability with large radius for Reed-Solomon codes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "List-decodability of Reed-Solomon codes has re-ceived a lot of attention, but the best-possible dependence between the parameters is still not well-understood. In this work, we focus on the case where the list-decoding radius is of the form $r=1-\\varepsilon$ for $\\varepsilon$ tending to zero. Our main result states that there exist Reed-Solomon codes with rate $\\Omega(\\varepsilon)$ which are $(1-\\varepsilon, O(1/\\varepsilon)$ -list-decodable, meaning that any Hamming ball of radius $1-\\varepsilon$ contains at most $O(1/\\varepsilon)$ codewords. This trade-off between rate and list-decoding radius is best-possible for any code with list size less than exponential in the block length. By achieving this trade-off between rate and list-decoding radius we improve a recent result of <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, and resolve the main motivating question of their work. Moreover, while their result requires the field to be exponentially large in the block length, we only need the field size to be polynomially large (and in fact, almost-linear suffices). We deduce our main result from a more general theorem, in which we prove good list-decodability properties of random puncturings of any given code with very large distance.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00075"}, {"primary_key": "2134317", "vector": [], "sparse_vector": [], "title": "FIXP-membership via Convex Optimization: Games, Cakes, and Markets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a new technique for proving membership of problems in FIXP-the class capturing the complexity of computing a fixed-point of an algebraic circuit. Our technique constructs a 'pseudogate' which can be used as a black box when building FIXP circuits. This pseudogate, which we term the 'OPT-gate', can solve most convex optimization problems. Using the OPT-gate, we prove new FIXP-membership results, and we generalize and simplify several known results from the literature on fair division, game theory and competitive markets. In particular, we prove complexity results for two classic problems: computing a market equilibrium in the Arrow-Debreu model with general concave utilities is in FIXP, and computing an envy-free division of a cake with general valuations is FIXP-complete. We further showcase the wide applicability of our technique, by using it to obtain simplified proofs and extensions of known FIXP-membership results for equilibrium computation for various types of strategic games, as well as the pseudomarket mechanism of Hylland and Zeckhauser.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00085"}, {"primary_key": "2134318", "vector": [], "sparse_vector": [], "title": "Hop-Constrained Metric Embeddings and their Applications.", "authors": ["<PERSON>"], "summary": "In network design problems, such as compact routing, the goal is to route packets between nodes using the (approximated) shortest paths. A desirable property of these routes is a small number of hops, which makes them more reliable, and reduces the transmission costs. Following the overwhelming success of stochastic tree embeddings for algorithmic design, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (STOC'21) studied hop-constrained Ramsey-type metric embeddings into trees. Specifically, embedding $f: G(V, E)\\rightarrow T$ has Ramsey hop-distortion ( $t, M,\\beta, h$ ), (here $t, \\beta, h\\geq 1$ and $M\\subseteq V)$ if $\\forall u\\in M, v\\in V,\\ d_{G}^{(\\beta\\cdot h)}(u, v)\\leq d_{T}(u, v)\\leq t\\cdot d_{G}^{(h)}(u, v). t$ is called the distortion, $\\beta$ is called the hop-stretch, and $d_{G}^{(h)}(u, v)$ denotes the minimum weight of a $u-v$ path with at most $h$ hops. <PERSON><PERSON><PERSON> et al. constructed embedding where $M$ contains $1-\\epsilon$ fraction of the vertices and $\\beta=t=O(\\frac{\\log^{2}n}{\\epsilon})$ . They used their embedding to obtain multiple bicriteria approximation algorithms for hop-constrained network design problems. In this paper, we first improve the Ramsey-type embedding to obtain parameters $t=\\beta=\\frac{\\tilde{O}(\\log n)}{\\epsilon}$ , and generalize it to arbitrary distortion parameter $t$ (in the cost of reducing the size of $M$ ). This embedding immediately implies polynomial improvements for all the approximation algorithms from Haeupler et al.. Further, we construct hop-constrained clan embeddings (where each vertex has multiple copies), and use them to construct bicriteria approximation algorithms for the group Steiner tree problem, matching the state of the art of the non constrained version. Finally, we use our embedding results to construct hop constrained distance oracles, distance labeling, and most prominently, the first hop constrained compact routing scheme with provable guarantees. All our metric data structures almost match the state of the art parameters of the non-constrained versions.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00056"}, {"primary_key": "2134319", "vector": [], "sparse_vector": [], "title": "Integer programs with bounded subdeterminants and two nonzeros per row.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We give a strongly polynomial-time algorithm for integer linear programs defined by integer coefficient matrices whose subdeterminants are bounded by a constant and that contain at most two nonzero entries in each row. The core of our approach is the first polynomial-time algorithm for the weighted stable set problem on graphs that do not contain more than $k$ vertex-disjoint odd cycles, where $k$ is any constant. Previously, polynomial-time algorithms were only known for $k=0$ (bipartite graphs) and for $k=1$ . We observe that integer linear programs defined by coefficient matrices with bounded subdeterminants and two nonzeros per column can be also solved in strongly polynomial-time, using a reduction to b-matching.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00011"}, {"primary_key": "2134320", "vector": [], "sparse_vector": [], "title": "Minor Sparsifiers and the Distributed Laplacian Paradigm.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study distributed algorithms built around minor-based vertex sparsifiers, and give the first algorithm in the CONGEST model for solving linear systems in graph Laplacian matrices to high accuracy. Our Laplacian solver has a round complexity of $O(n^{o(1)}(\\sqrt{n}+D))$ , and thus almost matches the lower bound of $\\widetilde{\\Omega}(\\sqrt{n}+D)$ , where $n$ is the number of nodes in the network and $D$ is its diameter. We show that our distributed solver yields new sublinear round algorithms for several cornerstone problems in combinatorial optimization. This is achieved by leveraging the powerful algorithmic framework of Interior Point Methods (IPMs) and the Laplacian paradigm in the context of distributed graph algorithms, which entails numerically solving optimization problems on graphs via a series of Laplacian systems. Problems that benefit from our distributed algorithmic paradigm include exact mincost flow, negative weight shortest paths, maxflow, and bipartite matching on sparse directed graphs. For the maxflow problem, this is the first exact distributed algorithm that applies to directed graphs, while the previous work by [<PERSON><PERSON><PERSON><PERSON> et al. SICOMP'18] considered the approximate setting and works only for undirected graphs. For the mincost flow and the negative weight shortest path problems, our results constitute the first exact distributed algorithms running in a sublinear number of rounds. Given that the hybrid between IPMs and the Laplacian paradigm has proven useful for tackling numerous optimization problems in the centralized setting, we believe that our distributed solver will find future applications. At the heart of our distributed Laplacian solver is the notion of spectral subspace sparsifiers of [Li, Schild FOCS'18]. We present a nontrivial distributed implementation of their construction by (i) giving a parallel variant of their algorithm that avoids the sampling of random spanning trees and uses approximate leverage scores instead, and (ii) showing that the algorithm still produces a high-quality subspace spectral sparsifier by carefully setting up and analyzing matrix martingales. Combining this vertex reduction recursively with both tree and elimination-based preconditioners leads to our algorithm for solving Laplacian systems. The construction of the elimination-based preconditioners is based on computing short random walks, and we introduce a new technique for reducing the congestion incurred by the simulation of these walks on weighted graphs.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00099"}, {"primary_key": "2134321", "vector": [], "sparse_vector": [], "title": "Improved Online Correlated Selection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper studies online correlated selection (OCS). Suppose that we receive a pair of elements in each round and select one of them. Can we select with negative correlation to be more effective than independent random selections? Our contributions are threefold. For semi-OCS, which considers the probability that an element remains unselected after appearing in $k$ rounds, we give an optimal algorithm that minimizes this probability for all k. It leads to 0.536-competitive unweighted and vertex-weighted on-line bipartite matching algorithms that randomize over only two options in each round, improving the previous 0.508-competitive ratio by <PERSON><PERSON><PERSON><PERSON> et al. (2020). Further, we develop the first multi-way semi-OCS that allows an arbitrary number of elements with arbitrary masses in each round. As an application, it rounds the Balance algorithm in unweighted and vertex-weighted online bi-partite matching to get a 0.593-competitive ratio. Finally, we study OCS, which further considers the probability that an element is unselected in any subset of rounds. We prove that the optimal \"level of negative correlation\" is between 0.167 and 0.25, improving the previous bounds of 0.109 and 1 by <PERSON><PERSON><PERSON><PERSON> et al. (2020). Our OCS gives a 0.519-competitive edge-weighted online bipartite matching algorithm, improving the previous 0.508-competitive ratio by <PERSON><PERSON><PERSON><PERSON> et al. (2020).", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00123"}, {"primary_key": "2134322", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Electrical Flows: Sparse Maxflow Faster <PERSON>-<PERSON>.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give an algorithm for computing exact maximum flows on graphs with $m$ edges and integer capacities in the range [ $1,U$ ] in $\\tilde{O}(m^{\\frac{3}{2}-\\frac{1}{328}}\\log U)$ time. 1 1 We use $\\tilde{O}(\\cdot)$ to suppress logarithmic factors in $m$ . For sparse graphs with polynomially bounded integer capacities, this is the first improvement over the $\\tilde{O}(m^{1.5}\\log U)$ time bound from [Goldberg-Rao JACM '98]. Our algorithm revolves around dynamically maintaining the augmenting electrical flows at the core of the interior point method based algorithm from [Mądry JACM '16]. This entails designing data structures that, in limited settings, return edges with large electric energy in a graph undergoing resistance updates.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00058"}, {"primary_key": "2134323", "vector": [], "sparse_vector": [], "title": "Deterministic Distributed Vertex Coloring: Simpler, Faster, and without Network Decomposition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a simple deterministic distributed algorithm that computes a ( $\\Delta+1$ )-vertex coloring in $O(\\text{log}^{2}\\Delta. \\text{log}\\ n)$ rounds. The algorithm can be implemented with $O(\\text{log}\\ n)$ -bit messages. The algorithm can also be extended to the more general ( $degree+1$ )-list coloring problem. Obtaining a polylogarithmic-time deterministic algorithm for ( $\\Delta +1$ )-vertex coloring had remained a central open question in the area of distributed graph algorithms since the 1980s, until a recent network decomposition algorithm of Rozhoň and Ghaffari [STOC'20]. The current state of the art is based on an improved variant of their decomposition, which leads to an $O(\\text{log}^{5}n)$ -round algorithm for ( $\\Delta+1$ )-vertex coloring. Our coloring algorithm is completely different and considerably simpler and faster. It solves the coloring problem in a direct way, without using network decomposition, by gradually rounding a certain fractional color assignment until reaching an integral color assignments. Moreover, via the approach of <PERSON>, <PERSON>, and <PERSON><PERSON> [STOC'18], this improved deterministic algorithm also leads to an improvement in the complexity of randomized algorithms for ( $\\Delta +1$ )-coloring, now reaching the bound of $O(\\text{log}^{3}\\text{log}\\ n)$ rounds. As a further application, we provide faster deterministic distributed algorithms for the following vertex coloring variants. In graphs of arboricity $a$ , we show that a $(2+\\varepsilon)a$ -vertex coloring can be computed in $O(\\text{log}^{3}a\\cdot \\text{log} n)$ rounds. We also show that for $\\Delta\\geq 3$ , a $\\Delta$ -coloring of a $\\Delta$ -colorable graph $G$ can be computed in $O(\\text{log}^{2}\\Delta\\cdot \\text{log}^{2}n)$ rounds.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00101"}, {"primary_key": "2134324", "vector": [], "sparse_vector": [], "title": "Improved List-Decodability and List-Recoverability of Reed-Solomon Codes via Tree Packings: [Extended Abstract].", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Chong <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper shows that there exist Reed-Solomon (RS) codes, over large finite fields, that are combinatorially list-decodable well beyond the Johnson radius, in fact almost achieving list-decoding capacity. In particular, we show that for any ε E (0,1] there exist RS codes with rate $\\Omega(\\frac{\\varepsilon}{1\\not\\varepsilon(1/_{\\in})+1})$ that are list-decodable from radius of 1-ε. We generalize this result to list-recovery, showing that there exist $(1-\\varepsilon,\\ell, O(\\ell/\\varepsilon))$ -list-recoverable RS codes with rate $\\Omega\\left(\\frac{\\varepsilon}{\\sqrt{\\ell}(\\log(1/\\varepsilon)+1)}\\right)$ . Along the way we use our techniques to give a new proof of a result of <PERSON> on optimal linear perfect hash matrices, and strengthen it to obtain a construction of strongly perfect hash matrices. To derive the results in this paper we show a surprising connection of the above problems to graph theory, and in particular to the tree packing theorem of <PERSON> and <PERSON>. We also state a new conjecture that generalizes the tree-packing theorem to hypergraphs, and show that if this conjecture holds, then there would exist RS codes that are optimally (non-asymptotically) list-decodable. 1 1 A full version of this paper is available online at https://arxiv.org/abs/2011.04453.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00074"}, {"primary_key": "2134325", "vector": [], "sparse_vector": [], "title": "The zero-rate threshold for adversarial bit-deletions is less than 1/2.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON>", "<PERSON>"], "summary": "We prove that there exists an absolute constant 6 > 0 such any binary code $C$ ⊂ {0, 1} N tolerating (1/2 - δ) $N$ adversarial deletions must satisfy| C| ≤ 2 polylog $N$ and thus have rate asymptotically approaching 0. This is the first constant fraction improvement over the trivial bound that codes tolerating $N$ /2 adversarial deletions must have rate going to 0 asymptotically. Equivalently, we show that there exists absolute constants $A$ and 6 > 0 such that any set $C$ ⊂ {0, 1} of 2 log A N binary strings must contain two strings $c$ and c' whose longest common subsequence has length at least (1/2 + δ) N. As an immediate corollary, we show that q-ary codes tolerating a fraction 1 - (1 + 2δ) / $q$ of adversarial deletions must also have rate approaching 0. Our techniques include string regularity arguments and a structural lemma that classifies binary strings by their oscillation patterns. Leveraging these tools, we find in any large code two strings with similar oscillation patterns, which is exploited to find a long common subsequence.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00076"}, {"primary_key": "2134326", "vector": [], "sparse_vector": [], "title": "Smoothed Analysis with Adaptive Adversaries.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We prove novel algorithmic guarantees for several online problems in the smoothed analysis model. In this model, at each time step an adversary chooses an input distribution with density function bounded above pointwise by a multiplicative factor from the uniform distribution; nature then samples an input from this distribution. This interpolates between the extremes of worst-case and average case analysis. Crucially, our results hold for adaptive adversaries that can base their choice of an input distribution on the decisions of the algorithm and the realizations of the inputs in the previous time steps. An adaptive adversary can nontrivially correlate inputs at different time steps with each other and with the algorithm's current state; this appears to rule out the standard proof approaches in smoothed analysis. This paper presents a general technique for proving smoothed algorithmic guarantees against adaptive adversaries, in effect reducing the setting of an adaptive adversary to the much simpler case of an oblivious adversary (i.e., an adversary that commits in advance to the entire sequence of input distributions). We apply this technique to prove strong smoothed guarantees for three different problems: Online learning, Online discrepancy and Dispersion in online optimization. We show that in these setting, we can get bounds that match bounds we can get for non-adaptive adversaries.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00095"}, {"primary_key": "2134327", "vector": [], "sparse_vector": [], "title": "Fooling Constant-Depth Threshold Circuits (Extended Abstract).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present new constructions of pseudorandom generators (PRGs) for two of the most widely studied non-uniform circuit classes in complexity theory. Our main result is a construction of the first non-trivial PRG for linear threshold (LTF) circuits of arbitrary constant depth and super-linear size. This PRG fools circuits with depth $d\\in\\mathbb{N}$ and $n^{1+\\delta}$ wires, where $\\delta=2^{-O(d)}$ , using seed length $O(n^{1-\\delta})$ and with error $2^{-n^{\\delta}}$ . This tightly matches the best known lower bounds for this circuit class. As a consequence of our result, all the known hardness for LTF circuits has now effectively been translated into pseudorandomness. This brings the extensive effort in the last decade to construct PRGs and deterministic circuit-analysis algorithms for this class to the point where any subsequent improvement would yield breakthrough lower bounds. Our second contribution is a PRG for De Morgan formulas of size $s$ whose seed length is $s^{1/3+o(1)}\\cdot\\text{polylog}(1/\\epsilon)$ for error $\\epsilon$ . In particular, our PRG can fool formulas of sub-cubic size $s=n^{3-\\Omega(1)}$ with an exponentially small error $\\epsilon=\\exp(-n^{\\Omega(1)})$ . This significantly improves the inverse-polynomial error of the previous state-of-the-art for such formulas by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (FOCS 2012, J<PERSON>M 2019), and again tightly matches the best currently-known lower bounds for this class. In both settings, a key ingredient in our constructions is a pseudorandom restriction procedure that has tiny failure probability, but simplifies the function to a non-natural \"hybrid computational model\" that combines several computational models. As part of our proofs we also construct \"extremely low-error\" PRGs for related circuit classes; for example, we construct a PRG for arbitrary functions of $s$ LTFs that can handle even the extreme setting of parameters $s=n/\\text{polylog}(n)$ and $\\epsilon=2^{-n/\\text{polylog}(n)}$ .", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00019"}, {"primary_key": "2134328", "vector": [], "sparse_vector": [], "title": "On Worst-Case Learning in Relativized Heuristica.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A PAC learning model involves two worst-case requirements: a learner must learn all functions in a class on all example distributions. However, basing the hardness of learning on NP-hardness has remained a key challenge for decades. In fact, recent progress in computational complexity suggests the possibility that a weaker assumption might be sufficient for worst-case learning than the feasibility of worst-case algorithms for NP problems. In this study, we investigate whether these worst-case re-quirements for learning are satisfied on the basis of only average-case assumptions in order to understand the nature of learning. First, we construct a strong worst-case learner based on the assumption that DistNP ⊆ AvgP, i.e., in Heuristica. Our learner agnostically learns all polynomial-size circuits on all unknown P/ poly-samplable distributions in polynomial time, where the complexity of learning depends on the complexity of sampling examples. Second, we study the limitation of relativizing constructions of learners based on average-case heuristic algorithms. Specifically, we construct a powerful oracle such that DistPH ⊆ AvgP, i.e., every problem in PH is easy on average, whereas UP ∩ coUP and PAC learning on almost-uniform distributions are hard even for 2 n/w(1og n) - time algorithms in the relativized world, which improves the oracle separation presented by <PERSON><PERSON><PERSON><PERSON><PERSON> (CCC 2011). The core concept of our improvements is the consideration of a switching lemma on a large alphabet, which may be of independent interest. The lower bound on the time complexity is nearly optimal because <PERSON><PERSON><PERSON> (STOC 2021) showed that DistPH ⊆ AvgP implies that PH can be solved in time 2 O(n/ log n) under any relativized world. The full version of this paper is available on ECCC [1].", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00078"}, {"primary_key": "2134329", "vector": [], "sparse_vector": [], "title": "The Minimum Formula Size Problem is (ETH) Hard.", "authors": ["<PERSON><PERSON>"], "summary": "A longstanding open question is whether the Minimum Circuit Size Problem (MCSP) is NP-complete. In fact, even determining whether MCSP has a search-to-decision reduction has been open for over twenty years. We show that, under the Exponential Time Hypothesis, the Minimum (DeMorgan) Formula Size Problem, MFSP, is not in P. Building on this, we show that MFSP has a polynomial-time (exact) search-to-decision reduction, a result that does not relativize. Our main technique relates the formula complexity of a partial function with the formula complexity of an associated total function and is proved using the \"leaf weighting\" technique of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00050"}, {"primary_key": "2134330", "vector": [], "sparse_vector": [], "title": "Towards the sampling Lovász Local Lemma.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Thuy-<PERSON>ng <PERSON>"], "summary": "Let $\\Phi=(V, \\mathcal{C})$ be a constraint satisfaction problem on variables $v_{1}, \\ldots, v_{n}$ such that each constraint depends on at most $k$ variables and such that each variable assumes values in an alphabet of size at most [ $q$ ]. Suppose that each constraint shares variables with at most $\\Delta$ constraints and that each constraint is violated with probability at most $p$ (under the product measure on its variables). We show that for $k, q=O(1)$ , there is a deterministic, polynomial time algorithm to approximately count the number of satisfying assignments and a randomized, polynomial time algorithm to sample from approximately the uniform distribution on satisfying assignments, provided that $C\\cdot q^{2}\\cdot k\\cdot p\\cdot\\Delta^{7} &lt; 1$ , where $C$ is an absolute constant. Previously, a result of this form was known essentially only in the special case when each constraint is violated by exactly one assignment to its variables. For the special case of $k$ .CNF formulas, the term $\\Delta^{7}$ improves the previously best known $\\Delta^{60}$ for deterministic algorithms [Moitra, J.ACM, 2019] and $\\Delta^{13}$ for randomized algorithms [<PERSON>, <PERSON>, <PERSON>, and <PERSON>, STOC 2021]. For the special case of properly $q$ -coloring $k$ -uniform hypergraphs, the term $\\Delta^{7}$ improves the previously best known $\\Delta^{14}$ for deterministic algorithms [<PERSON>, Liao, Lu, and Zhang, SICOMP, 2019] and $\\Delta^{9}$ for randomized algorithms [Feng, Guo, Yin, and Zhang, STOC 2021].", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00025"}, {"primary_key": "2134331", "vector": [], "sparse_vector": [], "title": "Quantum soundness of testing tensor codes.", "authors": ["Zhengfeng Ji", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A locally testable code is an error-correcting code that admits very efficient probabilistic tests of membership. Tensor codes provide a simple family of combinatorial constructions of locally testable codes that generalize the family of Reed-Muller codes. The natural test for tensor codes, the axis-parallel line vs. point test, plays an essential role in constructions of probabilistically checkable proofs. We analyze the axis-parallel line vs. point test as a two-prover game and show that the test is sound against quantum provers sharing entanglement. Our result implies the quantum-soundness of the low individual degree test, which is an essential component of the MIP* = RE theorem. Our proof also generalizes to the infinite-dimensional commuting-operator model of quantum provers.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00064"}, {"primary_key": "2134332", "vector": [], "sparse_vector": [], "title": "Sum-of-Squares Lower Bounds for Sparse Independent Set.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Sum-of-Squares (SoS) hierarchy of semidefinite programs is a powerful algorithmic paradigm which captures state-of-the-art algorithmic guarantees for a wide array of problems. In the average case setting, SoS lower bounds provide strong evidence of algorithmic hardness or information-computation gaps. Prior to this work, SoS lower bounds have been obtained for problems in the \"dense\" input regime, where the input is a collection of independent Rademacher or Gaussian random variables, while the sparse regime has remained out of reach. We make the first progress in this direction by obtaining strong SoS lower bounds for the problem of Independent Set on sparse random graphs. We prove that with high probability over an Erdós-Rénvi random graph $G\\sim G_{n_{J}\\frac{d}{u}}$ with average degree $d &gt; \\log^{2}n$ , degree-Dsos SoS fails to refute the existence of an independent set of size $k=\\displaystyle \\Omega(\\frac{n}{\\sqrt{d}(\\log n)(\\mathrm{D}_{\\mathrm{S}\\mathrm{o}\\mathrm{S}})^{c_{0}}})$ in $G$ (where $c_{0}$ is an absolute constant), whereas the true size of the largest independent set in $G$ is $O(\\displaystyle \\frac{n\\log d}{d})$ . Our proof involves several significant extensions of the techniques used for proving SoS lower bounds in the dense setting. Previous lower bounds are based on the pseudo-calibration heuristic of Barak et al. [FOCS 2016] which produces a candidate SoS solution using a planted distribution indistinguishable from the input distribution via low-degree tests. In the sparse case the natural planted distribution does admit low-degree distinguishers, and we show how to adapt the pseudo-calibration heuristic to overcome this. Another notorious technical challenge for the sparse regime is the quest for matrix norm bounds. In this paper, we obtain new norm bounds for graph matrices in the sparse setting. While in the dense setting the norms of graph matrices are characterized by the size of the minimum vertex separator of the corresponding graph, this turns not to be the case for sparse graph matrices. Another contribution of our work is developing a new combinatorial understanding of structures needed to understand the norms of sparse graph matrices.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00048"}, {"primary_key": "2134333", "vector": [], "sparse_vector": [], "title": "A Quantum Advantage for a Natural Streaming Problem.", "authors": ["<PERSON>"], "summary": "Data streaming, in which a large dataset is received as a \"stream\" of updates, is an important model in the study of space-bounded computation. Starting with the work of <PERSON> [SPAA '06], it has been known that quantum streaming algorithms can use asymptotically less space than their classical counterparts for certain problems. However, so far, all known examples of quantum advantages in streaming are for problems that are either specially constructed for that purpose, or require many streaming passes over the input. We give a one-pass quantum streaming algorithm for one of the best-studied problems in classical graph streaming-the triangle counting problem. Almost-tight parametrized upper and lower bounds are known for this problem in the classical setting; our algorithm uses polynomially less space in certain regions of the parameter space, resolving a question posed by <PERSON> and <PERSON><PERSON> in 2014 on achieving quantum advantages for natural streaming problems.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00091"}, {"primary_key": "2134334", "vector": [], "sparse_vector": [], "title": "A proof of the <PERSON><PERSON><PERSON><PERSON><PERSON> conjecture: Algorithmic aspects.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The <PERSON><PERSON><PERSON><PERSON> conjecture (posed in 1972) states that the chromatic index of any linear hypergraph on $n$ vertices is at most n. <PERSON><PERSON><PERSON><PERSON> considered this to be one of his three most favorite combinatorial problems and offered a $500 reward for a proof of this conjecture. We prove this conjecture for every large n. Here, we also provide a randomised algorithm to find such a colouring in polynomial time with high probability.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00107"}, {"primary_key": "2134335", "vector": [], "sparse_vector": [], "title": "Spectral Hypergraph Sparsifiers of Nearly Linear Size.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graph sparsification has been studied extensively over the past two decades, culminating in spectral sparsifiers of optimal size (up to constant factors). Spectral hypergraph sparsification is a natural analogue of this problem, for which optimal bounds on the sparsifier size are not known, mainly because the hypergraph Laplacian is non-linear, and thus lacks the linear-algebraic structure and tools that have been so effective for graphs. Our main contribution is the first algorithm for constructing $\\epsilon$ -spectral sparsifiers for hypergraphs with $O^{\\ast}(n)$ hyperedges, where $O^{\\ast}$ suppresses $(\\epsilon^{-1}\\log n)^{O(1)}$ factors. This bound is independent of the rank $r$ (maximum cardinality of a hyperedge), and is essentially best possible due to a recent bit complexity lower bound of $\\Omega(nr)$ for hypergraph sparsification. This result is obtained by introducing two new tools. First, we give a new proof of spectral concentration bounds for sparsifiers of graphs; it avoids linear-algebraic methods, replacing e.g. the usual application of the matrix Bernstein inequality and therefore applies to the (non-linear) hypergraph setting. To achieve the result, we design a new sequence of hypergraph-dependent $\\epsilon$ -nets on the unit sphere in $\\mathbb{R}^{n}$ . Second, we extend the weight-assignment technique of <PERSON>, <PERSON> and <PERSON><PERSON> [FOCS'20] to the spectral sparsification setting. Surprisingly, the number of spanning trees after the weight assignment can serve as a potential function guiding the reweighting process in the spectral setting.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00114"}, {"primary_key": "2134336", "vector": [], "sparse_vector": [], "title": "Embeddings of Planar Quasimetrics into Directed ℓ1 and Polylogarithmic Approximation for Directed Sparsest-Cut.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The multi-commodity flow-cut gap is a fundamental parameter that affects the performance of several divide & conquer algorithms, and has been extensively studied for various classes of undirected graphs. It has been shown by <PERSON><PERSON>, <PERSON> and <PERSON> [20] and by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [5] that for general n-vertex graphs it is bounded by O(log n) and the <PERSON><PERSON><PERSON> conjecture [13] asserts that it is O(1) for any family of graphs that excludes some fixed minor. We show that the multicommodity flow-cut gap on directed planar graphs is O(log 3 n). This is the first sub-polynomial bound for any family of directed graphs of super-constant treewidth. We remark that for general directed graphs, it has been shown by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [11] that the gap is Ω(n 1/7 ), even for directed acyclic graphs. As a direct consequence of our result, we also obtain the first polynomial-time polylogarithmic-approximation algorithms for the Directed Non-Bipartite Sparsest-Cut, and the Directed Multicut problems for directed planar graphs, which extends the long-standing result for undirectd planar graphs by <PERSON> [22] (with a slightly weaker bound). At the heart of our result we investigate low-distortion quasimetric embeddings into directed $e$ 1 . More precisely, we construct O(log 2 n)-Lipschitz quasipartitions for the shortest-path quasimetric spaces of planar digrap $hs$ , which generalize the notion of Lipschitz partitions from the theory of metric embeddings. This construction combines ideas from the theory of bi-Lipschitz embeddings, with tools form data structures on directed planar graphs.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00055"}, {"primary_key": "2134337", "vector": [], "sparse_vector": [], "title": "On the Power of Preconditioning in Sparse Linear Regression.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Sparse linear regression is a fundamental problem in high-dimensional statistics, but strikingly little is known about how to efficiently solve it without restrictive conditions on the design matrix. We consider the (correlated) random design setting, where the covariates are independently drawn from a multivariate Gaussian $N(0,\\ \\Sigma)$ , for some $n\\times n$ positive semi-definite matrix $\\Sigma$ , and seek estimators $\\hat{w}$ minimizing $(\\hat{w}-w^{\\ast})^{T}\\Sigma(\\hat{w}-w^{\\ast})$ , where $w^{\\ast}$ is the k-sparse ground truth. Information theoretically, one can achieve strong error bounds with only $O(k\\log n)$ samples for arbitrary $\\Sigma$ and $w^{\\ast}$ ; however, no efficient algorithms are known to match these guarantees even with $o(n)$ samples, without further assumptions on $\\Sigma$ or $w^{\\ast}$ . Yet there is little evidence for this gap in the random design setting: computational lower bounds are only known for worst-case design matrices. To date, random-design instances (i.e. specific covariance matrices $\\Sigma$ ) have only been proven hard against the Lasso program and variants. More precisely, these \"hard\" instances can often be solved by <PERSON><PERSON> after a simple change-of-basis (i.e. preconditioning). In this work, we give both upper and lower bounds clarifying the power of preconditioning as a tool for solving sparse linear regression problems. On the one hand, we show that the preconditioned <PERSON>so can solve a large class of sparse linear regression problems nearly optimally: it succeeds whenever the dependency structure of the covariates, in the sense of the Markov property, has low treewidth - even if $\\Sigma$ is highly ill-conditioned. This upper bound builds on ideas from the wavelet and signal processing literature. As a special case of this result, we give an algorithm for sparse linear regression with covariates from an autoregressive time series model, where we also show that the (usual) Lasso provably fails. On the other hand, we construct (for the first time) random-design instances which are provably hard even for an optimally preconditioned Lasso. In fact, we complete our treewidth classification by proving that for any treewidth-t graph, there exists a Gaussian Markov Random Field on this graph such that the preconditioned Lasso, with any choice of preconditioner, requires $\\Omega(t^{1/20})$ samples to recover $O(\\log n)$ -sparse signals when covariates are drawn from this model.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00061"}, {"primary_key": "2134338", "vector": [], "sparse_vector": [], "title": "A Gap-ETH-Tight Approximation Scheme for Euclidean TSP.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We revisit the classic task of finding the shortest tour of $n$ , points in d-dimensional Euclidean space, for any fixed constant $d\\geqslant 2$ . We determine the optimal dependence on $\\varepsilon$ in the running time of an algorithm that computes a $(1+\\varepsilon){-}$ approximate tour, under a plausible assumption, Specifically, we give an algorithm that runs in $2^{\\mathcal{O}(1/\\varepsilon^{d-1})}n\\log n$ time. This improves the previously smallest dependence on $\\varepsilon$ in the running time $(1/\\varepsilon)^{\\mathcal{O}(1/\\varepsilon^{d-1})}n\\log n$ of the algorithm by <PERSON> and <PERSON> (STOC 1998). We also show that a $2^{o(1/\\varepsilon^{d-1})}\\text{poly}(n)$ algorithm would violate the Gap-Exponential Time Hypothesis (Gap-ETH). Our new algorithm builds upon the celebrated quadtree-based methods initially proposed by <PERSON><PERSON><PERSON> (J. <PERSON> 1998), but it adds a new idea that we call sparsity-sensitive patching. On a high level this lets the granularity with which we simplify the tour depend on how sparse it is locally. We demonstrate that our technique extends to other problems, by showing that for Steiner Tree and Rectilinear Steiner Tree it yields the same running time. We complement our results with a matching Gap-Ethlower bound for Rectilinear Steiner Tree.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00043"}, {"primary_key": "2134339", "vector": [], "sparse_vector": [], "title": "Small-space and streaming pattern matching with $k$ edits.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we revisit the fundamental and well-studied problem of approximate pattern matching under edit distance. Given an integer $k$ , a pattern $P$ of length $m$ , and a text $T$ of length $n\\geq m$ , the task is to find substrings of $T$ that are within edit distance $k$ from $P$ . Our main result is a streaming algorithm that solves the problem in $\\tilde{\\mathcal{O}}(k^{5})$ space 1 1 Hereafter, $\\tilde{\\mathcal{O}}(\\cdot)$ hides a $\\text{poly} (\\log n)$ factor. and $\\tilde{\\mathcal{O}}(k^{8})$ amortized time per character of the text, providing answers correct with high probability. This answers a decade-old question: since the discovery of a poly ( $k\\ \\text{log}\\ n$ ) -space streaming algorithm for pattern matching under Hamming distance by <PERSON><PERSON> and <PERSON><PERSON> [FOCS 2009], the existence of an analogous result for edit distance remained open. Up to this work, no poly ( $k\\ \\text{log}\\ n$ )-space algorithm was known even in the simpler semi-streaming model, where $T$ comes as a stream but $P$ is available for read-only access. In this model, we give a deterministic algorithm that achieves slightly better complexity. Our central technical contribution is a new space-efficient deterministic encoding of two strings, called the greedy encoding, which encodes a set of all alignments of cost at most $k$ with a certain property (we call such alignments greedy). On strings of length at most $n$ , the encoding occupies $\\tilde{\\mathcal{O}}(k^{2})$ space. We use the encoding to compress substrings of the text that are close to the pattern. In order to do so, we compute the encoding for substrings of the text and of the pattern, which requires read-only access to the latter. In order to develop the fully streaming algorithm, we further introduce a new edit distance sketch parameterized by integers $n &gt; k$ . For any string of length at most $n$ , the sketch is of size $\\tilde{\\mathcal{O}}\\overline{(k}^{2})$ , and it can be computed with an $\\tilde{\\mathcal{O}}(k^{2})$ -space streaming algorithm. Given the sketches of two strings, in $\\tilde{\\mathcal{O}}(k^{3})$ time we can compute their edit distance or certify that it is larger than $k$ . This result improves upon $\\tilde{\\mathcal{O}}(k^{8})$ -size sketches of Belazzougui and Zhang [FOCS 2016] and very recent $\\tilde{\\mathcal{O}}(k^{3})$ -size sketches of Jin, Nelson, and Wu [STACS 2021].", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00090"}, {"primary_key": "2134340", "vector": [], "sparse_vector": [], "title": "Quantum supremacy and hardness of estimating output probabilities of quantum circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by the recent experimental demonstrations of quantum supremacy, proving the hardness of the output of random quantum circuits is an imperative near term goal. We prove under the complexity theoretical assumption of the non-collapse of the polynomial hierarchy that approximating the output probabilities of random quantum circuits to within $\\exp(-\\Omega(m\\log m))$ additive error is hard for any classical computer, where $m$ is the number of gates in the quantum computation. More precisely, we show that the above problem is #P-hard under BPP NP reduction. In the recent experiments, the quantum circuit has n-qubits and the architecture is a two-dimensional grid of size $\\sqrt{n}\\times\\sqrt{n}$ [1]. Indeed for constant depth circuits approximating the output probabilities to within $2^{-\\Omega(n\\log n)}$ is hard. For circuits of depth $\\log n$ or $\\sqrt{n}$ for which the anti-concentration property holds, approximating the output probabilities to within $2^{-\\Omega(n\\log^{2}n)}$ and $2^{-\\Omega(n^{3/2}\\log n)}$ is hard respectively. We then show that the hardness results extend to any open neighborhood of an arbitrary (fixed) circuit including the trivial circuit with identity gates. We made an effort to find the best proofs and proved these results from first principles, which do not use the standard techniques such as the <PERSON><PERSON><PERSON><PERSON> algorithm, the usual <PERSON><PERSON>'s lemma, and <PERSON><PERSON><PERSON><PERSON>'s result.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00126"}, {"primary_key": "2134341", "vector": [], "sparse_vector": [], "title": "A Single-Exponential Time 2-Approximation Algorithm for Treewidth.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We give an algorithm, that given an n-vertex graph $G$ and an integer k, in time 2 O(k) n either outputs a tree decomposition of $G$ of width at most 2k + 1 or determines that the treewidth of $G$ is larger than k. This is the first 2-approximation algorithm for treewidth that is faster than the known exact algorithms. In particular, our algorithm improves upon both the previous best approximation ratio of 5 in time 2 O(k) n and the previous best approximation ratio of 3 in time 2 O(k) n O(1) , both given by <PERSON><PERSON> et al. [FOCS 2013, SICOMP 2016]. Our algorithm is based on a local improvement method adapted from a proof of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [Comb. Probab. Comput. 2002].", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00026"}, {"primary_key": "2134342", "vector": [], "sparse_vector": [], "title": "The Hardest Explicit Construction.", "authors": ["<PERSON>"], "summary": "We investigate the complexity of explicit construction problems, where the goal is to produce a particular object possessing some pseudorandom property in time polynomial in the size of that object. We give overwhelming evidence that APEPP, defined originally by <PERSON> et al. [12], is the natural complexity class associated with explicit constructions of objects whose existence follows from the probabilistic method, by placing a variety of such construction problems in this class. We then demonstrate that a result of <PERSON><PERSON><PERSON><PERSON> [10] on provability in Bounded Arithmetic, when reinterpreted as a reduction between search problems, shows that constructing a truth table of high circuit complexity is complete for APEPP under NP-oracle reductions. This illustrates that <PERSON>'s classical proof of the existence of hard boolean functions is in fact a universal probabilistic existence argument: deran-domizing his proof implies a generic derandomization of the probabilistic method. As a corollary, we prove that EXP NP contains a language of mildly-exponential circuit complexity if and only if it contains a language of nearly maximum circuit complexity. Finally, for several of the problems shown to lie in APEPP, we demonstrate direct polynomial time reductions to the explicit construction of hard truth tables.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00051"}, {"primary_key": "2134343", "vector": [], "sparse_vector": [], "title": "Stochastic and Worst-Case Generalized Sorting Revisited.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The generalized sorting problem is a restricted version of standard comparison sorting where we wish to sort $n$ elements but only a subset of pairs are allowed to be compared. Formally, there is some known graph $G=(V, E)$ on the $n$ elements $v_{1, \\ldots, v_{n}}$ , and the goal is to determine the true order of the elements using as few comparisons as possible, where all comparisons ( $v_{i, v_{j}}$ ) must be edges in $E$ . We are promised that if the true ordering is $x_{1 &lt; x_{2} &lt; \\cdots &lt; x_{n}}$ for $\\{x_{i\\}}$ an unknown permutation of the vertices $\\{v_{i\\}}$ , then $(x_{i, x_{i+1})\\in E}$ for all $i$ : this Hamiltonian path ensures that sorting is actually possible. In this work, we improve the bounds for generalized sorting on both random graphs and worst-case graphs. For Erdős-Renyi random graphs $G(n, p)$ (with the promised Hamiltonian path added to ensure sorting is possible), we provide an algorithm for generalized sorting with an expected $O(n\\ \\text{lg}(np))$ comparisons, which we prove to be optimal for query complexity. This strongly improves over the best known algorithm of <PERSON>, <PERSON>, and <PERSON><PERSON> (FOCS 2011), which uses $\\tilde{O(\\min(n\\sqrt{np},\\ n/p^{2}))}$ comparisons. For arbitrary graphs $G$ with $n$ vertices and $m$ edges (again with the promised Hamiltonian path), we provide an algorithm for generalized sorting with $\\tilde{O(\\sqrt{mn})}$ comparisons. This improves over the best known algorithm of Huang et al., which uses $\\min(m,\\tilde{O}(n^{3/2}))$ comparisons.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00105"}, {"primary_key": "2134344", "vector": [], "sparse_vector": [], "title": "Optimal Approximate Distance Oracle for Planar Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A (1+ϵ)-approximate distance oracle of an edge-weighted graph is a data structure that returns an approximate shortest path distance between any two query vertices up to a (1+ϵ) factor. <PERSON><PERSON> (FOCS 2001, JACM 2004) and <PERSON> (SODA 2002) independently constructed a (1+ϵ)-approximate distance oracle with O(nlogn) space, measured in number of words, and O(1) query time when G is an undirected planar graph with n vertices and ϵ is a fixed constant. Many follow-up works gave (1+ϵ)-approximate distance oracles with various trade-offs between space and query time. However, improving O(nlogn) space bound without sacrificing query time remains an open problem for almost two decades. In this work, we resolve this problem affirmatively by constructing a (1+ϵ)-approximate distance oracle with optimal O(n) space and O(1) query time for undirected planar graphs and fixed ϵ. We also make substantial progress for planar digraphs with non-negative edge weights. For fixed ϵ>0, we give a (1+ϵ)-approximate distance oracle with space o(nlog(Nn)) and O(loglog(Nn) query time; here N is the ratio between the largest and smallest positive edge weight. This improves <PERSON><PERSON>'s (FOCS 2001, JACM 2004) O(nlog(Nn)logn) space bound by more than a logarithmic factor while matching the query time of his structure. This is the first improvement for planar digraphs in two decades, both in the weighted and unweighted setting.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00044"}, {"primary_key": "2134345", "vector": [], "sparse_vector": [], "title": "Sharper bounds on the Fourier concentration of DNFs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In 1992 <PERSON><PERSON> proved that every size-s DNF formula is Fourier-concentrated on $s^{O(\\log\\log s)}$ coefficients. We improve this to $s^{O(\\log\\log k)}$ where $k$ is the read number of the DNF. Since $k$ is always at most $s$ , our bound matches <PERSON><PERSON>'s for all DNFs and strengthens it for small-read ones. The previous best bound for read-k DNFs was $s^{O(k^{3/2})}$ . For $k$ up to $\\tilde{\\Theta}$ (log log $s$ ), we further improve our bound to the optimal poly $(s)$ ; previously no such bound was known for any $k=\\omega_{s}(1)$ . Our techniques involve new connections between the term structure of a DNF, viewed as a set system, and its Fourier spectrum. The full version of this paper is available at https://arxiv.org/abs/2109.04525. We strongly recommend reading the full version because it has better typesetting.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00094"}, {"primary_key": "2134346", "vector": [], "sparse_vector": [], "title": "Optimal Sub-Gaussian Mean Estimation in $\\mathbb{R}$.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We settle the fundamental problem of estimating the mean of a real-valued distribution in the high probability regime, under the minimal (and essentially necessary) assumption that the distribution has finite but unknown variance: we propose an estimator with convergence tight up to a $1 +o(1)$ factor. Crucially, in contrast to prior works, our estimator does not require prior knowledge of the variance, and works across the entire gamut of distributions with finite variance, including those without any higher moments. Parameterized by the sample size $n$ , the failure probability $\\delta$ , and the variance $\\sigma^{2}$ , our estimator has additive accuracy within $\\sigma\\cdot(1+o(1))\\sqrt{\\frac{2\\log\\frac{1}{\\delta}}{n}}$ , which is optimal up to the $1+o(1)$ term. This asymptotically matches the convergence of the sample mean for the Gaussian distribution with the same variance. Our estimator construction and analysis gives a framework generalizable to other problems, tightly analyzing a sum of dependent random variables by viewing the sum implicitly as a 2-parameter $\\psi$ -estimator, and constructing bounds using mathematical programming and duality techniques.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00071"}, {"primary_key": "2134347", "vector": [], "sparse_vector": [], "title": "The Reachability Problem for Petri Nets is Not Primitive Recursive.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a way to lift up the Tower complexity lower bound of the reachability problem for Petri nets to match the Ackermannian upper bound closing a long standing open problem. We also prove that the reachability problem in dimension 17 is not elementary.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00121"}, {"primary_key": "2134348", "vector": [], "sparse_vector": [], "title": "A constant-factor approximation algorithm for Nash Social Welfare with submodular valuations.", "authors": ["Wenzheng Li", "<PERSON>"], "summary": "We present a 380-approximation algorithm for the Nash Social Welfare problem with submodular valuations. Our algorithm builds on and extends a recent constant-factor approximation for Rado valuations [15].", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00012"}, {"primary_key": "2134349", "vector": [], "sparse_vector": [], "title": "Settling the Horizon-Dependence of Sample Complexity in Reinforcement Learning.", "authors": ["<PERSON>zhi Li", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recently there is a surge of interest in under-standing the horizon-dependence of the sample complexity in reinforcement learning (RL). Notably, for an RL environment with horizon length H, previous work have shown that there is a probably approximately correct (PAC) algorithm that learns an O(1)-optimal policy using polylog(H) episodes of environment interactions when the number of states and actions is fixed. It is yet unknown whether the polylog (H) dependence is necessary or not. In this work, we resolve this question by developing an algorithm that achieves the same PAC guarantee while using only O(1) episodes of environment interactions, completely settling the horizon-dependence of the sample complexity in RL. We achieve this bound by (i) establishing a connection between value functions in discounted and finite-horizon Markov decision processes (MDPs) and (ii) a novel perturbation analysis in MDPs. We believe our new techniques are of independent interest and could be applied in related questions in RL.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00097"}, {"primary_key": "2134350", "vector": [], "sparse_vector": [], "title": "Superpolynomial Lower Bounds Against Low-Depth Algebraic Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "An Algebraic Circuit for a polynomial $P\\ \\ \\in \\mathbb{F}[x_{1}, \\ldots, x_{N}]$ is a computational model for constructing the polynomial $P$ using only additions and multiplications. It is a syntactic model of computation, as opposed to the Boolean Circuit model, and hence lower bounds for this model are widely expected to be easier to prove than lower bounds for Boolean circuits. Despite this, we do not have superpolynomial lower bounds against general algebraic circuits of depth 3 (except over constant-sized finite fields) and depth 4 (over fields other than $\\mathbb{F}_{2}$ ), while constant-depth Boolean circuit lower bounds have been known since the early 1980s. In this paper, we prove the first super polynomial lower bounds against general algebraic circuits of all constant depths over all fields of characteristic 0 (or large). We also prove the first lower bounds against homogeneous algebraic circuits of constant depth over any field. Our approach is surprisingly simple. We first prove superpolynomial lower bounds for constant-depth Set-Multilinear circuits. While strong lower bounds were already known against such circuits, most previous lower bounds were of the form $f(d)\\cdot \\text{poly}(N)$ , where $d$ denotes the degree of the polynomial. In analogy with Parameterized complexity, we call this an FPT lower bound. We extend a well-known technique of <PERSON><PERSON> and <PERSON> (FOCS 1995) to prove non-FPT lower bounds against constant-depth set-multilinear circuits computing the Iterated Matrix Multiplication polynomial $\\text{IMM}_{n, d}$ (which computes a fixed entry of the product of $d\\ n\\times n$ matrices). More precisely, we prove that any set-multilinear circuit of depth $\\Delta$ computing $\\text{IMM}_{n, d}$ must have size at least $n^{d^{\\exp(-O(\\Delta))}}$ . This result holds over any field, as long as $d=o(\\log n)$ . We then show how to convert any constant-depth algebraic circuit of size $s$ to a constant-depth set-multilinear circuit with a blow-up in size that is exponential in $d$ but only polynomial in $s$ over fields of characteristic 0. (For depths greater than 3, previous results of this form increased the depth of the resulting circuit to $\\Omega(\\log s))$ . This implies our constant-depth circuit lower bounds. Finally, we observe that our superpolynomial lower bound for constant-depth circuits implies the first deterministic sub-exponential time algorithm for solving the Polynomial Identity Testing (PIT) problem for all small depth circuits using the known connection between algebraic hardness and randomness.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00083"}, {"primary_key": "2134351", "vector": [], "sparse_vector": [], "title": "On statistical inference when fixed points of belief propagation are unstable.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many statistical inference problems correspond to recovering the values of a set of hidden variables from sparse observations on them. For instance, in a planted constraint satisfaction problem such as planted 3-SAT, the clauses are sparse observations from which the hidden assignment is to be recovered. In the problem of community detection in a stochastic block model, the community labels are hidden variables that are to be recovered from the edges of the graph. Inspired by ideas from statistical physics, the presence of a stable fixed point for belief propogation has been widely conjectured to characterize the computational tractability of these problems. For community detection in stochastic block models, many of these predictions have been rigorously confirmed. In this work, we consider a general model of statistical inference problems that includes both community detection in stochastic block models, and all planted constraint satisfaction problems as special cases. We carry out the cavity method calculations from statistical physics to compute the regime of parameters where detection and recovery should be algorithmically tractable. At precisely the predicted tractable regime, we give: (i) a general polynomial-time algorithm for the problem of detection: distinguishing an input with a planted signal from one without; (ii) a general polynomial-time algorithm for the problem of recovery: outputting a vector that correlates with the hidden assignment significantly better than a random guess would. Analogous to the spectral algorithm for community detection [1], [2], the detection and recovery algorithms are based on the spectra of a matrix that arises as the derivatives of the belief propagation update rule. To devise a spectral algorithm in our general model, we obtain bounds on the spectral norms of certain families of random matrices with correlated and matrix valued entries. We then demonstrate how eigenvectors of various powers of the matrix can be used to partially recover the hidden variables.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00047"}, {"primary_key": "2134352", "vector": [], "sparse_vector": [], "title": "Breaking the Cubic Barrier for (Unweighted) Tree Edit Distance.", "authors": ["<PERSON>"], "summary": "The (unweighted) tree edit distance problem for $n$ node trees asks to compute a measure of dissimilarity between two rooted trees with node labels. The current best algorithm from more than a decade ago runs in $O(n^{3})$ time [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, ICALP 2007]. The same paper also showed that $O(n^{3})$ is the best possible running time for any algorithm using the so-called decomposition strategy, which underlies almost all the known algorithms for this problem. These algorithms would also work for the weighted tree edit distance problem, which cannot be solved in truly sub-cubic time under the APSP conjecture [<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, SODA 2018]. In this paper, we break the cubic barrier by showing an $O(n^{2.9546})$ time algorithm for the unweighted tree edit distance problem. We consider an equivalent maximization problem and use a dynamic programming scheme involving matrices with many special properties. By using a decomposition scheme as well as several combinatorial techniques, we reduce tree edit distance to the max-plus product of bounded-difference matrices, which can be solved in truly sub-cubic time [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, FOCS 2016].", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00082"}, {"primary_key": "2134353", "vector": [], "sparse_vector": [], "title": "On Classifying Continuous Constraint Satisfaction problems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "A continuous constraint satisfaction problem (CCSP) is a constraint satisfaction problem (CSP) with an interval domain $U\\subset \\mathbb{R}$ . We engage in a systematic study to classify CCSPs that are complete of the Existential Theory of the Reals, i.e., $\\exists \\mathbb{R}$ -complete. To define this class, we first consider the problem ETR, which also stands for Existential Theory of the Reals. In an instance of this problem we are given some sentence of the form $\\exists x_{1}, \\ldots, x_{n}\\in \\mathbb{R}$ : $\\Phi(x_{1},\\ldots,\\ x_{n})$ , where $\\Phi$ is a well-formed quantifier-free formula consisting of the symbols $\\{0,1,\\ x_{1},\\ldots,\\ x_{n},\\ +,\\ \\cdot,\\ \\geq,\\ &gt;, \\ \\wedge,\\ \\vee,\\ \\neg\\}$ , the goal is to check whether this sentence is true. Now the class $\\exists \\mathbb{R}$ is the family of all problems that admit a polynomial-time many-one reduction to ETR. It is known that NP $\\subseteq\\exists \\mathbb{R}\\subseteq$ PSPACE. We restrict our attention on CCSPs with addition constraints $(x+y=z)$ and some other mild technical condition. Previously, it was shown that multiplication constraints $(x\\cdot y=z)$ , squaring constraints $(x^{2}=y)$ , or inversion constraints $(x\\cdot y=1)$ are sufficient to establish $\\exists \\mathbb{R}$ -completeness. We extend this in the strongest possible sense for equality constraints as follows. We show that CCSPs (with addition constraints and some other mild technical condition) that have any one well-behaved curved equality constraint $(f(x,\\ y)=0)$ are $\\exists \\mathbb{R}$ -complete. We further extend our results to inequality constraints. We show that any well-behaved convexly curved and any well-behaved concavely curved inequality constraint $(f(x,\\ y)\\geq 0$ and $g(x,\\ y)\\geq 0)$ imply $\\exists \\mathbb{R}$ -completeness on the class of such CCSPs. Here, we call a function $f: U^{2}\\rightarrow\\mathbb{R}$ well-behaved if it is a $C^{2}$ -function, $f(0,0)=0$ , all its partial derivatives $f_{x}, f_{y}, f$ are rational in $(0,0), f_{x}(0,0)\\neq 0$ or $f_{y}(0,0)\\neq 0$ , and it can be computed on a real RAM. Furthermore we call $f$ curved if the curvature of the curve given by $f(x,\\ y)=0$ is nonzero, at the origin. In this case we call $f$ either convexly curved if the curvature is negative, or concavely curved if it is positive. We apply our findings to geometric packing and answer an open question by Abrahamsen et al. [1, FOCS 2020]. Namely, we establish $\\exists\\mathbb{R}$ -completeness of packing convex pieces into a square container under rotations and translations. This work is based on the master's thesis of the second author [2]. The full version of this paper can be found on arXiv [3].", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00081"}, {"primary_key": "2134354", "vector": [], "sparse_vector": [], "title": "Approximating Maximum Independent Set for Rectangles in the Plane.", "authors": ["<PERSON>"], "summary": "We give a polynomial-time constant-factor approximation algorithm for maximum independent set for (axis-aligned) rectangles in the plane. Using a polynomial-time algorithm, the best approximation factor previously known is $O(\\log\\log n)$ . The results are based on a new form of recursive partitioning in the plane, in which faces that are constant-complexity and orthogonally convex are recursively partitioned into a constant number of such faces.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00042"}, {"primary_key": "2134355", "vector": [], "sparse_vector": [], "title": "New data structure for univariate polynomial approximation and applications to root isolation, numerical multipoint evaluation, and other problems.", "authors": ["<PERSON>"], "summary": "We present a new data structure to approximate accurately and efficiently a polynomial $f$ of degree $d$ given as a list of coefficients f i . Its properties allow us to improve the state-of-the-art bounds on the bit complexity for the problems of root isolation and approximate multi-point evaluation. This data structure also leads to a new geometric criterion to detect ill-conditioned polynomials, implying notably that the standard condition number of the zeros of a polynomial is at least exponential in the number of roots of modulus less than 1/2 or greater than 2. Given a polynomial $f$ of degree $d$ with ║f║ 1 = Σ | f i | ≤ 2 τ for τ ≥ 1, isolating all its complex roots or evaluating it at $d$ points can be done with a quasi-linear number of arithmetic operations. However, considering the bit complexity, the state-of-the-art algorithms require at least d 3/2 bit operations even for well-conditioned polynomials and when the accuracy required is low. Given a positive integer $m$ , we can compute our new data structure and evaluate $f$ at $d$ points in the unit disk with an absolute error less than 2 −m in Õ(d(τ + m)) bit operations, where Õ(.) means that we omit logarithmic factors. We also show that if κ is the absolute condition number of the zeros of f, then we can isolate all the roots of $f$ in Õ(d(τ + log κ)) bit operations. Moreover, our algorithms are simple to implement. For approximating the complex roots of a polynomial, we implemented a small prototype in Python/NumPy that is an order of magnitude faster than the state-of-the-art solver MPSolve for high degree polynomials with random coefficients.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00108"}, {"primary_key": "2134356", "vector": [], "sparse_vector": [], "title": "One-step replica symmetry breaking of random regular NAE-SAT.", "authors": ["<PERSON>", "<PERSON>", "Young<PERSON><PERSON>"], "summary": "In a broad class of sparse random constraint satisfaction problems (CSP), deep heuristics from statistical physics predict that there is a condensation phase transition before the satisfiability threshold, governed by one-step replica symmetry breaking (1RSB). In fact, in random regular k-NAE-SAT, which is one of such random CSPS, it was verified [1] that its free energy is well-defined and the explicit value follows the 1RSB prediction. However, for any model of sparse random CSP, it has been unknown whether the solution space indeed condensates on O(1) clusters according to the 1RSB prediction. In this paper, we give an affirmative answer to this question for the random regular k-NAE-SAT model. Namely, we prove that with probability close to one, most of the solutions lie inside a bounded number of solution clusters whose sizes are comparable to the scale of the free energy. Furthermore, we establish that the overlap between two independently drawn solutions concentrates precisely at two values. This is the defining property of the one-step replica symmetry breaking class which we establish for the first time in a sparse random CSP, Our proof is based on a detailed moment analysis of a spin system, which has an infinite spin space that encodes the structure of solution clusters. We develop new techniques to study the partition function as well as enhance previous approaches which were only applicable to spin systems with finitely many spins. We believe that our method is applicable to a broad range of random CSPS in the 1RSB universality class.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00039"}, {"primary_key": "2134357", "vector": [], "sparse_vector": [], "title": "Continuity, Uniqueness and Long-Term Behavior of Nash Flows Over Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider a dynamic model of traffic that has received a lot of attention in the past few years. Users control infinitesimal flow particles aiming to travel from a source to destination as quickly as possible. Flow patterns vary over time, and congestion effects are modeled via queues, which form whenever the inflow into a link exceeds its capacity. Despite lots of interest, some very basic questions remain open in this model. We resolve a number of them: • We show uniqueness of journey times in equilibria. • We show continuity of equilibria: small perturbations to the instance or to the traffic situation at some moment cannot lead to wildly different equilibrium evolutions. • We demonstrate that, assuming constant inflow into the network at the source, equilibria always settle down into a \"steady state\" in which the behavior extends forever in a linear fashion. One of our main conceptual contributions is to show that the answer to the first two questions, on uniqueness and continuity, are intimately connected to the third. Our result also shows very clearly that resolving uniqueness and continuity, despite initial appearances, cannot be resolved by analytic techniques, but are related to very combinatorial aspects of the model. To resolve the third question, we substantially extend the approach of <PERSON><PERSON><PERSON> et al. [1], who show a steady-state result in the regime where the input flow rate is smaller than the network capacity. The full version of this extended abstract can be found on the arXiv preprint server as article 2111.06877", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00087"}, {"primary_key": "2134358", "vector": [], "sparse_vector": [], "title": "At most 3.55n stable matchings.", "authors": ["<PERSON>", "Dömötör <PERSON>lvölgyi"], "summary": "We improve the upper bound for the maximum possible number of stable matchings among $n$ jobs and $n$ applicants from 131072 n + O(1) to 3.55 n + O(1). To establish this bound, we state a novel formulation of a certain entropy bound that is easy to apply and may be of independent interest in counting other combinatorial objects.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00029"}, {"primary_key": "2134359", "vector": [], "sparse_vector": [], "title": "Tradeoffs for small-depth Frege proofs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the complexity of small-depth Frege proofs and give the first tradeoffs between the size of each line and the number of lines. Existing lower bounds apply to the overall proof size-the sum of sizes of all lines-and do not distinguish between these notions of complexity. For depth-d Frege proofs of the Tseitin principle where each line is a size-s formula, we prove that $\\exp(n/2^{\\Omega(d\\sqrt{\\log s})})$ many lines are necessary. This yields new lower bounds on line complexity that are not implied by $\\mathbf{H}\\mathop{\\mathbf{a}}\\!\\!\\!\\!^{\\circ}\\mathbf{stad}$ 's recent $\\exp(n^{\\Omega(1/d)})$ lower bound on the overall proof size. For $s$ = poly $(n)$ , for example, our lower bound remains $\\exp(n^{1-o(1)})$ for all $d=o(\\sqrt{\\log n})$ , whereas $\\mathbf{H}\\mathop{\\mathbf{a}}\\!\\!\\!\\!^{\\circ}\\mathbf{stad}$ 's lower bound is $\\exp(n^{o(1)})$ once $d\\ = \\omega_{n}(1)$ . Our main conceptual contribution is the simple obser-vation that techniques for establishing correlation bounds in circuit complexity can be leveraged to establish such tradeoffs in proof complexity.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00052"}, {"primary_key": "2134360", "vector": [], "sparse_vector": [], "title": "Amortized Circuit Complexity, Formal Complexity Measures, and Catalytic Algorithms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the amortized circuit complexity of boolean functions. Given a circuit model $\\mathcal{F}$ and a boolean function $f:\\{0,1\\}^{n}\\rightarrow\\{0,1\\}$ , the $\\mathcal{F}$ -amortized circuit complexity is defined to be the size of the smallest circuit that outputs $m$ copies of $f$ (evaluated on the same input), divided by $m$ , as $m\\rightarrow\\infty$ . We prove a general duality theorem that characterizes the amortized circuit complexity in terms of \"formal complexity measures\". More precisely, we prove that the amortized circuit complexity in any circuit model composed out of gates from a finite set is equal to the pointwise maximum of the family of \"formal complexity measures\" associated with $\\mathcal{F}$ . Our duality theorem captures many of the formal complexity measures that have been previously studied in the literature for proving lower bounds (such as formula complexity measures, submodular complexity measures, and branching program complexity measures), and thus gives a characterization of formal complexity measures in terms of circuit complexity. We also introduce and investigate a related notion of catalytic circuit complexity, which we show is \"intermediate\" between amortized circuit complexity and standard circuit complexity, and which we also characterize (now, as the best integer solution to a linear program). Finally, using our new duality theorem as a guide, we strengthen the known upper bounds for non-uniform catalytic space, introduced by <PERSON><PERSON><PERSON> et. al [1] (this is related to, but not the same as, our notion of catalytic circuit size). <PERSON><PERSON><PERSON> [2] proved that for any boolean function $f:\\{0,1\\}^{n}\\rightarrow\\{0,1\\}$ , there is a catalytic branching program computing $m=2^{2^{n}-1}$ copies of $f$ with total size $O(mn)$ -that is, linear size per copy — refuting a conjecture of Girard, Koucký and McKenzie [3]. Potechin then asked if the number of copies $m$ can be reduced while retaining the amortized upper bound. We make progress on this question by showing that if $f$ has degree $d$ when represented as polynomial over $\\mathbb{F}_{2}$ , then there is a catalytic branching program computing $m=2^{\\begin{pmatrix}n\\\\ \\leq d\\end{pmatrix}}$ copies of $f$ with total size $O(mn)$ .", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00079"}, {"primary_key": "2134361", "vector": [], "sparse_vector": [], "title": "Almost Optimal Inapproximability of Multidimensional Packing Problems.", "authors": ["<PERSON>"], "summary": "Multidimensional packing problems generalize the classical packing problems such as Bin Packing, Multiprocessor Scheduling by allowing the jobs to be d-dimensional vectors. While the approximability of the scalar problems is well understood, there has been a significant gap between the approximation algorithms and the hardness results for the multidimensional variants. In this paper, we close this gap by giving almost tight hardness results for these problems. 1)We show that Vector Bin Packing has no $\\Omega(\\log d)$ factor asymptotic approximation algorithm when $d$ is a large constant, assuming $\\mathrm{P}\\neq\\text{NP}$ . This matches the ln d + O (1) factor approximation algorithms (Chekuri, Khanna SICOMP 2004, Bansal, Caprara, Sviridenko SICOMP 2009, Bansal, Eli<PERSON>, Khan SODA 2016) upto constants. 2)We show that Vector Scheduling has no polyno-mial time algorithm with an approximation ratio of $\\Omega((\\log d)^{1-\\epsilon})$ when $d$ is part of the input, assuming $\\text{NP}\\nsubseteq$ ZPTIME $\\left(n^{(\\log n)^{O(1)}}\\right)$ . This almost matches the $O\\left(\\frac{\\log d}{\\log\\log d}\\right)$ factor algorithms(<PERSON>, <PERSON><PERSON> 2019, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> 2019). We also show that the problem is NP-hard to approximate within $(\\log \\log d)^{\\omega(1)}$ . 3)We show that Vector Bin Covering is NP-hard to approx-imate within $\\Omega\\left(\\frac{\\log d}{\\log\\log d}\\right)$ when $d$ is part of the input, almost matching the O (log d) factor algorithm (Alon et al., Algorithmica 1998). Previously, no hardness results that grow with $d$ were known for Vector Scheduling and Vector Bin Covering when $d$ is part of the input and for Vector Bin Packing when $d$ is a fixed constant.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00033"}, {"primary_key": "2134362", "vector": [], "sparse_vector": [], "title": "PPSZ is better than you think.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "PPSZ, for long time the fastest known algorithm for $k$ -SAT, works by going through the variables of the input formula in random order; each variable is then set randomly to 0 or 1, unless the correct value can be inferred by an efficiently implementable rule (like small-width resolution; or being implied by a small set of clauses). We show that PPSZ performs exponentially better than previously known, for all $k\\geq 3$ . For Unique-3-SAT we bound its running time by $O(1.306973^{n})$ , which is somewhat better than the algorithm of <PERSON>, <PERSON>, <PERSON>, and <PERSON>. All improvements are achieved without changing the original PPSZ. The core idea is to pretend that PPSZ does not process the variables in uniformly random order, but according to a carefully designed distribution. We write \"pretend\" since this can be done without any actual change to the algorithm. 2 2 The full version of this paper can be found under https://eccc.weizmann.ac.il/report/2021/069/", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00028"}, {"primary_key": "2134363", "vector": [], "sparse_vector": [], "title": "A Better-Than-2 Approximation for Weighted Tree Augmentation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present an approximation algorithm for Weighted Tree Augmentation with approximation factor 1 + $\\ln 2+\\varepsilon &lt; 1.7$ . This is the first algorithm beating the longstanding factor of 2, which can be achieved through many standard techniques. −", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00010"}, {"primary_key": "2134364", "vector": [], "sparse_vector": [], "title": "FOCS 2021 Preface.", "authors": ["Nisheeth K. <PERSON>"], "summary": "The papers in these proceedings were presented at the 62nd Annual IEEE Symposium on the Foundations of Computer Science, which was held on February 7-10, 2022, through a virtual format. The conference is sponsored by the IEEE Computer Society Technical Committee on Mathematical Foundations of Computing.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00005"}, {"primary_key": "2134365", "vector": [], "sparse_vector": [], "title": "The supersingular isogeny path and endomorphism ring problems are equivalent.", "authors": ["<PERSON>"], "summary": "We prove that the path-finding problem in isogeny graphs and the endomorphism ring problem for supersingular elliptic curves are equivalent under reductions of polynomial expected time, assuming the generalised Riemann hypothesis. The presumed hardness of these problems is foundational for isogeny-based cryptography. As an essential tool, we develop a rigorous algorithm for the quaternion analog of the path-finding problem, building upon the heuristic method of <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>ign<PERSON>. This problem, and its (previously heuristic) resolution, are both a powerful cryptanalytic tool and a building-block for cryptosystems. This is an extended abstract of the full article available at http://arxiv.org/abs/2111.01481. This full article will be referred to as \"the full version\" throughout the text.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00109"}, {"primary_key": "2134366", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Adversarially Robust Streams and Sliding Windows via Difference Estimators.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In the adversarially robust streaming model, a stream of elements is presented to an algorithm and is allowed to depend on the output of the algorithm at earlier times during the stream. In the classic insertion-only model of data streams, <PERSON><PERSON><PERSON><PERSON> et al. (PODS 2020, best paper award) show how to convert a non-robust algorithm into a robust one with a roughly $1/\\varepsilon$ factor overhead. This was subsequently improved to a $1/\\sqrt{\\varepsilon}$ factor overhead by <PERSON><PERSON><PERSON><PERSON> et al. (NeurIPS 2020, oral presentation), suppressing logarithmic factors. For general functions the latter is known to be best-possible, by a result of <PERSON> et al. (CRYPTO 2021). We show how to bypass this impossibility result by developing data stream algorithms for a large class of streaming problems, with no overhead in the approximation factor. Our class of streaming problems includes the most well-studied problems such as the $L_{2}$ -heavy hitters problem, $F_{p}$ -moment estimation, as well as empirical entropy estimation. We substantially improve upon all prior work on these problems, giving the first optimal dependence on the approximation factor. As in previous work, we obtain a general transformation that applies to any non-robust streaming algorithm and depends on the so-called flip number. However, the key technical innovation is that we apply the transformation to what we call a difference estimator for the streaming problem, rather than an estimator for the streaming prob-lem itself. We then develop the first difference estimators for a wide range of problems. Our difference estimator methodology is not only applicable to the adversarially ro-bust model, but to other streaming models where temporal properties of the data play a central role. To demonstrate the generality of our technique, we additionally introduce a general framework for the related sliding window model of data streams and resolve longstanding open questions in that model, obtaining a drastic improvement from the previous $1/\\varepsilon^{2+p}$ dependence for $F_{p}$ -moment estimation for $p\\in$ [1], [2] and integer $p &gt; 2$ of Braverman and Ostrovsky (FOCS, 2007), to the optimal $1/\\varepsilon^{2}$ bound. We also improve the prior $1/\\varepsilon^{3}$ bound for $p\\in[0,1)$ , and the prior $1/-\\varepsilon^{4}$ bound for empirical entropy, obtaining the first optimal $1/\\varepsilon^{2}$ dependence for both of these problems as well. Qualitatively, our results show there is no separation between the sliding window model and the standard data stream model in terms of the approximation factor.", "published": "2021-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS52979.2021.00116"}]