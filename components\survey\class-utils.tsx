
interface Paper {
    title: string;
    authors: string[];
    summary: string; // 原文摘要
    pdf_url: string; // 论文链接
    published: string; // 发表时间
    source: string; // 发表会议
    distance: number; // 相似度分数(浮点数)

    translated_title?: string;
    abstract_summary?: string; // 中文简短总结
    translation?: string; // 中文摘要
    title_translation?: string;
    tags?: string[]; // 标签
}

export type { Paper };