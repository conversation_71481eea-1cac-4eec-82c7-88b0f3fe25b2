[{"primary_key": "2249587", "vector": [], "sparse_vector": [], "title": "Allocating Indivisible Goods to Strategic Agents: Pure Nash Equilibria and Fairness.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of fairly allocating a set of indivisible goods to a set ofstrategicagents with additive valuation functions. We assume no monetary transfers and, therefore, amechanismin our setting is an algorithm that takes as input the reported—rather than the true—values of the agents. Our main goal is to explore whether there exist mechanisms that have pure Nash equilibria for every instance and, at the same time, provide fairness guarantees for the allocations that correspond to these equilibria. We focus on two relaxations of envy-freeness, namelyenvy-freeness up to one good(\\(\\text {EF1}\\)), andenvy-freeness up to any good(\\(\\text {EFX}\\)), and we positively answer the above question. In particular, we study two algorithms that are known to produce such allocations in the non-strategic setting: Round-Robin (\\(\\text {EF1}\\)allocations for any number of agents) and a cut and choose algorithm of Plaut and Roughgarden [35] (\\(\\text {EFX}\\)allocations for two agents). For Round-Robin we show that all of its pure Nash equilibria induce allocations that are\\(\\text {EF1}\\)with respect to the underlying true values, while for the algorithm of <PERSON><PERSON><PERSON> and <PERSON><PERSON>en we show that the corresponding allocations not only are\\(\\text {EFX}\\)but also satisfymax<PERSON>in share fairness, something that is not true for this algorithm in the non-strategic setting! Further, we show that a weaker version of the latter result holds for any mechanism for two agents that always has pure Nash equilibria which all induce\\(\\text {EFX}\\)allocations.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_9"}, {"primary_key": "2249588", "vector": [], "sparse_vector": [], "title": "The Distortion of Distributed Metric Social Choice.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider a social choice setting with agents that are partitioned into disjoint groups, and have metric preferences over a set of alternatives. Our goal is to choose a single alternative aiming to optimize various objectives that are functions of the distances between agents and alternatives in the metric space, under the constraint that this choice must be made in a distributed way: The preferences of the agents within each group are first aggregated into a representative alternative for the group, and then these group representatives are aggregated into the final winner. Deciding the winner in such a way naturally leads to loss of efficiency, even when complete information about the metric space is available. We provide a series of (mostly tight) bounds on the distortion of distributed mechanisms for variations of well-known objectives, such as the (average) total cost and the maximum cost, and also for new objectives that are particularly appropriate for this distributed setting and have not been studied before.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_26"}, {"primary_key": "2249589", "vector": [], "sparse_vector": [], "title": "Beyond Pigouvian Taxes: A Worst Case Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the early 20thcentury, <PERSON><PERSON> observed that imposing a marginal cost tax on the usage of a public good induces a socially efficient level of use as an equilibrium. Unfortunately, such a “Pigouvian” tax may also induce other, socially inefficient, equilibria. We observe that this social inefficiency may be unbounded, and study whether alternative tax structures may lead to milder losses in the worst case, i.e. to a lower price of anarchy. We show that no tax structure leads to bounded losses in the worst case. However, we do find a tax scheme that has a lower price of anarchy than the Pigouvian tax, obtaining tight lower and upper bounds in terms of a crucial parameter that we identify. We generalize our results to various scenarios that each offers an alternative to the use of a public road by private cars, such as ride sharing, or using a bus or a train.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_13"}, {"primary_key": "2249590", "vector": [], "sparse_vector": [], "title": "Formal Barriers to Simple Algorithms for the Matroid Secretary Problem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> et al. [4] introduced the matroid secretary problem in 2007, a natural extension of the classic single-choice secretary problem to matroids, and conjectured that a constant-competitive online algorithm exists. The conjecture still remains open despite substantial partial progress, including constant-competitive algorithms for numerous special cases of matroids, and an\\(O(\\log \\log \\text {rank})\\)-competitive algorithm in the general case. Many of these algorithms follow principled frameworks. The limits of these frameworks are previously unstudied, and prior work establishes only that a handful of particular algorithms cannot resolve the matroid secretary conjecture. We initiate the study of impossibility results for frameworks to resolve this conjecture. We establish impossibility results for a natural class of greedy algorithms and for randomized partition algorithms, both of which contain known algorithms that resolve special cases\n.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_16"}, {"primary_key": "2249591", "vector": [], "sparse_vector": [], "title": "Threshold Tests as Quality Signals: Optimal Strategies, Equilibria, and Price of Anarchy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study a signaling game between two firms competing to have their product chosen by a principal. The products have (real-valued) qualities, which are drawn i.i.d. from a common prior. The principal aims to choose the better of the two products, but the quality of a product can only be estimated via a coarse-grainedthreshold test: given a threshold\\(\\theta \\), the principal learns whether a product’s quality exceeds\\(\\theta \\)or fails to do so. We study this selection problem under two types of interactions. In the first, the principal does the testing herself, and can choose tests optimally from a class of allowable tests. We show that the optimum strategy for the principal is to administerdifferenttests to the two products: one which is passed with probability\\(\\frac{1}{3}\\)and the other with probability\\(\\frac{2}{3}\\). If, however, the principal is required to choose the tests in a symmetric manner (i.e., via an i.i.d. distribution), then the optimal strategy is to choose tests whose probability of passing is drawn uniformly from\\([\\frac{1}{4}, \\frac{3}{4}]\\). In our second interaction model, test difficulties are selected endogenously by the two firms. This corresponds to a setting in which the firms must commit to their testing (quality control) procedures before knowing the quality of their products. This interaction model naturally gives rise to asignaling gamewith two senders and one receiver. We characterize the unique Bayes-Nash Equilibrium of this game, which happens to be symmetric. We then calculate its Price of Anarchy in terms of the principal’s probability of choosing the worse product. Finally, we show that by restricting both firms’ set of available thresholds to choose from, the principal can lower the Price of Anarchy of the resulting equilibrium; however, there is a limit, in that for every (common) restricted set of tests, the equilibrium failure probability is strictly larger than under the optimal i.i.d. distribution.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_17"}, {"primary_key": "2249592", "vector": [], "sparse_vector": [], "title": "Approximating Nash Social Welfare Under Binary XOS and Binary Subadditive Valuations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of allocating indivisible goods among agents in a fair and economically efficient manner. In this context, the Nash social welfare—defined as the geometric mean of agents’ valuations for their assigned bundles—stands as a fundamental measure that quantifies the extent of fairness of an allocation. Focusing on instances in which the agents’ valuations have binary marginals, we develop essentially tight results for (approximately) maximizing Nash social welfare under two of the most general classes of complement-free valuations, i.e., under binary\\(\\mathrm {XOS}\\)and binary subadditive valuations. For binary\\(\\mathrm {XOS}\\)valuations, we develop a polynomial-time algorithm that finds a constant-factor (specifically\\(288\\)) approximation for the optimal Nash social welfare, in the standard value-oracle model. The allocations computed by our algorithm also achieve constant-factor approximation for social welfare and the groupwise maximin share guarantee. These results imply that—in the case of binary\\(\\mathrm {XOS}\\)valuations—there necessarily exists an allocation that simultaneously satisfies multiple (approximate) fairness and efficiency criteria. We complement the algorithmic result by proving that Nash social welfare maximization is\\(\\mathrm {APX}\\)-hard under binary\\(\\mathrm {XOS}\\)valuations. Furthermore, this work establishes an interesting separation between the binary\\(\\mathrm {XOS}\\)and binary subadditive settings. In particular, we prove that an exponential number of value queries are necessarily required to obtain even a sub-linear approximation for Nash social welfare under binary subadditive valuations.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_21"}, {"primary_key": "2249593", "vector": [], "sparse_vector": [], "title": "The Optimality of Upgrade Pricing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider a multiproduct monopoly pricing model. We provide sufficient conditions under which the optimal mechanism can be implemented via upgrade pricing—a menu of product bundles that are nested in the strong set order. Our approach exploits duality methods to identify conditions on the distribution of consumer types under which (a) each product is purchased by the same set of buyers as under separate monopoly pricing (though the transfers can be different), and (b) these sets are nested. We exhibit two distinct sets of sufficient conditions. The first set of conditions weakens the monotonicity requirement of types and virtual values but maintains aregularityassumption, i.e., that the product-by-product revenue curves are single-peaked. The second set of conditions establishes the optimality of upgrade pricing for type spaces withmonotone marginal rates of substitution (MRS)—the relative preference ratios for any two products are monotone across types. The monotone MRS condition allows us to relax the earlier regularity assumption. Under both sets of conditions, we fully characterize the product bundles and prices that form the optimal upgrade pricing menu. Finally, we show that, if the consumer’s types are monotone, the seller can equivalently post a vector of single-item prices: upgrade pricing and separate pricing are equivalent.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_3"}, {"primary_key": "2249595", "vector": [], "sparse_vector": [], "title": "Relaxing the Independence Assumption in Sequential Posted Pricing, Prophet Inequality, and Random Bipartite Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We reexamine three classical settings ofoptimization under uncertainty, which have been extensively studied in the past, assuming that the several random events involved are mutually independent. Here, we assume that such events are only pair-wise independent; this gives rise to a much richer space of instances. Our aim has been to explore whether positive results are possible even under the more general assumptions. We show that this is indeed the case. Indicatively, we show that, when applied to pair-wise independent distributions of buyer values, sequential posted pricing mechanisms get at least\\(\\frac{1}{1.299}\\)of the revenue they get from mutually independent distributions with the same marginals. We also adapt the well-known prophet inequality to pair-wise independent distributions of prize values to get a 1/3-approximation using a non-standard uniform threshold strategy. Finally, in a stochastic model of generating random bipartite graphs with pair-wise independence on the edges, we show that the expected size of the maximum matching is large but considerably smaller than in Erdős-Renyi random graph models where edges are selected independently. Our techniques include a technical lemma that might find applications in other interesting settings involving pair-wise independence.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_8"}, {"primary_key": "2249596", "vector": [], "sparse_vector": [], "title": "Computing Envy-Freeable Allocations with Limited Subsidies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fair division has emerged as a very hot topic in EconCS research, and envy-freeness is among the most compelling fairness concepts. An allocation of indivisible items to agents is envy-free if no agent prefers the bundle of any other agent to his own in terms of value. As envy-freeness is rarely a feasible goal, there is a recent focus on relaxations of its definition. An approach in this direction is to complement allocations with payments (or subsidies) to the agents. A feasible goal then is to achieve envy-freeness in terms of the total value an agent gets from the allocation and the subsidies. We consider the natural optimization problem of computing allocations that areenvy-freeableusing the minimum amount of subsidies. As the problem is NP-hard, we focus on the design of approximation algorithms. On the positive side, we present an algorithm which, for a constant number of agents, approximates the minimum amount of subsidies within any required accuracy, at the expense of a graceful increase in the running time. On the negative side, we show that, for a superconstant number of agents, the problem of minimizing subsidies for envy-freeness is not only hard to compute exactly (as a folklore argument shows) but also, more importantly, hard to approximate.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_29"}, {"primary_key": "2249599", "vector": [], "sparse_vector": [], "title": "Maximal Information Propagation via Lotteries.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Propagating information to more people through their friends is becoming an increasingly important technology used in domains such as blockchain, advertising, and social media. To incentivize people to broadcast the information, the designer may use a monetary rewarding scheme, which specifies who gets how much, to compensate for the propagation. Several properties are desirable for the rewarding scheme, such as budget feasible, individually rational, incentive compatible and Sybil-proof. In this work, we design a free market with lotteries, where every participant can decide by herself how much of the reward she wants to withhold before propagating to others. We show that in the free market, the participants have a strong incentive to maximally propagate the information and all the above properties are satisfied automatically.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_27"}, {"primary_key": "2249600", "vector": [], "sparse_vector": [], "title": "Decentralized Asset Custody Scheme with Security Against Rational Adversary.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Asset custody is a core financial service in which the custodian holds in-safekeeping assets on behalf of the client. Although traditional custody service is typically endorsed by centralized authorities, decentralized custody scheme has become technically feasible since the emergence of digital assets, and furthermore, it is greatly needed by new applications such as blockchain and DeFi (Decentralized Finance). In this work, we propose a framework of decentralized asset custody scheme that is able to support a large number of custodians and safely hold customer assets of multiple times the value of the total security deposit. The proposed custody scheme distributes custodians and assets into many custodian groups via combinatorial designs, where each group fully controls the assigned assets. Since every custodian group is small, the overhead cost is significantly reduced. The liveness is also improved because even a single alive group would be able to process transactions. The security of this custody scheme is guaranteed under the rational adversary model, i.e. any adversary corrupting a bounded fraction of custodians cannot move assets more than the security deposit paid. We further analyze the security and performance of our constructions from both theoretical and experimental sides, and provide explicit examples with concrete numbers and figures for a better understanding.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_25"}, {"primary_key": "2249601", "vector": [], "sparse_vector": [], "title": "Welfare-Preserving ε-BIC to BIC Transformation with Negligible Revenue Loss.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we provide a transform from an\\(\\varepsilon \\)-BIC mechanism into an exactly BIC mechanism without any loss of social welfare and with additive and negligible revenue loss. This is the first\\(\\varepsilon \\)-BIC to BIC transformation that preserves welfare and provides negligible revenue loss. The revenue loss bound is tight given the requirement to maintain social welfare. Previous\\(\\varepsilon \\)-BIC to BIC transformations preserve social welfare but have no revenue guarantee [4], or suffer welfare loss while incurring a revenue loss with both a multiplicative and an additive term, e.g., [9,14,28]. The revenue loss achieved by our transformation is incomparable to these earlier approaches and can be significantly less. Our approach is different from the previous replica-surrogate matching methods and we directly make use of a directed and weighted type graph (induced by the types’ regret), one for each agent. The transformation runs afractional rotation stepand apayment reducing stepiteratively to make the mechanism Bayesian incentive compatible. We also analyze\\(\\varepsilon \\)-expected ex-post IC (\\(\\varepsilon \\)-EEIC) mechanisms [18]. We provide a welfare-preserving transformation in this setting with the same revenue loss guarantee for uniform type distributions and give an impossibility result for non-uniform distributions. We apply the transform to linear-programming based and machine-learning based methods of automated mechanism design.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_5"}, {"primary_key": "2249603", "vector": [], "sparse_vector": [], "title": "On Symmetries in Multi-dimensional Mechanism Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider a revenue-maximizing seller with multiple items for sale to a single population of buyers. Our main result shows that for a single population of additive buyers with independent (but not necessarily identically distributed) item values, bundling all items together achieves a constant-factor approximation to the revenue-optimal item-symmetric mechanism. We further motivate this direction via fairness in ad auctions. In ad auction domains the items correspond to views from particular demographics, and recent works have therefore identified a novelfairness constraint: equally-qualified users from different demographics should be shown the same desired ad at equal rates. Prior work abstracts this to the following fairness guarantee: if an advertiser places an identical bid on two users, those two users should view the ad with the same probability [27,34]. We first propose a relaxation of this guarantee from worst-case to Bayesian settings, which circumvents strong impossibility results from these works, and then study this guarantee through the lens of symmetries, as any item-symmetric auction is also fair (by this definition). Observe that in this domain, bundling all items together corresponds to concealing all demographic data [23].", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_4"}, {"primary_key": "2249604", "vector": [], "sparse_vector": [], "title": "A Tight Negative Example for MMS Fair Allocations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of allocating indivisible goods to agents with additive valuation functions. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON> [JACM, 2018] present instances for which every allocation gives some agent less than her maximin share. We present such examples with larger gaps. For three agents and nine items, we design an instance in which at least one agent does not get more than a\\(\\frac{39}{40}\\)fraction of her maximin share. Moreover, we show that there is no negative example in which the difference between the number of items and the number of agents is smaller than six, and that the gap (of\\(\\frac{1}{40}\\)) of our example is worst possible among all instances with nine items. For\\(n \\ge 4\\)agents, we show examples in which at least one agent does not get more than a\\(1 - \\frac{1}{n^4}\\)fraction of her maximin share. In the instances designed by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, the gap is exponentially small inn. Our proof techniques extend to allocation of chores (items of negative value), though the quantitative bounds for chores are different from those for goods. For three agents and nine chores, we design an instance in which the MMS gap is\\(\\frac{1}{43}\\).", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_20"}, {"primary_key": "2249606", "vector": [], "sparse_vector": [], "title": "Two-Way Greedy: Algorithms for Imperfect Rationality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The realization that selfish interests need to be accounted for in the design of algorithms has produced many interesting and valuable contributions in computer science under the general umbrella of algorithmic mechanism design. Novel algorithmic properties and paradigms have been identified and studied in the literature. Our work stems from the observation that selfishness is different from rationality; agents will attempt to strategize whenever they perceive it to be convenient according to their imperfect rationality. Recent work in economics [18] has focused on a particular notion of imperfect rationality, namely absence of contingent reasoning skills, and defined obvious strategyproofness (OSP) as a way to deal with the selfishness of these agents. Essentially, this definition states that to care for the incentives of these agents, we need not only pay attention about the relationship between input and output, but also about the way the algorithm is run. However, it is not clear to date what algorithmic approaches ought to be used for OSP. In this paper, we rather surprisingly show that, for binary allocation problems, OSP is fully captured by a natural combination of two well-known and extensively studied algorithmic techniques: forward and reverse greedy. We call two-way greedy this underdeveloped algorithmic design paradigm. Our main technical contribution establishes the connection between OSP and two-way greedy. We build upon the recently introduced cycle monotonicity technique for OSP [9]. By means of novel structural properties of cycles and queries of OSP mechanisms, we fully characterize these mechanisms in terms of extremal implementations. These are protocols that ask each agent to consistently separate one extreme of their domain at the current history from the rest. Through the natural connection with the greedy paradigm, we are able to import a host of known approximation bounds to OSP and strengthen the strategic properties of this family of algorithms. Finally, we begin exploring the full power of two-way greedy (and, in turns, OSP) in the context of set systems.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_1"}, {"primary_key": "2249607", "vector": [], "sparse_vector": [], "title": "Strategyproof Facility Location in Perturbation Stable Instances.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the approximability ofk-Facility Location games on the real line by strategyproof mechanisms without payments. To circumvent impossibility results for\\(k \\ge 3\\), we focus on\\(\\gamma \\)-(perturbation) stableinstances, where the optimal agent clustering is not affected by moving any subset of consecutive agent locations closer to each other by a factor at most\\(\\gamma \\ge 1\\). We show that the optimal solution is strategyproof in\\((2+\\sqrt{3})\\)-stable instances, if it does not include any singleton clusters, and that allocating the facility to the agent next to the rightmost one in each optimal cluster is strategyproof and\\((n-2)/2\\)-approximate for 5-stable instances (even if singleton clusters are present), wherenis the number of agents. On the negative side, we show that for any\\(k \\ge 3\\)and any\\(\\delta > 0\\), deterministic anonymous strategyproof mechanisms suffer an unbounded approximation ratio in\\((\\sqrt{2}-\\delta )\\)-stable instances. Moreover, we prove that allocating the facility to a random agent of each optimal cluster is strategyproof and 2-approximate in 5-stable instances.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_6"}, {"primary_key": "2249608", "vector": [], "sparse_vector": [], "title": "A Consumer-Theoretic Characterization of Fisher Market Equilibria.", "authors": ["Denizalp Goktas", "<PERSON>", "<PERSON>"], "summary": "In this paper, we bring consumer theory to bear in the analysis of Fisher markets whose buyers have arbitrary continuous, concave, homogeneous (CCH) utility functions representing locally non-satiated preferences. The main tools we use are the dual concepts of expenditure minimization and indirect utility maximization. First, we use expenditure functions to construct a new convex program whose dual, like the dual of the Eisenberg-Gale program, characterizes the equilibrium prices of CCH Fisher markets. We then prove that the subdifferential of the dual of our convex program is equal to the negative excess demand in the associated market, which makes generalized gradient descent equivalent to computing equilibrium prices via tâtonnement. Finally, we run a series of experiments which suggest that tâtonnement may converge at a rate of\\(O(\\nicefrac {(1+E)}{t^2})\\)in CCH Fisher markets that comprise buyers with elasticity of demand bounded byE. Our novel characterization of equilibrium prices may provide a path to proving the convergence of tâtonnement in Fisher markets beyond those in which buyers utilities exhibit constant elasticity of substitution.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_19"}, {"primary_key": "2249612", "vector": [], "sparse_vector": [], "title": "Envy-free Division of Multi-layered Cakes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the problem of dividing a multi-layered cake among heterogeneous agents under non-overlapping constraints. This problem, recently proposed by <PERSON><PERSON><PERSON> et al. (2020), captures several natural scenarios such as the allocation of multiple facilities over time where each agent can utilize at most one facility simultaneously, and the allocation of tasks over time where each agent can perform at most one task simultaneously. We establish the existence of an envy-free multi-division that is both non-overlapping and contiguous within each layered cake when the numbernof agents is a prime power and the numbermof layers is at mostn, thus providing a positive partial answer to a recent open question. To achieve this, we employ a new approach based on a general fixed point theorem, originally proven by <PERSON><PERSON><PERSON><PERSON> (1996), and recently applied by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (2020) to the envy-free division problem of a cake. We further show that for a two-layered cake division among three agents with monotone preferences, an\\(\\varepsilon \\)-approximate envy-free solution that is both non-overlapping and contiguous can be computed in logarithmic time of\\(1/{\\varepsilon }\\).", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_28"}, {"primary_key": "2249614", "vector": [], "sparse_vector": [], "title": "Improved Analysis of RANKING for Online Vertex-Weighted Bipartite Matching in the Random Order Model.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we consider the online vertex-weighted bipartite matching problem in the random arrival model. We consider the generalization of the RANKING algorithm for this problem introduced by <PERSON>, <PERSON>, <PERSON>, and <PERSON> [9], who show that their algorithm has a competitive ratio of 0.6534. We show that assumptions in their analysis can be weakened, allowing us to replace their derivation of a crucial functiongon the unit square with a linear program that computes the values of a best possiblegunder these assumptions on a discretized unit square. We show that the discretization does not incur much error, and show computationally that we can obtain a competitive ratio of 0.6629. To compute the bound over our discretized unit square we use parallelization, and still needed two days of computing on a 64-core machine. Furthermore, by modifying our linear program somewhat, we can show computationally an upper bound on our approach of 0.6688; any further progress beyond this bound will require either further weakening in the assumptions ofgor a stronger analysis than that of <PERSON> et al.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_12"}, {"primary_key": "2249616", "vector": [], "sparse_vector": [], "title": "Contract Design for Afforestation Programs.", "authors": ["Wanyi Dai Li", "<PERSON>", "<PERSON>"], "summary": "Trees on farms provide environmental benefits to society and improve agricultural productivity for farmers. We study incentive schemes for afforestation on farms through the lens of contract theory, designing conditional cash transfer schemes that encourage farmers to sustain tree growth. We capture the tree growth process as a Markov chain whose evolution is affected by the agent’s (farmer’s) choice of costly effort. The principal has imperfect information about the agent’s costs and chosen effort, and wants to find the minimal payments that maximize long-run tree survival. We derive the form of optimal contract structure and show how to calculate optimal payments in polynomial time. Notably, even when costs are time-invariant, the optimal contract can involve time-varying payments that are typically higher in earlier periods and may end early. We surveyed farmers partnered with an afforestation program in Uganda to collect data on tree maintenance costs and we derive the optimal payment contract for the reported costs.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_7"}, {"primary_key": "2249618", "vector": [], "sparse_vector": [], "title": "Towards a Characterization of Worst Case Equilibria in the Discriminatory Price Auction.", "authors": ["<PERSON><PERSON><PERSON>", "Alk<PERSON>", "<PERSON><PERSON>"], "summary": "We study the performance of the discriminatory price auction under the uniform bidding interface, which is one of the popular formats for running multi-unit auctions in practice. We undertake an equilibrium analysis with the goal of characterizing the inefficient mixed equilibria that may arise in such auctions. We consider bidders with capped-additive valuations, which is in line with the bidding format, and we first establish a series of properties that help us understand the sources of inefficiency. Moving on, we then use these results to derive new lower and upper bounds on the Price of Anarchy of mixed equilibria. For the case of two bidders, we arrive at a complete characterization of inefficient equilibria and show an upper bound of 1.1095, which is also tight. For multiple bidders, we show that the Price of Anarchy is strictly worse, improving the best known lower bound for submodular valuations. We further present an improved upper bound of 4/3 for the special case where there exists a “high” demand bidder. Finally, we also study Bayes-Nash equilibria, and exhibit a separation result that had been elusive so far. Namely, already with two bidders, the Price of Anarchy for Bayes-Nash equilibria is strictly worse than that for mixed equilibria. Such separation results are not always true (e.g., the opposite is known for simultaneous second price auctions) and reveal that the Bayesian model here introduces further inefficiency.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_11"}, {"primary_key": "2249619", "vector": [], "sparse_vector": [], "title": "Mechanisms for Trading Durable Goods.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider trading indivisible and easily transferabledurable goods, which are goods that an agent can receive, use, and trade again for a different good. This is often the case with books that can be read and later exchanged for unread ones. Other examples of such easily transferable durable goods include puzzles, video games and baby clothes. We introduce a model for the exchange of easily transferable durable goods. In our model, each agent owns a set of items and demands a different set of items. An agent is interested in receiving as many items as possible from his demand set. We consider mechanisms that exchange items in cycles in which each participating agent receives an item that he demands and gives an item that he owns. We aim to develop mechanisms that have the following properties: they areefficient, in the sense that they maximize the total number of items that agents receive from their demand set, they arestrategyproof(i.e., it is in the agents’ best interest to report their preferences truthfully) and they run inpolynomial time. One challenge in developing mechanisms for our setting is that the supply and demand sets of the agents are updated after a trade cycle is executed. This makes constructing strategyproof mechanisms in our model significantly different from previous works, both technically and conceptually and requires developing new tools and techniques. We prove that simultaneously satisfying all desired properties is impossible and thus focus on studying the tradeoffs between these properties. To this end, we provide both approximation algorithms and impossibility results.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_15"}, {"primary_key": "2249620", "vector": [], "sparse_vector": [], "title": "Planning on an Empty Stomach: On Agents with Projection Bias.", "authors": ["<PERSON><PERSON>", "Nadav Sklar"], "summary": "People often believe that their future preferences will be similar to their current ones. For example, people who go hungry to the supermarket, often buy less healthy food items than when they go on a full stomach. <PERSON><PERSON><PERSON> et al. [10] coined the termprojection biasto capture this and similar behaviors. Our first contribution is a generalization of the restricted model of <PERSON><PERSON> et al. by considering agents with projection bias that traverse a state graph for time horizont. Our generalization allows us to capture more complex planning scenarios, such as a student that plans his occupational path. We analyze the planning behavior of biased agents and show that their loss due to their projection bias may be unbounded. Obviously, agents who do not suffer from projection bias at all will be able to traverse the graph optimally. We show–perhaps surprisingly–that agents that exhibit a strong projection bias sometimes fare better than agents that exhibit projection bias to a smaller extent. Similarly, we show that agents that plan for a longer time horizon do not necessarily fare better than agents that plan for a shorter time horizon. We then provide bounds on the number of these “non-monotonicity” points in a given state graph. Among other results, we prove a hardness result for computing a subgraph that maximizes the utility of the biased agent.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_23"}, {"primary_key": "2249621", "vector": [], "sparse_vector": [], "title": "The Platform Design Problem.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "On-line firms deploy suites of software platforms, where each platform is designed to interact with users during a certain activity, such as browsing, chatting, socializing, emailing, driving, etc. The economic and incentive structure of this exchange, as well as its algorithmic nature, have not been explored to our knowledge. We model this interaction as a Stackelberg game between a Designer and one or more Agents. We model an Agent as a Markov chain whose states are activities; we assume that the Agent’s utility is a linear function of the steady-state distribution of this chain. The Designer may design a platform for each of these activities/states; if a platform is adopted by the Agent, the transition probabilities of the Markov chain are affected, and so is the objective of the Agent. The Designer’s utility is a linear function of the steady state probabilities of the accessible states, minus the platform development costs. The underlying optimization problem of the Agent—how to choose the states for which to adopt the platform—is an MDP. If this MDP has a simple yet plausible structure (the transition probabilities from one state to another only depend on the target state and the recurrent probability of the current state) the Agent’s problem can be solved by a greedy algorithm. The Designer’s optimization problem (designing a custom suite for the Agent so as to optimize, through the Agent’s optimum reaction, the Designer’s revenue), is in general NP-hard to approximate within any finite ratio; however, in the special case, while still NP-hard, has an FPTAS. These results generalize, under mild additional assumptions, from a single Agent to a distribution of Agents with finite support, as well as to the setting where other Designers have already created platforms. We discuss directions of future research.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_18"}, {"primary_key": "2249622", "vector": [], "sparse_vector": [], "title": "Default Ambiguity: Finding the Best Solution to the Clearing Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study financial networks with debt contracts and credit default swaps between specific pairs of banks. Given such a financial system, we want to decide which of the banks are in default, and how much of their liabilities can these defaulting banks pay. There can easily be multiple different solutions to this problem, leading to a situation ofdefault ambiguity, and a range of possible solutions to implement for a financial authority. In this paper, we study the properties of the solution space of such financial systems, and analyze a wide range of reasonable objective functions for selecting from the set of solutions. Examples of such objective functions include minimizing the number of defaulting banks, minimizing the amount of unpaid debt, maximizing the number of satisfied banks, and many others. We show that for all of these objectives, it is NP-hard to approximate the optimal solution to an\\(n^{1-\\epsilon }\\)factor for any\\(\\epsilon >0\\), withndenoting the number of banks. Furthermore, we show that this situation is rather difficult to avoid from a financial regulator’s perspective: the same hardness results also hold if we apply strong restrictions on the weights of the debts, the structure of the network, or the amount of funds that banks must possess. However, if we restrict both the network structure and the amount of funds simultaneously, then the solution becomes unique, and it can be found efficiently.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_22"}, {"primary_key": "2249623", "vector": [], "sparse_vector": [], "title": "The Core of Housing Markets from an Agent&apos;s Perspective: Is It Worth Sprucing Up Your Home?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study housing markets as introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [39]. We investigate the computational complexity of various questions regarding the situation of an agentain a housing marketH: we show that it is\\(\\mathsf {NP}\\)-hard to find an allocation in the core ofHwhere (i)areceives a certain house, (ii)adoes not receive a certain house, or (iii)areceives a house other than her own. We prove that the core of housing marketsrespects improvementin the following sense: given an allocation in the core ofHwhere agentareceives a househ, if the value of the house owned byain<PERSON><PERSON>, then the resulting housing market admits an allocation whereareceives eitherh, or a house that she prefers toh; moreover, such an allocation can be found efficiently. We further show an analogous result in theStable Roommatessetting by proving that stable matchings in a one-sided market also respect improvement.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_14"}, {"primary_key": "2249624", "vector": [], "sparse_vector": [], "title": "On the Benefits of Being Constrained When Receiving Signals.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study a Bayesian persuasion setting in which the receiver is trying to match the (binary) state of the world. The sender’s utility is partially aligned with the receiver’s, in that conditioned on the receiver’s action, the sender derives higher utility when the state of the world matches the action. Our focus is on whether in such a setting, being constrained helps a receiver. Intuitively, if the receiver can only take the sender’s preferred action with smaller probability, the sender might have to reveal more information, so that the receiver can take the action more specifically when the sender prefers it. We show that with a binary state of the world, this intuition indeed carries through: under very mild non-degeneracy conditions, a more constrained receiver will always obtain (weakly) higher utility than a less constrained one. Unfortunately, without additional assumptions, the result does not hold when there are more than two states in the world, which we show with an explicit example.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_10"}, {"primary_key": "2249625", "vector": [], "sparse_vector": [], "title": "Bayesian Persuasion in Sequential Trials.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider a Bayesian persuasion problem where the sender tries to persuade the receiver to take a particular action via a sequence of signals. This we model by considering multi-phase trials with different experiments conducted based on the outcomes of prior experiments. In contrast to most of the literature, we consider the problem with constraints on signals imposed on the sender. This we achieve by fixing some of the experiments in an exogenous manner; these are called determined experiments. This modeling helps us understand real-world situations where this occurs: e.g., multi-phase drug trials where the FDA determines some of the experiments, start-up acquisition by big firms where late-stage assessments are determined by the potential acquirer, multi-round job interviews where the candidates signal initially by presenting their qualifications but the rest of the screening procedures are determined by the interviewer. The non-determined experiments (signals) in the multi-phase trial are to be chosen by the sender in order to persuade the receiver best. With a binary state of the world, we start by deriving the optimal signaling policy in the only non-trivial configuration of a two-phase trial with binary-outcome experiments. We then generalize to multi-phase trials with binary-outcome experiments where the determined experiments can be placed at arbitrary nodes in the trial tree. Here we present a dynamic programming algorithm to derive the optimal signaling policy that uses the two-phase trial solution’s structural insights. We also contrast the optimal signaling policy structure with classical Bayesian persuasion strategies to highlight the impact of the signaling constraints on the sender.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_2"}, {"primary_key": "2249626", "vector": [], "sparse_vector": [], "title": "Eliciting Social Knowledge for Creditworthiness Assessment.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "Access to capital is a major constraint for economic growth in the developing world. Yet lenders often face high default rates due to their inability to distinguish creditworthy borrowers from the rest. In this paper, we propose two novel scoring mechanisms that incentivize community members to truthfully report their signal on the creditworthiness of others in their community. We first design a truncated asymmetric scoring rule for a setting where the lender has no liquidity constraints. We then derive a novel, strictly-proper Vickrey-Clarke-Groves (VCG) scoring mechanism for the liquidity-constrained setting. Whereas <PERSON> et al. [7] give an impossibility result for an analogous setting in which sequential reports are made in the context of decision markets, we achieve a positive result through appeal to interim uncertainty about the reports of others. Additionally, linear belief aggregation methods integrate nicely with the VCG scoring mechanism that we develop.", "published": "2021-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-94676-0_24"}]