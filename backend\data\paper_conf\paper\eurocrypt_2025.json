[{"primary_key": "161275", "vector": [], "sparse_vector": [], "title": "On the Adaptive Security of Free-XOR-Based Garbling Schemes in the Plain Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A Garbling Scheme is a fundamental cryptographic primitive, with numerous theoretical and practical applications. Since its inception by <PERSON> (FOCS’82, ’86), optimizing the communication and computation complexities of securely garbling circuits has been an area of active research. One such optimization, and perhaps the most fundamental, is the ‘Free-XOR’ technique (<PERSON><PERSON><PERSON> and <PERSON>, ICALP’08) which allows XOR gates in a function garbling to not require representation, and therefore communication. Since then, several works have designed and analysed the security of schemes that adopt the Free-XOR optimisation. In particular: (1) <PERSON><PERSON> (JoC’16) proved that this can be securely instantiated assuming symmetric-key encryption satisfying a notion called RK-KDM security; and (2) <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON> (Eurocrypt’15) proposed the so-called ‘Half Gates’ scheme, and proved that it can be instantiated assuming hash functions satisfying a notion called CCR security. Although both schemes have been proven selectively secure, prior work leaves it open to analyze whether they satisfy a stronger security notion – adaptive security – in the plain model. In this work, we formally show that the selective security of these two schemescannotbe lifted to adaptive security under the same assumptions. To establish these barriers, we adopt techniques from the work of <PERSON><PERSON><PERSON> et al. (Crypto’21), who proved similar negative results for <PERSON>’s garbling. We use that as a starting point and introduce new techniques tailored towards addressing Free-XOR-based schemes.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_8"}, {"primary_key": "161276", "vector": [], "sparse_vector": [], "title": "Low-Bandwidth Mixed Arithmetic in VOLE-Based ZK from Low-Degree PRGs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Vector oblivious linear evaluation, or VOLE, has recently been shown to be a useful tool for designing efficient zero-knowledge proof systems that can scale to large statements with a low memory footprint (<PERSON> et al. CCS 2021, <PERSON><PERSON> et al. CRYPTO 2021). While most ZK protocols require statements to be expressed in terms of arithmetic operations over a single finite field, recent works in VOLE-based ZK have shown how to mix Boolean and arithmetic operations in a single statement, through conversions between different finite fields (<PERSON><PERSON> et al. CCS 2021, <PERSON><PERSON> et al. USENIX 2021). We present new, lightweight protocols for arithmetic/Boolean conversions in VOLE-based ZK. In contrast to previous works, which rely on an expensive cut-and-choose method, we take a new approach that leverages the ability of recent proof systems to prove higher-degree polynomial constraints, and combines this with specialized low-degree pseudorandom generators. This not only simplifies conversions, but we showcase how it also improves the concrete efficiency of tasks important in practical ZK protocols of complex statements, including fixed point arithmetic, comparison and range proofs.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_14"}, {"primary_key": "161277", "vector": [], "sparse_vector": [], "title": "Hollow LWE: A New Spin - Unbounded Updatable Encryption from LWE and PCE.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Updatable public-key encryption (UPKE) allows anyone to update a public key while simultaneously producing an update token, given which the secret key holder could consistently update the secret key. Furthermore, ciphertexts encrypted under the old public key remain secure even if the updated secret key is leaked – a property much desired in secure messaging. All existing lattice-based constructions of UPKE update keys by a noisy linear shift. As the noise accumulates, these schemes either require super-polynomial-size moduli or an a priori bounded number of updates to maintain decryption correctness. Inspired by recent works on cryptography based on the lattice isomorphism problem, we propose an alternative way to update keys in lattice-based UPKE. Instead of shifting, we rotate them. As rotations do not induce norm growth, our construction supports an unbounded number of updates with a polynomial-size modulus. The security of our scheme is based on the LWE assumption over hollow matrices – matrices which generate linear codes with non-trivial hull – and the hardness of permutation code equivalence. Along the way, we also show that LWE over hollow matrices is as hard as LWE over uniform matrices, and that a leftover hash lemma holds for hollow matrices.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_13"}, {"primary_key": "161278", "vector": [], "sparse_vector": [], "title": "Formal Analysis of Multi-device Group Messaging in WhatsApp.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "WhatsApp provides end-to-end encrypted messaging to over two billion users. However, due to a lack of public documentation and source code, the specific security guarantees it provides are unclear. Seeking to rectify this situation, we combine the limited public documentation with information we gather throughreverse-engineeringits implementation to provide a formal description of the subset of WhatsApp that providesmulti-device group messaging. We utilise this description to state and prove the security guarantees that this subset of WhatsApp provides. Our analysis is performed within a variant of the Device-Oriented Group Messaging model, which we extend to supportdevice revocation. We discuss how to interpret these results, including the security WhatsApp provides as well as its limitations.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_9"}, {"primary_key": "161279", "vector": [], "sparse_vector": [], "title": "Analysis of the Telegram Key Exchange.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We describe, formally model, and prove the security of Telegram’s key exchange protocols for client-server communications. To achieve this, we develop a suitable multi-stage key exchange security model along with pseudocode descriptions of the Telegram protocols that are based on analysis of Telegram’s specifications and client source code. We carefully document how our descriptions differ from reality and justify our modelling choices. Our security proofs reduce the security of the protocols to that of their cryptographic building blocks, but the subsequent analysis of those building blocks requires the introduction of a number of novel security assumptions, reflecting many design decisions made by Telegram that are suboptimal from the perspective of formal analysis. Along the way, we provide a proof of IND-CCA security for the variant of RSA-OEAP+ used in Telegram and identify a hypothetical attack exploiting current Telegram server behaviour (which is not captured in our protocol descriptions). Finally, we reflect on the broader lessons about protocol design that can be taken from our work.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_8"}, {"primary_key": "161280", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of Rank-2 Module-LIP: A Single Real Embedding Is All It Takes.", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON> P<PERSON>"], "summary": "The rank-2 module-LIP problem was introduced in cryptography by (<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Asiacrypt 2022), to construct the highly performantHAWKscheme. A first cryptanalytic work by (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Eurocrypt 2024) showed a heuristic polynomial time attack against the rank-2 module-LIP problem overtotally realnumber fields. While mathematically interesting, this attack focuses on number fields that are not relevant for cryptography. The main families of fields used in cryptography are the highly predominant cyclotomic fields (used for instance in theHAWKscheme), as well as the NTRU Prime fields, used for instance in the eponymous NTRU Prime scheme (<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, SAC 2017). In this work, we generalize the attack of <PERSON><PERSON><PERSON> et al. against rank-2 module-LIP to the family of all number fields with at least one real embedding, which contains the NTRU Prime fields. We present three variants of our attack, firstly a heuristic one that runs in quantum polynomial time. Secondly, under the extra assumption that the defining polynomial ofKhas a 2-transitive Galois group (which is the case for the NTRU Prime fields), we give a provable attack that runs in quantum polynomial time. And thirdly, with the same 2-transitivity assumption we give a heuristic attack that runs in classical polynomial time. For the latter we use a generalization of the Gen<PERSON>–<PERSON> algorithm to any number field which might be of independent interest.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_7"}, {"primary_key": "161281", "vector": [], "sparse_vector": [], "title": "Fine-Grained Complexity in a World Without Cryptography.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The study of fine-grained cryptography has proliferated in recent years due to its allure of potentially relying on weaker assumptions compared to standard cryptography. As fine-grained cryptography only requires polynomial gaps between the adversary and honest parties, it seems plausible to build primitives relying upon popular hardness assumptions about problems in\\(\\textbf{P}\\)such as\\(k\\text {-}\\textsf{SUM}\\)or\\(\\textsf{Zero}\\text {-}k\\text {-}\\textsf{Clique}\\). The ultimate hope is that fine-grained cryptography could still be viable even if all current cryptographic assumptions are false, such as if\\(\\textbf{P} = \\textbf{NP}\\)or if we live in Pessiland where one-way functions do not exist. In our work, we consider whether this approach is viable by studying fine-grained complexity when all standard cryptographic assumptions are false. As our main result, we show that many popular fine-grained complexity problems are easy to solve in the average-case when one-way functions do not exist. In other words, many candidate hardness assumptions for building fine-grained cryptography are no longer options in Pessiland. As an example, we prove that the average-case\\(k\\text {-}\\textsf{SUM}\\)and\\(\\textsf{Zero}\\text {-}k\\text {-}\\textsf{Clique}\\)conjectures are false for sufficiently large constantkwhen no one-way functions exist. The average-case\\(\\textsf{Zero}\\text {-}k\\text {-}\\textsf{Clique}\\)assumption was used to build fine-grained key-exchange by Lavigneet al.[CRYPTO’19]. One can also view the contrapositive of our result as providing an explicit construction of one-way functions assuming\\(n^{\\omega _k(1)}\\)average-case hardness of\\(k\\text {-}\\textsf{SUM}\\)or\\(\\textsf{Zero}\\text {-}k\\text {-}\\textsf{Clique}\\)for all constantk. We also show that barriers for reductions in fine-grained complexity may be explained by problems in cryptography. First, we show that finding faster algorithms for computing discrete logarithms is equivalent to designing average-case equivalence between\\(k\\text {-}\\textsf{SUM}\\)and\\(k\\text {-}\\textsf{CYC}\\)(an extension of\\(k\\text {-}\\textsf{SUM}\\)to cyclic groups). In particular, finding such a reduction from\\(k\\text {-}\\textsf{CYC}\\)to\\(k\\text {-}\\textsf{SUM}\\)could potentially lead to breakthrough algorithms for the discrete logarithm, factoring, RSA and quadratic residuosity problems. Finally, we show that discrete logarithms with preprocessing may be reduced to the\\(k\\text {-}\\textsf{CYC}\\mathsf {\\text {-}Index}\\)problem, and we present faster algorithms for average-case\\(k\\text {-}\\textsf{SUM}\\mathsf {\\text {-}Index}\\)and\\(k\\text {-}\\textsf{CYC}\\mathsf {\\text {-}Index}\\).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_14"}, {"primary_key": "161282", "vector": [], "sparse_vector": [], "title": "Pseudorandomness in the (Inverseless) Haar Random Oracle Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the (in)feasibility of quantum pseudorandom notions in a quantum analog of the random oracle model, where all the parties, including the adversary, have oracle access to the same Haar random unitary. In this model, we show the following: (Unbounded-query secure) pseudorandom unitaries (PRU) exist. Moreover, the PRU construction makes two calls to the Haar oracle. We consider constructions of PRUs making a single call to the Haar oracle. In this setting, we show that unbounded-query security is impossible to achieve. We complement this result by showing that bounded-query secure PRUs do exist with a single query to the Haar oracle. We show that multi-copy pseudorandom state generators and function-like state generators (with classical query access), making a single call to the Haar oracle, exist. Our results have the following consequence: for the first time, we show that the key length in pseudorandom unitaries can be generically shrunk (relative to the output length). Our results are also some of the first usecases of the new “path recording” formalism for Haar random unitaries, introduced in the recent breakthrough work of <PERSON> and <PERSON>.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_6"}, {"primary_key": "161283", "vector": [], "sparse_vector": [], "title": "Instance Compression, Revisited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "E<PERSON>"], "summary": "Collision-resistant hashing (CRH) is a cornerstone of cryptographic protocols. However, despite decades of research, no construction of a CRH based solely on one-way functions has been found. Moreover, there are black-box limitations that separate these two primitives. Harnik and Naor [HN10] overcame this black-box barrier by introducing the notion ofinstance compression. Instance compression reduces large\\(\\textrm{NP}\\)instances to a size that depends on their witness size while preserving the “correctness” of the instance relative to the language. Shortly thereafter, Fortnow and Santhanam showed that efficient instance compression algorithms are unlikely to exist (as the polynomial hierarchy would collapse). <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> defined a computational analog of instance compression, which they calledcomputational instance compression(CIC), and gave a construction of CIC under standard assumptions. Unfortunately, this notion is not strong enough to replace instance compression in <PERSON><PERSON><PERSON> and <PERSON><PERSON>’s CRH construction. In this work, we revisit the notion of computational instance compression and ask what the “correct” notion for CIC is, in the sense that it is sufficiently strong to achieve useful cryptographic primitives while remaining consistent with common assumptions. First, we give a natural strengthening of the CIC definition that serves as a direct substitute for the instance compression scheme in the <PERSON>rn<PERSON>–<PERSON><PERSON> construction. However, we show that even this notion is unlikely to exist. We then identify a notion of CIC that gives new hope for constructing CRH from one-way functions via instance compression. We observe that this notion is achievable under standard assumptions and, by revisiting the Harnik–<PERSON> proof, demonstrate that it is sufficiently strong to achieve CRH. In fact, we show that our CIC notion is existentiallyequivalentto CRH. Beyond Minicrypt, Harnik and Naor showed that a strengthening of instance compression can be used to construct OT and public-key encryption. We rule out the computational analog of this stronger notion by showing that it contradicts the existence ofincompressible public-key encryption, which was recently constructed under standard assumptions.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_9"}, {"primary_key": "161284", "vector": [], "sparse_vector": [], "title": "WHIR: Reed-Solomon Proximity Testing with Super-Fast Verification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "E<PERSON>"], "summary": "We introduce WHIR, a new IOP of proximity that offers small query complexity and exceptionally fast verification time. The WHIR verifier typically runs in a few hundredmicroseconds, whereas other verifiers in the literature require severalmilliseconds (if not much more). This significantly improves the state of the art in verifier time for hash-based SNARGs (and beyond). Crucially, WHIR is an IOP of proximity forconstrained Reed–Solomon codes, which can express a rich class of queries to multilinear polynomials and to univariate polynomials. In particular, WHIR serves as a direct replacement for protocols like FRI, STIR, BaseFold, and others. Leveraging the rich queries supported by WHIR and a new compiler for multilinear polynomial IOPs, we obtain a highly efficient SNARG for generalized R1CS. As a comparison point, our techniques also yield state-of-the-art constructions of hash-based (non-interactive) polynomial commitment schemes for both univariate and multivariate polynomials (since sumcheck queries naturally express polynomial evaluations). For example, if we use WHIR to construct a polynomial commitment scheme for degree\\(2^{22}\\), with 100 bits of security, then the time to commit and open is 1.2 s, the sender communicates 63 KiB to the receiver, and the opening verification time is\\(360\\,\\upmu \\)s.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_8"}, {"primary_key": "161285", "vector": [], "sparse_vector": [], "title": "Peeking Into the Future: MPC Resilient to Super-Rushing Adversaries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An important requirement in synchronous protocols is that, even when a party receives all its messages for a given round ahead of time, it must wait until the round officially concludes before sending its messages for the next round. In practice, however, implementations often overlook this waiting requirement. This leads to a mismatch between the security analysis and real-world deployments, giving adversaries a new, unaccounted-for capability: the ability to “peek into the future.” Specifically, an adversary can force certain honest parties to advance to round\\(r+1\\), observe their round\\(r+1\\)messages, and then use this information to determine its remaining roundrmessages. We refer to adversaries with this capability as “super-rushing” adversaries. We initiate a study of secure computation in the presence of super-rushing adversaries. We focus on understanding the conditions under which existing synchronous protocols remain secure in the presence of super-rushing adversaries. We show that not all protocols remain secure in this model, highlighting a critical gap between theoretical security guarantees and practical implementations. Even worse, we show that security against super-rushing adversaries is not necessarily maintained under sequential composition. Despite those limitations, we present a general positive result: secret-sharing based protocols in the perfect setting, such as BGW, or those that are based on multiplication triplets, remain secure against super-rushing adversaries. This general theorem effectively enhances the security of such protocols “for free.” It shows that these protocols do not require parties to wait for the end of a round, enabling potential optimizations and faster executions without compromising security. Moreover, it shows that there is no need to spend efforts to achieve perfect synchronization when establishing the communication networks for such protocols.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_14"}, {"primary_key": "161286", "vector": [], "sparse_vector": [], "title": "Glacius: <PERSON><PERSON><PERSON><PERSON> Signatures from DDH with Full Adaptive Security.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Threshold signatures are one of the most important cryptographic primitives in distributed systems. The threshold Schnorr signature scheme, an efficient and pairing-free scheme, is a popular choice and is included in NIST’s standards and recent call for threshold cryptography. Despite its importance, most threshold Schnorr signature schemes assume a static adversary in their security proof. A recent scheme proposed by <PERSON><PERSON><PERSON> et al. (Crypto 2024) addresses this issue. However, it requires linear-sized signing keys and lacks the identifiable abort property, which makes it vulnerable to denial-of-service attacks. Other schemes with adaptive security either have reduced corruption thresholds or rely on non-standard assumptions such as the algebraic group model (AGM) or hardness of the algebraic one-more discrete logarithm (AOMDL) problem. In this work, we present Glacius, the first threshold Schnorr signature scheme that overcomes all these issues. Glacius is adaptively secure based on the hardness of decisional Di<PERSON>ie-<PERSON>man (DDH) in the random oracle model (ROM), and it supports a full corruption threshold\\(t<n\\), wherenis the total number of signers andtis the signing threshold. Additionally, Glacius provides constant-sized signing keys and identifiable abort, meaning signers can detect misbehavior. We also give a formal game-based definition of identifiable abort, addressing certain subtle issues present in existing definitions, which may be of independent interest. Full version.For the full version of this paper, we refer to [5].", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_11"}, {"primary_key": "161287", "vector": [], "sparse_vector": [], "title": "Key Derivation Functions Without a Grain of Salt.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Key derivation functions (KDFs) are integral to many cryptographic protocols. Their functionality is to turn raw key material, such as a <PERSON><PERSON><PERSON><PERSON> secret, into a strong cryptographic key that is indistinguishable from random. This guarantee was formalized by <PERSON><PERSON><PERSON><PERSON> together with the seminal introduction of HKDF (CRYPTO 2010), in a model where the KDF only takes a single key material input. Modern protocol designs, however, regularly need to combine multiple secrets, possibly even from different sources, with the guarantee that the derived key is secure as long as at least one of the inputs is good. This is particularly relevant in settings like hybrid key exchange for quantum-safe migration. <PERSON><PERSON><PERSON><PERSON>’s KDF formalism does not capture this goal, and there has been surprisingly little work on the security considerations for KDFs since then. In this work, we thus revisit the syntax and security model for KDFs to treat multiple, possibly correlated inputs. Our syntax is assertive: We do away with salts, which are needed in theory to extract from arbitrary sources in the standard model, but in practice, they are almost never used (or even available) and sometimes even misused, as we argue. We use our new model to analyze real-world multi-input KDFs—in Signal’s X3DH protocol, ETSI’s TS 103-744 standard, and MLS’ combiner for pre-shared keys—as well as new constructions we introduce for specialized settings—e.g., a purely blockcipher-based one. We further discuss the importance of collision resistance for KDFs and finally apply our multi-input KDF model to show how hybrid KEM key exchange can be analyzed from a KDF perspective.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_14"}, {"primary_key": "161288", "vector": [], "sparse_vector": [], "title": "(Inefficient Prover) ZAPs from Hard-to-Invert Functions.", "authors": ["<PERSON>", "<PERSON>-<PERSON>ed"], "summary": "A ZAP is a witness-indistinguishable two-message public-coin interactive proof with the following simple structure: the verifier sends a uniformly random string, the prover responds, and the verifier decides in polynomial time whether to accept or reject. We show that one-way functions imply the existence of ZAPs for NP where the prover runs in time\\(2^{n^\\epsilon }\\)for arbitrarily small constant\\(\\epsilon >0\\)(wherendenotes the length on the NP instance). Moreover, it suffices to simply assume there exist functions that are hard to invert, but valid image/preimage pairs can be efficiently recognized. Such functions need not be efficiently computable and hence are not known to imply even one-way functions. Prior to this work such ZAPs were only known from one-way permutations [<PERSON> et al., CRYPTO’20]. <PERSON><PERSON><PERSON> et al. [STOC’23] recently showed a separation of\\(\\textsf{P}\\)and\\(\\textsf{NP}\\cap \\textsf{coNP}\\)assuming\\(\\textsf{UP}\\not \\subseteq \\textsf{RP}\\)in the random oracle model. As an application of our main result, we show that one-way functions (in addition to other complexity theoretic assumptions) imply a non-uniform variant of this separationin the plain model.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_10"}, {"primary_key": "161289", "vector": [], "sparse_vector": [], "title": "POKÉ: A Compact and Efficient PKE from Higher-Dimensional Isogenies.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce a new PKE protocol,POKÉ, based on isogenies of unknown degree. The protocol relies on two new techniques: the first constructs an SIDH square while also working with higher-dimensional representations, whereas the second allows us to obtain a shared secret even when all curves in the commutative diagram are known. The resulting protocol is compact and extremely efficient. We provide a proof-of-concept implementation in SageMath ofPOKÉthat shows encryption and decryption taking about a hundred milliseconds at security level I:POKÉis thus the most efficient encryption protocol from isogenies, and it outperforms existing protocols by more than an order of magnitude.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_4"}, {"primary_key": "161290", "vector": [], "sparse_vector": [], "title": "A New World in the Depths of Microcrypt: Separating OWSGs and Quantum Money from QEFID.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While in classical cryptography one-way functions (OWFs) are widely regarded as the “minimal assumption”, the situation in quantum cryptography is less clear. Recent works have put forward two concurrent candidates for the minimal assumption in quantum cryptography: One-way state generators (OWSGs), postulating the existence of a hardsearchproblem with an efficient verification algorithm, and EFI pairs, postulating the existence of a harddistinguishingproblem. Two recent papers [<PERSON><PERSON><PERSON> and Tomer STOC’24; <PERSON><PERSON> and Jain FOCS’24] showed that OWSGs imply EFI pairs, but the reverse direction remained open. In this work, we give strong evidence that the opposite direction does not hold: We show that there is a quantum unitary oracle relative to which EFI pairs exist but OWSGs do not. In fact, we show a slightly stronger statement that holds also for EFI pairs that output classical bits (QEFID pairs). As a consequence, we separate, via our oracle, QEFID pairs and one-way puzzles from OWSGs and several other Microcrypt primitives, including efficiently verifiable one-way puzzles and unclonable state generators. In particular, this solves a problem left open in [<PERSON>, <PERSON>, and Gray Crypto’24]. Using similar techniques, we also establish a fully black-box separation (which is slightly weaker than an oracle separation) between private-key quantum money schemes and QEFID pairs. One conceptual implication of our work is that the existence of an efficient verification algorithm may lead to qualitatively stronger primitives in quantum cryptography.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_2"}, {"primary_key": "161291", "vector": [], "sparse_vector": [], "title": "INDIANA - Verifying (Random) Probing Security Through Indistinguishability Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While masking is a widely used defense against passive side-channel attacks, its secure implementation in hardware continues to be a manual, complex, and error-prone process. This paper introducesINDIANA, a comprehensive security verification methodology for hardware masking. Our results include a hardware verification tool, enabling a complete analysis of simulation-based security in the glitch-extended probing model and intra-cycle estimations for leakage probabilities in the random probing model. Notably,INDIANAis the first framework to analyze arbitrary masked circuits in both models, even at the scale of full SPN cipher rounds (e.g., AES), while delivering exact verification results. To achieve accurate and comprehensive verification, we propose a partitionable probing distinguisher that allows for fast validation of probe tuples, surpassing current methods that rely on statistical independence. Furthermore, our approach naturally supports extensions to the random probing model by utilizing Fast Fourier-Hadamard Transformations (FHTs). Benchmark results show thatINDIANAcompetes effectively with leading probing model verification tools, such asironMask,maskVerif, andVERICA.INDIANAis also the first tool that is capable to provide intra-cycle estimations of random probing leakage probabilities for large-scale masked circuits.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_2"}, {"primary_key": "161292", "vector": [], "sparse_vector": [], "title": "New Techniques for Random Probing Security and Application to Raccoon Signature Scheme.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The random probing model formalizes a leakage scenario where each wire in a circuit leaks with probabilityp. This model holds practical relevance due to its reduction to the noisy leakage model, which is widely regarded as the appropriate formalization for power and electromagnetic side-channel attacks. In this paper, we present new techniques for designing efficient masking schemes that achieve tighter random probing security with lower complexity. First, we introduce the notion ofcardinal random probing composability(Cardinal-RPC), offering a new trade-off between complexity and security for composing masking gadgets. Next, we propose a novel refresh technique based on a simple iterative process: randomly selecting and updating two shares with fresh randomness. While not perfectly secure in the standard probing model, this method achieves arbitrary cardinal-RPC security, making it a versatile tool for constructing random-probing secure circuits. Using this refresh, we develop additional basic gadgets (e.g., linear multiplication, addition, and copy) that satisfy the cardinal-RPC notion. Despite the increased complexity, the gains in security significantly outweigh the overhead, with the number of iterations offering useful flexibility. To showcase our techniques, we apply them to lattice-based signatures. Specifically, we introduce a new random-probing composable gadget for sampling small noise, a key component in various post-quantum algorithms. To assess security in this context, we generalize the random probing security model to address auxiliary inputs and public outputs. We apply our findings to <PERSON><PERSON><PERSON>, a masking-friendly signature scheme originally designed for standard probing security. We prove the secure composition of our new gadgets for key generation and signature computation, and show that our masking scheme achieves a superior security-performance tradeoff compared to previous approaches based on random probing expansion. To our knowledge, this is the first fully secure instantiation of a post-quantum algorithm in the random probing model.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_4"}, {"primary_key": "161293", "vector": [], "sparse_vector": [], "title": "ChiLow and ChiChi: New Constructions for Code Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Shi<PERSON><PERSON> Ghos<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>wei Sun", "<PERSON>", "<PERSON>"], "summary": "We study the problem of embedded code encryption, i.e., encryption for binary software code for a secure microcontroller that is stored in an insecure external memory. As every single instruction must be decrypted before it can be executed, this scenario requires an extremely low latency decryption. We present a formal treatment of embedded code encryption security definitions, propose three constructions, namely ACE1, ACE2 and ACE3, and analyze their security. Further, we presentChiLow, a family of tweakable block ciphers and a related PRF specifically designed for embedded code encryption. At the core ofChiLow, there isChiChi, a new family of non-linear layers ofevendimension based on the well-known\\(\\chi \\)function. Our fully unrolled hardware implementation ofChiLow, using the Nangate 15nm Open Cell Library, achieves a decryption latency of less than 280 picoseconds.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_8"}, {"primary_key": "161294", "vector": [], "sparse_vector": [], "title": "Drifting Towards Better Error Probabilities in Fully Homomorphic Encryption Schemes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "There are two security notions for FHE schemes: the traditional notion of IND-CPA and a more stringent notion of IND-CPAD.  These notions are equivalent when FHE schemes are perfectly correct. However, for schemes with negligible failure probability, the FHE parameters required to achieve IND-CPADsecurity can be much larger than those needed to obtain IND-CPA security. This paper uses the notion ofciphertext driftin order to understand the practical difference between IND-CPA and IND-CPADsecurity in schemes such as FHEW, TFHE, and FINAL. This notion allows us to define a modulus switching operation (the main culprit for the difference in parameters) such that one does not require adapting IND-CPA cryptographic parameters to meet the IND-CPADsecurity level. Further, the extra cost incurred by the new techniques has no noticeable performance impact in practical applications. The paper also formally defines a stronger version for IND-CPADsecurity called sIND-CPAD, which is proved to be strictly separated from the IND-CPADnotion. Criterion for turning an IND-CPADsecure public-key encryption scheme into an sIND-CPADone is also provided.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_7"}, {"primary_key": "161295", "vector": [], "sparse_vector": [], "title": "Improved Cryptanalysis of SNOVA.", "authors": ["<PERSON>"], "summary": "SNOVA is a multivariate signature scheme submitted to the NIST project for additional signature schemes by <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. With small key and signature sizes and good performance, SNOVA is one of the more efficient schemes in the competition, which makes SNOVA an important target for cryptanalysis. In this paper, we observe that SNOVA implicitly uses a structured version of the “whipping” technique developed for the MAYO signature scheme. We show that the extra structure makes the construction vulnerable to new forgery attacks. Concretely, we formulate new attacks that reduce the security margin of the proposed SNOVA parameter sets by a factor between\\(2^{8}\\)and\\(2^{39}\\). Furthermore, we show that large fractions of public keys are vulnerable to more efficient versions of our attack. For example, for SNOVA-37-17-2, a parameter set targeting NIST’s first security level, we show that roughly one out of every 500 public keys is vulnerable to a universal forgery attack with bit complexity\\(2^{97}\\), and roughly one out of every 143000 public keys is even breakable in practice within a few minutes.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_10"}, {"primary_key": "161296", "vector": [], "sparse_vector": [], "title": "The 2Hash OPRF Framework and Efficient Post-quantum Instantiations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "An Oblivious Pseudo-Random Function (OPRF) is a two-party protocol for jointly evaluating a Pseudo-Random Function (PRF). OPRFs are a prime tool for building secure authentication and key exchange from passwords, private set intersection, private information retrieval, and many other privacy-preserving systems. While classical OPRFs run as fast as a TLS Handshake, currentquantum-safeOPRF candidates with malicious security are still practically inefficient. In this paper, we propose a framework for constructing OPRFs from secure two-party computation. The framework captures a family of so-called2Hash PRFs, which sandwich a function evaluation between two hashes. The core of our framework is a compiler that yields an OPRF from a secure evaluation of any function that is key-collision resistant and one-more unpredictable. We instantiate this compiler by providing such functions built from Legendre symbols or from a block cipher. We then give a case-tailored protocol for securely evaluating our Legendre-based function, built from Oblivious Transfer (OT) and Zero-Knowledge Proofs (ZKP). Instantiated with lattice-based OT and proofs based on Vector Oblivious Linear Evaluation (VOLE), we obtain the first somewhat practically efficient quantum-safe OPRF with malicious and composable security guarantees. A preliminary implementation shows that an execution of our OPRF protocol, instantiated for 128 bits of security, runs in only\\(185 \\text { ms}\\)if both parties are running in separate threads on the same machine, with a total communication cost of approximately\\(748 \\text { KB}\\).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_12"}, {"primary_key": "161297", "vector": [], "sparse_vector": [], "title": "Weakly Super-Invertible Matrices and Constant Communication Dishonest Majority MPC.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In recent years, there has been tremendous progress in improving the concrete communication complexity of dishonest majority MPC. In the sub-optimal corruption threshold setting where\\(t<(1-\\varepsilon )\\cdot n\\)for some constant\\(0<\\varepsilon \\le 1/2\\), Sharing Transformation (Goyalet al., CRYPTO’22) and SuperPack (Escuderoet al., EUROCRYPT’23) presented protocols with information-theoretic online phases requiringO(1) field elements of total communication per multiplication gate. However, Sharing Transformation assumes that their offline phase is instantiated by a trusted party, while SuperPack instantiates their offline phase with large communication of\\(\\varOmega (n)\\)per multiplication gate assuming oblivious linear evaluation (OLE) correlations. The main bottleneck in instantiating the offline phases of both protocols is generating random packed beaver triples of the form\\([ \\boldsymbol{a} ],[ \\boldsymbol{b} ],[ \\boldsymbol{c} ]\\), for random\\(\\boldsymbol{a},\\boldsymbol{b}\\in \\mathbb {F}^k\\), and\\(\\boldsymbol{c}=\\boldsymbol{a}*\\boldsymbol{b}\\in \\mathbb {F}^k\\), where\\(k=\\varOmega (n)\\)is thepacking parameter. To address this bottleneck, our main technical contribution is introducing and constructingweaklysuper-invertible matrices, a relaxation of super-invertible matrices in which sub-matrices have high (but not necessarily full) rank. This relaxation allows for matrices with only\\(\\widetilde{O}(n)\\)non-zero entries, enabling a first step towards generating packed beaver triples with\\(\\widetilde{O}(1)\\)total communication per underlying triple, assuming OLE correlations. As the second (and final) step, we use the efficienttriple extractionprotocol of (Choudhury and Patra, Trans. Inform. Theory’17). We also implement our packed beaver triple protocol and provide experimental results. Our new protocol obtains up to 38% smaller communication and 9% reduction in runtime compared to SuperPack’s triple protocol. Additionally, by instantiating SuperPack’s offline phase with our new protocol, we obtain up to 16% communication reductions. Finally, we use our packed beaver triple protocol to instantiate the offline phase of Sharing Transformation, yielding a dishonest majority MPC protocol with\\(\\widetilde{O}(|C|)\\)total communication across both the offline and online phases.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_15"}, {"primary_key": "161298", "vector": [], "sparse_vector": [], "title": "Succinct Randomized Encodings from Laconic Function Evaluation, Faster and Simpler.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Succinct randomized encodings allow encoding the inputxof a time-tuniform computationM(x) in sub-linear timeo(t). The resulting encoding\\(\\tilde{x}\\)allows recovering the result of the computationM(x), but hides any other information aboutx. These encodings have powerful applications, including time-lock puzzles, reducing communication in MPC, and bootstrapping advanced encryption schemes. Until not long ago, the only known constructions were based on indistinguishability obfuscation, and in particular were not based on standard post-quantum assumptions. In terms of efficiency, these constructions’ encoding time is\\(\\textrm{polylog}(t)\\), essentially the best one can hope for. Recently, a new construction was presented based on Circular Learning with Errors, an assumption similar to the one used in fully-homomorphic encryption schemes, and which is widely considered to be post-quantum resistant. However, the encoding efficiency significantly falls behind obfuscation-based scheme and is\\(\\approx \\sqrt{t} \\cdot s\\), wheresis the space of the computation. We construct, under the same assumption, succinct randomized encodings with encoding time\\(\\approx t^{\\varepsilon } \\cdot s\\)for arbitrarily small constant\\(\\varepsilon <1\\). Our construction is relatively simple, generic and relies on any laconic function evaluation scheme that satisfies a naturalefficiency preservationproperty. Under sub-exponential assumptions, the encoding time can be further reduced to\\(\\approx \\sqrt{s}\\), but at the account of a huge security loss. As a corollary, assuming also bounded-space languages that are worst-case hard-to-parallelize, we obtain time-lock puzzles with an arbitrary polynomial gap between encoding and decoding times.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_15"}, {"primary_key": "161299", "vector": [], "sparse_vector": [], "title": "The Impact of Reversibility on Parallel Pebbling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The (parallel) classical black pebbling game is a helpful abstraction which allows us to analyze the resources (time, space, space-time, cumulative space) necessary to evaluate a functionfwith a static data-dependency graphGon a (parallel) computer. In particular, the parallel black pebbling game has been used as a tool to quantify the (in)security of Data-Independent Memory-Hard Functions (iMHFs). However, the classical black pebbling game is not suitable to analyze the cost of quantum preimage attack. Thus, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (TCC 2022) introduced the parallel reversible pebbling game as a tool to analyze resource requirements for a quantum computer. While there is an extensive line of work analyzing pebbling complexity in the (parallel) black pebbling game, comparatively little is known about the parallel reversible pebbling game. Our first result is a lower bound of\\(\\varOmega \\left( N^{1+\\sqrt{\\frac{ 2-o(1)}{\\log N}}} \\right) \\)on the reversible cumulative pebbling cost for a line graph onNnodes. This yields a separation between classical and reversible pebbling costs demonstrating that the reversibility constraint can increase cumulative pebbling costs (and space-time costs) by a multiplicative factor of\\(N^{(\\sqrt{2} + o(1))/\\sqrt{\\log N}}\\)—the classical pebbling cost (space-time or cumulative) for a line graph is just\\(\\mathcal {O}\\left( N \\right) \\). On the positive side, we prove thatanyclassical parallel pebbling can be transformed into a reversible pebbling strategy whilst increasing space-time (resp. cumulative memory) costs by a multiplicative factor of at most\\(\\mathcal {O}\\left( N^{\\sqrt{\\frac{8}{\\log N}}} \\right) \\)(resp.\\(\\mathcal {O}\\left( N^{\\mathcal {O}\\left( 1 \\right) /\\root 4 \\of {\\log N}} \\right) \\)). We also analyze the impact of the reversibility constraint on the cumulative pebbling cost of depth-robust and depth-reducible DAGs exploiting reversibility to improve constant factors in a prior lower bound of Alwen, Blocki, and Pietrzak (Eurocrypt 2017). For depth-reducible DAGs we show that the state-of-the-art recursive pebbling techniques of Alwen, Blocki, and Pietrzak (Eurocrypt 2017) can be converted into a recursive reversible pebbling attack without any asymptotic increases in pebbling costs. Finally, we extend a result of Blocki, Lee, and Zhou (ITCS 2020) to show that it is Unique Games-hard to approximate the reversible cumulative pebbling cost of a DAGGto within any constant factor.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_5"}, {"primary_key": "161300", "vector": [], "sparse_vector": [], "title": "Exponent-VRFs and Their Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Verifiable random functions (VRFs)are pseudorandom functions where the function owner can prove that a generated output is correct relative to a committed key. In this paper we introduce the notion of anexponent-VRF (eVRF): a VRF that does not provide its outputyexplicitly, but instead provides\\(Y = y \\cdot G\\), whereGis a generator of some finite cyclic group (or\\(Y=g^y\\)in multiplicative notation). We construct eVRFs from the Paillier encryption scheme and from DDH, both in the random-oracle model. We then show that an eVRF is a powerful tool that has many important applications in threshold cryptography. In particular, we construct (1) a one-round fully simulatable distributed key-generation protocol (after a single two-round initialization phase), (2) a two-round fully simulatable signing protocol for multiparty Schnorr with a deterministic variant, (3) a two-party ECDSA protocol that has a deterministic variant, (4) a threshold Schnorr signing protocol where the parties can later prove that they signed without being able to frame another group, and (5) an MPC-friendly and verifiable HD-derivation. All these applications are derived from this single new eVRF abstraction, and the resulting protocols are concretely efficient.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_8"}, {"primary_key": "161301", "vector": [], "sparse_vector": [], "title": "Good Things Come to Those Who Wait - Dishonest-Majority Coin-Flipping Requires Delay Functions.", "authors": ["<PERSON>", "Benedikt <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We reconsider <PERSON><PERSON><PERSON>’s famous 1986 impossibility result on coin-flipping without an honest majority. Recently proposed constructions have circumvented this limit by using cryptographic delay functions. We show that this is necessary: a (weak) notion of delay functions is in factimpliedby the existence of a protocol circumventing <PERSON><PERSON><PERSON>’s impossibility. However, such delay functions are weaker than those used in existing constructions. We complete our result by showing an equivalence, that these weaker delay functions are also sufficient to construct not just fair dishonest-majority coin-flipping protocols, but also the stronger notion of a distributed randomness beacon. We also show that this is possible in a weaker communication model than previously considered, without the assumption of reliable broadcast or a public bulletin board.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_9"}, {"primary_key": "161302", "vector": [], "sparse_vector": [], "title": "Oracle Separation Between Quantum Commitments and Quantum One-Wayness.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that there exists a unitary quantum oracle relative to which quantum commitments exist but no (efficiently verifiable) one-way state generators exist. Both have been widely considered candidates for replacing one-way functions as the minimal assumption for cryptography—the weakest cryptographic assumption implied by all of computational cryptography. Recent work has shown that commitments can be constructed from one-way state generators, but the other direction has remained open. Our results rule out any black-box construction, and thus settle this crucial open problem, suggesting that quantum commitments (as well as its equivalency class of EFI pairs, quantum oblivious transfer, and secure quantum multiparty computation) appear to be strictly weakest among all known cryptographic primitives.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_1"}, {"primary_key": "161303", "vector": [], "sparse_vector": [], "title": "Simultaneous-Message and Succinct Secure Computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We put forth a new primitive we callsimultaneous-message and succinct (SMS)secure computation. An SMS scheme enables aminimalcommunication pattern for secure computation in the following scenario: <PERSON> has a large private inputX, <PERSON> has a small private inputy, and <PERSON> wants to learnf(X,y) for some public functionf. Given a common reference string (CRS) setup phase, an SMS scheme for a functionfis instantiated with two parties holding inputsXandy, and has the following structure: The partiessimultaneouslyexchange a single message. Communication issuccinct, scaling sublinearly in the size ofXand the outputf(X,y). Without further interaction, the parties can locally derive additive secret shares off(X,y). We obtain the following results. First, assuming Learning With Errors (LWE), we build an SMS scheme supporting evaluation of depth-dcircuits, where <PERSON>’s message is of size\\(|f(X,y)|^{2/3} \\cdot \\textsf{poly}(\\lambda ,d)\\), <PERSON>’s message is of size\\((|y| + |f(X,y)|^{2/3}) \\cdot \\textsf{poly}(\\lambda ,d)\\), and\\(\\lambda \\)is the security parameter. We can further extend this to support all functions by assuming the circular security of LWE. Second, assuming sub-exponentially secure indistinguishability obfuscation (\\(i\\mathcal {O}\\)), along with other standard assumptions, we build an SMS scheme supporting arbitrary polynomial-sizedbatchfunctions of the form\\((f(x_1,y),\\ldots ,f(x_L, y))\\), for\\(X = (x_1,\\ldots ,x_L)\\). Alice’s and Bob’s messages in this construction are of size\\(\\textsf{poly}(\\lambda )\\)and\\(\\textsf{poly}(\\lambda , |f|, \\log L)\\), respectively. We show that SMS schemes have several immediate applications, including (1) a construction of trapdoor hash functions (TDH) (Döttling et al., Crypto’19) for the same class of functions as the one supported by the SMS scheme, (2) a generic compiler for obtaining rate-1 fully homomorphic encryption (FHE) from any non-compact FHE scheme, and (3) a generic compiler for correlation-intractable hash functions that are secure against all efficiently-searchable relations.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_8"}, {"primary_key": "161304", "vector": [], "sparse_vector": [], "title": "The Complexity of Memory Checking with Covert Security.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A memory checker is an algorithmic tool used to certify the integrity of a database maintained on a remote, unreliable, computationally bounded server. Concretely, it allows a user to issue instructions to the server and after every instruction, obtain either the correct value or a failure (but not an incorrect answer) with high probability. A recent result due to <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (BK<PERSON>, STOC ’24) showed a tradeoff between the size of the local storage and the number of queries the memory checker makes to the server upon every logical instruction. Specifically, they show that every non-trivial memory checker construction with inverse-polynomial soundness and local storage at most\\(n^{1 - \\epsilon }\\)must make\\(\\varOmega (\\log n/ \\log \\log n)\\)queries, and this is tight up to constant factors given known constructions. However, an intriguing question is whether natural relaxations of the security guarantee could allow for more efficient constructions. We consider and adapt the notion ofcovertsecurity to the memory checking context, wherein the adversary can effectively cheat while taking the risk of being caught with constant probability. Notably, BKV’s lower bound does not apply in this setting. We close this gap and prove that\\(\\varOmega (\\log n/ \\log \\log n)\\)overhead is unavoidable even in the covert security setting. Our lower bound applies to any memory checker construction, including ones that use randomness and adaptivity and ones that rely on cryptographic assumptions and/or the random oracle model, as long as they satisfy a natural “read-only reads” property. This property requires a memory checker not to modify contents of the database or local storage in the execution of a logical read instruction.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_11"}, {"primary_key": "161305", "vector": [], "sparse_vector": [], "title": "Black-Box Non-interactive Zero Knowledge from Vector Trapdoor Hash.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a new approach for constructing non-interactive zero-knowledge (NIZK) proof systems fromvector trapdoor hashing(VTDH) – a generalization of trapdoor hashing [<PERSON><PERSON><PERSON><PERSON> et al., Crypto’19]. Unlike prior applications of trapdoor hash to NIZKs, we use VTDH to realize the hidden bits model [Feige-Lapidot-Shamir, FOCS’90] leading toblack-boxconstructions of NIZKs. This approach gives us the following new results: Astatistically-soundNIZK proof system based on the hardness of decisional <PERSON><PERSON><PERSON><PERSON> (DDH) and learning parity with noise (LPN) over finite fields with inverse polynomial noise rate. This gives the first statistically sound NIZK proof system that is not based on either LWE, or bilinear maps, or factoring. A dual-mode NIZK satisfying statistical zero-knowledge in the common random string mode and statistical soundness in the common reference string mode assuming the hardness of learning with errors (LWE) withpolynomialmodulus-to-noise ratio. This gives the firstblack-boxconstruction of such a dual-mode NIZK under LWE. This improves the recent work of Waters (STOC’24) which relied on LWE with super-polynomial modulus-to-noise ratio and required a setup phase with private coins. The above constructions are black-box and satisfy single-theorem zero-knowledge property. Building on the works of <PERSON><PERSON> et al.(FOCS’90) and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (PKC’21), we upgrade these constructions (under the same assumptions) to satisfy multi-theorem zero-knowledge property at the expense of making non-black-box use of cryptography.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_3"}, {"primary_key": "161306", "vector": [], "sparse_vector": [], "title": "Blaze: Fast SNARKs from Interleaved RAA Codes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this work we construct a new and highly efficient multilinear polynomial commitment scheme (MLPCS) over binary fields, which we callBlaze. Polynomial commitment schemes allow a server to commit to a large polynomial and later decommit to its evaluations. Such schemes have emerged as a key component in recent efficient SNARK constructions. Blaze has an extremely efficient prover, both asymptotically and concretely. For a witness size of\\(n \\in \\mathbb {N}\\), the commitment is dominated by 8nfield additions (i.e., XORs) and one Merkle tree computation. The evaluation proof generation is dominated by 6nadditions and 5nmultiplications over the field. The verifier runs in time\\(O_\\lambda (\\log ^2(n))\\). Concretely, for sufficiently large message sizes, the prover is faster than all prior schemes except for Brakedown (Golovnevet al., Crypto 2023), but offers significantly smaller proofs than the latter. The scheme is obtained by combining two ingredients: Building on the code-switching technique (<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>, JACM 2024), we show how to compose any error-correcting code together with an interactive oracle proof of proximity (IOPP) underlying existing MLPCS constructions, into a new MLPCS. The new MLPCS inherits its proving time from the code’s encoding time, and its verification complexity from the underlying MLPCS. The composition is distinctive in that it is done purely on the information-theoretic side. We apply the above methodology using an extremely efficient error-correcting code known as the Repeat-Accumulate-Accumulate (RAA) code. We give new asymptotic and concrete bounds, which demonstrate that (for sufficiently large message sizes) this code has a better encoding time vs. distance tradeoff than previous linear-time encodable codes that were considered in the literature.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_5"}, {"primary_key": "161307", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON> Basis Cryptanalysis of Anemoi.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Arithmetization-Oriented (AO) symmetric primitives play an important role in the efficiency and security of zero-knowledge (ZK) proof systems. The design and cryptanalysis of AO symmetric-key primitives is a new topic particularly focusing on algebraic aspects. An efficient AO hash function aims at lowering the multiplicative complexity in the arithmetic circuit of the hash function over a suitable finite field. The AO hash function Anemoi was proposed in CRYPTO 2023. In this work we present an in-depth Gröbner basis (GB) cryptanalysis of Anemoi over\\(\\mathbb {F}_p\\). The main aim of any GB cryptanalysis is to obtain a well-structured set of polynomials representing the target primitive, and finally solve this system of polynomials using an efficient algorithm. We propose a new polynomial modelling for Anemoi that we call ACICO. We show that using ACICO one can obtain a GB defined by a well-structured set of polynomials. Moreover, by utilising ACICO we can prove the exact complexity of the <PERSON><PERSON><PERSON><PERSON> basis computation (w.r.t <PERSON><PERSON><PERSON>’s algorithm) in the cryptanalysis of Anemoi. The structured GB further allows us to prove the dimension of the quotient space, that implicitly decides the complexity of the polynomial solving step, and was conjectured in a recently published work. Afterwards, we provide the complexity analysis for computing the variety (or the solutions) of the GB polynomial system (corresponding to Anemoi) which is the final step in GB cryptanalysis, by using known approaches. In particular, we show that GB polynomial structure allows us to use the <PERSON><PERSON><PERSON> algorithm and improve the efficiency of cryptanalysis compared to previous works. Our GB cryptanalysis is applicable tomore than two branches(a parameter in Anemoi), while the previously published results showed cryptanalysis only for two branches. Our complexity analysis implies that the security of Anemoi should not rely upon the GB computation. We also address an important mathematical question in GB cryptanalysis of Anemoi namely,does the Anemoi polynomial system has a Shape form?, positively. By proving this we guarantee that upon application of basis conversion method like FGLM one can obtain a convenient system of polynomials that are easy to solve.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_11"}, {"primary_key": "161308", "vector": [], "sparse_vector": [], "title": "SNARKs for Virtual Machines Are Non-malleable.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cryptographic proof systems have a plethora of applications: from building other cryptographic tools (e.g., malicious security for MPC protocols) to concrete settings such as private transactions or rollups. In several settings it is important for proof systems to benon-malleable: an adversary should not to be able to modify a proof they have observed into another for a statement for which they do not know the witness. Proof systems that have been deployed in practice should arguably satisfy this notion: it is crucial in settings such as transaction systems and in order to securely compose proofs with other cryptographic protocols. As a consequence, results on non-malleability should keep up with designs of proofs being deployed. Recently, <PERSON><PERSON> et al. proposed\\(\\textsf{Jolt}\\)(Eurocrypt 2024), arguably the first efficient proof system whose architecture is based on thelookup singularityapproach (<PERSON>, 2022). This approach consists in representing a general computation as a series oftable lookups. The final result is a SNARK for a Virtual Machine execution (or SNARK VM). Both SNARK VMs and lookup-singularity SNARKs are architectures with enormous potential and will probably be adopted more and more in the next years (and they already are). As of today, however, there is no literature regarding the non-malleability of SNARK VMs. The goal of this work is to fill this gap by providing both concrete non-malleability results and a set of technical tools for a more general study of SNARK VMs security (as well as “modular” SNARKs in general). As a concrete result, we study the non-malleability of (an idealized version of)\\(\\textsf{Jolt}\\)and its fundamental building block, the lookup argument\\(\\textsf{Lasso}\\). While connecting our new result on the non-malleability of\\(\\textsf{Lasso}\\)to that of\\(\\textsf{Jolt}\\), we develop a set of tools that enable the composition of non-malleable SNARKs. We believe this toolbox to be valuable in its own right.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_6"}, {"primary_key": "161309", "vector": [], "sparse_vector": [], "title": "Polynomial Time Cryptanalytic Extraction of Deep Neural Networks in the Hard-Label Setting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep neural networks (DNNs) are valuable assets, yet their public accessibility raises security concerns about parameter extraction by malicious actors. Recent work by <PERSON><PERSON> et al. (Crypto’20) and <PERSON><PERSON><PERSON> et al. (Eurocrypt’24) has drawn parallels between this issue and block cipher key extraction via chosen plaintext attacks. Leveraging differential cryptanalysis, they demonstrated that all the weights and biases of black-box ReLU-based DNNs could be inferred using a polynomial number of queries and computational time. However, their attacks relied on the availability of the exact numeric value of output logits, which allowed the calculation of their derivatives. To overcome this limitation, <PERSON> et al. (Asiacrypt’24) tackled the more realistichard-label scenario, where only the final classification label (e.g., “dog” or “car”) is accessible to the attacker. They proposed an extraction method requiring a polynomial number of queries but an exponential execution time. In addition, their approach was applicable only to a restricted set of architectures, could deal only with binary classifiers, and was demonstrated only on tiny neural networks with up to four neurons split among up to two hidden layers. This paper introduces new techniques that, for the first time, achieve cryptanalytic extraction of DNN parameters in the most challenging hard-label setting, using both a polynomial number of queriesandpolynomial time. We validate our approach by extracting nearly one million parameters from a DNN trained on the CIFAR-10 dataset, comprising 832 neurons in four hidden layers. Our results reveal the surprising fact that all the weights of a ReLU-based DNN can be efficiently determined by analyzing only the geometric shape of its decision boundaries.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_13"}, {"primary_key": "161310", "vector": [], "sparse_vector": [], "title": "Generic Anamorphic Encryption, Revisited: New Limitations and Constructions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The notion of Anamorphic Encryption (Persianoet al.Eurocrypt 2022) aims at establishing private communication against an adversary who can access secret decryption keys and influence the chosen messages. Persianoet al.gave a simple, black-box, rejection sampling-based technique to send anamorphicbitsusing any\\( \\textrm{IND}\\text {-}\\textrm{CPA}\\)secure scheme as underlying PKE. In this paper however we provide evidence that their solution is not as general as claimed: indeed there exists a (contrived yet secure) PKE which lead to insecure anamorphic instantiations. Actually, our result implies that such stateless black-box realizations of AE are impossible to achieve, unless weaker notions are targeted or extra assumptions are made on the PKE. Even worse, this holds true even if one resort to powerful non-black-box techniques, such as NIZKs,\\( \\textsf{iO}\\)or garbling. From a constructive perspective, we shed light on those required assumptions. Specifically, we show that one could bypass (to some extent) our impossibility by either considering a weaker (but meaningful) notion of AE or by assuming the underlying PKE to (always) produce high min-entropy ciphertexts. Finally, we prove that, for the case ofFully-AsymmetricAE,\\( \\textsf{iO}\\)canactually be used to overcome existing impossibility barriers. We show how to use\\( \\textsf{iO}\\)to build Fully-Asymmetric AE (with small anamorphic message space) generically from any\\( \\textrm{IND}\\text {-}\\textrm{CPA}\\)secure PKE with sufficiently high min-entropy ciphertexts. Put together our results provide a clearer picture of what black-box constructions can and cannot achieve.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_10"}, {"primary_key": "161311", "vector": [], "sparse_vector": [], "title": "A Meta-complexity Characterization of Quantum Cryptography.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove the first meta-complexity characterization of a quantum cryptographic primitive. We show that one-way puzzles exist if and only if there is some quantum samplable distribution of binary strings over which it is hard to approximate Kolmogorov complexity. Therefore, we characterize one-way puzzles by the average-case hardness of auncomputableproblem. This brings to the quantum setting a recent line of work that characterizes classical cryptography with the average-case hardness of a meta-complexity problem, initiated by <PERSON> and <PERSON>. Moreover, since the average-case hardness of Kolmogorov complexity overclassicallypolynomial-time samplable distributions characterizes one-way functions, this result poses one-way puzzles as a natural generalization of one-way functions to the quantum setting. Furthermore, our equivalence goes through probability estimation, giving us the additional equivalence that one-way puzzles exist if and only if there is a quantum samplable distribution over which probability estimation is hard. We also observe that the oracle worlds of defined by <PERSON> et al. rule out any relativizing characterization of one-way puzzles by the hardness of a problem in\\(\\textsf{NP}\\)or\\(\\textsf{QMA}\\), which means that it may not be possible with current techniques to characterize one-way puzzles with another meta-complexity problem.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_4"}, {"primary_key": "161312", "vector": [], "sparse_vector": [], "title": "Malleable SNARKs and Their Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Succinct non-interactive arguments of knowledge (SNARKs) are variants of non-interactive zero-knowledge proofs (NIZKs) in which complex statements can be proven in a compact way. SNARKs have had tremendous impact in several areas of cryptography, including verifiable computing, blockchains, and anonymous communication. A recurring concept in many applications is the concept of recursive SNARKs, in which a proof references a previous proof to show an evolved statement. In this work, we investigatemalleable SNARKs, a generalization of this concept of recursion. An adaptation of the existing concept of malleable NIZKs, malleable SNARKs allow to modify SNARK proofs to show related statements, but such that such mauled proofs are indistinguishable from “properly generated” fresh proofs of the related statement. We show how to instantiate malleable SNARKs for universal languages and relations, and give a number of applications: the first post-quantum RCCA-secure rerandomizable and updatable encryption schemes, a generic construction of reverse firewalls, and an unlinkable (i.e., computation-hiding) targeted malleable homomorphic encryption scheme. Technically, our malleable SNARK construction relies on recursive proofs, but with a twist: in order to support the strong indistinguishability properties of mauled and fresh SNARK proofs, we need to allow an unbounded recursion depth. To still allow for a reasonable notion of extractability in this setting (and in particular to guarantee that extraction eventually finishes with a “proper” witness that does not refer to a previous SNARK proof), we rely on a new and generic computational primitive calledadversarial one-way function (AOWF)that may be of independent interest. We give an AOWF candidate and prove it secure in the random oracle model.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_7"}, {"primary_key": "161313", "vector": [], "sparse_vector": [], "title": "Quantum Key Leasing for PKE and FHE with a Classical Lessor.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we consider the problem of secure key leasing, also known as revocable cryptography (<PERSON><PERSON><PERSON> et al. Eurocrypt’ 23, <PERSON><PERSON><PERSON> et al. TCC’ 23), as a strengthened security notion to its predecessor put forward in <PERSON><PERSON><PERSON> et al. (Eurocrypt’ 21). This problem aims to leverage unclonable nature of quantum information to allow a lessor to lease a quantum key with reusability for evaluating a classical functionality. Later, the lessor can request the lessee to provably delete the key and then the lessee will be completely deprived of the capability to evaluate. In this work, we construct a secure key leasing scheme to lease a decryption key of a (classical) public-key, homomorphic encryption scheme from standard lattice assumptions. Our encryption scheme is exactly identical to the (primal) version of Gentry-Sahai-Waters homomorphic encryption scheme with a carefully chosen public key matrix. We achieve strong form of security where: The entire protocol (including key generation and verification of deletion) uses merely classical communication between aclassical lessor (client)and a quantum lessee (server). Assuming standard assumptions, our security definition ensures that every computationally bounded quantum adversary could only simultaneously provide a valid classical deletion certificate and yet distinguish ciphertexts with at most some negligible probability. Our security relies on subexponential time hardness of learning with errors assumption. Our scheme is the first scheme to be based on a standard assumption and satisfying the two properties mentioned above. The main technical novelty in our work is the design of an FHE scheme that enables us to apply elegant analyses done in the context of classical verification of quantumness from LWE (Brakers<PERSON> et al. (FOCS’18, JACM’21) and its parallel amplified version in Radian et al. (AFT’21)) to the setting of secure leasing. This connection to classical verification of quantumness leads to a modular construction and arguably simpler proofs than previously known. An important technical component we prove along the way is an amplified quantum search-to-decision reduction: we design an extractor that uses a quantum distinguisher (who has an internal quantum state) for decisional LWE, to extract secrets with success probability amplified to almost one. This technique might be of independent interest.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_9"}, {"primary_key": "161314", "vector": [], "sparse_vector": [], "title": "The Power of a Single Haar Random State: Constructing and Separating Quantum Pseudorandomness.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Or Sattath"], "summary": "In this work, we focus on the following question: what are the cryptographic implications of having access to an oracle that provides asingleHaar random quantum state? We find that the study of such a model sheds light on several aspects of the notion of quantum pseudorandomness. Pseudorandom states are a family of states for which it is hard to distinguish between polynomially many copies of either a state sampled uniformly from the family or a Haar random state. A weaker notion, called single-copy pseudorandom states (\\({\\textsf{1PRS}}\\)), satisfies this property with respect to a single copy. We obtain the following results: First, we show, perhaps surprisingly, that\\({\\textsf{1PRS}}\\)(as well as bit-commitments) exist relative to an oracle that provides asingleHaar random state. Second, we build on this result to show the existence of an isometry oracle relative to which\\({\\textsf{1PRS}}\\)exist, but\\({\\textsf{PRS}}\\)do not. Taken together, our contributions yield one of the first black-box separations between central notions of quantum pseudorandomness, and introduce a new framework to study black-box separations between various inherently quantum primitives.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_5"}, {"primary_key": "161315", "vector": [], "sparse_vector": [], "title": "Towards Optimally Secure Deterministic Authenticated Encryption Schemes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The public comments received for the review process for NIST (SP) 800-38A pointed out two important issues that most companies face: (1) the limited security that AES can provide due to its 128-bit block size and (2) the problem of nonce-misuse in practice. In this paper, we provide an alternative solution to these problems by introducing two optimally secure deterministic authenticated encryption (DAE) schemes, denoted asDENC1andDENC2respectively. We show that our proposed constructions improve the state-of-the-art in terms of security and efficiency. Specifically,DENC1achieves a robust security level of\\(O(r^2\\sigma ^2\\ell /2^{2n})\\), whileDENC2attains a near-optimal security level of\\(O(r\\sigma /2^{n})\\), where\\(\\sigma \\)is the total number of blocks,\\(\\ell \\)is maximum number of blocks in each query, andris a user-defined parameter closely related to the rate of the construction. Our research centers on the development of two IV-based encryption schemes, referred to asIV1andIV2, which respectively offer security levels of\\(O(r^2\\sigma ^2\\ell /2^{2n})\\)and\\(O(r\\sigma /2^{n})\\). Notably, both of our DAE proposals are nearly rate 1/2 constructions. In terms of efficiency, our proposals compare favorably with state-of-the-art AE modes on contemporary microprocessors.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_1"}, {"primary_key": "161316", "vector": [], "sparse_vector": [], "title": "Committing Authenticated Encryption: Generic Transforms with Hash Functions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent applications and attacks have highlighted the need for authenticated encryption (AE) schemes to achieve the so-called committing security beyond privacy and authenticity. As a result, several generic solutions have been proposed to transform a non-committing AE scheme to a committing one, for both basic unique-nonce security and advanced misuse-resistant (MR) security. We observe that all existing practical generic transforms are subject to at least one of the following limitations: (i) not committing to the entire encryption context, (ii) involving non-standard primitives, (iii) not being a black-box transform, (iv) providing limited committing security. Furthermore, so far, there has been no generic transform that can directly elevate a basic AE scheme to a committing AE scheme that offers MR security. Our work fills these gaps by developing black-box generic transforms that crucially rely on hash functions, which are well standardized and widely deployed. First, we construct three basic transforms that combine AE with a single hash function, which we call\\(\\textsf{HtAE}\\),\\(\\textsf{AEaH}\\)and\\(\\textsf{EtH}\\). They all guarantee strong security, and\\(\\textsf{EtH}\\)can be applied to both AE and basic privacy-only encryption schemes. Next, for MR security, we propose two advanced hash-based transforms that we call\\(\\textsf{AEtH}\\)and\\(\\textsf{chaSIV}\\).\\(\\textsf{AEtH}\\)is an MRAE-preserving transform that adds committing security to an MR-secure AE scheme.\\(\\textsf{chaSIV}\\)is thefirstgeneric transform that can directly elevate basic AE to one with both committing and MR security; moreover,\\(\\textsf{chaSIV}\\)also works with arbitrary privacy-only encryption schemes. Both of them feature a simple design and ensure strong security. For performance evaluation, we compare our transforms to similar existing ones, both in theory and through practical implementations. The results show that our\\(\\textsf{AEaH}\\)achieves the highest practical efficiency among basic transforms, while\\(\\textsf{AEtH}\\)excels in MRAE-preserving transforms. Our MRAE-lifting transform\\(\\textsf{chaSIV}\\)demonstrates comparable performance to MRAE-preserving ones and surpasses them for messages larger than approximately 360 bytes; for longer messages, it even outperforms the benchmark, non-committing standardized\\(\\mathsf {AES\\text {-}GCM\\text {-}SIV}\\).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_4"}, {"primary_key": "161317", "vector": [], "sparse_vector": [], "title": "Universal Computational Extractors and Multi-Bit AIPO from Lattice Assumptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We put forth a new primitive calledobliviously programmable function (OPF)to construct two random-oracle-like primitives: Universal computational extractors (UCEs), introduced by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> [3], can securely replace random oracles in various applications, including KDM-secure encryption, deterministic encryption, RSA-OAEP, universal hardcore bits, etc. Multi-bit point obfuscation with auxiliary input (MB-AIPO). It enables upgrading CPA-secure public-key encryption (PKE) into a CCA-secure one [30] and serves as a tool to instantiate the random oracles used in the Fujisaki-Okamoto transform for lossy PKEs [32]. Despite their usefulness, constructing UCEs and MB-AIPO in the standard model is challenging. The existing constructions of both primitives [15,16] use indistinguishability obfuscation (iO) plus point functions with auxiliary input (AIPO). OPF can replace the use iO in the constructions of UCE and MB-AIPO. We use OPF plus AIPO to construct UCE with one query for strongly unpredictable sources, MB-AIPO for strongly unpredictable distributions and PKE scheme that is IND-CPA secure in the presence of computationally uninvertible leakage on the secret key. We then construct OPF for\\(\\textsf{NC}^1\\)circuits from lattice assumptions based on the GGH15 encodings [23], without using iO. In sum, we give new constructions of the above three primitives under the following assumptions: (1) LWE with subexponential hardness; (2) private-coin evasive LWE assumption for specific samplers; (3) the existence of AIPO in\\(\\textsf{NC}^1\\). As a byproduct, we construct an ‘\\(\\textsf{NC}^1\\)-universal AIPO’ under the assumptions (1) and (2).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_3"}, {"primary_key": "161318", "vector": [], "sparse_vector": [], "title": "Computing the Endomorphism Ring of a Supersingular Elliptic Curve from a Full Rank Suborder.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we study the problem of computing the endomorphism ring of a supersingular elliptic curve given the knowledge of a full rank suborder. We provide a polynomial time quantum algorithm to solve this problem in full generality. This result enhances our understanding of the endomorphism ring problem, which is at the core of isogeny-based cryptography. As part of our approach, we also present a polynomial time quantum algorithm to solve the problem of computing the endomorphism ring of the codomain curve of an isogeny from a curve with known endomorphism ring. This extends the work of [CII+23a] by lifting their restrictions on the number of factors of the isogeny degree. As an application, we present quantum reductions between key hard problems in isogeny-based cryptography. We show that some of our quantum reductions are tighter than the classical ones, while all reductions are of polynomial time complexity. In particular, we improve the query complexity of the reduction of the\\({{\\,\\mathrm{\\textsf {EndRing}}\\,}}\\)problem to the\\({{\\,\\mathrm{\\textsf {OneEnd}}\\,}}\\)problem from\\({{\\,\\textrm{poly}\\,}}(\\log p)\\)(classically) toO(1) (quantumly), strengthening the hardness assumption of the\\({{\\,\\mathrm{\\textsf {OneEnd}}\\,}}\\)problem in the post-quantum setting. This reduction underlies the 2-special soundness proof of SQIsign identification protocols.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_16"}, {"primary_key": "161319", "vector": [], "sparse_vector": [], "title": "SHIP: A Shallow and Highly Parallelizable CKKS Bootstrapping Algorithm.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The CKKS fully homomorphic encryption scheme enables efficient homomorphic operations in terms of throughput, but its bootstrapping algorithm incurs a significant latency. In this work, we introduce SHIP, a novel bootstrapping algorithm for CKKS ciphertexts. SHIP enjoys a very shallow homomorphic multiplicative depth compared to state-of-the-art CKKS bootstrapping algorithms. Bootstrapping depth directly impacts the required Ring-LWE modulus, and hence the Ring-LWE degree. The massive depth saving allows us to report the first bootstrapping of CKKS ciphertexts for full-dimensional cleartext vectors in ring degree\\(N=2^{13}\\), without resorting to an expensive scheme switching to DM/CGGI. SHIP also enjoys great parallelizability, with minimal communication between threads. The combined ring size reduction and high parallelizability lead to very low latency. In ring degree\\(N=2^{13}\\), our experimental implementation runs in 215ms on a 32-core CPU for real-valued cleartext vectors. This is 2.5x lower than the smallest latency we could observe with the HEaaN library (using 48 cores). For binary cleartext vectors, the latency is lowered to 174ms, which is 2.2x lower than <PERSON><PERSON> et al. [Eurocrypt’24] (with 32 cores).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_14"}, {"primary_key": "161320", "vector": [], "sparse_vector": [], "title": "A Reduction from <PERSON> to the Principal Ideal Problem in a Quaternion Algebra.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this article we present a non-uniform reduction from rank-2 module-LIP over Complex Multiplication fields, to a variant of the Principal Ideal Problem, in some fitting quaternion algebra. This reduction is classical deterministic polynomial-time in the size of the inputs. The quaternion algebra in which we need to solve the variant of the principal ideal problem depends on the parameters of the module-LIP problem, but not on the problem’s instance. Our reduction requires the knowledge of some special elements of this quaternion algebras, which is why it is non-uniform. In some particular cases, these elements can be computed in polynomial time, making the reduction uniform. This is the case for the Hawk signature scheme: we show that breaking <PERSON> is no harder than solving a variant of the principal ideal problem in a fixed quaternion algebra (and this reduction is uniform).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_6"}, {"primary_key": "161321", "vector": [], "sparse_vector": [], "title": "Making GCM Great Again: Toward Full Security and Longer Nonces.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The\\(\\textsf{GCM}\\)authenticated encryption (AE) scheme is one of the most widely used AE schemes in the world, while it suffers from risk of nonce misuse, short message length per encryption and an insufficient level of security. The goal of this paper is to design new AE schemes achieving stronger provable security in the standard model and accepting longer nonces (or providing nonce misuse resistance), with the design rationale behind\\(\\textsf{GCM}\\). As a result, we propose two enhanced variants of\\(\\textsf{GCM}\\)and\\(\\textsf{GCM}\\text {-}\\textsf{SIV}\\), dubbed\\(\\textsf{eGCM}\\)and\\(\\textsf{eGCM}\\text {-}\\textsf{SIV}\\), respectively.\\(\\textsf{eGCM}\\)and\\(\\textsf{eGCM}\\text {-}\\textsf{SIV}\\)are built on top of a new\\(\\textsf{CENC}\\)-type encryption mode, dubbed\\(\\textsf{eCTR}\\): using 2n-bit counters,\\(\\textsf{eCTR}\\)enjoys beyond-birthday-bound security without significant loss of efficiency.\\(\\textsf{eCTR}\\)is combined with an almost uniform and almost universal hash function, yielding a variable input-length variable output-length pseudorandom function, dubbed\\(\\textsf{HteC}\\).\\(\\textsf{GCM}\\)and\\(\\textsf{GCM}\\text {-}\\textsf{SIV}\\)are constructed using\\(\\textsf{eCTR}\\)and\\(\\textsf{HteC}\\)as building blocks. \\(\\textsf{eGCM}\\)and\\(\\textsf{eGCM}\\text {-}\\textsf{SIV}\\)accept nonces of arbitrary length, and provide almost the full security (namely,n-bit security when they are based on ann-bit block cipher) for a constant maximum input length, under the assumption that the underlying block cipher is a pseudorandom permutation (PRP). Their efficiency is also comparable to\\(\\textsf{GCM}\\)in terms of the rate and the overall speed.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_2"}, {"primary_key": "161322", "vector": [], "sparse_vector": [], "title": "Black-Box Constant-Round Secure 2PC with Succinct Communication.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The most fundamental performance metrics of secure multi-party computation (MPC) protocols are related to the number of messages the parties exchange (i.e., round complexity), the size of these messages (i.e., communication complexity), and the overall computational resources required to execute the protocol (i.e., computational complexity). Another quality metric of MPC protocols is related to theblack-boxornon-black-boxuse of the underlying cryptographic primitives. Indeed, the design of black-box MPC protocols, other than being of theoretical interest, usually can lead to protocols that have better computational complexity. In this work, we aim to optimize the round and communication complexity ofblack-boxsecure multi-party computation in the plain model, by designing a constant-round two-party computation protocol in the malicious setting, whose communication complexity is only polylogarithmic in the size of the function being evaluated. We successfully design such a protocol, having onlyblack-boxaccess to fully homomorphic encryption, trapdoor permutations, and hash functions. To the best of our knowledge, our protocol is the first to make black-box use of standard cryptographic primitives while achieving almost asymptotically optimal communication and round complexity.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_13"}, {"primary_key": "161323", "vector": [], "sparse_vector": [], "title": "Round-Optimal Black-Box Multiparty Computation from Polynomial-Time Assumptions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A central direction of research in secure multiparty computation with dishonest majority has been to achieve three main goals: reduce the total number of rounds of communication (to four, which is optimal); use only polynomial-time hardness assumptions, and rely solely on cryptographic assumptions in a black-box manner. This is especially challenging when we do not allow a trusted setup assumption of any kind. While protocols achieving two out of three goals in this setting have been designed in recent literature, achieving all threesimultaneouslyremained an elusive open question. Specifically, it was answered positively only for a restricted class of functionalities. In this paper, we completely resolve this long-standing open question. Specifically, we present a protocol for all polynomial-time computable functions that does not require any trusted setup assumptions and achieves all three of the above goals simultaneously.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_12"}, {"primary_key": "161324", "vector": [], "sparse_vector": [], "title": "Faster ABE for Turing Machines from Circular Evasive LWE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We present a new ABE for Turing machines, where encryption runs in timeO(T) and produces a ciphertext of sizeO(T) for timeTcomputation; key generation produces a key of sizeO(1); security relies on the (public-coin) circular evasive LWE assumption. In fact, we obtain an ABE for a simple and more general model of computation which we refer to as iterated (local) computation. We improve on the recent construction of <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (CRYPTO 24) in three ways: (i) faster encryption and smaller ciphertext size; (ii) smaller key size, and (iii) weaker assumptions, eliminating the need for circular tensor LWE.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_4"}, {"primary_key": "161325", "vector": [], "sparse_vector": [], "title": "Juggernaut: Efficient Crypto-Agnostic Byzantine Agreement.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It is well known that a trusted setup allows one to solve the Byzantine agreement problem in the presence of\\(t<n/2\\)corruptions, bypassing the setup-free\\(t<n/3\\)barrier. Alas, the overwhelming majority of protocols in the literature have the caveat that their security crucially hinges on the security of the cryptography and setup, to the point where if the cryptography is broken, even a single corrupted party can violate the security of the protocol. Thus these protocols provide higher corruption resilience (n/2 instead ofn/3) for the price of increased assumptions. Is this trade-off necessary? We further the study ofcrypto-agnosticByzantine agreement amongnparties that answers this question in the negative. Specifically, let\\(t_s\\)and\\(t_i\\)denote two parameters such that (1)\\(2t_i + t_s < n\\), and (2)\\(t_i \\le t_s < n/2\\). Crypto-agnostic Byzantine agreement ensures agreement among honest parties if (1) the adversary is computationally bounded and corrupts up to\\(t_s\\)parties, or (2) the adversary is computationally unbounded and corrupts up to\\(t_i\\)parties, and is moreover given all secrets of all parties established during the setup. We propose a compiler that transforms any pair of resilience-optimal Byzantine agreement protocols in the authenticated and information-theoretic setting into one that is crypto-agnostic. Our compiler has several attractive qualities, including using only\\(O(\\lambda n^2)\\)bits over the two underlying Byzantine agreement protocols, and preserving round and communication complexity in the authenticated setting. In particular, our results improve the state-of-the-art in bit complexity by at least two factors ofnand provide either early stopping (deterministic) or expected constant round complexity (randomized). We therefore provide fallback security for authenticated Byzantine agreementfor freefor\\(t_i \\le n/4\\).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_3"}, {"primary_key": "161326", "vector": [], "sparse_vector": [], "title": "Somewhat Homomorphic Encryption from Linear Homomorphism and Sparse LPN.", "authors": ["<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We construct somewhat homomorphic encryption from the sparse learning-parities-with-noise problem, along with an assumption that implies linearly homomorphic encryption (e.g., the decisional Di<PERSON>ie-Hellman or decisional composite residuosity assumptions). Our resulting schemes support an a-priori bounded number of homomorphic operations:\\(O(\\log \\lambda /\\log \\log \\lambda )\\)multiplications followed by\\(\\operatorname {poly}(\\lambda )\\)additions, where\\(\\lambda \\in \\mathbb {N}\\)is a security parameter. These schemes have compact ciphertexts: before and after homomorphic evaluation, the bit length of each ciphertext is a fixed polynomial in the security parameter\\(\\lambda \\), independent of the number of homomorphic operations that the scheme supports. This gives the first constructions of somewhat homomorphic encryption that can evaluate the class of bounded-degree polynomials without relying on lattice assumptions or bilinear maps. Our new encryption schemes are conceptually simple: much as in Gentry, Sahai, and Waters’ fully homomorphic encryption scheme, ciphertexts in our scheme are matrices, homomorphic addition is matrix addition, and homomorphic multiplication is matrix multiplication. Moreover, when encrypting many messages at once and performing many homomorphic evaluations at once, the bit length of the ciphertexts in (some of) our schemes can be made arbitrarily close to the bit length of the plaintexts. The main limitation of our schemes is that they require a large evaluation key, whose size scales with the complexity of the homomorphic computation performed, though this key can be re-used across any polynomial number of encryptions and evaluations.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_1"}, {"primary_key": "161327", "vector": [], "sparse_vector": [], "title": "Multi-Key Homomorphic Secret Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Homomorphic secret sharing (HSS) is a distributed analogue of fully homomorphic encryption (FHE) where following an input-sharing phase, two or more parties can locally compute a function over their private inputs to obtain shares of the function output. Over the last decade, HSS schemes have been constructed from an array of different assumptions. However, all existing HSS schemes, except ones based on assumptions known to imply multi-key FHE, require a public-key infrastructure (PKI) or a correlated setup between parties. This limitation carries over to many applications of HSS. In this work, we constructmulti-keyhomomorphic secret sharing (MKHSS), where given only a common reference string (CRS), two parties can secret share their inputs to each other and then perform local computations as in HSS, eliminating the need for PKI or a correlated setup. Specifically, we present the first MKHSS schemes supporting all\\(\\textsf{NC}^1\\)computations from either the decisional Diffie–<PERSON>man (DDH) assumption, the decisional composite residuosity (DCR) assumption, or DDH-like assumptions in class group. Our constructions imply the following applications in the CRS model: Succinct two-round secure computation.Under the same assumptions as our MKHSS schemes, we construct a succinct, two-round, two-party secure computation protocol for\\(\\textsf{NC}^1\\)circuits. Attribute-based NIKE.Under DCR or class group assumptions, we construct non-interactive key exchange (NIKE) protocols where two parties agree on a key if and only if their secret attributes satisfy a public\\(\\textsf{NC}^1\\)predicate. Public-key PCFs.Under DCR or class group assumptions, we construct public-key pseudorandom correlation functions (PCFs) for any\\(\\textsf{NC}^1\\)correlation. This yields the first public-key PCFs for Beaver triples (and more) from non-lattice assumptions. Silent MPC.Under DCR or class group assumptions, we construct ap-party secure computation protocol in the silent preprocessing model where the preprocessing phase has communication\\(O({p})\\), ignoring polynomial factors. All prior protocols that do not rely on multi-key FHE techniques require\\(\\varOmega (p^2)\\)communication.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_1"}, {"primary_key": "161328", "vector": [], "sparse_vector": [], "title": "Breaking the 1/λ-Rate Barrier for Arithmetic Garbling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Garbled circuits, introduced in the seminal work of <PERSON> (FOCS, 1986), have received considerable attention in the boolean setting due to their efficiency and application to round-efficient secure computation. In contrast, arithmetic garbling schemes have received much less scrutiny. The main efficiency measure of garbling schemes is their rate, defined as the bit size of each gate’s output divided by the size of the (amortized) garbled gate. Despite recent progress, state-of-the-art garbling schemes for arithmetic circuits suffer from important limitations: all existing schemes are either restricted toB-bounded integer arithmetic circuits (a computational model where the arithmetic is performed over\\(\\mathbb {Z}\\)and correctness is only guaranteed if no intermediate computation exceeds the boundB) and achieve constant rate only for very large bounds\\(B = 2^{\\varOmega (\\lambda ^3)}\\), or have a rate at most\\(O(1/\\lambda )\\)otherwise, where\\(\\lambda \\)denotes a security parameter. In this work, we improve this state of affairs in both settings. As our main contribution, we introduce the first arithmetic garbling scheme over modular rings\\(\\mathbb {Z}_B\\)with rate\\(O(\\log \\lambda /\\lambda )\\), breaking for the first time the\\(1/\\lambda \\)-rate barrier for modular arithmetic garbling. Our construction relies on the power-DDH assumption. As a secondary contribution, we introduce a new arithmetic garbling scheme forB-bounded integer arithmetic that achieves a constant rate for boundsBas low as\\(2^{O(\\lambda )}\\). Our construction relies on a new non-standard KDM-security assumption on Paillier encryption with small exponents.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_7"}, {"primary_key": "161329", "vector": [], "sparse_vector": [], "title": "Enhanced Trapdoor Hashing from DDH and DCR.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Pu"], "summary": "We introduce improved constructions of trapdoor hash (TDH) schemes under either DDH or DCR. Compared with the original construction of (Döttlinget al., Crypto 2019), our new schemes are more expressive and feature more compact encoding keys. Expressivity:Our TDH scheme allows computing arbitrary functions of the form\\(f(x,y) = \\sum _i f_i(x)\\cdot g_i(y)\\), where\\(f_i, g_i\\)are logarithmic-depth functions. This improves over the original construction that was restricted to computing the inner product betweenxandy. Compactness:Our TDH scheme has encoding keys of length\\(|y|\\cdot (1+o(1))\\), shaving an\\(\\varOmega (\\lambda )\\)factor compared to the original construction. Equipped with our new scheme, we revisit numerous applications of TDH and construct various low-communication cryptographic primitives that improve over the state of the art, including: Rate-1 batch OT with semi-honest statistical sender privacy from DDH. Previously, it was only known under DDH+LPN (even without semi-honest statistical sender privacy). As a consequence of our rate-1 batch OT, we also obtain rate-1 lossy trapdoor functions with public keys of sizeo(n) from DDH. Optimal preprocessing PIR from DCR, where after a single broadcast ofo(n) bits, a server with a size-ndatabase and a client can execute any number of PIR queries adaptively with fully optimal communication (upload communication exactly\\(\\log n\\), download communication exactly 1). Previously, such communication features were not known, even under strong cryptographic assumptions. Rate-1/2 PSI and fuzzy PSI from DCR, where after a single broadcast ofo(n) bits, a server with a size-ndatabase and a client can execute any number of (fuzzy) membership queries with upload and download communication exactly\\(\\log n\\). Previously, such communication features were not known, even under strong cryptographic assumptions. Secure 2-party computation of layered circuit with one-sided statistical security and communication sublinear in both the circuit size and the largest input, from DCR. Previously, similar results were only known from FHE.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_5"}, {"primary_key": "161330", "vector": [], "sparse_vector": [], "title": "Distributed Randomness Using Weighted VUFs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Shared randomness in blockchain can expand its support for randomized applications and can also help strengthen its security. Many existing blockchains rely on external randomness beacons for shared randomness, but this approach reduces fault tolerance, increases latency, and complicates application development. An alternate approach is to let the blockchain validators generate fresh shared randomness themselves once for every block. We refer to such a design as theon-chainrandomness. In this paper, we design an efficient on-chain randomness protocol for Byzantine fault-tolerance based Proof-of-Stake blockchains with weighted validators. A key component of our protocol is a weighted verifiable unpredictable function (VUF). The notable feature of our weighted VUF is that the computation and communication costs of parties are independent of their weight. This is crucial for scalability of on-chain randomness where we repeatedly evaluate the weighted VUF in quick succession. We also design a new scalable publicly verifiable secret sharing (PVSS) scheme with aggregatable transcript and use it to design a distributed key generation (DKG) protocol for our VUF. We implemented our schemes on top of Aptos, a proof-of-stake blockchain deployed in production, conducted an end-to-end evaluation with 112 validators and a total weight of up to 4053. In this setup, our on-chain randomness protocol adds only 133 milliseconds of latency compared to a protocol without randomness. We also demonstrate the performance improvements of our design through rigorous comparison with baseline methods.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_12"}, {"primary_key": "161331", "vector": [], "sparse_vector": [], "title": "Asymptotically Optimal Early Termination for Dishonest Majority Broadcast.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deterministic broadcast protocols amongnparties toleratingtcorruptions require\\(\\min \\{f+2, t+1\\}\\)rounds, where\\(f \\le t\\)is the actual number of corruptions in an execution of the protocol. We provide the first protocol which is optimally resilient, adaptively secure, and asymptotically matches this lower bound for any\\(t<(1-\\varepsilon )n\\). By contrast, the best known algorithm in this regime by Loss and Nielsen (EUROCRYPT’24) always requires\\(O(\\min \\{f^2, t\\})\\)rounds. Our main technical tool is a generalization of the notion of polarizer introduced by Loss and Nielsen, which allows parties to obtain transferable cryptographic evidence of missing messages with fewer rounds of interaction.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_5"}, {"primary_key": "161332", "vector": [], "sparse_vector": [], "title": "Succinct Arguments over Towers of Binary Fields.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce an efficient SNARK fortowers of binary fields. Adapting Brakedown (CRYPTO ’23), we construct a multilinear polynomial commitment scheme suitable for polynomials over tiny fields, including that with just two elements. Our commitment scheme, unlike those of previous works, treats small-field polynomials with noembedding overhead. We further introduce binary-field adaptations of HyperPlonk (EUROCRYPT ’23)’s product and permutation checks and of Lasso (EUROCRYPT ’24)’s lookup. Our binary PLONKish variant captures standard hash functions—like Keccak-256 and Grøstl—extremely efficiently. With recourse to thorough performance benchmarks, we argue that our scheme can efficiently generate precisely those Keccak-256-proofs which critically underlie modern efforts to scale Ethereum.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_4"}, {"primary_key": "161333", "vector": [], "sparse_vector": [], "title": "TinyLabels: How to Compress Garbled Circuit Input Labels, Efficiently.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Garbled circuits are a foundational primitive in both theory and practice of cryptography. Given\\((\\widehat{C}, \\textbf{K}[\\textbf{x}])\\), where\\(\\widehat{C}\\)is the garbling of a circuitCand\\(\\textbf{K}[\\textbf{x}] = \\{\\textbf{K}[i, x_i]\\}_{i \\in [|\\textbf{x}|]}\\)are the input labels for an input\\(\\textbf{x}\\), anyone can recover\\(C(\\textbf{x})\\), but nothing else about input\\(\\textbf{x}\\). Most research efforts focus on minimizing the size of the garbled circuit\\(\\widehat{C}\\). In contrast, the work by <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON> (CRYPTO ’13) initiated the study of minimizing the cost for transferring the input labels\\(\\textbf{K}[\\textbf{x}]\\). Later improved in a follow-up by <PERSON><PERSON> et al. (STOC ’23), the state-of-the-art techniques allow compressing the input labels to the optimal rate of\\(1 + o(1)\\). That is, each input label can be transferred by essentially sending 1 bit. However, existing solutions are computationally expensive, requiring large numbers of public-key operations (such as RSA exponentiation). In this work, we present anefficientinput label compression technique based on Ring-LWE. We achieve the same optimal rate of\\(1 + o(1)\\), by making use of additional communication in an offline stage (before the input\\(\\textbf{x}\\)becomes known), a paradigm that has already been explored in prior works. A novel feature of the offline communication in our scheme is that the information sent is eitherreusableorcompressibleusing a random oracle, leading to small amortized offline cost\\(o(|\\textbf{x}|)\\). We further demonstrate concrete efficiency through an implementation whose online latency outperforms the naive baseline (which sends all of\\(\\textbf{K}[\\textbf{x}]\\)in the online phase) in a realistic network with a bandwidth of up to 45Mbps. This break-even point could be pushed even further by leveraging the large potential for parallelization of computation. Finally, we apply our techniques to construct maliciously-secure two-party computation protocols withsuccinct online communication: The online phase starts once the circuitCbecomes known, and requires exchanging only\\(\\textrm{poly}(\\lambda )\\)bits (independent of |C|). After inputs\\(\\textbf{x}_A, \\textbf{x}_B\\)arrive, an additional\\(|\\textbf{x}_A|+|\\textbf{x}_B|+\\textrm{poly}(\\lambda )\\)bits need to be sent.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_9"}, {"primary_key": "161334", "vector": [], "sparse_vector": [], "title": "Combining Outputs of a Random Permutation: New Constructions and Tight Security Bounds by Fourier Analysis.", "authors": ["<PERSON><PERSON>"], "summary": "We consider constructions that combine outputs of a single permutation\\(\\pi :\\{0,1\\}^n \\rightarrow \\{0,1\\}^n\\)using a public function. These are popular constructions for achieving security beyond the birthday bound when implementing a pseudorandom function using a block cipher (i.e., a pseudorandom permutation). One of the best-known constructions (denoted SXoP[2,n]) XORs the outputs of 2 domain-separated calls to\\(\\pi \\). Modeling\\(\\pi \\)as a uniformly chosen permutation, several previous works proved a tight information-theoretic indistinguishability bound for SXoP[2,n] of about\\(q/2^{n}\\), whereqis the number of queries. However, tight bounds are unknown for the generalized variant (denoted SXoP[r,n]) which XORs the outputs of\\(r \\ge 2\\)domain-separated calls to a uniform permutation. In this paper, we obtain two results. Our first result improves the known bounds for SXoP[r,n] for all (constant)\\(r \\ge 3\\)(assuming\\(q \\le O(2^n/r)\\)is not too large) in both the single-user and multi-user settings. In particular, for\\(r=3\\), our bound is about\\(\\sqrt{u}q_{\\max }/2^{2.5n}\\)(whereuis the number of users and\\(q_{\\max }\\)is the maximal number of queries per user), improving the best-known previous result by a factor of at least\\(2^n\\). For oddr, our bounds are tight for\\(q > 2^{n/2}\\), as they match known attacks. For evenr, we prove that our single-user bounds are tight by providing matching attacks. Our second and main result is divided into two parts. First, we devise a family of constructions that outputnbits by efficiently combining outputs of 2 calls to a permutation on\\(\\{0,1\\}^n\\), and achieve multi-user security of about\\(\\sqrt{u} q_{\\max }/2^{1.5n}\\). Then, inspired by the CENC construction of Iwata [FSE’06], we further extend this family to output 2nbits by efficiently combining outputs of 3 calls to a permutation on\\(\\{0,1\\}^n\\). The extended construction has similar multi-user security of\\(\\sqrt{u} q_{\\max }/2^{1.5n}\\). The new single-user (\\(u=1\\)) bounds of\\(q/2^{1.5n}\\)for both families should be contrasted with the previously best-known bounds of\\(q/2^n\\), obtained by the comparable constructions of SXoP[2,n] and CENC. All of our bounds are proved by Fourier analysis, extending the provable security toolkit in this domain in multiple ways.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_9"}, {"primary_key": "161335", "vector": [], "sparse_vector": [], "title": "Anamorphism Beyond One-to-One Messaging: Public-Key with Anamorphic Broadcast Mode.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To date, Anamorphic Cryptography [EC22] has been developed to support adding ananamorphicmessage within a ciphertext carrying a primary message. The anamorphic message remains hidden even in the presence of a strong adversary that possesses the receiver’s key and/or determined the sent primary message. In this paper, we expand one-to-one encrypted anamorphic communication to one-to-many anamorphism, naturally assuming communication over a broadcast channel. What we show is that using a previously designed public-key encryption scheme, two things can happen: First, the receiver of an added hidden message may be a party different from the actual receiver (i.e., a shadow party) who has initially collaborated with the sender. Secondly, and perhaps more surprisingly, the receiving party need not be a singleton, and can be a number of different shadow (i.e., anonymous) groups, each receiving a different anamorphic message, where all these messages are extracted from a single one-receiver ciphertext. The idea of having multiple hidden channels to different shadow groups is highly handy if, for example, the anamorphic messages are warnings with operational instructions, sent to the groups and will be received by a group even if the adversary is able to temporarily cut off all but one members of a channel. More specifically, First, we motivate and formalize the notion ofPublic-Key Encryption with an Anamorphic Broadcast Mode. We then present, as an initial result of an independent interest, the first lattice-based construction ofAnonymous Multi-Channel Broadcast Encryption. It is important to note here that all Multi-Channel Broadcast schemes to date are in the pairing-based setting (and are, thus, insecure against quantum adversaries). Finally, we show how to transform a strong form of anonymity (where the ciphertext also hides the number of channels) into a system with anamorphism in the multi-channel broadcast setting for the well-known Dual Regev Public-Key Encryption scheme. Specifically, we show that, given the public key\\(\\textsf{pk}\\)for the Dual Regev encryption scheme, and a sequence of\\(\\ell \\)messages for the\\(\\ell \\)channels of broadcast scheme, it is possible to create a ciphertext that will carry the\\(\\ell \\)messages and is also a legitimate ciphertext for\\(\\textsf{pk}\\).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_15"}, {"primary_key": "161336", "vector": [], "sparse_vector": [], "title": "Efficient Instances of Docked Double Decker with AES, and Application to Authenticated Encryption.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A tweakable wide blockcipher is a construction which behaves in the same way as a tweakable blockcipher, with the difference that the actual block size is flexible. Due to this feature, a tweakable wide blockcipher can be directly used as a strong encryption scheme that provides full diffusion when encrypting plaintexts to ciphertexts and vice versa. Furthermore, it can be the basis of authenticated encryption schemes fulfilling the strongest security notions. In this paper, we present three instantiations of the docked double decker tweakable wide blockcipher:\\( ddd \\text {-} AES \\),\\( ddd \\text {-} AES ^+\\), and\\( bbb \\text {-} ddd \\text {-} AES \\). These instances exclusively use similar building blocks as AES-GCM (AES and finite field multiplication), are designed for maximal parallelism, and hence, can make efficient use of existing hardware accelerators.\\( ddd \\text {-} AES \\)is a birthday bound secure scheme, and\\( ddd \\text {-} AES ^+\\)is an immediate generalization to allow for variable length tweaks.\\( bbb \\text {-} ddd \\text {-} AES \\)achieves security beyond the birthday bound provided that the same tweak is not used too often. Moreover,\\( bbb \\text {-} ddd \\text {-} AES \\)builds upon a novel conditionally beyond birthday bound secure pseudorandom function, a tweakable variant of the XOR of permutations, facilitating in the need to include a tweak in the AES evaluations without sacrificing flexibility in docked double decker. We furthermore introduce an authenticated encryption mode\\( aaa \\)specifically tailored to be instantiated with\\( ddd \\text {-} AES \\)and\\( bbb \\text {-} ddd \\text {-} AES \\), where special attention is given to how the nonce and associated data can be processed. We prove that this mode is secure in the nonce-respecting setting, in the nonce-misuse setting, as well as in the setting where random nonces are used. We finally present a comparison with other tweakable wide blockciphers, give a high-level idea of the efficiency potential of our schemes, and provide benchmarks that confirm this idea.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_3"}, {"primary_key": "161337", "vector": [], "sparse_vector": [], "title": "Random Oracle Combiners: Merkle-Damgård Style.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "ARandom Oracle Combiner(ROC), introduced by <PERSON><PERSON> et al. (CRYPTO ’22), takes two hash functions\\(h_1,h_2\\)frommbits tonbits and outputs a new hash functionCfrom\\(m'\\)to\\(n'\\)bits. This functionCis guaranteed to be indifferentiable from a fresh random oracle as long as one of\\(h_1\\)and\\(h_2\\)(say,\\(h_1\\)) is a random oracle, while the other\\(h_2\\)can “arbitrarily depend” on\\(h_1\\). The work of <PERSON><PERSON> et al. also built the firstlength-preservingROC, where\\(n'=n\\). Unfortunately, despite this feasibility result, this construction has several deficiencies. From the practical perspective, it could not be directly applied to existing Merkle-Damgård-based hash functions, such as SHA2 or SHA3. From the theoretical perspective, it required\\(h_1\\)and\\(h_2\\)to have input length\\(m> 3\\lambda \\), where\\(\\lambda \\)is the security parameter. To overcome these limitations, <PERSON><PERSON> et al. conjectured—and left as the main open question—that the following (salted) construction is a length-preserving ROC: where\\(\\mathcal {Z}_1, \\mathcal {Z}_2\\)are random salts of appropriate length, and\\(f^*\\)denotes the Merkle-Damgård-extension of a given compression functionf. As our main result, weresolve this conjecture in the affirmative. For practical use, this makes the resulting combiner applicable to existing, Merkle-Damgård-based hash functions. On the theory side, it shows the existence of ROCs only requiring optimal input length\\(m=\\lambda +O(1)\\).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_10"}, {"primary_key": "161338", "vector": [], "sparse_vector": [], "title": "Triple Ratchet: A Bandwidth Efficient Hybrid-Secure Signal Protocol.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Secure Messaging apps have seen growing adoption, and are used by billions of people daily. However, due to imminent threat of a “Harvest Now, Decrypt Later” attack, secure messaging providers must react know in order to make their protocolshybrid-secure: at least as secure as before, but now also post-quantum (PQ) secure. Since many of these apps are internally based on the famous Signal’s Double-Ratchet (DR) protocol, making Signal hybrid-secure is of great importance. In fact, Signal and Apple already put in production various Signal-based variants with certain levels of hybrid security: PQXDH (only on the initial handshake), and PQ3 (on the entire protocol), by adding aPQ-ratchetto the DR protocol. Unfortunately, due to the large communication overheads of theKyberscheme used by PQ3, real-world PQ3 performs this PQ-ratchet approximately every 50 messages. As we observe, the effectiveness of this amortization, while reasonable in the best-case communication scenario, quickly deteriorates in other still realistic scenarios; causingmany consecutive(rather than 1 in 50) re-transmissions of the sameKyberpublic keys and ciphertexts (of combined size 2272 bytes!). In this work we design a new Signal-based, hybrid-secure secure messaging protocol, which significantly reduces the communication complexity of PQ3. We call our protocol “theTriple Ratchet” (TR) protocol. First, TR useserasure codesto make the communication inside the PQ-ratchet provably balanced. This results in much betterworst-casecommunication guarantees of TR, as compared to PQ3. Second, we design a novel “variant” ofKyber, called\\(\\textsf{Katana}\\), with significantly smaller combined length of ciphertext and public key (which is the relevant efficiency measure for “PQ-secure ratchets”). For 192 bits of security,\\(\\textsf{Katana}\\)improves this key efficiency measure by over 37%: from 2272 to 1416 bytes. In doing so, we identify a critical security flaw in prior suggestions to optimize communication complexity of lattice-based PQ-ratchets, and fix this flaw with a novel proof relying on the recently introduced\\(\\mathsf {hint\\text {-}MLWE}\\)assumption. During the development of this work we have been in discussion with the Signal team, and they are actively evaluating bringing a variant of it into production in a future iteration of the Signal protocol.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_11"}, {"primary_key": "161339", "vector": [], "sparse_vector": [], "title": "Efficient Multiparty Private Simultaneous Messages for Symmetric Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "APrivate Simultaneous Messages(PSM) protocol is a secure multiparty computation protocol with a minimal interaction pattern, which allows input parties sharing common randomness to securely reveal the output of a function by sending messages only once to an external party. Since existing PSM protocols for arbitrary functions have exponentially large communication complexity in the numbernof parties, it is important to explore efficient protocols by focusing on special functions of practical use. In this paper, we study the communication efficiency of PSM protocols forsymmetricfunctions, which provide many useful functionalities for real-world applications. We present a newn-party PSM protocol for symmetric functions with communication complexity\\(n^{2d/3+O(1)}\\), wheredis the size of the input domain of each party. Our protocol improves the currently best known communication complexity of\\(n^{d+O(1)}\\). As applications to other related models, we show that our novel protocol implies improved communication complexity ofad-hocPSM, where only a subset of parties actually send messages, and also leads to a more communication-efficientrobustPSM protocol, which is secure against collusion of the external party and input parties. The extension to ad-hoc PSM is not a straightforward application of the previous transformation but includes an optimization technique based on the symmetry of functions.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_9"}, {"primary_key": "161340", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of Full SCARF.", "authors": ["<PERSON>Guti<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "SCARF is a tweakable block cipher dedicated to cache address randomization, proposed at the USENIX Security conference. It has a 10-bit block, 48-bit tweak, and 240-bit key. SCARF is aggressively optimized to meet the harsh latency constraints of cache address randomization, and uses a dedicated model for its security claim. The full version of SCARF has 8 rounds, and its designers claim security up to\\(2^{40}\\)queries and\\(2^{80}\\)computations. In this work we present a distinguisher against 6-round SCARF under the collision model with time and query complexity\\(2^{30}\\), and a key-recovery attack against the full 8-round SCARF under the encryption-decryption model with\\(2^{39}\\)queries and time\\(2^{76.2}\\). As part of the attack, we present a novel method to compute the minimal number of right pairs following a differential characteristic when the input pairs are restricted to a subspace of the domain of the primitive.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_14"}, {"primary_key": "161341", "vector": [], "sparse_vector": [], "title": "Improved Cryptanalysis of ChaCha: Beating PNBs with Bit Puncturing.", "authors": ["<PERSON>Guti<PERSON>", "<PERSON><PERSON>"], "summary": "ChaCha is a widely deployed stream cipher and one of the most important symmetric primitives. Due to this practical importance, many cryptanalysis have been proposed. Until now, Probabilistic Neutral Bits (PNBs) have been the most successful. Given differential-linear distinguishers, PNBs are the technique for key recovery relying on an experimental backward correlation obtained through blackbox analysis. A careful theoretical analysis exploiting the round function design may find a better attack and improve our understanding, but the complicated nature of the ARX structure makes such analysis difficult. We propose a theoretical methodology inspired by bit puncturing, which was recently proposed at Eurocrypt 2024. Our method has a theoretical foundation and is thus fundamentally different from PNBs, to which it is the first effective alternative. As a result, we significantly improved the attack complexity for 6, 7, and 7.5-round ChaCha. The 7-round attack is about\\(2^{40}\\)times faster than the previous best. Furthermore, we propose the first 7.5-round attack with a non-negligible advantage over an exhaustive search.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_15"}, {"primary_key": "161342", "vector": [], "sparse_vector": [], "title": "Fully Homomorphic Encryption for Cyclotomic Prime Moduli.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a Generalized BFV (GBFV) fully homomorphic encryption scheme that encrypts plaintext spaces of the form\\(\\mathbb Z[x]/(\\varPhi _m(x), t(x))\\)with\\(\\varPhi _m(x)\\)them-th cyclotomic polynomial and\\(t(x)\\)an arbitrary polynomial. GBFV encompasses both BFV where\\(t(x) = p\\)is a constant, and the CLPX scheme (CT-RSA 2018) where\\(m = 2^k\\)and\\(t(x) = x-b\\)is a linear polynomial. The latter can encrypt a single huge integer modulo\\(\\varPhi _m(b)\\), has much lower noise growth than BFV, but it is not known to be efficiently bootstrappable. We show that by a clever choice ofmand higher degree polynomial\\(t(x)\\), our scheme combines the SIMD capabilities of BFV with the low noise growth of CLPX, whilst still being efficiently bootstrappable. Moreover, we present parameter families that natively accommodate packed plaintext spaces defined by a large cyclotomic prime, such as the <PERSON>rmat prime\\(\\varPhi _2(2^{16}) = 2^{16} + 1\\)and the Goldilocks prime\\(\\varPhi _6(2^{32}) = 2^{64} - 2^{32} + 1\\). These primes are often used in homomorphic encryption applications and zero-knowledge proof systems. Due to the lower noise growth, GBFV can evaluate much deeper circuits compared to native BFV in the same ring dimension. As a result, we can evaluate either larger circuits or work with smaller ring dimensions. In particular, we can natively bootstrap GBFV at 128-bit security already at ring dimension\\(n = 2^{14}\\), which was impossible before. We implemented the GBFV scheme on top of the SEAL library and achieve a latency of only 2 seconds to bootstrap a ciphertext encrypting up to 8192 elements modulo\\(2^{16}+1\\).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_13"}, {"primary_key": "161343", "vector": [], "sparse_vector": [], "title": "Post-quantum PKE from Unstructured Noisy Linear Algebraic Assumptions: Beyond LWE and Alekhnovich&apos;s LPN.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Noisy linear algebraic assumptions with respect to random matrices, in particular Learning with Erro<PERSON> (\\(\\textsf{LWE}\\)) and <PERSON><PERSON><PERSON>ovich Learning Parity with Noise (<PERSON><PERSON><PERSON><PERSON>\\(\\textsf{LPN}\\)), are among the most investigated assumptions that imply post-quantum public-key encryption (PKE). They enjoy elegant mathematical structure. Indeed, efforts to build post-quantum PKE and advanced primitives such as homomorphic encryption and indistinguishability obfuscation have increasingly focused their attention on these two assumptions and their variants. Unfortunately, this increasing reliance on these two assumptions for building post-quantum cryptography leaves us vulnerable to potential quantum (and classical) attacks on <PERSON><PERSON><PERSON><PERSON>\\(\\textsf{LPN}\\)and\\(\\textsf{LWE}\\). Quantum algorithms is a rapidly advancing area, and we must stay prepared for unexpected cryptanalytic breakthroughs. Just three decades ago, a short time frame in the development of our field, <PERSON><PERSON><PERSON>s algorithm rendered most then-popular number theoretic and algebraic assumptions quantumly broken. Furthermore, within the last several years, we have witnessed major classical and quantum breaks on several assumptions previously introduced for post-quantum cryptography. Therefore, we ask the following question: In a world where both\\(\\textsf{LWE}\\)and <PERSON><PERSON><PERSON><PERSON>\\(\\textsf{LPN}\\)are broken, can there still exist noisy linear assumptions that remain plausibly quantum hard and imply PKE? To answer this question positively, we introduce two natural noisy-linear algebraic assumptions that are both with respect to random matrices, exactly like\\(\\textsf{LWE}\\)and <PERSON><PERSON><PERSON><PERSON>\\(\\textsf{LPN}\\), but with different error distributions. Our error distribution combines aspects of both small norm and sparse error distributions. We design a PKE from these assumptions and give evidence that these assumptions are likely to still be secure even in a world where both the\\(\\textsf{LWE}\\)and Alekhnovich\\(\\textsf{LPN}\\)assumptions are simultaneously broken. We also study basic properties of these assumptions, and show that in the parameter settings we employ to build PKE, neither of them are “lattice” assumptions in the sense that we don’t see a way to attack them using a lattice closest vector problem solver, except via\\(\\textsf{NP}\\)-completeness reductions.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_3"}, {"primary_key": "161344", "vector": [], "sparse_vector": [], "title": "Pseudorandom Functions with Weak Programming Privacy and Applications to Private Information Retrieval.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although privately programmable pseudorandom functions (PPPRFs) are known to have numerous applications, so far, the only known constructions rely on Learning with Error (LWE) or indistinguishability obfuscation. We show how to construct a relaxed PPPRF with only one-way functions (OWF). The resulting PPPRF satisfies 1/poly security and works for polynomially sized input domains. Using the resulting PPPRF, we can get new results for preprocessing Private Information Retrieval (PIR) that improve the state of the art. Specifically, we show that relying only on OWF, we can get a 2-server preprocessing PIR with polylogarithmic bandwidth while consuming\\(\\widetilde{O}_\\lambda (N^{\\frac{1}{2} + \\epsilon })\\)client space and\\(N^{1+\\epsilon }\\)server space for an arbitrarily small constant\\(\\epsilon \\in (0, 1)\\). In the 1-server setting, we get a preprocessing PIR from OWF that achieves polylogarithmiconlinebandwidth and\\(\\widetilde{O}_\\lambda (N^{\\frac{1}{2} + \\epsilon })\\)offlinebandwidth, while preserving the same client and server space as before. Our result, in combination with the lower bound of <PERSON><PERSON>, <PERSON>, and <PERSON>ich<PERSON> (CRYPTO’24), establishes a tight understanding of the bandwidth and client space tradeoff for 1-server preprocessing PIR from Minicrypt assumptions. Interestingly, we are also the first to show non-trivial ways to combine client-side and server-side preprocessing to get improved results for PIR.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_11"}, {"primary_key": "161345", "vector": [], "sparse_vector": [], "title": "Disincentivize Collusion in Verifiable Secret Sharing.", "authors": ["Tiantian <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In verifiable secret sharing (VSS), adealershares a secret input among severalparties, ensuring each share is verifiable. Motivated by its applications in the blockchain space, we focus on a VSS where parties holding shares arenotallowed to reconstruct the dealer’s secret (even partially) on their own terms, which we address asprivacy-targeted collusionif attempted. In this context, our work investigates mechanisms deterring such collusion in VSS among rational and malicious parties. For this problem, we make both algorithmic and combinatorial contributions: We provide two collusion-deterrent mechanisms to discourage parties from colluding and recovering the dealer’s secret. Notably, when it is desired to achievefairness—where non-colluding parties are not at a loss—while allowing for the best achievable malicious fault tolerance, we define “trackable access structures” (TAS) and design a deterrence mechanism tailored for VSS on these structures. We estimate the size of the optimal TAS, construct them from Steiner systems, provide highly robust TAS using partial Steiner systems, and present efficient secret sharing schemes for the latter close-to-optimal TAS for various parameter regimes. We demonstrate thattrackabilityin access structures is connected to combinatorial objects like (partial) Steiner systems, uniform subsets with restricted intersections, and appropriate binary codes. Therobustnessof access structures is equivalent to the minimum vertex cover of hypergraphs. We believe these connections between cryptography, game theory, and discrete mathematics will be of broader interest.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_2"}, {"primary_key": "161346", "vector": [], "sparse_vector": [], "title": "Efficient Mixed Garbling from Homomorphic Secret Sharing and GGM-Tree.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present new techniques for garbling mixed arithmetic and boolean circuits, utilizing the homomorphic secret sharing scheme introduced by <PERSON> (Crypto 2021), along with the half-tree protocol developed by <PERSON> et al. (Eurocrypt 2023). Compared to some two-party interactive protocols, our mixed garbling only requires several times\\((<10)\\)more communication cost. We construct the bit decomposition/composition gadgets with communication cost\\(O((\\lambda +\\lambda _{\\text {DCR}}/k)b)\\)for integers in the range\\((-2^{b-1}, 2^{b-1})\\), requiring\\(O(2^k)\\)computations for the GGM-tree. Our approach is compatible with constant-rate multiplication protocols, and the cost decreases askincreases. Even for a small\\(k=8\\), the concrete efficiency ranges from\\(6\\lambda b\\)(\\(b \\ge 1000\\)bits) to\\(9\\lambda b\\)(\\(b \\sim 100\\)bits) per decomposition/composition. In addition, we develop the efficient gadgets for modqand unsigned truncation based on bit decomposition and composition. We construct efficient arithmetic gadgets over various domains. For bounded integers, we improve the multiplication rate in the work of <PERSON> et al. (TCC 2024) from\\(\\textstyle \\frac{\\zeta -2}{\\zeta +1}\\)to\\(\\frac{\\zeta -2}{\\zeta }\\). We propose new garbling schemes over other domains through bounded integers with our modular and truncation gadgets, which is more efficient than previous constructions. For\\(\\mathbb {Z}_{2^b}\\), additions and multiplication can be garbled with a communication cost comparable to our bit decomposition. For general finite field\\(\\mathbb {F}_{p^n}\\), particularly for large values ofpandn, we garble the addition and multiplication at the cost of\\(O((\\lambda +\\lambda _{\\text {DCR}}/k)b)\\), where\\(b = n\\lceil \\log p \\rceil \\). For applications to real numbers, we introduce an “error-based” truncation that makes the cost of multiplication dependent solely on the desired precision.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_6"}, {"primary_key": "161347", "vector": [], "sparse_vector": [], "title": "sfPolocolo: A ZK-Friendly Hash Function Based on S-Boxes Using Power Residues.", "authors": ["Jincheol Ha", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Seungmin Park", "Mincheol Son"], "summary": "Conventional hash functions are often inefficient in zero-knowledge proof settings, leading to design of several ZK-friendly hash functions. On the other hand, lookup arguments have recently been incorporated into zero-knowledge protocols, allowing for more efficient handling of “ZK-unfriendly” operations, and hence ZK-friendly hash functions based on lookup tables. In this paper, we propose a new ZK-friendly hash function, dubbed\\(\\textsf{Polocolo}\\), that employs an S-box constructed using power residues. Our approach reduces the numbers of gates required for table lookups, in particular, when combined with Plonk, allowing one to use such nonlinear layers over multiple rounds. We also propose a new MDS matrix for the linear layer of\\(\\textsf{Polocolo}\\). In this way,\\(\\textsf{Polocolo}\\)requires fewer Plonk gates compared to the state-of-the-art ZK-friendly hash functions. For example, when\\(t = 8\\),\\(\\textsf{Polocolo}\\)requires\\(21\\%\\)less Plonk gates compared to Anemoi, which is currently the most efficient ZK-friendly hash function, wheretdenotes the size of the underlying permutation in blocks of\\(\\mathbb {F}_p\\). For\\(t = 3\\),\\(\\textsf{Polocolo}\\)requires\\(24\\%\\)less Plonk gates than Reinforced Concrete, which is one of the recent lookup-based ZK-friendly hash functions.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_11"}, {"primary_key": "161348", "vector": [], "sparse_vector": [], "title": "Non-interactive Blind Signatures from RSA Assumption and More.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Blind signatures have received increased attention from researchers and practitioners. They allow users to obtain a signature under a message without revealing it to the signer. One of the most popular applications of blind signatures is to use them as one-time tokens, where the issuing is not linkable to the redeeming phase, and the signature under a random identifier forms a valid token. This concept is the backbone of the Privacy Pass system, which uses it to identify honest but anonymous users and protect content delivery networks from botnets. Non-interactive blind signatures for random messages were introduced by <PERSON><PERSON><PERSON> (Eurocrypt’23). They allow a signer to create a pre-signature with respect to a particular public key, while the corresponding secret key can later be used to finalize the signature. This non-interaction allows for more applications than in the case of blind signatures. In particular, the author suggested using regular PKI keys as the recipient public key, allowing for a distribution of one-time tokens to users outside the system, e.g., to public keys of GitHub users, similar to airdropping of cryptocurrencies. Unfortunately, despite introducing this concept, the paper fails to provide schemes that work with keys used in the wild. We solve this open problem. We introduce a generic construction of non-interactive blind signatures that relies on <PERSON>’s garbled circuit techniques and provide particular improvements to this generic setting. We replace oblivious transfer with their non-interactive variant and show how to construct them so that the recipient’s public key, encoding the\\(\\textsf{OT}\\)choice, is a standard RSA public key (e,N). To improve the efficiency of the garbling, we show how to garble the signing algorithm of the pairing-based Pointcheval-<PERSON> (PS) signatures and the RSA-based signature scheme with efficient protocols by Camenisch and Lysyanskaya. Our technique also apply to the well-known BBS signatures. All our improvements are of independent interest and are central to our contribution.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_13"}, {"primary_key": "161349", "vector": [], "sparse_vector": [], "title": "Leap: A Fast, Lattice-Based OPRF with Application to Private Set Intersection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Oblivious pseudorandom functions (OPRFs) are an important primitive in privacy-preserving cryptographic protocols. The growing interest in OPRFs, both in theory and practice, has led to the development of numerous constructions and variations. However, most of these constructions rely on classical assumptions. Potential future quantum attacks may limit the practicality of those OPRFs for real-world applications. To close this gap, we introduceLeap, a novel OPRF based on heuristic lattice assumptions. Fundamentally,Leapbuilds upon theSpring[BBL+15] pseudorandom function (PRF), which relies on the learning with rounding assumption, and integrates techniques from multi-party computation, specifically Oblivious Transfer (OT) and Oblivious Linear Evaluation (OLE). With this combination of oblivious protocols, we construct an OPRF that evaluates in less than a millisecond on a modern computer. Efficiency-wise, our prototype implementation achieves computation times of just 11\\(\\mu \\)s for the client and 750\\(\\mu \\)s for the server, excluding some base OT preprocessing overhead. Moreover,<PERSON><PERSON>requires an online communication cost of 23 kB per evaluation, where the client only has to send around 380 bytes online. To demonstrate the practical applicability of<PERSON><PERSON><PERSON>, we present an efficient private set intersection (PSI) protocol built on top of<PERSON>eap. This application highlightsLeap’s potential for integration into various privacy-preserving applications: We can compute an unbalanced set intersection with set sizes of\\(2^{24}\\)and\\(2^{15}\\)in under a minute of online time and just over two minutes overall.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_10"}, {"primary_key": "161350", "vector": [], "sparse_vector": [], "title": "A Generic Framework for Side-Channel Attacks Against LWE-Based Cryptosystems.", "authors": ["<PERSON>", "Silvan <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Lattice-based cryptography is in the process of being standardized. Several proposals to deal with side-channel information using lattice reduction exist. However, it has been shown that algorithms based on Bayesian updating are often more favorable in practice. In this work, we definedistribution hints; a type of hint that allows modelling probabilistic information. These hints generalize most previously defined hints and the information obtained in several attacks. We define two solvers for our hints; one is based on belief propagation and the other one uses a greedy approach. We prove that the latter is a computationally less expensive approximation of the former and that previous algorithms used for specific attacks may be seen as special cases of our solvers. Thereby, we provide a systematization of previously obtained information and used algorithms in real-world side-channel attacks. In contrast to lattice-based approaches, our framework is not limited to value leakage. For example, it can deal with noisy Hamming weight leakage or partially incorrect information. Moreover, it improves upon the recovery of the secret key from approximate hints in the form they arise in real-world attacks. Our framework has several practical applications: We exemplarily show that a recent attack can be improved; we reduce the number of traces and corresponding ciphertexts and increase the noise resistance. Further, we explain how distribution hints could be applied in the context of previous attacks and outline a potential new attack.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_1"}, {"primary_key": "161351", "vector": [], "sparse_vector": [], "title": "PAKE Combiners and Efficient Post-quantum Instantiations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Much work has been done recently on developing password-authenticated key exchange (PAKE) mechanisms with post-quantum security. However, modern guidance recommends the use ofhybridschemes—schemes which rely on the combined hardness of a post-quantum assumption, e.g., Learning with Errors (LWE), and a more traditional assumption, e.g., decisional <PERSON><PERSON><PERSON>-<PERSON><PERSON>. To date, there is no known hybrid PAKE construction, let alone a general method for achieving such. In this paper, we present two efficient PAKE combiners—algorithms that take two PAKEs satisfying mild assumptions, and output a third PAKE with combined security properties—and prove these combiners secure in the Universal Composability (UC) model. Our sequential combiner, instantiated with efficient existing PAKEs such as CPace (built on <PERSON><PERSON><PERSON>-<PERSON><PERSON>-type assumptions) and CAKE (built on lattice assumptions), yields the first known hybrid PAKE.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_14"}, {"primary_key": "161352", "vector": [], "sparse_vector": [], "title": "A New Approach to Generic Lower Bounds - Classical/Quantum MDL, Quantum Factoring, and More.", "authors": ["<PERSON><PERSON>"], "summary": "This paper presents a unified way to study the limitations of the generic quantum and classical algorithms to solve cryptographic problems over algebraic structures. Our main new lower bounds for the discrete logarithm (DL) and integer factoring problems are as follows. Classical lower bounds for the multiple-instance DL (MDL) problem in the presence of the DL oracle and preprocessing in the (classical) generic group model (GGM). Quantum lower bounds for the DL and MDL problems over the composite-order group, even allowing the algorithm to construct uniform (or arbitrary) superpositions of single group elements in the quantum GGM (QGGM). Quantum lower bounds for the order-finding problem and certain factoring algorithms, including <PERSON><PERSON>’s algorithm, in the quantum generic ring model (QGRM). All lower bounds match the known algorithms, resolving many open problems suggested by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (CRYPTO’24). The central tool of the proofs is the so-called compression lemma. Using this tool, we give alternative and simple proofs for the known lower bounds. We also prove the classical lower bounds for the (basic) index calculus method in the smooth GGM (SGGM). Our use of the compression lemma may be of independent interest. Along the way, we establish new generic models to prove the lower bounds: the QGRM and SGGM, which capture quantum factoring algorithms by <PERSON><PERSON> and <PERSON><PERSON> (or slight variations) and the basic index calculus, respectively. Thus, our lower bounds indicate the limitations of those strategies for the DL and factoring problems.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_13"}, {"primary_key": "161353", "vector": [], "sparse_vector": [], "title": "Plinko: Single-Server PIR with Efficient Updates via Invertible PRFs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study single-server private information retrieval (PIR) where a client wishes to privately retrieve thex-th entry from a database held by a server without revealing the indexx. In our work, we focus on PIR with client pre-processing where the client may compute hints during an offline phase. The hints are then leveraged during queries to obtain sub-linear online time. We presentPlinkothat is the first single-server PIR with client pre-processing that obtains optimal trade-offs between client storage and total (client and server) query time for all parameters. Our scheme uses\\(t = \\tilde{O}(n/r)\\)query time for any client storage sizer. This matches known lower bounds of\\(r \\cdot t = \\varOmega (n)\\)up to logarithmic factors for all parameterizations whereas prior works could only match the lower bound when\\(r = \\tilde{O}(\\sqrt{n})\\). Moreover, Plinko is also the firstupdateablePIR scheme where an entry can be updated in worst-case\\(\\tilde{O}(1)\\)time. As our main technical tool, we define the notion of aninvertible pseudorandom function(iPRF) that generalizes standard PRFs to be equipped with an efficient inversion algorithm. We present a construction of an iPRF from one-way functions where forward evaluation runs in\\(\\tilde{O}(1)\\)time and inversion runs in time linear in the inverse set (output) size. Furthermore, our iPRF construction is the first that remains efficient and secure for arbitrary domain and range sizes (including small domains and ranges). In the context of single-server PIR, we show that iPRFs may be used to construct the first hint set representation where finding a hint containing an entryxmay be done in\\(\\tilde{O}(1)\\)time.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_1"}, {"primary_key": "161354", "vector": [], "sparse_vector": [], "title": "(Un)breakable Curses - Re-encryption in the Fujisaki-Okamoto Transform.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Fuji<PERSON>-<PERSON><PERSON> transform (FO) is the go-to method for achieving chosen-ciphertext (CCA) security for post-quantum key encapsulation mechanisms (KEMs). An important step in FO is augmenting the decryption/decapsulation algorithm with a re-encryption step – the decrypted message is re-encrypted to check whether the correct encryption randomness was used. While solving a security problem (ciphertext-malleability), re-encryption has turned out to introduce side-channel vulnerabilities and is computationally expensive, which has lead designers to searching for alternatives. In this work, we perform a comprehensive study of such alternatives. We formalize a central security property, computational rigidity, and show that it is sufficient for obtaining CCA security. We present a framework for analyzing algorithms that can replace re-encryption and still achieve rigidity, and analyze existing proposals in this framework. Along the way, we pick up a novel QROM security statement for explicitly rejecting KEMs based on deterministic PKE schemes, something that so far only was possible when requiring a hard-to-ensure quantum property for the base PKE scheme.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_9"}, {"primary_key": "161355", "vector": [], "sparse_vector": [], "title": "A Generic Approach to Adaptively-Secure Broadcast Encryption in the Plain Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Broadcast encryption allows a user to encrypt a message toNrecipients with a ciphertext whose size scales sublinearly withN. The natural security notion for broadcast encryption isadaptivesecurity which allows an adversary to choose the set of recipientsafterseeing the public parameters. Achieving adaptive security in broadcast encryption is challenging, and in the plain model, the primary technique is the celebrated dual-systems approach, which can be implemented over groups with bilinear maps. Unfortunately, it has been challenging to replicate the dual-systems approach in other settings (e.g., with lattices or witness encryption). Moreover, even if we focus on pairing-based constructions, the dual-systems framework critically relies ondecisional(and source-group) assumptions. We do not have constructions of adaptively-secure broadcast encryption fromsearch(or target-group) assumptions in the plain model. <PERSON><PERSON> and Waters (EUROCRYPT 2009) described a compiler that takes anysemi-statically-securebroadcast encryption scheme and transforms it into an adaptively-secure scheme in therandom oraclemodel. While semi-static security is easier to achieve and constructions are known from witness encryption as well as search (and target-group) assumptions on pairing groups, the transformed scheme relies on random oracles. In this work, we show that usingpublicly-sampleableprojective PRGs, we can achieve adaptive security in theplain model. We then show how to build publicly-sampleable projective PRGs from many standard number-theoretic assumptions (e.g., CDH, LWE, RSA). Our compiler yields the first adaptively-secure broadcast encryption scheme fromsearchassumptions as well as the first such scheme from witness encryption in the plain model. We also obtain the first adaptively-secure pairing-based scheme in the plain model with\\(O_\\lambda (N)\\)-size public keys and\\(O_\\lambda (1)\\)-size ciphertexts (where\\(O_\\lambda (\\cdot )\\)suppresses polynomial factors in the security parameter\\(\\lambda \\)). Previous adaptively-secure pairing-based schemes in the plain model with\\(O_\\lambda (1)\\)-size ciphertexts required\\(O_\\lambda (N^2)\\)-size public keys.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_12"}, {"primary_key": "161356", "vector": [], "sparse_vector": [], "title": "Constructing Quantum Implementations with the Minimal T-depth or Minimal Width and Their Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Dongdai Lin"], "summary": "With the rapid development of quantum computers, optimizing the quantum implementations of symmetric-key ciphers, which constitute the primary components of the quantum oracles used in quantum attacks based on <PERSON><PERSON> and <PERSON>’s algorithms, has become an active topic in the cryptography community. In this field, a challenge is to construct quantum circuits that require the least amount of quantum resources. In this work, we aim to address the problem of constructing quantum circuits with the minimalT-depth or width (number of qubits) for nonlinear components, thereby enabling implementations of symmetric-key ciphers with the minimalT-depth or width. Specifically, we propose several general methods for obtaining quantum implementation of generic vectorial Boolean functions and multiplicative inversions in\\({\\mathbb {F}}_{2^n}\\), achieving the minimalT-depth and low costs across other metrics. As an application, we present a highly compactT-depth-3 Clifford+Tcircuit for the AES S-box. Compared to theT-depth-3 circuits presented in previous works (ASIACRYPT 2022, IEEE TC 2024), our circuit has significant reductions inT-count, full depth and Clifford gate count. Compared to the state-of-the-artT-depth-4 circuits, our circuit not only achieves the minimalT-depth but also exhibits reduced full depth and closely comparable width. This leads to lower costs for the DW-cost and T-DW-cost. Additionally, we propose two methods for constructing minimal-width implementations of vectorial Boolean functions. As applications, for the first time, we present a 9-qubit Clifford+Tcircuit for the AES S-box, a 16-qubit Clifford+Tcircuit for a pair of AES S-boxes, and a 5-qubit Clifford+Tcircuit for the\\(\\chi \\)function of SHA3. These circuits can be used to derive quantum circuits that implement AES or SHA3 without ancilla qubits.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_6"}, {"primary_key": "161357", "vector": [], "sparse_vector": [], "title": "Query-Reusable Proof Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Probabilistic proof systems such as PCPs and their zero-knowledge variants (ZK-PCPs) are central building blocks in cryptographic applications. In this work, we studyquery-reusableproof systems where the verifier can sample its queries once and use them to verify any polynomial number of proofs. In this reusable setting, soundness should still hold even if the prover can learn the verifier’s decision (accept or reject) on many badly formed proofs. Our study is motivated by attractive features of designated-verifier NIZK systems that combine a query-reusable (honest-verifier) ZK-PCP with symmetric encryption. The reusability of ZK-PCP was studied by <PERSON> et al. (Crypto 2019), who obtained a limited negative result for ZK-PCP with a special simulator. This left the question open for unrestricted ZK-PCP. We essentially settle this question by showing a negative result for statistical ZK-PCP (alternatively, PCP with sublinear query complexity) under standard complexity theoretic assumptions. We complement this with a positive result, showing that ifeithersoundness or ZK are computational, query-reusable ZK-PCPs thatdo notmeet the special simulation requirement of <PERSON> et al. follow from standard cryptographic assumptions. Finally, we study the relaxed notion ofboundedquery reusability, where the prover is allowed to interact with the verifier over a bounded number of epochs by issuing a batch of polynomially many proofs in each epoch and learning the verifier’s decisions. We obtain a nearly tight characterization of the number of queries required forr-epoch reusability.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_12"}, {"primary_key": "161358", "vector": [], "sparse_vector": [], "title": "Zero-Knowledge RAM: Doubly Efficient and Black-Box.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of verifying the computations of a RAM program on a committed database\\(x\\in \\{0,1\\}^n\\). A recent work by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (Crypto 2023) obtained adoubly efficientsolution, in which the communication and verifier’s work are polylogarithmic innand the prover’s work is comparable to the (possibly sublinear) running time of the RAM program. This only makes ablack-boxuse of a collision-resistant hash function, or alternatively can be implemented unconditionally and non-interactively in the random oracle model. In the current work, we extend this prior work by providing an additionalzero-knowledgeguarantee and by supportingdatabase updates. This gives the first doubly efficient zero-knowledge implementation of RAM programs that makes a black-box use of symmetric cryptography. While the extra zero knowledge and updatable database features of our solution are orthogonal in scope, our means for achieving them are technically related: to verify with zero knowledge many computations on the same database, we rely on a database refreshing procedure that we also use to accommodate updates.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_13"}, {"primary_key": "161359", "vector": [], "sparse_vector": [], "title": "Analyzing Group Chat Encryption in MLS, Session, Signal, and Matrix.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We analyze the composition of symmetric encryption and digital signatures in secure group messaging protocols where group members share a symmetric encryption key. In particular, we analyze the chat encryption algorithms underlying MLS, Session, Signal, and Matrix using the formalism of symmetric signcryption introduced by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> (Eurocrypt 2024). We identify theoretical attacks against each of the constructions we analyze that result from the insufficient binding between the symmetric encryption scheme and the digital signature scheme. In the case of MLS and Session, these translate into practically exploitable replay attacks by a group-insider. For Signal this leads to a forgery attack by a group-outsider with access to a user’s signing key, an attack previously discovered by <PERSON><PERSON>bás, Collins, and Gajland (Asiacrypt 2023). In Matrix there are mitigations in the broader ecosystem that prevent exploitation. We provide formal security theorems that each of the four constructions are secure up to these attacks.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_10"}, {"primary_key": "161360", "vector": [], "sparse_vector": [], "title": "Under What Conditions Is Encrypted Key Exchange Actually Secure?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A Password-Authenticated Key Exchange (PAKE) protocol allows two parties to agree upon a cryptographic key, in the setting where the only secret shared in advance is a low-entropy password. The standard security notion for PAKE is in the Universal Composability (UC) framework. In recent years there have been a large number of works analyzing the UC-security of Encrypted Key Exchange (EKE), the very first PAKE protocol, and its One-encryption variant (OEKE), both of which compile an unauthenticated Key Agreement (KA) protocol into a PAKE. In this work, we present a comprehensive and thorough study of the UC-security of both EKE and OEKE in themost generalsetting and using themost efficientbuilding blocks: We show that among the five existing results on the UC-security of (O)EKE using a general KA protocol, all are incorrect; We show that for (O)EKE to be UC-secure, the underlying KA protocol needs to satisfy several additional security properties: though some of these are closely related to existing security properties, some are new, and all are missing from existing works on (O)EKE; We give UC-security proofs for EKE and OEKE using Programmable-Once Public Function (POPF), which is the most efficient instantiation to date and is around 4 times faster than the standard instantiation using Ideal Cipher (IC). Our results in particular allow for PAKE constructions from post-quantum KA protocols such as Kyber. We also present a security analysis of POPF using a new, weakened notion ofalmost UCrealizing a functionality, that is still sufficient for proving composed protocols to be fully UC-secure.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_16"}, {"primary_key": "161361", "vector": [], "sparse_vector": [], "title": "Re-randomize and Extract: A Novel Commitment Construction Framework Based on Group Actions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tang Gang", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cryptographic group actions have attracted growing attention as a useful tool for constructing cryptographic schemes. Among their applications, commitment schemes are particularly interesting as fundamental primitives, playing a crucial role in protocols such as zero-knowledge proofs, multi-party computation, and more. In this paper, we introduce a novel framework to construct commitment schemes based on cryptographic group actions. Specifically, we propose two key techniques for general group actions: re-randomization and randomness extraction. Roughly speaking, a re-randomization algorithm introduces randomness within an orbit for any input element, while a randomness extractor maps this randomness to uniformity over the message space. We demonstrate that these techniques can significantly facilitate the construction of commitment schemes, providing a flexible framework for constructing either perfectly hiding or perfectly binding commitments, depending on the type of extractor involved. Moreover, we extend our framework to support the construction of commitments with additional desirable properties beyond hiding and binding, such as dual-mode commitments and enhanced linkable commitments. These extensions are achieved by further adapting the extractor to satisfy trapdoor or homomorphic properties. Finally, we instantiate all our proposed commitment schemes using lattices, specifically leveraging the lattice isomorphism problem (LIP) and the lattice automorphism problem (LAP) as underlying cryptographic assumptions. To the best of our knowledge, this is the first commitment scheme construction based on LIP/LAP. Additionally, we use LIP to provide a repair and improvement to the tensor isomorphism-based non-interactive commitment scheme proposed by <PERSON><PERSON>, <PERSON><PERSON>ini, and <PERSON><PERSON><PERSON> (ASIACRYPT2023), which was recently shown to be insecure by an attack from <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> (CRYPTO2024).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_5"}, {"primary_key": "161362", "vector": [], "sparse_vector": [], "title": "Secret Sharing with Publicly Verifiable Deletion.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Certified deletion, an inherently quantum capability, allows a party holding a quantum state to prove they have deleted the information contained in that state. <PERSON><PERSON><PERSON> and <PERSON><PERSON> (Crypto 2024) recently studied certified deletion in the context of secret-sharing schemes, and showed constructions withprivatelyverifiable proofs of deletion that can be verified only by the dealer who generated the shares. We give two constructions of secret-sharing schemes withpubliclyverifiable certified deletion. Our first construction is based on quantum security of the LWE problem, and each share requires a number of qubits linear in the share size of an underlying classical secret-sharing scheme for the same access structure. Our second construction is based on a weaker assumption—the existence of quantum-secure one-way functions—but requires an asymptotically larger number of qubits.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_10"}, {"primary_key": "161363", "vector": [], "sparse_vector": [], "title": "Relaxed Vector Commitment for Shorter Signatures.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mincheol Son"], "summary": "MPC-in-the-Head (MPCitH) has recently gained traction as a foundation for post-quantum signature schemes, offering robust security without trapdoors. Despite its strong security profile, MPCitH-based schemes suffer from high computational overhead and large signature sizes, limiting their practical application. This work addresses these inefficiencies by relaxing vector commitments within MPCitH-based schemes. We introduce the concept ofvector semi-commitment, which relaxes the binding property of traditional vector commitment. Vector semi-commitment schemes may allow an adversary to find more than one preimage of a commitment. We instantiate vector semi-commitment schemes in both the random oracle model and the ideal cipher model, leveraging recent optimizations on GGM tree such as correlated GGM tree. We apply the ideal-cipher-based vector semi-commitment scheme to the BN++ signature scheme and prove it almost fully secure in the ideal cipher model. Implementing these improvements in the\\(\\textsf{AIMer}\\)v2.0 signature scheme, we achieve up to 18% shorter signatures and up to 112% faster signing and verification speeds, setting new benchmarks for MPCitH-based schemes.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_15"}, {"primary_key": "161364", "vector": [], "sparse_vector": [], "title": "A Simple Framework for Secure Key Leasing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Secure key leasing (a.k.a. key-revocable cryptography) enables us to lease a cryptographic key as a quantum state in such a way that the key can be later revoked in a verifiable manner. We propose a simple framework for constructing cryptographic primitives with secure key leasing via the certified deletion property of BB84 states. Based on our framework, we obtain the following schemes. A public key encryption scheme with secure key leasing that has classical revocation based on any IND-CPA secure public key encryption scheme. Prior works rely on either quantum revocation or stronger assumptions such as the quantum hardness of the learning with errors (LWE) problem. A pseudorandom function with secure key leasing that has classical revocation based on one-way functions. Prior works rely on stronger assumptions such as the quantum hardness of the LWE problem. A digital signature scheme with secure key leasing that has classical revocation based on the quantum hardness of the short integer solution (SIS) problem. Our construction has static signing keys, i.e., the state of a signing key almost does not change before and after signing. Prior constructions either rely on non-static signing keys or indistinguishability obfuscation to achieve a stronger goal of copy-protection. In addition, all of our schemes remain secure even if a verification key for revocation is leaked after the adversary submits a valid certificate of deletion. To our knowledge, all prior constructions are totally broken in this setting. Moreover, in our view, our security proofs are much simpler than those for existing schemes.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_8"}, {"primary_key": "161365", "vector": [], "sparse_vector": [], "title": "Stronger Security for Threshold Blind Signatures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Blind signatures allow a user to obtain a signature from an issuer in a privacy-preserving way: the issuer neither learns the signed message, nor can link the signature to its issuance. The threshold version of blind signatures further splits the secret key amongnissuers, and requires the user to obtain at least\\(t\\le n\\)of signature shares in order to derive the final signature. Security should then hold as long as at most\\(t-1\\)issuers are corrupt. Security for blind signatures is expressed through the notion ofone-more unforgeabilityand demands that an adversary must not be able to produce more signatures than what is considered trivial after its interactions with the honest issuer(s). While one-more unforgeability is well understood for the single-issuer setting, the situation is much less clear in the threshold case: due to the blind issuance, counting which interactions can yield a trivial signature is a challenging task. Existing works bypass that challenge by using simplified models that do not fully capture the expectations of the threshold setting. In this work, we study the security of threshold blind signatures, and propose a framework of one-more unforgeability notions where the adversary can corrupt\\(c<t\\)issuers. Our model is generic enough to capture both interactive and non-interactive protocols, and it provides a set of natural properties with increasingly stronger guarantees, giving the issuers gradually more control over how their shares can be combined. As a point of comparison, we reconsider the existing threshold blind signature models and show that their security guarantees are weaker and less clearly comprehensible than they seem. We then re-assess the security of existing threshold blind signature schemes – BLS-based and Snowblind – in our framework, and show how to lift them to provide stronger security.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_12"}, {"primary_key": "161366", "vector": [], "sparse_vector": [], "title": "Verifiable Random Function from the Deuring Correspondence and Higher Dimensional Isogenies.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper, we introduce\\(\\textsf {DeuringVUF} \\), a new Verifiable Unpredictable Function (VUF) protocol based on isogenies between supersingular curves. The most interesting application of this VUF is\\(\\textsf {DeuringVRF} \\)a post-quantum Verifiable Random Function (VRF). The main advantage of this new scheme is its compactness, with combined public key and proof size of roughly 450 bytes, which is orders of magnitude smaller than other generic purpose post-quantum VRF constructions. This scheme is also the first post-quantum VRF satisfying unconditional uniqueness. We show that this scheme is practical by providing a first non-optimized C implementation that runs in roughly 18 ms for verification and 160 ms for evaluation. Up to our knowledge, this is the fastest implementation of a post-quantum VRF. The function at the heart of our construction is the one that computes the codomain of an isogeny of big prime degree from its kernel. The evaluation can be performed efficiently with the knowledge of the endomorphism ring using a new ideal-to-isogeny algorithm introduced recently by <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON> that uses computation of dimension 2 isogenies between elliptic products to compute effectively the translation through the Deuring correspondence of any ideal. On the other hand, without the knowledge of the endomorphism ring, this computation appears to be hard. The security of our\\(\\textsf {DeuringVUF} \\)holds under a new assumption call the one-more isogeny problem (\\(\\textrm{OMIP}\\)). Another application of\\(\\textsf {DeuringVUF} \\)is the first hash-and-sign signature based on isogenies in the standard model. While we don’t expect the signature in itself to outperform the recent variants of SQIsign, it remains very competitive in both compactness and efficiency while providing a new framework to build isogeny-based signature that could lead to new interesting applications.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_7"}, {"primary_key": "161367", "vector": [], "sparse_vector": [], "title": "Efficient Pseudorandom Correlation Generators for Any Finite Field.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yizhou Yao", "<PERSON>"], "summary": "Correlated randomness lies at the core of efficient modern secure multi-party computation (MPC) protocols. Costs of generating such correlated randomness required for the MPC online phase protocol often constitute a bottleneck in the overall protocol. A recent paradigm ofpseudorandom correlation generator(PCG) initiated by <PERSON> et al. (CCS’18, Crypto’19) offers an appealing solution to this issue. In sketch, each party is given a short PCG seed, which can be locally expanded into long correlated strings, satisfying the target correlation. Among various types of correlations, there is oblivious linear evaluation (OLE), a fundamental and useful primitive for typical MPC protocols on arithmetic circuits. Towards efficient generating a great amount of OLE, and applications to MPC protocols, we establish the following results: We propose a novelprogrammablePCG construction for OLE over any field\\(\\mathbb {F}_{p}\\). ForkNOLE correlations, we require\\(O(k\\log {N})\\)communication and\\(O(k^2N\\log {N})\\)computation, wherekis an arbitrary integer\\(\\ge 2\\). Previous works either have quadratic computation (<PERSON> et al. Crypto’19), or can only support fields of size larger than 2 (<PERSON><PERSON> et al. Crypto’23). We extend the above OLE construction to provide various types of correlations for any finite field. One of the fascinating applications is an efficient PCG for two-partyauthenticated Boolean multiplication triples. ForkNauthenticated triples, we offer PCGs with seed size of\\(O(k^2\\log {N})\\)bits. To our best knowledge, such correlation has not been realized with sublinear communication and quasi-linear computation ever before. In addition, theprogrammabilityadmits efficient PCGs for multi-party Boolean triples, and thus the first efficient MPC protocol for Boolean circuits withsilentpreprocessing. In particular, we showkNm-party Boolean multiplication triples can be generated in\\(O(m^2k\\log {N})\\)-bit communication, while the state-of-the-art FOLEAGE (Asiacrypt’24) requires a broadcast channel and takes\\(mkN+O(m^2\\log {kN})\\)bits communication. Finally, we present efficient PCGs for circuit-dependent preprocessing, matrix multiplications triples, and string OTs etc. Compared to previous works, each has its own right.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_6"}, {"primary_key": "161368", "vector": [], "sparse_vector": [], "title": "Leveraging Small Message Spaces for CCA1 Security in Additively Homomorphic and BGN-Type Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We show that the smallness of message spaces can be used as a checksum allowing to hedge against CCA1 attacks in additively homomorphic encryption schemes. We first show that the additively homomorphic variant of Damgård’s Elgamal provides IND-CCA1 security under the standard DDH assumption. Earlier proofs either required non-standard assumptions or only applied to hybrid versions of <PERSON>g<PERSON><PERSON>’s Elgamal, which are not additively homomorphic. Our security proof builds on hash proof systems and exploits the fact that encrypted messages must be contained in a polynomial-size interval in order to enable decryption. With 3 group elements per ciphertext, this positions <PERSON>g<PERSON><PERSON>’s Elgamal as the most efficient/compact DDH-based additively homomorphic CCA1 cryptosystem. Under the same assumption, the best candidate so far was the lite Cramer-Shoup cryptosystem, where ciphertexts consist of 4 group elements. We extend this observation to build an IND-CCA1 variant of the Boneh-Goh-Nissim encryption scheme, which allows evaluating 2-DNF formulas on encrypted data. By computing tensor products of <PERSON>gård’s Elgamal ciphertexts, we obtain product ciphertexts consisting of 9 elements (instead of 16 elements if we were tensoring lite Cramer-Shoup ciphertexts) in the target group of a bilinear map. Using similar ideas, we also obtain a CCA1 variant of the Elgamal-Paillier cryptosystem by forcing\\(\\lambda \\)plaintext bits to be zeroes, which yields CCA1 security almost for free. In particular, the message space remains exponentially large and ciphertexts are as short as in the IND-CPA scheme. We finally adapt the technique to the Castagnos-Laguillaumie system.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_2"}, {"primary_key": "161369", "vector": [], "sparse_vector": [], "title": "Black Box Crypto is Useless for Doubly Efficient PIR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A(single server) private information retrieval (PIR)allows a client to read data from a public database held on a remote server, without revealing to the server which locations she is reading. In adoubly efficient PIR (DEPIR), the database is first preprocessed offline into a data structure, which then allows the server to answer any client query efficiently in sub-linear online time. Constructing DEPIR is a notoriously difficult problem, and this difficulty even extends to a weaker notion secret-key DEPIR (SK-DEPIR), where the database is preprocessed using secret randomness and the client is given a secret key for making queries. We currently only have constructions of SK-DEPIR from the Ring LWE assumption or from non-standard code-based assumptions. We show that the black-box use of essentially all generic cryptographic primitives (e.g., key agreement, oblivious transfer, indistinguishability obfuscation, etc.), including idealized primitives (e.g., random oracles, generic multilinear groups, virtual black-box obfuscation, etc.) is essentially useless for constructing SK-DEPIR. In particular, in any such SK-DEPIR construction, we can replace all black-box use of these primitives with just a black-box use of one-way functions. While we conjecture that SK-DEPIR cannot be constructed using black-box one-way functions alone, we are unable to show this in its full generality. However, we do show this for2-round schemes with a passive serverthat simply outputs requested locations in the preprocessed data structure, which is the format of all known schemes. Overall, this shows that the black-box use of essentially all crypto primitives is insufficient for constructing 2-round passive-server SK-DEPIR, and does not provide any benefit beyond black-box one-way functions for constructing general SK-DEPIR.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_3"}, {"primary_key": "161370", "vector": [], "sparse_vector": [], "title": "Do Not Disturb a Sleeping Falcon - Floating-Point Error Sensitivity of the Falcon Sampler and Its Consequences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Falconis one of the three postquantum signature schemes already selected by NIST for standardization. It is the most compact among them, and offers excellent efficiency and security. However, it is based on a complex algorithm for lattice discrete Gaussian sampling which presents a number of implementation challenges. In particular, it relies on (possibly emulated) floating-point arithmetic, which is often regarded as a cause for concern, and has been leveraged in, e.g., side-channel analysis. The extent to whichFalcon’s use of floating point arithmetic can cause security issues has yet to be thoroughly explored in the literature. In this paper, we contribute to filling this gap by identifying a way in whichFalcon’s lattice discrete Gaussian sampler, due to specific design choices, is singularly sensitive to floating-point errors. In the presence of small floating-point discrepancies (which can occur in various ways, including the use of the twoalmost but not quiteequivalent signing procedures “dynamic” and “tree” exposed by theFalconAPI), we find that, when called twice on the same input, theFalconsampler has a small but significant chance (on the order of once in a few thousand calls) of outputting two different lattice points with a very structured difference, that immediately reveals the secret key. This is in contrast to other lattice Gaussian sampling algorithms like <PERSON><PERSON><PERSON><PERSON>’s sampler and Pre<PERSON>’s hybrid sampler, that are stable with respect to small floating-point errors. Correctly generatedFalconsignatures include a salt that should in principle prevent the sampler to ever be called on the same input twice. In that sense, our observation has little impact on the security ofFalconsignatures per se (beyond echoing warnings about the dangers of repeated randomness). On the other hand, it is critical forderandomizedvariants ofFalcon, which have been proposed for use in numerous settings. One can mention in particular identity-based encryption, SNARK-friendly signatures, and sublinear signature aggregation. For all these settings, small floating point discrepancies have a chance of resulting in full private key exposure, even when using the slower, integer-based emulated floating-point arithmetic ofFalcon’s reference implementation.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_8"}, {"primary_key": "161371", "vector": [], "sparse_vector": [], "title": "Snake-Eye Resistant PKE from LWE for Oblivious Message Retrieval and Robust Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Oblivious message retrieval (OMR) allows resource-limited recipients to outsource the message retrieval process without revealing which messages are pertinent to which recipient. Its realizations in recent works leave an open problem: can an OMR scheme be both practical and provably secure against spamming attacks by malicious senders (i.e., DoS-resistant) under standard assumptions? In this paper, we present\\(\\mathsf {DoS\\text {-}PerfOMR}\\): a provably DoS-resistant OMR construction that is 12x faster than\\(\\textsf{OMRp2}\\)(a conjectured DoS-resistant OMR construction in prior works), and (almost) matches the performance of the state-of-the-art OMR scheme that isnotDoS-resistant (proven by the attacks we show). To achieve this, we analyze thesnake-eye resistanceproperty for general PKE schemes, i.e., whether it is hard to encrypt an identical message under two public keys. We construct a new lattice-based PKE scheme:\\(\\textsf{LWEmongrass}\\), that is provably snake-eye resistant and has better efficiency than the PVW scheme underlying\\(\\textsf{OMRp2}\\). We also show that natural candidates (e.g., RingLWE PKE) are not snake-eye resistant. Furthermore, we show that a snake-eye resistant PKE scheme implies a robust PKE scheme, thus introducing the first robust lattice-based PKE scheme without relying on the KEM-DEM paradigm, avoiding its inherent inefficiencies. Of independent interest, we introduce two variants of LWE with side information, as components towards proving the properties of\\(\\textsf{LWEmongrass}\\), and reduce standard LWE to them for the parameters of interest.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_5"}, {"primary_key": "161372", "vector": [], "sparse_vector": [], "title": "BitGC: Garbled Circuits with 1 Bit per Gate.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We presentBitGC, a garbling scheme for Boolean circuits with 1 bit per gate communication based on either ring learning with errors (RLWE) or NTRU assumption, with key-dependent message security. The garbling consists of 1) a homomorphically encrypted seed that can be expanded to encryption of many pseudo-random bits and 2) one-bit stitching information per gate to reconstruct garbled tables from the expanded ciphertexts. By using low-complexity PRGs, both the garbling and evaluation of each gate require onlyO(1) homomorphic addition/multiplication operations without bootstrapping.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_16"}, {"primary_key": "161373", "vector": [], "sparse_vector": [], "title": "Efficient Distributed Randomness Generation from Minimal Assumptions Where PArties Speak Sequentially Once.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>"], "summary": "We study efficient public randomness generation protocols in the PASSO (PArties Speak Sequentially Once) model for multi-party computation (MPC).\\(\\text {PASSO}\\)is a variation of traditional MPC wherenparties are executed in sequence and each party “speaks” only once, broadcasting and sending secret messages only to parties further down the line. Prior results in this setting include information-theoretic protocols in which the computational complexity scales exponentially with the number of corruptionst(CRYPTO 2022), as well as more efficient computationally-secure protocols either assuming a trusted setup phase or DDH (FC 2024). Moreover, these works only consider security against static adversaries. In this work, we focus on computational security againstadaptive adversariesandfrom minimal assumptions, and improve on the works mentioned above in several ways: Assuming the existence of non-interactive perfectly binding commitments, we design protocols with\\(n=3t+1\\)or\\(n=4t\\)parties that are efficient and securewhenevertis small compared to the security parameter\\(\\lambda \\)(e.g.,tis constant). This improves the resiliency of all previous protocols, even those requiring a trusted setup. It also shows that\\(n=4\\)parties are necessary and sufficient for\\(t=1\\)corruptions in the computational setting, while\\(n=5\\)parties are required for information-theoretic security. Under the same assumption, we design protocols with\\(n=4t+2\\)or\\(n=5t+2\\)parties (depending on the adversarial network model) which are efficient whenever\\(t=\\textsf{poly}(\\lambda )\\). This improves on the existing DDH-based protocol both in terms of resiliency and the underlying assumptions. We design efficient protocols with\\(n=5t+3\\)or\\(n=6t+3\\)parties (depending on the adversarial network model) assuming the existence of one-way functions. We complement these results by studying lower bounds for randomness generation protocols in the computational setting.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_7"}, {"primary_key": "161374", "vector": [], "sparse_vector": [], "title": "MiniCast: Minimizing the Communication Complexity of Reliable Broadcast.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give a new protocol for reliable broadcast with improved communication complexity for long messages. Namely, to reliably broadcast a messagemover an asynchronous network to a set ofnparties, of which fewer thann/3 may be corrupt, our protocol achieves a communication complexity of\\(1.5 |m | n + O( \\kappa n^2 \\log (n) )\\), where\\(\\kappa \\)is the output length of a collision-resistant hash function. This result improves on the previously best known bound for long messages of\\(2 |m | n + O( \\kappa n^2 \\log (n) )\\).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_4"}, {"primary_key": "161375", "vector": [], "sparse_vector": [], "title": "Multi-authority Registered Attribute-Based Encryption.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Registered attribute-based encryption (ABE) enables fine-grained access control to encrypted data without a trusted authority. In this model, users generate their own public keys andregistertheir public key along with a set of attributes with a key curator. The key curator aggregates the public keys into a short master public key that functions as the public key for an ABE scheme. A limitation of ABE (registered or centralized) is the assumption that a single entity manages all of the attributes in a system. In many settings, the attributes belong to different organizations, making it unrealistic to expect that a single entity manage all of them. In the centralized setting, this motivated the notion of multi-authority ABE, where multiple independent authorities control their individual set of attributes. Access policies are then defined over attributes across multiple authorities. In this work, we introducemulti-authority registered ABE, where multiple (independent) key curators each manage their individual sets of attributes. Users can register their public keys with any key curator, and access policies can be defined over attributes from multiple key curators. Multi-authority registered ABE combines the trustless nature of registered ABE with the decentralized nature of multi-authority ABE. We start by constructing a multi-authority registered ABE scheme from composite-order pairing groups. This scheme supports an a priori bounded number of users and access policies that can be represented by a linear secret sharing scheme (which includes monotone Boolean formulas). Our construction relies on a careful integration of ideas from pairing-based registered ABE and multi-authority ABE schemes. We also construct a multi-authority registered ABE scheme that supports an unbounded number of users and arbitrary monotone policies using indistinguishability obfuscation (and function-binding hash functions).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_1"}, {"primary_key": "161376", "vector": [], "sparse_vector": [], "title": "Hybrid Password Authentication Key Exchange in the UC Framework.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A hybrid cryptosystem combines two systems that fulfill the same cryptographic functionality, and its security enjoys the security of the harder one. There are many proposals for hybrid public-key encryption (hybrid PKE), hybrid signature (hybrid SIG) and hybrid authenticated key exchange (hybrid AKE). In this paper, we fill the blank of Hybrid Password Authentication Key Exchange (hybrid PAKE). For constructing hybrid PAKE, we first define an important class of PAKE –fullDH-type PAKE, from which we abstract sufficient properties to achieve UC security. OurfullDH-type PAKE framework unifies lots of PAKE schemes like SPAKE2, TBPEKE, (Crs)X-GA-PAKE, and summarizes their common features for UC security. Stepping from full DH-type PAKE, we propose two generic approaches to hybrid PAKE,parallel compositionandserial composition. We propose a generic construction of hybrid PAKE viaparallel compositionand prove that the hybrid PAKE by composing DH-type PAKEsin parallelis a full DH-type PAKE and hence achieves UC security, as long as one underlying DH-type PAKE is a full DH-type. We propose a generic construction of hybrid PAKE viaserial composition, and prove that the hybrid PAKE by composing a DH-type PAKE and another PAKEin serialachieves UC security, ifeitherthe DH-type PAKE is a full DH-typeorthe other PAKE has UC security and the DH-type PAKE only has some statistical properties. Our generic constructions of hybrid PAKE result in a variety of hybrid PAKE schemes enjoying different nice features, like round-optimal, high efficiency, or UC security in quantum random oracle model (QROM).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91124-8_15"}, {"primary_key": "161377", "vector": [], "sparse_vector": [], "title": "Quasi-Linear Indistinguishability Obfuscation via Mathematical Proofs of Equivalence and Applications.", "authors": ["Yaohua Ma", "<PERSON><PERSON> Dai", "<PERSON>"], "summary": "Indistinguishability obfuscation (\\({\\textsf{iO}}\\)) is a powerful cryptographic primitive and has been quoted as the “swiss army-knife of modern cryptography”. Most prior works on\\({\\textsf{iO}}\\)focused on theoretical feasibility, and paid less attention to the efficiency of the constructions. As a result, all prior constructions stopped at achieving polynomial efficiency without worrying about how large the polynomial is. In fact, it has even been conjectured that a polynomial dependence on the input length is necessary. In this work, we show that if the two circuits to be obfuscated enjoy a succinct propositional logic proof of equivalence, then we can create obfuscated versions of these programs that are computationally indistinguishable; and importantly, the obfuscated program’s efficiency is quasi-linear in the circuit size and proof size. We show that our quasi-linear\\({\\textsf{iO}}\\)construction also leads to new applications. Specifically, we show how to achieve quasi-linear efficiency for 1)\\({\\textsf{iO}}\\)for Turing Machines with unbounded inputs, and 2) multi-input functional encryption, also assuming succinct proofs of equivalence.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_6"}, {"primary_key": "161378", "vector": [], "sparse_vector": [], "title": "Physical-Bit Leakage Resilience of Linear Code-Based Secret Sharing.", "authors": ["<PERSON>"], "summary": "Physical attacks through hardware bit probing expose significant vulnerabilities in cryptographic systems. This paper investigates the leakage resilience of linear code-based secret sharing schemes, including <PERSON><PERSON><PERSON>’s secret sharing, under the threat of physical-bit leakages. Our focus is on schemes over binary extension fields, which are prevalent in practical cryptographic applications. We present the following key results: A novel dichotomy showing that every scheme is either perfectly secure or entirely insecure in the presence of any physical-bit leakage. A complete characterization of leakage resilience based on the minimal codewords in the dual code of the binary image code, providing new insights into the leakage structure. This is the first complete characterization in the context of leakage-resilient secret sharing. A Monte-Carlo construction of a variant of <PERSON><PERSON><PERSON>’s secret sharing with high leakage resilience.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_3"}, {"primary_key": "161379", "vector": [], "sparse_vector": [], "title": "On the Soundness of Algebraic Attacks Against Code-Based Assumptions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study recent algebraic attacks (Briaud-Øygarden EC’23) on the Regular Syndrome Decoding (RSD) problem and the assumptions underlying the correctness of their attacks’ complexity estimates. By relating these assumptions to interesting algebraic-combinatorial problems, we prove that they do not hold in full generality. However, we show that they are (asymptotically) true for most parameter sets, supporting the soundness of algebraic attacks on RSD. Further, we prove—without any heuristics or assumptions—that RSD can be broken in polynomial time whenever the number of error blocks times the square of the size of error blocks is larger than 2 times the square of the dimension of the code. Additionally, we use our methodology to attack a variant of the Learning With Errors problem where each error term lies in a fixed set of constant size. We prove that this problem can be broken in polynomial time, given a sufficient number of samples. This result improves on the seminal work by <PERSON><PERSON><PERSON> and <PERSON>e (ICALP’11), as the attack’s time complexity is independent of the LWE modulus.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_14"}, {"primary_key": "161380", "vector": [], "sparse_vector": [], "title": "On Algebraic Homomorphic Encryption and Its Applications to Doubly-Efficient PIR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Doubly-Efficient Private Information Retrieval (DEPIR) protocol of Lin, Mook, and Wichs (STOC’23) relies on a Homomorphic Encryption (HE) scheme that isalgebraic, i.e., whose ciphertext space has a ring structure that matches the homomorphic operations. Since modern, well-studied HE schemes are not algebraic, an important prerequisite for practical DEPIR is to find an efficient algebraic HE scheme. In this work, we study the properties of algebraic HE and try to make progress in solving this problem. We first prove a lower bound of\\(2^{\\varOmega (2^d)}\\)for the ciphertext ring size of post-quantum algebraic HE schemes (in terms of the depthdof the evaluated circuit), which demonstrates a gap between optimal algebraic HE and the existing schemes, which have a ciphertext ring size of\\(2^{O(2^{2d})}\\). As we are unable to bridge this gap directly, we instead slightly relax the notion of being algebraic. This allows us to construct a practically more efficientrelaxed-algebraicHE scheme, which indeed leads to a more efficient instantiation and implementation of DEPIR. We experimentally demonstrate run-time improvements of more than\\(4\\times \\)for benchmarked parameters and reduce memory queries by\\(23\\times \\)for larger parameters compared to prior work. Notably, our relaxed-algebraic HE scheme relies on a new variant of the Ring Learning with Errors (RLWE) problem that we call\\(\\{0, 1\\}\\)-CRT RLWE. We give a formal security reduction from standard RLWE, and estimate its concrete security. Both the\\(\\{0, 1\\}\\)-CRT RLWE problem and the techniques used for the reduction may be of independent interest.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_2"}, {"primary_key": "161381", "vector": [], "sparse_vector": [], "title": "Ciphertext-Ciphertext Matrix Multiplication: Fast for Large Matrices.", "authors": ["<PERSON>"], "summary": "Matrix multiplication of two encrypted matrices (CC-MM) is a key challenge for privacy-preserving machine learning applications. As modern machine learning models focus on scalability, fast CC-MM on large datasets is increasingly in demand. In this work, we present a CC-MM algorithm for large matrices. The algorithm consists of plaintext matrix multiplications (PP-MM) and ciphertext matrix transpose algorithms (C-MT). We propose a fast C-MT algorithm, which is computationally inexpensive compared to PP-MM. By leveraging high-performance BLAS libraries to optimize PP-MM, we implement large-scale CC-MM with substantial performance improvements. Furthermore, we propose lightweight algorithms, significantly reducing the key size from\\(1\\ 960\\)MB to 1.57 MB for CC-MM with comparable efficiency. In a single-thread implementation, the C-MT algorithm takes 0.76 s to transpose a\\(2\\ 048\\times 2\\ 048\\)encrypted matrix. The CC-MM algorithm requires 85.2 s to multiply two\\(4\\ 096\\times 4\\ 096\\)encrypted matrices. For large matrices, our algorithm outperforms the state-of-the-art CC-MM method from Jiang-Kim<PERSON><PERSON>ter-Song [CCS’18] by a factor of over 800.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_6"}, {"primary_key": "161382", "vector": [], "sparse_vector": [], "title": "Singular Points of UOV and VOX.", "authors": ["<PERSON>"], "summary": "In this work, we study the singular locus of the varieties defined by the public keys of UOV and VOX, two multivariate signature schemes submitted to the additional NIST call for post-quantum signature schemes. We give a new attack for UOV\\({\\hat{+}}\\)and VOX targeting singular points of the underlying UOV key. Our attack lowers the security of the schemes, both asymptotically and in number of gates, showing in particular that the parameter sets proposed for these schemes do not meet the NIST security requirements. More precisely, we show that the security of VOX/UOV\\({\\hat{+}}\\)was overestimated by factors\\(2^{2}, 2^{18}, 2^{37}\\)for security levels I, III, V respectively. As an essential element of the attack on VOX, we introduce a polynomial time algorithm performing a key recovery from one vector, with an implementation requiring only 15 seconds at security level V.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_11"}, {"primary_key": "161383", "vector": [], "sparse_vector": [], "title": "Hard Quantum Extrapolations in Quantum Cryptography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although one-way functions are well-established as the minimal primitive for classical cryptography, a minimal primitive for quantum cryptography is still unclear. Universal extrapolation, first considered by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> (1990), is hard if and only if one-way functions exist. Towards better understanding minimal assumptions for quantum cryptography, we study the quantum analogues of the universal extrapolation task. Specifically, we put forth the classical\\(\\rightarrow \\)quantum extrapolation task, where we ask to extrapolate the rest of a bipartite pure state given the first register measured in the computational basis. We then use it as a key component to establish new connections in quantum cryptography: (a) quantum commitments exist if classical\\(\\rightarrow \\)quantum extrapolation is hard; and (b) classical\\(\\rightarrow \\)quantum extrapolation is hard if any of the following cryptographic primitives exists: quantum public-key cryptography (such as quantum money and signatures) with a classical public key or 2-message quantum key distribution protocols. For future work, we further generalize the extrapolation task and propose a fully quantum analogue. We show that it is hard if quantum commitments exist, and it is easy for quantum polynomial space.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91098-2_3"}, {"primary_key": "161384", "vector": [], "sparse_vector": [], "title": "Unique NIZKs and Steganography Detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Non-interactive zero-knowledge (NIZK) proofs tend to be randomized and there are many possible proofs for any fixed NP statement. Can we have NIZKs with only a singleuniquevalid proof per statement? Such NIZKs are known under strong cryptographic assumptions (indistinguishability obfuscation), and are conversely known to require strong cryptographic assumptions (witness encryption). In this work, following <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (TCC ’05), we consider the following relaxed notion of unique NIZKs (UNIZKs): We only require (computationally) unique proofs for NP statements with a (computationally) unique witness; an adversary that can produce two distinct proofs must also know two distinct witnesses. We consider NIZKs with prover setup, where a potentially malicious prover initially publishes a public key\\(\\textsf{pk}\\)and keeps a corresponding secret key\\(\\textsf{sk}\\), which it uses to produce arbitrarily many NIZK proofs\\(\\pi \\)in the future. While the public key\\(\\textsf{pk}\\)is not required to be unique, once it is fixed, all the subsequent proofs\\(\\pi \\)that the prover can produce should be unique. We show thatbothof these relaxations are needed to avoid witness encryption. Prior work constructed such UNIZKs under the quadratic residuosity assumption, and it remained an open problem to do so under any other assumptions. Here, we give a new construction of UNIZKs under thelearning with errors(LWE) assumption. We also identify and fix a subtle circularity issue in the prior work. UNIZKs are a non-interactive version of steganography-free zero knowledge of Abdolmaleki et al. (TCC ’22). As an application of UNIZKs, we get a general steganography detection mechanism that can passively monitor arbitrary functionalities to detect steganographic leakage.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_2"}, {"primary_key": "161385", "vector": [], "sparse_vector": [], "title": "The syzygy Distinguisher.", "authors": ["<PERSON><PERSON>"], "summary": "We present a new distinguisher for alternant and Goppa codes, whose complexity is subexponential in the error-correcting capability, hence better than that of generic decoding algorithms. Moreover it does not suffer from the strong regime limitations of the previous distinguishers or structure recovery algorithms: in particular, it applies to the codes used in theClassic McEliececandidate for postquantum cryptography standardization. The invariants that allow us to distinguish are graded Betti numbers of the homogeneous coordinate ring of a shortening of the dual code. Since its introduction in 1978, this is the first time an analysis (we exclude results such as [28] that use an attack model for which direct countermeasures exist) of the McEliece cryptosystem breaks the exponential barrier.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_12"}, {"primary_key": "161386", "vector": [], "sparse_vector": [], "title": "MPC with Publicly Identifiable Abort from Pseudorandomness and Homomorphic Encryption.", "authors": ["<PERSON>"], "summary": "Publicly identifiable abort is a critical feature for ensuring accountability in outsourced computations using secure multiparty computation (MPC). Despite its importance, no prior work has specifically addressed identifiable abort in the context of outsourced computations. In this paper, we present the first MPC protocol that supports publicly identifiable abort with minimal overhead for external clients. Our approach minimizes client-side computation by requiring only a few pseudorandom function evaluations per input. On the server side, the verification process involves lightweight linear function evaluations using homomorphic encryption. This results in verification times of a few nanoseconds per operation for servers, with client overhead being approximately two orders of magnitude lower. Additionally, the public verifiability of our protocol reduces client input/output costs compared to SPDZ-based protocols, on which we base our protocol. For example, in secure aggregation use cases, our protocol achieves over twice the efficiency during the offline phase and up to an 18% speedup in the online phase, significantly outperforming SPDZ.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_10"}, {"primary_key": "161387", "vector": [], "sparse_vector": [], "title": "Halving Differential Additions on Kummer Lines.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study differential additions formulas on Kummer lines that factorize through a degree 2 isogeny\\(\\varphi \\). We call the resulting formulas half differential additions: from the knowledge of\\(\\varphi (P), \\varphi (Q)\\)and\\(P-Q\\), the half differential addition allows to recover\\(P+Q\\). We explain how <PERSON><PERSON>’s theta group theory allows, in any model of Kummer lines, to find a basis of the half differential relations. This involves studying the dimension 2 isogeny\\((P, Q) \\mapsto (P+Q, P-Q)\\). We then use the half differential addition formulas to build a new type of Montgomery ladder, called the half-ladder, using a time-memory trade-off. On a Montgomery curve with full rational 2-torsion, our half ladder first build a succession of isogeny images\\(P_i=\\varphi _i(P_{i-1})\\), which only depends on the base pointPand not the scalarn, for a pre-computation cost of\\(2\\textbf{S}+1\\textbf{m}_0\\)by bit. Then we use half doublings and half differential additions to compute any scalar multiplication\\(n \\cdot P\\), for a cost of\\(4\\textbf{M}+2\\textbf{S}+1\\textbf{m}_0\\)by bit. The total cost is then\\(4 \\textbf{M}+ 4 \\textbf{S}+ 2\\textbf{m}_0\\), even when the base pointPis not normalized. By contrast, the usual Montgomery ladder costs\\(4\\textbf{M}+ 4\\textbf{S}+ 1\\textbf{m}+ 1\\textbf{m}_0\\)by bit, for a normalized point. In the long version of the paper, we extend our approach to higher dimensional ladders in theta coordinates or twisted theta coordinates. In dimension 2, after a precomputation step which depends on the base pointP, our half ladder only costs\\(7\\textbf{M}+ 4\\textbf{S}+3\\textbf{m}_0\\), compared to\\(10\\textbf{M}+9\\textbf{S}+6\\textbf{m}_0\\)for the standard ladder.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_15"}, {"primary_key": "161388", "vector": [], "sparse_vector": [], "title": "Solving Multivariate Coppersmith Problems with Known <PERSON>.", "authors": ["<PERSON><PERSON>"], "summary": "We examine the problem of finding small solutions to systems of modular multivariate polynomials. While the case of univariate polynomials has been well understood since <PERSON><PERSON>’s original 1996 work, multivariate systems typically rely on carefully crafted shift polynomials and significant manual analysis of the resulting <PERSON><PERSON> lattice. In this work, we develop several algorithms that make such hand-crafted strategies obsolete. We first use the theory of <PERSON><PERSON><PERSON><PERSON> bases to develop an algorithm that provably computes an optimal set of shift polynomials, and we use lattice theory to construct a lattice which provably contains all desired short vectors. While this strategy is usable in practice, the resulting lattice often has large rank. Next, we propose a heuristic strategy based on graph optimization algorithms that quickly identifies low-rank alternatives. Third, we develop a strategy which symbolically precomputes shift polynomials, and we use the theory of polytopes to polynomially bound the running time. Like <PERSON><PERSON> and <PERSON><PERSON><PERSON>’s automated method, our precomputation strategy enables heuristically and automatically determining asymptotic bounds. We evaluate our new strategies on over a dozen previously studied Coppersmith problems. In all cases, our unified approach achieves the same recovery bounds in practice as prior work, even improving the practical bounds for three of the problems. In five problems, we find smaller and more efficient lattice constructions, and in three problems, we improve the existing asymptotic bounds. While our strategies are still heuristic, they are simple to describe, implement, and execute, and we hope that they drastically simplify the application of <PERSON><PERSON>’s method to systems of multivariate polynomials.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_13"}, {"primary_key": "161389", "vector": [], "sparse_vector": [], "title": "Binary Codes for Error Detection and Correction in a Computationally Bounded World.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study error detection and correction in a computationally bounded world, where errors are introduced by an arbitrarypolynomial-timeadversarial channel. Our focus is onseededcodes, where the encoding and decoding procedures can share a public random seed, but are otherwise deterministic. We can ask for eitherselectiveoradaptivesecurity, depending on whether the adversary can choose the message being encoded before or after seeing the seed. For large alphabets, a recent construction achieves essentially optimal rate versus error tolerance trade-offs under minimal assumptions, surpassing information-theoretic limits. However, for the binary alphabet, the only prior improvement over information theoretic codes relies on non-standard assumptions justified via the random oracle model. We show the following: Selective Security under LWE:Under the learning with errors (LWE) assumption, we construct selectively secure codes over the binary alphabet. For error detection, our codes achieve essentially optimal rate\\(R \\approx 1\\)and relative error tolerance\\(p\\approx \\frac{1}{2}\\). For error correction, they can uniquely correct\\(p< 1/4\\)relative errors with a rateRthat essentially matches that of the best list-decodable codes with error tolerance\\(p\\). Both cases provide significant improvements over information-theoretic counterparts. The construction relies on a novel form of 2-input correlation intractable hash functions that we construct from LWE. Adaptive Security via Crypto Dark Matter:Assuming the exponential security of a natural collision-resistant hash function candidate based on the “crypto dark matter” approach of mixing linear functions over different moduli, we construct adaptively secure codes over the binary alphabet, for both error detection and correction. They achieve essentially the same trade-offs between error tolerance\\(p\\)and rateRas above, with the caveat that for error-correction they only do so for sufficiently small values of\\(p\\).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_7"}, {"primary_key": "161390", "vector": [], "sparse_vector": [], "title": "Honest Majority MPC with Õ( C ) Communication in Minicrypt.", "authors": ["<PERSON><PERSON>", "Xiax<PERSON> Ye"], "summary": "In this work, we consider the communication complexity of MPC protocols in honest majority setting achieving malicious security in both information-theoretic setting and computational setting. On the one hand, we study the possibility of basing honest majority MPC protocols on oblivious linear evaluation (OLE)-hybrid model efficiently with information-theoretic security. More precisely, we instantiate preprocessing phase of the recent work Sharing Transformation (<PERSON>yal, Polychroniadou, and <PERSON>, CRYPTO 2022) assuming random OLE correlations. Notably, we are able to prepare packed Beaver triples with malicious security achieving amortized communication ofO(n) field elements plus a number ofO(n) OLE correlations per packed Beaver triple, which is the best known result. To further efficiently prepare random OLE correlations, we resort to IKNP-style OT extension protocols (<PERSON><PERSON> et al., CRYPTO 2003) in random oracle model. On the other hand, we derive a communication lower bound for preparing OLE correlations in the information-theoretic setting based on negative results due to <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (CRYPTO 2019). Combining our positive result with the work of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON> (CRYPTO 2022), we derive an MPC protocol with amortized communication of\\(O(\\ell +\\kappa )\\)elements per gate in random oracle model achieving malicious security, where\\(\\ell \\)denotes the length of a field element and\\(\\kappa \\)is the security parameter.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91092-0_16"}, {"primary_key": "161391", "vector": [], "sparse_vector": [], "title": "Single-Server Client Preprocessing PIR with Tight Space-Time Trade-Off.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper partly solves the open problem of tight trade-off of client storage and server time in the client preprocessing setting of private information retrieval (PIR). In the client preprocessing setting of PIR, the client is allowed to store some hints generated from the database in a preprocessing phase and use the hints to assist online queries. We construct a new single-server client preprocessing PIR scheme. For a database withnentries of sizew, our scheme uses\\(S=O((n/T) \\cdot (\\log n + w))\\)bits of client storage andTamortized server probes overn/Tqueries, whereTis a tunable online time parameter. Our scheme matches (up to constant factors) a\\(ST = \\varOmega (nw)\\)lower bound generalized from a recent work by <PERSON><PERSON> (EUROCRYPT 2023) and a communication barrier generalized from Ishai, Shi, and Wichs (CRYPTO 2024). From a technical standpoint, we present a novel organization of hints where each PIR query consumes a hint, and entries in the consumed hint are relocated to other hints. We then present a new data structure to track the hint relocations and use small-domain pseudorandom permutations to make the hint storage sublinear while maintaining efficient lookups in the hints.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91095-1_4"}, {"primary_key": "161392", "vector": [], "sparse_vector": [], "title": "Tighter Security Notions for a Modular Approach to Private Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To counteract side-channel attacks, a masking scheme splits each intermediate variable intonshares and transforms each elementary operation (e.g., field addition and multiplication) to the masked correspondence called gadget, such that intrinsic noise in the leakages renders secret recovery infeasible in practice. A simple and efficient security notion is the probing model ensuring that any\\(n-1\\)shares are independently distributed from the secret input. One requirement of the probing model is that the noise in the leakages should increase with the number of shares, largely restricting the side-channel security in the low-noise scenario. Another security notion for masking, called the random probing model, allows each variable to leak with a probabilityp. While this model reflects the physical reality of side channels much better, it brings significant overhead. At Crypto 2018, <PERSON><PERSON><PERSON> et al. proposed a modular approach that can provide random probing security for any security level by expanding small base gadgets withnshare recursively, such that the tolerable leakage probabilitypdecreases withnwhile the security increases exponentially with the recursion depth of expansion. Then, <PERSON><PERSON> et al. provided a formal security definition called Random Probing Expandability (RPE) and an explicit framework using the modular approach to construct masking schemes at Crypto 2020. In this paper, we investigate how to tighten the RPE definition via allowing the dependent failure probabilities of multiple inputs, which results in a new definition called related RPE. It can be directly used for the expansion of multiplication gates and reduce the complexity of the base multiplication gadget from\\(\\mathcal {O}(n^2\\log n)\\)proposed at Asiacrypt 2021 to\\(\\mathcal {O}(n^2)\\)and maintain the same security level. Furthermore, we describe a method to expand any gates (rather than only multiplication) with the related RPE gadgets. Besides, we denote another new RPE definition called Multiple inputs RPE used for the expansion of multiple-input gates composed with any gates. Utilizing these methods, we reduce the complexity of the 3-share circuit compiler to\\(\\mathcal {O}(|C|\\cdot \\kappa ^{3.2})\\), where |C| is the size of the unprotected circuit and the protection failure probability of the global circuit is\\(2^{-\\kappa }\\).In comparison, the complexity of the state-of-the-art work, proposed at Eurocrypt 2021, is\\(\\mathcal {O}(|C|\\cdot \\kappa ^{3.9})\\)for the same value ofn. Additionally, we provide the construction of a 5-share circuit compiler with a complexity\\(\\mathcal {O}(|C|\\cdot \\kappa ^{2.8})\\).", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91101-9_5"}, {"primary_key": "161393", "vector": [], "sparse_vector": [], "title": "New Techniques for Preimage Sampling: Improved NIZKs and More from LWE.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>e", "<PERSON>"], "summary": "Recent constructions of vector commitments and non-interactive zero-knowledge (NIZK) proofs from LWE implicitly solve the followingshifted multi-preimage sampling problem: given matrices\\(\\textbf{A}_1, \\ldots , \\textbf{A}_\\ell \\in \\mathbb {Z}_{q}^{n \\times m}\\)and targets\\(\\textbf{t}_1, \\ldots , \\textbf{t}_\\ell \\in \\mathbb {Z}_{q}^n\\), sample a shift\\(\\textbf{c}\\in \\mathbb {Z}_{q}^n\\)and short preimages\\(\\boldsymbol{\\pi }_1, \\ldots , \\boldsymbol{\\pi }_\\ell \\in \\mathbb {Z}_{q}^m\\)such that\\(\\textbf{A}_i \\boldsymbol{\\pi }_i = \\textbf{t}_i + \\textbf{c}\\)for all\\(i \\in [\\ell ]\\). In this work, we introduce a new technique for sampling\\(\\textbf{A}_1, \\ldots , \\textbf{A}_\\ell \\)together with a succinct public trapdoor for solving the multi-preimage sampling problem with respect to\\(\\textbf{A}_1, \\ldots , \\textbf{A}_\\ell \\). This enables the following applications: We provide a dual-mode instantiation of the hidden-bits model (and by correspondence, a dual-mode NIZK proof for\\(\\textsf{NP} \\)) with (1) a linear-size common reference string (CRS); (2) a transparent setup in hiding mode (which yields statistical NIZK arguments); and (3) hardness from LWE with a polynomial modulus-to-noise ratio. This improves upon the work of Waters (STOC 2024) which required a quadratic-size structured reference string (inbothmodes) and LWE with a super-polynomial modulus-to-noise ratio. We give a statistically-hiding vector commitment with transparent setup and polylogarithmic-size CRS, commitments, and openings from SIS. This simultaneously improves upon the vector commitment schemes of de Castro and Peikert (EUROCRYPT 2023) as well as Wee and Wu (EUROCRYPT 2023). At a conceptual level, our work provides a unified view of recent lattice-based vector commitments and hidden-bits model NIZKs through the lens of the shifted multi-preimage sampling problem.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91134-7_1"}, {"primary_key": "161394", "vector": [], "sparse_vector": [], "title": "Almost Optimal KP and CP-ABE for Circuits from Succinct LWE.", "authors": ["<PERSON><PERSON><PERSON>e"], "summary": "We present almost-optimal lattice-based attribute-based encryption (ABE) and laconic function evaluation (LFE). For depthdcircuits over\\(\\ell \\)-bit inputs, we obtain key-policy and ciphertext-policy ABE schemes with ciphertext, secret key and public key sizeO(1); LFE with ciphertext size\\(\\ell + O(1)\\)as well as CRS and digest sizeO(1); where\\(O(\\cdot )\\)hides\\(\\textsf{poly}(d,\\lambda )\\)factors. Our parameter sizes areoptimal, up to the\\(\\textsf{poly}(d)\\)dependencies. The security of our schemes rely on succinct LWE (Wee, CRYPTO 2024). Our results constitute a substantial improvement over the state of the art; none of our results were known even under the stronger evasive LWE assumption.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_2"}, {"primary_key": "161395", "vector": [], "sparse_vector": [], "title": "On Quantum Money and Evasive Obfuscation.", "authors": ["<PERSON>"], "summary": "We show a black box barrier against constructing public key quantum money from obfuscation for evasive functions. As current post-quantum obfuscators based on standard assumptions are all evasive, this shows a fundamental barrier to achieving public key quantum money from standard tools. Our impossibility applies to black box schemes where (1) obfuscation queries made by the mint are classical, and (2) the verifier only makes (possibly quantum) evaluation queries, but no obfuscation queries. This class seems to capture any natural method of using obfuscation to build quantum money.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_7"}, {"primary_key": "161396", "vector": [], "sparse_vector": [], "title": "Optimal Traitor Tracing from Pairings.", "authors": ["<PERSON>"], "summary": "We use pairings over elliptic curves to give a collusion-resistant traitor tracing scheme where the sizes of public keys, secret keys, and ciphertexts are independent of the number of users. Prior constructions from pairings had size\\(\\varOmega (N^{1/3})\\). An additional consequence of our techniques is general result showing that attribute-based encryption for circuits generically implies optimal traitor tracing.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91131-6_11"}, {"primary_key": "161397", "vector": [], "sparse_vector": [], "title": "Preimage Attacks on up to 5 Rounds of SHA-3 Using Internal Differentials.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Mei<PERSON> Liu"], "summary": "In this paper, we study preimage resistance of the\\(\\texttt {SHA}\\text {-} \\texttt {3}\\)standard. We propose a squeeze meet-in-the-middle attack as a new preimage attack method for the sponge functions. This attack combines the squeeze attack and meet-in-the-middle attack, and is implemented by internal differentials. We analyze the inverse operation of the\\(\\texttt {SHA}\\text {-} \\texttt {3}\\)round function, and develop a new target internal differential algorithm as well as a linearization technique for the Sbox in the backward phase. In addition, we propose the concept of a value-difference distribution table (VDDT) to optimize the attack complexity. These techniques lead to faster preimage attacks on five (out of six)\\(\\texttt {SHA}\\text {-} \\texttt {3}\\)functions reduced to 4 rounds, and also bring preimage attacks on 5 rounds of four\\(\\texttt {SHA}\\text {-} \\texttt {3}\\)instances. The attack techniques are verified by performing practical preimage attack on a small variant of 4-roundKeccak.", "published": "2025-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-91107-1_12"}]