[{"primary_key": "2565756", "vector": [], "sparse_vector": [], "title": "In-Memory Computing in Emerging Memory Technologies for Machine Learning: An Overview.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The saturating scaling trends of CMOS technology have fuelled the exploration of emerging non-volatile memory (NVM) technologies as a promising alternative for accelerating data intensive Machine Learning (ML) workloads. To that effect, researchers have explored special-purpose accelerators based on NVM crossbar primitives. NVM crossbars have high storage density and can efficiently per-form massively parallel in-situ Matrix Vector Multiplication (MVM) operations, the key computation in ML workloads, helping over-come the memory bottleneck faced by von Neumann architectures. Despite the promises, analog computing nature of NVM crossbars can lead to functional errors due to device and circuit non-idealities such as parasitic resistances and device non-linearities. Moreover, NVM crossbars need high cost peripheral circuitry to be integrated in large scale systems. Hence, there is a need to study different levels of the design stack to realize the potential of this technology.In this paper, we present an overview of in-memory computing in NVM crossbars for ML workloads. We discuss the basic anatomy of NVM crossbars and highlight the challenges faced at the primitive level. Next, we present how the high storage density of NVM crossbars can enable spatially distributed architectures. Further, we present various modeling and evaluation tools which can effectively help us study the functionality as well as performance of NVM crossbar systems. Finally, we provide an outlook on the future research directions in this field.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218505"}, {"primary_key": "2565757", "vector": [], "sparse_vector": [], "title": "Multiplicative Complexity of Autosymmetric Functions: Theory and Applications to Security.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Valentina Ciriani", "<PERSON>"], "summary": "The multiplicative complexity of a Boolean function is the minimum number of AND gates (i.e., multiplications) that are sufficient to represent the function over the basis {AND, XOR, NOT}. The multiplicative complexity measure plays a crucial role in cryptography-related applications. In fact, the minimization of the number of AND gates is important for high-level cryptography protocols such as secure multiparty computation, where processing AND gates is more expensive than processing XOR gates. Moreover, it is an indicator of the degree of vulnerability of the circuit, as a small number of AND gates corresponds to a high vulnerability to algebraic attacks. In this paper we study a particular structure regularity of Boolean functions, called autosymmetry, and exploit it to decrease the number of ANDs in XOR-AND Graphs (XAGs), i.e., Boolean networks composed by ANDs, XORs, and inverters. The interest in autosymmetric functions is motivated by the fact that a considerable amount of standard Boolean functions of practical interest presents this regularity; indeed, about 24% of the functions in the classical ESPRESSO benchmark suite have at least one autosymmetric output. The experimental results validate the proposed approach.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218492"}, {"primary_key": "2565758", "vector": [], "sparse_vector": [], "title": "Hardware-Assisted Intellectual Property Protection of Deep Learning Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The protection of intellectual property (IP) rights of well-trained deep learning (DL) models has become a matter of major concern, especially with the growing trend of deployment of Machine Learning as a Service (MLaaS). In this work, we demonstrate the utilization of a hardware root-of-trust to safeguard the IPs of such DL models which potential attackers have access to. We propose an obfuscation framework called Hardware Protected Neural Network (HPNN) in which a deep neural network is trained as a function of a secret key and then, the obfuscated DL model is hosted on a public model sharing platform. This framework ensures that only an authorized end-user who possesses a trustworthy hardware device (with the secret key embedded on-chip) is able to run intended DL applications using the published model. Extensive experimental evaluations show that any unauthorized usage of such obfuscated DL models result in significant accuracy drops ranging from 73.22 to 80.17% across different neural network architectures and benchmark datasets. In addition, we also demonstrate the robustness of proposed HPNN framework against a model fine-tuning type of attack.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218651"}, {"primary_key": "2565759", "vector": [], "sparse_vector": [], "title": "GCN-RL Circuit Designer: Transferable Transistor Sizing with Graph Neural Networks and Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Automatic transistor sizing is a challenging problem in circuit design due to the large design space, complex performance tradeoffs, and fast technology advancements. Although there have been plenty of work on transistor sizing targeting on one circuit, limited research has been done on transferring the knowledge from one circuit to another to reduce the re-design overhead. In this paper, we present GCN-RL Circuit Designer, leveraging reinforcement learning (RL) to transfer the knowledge between different technology nodes and topologies. Moreover, inspired by the simple fact that circuit is a graph, we learn on the circuit topology representation with graph convolutional neural networks (GCN). The GCN-RL agent extracts features of the topology graph whose vertices are transistors, edges are wires. Our learning-based optimization consistently achieves the highest Figures of Merit (FoM) on four different circuits compared with conventional black box optimization methods (Bayesian Optimization, Evolutionary Algorithms), random search and human expert designs. Experiments on transfer learning between five technology nodes and two circuit topologies demonstrate that RL with transfer learning can achieve much higher FoMs than methods without knowledge transfer. Our transferable optimization method makes transistor sizing and design porting more effective and efficient.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218757"}, {"primary_key": "2565760", "vector": [], "sparse_vector": [], "title": "Timing-Accurate General-Purpose I/O for Multi- and Many-Core Systems: Scheduling and Hardware Support.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "General-purpose I/O widely exists on multi- and many-core systems. For real-time applications, I/O operations are often required to be timing-predictable, i.e., bounded in the worst case, and timing-accurate, i.e., occur at (or near) an exact desired time instant. Unfortunately, both timing requirements of I/O operations are hard to achieve from the system level, especially for many-core architectures, due to various latency and contention factors presented in the path of instigating an I/O request. This paper considers a dedicated I/O co-processing unit, and proposes two scheduling methods, with the necessary hardware support implemented. It is the first work that guarantees timing predictability and maximises timing accuracy of I/O tasks in the multi-and many-core systems.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218686"}, {"primary_key": "2565761", "vector": [], "sparse_vector": [], "title": "A Versatile and Flexible Chiplet-based System Design for Heterogeneous Manycore Architectures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Heterogeneous manycore architectures are deployed to simultaneously run multiple and diverse applications. This requires various computing capabilities (CPUs, GPUs, and accelerators), and an efficient network-on-chip (NoC) architecture to concurrently handle diverse application communication behavior. However, supporting the concurrent communication requirements of diverse applications is challenging due to the dynamic application mapping, the complexity of handling distinct communication patterns and limited on-chip resources. In this paper, we propose Adapt-NoC, a versatile and flexible NoC architecture for chiplet-based manycore architectures, consisting of adaptable routers and links. Adapt-NoC can dynamically allocate disjoint regions of the NoC, called subNoCs, for concurrently-running applications, each of which can be optimized for different communication behavior. The adaptable routers and links are capable of providing various subNoC topologies, satisfying different latency and bandwidth requirements of various traffic patterns (e.g. all-to-all, one-to-many). Full system simulation shows that AdaptNoC can achieve 31% latency reduction, 24% energy saving and 10% execution time reduction on average, when compared to prior designs.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218654"}, {"primary_key": "2565762", "vector": [], "sparse_vector": [], "title": "S-CDA: A Smart Cloud Disk Allocation Approach in Cloud Block Storage System.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Mengling Tao", "<PERSON>"], "summary": "Cloud disk provided to users by cloud providers has been a prevalent form of cloud storage. Assigning disk storage space for cloud disks among available data warehouses remains a challenging task, as the load characteristics of cloud disks are not known at the time of disk creation. Methods considering only subscribed capacity could be prone to result in resource under-utilization and load imbalance among warehouses. We propose a Smart Cloud Disk Allocation (S-CDA) approach, which uses clustering and classifying to predict the load information for new cloud disks, and then realizes multi-dimensional allocation based on Manhattan distance. Experimental results with realistic cloud workloads show that, compared with the existing one-dimensional allocation scheme, S-CDA increases the overall space/IOPS/disk bandwidth utilization, while decreasing the load imbalance.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218702"}, {"primary_key": "2565763", "vector": [], "sparse_vector": [], "title": "Remote Atomic Extension (RAE) for Scalable High Performance Computing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Emerging data-intensive applications such as graph analytics, machine learning, and data-driven scientific computing are driving the evolution of high-performance computing (HPC) systems from monolithic to scaled-out, heterogeneous, and complex architectures. In these systems, enormous data sets are mapped to discrete nodes to improve the performance of the system by using distributed storage and computing resources. As such, these data distributions induce frequent cross-node data transactions which challenge the performance of large-scale systems. Global atomic operations are one emerging class of the remote data operations that enable lock-free remote shared data operations. However, the cross-node read-modify-write operations consist of multiple distinct data operations and specific atomicity management, which induces a large amount of overhead. As such, these global atomic operations require an efficient communication methodology Existing advanced compo-nents, such as network interface controllers, network fabrics, network-on-chip (NoC) interconnects, are architected together to improve the system performance. However, complex software infrastructures are needed to provide integration between each discrete component. As a result, the redundant software routines across distinct devices induce a large amount of overhead that causes performance degradationIn this paper, we propose a remote atomic extension (RAE) design that provides inherent ISA-level instructions and micro-architecture support for remote atomic operations based on the RISC-V instruction set architecture (ISA). We design a toolchain and evaluate the RAE infrastructure via simulation. Our experiment results show that RAE eliminates 89.71% of the redundant software instructions used for remote atomic accesses and improves the performance by 17.61% on average (up to 23.35%), compared with the OpenSHMEM.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218589"}, {"primary_key": "2565764", "vector": [], "sparse_vector": [], "title": "Best of Both Worlds: AutoML Codesign of a CNN and its Hardware Accelerator.", "authors": ["<PERSON>", "Lu<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Neural architecture search (NAS) has been very successful at outperforming human-designed convolutional neural networks (CNN) in accuracy, and when hardware information is present, latency as well. However, NAS-designed CNNs typically have a complicated topology, therefore, it may be difficult to design a custom hardware (HW) accelerator for such CNNs. We automate HW-CNN codesign using NAS by including parameters from both the CNN model and the HW accelerator, and we jointly search for the best model-accelerator pair that boosts accuracy and efficiency. We call this Codesign-NAS. In this paper we focus on defining the Codesign-NAS multiobjective optimization problem, demonstrating its effectiveness, and exploring different ways of navigating the codesign search space. For CIFAR-10 image classification, we enumerate close to 4 billion model-accelerator pairs, and find the Pareto frontier within that large search space. This allows us to evaluate three different reinforcement-learning-based search strategies. Finally, compared to ResNet on its most optimal HW accelerator from within our HW design space, we improve on CIFAR-100 classification accuracy by 1.3% while simultaneously increasing performance/area by 41% in just ~1000 GPU-hours of running Codesign-NAS.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218596"}, {"primary_key": "2565765", "vector": [], "sparse_vector": [], "title": "Centaur: Hybrid Processing in On/Off-chip Memory Architecture for Graph Analytics.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The increased use of graph algorithms in diverse fields has highlighted their inefficiencies in current chip-multiprocessor (CMP) architectures, primarily due to their seemingly random-access patterns to off-chip memory. Recently, two families of solutions have been proposed: 1) solutions that offload operations generated by all vertices from the processor cores to off-chip memory; and 2) solutions that offload only operations generated by high-degree vertices to dedicated on-chip memory, while the cores continue to process the work related to the remaining vertices. Neither approach is optimal over the full range of vertex's degrees. Thus, in this work, we propose Centaur, a novel architecture that processes operations on vertex data in on- and off-chip memory. Centaur utilizes a vertex's degree as a proxy to determine whether to process related operations in on- or off-chip memory. Centaur manages to provide up to 4.0× improvement in performance and 3.8× in energy benefits, compared to a baseline CMP, and up to a 2.0× performance boost over state-of-the-art specialized solutions.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218624"}, {"primary_key": "2565766", "vector": [], "sparse_vector": [], "title": "Hawkware: Network Intrusion Detection based on Behavior Analysis with ANNs on an IoT Device.", "authors": ["<PERSON><PERSON><PERSON> Ahn", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The network-based Intrusion detection system (NIDS) plays a key role in Internet of Things (IoT) as most IoT services are network-driven. However, the existing NIDSes for IoT systems are either too costly to scale or vulnerable against advanced attacks such as traffic mimicry. In this paper, we propose a novel IDS named Hawkware, a lightweight ANN-based distributed NIDS that runs on an IoT device and analyzes the device's runtime behavior in tandem with its network traffic. By analyzing device behavior, Hawkware is able to replace expensive, deep data analysis that has traditionally been used to detect advanced attacks. Our evaluations show that Hawkware is lightweight enough to be distributed and deployed on a Raspberry PI, and yet capable of detecting such attacks at a satisfactory level.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218559"}, {"primary_key": "2565767", "vector": [], "sparse_vector": [], "title": "An Efficient Circuit Compilation Flow for Quantum Approximate Optimization Algorithm.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quantum approximate optimization algorithm (QAOA) is a promising quantum-classical hybrid algorithm to solve hard combinatorial optimization problems. The two-qubits gates used in quantum circuit for QAOA are commutative i.e., the order of gates can be altered without changing the logical output. This re-ordering leads to execution of more gates in parallel and a smaller number of additional gates to compile the QAOA circuit resulting in lower circuit depth and gate-count which is beneficial for circuit run-time and noise. A lower number of gates means a lower accumulation of gate errors, and a lower circuit depth means the quantum bits will have a lower time to decohere (lose state). However, finding the best re-ordered circuit is a difficult problem and does not scale well with circuit size. This paper presents a compilation flow with 3 approaches to find an optimal re-ordered circuit with reduced depth and gate count. Our approaches can reduce gate count up to 23.21% and circuit depth up to 53.65%. Our approaches are compiler agnostic, can be integrated with existing compilers, and scalable.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218558"}, {"primary_key": "2565768", "vector": [], "sparse_vector": [], "title": "Wafer Map Defect Patterns Classification using Deep Selective Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the continuous drive toward integrated circuits scaling, efficient yield analysis is becoming more crucial yet more challenging. In this paper, we propose a novel methodology for wafer map defect pattern classification using deep selective learning. Our proposed approach features an integrated reject option where the model chooses to abstain from predicting a class label when misclassification risk is high. Thus, providing a trade-off between prediction coverage and misclassification risk. This selective learning scheme allows for new defect class detection, concept shift detection, and resource allocation. Besides, and to address the class imbalance problem in the wafer map classification, we propose a data augmentation framework built around a convolutional auto-encoder model for synthetic sample generation. The efficacy of our proposed approach is demonstrated on the WM-811k industrial dataset where it achieves 94% accuracy under full coverage and 99% with selective learning while successfully detecting new defect types.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218580"}, {"primary_key": "2565769", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Enabling Containerized Computing and Orchestration of ROS-based Robotic SW Applications on Cloud-Server-Edge Architectures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a toolehain based on <PERSON><PERSON> and <PERSON>beEdge that enables containerization and orchestration of ROS-based robotic SW applications on heterogeneous and hierarchical HW architectures. The toolehain allows for verification of functional and real-time constraints through HW-in-the-loop simulation, and for automatic mapping exploration of the SW across Cloud-Server-Edge architectures. We present the results obtained for the deployment of a real case of study composed by an ORB-SLAM application combined to local/global planners with obstacle avoidance for a mobile robot navigation.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218659"}, {"primary_key": "2565770", "vector": [], "sparse_vector": [], "title": "WET: Write Efficient Loop Tiling for Non-Volatile Main Memory.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Future systems are expected to increasingly include a Non-Volatile Main Memory (NVMM). However, due to the limited NVMM write endurance, the number of writes must be reduced. While new architectures and algorithms have been proposed to reduce writes to NVMM, few or no studies have looked at the effect of compiler optimizations on writes.In this paper, we investigate the impact of one popular compiler optimization (loop tiling) on a very important computation kernel (matrix multiplication). Our novel observation includes that tiling on matrix multiplication causes a 25× write amplification. Furthermore, we investigate techniques to make tilling more NVMM friendly, through choosing the right tile size and employing hierarchical tiling. Our method Write-Efficient Tiling (WET) adds a new outer tile designed for fitting the write working set to the Last Level Cache (LLC) to reduce the number of writes to NVMM. Our experiments reduce writes by 81% while simultaneously improve performance.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218612"}, {"primary_key": "2565771", "vector": [], "sparse_vector": [], "title": "SAT-Sweeping Enhanced for Logic Synthesis.", "authors": ["<PERSON>", "<PERSON>", "Eleon<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "SAT-sweeping is a powerful method for simplifying logic networks. It consists of merging gates that are proven equivalent (up to complementation) by running simulation and SAT solving in synergy. SAT-sweeping is used in both verification and synthesis applications within EDA. In this paper, we focus on the development of a highly efficient, synthesis-oriented, SAT-sweeping engine. We introduce a new algorithm to guide initial simulation, which strongly reduces the number of false candidates for merge, thus increasing the computational efficiency of the sweeper. We revisit the SAT-sweeping flow in light of practical considerations for synthesis, with the aim of proving all valid merges and ensuring fast execution. Experimental results confirm remarkable speedup deriving from our methodology, up to 10× for large combinational networks, and better QoR as compared to previous SAT-sweeping implementation. Embedded in a commercial synthesis flow, our proposes SAT-sweeper enables area and power savings of 1.98% and 1.81%, respectively, with neutral timing at negligible runtime overhead, over 36 testcases.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218691"}, {"primary_key": "2565772", "vector": [], "sparse_vector": [], "title": "Invited: Chipyard - An Integrated SoC Research and Implementation Environment.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Continued improvement in computing efficiency requires functional specialization of hardware designs. We present an agile design flow for custom SoCs using the Chipyard framework, an integrated SoC research and implementation environment for custom systems. Chipyard includes configurable, composable, open-source, generator-based designs that can be used across multiple stages of the hardware development flow while maintaining design intent and integration consistency. Through cloud FPGA simulation and rapid ASIC implementation, we demonstrate an iterative agile hardware design cycle which enables continuous validation of physically-realizable customized systems.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218756"}, {"primary_key": "2565773", "vector": [], "sparse_vector": [], "title": "Extending the RISC-V ISA for Efficient RNN-based 5G Radio Resource Management.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Radio Resource Management in 5G mobile communication is a challenging problem for which Recurrent Neural Networks (RNN) have shown promising results. Accelerating the compute-intensive RNN inference is therefore of utmost importance. Programmable solutions are desirable for effective 5G-RRM coping with the rapidly evolving landscape of RNN variations. In this paper, we investigate RNN inference acceleration by tuning both the instruction set and micro-architecture of a micro-controller-class open-source RISC-V core. We couple HW extensions with software optimizations to achieve an overall improvement in throughput and energy efficiency of 15× and 10× w.r.t. the baseline core on a wide range of RNNs used in various RRM tasks.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218496"}, {"primary_key": "2565774", "vector": [], "sparse_vector": [], "title": "PIM-Assembler: A Processing-in-Memory Platform for Genome Assembly.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, for the first time, we propose a high-throughput and energy-efficient Processing-in-DRAM-accelerated genome assembler called PIM-Assembler based on an optimized and hardware-friendly genome assembly algorithm. PIM-Assembler can assemble large-scale DNA sequence dataset from all-pair overlaps. We first develop PIM-Assembler platform that harnesses DRAM as computational memory and transforms it to a fundamental processing unit for genome assembly. PIM-Assembler can perform efficient X(N)OR-based operations inside DRAM incurring low cost on top of commodity DRAM designs (~5% of chip area). PIM-Assembler is then optimized through a correlated data partitioning and mapping methodology that allows local storage and processing of DNA short reads to fully exploit the genome assembly algorithm-level's parallelism. The simulation results show that PIM-Assembler achieves on average 8.4× and 2.3 wise× higher throughput for performing bulk bit-XNOR-based comparison operations compared with CPU and recent processing-in-DRAM platforms, respectively. As for comparison/addition-extensive genome assembly application, it reduces the execution time and power by ~5× and ~ 7.5× compared to GPU.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218653"}, {"primary_key": "2565775", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: LDFSM: A Low-Cost Bit-Stream Generator for Low-Discrepancy Stochastic Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Low-discrepancy (LD) bit-streams have been proposed to improve accuracy and computation speed of stochastic computing (SC) circuits. These bit-streams are conventionally generated using a quasi-random number generator such as a Sobol sequence generator and a comparator. The high hardware cost of quasi-random number generators makes the current comparator-based LD bit-stream generators expensive in terms of area and power cost. The hardware cost issue further aggravates when increasing the number of inputs and the precision of data. A finite state machine (FSM)-based LD bit-stream generator was proposed recently to mitigate this hardware cost. The proposed generator however can only generate one LD pattern which limits its application to SC circuits with only one LD bit-stream. This work proposes LDFSM, a low-cost FSM-based LD bit-stream generator that supports generation of any LD pattern. LDFSM reduces the hardware area and the area-delay product up to 80% compared to those of the state-of-the-art LD bit-stream generator.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218733"}, {"primary_key": "2565776", "vector": [], "sparse_vector": [], "title": "PISCES: Power-Aware Implementation of SLAM by Customizing Efficient Sparse Algebra.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A key real-time task in autonomous systems is simultaneous localization and mapping (SLAM). Although prior work has proposed hardware accelerators to process SLAM in real time, they paid less attention to power consumption. To be more power-efficient, we propose Pisces, which co-optimizes power consumption and latency by exploiting sparsity, a key characteristic of SLAM missed in prior work. By orchestrating sparse data, Pisces aligns correlated data and enables deterministic, one-time, and parallel accesses to the on-chip memory. Therefore, Pisces (i) eliminates unnecessary memory accesses and (ii) enables pipelined and parallel processing. Our FPGA implementation shows that <PERSON>sces consumes 2.5× less power and executes SLAM 7.4× faster than the state of the art.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218550"}, {"primary_key": "2565777", "vector": [], "sparse_vector": [], "title": "Hardware Acceleration of Graph Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph neural networks (GNNs) have been shown to extend the power of machine learning to problems with graph-structured inputs. Recent research has shown that these algorithms can exceed state-of-the-art performance on applications ranging from molecular inference to community detection. We observe that existing execution platforms (including existing machine learning accelerators) are a poor fit for GNNs due to their unique memory access and data movement requirements. We propose, to the best of our knowledge, the first accelerator architecture targeting GNNs. The architecture includes dedicated hardware units to efficiently execute the irregular data movement required for graph computation in GNNs, while also providing high compute throughput required by GNN models. We show that our architecture outperforms existing execution platforms in terms of inference latency on several key GNN benchmarks (e.g., 7.5x higher performance than GPUs and 18x higher performance than CPUs at iso-bandwidth).", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218751"}, {"primary_key": "2565778", "vector": [], "sparse_vector": [], "title": "NACU: A Non-Linear Arithmetic Unit for Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reconfigurable architectures targeting neural networks are an attractive option. They allow multiple neural networks of different types to be hosted on the same hardware, in parallel or sequence. Reconfigurability also grants the ability to morph into different micro-architectures to meet varying power-performance constraints. In this context, the need for a reconfigurable non-linear computational unit has not been widely researched. In this work, we present a formal and comprehensive method to select the optimal fixed-point representation to achieve the highest accuracy against the floating-point implementation benchmark. We also present a novel design of an optimised reconfigurable arithmetic unit for calculating non-linear functions. The unit can be dynamically configured to calculate the sigmoid, hyperbolic tangent, and exponential function using the same underlying hardware. We compare our work with the state-of-the-art and show that our unit can calculate all three functions without loss of accuracy.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218549"}, {"primary_key": "2565779", "vector": [], "sparse_vector": [], "title": "R2D3: A Reliability Engine for 3D Parallel Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper proposes a holistic reliability management engine, R2D3, for <PERSON><PERSON><PERSON>'s technology based parallel 3D systems that have low yield and high failure rate. The proposed engine, comprising of a controller, reconfigurable crossbars and detection circuitry, provides concurrent single-replay detection and diagnosis, fault-mitigating repair and aging-aware lifetime management at runtime. We show that R2D3 achieves 96% coverage of defects, repairs faulty cores, and reduces V th degradation by 53%. This leads to a 78% performance improvement over 8 years and a 2.16× longer mean-time-to-failure over a baseline 8-core 3D processor with no reliability management.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218497"}, {"primary_key": "2565780", "vector": [], "sparse_vector": [], "title": "Creating an Agile Hardware Design Flow.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Taeyoung Kong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Mann", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Although an agile approach is standard for software design, how to properly adapt this method to hardware is still an open question. This work addresses this question while building a system on chip (SoC) with specialized accelerators. Rather than using a traditional waterfall design flow, which starts by studying the application to be accelerated, we begin by constructing a complete flow from an application expressed in a high-level domain-specific language (DSL), in our case Halide, to a generic coarse-grained reconfigurable array (CGRA). As our under-standing of the application grows, the CGRA design evolves, and we have developed a suite of tools that tune application code, the compiler, and the CGRA to increase the efficiency of the resulting implementation. To meet our continued need to update parts of the system while maintaining the end-to-end flow, we have created DSL-based hardware generators that not only provide the Verilog needed for the implementation of the CGRA, but also create the collateral that the compiler/mapper/place and route system needs to configure its operation. This work provides a systematic approach for desiging and evolving high-performance and energy-efficient hardware-software systems for any application domain.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218553"}, {"primary_key": "2565781", "vector": [], "sparse_vector": [], "title": "Vehicular and Edge Computing for Emerging Connected and Autonomous Vehicle Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Emerging connected and autonomous vehicles involve complex applications requiring not only optimal computing resource allocations but also efficient computing architectures. In this paper, we unfold the critical performance metrics required for emerging vehicular computing applications and show with preliminary experimental results, how optimal choices can be made to satisfy the static and dynamic computing requirements in terms of the performance metrics. We also discuss the feasibility of edge computing architectures for vehicular computing and show tradeoffs for different offloading strategies. The paper shows directions for light weight, high performance and low power computing paradigms, architectures and design-space exploration tools to satisfy evolving applications and requirements for connected and autonomous vehicles.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218618"}, {"primary_key": "2565782", "vector": [], "sparse_vector": [], "title": "Efficiently Exploiting Low Activity Factors to Accelerate RTL Simulation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Hardware simulation is a critical tool for design, but its slow speed often bottlenecks the entire design process. Although most signals in a digital design rarely change, most leading simulators still simulate the entirety of the design every cycle. Tracking which signals are unchanged and can thus be reused typically introduces too much overhead to deliver a practical speedup.In this work, we explore the challenge of efficiently detecting opportunities for reuse, and we demonstrate practical techniques to profitably exploit them. Thanks to our novel acyclic partitioning algorithm and other optimizations, our generated simulators outperform open-source and industrial state-of-the-art simulators.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218632"}, {"primary_key": "2565783", "vector": [], "sparse_vector": [], "title": "RedCache: Reduced DRAM Caching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Adapting in-package caching to run-time characteristics of user applications seems a promising approach to improve bandwidth efficiency and performance. However, fine-grained cache block monitoring and adaptation are often impractical due to the significant bandwidth and energy overheads. This paper proposes RedCache that enables fine-grained adaptation at run-time via reduced DRAM caching. Two adaptive parameters are proposed to start and stop caching for individual blocks. Architectural techniques and DRAM specific control mechanisms are proposed to alleviate overheads. Our simulation results indicate averages of 31% and 24% performance improvements over the state-of-the-art Alloy and Bear cache architectures. Respective energy savings over the same baselines are 29% and 18% on average.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218658"}, {"primary_key": "2565784", "vector": [], "sparse_vector": [], "title": "Kite: A Family of Heterogeneous Interposer Topologies Enabled via Accurate Interconnect Modeling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bradford <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent advances in die-stacking and 2.5D chip integration technologies introduce in-package network heterogeneities that can complicate the interconnect design. Integrating chiplets over a silicon interposer offers new opportunities of optimizing interposer topologies. However, limited by the capability of existing network-on-chip (NoC) simulators, the full potential of the interposer-based NoCs has not been exploited. In this paper, we address the shortfalls of prior NoC designs and present a new family of chiplet topologies called Kite. Kite topologies better utilize the diverse networking and frequency domains existing in new interposer systems and outperform the prior chiplet topology proposals. Kite decreased synthetic traffic latency by 7% and improved the maximum throughput by 17% on average versus Double Butterfly and Butter Donut, two previous proposals developed using less accurate modeling.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218539"}, {"primary_key": "2565785", "vector": [], "sparse_vector": [], "title": "INVITED: New Directions in Distributed Deep Learning: Bringing the Network at Forefront of IoT Design.", "authors": ["Kartikeya Bhardwaj", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we first highlight three major challenges to large-scale adoption of deep learning at the edge: (i) Hardware-constrained IoT devices, (ii) Data security and privacy in the IoT era, and (iii) Lack of network-aware deep learning algorithms for distributed inference across multiple IoT devices. We then provide a unified view targeting three research directions that naturally emerge from the above challenges: (1) Federated learning for training deep networks, (2) Data-independent deployment of learning algorithms, and (3) Communication-aware distributed inference. We believe that the above research directions need a network-centric approach to enable the edge intelligence and, therefore, fully exploit the true potential of IoT.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218628"}, {"primary_key": "2565786", "vector": [], "sparse_vector": [], "title": "Proactive Aging Mitigation in CGRAs through Utilization-Aware Allocation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Resource balancing has been effectively used to mitigate the long-term aging effects of Negative Bias Temperature Instability (NBTI) in multi-core and Graphics Processing Unit (GPU) architectures. In this work, we investigate this strategy in Coarse-Grained Reconfigurable Arrays (CGRAs) with a novel application-to-CGRA allocation approach. By introducing important extensions to the reconfiguration logic and the datapath, we enable the dynamic movement of configurations throughout the fabric and allow overutilized Functional Units (FUs) to recover from stress-induced NBTI aging. Implementing the approach in a resource-constrained state-of-the-art CGRA reveals $2.2\\times$ lifetime improvement with negligible performance overheads and less than $10\\%$ increase in area.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218586"}, {"primary_key": "2565787", "vector": [], "sparse_vector": [], "title": "The Power of Simulation for Equivalence Checking in Quantum Computing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The rapid rate of progress in the physical realization of quantum computers sparked the development of elaborate design flows for quantum computations on such devices. Each stage of these flows comes with its own representation of the intended functionality. Ensuring that each design step preserves this intended functionality is of utmost importance. However, existing solutions for equivalence checking of quantum computations heavily struggle with the complexity of the underlying problem and, thus, no conclusions on the equivalence may be reached with reasonable efforts in many cases. In this work, we uncover the power of simulation for equivalence checking in quantum computing. We show that, in contrast to classical computing, it is in general not necessary to compare the complete representation of the respective computations. Even small errors frequently affect the entire representation and, thus, can be detected within a couple of simulations. The resulting equivalence checking flow substantially improves upon the state of the art by drastically accelerating the detection of errors or providing a highly probable estimate of the operations' equivalence.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218563"}, {"primary_key": "2565788", "vector": [], "sparse_vector": [], "title": "Predictable Memory-CPU Co-Scheduling with Support for Latency-Sensitive Tasks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Predictable execution models have been proposed over the years to achieve contention-free execution of real-time tasks by preloading data into dedicated local memories. In this way, memory access delays can be hidden by delegating a DMA engine to perform memory transfers in parallel with processor execution. Nevertheless, state-of-the-art protocols introduce additional blocking due to priority inversion, which may severely penalize latency-sensitive applications and even worsen the system schedulability with respect to the use of classical scheduling schemes. This paper proposes a new protocol that allows hiding memory transfer delays while reducing priority inversion, thus favoring the schedulability of latency-sensitive tasks. The corresponding analysis is formulated as an optimization problem. Experimental results show the advantages of the proposed protocol against state-of-the-art solutions.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218640"}, {"primary_key": "2565789", "vector": [], "sparse_vector": [], "title": "GENIEx: A Generalized Approach to Emulating Non-Ideality in Memristive Xbars using Neural Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Memristive crossbars have been extensively explored for deep learning accelerators due to their high on-chip storage density and efficient Matrix Vector Multiplication (MVM) compared to digital CMOS. However, their analog nature of computing poses significant issues due to various non-idealities such as: parasitic resistances, non-linear I-V characteristics of the memristor device etc. The non-idealities can have a detrimental impact on the functionality i.e. computational accuracy of crossbars. Past works have explored modeling the non-idealities using analytical techniques. However, several non-idealities have data dependent behavior. This can not be captured using analytical (non data-dependent) models thereby, limiting their suitability in predicting application accuracy. To address this, we propose a Generalized Approach to Emulating Non-Ideality in Memristive Crossbars using Neural Networks (GENIEx), which accurately captures the data-dependent nature of non-idealities. First, we perform extensive HSPICE simulations of crossbars with different voltage and conductance combinations. Based on the obtained data, we train a neural network to learn the transfer characteristics of the non-ideal crossbar. Next, we build a functional simulator which includes key architectural facets such as tiling, and bit-slicing to analyze the impact of non-idealities on the classification accuracy of large-scale neural networks. We show that GENIEx achieves low root mean square errors (RMSE) of 0.25 and 0.7 for low and high voltages, respectively, compared to HSPICE. Additionally, the GENIEx errors are 7× and 12.8× better than an analytical model which can only capture the linear non-idealities. Further, using the functional simulator and GENIEx, we demonstrate that an analytical model can overestimate the degradation in classification accuracy by ≥ 10% on CIFAR-100 and 3.7% on ImageNet datasets compared to GENIEx.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218688"}, {"primary_key": "2565790", "vector": [], "sparse_vector": [], "title": "Accurate Inference with Inaccurate RRAM Devices: Statistical Data, Model Transfer, and On-line Adaptation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Resistive random-access memory (RRAM) is a promising technology for in-memory computing with high storage density, fast inference, and good compatibility with CMOS. However, the mapping of a pre-trained deep neural network (DNN) model on RRAM suffers from realistic device issues, especially the variation and quantization error, resulting in a significant reduction in inference accuracy. In this work, we first extract these statistical properties from 65 nm RRAM data on 300mm wafers. The RRAM data present 10-levels in quantization and 50% variance, resulting in an accuracy drop to 31.76% and 10.49% for MNIST and CIFAR-10 datasets, respectively. Based on the experimental data, we propose a combination of machine learning algorithms and on-line adaptation to recover the accuracy with the minimum overhead. The recipe first applies Knowledge Distillation (KD) to transfer an ideal model into a student model with statistical variations and 10 levels. Furthermore, an on-line sparse adaptation (OSA) method is applied to the DNN model mapped on to the RRAM array. Using importance sampling, OSA adds a small SRAM array that is sparsely connected to the main RRAM array; only this SRAM array is updated to recover the accuracy. As demonstrated on MNIST and CIFAR-10 datasets, a 7.86% area cost is sufficient to achieve baseline accuracy for the 65 nm RRAM devices.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218605"}, {"primary_key": "2565791", "vector": [], "sparse_vector": [], "title": "An Efficient EPIST Algorithm for Global Placement with Non-Integer Multiple-Height Cells *.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the increasing design requirements of modern circuits, a standard-cell library often contains cells of different row heights to address various trade-offs among performance, power, and area. However, maintaining all standard cells with integer multiples of a single-row height could cause some area overheads and increase power consumption. In this paper, we present an analytical placer to directly consider a circuit design with non-integer multiple-height standard cells and additional layout constraints. The region of different cell heights is adaptively generated by the global placement result. In particular, an exact penalty iterative shrinkage and thresholding (EPIST) algorithm is employed to efficiently optimize the global placement problem. The convergence of the algorithm is proved, and the acceleration strategy is proposed to improve the performance of our algorithm. Compared with the state-of-the-art works, experimental results based on the 2017 CAD Contest at ICCAD benchmarks show that our algorithm achieves the best wirelength and area for every benchmark. In particular, our proposed EPIST algorithm provides a new direction for effectively solving large-scale nonlinear optimization problems with non-smooth terms, which are often seen in real-world applications.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218504"}, {"primary_key": "2565792", "vector": [], "sparse_vector": [], "title": "Reducing Bit Writes in Non-volatile Main Memory by Similarity-aware Compression.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yuanyuan Sun", "Yuncheng Guo"], "summary": "Various applications use image bitmaps (data containing pixels) in main memory for fast accesses, thereby leading to lots of memory consumption. Unlike legacy DRAM, nonvolatile memories (NVMs) have larger capacity. However, NVM writes consume higher energy and latency compared with reads. Existing data compression schemes leverage precise general-purpose data patterns or precision scaling to reduce data sizes, which suffer from limited compression performance for bitmaps due to large variance or serious quality loss. By exploiting the pixel-level similarity due to the analogous contents in adjacent pixels, we propose SimCom, an approximate Similarity-aware Compression scheme, to compress the write accesses to bitmaps in NVMs, thus efficiently improving the memory performance for image/video applications. SimCom reduces the data size by compressing data into base words and runs. The storage costs for small runs are further mitigated by reusing the least significant bits of base words. The adaptive compression scheme handles various data formats without user annotations on data types. Our experimental results with real-world image/video workloads demonstrate the efficacy and efficiency of SimCom.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218683"}, {"primary_key": "2565793", "vector": [], "sparse_vector": [], "title": "A Robust Exponential Integrator Method for Generic Nonlinear Circuit Simulation.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper, we aim to address two long-lasting issues in large scale transient circuit simulation using the exponential integrator (EI) method. First is the numerical instability caused by the singularity in the differential-algebraic equation system. Our proposed solution is a systematic, algebraic and sparsity preserving regularization technique to eliminate the unstable modes in the system to be solved. Next, we devise a generic scheme to apply Newton-Raphson iterations in the EI framework for enhanced nonlinearity handling capability. With the two techniques, we wish to elevate the robustness and performance of EI and make it a competitive alternative to the existing SPICE-type simulators in practical usage.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218556"}, {"primary_key": "2565794", "vector": [], "sparse_vector": [], "title": "AHEC: End-to-end Compiler Framework for Privacy-preserving Machine Learning Acceleration.", "authors": ["<PERSON><PERSON>", "Rosario Cammarota", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Privacy-preserving machine learning (PPML) is driven by the emerging adoption of Machine Learning as a Service (MLaaS). In a typical MLaaS system, the end-user sends his personal data to the service provider and receives the corresponding prediction output. However, such interaction raises severe privacy concerns about both the user's proprietary data and the server's ML model. PPML integrates cryptographic primitives such as Multi-Party Computation (MPC) and/or Homomorphic Encryption (HE) into ML services to resolve the privacy issue. However, existing PPML solutions have not been widely deployed in practice since: (i) Privacy protection comes at the cost of additional computation and/or communication overhead; (ii) Adapting PPML to different front-end frameworks and back-end hardware incurs prohibitive engineering cost.We propose AHEC, the first automated, end-to-end HE compiler for efficient PPML inference. Leveraging the capability of Domain Specific Languages (DSLs), AHEC enables automated generation and optimization of HE kernels across diverse types of hardware platforms and ML frameworks. We perform extensive experiments to investigate the performance of AHEC from different abstraction levels: HE operations, HE-based ML kernels, and neural network layers. Empirical results corroborate that AHEC achieves superior runtime reduction compared to the state-of-the-art solutions built from static HE libraries.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218508"}, {"primary_key": "2565795", "vector": [], "sparse_vector": [], "title": "Don&apos;t-Care-Based Node Minimization for Threshold Logic Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Threshold logic re-attracts researchers' attention recently due to the advancement of hardware realization techniques and its applications to deep learning. In the past decade, several design automation techniques for threshold logic have been proposed, such as logic synthesis and logic optimization. Although they are effective, threshold logic network (TLN) optimization based on don't cares has not been well studied. In this paper, we propose a don't-care-based node minimization scheme for TLNs. We first present a sufficient condition for don't cares to exist and a logic-implication-based method to identify the don't cares of a threshold logic gate (TLG). Then, we transform the problem of TLG minimization with don't cares to an integer linear programming problem, and present a method to compute the necessary constraints for the ILP formulation. We apply the proposed optimization scheme to two set of TLNs generated by the state-of-the-art synthesis technique. The experimental results show that, for the two sets, it achieves an average of 11% and 19% of area reduction in terms of the sum of the weights and threshold values without overhead on the TLG count and logic depth. Additionally, it completes the optimization of most TLNs within one minute.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218506"}, {"primary_key": "2565796", "vector": [], "sparse_vector": [], "title": "Developing Privacy-preserving AI Systems: The Lessons learned.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Rosario Cammarota"], "summary": "Advances in customers' data privacy laws create pressures and pain points across the entire lifecycle of AI products. Working figures such as data scientists and data engineers need to account for the correct use of privacy-enhancing technologies such as homomorphic encryption, secure multi-party computation, and trusted execution environment when they develop, test and deploy products embedding AI models while providing data protection guarantees. In this work, we share the lessons learned during the development of frameworks to aid data scientists and data engineers to map their optimized workloads onto privacy-enhancing technologies seamlessly and correctly.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218662"}, {"primary_key": "2565797", "vector": [], "sparse_vector": [], "title": "Circuit Learning for Logic Regression on High Dimensional Boolean Space.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Logic regression aims to find a Boolean model involving binary covariates that predicts the response of an unknown system. It has many important applications, e.g., in data analysis and system design. In the 2019 ICCAD CAD Contest, the challenge of learning a compact circuit representing a black-box input-output pattern generator in a high dimensional Boolean space is formulated as the logic regression problem. This paper presents our winning approach to the problem based on a decision-tree reasoning procedure assisted with a template based preprocessing. Our methods outperformed other contestants in the competition in both prediction accuracy and circuit size.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218510"}, {"primary_key": "2565798", "vector": [], "sparse_vector": [], "title": "Tight Compression: Compressing CNN Model Tightly Through Unstructured Pruning and Simulated Annealing Based Permutation.", "authors": ["<PERSON><PERSON>", "Jingyang Zhu", "Jingbo Jiang", "<PERSON><PERSON><PERSON>"], "summary": "The unstructured sparsity after pruning poses a challenge to the efficient implementation of deep learning models in existing regular architectures like systolic arrays. The coarse-grained structured pruning, on the other hand, tends to have higher accuracy loss than unstructured pruning when the pruned models are of the same size. In this work, we propose a compression method based on the unstructured pruning and a novel weight permutation scheme. Through permutation, the sparse weight matrix is further compressed to a small and dense format to make full use of the hardware resources. Compared to the state-of-the-art works, the matrix compression rate is effectively improved from 5.88x to 10.28x. As a result, the throughput and energy efficiency are improved by 2.12 and 1.57 times, respectively.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218701"}, {"primary_key": "2565799", "vector": [], "sparse_vector": [], "title": "Hamiltonian Path Based Mixed-Cell-Height Legalization for Neighbor Diffusion Effect Mitigation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qing<PERSON> Liu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In modern circuit designs, standard cells are designed with different heights based on the power, area, and other characteristics to address various design requirements. For those cells with different heights, in particular, there are inter-cell diffusion steps if the diffusion heights of neighboring cells are different, called the neighbor diffusion effect (NDE) which has become critical in advanced technology nodes. In this paper, we present a Hamiltonian-path-based mixed-cell-height legalization algorithm for NDE mitigation. We first present a row assignment method considering both cell displacements and diffusion steps to assign cells to their desired rows that meet the power-rail alignment constraints. Then, we propose a Hamiltonian-path-based diffusion-step reduction method to effectively reduce the NDE violations while preserving the global placement solution. Particularly, we develop a 2-approximation algorithm to find a minimum weight Hamiltonian path connecting two vertices, and a 1.5-approximation algorithm to find a minimum weight Hamiltonian path with a specified end vertex. Finally, we present an NDE-aware legalization method with design compaction to resolve overlaps and NDE violations. Experimental results show that our algorithm can resolve all NDE violations without any area overhead in reasonable runtime.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218513"}, {"primary_key": "2565800", "vector": [], "sparse_vector": [], "title": "DECOY: DEflection-Driven HLS-Based Computation Partitioning for Obfuscating Intellectual PropertY.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Among various competing designs targeting similar functionality, the key differentiator typically consists of a small amount of custom Intellectual Property (IP). To protect this IP from reverse engineering, designers need effective solutions for hiding the unique aspects of their implementations. In this work, we introduce a general framework for partitioning the computation performed by a design into a part whose implementation is commonly known (and encountered across many designs), and a part which is unique to this design. The former can then be built using conventional techniques (including untrusted manufacturing facilities) while the latter needs to be protected using additional obfuscation techniques. The existence of several other known implementations of the (same or similar) target function serves as a decoy which deflects efforts seeking to reverse-engineer the unique implementation. We demonstrate our framework using a hardware accelerator case study where (a) partitioning is performed through High Level Synthesis (HLS), (b) the commonly known portion of the accelerator is implemented as an Application Specific Integrated Circuit (ASIC), and (c) the unique portion of the accelerator is implemented on an embedded Field-Programmable Gate Array (eFPGA).", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218519"}, {"primary_key": "2565801", "vector": [], "sparse_vector": [], "title": "Fast and Accurate Wire Timing Estimation on Tree and Non-Tree Net Structures.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Timing optimization is repeatedly performed throughout the entire design flow. The long turn-around time of querying a sign-off timer has become a bottleneck. To break through the bottleneck, a fast and accurate timing estimator is desirable to expedite the pace of timing closure. Unlike gate timing, which is calculated by interpolating lookup tables in cell libraries, wire timing calculation has remained a mystery in timing analysis. The mysterious formula and complex net structures increase the difficulty to correlate with the results generated by a sign-off timer, thus further preventing incremental timing optimization engines from accurate timing estimation without querying a sign-off timer. We attempt to solve the mystery by a novel machine-learning-based wire timing model. Different from prior machine learning models, we first extract topological features to capture the characteristics of RC networks. Then, we propose a loop breaking algorithm to transform non-tree nets into tree structures, and thus non-tree nets can be handled in the same way as tree-structured nets. Experiments are conducted on four industrial designs with tree-like nets (28nm) and two industrial designs with non-tree nets (16nm). Our results show that the prediction model trained by XGBoost is highly accurate: For both tree-like and non-tree nets, the mean error of wire delay is lower than 2 ps. The predicted path arrival times have less than 1% mean error. Experimental results also demonstrate that our model can be trained only once and applied to different designs using the same manufacturing process. Our fast and accurate wire timing prediction can easily be integrated into incremental timing optimization and expedites timing closure.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218712"}, {"primary_key": "2565802", "vector": [], "sparse_vector": [], "title": "Exploiting Computation Reuse for Stencil Accelerators.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Stencil kernel is an important type of kernel used extensively in many application domains. Over the years, researchers have been studying the optimizations on parallelization, communication reuse, and computation reuse for various target platforms. However, challenges still exist, especially on the computation reuse problem for accelerators, due to the lack of complete design-space exploration and effective design-space pruning. In this paper, we present solutions to the above challenges for a wide range of stencil kernels (i.e., stencil with reduction operations), where the computation reuse patterns are extremely flexible due to the commutative and associative properties. We formally define the complete design space, based on which we present a provably optimal dynamic programming algorithm and a heuristic beam search algorithm that provides near-optimal solutions under an architecture-aware model. Experimental results show that for synthesizing stencil kernels to FPGAs, compared with state-of-the-art stencil compiler without computation reuse capability, our proposed algorithm can reduce the look-up table (LUT) and digital signal processor (DSP) usage by 58.1% and 54.6% on average respectively, which leads to an average speedup of 2.3× for compute-intensive kernels, outperforming the latest CPU/GPU results.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218680"}, {"primary_key": "2565803", "vector": [], "sparse_vector": [], "title": "Time Multiplexing via Circuit Folding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Time multiplexing is an important technique to overcome the bandwidth bottleneck of limited input-output pins in FPGAs. Most prior work tackles the problem from a physical design standpoint to minimize the number of cut nets or Time Division Multiplexing (TDM) ratio through circuit partitioning or routing. In this work, we formulate a new orthogonal approach at the logic level to achieve time multiplexing through structural and functional circuit folding. The new formulation provides a smooth trade-off between bandwidth and throughput. Experiments show the effectiveness of the structural method and improved optimality of the functional method on look-up-table and flip-flop usage.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218552"}, {"primary_key": "2565804", "vector": [], "sparse_vector": [], "title": "PIM-Prune: Fine-Grain DCNN Pruning for Crossbar-Based Process-In-Memory Architecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Shaokai Ye", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Li <PERSON>"], "summary": "Deep Convolution Neural network (DCNN) pruning is an efficient way to reduce the resource and power consumption in a DCNN accelerator. Exploiting the sparsity in the weight matrices of DCNNs, however, is nontrivial if we deploy these DC-NNs in a crossbar-based Process-In-Memory (PIM) architecture, because of the crossbar structure. Structural pruning-exploiting a coarse-grained sparsity, such as filter/channel-level pruning-can result in a compressed weight matrix that fits the crossbar structure. However, this pruning method inevitably degrades the model accuracy. To solve this problem, in this paper, we propose PIM-PRUNE to exploit the finer-grained sparsity in PIM-architecture, and the resulting compressed weight matrices can significantly reduce the demand of crossbars with negligible accuracy loss. Further, we explore the design space of the crossbar, such as the crossbar size and aspect-ratio, from a new point-of-view of resource-oriented pruning. We find a trade-off existing between the pruning algorithm and the hardware overhead: a PIM with smaller crossbars is more friendly for pruning methods; however, the resulting peripheral circuit cause higher power consumption. Given a specific DCNN, we can suggest a sweet-spot of crossbar design to the optimal overall energy efficiency. Experimental results show that the proposed pruning method applied on Resnet18 can achieve up to 24.85× and 3.56× higher compression rate of occupied crossbars on CifarlO and Imagenet, respectively; while the accuracy loss is negligible, which is 4.56× and 1.99× better than the state-of-art methods.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218523"}, {"primary_key": "2565805", "vector": [], "sparse_vector": [], "title": "A 90nm 103.14 TOPS/W Binary-Weight Spiking Neural Network CMOS ASIC for Real-Time Object Classification.", "authors": ["Po<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper introduces a low-power 90nm CMOS binary weight spiking neural network (BW-SNN) ASIC for real-time image classification. The chip maximizes data reuse through systolic arrays that house the entire 5-layer BW-SNN, requiring a minimum off-chip bandwidth for data access. The chip achieves 97.57% accuracy for real-time bottled-drink recognition, consuming only 0.62uJ per inference. For comparison purpose, it achieves 98.73% accuracy for MNIST hand-written character recognition, consuming only 0.59uJ per inference. The bottled-drink recognition is demonstrated at 300 fps that is well enough for many other real-time applications. The peak efficiency point is 103.14TOPS/W at a voltage of 0.6V, which outperforms other designs so far as we know. By normalizing to the 28nm technology node, the proposed ASIC is about 5× more efficient and 7× lower hardware cost as compared with the state-of-the-art designs.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218714"}, {"primary_key": "2565806", "vector": [], "sparse_vector": [], "title": "CRAFFT: High Resolution FFT Accelerator In Spintronic Computational RAM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhao", "<PERSON>", "<PERSON><PERSON><PERSON>", "Sachin <PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "High resolution Fast Fourier Transform (FFT) is important for various applications while increased memory access and parallelism requirement limits the traditional hardware. In this work, we explore acceleration opportunities for high resolution FFTs in spintronic computational RAM (CRAM) which supports true in-memory processing semantics. We experiment with Spin-Torque-Transfer (STT) and Spin-Hall-Effect (SHE) based CRAMs in implementing CRAFFT, a high resolution FFT accelerator in memory. For one million point fixed-point FFT, we demonstrate that CRAFFT can provide up to 2.57× speedup and 673× energy reduction. We also provide a proof-of-concept extension to floating-point FFT.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218673"}, {"primary_key": "2565807", "vector": [], "sparse_vector": [], "title": "SparseTrain: Exploiting Dataflow Sparsity for Efficient Convolutional Neural Networks Training.", "authors": ["Pengcheng Dai", "<PERSON><PERSON><PERSON><PERSON>", "Xucheng Ye", "Xingzhou Cheng", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Zhao"], "summary": "Training Convolutional Neural Networks (CNNs) usually requires a large number of computational resources. In this paper, SparseTrain is proposed to accelerate CNN training by fully exploiting the sparsity. It mainly involves three levels of innovations: activation gradients pruning algorithm, sparse training dataflow, and accelerator architecture. By applying a stochastic pruning algorithm on each layer, the sparsity of back-propagation gradients can be increased dramatically without degrading training accuracy and convergence rate. Moreover, to utilize both natural sparsity (resulted from ReLU or Pooling layers) and artificial sparsity (brought by pruning algorithm), a sparse-aware architecture is proposed for training acceleration. This architecture supports forward and back-propagation of CNN by adopting 1-Dimensional convolution dataflow. We have built a cycle-accurate architecture simulator to evaluate the performance and efficiency based on the synthesized design with 14nm FinFET technologies. Evaluation results on AlexNet/ResNet show that SparseTrain could achieve about 2.7× speedup and 2.2× energy efficiency improvement on average compared with the original training process.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218710"}, {"primary_key": "2565808", "vector": [], "sparse_vector": [], "title": "Clustering Approach for Solving Traveling Salesman Problems via Ising Model Based Solver.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ising model based solver have gained increasing attention due to their efficiency in finding approximate solutions for combinatorial optimization problems. However, when solving doubly constrained problems, such as traveling salesman problem using the Ising model-based solver, both the execution speed and the quality of solutions deteriorate significantly due to the quadratically increasing spin counts and strong constraints placed on the spins. In this paper, we propose a recursive clustering approach that accelerates the calculations of the Ising model and that also helps to obtain high-quality solutions. Through evaluations using the TSP benchmarks, the qualities with the proposed method have been improved by up to 67.1% and runtime were reduced by 73.8x.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218695"}, {"primary_key": "2565809", "vector": [], "sparse_vector": [], "title": "MEMTONIC: A Neuromorphic Accelerator for Energy Efficient Deep Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most deep learning accelerators in the literature focus only on improving the design of inference phase. We propose a novel photonics-based backpropagation accelerator for high performance deep learning training. The proposed MEMTONIC architecture is a first-of-its-kind memristor-integrated photonics-based deep learning architecture for end-to-end training and prediction. We evaluate the architecture using a photonic CAD framework (IPKISS) on deep learning benchmark models including LeNet and VGG-Net. The proposed design achieves at least 35× acceleration in training time, 31× improvement in computational efficiency, and 45× energy savings compared to the state-of-the-art designs, without any loss of accuracy.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218560"}, {"primary_key": "2565810", "vector": [], "sparse_vector": [], "title": "Codar: A Contextual Duration-Aware Qubit Mapping for Various NISQ Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quantum computing devices in the NISQ era share common features and challenges like limited connectivity between qubits. Since two-qubit gates are allowed on limited qubit pairs, quantum compilers must transform original quantum programs to fit the hardware constraints. Previous works on qubit mapping assume different gates have the same execution duration, which limits them to explore the parallelism from the program. To address this drawback, we propose a Multi-architecture Adaptive Quantum Abstract Machine (maQAM) and a COntext-sensitive and Duration-Aware Remapping algorithm (CODAR). The CODAR remapper is aware of gate duration difference and program context, enabling it to extract more parallelism from programs and speed up the quantum programs by 1.23 in simulation on average in different architectures and maintain the fidelity of circuits when running on Origin Quantum noisy simulator.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218561"}, {"primary_key": "2565811", "vector": [], "sparse_vector": [], "title": "Camouflage: Hardware-assisted CFI for the ARM Linux kernel.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software control-flow integrity (CFI) solutions have been applied to the Linux kernel for memory protection. Due to performance costs, deployed software CFI solutions are coarse grained. In this work, we demonstrate a precise hardware-assisted kernel CFI running on widely-used off-the-shelf processors. Specifically, we use the ARMv8.3 pointer authentication (PAuth) extension and present a design that uses it to achieve strong security guarantees with minimal performance penalties. Furthermore, we show how deployment of such security primitives in the kernel can significantly differ from their user space application.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218535"}, {"primary_key": "2565812", "vector": [], "sparse_vector": [], "title": "INVITED: AI Utopia or Dystopia - On Securing AI Platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Today we are witnessing the widespread deployment of AI algorithms on many computing platforms already to provide various services, thus driving the growing market for AI-based platforms. On the one end, AI support is demanded for resource-constrained embedded devices, e.g., integrated into smart homes and vehicles. On the other end, hi-tech giants and cloud services require AI platforms with increasing computational power to feed their data-hungry neural networks. Neglecting security and privacy aspects on both such low-end and high-end AI platforms can have devastating consequences for end users (privacy and safety) as well as for the AI service providers (IP theft). The utopia of a world where intelligent devices ease the human life can easily turn into a dystopia where the ownership of personal data is threatened.In recent years, tremendous effort has been invested in the development of security architectures that protect sensitive services in isolated execution contexts, called enclaves, which provide protection beyond that of commodity operating systems. In this paper, we elaborate on the most well-known enclave-based security architectures to protect AI services. We point out their shortcomings in providing the security guarantees needed for existing and emerging AI services and discuss new ideas and research directions.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218490"}, {"primary_key": "2565813", "vector": [], "sparse_vector": [], "title": "Autonomous Warehouse-Scale Computers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Modern Warehouse-Scale Computers (WSCs), composed of many generations of servers and a myriad of domain specific accelerators, are becoming increasingly heterogeneous. Meanwhile, WSC workloads are also becoming incredibly diverse with different communication patterns, latency requirements, and service level objectives (SLOs). Insufficient understanding of the interactions between workload characteristics and the underlying machine architecture leads to resource over-provisioning, thereby significantly impacting the utilization of WSCs. We present Autonomous Warehouse-Scale Computers, a new WSC design that leverages machine learning techniques and automation to improve job scheduling, resource management, and hardware-software co-optimization to address the increasing heterogeneity in WSC hardware and workloads. Our new design introduces two new layers in the WSC stack, namely: (a) a Software-Defined Server (SDS) Abstraction Layer which redefines the hardware-software boundary and provides greater control of the hardware to higher layers of the software stack through stable abstractions; and (b) a WSC Efficiency Layer which regularly monitors the resource usage of workloads on different hardware types, autonomously quantifies the performance sensitivity of workloads to key system configurations, and continuously improves scheduling decisions and hardware resource QoS policies to maximize cluster level performance. Our new WSC design has been successfully deployed across all WSCs at Google for several years now. The new WSC design improves throughput of workloads (by 7-10%, on average), increases utilization of hardware resources (up to 2x), and reduces performance variance for critical workloads (up to 25%).", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218509"}, {"primary_key": "2565814", "vector": [], "sparse_vector": [], "title": "RTMobile: Beyond Real-Time Mobile Acceleration of RNNs for Speech Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recurrent neural networks (RNNs) based automatic speech recognition has nowadays become promising and important on mobile devices such as smart phones. However, previous RNN compression techniques either suffer from hardware performance overhead due to irregularity or significant accuracy loss due to the preserved regularity for hardware friendliness. In this work, we propose RTMobile that leverages both a novel block-based pruning approach and compiler optimizations to accelerate RNN inference on mobile devices. Our proposed RTMobile is the first work that can achieve real-time RNN inference on mobile platforms. Experimental results demonstrate that RTMobile can significantly outperform existing RNN hardware acceleration methods in terms of both inference accuracy and time. Compared with prior work on FPGA, RTMobile using Adreno 640 embedded GPU on GRU can improve the energy-efficiency by 40× while maintaining the same inference time.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218499"}, {"primary_key": "2565815", "vector": [], "sparse_vector": [], "title": "Probabilistic Error Propagation through Approximated Boolean Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most approximate logic synthesis techniques successively apply local approximate transformations to Boolean circuits. Naturally, an efficient, robust, and scalable error estimation technique is due. This paper addresses this problem by propagating error probabilities within a network of circuits, each circuit being described by an approximated Boolean function. We specifically tackle error rate, that is, the likelihood of a logic network evaluating to an erroneous output. Our simulation-free error rate estimation technique is fully accurate when there are no mutual dependencies among signals in the Boolean network-also known as fanout-reconvergence-and shows a neglectable inaccuracy lying within 1% with respect to exhaustively simulated values for benchmark designs including signal correlations. Moreover, our methodology is capable of computing the error rate in the order of milliseconds for every tested benchmark, allowing the proposed error analysis to be applied during design space exploration. For comparison, we finally applied our methodology to a state-of-the-art approximate logic synthesis framework showing its superiority in terms of quality and runtime.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218536"}, {"primary_key": "2565816", "vector": [], "sparse_vector": [], "title": "A Formal Approach for Detecting Vulnerabilities to Transient Execution Attacks in Out-of-Order Processors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Transient execution attacks, such as <PERSON>pect<PERSON> and <PERSON><PERSON><PERSON>, create a new and serious attack surface in modern processors. In spite of all countermeasures taken during recent years, the cycles of alarm and patch are ongoing and call for a better formal understanding of the threat and possible preventions.This paper introduces a formal definition of security with respect to transient execution attacks, formulated as a HW property. We present a formal method for security verification by HW property checking based on extending Unique Program Execution Checking (UPEC) to out-of-order processors. UPEC can be used to systematically detect all vulnerabilities to transient execution attacks, including vulnerabilities unknown so far. The feasibility of our approach is demonstrated at the example of the BOOM processor, which is a design with more than 650,000 state bits. In BOOM our approach detects a new, so far unknown vulnerability, called Spectre-STC, indicating that also single-threaded processors can be vulnerable to contention-based Spectre attacks.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218572"}, {"primary_key": "2565817", "vector": [], "sparse_vector": [], "title": "Reuse-trap: Re-purposing <PERSON><PERSON> Distance to Defend against Side Channel Leakage.", "authors": ["Hongyu Fang", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern computing systems typically have multiple users sharing hardware resources. While such shared hardware have typically been performance boosters, they have also led to inadvertent side-effects such as side channels. Caches, that present the largest attack surface, have been popular among adversaries for side channel attacks. In this work, we repurpose a classic cache performance metric namely, reuse distance, to capture the activity of an adversary in cache timing channels. We design Reuse-trap, an efficient cache side channel mitigation framework to record reuse distances during victim accesses and carefully inject noise to mislead the spy from inferring the victim's activity. Our experimental results show that we can identify adversaries with zero false positives and make timing channels suffer from over 50% bit error rate on average.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218725"}, {"primary_key": "2565818", "vector": [], "sparse_vector": [], "title": "ALF: Autoencoder-based Low-rank Filter-sharing for Efficient Convolutional Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Closing the gap between the hardware requirements of state-of-the-art convolutional neural networks and the limited resources constraining embedded applications is the next big challenge in deep learning research. The computational complexity and memory footprint of such neural networks are typically daunting for deployment in resource constrained environments. Model compression techniques, such as pruning, are emphasized among other optimization methods for solving this problem. Most existing techniques require domain expertise or result in irregular sparse representations, which increase the burden of deploying deep learning applications on embedded hardware accelerators. In this paper, we propose the autoencoder-based low-rank filter-sharing technique (ALF). When applied to various networks, ALF is compared to state-of-the-art pruning methods, demonstrating its efficient compression capabilities on theoretical metrics as well as on an accurate, deterministic hardware-model. In our experiments, ALF showed a reduction of 70% in network parameters, 61% in operations and 41% in execution time, with minimal loss in accuracy.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218501"}, {"primary_key": "2565819", "vector": [], "sparse_vector": [], "title": "A Cross-Layer Power and Timing Evaluation Method for Wide Voltage Scaling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Longxing Shi"], "summary": "Wide supply voltage scaling is critical to enable worthwhile dynamic adjustment of the processor efficiency against varying workloads. In this paper, a cross-layer power and timing evaluation method is proposed to estimate the processor energy efficiency using both circuit and architectural information in a wide voltage range. The process variations are considered through statistical static timing analysis while the voltage effect is modeled through secondary iterated fittings. The error for estimating processor energy efficiency decreases to 8.29% when the supply voltage is scaled from 1.1V to 0.6V, while traditional architectural evaluations behave more than 40% errors.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218682"}, {"primary_key": "2565820", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: FRIENDS - Finding Related Interesting Events via Neighbor Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Ziv Nevo", "<PERSON><PERSON>"], "summary": "We present Finding Related Interesting Events via Neighbor Detection (FRIENDS), a novel approach to assist verification teams with coverage closure. FRIENDS uses formal verification to find the neighboring events of a never-hit, or hard-to-hit, coverage event. The neighbor events of a given target event are defined as those events for which a test hitting them has higher probability of hitting the target event than a test not hitting them. Assuming that some of the neighboring events are easier to hit than the target event itself, this information can be used by the team or by a Coverage Directed Generation (CDG) tool during the coverage closure process.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218685"}, {"primary_key": "2565821", "vector": [], "sparse_vector": [], "title": "RELIC-FUN: Logic Identification through Functional Signal Comparisons.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The ability to reverse engineer a hardware netlist in order to detect malicious logic has become an important problem in recent years. Much work has been done on algorithmically identifying structure and state in circuits; the first step of which is to separate control signals from data signals. The most current tools rely on topological comparisons of logic in order to identify signals which are uniquely structured in the netlist, as these signals are likely control signals. However, topological comparisons become less effective when a netlist has been resynthesized and optimized. We present a new tool, RELIC-FUN, based on netlist slicing and functional comparison of logic. Experimental results show that depending on netlist size, optimization, and control logic density, the proposed algorithm can be more accurate, and faster, than existing topological algorithms in many cases.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218616"}, {"primary_key": "2565822", "vector": [], "sparse_vector": [], "title": "Bit-Parallel Vector Composability for Neural Acceleration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Conventional neural accelerators rely on isolated self-sufficient functional units that perform an atomic operation while communicating the results through an operand delivery-aggregation logic. Each single unit processes all the bits of their operands atomically and produce all the bits of the results in isolation. This paper explores a different design style, where each unit is only responsible for a slice of the bit-level operations to interleave and combine the benefits of bit-level parallelism with the abundant data-level parallelism in deep neural networks. A dynamic collection of these units cooperate at runtime to generate bits of the results, collectively. Such cooperation requires extracting new grouping between the bits, which is only possible if the operands and operations are vectorizable. The abundance of Data-Level Parallelism and mostly repeated execution patterns, provides a unique opportunity to define and leverage this new dimension of Bit-Parallel Vector Composability. This design intersperses bit parallelism within data-level parallelism and dynamically interweaves the two together. As such, the building block of our neural accelerator is a Composable Vector Unit that is a collection of Narrower-Bitwidth Vector Engines, which are dynamically composed or decomposed at the bit granularity. Using six diverse CNN and LSTM deep networks, we evaluate this design style across four design points: with and without algorithmic bitwidth heterogeneity and with and without availability of a high-bandwidth off-chip memory. Across these four design points, Bit-Parallel Vector Composability brings (1.4× to 3.5×) speedup and (1.1× to 2.7×) energy reduction. We also comprehensively compare our design style to the Nvidia's RTX 2080 TI GPU, which also supports INT-4 execution. The benefits range between 28.0× and 33.7× improvement in Performance-per-Watt.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218656"}, {"primary_key": "2565823", "vector": [], "sparse_vector": [], "title": "Reverse-Engineering Deep Neural Networks Using Floating-Point Timing Side-Channels.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Trained Deep Neural Network (DNN) models have become valuable intellectual property. A new attack surface has emerged for DNNs: model reverse engineering. Several recent attempts have utilized various common side channels. However, recovering DNN parameters, weights and biases, remains a challenge. In this paper, we present a novel attack that utilizes a floating-point timing side channel to reverse-engineer parameters of multi-layer perceptron (MLP) models in software implementation, entirely and precisely. To the best of our knowledge, this is the first work that leverages a floating-point timing side-channel for effective DNN model recovery.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218707"}, {"primary_key": "2565824", "vector": [], "sparse_vector": [], "title": "Stannis: Low-Power Acceleration of DNN Training Using Computational Storage Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Computational storage devices enable in-storage processing of data in place. These devices contain 64-bit application processors and hardware accelerators that can help improving performance and saving power by reducing or eliminating data movement between host computers and storage units. This paper proposes a framework, named <PERSON><PERSON>, for distributed in-storage training of deep neural networks on clusters of computational storage devices. This in-storage processing style of training ensures that private data never leaves the storage while fully controlling the public sharing of data. The Stannis framework distributes the workload based on the processing power of each worker by determining the proper batch size for each node. <PERSON><PERSON> also ensures the availability of input data for all nodes to avoid rank stall while maximizing the utilization and overall processing speed. Experimental results show up to 2.7x speedup and 69% reduction in energy consumption with no significant loss in accuracy.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218687"}, {"primary_key": "2565825", "vector": [], "sparse_vector": [], "title": "Romeo: Conversion and Evaluation of HDL Designs in the Encrypted Domain.", "authors": ["<PERSON>", "Nektar<PERSON>"], "summary": "As cloud computing becomes increasingly ubiquitous, protecting the confidentiality of data outsourced to third parties becomes a priority. While encryption is a natural solution to this problem, traditional algorithms may only protect data at rest and in transit, but do not support encrypted processing. In this work we introduce ROMEO, which enables easy-to-use privacy-preserving processing of data in the cloud using homomorphic encryption. ROMEO automatically converts arbitrary programs expressed in Verilog HDL into equivalent homomorphic circuits that are evaluated using encrypted inputs. For our experiments, we employ cryptographic circuits, such as AES, and benchmarks from the ISCAS'85 and ISCAS'89 suites.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218579"}, {"primary_key": "2565826", "vector": [], "sparse_vector": [], "title": "Seesaw: End-to-end Dynamic Sensing for IoT using Machine Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "IoT edge devices' small form factor places tight constraints on their battery life. These devices are often equipped with multiple sensors, a few of them responsible for most of the energy usage. Naïvely lowering the sensing rate of these power-hungry sensors reduces energy consumption, but also degrades application's output quality. In this work, we observe that it is possible to leverage low-power sensors in a system to predict the impact of throttling a power-intensive sensor on application's output accuracy. We thus propose Seesaw, an end-to-end ML-based solution that automatically identifies correlations between power-intensive and lightweight sensors without human expertise. Further, Seesaw deploys a low-overhead decision tree predictor to determine the optimal sensing rates for power-intensive sensors, thus avoiding significant quality degradation. We show that Seesaw improves battery life for (1) video recording on mountable video cameras and (2) route tracking on fitness trackers by 32% and 66%, respectively, without significant accuracy loss.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218669"}, {"primary_key": "2565827", "vector": [], "sparse_vector": [], "title": "FLOPS: EFficient On-Chip Learning for OPtical Neural Networks Through Stochastic Zeroth-Order Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Optical neural networks (ONNs) have attracted extensive attention due to its ultra-high execution speed and low energy consumption. The traditional software-based ONN training, however, suffers the problems of expensive hardware mapping and inaccurate variation modeling while the current on-chip training methods fail to leverage the self-learning capability of ONNs due to algorithmic inefficiency and poor variation- robustness. In this work, we propose an on-chip learning method to resolve the aforementioned problems that impede ONNs' full potential for ultra-fast forward acceleration. We directly optimize optical components using stochastic zeroth-order optimization on-chip, avoiding the traditional high-overhead back-propagation, matrix decomposition, or in situ devicelevel intensity measurements. Experimental results demonstrate that the proposed on-chip learning framework provides an efficient solution to train integrated ONNs with 3~4× fewer ONN forward, higher inference accuracy, and better variation-robustness than previous works.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218593"}, {"primary_key": "2565828", "vector": [], "sparse_vector": [], "title": "Balancing Efficiency and Flexibility for DNN Acceleration via Temporal GPU-Systolic Array Integration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zidong Du", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The research interest in specialized hardware accelerators for deep neural networks (DNN) spikes recently owing to their superior performance and efficiency. However, today’s DNN accelerators primarily focus on accelerating specific \"kernels\" such as convolution and matrix multiplication, which are vital but only part of an end-to-end DNN-enabled application. Meaningful speedups over the entire application often require supporting computations that are, while massively parallel, ill-suited to DNN accelerators. Integrating a general-purpose processor such as a CPU or a GPU incurs significant data movement overhead and leads to resource under-utilization on the DNN accelerators.We propose Simultaneous Multi-mode Architecture (SMA), a novel architecture design and execution model that offers general-purpose programmability on DNN accelerators in order to accelerate end-to-end applications. The key to SMA is the temporal integration of the systolic execution model with the GPU-like SIMD execution model. The SMA exploits the common components shared between the systolic-array accelerator and the GPU, and provides lightweight reconfiguration capability to switch between the two modes in-situ. The SMA achieves up to 63% performance improvement while consuming 23% less energy than the baseline Volta architecture with TensorCore.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218732"}, {"primary_key": "2565829", "vector": [], "sparse_vector": [], "title": "An Efficient Critical Path Generation Algorithm Considering Extensive Path Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we introduce a fast and efficient critical path generation algorithm considering extensive path constraints on a Static Timing Analysis (STA) graph. Critical path generation is a key routine in the inner loop of path-based analysis and timing-driven synthesis flows. Our algorithm can report arbitrary numbers of critical paths on a logic cone constrained by a sequence of from/through/to pins under different min/max modes and rise/fall transitions. Our algorithm is general, efficient, and provably good. Experimental results have showed that our algorithm produces reports that matches a golden reference generated by an industrial signoff timer. Our results also correlate to a commercial timer yet achieving more than an order of magnitude speed-up.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218750"}, {"primary_key": "2565830", "vector": [], "sparse_vector": [], "title": "Tail: An Automated and Lightweight Gradient Compression Framework for Distributed Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Hu", "<PERSON>", "<PERSON><PERSON>", "Jizhong Han", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing gradient compression schemes fail to automatically determine the compression ratio or are accompanied by high compression overhead. To address this, we present Tail, an automated and lightweight gradient compression framework stacked by three modules, quantization, sparsification, and encoding. Without any hand-tuned effort, quantization module automatically adjusts the compression ratio along training iterations to retain accuracy first. Then, sparsification and encoding modules are successively applied to the quantized gradient to further improve compression ratio. Moreover, <PERSON>l reduces the compression overhead by approximate computing in the automated decision-making process. Experiments validate that <PERSON><PERSON> can reduce communication traffic by an order of magnitude while retaining or even improving model accuracy.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218587"}, {"primary_key": "2565831", "vector": [], "sparse_vector": [], "title": "Analysis and Optimization of the Implicit Broadcasts in FPGA HLS to Improve Maximum Frequency.", "authors": ["Licheng Guo", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Designs generated by high-level synthesis (HLS) tools typically achieve a lower frequency compared to manual RTL designs. In this work, we study the timing issues in a diverse set of realistic and complex FPGA HLS designs. (1) We observe that in almost all cases the frequency degradation is caused by the broadcast structures generated by the HLS compiler. (2) We classify three major types of broadcasts in HLS-generated designs, including high-fanout data signals, pipeline flow control signals and synchronization signals for concurrent modules. (3) We reveal a number of limitations of the current HLS tools that result in those broadcast-related timing issues. (4) We propose a set of effective yet easy-to-implement approaches, including broadcast-aware scheduling, synchronization pruning, and skid-buffer-based flow control. Our experimental results show that our methods can improve the maximum frequency of a set of nine representative HLS benchmarks by 53% on average. In some cases, the frequency gain is more than 100 MHz.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218718"}, {"primary_key": "2565832", "vector": [], "sparse_vector": [], "title": "Building End-to-End IoT Applications with QoS Guarantees.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many industrial players are currently challenged in building distributed CPS and IoT applications with stringent end-to-end QoS requirements. Examples are Vehicle-to-X applications, Advanced Driver-Assistance Systems (ADAS) or functionalities in the Industrial Internet of Things (IIoT). Currently, there is no comprehensive solution allowing to efficiently program, deploy, and operate such distributed applications. This paper will focus on real-time concerns, in building distributed CPS and IoT systems. Thereby, the focus lies, on the one hand, on mechanisms required inside of the IoT (compute) nodes, and, on the other hand, on communication protocols such as TSN and 5G connecting them. In the authors' view, the required building blocks for a first end-to-end technology stack are available. However, their integration into a holistic framework is missing.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218564"}, {"primary_key": "2565833", "vector": [], "sparse_vector": [], "title": "GUI-Enhanced Layout Generation of FFE SST TXs for Fast High-Speed Serial Link Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hong-June Park", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present the first FFE SST TX layout generator enhanced by various software techniques including a GUI-based template engine. Seven different DRC/LVS-clean TXs were generated in multiple technologies (40nm/65nm/90nm CMOS) for the first time, and achieved adequate maximum data rates: 36Gb/s with 40nm in post-layout simulation; 14Gb/s with 65nm in measurement. Total generation time was less than 5 days, including iterative parameter tuning by a human designer and computation (30 minutes for TX core, 8 hours for power network). Fast post-layout analysis of TX's performance-power trade-off was enabled by the presented generator for the first time.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218723"}, {"primary_key": "2565834", "vector": [], "sparse_vector": [], "title": "PEMACx: A Probabilistic Error Analysis Methodology for Adders with Cascaded Approximate Units.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we propose a novel methodology for efficiently computing the Probability Mass Function (PMF) of error at the output of a major class of approximate adders that comprise of a cascade of multiple stages of smaller approximate adder units. The proposed methodology utilizes the carry-out probability of the previous stage along with the input probabilities of the current stage to recursively computes the PMF of error of the current stage. The proposed methodology eliminates the need for exhaustive simulations and, therefore, can be used for efficiently analyzing error distribution of a wide variety of low-power large bit-width adders with cascaded approximate adder units. Experimental results demonstrate that the proposed methodology provides error estimates that commensurate with exhaustive simulations, while offering a speedup of at least 2958x for 8-bit (or larger) approximate adder configurations.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218678"}, {"primary_key": "2565835", "vector": [], "sparse_vector": [], "title": "Runtime Trust Evaluation and Hardware Trojan Detection Using On-Chip EM Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yanjiang Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It has been widely demonstrated that the utilization of postdeployment trust evaluation approaches, such as side-channel measurements, along with statistical analysis methods is effective for detecting hardware Trojans in fabricated integrated circuits (ICs). However, more sophisticated Trojans proposed recently invalidate these methods with stealthy triggers and very-low side-channel signatures. Upon these challenges, in this paper, we propose an electromagnetic (EM) side-channel based post-fabrication trust evaluation framework which monitors EM radiations at runtime. The key component of the runtime trust evaluation framework is an on-chip EM sensor which can constantly measure and collect EM side-channel information of the target circuit. The simulation results validate the capability of the proposed framework in detecting stealthy hardware Trojans. Further, we fabricate an AES circuit protected by the proposed trust evaluation framework along with four different types of hardware Trojans. The measurements on the fabricated chips prove two key findings. First, the on-chip EM sensor can achieve a higher signal to noise ratio (SNR) and thus facilitate a better Trojan detection accuracy. Second, the trust evaluation framework can help detect different hardware Trojans at runtime.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218514"}, {"primary_key": "2565836", "vector": [], "sparse_vector": [], "title": "Towards State-Aware Computation in ReRAM Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Resistive RAM (ReRAM) is a promising device to realize the Computing in Memory (CiM) architecture, suitable for power-constrained IoT systems. Because of low leakage, the dot-production operations in ReRAM crossbars dominate the chip power, especially when implementing low-precision neural networks. This work investigates the correlation between the cell resistance state and the crossbar operation power, and proposes a State-Aware ReRAM Accelerator (SARA) architecture for energy-efficient neural networks. With the proposed state-aware network training and mapping strategy, crossbars in the ReRAM accelerator can perform in a lower-power state. The evaluation shows that our design reduces 47% energy over the baseline without compromising the network accuracy.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218729"}, {"primary_key": "2565837", "vector": [], "sparse_vector": [], "title": "Exploring Inherent Sensor Redundancy for Automotive Anomaly Detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Fanxin Kong", "<PERSON><PERSON>"], "summary": "The increasing autonomy and connectivity have been transitioning automobiles to complex and open architectures that are vulnerable to malicious attacks beyond conventional cyber attacks. Attackers may non-invasively compromise sensors and spoof the controller to perform unsafe actions. This concern emphasizes the need to validate sensor data before acting on them. Unlike existing works, this paper exploits inherent redundancy among heterogeneous sensors for detecting anomalous sensor measurements. The redundancy is that multiple sensors simultaneously respond to the same physical phenomenon in a related fashion. Embedding the redundancy into a deep autoencoder, we propose an anomaly detector that learns a consistent pattern from vehicle sensor data in normal states and utilizes it as the nominal behavior for the detection. The proposed method is independent of the scarcity of anomalous data for training and the intensive calculation of pairwise correlation among senors as in existing works. Using a real-world data set collected from tens of vehicle sensors, we demonstrate the feasibility and efficacy of the proposed method.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218557"}, {"primary_key": "2565838", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Can You Hear Me? Towards an Ultra Low-Cost Hearing Screening Device.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hearing screening devices emit an acoustic signal in the outer ear, which invokes a specific response from a healthy inner ear. However, the high cost of such devices prevents widely deploying them in schools or private homes, especially in developing countries. In this paper, we for the first time show that such tests are also feasible with a device that consists of only one speaker for emitting the signal and using the same speaker - now as a microphone - for also recording the response. Existing devices rely on a speaker and microphone pair, which makes them significantly more complex and costly. We further outline the embedded systems and signal processing challenges that such a setup entails. If successful, it has the potential to make hearing screening available to a much wider population in developing countries.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218597"}, {"primary_key": "2565839", "vector": [], "sparse_vector": [], "title": "CAP&apos;NN: Class-Aware Personalized Neural Network Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose CAP'NN, a framework for Class-Aware Personalized Neural Network Inference. CAP'NN prunes an already-trained neural network model based on the preferences of individual users. Specifically, by adapting to the subset of output classes that each user is expected to encounter, CAP'NN is able to prune not only ineffectual neurons but also miseffectual neurons that confuse classification, without the need to retrain the network. CAP'NN achieves up to 50% model size reduction while actually improving the top-l(5) classification accuracy by up to 2.3%(3.2%) when the user only encounters a subset of VGG-16 classes.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218741"}, {"primary_key": "2565840", "vector": [], "sparse_vector": [], "title": "Closing the RISC-V Compliance Gap: Looking from the Negative Testing Side*.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Compliance testing for RISC-V is very important. Therefore, an official hand-written compliance test-suite is being actively developed. However, besides requiring significant manual effort, it focuses on positive testing (the implemented instructions work as expected) only and neglects negative testing (consider illegal instructions to also ensure that no additional/unexpected behavior is accidentally added). This leaves a large gap in compliance testing. In this paper we propose a fuzzing-based test-suite generation approach to close this gap. We found new bugs in several RISC-V simulators including riscvOVPsim from Imperas which is the official reference simulator for compliance testing.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218629"}, {"primary_key": "2565841", "vector": [], "sparse_vector": [], "title": "Deep Learning Multi-Channel Fusion Attack Against Side-Channel Protected Hardware.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "State-of-the-art hardware masking approaches like threshold implementations and domain-oriented masking provide a guaranteed level of security even in the presence of glitches. Although provable secure in theory, recent work showed that the effective security order of a masked hardware implementation can be lowered by applying a multi-probe attack or exploiting externally amplified coupling effects. However, the proposed attacks are based on an unrealistic adversary model (i.e. knowledge of masks values during profiling) or require complex measurement setup manipulations.In this work, we propose a novel attack vector that exploits location dependent leakage from several decoupling capacitors of a modern System-on-Chip (SoC) with 16 nm fabrication technology. We combine the leakage from different sources using a deep learning-based information fusion approach. The results show a remarkable advantage regarding the number of required traces for a successful key recovery compared to state-of-the-art profiled side-channel attacks. All evaluations are performed under realistic conditions, resulting in a real-world attack scenario that is not limited to academic environments.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218705"}, {"primary_key": "2565842", "vector": [], "sparse_vector": [], "title": "Just Like the Real Thing: Fast Weak Simulation of Quantum Computation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Quantum computers promise significant speedups in solving problems intractable for conventional computers but, despite recent progress, remain limited in scaling and availability. Therefore, quantum software and hardware development heavily rely on simulation that runs on conventional computers. Most such approaches perform strong simulation in that they explicitly compute amplitudes of quantum states. However, such information is not directly observable from a physical quantum computer because quantum measurements produce random samples from probability distributions defined by those amplitudes. In this work, we focus on weak simulation that aims to produce outputs which are statistically indistinguishable from those of error-free quantum computers. We develop algorithms for weak simulation based on quantum state representation in terms of decision diagrams. We compare them to using state-vector arrays and binary search on prefix sums to perform sampling. Empirical validation shows, for the first time, that this enables mimicking of physical quantum computers of significant scale.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218555"}, {"primary_key": "2565843", "vector": [], "sparse_vector": [], "title": "O-2A: Low Overhead DNN Compression with Outlier-Aware Approximation.", "authors": ["Nguyen<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a low-latency DNN compression technique to reduce DRAM energy, significant in DNN inferences, namely Outlier-Aware Approximation (O-2A) coding. This technique compresses 8-bit integer, de-facto standard of DNN inferences, to 6-bit without degrading the accuracies of DNNs. The hardware for the O-2A coding can be easily embedded to DRAM controllers due to small overhead. In an Eyeriss platform, the O-2A coding improves both DRAM energy and system performance by 18~20%. The O-2A coding enables us to implement an error-correction scheme without additional parity overhead, opening the possibility of an approximate DRAM to simultaneously reduce DRAM accessing and refresh energy.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218594"}, {"primary_key": "2565844", "vector": [], "sparse_vector": [], "title": "BrezeFlow: Unified Debugger for Android CPU Power Governors and Schedulers on Edge Devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Power management is quintessential to the successful deployment of edge devices, such as smartphones, in power-, thermal-, and energy-constrained environments. Governors and schedulers operate system sub-routines for power management at the edge. There exist several tools for debugging power issues in Android applications. However, there exists no tool to identify and classify inevitable misdecisions by power managers, given their often inefficient underlying heuristics. In this work, we introduce the first tool - BrezeFlow - designed for unified (scheduling and frequency scaling) power debugging of CPU power managers on Android edge devices. BrezeFlow enables kernel developers to evaluate designs of their power managers retrospectively with closed-source applications in real-world scenarios based on any user-defined strategy and thereby gain insights for better future governor designs. BrezeFlow detected an average of 815 misdecisions per second for the commonly deployed duo, ondemand governor and Completely Fair Scheduler, on mobile edge devices running popular applications.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218542"}, {"primary_key": "2565845", "vector": [], "sparse_vector": [], "title": "Realistic Fault Models and Fault Simulation for Quantum Dot Quantum Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Testing for quantum circuits (QC) is a challenging task because QC is intrinsically probabilistic. Existing fault models for QC, such as missing gate faults, are not suitable for quantum dot QC. This paper proposes realistic fault models and fault simulation for quantum dot QC. Our fault models are based on real physical phenomenon of quantum dot devices so that they represent real defect behavior or control errors. Our fault simulation does not need to fully expand gate matrices to 2 n x 2 n , where n is the number of qubits. Using sparse matrix multiplication, our fault simulation saves a lot of memory and CPU time. We also calculate the test repetition of each test pattern so that we can estimate our test time. Based on fault simulation of a full adder QC, we can select a small test set of six test patterns, totally 526 repetitions, to detect all faults with 99% confidence level.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218573"}, {"primary_key": "2565846", "vector": [], "sparse_vector": [], "title": "Adjoint Transient Sensitivity Analysis for Objective Functions Associated to Many Time Points.", "authors": ["<PERSON><PERSON><PERSON>", "Zuo<PERSON> Ye", "<PERSON>"], "summary": "Transient sensitivity is useful for analyzing the gradients of objective functions with respect to given parameters. This is useful in variation and circuit optimization. The computational complexity for traditional transient sensitivity analysis for objective function with N time points is O(N 2 ), which is too expensive for large N. In this paper we propose a transient sensitivity analysis method that reduces the computational complexity to O(N), enabling the analysis of performance metrices related to thousands of time points, such as SNDR, THD, SFDR, and etc.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218602"}, {"primary_key": "2565847", "vector": [], "sparse_vector": [], "title": "Latch Clustering for Timing-Power Co-Optimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Latch clustering is a critical stage to reduce power consumption at cost of timing disruption during a modern SoC design flow. However, most existing latch clustering researches mitigate timing disruptions by indirectly minimizing latch displacement during clustering, which is inaccurate and insufficient for timing closure in the design flow. Further, most researches do not control the amount of inserted clock buffers during clustering, which is the key factor to provide flexibility for timing and power trade-off. To address the two issues above, this paper presents a novel timing-power co-optimized latch clustering framework: we augment an integer linear programming (ILP) formulation of a facility-location allocation (FLA) problem to (1) directly optimize timing with a path-based timing model and (2) accurately control the number of inserted buffers by the FLA formulation for power optimization. We evaluate the framework with a displacement-optimized clustering approach and a state-of-the-art approach. Experimental results show 46% total negative slack timing overhead reduction, and 21% reduction for total power consumption.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218617"}, {"primary_key": "2565848", "vector": [], "sparse_vector": [], "title": "On Countermeasures Against the Thermal Covert Channel Attacks Targeting Many-core Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Although it has been demonstrated in multiple studies that serious data leaks could occur to many-core systems thanks to the existence of the thermal covert channels (TCC), little has been done to produce effective countermeasures that are necessary to fight against such TCC attacks. In this paper, we propose a three-step countermeasure to address this critical defense issue. Specifically, the countermeasure includes detection based on signal frequency scanning, positioning affected cores, and blocking based on Dynamic Voltage Frequency Scaling (DVFS) technique. Our experiments have confirmed that on average 98% of the TCC attacks can be detected, and with the proposed defense, the bit error rate of a TCC attack can soar to 92%, literally shutting down the attack in practical terms. The performance penalty caused by the inclusion of the proposed countermeasures is only 3% for an 8×8 system.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218648"}, {"primary_key": "2565849", "vector": [], "sparse_vector": [], "title": "Opportunistic Intermittent Control with Safety Guarantees for Autonomous Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Control schemes for autonomous systems are often designed in a way that anticipates the worst case in any situation. At runtime, however, there could exist opportunities to leverage the characteristics of specific environment and operation context for more efficient control. In this work, we develop an online intermittent-control framework that combines formal verification with model-based optimization and deep reinforcement learning to opportunistically skip certain control computation and actuation to save actuation energy and computational resources without compromising system safety. Experiments on an adaptive cruise control system demonstrate that our approach can achieve significant energy and computation savings.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218742"}, {"primary_key": "2565850", "vector": [], "sparse_vector": [], "title": "Unified Architectural Support for Secure and Robust Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent advances in Deep Learning (DL) have enabled a paradigm shift to include machine intelligence in a wide range of autonomous tasks. As a result, a largely unexplored surface has opened up for attacks jeopardizing the integrity of DL models and hindering the success of autonomous systems. To enable ubiquitous deployment of DL approaches across various intelligent applications, we propose to develop architectural support for hardware implementation of secure and robust DL. Towards this goal, we leverage hardware/software co-design to develop a DL execution engine that supports algorithms specifically designed to defend against various attacks. The proposed framework is enhanced with two real-time defense mechanisms, securing both DL training and execution stages. In particular, we enable model-level Trojan detection to mitigate backdoor attacks and malicious behaviors induced on the DL model during training. We further realize real-time adversarial attack detection to avert malicious behavior during execution. The proposed execution engine is equipped with hardware-level IP protection and usage control mechanism to attest the legitimacy of the DL model mapped to the device. Our design is modular and can be tuned to task-specific demands, e.g., power, throughput, and memory bandwidth, by means of a customized hardware compiler. We further provide an accompanying API to reduce the nonrecurring engineering cost and ensure automated adaptation to various domains and applications.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218575"}, {"primary_key": "2565851", "vector": [], "sparse_vector": [], "title": "PETNet: Polycount and Energy Trade-off Deep Networks for Producing 3D Objects from Images.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the task of predicting 3D object shapes from color images on mobile platforms, which has many real-world applications including augmented reality (AR), virtual reality (VR), and robotics. Recent work has developed a Graph Convolution Network (GCN) based approach to produce 3D object shapes in the form of a triangular mesh of increasing polycount (no. of triangles in the mesh). In this paper, we propose a novel approach to trade-off polycount of a 3D object shape for the energy consumed at run-time called Polycount-Energy Trade-off networks (PETNet). The key idea behind PETNets is to design an architecture of increasing complexity with a comparator module and leveraging the pre-trained GCN to perform input-specific adaptive predictions. We perform experiments using pre-trained GCN on the ShapeNet dataset. Results show that with the optimized PETNets, we can get up to 20%-37% gain in energy for negligible loss (0.01 to 0.02) in accuracy, and provides a fine-grained control on performance when compared to a fixed level performance with the state-of-the-art Pixel2Mesh network.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218525"}, {"primary_key": "2565852", "vector": [], "sparse_vector": [], "title": "PAIR: Pin-aligned In-DRAM ECC architecture using expandability of Reed-Solomon code.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The computation speed of computer systems is getting faster and the memory has been enhanced in performance and density through process scaling. However, due to the process scaling, DRAMs are recently suffering from numerous inherent faults. DRAM vendors suggest In-DRAM Error Correcting Code (IECC) to cope with the unreliable operation. However, the conventional IECC schemes have concerns about miscorrection and performance degradation. This paper proposes a pin-aligned In-DRAM ECC architecture using the expandability of a Reed-Solomon code (PAIR), that aligns ECC codewords with DQ pin lines (data passage of DRAM). PAIR is specialized in managing widely distributed inherent faults without the performance degradation, and its correction capability is sufficient to correct burst errors as well. The experimental results analyzed with the latest DRAM model show that the proposed architecture achieves up to 10 6 times higher reliability than XED with 14% performance improvement, and 10 times higher reliability than DUO with a similar performance, on average.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218745"}, {"primary_key": "2565853", "vector": [], "sparse_vector": [], "title": "Learning Concise Models from Long Execution Traces.", "authors": ["<PERSON>", "Thomas <PERSON>", "<PERSON>", "John <PERSON>;<PERSON>"], "summary": "Abstract models of system-level behaviour have applications in design exploration, analysis, testing and verification. We describe a new algorithm for automatically extracting useful models, as automata, from execution traces of a HW/SW system driven by software exercising a use-case of interest. Our algorithm leverages modern program synthesis techniques to generate predicates on automaton edges, succinctly describing system behaviour. It employs trace segmentation to tackle complexity for long traces. We learn concise models capturing transaction-level, system-wide behaviour-experimentally demonstrating the approach using traces from a variety of sources, including the x86 QEMU virtual platform and the Real-Time Linux kernel.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218613"}, {"primary_key": "2565854", "vector": [], "sparse_vector": [], "title": "Transfer Learning-Based Microfluidic Design System for Concentration Generation∗.", "authors": ["Weiqing Ji", "Tsung-<PERSON>", "<PERSON><PERSON>"], "summary": "Due to the complexity of human physiology and variability among individuals, e.g., genes, environment, lifestyle exposures, etc., personalized medicine has attracted great interest in the past few years. For synthesizing personalized medicine, it is critical to prepare customized samples with specific concentrations by microfluidic biochips because of the advantages in saving costly reagents and rare samples. The current state-of-the-art of concentration generation for microfluidic biochips is to construct a database by random design methods. However, due to the complex multidimentional parameters such as molecule diameters, inlets, outlets, etc, the whole process is error prone and time consuming. To speedup database construction and reduce the errors in concentration generation, this paper proposes the first transfer learning-based method based on an artificial neural network model (ANN). Given an initial ANN model, transfer learning method can fine-tune weights of ANN to obtain all ANN models needed in the database, which can significantly reduce the amount of required training data. Computational simulation results show that the time for database construction is reduced from several months to 2 days, and the query error is reduced by 83% compared with the existing method.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218722"}, {"primary_key": "2565855", "vector": [], "sparse_vector": [], "title": "A Two-way SRAM Array based Accelerator for Deep Neural Network On-chip Training.", "authors": ["Hongwu Jiang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "On-chip training of large-scale deep neural networks (DNNs) is challenging due to computational complexity and resource limitation. Compute-in-memory (CIM) architecture exploits the analog computation inside the memory array to speed up the vectormatrix multiplication (VMM) and alleviate the memory bottleneck. However, existing CIM prototype chips, in particular, SRAM-based accelerators target at implementing low-precision inference engine only. In this work, we propose a two-way SRAM array design that could perform bi-directional in-memory VMM with minimum hardware overhead. A novel solution of signed number multiplication is also proposed to handle the negative input in backpropagation. We taped-out and validated proposed two-way SRAM array design in TSMC 28nm process. Based on the silicon measurement data on CIM macro, we explore the hardware performance for the entire architecture for DNN on-chip training. The experimental data shows that proposed accelerator can achieve energy efficiency of ~3.2 TOPS/W, >1000 FPS and >300 FPS for ResNet and DenseNet training on ImageNet, respectively.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218524"}, {"primary_key": "2565856", "vector": [], "sparse_vector": [], "title": "TEVoT: Timing Error Modeling of Functional Units under Dynamic Voltage and Temperature Variations.", "authors": ["<PERSON><PERSON>", "Dongning Ma", "<PERSON><PERSON>", "Yu <PERSON>"], "summary": "With the continuous scaling of CMOS technology, microelectronic circuits are increasingly susceptible to micro-electronic variations such as variations in operating conditions. Such variations can cause delay uncertainty in microelectronic circuits, leading to timing errors. Circuit designers typically combat these errors using conservative guardbands in the circuit and architectural design, which can, however, cause significant loss of operational efficiency. In this paper, we propose TEVoT, a supervised learning model that can predict the timing errors of functional units (FUs) under different operating conditions, clock speeds, and input workload. We perform dynamic timing analysis to characterize the delay variations of FUs under different conditions, based on which we collect training data. We then extract useful features from training data and apply supervised learning methods to establish TEVoT. Across 100 different operating conditions, 4 widely-used FUs, 3 clocking speeds, and 3 datasets, TEVoT achieves an average prediction accuracy at 98.25% and is 100X faster than gate-level simulation. We further use TEVoT to estimate application output quality under different operating conditions by exposing circuit-level timing errors to application level. TEVoT achieves an average estimation accuracy at 97% for two image processing applications across 100 operating conditions.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218588"}, {"primary_key": "2565857", "vector": [], "sparse_vector": [], "title": "The Tao of PAO: Anatomy of a Pin Access Oracle for Detailed Routing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Pin accessibility has been widely studied, particularly in recent works that span detailed placement optimization, standard cell layout optimization and new design rule-aware access model. However, to our knowledge, no previous work has described a full solution for pin access analysis, with validations on real detailed routing benchmarks. This paper presents a complete, robust, scalable and design rule-aware dynamic programming-based pin access analysis framework that is capable of both standard cell-based and instance-based pin access analysis. Integration into the open-source TritonRoute router results in superior solution quality compared to previous best-known results for the official ISPD-2018 benchmark suite.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218532"}, {"primary_key": "2565858", "vector": [], "sparse_vector": [], "title": "Tensor Virtualization Technique to Support Efficient Data Reorganization for CNN Accelerators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There is a growing need for data reorganization in recent neural networks for various applications such as Generative Adversarial Networks(GANs) that use transposed convolution and U-Net that requires upsampling. We propose a novel technique, called tensor virtualization technique, to perform data reorganization efficiently with a minimal hardware addition for adder-tree based CNN accelerators. In the proposed technique, a data reorganization request is specified with a few parameters and data reorganization is performed in the virtual space without overhead in the physical memory. It allows existing adder-tree-based CNN accelerators to accelerate a wide range of neural networks that require data reorganization, including U-Net, DCGAN, and SRGAN.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218726"}, {"primary_key": "2565859", "vector": [], "sparse_vector": [], "title": "A Device Non-Ideality Resilient Approach for Mapping Neural Networks to Crossbar Arrays.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Xiao<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a technology-independent method, referred to as adjacent connection matrix (ACM), to efficiently map signed weight matrices to non-negative crossbar arrays. When compared to same-hardware-overhead mapping methods, using ACM leads to improvements of up to 20% in training accuracy for ResNet-20 with the CIFAR-10 dataset when training with 5-bit precision crossbar arrays or lower. When compared with strategies that use two elements to represent a weight, ACM achieves comparable training accuracies, while also offering area and read energy reductions of 2.3× and 7×, respectively. ACM also has a mild regularization effect that improves inference accuracy in crossbar arrays without any retraining or costly device/variation-aware training.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218544"}, {"primary_key": "2565860", "vector": [], "sparse_vector": [], "title": "Prive-HD: Privacy-Preserved Hyperdimensional Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The privacy of data is a major challenge in machine learning as a trained model may expose sensitive information of the enclosed dataset. Besides, the limited computation capability and capacity of edge devices have made cloud-hosted inference inevitable. Sending private information to remote servers makes the privacy of inference also vulnerable because of susceptible communication channels or even untrustworthy hosts. In this paper, we target privacy-preserving training and inference of brain-inspired Hyperdimensional (HD) computing, a new learning algorithm that is gaining traction due to its light-weight computation and robustness particularly appealing for edge devices with tight constraints. Indeed, despite its promising attributes, HD computing has virtually no privacy due to its reversible computation. We present an accuracy-privacy trade-off method through meticulous quantization and pruning of hypervectors, the building blocks of HD, to realize a differentially private model as well as to obfuscate the information sent for cloud-hosted inference. Finally, we show how the proposed techniques can be also leveraged for efficient hardware implementation.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218493"}, {"primary_key": "2565861", "vector": [], "sparse_vector": [], "title": "Learning to Quantize Deep Neural Networks: A Competitive-Collaborative Approach.", "authors": ["<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Reducing the model size and computation costs for dedicated AI accelerator designs, neural network quantization methods have attracted momentous attention recently. Unfortunately, merely minimizing quantization loss using constant discretization causes accuracy deterioration. In this paper, we propose an iterative accuracy-driven learning framework of competitive-collaborative quantization (CCQ) to gradually adapt the bit-precision of each individual layer. Orthogonal to prior quantization policies working with full precision for the first and last layers of the network, CCQ offers layer-wise competition for any target quantization policy with holistic layer fine-tuning to recover accuracy, where the state-of-the-art networks can be entirely quantized without any significant accuracy degradation.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218576"}, {"primary_key": "2565862", "vector": [], "sparse_vector": [], "title": "SHIELDeNN: Online Accelerated Framework for Fault-Tolerant Deep Neural Network Architectures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose SHIELDeNN, an end-to-end inference accelerator frame-work that synergizes the mitigation approach and computational resources to realize a low-overhead error-resilient Neural Network (NN) overlay. We develop a rigorous fault assessment paradigm to delineate a ground-truth fault-skeleton map for revealing the most vulnerable parameters in NN. The error-susceptible parameters and resource constraints are given to a function to find superior design. The error-resiliency magnitude offered by SHIELDeNN can be adjusted based on the given boundaries. SHIELDeNN methodology improves the error-resiliency magnitude of cnvW1A1 by 17.19% and 96.15% for 100 MBUs that target weight and activation layers, respectively.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218697"}, {"primary_key": "2565863", "vector": [], "sparse_vector": [], "title": "Navigator: Dynamic Multi-kernel Scheduling to Improve GPU Performance.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Yongjun Park"], "summary": "Efficient GPU resource-sharing between multiple kernels has recently been a critical factor on overall performance. While previous works mainly focused on how to allocate resources to two kernels, there has been limited amount of work on determining which workloads to concurrently execute among multiple workloads. Therefore, we first demonstrate on a real GPU system how the selection of concurrent workloads can have significant impact on overall performance. We then propose GPU Navigator – a lookup-table-based dynamic multi-kernel scheduler that maximizes overall performance through online profiling. Our evaluation shows that GPU Navigator outperforms a greedy policy by 29.3% on average.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218711"}, {"primary_key": "2565864", "vector": [], "sparse_vector": [], "title": "Algorithm/Hardware Co-Design for In-Memory Neural Network Computing with Minimal Peripheral Circuit Overhead.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose an in-memory neural network accelerator architecture called MOSAIC which uses minimal form of peripheral circuits; 1-bit word line driver to replace DAC and 1-bit sense amplifier to replace ADC. To map multi-bit neural networks on MOSAIC architecture which has 1-bit precision peripheral circuits, we also propose a bit-splitting method to approximate the original network by separating each bit path of the multi-bit network so that each bit path can propagate independently throughout the network. Thanks to the minimal form of peripheral circuits, MOSAIC can achieve an order of magnitude higher energy and area efficiency than previous in-memory neural network accelerators.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218657"}, {"primary_key": "2565865", "vector": [], "sparse_vector": [], "title": "TrojDRL: Evaluation of Backdoor Attacks on Deep Reinforcement Learning.", "authors": ["Panagiota Kiourti", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present TrojDRL, a tool for exploring and evaluating backdoor attacks on deep reinforcement learning agents. TrojDRL exploits the sequential nature of deep reinforcement learning (DRL) and considers different gradations of threat models. We show that untargeted attacks on state-of-the-art actor-critic algorithms can circumvent existing defenses built on the assumption of backdoors being targeted. We evaluated TrojDRL on a broad set of DRL benchmarks and showed that the attacks require only poisoning as little as 0.025% of the training data. Compared with existing works of backdoor attacks on classification models, TrojDRL provides a first step towards understanding the vulnerability of DRL agents.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218663"}, {"primary_key": "2565866", "vector": [], "sparse_vector": [], "title": "TAEM: Fast Transfer-Aware Effective Loop Mapping for Heterogeneous Resources on CGRA.", "authors": ["<PERSON><PERSON>", "Jiangyuan Gu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Coarse-grained reconfigurable architecture (CGRA) is an energy-efficient and processing-flexible parallel computing architecture. Efficiency of CGRA highly depends on how to map data dependencies using different CGRA resources. Previous works investigated different strategies for transferring data dependencies, using registers, processing elements (PEs) and memory. However, these works do not consider all those resources in CGRA and take a long time during compilation period. This paper proposes a Transfer-Aware Effective loop Mapping (TAEM) method for CGRA, which can efficiently utilize all those heterogeneous resources on CGRA and significantly accelerate the compilation time. Experimental results show that TAEM is able to reduce the compilation time by 11.1x over the state-of-the-art technique RAMP, while keeping the same or better performance of loop mapping results.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218668"}, {"primary_key": "2565867", "vector": [], "sparse_vector": [], "title": "ATUNs: Modular and Scalable Support for Atomic Operations in a Shared Memory Multiprocessor.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Atomic operations are crucial for most modern parallel and concurrent algorithms, which necessitates their optimized implementation in highly-scalable manycore processors. We pro-pose a modular and efficient, open-source ATomic UNit (ATUN) architecture that can be placed flexibly at different levels of the memory hierarchy. ATUN demonstrates near-optimal linear scaling for various synthetic and real-world workloads on an FPGA prototype with 32 RISC-V cores. We characterize the hardware complexity of our ATUN design in 22 nm FDSOI and find that it scales linearly in area (only 0.5 kGE per core) and logarithmically in the critical path.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218661"}, {"primary_key": "2565868", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Automated Hardware Generation of CNN Models on FPGAs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we propose an automated framework that takes as input a TensorFlow inference graph and generates high-performance accelerators on FPGA by assembling CNN pre-implemented components as a puzzle, based on the graph topology. Using pre-implemented components allows us the only use the minimum of resources necessary, predict the performance and a gain in productivity We adopt a unified representation based on systolic array to perform the computational-hungry operations of the model and provide novel analysis of design trade-offs for FPGA CNN accelerators. Experimental results show the great performance, low latency and flexibility provided by the proposed framework.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218692"}, {"primary_key": "2565869", "vector": [], "sparse_vector": [], "title": "Scalable Multi-FPGA Acceleration for Large RNNs with Full Parallelism Levels.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>on Hur", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The increasing size of recurrent neural networks (RNNs) makes it hard to meet the growing demand for real-time AI services. For low-latency RNN serving, FPGA-based accelerators can leverage specialized architectures with optimized dataflow. However, they also suffer from severe HW under-utilization when partitioning RNNs, and thus fail to obtain the scalable performance.In this paper, we identify the performance bottlenecks of existing RNN partitioning strategies. Then, we propose a novel RNN partitioning strategy to achieve the scalable multi-FPGA acceleration for large RNNs. First, we introduce three parallelism levels and exploit them by partitioning weight matrices, matrix/vector operations, and layers. Second, we examine the performance impact of collective communications and software pipelining to derive more accurate and optimal distribution results. We prototyped an FPGA-based acceleration system using multiple Intel high-end FPGAs, and our partitioning scheme allows up to 2.4x faster inference of modern RNN workloads than conventional partitioning methods.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218528"}, {"primary_key": "2565870", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Reinforcement Learning-based Power Management Policy for Mobile Device Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Sodam Han", "Yoonho Park", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents a power management policy that exploits reinforcement learning to increase power efficiency of mobile device systems. Our Q-learning-based policy predicts a system's characteristics and learns power management controls to adapt to the system's variations. Therefore, we can flexibly manage the system power regardless of the application scenario and can achieve lower energy per QoS compared to previous dynamic voltage/frequency scaling governors. To minimize the process overhead, we implemented our power management policy as hardware; the hardware-implemented policy reduced the average latency up to 40× compared to the software-implemented policy.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218716"}, {"primary_key": "2565871", "vector": [], "sparse_vector": [], "title": "FlexReduce: Flexible All-reduce for Distributed Deep Learning on Asymmetric Network Topology.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose FlexReduce, an efficient and flexible all-reduce algorithm for distributed deep learning under irregular network hierarchies. With ever-growing deep neural networks, distributed learning over multiple nodes is becoming imperative for expedited training. There are several approaches leveraging the symmetric network structure to optimize the performance over different hierarchy levels of the network. However, the assumption of symmetric network does not always hold, especially in shared cloud environments. By allocating an uneven portion of gradients to each learner (GPU), FlexReduce outperforms conventional algorithms on asymmetric network structures, and still performs even or better on symmetric networks.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218538"}, {"primary_key": "2565872", "vector": [], "sparse_vector": [], "title": "Bit Parallel 6T SRAM In-memory Computing with Reconfigurable Bit-Precision.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jongsun Park"], "summary": "This paper presents 6T SRAM cell-based bit-parallel in-memory computing (IMC) architecture to support various computations with reconfigurable bit-precision. In the proposed technique, bit-line computation is performed with a short WL followed by BL boosting circuits, which can reduce BL computing delays. By per-forming carry-propagation between each near-memory circuit, bit-parallel complex computations are also enabled by iterating operations with low latency. In addition, reconfigurable bit-precision is also supported based on carry-propagation size. Our 128KB in/near memory computing architecture has been implemented using a 28nm CMOS process, and it can achieve 2.25GHz clock frequency at 0.9V with 5.2% of area overhead. The proposed architecture also achieves 0.68, 8.09 TOPS/W for the parallel addition and multiplication, respectively. In addition, the proposed work also supports a wide range of supply voltage, from 0.6V to 1.1V.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218567"}, {"primary_key": "2565873", "vector": [], "sparse_vector": [], "title": "Learning to Predict IR Drop with Effective Training for ReRAM-based Neural Network Hardware.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Due to the inevitability of the IR drop problem in passive ReRAM crossbar arrays, finding a software solution that can predict the effect of IR drop without the need of expensive SPICE simulations, is very desirable. In this paper, two simple neural networks are proposed as software solution to predict the effect of IR drop. These networks can be easily integrated in any deep neural network framework to incorporate the IR drop problem during training. As an example, the proposed solution is integrated in BinaryNet framework and the test validation results, done through SPICE simulations, show very high improvement in performance close to the baseline performance, which demonstrates the efficacy of the proposed method. In addition, the proposed solution outperforms the prior work on challenging datasets such as CIFAR10 and SVHN.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218735"}, {"primary_key": "2565874", "vector": [], "sparse_vector": [], "title": "EANeM: Energy-Aware Network Stack Management for Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In mobile computing, various energy-efficient thread scheduling schemes for heterogeneous multiprocessing architectures are proposed. For network applications, however, inaccurate prediction of the CPU load and high-priority network packet processing overdrive CPU cores, leading to large energy consumption. We present a framework including a network stack monitor, a bandwidth controller, and an energy-efficient thread scheduling scheme, which accurately estimates the CPU load for packet processing and optimally schedules CPU resource. It improves performance/watt by 4.79 times over the baseline Linux scheduler for FTP applications. In multi-threaded environments, it improves performance/watt by 2.35–3.11 times for PARSEC benchmark and network applications.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218670"}, {"primary_key": "2565875", "vector": [], "sparse_vector": [], "title": "WarningNet: A Deep Learning Platform for Early Warning of Task Failures under Input Perturbation for Reliable Autonomous Platforms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Taesik Na", "<PERSON><PERSON>"], "summary": "There is a growing interest in deploying complex deep neural networks (DNN) in autonomous systems to extract task-specific information from real-time sensor data and drive critical tasks. The perturbations in sensor data due to noise or environmental conditions can lead to errors in information extraction and degrade reliability of the entire autonomous systems. This paper presents a light-weight deep learning plat-form, WarningNet, that operates on sensor data to estimate potential task failures due to spatiotemporal input perturbations. Experimental results show that WarningNet can provide early warning of the performance degradation of different tasks within a fraction of the time required for the task to complete. As a case-study, we show that the early warning can be leveraged to improve the task reliability under adverse condition using on-demand input pre-processing.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218502"}, {"primary_key": "2565876", "vector": [], "sparse_vector": [], "title": "Eliminating Redundant Computation in Noisy Quantum Computing Simulation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Noisy Quantum Computing (QC) simulation on a classical machine is very time consuming since it requires Monte Carlo simulation with a large number of error-injection trials to model the effect of random noises. Orthogonal to existing QC simulation optimizations, we aim to accelerate the simulation by eliminating the redundant computation among those Monte Carlo simulation trials. We observe that the intermediate states of many trials can often be the same. Once these states are computed in one trial, they can be temporarily stored and reused in other trials. However, storing such states will consume significant memory space. To leverage the shared intermediate states without introducing too much storage overhead, we propose to statically generate and analyze the Monte Carlo simulation simulation trials before the actual simulation. Those trials are reordered to maximize the overlapped computation between two consecutive trials. The states that cannot be reused in follow-up simulation are dropped, so that we only need to store a few states. Experiment results show that the proposed optimization scheme can save on average 80% computation with only a small number of state vectors stored. In addition, the proposed simulation scheme demonstrates great scalability as more computation can be saved with more simulation trials or on future QC devices with reduced error rates.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218666"}, {"primary_key": "2565877", "vector": [], "sparse_vector": [], "title": "EDD: Efficient Differentiable DNN Architecture and Implementation Co-search for Embedded AI Solutions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "High quality AI solutions require joint optimization of AI algorithms and their hardware implementations. In this work, we are the first to propose a fully simultaneous, Efficient Differentiable DNN (deep neural network) architecture and implementation co-search (EDD) methodology. We formulate the co-search problem by fusing DNN search variables and hardware implementation variables into one solution space, and maximize both algorithm accuracy and hardware implementation quality. The formulation is differentiable with respect to the fused variables, so that gradient descent algorithm can be applied to greatly reduce the search time. The formulation is also applicable for various devices with different objectives. In the experiments, we demonstrate the effectiveness of our EDD methodology by searching for three representative DNNs, targeting low-latency GPU implementation and FPGA implementations with both recursive and pipelined architectures. Each model produced by EDD achieves similar accuracy as the best existing DNN models searched by neural architecture search (NAS) methods on ImageNet, but with superior performance obtained within 12 GPU-hour searches. Our DNN targeting GPU is 1.40× faster than the state-of-the-art solution reported in Proxyless [1], and our DNN targeting FPGA delivers 1.45× higher throughput than the state-of-the-art solution reported in DNNBuilder [2].", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218749"}, {"primary_key": "2565878", "vector": [], "sparse_vector": [], "title": "ReTriple: Reduction of Redundant Rendering on Android Devices for Performance and Energy Optimizations.", "authors": ["Xi<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graphics rendering is a compute-intensive work and a major source of energy consumption on battery-driven mobile devices. Unlike the existing works that degrade user experience or reuse rendering results coarsely, we propose ReTriple, a fine-grained scheme to reduce rendering workload by reusing the past rendering results at the UI element level. This fine-grained reuse mechanism can explore more opportunities to reduce the workload of the rendering process and save energy. The experiments tested with popular apps show that ReTriple achieves an average speedup of 2.6x and per-frame energy saving of 32.3% for the rendering process while improving user experience.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218517"}, {"primary_key": "2565879", "vector": [], "sparse_vector": [], "title": "Defending Bit-Flip Attack through DNN Weight Reconstruction.", "authors": ["Jing<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent studies show that adversarial attacks on neural network weights, aka, Bit-Flip Attack (BFA), can degrade Deep Neural Network's (DNN) prediction accuracy severely. In this work, we propose a novel weight reconstruction method as a countermeasure to such BFAs. Specifically, during inference, the weights are reconstructed such that the weight perturbation due to BFA is minimized or diffused to the neighboring weights. We have successfully demonstrated that our method can significantly improve the DNN robustness against random and gradient-based BFA variants. Even under the most aggressive attacks (i.e., greedy progressive bit search), our method maintains a test accuracy of 60% on ImageNet after 5 iterations while the baseline accuracy drops to below 1%.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218665"}, {"primary_key": "2565880", "vector": [], "sparse_vector": [], "title": "Adaptive Layout Decomposition with Graph Embedding Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multiple patterning lithography decomposition (MPLD) has been widely investigated, but so far there is no decomposer that dominates others in terms of both the optimality and the efficiency. This observation motivates us exploring how to adaptively select the most suitable MPLD strategy for a given layout graph, which is non-trivial and still an open problem. In this paper, we propose a layout decomposition framework based on graph convolutional networks to obtain the graph embeddings of the layout. The graph embeddings are used for graph library construction, decomposer selection and graph matching. Experimental results show that our graph embedding based framework can achieve optimal decompositions under negligible runtime overhead even comparing with fast but non-optimal heuristics.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218706"}, {"primary_key": "2565881", "vector": [], "sparse_vector": [], "title": "DVFS-Based Scrubbing Scheduling for Reliability Maximization on Parallel Tasks in SRAM-based FPGAs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To obtain high reliability but avoiding the huge area overhead of traditional triple modular redundancy (TMR) methods in SRAM-based FPGAs, scrubbing based methods reconfigure the configuration memory of each task just before its execution. However, due to the limitation of the FPGA reconfiguration module that can only scrub one task at a time, parallel tasks may leave stringent timing requirements to schedule their scrubbing processes. Thus the scrubbing requests may be either delayed or omitted, leading to a less reliable system. To address this issue, we propose a novel optimal DVFS-based scrubbing algorithm to adjust the execution time of user tasks, thus significantly enhance the chance to schedule scrubbing successfully for parallel tasks. Besides, we develop an approximation algorithm to speed up its optimal version and develop a novel K-Means based method to reduce the memory usage of the algorithm. Compared to the state-of-the-art, experimental results show that our work achieves up to 36.11% improvement on system reliability with comparable algorithm execution time and memory consumption.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218574"}, {"primary_key": "2565882", "vector": [], "sparse_vector": [], "title": "ReSiPE: ReRAM-based Single-Spiking Processing-In-Memory Engine.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Processing-in-memory (PIM) designs that leverage emerging nanotechnologies like resistive random access memory (ReRAM) have demonstrated enormous potential in accelerating deep learning applications due to high energy efficiency and integration density. The common approach of existing ReRAM-based PIM designs is to encode data into either voltage levels with the assist of power-thirsty analog/digital conversion circuits or spike series by sacrificing computing latency. In this paper, we introduce ReSiPE, a ReRAM-based Single-spiking PIM Engine, which uses the arrival time of a single spike to represent the data. We analyze how to encode data into a set of single spikes and develop the circuit to realize the matrix-based computation. The proposed design can minimize the spike numbers, shorten the computation period, and thus improve energy efficiency dramatically. Our simulation results show that ReSiPE achieves 67.1% power reduction and 1.97× power efficiency improvement compared to rate-coding based ReRAM PIM designs under the comparable area, throughput, and accuracy.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218578"}, {"primary_key": "2565883", "vector": [], "sparse_vector": [], "title": "Exploring a Bayesian Optimization Framework Compatible with Digital Standard Flow for Soft-Error-Tolerant Circuit.", "authors": ["<PERSON>", "Xiao<PERSON><PERSON> Zeng", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Soft error is a major reliability concern in advanced technology nodes. Although mitigating Soft Error Rate (SER) will inevitably sacrifice area and power, few studies paid attention to optimization methods to explore trade-offs between area, power and SER. This paper proposes an optimization framework based on Bayesian approach for soft-error-tolerant circuit design. It comprises two steps:1) data preprocessing and 2) Bayesian optimization. In the preprocessing step, a strategy incorporating k-means algorithm and a novel sequencing algorithm is used to cluster Flip-Flops (FFs) with similar SER in order to reduce the dimensionality for the subsequent step. Bayesian Neural Network (BNN) is the applied surrogate model for acquiring the posterior distribution of three design metrics, while the Lower confidence bound (LCB) functions are employed as acquisition functions to select the next point based on BNN when optimizing. Finally, the non-dominated sorting genetic algorithm (NSGA-II) is used to search the Pareto Optimal Front (POF) solutions of three LCB functions. Experimental results demonstrate the proposed framework has a 1.4x improvement in accuracy and a 70% reduction in SER with acceptable increases in power and area.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218696"}, {"primary_key": "2565884", "vector": [], "sparse_vector": [], "title": "Enabling a B+-tree-based Data Management Scheme for Key-value Store over SMR-based SSHD.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Owing to the explosive growth of data volume, high areal density storage technologies have been proposed in the past few years. Among them, shingled magnetic recording (SMR) has been regarded as the most promising candidate to replace current conventional hard disk drive based on the perpendicular magnetic recording technology. However, SMR technology not only brings large capacity storage devices but also results in terrible random access performance. For increasing the random access performance of SMR, solid-state hybrid drive (SSHD) seems a possible solution in storage system development. Nevertheless, when an SMR-based SSHD is adopted to a large-scale data management system, a severe performance degeneration will happen because an indexing scheme for access efficiency always maintains data in the large-scale data management system. More specifically, jointly managing indexing keys and data values on an SSHD drive will result in the massive amount of write amplification because of read-merge-write operations and garbage collection processes. Based on such motivations, this work proposed a total solution, namely XsB + -tree, to establish a high-performance B + -tree-based data management scheme for key-value store systems. To the best of our knowledge, this work is the first work to discuss the total solution for the key-value store over an SMR-based SSHD. According to our experimental results, XsB + -tree can improve the access time by 80% on average and prolong the lifetime of SSD up to 19%.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218708"}, {"primary_key": "2565885", "vector": [], "sparse_vector": [], "title": "Routing Topology and Time-Division Multiplexing Co-Optimization for Multi-FPGA Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Time-division multiplexing (TDM) is widely used to overcome bandwidth limitations and thus enhances routability in multi-FPGA systems due to the shortage of I/O pins in an FPGA. However, multiplexed signals induce significant delays. To evaluate timing degradation, nets with similar criticalities are often grouped to form NetGroups. In this paper, we propose a framework concerning routing topology and time-division multiplexing co-optimization for multi-FPGA systems. The proposed framework first generates high-quality topologies considering Net-Group criticalities. Then, inspired by column generation, TDM ratio assignment is solved optimally by Lagrangian relaxation. Experimental results show that our approach outperforms the top three entries of ICCAD 2019 CAD Contest. Moreover, our TDM ratio assignment algorithm can further improve the results of the top three winners to almost as good as ours.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218667"}, {"primary_key": "2565886", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: An Analytical Timing-Driven Placer for Heterogeneous FPGAs*.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the feature sizes keep shrinking, interconnect delays have become a major limiting factor for FPGA timing closure. Traditional placement algorithms that address wirelength alone are no longer sufficient to close timing, especially for the large-scale heterogeneous FPGAs. In this paper, we resolve the crucial FPGA placement problem by optimizing wirelength and timing simultaneously. First, a smoothed routing-architecture-aware timing model is proposed to accurately estimate each interconnect delay. Then, a timing-driven delay look-up table is constructed to further speed up delay access. Finally, we present an effective wirelength and timing co-optimization strategy to produce high-quality placements without timing violations. Compared with Vivado 2019.1 on Xilinx benchmark suites for xc7k325t device, experimental results show that our algorithm achieves not only a 6.6% improvement in worst slack but also a 3.2% reduction for routed wirelength.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218699"}, {"primary_key": "2565887", "vector": [], "sparse_vector": [], "title": "CoExe: An Efficient Co-execution Architecture for Real-Time Neural Network Services.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Mingcong Song", "Jiechen <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "End-to-end latency is sensitive for user-interactive neural network (NN) services on clouds. For periods of high request load, co-locating multiple NN requests has the potential to reduce end-to-end latency. However, current batch-based accelerators lack request-level parallelism support, leaving the queuing time non-optimized. Meanwhile, naively partitioning resources for simultaneous requests suffers from longer execution time as well as lower resource efficiency because different applications utilize separate resources without sharing. To effectively reduce the end-to-end latency for real-time NN requests, we propose CoExe architecture, equipped with a pipeline implementation of a sparsity-driven real-time co-execution model. By leveraging the non-trivial amount of sparse operations during concurrent NNs execution, the end-to-end latency is decreased by up to 12.3× and 2.4× over Eyeriss-like and SCNN at peak workload mode. Besides, we propose row cross (RC) dataflow to reduce data movement cost, and avoid memory duplication.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218740"}, {"primary_key": "2565888", "vector": [], "sparse_vector": [], "title": "Closing the Design Loop: Bayesian Optimization Assisted Hierarchical Analog Layout Synthesis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiyuan Tang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Existing analog layout synthesis tools provide little guarantee to post layout performance and have limited capabilities of handling system-level designs. In this paper, we present a closed-loop hierarchical analog layout synthesizer, capable of handling system designs. To ensure system performance, the building block layout implementations are optimized efficiently, utilizing post layout simulations with multi-objective Bayesian optimization. To the best of our knowledge, this is the first work demonstrating success in automated layout synthesis on generic analog system designs. Experimental results show our synthesized continuous-time ΔΣ modulator (CTDSM) achieves post layout performance of 65.9dB in signal to noise and distortion ratio (SNDR), compared with 67.8dB in the schematic design.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218621"}, {"primary_key": "2565889", "vector": [], "sparse_vector": [], "title": "Imperceptible Misclassification Attack on Deep Learning Accelerator by Glitch Injection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The convergence of edge computing and deep learning empowers endpoint hardwares or edge devices to perform inferences locally with the help of deep neural network (DNN) accelerator. This trend of edge intelligence invites new attack vectors, which are methodologically different from the well-known software oriented deep learning attacks like the input of adversarial examples. Current studies of threats on DNN hardware focus mainly on model parameters interpolation. Such kind of manipulation is not stealthy as it will leave non-erasable traces or create conspicuous output patterns. In this paper, we present and investigate an imperceptible misclassification attack on DNN hardware by introducing infrequent instantaneous glitches into the clock signal. Comparing with falsifying model parameters by permanent faults, corruption of targeted intermediate results of convolution layer(s) by disrupting associated computations intermittently leaves no trace. We demonstrated our attack on nine state-of-the-art ImageNet models running on Xilinx FPGA based deep learning accelerator. With no knowledge about the models, our attack can achieve over 98% misclassification on 8 out of 9 models with only 10% glitches launched into the computation clock cycles. Given the model details and inputs, all the test images applied to ResNet50 can be successfully misclassified with no more than 1.7% glitch injection.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218577"}, {"primary_key": "2565890", "vector": [], "sparse_vector": [], "title": "Content Sifting Storage: Achieving Fast Read for Large-scale Image Dataset Analysis.", "authors": ["<PERSON>", "Hong Jiang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Analyzing large-scale image dataset requires all images to be read from disks first, leading to high read latency. Therefore, we propose a Content Sifting Storage (CSS) system, which aims to reduce the read latency by only reading sifted relevant data. CSS generates embedded content metadata via deep learning and manages the metadata via Semantic Hamming Graph, which achieves fast read based on content similarity meeting the given analysis. Extensive experimental results on image datasets show that compared with conventional semantic storage systems, our CSS can greatly reduce the read latency by 82.21% to 94.8% with more than 98% recall rate.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218738"}, {"primary_key": "2565891", "vector": [], "sparse_vector": [], "title": "Monitoring the Health of Emerging Neural Network Accelerators with Cost-effective Concurrent Test.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "ReRAM-based neural network accelerator is a promising solution to handle the memory-and computation-intensive deep learning workloads. However, it suffers from unique device errors. These errors can accumulate to massive levels during the run time and cause significant accuracy drop. It is crucial to obtain its fault status in real-time before any proper repair mechanism can be applied. However, calibrating such statistical information is non-trivial because of the need of a large number of test patterns, long test time, and high test coverage considering that complex errors may appear in million-to-billion weight parameters. In this paper, we leverage the concept of comer data that can significantly confuse the decision making of neural network model, as well as the training algorithm, to generate only a small set of test patterns that is tuned to be sensitive to different levels of error accumulation and accuracy loss. Experimental results show that our method can quickly and correctly report the fault status of a running accelerator, outperforming existing solutions in both detection efficiency and cost", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218675"}, {"primary_key": "2565892", "vector": [], "sparse_vector": [], "title": "CUGR: Detailed-Routability-Driven 3D Global Routing with Probabilistic Resource Model.", "authors": ["<PERSON><PERSON>", "Chak-<PERSON><PERSON>", "Fangzhou Wang", "<PERSON><PERSON><PERSON>. <PERSON>"], "summary": "Many competitive global routers adopt the technique of compressing the 3D routing space into 2D in order to handle today's massive circuit scales. It has been shown as an effective way to shorten the routing time, however, quality will inevitably be sacrificed to different extents. In this paper, we propose two routing techniques that directly operate on the 3D routing space and can maximally utilize the 3D structure of a grid graph. The first technique is called 3D pattern routing, by which we combine pattern routing and layer assignment, and we are able to produce optimal solutions with respect to the patterns under consideration in terms of a cost function in wire length and routability. The second technique is called multi-level 3D maze routing. Two levels of maze routing with different cost functions and objectives are designed to maximize the routability and to search for the minimum cost path efficiently. Besides, we also designed a cost function that is sensitive to resources changes and a post-processing technique called patching that gives the detailed router more flexibility in escaping congested regions. Finally, the experimental results show that our global router outperforms all the contestants in the ICCAD'19 global routing contest.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218646"}, {"primary_key": "2565893", "vector": [], "sparse_vector": [], "title": "A Model Checking-based Analysis Framework for Systems Biology Models.", "authors": ["<PERSON>", "Sara <PERSON>"], "summary": "Biological systems are often modeled as a system of ordinary differential equations (ODEs) with time-invariant parameters. However, cell signaling events or pharmacological interventions may alter the cellular state and induce multimode dynamics of the system. Such systems are naturally modeled as hybrid automata, which possess multiple operational modes with specific nonlinear dynamics in each mode. In this paper we introduce a model checking-enabled framework than can model and analyze both single- and multi-mode biological systems. We tackle the central problem in systems biology- identify parameter values such that a model satisfies desired behaviors-using bounded model checking. We resort to the delta- decision procedures to solve satisfiability modulo theories (SMT) problems and sidestep undecidability of reachability problems. Our framework enables several analysis tasks including model calibration and falsification, therapeutic strategy identification, and Lyapunov stability analysis. We demonstrate the applica- blitliy of these methods using case studies of prostate cancer progression, cardiac cell action potential and radiation diseases.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218655"}, {"primary_key": "2565894", "vector": [], "sparse_vector": [], "title": "Q-PIM: A Genetic Algorithm based Flexible DNN Quantization Method and Application to Processing-In-Memory Platform.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a genetic algorithm (GA) based training free layer-wise quantization method, named as GAQ, to reduce model complexity of arbitrary DNN architectures. The proposed algorithm formulates an optimization problem to determine the quantization level for each DNN layer under the constrain of maximum accuracy degradation and uses genetic algorithm to solve the problem at the inference stage of any pre-trained DNN models. The experimental results on various DNNs for image classification demonstrate 5x to 17x weight compression rate with insignificant (< 2%) accuracy loss, comparable with existing quantization algorithms which typically require multi-pass retraining and handcrafted tuning. To evaluate the computational benefits of GAQ, we present a SRAM based flexible precision all-digital processing-in-memory (PIM) architecture, named as Q-PIM, that leverages GAQ to optimally control precision for each DNN layer to enhance efficiency. The simulation in 28nm CMOS shows potential for significant energy and latency advantage over fixed-precision PIM architectures.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218737"}, {"primary_key": "2565895", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Design Dependent Mega Cell Methodology for Area and Power Optimization.", "authors": ["<PERSON>en<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Technology mapping is the key link between technology independent logic synthesis and technology dependent physical design of IC design flow. Conventionally, physical design honors the circuit structure generated by logic synthesis and then performs optimizations to meet the design requirement by using the same cell library as logic synthesis. Thus, the quality of technology mapping is bounded by the variety of library cells. To enhance the flexibility and capability, we propose an analytical mega cell methodology, which clusters the same type or different types of cells together to improve area and power. We analyze the placement of a design and rank mergeable cells for mega cell creation. Through sharing layout space or gate reduction, our approach minimizes the area and power without timing degradation. Our experiments are conducted on five types of SHA256 cores (block chain mining machines) with below 10nm process. Compared with the conventional technology mapping approach (widely adopted by commercial tools), our approach can save average 2.23% area and 14.44% total power consumption. Our results show that the proposed mega cell methodology is promising for energy and area reduction in modern block chain designs and can be easily ported to other ASIC designs.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218739"}, {"primary_key": "2565896", "vector": [], "sparse_vector": [], "title": "BPU: A Blockchain Processing Unit for Accelerated Smart Contract Execution.", "authors": ["Tao Lu", "<PERSON>"], "summary": "Modern blockchains use smart contracts to implement automatic and decentralized programs, which are the foundations of Decentralized Applications (DApp). The poor performance on general purpose computers has become the bottleneck that limits the blockchain and smart contracts from being widely used. In this paper, we present BPU, a high-performance modularized blockchain processing unit. BPU aims at bringing performance and flexibility to the blockchain and DApp processing. Our design achieves significant speedup compared against the software implementation on an Intel CPU.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218512"}, {"primary_key": "2565897", "vector": [], "sparse_vector": [], "title": "TP-GNN: A Graph Neural Network Framework for Tier Partitioning in Monolithic 3D ICs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Kambiz <PERSON>", "<PERSON>"], "summary": "3D integration technology is one of the few options that can keep <PERSON>'s Law trajectory beyond conventional scaling. Existing 3D physical design flows fail to benefit from the full advantage that 3D integration provides. Particularly, current 3D partitioning algorithms do not comprehend technology and design-related parameters properly, which results in sub-optimal partitioning solutions. In this paper, we propose TP-GNN, an unsupervised graph-learning-based tier partitioning framework, to overcome this issue. Experimental results on 7 industrial designs demonstrate that our framework significantly improves the QoR of the state-of-the-art 3D implementation flows. Specifically, in OpenPiton, a RISC-V-based multi-core system, we observe 27.4%, 7.7% and 20.3% improvements in performance, wirelength, and energy-per-cycle respectively.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218582"}, {"primary_key": "2565898", "vector": [], "sparse_vector": [], "title": "Topological Structure and Physical Layout Codesign for Wavelength-Routed Optical Networks-on-Chip.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The wavelength-routed optical network-on-chip (WRONoC) is a promising solution for signal transmission in modern system-on-chip (SoC) designs. Previous works do not handle three main issues for WRONoCs: correlations between the topological structure and physical layout, trade-offs between the maximum insertion loss and wavelength power, and a fully automated flow to generate predictable designs. As a result, the insertion loss estimation is inaccurate, and thus only suboptimal results are obtained. To remedy these disadvantages, we present a fully automated topological structure and physical layout codesign flow to minimize the maximum insertion loss and the wavelength power simultaneously with a significant speedup. Experimental results show that our codesign flow significantly outperforms state-of-the-art works in the maximum insertion loss, wavelength power, and runtimes.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218625"}, {"primary_key": "2565899", "vector": [], "sparse_vector": [], "title": "A Provably Good Wavelength-Division-Multiplexing-Aware Clustering Algorithm for On-Chip Optical Routing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the VLSI technology continues to scale down, combined with increasing demands for large bandwidth and low-power consumption, the optical interconnections with Wavelength Division Multiplexing (WDM) become an attractive alternative for on-chip signal transmission. Previous WDM-aware optical routing works consist of two main drawbacks: they are based mainly on heuristics or restricted integer linear programming to handle optical routing, and the addressed types of transmission loss and WDM overheads are incomplete. As a result, no performance guarantees can be achieved on their WDM clustering results, and/or their computations are too time-consuming. To remedy these disadvantages, we present a polynomial-time provably good WDM-aware clustering algorithm and a new WDM-aware optical routing flow to minimize the transmission loss and the WDM overheads with a significant speedup. The proposed WDM-aware clustering algorithm guarantees to find an optimal solution for 1-, 2-, and 3-path clustering, and has the constant performance bound 3 for most cases of 4-path clustering. Experimental results based on the ISPD 2007 and 2019 contest benchmarks and a real optical design show that our optical router significantly outperforms published works in wirelength, transmission loss, wavelength power, and runtimes.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218637"}, {"primary_key": "2565900", "vector": [], "sparse_vector": [], "title": "ICS Protocol Fuzzing: Coverage Guided Packet Crack and Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yu <PERSON>"], "summary": "Industrial Control System (ICS) protocols play an essential role in building communications among system components. Recently, many severe vulnerabilities, such as Stuxnet and DragonFly, exposed in ICS protocols have affected a wide distribution of devices. Therefore, it is of vital importance to ensure their correctness. However, the vulnerability detection efficiency of traditional techniques such as fuzzing is challenged by the complexity and diversity of the protocols.In this paper, we propose to equip the traditional protocol fuzzing with coverage-guided packet crack and generation. We collect the coverage information during the testing procedure, save those valuable packets that trigger new path coverage and crack them into pieces, based on which, we can construct higher-quality new packets for further testing. For evaluation, we build Peach * on top of Peach, which is one of the most widely used protocol fuzzers, and conduct experiments on several ICS protocols such as Modbus and DNP3. Results show that, compared with the original Peach, Peach * achieves the same code coverage and bug detection numbers at the speed of 1.2X-25X. It also gains final increase with 8.35%-36.84% more paths within 24 hours and has exposed 9 previously unknown vulnerabilities.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218603"}, {"primary_key": "2565901", "vector": [], "sparse_vector": [], "title": "Access Characteristic Guided Partition for Read Performance Improvement on Solid State Drives.", "authors": ["Yina Lv", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Solid state drives (SSDs) are now widely deployed due to the development of high-density and low-cost NAND flash memories. Previous works have identified that the read performance of SSDs is degrading along with the development. One of the most critical reasons is the access interference between reads and writes, as the latest NAND flash memories have significant latency gap between reads and writes. This paper addresses this issue with the assistance of access characteristic guided SSD partitioning. First, several server workloads are studied and it is shown that reads and writes can be separated based on their access characteristics. Second, a set of techniques is proposed to place data judiciously for requests separation. Finally, a workload based SSD partitioning scheme is proposed to improve the read performance. The experimental results show that the proposed solution can improve read performance by 36% on average compared with the state-of-the-art solutions.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218540"}, {"primary_key": "2565902", "vector": [], "sparse_vector": [], "title": "KFR: Optimal Cache Management with K-Framed Reclamation for Drive-Managed SMR Disks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Shingled Magnetic Recording (SMR) disks have been proposed as a promising solution to satisfy the increasing capacity need in the big data era. Drive-Managed SMR (DM-SMR) disk which acts as a traditional block device is favored for providing high compatibility. However, DM-SMR disks suffer from high performance recovery time (PRT) due to the \"SMR space reclamation\" issue. This paper proposes an optimal cache management named K-Framed Reclamation (KFR) to minimize PRT within the DM-SMR disk. The effectiveness of our proposed design was evaluated with realistic and intensive I/O workloads and the results are encouraging.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218636"}, {"primary_key": "2565903", "vector": [], "sparse_vector": [], "title": "Online Adaptive Learning for Runtime Resource Management of Heterogeneous SoCs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic resource management has become one of the major areas of research in modern computer and communication system design due to lower power consumption and higher performance demands. The number of integrated cores, level of heterogeneity and amount of control knobs increase steadily. As a result, the system complexity is increasing faster than our ability to optimize and dynamically manage the resources. Moreover, offline approaches are sub-optimal due to workload variations and large volume of new applications unknown at design time. This paper first reviews recent online learning techniques for predicting system performance, power, and temperature. Then, we describe the use of predictive models for online control using two modern approaches: imitation learning (IL) and an explicit nonlinear model predictive control (NMPC). Evaluations on a commercial mobile platform with 16 benchmarks show that the IL approach successfully adapts the control policy to unknown applications. The explicit NMPC provides 25% energy savings compared to a state-of-the-art algorithm for multi-variable power management of modern GPU sub-systems.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218604"}, {"primary_key": "2565904", "vector": [], "sparse_vector": [], "title": "Q-CapsNets: A Specialized Framework for Quantizing Capsule Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Capsule Networks (CapsNets), recently proposed by the Google Brain team, have superior learning capabilities in machine learning tasks, like image classification, compared to the traditional CNNs. However, CapsNets require extremely intense computations and are difficult to be deployed in their original form at the resource-constrained edge devices. This paper makes the first attempt to quantize CapsNet models, to enable their efficient edge implementations, by developing a specialized quantization framework for CapsNets. We evaluate our framework for several benchmarks. On a deep CapsNet model for the CIFAR10 dataset, the framework reduces the memory footprint by 6.2x, with only 0.15% accuracy loss. We will open-source our framework at https://git.io/JvDIF.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218746"}, {"primary_key": "2565905", "vector": [], "sparse_vector": [], "title": "Massively Parallel Approximate Simulation of Hard Quantum Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As quantum computers grow more capable, simulating them on conventional hardware becomes more challenging yet more attractive since this helps in design and verification. Some quantum algorithms and circuits are amenable to surprisingly efficient simulation, and this makes hard-to-simulate computations particularly valuable. For such circuits, we develop accurate massively-parallel simulation with dramatic speedups over earlier methods on 42- and 45-qubit circuits. We propose two ways to trade circuit fidelity for computational speedups, so as to match the error rate of any quantum computer. Using Google Cloud, we simulate approximate sampling from the output of a circuit with 7 × 8 qubits and depth 42 with fidelity 0.5% at an estimated cost of $35K.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218591"}, {"primary_key": "2565906", "vector": [], "sparse_vector": [], "title": "ALSRAC: Approximate Logic Synthesis by Resubstitution with Approximate Care Set.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Approximate computing is an emerging design technique for error-resilient applications. It improves circuit area, power, and delay at the cost of introducing some errors. Approximate logic synthesis (ALS) is an automatic process to produce approximate circuits. This paper proposes approximate resubstitution with approximate care set and uses it to build a simulation-based ALS flow. The experimental results demonstrate that the proposed method saves 7%-18% area compared to state-of-the-art methods. The code of ALSRAC is made open-source.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218627"}, {"primary_key": "2565907", "vector": [], "sparse_vector": [], "title": "Compact domain-specific co-processor for accelerating module lattice-based KEM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a domain-specific co-processor to speed up <PERSON><PERSON>, a post-quantum key encapsulation mechanism competing on the NIST Post-Quantum Cryptography standardization process. Contrary to most lattice-based schemes, <PERSON>ber doesn't use NTT-based polynomial multiplication. We follow a hardware-software co-design approach: the execution is performed on an ARM core and only the most computationally expensive operation, i.e., the polynomial multiplication, is offloaded to the co-processor to obtain a compact design. We exploit the idea of distributed computing at micro-architectural level together with novel algorithmic optimizations to achieve approximately a 6 times speedup with respect to optimized software at a small area cost, which we demonstrate on a Zynq-7000 ARM/FPGA SoC.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218727"}, {"primary_key": "2565908", "vector": [], "sparse_vector": [], "title": "ROPAD: A Fully Digital Highly Predictive Ring Oscillator Probing Attempt Detector.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Microprobing is an invasive attack technique to intercept data from on-chip signals. As a countermeasure, we propose a new probing detector for an industrial sub-40 nm advanced process node. It is based on ring oscillators incorporating bus lines. The oscillation frequency caused by the capacity of bus lines is differentially measured to detect attached probes. This concept reduces calibration effort and required protected nonvolatile memory in comparison to state of the art. Furthermore, the protected bus line capacitance is increased up to four times. This work is the first analyzing the detector behavior considering cross coupling between the protected bus lines.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218546"}, {"primary_key": "2565909", "vector": [], "sparse_vector": [], "title": "StatSAT: A Boolean Satisfiability based Attack on Logic-Locked Probabilistic Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The outsourcing of chip designs for fabrication has raised concerns regarding the protection of Intellectual Property (IP) from an untrustworthy foundry. Logic locking is a design-for-security technique that has the potential to thwart attacks from such an adversary. On the other hand, the notions of approximate and probabilistic computing have been popularized due to their low energy consumption characteristics and their potential application in error-tolerant frameworks. Prior work has looked into and exposed the vulnerability of logic-locked circuits using concepts of Boolean Satisfiability (SAT), but mostly from the perspective of deterministic designs. Despite existing attack frameworks not being directly applicable, we show in this work that circuits exhibiting probabilistic behavior also face the same threat. We propose StatSAT, an attack methodology incorporating statistical techniques into the existing SAT attack, that can overcome the hurdles imposed by the probabilistic behavior. Our attack results show that the adversary is capable of unlocking the circuit to an extent good for all practical purposes.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218703"}, {"primary_key": "2565910", "vector": [], "sparse_vector": [], "title": "Pythia: Intellectual Property Verification in Zero-Knowledge.", "authors": ["<PERSON><PERSON>", "Nektar<PERSON>"], "summary": "The contemporary IC supply chain depends heavily on third-party intellectual property (3PIP) that is integrated to in-house designs. As the correctness of such 3PIPs should be verified before integration, one important challenge for 3PIP vendors is proving the functionality of their designs while protecting the privacy of circuit implementations. In this work, we present Pythia that employs zero-knowledge proofs to enable vendors convince integrators about the functionality of a circuit without disclosing its netlist. Pythia automatically encodes netlists into zero knowledge-friendly format, evaluates them on different inputs, and proves correctness of outputs. We evaluate Pythia using the ISCAS'85 benchmark suite.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218639"}, {"primary_key": "2565911", "vector": [], "sparse_vector": [], "title": "A History-Based Auto-Tuning Framework for Fast and High-Performance DNN Design on GPU.", "authors": ["<PERSON><PERSON><PERSON> Mu", "<PERSON><PERSON><PERSON>", "Lanbo Li", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While Deep Neural Networks (DNNs) are becoming increasingly popular, there is a growing trend to accelerate the DNN applications on hardware platforms like GPUs, FPGAs, etc., to gain higher performance and efficiency. However, it is time-consuming to tune the performance for such platforms due to the large design space and the expensive cost to evaluate each design point. Although many tuning algorithms, such as XGBoost tuner and genetic algorithm (GA) tuner, have been proposed to guide the design space exploring process in the previous work, the timing issue still remains a critical problem. In this work, we propose a novel auto-tuning framework to optimize the DNN operator design on GPU by leveraging the tuning history efficiently in different scenarios. Our experiments show that we can achieve superior performance than the state-of-the-art work, such as auto-tuning framework TVM and the handcraft optimized library cuDNN, while reducing the searching time by 8.96x and 4.58x comparing with XGBoost tuner and GA tuner in TVM.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218700"}, {"primary_key": "2565912", "vector": [], "sparse_vector": [], "title": "CryptoPIM: In-memory Acceleration for Lattice-based Cryptographic Hardware.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Rosario Cammarota", "<PERSON><PERSON>"], "summary": "Quantum computers promise to solve hard mathematical problems such as integer factorization and discrete logarithms in polynomial time, making standardized public-key cryptosystems insecure. Lattice-Based Cryptography (LBC) is a promising post-quantum public key cryptographic protocol that could replace standardized public key cryptography, thanks to the inherent post-quantum resistant properties, efficiency, and versatility. A key mathematical tool in LBC is the Number Theoretic Transform (NTT), a common method to compute polynomial multiplication. It is the most compute-intensive routine and requires acceleration for practical deployment of LBC protocols. In this paper, we propose CryptoPIM, a high-throughput Processing In-Memory (PIM) accelerator for NTT-based polynomial multiplier with the support of polynomials with degrees up to 32k. Compared to the fastest FPGA implementation of an NTT-based multiplier, CryptoPIM achieves on average 31x throughput improvement with the same energy and only 28% performance reduction, thereby showing promise for practical deployment of LBC.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218730"}, {"primary_key": "2565913", "vector": [], "sparse_vector": [], "title": "AdaSense: Adaptive Low-Power Sensing and Activity Recognition for Wearable Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Wearable devices have strict power and memory limitations. As a result, there is a need to optimize the power consumption on those devices without sacrificing the accuracy. This paper presents AdaSense: a sensing, feature extraction and classification co-optimized framework for Human Activity Recognition. The proposed techniques reduce the power consumption by dynamically switching among different sensor configurations as a function of the user activity. The framework selects configurations that represent the pareto-frontier of the accuracy and energy trade-off. AdaSense also uses low-overhead processing and classification methodologies. The introduced approach achieves 69% reduction in the power consumption of the sensor with less than 1.5% decrease in the activity recognition accuracy.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218568"}, {"primary_key": "2565914", "vector": [], "sparse_vector": [], "title": "Layer RBER Variation Aware Read Performance Optimization for 3D Flash Memories.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "3D NAND flash enables the construction of large capacity Solid-State Drives (SSDs) for modern computer systems. While effectively reducing per bit cost, 3D NAND flash exhibits non-negligible process variations and thus RBER (raw bit error rate) difference across layers, which leads to sub-optimal read performance for applications with either small or large I/O requests. In this paper, we propose LRR, Layer RBER variation aware Read optimization schemes, to address the challenge. LRR consists of two schemes - LRR subpage read scheduling (SRS) and LRR fullpage allocation (FPA). SRS groups small read requests from the layers with similar RBERs to reduce the average read latency of subpage sized read requests. FPA distributes the data of a large write to multiple layers, which improves the read latency when reading from layers with large RBERs. Our experimental results show that our proposed scheme LRR reduces 46% read latency on average over the state-of-the-art.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218631"}, {"primary_key": "2565915", "vector": [], "sparse_vector": [], "title": "Convergence-Aware Neural Network Training.", "authors": ["<PERSON><PERSON><PERSON>", "Yongseung Yu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Yongjun Park", "<PERSON><PERSON>"], "summary": "Training a deep neural network(DNN) is expensive, requiring a large amount of computation time. While the training overhead is high, not all computation in DNN training is equal. Some parameters converge faster and thus their gradient computation may contribute little to the parameter update; in nearstationary points a subset of parameters may change very little. In this paper we exploit the parameter convergence to optimize gradient computation in DNN training. We design a light-weight monitoring technique to track the parameter convergence; we prune the gradient computation stochastically for a group of semantically related parameters, exploiting their convergence correlations. These techniques are efficiently implemented in existing GPU kernels. In our evaluation the optimization techniques substantially and robustly improve the training throughput for four DNN models on three public datasets.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218518"}, {"primary_key": "2565916", "vector": [], "sparse_vector": [], "title": "Input-Dependent Edge-Cloud Mapping of Recurrent Neural Networks Inference.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Chen", "Sara Vinco", "<PERSON>", "<PERSON><PERSON>"], "summary": "Given the computational complexity of Recurrent Neural Networks (RNNs) inference, IoT and mobile devices typically offload this task to the cloud. However, the execution time and energy consumption of RNN inference strongly depends on the length of the processed input. Therefore, considering also communication costs, it may be more convenient to process short input sequences locally and only offload long ones to the cloud. In this paper, we propose a low-overhead runtime tool that performs this choice automatically. Results based on real edge and cloud devices show that our method is able to simultaneously reduce the total execution time and energy consumption of the system compared to solutions that run RNN inference fully locally or fully in the cloud.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218595"}, {"primary_key": "2565917", "vector": [], "sparse_vector": [], "title": "T2FSNN: Deep Spiking Neural Networks with Time-to-first-spike Coding.", "authors": ["Seongsik Park", "<PERSON><PERSON>", "Byunggook Na", "<PERSON><PERSON><PERSON>"], "summary": "Spiking neural networks (SNNs) have gained considerable interest due to their energy-efficient characteristics, yet lack of a scalable training algorithm has restricted their applicability in practical machine learning problems. The deep neural network-to-SNN conversion approach has been widely studied to broaden the applicability of SNNs. Most previous studies, however, have not fully utilized spatio-temporal aspects of SNNs, which has led to inefficiency in terms of number of spikes and inference latency. In this paper, we present T2FSNN, which introduces the concept of time-to-first-spike coding into deep SNNs using the kernel-based dynamic threshold and dendrite to overcome the aforementioned drawback. In addition, we propose gradient-based optimization and early firing methods to further increase the efficiency of the T2FSNN. According to our results, the proposed methods can reduce inference latency and number of spikes to 22% and less than 1%, compared to those of burst coding, which is the state-of-the-art result on the CIFAR-100.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218689"}, {"primary_key": "2565918", "vector": [], "sparse_vector": [], "title": "BPNet: Branch-pruned Conditional Neural Network for Systematic Time-accuracy Tradeoff.", "authors": ["Kyungchul Park", "<PERSON><PERSON><PERSON> Oh", "<PERSON><PERSON>"], "summary": "Recently, there have been attempts to execute the neural network conditionally with auxiliary classifiers allowing early termination depending on the difficulty of the input, which can reduce the execution time or energy consumption without any or with negligible accuracy decrease. However, previous studies do not consider how many or where the auxiliary classifiers, or branches, should be added in a systematic fashion. In this paper, we propose Branch-pruned Conditional Neural Network (BPNet) and its methodology in which the time-accuracy tradeoff for the conditional neural network can be found systematically. We applied BPNet to SqueezeNet, ResNet-20, and VGG-16 with CIFAR-10 and 100. BPNet achieves up to 3.15× of speedup without any accuracy drop compared to the base networks.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218545"}, {"primary_key": "2565919", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Pole-aware Analog Placement Considering Monotonic Current Flow and Crossing-Wire Minimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a new paradigm for analog placement, which further incorporates poles in addition to the considerations of symmetry-island and monotonic current flow while minimizing wire crossings. The nodes along the signal path in an analog circuit contribute to the poles, and the parasitics on these dominant poles can significantly limit the circuit performance. Although the monotonic placements introduced in the previous works can generate simpler routing topologies, the unawareness of poles, especially both dominant pole and the first non-dominant pole, and wire crossing among critical nets may result in the increase wire-load and performance degradation. Experimental results show that the proposed pole-aware analog placement method considering symmetry-island, monotonic current flow, and crossing-wire minimization results in much better solution quality in terms of circuit performance.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218634"}, {"primary_key": "2565920", "vector": [], "sparse_vector": [], "title": "Dynamic Information Flow Tracking for Embedded Binaries using SystemC-based Virtual Prototypes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Avoiding security vulnerabilities is very important for embedded systems. Dynamic Information Flow Tracking (DIFT) is a powerful technique to analyze SW with respect to security policies in order to protect the system against a broad range of security related exploits. However, existing DIFT approaches either do not exist for Virtual Prototypes (VPs) or fail to model complex hardware/software interactions.In this paper, we present a novel approach that enables early and accurate DIFT of binaries targeting embedded systems with custom peripherals. Leveraging the SystemC framework, our DIFT engine tracks accurate data flow information alongside the program execution to detect violations of security policies at run-time. We demonstrate the effectiveness and applicability of our approach by extensive experiments.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218494"}, {"primary_key": "2565921", "vector": [], "sparse_vector": [], "title": "Flashmark: Watermarking of NOR Flash Memories for Counterfeit Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Counterfeit electronics represents a significant concern because recycled, over-produced, out-of-spec, or cloned chips can enter globalized supply chains. This paper introduces Flashmark, a technique for watermarking of NOR flash memories for counterfeit detection. Flashmark relies on novel approaches for (a) imprinting watermarks into dedicated blocks of flash memory chips by repeated stressing, thus irreversibly changing physical properties of flash memory cells, and (b) reading out watermarks by sensing the changes in physical properties of flash cells through standard digital interfaces. The paper demonstrates Flashmark on embedded NOR flash memories in a family of low-cost microcontrollers.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218521"}, {"primary_key": "2565922", "vector": [], "sparse_vector": [], "title": "EMAP: A Cloud-Edge Hybrid Framework for EEG Monitoring and Cross-Correlation Based Real-time Anomaly Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "State-of-the-art techniques for detecting, or predicting, neurological disorders (1) focus on predicting each disorder individually, and are (2) computationally expensive, leading to a delay that can potentially render the prediction useless, especially in critical events. Towards this, we present a real-time two-tiered framework called EMAP, which cross-correlates the input with all the EEG signals in our mega-database (a combination of multiple EEG datasets) at the cloud, while tracking the signal in real-time at the edge, to predict the occurrence of a neurological anomaly. Using the proposed framework, we have demonstrated a prediction accuracy of up to 94% for the three different anomalies that we have tested.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218713"}, {"primary_key": "2565923", "vector": [], "sparse_vector": [], "title": "ApproxFPGAs: Embracing ASIC-Based Approximate Arithmetic Components for FPGA-Based Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Zdenek Vasícek", "Lu<PERSON><PERSON>", "<PERSON>"], "summary": "There has been abundant research on the development of Approximate Circuits (ACs) for ASICs. However, previous studies have illustrated that ASIC-based ACs offer asymmetrical gains in FPGA-based accelerators. Therefore, an AC that might be pareto-optimal for ASICs might not be pareto-optimal for FPGAs. In this work, we present the ApproxFPGAs methodology that uses machine learning models to reduce the exploration time for analyzing the state-of-the-art ASIC-based ACs to determine the set of pareto-optimal FPGA-based ACs. We also perform a case-study to illustrate the benefits obtained by deploying these pareto-optimal FPGA-based ACs in a state-of-the-art automation framework to systematically generate pareto-optimal approximate accelerators that can be deployed in FPGA-based systems to achieve high performance or low-power consumption.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218533"}, {"primary_key": "2565924", "vector": [], "sparse_vector": [], "title": "DRMap: A Generic DRAM Data Mapping Policy for Energy-Efficient Processing of Convolutional Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many convolutional neural network (CNN) accelerators face performance- and energy-efficiency challenges which are crucial for embedded implementations, due to high DRAM access latency and energy. Recently, some DRAM architectures have been proposed to exploit subarray-level parallelism for decreasing the access latency. Towards this, we present a design space exploration methodology to study the latency and energy of different mapping policies on different DRAM architectures, and identify the pareto-optimal design choices. The results show that the energy-efficient DRAM accesses can be achieved by a mapping policy that orderly prioritizes to maximize the row buffer hits, bank- and subarray-level parallelism.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218672"}, {"primary_key": "2565925", "vector": [], "sparse_vector": [], "title": "The Best of Both Worlds: Combining CUDA Graph with an Image Processing DSL.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "CUDA graph is an asynchronous task-graph programming model recently released by Nvidia. It encapsulates application workflows in a graph, with nodes being operations connected by dependencies. The new API brings two benefits: Reduced work launch overhead and whole workflow optimizations. In this paper, we improve the ability of CUDA graph to exploit workflow optimizations, e.g., concurrent kernel executions with complementary resource occupancy. Additionally, we argue that the advantages of DSLs are complementary to CUDA graph, and joining the two techniques can benefit from the best of both worlds. Here, we propose a compiler-based approach that combines CUDA graph with an image processing DSL and a source-to-source compiler called Hipacc. For ten image processing applications benchmarked on two Nvidia GPUs, our approach is able to achieve a geometric mean speedup of 1.30 over Hipacc without CUDA graph, 1.11 over CUDA graph without Hipacc, and 3.96 over another state-of-the-art DSL called Halide.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218531"}, {"primary_key": "2565926", "vector": [], "sparse_vector": [], "title": "RaQu: An automatic high-utilization CNN quantization and mapping framework for general-purpose RRAM Accelerator.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Convolutional neural networks (CNNs) have become the state-of-the-art technique in many classification tasks in IoT system. However, the low-power and area-constraint edge devices are unable to afford the expensive cost of CNNs. Resistive random access memory (RRAM) is attractive for establishing the CNN accelerator at the edge end due to the features of scalability, low-power and in-situ dot-product. However, mapping a random network architecture onto a general-purpose RRAM accelerator suffers a severe issue of resource underutilization. The neural network quantization offers an opportunity to rescue the degraded resource utilization. Selecting the bit-width for the vast parameters is impractically completed by human labor. This paper proposes an AutoML-based array-aware quantization and mapping framework that generates the fine-grained mixed-precision neural networks to optimize resource utilization in RRAM. In this framework, we design a two-stage learning and array-aware grouping strategy to quickly explore the huge searching space. The experimental results show that the proposed framework achieves 18.2%~36.1% improvement in resource utilization and 0.9%~3.3% increase in model accuracy over prior coarse-grained quantization methods.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218724"}, {"primary_key": "2565927", "vector": [], "sparse_vector": [], "title": "Exploiting Zero Data to Reduce Register File and Execution Unit Dynamic Power Consumption in GPGPUs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "To achieve high compute performance, graphics processing units (GPUs) provide a large register file and a large number of execution units. However, these design components consume a large portion of the total dynamic power in the GPU, particularly for general purpose applications. In this paper, we present a low-cost gating scheme to reduce dynamic power consumption in the register file and execution units without impacting performance. The scheme proposed dynamically exploit frequent found data value of zeros within and across registers in order to gate off register file reads and writes as well as execution units. We find that on general purpose applications from Rodinia, our low-cost gating scheme can reduce register file reads and writes on average by 35% and 40%, respectively. The register file and execution unit dynamic power are reduced on average by 19% and 13%, respectively. The reduction in total GPU dynamic power achieved is ranging from 3% to 19% with 8% on average with no performance loss.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218547"}, {"primary_key": "2565928", "vector": [], "sparse_vector": [], "title": "ZENCO: Zero-bytes based ENCOding for Non-Volatile Buffers in On-Chip Interconnects.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With multiple cores integrated on the same die, communication across cores is managed by on-chip interconnect called Network-on-Chip (NoC). Power and performance of these interconnect become a significant factor as the communication network has limitations of high network power consumption and delay. The buffers used in the NoC router consume a considerable amount of dynamic as well as static power. This paper attempts to reduce static power consumption by using Non-Volatile Memory technology based STT-RAM buffers. STT-RAM technology has the advantage of higher density and low leakage, but suffer from costly write operation, and weaker write endurance. These characteristics on whole impacts on the total network power consumption, network latency, and lifetime of the router. In this paper, we propose a compression technique at Network Interface, which is based on zero bytes present in data packets. We also propose a compression with the wear-leveling technique, which reduces the write variation in VCs to reduce uneven writes across the buffers.Experimental evaluation on full system simulator shows that proposed policy obtains 0.37 compression ratio and 63% reduction of total network flit. All these results in a significant decrease in total network power. The policies also show remarkable improvement in the lifetime with wear-leveling compression.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218620"}, {"primary_key": "2565929", "vector": [], "sparse_vector": [], "title": "ParaGraph: Layout Parasitics and Device Parameter Prediction using Graph Neural Networks.", "authors": ["Haoxing Ren", "<PERSON>", "<PERSON>", "Ting-<PERSON><PERSON>"], "summary": "Layout-dependent parasitics and device parameters significantly impact integrated circuit performance and are often the cause of slow convergences between schematic and layout designs. Circuit designers typically estimate parasitics from past experience, resulting in variability between designers and the potential for inaccuracies. In this paper, we present ParaGraph: a graph neural network model to predict net parasitics and device parameters by converting circuit schematics into graphs and leveraging key modeling techniques based on GraphSage, Relation GCN and Graph Attention Networks. Furthermore, the use of ensemble modeling increases model accuracy over a large range of prediction values. Trained on a large dataset of industrial circuits, the model achieves an average prediction R 2 of 0.772 (110% better than XGBoost) and reduces average simulation errors from over 100% with designer's estimation to less than 10%.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218515"}, {"primary_key": "2565930", "vector": [], "sparse_vector": [], "title": "AXI HyperConnect: A Predictable, Hypervisor-level Interconnect for Hardware Accelerators in FPGA SoC.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "FPGA-based system-on-chips (SoC) are powerful computing platforms to implement mixed-criticality systems that require both multiprocessing and hardware acceleration. Virtualization via hypervisor technologies is, de-facto, an effective technique to allow the co-existence of multiple execution domains with different criticality levels in isolation upon the same platform. Implementing such technologies on FPGA-based SoC poses new challenges: one of such is the isolation of hardware accelerators deployed on the FPGA fabric that belong to different domains but share common resources such as a memory bus. This paper proposes AXI HyperConnect, a hypervisor-level hardware component that allows interconnecting hardware accelerators to the same bus while ensuring isolation and predictability features. AXI HyperConnect has been implemented on modern FPGA-SoC by Xilinx and tested with real-world accelerators, including one for Deep Neural Network inference.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218652"}, {"primary_key": "2565931", "vector": [], "sparse_vector": [], "title": "A Novel GPU Overdrive Fault Attack.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Graphics processing units (GPUs) are widely used to accelerate applications including cryptographic operations. The reliability and security of GPUs have become a concern. Prior work reported power and timing side-channel attacks on GPUs. In this paper, we present the first-ever overdrive fault attack targeting modern GPUs. This attack exploits voltage-frequency scaling features present on most commercial GPUs to introduce random faults during kernel execution. We demonstrate an effective fault-based attack on an AMD GPU, recovering the AES keys in minutes. Such software-controlled fault injections also pose serious threats to data integrity and service availability in the cloud.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218690"}, {"primary_key": "2565932", "vector": [], "sparse_vector": [], "title": "LoPher: SAT-Hardened Logic Embedding on Block Ciphers.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>"], "summary": "Block ciphers are widely regarded as concrete realizations of pseudorandom permutations with established security features. However, their applicability outside the domain of encryption has not been explored so far. In this paper, we open up, for the first time, an entirely novel application of them to logic hiding. We show that a combinational circuit can always be embedded within a block cipher having a bit-permutation based diffusion layer, preserving the cipher structure and security properties. The functionality of the embedded circuit becomes transparent only on the application of a secret key, whereas a wrong key will cause behaviour that is uncorrelated to that of the circuit. As an immediate application, we propose a combinational logic-locking scheme. The proposed locking scheme is also found to be robust against the state-of-the-art (SAT-assisted and other) attacks on logic locks.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218600"}, {"primary_key": "2565933", "vector": [], "sparse_vector": [], "title": "CL(R)Early: An Early-stage DSE Methodology for Cross-Layer Reliability-aware Heterogeneous Embedded Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cross-layer reliability (CLR) presents a cost-effective alternative to traditional single-layer design in resource-constrained embedded systems. CLR provides the scope for leveraging the inherent fault-masking of multiple layers and exploiting application-specific tolerances to degradation in some Quality of Service (QoS) metrics. However, it can also lead to an explosion in the design complexity. State-of-the art approaches to such joint optimization across multiple degrees of freedom can lead to degradation in the system-level Design Space Exploration (DSE) results. To this end, we propose a DSE methodology for enabling CLR-aware task-mapping in heterogeneous embedded systems. Specifically, we present novel approaches to both task and system-level analysis for performing an early-stage exploration of various design decisions. The proposed methodology results in considerable improvements over other state-of-the-art approaches and shows significant scaling with application size.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218747"}, {"primary_key": "2565934", "vector": [], "sparse_vector": [], "title": "INVITED: Computational Methods of Biological Exploration.", "authors": ["<PERSON>"], "summary": "Our technical ability to collect data about biological systems far outpaces our ability to understand them. Historically, for example, we have had complete and explicit genomes for almost two decades, but we still have no idea what many genes do. More recently a similar situation has arisen, where we can reconstruct huge neural circuits, and/or watch them operate in the brain, but still don't know how they work. This talk covers this second and newer problem, understanding neural circuits. We introduce a variety of computational tools currently being used to attack this data-rich, understanding-poor problems. Examples include dimensionality reduction for nonlinear systems, looking for known and proposed circuits, and using machine learning for parameter estimation. One general theme is the use of biological priors, to help fill in unknowns, see if proposed solutions are feasible, and more generally aid understanding.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218671"}, {"primary_key": "2565935", "vector": [], "sparse_vector": [], "title": "Symbolic Computer Algebra and SAT Based Information Forwarding for Fully Automatic Divider Verification.", "authors": ["<PERSON>", "<PERSON>"], "summary": "During the last few years Symbolic Computer Algebra (SCA) delivered excellent results in the verification of large integer and finite field multipliers at the gate level. In contrast to those encouraging advances, SCA-based divider verification has been still in its infancy and awaited a major breakthrough. In this paper we analyze the fundamental reasons that prevented the success for SCA-based divider verification so far and present SAT Based Information Forwarding (SBIF). SBIF enhances SCA-based backward rewriting by information propagation in the opposite direction. We successfully apply the method to the fully automatic formal verification of large non-restoring dividers.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218721"}, {"primary_key": "2565936", "vector": [], "sparse_vector": [], "title": "Neural Network-Based Side Channel Attacks and Countermeasures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper surveys results in the use of neural networks and deep learning in two areas of hardware security: power attacks and physically-unclonable functions (PUFs).", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218511"}, {"primary_key": "2565937", "vector": [], "sparse_vector": [], "title": "Impeccable Circuits II.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Protection against active physical attacks is of serious concerns of cryptographic hardware designers. Introduction of SIFA invalidating several previously-thought-effective counter-measures, made this challenge even harder. Here in this work we deal with error correction, and introduce a methodology which shows, depending on the selected adversary model, how to correctly embed error-correcting codes in a cryptographic implementation. Our construction guarantees the correction of faults, in any location of the circuit and at any clock cycle, as long as they fit into the underlying adversary model. Based on case studies evaluated by open-source fault diagnostic tools, we claim protection against SIFA.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218615"}, {"primary_key": "2565938", "vector": [], "sparse_vector": [], "title": "TDP-ADMM: A Timing Driven Placement Approach for Superconductive Electronic Circuits Using Alternating Direction Method of Multipliers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a novel timing driven global placement approach utilizing the alternating direction method of multipliers (ADMM) targeting superconductive electronic circuits. The proposed algorithm models the placement problem as an optimization problem with constraints on the maximum wirelength delay of timing-critical paths and employs the ADMM algorithm to decompose the problem into two sub-problems, one minimizing the total wirelength of the circuit and the other minimizing the delay of timing-critical paths of the circuit. Through an iterative process, a placement solution is generated that simultaneously minimizes the total wirelength and satisfies the setup time constraints. Compared to an state-of-the-art academic global placement tool, the proposed method (called TDP-ADMM) improves the worst and total negative slack for seven single flux quantum benchmark circuits by an average of 26% and 44%, respectively, with an average overhead of 1.98% in terms of total wirelength.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218527"}, {"primary_key": "2565939", "vector": [], "sparse_vector": [], "title": "Robust Design of Large Area Flexible Electronics via Compressed Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Large area flexible electronics (FE) is emerging for low-cost, light-weight wearable electronics, artificial skins and IoT nodes, benefiting from its low-cost fabrication and mechanical flexibility. How-ever, the low temperature requirement for fabrication on a flexible substrate and the large-area nature of flexible sensor arrays inevitably result in inadequate device yield, reliability and stability. Therefore, it is essential to develop design methodologies for large area sensing applications which can ensure system robustness with-out relying on highly reliable devices. Based on the observation that most signals sensed by body sensor arrays exhibit sparse statistical characteristics, we propose a system design method which lever-ages the sparse nature via compressed sensing (CS). Specifically, we use flexible circuitry to implement a CS encoder and decode the compressed signal in the silicon side. As a system demonstration, we fabricated the temperature sensor array, shift register and amplifier to illustrate the feasibility of the encoder design using carbon-nanotube-based flexible thin-film transistors. To evaluate the improvement of system robustness achieved by the proposed sensing schema, we conducted two case studies: temperature imaging and tactile-sensor based object recognition. With ~10% sparse errors (due to either device defects or transient errors), we achieved reduction of root-mean-square-error (RMSE) from 0.20 to 0.05 for temperature sensing and boost the classification accuracy from 65% to 84% for tactile-sensing based object recognition.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218570"}, {"primary_key": "2565940", "vector": [], "sparse_vector": [], "title": "TYMER: A Yield-based Performance Model for Timing-speculation SRAM.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Longxing Shi"], "summary": "In low power designs, timing-speculative techniques are proposed to boost the SRAM frequency and throughput. This paper proposes TYMER, a unified yield-based performance model for timing-speculative SRAM. In TYMER, the first sub-model evaluates access-time yield at different worldline enable time for a general 6T SRAM under low supply voltages, while the second one uses the yield results to estimate optimal sensing time and the overall read latency for speculative SRAM. TYMER is not only compared with simulation results but also the measurements from 28nm fabricated speculative SRAM chips. Both cases show precise evaluation results under different operating conditions.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218623"}, {"primary_key": "2565941", "vector": [], "sparse_vector": [], "title": "FTDL: A Tailored FPGA-Overlay for Deep Learning with High Scalability.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fast inference is of paramount value to a wide range of deep learning applications. This work presents FTDL, a highly-scalable FPGA overlay framework for deep learning applications, to address the architecture and hardware mismatch faced by traditional efforts. The FTDL overlay is specifically optimized for the tiled structure of FPGAs, thereby achieving post-place-and-route operating frequencies exceeding 88 % of the theoretical maximum across different devices and design scales. A flexible compilation framework efficiently schedules matrix multiply and convolution operations of large neural network inference on the overlay and achieved over 80 % hardware efficiency on average. Taking advantage of both high operating frequency and hardware efficiency, FTDL achieves 402.6 and 151.2 FPS with GoogLeNet and ResNet50 on ImageNet, respectively, while operating at a power efficiency of 27.6 GOPS/W, making it up to 7.7× higher performance and 1.9× more power-efficient than the state-of-the-art.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218581"}, {"primary_key": "2565942", "vector": [], "sparse_vector": [], "title": "A Pragmatic Approach to On-device Incremental Learning System with Selective Weight Updates.", "authors": ["<PERSON>aekan<PERSON> Shin", "Seungky<PERSON> Choi", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Incremental learning is drawing attention to widen capabilities of device-AI. Previous works have researched to reduce numerous computations and memory accesses required for the training process of IL, but they could not show a noticeable improvement in the weight gradient computation (WGC) phase. Therefore, we propose a selective weight update technique that searches for critical weights to be updated by applying the IL algorithm that training per-task binary masks. Also, we introduce a novel dataflow for the implementation of selective WGC on typical NPUs with minimum overheads. On average, our system shows a 2.9× speed up and 2.5× energy efficiency in WGC without degrading training quality.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218507"}, {"primary_key": "2565943", "vector": [], "sparse_vector": [], "title": "Prediction Confidence based Low Complexity Gradient Computation for Accelerating DNN Training.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jongsun Park"], "summary": "In deep neural network (DNN) training, network weights are iteratively updated with the weight gradients that are obtained from stochastic gradient descent (SGD). Since SGD inherently allows gradient calculations with noise, approximating weight gradient computations have a large potential of training energy/time savings without degrading accuracy. In this paper, we propose an input-dependent approximation of the weight gradient for improving energy efficiency of training process. Considering that the output predictions of network (confidence) changes with training inputs, the relation between the confidence and the magnitude of weight gradient can be efficiently exploited to skip the gradient computations without accuracy drop, especially for high confidence inputs. With a given squared error constraint, the computation skip rates can be also controlled by changing the confidence threshold. The simulation results show that our approach can skip 72.6% of gradient computations for CIFAR-100 dataset using ResNet-18 without accuracy degradation. Hardware implementation with 65nm CMOS process shows that our design achieves 88.84% and 98.16% of maximum per epoch training energy and time savings, respectively, for CIFAR-100 dataset using ResNet-18 compared to state-of-the-art training accelerator.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218650"}, {"primary_key": "2565944", "vector": [], "sparse_vector": [], "title": "MLParest: Machine Learning based Parasitic Estimation for Custom Circuit Design.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A novel machine learning based parasitic estimation (MLParest) method for pre-layout custom circuit design is presented. It reduces the error between pre-layout and post-layout circuit simulation from 37% to 8% on average for different measurements across a variety of analog circuits. MLParest can thus greatly reduce the number of iterations between pre-layout and post-layout design phases. The key contributions of this work are a machine learning based approach to parasitic estimation and a push-button model training framework, scalable across different technology nodes. To the best of our knowledge, a machine learning based framework of parasitic estimation is an industry first.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218495"}, {"primary_key": "2565945", "vector": [], "sparse_vector": [], "title": "A-QED Verification of Hardware Accelerators.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present A-QED (Accelerator-Quick Error Detection), a new approach for pre-silicon formal verification of stand-alone hardware accelerators. A-QED relies on bounded model checking -- however, it does not require extensive design-specific properties or a full formal design specification. While A- QED is effective for both RTL and high-level synthesis (HLS) design flows, it integrates seamlessly with HLS flows. Our A-QED results on several hardware accelerator designs demonstrate its practicality and effectiveness: 1. A-QED detected all bugs detected by conventional verification flow. 2. A-QED detected bugs that escaped conventional verification flow. 3. A-QED improved verification productivity dramatically, by 30X, in one of our case studies (1 person-day using A-QED vs. 30 person-days using conventional verification flow). 4. A-QED produced short counterexamples for easy debug (37X shorter on average vs. conventional verification flow).", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218715"}, {"primary_key": "2565946", "vector": [], "sparse_vector": [], "title": "Statistical Timing Analysis considering Multiple-Input Switching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chaitanya Peddawad", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Traditional statistical static timing analysis (SSTA) using available single-input switching (SIS) based gate delay libraries either ignore timing impact from multiple-input switching (MIS) or use single-corner (deterministic) models for MIS consideration. This paper presents a method for modeling the impact of MIS on statistical timing accurately using concepts of convolution and chain ruling. Experimental results in a commercial SSTA framework demonstrate negligible run-time overheads of modeling MIS while accurately exposing timing slack optimism in an SIS based flow of up to 17 pico-seconds on designs mapped to a 14 nanometer technology library. Prediction of timing critical paths due to MIS show excellent correlation to silicon hardware. Comparisons with prior work illustrate the accuracy improvements of the presented work.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218601"}, {"primary_key": "2565947", "vector": [], "sparse_vector": [], "title": "GPNPU: Enabling Efficient Hardware-Based Direct Convolution with Multi-Precision Support in GPU Tensor Cores.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Li <PERSON>", "Jing <PERSON>", "<PERSON><PERSON><PERSON>", "Naifeng Jing"], "summary": "To tailor for DNN (Deep Neural Network) acceleration, GPU has migrated to new architectures such as NVIDIA Volta and Turing that incorporate dedicated Tensor Cores. Although good at GEMM (generic matrix-matrix multiplication), Tensor Cores still have inefficiency facing convolutions with certain layer structures. This paper proposes a GPNPU (General-Purpose Neural-network Processing Unit) architecture, which offers another option of direct convolution in GPU. It stitches the direct convolution dataflow into the Tensor Cores with little hardware support, and resorts to regulated data layout with stripe-mined convolution execution to achieve higher performance and power efficiency, while retaining the general programability as GPU. We further apply a unified core design to support varied operand types and precision for higher computing throughput. The evaluation shows that GPNPU can outperform Tensor Cores on typical DNNs by 1.4X for inference (FP16) and 1.2X for training with much reduced power. The INT8 performance even increases to 2.4X. Our study demonstrates that it is possible and appealing to refine the Tensor Cores for greater DNN acceleration, while conforming to GPU architecture for the programmability necessary in future DNN evolution.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218566"}, {"primary_key": "2565948", "vector": [], "sparse_vector": [], "title": "Enhancing Thread-Level Parallelism in Asymmetric Multicores using Transparent Instruction Offloading.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Asymmetric multicore architectures (AMC) with single-ISA can accelerate multi-threaded applications by running the serial region on the big core and the parallel region on multiple small cores. In such architectures, all cores implement resource-expensive and application-specific instruction extensions (e.g., SIMD and FP). We argue that instead of implementing such extensions in the big core, the resources must be traded-off to increase the number of small cores. Furthermore, when the big core requires such instruction extensions, we offload execution to the small cores. This design mainly leverages the observation that SIMD/FP operations are more frequently executed inside parallel regions. The proposed AMC provides an additional 1.76x speedup and 12.4% energy savings compared to a traditional AMC of the same area due to enhanced thread-level parallelism (TLP) exploitation.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218614"}, {"primary_key": "2565949", "vector": [], "sparse_vector": [], "title": "Scenario-Based Soft Real-Time Hybrid Application Mapping for MPSoCs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For soft real-time applications, a fixed mapping to a heterogeneous MPSoC architecture can lead to high energy consumption and even deadline misses if tasks have input-dependent execution times. Here, specialized mappings are required that, e.g., map tasks with high execution times for the current input to resources with high computational power as they else may cause deadline misses. However, optimizing mappings for both energy and latency at run time is too compute-intensive. As a remedy, we propose a hybrid application mapping technique suited for blackbox applications, i.e., no information about functional behaviors is available. It is based on clustering input data evoking similar workloads into so-called workload scenarios. At design time, we optimize the scenario distribution and their associated mappings regarding energy consumption and latency by an iterative design space exploration. At run time, a machine-learning-based runtime manager first identifies the scenario of the current input by monitoring its non-functional execution properties. Based on these identified scenarios, a mapping for subsequent data processing is selected so that missed deadlines and the energy are minimized. Evaluations performed based on two dynamic applications show that the proposed hybrid application mapping procedure consistently outperforms state-of-the-art mapping approaches with regard to both deadline misses and energy consumption.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218537"}, {"primary_key": "2565950", "vector": [], "sparse_vector": [], "title": "VarSim: A Fast and Accurate Variability and Leakage Aware Thermal Simulator.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Some of the fastest thermal estimation techniques at the architectural level are based on Green's functions (impulse response of a unit power source). The resultant temperature profile can be easily obtained by computing a convolution of the Green's function and the power profile. Sadly, existing approaches do not take process and temperature variation into account, which are integral aspects of today's technologies. This problem is still open. In this paper, we provide a closed-form solution for the Green's function after taking process, temperature, and thermal conductivity variation into account. Moreover, during the process of computing the thermal map, we reduce the amount of redundant work by identifying similar regions in the chip using an unsupervised learning-based approach. We were able to obtain a 700,000X speedup over state-of-the-art proposals with a mean absolute error limited to 0.7 ◦ C (1.5%).", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218754"}, {"primary_key": "2565951", "vector": [], "sparse_vector": [], "title": "On Computing Exact WCRT for DAG Tasks†.", "authors": ["Jinghao Sun", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Most current real-time parallel applications can be modeled as a directed acyclic graph (DAG) task. Existing worst-case response time (WCRT) bounds (e.g., <PERSON>'s bound) derived for DAGs may be very pessimistic. No one precisely knows the gap between the WCRT bound and the actual WCRT. In this paper, we aim to derive the exact WCRT of a DAG task under the list scheduling upon multi-core platforms. We encode the WCRT analysis problem into a satisfaction modular theoretical (SMT) formulation based on insights into the list scheduling algorithm, and prove that our SMT program can solve the WCRT precisely, providing an accurate baseline to measure the tightness of the existing WCRT bounds. Experiments show that our method significantly improves the tightness of the WCRT bound, and is practically quite efficient, e.g., it can analyze DAGs with more than 40 vertices in a few seconds.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218744"}, {"primary_key": "2565952", "vector": [], "sparse_vector": [], "title": "INVITED: Computation on Sparse Neural Networks and its Implications for Future Hardware.", "authors": ["<PERSON><PERSON>", "Minghai Qin", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Neural network models are widely used in solving many challenging problems, such as computer vision, personalized recommendation, and natural language processing. Those models are very computationally intensive and reach the hardware limit of the existing server and IoT devices. Thus, finding better model architectures with much less amount of computation while maximally preserving the accuracy is a popular research topic. Among various mechanisms that aim to reduce the computation complexity, identifying the zero values in the model weights and in the activations to avoid computing them is a promising direction. In this paper, we summarize the current status of the research on the computation of sparse neural networks, from the perspective of the sparse algorithms, the software frameworks, and the hardware accelerations. We observe that the search for the sparse structure can be a general methodology for high-quality model explorations, in addition to a strategy for high-efficiency model execution. We discuss the model accuracy influenced by the number of weight parameters and the structure of the model. The corresponding models are called to be located in the weight dominated and structure dominated regions, respectively. We show that for practically complicated problems, it is more beneficial to search large and sparse models in the weight dominated region. In order to achieve the goal, new approaches are required to search for proper sparse structures, and new sparse training hardware needs to be developed to facilitate fast iterations of sparse models.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218645"}, {"primary_key": "2565953", "vector": [], "sparse_vector": [], "title": "3D CNN Acceleration on FPGA using Hardware-Aware Pruning.", "authors": ["<PERSON><PERSON><PERSON> Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "There have been many recent attempts to extend the successes of convolutional neural networks (CNNs) from 2-dimensional (2D) image classification to 3-dimensional (3D) video recognition by exploring 3D CNNs. Considering the emerging growth of mobile or Internet of Things (IoT) market, it is essential to investigate the deployment of 3D CNNs on edge devices. Previous works have implemented standard 3D CNNs (C3D) on hardware platforms, however, they have not exploited model compression for acceleration of inference. This work proposes a hardware-aware pruning approach that can fully adapt to the loop tiling technique of FPGA design and is applied onto a novel 3D network called R(2+1)D. Leveraging the powerful ADMM, the proposed pruning method achieves simultaneous high accuracy and significant acceleration of computation on FPGA. With layer-wise pruning rates up to 10× and negligible accuracy loss, the pruned model is implemented on a Xilinx ZCU102 FPGA board, where the pruned model achieves 2.6× speedup compared with the unpruned version, and 2.3× speedup and 2.3× power efficiency improvement compared with state-of-the-art FPGA implementation of C3D.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218571"}, {"primary_key": "2565954", "vector": [], "sparse_vector": [], "title": "INVITED: A 0.26% BER, Machine-Learning Resistant 1028 Challenge-Response PUF in 14nm CMOS Featuring Stability-Aware Adversarial Challenge Selection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A 10 28 challenge-response strong-PUF circuit is fabricated in 14nm CMOS, demonstrating modeling attack resistance across 6-million training samples. Two-stage nonlinearly cascaded PUF array organization with adversarial challenge selection increases modeling complexity by 4×, limiting ML attack accuracy to ~50%. A configurable cross-coupled inverter-based entropy source with stability-aware challenge pruning enables 9.8× higher array density, linearly-scaling enrollment data and worst-case bit-error-rate of 0.26% measured across 650-850mV, 0-100°C operation.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218720"}, {"primary_key": "2565955", "vector": [], "sparse_vector": [], "title": "Algorithm-Hardware Co-Design of Adaptive Floating-Point Encodings for Resilient Deep Learning Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Conventional hardware-friendly quantization methods, such as fixed-point or integer, tend to perform poorly at very low precision as their shrunken dynamic ranges cannot adequately capture the wide data distributions commonly seen in sequence transduction models. We present an algorithm-hardware co-design centered around a novel floating-point inspired number format, AdaptivFloat, that dynamically maximizes and optimally clips its available dynamic range, at a layer granularity, in order to create faithful encodings of neural network parameters. AdaptivFloat consistently produces higher inference accuracies compared to block floating-point, uniform, IEEE-like float or posit encodings at low bit precision (≤8-bit) across a diverse set of state-of-the-art neural networks, exhibiting narrow to wide weight distribution. Notably, at 4-bit weight precision, only a 2.1 degradation in BLEU score is observed on the AdaptivFloat-quantized Transformer network compared to total accuracy loss when encoded in the above-mentioned prominent datatypes. Furthermore, experimental results on a deep neural network (DNN) processing element (PE), exploiting AdaptivFloat logic in its computational datapath, demonstrate per-operation energy and area that is 0.9× and 1.14×, width, respectively that of an equivalent bit NVDLA-like integer-based PE.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218516"}, {"primary_key": "2565956", "vector": [], "sparse_vector": [], "title": "PCNN: Pattern-based Fine-Grained Regular Pruning Towards Optimizing CNN Accelerators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hongyang Chen", "Yuanqing Miao", "<PERSON><PERSON>", "Shaokai Ye", "<PERSON><PERSON>", "<PERSON><PERSON>", "Kaisheng Ma"], "summary": "Weight pruning is a powerful technique to realize model compression. We propose PCNN, a fine-grained regular 1D pruning method. A novel index format called Sparsity Pattern Mask (SPM) is presented to encode the sparsity in PCNN. Leveraging SPM with limited pruning patterns and non-zero sequences with equal length, PCNN can be efficiently employed in hardware. Evaluated on VGG-16 and ResNet-18, our PCNN achieves the compression rate up to 8.4× with only 0.2% accuracy loss. We also implement a pattern-aware architecture in 55nm process, achieving up to 9.0× speedup and 28.39 TOPS/W efficiency with only 3.1% on-chip memory overhead of indices.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218498"}, {"primary_key": "2565957", "vector": [], "sparse_vector": [], "title": "Towards Memory-Efficient Streaming Processing with Counter-Cascading Sketching on FPGA.", "authors": ["Minjin Tang", "<PERSON>", "<PERSON><PERSON><PERSON> Shen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Obtaining item frequencies in data streams with limited space is a well-recognized and challenging problem in a wide range of applications. Sketch-based solutions have been widely used to address this challenge due to their ability to accurately record the data streams at a low memory cost. However, most sketches suffer from low memory utilization due to the adoption of a fixed counter size. Accordingly, in this work, we propose a counter-cascading scheduling algorithm to maximize the memory utilization of sketches without incurring any accuracy loss. In addition, we propose an FPGA-based system design that supports sketch parameter learning, counter-cascading record and online query. We implement our designs on Xilinx VCU118, and conduct evaluations on real-world traces, thereby demonstrating that our design can achieve higher accuracy with lower storage; the performance achieved is 10× ~ 20× better than that of state-of-the-art sketches.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218503"}, {"primary_key": "2565958", "vector": [], "sparse_vector": [], "title": "A Machine Learning Approach for Reliability-Aware Application Mapping for Heterogeneous Multicores.", "authors": ["<PERSON>", "<PERSON>ago Mayk G. de A. <PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a transparent and runtime methodology to increase the system's Mean Workload to Failure (MWTF) in heterogeneous multicore processors. For that, we leverage an Artificial Neural Network that makes online predictions of the core's Architectural Vulnerability Factor (AVF), which allows for reliability-aware application-to-core mappings. We experiment with different configurations of RISC-V cores and compare the MWTF of prediction-based mappings against the optimal oracle, showing that our proposed model provides MWTF as close as 5.6% to the oracle. We also compare homogeneous and heterogeneous multicores, showing that heterogeneity provides room for increasing the MWTF in up to 19.4%.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218543"}, {"primary_key": "2565959", "vector": [], "sparse_vector": [], "title": "Invited: Software Defined Accelerators From Learning Tools Environment.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Next generation systems, such as edge devices, will need to provide efficient processing of machine learning (ML) algorithms along several metrics, including energy, performance, area, and latency. However, the quickly evolving field of ML makes it extremely difficult to generate accelerators able to support a wide variety of algorithms. At the same time, designing accelerators in hardware description languages (HDLs) by hand is hard and time consuming, and does not allow quick exploration of the design space. In this paper we present the Software Defined Accelerators From Learning Tools Environment (SODALITE), an automated open source high-level ML framework-to-verilog compiler targeting ML Application-Specific Integrated Circuits (ASICs) chiplets. The SODALITE approach will implement optimal designs by seamlessly combining custom components generated through high-level synthesis (HLS) with templated and fully tunable Intellectual Properties (IPs) and macros, integrated in an extendable resource library. Through a closed loop design space exploration engine, developers will be able to quickly explore their hardware designs along different dimensions.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218489"}, {"primary_key": "2565960", "vector": [], "sparse_vector": [], "title": "Factored Radix-8 Systolic Array for Tensor Processing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Systolic arrays are re-gaining the attention as the heart to accelerate machine learning workloads. This paper shows that a large design space exists at the logic level despite the simple structure of systolic arrays and proposes a novel systolic array based on factoring and radix-8 multipliers. The factored systolic array (FSA) extracts out the booth encoding and the hard-multiple generation which is common across all processing elements, reducing the delay and the area of the whole systolic array. This factoring is done at the cost of an increased number of registers, however, the reduced pipeline register requirement in radix-8 offsets this effect. The proposed factored 16-bit multiplier achieves up to 15%, 13%, and 23% better delay, area, and power, respectively, compared with the radix-4 multipliers even if the register overhead is included. The proposed FSA architecture improves delay, area, and power up to 11%, 20% and 31%, respectively, for different bitwidths when compared with the conventional radix-4 systolic array.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218585"}, {"primary_key": "2565961", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: A Neural Network that Routes ICs.", "authors": ["<PERSON>", "<PERSON><PERSON>-Vaisband"], "summary": "A global router is proposed that learns from routed circuits and autonomously routes unseen layouts. The uniqueness of this approach is in redefining the global routing as a classical image-to-image processing problem. The imaging problem is efficiently solved with a deep learning system, comprising a variational autoencoder and custom loss function. This fundamentally new routing method provides a natural way for global routing parallelization. The deep router is designed, trained, and tested on an unseen 64×64 ISPD'98 benchmark circuit. The test results yield 3.2% decrease in routability and over 5X speedup in runtime as compared with the state-of-the-art FastRoute router.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218598"}, {"primary_key": "2565962", "vector": [], "sparse_vector": [], "title": "Learning From A Big Brother - Mimicking Neural Networks in Profiled Side-channel Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, deep learning has emerged as a powerful technique for side-channel attacks, capable of even breaking common countermeasures. Still, trained models are generally large, and thus, performing evaluation becomes resource-intensive. The resource requirements increase in realistic settings where traces can be noisy, and countermeasures are active. In this work, we exploit mimicking to compress the learned models. We demonstrate up to 300 times compression of a state-of-the-art CNN. The mimic shallow network can also achieve much better accuracy as compared to when trained on original data and even reach the performance of a deeper network.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218520"}, {"primary_key": "2565963", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Building an On-Chip Deep Learning Memory Hierarchy Brick by Brick.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Data accesses between on- and off-chip memories account for a large fraction of overall energy consumption during inference with deep learning networks. We present Boveda, a lossless on-chip memory compression technique for neural networks operating on fixed-point values. Boveda reduces the datawidth used per block of values to be only as long as necessary: since most values are of small magnitude Boveda drastically reduces their footprint. Boveda can be used to increase the effective on-chip capacity, to reduce off-chip traffic, or to reduce the on-chip memory capacity needed to achieve a performance/energy target. Boveda reduces total model footprint to 53%.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218728"}, {"primary_key": "2565964", "vector": [], "sparse_vector": [], "title": "Verification for Field-coupled Nanocomputing Circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the decline of <PERSON>'s Law, several post-CMOS technologies are currently under heavy consideration. Promising candidates can be found in the class of Field-coupled Nanocomputing (FCN) devices as they allow for highest processing performance with tremendously low energy dissipation. With upcoming design automation in this domain, the need for formal verification approaches arises. Unfortunately, FCN circuits come with certain domain-specific properties that render conventional methods for the verification non-applicable. In this paper, we investigate this issue and propose a verification approach for FCN circuits that addresses this problem. For the first time, this provides researchers and engineers with an automatic method that allows them to check whether an obtained FCN circuit design indeed implements the given/desired function. A prototype implementation demonstrates the applicability of the proposed approach.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218641"}, {"primary_key": "2565965", "vector": [], "sparse_vector": [], "title": "DRAMDig: A Knowledge-assisted Tool to Uncover DRAM Address Mapping.", "authors": ["<PERSON><PERSON> Wang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Surya Nepal"], "summary": "As recently emerged rowhammer exploits require undocumented DRAM address mapping, we propose a generic knowledge-assisted tool, DRAMDig, which takes domain knowledge into consideration to efficiently and deterministically uncover the DRAM address mappings on any Intel-based machines. We test DRAMDig on a number of machines with different combinations of DRAM chips and microarchitectures ranging from Intel Sandy Bridge to Coffee Lake. Comparing to previous works, DRAMDig deterministically reverse-engineered DRAM address mappings on all the test machines with only 7.8 minutes on average. Based on the uncovered mappings, we perform double-sided rowhammer tests and the results show that DRAMDig induced significantly more bit flips than previous works, justifying the correctness of the uncovered DRAM address mappings.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218599"}, {"primary_key": "2565966", "vector": [], "sparse_vector": [], "title": "An Efficient Deep Learning Accelerator for Compressed Video Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous neural network accelerators tailored to video analysis only accept data of RGB/YUV domain, requiring decompressing the video that are often compressed before transmitted from the edge sensors. A compressed video processing accelerator can remove the decoding overhead, and gain performance speedup by operating on more compact input data. This work proposes a novel deep learning accelerator architecture, Alchemist, which predicts results directly from the compressed video bitstream instead of reconstructing the full RGB images. By utilizing the metadata of motion vector and critical blocks extracted from bitstream, Alchemist contributes to remarkable performance speedup of 5x with negligible accuracy loss.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218743"}, {"primary_key": "2565967", "vector": [], "sparse_vector": [], "title": "How to Cut Out Expired Data with Nearly Zero Overhead for Solid-State Drives.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Owing to flash memory constraints, a garbage collection (GC) mechanism hurts flash storage lifespan and performance since it generates a massive amount of write data to flash memory. To add insult to injury, all GC designs cannot identify disused data from valid data; therefore, all valid data, including disused data, will be rewritten to flash memory during the GC process. Fortunately, a flash storage vendor recently proposed a new write command to bring extra information to flash translation layer (FTL). Thanks to the new write command, the lifetime information of data can be brought from a host-side system to an FTL management layer for disused data identification. By such observations, this work proposes a dual-time referencing FTL (DTR-FTL) design to deal with disused data and minimize the overhead of GC by referring to data lifetime information and block retention time.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218610"}, {"primary_key": "2565968", "vector": [], "sparse_vector": [], "title": "An Efficient and Robust Yield Optimization Method for High-dimensional SRAM Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chang<PERSON> Yan", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to time-consuming SPICE simulations and extremely low failure rates, yield optimization for large static random access memory (SRAM) circuits is still a challenging problem. In this paper, a novel robust yield optimization problem is firstly proposed for SRAM circuits, where robust means considering design and process parameter variations simultaneously. Both a multi-fidelity Gaussian process regression model, which utilizes the strong nonlinear relationship between small and large SRAM columns, and a Bayesian optimization framework are applied to guide the sampling of the expensive large SRAM circuits. A multimodal problem is formulated to find all peaks and valleys on the small SRAM circuits. Such precomputational knowledge can accelerate the convergence of the proposed multi-fidelity and Bayesian optimization framework. Experimental results show that robust yield is essential to yield optimization, for traditional optimal design will degenerate with 4-5 orders of magnitude of yields, if design variations considered, and it doesn't coincide with the new optimum under the robust yield. The proposed method can gain a 3~4× speedup compared to the state-of-the-art method without loss of accuracy.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218611"}, {"primary_key": "2565969", "vector": [], "sparse_vector": [], "title": "Characterization and Applications of Spatial Variation Models for Silicon Microring-Based Optical Transceivers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Photonic integrated circuits suffer from large process variations. Effective and accurate characterization of the variation patterns is a critical task for enabling the development of novel techniques to alleviate the variation challenges. In this study, we propose a hierarchical approach that effectively decomposes the spatial variations of silicon microring-based optical transceivers into wafer-level, intra-die, and inter-die components. We then demonstrate that the characterized variation models can be used to generate trustworthy synthetic data for architecture- and system-level solutions for variation alleviation. We further demonstrate the utility of our variation characterization method for accurate yield prediction based on partial measurement data.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218608"}, {"primary_key": "2565970", "vector": [], "sparse_vector": [], "title": "CDRing: Reconfigurable Ring Architecture by Exploiting Cycle Decomposition of Torus Topology.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Future NoCs should be highly flexible to adapt to communication demands to achieve high scalability and low power consumption. However, the flexibility is still quite limited by the high complexity of reconfiguration for globally reconfigured channels. In this paper, we propose to augment a router-based buffered NoC with a reconfigurable ring architecture by exploiting cycle decomposition of a torus bufferless network. At runtime, the topologies of the rings can be reconfigured according to the workloads by choosing different cycle decompositions of the torus network. Because the shapes of the rings are restricted to a specified regular shape, the reconfiguration time can be reduced to a linear complexity with respect to network size, and the reconfiguration algorithm can be implemented in a distributed hardware. The experimental results show that the reconfigurable rings provide 54% and 26% improvements on packet latency and static power saving, respectively, for realistic workloads.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218647"}, {"primary_key": "2565971", "vector": [], "sparse_vector": [], "title": "Improving the Concurrency Performance of Persistent Memory Transactions on Multicores.", "authors": ["<PERSON>", "Youyou Lu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Persistent memory provides data persistence to in-memory transaction systems, enabling full ACID properties. However, high data persistence worsens the concurrency performance due to delayed execution of conflicted transactions on multicores. In this paper, we propose SP 3 (SPeculative Parallel Persistence) to improve the concurrency performance of persistent memory transactions. SP 3 keeps the dependencies between different transactions in a DAG (direct acyclic graph) by detecting conflicts in the read/write sets, and speculatively executes conflicted transactions without waiting for the completeness of data persistence. Evaluation shows that SP 3 significantly improves concurrency performance and achieves almost linear scalability in most evaluated workloads.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218554"}, {"primary_key": "2565972", "vector": [], "sparse_vector": [], "title": "Machine Leaming to Set Meta-Heuristic Specific Parameters for High-Level Synthesis Design Space Exploration.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Raising the level of VLSI design abstraction to C leads to many advantages compared to the use of low-level Hardware Description Languages (HDLs). One key advantage is that it allows the generation of micro-architectures with different trade-offs by simply setting unique combinations of synthesis options. Because the number of these synthesis options is typically very large, exhaustive enumerations are not possible. Hence, heuristics are required. Meta-heuristics like Simulated Annealing (SA), Genetic Algorithm (GA) and Ant Colony Optimizations (ACO) have shown to lead to good results for these types of multi-objective optimization problems. The main problem with these meta-heuristics is that they are very sensitive to their hyper-parameter settings, e.g. in the GA case, the mutation and crossover rate and the number of parents pairs. To address this, in this work we present a machine learning based approach to automatically set the search parameters for these three meta-heuristics such that a new unseen behavioral description given in C can be effectively explored. Moreover, we present an exploration technique that combines the SA, GA and ACO together and show that our proposed exploration method outperforms a single meta-heuristic.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218674"}, {"primary_key": "2565973", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Automatic Adaptive MOM Capacitor Cell Generation for Analog and Mixed-Signal Layout Design.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces the first problem formulation in the literature for automatic MOM capacitor cell generation with adaptive capacitance. Given an expected capacitance value and available metal layers, the proposed capacitor cell generation method can produce a compact MOM capacitor cell with minimized area and matched capacitance. Compared with MOM capacitor cells with non-adaptive capacitance in the previous work, the experimental results show that the proposed adaptive MOM capacitor cell generation method can reduce 25% chip area and 20% power consumption of the capacitor network in successive-approximation-register analog-to-digital converters (SAR ADC).", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218609"}, {"primary_key": "2565974", "vector": [], "sparse_vector": [], "title": "High PE Utilization CNN Accelerator with Channel Fusion Supporting Pattern-Compressed Sparse Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Yu", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zhuqing Yuan", "<PERSON><PERSON><PERSON>", "Xueqing Li", "<PERSON><PERSON>"], "summary": "Recently CNN-based methods have made remarkable progress in broad fields. Both network pruning algorithms and hardware accelerators have been introduced to accelerate CNN. However, existing pruning algorithms have not fully studied the pattern pruning method, and current index storage scheme of sparse CNN is not efficient. Furthermore, the performance of existing accelerators suffers from no-load PEs on sparse networks. This work proposes a software-hardware co-design to address these problems. The software includes an ADMM-based method which compresses the patterns of convolution kernels with acceptable accuracy loss, and a <PERSON><PERSON><PERSON> encoding method which reduces index storage overhead. The hardware is a fusion-enabled systolic architecture, which can reduce PEs' no-load rate and improve performance by supporting the channel fusion. On CIFAR-10, this work achieves 5.63× index storage reduction with 2-7 patterns among different layers with 0.87% top-1 accuracy loss. Compared with the state-of-art accelerator, this work achieves 1.54×-1.79× performance and 25%-34% reduction of no-load rate with reasonable area and power overheads.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218630"}, {"primary_key": "2565975", "vector": [], "sparse_vector": [], "title": "TCIM: Triangle Counting Acceleration With Processing-In-MRAM Architecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xingzhou Cheng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Zhao"], "summary": "Triangle counting (TC) is a fundamental problem in graph analysis and has found numerous applications, which motivates many TC acceleration solutions in the traditional computing platforms like GPU and FPGA. However, these approaches suffer from the bandwidth bottleneck because TC calculation involves a large amount of data transfers. In this paper, we propose to overcome this challenge by designing a TC accelerator utilizing the emerging processing-in-MRAM (PIM) architecture. The true innovation behind our approach is a novel method to perform TC with bitwise logic operations (such as AND), instead of the traditional approaches such as matrix computations. This enables the efficient in-memory implementations of TC computation, which we demonstrate in this paper with computational Spin-Transfer Torque Magnetic RAM (STT-MRAM) arrays. Furthermore, we develop customized graph slicing and mapping techniques to speed up the computation and reduce the energy consumption. We use a device-to-architecture co-simulation framework to validate our proposed TC accelerator. The results show that our data mapping strategy could reduce 99.99% of the computation and 72% of the memory WRITE operations. Compared with the existing GPU or FPGA accelerators, our in-memory accelerator achieves speedups of 9× and 23.4×, respectively, and a 20.6× energy efficiency improvement over the FPGA accelerator.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218660"}, {"primary_key": "2565976", "vector": [], "sparse_vector": [], "title": "Exploration of Design Space and Runtime Optimization for Affective Computing in Machine Learning Empowered Ultra-Low Power SoC.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The incorporation of artificial intelligence into the rapidly growing IoT devices demands a high level of built-in intelligence, e.g. machine learning capability at the device level. Affective computing offers a new degree of cognitive intelligence into edge processing IoT devices by inferring human emotion, stress levels for intelligent human assistance. This work explores the design space and runtime optimization opportunity for affective computing at the system-on-chip (SoC) level. A design optimization methodology for the neural network classifier and runtime power management schemes are proposed to achieve high energy efficiency on embedded low power devices. A test chip based on a 65nm CMOS process was used to demonstrate the proposed methodology on emotion and stress classification for affective computing. An average power saving of 45% is achieved with a peak power savings of 60% from the proposed emotion-driven adaptive power management scheme.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218583"}, {"primary_key": "2565977", "vector": [], "sparse_vector": [], "title": "Via-based Redistribution Layer Routing for InFO Packages with Irregular Pad Structures.", "authors": ["Hsiang<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The integrated fan-out (InFO) wafer-level chip-scale package is introduced for modern system-in-package designs with larger I/O counts and higher interconnection density. A redistribution layer (RDL) in an InFO package is an extra metal layer for inter-chip connections. To achieve flexible and compact inter-chip connections, the RDL routing problem for InFO packages has become a crucial problem for modern electronic designs. In advanced high-density InFO packages, multiple RDLs with flexible vias are often adopted. On the other hand, to integrate chips of different technology nodes into one package, irregular pad structures need to be considered. To our best knowledge, however, there is no published work for RDL routing considering flexible vias or irregular pad structures. In this paper, we present the first work to handle the routing problem with pre-assigned pad pairs (i.e., the hardest pre-assignment routing problem) on the via-based multi-chip multi-layer InFO package with irregular pad structures. We first propose a layer assignment method based on a weighted maximum planar subset of chords algorithm to concurrently route as many inter-chip nets as possible. We then propose an octagonal tile model with a layout partitioning method to tackle increasingly popular irregular structures. Finally, we develop an efficient linear-programming-based layout optimization algorithm to find solutions with high-quality wirelength and via arrangements. Experimental results demonstrate the effectiveness and robustness of our algorithm.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218619"}, {"primary_key": "2565978", "vector": [], "sparse_vector": [], "title": "Towards Purposeful Design Space Exploration of Heterogeneous CGRAs: Clock Frequency Estimation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Coarse Grained Reconfigurable Arrays become increasingly popular. Besides research on scheduling algorithms and microarchitecture concepts, the use of heterogeneous structures can be a key approach to exploit their full potential. Unfortunately, a purposeful design space exploration of CGRAs is not trivial, since one needs to know the clock frequency of the resulting hardware implementation. This paper discusses challenges and a statistical approach to maximum clock frequency estimation of heterogeneous CGRAs with an irregular interconnect on FPGAs. The presented approach allows estimation with a maximum error of 8.8 - 17.4% and a mean error of only 1.9 - 4.6%.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218649"}, {"primary_key": "2565979", "vector": [], "sparse_vector": [], "title": "Intermittent Inference with Nonuniformly Compressed Multi-Exit Neural Network for Energy Harvesting Powered Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jingtong Hu"], "summary": "This work aims to enable persistent, event-driven sensing and decision capabilities for energy-harvesting (EH)-powered devices by deploying lightweight DNNs onto EH-powered devices. However, harvested energy is usually weak and unpredictable and even lightweight DNNs take multiple power cycles to finish one inference. To eliminate the indefinite long wait to accumulate energy for one inference and to optimize the accuracy, we developed a power trace-aware and exit-guided network compression algorithm to compress and deploy multi-exit neural networks to EH-powered microcontrollers (MCUs) and select exits during execution according to available energy. The experimental results show superior accuracy and latency compared with state-of-the-art techniques.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218526"}, {"primary_key": "2565980", "vector": [], "sparse_vector": [], "title": "INVITED: Efficient Synthesis of Compact Deep Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep neural networks (DNNs) have been deployed in myriad machine learning applications. However, advances in their accuracy are often achieved with increasingly complex and deep network architectures. These large, deep models are often unsuitable for real-world applications, due to their massive computational cost, high memory bandwidth, and long latency. For example, autonomous driving requires fast inference based on Internet-of-Things (IoT) edge devices operating under run-time energy and memory storage constraints. In such cases, compact DNNs can facilitate deployment due to their reduced energy consumption, memory requirement, and inference latency. Long short-term memories (LSTMs) are a type of recurrent neural network that have also found widespread use in the context of sequential data modeling. They also face a model size vs. accuracy trade-off. In this paper, we review major approaches for automatically synthesizing compact, yet accurate, DNN/LSTM models suitable for real-world applications. We also outline some challenges and future areas of exploration.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218529"}, {"primary_key": "2565981", "vector": [], "sparse_vector": [], "title": "FCNNLib: An Efficient and Flexible Convolution Algorithm Library on FPGAs.", "authors": ["Qingcheng Xiao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Convolutions can be implemented with different algorithms, which are diverse in arithmetic complexity, resource requirement, etc. Multiple algorithms can share the FPGA resources spatially as well as temporally, introducing either reconfiguration overhead or resource underutilization. In this paper, we propose an efficient library FCNNLib to coordinate multiple convolution algorithms on FPGAs. We develop three scheduling techniques: spatial, temporal, and hybrid, which exhibit different trade-offs in latency and throughput. We also expose a set of interfaces to arm the users. Experiments using modern CNNs demonstrate FCNNLib achieves up to 1.315X latency improvement compared with dedicated accelerators and 1.755X energy efficiency improvement compared with cuDNN.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218748"}, {"primary_key": "2565982", "vector": [], "sparse_vector": [], "title": "Reducing DRAM Access Latency via Helper Rows.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The DRAM technology advancement has seen success in memory density and throughput improvement, but less in access latency reduction. This is mainly due to the intrinsic limitation of capacitance based bit store and access mechanism. The reduction of access latency has been well explored in literature. However, the recently proposed DRAM techniques, such as RowClone and Half-DRAM, offer new opportunities to further optimise the access latency.In this paper, we propose an efficient access strategy to improve the performance of DRAM by optionally discarding the restore. When activating a new row, our technique makes a copy of the row leveraging the RowClone method. Next time when accessing the same row, the cloned row is opened for sensing but it is not restored as the data is preserved in the original row. To improve the efficiency of our proposed strategy, we further exploit three schemes to minimize the copy overhead and increase the reuse of the cloned row. Experimental results show that our proposed strategy can achieve 11% performance improvement on average.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218719"}, {"primary_key": "2565983", "vector": [], "sparse_vector": [], "title": "STC: Significance-aware Transform-based Codec Framework for External Memory Access Reduction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep convolutional neural networks (DCNNs), with extensive computation, require considerable external memory bandwidth and storage for intermediate feature maps. External memory accesses for feature maps become a significant energy bottleneck for DCNN accelerators. Many works have been done on quantizing feature maps into low precision to decrease the costs for computation and storage. There is an opportunity that the large amount of correlation among channels in feature maps can be exploited to further reduce external memory access. Towards this end, we propose a novel compression framework called Significance-aware Transform-based Codec (STC). In its compression process, significance-aware transform is introduced to obtain low-correlated feature maps in an orthogonal space, as the intrinsic representations of original feature maps. The transformed feature maps are quantized and encoded to compress external data transmission. For the next layer computation, the data will be reloaded with STC's reconstruction process. The STC framework can be supported with a small set of extensions to current DCNN accelerators. We implement STC extensions to the baseline TPU architecture for hardware evaluation. The strengthened TPU achieves average reduction of 2.57x in external memory access, 1.95x~2.78x improvement of system-level energy efficiency, with a negligible accuracy loss of only 0.5%.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218522"}, {"primary_key": "2565984", "vector": [], "sparse_vector": [], "title": "Stealing Your Data from Compressed Machine Learning Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine learning models have been widely deployed in many real-world tasks. When a non-expert data holder wants to use a third-party machine learning service for model training, it is critical to preserve the confidentiality of the training data. In this paper, we for the first time explore the potential privacy leakage in a scenario that a malicious ML provider offers data holder customized training code including model compression which is essential in practical deployment The provider is unable to access the training process hosted by the secured third party, but could inquire models when they are released in public. As a result, adversary can extract sensitive training data with high quality even from these deeply compressed models that are tailored for resource-limited devices. Our investigation shows that existing compressions like quantization, can serve as a defense against such an attack, by degrading the model accuracy and memorized data quality simultaneously. To overcome this defense, we take an initial attempt to design a simple but stealthy quantized correlation encoding attack flow from an adversary perspective. Three integrated components-data pre-processing, layer-wise data-weight correlation regularization, data-aware quantization, are developed accordingly. Extensive experimental results show that our framework can preserve the evasiveness and effectiveness of stealing data from compressed models.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218633"}, {"primary_key": "2565985", "vector": [], "sparse_vector": [], "title": "Utilizing Direct Photocurrent Computation and 2D Kernel Scheduling to Improve In-Sensor-Processing Efficiency.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deploying intelligent visual algorithms in terminal devices for always-on sensing is an attractive trend in the IoT era. In-sensor-processing architecture is proposed to reduce power consumption on A/D conversion and data transmission, which performs pre-processing and only converting low-throughput features. However, current designs still require high energy consumption on photoelectric conversion and analog data movement. In this paper, two methods are proposed to improve the energy efficiency of in-sensor-processing architecture, including direct photocurrent computation and 2D kernel scheduling. Photocurrents are directly involved in computation to avoid data conversion; thus the indispensable imaging power is also utilized for computing. Since the location of the pixel data is fixed, data scheduling is conducted on digital weights to eliminate analog data storage and movement. We implement a prototype chip with an array of 32 × 32 units to calculate the first layer of binarized LeNet-5. The post-simulation shows that the proposed architecture reaches the energy efficiency of 11.49TOPs/W, about 14.8x higher than previous works.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218622"}, {"primary_key": "2565986", "vector": [], "sparse_vector": [], "title": "DDOT: Data Driven Online Tuning for energy efficient acceleration.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Eleftherios<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern accelerator platforms, are characterised by high micro-architectural complexity that affects both performance and energy consumption. Programmers usually are facing the problem of reasoning on differing trade-offs among the set of various code variants and their parameters configuration. While maximal configurations are usually adequate for performance optimization, this is not the case when optimizing for energy efficiency. Thus, efficient tuning methodologies accompanied with automated tools are of great importance for a quick and concrete evaluation of the explored design space. However, existing tuning frameworks are usually application-specific, i.e. performing well only on a priori known applications/workloads, and requiring heavy offline exploration and sampling procedures. In this paper, we present DDOT an online and scalable autotuning framework that enables the extraction of energy efficient tuning, with minimal online application characterisation. Instead of analyzing every application against every tuning configuration, it adopts a data driven approach, utilizing collaborative filtering, that quickly and with high accuracy configures the compilerand runtime-tuning parameters by identifying similarities to previously optimized applications. We evaluate DDOT efficiency utilizing as driving vehicle the Intel Phi accelerator platform, and compare it with state-of-art iterative and machine-learning tuning strategies as well with the exact optimal configurations of the derived solution space, through which we show that with minimal online characterisation, e.g. only either two or four online evaluations, DDOT finds tuning configurations that achieve more than 94% in respect to the optimal.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218734"}, {"primary_key": "2565987", "vector": [], "sparse_vector": [], "title": "HITTSFL: Design of a Cost-Effective HIS-Insensitive TNU-Tolerant and SET-Filterable Latch for Safety-Critical Applications.", "authors": ["<PERSON><PERSON>", "Xiangfeng Feng", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Wen"], "summary": "This paper proposes a cost-effective, high-impedance-state (HIS)-insensitive, triple-node-upset (TNU)-tolerant and single-event-transient (SET)-filterable latch, namely HITTSFL, to ensure high reliability with low-cost. The latch mainly comprises an output-level SET-filterable Schmitt-trigger and three inverters that make the values stored in three parallel single-node-upset (SNU)-recoverable dual-interlocked-storage-cells (DICEs) converge at a common node to tolerate any possible TNU. The latch does not use C-elements to be insensitive to the HIS. Simulation results demonstrate the TNU-tolerability and SET-filterability of the proposed HITTSFL latch. Moreover, due to the use of clock-gating technologies and fewer transistors, the proposed latch can reduce delay, power, and area by 76.65%, 6.16%, and 28.55%, respectively, compared with the state-of-the-art TNU hardened latch (TNUHL) that cannot filter SETs.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218704"}, {"primary_key": "2565988", "vector": [], "sparse_vector": [], "title": "TSN-Builder: Enabling Rapid Customization of Resource-Efficient Switches for Time-Sensitive Networking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Zhigang Sun"], "summary": "Time-Sensitive Networking (TSN) emerges as a promising technique empowering deterministic forwarding on standard Ethernet without sacrificing compatibility. There are some commercial off-the-shelf (COTS) switches that support TSN recently. However, the resource partitioning on these switches is normally inefficient for the on-chip memory resource in many specific application scenarios. We observe that the critical requirements (e.g., topology, flow features) of these scenarios are pre-determined. Thus, developing a TSN switch in a Top-down approach is feasible and urgently needed.In this paper, we propose TSN-Builder, a template-based developing model for customizing resource-efficient TSN switches rapidly with targeted application-dependent requirements. TSN-Builder decomposes the integrated TSN switching function into multiple function templates. With a fine-grained resource abstraction, TSN-Builder provides platform-independent customization interfaces for developers to customize the resource parameters. We prototype TSN switches on FPGA to evaluate the resource consumption and performance under different application scenarios. Experimental results show that TSN-Builder reduces the on-chip memory by up to 80.53% under the same Quality-of-Service, compared to the resource configuration in the COTS switch.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218753"}, {"primary_key": "2565989", "vector": [], "sparse_vector": [], "title": "Dadu-CD: Fast and Efficient Processing-in-Memory Accelerator for Collision Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Collision detection is a fundamental task in motion planning of robotics. Typically, the performance of collision detection is the bottleneck of an entire motion planning, and so does the energy consumption. Several hardware accelerators have been proposed for collision detection, which achieves higher performance and energy efficiency than general-purpose CPUs and GPUs. However, existing accelerators are still facing the limited memory bandwidth bottleneck, due to the large data volume required by the parallel processing cores and the limited DRAM bandwidth. In this work, we propose a novel collision detection accelerator by employing the processing-in-memory technique. We elaborate the in-memory processing architecture to fully utilize the internal bandwidth of DRAM banks. To make the algorithm and hardware suitable for in-memory processing to be highly efficient, a set of innovative software and hardware techniques are also proposed. Compared with a state-of-the-art ASIC-based collision detection accelerator, both performance and energy efficiency of our accelerator are significantly improved.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218709"}, {"primary_key": "2565990", "vector": [], "sparse_vector": [], "title": "DPCP-p: A Distributed Locking Protocol for Parallel Real-Time Tasks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Real-time scheduling and locking protocols are fundamental facilities to construct time-critical systems. For parallel real-time tasks, predictable locking protocols are required when concurrent sub-jobs mutually exclusive access to shared resources. This paper for the first time studies the distributed synchronization framework of parallel real-time tasks, where both tasks and global resources are partitioned to designated processors, and requests to each global resource are conducted on the processor on which the resource is partitioned. We extend the Distributed Priority Ceiling Protocol (DPCP) for parallel tasks under federated scheduling, with which we proved that a request can be blocked by at most one lower-priority request. We develop task and resource partitioning heuristics and propose analysis techniques to safely bound the task response times. Numerical evaluation (with heavy tasks on 8-, 16-, and 32-core processors) indicates that the proposed methods improve the schedulability significantly compared to the state-of-the-art locking protocols under federated scheduling.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218584"}, {"primary_key": "2565991", "vector": [], "sparse_vector": [], "title": "Non-uniform DNN Structured Subnets Sampling for Dynamic Inference.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the success of Deep Neural Networks (DNN), many recent works have been focusing on developing hardware accelerator for power and resource-limited system via model compression techniques, such as quantization, pruning, low-rank approximation and etc. However, almost all existing compressed DNNs are fixed after deployment, which lacks run-time adaptive structure to adapt to its dynamic hardware resource allocation, power budget, throughput requirement, as well as dynamic workload. As the countermeasure, to construct a novel run-time dynamic DNN structure, we propose a novel DNN sub-network sampling method via non-uniform channel selection for subnets generation. Thus, user can trade off between power, speed, computing load and accuracy on-the-fly after the deployment, depending on the dynamic requirements or specifications of the given system. We verify the proposed model on both CIFAR-10 and ImageNet dataset using ResNets, which outperforms the same sub-nets trained individually and other related works. It shows that, our method can achieve latency trade-off among 13.4, 24.6, 41.3, 62.1(ms) and 30.5, 38.7, 51, 65.4(ms) for GPU with 128 batch-size and CPU respectively on ImageNet using ResNet18.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218736"}, {"primary_key": "2565992", "vector": [], "sparse_vector": [], "title": "CoinPurse: A Device-Assisted File System with Dual Interfaces.", "authors": ["<PERSON><PERSON>", "Youyou Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Block I/O serves as a classic interface for accessing storage devices with portability. But it can also cause extra overhead by enforcing transferring data in the unit of blocks. In this paper, we present CoinPurse, a device-assisted file system with dual interfaces. By leveraging non-volatile memory (NVM) in SSD, CoinPurse manages to adaptively persist writes through both the block I/O and a byte-addressable partial update interface. In addition, we also develop a set of techniques to overcome hardware limitations and resolve possible consistency conflicts. Evaluation shows that CoinPurse outperforms F2FS, a popular flash-optimized file system, by up to 33.2%.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218679"}, {"primary_key": "2565993", "vector": [], "sparse_vector": [], "title": "Efficient Multi-Grained Wear Leveling for Inodes of Persistent Memory File Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qingfeng Zhuge", "<PERSON><PERSON><PERSON>"], "summary": "Existing persistent memory file systems usually store inodes in fixed locations, which ignores the external and internal imbalanced wears of inodes on the persistent memory (PM). Therefore, the PM for storing inodes can be easily damaged. Existing solutions achieve low accuracy of wear-leveling with high-overhead data migrations. In this paper, we propose a Lightweight and Multi-grained Wear-leveling Mechanism, called LMWM, to solve these problems. We implement the proposed LMWM in Linux kernel based on NOVA, a typical persistent memory file system. Compared with MARCH, the state-of-theart wear-leveling mechanism for inode table, experimental results show that LMWM can improve 2.5× lifetime of PM and 1.12× performance, respectively.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218626"}, {"primary_key": "2565994", "vector": [], "sparse_vector": [], "title": "UEFI Firmware Fuzzing with Simics Virtual Platform.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a fuzzing framework for Unified Extensible Firmware Interface (UEFI) BIOS with the Simics virtual platform. Firmware has increasingly become an attack target as operating systems are getting more and more secure. Due to its special execution environment and the extensive interaction with hardware, UEFI firmware is difficult to test compared to user-level applications running on operating systems. Fortunately, virtual platforms are widely used to enable early software and firmware development by modeling the target hardware platform in its virtual environment before silicon arrives. Virtual platforms play a critical role in left shifting UEFI firmware validation to pre-silicon phase. We integrated the fuzzing capability into Simics virtual platform to allow users to fuzz UEFI firmware code with high-fidelity hardware models provided by Simics. We demonstrated the ability to automatically detect previously unknown bugs, and issues found only by human experts.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218694"}, {"primary_key": "2565995", "vector": [], "sparse_vector": [], "title": "Co-Exploration of Neural Architectures and Heterogeneous ASIC Accelerator Designs Targeting Multiple Tasks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>zhen Lai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Neural Architecture Search (NAS) has demonstrated its power on various AI accelerating platforms such as Field Programmable Gate Arrays (FPGAs) and Graphic Processing Units (GPUs). However, it remains an open problem how to integrate NAS with Application-Specific Integrated Circuits (ASICs), despite them being the most powerful AI accelerating platforms. The major bottleneck comes from the large design freedom associated with ASIC designs. Moreover, with the consideration that multiple DNNs will run in parallel for different workloads with diverse layer operations and sizes, integrating heterogeneous ASIC sub-accelerators for distinct DNNs in one design can significantly boost performance, and at the same time further complicate the design space. To address these challenges, in this paper we build ASIC template set based on existing successful designs, described by their unique dataflows, so that the design space is significantly reduced. Based on the templates, we further propose a framework, namely ASICNAS, which can simultaneously identify multiple DNN architectures and the associated heterogeneous ASIC accelerator design, such that the design specifications (specs) can be satisfied, while the accuracy can be maximized. Experimental results show that compared with successive NAS and ASIC design optimizations which lead to design spec violations, ASICNAS can guarantee the results to meet the design specs with 17.77%, 2.49×, and 2.32× reductions on latency, energy, and area and less than 1.6% accuracy loss. To the best of the authors' knowledge, this is the first work on neural architecture and ASIC accelerator design co-exploration.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218676"}, {"primary_key": "2565996", "vector": [], "sparse_vector": [], "title": "Permutation-Write: Optimizing Write Performance and Energy for Skyrmion Racetrack Memory.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Skyrmion racetrack memory (Sky-RM) is an emerging non-volatile memory technology that offers not only high-density data storage but also the feature of shifting data along a racetrack by current. However, compared to other shifting based manipulations, writing/injecting new particle-like Skyrmions is much more time-consuming and energy-hungry. To optimize the write performance and energy for Sky-RM, this paper introduces a new write strategy, called Permutation-Write. Specifically, this strategy circumvents new Skyrmion injections during data writes by \"re-permuting\" existing Skyrmion particles in the racetrack. Evaluation results show that Permutation-Write strategy can significantly reduce the number of expensive Skyrmion injections by at least 50% compared to other state-of-the-art write reduction strategies, and merely introduce 2.25 injections on average for every 64-bit-word write under realistic workloads.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218642"}, {"primary_key": "2565997", "vector": [], "sparse_vector": [], "title": "HybridDNN: A Framework for High-Performance Hybrid DNN Accelerator Design and Implementation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To speedup Deep Neural Networks (DNN) accelerator design and enable effective implementation, we propose HybridDNN, a framework for building high-performance hybrid DNN accelerators and delivering FPGA-based hardware implementations. Novel techniques include a highly flexible and scalable architecture with a hybrid Spatial/Winograd convolution (CONV) Processing Engine (PE), a comprehensive design space exploration tool, and a complete design flow to fully support accelerator design and implementation. Experimental results show that the accelerators generated by HybridDNN can deliver 3375.7 and 83.3 GOPS on a high-end FPGA (VU9P) and an embedded FPGA (PYNQ-Z1), respectively, which achieve a 1.8x higher performance improvement compared to the state-of-art accelerator designs. This demonstrates that HybridDNN is flexible and scalable and can target both cloud and embedded hardware platforms with vastly different resource constraints.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218684"}, {"primary_key": "2565998", "vector": [], "sparse_vector": [], "title": "INCA: INterruptible CNN Accelerator for Multi-tasking in Embedded Robots.", "authors": ["Jincheng Yu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Chao<PERSON> Shen", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In recent years, Convolutional Neural Network (CNN) has been widely used in robotics, which has dramatically improved the perception and decision-making ability of robots. A series of CNN accelerators have been designed to implement energy-efficient CNN on embedded systems. However, despite the high energy efficiency on CNN accelerators, it is difficult for robotics developers to use it. Since the various functions on the robot are usually implemented independently by different developers, simultaneous access to the CNN accelerator by these multiple independent processes will result in hardware resources conflicts.To handle the above problem, we propose an INterruptible CNN Accelerator (INCA) to enable multi-tasking on CNN accelerators. In INCA, we propose a Virtual-Instruction-based interrupt method (VI method) to support multi-task on CNN accelerators. Based on INCA, we deploy the Distributed Simultaneously Localization and Mapping (DSLAM) on an embedded FPGA platform. We use CNN to implement two key components in DSLAM, Feature-point Extraction (FE) and Place Recognition (PR), so that they can both be accelerated on the same CNN accelerator. Experimental results show that, compared to the layer-by-layer interrupt method, our VI method reduces the interrupt respond latency to 1%.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218717"}, {"primary_key": "2565999", "vector": [], "sparse_vector": [], "title": "A Simple Cache Coherence Scheme for Integrated CPU-GPU Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a novel approach to accelerate applications running on integrated CPU-GPU systems. Many integrated CPU-GPU systems use cache-coherent shared memory to communicate. For example, after CPU produces data for GPU, the GPU may pull the data into its cache when it accesses the data. In such a pull-based approach, data resides in a shared cache until the GPU accesses it, resulting in long load latency on a first GPU access to a cache line. In this work, we propose a new, push-based, coherence mechanism that explicitly exploits the CPU and GPU producer-consumer relationship by automatically moving data from CPU to GPU last-level cache. The proposed mechanism results in a dramatic reduction of the GPU L2 cache miss rate in general, and a consequent increase in overall performance. Our experiments show that the proposed scheme can increase performance by up to 37%, with typical improvements in the 5–7% range. We find that even when tested applications do not benefit from the proposed approach, their performance does not decrease with our technique. While we demonstrate how the proposed scheme can co-exist with traditional cache coherence mechanisms, we argue that it could also be used as a simpler replacement for existing protocols.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218664"}, {"primary_key": "2566000", "vector": [], "sparse_vector": [], "title": "SIEVE: Speculative Inference on the Edge with Versatile Exportation.", "authors": ["Babak Zamirai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper proposes SIEVE, Speculative Inference on the Edge with Versatile Exportation, which dynamically distributes CNN computation between the cloud and edge device based on the input data and environmental conditions to maximize efficiency and performance. A speculative CNN is created through aggressive precision reduction techniques to run most of the inferences on the edge device, while the original CNN is run on the cloud server. A runtime system directs each input to either the edge or cloud and decides whether to accept speculative inferences made on the edge or invoke recovery by replaying the inference on the cloud. Compared to the cloud-only approach, SIEVE reduces energy consumption by an average of 91%, 57% and 26% and increases performance by an average of 12 . 3×, 2 . 8× and 2 . 0× for 3G, LTE and WiFi connections without accuracy loss across a range of nine CNNs.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218698"}, {"primary_key": "2566001", "vector": [], "sparse_vector": [], "title": "On the Security of Strong Memristor-based Physically Unclonable Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "PUFs are cost-effective security primitives that extract unique identifiers from integrated circuits. However, since their introduction, PUFs have been subject to modeling attacks based on machine learning. Recently, researchers explored emerging nano-electronic technologies, e.g., memristors, to construct hybrid-PUFs, which outperform CMOS-only PUFs and are claimed to be more resilient to modeling attacks. However, since such PUF designs are not open-source, the security claims remain dubious. In this paper, we reproduce a set of memristor-PUFs and extensively evaluate their unpredictability property. By leveraging state-of-the-art machine learning algorithms, we show that it is feasible to successfully model memristor-PUFs with high prediction rates of 98%. Even incorporating XOR gates, to further strengthen PUFs' against modeling attacks, has a negligible effect.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218491"}, {"primary_key": "2566002", "vector": [], "sparse_vector": [], "title": "An Efficient Asynchronous Batch Bayesian Optimization Approach for Analog Circuit Synthesis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose EasyBO, an Efficient ASYnchronous Batch Bayesian Optimization approach for analog circuit synthesis. In this proposed approach, instead of waiting for the slowest simulations in the batch to finish, we accelerate the optimization procedure by asynchronously issuing the next query points whenever there is an idle worker. We introduce a new acquisition function that can better explore the design space for asynchronous batch Bayesian optimization. A new strategy is proposed to better balance the exploration and exploitation and guarantee the diversity of the query points. And a penalization scheme is proposed to further avoid redundant queries during the asynchronous batch optimization. The efficiency of optimization can thus be further improved. Compared with the state-of-the-art batch Bayesian optimization algorithm, EasyBO achieves up to 7.35 times speed-up without sacrificing the optimization results.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218592"}, {"primary_key": "2566003", "vector": [], "sparse_vector": [], "title": "SFO: A Scalable Approach to Fanout-Bounded Logic Synthesis for Emerging Technologies.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Fanouts are an essential element for signal cloning to achieve logic sharing, but can be a very limited resource in certain emerging technologies, such as quantum circuits, superconducting electronic circuits, photonic integrated circuits, and biological circuits. Although fanout synthesis has been intensively studied for high performance circuit synthesis, prior methods often treat fanout as a soft constraint for critical path optimization or target on specific high-fanout nets such as clock and reset signals. They are not particularly suited for circuit synthesis of these emerging technologies. By treating fanouts as first class citizens, the problem of fanout-bounded logic synthesis was posed as a challenge in the 2019 IWLS Programming Contest. In this paper, we present our winning method, which achieved the overall best quality in the competition, based on fanout load redistribution among existing or expanded equivalent signals.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218500"}, {"primary_key": "2566004", "vector": [], "sparse_vector": [], "title": "PattPIM: A Practical ReRAM-Based DNN Accelerator by Reusing Weight Pattern Repetitions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yungang Pan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Weight sparsity has been explored to achieve energy efficiency for Resistive Random-access Memory (ReRAM) based DNN accelerators. However, most existing ReRAM-based DNN accelerators are based on an overidealized crossbar architecture and mainly focus on compressing zero weights. In this paper, we propose a novel ReRAM-based accelerator — PattPIM, to achieve space compression and computation reuse by studying DNN weight patterns based on practical ReRAM crossbars. We first thoroughly analyze the weight distribution characteristics of several typical DNN models and observe many non-zero weight pattern repetitions (WPRs). Thus, in PattPIM, we propose a WPR-aware DNN engine and a WPR-to-OU mapping scheme to save both space and computation resources. Furthermore, we adopt an approximate weight pattern transform algorithm to improve the DNN WPRs ratio to enhance the reuse efficiency with negligible inference accuracy loss. Our evaluation with 6 DNN models shows that the proposed PattPIM delivers significant performance improvement, ReRAM resources efficiency and energy saving.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218638"}, {"primary_key": "2566005", "vector": [], "sparse_vector": [], "title": "LOFFS: A Low-Overhead File System for Large Flash Memory on Embedded Devices.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> She", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Emerging applications like machine learning in embedded devices (e.g., satellite and vehicles) require huge storage space, which recently stimulates the widespread deployment of large-capacity flash memory in IoT devices. However, existing embedded file systems fall short in managing large-capacity storage efficiently for excessive memory consumption and poor booting performance. In this paper, we propose a novel embedded file system, LOFFS, to tackle the above issues and manage large-capacity NAND flash on resource-limited embedded devices. We redesign the space management mechanisms and construct hybrid file structures to achieve high performance with minimum resource occupation. We have implemented LOFFS in Linux, and the experimental results show that LOFFS outperforms YAFFS by 55.8% on average with orders of magnitude reductions on memory footprint.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218635"}, {"primary_key": "2566006", "vector": [], "sparse_vector": [], "title": "GRANNITE: Graph Neural Network Inference for Transferable Power Estimation.", "authors": ["Yan<PERSON> Zhang", "Haoxing Ren", "<PERSON><PERSON>"], "summary": "This paper introduces GRANNITE, a GPU-accelerated novel graph neural network (GNN) model for fast, accurate, and transferable vector-based average power estimation. During training, GRANNITE learns how to propagate average toggle rates through combinational logic: a netlist is represented as a graph, register states and unit inputs from RTL simulation are used as features, and combinational gate toggle rates are used as labels. A trained GNN model can then infer average toggle rates on a new workload of interest or new netlists from RTL simulation results in a few seconds. Compared to traditional power analysis using gate-level simulations, GRANNITE achieves >18.7X speedup with an error of only <; 5.5% across a diverse set of benchmark circuits. Compared to a GPU-accelerated conventional probabilistic switching activity estimation approach, GRANNITE achieves much better accuracy (on average 25.9% lower error) at similar runtimes.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218643"}, {"primary_key": "2566007", "vector": [], "sparse_vector": [], "title": "From Homogeneous to Heterogeneous: Leveraging Deep Learning based Power Analysis across Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we raise practical situations in profiling based power analysis when profiling and target devices are quite different at several levels. \"Crossed devices\" are newly termed, including homogeneous and heterogeneous devices, which have not been carefully investigated. We identify such device variations and take a further step towards leveraging the deep learning based power analysis. Traditional template attacks and straight-forward deep learning based power analysis will fail, when the gap across devices is significantly enlarged. In this paper, we propose a noval frequency and learning based power analysis machanism, which is able to explore new attacking power of deep learning and address challenges caused by device variations. For the first time, power traces collected from our own PIC devices can be utilized to successfully attack the public dataset in DPAContest v4 which is based on a totally different AVR microcontroller.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218693"}, {"primary_key": "2566008", "vector": [], "sparse_vector": [], "title": "Tier-Scrubbing: An Adaptive and Tiered Disk Scrubbing Scheme with Improved MTTD and Reduced Cost.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Yongguang Ji"], "summary": "Sector errors are a common type of error in modern disks. A sector error that occurs during I/O operations might cause inaccessibility of an application. Even worse, it could result in permanent data loss if the data is being reconstructed, and thereby severely affects the reliability of a storage system. Many disk scrubbing schemes have been proposed to solve this problem. However, existing approaches have several limitations. First, schemes use machine learning (ML) to predict latent sector errors (LSEs), but only leverage a single snapshot of training data to make a prediction, and thereby ignore sequential dependencies between different statuses of a hard disk over time. Second, they accelerate the scrubbing at a fixed rate based on the results of a binary classification model, which may result in unnecessary increases in scrubbing cost. Third, they naively accelerate the scrubbing of the full disk which has LSEs based on the predictive results, but neglect partial high-risk areas (the areas that have a higher probability of encountering LSEs). Lastly, they do not employ strategies to scrub these high-risk areas in advance based on I/O accesses patterns, in order to further increase the efficiency of scrubbing.We address these challenges by designing a Tier-Scrubbing (TS) scheme that combines a Long Short-Term Memory (LSTM) based Adaptive Scrubbing Rate Controller (ASRC), a module focusing on sector error locality to locate high-risk areas in a disk, and a piggyback scrubbing strategy to improve the reliability of a storage system. Our evaluation results on realistic datasets and workloads from two real world data centers demonstrate that TS can simultaneously decrease the Mean-Time-To-Detection (MTTD) by about 80% and the scrubbing cost by 20%, compared to a state-of-the-art scrubbing scheme.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218551"}, {"primary_key": "2566009", "vector": [], "sparse_vector": [], "title": "BitPruner: Network Pruning for Bit-serial Accelerators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Bit-serial architectures (BSAs) are becoming increasingly popular in low power neural network processor (NNP) design. However, the performance and efficiency of state-of-the-art BSA NNPs are heavily depending on the distribution of ineffectual weight-bits of the running neural network. To boost the efficiency of third-party BSA accelerators, this work presents Bit-Pruner, a software approach to learn BSA-favored neural networks without resorting to hardware modifications. The techniques proposed in this work not only progressively prune but also structure the non-zero bits in weights, so that the number of zero-bits in the model can be increased and also load-balanced to suit the architecture of the target BSA accelerators. According to our experiments on a set of representative neural networks, Bit-Pruner increases the bit-sparsity up to 94.4% with negligible accuracy degradation. When the bit-pruned models are deployed onto typical BSA accelerators, the average performance is 2.1X and 1.5X higher than the baselines running non-pruned and weight-pruned networks, respectively.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218534"}, {"primary_key": "2566010", "vector": [], "sparse_vector": [], "title": "SCA: A Secure CNN Accelerator for Both Training and Inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Convolutional neural networks (CNNs), while being widely deployed to edge devices, face increasingly requirements for IP protection, i.e., the protection of the models and their weights. This becomes particularly challenging for those that demand post-deployment training to enhance inference performance. Existing schemes focus mainly on IP protection at the inference phase, and lack the ability to extend to the training phase. In this paper, we propose SCA, a secure CNN accelerator that exploits stochastic computing to achieve IP protection at both training and inference phases. We propose hybrid stochastic addition and weight remapping to further optimize space utilization and design robustness. Our experimental results show that SCA effectively prevents pirating the CNN IP from the authorized devices. In addition, it achieves 4.8× and 34.2× speedups and 84.3% and 98.5% energy reductions over a non-secure baseline and an inference-only secure baseline, respectively.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218752"}, {"primary_key": "2566011", "vector": [], "sparse_vector": [], "title": "Lattice: An ADC/DAC-less ReRAM-based Processing-In-Memory Architecture for Accelerating Deep Convolution Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yima<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Nonvolatile Processing-In-Memory (NVPIM) has demonstrated its great potential in accelerating Deep Convolution Neural Networks (DCNN). However, most of existing NVPIM designs require costly analog-digital conversions and often rely on excessive data copies or writes to achieve performance speedup. In this paper, we propose a new NVPIM architecture, namely, <PERSON><PERSON><PERSON>, which calculates the partial sum of the dot products between the feature map and weights of network layers in a CMOS peripheral circuit to eliminate the analog-digital conversions. <PERSON><PERSON><PERSON> also naturally offers an efficient data mapping scheme to align the data of the feature maps and the weights and hence, avoiding the excessive data copies or writes in the previous NVPIM designs. Finally, we develop a zero-flag encoding scheme to save the energy of processing zero-values in sparse DCNNs. Our experimental results show that <PERSON><PERSON><PERSON> improves the system energy efficiency by 4× ~ 13.22× compared to three state-of-the-art NVPIM designs: ISAAC, PipeLayer, and FloatPIM.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218590"}, {"primary_key": "2566012", "vector": [], "sparse_vector": [], "title": "Deep Learning-Driven Simultaneous Layout Decomposition and Mask Optimization.", "authors": ["<PERSON>", "Shuxiang Hu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xi<PERSON>uan Ma", "<PERSON><PERSON>"], "summary": "Combining multiple pattern lithography (MPL) and optical proximity correlation (OPC) pushes the limit of 193nm wavelength lithography to go further. Considering that layout decomposition may generate plenty of solutions with diverse printabilities, relying on conventional mask optimization process to select the best candidates for manufacturing is computationally expensive. Therefore, an accurate and efficient printability estimation is crucial and can significantly accelerate the layout decomposition and mask optimization (LDMO) process. In this paper, we propose a CNN based prediction and integrate it into our new high performance LDMO framework. We also develop both the layout and the decomposition sampling strategies to facilitate the network training. The experimental results demonstrate the effectiveness and the efficiency of the proposed algorithms.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218530"}, {"primary_key": "2566013", "vector": [], "sparse_vector": [], "title": "Hardware-assisted Service Live Migration in Resource-limited Edge Computing Systems.", "authors": ["<PERSON><PERSON>", "Xintong Li", "<PERSON><PERSON>", "<PERSON>", "Guangyu Sun", "<PERSON><PERSON><PERSON>"], "summary": "Service live migration means migrating the running services from one machine to another with negligible service downtime. It has been considered as a powerful mechanism to facilitate service management. However, conventional live migration methods always come with expensive cost of data transmission, and thus can hardly be applied to a real-world edge computing system directly due to the limited network bandwidth. To tackle this problem, some recent works present various techniques to reduce the data transmission.However, these techniques for data transmission reduction always introduce extra computational costs, which have a great impact on the quality of service (QoS), especially in edge systems containing lots of nodes with insufficient computational resources. To alleviate this issue, we propose an insight to offload data reduction computations to a specific hardware accelerator, thus reducing the burden of CPU cores. To this end, we present a novel hardware accelerator design to speed up the data transmission reduction computations to accelerate the service live migration. For evaluation, we implement a prototype on an FPGA platform. Compared to the normal CPU-based approaches, our specialized accelerator is 3.1× faster, 2.9× more-energy efficient, and can reduce 29%∼47% of total migrating time and 24%∼40% of service downtime in our cases. Furthermore, our architecture has great scalability and is easy-configurable to achieve a balance between cost and performance.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218677"}, {"primary_key": "2566014", "vector": [], "sparse_vector": [], "title": "Taming Unstructured Sparsity on GPUs via Latency-Aware Optimization.", "authors": ["<PERSON><PERSON> Zhu", "<PERSON>"], "summary": "Neural Networks (NNs) exhibit high redundancy in their parameters so that pruning methods can achieve high compression ratio without accuracy loss. However, the very high sparsity produced by unstructured pruning methods is difficult to be efficiently mapped onto Graphics Processing Units (GPUs) because of its decoding overhead and workload imbalance. With the introduction of Tensor Core, the latest GPUs achieve even higher throughput for the dense neural networks. This makes unstructured neural networks fail to outperform their dense counterparts because they are not currently supported by Tensor Core. To tackle this problem, prior work suggests structured pruning to improve the performance of sparse NNs on GPUs. However, such structured pruning methods have to sacrifice a significant part of sparsity to retain the model accuracy, which limits the speedup on the hardware. In this paper, we observe that the Tensor Core is also able to compute unstructured sparse NNs efficiently. To achieve this goal, we first propose ExTensor, a set of sparse Tensor Core instructions with a variable input matrix tile size. The variable tile size allows a matrix multiplication to be implemented by mixing different types of ExTensor instructions. We build a performance model to estimate the latency of an ExTensor instruction given an operand sparse weight matrix. Based on this model, we propose a heuristic algorithm to find the optimal sequence of the instructions for an ExTensor based kernel to achieve the best performance on the GPU. Experimental results demonstrate that our approach achieves 36% better performance than the state-of-the-art sparse Tensor Core design.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218644"}, {"primary_key": "2566015", "vector": [], "sparse_vector": [], "title": "Time-Division Multiplexing Based System-Level FPGA Routing for Logic Verification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multi-FPGA prototyping is widely used for modern VLSI verification, but the limited number of inter-FPGA connections in a multi-FPGA system may cause routing failures. As a result, the time-division multiplexing (TDM) technique is adopted to increase its resource utilization by transmitting multiple signals through the same routing channel. Due to the large signal delay between FPGA pairs, however, the performance of such a system greatly depends on the inter-FPGA routing quality. In this paper, we propose a TDM-based system-level routing algorithm to simultaneously minimize the maximum TDM (signal multiplexing) ratio and runtime, considering the crucial ratio constraints. By weighting the routing edges, we first model the net routing as a Steiner minimum tree (SMT) problem and solve it with an approximation algorithm with the performance bound 2(1 - 1/1), where l is the number of leaves in an optimal SMT. Then, a timing-driven assignment method is presented to evenly distribute the TDM ratio to routing signals, followed by a novel reassignment algorithm to efficiently handle unbalanced net groups. Finally, a ratio-aware refinement technique is employed to further improve the solution quality. Compared with the top-3 winners at the 2019 CAD Contest at ICCAD based on the contest benchmarks, experiment results show that our proposed algorithm achieves the best runtime and TDM ratio while satisfying all TDM constraints.", "published": "2020-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18072.2020.9218569"}]