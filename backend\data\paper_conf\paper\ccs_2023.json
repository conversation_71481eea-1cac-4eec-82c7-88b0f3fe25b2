[{"primary_key": "1101535", "vector": [], "sparse_vector": [], "title": "HE3DB: An Efficient and Elastic Encrypted Database Via Arithmetic-And-Logic Fully Homomorphic Encryption.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zhenyu Guan"], "summary": "As concerns are increasingly raised about data privacy, encrypted database management system (DBMS) based on fully homomorphic encryption (FHE) attracts increasing research attention, as FHE permits DBMS to be directly outsourced to cloud servers without revealing any plaintext data. However, the real-world deployment of FHE-based DBMS faces two main challenges: i) high computational latency, and ii) lack of elastic query processing capability, both of which stem from the inherent limitations of the underlying FHE operators. Here, we introduce HE3DB, a fully homomorphically encrypted, efficient and elastic DBMS framework based on a new FHE infrastructure. By proposing and integrating new arithmetic and logic homomorphic operators, we devise fast and high-precision homomorphic comparison and aggregation algorithms that enable a variety of SQL queries to be applied over FHE ciphertexts, e.g., compound filter-aggregation, sorting, grouping, and joining. In addition, in contrast to existing encrypted DBMS that only support aggregated information retrieval, our framework permits further server-side elastic analytical processing over the queried FHE ciphertexts, such as private decision tree evaluation. In the experiment, we rigorously study the efficiency and flexibility of HE3DB. We show that, compared to the state-of-the-art techniques, HE3DB can homomorphically evaluate end-to-end SQL queries as much as 41X-299X faster than the state-of-the-art solution, completing a TPC-H query over a 16-bit 10K-row database within 241 seconds.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616608"}, {"primary_key": "1101536", "vector": [], "sparse_vector": [], "title": "CCSW &apos;23: Cloud Computing Security Workshop.", "authors": ["<PERSON>", "A<PERSON><PERSON><PERSON>"], "summary": "Clouds and massive-scale computing infrastructures are starting to dominate computing and will likely continue to do so for the foreseeable future. Major cloud operators are now comprising millions of cores hosting substantial fractions of corporate and government IT infrastructure. CCSW is the world's premier forum bringing together researchers and practitioners in all security aspects of cloud-centric and outsourced computing, including:", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624024"}, {"primary_key": "1101537", "vector": [], "sparse_vector": [], "title": "The Effectiveness of Security Interventions on GitHub.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In 2017, GitHub was the first online open source platform to show security alerts to its users. It has since introduced further security interventions to help developers improve the security of their open source software. In this study, we investigate and compare the effects of these interventions. This offers a valuable empirical perspective on security interventions in the context of software development, enriching the predominantly qualitative and survey-based literature landscape with substantial data-driven insights. We conduct a time series analysis on security-altering commits covering the entire history of a large-scale sample of over 50,000 GitHub repositories to infer the causal effects of the security alert, security update, and code scanning interventions. Our analysis shows that while all of GitHub's security interventions have a significant positive effect on security, they differ greatly in their effect size. By comparing the design of each intervention, we identify the building blocks that worked well and those that did not. We also provide recommendations on how practitioners can improve the design of their interventions to enhance their effectiveness.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623174"}, {"primary_key": "1101538", "vector": [], "sparse_vector": [], "title": "Efficient Set Membership Encryption and Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The emerging area of laconic cryptography [<PERSON> et al., CRYPTO'17] involves the design of two-party protocols involving a sender and a receiver, where the receiver's input is large. The key efficiency requirement is that the protocol communication complexity must be independent of the receiver's input size. In recent years, many tasks have been studied under this umbrella, including laconic oblivious transfer (ℓOT).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623131"}, {"primary_key": "1101539", "vector": [], "sparse_vector": [], "title": "Asymptotically Faster Multi-Key Homomorphic Encryption from Homomorphic Gadget Decomposition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Homomorphic Encryption (HE) is a cryptosytem that allows us to perform an arbitrary computation on encrypted data. The standard HE, however, has a disadvantage in that the authority is concentrated in the secret key owner since computations can only be performed on ciphertexts encrypted under the same secret key. To resolve this issue, research is underway on Multi-Key Homomorphic Encryption (MKHE), which is a variant of HE supporting computations on ciphertexts possibly encrypted under different keys. Despite its ability to provide privacy for multiple parties, existing MKHE schemes suffer from poor performance due to the cost of multiplication which grows at least quadratically with the number of keys involved.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623176"}, {"primary_key": "1101540", "vector": [], "sparse_vector": [], "title": "Realizing Flexible Broadcast Encryption: How to Broadcast to a Public-Key Directory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Suppose a user wants to broadcast an encrypted message to K recipients. With public-key encryption, the sender would construct K different ciphertexts, one for each recipient. The size of the broadcasted message then scales linearly with K. A natural question is whether the sender can encrypt the message with a ciphertext whose size scales \\em sublinearly with the number of recipients.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623168"}, {"primary_key": "1101542", "vector": [], "sparse_vector": [], "title": "Poster: Metadata-private Messaging without Coordination.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Metadata-private messaging (MPM) refers to an end-to-end encrypted messaging system that protects not just the payload messages but also the privacy-revealing communication metadata, such as user identities, conversation frequencies, traffic volumes, etc. Protecting the communication metadata is challenging due to the existence of global adversaries that can monitor and even actively interfere with the traffic. Established systems like Tor are not adequate under such adversarial models. Thus, many academic systems have been proposed to push this frontier with different trade-offs among security, performance, and trust assumptions. Despite progress, one major limitation prevalent in almost all prior art is the requirement for messaging buddies to coordinate the time (also known as \"dialing'') to start the conversation. Compared to traditional messaging systems, such coordination protocols, which must also be metadata private, are expensive for both user adoption and service operations. In this ongoing study, we propose to develop a new MPM system without coordination. Unlike prior art, we plan to model the MPM system into two separate modules: metadata-private notifications and metadata-private message retrieval, which is intuitively inspired by traditional messaging systems. We will instantiate these ideas by drawing insights from recent work about private signaling, oblivious message retrieval, and MPM under hardware trust.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624385"}, {"primary_key": "1101543", "vector": [], "sparse_vector": [], "title": "WAHC &apos;23: 11th Workshop on Encrypted Computing &amp; Applied Homomorphic Cryptography.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The 11th Workshop on Encrypted Computing and Applied Homomorphic Cryptography is held in Copenhagen, Denmark on Novem- ber 26, 2023, co-located with the ACM Conference on Computer and Communications Security (CCS). The workshop aims to bring together professionals, researchers and practitioners from academia, industry and government in the area of computer security and applied cryptography with an interest in practical applications of homomorphic encryption, encrypted computing, functional encryption and secure function evaluation, private information retrieval and searchable encryption. The workshop will feature 9 exciting accepted talks on different aspects of secure computation and a forum to discuss current and future challenges. Additionally, the workshop will feature one keynote presentation, as well as one invited talk.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624021"}, {"primary_key": "1101544", "vector": [], "sparse_vector": [], "title": "Blink: Link Local Differential Privacy in Graph Neural Networks via Bayesian Estimation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph neural networks (GNNs) have gained an increasing amount of popularity due to their superior capability in learning node embeddings for various graph inference tasks, but training them can raise privacy concerns. To address this, we propose using link local differential privacy over decentralized nodes, enabling collaboration with an untrusted server to train GNNs without revealing the existence of any link. Our approach spends the privacy budget separately on links and degrees of the graph for the server to better denoise the graph topology using Bayesian estimation, alleviating the negative impact of LDP on the accuracy of the trained GNNs. We bound the mean absolute error of the inferred link probabilities against the ground truth graph topology. We then propose two variants of our LDP mechanism complementing each other in different privacy settings, one of which estimates fewer links under lower privacy budgets to avoid false positive link estimates when the uncertainty is high, while the other utilizes more information and performs better given relatively higher privacy budgets. Furthermore, we propose a hybrid variant that combines both strategies and is able to perform better across different privacy budgets. Extensive experiments show that our approach outperforms existing methods in terms of accuracy under varying privacy budgets.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623165"}, {"primary_key": "1101545", "vector": [], "sparse_vector": [], "title": "Privacy Leakage via Speech-induced Vibrations on Room Objects through Remote Sensing based on Phased-MIMO.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Gao", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Speech eavesdropping has long been an important threat to the privacy of individuals and enterprises. Recent research has shown the possibility of deriving private speech information from sound-induced vibrations. Acoustic signals transmitted through a solid medium or air may induce vibrations upon solid surfaces, which can be picked up by various sensors (e.g., motion sensors, high-speed cameras and lasers), without using a microphone. To date, these threats are limited to scenarios where the sensor is in contact with the vibration surface or at least in the visual line-of-sight.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616634"}, {"primary_key": "1101546", "vector": [], "sparse_vector": [], "title": "iLeakage: Browser-based Timerless Speculative Execution Attacks on Apple Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Over the past few years, the high-end CPU market is undergoing a transformational change. Moving away from using x86 as the sole architecture for high performance devices, we have witnessed the introduction of heavy-weight Arm CPUs computing devices. Among these, perhaps the most influential was the introduction of Apple's M-series architecture, aimed at completely replacing Intel CPUs in the Apple ecosystem. However, while significant effort has been invested analyzing x86 CPUs, the Apple ecosystem remains largely unexplored.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616611"}, {"primary_key": "1101547", "vector": [], "sparse_vector": [], "title": "Poster: Detecting Adversarial Examples Hidden under Watermark Perturbation via Usable Information Theory.", "authors": ["Zim<PERSON>", "<PERSON><PERSON><PERSON>", "Tingting Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Image watermark is a technique widely used for copyright protection. Recent studies show that the image watermark can be added to the clear image as a kind of noise to realize fooling deep learning models. However, previous adversarial example (AE) detection schemes tend to be ineffective since the watermark logo differs from typical noise perturbations. In this poster, we propose Themis, a novel AE detection method against watermark perturbation. Different from prior methods, Themis neither modifies the protected classifier nor requires knowledge of the process for generating AEs. Specifically, Themis leverages usable information theory to calculate the pointwise score, thereby discovering those instances that may be watermark AEs. The empirical evaluations involving 5 different logo watermark perturbations demonstrate the proposed scheme can efficiently detect AEs, and significantly (over 15% accuracy) outperforms five state-of-the-art (SOTA) detection methods. The visualization results display our detection metric is more distinguishable between AEs and non-AEs. Meanwhile, Themis realizes a larger Area Under Curve (AUC) in a threshold-resilient manner, while only introducing ∼0.04s overhead.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624396"}, {"primary_key": "1101549", "vector": [], "sparse_vector": [], "title": "Poster: The Unknown Unknown: Cybersecurity Threats of Shadow IT in Higher Education.", "authors": ["<PERSON><PERSON><PERSON>", "Joost F<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The growing number of employee-introduced IT solutions creates new attack vectors and challenges for cybersecurity management and IT administrators. These unauthorised hardware, software, or services are called shadow IT. In higher education, the diversity of the shadow IT landscape is even more prominent due to the flexible needs of researchers, educators, and students.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624395"}, {"primary_key": "1101550", "vector": [], "sparse_vector": [], "title": "Poster: Efficient AES-GCM Decryption Under Homomorphic Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Computation delegation to untrusted third-party while maintaining data confidentiality is possible with homomorphic encryption (HE). However, in many cases, the data was encrypted using another cryptographic scheme such as AES-GCM. Hybrid encryption (a.k.a Transciphering) is a technique that allows moving between cryptosystems, which currently has two main drawbacks: 1) lack of standardization or bad performance of symmetric decryption under FHE; 2) lack of input data integrity.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624377"}, {"primary_key": "1101551", "vector": [], "sparse_vector": [], "title": "Tutorial-HEPack4ML &apos;23: Advanced HE Packing Methods with Applications to ML.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Outsourcing computations over sensitive data to a third-party cloud environment should often rely on dedicated privacy-preserving solutions in order to adhere to privacy regulations such as the GDPR [7]. One solution that gained great attention is fully homomorphic encryption (FHE), a cryptographic method that allows performing different types of computation on encrypted data. Still, writing a non-interactive FHE code that evaluates complex functions is a task that is mostly left to experts. Otherwise, the resulted code may become very slow and even impractical.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624030"}, {"primary_key": "1101552", "vector": [], "sparse_vector": [], "title": "Poster: Data Minimization by Construction for Trigger-Action Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Trigger-Action Platforms (TAPs) enable applications to integrate various devices and services otherwise unconnected. Recent features of TAPs introduce additional sources of data such as queries in IFTTT. The current TAPs, like IFTTT, demand that trigger and query services transmit excessive amounts of user data to the TAP. To limit the data to what is actually necessary for the execution to comply with the principle of data minimization, input services should send no more than the necessary data. LazyTAP proposes a new paradigm of data minimization by construction in TAPs, introducing a novel perspective for data collection from input services. While the existing push-all approach of TAPs entails coarse-grained data over-approximation, LazyTAP pulls input data on-demand at the level of attributes, once accessed by the app execution. Thanks to the fine granularity provided by LazyTAP, multiple trigger and query services can be naturally minimized while the behavior of app executions is preserved. In addition, a great benefit of LazyTAP is being seamless for third-party app developers. By leveraging laziness, LazyTAP defers computation and proxies objects to load necessary remote data behind the scenes. Our evaluation study on app benchmarks shows that on average LazyTAP improves minimization by 95% over IFTTT and by 38% over minTAP, with a tolerable performance overhead. This poster goes into further details about LazyTAP and elaborates on its prototype implementation.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624376"}, {"primary_key": "1101553", "vector": [], "sparse_vector": [], "title": "Formal Analysis of Access Control Mechanism of 5G Core Network.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Dong", "<PERSON>"], "summary": "We present 5GCVerif, a model-based testing framework designed to formally analyze the access control framework of the 5G Core. With its modular design, 5GCVerif employs various abstraction techniques to craft an abstract model that captures the intricate details of the 5G Core's access control mechanism. This approach offers customizability and extensibility in constructing the abstract model and addresses the state explosion problem in model checking. 5GCVerif also sidesteps the challenge of exhaustively generating models for all possible core network configurations by restricting the model checker to explore policy violations only within the valid network configurations. Using 5GCVerif, we evaluated 55 security properties, leading to the discovery of five new vulnerabilities in 5G Core's access control mechanism. The uncovered vulnerabilities can result in multiple attacks including unauthorized entry to sensitive information, illegitimate access to services, and denial-of-services.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623113"}, {"primary_key": "1101554", "vector": [], "sparse_vector": [], "title": "CPSIoTSec&apos;23: Fifth Workshop on CPS &amp; IoT Security and Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The fifth Workshop on CPS & IoT Security and Privacy is set to take place in Copenhagen, Denmark, on November 26, 2023, in conjunction with the ACM Conference on Computer and Communications Security (CCS'23). This workshop marks the amalgamation of two workshops held in 2019: one focused on the security and privacy of cyber-physical systems, while the other one centered on the security and privacy of IoT. The primary objective of this workshop is to create a collaborative forum that brings together academia, industry experts, and governmental entities, encouraging them to contribute cutting-edge research, share demonstrations or hands-on experiences, and engage in discussions.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624020"}, {"primary_key": "1101555", "vector": [], "sparse_vector": [], "title": "Measuring Website Password Creation Policies At Scale.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Researchers have extensively explored how password creation policies influence the security and usability of user-chosen passwords, producing evidence-based policy guidelines. However, for web authentication to improve in practice, websites must actually implement these recommendations. To date, there has been limited investigation into what password creation policies are actually deployed by sites. Existing works are mostly dated and all studies relied on manual evaluations, assessing a small set of sites (at most 150, skewed towards top sites). Thus, we lack a broad understanding of the password policies used today. In this paper, we develop an automated technique for inferring a website's password creation policy, and apply it at scale to measure the policies of over 20K sites, over two orders of magnitude (~135x) more sites than prior work. Our findings identify the common policies deployed, potential causes of weak policies, and directions for improving authentication in practice. Ultimately, our study provides the first large-scale understanding of password creation policies on the web.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623156"}, {"primary_key": "1101556", "vector": [], "sparse_vector": [], "title": "Poster: <PERSON><PERSON><PERSON>: A Summarization-based Approach for Normalized Vulnerability Description.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This work proposes a multi-task Natural Language Processing (NLP) system to normalize and summarize the descriptions into a uniform structure. A dataset was curated from an official public database and broken into several constituent entities representing a particular aspect of the description. A model is trained on the annotated features independently and jointly to generate a simple and uniform summary. We also introduce our human metrics to judge the quality of the generated summary with respect to human comprehension and content accuracy.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624386"}, {"primary_key": "1101557", "vector": [], "sparse_vector": [], "title": "ProvG-Searcher: A Graph Representation Learning Approach for Efficient Provenance Graph Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We present ProvG-Searcher, a novel approach for detecting known APT behaviors within system security logs. Our approach leverages provenance graphs, a comprehensive graph representation of event logs, to capture and depict data provenance relations by mapping system entities as nodes and their interactions as edges. We formulate the task of searching provenance graphs as a subgraph matching problem and employ a graph representation learning method. The central component of our search methodology involves embedding of subgraphs in a vector space where subgraph relationships can be directly evaluated. We achieve this through the use of order embeddings that simplify subgraph matching to straightforward comparisons between a query and precomputed subgraph representations. To address challenges posed by the size and complexity of provenance graphs, we propose a graph partitioning scheme and a behavior-preserving graph reduction method. Overall, our technique offers significant computational efficiency, allowing most of the search computation to be performed offline while incorporating a lightweight comparison step during query execution. Experimental results on standard datasets demonstrate that ProvG-Searcher achieves superior performance, with an accuracy exceeding 99% in detecting query behaviors and a false positive rate of approximately 0.02%, outperforming other approaches.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623187"}, {"primary_key": "1101558", "vector": [], "sparse_vector": [], "title": "A Novel Analysis of Utility in Privacy Pipelines, Using Kronecker Products and Quantitative Information Flow.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We combine Kronecker products, and quantitative information flow, to give a novel formal analysis for the fine-grained verification of utility in complex privacy pipelines. The combination explains a surprising anomaly in the behaviour of utility of privacy-preserving pipelines - that sometimes a reduction in privacy results also in a decrease in utility. We use the standard measure of utility for Bayesian analysis, introduced by <PERSON><PERSON><PERSON> at al. [1], to produce tractable and rigorous proofs of the fine-grained statistical behaviour leading to the anomaly. More generally, we offer the prospect of formal-analysis tools for utility that complement extant formal analyses of privacy. We demonstrate our results on a number of common privacy-preserving designs.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623081"}, {"primary_key": "1101559", "vector": [], "sparse_vector": [], "title": "Post-Quantum Multi-Recipient Public Key Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A multi-message multi-recipient PKE (mmPKE) encrypts a batch of messages, in one go, to a corresponding set of independently chosen receiver public keys. The resulting ''multi-recipient ciphertext'' can be then be reduced (by any 3rd party) to a shorter, receiver specific, ''invidual ciphertext.'' Finally, to recover the i-th message in the batch from their indvidual ciphertext the i-th receiver only needs their own decryption key. A special case of mmPKE is multi-recipient PKE (mPKE) where all receivers are sent the same message. By treating (m)mPKE and their KEM counterparts as a stand-alone primitives we allow for more efficient constructions than trivially composing individual PKE/KEM instances. This is especially valuable in the post-quantum setting, where PKE/KEM ciphertexts and public keys tend to be far larger than their classic counterparts.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623185"}, {"primary_key": "1101560", "vector": [], "sparse_vector": [], "title": "&quot;We&apos;ve Disabled MFA for You&quot;: An Evaluation of the Security and Usability of Multi-Factor Authentication Recovery Deployments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multi-Factor Authentication is intended to strengthen the security of password-based authentication by adding another factor, such as hardware tokens or one-time passwords using mobile apps.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623180"}, {"primary_key": "1101561", "vector": [], "sparse_vector": [], "title": "Demo: Data Minimization and Informed Consent in Administrative Forms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This article proposes a demonstration implementing the data minimization privacy principle, focusing on reducing data collected by government administrations through forms. Data minimization is defined in many privacy regulations worldwide, but has not seen extensive real-world application. We propose a model based on logic and game theory and show that it is possible to create a practical and efficient solution for a real French welfare benefit case.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624363"}, {"primary_key": "1101562", "vector": [], "sparse_vector": [], "title": "BLUFFS: Bluetooth Forward and Future Secrecy Attacks and Defenses.", "authors": ["<PERSON><PERSON>"], "summary": "Bluetooth is a pervasive technology for wireless communication. Billions of devices use it in sensitive applications and to exchange private data. The security of Bluetooth depends on the Bluetooth standard and its two security mechanisms: pairing and session establishment. No prior work, including the standard itself, analyzed the future and forward secrecy guarantees of these mechanisms, e.g., if Bluetooth pairing and session establishment defend past and future sessions when the adversary compromises the current. To address this gap, we present six novel attacks, defined as the BLUFFS attacks, breaking Bluetooth sessions' forward and future secrecy. Our attacks enable device impersonation and machine-in-the-middle across sessions by only compromising one session key. The attacks exploit two novel vulnerabilities that we uncover in the Bluetooth standard related to unilateral and repeatable session key derivation. As the attacks affect Bluetooth at the architectural level, they are effective regardless of the victim's hardware and software details (e.g., chip, stack, version, and security mode).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623066"}, {"primary_key": "1101563", "vector": [], "sparse_vector": [], "title": "Shufflecake: Plausible Deniability for Multiple Hidden Filesystems on Linux.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Shufflecake, a new plausible deniability design to hide the existence of encrypted data on a storage medium making it very difficult for an adversary to prove the existence of such data. Shufflecake can be considered a \"spiritual successor'' of tools such as TrueCrypt and VeraCrypt, but vastly improved: it works natively on Linux, it supports any filesystem of choice, and can manage multiple volumes per device, so to make deniability of the existence of hidden partitions really plausible. Compared to ORAM-based solutions, Shufflecake is extremely fast and simpler but does not offer native protection against multi-snapshot adversaries. However, we discuss security extensions that are made possible by its architecture, and we show evidence why these extensions might be enough to thwart more powerful adversaries. We implemented Shufflecake as an in-kernel tool for Linux, adding useful features, and we benchmarked its performance showing only a minor slowdown compared to a base encrypted system. We believe Shufflecake represents a useful tool for people whose freedom of expression is threatened by repressive authorities or dangerous criminal organizations, in particular: whistleblowers, investigative journalists, and activists for human rights in oppressive regimes.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623126"}, {"primary_key": "1101564", "vector": [], "sparse_vector": [], "title": "Verifiable Mix-Nets and Distributed Decryption for Voting from Lattice-Based Assumptions.", "authors": ["Diego F. <PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cryptographic voting protocols have recently seen much interest from practitioners due to their (planned) use in countries such as Estonia, Switzerland, France, and Australia. Practical protocols usually rely on tested designs such as the mixing-and-decryption paradigm. There, multiple servers verifiably shuffle encrypted ballots, which are then decrypted in a distributed manner. While several efficient protocols implementing this paradigm exist from discrete log-type assumptions, the situation is less clear for post-quantum alternatives such as lattices. This is because the design ideas of the discrete log-based voting protocols do not carry over easily to the lattice setting, due to specific problems such as noise growth and approximate relations. This work proposes a new verifiable secret shuffle for BGV ciphertexts and a compatible verifiable distributed decryption protocol. The shuffle is based on an extension of a shuffle of commitments to known values which is combined with an amortized proof of correct re-randomization. The verifiable distributed decryption protocol uses noise drowning, proving the correctness of decryption steps in zero-knowledge. Both primitives are then used to instantiate the mixing-and-decryption electronic voting paradigm from lattice-based assumptions. We give concrete parameters for our system, estimate the size of each component and provide implementations of all important sub-protocols. Our experiments show that the shuffle and decryption protocol is suitable for use in real-world e-voting schemes.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616683"}, {"primary_key": "1101565", "vector": [], "sparse_vector": [], "title": "Faster Constant-time Evaluation of the Kronecker Symbol with Application to Elliptic Curve Hashing.", "authors": ["Diego F. <PERSON>a", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We generalize the Bernstein-Yang (BY) algorithm [11] for constant-time modular inversion to compute the <PERSON><PERSON><PERSON> symbol, of which the Jacobi and <PERSON><PERSON> symbols are special cases. We first develop a basic and easy-to-implement algorithm, defined with full-precision division steps. We then describe an optimized version due to Hamburg [21] over word-sized inputs, and formally verify its correctness. Along the way, we introduce a number of optimizations for implementing both versions in constant time. The resulting algorithms are particularly suitable for computing the <PERSON>re symbol with dense prime p, where no efficient addition chain is known for expo-nentiating to P −1/ 2, as it is often the case in pairing-friendly elliptic curves. Our high-speed implementation for a range of parameters shows that the new algorithm is up to 40 times faster than exponentiation, and up to 25.7% faster than the previous state of the art. We illustrate our techniques with hashing to elliptic curves using the SwiftEC algorithm [17], with savings of 14.7% - 48.1%, and to accelerating the CTIDH isogeny-based key exchange [7], with savings of 3.5-13.5%.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616597"}, {"primary_key": "1101566", "vector": [], "sparse_vector": [], "title": "Turning Privacy-preserving Mechanisms against Federated Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, researchers have successfully employed Graph Neural Networks (GNNs) to build enhanced recommender systems due to their capability to learn patterns from the interaction between involved entities. In addition, previous studies have investigated federated learning as the main solution to enable a native privacy-preserving mechanism for the construction of global GNN models without collecting sensitive data into a single computation unit. Still, privacy issues may arise as the analysis of local model updates produced by the federated clients can return information related to sensitive local data. For this reason, researchers proposed solutions that combine federated learning with Differential Privacy strategies and community-driven approaches, which involve combining data from neighbor clients to make the individual local updates less dependent on local sensitive data.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623114"}, {"primary_key": "1101567", "vector": [], "sparse_vector": [], "title": "A Generic Methodology for the Modular Verification of Security Protocol Implementations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Security protocols are essential building blocks of modern IT systems. Subtle flaws in their design or implementation may compromise the security of entire systems. It is, thus, important to prove the absence of such flaws through formal verification. Much existing work focuses on the verification of protocol *models*, which is not sufficient to show that their *implementations* are actually secure. Verification techniques for protocol implementations (e.g., via code generation or model extraction) typically impose severe restrictions on the used programming language and code design, which may lead to sub-optimal implementations. In this paper, we present a methodology for the modular verification of strong security properties directly on the level of the protocol implementations. Our methodology leverages state-of-the-art verification logics and tools to support a wide range of implementations and programming languages. We demonstrate its effectiveness by verifying memory safety and security of Go implementations of the Needham-Schroeder-Lowe, <PERSON><PERSON><PERSON><PERSON> key exchange, and WireGuard protocols, including forward secrecy and injective agreement for WireGuard. We also show that our methodology is agnostic to a particular language or program verifier with a prototype implementation for C.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623105"}, {"primary_key": "1101568", "vector": [], "sparse_vector": [], "title": "Secure Statistical Analysis on Multiple Datasets: Join and Group-By.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We implement a secure platform for statistical analysis over multiple organizations and multiple datasets. We provide a suite of protocols for different variants of JOIN and GROUP-BY operations. JOIN allows combining data from multiple datasets based on a common column. GROUP-BY allows aggregating rows that have the same values in a column or a set of columns, and then apply some aggregation summary on the rows (such as sum, count, median, etc.). Both operations are fundamental tools for relational databases. One example use case of our platform is in data marketing in which an analyst would join purchase histories and membership information, and then obtain statistics, such as \"Which products were bought by people earning this much per annum?\" Both JOIN and GROUP-BY involve many variants, and we design protocols for several common procedures. In particular, we propose a novel group-by-median protocol that has not been known so far. Our protocols rely on sorting protocols, and work in the honest majority setting and against malicious adversaries. To the best of our knowledge, this is the first implementation of JOIN and GROUP-BY protocols secure against a malicious adversary.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623119"}, {"primary_key": "1101569", "vector": [], "sparse_vector": [], "title": "FutORAMa: A Concretely Efficient Hierarchical Oblivious RAM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Oblivious RAM (ORAM) is a general-purpose technique for hiding memory access patterns. This is a fundamental task underlying many secure computation applications. While known ORAM schemes provide optimal asymptotic complexity, despite extensive efforts, their concrete costs remain prohibitively expensive for many interesting applications. The current state-of-the-art practical ORAM schemes are suitable only for somewhat small memories (Square-Root ORAM or Path ORAM).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623125"}, {"primary_key": "1101570", "vector": [], "sparse_vector": [], "title": "Lanturn: Measuring Economic Security of Smart Contracts Through Adaptive Learning.", "authors": ["<PERSON><PERSON><PERSON> Babel", "<PERSON><PERSON>", "Yan Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce Lanturn: a general purpose adaptive learning-based framework for measuring the cryptoeconomic security of composed decentralized-finance (DeFi) smart contracts. Lanturn discovers strategies comprising of concrete transactions for extracting economic value from smart contracts interacting with a particular transaction environment. We formulate the strategy discovery as a black-box optimization problem and leverage a novel adaptive learning-based algorithm to address it.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623204"}, {"primary_key": "1101571", "vector": [], "sparse_vector": [], "title": "Adaptively Secure (Aggregatable) PVSS and Application to Distributed Randomness Beacons.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Publicly Verifiable Secret Sharing (PVSS) is a fundamental primitive that allows to share a secret S among n parties via a publicly verifiable transcript T. Existing (efficient) PVSS are only proven secure against static adversaries who must choose who to corrupt ahead of a protocol execution. As a result, any protocol (e.g., a distributed randomness beacon) that builds on top of such a PVSS scheme inherits this limitation. To overcome this barrier, we revisit the security of PVSS under adaptive corruptions and show that, surprisingly, many protocols from the literature already achieve it in a meaningful way:", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623106"}, {"primary_key": "1101572", "vector": [], "sparse_vector": [], "title": "Realistic Website Fingerprinting By Augmenting Network Traces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Website Fingerprinting (WF) is considered a major threat to the anonymity of Tor users (and other anonymity systems). While state-of-the-art WF techniques have claimed high attack accuracies, e.g., by leveraging Deep Neural Networks (DNN), several recent works have questioned the practicality of such WF attacks in the real world due to the assumptions made in the design and evaluation of these attacks. In this work, we argue that such impracticality issues are mainly due to the attacker's inability in collecting training data in comprehensive network conditions, e.g., a WF classifier may be trained only on high-bandwidth samples collected on specific high-bandwidth network links but deployed on connections with different network conditions. We show that augmenting network traces can enhance the performance of WF classifiers in unobserved network conditions. Specifically, we introduce NetAugment, an augmentation technique tailored to the specifications of Tor traces. We instantiate NetAugment through semi-supervised and self-supervised learning techniques. Our extensive open-world and close-world experiments demonstrate that under practical evaluation settings, our WF attacks provide superior performances compared to the state-of-the-art; this is due to their use of augmented network traces for training, which allows them to learn the features of target traffic in unobserved settings (e.g., unknown bandwidth, Tor circuits, etc.). For instance, with a 5-shot learning in a closed-world scenario, our self-supervised WF attack (named NetCLR) reaches up to 80% accuracy when the traces for evaluation are collected in a setting unobserved by the WF adversary. This is compared to an accuracy of 64.4% achieved by the state-of-the-art Triplet Fingerprinting [34]. We believe that the promising results of our work can encourage the use of network trace augmentation in other types of network traffic analysis.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616639"}, {"primary_key": "1101573", "vector": [], "sparse_vector": [], "title": "Poster: Longitudinal Measurement of the Adoption Dynamics in Apple&apos;s Privacy Label Ecosystem.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This work reports on a large scale, longitudinal analysis of the adoption dynamics of privacy labels in the iOS App Store, measuring this first-of-its kind ecosystem as it reaches maturity over two and a half years after launching in December 2020. The motivation is to shed light on the factors affecting the shifts in privacy labels and provide insights into how and when an app's label changes. By collecting nearly weekly snapshots of over 1.6 million apps for over a year, we analyze the dynamics of privacy label adoption and the accuracy of reported labels. Our analysis of 74.5% of apps having labels after two years provides important context into this mature ecosystem where labels are becoming the standard. However, we find compelling evidence that labels may not fully capture behavior, as 28.9% of apps indicate no data collection and distributions differ between voluntary versus mandatory adoptions. Once set, labels rarely change but additions reflect more data collection. In addition to our measurement, we also plan to release a new (and growing) data set that can be used by future researchers.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624383"}, {"primary_key": "1101574", "vector": [], "sparse_vector": [], "title": "Modular Sumcheck Proofs with Applications to Machine Learning and Image Processing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cryptographic proof systems provide integrity, fairness, and privacy in applications that outsource data processing tasks. However, general-purpose proof systems do not scale well to large inputs. At the same time, ad-hoc solutions for concrete applications - e.g., machine learning or image processing - are more efficient but lack modularity, hence they are hard to extend or to compose with other tools of a data-processing pipeline.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623160"}, {"primary_key": "1101575", "vector": [], "sparse_vector": [], "title": "HELiKs: HE Linear Algebra Kernels for Secure Inference.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce HELiKs, a groundbreaking framework for fast and secure matrix multiplication and 3D convolutions, tailored for privacy-preserving machine learning. Leveraging Homomorphic Encryption (HE) and Additive Secret Sharing, HELiKs enables secure matrix and vector computations while ensuring end-to-end data privacy for all parties. Key innovations of the proposed framework include an efficient multiply-accumulate (MAC) design that significantly reduces HE error growth, a partial sum accumulation strategy that cuts the number of HE rotations by a logarithmic factor, and a novel matrix encoding that facilitates faster online HE multiplications with one-time pre-computation. Furthermore, HELiKs substantially reduces the number of keys used for HE computation, leading to lower bandwidth usage during the setup phase. In our evaluation, HELiKs shows considerable performance improvements in terms of runtime and communication overheads when compared to existing secure computation methods. With our proof-of-work implementation (available on GitHub: https://github.com/shashankballa/HELiKs), we demonstrate state-of-the-art performance with up to 32x speedup for matrix multiplication and 27x speedup for 3D convolution when compared to prior art. HELiKs also reduces communication overheads by 1.5x for matrix multiplication and 29x for 3D convolution over prior works, thereby improving the efficiency of data transfer.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623136"}, {"primary_key": "1101576", "vector": [], "sparse_vector": [], "title": "Amplification by Shuffling without Shuffling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by recent developments in the shuffle model of differential privacy, we propose a new approximate shuffling functionality called Alternating Shuffle, and provide a protocol implementing alternating shuffling in a single-server threat model where the adversary observes all communication. Unlike previous shuffling protocols in this threat model, the per-client communication of our protocol only grows sub-linearly in the number of clients. Moreover, we study the concrete efficiency of our protocol and show it can improve per-client communication by one or more orders of magnitude with respect to previous (approximate) shuffling protocols. We also show a differential privacy amplification result for alternating shuffling analogous to the one for uniform shuffling, and demonstrate that shuffling-based protocols for secure summation based a construction of <PERSON><PERSON> et al. remain secure under the Alternating Shuffle. In the process we also develop a protocol for exact shuffling in single-server threat model with amortized logarithmic communication per-client which might be of independent interest.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623215"}, {"primary_key": "1101577", "vector": [], "sparse_vector": [], "title": "Unforgeability in Stochastic Gradient Descent.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Stochastic Gradient Descent (SGD) is a popular training algorithm, a cornerstone of modern machine learning systems. Several security applications benefit from determining if SGD executions are forgeable, i.e., whether the model parameters seen at a given step are obtainable by more than one distinct set of data samples. In this paper, we present the first attempt at proving impossibility of such forgery. We furnish a set of conditions, which are efficiently checkable on concrete checkpoints seen during training runs, under which checkpoints are provably unforgeable at that step. Our experiments show that the conditions are somewhat mild and hence always satisfied at checkpoints sampled in our experiments. Our results sharply contrast prior findings at a high level: We show that checkpoints we find to be provably unforgeable have been deemed to be forgeable using the same methodology and experimental setup suggested in prior work. This discrepancy arises because of unspecified subtleties in definitions. We experimentally confirm that the distinction matters, i.e., small errors amplify during training to produce significantly observable difference in final models trained. We hope our results serve as a cautionary note on the role of algebraic precision in forgery definitions and related security arguments.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623093"}, {"primary_key": "1101578", "vector": [], "sparse_vector": [], "title": "Is Modeling Access Control Worth It?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Implementing access control policies is an error-prone task that can have severe consequences for the security of software applications. Model-driven approaches have been proposed in the literature and associated tools have been developed with the goal of reducing the complexity of this task and helping developers to produce secure software efficiently. Nevertheless, there is a lack of empirical data supporting the advantages of model-driven security approaches over code-centric approaches, which are the de-facto industry standard for software development.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623196"}, {"primary_key": "1101580", "vector": [], "sparse_vector": [], "title": "ASHES &apos;23: Workshop on Attacks and Solutions in Hardware Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Domenic Forte", "<PERSON>"], "summary": "The workshop on \"Attacks and Solutions in HardwarE Security (ASHES)\" welcomes any theoretical and practical works on hardware security, including attacks, solutions, countermeasures, proofs, classification, formalization, and implementations. Besides mainstream research, ASHES puts some focus on new and emerging scenarios: This includes the Internet of Things (IoT), nuclear weapons inspections, arms control, consumer and infrastructure security, or supply chain security, among others. ASHES also welcomes works on special purpose hardware, such as lightweight, low-cost, and energy-efficient devices, or non-electronic security systems.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624028"}, {"primary_key": "1101581", "vector": [], "sparse_vector": [], "title": "Scalable Multiparty Garbling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zhengzhong Jin", "<PERSON>"], "summary": "Multiparty garbling is the most popular approach for constant-round secure multiparty computation (MPC). Despite being the focus of significant research effort, instantiating prior approaches to multiparty garbling results in constant-round MPC that can not realistically accommodate large numbers of parties. In this work we present the first global-scale multiparty garbling protocol. The per-party communication complexity of our protocol decreases as the number of parties participating in the protocol increases - for the first time matching the asymptotic communication complexity of non-constant round MPC protocols. Our protocol achieves malicious security in the honest-majority setting and relies on the hardness of the Learning Party with Noise assumption.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623132"}, {"primary_key": "1101582", "vector": [], "sparse_vector": [], "title": "FPT: A Fixed-Point Accelerator for Torus Fully Homomorphic Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "Jan<PERSON><PERSON>&apo<PERSON>;An<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Fully Homomorphic Encryption (FHE) is a technique that allows computation on encrypted data. It has the potential to drastically change privacy considerations in the cloud, but high computational and memory overheads are preventing its broad adoption. TFHE is a promising Torus-based FHE scheme that heavily relies on bootstrapping, the noise-removal tool invoked after each encrypted logical/arithmetical operation.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623159"}, {"primary_key": "1101583", "vector": [], "sparse_vector": [], "title": "Attack Some while Protecting Others: Selective Attack Strategies for Attacking and Protecting Multiple Concepts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine learning models are vulnerable to adversarial attacks. Existing research focuses on attack-only scenarios. In practice, one dataset may be used for learning different concepts, and the attacker may be incentivized to attack some concepts but protect the others. For example, the attacker might tamper a profile image for the \"age'' model to predict \"young'', while the \"attractiveness'' model still predicts \"pretty''. In this work, we empirically demonstrate that attacking the classifier for one learning task may negatively impact classifiers learning other tasks on the same data. This raises an interesting research question: is it possible to attack one set of classifiers while protecting the others trained on the same data?", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623177"}, {"primary_key": "1101584", "vector": [], "sparse_vector": [], "title": "Recursion over Public-Coin Interactive Proof Systems; Faster Hash Verification.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "SNARK is a well-known family of cryptographic tools that is increasingly used in the field of computation integrity at scale. In this area, multiple works have introduced SNARK-friendly cryptographic primitives: hashing, but also encryption and signature verification. Despite all the efforts to create cryptographic primitives that can be proved faster, it remains a major performance hole in practice. In this paper, we present a recursive technique that can improve the efficiency of the prover by an order of magnitude compared to proving MiMC hashes (a SNARK-friendly hash function, <PERSON><PERSON> et al. 2016) with a Groth16 (Eurocrypt 2016) proof. We use GKR (a well-known public-coin argument system by <PERSON><PERSON> et al., STOC 2008) to prove the integrity of hash computations and embed the GKR verifier inside a SNARK circuit. The challenge comes from the fact that GKR is a public-coin interactive protocol, and applying Fiat-Shamir naively may result in worse performance than applying existing techniques directly. This is because Fiat-Shamir itself is involved with hash computation over a large string. We take advantage of a property that SNARK schemes commonly have, to build a protocol in which the Fiat-Shamir hashes have very short inputs. The technique we present is generic and can be applied over any SNARK-friendly hash, most known SNARK schemes, and any (one-round) public-coin argument system in place of GKR. We emphasize that while our general compiler is secure in the random oracle model, our concrete instantiation (i.e., GKR plus outer SNARK) is only proved to be heuristically secure. This is due to the fact we first need to convert the GKR protocol to a one-round protocol. Thus, the random oracle of GKR, starting from the second round, is replaced with a concrete hash inside the outer layer SNARK which makes the security-proof heuristic.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623078"}, {"primary_key": "1101585", "vector": [], "sparse_vector": [], "title": "In Search of netUnicorn: A Data-Collection Platform to Develop Generalizable ML Models for Network Security Problems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The remarkable success of the use of machine learning-based solutions for network security problems has been impeded by the developed ML models' inability to maintain efficacy when used in different network environments exhibiting different network behaviors. This issue is commonly referred to as the generalizability problem of ML models. The community has recognized the critical role that training datasets play in this context and has developed various techniques to improve dataset curation to overcome this problem. Unfortunately, these methods are generally ill-suited or even counterproductive in the network security domain, where they often result in unrealistic or poor-quality datasets.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623075"}, {"primary_key": "1101586", "vector": [], "sparse_vector": [], "title": "Lattice-Based Blind Signatures: Short, Efficient, and Round-Optimal.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a 2-round blind signature protocol based on the random oracle heuristic and the hardness of standard lattice problems (Ring/Module-SIS/LWE and NTRU) with a signature size of 20 KB. The protocol is round-optimal and has a transcript size that can be as small as 60 KB. This blind signature is around 4 times shorter than the most compact lattice-based scheme based on standard assumptions of <PERSON> and <PERSON><PERSON> (Crypto 2022) and around 2 times shorter than the scheme of <PERSON><PERSON><PERSON> et al. (CCS 2022) based on their newly-proposed one-more-ISIS assumption. We also propose a \"keyed-verification'' blind signature scheme in which the verifier and the signer need to share a secret key. This scheme has a smaller signature size of only 48 bytes, but further work is needed to explore the efficiency of its signature generation protocol.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616613"}, {"primary_key": "1101587", "vector": [], "sparse_vector": [], "title": "Let&apos;s Go Eevee! A Friendly and Suitable Family of AEAD Modes for IoT-to-Cloud Secure Computation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "IoT devices collect privacy-sensitive data, e.g., in smart grids or in medical devices, and send this data to cloud servers for further processing. In order to ensure confidentiality as well as authenticity of the sensor data in the untrusted cloud environment, we consider a transciphering scenario between embedded IoT devices and multiple cloud servers that perform secure multi-party computation (MPC). Concretely, the IoT devices encrypt their data with a lightweight symmetric cipher and send the ciphertext to the cloud servers. To obtain the secret shares of the cleartext message for further processing, the cloud servers engage in an MPC protocol to decrypt the ciphertext in a distributed manner. This way, the plaintext is never exposed to the individual servers.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623091"}, {"primary_key": "1101588", "vector": [], "sparse_vector": [], "title": "ASMesh: Anonymous and Secure Messaging in Mesh Networks Using Stronger, Anonymous Double Ratchet.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The majority of secure messengers have single, centralized service providers that relay ciphertexts between users to enable asynchronous communication. However, in some scenarios such as mass protests in censored networks, relying on a centralized provider is fatal. Mesh messengers attempt to solve this problem by building ad hoc networks in which user clients perform the ciphertext-relaying task. Yet, recent analyses of widely deployed mesh messengers discover severe security weaknesses (<PERSON><PERSON> et al. CT-RSA'21 & USENIX Security'22).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616615"}, {"primary_key": "1101589", "vector": [], "sparse_vector": [], "title": "Interactive Proofs For Differentially Private Counting.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Differential Privacy (DP) is often presented as a strong privacy-enhancing technology with broad applicability and advocated as a de facto standard for releasing aggregate statistics on sensitive data. However, in many embodiments, DP introduces a new attack surface: a malicious entity entrusted with releasing statistics could manipulate the results and use the randomness of DP as a convenient smokescreen to mask its nefariousness. Since revealing the random noise would obviate the purpose of introducing it, the miscreant may have a perfect alibi. To close this loophole, we introduce the idea of Interactive Proofs For Differential Privacy, which requires the publishing entity to output a zero knowledge proof that convinces an efficient verifier that the output is both DP and reliable. Such a definition might seem unachievable, as a verifier must validate that DP randomness was generated faithfully without learning anything about the randomness itself. We resolve this paradox by carefully mixing private and public randomness to compute verifiable DP counting queries with theoretical guarantees and show that it is also practical for real-world deployment. We also demonstrate that computational assumptions are necessary by showing a separation between information-theoretic DP and computational DP under our definition of verifiability.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616681"}, {"primary_key": "1101591", "vector": [], "sparse_vector": [], "title": "ARTMAN &apos;23: First Workshop on Recent Advances in Resilient and Trustworthy ML Systems in Autonomous Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The increasing integration of machine learning (ML) approaches into the operation and management (O&M) of modern networks has led researchers to address various problems such as performance optimization, anomaly detection, traffic prediction, root-cause analysis and incident troubleshooting. Autonomous networks leverage the wealth of both business and operations data to achieve fully intelligent and automated O&M for various telecommunications applications. However, their high level of service requires the closest scrutiny as such applications depend on their resilience and trustworthiness, especially in the face of motivated attackers that aim at abusing their underlying ML models. This workshop fosters the close collaboration between researchers and practitioners at the intersection of security, networks and ML communities to improve the security of ML applications in autonomous networks together.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624027"}, {"primary_key": "1101592", "vector": [], "sparse_vector": [], "title": "Abraxas: Throughput-Efficient Hybrid Asynchronous Consensus.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Protocols for state-machine replication (SMR) often trade off performance for resilience to network delay. In particular, protocols for asynchronous SMR tolerate arbitrary network delay but sacrifice throughput/latency when the network is fast, while partially synchronous protocols have good performance in a fast network but fail to make progress if the network experiences high delay. Existing hybrid protocols are resilient to arbitrary network delay and have good performance when the network is fast, but suffer from high overhead (''thrashing'') if the network repeatedly switches between being fast and slow, e.g., in a network that is typically fast but has intermittent message delays.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623191"}, {"primary_key": "1101593", "vector": [], "sparse_vector": [], "title": "Analyzing the Real-World Security of the Algorand Blockchain.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Algorand consensus protocol is interesting both in theory and in practice. On the theoretical side, to achieve adaptive security, it introduces the novel idea of player replaceability, where each step of the protocol is executed by a different randomly selected committee whose members remain secret until they send their first and only message. The protocol provides consistency under arbitrary network conditions and liveness under intermittent network partitions. On the practical side, the protocol is used to secure the Algorand cryptocurrency, whose total value is approximately 850M at the time of writing.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623167"}, {"primary_key": "1101594", "vector": [], "sparse_vector": [], "title": "Ramen: Souper Fast Three-Party Computation for RAM Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Secure RAM computation allows a number of parties to evaluate a function represented as a random-access machine (RAM) program in a way that reveals nothing about the private inputs of the parties except from what is already revealed by the function output itself. In this work we present Ramen, which is a new protocol for computing RAM programs securely among three parties, tolerating up to one passive corruption. Ramen provides reasonable asymptotic guarantees and is concretely efficient at the same time. We have implemented our protocol and provide extensive benchmarks for various settings.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623115"}, {"primary_key": "1101595", "vector": [], "sparse_vector": [], "title": "PLAS: The 18th Workshop on Programming Languages and Analysis for Security.", "authors": ["<PERSON>", "<PERSON>"], "summary": "PLAS provides a forum for exploring and evaluating the use of programming language and program analysis techniques for promoting security in the complete range of software systems, from compilers to machine-learned models and smart contracts. The workshop encourages proposals of new, speculative ideas, evaluations of new or known techniques in practical settings, and discussions of emerging threats and problems. We also host position papers that are radical, forward-looking, and lead to lively and insightful discussions influential to future research at the intersection of programming languages and security. This year will mark the 18th iteration of PLAS, which was first held in 2007 in San Diego. We expect an exciting program and many interesting discussions.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624025"}, {"primary_key": "1101596", "vector": [], "sparse_vector": [], "title": "CheckMate: Automated Game-Theoretic Security Reasoning.", "authors": ["<PERSON>ome Brugger", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present the CheckMate framework for full automation of game-theoretic security analysis, with particular focus on blockchain technologies. CheckMate analyzes protocols modeled as games for their game-theoretic security - that is, for incentive compatibility and Byzantine fault-tolerance. The framework either proves the protocols secure by providing defense strategies or yields all possible attack vectors. For protocols that are not secure, CheckMate can also provide weakest preconditions under which the protocol becomes secure, if they exist. CheckMate implements a sound and complete encoding of game-theoretic security in first-order linear real arithmetic, thereby reducing security analysis to satisfiability solving. CheckMate further automates efficient handling of case splitting on arithmetic terms. Experiments show CheckMate scales, analyzing games with trillions of strategies that model phases of Bitcoin's Lightning Network.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623183"}, {"primary_key": "1101597", "vector": [], "sparse_vector": [], "title": "Improved Distributed RSA Key Generation Using the Miller-Rabin Test.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Secure distributed generation of RSA moduli (e.g., generating N=pq where none of the parties learns anything about p or q) is an important cryptographic task, that is needed both in threshold implementations of RSA-based cryptosystems and in other, advanced cryptographic protocols that assume that all the parties have access to a trusted RSA modulo. In this paper, we provide a novel protocol for secure distributed RSA key generation based on the Miller-<PERSON><PERSON> test. Compared with the more commonly used Boneh-Franklin test (which requires many iterations), the Miller-<PERSON><PERSON> test has the advantage of providing negligible error after even a single iteration of the test for large enough moduli (e.g., 4096 bits).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623163"}, {"primary_key": "1101598", "vector": [], "sparse_vector": [], "title": "Provably Unlinkable Smart Card-based Payments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The most prevalent smart card-based payment method, EMV, currently offers no privacy to its users. Transaction details and the card number are sent in cleartext, enabling the profiling and tracking of cardholders. Since public awareness of privacy issues is growing and legislation, such as GDPR, is emerging, we believe it is necessary to investigate the possibility of making payments anonymous and unlikable without compromising essential security guarantees and functional properties of EMV. This paper draws attention to trade-offs between functional and privacy requirements in the design of such a protocol. We present the UTX protocol - an enhanced payment protocol satisfying such requirements, and we formally certify key security and privacy properties using techniques based on the applied π-calculus.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623109"}, {"primary_key": "1101599", "vector": [], "sparse_vector": [], "title": "Verifiable Learning for Robust Tree Ensembles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Verifying the robustness of machine learning models against evasion attacks at test time is an important research problem. Unfortunately, prior work established that this problem is NP-hard for decision tree ensembles, hence bound to be intractable for specific inputs. In this paper, we identify a restricted class of decision tree ensembles, called large-spread ensembles, which admit a security verification algorithm running in polynomial time. We then propose a new approach called verifiable learning, which advocates the training of such restricted model classes which are amenable for efficient verification. We show the benefits of this idea by designing a new training algorithm that automatically learns a large-spread decision tree ensemble from labelled data, thus enabling its security verification in polynomial time. Experimental results on public datasets confirm that large-spread ensembles trained using our algorithm can be verified in a matter of seconds, using standard commercial hardware. Moreover, large-spread ensembles are more robust than traditional ensembles against evasion attacks, at the cost of an acceptable loss of accuracy in the non-adversarial setting.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623100"}, {"primary_key": "1101600", "vector": [], "sparse_vector": [], "title": "Deciding Differential Privacy of Online Algorithms with Multiple Variables.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of checking the differential privacy of online randomized algorithms that process a stream of inputs and produce outputs corresponding to each input. This paper generalizes an automaton model called DiP automata [10] to describe such algorithms by allowing multiple real-valued storage variables. A DiP automaton is a parametric automaton whose behavior depends on the privacy budget ∈. An automaton A will be said to be differentially private if, for some D, the automaton is D∈-differentially private for all values of ∈ > 0. We identify a precise characterization of the class of all differentially private DiP automata. We show that the problem of determining if a given DiP automaton belongs to this class is PSPACE-complete. Our PSPACE algorithm also computes a value for D when the given automaton is differentially private. The algorithm has been implemented, and experiments demonstrating its effectiveness are presented.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623170"}, {"primary_key": "1101601", "vector": [], "sparse_vector": [], "title": "Poster: Secure and Differentially Private kth Ranked Element.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The problem of finding the kth Ranked Element (KRE) is of particular interest in collaborative studies for financial and medical agencies alike. Many of the applications of KRE deal with sensitive information that needs to be protected. The protocol by <PERSON><PERSON> et al. (SECRYPT'22) considers a model where multiple parties hold datasets with many elements and wish to compute the kth element of their joint dataset. In their model, all participating parties interact with a central party in a star network topology. However, they leak some intermediate information to the central party.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624392"}, {"primary_key": "1101602", "vector": [], "sparse_vector": [], "title": "Poster: Verifiable Encodings for Maliciously-Secure Homomorphic Encryption Evaluation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Carmela Tron<PERSON>o", "<PERSON><PERSON><PERSON>"], "summary": "Homomorphic encryption has become a promising solution for protecting the privacy of computations on sensitive data. However, existing homomorphic encryption pipelines do not guarantee the correctness of the computation result in the presence of a malicious adversary. In this poster, we present two encodings compatible with state-of-the-art fully homomorphic encryption schemes that enable practical client-verification of homomorphic computations, while enabling all the operations required for modern privacy-preserving analytics. Based on these encodings, we introduce a ready-to-use library for the verification of any homomorphic operation executed over encrypted data. We demonstrate its practicality for various applications and, in particular, we show that it enables verifiability of some homomorphic analytics with less than 3 times overhead compared to the homomorphic encryption baseline.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624403"}, {"primary_key": "1101603", "vector": [], "sparse_vector": [], "title": "PELTA - Shielding Multiparty-FHE against Malicious Adversaries.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Carmela Tron<PERSON>o", "<PERSON><PERSON><PERSON>"], "summary": "Multiparty fully homomorphic encryption (MFHE) schemes enable multiple parties to efficiently compute functions on their sensitive data while retaining confidentiality. However, existing MFHE schemes guarantee data confidentiality and the correctness of the computation result only against honest-but-curious adversaries. In this work, we provide the first practical construction that enables the verification of MFHE operations in zero-knowledge, protecting MFHE from malicious adversaries. Our solution relies on a combination of lattice-based commitment schemes and proof systems which we adapt to support both modern FHE schemes and their implementation optimizations. We implement our construction in PELTA. Our experimental evaluation shows that PELTA is one to two orders of magnitude faster than existing techniques in the literature.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623139"}, {"primary_key": "1101604", "vector": [], "sparse_vector": [], "title": "Poster: Membership Inference Attacks via Contrastive Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Since machine learning model is often trained on a limited data set, the model is trained multiple times on the same data sample, which causes the model to memorize most of the training set data. Membership Inference Attacks (MIAs) exploit this feature to determine whether a data sample is used for training a machine learning model. However, in realistic scenarios, it is difficult for the adversary to obtain enough qualified samples that mark accurate identity information, especially since most samples are non-members in real world applications. To address this limitation, in this paper, we propose a new attack method called CLMIA, which uses unsupervised contrastive learning to train an attack model. Meanwhile, in CLMIA, we require only a small amount of data with known membership status to fine-tune the attack model. We evaluated the performance of the attack using ROC curves showing a higher TPR at low FPR compared to other schemes.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624384"}, {"primary_key": "1101607", "vector": [], "sparse_vector": [], "title": "Hopper: Interpretative Fuzzing for Libraries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite the fact that the state-of-the-art fuzzers can generate inputs efficiently, existing fuzz drivers still cannot adequately cover entries in libraries. Most of these fuzz drivers are crafted manually by developers, and their quality depends on the developers' understanding of the code. Existing works have attempted to automate the generation of fuzz drivers by learning API usage from code and execution traces. However, the generated fuzz drivers are limited to a few specific call sequences by the code being learned. To address these challenges, we present HOPPER, which can fuzz libraries without requiring any domain knowledge to craft fuzz drivers. It transforms the problem of library fuzzing into the problem of interpreter fuzzing. The interpreters linked against libraries under test can interpret the inputs that describe arbitrary API usage. To generate semantically correct inputs for the interpreter, HOPPER learns the intra-and inter-API constraints in the libraries and mutates the program with grammar awareness. We implemented HOPPER and evaluated its effectiveness on 11 real-world libraries against manually crafted fuzzers and other automatic solutions. Our results show that HOPPER greatly outperformed the other fuzzers in both code coverage and bug finding, having uncovered 25 previously unknown bugs that other fuzzers couldn't. Moreover, we have demonstrated that the proposed intra- and inter-API constraint learning methods can correctly learn constraints implied by the library and, therefore, significantly improve the fuzzing efficiency. The experiment results indicate that HOPPER is able to explore a vast range of API usages for library fuzzing out of the box.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616610"}, {"primary_key": "1101608", "vector": [], "sparse_vector": [], "title": "Homomorphic Multiple Precision Multiplication for CKKS and Reduced Modulus Consumption.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Homomorphic Encryption (HE) schemes such as BGV, BFV, and CKKS consume some ciphertext modulus for each multiplication. Bootstrapping (BTS) restores the modulus and allows homomorphic computation to continue, but it is time-consuming and requires a significant amount of modulus. For these reasons, decreasing modulus consumption is crucial topic for BGV, BFV and CKKS, on which numerous studies have been conducted.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623086"}, {"primary_key": "1101611", "vector": [], "sparse_vector": [], "title": "Declassiflow: A Static Analysis for Modeling Non-Speculative Knowledge to Relax Speculative Execution Security Measures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Speculative execution attacks undermine the security of constant-time programming, the standard technique used to prevent microarchitectural side channels in security-sensitive software such as cryptographic code. Constant-time code must therefore also deploy a defense against speculative execution attacks to prevent leakage of secret data stored in memory or the processor registers. Unfortunately, contemporary defenses, such as speculative load hardening (SLH), can only satisfy this strong security guarantee at a very high performance cost.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623065"}, {"primary_key": "1101613", "vector": [], "sparse_vector": [], "title": "On the Security of Rate-limited Privacy Pass.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The privacy pass protocol allows users to redeem anonymously issued cryptographic tokens instead of solving annoying CAPTCHAs. The issuing authority verifies the credibility of the user, who can later use the pass while browsing the web using an anonymous or virtual private network. <PERSON><PERSON><PERSON><PERSON> et al. proposed an IETF draft (privacypass-rate-limit-tokens-00) for a rate-limiting version of the privacy pass protocol, also called rate-limited Privacy Pass(RlP). Introducing a new actor called a mediator makes both versions inherently different. The mediator applies access policies to rate-limit users' access to the service while, at the same time, should be oblivious to the website/origin the user is trying to access. In this paper, we formally define the rate-limited Privacy Pass protocol and propose a game-based security model to capture the informal security notions introduced by <PERSON><PERSON><PERSON><PERSON> et al.. We show a construction from simple building blocks that fulfills our security definitions and even allows for a post-quantum secure instantiation. Interestingly, the instantiation proposed in the IETF draft is a specific case of our construction. Thus, we can reuse the security arguments for the generic construction and show that the version used in practice is secure.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616619"}, {"primary_key": "1101614", "vector": [], "sparse_vector": [], "title": "Control, Confidentiality, and the Right to be Forgotten.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent digital rights frameworks give users the right to delete their data from systems that store and process their personal information (e.g., the \"right to be forgotten\" in the GDPR).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616585"}, {"primary_key": "1101615", "vector": [], "sparse_vector": [], "title": "Poster: Panacea - Stateless and Non-Interactive Oblivious RAM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jeongeun Park"], "summary": "Oblivious RAM (ORAM) allows a client to outsource database storage to a remote server while hiding the data access pattern. Existing designs use non-linear data structures (e.g., trees or hierarchical structures) and follow a online-offline paradigm. Clients submit their queries in the online phase and then the queries are ''flushed'' in the offline (eviction) phase. Such designs are interactive, requiring more than one round of client-server communication, be it during the online, offline, or both phases. Moreover, the client has to maintain an internal state which depends on the database state.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624388"}, {"primary_key": "1101617", "vector": [], "sparse_vector": [], "title": "Using Range-Revocable Pseudonyms to Provide Backward Unlinkability in the Edge.", "authors": ["Cláudio <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we propose a novel abstraction that we have named Range-Revocable Pseudonyms (RRPs). RRPs are a new class of pseudonyms whose validity can be revoked for any time-range within its original validity period. The key feature of RRPs is that the information provided to revoke a pseudonym for a given time-range cannot be linked with the information provided when using the pseudonym outside the revoked range. We provide an algorithm to implement RRPs using efficient cryptographic primitives where the space complexity of the pseudonym is constant, regardless of the granularity of the revocation range, and the space complexity of the revocation information only grows logarithmically with the granularity; this makes the use of RRPs far more efficient than the use of many short-lived pseudonyms. We have used RRPs to design EDGAR, an access control system for VANET scenarios that offers backward unlinkability. The experimental evaluation of EDGAR shows that, when using RRPs, the revocation can be performed efficiently (even when using time slots as small as 1 second) and that users can authenticate with low latency (0.5-3.5ms ).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623111"}, {"primary_key": "1101619", "vector": [], "sparse_vector": [], "title": "ParBFT: Faster Asynchronous BFT Consensus with a Parallel Optimistic Path.", "authors": ["<PERSON><PERSON> Dai", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "To reduce latency and communication overhead of asynchronous Byzantine Fault Tolerance (BFT) consensus, an optimistic path is often added, with <PERSON><PERSON> and BDT as state-of-the-art representatives. These protocols first attempt to run an optimistic path that is typically adapted from partially-synchronous BFT and promises good performance in good situations. If the optimistic path fails to make progress, these protocols switch to a pessimistic path after a timeout, to guarantee liveness in an asynchronous network. This design crucially relies on an accurate estimation of the network delay Δ to set the timeout parameter correctly. A wrong estimation of Δ can lead to either premature or delayed switching to the pessimistic path, hurting the protocol's efficiency in both cases.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623101"}, {"primary_key": "1101620", "vector": [], "sparse_vector": [], "title": "Decoding the Secrets of Machine Learning in Malware Classification: A Deep Dive into Datasets, Feature Extraction, and Model Performance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many studies have proposed machine-learning (ML) models for malware detection and classification, reporting an almost-perfect performance. However, they assemble ground-truth in different ways, use diverse static- and dynamic-analysis techniques for feature extraction, and even differ on what they consider a malware family. As a consequence, our community still lacks an understanding of malware classification results: whether they are tied to the nature and distribution of the collected dataset, to what extent the number of families and samples in the training dataset influence performance, and how well static and dynamic features complement each other.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616589"}, {"primary_key": "1101621", "vector": [], "sparse_vector": [], "title": "Threshold Signatures from Inner Product Argument: Succinct, Weighted, and Multi-threshold.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Benedikt <PERSON>", "<PERSON>"], "summary": "Threshold signatures protect the signing key by sharing it among a group of signers so that an adversary must corrupt a threshold number of signers to be able to forge signatures. Existing threshold signatures with succinct signatures and constant verification times do not work if signers have different weights. Such weighted settings are seeing increasing importance in decentralized systems, especially in the Proof-of-Stake blockchains. This paper presents a new paradigm for threshold signatures for pairing and discrete logarithm-based cryptosystems. Our scheme has a compact verification key consisting of only 7 group elements, and a signature consisting of 8 group elements. Verifying the signature requires 8 exponentiations and 8 bilinear pairings. Our scheme supports arbitrary weight distributions among signers and arbitrary thresholds. It requires non-interactive preprocessing after a universal powers-of-tau setup. We prove the security of our scheme in the Algebraic Group Model and implement it using Golang. Our evaluation shows that our scheme achieves a comparable signature size and verification time to a standard (unweighted) threshold signature. Compared to existing multisignature schemes, our scheme has a much smaller public verification key.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623096"}, {"primary_key": "1101622", "vector": [], "sparse_vector": [], "title": "NestFuzz: Enhancing Fuzzing with Comprehensive Understanding of Input Processing Logic.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Wenzheng Hong", "<PERSON>", "<PERSON>"], "summary": "Fuzzing is one of the most popular and practical techniques for security analysis. In this work, we aim to address the critical problem of high-quality input generation with a novel input-aware fuzzing approach called NestFuzz. NestFuzz can universally and automatically model input format specifications and generate valid input.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623103"}, {"primary_key": "1101623", "vector": [], "sparse_vector": [], "title": "Boosting the Performance of High-Assurance Cryptography: Parallel Execution and Optimizing Memory Access in Formally-Verified Line-Point Zero-Knowledge.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite the notable advances in the development of high-assurance, verified implementations of cryptographic protocols, such implementations typically face significant performance overheads, particularly due to the penalties induced by formal verification and automated extraction of executable code. In this paper, we address some core performance challenges facing computer-aided cryptography by presenting a formal treatment for accelerating such verified implementations based on multiple generic optimizations covering parallelism and memory access. We illustrate our techniques for addressing such performance bottlenecks using the Line-Point Zero-Knowledge (LPZK) protocol as a case study. Our starting point is a new verified implementation of LPZK that we formalize and synthesize using EasyCrypt; our first implementation is developed to reduce the proof effort and without considering the performance of the extracted executable code. We then show how such (automatically) extracted code can be optimized in three different ways to obtain a 3000x speedup and thus matching the performance of the manual implementation of LPZK of lpzkv2.[13] We obtain such performance gains by first modifying the algorithmic specifications, then by adopting a provably secure parallel execution model, and finally by optimizing the memory access structures. All optimizations are first formally verified inside EasyCrypt, and then executable code is automatically synthesized from each step of the formalization. For each optimization, we analyze performance gains resulting from it and also address challenges facing the computer-aided security proofs thereof, and challenges facing automated synthesis of executable code with such an optimization.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616583"}, {"primary_key": "1101624", "vector": [], "sparse_vector": [], "title": "Are we there yet? An Industrial Viewpoint on Provenance-based Endpoint Detection and Response Tools.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Provenance-Based Endpoint Detection and Response (P-EDR) systems are deemed crucial for future Advanced Persistent Threats (APT) defenses. Despite the fact that numerous new techniques to improve P-EDR systems have been proposed in academia, it is still unclear whether the industry will adopt P-EDR systems and what improvements the industry desires for P-EDR systems. To this end, we conduct the first set of systematic studies on the effectiveness and the limitations of P-EDR systems. Our study consists of four components: a one-to-one interview, an online questionnaire study, a survey of the relevant literature, and a systematic measurement study. Our research indicates that all industry experts consider P-EDR systems to be more effective than conventional Endpoint Detection and Response (EDR) systems. However, industry experts are concerned about the operating cost of P-EDR systems. In addition, our research reveals three significant gaps between academia and industry (1) overlooking client-side overhead; (2) imbalancedalarm triage cost and interpretation cost; and (3) excessive server side memory consumption. This paper's findings provide objective data on the effectiveness of P-EDR systems and how much improvements are needed to adopt P-EDR systems in industry.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616580"}, {"primary_key": "1101625", "vector": [], "sparse_vector": [], "title": "Poster: Towards Lightweight TEE-Assisted MPC.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work presents HPCG (short for hardware-assisted pseudorandom correlation generator), a work-in-progress lightweight TEE (LTEE)-assisted MPC solution for both high performance and strong security. HPCG relies on a succinct codebase and small LTEE chips that work only in the MPC offline phase, with the aim of addressing efficiency bottlenecks in traditional MPC, while minimizing the use and trust in secure hardware, which makes a rational compromise between pure cryptography and TEE techniques. We design HPCG to work for diverse MPC settings in the preprocessing model and conform to the mainstream secret-sharing semantics, making it easy to deploy and integrate into existing MPC practices.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624398"}, {"primary_key": "1101626", "vector": [], "sparse_vector": [], "title": "Improving Security Tasks Using Compiler Provenance Information Recovered At the Binary-Level.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The complex optimizations supported by modern compilers allow for compiler provenance recovery at many levels. For instance, it is possible to identify the compiler family and optimization level used when building a binary, as well as the individual compiler passes applied to functions within the binary. Yet, many downstream applications of compiler provenance remain unexplored. To bridge that gap, we train and evaluate a multi-label compiler provenance model on data collected from over 27,000 programs built using LLVM 14, and apply the model to a number of security-related tasks. Our approach considers 68 distinct compiler passes and achieves an average F-1 score of 84.4%. We first use the model to examine the magnitude of compiler-induced vulnerabilities, identifying 53 information leak bugs in 10 popular projects. We also show that several compiler optimization passes introduce a substantial amount of functional code reuse gadgets that negatively impact security. Beyond vulnerability detection, we evaluate other security applications, including using recovered provenance information to verify the correctness of Rich header data in Windows binaries (e.g., forensic analysis), as well as for binary decomposition tasks (e.g., third party library detection).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623098"}, {"primary_key": "1101627", "vector": [], "sparse_vector": [], "title": "DP-Forward: Fine-tuning and Inference on Language Models with Differential Privacy in Forward Pass.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Sherman S. M. Chow", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Differentially private stochastic gradient descent (DP-SGD) adds noise to gradients in back-propagation, safeguarding training data from privacy leakage, particularly membership inference. It fails to cover (inference-time) threats like embedding inversion and sensitive attribute inference. It is also costly in storage and computation when used to fine-tune large pre-trained language models (LMs).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616592"}, {"primary_key": "1101628", "vector": [], "sparse_vector": [], "title": "FIN: Practical Signature-Free Asynchronous Common Subset in Constant Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Asynchronous common subset (ACS) is a powerful paradigm enabling applications such as Byzantine fault-tolerance (BFT) and multi-party computation (MPC). The most efficient ACS framework in the information-theoretic setting is due to <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (BKR, 1994). The BKR ACS protocol has been both theoretically and practically impactful. However, the BKR protocol has an O(log n) running time (where n is the number of replicas) due to the usage of n parallel asynchronous binary agreement (ABA) instances, impacting both performance and scalability. Indeed, for a network of 16 ~ 64 replicas, the parallel ABA phase occupies about 95% ~ 97% of the total runtime in BKR. A long-standing open problem is whether we can build an ACS framework with O(1) time while not increasing the message or communication complexity of the BKR protocol.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616633"}, {"primary_key": "1101629", "vector": [], "sparse_vector": [], "title": "Capacity: Cryptographically-Enforced In-Process Capabilities for Modern ARM Architectures.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In-process compartmentalization and access control have been actively explored to provide in-place and efficient isolation of in-process security domains. Many works have proposed compartmentalization schemes that leverage hardware features, most notably using the new page-based memory isolation feature called Protection Keys for Userspace (PKU) on x86. Unfortunately, the modern ARM architecture does not have an equivalent feature. Instead, newer ARM architectures introduced Pointer Authentication (PA) and Memory Tagging Extension (MTE), adapting the reference validation model for memory safety and runtime exploit mitigation. We argue that those features have been underexplored in the context of compartmentalization and that they can be retrofitted to implement a capability-based in-process access control scheme. This paper presents Capacity, a novel hardware-assisted intra-process access control design that embraces capability-based security principles. Capacity coherently incorporates the new hardware security features on ARM that already exhibit inherent characteristics of capability. It supports the life-cycle protection of the domain's sensitive objects -- starting from their import from the file system to their place in memory. With intra-process domains authenticated with unique PA keys, Capacity transforms file descriptors and memory pointers into cryptographically-authenticated references and completely mediates reference usage with its program instrumentation framework and an efficient system call monitor. We evaluate our Capacity-enabled NGINX web server prototype and other common applications in which sensitive resources are isolated into different domains. Our evaluation shows that Capacity incurs a low-performance overhead of approximately 17% for the single-threaded and 13.54% for the multi-threaded webserver.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623079"}, {"primary_key": "1101630", "vector": [], "sparse_vector": [], "title": "<PERSON> Ostrich: Web Application Scanning with String Solvers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Securing web applications remains a pressing challenge. Unfortunately, the state of the art in web crawling and security scanning still falls short of deep crawling. A major roadblock is the crawlers' limited ability to pass input validation checks when web applications require data of a certain format, such as email, phone number, or zip code. This paper develops Black Ostrich, a principled approach to deep web crawling and scanning. The key idea is to equip web crawling with string constraint solving capabilities to dynamically infer suitable inputs from regular expression patterns in web applications and thereby pass input validation checks. To enable this use of constraint solvers, we develop new automata-based techniques to process JavaScript regular expressions. We implement our approach extending and combining the <PERSON><PERSON><PERSON> constraint solver with the Black Widow web crawler. We evaluate <PERSON> Ostrich on a set of 8,820 unique validation patterns gathered from over 21,667,978 forms from a combination of the July 2021 Common~Crawl and Tranco top 100K. For these forms and reconstructions of input elements corresponding to the patterns, we demonstrate that <PERSON> Ostrich achieves a 99% coverage of the form validations compared to an average of 36% for the state-of-the-art scanners. Moreover, out of the 66,377 domains using these patterns, we solve all patterns on 66,309 (99%) while the combined efforts of the other scanners cover 52,632 (79%). We further show that our approach can boost coverage by evaluating it on three open-source applications. Our empirical studies include a study of email validation patterns, where we find that 213 (26%) out of the 825 found email validation patterns liberally admit XSS injection payloads.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616582"}, {"primary_key": "1101631", "vector": [], "sparse_vector": [], "title": "Poster: Towards Practical Brainwave-based User Authentication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Brainwave measuring devices have transitioned from specialized medical tools to user-friendly and economically accessible consumer products. This shift has opened new avenues for pervasive services, with applications spanning brain-computer interfaces (BCIs), disease detection, criminal trials, and, notably, authentication in computer security. Electroencephalography (EEG) signals, being difficult to steal and revocable, present an attractive biometric option. However, the practical deployment of these signals is hindered by security threats, usability issues, and privacy concerns. To this end, we expect to improve the overall performance of authentication systems using consumer-grade devices, gain a better understanding of user attitudes toward this type of authentication, and protect the user's privacy against unauthorized use of samples collected during enrollment and verification.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624399"}, {"primary_key": "1101632", "vector": [], "sparse_vector": [], "title": "Short Privacy-Preserving Proofs of Liabilities.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the wake of fraud scandals involving decentralized exchanges and the significant financial loss suffered by individuals, regulators are pressed to put mechanisms in place that enforce customer protections and capital requirements in decentralized ecosystems. Proof of liabilities (PoL) is such a mechanism: it allows a prover (e.g., an exchange) to prove its liability to a verifier (i.e., a customer).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616645"}, {"primary_key": "1101633", "vector": [], "sparse_vector": [], "title": "Gotcha! I Know What You Are Doing on the FPGA Cloud: Fingerprinting Co-Located Cloud FPGA Accelerators via Measuring Communication Links.", "authors": ["Chongzhou Fang", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In recent decades, due to the emerging requirements of computation acceleration, cloud FPGAs have become popular in public clouds. Major cloud service providers, e.g. AWS and Microsoft Azure have provided FPGA computing resources in their infrastructure and have enabled users to design and deploy their own accelerators on these FPGAs. Multi-tenancy FPGAs, where multiple users can share the same FPGA fabric with certain types of isolation to improve resource efficiency, have already been proved feasible. However, this also raises security concerns. Various types of side-channel attacks targeting multi-tenancy FPGAs have been proposed and validated. The awareness of security vulnerabilities in the cloud has motivated cloud providers to take action to enhance the security of their cloud environments.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616606"}, {"primary_key": "1101634", "vector": [], "sparse_vector": [], "title": "Combined Private Circuits - Combined Security Refurbished.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Physical attacks are well-known threats to cryptographic implementations. While countermeasures against passive Side-Channel Analysis (SCA) and active Fault Injection Analysis (FIA) exist individually, protecting against their combination remains a significant challenge. A recent attempt at achieving joint security has been published at CCS 2022 under the name CINI-MINIS. The authors introduce relevant security notions and aim to construct arbitrary-order gadgets that remain trivially composable in the presence of a combined adversary. Yet, we show that all CINI-MINIS gadgets at any order are susceptible to a devastating attack with only a single fault and probe due to a lack of error correction modules in the compression. We explain the details of the attack, pinpoint the underlying problem in the constructions, propose an additional design principle, and provide new (fixed) provably secure and composable gadgets for arbitrary order. Luckily, the changes in the compression stage help us to save correction modules and registers elsewhere, making the resulting Combined Private Circuits (CPC) more secure and more efficient than the original ones. We also explain why the discovered flaws have been missed by the associated formal verification tool VERICA (TCHES 2022) and propose fixes to remove its blind spot. Finally, we explore alternative avenues to repair the compression stage without additional corrections based on non-completeness, i.e. constructing a compression that never recombines any secret. Yet, while this approach could have merit for low-order gadgets, it is, for now, hard to generalize and scales poorly to higher orders. We conclude that our refurbished arbitrary order CINI gadgets provide a solid foundation for further research.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623129"}, {"primary_key": "1101635", "vector": [], "sparse_vector": [], "title": "Stateful Defenses for Machine Learning Models Are Not Yet Secure Against Black-box Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent work has proposed stateful defense models (SDMs) as a compelling strategy to defend against a black-box attacker who only has query access to the model, as is common for online machine learning platforms. Such stateful defenses aim to defend against black-box attacks by tracking the query history and detecting and rejecting queries that are \"similar\" and thus preventing black-box attacks from finding useful gradients and making progress towards finding adversarial attacks within a reasonable query budget. Recent SDMs (e.g., Blacklight and PIHA) have shown remarkable success in defending against state-of-the-art black-box attacks. In this paper, we show that SDMs are highly vulnerable to a new class of adaptive black-box attacks. We propose a novel adaptive black-box attack strategy called Oracle-guided Adaptive Rejection Sampling (OARS) that involves two stages: (1) use initial query patterns to infer key properties about an SDM's defense; and, (2) leverage those extracted properties to design subsequent query patterns to evade the SDM's defense while making progress towards finding adversarial inputs. OARS is broadly applicable as an enhancement to existing black-box attacks - we show how to apply the strategy to enhance six common black-box attacks to be more effective against current class of SDMs. For example, OARS-enhanced versions of black-box attacks improved attack success rate against recent stateful defenses from almost 0% to to almost 100% for multiple datasets within reasonable query budgets.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623116"}, {"primary_key": "1101636", "vector": [], "sparse_vector": [], "title": "Poster: WIP: Account ZK<PERSON><PERSON><PERSON> from Sumcheck Arguments.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional blockchains execute transactions through the use of consensus mechanisms that guarantee reasonable expectations of integrity and finality. However, often these incur costs making the throughput of transactions orders of magnitude slower than their web2 counterparts. To address this drawback, so called L2 layers offload transactions from the main chain, called L1, execute these fast and anchor back the result of these transaction through succinct checkpoints. ZK-Rollups are emerging as compelling methods to establish the integrity of such checkpoints, by the use of compressed cryptographic proofs.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624405"}, {"primary_key": "1101637", "vector": [], "sparse_vector": [], "title": "Verifiable Verification in Cryptographic Protocols.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Common verification steps in cryptographic protocols, such as signature or message authentication code checks or the validation of elliptic curve points, are crucial for the overall security of the protocol. Yet implementation errors omitting these steps easily remain unnoticed, as often the protocol will function perfectly anyways. One of the most prominent examples is Apple's goto fail bug where the erroneous certificate verification skipped over several of the required steps, marking invalid certificates as correctly verified. This vulnerability went undetected for at least 17 months.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623151"}, {"primary_key": "1101638", "vector": [], "sparse_vector": [], "title": "Stealth Key Exchange and Confined Access to the Record Protocol Data in TLS 1.3.", "authors": ["<PERSON>"], "summary": "We show how to embed a covert key exchange sub protocol within a regular TLS 1.3 execution, generating a stealth key in addition to the regular session keys. The idea, which has appeared in the literature before, is to use the exchanged nonces to transport another key value. Our contribution is to give a rigorous model and analysis of the security of such embedded key exchanges, requiring that the stealth key remains secure even if the regular key is under adversarial control. Specifically for our stealth version of the TLS 1.3 protocol we show that this extra key is secure in this setting under the common assumptions about the TLS protocol.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623099"}, {"primary_key": "1101639", "vector": [], "sparse_vector": [], "title": "Chipmunk: Better Synchronized Multi-Signatures from Lattices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Multi-signatures allow for compressing many signatures for the same message that were generated under independent keys into one small aggregated signature. This primitive is particularly useful for proof-of-stake blockchains, like Ethereum, where the same block is signed by many signers, who vouch for the block's validity. Being able to compress all signatures for the same block into a short string significantly reduces the on-chain storage costs, which is an important efficiency metric for blockchains.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623219"}, {"primary_key": "1101640", "vector": [], "sparse_vector": [], "title": "Point Cloud Analysis for ML-Based Malicious Traffic Detection: Reducing Majorities of False Positive Alarms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As an emerging security paradigm, machine learning (ML) based malicious traffic detection is an essential part of automatic defense against network attacks. Powered by dedicated traffic features, the ML based methods can detect various sophisticated attacks, in particular capturing zero-day attacks, which cannot be achieved by the traditional non-ML methods. However, false positive alarms raised by these advanced ML methods become the major obstacle to real-world deployment. These methods require experts to manually analyze false positives, which incurs significant labor costs. Thus, it is vital that we can reduce such false positives without heavyweight manual investigations.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616631"}, {"primary_key": "1101641", "vector": [], "sparse_vector": [], "title": "SysXCHG: Refining Privilege with Adaptive System Call Filters.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present the design, implementation, and evaluation of SysXCHG: a system call (syscall) filtering enforcement mechanism that enables programs to run in accordance with the principle of least privilege. In contrast to the current, hierarchical design of seccomp-BPF, which does not allow a program to run with a different set of allowed syscalls than its descendants, SysXCHG enables applications to run with \"tight\" syscall filters, uninfluenced by any future-executed (sub-)programs, by allowing filters to be dynamically exchanged at runtime during execve[at]. As a part of SysXCHG, we also present xfilter: a mechanism for fast filtering using a process-specific view of the kernel's syscall table where filtering is performed. In our evaluation of SysXCHG, we found that our filter exchanging design is performant, incurring ≤= 1.71% slowdown on real-world programs in the PaSH benchmark suite, as well as effective, blocking vast amounts of extraneous functionality, including security-critical syscalls, which the current design of seccomp-BPF is unable to.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623137"}, {"primary_key": "1101642", "vector": [], "sparse_vector": [], "title": "Experimenting with Zero-Knowledge Proofs of Training.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "How can a model owner prove they trained their model according to the correct specification? More importantly, how can they do so while preserving the privacy of the underlying dataset and the final model? We study this problem and formulate the notion of zero-knowledge proof of training (zkPoT), which formalizes rigorous security guarantees that should be achieved by a privacy-preserving proof of training. While it is theoretically possible to design zkPoT for any model using generic zero-knowledge proof systems, this approach results in extremely unpractical proof generation times. Towards designing a practical solution, we propose the idea of combining techniques from MPC-in-the-head and zkSNARKs literature to strike an appropriate trade-off between proof size and proof computation time. We instantiate this idea and propose a concretely efficient, novel zkPoT protocol for logistic regression.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623202"}, {"primary_key": "1101643", "vector": [], "sparse_vector": [], "title": "Fait Accompli Committee Selection: Improving the Size-Security Tradeoff of Stake-Based Committees.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of committee selection in the context of proof-of-stake consensus mechanisms or distributed ledgers. These settings determine a family of participating parties---each of which has been assigned a non-negative ''stake''---and are subject to an adversary that may corrupt a subset of the parties. The challenge is to select a committee of participants that accurately reflects the proportion of corrupt and honest parties, as measured by stake, in the full population. The trade-off between committee size and the probability of selecting a committee that over-represents the corrupt parties is a fundamental factor in both security and efficiency of proof-of-stake consensus, as well as committee-run layer-two protocols.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623194"}, {"primary_key": "1101644", "vector": [], "sparse_vector": [], "title": "Accio: Variable-Amount, Optimized-Unlinkable and NIZK-Free Off-Chain Payments via Hubs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Payment channel hubs (PCHs) serve as a promising solution to achieving quick off-chain payments between pairs of users. They work by using an untrusted tumbler to relay the payments between the payer and payee and enjoy the advantages of low cost and high scalability. However, the most recent privacy-preserving payment channel hub solution that supports variable payment amounts suffers from limited unlinkability, e.g., being vulnerable to the abort attack. Moreover, this solution utilizes zero-knowledge proofs, which bring huge costs on both computation time and communication overhead. Therefore, how to design PCHs that support variable amount payments and unlinkability, but reduce the use of huge-cost cryptographic tools as much as possible, is significant for the large-scale practical applications of off-chain payments.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616577"}, {"primary_key": "1101645", "vector": [], "sparse_vector": [], "title": "A Systematic Evaluation of Automated Tools for Side-Channel Vulnerabilities Detection in Cryptographic Libraries.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Clé<PERSON><PERSON> Maurice"], "summary": "To protect cryptographic implementations from side-channel vulnerabilities, developers must adopt constant-time programming practices. As these can be error-prone, many side-channel detection tools have been proposed. Despite this, such vulnerabilities are still manually found in cryptographic libraries. While a recent paper by <PERSON><PERSON> et al. shows that developers rarely perform side-channel detection, it is unclear if existing detection tools could have found these vulnerabilities in the first place.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623112"}, {"primary_key": "1101646", "vector": [], "sparse_vector": [], "title": "Sharing Communities: The Good, the Bad, and the Ugly.", "authors": ["<PERSON>", "<PERSON>"], "summary": "There are many mysteries surrounding sharing communities, mainly due to their hidden workings and the complexity of joining. Nevertheless, these communities are critical to the security ecosystem, so a more profound understanding is necessary. In addition, they face challenges such as building trust, communicating effectively, and addressing social problems.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623144"}, {"primary_key": "1101647", "vector": [], "sparse_vector": [], "title": "Read Between the Lines: Detecting Tracking JavaScript with Bytecode Classification.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Browsers and extensions that aim to block online ads and tracking scripts predominantly rely on rules from filter lists for determining which resource requests must be blocked. These filter lists are often manually curated by a community of online users. However, due to the arms race between blockers and ad-supported websites, these rules must continuously get updated so as to adapt to novel bypassing techniques and modified requests, thus rendering the detection and rule-generation process cumbersome and reactive (which can result in major delays between propagation and detection). In this paper, we address the detection problem by proposing an automated pipeline that detects tracking and advertisement JavaScript resources with high accuracy, designed to incur minimal false positives and overhead. Our method models script detection as a text classification problem, where JavaScript resources are documents containing bytecode sequences. Since bytecode is directly obtained from the JavaScript interpreter, our technique is resilient against commonly used bypassing methods, such as URL randomization or code obfuscation. We experiment with both deep learning and traditional ML-based approaches for bytecode classification and show that our approach identifies ad/tracking scripts with 97.08% accuracy, significantly outperforming cutting-edge systems in terms of both precision and the level of required features. Our experimental analysis further highlights our system's capabilities, by demonstrating how it can augment filter lists by uncovering ad/tracking scripts that are currently unknown, as well as proactively detecting scripts that have been erroneously added by list curators.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616637"}, {"primary_key": "1101649", "vector": [], "sparse_vector": [], "title": "Efficient Registration-Based Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Registration-based encryption (RBE) was recently introduced as an alternative to identity-based encryption (IBE), to resolve the key-escrow problem: In RBE, the trusted authority is substituted with a weaker entity, called the key curator, who has no knowledge of any secret key. Users generate keys on their own and then publicly register their identities and their corresponding public keys to the key curator. RBE is a promising alternative to IBE, retaining many of its advantages while removing the key-escrow problem, the major drawback of IBE. Unfortunately, all existing constructions of RBE use cryptographic schemes in a non black-box way, which makes them prohibitively expensive. It has been estimated that the size of an RBE ciphertext would be in the order of terabytes (though no RBE has even been implemented).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616596"}, {"primary_key": "1101650", "vector": [], "sparse_vector": [], "title": "Poster: Using CodeQL to Detect Malware in npm.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Malicious packages are a problem on npm, but like other malware, they are rarely completely novel and share large semantic similarities. We propose to leverage the existing static analysis framework CodeQL to find malware on npm; but instead of detecting variants of vulnerabilities, we use it to detect variants of malware. We present a methodology for writing queries from recently reported packages, as a way of defining semantic signature for specific malicious behavior, where a single one can then be used to match entire families of malware. An iteration of our approach resulted in the discovery of 125 malicious packages from the registry, without producing a single false alarm.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624401"}, {"primary_key": "1101651", "vector": [], "sparse_vector": [], "title": "Poster: Computing the Persistent Homology of Encrypted Data.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Topological Data Analysis (TDA) offers a suite of computational tools that provide quantified shape features of high dimensional data that can be used by modern statistical and predictive machine learning (ML) models. Persistent homology (PH) transforms data (e.g., point clouds, images, time series) into persistence diagrams (PDs)--compact representations of its latent topological structures. Because PDs enjoy inherent noise tolerance, are interpretable, provide a solid basis for data analysis, and can be made compatible with the expansive set of well-established ML model architectures, PH has been widely adopted for model development including on sensitive data. Thus, TDA should be incorporated into secure end-to-end data analysis pipelines. This paper introduces a version of the fundamental algorithm to compute PH on encrypted data using homomorphic encryption (HE).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624406"}, {"primary_key": "1101652", "vector": [], "sparse_vector": [], "title": "Cybercrime Bitcoin Revenue Estimations: Quantifying the Impact of Methodology and Coverage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Multiple works have leveraged the public Bitcoin ledger to estimate the revenue cybercriminals obtain from their victims. Estimations focusing on the same target often do not agree, due to the use of different methodologies, seed addresses, and time periods. These factors make it challenging to understand the impact of their methodological differences. Furthermore, they underestimate the revenue due to the (lack of) coverage on the target's payment addresses, but how large this impact remains unknown.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623094"}, {"primary_key": "1101653", "vector": [], "sparse_vector": [], "title": "Linear Communication in Malicious Majority MPC.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The SPDZ multiparty computation protocol \\citeC:DPSZ12 allows n parties to securely compute arithmetic circuits over a finite field, while tolerating up to n-1 active corruptions. A line of work building upon SPDZ has made considerable improvement to the protocol's performance, typically focusing on concrete efficiency. However, the communication complexity of each of these protocols is Ømega(n^2|C|).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623162"}, {"primary_key": "1101654", "vector": [], "sparse_vector": [], "title": "Demo: Image Disguising for Scalable GPU-accelerated Confidential Deep Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning training involves large training data and expensive model tweaking, for which cloud GPU resources can be a popular option. However, outsourcing data often raises privacy concerns. The challenge is to preserve data and model confidentiality without sacrificing GPU-based scalable training and low-cost client-side preprocessing, which is difficult for conventional cryptographic solutions to achieve. This demonstration shows a new approach, image disguising, represented by recent work: DisguisedNets, NeuraCrypt, and InstaHide, which aim to securely transform training images while still enabling the desired scalability and efficiency. We present an interactive system for visually and comparatively exploring these methods. Users can view disguised images, note low client-side processing costs, and observe the maintained efficiency and model quality during server-side GPU-accelerated training. This demo aids researchers and practitioners in swiftly grasping the advantages and limitations of image-disguising methods.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624364"}, {"primary_key": "1101655", "vector": [], "sparse_vector": [], "title": "Detecting Violations of Differential Privacy for Quantum Algorithms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quantum algorithms for solving a wide range of practical problems have been proposed in the last ten years, such as data search and analysis, product recommendation, and credit scoring. The concern about privacy and other ethical issues in quantum computing naturally rises up. In this paper, we define a formal framework for detecting violations of differential privacy for quantum algorithms. A detection algorithm is developed to verify whether a (noisy) quantum algorithm is differentially private and automatically generate bugging information when the violation of differential privacy is reported. The information consists of a pair of quantum states that violate the privacy, to illustrate the cause of the violation. Our algorithm is equipped with Tensor Networks, a highly efficient data structure, and executed both on TensorFlow Quantum and TorchQuantum which are the quantum extensions of famous machine learning platforms -- TensorFlow and PyTorch, respectively. The effectiveness and efficiency of our algorithm are confirmed by the experimental results of almost all types of quantum algorithms already implemented on realistic quantum computers, including quantum supremacy algorithms (beyond the capability of classical algorithms), quantum machine learning models, quantum approximate optimization algorithms, and variational quantum eigensolvers with up to 21 quantum bits.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623108"}, {"primary_key": "1101656", "vector": [], "sparse_vector": [], "title": "Splice: Efficiently Removing a User&apos;s Data from In-memory Application State.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Splice is a new programming framework that allows security-conscious applications to efficiently locate and delete a user's in-memory state. The core technical challenge is determining how to delete a user's memory values without breaking application-specific semantic invariants involving the memory state of remaining users. Splice solves this problem using three techniques: taint tracking (which traces how a user's data flows through memory), deletion by synthesis (which overwrites each user-owned memory value in place, replacing it with a value that preserves the symbolic constraints of enclosing data structures), and a novel type system (which forces applications to employ defensive programming to avoid computing over synthesize-deleted values in unsafe ways). Using four realistic applications that we ported to Splice, we show that Splice's type system and defensive programming requirements are not onerous for developers. We also demonstrate that Splice's run-time overheads are similar to those of prior taint tracking systems, while enabling strong deletion semantics.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623070"}, {"primary_key": "1101657", "vector": [], "sparse_vector": [], "title": "Concurrent Composition for Interactive Differential Privacy with Adaptive Privacy-Loss Parameters.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we study the concurrent composition of interactive mechanisms with adaptively chosen privacy-loss parameters. In this setting, the adversary can interleave queries to existing interactive mechanisms, as well as create new ones. We prove that every valid privacy filter and odometer for noninteractive mechanisms extends to the concurrent composition of interactive mechanisms if privacy loss is measured using (ε, δ)-DP, ƒ-DP, or Rényi DP of fixed order. Our results offer strong theoretical foundations for enabling full adaptivity in composing differentially private interactive mechanisms, showing that concurrency does not affect the privacy guarantees. We also provide an implementation for users to deploy in practice.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623128"}, {"primary_key": "1101658", "vector": [], "sparse_vector": [], "title": "You Call This Archaeology? Evaluating Web Archives for Reproducible Web Security Measurements.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Given the dynamic nature of the Web, security measurements on it suffer from reproducibility issues. In this paper we take a systematic look into the potential of using web archives for web security measurements. We first evaluate an extensive set of web archives as potential sources of archival data, showing the superiority of the Internet Archive with respect to its competitors. We then assess the appropriateness of the Internet Archive for historical web security measurements, detecting subtleties and possible pitfalls in its adoption. Finally, we investigate the feasibility of using the Internet Archive to simulate live security measurements, using recent archival data in place of live data. Our analysis shows that archive-based security measurements are a promising alternative to traditional live security measurements, which is reproducible by design; nevertheless, it also shows potential pitfalls and shortcomings of archive-based measurements. As an important contribution, we use the collected knowledge to identify insights and best practices for future archive-based security measurements.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616688"}, {"primary_key": "1101659", "vector": [], "sparse_vector": [], "title": "FINER: Enhancing State-of-the-art Classifiers with Feature Attribution to Facilitate Security Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning classifiers achieve state-of-the-art performance in various risk detection applications. They explore rich semantic representations and are supposed to automatically discover risk behaviors. However, due to the lack of transparency, the behavioral semantics cannot be conveyed to downstream security experts to reduce their heavy workload in security analysis. Although feature attribution (FA) methods can be used to explain deep learning, the underlying classifier is still blind to what behavior is suspicious, and the generated explanation cannot adapt to downstream tasks, incurring poor explanation fidelity and intelligibility.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616599"}, {"primary_key": "1101660", "vector": [], "sparse_vector": [], "title": "TxPhishScope: Towards Detecting and Understanding Transaction-based Phishing on Ethereum.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yufeng Hu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The prosperity of Ethereum attracts many users to send transactions and trade crypto assets. However, this has also given rise to a new form of transaction-based phishing scam, named TxPhish. Specifically, tempted by high profits, users are tricked into visiting fake websites and signing transactions that enable scammers to steal their crypto assets. The past year has witnessed 11 large-scale TxPhish incidents causing a total loss of more than 70 million.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623210"}, {"primary_key": "1101661", "vector": [], "sparse_vector": [], "title": "Good-looking but Lacking Faithfulness: Understanding Local Explanation Methods through Trend-based Testing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While enjoying the great achievements brought by deep learning (DL), people are also worried about the decision made by DL models, since the high degree of non-linearity of DL models makes the decision extremely difficult to understand. Consequently, attacks such as adversarial attacks are easy to carry out, but difficult to detect and explain, which has led to a boom in the research on local explanation methods for explaining model decisions. In this paper, we evaluate the faithfulness of explanation methods and find that traditional tests on faithfulness encounter the random dominance problem, \\ie, the random selection performs the best, especially for complex data. To further solve this problem, we propose three trend-based faithfulness tests and empirically demonstrate that the new trend tests can better assess faithfulness than traditional tests on image, natural language and security tasks. We implement the assessment system and evaluate ten popular explanation methods. Benefiting from the trend tests, we successfully assess the explanation methods on complex data for the first time, bringing unprecedented discoveries and inspiring future research. Downstream tasks also greatly benefit from the tests. For example, model debugging equipped with faithful explanation methods performs much better for detecting and correcting accuracy and security problems.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616605"}, {"primary_key": "1101662", "vector": [], "sparse_vector": [], "title": "When Free Tier Becomes Free to Enter: A Non-Intrusive Way to Identify Security Cameras with no Cloud Subscription.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Wireless security cameras may deter intruders. Accompanying the hardware, consumers may pay recurring monthly fees for recording videos to the cloud, or use the free tier offering motion alerts and sometimes live streams via the camera app. Many users may purchase the hardware without buying the subscription to save money, which inherently reduces their efficacy. We discover that the wireless traffic generated by a camera responding to stimulating motion may disclose whether or not video is being streamed. A malicious user such as a burglar may use such knowledge to target homes with a ''weak camera'' that does not upload video or turn on live view mode. In such cases, criminal activities would not be recorded though they are performed within the monitoring area of the camera. Accordingly, we describe a novel technique called WeakCamID that creates motion stimuli and sniffs resultant wireless traffic to infer the camera state. We perform a survey involving a total of 220 users, finding that all users think cameras have a consistent security guarantee regardless of the subscription status. Our discovery breaks such ''common sense''. We implement WeakCamID in a mobile app and experiment with 11 popular wireless cameras to show that WeakCamID can identify weak cameras with a mean accuracy of around 95% and within less than 19 seconds.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623083"}, {"primary_key": "1101663", "vector": [], "sparse_vector": [], "title": "Large Language Models for Code: Security Hardening and Adversarial Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Large language models (large LMs) are increasingly trained on massive codebases and used to generate code. However, LMs lack awareness of security and are found to frequently produce unsafe code. This work studies the security of LMs along two important axes: (i) security hardening, which aims to enhance LMs' reliability in generating secure code, and (ii) adversarial testing, which seeks to evaluate LMs' security at an adversarial standpoint. We address both of these by formulating a new security task called controlled code generation. The task is parametric and takes as input a binary property to guide the LM to generate secure or unsafe code, while preserving the LM's capability of generating functionally correct code. We propose a novel learning-based approach called SVEN to solve this task. SVEN leverages property-specific continuous vectors to guide program generation towards the given property, without modifying the LM's weights. Our training procedure optimizes these continuous vectors by enforcing specialized loss terms on different regions of code, using a high-quality dataset carefully curated by us. Our extensive evaluation shows that SVEN is highly effective in achieving strong security control. For instance, a state-of-the-art CodeGen LM with 2.7B parameters generates secure code for 59.1% of the time. When we employ SVEN to perform security hardening (or adversarial testing) on this LM, the ratio is significantly boosted to 92.3% (or degraded to 36.8%). Importantly, SVEN closely matches the original LMs in functional correctness.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623175"}, {"primary_key": "1101664", "vector": [], "sparse_vector": [], "title": "Efficient Query-Based Attack against ML-Based Android Malware Detection under Zero Knowledge Setting.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Shouling Ji"], "summary": "The widespread adoption of the Android operating system has made malicious Android applications an appealing target for attackers. Machine learning-based (ML-based) Android malware detection (AMD) methods are crucial in addressing this problem; however, their vulnerability to adversarial examples raises concerns. Current attacks against ML-based AMD methods demonstrate remarkable performance but rely on strong assumptions that may not be realistic in real-world scenarios, e.g., the knowledge requirements about feature space, model parameters, and training dataset. To address this limitation, we introduce AdvDroidZero, an efficient query-based attack framework against ML-based AMD methods that operates under the zero knowledge setting. Our extensive evaluation shows that AdvDroidZero is effective against various mainstream ML-based AMD methods, in particular, state-of-the-art such methods and real-world antivirus solutions.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623117"}, {"primary_key": "1101667", "vector": [], "sparse_vector": [], "title": "Understanding and Detecting Abused Image Hosting Modules as Malicious Services.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As a new type of underground ecosystem, the exploitation of Abused IHMs as MalIcious sErvices (AIMIEs) is becoming increasingly prevalent among miscreants to host illegal images and propagate harmful content. However, there has been little effort to understand this new menace, in terms of its magnitude, impact, and techniques, not to mention any serious effort to detect vulnerable image hosting modules on a large scale. To fulfill this gap, this paper presents the first measurement study of AIMIEs. By collecting and analyzing 89 open-sourced AIMIEs, we reveal the landscape of AIMIEs, report the evolution and evasiveness of abused image hosting APIs from reputable companies such as Alibaba, Tencent, and Bytedance, and identify real-world abused images uploaded through those AIMIEs. In addition, we propose a tool, called Viola, to detect vulnerable image hosting modules (IHMs) in the wild. We find 477 vulnerable IHM upload APIs associated with 338 web services, which integrated vulnerable IHMs, and 207 victim FQDNs. The highest-ranked domain with vulnerable web service is baidu.com, followed by bilibili.com and 163.com. We have reported abused and vulnerable IHM upload APIs and received acknowledgments from 69 of them by the time of paper submission.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623143"}, {"primary_key": "1101668", "vector": [], "sparse_vector": [], "title": "Poster: Verifiable Data Valuation with Strong Fairness in Horizontal Federated Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL) represents an innovative decentralized paradigm in the field of machine learning, which differs from traditional centralized approaches. It facilitates collaborative model training among multiple participants and transfers only model parameters without directly exchanging raw data to maintain confidentiality. Data valuation for each data provider becomes a critical issue to guarantee the fairness of federated learning by estimating the dataset quality of each data provider based on the contribution to the global model prediction performance. To value datasets in FL, the concept of Shapley value is introduced to estimate the contribution of each dataset to a trained global model by measuring the effects of including and excluding a local model parameter in various combinations of global model parameters. However, the contribution measurement to each dataset performed by an aggregator or certain central component as a verifier becomes irrational as the verifier is under the control of an organization. Thus, this work presents a contribution measurement framework or data valuation with strong fairness, where forged results from the contribution measurement procedure are impossible. The new framework allows every participant (data provider) to verify the results of contribution measurement.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624402"}, {"primary_key": "1101669", "vector": [], "sparse_vector": [], "title": "Hacksaw: Hardware-Centric Kernel Debloating via Device Inventory and Dependency Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Kernel debloating is a practical mechanism to mitigate the security problems of the operating system kernel by reducing its attack surface. Existing kernel debloating mechanisms focus on specializing a kernel to run a target application based on its dynamic traces collected in the past - they remove functions from the kernel which are not used by the application according to the traces. However, since the dynamic traces do not ensure full coverage, false removals of required functions are unavoidable. This paper proposes Hacksaw, a novel mechanism to debloat a kernel for a target machine based on its hardware device inventory. <PERSON><PERSON>aw accurately debloats a kernel without false removals because figuring out which hardware components are attached to the machine as well as which device drivers manage them is comprehensive and deterministic. Hacksaw removes not only inoperative device drivers that do not control any attached hardware components but also other kernel modules and functions which are associated with the inoperative drivers according to three dependency analysis approaches: call-graph, driver-model, and compilation-unit analyses. Our evaluation shows that <PERSON><PERSON><PERSON> effectively removes inoperative kernel modules and functions (i.e., their respective reduction ratios are 45% and 30% on average) while ensuring validity and compatibility.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623208"}, {"primary_key": "1101670", "vector": [], "sparse_vector": [], "title": "Password-Stealing without Hacking: Wi-Fi Enabled Practical Keystroke Eavesdropping.", "authors": ["Jingyang Hu", "Hong<PERSON> Wang", "<PERSON><PERSON><PERSON><PERSON>", "Jingzhi Hu", "<PERSON><PERSON>", "Hongbo Jiang", "<PERSON>"], "summary": "The contact-free sensing nature of Wi-Fi has been leveraged to achieve privacy breaches, yet existing attacks relying on Wi-Fi CSI (channel state information) demand hacking Wi-Fi hardware to obtain desired CSIs. Since such hacking has proven prohibitively hard due to compact hardware, its feasibility in keeping up with fast-developing Wi-Fi technology becomes very questionable. To this end, we propose WiKI-Eve to eavesdrop keystrokes on smartphones without the need for hacking. WiKI-Eve exploits a new feature, BFI (beamforming feedback information), offered by latest Wi-Fi hardware: since BFI is transmitted from a smartphone to an AP in clear-text, it can be overheard (hence eavesdropped) by any other Wi-Fi devices switching to monitor mode. As existing keystroke inference methods offer very limited generalizability, WiKI-Eve further innovates in an adversarial learning scheme to enable its inference generalizable towards unseen scenarios. We implement WiKI-Eve and conduct extensive evaluation on it; the results demonstrate that WiKI-Eve achieves 88.9% inference accuracy for individual keystrokes and up to 65.8% top-10 accuracy for stealing passwords of mobile applications (e.g., WeChat).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623088"}, {"primary_key": "1101672", "vector": [], "sparse_vector": [], "title": "Formalizing, Verifying and Applying ISA Security Guarantees as Universal Contracts.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Progress has recently been made on specifying instruction set architectures (ISAs) in executable formalisms rather than through prose. However, to date, those formal specifications are limited to the functional aspects of the ISA and do not cover its security guarantees. We present a novel, general method for formally specifying an ISA's security guarantees to (1) balance the needs of ISA implementations (hardware) and clients (software), (2) can be semi-automatically verified to hold for the ISA operational semantics, producing a high-assurance mechanically-verifiable proof, and (3) support informal and formal reasoning about security-critical software in the presence of adversarial code. Our method leverages universal contracts: software contracts that express bounds on the authority of arbitrary untrusted code. Universal contracts can be kept agnostic of software abstractions, and strike the right balance between requiring sufficient detail for reasoning about software and preserving implementation freedom of ISA designers and CPU implementers. We semi-automatically verify universal contracts against Sail implementations of ISA semantics using our Katamaran tool; a semi-automatic separation logic verifier for Sail which produces machine-checked proofs for successfully verified contracts. We demonstrate the generality of our method by applying it to two ISAs that offer very different security primitives: (1) MinimalCaps: a custom-built capability machine ISA and (2) a (somewhat simplified) version of RISC-V with PMP. We verify a femtokernel using the security guarantee we have formalized for RISC-V with PMP.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616602"}, {"primary_key": "1101674", "vector": [], "sparse_vector": [], "title": "Poster: Control-Flow Integrity in Low-end Embedded Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Embedded, smart, and IoT devices are increasingly popular in numerous everyday settings. Since lower-end devices have the most strict cost constraints, they tend to have few, if any, security features. This makes them attractive targets for exploits and malware. Prior research proposed various security architectures for enforcing security properties for resource-constrained devices, e.g., via Remote Attestation (RA). Such techniques can (statically) verify software integrity of a remote device and detect compromise. However, run-time (dynamic) security, e.g., via Control-Flow Integrity (CFI), is hard to achieve. This work constructs an architecture that ensures integrity of software execution against run-time attacks, such as Return-Oriented Programming (ROP). It is built atop a recently proposed CASU -- a low-cost active Root-of-Trust (RoT) that guarantees software immutability. We extend CASU to support a shadow stack and a CFI monitor to mitigate run-time attacks. This gives some confidence that CFI can indeed be attained even on low-end devices, with minimal hardware overhead.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624374"}, {"primary_key": "1101675", "vector": [], "sparse_vector": [], "title": "Caveat (IoT) Emptor: Towards Transparency of IoT Device Presence.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As many types of IoT devices worm their way into numerous settings and many aspects of our daily lives, awareness of their presence and functionality becomes a source of major concern. Hidden IoT devices can snoop (via sensing) on nearby unsuspecting users, and impact the environment where unaware users are present, via actuation. This prompts, respectively, privacy and security/safety issues. The dangers of hidden IoT devices have been recognized and prior research suggested some means of mitigation, mostly based on traffic analysis or using specialized hardware to uncover devices. While such approaches are partially effective, there is currently no comprehensive approach to IoT device transparency. Prompted in part by recent privacy regulations (GDPR and CCPA), this paper motivates and constructs a privacy-agile Root-of-Trust architecture for IoT devices, called PAISA: Privacy-Agile IoT Sensing and Actuation. It guarantees timely and secure announcements about IoT devices' presence and their capabilities. PAISA has two components: one on the IoT device that guarantees periodic announcements of its presence even if all device software is compromised, and the other that runs on the user device, which captures and processes announcements. Notably, PAISA requires no hardware modifications; it uses a popular off-the-shelf Trusted Execution Environment (TEE) -- ARM TrustZone. This work also comprises a fully functional (open-sourced) prototype implementation of PAISA, which includes: an IoT device that makes announcements via IEEE 802.11 WiFi beacons and an Android smartphone-based app that captures and processes announcements. Both security and performance of PAISA design and prototype are discussed.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623089"}, {"primary_key": "1101676", "vector": [], "sparse_vector": [], "title": "Evading Watermark based Detection of AI-Generated Content.", "authors": ["Zheng<PERSON> Jiang", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A generative AI model can generate extremely realistic-looking content, posing growing challenges to the authenticity of information. To address the challenges, watermark has been leveraged to detect AI-generated content. Specifically, a watermark is embedded into an AI-generated content before it is released. A content is detected as AI-generated if a similar watermark can be decoded from it. In this work, we perform a systematic study on the robustness of such watermark-based AI-generated content detection. We focus on AI-generated images. Our work shows that an attacker can post-process a watermarked image via adding a small, human-imperceptible perturbation to it, such that the post-processed image evades detection while maintaining its visual quality. We show the effectiveness of our attack both theoretically and empirically. Moreover, to evade detection, our adversarial post-processing method adds much smaller perturbations to AI-generated images and thus better maintain their visual quality than existing popular post-processing methods such as JPEG compression, Gaussian blur, and Brightness/Contrast. Our work shows the insufficiency of existing watermark-based detection of AI-generated content, highlighting the urgent needs of new methods. Our code is publicly available: https://github.com/zhengyuan-jiang/WEvade.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623189"}, {"primary_key": "1101677", "vector": [], "sparse_vector": [], "title": "Transformer-based Model for Multi-tab Website Fingerprinting Attack.", "authors": ["<PERSON><PERSON>", "Tianbo Lu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While the anonymous communication system Tor can protect user privacy, website fingerprinting (WF) attackers can still identify the websites that users access over encrypted network connections by analyzing the metadata generated during network communication. Despite the emergence of new WF attack techniques in recent years, most research in this area has focused on pure traffic traces generated from single-tab browsing behavior. However, multi-tab browsing behavior significantly degrades the performance of WF classification models based on the single-tab assumption. As a result, some research has shifted its focus to multi-tab WF attacks, although most of these works have limited utilization of the mixed information contained in multi-tab traces. In this paper, we propose an end-to-end multi-tab WF attack model, called Transformer-based model for Multi-tab Website Fingerprinting attack (TMWF). Inspired by object detection algorithms in computer vision, we treat multi-tab WF recognition as a problem of predicting ordered sets with a maximum length. By adding enough single-tab queries to the detection model and letting each query extract WF features from different positions in the multi-tab traces, our model's Transformer architecture capitalizes more fully on trace features. Paired with our new proposed model training approach, we accomplish adaptive recognition of multi-tab traces with varying numbers of web pages. This approach successfully eliminates a strong and unrealistic assumption in the field of multi-tab WF attacks - that the number of tabs contained in a sample belongs to the attacker's prior knowledge. Experimental results in various scenarios demonstrate that the performance of TMWF is significantly better than existing multi-tab WF attack models. To evaluate model performance in more authentic scenarios, we present a dataset of multi-tab trace data collected from real open-world environments.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623107"}, {"primary_key": "1101679", "vector": [], "sparse_vector": [], "title": "Protecting HRP UWB Ranging System Against Distance Reduction Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Jeong", "<PERSON><PERSON><PERSON>"], "summary": "Ultra-wideband (UWB) communication is an emerging technology that enables secure ranging and localization. Since UWB communication enables measuring an exact distance, enhanced security would be expected based on it. Recently, however, it has been demonstrated that a distance measured by IEEE 802.15.4z high-rate pulse repetition frequency (HRP) UWB ranging system can be maliciously reduced. The HRP UWB ranging system is widely adopted by smartphone manufacturers such as Samsung and Apple.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623145"}, {"primary_key": "1101681", "vector": [], "sparse_vector": [], "title": "Privacy in the Age of Neurotechnology: Investigating Public Attitudes towards Brain Data Collection and Use.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Brain Computer Interfaces (BCIs) are expanding beyond the medical realm into entertainment, wellness, and marketing. However, as consumer neurotechnology becomes more popular, privacy concerns arise due to the sensitive nature of brainwave data and its potential commodification. Attacks on privacy have been demonstrated and AI advancements in brain-to-speech and brain-to-image decoding pose a new unique set of risks. In this space, we contribute with the first user study (n=287) to understand people's neuroprivacy expectations and awareness of neurotechnology implications. Our analysis shows that, while users are interested in the technology, privacy is a critical issue for acceptability. The results underscore the importance of consent and the need for implementing effective transparency about neurodata sharing. Our insights provide a ground to analyse the gap in current privacy protection mechanisms, adding to the debate on how to design privacy-respecting neurotechnology.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623164"}, {"primary_key": "1101682", "vector": [], "sparse_vector": [], "title": "Comprehension from Chaos: Towards Informed Consent for Private Computation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> U<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Private computation, which includes techniques like multi-party computation and private query execution, holds great promise for enabling organizations to analyze data they and their partners hold while maintaining data subjects' privacy. Despite recent interest in communicating about differential privacy, end users' perspectives on private computation have not previously been studied. To fill this gap, we conducted 22 semi-structured interviews investigating users' understanding of, and expectations for, private computation over data about them. Interviews centered on four concrete data-analysis scenarios (e.g., ad conversion analysis), each with a variant that did not use private computation and another that did. While participants struggled with abstract definitions of private computation, they found the concrete scenarios enlightening and plausible even though we did not explain the complex cryptographic underpinnings. Private computation increased participants' acceptance of data sharing, but not unconditionally; the purpose of data sharing and analysis was the primary driver of their attitudes. Through collective activities, participants emphasized the importance of detailing the purpose of a computation and clarifying that inputs to private computation are not shared across organizations when describing private computation to end users.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623152"}, {"primary_key": "1101683", "vector": [], "sparse_vector": [], "title": "Poster: Longitudinal Analysis of DoS Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Denial-of-Service (DoS) attacks have become a regular occurrence in the digital world of today. Easy-to-use attack software via download and botnet services that can be rented cheaply in the darknet enable adversaries to conduct such attacks without requiring a comprehensive knowledge of the techniques.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624382"}, {"primary_key": "1101684", "vector": [], "sparse_vector": [], "title": "Prediction Privacy in Distributed Multi-Exit Neural Networks: Vulnerabilities and Solutions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Distributed Multi-exit Neural Networks (MeNNs) use partitioning and early exits to reduce the cost of neural network inference on low-power sensing systems. Existing MeNNs exhibit high inference accuracy using policies that select when to exit based on data-dependent prediction confidence. This paper presents a side-channel attack against distributed MeNNs employing data-dependent early exit policies. We find that an adversary can observe when a distributed MeNN exits early using encrypted communication patterns. An adversary can then use these observations to discover the MeNN's predictions with over 1.85× the accuracy of random guessing. In some cases, the side-channel leaks over 80% of the model's predictions. This leakage occurs because prior policies make decisions using a single threshold on varying prediction confidence distributions. We address this problem through two new exit policies. The first method, Per-Class Exiting (PCE), uses multiple thresholds to balance exit rates across predicted classes. This policy retains high accuracy and lowers prediction leakage, but we prove it has no privacy guarantees. We obtain these guarantees with a second policy, Confidence-Guided Randomness (CGR), which randomly selects when to exit using probabilities biased toward PCE's decisions. CGR provides statistically equivalent privacy with consistently higher inference accuracy than exiting early uniformly at random. Both PCE and CGR have low overhead, making them viable security solutions in resource-constrained settings.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623069"}, {"primary_key": "1101685", "vector": [], "sparse_vector": [], "title": "Concurrent Security of Anonymous Credentials Light, Revisited.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We revisit the concurrent security guarantees of the well-known Anonymous Credentials Light (ACL) scheme (<PERSON>ld<PERSON><PERSON><PERSON> and <PERSON><PERSON>, CCS'13). This scheme was originally proven secure when executed sequentially, and its concurrent security was left as an open problem. A later work of <PERSON><PERSON><PERSON><PERSON> et al. (EUROCRYPT'21) gave an efficient attack on ACL when executed concurrently, seemingly resolving this question once and for all.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623184"}, {"primary_key": "1101686", "vector": [], "sparse_vector": [], "title": "FlexiRand: Output Private (Distributed) VRFs and Application to Blockchains.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Web3 applications based on blockchains regularly need access to randomness that is unbiased, unpredictable, and publicly verifiable. For Web3 gaming applications, this becomes a crucial selling point to attract more users by providing credibility to the \"random reward\" distribution feature. A verifiable random function (VRF) protocol satisfies these requirements naturally, and there is a tremendous rise in the use of VRF services. As most blockchains cannot maintain the secret keys required for VRFs, Web3 applications interact with external VRF services via a smart contract where a VRF output is exchanged for a fee. While this smart contract-based plain-text exchange offers the much-needed public verifiability immediately, it severely limits the way the requester can employ the VRF service: the requests cannot be made in advance, and the output cannot be reused. This introduces significant latency and monetary overhead.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616601"}, {"primary_key": "1101687", "vector": [], "sparse_vector": [], "title": "Themis: Fast, Strong Order-Fairness in Byzantine Consensus.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce Themis, a scheme for introducing fair ordering of transactions into (permissioned) Byzantine consensus protocols with at most ƒ faulty nodes among n ≥ 4ƒ + 1. Themis enforces the strongest notion of fair ordering proposed to date. It also achieves standard liveness, rather than the weaker notion of previous work with the same fair ordering property.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616658"}, {"primary_key": "1101689", "vector": [], "sparse_vector": [], "title": "AIM: Symmetric Primitive for Shorter Signatures with Stronger Security.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Jincheol Ha", "Mincheol Son", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Post-quantum signature schemes based on the MPC-in-the-Head (MPCitH) paradigm are recently attracting significant attention as their security solely depends on the one-wayness of the underlying primitive, providing diversity for the hardness assumption in post-quantum cryptography. Recent MPCitH-friendly ciphers have been designed using simple algebraic S-boxes operating on a large field in order to improve the performance of the resulting signature schemes. Due to their simple algebraic structures, their security against algebraic attacks should be comprehensively studied.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616579"}, {"primary_key": "1101690", "vector": [], "sparse_vector": [], "title": "General Data Protection Runtime: Enforcing Transparent GDPR Compliance for Existing Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent advances in data protection regulations brings privacy benefits for website users, but also comes at a cost for operators. Retrofitting the privacy requirements of laws such as the General Data Protection Regulation (GDPR) onto legacy software requires significant auditing and development effort. In this work we demonstrate that this effort can be minimized by viewing data protection requirements through the lens of information flow tracking. Instead of manual inspections of applications, we propose a lightweight enforcement engine which can reliably prevent unlawful data processing even in the presence of bugs or misconfigured software. Taking GDPR regulations as a starting point, we define twelve software requirements which, if implemented properly, ensure adequate handling of personal data. We go on to show how these requirements can be fulfilled by proposing a metadata structure and enforcement policies for dynamic information flow tracking frameworks. To put this idea into practice, we present Fontus, a Java Virtual Machine (JVM) information flow tracking framework, which can transparently label personal data in existing Java applications in order to aid compliance with data protection regulations. Finally, we demonstrate the applicability of our approach by enforcing data protection polices across 7 large, open source web applications, with no changes required to the applications themselves.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616604"}, {"primary_key": "1101691", "vector": [], "sparse_vector": [], "title": "&quot;Make Them Change it Every Week!&quot;: A Qualitative Exploration of Online Developer Advice on Usable and Secure Authentication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Usable and secure authentication on the web and beyond is mission-critical. While password-based authentication is still widespread, users have trouble dealing with potentially hundreds of online accounts and their passwords. Alternatives or extensions such as multi-factor authentication have their own challenges and find only limited adoption. Finding the right balance between security and usability is challenging for developers. Previous work found that developers use online resources to inform security decisions when writing code. Similar to other areas, lots of authentication advice for developers is available online, including blog posts, discussions on Stack Overflow, research papers, or guidelines by institutions like OWASP or NIST.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623072"}, {"primary_key": "1101692", "vector": [], "sparse_vector": [], "title": "WPES &apos;23: 22nd Workshop on Privacy in the Electronic Society.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "These proceedings contain the papers selected for inclusion in the technical program for the 22st ACM Workshop on Privacy in the Electronic Society (ACM WPES 2023), held in conjunction with the 30th ACM Conference on Computer and Communication Security (ACM CCS 2023) at the Tivoli Congress Center in Copenhagen, Denmark, on November 26, 2023. In response to the workshop's call for papers, 31 valid submissions were received, including 21 full paper submissions and 10 short paper submissions. They were evaluated by a technical program committee consisting of 54 researchers whose backgrounds include a diverse set of topics related to privacy. Each paper was reviewed by at least 3 members of the program committee. Papers were evaluated based on their importance, novelty, and technical quality. After the rigorous review process, 9 submissions were accepted as full papers (acceptance rate: 29.0%) and an additional 8 submissions were accepted as short papers.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624019"}, {"primary_key": "1101693", "vector": [], "sparse_vector": [], "title": "Poster: The Risk of Insufficient Isolation of Database Transactions in Web Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Web applications utilizing databases for persistence frequently expose security flaws due to race conditions. The commonly accepted remedy to this problem is to envelope related database operations in transactions. Unfortunately, sole trust in transactions to isolate competing sets of database interactions is often misplaced. While the precise isolation properties of transactions depend on the configuration of the database management system (DBMS), the default configuration of common DBMS exposes transactions to anomalies that render their protection worthless.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624394"}, {"primary_key": "1101694", "vector": [], "sparse_vector": [], "title": "MESAS: Poisoning Defense for Federated Learning Resilient against Adaptive Attackers.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Federated Learning (FL) enhances decentralized machine learning by safeguarding data privacy, reducing communication costs, and improving model performance with diverse data sources. However, FL faces vulnerabilities such as untargeted poisoning attacks and targeted backdoor attacks, posing challenges to model integrity and security. Preventing backdoors proves especially challenging due to their stealthy nature. Existing mitigation techniques have shown efficacy but often overlook realistic adversaries and diverse data distributions.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623212"}, {"primary_key": "1101695", "vector": [], "sparse_vector": [], "title": "Evaluating the Security Posture of Real-World FIDO2 Deployments.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "FIDO2 is a suite of protocols that combines the usability of local authentication (e.g., biometrics) with the security of public-key cryptography to deliver passwordless authentication. It eliminates shared authentication secrets (i.e., passwords, which could be leaked or phished) and provides strong security guarantees assuming the benign behavior of the client-side protocol components.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623063"}, {"primary_key": "1101697", "vector": [], "sparse_vector": [], "title": "A Thorough Evaluation of RAMBAM.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The application of masking, widely regarded as the most robust and reliable countermeasure against Side-Channel Analysis~(SCA) attacks, has been the subject of extensive research across a range of cryptographic algorithms, especially AES. However, the implementation cost associated with applying such a countermeasure can be significant and even in some scenarios infeasible due to considerations such as area and latency overheads, as well as the need for fresh randomness to ensure the security properties of the resulting design. Most of these overheads originate from the ability to maintain security in the presence of physical defaults such as glitches and transitions. Among several schemes with a trade-off between such overheads, RAMBAM, presented at CHES~2022, offers an ultra-low latency in terms of the number of clock cycles. It is dedicated to the AES and utilizes redundant representations of the finite field elements to enhance protection against both passive and active physical attacks.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623190"}, {"primary_key": "1101699", "vector": [], "sparse_vector": [], "title": "Simplifying Mixed Boolean-Arithmetic Obfuscation by Program Synthesis and Term Rewriting.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mixed Boolean Arithmetic (MBA) obfuscation transforms a program expression into an equivalent but complex expression that is hard to understand. MBA obfuscation has been popular to protect programs from reverse engineering thanks to its simplicity and effectiveness. However, it is also used for evading malware detection, necessitating the development of effective MBA deobfuscation techniques. Existing deobfuscation methods suffer from either of the four limitations: (1) lack of general applicability, (2) lack of flexibility, (3) lack of scalability, and (4) lack of correctness. In this paper, we propose a versatile MBA deobfuscation method that synergistically combines program synthesis, term rewriting, and an algebraic simplification method. The key novelty of our approach is that we perform on-the-fly learning of transformation rules for deobfuscation, and apply them to rewrite the input MBA expression. We implement our method in a tool called ProMBA and evaluate it on over 4000 MBA expressions obfuscated by the state-of-the-art obfuscation tools. Experimental results show that our method outperforms the state-of-the-art MBA deobfuscation tools by a large margin, successfully simplifying a vast majority of the obfuscated expressions into their original forms.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623186"}, {"primary_key": "1101700", "vector": [], "sparse_vector": [], "title": "AdCPG: Classifying JavaScript Code Property Graphs with Explanations for Ad and Tracker Blocking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Advertising and tracking service (ATS) blocking has been safeguarding the privacy of millions of Internet users from privacy-invasive tracking behaviors. Previous research has proposed using a graph representation that models the structural relationships in loading web resources and then conducting ATS node classification based on this graph representation. However, these context-based ATS classification methods suffer from (1) inconsistent classification due to the varying context in which ATS resources are loaded and (2) a lack of explainability of the classification results, making it difficult to identify the code-level causes for ATS classification.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623084"}, {"primary_key": "1101701", "vector": [], "sparse_vector": [], "title": "Put Your Memory in Order: Efficient Domain-based Memory Isolation for WASM Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Memory corruption vulnerabilities can have more serious consequences in WebAssembly than in native applications. Therefore, we present \\tool, the first WebAssembly runtime with memory isolation. Our insight is to use MPK hardware for efficient memory protection in WebAssembly. However, MPK and WebAssembly have different memory models: MPK protects virtual memory pages, while WebAssembly uses linear memory that has no pages. Mapping MPK APIs to WebAssembly causes memory bloating and low running efficiency. To solve this, we propose \\acfdilm, which protects linear memory at function-level granularity. We implemented \\acdilm into the official WebAssembly runtime to build \\tool. Our evaluation shows that \\tool can prevent memory corruption in real projects with a 1.77% average overhead and negligible memory cost.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623205"}, {"primary_key": "1101702", "vector": [], "sparse_vector": [], "title": "ELEKTRA: Efficient Lightweight multi-dEvice Key TRAnsparency.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Key Transparency (KT) systems enable service providers of end-to-end encrypted communication (E2EE) platforms to maintain a Verifiable Key Directory (VKD) that maps each user's identifier, such as a username or email address, to their identity public key(s). Users periodically monitor the directory to ensure their own identifier maps to the correct keys, thus detecting any attempt to register a fake key on their behalf to Meddler-in-the-Middle (MitM) their communications.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623161"}, {"primary_key": "1101703", "vector": [], "sparse_vector": [], "title": "Poster: Privacy Risks from Misconfigured Android Content Providers.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Android applications record and process personal user data, and they can share it among each other throughcontent providers. While the access is protected through multiple mechanisms, unintentional misconfigurations can allow an attacker to access or modify private application data. In this work, we study how content providers protect private data in a systematic study on 14.4 million Android apps. We identify potentially vulnerable apps by using static analysis to successively reduce the set of target apps. Using a custom attack app, we can confirm data leakage in practice and successfully access privacy-sensitive information. We conclude that this points to an inherent problem in designing secure Android applications and discuss possible mitigations.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624389"}, {"primary_key": "1101704", "vector": [], "sparse_vector": [], "title": "COMBINE: COMpilation and Backend-INdependent vEctorization for Multi-Party Computation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent years have witnessed significant advances in programming technology for multi-party computation (MPC), bringing MPC closer to practice and wider applicability. Typical MPC programming frameworks focus on either front-end language design (e.g., Wysteria, Viaduct, SPDZ), or back-end protocol design and implementation (e.g., ABY, MOTION, MP-SPDZ).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623181"}, {"primary_key": "1101705", "vector": [], "sparse_vector": [], "title": "PackGenome: Automatically Generating Robust YARA Rules for Accurate Malware Packer Detection.", "authors": ["<PERSON><PERSON><PERSON>", "Jiang <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen", "Lan<PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Binary packing, a widely-used program obfuscation style, compresses or encrypts the original program and then recovers it at runtime. Packed malware samples are pervasive---they conceal arresting code features as unintelligible data to evade detection. To rapidly respond to large-scale packed malware, security analysts search specific binary patterns to identify corresponding packers. The quality of such packer patterns or signatures is vital to malware dissection. However, existing packer signature rules severely rely on human analysts' experience. In addition to expensive manual efforts, these human-written rules (e.g., YARA) also suffer from high false positives: as they are designed to search the pattern of bytes rather than instructions, they are very likely to mismatch with unexpected instructions.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616625"}, {"primary_key": "1101706", "vector": [], "sparse_vector": [], "title": "Lost along the Way: Understanding and Mitigating Path-Misresolution Threats to Container Isolation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Filesystem isolation enforced by today's container technology has been found to be less effective in the presence of host-container interactions increasingly utilized by container tools. This weakened isolation has led to a type of path misresolution (Pamir) vulnerabilities, which have been considered to be highly risky and continuously reported over the years. In this paper, we present the first systematic study on the Pamir risk and the existing fixes to related vulnerabilities. Our research reveals that in spite of significant efforts being made to patch vulnerable container tools and address the risk, the Pamir vulnerabilities continue to be discovered, including a new vulnerability (CVE-2023-0778) we rediscovered from patched software. A key insight of our study is that the Pamir risk is inherently hard to prevent at the level of container tools, due to their heavy reliance on third-party components. While security inspections should be applied to all components to mediate host-container interactions, third-party component developers tend to believe that container tools should perform security checks before invoking their components, and are therefore reluctant to patch their code with the container-specific protection. Moreover, due to the large number of components today's container tools depend on, re-implementing all of them is impractical.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623154"}, {"primary_key": "1101708", "vector": [], "sparse_vector": [], "title": "Demystifying DeFi MEV Activities in Flashbots Bundle.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Decentralized Finance, mushrooming in permissionless blockchains, has attracted a recent surge in popularity. Due to the transparency of permissionless blockchains, opportunistic traders can compete to earn revenue by extracting Miner Extractable Value (MEV), which undermines both the consensus security and efficiency of blockchain systems. The Flashbots bundle mechanism further aggravates the MEV competition because it empowers opportunistic traders with the capability of designing more sophisticated MEV extraction. In this paper, we conduct the first systematic study on DeFi MEV activities in Flashbots bundle by developing ActLifter, a novel automated tool for accurately identifying DeFi actions in transactions of each bundle, and ActCluster, a new approach that leverages iterative clustering to facilitate us to discover known/unknown DeFi MEV activities. Extensive experimental results show that ActLifter can achieve nearly 100% precision and recall in DeFi action identification, significantly outperforming state-of-the-art techniques. Moreover, with the help of ActCluster, we obtain many new observations and discover 17 new kinds of DeFi MEV activities, which occur in 53.12% of bundles but have not been reported in existing studies.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616590"}, {"primary_key": "1101709", "vector": [], "sparse_vector": [], "title": "martFL: Enabling Utility-Driven Data Marketplace with a Robust and Verifiable Federated Learning Architecture.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The development of machine learning models requires a large amount of training data. Data marketplace is a critical platform to trade high-quality and private-domain data that is not publicly available on the Internet. However, as data privacy becomes increasingly important, directly exchanging raw data becomes inappropriate. Federated Learning (FL) is a distributed machine learning paradigm that exchanges data utilities (in form of local models or gradients) among multiple parties without directly sharing the original data. However, we recognize several key challenges in applying existing FL architectures to construct a data marketplace. (i) In existing FL architectures, the Data Acquirer (DA) cannot privately assess the quality of local models submitted by different Data Providers (DPs) prior to trading; (ii)The model aggregation protocols in existing FL designs cannot effectively exclude malicious DPs without \"overfitting'' to the DA's (possibly biased) root dataset; (iii) Prior FL designs lack a proper billing mechanism to enforce the DA to fairly allocate the reward according to contributions made by different DPs. To address above challenges, we propose martFL, the first federated learning architecture that is specifically designed to enable a secure utility-driven data marketplace. At a high level, martFL is empowered by two innovative designs: (i) a quality-aware model aggregation protocol that allows the DA to properly exclude local-quality or even poisonous local models from the aggregation, even if the DA's root dataset is biased; (ii) a verifiable data transaction protocol that enables the DA to prove, both succinctly and in zero-knowledge, that it has faithfully aggregated these local models according to the weights that the DA has committed to. This enables the DPs to unambiguously claim the rewards proportional to their weights/contributions. We implement a prototype of martFL and evaluate it extensively over various tasks. The results show that martFL can improve the model accuracy by up to 25% while saving up to 64% data acquisition cost.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623134"}, {"primary_key": "1101710", "vector": [], "sparse_vector": [], "title": "How Hard is Takeover in DPoS Blockchains? Understanding the Security of Coin-based Voting Governance.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Delegated-Proof-of-Stake (DPoS) blockchains, such as EOSIO, Steem and TRON, are governed by a committee of block producers elected via a coin-based voting system. We recently witnessed the first de facto blockchain takeover that happened between Steem and TRON. Within one hour of this incident, TRON founder took over the entire Steem committee, forcing the original Steem community to leave the blockchain that they maintained for years. This is a historical event in the evolution of blockchains and Web 3.0. Despite its significant disruptive impact, little is known about how vulnerable DPoS blockchains are in general to takeovers and the ways in which we can improve their resistance to takeovers.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623171"}, {"primary_key": "1101711", "vector": [], "sparse_vector": [], "title": "SalsaPicante: A Machine Learning Attack on LWE with Binary Secrets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Learning with Errors (LWE) is a hard math problem underpinning many proposed post-quantum cryptographic (PQC) systems. The only PQC Key Exchange Mechanism (KEM) standardized by NIST [13] is based on module LWE [2], and current publicly available PQ Homomorphic Encryption (HE) libraries are based on ring LWE. The security of LWE-based PQ cryptosystems is critical, but certain implementation choices could weaken them. One such choice is sparse binary secrets, desirable for PQ HE schemes for efficiency reasons. Prior work SALSA[51] demonstrated a machine learning-based attack on LWE with sparse binary secrets in small dimensions (n ≤ = 128) and low Hamming weights (h ≤ = 4). However, this attack assumes access to millions of eavesdropped LWE samples and fails at higher Hamming weights or dimensions.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623076"}, {"primary_key": "1101712", "vector": [], "sparse_vector": [], "title": "Learning from Limited Heterogeneous Training Data: Meta-Learning for Unsupervised Zero-Day Web Attack Detection across Web Domains.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently unsupervised machine learning based systems have been developed to detect zero-day Web attacks, which can effectively enhance existing Web Application Firewalls (WAFs). However, prior arts only consider detecting attacks on specific domains by training particular detection models for the domains. These systems require a large amount of training data, which causes a long period of time for model training and deployment. In this paper, we propose RETSINA, a novel meta-learning based framework that enables zero-day Web attack detection across different domains in an organization with limited training data. Specifically, it utilizes meta-learning to share knowledge across these domains, e.g., the relationship between HTTP requests in heterogeneous domains, to efficiently train detection models. Moreover, we develop an adaptive preprocessing module to facilitate semantic analysis of Web requests across different domains and design a multi-domain representation method to capture semantic correlations between different domains for cross-domain model training. We conduct experiments using four real-world datasets on different domains with a total of 293M Web requests. The experimental results demonstrate that RETSINA outperforms the existing unsupervised Web attack detection methods with limited training data, e.g., RETSINA needs only 5-minute training data to achieve comparable detection performance to the existing methods that train separate models for different domains using 1-day training data. We also conduct real-world deployment in an Internet company. RETSINA captures on average 126 and 218 zero-day attack requests per day in two domains, respectively, in one month.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623123"}, {"primary_key": "1101713", "vector": [], "sparse_vector": [], "title": "Protecting Intellectual Property of Large Language Model-Based Code Generation APIs via Watermarks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rise of large language model-based code generation (LLCG) has enabled various commercial services and APIs. Training LLCG models is often expensive and time-consuming, and the training data are often large-scale and even inaccessible to the public. As a result, the risk of intellectual property (IP) theft over the LLCG models (e.g., via imitation attacks) has been a serious concern. In this paper, we propose the first watermark (WM) technique to protect LLCG APIs from remote imitation attacks. Our proposed technique is based on replacing tokens in an LLCG output with their \"synonyms\" available in the programming language. A WM is thus defined as the stealthily tweaked distribution among token synonyms in LLCG outputs. We design six WM schemes (instantiated into over 30 WM passes) which rely on conceptually distinct token synonyms available in programming languages. Moreover, to check the IP of a suspicious model (decide if it is stolen from our protected LLCG API), we propose a statistical tests-based procedure that can directly check a remote, suspicious LLCG API.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623120"}, {"primary_key": "1101714", "vector": [], "sparse_vector": [], "title": "Poster: Ethics of Computer Security and Privacy Research - Trends and Standards from a Data Perspective.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Ethics is an important criterion for security research. This work presents the current status and trends that security researchers have taken to address ethical concerns in their studies from a data perspective. In particular, we created a dataset of 3,756 papers published in three top-tier conferences between 2010 and 2022, among which 963 papers were identified with ethical concerns. With this dataset, we provided answers to three questions regarding the current practices and trends: (1) What is the landscape of ethical considerations in security research? For example, how many security research projects have raised ethical concerns in their studies, and which research areas are likely to cause ethical risks and concerns? (2) What are the current practices to address these ethical risks? And (3) What are the important factors impacting the ethical awareness of researchers?", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624378"}, {"primary_key": "1101715", "vector": [], "sparse_vector": [], "title": "PyRTFuzz: Detecting Bugs in Python Runtimes via Two-Level Collaborative Fuzzing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Haipeng <PERSON>ai"], "summary": "Given the widespread use of Python and its sustaining impact, the security and reliability of the Python runtime system is highly and broadly critical. Yet with real-world bugs in Python runtimes being continuously and increasingly reported, technique/tool support for automated detection of such bugs is still largely lacking. In this paper, we present PyRTFuzz, a novel fuzzing technique/tool for holistically testing Python runtimes including the language interpreter and its runtime libraries. PyRTFuzz combines generationand mutation-based fuzzing at the compiler- and application-testing level, respectively, as enabled by static/dynamic analysis for extracting runtime API descriptions, a declarative, specification language for valid and diverse Python code generation, and a custom type-guided mutation strategy for format/structure-aware application input generation. We implemented PyRTFuzz for the primary Python implementation (CPython) and applied it to three versions of the runtime. Our experiments revealed 61 new, demonstrably exploitable bugs including those in the interpreter and most in the runtime libraries. Our results also demonstrated the promising scalability and cost-effectiveness of PyRTFuzz and its great potential for further bug discovery. The two-level collaborative fuzzing methodology instantiated in PyRTFuzz may also apply to other language runtimes especially those of interpreted languages.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623166"}, {"primary_key": "1101716", "vector": [], "sparse_vector": [], "title": "Concentrated Geo-Privacy.", "authors": ["<PERSON><PERSON>", "Ke <PERSON>"], "summary": "This paper proposes concentrated geo-privacy (CGP), a privacy notion that can be considered as the counterpart of concentrated differential privacy (CDP) for geometric data. Compared with the previous notion of geo-privacy [1,5], which is the counterpart of standard differential privacy, CGP offers many benefits including simplicity of the mechanism, lower noise scale in high dimensions, and better composability known as advanced composition. The last one is the most important, as it allows us to design complex mechanisms using smaller building blocks while achieving better utilities. To complement this result, we show that the previous notion of geo-privacy inherently does not admit advanced composition even using its approximate version. Next, we study three problems on private geometric data: the identity query, k nearest neighbors, and convex hulls. While the first problem has been previously studied, we give the first mechanisms for the latter two under geo-privacy. For all three problems, composability is essential in obtaining good utility guarantees on the privatized query answer.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623068"}, {"primary_key": "1101717", "vector": [], "sparse_vector": [], "title": "SkillScanner: Detecting Policy-Violating Voice Applications Through Static Analysis at the Development Phase.", "authors": ["<PERSON>", "<PERSON>", "Haipeng <PERSON>ai", "<PERSON><PERSON>", "Hong<PERSON> Hu"], "summary": "The Amazon Alexa marketplace is the largest Voice Personal Assistant (VPA) platform with over 100,000 voice applications (i.e., skills) published to the skills store. In an effort to maintain the quality and trustworthiness of voice-apps, Amazon Alexa has implemented a set of policy requirements to be adhered to by third-party skill developers. However, recent works reveal the prevalence of policy-violating skills in the current skills store. To understand the causes of policy violations in skills, we first conduct a user study with 34 third-party skill developers focusing on whether they are aware of the various policy requirements defined by the Amazon Alexa platform. Our user study results show that there is a notable gap between VPA's policy requirements and skill developers' practices. As a result, it is inevitable that policy-violating skills will be published.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616650"}, {"primary_key": "1101718", "vector": [], "sparse_vector": [], "title": "SaTS&apos;23: The 1st ACM Workshop on Secure and Trustworthy Superapps.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The paradigm of mobile computing has shifted with the rise of mobile super apps, encompassing diverse services within single applications. These apps, featuring \"miniapps,\" have gained popularity for their native app-like features and comprehensive ecosystems. However, this popularity has led to significant concerns about user data security and privacy. The Workshop on Secure and Trustworthy Superapps (SaTS 2023), co-hosted with ACM CCS 2023, addresses these challenges. As super apps become essential for communication, entertainment, and commerce, the workshop fosters collaboration among researchers and practitioners. By tackling these concerns, the event aims to provide insights and solutions benefiting the security community, industry, and society. SaTS 2023 aims to illuminate these issues while promoting knowledge exchange and innovative problem-solving.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624023"}, {"primary_key": "1101719", "vector": [], "sparse_vector": [], "title": "TypeSqueezer: When Static Recovery of Function Signatures for Binary Executables Meets Dynamic Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Control-Flow Integrity (CFI) is considered a promising solution in thwarting advanced code-reuse attacks. While the problem of backward-edge protection in CFI is nearly closed, effective forward-edge protection is still a major challenge. The keystone of protecting the forward edge is to resolve indirect call targets, which although can be done quite accurately using type-based solutions given the program source code, it faces difficulties when carried out at the binary level. Since the actual type information is unavailable in COTS binaries, type-based indirect call target matching typically resorts to approximate function signatures inferred using the arity and argument width of indirect callsites and calltargets. Doing so with static analysis, therefore, forces the existing solutions to assume the arity/width boundaries in a too-permissive way to defeat sophisticated attacks.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623214"}, {"primary_key": "1101720", "vector": [], "sparse_vector": [], "title": "ADEM: An Authentic Digital EMblem.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In times of armed conflict, the emblems of the red cross, red crescent, and red crystal are used to mark physical infrastructure. This enables military units to identify assets as protected under international humanitarian law to avoid attacking them. In this paper, we tackle the novel security problem of how to extend such protection to digital, network-connected infrastructure through a digital emblem. A digital emblem has a unique combination of security requirements, namely, authentication, accountability, and a property that we call covert inspection. Covert inspection states that those wishing to authenticate assets as protected must be able to do so without revealing that they may attack unprotected entities.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616578"}, {"primary_key": "1101721", "vector": [], "sparse_vector": [], "title": "DSFuzz: Detecting Deep State Bugs with Dependent State Exploration.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Traditional random mutation-based fuzzers are ineffective at reaching deep program states that require specific input values. Consequently, a large number of deep bugs remain undiscovered. To enhance the effectiveness of input mutation, previous research has utilized taint analysis to identify control-dependent critical bytes and only mutates those bytes. However, existing works do not consider indirect control dependencies, in which the critical bytes for taking one branch can only be set in a basic block that is control dependent on a series of other basic blocks. These critical bytes cannot be identified unless that series of basic blocks are visited in the execution path. Existing approaches would take an unacceptably long time and computation resources to attempt multiple paths before setting these critical bytes. In other words, the search space for identifying the critical bytes cannot be effectively explored by the current mutation strategies.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616594"}, {"primary_key": "1101722", "vector": [], "sparse_vector": [], "title": "FITS: Matching Camera Fingerprints Subject to Software Noise Pollution.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON><PERSON>", "Zhongjie Ba", "<PERSON>", "Li Lu", "<PERSON><PERSON>"], "summary": "Physically unclonable hardware fingerprints can be used for device authentication. The photo-response non-uniformity (PRNU) is the most reliable hardware fingerprint of digital cameras and can be conveniently extracted from images. However, we find image post-processing software may introduce extra noise into images. Part of this noise remains in the extracted PRNU fingerprints and is hard to be eliminated by traditional approaches, such as denoising filters. We define this noise as software noise, which pollutes PRNU fingerprints and interferes with authenticating a camera armed device. In this paper, we propose novel approaches for fingerprint matching, a critical step in device authentication, in the presence of software noise. We calculate the cross correlation between PRNU fingerprints of different cameras using a test statistic such as the Peak to Correlation Energy (PCE) so as to estimate software noise correlation. During fingerprint matching, we derive the ratio of the test statistic on two PRNU fingerprints of interest over the estimated software noise correlation. We denote this ratio as the fingerprint to software noise ratio (FITS), which allows us to detect the PRNU hardware noise correlation component in the test statistic for fingerprint matching. Extensive experiments over 10,000 images taken by more than 90 smartphones are conducted to validate our approaches, which outperform the state-of-the-art approaches significantly for polluted fingerprints. We are the first to study fingerprint matching with the existence of software noise.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616600"}, {"primary_key": "1101723", "vector": [], "sparse_vector": [], "title": "Efficient Multiparty Probabilistic Threshold Private Set Intersection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Threshold private set intersection (TPSI) allows multiple parties to learn the intersection of their input sets only if the size of the intersection is greater than a certain threshold. This task has been demonstrated useful with practical applications, and thus many active research has been conducted. However, current solutions for TPSI are still slow for large input sets e.g., n=2^20 for the set size, and the potentially practical candidates are only secure against semi-honest adversaries. For the basic PSI, there have been efficient and scalable solutions, even in the malicious settings. It is interesting to determine whether adding a threshold feature would inherently incur a large overhead to PSI.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623158"}, {"primary_key": "1101724", "vector": [], "sparse_vector": [], "title": "Poster: Bridging Trust Gaps: Data Usage Transparency in Federated Data Ecosystems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The evolving landscape of data ecosystems (DEs) increasingly demands integrated and collaborative data-sharing mechanisms that simultaneously ensure data sovereignty. However, recently proposed federated platforms, e.g., Gaia-X, only offer a promising solution to share data among already trusted participants-they still lack features to establish and maintain trust. To address this issue, we propose transparency logs for data usage that retrospectively build trust among participants. Inspired by certificate transparency logs that successfully bridge trust gaps in PKIs, we equip data owners with credible evidence of data usage. We show that our transparency logs for data usage are well scalable to sizable DEs. Thus, they are a promising approach to bridge trust gaps in federated DEs with cryptographic guarantees, fostering more robust data sharing.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624371"}, {"primary_key": "1101725", "vector": [], "sparse_vector": [], "title": "Group and Attack: Auditing Differential Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "(ε, δ) differential privacy has seen increased adoption recently, especially in private machine learning applications. While this privacy definition allows provably limiting the amount of information leaked by an algorithm, practical implementations of differentially private algorithms often contain subtle vulnerabilities. This motivates the need for effective tools that can audit (ε, δ) differential privacy algorithms before deploying them in the real world. However, existing state-of-the-art-tools for auditing (ε, δ) differential privacy directly extend the tools for ε-differential privacy by fixing either ε or δ in the violation search, inherently restricting their ability to efficiently discover violations of (ε, δ) differential privacy.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616607"}, {"primary_key": "1101726", "vector": [], "sparse_vector": [], "title": "Phoenix: Detect and Locate Resilience Issues in Blockchain via Context-Sensitive Chaos.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yu <PERSON>", "Jiaguang Sun", "Huizhong Li"], "summary": "Resilience is vital to blockchain systems and helps them automatically adapt and continue providing their service when adverse situations occur, e.g., node crashing and data discarding. However, due to the vulnerabilities in their implementation, blockchain systems may fail to recover from the error situations, resulting in permanent service disruptions. Such vulnerabilities are called resilience issues.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623071"}, {"primary_key": "1101727", "vector": [], "sparse_vector": [], "title": "Travelling the Hypervisor and SSD: A Tag-Based Approach Against Crypto Ransomware with Fine-Grained Data Recovery.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Shen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ransomware has evolved from an economic nuisance to a national security threat nowadays, which poses a significant risk to users. To address this problem, we propose RansomTag, a tag-based approach against crypto ransomware with fine-grained data recovery. Compared to state-of-the-art SSD-based solutions, RansomTag makes progress in three aspects. First, it decouples the ransomware detection functionality from the firmware of the SSD and integrates it into a lightweight hypervisor of Type I. Thus, it can leverage the powerful computing capability of the host system and the rich context information, which is introspected from the operating system, to achieve accurate detection of ransomware attacks and defense against potential targeted attacks on SSD characteristics. Further, RansomTag is readily deployed onto desktop personal computers due to its parapass-through architecture. Second, RansomTag bridges the semantic gap between the hypervisor and the SSD through the tag-based approach proposed by us. Third, RansomTag is able to keep 100% of the user data overwritten or deleted by ransomware, and restore any single or multiple user files to any versions based on timestamps. To validate our approach, we implement a prototype of RansomTag and collect 3,123 recent ransomware samples to evaluate it. The evaluation results show that our prototype effectively protects user data with minimal scale data backup and acceptable performance overhead. In addition, all the attacked files can be completely restored in fine-grained.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616665"}, {"primary_key": "1101728", "vector": [], "sparse_vector": [], "title": "Level Up: Private Non-Interactive Decision Tree Evaluation using Levelled Homomorphic Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As machine learning as a service continues gaining popularity, concerns about privacy and intellectual property arise. Users often hesitate to disclose their private information to obtain a service, while service providers aim to protect their proprietary models. Decision trees, a widely used machine learning model, are favoured for their simplicity, interpretability, and ease of training. In this context, Private Decision Tree Evaluation (PDTE) enables a server holding a private decision tree to provide predictions based on a client's private attributes. The protocol is such that the server learns nothing about the client's private attributes. Similarly, the client learns nothing about the server's model besides the prediction and some hyperparameters.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623095"}, {"primary_key": "1101729", "vector": [], "sparse_vector": [], "title": "Targeted Attack Synthesis for Smart Grid Vulnerability Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Modern smart grids utilize advanced sensors and digital communication to manage the flow of electricity from generation source to consumption points. They also employ anomaly detection units and phasor measurement units (PMUs) for security and monitoring of grid behavior. However, as smart grids are distributed, vulnerability analysis is necessary to identify and mitigate potential security threats targeting the sensors and communication links. We propose a novel algorithm that uses measurement parameters, such as power flow or load flow, to identify the smart grid's most vulnerable operating intervals. Our methodology incorporates a Monte Carlo simulation approach to identify these intervals and deploys a deep reinforcement learning agent to generate attack vectors during the identified intervals that can compromise the grid's safety and stability in the minimum possible time, while remaining undetected by local anomaly detection units and PMUs. Our approach provides a structured methodology for effective smart grid vulnerability analysis, enabling system operators to analyze the impact of attack parameters on grid safety and stability and facilitating suitable design changes in grid topology and operational parameters.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623155"}, {"primary_key": "1101730", "vector": [], "sparse_vector": [], "title": "Towards Practical Sleepy BFT.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Bitcoin's longest-chain protocol pioneered consensus under dynamic participation, also known as sleepy consensus, where nodes do not need to be permanently active. However, existing solutions for sleepy consensus still face two major issues, which we address in this work. First, existing sleepy consensus protocols have high latency (either asymptotically or concretely). We tackle this problem and achieve 4Δ latency (Δ is the bound on network delay) in the best case, which is comparable to classic BFT protocols without dynamic participation support. Second, existing protocols have to assume that the set of corrupt participants remains fixed throughout the lifetime of the protocol due to a problem we call costless simulation. We resolve this problem and support growing participation of corrupt nodes. Our new protocol also offers several other important advantages, including support for arbitrary fluctuation of honest participation as well as an efficient recovery mechanism for new active nodes.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623073"}, {"primary_key": "1101731", "vector": [], "sparse_vector": [], "title": "Uncovering Impact of Mental Models towards Adoption of Multi-device Crypto-Wallets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cryptocurrency users saw a sharp increase in different types of crypto wallets in the past decade. However, the emerging multi-device wallets, even with improved security guarantees over their single-device counterparts, are yet to receive proportionate adoption. This work presents a data-driven investigation into the perceptions of users towards multi-device wallets, using a survey of 357 crypto-wallet users. Our results revealed two significant groups among our participants-Newbies and Non-newbies. Our follow-up qualitative analysis, after educating, revealed a gap between the mental model for these participants and actual security guarantees. Furthermore, we investigated preferred default settings for crypto-wallets across our participants over different key-share distribution settings of multi-device wallets-the threat model considerations affected user preferences, signifying a need for contextualizing default settings. We identified concrete, actionable design avenues for future multi-device wallet developers to improve adoption.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623218"}, {"primary_key": "1101732", "vector": [], "sparse_vector": [], "title": "Your Battery Is a Blast! Safeguarding Against Counterfeit Batteries with Authentication.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Lithium-ion (Li-ion) batteries are the primary power source in various applications due to their high energy and power density. Their market was estimated to be up to 48 billion U.S. dollars in 2022. However, the widespread adoption of Li-ion batteries has resulted in counterfeit cell production, which can pose safety hazards to users. Counterfeit cells can cause explosions or fires, and their prevalence in the market makes it difficult for users to detect fake cells. Indeed, current battery authentication methods can be susceptible to advanced counterfeiting techniques and are often not adaptable to various cells and systems.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623179"}, {"primary_key": "1101733", "vector": [], "sparse_vector": [], "title": "Compact Frequency Estimators in Adversarial Environments.", "authors": ["<PERSON> <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Count-<PERSON> (CMS) and <PERSON><PERSON><PERSON><PERSON> (HK) are two realizations of a compact frequency estimator (CFE). These are a class of probabilistic data structures that maintain a compact summary of (typically) high-volume streaming data, and provides approximately correct estimates of the number of times any particular element has appeared. CFEs are often the base structure in systems looking for the highest-frequency elements (i.e., top-K elements, heavy hitters, elephant flows). Traditionally, probabilistic guarantees on the accuracy of frequency estimates are proved under the implicit assumption that stream elements do not depend upon the internal randomness of the structure. Said another way, they are proved in the presence of data streams that are created by non-adaptive adversaries. Yet in many practical use-cases, this assumption is not well-matched with reality; especially, in applications where malicious actors are incentivized to manipulate the data stream. We show that the CMS and HK structures can be forced to make significant estimation errors, by concrete attacks that exploit adaptivity. We analyze these attacks analytically and experimentally, with tight agreement between the two. Sadly, these negative results seem unavoidable for (at least) sketch-based CFEs with parameters that are reasonable in practice. On the positive side, we give a new CFE (Count-Keeper) that can be seen as a composition of the CMS and HK structures. Count-Keeper estimates are typically more accurate (by at least a factor of two) than CMS for \"honest\" streams; our attacks against CMS and HK are less effective (and more resource intensive) when used against Count-Keeper; and <PERSON><PERSON><PERSON> has a native ability to flag estimates that are suspicious, which neither CMS or HK (or any other CFE, to our knowledge) admits.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623216"}, {"primary_key": "1101734", "vector": [], "sparse_vector": [], "title": "Poster: Towards a Dataset for the Discrimination between Warranted and Unwarranted Emails.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this research, the prevailing issue we address is the over-generalized perspective of spam/ham (non-spam) classification. Despite the intricacies of spam classification, reliance on user feedback may inadvertently skew filters to misclassify legitimate and malicious emails, as users are prone to flag innocuous commercial mail as spam rather than unsubscribing. Current spam datasets have a propensity to include such user-flagged spam which can lead to further misclassification, leading to filters biased against warranted commercial correspondence. Motivated to address this concern, we introduce two new classification categories that delve deeper into the nuances of spam. 'Warranted spam', refers to consensual communications, from a credible source with transparent and safe opt-out mechanisms, and 'unwarranted spam' describes unsolicited messages, often of a malicious nature. Utilizing these classifications, we propose an innovative and dynamic 'warranted spam' dataset that seeks to pave the way for researchers to develop more sophisticated spam filtering techniques. Furthermore, our study delves into pioneering machine learning and natural language processing approaches, harnessing our dataset's potential. The overarching aspiration of our work is to augment online safety, preserve brand integrity, and optimize both the user experience and the efficacy of email marketing campaigns.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624397"}, {"primary_key": "1101735", "vector": [], "sparse_vector": [], "title": "Poster: Accountable Processing of Reported Street Problems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Municipalities increasingly depend on citizens to file digital reports about issues such as potholes or illegal trash dumps to improve their response time. However, the responsible authorities may be incentivized to ignore certain reports, e.g., when addressing them inflicts high costs. In this work, we explore the applicability of blockchain technology to hold authorities accountable regarding filed reports. Our initial assessment indicates that our approach can be extended to benefit citizens and authorities in the future.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624367"}, {"primary_key": "1101736", "vector": [], "sparse_vector": [], "title": "Tainted Secure Multi-Execution to Restrict Attacker Influence.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Attackers can steal sensitive user information from web pages via third-party scripts. Prior work shows that secure multi-execution (SME) with declassification is useful for mitigating such attacks, but that attackers can leverage dynamic web features to declassify more than intended. The proposed solution of disallowing events from dynamic web elements to be declassified is too restrictive to be practical; websites that declassify events from dynamic elements cannot function correctly.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623110"}, {"primary_key": "1101737", "vector": [], "sparse_vector": [], "title": "Poster: RPAL-Recovering Malware Classifiers from Data Poisoning using Active Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Intuitively, poisoned machine learning (ML) models may forget their adversarial manipulation via retraining. However, can we quantify the time required for model recovery? From an adversarial perspective, is a small amount of poisoning sufficient to force the defender to retrain significantly more over time?", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624391"}, {"primary_key": "1101738", "vector": [], "sparse_vector": [], "title": "Marketing to Children Through Online Targeted Advertising: Targeting Mechanisms and Legal Aspects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Many researchers and organizations, such as WHO and UNICEF, have raised awareness of the dangers of advertisements targeted at children. While most existing laws only regulate ads on television that may reach children, lawmakers have been working on extending regulations to online advertising and, for example, forbid (e.g., the DSA) or restrict (e.g., the COPPA) advertising based on profiling to children.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623172"}, {"primary_key": "1101739", "vector": [], "sparse_vector": [], "title": "SCORED &apos;23: Workshop on Software Supply Chain Offensive Research and Ecosystem Defenses.", "authors": ["Marcela S<PERSON>", "Santiago Torres-Arias", "<PERSON>"], "summary": "Recent attacks on the software supply chain have shed light on the fragility and importance of ensuring the security and integrity of this vital ecosystem. Addressing the technical and social challenges to building trustworthy software for deployment in sensitive and/or large-scale enterprise or governmental settings requires innovative solutions and an interdisciplinary approach. The Workshop on Software Supply Chain Offensive Research and Ecosystem Defenses (SCORED) is a venue that brings together industry practitioners, academics, and policymakers to present and discuss security vulnerabilities, novel defenses against attacks, project demos, adoption requirements and best practices in the software supply chain. The complete SCORED'23 workshop proceedings are available at: https://dl.acm.org/doi/proceedings/10.1145/3576915", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624031"}, {"primary_key": "1101740", "vector": [], "sparse_vector": [], "title": "Devil in Disguise: Breaching Graph Neural Networks Privacy through Infiltration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yutong Hu", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>"], "summary": "Graph neural networks (GNNs) have been developed to mine useful information from graph data of various applications, e.g., healthcare, fraud detection, and social recommendation. However, GNNs open up new attack surfaces for privacy attacks on graph data. In this paper, we propose Infiltrator, a privacy attack that is able to pry node-level private information based on black-box access to GNNs. Different from existing works that require prior information of the victim node, we explore the possibility of conducting the attack without any information of the victim node. Our idea is to infiltrate the graph with attacker-created nodes to befriend the victim node. More specifically, we design infiltration schemes that enable the adversary to infer the label, neighboring links, and sensitive attributes of a victim node. We evaluate Infiltrator with extensive experiments on three representative GNN models and six real-world datasets. The results demonstrate that Infiltrator can achieve an attack performance of more than 98% in all three attacks, outperforming baseline approaches. We further evaluate the defense resistance of Infiltrator against the graph homophily defender and the differentially private model.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623173"}, {"primary_key": "1101741", "vector": [], "sparse_vector": [], "title": "Greybox Fuzzing of Distributed Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Grey-box fuzzing is the lightweight approach of choice for finding bugs in sequential programs. It provides a balance between efficiency and effectiveness by conducting a biased random search over the domain of program inputs using a feedback function from observed test executions. For distributed system testing, however, the state-of-practice is represented today by only black-box tools that do not attempt to infer and exploit any knowledge of the system's past behaviours to guide the search for bugs.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623097"}, {"primary_key": "1101742", "vector": [], "sparse_vector": [], "title": "Speranza: Usable, Privacy-friendly Software Signing.", "authors": ["<PERSON>", "<PERSON>", "Santiago Torres-Arias", "<PERSON>"], "summary": "Software repositories, used for wide-scale open software distribution, are a significant vector for security attacks. Software signing provides authenticity, mitigating many such attacks. Developer-managed signing keys pose usability challenges, but certificate-based systems introduce privacy problems. This work, S<PERSON>anza, uses certificates to verify software authenticity but still provides anonymity to signers using zero-knowledge identity co-commitments.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623200"}, {"primary_key": "1101743", "vector": [], "sparse_vector": [], "title": "A Good Fishman Knows All the Angles: A Critical Evaluation of Google&apos;s Phishing Page Classifier.", "authors": ["Changqing Miao", "<PERSON><PERSON><PERSON>", "<PERSON>", "Wenchang Shi", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Phishing is one of the most popular cyberspace attacks. Phishing detection has been integrated into mainstream browsers to provide online protection. The phishing detector of Google Chrome reports millions of phishing attacks per week. However, it has been proven to be vulnerable to evasion attacks. Currently, Google has upgraded Chrome/Chromium's phishing detector, introducing a CNN-based image classifier. The robustness of the new-generation detector is unclear. If it can be bypassed, its billions of users will be exposed to sophisticated attackers. This paper presents a critical evaluation of Google's phishing detector by targeted evasion testing, and investigates corresponding defensive techniques. First, we propose a three-stage evasion method against the phishing image classifier. The experiments show that it can be completely bypassed with adversarial phishing pages generated using the proposed method. Meanwhile, the phishing pages still preserve their visual utility. Second, we introduce two defense techniques to enhance the phishing detection model. The results show that even using lightweight defense methods can significantly improve the model robustness. Our research reveals that Google's new-generation phishing classifier is very vulnerable to targeted evasion attacks. A sophisticated phishers can know how to fool the classifier. Billions of Chrome users are being exposed to potential phishing attacks. To improve its robustness, necessary security enhancements should be introduced.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623199"}, {"primary_key": "1101744", "vector": [], "sparse_vector": [], "title": "Aggregate Signatures with Versatile Randomization and Issuer-Hiding Multi-Authority Anonymous Credentials.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Anonymous credentials (AC) offer privacy in user-centric identity management. They enable users to authenticate anonymously, revealing only necessary attributes. With the rise of decentralized systems like self-sovereign identity, the demand for efficient AC systems in a decentralized setting has grown. Relying on conventional AC systems, however, require users to present independent credentials when obtaining them from different issuers, leading to increased complexity. AC systems should ideally support being multi-authority for efficient presentation of multiple credentials from various issuers. Another vital property is issuer hiding, ensuring that the issuer's identity remains concealed, revealing only compliance with the verifier's policy. This prevents unique identification based on the sole combination of credential issuers. To date, there exists no AC scheme satisfying both properties simultaneously.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623203"}, {"primary_key": "1101745", "vector": [], "sparse_vector": [], "title": "Poster: Query-efficient Black-box Attack for Image Forgery Localization via Reinforcement Learning.", "authors": ["Xianbo Mo", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recently, deep learning has been widely used in forensics tools to detect and localize forgery images. However, its susceptibility to adversarial attacks highlights the need for the exploration of anti-forensics research. To achieve this, we introduce an innovative and query-efficient black-box anti-forensics framework tailored for the generation of adversarial forgery images. This framework is designed to simulate the query dynamics of online forensic services, utilizing a Markov Decision Process formulation within the paradigm of reinforcement learning. We further introduce a novel reward function, which evaluates the efficacy of attacks based on the disjunction between query results and attack targets. To improve the query efficiency of these attacks, an actor-critic algorithm is employed to maximize cumulative rewards. Empirical findings substantiate the efficacy of our proposed methodology. Specifically, it demonstrates pronounced adversarial effects on a range of prevailing image forgery detectors, while ensuring negligible visually perceptible distortions in the resultant anti-forensics images.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624390"}, {"primary_key": "1101746", "vector": [], "sparse_vector": [], "title": "Poster: Generating Experiences for Autonomous Network Defense.", "authors": ["<PERSON><PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reinforcement Learning (RL) offers a promising path toward developing defenses for the next generation of computer networks. The hope is that RL not only helps to automate network defenses, but in addition, RL finds novel solutions to defend networks that adapt to deal with the increasing complexity of networks and threats. Despite the promise, existing work applying RL to cybersecurity trains cyber defenders on rigid and narrow problem definitions with small computer networks.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624381"}, {"primary_key": "1101747", "vector": [], "sparse_vector": [], "title": "On the Security of KZG Commitment for VSS.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The constant-sized polynomial commitment scheme by <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (Asiscrypt 2010), also known as the KZG commitment, is an essential component in designing bandwidth-efficient verifiable secret-sharing (VSS) protocols. We point out, however, that the KZG commitment is missing two important properties that are crucial for VSS protocols.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623127"}, {"primary_key": "1101748", "vector": [], "sparse_vector": [], "title": "Poster: Attestor - Simple Proof-of-Storage-Time.", "authors": ["<PERSON><PERSON>"], "summary": "Proof of Storage-Time (PoST) is a cryptographic primitive that enables a server to demonstrate non-interactive continuous availability of outsourced data in a publicly verifiable way.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624368"}, {"primary_key": "1101749", "vector": [], "sparse_vector": [], "title": "LeakyOhm: Secret Bits Extraction using Impedance Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The threats of physical side-channel attacks and their countermeasures have been widely researched. Most physical side-channel attacks rely on the unavoidable influence of computation or storage on current consumption or voltage drop on a chip. Such data-dependent influence can be exploited by, for instance, power or electromagnetic analysis. In this work, we introduce a novel non-invasive physical side-channel attack, which exploits the data-dependent changes in the impedance of the chip. Our attack relies on the fact that the temporarily stored contents in registers alter the physical characteristics of the circuit, which results in changes in the die's impedance. To sense such impedance variations, we deploy a well-known RF/microwave method called scattering parameter analysis, in which we inject sine wave signals with high frequencies into the system's power distribution network (PDN) and measure the echo of the signals. We demonstrate that according to the content bits and physical location of a register, the reflected signal is modulated differently at various frequency points enabling the simultaneous and independent probing of individual registers. Such side-channel leakage challenges the t-probing security model assumption used in masking, which is a prominent side-channel countermeasure. To validate our claims, we mount non-profiled and profiled impedance analysis attacks on hardware implementations of unprotected and high-order masked AES. We show that in the case of the profiled attack, only a single trace is required to recover the secret key. Finally, we discuss how a specific class of hiding countermeasures might be effective against impedance leakage.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623092"}, {"primary_key": "1101751", "vector": [], "sparse_vector": [], "title": "CookieGraph: Understanding and Detecting First-Party Tracking Cookies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Carmela Tron<PERSON>o"], "summary": "As third-party cookie blocking is becoming the norm in mainstream web browsers, advertisers and trackers have started to use first-party cookies for tracking. To understand this phenomenon, we conduct a differential measurement study with versus without third-party cookies. We find that first-party cookies are used to store and exfiltrate identifiers to known trackers even when third-party cookies are blocked.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616586"}, {"primary_key": "1101752", "vector": [], "sparse_vector": [], "title": "&quot;I just stopped using one and started using the other&quot;: Motivations, Techniques, and Challenges When Switching Password Managers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper explores what motivates password manager (PM) users in the US to switch from one PM to another, the techniques they employ when switching, and challenges they encounter throughout. Through a screener (n = 412) followed by a main survey (n = 54), we find that browser-based PMs are the most widely used, with most of these users motivated to use the PM due to convenience. Unfortunately, password reuse remains high. Most participants that switch PMs do so for usability reasons, but are also motivated by cost, as third-party PMs' full suite of features often require a subscription fee. Some PM-switchers are also motivated by recent security breaches, such as what was reported at LastPass in the Fall of 2022, with some participants losing trust in LastPass and PMs generally as a result. Those that switch mostly employ manual techniques of moving their passwords, e.g., copying and pasting their credentials from their previous to their new PM, despite most PMs offering ways to automatically transfer credentials in bulk across PMs. Assistance during the switching process is limited, with less than half of participants that switched receiving guidance during the switching process. From these findings, we make recommendations to PMs that can improve their overall user experience and use, including eliciting and acting on regular feedback from users as well as making PM settings more easily reachable and customizable by end-users.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623150"}, {"primary_key": "1101753", "vector": [], "sparse_vector": [], "title": "Assume but Verify: Deductive Verification of Leaked Information in Concurrent Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of specifying and proving the security of non-trivial, concurrent programs that intentionally leak information. We present a method that decomposes the problem into (a) proving that the program only leaks information it has declassified via assume annotations already widely used in deductive program verification; and (b) auditing the declassifications against a declarative security policy. We show how condition (a) can be enforced by an extension of the existing program logic SecCSL, and how (b) can be checked by proving a set of simple entailments. Part of the challenge is to define respective semantic soundness criteria and to formally connect these to the logic rules and policy audit. We support our methodology in an auto-active program verifier, which we apply to verify the implementations of various case study programs against a range of declassification policies.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623141"}, {"primary_key": "1101754", "vector": [], "sparse_vector": [], "title": "Pakistani Teens and Privacy - How Gender Disparities, Religion and Family Values Impact the Privacy Design Space.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The understanding of how teenagers perceive, manage and perform privacy is less well-understood in spaces outside of Western, educated, industrialised, rich and democratic countries.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623087"}, {"primary_key": "1101755", "vector": [], "sparse_vector": [], "title": "Stealing the Decoding Algorithms of Language Models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Krishna", "<PERSON><PERSON>", "<PERSON>"], "summary": "A key component of generating text from modern language models (LM) is the selection and tuning of decoding algorithms. These algorithms determine how to generate text from the internal probability distribution generated by the LM. The process of choosing a decoding algorithm and tuning its hyperparameters takes significant time, manual effort, and computation, and it also requires extensive human evaluation. Therefore, the identity and hyperparameters of such decoding algorithms are considered to be extremely valuable to their owners. In this work, we show, for the first time, that an adversary with typical API access to an LM can steal the type and hyperparameters of its decoding algorithms at very low monetary costs. Our attack is effective against popular LMs used in text generation APIs, including GPT-2, GPT-3 and GPT-Neo. We demonstrate the feasibility of stealing such information with only a few dollars, e.g., 0.8, 1, 4, and 40 for the four versions of GPT-3.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616652"}, {"primary_key": "1101757", "vector": [], "sparse_vector": [], "title": "CryptoBap: A Binary Analysis Platform for Cryptographic Protocols.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce CryptoBap, a platform to verify weak secrecy and authentication for the (ARMv8 and RISC-V) machine code of cryptographic protocols. We achieve this by first transpiling the binary of protocols into an intermediate representation and then performing a crypto-aware symbolic execution to automatically extract a model of the protocol that represents all its execution paths. Our symbolic execution resolves indirect jumps and supports bounded loops using the loop-summarization technique, which we fully automate. The extracted model is then translated into models amenable to automated verification via ProVerif and CryptoVerif using a third-party toolchain. We prove the soundness of the proposed approach and used CryptoBap to verify multiple case studies ranging from toy examples to real-world protocols, TinySSH, an implementation of SSH, and WireGuard, a modern VPN protocol.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623090"}, {"primary_key": "1101758", "vector": [], "sparse_vector": [], "title": "Optical Cryptanalysis: Recovering Cryptographic Keys from Power LED Light Fluctuations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Although power LEDs have been integrated in various devices that perform cryptographic operations for decades, the cryptanalysis risk they pose has not yet been investigated. In this paper, we present optical cryptanalysis, a new form of cryptanalytic side-channel attack, in which secret keys are extracted by using a photodiode to measure the light emitted by a device's power LED and analyzing subtle fluctuations in the light intensity during cryptographic operations. We analyze the optical leakage of power LEDs of various consumer devices and the factors that affect the optical SNR. We then demonstrate end-to-end optical cryptanalytic attacks against a range of consumer devices (smartphone, smartcard, and Raspberry Pi, along with their USB peripherals) and recover secret keys (RSA, ECDSA, SIKE) from prior and recent versions of popular cryptographic libraries (GnuPG, Libgcrypt, PQCrypto-SIDH) from a maximum distance of 25 meters.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616620"}, {"primary_key": "1101759", "vector": [], "sparse_vector": [], "title": "Recovering Fingerprints from In-Display Fingerprint Sensors via Electromagnetic Side Channel.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, in-display fingerprint sensors have been widely adopted in newly-released smartphones. However, we find this new technique can leak information about the user's fingerprints during a screen-unlocking process via the electromagnetic (EM) side channel that can be exploited for fingerprint recovery. We propose FPLogger to demonstrate the feasibility of this novel side-channel attack. Specifically, it leverages the emitted EM emanations when the user presses the in-display fingerprint sensor to extract fingerprint information, then maps the captured EM signals to fingerprint images and develops 3D fingerprint pieces to spoof and unlock the smartphones. We have extensively evaluated the effectiveness of FPlogger on five commodity smartphones equipped with both optical and ultrasonic in-display fingerprint sensors, and the results show it achieves promising similarities in recovering fingerprint images. In addition, results from 50 end-to-end spoofing attacks also present FPLogger achieves 24% (top-1) and 54% (top-3) success rates in spoofing five different smartphones.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623153"}, {"primary_key": "1101760", "vector": [], "sparse_vector": [], "title": "Poster: Circumventing the GFW with TLS Record Fragmentation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "State actors around the world censor the HTTPS protocol to block access to certain websites. While many circumvention strategies utilize the TCP layer only little emphasis has been placed on the analysis of TLS-a complex protocol and integral building block of HTTPS. In contrast to the TCP layer, circumvention methods on the TLS layer do not require root privileges since TLS operates on the application layer. With this proposal, we want to motivate a deeper analysis of TLS in regard to censorship circumvention techniques. To prove the existence of such techniques, we present TLS record fragmentation as a novel circumvention technique and circumvent the Great Firewall of China (GFW) using this technique. We hope that our research fosters collaboration between censorship and TLS researchers.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624372"}, {"primary_key": "1101761", "vector": [], "sparse_vector": [], "title": "Poster: Fooling XAI with Explanation-Aware Backdoors.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The overabundance of learnable parameters in recent machine-learning models renders them inscrutable. Even their developers can not explain their exact inner workings anymore. For this reason, researchers have developed explanation algorithms to shed light on a model's decision-making process. Explanations identify the deciding factors for a model's decision. Therefore, much hope is set in explanations to solve problems like biases, spurious correlations, and more prominently attacks like neural backdoors.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624379"}, {"primary_key": "1101762", "vector": [], "sparse_vector": [], "title": "Finding All Cross-Site Needles in the DOM Stack: A Comprehensive Methodology for the Automatic XS-Leak Detection in Web Browsers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cross-Site Leaks (XS-Leaks) are a class of vulnerabilities that allow a web attacker to infer user state from a target web application cross-origin. Fixing XS-Leaks is a cat-and-mouse game: once a published vulnerability is fixed, a variant is discovered. To end this game, we propose a methodology to find all leak techniques for a given state-dependent resource and a set of inclusion method. We translate a website's DOM at runtime into a directed graph. We execute this translation twice, once for each state. The outputs are two slightly different graphs. We then get the set of all leak techniques by computing these two graphs' differences. The remaining nodes and edges differ between the two states, and the corresponding DOM properties and objects can be observed cross-origin.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616598"}, {"primary_key": "1101763", "vector": [], "sparse_vector": [], "title": "&quot;Get in Researchers; We&apos;re Measuring Reproducibility&quot;: A Reproducibility Study of Machine Learning Papers in Tier 1 Security Conferences.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reproducibility is crucial to the advancement of science; it strengthens confidence in seemingly contradictory results and expands the boundaries of known discoveries. Computer Security has the natural benefit of creating artifacts that should facilitate computational reproducibility, the ability for others to use someone else's code and data to independently recreate results, in a relatively straightforward fashion. While the Security community has recently increased its attention on reproducibility, an independent and comprehensive measurement of the current state of reproducibility has not been conducted. In this paper, we perform the first such study, targeting reproducible artifacts generated specifically by papers on machine learning security (one of the most popular areas in academic research) published in Tier 1 security conferences over the past ten years (2013-2022). We perform our measurement study of indirect and direct reproducibility over nearly 750 papers, their codebases, and datasets. Our analysis shows that there is no statistically significant difference between the availability of artifacts before and after the introduction of Artifact Evaluation Committees in Tier 1 conferences. However, based on three years of results, artifacts that pass through this process work at a higher rate than those that do not. From our collected findings, we offer data-driven suggestions for improving reproducibility in our community, including five common problems observed in our study. In so doing, we demonstrate that significant progress still needs to be made in computational reproducibility in Computer Security research.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623130"}, {"primary_key": "1101764", "vector": [], "sparse_vector": [], "title": "Poster: Generic Multidimensional Linear Cryptanalysis of Feistel Ciphers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This poster presents new generic attacks on Feistel ciphers that incorporate the key addition at the input of the round function only. This feature leads to a specific vulnerability that can be exploited using multidimensional linear cryptanalysis. More specifically, our approach involves using key-independent linear trails so that the distribution of a combination of the plaintext and ciphertext can be computed, making it possible to use the likelihood-ratio test as a distinguisher. We provide theoretical estimates of the cost of our generic attacks, and verify these experimentally by applying the attacks to CAST-128 and LOKI91. The theoretical and experimental findings demonstrate that the proposed attacks lead to significant reductions in data or time complexity in several interesting cases.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624365"}, {"primary_key": "1101765", "vector": [], "sparse_vector": [], "title": "Do Users Write More Insecure Code with AI Assistants?", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "AI code assistants have emerged as powerful tools that can aid in the software development life-cycle and can improve developer productivity. Unfortunately, such assistants have also been found to produce insecure code in lab environments, raising significant concerns about their usage in practice. In this paper, we conduct a user study to examine how users interact with AI code assistants to solve a variety of security related tasks. Overall, we find that participants who had access to an AI assistant wrote significantly less secure code than those without access to an assistant. Participants with access to an AI assistant were also more likely to believe they wrote secure code, suggesting that such tools may lead users to be overconfident about security flaws in their code. To better inform the design of future AI-based code assistants, we release our user-study apparatus to researchers seeking to build on our work.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623157"}, {"primary_key": "1101766", "vector": [], "sparse_vector": [], "title": "ACABELLA: Automated (Crypt)analysis of Attribute-Based Encryption Leveraging Linear Algebra.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Attribute-based encryption (ABE) is a popular type of public-key encryption that enforces access control cryptographically, and has spurred the proposal of many use cases. To satisfy the requirements of the setting, tailor-made schemes are often introduced. However, designing secure schemes---as well as verifying that they are secure---is notoriously hard. Several of these schemes have turned out to be broken, making them dangerous to deploy in practice.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616576"}, {"primary_key": "1101767", "vector": [], "sparse_vector": [], "title": "AISec &apos;23: 16th ACM Workshop on Artificial Intelligence and Security.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The use of Artificial Intelligence (AI) and Machine Learning (ML) has been the center of the most outstanding advancements in the last years. The ability to analyze considerable streams of data in real time makes these technologies the most promising tool in many domains, including cybersecurity. As an outstanding example, ML can be used for identifying malware because of its ability to detect patterns otherwise difficult to see for humans and hard-coded rules. As malware continues to evolve, ML will become increasingly important for keeping up with the latest threats. However, the use of AI and ML in security-relevant domains raised rightful concerns about their trustworthiness and robustness, especially in front of adaptive attackers. Additionally, privacy threats are now emerging as a crucial aspect and need proper testing and possibly mitigation to prevent data stealing and leakage of sensitive information. The AISec workshop provides a venue for presenting and discussing new developments in the intersection of security and privacy with AI and ML. The complete AISec'23 workshop proceedings are available at: https://dl.acm.org/doi/proceedings/10.1145/3576915.3624029.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624029"}, {"primary_key": "1101768", "vector": [], "sparse_vector": [], "title": "KRover: A Symbolic Execution Engine for Dynamic Kernel Analysis.", "authors": ["Pansilu <PERSON>", "<PERSON><PERSON>", "Haiqing Qiu", "Haoxin Tu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present KRover, a novel kernel symbolic execution engine catered for dynamic kernel analysis such as vulnerability analysis and exploit generation. Different from existing symbolic execution engines, KRover operates directly upon a live kernel thread's virtual memory and weaves symbolic execution into the target's native executions. KRover is compact as it neither lifts the target binary to an intermediary representation nor uses QEMU or dynamic binary translation. Benchmarked against S2E, our performance experiments show that KRover is up to 50 times faster but with one tenth to one quarter of S2E memory cost. As shown in our four case studies, KRover is noise free, has the best-possible binary intimacy and does not require prior kernel instrumentation. Moreover, a user can develop her kernel analyzer that not only uses KRover as a symbolic execution library but also preserves its independent capabilities of reading/writing/controlling the target runtime. Namely, the resulting analyzer on top of KRover integrates symbolic reasoning and conventional dynamic analysis and reaps the benefits of their reinforcement to each other.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623198"}, {"primary_key": "1101769", "vector": [], "sparse_vector": [], "title": "Post Quantum Fuzzy Stealth Signatures and Applications.", "authors": ["<PERSON><PERSON> Pu", "<PERSON> <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Private payments in blockchain-based cryptocurrencies have been a topic of research, both academic and industrial, ever since the advent of Bitcoin. Stealth address payments were proposed as a solution to improve payment privacy for users and are, in fact, deployed in several major cryptocurrencies today. The mechanism lets users receive payments so that none of these payments are linkable to each other or the recipient. Currently known stealth address mechanisms either (1) are insecure in certain reasonable adversarial models, (2) are inefficient in practice or (3) are incompatible with many existing currencies.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623148"}, {"primary_key": "1101770", "vector": [], "sparse_vector": [], "title": "Stolen Risks of Models with Security Properties.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Verifiable robust machine learning, as a new trend of ML security defense, enforces security properties (e.g., Lipschitzness, Monotonicity) on machine learning models and achieves satisfying accuracy-security trade-off. Such security properties identify a series of evasion strategies of ML security attackers and specify logical constraints on their effects on a classifier (e.g., the classifier is monotonically increasing along some feature dimensions). However, little has been done so far to understand the side effect of those security properties on the model privacy.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616653"}, {"primary_key": "1101771", "vector": [], "sparse_vector": [], "title": "Vulnerability Intelligence Alignment via Masked Graph Attention Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cybersecurity vulnerability information is often sourced from multiple channels, such as government vulnerability repositories, individually maintained vulnerability-gathering platforms, or vulnerability-disclosure email lists and forums. Integrating vulnerability information from different channels enables comprehensive threat assessment and quick deployment to various security mechanisms. However, automatic integration of vulnerability information, especially those lacking decisive information (e.g., CVE-ID), is hindered by the limitations of today's entity alignment techniques.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616686"}, {"primary_key": "1101772", "vector": [], "sparse_vector": [], "title": "DeFi &apos;23: Workshop on Decentralized Finance and Security.", "authors": ["Kaihua Qin", "<PERSON>"], "summary": "Decentralized Finance (DeFi) heralds a transformative moment in the realm of finance, challenging traditional intermediaries with a blockchain-centric blueprint. As DeFi burgeons, the intricate dance between its evolution and security emerges as an area of pivotal significance. This workshop navigates the multifaceted landscape of DeFi, where inherent challenges intertwine with new vulnerabilities, emphasizing the necessity for vigilant evaluations and adaptive measures to ensure the integrity of the ecosystem. It further delves into the ripple effects of regulatory scrutiny and its subsequent influence on DeFi's security matrix. As we stand on the cusp of uncharted territories, the workshop aims to provide a comprehensive discourse on DeFi's security challenges, fortified by interdisciplinary expertise, inviting participants to explore, ideate, and collaboratively forge a path towards a robust and secure DeFi paradigm.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624026"}, {"primary_key": "1101773", "vector": [], "sparse_vector": [], "title": "Unsafe Diffusion: On the Generation of Unsafe Images and Hateful Memes From Text-To-Image Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Shen", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "State-of-the-art Text-to-Image models like Stable Diffusion and DALLE\\cdot2 are revolutionizing how people generate visual content. At the same time, society has serious concerns about how adversaries can exploit such models to generate problematic or unsafe images. In this work, we focus on demystifying the generation of unsafe images and hateful memes from Text-to-Image models. We first construct a typology of unsafe images consisting of five categories (sexually explicit, violent, disturbing, hateful, and political). Then, we assess the proportion of unsafe images generated by four advanced Text-to-Image models using four prompt datasets. We find that Text-to-Image models can generate a substantial percentage of unsafe images; across four models and four prompt datasets, 14.56% of all generated images are unsafe. When comparing the four Text-to-Image models, we find different risk levels, with Stable Diffusion being the most prone to generating unsafe content (18.92% of all generated images are unsafe). Given Stable Diffusion's tendency to generate more unsafe content, we evaluate its potential to generate hateful meme variants if exploited by an adversary to attack a specific individual or community. We employ three image editing methods, DreamBooth, Textual Inversion, and SDEdit, which are supported by Stable Diffusion to generate variants. Our evaluation result shows that 24% of the generated images using DreamBooth are hateful meme variants that present the features of the original hateful meme and the target individual/community; these generated images are comparable to hateful meme variants collected from the real world. Overall, our results demonstrate that the danger of large-scale generation of unsafe images is imminent. We discuss several mitigating measures, such as curating training data, regulating prompts, and implementing safety filters, and encourage better safeguard tools to be developed to prevent unsafe generation.1 Our code is available at https://github.com/YitingQu/unsafe-diffusion.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616679"}, {"primary_key": "1101774", "vector": [], "sparse_vector": [], "title": "Jack-in-the-box: An Empirical Study of JavaScript Bundling on the Web and its Security Implications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In recent years, we have seen an increased interest in studying the software supply chain of user-facing applications to uncover problematic third-party dependencies. Prior work shows that web applications often rely on outdated or vulnerable third-party code. Moreover, real-world supply chain attacks show that dependencies can also be used to deliver malicious code, e.g., for carrying cryptomining operations. Nonetheless, existing measurement studies in this domain neglect an important software engineering practice: developers often merge together third-party code into a single file called bundle, which they then deliver from their own servers, making it appear as first-party code. Bundlers like Webpack or Rollup are popular open-source projects with tens of thousand of GitHub stars, suggesting that this technology is widely-used by developers. Ignoring bundling may result in underestimating the complexity of modern software supply chains.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623140"}, {"primary_key": "1101776", "vector": [], "sparse_vector": [], "title": "MDTD: A Multi-Domain Trojan Detector for Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Fengqing Jiang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Machine learning models that use deep neural networks (DNNs) are vulnerable to backdoor attacks. An adversary carrying out a backdoor attack embeds a predefined perturbation called a trigger into a small subset of input samples and trains the DNN such that the presence of the trigger in the input results in an adversary-desired output class. Such adversarial retraining however needs to ensure that outputs for inputs without the trigger remain unaffected and provide high classification accuracy on clean samples. Existing defenses against backdoor attacks are computationally expensive, and their success has been demonstrated primarily on image-based inputs. The increasing popularity of deploying pretrained DNNs to reduce costs of re/training large models makes defense mechanisms that aim to detect 'suspicious' input samples preferable.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623082"}, {"primary_key": "1101777", "vector": [], "sparse_vector": [], "title": "SysPart: Automated Temporal System Call Filtering for Binaries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Restricting the system calls available to applications reduces the attack surface of the kernel and limits the functionality available to compromised applications. Recent approaches automatically identify the system calls required by programs to block unneeded ones. For servers, they even consider different phases of execution to tighten restrictions after initialization completes. However, they require access to the source code for applications and libraries, depend on users identifying when the server transitions from initialization to serving clients, or do not account for dynamically-loaded libraries. This paper introduces SYSPART, an automatic system-call filtering system designed for binary-only server programs that addresses the above limitations. Using a novel algorithm that combines static and dynamic analysis, SYSPART identifies the serving phases of all working threads of a server. Static analysis is used to compute the system calls required during the various serving phases in a sound manner, and dynamic observations are only used to complement static resolution of dynamically-loaded libraries when necessary. We evaluated SYSPART using six popular servers on x86-64 Linux to demonstrate its effectiveness in automatically identifying serving phases, generating accurate system-call filters, and mitigating attacks. Our results show that SYSPART outperforms prior binary-only approaches and performs comparably to source-code approaches.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623207"}, {"primary_key": "1101779", "vector": [], "sparse_vector": [], "title": "Passive SSH Key Compromise via Lattices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We demonstrate that a passive network attacker can opportunistically obtain private RSA host keys from an SSH server that experiences a naturally arising fault during signature computation. In prior work, this was not believed to be possible for the SSH protocol because the signature included information like the shared <PERSON><PERSON><PERSON><PERSON><PERSON> secret that would not be available to a passive network observer. We show that for the signature parameters commonly in use for SSH, there is an efficient lattice attack to recover the private key in case of a signature fault. We provide a security analysis of the SSH, IKEv1, and IKEv2 protocols in this scenario, and use our attack to discover hundreds of compromised keys in the wild from several independently vulnerable implementations.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616629"}, {"primary_key": "1101780", "vector": [], "sparse_vector": [], "title": "Unhelpful Assumptions in Software Security Research.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Klaas<PERSON><PERSON>"], "summary": "In the study of software security many factors must be considered. Once venturing beyond the simplest of laboratory experiments, the researcher is obliged to contend with exponentially complex conditions. Software security has been shown to be affected by priming, tool usability, library documentation, organisational security culture, the content and format of internet resources, IT team and developer interaction, Internet search engine ordering, developer personality, security warning placement, mentoring, developer experience and more. In a systematic review of software security papers published since 2016, we have identified a number of unhelpful assumptions that are commonly made by software security researchers. In this paper we list these assumptions, describe why they sometimes do not reflect reality, and suggest implications for researchers.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623122"}, {"primary_key": "1101782", "vector": [], "sparse_vector": [], "title": "Ou: Automating the Parallelization of Zero-Knowledge Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Ruzica Piskac", "<PERSON><PERSON>"], "summary": "A zero-knowledge proof (ZKP) is a powerful cryptographic primitive used in many decentralized or privacy-focused applications. However, the high overhead of ZKPs can restrict their practical applicability. We design a programming language, Ou, aimed at easing the programmer's burden when writing efficient ZKPs, and a compiler framework, <PERSON>n, that automates the analysis and distribution of statements to a computing cluster. Ou uses programming language semantics, formal methods, and combinatorial optimization to automatically partition an Ou program into efficiently sized chunks for parallel ZK-proving and/or verification. We contribute: (1) A front-end language where users can write proof statements as imperative programs in a familiar syntax; (2) A compiler architecture and implementation that automatically analyzes the program and compiles it into an optimized IR that can be lifted to a variety of ZKP constructions; and (3) A cutting algorithm, based on Pseudo-Boolean optimization and Integer Linear Programming, that reorders instructions and then partitions the program into efficiently sized chunks for parallel evaluation and efficient state reconciliation.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616621"}, {"primary_key": "1101783", "vector": [], "sparse_vector": [], "title": "Waks-On/Waks-Off: Fast Oblivious Offline/Online Shuffling and Sorting with Waksman Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As more privacy-preserving solutions leverage trusted execution environments (TEEs) like Intel SGX, it becomes pertinent that these solutions can by design thwart TEE side-channel attacks that research has brought to light. In particular, such solutions need to be fully oblivious to circumvent leaking private information through memory or timing side channels.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623133"}, {"primary_key": "1101784", "vector": [], "sparse_vector": [], "title": "FetchBench: Systematic Identification and Characterization of Proprietary Prefetchers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Prefetchers speculatively fetch memory using predictions on future memory use by applications. Different CPUs may use different prefetcher types, and two implementations of the same prefetcher can differ in details of their characteristics, leading to distinct runtime behavior. For a few implementations, security researchers showed through manual analysis how to exploit specific prefetchers to leak data. Identifying such vulnerabilities required tedious reverse-engineering, as prefetcher implementations are proprietary and undocumented. So far, no systematic study of prefetchers in common CPUs is available, preventing further security assessment.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623124"}, {"primary_key": "1101785", "vector": [], "sparse_vector": [], "title": "IoTFlow: Inferring IoT Device Behavior at Scale through Static Mobile Companion App Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The number of \"smart'' devices, that is, devices making up the Internet of Things (IoT), is steadily growing. They suffer from vulnerabilities just as other software and hardware. Automated analysis techniques can detect and address weaknesses before attackers can misuse them. Applying existing techniques or developing new approaches that are sufficiently general is challenging though. Contrary to other platforms, the IoT ecosystem features various software and hardware architectures.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623211"}, {"primary_key": "1101786", "vector": [], "sparse_vector": [], "title": "DE-FAKE: Detection and Attribution of Fake Images Generated by Text-to-Image Generation Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Text-to-image generation models that generate images based on prompt descriptions have attracted an increasing amount of attention during the past few months. Despite their encouraging performance, these models raise concerns about the misuse of their generated fake images. To tackle this problem, we pioneer a systematic study on the detection and attribution of fake images generated by text-to-image generation models. Concretely, we first build a machine learning classifier to detect the fake images generated by various text-to-image generation models. We then attribute these fake images to their source models, such that model owners can be held responsible for their models' misuse. We further investigate how prompts that generate fake images affect detection and attribution. We conduct extensive experiments on four popular text-to-image generation models, including DALL·E 2, Stable Diffusion, GLIDE, and Latent Diffusion, and two benchmark prompt-image datasets. Empirical results show that (1) fake images generated by various models can be distinguished from real ones, as there exists a common artifact shared by fake images from different models; (2) fake images can be effectively attributed to their source models, as different models leave unique fingerprints in their generated images; (3) prompts with the \"person'' topic or a length between 25 and 75 enable models to generate fake images with higher authenticity. All findings contribute to the community's insight into the threats caused by text-to-image generation models. We appeal to the community's consideration of the counterpart solutions, like ours, against the rapidly-evolving fake image generation.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616588"}, {"primary_key": "1101787", "vector": [], "sparse_vector": [], "title": "TrustBoost: Boosting Trust among Interoperable Blockchains.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Currently there exist many blockchains with weak trust guarantees, limiting applications and participation. Existing solutions to boost the trust using a stronger blockchain, e.g., via checkpointing, requires the weaker blockchain to give up sovereignty. In this paper, we propose a family of protocols in which multiple blockchains interact to create a combined ledger with boosted trust. We show that even if several of the interacting blockchains cease to provide security guarantees, the combined ledger continues to be secure - our Trustboost protocols achieve the optimal threshold of tolerating the insecure blockchains. This optimality, along with the necessity of blockchain interactions, is formally shown within the classic shared memory model, tackling the long standing open challenge of solving consensus in the presence of both Byzantine objects and processes. Furthermore, our proposed construction of Trustboost simply operates via smart contracts and require no change to the underlying consensus protocols of the participating blockchains, a form of \"consensus on top of consensus''. The protocols are lightweight and can be used on specific (e.g., high value) transactions; we demonstrate the practicality by implementing and deploying Trustboost as cross-chain smart contracts in the Cosmos ecosystem using approximately 3,000 lines of Rust code, made available as open source [52]. Our evaluation shows that using 10 Cosmos chains in a local testnet, Trustboost has a gas cost of roughly $2 with a latency of 2 minutes per request, which is in line with the cost on a high security chain such as Bitcoin or Ethereum.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623080"}, {"primary_key": "1101788", "vector": [], "sparse_vector": [], "title": "Lifting Network Protocol Implementation to Precise Format Specification with Security Applications.", "authors": ["Qingkai Shi", "<PERSON><PERSON>", "<PERSON><PERSON>g <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While inferring protocol formats is critical for many security applications, existing techniques often fall short of coverage, inasmuch as almost all of them are in a fashion of dynamic analysis and driven by a limited number of network packets. If a feature is not present in the input packets, the feature will be missed in the resulting formats. To tackle this problem, we develop a novel static program analysis that infers protocol message formats from the implementation of common top-down protocol parsers. However, to achieve the trifecta of coverage, precision, and efficiency, we have to address two challenges, namely path explosion and disordered path constraints. To this end, our approach uses abstract interpretation to produce a novel data structure called the abstract format graph. The graph structure delimits precise but costly operations to only small regions, thus ensuring precision and efficiency at the same time. Our inferred formats are of high coverage and precisely specify both field boundaries and semantic constraints among packet fields. Our evaluation shows that we can infer formats for a protocol in one minute with over 95% precision and recall, much better than four baselines. Our inferred formats can substantially enhance existing protocol fuzzers, improving the coverage by 20% to 260% and discovering 53 zero-days with 47 assigned CVEs. We also provide case studies of adopting our inferred formats in network traffic auditing and network intrusion detection.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616614"}, {"primary_key": "1101789", "vector": [], "sparse_vector": [], "title": "Poster: From Hashes to Ashes - A Comparison of Transcription Services.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In recent years, semi-structured interviews gained more and more importance in cyber security research. Transcribing audio recordings of such interviews is a crucial step in qualitative data analysis, but it is also a work-intensive and time-consuming task. While outsourcing presents a common option, maintaining research quality requires precise transcriptions -- a task further compounded by technical jargon and established expressions in the research field. In this study, we compare different transcription services and evaluate their outcome quality within the context of cyber security. Our findings provide insights for researchers navigating the complex landscape of transcription services, offering informed choices to enhance the accuracy and validity of qualitative data analysis.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624380"}, {"primary_key": "1101790", "vector": [], "sparse_vector": [], "title": "Poster: Vulcan - Repurposing Accessibility Features for Behavior-based Intrusion Detection Dataset Generation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The generation of datasets is one of the most promising approaches to collecting the necessary behavior data to train machine learning models for host-based intrusion detection. While various dataset generation methods have been proposed, they are often limited and either only generate network traffic or are restricted to a narrow subset of applications. We present Vulcan, a preliminary framework that uses accessibility features to generate datasets by simulating user interactions for an extendable set of applications. It uses behavior profiles that define realistic user behavior and facilitate dataset updates upon changes in software versions, thus reducing the effort required to keep a dataset relevant. Preliminary results show that using accessibility features presents a promising approach to improving the quality of datasets in the HIDS domain.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624404"}, {"primary_key": "1101791", "vector": [], "sparse_vector": [], "title": "Watch This Space: Securing Satellite Communication through Resilient Transmitter Fingerprinting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Due to an increase in the availability of cheap off-the-shelf radio hardware, signal spoofing and replay attacks on satellite ground systems have become more accessible than ever. This is particularly a problem for legacy systems, many of which do not offer cryptographic security and cannot be patched to support novel security measures.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623135"}, {"primary_key": "1101792", "vector": [], "sparse_vector": [], "title": "Fuzz on the Beach: Fuzzing Solana Smart Contracts.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Solana has quickly emerged as a popular platform for building decentralized applications (DApps), such as marketplaces for non-fungible tokens (NFTs). A key reason for its success are Solana's low transaction fees and high performance, which is achieved in part due to its stateless programming model. Although the literature features extensive tooling support for smart contract security, current solutions are largely tailored for the Ethereum Virtual Machine. Unfortunately, the very stateless nature of Solana's execution environment introduces novel attack patterns specific to Solana requiring a rethinking for building vulnerability analysis methods.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623178"}, {"primary_key": "1101793", "vector": [], "sparse_vector": [], "title": "Grotto: Screaming fast (2+1)-PC or ℤ2n via (2, 2)-DPFs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce Grotto, a framework and C++ library for space- and time-efficient (2+1)-party piecewise polynomial (i.e., spline) evaluation on secrets additively shared over ℤ2n. Grotto improves on the state-of-the-art approaches based on distributed comparison functions (DCFs) in almost every metric, offering asymptotically superior communication and computation costs with the same or lower round complexity. At the heart of Grotto is a novel observation about the structure of the ''tree'' representation underlying the most efficient distributed point functions (DPFs) from the literature, alongside an efficient algorithm that leverages this structure to do with a lightweight DPF what state-of-the-art approaches require comparatively heavyweight DCFs to do. Our open-source Grotto implementation supports dozens of useful functions out of the box, including trigonometric and hyperbolic functions with their inverses; various logarithms; roots, reciprocals, and reciprocal roots; sign testing and bit counting; and over two dozen of the most common univariate activation functions from the deep-learning literature.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623147"}, {"primary_key": "1101794", "vector": [], "sparse_vector": [], "title": "Poster: Cybersecurity Usage in the Wild: A look at Deployment Challenges in Intrusion Detection and Alert Handling.", "authors": ["<PERSON>", "<PERSON><PERSON> (Daphne) Yao"], "summary": "We examine the challenges cybersecurity practitioners face during their daily activities, employing a survey and semi-directed interview for data gathering. Practitioners report on the frequency and level of threats as well as other factors like burnout. These factors are observed to vary with organization size and field (e.g. Medical, E-commerce).", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624375"}, {"primary_key": "1101795", "vector": [], "sparse_vector": [], "title": "LedgerLocks: A Security Framework for Blockchain Protocols Based on Adaptor Signatures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The scalability and interoperability challenges in current cryptocurrencies have motivated the design of cryptographic protocols that enable efficient applications on top and across widely used cryptocurrencies such as Bitcoin or Ethereum. Examples of such protocols include (virtual) payment channels, atomic swaps, oracle-based contracts, deterministic wallets, and coin mixing services. Many of these protocols are built upon minimal core functionalities supported by a wide range of cryptocurrencies. Most prominently, adaptor signatures (AS) have emerged as a powerful tool for constructing blockchain protocols that are (mostly) agnostic to the specific logic of the underlying cryptocurrency. Even though AS-based protocols are built upon the same cryptographic principles, there exists no modular and faithful way for reasoning about their security. Instead, all the works analyzing such protocols focus on reproving how adaptor signatures are used to cryptographically link transactions while considering highly simplified blockchain models that do not capture security-relevant aspects of transaction execution in blockchain-based consensus.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623149"}, {"primary_key": "1101796", "vector": [], "sparse_vector": [], "title": "Poster: Backdoor Attack on Extreme Learning Machines.", "authors": ["<PERSON><PERSON><PERSON>", "Gorka Abad", "<PERSON><PERSON><PERSON>"], "summary": "Deep neural networks (DNNs) achieve top performance through costly training on large datasets. Such resources may not be available in some scenarios, like IoT or healthcare. Extreme learning machines (ELMs) aim to alleviate this problem using single-layered networks, requiring fewer training resources. Current investigations have found that DNNs are prone to security and privacy threats, where malfunction of the network or training data extraction can be performed.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624369"}, {"primary_key": "1101797", "vector": [], "sparse_vector": [], "title": "SHERLOC: Secure and Holistic Control-Flow Violation Detection on Embedded Systems.", "authors": ["<PERSON>", "Zim<PERSON>"], "summary": "Microcontroller-based embedded systems are often programmed in low-level languages and are vulnerable to control-flow hijacking attacks. One approach to prevent such attacks is to enforce control-flow integrity (CFI), but inlined CFI enforcement can pose challenges in embedded systems. For example, it increases binary size and changes memory layout. Trace-based control-flow violation detection (CFVD) offers an alternative that doesn't require instrumentation of the protected software or changes to its memory layout. However, existing CFVD methods used in desktop systems require kernel modifications to store and analyze the trace, which limits their use to monitoring unprivileged applications. But, embedded systems are interrupt-driven, with the majority of processing taking place in the privileged mode. Therefore, it is critical to provide a holistic and system-oriented CFVD solution that can monitor control-flow transfers both within and among privileged and unprivileged components.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623077"}, {"primary_key": "1101798", "vector": [], "sparse_vector": [], "title": "Security Verification of Low-Trust Architectures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Low-trust architectures work on, from the viewpoint of software, always-encrypted data, and significantly reduce the amount of hardware trust to a small software-free enclave component. In this paper, we perform a complete formal verification of a specific low-trust architecture, the Sequestered Encryption (SE) architecture, to show that the design is secure against direct data disclosures and digital side channels for all possible programs. We first define the security requirements of the ISA of SE low-trust architecture. Looking upwards, this ISA serves as an abstraction of the hardware for the software, and is used to show how any program comprising these instructions cannot leak information, including through digital side channels. Looking downwards this ISA is a specification for the hardware, and is used to define the proof obligations for any RTL implementation arising from the ISA-level security requirements. These cover both functional and digital side-channel leakage. Next, we show how these proof obligations can be successfully discharged using commercial formal verification tools. We demonstrate the efficacy of our RTL security verification technique for seven different correct and buggy implementations of the SE architecture.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616643"}, {"primary_key": "1101799", "vector": [], "sparse_vector": [], "title": "SyzDirect: Directed Greybox Fuzzing for Linux Kernel.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Jiadong Lu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Bug reports and patch commits are dramatically increasing for OS kernels, incentivizing a critical need for kernel-level bug reproduction and patch testing. Directed greybox fuzzing (DGF), aiming to stress-test a specific part of code, is a promising approach for bug reproduction and patch testing. However, the existing DGF methods exclusively target user-space applications, presenting intrinsic limitations in handling OS kernels. In particular, these methods cannot pinpoint the appropriate system calls and the needed syscall parameter values to reach the target location,resulting in low efficiency and waste of resources.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623146"}, {"primary_key": "1101800", "vector": [], "sparse_vector": [], "title": "Interchain Timestamping for Mesh Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fourteen years after the invention of Bitcoin, there has been a proliferation of many permissionless blockchains. Each such chain provides a public ledger that can be written to and read from by anyone. In this multi-chain world, a natural question arises: what is the optimal security an existing blockchain, a consumer chain, can extract by only reading and writing to k other existing blockchains, the provider chains? We design a protocol, called interchain timestamping, and show that it extracts the maximum economic security from the provider chains, as quantified by the slashable safety resilience. We observe that interchain timestamps are already provided by light-client based bridges, so interchain timestamping can be readily implemented for Cosmos chains connected by the Inter-Blockchain Communication (IBC) protocol. We compare interchain timestamping with cross-staking, the original solution to mesh security, as well as with Trustboost, another recent security sharing protocol.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616612"}, {"primary_key": "1101801", "vector": [], "sparse_vector": [], "title": "Poster: A Privacy-Preserving Smart Contract Vulnerability Detection Framework for Permissioned Blockchain.", "authors": ["Wensheng Tian", "<PERSON><PERSON>", "Shuangxi Chen", "<PERSON>", "<PERSON>"], "summary": "The two main types of blockchains that are currently widely deployed are public blockchains and permissioned blockchains. The research that has been conducted for blockchain vulnerability detection is mainly oriented to public blockchains. Less consideration is given to the unique requirements of the permissioned blockchains, which cannot be directly migrated to the application scenarios of the permissioned blockchains. The permissioned blockchain is deployed between verified organizations, and its smart contracts may contain sensitive information such as the transaction flow of the contracts, transaction algorithms, etc. The sensitive information can be considered as the private information of the smart contracts themselves, which should be kept confidential to users outside the blockchain. In this paper, a privacy-preserving smart contract vulnerability detection framework is proposed. The framework leverages blockchain and confidential computing technologies to enable vulnerability detection in permissioned blockchain smart contracts while protecting the privacy of smart contracts. The framework is also able to protect the interests of vulnerability detection model owners. We experimentally validate the detection performance of our framework in a confidential computing environment.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624366"}, {"primary_key": "1101802", "vector": [], "sparse_vector": [], "title": "CryptoConcurrency: (Almost) Consensusless Asset Transfer with Shared Accounts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A typical blockchain protocol uses consensus to make sure that mutually mistrusting users agree on the order in which their operations on shared data are executed. However, it is known that asset transfer systems, by far the most popular application of blockchains, can be implemented without consensus. Assuming that no account can be accessed concurrently and every account belongs to a single owner, one can efficiently implement an asset transfer system in a purely asynchronous, consensus-free manner. It has also been shown that implementing asset transfer with shared accounts is impossible without consensus.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616587"}, {"primary_key": "1101803", "vector": [], "sparse_vector": [], "title": "Fast Unbalanced Private Set Union from Fully Homomorphic Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Private set union (PSU) allows two parties to compute the union of their sets without revealing anything else. It has been widely used in various applications. While several computationally efficient PSU protocols have been developed for the balanced case, they have a potential limitation in their communication complexity, which grows (super)-linearly with the size of the larger set. This poses a challenge when performing PSU in the unbalanced setting, where one party is a constrained device holding a small set, and another is a service provider holding a large set.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623064"}, {"primary_key": "1101804", "vector": [], "sparse_vector": [], "title": "Riggs: Decentralized Sealed-Bid Auctions.", "authors": ["Nirvan Tyagi", "<PERSON><PERSON>", "<PERSON>", "Riad S. Wahby", "<PERSON>", "<PERSON>"], "summary": "We introduce the first practical protocols for fully decentralized sealed-bid auctions using timed commitments. Timed commitments ensure that the auction is finalized fairly even if all participants drop out after posting bids or if n bidders collude to try to learn the nth bidder's bid value. Our protocols rely on a novel non-malleable timed commitment scheme which efficiently supports range proofs to establish that bidders have sufficient funds to cover a hidden bid value. This allows us to penalize users who abandon bids for exactly the bid value, while supporting simultaneous bidding in multiple auctions with a shared collateral pool. Our protocols are concretely efficient and we have implemented them in an Ethereum-compatible smart contract which automatically enforces payment and delivery of an auctioned digital asset.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623182"}, {"primary_key": "1101805", "vector": [], "sparse_vector": [], "title": "Cryptographically Enforced Memory Safety.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "C/C++ memory safety issues, such as out-of-bounds errors, are still prevalent in today's applications. The presence of a single exploitable software bug allows an adversary to gain unauthorized memory access and ultimately compromise the entire system. Typically, memory safety schemes only achieve widespread adaption if they provide lightweight and practical security. Thus, hardware support is indispensable. However, countermeasures often restrict unauthorized access to data using heavy-weight protection mechanisms that extensively reshape the processor's microarchitecture and break legacy compatibility.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623138"}, {"primary_key": "1101806", "vector": [], "sparse_vector": [], "title": "Alert Alchemy: SOC Workflows and Decisions in the Management of NIDS Rules.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Signature-based network intrusion detection systems (NIDSs) and network intrusion prevention systems (NIPSs) remain at the heart of network defense, along with the rules that enable them to detect threats. These rules allow Security Operation Centers (SOCs) to properly defend a network, yet we know almost nothing about how rules are created, evaluated and managed from an organizational standpoint. In this work, we analyze the processes surrounding the creation, management, and acquisition of rules for network intrusion detection. To understand these processes, we conducted interviews with 17 professionals who work at Managed Security Service Providers (MSSPs) or other organizations that provide network monitoring as a service or conduct their own network monitoring internally. We discovered numerous critical factors, such as rule specificity and total number of alerts and false positives, that guide SOCs in their rule management processes. These lower-level aspects of network monitoring processes have generally been regarded as immutable by prior work, which has mainly focused on designing systems that handle the resulting alert flows by dynamically reducing the number of noisy alerts SOC analysts need to sift through. Instead, we present several recommendations that address these lower-level aspects to help improve alert quality and allow SOCs to better optimize workflows and use of available resources. These recommendations include increasing the specificity of rules, explicitly defining feedback loops from detection to rule development, and setting up organizational processes to improve the transfer of tacit knowledge.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616581"}, {"primary_key": "1101807", "vector": [], "sparse_vector": [], "title": "Comparse: Provably Secure Formats for Cryptographic Protocols.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Data formats used for cryptographic inputs have historically been the source of many attacks on cryptographic protocols, but their security guarantees remain poorly studied. One reason is that, due to their low-level nature, formats often fall outside of the security model. Another reason is that studying all of the uses of all of the formats within one protocol is too difficult to do by hand, and requires a comprehensive, automated framework.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623201"}, {"primary_key": "1101808", "vector": [], "sparse_vector": [], "title": "The Danger of Minimum Exposures: Understanding Cross-App Information Leaks on iOS through Multi-Side-Channel Learning.", "authors": ["<PERSON><PERSON><PERSON>", "Jiale Guan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Fares Fahad S<PERSON>"], "summary": "Research on side-channel leaks has long been focusing on the information exposure from a single channel (memory, network traffic, power, etc.). Less studied is the risk of learning from multiple side channels related to a target activity (e.g., website visits) even when individual channels are not informative enough for an effective attack. Although the prior research made the first step on this direction, inferring the operations of foreground apps on iOS from a set of global statistics, still less clear are how to determine the maximum information leaks from all target-related side channels on a system, what can be learnt about the target from such leaks and most importantly, how to control information leaks from the whole system, not just from an individual channel.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616655"}, {"primary_key": "1101809", "vector": [], "sparse_vector": [], "title": "The Locality of Memory Checking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by the extended deployment of authenticated data structures (e.g., Merk<PERSON> Patricia Tries) for verifying massive amounts of data in blockchain systems, we begin a systematic study of the I/O efficiency of such systems. We first explore the fundamental limitations of memory checking, a previously-proposed abstraction for verifiable storage, in terms of its locality-a complexity measure that we introduce for the first time and is defined as the number of non-contiguous memory regions a checker must query to verifiably answer a read or a write query. Our central result is an Ω(log n/log log n) lower bound for the locality of any memory checker. Then we turn our attention to (dense and sparse) Merkle trees, one of the most celebrated memory checkers, and provide stronger lower bounds for their locality. For example, we show that any dense Merkle tree layout will have average locality at least (1/3)log n. Furthermore, if we allow node duplication, we show that if any write operation has at most polylog complexity, then the read locality cannot be less than log n/log log n. Our lower bounds help us construct two new locality-optimized authenticated data structures (DupTree and PrefixTree) which we implement and evaluate on random operations and real workloads, and which are shown to outperform traditional Merkle trees, especially as the number of leaves increases.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623195"}, {"primary_key": "1101810", "vector": [], "sparse_vector": [], "title": "HODOR: Shrinking Attack Surface on Node.js via System Call Limitation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>v", "<PERSON><PERSON><PERSON>"], "summary": "Node.js applications are becoming more and more widely adopted on the server side, partly due to the convenience of building these applications on top of the runtime provided by popular Node.js engines and the large number of third-party packages provided by the Node Package Management (npm) registry. Node.js provides Node.js applications with system interaction capabilities using system calls. However, such convenience comes with a price, i.e., the attack surface of JavaScript arbitrary code execution (ACE) vulnerabilities is expanded to the system call level.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616609"}, {"primary_key": "1101811", "vector": [], "sparse_vector": [], "title": "Specification and Verification of Side-channel Security for Open-source Processors via Leakage Contracts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Leakage contracts have recently been proposed as a new security abstraction at the Instruction Set Architecture (ISA) level. Leakage contracts aim to capture the information that processors leak through their microarchitectural implementations. However, so far, we lack a methodology to verify that a processor actually satisfies a given leakage contract.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623192"}, {"primary_key": "1101812", "vector": [], "sparse_vector": [], "title": "Fine-Grained Data-Centric Content Protection Policy for Web Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The vast amount of sensitive data in modern web applications has become a prime target for cyberattacks. Existing browser security policies disallow the execution of unknown scripts, but do not restrict access to sensitive web content by 'trusted' third-party scripts. Therefore, the over-privileged third-party scripts can compromise the confidentiality and integrity of sensitive user data in the applications.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623217"}, {"primary_key": "1101813", "vector": [], "sparse_vector": [], "title": "Secure and Timely GPU Execution in Cyber-physical Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graphics Processing Units (GPU) are increasingly deployed on Cyber-physical Systems (CPSs), frequently used to perform real-time safety-critical functions, such as object detection on autonomous vehicles. As a result, availability is important for GPU tasks in CPS platforms. However, existing Trusted Execution Environments (TEE) solutions with availability guarantees focus only on CPU computing.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623197"}, {"primary_key": "1101814", "vector": [], "sparse_vector": [], "title": "SymGX: Detecting Cross-boundary Pointer Vulnerabilities of SGX Applications via Static Symbolic Execution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Intel Security Guard Extensions (SGX) have shown effectiveness in critical data protection. Recent symbolic execution-based techniques reveal that SGX applications are susceptible to memory corruption vulnerabilities. While existing approaches focus on conventional memory corruption in ECalls of SGX applications, they overlook an important type of SGX dedicated vulnerability: cross-boundary pointer vulnerabilities. This vulnerability is critical for SGX applications since they heavily utilize pointers to exchange data between secure enclaves and untrusted environments. Unfortunately, none of the existing symbolic execution approaches can effectively detect cross-boundary pointer vulnerabilities due to the lack of an SGX-specific analysis model that properly handles three unique features of SGX applications: Multi-entry Arbitrary-order Execution, Stateful Execution, and Context-aware Pointers. To address such problems, we propose a new analysis model named Global State Transition Graph with Context Aware Pointers (GSTG-CAP) that simulates properties-preserving execution behaviors for SGX applications and drives symbolic execution for vulnerability detection. Based on GSTG-CAP, we build a novel symbolic execution-based vulnerability detector named SYMGX to detect cross-boundary pointer vulnerabilities. According to our evaluation, SYMGX can find 30 0-DAY vulnerabilities in 14 open-source projects, three of which have been confirmed by developers. SYMGX also outperforms two state-of-the-art tools, COIN and TeeRex, in terms of effectiveness, efficiency, and accuracy.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623213"}, {"primary_key": "1101815", "vector": [], "sparse_vector": [], "title": "Uncovering and Exploiting Hidden APIs in Mobile Super Apps.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Mobile applications, particularly those from social media platforms such as WeChat and TikTok, are evolving into \"super apps\" that offer a wide range of services such as instant messaging and media sharing, e-commerce, e-learning, and e-government. These super apps often provide APIs for developers to create \"miniapps\" that run within the super app. These APIs should have been thoroughly scrutinized for security. Unfortunately, we find that many of them are undocumented and unsecured, potentially allowing miniapps to bypass restrictions and gain higher privileged access. To systematically identify these hidden APIs before they are exploited by attackers, we have developed a tool APIScope with both static analysis and dynamic analysis, where static analysis is used to recognize hidden undocumented APIs, and dynamic analysis is used to confirm whether the identified APIs can be invoked by an unprivileged 3rd-party miniapps. We have applied APIScope to five popular super apps (i.e., WeChat, WeCom, Baidu, QQ, and Tiktok) and found that all of them contain hidden APIs, many of which can be exploited due to missing security checks. We have also quantified the hidden APIs that may have security implications by verifying if they have access to resources protected by Android permissions. Furthermore, we demonstrate the potential security hazards by presenting various attack scenarios, including unauthorized access to any web pages, downloading and installing malicious software, and stealing sensitive information. We have reported our findings to the relevant vendors, some of whom have patched the vulnerabilities and rewarded us with bug bounties.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616676"}, {"primary_key": "1101816", "vector": [], "sparse_vector": [], "title": "Securely Sampling Discrete Gaussian Noise for Multi-Party Differential Privacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Differential Privacy (DP) is a widely used technique for protecting individuals' privacy by limiting what can be inferred about them from aggregate data. Recently, there have been efforts to implement DP using Secure Multi-Party Computation (MPC) to achieve high utility without the need for a trusted third party. One of the key components of implementing DP in MPC is noise sampling. Our work presents the first MPC solution for sampling discrete Gaussian, a common type of noise used for constructing DP mechanisms, which plays nicely with malicious secure MPC protocols.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616641"}, {"primary_key": "1101817", "vector": [], "sparse_vector": [], "title": "DPMLBench: Holistic Evaluation of Differentially Private Machine Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Differential privacy (DP), as a rigorous mathematical definition quantifying privacy leakage, has become a well-accepted standard for privacy protection. Combined with powerful machine learning (ML) techniques, differentially private machine learning (DPML) is increasingly important. As the most classic DPML algorithm, DP-SGD incurs a significant loss of utility, which hinders DPML's deployment in practice. Many studies have recently proposed improved algorithms based on DP-SGD to mitigate utility loss. However, these studies are isolated and cannot comprehensively measure the performance of improvements proposed in algorithms. More importantly, there is a lack of comprehensive research to compare improvements in these DPML algorithms across utility, defensive capabilities, and generalizability.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616593"}, {"primary_key": "1101818", "vector": [], "sparse_vector": [], "title": "PolicyChecker: Analyzing the GDPR Completeness of Mobile Apps&apos; Privacy Policies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The European General Data Protection Regulation (GDPR) mandates a data controller (e.g., an app developer) to provide all information specified in Articles (Arts.) 13 and 14 to data subjects (e.g., app users) regarding how their data are being processed and what are their rights. While some studies have started to detect the fulfillment of GDPR requirements in a privacy policy, their exploration only focused on a subset of mandatory GDPR requirements. In this paper, our goal is to explore the state of GDPR-completeness violations in mobile apps' privacy policies. To achieve our goal, we design the PolicyChecker framework by taking a rule and semantic role based approach. PolicyChecker automatically detects completeness violations in privacy policies based not only on all mandatory GDPR requirements but also on all if-applicable GDPR requirements that will become mandatory under specific conditions. Using PolicyChecker, we conduct the first large-scale GDPR-completeness violation study on 205,973 privacy policies of Android apps in the UK Google Play store. PolicyChecker identified 163,068 (79.2%) privacy policies containing data collection statements; therefore, such policies are regulated by GDPR requirements. However, the majority (99.3%) of them failed to achieve the GDPR-completeness with at least one unsatisfied requirement; 98.1% of them had at least one unsatisfied mandatory requirement, while 73.0% of them had at least one unsatisfied if-applicable requirement logic chain. We conjecture that controllers' lack of understanding of some GDPR requirements and their poor practices in composing a privacy policy can be the potential major causes behind the GDPR-completeness violations. We further discuss recommendations for app developers to improve the completeness of their apps' privacy policies to provide a more transparent personal data processing environment to users.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623067"}, {"primary_key": "1101819", "vector": [], "sparse_vector": [], "title": "MicPro: Microphone-based Voice Privacy Protection.", "authors": ["<PERSON><PERSON>", "Xiaoyu Ji", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu"], "summary": "Hundreds of hours of audios are recorded and transmitted over the Internet for voice interactions such as virtual calls or speech recognitions. As these recordings are uploaded, embedded biometric information, i.e., voiceprints, is unnecessarily exposed. This paper proposes the first privacy-enhanced microphone module (i.e., MicPro) that can produce anonymous audio recordings with biometric information suppressed while preserving speech quality for human perception or linguistic content for speech recognition. Limited by the hardware capabilities of microphone modules, previous works that modify recording at the software level are inapplicable. To achieve anonymity in this scenario, MicPro transforms formants, which are distinct for each person due to the unique physiological structure of the vocal organs, and formant transformations are done by modifying the linear spectrum frequencies (LSFs) provided by a popular codec (i.e., CELP) in low-latency communications.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616616"}, {"primary_key": "1101820", "vector": [], "sparse_vector": [], "title": "Geometry of Sensitivity: Twice Sampling and Hybrid Clipping in Differential Privacy with Optimal Gaussian Noise and Application to Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the fundamental problem of the construction of optimal randomization in Differential Privacy (DP). Depending on the clipping strategy or additional properties of the processing function, the corresponding sensitivity set theoretically determines the necessary randomization to produce the required security parameters. Towards the optimal utility-privacy tradeoff, finding the minimal perturbation for properly-selected sensitivity sets stands as a central problem in DP research. In practice, l2/l1-norm clippings with Gaussian/Laplace noise mechanisms are among the most common setups. However, they also suffer from the curse of dimensionality. For more generic clipping strategies, the understanding of the optimal noise for a high-dimensional sensitivity set remains limited. This raises challenges in mitigating the worst-case dimension dependence in privacy-preserving randomization, especially for deep learning applications.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623142"}, {"primary_key": "1101821", "vector": [], "sparse_vector": [], "title": "Unraveling the Connections between Privacy and Certified Robustness in Federated Learning Against Poisoning Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Federated learning (FL) provides an efficient paradigm to jointly train a global model leveraging data from distributed users. As local training data comes from different users who may not be trustworthy, several studies have shown that FL is vulnerable to poisoning attacks. Meanwhile, to protect the privacy of local users, FL is usually trained in a differentially private way (DPFL). Thus, in this paper, we ask: What are the underlying connections between differential privacy and certified robustness in FL against poisoning attacks? Can we leverage the innate privacy property of DPFL to provide certified robustness for FL? Can we further improve the privacy of FL to improve such robustness certification? We first investigate both user-level and instance-level privacy of FL and provide formal privacy analysis to achieve improved instance-level privacy. We then provide two robustness certification criteria: certified prediction and certified attack inefficacy for DPFL on both user and instance levels. Theoretically, we provide the certified robustness of DPFL based on both criteria given a bounded number of adversarial users or instances. Empirically, we conduct extensive experiments to verify our theories under a range of poisoning attacks on different datasets. We find that increasing the level of privacy protection in DPFL results in stronger certified attack inefficacy; however, it does not necessarily lead to a stronger certified prediction. Thus, achieving the optimal certified prediction requires a proper balance between privacy and utility loss.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623193"}, {"primary_key": "1101822", "vector": [], "sparse_vector": [], "title": "Securing NISQ Quantum Computer Reset Operations Against Higher Energy State Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Enabling the sharing of quantum computers among different users requires a secure reset operation that can reset the state of a qubit to ground state |0> and prevent leakage of the state to a post-reset circuit. This work highlights that the existing reset operations available in superconducting qubit NISQ quantum computers are not fully secure. In particular, this work demonstrates for the first time a new type of higher-energy state attack. Although NISQ quantum computers are typically abstracted as working with only energy states |0> and |1>, this work shows that it is possible for unprivileged users to set the qubit state to |2 or |3>. By breaking the abstraction of a two-level system, the new higher-energy state attack can be deployed to affect the operation of circuits or for covert communication between circuits. This work shows that common reset protocols are ineffective in resetting a qubit from a higher-energy state. To provide a defense, this work proposes a new Cascading Secure Reset (CSR) operation. CSR, without hardware modifications, is able to efficiently and reliably reset higher-energy states back to |0>. CSR achieves a reduction in |3> -initialized state leakage channel capacity by between 1 and 2 orders of magnitude, and does so with a 25x speedup compared with the default decoherence reset.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623104"}, {"primary_key": "1101823", "vector": [], "sparse_vector": [], "title": "Exploration of Power Side-Channel Vulnerabilities in Quantum Computer Controllers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rapidly growing interest in quantum computing also increases the importance of securing these computers from various physical attacks. Constantly increasing qubit counts and improvements to the fidelity of the quantum computers hold great promise for the ability of these computers to run novel algorithms with highly sensitive intellectual property. However, in today's cloud-based quantum computer setting, users lack physical control over the computers. Physical attacks, such as those perpetrated by malicious insiders in data centers, could be used to extract sensitive information about the circuits being executed on these computers. This work shows the first exploration and study of power-based side-channel attacks in quantum computers. The explored attacks could be used to recover information about the control pulses sent to these computers. By analyzing these control pulses, attackers can reverse-engineer the equivalent gate-level description of the circuits, and the algorithms being run, or data hard-coded into the circuits. This work introduces five new types of attacks, and evaluates them using control pulse information available from cloud-based quantum computers. This work demonstrates how and what circuits could be recovered, and then in turn how to defend from the newly demonstrated side-channel attacks on quantum computing systems.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623118"}, {"primary_key": "1101824", "vector": [], "sparse_vector": [], "title": "TsuKing: Coordinating DNS Resolvers and Queries into Potent DoS Amplifiers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present a new DNS amplification attack, named T<PERSON><PERSON><PERSON>. Instead of exploiting individual DNS resolvers independently to achieve an amplification effect, <PERSON><PERSON><PERSON><PERSON> deftly coordinates numerous vulnerable DNS resolvers and crafted queries together to form potent DoS amplifiers. We demconstrate that with Tsu<PERSON>ing, an initial small amplification factor can inrease exponentially through the internal layers of coordinated amplifiers, resulting in an extremely powerful amplification attack. TsuKing has three variants, including DNSRetry, DNSChain, and DNSLoop, all of which exploit a suite of inconsistent DNS implementations to achieve enormous amplification effect. With comprehensive measurements, we found that about 14.5% of 1.3M open DNS resolvers are potentially vulnerable to TsuKing. Real-world controlled evaluations indicated that attackers can achieve a packet amplification factor of at least 3,700X (DNSChain). We have reported vulnerabilities to affected vendors and provided them with mitigation recommendations. We have received positive responses from 6 vendors, including Unbound, MikroTik, and AliDNS, and 3 CVEs were assigned. Some of them are implementing our recommendations.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616668"}, {"primary_key": "1101825", "vector": [], "sparse_vector": [], "title": "Poster: Boosting Adversarial Robustness by Adversarial Pre-training.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vision Transformer (ViT) shows superior performance on various tasks, but, similar to other deep learning techniques, it is vulnerable to adversarial attacks. Due to the differences between ViT and traditional CNNs, previous works designed new adversarial training methods as defenses according to the design of ViT, such as blocking attention to individual patches or dropping embeddings with low attention. However, these methods usually focus on fine-tuning stage or the training of the model itself. Improving robustness at the pre-training stage, especially with lower overhead, has yet to be thoroughly investigated. This paper proposes a novel method, Adv-MAE, which increases adversarial robustness by masked adversarial pre-training without a penalty to performance on clean data. We design a simple method to generate adversarial perturbation for the autoencoder, as the autoencoder does not provide classification results. Then, we use masked inputs with perturbation to conduct adversarial training for the autoencoder. The pre-trained autoencoder can be used to build a ViT with better robustness. Our experimental results show that, when using adversarial fine-tuning, Adv-MAE offers better accuracy under adversarial attack than the non-adversarial pre-training method (3.46% higher on CIFAR-10, 1.12% higher on Tiny ImageNet). It also shows better accuracy on clean data (4.94% higher on CIFAR-10, 1.74% higher on Tiny ImageNet), meaning Adv-MAE does not deteriorate performance on clean inputs. In addition, masked pre-training also shows much lower time consumption at each training epoch.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624370"}, {"primary_key": "1101826", "vector": [], "sparse_vector": [], "title": "Poster: Multi-target &amp; Multi-trigger Backdoor Attacks on Graph Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent research has indicated that Graph Neural Networks (GNNs) are vulnerable to backdoor attacks, and existing studies focus on the One-to-One attack where there is a single target triggered by a single backdoor. In this work, we explore two advanced backdoor attacks, i.e., the multi-target and multi-trigger backdoor attacks, on GNNs: 1) One-to-N attack, where there are multiple backdoor targets triggered by controlling different values of the trigger; 2) N-to-One attack, where the attack is only triggered when all the N triggers are present. The initial experimental results illustrate that both attacks can achieve a high attack success rate (up to 99.72%) on GNNs for the node classification task.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624387"}, {"primary_key": "1101827", "vector": [], "sparse_vector": [], "title": "Demo: Certified Robustness on Toolformer.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tool-augmented language models (TALMs) overcome the limitations of current language models (LMs), allowing them to leverage external tools to enhance performance. One state-of-the-art example is Toolformer introduced by Meta AI Research, which achieves a broader integration of tool utilization. However, Toolformer faces particular concerns related to the robustness of its predictions in the optimal positioning for API calls. Adversarial perturbations can alter the position of API calls chosen by <PERSON><PERSON><PERSON><PERSON>, thus resulting in responses that are not only incorrect but potentially even less accurate than those generated by standard language models. To improve the robustness of Toolformer and fulfill the capability of its toolbox, our focus lies on addressing the potential vulnerabilities that arise from small perturbations in the input or prompt space. To achieve this goal, we plan to study adversarial attacks from both attackers' and defenders' perspectives by first studying the adversarial attack algorithms on the input and prompt space, then proposing the certified robustness to the Toolformer API calls scheduling, which is not only empirically effective but also theory-backed.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624362"}, {"primary_key": "1101828", "vector": [], "sparse_vector": [], "title": "PANIC: PAN-assisted Intra-process Memory Isolation on ARM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qijing Li", "<PERSON><PERSON>", "<PERSON><PERSON> Lai", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Intra-process memory isolation is a well-known technique to enforce least privilege within a process. In this paper, we propose a generic and efficient intra-process memory isolation technique named PANIC, by leveraging Privileged Access Never (PAN) and load/store unprivileged (LSU) instructions on AArch64. PANIC executes process code in kernel mode and compartments code into trusted and untrusted components. The untrusted code is restricted from accessing the isolated memory region, which is located on user pages, and the trusted code is allowed to access the isolated memory region by using LSU instructions. To mitigate threats induced by running user code in kernel mode, PANIC provides two novel security mechanisms: shim-based memory isolation and sensitive instruction emulation. PANIC provides a generic and efficient isolation primitive that can be applied in three different isolation scenarios: protecting sensitive data in CFI, creating isolated execution environments, and hardening JIT code cache. We have implemented a prototype of PANIC and experimental evaluation shows that PANIC incurs very low performance overhead, and performs better than existing methods.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623206"}, {"primary_key": "1101829", "vector": [], "sparse_vector": [], "title": "Leakage-Abuse Attacks Against Forward and Backward Private Searchable Symmetric Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>g Yuan", "<PERSON><PERSON>"], "summary": "Dynamic searchable symmetric encryption (DSSE) enables a server to efficiently search and update over encrypted files. To minimize the leakage during updates, a security notion named forward and backward privacy is expected for newly proposed DSSE schemes. Those schemes are generally constructed in a way to break the linkability across search and update queries to a given keyword. However, it remains underexplored whether forward and backward private DSSE is resilient against practical leakage-abuse attacks (LAAs), where an attacker attempts to recover query keywords from the leakage passively collected during queries. In this paper, we aim to be the first to answer this question firmly through two non-trivial efforts. First, we revisit the spectrum of forward and backward private DSSE schemes over the past few years, and unveil some inherent constructional limitations in most schemes. Those limitations allow attackers to exploit query equality and establish a guaranteed linkage among different (refreshed) query tokens surjective to a candidate keyword. Second, we refine volumetric leakage profiles of updates and queries by associating each with a specific operation. By further exploiting update volume and query response volume, we demonstrate that all forward and backward private DSSE schemes can leak the same volumetric information (e.g., insertion volume, deletion volume) as those without such security guarantees. To testify our findings, we realize two generic LAAs, i.e., frequency matching attack and volumetric inference attack, and we evaluate them over various experimental settings in the dynamic context. Finally, we call for new efficient schemes to protect query equality and volumetric information across search and update queries.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623085"}, {"primary_key": "1101830", "vector": [], "sparse_vector": [], "title": "Efficient Multiplicative-to-Additive Function from Joye-Libert Cryptosystem and Its Application to Threshold ECDSA.", "authors": ["<PERSON><PERSON> Xue", "<PERSON>", "Mengling Liu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "Threshold ECDSA receives interest lately due to its widespread adoption in blockchain applications. A common building block of all leading constructions involves a secure conversion of multiplicative shares into additive ones, which is called the multiplicative-to-additive (MtA) function. MtA dominates the overall complexity of all existing threshold ECDSA constructions. Specifically, O(n2) invocations of MtA are required in the case of n active signers. Hence, improvement of MtA leads directly to significant improvements for all state-of-the-art threshold ECDSA schemes.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616595"}, {"primary_key": "1101831", "vector": [], "sparse_vector": [], "title": "Whole-Program Control-Flow Path Attestation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Path attestation is an approach to remotely attest the execution of a program ℘. In path attestation, a prover platform, which executes ℘, convinces a remote verifier V of the integrity of ℘ by recording the path that ℘ takes as it executes a particular input. While a number of prior techniques have been developed for path attestation, they have generally been applied to record paths only for parts of the execution of ℘. In this paper, we consider the problem of whole program control-flow path attestation, i.e., to attest the execution of the entire program path in ℘. We show that prior approaches for path attestation use sub-optimal techniques that fundamentally fail to scale to whole program paths, and impose a large runtime overhead on the execution of ℘. We then develop Blast, an approach that reduces these overheads using a number of novel approaches inspired by prior work from the program profiling literature. Our experiments show that Blast makes path attestation more practical for use on a wide variety of embedded programs.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616687"}, {"primary_key": "1101832", "vector": [], "sparse_vector": [], "title": "Uncle Maker: (Time)Stamping Out The Competition in Ethereum.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present and analyze an attack on Ethereum 1's consensus mechanism, which allows miners to obtain higher mining rewards compared to their honest peers. This attack is novel in that it relies on manipulating block timestamps and the difficulty-adjustment algorithm (DAA) to give the miner an advantage whenever block races ensue. We call our attack Uncle Maker, as it induces a higher rate of uncle blocks. We describe several variants of the attack. Among these, one that is risk-free for miners.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616674"}, {"primary_key": "1101833", "vector": [], "sparse_vector": [], "title": "Poster: Signer Discretion is Advised: On the Insecurity of Vitalik&apos;s Threshold Hash-based Signatures.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We show that the Lamport threshold signature scheme proposed by <PERSON><PERSON> is not existentially unforgeable under chosen message attacks (EU-CMA). In this work, we formalize the proposed threshold hash-based signature scheme, and show an attack that results in a 60-bit security reduction. Our attack completes in seconds in a setting with a single malicious adversary (the leader of a consensus round), thus contradicting the claim that even with 96 malicious colluding participants (out of a total of 256), an adversary can only make a signature for approximately 1 in 280 possible values. In summary, the original estimated security analysis of the proposed threshold signature scheme claimed security against an adversary in control of approximately a year of continuous work from the entire bitcoin network. Our attack, however, runs in seconds using a commodity laptop.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624393"}, {"primary_key": "1101834", "vector": [], "sparse_vector": [], "title": "Enhancing OSS Patch Backporting with Semantics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Keeping open-source software (OSS) up to date is one potential solution to prevent known vulnerabilities. However, it requires frequent and costly testing and may introduce compatibility issues. Consequently, developers often choose to backport security patches to the vulnerable versions instead. Manual backporting is time-consuming, especially for large OSS such as the Linux kernel. Therefore, automating this process is urgently needed to save considerable time. Existing automated approaches for backporting patches involve either automatic patch generation or automatic patch migration. However, these methods are often ineffective and error-prone since they failed to locate the precise patch locations or generate the correct patch, operating only on the syntactic level.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623188"}, {"primary_key": "1101835", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> and <PERSON>: Batched and Non-batched Branching for Interactive ZK.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Vector Oblivious Linear Evaluation (VOLE) supports fast and scalable interactive Zero-Knowledge (ZK) proofs. Despite recent improvements to VOLE-based ZK, compiling proof statements to a control-flow oblivious form (e.g., a circuit) continues to lead to expensive proofs. One useful setting where this inefficiency stands out is when the statement is a disjunction of clauses \\mathcalL _1 łor \\cdots łor \\mathcalL _B. Typically, ZK requires paying the price to handle all B branches. Prior works have shown how to avoid this price in communication, but not in computation.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623169"}, {"primary_key": "1101836", "vector": [], "sparse_vector": [], "title": "Towards Generic MPC Compilers via Variable Instruction Set Architectures (VISAs).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In MPC, we usually represent programs as circuits. This is a poor fit for programs that use complex control flow, as it is costly to compile control flow to circuits. This motivated prior work to emulate CPUs inside MPC. Emulated CPUs can run complex programs, but they introduce high overhead due to the need to evaluate not just the program, but also the machinery of the CPU, including fetching, decoding, and executing instructions, accessing RAM, etc.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616664"}, {"primary_key": "1101837", "vector": [], "sparse_vector": [], "title": "Take Over the Whole Cluster: Attacking Kubernetes via Excessive Permissions of Third-party Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Shen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the dominant container orchestration system, Kubernetes is widely used by many companies and cloud vendors. It runs third-party add-ons and applications (termed third-party apps) on its control plane to manage the whole cluster. The security of these third-party apps is critical to the whole cluster but has not been systematically studied so far.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623121"}, {"primary_key": "1101838", "vector": [], "sparse_vector": [], "title": "CoCo: Efficient Browser Extension Vulnerability Detection via Coverage-guided, Concurrent Abstract Interpretation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Song Li", "<PERSON><PERSON>", "<PERSON><PERSON> Cao"], "summary": "Extensions complement web browsers with additional functionalities and also bring new vulnerability venues, allowing privilege escalations from adversarial web pages to use extension APIs. Prior works on extension vulnerability detection adopt classic static analysis, which is unable to handle dynamic JavaScript features such as those function calls as part of array lookups. At the same time, prior abstract interpretation focuses on lightweight server-side JavaScript, which often cannot scale to client-side extension code due to object explosions in the abstract domain.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616584"}, {"primary_key": "1101839", "vector": [], "sparse_vector": [], "title": "Poster: Combining Fuzzing with Concolic Execution for IoT Firmware Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The supply of IoT devices is increasing year by year. Even in industries that demand sophistication, such as unmanned driving, construction, and robotics industry, IoT devices are being utilized. However, the security of IoT devices is lagging behind this development due to their diverse types and challenging firmware execution environments. The existing methods, such as direct device connectivity or partial emulation, are used to solve this. However, full system emulation is better suited for the large-scale analysis, because it can test many firmwares without requiring devices. Therefore, recent studies have integrated emulation and software testing techniques such as fuzzing, but they are still unsuitable for testing various firmware and inefficient. In this poster, we propose FirmColic, which combines fuzzing with concolic execution to mitigate these limitations. FirmColic is a type of augmented process emulation, which improves the effectiveness of fuzzing using keyword extraction based on concolic execution. Also, we apply five arbitration techniques in an augmented process emulation environment for the high success rates of the emulation. We prove that FirmColic has faster detection, more crash detection, and a higher code coverage than the previous studies.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624373"}, {"primary_key": "1101840", "vector": [], "sparse_vector": [], "title": "AntiFake: Using Adversarial Audio to Prevent Unauthorized Speech Synthesis.", "authors": ["Z<PERSON><PERSON> Yu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rapid development of deep neural networks and generative AI has catalyzed growth in realistic speech synthesis. While this technology has great potential to improve lives, it also leads to the emergence of ''DeepFake'' where synthesized speech can be misused to deceive humans and machines for nefarious purposes. In response to this evolving threat, there has been a significant amount of interest in mitigating this threat by DeepFake detection.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623209"}, {"primary_key": "1101841", "vector": [], "sparse_vector": [], "title": "Poster: Unveiling the Impact of Patch Placement: Adversarial Patch Attacks on Monocular Depth Estimation.", "authors": ["Gyungeun <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For autonomous driving systems, cameras and LiDAR sensors are necessary devices that provide precise depth information by which positions and sizes of objects can be identified. Moreover, recent advances in deep learning have extended their capabilities to include monocular camera setup for depth estimation. Compared with the conventional devices like LiDAR or stereo cameras for the depth estimation, the monocular camera enables to estimate depths with a low cost. It is known that the depth estimation models for the monocular camera are vulnerable to adversarial examples. However, most adversarial attacks against the monocular depth estimation have been conducted with targeted patches that are placed on a target object. It is known that the targeted patch outperforms the adjacent and remote patch that is placed beyond the target object, when it comes to an attack success rate. However, the adjacent and remote patch would provide high flexibility in patch placement, as it can be placed beyond the target object's scope. In this paper, we experimentally confirm that the patch placement significantly affects the attack success rates, particularly in specific regions.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624400"}, {"primary_key": "1101842", "vector": [], "sparse_vector": [], "title": "SpecVerilog: Adapting Information Flow Control for Secure Speculation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "To address transient execution vulnerabilities, processor architects have proposed both defensive designs and formal descriptions of the security they provide. However, these designs are not typically formally proven to enforce the claimed guarantees; more importantly, there are few tools to automatically ensure that Register Transfer Level (RTL) descriptions are faithful to high-level designs.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623074"}, {"primary_key": "1101843", "vector": [], "sparse_vector": [], "title": "RetSpill: Igniting User-Controlled Data to Burn Away Linux Kernel Protections.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Leveraging a control flow hijacking primitive (CFHP) to gain root privileges is critical to attackers striving to exploit Linux kernel vulnerabilities. Such attack has become increasingly elusive as security researchers propose capable kernel security mitigations, leading to the development of complex (and, as a trade-off, brittle and unreliable) attack techniques to regain it. In this paper, we obviate the need for complexity by proposing RetSpill, a powerful yet elegant exploitation technique that employs user space data already present on the kernel stack for privilege escalation.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623220"}, {"primary_key": "1101844", "vector": [], "sparse_vector": [], "title": "Narcissus: A Practical Clean-Label Backdoor Attack with Limited Information.", "authors": ["<PERSON>", "Minzhou Pan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Backdoor attacks introduce manipulated data into a machine learning model's training set, causing the model to misclassify inputs with a trigger during testing to achieve a desired outcome by the attacker. For backdoor attacks to bypass human inspection, it is essential that the injected data appear to be correctly labeled. The attacks with such property are often referred to as \"clean-label attacks.\" The success of current clean-label backdoor methods largely depends on access to the complete training set. Yet, accessing the complete dataset is often challenging or unfeasible since it frequently comes from varied, independent sources, like images from distinct users. It remains a question of whether backdoor attacks still present real threats.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616617"}, {"primary_key": "1101845", "vector": [], "sparse_vector": [], "title": "Don&apos;t Leak Your Keys: Understanding, Measuring, and Exploiting the AppSecret Leaks in Mini-Programs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Mobile mini-programs in WeChat have gained significant popularity since their debut in 2017, reaching a scale similar to that of Android apps in the Play Store. Like Google, Tencent, the provider of WeChat, offers APIs to support the development of mini-programs and also maintains a mini-program market within the WeChat app. However, mini-program APIs often manage sensitive user data within the social network platform, both on the WeChat client app and in the cloud. As a result, cryptographic protocols have been implemented to secure data access. In this paper, we demonstrate that WeChat should have required the use of the \"appsecret\" master key, which is used to authenticate a mini-program, to be used only in the mini-program back-end. If this key is leaked in the front-end of the mini-programs, it can lead to catastrophic attacks on both mini-program developers and users. Using a mini-program crawler and a master key leakage inspector, we measured 3,450,586 crawled mini-programs and found that 40,880 of them had leaked their master keys, allowing attackers to carry out various attacks such as account hijacking, promotion abuse, and service theft. Similar issues were confirmed through testing and measuring of Baidu mini-programs too. We have reported these vulnerabilities and the list of vulnerable mini-programs to Ten<PERSON> and Baidu, which awarded us with bug bounties, and also Tencent recently released a new API to defend against these attacks based on our findings.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616591"}, {"primary_key": "1101846", "vector": [], "sparse_vector": [], "title": "TunneLs for Bootlegging: <PERSON><PERSON>erse-Engineering GPU TLBs for Challenging Isolation Guarantees of NVIDIA MIG.", "authors": ["<PERSON><PERSON><PERSON> Zhang", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Ge"], "summary": "Recent studies have revealed much detailed information about the translation lookaside buffers (TLBs) of modern CPUs, but we find that many properties of such components in modern GPUs still remain unknown or unclear. To fill this knowledge gap, we develop a new GPU TLB reverse-engineering method and apply it to a variety of consumer- and server-grade GPUs in Turing and Ampere generations. Aside from learning significantly more comprehensive and accurate GPU TLB properties, we discover a design flaw of NVIDIA Multi-Instance GPU (MIG) feature. MIG claims full partitioning of the entire GPU memory system for secure GPU sharing in cloud computing. However, we surprisingly find that MIG does not partition the last-level TLB, which is shared by all the compute units in a GPU. Exploiting this design flaw and learned TLB properties, we are able to construct a covert channel for data exfiltration across MIG-enforced isolation. To the best of our knowledge, this is the first attack on MIG. We evaluate the proposed attack on a commercial cloud platform, and we successfully achieve reliable data exfiltration from a victim tenant at a speed of up to 31 kbps with a very high accuracy around 99.8%. Even when the victim is using the GPU for deep neural network training, the transmission can still reach more than 25 kbps with a more than 99.5% accuracy. We propose and implement a mitigation approach that can effectively thwart data exfiltration through this covert channel. Additionally, we present a preliminary study on exploiting the access patterns of the last-level TLB to infer the identity of applications running in other MIG-created GPU instances.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616672"}, {"primary_key": "1101847", "vector": [], "sparse_vector": [], "title": "Under the Dark: A Systematical Study of Stealthy Mining Pools (Ab)use in the Wild.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cryptocurrency mining is a crucial operation in blockchains, and miners often join mining pools to increase their chances of earning rewards. However, the energy-intensive nature of PoW cryptocurrency mining has led to its ban in New York State of the United States, China, and India. As a result, mining pools, serving as a central hub for mining activities, have become prime targets for regulatory enforcement. Furthermore, cryptojacking malware refers to self-owned stealthy mining pools to evade detection techniques and conceal profit wallet addresses. However, no systematic research has been conducted to analyze it, largely due to a lack of full understanding of the protocol implementation, usage, and port distribution of the stealth mining pool.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616677"}, {"primary_key": "1101848", "vector": [], "sparse_vector": [], "title": "MTD &apos;23: 10th ACM Workshop on Moving Target Defense.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The tenth ACM Workshop on Moving Target Defense (MTD) is held on November 26, 2023, in conjunction with the ACM Conference on Computer and Communications Security (CCS). The main objective of the workshop is to discuss novel randomization, diversification, and dynamism techniques for computer systems and network, new metric and analysis frameworks to assess and quantify the effectiveness of MTD, and discuss challenges and opportunities that such defenses provide. We have constructed an exciting and diverse program of six refereed papers, and two invited keynote talks that will provide the participants with a vibrant and thought-provoking set of ideas and insights.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3624022"}, {"primary_key": "1101849", "vector": [], "sparse_vector": [], "title": "Silence is not Golden: Disrupting the Load Balancing of Authoritative DNS Servers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Lu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Authoritative nameservers are delegated to provide the final resource record. Since the security and robustness of DNS are critical to the general operation of the Internet, domain name owners are required to deploy multiple candidate nameservers for traffic load balancing. Once the load balancing mechanism is compromised, an adversary can manipulate a large number of legitimate DNS requests to a specified candidate nameserver. As a result, it may not only bypass the defense mechanisms used to filter malicious traffic that can overload the victim nameserver, but also lowers the bar for DNS traffic hijacking and cache poisoning attacks.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616647"}, {"primary_key": "1101850", "vector": [], "sparse_vector": [], "title": "Profile-guided System Optimizations for Accelerated Greybox Fuzzing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Greybox fuzzing is a highly popular option for security testing, incentivizing tremendous efforts to improve its performance. Prior research has brought many algorithmic advancements, leading to substantial performance growth. However, less attention has been paid to the system-level designs of greybox fuzzing tools, despite the high impacts of such designs on fuzzing throughput.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616636"}, {"primary_key": "1101851", "vector": [], "sparse_vector": [], "title": "FaceReader: Unobtrusively Mining Vital Signs and Vital Sign Embedded Sensitive Info via AR/VR Motion Sensors.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The market size of augmented reality and virtual reality (AR/VR) has been expanding rapidly in recent years, with the use of face-mounted headsets extending beyond gaming to various application sectors, such as education, healthcare, and the military. Despite the rapid growth, the understanding of information leakage through sensor-rich headsets remains in its infancy. Some of the headset's built-in sensors do not require users' permission to access, and any apps and websites can acquire their readings. While theseunrestricted sensors are generally considered free of privacy risks, we find that an adversary could uncover private information by scrutinizing sensor readings, making existing AR/VR apps and websites potential eavesdroppers. In this work, we investigate a novel, unobtrusive privacy attack called FaceReader, which reconstructs high-quality vital sign signals (breathing and heartbeat patterns) based on unrestricted AR/VR motion sensors. FaceReader is built on the key insight that the headset is closely mounted on the user's face, allowing the motion sensors to detect subtle facial vibrations produced by users' breathing and heartbeats. Based on the reconstructed vital signs, we further investigate three more advanced attacks, including gender recognition, user re-identification, and body fat ratio estimation. Such attacks pose severe privacy concerns, as an adversary may obtain users' sensitive demographic/physiological traits and potentially uncover their real-world identities. Compared to prior privacy attacks relying on speeches and activities, FaceReader targets spontaneous breathing and heartbeat activities that are naturally produced by the human body and are unobtrusive to victims. In particular, we design an adaptive filter to dynamically mitigate the impacts of body motions. We further employ advanced deep-learning techniques to reconstruct vital sign signals, achieving signal qualities comparable to those of dedicated medical instruments, as well as deriving sensitive gender, identity, and body fat information. We conduct extensive experiments involving 35 users on three types of mainstream AR/VR headsets across 3 months. The results reveal that FaceReader can reconstruct vital signs with low mean errors and accurately detect gender (over 93.33%). The attack can also link/re-identify users across different apps, websites, and longitudinal sessions with over 97.83% accuracy. Furthermore, we present the first successful attempt at revealing body fat information from motion sensor data, achieving a remarkably low estimation error of 4.43%.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3623102"}, {"primary_key": "1101852", "vector": [], "sparse_vector": [], "title": "Galápagos: Developing Verified Low Level Cryptography on Heterogeneous Hardwares.", "authors": ["<PERSON>", "<PERSON> Gibson", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The proliferation of new hardware designs makes it difficult to produce high-performance cryptographic implementations tailored at the assembly level to each platform, let alone to prove such implementations correct. Hence we introduce Galápagos, an extensible framework designed to reduce the effort of verifying cryptographic implementations across different ISAs.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616603"}, {"primary_key": "1101853", "vector": [], "sparse_vector": [], "title": "TileMask: A Passive-Reflection-based Attack against mmWave Radar Object Detection in Autonomous Driving.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yunnan Yu", "<PERSON><PERSON><PERSON>", "Lu <PERSON>", "<PERSON><PERSON>"], "summary": "In autonomous driving, millimeter wave (mmWave) radar has been widely adopted for object detection because of its robustness and reliability under various weather and lighting conditions. For radar object detection, deep neural networks (DNNs) are becoming increasingly important because they are more robust and accurate, and can provide rich semantic information about the detected objects, which is critical for autonomous vehicles (AVs) to make decisions. However, recent studies have shown that DNNs are vulnerable to adversarial attacks. Despite the rapid development of DNN-based radar object detection models, there have been no studies on their vulnerability to adversarial attacks. Although some spoofing attack methods are proposed to attack the radar sensor by actively transmitting specific signals using some special devices, these attacks require sub-nanosecond-level synchronization between the devices and the radar and are very costly, which limits their practicability in real world. In addition, these attack methods can not effectively attack DNN-based radar object detection. To address the above problems, in this paper, we investigate the possibility of using a few adversarial objects to attack the DNN-based radar object detection models through passive reflection. These objects can be easily fabricated using 3D printing and metal foils at low cost. By placing these adversarial objects at some specific locations on a target vehicle, we can easily fool the victim <PERSON><PERSON>'s radar object detection model. The experimental results demonstrate that the attacker can achieve the attack goal by using only two adversarial objects and conceal them as car signs, which have good stealthiness and flexibility. To the best of our knowledge, this is the first study on the passive-reflection-based attacks against the DNN-based radar object detection models using low-cost, readily-available and easily concealable geometric shaped objects.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915.3616661"}, {"primary_key": "1276302", "vector": [], "sparse_vector": [], "title": "Proceedings of the 2023 ACM SIGSAC Conference on Computer and Communications Security, CCS 2023, Copenhagen, Denmark, November 26-30, 2023", "authors": ["<PERSON><PERSON> Meng", "<PERSON>", "Cas Cremers", "<PERSON><PERSON>"], "summary": "On behalf of the ACM CCS 2023 Organizing Committee, we welcome you to the 30th ACM SIGSAC Conference on Computer and Communications Security (CCS). ACM CCS continues to be the premier security conference, where researchers, practitioners, and educators come together to present, learn, and debate research, innovation, and trends in the field of Computer and Communications Security. This is the first time ACM CCS is held in Copenhagen Denmark, and we are happy to be back to a fully physical event after COVID-19. Copenhagen ranks high amongst the livable cities in the world and the size matches the national population of 6 million Danes. We like to think that the city is small, but still offers some of the urban metropolis vibe that is associated with capitals around the world. November in Denmark is the foundation of the Nordic noir genre of tv-series and detective novels, but despite the darkness and the inclement weather, we hope that you will explore the city and enjoy your stay in Copenhagen. This year's main conference is one of the largest with 235 paper presentations over three days. We are also honored to have two distinguished keynote speakers for the main conference: <PERSON> from Duke University (USA), and <PERSON><PERSON> from Carnegie Mellon University (USA). In addition, fourteen workshops and one tutorial will take place on the pre-conference and postconference days to discuss numerous specialized topics.", "published": "2023-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3576915"}]