[{"primary_key": "2206164", "vector": [], "sparse_vector": [], "title": "From Multisets over Distributions to Distributions over Multisets.", "authors": ["<PERSON>"], "summary": "A well-known challenge in the semantics of programming languages is how to combine non-determinism and probability. At a technical level, the problem arises from the fact that there is a no distributive law between the powerset monad and the distribution monad - as noticed some twenty years ago by <PERSON><PERSON><PERSON>. More recently, it has become clear that there is a distributive law of the multiset monad over the distribution monad. This article elaborates the details of this distributivity and shows that there is a rich underlying theory relating multisets and probability distributions. It is shown that the new distributive law, called parallel multinomial law, can be defined in (at least) four equivalent ways. It involves putting multinomial distributions in parallel and commutes with hypergeometric distributions. Further, it is shown that this distributive law commutes with a new form of zipping for multisets. Abstractly, this can be described in terms of monoidal structure for a fixed-size multiset functor, when lifted to the K<PERSON><PERSON><PERSON> category of the distribution monad. Concretely, an application of the theory to sampling semantics is included.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470678"}, {"primary_key": "2206165", "vector": [], "sparse_vector": [], "title": "Efficient Local Computation of Differential Bisimulations via Coupling and Up-to Methods.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Mirco Tribastone", "<PERSON>", "<PERSON>"], "summary": "We introduce polynomial couplings, a generalization of probabilistic couplings, to develop an algorithm for the computation of equivalence relations which can be interpreted as a lifting of probabilistic bisimulation to polynomial differential equations, a ubiquitous model of dynamical systems across science and engineering. The algorithm enjoys polynomial time complexity and complements classical partition-refinement approaches because: (a) it implements a local exploration of the system, possibly yielding equivalences that do not necessarily involve the inspection of the whole system of differential equations; (b) it can be enhanced by up-to techniques; and (c) it allows the specification of pairs which ought not be included in the output. Using a prototype, these advantages are demonstrated on case studies from systems biology for applications to model reduction and comparison. Notably, we report four orders of magnitude smaller runtimes than partition-refinement approaches when disproving equivalences between Markov chains.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470555"}, {"primary_key": "2206166", "vector": [], "sparse_vector": [], "title": "Evidenced Frames: A Unifying Framework Broadening Realizability Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Constructive foundations have for decades been built upon realizability models for higher-order logic and type theory. However, traditional realizability models have a rather limited notion of computation, which only supports non-termination and avoids many other commonly used effects. Work to address these limitations has typically overlaid structure on top of existing models, such as by using powersets to represent non-determinism, but kept the realizers themselves deterministic. This paper alternatively addresses these limitations by making the structure underlying realizability models more flexible. To this end, we introduce evidenced frames: a general-purpose framework for building realizability models that support diverse effectful computations. We demonstrate that this flexibility permits models wherein the realizers themselves can be effectful, such as λ-terms that can manipulate state, reduce non-deterministically, or fail entirely. Beyond the broader notions of computation, we demonstrate that evidenced frames form a unifying framework for (realizability) models of higher-order dependent predicate logic. In particular, we prove that evidenced frames are complete with respect to these models, and that the existing completeness construction for implicative algebras-another foundational framework for realizability-factors through our simpler construction. As such, we conclude that evidenced frames offer an ideal domain for unifying and broadening realizability models.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470514"}, {"primary_key": "2206167", "vector": [], "sparse_vector": [], "title": "Fusible numbers and P<PERSON>o Ari<PERSON>ic.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Inspired by a mathematical riddle involving fuses, we define the fusible numbers as follows: 0 is fusible, and whenever x, y are fusible with |y - x| - 1 ≥ F ε0 (n - c) for some constant c, where Fα denotes the fast-growing hierarchy.Finally, we derive some true statements that can be formulated but not proven in Peano Arithmetic, of a different flavor than previously known such statements: PA cannot prove the true statement \"For every natural number n there exists a smallest fusible number larger than n.\" Also, consider the algorithm \"M(x): if x <; 0 return -x, else return M(x - M(x - 1))/2.\" Then M terminates on real inputs, although PA cannot prove the statement \"M terminates on all natural inputs.\"", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470703"}, {"primary_key": "2206168", "vector": [], "sparse_vector": [], "title": "A Complete Axiomatisation for Divergence Preserving Branching Congruence of Finite-State Behaviours.", "authors": ["<PERSON><PERSON><PERSON>", "Tingting Yu"], "summary": "We present an equational inference system for finite-state expressions, and prove that the system is sound and complete with respect to divergence preserving branching congruence, closing a problem that has been open since 1993. The inference system refines <PERSON>'s simple and elegant complete axiomatisation for branching bisimulation congruence of finite-state behaviours by joining four simple axioms after dropping one axiom which is unsound under the more refined divergence sensitive semantics.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470647"}, {"primary_key": "2206169", "vector": [], "sparse_vector": [], "title": "Smart Choices and the Selection Monad.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Describing systems in terms of choices and their resulting costs and rewards promises to free algorithm designers and programmers from specifying how to make those choices. In implementations, the choices can be realized by optimization or machine-learning methods.We study this approach from a programming-language perspective. We define a small language that supports decision-making abstraction, rewards, and probabilities. We give a globally optimizing operational semantics, and, using the selection monad for decision-making, three denotational semantics with auxiliary monads for reward and probability; the three model various correlations between returned values and expected rewards. We show the two kinds of semantics coincide by proving adequacy theorems; we show that observational equivalence is characterized by semantic equality (at basic types) by proving full abstraction theorems; and we discuss program equations.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470641"}, {"primary_key": "2206170", "vector": [], "sparse_vector": [], "title": "Comonadic semantics for guarded fragments.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In previous work ([1], [2], [3]), it has been shown how a range of model comparison games which play a central role in finite model theory, including Ehrenfeucht-Fraïssé, pebbling, and bisimulation games, can be captured in terms of resource-indexed comonads on the category of relational structures. Moreover, the coalgebras for these comonads capture important combinatorial parameters such as tree-width and tree-depth.The present paper extends this analysis to quantifier-guarded fragments of first-order logic. We give a systematic account, covering atomic, loose and clique guards. In each case, we show that coKleisli morphisms capture winning strategies for Duplicator in the existential guarded bisimulation game, while back-and-forth bisimulation, and hence equivalence in the full guarded fragment, is captured by spans of open morphisms. We study the coalgebras for these comonads, and show that they correspond to guarded tree decompositions. We relate these constructions to a syntax-free setting, with a comonad on the category of hypergraphs.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470594"}, {"primary_key": "2206171", "vector": [], "sparse_vector": [], "title": "Strong Call-by-Value is Reasonable, Implosively.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Whether the number of β -steps in the λ-calculus can be taken as a reasonable time cost model (that is, polynomially related to the one of Turing machines) is a delicate problem, which depends on the notion of evaluation strategy. Since the nineties, it is known that weak (that is, out of abstractions) call-by-value evaluation is a reasonable strategy while <PERSON><PERSON><PERSON>'s optimal parallel strategy, which is strong (that is, it reduces everywhere), is not. The strong case turned out to be subtler than the weak one. In 2014 <PERSON><PERSON><PERSON><PERSON> and <PERSON> have shown that strong call-by-name is reasonable, by introducing a new form of useful sharing and, later, an abstract machine with an overhead quadratic in the number of β-steps.Here we show that also strong call-by-value evaluation is reasonable for time, via a new abstract machine realizing useful sharing and having a linear overhead. Moreover, our machine uses a new mix of sharing techniques, adding on top of useful sharing a form of implosive sharing, which on some terms brings an exponential speed-up. We give examples of families that the machine executes in time logarithmic in the number of β-steps.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470630"}, {"primary_key": "2206172", "vector": [], "sparse_vector": [], "title": "The Space of Interaction.", "authors": ["<PERSON><PERSON><PERSON>", "Ugo <PERSON>", "<PERSON><PERSON>"], "summary": "The space complexity of functional programs is not well understood. In particular, traditional implementation techniques are tailored to time efficiency, and space efficiency induces time inefficiencies, as it prefers re-computing to saving. <PERSON><PERSON><PERSON>'s geometry of interaction underlies an alternative approach based on the interaction abstract machine (IAM), claimed as space efficient in the literature. It has also been conjectured to provide a reasonable notion of space for the λ-calculus, but such an important result seems to be elusive.In this paper we introduce a new intersection type system precisely measuring the space consumption of the IAM on the typed term. Intersection types have been repeatedly used to measure time, which they achieve by dropping idempotency, turning intersections into multisets. Here we show that the space consumption of the IAM is connected to a further structural modification, turning multisets into trees. Tree intersection types lead to a finer understanding of some space complexity results from the literature. They also shed new light on the conjecture about reasonable space: we show that the usual way of encoding Turing machines into the λ-calculus cannot be used to prove that the space of the IAM is a reasonable cost model.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470726"}, {"primary_key": "2206173", "vector": [], "sparse_vector": [], "title": "In search of lost time: Axiomatising parallel composition in process algebras.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Valentina Castiglioni", "<PERSON>", "<PERSON><PERSON>"], "summary": "This survey reviews some of the most recent achievements in the saga of the axiomatisation of parallel composition, along with some classic results. We focus on the recursion, relabelling and restriction free fragment of CCS and we discuss the solutions to three problems that were open for many years. The first problem concerns the status of <PERSON><PERSON> and <PERSON><PERSON>'s auxiliary operators left merge and communication merge in the finite axiomatisation of parallel composition modulo bisimiliarity: We argue that, under some natural assumptions, the addition of a single auxiliary binary operator to CCS does not yield a finite axiomatisation of bisimilarity. Then we delineate the boundary between finite and non-finite axiomatisability of the congruences in <PERSON>'s linear time-branching time spectrum over CCS. Finally, we present a novel result to the effect that rooted weak bisimilarity has no finite complete axiomatisation over CCS.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470526"}, {"primary_key": "2206174", "vector": [], "sparse_vector": [], "title": "Axiomatizations and Computability of Weighted Monadic Second-Order Logic.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Weighted monadic second-order logic is a weighted extension of monadic second-order logic that captures exactly the behaviour of weighted automata. Its semantics is parameterized with respect to a semiring on which the values that weighted formulas output are evaluated. <PERSON><PERSON> and <PERSON><PERSON> (2018) gave abstract semantics for a version of weighted monadic second-order logic to give a more general and modular proof of the equivalence of the logic with weighted automata. We focus on the abstract semantics of the logic and we give a complete axiomatization both for the full logic and for a fragment without general sum, thus giving a more fine-grained understanding of the logic. We discuss how common decision problems for logical languages can be adapted to the weighted setting, and show that many of these are decidable, though they inherit bad complexity from the underlying first- and second-order logics. However, we show that a weighted adaptation of satisfiability is undecidable for the logic when one uses the abstract interpretation.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470615"}, {"primary_key": "2206175", "vector": [], "sparse_vector": [], "title": "Universal Semantics for the Stochastic λ-Calculus.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We define sound and adequate denotational and operational semantics for the stochastic lambda calculus. These two semantic approaches build on previous work that used an explicit source of randomness to reason about higher-order probabilistic programs.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470747"}, {"primary_key": "2206176", "vector": [], "sparse_vector": [], "title": "On the Expressive Power of Homomorphism Counts.", "authors": ["<PERSON>", "Phokion <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A classical result by <PERSON><PERSON><PERSON><PERSON> asserts that two graphs G and H are isomorphic if and only if they have the same left profile, that is, for every graph F, the number of homomorphisms from F to G coincides with the number of homomorphisms from F to H. <PERSON> and later on <PERSON>, <PERSON>, and <PERSON><PERSON> showed that restrictions of the left profile to a class of graphs can capture several different relaxations of isomorphism, including equivalence in counting logics with a fixed number of variables (which contains fractional isomorphism as a special case) and co-spectrality (i.e., two graphs having the same characteristic polynomial). On the other side, a result by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> asserts that isomorphism is also captured by the right profile, that is, two graphs G and H are isomorphic if and only if for every graph F, the number of homomorphisms from G to F coincides with the number of homomorphisms from H to F. In this paper, we embark on a study of the restrictions of the right profile by investigating relaxations of isomorphism that can or cannot be captured by restricting the right profile to a fixed class of graphs. Our results unveil striking differences between the expressive power of the left profile and the right profile. We show that fractional isomorphism, equivalence in counting logics with a fixed number of variables, and co-spectrality cannot be captured by restricting the right profile to a class of graphs. In the opposite direction, we show that chromatic equivalence cannot be captured by restricting the left profile to a class of graphs, while, clearly, it can be captured by restricting the right profile to the class of all cliques.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470543"}, {"primary_key": "2206177", "vector": [], "sparse_vector": [], "title": "Categories of Nets.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jade Master", "<PERSON>"], "summary": "We present a unified framework for Petri nets and various variants, such as pre-nets and <PERSON><PERSON>'s whole-grain Petri nets. Our framework is based on a less well-studied notion that we call Σ-nets, which allow fine-grained control over whether each transition behaves according to the collective or individual token philosophy. We describe three forms of execution semantics in which pre-nets generate strict monoidal categories, Σ-nets (including whole-grain Petri nets) generate symmetric strict monoidal categories, and Petri nets generate commutative monoidal categories, all by left adjoint functors. We also construct adjunctions relating these categories of nets to each other, in particular showing that all kinds of net can be embedded in the unifying category of Σ-nets, in a way that commutes coherently with their execution semantics.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470566"}, {"primary_key": "2206178", "vector": [], "sparse_vector": [], "title": "Decidability and Complexity in Weakening and Contraction Hypersequent Substructural Logics.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We establish decidability for the infinitely many axiomatic extensions of the commutative Full Lambek logic with weakening FLew (i.e. IMALLW) that have a cut-free hypersequent proof calculus. Specifically: every analytic structural rule extension of HFLew. Decidability for the corresponding extensions of its contraction counterpart FLec was established recently but their computational complexity was left unanswered. In the second part of this paper, we introduce just enough on length functions for well-quasi-orderings and the fast-growing complexity classes to obtain complexity upper bounds for both the weakening and contraction extensions. A specific instance of this result yields the first complexity bound for the prominent fuzzy logic MTL (monoidal t-norm based logic) providing an answer to a longstanding open problem.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470733"}, {"primary_key": "2206179", "vector": [], "sparse_vector": [], "title": "Some constructive variants of S4 with the finite model property.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The logics CS4 and IS4 are intuitionistic variants of the modal logic S4. Whether the finite model property holds for each of these logics has been a long-standing open problem. In this paper we introduce two logics closely related to IS4: GS4, obtained by adding the <PERSON><PERSON><PERSON><PERSON><PERSON> axiom to IS4, and S4I, obtained by reversing the roles of the modal and intuitionistic relations. We then prove that CS4, GS4, and S4I all enjoy the finite model property.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470643"}, {"primary_key": "2206180", "vector": [], "sparse_vector": [], "title": "The Topological Mu-Calculus: completeness and decidability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the topological μ-calculus, based on both Cantor derivative and closure modalities, proving completeness, decidability and FMP over general topological spaces, as well as over T0 and TD spaces. We also investigate relational μ-calculus, providing general completeness results for all natural fragments of μ-calculus over many different classes of relational frames. Unlike most other such proofs for μ-calculus, ours is modeltheoretic, making an innovative use of a known Modal Logic method (-the 'final' submodel of the canonical model), that has the twin advantages of great generality and essential simplicity.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470560"}, {"primary_key": "2206181", "vector": [], "sparse_vector": [], "title": "A Bunched Logic for Conditional Independence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Independence and conditional independence are fundamental concepts for reasoning about groups of random variables in probabilistic programs. Verification methods for independence are still nascent, and existing methods cannot handle conditional independence. We extend the logic of bunched implications (BI) with a non-commutative conjunction and provide a model based on Markov kernels; conditional independence can be directly captured as a logical formula in this model. Noting that Markov kernels are Kleisli arrows for the distribution monad, we then introduce a second model based on the powerset monad and show how it can capture join dependency, a non-probabilistic analogue of conditional independence from database theory. Finally, we develop a program logic for verifying conditional independence in probabilistic programs.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470712"}, {"primary_key": "2206182", "vector": [], "sparse_vector": [], "title": "A Constructive Logic with Classical Proofs and Refutations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study a conservative extension of classical propositional logic distinguishing between four modes of statement: a proposition may be affirmed or denied, and it may be strong or classical. Proofs of strong propositions must be constructive in some sense, whereas proofs of classical propositions proceed by contradiction. The system, in natural deduction style, is shown to be sound and complete with respect to a Kripke semantics. We develop the system from the perspective of the propositions-as-types correspondence by deriving a term assignment system with confluent reduction. The proof of strong normalization relies on a translation to System F with Mendler-style recursion.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470649"}, {"primary_key": "2206183", "vector": [], "sparse_vector": [], "title": "Minimal Taylor Algebras as a Common Framework for the Three Algebraic Approaches to the CSP.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper focuses on the algebraic theory underlying the study of the complexity and the algorithms for the Constraint Satisfaction Problem (CSP). We unify, simplify, and extend parts of the three approaches that have been developed to study the CSP over finite templates – absorption theory that was used to characterize CSPs solvable by local consistency methods (JACM'14), and <PERSON><PERSON><PERSON>'s and <PERSON><PERSON>'s theories that were used for two independent proofs of the CSP Dichotomy Theorem (FOCS'17, JACM'20).As the first contribution we present an elementary theorem about primitive positive definability and use it to obtain the starting points of <PERSON><PERSON><PERSON>'s and <PERSON><PERSON>'s proofs as corollaries. As the second contribution we propose and initiate a systematic study of minimal Taylor algebras. This class of algebras is broad enough so that it suffices to verify the CSP Dichotomy Theorem on this class only, but still is unusually well behaved. In particular, many concepts from the three approaches coincide in the class, which is in striking contrast with the general setting.We believe that the theory initiated in this paper will eventually result in a simple and more natural proof of the Dichotomy Theorem that employs a simpler and more efficient algorithm, and will help in attacking complexity questions in other CSP-related problems.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470557"}, {"primary_key": "2206184", "vector": [], "sparse_vector": [], "title": "Constraint Satisfaction Problems over Finite Structures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We initiate a systematic study of the computational complexity of the Constraint Satisfaction Problem (CSP) over finite structures that may contain both relations and operations. We show the close connection between this problem and a natural algebraic question: which finite algebras admit only polynomially many homomorphisms into them?We give some sufficient and some necessary conditions for a finite algebra to have this property. In particular, we show that every finite equationally nontrivial algebra has this property which gives us, as a simple consequence, a complete complexity classification of CSPs over two-element structures, thus extending the classification for two-element relational structures by <PERSON><PERSON><PERSON><PERSON> (STOC'78).We also present examples of two-element structures that have bounded width but do not have relational width (2,3), thus demonstrating that, from a descriptive complexity perspective, allowing operations leads to a richer theory.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470670"}, {"primary_key": "2206185", "vector": [], "sparse_vector": [], "title": "Continuous One-Counter Automata.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the reachability problem for continuous one-counter automata, COCA for short. In such automata, transitions are guarded by upper and lower bound tests against the counter value. Additionally, the counter updates associated with taking transitions can be (non-deterministically) scaled down by a nonzero factor between zero and one. Our three main results are as follows: (1) We prove that the reachability problem for COCA with global upper and lower bound tests is in NC2; (2) that, in general, the problem is decidable in polynomial time; and (3) that it is decidable in the polynomial hierarchy for COCA with parametric counter updates and bound tests.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470525"}, {"primary_key": "2206186", "vector": [], "sparse_vector": [], "title": "Canonical Polymorphisms of Ramsey Structures and the Unique Interpolation Property.", "authors": ["<PERSON>", "Bertalan Bodor"], "summary": "Constraint satisfaction problems for first-order reducts of finitely bounded homogeneous structures form a large class of computational problems that might exhibit a complexity dichotomy, P versus NP-complete. A powerful method to obtain polynomial-time tractability results for such CSPs is a certain reduction to polynomial-time tractable finite-domain CSPs de-fined over k-types, for a sufficiently large k. We give sufficient conditions when this method can be applied and illustrate how to use the general results to prove a new complexity dichotomy for first-order expansions of the basic relations of the spatial reasoning formalism RCC5.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470683"}, {"primary_key": "2206187", "vector": [], "sparse_vector": [], "title": "On Logics and Homomorphism Closure.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Predicate logic is the premier choice for specifying classes of relational structures. Homomorphisms are key to describing correspondences between relational structures. Questions concerning the interdependencies between these two means of characterizing (classes of) structures are of fundamental interest and can be highly non-trivial to answer. We investigate several problems regarding the homomorphism closure (homclosure) of the class of all (finite or arbitrary) models of logical sentences: membership of structures in a sentence's homclosure; sentence homclosedness; homclosure characterizability in a logic; normal forms for homclosed sentences in certain logics. For a wide variety of fragments of first- and second-order predicate logic, we clarify these problems' computational properties.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470511"}, {"primary_key": "2206188", "vector": [], "sparse_vector": [], "title": "Orbit-Finite-Dimensional Vector Spaces and Weighted Register Automata.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We develop a theory of vector spaces spanned by orbit-finite sets. Using this theory, we give a decision procedure for equivalence of weighted register automata, which are the common generalization of weighted automata and register automata for infinite alphabets. The algorithm runs in exponential time, and in polynomial time for a fixed number of registers. As a special case, we can decide, with the same complexity, language equivalence for unambiguous register automata, which improves previous results in three ways: (a) we allow for order comparisons on atoms, and not just equality; (b) the complexity is exponentially better; and (c) we allow automata with guessing.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470634"}, {"primary_key": "2206189", "vector": [], "sparse_vector": [], "title": "Asynchronous Extensions of HyperLTL.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Hyperproperties are a modern specification paradigm that extends trace properties to express properties of sets of traces. Temporal logics for hyperproperties studied in the literature, including HyperLTL, assume a synchronous semantics and enjoy a decidable model checking problem. In this paper, we introduce two asynchronous and orthogonal extensions of HyperLTL, namely Stuttering HyperLTL (HyperLTLS) and Context HyperLTL (HyperLTLC). Both of these extensions are useful, for instance, to formulate asynchronous variants of information-flow security properties. We show that for these logics, model checking is in general undecidable. On the positive side, for each of them, we identify a fragment with a decidable model checking that subsumes HyperLTL and that can express meaningful asynchronous requirements. Moreover, we provide the exact computational complexity of model checking for these two fragments which, for the HyperLTLS fragment, coincides with that of the strictly less expressive logic HyperLTL.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470583"}, {"primary_key": "2206190", "vector": [], "sparse_vector": [], "title": "On the logical structure of choice and bar induction principles.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We develop an approach to choice principles and their contrapositive bar-induction principles as extensionality schemes connecting an \"intensional\" or \"effective\" view of respectively ill- and well-foundedness properties to an \"extensional\" or \"ideal\" view of these properties. After classifying and analysing the relations between different intensional definitions of ill-foundedness and well-foundedness, we introduce, for a domain A, a codomain B and a \"filter\" T on finite approximations of functions from A to B, a generalised form GDCABT of the axiom of dependent choice and dually a generalised bar induction principle GBIABT such that:GDCABTintuitionistically captures the strength of·the general axiom of choice expressed as ∀a∃bR(a,b) ⇒ ∃α∀aR(a,α(a))) when T is a filter that derives point-wise from a relation R on A × B without introducing further constraints,·the Boolean Prime Filter Theorem / Ultrafilter Theorem if B is the two-element set \\mathbbB (for a constructive definition of prime filter),·the axiom of dependent choice if A = \\mathbbN,·<PERSON><PERSON>'s Lemma if A = \\mathbbN and B = \\mathbbB (up to weak classical reasoning).GBIABTintuitionistically captures the strength of<PERSON><PERSON><PERSON>'s completeness theorem in the form validity implies provability for entailment relations if B = \\mathbbB (for a constructive definition of validity),·bar induction if A = \\mathbbN,·the Weak Fan Theorem if A = \\mathbbN and B = \\mathbbB.Contrastingly, even though GDCABT and GBIABTsmoothly capture several variants of choice and bar induction, some instances are inconsistent, e.g. when A is \\mathbbB \\mathbbN and B is \\mathbbN.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470523"}, {"primary_key": "2206191", "vector": [], "sparse_vector": [], "title": "A Logic for Locally Complete Abstract Interpretations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce the notion of local completeness in abstract interpretation and define a logic for proving both the correctness and incorrectness of some program specification. Abstract interpretation is extensively used to design sound-by-construction program analyses that over-approximate program behaviours. Completeness of an abstract interpretation A for all possible programs and inputs would be an ideal situation for verifying correctness specifications, because the analysis can be done compositionally and no false alert will arise. Our first result shows that the class of programs whose abstract analysis on A is complete for all inputs has a severely limited expressiveness. A novel notion of local completeness weakens the above requirements by considering only some specific, rather than all, program inputs and thus finds wider applicability. In fact, our main contribution is the design of a proof system, parameterized by an abstraction A, that, for the first time, combines over- and under-approximations of program behaviours. Thanks to local completeness, in a provable triple ⊢A [P ] c [Q], the assertion Q is an under-approximation of the strongest post-condition post[c](P ) such that the abstractions in A of Q and post[c](P ) coincide. This means that Q is never too coarse, namely, under mild assumptions, the abstract interpretation of c does not yield false alerts for the input P iff Q has no alert. Thus, ⊢ A [P ] c [Q] not only ensures that all the alerts raised in Q are true ones, but also that if Q does not raise alerts then c is correct.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470608"}, {"primary_key": "2206192", "vector": [], "sparse_vector": [], "title": "From Finite-Valued Nondeterministic Transducers to Deterministic Two-Tape Automata.", "authors": ["<PERSON>sa<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The question whether P equals NP revolves around the discrepancy between active production and mere verification by Turing machines. In this paper, we examine the analogous problem for finite transducers and automata. Every nondeterministic finite transducer defines a binary relation associating each input word with all output words that the transducer can successfully produce on the given input. Finite-valued transducers are those for which there is a finite upper bound on the number of output words that the relation associates with every input word. We characterize finite-valued, functional, and unambiguous nondeterministic transducers whose relations can be verified by a deterministic two-tape automaton, show how to construct such an automaton if one exists, and prove the undecidability of the criterion.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470688"}, {"primary_key": "2206193", "vector": [], "sparse_vector": [], "title": "Initial Limit Datalog: a New Extensible Class of Decidable Constrained Horn Clauses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present initial limit Datalog, a new extensible class of constrained Horn clauses for which the satisfiability problem is decidable. The class may be viewed as a generalisation to higher-order logic (with a simple restriction on types) of the first-order language limit DatalogZ (a fragment of Datalog modulo linear integer arithmetic), but can be instantiated with any suitable background theory. For example, the fragment is decidable over any countable well-quasi-order with a decidable first-order theory, such as natural number vectors under componentwise linear arithmetic, and words of a bounded, context-free language ordered by the subword relation. Formulas of initial limit Datalog have the property that, under some assumptions on the background theory, their satisfiability can be witnessed by a new kind of term model which we call entwined structures. Whilst the set of all models is typically uncountable, the set of all entwined structures is recursively enumerable, and model checking is decidable.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470527"}, {"primary_key": "2206194", "vector": [], "sparse_vector": [], "title": "Higher Lenses.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We show that total, very well-behaved lenses are not very well-behaved when treated proof-relevantly in the setting of homotopy type theory/univalent foundations. In their place we propose something more well-behaved: higher lenses. Such a lens contains an equivalence between the lens's source type and the product of its view type and a remainder type, plus a function from the remainder type to the propositional truncation of the view type. It can equivalently be formulated as a getter function and a proof that its family of fibres is coherently constant, i.e. factors through propositional truncation.We explore the properties of higher lenses. For instance, we prove that higher lenses are equivalent to traditional ones for types that satisfy the principle of uniqueness of identity proofs. We also prove that higher lenses are n-truncated for n-truncated types, using a coinductive characterisation of coherently constant functions.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470613"}, {"primary_key": "2206195", "vector": [], "sparse_vector": [], "title": "Graphical Language with Delayed Trace: Picturing Quantum Computing with Finite Memory.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Graphical languages, like quantum circuits or ZX-calculus, have been successfully designed to represent (memoryless) quantum computations acting on a finite number of qubits. Meanwhile, delayed traces have been used as a graphical way to represent finite-memory computations on streams, in a classical setting (cartesian data types). We merge those two approaches and describe a general construction that extends any graphical language, equipped with a notion of discarding, to a graphical language of finite memory computations. In order to handle cases like the ZX-calculus, which is complete for post-selected quantum mechanics, we extend the delayed trace formalism beyond the causal case, refining the notion of causality for stream transformers. We design a stream semantics based on stateful morphism sequences and, under some assumptions, show universality and completeness results. Finally, we investigate the links of our framework with previous works on cartesian data types, signal flow graphs, and quantum channels with memories.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470553"}, {"primary_key": "2206196", "vector": [], "sparse_vector": [], "title": "On Linear Time Decidability of Differential Privacy for Programs with Unbounded Inputs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce an automata model for describing interesting classes of differential privacy mechanisms/algorithms that include known mechanisms from the literature. These automata can model algorithms whose inputs can be an unbounded sequence of real-valued query answers. We consider the problem of checking whether there exists a constant d such that the algorithm described by these automata are dϵ-differentially private for all positive values of the privacy budget parameter ϵ. We show that this problem can be decided in time linear in the automaton's size by identifying a necessary and sufficient condition on the underlying graph of the automaton. This paper's results are the first decidability results known for algorithms with an unbounded number of query answers taking values from the set of reals.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470708"}, {"primary_key": "2206197", "vector": [], "sparse_vector": [], "title": "Stochastic Processes with Expected Stopping Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Markov chains are the de facto finite-state model for stochastic dynamical systems, and Markov decision processes (MDPs) extend Markov chains by incorporating non-deterministic behaviors. Given an MDP and rewards on states, a classical optimization criterion is the maximal expected total reward where the MDP stops after T steps, which can be computed by a simple dynamic programming algorithm. We consider a natural generalization of the problem where the stopping times can be chosen according to a probability distribution, such that the expected stopping time is T, to optimize the expected total reward. Quite surprisingly we establish inter-reducibility of the expected stopping-time problem for Markov chains with the Positivity problem (which is related to the well-known Skolem problem), for which establishing either decidability or undecidability would be a major breakthrough. Given the hardness of the exact problem, we consider the approximate version of the problem: we show that it can be solved in exponential time for Markov chains and in exponential space for MDPs.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470595"}, {"primary_key": "2206198", "vector": [], "sparse_vector": [], "title": "Symbolic Time and Space Tradeoffs for Probabilistic Verification.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a faster symbolic algorithm for the following central problem in probabilistic verification: Compute the maximal end-component (MEC) decomposition of Markov decision processes (MDPs). This problem generalizes the SCC decomposition problem of graphs and closed recurrent sets of Markov chains. The model of symbolic algorithms is widely used in formal verification and model-checking, where access to the input model is restricted to only symbolic operations (e.g., basic set operations and computation of one-step neighborhood). For an input MDP with n vertices and m edges, the classical symbolic algorithm from the 1990s for the MEC decomposition requires O(n 2 ) symbolic operations and O(1) symbolic space. The only other symbolic algorithm for the MEC decomposition requires O(n√m ) symbolic operations and O(√m ) symbolic space. The main open question has been whether the worst-case O(n 2 ) bound for symbolic operations can be beaten for MEC decomposition computation. In this work, we answer the open question in the affirmative. We present a symbolic algorithm that requires ~O( n 1.5 ) symbolic operations and ~O( √n ) symbolic space. Moreover, the parametrization of our algorithm provides a trade-off between symbolic operations and esymbolic space: for all 0 2 - ∈ ) symbolic operations and ~O( n ∈ ) symbolic space (~O(·) hides poly-logarithmic factors).Using our techniques we also present faster algorithms for computing the almost-sure winning regions of ω-regular objectives for MDPs. We consider the canonical parity objectives for ω-regular objectives, and for parity objectives with d-priorities we present an algorithm that computes the almost-sure winning region with ~O( n 2 - ∈ ) symbolic operations and ~O( n ∈ ) symbolic space, for all 0 2 · d) symbolic operations and O(log n) symbolic space; or (b) O(n√m ·d) symbolic operations and ~O( √m ) symbolic space. Thus we improve the time-space product from ~O( n 2 ·d ) to ~O( n 2 ).", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470739"}, {"primary_key": "2206199", "vector": [], "sparse_vector": [], "title": "Forbidden Induced Subgraphs and the Łoś-Tarski Theorem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Let C be a class of finite and infinite graphs that is closed under induced subgraphs. The well-known Łoś-Ta<PERSON>i Theorem from classical model theory implies that C is definable in first-order logic (FO) by a sentence φ if and only if C has a finite set of forbidden induced finite subgraphs. It provides a powerful tool to show nontrivial characterizations of graphs of small vertex cover, of bounded tree-depth, of bounded shrub-depth, etc. in terms of forbidden induced finite subgraphs. Furthermore, by the Completeness Theorem, we can compute from φ the corresponding forbidden induced subgraphs. Our results (a) and (b) show that this machinery fails on finite graphs. (a)There is a class of finite graphs that is definable in FO and closed under induced subgraphs but has no finite set of forbidden induced subgraphs. (b)Even if we only consider classes C of finite graphs that can be characterized by a finite set of forbidden induced subgraphs such a characterization cannot be computed from an FO-sentence φ that defines C and the size of the characterization cannot be bounded by f(|φ|) for any computable function f.Besides their importance in graph theory, our results also significantly strengthen similar known theorems for arbitrary structures.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470742"}, {"primary_key": "2206200", "vector": [], "sparse_vector": [], "title": "Abstraction in Data Integration.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data integration provides a unified and abstract view over a set of existing data sources. The typical architecture of a data integration system comprises the global schema, which is the structure for the unified view, the source schema, and the mapping, which is a formal account of how data at the sources relate to the global view. Most of the research work on data integration in the last decades deals with the problem of processing a query expressed on the global schema by computing a suitable query over the sources, and then evaluating the latter in order to derive the answers to the original query. Here, we address a novel issue in data integration: starting from a query expressed over the sources, the goal is to find an abstraction of such query, i.e., a query over the global schema that captures the original query, modulo the mapping. The goal of the paper is to provide an overview of the notion of abstraction in data integration, by presenting a formal framework, illustrating the results that have appeared in the recent literature, and discussing interesting directions for future research.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470716"}, {"primary_key": "2206201", "vector": [], "sparse_vector": [], "title": "SD-Regular Transducer Expressions for Aperiodic Transformations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "FO transductions, aperiodic deterministic two-way transducers, as well as aperiodic streaming string transducers are all equivalent models for first order definable functions. In this paper, we solve the problem of expressions capturing first order definable functions, thereby generalizing the seminal SF=AP (star-free expressions = aperiodic languages) result of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. Our result also generalizes a lesser known characterization by <PERSON><PERSON><PERSON><PERSON><PERSON> of aperiodic languages by SD-regular expressions (SD=AP). We show that every first order definable function over finite words captured by an aperiodic deterministic two-way transducer can be described with an SD-regular transducer expression (SDRTE). An SDRTE is a regular expression where Kleene stars are used in a restricted way: they can appear only on aperiodic languages which are prefix codes of bounded synchronization delay. SDRTEs are constructed from simple functions using the combinators unambiguous sum (deterministic choice), Hadamard product, and unambiguous versions of the Cauchy product and the fc-chained Kleene-star, where the star is restricted as mentioned. In order to construct an SDRTE associated with an aperiodic deterministic two-way transducer, (i) we concretize <PERSON><PERSON><PERSON><PERSON><PERSON>'s SD=AP result, by proving that aperiodic languages are captured by SD-regular expressions which are unambiguous and stabilising; (ii) by structural induction on the unambiguous, stabilising SD-regular expressions describing the domain of the transducer, we construct SDRTEs. Finally, we also look at various formalisms equivalent to SDRTEs which use the function composition, allowing to trade the fc-chained star for a 1-star.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470738"}, {"primary_key": "2206202", "vector": [], "sparse_vector": [], "title": "Lovász-Type Theorems and Game Comonads.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (1967) showed that two finite relational structures A and B are isomorphic if, and only if, the number of homomorphisms from C to A is the same as the number of homomorphisms from C to B for any finite structure C. Soon after, <PERSON><PERSON><PERSON> (1973) proved a categorical generalisation of this fact. We propose a new categorical formulation, which applies to any locally finite category with pushouts and a proper factorisation system. As special cases of this general theorem, we obtain two variants of Lov\\'asz' theorem: the result by Dvo\\v{r}\\'ak (2010) that characterises equivalence of graphs in the k-dimensional Weisfeiler-Leman equivalence by homomorphism counts from graphs of tree-width at most k, and the result of <PERSON><PERSON><PERSON> (2020) characterising equivalence with respect to first-order logic with counting and quantifier depth k in terms of homomorphism counts from graphs of tree-depth at most k. The connection of our categorical formulation with these results is obtained by means of the game comonads of <PERSON> et al. We also present a novel application to homomorphism counts in modal logic.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470609"}, {"primary_key": "2206203", "vector": [], "sparse_vector": [], "title": "Session Logical Relations for Noninterference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Information flow control type systems statically restrict the propagation of sensitive data to ensure end-to-end confidentiality. The property to be shown is noninterference, asserting that an attacker cannot infer any secrets from made observations. Session types delimit the kinds of observations that can be made along a communication channel by imposing a protocol of message exchange. These protocols govern the exchange along a single channel and leave unconstrained the propagation along adjacent channels. This paper contributes an information flow control type system for linear session types. The type system stands in close correspondence with intuitionistic linear logic. Intuitionistic linear logic typing ensures that process configurations form a tree such that client processes are parent nodes and provider processes child nodes. To control the propagation of secret messages, the type system is enriched with secrecy levels and arranges these levels to be aligned with the configuration tree. Two levels are associated with every process: the maximal secrecy denoting the process' security clearance and the running secrecy denoting the highest level of secret information obtained so far. The computational semantics naturally stratifies process configurations such that higher-secrecy processes are parents of lower-secrecy ones, an invariant enforced by typing. Noninterference is stated in terms of a logical relation that is indexed by the secrecy-level-enriched session types. The logical relation contributes a novel development of logical relations for session typed languages as it considers open configurations, allowing for a more nuanced equivalence statement.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470654"}, {"primary_key": "2206204", "vector": [], "sparse_vector": [], "title": "Verifying higher-order concurrency with data automata.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Using a combination of automata-theoretic and game-semantic techniques, we propose a method for analysing higher-order concurrent programs. Our language of choice is Finitary Idealised Concurrent Algol (FICA) due to its relatively simple fully abstract game model.Our first contribution is an automata model over a tree-structured infinite data alphabet, called split automata, whose distinctive feature is the separation of control and memory. We show that every FICA term can be translated into such an automaton. Thanks to the structure of split automata, we are able to observe subtle aspects of the underlying game semantics.This enables us to identify a fragment of FICA with iteration and limited synchronisation (but without recursion), for which, in contrast to the whole FICA, a variety of verification problems turn out to be decidable.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470691"}, {"primary_key": "2206205", "vector": [], "sparse_vector": [], "title": "Lacon- and Shrub-Decompositions: A New Characterization of First-Order Transductions of Bounded Expansion Classes.", "authors": ["<PERSON>"], "summary": "The concept of bounded expansion provides a robust way to capture sparse graph classes with interesting algorithmic properties. Most notably, every problem definable in first-order logic can be solved in linear time on bounded expansion graph classes. First-order interpretations and transductions of sparse graph classes lead to more general, dense graph classes that seem to inherit many of the nice algorithmic properties of their sparse counterparts.In this work we introduce lacon- and shrub-decompositions and use them to characterize transductions of bounded expansion graph classes and other graph classes. If one can efficiently compute sparse shrub- or lacon-decompositions of transductions of bounded expansion classes then one can solve every problem definable in first-order logic in linear time on these classes.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470680"}, {"primary_key": "2206206", "vector": [], "sparse_vector": [], "title": "The Undecidability of System F Typability and Type Checking for Reductionists.", "authors": ["<PERSON><PERSON>"], "summary": "The undecidability of both typability and type checking for System F (polymorphic lambda-calculus) was established by <PERSON> in the 1990s. For type checking <PERSON> gave an astonishingly simple reduction from semi-unification (first-order unification combined with first-order matching). For typability <PERSON> developed an intricate calculus to control the shape of type assumptions across type derivations via term structure. This calculus of invariant type assumptions allows for a reduction from type checking to typability. Unfortunately, this approach relies on heavy machinery that complicates surveyability of the overall argument. The present work gives comparatively simple, direct reduction from semi-unification to System F typability. The key observation is as follows: in the existential setting of typability, it suffices to consider some specific (but not all, as for invariant type assumptions) type derivations. Additionally, the particular result requires only to consider closed types without nested quantification. The undecidability of type checking is obtained via a folklore reduction from typability. Profiting from its smaller footprint, correctness of the new approach is witnessed by a mechanization in the Coq proof assistant. The mechanization is incorporated into the existing Coq library of undecidability proofs. For free, the library provides constructive, mechanically verified many-one reductions from Turing machine halting to both System F typability and System F type checking.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470520"}, {"primary_key": "2206207", "vector": [], "sparse_vector": [], "title": "Categorical models of Linear Logic with fixed points of formulas.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We develop a categorical semantics of μLL, a version of propositional Linear Logic with least and greatest fixed points extending <PERSON>'s propositional μMALL with exponentials. Our general categorical setting is based on Seely categories and on strong functors acting on them. We exhibit two simple instances of this setting. In the first one, which is based on the category of sets and relations, least and greatest fixed points are interpreted in the same way. In the second one, based on a category of sets equipped with a notion of totality (non-uniform totality spaces) and relations preserving it, least and greatest fixed points have distinct interpretations. This latter model shows that μLL enjoys a denotational form of normalization of proofs.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470664"}, {"primary_key": "2206208", "vector": [], "sparse_vector": [], "title": "Multi-Structural Games and Number of Quantifiers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study multi-structural games, played on two sets ${\\mathcal{A}}$ and ${\\mathcal{B}}$ of structures. These games generalize Ehrenfeucht-Fraïssé games. Whereas Ehrenfeucht-Fraïssé games capture the quantifier rank of a first-order sentence, multi-structural games capture the number of quantifiers, in the sense that <PERSON><PERSON><PERSON> wins the r-round game if and only if there is a first-order sentence ϕ with at most r quantifiers, where every structure in ${\\mathcal{A}}$ satisfies ϕ and no structure in ${\\mathcal{B}}$ satisfies ϕ. We use these games to give a complete characterization of the number of quantifiers required to distinguish linear orders of different sizes, and develop machinery for analyzing structures beyond linear orders.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470756"}, {"primary_key": "2206209", "vector": [], "sparse_vector": [], "title": "The Laplace Mechanism has optimal utility for differential privacy over continuous queries.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Differential Privacy protects individuals' data when statistical queries are published from aggregated databases: applying \"obfuscating\" mechanisms to the query results makes the released information less specific but, unavoidably, also decreases its utility. Yet it has been shown that for discrete data (e.g. counting queries), a mandated degree of privacy and a reasonable interpretation of loss of utility, the Geometric obfuscating mechanism is optimal: it loses as little utility as possible [<PERSON><PERSON><PERSON> et al. [1]].For continuous query results however (e.g. real numbers) the optimality result does not hold. Our contribution here is to show that optimality is regained by using the Laplace mechanism for the obfuscation.The technical apparatus involved includes the earlier discrete result [Ghosh op. cit.], recent work on abstract channels and their geometric representation as hyper-distributions [<PERSON><PERSON><PERSON> et al. [2]], and the dual interpretations of distance between distributions provided by the Kantorovich-Rubinstein Theorem.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470718"}, {"primary_key": "2206210", "vector": [], "sparse_vector": [], "title": "Types Are Internal ∞-Groupoids.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "By extending type theory with a universe of definitionally associative and unital polynomial monads, we show how to arrive at a definition of opetopic type which is able to encode a number of fully coherent algebraic structures. In particular, our approach leads to a definition of ∞-groupoid internal to type theory and we prove that the type of such ∞-groupoids is equivalent to the universe of types. That is, every type admits the structure of an ∞-groupoid internally, and this structure is unique.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470541"}, {"primary_key": "2206211", "vector": [], "sparse_vector": [], "title": "Parameterized Complexity of Elimination Distance to First-Order Logic Properties.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "Dimitrios M. Thilikos"], "summary": "The elimination distance to some target graph property P is a general graph modification parameter introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON>. We initiate the study of elimination distances to graph properties expressible in first-order logic. We delimit the problem's fixed-parameter tractability by identifying sufficient and necessary conditions on the structure of prefixes of first-order logic formulas. Our main result is the following meta-theorem: For every graph property P expressible by a first order-logic formula φ ∈ Σ3, that is, of the form φ = ∃x 1 ∃x 2 ⋯∃x r ∀y 1 ∀y 2 ⋯∀y s ∃z 1 ∃z 2 ⋯∃z t ψ, where ψ is a quantifier-free first-order formula, checking whether the elimination distance of a graph to P does not exceed k, is fixed-parameter tractable parameterized by k. Properties of graphs expressible by formulas from Σ3 include being of bounded degree, excluding a forbidden subgraph, or containing a bounded dominating set. We complement this theorem by showing that such a general statement does not hold for formulas with even slightly more expressive prefix structure: There are formulas φ ∈ Π3, for which computing elimination distance is W[2]-hard.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470540"}, {"primary_key": "2206212", "vector": [], "sparse_vector": [], "title": "Behavioural Preorders via Graded Monads.", "authors": ["Chase Ford", "<PERSON>", "<PERSON><PERSON>"], "summary": "Like notions of process equivalence, behavioural preorders on processes come in many flavours, ranging from fine-grained comparisons such as ready simulation to coarse-grained ones such as trace inclusion. Often, such behavioural preorders are characterized in terms of theory inclusion in dedicated characteristic logics; e.g. simulation is characterized by theory inclusion in the positive fragment of Hennessy-Milner logic. We introduce a unified semantic framework for behavioural preorders and their characteristic logics in which we parametrize the system type as a functor on the category Pos of partially ordered sets following the paradigm of universal coalgebra, while behavioural preorders are captured as graded monads on Pos, in generalization of a previous approach to notions of process equivalence. We show that graded monads on Pos are induced by a form of graded inequational theories that we introduce here. Moreover, we provide a general notion of modal logic compatible with a given graded behavioural preorder, along with a criterion for expressiveness, in the indicated sense of characterization of the behavioural preorder by theory inclusion. We illustrate our main result on various behavioural preorders on labelled transition systems and probabilistic transition systems.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470517"}, {"primary_key": "2206213", "vector": [], "sparse_vector": [], "title": "A Relational Theory of Monadic Rewriting Systems, Part I.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Motivated by the study of effectful programming languages and computations, we introduce a relational theory of monadic rewriting systems. The latter are rewriting systems whose notion of reduction is effectful, where effects are modelled as monads. Contrary to what happens in the ordinary operational semantics of monadic programming languages, defining meaningful notions of monadic rewriting turns out to problematic for several monads, including the distribution, powerset, reader, and global state monad. This raises the question of when monadic rewriting is possible. We answer that question by identifying a class of monads, known as weakly cartesian monads, that guarantee monadic rewriting to be well-behaved. In case monads are given as equational theories, as it is the case for algebraic effects, we also show that a sufficient condition to have a well-behaved notion of monadic rewriting is that all equations in the theory are linear. Finally, we apply the abstract theory of monadic rewriting systems to the call-by-value λ-calculus with algebraic effects, this way obtaining effectful (surface) standardisation and confluence theorems.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470633"}, {"primary_key": "2206214", "vector": [], "sparse_vector": [], "title": "Removing Redundant Refusals: Minimal Complete Test Suites for Failure Trace Semantics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We explore the problem of finding a minimal complete test suite for a refusal trace (or failure trace) semantics. Since complete test suites are typically infinite, we consider the setting with a bound ℓ on the length of refusal traces of interest. A test suite T is thus complete if it is failed by all processes that contain a disallowed refusal trace of length at most ℓ.The proposed approach is based on generating a minimal complete set of forbidden refusal traces. Our solution utilises several interesting insights into refusal trace semantics. In particular, we identify a key class of refusals called fundamental refusals which essentially determine the refusal trace semantics, and the associated fundamental equivalence relation. We then propose a small but not necessarily minimal test suite based on our theory, which can be constructed with a simple algorithm. Subsequently, we provide an enumerative method to remove all redundant traces from our complete test suite, which comes in two variants, depending on whether we wish to retain the highly desirable uniform completeness (guarantee of shortest counterexamples).A related problem is the construction of a characteristic formula of a process P, that is, a formula ΦP such that every process which satisfies ΦP refines P. Our test generation algorithm can be used to construct such a formula using a variant of Hennessy-Milner logic with recursion.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470737"}, {"primary_key": "2206215", "vector": [], "sparse_vector": [], "title": "Global Optimisation with Constructive Reals.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We draw new connections between deterministic, complete, and general global optimisation of continuous functions and a generalised notion of regression, using constructive type theory and computable real numbers. Using this foundation we formulate novel convergence criteria for regression, derived from the convergence properties of global optimisations. We see this as possibly having an impact on optimisation-based computational sciences, which include much of machine learning. Using computable reals, as opposed to floating-point representations, we can give strong theoretical guarantees in terms of both precision and termination. The theory is fully formalised using the safe mode of the proof assistant AGDA. Some examples implemented using an off-the-shelf constructive reals library in JAVA indicate that the approach is algorithmically promising.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470549"}, {"primary_key": "2206216", "vector": [], "sparse_vector": [], "title": "Assuming Just Enough Fairness to make Session Types Complete for Lock-freedom.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We investigate how different fairness assumptions affect results concerning lock-freedom, a typical liveness property targeted by session type systems. We fix a minimal session calculus and systematically take into account all known fairness assumptions, thereby identifying precisely three interesting and semantically distinct notions of lock-freedom, all of which having a sound session type system. We then show that, by using a general merge operator in an otherwise standard approach to global session types, we obtain a session type system complete for the strongest amongst those notions of lock-freedom, which assumes only justness of execution paths, a minimal fairness assumption for concurrent systems.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470531"}, {"primary_key": "2206217", "vector": [], "sparse_vector": [], "title": "The Logic of Graph Neural Networks.", "authors": ["<PERSON>"], "summary": "Graph neural networks (GNNs) are deep learning architectures for machine learning problems on graphs. It has recently been shown that the expressiveness of GNNs can be characterised precisely by the combinatorial <PERSON><PERSON><PERSON><PERSON><PERSON> algorithms and by finite variable counting logics. The correspondence has even led to new, higher-order GNNs corresponding to the WL algorithm in higher dimensions.The purpose of this paper is to explain these descriptive characterisations of GNNs.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470677"}, {"primary_key": "2206218", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for Heyting-Lewis Implication.", "authors": ["<PERSON>", "Tadeusz <PERSON>", "<PERSON>"], "summary": "Heyting-Lewis Logic is the extension of intuitionistic propositional logic with a strict implication connective that satisfies the constructive counterparts of axioms for strict implication provable in classical modal logics. Variants of this logic are surprisingly widespread: they appear as Curry-Howard correspondents of (simple type theory extended with) Haskell-style arrows, in preservativity logic of Heyting arithmetic, in the proof theory of guarded (co)recursion, and in the generalization of intuitionistic epistemic logic.Heyting-Lewis Logic can be interpreted in intuitionistic Kripke frames extended with a binary relation to account for strict implication. We use this semantics to define descriptive frames (generalisations of Esakia spaces), and establish a categorical duality between the algebraic interpretation and the frame semantics. We then adapt a transformation by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> to translate Heyting-Lewis Logic to classical modal logic with two unary operators. This allows us to prove a Blok-E<PERSON> theorem that we then use to obtain both known and new canonicity and correspondence theorems, and the finite model property and decidability for a large family of Heyting-Lewis logics.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470508"}, {"primary_key": "2206219", "vector": [], "sparse_vector": [], "title": "The Smash Product of Monoidal Theories.", "authors": ["<PERSON>"], "summary": "The tensor product of props was defined by <PERSON><PERSON> and <PERSON> as an extension of the <PERSON><PERSON> product of operads to more general monoidal theories. Theories that factor as tensor products include the theory of commutative monoids and the theory of bialgebras. We give a topological interpretation (and vast generalisation) of this construction as a low-dimensional projection of a \"smash product of pointed directed spaces\". Here directed spaces are embodied by combinatorial structures called diagrammatic sets, while Gray products replace cartesian products. The correspondence is mediated by a web of adjunctions relating diagrammatic sets, pros, probs, props, and Gray-categories. The smash product applies to presentations of higher-dimensional theories and systematically produces higher-dimensional coherence data.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470575"}, {"primary_key": "2206220", "vector": [], "sparse_vector": [], "title": "Monomial size vs. Bit-complexity in Sums-of-Squares and Polynomial Calculus.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In this paper we consider the relationship between monomial-size and bit-complexity in Sums-of-Squares (SOS) in Polynomial Calculus Resolution over rationals (PCR/ℚ). We show that there is a set of polynomial constraints Qnover Boolean variables that has both SOS and PCR/ℚ refutations of degree 2 and thus with only polynomially many monomials, but for which any SOS or PCR/ℚ refutation must have exponential bit-complexity, when the rational coefficients are represented with their reduced fractions written in binary.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470545"}, {"primary_key": "2206221", "vector": [], "sparse_vector": [], "title": "Quantitative and Approximate Monitoring.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In runtime verification, a monitor watches a trace of a system and, if possible, decides after observing each finite prefix whether or not the unknown infinite trace satisfies a given specification. We generalize the theory of runtime verification to monitors that attempt to estimate numerical values of quantitative trace properties (instead of attempting to conclude boolean values of trace specifications), such as maximal or average response time along a trace. Quantitative monitors are approximate: with every finite prefix, they can improve their estimate of the infinite trace's unknown property value. Consequently, quantitative monitors can be compared with regard to a precision-cost trade-off: better approximations of the property value require more monitor resources, such as states (in the case of finite-state monitors) or registers, and additional resources yield better approximations. We introduce a formal framework for quantitative and approximate monitoring, show how it conservatively generalizes the classical boolean setting for monitoring, and give several precision-cost trade-offs for monitors. For example, we prove that there are quantitative properties for which every additional register improves monitoring precision.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470547"}, {"primary_key": "2206222", "vector": [], "sparse_vector": [], "title": "Demonic Lattices and Semilattices in Relational Semigroups with Ordinary Composition.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Relation algebra and its reducts provide us with a strong tool for reasoning about nondeterministic programs and their partial correctness. Demonic calculus, introduced to model the behaviour of a machine where the demon is in control of nondeterminism, has also provided us with an extension of that reasoning to total correctness. We formalise the framework for relational reasoning about total correctness in nondeterministic programs using semigroups with ordinary composition and demonic lattice operations. We show that the class of representable demonic join semigroups is not finitely axiomatisable and that the representation class of demonic meet semigroups does not have the finite representation property for its finite members. For lattice semigroups (with composition, demonic join and demonic meet) we show that the representation problem for finite algebras is undecidable, moreover the finite representation problem is also undecidable. It follows that the representation class is not finitely axiomatisable, furthermore the finite representation property fails.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470509"}, {"primary_key": "2206223", "vector": [], "sparse_vector": [], "title": "On sequentiality and well-bracketing in the π-calculus.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The π-calculus is used as a model for programming languages. Its contexts exhibit arbitrary concurrency, making them very discriminating. This may prevent validating desirable behavioural equivalences in cases when more disciplined contexts are expected. In this paper we focus on two such common disciplines: sequentiality, meaning that at any time there is a single thread of computation, and well-bracketing, meaning that calls to external services obey a stack-like discipline. We formalise the disciplines by means of type systems. The main focus of the paper is on studying the consequence of the disciplines on behavioural equivalence. We define and study labelled bisimilarities for sequentiality and well-bracketing. These relations are coarser than ordinary bisimilarity. We prove that they are sound for the respective (contextual) barbed equivalence, and also complete under a certain technical condition.We show the usefulness of our techniques on a number of examples, that have mainly to do with the representation of functions and store.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470559"}, {"primary_key": "2206224", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON>&<PERSON>;s theorem for infinite alphabets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate commutative images of languages recognised by register automata and grammars. Semi-linear and rational sets can be naturally extended to this setting by allowing for orbit-finite unions instead of only finite ones. We prove that commutative images of languages of one-register automata are not always semi-linear, but they are always rational. We also lift the latter result to grammars: commutative images of one- register context-free languages are rational, and in consequence commutatively equivalent to register automata. We conjecture analogous results for automata and grammars with arbitrarily many registers.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470626"}, {"primary_key": "2206225", "vector": [], "sparse_vector": [], "title": "Combinatorial Proofs and Decomposition Theorems for First-order Logic.", "authors": ["<PERSON>", "Lutz Straßburger", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We uncover a close relationship between combinatorial and syntactic proofs for first-order logic (without equality). Whereas syntactic proofs are formalized in a deductive proof system based on inference rules, a combinatorial proof is a syntax-free presentation of a proof that is independent from any set of inference rules. We show that the two proof representations are related via a deep inference decomposition theorem that establishes a new kind of normal form for syntactic proofs. This yields (a) a simple proof of soundness and completeness for first-order combinatorial proofs, and (b) a full completeness theorem: every combinatorial proof is the image of a syntactic proof.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470579"}, {"primary_key": "2206226", "vector": [], "sparse_vector": [], "title": "Compositional relational reasoning via operational game semantics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show how to use operational game semantics as a guide to develop relational techniques for establishing contextual equivalences with respect to contexts drawn from a hierarchy of four call-by-value higher-order languages: with either general or ground-type references and with either call/cc or no control operator. In game semantics, differences between the contexts can be captured by the absence or presence of the O-visibility and O-bracketing conditions.The proposed technique, which we call Kripke normal-form bisimulations, combines insights from normal-form bisimulation and Kripke logical relations with game semantics. In particular, the role of the heap and the name history is abstracted away using Kripke-style world transition systems. The differences between the four kinds of contexts manifest themselves through simple local conditions that can be shown to correspond to O-visibility and O-bracketing, as applicable.The technique is sound and complete by virtue of correspondence with operational game semantics. Moreover, it sheds a new light on other related developments, such as backtracking and private transitions in Kripke logical relations, which can be related to specific phenomena in game models.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470524"}, {"primary_key": "2206227", "vector": [], "sparse_vector": [], "title": "Commutative Monads for Probabilistic Programming Languages.", "authors": ["Xiaodong Jia", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A long-standing open problem in the semantics of programming languages supporting probabilistic choice is to find a commutative monad for probability on the category DCPO. In this paper we present three such monads and a general construction for finding even more. We show how to use these monads to provide a sound and adequate denotational semantics for the Probabilistic FixPoint Calculus (PFPC) -- a call-by-value simply-typed lambda calculus with mixed-variance recursive types, term recursion and probabilistic choice. We also show that in the special case where we consider continuous dcpo's, then all three monads coincide with the valuations monad of <PERSON> and we fully characterise the induced Eilenberg-Moore categories by showing that they are all isomorphic to the category of continuous Kegelspitzen of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470611"}, {"primary_key": "2206228", "vector": [], "sparse_vector": [], "title": "Living without <PERSON> and <PERSON>: Definitions and Interpolants in the Guarded and Two-Variable Fragments.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In logics with the Craig interpolation property (CIP) the existence of an interpolant for an implication follows from the validity of the implication. In logics with the projective Beth definability property (PBDP), the existence of an explicit definition of a relation follows from the validity of a formula expressing its implicit definability. The two-variable fragment, FO 2 , and the guarded fragment, GF, of first-order logic both fail to have the CIP and the PBDP. We show that nevertheless in both fragments the existence of interpolants and explicit definitions is decidable. In GF, both problems are 3EXPTIME-complete in general, and 2EXPTIME-complete if the arity of relation symbols is bounded by a constant c ≥ 3. In FO 2 , we prove a CON2EXPTIME upper bound and a 2EXPTIME lower bound for both problems. Thus, both for GF and FO 2 existence of interpolants and explicit definitions are decidable but harder than validity (in case of FO 2 under standard complexity assumptions).", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470585"}, {"primary_key": "2206229", "vector": [], "sparse_vector": [], "title": "Supermartingales, Ranking Functions and Probabilistic Lambda Calculus.", "authors": ["<PERSON>", "C.<PERSON><PERSON><PERSON>"], "summary": "We introduce a method for proving almost sure termination in the context of lambda calculus with continuous random sampling and explicit recursion, based on ranking supermartingales. This result is extended in three ways. Antitone ranking functions have weaker restrictions on how fast they must decrease, and are applicable to a wider range of programs. Sparse ranking functions take values only at a subset of the program's reachable states, so they are simpler to define and more flexible. Ranking functions with respect to alternative reduction strategies give yet more flexibility, and significantly increase the applicability of the ranking supermartingale approach to proving almost sure termination, thanks to a novel (restricted) confluence result which is of independent interest. The notion of antitone ranking function was inspired by similar work by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON> in the setting of a first-order imperative language, but adapted to a higher-order functional language. The sparse ranking function and confluent semantics extensions are unique to the higher-order setting. Our methods can be used to prove almost sure termination of programs that are beyond the reach of methods in the literature, including higher-order and non-affine recursion.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470550"}, {"primary_key": "2206230", "vector": [], "sparse_vector": [], "title": "Finite Model Theory of the Triguarded Fragment and Related Logics.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The Triguarded Fragment (TGF) is among the most expressive decidable fragments of first-order logic, subsuming both its two-variable and guarded fragments without equality. We show that the TGF has the finite model property (providing a tight doubly exponential bound on the model size) and hence finite satisfiability coincides with satisfiability known to be N2ExpTime-complete. Using similar constructions, we also establish 2ExpTime-completeness for finite satisfiability of the constant-free (tri)guarded fragment with transitive guards.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470734"}, {"primary_key": "2206231", "vector": [], "sparse_vector": [], "title": "Expressivity of Quantitative Modal Logics : Categorical Foundations via Codensity and Approximation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A modal logic that is strong enough to fully characterize the behavior of a system is called expressive. Recently, with the growing diversity of systems to be reasoned about (probabilistic, cyber-physical, etc.), the focus shifted to quantitative settings which resulted in a number of expressivity results for quantitative logics and behavioral metrics. Each of these quantitative expressivity results uses a tailor-made argument; distilling the essence of these arguments is non-trivial, yet important to support the design of expressive modal logics for new quantitative settings. In this paper, we present the first categorical framework for deriving quantitative expressivity results, based on the new notion of approximating family. A key ingredient is the codensity lifting-a uniform observation-centric construction of various bisimilarity-like notions such as bisimulation metrics. We show that several recent quantitative expressivity results (e.g. by <PERSON> et al. and by <PERSON><PERSON><PERSON> et al.) are accommodated in our framework; a new expressivity result is derived, too, for what we call bisimulation uniformity.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470656"}, {"primary_key": "2206232", "vector": [], "sparse_vector": [], "title": "Internal ∞-Categorical Models of Dependent Type Theory : Towards 2LTT Eating HoTT.", "authors": ["<PERSON><PERSON>"], "summary": "Using dependent type theory to formalise the syntax of dependent type theory is a very active topic of study and goes under the name of \"type theory eating itself\" or \"type theory in type theory.\" Most approaches are at least loosely based on <PERSON><PERSON><PERSON><PERSON>'s categories with families (CwF's) and come with a type Con of contexts, a type family Ty indexed over it modelling types, and so on. This works well in versions of type theory where the principle of unique identity proofs (UIP) holds. In homotopy type theory (HoTT) however, it is a long-standing and frequently discussed open problem whether the type theory \"eats itself\" and can serve as its own interpreter. The fundamental underlying difficulty seems to be that categories are not suitable to capture a type theory in the absence of UIP. In this paper, we develop a notion of ∞-categories with families (∞-CwF's). The approach to higher categories used relies on the previously suggested semi-Segal types, with a new construction of identity substitutions that allow for both univalent and non-univalent variations. The type-theoretic universe as well as the internalised (set-level) syntax are models, although it remains a conjecture that the latter is initial. To circumvent the known unsolved problem of constructing semisimplicial types, the definition is presented in two-level type theory (2LTT). Apart from introducing ∞-CwF's, the paper explains the shortcomings of 1-categories in type theory without UIP as well as the difficulties of and approaches to internal higher-dimensional categories.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470667"}, {"primary_key": "2206233", "vector": [], "sparse_vector": [], "title": "Positive First-order Logic on Words.", "authors": ["<PERSON>"], "summary": "We study FO + , a fragment of first-order logic on finite words, where monadic predicates can only appear positively. We show that there is an FO-definable language that is monotone in monadic predicates but not definable in FO + . This provides a simple proof that <PERSON>'s preservation theorem fails on finite structures. We additionally show that given a regular language, it is undecidable whether it is definable in FO + .", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470602"}, {"primary_key": "2206234", "vector": [], "sparse_vector": [], "title": "Perspective Multi-Player Games.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Perspective games model multi-agent systems in which agents can view only the parts of the system that they own. Unlike the observation-based model of partial visibility, where uncertainty is longitudinal – agents partially observe the full history, uncertainty in perspective games is transverse – agents fully observe parts of the history. So far, researchers studied zero-sum two-player perspective games. There, the objective of one agent (the system) is to satisfy a given specification, and the objective of the second agent (the environment) is to fail the specification.We study richer and more realistic settings of perspective games. We consider games with more than two players, and distinguish between zero-sum games, where the objectives of the players form a partition of all possible behaviors, zero-sum games among coalitions, where agents in a coalition share their objectives but do not share their visibility, and non-zero-sum games, where each agent has her own objectives and is assumed to be rational rather than hostile. In the non-zero-sum setting, we are interested in stable outcomes of the game; in particular, Nash equilibria.We show that, as is the case with longitudinal uncertainty, transverse uncertainty leads to undecidability in settings with three or more players that include coalitions or non-zero-sum objectives. We then focus on two-player non-zero-sum perspective games. There, finding and reasoning about stable outcomes is decidable, and in fact, unlike the case with longitudinal uncertainty, can be done in the same complexity as in games with full visibility. In particular, we study rational synthesis in the perspective setting, where the goal is to generate systems that satisfy their specification when interacting with rational environments. Our study includes Boolean objectives given by automata or LTL formulas, as well as a multi-valued setting, where the objectives are ${\\text{LTL}}\\left[ {\\mathcal{F}} \\right]$ formulas with satisfaction values in [0, 1], and the agents aim to maximize the satisfaction value of their objectives.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470616"}, {"primary_key": "2206235", "vector": [], "sparse_vector": [], "title": "A Compositional Cost Model for the λ-calculus.", "authors": ["<PERSON>"], "summary": "We describe a (time) cost model for the (call-by-value) λ-calculus based on a natural presentation of its game semantics: the cost of computing a finite approximant to the denotation of a term (its evaluation tree) is the size of its smallest derivation in the semantics. This measure has an optimality property enabling compositional reasoning about cost bounds: for any term A, context C[_] and approximants a and c to the trees of A and C[A], the cost of computing c from C[A] is no more than the cost of computing a from A and c from C[a].Although the natural semantics on which it is based is nondeterministic, our cost model is reasonable: we describe a deterministic algorithm for recognizing evaluation tree approximants which satisfies it (up to a constant factor overhead) on a Random Access Machine. This requires an implementation of the λ v -calculus on the RAM which is completely lazy: compositionality of costs entails that work done to evaluate any part of a term cannot be duplicated. This is achieved by a novel implementation of graph reduction for nameless explicit substitutions, to which we compile the λ v -calculus via a series of linear cost reductions.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470567"}, {"primary_key": "2206236", "vector": [], "sparse_vector": [], "title": "Separating Rank Logic from Polynomial Time.", "authors": ["<PERSON><PERSON>"], "summary": "In the search for a logic capturing polynomial time the most promising candidates are Choiceless Polynomial Time (CPT) and rank logic. Rank logic extends fixed-point logic with counting by a rank operator over prime fields. We show that the isomorphism problem for CFI graphs over ${{\\mathbb{Z}}_{{2^i}}}$ cannot be defined in rank logic, even if the base graph is totally ordered. However, CPT can define this isomorphism problem. We thereby separate rank logic from CPT and in particular from polynomial time.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470598"}, {"primary_key": "2206237", "vector": [], "sparse_vector": [], "title": "Towards a more efficient approach for the satisfiability of two-variable logic.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We revisit the satisfiability problem for two-variable logic, denoted by SAT(FO 2 ), which is known to be NEXP-complete. The upper bound is usually derived from its well known exponential size model property. Whether it can be determinized/randomized efficiently is still an open question.In this paper we present a different approach by reducing it to a novel graph-theoretic problem that we call Conditional Independent Set (CIS). We show that CIS is NP-complete and present three simple algorithms for it: Deterministic, randomized with zero error and randomized with small one-sided error, with run time O(1.4423 n ), O(1.6181 n ) and O(1.3661 n ), respectively.We then show that without the equality predicate SAT(FO 2 ) is in fact equivalent to CIS in succinct representation. This yields the same three simple algorithms as above for SAT(FO 2 ) without the the equality predicate with run time O(1.4423 (2n) ), O(1.6181 (2n) ) and O(1.3661 (2n) ), respectively, where n is the number of predicates in the input formula. To the best of our knowledge, these are the first deterministic/randomized algorithms for an NEXP-complete decidable logic with time complexity significantly lower than O(2 (2n) ). We also identify a few lower complexity fragments of FO 2 which correspond to the tractable fragments of CIS.For the fragment with the equality predicate, we present a linear time many-one reduction to the fragment without the equality predicate. The reduction yields equi-satisfiable formulas and incurs a small constant blow-up in the number of predicates.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470502"}, {"primary_key": "2206238", "vector": [], "sparse_vector": [], "title": "Universal Skolem Sets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "It is a longstanding open problem whether there is an algorithm to decide the Skolem Problem for linear recurrence sequences, namely whether a given such sequence has a zero term. In this paper we introduce the notion of a Universal Skolem Set: an infinite subset S of the positive integers such that there is an effective procedure that inputs a linear recurrence sequence u = (u(n))n ≥ 0and decides whether u(n) = 0 for some n ∈ S . The main technical contribution of the paper is to exhibit such a set.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470513"}, {"primary_key": "2206239", "vector": [], "sparse_vector": [], "title": "An Algebraic Characterisation of First-Order Logic with Neighbour.", "authors": ["<PERSON><PERSON><PERSON>", "Dhr<PERSON>v Nevatia"], "summary": "We give an algebraic characterisation of first-order logic with the neighbour relation, on finite words. For this, we consider languages of finite words over alphabets with an involution on them. The natural algebras for such languages are involution semigroups. To characterise the logic, we define a special kind of semidirect product of involution semigroups, called the locally hermitian product. The characterisation theorem for FO with neighbour states that a language is definable in the logic if and only if it is recognised by a locally hermitian product of an aperiodic commutative involution semigroup, and a locally trivial involution semigroup. We then define the notion of involution varieties of languages, namely classes of languages closed under Boolean operations, quotients, involution, and inverse images of involutory morphisms. An Eilenberg-type correspondence is established between involution varieties of languages and pseudovarieties of involution semigroups.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470660"}, {"primary_key": "2206240", "vector": [], "sparse_vector": [], "title": "Fixed-Points for Quantitative Equational Logics.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We develop a fixed-point extension of quantitative equational logic and give semantics in one-bounded complete quantitative algebras. Unlike previous related work about fixed-points in metric spaces, we are working with the notion of approximate equality rather than exact equality. The result is a novel theory of fixed points which can not only provide solutions to the traditional fixed-point equations but we can also define the rate of convergence to the fixed point. We show that such a theory is the quantitative analogue of a Conway theory and also of an iteration theory; and it reflects the metric coinduction principle. We study the Bellman equation for a Markov decision process as an illustrative example.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470662"}, {"primary_key": "2206241", "vector": [], "sparse_vector": [], "title": "Responsibility and verification: Importance value in temporal logics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We aim at measuring the influence of the nondeterministic choices of a part of a system on its ability to satisfy a specification. For this purpose, we apply the concept of Shapley values to verification as a means to evaluate how important a part of a system is. The importance of a component is measured by giving its control to an adversary, alone or along with other components, and testing whether the system can still fulfill the specification. We study this idea in the framework of model-checking with various classical types of linear-time specification, and propose several ways to transpose it to branching ones. We also provide tight complexity bounds in almost every case.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470597"}, {"primary_key": "2206242", "vector": [], "sparse_vector": [], "title": "A distributed operational view of Reversible Prime Event Structures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Reversible prime event structures extend the well-known model of prime event structures to represent reversible computational processes. Essentially, they give abstract descriptions of processes capable of undoing computation steps. Since their introduction, event structures have played a pivotal role in connecting operational models (traditionally, Petri nets and process calculi) with denotational ones (algebraic domains). For this reason, there has been a lot of interest in linking different classes of operational models with different kinds of event structures. Hence, it is natural to ask which is the operational counterpart of reversible prime event structures. Such question has been previously addressed for a subclass of reversible prime event structures in which the interplay between causality and reversibility is restricted to the so-called cause-respecting reversible structures. In this paper, we present an operational characterisation of the full-fledged model and show that reversible prime event structures correspond to a subclass of contextual Petri nets, called reversible causal nets. The distinctive feature of reversible causal nets is that causality is recovered from inhibitor arcs instead of the usual overlap between post and presets of transitions. In this way, we are able to operationally explain also out-of-causal order reversibility.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470623"}, {"primary_key": "2206243", "vector": [], "sparse_vector": [], "title": "Asynchronous Template Games and the Gray Tensor Product of 2-Categories.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In his recent and exploratory work on template games and linear logic, <PERSON><PERSON><PERSON> defines sequential and concurrent games as categories with positions as objects and trajectories as morphisms, labelled by a specific synchronization template. In the present paper, we bring the idea one dimension higher and advocate that template games should not be just defined as 1-dimensional categories but as 2-dimensional categories of positions, trajectories and reshufflings (or reschedulings) as 2-cells. In order to achieve the purpose, we take seriously the parallel between asynchrony in concurrency and the Gray tensor product of 2-categories. One technical difficulty on the way is that the category $\\mathbb{S} = 2$-Cat of small 2-categories equipped with the Gray tensor product is monoidal, and not cartesian. This prompts us to extend the framework of template games originally formulated by <PERSON><PERSON><PERSON> in a category $\\mathbb{S}$ with finite limits, and to upgrade it in the style of <PERSON><PERSON><PERSON>'s work on quantum groups to the more general situation of a monoidal category $\\mathbb{S}$ with coreflexive equalizers, preserved by the tensor product componentwise. We construct in this way an asynchronous template game semantics of multiplicative additive linear logic (MALL) where every formula and every proof is interpreted as a labelled 2-category equipped, respectively, with the structure of Gray comonoid for asynchronous template games, and of Gray bicomodule for asynchronous strategies.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470758"}, {"primary_key": "2206244", "vector": [], "sparse_vector": [], "title": "PTAS for Sparse General-Valued CSPs.", "authors": ["Balázs F. Mezei", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study polynomial-time approximation schemes (PTASes) for constraint satisfaction problems (CSPs) such as Maximum Independent Set or Minimum Vertex Cover on sparse graph classes.<PERSON>'s approach gives a PTAS on planar graphs, excluded-minor classes, and beyond. For Max-CSPs, and even more generally, maximisation finite-valued CSPs (where constraints are arbitrary non-negative functions), <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> [SODA'21] showed that the Sherali-Adams LP relaxation gives a simple PTAS for all fractionally-treewidth-fragile classes, which is the most general \"sparsity\" condition for which a PTAS is known. We extend these results to general-valued CSPs, which include \"crisp\" (or \"strict\") constraints that have to be satisfied by every feasible assignment. The only condition on the crisp constraints is that their domain contains an element which is at least as feasible as all the others (but possibly less valuable).For minimisation general-valued CSPs with crisp constraints, we present a PTAS for all Baker graph classes - a definition by <PERSON><PERSON><PERSON> [SODA'20] which encompasses all classes where <PERSON>'s technique is known to work, except for fractionally-treewidth-fragile classes. While this is standard for problems satisfying a certain monotonicity condition on crisp constraints, we show this can be relaxed to diagonalisability - a property of relational structures connected to logics, statistical physics, and random CSPs.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470599"}, {"primary_key": "2206245", "vector": [], "sparse_vector": [], "title": "Combining Nondeterminism, Probability, and Termination: Equational and Metric Reasoning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study monads resulting from the combination of nondeterministic and probabilistic behaviour with the possibility of termination, which is essential in program semantics. Our main contributions are presentation results for the monads, providing equational reasoning tools for establishing equivalences and distances of programs.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470717"}, {"primary_key": "2206246", "vector": [], "sparse_vector": [], "title": "Parametricity and Semi-Cubical Types.", "authors": ["<PERSON>"], "summary": "We construct a model of type theory enjoying parametricity from an arbitrary one. A type in the new model is a semi-cubical type in the old one, illustrating the correspondence between parametricity and cubes.Our construction works not only for parametricity, but also for similar interpretations of type theory and in fact similar interpretations of any generalized algebraic theory. To be precise we consider a functor forgetting unary operations and equations defining them recursively in a generalized algebraic theory. We show that it has a right adjoint.We use techniques from locally presentable category theory, as well as from quotient inductive-inductive types.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470728"}, {"primary_key": "2206247", "vector": [], "sparse_vector": [], "title": "Alignment Completeness for Relational Hoare Logics.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Relational Hoare logics (RHL) provide rules for reasoning about relations between programs. Several RHLs include a rule we call sequential product that infers a relational correctness judgment from judgments of ordinary Hoare logic (HL). Other rules embody sensible patterns of reasoning and have been found useful in practice, but sequential product is relatively complete on its own (with HL). As a more satisfactory way to evaluate RHLs, a notion of alignment completeness is introduced, in terms of the inductive assertion method and product automata. Alignment completeness results are given to account for several different sets of rules. The notion may serve to guide the design of RHLs and relational verifiers for richer programming languages and alignment patterns.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470690"}, {"primary_key": "2206248", "vector": [], "sparse_vector": [], "title": "Intersection Type Distributors.", "authors": ["<PERSON>"], "summary": "We study a family of distributors-induced bicategorical models of λ-calculus, proving that they can be syntactically presented via intersection type systems. We first introduce a class of 2-monads whose algebras are monoidal categories modelling resource management. We lift these monads to distributors and define a parametric Kleisli bicategory, giving a sufficient condition for its cartesian closure. In this framework we define a proof-relevant semantics: the interpretation of a term associates to it the set of its typing derivations in appropriate systems. We prove that our model characterize solvability, adapting reducibility techniques to our setting. We conclude by describing two examples of our construction.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470617"}, {"primary_key": "2206249", "vector": [], "sparse_vector": [], "title": "First-Order Reasoning and Efficient Semi-Algebraic Proofs.", "authors": ["Fedor <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Semi-algebraic proof systems such as sum-of-squares (SoS) have attracted a lot of attention recently due to their relation to approximation algorithms [3]: constant degree semi-algebraic proofs lead to conjecturally optimal polynomial-time approximation algorithms for important NP-hard optimization problems (cf. [4]). Motivated by the need to allow a more streamlined and uniform framework for working with SoS proofs than the restrictive propositional level, we initiate a systematic first-order logical investigation into the kinds of reasoning possible in algebraic and semi-algebraic proof systems. Specifically, we develop first-order theories that capture in a precise manner constant degree algebraic and semi-algebraic proof systems: every statement of a certain form that is provable in our theories translates into a family of constant degree polynomial calculus or SoS refutations, respectively; and using a reflection principle, the converse also holds.This places algebraic and semi-algebraic proof systems in the established framework of bounded arithmetic, while providing theories corresponding to systems that vary quite substantially from the usual propositional-logic ones.We give examples of how our semi-algebraic theory proves statements such as the pigeonhole principle, we provide a separation between algebraic and semi-algebraic theories, and we describe initial attempts to go beyond these theories by introducing extensions that use the inequality symbol, identifying along the way which extensions lead outside the scope of constant degree SoS. Moreover, we prove new results for propositional proofs, and specifically extend <PERSON><PERSON><PERSON><PERSON>'s [7] dynamic-by-static simulation of polynomial calculus (PC) by SoS to PC with the radical rule.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470546"}, {"primary_key": "2206250", "vector": [], "sparse_vector": [], "title": "On Generalized Metric Spaces for the Simply Typed Lambda-Calculus.", "authors": ["<PERSON>"], "summary": "Generalized metrics, arising from <PERSON><PERSON>'s view of metric spaces as enriched categories, have been widely applied in denotational semantics as a way to measure to which extent two programs behave in a similar, although non equivalent, way. However, the application of generalized metrics to higher-order languages like the simply typed lambda calculus has so far proved unsatisfactory. In this paper we investigate a new approach to the construction of cartesian closed categories of generalized metric spaces. Our starting point is a quantitative semantics based on a generalization of usual logical relations. Within this setting, we show that several families of generalized metrics provide ways to extend the Euclidean metric to all higher-order types.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470696"}, {"primary_key": "2206251", "vector": [], "sparse_vector": [], "title": "A Normal Form Characterization for Efficient Boolean Skolem Function Synthesis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Supratik Chakraborty"], "summary": "Boolean Skolem function synthesis concerns syn¬thesizing outputs as Boolean functions of inputs such that a relational specification between inputs and outputs is satisfied. This problem, also known as Boolean functional synthesis, has several applications, including design of safe controllers for autonomous systems, certified QBF solving, cryptanalysis etc. Recently, complexity theoretic hardness results have been shown for the problem, although several algorithms proposed in the literature are known to work well in practice. This dichotomy between theoretical hardness and practical efficacy has motivated research on normal forms of specification representation that guarantee efficient synthesis, thus partially explaining the efficacy of some of these algorithms.In this paper we go one step further and ask if there exists a normal form representation of the specification that precisely characterizes \"efficient\" synthesis. We present a normal form called SAUNF that answers this question affirmatively. Specifically, a specification is polynomial time synthesizable iff it can be compiled to SAUNF in polynomial time. Additionally, a specification admits a polynomial-sized functional solution iff there exists a semantically equivalent polynomial-sized SAUNF representation. SAUNF is exponentially more succinct than well- established normal forms like BDDs and DNNFs, used in the context of AI problems, and strictly subsumes other more recently proposed forms like SynNNF. It enjoys compositional properties that are similar to those of DNNF. Thus, SAUNF provides the right trade-off in knowledge representation for Boolean functional synthesis.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470741"}, {"primary_key": "2206252", "vector": [], "sparse_vector": [], "title": "Compositional Semantics for Probabilistic Programs with Exact Conditioning.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We define a probabilistic programming language for Gaussian random variables with a first-class exact conditioning construct. We give operational, denotational and equational semantics for this language, establishing convenient properties like exchangeability of conditions. Conditioning on equality of continuous random variables is nontrivial, as the exact observation may have probability zero; this is <PERSON><PERSON>'s paradox. Using categorical formulations of conditional probability, we show that the good properties of our language are not particular to Gaussians, but can be derived from universal properties, thus generalizing to wider settings. We define the Cond construction, which internalizes conditioning as a morphism, providing general compositional semantics for probabilistic programming with exact conditioning.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470552"}, {"primary_key": "2206253", "vector": [], "sparse_vector": [], "title": "Normalization for Cubical Type Theory.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We prove normalization for (univalent, Cartesian) cubical type theory, closing the last major open problem in the syntactic metatheory of cubical type theory. Our normalization result is reduction-free, in the sense of yielding a bijection between equivalence classes of terms in context and a tractable language of $β/η$-normal forms. As corollaries we obtain both decidability of judgmental equality and the injectivity of type constructors.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470719"}, {"primary_key": "2206254", "vector": [], "sparse_vector": [], "title": "Inapproximability of Unique Games in Fixed-Point Logic with Counting.", "authors": ["<PERSON>"], "summary": "We study the extent to which it is possible to approximate the optimal value of a Unique Games instance in Fixed-Point Logic with Counting (FPC). We prove two new FPC- inexpressibility results for Unique Games: the existence of a ( \\frac12,\\frac13 + δ )-inapproximability gap, and inapproximability to within any constant factor. Previous recent work has established similar FPC-inapproximability results for a small handful of other problems. Our construction builds upon some of these ideas, but contains a novel technique. While most FPC-inexpressibility results are based on variants of the CFI-construction, ours is significantly different.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470706"}, {"primary_key": "2206255", "vector": [], "sparse_vector": [], "title": "Zero-one laws for provability logic: Axiomatizing validity in almost all models and almost all frames.", "authors": ["Rineke Verbrugge"], "summary": "It has been shown in the late 1960s that each formula of first-order logic without constants and function symbols obeys a zero-one law: As the number of elements of finite models increases, every formula holds either in almost all or in almost no models of that size. Therefore, many properties of models, such as having an even number of elements, cannot be expressed in the language of first-order logic. For modal logics, limit behavior for models and frames may differ. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> proved zero-one laws for classes of models corresponding to the modal logics K, T, S4, and S5. They also proposed zero-one laws for the corresponding classes of frames, but their zero-one law for K-frames has since been disproved.In this paper, we prove zero-one laws for provability logic with respect to both model and frame validity. Moreover, we axiomatize validity in almost all irreflexive transitive finite models and in almost all irreflexive transitive finite frames, leading to two different axiom systems. In the proofs, we use a combinatorial result by <PERSON><PERSON><PERSON><PERSON> and <PERSON> about the structure of almost all finite partial orders. On the way, we also show that a previous result by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> about the axiomatization of almost sure frame validity for S4 is not correct. Finally, we consider the complexity of deciding whether a given formula is almost surely valid in the relevant finite models and frames.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470666"}, {"primary_key": "2206256", "vector": [], "sparse_vector": [], "title": "Complexity Lower Bounds from Algorithm Design.", "authors": ["<PERSON><PERSON>"], "summary": "Since the beginning of the theory of computation, researchers have been fascinated by the prospect of proving impossibility results on computing. When and how can we argue that a task cannot be efficiently solved, no matter what algorithm we try to use?In this short article, I will briefly introduce some of the ideas behind a research program in computational complexity that <PERSON> and others have studied, for the last decade. (The accompanying talk will contain more details.) The program begins with the observations that:(a) Computer scientists know a great deal about how to design efficient algorithms.(b) However, we do not know how to prove many weak-looking complexity lower bounds.It turns out that certain knowledge we have from (a) can be leveraged to prove complexity lower bounds in a systematic way, making progress on (b). For example, progress on faster circuit satisfiability algorithms (even those that barely improve upon exhaustive search) automatically imply circuit complexity lower bounds for interesting functions 1 .", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470522"}, {"primary_key": "2206257", "vector": [], "sparse_vector": [], "title": "A Quantum Interpretation of Bunched Logic &amp; Quantum Separation Logic.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a model of the substructural logic of Bunched Implications (BI) that is suitable for reasoning about quantum states. In our model, the separating conjunction of BI describes separable quantum states. We develop a program logic where pre- and post-conditions are BI formulas describing quantum states—the program logic can be seen as a counterpart of separation logic for imperative quantum programs. We exercise the logic for proving the security of quantum one-time pad and secret sharing, and we show how the program logic can be used to discover a flaw in Google Cirq's tutorial on the Variational Quantum Algorithm (VQA).", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470673"}, {"primary_key": "2206258", "vector": [], "sparse_vector": [], "title": "No-Rainbow Problem and the Surjective Constraint Satisfaction Problem.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Surjective Constraint Satisfaction Problem (SCSP) is the problem of deciding whether there exists a surjective assignment to a set of variables subject to some specified constraints, where a surjective assignment is an assignment containing all elements of the domain. In this paper we show that the most famous SCSP, called No-Rainbow Problem, is NP-Hard. Additionally, we disprove the conjecture saying that the SCSP over a constraint language Γ and the CSP over the same language with constants have the same computational complexity up to poly-time reductions. Our counter-example also shows that the complexity of the SCSP cannot be described in terms of polymorphisms of the constraint language.", "published": "2021-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS52264.2021.9470632"}]