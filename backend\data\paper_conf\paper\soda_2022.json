[{"primary_key": "1764353", "vector": [], "sparse_vector": [], "title": "Friendly Cut Sparsifiers and Faster Gomory-Hu Trees.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Friendly Cut Sparsifiers and Faster Gomory-<PERSON> <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.3630 - 3649<PERSON>hapter DOI:https://doi.org/10.1137/1.*************.143PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We devise new cut sparsifiers that are related to the classical sparsification of <PERSON>gamochi and Ibaraki [Algorithmica, 1992], which is an algorithm that, given an unweighted graph G on n nodes and a parameter k, computes a subgraph with O(nk) edges that preserves all cuts of value up to k. We put forward the notion of a friendly cut sparsifier, which is a minor of G that preserves all friendly cuts of value up to k, where a cut in G is called friendly if every node has more edges connecting it to its own side of the cut than to the other side. We present an algorithm that, given a simple graph G, computes in almost-linear time a friendly cut sparsifier with edges. Using similar techniques, we also show how, given in addition a terminal set T, one can compute in almost-linear time a terminal sparsifier, which preserves the minimum st-cut between every pair of terminals, with edges. Plugging these sparsifiers into the recent n2+o(1)-time algorithms for constructing a Gomory-Hu tree of simple graphs, along with a relatively simple procedure for handling the unfriendly minimum cuts, we improve the running time for moderately dense graphs (e.g., with m = n1.75 edges). In particular, assuming a linear-time Max-Flow algorithm, the new state-of-the-art for Gomory-Hu tree is the minimum between our (m + n1.75)1+o(1) and the known mn1/2+o(1). We further investigate the limits of this approach and the possibility of better sparsification. Under the hypothesis that an Õ(n)-edge sparsifier that preserves all friendly minimum st-cuts can be computed efficiently, our upper bound improves to Õ(m + n1.5) which is the best possible without breaking the cubic barrier for constructing Gomory-Hu trees in non-simple graphs. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.143"}, {"primary_key": "1764354", "vector": [], "sparse_vector": [], "title": "Polynomial-time algorithm for Maximum Independent Set in bounded-degree graphs with no long induced claws.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Polynomial-time algorithm for Maximum Independent Set in bounded-degree graphs with no long induced claws<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1448 - 1470Chapter DOI:https://doi.org/10.1137/1.*************.61PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract For graphs G and H, we say that G is H-free if it does not contain H as an induced subgraph. Already in the early 1980s <PERSON><PERSON><PERSON><PERSON> observed that if H is connected, then the Max Weight Independent Set problem (MWIS) remains NP-hard in H-free graphs, unless H is a path or a subdivided claw, i.e., a graph obtained from the three-leaf star by subdividing each edge some number of times (possibly zero). Since then determining the complexity of MWIS in these remaining cases is one of the most important problems in algorithmic graph theory. A general belief is that the problem is polynomial-time solvable, which is witnessed by algorithmic results for graphs excluding some small paths or subdivided claws. A more conclusive evidence was given by the recent breakthrough result by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [FOCS 2020]: They proved that MWIS can be solved in quasipolynomial time in H-free graphs, where H is any fixed path. If H is an arbitrary subdivided claw, we know much less: The problem admits a QPTAS and a subexponential-time algorithm [Chudnovsky et al., SODA 2019]. In this paper we make an important step towards solving the problem by showing that for any subdivided claw H, MWIS is polynomial-time solvable in H-free graphs of bounded degree. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.61"}, {"primary_key": "1764355", "vector": [], "sparse_vector": [], "title": "Deleting, Eliminating and Decomposing to Hereditary Classes Are All FPT-Equivalent.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Deleting, Eliminating and Decomposing to Hereditary Classes Are All FPT-Equi<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1976 - 2004Chapter DOI:https://doi.org/10.1137/1.*************.79PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Vertex-deletion problems have been at the heart of parameterized complexity throughout its history. Here, the aim is to determine the minimum size (denoted by modℋ) of a modulator to a graph class ℋ, i.e., a set of vertices whose deletion results in a graph in ℋ. Recent years have seen the development of a research programme where the complexity of modulators is measured in ways other than size. For instance, for a graph class ℋ, the graph parameters elimination distance to ℋ (denoted by edℋ) [<PERSON><PERSON><PERSON> and <PERSON>, <PERSON>gor<PERSON>a, 2016] and ℋ-treewidth (denoted by twℋ) [<PERSON> et al. J<PERSON>S, 2021] aim to minimize the treedepth and treewidth, respectively, of the \"torso\" of the graph induced on a modulator to the graph class ℋ. Here, the torso of a vertex set S in a graph G is the graph with vertex set S and an edge between two vertices u, v ∊ S if there is a path between u and v in G whose internal vertices all lie outside S. In this paper, we show that from the perspective of (non-uniform) fixed-parameter tractability (FPT), the three parameters described above give equally powerful parameterizations for every hereditary graph class ℋ that satisfies mild additional conditions. In fact, we show that for every hereditary graph class ℋ satisfying mild additional conditions, with the exception of edℋ parameterized by twℋ, for every pair of these parameters, computing one parameterized by itself or any of the others is FPT-equivalent to the standard vertex-deletion (to ℋ) problem. As an example, we prove that an FPT algorithm for the vertex-deletion problem implies a non-uniform FPT algorithm for computing edℋ and twℋ. The conclusions of non-uniform FPT algorithms being somewhat unsatisfactory, we essentially prove that if ℋ is hereditary, union-closed, CMSO-definable, and (a) the canonical equivalence relation (or any refinement thereof) for membership in the class can be efficiently computed, or (b) the class admits a \"strong irrelevant vertex rule\", then there exists a uniform FPT algorithm for edℋ. Using these sufficient conditions, we obtain uniform FPT algorithms for computing edℋ, when ℋ is defined by excluding a finite number of connected (a) minors, or (b) topological minors, or (c) induced subgraphs, or when ℋ is any of bipartite, chordal or interval graphs. For most of these problems, the existence of a uniform FPT algorithm has remained open in the literature. In fact, for some of them, even a non-uniform FPT algorithm was not known. For example, Jansen et al. [STOC 2021] ask for such an algorithm when ℋ is defined by excluding a finite number of connected topological minors. We resolve their question in the affirmative. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.79"}, {"primary_key": "1764356", "vector": [], "sparse_vector": [], "title": "Robust Load Balancing with Machine Learned Advice.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Robust Load Balancing with Machine Learned Advice<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> Pengpp.20 - 34Chapter DOI:https://doi.org/10.1137/1.*************.2PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Motivated by the exploding growth of web-based services and the importance of efficiently managing the computational resources of such systems, we introduce and study a theoretical model for load balancing of very large databases such as commercial search engines. Our model is a more realistic version of the well-received balls-into-bins model with an additional constraint that limits the number of servers that carry each piece of the data. This additional constraint is necessary when, on one hand, the data is so large that we can not copy the whole data on each server. On the other hand, the query response time is so limited that we can not ignore the fact that the number of queries for each piece of the data changes over time, and hence we can not simply split the data over different machines In this paper, we develop an almost optimal load balancing algorithm that works given an estimate of the load of each piece of the data. Our algorithm is almost perfectly robust to wrong estimates, to the extent that even when all of the loads are adversarially chosen the performance of our algorithm is 1–1/e, which is provably optimal. Along the way, we develop various techniques for analyzing the balls-into-bins process under certain correlations and build a novel connection with the multiplicative weights update scheme. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.2"}, {"primary_key": "1764357", "vector": [], "sparse_vector": [], "title": "Near-Optimal Quantum Algorithms for String Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Near-Optimal Quantum Algorithms for String ProblemsShyan Akmal and Ce JinShyan Akmal and Ce Jinpp.2791 - 2832Chapter DOI:https://doi.org/10.1137/1.*************.109PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study quantum algorithms for several fundamental string problems, including Longest Common Substring, Lexicographically Minimal String Rotation, and Longest Square Substring. These problems have been widely studied in the stringology literature since the 1970s, and are known to be solvable by near-linear time classical algorithms. In this work, we give quantum algorithms for these problems with near-optimal query complexities and time complexities. Specifically, we show that: Longest Common Substring can be solved by a quantum algorithm in Õ(n2/3) time, improving upon the recent Õ(n5/6)-time algorithm by <PERSON> and <PERSON><PERSON><PERSON><PERSON> (2020). Our algorithm uses the MNRS quantum walk framework, together with a careful combination of string synchronizing sets (<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, 2019) and generalized difference covers. Lexicographically Minimal String Rotation can be solved by a quantum algorithm in n1/2 + o(1) time, improving upon the recent Õ(n3/4)-time algorithm by <PERSON> and <PERSON> (2020). We design our algorithm by first giving a new classical divide-and-conquer algorithm in near-linear time based on exclusion rules, and then speeding it up quadratically using nested Grover search and quantum minimum finding. Longest Square Substring can be solved by a quantum algorithm in time. Our algorithm is an adaptation of the algorithm by Le Gall and Seddighin (2020) for the Longest Palindromic Substring problem, but uses additional techniques to overcome the difficulty that binary search no longer applies. Our techniques naturally extend to other related string problems, such as Longest Repeated Substring, Longest Lyndon Substring, and Minimal Suffix. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.109"}, {"primary_key": "1764358", "vector": [], "sparse_vector": [], "title": "Algorithms Using Local Graph Features to Predict Epidemics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Algorithms Using Local Graph Features to Predict Epidemics<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.3430 - 3451Chapter DOI:https://doi.org/10.1137/1.*************.136PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study a simple model of epidemics where an infected node transmits the infection to its neighbors independently with probability p. This is also known as the independent cascade or Susceptible-Infected-Recovered (SIR) model with fixed recovery time. The size of an outbreak in this model is closely related to that of the giant connected component in \"edge percolation\", where each edge of the graph is kept independently with probability p, studied for a large class of networks including configuration model [30] and preferential attachment [15, 37]. Even though these models capture the effects of degree inhomogeneity and the role of super-spreaders in the spread of an epidemic, they only consider graphs that are locally tree like i.e. have a few or no short cycles. Some generalizations of the configuration model were suggested to capture local communities, known as household models [6], or hierarchical configuration model [48]. Here, we ask a different question: what information is needed for general networks to predict the size of an outbreak? Is it possible to make predictions by accessing the distribution of small subgraphs (or motifs)? We answer the question in the affirmative for large-set expanders with local weak limits (also known as Benjamini-Schramm limits). In particular, we show that there is an algorithm which gives a (1–∊) approximation of the probability and the final size of an outbreak by accessing a constant-size neighborhood of a constant number of nodes chosen uniformly at random. We also present corollaries of the theorem for the preferential attachment model, and study generalizations with household (or motif) structure. The latter was only known for the configuration model. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.136"}, {"primary_key": "1764359", "vector": [], "sparse_vector": [], "title": "Untangling Planar Graphs and Curves by Staying Positive.", "authors": ["Santiago Aranguri", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Untangling Planar Graphs and Curves by Staying Po<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>pp.211 - 225Chapter DOI:https://doi.org/10.1137/1.*************.11PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Any generic planar closed curve with n crossings can be turned into a simple closed curve by applying O(n3/2) homotopy moves without ever increasing the number of self-crossings; this improves over the O(n2) upper bound from <PERSON>itz [Ency. Math. Wiss. III 1916], and matches the best lower bound. We prove the existence of a positive move that decreases the depth-sum potential at every step. Using similar techniques, we show that any 2-terminal plane graph with n vertices can be reduced to a single edge between the terminals using O(n3/2) electrical transformations, consisting of degree-1 reductions, series-parallel reductions, and ΔY-transformations; this proves a conjecture of <PERSON><PERSON> and <PERSON><PERSON> that was open for more than 30 years. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.11"}, {"primary_key": "1764360", "vector": [], "sparse_vector": [], "title": "Robust Secretary and Prophet Algorithms for Packing Integer Programs.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Robust Secretary and Prophet Algorithms for Packing Integer ProgramsC<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.1273 - 1297Chapter DOI:https://doi.org/10.1137/1.*************.53PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the problem of solving Packing Integer Programs (PIPs) in the online setting, where columns in [0, 1]d of the constraint matrix are revealed sequentially, and the goal is to pick a subset of the columns that sum to at most B in each coordinate while maximizing the objective. Excellent results are known in the secretary setting, where the columns are adversarially chosen, but presented in a uniformly random order. However, these existing algorithms are susceptible to adversarial attacks: they try to \"learn\" characteristics of a good solution, but tend to over-fit to the model, and hence a small number of adversarial corruptions can cause the algorithm to fail. In this paper, we give the first robust algorithms for Packing Integer Programs, specifically in the recently proposed Byzantine Secretary framework [BGSZ20]. Our techniques are based on a two-level use of online learning, to robustly learn an approximation to the optimal value, and then to use this robust estimate to pick a good solution. These techniques are general and we use them to design robust algorithms for PIPs in the prophet model as well, specifically in the Prophet-with-Augmentations framework [ISW20]. We also improve known results in the Byzantine Secretary framework: we make the non-constructive results algorithmic and improve the existing bounds for single-item and matroid constraints. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.53"}, {"primary_key": "1764361", "vector": [], "sparse_vector": [], "title": "Optimal Oblivious Parallel RAM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Optimal Oblivious Parallel RAM<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>.2459 - 2521Chapter DOI:https://doi.org/10.1137/1.*************.98PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract An oblivious RAM (ORAM), introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC '87 and J. ACM '96), is a technique for hiding RAM's access pattern. That is, for every input the distribution of the observed locations accessed by the machine is essentially independent of the machine's secret inputs. Recent progress culminated in a work of <PERSON><PERSON><PERSON> et al. (EUROCRYPT '20), obtaining an ORAM with (amortized) logarithmic overhead in total work, which is known to be optimal. Oblivious Parallel RAM (OPRAM) is a natural extension of ORAM to the (more realistic) parallel setting where several processors make concurrent accesses to a shared memory. It is known that any OPRAM must incur logarithmic work overhead (in the balls and bins model). Despite the significant recent advances for constructing ORAM, there is still a significant gap for OPRAM: all existing OPRAM schemes incur a poly-logarithmic overhead either in total work or in depth. Our main result closes the aforementioned gap and provides an optimal OPRAM. Specifically, assuming one-way functions, we show that any Parallel RAM with memory capacity N can be obliviously simulated in space O(N), incurring only O(log N) blowup in (amortized) total work as well as in depth. Our transformation supports all PRAMs in the CRCW (concurrent read, concurrent write) mode and the resulting simulation is in the CRCW mode as well. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.98"}, {"primary_key": "1764362", "vector": [], "sparse_vector": [], "title": "A Two-Pass (Conditional) Lower Bound for Semi-Streaming Maximum Matching.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A Two-Pass (Conditional) Lower Bound for Semi-Streaming Maximum MatchingSepehr AssadiSepehr Assadipp.708 - 742Chapter DOI:https://doi.org/10.1137/1.*************.32PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We prove a lower bound on the space complexity of two-pass semi-streaming algorithms that approximate the maximum matching problem. The lower bound is parameterized by the density of Ruzsa-Szemerédi graphs: Any two-pass semi-streaming algorithm for maximum matching has approximation ratio at most , where RS(n) denotes the maximum number of induced matchings of size Θ(n) in any n-vertex graph, i.e., the largest density of a Ruzsa-Szemerédi graph. Currently, it is known that and closing this (large) gap between upper and lower bounds has remained a notoriously difficult problem in combinatorics. Under the plausible hypothesis that RS(n) = nΩ(1), our lower bound is the first to rule out small-constant approximation two-pass semi-streaming algorithms for the maximum matching problem, making progress on a longstanding open question in the graph streaming literature. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.32"}, {"primary_key": "1764363", "vector": [], "sparse_vector": [], "title": "Semi-Streaming Bipartite Matching in Fewer Passes and Optimal Space.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Semi-Streaming Bipartite Matching in Fewer Passes and Optimal Space<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.627 - 669Chapter DOI:https://doi.org/10.1137/1.*************.29PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We provide Õ(∊–1)-pass semi-streaming algorithms for computing (1–∊)-approximate maximum cardinality matchings in bipartite graphs. Our most efficient methods are deterministic and use optimal, O(n), space, improving upon the space complexity of the previous state-of-the-art Õ(∊–1)-pass algorithm of [AG18]. To obtain our results we provide semi-streaming adaptations of more general continuous optimization tools. Further, we leverage these techniques to obtain improvements for streaming variants of approximate linear programming, optimal transport, exact matching, transshipment, and shortest path problems. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.29"}, {"primary_key": "1764364", "vector": [], "sparse_vector": [], "title": "Promise Constraint Satisfaction and Width.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Promise Constraint Satisfaction and Width<PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.1129 - 1153Chapter DOI:https://doi.org/10.1137/1.*************.48PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the power of the bounded-width consistency algorithm in the context of the fixed-template Promise Constraint Satisfaction Problem (PCSP). Our main technical finding is that the template of every PCSP that is solvable in bounded width satisfies a certain structural condition implying that its algebraic closure-properties include weak near unanimity polymorphisms of all large arities. While this parallels the standard (non-promise) CSP theory, the method of proof is quite different and applies even to the regime of sublinear width. We also show that, in contrast with the CSP world, the presence of weak near unanimity polymorphisms of all large arities does not guarantee solvability in bounded width. The separating example is even solvable in the second level of the Sherali-Adams (SA) hierarchy of linear programming relaxations. This shows that, unlike for CSPs, linear programming can be stronger than bounded width. A direct application of these methods also show that the problem of q-coloring p-colorable graphs is not solvable in bounded or even sublinear width, for any two constants p and q such that 3 ≤ p ≤ q. Turning to algorithms, we note that Wigderson's algorithm for -coloring 3-colorable graphs with n vertices is implementable in width 4. Indeed, by generalizing the method we see that, for any ∊ > 0 smaller than 1/2, the optimal width for solving the problem of O(n∊)-coloring 3-colorable graphs with n vertices lies between n1–3∊ and n1–2∊. The upper bound gives a simple exp(Θ(n1–2∊ log(n)))-time algorithm that, asymptotically, beats the straightforward exp(Θ(n1–∊)) bound that follows from partitioning the graph into O(n∊) many independent parts each of size O(n1–∊). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.48"}, {"primary_key": "1764365", "vector": [], "sparse_vector": [], "title": "Perfect Matching in Random Graphs is as Hard as <PERSON><PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Perfect Matching in Random Graphs is as Hard as <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.979 - 1012Chapter DOI:https://doi.org/10.1137/1.*************.43PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the complexity of proving that a sparse random regular graph on an odd number of vertices does not have a perfect matching, and related problems involving each vertex being matched some pre-specified number of times. We show that this requires proofs of degree Ω(n/log n) in the Polynomial Calculus (over fields of characteristic ≠ 2) and Sum-of-Squares proof systems, and exponential size in the bounded-depth Frege proof system. This resolves a question by <PERSON><PERSON><PERSON><PERSON> asking whether the Lovász-Schrijver proof system requires nδ rounds to refute these formulas for some δ > 0. The results are obtained by a worst-case to average-case reduction of these formulas relying on a topological embedding theorem which may be of independent interest. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.43"}, {"primary_key": "1764366", "vector": [], "sparse_vector": [], "title": "Distortion-Oblivious Algorithms for Minimizing Flow Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Distortion-Oblivious Algorithms for Minimizing Flow Time<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.252 - 274Chapter DOI:https://doi.org/10.1137/1.*************.13PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the classic online problem of scheduling on a single machine to minimize total flow time. In STOC 2021, the concept of robustness to distortion in processing times was introduced: for every distortion factor μ, an O(μ2)-competitive algorithm ALGμ which handles distortions up to μ was presented. However, using that result requires one to know the distortion of the input in advance, which is impractical. We present the first distortion-oblivious algorithms: algorithms which are competitive for every input of every distortion, and thus do not require knowledge of the distortion in advance. Moreover, the competitive ratios of our algorithms are Õ(μ), which is a quadratic improvement over the algorithm from STOC 2021, and is nearly optimal (we show a randomized lower bound of Ω(μ) on competitiveness). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.13"}, {"primary_key": "1764367", "vector": [], "sparse_vector": [], "title": "Online Graph Algorithms with Predictions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Online Graph Algorithms with Prediction<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>.35 - 66Chapter DOI:https://doi.org/10.1137/1.*************.3PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Online algorithms with predictions is a popular and elegant framework for bypassing pessimistic lower bounds in competitive analysis. In this model, online algorithms are supplied with future predictions and the goal is for the competitive ratio to smoothly interpolate between the best offline and online bounds as a function of the prediction error. In this paper, we study online graph problems with predictions. Our contributions are the following: The first question is defining prediction error. For graph/metric problems, there can be two types of error, locations that are not predicted, and locations that are predicted but the predicted and actual locations do not coincide exactly. We design a novel definition of prediction error called metric error with outliers to simultaneously capture both types of errors, which thereby generalizes previous definitions of error that only capture one of the two error types. We give a general framework for obtaining online algorithms with predictions that combines, in a \"black box\" fashion, existing online and offline algorithms, under certain technical conditions. To the best of our knowledge, this is the first general-purpose tool for obtaining online algorithms with predictions. Using our framework, we obtain tight bounds on the competitive ratio of several classical graph problems as a function of metric error with outliers: Steiner tree, Steiner forest, priority Steiner tree/forest, and uncapacitated/capacitated facility location. Both the definition of metric error with outliers and the general framework for combining offline and online algorithms are not specific to the problems that we consider in this paper. We hope that these will be useful for future work on other problems in this domain. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.3"}, {"primary_key": "1764368", "vector": [], "sparse_vector": [], "title": "High Dimensional Expanders: Eigenstripping, Pseudorandomness, and Unique Games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)High Dimensional Expanders: Eigenstripping, P<PERSON><PERSON>randomness, and Unique GamesMitali <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>.1069 - 1128Chapter DOI:https://doi.org/10.1137/1.*************.47PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Higher order random walks (HD-walks) on high dimensional expanders (HDX) have seen an incredible amount of study and application since their introduction by <PERSON> and <PERSON> (ITCS 2016), yet their broader combinatorial and spectral properties remain poorly understood. We develop a combinatorial characterization of the spectral structure of HD-walks on two-sided local-spectral expanders (<PERSON><PERSON> and Kaufman FOCS 2017), which offer a broad generalization of the well-studied Johnson and <PERSON> graphs. Our characterization, which shows that the spectra of HD-walks lie tightly concentrated in a few combinatorially structured strips, leads to novel structural theorems such as a tight ℓ2-characterization of edge-expansion, as well as to a new understanding of local-to-global graph algorithms on HDX. Towards the latter, we introduce a novel spectral complexity measure called Stripped Threshold Rank, and show how it can replace the (much larger) threshold rank as a parameter controlling the performance of algorithms on structured objects. Combined with a sum-of-squares proof for the former ℓ2-characterization, we give a concrete application of this framework to algorithms for unique games on HD-walks, where in many cases we improve the state of the art (Barak, Raghavendra, and Steurer FOCS 2011, and Arora, Barak, and Steurer JACM 2015) from nearly-exponential to polynomial time (e.g. for sparsifications of Johnson graphs or of slices of the q-ary hypercube). Our characterization of expansion also holds an interesting connection to hardness of approximation, where an ℓ∞-variant for the Grassmann graphs was recently used to resolve the 2-2 Games Conjecture (Khot, Minzer, and Safra FOCS 2018). We give a reduction from a related ℓ∞-variant to our ℓ2-characterization, but it loses factors in the regime of interest for hardness where the gap between ℓ2 and ℓ∞ structure is large. Nevertheless, our results open the door for further work on the use of HDX in hardness of approximation and their general relation to unique games. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.47"}, {"primary_key": "1764369", "vector": [], "sparse_vector": [], "title": "Deterministic Budget-Feasible Clock Auctions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Tan"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Deterministic Budget-Feasible Clock Auctions<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.2940 - 2963Chapter DOI:https://doi.org/10.1137/1.*************.114PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We revisit the well-studied problem of budget-feasible procurement, where a buyer with a strict budget constraint seeks to acquire services from a group of strategic providers (the sellers). During the last decade, several strategyproof budget-feasible procurement auctions have been proposed, aiming to maximize the value of the buyer, while eliciting each seller's true cost for providing their service. These solutions predominantly take the form of randomized sealed-bid auctions: they ask the sellers to report their private costs and then use randomization to determine which subset of services will be procured and how much each of the chosen providers will be paid, ensuring that the total payment does not exceed the buyer's budget. Our main result in this paper is a novel method for designing budget-feasible auctions, leading to solutions that outperform the previously proposed auctions in multiple ways. First, our solutions take the form of descending clock auctions, and thus satisfy a list of very appealing properties, such as obvious strategyproofness, group strategyproofness, transparency, and unconditional winner privacy; this makes these auctions much more likely to be used in practice. Second, in contrast to previous results that heavily depend on randomization, our auctions are deterministic. As a result, we provide an affirmative answer to one of the main open questions in this literature, asking whether a deterministic strategyproof auction can achieve a constant approximation when the buyer's valuation function is submodular over the set of services. In addition to this, we also provide the first deterministic budget-feasible auction that matches the approximation bound of the best-known randomized auction for the class of subadditive valuations. Finally, using our method, we improve the best-known approximation factor for monotone submodular valuations, which has been the focus of most of the prior work. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.114"}, {"primary_key": "1764370", "vector": [], "sparse_vector": [], "title": "An Improved Analysis of Greedy for Online Steiner Forest.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)An Improved Analysis of Greedy for Online Steiner ForestEtienne <PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.3202 - 3229Chapter DOI:https://doi.org/10.1137/1.*************.125PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract This paper considers the classic Online Steiner Forest problem where one is given a (weighted) graph G and an arbitrary set of k terminal pairs {{s1, t1}, …, {sk, tk}} that are required to be connected. The goal is to maintain a minimum-weight sub-graph that satisfies all the connectivity requirements as the pairs are revealed one by one. It has been known for a long time that no algorithm (even randomized) can be better than Ω(log(k))-competitive for this problem. Interestingly, a simple greedy algorithm is already very efficient for this problem. This algorithm can be informally described as follows: Upon arrival of a new pair {si, ti}, connect si and ti with the shortest path in the current metric, contract the metric along the chosen path and wait for the next pair. Although simple and intuitive, greedy proved itself challenging to analyze and its competitive ratio is a longstanding open problem in the area of online algorithms. The last progress on this problem is due to an elegant analysis by Awerbuch, Azar, and <PERSON>al [SODA 1996], who showed that greedy is O(log2(k))-competitive. In this paper, we identify a natural measure of the \"efficiency\" of greedy that we call the contraction. The contraction of a pair {si, ti} is the ratio between the distance dG (si, ti) in the graph G and the actual cost that greedy pays for connecting the pair {si, ti}. Intuitively, a worst-case instance should be an instance on which greedy is very \"inefficient\", i.e. an instance for which all pairs have a relatively small contraction. Indeed, one can remark that all hard instances that appeared in the literature are such that all pairs have a contraction of exactly 1 (which is the smallest contraction possible). Our main result, among others, is to show that greedy is O(log(k) log log(k))-competitive on such instances. At the heart of this new result lies an original use of dual fitting, in which we use the dual solution not only to lower bound the optimum as it is usually the case in competitive analysis, but also to recursively partition the global instance into several disjoint instances of much smaller complexity. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.125"}, {"primary_key": "1764371", "vector": [], "sparse_vector": [], "title": "Subexponential Parameterized Algorithms for Cut and Cycle Hitting Problems on H&lt;-Minor-Free Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Subexponential Parameterized Algorithms for Cut and Cycle Hitting Problems on H-Minor-Free Graphs<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.2063 - 2084Chapter DOI:https://doi.org/10.1137/1.*************.82PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We design the first subexponential-time (parameterized) algorithms for several cut and cycle-hitting problems on H-minor free graphs. In particular, we obtain the following results (where k is the solution-size parameter). time algorithms for Edge Bipartization and Odd Cycle Transversal; a time algorithm for Edge Multiway Cut and a time algorithm for Vertex Multiway Cut (with undeletable terminals), where r is the number of terminals to be separated; a time algorithm for Edge Multicut and a time algorithm for Vertex Multicut (with undeletable terminals), where r is the number of terminal pairs to be separated; a time algorithm for Group Feedback Edge Set and a time algorithm for Group Feedback Vertex Set, where g is the size of the group. In addition, our approach also gives time algorithms for all above problems with the exception of time for Edge/Vertex Multicut and time for Group Feedback Edge/Vertex Set. All of our FPT algorithms (the first four items above) are randomized, as they use known randomized kernelization algorithms as sub-routines. We obtain our results by giving a new decomposition theorem on graphs of bounded genus, or more generally, an h-almost-embeddable graph for an arbitrary but fixed constant h. Our new decomposition theorem generalizes known Contraction Decomposition Theorem. Prior studies on this topic exhibited that the classes of planar graphs [Klein, SICOMP, 2008], graphs of bounded genus [Demaine, Hajiaghayi and Mohar, Combinatorica 2010] and H-minor free graphs [Demaine, Hajiaghayi and Kawarabayashi, STOC 2011] admit a Contraction Decomposition Theorem. In particular we show the following. Let G be a graph of bounded genus, or more generally, an h-almost-embeddable graph for an arbitrary but fixed constant h. Then for every p ∊ ℕ, there exist disjoint sets Z1, …, Zp ⊆ V(G) such that for every i ∊ {1, …, p} and every Z′ ⊆ Zi, the treewidth of G/(Zi\\Z′) is upper bounded by O(p + |Z′|), where the constant hidden in O(·) depends on h. Here G/(Zi\\Z′) denotes the graph obtained from G by contracting every edge with both endpoints in Zi\\Z′. When Z′ = , this corresponds to classical Contraction Decomposition Theorem. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.82"}, {"primary_key": "1764372", "vector": [], "sparse_vector": [], "title": "Online Nash Social Welfare Maximization with Predictions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Online Nash Social Welfare Maximization with Prediction<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>.1 - 19Chapter DOI:https://doi.org/10.1137/1.*************.1PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the problem of allocating a set of divisible goods to N agents in an online manner, aiming to maximize the Nash social welfare, a widely studied objective which provides a balance between fairness and efficiency. The goods arrive in a sequence of T periods and the value of each agent for a good is adversarially chosen when the good arrives. We first observe that no online algorithm can achieve a competitive ratio better than the trivial O(N), unless it is given additional information about the agents' values. Then, in line with the emerging area of \"algorithms with predictions\", we consider a setting where for each agent, the online algorithm is only given a prediction of her monopolist utility, i.e., her utility if all goods were given to her alone (corresponding to the sum of her values over the T periods). Our main result is an online algorithm whose competitive ratio is parameterized by the multiplicative errors in these predictions. The algorithm achieves a competitive ratio of O(log N) and O(log T) if the predictions are perfectly accurate. Moreover, the competitive ratio degrades smoothly with the errors in the predictions, and is surprisingly robust: the logarithmic competitive ratio holds even if the predictions are very inaccurate. We complement this positive result by showing that our bounds are essentially tight: no online algorithm, even if provided with perfectly accurate predictions, can achieve a competitive ratio of O(log1–∊ N) or O(log1–∊ T) for any constant ∊ > 0. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.1"}, {"primary_key": "1764373", "vector": [], "sparse_vector": [], "title": "Fast Consensus via the Unconstrained Undecided State Dynamics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Fast Consensus via the Unconstrained Undecided State Dynamics<PERSON><PERSON>gor <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.3417 - 3429Chapter DOI:https://doi.org/10.1137/1.*************.135PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the plurality consensus problem for n agents. Initially, each agent has one of k opinions. Agents choose random interaction partners and revise their state according to a fixed transition function, depending on their own state and the state of the interaction partners. The goal is to reach a configuration in which all agents agree on the same opinion. If there is initially a sufficiently large bias towards some opinions one of them should prevail. In this paper we consider a synchronized variant of the undecided state dynamics where the agents use so-called phase clocks. The phase clocks divide the time in overlapping phases. Each phase consists of a decision and a boosting part. In the decision part, any agent that encounters an agent with a different opinion becomes undecided. In the boosting part, undecided agents adopt the first opinion they encounter. We consider this dynamics both in the sequential population model and the parallel gossip model. In the population model agents interact in randomly chosen pairs, one pair per time step. The runtime is measured in parallel time (number of interactions divided by n). We show that our protocol reaches consensus (w.h.p.) in O(log2 n) parallel time, providing the first polylogarithmic result for k > 2 (w.h.p.) in this model. If there is an initial bias of , then (w.h.p.) that opinion wins. The gossip model assumes parallel rounds. During each round every agent is allowed to communicate with one randomly chosen agent. Here it is known that consensus can be reached fast (in polylogarithmic time) if there is a bias of order towards one opinion [Ghaffari and Parter, PODC'16; Berenbrink et al., ICALP'16]. Without any assumption on the bias, fast consensus has only been shown for k = 2 for the unsynchronized version of the undecided state dynamics [Clementi et al., MFCS'18]. To account for the yet unsolved general case, we show that the synchronized variant of the undecided state dynamics reaches consensus (w.h.p.) in time O(log2 n) for every initial configuration. Again, we guarantee that if there is an initial bias of , then (w.h.p.) that opinion wins. A simple extension of our protocol in the gossip model yields a dynamics that does not depend on n or k, is anonymous, and has (w.h.p.) runtime O(log2 n). This solves an open problem formulated by Becchetti et al. [Distributed Computing, 2017]. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.135"}, {"primary_key": "1764374", "vector": [], "sparse_vector": [], "title": "Learning-Augmented Weighted Paging.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Learning-Augmented Weighted Pa<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>.67 - 89Chapter DOI:https://doi.org/10.1137/1.*************.4PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider a natural semi-online model for weighted paging, where at any time the algorithm is given predictions, possibly with errors, about the next arrival of each page. The model is inspired by <PERSON><PERSON>'s classic optimal offline algorithm for unweighted paging, and extends the recently studied model for learning-augmented paging [45, 50, 52] to the weighted setting. For the case of perfect predictions, we provide an ℓ-competitive deterministic and an O(log ℓ)-competitive randomized algorithm, where ℓ is the number of distinct weight classes. Both these bounds are tight, and imply an O(log W)- and O(log log W)-competitive ratio, respectively, when the page weights lie between 1 and W. Previously, it was not known how to use these predictions in the weighted setting and only bounds of k and O(log k) were known, where k is the cache size. Our results also generalize to the interleaved paging setting and to the case of imperfect predictions, with the competitive ratios degrading smoothly from O(ℓ) and O(log ℓ) to O(k) and O(log k), respectively, as the prediction error increases. Our results are based on several insights on structural properties of Belady's algorithm and the sequence of page arrival predictions, and novel potential functions that incorporate these predictions. For the case of unweighted paging, the results imply a very simple potential function based proof of the optimality of Belady's algorithm, which may be of independent interest. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.4"}, {"primary_key": "1764375", "vector": [], "sparse_vector": [], "title": "Combinatorial Gap Theorem and Reductions between Promise CSPs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Combinatorial Gap Theorem and Reductions between Promise CSPs<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.1204 - 1220Chapter DOI:https://doi.org/10.1137/1.*************.50PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A value of a CSP instance is typically defined as a fraction of constraints that can be simultaneously met. We propose an alternative definition of a value of an instance and show that, for purely combinatorial reasons, a value of an unsolvable instance is bounded away from one; we call this fact a gap theorem. We show that the gap theorem implies NP-hardness of a gap version of the Layered Label Cover Problem. The same result can be derived from the PCP Theorem, but a full, self-contained proof of our reduction is quite short and the result can still provide PCP–free NP–hardness proofs for numerous problems. The simplicity of our reasoning also suggests that weaker versions of Unique-Games-type conjectures, e.g., the d-to-1 conjecture, might be accessible and serve as an intermediate step for proving these conjectures in their full strength. As the second, main application we provide a sufficient condition under which a fixed template Promise Constraint Satisfaction Problem (PCSP) reduces to another PCSP. The correctness of the reduction hinges on the gap theorem, but the reduction itself is very simple. As a consequence, we obtain that every CSP can be canonically reduced to most of the known NP-hard PCSPs, such as the approximate hypergraph coloring problem. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.50"}, {"primary_key": "1764376", "vector": [], "sparse_vector": [], "title": "The complexity of testing all properties of planar graphs, and the role of isomorphism.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)The complexity of testing all properties of planar graphs, and the role of isomorphism<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.1702 - 1714<PERSON>hapter DOI:https://doi.org/10.1137/1.*************.69PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Consider property testing on bounded degree graphs and let ∊ > 0 denote the proximity parameter. A remarkable theorem of <PERSON><PERSON><PERSON><PERSON> (SICOMP 2013) asserts that all properties of planar graphs (more generally hyperfinite) are testable with query complexity only depending on ∊. Recent advances in testing minor-freeness have proven that all additive and monotone properties of planar graphs can be tested in poly(∊–1) queries. Some properties falling outside this class, such as Hamiltonicity, also have a similar complexity for planar graphs. Motivated by these results, we ask: can all properties of planar graphs can be tested in poly(∊–1) queries? Is there a uniform query complexity upper bound for all planar properties, and what is the \"hardest\" such property to test? We discover a surprisingly clean and optimal answer. Any property of bounded degree planar graphs can be tested in exp(O(∊–2)) queries. Moreover, there is a matching lower bound, up to constant factors in the exponent. The natural property of testing isomorphism to a fixed graph requires exp(Ω(∊–2)) queries, thereby showing that (up to polynomial dependencies) isomorphism to an explicit fixed graph is the hardest property of planar graphs. The upper bound is a straightforward adaptation of the Newman-Sohler analysis that tracks dependencies on ∊ more carefully. The main technical contribution is the lower bound construction, which is achieved by a special family of planar graphs that are all mutually far from each other. We can also apply our techniques to get analogous results for bounded treewidth graphs. We prove that all properties of bounded treewidth graphs can be tested in exp(O(∊–1 log ∊–1)) queries. Moreover, testing isomorphism to a fixed forest requires exp(Ω(∊–1)) queries. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.69"}, {"primary_key": "1764377", "vector": [], "sparse_vector": [], "title": "Sensitivity Oracles for All-Pairs Mincuts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Sensitivity Oracles for All-Pairs MincutsSurender Baswana and Abhyuday PandeySurender Baswana and Abhyuday Pandeypp.581 - 609Chapter DOI:https://doi.org/10.1137/1.*************.27PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Let G = (V, E) be an undirected unweighted graph on n vertices and m edges. We address the problem of sensitivity oracle for all-pairs mincuts in G defined as follows. Build a compact data structure that, on receiving any pair of vertices s,t ∊ V and failure (or insertion) of any edge as query, can efficiently report the mincut between s and t after the failure (or the insertion). To the best of our knowledge, there exists no data structure for this problem which takes o(mn) space and a non-trivial query time. We present the following results. Our first data structure occupies space and guarantees query time to report the value of resulting (s, t)-mincut upon failure (or insertion) of any edge. Moreover, the set of vertices defining a resulting (s, t)-mincut after the update can be reported in time which is worst-case optimal. Our second data structure optimizes space at the expense of increased query time. It takes space–which is also the space taken by G. The query time is where cs,t is the value of the mincut between s and t in G. This query time is faster by a factor of compared to the best known deterministic algorithm [21, 26, 28] to compute a (s,t)-mincut from scratch. If we are only interested in knowing if failure (or insertion) of an edge changes the value of (s, t)-mincut for any s, t ∊ V, we can distribute our space data structure evenly among n vertices. For any failed (or inserted) edge we only require the data structures stored at its endpoints to determine if the value of (s, t)-mincut has changed for any s, t ∊ V. Moreover, using these data structures we can also output efficiently a compact encoding of all pairs of vertices whose mincut value is changed after the failure (or the insertion) of an edge. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.27"}, {"primary_key": "1764378", "vector": [], "sparse_vector": [], "title": "Stochastic Vertex Cover with Few Queries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Stochastic Vertex Cover with Few Queries<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1808 - 1846Chapter DOI:https://doi.org/10.1137/1.*************.73PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the minimum vertex cover problem in the following stochastic setting. Let G be an arbitrary given graph, p ∊ (0, 1] a parameter of the problem, and let Gp be a random subgraph that includes each edge of G independently with probability p. We are unaware of the realization Gp, but can learn if an edge e exists in Gp by querying it. The goal is to find an approximate minimum vertex cover (MVC) of Gp by querying few edges of G non-adaptively. This stochastic setting has been studied extensively for various problems such as minimum spanning trees, matroids, shortest paths, and matchings. To our knowledge, however, no non-trivial bound was known for MVC prior to our work. In this work, we present a: (2 + ∊)-approximation for general graphs which queries edges per vertex, and a 1.367-approximation for bipartite graphs which queries poly(1/p) edges per vertex. Additionally, we show that at the expense of a triple-exponential dependence on p–1 in the number of queries, the approximation ratio can be improved down to (1 + ∊) for bipartite graphs. Our techniques also lead to improved bounds for bipartite stochastic matching. We obtain a 0.731-approximation with nearly-linear in 1/p per-vertex queries. This is the first result to break the prevalent (2/3∼ 0.66)-approximation barrier in the poly(1/p) query regime, improving algorithms of [Behnezhad et al., SODA'19] and [Assadi and Bernstein, SOSA'19]. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.73"}, {"primary_key": "1764379", "vector": [], "sparse_vector": [], "title": "New Trade-Offs for Fully Dynamic Matching via Hierarchical EDCS.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)New Trade-Offs for Fully Dynamic Matching via Hierarchical EDC<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.3529 - 3566Chapter DOI:https://doi.org/10.1137/1.*************.140PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the maximum matching problem in fully dynamic graphs: a graph is undergoing both edge insertions and deletions, and the goal is to efficiently maintain a large matching after each edge update. This problem has received considerable attention in recent years. The known algorithms naturally exhibit a trade-off between the quality of the matching maintained (i.e., the approximation ratio) and the time needed per update. While several interesting results have been obtained, the optimal behavior of this trade-off remains largely unclear. Our main contribution is a new approach to designing fully dynamic approximate matching algorithms that in a unified manner not only (essentially) recovers all previously known trade-offs that were achieved via very different techniques, but reveals some new ones as well. Specifically, we introduce a generalization of the edge-degree constrained subgraph (EDCS) of <PERSON> and <PERSON> (2015) that we call the hierarchical EDCS (HEDCS). We also present a randomized algorithm for efficiently maintaining an HEDCS. In an m-edge graph with maximum degree Δ, for any integer k ≥ 0 that is essentially the number of levels of the hierarchy in HEDCS, our algorithm takes Õ(min{Δ1/(k + 1), m1/(2k+2)}) worst-case update-time and maintains an (almost) α(k)-approximate matching where we show: These bounds recover all previous trade-offs known for dynamic matching in the literature up to logarithmic factors in the update-time. α(2) > .612 for bipartite graphs, and α(2) > .609 for general graphs. Note that these approximations are obtained in Õ(min{Δ1/3, m1/6}) update-time. α(3) > .563 for bipartite graphs, and α(3) > .532 for general graphs. Note that these approximations are obtained in Õ(min{Δ1/4, m1/8}) update-time. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.140"}, {"primary_key": "1764380", "vector": [], "sparse_vector": [], "title": "Deterministic enumeration of all minimum k-cut-sets in hypergraphs for fixed k.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Deterministic enumeration of all minimum k-cut-sets in hypergraphs for fixed k<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>.2208 - 2228Chapter DOI:https://doi.org/10.1137/1.*************.88PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the problem of deterministically enumerating all minimum k-cut-sets in a given hypergraph for any fixed k. The input here is a hypergraph G = (V, E) with non-negative hyperedge costs. A subset F ⊆ E of hyperedges is a k-cut-set if the number of connected components in G–F is at least k and it is a minimum k-cut-set if it has the least cost among all k-cut-sets. For fixed k, we call the problem of finding a minimum k-cut-set as Hypergraph-k-Cut and the problem of enumerating all minimum k-cut-sets as Enum-Hypergraph-k-Cut. The special cases of Hypergraph-k-Cut and Enum-Hypergraph-k-Cut restricted to graph inputs are well-known to be solvable in (randomized as well as deterministic) polynomial time [17,25,28,39]. In contrast, it is only recently that polynomial-time algorithms for Hypergraph-k-Cut were developed [2,3,12]. The randomized polynomial-time algorithm for Hypergraph-k-Cut that was designed in 2018 [3] showed that the number of minimum k-cut-sets in a hypergraph is O(n2k–2), where n is the number of vertices in the input hypergraph, and that they can all be enumerated in randomized polynomial time, thus resolving Enum-Hypergraph-k-Cut in randomized polynomial time. A deterministic polynomial-time algorithm for Hypergraph-k-Cut was subsequently designed in 2020 [2], but it is not guaranteed to enumerate all minimum k-cut-sets. In this work, we give the first deterministic polynomial-time algorithm to solve Enum-Hypergraph-k-Cut (this is non-trivial even for k = 2). Our algorithm is based on new structural results that allow for efficient recovery of all minimum k-cut-sets by solving minimum (S, T)-terminal cuts. Our techniques give new structural insights even for enumerating all minimum cut-sets (i.e., minimum 2-cut-sets) in a given hypergraph. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.88"}, {"primary_key": "1764381", "vector": [], "sparse_vector": [], "title": "Splay trees on trees.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Splay trees on treesBenjamin <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>.1875 - 1900Chapter DOI:https://doi.org/10.1137/1.*************.75PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Search trees on trees (STTs) are a far-reaching generalization of binary search trees (BSTs), allowing the efficient exploration of tree-structured domains. (BSTs are the special case in which the underlying domain is a path.) Trees on trees have been extensively studied under various guises in computer science and discrete mathematics. Recently <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON> (SODA 2020) considered adaptive STTs and observed that, apart from notable exceptions, the machinery developed for BSTs in the past decades does not readily transfer to STTs. In particular, they asked whether the optimal STT can be efficiently computed or approximated (by analogy to <PERSON><PERSON><PERSON>'s algorithm for optimal BSTs), and whether natural self-adjusting BSTs such as Splay trees (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, 1983) can be extended to this more general setting. We answer both questions affirmatively. First, we show that a -approximation of an optimal size-n STT for a given search distribution can be computed in time (n2t + 1) for all integers t ≥ 1. Second, we identify a broad family of STTs with linear rotation-distance, allowing the generalization of Splay trees to the STT setting. We show that our generalized Splay satisfies a static optimality theorem, asymptotically matching the cost of the optimal STT in an online fashion, i.e. without knowledge of the search distribution. Our results suggest an extension of the dynamic optimality conjecture for Splay trees to the broader setting of trees on trees. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.75"}, {"primary_key": "1764382", "vector": [], "sparse_vector": [], "title": "Better Sum Estimation via Weighted Sampling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Better Sum Estimation via Weighted Sampling<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.2303 - 2338Chapter DOI:https://doi.org/10.1137/1.*************.93PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given a large set U where each item a ∊ U has weight w(a), we want to estimate the total weight W = Σa∊U w(a) to within factor of 1 ± ∊ with some constant probability > 1/2. Since n = |U| is large, we want to do this without looking at the entire set U. In the traditional setting in which we are allowed to sample elements from U uniformly, sampling Ω(n) items is necessary to provide any non-trivial guarantee on the estimate. Therefore, we investigate this problem in different settings: in the proportional setting we can sample items with probabilities proportional to their weights, and in the hybrid setting we can sample both proportionally and uniformly. These settings have applications, for example, in sublinear-time algorithms and distribution testing. Sum estimation in the proportional and hybrid setting has been considered before by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> [ICALP, 2007]. In their paper, they give both upper and lower bounds in terms of n. Their bounds are near-matching in terms of n, but not in terms of ∊. In this paper, we improve both their upper and lower bounds. Our bounds are matching up to constant factors in both settings, in terms of both n and ∊. No lower bounds with dependency on ∊ were known previously. In the proportional setting, we improve their algorithm to . In the hybrid setting, we improve to . Our algorithms are also significantly simpler and do not have large constant factors. We then investigate the previously unexplored scenario in which n is not known to the algorithm. In this case, we obtain a algorithm for the proportional setting, and a algorithm for the hybrid setting. This means that in the proportional setting, we may remove the need for advice without greatly increasing the complexity of the problem, while there is a major difference in the hybrid setting. We prove that this difference in the hybrid setting is necessary, by showing a matching lower bound. Our algorithms have applications in the area of sublinear-time graph algorithms. Consider a large graph G = (V, E) and the task of (1 ± ∊)-approximating |E|. We consider the (standard) settings where we can sample uniformly from E or from both E and V. This relates to sum estimation as follows: we set U = V and the weights to be equal to the degrees. Uniform sampling then corresponds to sampling vertices uniformly. Proportional sampling can be simulated by taking a random edge and picking one of its endpoints at random. If we can only sample uniformly from E, then our results immediately give a algorithm. When we may sample both from E and V, our results imply an algorithm with complexity . Surprisingly, one of our subroutines provides an (1 ± ∊)-approximation of |E| using Õ(d/∊2) expected samples, where d is the average degree, under the mild assumption that at least a constant fraction of vertices are non-isolated. This subroutine works in the setting where we can sample uniformly from both V and E. We find this remarkable since it is O(1/∊2) for sparse graphs. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.93"}, {"primary_key": "1764383", "vector": [], "sparse_vector": [], "title": "How many Clusters? - An algorithmic answer.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)How many Clusters? - An algorithmic answer<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.2607 - 2640Chapter DOI:https://doi.org/10.1137/1.*************.102PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Many algorithms for clustering high dimensional data assume that k, the number of clusters, is given. However, there has been little work on provably inferring k from the data. This paper gives polynomial time algorithms for finding k from the data assuming it satisfies certain natural deterministic conditions. Informally, we assume that there is a Ground Truth (GT) clustering of the data with the following properties: (i) Each cluster has a certain minimum size, (ii) the inter-mean separation of any two distinct clusters in the GT is large enough (although still weaker than what is typically assumed in the literature), and (iii) we define a novel \"no large sub-cluster\" (NLSC) property that characterizes the notion of a cluster by stipulating that there be no subsets of low \"directional variance\". NLSC is indeed satisfied by large class of distributions including log-concave densities. The first major contribution is an algorithm for finding k where m, the minimum GT cluster size, is assumed to be known. This algorithm uses a novel rounding procedure which finds subsets of size m with low Directional Variance by rounding a SDP relaxation using Cheeger's inequality and it is shown that k is precisely the number of such sets whose means are well-separated. The harder problem of finding k when m not given is addressed by running the previous algorithm for each value of m to find candidate values of k and the corresponding k-clustering. The second major contribution of this paper is a test which certifies the correct candidate thereby yielding a polynomial time algorithm which finds k. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.102"}, {"primary_key": "1764384", "vector": [], "sparse_vector": [], "title": "Distributed Zero-Knowledge Proofs Over Networks.", "authors": ["Aviv Bick", "Gillat Kol", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Distributed Zero-Knowledge Proofs Over NetworksAviv Bick, <PERSON><PERSON>, and Rotem OshmanAviv Bick, Gill<PERSON> Kol, and Rotem Oshmanpp.2426 - 2458Chapter DOI:https://doi.org/10.1137/1.*************.97PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Zero knowledge proofs are one of the most influential concepts in theoretical computer science. In the seminal definition due to <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> dating back to the 1980s, a computationally-bounded verifier interacts with a powerful but untrusted prover, with the goal of becoming convinced that the input is in some language. In addition to the usual requirements of completeness and soundness, in a zero knowledge proof, we protect the prover's knowledge: assuming the prover is honest, anything that the verifier can deduce after interacting with the prover, it could have deduced by itself. Zero knowledge proofs have found many applications within theoretical computer science and beyond, e.g., in cryptography, client-cloud computing, blockchains and cryptocurrencies, electronic voting and auctions, and in the financial industry. We define and study the notion of distributed zero knowledge proofs, reconciling the computational notion of zero-knowledge with the communication-based paradigm of distributed graph algorithms. In our setting, a network of verifiers interacts with an untrusted prover to decide some distributed language. As is usually the case in distributed graph algorithms, we assume that the verifiers have local views of the network and each only knows its neighbors. The prover, on the other hand, is assumed to know the entire network graph, as well as any input that the verifier may possess. As in the computational centralized setting, the protocol we design should protect this knowledge. In particular, due to the dual role of the underlying graph in distributed graph algorithms, serving as both the communication topology and the input to the problem, our protocol must protect the graph itself. We construct communication-efficient distributed zero knowledge proofs for two central problems: the 3-coloring problem, one of the poster children of computational zero-knowledge, and for the spanning-tree verification problem, a fundamental building block for designing graph algorithms. We also give a general scheme for converting proof labeling-schemes to distributed zero-knowledge protocols with related parameters. Our protocols combine ideas from computational complexity, distributed computing, and cryptography. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.97"}, {"primary_key": "1764385", "vector": [], "sparse_vector": [], "title": "Optimal angle bounds for Steiner triangulations of polygons.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Optimal angle bounds for Steiner triangulations of polygonsChristopher J. BishopChristopher J. Bishoppp.3127 - 3143Chapter DOI:https://doi.org/10.1137/1.*************.121PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract For any simple polygon P we compute the optimal upper and lower angle bounds for triangulating P with Steiner points, and show that these bounds can be attained (except in one special case). The sharp angle bounds for an N-gon are computable in time O(N), even though the number of triangles needed to attain these bounds has no bound in terms of N alone. In general, the sharp upper and lower bounds cannot both be attained by a single triangulation, although this does happen in some cases. For example, we show that any polygon with minimal interior angle θ has a triangulation with all angles in the interval I = [θ, 90°–min(36°, θ)/2], and for θ ≤ 36° both bounds are best possible. Surprisingly, we prove the optimal angle bounds for polygonal triangulations are the same as for triangular dissections. The proof of this verifies, in a stronger form, a 1984 conjecture of Gerver. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.121"}, {"primary_key": "1764386", "vector": [], "sparse_vector": [], "title": "Computational Topology in a Collapsing Universe: Laplacians, Homology, Cohomology.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Computational Topology in a Collapsing Universe: Laplacians, Homology, Cohomology<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON>, and <PERSON>.226 - 251Chapter DOI:https://doi.org/10.1137/1.*************.12PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider a variety of topology problems on a d-dimensional simplicial complex K given that K ∪ X for X a collapsible simplicial complex embedded in ℝd+1 with known collapsing sequence. Our first result is a solver for the linear system L1x = b, where L1 is the 1-Laplacian of a simplicial complex K with dimH1(K) = 0 and K ∪ X for X a collapsible simplicial complex embedded in ℝ3 with a known collapsing sequence. Our algorithm runs in O(n log2 (nκ/∊)) time, where n is the total number of vertices, edges, and triangles in X, κ is the largest condition number of the two parts of the Laplacian, and ∊ quantifies the approximation quality. This result is a generalization of <PERSON> et al. [SOD<PERSON> 2014]. The new technical piece of our Laplacian solver, in addition to the machinery described by <PERSON> et al., is an algorithm to compute a bounding chain of a 1-cycle within k. In addition, we describe faster algorithms for testing null-homology of (d–1)-cycles and null-cohomology of d-cocycles. Our algorithm runs in O(nd) time, where nd is the number of d-simplices in X. Finally, we describe an algorithm to compute a (d–1)-cohomology basis from a given (d–1)-homology basis for a d-simplicial complex K in O(βd–1nd) time; βd–1 is the rank of the (d–1)st homology group of k. In particular, we can obtain a cohomology basis for subcomplexes of a collapsible complex X embedded in ℝ3 in O(nd log nd + βd–1 n) time using a homology basis computed by the algorithm of Dey [SODA 2019]. For all of the problems above, if K ∪ ℝ3 and the collapsible supercomplex X is not provided, we can expand K into a convex ball of possibly quadratic complexity, which is known to be collapsible, resulting in nearly quadratic time algorithms. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.12"}, {"primary_key": "1764387", "vector": [], "sparse_vector": [], "title": "On Mixing of Markov Chains: Coupling, Spectral Independence, and Entropy Factorization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)On Mixing of Markov Chains: Coupling, Spectral Independence, and Entropy Factorization<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>.3670 - 3692Chapter DOI:https://doi.org/10.1137/1.*************.145PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract For general spin systems, we prove that a contractive coupling for an arbitrary local Markov chain implies optimal bounds on the mixing time and the modified log-Sobolev constant for a large class of Markov chains including the Glauber dynamics, arbitrary heat-bath block dynamics, and the Swendsen-Wang dynamics. This reveals a novel connection between probabilistic techniques for bounding the convergence to stationarity and analytic tools for analyzing the decay of relative entropy. As a corollary of our general results, we obtain O(n log n) mixing time and Ω(1/n) modified log-Sobolev constant of the Glauber dynamics for sampling random q-colorings of an n-vertex graph with constant maximum degree Δ when q > (11/6–∊0)Δ for some fixed ∊0 > 0. We also obtain O(log n) mixing time and Ω(1) modified log-Sobolev constant of the Swendsen-Wang dynamics for the ferromagnetic Ising model on an n-vertex graph of constant maximum degree when the parameters of the system lie in the tree uniqueness region. At the heart of our results are new techniques for establishing spectral independence of the spin system and block factorization of the relative entropy. On one hand we prove that a contractive coupling of any local Markov chain implies spectral independence of the Gibbs distribution. On the other hand we show that spectral independence implies factorization of entropy for arbitrary blocks, establishing optimal bounds on the modified log-Sobolev constant of the corresponding block dynamics. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.145"}, {"primary_key": "1764388", "vector": [], "sparse_vector": [], "title": "Partially Optimal Edge Fault-Tolerant Spanners.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Partially Optimal Edge Fault-Tolerant Spanner<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.3272 - 3286Chapter DOI:https://doi.org/10.1137/1.*************.129PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Recent work has established that, for every positive integer k, every n-node graph has a (2k–1)-spanner with O(f1–1/k n1+1/k) edges that is resilient to f edge or vertex faults. For vertex faults, this bound is tight. However, the case of edge faults is not as well understood: the best known lower bound for general k is . Our main result is to nearly close this gap with an improved upper bound, thus separating the cases of edge and vertex faults. For odd k, our new upper bound is , which is tight up to hidden poly(k) factors. For even k, our new upper bound is Ok(f1/2 n1 + 1/k + fn), which leaves a gap of poly(k)f1/(2k). Our proof is an analysis of the fault-tolerant greedy algorithm, which requires exponential time, but we also show that there is a polynomial-time algorithm which creates edge fault tolerant spanners that are larger only by factors of k. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.129"}, {"primary_key": "1764389", "vector": [], "sparse_vector": [], "title": "Twin-width VI: the lens of contraction sequences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Twin-width VI: the lens of contraction sequences<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>.1036 - 1056Chapter DOI:https://doi.org/10.1137/1.*************.45PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A contraction sequence of a graph consists of iteratively merging two of its vertices until only one vertex remains. The recently introduced twin-width graph invariant is based on contraction sequences. More precisely, if one puts error edges, henceforth red edges, between two vertices representing non-homogeneous subsets, the twin-width is the minimum integer d such that a contraction sequence exists that keeps red degree at most d. By changing the condition imposed on the trigraphs (i.e., graphs with some edges being red) and possibly slightly tweaking the notion of contractions, we show how to characterize the well-established bounded rank-width, tree-width, linear rank-width, path-width –usually defined in the framework of branch-decompositions–, and proper minor-closed classes by means of contraction sequences. Contraction sequences hold a crucial advantage over branch-decompositions: While one can scale down contraction sequences to capture classical width notions, the more general bounded twin-width goes beyond their scope, as it contains planar graphs in particular, a class with unbounded rank-width. As an application we give a transparent alternative proof of the celebrated Courcelle's theorem (actually of its generalization by Courcelle, Makowsky, and Rotics), that MSO2 (resp. MSO1) model checking on graphs with bounded tree-width (resp. bounded rank-width) is fixed-parameter tractable in the size of the input sentence. We are hopeful that our characterizations can help in other contexts. We then explore new avenues along the general theme of contraction sequences both in order to refine the landscape between bounded tree-width and bounded twin-width (via spanning twin-width) and to capture more general classes than bounded twin-width. To this end, we define an oriented version of twin-width, where appearing red edges are oriented away from the newly contracted vertex, and the mere red out-degree should remain bounded. Surprisingly, classes of bounded oriented twin-width coincide with those of bounded twin-width. This greatly simplifies the task of showing that a class has bounded twin-width. As an example, using a lemma by Norine, Seymour, Thomas, and Wollan, we give a 5-line proof that Kt-minor free graphs have bounded twin-width. Without oriented twin-width, this fact was shown by a somewhat intricate 4-page proof in the first paper of the series. Finally we explore the concept of partial contraction sequences, instead of terminating on a single-vertex graph, the sequence ends when reaching a particular target class. We show that FO model checking (resp. ∃FO model checking) is fixed-parameter tractable on classes with partial contraction sequences to a class of bounded degree (resp. bounded expansion), provided such a sequence is given. Efficiently finding such partial sequences could turn out simpler than finding a (complete) sequence. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.45"}, {"primary_key": "1764390", "vector": [], "sparse_vector": [], "title": "Polynomial Time Algorithms to Find an Approximate Competitive Equilibrium for Chores.", "authors": ["Shan<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Polynomial Time Algorithms to Find an Approximate Competitive Equilibrium for ChoresS<PERSON>t <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.2285 - 2302Chapter DOI:https://doi.org/10.1137/1.*************.92PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Competitive equilibrium with equal income (CEEI) is considered one of the best mechanisms to allocate a set of items among agents fairly and efficiently. In this paper, we study the computation of CEEI when items are chores that are disliked (negatively valued) by agents, under 1-homogeneous and concave utility functions which includes linear functions as a subcase. It is well-known that, even with linear utilities, the set of CEEI may be non-convex and disconnected, and the problem is PPAD-hard in the more general exchange model. In contrast to these negative results, we design a FPTAS: A polynomial-time algorithm to compute ∊-approximate CEEI where the running-time depends polynomially on . Our algorithm relies on the recent characterization due to <PERSON> et al. (2017) of the CEEI set as exactly the KKT points of a non-convex minimization problem that have all coordinates non-zero. Due to this non-zero constraint, naïve gradient-based methods fail to find the desired local minima as they are attracted towards zero. We develop an exterior-point method that alternates between guessing non-zero KKT points and maximizing the objective along supporting hyperplanes at these points. We show that this procedure must converge quickly to an approximate KKT point which then can be mapped to an approximate CEEI; this exterior point method may be of independent interest. When utility functions are linear, we give explicit procedures for finding the exact iterates, and as a result show that a stronger form of approximate CEEI can be found in polynomial time. Finally, we note that our algorithm extends to the setting of un-equal incomes (CE), and to mixed manna with linear utilities where each agent may like (positively value) some items and dislike (negatively value) others. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.92"}, {"primary_key": "1764391", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Approximate Near Neighbor Searching for Time Series under the Fréchet Distance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Tight Bounds for Approximate Near Neighbor Searching for Time Series under the Fréchet <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.517 - 550Chapter DOI:https://doi.org/10.1137/1.*************.25PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the c-approximate near neighbor problem under the continuous Fréchet distance: Given a set of n polygonal curves with m vertices, a radius δ > 0, and a parameter k ≤ m, we want to preprocess the curves into a data structure that, given a query curve q with k vertices, either returns an input curve with Fréchet distance at most c · δ to q, or returns that there exists no input curve with Fréchet distance at most δ to q. We focus on the case where the input and the queries are one-dimensional polygonal curves—also called time series—and we give a comprehensive analysis for this case. We obtain new upper bounds that provide different tradeoffs between approximation factor, preprocessing time, and query time. Our data structures improve upon the state of the art in several ways. We show that for any 0 < ∊ ≤ 1 an approximation factor of (1 + ∊) can be achieved within the same asymptotic time bounds as the previously best result for (2 + ∊). Moreover, we show that an approximation factor of (2 + ∊) can be obtained by using preprocessing time and space O(nm), which is linear in the input size, and query time in , where the previously best result used preprocessing time in and query time in O(1)k. We complement our upper bounds with matching conditional lower bounds based on the Orthogonal Vectors Hypothesis. Interestingly, some of our lower bounds already hold for any super-constant value of k. This is achieved by proving hardness of a one-sided sparse version of the Orthogonal Vectors problem as an intermediate problem, which we believe to be of independent interest. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.25"}, {"primary_key": "1764392", "vector": [], "sparse_vector": [], "title": "Deterministic and Las Vegas Algorithms for Sparse Nonnegative Convolution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Deterministic and Las Vegas Algorithms for Sparse Nonnegative Convolution<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and Vasileios Nakospp.3069 - 3090Chapter DOI:https://doi.org/10.1137/1.*************.119PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Computing the convolution A∗B of two length-n integer vectors A, B is a core problem in several disciplines. It frequently comes up as a subroutine in various problem domains, e.g. in algorithms for Knapsack, k-SUM, All-Pairs Shortest Paths, and string pattern matching problems. For these applications it typically suffices to compute convolutions of nonnegative vectors. This problem can be classically solved in time O(n log n) using the Fast Fourier Transform. However, in many applications the involved vectors are sparse and hence one could hope for output-sensitive algorithms to compute nonnegative convolutions. This question was raised by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and solved by <PERSON> and <PERSON> (STOC '02) by a randomized algorithm running in near-linear time in the (unknown) output-size t and recently improved by <PERSON><PERSON>, <PERSON> and <PERSON><PERSON> (STOC '21) in O(k log k) Monte <PERSON> time. <PERSON> and <PERSON>wenstein (STOC '15) presented a deterministic algorithm with a overhead in running time and the additional assumption that a small superset of the output is given; this assumption was later removed by Bringmann and Nakos (ICALP '21). In this paper we present the first deterministic near-linear-time algorithm for computing sparse nonnegative convolutions. This immediately gives improved deterministic algorithms for the state-of-the-art of output-sensitive Subset Sum, block-mass pattern matching, N-fold Boolean convolution, and others, matching up to log-factors the fastest known randomized algorithms for these problems. Our algorithm is a blend of algebraic and combinatorial ideas and techniques. Additionally, we provide two fast Las Vegas algorithms for computing sparse nonnegative convolutions. In particular, we present a simple O(t log2 t) time algorithm, which is an accessible alternative to Cole and Hariharan's algorithm. Subsequently, we further refine this new algorithm to run in Las Vegas time O(t log t · log log t), which matches the running time of the dense case apart from the log log t factor. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.119"}, {"primary_key": "1764393", "vector": [], "sparse_vector": [], "title": "Sparsifying, Shrinking and Splicing for Minimum Path Cover in Parameterized Linear Time.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Sparsifying, Shrinking and Splicing for Minimum Path Cover in Parameterized Linear TimeM<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>pp.359 - 376Chapter DOI:https://doi.org/10.1137/1.*************.18PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A minimum path cover (MPC) of a directed acyclic graph (DAG) G = (V, E) is a minimum-size set of paths that together cover all the vertices of the DAG. Computing an MPC is a basic polynomial problem, dating back to <PERSON><PERSON><PERSON>'s and <PERSON><PERSON><PERSON>'s results in the 1950s. Since the size k of an MPC (also known as the width) can be small in practical applications, research has also studied algorithms whose running time is parameterized on k. We obtain two new MPC parameterized algorithms for DAGs running in time O(k2|V| log |V| + |E|) and O(k3|V| + |E|). We also obtain a parallel algorithm running in O(k2|V| + |E|) parallel steps and using O(log |V|) processors (in the PRAM model). Our latter two algorithms are the first solving the problem in parameterized linear time. Finally, we show that we can transform (in O(k2|V|) time) a given MPC into another MPC that uses less than 2|V| distinct edges, which we prove to be asymptotically tight. As such, we also obtain edge sparsification algorithms preserving the width of the DAG with the same running time as our MPC algorithms. At the core of all our algorithms we interleave the usage of three techniques: transitive sparsification, shrinking of a path cover, and the splicing of a set of paths along a given path. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.18"}, {"primary_key": "1764394", "vector": [], "sparse_vector": [], "title": "Single-Sample Prophet Inequalities via Greedy-Ordered Selection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Single-Sample Prophet Inequalities via Greedy-Ordered Selection<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>pp.1298 - 1325Chapter DOI:https://doi.org/10.1137/1.*************.54PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study single-sample prophet inequalities (SSPIs), i.e., prophet inequalities where only a single sample from each prior distribution is available. Besides a direct, and optimal, SSPI for the basic single choice problem [<PERSON><PERSON> et al., 2020], most existing SSPI results were obtained via an elegant, but inherently lossy reduction to order-oblivious secretary (OOS) policies [<PERSON><PERSON> et al., 2014]. Motivated by this discrepancy, we develop an intuitive and versatile greedy-based technique that yields SSPIs directly rather than through the reduction to OOSs. Our results can be seen as generalizing and unifying a number of existing results in the area of prophet and secretary problems. Our algorithms significantly improve on the competitive guarantees for a number of interesting scenarios (including general matching with edge arrivals, bipartite matching with vertex arrivals, and certain matroids), and capture new settings (such as budget additive combinatorial auctions). Complementing our algorithmic results, we also consider mechanism design variants. Finally, we analyze the power and limitations of different SSPI approaches by providing a partial converse to the reduction from SSPI to OOS given by Azar et al. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.54"}, {"primary_key": "1764395", "vector": [], "sparse_vector": [], "title": "Efficient generation of elimination trees and graph associahedra.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Efficient generation of elimination trees and graph associahedra<PERSON><PERSON> <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.2128 - 2140Chapter DOI:https://doi.org/10.1137/1.*************.84PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract An elimination tree for a connected graph G is a rooted tree on the vertices of G obtained by choosing a root x and recursing on the connected components of G–x to produce the subtrees of x. Elimination trees appear in many guises in computer science and discrete mathematics, and they encode many interesting combinatorial objects, such as bitstrings, permutations and binary trees. We apply the recent Hartung-Hoang-Mütze-<PERSON> combinatorial generation framework to elimination trees, and prove that all elimination trees for a chordal graph G can be generated by tree rotations using a simple greedy algorithm. This yields a short proof for the existence of Hamilton paths on graph associahedra of chordal graphs. Graph associahedra are a general class of high-dimensional polytopes introduced by <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, whose vertices correspond to elimination trees and whose edges correspond to tree rotations. As special cases of our results, we recover several classical Gray codes for bitstrings, permutations and binary trees, and we obtain a new Gray code for partial permutations. Our algorithm for generating all elimination trees for a chordal graph G can be implemented in time (m + n) per generated elimination tree, where m and n are the number of edges and vertices of G, respectively. If G is a tree, we improve this to a loopless algorithm running in time (1) per generated elimination tree. We also prove that our algorithm produces a Hamilton cycle on the graph associahedron of G, rather than just Hamilton path, if the graph G is chordal and 2-connected. Moreover, our algorithm characterizes chordality, i.e., it computes a Hamilton path on the graph associahedron of G if and only if G is chordal. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.84"}, {"primary_key": "1764396", "vector": [], "sparse_vector": [], "title": "Augmenting Edge Connectivity via Isolating Cuts.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Augmenting Edge Connectivity via Isolating CutsRuox<PERSON> Cen, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> PanigrahiRuoxu Ce<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> Panigrahipp.3237 - 3252Chapter DOI:https://doi.org/10.1137/1.*************.127PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We give an algorithm for augmenting the edge connectivity of an undirected graph by using the isolating cuts framework (<PERSON> and <PERSON><PERSON>, FOCS '20). Our algorithm uses poly-logarithmic calls to any max-flow algorithm, which yields a running time of Õ(m + n3/2) and improves on the previous best time of Õ(n2) (<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, SODA '98) for this problem. We also obtain an identical improvement in the running time of the closely related edge splitting off problem in undirected graphs. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.127"}, {"primary_key": "1764397", "vector": [], "sparse_vector": [], "title": "Dynamic Geometric Set Cover, Revisited.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Dynamic Geometric Set Cover, Revisited<PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.3496 - 3528<PERSON><PERSON>pter DOI:https://doi.org/10.1137/1.*************.139PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Geometric set cover is a classical problem in computational geometry, which has been extensively studied in the past. In the dynamic version of the problem, points and ranges may be inserted and deleted, and our goal is to efficiently maintain a set cover solution (satisfying certain quality requirement) for the dynamic problem instance. In this paper, we give a plethora of new dynamic geometric set cover data structures in 1D and 2D, which significantly improve and extend the previous results. Our results include the following: The first data structure for (1 + ∊)-approximate dynamic interval set cover with polylogarithmic amortized update time. Specifically, we achieve an update time of O(log3 n/∊), improving the O(nδ/∊) bound of <PERSON><PERSON><PERSON> et al. [SoCG'20], where δ > 0 denotes an arbitrarily small constant. A data structure for O(1)-approximate dynamic unit-square set cover with amortized update time, substantially improving the O(n1/2+δ) update time of Agarwal et al. [SoCG'20]. A data structure for O(1)-approximate dynamic square set cover with O(n1/2+δ) randomized amortized update time, improving the O(n2/3+δ) update time of Chan and He [SoCG'21]. A data structure for O(1)-approximate dynamic 2D halfplane set cover with O(n17/23+δ) randomized amortized update time. The previous solution for halfplane set cover by Chan and He [SoCG'21] is slower and can only report the size of the approximate solution. The first sublinear results for the weighted version of dynamic geometric set cover. Specifically, we give a data structure for (3 + o(1))-approximate dynamic weighted interval set cover with amortized update time and a data structure for O(1)-approximate dynamic weighted unit-square set cover with O(nδ) amortized update time. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.139"}, {"primary_key": "1764398", "vector": [], "sparse_vector": [], "title": "Hopcroft&apos;s <PERSON>, Log-Star Shaving, 2D Fractional Cascading, and Decision Trees.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)<PERSON><PERSON>'s Problem, Log-Star Shaving, 2D Fractional Cascading, and Decision TreesTimothy <PERSON><PERSON> and <PERSON> and <PERSON>.190 - 210Chapter DOI:https://doi.org/10.1137/1.*************.10PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We revisit <PERSON><PERSON>'s problem and related fundamental problems about geometric range searching. Given n points and n lines in the plane, we show how to count the number of point-line incidence pairs or the number of point-above-line pairs in O(n4/3) time, which matches the conjectured lower bound and improves the best previous time bound of n4/32O(log∗ n) obtained almost 30 years ago by <PERSON><PERSON><PERSON><PERSON>. We describe two interesting and different ways to achieve the result: the first is randomized and uses a new 2D version of fractional cascading for arrangements of lines; the second is deterministic and uses decision trees in a manner inspired by the sorting technique of <PERSON><PERSON> (1976). The second approach extends to any constant dimension. Many consequences follow from these new ideas: for example, we obtain an O(n4/3)-time algorithm for line segment intersection counting in the plane, O(n4/3)-time randomized algorithms for bichromatic closest pair and Euclidean minimum spanning tree in three or four dimensions, and a randomized data structure for halfplane range counting in the plane with O(n4/3) preprocessing time and space and O(n1/3) query time. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.10"}, {"primary_key": "1764399", "vector": [], "sparse_vector": [], "title": "Near-Optimal Explainable k-Means for All Dimensions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Near-Optimal Explainable k-Means for All DimensionsM<PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.2580 - 2606Chapter DOI:https://doi.org/10.1137/1.*************.101PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Many clustering algorithms are guided by certain cost functions such as the widely-used k-means cost. These algorithms divide data points into clusters with often complicated boundaries, creating difficulties in explaining the clustering decision. In a recent work, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> (ICML 2020) introduced explainable clustering, where the cluster boundaries are axis-parallel hyperplanes and the clustering is obtained by applying a decision tree to the data. The central question here is: how much does the explainability constraint increase the value of the cost function? Given d-dimensional data points, we show an efficient algorithm that finds an explainable clustering whose k-means cost is at most k1–2/d poly(d log k) times the minimum cost achievable by a clustering without the explainability constraint, assuming k, d ≥ 2. Taking the minimum of this bound and the k polylog(k) bound in independent work by <PERSON><PERSON><PERSON><PERSON><PERSON> (ICML 2021), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>sson (2021), or Esfandiari-Mirrokni-<PERSON>an (2021), we get an improved bound of k1–2/d polylog(k), which we show is optimal for every choice of k, d ≥ 2 up to a poly-logarithmic factor in k. For d = 2 in particular, we show an O(log k log log k) bound, improving near-exponentially over the previous best bound of O(k log k) by Laber and Murtinho (ICML 2021). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.101"}, {"primary_key": "1764400", "vector": [], "sparse_vector": [], "title": "Metric Distortion Bounds for Randomized Social Choice.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Metric Distortion Bounds for Randomized Social ChoiceM<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.2986 - 3004Chapter DOI:https://doi.org/10.1137/1.*************.116PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Consider the following social choice problem. Suppose we have a set of n voters and m candidates that lie in a metric space. The goal is to design a mechanism to choose a candidate whose average distance to the voters is as small as possible. However, the mechanism does not get direct access to the metric space. Instead, it gets each voter's ordinal ranking of the candidates by distance. Given only this partial information, what is the smallest worst-case approximation ratio (known as the distortion) that a mechanism can guarantee? A simple example shows that no deterministic mechanism can guarantee distortion better than 3, and no randomized mechanism can guarantee distortion better than 2. It has been conjectured that both of these lower bounds are optimal, and recently, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> proved this conjecture for deterministic mechanisms. We disprove the conjecture for randomized mechanisms for m ≥ 3 by constructing elections for which no randomized mechanism can guarantee distortion better than 2.0261 for m = 3, 2.0496 for m = 4, up to 2.1126 as m → ∞. We obtain our lower bounds by identifying a class of simple metrics that appear to capture much of the hardness of the problem, and we show that any randomized mechanism must have high distortion on one of these metrics. We provide a nearly matching upper bound for this restricted class of metrics as well. Finally, we conjecture that these bounds give the optimal distortion for every m, and provide a proof for m = 3, thereby resolving that case. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.116"}, {"primary_key": "1764401", "vector": [], "sparse_vector": [], "title": "Nearly 2-Approximate Distance Oracles in Subquadratic Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Nearly 2-Approximate Distance Oracles in Subquadratic Time<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>pp.551 - 580Chapter DOI:https://doi.org/10.1137/1.*************.26PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Let G = (V, E) be an unweighted undirected graph on n vertices and m edges. For a fixed pair of real values α ≥ 1, β ≥ 0, an (α, β) distance oracle of G is a space-efficient data structure that answers, in constant time, for any pair of vertices u,v ∊ V a distance estimate within the range of [dist(u, v), α·dist(u, v) + β]; here dist denotes distances in the graph G. Two main concerns in designing distance oracles are the approximation ratio (the stretch) and the construction time. A classical result was given in [Baswana, Goyaland and Sen 2005] which builds a (2, 3) distance oracle with Õ(n5/3) space in Õ(n2) time. Recently, [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, 2020] broke the quadratic running time at the expense of increasing the stretch. More specifically, they obtained an algorithm that constructs a (2 + ∊, 5) distance oracle with space Õ(n11/6) in O(m + n2–Ω(∊)) time for any constant ∊ ∊ (0, 1/2). In this paper, we show that one can beat the quadratic running time without compromising on the stretch. More specifically, our algorithm constructs, with high probability, a (2, 3) distance oracle with Õ(n5/3) space in Õ(m+n1.987) time. As a secondary extension, we could further reduce the preprocessing time to Õ(m+n7/4+∊) by tolerating a (2, O(1/∊)) stretch, for any constant ∊ > 0. Finally, this preprocessing time could be pushed even further to Õ(m + n5/3+∊) if we allow a stretch of (2 + ∊, c), where c = c(∊) is a constant depending exponentially on 1/∊. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.26"}, {"primary_key": "1764402", "vector": [], "sparse_vector": [], "title": "Densest Subgraph: Supermodularity, Iterative Peeling, and Flow.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Densest Subgraph: Supermodularity, Iterative Peeling, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.1531 - 1555Chapter DOI:https://doi.org/10.1137/1.*************.64PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The densest subgraph problem in a graph (DSG), in the simplest form, is the following. Given an undirected graph G = (V, E) find a subset S ⊆ V of vertices that maximizes the ratio |E(S)|/|S| where E(S) is the set of edges with both endpoints in S. DSG and several of its variants are well-studied in theory and practice and have many applications in data mining and network analysis. In this paper we study fast algorithms and structural aspects of DSG via the lens of supermodularity. For this we consider the densest supermodular subset problem (DSS): given a non-negative supermodular function f : 2V → ℝ+, maximize f(S)/|S|. For DSG we describe a simple flow-based algorithm that outputs a (1–∊)-approximation in deterministic Õ(m/∊) time where m is the number of edges. Our algorithm is the first to have a near-linear dependence on m and 1/∊ and improves previous methods based on an LP relaxation. It generalizes to hypergraphs, and also yields a faster algorithm for directed DSG. Greedy peeling algorithms have been very popular for DSG and several variants due to their efficiency, empirical performance, and worst-case approximation guarantees. We describe a simple peeling algorithm for DSS and analyze its approximation guarantee in a fashion that unifies several existing results. Boob et al. [12] developed an iterative peeling algorithm for DSG which appears to work very well in practice, and made a conjecture about its convergence to optimality. We affirmatively answer their conjecture, and in fact prove that a natural generalization of their algorithm converges to a (1–∊)-approximation for any supermodular function f; the key to our proof is to consider an LP formulation that is derived via the Lovász extension of a supermodular function. For DSG the bound on the number of iterations we prove is where Δ is the maximum degree and λ∗ is the optimum value. Our work suggests that iterative peeling can be an effective heuristic for several objectives considered in the literature. Finally, we show that the 2-approximation for densest-at-least-k subgraph [37] extends to the supermodular setting. We also give a unified analysis of the peeling algorithm for this problem, and via this analysis derive an approximation guarantee for a generalization of DSS to maximize f(S)/g(|S|) for a concave function g. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.64"}, {"primary_key": "1764403", "vector": [], "sparse_vector": [], "title": "Computational Hardness of the Hylland-Zeckhauser Scheme.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Computational Hardness of the Hylland-Zeckhauser SchemeThom<PERSON> Chen, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.2253 - 2268Chapter DOI:https://doi.org/10.1137/1.*************.90PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the complexity of the classic Hylland-Zeckhauser scheme [21] for one-sided matching markets. We show that the problem of finding an ∊-approximate equilibrium in the HZ scheme is PPAD-hard, and this holds even when ∊ is polynomially small and when each agent has no more than four distinct utility values. Our hardness result, when combined with the PPAD membership result of [29], resolves the approximation complexity of the HZ scheme. We also show that the problem of approximating within a certain constant factor the optimal social welfare (the weight of the matching) achievable by HZ equilibria is NP-hard. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.90"}, {"primary_key": "1764404", "vector": [], "sparse_vector": [], "title": "Near-Optimal Average-Case Approximate Trace Reconstruction from Few Traces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Near-Optimal Average-Case Approximate Trace Reconstruction from Few Traces<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.779 - 821Chapter DOI:https://doi.org/10.1137/1.*************.34PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the standard trace reconstruction problem, the goal is to exactly reconstruct an unknown source string x ∊ {0, 1}n from independent \"traces\", which are copies of x that have been corrupted by a δ-deletion channel which independently deletes each bit of x with probability δ and concatenates the surviving bits. We study the approximate trace reconstruction problem, in which the goal is only to obtain a high-accuracy approximation of x rather than an exact reconstruction. We give an efficient algorithm, and a near-matching lower bound, for approximate reconstruction of a random source string x ∊ {0, 1}n from few traces. Our main algorithmic result is a polynomial-time algorithm with the following property: for any deletion rate 0 < δ < 1 (which may depend on n), for almost every source string x ∊ {0,1}n, given any number M ≤ Θ(1/δ) of traces from Delδ(x), the algorithm constructs a hypothesis string that has edit distance at most n · (δM)Ω(M) from x. We also prove a near-matching information-theoretic lower bound showing that given M ≤ Θ(1/δ) traces from Delδ(x) for a random n-bit string x, the smallest possible expected edit distance that any algorithm can achieve, regardless of its running time, is n · (δM)O(M). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.34"}, {"primary_key": "1764405", "vector": [], "sparse_vector": [], "title": "Sampling Colorings and Independent Sets of Random Regular Bipartite Graphs in the Non-Uniqueness Region.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Sampling Colorings and Independent Sets of Random Regular Bipartite Graphs in the Non-Uniqueness Region<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON>, and <PERSON>.2198 - 2207Chapter DOI:https://doi.org/10.1137/1.*************.87PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We give an FPRAS for counting q-colorings for even on almost every Δ-regular bipartite graph. This improves significantly upon the previous best bound of by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (SODA'19). Analogously, for the hard-core model on independentsets weighted by λ > 0, we present an FPRAS for estimating the partition function when , which improves upon previous results by an Ω(log Δ) factor. Our results for the colorings and hard-core models follow from a general result that applies to arbitrary spin systems. Our main contribution is to show how to elevate probabilistic/analytic bounds on the marginal probabilities for the typical structure of phases on random bipartite regular graphs into efficient algorithms, using the polymer method. We further show evidence that our results for colorings and independent sets are within a constant factor of best possible using current polymer-method approaches. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.87"}, {"primary_key": "1764406", "vector": [], "sparse_vector": [], "title": "Average-Case Subset Balancing Problems.", "authors": ["<PERSON>", "Yaonan Jin", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Average-Case Subset Balancing Problems<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Servediopp.743 - 778Chapter DOI:https://doi.org/10.1137/1.*************.33PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given a set of n input integers, the Equal Subset Sum problem asks us to find two distinct subsets with the same sum. In this paper we present an algorithm that runs in time O∗(30.387n) in the average case, significantly improving over the O∗(30.488n) running time of the best known worst-case algorithm [MNPW19] and the Meet-in-the-Middle benchmark of O∗(30.5n). Our algorithm generalizes to a number of related problems, such as the \"Generalized Equal Subset Sum\" problem, which asks us to assign a coefficient ci from a set C to each input number xi such that Σi cixi = 0. Our algorithm for the average-case version of this problem runs in time for some positive constant c0, whenever C = {0, ± 1, …, ± d} or {±1, …,±d} for some positive integer d (with runtime O∗(|C|0.45n) when |C| < 10). Our results extend to the problem of finding \"nearly balanced\" solutions in which the target is a not-too-large nonzero offset τ. Our approach relies on new structural results that characterize the probability that Σi cixi = τ has a solution c ∊ Cn when xi's are chosen randomly; these results may be of independent interest. Our algorithm is inspired by the \"representation technique\" introduced by Howgrave-Graham and Joux [HGJ10]. This requires several new ideas to overcome preprocessing hurdles that arise in the representation framework, as well as a novel application of dynamic programming in the solution recovery phase of the algorithm. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.33"}, {"primary_key": "1764407", "vector": [], "sparse_vector": [], "title": "Truly Low-Space Element Distinctness and Subset Sum via Pseudorandom Hash Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Truly Low-Space Element Distinctness and Subset Sum via Pseudorandom Hash Functions<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1661 - 1678Chapter DOI:https://doi.org/10.1137/1.*************.67PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider low-space algorithms for the classic Element Distinctness problem: given an array of n input integers with O(log n) bit-length, decide whether or not all elements are pairwise distinct. <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> [FOCS 2013] gave an Õ(n1.5)-time randomized algorithm for Element Distinctness using only O(log n) bits of working space. However, their algorithm assumes a random oracle (in particular, read-only random access to polynomially many random bits), and it was asked as an open question whether this assumption can be removed. In this paper, we positively answer this question by giving an Õ(n1.5)-time randomized algorithm using O(log3 n log log n) bits of space, with one-way access to random bits. As a corollary, we also obtain a poly(n)-space O∗(20.86n)-time randomized algorithm for the Subset Sum problem, removing the random oracles required in the algorithm of Bansal, Garg, Nederlof, and Vyas [STOC 2017]. The main technique underlying our results is a pseudorandom hash family based on iterative restrictions, which can fool the cycle-finding procedure in the algorithms of Beame et al. and Bansal et al. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.67"}, {"primary_key": "1764408", "vector": [], "sparse_vector": [], "title": "Distribution-free Testing for Halfspaces (Almost) Requires PAC Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Distribution-free Testing for Halfspaces (Almost) Requires PAC Learning<PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.1715 - 1743Chapter DOI:https://doi.org/10.1137/1.*************.70PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract It is well known that halfspaces over ℝn and {0, 1}n are PAC-learnable with Θ(n) samples. Recently <PERSON><PERSON><PERSON> et al. [4] showed that even the easier task of distribution-free sample-based testing requires Ω(n/log n) samples for halfspaces. In this work we study the distribution-free testing of halfspaces with queries, for which we show that the complexity remains to be . Indeed we prove the following stronger tradeoff result: any distribution-free testing algorithm for halfspaces over {0, 1}n that receives k samples must make queries on the input function, when k satisfies n.99 ≤ k ≤ O(n/ log3 n). For halfspaces over ℝn we show that any algorithm that makes a finite number of queries must draw Ω(n/log n) many samples. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.70"}, {"primary_key": "1764409", "vector": [], "sparse_vector": [], "title": "Cut Sparsification of the Clique Beyond the Ramanujan Bound: A Separation of Cut Versus Spectral Sparsification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Cut Sparsification of the Clique Beyond the Ramanujan Bound: A Separation of Cut Versus Spectral SparsificationA<PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>pp.3693 - 3731<PERSON>hapter DOI:https://doi.org/10.1137/1.*************.146PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We prove that a random d-regular graph, with high probability, is a cut sparsifier of the clique with approximation error at most , where = 1.595 … and on,d(1) denotes an error term that depends on n and d and goes to zero if we first take the limit n → ∞ and then the limit d → ∞. This is established by analyzing linear-size cuts using techniques of <PERSON><PERSON><PERSON> and <PERSON> [13] derived from ideas in statistical physics, and analyzing small cuts via martingale inequalities. We also prove new lower bounds on spectral sparsification of the clique. If G is a spectral sparsifier of the clique and G has average degree d, we prove that the approximation error is at least the \"Ramanujan bound\" , which is met by d-regular Ramanujan graphs, provided that either the weighted adjacency matrix of G is a (multiple of) a doubly stochastic matrix, or that G satisfies a certain high \"odd pseudo-girth\" property. The first case can be seen as an \"Alon-Boppana theorem for symmetric doubly stochastic matrices,\" showing that a symmetric doubly stochastic matrix with dn non-zero entries has a non-trivial eigenvalue of magnitude at least ; the second case generalizes a lower bound of Srivastava and Trevisan [23], which requires a large girth assumption. Together, these results imply a separation between spectral sparsification and cut sparsification. If G is a random log n-regular graph on n vertices (this is to ensure that G, and consequently any d-regular subgraph, has high pseudogirth), we show that, with high probability, G admits a (weighted subgraph) cut sparsifier of average degree d and approximation error at most , while every (weighted subgraph) spectral sparsifier of G having average degree d has approximation error at least . Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.146"}, {"primary_key": "1764410", "vector": [], "sparse_vector": [], "title": "Tight running times for minimum &lt;italic&gt;ℓq&lt;/italic&gt;-norm load balancing: beyond exponential dependencies on 1/&lt;italic&gt;∊&lt;/italic&gt;.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Tight running times for minimum ℓq-norm load balancing: beyond exponential dependencies on 1/∊Lin <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON>.275 - 315Chapter DOI:https://doi.org/10.1137/1.*************.14PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider a classical scheduling problem on m identical machines. For an arbitrary constant q > 1, the aim is to assign jobs to machines such that is minimized, where Ci is the total processing time of jobs assigned to machine i. It is well known that this problem is strongly NP-hard. Under mild assumptions, the running time of an (1 + ∊)-approximation algorithm for a strongly NP-hard problem cannot be polynomial on 1/∊, unless P = NP. For most problems in the literature, this translates into algorithms with running time at least as large as 2Ω(1/∊) + nO(1). For the natural scheduling problem above, we establish the existence of an algorithm which violates this threshold. More precisely, we design a PTAS that runs in time. This result is in sharp contrast to the closely related minimum makespan variant, where an exponential lower bound is known under the exponential time hypothesis (ETH). We complement our result with an essentially matching lower bound on the running time, showing that our algorithm is best-possible under ETH. The lower bound proof exploits new number-theoretical constructions for variants of progression-free sets, which might be of independent interest. Furthermore, we provide a fine-grained characterization on the running time of a PTAS for this problem depending on the relation between ∊ and the number of machines m. More precisely, our lower bound only holds when . Better algorithms, that go beyond the lower bound, exist for other values of m. In particular, there even exists an algorithm with running time polynomial in 1/∊ if we restrict ourselves to instances with m = Ω(1/∊ log2 1/∊). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.14"}, {"primary_key": "1764411", "vector": [], "sparse_vector": [], "title": "Near-Optimal Algorithms for Linear Algebra in the Current Matrix Multiplication Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Near-Optimal Algorithms for Linear Algebra in the Current Matrix Multiplication Time<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>pp.3043 - 3068Chapter DOI:https://doi.org/10.1137/1.*************.118PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the numerical linear algebra community, it was suggested that to obtain nearly optimal bounds for various problems such as rank computation, finding a maximal linearly independent subset of columns (a basis), regression, or low-rank approximation, a natural way would be to resolve the main open question of <PERSON> and <PERSON> (FOCS, 2013). This question is regarding the logarithmic factors in the sketching dimension of existing oblivious subspace embeddings that achieve constant-factor approximation. We show how to bypass this question using a refined sketching technique, and obtain optimal or nearly optimal bounds for these problems. A key technique we use is an explicit mapping of Indyk based on uncertainty principles and extractors, which after first applying known oblivious subspace embeddings, allows us to quickly spread out the mass of the vector so that sampling is now effective. We thereby avoid a logarithmic factor in the sketching dimension that is standard in bounds proven using the matrix Chernoff inequality. For the fundamental problems of rank computation and finding a basis, our algorithms improve Cheung, Kwok, and Lau (JACM, 2013), and are optimal to within a constant factor and a poly(log log(n))-factor, respectively. Further, for constant-factor regression and low-rank approximation we give the first optimal algorithms, for the current matrix multiplication exponent. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.118"}, {"primary_key": "1764412", "vector": [], "sparse_vector": [], "title": "Faster Algorithms for Bounded-Difference Min-Plus Product.", "authors": ["Shucheng Chi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Faster Algorithms for Bounded-Difference Min-Plus ProductShucheng Chi, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> Xi<PERSON>p.1435 - 1447Chapter DOI:https://doi.org/10.1137/1.*************.60PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Min-plus product of two n × n matrices is a fundamental problem in algorithm research. It is known to be equivalent to APSP, and in general it has no truly subcubic algorithms. In this paper, we focus on the min-plus product on a special class of matrices, δ-bounded-difference matrices, in which the difference between any two adjacent entries is bounded by δ = O(1). Our algorithm runs in randomized time O(n2.779) by the fast rectangular matrix multiplication algorithm [Le Gall & Urrutia 18], better than Õ(n2+ω/3) = O(n2.791) (ω < 2.373 [<PERSON>n & <PERSON><PERSON><PERSON> 20]). This improves the previous result of Õ(n2.824) [<PERSON><PERSON> et al. 16]. When ω = 2 in the ideal case, our complexity is Õ(n2+2/3), improving <PERSON><PERSON> et al.'s result of Õ(n2.755). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.60"}, {"primary_key": "1764413", "vector": [], "sparse_vector": [], "title": "Approximating Fair Clustering with Cascaded Norm Objectives.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximating Fair Clustering with Cascaded Norm Objectives<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON>.2664 - 2683Chapter DOI:https://doi.org/10.1137/1.*************.104PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We introduce the (p, q)-Fair Clustering problem. In this problem, we are given a set of points P and a collection of different weight functions W. We would like to find a clustering which minimizes the ℓq-norm of the vector over W of the ℓp-norms of the weighted distances of points in P from the centers. This generalizes various clustering problems, including Socially Fair k-Median and k-Means, and is closely connected to other problems such as Densest k-Subgraph and Min k-Union. We utilize convex programming techniques to approximate the (p, q)-Fair Clustering problem for different values of p and q. When p ≥ q, we get an O(k(p–q)/(2pq)), which nearly matches a kΩ((p–q)/(pq)) lower bound based on conjectured hardness of Min k-Union and other problems. When q ≥ p, we get an approximation which is independent of the size of the input for bounded p,q, and also matches the recent O((log n/(log log n))1/p)-approximation for (p, ∞)-Fair Clustering by Makarychev and Vakilian (COLT 2021). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.104"}, {"primary_key": "1764414", "vector": [], "sparse_vector": [], "title": "CLAP: A New Algorithm for Promise CSPs.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)CLAP: A New Algorithm for Promise CSPs<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON>.1057 - 1068Chapter DOI:https://doi.org/10.1137/1.*************.46PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We propose a new algorithm for Promise Constraint Satisfaction Problems (PCSPs). It is a combination of the Constraint Basic LP relaxation and the Affine IP relaxation (CLAP). We give a characterisation of the power of CLAP in terms of a minion homomorphism. Using this characterisation, we identify a certain weak notion of symmetry which, if satisfied by infinitely many polymorphisms of PCSPs, guarantees tractability. We demonstrate that there are PCSPs solved by CLAP that are not solved by any of the existing algorithms for PCSPs; in particular, not by the BLP + AIP algorithm of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [SODA'20] and not by a reduction to tractable finite-domain CSPs. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.46"}, {"primary_key": "1764415", "vector": [], "sparse_vector": [], "title": "An Improved Local Search Algorithm for k-Median.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hoon Oh", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)An Improved Local Search Algorithm for k-MedianV<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>.1556 - 1612Chapter DOI:https://doi.org/10.1137/1.*************.65PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present a new local-search algorithm for the k-median clustering problem. We show that local optima for this algorithm give a (2.836 + ∊)-approximation; our result improves upon the (3 + ∊)-approximate local-search algorithm of <PERSON><PERSON> et al. [AGK+01]. Moreover, a computer-aided analysis of a natural extension suggests that this approach may lead to an improvement over the best-known approximation guarantee for the problem. The new ingredient in our algorithm is the use of a potential function based on both the closest and second-closest facilities to each client. Specifically, the potential is the sum over all clients, of the distance of the client to its closest facility, plus (a small constant times) the truncated distance to its second-closest facility. We move from one solution to another only if the latter can be obtained by swapping a constant number of facilities, and has a smaller potential than the former. This refined potential allows us to avoid the bad local optima given by Arya et al. for the local-search algorithm based only on the cost of the solution. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.65"}, {"primary_key": "1764416", "vector": [], "sparse_vector": [], "title": "Johnson Coverage Hypothesis: Inapproximability of k-means and k-median in ℓp-metrics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> C. S.", "<PERSON><PERSON><PERSON><PERSON> Lee"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Johnson Coverage Hypothesis: Inapproximability of k-means and k-median in ℓp-metricsVincent <PERSON>, <PERSON><PERSON><PERSON> C. S., and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> S., and <PERSON><PERSON><PERSON><PERSON>.1493 - 1530Chapter DOI:https://doi.org/10.1137/1.*************.63PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract k-median and k-means are the two most popular objectives for clustering algorithms. Despite intensive effort, a good understanding of the approximability of these objectives, particularly in ℓp-metrics, remains a major open problem. In this paper, we significantly improve upon the hardness of approximation factors known in literature for these objectives in ℓp-metrics. We introduce a new hypothesis called the Johnson Coverage Hypothesis (JCH), which roughly asserts that the well-studied Max k-Coverage problem on set systems is hard to approximate to a factor greater than (1–1/e), even when the membership graph of the set system is a subgraph of the Johnson graph. We then show that together with generalizations of the embedding techniques introduced by <PERSON> and <PERSON><PERSON><PERSON> (FOCS '19), JCH implies hardness of approximation results for k-median and k-means in ℓp-metrics for factors which are close to the ones obtained for general metrics. In particular, assuming JCH we show that it is hard to approximate the k-means objective: Discrete case: To a factor of 3.94 in the ℓ1-metric and to a factor of 1.73 in the ℓ2-metric; this improves upon the previous factor of 1.56 and 1.17 respectively, obtained under the Unique Games Conjecture (UGC). Continuous case: To a factor of 2.10 in the ℓ1-metric and to a factor of 1.36 in the ℓ2-metric; this improves upon the previous factor of 1.07 in the ℓ2-metric obtained under UGC (and to the best of our knowledge, the continuous case of k-means in ℓ1-metric was not previously analyzed in literature). We also obtain similar improvements under JCH for the k-median objective. Additionally, we prove a weak version of JCH using the work of Dinur et al. (SICOMP '05) on Hypergraph Vertex Cover, and recover all the results stated above of Cohen-Addad and Karthik (FOCS '19) to (nearly) the same inapproximability factors but now under the standard NP ≠ P assumption (instead of UGC). Finally, we establish a strong connection between JCH and the long standing open problem of determining the Hypergraph Turán number. We then use this connection to prove improved SDP gaps (over the existing factors in literature) for k-means and k-median objectives. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.63"}, {"primary_key": "1764417", "vector": [], "sparse_vector": [], "title": "The Sparse Parity Matrix.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)The Sparse Parity MatrixA<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>.822 - 833Chapter DOI:https://doi.org/10.1137/1.*************.35PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The last decade witnessed several pivotal results on random inference problems where the aim is to learn a hidden ground truth from indirect randomised observations; much of this research has been guided by statistical physics intuition. Prominent examples include the stochastic block model, low-density parity check codes or compressed sensing. In all random inference problems studied so far the posterior distribution of the ground truth given the observations appears to enjoy a key property called \"strong replica symmetry\". This means that the overlap of the posterior distribution with the ground truth (basically the number of bits that can be learned correctly) concentrates on a deterministic value. Whether this is generally true has been an open question. In this paper we discover an example of an inference problem based on a very simple random matrix over that fails to exhibit strong replica symmetry. Beyond its impact on random inference problems, the random matrix model, reminiscent of the binomial Erdős-R<PERSON> random graph, gives rise to a natural random constraint satisfaction problem related to the intensely studied random k-XORSAT problem. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.35"}, {"primary_key": "1764418", "vector": [], "sparse_vector": [], "title": "On finding exact solutions of linear programs in the oracle model.", "authors": ["<PERSON>", "László A. <PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)On finding exact solutions of linear programs in the oracle model<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>.2700 - 2722Chapter DOI:https://doi.org/10.1137/1.*************.106PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider linear programming in the oracle model: mincT x s.t. x ∊ P, where the polyhedron P = {x ∊ ℝn: Ax ≤ b} is given by a separation oracle that returns violated inequalities from the system Ax ≤ b. We present an algorithm that finds exact primal and dual solutions using O(n2 log(n/δ)) oracle calls and O(n4 log(n/δ) + n6 log log(1/δ)) arithmetic operations, where δ is a geometric condition number associated with the system (A, b). These bounds do not depend on the cost vector c. The algorithm works in a black box manner, requiring a subroutine for approximate primal and dual solutions; the above running times are achieved when using the cutting plane method of <PERSON>, <PERSON>, <PERSON>, and <PERSON> (STOC 2020) for this subroutine. Whereas approximate solvers may return primal solutions only, we develop a general framework for extracting dual certificates based on the work of <PERSON><PERSON> and <PERSON> (Math. Oper. Res. 1985). Our algorithm works in the real model of computation, and extends results by Grötschel, Lovász, and Schrijver (Prog. Comb. Opt. 1984), and by Frank and Tardos (Combinatorica 1987) on solving LPs in the bit-complexity model. We show that under a natural assumption, simultaneous Diophantine approximation in these results can be avoided. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.106"}, {"primary_key": "1764419", "vector": [], "sparse_vector": [], "title": "A Near-Optimal Offline Algorithm for Dynamic All-Pairs Shortest Paths in Planar Digraphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the planar, dynamic All-Pairs Shortest Paths (APSP) problem, a planar, weighted digraph G undergoes a sequence of edge weight updates and the goal is to maintain a data structure on G, that can quickly answer distance queries between any two vertices x,y ? V(G). The currently best algorithms [FOCS'01, SODA'05] for this problem require Õ(n2/3) worst-case update and query time, while conditional lower bounds [FOCS'16] show that either update or query time is needed1. In this article, we present the first algorithm with near-optimal worst-case update and query time for the offline setting, where the update sequence is given initially. This result is obtained by giving the first offline dynamic algorithm for maintaining dense distance graphs (DDGs) faster than recomputing from scratch after each update. Further, we also present an online algorithm for the incremental APSP problem with worst-case update/query time. This allows us to reduce the online dynamic APSP problem to the online decremental APSP problem, which constitutes partial progress even for the online version of this notorious problem.", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.138"}, {"primary_key": "1764420", "vector": [], "sparse_vector": [], "title": "On the Hardness of Scheduling With Non-Uniform Communication Delays.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)On the Hardness of Scheduling With Non-Uniform Communication Delays<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.316 - 328Chapter DOI:https://doi.org/10.1137/1.*************.15PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the problem of scheduling with non-uniform communication delays, the input is a set of jobs with precedence constraints. Associated with every precedence constraint between a pair of jobs is a communication delay, the time duration the scheduler has to wait between the two jobs if they are scheduled on different machines. The objective is to assign the jobs to machines to minimize the makespan of the schedule. Despite being a fundamental problem in theory and a consequential problem in practice, the approximability of scheduling problems with communication delays is not very well understood. One of the top ten open problems in scheduling theory, in the influential list by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and its latest update by <PERSON><PERSON>, asks if the problem admits a constant-factor approximation algorithm. In this paper, we answer this question in the negative by proving a logarithmic hardness for the problem under the standard complexity theory assumption that NP-complete problems do not admit quasi-polynomial-time algorithms. Our hardness result is obtained using a surprisingly simple reduction from a problem that we call Unique Machine Precedence constraints Scheduling (UMPS). We believe that this problem is of central importance in understanding the hardness of many scheduling problems and we conjecture that it is very hard to approximate. Among other things, our conjecture implies a logarithmic hardness of related machine scheduling with precedences, a long-standing open problem in scheduling theory and approximation algorithms. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.15"}, {"primary_key": "1764421", "vector": [], "sparse_vector": [], "title": "Approximating Sumset Size.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximating Sums<PERSON> SizeAnindya De, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Servediopp.2339 - 2357Chapter DOI:https://doi.org/10.1137/1.*************.94PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given a subset A of the n-dimensional Boolean hypercube , the sumset A+A is the set {a + a′ : a, a′ ∊ A} where addition is in . Sumsets play an important role in additive combinatorics, where they feature in many central results of the field. The main result of this paper is a sublinear-time algorithm for the problem of sumset size estimation. In more detail, our algorithm is given oracle access to (the indicator function of) an arbitrary and an accuracy parameter ∊ > 0, and with high probability it outputs a value 0 ≤ v ≤ 1 that is ±∊-close to Vol (A′ + A′) for some perturbation A′ ⊆ A of A satisfying Vol (A \\ A′) ≤ ∊. It is easy to see that without the relaxation of dealing with A′ rather than A, any algorithm for estimating Vol (A + A) to any nontrivial accuracy must make 2Ω(n) queries. In contrast, we give an algorithm whose query complexity depends only on ∊ and is completely independent of the ambient dimension n. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.94"}, {"primary_key": "1764422", "vector": [], "sparse_vector": [], "title": "Spectral recovery of binary censored block models.", "authors": ["Souvik Dhara", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Spectral recovery of binary censored block models<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.3389 - 3416<PERSON>hapter DOI:https://doi.org/10.1137/1.*************.134PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Community detection is the problem of identifying community structure in graphs. Often the graph is modeled as a sample from the Stochastic Block Model, in which each vertex belongs to a community. The probability that two vertices are connected by an edge depends on the communities of those vertices. In this paper, we consider a model of censored community detection with two communities, where most of the data is missing as the status of only a small fraction of the potential edges is revealed. In this model, vertices in the same community are connected with probability p while vertices in opposite communities are connected with probability q. The connectivity status of a given pair of vertices {u, v} is revealed with probability α, independently across all pairs, where α = t log(n)/n. We establish the information-theoretic threshold tc(p, q), such that no algorithm succeeds in recovering the communities exactly when t tc(p, q), a simple spectral algorithm based on a weighted, signed adjacency matrix succeeds in recovering the communities exactly. While spectral algorithms are shown to have near-optimal performance in the symmetric case, we show that they may fail in the asymmetric case where the connection probabilities inside the two communities are allowed to be different. In particular, we show the existence of a parameter regime where a simple two-phase algorithm succeeds but any algorithm based on the top two eigenvectors of the weighted, signed adjacency matrix fails. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.134"}, {"primary_key": "1764423", "vector": [], "sparse_vector": [], "title": "Nested Dissection Meets IPMs: Planar Min-Cost Flow in Nearly-Linear Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "G<PERSON><PERSON> Ye"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Nested Dissection Meets IPMs: Planar Min-Cost Flow in Nearly-Linear TimeSally <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.124 - 153Chapter DOI:https://doi.org/10.1137/1.*************.7PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present a nearly-linear time algorithm for finding a minimum-cost flow in planar graphs with polynomially bounded integer costs and capacities. The previous fastest algorithm for this problem was based on interior point methods (IPMs) and worked for general sparse graphs in O(n1.5 poly(log n)) time [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ST<PERSON>'08]. Intuitively, Ω(n1.5) is a natural runtime barrier for IPM based methods, since they require iterations, each routing a possibly-dense electrical flow. To break this barrier, we develop a new implicit representation for flows based on generalized nested-dissection [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>'79] and approximate Schur complements [<PERSON><PERSON><PERSON><PERSON>, <PERSON>OCS'16]. This implicit representation permits us to design a data structure to route an electrical flow with sparse demands in roughly update time, resulting in a total running time of O(n · poly(log n)). Our results immediately extend to all families of separable graphs. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.7"}, {"primary_key": "1764424", "vector": [], "sparse_vector": [], "title": "Enumerating k-SAT functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Enumerating k-SAT functionsD<PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.2141 - 2184Chapter DOI:https://doi.org/10.1137/1.*************.85PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract How many k-SAT functions on n boolean variables are there? What does a typical such function look like? <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> conjectured that, for each fixed k ≥ 2, the number of k-SAT functions on n variables is , or equivalently: a 1–o(1) fraction of all k-SAT functions are unate, i.e., monotone after negating some variables. They proved a weaker version of the conjecture for k = 2. The conjecture was confirmed for k = 2 by <PERSON> and k = 3 by <PERSON><PERSON><PERSON> and <PERSON>. We show that the problem of enumerating k-SAT functions is equivalent to a Turán density problem for partially directed hypergraphs. Our proof uses the hypergraph container method. Furthermore, we confirm the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> conjecture for k = 4 by solving the corresponding Turán density problem. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.85"}, {"primary_key": "1764425", "vector": [], "sparse_vector": [], "title": "Streaming Regular Expression Membership and Pattern Matching.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Streaming Regular Expression Membership and Pattern MatchingB<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.670 - 694Chapter DOI:https://doi.org/10.1137/1.*************.30PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Regular expression search is a key primitive in myriads of applications, from web scrapping to bioinformatics. A regular expression is a formalism for compactly describing a set of strings, built recursively from single characters using three operators: concatenation, union, and Kleene star. Two basic algorithmic problems concerning such expressions are membership and pattern matching. In the regular expression membership problem, we are given a regular expression R and a string T of length n, and must decide whether T matches R. In the regular expression pattern matching problem, the task to find the substrings of T that match R. By now we have a good understanding of the complexity of regular expression membership and pattern matching in the classical setting. However, only some special cases have been considered in the practically relevant streaming setting: dictionary matching and wildcard pattern matching. In the dictionary matching problem, we are given a dictionary of d strings of length at most m and a string T, and must find substrings of T that match one of the dictionary strings. In the wildcard pattern matching problem, we are given a string P of length m that contains d wildcards, where a wildcard is a special symbol that matches any character of the alphabet, and a string T, and must find all substrings of T that match P. Both problems can be solved in the streaming model by a randomised Monte Carlo algorithm that uses (d log m) space [Golan and Porat (ESA 2017), Golan, Kopelowitz and Porat (Algorithmica 2019)]. In the general case, we cannot hope for a streaming algorithm with space complexity smaller than the length of R for either variant of regular expression search. The main contribution of this paper is that we identify the number of unions and Kleene stars, denoted by d, as the parameter that allows for an efficient streaming algorithm. This parameter has been previously considered in the classical setting, and it has been observed that in practice it is significantly smaller than the length of R. We design general randomised Monte Carlo algorithms for both problems that use (d3 polylog n) space in the streaming setting. A crucial technical ingredient of our algorithms is an adaptation of the general framework for evaluating a circuit with addition and convolution gates in a space-efficient manner [Lokshtanov and Nederlof (STOC 2010), Bringmann (SODA 2017)], initially designed as a key component of a pseudopolynomial time algorithm for the subset sum problem. We show how to replace the Extended Generalised Riemann Hypothesis in [Bringmann (SODA 2017)] by an application of the Bombieri–Vinogradov theorem to achieve the same bounds (but unconditionally), which might be of independent interest. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.30"}, {"primary_key": "1764426", "vector": [], "sparse_vector": [], "title": "Private Interdependent Valuations.", "authors": ["Alon Eden", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Private Interdependent ValuationsA<PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.2920 - 2939Chapter DOI:https://doi.org/10.1137/1.*************.113PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the single-item interdependent value setting, where there is a single item sold by a monopolist, n buyers, and each buyer has a private signal si describing a piece of information about the item. Additionally, each bidder i has a valuation function vi(s1, …, sn) mapping the (private) signals of all buyers into a positive real number representing their value for the item. This setting captures scenarios where the item's information is asymmetric or dispersed among agents, such as in competitions for oil drilling rights, or in auctions for art pieces. Due to the increased complexity of this model compared to the standard private values model, it is generally assumed that each bidder's valuation function vi is public knowledge to the seller or all other buyers. But in many situations, the seller may not know the bidders' valuation functions—how a bidder aggregates signals into a valuation is often their private information. In this paper, we design mechanisms that guarantee approximately-optimal social welfare while satisfying ex-post incentive compatibility and individually rationality for the case where the valuation functions are private to the bidders, and thus may be strategically misreported to the seller. When the valuations are public, it is possible for optimal social welfare to be attained by a deterministic mechanism when the valuations satisfy a single-crossing condition. In contrast, when the valuations are the bidders' private information, we show that no finite bound on the social welfare can be achieved by any deterministic mechanism even under single-crossing. Moreover, no randomized mechanism can guarantee better than n-approximation. We thus consider valuation functions that are submodular over signals (SOS), introduced in the context of combinatorial auctions in a recent breakthrough paper by Eden et al. [EC'19]. Our main result is an O(log2 n)-approximation randomized mechanism for buyers with private signals and valuations under the SOS condition. We also give a tight Θ(k)-approximation mechanism for the case each agent's valuation depends on at most k other signals even for unknown k. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.113"}, {"primary_key": "1764427", "vector": [], "sparse_vector": [], "title": "Approximating the Arboricity in Sublinear Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Mossel", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximating the Arboricity in Sublinear TimeTalya Eden, Saleet Mossel, and <PERSON>, <PERSON><PERSON> Mossel, and <PERSON>.2404 - 2425Chapter DOI:https://doi.org/10.1137/1.*************.96PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the problem of approximating the arboricity of a graph G = (V, E), which we denote by arb(G), in sublinear time, where the arboricity of a graph is the minimal number of forests required to cover its edge set. An algorithm for this problem may perform degree and neighbor queries, and is allowed a small error probability. We design an algorithm that outputs an estimate , such that with probability 1–1/poly(n), arb(G) ≤ ≤ clog2 n-arb(G), where n = |V| and c is a constant. The expected query complexity and running time of the algorithm are O(n/arb(G)) · poly(log n), and this upper bound also holds with high probability. This bound is optimal for such an approximation up to a poly (log n) factor. For the closely related problem of finding the densest subgraph, <PERSON><PERSON><PERSON> et al. (STOC, 2015) showed that there exists a factor-2 approximation algorithm that runs in time O(n) · poly (log n). In a follow up work, McGregor et al. (MFCS, 2015) improved the approximation factor to (1 + ∊) with the same complexity. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.96"}, {"primary_key": "1764428", "vector": [], "sparse_vector": [], "title": "Massively Parallel and Dynamic Algorithms for Minimum Size Clustering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Massively Parallel and Dynamic Algorithms for Minimum Size Clustering<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1613 - 1660Chapter DOI:https://doi.org/10.1137/1.*************.66PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Clustering of data in metric spaces is a fundamental problem and has many applications in data mining and it is often used as an unsupervised learning tool inside other machine learning systems. In many scenarios where we are concerned with the privacy implications of clustering users, clusters are required to have minimum-size constraint. A canonical example of min-size clustering is in enforcing anonymization and the protection of the privacy of user data. Our work is motivated by real-world applications (such as the Federated Learning of Cohorts project–FLoC) where a min size clustering algorithm needs to handle very large amount of data and the data may also change over time. Thus efficient parallel or dynamic algorithms are desired. In this paper, we study the r-gather problem, a natural formulation of minimum-size clustering in metric spaces. The goal of r-gather is to partition n points into clusters such that each cluster has size at least r, and the maximum radius of the clusters is minimized. This additional constraint completely changes the algorithmic nature of the problem, and many clustering techniques fail. Also previous dynamic and parallel algorithms do not achieve desirable complexity. We propose algorithms both in the Massively Parallel Computation (MPC) model and in the dynamic setting. Our MPC algorithm handles input points from the Euclidean space ℝd. It computes an O(1)-approximate solution of r-gather in O(log∊ n) rounds using total space O(n1 + γ · d) for arbitrarily small constants ∊, γ > 0. In addition our algorithm is fully scalable, i.e., there is no lower bound on the memory per machine. Our dynamic algorithm maintains an O(1)-approximate r-gather solution under insertions/deletions of points in a metric space with doubling dimension d. The update time is r·2O(d)·logO(1) Λ and the query time is 2O(d) · logO(1) Λ, where Λ is the ratio between the largest and the smallest distance. To obtain our results, we reveal connections between r-gather and r-nearest neighbors and provide several geometric and graph algorithmic tools including a near neighbor graph construction, and results on the maximal independent set / ruling set of the power graph in the MPC model, which might be both of independent interest. To show their generality, we extend our algorithm to solve several variants of r-gather in the MPC model, including r-gather with outliers and r-gather with total distance cost. Finally, we show effectiveness of these algorithmic techniques via a preliminary empirical study for Interest-Based Advertisement applications. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.66"}, {"primary_key": "1764429", "vector": [], "sparse_vector": [], "title": "Improved Sliding Window Algorithms for Clustering and Coverage via Bucketing-Based Sketches.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Improved Sliding Window Algorithms for Clustering and Coverage via Bucketing-Based Sketches<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.3005 - 3042Chapter DOI:https://doi.org/10.1137/1.*************.117PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Streaming computation plays an important role in large-scale data analysis. The sliding window model is a model of streaming computation which also captures the recency of the data. In this model, data arrives one item at a time, but only the latest W data items are considered for a particular problem. The goal is to output a good solution at the end of the stream by maintaining a small summary during the stream. In this work, we propose a new algorithmic framework for designing efficient sliding window algorithms via bucketing-based sketches. Based on this new framework, we develop space-efficient sliding window algorithms for k-cover, k-clustering and diversity maximization problems. For each of the above problems, our algorithm achieves (1 ± ∊)-approximation. Compared with the previous work, it improves both the approximation ratio and the space. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.117"}, {"primary_key": "1764430", "vector": [], "sparse_vector": [], "title": "Almost Tight Approximation Algorithms for Explainable Clustering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Almost Tight Approximation Algorithms for Explainable ClusteringH<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>pp.2641 - 2663Chapter DOI:https://doi.org/10.1137/1.*************.103PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Recently, due to an increasing interest for transparency in artificial intelligence, several methods of explainable machine learning have been developed with the simultaneous goal of accuracy and interpretability by humans. In this paper, we study a recent framework of explainable clustering first suggested by <PERSON><PERSON><PERSON> et al. [11]. Specifically, we focus on the k-means and k-median problems and provide nearly tight upper and lower bounds. First, we provide an O(log k log log k)-approximation algorithm for explainable k-median, improving on the best known algorithm of O(k) [11] and nearly matching the known Ω(log k) lower bound [11]. In addition, in low-dimensional spaces d ≪ log k, we show that our algorithm also provides an O(d log2 d)-approximate solution for explainable k-median. This improves over the best known bound of O(d log k) for low dimensions [19], and is a constant for constant dimensional spaces. To complement this, we show a nearly matching Ω(d) lower bound. Next, we study the k-means problem in this context and provide an O(k log k)-approximation algorithm for explainable k-means, improving over the O(k2) bound of Dasgupta et al. and the O(dk log k) bound of [19]. To complement this we provide an almost tight Ω(k) lower bound, improving over the Ω(log k) lower bound of Dasgupta et al. Given an approximate solution to the classic k-means and k-median, our algorithm for k-median runs in time O(kd log2 k) and our algorithm for k-means runs in time O(k2 d). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.103"}, {"primary_key": "1764431", "vector": [], "sparse_vector": [], "title": "Computing Lewis Weights to High Precision.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Computing Lewis Weights to High Precision<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>.2723 - 2742Chapter DOI:https://doi.org/10.1137/1.*************.107PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present an algorithm for computing approximate ℓp Lewis weights to high precision. Given a full-rank A ∊ ℝm × n with m ≥ n and a scalar p > 2, our algorithm computes ∊-approximate ℓp Lewis weights of A in Õp(log(1/∊)) iterations; the cost of each iteration is linear in the input size plus the cost of computing the leverage scores of DA for diagonal D ∊ ℝm × m. Prior to our work, such a computational complexity was known only for p ∊ (0,4) [CP15], and combined with this result, our work yields the first polylogarithmic-depth polynomial-work algorithm for the problem of computing ℓp Lewis weights to high precision for all constant p > 0. An important consequence of this result is also the first polylogarithmic-depth polynomial-work algorithm for computing a nearly optimal self-concordant barrier for a polytope. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.107"}, {"primary_key": "1764432", "vector": [], "sparse_vector": [], "title": "Counting list homomorphisms from graphs of bounded treewidth: tight complexity bounds.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Counting list homomorphisms from graphs of bounded treewidth: tight complexity bounds<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.431 - 458Chapter DOI:https://doi.org/10.1137/1.*************.22PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The goal of this work is to give precise bounds on the counting complexity of a family of generalized coloring problems (list homomorphisms) on bounded-treewidth graphs. Given graphs G, H, and lists L(v) ⊆ V(H) for every v ∊ V(G), a list homomorphism is a function f : V(G) → V(H) that preserves the edges (i.e., uv ∊ E(G) implies f(u)f(v) ∊ E(H)) and respects the lists (i.e., f(v) ∊ L(v)). Standard techniques show that if G is given with a tree decomposition of width t, then the number of list homomorphisms can be counted in time . Our main result is determining, for every fixed graph H, how much the base |V(H)| in the running time can be improved. For a connected graph H we define irr(H) in the following way: if H has a loop or is nonbipartite, then irr(H) is the maximum size of a set S ⊆ V(H) where any two vertices have different neighborhoods; if H is bipartite, then irr(H) is the maximum size of such a set that is fully in one of the bipartition classes. For disconnected H, we define irr(H) as the maximum of irr(C) over every connected component C of H. It follows from earlier results that if irr(H) = 1, then the problem of counting list homomorphisms to H is polynomial-time solvable, and otherwise it is #P-hard. We show that, for every fixed graph H, the number of list homomorphisms from (G, L) to H can be counted in time if a tree decomposition of G having width at most t is given in the input, and given that irr(H) ≥ 2, cannot be counted in time for any ∊ > 0, even if a tree decomposition of G having width at most t is given in the input, unless the Counting Strong Exponential-Time Hypothesis (#SETH) fails. Thereby we give a precise and complete complexity classification featuring matching upper and lower bounds for all target graphs with or without loops. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.22"}, {"primary_key": "1764433", "vector": [], "sparse_vector": [], "title": "Algorithmic Extensions of Dirac&apos;s Theorem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Algorithmic Extensions of Dirac's Theorem<PERSON>ed<PERSON> <PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.406 - 416Chapter DOI:https://doi.org/10.1137/1.*************.20PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In 1952, <PERSON><PERSON> proved the following theorem about long cycles in graphs with large minimum vertex degrees: Every n-vertex 2-connected graph G with minimum vertex degree δ ≥ 2 contains a cycle with at least min{2δ, n} vertices. In particular, if δ ≥ n/2, then G is Hamiltonian. The proof of <PERSON><PERSON>'s theorem is constructive, and it yields an algorithm computing the corresponding cycle in polynomial time. The combinatorial bound of <PERSON><PERSON>'s theorem is tight in the following sense. There are 2-connected graphs that do not contain cycles of length more than 2δ + 1. Also, there are non-Hamiltonian graphs with all vertices but one of degree at least n/2. This prompts naturally to the following algorithmic questions. For k ≥ 1, (A)How difficult is to decide whether a 2-connected graph contains a cycle of length at least min{2δ + k, n}?(B)How difficult is to decide whether a graph G is Hamiltonian, when at least n–k vertices of G are of degrees at least n/2–k? The first question was asked by Fomin, Golovach, Lokshtanov, <PERSON>olan, Saurabh, and Zehavi. The second question is due to Jansen, Kozma, and Nederlof. Even for a very special case of k = 1, the existence of a polynomial-time algorithm deciding whether G contains a cycle of length at least min{2δ + 1, n} was open. We resolve both questions by proving the following algorithmic generalization of Dirac's theorem: If all but k vertices of a 2-connected graph G are of degree at least δ, then deciding whether G has a cycle of length at least min{2δ + k, n} can be done in time . The proof of the algorithmic generalization of Dirac's theorem builds on new graph-theoretical results that are interesting on their own. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.20"}, {"primary_key": "1764434", "vector": [], "sparse_vector": [], "title": "An Improved Algorithm for The k-Dyck Edit Distance Problem.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)An Improved Algorithm for The k-Dyck Edit Distance ProblemD<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.3650 - 3669Chapter DOI:https://doi.org/10.1137/1.*************.144PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A Dyck sequence is a sequence of opening and closing parentheses (of various types) that is balanced. The Dyck edit distance of a given sequence of parentheses S is the smallest number of edit operations (insertions, deletions, and substitutions) needed to transform S into a Dyck sequence. We consider the threshold Dyck edit distance problem, where the input is a sequence of parentheses S and a positive integer k, and the goal is to compute the Dyck edit distance of S only if the distance is at most k, and otherwise report that the distance is larger than k. <PERSON> and <PERSON><PERSON> [PODS'16] showed that the threshold Dyck edit distance problem can be solved in O(n + k16) time. In this work, we design new algorithms for the threshold Dyck edit distance problem which costs O(n + k4.782036) time with high probability or O(n + k4.853059) deterministically. Our algorithms combine several new structural properties of the Dyck edit distance problem, a refined algorithm for fast (min, +) matrix product, and a careful modification of ideas used in Valiant's parsing algorithm. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.144"}, {"primary_key": "1764435", "vector": [], "sparse_vector": [], "title": "A 3-Approximation Algorithm for Maximum Independent Set of Rectangles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A 3-Approximation Algorithm for Maximum Independent Set of Rectangles<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>.894 - 905Chapter DOI:https://doi.org/10.1137/1.*************.38PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the Maximum Independent Set of Rectangles (MISR) problem, where we are given a set of axis-parallel rectangles in the plane and the goal is to select a subset of non-overlapping rectangles of maximum cardinality. In a recent breakthrough, <PERSON> [46] obtained the first constant-factor approximation algorithm for MISR. His algorithm achieves an approximation ratio of 10 and it is based on a dynamic program that intuitively recursively partitions the input plane into special polygons called corner-clipped rectangles (CCRs), without intersecting certain special horizontal line segments called fences. In this paper, we present a 3-approximation algorithm for MISR which is also based on a recursive partitioning scheme. First, we use a partition into a class of axis-parallel polygons with constant complexity each that are more general than CCRs. This allows us to provide an arguably simpler analysis and at the same time already improves the approximation ratio to 6. Then, using a more elaborate charging scheme and a recursive partitioning into general axis-parallel polygons with constant complexity, we improve our approximation ratio to 3. In particular, we construct a recursive partitioning based on more general fences which can be sequences of up to O(1) line segments each. This partitioning routine and our other new ideas may be useful for future work towards a PTAS for MISR. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.38"}, {"primary_key": "1764436", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> Matching on Grammar-Compressed Strings in Linear Time.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Pattern Matching on Grammar-Compressed Strings in Linear TimeM<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.2833 - 2846Chapter DOI:https://doi.org/10.1137/1.*************.110PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The most fundamental problem considered in algorithms for text processing is pattern matching: given a pattern p of length m and a text t of length n, does p occur in t? Multiple versions of this basic question have been considered, and by now we know algorithms that are fast both in practice and in theory. However, the rapid increase in the amount of generated and stored data brings the need of designing algorithms that operate directly on compressed representations of data. In the compressed pattern matching problem we are given a compressed representation of the text, with n being the length of the compressed representation and N being the length of the text, and an uncompressed pattern of length m. The most challenging (and yet relevant when working with highly repetitive data, say biological information) scenario is when the chosen compression method is capable of describing a string of exponential length (in the size of its representation). An elegant formalism for such a compression method is that of straight-line programs, which are simply context-free grammars describing exactly one string. While it has been known that compressed pattern matching problem can be solved in O(m + n log N) time for this compression method, designing a linear-time algorithm remained open. We resolve this open question by presenting an O(n + m) time algorithm that, given a context-free grammar of size n that produces a single string t and a pattern p of length m, decides whether p occurs in t as a substring. To this end, we devise improved solutions for the weighted ancestor problem and the substring concatenation problem. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.110"}, {"primary_key": "1764437", "vector": [], "sparse_vector": [], "title": "How Compression and Approximation Affect Efficiency in String Distance Measures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)How Compression and Approximation Affect Efficiency in String Distance Measures<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.2867 - 2919Chapter DOI:https://doi.org/10.1137/1.*************.112PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Real-world data often comes in compressed form. Analyzing compressed data directly (without first decompressing it) can save space and time by orders of magnitude. In this work, we focus on fundamental sequence comparison problems and try to quantify the gain in time complexity when the underlying data is highly compressible. We consider grammar compression, which unifies many practically relevant compression schemes such as the Lempel–Ziv family, dictionary methods, and others. For two strings of total length N and total compressed size n, it is known that the edit distance and a longest common subsequence (LCS) can be computed exactly in time Õ(nN), as opposed to O(N2) for the uncompressed setting. Many real-world applications need to align multiple sequences simultaneously, and the fastest known exact algorithms for median edit distance and LCS of k strings run in O(Nk) time, whereas the one for center edit distance has a time complexity of O(N2k). This naturally raises the question if compression can help to reduce the running time significantly for k ≥ 3, perhaps to O(Nk/2 nk/2) or, more optimistically, to O(Nnk–1).1 Unfortunately, we show new lower bounds that rule out any improvement beyond Ω(Nk–1 n) time for any of these problems assuming the Strong Exponential Time Hypothesis (SETH), where again N and n represent the total length and the total compressed size, respectively. This answers an open question of Abboud, Backurs, Bringmann, and Künnemann (FOCS'17). In presence of such negative results, we ask if allowing approximation can help, and we show that approximation and compression together can be surprisingly effective for both multiple and two strings. We develop an Õ(Nk/2 nk/2)-time FPTAS for the median edit distance of k sequences, leading to a saving of nearly half the dimensions for highly-compressible sequences. In comparison, no O(Nk–Ω(1))-time PTAS is known for the median edit distance problem in the uncompressed setting. We obtain an improvement from for the center edit distance problem. For two strings, we get an -time FPTAS for both edit distance and LCS; note that this running time is o(N) whenever n ≪ N1/4. In contrast, for uncompressed strings, there is not even a subquadratic algorithm for LCS that has less than polynomial gap in the approximation factor. Building on the insight from our approximation algorithms, we also obtain several new and improved results for many fundamental distance measures including the edit, Hamming, and shift distances. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.112"}, {"primary_key": "1764438", "vector": [], "sparse_vector": [], "title": "Approximating Equilibrium under Constrained Piecewise Linear Concave Utilities with Applications to Matching Markets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "László A. <PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximating Equilibrium under Constrained Piecewise Linear Concave Utilities with Applications to Matching MarketsJugal Garg, <PERSON><PERSON>, and László A<PERSON>, <PERSON><PERSON>, and László A. Véghpp.2269 - 2284Chapter DOI:https://doi.org/10.1137/1.*************.91PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the equilibrium computation problem in the Fisher market model with constrained piecewise linear concave (PLC) utilities. This general class captures many well-studied special cases, including markets with PLC utilities, markets with satiation, and matching markets. For the special case of PLC utilities, although the problem is PPAD-hard, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (F<PERSON>S 2008) gave a polynomial-time algorithm when the number of goods is constant. Our main result is a fixed parameter approximation scheme for computing an approximate equilibrium, where the parameters are the number of agents and the approximation accuracy. This provides an answer to an open question by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> for PLC utilities, and gives a simpler and faster algorithm for matching markets as the one by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (EC 2017). The main technical idea is to work with the stronger concept of thrifty equilibria, and approximating the input utility functions by 'robust' utilities that have favorable marginal properties. With some restrictions, the results also extend to the Arrow–Debreu exchange market model. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.91"}, {"primary_key": "1764439", "vector": [], "sparse_vector": [], "title": "A Deterministic Parallel Reduction from Weighted Matroid Intersection Search to Decision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A Deterministic Parallel Reduction from Weighted Matroid Intersection Search to <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1013 - 1035Chapter DOI:https://doi.org/10.1137/1.*************.44PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given two matroids on the same ground set, the matroid intersection problem asks for a common base, i.e., a subset of the ground set that is a base in both the matroids. The weighted version of the problem asks for a common base with maximum weight. In the general case, when the two matroids are given via rank oracles, the question of its parallel complexity is completely open. In the case of linearly representable matroids, the problem is known to have randomized parallel (RNC) algorithms, when the given weights are polynomially bounded. Finding a deterministic parallel (NC) algorithm in this case, even for the decision question, has been a long standing open question. We make some progress towards understanding the parallel complexity of matroid intersection by showing that the weighted matroid intersection (WMI) search problem is equivalent to its decision version, in a parallel model of computation. More precisely, we give an NC algorithm for WMI-search using an oracle access to WMI-decision. This resolves an open question posed by <PERSON>ri and Vazirani (ITCS 2020). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.44"}, {"primary_key": "1764440", "vector": [], "sparse_vector": [], "title": "Directed Tangle Tree-Decompositions and Applications.", "authors": ["Archontia <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Directed Tangle Tree-Decompositions and ApplicationsArchontia <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>ont<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>.377 - 405Chapter DOI:https://doi.org/10.1137/1.*************.19PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The tangle tree-decomposition theorem, proved by <PERSON> and <PERSON> in their seminal graph minors series, turns out to be an extremely valuable tool in structural and algorithmic graph theory. In this paper, we prove the analogous result for digraphs, the directed tangle tree-decomposition theorem. More precisely, we introduce directed tangles and provide a directed tree-decomposition of digraphs G that distinguishes all maximal directed tangles in G. Furthermore, for any integer k, we construct a directed tree-decomposition that distinguishes all directed tangles of order k. By relaxing the bound slightly, we can make the previous result algorithmic: for fixed k, we design a polynomial-time algorithm that finds a directed tree-decomposition distinguishing all directed tangles of order 6k–1 separated by some separation of order less than k. As a direct application of the tangle tree-decomposition theorem, we prove that for every fixed k there is a polynomial-time algorithm which, on input G, and source and sink vertices (s1, t1),…, (sk, tk), either finds a family of paths P1,…, Pk such that each Pi links si to ti and every vertex of G is contained in at most two paths, or determines that there is no set of pairwise vertex-disjoint paths each connecting si to ti. This result improves previous results (with \"two\" replaced by \"three\"), and given known hardness results, our result cannot be extended to fixed parameter tractability nor fully vertex-disjoint directed paths. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.19"}, {"primary_key": "1764441", "vector": [], "sparse_vector": [], "title": "Counting Homomorphic Cycles in Degenerate Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Counting Homomorphic Cycles in Degenerate Graphs<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>.417 - 430Chapter DOI:https://doi.org/10.1137/1.*************.21PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Since counting subgraphs in general graphs is, by and large, a computationally demanding problem, it is natural to try and design fast algorithms for restricted families of graphs. One such family that has been extensively studied is that of graphs of bounded degeneracy (e.g., planar graphs). This line of work, which started in the early 80's, culminated in a recent work of <PERSON><PERSON><PERSON><PERSON> et al., which highlighted the importance of the task of counting homomorphic copies of cycles (i.e., cyclic walks) in graphs of bounded degeneracy. Our main result in this paper is a surprisingly tight relation between the above task and the well-studied problem of detecting (standard) copies of directed cycles in general directed graphs. More precisely, we prove the following: One can compute the number of homomorphic copies of C2k and C2k+1 in n-vertex graphs of bounded degeneracy in time , where the fastest known algorithm for detecting directed copies of Ck in general m-edge digraphs runs in time . Conversely, one can transform any algorithm for computing the number of homomorphic copies of C2k or of C2k+1 in n-vertex graphs of bounded degeneracy, into an time algorithm for detecting directed copies of Ck in general m-edge digraphs. We emphasize that our first result does not use a black-box reduction (as opposed to the second result which does). Instead, we design an algorithm for computing the number of Ck-homomorphisms in degenerate graphs and show that one part of its analysis can be reduced to the analysis of the fastest known algorithm for detecting directed cycles in general digraphs, which was carried out in a recent breakthrough of Dalirrooyfard, Vuong and Vassilevska Williams. As a by-product of our algorithm, we obtain a new algorithm for detecting k-cycles in directed and undirected graphs of bounded degeneracy that is faster than all previously known algorithms for 7 ≤ k ≤ 11, and faster for all k ≥ 7 if the matrix multiplication exponent is 2. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.21"}, {"primary_key": "1764442", "vector": [], "sparse_vector": [], "title": "Unsplittable Flow on a Path: The Game!.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Unsplittable Flow on a Path: The Game!<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.906 - 926<PERSON>hapter DOI:https://doi.org/10.1137/1.*************.39PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The unsplittable flow on a path (UFP) problem is a well-studied optimization problem, and it has applications in various settings like bandwidth allocation, caching, and scheduling. We are given a path with capacities on its edges and a set of n tasks, each of them defined via a demand, a subpath, and a profit. The goal is to select the most profitable set of tasks that together respect the edge capacities, i.e., for each edge e the total demand of the selected tasks whose subpath contains e is at most the capacity of e. The best known polynomial time approximation algorithm for UFP is a (5/3 + ∊)-approximation [<PERSON> et al., STOC 2018]. It is an important open question whether the problem admits a PTAS. Informally, a task is large if its demand is at least an ∊-fraction of the capacity of some edge on its path, and small otherwise. If all tasks are large, a PTAS can be obtained via dynamic programming: intuitively each edge e is used by only O(1) relevant tasks in the optimal solution OPT. The same approach fails for small tasks since then this number can be up to Ω(n) which would yield an exponential number of states. In this paper we introduce a novel randomized sketching technique to address this issue. We model the computation of a solution as a solitary game where tasks are presented one by one to a player, who has to decide for each task i whether to select i (hence getting its profit) or not. When a small task i is selected, with some probability its demand is rounded up to some large value (and then i behaves like a large task), and otherwise down to zero (and then i can be \"forgotten\" afterwards), so that in expectation the demand of i does not change. The optimal strategy to play this game can be computed using similar ideas as used in the DP for large tasks. Furthermore, the expected profit of this strategy is at least as large as the profit of OPT. One complication is that the player's solution might be infeasible, e.g., when too many tasks are rounded down. Still, via probabilistic arguments, we can use it to construct a feasible UFP solution which is 1 + + ∊ < 1.269 approximate in expectation. It is potentially possible that a more sophisticated probabilistic analysis gives a PTAS for the problem. We believe that randomized sketching might turn out to be useful to address also other problems in which \"large\" and \"small\" objects interact, for example in packing, scheduling, or resource allocation settings, in particular when dynamic programming works if there are only large objects. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.39"}, {"primary_key": "1764443", "vector": [], "sparse_vector": [], "title": "A Tail Estimate with Exponential Decay for the Randomized Incremental Construction of Search Structures.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A Tail Estimate with Exponential Decay for the Randomized Incremental Construction of Search StructuresJ<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.610 - 626Chapter DOI:https://doi.org/10.1137/1.*************.28PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The Randomized Incremental Construction (RIC) of search DAGs for point location in planar subdivisions, nearest-neighbor search in 2D points, and extreme point search in 3D convex hulls, are well known to take (n log n) expected time for structures of (n) expected size. Moreover, searching takes w.h.p. (log n) comparisons in the first and w.h.p. (log2 n) comparisons in the latter two DAGs. However, the expected depth of the DAGs and high probability bounds for their size are unknown. Using a novel analysis technique, we show that the three DAGs have w.h.p. i) a size of (n), ii) a depth of (log n), and iii) a construction time of (n log n). One application of these new and improved results are remarkably simple Las Vegas verifiers to obtain search DAGs with optimal worst-case bounds. This positively answers the conjectured logarithmic search cost in the DAG of Delaunay triangulations [Guibas et al.; ICALP 1990] and a conjecture on the depth of the DAG of Trapezoidal subdivisions [Hemmer et al.; ESA 2012]. It also shows that history-based RIC circumvents a lower bound on runtime tail estimates of conflict-graph RICs [Sen; STACS 2019]. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771Key words:Randomized Incremental Construction, Data Structures, Tail Bound, Las Vegas Algorithm", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.28"}, {"primary_key": "1764444", "vector": [], "sparse_vector": [], "title": "Cubic upper and lower bounds for subtrajectory clustering under the continuous <PERSON><PERSON><PERSON> distance.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Cubic upper and lower bounds for subtrajectory clustering under the continuous Fréchet distance<PERSON><PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.173 - 189Chapter DOI:https://doi.org/10.1137/1.*************.9PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Detecting commuting patterns or migration patterns in movement data is an important problem in computational movement analysis. Given a trajectory, or set of trajectories, this corresponds to clustering similar subtrajectories. We study subtrajectory clustering under the continuous and discrete Fréchet distances. The most relevant theoretical result is by <PERSON><PERSON><PERSON> et al. (2011). They provide, in the continuous case, an O(n5) time algorithm1 and a 3SUM-hardness lower bound, and in the discrete case, an O(n3) time algorithm. We show, in the continuous case, an O(n3 log2 n) time algorithm and a 3OV-hardness lower bound, and in the discrete case, an O(n2 log n) time algorithm and a quadratic lower bound. Our bounds are almost tight unless <PERSON><PERSON> fails. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.9"}, {"primary_key": "1764445", "vector": [], "sparse_vector": [], "title": "Online Discrepancy with Recourse for Vectors and Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Online Discrepancy with Recourse for Vectors and Graphs<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.1356 - 1383Chapter DOI:https://doi.org/10.1137/1.*************.57PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The vector-balancing problem is a fundamental problem in discrepancy theory: given T vectors in [–1, 1]n, find a signing σ(a) ∊ {±1} of each vector a to minimize the discrepancy ‖ Σa σ(a) · a‖∞. This problem has been extensively studied in the static/offline setting. In this paper we initiate its study in the fully-dynamic setting with recourse: the algorithm sees a stream of T insertions and deletions of vectors, and at each time must maintain a low-discrepancy signing, while also minimizing the amortized recourse (the number of times any vector changes its sign) per update. For general vectors, we show algorithms which almost match <PERSON>'s offline discrepancy bound, with O(n polylog T) amortized recourse per update. The crucial idea behind our algorithm is to compute a basic feasible solution to the linear relaxation in a distributed and recursive manner, which helps find a low-discrepancy signing. We bound the recourse using the distributed computation of the basic solution, and argue that only a small part of the instance needs to be re-computed at each update. Since vector balancing has also been greatly studied for sparse vectors, we then give algorithms for low-discrepancy edge orientation, where we dynamically maintain signings for 2-sparse vectors in an n-dimensional space. Alternatively, this can be seen as orienting a dynamic set of edges of an n-vertex graph to minimize the discrepancy, i.e., the absolute difference between in- and out-degrees at any vertex. We present a deterministic algorithm with O(polylog n) discrepancy and O(polylog n) amortized recourse. The core ideas are to dynamically maintain an expander-decomposition with low recourse (using a very simple approach), and then to show that, as the expanders change over time, a natural local-search algorithm converges quickly (i.e., with low recourse) to a low-discrepancy solution. We also give strong lower bounds (with some matching upper bounds) for local-search discrepancy minimization algorithms for vector balancing and edge orientation. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.57"}, {"primary_key": "1764446", "vector": [], "sparse_vector": [], "title": "Approximate Hypergraph Vertex Cover and generalized Tuza&apos;s conjecture.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximate Hypergraph Vertex Cover and generalized <PERSON><PERSON>'s conjecture<PERSON><PERSON><PERSON><PERSON> and <PERSON>wami and <PERSON>pp.927 - 944Chapter DOI:https://doi.org/10.1137/1.*************.40PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A famous conjecture of <PERSON><PERSON> states that the minimum number of edges needed to cover all the triangles in a graph is at most twice the maximum number of edge-disjoint triangles. This conjecture was couched in a broader setting by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> who proposed a hypergraph version of this conjecture, and also studied its implied fractional versions. We establish the fractional version of the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> conjecture up to lower order terms. Specifically, we give a factor approximation based on LP rounding for an algorithmic version of the hypergraph Turán problem (AHTP). The objective in AHTP is to pick the smallest collection of (t–1)-sized subsets of vertices of an input t-uniform hypergraph such that every hyperedge contains one of these subsets. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> also posed whether <PERSON><PERSON>'s conjecture and its hypergraph versions could follow from non-trivial duality gaps between vertex covers and matchings on hypergraphs that exclude certain sub-hypergraphs, for instance, a \"tent\" structure that cannot occur in the incidence of triangles and edges. We give a strong negative answer to this question, by exhibiting tent-free hypergraphs, and indeed ℱ-free hypergraphs for any finite family ℱ of excluded sub-hypergraphs, whose vertex covers must include almost all the vertices. The algorithmic questions arising in the above study can be phrased as instances of vertex cover on simple hypergraphs, whose hyperedges can pairwise share at most one vertex. We prove that the trivial factor t approximation for vertex cover is hard to improve for simple t-uniform hypergraphs. However, for set cover on simple n-vertex hypergraphs, the greedy algorithm achieves a factor (ln n)/2, better than the optimal ln n factor for general hypergraphs. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.40"}, {"primary_key": "1764447", "vector": [], "sparse_vector": [], "title": "Deterministic algorithms for the Lovász Local Lemma: simpler, more general, and more parallel.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Deterministic algorithms for the Lovász Local Lemma: simpler, more general, and more parallelDavid G. HarrisDavid G. Harrispp.1744 - 1779Chapter DOI:https://doi.org/10.1137/1.*************.71PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The Lovász Local Lemma (LLL) is a keystone principle in probability theory, guaranteeing the existence of configurations which avoid a collection ℬ of \"bad\" events which are mostly independent and have low probability. In its simplest \"symmetric\" form, it asserts that whenever a bad-event has probability p and affects at most d bad-events, and epd < 1, then a configuration avoiding all ℬ exists. A seminal algorithm of Moser & Tardos (2010) (which we call the MT algorithm) gives nearly-automatic randomized algorithms for most constructions based on the LLL. However, deterministic algorithms have lagged behind. We address three specific shortcomings of the prior deterministic algorithms. First, our algorithm applies to the LLL criterion of Shearer (1985); this is more powerful than alternate LLL criteria and also removes a number of nuisance parameters and leads to cleaner and more legible bounds. Second, we provide parallel algorithms with much greater flexibility in the functional form of the bad-events. Third, we provide a derandomized version of the MT-distribution, that is, the distribution of the variables at the termination of the MT algorithm. We show applications to non-repetitive vertex coloring, independent transversals, strong coloring, and other problems. These give deterministic algorithms which essentially match the best previous randomized sequential and parallel algorithms. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.71"}, {"primary_key": "1764448", "vector": [], "sparse_vector": [], "title": "The Complexity of Average-Case Dynamic Subgraph Counting.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)The Complexity of Average-Case Dynamic Subgraph Counting<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.459 - 498Chapter DOI:https://doi.org/10.1137/1.*************.23PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Statistics of small subgraph counts such as triangles, four-cycles, and s-t paths of short lengths reveal important structural properties of the underlying graph. These problems have been widely studied in social network analysis. In most relevant applications, the graphs are not only massive but also change dynamically over time. Most of these problems become hard in the dynamic setting when considering the worst case. In this paper, we ask whether the question of small subgraph counting over dynamic graphs is hard also in the average case. We consider the simplest possible average case model where the updates follow an Erdős-Rényi graph: each update selects a pair of vertices (u, v) uniformly at random and flips the existence of the edge (u, v). We develop new lower bounds and matching algorithms in this model for counting four-cycles, counting triangles through a specified point s, or a random queried point, and st paths of length 3, 4 and 5. Our results indicate while computing st paths of length 3, and 4 are easy in the average case with O(1) update time (note that they are hard in the worst case), it becomes hard when considering st paths of length 5. We introduce new techniques which allow us to get average-case hardness for these graph problems from the worst-case hardness of the Online Matrix vector problem (OMv). Our techniques rely on recent advances in fine-grained average-case complexity. Our techniques advance this literature, giving the ability to prove new lower bounds on average-case dynamic algorithms. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.23"}, {"primary_key": "1764449", "vector": [], "sparse_vector": [], "title": "Preprocessing Imprecise Points for the Pareto Front.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Preprocessing Imprecise Points for the Pareto FrontI<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.3144 - 3167Chapter DOI:https://doi.org/10.1137/1.*************.122PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The preprocessing model for uncertain data models geometric imprecision of algorithmic input and provides a framework for working with it. In this model, we are given a set of regions ℛ which model the uncertainty associated with an unknown set of points P. There are two phases: a preprocessing phase, in which we have access only to ℛ, followed by a reconstruction phase, in which we have access to points in P, possibly at a certain retrieval cost C per point. For a given algorithmic problem, the goal in this model is to perform as much of the necessary computations as possible in the preprocessing phase, so that the amount of time spent in the reconstruction phase is minimized. In this paper, we investigate the following algorithmic question: how fast can we compute the Pareto front of P in the preprocessing model? We show that if ℛ is a set of pairwise-disjoint axis-aligned rectangles then we can preprocess ℛ to reconstruct the Pareto front of P efficiently. In contrast to earlier work in the preprocessing model, our solution achieves sublinear reconstruction time when the output complexity is sublinear. To refine our algorithmic analysis, we introduce a new notion of algorithmic optimality which relates to the entropy of the uncertainty regions. Our proposed uncertainty-region optimality falls on the spectrum between worst-case optimality and instance optimality. Our results are worst-case optimal, but we prove that instance optimality is unobtainable for a wide class of problems in the preprocessing model. We prove that, in fact, our results are uncertainty-region optimal with respect to real RAM instructions in the reconstruction phase. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.122"}, {"primary_key": "1764450", "vector": [], "sparse_vector": [], "title": "Algorithmic Thresholds for Refuting Random Polynomial Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Algorithmic Thresholds for Refuting Random Polynomial Systems<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON><PERSON> <PERSON>.1154 - 1203Chapter DOI:https://doi.org/10.1137/1.*************.49PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Consider a system of m polynomial equations {pi(x) = bi}i≤m of degree D ≥ 2 in n-dimensional variable x ∊ ℝn such that each coefficient of every pi and bis are chosen at random and independently from some continuous distribution. We study the basic question of determining the smallest m–the algorithmic threshold–for which efficient algorithms can find refutations (i.e. certificates of unsatisfiability) for such systems. This setting generalizes problems such as refuting random SAT instances, low-rank matrix sensing and certifying pseudo-randomness of <PERSON>reich's candidate generators and generalizations. We show that for every d ∊ ℕ, the (n + m)O(d)-time canonical sum-of-squares (SoS) relaxation refutes such a system with high probability whenever . We prove a lower bound in the restricted low-degree polynomial model of computation which suggests that this trade-off between SoS degree and the number of equations is nearly tight for all d. We also confirm the predictions of this lower bound in a limited setting by showing a lower bound on the canonical degree-4 sum-of-squares relaxation for refuting random quadratic polynomials. Together, our results provide evidence for an algorithmic threshold for the problem at -time algorithms for all δ. Our upper-bound relies on establishing a sharp bound on the smallest integer d such that degree d–D polynomial combinations of the input pis generate all degree-d polynomials in the ideal generated by the pis. Our lower bound actually holds for the easier problem of distinguishing random polynomial systems as above from a distribution on polynomial systems with a \"planted\" solution. Our choice of planted distribution is slightly (and necessarily) subtle: it turns out that the natural and well-studied planted distribution for quadratic systems (studied as the matrix sensing problem in machine learning) is easily distinguishable whenever m ≥ Õ(n)–a factor n smaller than the threshold in our upper bound above. Thus, our setting provides an example where refutation is harder than search in the natural planted model. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.49"}, {"primary_key": "1764451", "vector": [], "sparse_vector": [], "title": "On complete classes of valuated matroids.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "László A. <PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)On complete classes of valuated matroids<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Véghpp.945 - 962Chapter DOI:https://doi.org/10.1137/1.*************.41PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We characterize a rich class of valuated matroids, called R-minor valuated matroids that includes the indicator functions of matroids, and is closed under operations such as taking minors, duality, and induction by network. We exhibit a family of valuated matroids that are not R-minor based on sparse paving matroids. Valuated matroids are inherently related to gross substitute valuations in mathematical economics. By the same token we refute the Matroid Based Valuation Conjecture by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (Theoretical Economics 2015) asserting that every gross substitute valuation arises from weighted matroid rank functions by repeated applications of merge and endowment operations. Our result also has implications in the context of Lorentzian polynomials: it reveals the limitations of known construction operations. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.41"}, {"primary_key": "1764452", "vector": [], "sparse_vector": [], "title": "Frequency Estimation with One-Side<PERSON> Error.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Frequency Estimation with One-<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>pp.695 - 707Chapter DOI:https://doi.org/10.1137/1.*************.31PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Frequency estimation, also known as the Point Query problem, is one of the most fundamental problems in streaming algorithms. Given a stream S of elements from some universe U = {1 … n}, the goal is to compute, in a single pass, a short \"sketch\" of S so that for any element i ∊ U, one can estimate the number xi of times i occurs in S based on the sketch alone. Two state of the art solutions to this problems are Count-Min and Count-Sketch algorithms. They are based on linear sketches, which means that the data elements can be deleted as well as inserted and sketches for two different streams can be combined via addition. However, the guarantees offered by <PERSON>-<PERSON> and Count-Sketch are incomparable. The frequency estimator x produced by Count-Min sketch, using O(1/∊·log n) dimensions, guarantees that with high probability, and holds deterministically. Also, <PERSON><PERSON><PERSON> works under the assumption that x ≥ 0. On the other hand, <PERSON>-Sketch, using O(1/∊2 · log n) dimensions, guarantees that with high probability. A natural question is whether it is possible to design the \"best of both worlds\" sketching method, with error guarantees depending on the ℓ2 norm and space comparable to Count-Sketch, but (like Count-Min) also has the no-underestimation property. Our main set of results shows that the answer to the above question is negative. We show this in two incomparable computational models: linear sketching and streaming algorithms. Specifically, we show that: Any linear sketch satisfying the ℓp norm error guarantee with probability at least 2/3 and having the no-underestimation property must be of dimension of at least Ω(n1–1/p/∊), even if the sketched vectors are non-negative. This bound is tight, as we also give a linear sketch of dimension O(n1–1/p/∊) satisfying these properties. Any streaming algorithm satisfying the ℓp norm error guarantee with probability at least 2/3 and having the no-underestimation property must use at least Ω(n1–1/p/∊) bits. This holds even for algorithms that only allow insertions and make any constant number of passes over the stream. This bound is tight up to a logarithmic factor. We also study the complementary problem, where the sketch is required to not over-estimate, i.e., should hold always. We show that any linear sketch satisfying this property and having the ℓp error guarantee with probability at least 2/3 must be of dimension at least Ω(n1–1/p/∊). We also show that this bound is tight up to polylogarithmic factors, by providing an appropriate linear sketch. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.31"}, {"primary_key": "1764453", "vector": [], "sparse_vector": [], "title": "Monotone edge flips to an orientation of maximum edge-connectivity à <PERSON> Nash-Williams.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shun<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Monotone edge flips to an orientation of maximum edge-connectivity à la Nash-<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.1342 - 1355Chapter DOI:https://doi.org/10.1137/1.*************.56PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We initiate the study of k-edge-connected orientations of undirected graphs through edge flips for k ≥ 2. We prove that in every orientation of an undirected 2k-edge-connected graph, there exists a sequence of edges such that flipping their directions one by one does not decrease the edge-connectivity, and the final orientation is k-edge-connected. This yields an \"edge-flip based\" new proof of <PERSON><PERSON><PERSON>' theorem: an undirected graph G has a k-edge-connected orientation if and only if G is 2k-edge-connected. As another consequence of the theorem, we prove that the edge-flip graph of k-edge-connected orientations of an undirected graph G is connected if G is (2k + 2)-edge-connected. This has been known to be true only when k = 1. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.56"}, {"primary_key": "1764454", "vector": [], "sparse_vector": [], "title": "Approximation Schemes for Capacitated Vehicle Routing on Graphs of Bounded Treewidth, Bounded Doubling, or Highway Dimension.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximation Schemes for Capacitated Vehicle Routing on Graphs of Bounded Treewidth, Bounded Doubling, or Highway DimensionA<PERSON><PERSON> and <PERSON> and <PERSON>tipourpp.877 - 893Chapter DOI:https://doi.org/10.1137/1.*************.37PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper, we present Approximation Schemes for Capacitated Vehicle Routing Problem (CVRP) on several classes of graphs. In CVRP, introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON> in 1959 [13], we are given a graph G = (V, E) with metric edges costs, a depot r ∊ V, and a vehicle of bounded capacity Q. The goal is to find a minimum cost collection of tours for the vehicle that returns to the depot, each visiting at most Q nodes, such that they cover all the nodes. This generalizes classic TSP and has been studied extensively. In the more general setting, each node v has a demand dv and the total demand of each tour must be no more than Q. Either the demand of each node must be served by one tour (unsplittable) or can be served by multiple tours (splittable). The best known approximation algorithm for general graphs has ratio α + 2(1–∊) (for the unsplittable) and α + 1–∊ (for the splittable) for some fixed is the best approximation for TSP. Even for the case of trees, the best approximation ratio is 4/3 [5], and it has been an open question if there is an approximation scheme for this simple class of graphs. Das and Mathieu [14] presented an approximation scheme with time for Euclidean plane ℝ2. No other approximation scheme is known for any other class of metrics (without further restrictions on Q). In this paper, we make significant progress on this classic problem by presenting Quasi-Polynomial Time Approximation Schemes (QPTAS) for graphs of bounded treewidth, graphs of bounded highway dimensions, and graphs of bounded doubling dimensions. For comparison, our result implies an approximation scheme for Euclidean plane with run time . Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.37"}, {"primary_key": "1764455", "vector": [], "sparse_vector": [], "title": "Approximately counting independent sets in bipartite graphs via graph containers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximately counting independent sets in bipartite graphs via graph containers<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>pp.499 - 516Chapter DOI:https://doi.org/10.1137/1.*************.24PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract By implementing algorithmic versions of <PERSON><PERSON><PERSON><PERSON>'s graph container methods, we give new algorithms for approximating the number of independent sets in bipartite graphs. The first algorithm applies to d-regular, bipartite graphs satisfying a weak expansion condition: when d is constant, and the graph is a Ω(log2 d/d)-bipartite expander, we obtain an FPTAS for the number of independent sets. Previously such a result for d > 5 was known only for graphs satisfying the much stronger expansion conditions of random graphs. The second algorithm applies to all d-regular, bipartite graphs, runs in time exp , and outputs a (1 + o(1))-approximation to the number of independent sets. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.24"}, {"primary_key": "1764456", "vector": [], "sparse_vector": [], "title": "Tight Guarantees for Multi-unit Prophet Inequalities and Online Stochastic Knapsack.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Tight Guarantees for Multi-unit Prophet Inequalities and Online Stochastic Knapsack<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.1221 - 1246Chapter DOI:https://doi.org/10.1137/1.*************.51PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Prophet inequalities are a useful tool for designing online allocation procedures and comparing their performance to the optimal offline allocation. In the basic setting of k-unit prophet inequalities, the procedure of Alaei [2] with its celebrated performance guarantee of has found widespread adoption in mechanism design and general online allocation problems in online advertising, healthcare scheduling, and revenue management. Despite being commonly used for implementing a fractional allocation in an online fashion, the tightness of <PERSON><PERSON><PERSON>'s procedure for a given k has remained unknown. In this paper we resolve this question, characterizing the tight bound by identifying the structure of the optimal online implementation, and consequently improving the best-known guarantee for k-unit prophet inequalities for all k > 1. We also consider the more general online stochastic knapsack problem where each individual allocation can consume an arbitrary fraction of the initial capacity. Here we introduce a new \"best-fit\" procedure for implementing a fractionally-feasible knapsack solution online, with a performance guarantee of ≈ 0.319, which we also show is tight with respect to the standard LP relaxation. This improves the previously best-known guarantee of 0.2 for online knapsack. Our analysis differs from existing ones by eschewing the need to split items into \"large\" or \"small\" based on capacity consumption, using instead an invariant for the overall utilization on different sample paths. All in all, our results imply tight (non-greedy) Online Contention Resolution Schemes for k-uniform matroids and the knapsack polytope, respectively, which has further implications. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.51"}, {"primary_key": "1764457", "vector": [], "sparse_vector": [], "title": "A Sublinear Bound on the Page Number of Upward Planar Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The page number of a directed acyclic graph $G$ is the minimum $k$ for which there is a topological ordering of $G$ and a $k$-coloring of the edges such that no two edges of the same color cross, i.e., have alternating endpoints along the topological ordering. We address the long-standing open problem asking for the largest page number among all upward planar graphs. We improve the best known lower bound to $5$ and present the first asymptotic improvement over the trivial $O(n)$ upper bound, where $n$ denotes the number of vertices in $G$. Specifically, we first prove that the page number of every upward planar graph is bounded in terms of its width, as well as its height. We then combine both approaches to show that every $n$-vertex upward planar graph has page number $O(n^{2/3} \\log(n)^{2/3})$.", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.42"}, {"primary_key": "1764458", "vector": [], "sparse_vector": [], "title": "Algorithmic trade-offs for girth approximation in undirected graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Virginia Vassilevska Williams", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Algorithmic trade-offs for girth approximation in undirected graphs<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>.1471 - 1492Chapter DOI:https://doi.org/10.1137/1.*************.62PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present several new efficient algorithms for approximating the girth, g, of weighted and unweighted n-vertex, m-edge undirected graphs. For undirected graphs with polynomially bounded, integer, non-negative edge weights, we provide an algorithm that for every integer k ≥ 1, runs in Õ(m + n1 + 1/k log g) time and returns a cycle of length at most 2kg. For unweighted, undirected graphs we present an algorithm that for every k ≥ 1, runs in Õ(n1 + 1/k) time and returns a cycle of length at most 2k[g/2], an almost k-approximation. Both algorithms provide trade-offs between the running time and the quality of the approximation. We also obtain faster algorithms for approximation factors better than 2, and improved approximations when the girth is odd or small (e.g., 3 and 4). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.62"}, {"primary_key": "1764459", "vector": [], "sparse_vector": [], "title": "Simulating Random Walks in Random Streams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Simulating Random Walks in Random Streams<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.3091 - 3126<PERSON>hapter DOI:https://doi.org/10.1137/1.*************.120PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The random order graph streaming model has received significant attention recently, with problems such as matching size estimation, component counting, and the evaluation of bounded degree constant query testable properties shown to admit surprisingly space efficient algorithms. The main result of this paper is a space efficient single pass random order streaming algorithm for simulating nearly independent random walks that start at uniformly random vertices. We show that the distribution of k-step walks from b vertices chosen uniformly at random can be approximated up to error ∊ per walk using words of space with a single pass over a randomly ordered stream of edges, solving an open problem of <PERSON><PERSON> and <PERSON>hler [SODA '18]. Applications of our result include the estimation of the average return probability of the k-step walk (the trace of the kth power of the random walk matrix) as well as the estimation of PageRank. We complement our algorithm with a strong impossibility result for directed graphs. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.120"}, {"primary_key": "1764460", "vector": [], "sparse_vector": [], "title": "Fixed-Price Approximations in Bilateral Trade.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Fixed-Price Approximations in Bilateral Trade<PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.2964 - 2985Chapter DOI:https://doi.org/10.1137/1.*************.115PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the bilateral trade problem, in which two agents trade a single indivisible item. It is known that the only dominant-strategy truthful mechanism is the fixed-price mechanism: given commonly known distributions of the buyer's value B and the seller's value S, a price p is offered to both agents and trade occurs if S ≤ p ≤ B. The objective is to maximize either expected welfare or expected gains from trade . We improve the approximation ratios for several welfare maximization variants of this problem. When the agents' distributions are identical, we show that the optimal approximation ratio for welfare is . With just one prior sample from the common distribution, we show that a 3/4-approximation to welfare is achievable. When agents' distributions are not required to be identical, we show that a previously best-known (1–1/e)-approximation can be strictly improved, but 1–1/e is optimal if only the seller's distribution is known. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.115"}, {"primary_key": "1764461", "vector": [], "sparse_vector": [], "title": "Online Weighted Matching with a Sample.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Online Weighted Matching with a <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.1247 - 1272Chapter DOI:https://doi.org/10.1137/1.*************.52PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the greedy-based online algorithm for edge-weighted matching with (one-sided) vertex arrivals in bipartite graphs, and edge arrivals in general graphs. This algorithm was first studied more than a decade ago by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> for the bipartite case in the random-order model. While the weighted bipartite matching problem is solved in the random-order model, this is not the case in recent and exciting online models in which the online player is provided with a sample, and the arrival order is adversarial. The greedy-based algorithm is arguably the most natural and practical algorithm to be applied in these models. Despite its simplicity and appeal, and despite being studied in multiple works, the greedy-based algorithm was not fully understood in any of the studied online models, and its actual performance remained an open question for more than a decade. We provide a thorough analysis of the greedy-based algorithm in several online models. For vertex arrivals in bipartite graphs, we characterize the exact competitive-ratio of this algorithm in the random-order model, for any arrival order of the vertices subsequent to the sampling phase (adversarial and random orders in particular). We use it to derive tight analysis in the recent adversarial-order model with a sample (AOS model) for any sample size, providing the first result in this model beyond the simple secretary problem. Then, we generalize and strengthen the black box method of converting results in the random-order model to single-sample prophet inequalities, and use it to derive the state-of-the-art single-sample prophet inequality for the problem. Finally, we use our new techniques to analyze the greedy-based algorithm for edge arrivals in general graphs and derive results in all the mentioned online models. In this case as well, we improve upon the state-of-the-art single-sample prophet inequality. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.52"}, {"primary_key": "1764462", "vector": [], "sparse_vector": [], "title": "Simulating a stack using queues.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Simulating a stack using que<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>.1901 - 1924Chapter DOI:https://doi.org/10.1137/1.*************.76PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract It is well known that a queue can be simulated by two stacks using a constant number of stack operations per queue operation. In this paper we consider the forgotten converse problem of simulating a stack using several queues. We consider several variants of this problem. For the offline variant, we obtain a tight upper and lower bounds for the worst-case number of queue operations needed to simulate a sequence of n stack operations using k queues. For the online variant, when the number of queues k is constant, and n is the maximum number of items in the stack at any given time, we obtain tight Θ(n1/k) upper and lower bounds on the worst-case and amortized number of queue operations needed to simulate one stack operation. When k is allowed to grow with n, we prove an upper bound of O(n1/k + logk n) and a lower bound of on the amortized number of queue operations per stack operation. We also prove an upper bound of O(kn1/k) and a lower bound of Ω(n1/k + logk n) on the worst-case number of queue operations per stack operation. We also show that the specific but interesting sequence of n pushes followed by n pops can be implemented much faster using a total number of only Θ(n logk n) queue operations, for every k ≥ 2, an amortized number of Θ(logk n) queue operations per stack operation, and this bound is tight. On the other hand, we show that the same sequence requires at least Ω(n1/k) queue operations per stack operation in the worst case. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.76"}, {"primary_key": "1764463", "vector": [], "sparse_vector": [], "title": "Improved Strongly Polynomial Algorithms for Deterministic MDPs, 2VPI Feasibility, and Discounted All-Pairs Shortest Paths.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Improved Strongly Polynomial Algorithms for Deterministic MDPs, 2VPI Feasibility, and Discounted All-Pairs Shortest PathsAdam KarczmarzAdam Karczmarzpp.154 - 172Chapter DOI:https://doi.org/10.1137/1.*************.8PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We revisit the problem of finding optimal strategies for deterministic Markov Decision Processes (DMDPs), and a closely related problem of testing feasibility of systems of m linear inequalities on n real variables with at most two variables per inequality (2VPI). We give a randomized trade-off algorithm solving both problems and running in Õ(nmh + (n/h)3) time using Õ(n2/h + m) space for any parameter h ∊ [1,n]. In particular, using subquadratic space we get Õ(nm + n3/2m3/4) running time, which improves by a polynomial factor upon all the known upper bounds for non-dense instances with m = O(n2–∊). Moreover, using linear space we match the randomized Õ(nm + n3) time bound of <PERSON> and <PERSON><PERSON><PERSON> [SICOMP'94] that required space. Additionally, we show a new algorithm for the Discounted All-Pairs Shortest Paths problem, introduced by Madani et al. [TALG'10], that extends the DMDPs with optional end vertices. For the case of uniform discount factors, we give a deterministic algorithm running in Õ(n3/2m3/4) time, which improves significantly upon the randomized bound of Madani et al. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.8"}, {"primary_key": "1764464", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> and <PERSON> from ℓ∞-Independence.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Scalar and Matrix Chernoff Bounds from ℓ∞-<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON>.3732 - 3753<PERSON>hapter DOI:https://doi.org/10.1137/1.*************.147PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present new scalar and matrix Chernoff-style concentration bounds for a broad class of probability distributions over the binary hypercube {0, 1}n. Motivated by recent tools developed for the study of mixing times of Markov chains on discrete distributions, we say that a distribution is ℓ∞-independent when the infinity norm of its influence matrix is bounded by a constant. We show that any distribution which is ℓ∞-infinity independent satisfies a matrix Chernoff bound that matches the matrix Chernoff bound for independent random variables due to <PERSON>ropp. Our matrix Chernoff bound is a broad generalization and strengthening of the matrix Chernoff bound of <PERSON>yn<PERSON> and <PERSON> (FOCS'18). Using our bound, we can conclude as a corollary that a union of O(log |V|) random spanning trees gives a spectral graph sparsifier of a graph with |V| vertices with high probability matching results for independent edge sampling, and matching lower bounds from <PERSON><PERSON><PERSON> and <PERSON>. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.147"}, {"primary_key": "1764465", "vector": [], "sparse_vector": [], "title": "The popular assignment problem: when cardinality is more important than popularity.", "authors": ["Telike<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)The popular assignment problem: when cardinality is more important than popularity<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>.103 - 123Chapter DOI:https://doi.org/10.1137/1.*************.6PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider a matching problem in a bipartite graph G = (A∪B, E) where each node in A is an agent having preferences in partial order over her neighbors, while nodes in B are objects with no preferences. The size of our matching is more important than node preferences–thus, we are interested in maximum matchings only. Any pair of maximum matchings in G (equivalently, perfect matchings or assignments) can be compared by holding a head-to-head election between them where agents are voters. The goal is to compute an assignment such that there is no better or \"more popular\" assignment. This is the popular assignment problem and it generalizes the well-studied popular matching problem (<PERSON> et al., 2007). Popular assignments need not exist in every input instance. We show a polynomial-time algorithm that decides if the given instance admits one or not, and computes one, if so. In instances with no popular assignment, we consider the problem of finding an almost popular assignment, i.e., an assignment with minimum unpopularity margin. We show an O∗ (|E|k) time algorithm for deciding if there exists an assignment with unpopularity margin at most k. We then show that this algorithm is essentially optimal by proving that the problem is NP-complete and Wl[1]-hard with parameter k. We also consider the minimum-cost popular assignment problem when there are edge costs, and show this problem to be NP-hard. This hardness holds even when all edge costs are in {0,1} and agents have strict preferences. By contrast, we propose a polynomial-time algorithm to the problem of deciding if there exists a popular assignment with a given set of forced/forbidden edges (this tractability holds even for partially ordered preferences). Our algorithms are combinatorial and based on LP duality. They search for an appropriate witness or dual certificate, and when a certificate cannot be found, we prove that the desired assignment does not exist in G. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.6"}, {"primary_key": "1764466", "vector": [], "sparse_vector": [], "title": "An Upper Bound and Linear-Space Queries on the LZ-End Parsing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)An Upper Bound and Linear-Space Queries on the LZ-End ParsingDominik Kempa and Barna SahaDomi<PERSON> and <PERSON><PERSON>.2847 - 2866Chapter DOI:https://doi.org/10.1137/1.*************.111PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Lempel–Ziv (LZ77) compression is the most commonly used lossless compression algorithm. The basic idea is to greedily break the input string into blocks (called \"phrases\"), every time forming as a phrase the longest prefix of the unprocessed part that has an earlier occurrence. In 2010, Kreft and Navarro introduced a variant of LZ77 called LZ-End, that additionally requires the previous occurrence of each phrase to end at the boundary of an already existing phrase. Due to its excellent practical performance as a compression algorithm and a compressed index, they conjectured that it achieves a compression that can be provably upper-bounded in terms of the LZ77 size. Despite the recent progress in understanding such relation for other compression algorithms (e.g., the run-length encoded <PERSON><PERSON>–<PERSON> transform), no such result is known for LZ-End. We prove that for any string of length n, the number ze of phrases in the LZ-End parsing satisfies , where z is the number of phrases in the LZ77 parsing. This puts LZ-End among the strongest dictionary compressors and solves a decade-old open problem of Kreft and Navarro. Using our techniques we also derive bounds for other variants of LZ-End and with respect to other compression measures. Our second contribution is a data structure that implements random access queries to the text in space and time. This is the first linear-size structure on LZ-End that efficiently implements such queries. All previous data structures either incur a logarithmic penalty in the space or have slow queries. We also show how to extend these techniques to support longest-common-extension (LCE) queries. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.111"}, {"primary_key": "1764467", "vector": [], "sparse_vector": [], "title": "On the Fine-Grained Complexity of the Unbounded SubsetSum and the Frobenius Problem.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)On the Fine-Grained Complexity of the Unbounded SubsetSum and the Frobenius ProblemKim-<PERSON>pp.3567 - 3582Chapter DOI:https://doi.org/10.1137/1.*************.141PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Consider positive integral solutions to the equation a0x0 + … + anxn = t. In the so called unbounded subset sum problem, the objective is to decide whether such a solution exists, whereas in the Frobenius problem, the objective is to compute the largest t such that there is no such solution. In this paper we study the algorithmic complexity of the unbounded subset sum, the Frobenius problem and a generalization of both problems. More precisely, we study pseudo-polynomial time algorithms with a running time that depends on the smallest number a0 or respectively the largest number an. For the parameter a0, we show that all considered problems are subquadratically equivalent to (min, +)-convolution, a fundamental algorithmic problem from the area of fine-grained complexity. By this equivalence, we obtain hardness results for the considered problems (based on the assumption that an algorithm with a subquadratic running time for (min, +)-convolution does not exist) as well as algorithms with improved running time. The proof for the equivalence makes use of structural properties of solutions, a technique that was developed in the area of integer programming. In case of the complexity of the problems parameterized by an, we present improved algorithms. For example we give a quasi linear time algorithm for the Frobenius problem as well as a hardness result based on the strong exponential time hypothesis. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.141"}, {"primary_key": "1764468", "vector": [], "sparse_vector": [], "title": "Collapsing the Tower - On the Complexity of Multistage Stochastic IPs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Collapsing the Tower - On the Complexity of Multistage Stochastic IPsK<PERSON>-<PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON>pp.348 - 358Chapter DOI:https://doi.org/10.1137/1.*************.17PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper we study the computational complexity of solving a class of block structured integer programs (IPs) - so called multistage stochastic IPs. A multistage stochastic IP is an IP of the form where the constraint matrix consists of small block matrices ordered on the diagonal line and for each stage there are larger blocks with few columns connecting the blocks in a tree like fashion. Over the last years there was enormous progress in the area of block structured IPs. For many of the known block IP classes - such as n-fold, tree-fold, and two-stage stochastic IPs, nearly matching upper and lower bounds are known concerning their computational complexity. One of the major gaps that remained however was the parameter dependency in the running time for an algorithm solving multistage stochastic IPs. Previous algorithms require a tower of t exponentials, where t is the number of stages, while only a double exponential lower bound was known. In this paper we show that the tower of t exponentials is actually not necessary. We can show an improved running time for the algorithm solving multistage stochastic IPs with a running time of , where d is the sum of columns in the connecting blocks and n is the number of blocks on the lowest stage. Hence, we obtain the first bound by an elementary function for the running time of an algorithm solving multistage stochastic IPs. In contrast to previous works, our algorithm has only a triple exponential dependency on the parameters and only doubly exponential for every constant t. By this we come very close the known double exponential bound (based on the exponential time hypothesis) that holds already for two-stage stochastic IPs, i.e. multistage stochastic IPs with only two stages. The improved running time of the algorithm is based on new bounds for the proximity of multistage stochastic IPs. The idea behind the bound is based on generalization for a structural lemma originally used for two-stage stochastic IPs. While the structural lemma requires iteration to be applied to multistage stochastic IPs, our generalization directly applies to inherent combinatorial properties of multiple stages. Already a special case of our lemma yields an improved bound for the Graver Complexity of multistage stochastic IPs. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.17"}, {"primary_key": "1764469", "vector": [], "sparse_vector": [], "title": "Competitive Strategies for Symmetric Rendezvous on the Line.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Competitive Strategies for Symmetric Rendezvous on the LineMax Klimm, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>.329 - 347Chapter DOI:https://doi.org/10.1137/1.*************.16PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the Symmetric Rendezvous Search on the Line with Unknown Initial Distance, two identical agents are placed on the real line with their distance, the other's location, and their orientation unknown to them. Moving along the line at unit speed and executing the same randomized search strategy, the agents' goal is to meet up as early as possible. The expected meeting time obviously depends on the unknown initial distance and orientations. The quality of a randomized search strategy is thus measured by its competitive ratio, that is, the ratio of the expected meeting time and the earliest possible meeting time (half the initial distance). We present a class of successively refined randomized search strategies together with a rigorous mathematical analysis of their continuously improved competitive ratios. These strategies all rely on the basic idea of performing an infinite sequence of steps of geometrically increasing size in random directions, always returning to the agent's initial position before starting the next step. In addition, our more refined strategies use two novel ideas. First, remembering their past random choices, the agents randomly choose the direction of the next step in a <PERSON>ov-chain-like manner. Second, choosing the next few random directions in advance, each agent may combine consecutive steps in the same direction into one longer step. As our main result, we show that this combination of looking into the past as well as into the future leads to a substantially improved competitive ratio of 13.93 compared to the previously best known bound of 24.85 (Ozsoyeller et al. 2013). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.16"}, {"primary_key": "1764470", "vector": [], "sparse_vector": [], "title": "New Diameter-Reducing Shortcuts and Directed Hopsets: Breaking the Barrier.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)New Diameter-Reducing Shortcuts and Directed Hopsets: Breaking the BarrierShimon <PERSON> and Merav Parter<PERSON> and Merav Parterpp.1326 - 1341Chapter DOI:https://doi.org/10.1137/1.*************.55PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract For an n-vertex digraph G = (V, E), a shortcut set is a (small) subset of edges H taken from the transitive closure of G that, when added to G guarantees that the diameter of G ∪ H is small. Shortcut sets, introduced by <PERSON><PERSON> in 1993, have a wide range of applications in algorithm design, especially in the context of parallel, distributed and dynamic computation on directed graphs. A folklore result in this context shows that every n-vertex digraph admits a shortcut set of linear size (i.e., of O(n) edges) that reduces the diameter to1 . Despite extensive research over the years, the question of whether one can reduce the diameter to with Õ(n) shortcut edges has been left open. We provide the first improved diameter-sparsity tradeoff for this problem, breaking the diameter barrier. Specifically, we show an O(nω)-time randomized algorithm2 for computing a linear shortcut set that reduces the diameter of the digraph to Õ(n1/3). This narrows the gap w.r.t the current diameter lower bound of Ω(n1/6) by [Huang and Pettie, SWAT'18]. Moreover, we show that a diameter of O(n1/2) can in fact be achieved with a sublinear number of O(n3/4) shortcut edges. Formally, letting S(n, D) be the bound on the size of the shortcut set required in order to reduce the diameter of any n-vertex digraph to at most D, our algorithms yield: We also extend our algorithms to provide improved (β, ∊) hopsets for n-vertex weighted directed graphs. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.55"}, {"primary_key": "1764471", "vector": [], "sparse_vector": [], "title": "Average Sensitivity of Dynamic Programming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Average Sensitivity of Dynamic Programming<PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.1925 - 1961Chapter DOI:https://doi.org/10.1137/1.*************.77PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract When processing data with uncertainty, it is desirable that the output of the algorithm is stable against small perturbations in the input. <PERSON><PERSON> and <PERSON><PERSON><PERSON> [SODA'21] recently formalized this idea and proposed the notion of average sensitivity of algorithms, which is roughly speaking, the average Hamming distance between solutions for the original input and that obtained by deleting one element from the input, where the average is taken over the deleted element. In this work, we consider average sensitivity of algorithms for problems that can be solved by dynamic programming. We first present a (1–δ)-approximation algorithm for finding a maximum weight chain (MWC) in a transitive directed acyclic graph with average sensitivity O(δ–1 log3 n), where n is the number of vertices in the graph. We then show algorithms with small average sensitivity for various dynamic programming problems by reducing them to the MWC problem while preserving average sensitivity, including the longest increasing subsequence problem, the interval scheduling problem, the longest common subsequence problem, the longest palindromic subsequence problem, the knapsack problem with integral weight, and the RNA folding problem. For the RNA folding problem, our reduction is highly nontrivial because a naive reduction generates an exponentially large graph, which only provides a trivial average sensitivity bound. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.77"}, {"primary_key": "1764472", "vector": [], "sparse_vector": [], "title": "Strong recovery of geometric planted matchings.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Strong recovery of geometric planted matchings<PERSON><PERSON><PERSON><PERSON> and <PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>-Weedpp.834 - 876Chapter DOI:https://doi.org/10.1137/1.*************.36PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the problem of efficiently recovering the matching between an unlabelled collection of n points in ℝd and a small random perturbation of those points. We consider a model where the initial points are i.i.d. standard Gaussian vectors, perturbed by adding i.i.d. Gaussian vectors with covariance σ2Id. In this setting, the maximum likelihood estimator (MLE) can be found in polynomial time as the solution of a linear assignment problem. We establish thresholds on σ2 for the MLE to perfectly recover the planted matching (making no errors) and to strongly recover the planted matching (making o(n) errors) both for d constant and d = d(n) growing arbitrarily. Between these two thresholds, we show that the MLE makes nδ+o(1) errors for an explicit δ ∊ (0, 1). These results extend a recent line of work on recovering matchings planted in random graphs with independently-weighted edges to the geometric setting. Our proof techniques rely on careful analysis of the combinatorial structure of partial matchings in large, weakly dependent random graphs using the first and second moment methods. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.36"}, {"primary_key": "1764473", "vector": [], "sparse_vector": [], "title": "Polygon Placement Revisited: (Degree of Freedom + 1)-SUM Hardness and an Improvement via Offline Dynamic Rectangle Union.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Polygon Placement Revisited: (Degree of Freedom + 1)-SUM Hardness and an Improvement via Offline Dynamic Rectangle <PERSON><PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.3181 - 3201Chapter DOI:https://doi.org/10.1137/1.*************.124PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We revisit a classical problem in computational geometry: Determine the largest copy of a simple polygon P that can be placed into the simple polygon Q. Despite significant effort studying a number of settings, known algorithms require high polynomial running times, even for the interesting case when either P or Q have constant size. (<PERSON><PERSON><PERSON> and <PERSON><PERSON>, 2001) give a conditional lower bound of n2–o(1) under the 3SUM conjecture when P and Q are (convex) polygons with Θ(n) vertices each. This leaves open whether we can establish (1) hardness beyond quadratic time and (2) any superlinear bound for constant-sized P or Q. In this paper, we affirmatively answer these questions under the higher-order kSUM conjecture, proving natural hardness results that increase with each degree of freedom (scaling, x-translation, y-translation, rotation): (scaling, x-translation:) Finding the largest copy of P that can be x-translated into Q requires time n2–o(1) under the 3SUM conjecture, even for orthogonal (rectilinear) polygons P, Q with O(1) and n vertices, respectively. (scaling, x-translation, y-translation:) Finding the largest copy of P that can be arbitrarily translatedinto Q requires time n2–o(1) under the 4SUM conjecture, even for orthogonal polygons P, Q with O(1) and n vertices, respectively. This establishes the same lower bound under the assumption that Subset Sum cannot be solved in time O((2–∊)n/2) for any ∊ > 0. The above lower bounds are almost tight when one of the polygons is of constant size: Using an offline dynamic algorithm for maintaining the area of a union of rectangles due to Overmars and Yap, we obtain an -time algorithm for orthogonal polygons P, Q with p and q vertices, respectively. This matches the lower bounds up to an n1/2+o(1)-factor when P, Q have O(1) and n vertices. (scaling, x-translation, y-translation, rotation:) Finally, finding the largest copy of P that can be arbitrarily rotated and translated into Q requires time n3–o(1) under the 5SUM conjecture. As in our reductions, each degree of freedom determines one summand in a kSUM instance, these lower bounds appear likely to be best possible under kSUM. We are not aware of any other such natural (degree of freedom + 1)-SUM hardness for a geometric optimization problem. Finally, we prove an additional tight OV hardness of the translations-only case. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.124"}, {"primary_key": "1764474", "vector": [], "sparse_vector": [], "title": "Incremental SSSP for Sparse Digraphs Beyond the Hopset Barrier.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Incremental SSSP for Sparse Digraphs Beyond the Hopset Barrier<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>pp.3452 - 3481<PERSON><PERSON><PERSON><PERSON> DOI:https://doi.org/10.1137/1.*************.137PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given a directed, weighted graph G = (V, E) undergoing edge insertions, the incremental single-source shortest paths (SSSP) problem asks for the maintenance of approximate distances from a dedicated source s while optimizing the total time required to process the insertion sequence of m edges. Recently, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> [STOC'20] introduced a deterministic Õ(n2) algorithm for this problem, achieving near linear time for very dense graphs. For sparse graphs, <PERSON><PERSON><PERSON> and <PERSON> [SODA'21] recently presented a deterministic Õ(m5/3) algorithm, and an adaptive randomized algorithm with run-time . This algorithm is remarkable for two reasons: 1) in very spare graphs it reaches the directed hopset barrier of Ω(n3/2) that applied to all previous approaches for partially-dynamic SSSP [STOC'14, SODA'20, FOC<PERSON>'20] and 2) it does not resort to a directed hopset technique itself. In this article we introduce propagation synchronization, a new technique for controlling the error build-up on paths throughout batches of insertions. This leads us to a significant improvement of the approach in [SODA'21] yielding a deterministic O(m3/2) algorithm for the problem. By a very careful combination of our new technique with the sampling approach from [SODA'21], we further obtain an adaptive randomized algorithm with total update time Õ(m4/3). This is the first partially-dynamic SSSP algorithm in sparse graphs to bypass the notorious directed hopset barrier which is often seen as the fundamental challenge towards achieving truly near-linear time algorithms. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.137"}, {"primary_key": "1764475", "vector": [], "sparse_vector": [], "title": "Recognizing &lt;italic&gt;k&lt;/italic&gt;-leaf powers in polynomial time, for constant &lt;italic&gt;k&lt;/italic&gt;.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Recognizing k-leaf powers in polynomial time, for constant kManuel LafondManuel Lafondpp.1384 - 1410Chapter DOI:https://doi.org/10.1137/1.*************.58PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A graph G is a k-leaf power if there exists a tree T whose leaf set is V (G), and such that uv ∊ E(G) if and only if the distance between u and v in T is at most k. The graph classes of k-leaf powers have several applications in computational biology, but recognizing them has remained a challenging algorithmic problem for the past two decades. The best known result is that 6-leaf powers can be recognized in polynomial time. In this paper, we present an algorithm that decides whether a graph G is a k-leaf power in time O(nf(k)) for some function f that depends only on k (but has the growth rate of a power tower function). Our techniques are based on the fact that either a k-leaf power has a corresponding tree of low maximum degree, in which case finding it is easy, or every corresponding tree has large maximum degree. In the latter case, large degree vertices in the tree imply that G has redundant substructures which can be pruned from the graph. In addition to solving a longstanding open problem, we hope that the structural results presented in this work can lead to further results on k-leaf powers. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.58"}, {"primary_key": "1764476", "vector": [], "sparse_vector": [], "title": "Near-Optimal Spanners for General Graphs in (Nearly) Linear Time.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Near-Optimal Spanners for General Graphs in (Nearly) Linear TimeHung Le and Shay <PERSON> and <PERSON>pp.3332 - 3361Chapter DOI:https://doi.org/10.1137/1.*************.132PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Let G = (V, E, w) be a weighted undirected graph on |V| = n vertices and |E| = m edges, let k ≥ 1 be any integer, and let ∊ < 1 be any parameter. We present the following results on fast constructions of spanners with near-optimal sparsity and lightness,1 which culminate a long line of work in this area. (By near-optimal we mean optimal under <PERSON><PERSON><PERSON>' girth conjecture and disregarding the ∊-dependencies.) There are (deterministic) algorithms for constructing (2k–1)(1 + ∊)-spanners for G with a near-optimal sparsity of O(n1/k · log(1/∊)/∊)). The first algorithm can be implemented in the pointer-machine model within time O(mα(m, n) · log(1/∊)/∊)+ SORT(m)), where α(·,·) is the two-parameter inverse-Ackermann function and SORT(m) is the time needed to sort m integers. The second algorithm can be implemented in the Word RAM model within time O(m log(1/∊)/∊)). There is a (deterministic) algorithm for constructing a (2k–1)(1 + ∊)-spanner for G that achieves a near-optimal bound of O(n1/k ·poly(1/∊)) on both sparsity and lightness. This algorithm can be implemented in the pointer-machine model within time O(mα(m,n) · poly(1/∊) + SORT(m)) and in the Word RAM model within time O(mα(m,n) · poly(1/∊)). The previous fastest constructions of (2k–1)(1 + ∊)-spanners with near-optimal sparsity incur a runtime of is O(min{m(n1+1/k) + n log n, k · n2+1/k}), even regardless of the lightness. Importantly, the greedy spanner for stretch 2k–1 has sparsity O(n1/k) — with no ∊-dependence whatsoever, but its runtime is O(m(n1+1/k + n log n)). Moreover, the state-of-the-art lightness bound of any (2k–1)-spanner (including the greedy spanner) is poor, even regardless of the sparsity and runtime. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.132"}, {"primary_key": "1764477", "vector": [], "sparse_vector": [], "title": "Greedy Spanners in Euclidean Spaces Admit Sublinear Separators.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Greedy Spanners in Euclidean Spaces Admit Sublinear SeparatorsHung Le and Cuong ThanHung Le and Cuong Thanpp.3287 - 3310Chapter DOI:https://doi.org/10.1137/1.*************.130PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The greedy spanner in a low dimensional Euclidean space is a fundamental geometric construction that has been extensively studied over three decades as it possesses the two most basic properties of a good spanner: constant maximum degree and constant lightness. Recently, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> [<PERSON><PERSON>21] showed that the greedy spanner in ℝ2 admits a sublinear separator in a strong sense: any subgraph of k vertices of the greedy spanner in ℝ2 has a separator of size . Their technique is inherently planar and is not extensible to higher dimensions. They left showing the existence of a small separator for the greedy spanner in ℝd for any constant d ≥ 3 as an open problem. In this paper, we resolve the problem of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [<PERSON>K21] by showing that any subgraph of k vertices of the greedy spanner in ℝd has a separator of size O(k1–1/d). We introduce a new technique that gives a simple criterion for any geometric graph to have a sublinear separator that we dub τ-lanky: a geometric graph is τ-lanky if any ball of radius r cuts at most τ edges of length at least r in the graph. We show that any τ-lanky geometric graph of n vertices in ℝd has a separator of size O(τn1–1/d). We then derive our main result by showing that the greedy spanner is O(1)-lanky. We indeed obtain a more general result that applies to unit ball graphs and point sets of low fractal dimensions in ℝd. Our technique naturally extends to doubling metrics. We use the τ-lanky criterion to show that there exists a (1 + ∊)-spanner for doubling metrics of dimension d with a constant maximum degree and a separator of size ; this result resolves an open problem posed by Abam and Har-Peled [AHP10] a decade ago. We then introduce another simple criterion for a graph in doubling metrics of dimension d to have a sublinear separator. We use the new criterion to show that the greedy spanner of an n-point metric space of doubling dimension d has a separator of size where Δ is the spread of the metric; the factor log(Δ) is tightly connected to the fact that, unlike its Euclidean counterpart, the greedy spanner in doubling metrics has unbounded maximum degree. Finally, we discuss algorithmic implications of our results. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.130"}, {"primary_key": "1764478", "vector": [], "sparse_vector": [], "title": "Multi-token Markov Game with Switching Costs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Multi-token Markov Game with Switching Co<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.1780 - 1807Chapter DOI:https://doi.org/10.1137/1.*************.72PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study a general Markov game with metric switching costs: in each round, the player adaptively chooses one of several Markov chains to advance with the objective of minimizing the expected cost for at least k chains to reach their target states. If the player decides to play a different chain, an additional switching cost is incurred. The special case in which there is no switching cost was solved optimally by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> [DTW03] by a variant of the celebrated Gittins Index for the classical multi-armed bandit (MAB) problem with Markovian rewards [Git74, Git79]. However, for Markovian multi-armed bandit with nontrivial switching cost, even if the switching cost is a constant, the classic paper by <PERSON> and <PERSON><PERSON><PERSON> [BS94] showed that no index strategy can be optimal.1 In this paper, we complement their result and show there is a simple index strategy that achieves a constant approximation factor if the switching cost is constant and k = 1. To the best of our knowledge, this index strategy is the first strategy that achieves a constant approximation factor for a general Markovian MAB variant with switching costs. For the general metric, we propose a more involved constant-factor approximation algorithm, via a nontrivial reduction to the stochastic k-TSP problem, in which a Markov chain is approximated by a random variable. Our analysis makes extensive use of various interesting properties of the Gittins index. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.72"}, {"primary_key": "1764479", "vector": [], "sparse_vector": [], "title": "Polynomial Integrality Gap of Flow LP for Directed Steiner Tree.", "authors": ["<PERSON>", "Bundit Laekhanukit"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Polynomial Integrality Gap of Flow LP for Directed Steiner TreeShi Li and Bundit LaekhanukitShi Li and Bundit Laekhanukitpp.3230 - 3236Chapter DOI:https://doi.org/10.1137/1.*************.126PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the Directed Steiner Tree (DST) problem, we are given a directed graph G = (V, E) on n vertices with edge-costs , a root vertex r, and a set K of k terminals. The goal is to find a minimum-cost subgraph of G that contains a path from r to every terminal t ∊ k. DST has been a notorious problem for decades as there is a large gap between the best-known polynomial-time approximation ratio of O(k∊) for any constant ∊ > 0, and the best quasi-polynomial-time approximation ratio of . Towards understanding this gap, we study the integrality gap of the standard flow LP relaxation for the problem. We show that the LP has an integrality gap polynomial in n. Previously, the integrality gap LP is only known to be [<PERSON><PERSON><PERSON> et al., SODA'03 & SIAM J. Comput.] and [<PERSON><PERSON><PERSON><PERSON>, <PERSON>ODA'02] in some instance with . Our result gives the first known lower bound on the integrality gap of this standard LP that is polynomial in n, the number of vertices. Consequently, we rule out the possibility of developing a poly-logarithmic approximation algorithm for the problem based on the flow LP relaxation. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.126"}, {"primary_key": "1764480", "vector": [], "sparse_vector": [], "title": "Optimal Sorting Circuits for Short Keys.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Optimal Sorting Circuits for Short KeysW<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.3583 - 3629Chapter DOI:https://doi.org/10.1137/1.*************.142PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A long-standing open question in the algorithms and complexity literature is whether there exist sorting circuits of size o(n log n). A recent work by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (SODA'21) showed that if the elements to be sorted have short keys whose length k = o(log n), then one can indeed overcome the n log n barrier for sorting circuits, by leveraging non-comparison-based techniques. More specifically, <PERSON><PERSON><PERSON> et al. showed that there exist O(n) · min(k, log n)-sized sorting circuits for k-bit keys, ignoring polylog∗ factors. Interestingly, the recent works by <PERSON><PERSON><PERSON> et al. (STOC'19) and <PERSON><PERSON><PERSON> et al. (SODA'21) also showed that the above result is essentially optimal for every key length k, assuming that the famous Li-Li network coding conjecture holds. Note also that proving any unconditional super-linear circuit lower bound for a wide class of problems is beyond the reach of current techniques. Unfortunately, the approach taken by previous works to achieve optimality in size somewhat crucially relies on sacrificing the depth: specifically, their circuit is super-polylogarithmic in depth even for 1-bit keys. Asharov et al. phrase it as an open question how to achieve optimality both in size and depth. In this paper, we close this important gap in our understanding. We construct a sorting circuit of size O(n) · min(k, log n) (ignoring polylog∗ terms) and depth O(log n). To achieve this, our approach departs significantly from the prior works. Our result can be viewed as a generalization of the landmark result by Ajtai, Komlós, and Szemerédi (STOC'83), simultaneously in terms of size and depth. Specifically, for k = o(log n), we achieve asymptotical improvements in size over the AKS sorting circuit, while preserving optimality in depth. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.142"}, {"primary_key": "1764481", "vector": [], "sparse_vector": [], "title": "Subexponential Parameterized Algorithms on Disk Graphs (Extended Abstract).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Subexponential Parameterized Algorithms on Disk Graphs (Extended Abstract)<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.2005 - 2031Chapter DOI:https://doi.org/10.1137/1.*************.80PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract One of the most celebrated results in Parameterized Complexity is the Bidimensionality theory of <PERSON><PERSON><PERSON> et al. [J. ACM, 2005], which has yielded, over the past two decades, numerous subexponential-time fixed-parameter tractable (FPT) algorithms for various problems on planar (and H-minor-free) graphs. At the heart of this theory is the proof of sublinear bounds in terms of solution size on the treewidth of a given graph. Inspired by this theory, in recent years, significant efforts have been devoted to design subexponential-time FPT algorithms for problems on geometric graph classes that utilize new treewidth bounds, in particular (but not only) for unit disk graphs [<PERSON><PERSON><PERSON> et al., SODA'12; <PERSON><PERSON><PERSON> et al., DCG'19; Panolan et al., SODA'19; Fomin et al. SoCG'20]. In this paper, we aim to attain such results on disk graphs, a broad class of graphs that generalizes both the classes of planar graphs and unit disk graphs, and thereby unify the aforementioned research frontiers for planar and unit disk graphs. Our main contribution is an approach to design subexponential-time FPT algorithms for problems on disk graphs, which we apply to several well-studied graph problems. At the heart of our approach lie two new combinatorial theorems concerning the treewidth of disk graphs having a realization of bounded ply (or maximum clique size) that are of independent interest. In particular, we prove a stronger version of the following treewidth bound: Let G be a disk graph that has some realization of ply p and no false twins, and M ⊆ V(G) such that G has no triangle with exactly one vertex from M, and G–M has treewidth w. Then, the treewidth of G is . Among our applications are the first subexponential-time FPT algorithms for several problems on disk graphs, including Triangle Hitting, Feedback Vertex Set and Odd Cycle Transversal (OCT). Previously, subexponential-time FPT algorithms for these problems were only known on planar graphs and unit disk graphs (excluding OCT, which was only known to admit such an algorithm on planar graphs). Our algorithms are robust, in particular, they do not require a geometric realization of the input graph (for all aforementioned problems), and they generalize to the weighted and counting versions of all aforementioned problems except for OCT. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.80"}, {"primary_key": "1764482", "vector": [], "sparse_vector": [], "title": "Balanced Allocations: Caching and Packing, Twinning and Thinning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Balanced Allocations: Caching and Packing, Twinning and ThinningDimitrio<PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.1847 - 1874Chapter DOI:https://doi.org/10.1137/1.*************.74PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the sequential allocation of m balls (jobs) into n bins (servers) by allowing each ball to choose from some bins sampled uniformly at random. The goal is to maintain a small gap between the maximum load and the average load. In this paper, we present a general framework that allows us to analyze various allocation processes that slightly prefer allocating into underloaded, as opposed to overloaded bins. Our analysis covers several natural instances of processes, including: The Caching process (a.k.a. memory protocol) as studied by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON> (2002). The Packing process: At each round we only take one bin sample. If the load is below some threshold (e.g., the average load), then we place as many balls until the threshold is reached; otherwise, we place only one ball. The Twinning process: At each round, we only take one bin sample. If the load is below some threshold, then we place two balls; otherwise, we place only one ball. The Thinning process as recently studied by <PERSON>ldheim and Gurel-<PERSON>urevich (2021). As we demonstrate, using an interplay between several potential functions our general framework implies for all these processes a gap of O(log n) for any number of balls m ≥ n. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.74"}, {"primary_key": "1764483", "vector": [], "sparse_vector": [], "title": "Better Lower Bounds for Shortcut Sets and Additive Spanners via an Improved Alternation Product.", "authors": ["<PERSON>", "Virginia Vassilevska Williams", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Better Lower Bounds for Shortcut Sets and Additive Spanners via an Improved Alternation Product<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.3311 - 3331Chapter DOI:https://doi.org/10.1137/1.*************.131PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We obtain improved lower bounds for additive spanners, additive emulators, and diameter-reducing shortcut sets. Spanners and emulators are sparse graphs that approximately preserve the distances of a given graph. A shortcut set is a set of edges that when added to a directed graph, decreases its diameter. The previous best known lower bounds for these three structures are given by <PERSON> and <PERSON><PERSON> [HP18]. For O(n)-sized spanners, we improve the lower bound on the additive stretch from Ω(n1/11) to Ω(n2/21). For O(n)-sized emulators, we improve the lower bound on the additive stretch from Ω(n1/18) to Ω(n2/29). For O(m)-sized shortcut sets, we improve the lower bound on the graph diameter from Ω(n1/11) to Ω(n1/8). Our key technical contribution, which is the basis of all of our bounds, is an improvement of a graph product known as an alternation product. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.131"}, {"primary_key": "1764484", "vector": [], "sparse_vector": [], "title": "A Framework for Parameterized Subexponential Algorithms for Generalized Cycle Hitting Problems on Planar Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "Pranaben<PERSON> Mi<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A Framework for Parameterized Subexponential Algorithms for Generalized Cycle Hitting Problems on Planar Graphs<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.2085 - 2127Chapter DOI:https://doi.org/10.1137/1.*************.83PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Subexponential parameterized algorithms are known for a wide range of natural problems on planar graphs, but the techniques are usually highly problem specific. The goal of this paper is to introduce a framework for obtaining time algorithms for a family of graph modification problems that includes problems that can be seen as generalized cycle hitting problems. Our starting point is the Node Unique Label Cover problem (that is, given a CSP instance where each constraint is a permutation of values on two variables, the task is to delete k variables to make the instance satisfiable). We introduce a variant of the problem where k vertices have to be deleted such that every 2-connected component of the remaining instance is satisfiable. Then we extend the problem with cardinality constraints that restrict the number of times a certain value can be used (globally or within a 2-connected component of the solution). We show that there is an time algorithm on planar graphs for any problem that can be formulated this way, which includes a large number of well-studied problems, for example, Odd Cycle Transversal, Subset Feedback Vertex Set, Group Feedback Vertex Set, Subset Group Feedback Vertex Set, Vertex Multiway Cut, and Component Order Connectivity. For those problems that admit appropriate (quasi)polynomial kernels (that increase the parameter only linearly and preserve planarity), our results immediately imply time parameterized algorithms on planar graphs. In particular, we use or adapt known kernelization results to obtain time (randomized) algorithms for Vertex Multiway Cut, Group Feedback Vertex Set, and Subset Feedback Vertex Set. Our algorithms are designed with possible generalization to H-minor free graphs in mind. To obtain the same time algorithms on H-minor free graphs, the only missing piece is the vertex version of a contraction decomposition theorem that we currently have only for planar graphs. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.83"}, {"primary_key": "1764485", "vector": [], "sparse_vector": [], "title": "Approximate Core for Committee Selection via Multilinear Extension and Market Clearing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximate Core for Committee Selection via Multilinear Extension and Market ClearingKamesh <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.2229 - 2252Chapter DOI:https://doi.org/10.1137/1.*************.89PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Motivated by civic problems such as participatory budgeting and multiwinner elections, we consider the problem of public good allocation: Given a set of indivisible projects (or candidates) of different sizes, and voters with different monotone utility functions over subsets of these candidates, the goal is to choose a budget-constrained subset of these candidates (or a committee) that provides fair utility to the voters. The notion of fairness we adopt is that of core stability from cooperative game theory: No subset of voters should be able to choose another blocking committee of proportionally smaller size that provides strictly larger utility to all voters that deviate. The core provides a strong notion of fairness, subsuming other notions that have been widely studied in computational social choice. It is well-known that an exact core need not exist even when utility functions of the voters are additive across candidates. We therefore relax the problem to allow approximation: Voters can only deviate to the blocking committee if after they choose any extra candidate (called an additament), their utility still increases by an α factor. If no blocking committee exists under this definition, we call this an α-core. Our main result is that an α-core, for α 1.015 for submodular utilities, and a lower bound of any function in the number of voters and candidates for general monotone utilities. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.89"}, {"primary_key": "1764486", "vector": [], "sparse_vector": [], "title": "Congruency-Constrained TU Problems Beyond the Bimodular Case.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Congruency-Constrained TU Problems Beyond the Bimodular <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>pp.2743 - 2790Chapter DOI:https://doi.org/10.1137/1.*************.108PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A long-standing open question in Integer Programming is whether integer programs with constraint matrices with bounded subdeterminants are efficiently solvable. An important special case thereof are congruency-constrained integer programs min{cT x: Tx ≤ b, γT x ≡ r (mod m), x ∊ ℤn} with a totally unimodular constraint matrix T. Such problems have been shown to be polynomial-time solvable for m = 2, which led to an efficient algorithm for integer programs with bimodular constraint matrices, i.e., full-rank matrices whose n × n subdeterminants are bounded by two in absolute value. Whereas these advances heavily relied on existing results on well-known combinatorial problems with parity constraints, new approaches are needed beyond the bimodular case, i.e., for m > 2. We make first progress in this direction through several new techniques. In particular, we show how to efficiently decide feasibility of congruency-constrained integer programs with a totally unimodular constraint matrix for m = 3. Furthermore, for general m, our techniques also allow for identifying flat directions of infeasible problems, and deducing bounds on the proximity between solutions of the problem and its relaxation. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.108"}, {"primary_key": "1764487", "vector": [], "sparse_vector": [], "title": "Isomorphism Testing for Graphs Excluding Small Topological Subgraphs.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Isomorphism Testing for Graphs Excluding Small Topological SubgraphsDaniel NeuenDaniel Neuenpp.1411 - 1434Chapter DOI:https://doi.org/10.1137/1.*************.59PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We give an isomorphism test that runs in time npolylog(h) on all n-vertex graphs excluding some h-vertex graph as a topological subgraph. Previous results state that isomorphism for such graphs can be tested in time npolylog(n) (<PERSON><PERSON>, STOC 2016) and nf(h) for some function f (<PERSON><PERSON><PERSON> and <PERSON>, SIAM J. Comp., 2015). Our result also unifies and extends previous isomorphism tests for graphs of maximum degree d running in time npolylog(d) (FOCS 2018) and for graphs of Hadwiger number h running in time npolylog(h) (FOCS 2020). Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.59"}, {"primary_key": "1764488", "vector": [], "sparse_vector": [], "title": "Planar Multiway Cut with Terminals on Few Faces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Planar Multiway Cut with Terminals on Few FacesSukanya Pandey and <PERSON> and <PERSON>.2032 - 2063Chapter DOI:https://doi.org/10.1137/1.*************.81PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the Edge Multiway Cut problem on planar graphs. It is known that this can be solved in time [<PERSON>, <PERSON>, ICALP 2012] and not in time under the Exponential Time Hypothesis [Marx, ICALP 2012], where t is the number of terminals. A generalization of this parameter is the number k of faces of the planar graph that jointly cover all terminals. For the related Steiner Tree problem, an time algorithm was recently shown [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., SODA 2019]. By a completely different approach, we prove in this paper that Edge Multiway Cut can be solved in time as well. Our approach employs several major concepts on planar graphs, including homotopy and sphere-cut decomposition. We also mix a global treewidth dynamic program with a <PERSON><PERSON><PERSON><PERSON>-<PERSON> style dynamic program to locally deal with large numbers of terminals. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.81"}, {"primary_key": "1764489", "vector": [], "sparse_vector": [], "title": "Limits of Preprocessing for Single-Server PIR.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Limits of Preprocessing for Single-Server PIR<PERSON><PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.2522 - 2548Chapter DOI:https://doi.org/10.1137/1.*************.99PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present lower bounds for the static cryptographic data structure problem of single-server private information retrieval (PIR). PIR considers the setting where a server holds a database of n entries and a client wishes to privately retrieve the i-th entry without revealing the index i to the server. In our work, we focus on PIR with preprocessing where an r-bit hint may be computed in a preprocessing stage and stored by the server to be used to perform private queries in expected time t. As our main result, we prove that for any single-server, computationally secure PIR with preprocessing, it must be that tr = Ω(n log n) when r = Ω(log n). If r = O(log n), then we show that t = Ω(n). Our lower bound holds even when the scheme errs with probability 1/n2 and the adversary's distinguishing advantage is 1/n. Our work improves upon the tr = Ω(n) lower bound of <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> [Jo<PERSON>'04]. For information-theoretic security, we present a stronger lower bound of t + r = Ω(n) and show a matching construction. Both our lower bounds apply for public-key doubly-efficient PIRs of Boyle, Ishai, Pass and Wootters [TCC'17]. Additionally, our lower bound for information-theoretic security also applies for offline-online PIRs as defined by Corrigan-Gibbs and Kogan [Eurocrypt'20], where the hint is private and only viewed by the client. We prove our lower bounds in a variant of the cell probe model where only accesses to the database are charged cost and computation and accesses to the hint are free. Our main technical contribution is a novel use of the cell sampling technique (also known as the incompressibility technique) used to obtain lower bounds on data structures. In previous works, this technique only leveraged the correctness guarantees to prove lower bounds even when used for cryptographic primitives. Our work combines the cell sampling technique with the privacy guarantees of PIR to construct a powerful, polynomial-time adversary that is critical to proving our higher lower bounds. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.99"}, {"primary_key": "1764490", "vector": [], "sparse_vector": [], "title": "On the complexity of binary polynomial optimization over acyclic hypergraphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)On the complexity of binary polynomial optimization over acyclic hypergraphs<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.2684 - 2699Chapter DOI:https://doi.org/10.1137/1.*************.105PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this work we advance the understanding of the fundamental limits of computation for Binary Polynomial Optimization (BPO), which is the problem of maximizing a given polynomial function over all binary points. In our main result we provide a novel class of BPO that can be solved efficiently both from a theoretical and computational perspective. In fact, we give a strongly polynomial-time algorithm for instances whose corresponding hypergraph is β-acyclic. We note that the β-acyclicity assumption is natural in several applications including relational database schemes and the lifted multicut problem on trees. Due to the novelty of our proving technique, we obtain an algorithm which is interesting also from a practical viewpoint. This is because our algorithm is very simple to implement and the running time is a polynomial of very low degree in the number of nodes and edges of the hypergraph. Our result completely settles the computational complexity of BPO over acyclic hypergraphs, since the problem is NP-hard on α-acyclic instances. Our algorithm can also be applied to any general BPO problem that contains β-cycles. For these problems, the algorithm returns a smaller instance together with a rule to extend any optimal solution of the smaller instance to an optimal solution of the original instance. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.105"}, {"primary_key": "1764491", "vector": [], "sparse_vector": [], "title": "Compact Redistricting Plans Have Many Spanning Trees.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Compact Redistricting Plans Have Many Spanning Trees<PERSON>riel <PERSON> and <PERSON><PERSON> and <PERSON>-<PERSON>oltzpp.3754 - 3771Chapter DOI:https://doi.org/10.1137/1.*************.148PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the design and analysis of political redistricting maps, it is often useful to be able to sample from the space of all partitions of the graph of census blocks into connected subgraphs of equal population. There are influential Markov chain Monte Carlo methods for doing so that are based on sampling and splitting random spanning trees. Empirical evidence suggests that the distributions such algorithms sample from place higher weight on more \"compact\" redistricting plans, which is a practically useful and desirable property. In this paper, we confirm these observations analytically, establishing an inverse exponential relationship between the total length of the boundaries separating districts and the probability that such a map will be sampled. This result provides theoretical underpinnings for algorithms that are already making a significant real-world impact. Previous chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.148"}, {"primary_key": "1764492", "vector": [], "sparse_vector": [], "title": "Selectable Heaps and Optimal Lazy Search Trees.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Selectable Heaps and Optimal Lazy Search Trees<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.1962 - 1975Chapter DOI:https://doi.org/10.1137/1.*************.78PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We show the O(log n) time extract minimum function of efficient priority queues can be generalized to the extraction of the k smallest elements in O(k log(n/k)) time1, which we prove optimal for comparison-based priority queues with o(log n) time insertion. We show heap-ordered tree selection (<PERSON> et al., SOSA '19) can be applied on the heap-ordered trees of the classic Fibonacci heap and Brodal queue, in O(k log(n/k)) amortized and worst-case time, respectively. We additionally show the deletion of k elements or selection without extraction can be performed on both heaps, also in O(k log(n/k)) time. Surprisingly, all operations are possible with no modifications to the original Fibonacci heap and Brodal queue data structures. We then apply the result to lazy search trees (<PERSON><PERSON> & <PERSON>, FOCS '20), creating a new interval data structure based on selectable heaps. This gives optimal O(B+n) time lazy search tree performance, lowering insertion complexity into a gap Δi from O(log(n/|Δi|) + log log n) to O(log(n/|Δi|)) time. An O(1) time merge operation is also made possible when used as a priority queue, among other situations. If Brodal queues are used, all runtimes of the lazy search tree can be made worst-case. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.78"}, {"primary_key": "1764493", "vector": [], "sparse_vector": [], "title": "A Faster Algorithm for Quickest Transshipments via an Extended Discrete Newton Method.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A Faster Algorithm for Quickest Transshipments via an Extended Discrete Newton Method<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.90 - 102Chapter DOI:https://doi.org/10.1137/1.*************.5PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The Quickest Transshipment Problem is to route flow as quickly as possible from sources with supplies to sinks with demands in a network with capacities and transit times on the arcs. It is of fundamental importance for numerous applications in areas such as logistics, production, traffic, evacuation, and finance. More than 25 years ago, <PERSON><PERSON> and <PERSON><PERSON><PERSON> presented the first (strongly) polynomial-time algorithm for this problem. Their approach, as well as subsequently derived algorithms with strongly polynomial running time, are hardly practical as they rely on parametric submodular function minimization via <PERSON><PERSON><PERSON>'s method of parametric search. The main contribution of this paper is a considerably faster algorithm for the Quickest Transshipment Problem that instead employs a subtle extension of the Discrete Newton Method. This improves the previously best known running time of Õ(m4k14) to O(m2k5 + m3k3 + m3n), where n is the number of nodes, m the number of arcs, and k the number of sources and sinks. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.5"}, {"primary_key": "1764494", "vector": [], "sparse_vector": [], "title": "A Lower Bound for the n-queens Problem.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A Lower Bound for the n-queens <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.2185 - 2197Chapter DOI:https://doi.org/10.1137/1.*************.86PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The n-queens puzzle is to place n mutually non-attacking queens on an n × n chessboard. We present a simple two stage randomized algorithm to construct such configurations. In the first stage, a random greedy algorithm constructs an approximate toroidal n-queens configuration. In this well-known variant the diagonals wrap around the board from left to right and from top to bottom. We show that with high probability this algorithm succeeds in placing (1–o(1))n queens on the board. In the second stage, the method of absorbers is used to obtain a complete solution to the non-toroidal problem. By counting the number of choices available at each step of the random greedy algorithm we conclude that there are more than ((1–o(1)) ne–3)n solutions to the n-queens problem. This proves a conjecture of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> in a strong form. Recently, using different methods, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> proved the same lower bound for the toroidal problem, giving an independent proof of the result. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.86"}, {"primary_key": "1764495", "vector": [], "sparse_vector": [], "title": "Testing matrix product states.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Testing matrix product states<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.1679 - 1701Chapter DOI:https://doi.org/10.1137/1.*************.68PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Matrix product states (MPS) are a class of physically-relevant quantum states which arise in the study of quantum many-body systems. A quantum state comprised of n qudits is said to be an MPS of bond dimension r if the reduced density matrix ψ1, …, k has rank r for each k ∊ {1, …, n}. When r = 1, this corresponds to the set of product states, i.e. states of the form |ψ1〉 ⊗ ⃛ ⊗ |ψn), which possess no entanglement. For larger values of r, this yields a more expressive class of quantum states, which are allowed to possess limited amounts of entanglement. Devising schemes for testing the amount of entanglement in quantum systems has played a crucial role in quantum computing and information theory. In this work, we study the problem of testing whether an unknown state |ψ〉 is an MPS in the property testing model. In this model, one is given m identical copies of |ψ〉, and the goal is to determine whether |ψ〉 is an MPS of bond dimension r or whether |ψ〉 is far from all such states. For the case of product states, we study the product test, a simple two-copy test previously analyzed by Harrow and Montanaro [17], and a key ingredient in their proof that QMA(2) = QMA(k) for k ≥ 2. We give a new and simpler analysis of the product test which achieves an optimal bound for a wide range of parameters, answering open problems in [17] and [23]. For the case of r ≥ 2, we give an efficient algorithm for testing whether |ψ〉 is an MPS of bond dimension r using m = O(nr2) copies, independent of the dimensions of the qudits, and we show that Ω(n1/2) copies are necessary for this task. This lower bound shows that a dependence on the number of qudits n is necessary, in sharp contrast to the case of product states where a constant number of copies suffices. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.68"}, {"primary_key": "1764496", "vector": [], "sparse_vector": [], "title": "Local Search for Weighted Tree Augmentation and Steiner Tree.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Local Search for Weighted Tree Augmentation and Steiner TreeVera Traub and <PERSON> Traub and <PERSON>pp.3253 - 3272Chapter DOI:https://doi.org/10.1137/1.*************.128PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present a technique that allows for improving on some relative greedy procedures by well-chosen (non-oblivious) local search algorithms. Relative greedy procedures are a particular type of greedy algorithm that start with a simple, though weak, solution, and iteratively replace parts of this starting solution by stronger components. Some well-known applications of relative greedy algorithms include approximation algorithms for Steiner Tree and, more recently, for connectivity augmentation problems. The main application of our technique leads to a (1.5 + ∊)-approximation for Weighted Tree Augmentation, improving on a recent relative greedy based method with approximation factor 1 + ln 2 + ∊ ≈ 1.69. Furthermore, we show how our local search technique can be applied to Steiner Tree, leading to an alternative way to obtain the currently best known approximation factor of ln 4 + ∊. Contrary to prior methods, our approach is purely combinatorial without the need to solve an LP. Nevertheless, the solution value can still be bounded in terms of the well-known hypergraphic LP, leading to an alternative, and arguably simpler, technique to bound its integrality gap by ln 4. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.128"}, {"primary_key": "1764497", "vector": [], "sparse_vector": [], "title": "Constructing Many Faces in Arrangements of Lines and Segments.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Constructing Many Faces in Arrangements of Lines and SegmentsHaitao WangHaitao Wangpp.3168 - 3180Chapter DOI:https://doi.org/10.1137/1.*************.123PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present new algorithms for computing many faces in arrangements of lines and segments. Given a set S of n lines (resp., segments) and a set P of m points in the plane, the problem is to compute the faces of the arrangements of S that contain at least one point of P. For the line case, we give a deterministic algorithm of time. This improves the previously best deterministic algorithm [<PERSON><PERSON><PERSON>, 1990] by a factor of log2.22 n and improves the previously best randomized algorithm [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, 1998] by a factor of log1/3 n in certain cases (e.g., when m = Θ(n)). For the segment case, we present a deterministic algorithm of time, where and α(n) is the inverse <PERSON><PERSON>mann function. This improves the previously best deterministic algorithm [<PERSON><PERSON><PERSON>, 1990] by a factor of log2.11 n and improves the previously best randomized algorithm [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, 1998] by a factor of log n in certain cases (e.g., when m = Θ(n)). We also give a randomized algorithm of O(m2/3 K1/3 log n + τ(nα(n) + n log m + m) log n log K) expected time, where K is the number of intersections of all segments of S. In addition, we consider the query version of the problem, that is, preprocess S to compute the face of the arrangement of S that contains any query point. We present new results that improve the previous work for both the line and the segment cases. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.123"}, {"primary_key": "1764498", "vector": [], "sparse_vector": [], "title": "Co-evolution of Opinion and Social Tie Dynamics Towards Structural Balance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Co-evolution of Opinion and Social Tie Dynamics Towards Structural Balance<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.3362 - 3388Chapter DOI:https://doi.org/10.1137/1.*************.133PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper, we propose co-evolution models for both dynamics of opinions (people's view on a particular topic) and dynamics of social appraisals (the approval or disapproval towards each other). Opinion dynamics and dynamics of signed networks, respectively, have been extensively studied. We propose a co-evolution model, where each vertex i in the network has a current opinion vector vi and each edge (i, j) has a weight wij that models the relationship between i, j. The system evolves as opinions and edge weights are updated over time by the following rules: Opinion dynamics: The opinion of agent i is updated as a linear combination of its current opinion and the weighted sum of neighbors' opinions with coefficients in matrix W = [wij]. Appraisal dynamics: The appraisal wij is updated as a linear combination of its current value and the agreement of the opinions of agents i and j. The agreement of opinion vi and vj is taken as the dot product vi · vj. We are interested in characterizing the long-time behavior of the dynamic model–i.e., whether edge weights evolve to have stable signs (positive or negative) and structural balance (the multiplication of weights on any triangle is non-negative). Our main theoretical result solves the above dynamic system with time-evolving opinions V(t) = [v1(t), …, vn(t)] and social tie weights W(t) = [wij(t)]n×n. For a generic initial opinion vector V(0) and weight matrix W(0), one of the two phenomena must occur at the limit. The first one is that both sign stability and structural balance (for any triangle with individual i, j, k, wijwjkwki ≥ 0) occur. In the special case that V(0) is an eigenvector of W(0), we are able to obtain the explicit solution to the co-evolution equation and give exact estimates on the blowup time and rate convergence. The second one is that all the opinions converge to 0, i.e., limt→∞ |V(t)| = 0. We also performed extensive simulations to examine how different initial conditions affect the network evolution. Of particular interest is that our dynamic model can be used to faithfully detect community structures. On real-world graphs, with a small number of seeds initially assigned ground truth opinions, the dynamic model successfully discovers the final community structure. The model sheds lights on why community structure emerges and becomes a widely observed, sustainable property in complex networks. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.133"}, {"primary_key": "1764499", "vector": [], "sparse_vector": [], "title": "Improved Algorithms for Low Rank Approximation from Sparsity.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Improved Algorithms for Low Rank Approximation from SparsityDavi<PERSON> <PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.2358 - 2403Chapter DOI:https://doi.org/10.1137/1.*************.95PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We overcome two major bottlenecks in the study of low rank approximation by assuming the low rank factors themselves are sparse. Specifically, (1)for low rank approximation with spectral norm error, we show how to improve the best known running time to running time plus low order terms depending on the sparsity of the low rank factors, and(2)for streaming algorithms for Frobenius norm error, we show how to bypass the known Ω(nk/∊) memory lower bound and obtain an sk(log n)/poly(∊) memory bound, where s is the number of non-zeros of each low rank factor. Although this algorithm runs in exponential time, as it must under standard complexity-theoretic assumptions, we also present polynomial time algorithms using poly(s, k, log n, ∊–1) memory that output rank k approximations supported on an O(sk/∊) × O(sk/∊) submatrix. Both the prior running time and the nk/∊ memory for these problems were long-standing barriers; our results give a natural way of overcoming them assuming sparsity of the low rank factors. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.95"}, {"primary_key": "1764500", "vector": [], "sparse_vector": [], "title": "Universally-Optimal Distributed Shortest Paths and Transshipment via Graph-Based ℓ1-Oblivious Routing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2022 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Universally-Optimal Distributed Shortest Paths and Transshipment via Graph-Based ℓ1-Oblivious Routing<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.2549 - 2579Chapter DOI:https://doi.org/10.1137/1.*************.100PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We provide universally-optimal distributed graph algorithms for (1+∊)-approximate shortest path problems including shortest-path-tree and transshipment. The universal optimality of our algorithms guarantees that, on any n-node network G, our algorithm completes in T · no(1) rounds whenever a T-round algorithm exists for G. This includes D · no(1)-round algorithms for any planar or excluded-minor network. Our algorithms never require more than rounds, resulting in the first sub-linear-round distributed algorithm for transshipment. The key technical contribution leading to these results is the first efficient no(1)-competitive linear ℓ1-oblivious routing operator that does not require the use of ℓ1-embeddings. Our construction is simple, solely based on low-diameter decompositions, and—in contrast to all known constructions—directly produces an oblivious flow instead of just an approximation of the optimal flow cost. This also has the benefit of simplifying the interaction with Sherman's multiplicative weight framework [SODA'17] in the distributed setting and its subsequent rounding procedures. Previous chapter Next chapter RelatedDetails Published:2022eISBN:978-1-61197-707-3 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA22Book Pages:xvii + 3771", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.100"}, {"primary_key": "1783298", "vector": [], "sparse_vector": [], "title": "Proceedings of the 2022 ACM-SIAM Symposium on Discrete Algorithms, SODA 2022, Virtual Conference / Alexandria, VA, USA, January 9 - 12, 2022", "authors": ["<PERSON> (<PERSON><PERSON><PERSON>) <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider linear programming in the oracle model: max{c x : x ∈ P }, where the polyhedron P = {x ∈ R n : Ax ≤ b} is given by a separation oracle that returns violated inequalities from the system Ax ≤ b.We present an algorithm that finds exact primal and dual solutions using O(n 2 log(n/δ)) oracle calls and O(n 4 log(n/δ) + n 5 log log(1/δ)) arithmetic operations, where δ is a geometric condition number associated with the system (A, b).These bounds do not depend on the cost vector c and do not require any a-priori knowledge of δ.The algorithm works in a black box manner, requiring a subroutine for approximate primal and dual solutions; the above running times are achieved when using the cutting plane method of <PERSON>, <PERSON>, <PERSON>, and <PERSON> (STOC 2020) for this subroutine.Whereas approximate solvers may return primal solutions only, we develop a general framework for extracting dual certificates based on the work of <PERSON><PERSON> and <PERSON> (Math.Oper.Res.1985).Our algorithm works in the real model of computation, and extends results by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> (Prog.Comb.Opt.1984), and by <PERSON> and <PERSON> (Combinatorica 1987) on solving LPs in the bit-complexity model.We show that under a natural assumption, simultaneous Diophantine approximation in these results can be avoided.", "published": "2022-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************"}]