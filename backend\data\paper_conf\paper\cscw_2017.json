[{"primary_key": "3673914", "vector": [], "sparse_vector": [], "title": "Evaluating a Computational Approach to Labeling Politeness: Challenges for the Application of Machine Classification to Social Computing Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social computing researchers are beginning to apply machine learning tools to classify and analyze social media data. Our interest in understanding politeness in an online community focused our attention on tools that would help automate politeness analysis. This paper highlights one popular classification tool designed to score the politeness of text. Our application of this tool to Wikipedia data yielded some unexpected results. Those unexpected results led us to question how the tool worked and its effectiveness relative to human judgment and classification. We designed a user study to revalidate the tool with crowdworkers labeling samples of content from Wikipedia talk pages, imitating the original efforts to validate the tool. This revalidation points to challenges for automatic labeling. Based on our results, this paper reconsiders politeness in online communities as well as broader trends in the use of machine classifiers in social computing research.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134687"}, {"primary_key": "3673872", "vector": [], "sparse_vector": [], "title": "Motivating Participation in Crowdsourced Policymaking: The Interplay of Epistemic and Interactive Aspects.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we examine the changes in motivation factors in crowdsourced policymaking. By drawing on longitudinal data from a crowdsourced law reform, we show that people participated because they wanted to improve the law, learn, and solve problems. When crowdsourcing reached a saturation point, the motivation factors weakened and the crowd disengaged. Learning was the only factor that did not weaken. The participants learned while interacting with others, and the more actively the participants commented, the more likely they stayed engaged. Crowdsourced policymaking should thus be designed to support both epistemic and interactive aspects. While the crowd's motives were rooted in self-interest, their knowledge perspective showed common-good orientation, implying that rather than being dichotomous, motivation factors move on a continuum. The design of crowdsourced policymaking should support the dynamic nature of the process and the motivation factors driving it.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134653"}, {"primary_key": "3673874", "vector": [], "sparse_vector": [], "title": "WevQuery: Testing Hypotheses about Web Interaction Patterns.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Remotely stored user interaction logs, which give access to a wealth of data generated by large numbers of users, have been long used to understand if interactive systems meet the expectations of designers. Unfortunately, detailed insight into users' interaction behaviour still requires a high degree of expertise and domain specific knowledge. We present WevQuery, a scalable system to query user interaction logs in order to allow designers to test their hypotheses about users' behaviour. WevQuery supports this purpose using a graphical notation to define the interaction patterns designers are seeking. WevQuery is scalable as the queries can then be executed against large user interaction datasets by employing the MapReduce paradigm. This way WevQuery provides designers effortless access to harvest users' interaction patterns, removing the burden of low-level interaction data analysis. We present two scenarios to showcase the potential of WevQuery, from the design of the queries to their execution on real interaction data accounting for 5.7m events generated by 2,445 unique users.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3095806"}, {"primary_key": "3673877", "vector": [], "sparse_vector": [], "title": "A Sociotechnical Study of a Community-based Rewards Program: Insights on Building Social, Financial and Human Capital.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Individual empowerment is defined as an increased sense of confidence and control over one's life. Empowerment is critical in low-income communities, and can be facilitated through the development of social, financial and human capital. We present a qualitative study of a community program that seeks to empower low-income neighborhood residents through a mobile application that connects them to local resources. Our findings highlight how the application and offline socio-organizational mechanisms worked in tandem to create gateways for capital building-sparking connections (to people and opportunities) that residents leveraged with varying motivations and outcomes. We also discuss how the interplay of newly-developed financial, social, and human capital contributed to residents' sense of empowerment and impacted their families. We contribute to CSCW by extending an existing community informatics framework, characterizing the value of sociotechnical systems that holistically build social, financial, and human capital amongst neighborhood residents.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134690"}, {"primary_key": "3673896", "vector": [], "sparse_vector": [], "title": "The Sharing Economy in Computing: A Systematic Literature Review.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The sharing economy has quickly become a very prominent subject of research in the broader computing literature and the in human--computer interaction (HCI) literature more specifically. When other computing research areas have experienced similarly rapid growth (e.g. human computation, eco-feedback technology), early stage literature reviews have proved useful and influential by identifying trends and gaps in the literature of interest and by providing key directions for short- and long-term future work. In this paper, we seek to provide the same benefits with respect to computing research on the sharing economy. Specifically, following the suggested approach of prior computing literature reviews, we conducted a systematic review of sharing economy articles published in the Association for Computing Machinery Digital Library to investigate the state of sharing economy research in computing. We performed this review with two simultaneous foci: a broad focus toward the computing literature more generally and a narrow focus specifically on HCI literature. We collected a total of 112 sharing economy articles published between 2008 and 2017 and through our analysis of these papers, we make two core contributions: (1) an understanding of the computing community's contributions to our knowledge about the sharing economy, and specifically the role of the HCI community in these contributions (i.e. what has been done ) and (2) a discussion of under-explored and unexplored aspects of the sharing economy that can serve as a partial research agenda moving forward (i.e. what is next to do ).", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134673"}, {"primary_key": "3673898", "vector": [], "sparse_vector": [], "title": "Private <PERSON><PERSON> Fe<PERSON>back as Engagement Driver in Humanitarian Mapping.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Prior research suggests that public negative feedback on social knowledge sharing platforms can be powerfully demotivating to newcomers, particularly when it involves peer feedback mechanisms such as ratings and commenting systems. What is the impact on newcomer retention when feedback is private, and from a single peer reviewer? We study these effects using the example of the Humanitarian OpenStreetMap Team, a Wikipedia-style social mapping platform where the review process is closer to a teacher-learner model rather than a public peer review. We observe peer feedback for early contributions by 1,300 newcomers, and assess the impact of different classes of feedback, including performance feedback, corrective feedback, and verbal rewards. We find that verbal rewards and immediate feedback can have a powerful effect on newcomer retention. In order to better support such positive engagement effects, we recommend that system designers conceptually distinguish between mechanisms for quality control and for learner feedback.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134675"}, {"primary_key": "3673903", "vector": [], "sparse_vector": [], "title": "TUIOFX: A JavaFX Toolkit for Shared Interactive Surfaces.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Building multi-touch multi-user applications for Shared Interactive Surfaces is a complex endeavour that requires fundamental knowledge in touch enabling hardware, gesture recognition, graphical representation of digital information and multi-user interaction. While several specialised toolkits help developers in this effort, we identified a variety of challenges with these toolkits, as for example the lack of cross-platform support, the limited number of touch-enabled multi-user widgets, missing documentation, and lacking community support -- all raising the barriers to entry. In this paper, we present TUIOFX, a toolkit for developing multi-touch, multi-user applications for Shared Interactive Surfaces in Java, which tackles all of the identified problems. The sophisticated implementation of TUIOFX adds support for TUIO-enabled hardware and multi-user interaction under the hood of JavaFX, and leaves the well-learned JavaFX API for the developers fully intact -- thus allowing particularly novices a very quick start. In this paper we provide the technical insights, in the concepts and their elegant implementation.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3095812"}, {"primary_key": "3673909", "vector": [], "sparse_vector": [], "title": "On Making Data Actionable: How Activists Use Imperfect Data to Foster Social Change for Human Rights Violations in Mexico.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we examine how activist organizations, focused on human rights violations (HRVs) in Mexico, obtain and translate data to produce actionable insight for social change. Through interviews with 15 participants working in think tanks, human rights centers, non-governmental organizations, and nonprofit organizations, we identified two key data challenges that impact their work: absent and conflicting data. We then describe how these nonprofits try to understand these issues by building alliances to address specific, detrimental knowledge and data gaps. Next, we articulate how these activists use data to work towards social change by informing citizens, requesting action, and building capacity. Lastly, we propose recommendations on how to design for HRVs-focused data practices, focusing on issues related to addressing technology and infrastructure constraints, designing for safety, and supporting community data collection and dissemination.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134654"}, {"primary_key": "3673959", "vector": [], "sparse_vector": [], "title": "HistoryViewer: Instrumenting a Visual Analytics Application to Support Revisiting a Session of Interactive Data Analysis.", "authors": ["<PERSON>ícius C. V. B. Segura", "<PERSON>"], "summary": "Visual analytics applications (VAApps) rely heavily on visual representations and notations to communicate information and support user interaction. The visual representations themselves are the main communication form used by designers, reflecting their interpretation of what data aspects should be highlighted and how users can explore the data. Ultimately, the combination of representation and interactivity guides the user's data analysis in VAApp. To keep the VAApp development centered in their users, the design team must pay special attention to the visual representations and their underlying interaction mechanisms. Our team is developing HistoryViewer, a VAApp itself for users to explore log data obtained from interactions with other (\"source\") VAApps (VAAppsrc). During the development of HistoryViewer and the instrumentation of a VAAppsrc, we noticed unanticipated benefits for the development of the VAAppsrc itself. This paper presents our HistoryViewer system, proposes an architecture for VAApps integrated with our log model, discussing some ancillary benefits of such instrumentation. Finally, it reports two empirical studies conducted to gather potential users' opinions about HistoryViewer, which revealed positive attitudes towards the system and pointed to opportunities for improvement.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3095813"}, {"primary_key": "3673970", "vector": [], "sparse_vector": [], "title": "Social CheatSheet: An Interactive Community-Curated Information Overlay for Web Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "April <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Users can often find it difficult to sift through dense help pages, tutorials, Q&amp;A sites, blogs, and wikis to locate useful task-specific instructions for feature-rich applications. We present Social CheatSheet, an interactive information overlay that can appear atop any existing web application and retrieve relevant step-by-step instructions and tutorials curated by other users. Based on results of our formative study, the system offers several features for users to search, browse, filter, and bookmark community-generated help content and to ask questions and clarifications. Furthermore, Social CheatSheet includes embedded curation features for users to generate their own annotated notes and tutorials that can be kept private or shared with the user community. A weeklong deployment study with 15 users showed that users found Social CheatSheet to be useful and they were able to easily both add their own curated content and locate content generated by other users. The majority of users wanted to keep using the system beyond the deployment. We discuss the potential of Social CheatSheet as an application-independent platform driven by community curation efforts to lower the barriers in finding relevant help and instructions.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134737"}, {"primary_key": "3673977", "vector": [], "sparse_vector": [], "title": "Understanding Collaborative Decision Making Around a Large-Scale Interactive Tabletop.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present findings from an empirical study of how groups of eight users collaborate on a decision-making task around an interactive tabletop. To our knowledge, this is the first study to examine co-located collaboration in larger groups (of 8-12 users) seated around a large-scale high-resolution multi-touch horizontal display. Our findings shed light on: 1) the effect of collaboration patterns of larger groups on equity of participation; 2) the role of participants' position around the tabletop in forming collaborations; and 3) the mechanisms, which facilitate coordination and collaboration in larger group interacting around large-scale tabletops; We also contribute computational methods that leverage image processing to analyze interaction around large-scale tabletops. Finally, we discuss implications for the design of large-scale tabletop systems for supporting co-located collaboration in larger groups.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134745"}, {"primary_key": "3673989", "vector": [], "sparse_vector": [], "title": "The Effect of Computer-Generated Descriptions on Photo-Sharing Experiences of People with Visual Impairments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Like sighted people, visually impaired people want to share photographs on social networking services, but find it difficult to identify and select photos from their albums. We aimed to address this problem by incorporating state-of-the-art computer-generated descriptions into Facebook's photo-sharing feature. We interviewed 12 visually impaired participants to understand their photo-sharing experiences and designed a photo description feature for the Facebook mobile application. We evaluated this feature with six participants in a seven-day diary study. We found that participants used the descriptions to recall and organize their photos, but they hesitated to upload photos without a sighted person's input. In addition to basic information about photo content, participants wanted to know more details about salient objects and people, and whether the photos reflected their personal aesthetic. We discuss these findings from the lens of self-disclosure and self-presentation theories and propose new computer vision research directions that will better support visual content sharing by visually impaired people.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134756"}, {"primary_key": "3673869", "vector": [], "sparse_vector": [], "title": "Social Media in Emergencies: A Representative Study on Citizens&apos; Perception in Germany.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The value of social media in crises, disasters, and emergencies across different events (e.g. floods, storms, terroristic attacks), countries, and for heterogeneous participants (e.g. citizens, emergency services) is now well-attested. Existing work has examined the potentials and weaknesses of its use during specific events. Fewer studies, however, have focused on citizens' perceptions of social media in emergencies, and none have deployed a representative sample to examine this. We present the results of the first representative study on citizens' perception of social media in emergencies that we have conducted in Germany. Our study highlights, for example, that around half (45%) of people have used social media during an emergency to share and or look for information. In contrast, false rumours on social media (74%) are perceived as a threat. Moreover, only a minority of people have downloaded a smartphone app for emergencies (16%), with the most popular ones' weather and first aid apps.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134725"}, {"primary_key": "3673870", "vector": [], "sparse_vector": [], "title": "Collaborative Problem Solving in an Open-Ended Scientific Discovery Game.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Countless human pursuits depend upon collaborative problem solving, especially in complex, open-ended domains. As part of the growing technological support for such collaboration, an opportunity exists to design systems that actively guide and facilitate collaborative problem solving toward the most productive outcomes. A better understanding of the dynamics of open-ended collaboration on complex problems is needed to realize this opportunity. Motivated by this need for better understanding, we investigate the collaborative problem solving ecosystem of the scientific-discovery game Foldit. Our investigation is guided by two primary questions: how do the social aspects of Foldit impact an individual's behavior? and what factors have significant impact on group success? We find that collaboration and competition are associated with increased participation and that collaboration increases individual performance. We also find that measures of group skill, individual skill, and participation correlate with better group performance.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134657"}, {"primary_key": "3673871", "vector": [], "sparse_vector": [], "title": "Digital Privacy Challenges with Shared Mobile Phone Use in Bangladesh.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Prior research on technology use in the Global South suggests that people in marginalized communities frequently share a single device among multiple individuals. However, the data privacy challenges and tensions that arise when people share devices have not been studied in depth. This paper presents a qualitative study with 72 participants that analyzes how families in Bangladesh currently share mobile phones, their usage patterns, and the tensions and challenges that arise as individuals seek to protect the privacy of their personal data. We show how people share devices out of economic need, but also because sharing is a social and cultural practice that is deeply embedded in Bangladeshi society. We also discuss how prevalent power relationships affect sharing practices and reveal gender dynamics that impact the privacy of women's data. Finally, we highlight strategies that participants adopted to protect their private data from the people with whom they share devices. Taken together, our findings have broad implications that advance the CSCW community's understanding of digital privacy outside the Western world.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134652"}, {"primary_key": "3673873", "vector": [], "sparse_vector": [], "title": "Multi-Channel Topic-Based Mobile Messaging in Romantic Relationships.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "With recent shifts from email to messaging apps for personal communication, many communication partners are no longer able to converse using multiple threads within one platform as they used to via email. Romantic couples are the most frequently messaged contacts on mobile and communicate about a wide range of topics with each other, need to make joint decisions, and may have several roles in relation to each other (e.g., spouse, co-parent). Through a 3-week field study with ten U.S. diverse couples we studied how couples use a messaging app that allows them to compartmentalize their conversations into multiple \"channels\" around topics of their choice. We detail how couples managed and used channels in daily life, and channels' strengths and limitations. We found that channels made couples feel more organized, and helped with finding content and keeping track of topics. However, it was sometimes difficult to choose or navigate channels. We discuss perceived impacts of channels on relationships (e.g., topic-switching during conflicts), and outline design opportunities for messaging apps.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134655"}, {"primary_key": "3673875", "vector": [], "sparse_vector": [], "title": "It was Fun, but Did it Last?: The Dynamic Interplay between Fun Motives and Contributors&apos; Activity in Peer Production.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Coye Cheshire", "<PERSON><PERSON>", "Oded Nov"], "summary": "Peer production communities often struggle to retain contributors beyond initial engagement. This may be a result of contributors' level of motivation, as it is deeply intertwined with activity. Existing studies on participation focus on activity dynamics but overlook the accompanied changes in motivation. To fill this gap, this study examines the interplay between contributors' fun motives and activity over time. We combine motivational data from two surveys of Wikipedia newcomers with data of two periods of editing activity. We find that persistence in editing is related to fun, while the amount of editing is not: individuals who persist in editing are characterized by higher fun motives early on (when compared to dropouts), though their motives are not related to the number of edits made. Moreover, we found that newcomers' experience of fun was reinforced by their amount of activity over time: editors who were initially motivated by fun entered a virtuous cycle, whereas those who initially had low fun motives entered a vicious cycle. Our findings shed new light on the importance of early experiences and reveal that the relationship between motivation and participation levels is more complex than previously understood.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134656"}, {"primary_key": "3673876", "vector": [], "sparse_vector": [], "title": "&quot;It&apos;s good to know you&apos;re not a stranger every time&quot;: Communication about Values Between Patients with Multiple Chronic Conditions and Healthcare Providers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "When patients' decisions about health care priorities conflict with those of their health care providers, patients' health outcomes suffer. Patients' values for health and well-being influence their healthcare priorities, but recent work suggests that the values discussed in clinical settings do not reflect the full breadth of patients' values. To address an evidence gap regarding how discussions about values occur in clinical settings, we conducted a field study with patients with multiple chronic conditions and their health care providers, including clinical observations, interviews, and home visits. We report on the extent to which certain categories of patients' values identified in prior research were discussed in clinic visits. We then discuss how patients and providers coordinated their perspectives to establish connections among patients' values and health concerns. These findings have implications for the design of systems to support patient-provider communication to incorporate patients' values and promote concordant priorities for health care.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134658"}, {"primary_key": "3673878", "vector": [], "sparse_vector": [], "title": "Classification and Its Consequences for Online Harassment: Design Insights from HeartMob.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Online harassment is a pervasive and pernicious problem. Techniques like natural language processing and machine learning are promising approaches for identifying abusive language, but they fail to address structural power imbalances perpetuated by automated labeling and classification. Similarly, platform policies and reporting tools are designed for a seemingly homogenous user base and do not account for individual experiences and systems of social oppression. This paper describes the design and evaluation of HeartMob, a platform built by and for people who are disproportionately affected by the most severe forms of online harassment. We conducted interviews with 18 HeartMob users, both targets and supporters, about their harassment experiences and their use of the site. We examine systems of classification enacted by technical systems, platform policies, and users to demonstrate how 1) labeling serves to validate (or invalidate) harassment experiences; 2) labeling motivates bystanders to provide support; and 3) labeling content as harassment is critical for surfacing community norms around appropriate user behavior. We discuss these results through the lens of Bowker and Star's classification theories and describe implications for labeling and classifying online abuse. Finally, informed by intersectional feminist theory, we argue that fully addressing online harassment requires the ongoing integration of vulnerable users' needs into the design and moderation of online platforms.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134659"}, {"primary_key": "3673879", "vector": [], "sparse_vector": [], "title": "Project Management Practices as a Subject of Research for CSCW: Status and future opportunities.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The ‘project’ is a prevalent form for organising endeavours of construction, innovation, IT development and organisational change. ‘Projects’ involve coordination and cooperation between colocated and distributed actors, and are relevant for CSCW (computer supported cooperative work) research as a particular kind of cooperative work. A survey of CSCW publications only identified 26 papers that explicitly address project management (PM), of which most primarily focus on IT development. We argue that CSCW’s conceptual and methodological tools can make significant contributions to PM research, practice and its computational support. We point to four issues of relevance for future CSCW research on projects: continue to sophisticate the empirical and conceptual understanding of projects, broaden research beyond IT projects into other domains, develop agile approaches beyond IT development and focus on computational support for project work and management. In all, we argue that CSCW can advance our understanding of project work and management and the design of adequate computational support.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134660"}, {"primary_key": "3673880", "vector": [], "sparse_vector": [], "title": "Polymodal Menus: A Model-based Approach for Designing Multimodal Adaptive Menus for Small Screens.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a model-based approach for designing Polymodal Menus, a new type of multimodal adaptive menu for small screen graphical user interfaces where item selection and adaptivity are responsive to more than one interaction modality: a menu item can be selected graphically, tactilely, vocally, gesturally, or any combination of them. The prediction window containing the most predicted menu items by assignment, equivalence, or redundancy is made equally adaptive. For this purpose, an adaptive menu model maintains the most predictable menu items according to various prediction methods. This model is exploited throughout various steps defined on a new Adaptivity Design Space based on a Perception-Decision-Action cycle com-ing from cognitive psychology. A user experiment compares four conditions of Polymodal Menus (graphical, vocal, gestural, and mixed) in terms of menu selection time, error rate, user subjective satisfaction and user preference, when item prediction has a low or high level of accuracy. Polymodal Menus offer alternative input/output modalities to select menu items in various contexts of use, especially when graphical modality is constrained.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3099585"}, {"primary_key": "3673881", "vector": [], "sparse_vector": [], "title": "Generating Obligations, Assertions and Tests from UI Models.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Model-based development of interactive systems provides a number of benefits which can support the creation of robust and correct systems, particularly important when the interactive systems are safety-critical. Many different approaches have been proposed which target the models at different aspects of the development process (for example task analysis, interface layouts, functional behaviours etc.) and which can be used in different ways (verification of correctness, plasticity, usability). One of the aims for any modelling method should be simplicity - we are after all trying to hide complexity via abstraction in order to make reasoning about systems more tractable than working at the programming level. One of the challenges that exists however we do our modelling is ensuring the consistency between the model of the interface and interactivity and model of the functional behaviour of the system. This is primarily due to the different types of models that most naturally describe these different elements. In this paper we propose a method of tightening the integration of models of these different components of the system by generating obligations which explicitly describe the coupling of functional behaviour with interactive elements. We then show how these obligations can be used to support the development process during the programming and testing of the system.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3095807"}, {"primary_key": "3673882", "vector": [], "sparse_vector": [], "title": "xPress: Rethinking Design for Aging and Accessibility through an IVR Blogging System.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Although many older adults are active online, certain age-related disabilities, such as late-life vision impairment, make sustaining online participation difficult. Motivated by the need for accessible online spaces for people experiencing vision impairment in older adulthood, we developed xPress, a voice-based online blogging community. Through a 10-week deployment with seven older adults with acquired vision loss, we analyze how this type of online community enables connecting with peers, sharing experiences, and offering social support in new ways. The design of xPress also highlights the importance of human voice in accessible social platforms and reveals expectations around community participation. We discuss designing for age and disability through the lens of intersectionality and offer design considerations for similar voice-based online communities.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3139354"}, {"primary_key": "3673883", "vector": [], "sparse_vector": [], "title": "Extended Features of Task Models for Specifying Cooperative Activities.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, an extended version of task models is discussed that allows detailed specifications of cooperative activities. Within the presented specification CoTaL (Cooperative Task Language), there exist two complementary types of task models called role model and team model. One or more instances of each role model describe the specific activities of actors. The team model represents joined activities and reflects progression in cooperation between role instances. For each scenario there exists one instance of the team model. Preconditions and events can be assigned to tasks and refer to one or all running instances of a role model. An event can be a starting or finishing trigger and is activated as result of task executions. Additionally, variables can be defined. They are bound within the specified context during runtime and get the value of the identifier of a certain role instance. In this way, communication and collaboration between different actors can be specified. Tasks of a team model cannot be performed directly but present the result of the execution of other (role) models only. However, a team model can restrict the execution of role model instances. It is shown how such models can be used to specify the activities in a smart meeting room. Snapshots of their simulation in CoTaSE (Cooperative Task Specification Environment) are presented. Additionally to the local implementation there exists an implementation in a cloud. It allows real cooperative executions of tasks.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3095809"}, {"primary_key": "3673884", "vector": [], "sparse_vector": [], "title": "Threading is Sticky: How Threaded Conversations Promote Comment System User Retention.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Guardian ---the fifth most widely read online newspaper in the world as of 2014---changed conversations on its commenting platform by altering its design from non-threaded to single-level threaded in 2012. We studied this naturally occurring experiment to investigate the impact of conversation threading on user retention as mediated by several potential changes in conversation structure and style. Our analysis shows that the design change made new users significantly more likely to comment a second time, and that this increased stickiness is due in part to a higher fraction of comments receiving responses after the design change. In mediation analysis, other anticipated mechanisms such as reciprocal exchanges and comment civility did not help to explain users' decision to return to the commenting system; indeed, civility did not increase after the design change and reciprocity declined. These analyses show that even simple design choices can have a significant impact on news forums' stickiness. Further, they suggest that this influence is more powerfully shaped by affordances---the new system made responding easier---than by changes in users' attention to social norms of reciprocity or civility. This has an array of implications for designers.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134662"}, {"primary_key": "3673885", "vector": [], "sparse_vector": [], "title": "Welcome to the First Issue of PACMHCI EICS.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Proceedings of the ACM (PACM) was initiated by ACM in 2015 as overarching framework for publishing high quality computer science research. The goal for these new journals is to provide an alternate journal publication model for rigorous research papers that have traditionally been presented at major ACM conferences. PACM titles cross multiple intellectual communities, and each separate PACM is designed to represent a broad, but consistent, reach area. This is the first issue of the Proceedings of the ACM on Human Computer Interaction (PACMHCI), which represents the varied topics and communities that compose the broader study of Human Computer Interaction (HCI). The rich heterogeneity of this field can be expressed as deep ethnographies of information use in context, to experiments showing the effectiveness of interface designs, to the production of new technologies that push the limits of how we interact with computers, and much more. The production of PACMHCI will focus on content associated with major research communities that are supported by the ACM Special Interest Group on Human-Computer Interaction (SIGCHI). Individual issues will be largely associated with separate research communities, who may then also select papers from the issue for presentation at their major conferences. These research communities provide the volunteers and editors necessary to provide the rigorous review and editorial process that will define this journal. Those editors will serve on the overall board for ACMHCI, to help bridge our diverse communities. Leveraging our research communities allows us to provide high-quality reviewing while maintaining the quick processing of work that is important in this quickly moving field. This inaugural issue of PACMHCI represents work from the Engineering Interactive Computing Systems (EICS) community. EICS gathers researchers that aim to improve the ways we build interactive systems. Building interactive systems is a multi-faceted and challenging activity, involving a plethora of different actors and roles. This is particularly true in the domain of HCI, where we continuously push the edge of what is possible, where there is a crucial need for adequate processes, tools and methods to build reliable, useful and usable systems that help people cope with the ever-increasing complexity of work and life. The primary goal of the EICS research community is to create novel and high quality contributions in this direction. Although there are only three articles in this first issue, our pipeline for future issues is promising. In the first submission cycle, 41 papers were submitted and, of those, 22 were asked for major revisions. We expect a good number of those 22 papers to be ultimately accepted over the coming months. We are grateful to our newly-formed Editorial Board consisting of more than 70 experts for lending their support and knowledge to the new journal. More information about PACMHCI can be found at http://pacmhci.acm.org/.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3106386"}, {"primary_key": "3673886", "vector": [], "sparse_vector": [], "title": "A More Intelligent Test Case Generation Approach through Task Models Manipulation.", "authors": ["<PERSON>", "<PERSON>", "Marcelo Go<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ensuring that an interactive application allows users to perform their activities and reach their goals is critical to the overall usability of the interactive application. Indeed, the effectiveness factor of usability directly refers to this capability. Assessing effectiveness is a real challenge for usability testing as usability tests only cover a very limited number of tasks and activities. This paper proposes an approach towards automated testing of effectiveness of interactive applications. To this end we resort to two main elements: an exhaustive description of users' activities and goals using task models, and the generation of scenarios (from the task models) to be tested over the application. However, the number of scenarios can be very high (beyond the computing capabilities of machines) and we might end up testing multiple similar scenarios. In order to overcome these problems, we propose strategies based on task models manipulations (e.g., manipulating task nodes, operator nodes, information...) resulting in a more intelligent test case generation approach. For each strategy, we investigate its relevance (both in terms of test case generation and in terms of validity compared to the original task models) and we illustrate it with a small example. Finally, the proposed strategies are applied on a real-size case study demonstrating their relevance and validity to test interactive applications.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3095811"}, {"primary_key": "3673887", "vector": [], "sparse_vector": [], "title": "Friends Don&apos;t Need Receipts: The Curious Case of Social Awareness Streams in the Mobile Payment App Venmo.", "authors": ["Monica <PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the inclusion of a social awareness stream (SAS) in the peer-to-peer payment app Venmo. While SASs are prominent in many social network sites, such as Facebook and Twitter, Venmo's use offers an illustrative example of how SASs can be used in task-oriented apps, particularly in a domain, finance, which people often view as sensitive. Through interviews with 14 Venmo users and surveys of 164 peer-to-peer payment app users and 80 Venmo users, we find uses consistent with other SASs and uncover novel uses that reflect the unusual inclusion of an SAS within a utilitarian app for personal finance. For many users, the SAS is a flexible feature that creates an experience that blends their task-driven use with social benefits. People write purely functional transaction descriptions with strangers, while in transactions with friends, they sometimes craft playful descriptions that enhance their experience or perform their social relationships. The SAS provides opportunities for learning about how to use the application and for keeping up with friends. The results of this study extend the CSCW community's knowledge of SASs and offer guidance to designers considering use of SAS in a variety of applications.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134663"}, {"primary_key": "3673888", "vector": [], "sparse_vector": [], "title": "Seeing Sound: Investigating the Effects of Visualizations and Complexity on Crowdsourced Audio Annotations.", "authors": ["<PERSON>", "<PERSON><PERSON>na Seals", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Oded Nov"], "summary": "Audio annotation is key to developing machine-listening systems; yet, effective ways to accurately and rapidly obtain crowdsourced audio annotations is understudied. In this work, we seek to quantify the reliability/redundancy trade-off in crowdsourced soundscape annotation, investigate how visualizations affect accuracy and efficiency, and characterize how performance varies as a function of audio characteristics. Using a controlled experiment, we varied sound visualizations and the complexity of soundscapes presented to human annotators. Results show that more complex audio scenes result in lower annotator agreement, and spectrogram visualizations are superior in producing higher quality annotations at lower cost of time and human labor. We also found recall is more affected than precision by soundscape complexity, and mistakes can be often attributed to certain sound event characteristics. These findings have implications not only for how we should design annotation tasks and interfaces for audio data, but also how we train and evaluate machine-listening systems.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134664"}, {"primary_key": "3673889", "vector": [], "sparse_vector": [], "title": "Tabloids in the Era of Social Media?: Understanding the Production and Consumption of Clickbaits in Twitter.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the growing shift towards news consumption primarily through social media sites like Twitter, most of the traditional as well as new-age media houses are promoting their news stories by tweeting about them. The competition for user attention in such mediums has led many media houses to use catchy sensational form of tweets to attract more users - a process known as clickbaiting. In this work, using an extensive dataset collected from Twitter, we analyze the social sharing patterns of clickbait and non-clickbait tweets to determine the organic reach of such tweets. We also attempt to study the sections of Twitter users who actively engage themselves in following clickbait and non-clickbait tweets. Comparing the advent of clickbaits with the rise of tabloidization of news, we bring out several important insights regarding the news consumers as well as the media organizations promoting news stories on Twitter.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134665"}, {"primary_key": "3673890", "vector": [], "sparse_vector": [], "title": "You Can&apos;t Stay Here: The Efficacy of Reddit&apos;s 2015 Ban Examined Through Hate Speech.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In 2015, Reddit closed several subreddits-foremost among them r/fatpeoplehate and r/CoonTown-due to violations of Reddit's anti-harassment policy. However, the effectiveness of banning as a moderation approach remains unclear: banning might diminish hateful behavior, or it may relocate such behavior to different parts of the site. We study the ban of r/fatpeoplehate and r/CoonTown in terms of its effect on both participating users and affected subreddits. Working from over 100M Reddit posts and comments, we generate hate speech lexicons to examine variations in hate speech usage via causal inference methods. We find that the ban worked for Reddit. More accounts than expected discontinued using the site; those that stayed drastically decreased their hate speech usage-by at least 80%. Though many subreddits saw an influx of r/fatpeoplehate and r/CoonTown \"migrants,\" those subreddits saw no significant changes in hate speech usage. In other words, other subreddits did not inherit the problem. We conclude by reflecting on the apparent success of the ban, discussing implications for online moderation, Reddit and internet communities more broadly.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134666"}, {"primary_key": "3673891", "vector": [], "sparse_vector": [], "title": "By the Community &amp; For the Community: A Deep Learning Approach to Assist Collaborative Editing in Q&amp;A Sites.", "authors": ["Chunyang Chen", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Community edits to questions and answers (called post edits) plays an important role in improving content quality in Stack Overflow. Our study of post edits in Stack Overflow shows that a large number of edits are about formatting, grammar and spelling. These post edits usually involve small-scale sentence edits and our survey of trusted contributors suggests that most of them care much or very much about such small sentence edits. To assist users in making small sentence edits, we develop an edit-assistance tool for identifying minor textual issues in posts and recommending sentence edits for correction. We formulate the sentence editing task as a machine translation problem, in which an original sentence is \"translated\" into an edited sentence. Our tool implements a character-level Recurrent Neural Network (RNN) encoder-decoder model, trained with about 6.8 millions original-edited sentence pairs from Stack Overflow post edits. We evaluate our edit assistance tool using a large-scale archival post edits, a field study of assisting a novice post editor, and a survey of trusted contributors. Our evaluation demonstrates the feasibility of training a deep learning model with post edits by the community and then using the trained model to assist post editing for the community.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134667"}, {"primary_key": "3673892", "vector": [], "sparse_vector": [], "title": "When to say &quot;Enough is Enough!&quot;: A Study on the Evolution of Collaboratively Created Process Models.", "authors": ["Irene-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Organizations conduct series of face-to-face meetings aiming to improve work practices. In these meetings, participants from different backgrounds collaboratively design artifacts, such as knowledge or process maps. Such meetings are orchestrated and carried out by facilitators and the success of the meetings almost solely depends on the experience of the facilitators. Previous research has mainly focused on approaches that support facilitators and participants in the upfront planning of such events. There is however, little guidance for facilitators and participants once a meeting has started. One critical aspect -- among others -- is that during a meeting, the facilitator and participants need to decide for how long the iterative process of discussion and design should continue. We argue that we can provide support for such decisions based on the evolution of artifacts collaboratively created during such meetings. This paper presents a multi-level, multi-method analysis of artifacts based on experts' observations in combination with network analytics. We study the use of automated analytics to assess the evolution of collaboratively created artifacts and to indicate maturity and established consensus of the collaborative practice. We propose a computational approach to support facilitators and participants in deciding when to stop face-to-face meetings.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134668"}, {"primary_key": "3673893", "vector": [], "sparse_vector": [], "title": "Korero: Facilitating Complex Referencing of Visual Materials in Asynchronous Discussion Interface.", "authors": ["<PERSON> <PERSON><PERSON>", "Toni-<PERSON>", "Dongwook Yoon", "<PERSON><PERSON>", "Sheng<PERSON> Zhao"], "summary": "In asynchronous online discussions, users actively reference visual materials (e.g., video, document) to provide supporting evidence and additional context. However, creating and comprehending complex references can be challenging, especially when there are multiple referents to refer, or when a referent is highly specific (e.g., specific sentences in a paper rather than the paper as a whole). To identify users' challenges in making references with multiple and specific referents while using existing discussion tools, we conducted an observational study and a preliminary interview. Based on the design lessons, we built Korero, a discussion interface that aims to facilitate complex referencing actions. For evaluation, we compared Korero against conventional interfaces in two user studies with referencing tasks of different referential difficulty. We found that Korero not only significantly reduces the time and effort in making references with multiple and specific referents, but also shows potential in increasing users' engagement with the discussion and referent materials.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134669"}, {"primary_key": "3673894", "vector": [], "sparse_vector": [], "title": "Privacy Leakage in Event-based Social Networks: A Meetup Case Study.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Jinyoung Han", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Event-based social networks (EBSNs) are increasingly popular since they provide platforms on which online and offline activities are combined. Despite the increasing interest in EBSNs, little research has paid attention to the privacy issues coming from the unique features of EBSNs; the on-site information of users is highly relevant to real lives. In this paper, we try to investigate privacy leakages in Meetup, one of the most popular EBSN service. More specifically, we answer what private information can be inferred from the site's publicly available data. To this end, we conduct a measurement study by crawling webpages from Meetup containing 240K groups, 8.9M users, 27M group affiliations and 78M topical interests. By analyzing the dataset, we find that LGBT status of users, which is one of the most sensitive privacy information, can be predicted with 93% accuracy. Finally we discuss the cause of the privacy leakage on EBSNs and its possible ensuing damages.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134670"}, {"primary_key": "3673895", "vector": [], "sparse_vector": [], "title": "Identifying Misaligned Inter-Group Links and Communities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many social media systems explicitly connect individuals (e.g., Facebook or Twitter); as a result, they are the targets of most research on social networks. However, many systems do not emphasize or support explicit linking between people (e.g., Wikipedia or Reddit), and even fewer explicitly link communities. Instead, network analysis is performed through inference on implicit connections, such as co-authorship or text similarity. Depending on how inference is done and what data drove it, different networks may emerge. While correlated structures often indicate stability, in this work we demonstrate that differences, or misalignment, between inferred networks also capture interesting behavioral patterns. For example, high-text but low-author similarity often reveals communities \"at war\" with each other over an issue or high-author but low-text similarity can suggest community fragmentation. Because we are able to model edge direction, we also find that asymmetry in degree (in-versus-out) co-occurs with marginalized identities (subreddits related to women, people of color, LGBTQ, etc.). In this work, we provide algorithms that can identify misaligned links, network structures and communities. We then apply these techniques to Reddit to demonstrate how these algorithms can be used to decipher inter-group dynamics in social media.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134672"}, {"primary_key": "3673897", "vector": [], "sparse_vector": [], "title": "Community Engagement Triage: Lightweight Prompts for Systematic Reviews.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Among platform designers there is still little actionable knowledge about how to foster community engagement, and new community platforms are likely to fail. Much research has documented effects that could inform better designs, however there is a lack of practical methods to review particular concerns. We design a lightweight method for systematic reviews of community engagement scenarios, based on a catalogue of 59 techniques, presented in the form of method cards, and co-developed with community engagement experts. We validate it in problem-solving workshops with organisers, and find that a full review is feasible in under 15 minutes, that it provides effective prompts and important guidance, and can successfully support process innovation. Finally, we validate it for use in summative evaluations of online communities. In a large-scale observational study of 1 million Flickr users, application of the method reveals the surprising impact of a major site redesign: it successfully increased content engagement, but at the expense of social interactions.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134674"}, {"primary_key": "3673899", "vector": [], "sparse_vector": [], "title": "Challenges in Transitioning from Civil to Military Culture: Hyper-Selective Disclosure through ICTs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>slim<PERSON> Akter", "<PERSON>", "<PERSON><PERSON>"], "summary": "A critical element for a successful transition is the ability to disclose, or make known, one's struggles. We explore the transition disclosure practices of Reserve Officers' Training Corps (ROTC) students who are transitioning from an individualistic culture to one that is highly collective. As ROTC students routinely evaluate their peers through a ranking system, the act of disclosure may impact a student's ability to secure limited opportunities within the military upon graduation. Through a qualitative interview study of active ROTC students (N=14) examining how they use information communication technologies (ICTs) to disclose their struggles in a hyper-competitive environment, we find they engage in a process of highly selective disclosure, choosing different groups with which to disclose based on the types of issues they face. We share implications for designing ICTs that better facilitate how ROTC students cope with personal challenges during their formative transition into the military.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134676"}, {"primary_key": "3673900", "vector": [], "sparse_vector": [], "title": "Suppressing the Search Engine Manipulation Effect (SEME).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A recent series of experiments demonstrated that introducing ranking bias to election-related search engine results can have a strong and undetectable influence on the preferences of undecided voters. This phenomenon, called the Search Engine Manipulation Effect (SEME), exerts influence largely through order effects that are enhanced in a digital context. We present data from three new experiments involving 3,600 subjects in 39 countries in which we replicate SEME and test design interventions for suppressing the effect. In the replication, voting preferences shifted by 39.0%, a number almost identical to the shift found in a previously published experiment (37.1%). Alerting users to the ranking bias reduced the shift to 22.1%, and more detailed alerts reduced it to 13.8%. Users' browsing behaviors were also significantly altered by the alerts, with more clicks and time going to lower-ranked search results. Although bias alerts were effective in suppressing SEME, we found that SEME could be completely eliminated only by alternating search results -- in effect, with an equal-time rule. We propose a browser extension capable of deploying bias alerts in real-time and speculate that SEME might be impacting a wide range of decision-making, not just voting, in which case search engines might need to be strictly regulated.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134677"}, {"primary_key": "3673901", "vector": [], "sparse_vector": [], "title": "Linguistic Markers Indicating Therapeutic Outcomes of Social Media Disclosures of Schizophrenia.", "authors": ["<PERSON><PERSON>", "Asra F. Rizvi", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Self-disclosure of stigmatized conditions is known to yield therapeutic benefits. Social media sites are emerging as promising platforms enabling disclosure around a variety of stigmatized concerns, including mental illness. What kind of behavioral changes precede and follow such disclosures? Do the therapeutic benefits of \"opening up\" manifest in these changes? In this paper, we address these questions by focusing on disclosures of schizophrenia diagnoses made on Twitter. We adopt a clinically grounded quantitative approach to first identify temporal phases around disclosure during which symptoms of schizophrenia are likely to be significant. Then, to quantify behaviors before and after disclosures, we define linguistic measures drawing from literature on psycholinguistics and the socio-cognitive model of schizophrenia. Along with significant linguistic differences before and after disclosures, we find indications of therapeutic outcomes following disclosures, including improved readability and coherence in language, future orientation, lower self preoccupation, and reduced discussion of symptoms and stigma perceptions. We discuss the implications of social media as a new therapeutic tool in supporting disclosures of stigmatized conditions.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134678"}, {"primary_key": "3673902", "vector": [], "sparse_vector": [], "title": "I Should Listen More: Real-time Sensing and Feedback of Non-Verbal Communication in Video Telehealth.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Video telehealth is growing to allow more clinicians to see patients from afar. As a result, clinicians, typically trained for in-person visits, must learn to communicate both health information and non-verbal affective signals to patients through a digital medium. We introduce a system called ReflectLive that senses and provides real-time feedback about non-verbal communication behaviors to clinicians so they can improve their communication behaviors. A user evaluation with 10 clinicians showed that the real-time feedback helped clinicians maintain better eye contact with patients and was not overly distracting. Clinicians reported being more aware of their non-verbal communication behaviors and reacted positively to summaries of their conversational metrics, motivating them to want to improve. Using ReflectLive as a probe, we also discuss the benefits and concerns around automatically quantifying the \"soft skills\" and complexities of clinician-patient communication, the controllability of behaviors, and the design considerations for how to present real-time and summative feedback to clinicians.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134679"}, {"primary_key": "3673904", "vector": [], "sparse_vector": [], "title": "Self-Tracking for Fertility Care: Collaborative Support for a Highly Personalized Problem.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Tera L. Reynolds", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Infertility is a global health concern that affects countless couples trying to conceive a child. Effective fertility treatment requires continuous monitoring of a wide range of health indicators through self-tracking. The process of collecting and interpreting data and information about fertility is complex, and much of the burden falls on women. In this study, we analyzed patient-generated content in a popular online health community dedicated to fertility issues. The objective was to understand the process in which women engage in tracking relevant information, and the challenges they face. Leveraging the Personal Informatics Model, we describe women's self-tracking experiences during their fertility cycles. We discuss how a complex and highly personalized context leads to responsibility, pressure, and emotional burden on women performing self-tracking activities, as well as the role of collaboration in creating individualized solutions. Finally, we provide implications for technologies aiming to support women with fertility care needs.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134671"}, {"primary_key": "3673905", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> and Expert Sensemaking of Crowdsourced Design Feedback.", "authors": ["Eureka Foong", "<PERSON>", "<PERSON>"], "summary": "Online feedback exchange (OFE) systems are an increasingly popular way to test concepts with millions of target users before going to market. Yet, we know little about how designers make sense of this abundant feedback. This empirical study investigates how expert and novice designers make sense of feedback in OFE systems. We observed that when feedback conflicted with frames originating from the participant's design knowledge, experts were more likely than novices to question the inconsistency, seeking critical information to expand their understanding of the design goals. Our results suggest that in order for OFE systems to be truly effective, they must be able to support nuances in sensemaking activities of novice and expert users.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134680"}, {"primary_key": "3673906", "vector": [], "sparse_vector": [], "title": "Digital Technologies and Intimate Partner Violence: A Qualitative Analysis with Multiple Stakeholders.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Digital technologies, including mobile devices, cloud computing services, and social networks, play a nuanced role in intimate partner violence (IPV) settings, including domestic abuse, stalking, and surveillance of victims by abusive partners. However, the interactions among victims of IPV, abusers, law enforcement, counselors, and others --- and the roles that digital technologies play in these interactions --- are poorly understood. We present a qualitative study that analyzes the role of digital technologies in the IPV ecosystem in New York City. Findings from semi-structured interviews with 40 IPV professionals and nine focus groups with 32 survivors of IPV reveal a complex set of socio-technical challenges that stem from the intimate nature of the relationships involved and the complexities of managing shared social circles. Both IPV professionals and survivors feel that they do not possess adequate expertise to be able to identify or cope with technology-enabled IPV, and there are currently insufficient best practices to help them deal with abuse via technology. We also reveal a number of tensions and trade-offs in negotiating technology's role in social support and legal procedures. Taken together, our findings contribute a nuanced understanding of technology's role in the IPV ecosystem and yield recommendations for HCI and technology experts interested in aiding victims of abuse.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134681"}, {"primary_key": "3673907", "vector": [], "sparse_vector": [], "title": "&quot;Control your emotions, Potter&quot;: An Analysis of Grief Policing on Facebook in Response to Celebrity Death.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As social media platforms become a larger part of sharing life, they have by necessity become a part of sharing death. In life, pop culture fans can have parasocial (one-sided, mediated) relationships with celebrities. Yet when fans of departed celebrities express their grief in public comment threads, conversations often result in disagreements about how to grieve. These disagreements consistently appear in response to the deaths of public figures, and have been broadly labeled \"grief policing.\" We performed a thematic analysis of public Facebook comments responding to the deaths of <PERSON>, <PERSON>, and <PERSON>. Our findings describe prominent grief policing practices and explain how commenters may be importing norms from other contexts when shared spaces consist of transient interactions that make norm formation difficult. Our findings contribute to a broader understanding of how conflicting norms affect discourse in transient online spaces. Approaching online incivility through a lens of conflicting social norm enforcement may open doors for improvements in public discourse online.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134682"}, {"primary_key": "3673908", "vector": [], "sparse_vector": [], "title": "Beyond Information Content: The Effects of Culture on Affective Grounding in Instant Messaging Conversations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Malte F. <PERSON>"], "summary": "When people communicate, their messages convey affect alongside informational content. The affective dimension of messages is often unclear and open to multiple interpretations especially in an intercultural context. Thus, interlocutors may or may not achieve a state of affective grounding in which each person's affective behaviors are correctly interpreted by his/her partners. The current study examines the effects of culture on affective grounding. We conducted a laboratory experiment in which pairs of participants, half from America (A) and half from China (C), collaborated over instant messaging (IM). We found that affective grounding was harder to achieve for AA and AC pairs, but easier for CC pairs. We propose several design solutions to facilitate affective grounding in remote collaborations.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134683"}, {"primary_key": "3673910", "vector": [], "sparse_vector": [], "title": "Operationalizing Conflict and Cooperation between Automated Software Agents in Wikipedia: A Replication and Expansion of &apos;Even Good Bots Fight&apos;.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This paper replicates, extends, and refutes conclusions made in a study published in PLoS ONE (\"Even Good Bots Fight\"), which claimed to identify substantial levels of conflict between automated software agents (or bots) in Wikipedia using purely quantitative methods. By applying an integrative mixed-methods approach drawing on trace ethnography, we place these alleged cases of bot-bot conflict into context and arrive at a better understanding of these interactions. We found that overwhelmingly, the interactions previously characterized as problematic instances of conflict are typically better characterized as routine, productive, even collaborative work. These results challenge past work and show the importance of qualitative/quantitative collaboration. In our paper, we present quantitative metrics and qualitative heuristics for operationalizing bot-bot conflict. We give thick descriptions of kinds of events that present as bot-bot reverts, helping distinguish conflict from non-conflict. We computationally classify these kinds of events through patterns in edit summaries. By interpreting found/trace data in the socio-technical contexts in which people give that data meaning, we gain more from quantitative measurements, drawing deeper understandings about the governance of algorithmic systems in Wikipedia. We have also released our data collection, processing, and analysis pipeline, to facilitate computational reproducibility of our findings and to help other researchers interested in conducting similar mixed-method scholarship in other platforms and contexts.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134684"}, {"primary_key": "3673911", "vector": [], "sparse_vector": [], "title": "Input Controls for Entering Uncertain Data: Probability Distribution Sliders.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Although more and more data is collected automatically, many interfaces still require manual input. When we, for example, enter our daily calorie intake or calculate our ecological footprint, we often have to guess the weight of the food or what distance we have covered with our car. In this paper, we propose a solution to overcome the problem of forcing users to enter a single value when they are unsure about the actual input. On the basis of a slider, we designed four input controls which allow the input of uncertain data in the form of probability distribution functions. To evaluate our input controls, we conducted two studies collecting subjective and objective feedback. Based on the evaluation, we derived implications for their usage. We additionally provide an open-source toolkit with the evaluated input controls that can be included in web applications and customized for different contexts and tasks.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3095805"}, {"primary_key": "3673912", "vector": [], "sparse_vector": [], "title": "Investigating Support Seeking from Peers for Pregnancy in Online Health Communities.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We report a study of peer support in online health communities for pregnancy care along three gestational stages (trimesters) to investigate how pregnant women seek and receive peer support during different stages of pregnancy. Using Babycenter.com as our research setting, we found that pregnant women sought peer support due to constrained access to healthcare providers, dissatisfaction with healthcare services/medical advice, limited offline social support, and unavailability of information in other venues. While the particular topics of concern typifying each trimester were distinct, pregnant women consistently sought advice, informal and formal knowledge, reassurance, and emotional support from peers during each stage of pregnancy. BabyCenter.com peers provided support by leveraging their own experiential knowledge and passing along clinical expertise acquired during the course of their own healthcare. We discuss design implications for health services and IT systems that meet pregnant women's temporal and multi-faceted needs during prenatal care.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134685"}, {"primary_key": "3673913", "vector": [], "sparse_vector": [], "title": "Two Sides to Every Story: Mitigating Intercultural Conflict through Automated Feedback and Shared Self-Reflections in Global Virtual Teams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Global virtual teams experience intercultural conflict. Yet, research on how Computer-Mediated Communication (CMC) tools can mitigate such conflict is minimal. We conducted an experiment with 30 Japanese-Canadian dyads who completed a negotiation task over email. Dyads were assigned to one of three conditions: C1) no feedback; C2) automated language feedback of participant emails based on national culture dimensions; and C3) automated language feedback (as in C2), and participants' shared self-reflections of that feedback. Results show Japanese and Canadian partners interpreted the negotiation task differently, resulting in perceptions of intercultural conflict and negative impressions of their partner. Compared to C1, automated language feedback (C2) and shared self-reflections (C3) made cultural differences more salient, motivating participants to empathize with their partner. Shared self-reflections (C3) served as a meta-channel to communication, providing insight into each partner's intentions and cultural values. We discuss implications for CMC tools to mitigate perceptions of intercultural conflict.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134686"}, {"primary_key": "3673915", "vector": [], "sparse_vector": [], "title": "Hacking with NPOs: Collaborative Analytics and Broker Roles in Civic Data Hackathons.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently Nonprofit organizations (NPOs) are adopting more and more data-driven approaches to their work, yet NPOs often lack appropriate tools and expertise in such data related works. To compensate, many NPOs are using a new form of collaboration, civic data hackathons, to leverage on external volunteers' data expertise. In this paper, we sought to understand how civic data hackathons could generate impactful data analytics for NPOs' data-driven work, and how to support collaborative data analytics during hackathons. We collected various types of data (observations, surveys, and interviews) from two civic data hackathons with 9 NPOs and over 300 data volunteers in a Midwestern city in the U.S. Our results describe the collaboration activities and the types of actionable collaborative analytics outputs generated from these activities. We also identify a unique social group (i.e., client teams), who help with preparing and coordinating the event, perform brokering activities to support the collaborative analytics through the civic data hackathons. This broker role is vital for the success of the collaboration between domain experts and data experts. Our findings contribute to the CSCW research on the collaborative work of interdisciplinary hackathons, and to a broader understanding of civic data collaborations.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134688"}, {"primary_key": "3673916", "vector": [], "sparse_vector": [], "title": "High-resolution Temporal Representations of Alcohol and Tobacco Behaviors from Social Media Data.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>nal <PERSON>", "<PERSON><PERSON>"], "summary": "Understanding tobacco- and alcohol-related behavioral patterns is critical for uncovering risk factors and potentially designing targeted social computing intervention systems. Given that we make choices multiple times per day, hourly and daily patterns are critical for better understanding behaviors. Here, we combine natural language processing, machine learning and time series analyses to assess Twitter activity specifically related to alcohol and tobacco consumption and their sub-daily, daily and weekly cycles. Twitter self-reports of alcohol and tobacco use are compared to other data streams available at similar temporal resolution. We assess if discussion of drinking by inferred underage versus legal age people or discussion of use of different types of tobacco products can be differentiated using these temporal patterns. We find that time and frequency domain representations of behaviors on social media can provide meaningful and unique insights, and we discuss the types of behaviors for which the approach may be most useful.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134689"}, {"primary_key": "3673917", "vector": [], "sparse_vector": [], "title": "Types of Motivation Affect Study Selection, Attention, and Dropouts in Online Experiments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Understanding whether and how motivation affects participation in online experiments is critical because who contributes and how they contribute can affect the validity of findings. Analyzing data from 7,674 participants across three different studies on the volunteer-based online experiment platform LabintheWild, we identified five motivation types for participating: boredom, comparison, fun, science, and self-learning. We found that these motivation types affect study selection, attention, and dropouts. Participants who were highly motivated by boredom paid less attention and were more likely to dropout than those who were motivated by the possibility of contributing to science. We additionally show that motivation can impact study results and suggest how researchers can take participants' motivation into account when designing and analyzing data from volunteer-based online experiments.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134691"}, {"primary_key": "3673918", "vector": [], "sparse_vector": [], "title": "Editor&apos;s Note/Chairs&apos; Welcome.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Welcome to this issue of the Proceedings of the ACM on Human-Computer Interaction, which will focus on contributions from the research community Computer-Supported Cooperative Work and Social Computing (CSCW). This diverse research community explores how different types of social groups affect, and are affected by, information and communication technology. The topics explored by this community can include social media use, crowdsourcing and micro-work, societal effects of computing, and much more. Like many other HCI communities, CSCW approaches these topics with a broad range of scientific techniques, theoretical perspectives and technology platforms. The call for papers for this issue on CSCW attracted 385 submissions, from Asia, Canada, Australia, Europe, Africa, and the United States. After the first round of reviewing, 207 (54%) papers were invited to the Revise and Resubmit phase. The editorial committee worked hard over August 2017 to arrive at final decisions, with a Virtual Committee meeting held to discuss those papers that needed collective deliberation. In the end, 105 papers (27%) were accepted. This issue exists because of the dedicated volunteer effort of 101 senior editors who served as Associate Chairs (ACs), and 885 expert reviewers to ensure high quality and insightful reviews for all papers in both rounds. Reviewers and committee members were kept constant for papers that submitted to both rounds. Senior members of the editorial group also helped shepherd some papers, reflecting the deep commitment of this research community. We are excited by the compelling and thought-provoking work that resulted in this PACMHCI CSCW issue and look forward to equally high quality submissions for the next submission cycle from this research community in the Spring of 2018. For those interested in this area, this group holds their next annual conference November 3-7, 2018 in New York City's Hudson River (Jersey City). That conference will provide many opportunities to share ideas with other researchers and practitioners from institutions around the world.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134651"}, {"primary_key": "3673919", "vector": [], "sparse_vector": [], "title": "Care as a Resource in Underserved Learning Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present results from an ethnographic inquiry of technology-based learning at an after-school learning center in Mumbai (India) that caters to students from neighboring slum communities.We conducted participant observation for 120 hours and 58 semi-structured interviews with different stakeholders (including teachers, staff, parents, and students) at the center over nine weeks from December 2015 to July 2016. Taking an assets-based approach in an underserved context, we uncover the role of care as a resource and present the rich and varied caring behaviors enacted in this sociotechnical system.We then discuss how care effects a greater sense of ownership, interdependency, and community. Examining the role of aligning values in motivating caring behavior, we conclude with recommendations for supporting, leveraging, and extending care via technology design in an underserved, technology-enhanced learning environment.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134739"}, {"primary_key": "3673920", "vector": [], "sparse_vector": [], "title": "Service Providers of the Sharing Economy: Who Joins and Who Benefits?", "authors": ["Qing Ke"], "summary": "Many \"sharing economy\" platforms, such as Uber and Airbnb, have become increasingly popular, providing consumers with more choices and suppliers a chance to make profit. They, however, have also brought about emerging issues regarding regulation, tax obligation, and impact on urban environment, and have generated heated debates from various interest groups. Empirical studies regarding these issues are limited, partly due to the unavailability of relevant data. Here we aim to understand service providers of the sharing economy, investigating who joins and who benefits, using the Airbnb market in the United States as a case study. We link more than 211 thousand Airbnb listings owned by 188 thousand hosts with demographic, socio-economic status (SES), housing, and tourism characteristics. We show that income and education are consistently the two most influential factors that are linked to the joining of Airbnb, regardless of the form of participation or year. Areas with lower median household income, or higher fraction of residents who have Bachelor's and higher degrees, tend to have more hosts. However, when considering the performance of listings, as measured by number of newly received reviews, we find that income has a positive effect for entire-home listings; listings located in areas with higher median household income tend to have more new reviews. Our findings demonstrate empirically that the disadvantage of SES-disadvantaged areas and the advantage of SES-advantaged areas may be present in the sharing economy.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134692"}, {"primary_key": "3673921", "vector": [], "sparse_vector": [], "title": "Quality Standards, Service Orientation, and Power in Airbnb and Couchsurfing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Although Couchsurfing and Airbnb are both online communities that help users host strangers in their homes, they differ in an important sense: Couchsurfing prohibits monetary payment while Airbnb is built around it.We conducted interviews with users experienced on both Couchsurfing and Airbnb (\"dual-users\") to better understand systemic differences between the platforms. Based on these interviews we propose that, compared to Couchsurfing, Airbnb: (1) appears to require higher quality services, (2) places more emphasis on places over people, and (3) shifts social power from hosts to guests. Using public profiles from both platforms, we present analyses exploring each theme. Finally, we present evidence showing that Airbnb's growth has coincided with a decline in Couchsurfing. Taken together, our findings paint a complex picture of the changing character of network hospitality.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134693"}, {"primary_key": "3673922", "vector": [], "sparse_vector": [], "title": "A Comparative Study of Visualizations with Different Granularities of Behavior for Communicating about Autism.", "authors": ["Ha Kyung Kong", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a comparative study of two webtools developed to capture engagement via visualization of coordinated communication behavior in children with autism. A clear preference arose for different tasks based on behavior granularity emphasis in the two visualizations. The survey and interview results further revealed the importance of showing behavior patterns, rather than displaying a single behavior without context, in behavior visualization. Based on the results, we propose three granularity-related features to incorporate into behavioral visualizations for communication in clinical settings: separating modalities, coordinating dyadic interactions, and displaying micro-behaviors.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134694"}, {"primary_key": "3673923", "vector": [], "sparse_vector": [], "title": "Supporting Distributed Critique through Interpretation and Sense-Making in an Online Creative Community.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Critique is an important component of creative work in design education and practice, through which individuals can solicit advice and obtain feedback on their work. Face-to-face critique in offline settings such as design studios has been well-documented and theorized. However, little is known about unstructured distributed critique in online creative communities where people share and critique each other's work, and how these practices might resemble or differ from studio critique. In this paper, we use mixed-methods to examine distributed critique practices in a UX-focused online creative community on Reddit. We found that distributed critique resembles studio critique categorically, but differs qualitatively. While studio critique often focuses on depth, distributed critique often revolved around collective sensemaking, through which creative workers engaged in iteratively interpreting, defining, and refining the artifact and their process. We discuss the relationship between distributed critique and socio-technical systems and identify implications for future research.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134695"}, {"primary_key": "3673924", "vector": [], "sparse_vector": [], "title": "Conspiracy Talk on Social Media: Collective Sensemaking during a Public Health Crisis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Conspiracy theories have gained much academic and media attention recently, due to their large impact on public events. Crisis informatics researchers have examined conspiracy theories as a type of rumor. However, little is known about how conspiracy theories are produced and developed on social media. We present a qualitative study of conspiracy theorizing on Reddit during a public health crisis--the Zika virus outbreak. Using a mixed-methods approach including content analysis and discourse analysis, we identified types of conspiracy theories that appeared on Reddit in response to the Zika crisis, the conditions under which Zika conspiracy theories emerge, and the particular discursive strategies through which Zika conspiracy theories developed in online forums. Our analysis shows that conspiracy talk emerged as people attempted to make sense of a public health crisis, reflecting their emergent information needs and their pervasive distrust in formal sources of Zika information. Practical implications for social computing researchers, health practitioners, and policymakers are discussed.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134696"}, {"primary_key": "3673925", "vector": [], "sparse_vector": [], "title": "Managing Disruptive Behavior through Non-Hierarchical Governance: Crowdsourcing in League of Legends and Weibo.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Disruptive behaviors such as flaming and vandalism have been part of the Internet since its beginning. Various models of hierarchical governance have been established and managed in different online venues, with both successes and failures. Recently, a new model of non-hierarchical governance has emerged using crowdsourcing technology to allow an online community to manage itself. How do people view and work with non-hierarchical governance? In this paper, we present an interview study with people from two sites: the video game League of Legends and Weibo, a microblogging site in China. We found that people were passionate about participation in crowdsourcing, but at the same time, struggled with the system, and acted beyond their designated role within the system. We derive implications for designing online non-hierarchical governance from our research.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134697"}, {"primary_key": "3673926", "vector": [], "sparse_vector": [], "title": "&apos;No Telling Passcodes Out Because They&apos;re Private&apos;: Understanding Children&apos;s Mental Models of Privacy and Security Online.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Children under age 12 increasingly use Internet-connected devices to go online. And while Internet use exposes people to privacy and security risks, few studies examine how these children perceive and address such concerns. To fill this gap, we conducted a qualitative study of 18 U.S. families with children ages 5-11. We found that children recognized certain privacy and security components from the contextual integrity framework, but children ages 5-7 had gaps in their knowledge. Children developed some strategies to manage concerns but largely relied on parents for support. Parents primarily used passive strategies to mediate children's device use and largely deferred teaching children about these concerns to the future. We argue that helping children develop strong privacy and security practices at a young age will prepare them to manage their privacy and security as adolescents and adults. We offer recommendations to scaffold children's learning on privacy and security.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134699"}, {"primary_key": "3673927", "vector": [], "sparse_vector": [], "title": "Increasing Quality and Involvement in Online Peer Feedback Exchange.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Design instructors are integrating the use of online peer review platforms to keep pace with growing class sizes. However, these platforms typically prioritize randomized peer assignment strategies and show only the current solution to peers when writing feedback. This can result in low quality feedback in project-based design courses. We report on an experiment in which students (N=59) worked on twelve-week design projects and both wrote and received online feedback at four stages. The experiment tested a novel assignment strategy of peer mentorship, where peers were assigned to give feedback to all stages of the same project, and tested showing the context from the preceding design stage when composing feedback. The results showed that displaying the context from the preceding design stage led to feedback with higher perceived quality at the late design stages (but not at earlier stages) and feedback from mentors prompts longer responses from the feedback recipients. Our work contributes deeper empirical understanding of how assignment strategies and showing additional context affects peer feedback and provides practical guidelines for instructors to implement these methods in design courses.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134698"}, {"primary_key": "3673928", "vector": [], "sparse_vector": [], "title": "Upvote My News: The Practices of Peer Information Aggregation for Breaking News on reddit.com.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Citizen participation in crisis communication increasingly occurs in social media contexts. As some platforms -- e.g., social news sites -- evolve around collaborative voting, filtering, and information sharing, the aggregation of breaking news information during crisis situations appears more often as an emergent practice in these online communities. Drawing from 53 interviews and descriptive quantitative analysis of reddit posts and comments, this paper presents a qualitative case study examining reddit.com members aggregate information during crisis events within the context of reddit's post/comment structure, crowd voting, and ranking algorithms. Using the lens of network gatekeeping, the paper shows how participants evaluate sources, organize information, and verify details to demonstrate how different affordances and limitations of information production allow or restrict particular types of network gatekeeping.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134700"}, {"primary_key": "3673929", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>-Pull-Action: Hand-based Digital Puppetry.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The human hand dexterity provides a natural and rich interface for computer interaction. Technologies for tracking hand and finger motion are now accessible and can be used with a minimum computation power for the manipulation of virtual puppets in real-time. However creating an intuitive hand-based motion con- trol interface presents problems such as, mapping the hand to an object that demands more degrees of freedom or to assign gestures that are difficult to memorize or to execute. We propose an ergonomic hand- mapping model for digital puppetry, based on the human hand anatomy and biomechanics, adapting tradi- tional puppetry methods. A cinematic virtual puppetry application was developed supporting distinct in- teraction styles based on the hand dexterity skills. An experiment using the Leap Motion controller was conducted to evaluate the hand mapping feasibility. The participants considered that the proposed interface provides a good level of directness. Furthermore, a custom interface for smartphones was combined with the application to extend the puppet manipulation and show control operations.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3095804"}, {"primary_key": "3673930", "vector": [], "sparse_vector": [], "title": "The Effect of Emotional Cues from the NFL on Wikipedia Contributions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Exploiting evidence that sporting results affect fans' mood, we analyze whether National Football League game outcomes can affect the contributions of Wikipedia editors who identify as fans of a specific team. We find that the day after a team loses, their fans decrease their contributions towards football-related pages (relative to after a win). Relative decreases are bigger if losses are unexpected, or if losing margins are big. In contrast, unexpected wins do not cause more contributions relative to wins that were not unexpected. Neither do big wins result in more contributions relative to small wins. Additionally, contributions to non-football-related pages are not affected by NFL game results. Our findings add to the literatures on (i) the determinants of individual contributions to peer production communities, (ii) how community dynamics affect user contributions, (iii) the importance of emotions, (iv) the effect of offline events on online behavior, and (v) the applicability of behavioral economics concepts to the HCI literature.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134701"}, {"primary_key": "3673931", "vector": [], "sparse_vector": [], "title": "Effectiveness and Users&apos; Experience of Obfuscation as a Privacy-Enhancing Technology for Sharing Photos.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Hong<PERSON> Hu", "<PERSON>"], "summary": "Current collaborative photo privacy protection solutions can be categorized into two approaches: controlling the recipient, which restricts certain viewers' access to the photo, and controlling the content, which protects all or part of the photo from being viewed. Focusing on the latter approach, we introduce privacy-enhancing obfuscations for photos and conduct an online experiment with 271 participants to evaluate their effectiveness against human recognition and how they affect the viewing experience. Results indicate the two most common obfuscations, blurring and pixelating, are ineffective. On the other hand, inpainting, which removes an object or person entirely, and avatar, which replaces content with a graphical representation are effective. From a viewer experience perspective, blurring, pixelating, inpainting, and avatar are preferable. Based on these results, we suggest inpainting and avatar may be useful as privacy-enhancing technologies for photos, because they are both effective at increasing privacy for elements of a photo and provide a good viewer experience.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134702"}, {"primary_key": "3673932", "vector": [], "sparse_vector": [], "title": "Distraction or Life Saver?: The Role of Technology in Undergraduate Students&apos; Boundary Management Strategies.", "authors": ["Hajin Lim", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous research has shown that communication technologies may make it challenging for working professionals to manage the boundaries between their work life and home life. For college students, however, there is a less clear definition of what constitutes work and what constitutes home life. As a result, students may use different boundary management strategies than working professionals. To explore this issue, we interviewed 29 undergraduates about how they managed boundaries between different areas of their life. Interviewees reported maintaining flexible and permeable boundaries that are not bounded physically or temporally. They used both technological and non-technological strategies to manage different life spheres. Interviewees saw technology as a major source of boundary violations but also as a boundary managing strategy that allowed them to achieve better life balance. Based on these findings, we propose design implications for tools to better support the boundary management processes of undergraduate students.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134703"}, {"primary_key": "3673933", "vector": [], "sparse_vector": [], "title": "Making Sense of Foreign Language Posts in Social Media.", "authors": ["Hajin Lim", "<PERSON>"], "summary": "Many people's social media feeds include posts in languages they do not understand. While previous research has examined bilingual social media users' language choices, little research has focused on how people make sense of foreign language posts. In the present study, we interviewed 23 undergraduate social media users about how they consume and make sense of posts in other languages. Interviewees reported that they often did not pay attention to or engage with foreign language posts, due to a lack of relevance and contextual knowledge. When they did actively engage with foreign language posts, interviewees did not rely solely on machine translation output but instead actively collected and combined various cues from within and outside the post in order to understand what it was about. Interviewees further reported different types of goals for trying to make sense of foreign language posts; some focused on simply extracting and understanding the emotional components of a post while others tried to gain a fuller understanding of a post, including its contextual and cultural meanings. Based on these findings, we suggest design possibilities that could better aid multilingual communication in social media.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134704"}, {"primary_key": "3673934", "vector": [], "sparse_vector": [], "title": "Tinkering with Governance: Technopolitics and the Economization of Citizenship.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "From statistics and mapping to engineering and medicine, technology has long been a tool of governance, shaping how nation states control their own and other regions' populations and natural resources. More recently, a nation's capacity to cultivate citizens as tech innovators and entrepreneurs is considered an indicator of its economic prosperity and global power. We show how this turn towards innovation and entrepreneurship is central to how technological and political elites tinker with new modes of governance and define the relationship between the future of the nation and its citizenry in economic terms. Drawing from long-term multi-sited ethnographic research in the United States, China, and Africa, we present a subset of findings to show how these shifts in governance are being enacted through interconnections between politicians and technologists, and the products, spaces, and educational ideals they fund and create. We argue that making sense of these shifts in governance is essential for current and future CSCW scholarship as they intersect with issues of power within social computing.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134705"}, {"primary_key": "3673935", "vector": [], "sparse_vector": [], "title": "Can Biosignals be Expressive?: How Visualizations Affect Impression Formation from Shared Brain Activity.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We are exploring the concept of expressive biosignals: leveraging wearable technologies to introduce sensed physiological data as cues for social perception. Biosignals can help us achieve a deeper understanding of each other by revealing or clarifying the psychological processes that underlie our subjective experience. We conducted an exploratory study investigating expressive biosignals, comparing the influence of a variety of brain activity visualizations on impression formation. Results revealed that while participants readily infer emotional and cognitive states from visualized brain activity, the ambiguity of the data can lead to diverse perceptions and interpretations. Participants also expressed concerns that the observation of another individual's data during interaction might be invasive or distracting. We present a set of design considerations addressing issues of interpretability, integration, and privacy of biosignals in interpersonal contexts.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134706"}, {"primary_key": "3673936", "vector": [], "sparse_vector": [], "title": "Selfies as Social Movements: Influences on Participation and Perceived Impact on Stereotypes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A new kind of online movement has emerged on social media: identity hashtag movements, through which individuals share \"selfies\" and personal stories to elucidate the experiences of marginalized social groups. These movements have the potential to counteract bias and enable social justice, but are enacted in a forum rife with identity and boundary management concerns. To understand this type of movement, we present a qualitative study of #ILookLikeAnEngineer, a hashtag created to challenge engineering stereotypes. We interviewed 32 people, including participants and non-participants of the movement, about their experiences with the hashtag. We found that personally identifiable participation promoted feelings of empowerment and strengthened connections within the marginalized community. At the same time, the personal and professional identity focus raised ambiguity about the boundaries of the collective and the movement's ability to change stereotypes. We discuss implications for online collective action and the use of social media for addressing stereotypes.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134707"}, {"primary_key": "3673937", "vector": [], "sparse_vector": [], "title": "Write for Life: Persisting in Online Health Communities through Expressive Writing and Social Support.", "authors": ["Haiwei <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Expressive writing has been shown to improve physical, mental, and social health outcomes for patients struggling with difficult diagnoses. In many online health communities, writing comprises a substantial portion of the user experience, yet little work has explored how writing itself affects user engagement. This paper explores user engagement on CaringBridge, a prominent online community for writing about personal health journeys. We build a survival analysis model, defining a new set of variables that operationalize expressive writing, and comparing these effects to those of social support, which are well-known to benefit user engagement. Furthermore, we use machine learning methods to estimate that approximately one third of community members who self-identify with a cancer condition cease engagement due to literal death. Finally, we provide quantitative evidence that: (1) receiving support, expressive writing, and giving support, in decreasing magnitude of relative impact, are associated with user engagement on CaringBridge, and (2) that considering deceased sites separately in our analysis significantly shifts our interpretations of user behavior.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134708"}, {"primary_key": "3673938", "vector": [], "sparse_vector": [], "title": "COPSE: Rapidly Instantiating Problem Solving Activities based on Tangible Tabletop Interfaces.", "authors": ["Valérie <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Collaborative problem solving is a skill that has become very important in our everyday lives and is constantly gaining attention in educational settings. In this paper, we present COPSE: a novel and unique software framework for instantiating Microworlds as collaborative problem solving activities on tangible tabletop interfaces. The framework provides three types of building blocks: widgets (provide input and localized feedback), equations (define the model), and scenes (visualize feedback), which can be specified in the form of structured text. Aim of COPSE is to simplify processes of creating, adjusting, and reusing custom Microworlds scenarios. We describe the structure of the framework, provide an example of a scenario, and report on a case study where we have used COPSE together with 33 teachers to build new scenarios on the fly.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3095808"}, {"primary_key": "3673939", "vector": [], "sparse_vector": [], "title": "When Distribution of Tasks and Skills are Fundamentally Problematic: A Failure Story from Global Software Outsourcing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Using ethnographic data, we provide a critical reflection on the discrepancies between the application of agile development principles and the conditions which render these principles effective for global software development work. This reflection is based on the analysis of a failed collaboration within a global software project, which relied heavily on feedback from mundane project tools utilized for everyday coordination and monitoring. Our study reveals that these tools hid serious issues relating to both the distribution of socio-technical skills and a discharge of accountability in task execution. As a result, markers of complex collaborative problems were concealed. Furthermore, the imbalance evident in outsourcing setups, which is enacted through high and low status task distribution among partners, further compounds collaboration problems by emphasizing assumptions about remote workers in the absence of direct forms of knowledge interchange.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3139336"}, {"primary_key": "3673940", "vector": [], "sparse_vector": [], "title": "Would You Slack That?: The Impact of Security and Privacy on Cooperative Newsroom Work.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Journalistic work is increasingly conducted using cooperative technologies. But while journalists need security and privacy just like professionals in sectors like health and education, constrained finances and missing legal requirements cause journalists to rely mostly on third-party platforms for their professional communications. In this study, we analyze how journalists manage professional collaborations across myriad tasks and contexts, with a focus on how their security and privacy concerns may shape these behaviors. We find that journalists' relatively open information-sharing practices may reflect the strength of their informal intra-organizational networks, rather than their organizational commitment per se. Moreover, while journalists' like many workers' manage their security and privacy concerns by avoiding sensitive communications via certain channels, the physical shrinking newsrooms of is reducing opportunities for the face-to-face communications journalists rely on for secure, private, professional exchange. Journalists' self-censorship on collaborative platforms therefore warrants particular attention given journalism's essential role in the public sphere.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134710"}, {"primary_key": "3673941", "vector": [], "sparse_vector": [], "title": "Most Teens Bounce Back: Using Diary Methods to Examine How Quickly Teens Recover from Episodic Online Risk Exposure.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Cross-sectional research suggests that online risk exposure (e.g., cyberbullying, sexual solicitations, and explicit content) may negatively impact teens, increasing concerns over the risks teens are exposed to online. Yet, there has been little research as to how these experiences impact teens' mood over time, or how long these effects may last. To examine the effects of online risk exposure on mood, we asked 68 teens to report their weekly online risk experiences, emotions, and sense of well-being for two months. We found that teens experienced more negative emotions the week that they reported cyberbullying and exposure to explicit content, but these effects were gone one week later. In addition, teens reported a slight in crease in positive emotions and mental well-being during weeks they were exposed to other risks. Our results suggest that most of the risks teens in our study experienced online only pose brief negative effects, if any, and initiates a discussion on how our society may overly problematize the negative effects of online risk exposure on teens.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134711"}, {"primary_key": "3673942", "vector": [], "sparse_vector": [], "title": "A Nine-Item Questionnaire for Measuring the Social Disfordance of Mediated Social Touch Technologies.", "authors": ["Kenya Mejia", "<PERSON><PERSON><PERSON>"], "summary": "Mediated Social Touch (MST) technologies focus on enhancing a communication experience by sensing, transmitting, and simulating social touch between remote partners. With interest in developing MST technologies continuing to grow, it is important to create standardized methods for measuring the effect of these novel systems. We designed and validated a 9-item questionnaire to measure the \"Social Disfordance\" of Mediated Social Touch, with three scales that focus on Social Discomfort, Communicational Expressiveness, and Need for Additional Consideration. A high degree of \"social disfordance\" of an MST system signifies that it may not provide the appropriate social affordances for mediating touch in a particular context. The development of the Social Disfordance of Mediated Social Touch (SDMST) instrument included a systematic literature review, expert feedback, and think-out-loud piloting. Its refinement included an exploratory factor analysis with a subsequent reduction of questions and scales. We report its psychometric properties, including metrics of inter-item reliability, convergent validity, test-retest reliability, and concurrent validity, confirming that these properties are sufficient for future use. We conclude with examples of scoring, appropriate use, and a discussion of the limitations.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134712"}, {"primary_key": "3673943", "vector": [], "sparse_vector": [], "title": "Collocated Use of Imaging Systems in Coordinated Surgical Practice.", "authors": ["<PERSON>"], "summary": "In surgery, assemblages of imaging systems need to be managed, interfaced, and coordinated by a collocated team. In this paper, I untangle the different forms of imaging system interaction in this highly specialized multi-device environment (MDE). I show how the different forms of imaging systems affect the work practice by creating a need for additional articulation work. This articulation work is coordinated into the surgical practice on the body by either distributing the work across the team or by requiring a momentary pause of the surgical work for the entire team to attend to the articulation work to be done on or with the images. And we see how distributing the work across the team still requires a reintegration of the knowledge gleaned from the new information into the surgery itself. This presents an expansion of our understanding of how collocated teams function in MDEs that consist of purely shared displays.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134713"}, {"primary_key": "3673944", "vector": [], "sparse_vector": [], "title": "Retrospecting on Work and Productivity: A Study on Self-Monitoring Software Developers&apos; Work.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "One way to improve the productivity of knowledge workers is to increase their self-awareness about productivity at work through self-monitoring. Yet, little is known about expectations of, the experience with, and the impact of self-monitoring in the workplace. To address this gap, we studied software developers, as one community of knowledge workers. We used an iterative, user-feedback-driven development approach (N=20) and a survey (N=413) to infer design elements for workplace self-monitoring, which we then implemented as a technology probe called WorkAnalytics. We field-tested these design elements during a three-week study with software development professionals (N=43). Based on the results of the field study, we present design recommendations for self-monitoring in the workplace, such as using experience sampling to increase the awareness about work and to create richer insights, the need for a large variety of different metrics to retrospect about work, and that actionable insights, enriched with benchmarking data from co-workers, are likely needed to foster productive behavior change and improve collaboration at work. Our work can serve as a starting point for researchers and practitioners to build self-monitoring tools for the workplace.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134714"}, {"primary_key": "3673945", "vector": [], "sparse_vector": [], "title": "Spread of Employee Engagement in a Large Organizational Network: A Longitudinal Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Behavioral statescan be transferred to others, leading people to behave in ways similar to those around them. Can this phenomenon of behavioral contagion be seen in the workplace? Using employees' organizational social media data and their workplace hierarchical network structure, we studied contagion across a large multinational corporation, focusing on an important workplace behavior - employee engagement. We measured employees' engagement based on their word choice in organizational social media, and we applied a longitudinal statistical technique which controls for homophily, employees' traits and prior expressions of engagement. We found that engagement and disengagement spread from one employee to another with direct peers exerting the strongest influence. While engagement-spread was more powerful laterally among people at the same organizational level, disengagement-spread followed the vertical managerial chain. Further, we found that disengaged co-workers exerted a stronger influence on employee's future engagement compared to the engaged co-workers. Our results suggest the need for organizations to sense and address workplace disengagement promptly. Moreover, our findings offer opportunities for using workplace interventions to promote engagement and mitigate disengagement.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134716"}, {"primary_key": "3673946", "vector": [], "sparse_vector": [], "title": "Credibility and the Dynamics of Collective Attention.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today, social media provide the means by which billions of people experience news and events happening around the world. However, the absence of traditional journalistic gatekeeping allows information to flow unencumbered through these platforms, often raising questions of veracity and credibility of the reported information. Here we ask: How do the dynamics of collective attention directed toward an event reported on social media vary with its perceived credibility? By examining the first large-scale, systematically tracked credibility database of public Twitter messages (47M messages corresponding to 1,138 real-world events over a period of three months), we established a relationship between the temporal dynamics of events reported on social media and their associated level of credibility judgments. Representing collective attention by the aggregate temporal signatures of an event's reportage, we found that the amount of continued attention focused on an event provides information about its associated levels of perceived credibility. Events exhibiting sustained, intermittent bursts of attention were found to be associated with lower levels of perceived credibility. In other words, as more people showed interest during moments of transient collective attention, the associated uncertainty surrounding these events also increased.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134715"}, {"primary_key": "3673947", "vector": [], "sparse_vector": [], "title": "Stickers for Steps: A Study of an Activity Tracking System with Face-to-Face Social Engagement.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Many systems have been designed to study social aspects in physical activity tracking. In most, social functions are performed at a distance, such as posting comments and achievements, or via in-app leaderboards. We present an activity tracking app designed instead to encourage face-to-face encounters. Stickers for Steps seeks to recreate the experience of a physical sticker book, where digital 'stickers' are collected in an album, but where stickers are awarded for reaching activity targets. Users will accrue duplicate stickers, which can be swapped with other co-located users over a Bluetooth connection. We explore the usage of our app, reporting on a trial with 33 participants. We find that our app successfully encouraged groups of users to swap duplicates, review progress and to discuss their levels of activity. We provide design recommendations for future activity tracking systems that could incorporate face-to-face interactions.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134717"}, {"primary_key": "3673948", "vector": [], "sparse_vector": [], "title": "Preserving the Margins: Supporting Creativity and Resistance on Digital Participatory Platforms.", "authors": ["<PERSON>"], "summary": "Online participatory platforms like Wikipedia and Zooniverse are designed to welcome contributions from anyone, however, to contend with a high volume of contributions, a range of constraints are deployed that align opportunities for participation toward ends defined by the experts and leaders of such platforms. In this paper I draw on extensive ethnographic work to describe how users encounter and negotiate opportunities for participation on two participatory platforms, demonstrating how platforms can exhibit distinct spaces and opportunities for participation, in some cases heavily enforcing standards of practice defined by experts and leaders while also leaving room for emergent and even divergent and deviant behavior. In describing this tension between conditions of normative and deviant participation, I highlight the importance of supporting opportunities for deviant and emergent participation to occur, emphasizing that design which uniquely supports narrow modes of participation can prevent opportunities for more inclusionary practice and evolving objectives.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134718"}, {"primary_key": "3673949", "vector": [], "sparse_vector": [], "title": "Recurring Meetings: An Experiential Account of Repeating Meetings in a Large Organization.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Meetings are often seen solely as a site of collective work. However, as <PERSON> has noted, groups are concerned with much more than collective work. In this study we examine how individuals experience meetings, and ask what they do, why they do it, and how they feel about it. Our study focuses on recurring meetings, both because recurring meetings are an ordinary aspect of organization life, and because their routine nature lends them a casual character that distinguishes them from one-time, issue-focused meetings. This paper analyzes accounts of 19 meetings and examines how various peripheral activities -- side-talk, side-tracking, multi-tasking, pre- and post-meeting talk -- have positive effects, as well as negative ones. We argue that viewing recurring meetings as a confluence of individual and collective aims suggests new approaches for designing technology that supports both meetings and participants.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134719"}, {"primary_key": "3673950", "vector": [], "sparse_vector": [], "title": "&quot;Who Has Plots?&quot;: Contextualizing Scientific Software, Practice, and Visualizations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Software is an integral element of the work of science yet it is not commonly an object of inquiry in studies of scientific infrastructures. This paper presents findings from an ethnographic study of a cosmology group's collaborative scientific software production. We demonstrate how these cosmologists use plots to simultaneously test their software and analyze data while interrogating multiple layers of infrastructural components. We broaden perspectives on scientific software development using a sociotechnical, software studies lens to examine this work of scientific discovery as a creative and embodied, yet exacting and methodical, activity that requires a 'human in the loop'. We offer a new reading of scientific software practices to convey how creating scientific software is often really the act of doing science itself--an intervention we believe is necessary to more successfully support scientific software sharing and infrastructure production.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134720"}, {"primary_key": "3673951", "vector": [], "sparse_vector": [], "title": "Synchronous Text Messaging: A Field Trial of Curtains Messenger.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We have created and evaluated a novel mobile messaging app named Curtains Messenger. The app has been designed to support synchrony in messaging. It does this by requiring users to be in the app at the same time as each other in order to send, receive and read messages. This design is contrary to typical apps where messages can be sent and read asynchronously at an individual's convenience. We have conducted a field trial in which 15 users installed the app on their own devices and used it in the wild. We present a qualitative analysis of interviews with the participants following the trial. The findings address how the app was used, how synchrony affected conversational flows, how synchrony raised issues of attention and intimacy, and what issues users faced in the practical work of conducting synchronous messaging. This work demonstrates how core concepts in the study of cooperative work such as a/synchrony can be drawn upon to reconsider taken-for-granted design features of mobile applications and the lived experience of communication.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134721"}, {"primary_key": "3673952", "vector": [], "sparse_vector": [], "title": "Filtered Out: Disability Disclosure Practices in Online Dating Communities.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we sought to understand the experiences and disability disclosure preferences of adults with and without disabilities who have dated online. Our 91 survey respondents expressed varying opinions about the need for potential partners to disclose disability status when online dating depending on the nature or perceived severity, with \"visible\" disabilities carrying a higher expectation of upfront disclosure than \"invisible\" disabilities. Many disabled respondents also described proactively disclosing as a technique to filter potential connections. Our findings suggest that individuals with disabilities must perform additional labor and navigate complex group norms in pursuit of personal connection. We advocate that the social computing research community consider how these processes are driven by both societal expectation and the constraints of online dating platforms. We then offer design considerations and open questions as a means to extend social computing study at the intersection of online dating and disability studies.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134722"}, {"primary_key": "3673953", "vector": [], "sparse_vector": [], "title": "Understanding Individual and Collaborative Problem-Solving with Patient-Generated Data: Challenges and Opportunities.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Making effective use of patient-generated data (PGD) is challenging for both patients and providers. Designing systems to support collaborative and individual use of PGD is a topic of importance in CSCW, considering the limitations of informatics tools. To inform better system design, we conducted a study including focus groups, observations and interviews with patients and providers to understand how PGD is interpreted and used. We found that while PGD is useful for identifying and solving disease-related problems, the following differences in patient-provider perceptions challenge its effective use - different perceptions about what is a problem, selecting what kinds of problems to focus on, and using different data representations. Drawing on these insights, we reflect on two specific conceptualizations of disease management behavior (sensemaking and problem-solving) as they relate to data specific activities of patients and providers and provide design suggestions for tools to support collaborative and individual use of PGD.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134723"}, {"primary_key": "3673954", "vector": [], "sparse_vector": [], "title": "No Workflow Can Ever Be Enough: How Crowdsourcing Workflows Constrain Complex Work.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The dominant crowdsourcing infrastructure today is the workflow, which decomposes goals into small independent tasks. However, complex goals such as design and engineering have remained stubbornly difficult to achieve with crowdsourcing workflows. Is this due to a lack of imagination, or a more fundamental limit? This paper explores this question through in-depth case studies of 22 workers across six workflow-based crowd teams, each pursuing a complex and interdependent web development goal. We used an inductive mixed method approach to analyze behavior trace data, chat logs, survey responses and work artifacts to understand how workers enacted and adapted the crowdsourcing workflows. Our results indicate that workflows served as useful coordination artifacts, but in many cases critically inhibited crowd workers from pursuing real-time adaptations to their work plans. However, the CSCW and organizational behavior literature argues that all sufficiently complex goals require open-ended adaptation. If complex work requires adaptation but traditional static crowdsourcing workflows can't support it, our results suggest that complex work may remain a fundamental limitation of workflow-based crowdsourcing infrastructures.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134724"}, {"primary_key": "3673955", "vector": [], "sparse_vector": [], "title": "Interpretations of Online Anonymity in Alcoholics Anonymous and Narcotics Anonymous.", "authors": ["Sabirat Rubya", "<PERSON><PERSON><PERSON>"], "summary": "How do individuals in twelve-step fellowships like Alcoholics Anonymous (AA) and Narcotics Anonymous (NA) interpret and enact \"anonymity?\" In this paper, we answer this question through a mixed-methods investigation. Through secondary analysis of interview data from 26 participants and an online questionnaire (N=285) we found three major interpretations of anonymity among AA and NA members: \"unidentifiability,\" \"social contract,\" and \"program over individual.\" While unidentifiability has been the focus of computing investigations, the other interpretations provide a significant and novel lens on anonymity. To understand how and when the unidentifiability interpretation was most likely to be enacted, we conducted a quantitative analysis of traces of activity in a large online recovery community. We observed that members were less likely to enact \"unidentifiability\" if they were more connected to the particular community and had more time in recovery. We provide implications for future research on context-specific anonymity and implications for design in online recovery spaces and similar sensitive contexts.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134726"}, {"primary_key": "3673956", "vector": [], "sparse_vector": [], "title": "Modeling Stress with Social Media Around Incidents of Gun Violence on College Campuses.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Stress constitutes a persistent wellbeing challenge to college students, impacting their personal, social, and academic life. However, violent events on campuses may aggravate student stress, due to the induced fear and trauma. In this paper, leveraging social media as a passive sensor of stress, we propose novel computational techniques to quantify and examine stress responses after gun violence on college campuses. We first present a machine learning classifier for inferring stress expression in Reddit posts, which achieves an accuracy of 82%. Next, focusing on 12 incidents of campus gun violence in the past five years, and social media data gathered from college Reddit communities, our methods reveal amplified stress levels following the violent incidents, which deviate from usual stress patterns on the campuses. Further, distinctive temporal and linguistic changes characterize the campus populations, such as reduced cognition, higher self pre-occupation and death-related conversations. We discuss the implications of our work in improving mental wellbeing and rehabilitation efforts around crisis events in college student populations.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134727"}, {"primary_key": "3673957", "vector": [], "sparse_vector": [], "title": "User Interface Optimization using Genetic Programming with an Application to Landing Pages.", "authors": ["Paulo Salem"], "summary": "The design of user interfaces (UIs), such as World Wide Web pages, usually consists in a human designer mapping one particular problem (e.g., the demands of a customer) to one particular solution (i.e., the UI). In this article, a technology based on Genetic Programming is proposed to automate critical parts of the design process. In this approach, designers are supposed to define basic content elements and ways to combine them, which are then automatically composed and tested with real users by a genetic algorithm in order to find optimized compositions. Such a strategy enables the exploration of large design state-spaces in a systematic manner, hence going beyond traditional A/B testing approaches. In relation to similar techniques also based on genetic algorithms, this system has the advantage of being more general, providing the basis of an overall programmatic UI design workflow, and of calculating the fitness of solutions incrementally. To illustrate and evaluate the approach, an experiment based on the optimization of landing pages is provided. The empirical result obtained, though preliminary, is statistically significant and corroborates the hypothesis that the technique works.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3099583"}, {"primary_key": "3673958", "vector": [], "sparse_vector": [], "title": "WeBrowse: Leveraging User Clicks for Content Discovery in Communities of a Place.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "One of the limits of web content discovery tools, let them be recommender systems or content curation tools such as social rating, social bookmarking and other social media, is the scarcity of user input (e.g. rate, submit, share). This problem is even worse in the case of what we call communities of a place: people who study, live or work at the same place. Such people often share common interests but either do not know each other or fail to actively engage in submitting and relaying information. In this paper, we investigate the feasibility of using the aggregated clicks of entire communities of users to passively emulate a content curation service a la Reddit. To this end, we prototype and deploy WeBrowse, a content curation service based on the processing of raw HTTP logs. Evaluation based on our deployments demonstrates feasibility at scale while respecting user privacy. The majority of WeBrowse's users welcome the quality of content it promotes.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134728"}, {"primary_key": "3673960", "vector": [], "sparse_vector": [], "title": "Never Too Old, Cold or Dry to Watch the Sky: A Survival Analysis of Citizen Science Volunteerism.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "CoCoRaHS is a multinational citizen science project for observing precipitation. Like many citizen science projects, volunteer retention is a key measure of engagement and data quality. Through survival analysis, we found that participant age (self-reported at account creation) is a significant predictor of retention. Compared to all other age groups, participants aged 60-70 are much more likely to sign up for CoCoRaHS, and to remain active for several years. We also measured the influence of task difficulty and the relative frequency of rain, finding small but statistically significant and counterintuitive effects. Finally, we confirmed previous work showing that participation levels within the first month are highly predictive of eventual retention. We conclude with implications for observational citizen science projects and crowdsourcing research in general.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134729"}, {"primary_key": "3673961", "vector": [], "sparse_vector": [], "title": "Inferring Individual Social Capital Automatically via Phone Logs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Social capital is one of the most fundamental concepts in social computing. Individual social capital is often connected with one's happiness levels, well-being, and propensity to cooperate with others. The dominant approach for quantifying individual social capital remains self-reported surveys and generator-methods, which are costly, attention-consuming, and fraught with biases. Given the important role played by mobile phones in mediating human social lives, this study explores the use of phone metadata (call and SMS logs) to automatically infer an individual's social capital. Based on <PERSON>' Social Capital survey as ground truth and ten-week phone data collection for 55 participants, we report that (1) multiple phone-based social features are intrinsically associated with social capital; and (2) analytics algorithms utilizing phone data can achieve high accuracy at automatically inferring an individual's bridging, bonding, and overall social capital scores. Results pave way for studying social capital and its temporal dynamics at an unprecedented scale.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134730"}, {"primary_key": "3673962", "vector": [], "sparse_vector": [], "title": "Drawing the Lines of Contention: Networked Frame Contests Within #BlackLivesMatter Discourse.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This research examines Twitter discourse related to #BlackLivesMatter and police-related shooting events in 2016 through a mixed-method, interpretative approach. We construct a \"shared audience graph\", revealing structural and ideological disparities between two groups of participants (one on the political left, the other on the political right). We utilize an integrated networked gatekeeping and framing lens to examine how #BlackLivesMatter frames were produced--and how they were contested --by separate communities of supporters and critics. Among other empirical findings, this work demonstrates hashtags being used in diverse ways--e.g. to mark participation, assert individual identity, promote group identity, and support or challenge a frame. Considered from a networked gatekeeping perspective, we illustrate how hashtags can serve as channeling mechanisms, shaping trajectories of information flow. This analysis also reveals a right-leaning community of BlackLivesMatter critics to have a more well-defined group of crowdsourced elite who largely define their side's counter-frame.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134920"}, {"primary_key": "3673963", "vector": [], "sparse_vector": [], "title": "The Gig Economy and Information Infrastructure: The Case of the Digital Nomad Community.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Ongoing discussions of the gig economy have focused on the critical aspect of digital mediation, and in particular the role of applications and platforms such as Uber or TaskRabbit. We extend this discussion by considering more decentralized contexts of gig economy, in which individuals do not rely on a single dominant, central intermediary, but rather exercise a higher degree of agency in arranging and aligning multiple digital platforms to support relevant work practices.We employ the concept of information infrastructure to describe the emergent configuration of heterogeneous digital platforms leveraged by digital nomads as a community of location-independent, remote workers. Using both forum analysis and in-depth interviews, we examine how the digital nomad community dynamically brings together and negotiates digital mediation in the form of an information infrastructure.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134732"}, {"primary_key": "3673964", "vector": [], "sparse_vector": [], "title": "Crowdcasting: Remotely Participating in Live Events Through Multiple Live Streams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We designed and developed a \"crowdcasting\" prototype to enable remote people to participate in a live event through a collection of live streams coming from the event. Viewers could select from a choice of streams and interact with the streamer and other viewers through text comments and heart reactions. We deployed the prototype in three live events: a Winterfest holiday festival, a local Women's March, and the South by Southwest festival. We found that viewers actively switched among a choice of streams from the event and actively interacted with each other, especially through text comments. Streams of walking about among exhibits or city sights elicited more user interaction than streams of lectures. We observed that voluntary viewers had a wider variation in how long they viewed the event, and switched among streams less and had less interaction through text comments compared to viewers recruited through Mechanical Turk.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134733"}, {"primary_key": "3673965", "vector": [], "sparse_vector": [], "title": "Gender Bias in the Job Market: A Longitudinal Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For millions of workers, online job listings provide the first point of contact to potential employers. As a result, job listings and their word choices can significantly affect the makeup of the responding applicant pool. Here, we study the effects of potentially gender-biased terminology in job listings, and their impact on job applicants, using a large historical corpus of 17 million listings on LinkedIn spanning 10 years. We develop algorithms to detect and quantify gender bias, validate them using external tools, and use them to quantify job listing bias over time. We then perform a user survey over two user populations (N 1=469 , N 2=273 ) to validate our findings and to quantify the end-to-end impact of such bias on applicant decisions. Our findings show gender-bias has decreased significantly over the last 10 years. More surprisingly, we find that impact of gender bias in listings is dwarfed by our respondents' inherent bias towards specific job types.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134734"}, {"primary_key": "3673966", "vector": [], "sparse_vector": [], "title": "To Share, or Not to Share?: Community-Level Collaboration in Open Innovation Contests.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Open innovation contests have been incredibly successful at producing creative designs and solutions. While participants compete for prizes in these contests, over half of contests conducted on online platforms allow participants to share ideas during contests, for benefits such as individual learning, community building, and cumulative innovation. Such sharing, however, is at tension with the competitive nature of crowd contests. To understand this tension, this study investigates community-level sharing of code on Kaggle, a contest platform for predictive modeling. Analyzing data on 25 contests in 2015 and 2016, we find that 10% of users shared code during contests, that participants doing medium well in the contest were the most likely to share code, and that sharing code improved individual, but not collective performance. These findings allow us to contribute insights about the participants, conditions, processes, and outcomes of community-level collaboration to both research on and design of open innovation contests.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134735"}, {"primary_key": "3673967", "vector": [], "sparse_vector": [], "title": "CCBL: A Language for Better Supporting Context Centered Programming in the Smart Home.", "authors": ["Lénaïc Terrier", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents CCBL (Cascading Contexts Based Language), an end-user programming language dedicated to Smart Home. We design CCBL to avoid the problems encountered by end-users programming with ECA (Event Conditions Actions), which is the dominant approach in the domain. We present the results of an experiment where we asked 21 adults (11 experimented programmers and 10 non-programmers) to express four increasingly complex behaviors using both CCBL and ECA. We show that significantly less errors were made using CCBL than using ECA. From this experiment, we also propose some categorization and explanation of the errors made when using ECA and explain why users avoid these errors when programming with CCBL.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3099584"}, {"primary_key": "3673968", "vector": [], "sparse_vector": [], "title": "Simulation Experiments on (the Absence of) Ratings Bias in Reputation Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As the gig economy continues to grow and freelance work moves online, five-star reputation systems are becoming more and more common. At the same time, there are increasing accounts of race and gender bias in evaluations of gig workers, with negative impacts for those workers. We report on a series of four Mechanical Turk-based studies in which participants who rated simulated gig work did not show race- or gender bias, while manipulation checks showed they reliably distinguished between low- and high-quality work. Given prior research, this was a striking result. To explore further, we used a Bayesian approach to verify absence of ratings bias (as opposed to merely not detecting bias). This Bayesian test let us identify an upper- bound: if any bias did exist in our studies, it was below an average of 0.2 stars on a five-star scale. We discuss possible interpretations of our results and outline future work to better understand the results.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134736"}, {"primary_key": "3673969", "vector": [], "sparse_vector": [], "title": "The Context Modelling Toolkit: A Unified Multi-layered Context Modelling Approach.", "authors": ["<PERSON>", "<PERSON>", "Beat Signer"], "summary": "Context awareness plays an important role in recent smart environments and embedded interactions. In order to increase user satisfaction and acceptance, these context-aware solutions should be controllable by end users. Over the last few years we have therefore seen an emerging trend towards visual programming tools for context-aware applications based on simple \"IF this THEN that\" rules. However, existing solutions often do not support the simple reuse of the \"this\" part in order to define more sophisticated rules. Given that the desired level of control varies among individuals, we propose a unified multi-layered context modelling approach distinguishing between end users, expert users and programmers. Our Context Modelling Toolkit (CMT) consists of the necessary context modelling concepts and offers a rule-based context processing engine. We further illustrate how end users and expert users might interact with the CMT framework. Finally, we highlight some advantages of our Context Modelling Toolkit by discussing a number of use cases.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3095810"}, {"primary_key": "3673971", "vector": [], "sparse_vector": [], "title": "Dyslexia in SNS: An Exploratory Study to Investigate Expressions of Identity and Multimodal Literacies.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Kaska Porayska-Pomsta"], "summary": "The paradigm of neurodiversity provides a theoretical scaffold to challenge the idea of dyslexia as a deficit, by considering how difficulties related to literacy may reflect possible cognitive strengths and opportunities for learning. In this paper we adopt this perspective which associates dyslexia with strengths in visual, oral and three-dimensional thinking. Our goal is to understand if and how the multimodal affordances of SNS mediate participation and new literacies for dyslexic youth, and how these affordances interact with identity work. Seven young people struggling with literacy were interviewed about their use of SNS. Our results show that the visual affordances of SNS enable new forms of participation and expression, furthering our understanding of visual literacies. Nonetheless, despite the pervasive use of visual affordances to perform identity work, we also find that young people's learning differences are not always obviated but re-constructed, or even confronted in SNS.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134738"}, {"primary_key": "3673972", "vector": [], "sparse_vector": [], "title": "Everyday Resilience: Supporting Resilient Strategies among Low Socioeconomic Status Communities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "People experiencing financial hardship often possess resilient and resourceful behaviors when handling their day-to-day activities. Understanding how these individuals manifest resilience during adversity could provide insights into how technologies can support their existing efforts. In a partnership with an Australian community care center, we identified resilient practices of people with low socioeconomic status. Following a strength-based approach, we collected data via home visits and semi-structured interviews involving 14 participants, and observed activities at the care center over 8 months. Our participants exhibited three key facets of resilience: (1) resilience as an integral part of their everyday lives, (2) a spirited phenomenon, and (3) a social and care-focused process. We contribute empirical insights into our participants' situated resilience and articulate ways through which design can support their existing social and collaborative practices. We compare and contrast our findings within and outside CSCW literature and advocate for a strength-based approach.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134740"}, {"primary_key": "3673973", "vector": [], "sparse_vector": [], "title": "Technologies, Methods, and Values: Changes in Empirical Research at CSCW 1990 - 2015.", "authors": ["<PERSON>", "Saba <PERSON>", "<PERSON>"], "summary": "The field of Computer Supported Cooperative Work has constantly evolved to meet the changing needs of individuals at home, at work, and online. To understand how these changes impacted CSCW research, we systematically reviewed 1209 papers and notes published at the ACM Conference on Computer Supported Cooperative Work between 1990 and 2015. When considered with results from two previous literature reviews, covering 1990 - 1998 and 1998 - 2004 respectively, our analysis provides perspective on 25 years of groupware research. We show that the field has responded to, not anticipated, changes in the computing landscape, long-term trends away from 'systems' and explanatory research, and a lack of bibliographic research that synthesizes findings. Finally, we discuss implications of these trends for CSCW research: how results are synthesized across the field, what kinds of research we value, and how multi-device ecologies are studied.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134741"}, {"primary_key": "3673974", "vector": [], "sparse_vector": [], "title": "Quantified Baby: Parenting and the Use of a Baby Wearable in the Wild.", "authors": ["<PERSON><PERSON> Wang", "Aisling Ann O&apos;Kane", "<PERSON>", "Gera<PERSON>", "<PERSON><PERSON>"], "summary": "Baby wearable technologies are becoming increasingly popular, particularly in early infancy. However, little research has been conducted to investigate how baby wearable technologies are adopted and used in parenting. This paper presents a two-week in-depth situated study with six mothers in their homes consisting of contextual entry and exit interviews, video recordings of \"out-of-box\" experiences, and a diary study. Using interpretative phenomenological analysis, participants' use and expectations of the baby wearable technology were examined. Use of the device directly impacted upon parents' knowledge production and anxiety, and influenced the perceived physicality and social factors of parenting. We frame these findings around sociological norms of the vulnerable child and responsible mother, as well as the notion of \"lived informatics\", where individuals' interaction with the technology influenced the perception, use and impact of the baby wearable on everyday parenting practices and familial relationships.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134743"}, {"primary_key": "3673975", "vector": [], "sparse_vector": [], "title": "Why Users Do Not Want to Write Together When They Are Writing Together: Users&apos; Rationales for Today&apos;s Collaborative Writing Practices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This study builds upon the 30-years HCI research of collaborative writing and focuses on users' experience of writing together in today's context. By interviewing 30 participants from both academia and industry, the paper examines how people write together using today's commercially available systems. The analysis focuses on the new co-editing capabilities (e.g., track changes) that are integrated into commercial tools and thus adopted by users widely in the last decade. These capabilities enable new ways of working together (e.g., directly edit the content at the character level at the same time), but users reported reluctance to fully commit to these new working styles. We thus systematically analyze users' rationales of why they do not want to write together while they are writing together with other. We argue that the development of collaborative writing tools is far from finished and these findings provide insights for the design of technology, and suggest future directions for research.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134742"}, {"primary_key": "3673976", "vector": [], "sparse_vector": [], "title": "Supporting Virtual Team Formation through Community-Wide Deliberation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Team-based learning is a structured, small-group learning method that has been associated with many positive outcomes in traditional classroom settings. However, relatively little research has focused on how to form and support teams within online learning platforms, such as Massive Open Online Courses (MOOCs). A number of challenges arise for team formation in voluntary online classes: students may drop out and leave their team, and even if they do persist with the course, the team may not work together effectively. In this paper, we introduce a team-formation strategy that incorporates a deliberation process, where participants hold discussions in preparation for the collaboration task. First, we present a crowdsourced experiment that compares teams that are formed before or after a community deliberation process. Results demonstrate that teams engaging in a larger community deliberative process prior to team formation exhibit better team performance--as measured by team collaboration product quality--than pre-discussion teams. In a second crowdsourced experiment, we further explore the benefits of community-wide processes by automatically assigning teams based on participants' transactive interaction during deliberation. The results demonstrate advantages in terms of team performance for teams formed based on observed interactions during the community-level deliberation, compared to randomly formed teams. Finally, in a case study, we demonstrate how we successfully adapted the team formation strategy for use in a small MOOC.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134744"}, {"primary_key": "3673978", "vector": [], "sparse_vector": [], "title": "Social Media for Earthquake Response: Unpacking its Limitations with Care.", "authors": ["<PERSON><PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "When a 7.8 intensity earthquake caused widespread disaster in Ecuador on April 16, 2016, citizens across the country self-organized to gather, mobilize, and distribute supplies to affected populations, assuming the role of ad hoc humanitarian logisticians. Drawing on ethnographic findings, we present a situated perspective of how these citizens straddled the boundaries of online and offline activity for earthquake relief. In doing so, we offer an enriched understanding of how various social media channels might support informal, on-the-ground, crisis response, but also where they fall short in the process. Studying the emergence of care where social media fell short allows us to make recommendations for technology design to improve the effectiveness of logisticians in crisis response. By examining the bodily engagement of our participants at crisis sites, their efforts to deal with material convergence, and how they interacted with social media for technology-mediated care, we also contribute an understanding of the sociomateriality of care that emerges amidst efforts towards crisis relief.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134747"}, {"primary_key": "3673979", "vector": [], "sparse_vector": [], "title": "Eliciting Values Reflections by Engaging Privacy Futures Using Design Workbooks.", "authors": ["Richmond Y<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although \"privacy by design\" (PBD)?embedding privacy protections into products during design, rather than retroactively?uses the term \"design\" to recognize how technical design choices implement and settle policy, design approaches and methodologies are largely absent from PBD conversations. Critical, speculative, and value-centered design approaches can be used to elicit reflections on relevant social values early in product development, and are a natural fit for PBD and necessary to achieve PBD's goal. Bringing these together, we present a case study using a design workbook of speculative design fictions as a values elicitation tool. Originally used as a reflective tool among a research group, we transformed the workbook into artifacts to share as values elicitation tools in interviews with graduate students training as future technology professionals. We discuss how these design artifacts surface contextual, socially-oriented understandings of privacy, and their potential utility in relationship to other values levers.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134746"}, {"primary_key": "3673980", "vector": [], "sparse_vector": [], "title": "&quot;Our Privacy Needs to be Protected at All Costs&quot;: Crowd Workers&apos; Privacy Experiences on Amazon Mechanical Turk.", "authors": ["Huichuan Xia", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Crowdsourcing platforms such as Amazon Mechanical Turk (MTurk) are widely used by organizations, researchers, and individuals to outsource a broad range of tasks to crowd workers. Prior research has shown that crowdsourcing can pose privacy risks (e.g., de-anonymization) to crowd workers. However, little is known about the specific privacy issues crowd workers have experienced and how they perceive the state of privacy in crowdsourcing. In this paper, we present results from an online survey of 435 MTurk crowd workers from the US, India, and other countries and areas. Our respondents reported different types of privacy concerns (e.g., data aggregation, profiling, scams), experiences of privacy losses (e.g., phishing, malware, stalking, targeted ads), and privacy expectations on MTurk (e.g., screening tasks). Respondents from multiple countries and areas reported experiences with the same privacy issues, suggesting that these problems may be endemic to the whole MTurk platform. We discuss challenges, high-level principles and concrete suggestions in protecting crowd workers'; privacy on MTurk and in crowdsourcing more broadly.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134748"}, {"primary_key": "3673981", "vector": [], "sparse_vector": [], "title": "Supporting Responsive Cohabitation Between Virtual Interfaces and Physical Objects on Everyday Surfaces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Systems for providing mixed physical-virtual interaction on desktop surfaces have been proposed for decades, though no such systems have achieved widespread use. One major factor contributing to this lack of acceptance may be that these systems are not designed for the variety and complexity of actual work surfaces, which are often in flux and cluttered with physical objects. In this paper, we use an elicitation study and interviews to synthesize a list of ten interactive behaviors that desk-bound, digital interfaces should implement to support responsive cohabitation with physical objects. As a proof of concept, we implemented these interactive behaviors in a working augmented desk system, demonstrating their imminent feasibility.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3095814"}, {"primary_key": "3673982", "vector": [], "sparse_vector": [], "title": "Cross-Strait Frenemies: Chinese Netizens VPN in to Facebook Taiwan.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Contributing to research on social activism as a form of collective action, we report on <PERSON><PERSON>, a sizable group of Chinese nationalists, who overcame the Great Firewall in order to troll Taiwan's political leadership. <PERSON><PERSON>'s political activism can be characterized as negotiating a tension between two seemingly opposed goals. On the one hand is their construction of a pro-PRC message using the tactics of Internet subcultures (memes, trolling, etc.), but toned down to meet standards of civility. On the other hand, by collectively breaching the Great Firewall and establishing Facebook accounts, the group transgressed PRC technical and legal norms, which were designed to prevent unsanctioned collective action. We argue that the Diba Expedition exemplifies the coordinated use of a complex, transnational social media ecology to support and produce a mass-scale event and newsworthy spectacle, loosened if not severed from state control, and a discursively innovative polysemous message targeted at diverse international audiences: civilized trolling.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134750"}, {"primary_key": "3673983", "vector": [], "sparse_vector": [], "title": "Persuading Teammates to Give: Systematic versus Heuristic Cues for Soliciting Loans.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Dual processing theories in psychology suggest that people process persuasive requests by assessing the quality of arguments (systematic processing) or by relying on heuristic rules (heuristic processing). However, the factors that act as systematic and heuristic processing cues and affect the success of persuasion have not been adequately described in social lending contexts. This research examines the effectiveness of systematic and heuristic cues in persuasive requests in Kiva lending teams intended to convince members to donate. An analysis of 88,596 requests exchanged in 1,610 teams shows that certain heuristic processing cues (e.g., liking between requesters and potential lenders, advocates low authority in their teams and the importance of the team to the lender) strongly predicted whether lenders would contribute to a loan request. In contrast, cues that required systematic processing are less influential. We also found that behavioral cues are more important than verbal ones. We discuss the theoretical and practical implications of our work.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134749"}, {"primary_key": "3673984", "vector": [], "sparse_vector": [], "title": "SqueezeBands: Mediated Social Touch Using Shape Memory Alloy Actuation.", "authors": ["<PERSON><PERSON><PERSON>", "Kenya Mejia", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Mediated social touch technologies aim to transmit the sense of touch between two or more physically distributed partners. Previous work in CSCW has focused mostly on vibrotactile actuation, though there is also significant recent interest in exploring a wider variety of haptic actuation modalities. In this paper, we explore Shape Memory Alloys as a novel means for constriction and heat activation. We demonstrate the feasibility of this approach by implementing the SqueezeBands system, which augments social gestures over videochat with haptic actuation. We describe an evaluation of the system with 57 pairs of participants, collaborating on tasks either high or low emotional salience. Our results demonstrate that SqueezeBands encourage greater and more diverse demonstrations of touch and that they may be particularly appropriate for easing mental and physical demand in high emotion tasks. We end with a discussion of the opportunities and challenges in leveraging Shape Memory Alloy actuation for mediated social touch.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134751"}, {"primary_key": "3673985", "vector": [], "sparse_vector": [], "title": "Out With The Old, In With The New?: Unpacking Member Turnover in Online Production Groups.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Yuqing Ren", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Nearly any group is subject to turnover : some people leave, while others join. Turnover can be especially high in online groups, since participation typically is strictly voluntary. We investigated the effects of member turnover in online groups, specifically in Wikipedia's WikiProjects. We based our studies on theories from organizational science, which suggest that it is not just the amount of turnover, but the characteristics of those leaving and those joining that matter. We characterized leavers and newcomers by their prior productivity, tenure (in the group or community), and participation in other groups within the larger community. Furthermore, we considered the moderating effect of group size on turnover. We analyzed data from 88,427 editors who participated in 1,054 WikiProjects, finding that (1) the positive effects of newcomers to a group were larger than the negative effects of leavers, (2) prior productivity, tenure, and participation in other groups all played significant roles, and (3) the effects of leavers and newcomers were amplified in larger groups.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134752"}, {"primary_key": "3673986", "vector": [], "sparse_vector": [], "title": "I Didn&apos;t Know that You Knew I Knew: Collaborative Shopping Practices between People with Visual Impairment and People with Vision.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is important to support independent living for people with visual impairments (PVI). Part of this can be accomplished with individual assistive technologies. However, in this paper we emphasize the social and collaborative needs for PVI to fully integrate into society as equals. The study assesses how PVI collaborate with different types of sighted partners when shopping together. We chose to study grocery shopping because it is a critical and challenging task for PVI. We conducted field observations and in-depth interviews with five PVI and their sighted shopping partners, including spouses, caseworkers, and store-provided courtesy shoppers. We found several factors that modulated these collaborations with varying forms of common ground: 1) knowledge about how to assist PVI; 2) interpersonal knowledge resulting from common experience and interpersonal relationship history; and 3) knowledge of shopping as a practice. We discuss our findings with respect to the implications for designing collaborative interactions.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134753"}, {"primary_key": "3673987", "vector": [], "sparse_vector": [], "title": "Crowd Development: The Interplay between Crowd Evaluation and Collaborative Dynamics in Wikipedia.", "authors": ["Ark Fangzhou Zhang", "<PERSON>", "<PERSON><PERSON>", "<PERSON>.", "<PERSON>"], "summary": "Collaborative crowdsourcing is an increasingly common way of accomplishing work in our economy. Yet, we know very little about how the behavior of these crowds changes over time and how these dynamics impact their performance. In this paper, we take a group development approach that considers how the behavior of crowds change over time in anticipation and as a result of their evaluation and recognition. Towards this goal, this paper studies the collaborative behavior of groups comprised of editors of articles that have been recognized for their outstanding quality and given the Good Articles (GA) status and those that eventually become Featured Articles (FA) on Wikipedia. The results show that the collaborative behavior of GA groups radically changes just prior to their nomination. In particular, the GA groups experience increases in the level of activity, centralization of workload, and level of GA experience and decreases in conflict (i.e., reverts) among editors. After being promoted to GA, they converge back to their typical behavior and composition. This indicates that crowd behavior prior to their evaluation period is dramatically different than behavior before or after. In addition, the collaborative behaviors of crowds during their promotion to GA are predictive of whether they are eventually promoted to FA. Our findings shed new light on the importance of time in understanding the relationship between crowd performance and collaborative measures such as centralization, conflict and experience.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134754"}, {"primary_key": "3673988", "vector": [], "sparse_vector": [], "title": "Understanding Relationship Overlapping on Social Network Sites: A Case Study of Weibo and Douban.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Nowadays people have many overlapping relationships across different social network sites. Will the communication frequency of a relationship on a focal SNS increase or decrease after the corresponding parties begin interacting with each other on a new SNS? What kinds of relationships and parties on the focal SNS are more robust to relationship overlapping across different sites? We conducted a case study on two Chinese popular social network sites (Weibo and Douban). Our results indicate that relationships' interactions on a new SNS have negative effect on the parties' communication frequency on the focal SNS. The communication frequency of older relationships on the focal SNS is more susceptible to the influence of the interacting on the new SNS, while relationships with more common groups and more extensive posts are less likely to be influenced. Our findings imply opportunities for SNS designers to strengthen their existing fragile ties and help users to build much more robust relationships.", "published": "2017-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3134755"}]