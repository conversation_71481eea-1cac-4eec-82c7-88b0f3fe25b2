// Define your models here.

export interface Model {
  id: string;
  label: string;
  apiIdentifier: string;
  description: string;
}

export const models: Array<Model> = [
  // =========================== deepseek 系列 ===========================
  {
    id: 'deepseek-V2.5',
    label: 'DeepSeek-V2.5',
    apiIdentifier: 'deepseek-ai/DeepSeek-V2.5',
    description: 'deepseek 最新模型，速度+++',
  },
  {
    id: 'deepseek-VL2',
    label: 'DeepSeek-VL2 🖼️',
    apiIdentifier: 'deepseek-ai/deepseek-vl2',
    description: 'deepseek 混合多模态模型，速度+++',
  },
  // =========================== glm 系列 ===========================
  // {
  //   id: 'glm-4-flash',
  //   label: 'GLM-4-Flash',
  //   apiIdentifier: 'GLM-4-Flash',
  //   description: '智谱最强小模型，速度++',
  // },  
  // {
  //   id: 'glm-4v-flash',
  //   label: 'GLM-4V-Flash 🖼️',
  //   apiIdentifier: 'GLM-4V-Flash',
  //   description: '智谱最强多模态小模型，速度++ （仅能单图）',
  // },
  // =========================== qwen 系列 ===========================
  // {
  //   id: 'qwen25-7B',
  //   label: 'Qwen2.5-7B',
  //   apiIdentifier: 'Qwen/Qwen2.5-7B-Instruct',
  //   description: '阿里最强小模型，速度+++',
  // },
  // {
  //   id: 'qwen25-coder-7B',
  //   label: 'Qwen2.5-Coder-7B',
  //   apiIdentifier: 'Qwen/Qwen2.5-Coder-7B-Instruct',
  //   description: '阿里代码小模型，速度+++',
  // },
  // {
  //   id: 'qwen25-coder-32B',
  //   label: 'Qwen2.5-Coder-32B',
  //   apiIdentifier: 'Qwen/Qwen2.5-Coder-32B-Instruct',
  //   description: '阿里代码模型，速度+++',
  // },
  // {
  //   id: 'qwen25-72B',
  //   label: 'Qwen2.5-72B',
  //   apiIdentifier: 'Qwen/Qwen2.5-72B-Instruct',
  //   description: '阿里大模型，速度++',
  // },
  {
    id: 'qwen25-72B',
    label: 'Qwen2.5-72B',
    apiIdentifier: 'Vendor-A/Qwen/Qwen2.5-72B-Instruct',
    description: '阿里大模型，速度++，使用国产芯片',
  },
  // {
  //   id: 'QwQ-32B-Preview',
  //   label: 'QwQ-32B-Preview',
  //   apiIdentifier: 'Qwen/QwQ-32B-Preview',
  //   description: '阿里推理模型，速度++',
  // },
  // =========================== gpt 系列 ===========================
  // {
  //   id: 'gpt-4o-mini',
  //   label: 'GPT-4o-mini',
  //   apiIdentifier: 'gpt-4o-mini',
  //   description: 'GPT-4o 的迷你版，速度++',
  // },
  {
    id: 'gpt-4o',
    label: 'GPT-4o 🖼️',
    apiIdentifier: 'gpt-4o',
    description: '通用任务，速度++',
  },
  {
    id: 'o1-mini',
    label: 'o1-mini',
    apiIdentifier: 'o1-mini',
    description: '推理任务，速度--',
  },
  // =========================== claude 系列 ===========================
  {
    id: 'claude-3-5-sonnet-20241022',
    label: 'claude-3-5-sonnet 🖼️',
    apiIdentifier: 'claude-3-5-sonnet-20241022',
    description: '代码任务，速度++',
  },
  // {
  //   id: 'claude-3-5-sonnet-20240620',
  //   label: 'claude-3-5-sonnet',
  //   apiIdentifier: 'claude-3-5-sonnet-20240620',
  //   description: '代码任务，速度+',
  // },
  // // =========================== gemini 系列 ===========================
  // {
  //   id: 'gemini-1.5-pro-latest',
  //   label: 'gemini 1.5 pro',
  //   apiIdentifier: 'gemini-1.5-pro-latest',
  //   description: '通用任务，速度+',
  // },
  // {
  //   id: 'gemini-1.5-flash-latest',
  //   label: 'gemini 1.5 flash',
  //   apiIdentifier: 'gemini-1.5-flash-latest',
  //   description: '性价比爆炸！！！',
  // },
] as const;

export const DEFAULT_MODEL_NAME: string = 'deepseek-V2.5';
