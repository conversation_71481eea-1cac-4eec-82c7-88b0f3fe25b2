import Image from "next/image";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { IoQrCode } from "react-icons/io5";
export default function WechatPopover() {
  return (
    <Popover>
      <PopoverTrigger>
        {/* <IoQrCode className="w-6 h-6 text-gray-700 hover:text-blue-400 transition-colors cursor-pointer" /> */}
        
        <p className="text-sm font-bold">交流群</p>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-4">
        <div className="flex justify-center items-center space-x-4">
          {/* 微信好友二维码 */}
          <div className="text-center">
            <Image 
                src="/images/QR.jpg"
                alt="交流群二维码"
                width={130}
                height={130}
                className="mx-auto"
            />
            <p className="text-sm text-gray-500 mt-2 font-bold">微信扫码</p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}