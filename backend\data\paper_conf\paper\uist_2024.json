[{"primary_key": "710936", "vector": [], "sparse_vector": [], "title": "Computational Trichromacy Reconstruction: Empowering the Color-Vision Deficient to Recognize Colors Using Augmented Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Yukang Yan", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose an assistive technology that helps individuals with Color Vision Deficiencies (CVD) to recognize/name colors. A dichromat's color perception is a reduced two-dimensional (2D) subset of a normal trichromat's three dimensional color (3D) perception, leading to confusion when visual stimuli that appear identical to the dichromat are referred to by different color names. Using our proposed system, CVD individuals can interactively induce distinct perceptual changes to originally confusing colors via a computational color space transformation. By combining their original 2D precepts for colors with the discriminative changes, a three dimensional color space is reconstructed, where the dichromat can learn to resolve color name confusions and accurately recognize colors. Our system is implemented as an Augmented Reality (AR) interface on smartphones, where users interactively control the rotation through swipe gestures and observe the induced color shifts in the camera view or in a displayed image. Through psychophysical experiments and a longitudinal user study, we demonstrate that such rotational color shifts have discriminative power (initially confusing colors become distinct under rotation) and exhibit structured perceptual shifts dichromats can learn with modest training. The AR App is also evaluated in two real-world scenarios (building with lego blocks and interpreting artistic works); users all report positive experience in using the App to recognize object colors that they otherwise could not.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676415"}, {"primary_key": "710937", "vector": [], "sparse_vector": [], "title": "SIM2VR: Towards Automated Biomechanical Testing in VR.", "authors": ["<PERSON><PERSON><PERSON>", "Aleksi <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automated biomechanical testing has great potential for the development of VR applications, as initial insights into user behaviour can be gained in silico early in the design process. In particular, it allows prediction of user movements and ergonomic variables, such as fatigue, prior to conducting user studies. However, there is a fundamental disconnect between simulators hosting state-of-the-art biomechanical user models and simulators used to develop and run VR applications. Existing user simulators often struggle to capture the intricacies of real-world VR applications, reducing ecological validity of user predictions. In this paper, we introduce sim2vr, a system that aligns user simulation with a given VR application by establishing a continuous closed loop between the two processes. This, for the first time, enables training simulated users directly in the same VR application that real users interact with. We demonstrate that sim2vr can predict differences in user performance, ergonomics and strategies in a fast-paced, dynamic arcade game. In order to expand the scope of automated biomechanical testing beyond simple visuomotor tasks, advances in cognitive models and reward function design will be needed.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676452"}, {"primary_key": "710938", "vector": [], "sparse_vector": [], "title": "Clarify: Improving Model Robustness With Natural Language Corrections.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Chelsea Finn"], "summary": "The standard way to teach models is by feeding them lots of data.However, this approach often teaches models incorrect ideas because they pick up on misleading signals in the data.To prevent such misconceptions, we must necessarily provide additional information beyond the training data.Prior methods incorporate additional instance-level supervision, such as labels for misleading features or additional labels for debiased data.However, such strategies require a large amount of labeler effort.We hypothesize that people are good at providing textual feedback at the concept level, a capability that existing teaching frameworks do not leverage.We propose Clarify, a novel interface and method for interactively correcting model misconceptions.Through Clarify, users need only provide a short text description of a model's consistent failure patterns.Then, in an entirely automated way, we use such descriptions to improve the training process.Clarify is the first end-to-end system for user model correction.Our user studies show that non-expert users can successfully describe model misconceptions via Clarify, leading to increased worst-case performance in two datasets.We additionally conduct a case study on a large-scale image dataset, ImageNet, using Clarify to find and rectify 31 novel hard subpopulations.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676362"}, {"primary_key": "710939", "vector": [], "sparse_vector": [], "title": "UIClip: A Data-driven Model for Assessing User Interface Design.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "User interface (UI) design is a difficult yet important task for ensuring the usability, accessibility, and aesthetic qualities of applications. In our paper, we develop a machine-learned model, UIClip, for assessing the design quality and visual relevance of a UI given its screenshot and natural language description. To train UIClip, we used a combination of automated crawling, synthetic augmentation, and human ratings to construct a large-scale dataset of UIs, collated by description and ranked by design quality. Through training on the dataset, UIClip implicitly learns properties of good and bad designs by i) assigning a numerical score that represents a UI design’s relevance and quality and ii) providing design suggestions. In an evaluation that compared the outputs of UIClip and other baselines to UIs rated by 12 human designers, we found that UIClip achieved the highest agreement with ground-truth rankings. Finally, we present three example applications that demonstrate how UIClip can facilitate downstream applications that rely on instantaneous assessment of UI design quality: i) UI code generation, ii) UI design tips generation, and iii) quality-aware UI example search.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676408"}, {"primary_key": "710940", "vector": [], "sparse_vector": [], "title": "Natural Expression of a Machine Learning Model&apos;s Uncertainty Through Verbal and Non-Verbal Behavior of Intelligent Virtual Agents.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Uncertainty cues are inherent in natural human interaction, as they signal to communication partners how much they can rely on conveyed information. Humans subconsciously provide such signals both verbally (e.g., through expressions such as “maybe’’ or “I think’’) and non-verbally (e.g., by diverting their gaze). In contrast, artificial intelligence (AI)-based services and machine learning (ML) models such as ChatGPT usually do not disclose the reliability of answers to their users. In this paper, we explore the potential of combining ML models as powerful information sources with human means of expressing uncertainty to contextualize the information. We present a comprehensive pipeline that comprises (1) the human-centered collection of (non-)verbal uncertainty cues, (2) the transfer of cues to virtual agent videos, (3) the annotation of videos for perceived uncertainty, and (4) the subsequent training of a custom ML model that can generate uncertainty cues in virtual agent behavior. In a final step (5), the trained ML model is evaluated in terms of both fidelity and generalizability of the generated (non-)verbal uncertainty behavior.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676454"}, {"primary_key": "710941", "vector": [], "sparse_vector": [], "title": "Fiery Hands: Designing Thermal Glove through Thermal and Tactile Integration for Virtual Object Manipulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a novel approach to render thermal and tactile feedback to the palm and fingertips through thermal and tactile integration. Our approach minimizes the obstruction of the palm and inner side of the fingers and enables virtual object manipulation while providing localized and global thermal feedback. By leveraging thermal actuators positioned strategically on the outer palm and back of the fingers in interplay with tactile actuators, our approach exploits thermal referral and tactile masking phenomena. Through a series of user studies, we validate the perception of localized thermal sensations across the palm and fingers, showcasing the ability to generate diverse thermal patterns. Furthermore, we demonstrate the efficacy of our approach in VR applications, replicating diverse thermal interactions with virtual objects. This work represents significant progress in thermal interactions within VR, offering enhanced sensory immersion at an optimal energy cost.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676457"}, {"primary_key": "710942", "vector": [], "sparse_vector": [], "title": "EyeFormer: Predicting Personalized Scanpaths with Transformer-Guided Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "From a visual-perception perspective, modern graphical user interfaces (GUIs) comprise a complex graphics-rich two-dimensional visuospatial arrangement of text, images, and interactive objects such as buttons and menus. While existing models can accurately predict regions and objects that are likely to attract attention “on average”, no scanpath model has been capable of predicting scanpaths for an individual. To close this gap, we introduce EyeFormer, which utilizes a Transformer architecture as a policy network to guide a deep reinforcement learning algorithm that predicts gaze locations. Our model offers the unique capability of producing personalized predictions when given a few user scanpath samples. It can predict full scanpath information, including fixation positions and durations, across individuals and various stimulus types. Additionally, we demonstrate applications in GUI layout optimization driven by our model.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676436"}, {"primary_key": "710943", "vector": [], "sparse_vector": [], "title": "Demonstrating PopCore: Personal Fabrication of 3D Foamcore Models for Professional High-Quality Applications in Design and Architecture.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>ieland Storch", "<PERSON><PERSON>", "<PERSON>"], "summary": "PopCore is a fabrication technique that laser-cuts 3D models from paper-foam-paper sandwich materials. Its key elements are two laser-cut lever mechanisms that allow users to break off surrounding residue material, thereby “excavating” joints efficiently and with very high precision, which PopCore produces by laser cutting from the top and bottom. This produces flush joints, folded edges that are perfectly straight, and no burn marks—giving models a homogeneous, clean look. This allows applying personal fabrication to new fields, including industrial design, architecture, and packaging design, that require a visual finish beyond what traditional personal fabrication delivers. We demonstrate the algorithms and a software tool that generates PopCore automatically. Our user study participants rated PopCore models significantly more visually appealing (7.9/9) than models created using techniques from the related work (4.7/9 and 2.3/9) and suitable for presentation models (11/12 participants), products (10/12 participants) and high-end packaging (10/12 participants).", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686761"}, {"primary_key": "710944", "vector": [], "sparse_vector": [], "title": "Silent Impact: Tracking Tennis Shots from the Passive Arm.", "authors": ["Junyong Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wearable technology has transformed sports analytics, offering new dimensions in enhancing player experience. Yet, many solutions involve cumbersome setups that inhibit natural motion. In tennis, existing products require sensors on the racket or dominant arm, causing distractions and discomfort. We propose Silent Impact, a novel and user-friendly system that analyzes tennis shots using a sensor placed on the passive arm. Collecting Inertial Measurement Unit sensor data from 20 recreational tennis players, we developed neural networks that exclusively utilize passive arm data to detect and classify six shots, achieving a classification accuracy of 88.2% and a detection F1 score of 86.0%, comparable to the dominant arm. These models were then incorporated into an end-to-end prototype, which records passive arm motion through a smartwatch and displays a summary of shots on a mobile app. User study (N=10) showed that participants felt less burdened physically and mentally using Silent Impact on the passive arm. Overall, our research establishes the passive arm as an effective, comfortable alternative for tennis shot analysis, advancing user-friendly sports analytics.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676403"}, {"primary_key": "710945", "vector": [], "sparse_vector": [], "title": "JetUnit: Rendering Diverse Force Feedback in Virtual Reality Using Water Jets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose JetUnit, a water-based VR haptic system designed to produce force feedback with a wide spectrum of intensities and frequencies through water jets. The key challenge in designing this system lies in optimizing parameters to enable the haptic device to generate force feedback that closely replicates the most intense force produced by direct water jets while ensuring the user remains dry. In this paper, we present the key design parameters of the JetUnit wearable device determined through a set of quantitative experiments and a perception study. We further conducted a user study to assess the impact of integrating our haptic solutions into virtual reality experiences. The results revealed that, by adhering to the design principles of JetUnit, the water-based haptic system is capable of delivering diverse force feedback sensations, significantly enhancing the immersive experience in virtual reality.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676440"}, {"primary_key": "710946", "vector": [], "sparse_vector": [], "title": "Demonstration of JetUnit: Rendering Diverse Force Feedback in Virtual Reality Using Water Jets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "JetUnit [21] is a water-based VR haptic system capable of generating a diverse range of perceived force intensities and frequencies through water jets. A key challenge in designing this system was optimizing parameters to enable the haptic device to produce force feedback that closely mimics the most intense force achievable with direct water jets, while ensuring the user remains dry. In this demonstration, we showcase JetUnit by integrating our haptic solutions into various virtual reality interactions, including touch, poking, injection, and recurring pulsing and wave signals.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686742"}, {"primary_key": "710947", "vector": [], "sparse_vector": [], "title": "Lumina: A Software Tool for Fostering Creativity in Designing Chinese Shadow Puppets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Shiqing Lyu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Shadow puppetry, a culturally rich storytelling art, faces challenges transitioning to the digital realm. Creators in the early design phase struggle with crafting intricate patterns, textures, and basic animations while adhering to stylistic conventions - hindering creativity, especially for novices. This paper presents Lumina, a tool to facilitate the early Chinese shadow puppet design stage. Lumina provides contour templates, animations, scene editing tools, and machine-generated traditional puppet patterns. These features liberate creators from tedious tasks, allowing focus on the creative process. Developed based on a formative study with puppet creators, the web-based Lumina enables wide dissemination. An evaluation with 18 participants demonstrated <PERSON><PERSON>’s effectiveness and ease of use, with participants successfully creating designs spanning traditional themes to contemporary and science-fiction concepts.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676426"}, {"primary_key": "710948", "vector": [], "sparse_vector": [], "title": "Embodied AR Language Learning Through Everyday Object Interactions: A Demonstration of EARLL.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON><PERSON>", "Minji Park", "<PERSON>", "<PERSON>"], "summary": "Learning a new language is an exciting and important yet often challenging goal. To support foreign language acquisition, we introduce EARLL, an embodied and context-aware language learning application for AR glasses. EARLL leverages real-time computer vision and depth sensing to continuously segment and localize objects in users’ surroundings, check for hand-object manipulations, and then subtly trigger foreign vocabulary prompts relevant to that object. In this demo paper, we present our initial EARLL prototype and highlight current challenges and future opportunities with always-available, wearable, embodied AR language learning.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686746"}, {"primary_key": "710949", "vector": [], "sparse_vector": [], "title": "CookAR: Affordance Augmentations in Wearable AR to Support Kitchen Tool Interactions for People with Low Vision.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Minji Park", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cooking is a central activity of daily living, supporting independence as well as mental and physical health. However, prior work has highlighted key barriers for people with low vision (LV) to cook, particularly around safely interacting with tools, such as sharp knives or hot pans. Drawing on recent advancements in computer vision (CV), we present CookAR, a head-mounted AR system with real-time object affordance augmentations to support safe and efficient interactions with kitchen tools. To design and implement CookAR, we collected and annotated the first egocentric dataset of kitchen tool affordances, fine-tuned an affordance segmentation model, and developed an AR system with a stereo camera to generate visual augmentations. To validate CookAR, we conducted a technical evaluation of our fine-tuned model as well as a qualitative lab study with 10 LV participants for suitable augmentation design. Our technical evaluation demonstrates that our model outperforms the baseline on our tool affordance dataset, while our user study indicates a preference for affordance augmentations over the traditional whole object augmentations.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676449"}, {"primary_key": "710950", "vector": [], "sparse_vector": [], "title": "Digital Phenotyping based on a Mobile App Identifies Distinct and Overlapping Features in Children Diagnosed with Autism versus ADHD.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> L<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Marina Spanos", "<PERSON><PERSON>", "<PERSON>"], "summary": "The high prevalence of autism calls for accessible and scalable technology-assisted screening tools. This will aid in early detection allowing timely access to services and supports. SenseToKnow, a mobile digital phenotyping app, showed potential in eliciting autism-related behaviors that can be automatically captured via computer vision analysis (CVA) in toddlers. Here, we present the capability of SenseToKnow in characterizing autism in school age children and showcase the robustness of the CVA features in interpreting distinct and overlapping behaviors with attention-deficit/hyperactive disorder (ADHD).", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686323"}, {"primary_key": "710951", "vector": [], "sparse_vector": [], "title": "VIME: Visual Interactive Model Explorer for Identifying Capabilities and Limitations of Machine Learning Models for Sequential Decision-Making.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ensuring that Machine Learning (ML) models make correct and meaningful inferences is necessary for the broader adoption of such models into high-stakes decision-making scenarios. Thus, ML model engineers increasingly use eXplainable AI (XAI) tools to investigate the capabilities and limitations of their ML models before deployment. However, explaining sequential ML models, which make a series of decisions at each timestep, remains challenging. We present Visual Interactive Model Explorer (VIME), an XAI toolbox that enables ML model engineers to explain decisions of sequential models in different “what-if” scenarios. Our evaluation with 14 ML experts, who investigated two existing sequential ML models using VIME and a baseline XAI toolbox to explore “what-if” scenarios, showed that VIME made it easier to identify and explain instances when the models made wrong decisions compared to the baseline. Our work informs the design of future interactive XAI mechanisms for evaluating sequential ML-based decision support systems.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676323"}, {"primary_key": "710952", "vector": [], "sparse_vector": [], "title": "PrISM-Observer: Intervention Agent to Help Users Perform Everyday Procedures Sensed using a Smartwatch.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We routinely perform procedures (such as cooking) that include a set of atomic steps. Often, inadvertent omission or misordering of a single step can lead to serious consequences, especially for those experiencing cognitive challenges such as dementia. This paper introduces PrISM-Observer, a smartwatch-based, context-aware, real-time intervention system designed to support daily tasks by preventing errors. Unlike traditional systems that require users to seek out information, the agent observes user actions and intervenes proactively. This capability is enabled by the agent’s ability to continuously update its belief in the user’s behavior in real-time through multimodal sensing and forecast optimal intervention moments and methods. We first validated the steps-tracking performance of our framework through evaluations across three datasets with different complexities. Then, we implemented a real-time agent system using a smartwatch and conducted a user study in a cooking task scenario. The system generated helpful interventions, and we gained positive feedback from the participants. The general applicability of PrISM-Observer to daily tasks promises broad applications, for instance, including support for users requiring more involved interventions, such as people with dementia or post-surgical patients.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676350"}, {"primary_key": "710953", "vector": [], "sparse_vector": [], "title": "Rhapso: Automatically Embedding Fiber Materials into 3D Prints for Enhanced Interactivity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Kim"], "summary": "We introduce Rhapso, a 3D printing system designed to embed a diverse range of continuous fiber materials within 3D objects during the printing process. This approach enables integrating properties like tensile strength, force storage and transmission, or aesthetic and tactile characteristics, directly into low-cost thermoplastic 3D prints. These functional objects can have intricate actuation, self-assembly, and sensing capabilities with little to no manual intervention. To achieve this, we modify a low-cost Fused Filament Fabrication (FFF) 3D printer, adding a stepper motor-controlled fiber spool mechanism on a gear ring above the print bed. In addition to hardware, we provide parsing software for precise fiber placement, which generates G-code for printer operation. To illustrate the versatility of our system, we present applications that showcase its extensive design potential. Additionally, we offer comprehensive documentation and open designs, empowering others to replicate our system and explore its possibilities.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676468"}, {"primary_key": "710954", "vector": [], "sparse_vector": [], "title": "Demonstration of Haptic Devices with Variable Volume Using Spiral Spring Structures.", "authors": ["Minhyeok Baek", "<PERSON><PERSON>"], "summary": "Controllers that convey tactile sensations of object size in virtual environments are crucial for user interaction. However, existing research often faces commercialization constraints due to the complexity and high number of actuators required. This study proposes a haptic device that utilizes a spiral spring structure that offers approximately three times the variable range with a single actuator. This system can quickly render the diameters of objects grasped by users and simulate continuous volume changes, such as inflating a balloon. Due to its simple structure and wide variable range, this system is expected to be suitable for various scenarios, providing users with a more immersive virtual reality experience.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686752"}, {"primary_key": "710955", "vector": [], "sparse_vector": [], "title": "Efficient Optimal Mouse Sensor Position Estimation using Simulated Cursor Trajectories.", "authors": ["Minhyeok Baek", "<PERSON><PERSON>"], "summary": "The optimal sensor position on a computer mouse can improve pointing performance, but existing calibration methods require time-consuming repetitions of pointing tasks. In this paper, we propose a novel calibration approach that dramatically reduces the time and effort required to determine a user’s optimal mouse sensor position. Our method simulates cursor trajectories for different sensor positions using a dual-sensor mouse, eliminating the need for repetitive measurements with multiple sensor placements. By analyzing the straightness of the simulated paths, quantified by the mean absolute error (MAE) relative to an ideal straight-line path, we estimate the sensor position that would yield the most efficient pointing motion for the user. Our preliminary results indicate that the proposed simulation-based calibration method could reduce the calibration time from an hour to just five minutes, while providing a better identification of the optimal mouse sensor positions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686345"}, {"primary_key": "710956", "vector": [], "sparse_vector": [], "title": "Active Haptic Feedback for a Virtual Wrist-Anchored User Interface.", "authors": ["<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The presented system combines a virtual wrist-anchored user interface (UI) with a new low-profile, wrist-worn device that provides salient and expressive haptic feedback such as contact, pressure and broad-bandwidth vibration. This active feedback is used to add tactile cues to interactions with virtual mid-air UI elements that track the user’s wrist; we demonstrate a simple menu-interaction task to showcase the utility of haptics for interactions with virtual buttons and sliders. Moving forward, we intend to use this platform to develop haptic guidelines for body-anchored interfaces and test multiple haptic devices across the body to create engaging interactions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686765"}, {"primary_key": "710957", "vector": [], "sparse_vector": [], "title": "Story-Driven: Exploring the Impact of Providing Real-time Context Information on Automated Storytelling.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Stories have long captivated the human imagination with narratives that enrich our lives. Traditional storytelling methods are often static and not designed to adapt to the listener’s environment, which is full of dynamic changes. For instance, people often listen to stories in the form of podcasts or audiobooks while traveling in a car. Yet, conventional in-car storytelling systems do not embrace the adaptive potential of this space. The advent of generative AI is the key to creating content that is not just personalized but also responsive to the changing parameters of the environment. We introduce a novel system for interactive, real-time story narration that leverages environment and user context in correspondence with estimated arrival times to adjust the generated story continuously. Through two comprehensive real-world studies with a total of 30 participants in a vehicle, we assess the user experience, level of immersion, and perception of the environment provided by the prototype. Participants’ feedback shows a significant improvement over traditional storytelling and highlights the importance of context information for generative storytelling systems.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676372"}, {"primary_key": "710958", "vector": [], "sparse_vector": [], "title": "Augmented Breathing via Thermal Feedback in the Nose.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose, engineer, and study a novel method to augment the feeling of breathing—enabling interactive applications to let users feel like they are inhaling more/less air (perceived nasal airflow). We achieve this effect by cooling or heating the nose in sync with the user’s inhalation. Our illusion builds on the physiology of breathing: we perceive our breath predominantly through the cooling of our nasal cavities during inhalation. This is why breathing in a “fresh” cold environment feels easier than in a “stuffy” hot environment, even when the inhaled volume is the same. Our psychophysical study confirmed that our in-nose temperature stimulation significantly influenced breathing perception in both directions: making it feel harder & easier to breathe. Further, we found that Math 1 of the trials were described as a change in perceived airflow/breathing, while only Math 2 as temperature. Following, we engineered a compact device worn across the septum that uses Peltier elements. We illustrate the potential of this augmented breathing in interactive contexts, such as for virtual reality (e.g., rendering ease of breathing crisp air or difficulty breathing with a deteriorated gas mask) and everyday interactions (e.g., in combination with a relaxation application or to alleviate the perceived breathing resistance when wearing a mask).", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676438"}, {"primary_key": "710959", "vector": [], "sparse_vector": [], "title": "Demonstrating Augmented Breathing via Thermal Feedback in the Nose.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We demonstrate a novel method to augment the feeling of breathing—enabling interactive applications to let users feel like they are inhaling more/less air (perceived nasal airflow). We achieve this effect by cooling or heating the nose in sync with the user’s inhalation. Our illusion builds on the physiology of breathing: we perceive our breath predominantly through the cooling of our nasal cavities during inhalation. This is why breathing in a “fresh” cold environment feels easier than in a “stuffy” hot environment, even when the inhaled volume is the same. Following, we engineered a compact device worn across the septum that uses Peltier elements. We illustrate the potential of this augmented breathing in a demonstration with two tracks: a fast track for a quick experience and a slow track for an immersive virtual reality (VR) walkthrough. In the fast track, visitors can quickly experience altered breathing perception using a simple application and our devices. In the slow track, visitors can put on a VR headset and experience an interactive scene where their sense of breathing is altered (e.g., rendering ease of breathing crisp air or difficulty breathing with a deteriorated gas mask).", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686773"}, {"primary_key": "710960", "vector": [], "sparse_vector": [], "title": "SoundModVR: Sound Modifications in Virtual Reality for Sound Accessibility.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous VR sound accessibility work have substituted sounds with visual or haptic output to increase VR accessibility for deaf and hard of hearing (DHH) people. However, deafness occurs on a spectrum, and many DHH people (e.g., those with partial hearing) can also benefit from manipulating audio (e.g., increasing volume at specific frequencies) instead of substituting it with another modality. In this demo paper, we present a toolkit that allows modifying sounds in VR to support DHH people. We designed and implemented 18 VR sound modification tools spanning four categories, including prioritizing sounds, modifying sound parameters, providing spatial assistance, and adding additional sounds. Evaluation of our tools with 10 DHH users across five diverse VR scenarios reveal that our toolkit can improve DHH users’ VR experience but could be further improved by providing more customization options and decreasing cognitive load. We then compiled a Unity toolkit and conducted a preliminary evaluation with six Unity VR developers. Preliminary insights show that our toolkit is easy to use but could be enhanced through modularization.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686754"}, {"primary_key": "710961", "vector": [], "sparse_vector": [], "title": "WorldScribe: Towards Context-Aware Live Visual Descriptions.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Automated live visual descriptions can aid blind people in understanding their surroundings with autonomy and independence. However, providing descriptions that are rich, contextual, and just-in-time has been a long-standing challenge in accessibility. In this work, we develop WorldScribe, a system that generates automated live real-world visual descriptions that are customizable and adaptive to users' contexts: (i) WorldScribe's descriptions are tailored to users' intents and prioritized based on semantic relevance. (ii) WorldScribe is adaptive to visual contexts, e.g., providing consecutively succinct descriptions for dynamic scenes, while presenting longer and detailed ones for stable settings. (iii) WorldScribe is adaptive to sound contexts, e.g., increasing volume in noisy environments, or pausing when conversations start. Powered by a suite of vision, language, and sound recognition models, WorldScribe introduces a description generation pipeline that balances the tradeoffs between their richness and latency to support real-time use. The design of WorldScribe is informed by prior work on providing visual descriptions and a formative study with blind participants. Our user study and subsequent pipeline evaluation show that WorldScribe can provide real-time and fairly accurate visual descriptions to facilitate environment understanding that is adaptive and customized to users' contexts. Finally, we discuss the implications and further steps toward making live visual descriptions more context-aware and humanized.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676375"}, {"primary_key": "710962", "vector": [], "sparse_vector": [], "title": "AutoSpark: Supporting Automobile Appearance Design Ideation with Kansei Engineering and Generative AI.", "authors": ["Liu<PERSON> Chen", "Qi<PERSON><PERSON> Jing", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Sun"], "summary": "Rapid creation of novel product appearance designs that align with consumer emotional requirements poses a significant challenge. Text-to-image models, with their excellent image generation capabilities, have demonstrated potential in providing inspiration to designers. However, designers still encounter issues including aligning emotional needs, expressing design intentions, and comprehending generated outcomes in practical applications. To address these challenges, we introduce AutoSpark, an interactive system that integrates Kansei Engineering and generative AI to provide creativity support for designers in creating automobile appearance designs that meet emotional needs. AutoSpark employs a Kansei Engineering engine powered by generative AI and a semantic network to assist designers in emotional need alignment, design intention expression, and prompt crafting. It also facilitates designers’ understanding and iteration of generated results through fine-grained image-image similarity comparisons and text-image relevance assessments. The design-thinking map within its interface aids in managing the design process. Our user study indicates that AutoSpark effectively aids designers in producing designs that are more aligned with emotional needs and of higher quality compared to a baseline system, while also enhancing the designers’ experience in the human-AI co-creation process.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676337"}, {"primary_key": "710963", "vector": [], "sparse_vector": [], "title": "VisCourt: In-Situ Guidance for Interactive Tactic Training in Mixed Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In team sports like basketball, understanding and executing tactics—coordinated plans of movements among players—are crucial yet complex, requiring extensive practice. These tactics require players to develop a keen sense of spatial and situational awareness. Traditional coaching methods, which mainly rely on basketball tactic boards and video instruction, often fail to bridge the gap between theoretical learning and the real-world application of tactics, due to shifts in view perspectives and a lack of direct experience with tactical scenarios. To address this challenge, we introduce VisCourt, a Mixed Reality (MR) tactic training system, in collaboration with a professional basketball team. To set up the MR training environment, we employed semi-automatic methods to simulate realistic 3D tactical scenarios and iteratively designed visual in-situ guidance. This approach enables full-body engagement in interactive training sessions on an actual basketball court and provides immediate feedback, significantly enhancing the learning experience. A user study with athletes and enthusiasts shows the effectiveness and satisfaction with VisCourt in basketball training and offers insights for the design of future SportsXR training systems.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676466"}, {"primary_key": "710964", "vector": [], "sparse_vector": [], "title": "ChipQuest: Gamifying the Semiconductor Manufacturing Process to Inspire Future Workforce.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Semiconductor manufacturing is crucial for national economies; however, the industry faces significant talent shortages. While extensive research exists on motivating students in STEM learning, there is little work specifically addressing semiconductor education. To fill this gap, we first examined current barriers and motivational factors influencing students’ pursuit of careers in semiconductor fields through interviews with 13 participants. Findings reveal that limited recognition of semiconductor companies relative to software engineering poses a barrier, while early exposure to the field and hands-on experience emerge as pivotal factors motivating prospective students. Drawing upon these insights, we introduce ChipQuest, an educational game designed to enhance K-12 students’ engagement and interest in semiconductors. ChipQuest integrates gamification elements to simulate the complexities of semiconductor chip manufacturing, featuring a pedagogical agent, interactive tasks, a reward system, and competitive components. By incorporating gaming principles into semiconductor education, ChipQuest aims to offer a promising approach to inspire young students as the future workforce in the semiconductor industry.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686318"}, {"primary_key": "710965", "vector": [], "sparse_vector": [], "title": "SonoHaptics: An Audio-Haptic Cursor for Gaze-Based Object Selection in XR.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce SonoHaptics, an audio-haptic cursor for gaze-based 3D object selection. SonoHaptics addresses challenges around providing accurate visual feedback during gaze-based selection in Extended Reality (XR), e.g., lack of world-locked displays in no- or limited-display smart glasses and visual inconsistencies. To enable users to distinguish objects without visual feedback, SonoHaptics employs the concept of cross-modal correspondence in human perception to map visual features of objects (color, size, position, material) to audio-haptic properties (pitch, amplitude, direction, timbre). We contribute data-driven models for determining cross-modal mappings of visual features to audio and haptic features, and a computational approach to automatically generate audio-haptic feedback for objects in the user's environment. SonoHaptics provides global feedback that is unique to each object in the scene, and local feedback to amplify differences between nearby objects. Our comparative evaluation shows that SonoHaptics enables accurate object identification and selection in a cluttered scene without visual feedback.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676384"}, {"primary_key": "710966", "vector": [], "sparse_vector": [], "title": "Auptimize: Optimal Placement of Spatial Audio Cues for Extended Reality.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Yukang Yan", "<PERSON>"], "summary": "Figure 1: Auptimize takes the layout of Extended Reality (XR) elements as input (left), analyzes the probability that the user misidentifies from which element a spatial sound is coming from (middle), then relocates the sound sources to the optimized locations which are disentangled from their visual counterparts to minimize the confusion probability (right).", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676424"}, {"primary_key": "710967", "vector": [], "sparse_vector": [], "title": "Patchview: LLM-powered Worldbuilding with Generative Dust and Magnet Visualization.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Large language models (LLMs) can help writers build story worlds by generating world elements, such as factions, characters, and locations. However, making sense of many generated elements can be overwhelming. Moreover, if the user wants to precisely control aspects of generated elements that are difficult to specify verbally, prompting alone may be insufficient. We introduce Patchview, a customizable LLM-powered system that visually aids worldbuilding by allowing users to interact with story concepts and elements through the physical metaphor of magnets and dust. Elements in Patchview are visually dragged closer to concepts with high relevance, facilitating sensemaking. The user can also steer the generation with verbally elusive concepts by indicating the desired position of the element between concepts. When the user disagrees with the LLM's visualization and generation, they can correct those by repositioning the element. These corrections can be used to align the LLM's future behaviors to the user's perception. With a user study, we show that Patchview supports the sensemaking of world elements and steering of element generation, facilitating exploration during the worldbuilding process. Patchview provides insights on how customizable visual representation can help sensemake, steer, and align generative AI model behaviors with the user's intentions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676352"}, {"primary_key": "710968", "vector": [], "sparse_vector": [], "title": "Toyteller: Toy-Playing with Character Symbols for AI-Powered Visual Storytelling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce Toyteller, an AI-powered storytelling system that allows users to generate a mix of story texts and visuals by directly manipulating character symbols like they are playing with toys. Anthropomorphized motions of character symbols can convey rich and nuanced social interactions between characters; Toyteller leverages these motions as (1) a means for users to steer story text generation and (2) an output format for generated visual accompaniment to user-provided story texts and user-controlled character motions. We enabled motion-steered story text generation and text-steered motion generation by mapping symbol motions and story texts onto a shared semantic vector space so that motion generation models and large language models can use it as a translational layer. We hope this demonstration sheds light on extending the range of modalities supported by generative human-AI co-creation systems.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686781"}, {"primary_key": "710969", "vector": [], "sparse_vector": [], "title": "Physical and Social Adaptation for Assistive Robot Interactions.", "authors": ["<PERSON>"], "summary": "Robots have the potential to provide users with limited mobility additional ways of interacting with the world around them. However, each user has preferences for how they interact with these physical interfaces. My dissertation research develops tools and algorithms to allow robot interactions to adapt to the individual needs of users. In particular, I develop ways to adapt a robot’s design, physical movements, and social behaviors. By adapting robots to users I hope to develop systems that more holistically aid users with limited mobility.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686713"}, {"primary_key": "710970", "vector": [], "sparse_vector": [], "title": "PyLips: an Open-Source Python Package to Expand Participation in Embodied Interaction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We demonstrate PyLips, a Python package for expanding access to screen-based facial interfaces for text-to-speech. PyLips can be used to rapidly develop social interactions for a wide variety of applications. We designed PyLips to be easy to use for novice users and expressive for experienced interaction designers. We demonstrate key features of PyLips: compatibility across devices, customizable face appearance, and automated lip synching for text inputs. PyLips can be found at https://github.com/interaction-lab/PyLips.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686747"}, {"primary_key": "710971", "vector": [], "sparse_vector": [], "title": "Enabling Advanced Interactions through Closed-loop Control of Motor Unit Activity After Tetraplegia.", "authors": ["Dailyn Despradel <PERSON>del", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Studies have shown that most individuals with motor complete spinal cord injuries (SCIs) can activate motor units (MUs) below the injury level, potentially enabling the use of myoelectric controllers for computer tasks. We present a novel wearable neuromotor interface driven by surface electromyography (sEMG). This wearable sEMG interface detects motor unit action potentials (MUAPs) from an array of sensors on the forearm. The MU event rate is translated (i.e. decoded) into discrete or continuous inputs for emulating button presses or joystick inputs. Our study demonstrates real-time detection and decoding of MUAPs in two individuals with tetraplegia, enabling computer task control via a non-invasive interface. Participants calibrated a spike sorting model through periods of rest and tonic activation, mapping MUAP firing rates to various degrees of freedom (DOF) for cursor or character control in tasks and 2D games. Each MUAP was mapped to a separate DOF axis, which we utilized in a Fitts’ Law target acquisition task to evaluate device throughput rate and other performance metrics such as completion rate, initiation time, time to target acquisition, and dial-in time. Our findings highlight MU firing as an effective control input, enabling gaming and social interaction for individuals with tetraplegia. This device’s wearability and ease of use offer an innovative human-computer interaction solution that may enable people with SCI to interact freely with computers and other digital devices.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686325"}, {"primary_key": "710972", "vector": [], "sparse_vector": [], "title": "Who did it? How User Agency is influenced by Visual Properties of Generated Images.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The increasing proliferation of AI and GenAI requires new interfaces tailored to how their specific affordances and human requirements meet. As GenAI is capable of taking over tasks from users on an unprecedented scale, designing the experience of agency – if and how users experience control over the process and attribution of the outcome – is crucial. As an initial step towards design guidelines for shaping agency, we present a study that explores how properties of AI-generated images influence users’ experience of agency. We use two measures; temporal binding to implicitly estimate pre-reflective agency and magnitude estimation to assess user judgments of agency. We observe that abstract images lead to more temporal binding than images with semantic meaning. In contrast, the closer an image aligns with what a user might expect, the higher the agency judgment. When comparing the experiment results with objective metrics of image differences, we find that temporal binding results correlate with semantic differences, while agency judgments are better explained by local differences between images. This work contributes towards a future where agency is considered an important design dimension for GenAI interfaces.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676335"}, {"primary_key": "710973", "vector": [], "sparse_vector": [], "title": "Demo of FlowRing: Seamless Cross-Surface Interaction via Opto-Acoustic Ring.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Shwetak N. Patel"], "summary": "We demonstrate FlowRing, a ring-form-factor input device that enables interaction across a range of ad-hoc surfaces including desks, pants, palms and fingertips with seamless switching between them. This versatility supports systems that require both high precision as well as mobile control, such as mobile XR. FlowRing consists of a miniature optical flow sensor, skin-contact microphone, and IMU, providing a unique ergonomic design that rests at the base of the finger like conventional jewelry. We show the potential of FlowRing to enable precise control of interfaces on available surfaces via music player application and whiteboarding application.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686744"}, {"primary_key": "710974", "vector": [], "sparse_vector": [], "title": "Augmented Object Intelligence with XR-Objects.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>"], "summary": "Seamless integration of physical objects as interactive digital entities remains a challenge for spatial computing. This paper explores Augmented Object Intelligence (AOI) in the context of XR, an interaction paradigm that aims to blur the lines between digital and physical by equipping real-world objects with the ability to interact as if they were digital, where every object has the potential to serve as a portal to digital functionalities. Our approach utilizes real-time object segmentation and classification, combined with the power of Multimodal Large Language Models (MLLMs), to facilitate these interactions without the need for object pre-registration. We implement the AOI concept in the form of XR-Objects, an open-source prototype system that provides a platform for users to engage with their physical environment in contextually relevant ways using object-based context menus. This system enables analog objects to not only convey information but also to initiate digital actions, such as querying for details or executing tasks. Our contributions are threefold: (1) we define the AOI concept and detail its advantages over traditional AI assistants, (2) detail the XR-Objects system’s open-source design and implementation, and (3) show its versatility through various use cases and a user study.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676379"}, {"primary_key": "710975", "vector": [], "sparse_vector": [], "title": "UICrit: Enhancing Automated Design Evaluation with a UI Critique Dataset.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gang Li", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Automated UI evaluation can be beneficial for the design process; for example, to compare different UI designs, or conduct automated heuristic evaluation. LLM-based UI evaluation, in particular, holds the promise of generalizability to a wide variety of UI types and evaluation tasks. However, current LLM-based techniques do not yet match the performance of human evaluators. We hypothesize that automatic evaluation can be improved by collecting a targeted UI feedback dataset and then using this dataset to enhance the performance of general-purpose LLMs. We present a targeted dataset of 3,059 design critiques and quality ratings for 983 mobile UIs, collected from seven designers, each with at least a year of professional design experience. We carried out an in-depth analysis to characterize the dataset’s features. We then applied this dataset to achieve a 55% performance gain in LLM-generated UI feedback via various few-shot and visual prompting techniques. We also discuss future applications of this dataset, including training a reward model for generative UI techniques, and fine-tuning a tool-agnostic multi-modal LLM that automates UI evaluation.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676381"}, {"primary_key": "710976", "vector": [], "sparse_vector": [], "title": "LessonPlanner: Assisting Novice Teachers to Prepare Pedagogy-Driven Lesson Plans with Large Language Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Preparing a lesson plan, e.g., a detailed road map with strategies and materials for instructing a 90-minute class, is beneficial yet challenging for novice teachers. Large language models (LLMs) can ease this process by generating adaptive content for lesson plans, which would otherwise require teachers to create from scratch or search existing resources. In this work, we first conduct a formative study with six novice teachers to understand their needs for support of preparing lesson plans with LLMs. Then, we develop LessonPlanner that assists users to interactively construct lesson plans with adaptive LLM-generated content based on Gagne's nine events. Our within-subjects study (N=12) shows that compared to the baseline ChatGPT interface, LessonPlanner can significantly improve the quality of outcome lesson plans and ease users' workload in the preparation process. Our expert interviews (N=6) further demonstrate <PERSON>onPlanner's usefulness in suggesting effective teaching strategies and meaningful educational resources. We discuss concerns on and design considerations for supporting teaching activities with LLMs.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676390"}, {"primary_key": "710977", "vector": [], "sparse_vector": [], "title": "SpinShot: Optimizing Both Physical and Perceived Force Feedback of Flywheel-Based, Directional Impact Handheld Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Real-world impact, such as hitting a tennis ball and a baseball, generates instantaneous, directional impact forces. However, current ungrounded force feedback technologies, such as air jets and propellers, can only generate directional impulses that are 10x-10,000x weaker. We present SpinShot, a flywheel-based device with a solenoid-actuated stopper capable of generating directional impulse of 22Nm in 1ms, which is more than 10x stronger than prior ungrounded directional technologies. Furthermore, we present a novel force design that reverses the flywheel immediately after the initial impact, to significantly increase the perceived magnitude. We conducted a series of two formative, perceptual studies (n=16, 18), followed by a summative user experience study (n=16) that compared SpinShot vs. moving mass (solenoid) and vs. air jets in a VR baseball hitting game. Results showed that SpinShot significantly improved realism, immersion, magnitude (p < .01) compared to both baselines, but significantly reduced comfort vs. air jets primarily due to the 2.9x device weight. Overall, SpinShot was preferred by 63-75% of the participants.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676433"}, {"primary_key": "710978", "vector": [], "sparse_vector": [], "title": "WatchThis: A Wearable Point-and-Ask Interface powered by Vision-Language Models for Contextual Queries.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces WatchThis, a novel wearable device that enables natural language interactions with real-world objects and environments through pointing gestures. Building upon previous work in gesture-based computing interfaces, WatchThis leverages recent advancements in Large Language Models (LLM) and Vision Language Models (VLM) to create a hands-free, contextual querying system. The prototype consists of a wearable watch with a rotating, flip-up camera that captures the area of interest when pointing, allowing users to ask questions about their surroundings in natural language. This design addresses limitations of existing systems that require specific commands or occupy the hands, while also maintaining a non-discrete form factor for social awareness. The paper explores various applications of this point-and-ask interaction, including object identification, translation, and instruction queries. By utilizing off-the-shelf components and open-sourcing the design, this work aims to facilitate further research and development in wearable, AI-enabled interaction paradigms.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686776"}, {"primary_key": "710979", "vector": [], "sparse_vector": [], "title": "Investigating the Design Space of Affective Touch on the Forearm Area.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Affective touch, which involves slow, gentle mechanical stimulation of the skin, is drawing interest from the Human-Computer Interaction community in recent years. Stroking, the most common form of affective touch, is strongly related to emotional responses and has been proven beneficial for interventing and mitigating anxiety. This has led to a growing need for developing wearable stroking devices. In our study, we first presented a custom-built forearm-worn interface. We then explored the design parameters for stroking devices, focusing on 2 factors: (1) the form factors (shape / material) of the end effector contacted directly with the skin, and (2) the stroking distance, respectively.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686320"}, {"primary_key": "710980", "vector": [], "sparse_vector": [], "title": "Game Jam with CARDinality: A Case Study of Exploring Play-based Interactive Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>rrow", "<PERSON>"], "summary": "This paper delves into a Game Jam, a workshop-based investigation widespread in the game design community, to explore the potential and applications of CARDinality, a card-shaped robotic device, giving insights into the material implications and prototyping potential of this novel platform. During the Game Jam, 9 participants were informed of basic game design principles and techniques from a game design researcher. They then interacted with our hardware devices to brainstorm future card games supported by CARDinality, revealing a breadth of unique applications anchored in ‘play.’", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686347"}, {"primary_key": "710981", "vector": [], "sparse_vector": [], "title": "Predicting the Limits: Tailoring Unnoticeable Hand Redirection Offsets in Virtual Reality to Individuals&apos; Perceptual Boundaries.", "authors": ["<PERSON>", "Kora Persephone Regitz", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many illusion and interaction techniques in Virtual Reality (VR) rely on Hand Redirection (HR), which has proved to be effective as long as the introduced offsets between the position of the real and virtual hand do not noticeably disturb the user experience. Yet calibrating HR offsets is a tedious and time-consuming process involving psychophysical experimentation, and the resulting thresholds are known to be affected by many variables—limiting HR’s practical utility. As a result, there is a clear need for alternative methods that allow tailoring HR to the perceptual boundaries of individual users. We conducted an experiment with 18 participants combining movement, eye gaze and EEG data to detect HR offsets Below, At, and Above individuals’ detection thresholds. Our results suggest that we can distinguish HR At and Above from no HR. Our exploration provides a promising new direction with potentially strong implications for the broad field of VR illusions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676425"}, {"primary_key": "710982", "vector": [], "sparse_vector": [], "title": "OptiBasePen: Mobile Base+Pen Input on Passive Surfaces by Sensing Relative Base Motion Plus Close-Range Pen Position.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Digital pen input devices based on absolute pen position sensing, such as Wacom Pens, support high-fidelity pen input. However, they require specialized sensing surfaces like drawing tablets, which can have a large desk footprint, constrain the possible input area, and limit mobility. In contrast, digital pens with integrated relative sensing enable mobile use on passive surfaces, but suffer from motion artifacts or require surface contact at all times, deviating from natural pen affordances. We present OptiBasePen, a device for mobile pen input on ordinary surfaces. Our prototype consists of two parts: the \"base\" on which the hand rests and the pen for fine-grained input. The base features a high-precision mouse sensor to sense its own relative motion, and two infrared image sensors to track the absolute pen tip position within the base’s frame of reference. This enables pen input on ordinary surfaces without external cameras while also avoiding drift from pen micro-movements. In this work, we present our prototype as well as the general base+pen concept, which combines relative and absolute sensing.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676467"}, {"primary_key": "710983", "vector": [], "sparse_vector": [], "title": "PointerVol: A Laser Pointer for Swept Volumetric Displays.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A laser pointer is a commonly used device that does not require communication with the display system or modifications on the applications, the presenter can just take a pointer and start using it. When a laser pointer is used on a volumetric display, a line rather than a point appears, making it not suitable for pointing at 3D locations. PointerVol is a modified laser pointer that allows users to point to 3D positions inside a swept volumetric display. We propose two PointerVol implementations based on timing and distance measurements, we evaluate the pointing performance using them. Finally, we present other features such as multi-user pointing, line patterns and a multi-finger wearable. PointerVol is a simple device that can help to popularize volumetric displays, or at least to make them more usable for presentations with true-3D content.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676432"}, {"primary_key": "710984", "vector": [], "sparse_vector": [], "title": "Qlarify: Recursively Expandable Abstracts for Dynamic Information Retrieval over Scientific Papers.", "authors": ["<PERSON>", "<PERSON>", "Tal August", "<PERSON>", "<PERSON>"], "summary": "Navigating the vast scientific literature often starts with browsing a paper’s abstract. However, when a reader seeks additional information, not present in the abstract, they face a costly cognitive chasm during their dive into the full text. To bridge this gap, we introduce recursively expandable abstracts, a novel interaction paradigm that dynamically expands abstracts by progressively incorporating additional information from the papers’ full text. This lightweight interaction allows scholars to specify their information needs by quickly brushing over the abstract or selecting AI-suggested expandable entities. Relevant information is synthesized using a retrieval-augmented generation approach, presented as a fluid, threaded expansion of the abstract, and made efficiently verifiable via attribution to relevant source-passages in the paper. Through a series of user studies, we demonstrate the utility of recursively expandable abstracts and identify future opportunities to support low-effort and just-in-time exploration of long-form information contexts through LLM-powered interactions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676397"}, {"primary_key": "710985", "vector": [], "sparse_vector": [], "title": "&quot;The Data Says Otherwise&quot; - Towards Automated Fact-checking and Communication of Data Claims.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fact-checking data claims requires data evidence retrieval and analysis, which can become tedious and intractable when done manually. This work presents Aletheia, an automated fact-checking prototype designed to facilitate data claims verification and enhance data evidence communication. For verification, we utilize a pre-trained LLM to parse the semantics for evidence retrieval. To effectively communicate the data evidence, we design representations in two forms: data tables and visualizations, tailored to various data fact types. Additionally, we design interactions that showcase a real-world application of these techniques. We evaluate the performance of two core NLP tasks with a curated dataset comprising 400 data claims and compare the two representation forms regarding viewers' assessment time, confidence, and preference via a user study with 20 participants. The evaluation offers insights into the feasibility and bottlenecks of using LLMs for data fact-checking tasks, potential advantages and disadvantages of using visualizations over data tables, and design recommendations for presenting data evidence.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676359"}, {"primary_key": "710986", "vector": [], "sparse_vector": [], "title": "ShadowMagic: Designing Human-AI Collaborative Support for Comic Professionals&apos; Shadowing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Shadowing allows artists to convey realistic volume and emotion of characters in comic colorization. While AI technologies have the potential to improve professionals’ shadowing experience, current practice is manual and time-consuming. To understand how we can improve their shadowing experience, we conducted interviews with 5 professionals. We found that professionals’ level of engagement can vary depending on semantics, such as characters’ faces or hair. We also found they spent time on shadow “landscaping”—deciding where to put big shadow regions to make a realistic volumetric presentation—while the final results can dramatically vary depending on their “staging” and “attention guiding” needs. We found they would accept AI suggestions for less engaging semantic parts or landscaping, while they would need to have the capability to adjust details. Based on our observations, we built ShadowMagic that (1) generates AI-driven shadows based on typically used light directions, (2) enables a user to selectively choose the results depending on the semantics, and (3) allows users to finish shadow areas by themselves for further perfection. Through a summative evaluation with 5 professionals, we found that they were significantly more satisfied with our AI-driven results than a baseline. We also found ShadowMagic’s “step by step” workflow helps participants more easily adopt AI-driven results. We conclude by providing implications.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676332"}, {"primary_key": "710987", "vector": [], "sparse_vector": [], "title": "Mul-O: Encouraging Olfactory Innovation in Various Scenarios Through a Task-Oriented Development Platform.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Shao<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu"], "summary": "Olfactory interfaces are pivotal in HCI, yet their development is hindered by limited application scenarios, stifling the discovery of new research opportunities. This challenge primarily stems from existing design tools focusing predominantly on odor display devices and the creation of standalone olfactory experiences, rather than enabling rapid adaptation to various contexts and tasks. Addressing this, we introduce Mul-O, a novel task-oriented development platform crafted to aid semi-professionals in navigating the diverse requirements of potential application scenarios and effectively prototyping ideas. Mul-O facilitates the swift association and integration of olfactory experiences into functional designs, system integrations, and concept validations. Comprising a web UI for task-oriented development, an API server for seamless third-party integration, and wireless olfactory display hardware, Mul-O significantly enhances the ideation and prototyping process in multisensory tasks. This was verified by a 15-day workshop attended by 30 participants. The workshop produced seven innovative projects, underscoring Mul-O’s efficacy in fostering olfactory innovation.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676387"}, {"primary_key": "710988", "vector": [], "sparse_vector": [], "title": "Towards Multimodal Interaction with AI-Infused Shape-Changing Interfaces.", "authors": ["<PERSON><PERSON> Gao", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a proof-of-concept system exploring multimodal interaction with AI-infused Shape-Changing Interfaces. Our prototype integrates inFORCE, a 10x5 pin-based shape display, with AI tools for 3D mesh generation and editing. Users can create and modify 3D shapes through speech, gesture, and tangible inputs. We demonstrate potential applications including AI-assisted 3D modeling, adaptive physical controllers, and dynamic furniture. Our implementation, which translates text to point clouds for physical rendering, reveals both the potential and challenges of combining AI with shape-changing interfaces. This work explores how AI can enhance tangible interaction with 3D information and opens up new possibilities for multimodal shape-changing UIs.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686315"}, {"primary_key": "710989", "vector": [], "sparse_vector": [], "title": "MOCHA: Model Optimization through Collaborative Human-AI Alignment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "We present MOCHA, a novel interactive system designed to enhance data annotation in natural language processing. MOCHA integrates active learning with counterfactual data augmentation, allowing users to better align model behaviors with their intentions, preferences, and values through annotations. Utilizing principles from Variation Theory and Structural Alignment Theory, MOCHA (1) generates counterfactual examples that reveal key data variations and commonalities for users to annotate; and (2) presents them in a way that highlights shared analogical structures. This design reduces the cognitive load on users, making it easier for them to understand and reflect on the data. Consequently, this approach not only improves the clarity and efficiency of annotation but also fosters the creation of high-quality datasets and more effectively trained models.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686760"}, {"primary_key": "710990", "vector": [], "sparse_vector": [], "title": "Tyche: Making Sense of PBT Effectiveness.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>-<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software developers increasingly rely on automated methods to assess the correctness of their code. One such method is property-based testing (PBT), wherein a test harness generates hundreds or thousands of inputs and checks the outputs of the program on those inputs using parametric properties. Though powerful, PBT induces a sizable gulf of evaluation: developers need to put in nontrivial effort to understand how well the different test inputs exercise the software under test. To bridge this gulf, we propose Tyche, a user interface that supports sensemaking around the effectiveness of property-based tests. Guided by a formative design exploration, our design of Tyche supports developers with interactive, configurable views of test behavior with tight integrations into modern developer testing workflow. These views help developers explore global testing behavior and individual test inputs alike. To accelerate the development of powerful, interactive PBT tools, we define a standard for PBT test reporting and integrate it with a widely used PBT library. A self-guided online usability study revealed that Tyche’s visualizations help developers to more accurately assess software testing effectiveness.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676407"}, {"primary_key": "710991", "vector": [], "sparse_vector": [], "title": "Demonstrating XDTK: Prototyping Multi-Device Interaction and Arbitration in XR.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The interaction space of XR head-mounted devices can be extended by leveraging other digital devices, such as phones, tablets, and smartwatches. We present a demonstration of XDTK (Cross-Device Toolkit), an open-sourced prototyping toolkit for multi-device interactions in XR. The toolkit consists of two parts: (1) an Android app that runs on client devices and surfaces pose, touch, and other sensor data to a (2) Unity server that can be added to any Unity-based XR application. For this demo, we specifically apply XDTK toward a few example applications, including multi-device arbitration. By leveraging pose data from each device, we can infer which device the user is gazing at to seamlessly hand off control and display between multiple devices. We also show examples leveraging a tablet sketching and a smartwatch for menu navigation.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686784"}, {"primary_key": "710992", "vector": [], "sparse_vector": [], "title": "Facilitating the Parametric Definition of Geometric Properties in Programming-Based CAD.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Parametric Computer-aided design (CAD) enables the creation of reusable models by integrating variables into geometric properties, facilitating customization without a complete redesign.However, creating parametric designs in programming-based CAD presents significant challenges.Users define models in a code editor using a programming language, with the application generating a visual representation in a viewport.This process involves complex programming and arithmetic expressions to describe geometric properties, linking various object properties to create parametric designs.Unfortunately, these applications lack assistance, making the process unnecessarily demanding.We propose a solution that allows users to retrieve parametric expressions from the visual representation for reuse in the code, streamlining the design process.We demonstrated this concept through a proof-of-concept implemented in the programming-based CAD application, OpenSCAD, and conducted an experiment with 11 users.Our findings suggest that this solution could significantly reduce design errors, improve interactivity and engagement in the design process, and lower the entry barrier for newcomers by reducing the mathematical skills typically required in programming-based CAD applications.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676417"}, {"primary_key": "710993", "vector": [], "sparse_vector": [], "title": "VizAbility: Enhancing Chart Accessibility with LLM-based Conversational Interaction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Traditional accessibility methods like alternative text and data tables typically underrepresent data visualization’s full potential. Keyboard-based chart navigation has emerged as a potential solution, yet efficient data exploration remains challenging. We present VizAbility, a novel system that enriches chart content navigation with conversational interaction, enabling users to use natural language for querying visual data trends. VizAbility adapts to the user’s navigation context for improved response accuracy and facilitates verbal command-based chart navigation. Furthermore, it can address queries for contextual information, designed to address the needs of visually impaired users. We designed a large language model (LLM)-based pipeline to address these user queries, leveraging chart data & encoding, user context, and external web knowledge. We conducted both qualitative and quantitative studies to evaluate VizAbility’s multimodal approach. We discuss further opportunities based on the results, including improved benchmark testing, incorporation of vision models, and integration with visualization workflows.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676414"}, {"primary_key": "710994", "vector": [], "sparse_vector": [], "title": "TRAvel Slicer: Continuous Extrusion Toolpaths for 3D Printing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we present Travel Reduction Algorithm (TRAvel) Slicer, which minimizes travel movements in 3D printing. Conventional slicing software generates toolpaths with many travel movements–movements without material extrusion. Some 3D printers are incapable of starting and stopping extrusion and it is difficult to impossible to control the extrusion of many materials. This makes toolpaths with travel movements unsuitable for a wide range of printers and materials. We developed the open-source TRAvel Slicer to enable the printing of complex 3D models on a wider range of printers and in a wider range of materials than is currently possible. TRAvel Slicer minimizes two different kinds of travel movements–what we term Inner- and Outer-Model travel. We minimize Inner-Model travel (travel within the 3D model) by generating space-filling Fermat spirals for each contiguous planar region of the model. We minimize Outer-Model travel (travels outside of the 3D model) by ordering the printing of different branches of the model, thus limiting transitions between branches. We present our algorithm and software and then demonstrate how: 1) TRAvel Slicer makes it possible to generate high-quality prints from a metal-clay material, CeraMetal, that is functionally unprintable using an off-the-shelf slicer. 2) TRAvel Slicer dramatically increases the printing efficiency of traditional plastic 3D printing compared to an off-the-shelf slicer.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676349"}, {"primary_key": "710995", "vector": [], "sparse_vector": [], "title": "A New Approach for Volumetric Knitting.", "authors": ["<PERSON>", "Amritansh Kwatra", "<PERSON>", "<PERSON>"], "summary": "Unlike 3D printers, which offer the ability to fabricate nearly arbitrary geometric forms, most textile fabrication processes are limited to the creation of sheets or hollow surface-based forms. This poster presents a new machine architecture to directly produce volumetric (solid 3D) knitted forms using a 2D bed of knitting needles, rather than the 1D line of needles used in conventional knitting. We describe a small prototype with 4x4 needles, and demonstrate that it can create fully volumetric knits, including overhangs.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686337"}, {"primary_key": "710996", "vector": [], "sparse_vector": [], "title": "Augmented Physics: Creating Interactive and Embedded Physics Simulations from Static Textbook Diagrams.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Augmented Physics is a machine learning-integrated authoring tool to transform static physics diagrams into embedded interactive simulations for various topics, such as optics, kinematics, pendulum, and electric circuits.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676392"}, {"primary_key": "710997", "vector": [], "sparse_vector": [], "title": "Understanding and Supporting Debugging Workflows in CAD.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "One of the core promises of parametric Computer-Aided Design (CAD) is that users can easily edit their model at any point in time. However, due to the ambiguity of changing references to intermediate, updated geometry, parametric edits can lead to reference errors which are difficult to fix in practice. We claim that debugging reference errors remains challenging because CAD systems do not provide users with tools to understand where the error happened and how to fix it. To address these challenges, we prototype a graphical debugging tool, DeCAD, which helps comparing CAD model states both across operations and across edits. In a qualitative lab study, we use DeCAD as a probe to understand specific challenges that users face and what workflows they employ to overcome them. We conclude with design implications for future debugging tool developers.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676353"}, {"primary_key": "710998", "vector": [], "sparse_vector": [], "title": "Breaking Future Rhythm Visualizer.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In breaking, the timing of the technique and rhythm needs to be matched, but the audience has trouble judging whether or not the timing is correct. Therefore, We developed a system that shows the future rhythm of the sound source the DJ is playing.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686739"}, {"primary_key": "710999", "vector": [], "sparse_vector": [], "title": "IntelliCID: Intelligent Caustics Illumination Device.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Environmental illumination has the power to influence our daily lives to some extent. When combined with the inherent human affinity for nature, the presence of natural illumination, such as caustics and komorebi, can be used to create a relaxing environment. However, if this illumination does not seamlessly adapt to our daily activities, it may become overwhelming and unpleasant. To address this issue, we present IntelliCID, an intelligent and interactive system designed for smart environments. This system is capable of sensing the user’s state and adjusting the intensity of the dynamic caustics lighting effect accordingly.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686733"}, {"primary_key": "711000", "vector": [], "sparse_vector": [], "title": "Selfrionette: A Fingertip Force-Input Controller for Continuous Full-Body Avatar Manipulation and Diverse Haptic Interactions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose Selfrionette, a controller that uses fingertip force input to drive avatar movements in virtual reality (VR). This system enables users to interact with virtual objects and walk in VR using only fingertip force, overcoming physical and spatial constraints. Additionally, by fixing users’ fingers, it provides users with counterforces equivalent to the applied force, allowing for diverse and wide dynamic range haptic feedback by adjusting the relationship between force input and virtual movement. To evaluate the effectiveness of the proposed method, this paper focuses on hand interaction as a first step. In User Study 1, we measured usability and embodiment during reaching tasks under Selfrionette, body tracking, and finger tracking conditions. In User Study 2, we investigated whether users could perceive haptic properties such as weight, friction, and compliance under the same conditions as User Study 1. Selfrionette was found to be comparable to body tracking in realism of haptic interaction, enabling embodied avatar experiences even in limited spatial conditions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676409"}, {"primary_key": "711001", "vector": [], "sparse_vector": [], "title": "An Interactive System for Supporting Creative Exploration of Cinematic Composition Designs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Designing cinematic compositions, which involves moving cameras through a scene, is essential yet challenging in filmmaking. Machinima filmmaking provides real-time virtual environments for exploring different compositions flexibly and efficiently. However, producing high-quality cinematic compositions in such environments still requires significant cinematography skills and creativity. This paper presents Cinemassist , a tool designed to support and enhance this creative process by generating a variety of cinematic composition proposals at both keyframe and scene levels, which users can incorporate into their workflows and achieve more creative results. At the crux of our system is a deep generative model trained on real movie data, which can generate plausible, diverse camera poses conditioned on 3D animations and additional input semantics. Our model enables an interactive cinematic composition design workflow where users can co-design with the model by being inspired by model-generated suggestions while having control over the generation process. Our user study and expert rating find Cinemassist can facilitate the design process for users of different backgrounds and enhance the design quality especially for users with animation expertise, demonstrating its potential as an invaluable tool in the context of digital filmmaking.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676393"}, {"primary_key": "711002", "vector": [], "sparse_vector": [], "title": "ProgramAlly: Creating Custom Visual Access Programs via Multi-Modal End-User Programming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing visual assistive technologies are built for simple and common use cases, and have few avenues for blind people to customize their functionalities. Drawing from prior work on DIY assistive technology, this paper investigates end-user programming as a means for users to create and customize visual access programs to meet their unique needs. We introduce ProgramAlly, a system for creating custom filters for visual information, e.g., 'find NUMBER on BUS', leveraging three end-user programming approaches: block programming, natural language, and programming by example. To implement ProgramAlly, we designed a representation of visual filtering tasks based on scenarios encountered by blind people, and integrated a set of on-device and cloud models for generating and running these programs. In user studies with 12 blind adults, we found that participants preferred different programming modalities depending on the task, and envisioned using visual access programs to address unique accessibility challenges that are otherwise difficult with existing applications. Through ProgramAlly, we present an exploration of how blind end-users can create visual access programs to customize and control their experiences.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676391"}, {"primary_key": "711003", "vector": [], "sparse_vector": [], "title": "Conductive Fabric Diaphragm for Noise-Suppressive Headset Microphone.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Voice interaction systems require high-quality speech input; however, ambient noise significantly degrades the performance of conventional microphones. While multimodal sensor-based speech enhancement techniques have been proposed, they typically require GPU devices for processing. We proposed a conductive fabric diaphragm headset microphone, which integrates a planar fabric microphone into a headset configuration. The proposed microphone exhibits low sensitivity and curved directivity characteristics, enabling efficient capture of the user’s voice while simultaneously suppressing background and competing speech noises. Comparative analyses against two commercially available noise-cancelling headsets demonstrated that our approach achieved superior Scale-Invariant Signal-to-Noise Ratio (Si-SNR) performance. Our approach constitutes a real-time, hardware-based noise-suppressive microphone solution with potential applications in speech communication, automatic speech recognition, and voice conversion technologies operating in noise-rich environments.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686768"}, {"primary_key": "711004", "vector": [], "sparse_vector": [], "title": "Piezoelectric Sensing of Mask Surface Waves for Noise-Suppressive Speech Input.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Voice-controlled medical devices in operating rooms face challenges due to face masks and noise, hindering speech recognition accuracy. We present a novel solution: a detachable piezoelectric microphone attached to a face mask, prioritizing hygiene and comfort. By capturing surface vibrations from the user’s voice, our system significantly improves scale-invariant signal-to-distortion ratio (Si-SNR) compared to traditional unidirectional microphones, especially for whispered speech, as demonstrated in a simulated noisy environment. This real-time, noise-suppressive approach offers a promising avenue for enhancing voice input and conversations in various applications and settings.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686331"}, {"primary_key": "711005", "vector": [], "sparse_vector": [], "title": "KODA: Knit-program Optimization by Dependency Analysis.", "authors": ["<PERSON>"], "summary": "Digital knitting machines have the capability to reliably manufacture seamless, textured, and multi-material garments, but these capabilities are obscured by limiting CAD tools. Recent innovations in computational knitting build on emerging programming infrastructure that gives full access to the machine’s capabilities but requires an extensive understanding of machine operations and execution. In this paper, we contribute a critical missing piece of the knitting-machine programming pipeline–a program optimizer. Program optimization allows programmers to focus on developing novel algorithms that produce desired fabrics while deferring concerns of efficient machine operations to the optimizer. We present KODA, the Knit-program Optimization by Dependency Analysis method. KODA re-orders and reduces machine instructions to reduce knitting time, increase knitting reliability, and manage boilerplate operations that adjust the machine state. The result is a system that enables programmers to write readable and intuitive knitting algorithms while producing efficient and verified programs.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676405"}, {"primary_key": "711006", "vector": [], "sparse_vector": [], "title": "Democratizing Intelligent Soft Wearables.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wearables have long been integral to human culture and daily life. Recent advances in intelligent soft wearables have dramatically transformed how we interact with the world, enhancing our health, productivity, and overall well-being. These innovations, combining advanced sensor design, fabrication, and computational power, offer unprecedented opportunities for monitoring, assistance, and augmentation. However, the benefits of these advancements are not yet universally accessible. Economic and technical barriers often limit the reach of these technologies to domain-specific experts. There is a growing need for democratizing intelligent wearables that are scalable, seamlessly integrated, customized, and adaptive. By bringing researchers from relevant disciplines together, this workshop aims to identify the challenges and investigate opportunities for democratizing intelligent soft wearables within the HCI community via interactive demos, invited keynotes, and focused panel discussions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686707"}, {"primary_key": "711007", "vector": [], "sparse_vector": [], "title": "Experiencing Thing2Reality: Transforming 2D Content into Conditioned Multiviews and 3D Gaussian Objects for XR Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Seongkook Heo", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "During remote communication, participants share both digital and physical content, such as product designs, digital assets, and environments, to enhance mutual understanding. Recent advances in augmented communication have facilitated users to swiftly create and share digital 2D copies of physical objects from video feeds into a shared space. However, the conventional 2D representation of digital objects restricts users’ ability to spatially reference items in a shared immersive environment. To address these challenges, we propose Thing2Reality, an Extended Reality (XR) communication platform designed to enhance spontaneous discussions regarding both digital and physical items during remote sessions. With Thing2Reality, users can quickly materialize ideas or physical objects in an immersive environment and share them as conditioned multiview renderings or 3D Gaussians. Our system enables users to interact with remote objects or discuss concepts in a collaborative manner.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686740"}, {"primary_key": "711008", "vector": [], "sparse_vector": [], "title": "Demonstrating VibraForge: An Open-source Vibrotactile Prototyping Toolkit with Scalable Modular Design.", "authors": ["<PERSON><PERSON><PERSON>", "Hanfeng Cai", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We demonstrate VibraForge, an open-source vibrotactile prototyping toolkit1 that supports fine-grained control of up to 120 vibrotactile actuators. Our solution features modular and scalable design principles, self-contained vibration units, chain-connection topology, and custom communication protocol. Additionally, we offer a GUI editor for intuitive multi-actuator pattern authoring. This toolkit significantly lowers the barriers to haptic design and expands the design space for multi-actuator applications. In our demonstration, participants will have the chance to experience the full process of designing vibrotactile systems from scratch using VibraForge. This includes assembling vibrotactile wearable devices, mounting them on clothes, designing vibration patterns, and real-time evaluation. Our goal is to showcase the transformative potential of multi-actuator systems and lower the barriers to haptic design for researchers and designers.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686764"}, {"primary_key": "711009", "vector": [], "sparse_vector": [], "title": "VirtualNexus: Enhancing 360-Degree Video AR/VR Collaboration with Environment Cutouts and Virtual Replicas.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Asymmetric AR/VR collaboration systems bring a remote VR user to a local AR user’s physical environment, allowing them to communicate and work within a shared virtual/physical space. Such systems often display the remote environment through 3D reconstructions or 360° videos. While 360° cameras stream an environment in higher quality, they lack spatial information, making them less interactable. We present VirtualNexus, an AR/VR collaboration system that enhances 360° video AR/VR collaboration with environment cutouts and virtual replicas. VR users can define cutouts of the remote environment to interact with as a world-in-miniature, and their interactions are synchronized to the local AR perspective. Furthermore, AR users can rapidly scan and share 3D virtual replicas of physical objects using neural rendering. We demonstrated our system’s utility through 3 example applications and evaluated our system in a dyadic usability test. VirtualNexus extends the interaction space of 360° telepresence systems, offering improved physical presence, versatility, and clarity in interactions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676377"}, {"primary_key": "711010", "vector": [], "sparse_vector": [], "title": "DesignChecker: Visual Design Support for Blind and Low Vision Web Developers.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Blind and low vision (BLV) developers create websites to share knowledge and showcase their work. A well-designed website can engage audiences and deliver information effectively, yet it remains challenging for BLV developers to review their web designs. We conducted interviews with BLV developers (N=9) and analyzed 20 websites created by BLV developers. BLV developers created highly accessible websites but wanted to assess the usability of their websites for sighted users and follow the design standards of other websites. They also encountered challenges using screen readers to identify illegible text, misaligned elements, and inharmonious colors. We present DesignChecker, a browser extension that helps BLV developers improve their web designs. With DesignChecker, users can assess their current design by comparing it to visual design guidelines, a reference website of their choice, or a set of similar websites. DesignChecker also identifies the specific HTML elements that violate design guidelines and suggests CSS changes for improvements. Our user study participants (N=8) recognized more visual design errors than using their typical workflow and expressed enthusiasm about using DesignChecker in the future.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676369"}, {"primary_key": "711011", "vector": [], "sparse_vector": [], "title": "ValueSphere: A Portable Widget for Quick and Easy Shading in Digital Drawings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Marker shading is essential for communicating 3D forms in the early stages of product design. Inspired by a technique of marker shading that is widely used by designers, this study introduces ValueSphere, a novel widget for quick and easy shading in digital drawings. Using ValueSphere, the user can set the light direction, find accurate shading values, and apply them to the sketch through intuitive pen and multi-touch gestures. We utilized ValueSphere to shade various design sketches and showcase its usefulness.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686350"}, {"primary_key": "711012", "vector": [], "sparse_vector": [], "title": "Hydroptical Thermal Feedback: Spatial Thermal Feedback Using Visible Lights and Water.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We control the temperature of materials in everyday interactions, recognizing temperature’s important influence on our bodies, minds, and experiences. However, thermal feedback is an under-explored modality in human-computer interaction partly due to its limited temporal (slow) and spatial (small-area and non-moving) capabilities. We introduce hydroptical thermal feedback, a spatial thermal feedback method that works by applying visible lights on body parts in water. Through physical measurements and psychophysical experiments, our results show: (1) Humans perceive thermal sensations when visible lights are cast on the skin under water, and perceived warmth is greater for lights with shorter wavelengths, (2) temporal capabilities, (3) apparent motion (spatial) of warmth and coolness sensations, and (4) hydroptical thermal feedback can support the perceptual illusion that the water itself is warmer. We propose applications, including virtual reality (VR), shared water experiences, and therapies. Overall, this paper contributes hydroptical thermal feedback as a novel method, empirical results demonstrating its unique capabilities, proposed applications, and design recommendations for using hydroptical thermal feedback. Our method introduces controlled, spatial thermal perceptions to water experiences.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676453"}, {"primary_key": "711013", "vector": [], "sparse_vector": [], "title": "LoopBot: Representing Continuous Haptics of Grounded Objects in Room-scale VR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kumpei <PERSON>gawa", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In room-scale virtual reality, providing continuous haptic feedback from touching grounded objects, such as walls and handrails, has been challenging due to the user’s walking range and the required force. In this study, we propose LoopBot, a novel technique to provide continuous haptic feedback from grounded objects using only a single user-following robot. Specifically, LoopBot is equipped with a loop-shaped haptic prop attached to an omnidirectional robot that scrolls to cancel out the robot’s displacement, giving the user the haptic sensation that the prop is actually fixed in place, or “grounded.” We first introduce the interaction design space of LoopBot and, as one of its promising interaction scenarios, implement a prototype for the experience of walking while grasping handrails. A performance evaluation shows that scrolling the prop cancels 77.5% of the robot’s running speed on average. A preliminary user test (N = 10) also shows that the subjective realism of the experience and the sense of the virtual handrails being grounded were significantly higher than when the prop was not scrolled. Based on these findings, we discuss possible further development of LoopBot.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676389"}, {"primary_key": "711014", "vector": [], "sparse_vector": [], "title": "Enhancing Readability with a Target-Aware Zooming Technique for Touch Surfaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Double-tapping is a common way to zoom in and out of content on touch surfaces, snapping between overview and detail levels. However, the optimal zoom level often differs for each user depending on the displayed content and user characteristics (e.g., age or visual acuity). When focusing on content containing text, the scaling factor is especially crucial for readability. However, the conventional double-tap to zoom uses a fixed scale factor, which often requires users to manually adjust the zoom level by pinching in and out after double-tapping. Additionally, on small-screen devices such as smartphones, the specific area of interest may not fit within the screen after zooming in, causing users to pan repeatedly to adjust the display position of the content. To address these issues, we propose a target-aware zooming technique that dynamically adjusts the zoom level based on the content and user preferences. Furthermore, to minimize the need for panning, our technique simultaneously snaps the top-left corner of the bounding box of the tapped text to the top-left corner of the screen as the content is zoomed in. This approach aims to reduce the need for manual adjustments, improving usability and readability of digital content for diverse user groups.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686329"}, {"primary_key": "711015", "vector": [], "sparse_vector": [], "title": "Bridging Disciplines for a New Era in Physical AI.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Physical AI is extending models from the digital world into performing tasks in the real world. Robots and autonomous cars are common examples of physical AI agents. In this workshop, we aim to go beyond containing physical AI in those agents and ask what if all objects and materials we interact with were intelligent? We aim to form an understanding of the opportunities and challenges of extending agents to include objects and materials that can adapt to users needs, i.e., change shape, firmness, color, tactile properties, etc. This broad vision, which is challenging to achieve, is related to many active research areas, e.g., programmable matter, modular robotics, soft robotics, smart materials, shape-changing interfaces, or radical atoms, and has homes in many disciplines, incl. mechanical engineering, robotics, material science, computer science. Many new approaches are being developed in the individual disciplines that together might be the start of a new era for what we like to call extended physical AI. In this workshop, we bring perspectives from these different disciplines together to exchange new approaches to longstanding challenges (e.g., actuation, computational design, fabrication, control), exchange tacit knowledge, discuss visions for future applications, map the new grand challenges, and inspire the next generation of physical AI research.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686704"}, {"primary_key": "711016", "vector": [], "sparse_vector": [], "title": "ScreenConcealer: Privacy-protection System with Obfuscations for Screen Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "During screen sharing in virtual meetings, there have been many cases of unintentional disclosure of personal data that can directly identify users (e.g., names or addresses) or private information that directly does not identify users but that they do not want to share (e.g., users’ preferences or online activities). We propose a system that protects personal data and private information on web browsers from being viewed by others. The system automatically takes screenshots of the web-browser window and detects personal data and private information from the images. The system then obfuscates the detected area with blocking or blurring, and displays it as a duplicated window. We present an experiment on a model that detects personal data and private information on web browsers to implement our system. We also introduce several features to improve user experience.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686750"}, {"primary_key": "711017", "vector": [], "sparse_vector": [], "title": "RelieFoam: Rapid Prototyping of 2.5D Texture using Laser Cutter.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose RelieFoam, a rapid prototyping method that uses a laser cutter to produce a 2.5D textured surface on polystyrene foam. Conventional rapid prototyping methods using a laser cutter can quickly create 3D prototypes, but with the limitation that their surfaces can only be flat and smooth. Our method enables the rapid creation of objects with finely detailed 2.5D textured surfaces. By applying localized high-density energy from a laser cutter to low thermal conductivity polystyrene foam, a laser cutter can selectively remove only the specific parts of the surface of the polystyrene foam being targeted. We have built a computational model that calculates the laser parameters required to engrave the polystyrene foam in this way. Applying the laser parameters calculated by our model, we are able to implement applications with haptic textures and visual translucency and thus demonstrate the potential of our method.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686741"}, {"primary_key": "711018", "vector": [], "sparse_vector": [], "title": "Wheeler: A Three-Wheeled Input Device for Usable, Efficient, and Versatile Non-Visual Interaction.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Blind users rely on keyboards and assistive technologies like screen readers to interact with user interface (UI) elements. In modern applications with complex UI hierarchies, navigating to different UI elements poses a significant accessibility challenge. Users must listen to screen reader audio descriptions and press relevant keyboard keys one at a time. This paper introduces <PERSON>, a novel three-wheeled, mouse-shaped stationary input device, to address this issue. Informed by participatory sessions, <PERSON> enables blind users to navigate up to three hierarchical levels in an app independently using three wheels instead of navigating just one level at a time using a keyboard. The three wheels also offer versatility, allowing users to repurpose them for other tasks, such as 2D cursor manipulation. A study with 12 blind users indicates a significant reduction (40%) in navigation time compared to using a keyboard. Further, a diary study with our blind co-author highlights <PERSON>'s additional benefits, such as accessing UI elements with partial metadata and facilitating mixed-ability collaboration.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676396"}, {"primary_key": "711019", "vector": [], "sparse_vector": [], "title": "Demonstration of Wheeler: A Three-Wheeled Input Device for Usable, Efficient, and Versatile Non-Visual Interaction.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Navigating multi-level menus with complex hierarchies remains a big challenge for blind and low-vision users, who predominantly use screen readers to interact with computers. To that end, we demonstrate <PERSON>, a three-wheeled input device with two side buttons that can speed up complex multi-level hierarchy navigation in common applications. When in operation, the three wheels of <PERSON> are each mapped to a different level in the application hierarchy. Each level can be independently traversed using its designated wheel, allowing users to navigate through multiple levels efficiently. <PERSON>'s three wheels can also be repurposed for other tasks such as 2D cursor manipulation. In this demonstration, we describe the different operation modes and usage of <PERSON>.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686749"}, {"primary_key": "711020", "vector": [], "sparse_vector": [], "title": "StreetNav: Leveraging Street Cameras to Support Precise Outdoor Navigation for Blind Pedestrians.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Basel Hindi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Blind and low-vision (BLV) people rely on GPS-based systems for outdoor navigation. GPS’s inaccuracy, however, causes them to veer off track, run into obstacles, and struggle to reach precise destinations. While prior work has made precise navigation possible indoors via hardware installations, enabling this outdoors remains a challenge. Interestingly, many outdoor environments are already instrumented with hardware such as street cameras. In this work, we explore the idea of repurposing existing street cameras for outdoor navigation. Our community-driven approach considers both technical and sociotechnical concerns through engagements with various stakeholders: BLV users, residents, business owners, and Community Board leadership. The resulting system, StreetNav, processes a camera’s video feed using computer vision and gives BLV pedestrians real-time navigation assistance. Our evaluations show that StreetNav guides users more precisely than GPS, but its technical performance is sensitive to environmental occlusions and distance from the camera. We discuss future implications for deploying such systems at scale.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676333"}, {"primary_key": "711021", "vector": [], "sparse_vector": [], "title": "What&apos;s the Game, then? Opportunities and Challenges for Runtime Behavior Generation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Procedural content generation (PCG), the process of algorithmically creating game components instead of manually, has been a common tool of game development for decades. Recent advances in large language models (LLMs) enable the generation of game behaviors based on player input at runtime. Such code generation brings with it the possibility of entirely new gameplay interactions that may be difficult to integrate with typical game development workflows. We explore these implications through GROMIT, a novel LLM-based runtime behavior generation system for Unity. When triggered by a player action, GROMIT generates a relevant behavior which is compiled without developer intervention and incorporated into the game. We create three demonstration scenarios with GROMIT to investigate how such a technology might be used in game development. In a system evaluation we find that our implementation is able to produce behaviors that result in significant downstream impacts to gameplay. We then conduct an interview study with n=13 game developers using GROMIT as a probe to elicit their current opinion on runtime behavior generation tools, and enumerate the specific themes curtailing the wider use of such tools. We find that the main themes of concern are quality considerations, community expectations, and fit with developer workflows, and that several of the subthemes are unique to runtime behavior generation specifically. We outline a future work agenda to address these concerns, including the need for additional guardrail systems for behavior generation.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676358"}, {"primary_key": "711022", "vector": [], "sparse_vector": [], "title": "Embrogami: Shape-Changing Textiles with Machine Embroidery.", "authors": ["Yu <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine embroidery is a versatile technique for creating custom and entirely fabric-based patterns on thin and conformable textile surfaces. However, existing machine-embroidered surfaces remain static, limiting the interactions they can support. We introduce Embrogami, an approach for fabricating textile structures with versatile shape-changing behaviors. Inspired by origami, we leverage machine embroidery to form finger-tip-scale mountain-and-valley structures on textiles with customized shapes, bistable or elastic behaviors, and modular composition. The structures can be actuated by the user or the system to modify the local textile surface topology, creating interactive elements like toggles and sliders or textile shape displays with an ultra-thin, flexible, and integrated form factor. We provide a dedicated software tool and report results of technical experiments to allow users to flexibly design, fabricate, and deploy customized Embrogami structures. With four application cases, we showcase Embrogami’s potential to create functional and flexible shape-changing textiles with diverse visuo-tactile feedback.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676431"}, {"primary_key": "711023", "vector": [], "sparse_vector": [], "title": "MouthIO: Fabricating Customizable Oral User Interfaces with Integrated Sensing and Actuation.", "authors": ["Yi<PERSON>g Jiang", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces MouthIO, the first customizable intraoral user interface that can be equipped with various sensors and output components. MouthIO consists of an SLA-printed brace that houses a flexible PCB within a bite-proof enclosure positioned between the molar teeth and inner cheeks. Our MouthIO design and fabrication technique enables makers to customize the oral user interfaces in both form and function at low cost. All parts in contact with the oral cavity are made of bio-compatible materials to ensure safety, while the design takes into account both comfort and portability. We demonstrate MouthIO through three application examples ranging from beverage consumption monitoring, health monitoring, to assistive technology. Results from our full-day user study indicate high wearability and social acceptance levels, while our technical evaluation demonstrates the device’s ability to withstand adult bite forces.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676443"}, {"primary_key": "711024", "vector": [], "sparse_vector": [], "title": "Demonstration of MouthIO: Customizable Oral User Interfaces with Integrated Sensing and Actuation.", "authors": ["Yi<PERSON>g Jiang", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "MouthIO is the first customizable intraoral user interface that can be equipped with various sensors and output components. It consists of an SLA-printed brace that houses a flexible PCB within a bite-proof enclosure positioned between the molar teeth and inner cheeks. All parts in contact with the oral cavity are made of bio-compatible materials to ensure safety, while the design takes into account both comfort and portability. We demonstrate MouthIO through three application examples ranging from beverage consumption monitoring, health monitoring, to assistive technology.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686758"}, {"primary_key": "711025", "vector": [], "sparse_vector": [], "title": "Quilt: Custom UIs for Linking Unstructured Documents to Structured Datasets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Practitioners working with large document dumps struggle to identify and record connections between documents and structured datasets. For example, how should journalists building a database of police misconduct incidents connect police report documents to their database of officers? The current approach is either to (i) switch between reading entire documents and querying databases, which is a tedious manual process, or (ii) build a custom linking UI to help. These linking UIs are typically built from scratch using vanilla web programming, despite having similar structures. We introduce Quilt, a framework for generating custom UIs for linking between documents and databases. Quilt’s API lets programmers provide domain knowledge—e.g., what counts as evidence that a particular database row is relevant to a document. From this information, Quilt generates the UI automatically. We explore Quilt’s expressivity via four case studies and find it handles a diverse range of documents and databases.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686777"}, {"primary_key": "711026", "vector": [], "sparse_vector": [], "title": "Flip-Pelt: Motor-Driven Peltier Elements for Rapid Thermal Stimulation and Congruent Pressure Feedback in Virtual Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jeongju Park", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This study introduces \"Flip-Pelt,\" a motor-driven peltier device designed to provide rapid thermal stimulation and congruent pressure feedback in virtual reality (VR) environments. Our system incorporates eight motor-driven peltier elements, allowing for the flipping of preheated or cooled elements to the opposite side. In evaluating the Flip-Pelt device, we assess user ability to distinguish between heat/cold sources by their patterns and stiffness, and its impact on enhancing haptic experiences in VR content that involves contact with various thermal sources. Our findings demonstrate that rapid thermal stimulation and congruent pressure feedback provided by Flip-Pelt enhance the recognition accuracy of thermal patterns and the stiffness of virtual objects. These features also improve haptic experiences in VR scenarios through their temporal congruency between tactile and thermal stimuli. Additionally, we discuss the scalability of the Flip-Pelt system to other body parts by proposing design prototypes.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676363"}, {"primary_key": "711027", "vector": [], "sparse_vector": [], "title": "Improving Steering and Verification in AI-Assisted Data Analysis with Interactive Task Decomposition.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Advait Sarkar"], "summary": "LLM-powered tools like ChatGPT Data Analysis, have the potential to help users tackle the challenging task of data analysis programming, which requires expertise in data processing, programming, and statistics. However, our formative study (n=15) uncovered serious challenges in verifying AI-generated results and steering the AI (i.e., guiding the AI system to produce the desired output). We developed two contrasting approaches to address these challenges. The first (Stepwise) decomposes the problem into step-by-step subgoals with pairs of editable assumptions and code until task completion, while the second (Phasewise) decomposes the entire problem into three editable, logical phases: structured input/output assumptions, execution plan, and code. A controlled, within-subjects experiment (n=18) compared these systems against a conversational baseline. Users reported significantly greater control with the Stepwise and Phasewise systems, and found intervention, correction, and verification easier, compared to the baseline. The results suggest design guidelines and trade-offs for AI-assisted data analysis tools.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676345"}, {"primary_key": "711028", "vector": [], "sparse_vector": [], "title": "Transforming Procedural Instructions into In-Situ Augmented Reality Guides with InstructAR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Following manual procedural instructions can often be frustrating due to the mental gap between the instructions and their real-world application. Variations in settings can further compound this confusion for those attempting to follow them. To address this problem, we propose InstructAR, a system under development that bridges textual instructions with the real world through Augmented Reality (AR) guides. With this tool, users can easily follow clear and actionable step-by-step instructions, as related objects are highlighted and AR guides indicate necessary actions. InstructAR also provides feedback upon task completion. This proof-of-concept leverages Natural Language Processing and Computer Vision techniques. Our work aims to reduce cognitive load and errors, making it easier for users to follow manual procedural instructions accurately.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686321"}, {"primary_key": "711029", "vector": [], "sparse_vector": [], "title": "FathomGPT: A natural language interface for interactively exploring ocean science data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON>"], "summary": "We introduce FathomGPT, an open source system for the interactive investigation of ocean science data via a natural language interface. FathomGPT was developed in close collaboration with marine scientists to enable researchers to explore and analyze the FathomNet image database. FathomGPT provides a custom information retrieval pipeline that leverages OpenAI's large language models to enable: the creation of complex queries to retrieve images, taxonomic information, and scientific measurements; mapping common names and morphological features to scientific names; generating interactive charts on demand; and searching by image or specified patterns within an image. In designing FathomGPT, particular emphasis was placed on enhancing the user's experience by facilitating free-form exploration and optimizing response times. We present an architectural overview and implementation details of FathomGPT, along with a series of ablation studies that demonstrate the effectiveness of our approach to name resolution, fine tuning, and prompt modification. We also present usage scenarios of interactive data exploration sessions and document feedback from ocean scientists and machine learning experts.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676462"}, {"primary_key": "711030", "vector": [], "sparse_vector": [], "title": "MagicDraw: Haptic-Assisted One-Line Drawing with Shared Control.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present MagicDraw, a platform designed for force feedback guidance in one-line drawing. MagicDraw allows users to transition seamlessly between fully assisted sketching and freehand drawing through a control-sharing mechanism. The initial drawing concept is generated based on user input prompts. This platform operates similarly to tracing but introduces two major enhancements. First, MagicDraw provides force feedback guidance, aiding users in maintaining accurate line-following. Second, the system enables dynamic control sharing, allowing users to deviate from the predefined path and engage in creative exploration. We also introduce “exploration region,” where users can perform freehand drawing. In these regions, the predefined path advances outside the boundary, pausing for the user’s creative deviations. As the user returns to fully assisted sketching, these regions shrink until the user resumes force feedback-guided tracing. This approach ensures users can explore creative variations while still receiving structured guidance.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686753"}, {"primary_key": "711031", "vector": [], "sparse_vector": [], "title": "IRIS: Wireless ring for vision-based smart home interaction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aditya Bagaria", "Shwetak N. Patel", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Integrating cameras into wireless smart rings has been challenging due to size and power constraints. We introduce IRIS, the first wireless vision-enabled smart ring system for smart home interactions. Equipped with a camera, Bluetooth radio, inertial measurement unit (IMU), and an onboard battery, IRIS meets the small size, weight, and power (SWaP) requirements for ring devices. IRIS is context-aware, adapting its gesture set to the detected device, and can last for 16-24 hours on a single charge. IRIS leverages the scene semantics to achieve instance-level device recognition. In a study involving 23 participants, IRIS consistently outpaced voice commands, with a higher proportion of participants expressing a preference for IRIS over voice commands regarding toggling a device's state, granular control, and social acceptability. Our work pushes the boundary of what is possible with ring form-factor devices, addressing system challenges and opening up novel interaction capabilities.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676327"}, {"primary_key": "711032", "vector": [], "sparse_vector": [], "title": "VibraHand: In-Hand Superpower Enabling Spying, Precognition, and Telekinesis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This work presents a novel integration of multiple sensing and control technologies into a hand-worn device, enabling users to experience superpowers such as remote eavesdropping, telekinesis, and precognition. By leveraging techniques such as surface vibration sensing, ultrasound, and mmWave radar, the device facilitates expressive and intuitive in-hand interactions. Additionally, the use of acoustic levitation for contactless object manipulation extends the scope of wearable interactions. This innovative approach enhances the functionality of wearable devices for the future of seamless and powerful user experiences.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686728"}, {"primary_key": "711033", "vector": [], "sparse_vector": [], "title": "Effects of Computer Mouse Lift-off Distance Settings in Mouse Lifting Action.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This study investigates the effect of Lift-off Distance (LoD) on a computer mouse, which refers to the height at which a mouse sensor stops tracking when lifted off the surface. Although a low LoD is generally preferred to avoid unintentional cursor movement in mouse lifting (=clutching), especially in first-person shooter games, it may reduce tracking stability. We conducted a psychophysical experiment to measure the perceptible differences between LoD levels and quantitatively measured the unintentional cursor movement error and tracking stability at four levels of LoD while users performed mouse lifting. The results showed a trade-off between movement error and tracking stability at varying levels of LoD. Our findings offer valuable information on optimal LoD settings, which could serve as a guide for choosing a proper mouse device for enthusiastic gamers.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676442"}, {"primary_key": "711034", "vector": [], "sparse_vector": [], "title": "Collision Prevention in Diminished Reality through the Use of Peripheral Vision.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Diminished reality (DR) removes virtual and real objects from the user’s view, decluttering and optimizing what the user sees through augmented/mixed reality. However, removing real objects create a potential safety concern because users may unintentionally bump into the diminished object. To address this issue, we apply characteristics of peripheral vision to DR and diminish objects when they are in the peripheral region and show the object when the user focuses on the object. We created 3 different object opacity control functions that use gaze information and evaluated them.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686346"}, {"primary_key": "711035", "vector": [], "sparse_vector": [], "title": "Pro-Tact: Hierarchical Synthesis of Proprioception and Tactile Exploration for Eyes-Free Ray Pointing on Out-of-View VR Menus.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce Pro-Tact, a novel eyes-free pointing technique for interacting with out-of-view (OoV) VR menus. This technique combines rapid rough pointing using proprioception with fine-grain adjustments through tactile exploration, enabling menu interaction without visual attention. Our user study demonstrated that Pro-Tact allows users to select menu items accurately (95% accuracy for 54 items) in an eyes-free manner, with reduced fatigue and sickness compared to eyes-engaged interaction. Additionally, we observed that participants voluntarily interacted with OoV menus eyes-free when Pro-Tact’s tactile feedback was provided in practical VR application usage contexts. This research contributes by introducing the novel interaction technique, Pro-Tact, and quantitatively evaluating its benefits in terms of performance, user experience, and user preference in OoV menu interactions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676324"}, {"primary_key": "711036", "vector": [], "sparse_vector": [], "title": "MyWebstrates: Webstrates as Local-first Software.", "authors": ["Clemens Nylandsted Klokmose", "<PERSON>", "<PERSON>"], "summary": "Webstrates are web substrates, a practical realization of shareable dynamic media under which distributability, shareability, and malleability are fundamental software principles. Webstrates blur the distinction between application and document in a way that enables users to share, repurpose, and refit software across a variety of domains, but its reliance on a central server constrains its use; it is at odds with personal and collective control of data; and limits applications to the web. We extend the fundamental principles to include interoperability and sovereignty over data and propose MyWebstrates, an implementation of Webstrates on top of a new, lower-level substrate for synchronization built around local-first software principles. MyWebstrates registers itself in the user's browser and function as a piece of local software that can selectively synchronise data over sync servers or peer-to-peer connections. We show how MyWebstrates extends Webstrates to enable offline collaborative use, interoperate between Webstrates on non-web technologies such as Unity, and maintain personal and collective sovereignty over data. We demonstrate how this enables new types of applications of Webstrates and discuss limitations of this approach and new challenges that it reveals.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676445"}, {"primary_key": "711037", "vector": [], "sparse_vector": [], "title": "Power-over-Skin: Full-Body Wearables Powered By Intra-Body RF Energy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Powerful computing devices are now small enough to be easily worn on the body. However, batteries pose a major design and user experience obstacle, adding weight and volume, and generally requiring periodic device removal and recharging. In response, we developed Power-over-Skin, an approach using the human body itself to deliver power to many distributed, battery-free, worn devices. We demonstrate power delivery from on-body distances as far as from head-to-toe, with sufficient energy to power microcontrollers capable of sensing and wireless communication. We share results from a study campaign that informed our implementation, as well as experiments that validate our final system. We conclude with several demonstration devices, ranging from input controllers to longitudinal bio-sensors, which highlight the efficacy and potential of our approach.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676394"}, {"primary_key": "711038", "vector": [], "sparse_vector": [], "title": "Beyond the Chat: Executable and Verifiable Text-Editing with LLMs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Conversational interfaces powered by Large Language Models (LLMs) have recently become a popular way to obtain feedback during document editing. However, standard chat-based conversational interfaces cannot explicitly surface the editing changes that they suggest. To give the author more control when editing with an LLM, we present InkSync, an editing interface that suggests executable edits directly within the document being edited. Because LLMs are known to introduce factual errors, Inksync also supports a 3-stage approach to mitigate this risk: Warn authors when a suggested edit introduces new information, help authors Verify the new information’s accuracy through external search, and allow a third party to Audit with a-posteriori verification via a trace of all auto-generated content. Two usability studies confirm the effectiveness of InkSync’s components when compared to standard LLM-based chat interfaces, leading to more accurate and more efficient editing, and improved user experience.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676419"}, {"primary_key": "711039", "vector": [], "sparse_vector": [], "title": "Granting Non-AI Experts Creative Control Over AI Systems.", "authors": ["<PERSON>"], "summary": "Many harmful behaviors and problematic deployments of AI stem from the fact that AI experts are not experts in the vast array of settings where AI is applied. Non-AI experts from these domains hold promising potential to contribute their expertise and directly design the AI systems that impact them, but they face substantial technical and effort barriers. Could we redesign AI development tools to match the language of non-technical end users? My research develops novel systems allowing non-AI experts to define AI behavior in terms of interpretable, self-defined concepts. Monolithic, black-box models do not yield such control, so we introduce techniques for users to create many narrow, personalized models that they can better understand and steer. We demonstrate the success of this approach across the AI lifecycle: from designing AI objectives to evaluating AI behavior to authoring end-to-end AI systems. When non-AI experts design AI from start to finish, they notice gaps and build solutions that AI experts could not—such as creating new feed ranking models to mitigate partisan animosity, surfacing underreported issues with content moderation models, and activating unique pockets of LLM behavior to amplify their personal writing style.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686714"}, {"primary_key": "711040", "vector": [], "sparse_vector": [], "title": "ScrapMap: Interactive Color Layout for Scrap Quilting.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Scrap quilting is a popular sewing process that involves combining leftover pieces of fabric into traditional patchwork designs. Imagining the possibilities for these leftovers and arranging the fabrics in such a way that achieves visual goals, such as high contrast, can be challenging given the large number of potential fabric assignments within the quilt’s design. We formulate the task of designing a scrap quilt as a graph coloring problem with domain-specific coloring and material constraints. Our interactive tool called ScrapMap helps quilters explore these potential designs given their available materials by leveraging the hierarchy of scrap quilt construction (e.g., quilt blocks and motifs) and providing user-directed automatic block coloring suggestions. Our user evaluation indicates that quilters find ScrapMap useful for helping them consider new ways to use their scraps and create visually striking quilts.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676404"}, {"primary_key": "711041", "vector": [], "sparse_vector": [], "title": "SERENUS: Alleviating Low-Battery Anxiety Through Real-time, Accurate, and User-Friendly Energy Consumption Prediction of Mobile Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Insik Shin"], "summary": "Low-battery anxiety has emerged as a result of growing dependence on mobile devices, where the anxiety arises when the battery level runs low. While battery life can be extended through power-efficient hardware and software optimization techniques, low-battery anxiety will still remain a phenomenon as long as mobile devices rely on batteries. In this paper, we investigate how an accurate real-time energy consumption prediction at the application-level can improve the user experience in low-battery situations. We present Serenus, a mobile system framework specifically tailored to predict the energy consumption of each mobile application and present the prediction in a user-friendly manner. We conducted user studies using Serenus to verify that highly accurate energy consumption predictions can effectively alleviate low-battery anxiety by assisting users in planning their application usage based on the remaining battery life. We summarize requirements to mitigate users’ anxiety, guiding the design of future mobile system frameworks.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676437"}, {"primary_key": "711042", "vector": [], "sparse_vector": [], "title": "Fluxable: A Tool for Making 3D Printable Sensors and Actuators.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present Fluxable, a tool for making custom sensors and actuators 3D printable with customer-grade Stereolithography (SLA) 3D printers. With this tool, the user converts an arbitrary 3D model into a deformable body with integrated helix-and-lattice structures, which comprise a hollow helical channel in the center, lattice paddings, and a wireframe structure on the surface. The tool allows for the parameterization of the helix for sensing performance and customization of the lattice for actuation. By inserting a conductive shape-memory alloy (SMA) into a printed object through the helical channel, the converted shape becomes a sensor to detect various shape-changing behaviors using inductive sensing or an actuator to trigger movements through temperature control. We demonstrated our tool with a series of example sensors and actuators, including an interactive timer, a DJ station, and a caterpillar robot.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686342"}, {"primary_key": "711043", "vector": [], "sparse_vector": [], "title": "Can Capacitive Touch Images Enhance Mobile Keyboard Decoding?", "authors": ["<PERSON><PERSON><PERSON>", "Shanqing Cai", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Capacitive touch sensors capture the two-dimensional spatial profile (referred to as a touch heatmap) of a finger's contact with a mobile touchscreen. However, the research and design of touchscreen mobile keyboards -- one of the most speed and accuracy demanding touch interfaces -- has focused on the location of the touch centroid derived from the touch image heatmap as the input, discarding the rest of the raw spatial signals. In this paper, we investigate whether touch heatmaps can be leveraged to further improve the tap decoding accuracy for mobile touchscreen keyboards. Specifically, we developed and evaluated machine-learning models that interpret user taps by using the centroids and/or the heatmaps as their input and studied the contribution of the heatmaps to model performance. The results show that adding the heatmap into the input feature set led to 21.4% relative reduction of character error rates on average, compared to using the centroid alone. Furthermore, we conducted a live user study with the centroid-based and heatmap-based decoders built into Pixel 6 Pro devices and observed lower error rate, faster typing speed, and higher self-reported satisfaction score based on the heatmap-based decoder than the centroid-based decoder. These findings underline the promise of utilizing touch heatmaps for improving typing experience in mobile keyboards.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676420"}, {"primary_key": "711044", "vector": [], "sparse_vector": [], "title": "NeuroSight: Combining Eye-Tracking and Brain-Computer Interfaces for Context-Aware Hand-Free Camera Interaction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Technology has blurred the boundaries of our work and private lives. Using touch-free technology can lessen the divide between technology and reality and bring us closer to the immersion we once had before. This work explores the combination of eye-tracking glasses and a brain-computer interface to enable hand-free interaction with the camera without holding or touching it. Different camera modes are difficult to implement without the use of eye-tracking. For example, visual search relies on an object, selecting a region in the scene by touching the touchscreen on your phone. Eye-tracking is used instead, and the fixation point is used to select the intended region. In addition, fixations can provide context for the mode the user wants to execute. For instance, fixations on foreign text could indicate translation mode. Ultimately, multiple touchless gestures create more fluent transitions between our life experiences and technology.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686312"}, {"primary_key": "711045", "vector": [], "sparse_vector": [], "title": "Mapping Gaze and Head Movement via Salience Modulation and Hanger Reflex.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Vision is crucial for daily input and plays a significant role in remote collaboration. Sharing gaze has been explored to enhance communication, but sharing gaze alone is not natural due to limited central vision (30 degrees). We propose a novel approach to map gaze and head movements simultaneously, enabling replicating natural observation across individuals. In this paper, we evaluate the effectiveness of replication head movements and gaze on another person by a pilot study. In the future, we will also explore the possibility of improving novices’ efficiency in imitating experts by replicating the gaze trajectories of experts.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686349"}, {"primary_key": "711046", "vector": [], "sparse_vector": [], "title": "EmoPus: Providing Emotional and Tactile Comfort with a AI Desk Companion Octopus.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the desktop scene, people often work and study for a long time, which can lead to mental stress and depression, therefore bringing the need for companion robots. Existing products offer voice dialogue and motion changes but lack direct user interactions. Consequently, we propose EmoPus, an AI companion robot resembling an octopus, designed for multi-dimensional interactions. Users can talk to EmoPus by voice and command it to complete simple tasks like grabbing objects. It also actively interacts based on the user’s mental state, soothing the user’s arm with its tentacles and offering a comforting tactile perception. User interaction would also change the shape of the EmoPus itself and provide real-time responses. In general, EmoPus interacts with users based on their psychological state, bringing fun, mental relief, and Emotional Companion while reducing the psychological burden after long work or study hours.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686730"}, {"primary_key": "711047", "vector": [], "sparse_vector": [], "title": "SituationAdapt: Contextual UI Optimization in Mixed Reality with Situation Awareness via LLM Reasoning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mixed Reality is increasingly used in mobile settings beyond controlled home and office spaces. This mobility introduces the need for user interface layouts that adapt to varying contexts. However, existing adaptive systems are designed only for static environments. In this paper, we introduce SituationAdapt, a system that adjusts Mixed Reality UIs to real-world surroundings by considering environmental and social cues in shared settings. Our system consists of perception, reasoning, and optimization modules for UI adaptation. Our perception module identifies objects and individuals around the user, while our reasoning module leverages a Vision-and-Language Model to assess the placement of interactive UI elements. This ensures that adapted layouts do not obstruct relevant environmental cues or interfere with social norms. Our optimization module then generates Mixed Reality interfaces that account for these considerations as well as temporal constraints. For evaluation, we first validate our reasoning module’s capability of assessing UI contexts in comparison to human expert users. In an online user study, we then establish SituationAdapt’s capability of producing context-aware layouts for Mixed Reality, where it outperformed previous adaptive layout methods. We conclude with a series of applications and scenarios to demonstrate SituationAdapt’s versatility.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676470"}, {"primary_key": "711048", "vector": [], "sparse_vector": [], "title": "E-Joint: Fabrication of Large-Scale Interactive Objects Assembled by 3D Printed Conductive Parts with Copper Plated Joints.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kecheng Jin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The advent of conductive thermoplastic filaments and multi-material 3D printing has made it feasible to create interactive 3D printed objects. Yet, challenges arise due to the volume constraints of desktop 3D printers and the high resistive characteristics of current conductive materials, making the fabrication of large-scale or highly conductive interactive objects can be daunting. We propose E-Joint, a novel fabrication pipeline for 3D printed objects utilizing mortise and tenon joint structures combined with a copper plating process. The segmented pieces and joint structures are customized in software along with integrated circuits. Then electroplate them for enhanced conductivity. We designed four distinct electrified joint structures in the experiment and evaluated the practical feasibility and effectiveness of fabricating pipes. By constructing three applications with those structures, we verified the usability of E-Joint in making large-scale interactive objects and showed the path to a more integrated future for manufacturing.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676398"}, {"primary_key": "711049", "vector": [], "sparse_vector": [], "title": "AniCraft: Crafting Everyday Objects as Physical Proxies for Prototyping 3D Character Animation in Mixed Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Qian<PERSON> Liu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce AniCraft, a mixed reality system for prototyping 3D character animation using physical proxies crafted from everyday objects. Unlike existing methods that require specialized equipment to support the use of physical proxies, AniCraft only requires affordable markers, webcams, and daily accessible objects and materials. AniCraft allows creators to prototype character animations through three key stages: selection of virtual characters, fabrication of physical proxies, and manipulation of these proxies to animate the characters. This authoring workflow is underpinned by diverse physical proxies, manipulation types, and mapping strategies, which ease the process of posing virtual characters and mapping user interactions with physical proxies to animated movements of virtual characters. We provide a range of cases and potential applications to demonstrate how diverse physical proxies can inspire user creativity. User experiments show that our system can outperform traditional animation methods for rapid prototyping. Furthermore, we provide insights into the benefits and usage patterns of different materials, which lead to design implications for future research.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676325"}, {"primary_key": "711050", "vector": [], "sparse_vector": [], "title": "OmniQuery: Enabling Question Answering on Personal Memory by Augmenting Multimodal Album Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Jerry) <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present OmniQuery, an interactive system that augments users’ personal photo albums and enables free-form question answering on users’ past memories. OmniQuery processes multimodal media data in personal albums, aggregates them into related episodic memory databases in different levels, and infers semantic knowledge including personal facts like social relationships, preferences, and experiences. OmniQuery then allows users to interact with their database using natural language, giving media that directly matches the query or an exact answer supported by related media as a result.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686313"}, {"primary_key": "711051", "vector": [], "sparse_vector": [], "title": "DualPad: Exploring Non-Dominant Hand Interaction on Dual-Screen Laptop Touchpads.", "authors": ["Changsung Lim", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dual-touchscreen laptops present opportunities for providing an expansive touchpad on the lower touchscreen. This expanded touchpad offers space for the engagement of both the dominant and non-dominant hands. In this context, it is necessary to redefine the role of the non-dominant hand. Therefore, we propose DualPad for dual-touchscreen laptops, which provides a long touchpad on the lower touchscreen. The non-dominant hand can utilize this DualPad to execute Touch Shortcut / Modifier, analogous to keyboard shortcuts and modifier keys on single-screen laptops. Moreover, we propose Dual Cursor as an example of bimanual interaction. In the demonstration, participants are expected to utilize the custom presentation program to create the given slide using two distinct methods. First, they employ the default layout of the virtual keyboard and virtual touchpad provided on the dual-touchscreen laptop. Then, they utilize DualPad for comparison.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686751"}, {"primary_key": "711053", "vector": [], "sparse_vector": [], "title": "AITentive: A Toolkit to Develop RL-based Attention Management Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In today’s fast-paced world, multitasking is common and affects productivity, decision-making, and cognition. Understanding its complexities is crucial for improving well-being, efficiency, and task management. Attention management systems optimize notification and interruption timings. This work introduces AITentive, an open-source Unity3D toolkit for multitasking research and developing attention management systems with reinforcement learning. The toolkit offers customizable tasks, built-in measurements, and a uniform interface for adding tasks, using Unity ML agents to develop and train attention management systems based on user models.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686314"}, {"primary_key": "711054", "vector": [], "sparse_vector": [], "title": "Palimpsest: a spatial user interface toolkit for cohering tracked physical entities and interactive 3D content.", "authors": ["<PERSON>"], "summary": "Designing effective 3D user interfaces for immersive technologies remains tedious and manual, particularly for the successful integration of complex interactive 3D content. Palimpsest automates parts of this workflow by associating 3D user interface elements with interactive 3D content, laid out without code by the designer. We demonstrate a custom-developed UI framework, built on top of recently introduced workflows for Apple Vision Pro, streamlining the coherent spatial arrangement of 3D model libraries, animations, and textures in relation to tracked 3D objects or 2D images in the user’s environment. In this demonstration, we showcase several sample applications built with Palimpsest in architectural visualization, hybrid puzzle games, and spatial reading.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686780"}, {"primary_key": "711055", "vector": [], "sparse_vector": [], "title": "HoloClass: Enhancing VR Classroom with Live Volumetric Video Streaming.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Virtual Reality (VR) enhances education by creating immersive and engaging learning environments. Volumetric video (VV) further improves VR classrooms by offering realistic, 3D representations of instructors and materials without high development costs. This study introduces HoloClass, a live VV streaming system for VR classrooms. We conducted interviews with 18 students to identify key design needs, resulting in features of HoloClass that support real-time awareness, classroom scalability, and note-taking. Our contributions include empirical insights into designing educational tools with live VV in VR classrooms and the implementation of features that enhance interaction and learning in a virtual setting.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686330"}, {"primary_key": "711056", "vector": [], "sparse_vector": [], "title": "MindCube: an Interactive Device for Gauging Emotions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper introduces the MindCube, an interactive device designed for studying emotions. Resembling a fidget cube toy commonly used for stress and anxiety relief, the MindCube features a compact cubic shape (3.3 cm × 3.3 cm × 3.3 cm), making it small, easy to hold, and ideal for playful interaction. Like a fidget cube toy, each side of the MindCube is equipped with various interactive inputs, including tactile buttons, a small rolling disk, and a joystick. Additionally, the device is fitted with a 9-DoF IMU (Inertial Measurement Unit) to measure real-time orientation when held by the user. Furthermore, the MindCube includes a linear vibration motor to provide haptic feedback to enhance the interactive experience.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686771"}, {"primary_key": "711057", "vector": [], "sparse_vector": [], "title": "KeyFlow: Acoustic Motion Sensing for Cursor Control on Any Keyboard.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Shan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite typing being a critical operation in the digital age, users still need to frequently switch between the mouse and keyboard while typing. We introduce KeyFlow, a tool that integrates mouse functionality into the keyboard through machine learning, allowing users to glide their fingers across the keyboard surface to move the cursor. The whole process does not press the keys down to differentiate from normal typing and avoid false touches. KeyFlow uses any computer-built-in microphones to capture the acoustic features of these gliding gestures, requiring no specialized equipment and can be set up and tested independently within 5 minutes. Our user research indicates that, compared to traditional keyboard and mouse methods, this system reduces hand movement distance by 78.3%, making the typing experience more focused.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686348"}, {"primary_key": "711058", "vector": [], "sparse_vector": [], "title": "Touchscreen-based Hand Tracking for Remote Whiteboard Interaction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In whiteboard-based remote communication, the seamless integration of drawn content and hand-screen interactions is essential for an immersive user experience. Previous methods either require bulky device setups for capturing hand gestures or fail to accurately track the hand poses from capacitive images. In this paper, we present a real-time method for precise tracking 3D poses of both hands from capacitive video frames. To this end, we develop a deep neural network to identify hands and infer hand joint positions from capacitive frames, and then recover 3D hand poses from the hand-joint positions via a constrained inverse kinematic solver. Additionally, we design a device setup for capturing high-quality hand-screen interaction data and obtained a more accurate synchronized capacitive video and hand pose dataset. Our method improves the accuracy and stability of 3D hand tracking for capacitive frames while maintaining a compact device setup for remote communication. We validate our scheme design and its superior performance on 3D hand pose tracking and demonstrate the effectiveness of our method in whiteboard-based remote communication.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676412"}, {"primary_key": "711059", "vector": [], "sparse_vector": [], "title": "ComPeer: A Generative Conversational Agent for Proactive Peer Support.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Hongzheng Zhao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Conversational Agents (CAs) acting as peer supporters have been widely studied and demonstrated beneficial for people’s mental health. However, previous peer support CAs either are user-initiated or follow predefined rules to initiate the conversations, which may discourage users to engage and build relationships with the CAs for long-term benefits. In this paper, we develop ComPeer , a generative CA that can proactively offer adaptive peer support to users. ComPeer leverages large language models to detect and reflect significant events in the dialogue, enabling it to strategically plan the timing and content of proactive care. In addition, ComPeer incorporates peer support strategies, conversation history, and its persona into the generative messages. Our one-week between-subjects study (N=24) demonstrates ComPeer ’s strength in providing peer support over time and boosting users’ engagement compared to a baseline user-initiated CA. We report users’ interaction patterns with ComPeer and discuss implications for designing proactive generative agents to promote people’s well-being.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676430"}, {"primary_key": "711060", "vector": [], "sparse_vector": [], "title": "FisheyeVR: Extending the Field of View by Dynamic Zooming in Virtual Reality.", "authors": ["De-Yuan Lu", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose FisheyeVR, a zooming interface in VR dynamically providing users a larger software FOV by zooming out to a shorter virtual focal length, trading in an acceptable visual distortion for more context. We conduct studies to (1) understand the visual distortion of zoom-out FOVs, (2) test 4 triggering methods with common VR scenarios and (3) evaluate the integrated FisheyeVR system. Our findings demonstrate that FisheyeVR not only significantly reduces users’ physical effort and oculomotor simulator sickness but also maintains performance levels, accompanied by positive feedback.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686316"}, {"primary_key": "711061", "vector": [], "sparse_vector": [], "title": "HandPad: Make Your Hand an On-the-go Writing Pad via Human Capacitance.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Pan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The convenient text input system is a pain point for devices such as AR glasses, and it is difficult for existing solutions to balance portability and efficiency. This paper introduces HandPad, the system that turns the hand into an on-the-go touchscreen, which realizes interaction on the hand via human capacitance. HandPad achieves keystroke and handwriting inputs for letters, numbers, and Chinese characters, reducing the dependency on capacitive or pressure sensor arrays. Specifically, the system verifies the feasibility of touch point localization on the hand using the human capacitance model and proposes a handwriting recognition system based on Bi-LSTM and ResNet. The transfer learning-based system only needs a small amount of training data to build a handwriting recognition model for the target user. Experiments in real environments verify the feasibility of HandPad for keystroke (accuracy of 100%) and handwriting recognition for letters (accuracy of 99.1%), numbers (accuracy of 97.6%) and Chinese characters (accuracy of 97.9%).", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676328"}, {"primary_key": "711062", "vector": [], "sparse_vector": [], "title": "Large Language Model Agents Enabled Generative Design of Fluidic Computation Interfaces.", "authors": ["<PERSON><PERSON><PERSON>", "Ji<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shiqing Lyu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The creation of interactive devices is a major area of interest. However, traditional design tools in this field often require a significant learning curve and may not effectively support creative ideation. This study explores the use of fluidic computation interfaces as a case study to examine the potential of enhancing design tools for physical devices with Large Language Model (LLM) agents. With LLM agents, the Generative Design Tool (GDT) can understand the capabilities and limitations of new devices, suggest diverse, insightful, and practical application scenarios, and recommend designs that are technically and contextually appropriate. Additionally, it generates the necessary design parameters for the traditional components of the design tool to visualize results and create files for fabrication.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686351"}, {"primary_key": "711063", "vector": [], "sparse_vector": [], "title": "LOST STAR: An Interactive Stereoscopic Picture Book Installation for Children&apos;s Bedtime Rituals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sleep disorders in early childhood hinder development and disrupt daily functions, but effective bedtime rituals can help. However, the integration of tangible and virtual interactions in sleep-related design projects remains unexplored. This paper investigates how pop-up books and multimedia can create a playful and accessible way for children to engage in bedtime activities. We present the design of Lost Star, a sonic and tactile Tangible User Interface (TUI) designed to enhance the quality of bedtime rituals for children with sleep problems. Additionally, we explore Joint Media Engagement (JME) to foster meaningful interactions between parents and children, enhancing bedtime routines and improving sleep outcomes.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686322"}, {"primary_key": "711064", "vector": [], "sparse_vector": [], "title": "Degrade to Function: Towards Eco-friendly Morphing Devices that Function Through Programmed Sequential Degradation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While it seems counterintuitive to think of degradation within an operating device as beneficial, one may argue that when rationally designed, the controlled breakdown of materials can be harnessed for specific functions. To apply this principle to the design of morphing devices, we introduce the concept of Degrade to Function (DtF). This concept aims to create eco-friendly and self-contained morphing devices that operate through a sequence of environmentally-triggered degradations. We explore its design considerations and implementation techniques by identifying environmental conditions and degradation types that can be exploited, evaluating potential materials capable of controlled degradation, suggesting designs for structures that can leverage degradation to achieve various transformations and functions, and developing sequential control approaches that integrate degradation triggers. To demonstrate the viability and versatility of this design strategy, we showcase several application examples across a range of environmental conditions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676464"}, {"primary_key": "711065", "vector": [], "sparse_vector": [], "title": "DeMorph: Morphing Devices Functioning via Sequential Degradation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While it may initially seem counterintuitive to view degradation within an operating system as advantageous, one could argue that, when intentionally designed, the controlled breakdown of materials—whether physical, chemical, or biological—can be leveraged for specific functions. To apply this principle to the development of functional morphing devices, we have introduced the concept of \"Degrade to Function\" (DtF) [16]. This concept is aimed at creating eco-friendly and self-contained morphing devices that operate through a series of environmentally-triggered degradations. In this demonstration, we elucidate the DtF design strategy and present five application examples across a range of ecosystems.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686737"}, {"primary_key": "711066", "vector": [], "sparse_vector": [], "title": "Exploring the Effects of Sensory Conflicts on Cognitive Fatigue in VR Remappings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Virtual reality (VR) is found to present significant cognitive challenges due to its immersive nature and frequent sensory conflicts. This study systematically investigates the impact of sensory conflicts induced by VR remapping techniques on cognitive fatigue, and unveils their correlation. We utilized three remapping methods (haptic repositioning, head-turning redirection, and giant resizing) to create different types of sensory conflicts, and measured perceptual thresholds to induce various intensities of the conflicts. Through experiments involving cognitive tasks along with subjective and physiological measures, we found that all three remapping methods influenced the onset and severity of cognitive fatigue, with visual-vestibular conflict having the greatest impact. Interestingly, visual-experiential/memory conflict showed a mitigating effect on cognitive fatigue, emphasizing the role of novel sensory experiences. This study contributes to a deeper understanding of cognitive fatigue under sensory conflicts and provides insights for designing VR experiences that align better with human perceptual and cognitive capabilities.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676439"}, {"primary_key": "711067", "vector": [], "sparse_vector": [], "title": "TactileNet: Bringing Touch Closer in the Digital World.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zhaodong Jiang", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Haptic technologies have improved human-computer interaction (HCI) by enabling immersive tactile experiences. This proposal introduces a 2D lateral touch actuator to simulate rubbing sensations on human skin and transmit them over the internet. Using a controllable tilted roller mechanism, the system recreates various touch sensations. Advanced hand gesture recognition algorithms identify and transmit the input user’s actions to the output user. Utilizing AI-enabled computing chips from the Gen-M kit, the project aims to develop a prototype that accurately simulates natural touch interactions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686731"}, {"primary_key": "711068", "vector": [], "sparse_vector": [], "title": "Hands-on, Hands-off: Gaze-Assisted Bimanual 3D Interaction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Extended Reality (XR) systems with hand-tracking support direct manipulation of objects with both hands. A common interaction in this context is for the non-dominant hand (NDH) to orient an object for input by the dominant hand (DH). We explore bimanual interaction with gaze through three new modes of interaction where the input of the NDH, DH, or both hands is indirect based on Gaze+Pinch. These modes enable a new dynamic interplay between our hands, allowing flexible alternation between and pairing of complementary operations. Through applications, we demonstrate several use cases in the context of 3D modelling, where users exploit occlusion-free, low-effort, and fluid two-handed manipulation. To gain a deeper understanding of each mode, we present a user study on an asymmetric rotate-translate task. Most participants preferred indirect input with both hands for lower physical effort, without a penalty on user performance. Otherwise, they preferred modes where the NDH oriented the object directly, supporting preshaping of the hand, which is more challenging with indirect gestures. The insights gained are of relevance for the design of XR interfaces that aim to leverage eye and hand input in tandem.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676331"}, {"primary_key": "711069", "vector": [], "sparse_vector": [], "title": "avaTTAR: Table Tennis Stroke Training with Embodied and Detached Visualization in Augmented Reality.", "authors": ["Dizhi Ma", "Xiyun Hu", "Jingyu Shi", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Table tennis stroke training is a critical aspect of player development. We designed a new augmented reality (AR) system, avaTTAR, for table tennis stroke training. The system provides both “on-body” (first-person view) and “detached” (third-person view) visual cues, enabling users to visualize target strokes and correct their attempts effectively with this dual perspectives setup. By employing a combination of pose estimation algorithms and IMU sensors, avaTTAR captures and reconstructs the 3D body pose and paddle orientation of users during practice, allowing real-time comparison with expert strokes. Through a user study, we affirm avaTTAR ’s capacity to amplify player experience and training results.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676400"}, {"primary_key": "711070", "vector": [], "sparse_vector": [], "title": "Stretchy Embroidered Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a preliminary system which generates machine embroidery patterns for electronic circuits on stretchy fabrics. Our system incorporates parametric stretch-optimized embroidery paths, and we offer tips for e-textile embroidery on stretch fabrics.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686343"}, {"primary_key": "711071", "vector": [], "sparse_vector": [], "title": "Parasitic or Symbiotic? Redefining our Relationship with Intelligent Systems.", "authors": ["<PERSON>"], "summary": "My “UIST vision” is to fundamentally change our approach to designing intelligent interactive systems. Rather than creating parasitic systems, our goal should be to create “human-computer partnerships” that establish symbiotic relationships between artificial intelligence (AI) and human users. This requires assessing the impact of users interacting with intelligent systems over the short, medium and long term. We also need to ensure that users control their level of agency, ranging from delegation to retaining full control. Finally, we need to understand how users and AI systems affect each other’s behavior over time. This implies we need to explicitly support “reciprocal co-adaptation” where users both learn from and appropriate (adapt and adapt to) intelligent systems, and those systems in turn both learn from and affect users over time.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3695752"}, {"primary_key": "711072", "vector": [], "sparse_vector": [], "title": "Demo of EITPose: Wearable and Practical Electrical Impedance Tomography for Continuous Hand Pose Estimation.", "authors": ["Hongyu Mao", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Real-time hand pose estimation has a wide range of applications spanning gaming, robotics, and human-computer interaction. In this paper, we introduce EITPose, a wrist-worn, continuous 3D hand pose estimation approach that uses eight electrodes positioned around the forearm to model its interior impedance distribution during pose articulation. Unlike wrist-worn systems relying on cameras, EITPose has a slim profile (12 mm thick sensing strap) and is power-efficient (consuming only 0.3 W of power), making it an excellent candidate for integration into consumer electronic devices. In a user study involving 22 participants, EITPose achieves with a within-session mean per joint positional error of 11.06 mm. Its camera-free design prioritizes user privacy, yet it maintains cross-session and cross-user accuracy levels comparable to camera-based wrist-worn systems, thus making EITPose a promising technology for practical hand pose estimation.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686770"}, {"primary_key": "711073", "vector": [], "sparse_vector": [], "title": "Computational Design and Fabrication of 3D Printed Zippers Connecting 3D Textile Structures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Zippers have long been used for connecting and disconnecting two textiles repeatedly and easily. However, many industrial zippers cannot retain their shape after assembly, limiting the potential use of connecting soft textiles to form 3D shapes with large and various curvatures. Thus, we present a method to design and fabricate interlocking 3D printable zippers that can encode post-assembly shapes. The user first gives a target 3D shape divided into developable patches (i.e., curved surfaces unrollable like paper). Then, our design software built on Rhino/Grasshopper computes an interlocking zipper on the boundary curve of the patches. The user 3D prints the zipper in a flat state and welds it to the edge of the textiles by thermal bonding, which can zip into a target 3D shape. In this demo, we report our method and exhibit design examples of 3D printed zippers.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686743"}, {"primary_key": "711074", "vector": [], "sparse_vector": [], "title": "What is Affective Touch Made Of? A Soft Capacitive Sensor Array Reveals the Interplay between Shear, Normal Stress and Individuality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Rúbia Reis Guerra", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Yiyuan Sun", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Humans physically express emotion by modulating parameters that register on mammalian skin mechanoreceptors, but are unavailable in current touch-sensing technology. Greater sensory richness combined with data on affect-expression composition is a prerequisite to estimating affect from touch, with applications including physical human-robot interaction. To examine shear alongside more easily captured normal stresses, we tailored recent capacitive technology to attain performance suitable for affective touch, creating a flexible, reconfigurable and soft 36-taxel array that detects multitouch normal and 2-dimensional shear at ranges of 1.5kPa-43kPa and ± 0.3-3.8kPa respectively, wirelessly at 43Hz (1548 taxels/s). In a deep-learning classification of 9 gestures (N=16), inclusion of shear data improved accuracy to 88%, compared to 80% with normal stress data alone, confirming shear stress’s expressive centrality. Using this rich data, we analyse the interplay of sensed-touch features, gesture attributes and individual differences, propose affective-touch sensing requirements, and share technical considerations for performance and practicality.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676346"}, {"primary_key": "711075", "vector": [], "sparse_vector": [], "title": "Electrical Connected Orchestra: A New Baton System that can Interactively Control the Body Movements of Performers.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This research leads to the creation of an innovative music performance system that connects conductors and performers. We developed an automatic performance system, that can interactively control the body movements of performers. This system converts music arranged by melody morphing into electrical muscle stimulations (EMS) and controls the body movements of multiple people with devices attached to their hands and feet to realize the performance. In addition, by controlling the behavior of the EMS using a baton interface with a built-in acceleration sensor, the conductor can interactively change the melody, tempo, and velocity of the performance.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686344"}, {"primary_key": "711076", "vector": [], "sparse_vector": [], "title": "EgoTouch: On-Body Touch Input Using AR/VR Headset Cameras.", "authors": ["Vimal Mollyn", "<PERSON>"], "summary": "In augmented and virtual reality (AR/VR) experiences, a user’s arms and hands can provide a convenient and tactile surface for touch input. Prior work has shown on-body input to have significant speed, accuracy, and ergonomic benefits over in-air interfaces, which are common today. In this work, we demonstrate high accuracy, bare hands (i.e., no special instrumentation of the user) skin input using just an RGB camera, like those already integrated into all modern XR headsets. Our results show this approach can be accurate, and robust across diverse lighting conditions, skin tones, and body motion (e.g., input while walking). Finally, our pipeline also provides rich input metadata including touch force, finger identification, angle of attack, and rotation. We believe these are the requisite technical ingredients to more fully unlock on-skin interfaces that have been well motivated in the HCI literature but have lacked robust and practical methods.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676455"}, {"primary_key": "711077", "vector": [], "sparse_vector": [], "title": "Manipulate to Obfuscate: A Privacy-Focused Intelligent Image Manipulation Tool for End-Users.", "authors": ["Kyzyl Monteiro", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Image-related privacy preservation techniques often demand significant technical expertise, creating a barrier for end-users. We present a privacy-focused intelligent image manipulation tool that leverages recent advancements in generative AI to lower this barrier. Our functional prototype allows users to express their privacy concerns, identify potential privacy risks in images, and recommends relevant AI-powered obfuscation techniques to mitigate these risks and concerns. We demonstrate the tool’s versatility across multiple different domains, showcasing its potential to empower users in managing their privacy across various contexts. This demonstration presents the concept, user workflow, and implementation details of our prototype, highlighting its potential to bridge the gap between privacy research and practical, user-facing tools for privacy-preserving image sharing.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686778"}, {"primary_key": "711078", "vector": [], "sparse_vector": [], "title": "Don&apos;t Mesh Around: Streamlining Manual-Digital Fabrication Workflows with Domain-Specific 3D Scanning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Devon Frost", "<PERSON>"], "summary": "Software-first digital fabrication workflows are often at odds with material-driven approaches to design. Material-driven design is especially critical in manual ceramics, where the craftsperson shapes the form through hands-on engagement. We present the Craft-Aligned Scanner (CAS), a 3D scanning and clay-3D printing system that enables practitioners to design for digital fabrication through traditional pottery techniques. The CAS augments a pottery wheel that has 3D printing capabilities with a precision distance sensor on a vertically oriented linear axis. By increasing the height of the sensor as the wheel turns, we directly synthesize a 3D spiralized toolpath from the geometry of the object on the wheel, enabling the craftsperson to immediately transition from manual fabrication to 3D printing without leaving the tool. We develop new digital fabrication workflows with CAS to augment scanned forms with functional features and add both procedurally and real-time-generated surface textures. CAS demonstrates how 3D printers can support material-first digital fabrication design without foregoing the expressive possibilities of software-based design.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676385"}, {"primary_key": "711079", "vector": [], "sparse_vector": [], "title": "Development and Evaluation of Collision Avoidance User Interface for Assistive Vision Impaired Navigation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, an initial novel user interface prototype is developed and evaluated to assist vision impaired people to avoid collision with moving people while navigating in indoor environments. The user interface performs both pose classification and distance estimation based on RGB images and sends the results to the decision tree classifier model which classifies whether it is safe for vision impaired person to navigate, or should the vision impaired person be cautious or stop to avoid collision with the moving person. Experimentation showed that the user interface with the combined performance of pose classification, distance estimation and decision tree model showed an accuracy of 93.55% on a testing video dataset.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686354"}, {"primary_key": "711080", "vector": [], "sparse_vector": [], "title": "Catch that butterfly: A Multimodal Approach for Detecting and Simulating Gut Feelings.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sur<PERSON>"], "summary": "Gut feelings are an omnipresent sensation underlying many emotional experiences, ranging from positive to negative emotions and even sixth sense intuition. Predicting and stimulating these feelings can enhance and enrich users’ emotional experiences. Despite the tremendous potential of gut feelings for HCI design, gut-related signals have been largely ignored in HCI research. In this paper, we introduce an ongoing prototype of an artifact that utilizes both bowel sounds and electrogastrography (EGG) signals to predict and simulate gut feelings. Our prototype consists of an EGG sensing module, a bowel sound sensing module, a machine learning model to process and predict gut-churning moments, and a feedback mechanism to simulate gut feelings in the abdominal area. We hope this work opens up a new design space for physiological signal-based interaction and offers novel opportunities for enhancing user experiences. For future work, we plan to build a robust dataset for gut feeling prediction and evaluate our artifact’s effectiveness in inducing gut feelings in real users.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686340"}, {"primary_key": "711081", "vector": [], "sparse_vector": [], "title": "Undercover Assistance: Designing a Disguised App to Navigate Sexual Harassment.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Sexual harassment remains a critical safety concern for many women. Despite extensive discussions within the HCI community on technological solutions to address harassment, prior research has primarily focused on cyber harassment, with relatively fewer studies addressing design interventions to assist women during incidents of sexual harassment in physical spaces. To this end, we conducted a survey with 106 women who have experienced sexual harassment, aiming to identify common contexts of occurrence and the coping strategies employed. The survey revealed that social gatherings, such as parties, are the most frequent settings for sexual harassment. Coping strategies varied based on the relationship with the assailant; only 6% of respondents would call 911 when harassed by an acquaintance, compared to 19% when harassed by a stranger. The most common strategy involved pretending to have an urgent matter and leaving the scene, followed by contacting friends for assistance. However, many participants expressed fear of escalating the situation by provoking the perpetrator. Drawing from these insights, we propose the design of a disguised app that mimics a social media app, thus reducing suspicion while using an app in close proximity to the perpetrator. The app includes multiple functionalities recommended by survey participants: 1) triggering a fake call from a male voice, 2) sharing location and discreetly chatting with friends, 3) recording, and 4) an emergency alarm. This paper discusses how these design features address the challenges of safely navigating sexual harassment incidents.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686332"}, {"primary_key": "711082", "vector": [], "sparse_vector": [], "title": "Real-Time Word-Level Temporal Segmentation in Streaming Speech Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Rich-text captions are essential to help communication for Deaf and hard-of-hearing (DHH) people, second-language learners, and those with autism spectrum disorder (ASD). They also preserve nuances when converting speech to text, enhancing the realism of presentation scripts and conversation or speech logs. However, current real-time captioning systems lack the capability to alter text attributes (ex. capitalization, sizes, and fonts) at the word level, hindering the accurate conveyance of speaker intent that is expressed in the tones or intonations of the speech. For example, “YOU should do this” tends to be considered as indicating “You” as the focus of the sentence, whereas “You should do THIS” tends to be “This” as the focus. This paper proposes a solution that changes the text decorations at the word level in real time. As a prototype, we developed an application that adjusts word size based on the loudness of each spoken word. Feedback from users implies that this system helped to convey the speaker’s intent, offering a more engaging and accessible captioning experience.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686738"}, {"primary_key": "711083", "vector": [], "sparse_vector": [], "title": "SpaceBlender: Creating Context-Rich Collaborative Spaces Through Generative 3D Scene Blending.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "There is increased interest in using generative AI to create 3D spaces for Virtual Reality (VR) applications. However, today's models produce artificial environments, falling short of supporting collaborative tasks that benefit from incorporating the user's physical context. To generate environments that support VR telepresence, we introduce SpaceBlender, a novel pipeline that utilizes generative AI techniques to blend users' physical surroundings into unified virtual spaces. This pipeline transforms user-provided 2D images into context-rich 3D environments through an iterative process consisting of depth estimation, mesh alignment, and diffusion-based space completion guided by geometric priors and adaptive text prompts. In a preliminary within-subjects study, where 20 participants performed a collaborative VR affinity diagramming task in pairs, we compared SpaceBlender with a generic virtual environment and a state-of-the-art scene generation framework, evaluating its ability to create virtual spaces suitable for collaboration. Participants appreciated the enhanced familiarity and context provided by SpaceBlender but also noted complexities in the generative environments that could detract from task focus. Drawing on participant feedback, we propose directions for improving the pipeline and discuss the value and design of blended spaces for different scenarios.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676361"}, {"primary_key": "711084", "vector": [], "sparse_vector": [], "title": "TeleHand: Hand-only Teleportation for Distant Object Pointing in Virtual Reality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The goal of this study is to present an easy-to-use and accurate pointing technique for distant objects in a virtual reality environment. For this purpose, we propose TeleHand pointing, in which the user first teleports only their hand near a target and points at the target using the ray cast from the teleported hand. To evaluate the usability of our method, we performed a user study in which participants repeated selection tasks for distant targets. As a result, we found that, although the proposed method slightly increases the task completion time, it significantly reduces pointing errors for distant targets compared to the traditional ray-based method.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686327"}, {"primary_key": "711085", "vector": [], "sparse_vector": [], "title": "NotePlayer: Engaging Computational Notebooks for Dynamic Presentation of Analytical Processes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Diverse presentation formats play a pivotal role in effectively conveying code and analytical processes during data analysis.One increasingly popular format is tutorial videos, particularly those based on Jupyter notebooks, which offer an intuitive interpretation of code and vivid explanations of analytical procedures.However, creating such videos requires a diverse skill set and significant manual effort, posing a barrier for many analysts.To bridge this gap, we introduce an innovative tool called NotePlayer, which connects notebook cells to video segments and incorporates a computational engine with language models to streamline video creation and editing.Our aim is to make the process more accessible and efficient for analysts.To inform the design of NotePlayer, we conducted a formative study and performed content analysis on a corpus of 38 Jupyter tutorial videos.This helped us identify key patterns and challenges encountered in existing tutorial videos, guiding the development of NotePlayer.Through a combination of a usage scenario and a user study, we validated the effectiveness of NotePlayer.The results show that the tool streamlines the video creation and facilitates the communication process for data analysts.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676410"}, {"primary_key": "711086", "vector": [], "sparse_vector": [], "title": "Speed-Modulated Ironing: High-Resolution Shade and Texture Gradients in Single-Material 3D Printing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Speed-Modulated Ironing, a new fabrication method for programming visual and tactile properties in single-material 3D printing. We use one nozzle to 3D print and a second nozzle to reheat printed areas at varying speeds, controlling the material’s temperature-response. The rapid adjustments of speed allow for fine-grained reheating, enabling high-resolution color and texture variations. We implemented our method in a tool that allows users to assign desired properties to 3D models and creates corresponding 3D printing instructions. We demonstrate our method with three temperature-responsive materials: a foaming filament, a filament with wood fibers, and a filament with cork particles. These filaments respond to temperature by changing color, roughness, transparency, and gloss. Our technical evaluation reveals the capabilities of our method in achieving sufficient resolution and color shade range that allows surface details such as small text, photos, and QR codes on 3D-printed objects. Finally, we provide application examples demonstrating the new design capabilities enabled by Speed-Modulated Ironing.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676456"}, {"primary_key": "711087", "vector": [], "sparse_vector": [], "title": "Demonstrating Speed-Modulated Ironing: High-Resolution Shade and Texture Gradients in Single-Material 3D Printing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Speed-Modulated Ironing, a new fabrication method for programming visual and tactile properties in single-material 3D printing. We use one nozzle to 3D print and a second nozzle to reheat printed areas at varying speeds, controlling the material’s temperature-response. The rapid adjustments of speed allow for fine-grained reheating, enabling high-resolution color and texture variations. We implemented our method in a tool that allows users to assign desired properties to 3D models and creates corresponding 3D printing instructions. We demonstrate our method with three temperature-responsive materials: a foaming filament, a filament with wood fibers, and a filament with cork particles. These filaments respond to temperature by changing color, roughness, transparency, and gloss. Our method is able to achieve sufficient resolution and color shade range that allows surface details such as small text, photos, and QR codes on 3D-printed objects. Finally, we provide application examples demonstrating the new design capabilities enabled by Speed-Modulated Ironing.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686772"}, {"primary_key": "711088", "vector": [], "sparse_vector": [], "title": "VoicePilot: <PERSON><PERSON><PERSON> LLMs as Speech Interfaces for Physically Assistive Robots.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Physically assistive robots present an opportunity to significantly increase the well-being and independence of individuals with motor impairments or other forms of disability who are unable to complete activities of daily living. Speech interfaces, especially ones that utilize Large Language Models (LLMs), can enable individuals to effectively and naturally communicate high-level commands and nuanced preferences to robots. Frameworks for integrating LLMs as interfaces to robots for high level task planning and code generation have been proposed, but fail to incorporate human-centric considerations which are essential while developing assistive interfaces. In this work, we present a framework for incorporating LLMs as speech interfaces for physically assistive robots, constructed iteratively with 3 stages of testing involving a feeding robot, culminating in an evaluation with 11 older adults at an independent living facility. We use both quantitative and qualitative data from the final study to validate our framework and additionally provide design guidelines for using LLMs as speech interfaces for assistive robots. Videos and supporting files are located on our project website: https://sites.google.com/andrew.cmu.edu/voicepilot/", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676401"}, {"primary_key": "711089", "vector": [], "sparse_vector": [], "title": "LingoComics: Co-Authoring Comic Style AI-Empowered Stories for Language Learning Immersion with Story Designer.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In language learning applications, contextualization and immersion rely on real-life communication and remain challenging despite the recent advancements in artificial intelligence (AI) that have significantly impacted educational and language learning experiences. This paper introduces LingoComics, a web application that embeds AI-empowered stories with narrative and comic-style illustrations to enhance contextualization and personalization in language learning. At the core of LingoComics is the Story Designer module, which allows learners to co-author short narratives using a structured set of parameters within a simple user interface. Leveraging OpenAI’s GPT-4-turbo for text completion and DALLE-3 for image generation, the Story Designer generates contextually relatable stories and comic-style images based on user input. Future work includes user evaluations, activity designs, and additional language learning support features. LingoComics aims to increase learners’ confidence and motivation by enabling personalized, situational language practice, preparing them for real-life communication.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686352"}, {"primary_key": "711090", "vector": [], "sparse_vector": [], "title": "Bluefish: Composing Diagrams with Declarative Relations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Figure 1: Diagrams built with the Bluefish language.These graphics run the gamut from computer science to physics to math, and are constructed with declarative, composable, extensible relations.From left to right: a quantum circuit equivalence [42], topologies [59], a Python Tutor diagram [34], an Ohm parse tree [23], and a physics pulley diagram [49].", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676465"}, {"primary_key": "711091", "vector": [], "sparse_vector": [], "title": "HRI and UIST: Designing Socially Engaging Robot Interfaces.", "authors": ["<PERSON><PERSON><PERSON>", "Ari<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Human-Robot Interaction (HRI) is a field of study that focuses on the understanding, design, and evaluation of interactions between humans and robots. This workshop aims to bring together researchers interested in exploring the intersection of UIST and HRI. Our goal is to provide attendees with a deeper understanding of the synergies between the two research communities and to inspire better alignment between technical advancements in UIST and their application to social HRI contexts. The workshop will feature interactive demos, prototyping sessions, and discussions to explore key HRI concepts and considerations for designing robot interfaces that facilitate social interactions with humans.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686705"}, {"primary_key": "711092", "vector": [], "sparse_vector": [], "title": "SHAPE-IT: Exploring Text-to-Shape-Display for Generative Shape-Changing Behaviors with LLMs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Gao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces text-to-shape-display, a novel approach to generating dynamic shape changes in pin-based shape displays through natural language commands. By leveraging large language models (LLMs) and AI-chaining, our approach allows users to author shape-changing behaviors on demand through text prompts without programming. We describe the foundational aspects necessary for such a system, including the identification of key generative elements (primitive, animation, and interaction) and design requirements to enhance user interaction, based on formative exploration and iterative design processes. Based on these insights, we develop SHAPE-IT, an LLM-based authoring tool for a 24 x 24 shape display, which translates the user's textual command into executable code and allows for quick exploration through a web-based control interface. We evaluate the effectiveness of SHAPE-IT in two ways: 1) performance evaluation and 2) user evaluation (N= 10). The study conclusions highlight the ability to facilitate rapid ideation of a wide range of shape-changing behaviors with AI. However, the findings also expose accuracy-related challenges and limitations, prompting further exploration into refining the framework for leveraging AI to better suit the unique requirements of shape-changing systems.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676348"}, {"primary_key": "711093", "vector": [], "sparse_vector": [], "title": "Seent: Interfacing Gamified Olfactory Training.", "authors": ["<PERSON>"], "summary": "Olfactory dysfunction affects a significant proportion of the global population. Although traditional olfactory training can help with rehabilitation, it often lacks engagement due to its repetitive and dull nature. Advances in digital olfactory technologies have made the interaction between humans and odours possible. We introduce Seent, an interactive interface designed to gamify olfactory training, including hardware to digitalise odours and a corresponding Graphical User Interface. Seent integrates playful gameplay into the training process, allowing participants to engage in a series of odour-based interactive games. This paper concludes by outlining future steps and proposing its potential to enhance olfactory rehabilitation and promote regular olfactory health monitoring.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686356"}, {"primary_key": "711094", "vector": [], "sparse_vector": [], "title": "Feminist Interaction Techniques: Social Consent Signals to Deter NCIM Screenshots.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Non-consensual Intimate Media (NCIM) refers to the distribution of sexual or intimate content without consent. NCIM is common and causes significant emotional, financial, and reputational harm. We developed Hands-Off, an interaction technique for messaging applications that deters non-consensual screenshots. Hands-Off requires recipients to perform a hand gesture in the air, above the device, to unlock media—which makes simultaneous screenshotting difficult. A lab study shows that Hands-Off gestures are easy to perform and reduce non-consensual screenshots by 67%. We conclude by generalizing this approach and introduce the idea of Feminist Interaction Techniques (FIT), interaction techniques that encode feminist values and speak to societal problems, and reflect on FIT’s opportunities and limitations.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676380"}, {"primary_key": "711095", "vector": [], "sparse_vector": [], "title": "Enabling Safer Augmented Reality Experiences: Usable Privacy Interventions for AR Creators and End-Users.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Augmented reality (AR) is approaching everyday usage, but poses novel privacy concerns for end-users and bystanders due to how AR devices capture users and process physical environments. To enable the benefits of AR while balancing privacy goals, my dissertation develops tools and frameworks to guide AR creators and users to address privacy risks that can arise with AR. First, I explore how to enable AR designers to interactively analyze potential risks in their prototypes through implicit threat modeling within AR authoring tools. Next, through elicitation studies with AR and privacy experts, I contribute frameworks to expand AR interaction models with privacy-friendlier alternatives to traditional AR input, output, and interaction techniques. Lastly, I develop a suite of AI-enabled Privacy Assistant techniques to raise users’ awareness of privacy risks and help them adapt AR interfaces accordingly. Ultimately, my dissertation promotes an AR ecosystem with privacy at the forefront by equipping AR creators and users with a privacy mindset.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686708"}, {"primary_key": "711096", "vector": [], "sparse_vector": [], "title": "BlendScape: Enabling End-User Customization of Video-Conferencing Environments through Generative AI.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Today’s video-conferencing tools support a rich range of professional and social activities, but their generic meeting environments cannot be dynamically adapted to align with distributed collaborators’ needs. To enable end-user customization, we developed BlendScape, a rendering and composition system for video-conferencing participants to tailor environments to their meeting context by leveraging AI image generation techniques. BlendScape supports flexible representations of task spaces by blending users’ physical or digital backgrounds into unified environments and implements multimodal interaction techniques to steer the generation. Through an exploratory study with 15 end-users, we investigated whether and how they would find value in using generative AI to customize video-conferencing environments. Participants envisioned using a system like BlendScape to facilitate collaborative activities in the future, but required further controls to mitigate distracting or unrealistic visual elements. We implemented scenarios to demonstrate BlendScape’s expressiveness for supporting environment design strategies from prior work and propose composition techniques to improve the quality of environments.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676326"}, {"primary_key": "711097", "vector": [], "sparse_vector": [], "title": "AccessTeleopKit: A Toolkit for Creating Accessible Web-Based Interfaces for Tele-Operating an Assistive Robot.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile manipulator robots, which can move around and physically interact with their environments, can empower people with motor limitations to independently carry out many activities of daily living. While many interfaces have been developed for tele-operating complex robots, most of them are not accessible to people with severe motor limitations. Further, most interfaces are rigid with limited configurations and are not readily available to download and use. To address these barriers, we developed AccessTeleopKit: an open-source toolkit for creating custom and accessible robot tele-operation interfaces based on cursor-and-click input for the Stretch 3 mobile-manipulator. With AccessTeleopKit users can add, remove, and rearrange components such as buttons and camera views, and select between a variety of control modes. We describe the participatory and iterative design process that led to the current implementation of AccessTeleopKit, involving three long-term deployments of the robot in the home of a quadriplegic user. We demonstrate how AccessTeleopKit allowed the user to create different interfaces for different tasks and the diversity of tasks it allowed the user to carry out. We also present two studies involving six additional users with severe motor limitations, demonstrating the power of AccessTeleopKit in creating custom interfaces for different user needs and preferences.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676355"}, {"primary_key": "711098", "vector": [], "sparse_vector": [], "title": "ScriptViz: A Visualization Tool to Aid Scriptwriting based on a Large Movie Database.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Scriptwriters usually rely on their mental visualization to create a vivid story by using their imagination to see, feel, and experience the scenes they are writing. Besides mental visualization, they often refer to existing images or scenes in movies and analyze the visual elements to create a certain mood or atmosphere. In this paper, we develop ScriptViz to provide external visualization based on a large movie database for the screenwriting process. It retrieves reference visuals on the fly based on scripts’ text and dialogue from a large movie database. The tool provides two types of control on visual elements that enable writers to 1) see exactly what they want with fixed visual elements and 2) see variances in uncertain elements. User evaluation among 15 scriptwriters shows that ScriptViz is able to present scriptwriters with consistent yet diverse visual possibilities, aligning closely with their scripts and helping their creation.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676402"}, {"primary_key": "711099", "vector": [], "sparse_vector": [], "title": "CARDinality: Interactive Card-shaped Robots with Locomotion and Haptics using Vibration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Hung", "<PERSON>"], "summary": "This paper introduces a novel approach to interactive robots by leveraging the form-factor of cards to create thin robots equipped with vibrational capabilities for locomotion and haptic feedback. The system is composed of flat-shaped robots with on-device sensing and wireless control, which offer lightweight portability and scalability. This research introduces a hardware prototype. Applications include augmented card playing, educational tools, and assistive technology, which showcase CARDinality's versatility in tangible interaction.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676421"}, {"primary_key": "711100", "vector": [], "sparse_vector": [], "title": "StegoType: Surface Typing from Egocentric Cameras.", "authors": ["<PERSON>", "Fadi <PERSON>", "Yangyang Shi", "<PERSON><PERSON><PERSON>", "Bradford J. Snow", "<PERSON><PERSON>", "Jing<PERSON> Dong", "<PERSON>", "<PERSON><PERSON><PERSON> Ma", "<PERSON>"], "summary": "Text input is a critical component of any general purpose computing system, yet efficient and natural text input remains a challenge in AR and VR. Headset based hand-tracking has recently become pervasive among consumer VR devices and affords the opportunity to enable touch typing on virtual keyboards. We present an approach for decoding touch typing on uninstrumented flat surfaces using only egocentric camera-based hand-tracking as input. While egocentric hand-tracking accuracy is limited by issues like self occlusion and image fidelity, we show that a sufficiently diverse training set of hand motions paired with typed text can enable a deep learning model to extract signal from this noisy input. Furthermore, by carefully designing a closed-loop data collection process, we can train an end-to-end text decoder that accounts for natural sloppy typing on virtual keyboards. We evaluate our work with a user study (n=18) showing a mean online throughput of 42.4 WPM with an uncorrected error rate (UER) of 7% with our method compared to a physical keyboard baseline of 74.5 WPM at 0.8% UER, showing progress towards unlocking productivity and high throughput use cases in AR/VR.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676343"}, {"primary_key": "711101", "vector": [], "sparse_vector": [], "title": "StegoType: Surface Typing from Egocentric Cameras.", "authors": ["<PERSON>", "Fadi <PERSON>", "Yangyang Shi", "Bradford J. Snow", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jing<PERSON> Dong", "<PERSON>", "<PERSON><PERSON><PERSON> Ma", "<PERSON>"], "summary": "Text input is a critical component of any general purpose computing system, yet efficient and natural text input remains a challenge in AR and VR. Headset based hand-tracking has recently become pervasive among consumer VR devices and affords the opportunity to enable touch typing on virtual keyboards. We present an approach for decoding touch typing on uninstrumented flat surfaces using only egocentric camera-based hand-tracking as input. While egocentric hand-tracking accuracy is limited by issues like self occlusion and image fidelity, we show that a sufficiently diverse training set of hand motions paired with typed text can enable a deep learning model to extract signal from this noisy input. Furthermore, by carefully designing a closed-loop data collection process, we can train an end-to-end text decoder that accounts for natural sloppy typing on virtual keyboards. We evaluate our work with a user study (n=18) showing a mean online throughput of 42.4 WPM with an uncorrected error rate (UER) of 7% with our method compared to a physical keyboard baseline of 74.5 WPM at 0.8% UER, showing progress towards unlocking productivity and high throughput use cases in AR/VR.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686762"}, {"primary_key": "711102", "vector": [], "sparse_vector": [], "title": "DrawTalking: Building Interactive Worlds by Sketching and Speaking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce DrawTalking, an approach to building and controlling interactive worlds by sketching and speaking while telling stories. It emphasizes user control and flexibility, and gives programming-like capability without requiring code. An early open-ended study with our prototype shows that the mechanics resonate and are applicable to many creative-exploratory use cases, with the potential to inspire and inform research in future natural interfaces for creative exploration and authoring.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676334"}, {"primary_key": "711103", "vector": [], "sparse_vector": [], "title": "HoloChemie - Sustainable Fabrication of Soft Biochemical Holographic Devices for Ubiquitous Sensing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sustainable fabrication approaches and biomaterials are increasingly being used in HCI to fabricate interactive devices. However, the majority of the work has focused on integrating electronics. This paper takes a sustainable approach to exploring the fabrication of biochemical sensing devices. Firstly, we contribute a set of biochemical formulations for biological and environmental sensing with bio-sourced and environment-friendly substrate materials. Our formulations are based on a combination of enzymes derived from bacteria and fungi, plant extracts and commercially available chemicals to sense both liquid and gaseous analytes: glucose, lactic acid, pH levels and carbon dioxide. Our novel holographic sensing scheme allows for detecting the presence of analytes and enables quantitative estimation of the analyte levels. We present a set of application scenarios that demonstrate the versatility of our approach and discuss the sustainability aspects, its limitations, and the implications for bio-chemical systems in HCI.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676448"}, {"primary_key": "711104", "vector": [], "sparse_vector": [], "title": "Block and Detail: Scaffolding Sketch-to-Image Generation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a novel sketch-to-image tool that aligns with the iterative refinement process of artists. Our tool lets users sketch blocking strokes to coarsely represent the placement and form of objects and detail strokes to refine their shape and silhouettes. We develop a two-pass algorithm for generating high-fidelity images from such sketches at any point in the iterative process. In the first pass we use a ControlNet to generate an image that strictly follows all the strokes (blocking and detail) and in the second pass we add variation by renoising regions surrounding blocking strokes. We also present a dataset generation scheme that, when used to train a ControlNet architecture, allows regions that do not contain strokes to be interpreted as not-yet-specified regions rather than empty space. We show that this partial-sketch-aware ControlNet can generate coherent elements from partial sketches that only contain a small number of strokes. The high-fidelity images produced by our approach serve as scaffolds that can help the user adjust the shape and proportions of objects or add additional elements to the composition. We demonstrate the effectiveness of our approach with a variety of examples and evaluative comparisons. Quantitatively, evaluative user feedback indicates that novice viewers prefer the quality of images from our algorithm over a baseline Scribble ControlNet for 84% of the pairs and found our images had less distortion in 81% of the pairs.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676444"}, {"primary_key": "711105", "vector": [], "sparse_vector": [], "title": "Intelligence as Agency.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In his 2009 AAAI article [10], <PERSON> described AI and HCI as “two fields divided by a common focus”, noting how they “competed for intellectual and economic resources” (Fig. 1(a)). HCI is in its strongest position yet — with several senior HCI researchers leading human-centered AI teams, organizations, and institutes at major companies and universities. But, there continues to be the risk that history repeats itself: that HCI finds itself primarily reacting to advances in AI [19], rather than being a coequal discipline that exerts pressures that drive advances in AI as well. In this talk, I will propose two conceptual shifts that more explicitly center HCI values in an era of rapid progress in AI: (1) redefining intelligence as agency, the capacity to meaningfully act, rather than the capacity to perform a task; and (2) formulating design as the delegation of constrained agency, rather than solely the specification of affordances.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3695751"}, {"primary_key": "711106", "vector": [], "sparse_vector": [], "title": "Supporting Control and Alignment in Personal Informatics Tools.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Despite the abundance of diverse personal data and its potential for improving health, individuals struggle to draw value from it. A key challenge is difficulties in controlling the functionality of existing systems and aligning them with evolving needs. These systems commonly restrict what information is recorded and how, lack effective means for sense-making and decision-making, and fall short in supporting the translation of data insights into personalized actions. My research addresses these challenges through building prototype systems, designing interactive techniques, and devising computational algorithms.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686709"}, {"primary_key": "711107", "vector": [], "sparse_vector": [], "title": "GradualReality: Enhancing Physical Object Interaction in Virtual Reality via Interaction State-Aware Blending.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present GradualReality, a novel interface enabling a Cross Reality experience that includes gradual interaction with physical objects in a virtual environment and supports both presence and usability. Daily Cross Reality interaction is challenging as the user’s physical object interaction state is continuously changing over time, causing their attention to frequently shift between the virtual and physical worlds. As such, presence in the virtual environment and seamless usability for interacting with physical objects should be maintained at a high level. To address this issue, we present an Interaction State-Aware Blending approach that (i) balances immersion and interaction capability and (ii) provides a fine-grained, gradual transition between virtual and physical worlds. The key idea includes categorizing the flow of physical object interaction into multiple states and designing novel blending methods that offer optimal presence and sufficient physical awareness at each state. We performed extensive user studies and interviews with a working prototype and demonstrated that GradualReality provides better Cross Reality experiences compared to baselines.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676463"}, {"primary_key": "711108", "vector": [], "sparse_vector": [], "title": "Vision-Based Hand Gesture Customization from a Single Demonstration.", "authors": ["<PERSON><PERSON><PERSON>", "Vimal Mollyn", "<PERSON>ri <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hand gesture recognition is becoming a more prevalent mode of human-computer interaction, especially as cameras proliferate across everyday devices. Despite continued progress in this field, gesture customization is often underexplored. Customization is crucial since it enables users to define and demonstrate gestures that are more natural, memorable, and accessible. However, customization requires efficient usage of user-provided data. We introduce a method that enables users to easily design bespoke gestures with a monocular camera from one demonstration. We employ transformers and meta-learning techniques to address few-shot learning challenges. Unlike prior work, our method supports any combination of one-handed, two-handed, static, and dynamic gestures, including different viewpoints, and the ability to handle irrelevant hand movements. We implement three real-world applications using our customization method, conduct a user study, and achieve up to 94% average recognition accuracy from one demonstration. Our work provides a viable path for vision-based gesture customization, laying the foundation for future advancements in this domain.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676378"}, {"primary_key": "711109", "vector": [], "sparse_vector": [], "title": "Who Validates the Validators? Aligning LLM-Assisted Evaluation of LLM Outputs with Human Preferences.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Due to the cumbersome nature of human evaluation and limitations of code-based evaluation, Large Language Models (LLMs) are increasingly being used to assist humans in evaluating LLM outputs. Yet LLM-generated evaluators simply inherit all the problems of the LLMs they evaluate, requiring further human validation. We present a mixed-initiative approach to “validate the validators”—aligning LLM-generated evaluation functions (be it prompts or code) with human requirements. Our interface, EvalGen, provides automated assistance to users in generating evaluation criteria and implementing assertions. While generating candidate implementations (Python functions, LLM grader prompts), EvalGen asks humans to grade a subset of LLM outputs; this feedback is used to select implementations that better align with user grades. A qualitative study finds overall support for EvalGen but underscores the subjectivity and iterative nature of alignment. In particular, we identify a phenomenon we dub criteria drift: users need criteria to grade outputs, but grading outputs helps users define criteria. What is more, some criteria appear dependent on the specific LLM outputs observed (rather than independent and definable a priori), raising serious questions for approaches that assume the independence of evaluation from observation of model outputs. We present our interface and implementation details, a comparison of our algorithm with a baseline approach, and implications for the design of future LLM evaluation assistants.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676450"}, {"primary_key": "711110", "vector": [], "sparse_vector": [], "title": "MetaController: Sheet Material Based Flexible Game Controlling System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce MetaControllers, reconfigurable game-controlling devices that are made of wood sheets. We intend to use this flexible material system to create devices whose configurations can be customized for different games to create context-specific haptic experiences, which cannot otherwise be achieved by traditional game controllers. Our approach exploits cellular-based material structure units consisting of rigid faces and hinges out of one single material. By assembling units together, we can create higher level of mechanical transformations through either embedded actuation or externally applied compression. In this proposal, we show that the different compositions of these units, when coupled with electronics, can enable novel ways of controlling and interacting with the games that provide dynamic physical affordances and guide users with dynamic physical constraints.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686732"}, {"primary_key": "711111", "vector": [], "sparse_vector": [], "title": "DexteriSync: A Hand Thermal I/O Exoskeleton for Morphing Finger Dexterity Experience.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Skin temperature is an important physiological factor for human hand dexterity. Leveraging this feature, we engineered an exoskeleton, called DexteriSync, that can dynamically adjust the user’s finger dexterity and induce different thermal perceptions by modulating finger skin temperature. This exoskeleton comprises flexible silicone-copper tube segments, 3D-printed finger sockets, a 3D-printed palm base, a pump system, and a water temperature control with a storage unit. By realising an embodied experience of compromised dexterity, DexteriSync can help product designers understand the lived experience of compromised hand dexterity, such as that of the elderly and/or neurodivergent users, when designing daily necessities for them. We validated DexteriSync via a technical evaluation and two user studies, demonstrating that it can change skin temperature, dexterity, and thermal perception. An exploratory session with design students and an autistic compromised dexterity individual, demonstrated the exoskeleton provided a more realistic experience compared to video education, and allowed them to gain higher confidence in their designs. The results advocated for the efficacy of experiencing embodied compromised finger dexterity, which can promote an understanding of the related physical challenges and lead to a more persuasive design for assistive tools.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676422"}, {"primary_key": "711112", "vector": [], "sparse_vector": [], "title": "Pay Attention! Human-Centric Improvements of LLM-based Interfaces for Assisting Software Test Case Development.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Implementing automation testing is difficult and as a consequence there is a growing desire for semi-automated software testing systems with humans in the loop. Leveraging the growth of LLMs, recent research has demonstrated LLMs’ potential to improve performance on test generation, reporting, and bug triaging. However, relatively little work has explored the interactivity issues that emerge in semi-automated LLM-assisted software test case development. To fill this gap, we present two user studies (N1 = 16, N2 = 24) that investigate productivity, creativity, and user attention in three semi-automated LLM-assisted interaction strategies: (1) pre-emptive prompting; (2) buffered response; and (3) guided input. We find that pre-emptively prompting the user significantly enhances branch coverage and task creativity by more than 30% while reducing user’s off-task idle time by up to 48.7%. We conclude by suggesting concrete research directions applying mixed-initiative principles for LLM-based interactive systems for semi-automated software testing.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686341"}, {"primary_key": "711113", "vector": [], "sparse_vector": [], "title": "Empathy-GPT: Leveraging Large Language Models to Enhance Emotional Empathy and User Engagement in Embodied Conversational Agents.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Emotional empathy, the ability to understand and respond to others’ emotions, is essential for effective communication. We propose Empathy-GPT, featuring embodied conversational agents with empathic capacity. To address the limitations of rule-based conversational agents, we leverage contextual understanding and adaptation capabilities of large language models (LLMs) to coordinate multiple modalities (e.g., agent’s tone, body movements, and facial expressions). To enhance user engagement in human-agent communication, agents dynamically respond to users’ voices and facial expressions, providing contextually empathic responses.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686729"}, {"primary_key": "711114", "vector": [], "sparse_vector": [], "title": "Desk2Desk: Optimization-based Mixed Reality Workspace Integration for Remote Side-by-side Collaboration.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>n <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mixed Reality enables hybrid workspaces where physical and virtual monitors are adaptively created and moved to suit the current environment and needs. However, in shared settings, individual users’ workspaces are rarely aligned and can vary significantly in the number of monitors, available physical space, and workspace layout, creating inconsistencies between workspaces which may cause confusion and reduce collaboration. We present Desk2Desk, an optimization-based approach for remote collaboration in which the hybrid workspaces of two collaborators are fully integrated to enable immersive side-by-side collaboration. The optimization adjusts each user’s workspace in layout and number of shared monitors and creates a mapping between workspaces to handle inconsistencies between workspaces due to physical constraints (e.g. physical monitors). We show in a user study how our system adaptively merges dissimilar physical workspaces to enable immersive side-by-side collaboration, and demonstrate how an optimization-based approach can effectively address dissimilar physical layouts.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676339"}, {"primary_key": "711115", "vector": [], "sparse_vector": [], "title": "Demonstrating FIRE: Mid-Air Thermo-Tactile Display.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present an innovative mid-air thermo-tactile display system based on ultrasound haptics. The system features an open-top chamber, heat modules, and an ultrasound haptic display. Our approach directs heated airflow toward the pressure point created by the ultrasound display, delivering thermal and tactile sensations simultaneously in mid-air. We demonstrate our system in four distinct VR environments—campfire, water fountain, kitchen, and candle—highlighting the enhanced user experiences made possible by the integration of thermal and tactile feedback.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686769"}, {"primary_key": "711116", "vector": [], "sparse_vector": [], "title": "Thermal In Motion: Designing Thermal Flow Illusions with Tactile and Thermal Interaction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This study presents a novel method for creating moving thermal sensations by integrating the thermal referral illusion with tactile motion. Conducted through three experiments on human forearms, the first experiment examined the impact of temperature and thermal actuator placement on perceived thermal motion, finding the clearest perception with a centrally positioned actuator under both hot and cold conditions. The second experiment identified the speed thresholds of perceived thermal motion, revealing a wider detectable range in hot conditions (1.8 cm/s to 9.5cm/s) compared to cold conditions (2.4cm/s to 5.0cm/s). Finally, we integrated our approach into virtual reality (VR) to assess its feasibility through two interaction scenarios. Our results shed light on the comprehension of thermal perception and its integration with tactile cues, promising significant advancements in incorporating thermal motion into diverse thermal interfaces for immersive VR experiences.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676460"}, {"primary_key": "711117", "vector": [], "sparse_vector": [], "title": "VisionTasker: Mobile Task Automation Using Vision Based UI Understanding and LLM Task Planning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile task automation is an emerging field that leverages AI to streamline and optimize the execution of routine tasks on mobile devices, thereby enhancing efficiency and productivity. Traditional methods, such as Programming By Demonstration (PBD), are limited due to their dependence on predefined tasks and susceptibility to app updates. Recent advancements have utilized the view hierarchy to collect UI information and employed Large Language Models (LLM) to enhance task automation. However, view hierarchies have accessibility issues and face potential problems like missing object descriptions or misaligned structures. This paper introduces VisionTasker, a two-stage framework combining vision-based UI understanding and LLM task planning, for mobile task automation in a step-by-step manner. VisionTasker firstly converts a UI screenshot into natural language interpretations using a vision-based UI understanding approach, eliminating the need for view hierarchies. Secondly, it adopts a step-by-step task planning method, presenting one interface at a time to the LLM. The LLM then identifies relevant elements within the interface and determines the next action, enhancing accuracy and practicality. Extensive experiments show that VisionTasker outperforms previous methods, providing effective UI representations across four datasets. Additionally, in automating 147 real-world tasks on an Android smartphone, VisionTasker demonstrates advantages over humans in tasks where humans show unfamiliarity and shows significant improvements when integrated with the PBD mechanism. VisionTasker is open-source and available at https://github.com/AkimotoAyako/VisionTasker.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676386"}, {"primary_key": "711118", "vector": [], "sparse_vector": [], "title": "TouchInsight: Uncertainty-aware Rapid Touch and Text Input for Mixed Reality from Egocentric Vision.", "authors": ["<PERSON>", "<PERSON>", "Fadi <PERSON>", "<PERSON><PERSON><PERSON> Ma", "<PERSON>", "<PERSON>"], "summary": "While passive surfaces offer numerous benefits for interaction in mixed reality, reliably detecting touch input solely from head-mounted cameras has been a long-standing challenge. Camera specifics, hand self-occlusion, and rapid movements of both head and fingers introduce considerable uncertainty about the exact location of touch events. Existing methods have thus not been capable of achieving the performance needed for robust interaction. In this paper, we present a real-time pipeline that detects touch input from all ten fingers on any physical surface, purely based on egocentric hand tracking. Our method TouchInsight comprises a neural network to predict the moment of a touch event, the finger making contact, and the touch location. TouchInsight represents locations through a bivariate Gaussian distribution to account for uncertainties due to sensing inaccuracies, which we resolve through contextual priors to accurately infer intended user input. We first evaluated our method offline and found that it locates input events with a mean error of 6.3 mm, and accurately detects touch events (F1=0.99) and identifies the finger used (F1=0.96). In an online evaluation, we then demonstrate the effectiveness of our approach for a core application of dexterous touch input: two-handed text entry. In our study, participants typed 37.0 words per minute with an uncorrected error rate of 2.9% on average.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676330"}, {"primary_key": "711119", "vector": [], "sparse_vector": [], "title": "A Demo of DIAM: Drone-based Indoor Accessibility Mapping.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jingwei Ma", "<PERSON>"], "summary": "Indoor mapping data is crucial for navigation and accessibility, yet such data are widely lacking due to the manual labor and expense of data collection, especially for larger indoor spaces. In this demo paper, we introduce Drone-based Indoor Accessibility Mapping (DIAM), a drone-based indoor scanning system that efficiently produces 3D reconstructions of indoor spaces with automatically recognized and located accessibility features/barriers such as stairs, elevators, and doors automatically. With DIAM, our goal is to scan indoor spaces quickly and generate a precise, detailed, and visual 3D indoor accessibility map. We describe DIAM’s system design, present its technical capabilities, and discuss future use cases.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686782"}, {"primary_key": "711120", "vector": [], "sparse_vector": [], "title": "SonifyAR: Context-Aware Sound Generation in Augmented Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Sound plays a crucial role in enhancing user experience and immersiveness in Augmented Reality (AR). However, current platforms lack support for AR sound authoring due to limited interaction types, challenges in collecting and specifying context information, and difficulty in acquiring matching sound assets. We present SonifyAR, an LLM-based AR sound authoring system that generates context-aware sound effects for AR experiences. SonifyAR expands the current design space of AR sound and implements a Programming by Demonstration (PbD) pipeline to automatically collect contextual information of AR events, including virtual-content-semantics and real-world context. This context information is then processed by a large language model to acquire sound effects with Recommendation, Retrieval, Generation, and Transfer methods. To evaluate the usability and performance of our system, we conducted a user study with eight participants and created five example applications, including an AR-based science experiment, and an assistive application for low-vision AR users.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676406"}, {"primary_key": "711121", "vector": [], "sparse_vector": [], "title": "Dynamic Abstractions: Building the Next Generation of Cognitive Tools and Interfaces.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This workshop provides a forum to discuss, brainstorm, and prototype the next generation of interfaces that leverage the dynamic experiences enabled by recent advances in AI and the generative capabilities of foundation models. These models simplify complex tasks by generating outputs in various representations (e.g., text, images, videos) through diverse input modalities like natural language, voice, and sketch. They interpret user intent to generate and transform representations, potentially changing how we interact with information and express ideas. Inspired by this potential, technologists, theorists, and researchers are exploring new forms of interaction by building demos and communities dedicated to concretizing and advancing the vision of working with dynamic abstractions. This UIST workshop provides a timely space to discuss AI’s impact on how we might design and use cognitive tools (e.g., languages, notations, diagrams). We will explore the challenges, critiques, and opportunities of this space by thinking through and prototyping use cases across various domains.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686706"}, {"primary_key": "711122", "vector": [], "sparse_vector": [], "title": "StructCurves: Interlocking Block-Based Line Structures.", "authors": ["Zezhou Sun", "<PERSON>", "<PERSON>"], "summary": "We present a new class of curved block-based line structures whose component chains are flexible when separated, and provably rigid when assembled together into an interlocking double chain. The joints are inspired by traditional zippers, where a binding fabric or mesh connects individual teeth. Unlike traditional zippers, the joint design produces a rigid interlock with programmable curvature. This allows fairly strong curved structures to be built out of easily stored flexible chains. In this paper, we introduce a pipeline for generating these curved structures using a novel block design template based on revolute joints. Mesh embedded in these structures maintains block spacing and assembly order. We evaluate the rigidity of the curved structures through mechanical performance testing and demonstrate several applications.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676354"}, {"primary_key": "711123", "vector": [], "sparse_vector": [], "title": "MagneDot: Integrated Fabrication and Actuation Methods of Dot-Based Magnetic Shape Displays.", "authors": ["<PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents MagneDot, a novel method for making interactive magnetic shape displays through an integrated fabrication process. Magnetic soft materials can potentially create fast, responsive morphing structures for interactions. However, novice users and designers typically do not have access to sophisticated equipment and materials or cannot afford heavy labor to create interactive objects based on this material. Modified from an open-source 3D printer, the fabrication system of MagneDot integrates the processes of mold-making, pneumatic extrusion, magnetization, and actuation, using cost-effective materials only. By providing a design tool, MagneDot allows users to generate G-code for fabricating and actuating displays of various morphing effects. Finally, a series of design examples demonstrate the possibilities of shape displays enabled by MagneDot.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676427"}, {"primary_key": "711124", "vector": [], "sparse_vector": [], "title": "EarHover: Mid-Air Gesture Recognition for Hearables Using Sound Leakage Signals.", "authors": ["<PERSON>nta <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce EarHover, an innovative system that enables mid-air gesture input for hearables. Mid-air gesture input, which eliminates the need to touch the device and thus helps to keep hands and the device clean. However, existing mid-air gesture input methods for hearables have been limited to adding cameras or infrared sensors. By focusing on the sound leakage phenomenon unique to hearables, we have realized mid-air gesture recognition using a speaker and an external microphone that are highly compatible with hearables. The signal leaked to the outside of the device due to sound leakage can be measured by an external microphone, which detects the differences in reflection characteristics caused by the hand’s speed and shape during mid-air gestures. Among 27 types of gestures, we determined the seven suitable gestures for EarHover in terms of signal discrimination and user acceptability. We then evaluated the gesture detection and classification performance of two prototype devices (in-ear type/open-ear type) for real-world application scenarios.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676367"}, {"primary_key": "711125", "vector": [], "sparse_vector": [], "title": "Flexmock: Fast, easy, stockable smocking method using 3D printed self-shrinkable pattern sheet.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this study, we propose Flexmock, a fabrication method that enables users to easily generate smocking patterns onto cloth by shrinking attachment sheets with geometric patterns. The proposed method offers the following advantages: (1) creating smocking patterns parametrically using design software and simulating the finished product; (2) automatically generating and exporting data for self-shrinking attachments; and (3) quickly applying smocking patterns to fabrics via attached 3D printed pattern sheets.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686335"}, {"primary_key": "711126", "vector": [], "sparse_vector": [], "title": "Can a Smartwatch Move Your Fingers? Compact and Practical Electrical Muscle Stimulation in a Smartwatch.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Archit <PERSON>", "<PERSON>", "Shan-<PERSON>", "<PERSON>"], "summary": "Smartwatches gained popularity in the mainstream, making them into today’s de-facto wearables. Despite advancements in sensing, haptics on smartwatches is still restricted to tactile feedback (e.g., vibration). Most smartwatch-sized actuators cannot render strong force-feedback. Simultaneously, electrical muscle stimulation (EMS) promises compact force-feedback but, to actuate fingers requires users to wear many electrodes on their forearms. While forearm electrodes provide good accuracy, they detract EMS from being a practical force-feedback interface. To address this, we propose moving the electrodes to the wrist—conveniently packing them in the backside of a smartwatch. In our first study, we found that by cross-sectionally stimulating the wrist in 1,728 trials, we can actuate thumb extension, index extension & flexion, middle flexion, pinky flexion, and wrist flexion. Following, we engineered a compact EMS that integrates directly into a smartwatch’s wristband (with a custom stimulator, electrodes, demultiplexers, and communication). In our second study, we found that participants could calibrate our device by themselves Math 1 faster than with conventional EMS. Furthermore, all participants preferred the experience of this device, especially for its social acceptability & practicality. We believe that our approach opens new applications for smartwatch-based interactions, such as haptic assistance during everyday tasks.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676373"}, {"primary_key": "711127", "vector": [], "sparse_vector": [], "title": "picoRing: battery-free rings for subtle thumb-to-index input.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Figure 1: Overview of picoRing.picoRing is a flexible sensing architecture enabling a variety of battery-free smart rings paired with a wristband.This paper shows four types of picoRing that support thumb-to-index finger pressing, sliding, or scrolling.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676365"}, {"primary_key": "711128", "vector": [], "sparse_vector": [], "title": "PronounSE: SFX Synthesizer from Language-Independent Vocal Mimic Representation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sound creators make various sound effects (SFX) depending on auditory events utilizing knowledge, techniques, and experience. These are challenging tasks for inexperienced creators. In this research, we focus on the fact that it is relatively easy for anyone to mimic SFX with utterances, and we propose a novel interactive technique called PronounSE. It can synthesize SFX to reflect subtle sound nuances by the vocal representation with language-independent sound mimicry. PronounSE consists of a Transformer that converts a mel-spectrogram of utterance mimic sound into a mel-spectrogram of SFX, and iSTFTNet, as a neural-vocoder, reconstructs a waveform for a synthesized SFX. We built a dataset for PronounSE that especially picked explosion sounds which we could easily represent with many nuances. This paper describes the model of PronounSE, the dataset of explosion sounds and their various vocal mimic representations of plural people, and the results of synthesized SFXs from untrained representations interactively.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686748"}, {"primary_key": "711129", "vector": [], "sparse_vector": [], "title": "Nervous System Interception: A New Paradigm for Haptics.", "authors": ["<PERSON><PERSON>"], "summary": "When outputting information to our senses, almost all wearable interfaces follow the same principle: externally generating stimuli (e.g., lights, sounds, vibrations) and then presenting them via devices placed at the endpoints of our sensory system, such as head-mounted displays facing the eyes and vibration motors on the skin. While this intuitive approach of stimulating the endpoints might be sufficient for audiovisual interfaces, we argue that when it comes to engaging the sense of touch (i.e., haptics), it will be insufficient to support a wide variety of interactive experiences. Even a single haptic device on the hand will obstruct users from touching or grabbing objects in the real world, making it undesirable for mixed reality. Let alone scaling this approach to a larger portion of the body, which would restrict the user’s whole body. My research introduces an alternative approach to haptic output: instead of stimulating endpoints with external stimuli, we explore interactive devices that “internally” send electrical signals to the user’s nervous system—intercepting the nervous system. Our approach creates haptic sensations beyond the point where the device is worn, establishing a basis for enabling haptic feedback while keeping the user’s body free and scaling haptic interfaces to work for the entire body. This paper provides an overview of our approach: (1) how intercepting the nerves can provide touch and force feedback without obstructing the user’s body with actuators; (2) how it can integrate into practical wearable devices such as a smartwatch; and (3) its potential to eventually generalize to a full-body interface by intercepting the user’s brain.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686715"}, {"primary_key": "711130", "vector": [], "sparse_vector": [], "title": "Demonstrating Haptic Source-Effector: Full-Body Haptics via Non-Invasive Brain Stimulation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a novel concept for haptics in which one centralized on-body actuator renders haptic effects on multiple body parts by stimulating the brain, i.e., the source of the nervous system—we call this a haptic source-effector, as opposed to the traditional wearables’ approach of attaching one actuator per body part (end-effectors). We implement our concept via transcranial-magnetic-stimulation (TMS)—a non-invasive technique from neuroscience/medicine in which electromagnetic pulses safely stimulate brain areas. Our approach renders ~15 touch/force-feedback sensations throughout the body (e.g., hands, arms, legs, feet, and jaw), all by stimulating the user’s sensorimotor cortex with a single magnetic coil moved mechanically across the scalp.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686756"}, {"primary_key": "711131", "vector": [], "sparse_vector": [], "title": "Exploring the Effects of Fantasy Level of Avatars on User Perception and Behavior.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Embodying avatars in virtual reality (VR) has transformed human experiences, such as in medicine and education. However, there is limited information about users’ self-identifications and perceptions of highly fantastical avatars. This pilot study explored the impact of avatar types of low and high fantasy levels on adults’ perceptions and behaviors. Participants (N = 18) engaged in a VR experience with either a human or blue Muppet avatar to complete body movement tasks, a cube-touching game, and free-form exploration. Findings showed that participants in the high fantasy avatar condition reported higher identification with their avatar and more interest in social-emotional activities relative to the low fantasy human avatar condition. Across both conditions, participants stood extremely close to the virtual mirror. Additionally, we report on participants preferences and priorities for their future avatars. This offers insights for future research on avatar design, with implications for more engaging VR experience.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686355"}, {"primary_key": "711132", "vector": [], "sparse_vector": [], "title": "VizGroup: An AI-assisted Event-driven System for Collaborative Programming Learning Analytics.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Programming instructors often conduct collaborative learning activities, like Peer Instruction, to foster a deeper understanding in students and enhance their engagement with learning. These activities, however, may not always yield productive outcomes due to the diversity of student mental models and their ineffective collaboration. In this work, we introduce VizGroup, an AI-assisted system that enables programming instructors to easily oversee students’ real-time collaborative learning behaviors during large programming courses. VizGroup leverages Large Language Models (LLMs) to recommend event specifications for instructors so that they can simultaneously track and receive alerts about key correlation patterns between various collaboration metrics and ongoing coding tasks. We evaluated VizGroup with 12 instructors in a comparison study using a dataset collected from a Peer Instruction activity that was conducted in a large programming lecture. The results showed that VizGroup helped instructors effectively overview, narrow down, and track nuances throughout students’ behaviors.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676347"}, {"primary_key": "711133", "vector": [], "sparse_vector": [], "title": "Demonstrating LLM-for-X: Application-agnostic Integration of Large Language Models to Support Writing Workflows.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this demonstration, we show LLM-for-X, a system-wide shortcut layer that connects any application to backend LLM support through a lightweight popup dialog. LLM-for-X provides users with quick and easy-to-use LLM assistance without context switching to support writing and reading tasks. We show the use of LLM-for-X across several applications, such as Microsoft Office, VSCode, and Adobe Acrobat, which our tool seamlessly connects to the backends of OpenAI ChatGPT and Google Gemini. We also demonstrate the use of our system inside web apps such as Overleaf.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686757"}, {"primary_key": "711134", "vector": [], "sparse_vector": [], "title": "SQLucid: Grounding Natural Language Database Queries with Interactive Explanations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Though recent advances in machine learning have led to significant improvements in natural language interfaces for databases, the accuracy and reliability of these systems remain limited, especially in high-stakes domains. This paper introduces SQLucid, a novel user interface that bridges the gap between non-expert users and complex database querying processes. SQLucid addresses existing limitations by integrating visual correspondence, intermediate query results, and editable step-by-step SQL explanations in natural language to facilitate user understanding and engagement. This unique blend of features empowers users to understand and refine SQL queries easily and precisely. Two user studies and one quantitative experiment were conducted to validate SQLucid’s effectiveness, showing significant improvement in task completion accuracy and user confidence compared to existing interfaces. Our code is available at https://github.com/magic-YuanTian/SQLucid.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676368"}, {"primary_key": "711135", "vector": [], "sparse_vector": [], "title": "DataPipettor: Touch-Based Information Transfer Interface Using Proximity Wireless Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hidet<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As diverse computing devices, including wearable and embedded systems, become ubiquitous in our living spaces, sharing and transferring data between these computers has become a usual operation. This paper introduces DataPipettor, an information transfer interface that enables intuitive data movement based on touch interactions. DataPipettor utilizes proximity wireless communication (PWC), allowing high-speed real-time communication between proximate channels while simultaneously achieving touch sensing and data transmission/reception. The interface can be miniaturized due to the characteristics of PWC, making it suitable for use in wearable devices and small equipment interfaces. Users wearing the interface can intuitively transfer data through touch interactions as if physically passing objects. This paper conducted the development and evaluation of the interface, a conceptual demonstration, and discuss future prospects.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686333"}, {"primary_key": "711136", "vector": [], "sparse_vector": [], "title": "Micro-Gesture Recognition of Tongue via Bone Conduction Sound.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a hands-free and less perceptible gesture sensing method of the tongue by capturing the bone conduction sound generated when the tongue rubs the teeth. The sound is captured by the bone conduction microphones attached behind the ears. In this work, we show that tongue slide, snap, and teeth click gestures can be classified using the decision tree algorithm, which focuses on the characteristics in the sound spectrogram. We conducted a preliminary experiment to verify that input methods for mouth microgesture devices using bone conduction can be expanded from only teeth to teeth and tongue gestures without any additional obtrusive sensors. The evaluation revealed that our method achieved a classification accuracy of 82.7% with user-specific parameter adjustment.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686336"}, {"primary_key": "711137", "vector": [], "sparse_vector": [], "title": "Personal Time-Lapse.", "authors": ["<PERSON><PERSON> (Nathan) Tran", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Our bodies are constantly in motion—from the bending of arms and legs to the less conscious movement of breathing, our precise shape and location change constantly. This can make subtler developments (e.g., the growth of hair, or the healing of a wound) difficult to observe. Our work focuses on helping users record and visualize this type of subtle, longer-term change. We present a mobile tool that combines custom 3D tracking with interactive visual feedback and computational imaging to capture personal time-lapse, which approximates longer-term video of the subject (typically, part of the capturing user’s body) under a fixed viewpoint, body pose, and lighting condition. These personal time-lapses offer a powerful and detailed way to track visual changes of the subject over time. We begin with a formative study that examines what makes personal time-lapse so difficult to capture. Building on our findings, we motivate the design of our capture tool, evaluate this design with users, and demonstrate its effectiveness in a variety of challenging examples.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676383"}, {"primary_key": "711138", "vector": [], "sparse_vector": [], "title": "Gait Gestures: Examining Stride and Foot Strike Variation as an Input Method While Walking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Walking is a cyclic pattern of alternating footstep strikes, with each pair of steps forming a stride, and a series of strides forming a gait. We conduct a systematic examination of different kinds of intentional variations from a normal gait that could be used as input actions without interrupting overall walking progress. A design space of 22 candidate Gait Gestures is generated by adapting previous standing foot input actions and identifying new actions possible in a walking context. A formative study (n=25) examines movement easiness, social acceptability, and walking compatibility with foot movement logging to calculate temporal and spatial characteristics. Using a categorization of these results, 7 gestures are selected for a wizard-of-oz prototype demonstrating an AR interface controlled by Gait Gestures for ordering food and audio playback while walking. As a technical proof-of-concept, a gait gesture recognizer is developed and tested using the formative study data.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676342"}, {"primary_key": "711139", "vector": [], "sparse_vector": [], "title": "Emotion Overflow: an Interactive System to Represent Emotion with Fluid.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents Emotion Overflow, a novel system that transforms textual emotional input into interactive fluid visualizations. Utilizing a GPT model, our approach extracts emotion attributes from text and maps them onto fluid characteristics, creating a dynamic visual representation of emotional states. The system focuses on valence and arousal as key emotion attributes, translating them into fluid properties such as color, variance over time, and responsiveness. We demonstrate the application through two interactive experiences: a direct emotion visualization and an emotion guessing game. This work contributes to the field of emotional expression in human-computer interaction by offering a fluid, intuitive interface for exploring and communicating complex emotional states.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686779"}, {"primary_key": "711140", "vector": [], "sparse_vector": [], "title": "What&apos;s in a cable? Abstracting Knitting Design Elements with Blended Raster/Vector Primitives.", "authors": ["<PERSON>", "Yuecheng <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In chart-based programming environments for machine knitting, patterns are specified at a low level by placing operations on a grid. This highly manual workflow makes it challenging to iterate on design elements such as cables, colorwork, and texture. While vector-based abstractions for knitting design elements may facilitate higher-level manipulation, they often include interdependencies which require stitch-level reconciliation. To address this, we contribute a new way of specifying knits with blended vector and raster primitives. Our abstraction supports the design of interdependent elements like colorwork and texture. We have implemented our blended raster/vector specification in a direct manipulation design tool where primitives are layered and rasterized, allowing for simulation of the resulting knit structure and generation of machine instructions. Through examples, we show how our approach enables higher-level manipulation of various knitting techniques, including intarsia colorwork, short rows, and cables. Specifically, we show how our tool supports the design of complex patterns including origami pleat patterns and capacitive sensor patches.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676351"}, {"primary_key": "711141", "vector": [], "sparse_vector": [], "title": "MicroCode: live, portable programming for children via robotics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The BBC micro:bit is a popular tool in education for teaching coding, but typically requires a host computer and internet access, limiting its accessibility. MicroCode addresses this by enabling portable programming directly on the micro:bit using a battery-powered accessory with an OLED screen and navigation buttons. This system utilises a simple, handheld graphical tile-based programming paradigm, yet supports complex programs with features including conditional execution and variables, providing immediate feedback through live programming. This paper illustrates how early studies have received a positive reception from children and educators, especially when paired with robotics as an application domain. Plans for future work aim to extend the reach of Microcode by providing more tangible digital learning opportunities to pre-literate children and communities around the world where access to mains power and internet are scarce.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686334"}, {"primary_key": "711143", "vector": [], "sparse_vector": [], "title": "Modulating Heart Activity and Task Performance using Haptic Heartbeat Feedback: A Study Across Four Body Placements.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper explores the impact of vibrotactile haptic feedback on heart activity when the feedback is provided at four different body locations (chest, wrist, neck, and ankle) and with two feedback rates (50 bpm and 110 bpm). A user study found that the neck placement resulted in higher heart rates and lower heart rate variability, and higher frequencies correlated with increased heart rates and decreased heart rate variability. The chest was preferred in self-reported metrics, and neck placement was perceived as less satisfying, harmonious, and immersive. This research contributes to understanding the interplay between psychological experiences and physiological responses when using haptic biofeedback resembling real body signals.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676435"}, {"primary_key": "711144", "vector": [], "sparse_vector": [], "title": "GPTVoiceTasker: Advancing Multi-step Mobile Task Efficiency Through Dynamic Interface Exploration and Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sheng<PERSON> Zhao", "<PERSON><PERSON><PERSON>", "Chunyang Chen"], "summary": "Virtual assistants have the potential to play an important role in helping users achieves different tasks.However, these systems face challenges in their real-world usability, characterized by inefficiency and struggles in grasping user intentions.Leveraging recent advances in Large Language Models (LLMs), we introduce <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a virtual assistant poised to enhance user experiences and task efficiency on mobile devices.GptVoiceTasker excels at intelligently deciphering user commands and executing relevant device interactions to streamline task completion.For unprecedented tasks, GptVoiceTasker utilises the contextual information and on-screen content to continuously explore and execute the tasks.In addition, the system continually learns from historical user commands to automate subsequent task invocations, further enhancing execution efficiency.From our experiments, GptVoiceTasker achieved 84.5% accuracy in parsing human commands into executable actions and 85.7% accuracy in automating multi-step tasks.In our user study, GptVoiceTasker boosted task efficiency in real-world scenarios by 34.85%, accompanied by positive participant feedback.We made GptVoiceTasker open-source, inviting further research into LLMs utilization for diverse tasks through prompt engineering and leveraging user usage data to improve efficiency.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676356"}, {"primary_key": "711145", "vector": [], "sparse_vector": [], "title": "Extending the Senses of Ubiquitous Devices.", "authors": ["<PERSON><PERSON>"], "summary": "Modern smart devices are equipped with numerous sensors that can serve new functions beyond their original design when repurposed through side-channel sensing. Side-channel sensing involves leveraging existing sensors in unconventional ways to detect subtle signals and gather information beyond their original intended purpose. This work explores the untapped potential of side-channel sensing to enhance the functionality of existing smart devices, particularly in addressing niche user needs. I present two approaches: The first technique involves low-cost, low-power sensor add-ons that users can attach to their devices, enhancing functionality without any internal modifications to the device. I showcase GlucoScreen, a smartphone add-on that leverages the capacitive touchscreen for blood glucose monitoring, and WatchLink, which allows users to connect external sensors to their smartwatches via the ECG interface. The second technique focuses on manufacturers making targeted upgrades to existing hardware to improve functionalities at reduced time and cost. I present Z-Ring, a ring wearable that expands the bandwidth of bio-impedance sensing to enable micro-gesture interactions and object identification. Our findings highlight the potential of side-channel sensing to create more personalized and adaptable technology.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686712"}, {"primary_key": "711146", "vector": [], "sparse_vector": [], "title": "WatchLink: Enhancing Smartwatches with Sensor Add-Ons via ECG Interface.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shwetak N. Patel"], "summary": "We introduce a low-power communication method that lets smartwatches leverage existing electrocardiogram (ECG) hardware as a data communication interface. Our unique approach enables the connection of external, inexpensive, and low-power \"add-on\" sensors to the smartwatch, expanding its functionalities. These sensors cater to specialized user needs beyond those offered by pre-built sensor suites, at a fraction of the cost and power of traditional communication protocols, including Bluetooth Low Energy. To demonstrate the feasibility of our approach, we conduct a series of exploratory and evaluative tests to characterize the ECG interface as a communication channel on commercial smartwatches. We design a simple transmission scheme using commodity components, demonstrating cost and power benefits. Further, we build and test a suite of add-on sensors, including UV light, body temperature, buttons, and breath alcohol, all of which achieved testing objectives at low material cost and power usage. This research paves the way for personalized and user-centric wearables by offering a cost-effective solution to expand their functionalities.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676329"}, {"primary_key": "711147", "vector": [], "sparse_vector": [], "title": "Demonstrating Z-Band: Enabling Subtle Hand Interactions with Bio-impedance Sensing on the Wrist.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Shwetak N. Patel"], "summary": "The increasing popularity of wearable extended reality (XR) technologies presents unique challenges for user input, as traditional methods like touchscreens or controllers can be cumbersome and less practical. As a result, researchers are exploring novel approaches to enable seamless and intuitive interaction within XR environments. In this work, we present Z-Band, a novel interaction device that enables subtle finger input in a wrist-worn form factor. Z-Band utilizes radio frequency (RF) sensing, leveraging the human hand as an antenna to detect subtle changes in hand impedance caused by finger movements. We demonstrate the feasibility and effectiveness of Z-Band by showcasing its capabilities through two applications: a music player controlled by finger gestures and a gesture-based game.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686766"}, {"primary_key": "711148", "vector": [], "sparse_vector": [], "title": "Eye-Hand Movement of Objects in Near Space Extended Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hand-tracking in Extended Reality (XR) enables moving objects in near space with direct hand gestures, to pick, drag and drop objects in 3D. In this work, we investigate the use of eye-tracking to reduce the effort involved in this interaction. As the eyes naturally look ahead to the target for a drag operation, the principal idea is to map the translation of the object in the image plane to gaze, such that the hand only needs to control the depth component of the operation. We have implemented four techniques that explore two factors: the use of gaze only to move objects in X-Y vs. extra refinement by hand, and the use of hand input in the Z axis to directly move objects vs. indirectly via a transfer function. We compared all four techniques in a user study (N=24) against baselines of direct and indirect hand input. We detail user performance, effort and experience trade-offs and show that all eye-hand techniques significantly reduce physical effort over direct gestures, pointing toward effortless drag-and-drop for XR environments.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676446"}, {"primary_key": "711149", "vector": [], "sparse_vector": [], "title": "EVE: Enabling Anyone to Train Robots using Augmented Reality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The increasing affordability of robot hardware is accelerating the integration of robots into everyday activities. However, training a robot to automate a task requires expensive trajectory data where a trained human annotator moves a physical robot to train it. Consequently, only those with access to robots produce demonstrations to train robots. In this work, we remove this restriction with EVE, an iOS app that enables everyday users to train robots using intuitive augmented reality visualizations, without needing a physical robot. With EVE, users can collect demonstrations by specifying waypoints with their hands, visually inspecting the environment for obstacles, modifying existing waypoints, and verifying collected trajectories. In a user study (N = 14, D = 30) consisting of three common tabletop tasks, EVE outperformed three state-of-the-art interfaces in success rate and was comparable to kinesthetic teaching—physically moving a physical robot—in completion time, usability, motion intent communication, enjoyment, and preference (meanp = 0.30). EVE allows users to train robots for personalized tasks, such as sorting desk supplies, organizing ingredients, or setting up board games. We conclude by enumerating limitations and design considerations for future AR-based demonstration collection systems for robotics.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676413"}, {"primary_key": "711150", "vector": [], "sparse_vector": [], "title": "X-Hair: 3D Printing Hair-like Structures with Multi-form, Multi-property and Multi-function.", "authors": ["<PERSON><PERSON><PERSON>", "Junzhe Ji", "Yunkai Xu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Sun", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present X-Hair, a method that enables 3D-printed hair with various forms, properties, and functions. We developed a two-step suspend printing strategy to fabricate hair-like structures in different forms (e.g. fluff, bristle, barb) by adjusting parameters including Extrusion Length Ratio and Total Length. Moreover, a design tool is also established for users to customize hair-like structures with various properties (e.g. pointy, stiff, soft) on imported 3D models, which virtually shows the results for previewing and generates G-code files for 3D printing. We demonstrate the design space of X-Hair and evaluate the properties of them with different parameters. Through a series of applications with hair-like structures, we validate X-hair’s practical usage of biomimicry, decoration, heat preservation, adhesion, and haptic interaction.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676360"}, {"primary_key": "711151", "vector": [], "sparse_vector": [], "title": "Towards Music-Aware Virtual Assistants.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a system for modifying spoken notifications in a manner that is sensitive to the music a user is listening to. Spoken notifications provide convenient access to rich information without the need for a screen. Virtual assistants see prevalent use in hands-free settings such as driving or exercising, activities where users also regularly enjoy listening to music. In such settings, virtual assistants will temporarily mute a user’s music to improve intelligibility. However, users may perceive these interruptions as intrusive, negatively impacting their music-listening experience. To address this challenge, we propose the concept of music-aware virtual assistants, where speech notifications are modified to resemble a voice singing in harmony with the user’s music. We contribute a system that processes user music and notification text to produce a blended mix, replacing original song lyrics with the notification content. In a user study comparing musical assistants to standard virtual assistants, participants expressed that musical assistants fit better with music, reduced intrusiveness, and provided a more delightful listening experience overall.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676416"}, {"primary_key": "711152", "vector": [], "sparse_vector": [], "title": "Empower Real-World BCIs with NIRS-X: An Adaptive Learning Framework that Harnesses Unlabeled Brain Signals.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Devon McKeon", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Brain-Computer Interfaces (BCIs) using functional near-infrared spectroscopy (fNIRS) hold promise for future interactive user interfaces due to their ease of deployment and declining cost. However, they typically require a separate calibration process for each user and task, which can be burdensome. Machine learning helps, but faces a data scarcity problem. Due to inherent inter-user variations in physiological data, it has been typical to create a new annotated training dataset for every new task and user. To reduce dependence on such extensive data collection and labeling, we present an adaptive learning framework, NIRS-X, to harness more easily accessible unlabeled fNIRS data. NIRS-X includes two key components: NIRSiam and NIRSformer. We use the NIRSiam algorithm to extract generalized brain activity representations from unlabeled fNIRS data obtained from previous users and tasks, and then transfer that knowledge to new users and tasks. In conjunction, we design a neural network, NIRSformer, tailored for capturing both local and global, spatial and temporal relationships in multi-channel fNIRS brain input signals. By using unlabeled data from both a previously released fNIRS2MW visual n-back dataset and a newly collected fNIRS2MW audio n-back dataset, NIRS-X demonstrates its strong adaptation capability to new users and tasks. Results show comparable or superior performance to supervised methods, making NIRS-X promising for real-world fNIRS-based BCIs.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676429"}, {"primary_key": "711153", "vector": [], "sparse_vector": [], "title": "RFTIRTouch: Touch Sensing Device for Dual-sided Transparent Plane Based on Repropagated Frustrated Total Internal Reflection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Frustrated total internal reflection (FTIR) imaging is widely applied in various touch-sensing systems. However, vision-based touch sensing has structural constraints, and the system size tends to increase. Although a sensing system with reduced thickness has been developed recently using repropagated FTIR (RFTIR), it lacks the property of instant installation anywhere because observation from the side of a transparent medium is required. Therefore, this study proposes an \"RFTIRTouch\" sensing device to capture RFTIR images from the contact surface. RFTIRTouch detects the touch position on a dual-sided plane using a physics-based estimation and can be retrofitted to existing transparent media with simple calibration. Our evaluation experiments confirm that the touch position can be estimated within an error of approximately 2.1 mm under optimal conditions. Furthermore, several application examples are implemented to demonstrate the advantages of RFTIRTouch, such as its ability to measure dual sides with a single sensor and waterproof the contact surface.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676428"}, {"primary_key": "711154", "vector": [], "sparse_vector": [], "title": "Demonstration of Sympathetic Orchestra: An Interactive Conducting Education System for Responsive, Tacit Skill Development.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Students learning musical conducting often practice along to static recordings, which do not provide real-time feedback similar to that of a live orchestra during rehearsals. Novice conductors need better solutions for practicing with feedback that mimics the experience of conducting a live orchestra. We can leverage emergent multimodal and spatial interaction technologies to support a “virtual orchestra” practice experience that allows students to develop tacit, live-practice knowledge. Through formative interviews with conducting experts and students, we designed and developed a dynamic, multimodal interaction system that targets key goals held by students developing their orchestral conducting skills, and that traditional practicing methods lack support for. Sympathetic Orchestra is an interactive virtual orchestra system that uses Google AI edge-powered Computer Vision hand and face tracking on webcam data to responsively interact with dynamic audio music playback and develop tacit practicing experiences.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686783"}, {"primary_key": "711155", "vector": [], "sparse_vector": [], "title": "Exploring a Software Tool for Biofibers Design.", "authors": ["<PERSON><PERSON>", "Eldy <PERSON>", "<PERSON>"], "summary": "The Biofibers Spinning Machine produces bio-based fibers (biofibers) that are dissolvable and biodegradable. These fibers enable recycling of smart textiles by making it easy to separate electronics from textiles. Currently, prototyping with the machine requires the use of low-level commands, i.e. G-code. To enable more people to participate in the sustainable smart textiles design space and develop new biofiber materials, we need to provide accessible tools and workflows. This work explores a software tool that facilitates material exploration with machine parameters. We describe the interface design and demonstrate using the tool to quantify the relationship between machine parameters and spun gelatin biofibers.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686317"}, {"primary_key": "711156", "vector": [], "sparse_vector": [], "title": "SolePoser: Full Body Pose Estimation using a Single Pair of Insole Sensor.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose SolePoser, a real-time 3D pose estimation system that leverages only a single pair of insole sensors. Unlike conventional methods relying on fixed cameras or bulky wearable sensors, our approach offers minimal and natural setup requirements. The proposed system utilizes pressure and IMU sensors embedded in insoles to capture the body weight’s pressure distribution at the feet and its 6 DoF acceleration. This information is used to estimate the 3D full-body joint position by a two-stream transformer network. A novel double-cycle consistency loss and a cross-attention module are further introduced to learn the relationship between 3D foot positions and their pressure distributions. We also introduced two different datasets of sports and daily exercises, offering 908k frames across eight different activities. Our experiments show that our method’s performance is on par with top-performing approaches, which utilize more IMUs and even outperform third-person-view camera-based methods in certain scenarios.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676418"}, {"primary_key": "711157", "vector": [], "sparse_vector": [], "title": "WaitGPT: Monitoring and Steering Conversational LLM Agent in Data Analysis with On-the-Fly Code Visualization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Large language models (LLMs) support data analysis through conversational user interfaces, as exemplified in OpenAI's ChatGPT (formally known as Advanced Data Analysis or Code Interpreter). Essentially, LLMs produce code for accomplishing diverse analysis tasks. However, presenting raw code can obscure the logic and hinder user verification. To empower users with enhanced comprehension and augmented control over analysis conducted by LLMs, we propose a novel approach to transform LLM-generated code into an interactive visual representation. In the approach, users are provided with a clear, step-by-step visualization of the LLM-generated code in real time, allowing them to understand, verify, and modify individual data operations in the analysis. Our design decisions are informed by a formative study (N=8) probing into user practice and challenges. We further developed a prototype named WaitGPT and conducted a user study (N=12) to evaluate its usability and effectiveness. The findings from the user study reveal that WaitGPT facilitates monitoring and steering of data analysis performed by LLMs, enabling participants to enhance error detection and increase their overall confidence in the results.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676374"}, {"primary_key": "711158", "vector": [], "sparse_vector": [], "title": "Memory Reviver: Supporting Photo-Collection Reminiscence for People with Visual Impairment via a Proactive Chatbot.", "authors": ["<PERSON><PERSON> Xu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yukang Yan", "<PERSON><PERSON><PERSON>"], "summary": "Reminiscing with photo collections offers significant psychological benefits but poses challenges for people with visual impairment (PVI). Their current reliance on sighted help restricts the flexibility of this activity. In response, we explored using a chatbot in a preliminary study. We identified two primary challenges that hinder effective reminiscence with a chatbot: the scattering of information and a lack of proactive guidance. To address these limitations, we present Memory Reviver, a proactive chatbot that helps PVI reminisce with a photo collection through natural language communication. Memory Reviver incorporates two novel features: (1) a Memory Tree, which uses a hierarchical structure to organize the information in a photo collection; and (2) a Proactive Strategy, which actively delivers information to users at proper conversation rounds. Evaluation with twelve PVI demonstrated that Memory Reviver effectively facilitated engaging reminiscence, enhanced understanding of photo collections, and delivered natural conversational experiences. Based on our findings, we distill implications for supporting photo reminiscence and designing chatbots for PVI.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676336"}, {"primary_key": "711159", "vector": [], "sparse_vector": [], "title": "SkipWriter: LLM-Powered Abbreviated Writing on Tablets.", "authors": ["<PERSON><PERSON><PERSON>", "Shanqing Cai", "<PERSON><PERSON>nd Varma T.", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large Language Models (LLMs) may offer transformative opportunities for text input, especially for physically demanding modalities like handwriting. We studied a form of abbreviated handwriting by designing, developing, and evaluating a prototype, named SkipWriter, that converts handwritten strokes of a variable-length prefix-based abbreviation (e.g., \"ho a y\" as handwritten strokes) into the intended full phrase (e.g., \"how are you\" in the digital format) based on the preceding context. SkipWriter consists of an in-production handwriting recognizer and an LLM fine-tuned on this task. With flexible pen input, SkipWriter allows the user to add and revise prefix strokes when predictions do not match the user’s intent. An user evaluation demonstrated a 60% reduction in motor movements with an average speed of 25.78 WPM. We also showed that this reduction is close to the ceiling of our model in an offline simulation.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676423"}, {"primary_key": "711160", "vector": [], "sparse_vector": [], "title": "MobilePoser: Real-Time Full-Body Pose Estimation and 3D Human Translation from IMUs in Mobile Consumer Devices.", "authors": ["<PERSON><PERSON> Xu", "<PERSON><PERSON> Gao", "<PERSON>", "<PERSON><PERSON>"], "summary": "There has been a continued trend towards minimizing instrumentation for full-body motion capture, going from specialized rooms and equipment, to arrays of worn sensors and recently sparse inertial pose capture methods. However, as these techniques migrate towards lower-fidelity IMUs on ubiquitous commodity devices, like phones, watches, and earbuds, challenges arise including compromised online performance, temporal consistency, and loss of global translation due to sensor noise and drift. Addressing these challenges, we introduce MobilePoser, a real-time system for full-body pose and global translation estimation using any available subset of IMUs already present in these consumer devices. MobilePoser employs a multi-stage deep neural network for kinematic pose estimation followed by a physics-based motion optimizer, achieving state-of-the-art accuracy while remaining lightweight. We conclude with a series of demonstrative applications to illustrate the unique potential of MobilePoser across a variety of fields, such as health and wellness, gaming, and indoor navigation to name a few.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676461"}, {"primary_key": "711161", "vector": [], "sparse_vector": [], "title": "DishAgent: Enhancing Dining Experiences through LLM-Based Smart Dishes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the rapid advancement of smart technologies, there is an increasing demand to enhance everyday experiences, including dining. Recent Human-Computer Interaction (HCI) research has begun to emphasize the aesthetic, affective, sensual, and sociocultural qualities of directly interacting with food. However, these technologies are often constrained by the material properties of food, limiting their everyday applicability. This research introduces DishAgent, an innovative device equipped with a Large Language Model (LLM)-based smart dish and a swarm robotics system. DishAgent adapts to various dining scenarios by generating appropriate conversational contexts and coordinating the action commands of swarm robots, thereby enhancing the dining experience through real-time interaction. This paper explores the applications of DishAgent in intelligent dining guidance, dietary behavior intervention, food information query and social companionship, aiming to fill the critical gap in current technologies for simply and intuitively enhancing dining experiences.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686755"}, {"primary_key": "711162", "vector": [], "sparse_vector": [], "title": "Sustainable in-house PCB prototyping.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Printed circuit boards (PCBs) are now ubiquitous in everyday objects. Despite advancements in mass production and the widespread availability of circuit design software, PCB manufacturing continues to present significant sustainability challenges due to the continual material sourcing and e-waste generation. In this paper, I investigate the sustainability challenges and potential solutions in PCB production, utilizing in-house prototyping as a focal point. This approach is both accessible for investigation and allows the generalization of findings to broader contexts. I propose novel digital fabrication techniques to enhance material circulation between PCB projects and design iterations, thereby mitigating the environmental impact of electronics manufacturing. I introduce three research archetypes: SolderlessPCB, Fibercuit, and PCB Renewal, each addressing sustainability challenges at different stages of PCB prototyping. These solutions possess the potential to be extrapolated to solve large-scale PCB manufacturing sustainability challenges, heralding a more sustainable future for electronics production.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686710"}, {"primary_key": "711163", "vector": [], "sparse_vector": [], "title": "Chromaticity Gradient Mapping for Interactive Control of Color Contrast in Images and Video.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a novel perceptually-motivated interactive tool for using color contrast to enhance details represented in the lightness channel of images and video. Our method lets users adjust the perceived contrast of different details by manipulating local chromaticity while preserving the original lightness of individual pixels. Inspired by the use of similar chromaticity mappings in painting, our tool effectively offers contrast along a user-selected gradient of chromaticities as additional bandwidth for representing and enhancing different details in an image. We provide an interface for our tool that closely resembles the familiar design of tonal contrast curve controls that are available in most professional image editing software. We show that our tool is effective for enhancing the perceived contrast of details without altering lightness in an image and present many examples of effects that can be achieved with our method on both images and video.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676340"}, {"primary_key": "711164", "vector": [], "sparse_vector": [], "title": "Enhancing How People Learn Procedural Tasks Through How-to Videos.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Humans learn skills to perform various tasks in their everyday lives. While how-to videos serve as a popular tool for people to learn skills and achieve tasks, there are limitations in learning from videos such as difficulties in accessing information in need or lack of personalized support. My Ph.D. research aims to enhance how people learn procedural tasks through how-to videos by understanding and improving the consumption of video content, application of the content to their own context, and reflection on the experiences. This research presents opportunities and insights into how we could better leverage videos for humans to learn skills and achieve tasks.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686711"}, {"primary_key": "711165", "vector": [], "sparse_vector": [], "title": "TorqueCapsules: Fully-Encapsulated Flywheel Actuation Modules for Designing and Prototyping Movement-Based and Kinesthetic Interaction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Flywheels are unique, versatile actuators that store and convert kinetic energy to torque, widely utilized in aerospace, robotics, haptics, and more. However, prototyping interaction using flywheels is not trivial due to safety concerns, unintuitive operation, and implementation challenges. We present TorqueCapsules: self-contained, fully-encapsulated flywheel actuation modules that make the flywheel actuators easy to control, safe to interact with, and quick to reconfigure and customize. By fully encapsulating the actuators with a wireless microcontroller, a battery, and other components, the module can be readily attached, embedded, or stuck to everyday objects, worn to people’s bodies, or combined with other devices. With our custom GUI, both novices and expert users can easily control multiple modules to design and prototype movements and kinesthetic haptics unique to flywheel actuation. We demonstrate various applications, including actuated everyday objects, wearable haptics, and expressive robots. We conducted workshops for novices and experts to employ TorqueCapsules to collect qualitative feedback and further application examples.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676364"}, {"primary_key": "711166", "vector": [], "sparse_vector": [], "title": "SenseBot: Leveraging Embodied Asymmetric Interaction and Social Robotic to Enhance Intergenerational Communication.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Ye", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Embodied communication, which uses physical cues to convey meaning and emotions, is essential for building social connections. In this work, we introduce <PERSON>Bot, an augmented reality (AR)-enabled robotic representative designed to facilitate embodied communication. SenseBot acts as a local agent for remote users, allowing them to engage more interactively and meaningfully with people on-site.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686734"}, {"primary_key": "711167", "vector": [], "sparse_vector": [], "title": "CrA<PERSON>zy MIDI: AI-powered Wearable Musical Instrumental for Novice Player.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Playing music is a deeply fulfilling and universally cherished activity, yet the steep learning curve often discourages novice amateurs. Traditional music creation demands significant time and effort to master musical theory, instrumental mechanics, motor skills, and notation reading. To lower these barriers, innovative technology-driven approaches are necessary. This proposal introduces CrAIzy MIDI, an AI-powered wearable musical instrument designed to simplify and enhance the music-playing experience for beginners. CrAIzy MIDI integrates three key technologies: wearable user interfaces, AI-generated music, and multi-modality tools. The wearable interface allows users to play multiple instruments using intuitive finger and palm movements, reducing the complexity of traditional instruments. AI-generated music segments enable users to input a few pitches and have the AI complete the musical piece, aiding beginners in overcoming composition challenges. The multi-modality experience enhances engagement by allowing adjustments in music effects through visual stimuli such as light color and intensity changes. Together, these features make music creation more accessible and enjoyable, fostering continuous practice and exploration for novice musicians.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686735"}, {"primary_key": "711168", "vector": [], "sparse_vector": [], "title": "Code Shaping: Iterative Code Editing with Free-form Sketching.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present an initial step towards building a system for programmers to edit code using free-form sketch annotations drawn directly onto editor and output windows. Using a working prototype system as a technical probe, an exploratory study (N = 6) examines how programmers sketch to annotate Python code to communicate edits for an AI model to perform. The results reveal personalized workflow strategies and how similar annotations vary in abstractness and intention across different scenarios and users.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686324"}, {"primary_key": "711169", "vector": [], "sparse_vector": [], "title": "Memolet: Reifying the Reuse of User-AI Conversational Memories.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "As users engage more frequently with AI conversational agents, conversations may exceed their “memory” capacity, leading to failures in correctly leveraging certain memories for tailored responses. However, in finding past memories that can be reused or referenced, users need to retrieve relevant information in various conversations and articulate to the AI their intention to reuse these memories. To support this process, we introduce Memolet, an interactive object that reifies memory reuse. Users can directly manipulate Memolet to specify which memories to reuse and how to use them. We developed a system demonstrating Memolet’s interaction across various memory reuse stages, including memory extraction, organization, prompt articulation, and generation refinement. We examine the system’s usefulness with an N=12 within-subject study and provide design implications for future systems that support user-AI conversational memory reusing.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676388"}, {"primary_key": "711170", "vector": [], "sparse_vector": [], "title": "CoLadder: Manipulating Code Generation via Multi-Level Blocks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper adopted an iterative design process to gain insights into programmers’ strategies when using LLMs for programming. We proposed CoLadder, a novel system that supports programmers by facilitating hierarchical task decomposition, direct code segment manipulation, and result evaluation during prompt authoring. A user study with 12 experienced programmers showed that CoLadder is effective in helping programmers externalize their problem-solving intentions flexibly, improving their ability to evaluate and modify code across various abstraction levels, from their task’s goal to final code implementation.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676357"}, {"primary_key": "711171", "vector": [], "sparse_vector": [], "title": "Palmrest+: Expanding Laptop Input Space with Shear Force on Palm-Resting Area.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The palmrest area of laptops has the potential as an additional input space, considering its consistent palm contact during keyboard interaction. We propose Palmrest+, leveraging shear force exerted on the palmrest area. We suggest two input techniques: Palmrest Shortcut, for instant shortcut execution, and Palmrest Joystick, for continuous value input. These allow seamless and subtle input amidst keyboard typing. Evaluation of Palmrest Shortcut against conventional keyboard shortcuts revealed faster performance for applying shear force in unimanual and bimanual-manner with a significant reduction in gaze shifting. Additionally, the assessment of Palmrest Joystick against the laptop touchpad demonstrated comparable performance in selecting one- and two- dimensional targets with low-precision pointing, i.e., for short distances and large target sizes. The maximal hand displacement significantly decreased for both Palmrest Shortcut and Palmrest Joystick compared to conventional methods. These findings verify the feasibility and effectiveness of leveraging the palmrest area as an additional input space on laptops, offering promising enhanced typing-related user interaction experiences.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676371"}, {"primary_key": "711172", "vector": [], "sparse_vector": [], "title": "Edible Lens Array: Dishes with lens-shaped jellies that change their appearance depending on the viewpoint.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This study presents food products whose appearance, such as color and image, changes depending on the viewpoint. This was achieved by fabricating jelly with structures of convex lenses, which is arranged in a two-dimensional plane using 3D-printed molds. This enables interactive gastronomic experiences with the presentation from multiple viewpoints. In this study, we developed a system that supports the design and fabrication workflow for edible lens arrays. Using our system, users can design arbitrary lens array shapes and simulate their appearance based on the refractive index of the jelly material. The system then outputs a 3D mold model for casting the jelly lenses. In addition, we created several dishes that exhibit viewpoint-dependent changes in appearance, demonstrating their potential for creating interactive gastronomic experiences.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686745"}, {"primary_key": "711173", "vector": [], "sparse_vector": [], "title": "SealingLid: FDM 3D Printing Technique that Bends Thin Walls to Work as a Lid.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "3D printers inspire user creativity by allowing them to design and create actual 3D objects. Unfortunately, printers often require long printing time and waste a lot of materials. Various techniques have been proposed to alleviate the issues, such as modification of the model and the user’s intervention during the printing process. We propose another approach, SealingLid, that creates thin walls and bends them by the printer’s head to form lids. It does not require model modification or user intervention and works with simple unmodified FDM 3D printers. A test confirms that the technique reduces the materials used for infill and support structures. Some primitive objects are fabricated to explore the possibilities of the technique.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686775"}, {"primary_key": "711174", "vector": [], "sparse_vector": [], "title": "FlexEOP: Flexible Shape-changing Actuator using Embedded Electroosmotic Pumps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Shape-changing actuators have been widely explored in the field of human-computer interaction (HCI), enabling various applications of shape-changing interfaces across from haptic feedback devices to robotics. However it is still challenging for existing methods to build shape-changing actuators that are flexible, capable of complex shape-changing behaviors, and highly self-contained at the same time. In this paper, we proposed FlexEOP, a method to create flexible electroosmotic pumps that are fully composed of flexible materials, facilitating shape-changing actuators with high flexibility and self-containment. We introduced the structure of FlexEOP and then demonstrated the design space of FlexEOP, including shape-changing display on flexible strips, panels, and curved surfaces, and a novel design of soft robotic fiber. Based on FlexEOP, we envision future applications including wearable tactile devices, curved shape-changing displays, and multi-degree-of-freedom self-contained soft robotics.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686785"}, {"primary_key": "711175", "vector": [], "sparse_vector": [], "title": "&quot;SimSnap&quot; Framework: Designing Interaction Methods for Cross-device Applications&quot;.", "authors": ["May Yu", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Tudor Tibu", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Despite significant developments in cross-device interaction techniques, disengagement interactions- that disconnect devices from the application- remain underexplored. This paper introduces \"SimSnap\", a touch-based cross-device interaction framework for connecting and disconnecting devices. We extended existing connection methods and developed a novel approach to disconnection. We identified design considerations for touch-based disconnecting and present our recommendations to address these issues.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686319"}, {"primary_key": "711176", "vector": [], "sparse_vector": [], "title": "SeamPose: Repurposing Seams as Capacitive Sensors in a Shirt for Upper-Body Pose Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Seams are areas of overlapping fabric formed by stitching two or more pieces of fabric together in the cut-and-sew apparel manufacturing process. In SeamPose, we repurposed seams as capacitive sensors in a shirt for continuous upper-body pose estimation. Compared to previous all-textile motion-capturing garments that place the electrodes on the clothing surface, our solution leverages existing seams inside of a shirt by machine-sewing insulated conductive threads over the seams. The unique invisibilities and placements of the seams afford the sensing shirt to look and wear similarly as a conventional shirt while providing exciting pose-tracking capabilities. To validate this approach, we implemented a proof-of-concept untethered shirt with 8 capacitive sensing seams. With a 12-participant user study, our customized deep-learning pipeline accurately estimates the relative (to the pelvis) upper-body 3D joint positions with a mean per joint position error (MPJPE) of 6.0 cm. SeamPose represents a step towards unobtrusive integration of smart clothing for everyday pose estimation.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676341"}, {"primary_key": "711177", "vector": [], "sparse_vector": [], "title": "Towards an LLM-Based Speech Interface for Robot-Assisted Feeding.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Physically assistive robots present an opportunity to significantly increase the well-being and independence of individuals with motor impairments or other forms of disability who are unable to complete activities of daily living (ADLs). Speech interfaces, especially ones that utilize Large Language Models (LLMs), can enable individuals to effectively and naturally communicate high-level commands and nuanced preferences to robots. In this work, we demonstrate an LLM-based speech interface for a commercially available assistive feeding robot. Our system is based on an iteratively designed framework, from the paper \"VoicePilot: Harnessing LLMs as Speech Interfaces for Physically Assistive Robots,\" that incorporates human-centric elements for integrating LLMs as interfaces for robots. It has been evaluated through a user study with 11 older adults at an independent living facility. Videos are located on our project website: https://sites.google.com/andrew.cmu.edu/voicepilot/.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686759"}, {"primary_key": "711178", "vector": [], "sparse_vector": [], "title": "MobiPrint: A Mobile 3D Printer for Environment-Scale Design and Fabrication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "3D printing is transforming how we customize and create physical objects in engineering, accessibility, and art. However, this technology is still primarily limited to confined working areas and dedicated print beds, thereby detaching design and fabrication from real-world environments and making measuring and scaling objects tedious and labor-intensive. In this paper, we present MobiPrint, a prototype mobile fabrication system that combines elements from robotics, architecture, and Human-Computer Interaction (HCI) to enable environment-scale design and fabrication in ad-hoc indoor environments. MobiPrint provides a multi-stage fabrication pipeline: first, the robotic 3D printer automatically scans and maps an indoor space; second, a custom design tool converts the map into an interactive CAD canvas for editing and placing models in the physical world; finally, the MobiPrint robot prints the object directly on the ground at the defined location. Through a “proof-by-demonstration” validation, we highlight our system’s potential across different applications, including accessibility, home furnishing, floor signage, and art. We also conduct a technical evaluation to assess MobiPrint’s localization accuracy, ground surface adhesion, payload capacity, and mapping speed. We close with a discussion of open challenges and opportunities for the future of contextualized mobile fabrication.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676459"}, {"primary_key": "711179", "vector": [], "sparse_vector": [], "title": "Accessible Gesture Typing on Smartphones for People with Low Vision.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While gesture typing is widely adopted on touchscreen keyboards, its support for low vision users is limited. We have designed and implemented two keyboard prototypes, layout-magnified and key-magnified keyboards, to enable gesture typing for people with low vision. Both keyboards facilitate uninterrupted access to all keys while the screen magnifier is active, allowing people with low vision to input text with one continuous stroke. Furthermore, we have created a kinematics-based decoding algorithm to accommodate the typing behavior of people with low vision. This algorithm can decode the gesture input even if the gesture trace deviates from a pre-defined word template, and the starting position of the gesture is far from the starting letter of the target word. Our user study showed that the key-magnified keyboard achieved 5.28 words per minute, 27.5% faster than a conventional gesture typing keyboard with voice feedback.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676447"}, {"primary_key": "711180", "vector": [], "sparse_vector": [], "title": "ChainBuddy: An AI-assisted Agent System for Helping Users Set up LLM Pipelines.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As large language models (LLMs) continue to advance, their potential applications have grown significantly. However, one persistent challenge remains: evaluating LLM behavior and crafting effective prompt chains. Many users struggle with where to start, often referred to as the \"blank page problem.\" ChainBuddy, a new evaluation assistant built into the ChainForge platform, aims to tackle this issue. It offers a straightforward and user-friendly way to plan and evaluate LLM behavior, making the process less daunting and more accessible.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686763"}, {"primary_key": "711181", "vector": [], "sparse_vector": [], "title": "ProtoDreamer: A Mixed-prototype Tool Combining Physical Model and Generative AI to Support Conceptual Design.", "authors": ["Hong<PERSON> Zhang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Wei<PERSON><PERSON>", "<PERSON><PERSON> Sun"], "summary": "Prototyping serves as a critical phase in the industrial conceptual design process, enabling exploration of problem space and identification of solutions. Recent advancements in large-scale generative models have enabled AI to become a co-creator in this process. However, designers often consider generative AI challenging due to the necessity to follow computer-centered interaction rules, diverging from their familiar design materials and languages. Physical prototype is a commonly used design method, offering unique benefits in prototype process, such as intuitive understanding and tangible testing. In this study, we propose ProtoDreamer, a mixed-prototype tool that synergizes generative AI with physical prototype to support conceptual design. ProtoDreamer allows designers to construct preliminary prototypes using physical materials, while AI recognizes these forms and vocal inputs to generate diverse design alternatives. This tool empowers designers to tangibly interact with prototypes, intuitively convey design intentions to AI, and continuously draw inspiration from the generated artifacts. An evaluation study confirms ProtoDreamer’s utility and strengths in time efficiency, creativity support, defects exposure, and detailed thinking facilitation.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676399"}, {"primary_key": "711182", "vector": [], "sparse_vector": [], "title": "DisMouse: Disentangling Information from Mouse Movement Data.", "authors": ["<PERSON><PERSON><PERSON> Zhang", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mouse movement data contain rich information about users, performed tasks, and user interfaces, but separating the respective components remains challenging and unexplored. As a first step to address this challenge, we propose DisMouse – the first method to disentangle user-specific and user-independent information and stochastic variations from mouse movement data. At the core of our method is an autoencoder trained in a semi-supervised fashion, consisting of a self-supervised denoising diffusion process and a supervised contrastive user identification module. Through evaluations on three datasets, we show that DisMouse 1) captures complementary information of mouse input, hence providing an interpretable framework for modelling mouse movements, 2) can be used to produce refined features, thus enabling various applications such as personalised and variable mouse data generation, and 3) generalises across different datasets. Taken together, our results underline the significant potential of disentangled representation learning for explainable, controllable, and generalised mouse behaviour modelling.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676411"}, {"primary_key": "711183", "vector": [], "sparse_vector": [], "title": "Understanding the Effects of Restraining Finger Coactivation in Mid-Air Typing: from a Neuromechanical Perspective.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Lin", "<PERSON><PERSON>", "<PERSON>"], "summary": "Typing in mid-air is often perceived as intuitive yet presents challenges due to finger coactivation, a neuromechanical phenomenon that involves involuntary finger movements stemming from the lack of physical constraints. Previous studies were used to examine and address the impacts of finger coactivation using algorithmic approaches. Alternatively, this paper explores the neuromechanical effects of finger coactivation on mid-air typing, aiming to deepen our understanding and provide valuable insights to improve these interactions. We utilized a wearable device that restrains finger coactivation as a prop to conduct two mid-air studies, including a rapid finger-tapping task and a ten-finger typing task. The results revealed that restraining coactivation not only reduced mispresses, which is a classic coactivated error always considered as harm caused by coactivation. Unexpectedly, the reduction of motor control errors and spelling errors, thinking as non-coactivated errors, also be observed. Additionally, the study evaluated the neural resources involved in motor execution using functional Near Infrared Spectroscopy (fNIRS), which tracked cortical arousal during mid-air typing. The findings demonstrated decreased activation in the primary motor cortex of the left hemisphere when coactivation was restrained, suggesting a diminished motor execution load. This reduction suggests that a portion of neural resources is conserved, which also potentially aligns with perceived lower mental workload and decreased frustration levels.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676441"}, {"primary_key": "711184", "vector": [], "sparse_vector": [], "title": "WasteBanned: Supporting Zero Waste Fashion Design Through Linked Edits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The commonly used cut-and-sew garment construction process, in which 2D fabric panels are cut from sheets of fabric and assembled into 3D garments, contributes to widespread textile waste in the fashion industry. There is often a significant divide between the design of the garment and the layout of the panels. One opportunity for bridging this gap is the emerging study and practice of zero waste fashion design, which involves creating clothing designs with maximum layout efficiency. Enforcing the strict constraints of zero waste sewing is challenging, as edits to one region of the garment necessarily affect neighboring panels. Based on our formative work to understand this emerging area within fashion design, we present WasteBanned, a tool that combines CAM and CAD to help users prioritize efficient material usage, work within these zero waste constraints, and edit existing zero waste garment patterns. Our user evaluation indicates that our tool helps fashion designers edit zero waste patterns to fit different bodies and add stylistic variation, while creating highly efficient fabric layouts.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676395"}, {"primary_key": "711185", "vector": [], "sparse_vector": [], "title": "VRCopilot: Authoring 3D Layouts with Generative AI Models in VR.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Immersive authoring provides an intuitive medium for users to create 3D scenes via direct manipulation in Virtual Reality (VR).Recent advances in generative AI have enabled the automatic creation of realistic 3D layouts.However, it is unclear how capabilities of generative AI can be used in immersive authoring to support fluid interactions, user agency, and creativity.We introduce VRCopilot, a mixed-initiative system that integrates pre-trained generative AI", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676451"}, {"primary_key": "711186", "vector": [], "sparse_vector": [], "title": "LlamaTouch: A Faithful and Scalable Testbed for Mobile UI Task Automation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Xianqing Jia", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The emergent large language/multimodal models facilitate the evolution of mobile agents, especially in mobile UI task automation. However, existing evaluation approaches, which rely on human validation or established datasets to compare agent-predicted actions with predefined action sequences, are unscalable and unfaithful. To overcome these limitations, this paper presents LlamaTouch, a testbed for on-device mobile UI task execution and faithful, scalable task evaluation. By observing that the task execution process only transfers UI states, LlamaTouch employs a novel evaluation approach that only assesses whether an agent traverses all manually annotated, essential application/system states. LlamaTouch comprises three key techniques: (1) On-device task execution that enables mobile agents to interact with realistic mobile environments for task execution. (2) Fine-grained UI component annotation that merges pixel-level screenshots and textual screen hierarchies to explicitly identify and precisely annotate essential UI components with a rich set of designed annotation primitives. (3) A multi-level application state matching algorithm that utilizes exact and fuzzy matching to accurately detect critical information in each screen, even with unpredictable UI layout/content dynamics. LlamaTouch currently incorporates four mobile agents and 496 tasks, encompassing both tasks in the widely-used datasets and our self-constructed ones to cover more diverse mobile applications. Evaluation results demonstrate LlamaTouch’s high faithfulness of evaluation in real-world mobile environments and its better scalability than human validation. LlamaTouch also enables easy task annotation and integration of new mobile agents. Code and dataset are publicly available at https://github.com/LlamaTouch/LlamaTouch.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676382"}, {"primary_key": "711187", "vector": [], "sparse_vector": [], "title": "TouchpadAnyWear: Textile-Integrated Tactile Sensors for Multimodal High Spatial-Resolution Touch Inputs with Motion Artifacts Tolerance.", "authors": ["<PERSON><PERSON>", "Pornthe<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Shen", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents TouchpadAnyWear, a novel family of textile-integrated force sensors capable of multi-modal touch input, encompassing micro-gesture detection, two-dimensional (2D) continuous input, and force-sensitive strokes. This thin (<1.5 mm) and conformal device features high spatial resolution sensing and motion artifact tolerance through its unique capacitive sensor architecture. The sensor consists of a knitted textile compressive core, sandwiched by stretchable silver electrodes, and conductive textile shielding layers on both sides. With a high-density sensor pixel array (25/cm2), TouchpadAnyWear can detect touch input locations and sizes with millimeter-scale spatial resolution and a wide range of force inputs (0.05 N to 20 N). The incorporation of miniature polymer domes, referred to as “poly-islands”, onto the knitted textile locally stiffens the sensing areas, thereby reducing motion artifacts during deformation. These poly-islands also provide passive tactile feedback to users, allowing for eyes-free localization of the active sensing pixels. Design choices and sensor performance are evaluated using in-depth mechanical characterization. Demonstrations include an 8-by-8 grid sensor as a miniature high-resolution touchpad and a T-shaped sensor for thumb-to-finger micro-gesture input. User evaluations validate the effectiveness and usability of TouchpadAnyWear in daily interaction contexts, such as tapping, forceful pressing, swiping, 2D cursor control, and 2D stroke-based gestures. This paper further discusses potential applications and explorations for TouchpadAnyWear in wearable smart devices, gaming, and augmented reality devices.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676344"}, {"primary_key": "711188", "vector": [], "sparse_vector": [], "title": "SelfGauge: An Intelligent Tool to Support Student Self-assessment in GenAI-enhanced Project-based Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Project-based learning (PBL) involves students tackling real-world problems and creating artifacts. With the rise of generative AI (GenAI) tools, assessing students in GenAI-enhanced PBL is challenging. To address this, we designed SelfGauge, a tool that supports student self-assessment by analyzing their GenAI usage and project artifacts. It helps students define criteria, seek feedback, and reflect on their performance, promoting continuous self-improvement.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686338"}, {"primary_key": "711189", "vector": [], "sparse_vector": [], "title": "DiscipLink: Unfolding Interdisciplinary Information Seeking Process via Human-AI Co-Exploration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Interdisciplinary studies often require researchers to explore literature in diverse branches of knowledge. Yet, navigating through the highly scattered knowledge from unfamiliar disciplines poses a significant challenge. In this paper, we introduce DiscipLink, a novel interactive system that facilitates collaboration between researchers and large language models (LLMs) in interdisciplinary information seeking (IIS). Based on users’ topic of interest, DiscipLink initiates exploratory questions from the perspectives of possible relevant fields of study, and users can further tailor these questions. DiscipLink then supports users in searching and screening papers under selected questions by automatically expanding queries with disciplinary-specific terminologies, extracting themes from retrieved papers, and highlighting the connections between papers and questions. Our evaluation, comprising a within-subject comparative experiment and an open-ended exploratory study, reveals that DiscipLink can effectively support researchers in breaking down disciplinary boundaries and integrating scattered knowledge in diverse fields. The findings underscore the potential of LLM-powered tools in fostering information-seeking practices and bolstering interdisciplinary research.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676366"}, {"primary_key": "711190", "vector": [], "sparse_vector": [], "title": "Data Pictorial: Deconstructing Raster Images for Data-Aware Animated Vector Posters.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "To support data integration into pictorials, we propose Data Pictorial, a pipeline that deconstructs a raster image into SVG objects whose attributes are contextualized in data. This process is achieved by cropping objects of interest using zero-shot detection, converting them into quantized bitmaps, and tracing the results as SVG paths. The technique then provides suggestions for binding the SVG objects and properties with data fields, affording the flexibility to automatically modify and animate the SVG based on the mapping. The resultant data-aware vector hypermedia can be potential candidates for real-time data inspection and personalization, all while maintaining the aesthetic of the original pictorial.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686353"}, {"primary_key": "711191", "vector": [], "sparse_vector": [], "title": "Improving Interface Design in Interactive Task Learning for Hierarchical Tasks based on a Qualitative Study.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Interactive Task Learning (ITL) systems acquire task knowledge from human instructions in natural language interaction. The interaction design of ITL agents for hierarchical tasks stays uncharted. This paper studied Verbal Apprentice Learner(VAL) for gaming, as an ITL example, and qualitatively analyzed the user study data to provide design insights on dialogue language types, task instruction strategies, and error handling. We then proposed an interface design: Editable Hierarchy Knowledge (EHK), as a generic probe for ITL systems for hierarchical tasks.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686326"}, {"primary_key": "711192", "vector": [], "sparse_vector": [], "title": "PortalInk: 2.5D Visual Storytelling with SVG Parallax and Waypoint Transitions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Efforts to expand the authoring of visual stories beyond the 2D canvas have commonly mapped flat imagery to 3D scenes or objects. This translation requires spatial reasoning, as artists must think in two spaces. We propose PortalInk 1, a tool for artists to craft and export 2.5D graphical stories while remaining in 2D space by using SVG transitions. This is achieved via a parallax effect that generates a sense of depth that can be further explored using pan and zoom interactions. Any canvas position can be saved and linked to in a closed drawn stroke, or “portal,” allowing the artist to create spatially discontinuous, or even infinitely looping visual trajectories. We provide three case studies and a gallery to demonstrate how artists can naturally incorporate these interactions to craft immersive comics, as well as re-purpose them to support use cases beyond drawing such as animation, slide-based presentations, web design, and digital journalism.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676376"}, {"primary_key": "711193", "vector": [], "sparse_vector": [], "title": "StyleFactory: Towards Better Style Alignment in Image Creation through Style-Strength-Based Control and Evaluation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Wei<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chenghao Pan", "<PERSON><PERSON> Liu", "Tianyu Lao", "<PERSON><PERSON>"], "summary": "Generative AI models have been widely used for image creation. However, generating images that are well-aligned with users’ personal styles on aesthetic features (e.g., color and texture) can be challenging due to the poor style expression and interpretation between humans and models. Through a formative study, we observed that participants showed a clear subjective perception of the desired style and variations in its strength, which directly inspired us to develop style-strength-based control and evaluation. Building on this, we present StyleFactory, an interactive system that helps users achieve style alignment. Our interface enables users to rank images based on their strengths in the desired style and visualizes the strength distribution of other images in that style from the model’s perspective. In this way, users can evaluate the understanding gap between themselves and the model, and define well-aligned personal styles for image creation through targeted iterations. Our technical evaluation and user study demonstrate that StyleFactory accurately generates images in specific styles, effectively facilitates style alignment in image creation workflow, stimulates creativity, and enhances the user experience in human-AI interactions.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676370"}, {"primary_key": "711194", "vector": [], "sparse_vector": [], "title": "PortaChrome: A Portable Contact Light Source for Integrated Re-Programmable Multi-Color Textures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present PortaChrome, a portable light source that can be attached to everyday objects to reprogram the color and texture of surfaces that come in contact with them. When PortaChrome makes contact with objects previously coated with photochromic dye, the UV and RGB LEDs inside PortaChrome create multi-color textures on the objects. In contrast to prior work, which used projectors for the color-change, PortaChrome has a thin and flexible form factor, which allows the color-change process to be integrated into everyday user interaction. Because of the close distance between the light source and the photochromic object, PortaChrome creates color textures in less than 4 minutes on average, which is 8 times faster than prior work. We demonstrate PortaChrome with four application examples, including data visualizations on textiles and dynamic designs on wearables.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676458"}, {"primary_key": "711195", "vector": [], "sparse_vector": [], "title": "Demo of PortaChrome: A Portable Contact Light Source for Integrated Re-Programmable Multi-Color Textures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this demo, we present PortaChrome, a portable light source that can be attached to everyday objects to reprogram the color of surfaces in contact with them. When PortaChrome makes contact with objects that were previously coated with photochromic dyes, the UV and RGBs LEDs inside PortaChrome create multi-color textures on the objects. In contrast to prior work, which used projectors for the color-change, PortaChrome has a thin and flexible form factor, which allows the color-change process to be integrated into daily user interactions. Because of the close distance between the light source and the photochromic object, PortaChrome creates color textures in less than 4 minutes on average, which is 8 times faster than prior work. We demonstrate PortaChrome with four application examples, including data visualizations on textiles and personalized wearables.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3672539.3686774"}, {"primary_key": "711196", "vector": [], "sparse_vector": [], "title": "Patterns of Hypertext-Augmented Sensemaking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The early days of HCI were marked by bold visions of hypertext as a transformative medium for augmented sensemaking, exemplified in systems like Memex, Xanadu, and NoteCards. Today, however, hypertext is often disconnected from discussions of the future of sensemaking. In this paper, we investigate how the recent resurgence in hypertext “tools for thought” might point to new directions for hypertext-augmented sensemaking. Drawing on detailed analyses of guided tours with 23 scholars, we describe hypertext-augmented use patterns for dealing with the core problem of revisiting and reusing existing/past ideas during scholarly sensemaking. We then discuss how these use patterns validate and extend existing knowledge of hypertext design patterns for sensemaking, and point to new design opportunities for augmented sensemaking.", "published": "2024-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3654777.3676338"}]