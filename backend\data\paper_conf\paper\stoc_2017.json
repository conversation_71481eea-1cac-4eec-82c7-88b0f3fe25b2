[{"primary_key": "3868185", "vector": [], "sparse_vector": [], "title": "A reverse Minkowski theorem.", "authors": ["<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "We prove a conjecture due to <PERSON><PERSON>, showing that if ℒ⊂ ℝn is a lattice such that det(ℒ′) 1 for all sublattices ℒ′ ⊆ ℒ, then", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055434"}, {"primary_key": "3868186", "vector": [], "sparse_vector": [], "title": "The computational complexity of ball permutations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We define several models of computation based on permuting distinguishable particles (which we call balls) and characterize their computational complexity. In the quantum setting, we use the representation theory of the symmetric group to find variants of this model which are intermediate between BPP and DQC1 (the class of problems solvable with one clean qubit) and between DQC1 and BQP. Furthermore, we consider a restricted version of this model based on an exactly solvable scattering problem of particles moving on a line. Despite the simplicity of this model from the perspective of mathematical physics, we show that if we allow intermediate destructive measurements and specific input states, then the model cannot be efficiently simulated classically up to multiplicative error unless the polynomial hierarchy collapses. Finally, we define a classical version of this model in which one can probabilistically permute balls. We find this yields a complexity class which is intermediate between L and BPP, and that a nondeterministic version of this model is NP-complete.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055453"}, {"primary_key": "3868187", "vector": [], "sparse_vector": [], "title": "Beating 1-1/e for ordered prophets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON> and <PERSON><PERSON> studied the prophet inequality on iid distributions [The Annals of Probability 1982]. They proved a theoretical bound of 1 - 1/e on the approximation factor of their algorithm. They conjectured that the best approximation factor for arbitrarily large n is 1/1+1/e ≃ 0.731. This conjecture remained open prior to this paper for over 30 years. In this paper we present a threshold-based algorithm for the prophet inequality with n iid distributions. Using a nontrivial and novel approach we show that our algorithm is a 0.738-approximation algorithm. By beating the bound of 1/1+1/e, this refutes the conjecture of <PERSON> and <PERSON>. Moreover, we generalize our results to non-uniform distributions and discuss its applications in mechanism design.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055479"}, {"primary_key": "3868188", "vector": [], "sparse_vector": [], "title": "Finding approximate local minima faster than gradient descent.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We design a non-convex second-order optimization algorithm that is guaranteed to return an approximate local minimum in time which scales linearly in the underlying dimension and the number of training examples. The time complexity of our algorithm to find an approximate local minimum is even faster than that of gradient descent to find a critical point. Our algorithm applies to a general class of optimization problems including training a neural network and other non-convex objectives arising in machine learning.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055464"}, {"primary_key": "3868189", "vector": [], "sparse_vector": [], "title": "Probabilistic rank and matrix rigidity.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We consider a notion of probabilistic rank and probabilistic sign-rank of a matrix, which measure the extent to which a matrix can be probabilistically represented by low-rank matrices. We demonstrate several connections with matrix rigidity, communication complexity, and circuit lower bounds. The most interesting outcomes are:", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055484"}, {"primary_key": "3868190", "vector": [], "sparse_vector": [], "title": "Quantum algorithm for tree size estimation, with applications to backtracking and 2-player games.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study quantum algorithms on search trees of unknown structure, in a model where the tree can be discovered by local exploration. That is, we are given the root of the tree and access to a black box which, given a vertex v, outputs the children of v.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055444"}, {"primary_key": "3868191", "vector": [], "sparse_vector": [], "title": "A generalization of permanent inequalities and applications in counting and optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A polynomial pΕℝ[z1,…,zn] is real stable if it has no roots in the upper-half complex plane. <PERSON><PERSON><PERSON><PERSON>'s permanent inequality gives a lower bound on the coefficient of the z1z2…zn monomial of a real stable polynomial p with nonnegative coefficients. This fundamental inequality has been used to attack several counting and optimization problems. Here, we study a more general question: Given a stable multilinear polynomial p with nonnegative coefficients and a set of monomials S, we show that if the polynomial obtained by summing up all monomials in S is real stable, then we can lower bound the sum of coefficients of monomials of p that are in S. We also prove generalizations of this theorem to (real stable) polynomials that are not multilinear. We use our theorem to give a new proof of <PERSON><PERSON><PERSON><PERSON><PERSON>'s inequality on the number of perfect matchings of a regular bipartite graph, generalize a recent result of <PERSON><PERSON><PERSON> and <PERSON>, and give deterministic polynomial time approximation algorithms for several counting problems.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055469"}, {"primary_key": "3868192", "vector": [], "sparse_vector": [], "title": "Approximate near neighbors for general symmetric norms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Aleksan<PERSON>", "Ilya <PERSON>", "<PERSON>"], "summary": "We show that every symmetric normed space admits an efficient nearest neighbor search data structure with doubly-logarithmic approximation. Specifically, for every n, d = no(1), and every d-dimensional symmetric norm ||·||, there exists a data structure for (loglogn)-approximate nearest neighbor search over ||·|| for n-point datasets achieving no(1) query time and n1+o(1) space. The main technical ingredient of the algorithm is a low-distortion embedding of a symmetric norm into a low-dimensional iterated product of top-k norms.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055418"}, {"primary_key": "3868193", "vector": [], "sparse_vector": [], "title": "Local max-cut in smoothed polynomial time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In 1988, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> wrote that \"Practically all the empirical evidence would lead us to conclude that finding locally optimal solutions is much easier than solving NP-hard problems\". Since then the empirical evidence has continued to amass, but formal proofs of this phenomenon have remained elusive. A canonical (and indeed complete) example is the local max-cut problem, for which no polynomial time method is known. In a breakthrough paper, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> proved that the smoothed complexity of local max-cut is quasi-polynomial, i.e., if arbitrary bounded weights are randomly perturbed, a local maximum can be found in ϕ nO(logn) steps where ϕ is an upper bound on the random edge weight density. In this paper we prove smoothed polynomial complexity for local max-cut, thus confirming that finding local optima for max-cut is much easier than solving it.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055402"}, {"primary_key": "3868194", "vector": [], "sparse_vector": [], "title": "Algorithms for stable and perturbation-resilient problems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the notion of stability and perturbation resilience introduced by <PERSON><PERSON><PERSON> and <PERSON> (2010) and <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (2012). A combinatorial optimization problem is α-stable or α-perturbation-resilient if the optimal solution does not change when we perturb all parameters of the problem by a factor of at most α. In this paper, we give improved algorithms for stable instances of various clustering and combinatorial optimization problems. We also prove several hardness results.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055487"}, {"primary_key": "3868195", "vector": [], "sparse_vector": [], "title": "Exponential separation of quantum communication and classical information.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We exhibit a Boolean function for which the quantum communication complexity is exponentially larger than the classical information complexity. An exponential separation in the other direction was already known from the work of <PERSON><PERSON><PERSON><PERSON> et. al. [SICOMP 44, pp. 1550-1572], hence our work implies that these two complexity measures are incomparable. As classical information complexity is an upper bound on quantum information complexity, which in turn is equal to amortized quantum communication complexity, our work implies that a tight direct sum result for distributional quantum communication complexity cannot hold. The function we use to present such a separation is the Symmetric k-ary Pointer Jumping function introduced by <PERSON> and <PERSON><PERSON> [ECCC TR15-057], whose classical communication complexity is exponentially larger than its classical information complexity. In this paper, we show that the quantum communication complexity of this function is polynomially equivalent to its classical communication complexity. The high-level idea behind our proof is arguably the simplest so far for such an exponential separation between information and communication, driven by a sequence of round-elimination arguments, allowing us to simplify further the approach of <PERSON> and <PERSON><PERSON>. As another application of the techniques that we develop, we give a simple proof for an optimal trade-off between <PERSON>'s and <PERSON>'s communication while computing the related Greater-Than function on n bits: say <PERSON> communicates at most b bits, then <PERSON> must send n/exp(O(b)) bits to <PERSON>. This holds even when allowing pre-shared entanglement. We also present a classical protocol achieving this bound.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055401"}, {"primary_key": "3868196", "vector": [], "sparse_vector": [], "title": "Provable learning of noisy-OR networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Ge", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many machine learning applications use latent variable models to explain structure in data, whereby visible variables (= coordinates of the given datapoint) are explained as a probabilistic function of some hidden variables. Learning the model ---that is, the mapping from hidden variables to visible ones and vice versa---is NP-hard even in very simple settings. In recent years, provably efficient algorithms were nevertheless developed for models with linear structure: topic models, mixture models, hidden markov models, etc. These algorithms use matrix or tensor decomposition, and make some reasonable assumptions about the parameters of the underlying model.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055482"}, {"primary_key": "3868197", "vector": [], "sparse_vector": [], "title": "A strongly polynomial algorithm for bimodular integer linear programming.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a strongly polynomial algorithm to solve integer programs of the form max{cT x: Ax≤ b, xεℤn }, for AεℤmXn with rank(A)=n, bε≤m, cε≤n, and where all determinants of (nXn)-sub-matrices of A are bounded by 2 in absolute value. In particular, this implies that integer programs max{cT x : Q x≤ b, xεℤ≥0n}, where Qε ℤmXn has the property that all subdeterminants are bounded by 2 in absolute value, can be solved in strongly polynomial time. We thus obtain an extension of the well-known result that integer programs with constraint matrices that are totally unimodular are solvable in strongly polynomial time.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055473"}, {"primary_key": "3868198", "vector": [], "sparse_vector": [], "title": "Randomized polynomial time identity testing for noncommutative circuits.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we show that black-box polynomial identity testing for noncommutative polynomials f∈𝔽⟨z1,z2,…,zn⟩ of degree D and sparsity t, can be done in randomized (n,logt,logD) time. As a consequence, given a circuit C of size s computing a polynomial f∈𝔽⟨ z1,z2,…,zn⟩ with at most t non-zero monomials, then testing if f is identically zero can be done by a randomized algorithm with running time polynomial in s and n and logt. This makes significant progress on a question that has been open for over ten years. Our algorithm is based on automata-theoretic ideas that can efficiently isolate a monomial in the given polynomial. In particular, we carry out the monomial isolation using nondeterministic automata.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055442"}, {"primary_key": "3868199", "vector": [], "sparse_vector": [], "title": "Online service with delay.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Ge", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we introduce the online service with delay problem. In this problem, there are n points in a metric space that issue service requests over time, and a server that serves these requests. The goal is to minimize the sum of distance traveled by the server and the total delay (or a penalty function thereof) in serving the requests. This problem models the fundamental tradeoff between batching requests to improve locality and reducing delay to improve response time, that has many applications in operations management, operating systems, logistics, supply chain management, and scheduling.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055475"}, {"primary_key": "3868200", "vector": [], "sparse_vector": [], "title": "The menu-size complexity of revenue approximation.", "authors": ["<PERSON><PERSON>", "Yannai <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider a monopolist that is selling n items to a single additive buyer, where the buyer's values for the items are drawn according to independent distributions F1,F2,…,Fn that possibly have unbounded support. It is well known that - unlike in the single item case - the revenue-optimal auction (a pricing scheme) may be complex, sometimes requiring a continuum of menu entries. It is also known that simple auctions with a finite bounded number of menu entries can extract a constant fraction of the optimal revenue. Nonetheless, the question of the possibility of extracting an arbitrarily high fraction of the optimal revenue via a finite menu size remained open.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055426"}, {"primary_key": "3868201", "vector": [], "sparse_vector": [], "title": "Communication complexity of approximate Nash equilibria.", "authors": ["<PERSON><PERSON>", "<PERSON>via<PERSON>"], "summary": "For a constant ϵ, we prove a (N) lower bound on the (randomized) communication complexity of ϵ-Nash equilibrium in two-player N x N games. For n-player binary-action games we prove an exp(n) lower bound for the (randomized) communication complexity of (ϵ,ϵ)-weak approximate Nash equilibrium, which is a profile of mixed actions such that at least (1-ϵ)-fraction of the players are ϵ-best replying.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055407"}, {"primary_key": "3868202", "vector": [], "sparse_vector": [], "title": "The limitations of optimization from samples.", "authors": ["<PERSON>", "<PERSON>via<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we consider the following question: can we optimize objective functions from the training data we use to learn them? We formalize this question through a novel framework we call optimization from samples (OPS). In OPS, we are given sampled values of a function drawn from some distribution and the objective is to optimize the function under some constraint.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055406"}, {"primary_key": "3868203", "vector": [], "sparse_vector": [], "title": "Average-case fine-grained hardness.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present functions that can be computed in some fixed polynomial time but are hard on average for any algorithm that runs in slightly smaller time, assuming widely-conjectured worst-case hardness for problems from the study of fine-grained complexity. Unconditional constructions of such functions are known from before (<PERSON><PERSON> et al., IPL '94), but these have been canonical functions that have not found further use, while our functions are closely related to well-studied problems and have considerable algebraic structure.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055466"}, {"primary_key": "3868204", "vector": [], "sparse_vector": [], "title": "Algorithmic discrepancy beyond partial coloring.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Garg"], "summary": "The partial coloring method is one of the most powerful and widely used method in combinatorial discrepancy problems. However, in many cases it leads to sub-optimal bounds as the partial coloring step must be iterated a logarithmic number of times, and the errors can add up in an adversarial way.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055490"}, {"primary_key": "3868205", "vector": [], "sparse_vector": [], "title": "Faster space-efficient algorithms for subset sum and k-sum.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Garg", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present randomized algorithms that solve Subset Sum and Knapsack instances with n items in O*(20.86n) time, where the O*(·) notation suppresses factors polynomial in the input size, and polynomial space, assuming random read-only access to exponentially many random bits. These results can be extended to solve Binary Linear Programming on n variables with few constraints in a similar running time. We also show that for any constant k≥ 2, random instances of k-Sum can be solved using O(nk-0.5(n)) time and O(logn) space, without the assumption of random access to random bits.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055467"}, {"primary_key": "3868206", "vector": [], "sparse_vector": [], "title": "Quantum entanglement, sum of squares, and the log rank conjecture.", "authors": ["<PERSON>az <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For every constant ε>0, we give an exp(Õ(∞n))-time algorithm for the 1 vs 1 - ε Best Separable State (BSS) problem of distinguishing, given an n2 x n2 matrix ℳ corresponding to a quantum measurement, between the case that there is a separable (i.e., non-entangled) state ρ that ℳ accepts with probability 1, and the case that every separable state is accepted with probability at most 1 - ε. Equivalently, our algorithm takes the description of a subspace 𝒲 ⊆ 𝔽n2 (where 𝔽 can be either the real or complex field) and distinguishes between the case that contains a rank one matrix, and the case that every rank one matrix is at least ε far (in 𝓁2 distance) from 𝒲.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055488"}, {"primary_key": "3868207", "vector": [], "sparse_vector": [], "title": "Hardness amplification for entangled games via anchoring.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the parallel repetition of one-round games involving players that can use quantum entanglement. A major open question in this area is whether parallel repetition reduces the entangled value of a game at an exponential rate - in other words, does an analogue of <PERSON><PERSON>'s parallel repetition theorem hold for games with players sharing quantum entanglement? Previous results only apply to special classes of games.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055433"}, {"primary_key": "3868208", "vector": [], "sparse_vector": [], "title": "An efficient reduction from two-source to non-malleable extractors: achieving near-logarithmic min-entropy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The breakthrough result of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2016) gives a reduction from the construction of explicit two-source extractors to the construction of explicit non-malleable extractors. However, even assuming the existence of optimal explicit non-malleable extractors only gives a two-source extractor (or a Ramsey graph) for poly(logn) entropy, rather than the optimal O(logn).", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055423"}, {"primary_key": "3868209", "vector": [], "sparse_vector": [], "title": "Streaming symmetric norms via measure concentration.", "authors": ["Jaroslaw Blasiok", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We characterize the streaming space complexity of every symmetric norm l (a norm on ℝn invariant under sign-flips and coordinate-permutations), by relating this space complexity to the measure-concentration characteristics of l. Specifically, we provide nearly matching upper and lower bounds on the space complexity of calculating a (1 ± ε)-approximation to the norm of the stream, for every 0 < ε ≤ 1/2. (The bounds match up to (ε-1 logn) factors.) We further extend those bounds to any large approximation ratio D≥ 1.1, showing that the decrease in space complexity is proportional to D2, and that this factor the best possible. All of the bounds depend on the median of l(x) when x is drawn uniformly from the l2 unit sphere. The same median governs many phenomena in high-dimensional spaces, such as large-deviation bounds and the critical dimension in <PERSON><PERSON><PERSON><PERSON><PERSON>'s Theorem.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055424"}, {"primary_key": "3868210", "vector": [], "sparse_vector": [], "title": "Non-interactive delegation and batch NP verification from standard computational assumptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present an adaptive and non-interactive protocol for verifying arbitrary efficient computations in fixed polynomial time. Our protocol is computationally sound and can be based on any computational PIR scheme, which in turn can be based on standard polynomial-time cryptographic assumptions (e.g. the worst case hardness of polynomial-factor approximation of short-vector lattice problems). In our protocol, the verifier sets up a public key ahead of time, and this key can be used by any prover to prove arbitrary statements by simpling sending a proof to the verifier. Verification is done using a secret verification key, and soundness relies on this key not being known to the prover. Our protocol further allows to prove statements about computations of arbitrary RAM machines.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055497"}, {"primary_key": "3868211", "vector": [], "sparse_vector": [], "title": "Kernel-based methods for bandit convex optimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the adversarial convex bandit problem and we build the first poly(T)-time algorithm with poly(n) √T-regret for this problem. To do so we introduce three new ideas in the derivative-free optimization", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055403"}, {"primary_key": "3868212", "vector": [], "sparse_vector": [], "title": "Holographic algorithm with matchgates is universal for planar #CSP over boolean domain.", "authors": ["<PERSON><PERSON><PERSON>", "Zhiguo Fu"], "summary": "We prove a complexity classification theorem that classifies all counting constraint satisfaction problems (#CSP) over Boolean variables into exactly three classes: (1) Polynomial-time solvable; (2) #P-hard for general instances, but solvable in polynomial-time over planar structures; and (3) #P-hard over planar structures. The classification applies to all finite sets of complex-valued, not necessarily symmetric, constraint functions on Boolean variables. It is shown that <PERSON><PERSON>'s holographic algorithm with matchgates is universal strategy for all problems in class (2).", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055405"}, {"primary_key": "3868213", "vector": [], "sparse_vector": [], "title": "Simple mechanisms for subadditive buyers via duality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We provide simple and approximately revenue-optimal mechanisms in the multi-item multi-bidder settings. We unify and improve all previous results, as well as generalize the results to broader cases. In particular, we prove that the better of the following two simple, deterministic and Dominant Strategy Incentive Compatible mechanisms, a sequential posted price mechanism or an anonymous sequential posted price mechanism with entry fee, achieves a constant fraction of the optimal revenue among all randomized, Bayesian Incentive Compatible mechanisms, when buyers' valuations are XOS over independent items. If the buyers' valuations are subadditive over independent items, the approximation factor degrades to O(logm), where m is the number of items. We obtain our results by first extending the Cai-Devanur-<PERSON>nberg duality framework to derive an effective benchmark of the optimal revenue for subadditive bidders, and then analyzing this upper bound with new techniques.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055465"}, {"primary_key": "3868214", "vector": [], "sparse_vector": [], "title": "Deciding parity games in quasipolynomial time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is shown that the parity game can be solved in quasipolynomial time. The parameterised parity game - with n nodes and m distinct values (aka colours or priorities) - is proven to be in the class of fixed parameter tractable (FPT) problems when parameterised over m. Both results improve known bounds, from runtime nO(√n) to O(nlog(m)+6) and from an XP-algorithm with runtime O(nΘ(m)) for fixed parameter m to an FPT-algorithm with runtime O(n5)+g(m), for some function g depending on m only. As an application it is proven that coloured Muller games with n nodes and m colours can be decided in time O((mm · n)5); it is also shown that this bound cannot be improved to O((2m · n)c), for any c, unless FPT = W[1].", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055409"}, {"primary_key": "3868215", "vector": [], "sparse_vector": [], "title": "Equivocating Yao: constant-round adaptively secure multiparty computation in the plain model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>'s circuit garbling scheme is one of the basic building blocks of cryptographic protocol design. Originally designed to enable two-message, two-party secure computation, the scheme has been extended in many ways and has innumerable applications. Still, a basic question has remained open throughout the years: Can the scheme be extended to guarantee security in the face of an adversary that corrupts both parties, adaptively, as the computation proceeds?", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055495"}, {"primary_key": "3868216", "vector": [], "sparse_vector": [], "title": "An adaptive sublinear-time block sparse fourier transform.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The problem of approximately computing the k dominant Fourier coefficients of a vector X quickly, and using few samples in time domain, is known as the Sparse Fourier Transform (sparse FFT) problem. A long line of work on the sparse FFT has resulted in algorithms with O(klognlog(n/k)) runtime [<PERSON><PERSON><PERSON> et al., STOC'12] and O(klogn) sample complexity [<PERSON><PERSON> et al., FOCS'14]. This paper revisits the sparse FFT problem with the added twist that the sparse coefficients approximately obey a (k0,k1)-block sparse model. In this model, signal frequencies are clustered in k0 intervals with width k1 in Fourier space, and k= k0k1 is the total sparsity.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055462"}, {"primary_key": "3868217", "vector": [], "sparse_vector": [], "title": "Subquadratic submodular function minimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Submodular function minimization (SFM) is a fundamental discrete optimization problem which generalizes many well known problems, has applications in various fields, and can be solved in polynomial time. Owing to applications in computer vision and machine learning, fast SFM algorithms are highly desirable. The current fastest algorithms [<PERSON>, <PERSON>, <PERSON>, 2015] run in O(n2lognM· EO + n3logO(1)nM) time and O(n3log2n· EO +n4logO(1)n)time respectively, where M is the largest absolute value of the function (assuming the range is integers) and is the time taken to evaluate the function on any set. Although the best known lower bound on the query complexity is only Ω(n) [<PERSON>, 2008], the current shortest non-deterministic proof [<PERSON>, 1985] certifying the optimum value of a function requires Ω(n2) function evaluations.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055419"}, {"primary_key": "3868218", "vector": [], "sparse_vector": [], "title": "Exponential separations in the energy complexity of leader election.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Energy is often the most constrained resource for battery-powered wireless devices and the lion's share of energy is often spent on transceiver usage (sending/receiving packets), not on computation. In this paper we study the energy complexity of Leader Election and Approximate Counting in several models of wireless radio networks. It turns out that energy complexity is very sensitive to whether the devices can generate random bits and their ability to detect collisions. We consider four collision-detection models: Strong-CD (in which transmitters and listeners detect collisions), Sender-CD and Receiver-CD (in which only transmitters or only listeners detect collisions), and No-CD (in which no one detects collisions.)", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055481"}, {"primary_key": "3868219", "vector": [], "sparse_vector": [], "title": "Learning from untrusted data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The vast majority of theoretical results in machine learning and statistics assume that the training data is a reliable reflection of the phenomena to be learned. Similarly, most learning techniques used in practice are brittle to the presence of large amounts of biased or malicious data. Motivated by this, we consider two frameworks for studying estimation, learning, and optimization in the presence of significant fractions of arbitrary data.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055491"}, {"primary_key": "3868220", "vector": [], "sparse_vector": [], "title": "Non-malleable codes and extractors for small-depth circuits, and affine functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Non-malleable codes were introduced by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> as an elegant relaxation of error correcting codes, where the motivation is to handle more general forms of tampering while still providing meaningful guarantees. This has led to many elegant constructions and applications in cryptography. However, most works so far only studied tampering in the split-state model where different parts of the codeword are tampered independently, and thus do not apply to many other natural classes of tampering functions. The only exceptions are the work of <PERSON><PERSON><PERSON> et al. which studied non-malleable codes against bit permutation composed with bit-wise tampering, and the works of <PERSON> et al. and <PERSON> et al., which studied non-malleable codes against local functions. However, in both cases each tampered bit only depends on a subset of input bits.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055483"}, {"primary_key": "3868221", "vector": [], "sparse_vector": [], "title": "Stability of service under time-of-use pricing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Balasubramanian <PERSON>"], "summary": "We consider time-of-use pricing as a technique for matching supply and demand of temporal resources with the goal of maximizing social welfare. Relevant examples include energy, computing resources on a cloud computing platform, and charging stations for electric vehicles, among many others. A client/job in this setting has a window of time during which he needs service, and a particular value for obtaining it. We assume a stochastic model for demand, where each job materializes with some probability via an independent <PERSON><PERSON><PERSON> trial. Given a per-time-unit pricing of resources, any realized job will first try to get served by the cheapest available resource in its window and, failing that, will try to find service at the next cheapest available resource, and so on. Thus, the natural stochastic fluctuations in demand have the potential to lead to cascading overload events. Our main result shows that setting prices so as to optimally handle the expected demand works well: with high probability, when the actual demand is instantiated, the system is stable and the expected value of the jobs served is very close to that of the optimal offline algorithm.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055455"}, {"primary_key": "3868222", "vector": [], "sparse_vector": [], "title": "Addition is exponentially harder than counting for shallow monotone circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Let Addk,N denote the Boolean function which takes as input k strings of N bits each, representing k numbers a(1),…,a(k) in {0,1,…,2N-1}, and outputs 1 if and only if a(1) + … + a(k) ≥ 2N. Let MAJt,n denote a monotone unweighted threshold gate, i.e., the Boolean function which takes as input a single string x Ε {0,1}n and outputs 1 if and only if x1 + … + xn ≥ t. The function Addk,N may be viewed as a monotone function that performs addition, and MAJt,n may be viewed as a monotone gate that performs counting. We refer to circuits that are composed of MAJ gates as monotone majority circuits.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055425"}, {"primary_key": "3868223", "vector": [], "sparse_vector": [], "title": "Beyond Talagrand functions: new lower bounds for testing monotonicity and unateness.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove a lower bound of Ω(n1/3) for the query complexity of any two-sided and adaptive algorithm that tests whether an unknown Boolean function f:{0,1}n→ {0,1} is monotone versus far from monotone. This improves the recent lower bound of Ω(n1/4) for the same problem by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC'16). Our result builds on a new family of random Boolean functions that can be viewed as a two-level extension of <PERSON><PERSON><PERSON>'s random DNFs.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055461"}, {"primary_key": "3868224", "vector": [], "sparse_vector": [], "title": "Set similarity search beyond MinHash.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of approximate set similarity search under Braun-<PERSON><PERSON><PERSON> similarity B(x, y) = |x ∩ y| / max(|x|, |y|). The (b1, b2)-approximate Braun-Blanquet similarity search problem is to preprocess a collection of sets P such that, given a query set q, if there exists x Ε P with B(q, x) ≥ b1, then we can efficiently return x′ Ε P with B(q, x′) > b2.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055443"}, {"primary_key": "3868225", "vector": [], "sparse_vector": [], "title": "New hardness results for routing on disjoint paths.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the classical Node-Disjoint Paths (NDP) problem, the input consists of an undirected n-vertex graph G, and a collection M={(s1,t1),…,(sk,tk)} of pairs of its vertices, called source-destination, or demand, pairs. The goal is to route the largest possible number of the demand pairs via node-disjoint paths. The best current approximation for the problem is achieved by a simple greedy algorithm, whose approximation factor is O(√n), while the best current negative result is an Ω(log1/2-δn)-hardness of approximation for any constant δ, under standard complexity assumptions. Even seemingly simple special cases of the problem are still poorly understood: when the input graph is a grid, the best current algorithm achieves an Õ(n1/4)-approximation, and when it is a general planar graph, the best current approximation ratio of an efficient algorithm is Õ(n9/19). The best currently known lower bound for both these versions of the problem is APX-hardness.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055411"}, {"primary_key": "3868226", "vector": [], "sparse_vector": [], "title": "Towards optimal two-source extractors and Ramsey graphs.", "authors": ["<PERSON>"], "summary": "The main contribution of this work is a construction of a two-source extractor for quasi-logarithmic min-entropy. That is, an extractor for two independent n-bit sources with min-entropy Ο(logn), which is optimal up to the poly(loglogn) factor. A strong motivation for constructing two-source extractors for low entropy is for Ramsey graphs constructions. Our two-source extractor readily yields a (logn)(logloglogn)Ο(1)-Ramsey graph on n vertices.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055429"}, {"primary_key": "3868227", "vector": [], "sparse_vector": [], "title": "Almost-linear-time algorithms for Markov chains and new spectral primitives for directed graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we begin to address the longstanding algorithmic gap between general and reversible Markov chains. We develop directed analogues of several spectral graph-theoretic tools that had previously been available only in the undirected setting, and for which it was not clear that directed versions even existed. In particular, we provide a notion of approximation for directed graphs, prove sparsifiers under this notion always exist, and show how to construct them in almost linear time. Using this notion of approximation, we design the first almost-linear-time directed Laplacian system solver, and, by leveraging the recent framework of [<PERSON><PERSON>, FOCS '16], we also obtain almost-linear-time algorithms for computing the stationary distribution of a Markov chain, computing expected commute times in a directed graph, and more. For each problem, our algorithms improve the previous best running times of O((nm3/4 + n2/3 m) logO(1) (n κ ε-1)) to O((m + n2O(√lognloglogn)) logO(1) (n κε-1)) where n is the number of vertices in the graph, m is the number of edges, κ is a natural condition number associated with the problem, and ε is the desired accuracy. We hope these results open the door for further studies into directed spectral graph theory, and that they will serve as a stepping stone for designing a new generation of fast algorithms for directed graphs.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055463"}, {"primary_key": "3868228", "vector": [], "sparse_vector": [], "title": "Information-theoretic thresholds from the cavity method.", "authors": ["<PERSON><PERSON>", "Flore<PERSON>", "<PERSON>", "Lenka <PERSON>boro<PERSON>"], "summary": "Vindicating a sophisticated but non-rigorous physics approach called the cavity method, we establish a formula for the mutual information in statistical inference problems induced by random graphs. This general result implies the conjecture on the information-theoretic threshold in the disassortative stochastic block model [<PERSON>elle et al.: Phys. Rev. E (2011)] and allows us to pinpoint the exact condensation phase transition in random constraint satisfaction problems such as random graph coloring, thereby proving a conjecture from [<PERSON><PERSON><PERSON><PERSON> et al.: PNAS (2007)]. As a further application we establish the formula for the mutual information in Low-Density Generator Matrix codes as conjectured in [Montanari: IEEE Transactions on Information Theory (2005)]. The proofs provide a conceptual underpinning of the replica symmetric variant of the cavity method, and we expect that the approach will find many future applications.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055420"}, {"primary_key": "3868229", "vector": [], "sparse_vector": [], "title": "Homomorphisms are a good basis for counting small subgraphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce graph motif parameters, a class of graph parameters that depend only on the frequencies of constant-size induced subgraphs. Classical works by <PERSON><PERSON>\\'asz show that many interesting quantities have this form, including, for fixed graphs $H$, the number of $H$-copies (induced or not) in an input graph $G$, and the number of homomorphisms from $H$ to $G$. Using the framework of graph motif parameters, we obtain faster algorithms for counting subgraph copies of fixed graphs $H$ in host graphs $G$: For graphs $H$ on $k$ edges, we show how to count subgraph copies of $H$ in time $k^{O(k)}\\cdot n^{0.174k + o(k)}$ by a surprisingly simple algorithm. This improves upon previously known running times, such as $O(n^{0.91k + c})$ time for $k$-edge matchings or $O(n^{0.46k + c})$ time for $k$-cycles. Furthermore, we prove a general complexity dichotomy for evaluating graph motif parameters: Given a class $\\mathcal C$ of such parameters, we consider the problem of evaluating $f\\in \\mathcal C$ on input graphs $G$, parameterized by the number of induced subgraphs that $f$ depends upon. For every recursively enumerable class $\\mathcal C$, we prove the above problem to be either FPT or #W[1]-hard, with an explicit dichotomy criterion. This allows us to recover known dichotomies for counting subgraphs, induced subgraphs, and homomorphisms in a uniform and simplified way, together with improved lower bounds. Finally, we extend graph motif parameters to colored subgraphs and prove a complexity trichotomy: For vertex-colored graphs $H$ and $G$, where $H$ is from a fixed class $\\mathcal H$, we want to count color-preserving $H$-copies in $G$. We show that this problem is either polynomial-time solvable or FPT or #W[1]-hard, and that the FPT cases indeed need FPT time under reasonable assumptions.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055502"}, {"primary_key": "3868230", "vector": [], "sparse_vector": [], "title": "Twenty (simple) questions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A basic combinatorial interpretation of <PERSON>'s entropy function is via the \"20 questions\" game. This cooperative game is played by two players, <PERSON> and <PERSON>: <PERSON> picks a distribution Π over the numbers {1,…,n}, and announces it to <PERSON>. She then chooses a number x according to Π, and <PERSON> attempts to identify x using as few Yes/No queries as possible, on average.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055422"}, {"primary_key": "3868231", "vector": [], "sparse_vector": [], "title": "Finding even cycles faster via capped k-walks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>l"], "summary": "Finding cycles in graphs is a fundamental problem in algorithmic graph theory. In this paper, we consider the problem of finding and reporting a cycle of length 2k in an undirected graph G with n nodes and m edges for constant k≥ 2. A classic result by <PERSON><PERSON> and <PERSON> [J. Combinatorial Theory, 1974] implies that if m ≥ 100k n1+1/k, then G contains a 2k-cycle, further implying that one needs to consider only graphs with m = O(n1+1/k).", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055459"}, {"primary_key": "3868232", "vector": [], "sparse_vector": [], "title": "Optimal mean-based algorithms for trace reconstruction.", "authors": ["<PERSON><PERSON><PERSON>", "Ryan <PERSON>&<PERSON>;Donnell", "<PERSON><PERSON>"], "summary": "In the (deletion-channel) trace reconstruction problem, there is an unknown n-bit source string x. An algorithm is given access to independent traces of x, where a trace is formed by deleting each bit of x independently with probability δ. The goal of the algorithm is to recover x exactly (with high probability), while minimizing samples (number of traces) and running time.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055450"}, {"primary_key": "3868233", "vector": [], "sparse_vector": [], "title": "Bernoulli factories and black-box reductions in mechanism design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We provide a polynomial-time reduction from Bayesian incentive-compatible mechanism design to Bayesian algorithm design for welfare maximization problems. Unlike prior results, our reduction achieves exact incentive compatibility for problems with multi-dimensional and continuous type spaces.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055492"}, {"primary_key": "3868234", "vector": [], "sparse_vector": [], "title": "Sampling random spanning trees faster than matrix multiplication.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present an algorithm that, with high probability, generates a random spanning tree from an edge-weighted undirected graph in (n5/3 m1/3) time. The tree is sampled from a distribution where the probability of each tree is proportional to the product of its edge weights. This improves upon the previous best algorithm due to <PERSON><PERSON><PERSON> et al. that runs in matrix multiplication time, O(nω). For the special case of unweighted graphs, this improves upon the best previously known running time of Õ(min{nω,m√n,m4/3}) for m ⪢ n7/4 (<PERSON><PERSON><PERSON> et al. '96, <PERSON><PERSON><PERSON><PERSON> '09, <PERSON><PERSON> et al. '15).", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055499"}, {"primary_key": "3868235", "vector": [], "sparse_vector": [], "title": "DecreaseKeys are expensive for external memory priority queues.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Huacheng Yu"], "summary": "One of the biggest open problems in external memory data structures is the priority queue problem with DecreaseKey operations. If only Insert and ExtractMin operations need to be supported, one can design a comparison-based priority queue performing O((N/B) lgM/B N) I/Os over a sequence of N operations, where B is the disk block size in number of words and M is the main memory size in number of words. This matches the lower bound for comparison-based sorting and is hence optimal for comparison-based priority queues. However, if we also need to support DecreaseKeys, the performance of the best known priority queue is only O((N/B) lg2 N) I/Os. The big open question is whether a degradation in performance really is necessary. We answer this question affirmatively by proving a lower bound of Ω((N/B) lglgN B) I/Os for processing a sequence of N intermixed Insert, ExtraxtMin and DecreaseKey operations. Our lower bound is proved in the cell probe model and thus holds also for non-comparison-based priority queues.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055437"}, {"primary_key": "3868236", "vector": [], "sparse_vector": [], "title": "Distributed exact shortest paths in sublinear time.", "authors": ["<PERSON>"], "summary": "The distributed single-source shortest paths problem is one of the most fundamental and central problems in the message-passing distributed computing. Classical Bellman-Ford algorithm solves it in O(n) time, where n is the number of vertices in the input graph G. <PERSON><PERSON><PERSON> and <PERSON>, FOCS'99, showed a lower bound of Ω(D + √n) for this problem, where D is the hop-diameter of G.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055452"}, {"primary_key": "3868237", "vector": [], "sparse_vector": [], "title": "How well do local algorithms solve semidefinite programs?", "authors": ["<PERSON>", "<PERSON>"], "summary": "Several probabilistic models from high-dimensional statistics and machine learning reveal an intriguing and yet poorly understood dichotomy. Either simple local algorithms succeed in estimating the object of interest, or even sophisticated semi-definite programming (SDP) relaxations fail. In order to explore this phenomenon, we study a classical SDP relaxation of the minimum graph bisection problem, when applied to <PERSON><PERSON><PERSON><PERSON> random graphs with bounded average degree d > 1, and obtain several types of results. First, we use a dual witness construction (using the so-called non-backtracking matrix of the graph) to upper bound the SDP value. Second, we prove that a simple local algorithm approximately solves the SDP to within a factor 2d^2/(2d^2 + d - 1) of the upper bound. In particular, the local algorithm is at most 8/9 suboptimal, and 1 + O(d^-1) suboptimal for large degree.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055451"}, {"primary_key": "3868238", "vector": [], "sparse_vector": [], "title": "Approximate modularity revisited.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>-<PERSON>"], "summary": "Set functions with convenient properties (such as submodularity) appear in application areas of current interest, such as algorithmic game theory, and allow for improved optimization algorithms. It is natural to ask (e.g., in the context of data driven optimization) how robust such properties are, and whether small deviations from them can be tolerated. We consider two such questions in the important special case of linear set functions.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055476"}, {"primary_key": "3868239", "vector": [], "sparse_vector": [], "title": "Succinct hitting sets and barriers to proving algebraic circuits lower bounds.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We formalize a framework of algebraically natural lower bounds for algebraic circuits. Just as with the natural proofs notion of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> for boolean circuit lower bounds, our notion of algebraically natural lower bounds captures nearly all lower bound techniques known. However, unlike the boolean setting, there has been no concrete evidence demonstrating that this is a barrier to obtaining super-polynomial lower bounds for general algebraic circuits, as there is little understanding whether algebraic circuits are expressive enough to support \"cryptography\" secure against algebraic circuits.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055496"}, {"primary_key": "3868240", "vector": [], "sparse_vector": [], "title": "The next 700 network programming languages (invited talk).", "authors": ["<PERSON>"], "summary": "Specification and verification of computer networks has become a reality in recent years, with the emergence of domain-specific programming languages and automated reasoning tools. But the design of these frameworks has been largely ad hoc, driven more by the needs of applications and the capabilities of hardware than by any foundational principles. This talk will present NetKAT, a language for programming networks based on a well-studied mathematical foundation: regular languages and finite automata. The talk will describe the design of the language, discuss its semantic underpinnings, and present highlights from ongoing work extending the language with stateful and probabilistic features.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3081042"}, {"primary_key": "3868241", "vector": [], "sparse_vector": [], "title": "Algorithmic and optimization aspects of Brascamp-Lieb inequalities, via operator scaling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The celebrated Brascamp-Lieb (BL) inequalities [BL76, Lie90], and their reverse form of <PERSON><PERSON> [Bar98], are an important mathematical tool, unifying and generalizing numerous in- equalities in analysis, convex geometry and information theory, with many used in computer science. While their structural theory is very well understood, far less is known about computing their main parameters below (which we later define). Prior to this work, the best known algorithms for any of these optimization tasks required at least exponential time. In this work, we give polynomial time algorithms to compute:", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055458"}, {"primary_key": "3868242", "vector": [], "sparse_vector": [], "title": "Settling the complexity of Leontief and PLC exchange markets under exact and approximate equilibria.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Our first result shows membership in PPAD for the problem of computing approximate equilibria for an Arrow-Debreu exchange market for piecewise-linear concave (PLC) utility functions. As a corollary we also obtain membership in PPAD for Leontief utility functions. This settles an open question of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (2011).", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055474"}, {"primary_key": "3868243", "vector": [], "sparse_vector": [], "title": "On the complexity of local distributed graph problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper is centered on the complexity of graph problems in the well-studied LOCAL model of distributed computing, introduced by <PERSON><PERSON> [FOCS '87]. It is widely known that for many of the classic distributed graph problems (including maximal independent set (MIS) and (Δ+1)-vertex coloring), the randomized complexity is at most polylogarithmic in the size n of the network, while the best deterministic complexity is typically 2O(√logn). Understanding and potentially narrowing down this exponential gap is considered to be one of the central long-standing open questions in the area of distributed graph algorithms.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055471"}, {"primary_key": "3868244", "vector": [], "sparse_vector": [], "title": "Removal lemmas with polynomial bounds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give new sufficient and necessary criteria guaranteeing that", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055404"}, {"primary_key": "3868245", "vector": [], "sparse_vector": [], "title": "Efficient empirical revenue maximization in single-parameter auction environments.", "authors": ["Yannai <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a polynomial-time algorithm that, given samples from the unknown valuation distribution of each bidder, learns an auction that approximately maximizes the auctioneer's revenue in a variety of single-parameter auction environments including matroid environments, position environments, and the public project environment. The valuation distributions may be arbitrary bounded distributions (in particular, they may be irregular, and may differ for the various bidders), thus resolving a problem left open by previous papers. The analysis uses basic tools, is performed in its entirety in value-space, and simplifies the analysis of previously known results for special cases. Furthermore, the analysis extends to certain single-parameter auction environments where precise revenue maximization is known to be intractable, such as knapsack environments.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055427"}, {"primary_key": "3868246", "vector": [], "sparse_vector": [], "title": "Surviving in directed graphs: a quasi-polynomial-time polylogarithmic approximation for two-connected directed Steiner tree.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Bundit Laekhanukit"], "summary": "Real-word networks are often prone to failures. A reliable network needs to cope with this situation and must provide a backup communication channel. This motivates the study of survivable network design, which has been a focus of research for a few decades. To date, survivable network design problems on undirected graphs are well-understood. For example, there is a 2 approximation in the case of edge failures [<PERSON>, FOCS'98/Combinatorica'01]. The problems on directed graphs, in contrast, have seen very little progress. Most techniques for the undirected case like primal-dual and iterative rounding methods do not seem to extend to the directed case. Almost no non-trivial approximation algorithm is known even for a simple case where we wish to design a network that tolerates a single failure.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055445"}, {"primary_key": "3868247", "vector": [], "sparse_vector": [], "title": "Uniform sampling through the Lovasz local lemma.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Jingcheng Liu"], "summary": "We propose a new algorithmic framework, called \"partial rejection sampling\", to draw samples exactly from a product distribution, conditioned on none of a number of bad events occurring. Our framework builds (perhaps surprising) new connections between the variable framework of the Lovász Local Lemma and some clas- sical sampling algorithms such as the \"cycle-popping\" algorithm for rooted spanning trees by <PERSON>. Among other applications, we discover new algorithms to sample satisfying assignments of k-CNF formulas with bounded variable occurrences.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055410"}, {"primary_key": "3868248", "vector": [], "sparse_vector": [], "title": "Online and dynamic algorithms for set cover.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we give new results for the set cover problem in the fully dynamic model. In this model, the set of \"active\" elements to be covered changes over time. The goal is to maintain a near-optimal solution for the currently active elements, while making few changes in each timestep. This model is popular in both dynamic and online algorithms: in the former, the goal is to minimize the update time of the solution, while in the latter, the recourse (number of changes) is bounded. We present generic techniques for the dynamic set cover problem inspired by the classic greedy and primal-dual offline algorithms for set cover. The former leads to a competitive ratio of O(lognt), where nt is the number of currently active elements at timestep t, while the latter yields competitive ratios dependent on ft, the maximum number of sets that a currently active element belongs to. We demonstrate that these techniques are useful for obtaining tight results in both settings: update time bounds and limited recourse, exhibiting algorithmic techniques common to these two parallel threads of research.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055493"}, {"primary_key": "3868249", "vector": [], "sparse_vector": [], "title": "Linear matroid intersection is in quasi-NC.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Given two matroids on the same ground set, the matroid intersection problem asks to find a common independent set of maximum size. We show that the linear matroid intersection problem is in quasi-NC2. That is, it has uniform circuits of quasi-polynomial size nO(logn), and O(log2 n) depth. This generalizes the similar result for the bipartite perfect matching problem. We do this by an almost complete derandomization of the Isolation lemma for matroid intersection.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055440"}, {"primary_key": "3868250", "vector": [], "sparse_vector": [], "title": "Synchronization strings: codes for insertions and deletions approaching the Singleton bound.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce synchronization strings, which provide a novel way of efficiently dealing with synchronization errors, i.e., insertions and deletions. Synchronization errors are strictly more general and much harder to deal with than more commonly considered half-errors, i.e., symbol corruptions and erasures. For every ε > 0, synchronization strings allow to index a sequence with an ε-O(1) size alphabet such that one can efficiently transform k synchronization errors into (1 + ε)k half-errors. This powerful new technique has many applications. In this paper we focus on designing insdel codes, i.e., error correcting block codes (ECCs) for insertion deletion channels.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055498"}, {"primary_key": "3868251", "vector": [], "sparse_vector": [], "title": "Targeted pseudorandom generators, simulation advice generators, and derandomizing logspace.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Assume that for every derandomization result for logspace algorithms, there is a pseudorandom generator strong enough to nearly recover the derandomization by iterating over all seeds and taking a majority vote. We prove under a precise version of this assumption that BPL ⊆ ∩α > 0 DSPACE(log1 + α n).", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055414"}, {"primary_key": "3868252", "vector": [], "sparse_vector": [], "title": "Efficient massively parallel methods for dynamic programming.", "authors": ["Sungjin Im", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern science and engineering is driven by massively large data sets and its advance heavily relies on massively parallel computing platforms such as Spark, MapReduce, and Hadoop. Theoretical models have been proposed to understand the power and limitations of such platforms. Recent study of developed theoretical models has led to the discovery of new algorithms that are fast and efficient in both theory and practice, thereby beginning to unlock their underlying power. Given recent promising results, the area has turned its focus on discovering widely applicable algorithmic techniques for solving problems efficiently.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055460"}, {"primary_key": "3868253", "vector": [], "sparse_vector": [], "title": "Decremental single-source reachability in planar digraphs.", "authors": ["Giuseppe F<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we show a new algorithm for the decremental single-source reachability problem in directed planar graphs. It processes any sequence of edge deletions in O(nlog2nloglogn) total time and explicitly maintains the set of vertices reachable from a fixed source vertex. Hence, if all edges are eventually deleted, the amortized time of processing each edge deletion is only O(log2 n loglogn), which improves upon a previously known O(√n) solution. We also show an algorithm for decremental maintenance of strongly connected components in directed planar graphs with the same total update time. These results constitute the first almost optimal (up to polylogarithmic factors) algorithms for both problems.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055480"}, {"primary_key": "3868254", "vector": [], "sparse_vector": [], "title": "A weighted linear matroid parity algorithm.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The matroid parity (or matroid matching) problem, introduced as a common generalization of matching and matroid intersection problems, is so general that it requires an exponential number of oracle calls. <PERSON><PERSON><PERSON><PERSON> (1980) showed that this problem admits a min-max formula and a polynomial algorithm for linearly represented matroids. Since then efficient algorithms have been developed for the linear matroid parity problem.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055436"}, {"primary_key": "3868255", "vector": [], "sparse_vector": [], "title": "Compression of quantum multi-prover interactive proofs.", "authors": ["Zhengfeng Ji"], "summary": "We present a protocol that transforms any quantum multi-prover interactive proof into a nonlocal game in which questions consist of logarithmic number of bits and answers of constant number of bits. As a corollary, it follows that the promise problem corresponding to the approximation of the nonlocal value to inverse polynomial accuracy is complete for QMIP*, and therefore NEXP-hard. This establishes that nonlocal games are provably harder than classical games without any complexity theory assumptions. Our result also indicates that gap amplification for nonlocal games may be impossible in general and provides a negative evidence for the feasibility of the gap amplification approach to the multi-prover variant of the quantum PCP conjecture.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055441"}, {"primary_key": "3868256", "vector": [], "sparse_vector": [], "title": "A polynomial restriction lemma with applications.", "authors": ["Valentine <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A polynomial threshold function (PTF) of degree d is a boolean function of the form f=sgn(p), where p is a degree-d polynomial, and sgn is the sign function. The main result of the paper is an almost optimal bound on the probability that a random restriction of a PTF is not close to a constant function, where a boolean function g is called δ-close to constant if, for some vε{1,-1}, we have g(x)=v for all but at most δ fraction of inputs. We show for every PTF f of degree d≥ 1, and parameters 0<δ, r≤ 1/16, that", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055470"}, {"primary_key": "3868257", "vector": [], "sparse_vector": [], "title": "On independent sets, 2-to-2 games, and Grassmann graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a candidate reduction from the 3-Lin problem to the 2-to-2 Games problem and present a combinatorial hypothesis about Grassmann graphs which, if correct, is sufficient to show the soundness of the reduction in a certain non-standard sense. A reduction that is sound in this non-standard sense implies that it is NP-hard to distinguish whether an n-vertex graph has an independent set of size ( 1- 1/√2 ) n - o(n) or whether every independent set has size o(n), and consequently, that it is NP-hard to approximate the Vertex Cover problem within a factor √2-o(1).", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055432"}, {"primary_key": "3868258", "vector": [], "sparse_vector": [], "title": "Time-space hardness of learning sparse parities.", "authors": ["Gillat Kol", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We define a concept class ℱ to be time-space hard (or memory-samples hard) if any learning algorithm for ℱ requires either a memory of size super-linear in n or a number of samples super-polynomial in n, where n is the length of one sample.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055430"}, {"primary_key": "3868259", "vector": [], "sparse_vector": [], "title": "Sum of squares lower bounds for refuting any CSP.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Ryan <PERSON>&<PERSON>;Donnell", "<PERSON>"], "summary": "Let P:{0,1}k → {0,1} be a nontrivial k-ary predicate. Consider a random instance of the constraint satisfaction problem (P) on n variables with Δ n constraints, each being P applied to k randomly chosen literals. Provided the constraint density satisfies Δ ≫ 1, such an instance is unsatisfiable with high probability. The refutation problem is to efficiently find a proof of unsatisfiability.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055485"}, {"primary_key": "3868260", "vector": [], "sparse_vector": [], "title": "Approximating rectangles by juntas and weakly-exponential lower bounds for LP relaxations of CSPs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that for constraint satisfaction problems (CSPs), sub-exponential size linear programming relaxations are as powerful as nΩ(1)-rounds of the Sherali-<PERSON> linear programming hierarchy. As a corollary, we obtain sub-exponential size lower bounds for linear programming relaxations that beat random guessing for many CSPs such as MAX-CUT and MAX-3SAT. This is a nearly-exponential improvement over previous results; previously, the best known lower bounds were quasi-polynomial in n (<PERSON>, <PERSON>, <PERSON>, <PERSON> 2013).", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055438"}, {"primary_key": "3868261", "vector": [], "sparse_vector": [], "title": "Examining classical graph-theory problems from the viewpoint of formal-verification methods (invited talk).", "authors": ["<PERSON><PERSON>"], "summary": "The talk surveys a series of works that lift the rich semantics and structure of graphs, and the experience of the formal-verification community in reasoning about them, to classical graph-theoretical problems.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3079075"}, {"primary_key": "3868262", "vector": [], "sparse_vector": [], "title": "An SDP-based algorithm for linear-sized spectral sparsification.", "authors": ["<PERSON>", "He Sun"], "summary": "For any undirected and weighted graph G=(V,E,w) with n vertices and m edges, we call a sparse subgraph H of G, with proper reweighting of the edges, a (1+Îµ)-spectral sparsifier if", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055477"}, {"primary_key": "3868263", "vector": [], "sparse_vector": [], "title": "Geodesic walks in polytopes.", "authors": ["<PERSON>", "Santosh S<PERSON>"], "summary": "We introduce the geodesic walk for sampling Riemannian manifolds and apply it to the problem of generating uniform random points from the interior of polytopes in ℝn specified by m inequalities. The walk is a discrete-time simulation of a stochastic differential equation (SDE) on the Riemannian manifold equipped with the metric induced by the Hessian of a convex function; each step is the solution of an ordinary differential equation (ODE). The resulting sampling algorithm for polytopes mixes in O*(mn3/4) steps. This is the first walk that breaks the quadratic barrier for mixing in high dimension, improving on the previous best bound of O*(mn) by <PERSON><PERSON><PERSON> and <PERSON><PERSON> for the <PERSON><PERSON> walk. We also show that each step of the geodesic walk (solving an ODE) can be implemented efficiently, thus improving the time complexity for sampling polytopes. Our analysis of the geodesic walk for general Hessian manifolds does not assume positive curvature and might be of independent interest.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055416"}, {"primary_key": "3868264", "vector": [], "sparse_vector": [], "title": "Improved non-malleable extractors, non-malleable codes and independent source extractors.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper we give improved constructions of several central objects in the literature of randomness extraction and tamper-resilient cryptography. Our main results are:", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055486"}, {"primary_key": "3868265", "vector": [], "sparse_vector": [], "title": "Lossy kernelization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we propose a new framework for analyzing the performance of preprocessing algorithms. Our framework builds on the notion of kernelization from parameterized complexity. However, as opposed to the original notion of kernelization, our definitions com- bine well with approximation algorithms and heuristics. The key new definition is that of a polynomial size α-approximate kernel. Loosely speaking, a polynomial size α-approximate kernel is a polynomial time pre-processing algorithm that takes as input an instance (I, k) to a parameterized problem, and outputs another instance (I′,k′) to the same problem, such that |I′| + k′ ≤ kO(1). Additionally, for every c ≥ 1, a c-approximate solution s′ to the pre-processed instance (I′, k′) can be turned in polynomial time into a (c · α)-approximate solution s to the original instance (I,k).", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055456"}, {"primary_key": "3868266", "vector": [], "sparse_vector": [], "title": "Almost-polynomial ratio ETH-hardness of approximating densest k-subgraph.", "authors": ["<PERSON><PERSON>"], "summary": "In the Densest k-Subgraph (DkS) problem, given an undirected graph G and an integer k, the goal is to find a subgraph of G on k vertices that contains maximum number of edges. Even though <PERSON><PERSON><PERSON> et al.'s state-of-the-art algorithm for the problem achieves only O(n1/4 + ϵ) approximation ratio, previous attempts at proving hardness of approximation, including those under average case assumptions, fail to achieve a polynomial ratio; the best ratios ruled out under any worst case assumption and any average case assumption are only any constant (<PERSON><PERSON><PERSON><PERSON> and <PERSON>) and 2O(log2/3 n) (<PERSON><PERSON> et al.) respectively.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055412"}, {"primary_key": "3868267", "vector": [], "sparse_vector": [], "title": "Optimizing tree pattern queries: why cutting is not enough (invited talk).", "authors": ["<PERSON><PERSON>"], "summary": "Tree pattern queries are a natural language for querying graph- and tree-structured data. A central question for understanding their optimization problem was whether they can be minimized by cutting away redundant parts. This question has been studied since the early 2000's and was recently resolved.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3079076"}, {"primary_key": "3868268", "vector": [], "sparse_vector": [], "title": "The non-cooperative tile assembly model is not intrinsically universal or capable of bounded Turing machine simulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The field of algorithmic self-assembly is concerned with the computational and expressive power of nanoscale self-assembling molecular systems. In the well-studied cooperative, or temperature 2, abstract tile assembly model it is known that there is a tile set to simulate any Turing machine and an intrinsically universal tile set that simulates the shapes and dynamics of any instance of the model, up to spatial rescaling. It has been an open question as to whether the seemingly simpler noncooperative, or temperature 1, model is capable of such behaviour. Here we show that this is not the case by showing that there is no tile set in the noncooperative model that is intrinsically universal, nor one capable of time-bounded Turing machine simulation within a bounded region of the plane.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055446"}, {"primary_key": "3868269", "vector": [], "sparse_vector": [], "title": "Approximate counting, the <PERSON><PERSON><PERSON> local lemma, and inference in graphical models.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In this paper we introduce a new approach for approximately counting in bounded degree systems with higher-order constraints. Our main result is an algorithm to approximately count the number of solutions to a CNF formula Ф when the width is logarithmic in the maximum degree. This closes an exponential gap between the known upper and lower bounds.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055428"}, {"primary_key": "3868270", "vector": [], "sparse_vector": [], "title": "Dynamic spanning forest with worst-case update time: adaptive, Las Vegas, and O(n1/2 - ε)-time.", "authors": ["Danupon <PERSON>", "Thatchaphol <PERSON>"], "summary": "We present two algorithms for dynamically maintaining a spanning forest of a graph undergoing edge insertions and deletions. Our algorithms guarantee worst-case update time and work against an adaptive adversary, meaning that an edge update can depend on previous outputs of the algorithms. We provide the first polynomial improvement over the long-standing O(√n) bound of [<PERSON><PERSON> STOC'84, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON>ssenzweig FOCS'92] for such type of algorithms. The previously best improvement was O(√n (loglogn)2/logn) [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON> ESA'16]. We note however that these bounds were obtained by deterministic algorithms while our algorithms are randomized.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055447"}, {"primary_key": "3868271", "vector": [], "sparse_vector": [], "title": "The integrality gap of the Goemans-Linial SDP relaxation for sparsest cut is at least a constant multiple of √log n.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We prove that the integrality gap of the Goemans-Linial semidefinite programming relaxation for the Sparsest Cut Problem is Ω(√logn) on inputs with n vertices, thus matching the previously best known upper bound (logn)1/2+o(1) up to lower-order factors. This statement is a consequence of the following new isoperimetric-type inequality. Consider the 8-regular graph whose vertex set is the 5-dimensional integer grid ℤ5 and where each vertex (a,b,c,d,e)∈ ℤ5 is connected to the 8 vertices (a± 1,b,c,d,e), (a,b± 1,c,d,e), (a,b,c± 1,d,e± a), (a,b,c,d± 1,e± b). This graph is known as the <PERSON><PERSON>ley graph of the 5-dimensional discrete Heisenberg group. Given Ω⊆ ℤ5, denote the size of its edge boundary in this graph (a.k.a. the horizontal perimeter of Ω) by |∂hΩ|. For t ϵ ℕ, denote by |∂vtΩ| the number of (a,b,c,d,e)ϵ ℤ5 such that exactly one of the two vectors (a,b,c,d,e),(a,b,c,d,e+t) is in Ω. The vertical perimeter of Ω is defined to be |∂vΩ|= √Σt=1∞|∂vtΩ|2/t2. We show that every subset Ω⊆ ℤ5 satisfies |∂vΩ|=O(|∂hΩ|). This vertical-versus-horizontal isoperimetric inequality yields the above-stated integrality gap for Sparsest Cut and answers several geometric and analytic questions of independent interest.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055413"}, {"primary_key": "3868272", "vector": [], "sparse_vector": [], "title": "A quantum linearity test for robustly verifying entanglement.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce a simple two-player test which certifies that the players apply tensor products of Pauli σX and σZ observables on the tensor product of n EPR pairs. The test has constant robustness: any strategy achieving success probability within an additive of the optimal must be poly(ε)-close, in the appropriate distance measure, to the honest n-qubit strategy. The test involves 2n-bit questions and 2-bit answers. The key technical ingredient is a quantum version of the classical linearity test of <PERSON><PERSON>, <PERSON>, and Rubinfeld.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055468"}, {"primary_key": "3868273", "vector": [], "sparse_vector": [], "title": "Trace reconstruction with exp(O(n1/3)) samples.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the trace reconstruction problem, an unknown bit string x ∈ {0,1}n is observed through the deletion channel, which deletes each bit of x with some constant probability q, yielding a contracted string x. How many independent copies of x are needed to reconstruct x with high probability? Prior to this work, the best upper bound, due to <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> (2008), was exp(O(n1/2)). We improve this bound to exp(O(n1/3)) using statistics of individual bits in the output and show that this bound is sharp in the restricted model where this is the only information used. Our method, that uses elementary complex analysis, can also handle insertions. Similar results were obtained independently and simultaneously by <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON>.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055494"}, {"primary_key": "3868274", "vector": [], "sparse_vector": [], "title": "Complexity of short Presburger arithmetic.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study complexity of short sentences in Presburger arithmetic (Short-PA). Here by \"short\" we mean sentences with a bounded number of variables, quantifers, inequalities and Boolean operations; the input consists only of the integers involved in the inequalities. We prove that assuming <PERSON><PERSON><PERSON>'s partition can be found in polynomial time, the satisfability of Short-PA sentences can be decided in polynomial time. Furthermore, under the same assumption, we show that the numbers of satisfying assignments of short Presburger sentences can also be computed in polynomial time.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055435"}, {"primary_key": "3868275", "vector": [], "sparse_vector": [], "title": "Practical post-quantum key agreement from generic lattices (invited talk).", "authors": ["<PERSON><PERSON>"], "summary": "Lattice-based cryptography offers some of the most attractive primitives believed to be resistant to quantum computers. This work introduces \"Frodo\" - a concrete instantiation of a key agreement mechanism based on hard problems in generic lattices.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3079078"}, {"primary_key": "3868276", "vector": [], "sparse_vector": [], "title": "Efficient quantum tomography II.", "authors": ["Ryan <PERSON>&<PERSON>;Donnell", "<PERSON>"], "summary": "We continue our analysis of: (i) \"Quantum tomography\", i.e., learning a quantum state, i.e., the quantum generalization of learning a discrete probability distribution; (ii) The distribution of Young diagrams output by the RSK algorithm on random words. Regarding (ii), we introduce two powerful new tools: first, a precise upper bound on the expected length of the longest union of k disjoint increasing subsequences in a random length-n word with letter distribution α1 ≥ α2 ≥ … ≥ αd. Our bound has the correct main term and second-order term, and holds for all n, not just in the large-n limit. Second, a new majorization property of the RSK algorithm that allows one to analyze the Young diagram formed by the lower rows λk, λk+1, … of its output. These tools allow us to prove several new theorems concerning the distribution of random Young diagrams in the nonasymptotic regime, giving concrete error bounds that are optimal, or nearly so, in all parameters. As one example, we give a fundamentally new proof of the celebrated fact that the expected length of the longest increasing sequence in a random length-n permutation is bounded by 2√n. This is the k = 1, αi ≡ 1/d, d → ∞ special case of a much more general result we prove: the expected length of the kth Young diagram row produced by an α-random word is αk n ± 2√αkd n.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055454"}, {"primary_key": "3868277", "vector": [], "sparse_vector": [], "title": "Pseudodeterministic constructions in subexponential time.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We study pseudodeterministic constructions, i.e., randomized algorithms which output the same solution on most computation paths. We establish unconditionally that there is an infinite sequence {pn} of primes and a randomized algorithm A running in expected sub-exponential time such that for each n, on input 1|pn|, A outputs pn with probability 1. In other words, our result provides a pseudodeterministic construction of primes in sub-exponential time which works infinitely often.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055500"}, {"primary_key": "3868278", "vector": [], "sparse_vector": [], "title": "A simpler and faster strongly polynomial algorithm for generalized flow maximization.", "authors": ["<PERSON>", "László A. <PERSON>"], "summary": "We present a new strongly polynomial algorithm for generalized flow maximization. The first strongly polynomial algorithm for this problem was given very recently by <PERSON><PERSON><PERSON>; our new algorithm is much simpler, and much faster. The complexity bound O((m+nlogn)mnlog(n2/m)) improves on the previous estimate obtained by <PERSON><PERSON><PERSON> by almost a factor O(n2). Even for small numerical parameter values, our algorithm is essentially as fast as the best weakly polynomial algorithms. The key new technical idea is relaxing primal feasibility conditions. This allows us to work almost exclusively with integral flows, in contrast to all previous algorithms.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055439"}, {"primary_key": "3868279", "vector": [], "sparse_vector": [], "title": "A time- and message-optimal distributed algorithm for minimum spanning trees.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a randomized (Las Vegas) distributed algorithm that constructs a minimum spanning tree (MST) in weighted networks with optimal (up to polylogarithmic factors) time and message complexity. This algorithm runs in Õ(D + √n) time and exchanges Õ(m) messages (both with high probability), where n is the number of nodes of the network, D is the diameter, and m is the number of edges. This is the first distributed MST algorithm that matches simultaneously the time lower bound of Ω(D + √n) [<PERSON>, SIAM J. Comput. 2006] and the message lower bound of Ω(m) [<PERSON> et al., J. ACM 2015], which both apply to randomized Monte Carlo algorithms.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055449"}, {"primary_key": "3868280", "vector": [], "sparse_vector": [], "title": "Pseudorandomness of ring-LWE for any ring and modulus.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "We give a polynomial-time quantum reduction from worst-case (ideal) lattice problems directly to decision (Ring-)LWE. This extends to decision all the worst-case hardness results that were previously known for the search version, for the same or even better parameters and with no algebraic restrictions on the modulus or number field. Indeed, our reduction is the first that works for decision Ring-LWE with any number field and any modulus.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055489"}, {"primary_key": "3868281", "vector": [], "sparse_vector": [], "title": "Strongly exponential lower bounds for monotone computation.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "For a universal constant α > 0 we prove size lower bounds of 2α(n) for an explicit function in monotone NP in the following models of computation: monotone formulas, monotone switching networks, monotone span programs, and monotone comparator circuits, where n is the number of variables of the underlying function. Our lower bounds improve on the best previous bounds in each of these models, and are the best possible for any function up to constant factors in the exponent. Moreover, we give one unified proof that is short and fairly elementary.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055478"}, {"primary_key": "3868282", "vector": [], "sparse_vector": [], "title": "Strongly refuting random CSPs below the spectral threshold.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Random constraint satisfaction problems (CSPs) are known to exhibit threshold phenomena: given a uniformly random instance of a CSP with n variables and m clauses, there is a value of m = Ω(n) beyond which the CSP will be unsatisfiable with high probability. Strong refutation is the problem of certifying that no variable assignment satisfies more than a constant fraction of clauses; this is the natural algorithmic problem in the unsatisfiable regime (when m/n = ω(1)).", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055417"}, {"primary_key": "3868283", "vector": [], "sparse_vector": [], "title": "Why prices need algorithms (invited talk).", "authors": ["<PERSON>", "<PERSON><PERSON>-<PERSON>"], "summary": "Computational complexity has already had plenty to say about the computation of economic equilibria. However, understanding when equilibria are guaranteed to exist is a central theme in economic theory, seemingly unrelated to computation. In this talk we survey our main results presented at EC'15, which show that the existence of equilibria in markets is inextricably connected to the computational complexity of related optimization problems, such as revenue or welfare maximization. We demonstrate how this relationship implies, under suitable complexity assumptions, a host of impossibility results. We also suggest a complexity-theoretic explanation for the lack of useful extensions of the Walrasian equilibrium concept: such extensions seem to require the invention of novel polynomial-time algorithms for welfare maximization.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3079077"}, {"primary_key": "3868284", "vector": [], "sparse_vector": [], "title": "Answering FAQs in CSPs, probabilistic graphical models, databases, logic and matrix operations (invited talk).", "authors": ["<PERSON><PERSON>"], "summary": "In this talk we will discuss a general framework to solve certain sums of products of functions over semi-rings. This captures many well-known problems in disparate areas such as CSPs, Probabilistic Graphical Models, Databases, Logic and Matrix Operations. This talk is based on joint work titled FAQ: Questions Asked Frequently with <PERSON><PERSON><PERSON> and <PERSON>, which appeared in PODS 2016.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3079073"}, {"primary_key": "3868285", "vector": [], "sparse_vector": [], "title": "Area-convexity, l∞ regularization, and undirected multicommodity flow.", "authors": ["<PERSON>"], "summary": "We show the strong-convexity assumption of regularization-based methods for solving bilinear saddle point problems may be relaxed to a weaker notion of area-convexity with respect to an alternating bilinear form. This allows bypassing the infamous '' barrier for strongly convex regularizers that has stalled progress on a number of algorithmic problems.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055501"}, {"primary_key": "3868286", "vector": [], "sparse_vector": [], "title": "Low rank approximation with entrywise l1-norm error.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the ℓ1-low rank approximation problem, where for a given n x d matrix A and approximation factor α ≤ 1, the goal is to output a rank-k matrix for which", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055431"}, {"primary_key": "3868287", "vector": [], "sparse_vector": [], "title": "Real stable polynomials and matroids: optimization and counting.", "authors": ["<PERSON>", "Nisheeth K. <PERSON>"], "summary": "Several fundamental optimization and counting problems arising in computer science, mathematics and physics can be reduced to one of the following computational tasks involving polynomials and set systems: given an oracle access to an m-variate real polynomial g and to a family of (multi-)subsets ℬ of [m], (1) compute the sum of coefficients of monomials in g corresponding to all the sets that appear in B(1), or find S ε ℬ such that the monomial in g corresponding to S has the largest coefficient in g. Special cases of these problems, such as computing permanents and mixed discriminants, sampling from determinantal point processes, and maximizing sub-determinants with combinatorial constraints have been topics of much recent interest in theoretical computer science.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055457"}, {"primary_key": "3868288", "vector": [], "sparse_vector": [], "title": "Fast convergence of learning in games (invited talk).", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "A plethora of recent work has analyzed properties of outcomes in games when each player employs a no-regret learning algorithm. Many algorithms achieve regret against the best fixed action in hindisght that decays at a rate of O(1/'T), when the game is played for T iterations. The latter rate is optimal in adversarial settings. However, in a game a player's opponents are minimizing their own regret, rather than maximizing the player's regret. (<PERSON><PERSON><PERSON><PERSON> et al. 2014) and (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> 2013) showed that in two player zero-sum games O(1/T) rates are achievable. In (<PERSON><PERSON><PERSON><PERSON><PERSON> et al. 2015), we show that O(1/T3/4) rates are achievable in general multi-player games and also analyze convergence of the dynamics to approximately optimal social welfare, where we show a convergence rate of O(1/T). The latter result was subsequently generalized to a broader class of learning algorithms by (<PERSON> et al. 2016). This is based on joint work with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3084098"}, {"primary_key": "3868289", "vector": [], "sparse_vector": [], "title": "Explicit, almost optimal, epsilon-balanced codes.", "authors": ["<PERSON><PERSON>"], "summary": "The question of finding an epsilon-biased set with close to optimal support size, or, equivalently, finding an explicit binary code with distance 1-ϵ/2 and rate close to the Gilbert<PERSON><PERSON> bound, attracted a lot of attention in recent decades. In this paper we solve the problem almost optimally and show an explicit ϵ-biased set over k bits with support size O(k/ϵ2+o(1)). This improves upon all previous explicit constructions which were in the order of k2/ϵ2, k/ϵ3 or k5/4/ϵ5/2. The result is close to the Gilbert-<PERSON><PERSON><PERSON> bound which is O(k/ϵ2) and the lower bound which is Ω(k/ϵ2 log1/ϵ).", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055408"}, {"primary_key": "3868290", "vector": [], "sparse_vector": [], "title": "Formula lower bounds via the quantum method.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "A de Morgan formula over Boolean variables x1,…,xn is a binary tree whose internal nodes are marked with AND or OR gates and whose leaves are marked with variables or their negation. We define the size of the formula as the number of leaves in it. Proving that some explicit function (in P or NP) requires a large formula is a central open question in computational complexity. While we believe that some explicit functions require exponential formula size, currently the best lower bound for an explicit function is the Ω(n3) lower bound for <PERSON><PERSON>'s function.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055472"}, {"primary_key": "3868291", "vector": [], "sparse_vector": [], "title": "Fully-dynamic minimum spanning forest with improved worst-case update time.", "authors": ["<PERSON><PERSON>"], "summary": "We give a Las Vegas data structure which maintains a minimum spanning forest in an n-vertex edge-weighted undirected dynamic graph undergoing updates consisting of any mixture of edge insertions and deletions. Each update is supported in O(n1/2-c) worst-case time wh.p. where c > 0 is some constant, and this bound also holds in expectation. This is the first data structure achieving an improvement over the O(√n) deterministic worst-case update time of <PERSON><PERSON> et al., a bound that has been standing for 25 years. In fact, it was previously not even known how to maintain a spanning forest of an unweighted graph in worst-case time polynomially faster than Θ(√n). Our result is achieved by first giving a reduction from fully-dynamic to decremental minimum spanning forest preserving worst-case update time up to logarithmic factors. Then decremental minimum spanning forest is solved using several novel techniques, one of which involves keeping track of low-conductance cuts in a dynamic graph. An immediate corollary of our result is the first Las Vegas data structure for fully-dynamic connectivity where each update is handled in worst-case time polynomially faster than Θ(√n) w.h.p.; this data structure has O(1) worst-case query time.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055415"}, {"primary_key": "3868292", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON>: the first direct acceleration of stochastic gradient methods.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>'s momentum trick is famously known for accelerating gradient descent, and has been proven useful in building fast iterative algorithms. However, in the stochastic setting, counterexamples exist and prevent <PERSON><PERSON><PERSON>'s momentum from providing similar acceleration, even if the underlying problem is convex.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055448"}, {"primary_key": "3868293", "vector": [], "sparse_vector": [], "title": "Kolmogorov complexity version of Slepian-Wolf coding.", "authors": ["<PERSON>"], "summary": "<PERSON> and <PERSON> are given two correlated n-bit strings x1 and, respectively, x2, which they want to losslessly compress and send to <PERSON>. They can either collaborate by sharing their strings, or work separately. We show that there is no disadvantage in the second scenario: <PERSON> and <PERSON>, without knowing the other party's string, can compress their strings to almost minimal description length in the sense of Kolmogorov complexity. Furthermore, compression takes polynomial time and can be made at any combination of lengths that satisfy some necessary conditions (modulo additive polylogarithmic terms). More precisely, there exist probabilistic algorithms E1, E2, and D, with E1 and E2 running in polynomial time, having the following behavior: if n1, n2 are two integers satisfying n1 + n2 ≥ C(x1,x2), n1 ≥ C(x1 | x2), n2 ≥ C(x2 | x1), then for i ∈ {1,2}, Ei on input xi and ni outputs a string of length ni + O(log3 n) such that D on input E1(x1), E2(x2) reconstructs (x1,x2) with high probability (where C(x) denotes the plain Kolmogorov complexity of x, and C(x | y) is the complexity of x conditioned by y). Our main result is more general, as it deals with the compression of any constant number of correlated strings. It is an analog in the framework of algorithmic information theory of the classic Slepian-Wolf Theorem, a fundamental result in network information theory, in which x1 and x2 are realizations of two discrete random variables representing n independent draws from a joint distribution. In the classical result, the decompressor needs to know the joint distribution of the sources. In our result no type of independence is assumed and the decompressor does not have any prior information about the sources that are compressed.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3055421"}, {"primary_key": "3868294", "vector": [], "sparse_vector": [], "title": "Recent trends in decentralized cryptocurrencies (invited talk).", "authors": ["<PERSON>"], "summary": "Following Bitcoin's introduction, decentralized cryptocurrencies began to emerge as a new application domain for computer science. Bitcoin's protocol has been researched and improved upon along many fronts: from its underlying incentives, through to its cryptographic primitives and its security. Many research questions and challenges still remain as cryptocurrencies and other financial systems that rely on similar principles gain wider adoption.", "published": "2017-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3055399.3079074"}]