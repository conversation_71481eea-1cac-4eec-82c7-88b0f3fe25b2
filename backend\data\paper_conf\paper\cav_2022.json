[{"primary_key": "1624168", "vector": [], "sparse_vector": [], "title": "PAC Statistical Model Checking of Mean Payoff in Discrete- and Continuous-Time MDP.", "authors": ["Chaitanya <PERSON>wal", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Abstract Markov decision processes (MDP) and continuous-time MDP (CTMDP) are the fundamental models for non-deterministic systems with probabilistic uncertainty. Mean payoff (a.k.a. long-run average reward) is one of the most classic objectives considered in their context. We provide the first algorithm to compute mean payoff probably approximately correctly in unknown MDP; further, we extend it to unknown CTMDP. We do not require any knowledge of the state space, only a lower bound on the minimum transition probability, which has been advocated in literature. In addition to providing probably approximately correct (PAC) bounds for our algorithm, we also demonstrate its practical nature by running experiments on standard benchmarks.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_1"}, {"primary_key": "1624169", "vector": [], "sparse_vector": [], "title": "Distilling Constraints in Zero-Knowledge Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Abstract The most widely used Zero-Knowledge (ZK) protocols require provers to prove they know a solution to a computational problem expressed as a Rank-1 Constraint System (R1CS). An R1CS is essentially a system of non-linear arithmetic constraints over a set of signals, whose security level depends on its non-linear part only, as the linear (additive) constraints can be easily solved by an attacker. Distilling the essential constraints from an R1CS by removing the part that does not contribute to its security is important, not only to reduce costs (time and space) of producing the ZK proofs, but also to reveal to cryptographic programmers the real hardness of their proofs. In this paper, we formulate the problem of distilling constraints from an R1CS as the (hard) problem of simplifying constraints in the realm of non-linearity. To the best of our knowledge, it is the first time that constraint-based techniques developed in the context of formal methods are applied to the challenging problem of analysing and optimizing ZK protocols.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_21"}, {"primary_key": "1624170", "vector": [], "sparse_vector": [], "title": "SolCMC: Solidity Compiler&apos;s <PERSON> Checker.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Abstract Formally verifying smart contracts is important due to their immutable nature, usual open source licenses, and high financial incentives for exploits. Since 2019 the Ethereum Foundation’s Solidity compiler ships with a model checker. The checker, called SolCMC, has two different reasoning engines and tracks closely the development of the Solidity language. We describe SolCMC’s architecture and use from the perspective of developers of both smart contracts and tools for software verification, and show how to analyze nontrivial properties of real life contracts in a fully automated manner.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_16"}, {"primary_key": "1624171", "vector": [], "sparse_vector": [], "title": "Sampling-Based Verification of CTMCs with Uncertain Rates.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We employ uncertain parametric CTMCs with parametric transition rates and a prior on the parameter values. The prior encodes uncertainty about the actual transition rates, while the parameters allow dependencies between transition rates. Sampling the parameter values from the prior distribution then yields a standard CTMC, for which we may compute relevant reachability probabilities. We provide a principled solution, based on a technique called scenario-optimization, to the following problem: From a finite set of parameter samples and a user-specified confidence level, compute prediction regions on the reachability probabilities. The prediction regions should (with high probability) contain the reachability probabilities of a CTMC induced by any additional sample. To boost the scalability of the approach, we employ standard abstraction techniques and adapt our methodology to support approximate reachability probabilities. Experiments with various well-known benchmarks show the applicability of the approach.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_2"}, {"primary_key": "1624172", "vector": [], "sparse_vector": [], "title": "Reachability of Koopman Linearized Systems Using Random Fourier Feature Observables and Polynomial Zonotope Refinement.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Abstract Koopman operator linearization approximates nonlinear systems of differential equations with higher-dimensional linear systems. For formal verification using reachability analysis, this is an attractive conversion, as highly scalable methods exist to compute reachable sets for linear systems. However, two main challenges are present with this approach, both of which are addressed in this work. First, the approximation must be sufficiently accurate for the result to be meaningful, which is controlled by the choice of observable functions during <PERSON><PERSON><PERSON> operator linearization. By using random Fourier features as observable functions, the process becomes more systematic than earlier work, while providing a higher-accuracy approximation. Second, although the higher-dimensional system is linear, simple convex initial sets in the original space can become complex non-convex initial sets in the linear system. We overcome this using a combination of Taylor model arithmetic and polynomial zonotope refinement. Compared with prior work, the result is more efficient, more systematic and more accurate.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_24"}, {"primary_key": "1624174", "vector": [], "sparse_vector": [], "title": "SMT-Based Translation Validation for Machine Learning Compiler.", "authors": ["Seong<PERSON> Bang", "<PERSON>unghyeon Nam", "<PERSON><PERSON><PERSON>", "Ho <PERSON> Jhoo", "<PERSON><PERSON><PERSON>"], "summary": "Abstract Machine learning compilers are large software containing complex transformations for deep learning models, and any buggy transformation may cause a crash or silently bring a regression to the prediction accuracy and performance. This paper proposes an SMT-based translation validation framework for Multi-Level IR (MLIR), a compiler framework used by many deep learning compilers. It proposes an SMT encoding tailored for translation validation that is an over-approximation of the FP arithmetic and reduction operations. It performs abstraction refinement if validation fails. We also propose a new approach for encoding arithmetic properties of reductions in SMT. We found mismatches between the specification and implementation of MLIR, and validated high-level transformations for , , and with proper splitting.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_19"}, {"primary_key": "1624175", "vector": [], "sparse_vector": [], "title": "Oblivious Online Monitoring for Safety LTL Specification via Fully Homomorphic Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract In many Internet of Things (IoT) applications, data sensed by an IoT device are continuously sent to the server and monitored against a specification. Since the data often contain sensitive information, and the monitored specification is usually proprietary, both must be kept private from the other end. We propose a protocol to conduct oblivious online monitoring —online monitoring conducted without revealing the private information of each party to the other—against a safety LTL specification. In our protocol, we first convert a safety LTL formula into a DFA and conduct online monitoring with the DFA. Based on fully homomorphic encryption (FHE) , we propose two online algorithms ( Reverse and Block ) to run a DFA obliviously. We prove the correctness and security of our entire protocol. We also show the scalability of our algorithms theoretically and empirically. Our case study shows that our algorithms are fast enough to monitor blood glucose levels online, demonstrating our protocol’s practical relevance.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_22"}, {"primary_key": "1624176", "vector": [], "sparse_vector": [], "title": "Data-Driven Invariant Learning for Probabilistic Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract <PERSON> and <PERSON><PERSON><PERSON><PERSON>’s weakest pre-expectation framework is one of the most well-established methods for deductive verification of probabilistic programs. Roughly, the idea is to generalize binary state assertions to real-valued expectations , which can measure expected values of probabilistic program quantities. While loop-free programs can be analyzed by mechanically transforming expectations, verifying loops usually requires finding an invariant expectation , a difficult task. We propose a new view of invariant expectation synthesis as a regression problem: given an input state, predict the average value of the post-expectation in the output distribution. Guided by this perspective, we develop the first data-driven invariant synthesis method for probabilistic programs. Unlike prior work on probabilistic invariant inference, our approach can learn piecewise continuous invariants without relying on template expectations. We also develop a data-driven approach to learn sub-invariants from data, which can be used to upper- or lower-bound expected values. We implement our approaches and demonstrate their effectiveness on a variety of benchmarks from the probabilistic programming literature.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_3"}, {"primary_key": "1624178", "vector": [], "sparse_vector": [], "title": "Abstraction Modulo Stability for Reverse Engineering.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Abstract The analysis of legacy systems requires the automated extraction of high-level specifications. We propose a framework, called Abstraction Modulo Stability, for the analysis of transition systems operating in stable states, and responding with run-to-completion transactions to external stimuli. The abstraction captures the effects of external stimuli on the system state, and describes it in the form of a finite state machine. This approach is parametric on a set of predicates of interest and the definition of stability. We consider some possible stability definitions which yield different practically relevant abstractions, and propose a parametric algorithm for abstraction computation. The obtained FSM is extended with guards and effects on a given set of variables of interest. The framework is evaluated in terms of expressivity and adequacy within an industrial project with the Italian Railway Network, on reverse engineering tasks of relay-based interlocking circuits to extract specifications for a computer-based reimplementation.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_23"}, {"primary_key": "1624180", "vector": [], "sparse_vector": [], "title": "Software Verification of Hyperproperties Beyond k-Safety.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Temporal hyperproperties are system properties that relate multiple execution traces. For (finite-state) hardware, temporal hyperproperties are supported by model checking algorithms, and tools for general temporal logics like HyperLTL exist. For (infinite-state) software, the analysis of temporal hyperproperties has, so far, been limited to $k$-safety properties, i.e., properties that stipulate the absence of a bad interaction between any $k$ traces. In this paper, we present an automated method for the verification of $\\forall^k\\exists^l$-safety properties in infinite-state systems. A $\\forall^k\\exists^l$-safety property stipulates that for any $k$ traces, there exist $l$ traces such that the resulting $k+l$ traces do not interact badly. This combination of universal and existential quantification enables us to express many properties beyond $k$-safety, including, for example, generalized non-interference or program refinement. Our method is based on a strategy-based instantiation of existential trace quantification combined with a program reduction, both in the context of a fixed predicate abstraction. Notably, our framework allows for mutual dependence of strategy and reduction.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_17"}, {"primary_key": "1624181", "vector": [], "sparse_vector": [], "title": "Verifying Generalised and Structural Soundness of Workflow Nets via Relaxations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Abstract Workflow nets are a well-established mathematical formalism for the analysis of business processes arising from either modeling tools or process mining. The central decision problems for workflow nets are k -soundness, generalised soundness and structural soundness. Most existing tools focus on k -soundness. In this work, we propose novel scalable semi-procedures for generalised and structural soundness. This is achieved via integral and continuous Petri net reachability relaxations. We show that our approach is competitive against state-of-the-art tools.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_23"}, {"primary_key": "1624182", "vector": [], "sparse_vector": [], "title": "Data-driven Numerical Invariant Synthesis with Automatic Generation of Attributes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Abstract We propose a data-driven algorithm for numerical invariant synthesis and verification. The algorithm is based on the ICE-DT schema for learning decision trees from samples of positive and negative states and implications corresponding to program transitions. The main issue we address is the discovery of relevant attributes to be used in the learning process of numerical invariants. We define a method for solving this problem guided by the data sample. It is based on the construction of a separator that covers positive states and excludes negative ones, consistent with the implications. The separator is constructed using an abstract domain representation of convex sets. The generalization mechanism of the decision tree learning from the constraints of the separator allows the inference of general invariants, accurate enough for proving the targeted property. We implemented our algorithm and showed its efficiency.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_14"}, {"primary_key": "1624184", "vector": [], "sparse_vector": [], "title": "Local Search for SMT on Linear Integer Arithmetic.", "authors": ["S<PERSON>wei <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Abstract Satisfiability Modulo Linear Integer Arithmetic, SMT (LIA) for short, has significant applications in many domains. In this paper, we develop the first local search algorithm for SMT (LIA) by directly operating on variables, breaking through the traditional framework. We propose a local search framework by considering the distinctions between Boolean and integer variables. Moreover, we design a novel operator and scoring functions tailored for LIA, and propose a two-level operation selection heuristic. Putting these together, we develop a local search SMT (LIA) solver called LS-LIA. Experiments are carried out to evaluate LS-LIA on benchmarks from SMTLIB and two benchmark sets generated from job shop scheduling and data race detection. The results show that LS-LIA is competitive and complementary with state-of-the-art SMT solvers, and performs particularly well on those formulae with only integer variables. A simple sequential portfolio with Z3 improves the state-of-the-art on satisfiable benchmark sets of LIA and IDL benchmarks from SMT-LIB. LS-LIA also solves Job Shop Scheduling benchmarks substantially faster than traditional complete SMT solvers.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_12"}, {"primary_key": "1624185", "vector": [], "sparse_vector": [], "title": "Neural Network Robustness as a Verification Property: A Principled Case Study.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Abstract Neural networks are very successful at detecting patterns in noisy data, and have become the technology of choice in many fields. However, their usefulness is hampered by their susceptibility to adversarial attacks . Recently, many methods for measuring and improving a network’s robustness to adversarial perturbations have been proposed, and this growing body of research has given rise to numerous explicit or implicit notions of robustness. Connections between these notions are often subtle, and a systematic comparison between them is missing in the literature. In this paper we begin addressing this gap, by setting up general principles for the empirical analysis and evaluation of a network’s robustness as a mathematical property—during the network’s training phase, its verification, and after its deployment. We then apply these principles and conduct a case study that showcases the practical benefits of our general approach.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_11"}, {"primary_key": "1624186", "vector": [], "sparse_vector": [], "title": "Playing Against Fair Adversaries in Stochastic Games with Total Rewards.", "authors": ["<PERSON>", "Pedro R. D&apos;Argenio", "<PERSON><PERSON>", "<PERSON>"], "summary": "Abstract We investigate zero-sum turn-based two-player stochastic games in which the objective of one player is to maximize the amount of rewards obtained during a play, while the other aims at minimizing it. We focus on games in which the minimizer plays in a fair way. We believe that these kinds of games enjoy interesting applications in software verification, where the maximizer plays the role of a system intending to maximize the number of “milestones” achieved, and the minimizer represents the behavior of some uncooperative but yet fair environment. Normally, to study total reward properties, games are requested to be stopping (i.e., they reach a terminal state with probability 1). We relax the property to request that the game is stopping only under a fair minimizing player. We prove that these games are determined, i.e., each state of the game has a value defined. Furthermore, we show that both players have memoryless and deterministic optimal strategies, and the game value can be computed by approximating the greatest-fixed point of a set of functional equations. We implemented our approach in a prototype tool, and evaluated it on an illustrating example and an Unmanned Aerial Vehicle case study.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_3"}, {"primary_key": "1624188", "vector": [], "sparse_vector": [], "title": "Sound and Complete Certificates for Quantitative Termination Analysis of Probabilistic Programs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Abstract We consider the quantitative problem of obtaining lower-bounds on the probability of termination of a given non-deterministic probabilistic program. Specifically, given a non-termination threshold $$p \\in [0, 1],$$ p ∈ [ 0 , 1 ] , we aim for certificates proving that the program terminates with probability at least $$1-p$$ 1 - p . The basic idea of our approach is to find a terminating stochastic invariant, i.e. a subset $$ SI $$ SI of program states such that (i) the probability of the program ever leaving $$ SI $$ SI is no more than p , and (ii) almost-surely, the program either leaves $$ SI $$ SI or terminates. While stochastic invariants are already well-known, we provide the first proof that the idea above is not only sound, but also complete for quantitative termination analysis. We then introduce a novel sound and complete characterization of stochastic invariants that enables template-based approaches for easy synthesis of quantitative termination certificates, especially in affine or polynomial forms. Finally, by combining this idea with the existing martingale-based methods that are relatively complete for qualitative termination analysis, we obtain the first automated, sound, and relatively complete algorithm for quantitative termination analysis. Notably, our completeness guarantees for quantitative termination analysis are as strong as the best-known methods for the qualitative variant. Our prototype implementation demonstrates the effectiveness of our approach on various probabilistic programs. We also demonstrate that our algorithm certifies lower bounds on termination probability for probabilistic programs that are beyond the reach of previous methods.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_4"}, {"primary_key": "1624189", "vector": [], "sparse_vector": [], "title": "Proof-Guided Underapproximation Widening for Bounded Model Checking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract Bounded Model Checking (BMC) is a popularly used strategy for program verification and it has been explored extensively over the past decade. Despite such a long history, BMC still faces scalability challenges as programs continue to grow larger and more complex. One approach that has proven to be effective in verifying large programs is called Counterexample Guided Abstraction Refinement (CEGAR). In this work, we propose a complementary approach to CEGAR for bounded model checking of sequential programs: in contrast to CEGAR, our algorithm gradually widens underapproximations of a program, guided by the proofs of unsatisfiability. We implemented our ideas in a tool called Legion . We compare the performance of Legion against that of Corral , a state-of-the-art verifier from Microsoft, that utilizes the CEGAR strategy. We conduct our experiments on 727 Windows and Linux device driver benchmarks. We find that Legion is able to solve 12% more instances than Corral and that Legion exhibits a complementary behavior to that of Corral . Motivated by this, we also build a portfolio verifier, $$\\textsc {Legion}^{+}$$ L E G I O N + , that attempts to draw the best of Legion and Corral . Our portfolio, $$\\textsc {Legion}^{+}$$ L E G I O N + , solves 15% more benchmarks than Corral with similar computational resource constraints (i.e. each verifier in the portfolio is run with a time budget that is half of the time budget of Corral ). Moreover, it is found to be $$2.9\\times $$ 2.9 × faster than Corral on benchmarks that are solved by both Corral and $$\\textsc {Legion}^{+}$$ L E G I O N + .", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_15"}, {"primary_key": "1624190", "vector": [], "sparse_vector": [], "title": "Does a Program Yield the Right Distribution? - Verifying Probabilistic Programs via Generating Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Abstract We study discrete probabilistic programs with potentially unbounded looping behaviors over an infinite state space. We present, to the best of our knowledge, the first decidability result for the problem of determining whether such a program generates exactly a specified distribution over its outputs (provided the program terminates almost-surely). The class of distributions that can be specified in our formalism consists of standard distributions (geometric, uniform, etc.) and finite convolutions thereof. Our method relies on representing these (possibly infinite-support) distributions as probability generating functions which admit effective arithmetic operations. We have automated our techniques in a tool called $$\\textsc {Prodigy}$$ PRODIGY , which supports automatic invariance checking, compositional reasoning of nested loops, and efficient queries to the output distribution, as demonstrated by experiments.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_5"}, {"primary_key": "1624191", "vector": [], "sparse_vector": [], "title": "Hemiola: A DSL and Verification Tools to Guide Design and Proof of Hierarchical Cache-Coherence Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract Cache-coherence protocols have been one of the greatest challenges in formal verification of hardware, due to their central complication of executing multiple memory-access transactions concurrently within a distributed message-passing system. In this paper, we introduce Hemiola, a framework embedded in Coq that guides the user to design protocols that never experience inconsistent interleavings while handling transactions concurrently. The framework provides a DSL, where any protocol designed in the DSL always satisfies the serializability property, allowing a user to verify the protocol assuming that transactions are executed one-at-a-time. Hemiola also provides a novel invariant proof method, for protocols designed in Hemiola, that only requires considering execution histories without interleaved memory accesses. We used <PERSON><PERSON><PERSON> to design and prove hierarchical MSI and MESI protocols as case studies. We also demonstrated that the case-study protocols are hardware-synthesizable, by using a compilation/synthesis toolchain targeting FPGAs.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_16"}, {"primary_key": "1624192", "vector": [], "sparse_vector": [], "title": "Explaining Hyperproperty Violations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Abstract Hyperproperties relate multiple computation traces to each other. Model checkers for hyperproperties thus return, in case a system model violates the specification, a set of traces as a counterexample. Fixing the erroneous relations between traces in the system that led to the counterexample is a difficult manual effort that highly benefits from additional explanations. In this paper, we present an explanation method for counterexamples to hyperproperties described in the specification logic HyperLTL. We extend <PERSON><PERSON><PERSON> and <PERSON>’s definition of actual causality to sets of traces witnessing the violation of a HyperLTL formula, which allows us to identify the events that caused the violation. We report on the implementation of our method and show that it significantly improves on previous approaches for analyzing counterexamples returned by HyperLTL model checkers.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_20"}, {"primary_key": "1624193", "vector": [], "sparse_vector": [], "title": "Verified Erasure Correction in Coq with MathComp and VST.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Abstract Most methods of data transmission and storage are prone to errors, leading to data loss. Forward erasure correction (FEC) is a method to allow data to be recovered in the presence of errors by encoding the data with redundant parity information determined by an error-correcting code. There are dozens of classes of such codes, many based on sophisticated mathematics, making them difficult to verify using automated tools. In this paper, we present a formal, machine-checked proof of a C implementation of FEC based on Reed-Solomon coding. The C code has been actively used in network defenses for over 25 years, but the algorithm it implements was partially unpublished, and it uses certain optimizations whose correctness was unknown even to the code’s authors. We use <PERSON>q’s Mathematical Components library to prove the algorithm’s correctness and the Verified Software Toolchain to prove that the C program correctly implements this algorithm, connecting both using a modular, well-encapsulated structure that could easily be used to verify a high-speed, hardware version of this FEC. This is the first end-to-end, formal proof of a real-world FEC implementation; we verified all previously unknown optimizations and found a latent bug in the code.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_14"}, {"primary_key": "1624194", "vector": [], "sparse_vector": [], "title": "Sound Automation of Magic Wands.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Abstract The magic wand $$\\mathbin {-\\!\\!*}$$ -∗ (also called separating implication) is a separation logic connective commonly used to specify properties of partial data structures, for instance during iterative traversals. A footprint of a magic wand formula \"Equation missing\"is a state that, combined with any state in which A holds, yields a state in which <PERSON> holds. The key challenge of proving a magic wand (also called packaging a wand) is to find such a footprint. Existing package algorithms either have a high annotation overhead or, as we show in this paper, are unsound. We present a formal framework that precisely characterises a wide design space of possible package algorithms applicable to a large class of separation logics. We prove in Isabelle/HOL that our formal framework is sound and complete, and use it to develop a novel package algorithm that offers competitive automation and is sound. Moreover, we present a novel, restricted definition of wands and prove in Isabelle/HOL that it is possible to soundly combine fractions of such wands, which is not the case for arbitrary wands. We have implemented our techniques for the Viper language, and demonstrate that they are effective in practice.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_7"}, {"primary_key": "1624196", "vector": [], "sparse_vector": [], "title": "FORQ-Based Language Inclusion Formal Testing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Abstract We propose a novel algorithm to decide the language inclusion between (nondeterministic) Büchi automata, a PSpace -complete problem. Our approach, like others before, leverage a notion of quasiorder to prune the search for a counterexample by discarding candidates which are subsumed by others for the quasiorder. Discarded candidates are guaranteed to not compromise the completeness of the algorithm. The novelty of our work lies in the quasiorder used to discard candidates. We introduce FORQs (family of right quasiorders) that we obtain by adapting the notion of family of right congruences put forward by <PERSON><PERSON> and <PERSON> in 1993. We define a FORQ-based inclusion algorithm which we prove correct and instantiate it for a specific FORQ, called the structural FORQ, induced by the Büchi automaton to the right of the inclusion sign. The resulting implementation, called Forklift , scales up better than the state-of-the-art on a variety of benchmarks including benchmarks from program verification and theorem proving for word combinatorics. Artifact: https://doi.org/10.5281/zenodo.6552870", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_6"}, {"primary_key": "1624197", "vector": [], "sparse_vector": [], "title": "From Spot 2.0 to Spot 2.10: What&apos;s New?", "authors": ["<PERSON>", "<PERSON><PERSON>ne Renault", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>C<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Abstract Spot is a C++17 library for LTL and $$\\omega $$ ω -automata manipulation, with command-line utilities, and Python bindings. This paper summarizes its evolution over the past six years, since the release of Spot 2.0, which was the first version to support $$\\omega $$ ω -automata with arbitrary acceptance conditions, and the last version presented at a conference. Since then, Spot has been extended with several features such as acceptance transformations, alternating automata, games, LTL synthesis, and more. We also shed some lights on the data-structure used to store automata. Artifact: https://zenodo.org/record/6521395 .", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_9"}, {"primary_key": "1624198", "vector": [], "sparse_vector": [], "title": "Reasoning About Data Trees Using CHCs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract Reasoning about data structures requires powerful logics supporting the combination of structural and data properties. We define a new logic called Mso-D (Monadic Second-Order logic with Data) as an extension of standard Mso on trees with predicates of the desired data logic. We also define a new class of symbolic data tree automata ( Sdta s) to deal with data trees using a simple machine. Mso-D and Sdta s are both Turing-powerful, and their high expressiveness is necessary to deal with interesting data structures. We cope with undecidability by encoding Sdta executions as a system of CHCs (Constrained Horn Clauses) , and solving the resulting system using off-the-shelf solvers. We also identify a fragment of Mso-D whose satisfiability can be effectively reduced to the emptiness problem for Sdta s. This fragment is very expressive since it allows us to characterize a variety of data trees from the literature, solving certain infinite-state games, etc. We implement this reduction in a prototype tool that combines an Mso decision procedure over trees ( Mona ) with a CHC engine (Z3), and use this tool to conduct several experiments, demonstrating the effectiveness of our approach across different problem domains.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_13"}, {"primary_key": "1624199", "vector": [], "sparse_vector": [], "title": "PoS4MPC: Automated Security Policy Synthesis for Secure Multi-party Computation.", "authors": ["<PERSON><PERSON>", "Fu Song", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Abstract Secure multi-party computation (MPC) is a promising technique for privacy-persevering applications. A number of MPC frameworks have been proposed to reduce the burden of designing customized protocols, allowing non-experts to quickly develop and deploy MPC applications. To improve performance, recent MPC frameworks allow users to declare variables secret only for these which are to be protected. However, in practice, it is usually highly non-trivial for non-experts to specify secret variables: declaring too many degrades the performance while declaring too less compromises privacy. To address this problem, in this work we propose an automated security policy synthesis approach to declare as few secret variables as possible but without compromising security. Our approach is a synergistic integration of type inference and symbolic reasoning. The former is able to quickly infer a sound—but sometimes conservative—security policy, whereas the latter allows to identify secret variables in a security policy that can be declassified in a precise manner. Moreover, the results from symbolic reasoning are fed back to type inference to refine the security types even further. We implement our approach in a new tool PoS4MPC . Experimental results on five typical MPC applications confirm the efficacy of our approach.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_19"}, {"primary_key": "1624201", "vector": [], "sparse_vector": [], "title": "Information Flow Guided Synthesis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Abstract Compositional synthesis relies on the discovery of assumptions, i.e., restrictions on the behavior of the remainder of the system that allow a component to realize its specification. In order to avoid losing valid solutions, these assumptions should be necessary conditions for realizability. However, because there are typically many different behaviors that realize the same specification, necessary behavioral restrictions often do not exist. In this paper, we introduce a new class of assumptions for compositional synthesis, which we call information flow assumptions . Such assumptions capture an essential aspect of distributed computing, because components often need to act upon information that is available only in other components. The presence of a certain flow of information is therefore often a necessary requirement, while the actual behavior that establishes the information flow is unconstrained. In contrast to behavioral assumptions, which are properties of individual computation traces, information flow assumptions are hyperproperties , i.e., properties of sets of traces. We present a method for the automatic derivation of information-flow assumptions from a temporal logic specification of the system. We then provide a technique for the automatic synthesis of component implementations based on information flow assumptions. This provides a new compositional approach to the synthesis of distributed systems. We report on encouraging first experiments with the approach, carried out with the BoSyHyper synthesis tool.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_25"}, {"primary_key": "1624202", "vector": [], "sparse_vector": [], "title": "Shared Certificates for Neural Network Verification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Abstract Existing neural network verifiers compute a proof that each input is handled correctly under a given perturbation by propagating a symbolic abstraction of reachable values at each layer. This process is repeated from scratch independently for each input (e.g., image) and perturbation (e.g., rotation), leading to an expensive overall proof effort when handling an entire dataset. In this work, we introduce a new method for reducing this verification cost without losing precision based on a key insight that abstractions obtained at intermediate layers for different inputs and perturbations can overlap or contain each other. Leveraging our insight, we introduce the general concept of shared certificates, enabling proof effort reuse across multiple inputs to reduce overall verification costs. We perform an extensive experimental evaluation to demonstrate the effectiveness of shared certificates in reducing the verification cost on a range of datasets and attack specifications on image classifiers including the popular patch and geometric perturbations. We release our implementation at https://github.com/eth-sri/proof-sharing .", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_7"}, {"primary_key": "1624203", "vector": [], "sparse_vector": [], "title": "Randomized Synthesis for Diversity and Cost Constraints with Control Improvisation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Abstract In many synthesis problems, it can be essential to generate implementations which not only satisfy functional constraints but are also randomized to improve variety, robustness, or unpredictability. The recently-proposed framework of control improvisation (CI) provides techniques for the correct-by-construction synthesis of randomized systems subject to hard and soft constraints. However, prior work on CI has focused on qualitative specifications, whereas in robotic planning and other areas we often have quantitative quality metrics which can be traded against each other. For example, a designer of a patrolling security robot might want to know by how much the average patrol time needs to be increased in order to ensure that a particular aspect of the robot’s route is sufficiently diverse and hence unpredictable. In this paper, we enable this type of application by generalizing the CI problem to support quantitative soft constraints which bound the expected value of a given cost function, and randomness constraints which enforce diversity of the generated traces with respect to a given label function. We establish the basic theory of labelled quantitative CI problems, and develop efficient algorithms for solving them when the specifications are encoded by finite automata. We also provide an approximate improvisation algorithm based on constraint solving for any specifications encodable as Boolean formulas. We demonstrate the utility of our problem formulation and algorithms with experiments applying them to generate diverse near-optimal plans for robotic planning problems.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_26"}, {"primary_key": "1624204", "vector": [], "sparse_vector": [], "title": "A Scalable Shannon Entropy Estimator.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract Quantified information flow (QIF) has emerged as a rigorous approach to quantitatively measure confidentiality; the information-theoretic underpinning of QIF allows the end-users to link the computed quantities with the computational effort required on the part of the adversary to gain access to desired confidential information. In this work, we focus on the estimation of Shannon entropy for a given program $$\\varPi $$ Π . As a first step, we focus on the case wherein a Boolean formula $$\\varphi (X,Y)$$ φ ( X , Y ) captures the relationship between inputs X and output Y of $$\\varPi $$ Π . Such formulas $$\\varphi (X,Y)$$ φ ( X , Y ) have the property that for every valuation to X , there exists exactly one valuation to Y such that $$\\varphi $$ φ is satisfied. The existing techniques require $$\\mathcal {O}(2^m)$$ O ( 2 m ) model counting queries, where $$m = |Y|$$ m = | Y | . We propose the first efficient algorithmic technique, called $$\\mathsf {EntropyEstimation}$$ EntropyEstimation to estimate the Shannon entropy of $$\\varphi $$ φ with PAC-style guarantees, i.e., the computed estimate is guaranteed to lie within a $$(1\\pm \\varepsilon )$$ ( 1 ± ε ) -factor of the ground truth with confidence at least $$1-\\delta $$ 1 - δ . Furthermore, $$\\mathsf {EntropyEstimation}$$ EntropyEstimation makes only $$\\mathcal {O}(\\frac{min(m,n)}{\\varepsilon ^2})$$ O ( m i n ( m , n ) ε 2 ) counting and sampling queries, where $$m = |Y|$$ m = | Y | , and $$n = |X|$$ n = | X | , thereby achieving a significant reduction in the number of model counting queries. We demonstrate the practical efficiency of our algorithmic framework via a detailed experimental evaluation. Our evaluation demonstrates that the proposed framework scales to the formulas beyond the reach of the previously known approaches.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_18"}, {"primary_key": "1624205", "vector": [], "sparse_vector": [], "title": "RINO: Robust INner and Outer Approximated Reachability of Neural Networks Controlled Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract We present a unified approach, implemented in the RINO tool, for the computation of inner and outer-approximations of reachable sets of discrete-time and continuous-time dynamical systems, possibly controlled by neural networks with differentiable activation functions. RINO combines a zonotopic set representation with generalized mean-value AE extensions to compute under and over-approximations of the robust range of differentiable functions, and applies these techniques to the particular case of learning-enabled dynamical systems. The AE extensions require an efficient and accurate evaluation of the function and its Jacobian with respect to the inputs and initial conditions. For continuous-time systems, possibly controlled by neural networks, the function to evaluate is the solution of the dynamical system. It is over-approximated in RINO using Taylor methods in time coupled with a set-based evaluation with zonotopes. We demonstrate the good performances of RINO compared to state-of-the art tools Verisig 2.0 and ReachNN* on a set of classical benchmark examples of neural network controlled closed loop systems. For generally comparable precision to Verisig 2.0 and higher precision than ReachNN*, RINO is always at least one order of magnitude faster, while also computing the more involved inner-approximations that the other tools do not compute.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_25"}, {"primary_key": "1624206", "vector": [], "sparse_vector": [], "title": "MoGym: Using Formal Models for Training and Verifying Decision-making Agents.", "authors": ["<PERSON>o P<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract M o G ym , is an integrated toolbox enabling the training and verification of machine-learned decision-making agents based on formal models, for the purpose of sound use in the real world. Given a formal representation of a decision-making problem in the JANI format and a reach-avoid objective, M o G ym (a) enables training a decision-making agent with respect to that objective directly on the model using reinforcement learning (RL) techniques, and (b) it supports rigorous assessment of the quality of the induced decision-making agent by means of deep statistical model checking (DSMC). M o G ym implements the standard interface for training environments established by OpenAI Gym, thereby connecting to the vast body of existing work in the RL community. In return, it makes accessible the large set of existing JANI model checking benchmarks to machine learning research. It thereby contributes an efficient feedback mechanism for improving in particular reinforcement learning algorithms. The connective part is implemented on top of Momba. For the DSMC quality assurance of the learned decision-making agents, a variant of the statistical model checker modes of the M odest T oolset is leveraged, which has been extended by two new resolution strategies for non-determinism when encountered during statistical evaluation.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_21"}, {"primary_key": "1624207", "vector": [], "sparse_vector": [], "title": "Verifying Fairness in Quantum Machine Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract Due to the beyond-classical capability of quantum computing, quantum machine learning is applied independently or embedded in classical models for decision making, especially in the field of finance. Fairness and other ethical issues are often one of the main concerns in decision making. In this work, we define a formal framework for the fairness verification and analysis of quantum machine learning decision models, where we adopt one of the most popular notions of fairness in the literature based on the intuition—any two similar individuals must be treated similarly and are thus unbiased. We show that quantum noise can improve fairness and develop an algorithm to check whether a (noisy) quantum machine learning model is fair. In particular, this algorithm can find bias kernels of quantum data (encoding individuals) during checking. These bias kernels generate infinitely many bias pairs for investigating the unfairness of the model. Our algorithm is designed based on a highly efficient data structure—Tensor Networks—and implemented on Google’s TensorFlow Quantum. The utility and effectiveness of our algorithm are confirmed by the experimental results, including income prediction and credit scoring on real-world data, for a class of random (noisy) quantum decision models with 27 qubits ( $$2^{27}$$ 227 -dimensional state space) tripling ( $$2^{18}$$ 218 times more than) that of the state-of-the-art algorithms for verifying quantum machine learning models.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_20"}, {"primary_key": "1624208", "vector": [], "sparse_vector": [], "title": "Program Verification with Constrained Horn Clauses (Invited Paper).", "authors": ["<PERSON><PERSON>"], "summary": "Abstract Many problems in program verification, Model Checking, and type inference are naturally expressed as satisfiability of a verification condition expressed in a fragment of First-Order Logic called Constrained Horn Clauses (CHC). This transforms program analysis and verification tasks to the realm of first order satisfiability and into the realm of SMT solvers. In this paper, we give a brief overview of how CHCs capture verification problems for sequential imperative programs, and discuss CHC solving algorithm underlying the Spacer engine of SMT-solver Z3.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_2"}, {"primary_key": "1624210", "vector": [], "sparse_vector": [], "title": "Complementing Büchi Automata with <PERSON><PERSON>.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract We present the tool Ranker for complementing Büchi automata (BAs). <PERSON><PERSON> builds on our previous optimizations of rank-based BA complementation and pushes them even further using numerous heuristics to produce even smaller automata. Moreover, it contains novel optimizations of specialized constructions for complementing (i) inherently weak automata and (ii) semi-deterministic automata, all delivered in a robust tool. The optimizations significantly improve the usability of <PERSON><PERSON> , as shown in an extensive experimental evaluation with real-world benchmarks, where <PERSON><PERSON> produced in the majority of cases a strictly smaller complement than other state-of-the-art tools.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_10"}, {"primary_key": "1624211", "vector": [], "sparse_vector": [], "title": "Affine Loop Invariant Generation via Matrix Algebra.", "authors": ["Yucheng Ji", "Hongfei Fu", "<PERSON>", "<PERSON><PERSON> Chen"], "summary": "Abstract Loop invariant generation, which automates the generation of assertions that always hold at the entry of a while loop, has many important applications in program analysis and formal verification. In this work, we target an important category of while loops, namely affine while loops, that are unnested while loops with affine loop guards and variable updates. Such a class of loops widely exists in many programs yet still lacks a general but efficient approach to invariant generation. We propose a novel matrix-algebra approach to automatically synthesizing affine inductive invariants in the form of an affine inequality. The main novelty of our approach is that (i) the approach is general in the sense that it theoretically addresses all the cases of affine invariant generation over an affine while loop, and (ii) it can be efficiently automated through matrix-algebra (such as eigenvalue, matrix inverse) methods. The details of our approach are as follows. First, for the case where the loop guard is a tautology (i.e., ‘ true ’), we show that the eigenvalues and their eigenvectors of the matrices derived from the variable updates of the loop body encompass all meaningful affine inductive invariants. Second, for the more general case where the loop guard is a conjunction of affine inequalities, our approach completely addresses the invariant-generation problem by first establishing through matrix inverse the relationship between the invariants and a key parameter in the application of <PERSON><PERSON> lemma, then solving the feasible domain of the key parameter from the inductive conditions, and finally illustrating that a finite number of values suffices for the key parameter w.r.t a tightness condition for the invariants to be generated. Experimental results show that compared with previous approaches, our approach generates much more accurate affine inductive invariants over affine while loops from existing and new benchmarks within a few seconds, demonstrating the generality and efficiency of our approach.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_13"}, {"primary_key": "1624212", "vector": [], "sparse_vector": [], "title": "Trainify: A CEGAR-Driven Training and Verification Framework for Safe Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Abstract Deep Reinforcement Learning (DRL) has demonstrated its strength in developing intelligent systems. These systems shall be formally guaranteed to be trustworthy when applied to safety-critical domains, which is typically achieved by formal verification performed after training. This train-then-verify process has two limits: (i) trained systems are difficult to formally verify due to their continuous and infinite state space and inexplicable AI components ( i.e. , deep neural networks), and (ii) the ex post facto detection of bugs increases both the time- and money-wise cost of training and deployment. In this paper, we propose a novel verification-in-the-loop training framework called Trainify for developing safe DRL systems driven by counterexample-guided abstraction and refinement. Specifically, Trainify trains a DRL system on a finite set of coarsely abstracted but efficiently verifiable state spaces. When verification fails, we refine the abstraction based on returned counterexamples and train again on the finer abstract states. The process is iterated until all predefined properties are verified against the trained system. We demonstrate the effectiveness of our framework on six classic control systems. The experimental results show that our framework yields more reliable DRL systems with provable guarantees without sacrificing system performance such as cumulative reward and robustness than conventional DRL approaches.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_10"}, {"primary_key": "1624213", "vector": [], "sparse_vector": [], "title": "Specification-Guided Learning of Nash Equilibria with High Social Welfare.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract Reinforcement learning has been shown to be an effective strategy for automatically training policies for challenging control problems. Focusing on non-cooperative multi-agent systems, we propose a novel reinforcement learning framework for training joint policies that form a Nash equilibrium. In our approach, rather than providing low-level reward functions, the user provides high-level specifications that encode the objective of each agent. Then, guided by the structure of the specifications, our algorithm searches over policies to identify one that provably forms an $$\\epsilon $$ ϵ -Nash equilibrium (with high probability). Importantly, it prioritizes policies in a way that maximizes social welfare across all agents. Our empirical evaluation demonstrates that our algorithm computes equilibrium policies with high social welfare, whereas state-of-the-art baselines either fail to compute Nash equilibria or compute ones with comparatively lower social welfare.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_17"}, {"primary_key": "1624214", "vector": [], "sparse_vector": [], "title": "Abstraction-Refinement for Hierarchical Probabilistic Models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> T. J. <PERSON>"], "summary": "Abstract Markov decision processes are a ubiquitous formalism for modelling systems with non-deterministic and probabilistic behavior. Verification of these models is subject to the famous state space explosion problem. We alleviate this problem by exploiting a hierarchical structure with repetitive parts. This structure not only occurs naturally in robotics, but also in probabilistic programs describing, e.g., network protocols. Such programs often repeatedly call a subroutine with similar behavior. In this paper, we focus on a local case, in which the subroutines have a limited effect on the overall system state. The key ideas to accelerate analysis of such programs are (1) to treat the behavior of the subroutine as uncertain and only remove this uncertainty by a detailed analysis if needed, and (2) to abstract similar subroutines into a parametric template, and then analyse this template. These two ideas are embedded into an abstraction-refinement loop that analyses hierarchical MDPs. A prototypical implementation shows the efficacy of the approach.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_6"}, {"primary_key": "1624215", "vector": [], "sparse_vector": [], "title": "Capture, Analyze, Diagnose: Realizability Checking Of Requirements in FRET.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Abstract Requirements formalization has become increasingly popular in industrial settings as an effort to disambiguate designs and optimize development time and costs for critical system components. Formal requirements elicitation also enables the employment of analysis tools to prove important properties, such as consistency and realizability. In this paper, we present the realizability analysis framework that we developed as part of the Formal Requirements Elicitation Tool ( FRET ). Our framework prioritizes usability, and employs state-of-the-art analysis algorithms that support infinite theories. We demonstrate the workflow for realizability checking, showcase the diagnosis process that supports visualization of conflicts between requirements and simulation of counterexamples, and discuss results from industrial-level case studies.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_24"}, {"primary_key": "1624217", "vector": [], "sparse_vector": [], "title": "The Lattice-Theoretic Essence of Property Directed Reachability Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract We present LT-PDR , a lattice-theoretic generalization of <PERSON>’s property directed reachability analysis (PDR) algorithm. LT-PDR identifies the essence of PDR to be an ingenious combination of verification and refutation attempts based on the <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> theorems. We introduce four concrete instances of LT-PDR, derive their implementation from a generic Haskell implementation of LT-PDR, and experimentally evaluate them. We also present a categorical structural theory that derives these instances.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_12"}, {"primary_key": "1624219", "vector": [], "sparse_vector": [], "title": "Automated Expected Amortised Cost Analysis of Probabilistic Data Structures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract In this paper, we present the first fully-automated expected amortised cost analysis of self-adjusting data structures, that is, of randomised splay trees , randomised splay heaps and randomised meldable heaps , which so far have only (semi-)manually been analysed in the literature. Our analysis is stated as a type-and-effect system for a first-order functional programming language with support for sampling over discrete distributions, non-deterministic choice and a ticking operator. The latter allows for the specification of fine-grained cost models. We state two soundness theorems based on two different—but strongly related—typing rules of ticking, which account differently for the cost of non-terminating computations. Finally we provide a prototype implementation able to fully automatically analyse the aforementioned case studies.\"Image missing\"", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_4"}, {"primary_key": "1624220", "vector": [], "sparse_vector": [], "title": "Divide-and-Conquer Determinization of Büchi Automata Based on SCC Decomposition.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Feng", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Abstract The determinization of a nondeterministic Büchi automaton (NBA) is a fundamental construction of automata theory, with applications to probabilistic verification and reactive synthesis. The standard determinization constructions, such as the ones based on the <PERSON><PERSON><PERSON><PERSON>’s approach, work on the whole NBA. In this work we propose a divide-and-conquer determinization approach. To this end, we first classify the strongly connected components (SCCs) of the given NBA as inherently weak, deterministic accepting, and nondeterministic accepting. We then present how to determinize each type of SCC independently from the others; this results in an easier handling of the determinization algorithm that takes advantage of the structure of that SCC. Once all SCCs have been determinized, we show how to compose them so to obtain the final equivalent deterministic Emerson-Lei automaton, which can be converted into a deterministic Rabin automaton without blow-up of states and transitions. We implement our algorithm in our tool COLA and empirically evaluate COLA with the state-of-the-art tools Spot and Owl on a large set of benchmarks from the literature. The experimental results show that our prototype COLA outperforms Spot and Owl regarding the number of states and transitions.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_8"}, {"primary_key": "1624223", "vector": [], "sparse_vector": [], "title": "Murxla: A Modular and Highly Extensible API Fuzzer for SMT Solvers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Abstract SMT solvers are highly complex pieces of software with performance, robustness, and correctness as key requirements. Complementing traditional testing techniques for these solvers with randomized stress testing has been shown to be quite effective. Recent work has showcased the value of input fuzzing for finding issues, but this approach typically does not comprehensively test a solver’s API. Previous work on model-based API fuzzing was tailored to a single solver and a small subset of SMT-LIB. We present Murxla, a comprehensive, modular, and highly extensible model-based API fuzzer for SMT solvers. Murxla randomly generates valid sequences of solver API calls based on a customizable API model, with full support for the semantics and features of SMT-LIB. It is solver-agnostic but extensible to allow for solver-specific testing and supports option fuzzing, cross-checking with other solvers, translation to SMT-LIBv2, and SMT-LIBv2 input fuzzing. Our evaluation confirms its efficacy in finding issues in multiple state-of-the-art SMT solvers.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_5"}, {"primary_key": "1624224", "vector": [], "sparse_vector": [], "title": "Even Faster Conflicts and Lazier Reductions for String Solvers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract In the past decade, satisfiability modulo theories (SMT) solvers have been extended to support the theory of strings and regular expressions. This theory has proven to be useful in a wide range of applications in academia and industry. To accommodate the expressive nature of string constraints used in those applications, string solvers use a multi-layered architecture where extended operators are reduced to a set of core operators. These reductions, however, are often costly to reason about. In this work, we propose new techniques for eagerly discovering conflicts based on equality reasoning and lazily avoiding reductions for certain extended functions based on lightweight reasoning. We present a strategy for integrating and scheduling these techniques in a CDCL $$(T)$$ ( T ) -based theory solver for strings and regular expressions. We implement the techniques and the strategy in cvc 5, a state-of-the-art SMT solver, and show that they lead to a significant performance improvement.\"Image missing\"\"Image missing\"", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_11"}, {"primary_key": "1624225", "vector": [], "sparse_vector": [], "title": "Synthesis and Analysis of Petri Nets from Causal Specifications.", "authors": ["<PERSON><PERSON>"], "summary": "Abstract Petri nets are one of the most prominent system-level formalisms for the specification of causality in concurrent, distributed, or multi-agent systems. This formalism is abstract enough to be analyzed using theoretical tools, and at the same time, concrete enough to eliminate ambiguities that would arise at implementation level. One interesting feature of Petri nets is that they can be studied from the point of view of true concurrency, where causal scenarios are specified using partial orders, instead of approaches based on interleaving. On the other hand, message sequence chart (MSC) languages, are a standard formalism for the specification of causality from a purely behavioral perspective. In other words, this formalism specifies a set of causal scenarios between actions of a system, without providing any implementation-level details about the system. In this work, we establish several new connections between MSC languages and Petri nets, and show that several computational problems involving these formalisms are decidable. Our results fill some gaps in the literature that had been open for several years. To obtain our results we develop new techniques in the realm of slice automata theory, a framework introduced one decade ago in the study of the partial order behavior of bounded Petri nets. These techniques can also be applied to establish connections between Petri nets and other well studied behavioral formalisms, such as the notion of <PERSON><PERSON><PERSON><PERSON><PERSON> trace languages.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_22"}, {"primary_key": "1624226", "vector": [], "sparse_vector": [], "title": "Example Guided Synthesis of Linear Approximations for Neural Network Verification.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Abstract Linear approximations of nonlinear functions have a wide range of applications such as rigorous global optimization and, recently, verification problems involving neural networks. In the latter case, a linear approximation must be hand-crafted for the neural network’s activation functions. This hand-crafting is tedious, potentially error-prone, and requires an expert to prove the soundness of the linear approximation. Such a limitation is at odds with the rapidly advancing deep learning field – current verification tools either lack the necessary linear approximation, or perform poorly on neural networks with state-of-the-art activation functions. In this work, we consider the problem of automatically synthesizing sound linear approximations for a given neural network activation function. Our approach is example-guided : we develop a procedure to generate examples, and then we leverage machine learning techniques to learn a (static) function that outputs linear approximations. However, since the machine learning techniques we employ do not come with formal guarantees, the resulting synthesized function may produce linear approximations with violations. To remedy this, we bound the maximum violation using rigorous global optimization techniques, and then adjust the synthesized linear approximation accordingly to ensure soundness. We evaluate our approach on several neural network verification tasks. Our evaluation shows that the automatically synthesized linear approximations greatly improve the accuracy (i.e., in terms of the number of verification problems solved) compared to hand-crafted linear approximations in state-of-the-art neural network verification tools. An artifact with our code and experimental scripts is available at: https://zenodo.org/record/6525186#.Yp51L9LMIzM . \"Image missing\"\"Image missing\"", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_8"}, {"primary_key": "1624227", "vector": [], "sparse_vector": [], "title": "Verifying Neural Networks Against Backdoor Attacks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Abstract Neural networks have achieved state-of-the-art performance in solving many problems, including many applications in safety/security-critical systems. Researchers also discovered multiple security issues associated with neural networks. One of them is backdoor attacks, i.e., a neural network may be embedded with a backdoor such that a target output is almost always generated in the presence of a trigger. Existing defense approaches mostly focus on detecting whether a neural network is ‘backdoored’ based on heuristics, e.g., activation patterns. To the best of our knowledge, the only line of work which certifies the absence of backdoor is based on randomized smoothing, which is known to significantly reduce neural network performance. In this work, we propose an approach to verify whether a given neural network is free of backdoor with a certain level of success rate. Our approach integrates statistical sampling as well as abstract interpretation. The experiment results show that our approach effectively verifies the absence of backdoor or generates backdoor triggers.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_9"}, {"primary_key": "1624228", "vector": [], "sparse_vector": [], "title": "UCLID5: Multi-modal Formal Modeling, Verification, and Synthesis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Lin", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Abstract UCLID5 is a tool for the multi-modal formal modeling, verification, and synthesis of systems. It enables one to tackle verification problems for heterogeneous systems such as combinations of hardware and software, or those that have multiple, varied specifications, or systems that require hybrid modes of modeling. A novel aspect of UCLID5 is an emphasis on the use of syntax-guided and inductive synthesis to automate steps in modeling and verification. This tool paper presents new developments in the UCLID5 tool including new language features, integration with new techniques for syntax-guided synthesis and satisfiability solving, support for hyperproperties and combinations of axiomatic and operational modeling, demonstrations on new problem classes, and a robust implementation.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_27"}, {"primary_key": "1624230", "vector": [], "sparse_vector": [], "title": "A Billion SMT Queries a Day (Invited Paper).", "authors": ["<PERSON><PERSON><PERSON>gt<PERSON>"], "summary": "Abstract Amazon Web Services (AWS) is a cloud computing services provider that has made significant investments in applying formal methods to proving correctness of its internal systems and providing assurance of correctness to their end-users. In this paper, we focus on how we built abstractions and eliminated specifications to scale a verification engine for AWS access policies, <PERSON>elkova , to be usable by all AWS users. We present milestones from our journey from a thousand SMT invocations daily to an unprecedented billion SMT calls in a span of five years. In this paper, we talk about how the cloud is enabling application of formal methods, key insights into what made this scale of a billion SMT queries daily possible, and present some open scientific challenges for the formal methods community.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_1"}, {"primary_key": "1624233", "vector": [], "sparse_vector": [], "title": "Synthesizing Fair Decision Trees via Iterative Constraint Solving.", "authors": ["<PERSON><PERSON> Wang", "Yannan Li", "<PERSON>"], "summary": "Abstract Decision trees are increasingly used to make socially sensitive decisions, where they are expected to be both accurate and fair, but it remains a challenging task to optimize the learning algorithm for fairness in a predictable and explainable fashion. To overcome the challenge, we propose an iterative framework for choosing decision attributes, or features , at each level by formulating feature selection as a series of mixed integer optimization problems. Both fairness and accuracy requirements are encoded as numerical constraints and solved by an off-the-shelf constraint solver. As a result, the trade-off between fairness and accuracy is quantifiable. At a high level, our method can be viewed as a generalization of the entropy-based greedy search techniques such as and , and existing fair learning techniques such as and . Our experimental evaluation on six datasets, for which demographic parity is used as the fairness metric, shows that the method is significantly more effective in reducing bias than other methods while maintaining accuracy. Furthermore, compared to non-iterative constraint solving, our iterative approach is at least 10 times faster.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_18"}, {"primary_key": "1624235", "vector": [], "sparse_vector": [], "title": "STLmc: Robust STL Model Checking of Hybrid Systems Using SMT.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract We present the STLmc model checker for signal temporal logic (STL) properties of hybrid systems. The STLmc tool can perform STL model checking up to a robustness threshold for a wide range of hybrid systems. Our tool utilizes the refutation-complete SMT-based bounded model checking algorithm by reducing the robust STL model checking problem into Boolean STL model checking. If STLmc does not find a counterexample, the system is guaranteed to be correct up to the given bounds and robustness threshold. We demonstrate the effectiveness of STLmc on a number of hybrid system benchmarks.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1_26"}, {"primary_key": "1624236", "vector": [], "sparse_vector": [], "title": "End-to-End Mechanized Proof of an eBPF Virtual Machine for Micro-controllers.", "authors": ["<PERSON><PERSON>ao Yuan", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Abstract RIOT is a micro-kernel dedicated to IoT applications that adopts eBPF (extended Berkeley Packet Filters) to implement so-called femto-containers. As micro-controllers rarely feature hardware memory protection, the isolation of eBPF virtual machines (VM) is critical to ensure system integrity against potentially malicious programs. This paper shows how to directly derive, within the Coq proof assistant, the verified C implementation of an eBPF virtual machine from a Gallina specification. Leveraging the formal semantics of the CompCert C compiler, we obtain an end-to-end theorem stating that the C code of our VM inherits the safety and security properties of the Gallina specification. Our refinement methodology ensures that the isolation property of the specification holds in the verified C implementation. Preliminary experiments demonstrate satisfying performance.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2_15"}, {"primary_key": "1781057", "vector": [], "sparse_vector": [], "title": "Computer Aided Verification - 34th International Conference, CAV 2022, Haifa, Israel, August 7-10, 2022, Proceedings, Part I", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The open access CAV 2022 proceedings volume focuses on the latest research on computer aided formal analysis methods for hardware and software systems.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13185-1"}, {"primary_key": "1781058", "vector": [], "sparse_vector": [], "title": "Computer Aided Verification - 34th International Conference, CAV 2022, Haifa, Israel, August 7-10, 2022, Proceedings, Part II", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The open access CAV 2022 proceedings volume focuses on the latest research on computer aided formal analysis methods for hardware and software systems.", "published": "2022-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-031-13188-2"}]