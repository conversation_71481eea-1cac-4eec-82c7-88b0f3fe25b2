'use client';

import { useState, useEffect, useRef } from 'react';
import { ChevronRight, ChevronLeft, MoreVertical, X, Edit2, Trash2, GripHorizontal } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Paper } from '../survey/class-utils';
import { Toaster, toast } from 'react-hot-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";

import PaperModal from "../survey/paper-modal";
import { FolderSection } from './collect-folder';

// 收藏的论文类型
export interface CollectedPaper extends Paper {
  customName?: string; // 自定义名称
  folderId?: string; // 文件夹id
  collectedAt: number; // 收藏时间
}

// 文件夹类型
export interface Folder {
  id: string;
  name: string;
  createdAt: number;
}

// 收藏栏状态类型
export interface CollectionState {
  papers: CollectedPaper[];
  folders: Folder[];
  defaultFolderId?: string; // 默认文件夹ID
}

// 本地存储键名
const COLLECTION_STORAGE_KEY = 'arxivInsightCollection';

// 获取本地存储的收藏数据
const getCollectionFromStorage = (): CollectionState => {
  if (typeof window === 'undefined') return { papers: [], folders: [] };
  
  try {
    const storedData = localStorage.getItem(COLLECTION_STORAGE_KEY);
    if (storedData) {
      return JSON.parse(storedData);
    }
  } catch (error) {
    console.error('Failed to load collection data:', error);
  }
  
  return { papers: [], folders: [] };
};

// 保存收藏数据到本地存储
const saveCollectionToStorage = (collection: CollectionState) => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(COLLECTION_STORAGE_KEY, JSON.stringify(collection));
  } catch (error) {
    console.error('Failed to save collection data:', error);
    
    // 如果是超出配额错误，尝试使用 IndexedDB
    if (error instanceof DOMException && error.name === 'QuotaExceededError') {
      toast.error('本地存储空间不足，部分数据可能无法保存');
    }
  }
};

// 收藏栏组件属性
interface CollectionSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  addToCollection: (paper: Paper) => Promise<void>;
  isPaperCollected: (paperId: string) => boolean;
  collection: CollectionState;
  onCollectionUpdate?: (updatedCollection: CollectionState) => void;
  width: number;
  onWidthChange: (width: number) => void;
  isMobile: boolean;
}

// 收藏栏组件
export const CollectionSidebar = ({ 
  isOpen, 
  onToggle,
  addToCollection,
  isPaperCollected,
  collection,
  onCollectionUpdate,
  width,
  onWidthChange,
  isMobile
}: CollectionSidebarProps) => {
  const [localCollection, setLocalCollection] = useState<CollectionState>(collection);
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [paperToRename, setPaperToRename] = useState<CollectedPaper | null>(null);
  const [newPaperName, setNewPaperName] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [startWidth, setStartWidth] = useState(width);
  const [selectedPaper, setSelectedPaper] = useState<Paper | null>(null);
  // 添加拖拽相关状态
  const [draggedPaper, setDraggedPaper] = useState<CollectedPaper | null>(null);
  const [dragOverFolder, setDragOverFolder] = useState<string | undefined>(undefined);
  
  // 为分隔线添加状态
  const [folderSectionHeight, setFolderSectionHeight] = useState<number>(0);
  const [isDraggingDivider, setIsDraggingDivider] = useState(false);
  const [startY, setStartY] = useState(0);
  const [startHeight, setStartHeight] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 当外部 collection 变化时更新本地状态
  useEffect(() => {
    setLocalCollection(collection);
  }, [collection]);
  
  // 初始化文件夹区域高度为容器的三分之一
  useEffect(() => {
    if (containerRef.current) {
      const containerHeight = containerRef.current.clientHeight;
      const initialHeight = containerHeight / 3;
      setFolderSectionHeight(initialHeight);
    }
  }, [isOpen]);
  
  // 更新收藏到本地存储和父组件
  const updateCollection = (updatedCollection: CollectionState) => {
    setLocalCollection(updatedCollection);
    saveCollectionToStorage(updatedCollection);
    if (onCollectionUpdate) {
      onCollectionUpdate(updatedCollection);
    }
  };
  
  // 调整宽度的处理函数
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setStartX(e.clientX);
    setStartWidth(width);
    e.preventDefault();
  };

  // 处理分隔线拖动开始
  const handleDividerMouseDown = (e: React.MouseEvent) => {
    setIsDraggingDivider(true);
    setStartY(e.clientY);
    setStartHeight(folderSectionHeight);
    e.preventDefault();
  };

  // 绑定鼠标移动和松开事件（调整收藏栏宽度）
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;
      const newWidth = Math.max(250, Math.min(500, startWidth + (e.clientX - startX)));
      onWidthChange(newWidth);
    };

    const handleDividerMouseMove = (e: MouseEvent) => {
      if (!isDraggingDivider || !containerRef.current) return;
      const minHeight = 100;
      const maxHeight = containerRef.current.clientHeight - 150;
      
      const newHeight = Math.max(minHeight, Math.min(maxHeight, startHeight + (e.clientY - startY)));
      setFolderSectionHeight(newHeight);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsDraggingDivider(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    if (isDraggingDivider) {
      document.addEventListener('mousemove', handleDividerMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mousemove', handleDividerMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, startX, startWidth, onWidthChange, isDraggingDivider, startY, startHeight]);
  
  const handleRenamePaper = () => {
    if (!paperToRename || !newPaperName.trim()) {
      toast.error('名称不能为空');
      return;
    }
    
    const updatedPapers = localCollection.papers.map(paper => 
      paper.title === paperToRename.title ? { ...paper, customName: newPaperName.trim() } : paper
    );
    
    const updatedCollection = {
      ...localCollection,
      papers: updatedPapers
    };
    
    updateCollection(updatedCollection);
    
    setIsRenameDialogOpen(false);
    setPaperToRename(null);
    setNewPaperName('');
    toast.success('论文已重命名');
  };

  // 删除论文
  const handleDeletePaper = (paper: CollectedPaper) => {
    const updatedCollection = {
      ...localCollection,
      papers: localCollection.papers.filter(p => p.title !== paper.title)
    };
    
    updateCollection(updatedCollection);
    
    toast.success('论文已从收藏中移除');
  };

  // 移动论文到文件夹
  const handleMovePaperToFolder = (paper: CollectedPaper, folderId: string | undefined) => {
    const updatedPapers = localCollection.papers.map(p => 
      p.title === paper.title ? { ...p, folderId } : p
    );
    
    const updatedCollection = {
      ...localCollection,
      papers: updatedPapers
    };
    
    updateCollection(updatedCollection);
    
    toast.success(folderId ? '已移动到文件夹' : '已移动到未分类');
  };

  // 获取当前显示的论文
  const getVisiblePapers = () => {
    if (!selectedFolder) {
      return localCollection.papers.filter(paper => !paper.folderId);
    }
    
    return localCollection.papers.filter(paper => paper.folderId === selectedFolder);
  };

  // 处理论文点击，显示论文卡片
  const handlePaperClick = (paper: Paper) => {
    // 先设置选中的论文
    setSelectedPaper(paper);
  };

  // 处理翻译更新（单击弹出卡片后如果翻译有更新，则更新到收藏中）
  const handleTranslationUpdate = (translation: string) => {
    if (!selectedPaper) return;

    const collectedPaperIndex = localCollection.papers.findIndex(p => p.title === selectedPaper.title);
    if (collectedPaperIndex !== -1) {
      const updatedPapers = [...localCollection.papers];
      updatedPapers[collectedPaperIndex] = {
        ...updatedPapers[collectedPaperIndex],
        translation
      };

      const updatedCollection = {
        ...localCollection,
        papers: updatedPapers
      };

      updateCollection(updatedCollection);
    }
  };

  // 当论文卡片关闭时检查是否有新的翻译结果并更新到收藏中
  const handlePaperModalClose = () => {
    setSelectedPaper(null);
  };

  // 处理拖拽开始
  const handleDragStart = (e: React.DragEvent, paper: CollectedPaper) => {
    setDraggedPaper(paper);
    e.dataTransfer.setData('text/plain', paper.title);
    e.dataTransfer.effectAllowed = 'move';
  };

  // 处理拖拽结束
  const handleDragEnd = () => {
    setDraggedPaper(null);
    setDragOverFolder(undefined);
  };

  // 处理拖拽到文件夹上
  const handleDragOver = (e: React.DragEvent, folderId: string | undefined) => {
    e.preventDefault();
    setDragOverFolder(folderId);
  };

  // 处理拖拽离开文件夹
  const handleDragLeave = () => {
    setDragOverFolder(undefined);
  };

  // 处理放置
  const handleDrop = (e: React.DragEvent, folderId: string | undefined) => {
    e.preventDefault();
    if (draggedPaper) {
      handleMovePaperToFolder(draggedPaper, folderId);
    }
    setDragOverFolder(undefined);
  };

  const visiblePapers = getVisiblePapers();

  return (
    <>
      {/* 书签样式按钮 - 根据收藏栏状态调整位置 */}
      {!isMobile && (
        <div 
          onClick={onToggle}
          className={`fixed z-50 cursor-pointer group shadow-lg transition-all duration-300 ${isOpen ? 'top-20' : 'top-20 left-0'}`}
          title={isOpen ? "关闭收藏栏" : "展开收藏栏"}
          style={isOpen ? { left: `${width}px` } : {}}
        >
          <div className="flex items-center">
            <div className="flex items-center justify-center h-10 w-9 bg-gray-400 dark:bg-gray-600 rounded-r-lg shadow-md group-hover:bg-gray-600 dark:group-hover:bg-gray-700 transition-colors">
              {isOpen ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="15 18 9 12 15 6"></polyline>
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                </svg>
              )}
            </div>
          </div>
        </div>
      )}

      <div className={`fixed left-0 top-0 h-full z-40 transition-all duration-300 ${isOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        {/* 收藏栏主体 */}
        <div className="flex h-full">
          <div 
            className="bg-background border-r shadow-md flex flex-col h-full overflow-hidden"
            style={{ width: `${width}px` }}
            ref={containerRef}
          >
            {/* 文件夹区域 */}
            <div 
              className="p-2 border-b overflow-auto" 
              style={{ height: `${folderSectionHeight}px` }}
            >
              {/* 可拖动的分隔线 */}
              <FolderSection 
                localCollection={localCollection}
                selectedFolder={selectedFolder}
                draggedPaper={draggedPaper}
                dragOverFolder={dragOverFolder}
                onSelectFolder={setSelectedFolder}
                updateCollection={updateCollection}
                handleDragOver={handleDragOver}
                handleDragLeave={handleDragLeave}
                handleDrop={handleDrop}
              />
            </div>
            
            <div 
              className="h-4 cursor-row-resize bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center justify-center"
              onMouseDown={handleDividerMouseDown}
            >
              <GripHorizontal className="h-4 w-4 text-gray-400" />
            </div>
            {/* 论文列表 */}
            <div className="flex-1 overflow-auto p-2">
              {visiblePapers.length === 0 && (  
                <p className="text-sm text-gray-500 text-center mt-4">暂无收藏论文</p>
              )}
              
              {/* 论文项 */}
              {visiblePapers.map((paper, idx) => (
                <div 
                  key={paper.title} 
                  className="mb-2 p-2 border rounded-md bg-gray-50 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-800 cursor-pointer"
                  onClick={() => handlePaperClick(paper)}
                  draggable
                  onDragStart={(e) => handleDragStart(e, paper)}
                  onDragEnd={handleDragEnd}
                >
                  <div className="flex justify-between items-start">
                    <div 
                      className={`text-sm font-medium hover:text-blue-600 truncate max-w-[${width}px]`}
                      title={paper.customName || paper.title}
                    >
                      <span className="text-sm text-gray-400 mr-1">{idx + 1}.</span>
                      {paper.customName || paper.title_translation || paper.title}
                    </div>
                    {/* 更多操作按钮 */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-7 w-7" onClick={(e) => e.stopPropagation()}>
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {/* 重命名论文 */}
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          setPaperToRename(paper);
                          // 优先使用自定义名称，其次使用中文翻译，最后使用原标题
                          setNewPaperName(paper.customName || paper.title_translation || paper.title);
                          setIsRenameDialogOpen(true);
                        }}>
                          <Edit2 className="h-4 w-4 mr-2" />
                          重命名
                        </DropdownMenuItem>

                        {/* 删除论文 */}
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleDeletePaper(paper);
                        }}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除
                        </DropdownMenuItem>
                        
                        {/* 移动到文件夹 */}
                        <DropdownMenuItem 
                          onSelect={e => e.preventDefault()}
                          className="px-2 py-1.5 text-sm"
                        >
                          <div className="w-full">
                            <p className="mb-1 text-gray-500">移动到文件夹（可拖拽）</p>
                            <div className="space-y-1 max-h-32 overflow-y-auto">
                              <div 
                                className={`p-1.5 rounded-md cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 ${!paper.folderId ? 'bg-gray-100 dark:bg-gray-800' : ''}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleMovePaperToFolder(paper, undefined);
                                }}
                              >
                                未分类
                              </div>
                              {localCollection.folders.map(folder => (
                                <div 
                                  key={folder.id}
                                  className={`p-1.5 rounded-md cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 ${paper.folderId === folder.id ? 'bg-gray-100 dark:bg-gray-800' : ''}`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleMovePaperToFolder(paper, folder.id);
                                  }}
                                >
                                  {folder.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <p className="text-xs text-gray-500 mt-0 truncate whitespace-nowrap">
                    {paper.source || 'arxiv'}
                    <span className="mx-2 text-gray-300">|</span>
                    发表：{paper.published.substring(0, 7)}
                    <span className="mx-2 text-gray-300">|</span>
                    收藏：{new Date(paper.collectedAt).toLocaleDateString().replace(/\//g, '-')}
                  </p>
                </div>
              ))}
            </div>
          </div>
          
          {/* 仅展示宽度调整控制条 */}
          {isOpen && !isMobile && (
            <div className="flex flex-col items-center">
              {/* 调整宽度控制条 */}
              <div 
                className="h-full w-2 cursor-col-resize hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center justify-center"
                onMouseDown={handleMouseDown}
                title="调整宽度"
              >
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* 当收藏栏打开时，添加遮罩层（仅在移动设备上） */}
      {isOpen && isMobile && (
        <div
          className="fixed inset-0 bg-black/50 z-30"
          onClick={onToggle}
        />
      )}
      
      {/* 重命名论文弹窗 */}
      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>重命名论文</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="论文名称"
              value={newPaperName}
              onChange={(e) => setNewPaperName(e.target.value)}
              className="mb-2"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleRenamePaper();
                }
              }}
            />
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">取消</Button>
            </DialogClose>
            <Button onClick={handleRenamePaper}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 论文卡片弹窗 */}
      {selectedPaper && (
        <PaperModal 
          paper={selectedPaper} 
          onClose={handlePaperModalClose}
          onTranslationUpdate={handleTranslationUpdate}
        />
      )}
    </>
  );
};

// 收藏按钮组件属性
export interface CollectButtonProps {
  paper: Paper;
  isCollected: boolean;
  onCollect: () => void;
}

// 收藏按钮组件
export const CollectButton = ({ paper, isCollected, onCollect }: CollectButtonProps) => {
  const [localIsCollected, setLocalIsCollected] = useState(isCollected);
  
  // 当外部 isCollected 变化时更新本地状态
  useEffect(() => {
    setLocalIsCollected(isCollected);
  }, [isCollected]);
  
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault(); // 阻止默认行为
    e.stopPropagation();
    // 立即更新按钮状态，提供更好的反馈
    setLocalIsCollected(!localIsCollected);
    // 调用外部处理函数
    onCollect();
  };
  
  return (
    <Button
      type="button"
      variant="ghost"
      size="icon"
      onClick={handleClick}
      className={`h-6 w-6 ${localIsCollected ? 'text-yellow-500' : 'text-gray-400 hover:text-gray-900'}`}
      title={localIsCollected ? '已收藏' : '收藏'}
      onMouseDown={(e) => e.preventDefault()} // 阻止 mousedown 事件
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill={localIsCollected ? "currentColor" : "none"} stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
      </svg>
    </Button>
  );
};

// 收藏管理器
export const useCollectionManager = () => {
  const [collection, setCollection] = useState<CollectionState>({ papers: [], folders: [] });
  const [isCollectionOpen, setIsCollectionOpen] = useState(false);
  
  // 加载收藏数据
  useEffect(() => {
    const loadCollection = () => {
      const loadedCollection = getCollectionFromStorage();
      setCollection(loadedCollection);
    };
    
    loadCollection();
    
    // 添加存储事件监听器，以便在其他组件更改存储时更新
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === COLLECTION_STORAGE_KEY) {
        loadCollection();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // 切换收藏栏显示状态
  const toggleCollection = () => {
    setIsCollectionOpen(prev => !prev);
  };

  // 检查论文是否已收藏
  const isPaperCollected = (title: string) => {
    return collection.papers.some(paper => paper.title === title);
  };

  // 添加论文到收藏
  const addToCollection = async (paper: Paper): Promise<void> => {
    // 检查是否已经收藏
    if (isPaperCollected(paper.title)) {
      toast.error('该论文已在收藏中');
      return;
    }

    // 先创建要添加的论文对象
    let paperToAdd: CollectedPaper = {
      ...paper,
      collectedAt: Date.now(),
      folderId: collection.defaultFolderId // 使用默认文件夹ID
    };

    // 先立即添加到收藏并更新UI，提高响应速度
    const initialCollection = {
      ...collection,
      papers: [...collection.papers, paperToAdd]
    };
    
    // 更新状态和本地存储
    setCollection(initialCollection);
    saveCollectionToStorage(initialCollection);
    
    // 显示成功消息
    toast.success('已添加到收藏');

    // 如果标题没有翻译，在后台异步处理翻译，不阻塞UI
    if (!paper.title_translation) {
      // 创建一个让 UI 可以保持响应的延迟
      await new Promise(resolve => setTimeout(resolve, 100));
      
      try {
        const translationResponse = await fetch('/api/translate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: [paper.title]
          }),
        });

        if (translationResponse.ok) {
          const data = await translationResponse.json();
          
          // 解析返回的翻译结果
          const translations = Array.isArray(data.translation) 
            ? data.translation
            : JSON.parse(data.translation);

          if (translations && translations.length > 0) {
            // 获取最新的 collection 状态，因为它可能已经被更新
            const currentCollection = getCollectionFromStorage();
            
            // 找到刚添加的论文并更新其翻译
            const updatedPapers = currentCollection.papers.map(p => 
              p.title === paper.title 
                ? { ...p, title_translation: translations[0] } 
                : p
            );
            
            const updatedCollectionWithTranslation = {
              ...currentCollection,
              papers: updatedPapers
            };
            
            // 更新状态和本地存储
            setCollection(updatedCollectionWithTranslation);
            saveCollectionToStorage(updatedCollectionWithTranslation);
          }
        }
      } catch (error) {
        console.error('标题翻译失败:', error);
      }
    }
  };

  // 移除论文从收藏
  const removeFromCollection = (title: string) => {
    const newCollection = {
      ...collection,
      papers: collection.papers.filter(paper => paper.title !== title)
    };
    
    // 保存到本地存储
    saveCollectionToStorage(newCollection);
    
    // 更新状态
    setCollection(newCollection);

    toast.success('已从收藏中移除');
  };
  
  // 更新整个收藏集合
  const updateCollection = (updatedCollection: CollectionState) => {
    setCollection(updatedCollection);
  };

  return {
    collection,
    isCollectionOpen,
    toggleCollection,
    isPaperCollected,
    addToCollection,
    removeFromCollection,
    updateCollection
  };
};

