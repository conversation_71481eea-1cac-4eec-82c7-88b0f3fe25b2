[{"primary_key": "3747714", "vector": [], "sparse_vector": [], "title": "A Formal Treatment of Multi-key Channels.", "authors": ["<PERSON>", "Sogol Ma<PERSON>heri"], "summary": "Secure channel protocols protect data transmission over a network from being overheard or tampered with. In the common abstraction, cryptographic models for channels involve a single key for ensuring the central security notions of confidentiality and integrity. The currently developed next version of the Transport Layer Security protocol, TLS 1.3, however introduces a key updating mechanism in order to deploy a sequence of multiple, possibly independent encryption keys in its channel sub-protocol. This design aims at achieving forward security, protecting prior communication after long-term key corruption, as well as security of individual channel phases even if the key in other phases is leaked (a property we denote as phase-key insulation). Neither of these security aspects has been treated formally in the context of cryptographic channels so far, leading to a current lack of techniques to evaluate such channel designs cryptographically. We approach this gap by introducing the first formal model of multi-key channels, where sender and receiver can update their shared secret key during the lifetime of the channel without interrupting the communication. We present modular, game-based notions for confidentiality and integrity, integrating forward security and phase-key insulation as two advanced security aspects. As we show, our framework of notions on the lower end of its hierarchy naturally connects to the existing notions of stateful encryption established for single-key channels. Like for classical channels, it further allows for generically composing chosen-ciphertext confidentiality from chosen-plaintext confidentiality and ciphertext integrity. We instantiate the strongest security notions in our model with a construction based on authenticated encryption with associated data and a pseudorandom function. Being comparatively close, our construction additionally enables us to discuss the TLS 1.3 record protocol design.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_20"}, {"primary_key": "3747715", "vector": [], "sparse_vector": [], "title": "Quantum Security of NMAC and Related Constructions - PRF Domain Extension Against Quantum attacks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove the security of NMAC, HMAC, AMAC, and the cascade construction with fixed input-length asquantum-securepseudo-random functions (PRFs). Namely, they are indistinguishable from a random oracle against any polynomial-time quantum adversary that can make quantum superposition queries. In contrast, many blockcipher-based PRFs including CBC-MAC were recently broken by quantum superposition attacks. Classical proof strategies for these constructions do not generalize to the quantum setting, and we observe that they sometimes even fail completely (e.g., the universal-hash then PRF paradigm for proving security of NMAC). Instead, we propose a direct hybrid argument as a new proof strategy (both classically and quantumly). We first show that a quantum-secure PRF is secure against key-recovery attacks, and remains secure under random leakage of the key. Next, as a key technical tool, we extend the oracle indistinguishability framework of Zhandry in two directions: we consider distributions onfunctionsrather than strings, and we also consider a relative setting, where an additional oracle, possibly correlated with the distributions, is given to the adversary as well. This enables a hybrid argument to prove the security of NMAC. Security proofs for other constructions follow similarly.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_10"}, {"primary_key": "3747716", "vector": [], "sparse_vector": [], "title": "Distinguisher-Dependent Simulation in Two Rounds and its Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We devise a novel simulation technique that makes black-box use of the adversary as well as the distinguisher. Using this technique we construct several round-optimal protocols, many of which were previously unknown even using non-black-box simulation techniques: Two-round witness indistinguishable (WI) arguments for\\(\\mathrm {NP}\\)from different assumptions than previously known. Two-round arguments and three-round arguments of knowledge for\\(\\mathrm {NP}\\)that achieve strong WI, witness hiding (WH) and distributional weak zero knowledge (WZK) properties in a setting where the instance is only determined by the prover in the last round of the interaction. The soundness of these protocols is guaranteed against adaptive provers. Three-round two-party computation satisfying input-indistinguishable security as well as a weaker notion of simulation security against malicious adversaries. Three-round extractable commitments with guaranteed correctness of extraction from polynomial hardness assumptions. Our three-round protocols can be based on DDH or QR or\\(\\text {N}^{\\text {th}}\\)residuosity and our two-round protocols require quasi-polynomial hardness of the same assumptions. In particular, prior to this work, two-round WI arguments for NP were only known based on assumptions such as the existence of trapdoor permutations, hardness assumptions on bilinear maps, or the existence of program obfuscation; we give the first construction based on (quasi-polynomial) DDH or QR or\\(\\text {N}^{\\text {th}}\\)residuosity. Our simulation technique bypasses known lower bounds on black-box simulation [Goldreich-Krawcyzk’96] by using the distinguisher’s output in a meaningful way. We believe that this technique is likely to find additional applications in the future.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_6"}, {"primary_key": "3747717", "vector": [], "sparse_vector": [], "title": "Compact Structure-Preserving Signatures with Almost Tight Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In structure-preserving cryptography, every building block shares the same bilinear groups. These groups must be generated for a specific, a priori fixed security level, and thus it is vital that the security reduction of all involved building blocks is as tight as possible. In this work, we present the first generic construction of structure-preserving signature schemes whose reduction cost is independent of the number of signing queries. Its chosen-message security is almost tightly reduced to the chosen-plaintext security of a structure-preserving public-key encryption scheme and the security of Groth-Sahai proof system. Technically, we adapt the adaptive partitioning technique by <PERSON><PERSON><PERSON><PERSON> (Eurocrypt 2017) to the setting of structure-preserving signature schemes. To achieve a structure-preserving scheme, our new variant of the adaptive partitioning technique relies only on generic group operations in the scheme itself. Interestingly, however, we will use non-generic operations during our security analysis. Instantiated over asymmetric bilinear groups, the security of our concrete scheme is reduced to the external <PERSON><PERSON><PERSON><PERSON> assumption with linear reduction cost in the security parameter, independently of the number of signing queries. The signatures in our schemes consist of a larger number of group elements than those in other non-tight schemes, but can be verified faster, assuming their security reduction loss is compensated by increasing the security parameter to the next standard level.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_19"}, {"primary_key": "3747718", "vector": [], "sparse_vector": [], "title": "Stronger Security for Reusable Garbled Circuits, General Definitions and Attacks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We construct a functional encryption scheme for circuits which simultaneously achieves and improves upon the security of the current best known, and incomparable, constructions from standard assumptions: reusable garbled circuits by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC 2013) [GKP+13] and predicate encryption for circuits by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (CRYPTO 2015) [GVW15]. Our scheme is secure based on the learning with errors (LWE) assumption. Our construction implies: A new construction for reusable garbled circuits that achieves stronger security than the only known prior construction [GKP+13]. A new construction for bounded collusion functional encryption with substantial efficiency benefits: our public parameters and ciphertext size incur anadditivegrowth of\\(O(Q^2)\\), whereQis the number of permissible queries (We note that due to a lower bound [AGVW13], the ciphertext size must necessarily grow withQ). Additionally, the ciphertext of our scheme issuccinct, in that it does not depend on the size of the circuit. By contrast, the prior best construction [GKP+13,GVW12] incurred amultiplicativeblowup of\\(O(Q^4)\\)in both the public parameters and ciphertext size. However, our scheme is secure in a weaker game than [GVW12]. Additionally, we show that existing LWE based predicate encryption schemes [AFV11,GVW15] are completely insecure against a general functional encryption adversary (i.e. in the “strong attribute hiding” game). We demonstrate three different attacks, the strongest of which is applicable even to the inner product predicate encryption scheme [AFV11]. Our attacks are practical and allow the attacker to completely recover\\(\\mathbf {x}\\)from its encryption\\(\\mathsf{Enc}(\\mathbf {x})\\)within a polynomial number of queries. This illustrates that the barrier between predicate and functional encryption is not just a limitation of proof techniques. We believe these attacks shed significant light on the barriers to achieving full fledged functional encryption from LWE, even for simple functionalities such as inner product zero testing [KSW08,AFV11]. Along the way, we develop a new proof technique that permits the simulator to program public parameters based on keys that will be requested in the future. This technique may be of independent interest.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_1"}, {"primary_key": "3747719", "vector": [], "sparse_vector": [], "title": "Topology-Hiding Computation on All Graphs.", "authors": ["<PERSON><PERSON>", "Rio LaVigne", "<PERSON><PERSON>"], "summary": "A distributed computation in which nodes are connected by a partial communication graph is calledtopology-hidingif it does not reveal information about the graph beyond what is revealed by the output of the function. Previous results have shown that topology-hiding computation protocols exist for graphs of constant degree and logarithmic diameter in the number of nodes [<PERSON><PERSON><PERSON>, TCC’15; <PERSON><PERSON> et al., Crypto’16] as well as for other graph families, such as cycles, trees, and low circumference graphs [Akavia-Moran, Eurocrypt’17], but the feasibility question for general graphs was open. In this work we positively resolve the above open problem: we prove that topology-hiding MPC is feasible forallgraphs under the Decisional <PERSON><PERSON><PERSON>-<PERSON> assumption. Our techniques employ random-walks to generate paths covering the graph, upon which we apply the Akavia-Moran topology-hiding broadcast for chain-graphs (paths). To prevent topology information revealed by the random-walk, we design multiple random-walks that, together, are locally identical to receiving at each round a message from each neighbors and sending back processed messages in a randomly permuted order.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_15"}, {"primary_key": "3747720", "vector": [], "sparse_vector": [], "title": "Quantum Non-malleability and Authentication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In encryption, non-malleability is a highly desirable property: it ensures that adversaries cannot manipulate the plaintext by acting on the ciphertext. In [6], <PERSON><PERSON><PERSON><PERSON> et al. gave a definition of non-malleability for the encryption of quantum data. In this work, we show that this definition is too weak, as it allows adversaries to “inject” plaintexts of their choice into the ciphertext. We give a new definition of quantum non-malleability which resolves this problem. Our definition is expressed in terms of entropic quantities, considers stronger adversaries, and does not assume secrecy. Rather, we prove thatquantum non-malleability implies secrecy; this is in stark contrast to the classical setting, where the two properties are completely independent. For unitary schemes, our notion of non-malleability is equivalent to encryption with a two-design (and hence also to the definition of [6]). Our techniques also yield new results regarding the closely-related task of quantum authentication. We show that “total authentication” (a notion recently proposed by <PERSON><PERSON><PERSON> et al. [18]) can be satisfied with two-designs, a significant improvement over the eight-design construction of [18]. We also show that, under a mild adaptation of the rejection procedure, both total authentication and our notion of non-malleability yield quantum authentication as defined by <PERSON><PERSON><PERSON> et al. [16].", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_11"}, {"primary_key": "3747721", "vector": [], "sparse_vector": [], "title": "Generic Transformations of Predicate Encodings: Constructions and Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Predicate encodings (<PERSON><PERSON>, <PERSON> 2014; <PERSON>, <PERSON>, <PERSON>, EUROCRYPT 2015), are symmetric primitives that can be used for building predicate encryption schemes. We give an algebraic characterization of the notion of privacy from predicate encodings, and explore several of its consequences. Specifically, we propose more efficient predicate encodings for boolean formulae and arithmetic span programs, and generic optimizations of predicate encodings. We define new constructions to build boolean combination of predicate encodings. We formalize the relationship between predicate encodings and pair encodings (Attrapadung, EUROCRYPT 2014), another primitive that can be transformed generically into predicate encryption schemes, and compare our constructions for boolean combinations of pair encodings with existing similar constructions from pair encodings. Finally, we demonstrate that our results carry to tag-based encodings (<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, SCN 2016).", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_2"}, {"primary_key": "3747722", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation for Turing Machines: Constant Overhead and Amortization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the asymptotic efficiency of indistinguishability obfuscation (\\(\\mathsf {i}\\mathcal {O}\\)) on two fronts: Obfuscation size:Present constructions of indistinguishability obfuscation (\\(\\mathsf {i}\\mathcal {O}\\)) create obfuscated programs where the size of the obfuscated program is at least a multiplicative factor of security parameter larger than the size of the original program. In this work, we construct the first\\(\\mathsf {i}\\mathcal {O}\\)scheme for (bounded-input) Turing machines that achieves only aconstantmultiplicative overhead in size. The constant in our scheme is, in fact, 2. Amortization:Suppose we want to obfuscate an arbitrary polynomial number of (bounded-input) Turing machines\\(M_1,\\ldots ,M_n\\). We ask whether it is possible to obfuscate\\(M_1,\\ldots ,M_n\\)using asingleapplication of an\\(\\mathsf {i}\\mathcal {O}\\)scheme for a circuit family where the size of any circuit isindependentofnas well the size of any Turing machine\\(M_i\\). In this work, we resolve this question in the affirmative, obtaining a new bootstrapping theorem for obfuscating arbitrarily many Turing machines. In order to obtain both of these results, we develop a new template for obfuscating Turing machines that is of independent interest and likely to find applications in future. The security of our results rely on the existence of sub-exponentially secure\\(\\mathsf {i}\\mathcal {O}\\)for circuits and re-randomizable encryption schemes.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_9"}, {"primary_key": "3747723", "vector": [], "sparse_vector": [], "title": "A New Approach to Round-Optimal Secure Multiparty Computation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a new approach towards constructing round-optimal secure multiparty computation (MPC) protocols against malicious adversaries without trusted setup assumptions. Our approach builds on ideas previously developed in the context of covert multiparty computation [<PERSON><PERSON> et al., FOCS’07] even though we do not seek covert security. Using our new approach, we obtain the following results: A five round MPC protocol based on the Decisional Diffie-Hellman (DDH) assumption. A four round MPC protocol based on one-way permutations and sub-exponentially secure DDH. This result isoptimalin the number of rounds. Previously, no four-round MPC protocol for general functions was known and five-round protocols were only known based on indistinguishability obfuscation (and some additional assumptions) [<PERSON><PERSON><PERSON> et al., EUROCRYPT’16].", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_16"}, {"primary_key": "3747724", "vector": [], "sparse_vector": [], "title": "Conditional Disclosure of Secrets: Amplification, Closure, Amortization, Lower-Bounds, and Separations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In theconditional disclosure of secretsproblem (<PERSON><PERSON><PERSON> et al. J. Comput. Syst. Sci. 2000) <PERSON> and <PERSON>, who hold inputsxandyrespectively, wish to release a common secretsto <PERSON> (who knows both<PERSON><PERSON>) if and only if the input (x,y) satisfies some predefined predicatef. <PERSON> and <PERSON> are allowed to send a single message to <PERSON> which may depend on their inputs and some joint randomness and the goal is to minimize the communication complexity while providing information-theoretic security. Following <PERSON> et al. (Crypto 2015), we study the communication complexity of CDS protocols and derive the following positive and negative results. (Closure): A CDS forfcan be turned into a CDS for its complement\\(\\bar{f}\\)with only a minor blow-up in complexity. More generally, for a (possibly non-monotone) predicateh, we obtain a CDS for\\(h(f_1,\\ldots ,f_m)\\)whose cost is essentially linear in the formula size ofhand polynomial in the CDS complexity of\\(f_i\\). (Amplification): It is possible to reduce the privacy and correctness error of a CDS from constant to\\(2^{-k}\\)with a multiplicative overhead ofO(k). Moreover, this overhead can be amortized overk-bit secrets. (Amortization): Every predicatefovern-bit inputs admits a CDS for multi-bit secrets whose amortized communication complexity per secret bit grows linearly with the input lengthnfor sufficiently long secrets. In contrast, the best known upper-bound for single-bit secrets is exponential inn. (Lower-bounds): There exists a (non-explicit) predicatefovern-bit inputs for which any perfect (single-bit) CDS requires communication of at least\\(\\varOmega (n)\\). This is an exponential improvement over the previously known\\(\\varOmega (\\log n)\\)lower-bound. (Separations): There exists an (explicit) predicate whose CDS complexity is exponentially smaller than its randomized communication complexity. This matches a lower-bound of Gay et al., and, combined with another result of theirs, yields an exponential separation between the communication complexity of linear CDS and non-linear CDS. This is the first provable gap between the communication complexity of linear CDS (which captures most known protocols) and non-linear CDS.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_24"}, {"primary_key": "3747725", "vector": [], "sparse_vector": [], "title": "Secure Arithmetic Computation with Constant Computational Overhead.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the complexity of securely evaluating an arithmetic circuit over a finite field\\(\\mathbb {F}\\)in the setting of secure two-party computation with semi-honest adversaries. In all existing protocols, the number of arithmetic operations per multiplication gate grows either linearly with\\(\\log |\\mathbb {F}|\\)or polylogarithmically with the security parameter. We present the first protocol that only makes aconstant(amortized) number of field operations per gate. The protocol uses the underlying field\\(\\mathbb {F}\\)as a black box, and its security is based on arithmetic analogues of well-studied cryptographic assumptions. Our protocol is particularly appealing in the special case of securely evaluating a “vector-OLE” function of the form\\(\\varvec{a}x+\\varvec{b}\\), where\\(x\\in \\mathbb {F}\\)is the input of one party and\\(\\varvec{a},\\varvec{b}\\in \\mathbb {F}^w\\)are the inputs of the other party. In this case, which is motivated by natural applications, our protocol can achieve an asymptotic rate of 1/3 (i.e., the communication is dominated by sending roughly 3welements of\\(\\mathbb {F}\\)). Our implementation of this protocol suggests that it outperforms competing approaches even for relatively small fields\\(\\mathbb {F}\\)and over fast networks. Our technical approach employs two new ingredients that may be of independent interest. First, we present a general way to combine any linear code that has a fast encoder and a cryptographic (“LPN-style”) pseudorandomness property with another linear code that supports fast encodingand erasure-decoding, obtaining a code that inherits both the pseudorandomness feature of the former code and the efficiency features of the latter code. Second, we employ localarithmeticpseudo-random generators, proposing arithmetic generalizations of boolean candidates that resist all known attacks.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_8"}, {"primary_key": "3747726", "vector": [], "sparse_vector": [], "title": "Boosting Authenticated Encryption Robustness with Minimal Modifications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Atul Luykx"], "summary": "Secure and highly efficient authenticated encryption (AE) algorithms which achieve data confidentiality and authenticity in the symmetric-key setting have existed for well over a decade. By all conventional measures, AES-OCB seems to be the AE algorithm of choice on any platform with AES-NI: it has a proof showing it is secure assuming AES is, and it is one of the fastest out of all such algorithms. However, algorithms such as AES-GCM and ChaCha20+Poly1305 have seen more widespread adoption, even though they will likely never outperform AES-OCB on platforms with AES-NI. Given the fact that changing algorithms is a long and costly process, some have set out to maximize the security that can be achieved with the already deployed algorithms, without sacrificing efficiency: ChaCha20+Poly1305 already improves over GCM in how it authenticates, GCM-SIV uses GCM’s underlying components to provide nonce misuse resistance, and TLS1.3 introduces a randomized nonce in order to improve GCM’s multi-user security. We continue this line of work by looking more closely at GCM and ChaCha20+Poly1305 to see what robustness they already provide over algorithms such as OCB, and whether minor variants of the algorithms can be used for applications where defense in depth is critical. We formalize and illustrate how GCM and ChaCha20+Poly1305 offer varying degrees of resilience to nonce misuse, as they can recover quickly from repeated nonces, as opposed to OCB, which loses all security. More surprisingly, by introducing minor tweaks such as an additional XOR, we can create a GCM variant which provides security even when unverified plaintext is released.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_1"}, {"primary_key": "3747727", "vector": [], "sparse_vector": [], "title": "Memory-Tight Reductions.", "authors": ["Benedik<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Cryptographic reductions typically aim to betightby transforming an adversary\\(\\mathsf{A}\\)into an algorithm that uses essentially the same resources as\\(\\mathsf{A}\\). In this work we initiate the study ofmemory efficiencyin reductions. We argue that the amount of working memory used (relative to the initial adversary) is a relevant parameter in reductions, and that reductions that are inefficient with memory will sometimes yield less meaningful security guarantees. We then point to several common techniques in reductions that are memory-inefficient and give a toolbox for reducing memory usage. We review common cryptographic assumptions and their sensitivity to memory usage. Finally, we prove an impossibility result showing that reductions between some assumptions mustunavoidablybe either memory- or time-inefficient. This last result follows from a connection to data streaming algorithms for which unconditional memory lower bounds are known.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_4"}, {"primary_key": "3747728", "vector": [], "sparse_vector": [], "title": "Bitcoin as a Transaction Ledger: A Composable Treatment.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bitcoin is one of the most prominent examples of a distributed cryptographic protocol that is extensively used in reality. Nonetheless, existing security proofs are property-based, and as such they do not support composition. In this work we put forth a universally composable treatment of the Bitcoin protocol. We specify the goal that Bitcoin aims to achieve as a ledger functionality in the (G)UC model of <PERSON><PERSON> et al. [TCC’07]. Our ledger functionality is weaker than the one recently proposed by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> [EUROCRYPT’16], but unlike the latter suggestion, which is arguably not implementable given the Bitcoin assumptions, we prove that the one proposed here is securely UC realized under standard assumptions by an appropriate abstraction of Bitcoin as a UC protocol. We further show how known property-based approaches can be cast as special instances of our treatment and how their underlying assumptions can be cast in (G)UC without restricting the environment or the adversary.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_11"}, {"primary_key": "3747729", "vector": [], "sparse_vector": [], "title": "Practical Functional Encryption for Quadratic Functions with Applications to Predicate Encryption.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present two practically efficient functional encryption schemes for a large class of quadratic functionalities. Specifically, our constructions enable the computation of so-calledbilinear mapson encrypted vectors. This represents a practically relevant class of functions that includes, for instance, multivariate quadratic polynomials (over the integers). Our realizations work over asymmetric bilinear groups and are surprisingly efficient and easy to implement. For instance, in our most efficient scheme the public key and each ciphertext consist of\\(2n+1\\)and\\(4n+2\\)group elements respectively, wherenis the dimension of the encrypted vectors, while secret keys are only two group elements. Our two schemes build on similar ideas, but develop them in a different way in order to achieve distinct goals. Our first scheme is proved (selectively) secure under standard assumptions, while our second construction is concretely more efficient and is proved (adaptively) secure in the generic group model. As a byproduct of our functional encryption schemes, we show new predicate encryption schemes for degree-two polynomial evaluation, where ciphertexts consist of onlyO(n) group elements. This significantly improves the\\(O(n^2)\\)bound one would get from inner product encryption-based constructions.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_3"}, {"primary_key": "3747730", "vector": [], "sparse_vector": [], "title": "Functional Graph Revisited: Updates on (Second) Preimage Attacks on Hash Combiners.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper studies functional-graph-based (second) preimage attacks against hash combiners. By exploiting more properties of functional graph, we find an improved preimage attack against the XOR combiner with a complexity of\\(2^{5n/8}\\), while the previous best-known complexity is\\(2^{2n/3}\\). Moreover, we find the first generic second-preimage attack on Zipper hash with an optimal complexity of\\(2^{3n/5}\\).", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_14"}, {"primary_key": "3747731", "vector": [], "sparse_vector": [], "title": "Proving Resistance Against Invariant Attacks: How to Choose the Round Constants.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many lightweight block ciphers apply a very simple key schedule in which the round keys only differ by addition of a round-specific constant. Generally, there is not much theory on how to choose appropriate constants. In fact, several of those schemes were recently broken using invariant attacks, i.e., invariant subspace or nonlinear invariant attacks. This work analyzes the resistance of such ciphers against invariant attacks and reveals the precise mathematical properties that render those attacks applicable. As a first practical consequence, we prove that some ciphers including<PERSON><PERSON><PERSON>,Skinny-64and\\(\\textsf {Mantis}_{\\mathsf {7}}\\)are not vulnerable to invariant attacks. Also, we show that the invariant factors of the linear layer have a major impact on the resistance against those attacks. Most notably, if the number of invariant factors of the linear layer is small (e.g., if its minimal polynomial has a high degree), we can easily find round constants which guarantee the resistance to all types of invariant attacks, independently of the choice of the S-box layer. We also explain how to construct optimal round constants for a given, but arbitrary, linear layer.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_22"}, {"primary_key": "3747732", "vector": [], "sparse_vector": [], "title": "Private Multiplication over Finite Fields.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The notion of privacy in the probing model, introduced by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> in 2003, is nowadays frequently involved to assess the security of circuits manipulating sensitive information. However, provable security in this model still comes at the cost of a significant overhead both in terms of arithmetic complexity and randomness complexity. In this paper, we deal with this issue for circuits processing multiplication over finite fields. Our contributions are manifold. Extending the work of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> at Eurocrypt 2016, we introduce an algebraic characterization of the privacy for multiplication in any finite field and we propose a novel algebraic characterization fornon-interference(a stronger security notion in this setting). Then, we present two generic constructions of multiplication circuits in finite fields that achieve non-interference in the probing model. Denoting bydthe number of probes used by the adversary, the first proposal reduces the number ofbilinearmultiplications (i.e., of general multiplications of two non-constant values in the finite field) to only\\(2d+1\\)whereas the state-of-the-art was\\(O(d^2)\\). The second proposal reduces the randomness complexity todrandom elements in the underlying finite field, hence improving the\\(O(d \\log d)\\)randomness complexity achieved by <PERSON><PERSON><PERSON><PERSON> et al. in their paper. This construction is almost optimal since we also prove thatd/ 2 is a lower bound. Eventually, we show that both algebraic constructions can always be instantiated in large enough finite fields. Furthermore, for the important cases\\(d \\in \\{2,3\\}\\), we illustrate that they perform well in practice by presenting explicit realizations for finite fields of practical interest.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_14"}, {"primary_key": "3747733", "vector": [], "sparse_vector": [], "title": "Ratcheted Encryption and Key Exchange: The Security of Messaging.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We aim to understand, formalize and provably achieve the goals underlying the core key-ratcheting technique of <PERSON><PERSON>, <PERSON> and <PERSON>, extensions of which are now used in secure messaging systems. We give syntax and security definitions for ratcheted encryption and key-exchange. We give a proven-secure protocol for ratcheted key exchange. We then show how to generically obtain ratcheted encryption from ratcheted key-exchange and standard encryption.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_21"}, {"primary_key": "3747734", "vector": [], "sparse_vector": [], "title": "Robust Non-interactive Multiparty Computation Against Constant-Size Collusion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Non-Interactive Multiparty Computations (<PERSON><PERSON><PERSON> et al., Crypto 2014) is a very powerful notion equivalent (under some corruption model) to garbled circuits, Private Simultaneous Messages protocols, and obfuscation. We present robust solutions to the problem of Non-Interactive Multiparty Computation in the computational and information-theoretic models. Our results include the first efficient and robust protocols to compute any function in\\(NC^1\\)for constant-size collusions, in the information-theoretic setting and in the computational setting, to compute any function inPfor constant-size collusions, assuming the existence of one-way functions. Our constructions start from a Private Simultaneous Messages construction (<PERSON><PERSON>, <PERSON><PERSON>, STOC 1994 and <PERSON><PERSON>, <PERSON><PERSON>, ISTCS 1997) and transform it into a Non-Interactive Multiparty Computation for constant-size collusions. We also present a new Non-Interactive Multiparty Computation protocol for symmetric functions with significantly better communication complexity compared to the only known one of <PERSON><PERSON><PERSON> et al.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_13"}, {"primary_key": "3747735", "vector": [], "sparse_vector": [], "title": "Structure vs. Hardness Through the Obfuscation Lens.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Much of modern cryptography, starting from public-key encryption and going beyond, is based on the hardness of structured (mostly algebraic) problems like factoring, discrete log or finding short lattice vectors. While structure is perhaps what enables advanced applications, it also puts the hardness of these problems in question. In particular, this structure often puts them in low complexity classes such as\\({\\textsf {NP}} \\cap {\\textsf {coNP}}\\)or statistical zero-knowledge (SZK). Is this structure really necessary? For some cryptographic primitives, such as one-way permutations and homomorphic encryption, we know that the answer isyes—they imply hard problems in\\({\\textsf {NP}} \\cap {\\textsf {coNP}}\\)and\\({\\textsf {SZK}}\\), respectively. In contrast, one-way functions donotimply such hard problems, at least not byfully black-box reductions. Yet, for many basic primitives such as public-key encryption, oblivious transfer, and functional encryption, we do not have any answer. We show that the above primitives, and many others, donotimply hard problems in\\({\\textsf {NP}} \\cap {\\textsf {coNP}}\\)or\\({\\textsf {SZK}}\\)via fully black-box reductions. In fact, we first show that even the very powerful notion of Indistinguishability Obfuscation (IO) doesnotimply such hard problems, and then deduce the same for a large class of primitives that can be constructed from IO.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_23"}, {"primary_key": "3747736", "vector": [], "sparse_vector": [], "title": "Secure Computation Based on Leaky Correlations: High Resilience Setting.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Correlated private randomness, or correlation in short, is a fundamental cryptographic resource that helps parties compute securely over their private data. An offline preprocessing step, which is independent of the eventual secure computation, generates correlated secret shares for the parties and the parties use these shares during the final secure computation step. However, these secret shares are vulnerable to leakage attacks. Inspired by the quintessential problem of privacy amplification, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (FOCS 2009) introduced the concept of correlation extractors. Correlation extractors are interactive protocols that take leaky correlations as input and produce secure independent copies of oblivious transfer (OT), the building blocks of secure computation protocols. Although their initial feasibility result is resilient to linear leakage and produces a linear number of “fresh” OTs, the constants involved are minuscule. The output of this correlation extractor can be used to perform only small secure computation tasks, because the number of OTs needed to evaluate a functionality securely is roughly proportional to its circuit size. Recently, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (CRYPTO 2015) constructed an extractor that is resilient to 1/4 fractional leakage and has near-linear production rate. They also constructed an extractor from a large correlation that has 1/2 fractional resilience but produces only one OT, which does not suffice to compute even constant size functionalities securely. In this paper, we show the existence of a correlation that producesn-bit shares for the parties and allows the extraction of\\(n^{1-o(1)}\\)secure OTs, despiten/2 bits of leakage. The key technical idea is to embed several multiplications over a field into one multiplication over an extension field. The packing efficiency of this embedding directly translates into the production rate of our correlation extractor. Our work establishes a connection between this problem and a rich vein of research in additive combinatorics on constructing dense sets of integers that are free of arithmetic progressions, a.k.a. 3-free sets. We introduce a new combinatorial problem that suffices for our multiplication embedding, and produces concrete embeddings that beat the efficiency of the embeddings inspired by the reduction to 3-free sets. Finally, the paper introduces a graph-theoretic measure to upper-bound the leakage resilience of correlations, namely thesimple partition number. This measure is similar in spirit to graph covering problems like the biclique partition number. If the simple partition number of a correlation is\\(2^\\lambda \\), then it is impossible to extract even one OT if parties can perform\\(\\lambda \\)-bits of leakage. We compute tight estimates of the simple partition number of several correlations that are relevant to this paper, and, in particular, show that our extractor and the extractor for the large correlation by Gupta et al. have optimal leakage resilience and (qualitatively) optimal simulation error.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_1"}, {"primary_key": "3747737", "vector": [], "sparse_vector": [], "title": "Hedging Public-Key Encryption in the Real World.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hedged PKE schemes are designed to provide useful security when the per-message randomness fails to be uniform, say, due to faulty implementations or adversarial actions. A simple and elegant theoretical approach to building such schemes works like this: Synthesize fresh random bits by hashing all of the encryption inputs, and use the resulting hash output as randomness for an underlying PKE scheme. In practice, implementing this simple construction is surprisingly difficult, as the high- and mid-level APIs presented by the most commonly used crypto libraries (e.g. OpenSSL and forks thereof)do notpermit one to specify the per-encryption randomness. Thus application developers are forced to piece together low-level functionalities and attend to any associated, security-critical algorithmic choices. Other approaches to hedged PKE present similar problems in practice. We reconsider the matter of building hedged PKE schemes, and the security notions they aim to achieve. We lift the current best-possible security notion for hedged PKE (IND-CDA) from the CPA setting to the CCA setting, and then show how to achieve it using primitives that are readily available from high-level APIs. We also propose a new security notion, MM-CCA, which generalizes traditional IND-CCA to admit imperfect randomness. Like IND-CCA, and unlike IND-CDA, our notion gives the adversary the public key. We show that MM-CCA is achieved by RSA-OAEP in the random-oracle model; this is significant in practice because RSA-OAEP is directly available from high-level APIs across all libraries we surveyed. We sort out relationships among the various notions, and also develop new results for existing hedged PKE constructions.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_16"}, {"primary_key": "3747738", "vector": [], "sparse_vector": [], "title": "All-But-Many Lossy Trapdoor Functions from Lattices and Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "“All-but-many lossy trapdoor functions” (ABM-LTF) are a powerful cryptographic primitive studied by <PERSON><PERSON><PERSON><PERSON> (Eurocrypt 2012). ABM-LTFs are parametrised with tags: a lossy tag makes the function lossy; an injective tag makes the function injective, and invertible with a trapdoor. Existing ABM-LTFs rely on non-standard assumptions. Our first result is an ABM-LTF construction from lattices, based on the learning-with-errors (LWE) problem. Unlike the previous schemes which behaved as “encrypted signatures”, the core of our construction is an “encrypted, homomorphic-evaluation-friendly, weak pseudorandom function”. The weak pseudorandom function outputs matrices, where the lossy tags are preimages of the zero matrix, and the injective tags are preimages of random full-rank matrices. Our second result is a public-key system tightly secure against “selective opening” attacks, where an attacker gets many challenges and can ask to see the random bits of any of them. Following the steps of <PERSON><PERSON><PERSON> et al. (Asiacrypt 2011) and <PERSON><PERSON><PERSON><PERSON> (Eurocrypt 2012), our ABM-LTF gives the first lattice-based, compact public-key encryption (PKE) scheme that has indistinguishability against adaptive chosen-ciphertext and selective opening attacks (IND-SO-CCA2), with tight security, and whose public-key size and security reduction are independent of the number of decryption queries and ciphertext challenges. Meanwhile, this result provides an alternative solution to the problem of building pairing-free IND-CCA2 PKE schemes with tight security in the multi-challenge setting, which was firstly answered by Gay et al. (Eurocrypt 2016). Additionally, our ABM-LTF answers the open question of constructing (non-necessarily lossy) all-but-many trapdoor functions from lattices, first asked by Alperin-Sheriff and Peikert (PKC 2012).", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_11"}, {"primary_key": "3747739", "vector": [], "sparse_vector": [], "title": "PRF-ODH: Relations, Instantiations, and Impossibility Results.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The pseudorandom-function oracle-Di<PERSON><PERSON><PERSON> (PRF-ODH) assumption has been introduced recently to analyze a variety of DH-based key exchange protocols, including TLS 1.2 and the TLS 1.3 candidates, as well as the extended access control (EAC) protocol. Remarkably, the assumption comes in different flavors in these settings and none of them has been scrutinized comprehensively yet. In this paper here we therefore present a systematic study of the different PRF-ODH variants in the literature. In particular, we analyze their strengths relative to each other, carving out that the variants form a hierarchy. We further investigate the boundaries between instantiating the assumptions in the standard model and the random oracle model. While we show that even the strongest variant is achievable in the random oracle model under the strong <PERSON><PERSON><PERSON><PERSON> assumption, we provide a negative result showing that it is implausible to instantiate even the weaker variants in the standard model via algebraic black-box reductions to common cryptographic problems.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_22"}, {"primary_key": "3747740", "vector": [], "sparse_vector": [], "title": "Anonymous Attestation with Subverted TPMs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Various sources have revealed that cryptographic standards and components have been subverted to undermine the security of users, reigniting research on means to achieve security in presence of such subverted components. In this paper we consider direct anonymous attestation (DAA) in this respect. This standardized protocol allows a computer with the help of an embedded TPM chip to remotely attest that it is in a healthy state. Guaranteeing that different attestations by the same computer cannot be linked was an explicit and important design goal of the standard in order to protect the privacy of the user of the computer. Surprisingly, none of the standardized or otherwise proposed DAA protocols achieves privacy when the TPM is subverted, but they all rely on the honesty of the TPM. As the TPM is a piece of hardware, it is hardly possible to tell whether or not a given TPM follows the specified protocol. In this paper we study this setting and provide a new protocol that achieves privacy also in presence of subverted TPMs.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_15"}, {"primary_key": "3747741", "vector": [], "sparse_vector": [], "title": "Encryption Switching Protocols Revisited: Switching Modulo p.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "At CRYPTO 2016, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON> introduced a new primitive calledencryption switching protocols, allowing to switch ciphertexts between two encryption schemes. If such an ESP is built with two schemes that are respectively additively and multiplicatively homomorphic, it naturally gives rise to a secure 2-party computation protocol. It is thus perfectly suited for evaluating functions, such as multivariate polynomials, given as arithmetic circuits. <PERSON><PERSON><PERSON> et al. built an ESP to switch between Elgamal and Paillier encryptions which do not naturally fit well together. Consequently, they had to design a clever variant of Elgamal over\\(\\mathbf {Z}/n\\mathbf {Z}\\)with a costly shared decryption. In this paper, we first present a conceptually simple generic construction for encryption switching protocols. We then give an efficient instantiation of our generic approach that uses two well-suited protocols, namely a variant of Elgamal in\\(\\mathbf {Z}/p\\mathbf {Z}\\)and the Castagnos-Laguillaumie encryption which is additively homomorphic over\\(\\mathbf {Z}/p\\mathbf {Z}\\). Among other advantages, this allows to perform all computations modulo a primepinstead of an RSA modulus. Overall, our solution leads to significant reductions in the number of rounds as well as the number of bits exchanged by the parties during the interactive protocols. We also show how to extend its security to the malicious setting.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_9"}, {"primary_key": "3747742", "vector": [], "sparse_vector": [], "title": "Laconic Oblivious Transfer and Its Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>"], "summary": "In this work, we introduce a novel technique for secure computation over large inputs. Specifically, we provide a new oblivious transfer (OT) protocol with a laconic receiver. Laconic OT allows a receiver to commit to a large inputD(of lengthM) via a short message. Subsequently, a single short message by a sender allows the receiver to learn\\(m_{D[L]}\\), where the messages\\(m_0, m_1\\)and the location\\(L \\in [M]\\)are dynamically chosen by the sender. All prior constructions of OT required the receiver’s outgoing message to grow withD. Our key contribution is an instantiation of this primitive based on the Decisional Diffie-Hellman (DDH) assumption in the common reference string (CRS) model. The technical core of this construction is a novel use of somewhere statistically binding (SSB) hashing in conjunction with hash proof systems. Next, we show applications of laconic OT to non-interactive secure computation on large inputs and multi-hop homomorphic encryption for RAM programs.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_2"}, {"primary_key": "3747743", "vector": [], "sparse_vector": [], "title": "Four-Round Concurrent Non-Malleable Commitments from One-Way Functions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "How many rounds and which assumptions are required forconcurrentnon-malleable commitments? The above question has puzzled researchers for several years. Pass in [TCC 2013] showed a lower bound of 3 rounds for the case of black-box reductions to falsifiable hardness assumptions with respect to polynomial-time adversaries. On the other side, <PERSON><PERSON> [STOC 2011], <PERSON> and <PERSON> [STOC 2011] and <PERSON><PERSON> et al. [FOCS 2012] showed that one-way functions (OWFs) are sufficient with a constant number of rounds. More recently <PERSON><PERSON><PERSON> et al. [CRYPTO 2016] showed a 3-round construction based on subexponentially strong one-way permutations. In this work we show asmain resultthe first 4-round concurrent non-malleable commitment scheme assuming the existence of any one-way function. Our approach builds on a new security notion for argument systems against man-in-the-middle attacks:Simulation-Witness-Independence. We show how to construct a 4-round one-many simulation-witnesses-independent argument system from one-way functions. We then combine this new tool in parallel with a weak form of non-malleable commitments constructed by <PERSON><PERSON> et al. in [FOCS 2014] obtaining the main result of our work.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_5"}, {"primary_key": "3747744", "vector": [], "sparse_vector": [], "title": "Information-Theoretic Indistinguishability via the Chi-Squared Method.", "authors": ["<PERSON>", "Viet Tung Hoang", "<PERSON>"], "summary": "Proving tight bounds on information-theoretic indistinguishability is a central problem in symmetric cryptography. This paper introduces a new method for information-theoretic indistinguishability proofs, called “the chi-squared method”. At its core, the method requires upper-bounds on the so-called\\(\\chi ^2\\)divergence (due to <PERSON><PERSON><PERSON> and <PERSON>) between the output distributions of two systems being queries. The method morally resembles, yet also considerably simplifies, a previous approach proposed by <PERSON><PERSON> and <PERSON> (ePrint, 1999), while at the same time increasing its expressiveness and delivering tighter bounds. We showcase the chi-squared method on some examples. In particular: (1) We prove an optimal bound of\\(q/2^n\\)for the XOR of two permutations, and our proof considerably simplifies previous approaches using theH-coefficient method, (2) we provide improved bounds for the recently proposed encrypted Davies-Meyer PRF construction by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (CRYPTO ’16), and (3) we give a tighter bound for the Swap-or-not cipher by <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (CRYPTO ’12).", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_17"}, {"primary_key": "3747745", "vector": [], "sparse_vector": [], "title": "Indifferentiability of Iterated Even-Mansour Ciphers with Non-idealized Key-Schedules: Five Rounds Are Necessary and Sufficient.", "authors": ["Yuanxi Dai", "<PERSON><PERSON>", "<PERSON>", "Aishwarya Thiruvengadam"], "summary": "We prove that the 5-round iterated Even-Mansour (IEM) construction with a non-idealized key-schedule (such as the trivial key-schedule, where all round keys are equal) is indifferentiable from an ideal cipher. In a separate result, we also prove that five rounds are necessary by describing an attack against the corresponding 4-round construction. This closes the gap regarding the exact number of rounds for which the IEM construction with a non-idealized key-schedule is indifferentiable from an ideal cipher, which was previously only known to lie between four and twelve. Moreover, the security bound we achieve is comparable to (in fact, slightly better than) the previously established 12-round bound.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_18"}, {"primary_key": "3747746", "vector": [], "sparse_vector": [], "title": "The TinyTable Protocol for 2-Party Secure Computation, or: Gate-Scrambling Revisited.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a new protocol, nicknamed TinyTable, for maliciously secure 2-party computation in the preprocessing model. One version of the protocol is useful in practice and allows, for instance, secure AES encryption with latency about 1 ms and amortized time about 0.5\\(\\upmu \\)s per AES block on a fast cloud set-up. Another version is interesting from a theoretical point of view: we achieve a maliciously and unconditionally secure 2-party protocol in the preprocessing model for computing a Boolean circuit, where both the communication complexity and preprocessed data size needed isO(s) wheresis the circuit size, while the computational complexity is\\(O(k^\\epsilon s)\\)wherekis the statistical security parameter and\\(\\epsilon <1\\)is a constant. For general circuits with no assumption on their structure, this is the best asymptotic performance achieved so far in this model.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_6"}, {"primary_key": "3747747", "vector": [], "sparse_vector": [], "title": "Time-Memory Tradeoff Attacks on the MTP Proof-of-Work Scheme.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Proof-of-work (PoW) schemes are cryptographic primitives with numerous applications, and in particular, they play a crucial role in maintaining consensus in cryptocurrency networks. Ideally, a cryptocurrency PoW scheme should have several desired properties, including efficient verification on one hand, and high memory consumption of the prover’s algorithm on the other hand, making the scheme less attractive for implementation on dedicated hardware. At the USENIX Security Symposium 2016, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> presented a new promising PoW scheme called MTP (Merkle Tree Proof) that achieves essentially all desired PoW properties. As a result, MTP has received substantial attention from the cryptocurrency community. The scheme uses a Merkle hash tree construction over a large array of blocks computed by a memory consuming (memory-hard) function. Despite the fact that only a small fraction of the memory is verified by the efficient verification algorithm, the designers claim that a cheating prover that uses a small amount of memory will suffer from a significant computational penalty. In this paper, we devise a sub-linear computation-memory tradeoff attack on MTP. We apply our attack to the concrete instance proposed by the designers which uses the memory-hard function Argon2d and computes a proof by allocating 2 gigabytes of memory. The attack computes arbitrary malicious proofs using less than a megabyte of memory (about 1/3000 of the honest prover’s memory) at a relatively mild penalty of 170 in computation. This is more than 55,000 times faster than what is claimed by the designers. The attack requires a one-time precomputation step of complexity\\(2^{64}\\), but its online cost is only increased by a factor which is less than 2 when spending\\(2^{48}\\)precomputation time. The main idea of the attack is to exploit the fact that Argon2d accesses its memory in a way which is determined by its previous computations. This allows to inject a small fraction of carefully selected memory blocks that manipulate Argon2d’s memory access patterns, significantly weakening its memory-hardness.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_13"}, {"primary_key": "3747748", "vector": [], "sparse_vector": [], "title": "Identity-Based Encryption from the Diffie-Hellman Assumption.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We provide the first constructions of identity-based encryption and hierarchical identity-based encryption based on the hardness of the (Computational) Diffie-Hellman Problem (without use of groups with pairings) or Factoring. Our construction achieves the standard notion of identity-based encryption as considered by <PERSON><PERSON> and <PERSON> [CRYPTO 2001]. We bypass known impossibility results using garbled circuits that make a non-black-box use of the underlying cryptographic primitives.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_18"}, {"primary_key": "3747749", "vector": [], "sparse_vector": [], "title": "Breaking the FF3 Format-Preserving Encryption Standard over Small Domains.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The National Institute of Standards and Technology (NIST) recently published a Format-Preserving Encryption standard accepting two Feistel structure based schemes called FF1 and FF3. Particularly, FF3 is a tweakable block cipher based on an 8-round Feistel network. In CCS 2016, <PERSON><PERSON> et al. gave an attack to break FF3 (and FF1) with time and data complexity\\(O(N^5\\log (N))\\), which is much larger than the code book (but using many tweaks), where\\(N^2\\)is domain size to the Feistel network. In this work, we give a new practical total break attack to the FF3 scheme (also known as BPS scheme). Our FF3 attack requires\\(O(N^{\\frac{11}{6}})\\)chosen plaintexts with time complexity\\(O(N^{5})\\). Our attack was successfully tested with\\(N\\leqslant 2^9\\). It is a slide attack (using two tweaks) that exploits the bad domain separation of the FF3 design. Due to this weakness, we reduced the FF3 attack to an attack on 4-round Feistel network. <PERSON><PERSON><PERSON><PERSON> et al. already gave a 4-round Feistel structure attack in SAC 2015. However, it works with chosen plaintexts and ciphertexts whereas we need a known-plaintext attack. Therefore, we developed a new generic known-plaintext attack to 4-round Feistel network that reconstructs the entire tables for all round functions. It works with\\(N^{\\frac{3}{2}} \\left( \\frac{N}{2} \\right) ^{\\frac{1}{6}}\\)known plaintexts and time complexity\\(O(N^{3})\\). Our 4-round attack is simple to extend to five and more rounds with complexity\\(N^{(r-5)N+o(N)}\\). It shows that FF1 with\\(N=7\\)and FF3 with\\(7\\leqslant N\\leqslant 10\\)do not offer a 128-bit security. Finally, we provide an easy and intuitive fix to prevent the FF3 scheme from our\\(O(N^{5})\\)attack.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_23"}, {"primary_key": "3747750", "vector": [], "sparse_vector": [], "title": "LPN Decoded.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose new algorithms withsmall memory consumptionfor the Learning Parity with Noise (LPN) problem, both classically and quantumly. Our goal is to predict the hardness of LPN depending on both parameters, its dimensionkand its noise rate\\(\\tau \\), as accurately as possible both in theory and practice. Therefore, we analyze our algorithms asymptotically, run experiments on medium size parameters and provide bit complexity predictions for large parameters. Our new algorithms are modifications and extensions of the simple Gaussian elimination algorithm with recent advanced techniques for decoding random linear codes. Moreover, we enhance our algorithms by the dimension reduction technique from <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. This results in a hybrid algorithm that is capable for achieving the best currently known run time for any fixed amount of memory. On the asymptotic side, we achieve significant improvements for the run time exponents, both classically and quantumly. To the best of our knowledge, we provide the first quantum algorithms for LPN. Due to the small memory consumption of our algorithms, we are able to solve for the first time LPN instances of medium size, e.g. with\\(k=243, \\tau = \\frac{1}{8}\\)in only 15 days on 64 threads. Our algorithms result in bit complexity prediction that require relatively largekfor small\\(\\tau \\). For instance for small noise LPN with\\(\\tau = \\frac{1}{\\sqrt{k}}\\), we predict 80-bit classical and only 64-bit quantum security for\\(k~\\ge ~2048\\). For the common cryptographic choice\\(k=512, \\tau = \\frac{1}{8}\\), we achieve with limited memory classically 102-bit and quantumly 69-bit security.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_17"}, {"primary_key": "3747751", "vector": [], "sparse_vector": [], "title": "Key Rotation for Authenticated Encryption.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A common requirement in practice is to periodically rotate the keys used to encrypt stored data. Systems used by Amazon and Google do so using a hybrid encryption technique which is eminently practical but has questionable security in the face of key compromises and does not provide full key rotation. Meanwhile, symmetric updatable encryption schemes (introduced by Bonehet al.CRYPTO 2013) support full key rotation without performing decryption: ciphertexts created under one key can be rotated to ciphertexts created under a different key with the help of a re-encryption token. By design, the tokens do not leak information about keys or plaintexts and so can be given to storage providers without compromising security. But the prior work of Bonehet al.addresses relatively weak confidentiality goals and does not consider integrity at all. Moreover, as we show, a subtle issue with their concrete scheme obviates a security proof even for confidentiality against passive attacks. This paper presents a systematic study ofupdatable Authenticated Encryption (AE). We provide a set of security notions that strengthen those in prior work. These notions enable us to tease out real-world security requirements of different strengths and build schemes that satisfy them efficiently. We show that the hybrid approach currently used in industry achieves relatively weak forms of confidentiality and integrity, but can be modified at low cost to meet our stronger confidentiality and integrity goals. This leads to a practical scheme that has negligible overhead beyond conventional AE. We then introducere-encryption indistinguishability, a security notion that formally captures the idea of fully refreshing keys upon rotation. We show how to repair the scheme of Bonehet al., attaining our stronger confidentiality notion. We also show how to extend the scheme to provide integrity, and we prove that it meets our re-encryption indistinguishability notion. Finally, we discuss how to instantiate our scheme efficiently using off-the-shelf cryptographic components (AE, hashing, elliptic curves). We report on the performance of a prototype implementation, showing that fully secure key rotations can be performed at a throughput of approximately 116 kB/s.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_4"}, {"primary_key": "3747752", "vector": [], "sparse_vector": [], "title": "Non-Malleable Codes for Space-Bounded Tampering.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Non-malleable codes—introduced by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> at ICS 2010—are key-less coding schemes in which mauling attempts to an encoding of a given message, w.r.t. some class of tampering adversaries, result in a decoded value that is either identical or unrelated to the original message. Such codes are very useful for protecting arbitrary cryptographic primitives against tampering attacks against the memory. Clearly, non-malleability is hopeless if the class of tampering adversaries includes the decoding and encoding algorithm. To circumvent this obstacle, the majority of past research focused on designing non-malleable codes for various tampering classes, albeit assuming that the adversary is unable to decode. Nonetheless, in many concrete settings, this assumption is not realistic. In this paper, we explore one particular such scenario where the class of tampering adversaries naturally includes the decoding (but not the encoding) algorithm. In particular, we consider the class of adversaries that are restricted in terms of memory/space. Our main contributions can be summarized as follows: We initiate a general study of non-malleable codes resisting space-bounded tampering. In our model, the encoding procedure requires large space, but decoding can be done in small space, and thus can be also performed by the adversary. Unfortunately, in such a setting it is impossible to achieve non-malleability in the standard sense, and we need to aim for slightly weaker security guarantees. In a nutshell, our main notion (dubbedleaky space-bounded non-malleability) ensures that this is the best the adversary can do, in that space-bounded tampering attacks can be simulated given a small amount of leakage on the encoded value. We provide a simple construction of a leaky space-bounded non-malleable code. Our scheme is based on any Proof of Space (PoS)—a concept recently put forward by Atenieseet al.(SCN 2014) and Dziembowskiet al.(CRYPTO 2015)—satisfying a variant of soundness. As we show, our paradigm can be instantiated by extending the analysis of the PoS construction by Ren and Devadas (TCC 2016-A), based on so-called stacks of localized expander graphs. Finally, we show that our flavor of non-malleability yields a natural security guarantee against memory tampering attacks, where one can trade a small amount of leakage on the secret key for protection against space-bounded tampering attacks.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_4"}, {"primary_key": "3747753", "vector": [], "sparse_vector": [], "title": "Identity-Based Encryption from Codes with Rank Metric.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Code-based cryptography has a long history, almost as long as the history of public-key encryption (\\({\\mathsf {PKE}}\\)). While we can construct almost all primitives from codes such as\\({\\mathsf {PKE}}\\), signature, group signature etc., it is a long standing open problem to construct an identity-based encryption from codes. We solve this problem by relying on codes with rank metric. The concept of identity-based encryption (\\({\\mathsf {IBE}}\\)), introduced by <PERSON><PERSON><PERSON> in 1984, allows the use of users’ identifier information such as email as public key for encryption. There are two problems that makes the design of IBE extremely hard: the requirement that the public key can be an arbitrary string and the possibility to extract decryption keys from the public keys. In fact, it took nearly twenty years for the problem of designing an efficient method to implement an\\({\\mathsf {IBE}}\\)to be solved. The known methods of designing\\({\\mathsf {IBE}}\\)are based on different tools: from elliptic curve pairings by <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and by <PERSON><PERSON> and <PERSON> in 2000 and 2001 respectively; from the quadratic residue problem by <PERSON><PERSON> in 2001; and finally from the Learning-with-Error problem by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in 2008. Among all candidates for post-quantum cryptography, there only exist thus lattice-based\\({\\mathsf {IBE}}\\). In this paper, we propose a new method, based on the hardness of learning problems with rank metric, to design the first code-based\\({\\mathsf {IBE}}\\)scheme. In order to overcome the two above problems in designing an\\({\\mathsf {IBE}}\\)scheme, we first construct a rank-based\\({\\mathsf {PKE}}\\), called\\({\\mathsf {RankPKE}}\\), where the public key space is dense and thus can be obtained from a hash of any identity. We then extract a decryption key from any public key by constructing an trapdoor function which relies on\\({\\mathsf {RankSign}}\\)- a signature scheme from PQCrypto 2014. In order to prove the security of our schemes, we introduced a new problem for rank metric: the Rank Support Learning problem (\\(\\mathsf {RSL}\\)). A high technical contribution of the paper is devoted to study in details the hardness of the\\(\\mathsf {RSL}\\)problem.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_7"}, {"primary_key": "3747754", "vector": [], "sparse_vector": [], "title": "The Price of Low Communication in Secure Multi-party Computation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional protocols for secure multi-party computation amongnparties communicate at least a linear (inn) number of bits, even when computing very simple functions. In this work we investigate the feasibility of protocols withsublinearcommunication complexity. Concretely, we consider two clients, one of which may be corrupted, who wish to perform some “small” joint computation usingnservers but without any trusted setup. We show that enforcing sublinear communication complexity drastically affects the feasibility bounds on the number of corrupted parties that can be tolerated in the setting of information-theoretic security. We provide a complete investigation of security in the presence of semi-honest adversaries—static and adaptive, with and without erasures—and initiate the study of security in the presence of malicious adversaries. For semi-honest static adversaries, our bounds essentially match the corresponding bounds when there is no communication restriction—i.e., we can tolerate up to\\(t < (1/2 -\\epsilon )n\\)corrupted parties. For the adaptive case, however, the situation is different. We prove that without erasures even a small constant fraction of corruptions is intolerable, and—more surprisingly—when erasures are allowed, we prove that\\(t < (1 - \\sqrt{0.5} - \\epsilon )n\\)corruptions can be tolerated, which we also show to be essentially optimal. The latter optimality proof hinges on a new treatment of probabilistic adversary structures that may be of independent interest. In the case of active corruptions in the sublinear communication setting, we prove that static “security with abort” is feasible when\\(t < (1/2 - \\epsilon )n\\), namely, the bound that is tight for semi-honest security. All of our negative results in fact rule out protocols with sublinearmessage complexity.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_14"}, {"primary_key": "3747755", "vector": [], "sparse_vector": [], "title": "The Bitcoin Backbone Protocol with Chains of Variable Difficulty.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Bitcoin’s innovative and distributedly maintainedblockchaindata structure hinges on the adequate degree of difficulty of so-called “proofs of work,” which miners have to produce in order for transactions to be inserted. Importantly, these proofs of work have to be hard enough so that miners have an opportunity to unify their views in the presence of an adversary who interferes but has bounded computational power, but easy enough to be solvable regularly and enable the miners to make progress. As such, as the miners’ population evolves over time, so should the difficulty of these proofs. Bitcoin provides this adjustment mechanism, with empirical evidence of a constant block generation rate against such population changes. In this paper we provide the first formal analysis of Bitcoin’s target (re)calculation function in the cryptographic setting, i.e., against all possible adversaries aiming to subvert the protocol’s properties. We extend theq-bounded synchronous model of theBitcoin backbone protocol[Eurocrypt 2015], which posed the basic properties of Bitcoin’s underlying blockchain data structure and shows how a robust public transaction ledger can be built on top of them, to environments that may introduce or suspend parties in each round. We provide a set of necessary conditions with respect to the way the population evolves under which the “Bitcoin backbone with chains of variable difficulty” provides a robust transaction ledger in the presence of an actively malicious adversary controlling a fraction of the miners strictly below\\(50\\%\\)at each instant of the execution. Our work introduces new analysis techniques and tools to the area of blockchain systems that may prove useful in analyzing other blockchain protocols.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_10"}, {"primary_key": "3747756", "vector": [], "sparse_vector": [], "title": "Lower Bounds on Obfuscation from All-or-Nothing Encryption Primitives.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Indistinguishability obfuscation (IO) enables many heretofore out-of-reach applications in cryptography. However, currently all known constructions of IO are based on multilinear maps which are poorly understood. Hence, tremendous research effort has been put towards basing obfuscation on better-understood computational assumptions. Recently, another path to IO has emerged through functional encryption [<PERSON><PERSON> and <PERSON>, CRYPTO 2015; <PERSON><PERSON><PERSON> and <PERSON><PERSON>, FOCS 2015] but such FE schemes currently are still based on multi-linear maps. In this work, we study whether IO could be based on other powerful encryption primitives. Separations for IO.We show that (assuming that the polynomial hierarchy does not collapse and one-way functions exist) IO cannot be constructed in a black-box manner from powerful all-or-nothing encryption primitives, such as witness encryption (WE), predicate encryption, and fully homomorphic encryption. What unifies these primitives is that they are of the “all-or-nothing” form, meaning either someone has the “right key” in which case they can decrypt the message fully, or they are not supposed to learn anything. Stronger Model for Separations.One might argue that fully black-box uses of the considered encryption primitives limit their power too much because these primitives can easily lead to non-black-box constructions if the primitive is used in aself-feedingfashion—namely, code of the subroutines of the considered primitive could easily be fed as input to the subroutines of the primitive itself. In fact, several important results (e.g., the construction of IO from functional encryption) follow this very recipe. In light of this, we prove our impossibility results with respect to astrongermodel than the fully black-box framework of Impagliazzo and Rudich (STOC’89) and Reingold, Trevisan, and Vadhan (TCC’04) where the non-black-box technique of self-feeding is actually allowed.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_22"}, {"primary_key": "3747757", "vector": [], "sparse_vector": [], "title": "Incremental Program Obfuscation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent advances in program obfuscation suggest that it is possible to create software that can provably safeguard secret information. However, software systems usually contain large executable code that is updated multiple times and sometimes very frequently. Freshly obfuscating the program for every small update will lead to a considerable efficiency loss. Thus, an extremely desirable property for obfuscation algorithms isincrementality: small changes to the underlying program translate into small changes to the corresponding obfuscated program. We initiate a thorough investigation ofincremental program obfuscation. We show that the strong simulation-based notions of program obfuscation, such as “virtual black-box” and “virtual grey-box” obfuscation, cannot be incremental (according to our efficiency requirements) even for very simple functions such as point functions. We then turn to the indistinguishability-based notions, and present two security definitions of varying strength — namely, a weak one and a strong one. To understand the overall strength of our definitions, we formulate the notion ofincremental best-possible obfuscationand show that it is equivalent to our strong indistinguishability-based notion. Finally, we present constructions for incremental program obfuscation satisfying both our security notions. We first give a construction achieving the weaker security notion based on the existence of general purpose indistinguishability obfuscation. Next, we present a generic transformation usingoblivious RAMto amplify security from weaker to stronger, while maintaining the incrementality property.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_7"}, {"primary_key": "3747758", "vector": [], "sparse_vector": [], "title": "New Security Notions and Feasibility Results for Authentication of Quantum Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give a new class of security definitions for authentication in the quantum setting. These definitions capture and strengthen existing definitions of security against quantum adversaries for bothclassicalmessage authentication codes (MACs) as well as full quantum state authentication schemes. The main feature of our definitions is that they preciselycharacterizethe effective behavior of any adversary when the authentication protocol accepts, including correlations with the key. Our definitions readily yield a host of desirable properties and interesting consequences; for example, our security definition for full quantum state authentication implies that the entire secret key can be re-used if the authentication protocol succeeds. Next, we present several protocols satisfying our security definitions. We show that the classical Wegman-Carter authentication scheme with 3-universal hashing is secure against superposition attacks, as well as adversaries with quantum side information. We then present conceptually simple constructions of full quantum state authentication. Finally, we prove a lifting theorem which shows that, as long as a protocol can securely authenticate the maximally entangled state, it can securely authenticate any state, even those that are entangled with the adversary. Thus, this shows that protocols satisfying a fairly weak form of authentication security automatically satisfy a stronger notion of security (in particular, the definition of <PERSON><PERSON><PERSON> et al. (2012)).", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_12"}, {"primary_key": "3747759", "vector": [], "sparse_vector": [], "title": "Kurosawa-Desmedt Meets Tight Security.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "At EUROCRYPT 2016, <PERSON> et al. presented the first pairing-free public-key encryption (PKE) scheme with an almost tight security reduction to a standard assumption. Their scheme is competitive in efficiency with state-of-the art PKE schemes and has very compact ciphertexts (of three group elements), but suffers from a large public key (of about\\(200\\)group elements). In this work, we present an improved pairing-free PKE scheme with an almost tight security reduction to the Decisional Diffie-Hell<PERSON> assumption, small ciphertexts (of three group elements),andsmall public keys (of six group elements). Compared to the work of <PERSON> et al., our scheme thus has a considerably smaller public key and comparable other characteristics, although our encryption and decryption algorithms are somewhat less efficient. Technically, our scheme borrows ideas both from the work of <PERSON> et al. and from a recent work of <PERSON><PERSON><PERSON><PERSON> (EUROCRYPT, 2017). The core technical novelty of our work is an efficient and compact designated-verifier proof system for an OR-like language. We show that adding such an OR-proof to the ciphertext of the state-of-the-art PKE scheme from Kurosawa and Desmedt enables a tight security reduction.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_5"}, {"primary_key": "3747760", "vector": [], "sparse_vector": [], "title": "Snarky Signatures: Minimal Signatures of Knowledge from Simulation-Extractable SNARKs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We construct a pairing based simulation-extractable SNARK (SE-SNARK) that consists of only 3 group elements and has highly efficient verification. By formally linking SE-SNARKs to signatures of knowledge, we then obtain a succinct signature of knowledge consisting of only 3 group elements. SE-SNARKs enable a prover to give a proof that they know a witness to an instance in a manner which is: (1)succinct- proofs are short and verifier computation is small; (2)zero-knowledge- proofs do not reveal the witness; (3)simulation-extractable- it is only possible to prove instances to which you know a witness, even when you have already seen a number of simulated proofs. We also prove that any pairing based signature of knowledge or SE-NIZK argument must have at least 3 group elements and 2 verification equations. Since our constructions match these lower bounds, we have the smallest size signature of knowledge and the smallest size SE-SNARK possible.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_20"}, {"primary_key": "3747761", "vector": [], "sparse_vector": [], "title": "Message Franking via Committing Authenticated Encryption.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We initiate the study of message franking, recently introduced in Facebook’s end-to-end encrypted message system. It targets verifiable reporting of abusive messages to Facebook without compromising security guarantees. We capture the goals of message franking via a new cryptographic primitive: compactly committing authenticated encryption with associated data (AEAD). This is an AEAD scheme for which a small part of the ciphertext can be used as a cryptographic commitment to the message contents. Decryption provides, in addition to the message, a value that can be used to open the commitment. Security for franking mandates more than that required of traditional notions associated with commitment. Nevertheless, and despite the fact that AEAD schemes are in general not committing (compactly or otherwise), we prove that many in-use AEAD schemes can be used for message franking by using secret keys as openings. An implication of our results is the first proofs that several in-use symmetric encryption schemes are committing in the traditional sense. We also propose and analyze schemes that retain security even after openings are revealed to an adversary. One is a generalization of the scheme implicitly underlying Facebook’s message franking protocol, and another is a new construction that offers improved performance.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_3"}, {"primary_key": "3747762", "vector": [], "sparse_vector": [], "title": "Optimal Security Reductions for Unique Signatures: Bypassing Impossibilities with a Counterexample.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Optimal security reductions for unique signatures (<PERSON><PERSON>, Eurocrypt 2002) and their generalization, i.e., efficiently re-randomizable signatures (<PERSON><PERSON><PERSON>zet al.PKC 2012 & <PERSON><PERSON><PERSON> al.Eurocrypt 2016) have been well studied in the literature. Particularly, it has been shown that under a non-interactive hard assumption, any security reduction (with or without random oracles) for a unique signature scheme or an efficiently re-randomizable signature scheme must loose a factor of at least\\(q_s\\)in the security model of existential unforgeability against chosen-message attacks (EU-CMA), where\\(q_s\\)denotes the number of signature queries. Note that the number\\(q_s\\)can be as large as\\(2^{30}\\)in practice. All unique signature schemes and efficiently re-randomizable signature schemes are concluded to be accompanied with loose reductions from these impossibility results. Somewhat surprisingly, in contrast to previous impossibility results (Coron, Eurocrypt 2002; Hofheinzet al.PKC 2012; Baderet al.Eurocrypt 2016), in this work we show that without changing the assumption type and security model, it is not always the case that any security reduction must loose a factor of at least\\(q_s\\). As a counterexample, we propose a unique signature scheme with a tight reduction in the EU-CMA security model under the Computational Diffie-Hellman (CDH) assumption. Precisely, in the random oracle model, we can program a security reduction with a loss factor of at most\\(nq^{1/{n}}\\), wherencan be any integer independent of the security parameter for the scheme construction andqis the number of hash queries to random oracles. The loss factor in our reduction can be very small. Considering\\(n=25\\)and\\(q=2^{50}\\)as an example, the loss factor is of at most\\(nq^{1/{n}}=100\\)and therefore our security reduction is tight. Notice that the previous impossibility results are derived from proofs via a so-called meta-reduction technique. We stress that instead of indicating any flaw in their meta-reduction proofs, our counterexample merely demonstrates that their given meta-reduction proofs fail to capture all security reductions. More precisely, we adopt a reduction calledquery-based reduction, where the reduction uses a hash query from the adversary to solve an underlying hard problem. We show that the meta-reduction proofs break down in our query-based reduction. The query-based reduction is not a new notion and it has been adopted for encryption proofs, but this work is the first seminal approach for applying query-based reduction in digital signatures. The given counterexample in this work is of an independent interest as it implies a generic way of constructing a digital signature scheme (including unique signatures) with a tight reduction in the random oracle model from a digital signature scheme with a loose reduction. Although our proposed methodology is somewhat impractical due to the inefficiency of signature length, it introduces a completely new approach for tight proofs that is different from traditional approaches using a random salt.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_18"}, {"primary_key": "3747763", "vector": [], "sparse_vector": [], "title": "ZMAC: A Fast Tweakable Block Cipher Mode for Highly Secure Message Authentication.", "authors": ["Tetsu Iwata", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new mode of operation called\\(\\mathsf {ZMAC}\\)allowing to construct a (stateless and deterministic) message authentication code (MAC) from a tweakable block cipher (TBC). When using a TBC withn-bit blocks andt-bit tweaks, our construction provides security (as a variable-input-length PRF) beyond the birthday bound with respect to the block-lengthnand allows to process\\(n+t\\)bits of inputs per TBC call. In comparison, previous TBC-based modes such asPMAC1, the TBC-based generalization of the seminalPMACmode (Black and Rogaway, EUROCRYPT 2002) orPMAC_TBC1k(Naito, ProvSec 2015) only processnbits of input per TBC call. Since ann-bit block,t-bit tweak TBC can process at most\\(n+t\\)bits of input per call, the efficiency of our construction is essentially optimal, while achieving beyond-birthday-bound security. The\\(\\mathsf {ZMAC}\\)mode is fully parallelizable and can be directly instantiated with several concrete TBC proposals, such asDeoxysandSKINNY. We also use\\(\\mathsf {ZMAC}\\)to construct a stateless and deterministic Authenticated Encryption scheme called\\(\\mathsf {ZAE}\\)which is very efficient and secure beyond the birthday bound.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_2"}, {"primary_key": "3747764", "vector": [], "sparse_vector": [], "title": "Be Adaptive, Avoid Overcommitting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For many cryptographic primitives, it is relatively easy to achieveselective security(where the adversary commits a-priori to some of the choices to be made later in the attack) but appears difficult to achieve the more natural notion ofadaptive security(where the adversary can make all choices on the go as the attack progresses). A series of several recent works shows how to cleverly achieve adaptive security in several such scenarios includinggeneralized selective decryption(<PERSON>j<PERSON><PERSON>, TCC ’07 and <PERSON><PERSON><PERSON><PERSON> et al., CRYPTO ’15),constrained PRFs(<PERSON><PERSON><PERSON><PERSON> et al., ASIACRYPT ’14), andYao garbled circuits(Jafargholi and Wichs, TCC ’16b). Although the above works expressed vague intuition that they share a common technique, the connection was never made precise. In this work we present a new framework that connects all of these works and allows us to present them in a unified and simplified fashion. Moreover, we use the framework to derive a new result for adaptively securesecret sharing over access structures defined via monotone circuits. We envision that further applications will follow in the future. Underlying our framework is the following simple idea. It is well known that selective security, where the adversary commits ton-bits of information about his future choices, automatically implies adaptive security at the cost of amplifying the adversary’s advantage by a factor of up to\\(2^n\\). However, in some cases the proof of selective security proceeds via a sequence of hybrids, where each pair of adjacent hybrids locally only requires some smaller partial information consisting of\\(m \\ll n\\)bits. The partial information needed might be completely different between different pairs of hybrids, and if we look across all the hybrids we might rely on the entiren-bit commitment. Nevertheless, the above is sufficient to prove adaptive security, at the cost of amplifying the adversary’s advantage by a factor of only\\(2^m \\ll 2^n\\). In all of our examples using the above framework, the different hybrids are captured by some sort of agraph pebbling gameand the amount of information that the adversary needs to commit to in each pair of hybrids is bounded by the maximum number of pebbles in play at any point in time. Therefore, coming up with better strategies for proving adaptive security translates to various pebbling strategies for different types of graphs.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_5"}, {"primary_key": "3747765", "vector": [], "sparse_vector": [], "title": "From Obfuscation to the Security of Fiat-Shamir for Proofs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Fiat-Shamir paradigm [CRYPTO’86] is a heuristic for converting three-round identification schemes into signature schemes, and more generally, for collapsing rounds in constant-round public-coin interactive protocols. This heuristic is very popular both in theory and in practice, and its security has been the focus of extensive study. In particular, this paradigm was shown to be secure in the Random Oracle Model. However, in the plain model, the results shown were mostly negative. In particular, the heuristic was shown to beinsecurewhen applied tocomputationally soundproofs (also known as arguments). Moreover, recently it was shown that even in the restricted setting where the heuristic is applied to interactiveproofs(as opposed to arguments), its soundness cannot be proven via a black-box reduction to any so-calledfalsifiableassumption. In this work, we give apositive resultfor the security of this paradigm in theplain model. Specifically, we construct a hash function for which the Fiat Shamir paradigm issecurewhen applied to proofs (as opposed to arguments), assuming the existence of a sub-exponentially secure indistinguishability obfuscator, the existence of an exponentially secure input-hiding obfuscator for the class of multi-bit point functions, and the existence of a sub-exponentially secure one-way function. More generally, we construct a hash family that iscorrelation intractable(under the computational assumptions above), solving an open problem originally posed by <PERSON><PERSON>, <PERSON> and <PERSON><PERSON> (JACM, 2004), under the above assumptions. In addition, we show that our result resolves a long-lasting open problem in about zero-knowledge proofs: It implies that there does not exist a public-coin constant-round zero-knowledge proof with negligible soundness (under the assumptions stated above).", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_8"}, {"primary_key": "3747766", "vector": [], "sparse_vector": [], "title": "Ouroboros: A Provably Secure Proof-of-Stake Blockchain Protocol.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present “Ouroboros”, the first blockchain protocol based onproof of stakewith rigorous security guarantees. We establish security properties for the protocol comparable to those achieved by the bitcoin blockchain protocol. As the protocol provides a “proof of stake” blockchain discipline, it offers qualitative efficiency advantages over blockchains based on proof of physical resources (e.g., proof of work). We also present a novel reward mechanism for incentivizing Proof of Stake protocols and we prove that, given this mechanism, honest behavior is an approximate Nash equilibrium, thus neutralizing attacks such as selfish mining.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_12"}, {"primary_key": "3747767", "vector": [], "sparse_vector": [], "title": "Watermarking Cryptographic Functionalities from Standard Lattice Assumptions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A software watermarking scheme allows one to embed a “mark” into a program without significantly altering the behavior of the program. Moreover, it should be difficult to remove the watermark without destroying the functionality of the program. Recently, <PERSON> et al. (STOC 2016) and <PERSON><PERSON> et al. (PKC 2017) showed how to watermark cryptographic functions such as PRFs using indistinguishability obfuscation. Notably, in their constructions, the watermark remains intact even againstarbitraryremoval strategies. A natural question is whether we can build watermarking schemes from standard assumptions that achieve this strong mark-unremovability property. We give the first construction of a watermarkable family of PRFs that satisfy this strong mark-unremovability property from standard lattice assumptions (namely, the learning with errors (LWE) and the one-dimensional short integer solution (SIS) problems). As part of our construction, we introduce a new cryptographic primitive called a translucent PRF. Next, we give a concrete construction of a translucent PRF family from standard lattice assumptions. Finally, we show that using our new lattice-based translucent PRFs, we obtain the first watermarkable family of PRFs with strong unremovability against arbitrary strategies from standard assumptions.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_17"}, {"primary_key": "3747768", "vector": [], "sparse_vector": [], "title": "Privacy-Free Garbled Circuits for Formulas: Size Zero and Information-Theoretic.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Garbled circuits are of central importance in cryptography, finding widespread application in secure computation, zero-knowledge (ZK) protocols, and verifiable outsourcing of computation to name a few. We are interested in a particular kind of garbling scheme, termedprivacy-freein the literature. We show that Boolean formulas can be garbledinformation-theoreticallyin the privacy-free setting, producingnociphertexts at all. Existing garbling schemes either rely on cryptographic assumptions (and thus require cryptographic operations to construct and evaluate garbled circuits), produce garbled circuits of non-zero size, or are restricted to low depth formulaic circuits. Our result has both theoretical and practical implications for garbled circuits as a primitive. On the theory front, our result breaks the known theoretical lower bound of one ciphertext for garbling an AND gate in this setting. As an interesting implication of producing size zero garbled circuits, our scheme scores adaptive security for free. On the practical side, our garbling scheme involves only cheap XOR operations and produces size zero garbled circuits. As a side result, we propose several interesting extensions of our scheme. Namely, we show how to garble threshold and high fan-in gates. An aspect of our garbling scheme that we believe is of theoretical interest is that it doesnotmaintain the invariant that the garbled circuit evaluator must not at any point be in possession of both keys of any wire in the garbled circuit. Our scheme directly finds application in ZK protocols where the verification function of the language is representable by a formulaic circuit. Such examples include Boolean formula satisfiability. The ZK protocols obtained by plugging in our scheme in the known paradigm of building ZK protocols from garbled circuits offer better proof size, while relying on standard assumptions. Furthermore, the adaptivity of our garbling scheme allows us to cast our ZK protocols in the offline-online setting and offload circuit dependent communication and computation to the offline phase. As a result, the online phase enjoys communication and computation (in terms of number of symmetric key operations) complexity that are linearly proportional to the witness size alone.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_7"}, {"primary_key": "3747769", "vector": [], "sparse_vector": [], "title": "All-But-Many Lossy Trapdoor Functions and Selective Opening Chosen-Ciphertext Security from LWE.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Selective opening (SO) security refers to adversaries that receive a number of ciphertexts and, after having corrupted a subset of the senders (thus obtaining the plaintexts and the senders’ random coins), aim at breaking the security of remaining ciphertexts. So far, very few public-key encryption schemes are known to provide simulation-based selective opening (SIM-SO-CCA2) security under chosen-ciphertext attacks and most of them encrypt messages bit-wise. The only exceptions to date rely onall-but-manylossy trapdoor functions (as introduced by <PERSON><PERSON><PERSON><PERSON>; Eurocrypt’12) and the Composite Residuosity assumption. In this paper, we describe the first all-but-many lossy trapdoor function with security relying on the presumed hardness of the Learning-With-Errors problem (\\(\\mathsf {LWE}\\)) with standard parameters. Our construction exploits homomorphic computations on lattice trapdoors for lossy\\(\\mathsf {LWE}\\)matrices. By carefully embedding a lattice trapdoor in lossy public keys, we are able to prove SIM-SO-CCA2 security under the\\(\\mathsf {LWE}\\)assumption. As a result of independent interest, we describe a variant of our scheme whose multi-challenge CCA2 security tightly relates to the hardness of\\(\\mathsf {LWE}\\)and the security of a pseudo-random function.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_12"}, {"primary_key": "3747770", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation from SXDH on 5-Linear Maps and Locality-5 PRGs.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Two recent works [Lin, EUROCRYPT 2016, <PERSON> and <PERSON><PERSON><PERSON>, FOCS 2016] showed how to construct Indistinguishability Obfuscation (IO) from constant degree multilinear maps. However, the concrete degrees of multilinear maps used in their constructions exceed 30. In this work, we reduce the degree of multilinear maps needed to 5, by giving a new construction of IO from asymmetricL-linear maps and a pseudo-random generator (PRG) with output localityLand polynomial stretch. When plugging in a candidate PRG with locality-5 (e.g., [Goldreich, ECCC 2010, Mossel, Shpilka, and Trevisan, FOCS 2013, <PERSON><PERSON> and <PERSON>, CCC 2014]), we obtain a construction of IO from5-linear maps. Our construction improves the state-of-the-art at two other fronts: First, it relies on “classical” multilinear maps, instead of their powerful generalization of graded encodings. Second, it comes with a security reduction to (i) the SXDH assumption on algebraic multilinear maps [<PERSON><PERSON> and <PERSON>, Contemporary Mathematics, Rothblum, TCC 2013], (ii) the security of PRG, and (iii) sub-exponential LWE, all with sub-exponential hardness. The SXDH assumption is weaker and/or simpler than assumptions on multilinear maps underlying previous IO constructions. When noisy multilinear maps [<PERSON><PERSON><PERSON> al., EUROCRYPT 2013] are used instead, security is based on a family of more complex assumptions that hold in the generic model.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_20"}, {"primary_key": "3747771", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation from Trilinear Maps and Block-Wise Local PRGs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the question of finding the lowest degreeLfor whichL-linear maps suffice to obtain IO. The current state of the art (<PERSON>, EUROCRYPT’16, CRYPTO ’17; <PERSON> and <PERSON><PERSON><PERSON><PERSON>, FOCS’16; <PERSON><PERSON><PERSON> and <PERSON><PERSON>, EUROCRYPT ’17) is thatL-linear maps (under suitable security assumptions) suffice for IO, assuming the existence of pseudo-random generators (PRGs) with output localityL. However, these works cannot answer the question of whether\\(L < 5\\)suffices, as no polynomial-stretch PRG with locality lower than 5 exists. In this work, we present a new approach that relies on the existence of PRGs withblock-wise localityL, i.e., every output bit depends on at mostL(disjoint)input blocks, each consisting of up to\\(\\log \\lambda \\)input bits. We show that the existence of PRGs with block-wise locality is plausible for any\\(L \\ge 3\\), and also provide: A construction of a general-purpose indistinguishability obfuscator fromL-linear maps and a subexponentially-secure PRG with block-wise localityLand polynomial stretch. A construction of general-purpose functional encryption fromL-linear maps and any slightly super-polynomially secure PRG with block-wise localityLand polynomial stretch. All our constructions are based on the SXDH assumption onL-linear maps and subexponential Learning With Errors (LWE) assumption, and follow by instantiating our new generic bootstrapping theorems with <PERSON>’s recently proposed FE scheme (CRYPTO ’17). Inherited from Lin’s work, our security proof requires algebraic multilinear maps (Boneh and Silverberg, Contemporary Mathematics), whereas security when using noisy multilinear maps is based on a family of more complex assumptions that hold in the generic model. Our candidate PRGs with block-wise locality are based on Goldreich’s local functions, and we show that the security of instantiations with block-wise locality\\(L \\ge 3\\)is backed by similar validation as constructions with (conventional) locality 5. We further complement this with hardness amplification techniques that further weaken the pseudorandomness requirements.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_21"}, {"primary_key": "3747772", "vector": [], "sparse_vector": [], "title": "Fast Secure Two-Party ECDSA Signing.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "ECDSA is a standard digital signature schemes that is widely used in TLS, Bitcoin and elsewhere. Unlike other schemes like RSA, Schnorr signatures and more, it is particularly hard to construct efficient threshold signature protocols for ECDSA (and DSA). As a result, the best-known protocols today for secure distributed ECDSA require running heavy zero-knowledge proofs and computing many large-modulus exponentiations for every signing operation. In this paper, we consider the specific case of two parties (and thus no honest majority) and construct a protocol that is approximatelytwo orders of magnitude fasterthan the previous best. Concretely, our protocol achieves good performance, with a single signing operation for curve P-256 taking approximately 37 ms between two standard machine types in Azure (utilizing a single core only). Our protocol is proven secure under standard assumptions using a game-based definition. In addition, we prove security by simulation under a plausible yet non-standard assumption regarding Paillier.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_21"}, {"primary_key": "3747773", "vector": [], "sparse_vector": [], "title": "Degree Evaluation of NFSR-Based Cryptosystems.", "authors": ["Mei<PERSON> Liu"], "summary": "In this paper, we study the security of NFSR-based cryptosystems from the algebraic degree point of view. We first present a general framework of iterative estimation of algebraic degree for NFSR-based cryptosystems, by exploiting a new technique, callednumeric mapping. Then based on this general framework we propose a concrete and efficient algorithm to find an upper bound on the algebraic degree for Trivium-like ciphers. Our algorithm has linear time complexity and needs a negligible amount of memory. As illustrations, we apply it toTrivium,KreyviumandTriviA-SC, and reveal various upper bounds on the algebraic degree of these ciphers by setting different input variables. By this algorithm, we can make use of a cube with any size in cube testers, which is generally believed to be infeasible for an NFSR-based cryptosystem before. Due to the high efficiency of our algorithm, we can exhaust a large set of the cubes with large size. As such, we obtain the best known distinguishing attacks on reducedTriviumandTriviA-SCas well as the first cryptanalysis ofKreyvium. Our experiments onTriviumshow that our algorithm is not only efficient in computation but also accurate in estimation of attacked rounds. The best cubes we have found forKreyviumandTriviA-SCare both of size larger than 60. To the best of our knowledge, our tool is the first formalized and systematic one for finding an upper bound on the algebraic degree of an NFSR-based cryptosystem, and this is the first time that a cube of size beyond practical computations can be used in cryptanalysis of an NFSR-based cryptosystem. It is also potentially useful in the future applications to key recovery attacks and more cryptographic primitives.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_8"}, {"primary_key": "3747774", "vector": [], "sparse_vector": [], "title": "Conditional Disclosure of Secrets via Non-linear Reconstruction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We present new protocols for conditional disclosure of secrets (CDS), where two parties want to disclose a secret to a third party if and only if their respective inputs satisfy some predicate. For general predicates\\(\\mathsf {P}: [N] \\times [N] \\rightarrow \\{0,1\\}\\), we present two protocols that achieve\\(o(N^{1/2})\\)communication: the first achieves\\(O(N^{1/3})\\)communication and the second achieves sub-polynomial\\(2^{O(\\sqrt{\\log N \\log \\log N})} = N^{o(1)}\\)communication. As a corollary, we obtain improved share complexity for forbidden graph access structures. Namely, for every graph onNvertices, there is a secret-sharing scheme forNparties in which each pair of parties can reconstruct the secret if and only if the corresponding vertices inGare connected, and where each party gets a share of size\\(2^{O(\\sqrt{\\log N \\log \\log N})} = N^{o(1)}\\). Prior to this work, the best protocols for both primitives required communication complexity\\(\\tilde{O}(N^{1/2})\\). Indeed, this is essentially the best that all prior techniques could hope to achieve as they were limited to so-called “linear reconstruction”. This is the first work to break this\\(O(N^{1/2})\\)“linear reconstruction” barrier in settings related to secret sharing. To obtain these results, we draw upon techniques for non-linear reconstruction developed in the context of information-theoretic private information retrieval. We further extend our results to the setting of private simultaneous messages (PSM), and provide applications such as an improved attribute-based encryption (ABE) for quadratic polynomials.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_25"}, {"primary_key": "3747775", "vector": [], "sparse_vector": [], "title": "Black-Box Parallel Garbled RAM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In 1982, <PERSON> introduced a technique of “circuit garbling” that became a central building block in cryptography. The question of garbling general random-access memory (RAM) programs was introduced by <PERSON> and <PERSON><PERSON><PERSON> in 2013. The most recent results of <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (FOCS 2015) achieve a garbled RAM with black-box use of any one-way functions and poly-log overhead of data and program garbling in all the relevant parameters, including program run-time. The advantage of Garbled RAM is that large data can be garbled first, and act as persistent garbled storage (e.g. in the cloud) and later programs can be garbled and sent to be executed on this garbled database in a non-interactive manner. One of the main advantages of cloud computing is not only that it has large storage but also that it has a large number of parallel processors. Despite multiple successful efforts on parallelizing (interactive) Oblivious RAM, the non-interactive garbling of parallel programs remained open until very recently. Specifically, <PERSON>, <PERSON> and <PERSON> in their TCC 2016-A [4] have shown how to garble PRAM programs with poly-logarithmic (parallel) overhead assuming non-black-box use of identity-based encryption (IBE). The question of whether the IBE assumption, and in particular, the non-black-box use of such a strong assumption is needed. In this paper, we resolve this question and show how to garble parallel programs, with black-box use of only one-way functions and with only poly-log overhead in the (parallel) running time. Our result works for any number of parallel processors.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_3"}, {"primary_key": "3747776", "vector": [], "sparse_vector": [], "title": "Insuperability of the Standard Versus Ideal Model Gap for Tweakable Blockcipher Security.", "authors": ["<PERSON>"], "summary": "Two types of tweakable blockciphers based on classical blockciphers have been presented over the last years: non-tweak-rekeyable and tweak-rekeyable, depending on whether the tweak may influence the key input to the underlying blockcipher. In the former direction, the best possible security is conjectured to be\\(2^{\\sigma n/(\\sigma +1)}\\), wherenis the size of the blockcipher and\\(\\sigma \\)is the number of blockcipher calls. In the latter direction, <PERSON><PERSON><PERSON> and <PERSON> et al. presented optimally secure schemes, but only in the ideal cipher model. We investigate the possibility to construct a tweak-rekeyable cipher that achieves optimal security in the standard cipher model. As a first step, we note that all standard-model security results in literature implicitly rely on a generic standard-to-ideal transformation, that replaces all keyed blockcipher calls by random secret permutations, at the cost of the security of the blockcipher. Then, we prove that if this proof technique is adopted, tweak-rekeying will not help in achieving optimal security: if\\(2^{\\sigma n/(\\sigma +1)}\\)is the best one can getwithouttweak-rekeying, optimal\\(2^n\\)provable securitywithtweak-rekeying is impossible.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_24"}, {"primary_key": "3747777", "vector": [], "sparse_vector": [], "title": "Encrypted <PERSON><PERSON><PERSON> and Its Dual: Towards Optimal Security Using Mirror Theory.", "authors": ["<PERSON>", "<PERSON>"], "summary": "At CRYPTO 2016, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> introduced the Encrypted Davies<PERSON>Meyer construction,\\(p_2(p_1(x) \\oplus x)\\)for twon-bit permutations\\(p_1,p_2\\), and proved security up to\\(2^{2n/3}\\). We present an improved security analysis up to\\(2^n/(67n)\\). Additionally, we introduce the dual of the Encrypted Davies<PERSON>Meyer construction,\\(p_2(p_1(x)) \\oplus p_1(x)\\), and prove even tighter security for this construction:\\(2^n/67\\). We finally demonstrate that the analysis neatly generalizes to prove almost optimal security of the Encrypted Wegman-Carter with Davies-<PERSON> MAC construction. Central to our analysis is a modernization of <PERSON><PERSON><PERSON>’s mirror theorem and an exposition of how it relates to fundamental cryptographic problems.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_19"}, {"primary_key": "3747778", "vector": [], "sparse_vector": [], "title": "Gaussian Sampling over the Integers: Efficient, Generic, Constant-Time.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Sampling integers with Gaussian distribution is a fundamental problem that arises in almost every application of lattice cryptography, and it can be both time consuming and challenging to implement. Most previous work has focused on the optimization and implementation of integer Gaussian sampling in the context of specific applications, with fixed sets of parameters. We present new algorithms for discrete Gaussian sampling that are both generic (application independent), efficient, and more easily implemented in constant time without incurring a substantial slow-down, making them more resilient to side-channel (e.g., timing) attacks. As an additional contribution, we present new analytical techniques that can be used to simplify the precision/security evaluation of floating point cryptographic algorithms, and an experimental comparison of our algorithms with previous algorithms from the literature.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_16"}, {"primary_key": "3747779", "vector": [], "sparse_vector": [], "title": "Amortization with Fewer Equations for Proving Knowledge of Small Secrets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For a linear functionf, a vector\\(\\mathbf{x}\\)with small coefficients, and a vector\\(y=f(\\mathbf{x})\\), we would like to be able to give a zero-knowledge proof for the knowledge of an\\(\\mathbf{x}'\\)with small coefficients that satisfies\\(f(\\mathbf{x}')=y\\). This is a common scenario in lattice-based cryptography, and there is currently no satisfactory solution for this problem. All known protocols are built via the repetition of a basic protocol that only has constant (1/2 or 2/3) soundness error. This implies that the communication complexity of the final protocol will be at least a factor ofklarger than that of the basic one, wherekis the security parameter. One can do better if one considers simultaneously proving the knowledge of many instances of the above linear equation. The protocol that has the smallest amortized communication complexity while achieving close-to-optimal slack (i.e. the ratio between the coefficients in the secret and those that can be extracted from the proof) is due to <PERSON><PERSON><PERSON> et al. (Eurocrypt ’17) which builds on an earlier work of <PERSON><PERSON> et al. (Crypto ’16). The main downside of this protocol is that the amortization only kicks in when the number of equations is rather large –\\(4k^2\\). This means that for\\(k=128\\), it is only truly optimal when one has more than\\(2^{16}\\)equations to prove. The aforementioned work of <PERSON><PERSON><PERSON> et al. also shows how to achieve a protocol requiring\\(o(k^2)\\)samples, but it is only applicable for much larger values ofkand the number of required samples ends up being larger than\\(2^{16}\\). The main result of our work is reducing the concrete minimal number of equations required for the amortization, while keeping the communication complexity almost unchanged. The cost of this is an increase in the running time of the zero-knowledge proof. More specifically, we show that one can decrease the required number of equations by a factor of\\(\\varOmega (\\log ^2{\\alpha })\\)at the cost of increasing the running time by a factor of\\(\\varOmega (\\alpha )\\). For example, increasing the running time by a factor of 8 allows us to decrease the required number of samples from 69000 to 4500 – a factor of 15. As a side benefit, the slack of our protocol decreases by a factor of\\(\\log {\\alpha }\\)as well. We also show that in the case thatfis a function over the polynomial ring\\(\\mathbb {Z}[X]/(X^d+1)\\)and we would like to give a proof of knowledge of an\\(\\mathbf{x}'\\)with small coefficients such that\\(f(\\mathbf{x}')=2y\\), then the number of samples needed for amortization is even lower. Without any trade-offs in the running time, our algorithm requires around 2000 samples, and for the same factor 8 increase in the running time, the requirement goes down to 850.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_13"}, {"primary_key": "3747780", "vector": [], "sparse_vector": [], "title": "Middle-Product Learning with Errors.", "authors": ["Miruna Rosca", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a new variant\\(\\mathsf {MP}\\text {-}\\mathsf {LWE}\\)of the Learning With Errors problem (\\(\\mathsf {LWE}\\)) making use of the Middle Product between polynomials modulo an integerq. We exhibit a reduction from the Polynomial-\\(\\mathsf {LWE}\\)problem (\\(\\mathsf {PLWE}\\)) parametrized by a polynomialf, to\\(\\mathsf {MP}\\text {-}\\mathsf {LWE}\\)which is defined independently of any suchf. The reduction only requiresfto be monic with constant coefficient coprime withq. It incurs a noise growth proportional to the so-called expansion factor off. We also describe a public-key encryption scheme with quasi-optimal asymptotic efficiency (the bit-sizes of the keys and the run-times of all involved algorithms are quasi-linear in the security parameter), which is secure against chosen plaintext attacks under the\\(\\mathsf {MP}\\text {-}\\mathsf {LWE}\\)hardness assumption. The scheme is hence secure under the assumption that\\(\\mathsf {PLWE}\\)is hard for at least one polynomialfof degreenamong a family off’s which is exponential inn.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_10"}, {"primary_key": "3747781", "vector": [], "sparse_vector": [], "title": "Non-full Sbox Linearization: Applications to Collision Attacks on Round-Reduced Keccak.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "TheKeccakhash function is the winner of the SHA-3 competition and became the SHA-3 standard of NIST in 2015. In this paper, we focus on practical collision attacks against round-reducedKeccakhash function, and two main results are achieved: the first practical collision attacks against 5-roundKeccak-224 and an instance of 6-roundKeccakcollision challenge. Both improve the number of practically attacked rounds by one. These results are obtained by carefully studying the algebraic properties of the nonlinear layer in the underlying permutation ofKeccakand applying linearization to it. In particular, techniques for partially linearizing the output bits of the nonlinear layer are proposed, utilizing which attack complexities are reduced significantly from the previous best results.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63715-0_15"}, {"primary_key": "3747782", "vector": [], "sparse_vector": [], "title": "The First Collision for Full SHA-1.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "SHA-1 is a widely used 1995 NIST cryptographic hash function standard that was officially deprecated by NIST in 2011 due to fundamental security weaknesses demonstrated in various analyses and theoretical attacks. Despite its deprecation, SHA-1 remains widely used in 2017 for document and TLS certificate signatures, and also in many software such as the GIT versioning system for integrity and backup purposes. A key reason behind the reluctance of many industry players to replace SHA-1 with a safer alternative is the fact that finding an actual collision has seemed to be impractical for the past eleven years due to the high complexity and computational cost of the attack. In this paper, we demonstrate that SHA-1 collision attacks have finally become practical by providing the first known instance of a collision. Furthermore, the prefix of the colliding messages was carefully chosen so that they allow an attacker to forge two distinct PDF documents with the same SHA-1 hash that display different arbitrarily-chosen visual contents. We were able to find this collision by combining many special cryptanalytic techniques in complex ways and improving upon previous work. In total the computational effort spent is equivalent to\\(2^{63.1}\\)calls to SHA-1’s compression function, and took approximately 6 500 CPU years and 100 GPU years. While the computational power spent on this collision is larger than other public cryptanalytic computations, it is still more than 100 000 times faster than a brute force search.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63688-7_19"}, {"primary_key": "3747783", "vector": [], "sparse_vector": [], "title": "Cube Attacks on Non-Blackbox Polynomials Based on Division Property.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The cube attack is a powerful cryptanalytic technique and is especially powerful against stream ciphers. Since we need to analyze the complicated structure of a stream cipher in the cube attack, the cube attack basically analyzes it by regarding it as a blackbox. Therefore, the cube attack is an experimental attack, and we cannot evaluate the security when the size of cube exceeds an experimental range, e.g., 40. In this paper, we propose cube attacks on non-blackbox polynomials. Our attacks are developed by using the division property, which is recently applied to various block ciphers. The clear advantage is that we can exploit large cube sizes because it never regards the cipher as a blackbox. We apply the new cube attack toTrivium, Grain128a, and ACORN. As a result, the secret keys of 832-roundTrivium, 183-round Grain128a, and 704-round ACORN are recovered. These attacks are the current best key-recovery attack against these ciphers.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_9"}, {"primary_key": "3747784", "vector": [], "sparse_vector": [], "title": "A New Distribution-Sensitive Secure Sketch and Popularity-Proportional Hashing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Motivated by typo correction in password authentication, we investigate cryptographic error-correction of secrets in settings where the distribution of secrets is a priori (approximately) known. We refer to this as the distribution-sensitive setting. We design a new secure sketch called the layer-hiding hash (LHH) that offers the best security to date. Roughly speaking, we show that LHH saves an additional\\(\\log H_0(W)\\)bits of entropy compared to the recent layered sketch construction due to <PERSON>, <PERSON>, and <PERSON> (FRS). Here\\(H_0(W)\\)is the size of the support of the distributionW. When supports are large, as with passwords, our new construction offers a substantial security improvement. We provide two new constructions of typo-tolerant password-based authentication schemes. The first combines a LHH or FRS sketch with a standard slow-to-compute hash function, and the second avoids secure sketches entirely, correcting typos instead by checking all nearby passwords. Unlike the previous such brute-force-checking construction, due to <PERSON><PERSON> et al., our new construction uses a hash function whose runtime is proportional to the popularity of the password (forcing a longer hashing time on more popular, lower entropy passwords). We refer to this as popularity-proportional hashing (PPH). We then introduce a framework for comparing different typo-tolerant authentication approaches. We show that PPH always offers a better time / security trade-off than the LHH and FRS constructions, and for certain distributions outperforms the <PERSON><PERSON><PERSON> et al. construction. Elsewhere, this latter construction offers the best trade-off. In aggregate our results suggest that the best known secure sketches are still inferior to simpler brute-force based approaches.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_23"}, {"primary_key": "3747785", "vector": [], "sparse_vector": [], "title": "Asymptotically Compact Adaptively Secure Lattice IBEs and Verifiable Random Functions via Generalized Partitioning Techniques.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper, we focus on the constructions of adaptively secure identity-based encryption (IBE) from lattices and verifiable random function (VRF) with large input spaces. Existing constructions of these primitives suffer from low efficiency, whereas their counterparts with weaker guarantees (IBEs with selective security and VRFs with small input spaces) are reasonably efficient. We try to fill these gaps by developing new partitioning techniques that can be performed with compact parameters and proposing new schemes based on the idea. We propose new lattice IBEs with poly-logarithmic master public key sizes, where we count the number of the basic matrices to measure the size. Our constructions are proven secure under the LWE assumption with polynomial approximation factors. They achieve the best asymptotic space efficiency among existing schemes that depend on the same assumption and achieve the same level of security. We also propose several new VRFs on bilinear groups. In our first scheme, the size of the proofs is poly-logarithmic in the security parameter, which is the smallest among all the existing schemes with similar properties. On the other hand, the verification keys are long. In our second scheme, the size of the verification keys is poly-logarithmic, which is the smallest among all the existing schemes. The size of the proofs is sub-linear, which is larger than our first scheme, but still smaller than all the previous schemes.", "published": "2017-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-319-63697-9_6"}]