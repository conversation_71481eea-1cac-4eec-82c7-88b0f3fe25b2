[{"primary_key": "3403064", "vector": [], "sparse_vector": [], "title": "Unbounded ABE via Bilinear Entropy Expansion, Revisited.", "authors": ["<PERSON><PERSON>", "Junqing Gong", "<PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We present simpler and improved constructions of unbounded attribute-based encryption (ABE) schemes with constant-size public parameters under static assumptions in bilinear groups. Concretely, we obtain: a simple and adaptively secure unbounded ABE scheme in composite-order groups, improving upon a previous construction of <PERSON><PERSON><PERSON> and <PERSON> (Eurocrypt ’11) which only achieves selective security; an improved adaptively secure unbounded ABE scheme based on thek-linear assumption in prime-order groups with shorter ciphertexts and secret keys than those of Okamoto and Takashima (Asiacrypt ’12); the first adaptively secure unbounded ABE scheme for arithmetic branching programs under static assumptions. At the core of all of these constructions is a “bilinear entropy expansion” lemma that allows us to generate any polynomial amount of entropy starting from constant-size public parameters; the entropy can then be used to transform existing adaptively secure “bounded” ABE schemes into unbounded ones.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_19"}, {"primary_key": "3403065", "vector": [], "sparse_vector": [], "title": "Unforgeable Quantum Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of encrypting and authenticating quantum data in the presence of adversaries making adaptive chosen plaintext and chosen ciphertext queries. Classically, security games use string copying and comparison to detect adversarial cheating in such scenarios. Quantumly, this approach would violate no-cloning. We develop new techniques to overcome this problem: we use entanglement to detect cheating, and rely on recent results for characterizing quantum encryption schemes. We give definitions for (i) ciphertext unforgeability, (ii) indistinguishability under adaptive chosen-ciphertext attack, and (iii) authenticated encryption. The restriction of each definition to the classical setting is at least as strong as the corresponding classical notion: (i) implies\\(\\mathsf {INT\\text {-}CTXT}\\), (ii) implies\\(\\mathsf {IND\\text {-}CCA2}\\), and (iii) implies\\(\\mathsf {AE}\\). All of our new notions also imply\\(\\mathsf {QIND\\text {-}CPA}\\)privacy. Combining one-time authentication and classical pseudorandomness, we construct symmetric-key quantum encryption schemes for each of these new security notions, and provide several separation examples. Along the way, we also give a new definition of one-time quantum authentication which, unlike all previous approaches, authenticates ciphertexts rather than plaintexts.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_16"}, {"primary_key": "3403066", "vector": [], "sparse_vector": [], "title": "Sustained Space Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Memory-hard functions (MHF) are functions whose evaluation cost is dominated by memory cost. MHFs are egalitarian, in the sense that evaluating them on dedicated hardware (like FPGAs or ASICs) is not much cheaper than on off-the-shelf hardware (like x86 CPUs). MHFs have interesting cryptographic applications, most notably to password hashing and securing blockchains. <PERSON><PERSON> and <PERSON><PERSON><PERSON> [STOC’15] define the cumulative memory complexity (cmc) of a function as the sum (over all time-steps) of the amount of memory required to compute the function. They advocate that a good MHF must have high cmc. Unlike previous notions, cmc takes into account that dedicated hardware might exploit amortization and parallelism. Still, cmc has been critizised as insufficient, as it fails to capture possible time-memory trade-offs; as memory cost doesn’t scale linearly, functions with the same cmc could still have very different actual hardware cost. In this work we address this problem, and introduce the notion of sustained-memory complexity, which requires that any algorithm evaluating the function must use a large amount of memory for many steps. We construct functions (in the parallel random oracle model) whose sustained-memory complexity is almost optimal: our function can be evaluated usingnsteps and\\(O(n/\\log (n))\\)memory, in each step making one query to the (fixed-input length) random oracle, while any algorithm that can make arbitrary many parallel queries to the random oracle, still needs\\(\\varOmega (n/\\log (n))\\)memory for\\(\\varOmega (n)\\)steps. As has been done for various notions (including cmc) before, we reduce the task of constructing an MHFs with high sustained-memory complexity to proving pebbling lower bounds on DAGs. Our main technical contribution is the construction is a family of DAGs onnnodes with constant indegree with high “sustained-space complexity”, meaning that any parallel black-pebbling strategy requires\\(\\varOmega (n/\\log (n))\\)pebbles for at least\\(\\varOmega (n)\\)steps. Along the way we construct a family of maximally “depth-robust” DAGs with maximum indegree\\(O(\\log n)\\), improving upon the construction of Mahmoody et al. [ITCS’13] which had maximum indegree\\(O\\left( \\log ^2 n \\cdot {{\\mathsf {polylog}}} (\\log n)\\right) \\).", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_4"}, {"primary_key": "3403067", "vector": [], "sparse_vector": [], "title": "The Communication Complexity of Private Simultaneous Messages, Revisited.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Private Simultaneous Message (PSM) protocols were introduced by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (STOC ’94) as a minimal non-interactive model for information-theoretic three-party secure computation. While it is known that every function\\(f:\\{0,1\\}^k\\times \\{0,1\\}^k \\rightarrow \\{0,1\\}\\)admits a PSM protocol with exponential communication of\\(2^{k/2}\\)(<PERSON><PERSON><PERSON> et al., TCC ’14), the best known (non-explicit) lower-bound is\\(3k-O(1)\\)bits. To prove this lower-bound, FKN identified a set of simple requirements, showed that any function that satisfies these requirements is subject to the\\(3k-O(1)\\)lower-bound, and proved that a random function is likely to satisfy the requirements. We revisit the FKN lower-bound and prove the following results: (Counterexample)We construct a function that satisfies the FKN requirements but has a PSM protocol with communication of\\(2k+O(1)\\)bits, revealing a gap in the FKN proof. (PSM lower-bounds)We show that, by imposing additional requirements, the FKN argument can be fixed leading to a\\(3k-O(\\log k)\\)lower-bound for a random function. We also get a similar lower-bound for a function that can be computed by a polynomial-size circuit (or even polynomial-time Turing machine under standard complexity-theoretic assumptions). This yields the first non-trivial lower-bound for an explicit Boolean function partially resolving an open problem of Data, Prabhakaran and Prabhakaran (Crypto ’14, IEEE Information Theory ’16). We further extend these results to the setting of imperfect PSM protocols which may have small correctness or privacy error. (CDS lower-bounds)We show that the original FKN argument applies (as is) to some weak form of PSM protocols which are strongly related to the setting of Conditional Disclosure of Secrets (CDS). This connection yields a simple combinatorial criterion for establishing linear\\(\\varOmega (k)\\)-bit CDS lower-bounds. As a corollary, we settle the complexity of the Inner Product predicate resolving an open problem of Gay, Kerenidis, and Wee (Crypto ’15).", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_9"}, {"primary_key": "3403068", "vector": [], "sparse_vector": [], "title": "But Why Does It Work? A Rational Protocol Design Treatment of Bitcoin.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An exciting recent line of work has focused on formally investigating the core cryptographic assumptions underlying the security of Bitcoin. In a nutshell, these works conclude that Bitcoin is secure if and only if the majority of the mining power is honest. Despite their great impact, however, these works do not address an incisive question asked by positivists and Bitcoin critics, which is fuelled by the fact that Bitcoin indeed works in reality: Why should the real-world system adhere to these assumptions? In this work we employ the machinery from the Rational Protocol Design (RPD) framework by Garayet al.[FOCS 2013] to analyze Bitcoin and address questions such as the above. We show that under the natural class of incentives for the miners’ behavior—i.e., rewarding them for adding blocks to the blockchain but having them pay for mining—we can reserve the honest majority assumption as a fallback, or even, depending on the application, completely replace it by the assumption that the miners aim to maximize their revenue. Our results underscore the appropriateness of RPD as a “rational cryptography” framework for analyzing Bitcoin. Along the way, we devise significant extensions to the original RPD machinery that broaden its applicability to cryptocurrencies, which may be of independent interest.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_2"}, {"primary_key": "3403069", "vector": [], "sparse_vector": [], "title": "Exploring the Boundaries of Topology-Hiding Computation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Topology-hiding computation (THC) is a form of multi-party computation over an incomplete communication graph that maintains the privacy of the underlying graph topology. In a line of recent works [<PERSON>, <PERSON><PERSON> & Rich<PERSON>on TCC’15, <PERSON><PERSON> et al. CRYPTO’16, <PERSON><PERSON><PERSON> & Moran EUROCRYPT’17, <PERSON><PERSON><PERSON> et al. CRYPTO’17], THC protocols for securely computing any function in thesemi-honestsetting have been constructed. In addition, it was shown by <PERSON> et al. that in the fail-stop setting THC with negligible leakage on the topology is impossible. In this paper, we further explore the feasibility boundaries of THC. We show that even against semi-honest adversaries, topology-hiding broadcast on a small (4-node) graph implies oblivious transfer; in contrast, trivial broadcast protocols exist unconditionally if topology can be revealed. We strengthen the lower bound of <PERSON> et al. identifying and extending a relation between theamountof leakage on the underlying graph topology that must be revealed in the fail-stop setting, as a function of the number of parties and communication round complexity: Anyn-party protocol leaking\\(\\delta \\)bits for\\(\\delta \\in (0,1]\\)must have\\(\\varOmega (n/\\delta )\\)rounds. We then present THC protocols providing close-to-optimal leakage rates, forunrestricted graphsonnnodes against a fail-stop adversary controlling a dishonest majority of thenplayers. These constitute the first general fail-stop THC protocols. Specifically, for this setting we show: A THC protocol that leaks at most one bit and requires\\(O(n^2)\\)rounds. A THC protocol that leaks at most\\(\\delta \\)bits for arbitrarily small non-negligible\\(\\delta \\), and requires\\(O(n^3/\\delta )\\)rounds. These protocols also achieve full security (with no leakage) for the semi-honest setting. Our protocols are based on one-way functions and a (stateless)secure hardware boxprimitive. This provides a theoretical feasibility result, a heuristic solution in the plain model using general-purpose obfuscation candidates, and a potentially practical approach to THC via commodity hardware such as Intel SGX. Interestingly, even with such hardware, proving security requires sophisticated simulation techniques.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_10"}, {"primary_key": "3403070", "vector": [], "sparse_vector": [], "title": "Non-malleable Codes from Average-Case Hardness: $${\\mathsf {A}}{\\mathsf {C}}0$$ , Decision Trees, and Streaming Space-Bounded Tampering.", "authors": ["<PERSON>", "<PERSON>-<PERSON>ed", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show a general framework for constructing non-malleable codes against tampering families with average-case hardness bounds. Our framework adapts ideas from the Naor-Yung double encryption paradigm such that to protect against tampering in a class\\({\\mathcal {F}}\\), it suffices to have average-case hard distributions for the class, and underlying primitives (encryption and non-interactive, simulatable proof systems) satisfying certain properties with respect to the class. We instantiate our scheme in a variety of contexts, yielding efficient, non-malleable codes (NMC) against the following tampering classes: Computational NMC against\\({\\mathsf {A}}{\\mathsf {C}}^0\\)tampering, in the CRS model, assuming a PKE scheme with decryption in\\({\\mathsf {A}}{\\mathsf {C}}^0\\)and NIZK. Computational NMC against bounded-depth decision trees (of depth\\(n^\\epsilon \\), wherenis the number of input variables and constant\\(0<\\epsilon <1\\)), in the CRS model and under the same computational assumptions as above. Information theoretic NMC (with no CRS) against a streaming, space-bounded adversary, namely an adversary modeled as a read-once branching program with bounded width. Ours are the first constructions that achieve each of the above in an efficient way, under the standard notion of non-malleability.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_20"}, {"primary_key": "3403071", "vector": [], "sparse_vector": [], "title": "Limits on Low-Degree Pseudorandom Generators (Or: Sum-of-Squares Meets Program Obfuscation).", "authors": ["<PERSON>az <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Anmoutput pseudorandom generator\\(\\mathcal {G}:(\\{\\pm 1\\}^b)^n \\rightarrow \\{\\pm 1\\}^m\\)that takes inputnblocks ofbbits each is said to be\\(\\ell \\)-block local if every output is a function of at most\\(\\ell \\)blocks. We show that such\\(\\ell \\)-block local pseudorandom generators can have output length at most\\(\\tilde{O}(2^{\\ell b} n^{\\lceil \\ell /2 \\rceil })\\), by presenting a polynomial time algorithm that distinguishes inputs of the form\\(\\mathcal {G}(x)\\)from inputs where each coordinate is sampled from the uniform distribution onmbits. As a corollary, we refute some conjectures recently made in the context of constructing provably secure indistinguishability obfuscation (iO). This includes refuting the assumptions underlying <PERSON> and <PERSON><PERSON>’s [47] recently proposed candidate iO from bilinear maps. Specifically, they assumed the existence of a secure pseudorandom generator\\(\\mathcal {G}:\\{ \\pm 1 \\}^{nb} \\rightarrow \\{\\pm 1\\}^{2^{cb}n}\\)as above for large enough\\(c>3\\)and\\(\\ell =2\\). (Following this work, and an independent work of <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [49], <PERSON> and <PERSON><PERSON> retracted the bilinear maps based candidate from their manuscript.) Our results actually hold for the much wider class of low-degree, non-binary valued pseudorandom generators: if every output of\\(\\mathcal {G}:\\{\\pm 1\\}^n \\rightarrow \\mathbb R^m\\)(\\(\\mathbb R\\)= reals) is a polynomial (over\\(\\mathbb R\\)) of degree at mostdwith at mostsmonomials and\\(m \\ge \\tilde{\\varOmega }(sn^{\\lceil d/2 \\rceil })\\), then there is a polynomial time algorithm for distinguishing the output\\(\\mathcal {G}(x)\\)fromzwhere each coordinate\\(z_i\\)is sampled independently from the marginal distribution on\\(\\mathcal {G}_i\\). Furthermore, our results continue to hold under arbitrarypre-processingof the seed. This implies that any such map\\(\\mathcal {G}\\), with arbitrary seed pre-processing, cannot be a pseudorandom generator in the mild sense of fooling a product distribution on the output space. This allows us to rule out various natural modifications to the notion of generators suggested in other works that still allow obtaining indistinguishability obfuscation from bilinear maps. Our algorithms are based on the Sum of Squares (SoS) paradigm, and in most cases can even be defined more simply using a canonical semidefinite program. We complement our algorithm by presenting a class of candidate generators with block-wise locality 3 and constant block size, that resists both Gaussian elimination and sum of squares (SOS) algorithms whenever\\(m = n^{1.5-\\varepsilon }\\). This class is extremely easy to describe: Let\\(\\mathbb G\\)be any simple non-abelian group with the group operation “\\(*\\)”, and interpret the blocks ofxas elements in\\(\\mathbb G\\). The description of the pseudorandom generator is a sequence ofmtriples of indices (i,j,k) chosen at random and each output of the generator is of the form\\(x_i *x_j *x_k\\).", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_21"}, {"primary_key": "3403072", "vector": [], "sparse_vector": [], "title": "Masking the GLP Lattice-Based Signature Scheme at Any Order.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, numerous physical attacks have been demonstrated against lattice-based schemes, often exploiting their unique properties such as the reliance on Gaussian distributions, rejection sampling and FFT-based polynomial multiplication. As the call for concrete implementations and deployment of postquantum cryptography becomes more pressing, protecting against those attacks is an important problem. However, few countermeasures have been proposed so far. In particular, masking has been applied to the decryption procedure of some lattice-based encryption schemes, but the much more difficult case of signatures (which are highly non-linear and typically involve randomness) has not been considered until now. In this paper, we describe the first masked implementation of a lattice-based signature scheme. Since masking Gaussian sampling and other procedures involving contrived probability distribution would be prohibitively inefficient, we focus on the GLP scheme of Gü<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (CHES 2012). We show how to provably mask it in the Ishai–<PERSON>–<PERSON> model (CRYPTO 2003) at any order in a relatively efficient manner, using extensions of the techniques of <PERSON><PERSON> et al. for converting between arithmetic and Boolean masking. Our proof relies on a mild generalization of probing security that supports the notion of public outputs. We also provide a proof-of-concept implementation to assess the efficiency of the proposed countermeasure.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_12"}, {"primary_key": "3403073", "vector": [], "sparse_vector": [], "title": "The Complexity of Multiparty PSM Protocols and Related Models.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the efficiency of computingarbitraryk-argument functions in thePrivate Simultaneous Messages(PSM) model of [10,14]. This question was recently studied by <PERSON><PERSON><PERSON> et al. [6], in the two-party case (\\(k=2\\)). We tackle this question in the general case of PSM protocols for\\(k\\ge 2\\)parties. Our motivation is two-fold: On one hand, there are various applications (old and new) of PSM protocols for constructing other cryptographic primitives, where obtaining more efficient PSM protocols imply more efficient primitives. On the other hand, improved PSM protocols are an interesting goal on its own. In particular, we pay a careful attention to the case of small number of parties (e.g.,\\(k=3,4,5\\)), which may be especially interesting in practice, and optimize our protocols for those cases. Our new upper bounds include ak-party PSM protocol, for any\\(k>2\\)and any function\\(f:[N]^k\\rightarrow \\{0,1\\}\\), of complexityO(poly\\((k)\\cdot N^{k/2})\\)(compared to the previous upper bound ofO(poly\\((k)\\cdot N^{k-1})\\)), and even better bounds for small values ofk; e.g., anO(N) PSM protocol for the case\\(k=3\\). We also handle the more involved case where different parties have inputs of different sizes, which is useful both in practice and for applications. As applications, we obtain more efficient Non-Interactive secure Multi-Party (NIMPC) protocols (a variant of PSM, where some of the parties may collude with the referee [5]), improved ad-hoc PSM protocols (another variant of PSM, where the subset of participating parties is not known in advance [4,7]), secret-sharing schemes for uniform access structures with smaller share size than previously known, and better homogeneous distribution designs [4] (a primitive with many cryptographic applications on its own).", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_10"}, {"primary_key": "3403074", "vector": [], "sparse_vector": [], "title": "k-Round Multiparty Computation from k-Round Oblivious Transfer via Garbled Interactive Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present new constructions ofround-efficient, or evenround-optimal, Multi-Party Computation (MPC) protocols from Oblivious Transfer (OT) protocols. Our constructions establish atightconnection between MPC and OT: In the setting of semi-honest security, for any\\(k \\ge 2\\),k-round semi-honest OT isnecessary and completefork-round semi-honest MPC. In the round-optimal case of\\(k = 2\\), we obtain 2-round semi-honest MPC from 2-round semi-honest OT, resolving the round complexity of semi-honest MPC assuming weak and necessary assumption. In comparison, previous 2-round constructions rely on either the heavy machinery of indistinguishability obfuscation or witness encryption, or the algebraic structure of bilinear pairing groups. More generally, for an arbitrary number of roundsk, all previous constructions ofk-round semi-honest MPC require at least OT with\\(k'\\)rounds for\\(k' \\le \\lfloor k/2 \\rfloor \\). In the setting of malicious security, we show: For any\\(k \\ge 5\\),k-round malicious OT isnecessary and completefork-round malicious MPC. In fact, OT satisfying a weaker notion ofdelayed-semi-malicioussecurity suffices. In the common reference string model, for any\\(k \\ge 2\\), we obtaink-round malicious Universal Composable (UC) protocols from anyk-round semi-malicious OT and non-interactive zero-knowledge. Previous 5-round protocols in the plain model, and 2-round protocols in the common reference string model all require algebraic assumptions such as DDH or LWE. At the core of our constructions is a new framework forgarbling interactive circuits. Roughly speaking, it allows for garbling interactive machines that participates in interactions of a special form. The garbled machine can emulate the original interactions receiving messages sent in theclear(without being encoded using secrets), and reveals only the transcript of the interactions, provided that the transcript iscomputationally uniquely defined. We show that garbled interactive circuits for the purpose of constructing MPC can be implemented using OT. Along the way, we also propose a new primitive ofwitness selectorthat strengthens witness encryption, and a new notion ofzero-knowledge functional commitments.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_17"}, {"primary_key": "3403075", "vector": [], "sparse_vector": [], "title": "Multi-Collision Resistant Hash Functions and Their Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Collision resistant hash functions are functions that shrink their input, but for which it is computationally infeasible to find a collision, namely two strings that hash to the same value (although collisions are abundant). In this work we studymulti-collision resistant hash functions(\\(\\mathsf {MCRH}\\)) a natural relaxation of collision resistant hash functions in which it is difficult to find at-way collision (i.e.,tstrings that hash to the same value) although finding\\((t-1)\\)-way collisions could be easy. We show the following: The existence of\\(\\mathsf {MCRH}\\)follows from the average case hardness of a variant of theEntropy Approximationproblem. The goal in this problem (Goldreich, Sahai and Vadhan, CRYPTO ’99) is to distinguish circuits whose output distribution has high entropy from those having low entropy. \\(\\mathsf {MCRH}\\)imply the existence ofconstant-roundstatistically hiding (and computationally binding) commitment schemes. As a corollary, using a result of <PERSON> et al. (SICOMP, 2015), we obtain a blackbox separation of\\(\\mathsf {MCRH}\\)from any one-way permutation.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_5"}, {"primary_key": "3403076", "vector": [], "sparse_vector": [], "title": "On the Gold Standard for Security of Universal Steganography.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While symmetric-key steganography is quite well understood both in the information-theoretic and in the computational setting, many fundamental questions about its public-key counterpart resist persistent attempts to solve them. The computational model for public-key steganography was proposed by <PERSON> and <PERSON> in EUROCRYPT 2004. At TCC 2005, <PERSON><PERSON> and <PERSON><PERSON><PERSON> gave the first universal public-key stegosystem – i.e. one that works on all channels – achieving security against replayable chosen-covertext attacks (ss-rcca) and asked whether security against non-replayable chosen-covertext attacks (ss-cca) is achievable. Later, <PERSON> (ICALP 2005) provided such a stegosystem for every efficiently sampleable channel, but did not achieve universality. He posed the question whether universalityandss-cca-security can be achieved simultaneously. No progress on this question has been achieved since more than a decade. In our work we solve <PERSON>’s problem in a somehow complete manner: As our main positive result we design anss-cca-secure stegosystem that works foreverymemoryless channel. On the other hand, we prove that this result is the best possible in the context of universal steganography. We provide a family of 0-memorylesschannels – where the already sent documents have only marginal influence on the current distribution – and prove that noss-cca-secure steganography for this family exists in the standard non-look-ahead model.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_2"}, {"primary_key": "3403077", "vector": [], "sparse_vector": [], "title": "Full Indifferentiable Security of the Xor of Two or More Random Permutations Using the \\chi 2 Method.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The construction\\(\\mathsf {XORP}\\)(bitwise-xor of outputs of two independentn-bit random permutations) has gained broad attention over the last two decades due to its high security. Very recently, <PERSON><PERSON> al.(CRYPTO’17), by using a method which they term theChi-squared method(\\(\\chi ^2\\)method), have shownn-bit security of\\(\\mathsf {XORP}\\)when the underlying random permutations are kept secret to the adversary. In this work, we consider the case where the underlying random permutations are publicly available to the adversary. The best known security of\\(\\mathsf {XORP}\\)in this security game (also known asindifferentiable security) is\\(\\frac{2n}{3}\\)-bit, due to <PERSON><PERSON><PERSON> al.(ACNS’15). Later, <PERSON> (IEEE-IT’17) proved a better\\(\\frac{(k-1)n}{k}\\)-bit security for the general construction\\(\\mathsf {XORP}[k]\\)which returns the xor ofk(\\(\\ge 2\\)) independent random permutations. However, the security was shown only for the cases wherekis an even integer. In this paper, we improve all these known bounds and prove full,i.e.,n-bit (indifferentiable) security of\\(\\mathsf {XORP}\\)as well as\\(\\mathsf {XORP}[k]\\)for anyk. Our main result isn-bit security of\\(\\mathsf {XORP}\\), and we use the\\(\\chi ^2\\)method to prove it.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_15"}, {"primary_key": "3403078", "vector": [], "sparse_vector": [], "title": "Formal Verification of Masked Hardware Implementations in the Presence of Glitches.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Masking provides a high level of resistance against side-channel analysis. However, in practice there are many possible pitfalls when masking schemes are applied, and implementation flaws are easily overlooked. Over the recent years, the formal verification of masked software implementations has made substantial progress. In contrast to software implementations, hardware implementations are inherently susceptible to glitches. Therefore, the same methods tailored for software implementations are not readily applicable. In this work, we introduce a method to formally verify the security of masked hardware implementations that takes glitches into account. Our approach does not require any intermediate modeling steps of the targeted implementation. The verification is performed directly on the circuit’s netlist in the probing model with glitches and covers also higher-order flaws. For this purpose, a sound but conservative estimation of the Fourier coefficients of each gate in the netlist is calculated, which characterize statistical dependence of the gates on the inputs and thus allow to predict possible leakages. In contrast to existing practical evaluations, like t-tests, this formal verification approach makes security statements beyond specific measurement methods, the number of evaluated leakage traces, and the evaluated devices. Furthermore, flaws detected by the verifier are automatically localized. We have implemented our method on the basis of a SAT solver and demonstrate the suitability on a range of correctly and incorrectly protected circuits of different masking schemes and for different protection orders. Our verifier is efficient enough to prove the security of a full masked first-order AES S-box, and of the Keccak S-box up to the third protection order.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_11"}, {"primary_key": "3403079", "vector": [], "sparse_vector": [], "title": "Quasi-Optimal SNARGs via Linear Multi-Prover Interactive Proofs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Succinct non-interactive arguments (SNARGs) enable verifying\\(\\mathsf {NP} \\)computations with significantly less complexity than that required for classical\\(\\mathsf {NP} \\)verification. In this work, we focus on simultaneously minimizing the proof size and the prover complexity of SNARGs. Concretely, for a security parameter\\(\\lambda \\), we measure the asymptotic cost of achieving soundness error\\(2^{-\\lambda }\\)against provers of size\\(2^\\lambda \\). We say a SNARG isquasi-optimally succinctif its proof length is\\(\\widetilde{O}(\\lambda )\\), and that it isquasi-optimal, if moreover, its prover complexity is only polylogarithmically greater than the running time of the classical\\(\\mathsf {NP} \\)prover. We show that this definition is the best we could hope for assuming that\\(\\mathsf {NP} \\)does not have succinct proofs. Our definition strictly strengthens the previous notion of quasi-optimality introduced in the work of <PERSON><PERSON> et al. (Eurocrypt 2017). This work gives the first quasi-optimal SNARG for Boolean circuit satisfiability from a concrete cryptographic assumption. Our construction takes a two-step approach. The first is an information-theoretic construction of a quasi-optimal linear multi-prover interactive proof (linear MIP) for circuit satisfiability. Then, we describe a generic cryptographic compiler that transforms our quasi-optimal linear MIP into a quasi-optimal SNARG by relying on the notion of linear-only vector encryption over rings introduced by Boneh et al. Combining these two primitives yields the first quasi-optimal SNARG based on linear-only vector encryption. Moreover, our linear MIP construction leverages a newrobustcircuit decomposition primitive that allows us to decompose a circuit satisfiability instance into several smaller circuit satisfiability instances. This primitive may be of independent interest. Finally, we consider (designated-verifier) SNARGs that provideoptimalsuccinctness for a non-negligible soundness error. Concretely, we put forward the notion of “1-bit SNARGs” that achieve soundness error\\(1\\text {/}2\\)with only one bit of proof. We first show how to build 1-bit SNARGs from indistinguishability obfuscation, and then show that 1-bit SNARGs also suffice for realizing a form of witness encryption. The latter result highlights a two-way connection between the soundness of very succinct argument systems and powerful forms of encryption.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_8"}, {"primary_key": "3403080", "vector": [], "sparse_vector": [], "title": "Revisiting AES-GCM-SIV: Multi-user Security, Faster Key Derivation, and Better Bounds.", "authors": ["<PERSON><PERSON><PERSON>", "Viet Tung Hoang", "<PERSON>"], "summary": "This paper revisits the multi-user (mu) security of symmetric encryption, from the perspective of delivering an analysis of the\\(\\mathsf {AES\\text {-}GCM\\text {-}SIV}\\)AEAD scheme. Our end result shows that its mu security is comparable to that achieved in the single-user setting. In particular, even when instantiated with short keys (e.g., 128 bits), the security of\\(\\mathsf {AES\\text {-}GCM\\text {-}SIV}\\)is not impacted by the collisions of two user keys, as long as each individual nonce is not re-used by too many users. Our bounds also improve existing analyses in the single-user setting, in particular when messages of variable lengths are encrypted. We also validate security against a general class of key-derivation methods, including one thathalvesthe complexity of the final proposal. As an intermediate step, we consider mu security in a setting where the data processed by every user is bounded, and where user keys are generated according to arbitrary, possibly correlated distributions. This viewpoint generalizes the currently adopted one in mu security, and can be used to analyze re-keying practices.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_18"}, {"primary_key": "3403081", "vector": [], "sparse_vector": [], "title": "Anonymous IBE, Leakage Resilience and Circular Security from New Assumptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Inanonymousidentity-based encryption (IBE), ciphertexts not only hide their corresponding messages, but also their target identity. We construct an anonymous IBE scheme based on the Computational Diffie-Hellman (CDH) assumption in general groups (and thus, as a special case, based on the hardness of factoring Blum integers). Our approach extends and refines the recent tree-based approach of <PERSON> et al. (CRYPTO ’17) and Döttling and Garg (CRYPTO ’17). Whereas the tools underlying their approach do not seem to provide any form of anonymity, we introduce two new building blocks which we utilize for achieving anonymity:blind garbled circuits(which we construct based on any one-way function), andblind batch encryption(which we construct based on CDH). We then further demonstrate the applicability of our newly-developed tools by showing that batch encryption implies a public-key encryption scheme that is both resilient to leakage of a\\((1-o(1))\\)-fraction of its secret key, and KDM secure (or circular secure) with respect to all linear functions of its secret key (which, in turn, is known to imply KDM security for bounded-size circuits). These yield the first high-rate leakage-resilient encryption scheme and the first KDM-secure encryption scheme based on the CDH or Factoring assumptions. Finally, relying on our techniques we also construct a batch encryption scheme based on the hardness of the Learning Parity with Noise (LPN) problem, albeit with very small noise rate\\(\\varOmega (\\log ^2(n)/n)\\). Although this batch encryption scheme is not blind, we show that it still implies standard (i.e., non-anonymous) IBE, leakage resilience and KDM security. IBE and high-rate leakage resilience were not previously known from LPN, even with extremely low noise.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_20"}, {"primary_key": "3403082", "vector": [], "sparse_vector": [], "title": "The Wonderful World of Global Random Oracles.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The random-oracle model by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (CCS’93) is an indispensable tool for the security analysis of practical cryptographic protocols. However, the traditional random-oracle model fails to guarantee security when a protocol is composed with arbitrary protocols that use thesamerandom oracle. <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (CCS’14) put forth aglobalbut non-programmable random oracle in the Generalized UC framework and showed that some basic cryptographic primitives with composable security can be efficiently realized in their model. Because their random-oracle functionality is non-programmable, there are many practical protocols that have no hope of being proved secure using it. In this paper, we study alternative definitions of a global random oracle and, perhaps surprisingly, show that these allow one to prove GUC-secure existing, very practical realizations of a number of essential cryptographic primitives including public-key encryption, non-committing encryption, commitments, Schnorr signatures, and hash-and-invert signatures. Some of our results hold generically for any suitable scheme proven secure in the traditional ROM, some hold for specific constructions only. Our results include many highly practical protocols, for example, the folklore commitment scheme\\(\\mathcal {H}(m\\Vert r)\\)(wheremis a message andris the random opening information) which is far more efficient than the construction of <PERSON><PERSON> et al.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_11"}, {"primary_key": "3403083", "vector": [], "sparse_vector": [], "title": "Fiat-Shamir and Correlation Intractability from Strong KDM-Secure Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A hash function family is called correlation intractable if for all sparse relations, it is hard to find, given a random function from the family, an input-output pair that satisfies the relation (<PERSON><PERSON> et al., STOC 1998). Correlation intractability (CI) captures a strong Random-Oracle-like property of hash functions. In particular, when security holds for all sparse relations, CI suffices for guaranteeing the soundness of the Fiat-Shamir transformation from any constant round, statistically sound interactive proof to a non-interactive argument. However, to date, the only CI hash function for all sparse relations (<PERSON><PERSON> et al., Crypto 2017) is based on general program obfuscation with exponential hardness properties. We construct a simple CI hash function for arbitrary sparse relations, from any symmetric encryption scheme that satisfies some natural structural properties, and in addition guarantees that key recovery attacks mounted by polynomial-time adversaries have only exponentially small success probability - even in the context of key-dependent messages (KDM). We then provide parameter settings where ElGamal encryption and Regev encryption plausibly satisfy the needed properties. Our techniques are based on those of <PERSON><PERSON> et al., with the main contribution being substituting a statistical argument for the use of obfuscation, therefore greatly simplifying the construction and basing security on better-understood intractability assumptions. In addition, we extend the definition of correlation intractability to handle moderately sparse relations so as to capture the properties required in proof-of-work applications (e.g. Bitcoin). We also discuss the applicability of our constructions and analyses in that regime.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_4"}, {"primary_key": "3403084", "vector": [], "sparse_vector": [], "title": "Homomorphic SIM 2 D Operations: Single Instruction Much More Data.", "authors": ["W<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In 2014, <PERSON> and <PERSON><PERSON><PERSON><PERSON>en introduced a packing technique for homomorphic encryption schemes by decomposing the plaintext space using the Chinese Remainder Theorem. This technique allows to encrypt multiple data values simultaneously into one ciphertext and execute Single Instruction Multiple Data operations homomorphically. In this paper we improve and generalize their results by introducing a flexible Laurent polynomial encoding technique and by using a more fine-grained CRT decomposition of the plaintext space. The Laurent polynomial encoding provides a convenient common framework for all conventional ways in which input data types can be represented, e.g. finite field elements, integers, rationals, floats and complex numbers. Our methods greatly increase the packing capacity of the plaintext space, as well as one’s flexibility in optimizing the system parameters with respect to efficiency and/or security.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_13"}, {"primary_key": "3403085", "vector": [], "sparse_vector": [], "title": "Efficient Designated-Verifier Non-interactive Zero-Knowledge Proofs of Knowledge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a framework for constructing efficient designated-verifier non-interactive zero-knowledge proofs (\\(\\mathsf {DVNIZK}\\)) for a wide class of algebraic languages over abelian groups, under standard assumptions. The proofs obtained via our framework are proofs of knowledge, enjoy statistical, and unbounded soundness (the soundness holds even when the prover receives arbitrary feedbacks on previous proofs). Previously, no efficient\\(\\mathsf {DVNIZK}\\)system satisfyinganyof those three properties was known. Our framework allows proving arbitrary relations between cryptographic primitives such as Pedersen commitments, ElGamal encryptions, or Paillier encryptions, in an efficient way. For the latter, we further exhibit the first non-interactive zero-knowledge proof system in the standard model that is more efficient than proofs obtained via the Fiat-<PERSON>hamir transform, with still-meaningful security guarantees and under standard assumptions. Our framework has numerous applications, in particular for the design of efficient privacy-preserving non-interactive authentication.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_7"}, {"primary_key": "3403086", "vector": [], "sparse_vector": [], "title": "On the Complexity of Simulating Auxiliary Input.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We construct a simulator for the simulating auxiliary input problem with complexity better than all previous results and prove the optimality up to logarithmic factors by establishing a black-box lower bound. Specifically, let\\(\\ell \\)be the length of the auxiliary input and\\(\\epsilon \\)be the indistinguishability parameter. Our simulator is\\(\\tilde{O}(2^{\\ell }\\epsilon ^{-2})\\)more complicated than the distinguisher family. For the lower bound, we show the relative complexity to the distinguisher of a simulator is at least\\(\\varOmega (2^{\\ell }\\epsilon ^{-2})\\)assuming the simulator is restricted to use the distinguishers in a black-box way and satisfy a mild restriction.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_12"}, {"primary_key": "3403087", "vector": [], "sparse_vector": [], "title": "Homomorphic Lower Digits Removal and Improved FHE Bootstrapping.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Bootstrapping is a crucial operation in <PERSON><PERSON>’s breakthrough work on fully homomorphic encryption (FHE), where a homomorphic encryption scheme evaluates its own decryption algorithm. There has been a couple of implementations of bootstrapping, among which HElib arguably marks the state-of-the-art in terms of throughput, ciphertext/message size ratio and support for large plaintext moduli. In this work, we applied a family of “lowest digit removal” polynomials to design an improved homomorphic digit extraction algorithm which is a crucial part in bootstrapping for both FV and BGV schemes. When the secret key has 1-norm\\(h=||s||_1\\)and the plaintext modulus is\\(t = p^r\\), we achieved bootstrapping depth\\(\\log h + \\log ( \\log _p(ht))\\)in FV scheme. In case of the BGV scheme, we brought down the depth from\\(\\log h + 2 \\log t\\)to\\(\\log h + \\log t\\). We implemented bootstrapping for FV in the SEAL library. We also introduced another “slim mode”, which restrict the plaintexts to batched vectors in\\(\\mathbb {Z}_{p^r}\\). The slim mode has similar throughput as the full mode, while each individual run is much faster and uses much smaller memory. For example, bootstrapping takes 6.75 s for vectors overGF(127) with 64 slots and 1381 s for vectors over\\(GF(257^{128})\\)with 128 slots. We also implemented our improved digit extraction procedure for the BGV scheme in HElib.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_12"}, {"primary_key": "3403088", "vector": [], "sparse_vector": [], "title": "Bootstrapping for Approximate Homomorphic Encryption.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper extends the leveled homomorphic encryption scheme for an approximate arithmetic of <PERSON><PERSON> et al. (ASIACRYPT 2017) to a fully homomorphic encryption, i.e., we propose a new technique to refresh low-level ciphertexts based on <PERSON><PERSON>’s bootstrapping procedure. The modular reduction operation is the main bottleneck in the homomorphic evaluation of the decryption circuit. We exploit a scaled sine function as an approximation of the modular reduction operation and present an efficient evaluation strategy. Our method requires only one homomorphic multiplication for each of iterations and so the total computation cost grows linearly with the depth of the decryption circuit. We also show how to recrypt packed ciphertexts on the RLWE construction with an open-source implementation. For example, it takes 139.8 s to refresh a ciphertext that encrypts 128 numbers with 12 bits of precision, yielding an amortized rate of 1.1 seconds per slot.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_14"}, {"primary_key": "3403089", "vector": [], "sparse_vector": [], "title": "Boomerang Connectivity Table: A New Cryptanalysis Tool.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A boomerang attack is a cryptanalysis framework that regards a block cipherEas the composition of two sub-ciphers\\(E_1\\circ E_0\\)and builds a particular characteristic forEwith probability\\(p^2q^2\\)by combining differential characteristics for\\(E_0\\)and\\(E_1\\)with probabilitypandq, respectively. Crucially the validity of this figure is under the assumption that the characteristics for\\(E_0\\)and\\(E_1\\)can be chosen independently. Indeed, <PERSON> has shown that independently chosen characteristics may turn out to be incompatible. On the other hand, several researchers observed that the probability can be improved toporqaround the boundary between\\(E_0\\)and\\(E_1\\)by considering a positive dependency of the two characteristics, e.g. the ladder switch and S-box switch by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. This phenomenon was later formalised by <PERSON><PERSON><PERSON><PERSON> et al. as a sandwich attack that regardsEas\\(E_1\\circ E_m \\circ E_0\\), where\\(E_m\\)satisfies some differential propagation among four texts with probabilityr, and the entire probability is\\(p^2q^2r\\). In this paper, we revisit the issue of dependency of two characteristics in\\(E_m\\), and propose a new tool calledBoomerang Connectivity Table (BCT), which evaluatesrin a systematic and easy-to-understand way when\\(E_m\\)is composed of a single S-box layer. With the BCT, previous observations on the S-box including the incompatibility, the ladder switch and the S-box switch are represented in a unified manner. Moreover, the BCT can detect a new switching effect, which shows that the probability around the boundary may be even higher thanporq. To illustrate the power of the BCT-based analysis, we improve boomerang attacks againstDeoxys-BC, and disclose the mechanism behind an unsolved probability amplification for generating a quartet inSKINNY. Lastly, we discuss the issue of searching for S-boxes having good BCT and extending the analysis to modular addition.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_22"}, {"primary_key": "3403090", "vector": [], "sparse_vector": [], "title": "Simple Proofs of Sequential Work.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "At ITCS 2013, <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON> [MMV13] introduce and constructpublicly verifiable proofs of sequential work, which is a protocol for proving that one spent sequential computational work related to some statement. The original motivation for such proofs included non-interactive time-stamping and universally verifiable CPU benchmarks. A more recent application, and our main motivation, are blockchain designs, where proofs of sequential work can be used – in combination with proofs of space – as a more ecological and economical substitute for proofs of work which are currently used to secure Bitcoin and other cryptocurrencies. The construction proposed by [MMV13] is based on a hash function and can be proven secure in the random oracle model, or assuminginherently sequentialhash-functions, which is a new standard model assumption introduced in their work. In a proof of sequential work, a prover gets a “statement”\\(\\chi \\), a time parameterNand access to a hash-function\\(\\mathsf{H}\\), which for the security proof is modelled as a random oracle. Correctness requires that an honest prover can make a verifier accept making onlyNqueries to\\(\\mathsf{H}\\), while soundness requires that any prover who makes the verifier accept must have made (almost)Nsequentialqueries to\\(\\mathsf{H}\\). Thus a solution constitutes a proof thatNtime passed since\\(\\chi \\)was received. Solutions must be publicly verifiable in time at most polylogarithmic inN. The construction of [MMV13] is based on “depth-robust” graphs, and as a consequence has rather poor concrete parameters. But the major drawback is that the prover needs not justNtime, but alsoNspace to compute a proof. In this work we propose a proof of sequential work which is much simpler, more efficient and achieves much better concrete bounds. Most importantly, the space required can be as small as\\(\\log (N)\\)(but we get better soundness using slightly more memory than that). An open problem stated by [MMV13] that our construction does not solve either is achieving a “unique” proof, where even a cheating prover can only generate a single accepting proof. This property would be extremely useful for applications to blockchains.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_15"}, {"primary_key": "3403091", "vector": [], "sparse_vector": [], "title": "Random Oracles and Non-uniformity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We revisit security proofs for various cryptographic primitives in theauxiliary-input random-oracle model(AI-ROM), in which an attacker\\(\\mathcal A\\)can compute arbitrarySbits of leakage about the random oracle\\(\\mathcal O\\)before attacking the system and then use additionalToracle queries to\\(\\mathcal O\\)during the attack. This model has natural applications in settings where traditional random-oracle proofs are not useful: (a) security against non-uniform attackers; (b) security against preprocessing. We obtain a number of new results about the AI-ROM: Unruh (CRYPTO’07) introduced thepre-sampling technique, which generically reduces security proofs in the AI-ROM to a much simplerP-bit-fixing random-oracle model(BF-ROM), where the attacker can arbitrarily fix the values of\\(\\mathcal O\\)on somePcoordinates, but then the remaining coordinates are chosen at random. <PERSON>ru<PERSON>’s security loss for this transformation is\\(\\sqrt{ST/P}\\). We improve this loss to theoptimalvalueO(ST/P), obtaining nearly tight bounds for a variety of indistinguishability applications in the AI-ROM. While the basic pre-sampling technique cannot give tight bounds for unpredictability applications, we introduce a novel “multiplicative version” of pre-sampling, which allows to dramatically reduce the size ofPof the pre-sampled set to\\(P=O(ST)\\)and yields nearly tight security bounds for a variety of unpredictability applications in the AI-ROM. Qualitatively, it validates Unruh’s “polynomial pre-sampling conjecture”—disproved in general by Dodiset al.(EUROCRYPT’17)—for the special case of unpredictability applications. Using our techniques, we reprove nearly all AI-ROM bounds obtained by Dodiset al.(using a much more laborious compression technique), but we also apply it to many settings where the compression technique is either inapplicable (e.g., computational reductions) or appears intractable (e.g., Merkle-Damgård hashing). We show that for anysaltedMerkle-Damgård hash function withm-bit output there exists a collision-finding circuit of size\\(\\varTheta (2^{m/3})\\)(taking salt as the input), which is significantly below the\\(2^{m/2}\\)birthday security conjectured against uniform attackers. We build two compilers to generically extend the security of applications proven in the traditional ROM to the AI-ROM. One compiler simply prepends a public salt to the random oracle, showing thatsalting generically provably defeats preprocessing. Overall, our results make it much easier to get concrete security bounds in the AI-ROM. These bounds in turn give concrete conjectures about the security of these applications (in the standard model) againstnon-uniformattackers.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_9"}, {"primary_key": "3403092", "vector": [], "sparse_vector": [], "title": "The Discrete-Logarithm Problem with Preprocessing.", "authors": ["<PERSON>-<PERSON>", "<PERSON>"], "summary": "This paper studies discrete-log algorithms that usepreprocessing. In our model, an adversary may use a very large amount of precomputation to produce an “advice” string about a specific group (e.g., NIST P-256). In a subsequent online phase, the adversary’s task is to use the preprocessed advice to quickly compute discrete logarithms in the group. Motivated by surprising recent preprocessing attacks on the discrete-log problem, we study the power and limits of such algorithms. In particular, we focus ongenericalgorithms—these are algorithms that operate in every cyclic group. We show that any generic discrete-log algorithm with preprocessing that uses anS-bit advice string, runs in online timeT, and succeeds with probability\\(\\epsilon \\), in a group of prime orderN, must satisfy\\(ST^2 = {\\widetilde{\\varOmega }}(\\epsilon N)\\). Our lower bound, which is tight up to logarithmic factors, uses a synthesis of incompressibility techniques and classic methods for generic-group lower bounds. We apply our techniques to prove related lower bounds for the CDH, DDH, and multiple-discrete-log problems. Finally, we demonstrate two new generic preprocessing attacks: one for the multiple-discrete-log problem and one for certain decisional-type problems in groups. This latter result demonstrates that, for generic algorithms with preprocessing, distinguishing tuples of the form\\((g,{g^x}, {g^{(x^2)}})\\)from random is much easier than the discrete-log problem.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_14"}, {"primary_key": "3403093", "vector": [], "sparse_vector": [], "title": "Ouroboros Praos: An Adaptively-Secure, Semi-synchronous Proof-of-Stake Blockchain.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present “Ouroboros Praos”, a proof-of-stake blockchain protocol that, for the first time, provides security againstfully-adaptive corruptionin thesemi-synchronous setting: Specifically, the adversary can corrupt any participant of a dynamically evolving population of stakeholders at any moment as long the stakeholder distribution maintains an honest majority of stake; furthermore, the protocol tolerates an adversarially-controlled message delivery delay unknown to protocol participants. To achieve these guarantees we formalize and realize in the universal composition setting a suitable form of forward secure digital signatures and a new type of verifiable random function that maintains unpredictability under malicious key generation. Our security proof develops a general combinatorial framework for the analysis of semi-synchronous blockchains that may be of independent interest. We prove our protocol secure under standard cryptographic assumptions in the random oracle model.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_3"}, {"primary_key": "3403094", "vector": [], "sparse_vector": [], "title": "Untagging Tor: A Formal Treatment of Onion Encryption.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tor is a primary tool for maintaining anonymity online. It provides a low-latency, circuit-based, bidirectional secure channel between two parties through a network of onion routers, with the aim of obscuring exactly who is talking to whom, even to adversaries controlling part of the network. Tor relies heavily on cryptographic techniques, yet its onion encryption scheme is susceptible totagging attacks(<PERSON> and Ling 2009), which allow an active adversary controlling the first and last node of a circuit to deanonymize with near-certainty. This contrasts with less active traffic correlation attacks, where the same adversary can at best deanonymize with high probability. The Tor project has been actively looking to defend against tagging attacks and its most concrete alternative is proposal 261, which specifies a new onion encryption scheme based on a variable-input-length tweakable cipher. We provide a formal treatment of low-latency, circuit-based onion encryption, relaxed to the unidirectional setting, by expanding existing secure channel notions to the new setting and introducingcircuit hidingto capture the anonymity aspect of Tor. We demonstrate that circuit hiding prevents tagging attacks and show proposal 261’srelayprotocol is circuit hiding and thus resistant against tagging attacks.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_9"}, {"primary_key": "3403095", "vector": [], "sparse_vector": [], "title": "Bloom Filter Encryption and Applications to Efficient Forward-Secret 0-RTT Key Exchange.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Forward secrecy is considered an essential design goal of modern key establishment (KE) protocols, such as TLS 1.3, for example. Furthermore, efficiency considerations such as zero round-trip time (0-RTT), where a client is able to send cryptographically protected payload data along with the very first KE message, are motivated by the practical demand for secure low-latency communication. For a long time, it was unclear whether protocols that simultaneously achieve 0-RTT and full forward secrecy exist. Only recently, the first forward-secret 0-RTT protocol was described by <PERSON><PERSON><PERSON><PERSON> et al. (Eurocrypt2017). It is based on Puncturable Encryption. Forward secrecy is achieved by “puncturing” the secret key after each decryption operation, such that a given ciphertext can only be decrypted once (cf. also <PERSON> and <PERSON>, S&P 2015). Unfortunately, their scheme is completely impractical, since one puncturing operation takes between 30 s and several minutes for reasonable security and deployment parameters, such that this solution is only a first feasibility result, but not efficient enough to be deployed in practice. In this paper, we introduce a new primitive that we term Bloom Filter Encryption (BFE), which is derived from the probabilistic Bloom filter data structure. We describe different constructions of BFE schemes, and show how these yield new puncturable encryption mechanisms with extremely efficient puncturing. Most importantly, a puncturing operation only involves a small number of very efficient computations, plus the deletion of certain parts of the secret key, which outperforms previous constructions by orders of magnitude. This gives rise to the first forward-secret 0-RTT protocols that are efficient enough to be deployed in practice. We believe that BFE will find applications beyond forward-secret 0-RTT protocols.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_14"}, {"primary_key": "3403096", "vector": [], "sparse_vector": [], "title": "An Improved Affine Equivalence Algorithm for Random Permutations.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper we study the affine equivalence problem, where given two functions\\(\\varvec{F},\\varvec{G}: \\{0,1\\}^n \\rightarrow \\{0,1\\}^n\\), the goal is to determine whether there exist invertible affine transformations\\(A_1,A_2\\)over\\(GF(2)^n\\)such that\\(\\varvec{G} = A_2 \\circ \\varvec{F} \\circ A_1\\). Algorithms for this problem have several well-known applications in the design and analysis of Sboxes, cryptanalysis of white-box ciphers and breaking a generalized Even-Mansour scheme. We describe a new algorithm for the affine equivalence problem and focus on the variant where\\(\\varvec{F},\\varvec{G}\\)are permutations overn-bit words, as it has the widest applicability. The complexity of our algorithm is about\\(n^3 2^n\\)bit operations with very high probability whenever\\(\\varvec{F}\\)(or\\(\\varvec{G})\\)is a random permutation. This improves upon the best known algorithms for this problem (published by <PERSON><PERSON><PERSON><PERSON> et al. at EUROCRYPT 2003), where the first algorithm has time complexity of\\(n^3 2^{2n}\\)and the second has time complexity of about\\(n^3 2^{3n/2}\\)and roughly the same memory complexity. Our algorithm is based on a new structure (called arank table) which is used to analyze particular algebraic properties of a function that remain invariant under invertible affine transformations. Besides its standard application in our new algorithm, the rank table is of independent interest and we discuss several of its additional potential applications.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_16"}, {"primary_key": "3403097", "vector": [], "sparse_vector": [], "title": "Shortest Vector from <PERSON><PERSON><PERSON>: A Few Dimensions for Free.", "authors": ["Léo Du<PERSON>"], "summary": "Asymptotically, the best known algorithms for solving the Shortest Vector Problem (SVP) in a lattice of dimensionnare sieve algorithms, which have heuristic complexity estimates ranging from\\((4/3)^{n+o(n)}\\)down to\\((3/2)^{n/2 +o(n)}\\)when Locality Sensitive Hashing techniques are used. Sieve algorithms are however outperformed by pruned enumeration algorithms in practice by several orders of magnitude, despite the larger super-exponential asymptotical complexity\\(2^{\\varTheta (n \\log n)}\\)of the latter. In this work, we show a concrete improvement of sieve-type algorithms. Precisely, we show that a few calls to the sieve algorithm in lattices of dimension less than\\(n-d\\)solves SVP in dimensionn, where\\(d = \\varTheta (n/\\log n)\\). Although our improvement is only sub-exponential, its practical effect in relevant dimensions is quite significant. We implemented it over a simple sieve algorithm with\\((4/3)^{n+o(n)}\\)complexity, and it outperforms the best sieve algorithms from the literature by a factor of 10 in dimensions 70–80. It performs less than an order of magnitude slower than pruned enumeration in the same range. By design, this improvement can also be applied to most other variants of sieve algorithms, including LSH sieve algorithms and tuple-sieve algorithms. In this light, we may expect sieve-techniques to outperform pruned enumeration in practice in the near future.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_5"}, {"primary_key": "3403098", "vector": [], "sparse_vector": [], "title": "Fuzzy Password-Authenticated Key Exchange.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Consider key agreement by two parties who start out knowing a common secret (which we refer to as “pass-string”, a generalization of “password”), but face two complications: (1) the pass-string may come from a low-entropy distribution, and (2) the two parties’ copies of the pass-string may have some noise, and thus not match exactly. We provide the first efficient and general solutions to this problem that enable, for example, key agreement based on commonly used biometrics such as iris scans. The problem of key agreement with each of these complications individually has been well studied in literature. Key agreement from low-entropy shared pass-strings is achieved bypassword-authenticated key exchange(PAKE), and key agreement from noisy but high-entropy shared pass-strings is achieved by information-reconciliation protocols as long as the two secrets are “close enough.” However, the problem of key agreement from noisy low-entropy pass-strings has never been studied. We introduce (universally composable)fuzzy password-authenticated key exchange(fPAKE), which solves exactly this problem.fPAKEdoes not have any entropy requirements for the pass-strings, and enables secure key agreement as long as the two pass-strings are “close” for some notion of closeness. We also give two constructions. The first construction achieves ourfPAKEdefinition for any (efficiently computable) notion of closeness, including those that could not be handled before even in the high-entropy setting. It uses <PERSON>’s garbled circuits in a way that is only two times more costly than their use against semi-honest adversaries, but that guarantees security against malicious adversaries. The second construction is more efficient, but achieves ourfPAKEdefinition only for pass-strings with low Hamming distance. It builds on very simple primitives: robust secret sharing andPAKE.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_13"}, {"primary_key": "3403099", "vector": [], "sparse_vector": [], "title": "Supersingular Isogeny Graphs and Endomorphism Rings: Reductions and Solutions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we study several related computational problems for supersingular elliptic curves, their isogeny graphs, and their endomorphism rings. We prove reductions between the problem of path finding in the\\(\\ell \\)-isogeny graph, computing maximal orders isomorphic to the endomorphism ring of a supersingular elliptic curve, and computing the endomorphism ring itself. We also give constructive versions of <PERSON><PERSON>’s correspondence, which associates to a maximal order in a certain quaternion algebra an isomorphism class of supersingular elliptic curves. The reductions are based on heuristics regarding the distribution of norms of elements in quaternion algebras. We show that conjugacy classes of maximal orders have a representative of polynomial size, and we define a way to represent endomorphism ring generators in a way that allows for efficient evaluation at points on the curve. We relate these problems to the security of the Charles-Goren-<PERSON> hash function. We provide a collision attack for special but natural parameters of the hash function and prove that for general parameters its preimage and collision resistance are also equivalent to the endomorphism ring computation problem.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_11"}, {"primary_key": "3403100", "vector": [], "sparse_vector": [], "title": "Improving the Linear Programming Technique in the Search for Lower Bounds in Secret Sharing.", "authors": ["Oriol <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a new improvement in the linear programming technique to derive lower bounds on the information ratio of secret sharing schemes. We obtain non-Shannon-type bounds without using information inequalities explicitly. Our new technique makes it possible to determine the optimal information ratio of linear secret sharing schemes for all access structures on 5 participants and all graph-based access structures on 6 participants. In addition, new lower bounds are presented also for some small matroid ports and, in particular, the optimal information ratios of the linear secret sharing schemes for the ports of the Vamos matroid are determined.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_22"}, {"primary_key": "3403101", "vector": [], "sparse_vector": [], "title": "On the Existence of Three Round Zero-Knowledge Proofs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the round complexity of zero-knowledge (ZK)proofsystems. While five round ZK proofs for\\({\\mathsf {NP}}\\)are known from standard assumptions [<PERSON><PERSON><PERSON><PERSON>, J. Cryptology’96], <PERSON> [TCC’08] proved that four rounds are insufficient for this task w.r.t. black-box simulation. In this work, we study the feasibility of ZK proofs usingnon-black-boxsimulation. Our main result is thatthree roundprivate-coin ZK proofs for\\({\\mathsf {NP}}\\)do not exist (even w.r.t. non-black-box simulation), under certain assumptions on program obfuscation. Our approach builds upon the recent work of <PERSON><PERSON> et al. [Crypto’17] who ruled out constant roundpublic-coinZK proofs under the same assumptions as ours.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_1"}, {"primary_key": "3403102", "vector": [], "sparse_vector": [], "title": "A New Approach to Black-Box Concurrent Secure Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the task of constructing concurrently composable protocols for general secure computation by making onlyblack-boxuse of underlying cryptographic primitives. Existing approaches for this task first construct a black-box version of CCA-secure commitments which provide a strong form of concurrent security to the committed value(s). This strong form of security is then crucially used to construct higher level protocols such as concurrently secure OT/coin-tossing (and eventually all functionalities). This work explores a fresh approach. We first aim to construct a concurrently-secure OT protocol whose concurrent security is proven directly using concurrent simulation techniques; in particular, it does not rely on the usual “non-polynomial oracles” of CCA-secure commitments. The notion of concurrent security we target issuper-polynomial simulation(SPS). We show that such an OT protocol can be constructed frompolynomialhardness assumptions in ablack-boxmanner, and within aconstantnumber of rounds. In fact, we only require the existence of (constant round) semi-honest OT and standard collision-resistant hash functions. Next, we show that such an OT protocol is sufficient to obtain SPS-secure (concurrent) multiparty computation (MPC) for general functionalities. This transformation does not require any additional assumptions; it also maintains the black-box nature as well as the constant round feature of the original OT protocol. Prior to our work, the only known black-box construction of constant-round concurrently composable MPC required stronger assumptions; namely, verifiable perfectly binding homomorphic commitment schemes and PKE with oblivious public-key generation.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_19"}, {"primary_key": "3403103", "vector": [], "sparse_vector": [], "title": "Two-Round Multiparty Secure Computation from Minimal Assumptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We provide new two-round multiparty secure computation (MPC) protocols assuming the minimal assumption that two-round oblivious transfer (OT) exists. If the assumed two-round OT protocol is secure against semi-honest adversaries (in the plain model) then so is our two-round MPC protocol. Similarly, if the assumed two-round OT protocol is secure against malicious adversaries (in the common random/reference string model) then so is our two-round MPC protocol. Previously, two-round MPC protocols were only known under relatively stronger computational assumptions. Finally, we provide several extensions.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_16"}, {"primary_key": "3403104", "vector": [], "sparse_vector": [], "title": "Adaptively Secure Garbling with Near Optimal Online Complexity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We construct an adaptively secure garbling scheme with an online communication complexity of\\(n+m+\\mathsf {poly}(\\log |C|, \\lambda )\\)where\\(C: \\{0,1\\}^n \\rightarrow \\{0,1\\}^{m}\\)is the circuit being garbled, and\\(\\lambda \\)is the security parameter. The security of our scheme can be based on (polynomial hardness of) the Computational Diffie-Hellman (CDH) assumption, or the Factoring assumption or the Learning with Errors assumption. This is nearly the best achievable in the standard model (i.e., without random oracles) as the online communication complexity must be larger than bothnandm. The online computational complexity of our scheme is\\(O(n+m)+\\mathsf {poly}(\\log |C|, \\lambda )\\). Previously known standard model adaptively secure garbling schemes had asymptotically worse online cost or relied on exponentially hard computational assumptions.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_18"}, {"primary_key": "3403105", "vector": [], "sparse_vector": [], "title": "More Efficient (Almost) Tightly Secure Structure-Preserving Signatures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We provide a structure-preserving signature (SPS) scheme with an (almost) tight security reduction to a standard assumption. Compared to the state-of-the-art tightly secure SPS scheme of <PERSON> et al. (CRYPTO 2017), our scheme has smaller signatures and public keys (of about\\(56\\%\\), resp.\\(40\\%\\)of the size of signatures and public keys in <PERSON> et al.’s scheme), and a lower security loss (of\\(\\mathbf{O}(\\log Q)\\)instead of\\(\\mathbf{O}(\\lambda )\\), where\\(\\lambda \\)is the security parameter, and\\(Q=\\mathsf {poly}(\\lambda )\\)is the number of adversarial signature queries). While our scheme is still less compact than structure-preserving signature schemeswithouttight security reduction, it significantly lowers the price to pay for a tight security reduction. In fact, when accounting for a non-tight security reduction with larger key (i.e., group) sizes, the computational efficiency of our scheme becomes at least comparable to that of non-tightly secure SPS schemes. Technically, we combine and refine recent existing works on tightly secure encryption and SPS schemes. Our technical novelties include a modular treatment (that develops an SPS scheme out of a basic message authentication code), and a refined hybrid argument that enables a lower security loss of\\(\\mathbf{O}(\\log Q)\\)(instead of\\(\\mathbf{O}(\\lambda )\\)).", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_8"}, {"primary_key": "3403106", "vector": [], "sparse_vector": [], "title": "Faster <PERSON><PERSON><PERSON> for Trapdoor Lattices with Arbit<PERSON> Mo<PERSON>lus.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We present improved algorithms for gaussian preimage sampling using the lattice trapdoors of (<PERSON><PERSON><PERSON><PERSON> and <PERSON>, CRYPTO 2012). The MP12 work only offered a highly optimized algorithm for the on-line stage of the computation in the special case when the lattice modulusqis a power of two. For arbitrary modulusq, the MP12 preimage sampling procedure resorted to general lattice algorithms with complexity cubic in the bitsize of the modulus (or quadratic, but with substantial preprocessing and storage overheads). Our new preimage sampling algorithm (for any modulusq) achieves linear complexity with very modest storage requirements, and experimentally outperforms the generic method of MP12 already for small values ofq. As an additional contribution, we give a new, quasi-linear time algorithm for the off-line perturbation sampling phase of MP12 in the ring setting. Our algorithm is based on a variant of the Fast Fourier Orthogonalization (FFO) algorithm of (<PERSON><PERSON> and <PERSON>st, ISSAC 2016), but avoids the need to precompute and store the FFO matrix by a careful rearrangement of the operations. All our algorithms are fairly simple, with small hidden constants, and offer a practical alternative to use the MP12 trapdoor lattices in a broad range of cryptographic applications.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_7"}, {"primary_key": "3403107", "vector": [], "sparse_vector": [], "title": "Masking Proofs Are Tight and How to Exploit it in Security Evaluations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Evaluating the security level of a leaking implementation against side-channel attacks is a challenging task. This is especially true when countermeasures such as masking are implemented since in this case: (i) the amount of measurements to perform a key recovery may become prohibitive for certification laboratories, and (ii) applying optimal (multivariate) attacks may be computationally intensive and technically challenging. In this paper, we show that by taking advantage of the tightness of masking security proofs, we can significantly simplify this evaluation task in a very general manner. More precisely, we show that the evaluation of a masked implementation can essentially be reduced to the one of an unprotected implementation. In addition, we show that despite optimal attacks against masking schemes are computationally intensive for large number of shares, heuristic (soft analytical side-channel) attacks can approach optimality efficiently. As part of this second contribution, we also improve over the recent multivariate (aka horizontal) side-channel attacks proposed at CHES 2016 by <PERSON><PERSON> et al.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_13"}, {"primary_key": "3403108", "vector": [], "sparse_vector": [], "title": "Synchronized Aggregate Signatures from the RSA Assumption.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this work we construct efficient aggregate signatures from the RSA assumption in the synchronized setting. In this setting, the signing algorithm takes as input a (time) periodtas well the secret key and message. A signer should sign at most once for eacht. A set of signatures can be aggregated so long as they were all created for the same periodt. Synchronized aggregate signatures are useful in systems where there is a natural reporting period such as log and sensor data, or for signatures embedded in a blockchain protocol. We design a synchronized aggregate signature scheme that works for a bounded number of periodsTthat is given as a parameter to a global system setup. The big technical question is whether we can create solutions that will perform well with the largeTvalues that we might use in practice. For instance, if one wanted signing keys to last up to ten years and be able to issue signatures every second, then we would need to support a period bound of upwards of\\(2^{28}\\). We build our solution in stages where we start with an initial solution that establishes feasibility, but has an impractically large signing time where the number of exponentiations and prime searches grows linearly withT. We prove this scheme secure in the standard model under the RSA assumption with respect to honestly-generated keys. We then provide a tradeoff method where one can tradeoff the time to create signatures with the space required to store private keys. One point in the tradeoff is where each scales with\\(\\sqrt{T}\\). Finally, we reach our main innovation which is a scheme where both the signing time and storage scale with\\(\\lg {T}\\)which allows for us to keep both computation and storage costs modest even for large values ofT. Conveniently, our final scheme uses the same verification algorithm, and has the same distribution of public keys and signatures as the first scheme. Thus we are able to recycle the existing security proof for the new scheme. We also extend our results to the identity-based setting in the random oracle model, which can further reduce the overall cryptographic overhead. We conclude with a detailed evaluation of the signing time and storage requirements for various settings of the system parameters.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_7"}, {"primary_key": "3403109", "vector": [], "sparse_vector": [], "title": "An Efficiency-Preserving Transformation from Honest-Verifier Statistical Zero-Knowledge to Statistical Zero-Knowledge.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present an unconditional transformation from any honest-verifier statistical zero-knowledge (HVSZK) protocol to standard SZK that preserves round complexity and efficiency of both the verifier and the prover. This improves over currently known transformations, which either rely on some computational assumptions or introduce significant computational overhead. Our main conceptual contribution is the introduction of instance-dependent SZK proofs for NP, which serve as a building block in our transformation. Instance-dependent SZK for NP can be constructed unconditionally based on instance-dependent commitment schemes of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (TCC’08). As an additional contribution, we give a simple constant-round SZK protocol for Statistical-Difference resembling the textbook HVSZK proof of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (J.ACM’03). This yields a conceptually simple constant-round protocol for all of SZK.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_3"}, {"primary_key": "3403110", "vector": [], "sparse_vector": [], "title": "OPAQUE: An Asymmetric PAKE Protocol Secure Against Pre-computation Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Password-Authenticated Key Exchange (PAKE) protocols allow two parties that only share a password to establish a shared key in a way that is immune to offline attacks.AsymmetricPAKE (aPAKE) strengthens this notion for the more common client-server setting where the server stores a mapping of the password and security is required even upon server compromise, that is, the only allowed attack in this case is an (inevitable) offline exhaustive dictionary attack against individual user passwords. Unfortunately, most suggested aPAKE protocols (that dispense with the use of servers’ public keys) allow forpre-computation attacksthat lead to theinstantaneous compromiseof user passwords upon server compromise, thus forgoing much of the intended aPAKE security. Indeed, these protocols use – in essential ways – deterministic password mappings or use random “salt” transmittedin the clearfrom servers to users, and thus are vulnerable to pre-computation attacks. We initiate the study ofStrong aPAKEprotocols that are secure as aPAKE’s butare also secure against pre-computation attacks.We formalize this notion in the Universally Composable (UC) settings and present two modular constructions using an Oblivious PRF as a main tool. The first builds a Strong aPAKE fromanyaPAKE (which in turn can be constructed from any PAKE [18]) while the second builds a Strong aPAKE fromanyauthenticated key-exchange protocol secure against reverse impersonation (a.k.a. KCI). Using the latter transformation, we showa practical instantiation of a UC-secure Strong aPAKEin the Random Oracle model. The protocol (“OPAQUE”) consists of 2 messages (3 with mutual authentication), requires 3 and 4 exponentiations for server and client, respectively (2 to 4 of which can be fixed-base depending on optimizations), provides forward secrecy, is PKI-free, supports user-side hash iterations, and allows a user-transparent server-side threshold implementation.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_15"}, {"primary_key": "3403111", "vector": [], "sparse_vector": [], "title": "Statistical Witness Indistinguishability (and more) in Two Messages.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Two-message witness indistinguishable protocols were first constructed by <PERSON><PERSON> and <PERSON><PERSON> (FOCS 2000). They have since proven extremely useful in the design of several cryptographic primitives. However, so far no two-message arguments for NP providedstatistical privacyagainst malicious verifiers. In this paper, we construct the first: \\(\\circ \\)Two-message statistical witness indistinguishable (SWI) arguments for NP. \\(\\circ \\)Two-message statistical zero-knowledge arguments for NP with super-polynomial simulation (Statistical SPS-ZK). \\(\\circ \\)Two-message statistical distributional weak zero-knowledge (SwZK) arguments for NP, where the simulator is a probabilistic polynomial time machine with oracle access to the distinguisher, and the instance is sampled by the prover in the second round. These protocols are based on quasi-polynomial hardness of two-message oblivious transfer (OT), which in turn can be based on quasi-polynomial hardness of DDH or QR or\\(N^{th}\\)residuosity. We also show how such protocols can be used to build more secure forms of oblivious transfer. Along the way, we show that the <PERSON><PERSON> and <PERSON><PERSON> (Crypto 09) transform compressing interactiveproofsto two-message arguments can be generalized to compress certain types of interactivearguments. We introduce and construct a new technical tool, which is a variant of extractable two-message statistically hiding commitments, building on the recent work of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (FOCS 17). These techniques may be of independent interest.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_2"}, {"primary_key": "3403112", "vector": [], "sparse_vector": [], "title": "Non-malleable Randomness Encoders and Their Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Non-malleable Codes (NMCs), introduced by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (ITCS 2010), serve the purpose of preventing “related tampering” of encoded messages. The most popular tampering model considered is the 2-split-state model where a codeword consists of 2 states, each of which can be tampered independently. While NMCs in the 2-split state model provide the strongest security guarantee, despite much research in the area we only know how to build them with poor rate (\\(\\varOmega (\\frac{1}{logn})\\), wherenis the codeword length). However, in many applications of NMCs one only needs to be able to encode randomness i.e., security is not required to hold for arbitrary, adversarially chosen messages. For example, in applications of NMCs to tamper-resilient security, the messages that are encoded are typically randomly generated secret keys. To exploit this, in this work, we introduce the notion of “Non-malleable Randomness Encoders” (NMREs) as a relaxation of NMCs in the following sense: NMREs output a random message along with its corresponding non-malleable encoding. Our main result is the construction of a 2-split state, rate-\\(\\frac{1}{2}\\)NMRE. While NMREs are interesting in their own right and can be directly used in applications such as in the construction of tamper-resilient cryptographic primitives, we also show how to use them, in a black-box manner, to build a 3-split-state (standard) NMCs with rate\\(\\frac{1}{3}\\). This improves both the number of states, as well as the rate, of existing constant-rate NMCs.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_19"}, {"primary_key": "3403113", "vector": [], "sparse_vector": [], "title": "Overdrive: Making SPDZ Great Again.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "SPDZ denotes a multiparty computation scheme in the preprocessing model based on somewhat homomorphic encryption (SHE) in the form of BGV. At CCS ’16, <PERSON> et al. presented MASCOT, a replacement of the preprocessing phase using oblivious transfer instead of SHE, improving by two orders of magnitude on the SPDZ implementation by <PERSON><PERSON><PERSON><PERSON> et al. (ESORICS ’13). In this work, we show that using SHE is faster than MASCOT in many aspects: We present a protocol that uses semi-homomorphic (addition-only) encryption. For two parties, our BGV-based implementation is six times faster than MASCOT on a LAN and 20 times faster in a WAN setting. The latter is roughly the reduction in communication. We show that using the proof of knowledge in the original work by <PERSON><PERSON><PERSON><PERSON> et al. (Crypto ’12) is more efficient in practice than the one used in the implementation mentioned above by about one order of magnitude. We present an improvement to the verification of the aforementioned proof of knowledge that increases the performance with a growing number of parties, doubling it for 16 parties.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_6"}, {"primary_key": "3403114", "vector": [], "sparse_vector": [], "title": "Efficient Maliciously Secure Multiparty Computation for RAM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A crucial issue, that mostly affects the performance of actively secure computation of RAM programs, is the task of reading/writing from/to memory in a private and authenticated manner. Previous works in the active security and multiparty settings are based purely on the SPDZ (reactive) protocol, hence, memory accesses are treated just like any input to the computation. However, a garbled-circuit-based construction (such as BMR), which benefits from a lower round complexity, must resolve the issue of converting memory data bits to their corresponding wire keys and vice versa. In this work we propose three techniques to construct a secure memory access, each appropriates to a different level of abstraction of the underlying garbling functionality. We provide a comparison between the techniques by several metrics. To the best of our knowledge, we are thefirstto construct, prove and implement a concretely efficient garbled-circuit-based actively secure RAM computation with dishonest majority. Our construction is based on our third (most efficient) technique, cleverly utilizing the underlying SPDZ authenticated shares (<PERSON><PERSON> et al., Crypto 2012), yields lean circuits and a constant number of communication rounds per physical memory access. Specifically, it requires no additional circuitry on top of the ORAM’s, incurs only two rounds of broadcasts between every two memory accesses and has a multiplicative overhead of 2 on top of the ORAM’s storage size. Our protocol outperforms the state of the art in this settings when deployed over WAN. Even when simulating a very conservative RTT of 100 ms our protocol is at least one order of magnitude faster than the current state of the art protocol of <PERSON> and <PERSON> (Asiacrypt 2015).", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_4"}, {"primary_key": "3403115", "vector": [], "sparse_vector": [], "title": "A Concrete Treatment of Fiat-Shamir Signatures in the Quantum Random-Oracle Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Fiat-Shamir transform is a technique for combining a hash function and an identification scheme to produce a digital signature scheme. The resulting scheme is known to be secure in the random oracle model (ROM), which does not, however, imply security in the scenario where the adversary also has quantum access to the oracle. The goal of this current paper is to create a generic framework for constructing tight reductions in the QROM from underlying hard problems to Fiat-Shamir signatures. Our generic reduction is composed of two results whose proofs, we believe, are simple and natural. We first consider a security notion (UF-NMA) in which the adversary obtains the public key and attempts to create a valid signature without accessing a signing oracle. We give a tight reduction showing that deterministic signatures (i.e., ones in which the randomness is derived from the message and the secret key) that are UF-NMA secure are also secure under the standard chosen message attack (UF-CMA) security definition. Our second result is showing that if the identification scheme is “lossy”, as defined in (<PERSON><PERSON><PERSON> et al. Eurocrypt 2012), then the security of the UF-NMA scheme is tightly based on the hardness of distinguishing regular and lossy public keys of the identification scheme. This latter distinguishing problem is normally exactly the definition of some presumably-hard mathematical problem. The combination of these components gives our main result. As a concrete instantiation of our framework, we modify the recent lattice-based Dilithium digital signature scheme (<PERSON><PERSON> et al., TCHES 2018) so that its underlying identification scheme admits lossy public keys. The original Dilithium scheme, which is proven secure in the classical ROM based on standard lattice assumptions, has 1.5 KB public keys and 2.7 KB signatures. The new scheme, which is tightly based on the hardness of the Module-LWE problem in the QROM using our generic reductions, has 7.7 KB public keys and 5.7 KB signatures for the same security level. Furthermore, due to our proof of equivalence between the UF-NMA and UF-CMA security notions of deterministic signature schemes, we can formulate a new non-interactive assumption under which the original Dilithium signature scheme is also tightly secure in the QROM.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_18"}, {"primary_key": "3403116", "vector": [], "sparse_vector": [], "title": "Obfustopia Built on Secret-Key Functional Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that indistinguishability obfuscation (IO) for all circuits can be constructed solely from secret-key functional encryption (SKFE). In the construction, SKFE need to be able to issue a-priori unbounded number of functional keys, that is, collusion-resistant. Our strategy is to replace public-key functional encryption (PKFE) in the construction of IO proposed by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (FOCS 2015) withpuncturable SKFE. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> introduced the notion of puncturable SKFE and observed that the strategy works. However, it has not been clear whether we can construct puncturable SKFE without assuming PKFE. In particular, it has not been known whether puncturable SKFE is constructed from ordinary SKFE. In this work, we show that a relaxed variant of puncturable SKFE can be constructed from collusion-resistant SKFE. Moreover, we show that the relaxed variant of puncturable SKFE is sufficient for constructing IO. In addition, we also study the relation of collusion-resistance and succinctness for SKFE. Functional encryption is said to be weakly-succinct if the size of its encryption circuit is sub-linear in the size of functions. We show that collusion-resistant SKFE can be constructed from weakly-succinct SKFE supporting only one functional key. By combining the above two results, we show that IO for all circuits can be constructed from weakly-succinct SKFE supporting only one functional key.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_20"}, {"primary_key": "3403117", "vector": [], "sparse_vector": [], "title": "Collision Resistant Hashing for Paranoids: Dealing with Multiple Collisions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "E<PERSON>"], "summary": "A collision resistant hash (CRH) function is one that compresses its input, yet it is hard to find a collision, i.e. a\\(x_1 \\ne x_2\\)s.t.\\(h(x_1) = h(x_2)\\). Collision resistant hash functions are one of the more useful cryptographic primitives both in theory and in practice and two prominent applications are in signature schemes and succinct zero-knowledge arguments. In this work we consider a relaxation of the above requirement that we call Multi-CRH: a function where it is hard to find\\(x_1, x_2, \\ldots , x_k\\)which are all distinct, yet\\( h(x_1) = h(x_2) = \\cdots = h(x_k)\\). We show that for some of the major applications of CRH functions it is possible to replace them by the weaker notion of a Multi-CRH, albeit at the price of adding interaction: we show a constant-round statistically-hiding commitment scheme with succinct interaction (committing to\\(\\mathsf {poly}(n)\\)bits requires exchanging\\(\\tilde{O}(n)\\)bits) that can be opened locally (without revealing the full string). This in turn can be used to provide succinct arguments for any\\({\\textsf {NP}}\\)statement. We formulate four possible worlds of hashing-related assumptions (in the spirit of Impagliazzo’s worlds). They are (1)Nocrypt, where no one-way functions exist, (2)Unihash, where one-way functions exist, and hence also UOWHFs and signature schemes, but no Multi-CRH functions exist, (3)Minihash, where Multi-CRH functions exist but no CRH functions exist, and (4)Hashomania, where CRH functions exist. We show that these four worlds are distinct in a black-box model: we show a separation of CRH from Multi-CRH and a separation of Multi-CRH from one-way functions.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_6"}, {"primary_key": "3403118", "vector": [], "sparse_vector": [], "title": "Another Step Towards Realizing Random Oracles: Non-malleable Point Obfuscation.", "authors": ["<PERSON><PERSON>", "E<PERSON>"], "summary": "The random oracle paradigm allows us to analyze the security of protocols and construction in an idealized model, where all parties have access to a truly random function. This is one of the most successful and well-studied models in cryptography. However, being such a strong idealized model, it is known to be susceptible to various weaknesses when implemented naively in “real-life”, as shown by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (<PERSON><PERSON> 2004). As a counter-measure, one could try to identify and implement only one or few of the properties a random oracle possesses that are needed for a specific setting. Such a systematic study was initiated by <PERSON><PERSON> (CRYPTO 1997), who showed how to implement the property that the output of the function does not reveal anything regarding the input by constructing a point function obfucator. This property turned out to suffice in many follow-up works and applications. In this work, we tackle another natural property of random oracles and implement it in the standard model. The property we focus on isnon-malleability, where it is guaranteed that the output on an input cannot be used to generate the output on any related point. We construct a point-obfuscator that is both point-hiding (à <PERSON>)andis non-malleable. The cost of our construction is a single exponentiation on top of <PERSON><PERSON>’s construction and could be used for any application where point obfuscators are used and obtain improved security guarantees. The security of our construction relies on variants of the DDH and power-DDH assumptions. On the technical side, we introduce a new technique for proving security of a construction based on a DDH-like assumption. We call this technique “double-exponentiation” and believe it will be useful in the future.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_10"}, {"primary_key": "3403119", "vector": [], "sparse_vector": [], "title": "Updatable Encryption with Post-Compromise Security.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "An updatable encryption scheme allows to periodically rotate the encryption key and move already existing ciphertexts from the old to the new key. These ciphertext updates are done with the help of a so-called update token and can be performed by an untrusted party, as the update never decrypts the data. Updatable encryption is particularly useful in settings where encrypted data is outsourced, e.g., stored on a cloud server. The data owner can produce an update token, and the cloud server can update the ciphertexts. We provide a comprehensive treatment ofciphertext-independentschemes, where a single token is used to update all ciphertexts. We show that the existing ciphertext-independent schemes and models by <PERSON><PERSON> et al. (CRYPTO’13) and <PERSON><PERSON><PERSON> et al. (CRYPTO’17) do not guarantee thepost-compromisesecurity one would intuitively expect from key rotation. In fact, the simple scheme recently proposed by <PERSON><PERSON><PERSON> et al. allows to recover the current key upon corruption of a single old key. Surprisingly, none of the models so far reflects the timely aspect of key rotation which makes it hard to graspwhenan adversary is allowed to corrupt keys. We propose strong security models that clearly capture post-compromise and forward security under adaptive attacks. We then analyze various existing schemes and show that none of them is secure in this strong model, but we formulate the additional constraints that suffice to prove their security in a relaxed version of our model. Finally, we propose a new updatable encryption scheme that achieves our strong notions while being (at least) as efficient as the existing solutions.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_22"}, {"primary_key": "3403120", "vector": [], "sparse_vector": [], "title": "The Missing Difference Problem, and Its Applications to Counter Mode Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The counter mode (CTR) is a simple, efficient and widely used encryption mode using a block cipher. It comes with a security proof that guarantees no attacks up to the birthday bound (i.e.as long as the number of encrypted blocks\\(\\sigma \\)satisfies\\(\\sigma \\ll 2^{n/2}\\)), and a matching attack that can distinguish plaintext/ciphertext pairs from random using about\\(2^{n/2}\\)blocks of data. The main goal of this paper is to study attacks against the counter mode beyond this simple distinguisher. We focus on message recovery attacks, with realistic assumptions about the capabilities of an adversary, and evaluate the full time complexity of the attacks rather than just the query complexity. Our main result is an attack to recover a block of message with complexity\\(\\tilde{\\mathcal {O}}(2^{n/2})\\). This shows that the actual security ofCTRis similar to that ofCBC, where collision attacks are well known to reveal information about the message. To achieve this result, we study a simple algorithmic problem related to the security of theCTRmode: the missing difference problem. We give efficient algorithms for this problem in two practically relevant cases: where the missing difference is known to be in some linear subspace, and when the amount of data is higher than strictly required. As a further application, we show that the second algorithm can also be used to break some polynomial MACs such asGMACandPoly1305, with a universal forgery attack with complexity\\(\\tilde{\\mathcal {O}}(2^{2n/3})\\).", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_24"}, {"primary_key": "3403121", "vector": [], "sparse_vector": [], "title": "Towards Breaking the Exponential Barrier for General Secret Sharing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "A secret-sharing scheme for a monotone Boolean (access) function\\(F: \\{0,1\\}^n \\rightarrow \\{0,1\\}\\)is a randomized algorithm that on input a secret, outputsnshares\\(s_1,\\ldots ,s_n\\)such that for any\\((x_1,\\ldots ,x_n) \\in \\{0,1\\}^n\\), the collection of shares\\( \\{ s_i : x_i = 1 \\}\\)determine the secret if\\(F(x_1,\\ldots ,x_n)=1\\)and reveal nothing about the secret otherwise. The best secret sharing schemes for general monotone functions have shares of size\\(\\varTheta (2^n)\\). It has long been conjectured that one cannot do much better than\\(2^{\\varOmega (n)}\\)share size, and indeed, such a lower bound is known for the restricted class of linear secret-sharing schemes. In this work, werefutetwo natural strengthenings of the above conjecture: First, we present secret-sharing schemes for a family of\\(2^{2^{n/2}}\\)monotone functions over\\(\\{0,1\\}^n\\)with sub-exponential share size\\(2^{O(\\sqrt{n} \\log n)}\\). Thisunconditionallyrefutes the stronger conjecture that circuit size is, within polynomial factors, a lower bound on the share size. Second, we disprove the analogous conjecture for non-monotone functions. Namely, we present “non-monotone secret-sharing schemes” forevery access functionover\\(\\{0,1\\}^n\\)with shares of size\\(2^{O(\\sqrt{n} \\log n)}\\). Our construction draws upon a rich interplay amongst old and new problems in information-theoretic cryptography: from secret-sharing, to multi-party computation, to private information retrieval. Along the way, we also construct the firstmulti-partyconditional disclosure of secrets (CDS) protocols for general functions\\(F:\\{0,1\\}^n \\rightarrow \\{0,1\\}\\)with communication complexity\\(2^{O(\\sqrt{n} \\log n)}\\).", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_21"}, {"primary_key": "3403122", "vector": [], "sparse_vector": [], "title": "Correlation Cube Attacks: From Weak-Key Distinguisher to Key Recovery.", "authors": ["Mei<PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Dongdai Lin"], "summary": "In this paper, we describe a new variant of cube attacks calledcorrelation cube attack. The new attack recovers the secret key of a cryptosystem by exploiting conditional correlation properties between the superpoly of a cube and a specific set of low-degree polynomials that we call abasis, which satisfies that the superpoly is a zero constant when all the polynomials in the basis are zeros. We present a detailed procedure of correlation cube attack for the general case, including how to find a basis of the superpoly of a given cube. One of the most significant advantages of this new analysis technique over other variants of cube attacks is that it converts from a weak-key distinguisher to a key recovery attack. As an illustration, we apply the attack to round-reduced variants of the stream cipherTrivium. Based on the tool of numeric mapping introduced by <PERSON> at CRYPTO 2017, we develop a specific technique to efficiently find a basis of the superpoly of a given cube as well as a large set of potentially good cubes used in the attack onTriviumvariants, and further set up deterministic or probabilistic equations on the key bits according to the conditional correlation properties between the superpolys of the cubes and their bases. For a variant when the number of initialization rounds is reduced from 1152 to 805, we can recover about 7-bit key information on average with time complexity\\(2^{44}\\), using\\(2^{45}\\)keystream bits and preprocessing time\\(2^{51}\\). For a variant ofTriviumreduced to 835 rounds, we can recover about 5-bit key information on average with the same complexity. All the attacks are practical and fully verified by experiments. To the best of our knowledge, they are thus far the best known key recovery attacks for these variants ofTrivium, and this is the first time that a weak-key distinguisher onTriviumstream cipher can be converted to a key recovery attack.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_23"}, {"primary_key": "3403123", "vector": [], "sparse_vector": [], "title": "Optimal Forgeries Against Polynomial-Based MACs and GCM.", "authors": ["Atul Luykx", "<PERSON>"], "summary": "Polynomial-based authentication algorithms, such as GCM and Poly1305, have seen widespread adoption in practice. Due to their importance, a significant amount of attention has been given to understanding and improving both proofs and attacks against such schemes. At EUROCRYPT 2005, <PERSON> published the best known analysis of the schemes when instantiated with PRPs, thereby establishing the most lenient limits on the amount of data the schemes can process per key. A long line of work, initiated by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> at CRYPTO 2008, finds the best known attacks, advancing our understanding of the fragility of the schemes. Yet surprisingly, no known attacks perform as well as the predicted worst-case attacks allowed by <PERSON>’s analysis, nor has there been any advancement in proofs improving <PERSON>’s bounds, and the gap between attacks and analysis is significant. We settle the issue by finding a novel attack against polynomial-based authentication algorithms using PRPs, and combine it with new analysis, to show that <PERSON>’s bound, and our attacks, are optimal.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_17"}, {"primary_key": "3403124", "vector": [], "sparse_vector": [], "title": "Short, Invertible Elements in Partially Splitting Cyclotomic Rings and Applications to Lattice-Based Zero-Knowledge Proofs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "When constructing practical zero-knowledge proofs based on the hardness of the Ring-LWE or the Ring-SIS problems over polynomial rings\\(\\mathbb {Z}_p[X]/(X^n+1)\\), it is often necessary that the challenges come from a set\\(\\mathcal {C}\\)that satisfies three properties: the set should be large (around\\(2^{256}\\)), the elements in it should have small norms, and all the non-zero elements in the difference set\\(\\mathcal {C}-\\mathcal {C}\\)should be invertible. The first two properties are straightforward to satisfy, while the third one requires us to make efficiency compromises. We can either work over rings where the polynomial\\(X^n+1\\)only splits into two irreducible factors modulop, which makes the speed of the multiplication operation in the ring sub-optimal; or we can limit our challenge set to polynomials of smaller degree, which requires them to have (much) larger norms. In this work we show that one can use the optimal challenge sets\\(\\mathcal {C}\\)and still have the polynomial\\(X^n+1\\)split into more than two factors. This comes as a direct application of our more general result that states that all non-zero polynomials with “small” coefficients in the cyclotomic ring\\(\\mathbb {Z}_p[X]/(\\varPhi _m(X))\\)are invertible (where “small” depends on the size ofpand how many irreducible factors the\\(m^{th}\\)cyclotomic polynomial\\(\\varPhi _m(X)\\)splits into). We furthermore establish sufficient conditions forpunder which\\(\\varPhi _m(X)\\)will split in such fashion. For the purposes of implementation, if the polynomial\\(X^n+1\\)splits intokfactors, we can run FFT for\\(\\log {k}\\)levels until switching to Karatsuba multiplication. Experimentally, we show that increasing the number of levels from one to three or four results in a speedup by a factor of\\(\\approx 2\\)– 3. We point out that this improvement comes completely for free simply by choosing a moduluspthat has certain algebraic properties. In addition to the speed improvement, having the polynomial split into many factors has other applications – e.g. when one embeds information into the Chinese Remainder representation of the ring elements, the more the polynomial splits, the more information one can embed into an element.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_8"}, {"primary_key": "3403125", "vector": [], "sparse_vector": [], "title": "On the Bit Security of Cryptographic Primitives.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a formal quantitative notion of “bit security” for a general type of cryptographic games (capturing both decision and search problems), aimed at capturing the intuition that a cryptographic primitive withk-bit security is as hard to break as an ideal cryptographic function requiring a brute force attack on ak-bit key space. Our new definition matches the notion of bit security commonly used by cryptographers and cryptanalysts when studying search (e.g., key recovery) problems, where the use of the traditional definition is well established. However, it produces a quantitatively different metric in the case of decision (indistinguishability) problems, where the use of (a straightforward generalization of) the traditional definition is more problematic and leads to a number of paradoxical situations or mismatches between theoretical/provable security and practical/common sense intuition. Key to our new definition is to consider adversaries that may explicitly declare failure of the attack. We support and justify the new definition by proving a number of technical results, including tight reductions between several standard cryptographic problems, a new hybrid theorem that preserves bit security, and an application to the security analysis of indistinguishability primitives making use of (approximate) floating point numbers. This is the first result showing that (standard precision) 53-bit floating point numbers can be used to achieve 100-bit security in the context of cryptographic primitives with general indistinguishability-based security definitions. Previous results of this type applied only to search problems, or special types of decision problems.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_1"}, {"primary_key": "3403126", "vector": [], "sparse_vector": [], "title": "Thunderella: Blockchains with Optimistic Instant Confirmation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "State machine replication, or “consensus”, is a central abstraction for distributed systems where a set of nodes seek to agree on an ever-growing, linearly-ordered log. In this paper, we propose a practical new paradigm calledThunderellafor achieving state machine replication by combining a fast, asynchronous path with a (slow) synchronous “fall-back” path (which only gets executed if something goes wrong); as a consequence, we getsimplestate machine replications that essentially are as robust as the best synchronous protocols, yet “optimistically” (if a super majority of the players are honest), the protocol “instantly” confirms transactions. We provide instantiations of this paradigm in both permissionless (using proof-of-work) and permissioned settings. Most notably, this yields a new blockchain protocol (for the permissionless setting) that remains resilient assuming only that a majority of the computing power is controlled by honest players, yetoptimistically—if 3/4 of the computing power is controlled by honest players, and a special player called the “accelerator”, is honest—transactions are confirmed as fast as the actual message delay in the network. We additionally show the 3/4 optimistic bound is tight for protocols that are resilient assuming only an honest majority.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_1"}, {"primary_key": "3403127", "vector": [], "sparse_vector": [], "title": "Efficient Circuit-Based PSI via Cuckoo Hashing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "While there has been a lot of progress in designing efficient custom protocols for computing Private Set Intersection (PSI), there has been less research on using generic Multi-Party Computation (MPC) protocols for this task. However, there are many variants of the set intersection functionality that are not addressed by the existing custom PSI solutions and are easy to compute with generic MPC protocols (e.g., comparing the cardinality of the intersection with a threshold or measuring ad conversion rates). Generic PSI protocols work over circuits that compute the intersection. For sets of sizen, the best known circuit constructions conduct\\(O(n \\log n)\\)or\\(O(n \\log n / \\log \\log n)\\)comparisons (<PERSON> et al., NDSS’12 and <PERSON> et al., USENIX Security’15). In this work, we propose new circuit-based protocols for computingvariants of the intersectionwith an almost linear number of comparisons. Our constructions are based on new variants of Cuckoo hashing in two dimensions. We present an asymptotically efficient protocol as well as a protocol with better concrete efficiency. For the latter protocol, we determine the required sizes of tables and circuits experimentally, and show that the run-time is concretely better than that of existing constructions. The protocol can be extended to a larger number of parties. The proof technique presented in the full version for analyzing Cuckoo hashing in two dimensions is new and can be generalized to analyzing standard Cuckoo hashing as well as other new variants of it.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_5"}, {"primary_key": "3403128", "vector": [], "sparse_vector": [], "title": "On the Ring-LWE and Polynomial-LWE Problems.", "authors": ["Miruna Rosca", "<PERSON>", "<PERSON>"], "summary": "The Ring Learning With Errors problem (\\(\\mathsf {RLWE}\\)) comes in various forms. Vanilla\\(\\mathsf {RLWE}\\)is the decision dual-\\(\\mathsf {RLWE}\\)variant, consisting in distinguishing from uniform a distribution depending on a secret belonging to the dual\\(\\mathcal {O}_K^{\\vee }\\)of the ring of integers\\(\\mathcal {O}_K\\)of a specified number fieldK. In primal-\\(\\mathsf {RLWE}\\), the secret instead belongs to\\(\\mathcal {O}_K\\). Both decision dual-\\(\\mathsf {RLWE}\\)and primal-\\(\\mathsf {RLWE}\\)enjoy search counterparts. Also widely used is (search/decision) Polynomial Learning With Errors (\\(\\mathsf {PLWE}\\)), which is not defined using a ring of integers\\(\\mathcal {O}_K\\)of a number fieldKbut a polynomial ring\\(\\mathbb {Z}[x]/f\\)for a monic irreducible\\(f \\in \\mathbb {Z}[x]\\). We show that there exist reductions between all of these six problems that incur limited parameter losses. More precisely: we prove that the (decision/search) dual to primal reduction from Lyubashevskyet al.[EUROCRYPT 2010] and Peikert [SCN 2016] can be implemented with a small error rate growth for all rings (the resulting reduction is non-uniform polynomial time); we extend it to polynomial-time reductions between (decision/search) primal\\(\\mathsf {RLWE}\\)and\\(\\mathsf {PLWE}\\)that work for a family of polynomialsfthat is exponentially large as a function of\\(\\deg f\\)(the resulting reduction is also non-uniform polynomial time); and we exploit the recent technique from Peikertet al.[STOC 2017] to obtain a search to decision reduction for\\(\\mathsf {RLWE}\\)for arbitrary number fields. The reductions incur error rate increases that depend on intrinsic quantities related toKandf.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_6"}, {"primary_key": "3403129", "vector": [], "sparse_vector": [], "title": "Tightly-Secure Key-Encapsulation Mechanism in the Quantum Random Oracle Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Key-encapsulation mechanisms secure against chosen ciphertext attacks (IND-CCA-secure KEMs) in the quantum random oracle model have been proposed by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (CRYPTO 2012), <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (TCC 2016-B), and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> (TCC 2017). However, all are non-tight and, in particular, security levels of the schemes obtained by these constructions are less than half of original security levels of their building blocks. In this paper, we give a conversion that tightly converts a weakly secure public-key encryption scheme into an IND-CCA-secure KEM in the quantum random oracle model. More precisely, we define a new security notion for deterministic public key encryption (DPKE) called the disjoint simulatability, and we propose a way to convert a disjoint simulatable DPKE scheme into an IND-CCA-secure key-encapsulation mechanism scheme without incurring a significant security degradation. In addition, we give DPKE schemes whose disjoint simulatability is tightly reduced to post-quantum assumptions. As a result, we obtain IND-CCA-secure KEMs tightly reduced to various post-quantum assumptions in the quantum random oracle model.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_17"}, {"primary_key": "3403130", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Goes Public: The Complexity of Known-Key Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the complexity of building secure block ciphers in the setting where the key is known to the attacker. In particular, we consider two security notions with useful implications, namely public-seed pseudorandom permutations (or psPRPs, for short) (<PERSON><PERSON> and <PERSON>, EUROCRYPT ’17) and correlation-intractable ciphers (<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, ASIACRYPT ’07; <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, TCC ’12). For both these notions, we exhibit constructions which make onlytwocalls to an underlying non-invertible primitive, matching the complexity of building a pseudorandom permutation in the secret-key setting. Our psPRP result instantiates the round functions in the Naor-Reingold (NR) construction with a secure UCE hash function. For correlation intractability, we instead instantiate them from a (public) random function, and replace the pairwise-independent permutations in the NR construction with (almost)\\(O(k^2)\\)-wise independent permutations, wherekis the arity of the relations for which we want correlation intractability. Our constructions improve upon the current state of the art, requiring five- and six-round Feistel networks, respectively, to achieve psPRP security and correlation intractability. To do so, we rely on techniques borrowed from Impagliazzo-Rudich-style black-box impossibility proofs for our psPRP result, for which we give what we believe to be the firstconstructiveapplication, and on techniques for studying randomness with limited independence for correlation intractability.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78372-7_21"}, {"primary_key": "3403131", "vector": [], "sparse_vector": [], "title": "Memory Lower Bounds of Reductions Revisited.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In Crypto 2017, <PERSON><PERSON><PERSON> et al. initiated the study on memory-tight reductions and proved two negative results on the memory-tightness of restricted black-box reductions from multi-challenge security to single-challenge security for signatures and an artificial hash function. In this paper, we revisit the results by <PERSON><PERSON><PERSON> et al. and show that for a large class of reductions treating multi-challenge security, it is impossible to avoid loss of memory-tightness unless we sacrifice the efficiency of their running-time. Specifically, we show three lower bound results. Firstly, we show a memory lower bound of natural black-box reductions from the multi-challenge unforgeability of unique signatures to any computational assumption. Then we show a lower bound of restricted reductions from multi-challenge security to single-challenge security for a wide class of cryptographic primitives with unique keys in the multi-user setting. Finally, we extend the lower bound result shown by <PERSON><PERSON><PERSON> et al. treating a hash function to one treating any hash function with a large domain.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78381-9_3"}, {"primary_key": "3403132", "vector": [], "sparse_vector": [], "title": "Fast Near Collision Attack on the Grain v1 Stream Cipher.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Modern stream ciphers often adopt a large internal state to resist various attacks, where the cryptanalysts have to deal with a large number of variables when mounting state recovery attacks. In this paper, we propose a general new cryptanalytic method on stream ciphers, called fast near collision attack, to address this situation. It combines a near collision property with the divide-and-conquer strategy so that only subsets of the internal state, associated with different keystream vectors, are recovered first and merged carefully later to retrieve the full large internal state. A self-contained method is introduced and improved to derive the target subset of the internal state from the partial state difference efficiently. As an application, we propose a new key recovery attack on Grain v1, one of the 7 finalists selected by the eSTREAM project, in the single-key setting. Both the pre-computation and the online phases are tailored according to its internal structure, to provide an attack for any fixed IV in\\(2^{75.7}\\)cipher ticks after the pre-computation of\\(2^{8.1}\\)cipher ticks, given\\(2^{28}\\)-bit memory and about\\(2^{19}\\)keystream bits. Practical experiments on Grain v1 itself whenever possible and on a 80-bit reduced version confirmed our results.", "published": "2018-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-78375-8_25"}]