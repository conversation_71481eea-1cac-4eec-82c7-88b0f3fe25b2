[{"primary_key": "2237261", "vector": [], "sparse_vector": [], "title": "Strongly refuting all semi-random Boolean CSPs.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON><PERSON>"], "summary": "We give an efficient algorithm to strongly refute semi-random instances of all Boolean constraint satisfaction problems. The number of constraints required by our algorithm matches (up to polylogarithmic factors) the best known bounds for efficient refutation of fully random instances. Our main technical contribution is an algorithm to strongly refute semi-random instances of the Boolean k-XOR problem on n variables that have Õ(nk/2) constraints. (In a semi-random k-XOR instance, the equations can be arbitrary and only the right hand sides are random.)One of our key insights is to identify a simple combinatorial property of random XOR instances that makes spectral refutation work. Our approach involves taking an instance that does not satisfy this property (i.e., is not pseudorandom) and reducing it to a partitioned collection of 2-XOR instances. We analyze these subinstances using a carefully chosen quadratic form as proxy, which in turn is bounded via a combination of spectral methods and semidefinite programming. The analysis of our spectral bounds relies only on an off-the-shelf matrix Bernstein inequality. Even for the purely random case, this leads to a shorter proof compared to the ones in the literature that rely on problem-specific trace-moment computations.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.28"}, {"primary_key": "2237262", "vector": [], "sparse_vector": [], "title": "Fast Convergence of Fictitious Play for Diagonal Payoff Matrices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fictitious Play (FP) is a simple and natural dynamic for repeated play in zero-sum games. Proposed by <PERSON> in 1949, FP was shown to converge to a Nash Equilibrium by <PERSON> in 1951, albeit at a slow rate that may depend on the dimension of the problem. In 1959, <PERSON><PERSON> conjectured that FP converges at the more natural rate of . However, <PERSON><PERSON><PERSON><PERSON> and <PERSON> disproved a version of this conjecture in 2014, showing that a slow rate can occur, although their result relies on adversarial tie-breaking. In this paper, we show that <PERSON><PERSON>'s conjecture is indeed correct for the class of diagonal payoff matrices, as long as ties are broken lexicographically. Specifically, we show that FP converges at a rate in the case when the payoff matrix is diagonal. We also prove this bound is tight by showing a matching lower bound in the identity payoff case under the lexicographic tie-breaking assumption.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.84"}, {"primary_key": "2237263", "vector": [], "sparse_vector": [], "title": "Induced subgraphs of bounded treewidth and the container method.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A hole in a graph is an induced cycle of length at least 4. A hole is long if its length is at least 5. By Pt we denote a path on t vertices. In this paper we give polynomial-time algorithms for the following problems:•the Maximum Weight Independent Set problem in long-hole-free graphs, and•the Feedback Vertex Set problem in P5-free graphs.Each of the above results resolves a corresponding long-standing open problem.An extended C5 is a five-vertex hole with an additional vertex adjacent to one or two consecutive vertices of the hole. Let be the class of graphs excluding an extended C5 and holes of length at least 6 as induced subgraphs; contains all long-hole-free graphs and all P5-free graphs. We show that, given an n-vertex graph G ∊ with vertex weights and an integer k, one can in time find a maximum-weight induced subgraph of G of treewidth less than k. This implies both aforementioned results.To achieve this goal, we extend the framework of potential maximal cliques (PMCs) to containers. Developed by <PERSON><PERSON><PERSON><PERSON> and Todinca [SIAM J. Comput. 2001] and extended by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [SIAM J. Comput. 2015], this framework allows to solve high variety of tasks, including finding a maximum-weight induced subgraph of treewidth less than k for fixed k, in time polynomial in the size of the graph and the number of potential maximal cliques. Further developments, tailored to solve the Maximum Weight Independent Set problem within this framework (e.g., for P5-free [SODA 2014] or P6-free graphs [SODA 2019]), enumerate only a specifically chosen subset of all PMCs of a graph. In all aforementioned works, the final step is an involved dynamic programming algorithm whose state space is based on the considered list of PMCs.Here we modify the dynamic programming algorithm and show that it is sufficient to consider only a container for each potential maximal clique: a superset of the maximal clique that intersects the sought solution only in the vertices of the potential maximal clique. This strengthening of the framework not only allows us to obtain our main result, but also leads to significant simplifications of reasonings in previous papers.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.116"}, {"primary_key": "2237264", "vector": [], "sparse_vector": [], "title": "On Testability of First-Order Properties in Bounded-Degree Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study property testing of properties that are definable in first-order logic (FO) in the bounded-degree graph and relational structure models. We show that any FO property that is defined by a formula with quantifier prefix ∃∗∀∗ is testable (i.e., testable with constant query complexity), while there exists an FO property that is expressible by a formula with quantifier prefix ∀∗∃∗ that is not testable. In the dense graph model, a similar picture is long known (<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Combinatorica 2000), despite the very different nature of the two models. In particular, we obtain our lower bound by a first-order formula that defines a class of bounded-degree expanders, based on zig-zag products of graphs. We expect this to be of independent interest. We then prove testability of some first-order properties that speak about isomorphism types of neighbourhoods, including testability of 1-neighbourhood-freeness, and r-neighbourhood-freeness under a mild assumption on the degrees.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.96"}, {"primary_key": "2237265", "vector": [], "sparse_vector": [], "title": "A Lower Bound for Dynamic Fractional Cascading.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We investigate the limits of one of the fundamental ideas in data structures: fractional cascading. This is an important data structure technique to speed up repeated searches for the same key in multiple lists and it has numerous applications. Specifically, the input is a \"catalog\" graph, Gcat, of constant degree together with a list of values assigned to every vertex of Gcat. The goal is to preprocess the input such that given a connected subgraph G′ of Gcat and a single query value q, one can find the predecessor of q in every list that belongs to G′. The classical result by <PERSON><PERSON><PERSON> and <PERSON><PERSON> shows that in a pointer machine, this can be done in the optimal time of where n is the total number of values. However, if insertion and deletion of values are allowed, then the query time slows down to . If only insertions (or deletions) are allowed, then once again, an optimal query time can be obtained but by using amortization at update time.We prove a lower bound of on the worst-case query time of dynamic fractional cascading, when queries are paths of length O(log n). The lower bound applies both to fully dynamic data structures with amortized polylogarithmic update time and incremental data structures with polylogarithmic worst-case update time. As a side, this also proves that amortization is crucial for obtaining an optimal incremental data structure.This is the first non-trivial pointer machine lower bound for a dynamic data structure that breaks the Ω(log n) barrier. In order to obtain this result, we develop a number of new ideas and techniques that hopefully can be useful to obtain additional dynamic lower bounds in the pointer machine model.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.133"}, {"primary_key": "2237266", "vector": [], "sparse_vector": [], "title": "On Two-Handed Planar Assembly Partitioning with Connectivity Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Tzvika Geft", "<PERSON>"], "summary": "Assembly planning is a fundamental problem in robotics and automation, which aims to design a sequence of motions that brings the separate constituent parts of a product into their final placement in the product. It is convenient to study assembly planning in reverse order, where the following key problem, assembly partitioning, arises: Given a set of parts in their final placement in a product, partition them into two sets, each regarded as a rigid body, which we call a subassembly, such that these two subassemblies can be moved sufficiently far away from each other, without colliding with one another. The basic assembly planning problem is further complicated by practical consideration such as how to hold the parts in a subassembly together. Therefore, a desired property of a valid assembly partition is for each of the two subassemblies to be connected.In this paper we study a natural special case of the connected-assembly-partitioning problem: Given a connected set A of unit-grid squares in the plane, find a connected subset S ⊂ A such that A \\ S is also connected and S can be rigidly translated to infinity along a prescribed direction without colliding with A\\S.We show that even this simple problem is NP-complete, settling an open question posed by <PERSON> et al. a quarter of a century ago [16]. We complement the hardness result with two positive results. First, we show that the problem is fixed-parameter tractable and present an O(2kn2)-time algorithm, where n = |A| and k = |S|. Second, we describe a special case of this problem where a connected partition can always be found in O(n) time.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.105"}, {"primary_key": "2237267", "vector": [], "sparse_vector": [], "title": "Decomposing the Complement of the Union of Cubes in Three Dimensions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Let be a set of n axis-aligned cubes of arbitrary sizes in ℝ3 in general position. Let ≔ () be their union, and let κ be the number of vertices on ∂; κ can vary between O(1) and O(n2). We show that cl(ℝ3 \\ ) can be decomposed into O(κ log4 n) axis-aligned boxes with pairwise-disjoint interiors. Given a boundary representation of , such a decomposition can be computed in O(n log2 n + κ log6 n) time. We also show that a decomposition of size O(σ log4 n + κ log2 n), where σ is the number of input cubes that appear on ∂, can be computed in O(n log2 n + σ log8 n + κ log6 n) time. The complexity and runtime bounds improve to O(n log n) if all cubes in are congruent.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.86"}, {"primary_key": "2237268", "vector": [], "sparse_vector": [], "title": "Fine-grained hardness of CVP(P) - Everything that we can prove (and nothing else).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "We show a number of fine-grained hardness results for the Closest Vector Problem in the ℓp norm (CVPp), and its approximate and non-uniform variants. First, we show that CVPp cannot be solved in 2(1–∊)n time for all p ∉ 2ℤ and ∊ > 0, assuming the Strong Exponential Time Hypothesis (SETH). Second, we extend this by showing that there is no 2(1–∊)n-time algorithm for approximating CVPp to within a constant factor γ for such p assuming a \"gap\" version of SETH, with an explicit relationship between γ, p, and the arity k = k(∊) of the underlying hard CSP. Third, we show the same hardness result for (exact) CVPp with preprocessing (assuming non-uniform SETH).For exact \"plain\" CVPp, the same hardness result was shown in [<PERSON>, <PERSON>, and <PERSON><PERSON> FOCS 2017] for all but finitely many p ∉ 2ℤ, where the set of exceptions depended on ∊ and was not explicit. For the approximate and preprocessing problems, only very weak bounds were known prior to this work.We also show that the restriction to p ∉ 2ℤ is in some sense inherent. In particular, we show that no \"natural\" reduction can rule out even a 23n/4-time algorithm for CVP2 under SETH. For this, we prove that the possible sets of closest lattice vectors to a target in the ℓ2 norm have quite rigid structure, which essentially prevents them from being as expressive as 3-CNFs.We prove these results using techniques from many different fields, including complex analysis, functional analysis, additive combinatorics, and discrete Fourier analysis. E.g., along the way, we give a new (and tighter) proof of Szemerédi's cube lemma for the boolean cube.Please see the full version of this paper for the proofs of these results [1].", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.109"}, {"primary_key": "2237269", "vector": [], "sparse_vector": [], "title": "Dimension-Preserving Reductions Between SVP and CVP in Different p-Norms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "We show a number of reductions between the Shortest Vector Problem and the Closest Vector Problem over lattices in different ℓp norms (SV<PERSON>p and CVPp respectively). Specifically, we present the following 2∊m-time reductions for 1 ≤ p ≤ q ≤ ∞, which all increase the rank n and dimension m of the input lattice by at most one:•a reduction from Õ(1/∊1/p)γ-approximate SVPq to γ-approximate SVPp;•a reduction from Õ(1/∊1/p)γ-approximate CVPp to γ-approximate CVPq; and•a reduction from Õ(1/∊1+1/p)-CVPq to (1 + ∊)-unique SVPp (which in turn trivially reduces to (1 + ∊)-approximate SVPp).The last reduction is interesting even in the case p = q. In particular, this special case subsumes much prior work adapting 2O(m)-time SVPp algorithms to solve O(1)-approximate CVPp. In fact, we show a stronger result in the special case when 1 ≤ p = q ≤ 2 and the SVPp oracle is exact: a reduction from O(1/∊1/p)-CVPp to (exact) SVPp in 2∊m time. For example, taking ∊ = log m/m and p = 2 gives a slight improvement over <PERSON><PERSON><PERSON>'s celebrated polynomial-time reduction from to SVP2. We also note that the last two reductions can be combined to give a reduction from approximate-CVPp to SVPq for any p and q, regardless of whether p ≤ q or p > q.Our techniques combine those from the recent breakthrough work of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [21] (which showed how to adapt the current fastest known algorithm for these problems in the ℓ2 norm to all ℓp norms) together with sparsification-based techniques.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.145"}, {"primary_key": "2237270", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Parallel Paging and Green Paging.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the parallel paging problem, there are p processors that share a cache of size k. The goal is to partition the cache among the processors over time in order to minimize their average completion time. For this long-standing open problem, we give tight upper and lower bounds of Θ(logp) on the competitive ratio with O(1) resource augmentation.A key idea in both our algorithms and lower bounds is to relate the problem of parallel paging to the seemingly unrelated problem of green paging. In green paging, there is an energy-optimized processor that can temporarily turn off one or more of its cache banks (thereby reducing power consumption), so that the cache size varies between a maximum size k and a minimum size k/p. The goal is to minimize the total energy consumed by the computation, which is proportional to the integral of the cache size over time.We show that any efficient solution to green paging can be converted into an efficient solution to parallel paging, and that any lower bound for green paging can be converted into a lower bound for parallel paging, in both cases in a black-box fashion. We then show that, with O(1) resource augmentation, the optimal competitive ratio for deterministic online green paging is Θ(log p), which, in turn, implies the same bounds for deterministic online parallel paging.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.180"}, {"primary_key": "2237271", "vector": [], "sparse_vector": [], "title": "A Refined Laser Method and Faster Matrix Multiplication.", "authors": ["<PERSON>", "Virginia Vassilevska Williams"], "summary": "The complexity of matrix multiplication is measured in terms of ω, the smallest real number such that two n × n matrices can be multiplied using O(nω+∊) field operations for all ∊ > 0; the best bound until now is ω < 2.37287 [<PERSON>'14]. All bounds on ω since 1986 have been obtained using the so-called laser method, a way to lower-bound the ‘value’ of a tensor in designing matrix multiplication algorithms. The main result of this paper is a refinement of the laser method that improves the resulting value bound for most sufficiently large tensors. Thus, even before computing any specific values, it is clear that we achieve an improved bound on ω, and we indeed obtain the best bound on ω to date:ω < 2.37286.The improvement is of the same magnitude as the improvement that [<PERSON>'14] obtained over the previous bound [<PERSON><PERSON><PERSON><PERSON><PERSON>.'12]. Our improvement to the laser method is quite general, and we believe it will have further applications in arithmetic complexity.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.32"}, {"primary_key": "2237272", "vector": [], "sparse_vector": [], "title": "Approximate Nearest Neighbors Beyond Space Partitions.", "authors": ["<PERSON><PERSON><PERSON>", "Aleksan<PERSON>", "Ilya <PERSON>", "<PERSON>"], "summary": "We show improved data structures for the high-dimensional approximate nearest neighbor search problem (ANN) for ℓp distances for \"large\" values of p and for generalized Hamming distances. The previous best data structures proceeded by embedding a metric of interest into the ℓ∞ space or an ℓ∞-direct sum with simple summands, and then using data structures of <PERSON><PERSON> (<PERSON><PERSON><PERSON> 1998, SoCG 2002) for ℓ∞-ANN. In contrast to this, we bypass the embedding step and proceed by extending the technique underlying the ℓ∞ data structures to handle ℓp and generalized Hamming distances directly. The resulting data structures are randomized, in contrast to <PERSON><PERSON>'s result for ℓ∞-ANN, and replicate input points, in contrast with Locality Sensitive Hashing. This leads to ANN data structures with significantly improved approximations over those implied by embeddings, as well as those obtained using all known approaches based on random space partitions.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.72"}, {"primary_key": "2237273", "vector": [], "sparse_vector": [], "title": "2-Level Quasi-Planarity or How Caterpillars Climb (SPQR-)Trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a bipartite graph G = (Vb, Vr, E), the 2-Level Quasi-Planarity problem asks for the existence of a drawing of G in the plane such that the vertices in Vb and in Vr lie along two parallel lines ℓb and ℓr, respectively, each edge in E is drawn in the unbounded strip of the plane delimited by ℓb and ℓr, and no three edges in E pairwise cross.We prove that the 2-LEVEL Quasi-Planarity problem is NP-complete. This answers an open question of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Furthermore, we show that the problem becomes linear-time solvable if the ordering of the vertices in Vb along ℓb is prescribed. Our contributions provide the first results on the computational complexity of recognizing quasi-planar graphs, which is a long-standing open question.Our linear-time algorithm exploits several ingredients, including a combinatorial characterization of the positive instances of the problem in terms of the existence of a planar embedding with a caterpillar-like structure, and an SPQR-tree-based algorithm for testing the existence of such a planar embedding. Our algorithm builds upon a classification of the types of embeddings with respect to the structure of the portion of the caterpillar they contain and performs a computation of the realizable embedding types based on a succinct description of their features by means of constant-size gadgets.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.165"}, {"primary_key": "2237274", "vector": [], "sparse_vector": [], "title": "Analytic quantum weak coin flipping protocols with arbitrarily small bias.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Weak coin flipping (WCF) is a fundamental cryptographic primitive for two-party secure computation, where two distrustful parties need to remotely establish a shared random bit whilst having opposite preferred outcomes. It is the strongest known primitive with arbitrarily close to perfect security quantumly while classically, its security is completely compromised (unless one makes further assumptions, such as computational hardness). A WCF protocol is said to have bias ∊ if neither party can force their preferred outcome with probability greater than 1/2 + ∊. Classical WCF protocols are shown to have bias 1/2, i.e., a cheating party can always force their preferred outcome. On the other hand, there exist quantum WCF protocols with arbitrarily small bias, as <PERSON><PERSON><PERSON> showed in his seminal work in 2007 [arXiv:0711.4114]. In particular, he proved the existence of a family of WCF protocols approaching bias ∊(k) = 1/(4k +2) for arbitrarily large k and proposed a protocol with bias 1/6. Last year, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> presented a protocol with bias 1/10 and to go below this bias, they designed an algorithm that numerically constructs unitary matrices corresponding to WCF protocols with arbitrarily small bias [STOC'19, p. 205–216]. In this work, we present new techniques which yield a fully analytical construction of WCF protocols with bias arbitrarily close to zero, thus achieving a solution that has been missing for more than a decade. Furthermore, our new techniques lead to a simplified proof of existence of WCF protocols by circumventing the non-constructive part of <PERSON><PERSON><PERSON>'s proof. As an example, we illustrate the construction of a WCF protocol with bias 1/14.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.58"}, {"primary_key": "2237275", "vector": [], "sparse_vector": [], "title": "Constrained-Order Prophet Inequalities.", "authors": ["<PERSON><PERSON>", "Odysseas Drosis", "<PERSON>"], "summary": "Free order prophet inequalities bound the ratio between the expected value obtained by two parties each selecting one value from a set of independent random variables: a \"prophet\" who knows the value of each variable and may select the maximum one, and a \"gambler\" who is free to choose the order in which to observe the values but must select one of them immediately after observing it, without knowing what values will be sampled for the unobserved variables. It is known that the gambler can always ensure an expected payoff at least 0.669 … times as great as that of the prophet. In fact, even if the gambler uses a threshold stopping rule, meaning there is a fixed threshold value such that the gambler rejects every sample below the threshold and accepts every sample above it, the threshold can always be chosen so that the gambler-to-prophet ratio is at least . … In contrast, if the gambler must observe the values in a predetermined order, the tight bound for the gambler-to-prophet ratio is 1/2.In this work we investigate a model that interpolates between these two extremes. We assume there is a predefined set of permutations of the set indexing the random variables, and the gambler is free to choose the order of observation to be any one of these predefined permutations. Surprisingly, we show that even when only two orderings are allowed — namely, the forward and reverse orderings — the gambler-to-prophet ratio improves to …, the inverse of the golden ratio. As the number of allowed permutations grows beyond 2, a striking \"double plateau\" phenomenon emerges: after increasing from 0.5 to φ–1 when two permutations are allowed, the gambler-to-prophet ratio achievable by threshold stopping rules does not exceed φ–1 + o(1) until the number of allowed permutations grows to O(log n). The ratio reaches for a suitably chosen set of O(poly(∊–1) · log n) permutations and does not exceed even when the full set of n! permutations is allowed.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.121"}, {"primary_key": "2237276", "vector": [], "sparse_vector": [], "title": "Sorting Short Keys in Circuits of Size o(n log n).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the classical problem of sorting n elements, where each element is described with a k-bit comparison-key and a w-bit payload. A long-standing open problem is whether there exist (k + w) · o(n log n)-sized boolean circuits for sorting. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> (STOC'83) constructed the famous AKS sorting network with (k + w) · O(n log n) boolean gates. Recently, <PERSON><PERSON><PERSON> et al. (STOC'19) showed that if the famous Li-Li network coding conjecture is true, then sorting circuits of size w · o(n log n) do not exist for general k (while unconditional circuit lower bound is out of the reach of existing techniques).In this paper, we show that one can overcome the n log n barrier when the comparison-keys are short. Specifically, we construct a sorting circuit with (k +w) · O(nk) · poly(log∗ n – log∗(w + k)) boolean gates, asymptotically better than AKS sorting network if the keys are short, say, k = o(log n) (ignoring poly log∗ terms). Such a result might be surprising since comparator-based techniques must incur Ω(n log n) comparators even when the keys are only 1-bit long (e.g., see <PERSON><PERSON><PERSON>'s \"Art of Programming\" textbook). To the best of our knowledge, this is also the first non-trivial result on non-comparison-based sorting circuits. We also show that if the Li-Li network coding conjecture is true, our upper bound is optimal, barring poly log∗ terms, for every k = O(log n).", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.134"}, {"primary_key": "2237277", "vector": [], "sparse_vector": [], "title": "Improved Truthful Mechanisms for Subadditive Combinatorial Auctions: Breaking the Logarithmic Barrier.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a computationally-efficient truthful mechanism for combinatorial auctions with subadditive bidders that achieves an $O((\\log\\!\\log{m})^3)$-approximation to the maximum welfare in expectation using $O(n)$ demand queries; here $m$ and $n$ are the number of items and bidders, respectively. This breaks the longstanding logarithmic barrier for the problem dating back to the $O(\\log{m}\\cdot\\log\\!\\log{m})$-approximation mechanism of <PERSON><PERSON><PERSON><PERSON> from 2007. Along the way, we also improve and considerably simplify the state-of-the-art mechanisms for submodular bidders.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.40"}, {"primary_key": "2237278", "vector": [], "sparse_vector": [], "title": "Optimal Inapproximability with Universal Factor Graphs.", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON>"], "summary": "The factor graph of an instance of a constraint satisfaction problem (CSP) is the bipartite graph indicating which variables appear in each constraint. An instance of the CSP is given by the factor graph together with a list of which predicate is applied for each constraint. We establish that many Max-CSPs remain as hard to approximate as in the general case even when the factor graph is fixed (depending only on the size of the instance) and known in advance.Examples of results obtained for this restricted setting are:1.Optimal inapproximability for Max-3-Lin and Max-3-Sa<PERSON> (<PERSON>åstad, J. ACM 2001).2.Approximation resistance for predicates supporting pairwise independent subgroups (<PERSON>, J. ACM 2016).3.Hardness of the \"(2 + ∊)-Sat\" problem and other Promise CSPs (<PERSON><PERSON><PERSON> et al., SIAM J. Comput. 2017).The main technical tool used to establish these results is a new way of folding the long code which we call \"functional folding\".", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.27"}, {"primary_key": "2237279", "vector": [], "sparse_vector": [], "title": "Infinite-Duration All-Pay Bidding Games.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In a two-player zero-sum graph game the players move a token throughout a graph to produce an infinite path, which determines the winner or payoff of the game. Traditionally, the players alternate turns in moving the token. In bidding games, however, the players have budgets, and in each turn, we hold an \"auction\" (bidding) to determine which player moves the token: both players simultaneously submit bids and the higher bidder moves the token. The bidding mechanisms differ in their payment schemes. Bidding games were largely studied with variants of first-price bidding in which only the higher bidder pays his bid. We focus on all-pay bidding, where both players pay their bids. Finite-duration all-pay bidding games were studied and shown to be technically more challenging than their first-price counterparts. We study for the first time, infinite-duration all-pay bidding games. Our most interesting results are for mean-payoff objectives: we portray a complete picture for games played on strongly-connected graphs. We study both pure (deterministic) and mixed (probabilistic) strategies and completely characterize the optimal and almost-sure (with probability 1) payoffs the players can respectively guarantee. We show that mean-payoff games under all-pay bidding exhibit the intriguing mathematical properties of their first-price counterparts; namely, an equivalence with random-turn games in which in each turn, the player who moves is selected according to a (biased) coin toss. The equivalences for all-pay bidding are more intricate and unexpected than for first-price bidding.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.38"}, {"primary_key": "2237280", "vector": [], "sparse_vector": [], "title": "The Min-Cost Matching with Concave Delays Problem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of online min-cost perfect matching with concave delays. We begin with the single location variant. Specifically, requests arrive in an online fashion at a single location. The algorithm must then choose between matching a pair of requests or delaying them to be matched later on. The cost is defined by a concave function on the delay. Given linear or even convex delay functions, matching any two available requests is trivially optimal. However, this does not extend to concave delays. We solve this by providing an O(1)-competitive algorithm that is defined through a series of delay counters.Thereafter we consider the problem given an underlying n-points metric. The cost of a matching is then defined as the connection cost (as defined by the metric) plus the delay cost. Given linear delays, this problem was introduced by <PERSON><PERSON> et al. and dubbed the Min-cost perfect matching with linear delays (MPMD) problem. <PERSON> et al. considered convex delays and subsequently asked whether there exists a solution with small competitive ratio given concave delays. We show this to be true by extending our single location algorithm and proving O(log n) competitiveness. Finally, we turn our focus to the bichromatic case, wherein requests have polarities and only opposite polarities may be matched. We show how to alter our former algorithms to again achieve O(1) and O(log n) competitiveness for the single location and for the metric case.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.20"}, {"primary_key": "2237281", "vector": [], "sparse_vector": [], "title": "List-Decodable Subspace Recovery: Dimension Independent Error in Polynomial Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In list-decodable subspace recovery, the input is a collection of $n$ points $\\alpha n$ (for some $\\alpha \\ll 1/2$) of which are drawn i.i.d. from a distribution $\\mathcal{D}$ with a isotropic rank $r$ covariance $\\Pi_*$ (the \\emph{inliers}) and the rest are arbitrary, potential adversarial outliers. The goal is to recover a $O(1/\\alpha)$ size list of candidate covariances that contains a $\\hat{\\Pi}$ close to $\\Pi_*$. Two recent independent works (<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>, Bakshi-Ko<PERSON>ri 2020) gave the first efficient algorithm for this problem. These results, however, obtain an error that grows with the dimension (linearly in [RY] and logarithmically in BK) at the cost of quasi-polynomial running time) and rely on \\emph{certifiable anti-concentration} - a relatively strict condition satisfied essentially only by the Gaussian distribution. In this work, we improve on these results on all three fronts: \\emph{dimension-independent} error via a faster fixed-polynomial running time under less restrictive distributional assumptions. Specifically, we give a $poly(1/\\alpha) d^{O(1)}$ time algorithm that outputs a list containing a $\\hat{\\Pi}$ satisfying $\\|\\hat{\\Pi} -\\Pi_*\\|_F \\leq O(1/\\alpha)$. Our result only needs $\\mathcal{D}$ to have \\emph{certifiably hypercontractive} degree 2 polynomials. As a result, in addition to Gaussians, our algorithm applies to the uniform distribution on the hypercube and $q$-ary cubes and arbitrary product distributions with subgaussian marginals. Prior work (Raghavendra and Yau, 2020) had identified such distributions as potential hard examples as such distributions do not exhibit strong enough anti-concentration. When $\\mathcal{D}$ satisfies certifiable anti-concentration, we obtain a stronger error guarantee of $\\|\\hat{\\Pi}-\\Pi_*\\|_F \\leq \\eta$ for any arbitrary $\\eta > 0$ in $d^{O(poly(1/\\alpha) + \\log (1/\\eta))}$ time.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.78"}, {"primary_key": "2237282", "vector": [], "sparse_vector": [], "title": "Connecting Robust Shuffle Privacy and Pan-Privacy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the shuffle model of differential privacy, data-holding users send randomized messages to a secure shuffler, the shuffler permutes the messages, and the resulting collection of messages must be differentially private with regard to user data. In the pan-private model, an algorithm processes a stream of data while maintaining an internal state that is differentially private with regard to the stream data. We give evidence connecting these two apparently different models.Our results focus on robustly shuffle private protocols, whose privacy guarantees are not greatly affected by malicious users. First, we give robustly shuffle private protocols and upper bounds for counting distinct elements and uniformity testing. Second, we use pan-private lower bounds to prove robustly shuffle private lower bounds for both problems. Focusing on the dependence on the domain size k, we find that robust shuffle privacy and pan-privacy have additive error for counting distinct elements. For uniformity testing, we give a robustly shuffle private protocol with sample complexity Õ(k2/3) and show that an Ω(k2/3) dependence is necessary in a specific parameter regime. Finally, we show that this connection is useful in both directions: we give a pan-private adaptation of recent work on shuffle private histograms and use it to recover further separations between pan-privacy and interactive local privacy.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.142"}, {"primary_key": "2237283", "vector": [], "sparse_vector": [], "title": "Non-Excludable Dynamic Mechanism Design.", "authors": ["Santiago R. <PERSON>eiro", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Dynamic mechanism design expands the scope of allocations that can be implemented and the performance that can be attained compared to static mechanisms. Even under stringent participation constraints and restrictions on transfers, recent work demonstrated that it is possible for a designer to extract the surplus of all players as revenue when players have quasilinear utilities and the number of interactions is large. Much of the analysis has focused on excludable environments (i.e., any player can be excluded from trade without affecting the utilities of others). The mechanisms presented in the literature, however, do not extend to non-excludable environments. Two prototypical examples of such environments are: (i) public projects, where all players must have the same allocation; and (ii) non-disposable goods, where each item must be allocated to some player. We show a general mechanism that can asymptotically extract full surplus as revenue in such environments. Moreover, we provide a tight characterization for general environments, and identify necessary and sufficient conditions on the possibility of asymptotic full surplus extraction. Our characterization is based on the geometry of achievable utility sets – convex sets that delineate the expected utilities that can be implemented by static mechanisms. Our results provide a reduction from dynamic to static mechanism design: the geometry of the achievable utility set of static mechanisms determines whether it is possible to fully extract surplus in the limit.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.82"}, {"primary_key": "2237284", "vector": [], "sparse_vector": [], "title": "Local Statistics, Semidefinite Programming, and Community Detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a new, efficiently solvable hierarchy of semidefinite programming relaxations for inference problems. As test cases, we consider the problem of community detection in block models. The vertices are partitioned into k communities, and a graph is sampled conditional on a prescribed number of inter- and intra-community edges. The problem of detection, where we are to decide with high probability whether a graph was drawn from this model or the uniform distribution on regular graphs, is conjectured to undergo a computational phase transition at a point called the Kesten-Stigum (KS) threshold.In this work, we consider two models of random graphs namely the well-studied (irregular) Stochastic Block Model and a distribution over random regular graphs we'll call the Degree Regular Block Model. For both these models, we show that sufficiently high constant levels of our hierarchy can perform detection arbitrarily close to the KS threshold and that our algorithm is robust to up to a linear number of adversarial edge perturbations. Furthermore, in the case of Degree Regular Block Model, we show that below the Kesten-Stigum threshold no constant level can do so.In the case of the (irregular) Stochastic Block Model, it is known that efficient algorithms exist all the way down to this threshold, although none are robust to adversarial perturbation of a linear number of edges. More importantly, there is little complexity-theoretic evidence that detection is hard below the threshold. In the DRBM with more than two groups, it has not to our knowledge been proven that any algorithm succeeds down to the KS threshold, let alone that one can do so robustly, and there is a similar dearth of evidence for hardness below this point.Our SDP hierarchy is highly general and applicable to a wide range of hypothesis testing problems.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.79"}, {"primary_key": "2237285", "vector": [], "sparse_vector": [], "title": "Non-uniform Geometric Set Cover and Scheduling on Multiple Machines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the following general scheduling problem studied recently by <PERSON><PERSON><PERSON> [27]. There are n jobs, all released at time 0, where job j has size pj and an associated arbitrary non-decreasing cost function fj of its completion time. The goal is to find a schedule on m machines with minimum total cost. We give an O(1) approximation for the problem, improving upon the previous O(log log nP) bound (P is the maximum to minimum size ratio), and resolving the open question in [27].We first note that the scheduling problem can be reduced to a clean geometric set cover problem where points on a line with arbitrary demands, must be covered by a minimum cost collection of given intervals with non-uniform capacity profiles. Unfortunately, current techniques for such problems based on knapsack cover inequalities and low union complexity, completely lose the geometric structure in the non-uniform capacity profiles and incur at least an Ω(log log P) loss.To this end, we consider general covering problems with non-uniform capacities, and give a new method to handle capacities in a way that completely preserves their geometric structure. This allows us to use sophisticated geometric ideas in a black-box way to avoid the Ω(log log P) loss in previous approaches. In addition to the scheduling problem above, we use this approach to obtain O(1) or inverse Ackermann type bounds for several basic capacitated covering problems.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.179"}, {"primary_key": "2237286", "vector": [], "sparse_vector": [], "title": "Improved Approximations for Min Sum Vertex Cover and Generalized Min Sum Set Cover.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the generalized min sum set cover (GMSSC) problem, wherein given a collection of hyperedges E with arbitrary covering requirements {ke ∊ Z+ : e ∊ E}, the goal is to find an ordering of the vertices to minimize the total cover time of the hyperedges; a hyperedge e is considered covered by the first time when ke many of its vertices appear in the ordering.We give a 4.642 approximation algorithm for GMSSC, coming close to the best possible bound of 4, already for the classical special case (with all ke = 1) of min sum set cover (MSSC) studied by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [11], and improving upon the previous best known bound of 12.4 due to <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON> [20]. Our algorithm is based on transforming the LP solution by a suitable kernel and applying randomized rounding. This also gives an LP-based 4 approximation for MSSC. As part of the analysis of our algorithm, we also derive an inequality on the lower tail of a sum of independent <PERSON><PERSON><PERSON> random variables, which might be of independent interest and broader utility.Another well-known special case is the min sum vertex cover (MSVC) problem, in which the input hypergraph is a graph (i.e., |e| = 2) and ke = 1, for every edge e ∊ E. We give a 16/9 ≃ 1.778 approximation for MSVC, and show a matching integrality gap for the natural LP relaxation. This improves upon the previous best 1.999946 approximation of <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> [6]. (The claimed 1.79 approximation result of <PERSON><PERSON><PERSON>, <PERSON><PERSON>i and <PERSON>athi [21] for the MSVC turned out have an unfortunate, seemingly unfixable, mistake in it.)Finally, we revisit MSSC and consider the ℓp norm of cover-time of the hyperedges. Using a dual fitting argument, we show that the natural greedy algorithm simultaneously achieves approximation guarantees of (p + 1)1+1/p, for all p ≥ 1, giving another proof of the result of Golovin, Gupta, Kumar and Tangwongsan [13], and showing its tightness up to NP-hardness. For p = 1, this gives yet another proof of the 4 approximation for MSSC.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.62"}, {"primary_key": "2237287", "vector": [], "sparse_vector": [], "title": "Online Discrepancy Minimization for Stochastic Arrivals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the stochastic online vector balancing problem, vectors v1, v2, …, vT chosen independently from an arbitrary distribution in ℝn arrive one-by-one and must be immediately given a ± sign. The goal is to keep the norm of the discrepancy vector, i.e., the signed prefix-sum, as small as possible for a given target norm.We consider some of the most well-known problems in discrepancy theory in the above online stochastic setting, and give algorithms that match the known offline bounds up to polylog(nT) factors. This substantially generalizes and improves upon the previous results of <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> (STOC' 20). In particular, for the Komlós problem where ‖vt‖2 ≤ 1 for each t, our algorithm achieves Õ(1) discrepancy with high probability, improving upon the previous Õ(n3/2) bound. For <PERSON><PERSON><PERSON><PERSON>'s problem of minimizing the discrepancy of axis-aligned boxes, we obtain an O(logd+4T) bound for arbitrary distribution over points. Previous techniques only worked for product distributions and gave a weaker O(log2d+1 T) bound. We also consider the Banaszczyk setting, where given a symmetric convex body K with Gaussian measure at least 1/2, our algorithm achieves Õ(1) discrepancy with respect to the norm given by <PERSON> for input distributions with sub-exponential tails.Our results are based on a new potential function approach. Previous techniques consider a potential that penalizes large discrepancy, and greedily chooses the next color to minimize the increase in potential. Our key idea is to introduce a potential that also enforces constraints on how the discrepancy vector evolves, allowing us to maintain certain anti-concentration properties. We believe that our techniques to control the evolution of states could find other applications in stochastic processes and online algorithms. For the Banaszczyk setting, we further enhance this potential by combining it with ideas from generic chaining. Finally, we also extend these results to the setting of online multicolor discrepancy.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.169"}, {"primary_key": "2237288", "vector": [], "sparse_vector": [], "title": "Self-Stabilizing Clock Synchronization with 1-bit Messages.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the fundamental problem of distributed clock synchronization in a basic probabilistic communication setting. We consider a synchronous fully-connected network of n agents, where each agent has a local clock, that is, a counter increasing by one modulo T in each round. The clocks have arbitrary values initially, and they must all indicate the same time eventually. We assume a pull communication model, where in every round each agent receives an ℓ-bit message from a random agent. We devise several fast synchronization algorithms that use small messages and are self-stabilizing, that is, the complete initial state of each agent (not just its clock value) can be arbitrary.We first provide a surprising algorithm for synchronizing a binary clock (T = 2) using 1-bit messages (ℓ = 1). This is a variant of the voter model and converges in O(log n) rounds w.h.p., unlike the voter model which needs polynomial time. Next we present an elegant extension of our algorithm that synchronizes a modulo T = 4 clock, with ℓ = 1, in O(log n) rounds. Using these two algorithms, we refine an algorithm of <PERSON><PERSON><PERSON> et al. (SODA'17), that synchronizes a modulo T clock in polylogarithmic time (in n and T). The original algorithm uses ℓ = 3 bit messages, and each agent receives messages from two agents per round. Our algorithm reduces the message size to ℓ = 2, and the number of messages received to one per round, without increasing the running time. Finally, we present two algorithms that simulate our last algorithm achieving ℓ < 2, without hurting the asymptotic running time. The first algorithm uses a message space of size 3, i.e., ℓ = log2(3). The second requires a rough upper bound on log n, and uses just 1-bit messages. More generally, our constructions can simulate any self-stabilizing algorithm that requires a shared clock, without increasing the message size and by only increasing the running time by a constant factor and a polylogarithmic term.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.129"}, {"primary_key": "2237289", "vector": [], "sparse_vector": [], "title": "Randomized Cup Game Algorithms Against Strong Adversaries.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In each step of the cup game on n cups, a filler distributes up to 1 – ∊ water among the cups, and then an emptier removes 1 unit of water from a single cup. The emptier's goal is to minimize the height of the fullest cup, also known as the backlog. The cup emptying game has found extensive applications to processor scheduling, network-switch buffer management, quality of service guarantees, and data-structure deamortization.The greedy emptying algorithm (i.e., always remove from the fullest cup) is known to achieve backlog O(log n) and to be the optimal deterministic algorithm. Randomized algorithms can do significantly better, achieving backlog O (log log n) with high probability, as long as ∊ is not too small. In order to achieve these improvements, the known randomized algorithms require that the filler is an oblivious adversary, unaware of which cups the emptier chooses to empty out of at each step. Such randomized guarantees are known to be impossible against fully adaptive fillers.We show that, even when the filler is just \"slightly\" non-adaptive, randomized emptying algorithms can still guarantee a backlog of O (log log n). In particular, we give randomized randomized algorithms against an elevated adaptive filler, which is an adaptive filler that can see the precise fills of every cup containing more than 3 units of water, but not of the cups containing less than 3 units.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.123"}, {"primary_key": "2237290", "vector": [], "sparse_vector": [], "title": "Near-Linear Time Homomorphism Counting in Bounded Degeneracy Graphs: The Barrier of Long Induced Cycles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Counting homomorphisms of a constant sized pattern graph H in an input graph G is a fundamental computational problem. There is a rich history of studying the complexity of this problem, under various constraints on the input G and the pattern H. Given the significance of this problem and the large sizes of modern inputs, we investigate when near-linear time algorithms are possible. We focus on the case when the input graph has bounded degeneracy, a commonly studied and practically relevant class for homomorphism counting. It is known from previous work that for certain classes of H, H-homomorphisms can be counted exactly in near-linear time in bounded degeneracy graphs. Can we precisely characterize the patterns H for which near-linear time algorithms are possible?We completely resolve this problem, discovering a clean dichotomy using fine-grained complexity. Let m denote the number of edges in G. We prove the following: if the largest induced cycle in H has length at most 5, then there is an O(m log m) algorithm for counting H-homomorphisms in bounded degeneracy graphs. If the largest induced cycle in H has length at least 6, then (assuming standard fine-grained complexity conjectures) there is a constant γ > 0, such that there is no o(m1+γ) time algorithm for counting H-homomorphisms.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.138"}, {"primary_key": "2237291", "vector": [], "sparse_vector": [], "title": "New Techniques and Fine-Grained Hardness for Dynamic Near-Additive Spanners.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Virginia Vassilevska Williams", "<PERSON>"], "summary": "Maintaining and updating shortest paths information in a graph is a fundamental problem with many applications. As computations on dense graphs can be prohibitively expensive, and it is preferable to perform the computations on a sparse skeleton of the given graph that roughly preserves the shortest paths information. Spanners and emulators serve this purpose. Unfortunately, very little is known about dynamically maintaining sparse spanners and emulators as the graph is modified by a sequence of edge insertions and deletions. This paper develops fast dynamic algorithms for spanner and emulator maintenance and provides evidence from fine-grained complexity that these algorithms are tight. For unweighted undirected m-edge n-node graphs we obtain the following results.Under the popular OMv conjecture, there can be no decremental or incremental algorithm that maintains an n1+o(1) edge (purely additive) +nδ-emulator for any δ < 1/2 with arbitrary polynomial preprocessing time and total update time m1+o(1). Also, under the Combinatorial k-Clique hypothesis, any fully dynamic combinatorial algorithm that maintains an n1+o(1) edge (1 + ∊, no(1))-spanner or emulator for small ∊ must either have preprocessing time mn1–o(1) or amortized update time m1–o(1). Both of our conditional lower bounds are tight.As the above fully dynamic lower bound only applies to combinatorial algorithms, we also develop an algebraic spanner algorithm that improves over the m1–o(1) update time for dense graphs. For any constant ∊ ∊ (0, 1], there is a fully dynamic algorithm with worst-case update time O(n1.529) that whp maintains an n1+o(1) edge (1 + ∊, no(1))-spanner.Our new algebraic techniques allow us to also obtain a new fully dynamic algorithm for All-Pairs Shortest Paths (APSP) that can perform both edge updates and can report shortest paths in worst-case time O(n1.9), which are correct whp. This is the first path-reporting fully dynamic APSP algorithm with a truly subquadratic query time that beats O(n2.5) update time. It works against an oblivious adversary.Finally, we give two applications of our new dynamic spanner algorithms: (1) a fully dynamic (1 + ∊)-approximate APSP algorithm with update time O(n1.529) that can report approximate shortest paths in n1+o(1) time per query; previous subquadratic update/query algorithms could only report the distance, but not obtain the paths; (2) a fully dynamic algorithm for near-2-approximate Steiner tree maintenance with both terminal and edge updates.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.110"}, {"primary_key": "2237292", "vector": [], "sparse_vector": [], "title": "Online Edge Coloring Algorithms via the Nibble Method.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Nearly thirty years ago, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [IPL'92] conjectured that an online (1 + o(1))Δ-edge-coloring algorithm exists for n-node graphs of maximum degree Δ = ω(log n). This conjecture remains open in general, though it was recently proven for bipartite graphs under one-sided vertex arrivals by <PERSON> et al. [FOCS'19]. In a similar vein, we study edge coloring under widely-studied relaxations of the online model.Our main result is in the random-order online model. For this model, known results fall short of the Bar<PERSON><PERSON><PERSON> et al. conjecture, either in the degree bound [<PERSON><PERSON><PERSON><PERSON> et al. FOCS'03], or number of colors used [<PERSON><PERSON><PERSON> et al. SODA'10]. We achieve the best of both worlds, thus resolving the <PERSON><PERSON><PERSON><PERSON> et al. conjecture in the affirmative for this model.Our second result is in the adversarial online (and dynamic) model with recourse. A recent algorithm of <PERSON><PERSON> et al. [SODA'19] yields a (1 + ∊) Δ-edge-coloring with poly(log n/∊) recourse. We achieve the same with poly(1/∊) recourse, thus removing all dependence on n.Underlying our results is one common offline algorithm, which we show how to implement in these two online models. Our algorithm, based on the Rödl Nibble Method, is an adaptation of the distributed algorithm of <PERSON><PERSON> et al. [TCS'98]. The Nibble Method has proven successful for distributed edge coloring. We display its usefulness in the context of online algorithms.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.168"}, {"primary_key": "2237293", "vector": [], "sparse_vector": [], "title": "Dynamic Set Cover: Improved Amortized and Worst-Case Update Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Danupon <PERSON>", "<PERSON><PERSON>"], "summary": "In the dynamic minimum set cover problem, a challenge is to minimize the update time while guaranteeing close to the optimal min(O(log n), f) approximation factor. (Throughout, m, n, f, and C are parameters denoting the maximum number of sets, number of elements, frequency, and the cost range.) In the high-frequency range, when f = Ω(log n), this was achieved by a deterministic O(log n)-approximation algorithm with O(f log n) amortized update time [<PERSON> et al. STOC'17]. In the low-frequency range, the line of work by <PERSON> et al. [STOC'17], <PERSON><PERSON><PERSON> et al. [STOC'19], and <PERSON><PERSON><PERSON> et al. [ICALP'15, IPCO'17, FOCS'19] led to a deterministic (1 + ∊) f-approximation algorithm with O(f log(Cn)/∊2) amortized update time. In this paper we improve the latter update time and provide the first bounds that subsume (and sometimes improve) the state-of-the-art dynamic vertex cover algorithms. We obtain: (1) (1 + ∊) f-approximation ratio in O(f log2(Cn)/∊3) worst-case update time: No non-trivial worst-case update time was previously known for dynamic set cover. Our bound subsumes and improves by a logarithmic factor the O(log3 n/poly(∊)) worst-case update time for unweighted dynamic vertex cover (i.e., when f = 2 and C = 1) by <PERSON><PERSON><PERSON><PERSON> et al. [SODA'17]. (2) (1 + ∊) f-approximation ratio in O ((f2/∊3) + (f/∊2) log C) amortized update time: This result improves the previous O(f log (Cn)/∊2) update time bound for most values of f in the low-frequency range, i.e. whenever f = o(log n). It is the first that is independent of m and n. It subsumes the constant amortized update time of Bhattacharya and Kulkarni [SODA'19] for unweighted dynamic vertex cover (i.e., when f = 2 and C = 1). These results are achieved by leveraging the approximate complementary slackness and background schedulers techniques. These techniques were used in the local update scheme for dynamic vertex cover. Our main technical contribution is to adapt these techniques within the global update scheme of Bhattacharya et al. [FOCS'19] for the dynamic set cover problem.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.150"}, {"primary_key": "2237294", "vector": [], "sparse_vector": [], "title": "The Fine-Grained Complexity of Computing the Tutte Polynomial of a Linear Matroid.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that computing the Tutte polynomial of a linear matroid of dimension k on kO(1) points over a field of kO(1) elements requires kΩ(k) time unless the #ETH—a counting extension of the Exponential Time Hypothesis of Impaglia<PERSON> and Paturi [CCC 1999] due to <PERSON> et al. [ACM TALG 2014]—is false. This holds also for linear matroids that admit a representation where every point is associated to a vector with at most two nonzero coordinates. Moreover, we also show that the same is true for computing the Tutte polynomial of a binary matroid of dimension k on kO(1) points with at most three nonzero coordinates in each point's vector. These two results stand in sharp contrast to computing the Tutte polynomial of a k-vertex graph (that is, the Tutte polynomial of a graphic matroid of dimension k—which is representable in dimension k over the binary field so that every vector has exactly two nonzero coordinates), which is known to be computable in 2kkO(1) time [<PERSON><PERSON><PERSON> et al., FOCS 2008]. Our lower-bound proofs proceed in three steps:1.a classic connection due to <PERSON><PERSON><PERSON> and <PERSON><PERSON> [1970] between the number of tuples of codewords of full support and the Tutte polynomial of the matroid associated with the code;2.an earlier-established #ETH-hardness of counting the solutions to a bipartite (d, 2)-CSP on n vertices in do(n) time; and3.new embeddings of such CSP instances as questions about codewords of full support in a linear code.Geometrically, our hardness results also establish that it is #ETH-hard to compute the volume of proper hyperplane chambers in time ko(k) for a given arrangement of hyperplanes through the origin of a finite k-dimensional vector space over a kO(1)-element field. We complement these lower bounds with two algorithm designs to form essentially a complexity dichotomy under #ETH. The first design computes the Tutte polynomial of a linear matroid of dimension k on kO(1) points in kO(k) arithmetic operations in the base field. The second design generalizes the Björklund et al. algorithm from the graphic case and runs in qk+1kO(1) time for linear matroids of dimension k defined over the q-element field by kO(1) points with at most two nonzero coordinates each.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.139"}, {"primary_key": "2237295", "vector": [], "sparse_vector": [], "title": "Query strategies for priced information, revisited.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of designing query strategies for priced information, introduced by <PERSON><PERSON><PERSON> et al. In this problem the algorithm designer is given a function f : {0, 1}n → {±1} and a price associated with each of the n coordinates. The goal is to design a query strategy for determining f's value on unknown inputs for minimum cost.Prior works on this problem have focused on specific classes of functions. We analyze a simple and natural strategy that applies to all functions f, and show that its performance relative to the optimal strategy can be expressed in terms of a basic complexity measure of f, its influence. For ∊ ∊ (0, ½), writing opt to denote the expected cost of the optimal strategy that errs on at most an ∊-fraction of inputs, our strategy has expected cost opt · Inf(f)/∊2 and also errs on at most an O(∊)-fraction of inputs. This connection yields new guarantees that complement existing ones for a number of function classes that have been studied in this context, as well as new guarantees for new classes.Finally, we show that improving on the parameters that we achieve will require making progress on the longstanding open problem of properly learning decision trees.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.99"}, {"primary_key": "2237296", "vector": [], "sparse_vector": [], "title": "On the Orbit Closure Containment Problem and Slice Rank of Tensors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the orbit closure containment problem, which, for a given vector and a group orbit, asks if the vector is contained in the closure of the group orbit. Recently, many algorithmic problems related to orbit closures have proved to be quite useful in giving polynomial time algorithms for special cases of the polynomial identity testing problem and several non-convex optimization problems. Answering a question posed by <PERSON><PERSON><PERSON><PERSON>, we show that the algorithmic problem corresponding to the orbit closure containment problem is NP-hard. We show this by establishing a computational equivalence between the solvability of homogeneous quadratic equations and a homogeneous version of the matrix completion problem, while showing that the latter is an instance of the orbit closure containment problem.Secondly, we consider the notion of slice rank of tensors, which was recently introduced by <PERSON>, and has subsequently been used for breakthroughs in several combinatorial problems like capsets, sunflower free sets, tri-colored sum-free sets, and progression-free sets. We show that the corresponding algorithmic problem, which can also be phrased as a problem about union of orbit closures, is also NP-hard, hence answering an open question by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. We show this by using a connection between the slice rank and the size of a minimum vertex cover of a hypergraph revealed by <PERSON> and <PERSON><PERSON>.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.152"}, {"primary_key": "2237297", "vector": [], "sparse_vector": [], "title": "The Impact of Heterogeneity and Geometry on the Proof Complexity of Random Satisfiability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Satisfiability is considered the canonical NP-complete problem and is used as a starting point for hardness reductions in theory, while in practice heuristic SAT solving algorithms can solve large-scale industrial SAT instances very efficiently. This disparity between theory and practice is believed to be a result of inherent properties of industrial SAT instances that make them tractable. Two characteristic properties seem to be prevalent in the majority of real-world SAT instances, heterogeneous degree distribution and locality. To understand the impact of these two properties on SAT, we study the proof complexity of random k-SAT models that allow to control heterogeneity and locality. Our findings show that heterogeneity alone does not make SAT easy as heterogeneous random k-SAT instances have superpolynomial resolution size. This implies intractability of these instances for modern SAT-solvers. On the other hand, modeling locality with an underlying geometry leads to small unsatisfiable subformulas, which can be found within polynomial time.A key ingredient for the result on geometric random k-SAT can be found in the complexity of higher-order Voronoi diagrams. As an additional technical contribution, we show an upper bound on the number of non-empty Voronoi regions, that holds for points with random positions in a very general setting. In particular, it covers arbitrary p-norms, higher dimensions, and weights affecting the area of influence of each point multiplicatively. Our bound is linear in the total weight. This is in stark contrast to quadratic lower bounds for the worst case.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.4"}, {"primary_key": "2237298", "vector": [], "sparse_vector": [], "title": "Optimal Vertex Fault-Tolerant Spanners in Polynomial Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent work has pinned down the existentially optimal size bounds for vertex fault-tolerant spanners: for any positive integer k, every n-node graph has a (2k – 1)-spanner on O(f1–1/kn1+1/k) edges resilient to f vertex faults, and there are examples of input graphs on which this bound cannot be improved. However, these proofs work by analyzing the output spanner of a certain exponential-time greedy algorithm. In this work, we give the first algorithm that produces vertex fault tolerant spanners of optimal size and which runs in polynomial time. Specifically, we give a randomized algorithm which takes Õ (f1–1/kn2+1/k + mf2) time. We also derandomize our algorithm to give a deterministic algorithm with similar bounds. This reflects an exponential improvement in runtime over [Bodwin-Patel PODC '19], the only previously known algorithm for constructing optimal vertex fault-tolerant spanners.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.174"}, {"primary_key": "2237299", "vector": [], "sparse_vector": [], "title": "Twin-width II: small classes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The recently introduced twin-width of a graph G is the minimum integer d such that G has a d-contraction sequence, that is, a sequence of |V(G)| – 1 iterated vertex identifications for which the overall maximum number of red edges incident to a single vertex is at most d, where a red edge appears between two sets of identified vertices if they are not homogeneous in G (not fully adjacent nor fully non-adjacent). We show that if a graph admits a d-contraction sequence, then it also has a linear-arity tree of f(d)-contractions, for some function f. Informally if we accept to worsen the twin-width bound, we can choose the next contraction from a set of Θ(|V(G)|) pairwise disjoint pairs of vertices. This has two main consequences. First it permits to show that every bounded twin-width class is small, i.e., has at most n!cn graphs labeled by [n], for some constant c. This unifies and extends the same result for bounded treewidth graphs [<PERSON><PERSON><PERSON> and <PERSON><PERSON>, JCT '69], proper subclasses of permutations graphs [<PERSON> and <PERSON>, JCTA '04], and proper minor-free classes [<PERSON><PERSON> et al., JCTB '06]. It implies in turn that bounded-degree graphs, interval graphs, and unit disk graphs have unbounded twin-width. The second consequence is an O(log n)-adjacency labeling scheme for bounded twin-width graphs, confirming several cases of the implicit graph conjecture.We then explore the small conjecture that, conversely, every small hereditary class has bounded twin-width. The conjecture passes many tests. Inspired by sorting networks of logarithmic depth, we show that logΘ(log log d) n-subdivisions of Kn (a small class when d is constant) have twin-width at most d. We obtain a rather sharp converse with a surprisingly direct proof: the logd+1 n-subdivision of Kn has twin-width at least d. Secondly graphs with bounded stack or queue number (also small classes) have bounded twin-width. These sparse classes are surprisingly rich since they contain certain (small) classes of expanders. Thirdly we show that cubic expanders obtained by iterated random 2-lifts from K4 [Bilu and Linial, Combinatorica '06] also have bounded twin-width. These graphs are related to so-called separable permutations and also form a small class. We suggest a promising connection between the small conjecture and group theory.Finally we define a robust notion of sparse twin-width. We show that for a hereditary class of bounded twin-width the five following conditions are equivalent: every graph in (1) is Kt,t-free for some fixed t, (2) has an adjacency matrix without a d-by-d division with a 1 entry in each d2 cells for some fixed d, (3) has at most linearly many edges, (4) the subgraph closure of has bounded twin-width, and (5) has bounded expansion. We discuss how sparse classes with similar behavior with respect to clique subdivisions compare to bounded sparse twin-width.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.118"}, {"primary_key": "2237300", "vector": [], "sparse_vector": [], "title": "On the Mysteries of MAX NAE-SAT.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "MAX NAE-SAT is a natural optimization problem, closely related to its better-known relative MAX SAT. The approximability status of MAX NAE-SAT is almost completely understood if all clauses have the same size k, for some k ≥ 2. We refer to this problem as MAX NAE-{k}-SAT. For k = 2, it is essentially the celebrated MAX CUT problem. For k = 3, it is related to the MAX CUT problem in graphs that can be fractionally covered by triangles. For k ≥ 4, it is known that an approximation ratio of , obtained by choosing a random assignment, is optimal, assuming P ≠ NP. For every k ≥ 2, an approximation ratio of at least 7/8 can be obtained for MAX NAE-{k}-SAT. There was some hope, therefore, that there is also a 7/8-approximation algorithm for MAX NAE-SAT, where clauses of all sizes are allowed simultaneously.Our main result is that there is no 7/8-approximation algorithm for MAX NAE-SAT, assuming the unique games conjecture (UGC). In fact, even for almost satisfiable instances of MAX NAE-{3, 5}-SAT (i.e., MAX NAE-SAT where all clauses have size 3 or 5), the best approximation ratio that can be achieved, assuming UGC, is at most .Using calculus of variations, we extend the analysis of <PERSON><PERSON><PERSON> and <PERSON> for MAX CUT to MAX NAE-{3}-SAT. We obtain an optimal algorithm, assuming UGC, for MAX NAE-{3}-SAT, slightly improving on previous algorithms. The approximation ratio of the new algorithm is ≈ 0.9089. This gives a full understanding of MAX NAE-{k}-SAT for every k ≥ 2. Interestingly, the rounding function used by this optimal algorithm is the solution of an integral equation.We complement our theoretical results with some experimental results. We describe an approximation algorithm for almost satisfiable instances of MAX NAE-{3, 5}-SAT with a conjectured approximation ratio of 0.8728, and an approximation algorithm for almost satisfiable instances of MAX NAE-SAT with a conjectured approximation ratio of 0.8698. We further conjecture that these are essentially the best approximation ratios that can be achieved for these problems, assuming the UGC. Somewhat surprisingly, the rounding functions used by these approximation algorithms are non-monotone step functions that assume only the values ±1.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.30"}, {"primary_key": "2237301", "vector": [], "sparse_vector": [], "title": "Coresets for Clustering in Excluded-minor Graphs and Beyond.", "authors": ["<PERSON>", "Shaofeng H.-<PERSON><PERSON> Jiang", "<PERSON>", "<PERSON><PERSON>"], "summary": "Coresets are modern data-reduction tools that are widely used in data analysis to improve efficiency in terms of running time, space and communication complexity. Our main result is a fast algorithm to construct a small coreset for k-Median in (the shortest-path metric of) an excluded-minor graph. Specifically we give the first coreset of size that depends only on k, ∊ and the excluded-minor size, and our running time is quasi-linear (in the size of the input graph).The main innovation in our new algorithm is that is iterative; it first reduces the n input points to roughly O(log n) reweighted points, then to O(log log n), and so forth until the size is independent of n. Each step in this iterative size reduction is based on the importance sampling framework of <PERSON><PERSON><PERSON> and <PERSON> (STOC 2011), with a crucial adaptation that reduces the number of distinct points, by employing a terminal embedding (where low distortion is guaranteed only for the distance from every terminal to all other points). Our terminal embedding is technically involved and relies on shortest-path separators, a standard tool in planar and excluded-minor graphs.Furthermore, our new algorithm is applicable also in Euclidean metrics, by simply using a recent terminal embedding result of <PERSON><PERSON> and <PERSON> (STOC 2019), which extends the Johnson-Lindenstrauss Lemma. We thus obtain an efficient coreset construction in high-dimensional Euclidean spaces, thereby matching and simplifying state-of-the-art results (<PERSON><PERSON> and <PERSON>, FOCS 2018; <PERSON> and <PERSON>, STOC 2020).In addition, we also employ terminal embedding with additive distortion to obtain small coresets in graphs with bounded highway dimension, and use applications of our coresets to obtain improved approximation schemes, e.g., an improved PTAS for planar k-Median via a new centroid set.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.159"}, {"primary_key": "2237302", "vector": [], "sparse_vector": [], "title": "A Fine-Grained Perspective on Approximating Subset Sum and Partition.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Approximating SubsetSum is a classic and fundamental problem in computer science and mathematical optimization. The state-of-the-art approximation scheme for SubsetSum computes a (1 – ∊)-approximation in time [<PERSON><PERSON>, <PERSON><PERSON>'78, <PERSON><PERSON> et al.'97]. In particular, a (1 – 1/n)-approximation can be computed in time .We establish a connection to Min-Plus-Convolution, a problem that is of particular interest in fine-grained complexity theory and can be solved naively in time . Our main result is that computing a (1 – 1/n)-approximation for SubsetSum is subquadratically equivalent to Min-Plus-Convolution. Thus, assuming the Min-Plus-Convolution conjecture from fine-grained complexity theory, there is no approximation scheme for SubsetSum with strongly subquadratic dependence on n and 1/∊. In the other direction, our reduction allows us to transfer known lower order improvements from Min-Plus-Convolution to SubsetSum, which yields a mildly subquadratic randomized approximation scheme. This adds the first approximation problem to the list of problems that are equivalent to Min-Plus-Convolution.For the related Partition problem, an important special case of SubsetSum, the state of the art is a randomized approximation scheme running in time [<PERSON><PERSON> et al.'19]. We adapt our reduction from SubsetSum to Min-Plus-Convolution to obtain a related reduction from Partition to Min-Plus-Convolution. This yields an improved approximation scheme for Partition running in time . Our algorithm is the first deterministic approximation scheme for Partition that breaks the quadratic barrier.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.108"}, {"primary_key": "2237303", "vector": [], "sparse_vector": [], "title": "On Near-Linear-Time Algorithms for Dense Subset Sum.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In the Subset Sum problem we are given a set of n positive integers X and a target t and are asked whether some subset of X sums to t. Natural parameters for this problem that have been studied in the literature are n and t as well as the maximum input number mxx and the sum of all input numbers Σx. In this paper we study the dense case of Subset Sum, where all these parameters are polynomial in n. In this regime, standard pseudo-polynomial algorithms solve Subset Sum in polynomial time nO(1).Our main question is: When can dense Subset Sum be solved in near-linear time Õ(n)? We provide an essentially complete dichotomy by designing improved algorithms and proving conditional lower bounds, thereby determining essentially all settings of the parameters n, t, mx x, Σx for which dense Subset Sum is in time Õ(n). For notational convenience we assume without loss of generality that t ≥ mxx (as larger numbers can be ignored) and t ≤ Σx/2 (using symmetry). Then our dichotomy reads as follows:•By reviving and improving an additive-combinatorics-based approach by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [SICOMP'91], we show that Subset Sum is in near-linear time Õ(n) if t » mxxΣx/n2.•We prove a matching conditional lower bound: If Subset Sum is in near-linear time for any setting with t « mxxΣx/n2, then the Strong Exponential Time Hypothesis and the Strong k-Sum Hypothesis fail.We also generalize our algorithm from sets to multi-sets, albeit with non-matching upper and lower bounds.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.107"}, {"primary_key": "2237304", "vector": [], "sparse_vector": [], "title": "Online Multiserver Convex Chasing and Optimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the problem of k-chasing of convex functions, a simultaneous generalization of both the famous k-server problem in ℝd, and of the problem of chasing convex bodies and functions. Aside from fundamental interest in this general form, it has natural applications to online k-clustering problems with objectives such as k-median or k-means. We show that this problem exhibits a rich landscape of behavior. In general, if both k > 1 and d > 1 there does not exist any online algorithm with bounded competitiveness. By contrast, we exhibit a class of nicely behaved functions (which include in particular the above-mentioned clustering problems), for which we show that competitive online algorithms exist, and moreover with dimension-free competitive ratio. We also introduce a parallel question of top-k action regret minimization in the realm of online convex optimization. There, too, a much rougher landscape emerges for k > 1. While it is possible to achieve vanishing regret, unlike the top-one action case the rate of vanishing does not speed up for strongly convex functions. Moreover, vanishing regret necessitates both intractable computations and randomness. Finally we leave open whether almost dimension-free regret is achievable for k > 1 and general convex losses. As evidence that it might be possible, we prove dimension-free regret for linear losses via an information-theoretic argument.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.125"}, {"primary_key": "2237305", "vector": [], "sparse_vector": [], "title": "Approximating (k, ℓ-Median Clustering for Polygonal Curves.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In 2015, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> introduced the (k, ℓ)-median problem for clustering polygonal curves under the Fréchet distance. Given a set of input curves, the problem asks to find k median curves of at most ℓ vertices each that minimize the sum of Fréchet distances over all input curves to their closest median curve. A major shortcoming of their algorithm is that the input curves are restricted to lie on the real line. In this paper, we present a randomized bicriteria-approximation algorithm that works for polygonal curves in ℝd and achieves approximation factor (1 + ∊) with respect to the clustering costs. The algorithm has worst-case running-time linear in the number of curves, polynomial in the maximum number of vertices per curve, i.e. their complexity, and exponential in d, ℓ, ∊ and δ, i.e., the failure probability. We achieve this result through a shortcutting lemma, which guarantees the existence of a polygonal curve with similar cost as an optimal median curve of complexity ℓ, but of complexity at most 2ℓ – 2, and whose vertices can be computed efficiently. We combine this lemma with the superset-sampling technique by <PERSON> et al. to derive our clustering result. In doing so, we describe and analyze a generalization of the algorithm by <PERSON><PERSON><PERSON> et al., which may be of independent interest.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.160"}, {"primary_key": "2237306", "vector": [], "sparse_vector": [], "title": "Lee<PERSON><PERSON> zeros and the complexity of the ferromagnetic Ising Model on bounded-degree graphs.", "authors": ["<PERSON><PERSON><PERSON>s", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the computational complexity of approximating the partition function of the ferromagnetic Ising model in the Lee-Yang circle of zeros given by |λ| = 1, where λ is the external field of the model.Complex-valued parameters for the <PERSON><PERSON> model are relevant for quantum circuit computations and phase transitions in statistical physics, but have also been key in the recent deterministic approximation scheme for all |λ| ≠ 1 by <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Here, we focus on the unresolved complexity picture on the unit circle, and on the tantalising question of what happens in the circular arc around λ = 1, where on one hand the classical algorithm of <PERSON><PERSON><PERSON> and <PERSON> gives a randomised approximation scheme on the real axis suggesting tractability, and on the other hand the presence of Lee<PERSON><PERSON> zeros alludes to computational hardness.Our main result establishes a sharp computational transition at the point λ = 1; in fact, our techniques apply more generally to the whole unit circle |λ| = 1. We show #P-hardness for approximating the partition function on graphs of maximum degree Δ when b, the edge-interaction parameter, is in the interval and λ is a non-real on the unit circle. This result contrasts with known approximation algorithms when , and shows that the Lee-Yang circle of zeros is computationally intractable, even on bounded-degree graphs.Our inapproximability result is based on constructing rooted tree gadgets via a detailed understanding of the underlying dynamical systems, which are further parameterised by the degree of the root. The ferromagnetic Ising model has radically different behaviour than previously considered anti-ferromagnetic models, and showing our #P-hardness results in the whole Lee-Yang circle requires a new high-level strategy to construct the gadgets. To this end, we devise an elaborate inductive procedure to construct the required gadgets by taking into account the dependence between the degree of the root of the tree and the magnitude of the derivative at the fixpoint of the corresponding dynamical system.The full version (with all proofs) is available on arXiv at arxiv.org/abs/2006.14828.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.91"}, {"primary_key": "2237307", "vector": [], "sparse_vector": [], "title": "New Planar P-time Computable Six-Vertex Models and a Complete Complexity Classification.", "authors": ["<PERSON><PERSON><PERSON>", "Zhiguo Fu", "<PERSON><PERSON>"], "summary": "We discover new P-time computable six-vertex models on planar graphs beyond <PERSON><PERSON><PERSON><PERSON>'s algorithm for counting planar perfect matchings.∗ We further prove that there are no more: Together, they exhaust all P-time computable six-vertex models on planar graphs, assuming #P is not P. This leads to the following exact complexity classification: For every parameter setting in ℂ for the six-vertex model, the partition function is either (1) computable in P-time for every graph, or (2) #P-hard for general graphs but computable in P-time for planar graphs, or (3) #P-hard even for planar graphs. The classification has an explicit criterion. The new P-time cases in (2) provably cannot be subsumed by <PERSON><PERSON><PERSON><PERSON>'s algorithm. They are obtained by a non-local connection to #CSP, defined in terms of a \"loop space\". This is the first substantive advance toward a planar Holant classification with not necessarily symmetric constraints. We introduce Möbius transformation on ℂ as a powerful new tool in hardness proofs for counting problems.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.93"}, {"primary_key": "2237308", "vector": [], "sparse_vector": [], "title": "On Multi-Dimensional Gains from Trade Maximization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study gains from trade in multi-dimensional two-sided markets. Specifically, we focus on a setting with n heterogeneous items, where each item is owned by a different seller i, and there is a constrained-additive buyer with feasibility constraint ℱ. Multi-dimensional settings in one-sided markets, e.g. where a seller owns multiple heterogeneous items but also is the mechanism designer, are well-understood. In addition, single-dimensional settings in two-sided markets, e.g. where a buyer and seller each seek or own a single item, are also well-understood. Multi-dimensional two-sided markets, however, encapsulate the major challenges of both lines of work: optimizing the sale of heterogeneous items, ensuring incentive-compatibility among both sides of the market, and enforcing budget balance. We present, to the best of our knowledge, the first worst-case approximation guarantee for gains from trade in a multi-dimensional two-sided market.Our first result provides an O(log(1/r))-approximation to the first-best gains from trade for a broad class of downward-closed feasibility constraints (such as matroid, matching, knapsack, or the intersection of these). Here r is the minimum probability over all items that a buyer's value for the item exceeds the seller's cost. Our second result removes the dependence on r and provides an unconditional O(log n)-approximation to the second-best gains from trade. We extend both results for a general constrained-additive buyer, losing another O(log n)-factor en-route. The first result is achieved using a fixed posted price mechanism, and the analysis involves a novel application of the prophet inequality or a new concentration inequality. Our second result follows from a stitching lemma that allows us to upper bound the second-best gains from trade by the first-best gains from trade from the \"likely to trade\" items (items with trade probability at least 1/n) and the optimal profit from selling the \"unlikely to trade\" items. We can obtain an O(log n)-approximation to the first term by invoking our O(log(1/r))-approximation on the \"likely to trade\" items. We introduce a generalization of the fixed posted price mechanism—seller adjusted posted price—to obtain an O(log n)-approximation to the optimal profit for the \"unlikely to trade\" items. Unlike fixed posted price mechanisms, not all seller adjusted posted price mechanisms are incentive compatible and budget balanced. We develop a new argument based on \"allocation coupling\" to show the seller adjusted posted price mechanism used in our approximation is indeed budget balanced and incentive-compatible.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.67"}, {"primary_key": "2237309", "vector": [], "sparse_vector": [], "title": "An FPTAS for the square lattice six-vertex and eight-vertex models at low temperatures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give the first efficient approximate counting and sampling algorithms for the six-vertex model and the eight-vertex model on regions of the square lattice ℤ2 in the low temperature regime. All previous algorithms for these problems are for high temperature settings, and rely on the rapid mixing of Markov chains. We prove that these natural Markov chains are torpidly mixing (exponentially slowly) in the low temperature settings. Rather than depending on rapid mixing MCMC, our algorithms are obtained by defining a special edge-2-coloring model, and showing an equivalence to (a linear combination of) abstract polymer models. We then prove the convergence of the cluster expansion of these polymer models. This allows us to employ the approach recently developed by <PERSON><PERSON>, <PERSON>, and <PERSON> [25]. This combined with <PERSON><PERSON><PERSON>'s method [3, 42] via Taylor expansion (zero-free region of log partition function) gives the approximate counting and sampling algorithms. Significantly, these results provide the first counting problems that admit a fully polynomial time approximation scheme (FPTAS) on square lattice graphs but NP-hard to approximate even on bipartite graphs (rather than the weaker #BIS-hardness.)", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.92"}, {"primary_key": "2237310", "vector": [], "sparse_vector": [], "title": "An Efficient ∊-BIC to BIC Transformation and Its Application to Black-Box Reduction in Revenue Maximization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the black-box reduction from multidimensional revenue maximization to virtual welfare maximization. <PERSON><PERSON> et al. [12, 13, 14, 15] show a polynomial-time approximation-preserving reduction, however, the mechanism produced by their reduction is only approximately Bayesian incentive compatible (∊-BIC). We provide two new polynomial time transformations that convert any ∊-BIC mechanism to an exactly BIC mechanism with only a negligible revenue loss.•Our first transformation applies to any mechanism design setting with downward-closed outcome space and only requires sample access to the agents' type distributions.•Our second transformation applies to the fully general outcome space, removing the downward-closed assumption, but requires full access to the agents' type distributions.Both transformations only require query access to the original ∊-BIC mechanism. Other ∊-BIC to BIC transformations for revenue exist in the literature [23, 36, 18] but all require exponential time to run in both of the settings we consider. As an application of our transformations, we improve the reduction by <PERSON><PERSON> et al. [12, 13, 14, 15] to generate an exactly BIC mechanism.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.81"}, {"primary_key": "2237311", "vector": [], "sparse_vector": [], "title": "Random Restrictions of High Dimensional Distributions and Uniformity Testing with Subcube Conditioning.", "authors": ["Clément L. <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We give a nearly-optimal algorithm for testing uniformity of distributions supported on {–1, 1}n, which makes many queries to a subcube conditional sampling oracle (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> (2018)). The key technical component is a natural notion of random restrictions for distributions on {–1, 1}n, and a quantitative analysis of how such a restriction affects the mean vector of the distribution. Along the way, we consider the problem of mean testing with independent samples and provide a nearly-optimal algorithm.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.21"}, {"primary_key": "2237312", "vector": [], "sparse_vector": [], "title": "Tight Distributed Listing of Cliques.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Much progress has recently been made in understanding the complexity landscape of subgraph finding problems in the CONGEST model of distributed computing. However, so far, very few tight bounds are known in this area. For triangle (i.e., 3-clique) listing, an optimal Õ(n1/3)-round distributed algorithm has been constructed by <PERSON> et al. [SODA 2019, PODC 2019]. Recent works of <PERSON> et al. [DISC 2019] and of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. [PODC 2020] have shown sublinear algorithms for Kp-listing, for each p ≥ 4, but still leaving a significant gap between the upper bounds and the known lower bounds of the problem.In this paper, we completely close this gap. We show that for each p ≥ 4, there is an Õ(n1–2/p)-round distributed algorithm that lists all p-cliques Kp in the communication network. Our algorithm is optimal up to a polylogarithmic factor, due to the -round lower bound of <PERSON> et al. [SPAA 2018], which holds even in the CONGESTED CLIQUE model. Together with the triangle-listing algorithm by <PERSON> et al. [SODA 2019, PODC 2019], our result thus shows that the round complexity of Kp-listing, for all p, is the same in both the CONGEST and CONGESTED CLIQUE models, at rounds.For p = 4, our result additionally matches the lower bound for K4-detection by <PERSON><PERSON><PERSON><PERSON> and <PERSON> [DISC 2018], implying that the round complexities for detection and listing of K4 are equivalent in the CONGEST model.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.171"}, {"primary_key": "2237313", "vector": [], "sparse_vector": [], "title": "Approximating the Median under the Ulam Metric.", "authors": ["Diptarka Chakraborty", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study approximation algorithms for variants of the median string problem, which asks for a string that minimizes the sum of edit distances from a given set of m strings of length n. Only the straightforward 2-approximation is known for this NP-hard problem. This problem is motivated e.g. by computational biology, and belongs to the class of median problems (over different metric spaces), which are fundamental tasks in data analysis.Our main result is for the Ulam metric, where all strings are permutations over [n] and each edit operation moves a symbol (deletion plus insertion). We devise for this problem an algorithms that breaks the 2-approximation barrier, i.e., computes a (2 – δ)-approximate median permutation for some constant δ > 0 in time Õ(nm2 + n3). We further use these techniques to achieve a (2 – δ) approximation for the median string problem in the special case where the median is restricted to length n and the optimal objective is large Ω(mn).We also design an approximation algorithm for the following probabilistic model of the Ulam median: the input consists of m perturbations of an (unknown) permutation x, each generated by moving every symbol to a random position with probability (a parameter) ∊ > 0. Our algorithm computes with high probability a (1 + o(1/∊))-approximate median permutation in time O(mn2 + n3).", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.48"}, {"primary_key": "2237314", "vector": [], "sparse_vector": [], "title": "Vertex Sparsification for Edge Connectivity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bundit Laekhanukit", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Graph compression or sparsification is a basic information-theoretic and computational question. A major open problem in this research area is whether (1 + ∊)-approximate cut-preserving vertex sparsifiers with size close to the number of terminals exist. As a step towards this goal, we study a thresholded version of the problem: for a given parameter c, find a smaller graph, which we call connectivity-c mimicking network, which preserves connectivity among k terminals exactly up to the value of c. We show that connectivity-c mimicking networks with O(kc4) edges exist and can be found in time m(c log n)O(c). We also give a separate algorithm that constructs such graphs with k · O(c)2c edges in time mcO(c) logO(1) n.These results lead to the first data structures for answering fully dynamic offline c-edge-connectivity queries for c ≥ 4 in polylogarithmic time per query, as well as more efficient algorithms for survivable network design on bounded treewidth graphs.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.74"}, {"primary_key": "2237315", "vector": [], "sparse_vector": [], "title": "Coloring and Maximum Weight Independent Set of Rectangles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In 1960, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> proved that every intersection graph of axis-parallel rectangles in the plane admits an O(ω2)-coloring, where ω is the maximum size of a clique. We present the first asymptotic improvement over this six-decade-old bound, proving that every such graph is O(ω log ω)-colorable and presenting a polynomial-time algorithm that finds such a coloring. This improvement leads to a polynomial-time O(log log n)-approximation algorithm for the maximum weight independent set problem in axis-parallel rectangles, which improves on the previous approximation ratio of .", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.54"}, {"primary_key": "2237316", "vector": [], "sparse_vector": [], "title": "How to Morph Graphs on the Torus.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present the first algorithm to morph graphs on the torus. Given two isotopic essentially 3-connected embeddings of the same graph on the Euclidean flat torus, where the edges in both drawings are geodesics, our algorithm computes a continuous deformation from one drawing to the other, such that all edges are geodesics at all times. Previously even the existence of such a morph was not known. Our algorithm runs in O(n1+ω/2) time, where ω is the matrix multiplication exponent, and the computed morph consists of O(n) parallel linear morphing steps. Existing techniques for morphing planar straight-line graphs do not immediately generalize to graphs on the torus; in particular, <PERSON>' original 1944 proof and its more recent improvements rely on the fact that every planar graph contains a vertex of degree at most 5. Our proof relies on a subtle geometric analysis of 6-regular triangulations of the torus. We also make heavy use of a natural extension of <PERSON><PERSON>'s spring embedding theorem to torus graphs.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.164"}, {"primary_key": "2237317", "vector": [], "sparse_vector": [], "title": "(Near-)Linear-Time Randomized Algorithms for Row Minima in Monge Partial Matrices and Related Problems.", "authors": ["<PERSON>"], "summary": "We revisit classical problems about searching in totally monotone and Monge matrices, which have many applications in computational geometry and other areas. We present a number of new results, including the following:•A randomized algorithm that finds the row minima in an n × n Monge staircase matrix in O(n) expected time; this improves a longstanding O(nα(n)) bound by <PERSON><PERSON><PERSON> and <PERSON> (1990) for totally monotone staircase matrices.•A randomized algorithm that reports the K smallest elements (in an arbitrary order) in an n × n Monge (complete or staircase) matrix in O(n + K) expected time; this improves and extends a previous O(n + K log n) algorithm by <PERSON><PERSON><PERSON> and <PERSON> [SODA'90].•A randomized algorithm that reports the K smallest elements (in an arbitrary order) in an n × n totally monotone (complete) matrix in O(n + K log∗ n) expected time.•A randomized algorithm that reports the ki smallest elements in the i-th row, for every i, in an n × n totally monotone (complete) matrix in O((n + K) log∗ n) expected time, where K = Σi ki.•A randomized algorithm that finds the row minima in an n × n totally monotone \"v-matrix\" in O(nα(n) log∗ n log log n) expected time; this answers an open question by <PERSON><PERSON><PERSON> [<PERSON>OD<PERSON>'90]. The log∗ n factor can be removed in the Monge case.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.88"}, {"primary_key": "2237318", "vector": [], "sparse_vector": [], "title": "Near-Optimal Randomized Algorithms for Selection in Totally Monotone Matrices.", "authors": ["<PERSON>"], "summary": "We revisit classical problems about searching in totally monotone matrices, which have many applications in computational geometry and other areas. In a companion paper, we gave new (near-)linear-time algorithms for a number of such problems. In the present paper, we describe new sub-quadratic results for more basic problems, including the following:•A randomized algorithm to select the K-th smallest element in an n × n totally monotone matrix in O(n4/3 polylog n) expected time; this improves previous O(n3/2 polylog n) algorithms by <PERSON><PERSON> and <PERSON> [SODA'92], <PERSON><PERSON> et al. (1993), and <PERSON><PERSON><PERSON> and <PERSON> (1996).•A near-matching lower bound of Ω(n4/3) for the problem (which holds even for Monge matrices).•A similar result for selecting the ki-th smallest in the i-th row for all i.•In the case when all ki's are the same, an improvement of the running time to O(n6/5 polylog n).•Variants of all these bounds that are sensitive to K (or Σi ki).These matrix searching problems are intimately related to problems about arrangements of pseudo-lines. In particular, our selection algorithm implies an O(n4/3 polylog n) algorithm for computing incidences between n points and n pseudo-lines in the plane. This improves, extends, and simplifies a previous method by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [SODA'02].", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.89"}, {"primary_key": "2237319", "vector": [], "sparse_vector": [], "title": "Min-max Partitioning of Hypergraphs and Symmetric Submodular Functions.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the complexity of minmax partitioning of graphs, hypergraphs and (symmetric) submodular functions. Our main result is an algorithm for the problem of partitioning the ground set of a given symmetric submodular function f : 2V → ℝ into k non-empty parts V1, V2, …, Vk to minimize . Our algorithm runs in time, where n = |V| and T is the time to evaluate f on a given set; hence. this yields a polynomial time algorithm for any fixed k in the evaluation oracle model. As an immediate corollary, for any fixed k, there is a polynomial-time algorithm for the problem of partitioning the vertex set of a given hypergraph H = (V, E) into k non-empty parts to minimize the maximum capacity of the parts. The complexity of this problem. termed Minmax-Hypergraph-k-Part, was raised by <PERSON><PERSON> in 1973 [16]. In contrast to our positive result, the reduction in [6] implies that when k is part of the input, Minmax-Hypergraph-k-Part is hard to approximate to within an almost polynomial factor under the Exponential Time Hypothesis (ETH).", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.64"}, {"primary_key": "2237320", "vector": [], "sparse_vector": [], "title": "Competitive Allocation of a Mixed Manna.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the fair division problem of allocating a mixed manna under additively separable piecewise linear concave (SPLC) utilities. A mixed manna contains goods that everyone likes and bads that everyone dislikes, as well as items that some like and others dislike. The seminal work of <PERSON><PERSON><PERSON><PERSON><PERSON> et al. [14] argue why allocating a mixed manna is genuinely more complicated than a good or a bad manna, and why competitive equilibrium is the best mechanism. They also provide the existence of equilibrium and establish its peculiar properties (e.g., non-convex and disconnected set of equilibria even under linear utilities), but leave the problem of computing an equilibrium open.Our main result is a simplex-like algorithm based on <PERSON><PERSON><PERSON>'s scheme for computing a competitive allocation of a mixed manna under SPLC utilities, a strict generalization of linear. Experimental results on randomly generated instances suggest that our algorithm will be fast in practice. The problem is known to be PPAD-hard for the case of good manna [24], and we also show a similar result for the case of bad manna. Given these PPAD-hardness results, designing such an algorithm is the only non-enumerative option known.Our algorithm also yields several new structural properties as simple corollaries. We obtain a (constructive) proof of existence for a far more general setting, membership of the problem in PPAD, rational-valued solution, and odd number of solutions property. The last property also settles the conjecture of [14] in the affirmative.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.85"}, {"primary_key": "2237321", "vector": [], "sparse_vector": [], "title": "Optimal Girth Approximation for Dense Directed Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we provide a Õ(n2) time algorithm that computes a 2-multiplicative approximation of the girth of an n-node m-edge directed graph with non-negative edge weights. We also provide an additional algorithm that computes a 2-multiplicative approximation of the girth in time 1. Our results naturally provide algorithms for improved constructions of 4-roundtrip spanners, the analog of spanners in directed graphs.Our algorithm is optimal (up to a log n factor) for dense graphs with m = Θ(n2). For comparison, previously, the best approximation ratio with a similar running time for dense graphs was O(log n log log n) [1]. Moreover, unlike previous algorithms, our algorithm neither assumes integer weights, nor does it depend on the maximum edge weight of the graph.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.19"}, {"primary_key": "2237322", "vector": [], "sparse_vector": [], "title": "Incremental Single Source Shortest Paths in Sparse Digraphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a directed graph G = (V, E, ω) with positive integer edge weights that undergoes a sequence of edge insertions, we are interested in maintaining approximate single-source shortest paths in the incremental graph G. In a very recent paper, [<PERSON><PERSON><PERSON> et al., 2020] proposed a deterministic algorithm for this problem with Õ(n2 log W) total update time, where n = |V| and W denotes the maximum edge weight. When the underlying graph is super dense, namely, the total number of insertions m is , their upper bound is essentially optimal. For sparse graphs, the only known result is due to [<PERSON><PERSON><PERSON> et al., 2014], whose algorithm is randomized and works in Õ(mn0.9 log W) total update time under the assumption of oblivious non-adaptive adversary.In this work, we provide two algorithms for this problem when the graph is sparse. The first one is a simple deterministic algorithm with Õ(m5/3 log W) total update time. The second one is a randomized algorithm with Õ ((mn1/2 + m7/5) log W) total update time, which improves over both previous results when m = O(n1.42); moreover, this randomized algorithm plays against adaptive adversaries. Our algorithms are the first to break the O(mn) bound with adaptive adversaries for sparse graphs.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.146"}, {"primary_key": "2237323", "vector": [], "sparse_vector": [], "title": "Efficient fully dynamic elimination forests with applications to detecting long paths and cycles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a data structure that in a dynamic graph of treedepth at most d, which is modified over time by edge insertions and deletions, maintains an optimum-height elimination forest. The data structure achieves worst-case update time , which matches the best known parameter dependency in the running time of a static fpt algorithm for computing the treedepth of a graph. This improves a result of <PERSON><PERSON> et al. [ESA 2014], who for the same problem achieved update time f(d) for some non-elementary (i.e. tower-exponential) function f. As a by-product, we improve known upper bounds on the sizes of minimal obstructions for having treedepth d from doubly-exponential in d to dO(d).As applications, we design new fully dynamic parameterized data structures for detecting long paths and cycles in general graphs. More precisely, for a fixed parameter k and a dynamic graph G, modified over time by edge insertions and deletions, our data structures maintain answers to the following queries:•Does G contain a simple path on k vertices?•Does G contain a simple cycle on at least k vertices? In the first case, the data structure achieves amortized update time . In the second case, the amortized update time is . In both cases we assume access to a dictionary on the edges of G.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.50"}, {"primary_key": "2237324", "vector": [], "sparse_vector": [], "title": "Polynomial-time trace reconstruction in the smoothed complexity model.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the trace reconstruction problem, an unknown source string x ∊ {0, 1}n is sent through a probabilistic deletion channel which independently deletes each bit with probability δ and concatenates the surviving bits, yielding a trace of x. The problem is to reconstruct x given independent traces. This problem has received much attention in recent years both in the worst-case setting where x may be an arbitrary string in {0, 1}n [6, 19, 7, 8, 4] and in the average-case setting where x is drawn uniformly at random from {0, 1}n [21, 9, 8, 4].This paper studies trace reconstruction in the smoothed analysis setting, in which a \"worst-case\" string xworst is chosen arbitrarily from {0, 1}n, and then a perturbed version x of xworst is formed by independently replacing each coordinate by a uniform random bit with probability σ. The problem is to reconstruct x given independent traces from it.Our main result is an algorithm which, for any constant perturbation rate 0 < σ < 1 and any constant deletion rate 0 < δ < 1, uses poly(n) running time and traces and succeeds with high probability in reconstructing the string x. This stands in contrast with the worst-case version of the problem, for which the best known sample complexity is exp(Õ(n1/5)) [5], a recent improvement on exp(O(n1/3)) [6, 19].Our approach is based on reconstructing x from the multiset of its short subwords and is quite different from previous algorithms for either the worst-case or average-case versions of the problem. The heart of our work is a new poly(n)-time procedure for reconstructing the multiset of all O(log n)-length subwords of any source string x ∊ {0, 1}n given access to traces of x.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.5"}, {"primary_key": "2237325", "vector": [], "sparse_vector": [], "title": "Rapid Mixing for Colorings via Spectral Independence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The spectral independence approach of <PERSON><PERSON> et al. (2020) utilized recent results on high-dimensional expanders of <PERSON><PERSON> and <PERSON> (2020) and established rapid mixing of the Glauber dynamics for the hard-core model defined on weighted independent sets. We develop the spectral independence approach for colorings, and obtain new algorithmic results for the corresponding counting/sampling problems.Let α∗ ≈ 1.763 denote the solution to exp(1/x) = x and let α > α∗. We prove that, for any triangle-free graph G = (V, E) with maximum degree Δ, for all q ≥ αΔ + 1, the mixing time of the Glauber dynamics for q-colorings is polynomial in n = |V|, with the exponent of the polynomial independent of Δ and q. In comparison, previous approximate counting results for colorings held for a similar range of q (asymptotically in Δ) but with larger girth requirement or with a running time where the polynomial exponent depended on Δ and q (exponentially). One further feature of using the spectral independence approach to study colorings is that it avoids many of the technical complications in previous approaches caused by coupling arguments or by passing to the complex plane; the key improvement on the running time is based on relatively simple combinatorial arguments which are then translated into spectral bounds.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.94"}, {"primary_key": "2237326", "vector": [], "sparse_vector": [], "title": "Efficient Linear and Affine Codes for Correcting Insertions/Deletions.", "authors": ["<PERSON><PERSON>", "<PERSON>en<PERSON><PERSON>wami", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper studies linear and affine error-correcting codes for correcting synchronization errors such as insertions and deletions. We call such codes linear/affine insdel codes.Linear codes that can correct even a single deletion are limited to have information rate at most 1/2 (achieved by the trivial 2-fold repetition code). Previously it was (erroneously) reported that more generally no non-trivial linear codes correcting k deletions exist, i.e., that the (k + 1)-fold repetition codes and its rate of 1/(k + 1) are basically optimal for any k. We disprove this and show the existence of binary linear codes of length n and rate just below 1/2 capable of correcting Ω(n) insertions and deletions. This identifies rate 1/2 as a sharp threshold for recovery from deletions for linear codes, and reopens the quest for a better understanding of the capabilities of linear codes for correcting insertions/deletions.We prove novel outer bounds and existential inner bounds for the rate vs. (edit) distance trade-off of linear insdel codes. We complement our existential results with an efficient synchronization-string-based transformation that converts any asymptotically-good linear code for Hamming errors into an asymptotically-good linear code for insdel errors. Lastly we show that the ½-rate limitation does not hold for affine codes by giving an explicit affine code of rate 1 – ∊ which can efficiently correct a constant fraction of insdel errors.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.1"}, {"primary_key": "2237327", "vector": [], "sparse_vector": [], "title": "Efficient Document Exchange and Error Correcting Codes with Asymmetric Information.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study two fundamental problems in communication, Document Exchange (DE) and Error Correcting Code (ECC). In the first problem, two parties hold two strings, and one party tries to learn the other party's string through communication. In the second problem, one party tries to send a message to another party through a noisy channel, by adding some redundant information to protect the message. Two important goals in both problems are to minimize the communication complexity or redundancy, and to design efficient protocols or codes.Both problems have been studied extensively. In this paper we study whether asymmetric partial information can help in these two problems. We focus on the case of Hamming distance/errors, and the asymmetric partial information is modeled by one party having a vector of disjoint subsets S = (S1, …, St) of indices and a vector of integers k = (k1, …, kt), such that in each Si the Hamming distance/errors is at most ki. To our knowledge, no previous work has studied this problem systematically. We establish both lower bounds and upper bounds in this model, and provide efficient randomized constructions that achieve a min{O(t2), O((log log n)2)} factor within the optimum, with almost linear running time.We further show a connection between the above document exchange problem and the problem of document exchange under edit distance, and use our techniques to give an efficient randomized protocol with optimal communication complexity and exponentially small error for the latter. This improves the previous result by Ha<PERSON><PERSON> [20] (FOCS'19), which has polynomially large error; and that by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> [8] (FOCS'16), which is only optimal for a limited range of parameters. Our techniques are based on a generalization of the celebrated expander codes by <PERSON>pser and <PERSON><PERSON><PERSON> [36], which may be of independent interests.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.144"}, {"primary_key": "2237328", "vector": [], "sparse_vector": [], "title": "Deterministic Algorithms for Decremental Shortest Paths via Layered Core Decomposition.", "authors": ["<PERSON>", "Thatchaphol <PERSON>"], "summary": "In the decremental single-source shortest paths (SSSP) problem, the input is an undirected graph G = (V, E) with n vertices and m edges undergoing edge deletions, together with a fixed source vertex s ∊ V. The goal is to maintain a data structure that supports shortest-path queries: given a vertex v ∊ V, quickly return an (approximate) shortest path from s to v. The decremental all-pairs shortest paths (APSP) problem is defined similarly, but now the shortest-path queries are allowed between any pair of vertices of V.Both problems have been studied extensively since the 80's, and algorithms with near-optimal total update time and query time have been discovered for them. Unfortunately, all these algorithms are randomized and, more importantly, they need to assume an oblivious adversary – a drawback that prevents them from being used as subroutines in several known algorithms for classical static problems. In this paper, we provide new deterministic algorithms for both problems, which, by definition, can handle an adaptive adversary.Our first result is a deterministic algorithm for the decremental SSSP problem on weighted graphs with O(n2+o(1)) total update time, that supports (1 + ∊)-approximate shortest-path queries, with query time O(|P| · no(1)), where P is the returned path. This is the first (1 + ∊)-approximation adaptive-update algorithm supporting shortest-path queries in time below O(n), that breaks the O(mn) total update time bound of the classical algorithm of <PERSON> and <PERSON><PERSON><PERSON> from 1981. Previously, <PERSON> and <PERSON><PERSON><PERSON> [STOC'16, ICALP'17] provided a Õ(n2)-time deterministic algorithm that supports approximate distance queries, but unfortunately the algorithm cannot return the approximate shortest paths. Chuzhoy and Khanna [STOC'19] showed an O(n2+o(1))-time randomized algorithm for SSSP that supports approximate shortest-path queries in the adaptive adversary regime, but their algorithm only works in the restricted setting where only vertex deletions, and not edge deletions are allowed, and it requires Ω(n) time to respond to shortest-path queries.Our second result is a deterministic algorithm for the decremental APSP problem on unweighted graphs that achieves total update time O(n2.5+δ), for any constant δ > 0, supports approximate distance queries in O(log log n) time, and supports approximate shortest-path queries in time O(|E(P)| · no(1)), where P is the returned path; the algorithm achieves an O(1)-multiplicative and no(1)-additive approximation on the path length. All previous algorithms for APSP either assume an oblivious adversary or have an Ω(n3) total update time when m = Ω(n2), even if an o(n)-multiplicative approximation is allowed.To obtain both our results, we improve and generalize the layered core decomposition data structure introduced by Chuzhoy and Khanna to be nearly optimal in terms of various parameters, and introduce a new generic approach of rooting Even-Shiloach trees at expander sub-graphs of the given graph. We believe both these technical tools to be interesting in their own right and anticipate them to be useful for designing future dynamic algorithms that work against an adaptive adversary.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.147"}, {"primary_key": "2237329", "vector": [], "sparse_vector": [], "title": "On Approximability of Clustering Problems Without Candidate Centers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> C. S.", "<PERSON><PERSON><PERSON><PERSON> Lee"], "summary": "The k-means objective is arguably the most widely-used cost function for modeling clustering tasks in a metric space. In practice and historically, k-means is thought of in a continuous setting, namely where the centers can be located anywhere in the metric space. For example, the popular Lloyd's heuristic locates a center at the mean of each cluster.Despite persistent efforts on understanding the approximability of k-means, and other classic clustering problems such as k-median and k-minsum, our knowledge of the hardness of approximation factors of these problems remains quite poor. In this paper, we significantly improve upon the hardness of approximation factors known in the literature for these objectives. We show that if the input lies in a general metric space, it is NP-hard to approximate:•Continuous k-median to a factor of 2 – o(1); this improves upon the previous inapproximability factor of 1.36 shown by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (<PERSON><PERSON> '99).•Continuous k-means to a factor of 4–o(1); this improves upon the previous inapproximability factor of 2.10 shown by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (<PERSON><PERSON> '99).•k-minsum to a factor of 1.415; this improves upon the APX-hardness shown by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (SODA '03).Our results shed new and perhaps counter-intuitive light on the differences between clustering problems in the continuous setting versus the discrete setting (where the candidate centers are given as part of the input).", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.156"}, {"primary_key": "2237330", "vector": [], "sparse_vector": [], "title": "Hamiltonicity of random subgraphs of the hypercube.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study Hamiltonicity in random subgraphs of the hypercube n. Our first main theorem is an optimal hitting time result. Consider the random process which includes the edges of n according to a uniformly chosen random ordering. Then, with high probability, as soon as the graph produced by this process has minimum degree 2k, it contains k edge-disjoint Hamilton cycles, for any fixed k ∊ ℕ. Secondly, we obtain a perturbation result: if H ⊆ n satisfies δ(H) ≥ αn with α > 0 fixed and we consider a random binomial subgraph of n with p ∊ (0, 1] fixed, then with high probability contains k edge-disjoint Hamilton cycles, for any fixed k ∊ ℕ. In particular, both results resolve a long standing conjecture, posed e.g. by <PERSON><PERSON><PERSON><PERSON>, that the threshold probability for Hamiltonicity in the random binomial subgraph of the hypercube equals 1/2. Our techniques also show that, with high probability, for all fixed p ∊ (0, 1] the graph contains an almost spanning cycle. Our methods involve branching processes, the Rödl nibble, and absorption.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.56"}, {"primary_key": "2237331", "vector": [], "sparse_vector": [], "title": "The Secretary Problem with <PERSON> Sampling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON>"], "summary": "In the secretary problem we are faced with an online sequence of elements with values. Upon seeing an element we have to make an irrevocable take-it-or-leave-it decision. The goal is to maximize the probability of picking the element of maximum value. The most classic version of the problem is that in which the elements arrive in random order and their values are arbitrary. Here, the optimal algorithm picks the maximum value with probability at least 1/e. However, by varying the available information, new interesting problems arise. For instance, in the full information variant of the secretary problem the values are i.i.d. samples from a known distribution. Naturally, the best possible success probability increases and turns out to be approximately 0.58. Also, the case in which the arrival order is adversarial instead of random leads to interesting variants that have been considered in the literature.In this paper we study both the random order and adversarial order secretary problems with an additional twist. The values are arbitrary, but before starting the online sequence we independently sample each element with a fixed probability p. The sampled elements become our information or history set and the game is played over the remaining elements. We call these problems the random order secretary problem with p-sampling (ROSp for short) and the adversarial order secretary problem with p-sampling (AOSp for short). Our main result is to obtain best possible algorithms for both problems and all values of p. As p grows to 1 the obtained guarantees converge to the optimal guarantees in the full information case. In the adversarial order setting, the best possible algorithm turns out to be a simple fixed threshold algorithm in which the optimal threshold is a function of p only. Therefore, even knowledge of the total number of elements is unnecessary. Proving that this algorithm is optimal involves a novel technique, which boils down to analyzing a related game in a conflict graph over binary sequences. In the random order setting we prove that the best possible algorithm is characterized by a fixed sequence of time thresholds, dictating at which point in time we should start accepting a value that is both a maximum of the online sequence and has a given ranking within the sampled elements. Surprisingly, this sequence of time thresholds arises from a separable and convex optimization problem whose solution is independent of p.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.122"}, {"primary_key": "2237332", "vector": [], "sparse_vector": [], "title": "On Indexing and Compressing Finite Automata.", "authors": ["<PERSON>", "<PERSON>"], "summary": "An index for a finite automaton is a powerful data structure that supports locating paths labeled with a query pattern, thus solving pattern matching on the underlying regular language. The problem is hard in the general case: a recent conditional lower bound suggests that, in the worst case, deciding at query time whether a pattern of length m belongs to the substring closure of the language accepted by requires time. On the other hand, <PERSON><PERSON><PERSON> et al. [TCS 2017] introduced a subclass of automata that allow an optimal Õ(m)-time solution based on prefix-sorting the states in a total order. In this paper, we solve the long-standing problem of indexing arbitrary finite automata, matching the above bounds. Our solution consists in finding a partial co-lexicographic order of the states and proving, as in the total order case, that states reached by a given string form one interval on the partial order, thus enabling indexing. We provide a lower bound stating that such an interval requires O(p) words to be represented, p being the order's width (i.e. the size of its largest antichain). Indeed, we show that p determines the complexity of several fundamental problems on finite automata:i.Letting σ be the alphabet size, we provide an encoding for NFAs using [log σ] + 2[log p] + 2 bits per transition and a smaller encoding for DFAs using [log σ] + [log p] + 2 bits per transition. This is achieved by generalizing the <PERSON>ows-Wheeler transform to arbitrary automata.ii.We show that indexed pattern matching can be solved in Õ(m · p2) query time on NFAs.iii.We provide a polynomial-time algorithm to index DFAs, while matching the optimal value for p. On the other hand, we prove that the problem is NP-hard on NFAs.iv.We show that, in the worst case, the classic power-set construction algorithm for NFA determinization generates an equivalent DFA of size 2p(n–p+1) – 1, where n is the number of NFA's states.Contribution (i) provides a new compression paradigm for labeled graphs. Contributions (ii)-(iii) solve the regular language indexing problem, notably with a polynomial-time solution for DFAs. Contribution (iv) implies a new FPT analysis for the complexity of classic algorithms on automata, including membership and equivalence (the latter being PSPACE-complete when input automata are NFAs).", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.153"}, {"primary_key": "2237333", "vector": [], "sparse_vector": [], "title": "Block-Structured Integer and Linear Programming in Strongly Polynomial and Near Linear Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider integer and linear programming problems for which the linear constraints exhibit a (recursive) block-structure: The problem decomposes into independent and efficiently solvable sub-problems if a small number of constraints is deleted. A prominent example are n-fold integer programming problems and their generalizations which have received considerable attention in the recent literature. The previously known algorithms for these problems are based on the augmentation framework, a tailored integer programming variant of local search.In this paper we propose a different approach. Our algorithm relies on parametric search and a new proximity bound. We show that block-structured linear programming can be solved efficiently via an adaptation of a parametric search framework by <PERSON>, <PERSON>, and <PERSON><PERSON> in combination with <PERSON><PERSON><PERSON>'s multidimensional search technique. This also forms a subroutine of our algorithm for the integer programming case by solving a strong relaxation of it. Then we show that, for any given optimal vertex solution of this relaxation, there is an optimal integer solution within ℓ1-distance independent of the dimension of the problem. This in turn allows us to find an optimal integer solution efficiently. We apply our techniques to integer and linear programming with n-fold structure or bounded dual treedepth, two benchmark problems in this field. We obtain the first algorithms for these cases that are both near-linear in the dimension of the problem and strongly polynomial. Moreover, unlike the augmentation algorithms, our approach is highly parallelizable.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.101"}, {"primary_key": "2237334", "vector": [], "sparse_vector": [], "title": "A Fast Minimum Degree Algorithm and Matching Lower Bound.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The minimum degree algorithm is one of the most widely-used heuristics for reducing the cost of solving large sparse systems of linear equations. It has been studied for nearly half a century and has a rich history of bridging techniques from data structures, graph algorithms, and scientific computing. In this paper, we present a simple but novel combinatorial algorithm for computing an exact minimum degree elimination ordering in O(nm) time, which improves on the best known time complexity of O(n3) and offers practical improvements for sparse systems with small values of m. Our approach leverages a careful amortized analysis, which also allows us to derive output-sensitive bounds for the running time of , where m+ is the number of unique fill edges and original edges that the algorithm encounters and Δ is the maximum degree of the input graph.Furthermore, we show there cannot exist an exact minimum degree algorithm that runs in O(nm1 – ∊) time, for any ∊ > 0, assuming the strong exponential time hypothesis. This fine-grained reduction goes through the orthogonal vectors problem and uses a new low-degree graph construction called U-fillers, which act as pathological inputs and cause any minimum degree algorithm to exhibit nearly worst-case performance. With these two results, we nearly characterize the time complexity of computing an exact minimum degree ordering.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.45"}, {"primary_key": "2237335", "vector": [], "sparse_vector": [], "title": "A Structural Theorem for Local Algorithms with Applications to Coding, Testing, and Privacy.", "authors": ["Marcel de Sena Dall&apos;Agnol", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove a general structural theorem for a wide family of local algorithms, which includes property testers, local decoders, and PCPs of proximity. Namely, we show that the structure of every algorithm that makes q adaptive queries and satisfies a natural robustness condition admits a sample-based algorithm with sample complexity. We also prove that this transformation is nearly optimal, and admits a scheme for constructing privacy-preserving local algorithms.Using the unified view that our structural theorem provides, we obtain the following results.•We strengthen the state-of-the-art lower bound for relaxed locally decodable codes, obtaining an exponential improvement on the dependency in query complexity; this resolves an open problem raised by <PERSON><PERSON> and <PERSON> (SODA 2020).•We show that any (constant-query) testable property admits a sample-based tester with sublinear sample complexity; this resolves a problem left open in a work of <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> (FOCS 2015) by extending their main result to adaptive testers.•We prove that the known separation between proofs of proximity and testers is essentially maximal; this resolves a problem left open by <PERSON><PERSON> and <PERSON><PERSON> (ECCC 2013, Computational Complexity 2018) regarding sublinear-time delegation of computation.Our techniques strongly rely on relaxed sunflower lemmas and the <PERSON><PERSON><PERSON><PERSON> theorem.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.100"}, {"primary_key": "2237336", "vector": [], "sparse_vector": [], "title": "Scheduling with Communication Delays via LP Hierarchies and Clustering II: Weighted Completion Times on Related Machines.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of scheduling jobs with precedence constraints on related machines to minimize the weighted sum of completion times, in the presence of communication delays. In this setting, denoted by Q | prec, c | ΣwjCj, if two dependent jobs are scheduled on different machines, then at least c units of communication delay time must pass between their executions. Our main result is an O(log4 n)-approximation algorithm for the problem. As a byproduct of our result, we also obtain an O(log3 n)-approximation algorithm for the problem of minimizing makespan Q | prec, c | Cmax, which improves upon the O(log5 n/ log log n)-approximation algorithm due to a recent work of <PERSON><PERSON> et al. [MRS+20].", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.176"}, {"primary_key": "2237337", "vector": [], "sparse_vector": [], "title": "Online Combinatorial Auctions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study combinatorial auctions in online environments with the goal of maximizing social welfare. In this problem, new items become available on each day and must be sold before their respective expiration dates. We design online auctions for the widely studied classes of submodular and XOS valuations, and show the following results:– For submodular valuations, we give an O(log m)-competitive mechanism for adversarial valuations and an O(1)-competitive mechanism for Bayesian valuations, where m is the total number of items. Both these mechanisms are computationally efficient and universally truthful for myopic agents, i.e., agents with no knowledge of the future.– For XOS valuations, we show that there is no online mechanism that can achieve a competitive ratio of o ((m/ log m)1/3) even in a Bayesian setting. Our lower bound holds even if we do not require truthfulness and/or computational efficiency of the mechanism.This establishes a sharp separation between XOS valuations and its subclass of submodular valuations for online combinatorial auctions. In contrast, no such separation exists for offline auctions, where the best bounds for both submodular and XOS valuations are O((log log m)3) for adversarial settings (<PERSON><PERSON><PERSON> and <PERSON><PERSON>, FOCS 2019) and O(1) for Bayesian settings (<PERSON><PERSON><PERSON> et al., FOCS 2017).In contrast to the above, if items do not expire and only need to be sold before the market closes, then we give a reduction from offline to online mechanisms that preserves the competitive ratio for all subadditive valuations (that includes XOS and submodular valuations), thereby achieving the same bounds as the respective best offline mechanisms.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.70"}, {"primary_key": "2237338", "vector": [], "sparse_vector": [], "title": "Beating Greedy For Approximating Reserve Prices in Multi-Unit VCG Auctions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of finding personalized reserve prices for unit-demand buyers in multi-unit eager VCG auctions with correlated buyers. The input to this problem is a dataset of submitted bids of n buyers in a set of auctions. The goal is to find a vector of reserve prices, one for each buyer, that maximizes the total revenue across all auctions.<PERSON><PERSON><PERSON> and <PERSON> (2016) showed that this problem is APX-hard but admits a greedy ½-approximation algorithm. Later, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (2019) gave an LP-based algorithm achieving a 0.68-approximation for the (important) special case of the problem with a single-item, thereby beating greedy. We show in this paper that the algorithm of <PERSON><PERSON><PERSON><PERSON> et al. in fact does not beat greedy for the general multi-item problem. This raises the question of whether or not the general problem admits a better-than-½ approximation.In this paper, we answer this question in the affirmative and provide a polynomial-time algorithm with a significantly better approximation-factor of 0.63. Our solution is based on a novel linear programming formulation, for which we propose two different rounding schemes. We prove that the best of these two and the no-reserve case (all-zero vector) is a 0.63-approximation.Full version. Due to the page limit, this version of the paper does not include all the proofs. The full version of the paper is available at [11].", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.68"}, {"primary_key": "2237339", "vector": [], "sparse_vector": [], "title": "Branch-and-Bound Solves Random Binary IPs in Polytime.", "authors": ["Santanu <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Branch-and-bound is the workhorse of all state-of-the-art mixed integer linear programming (MILP) solvers. These implementations of branch-and-bound typically use variable branching, that is, the child nodes are obtained by fixing some variable to an integer value v in one node and to v+1 in the other node. Even though modern MILP solvers are able to solve very large-scale instances efficiently, relatively little attention has been given to understanding why the underlying branch-and-bound algorithm performs so well. In this paper our goal is to theoretically analyze the performance of the standard variable branching based branch-and-bound algorithm. In order to avoid the exponential worst-case lower bounds, we follow the common idea of considering random instances. More precisely, we consider random integer programs where the entries of the coefficient matrix and the objective function are randomly sampled.Our main result is that with good probability branch-and-bound with variable branching explores only a polynomial number of nodes to solve these instances, for a fixed number of constraints. To the best of our knowledge this is the first known such result for a standard version of branch-and-bound. We believe that this result provides a compelling indication of why branch-and-bound with variable branching works so well in practice.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.35"}, {"primary_key": "2237340", "vector": [], "sparse_vector": [], "title": "Improved Algorithms for Solving Polynomial Systems over GF(2) by Multiple Parity-Counting.", "authors": ["<PERSON><PERSON>"], "summary": "We consider the problem of finding a solution to a multivariate polynomial equation system of degree d in n variables over . For d = 2, the best-known algorithm for the problem is by <PERSON><PERSON> et al. [<PERSON>. Complexity, 2013] and was shown to run in time O(20.792n) under assumptions that were experimentally found to hold for random equation systems. The best-known worst-case algorithm for the problem is due to <PERSON><PERSON><PERSON><PERSON><PERSON> et al. [ICALP'19]. It runs in time O(20.804n) for d = 2 and O(2(1-1/(2.7d))n) for d > 2.In this paper, we devise a worst-case algorithm that improves the one by <PERSON><PERSON><PERSON><PERSON><PERSON> et al. It runs in time O(20.6943n) (or O(1.6181n)) for d = 2 and O(2(1–1/(2d))n) for d > 2. Our algorithm thus outperforms all known worst-case algorithms, as well as ones analyzed for random equation systems. We also devise a second algorithm that outputs all solutions to a polynomial system and has similar complexity to the first (provided that the number of solutions is not too large).A central idea in the work of <PERSON><PERSON><PERSON><PERSON><PERSON> et al. was to reduce the problem of finding a solution to a polynomial system over to the problem of counting the parity of all solutions. A parity-counting instance was then reduced to many smaller parity-counting instances. Our main observation is that these smaller instances are related and can be solved more efficiently by a new algorithm to a problem which we call multiple parity-counting.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.151"}, {"primary_key": "2237341", "vector": [], "sparse_vector": [], "title": "Rolling backwards can move you forward: on embedding problems in sparse expanders.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We develop a general embedding method based on the Friedman-<PERSON><PERSON><PERSON> tree embedding technique (1987) and its algorithmic version, essentially due to <PERSON><PERSON><PERSON><PERSON> et al. (1996), enhanced with a roll-back idea allowing to sequentially retrace previously performed embedding steps. This proves to be a powerful tool for embedding graphs of large girth into expander graphs. As an application of this method, we settle two problems:•For a graph H, we denote by Hq the graph obtained from H by subdividing its edges with q–1 vertices each. We show that the k-size-Ramsey number Ŗk(Hq) satisfies Ŗk(Hq) = O(qn) for every bounded degree graph H on n vertices and for q = Ω(log n), which is optimal up to a constant factor. This settles a conjecture of <PERSON> (2002).•We give a deterministic, polynomial time algorithm for finding vertex-disjoint paths between given pairs of vertices in a strong expander graph. More precisely, let G be an (n, d, λ)-graph with λ = O(d1 – ∊), and let be any collection of at most disjoint pairs of vertices in G for some small constant c, such that in the neighborhood of every vertex in G there are at most d/4 vertices from . Then there exists a polynomial time algorithm which finds vertex-disjoint paths between every pair in , and each path is of the same length . Both the number of pairs and the length of the paths are optimal up to a constant factor; the result answers the offline version of a question of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (2007).", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.9"}, {"primary_key": "2237342", "vector": [], "sparse_vector": [], "title": "Approximate Evaluation of First-Order Counting Queries.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> introduced the very expressive first-order counting logic FOC(P) to model database queries with counting operations. They showed that there is an efficient model-checking algorithm on graphs with bounded degree, while <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> showed that probably no such algorithm exists for trees of bounded depth. We analyze the fragment FO({>0}) of this logic. While we remove for example subtraction and comparison between two nonatomic counting terms, this logic remains quite expressive: We allow nested counting and comparison between counting terms and arbitrarily large numbers. Our main result is an approximation scheme of the model-checking problem for FO({>0}) that runs in linear fpt time on structures with bounded expansion. This scheme either gives the correct answer or says \"I do not know.\" The latter answer may only be given if small perturbations in the number-symbols of the formula could make it both satisfied and unsatisfied. This is complemented by showing that exactly solving the model-checking problem for FO({>0}) is already hard on trees of bounded depth and just slightly increasing the expressiveness of FO({>0}) makes even approximation hard on trees.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.104"}, {"primary_key": "2237343", "vector": [], "sparse_vector": [], "title": "Approximate Distance Oracles Subject to Multiple Vertex Failures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Ren"], "summary": "Given an undirected graph G = (V, E) of n vertices and m edges with weights in [1, W], we construct vertex sensitive distance oracles (VSDO), which are data structures that preprocess the graph, and answer the following kind of queries: Given a source vertex u, a target vertex v, and a batch of d failed vertices D, output (an approximation of) the distance between u and v in G – D (that is, the graph G with vertices in D removed). An oracle has stretch α if it always holds that , where δG–D(u, v) is the actual distance between u and v in G – D, and is the distance reported by the oracle.In this paper we construct efficient VSDOs for any number d of failures. For any constant c ≥ 1, we propose two oracles:•The first oracle has size n2+1/c(log n/∊)O(d) · log W, answers a query in poly(log n, dc, log log W, ∊–1) time, and has stretch 1 + ∊, for any constant ∊ > 0.•The second oracle has size n2+1/cpoly (log(nW), d), answers a query in poly (log n, dc, log log W) time, and has stretch poly (log n, d).Both of these oracles can be preprocessed in time polynomial in their space complexity. These results are the first approximate distance oracles of poly-logarithmic query time for any constant number of vertex failures in general undirected graphs. Previously there are (1 + ∊)-approximate d-edge sensitive distance oracles [Chechik et al. 2017] answering distance queries when d edges fail, which have size O(n2(log n/∊)d · d log W) and query time poly (log n, d, log log W).", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.148"}, {"primary_key": "2237344", "vector": [], "sparse_vector": [], "title": "EPTAS for k-means Clustering of Affine Subspaces.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider a generalization of the fundamental k-means clustering for data with incomplete or corrupted entries. When data objects are represented by points in ℝd, a data point is said to be incomplete when some of its entries are missing or unspecified. An incomplete data point with at most Δ unspecified entries corresponds to an axis-parallel affine subspace of dimension at most Δ, called a Δ-point. Thus we seek a partition of n input Δ-points into k clusters minimizing the k-means objective. For Δ = 0, when all coordinates of each point are specified, this is the usual k-means clustering. We give an algorithm that finds an (1 + ∊)-approximate solution in time f(k, ∊, Δ) · n2 · d for some function f of k, ∊, and Δ only.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.157"}, {"primary_key": "2237345", "vector": [], "sparse_vector": [], "title": "Counting Small Permutation Patterns.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "A sample of n generic points in the xy-plane defines a permutation that relates their ranks along the two axes. Every subset of k points similarly defines a pattern, which occurs in that permutation. The number of occurrences of small patterns in a large permutation arises in many areas, including nonparametric statistics. It is therefore desirable to count them more efficiently than the straightforward Õ(nk) time algorithm.This work proposes new algorithms for counting patterns. We show that all patterns of order 2 and 3, as well as eight patterns of order 4, can be counted in nearly linear time. To that end, we develop an algebraic framework that we call corner tree formulas. Our approach generalizes the existing methods and allows a systematic study of their scope.Using the machinery of corner trees, we find twenty-three independent linear combinations of order-4 patterns, that can be computed in time Õ(n). We also describe an algorithm that counts one of the remaining 4-patterns, and hence all 4-patterns, in time Õ(n3/2).As a practical application, we provide a nearly linear time computation of a statistic by <PERSON><PERSON><PERSON> (1970), <PERSON> and <PERSON> (2010). This statistic yields a natural and strongly consistent variant of <PERSON><PERSON><PERSON><PERSON>'s test for independence of X and Y, given a random sample as above. This improves upon the so far most efficient Õ(n2) algorithm.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.136"}, {"primary_key": "2237346", "vector": [], "sparse_vector": [], "title": "Rapid Mixing from Spectral Independence beyond the Boolean Domain.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We extend the notion of spectral independence (introduced by <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> [2]) from the Boolean domain to general discrete domains. This property characterises distributions with limited correlations, and implies that the corresponding Glauber dynamics is rapidly mixing.As a concrete application, we show that Glauber dynamics for sampling proper q-colourings mixes in polynomial-time for the family of triangle-free graphs with maximum degree Δ provided q ≥ (α∗ + δ)Δ where α∗ ≈ 1.763 is the unique solution to α∗ = exp (1/α∗) and δ > 0 is any constant. This is the first efficient algorithm for sampling proper q-colourings in this regime with possibly unbounded Δ. Our main tool of establishing spectral independence is the recursive coupling by <PERSON>, <PERSON>, and <PERSON> [19].", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.95"}, {"primary_key": "2237347", "vector": [], "sparse_vector": [], "title": "Distributed Metropolis Sampler with Optimal Parallelism.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Metropolis-Hastings algorithm is a fundamental Markov chain Monte Carlo (MCMC) method for sampling and inference. With the advent of Big Data, distributed and parallel variants of MCMC methods are attracting increased attention. In this paper, we give a distributed algorithm that can faithfully simulates sequential single-site Metropolis chains without introducing any bias. When a natural Lipschitz condition for the the Metropolis filters is satisfied, the algorithm can faithfully simulate N-step Metropolis chains within O(N/n + log n) rounds of asynchronous communications, where n is the number of variables. For sequential single-site dynamics, whose mixing requires Ω(n log n) steps, this achieves an optimal linear speedup. For several well-studied graphical models, including proper graph coloring, hardcore model, and Ising model, our condition for linear speedup is much weaker than the uniqueness conditions for the respective models.The novel idea in our algorithm is to resolve updates in advance: the local Metropolis filters can be executed correctly before the full information about neighboring spins is available. This achieves optimal parallelism without introducing any bias.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.127"}, {"primary_key": "2237348", "vector": [], "sparse_vector": [], "title": "Two-stage Stochastic Matching with Application to Ride Hailing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study a two-stage stochastic matching problem motivated in part by applications in online marketplaces used for ride hailing. Using a randomized primal-dual algorithm applied to a family of \"balancing\" convex programs, we obtain the optimal 3/4 competitive ratio against the optimum offline benchmark. These balancing convex programs offer a natural generalization of the matching skeleton by <PERSON><PERSON> et al. (2012) and may be of independent interest. Switching to the more precise benchmark of optimum online, we exploit connections to submodular optimization and use a factor-revealing program to improve the 3/4 ratio to (1 – 1/e + 1/e2) ≈ 0.767 for the unweighted and 0.761 for the weighted case. We also show it is NP-hard to obtain an FPTAS with respect to this benchmark.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.170"}, {"primary_key": "2237349", "vector": [], "sparse_vector": [], "title": "On Efficient Distance Approximation for Graph Properties.", "authors": ["Nimrod Fiat", "<PERSON>"], "summary": "A distance-approximation algorithm for a graph property P in the adjacency-matrix model is given an approximation parameter ∊ ∊ (0, 1) and query access to the adjacency matrix of a graph G = (V, E). It is required to output an estimate of the distance between G and the closest graph G′ = (V, E′) that satisfies , where the distance between graphs is the size of the symmetric difference between their edge sets, normalized by |V|2.In this work we introduce property covers, as a basis for a methodology that uses distance-approximation algorithms for \"simple\" properties to design distance-approximation algorithms for more \"complex\" properties. Applying this methodology we present distance-approximation algorithms with poly(1/∊) query complexity for induced P3-freeness, induced P4-freeness, and Chordality. For induced C4-freeness our algorithm has query complexity exp(poly(1/∊)). These complexities essentially match the corresponding known results for testing these properties and provide an exponential improvement on previously known results.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.98"}, {"primary_key": "2237350", "vector": [], "sparse_vector": [], "title": "Consistent k-Clustering for General Metrics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>Fard", "<PERSON><PERSON>"], "summary": "Given a stream of points in a metric space, is it possible to maintain a constant approximate clustering by changing the cluster centers only a small number of times during the entire execution of the algorithm?This question received attention in recent years in the machine learning literature and, before our work, the best known algorithm performs Õ(k2) center swaps (the Õ(·) notation hides polylogarithmic factors in the number of points n and the aspect ratio Δ of the input instance). This is a quadratic increase compared to the offline case — the whole stream is known in advance and one is interested in keeping a constant approximation at any point in time — for which Õ(k) swaps are known to be sufficient and simple examples show that Ω(k log(nΔ)) swaps are necessary. We close this gap by developing an algorithm that, perhaps surprisingly, matches the guarantees in the offline setting. Specifically, we show how to maintain a constant-factor approximation for the k-median problem by performing an optimal (up to polylogarithimic factors) number Õ(k) of center swaps. To obtain our result we leverage new structural properties of k-median clustering that may be of independent interest.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.158"}, {"primary_key": "2237351", "vector": [], "sparse_vector": [], "title": "A Topological Characterization of Modulo-p Arguments and Implications for Necklace Splitting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Manolis Zampetakis"], "summary": "The classes PPA-p have attracted attention lately, because they are the main candidates for capturing the complexity of Necklace Splitting with p thieves, for prime p. However, these classes were not known to have complete problems of a topological nature, which impedes any progress towards settling the complexity of the Necklace Splitting problem. On the contrary, topological problems have been pivotal in obtaining completeness results for PPAD and PPA, such as the PPAD-completeness of finding a Nash equilibrium [18, 15] and the PPA-completeness of Necklace Splitting with 2 thieves [24].In this paper, we provide the first topological characterization of the classes PPA-p. First, we show that the computational problem associated with a simple generalization of <PERSON>'s Lemma, termed p-polygon-Tucker, as well as the associated Borsuk-Ulam-type theorem, p-polygon-Borsuk-Ulam, are PPA-p-complete. Then, we show that the computational version of the well-known BSS Theorem [8], as well as the associated BSS-Tucker problem are PPA-p-complete. Finally, using a different generalization of <PERSON>'s Lemma (termed ℤp-star-Tucker), which we prove to be PPA-p-complete, we prove that p-thief Necklace Splitting is in PPA-p. This latter result gives a new combinatorial proof for the Necklace Splitting theorem, the only proof of this nature other than that of <PERSON><PERSON><PERSON> [42].All of our containment results are obtained through a new combinatorial proof for ℤp-versions of <PERSON>'s lemma that is a natural generalization of the standard combinatorial proof of <PERSON>'s lemma by <PERSON><PERSON>nd and <PERSON> [27]. We believe that this new proof technique is of independent interest.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.155"}, {"primary_key": "2237352", "vector": [], "sparse_vector": [], "title": "Static and Streaming Data Structures for Fréchet Distance Queries.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a curve P with points in ℝd in a streaming fashion, and parameters ∊ > 0 and k, we construct a distance oracle that uses space, and given a query curve Q with k points in ℝd, returns in O(kd) time a 1 + ∊ approximation of the discrete Fréchet distance between Q and P.In addition, we construct simplifications in the streaming model, oracle for distance queries to a sub-curve (in the static setting), and introduce the zoom-in problem. Our algorithms work in any dimension d, and therefore we generalize some useful tools and algorithms for curves under the discrete Fréchet distance to work efficiently in high dimensions.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.71"}, {"primary_key": "2237353", "vector": [], "sparse_vector": [], "title": "Graph Spanners by Sketching in Dynamic Streams and the Simultaneous Communication Model.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Graph sketching is a powerful technique introduced by the seminal work of <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>'12 on connectivity in dynamic graph streams that has enjoyed considerable attention in the literature since then, and has led to near optimal dynamic streaming algorithms for many fundamental problems such as connectivity, cut and spectral sparsifiers and matchings. Interestingly, however, the sketching and dynamic streaming complexity of approximating the shortest path metric of a graph is still far from well-understood. Besides a direct k-pass implementation of classical spanner constructions (recently improved to -passes by <PERSON>, <PERSON> and <PERSON>'20) the state of the art amounts to a O(log k)-pass algorithm of <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>'12, and a 2-pass algorithm of <PERSON><PERSON><PERSON> and <PERSON><PERSON>'14. In particular, no single pass algorithm is known, and the optimal tradeoff between the number of passes, stretch and space complexity is open.In this paper we introduce several new graph sketching techniques for approximating the shortest path metric of the input graph. We give the first single pass sketching algorithm for constructing graph spanners: we show how to obtain a Õ(n⅔)-spanner using Õ(n) space, and in general a Õ(n⅔(1–α))-spanner using Õ(n1+α) space for every α ∊ [0, 1], a tradeoff that we think may be close optimal. We also give new spanner construction algorithms for any number of passes, simultaneously improving upon all prior work on this problem. Finally, we note that unlike the original sketching approach of <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>'12, none of the existing spanner constructions yield simultaneous communication protocols with low per player information. We give the first such protocols for the spanner problem that use a small number of rounds.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.113"}, {"primary_key": "2237354", "vector": [], "sparse_vector": [], "title": "Counting Homomorphisms to K4-minor-free Graphs, modulo 2.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of computing the parity of the number of homomorphisms from an input graph G to a fixed graph H. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [ToC'15] introduced an explicit criterion on the graph H and conjectured that, if satisfied, the problem is solvable in polynomial time and, otherwise, the problem is complete for the complexity class ⊕P of parity problems.We verify their conjecture for all graphs H that exclude the complete graph on 4 vertices as a minor. Further, we rule out the existence of a subexponential-time algorithm for the ⊕P-complete cases, assuming the randomised Exponential Time Hypothesis.Our proofs introduce a novel method of deriving hardness from globally defined substructures of the fixed graph H. Using this, we subsume all prior progress towards resolving the conjecture (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [ToC'15]; <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON> [ToCT'14,'16]). As special cases, our machinery also yields a proof of the conjecture for graphs with maximum degree at most 3, as well as a full classification for the problem of counting list homomorphisms, modulo 2.A full version of our paper, containing all proofs, is available at https://arxiv.org/abs/2006.16632v2. Here we number key lemmas to match the numbering in the full version.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.137"}, {"primary_key": "2237355", "vector": [], "sparse_vector": [], "title": "Dynamic Maintenance of Low-Stretch Probabilistic Tree Embeddings with Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give the first non-trivial fully dynamic probabilistic tree embedding algorithm for a weighted, undirected graph G with n nodes and at most m edges undergoing edge insertions and deletions. The goal in this problem is to maintain a tree containing all nodes of G with a randomized algorithm such that for every edge (u, v) of G the expected length of the path from u to v in the tree exceeds the weight of the edge (u, v) only by a small multiplicative factor, called the stretch of the embedding. In this paper, we obtain a trade-off between amortized update time and expected stretch against an oblivious adversary. At the two extremes of this trade-off, we can maintain a tree of expected stretch O(log4 n) with update time m1/2+o(1) or a tree of expected stretch no(1) with update time no(1) (for edge weights polynomial in n). A guarantee of the latter type has so far only been known for maintaining tree embeddings with average (instead of expected) stretch [Chechik/Zhang, SODA '20].Our main result has direct implications to fully dynamic approximate distance oracles and fully dynamic buy-at-bulk network design as our trade-off from above carries over to these two problems with minor overheads. For dynamic distance oracles, our result is the first to break the update-time barrier. For buy-at-bulk network design, a problem which also in the static setting heavily relies on probabilistic tree embeddings, we give the first non-trivial dynamic algorithm. As probabilistic tree embeddings are an important tool in static approximation algorithms, we expect our result to have further applications in dynamic approximation algorithms.From a technical perspective, we obtain our main result by first designing a decremental (i.e., deletionsonly) algorithm for probabilistic low-diameter decompositions via a careful combination of Bartal's ball-growing approach [FOCS '96] with the pruning framework of Chechik and Zhang [SODA '20]. Such a low-diameter decomposition is the heart of Bartal's seminal tree embedding construction and we show how to adapt it to the decremental setting. We then extend this to a fully dynamic algorithm by significantly enriching a well-known \"decremental to fully dynamic\" reduction with a new bootstrapping idea to recursively employ a fully dynamic algorithm instead of a static one in this reduction. By additionally exploiting certain properties of our tree embedding, this bootstrapping scheme can be made highly efficient.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.75"}, {"primary_key": "2237356", "vector": [], "sparse_vector": [], "title": "Hardness of Approximation for Orienteering with Multiple Time Windows.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Vehicle routing problems are a broad class of combinatorial optimization problems that can be formulated as the problem of finding a tour in a weighted graph that optimizes some function of the visited vertices. For instance, a canonical and extensively studied vehicle routing problem is the orienteering problem where the goal is to find a tour that maximizes the number of vertices visited by a given deadline. In this paper, we consider the computational tractability of a well-known generalization of the orienteering problem called the Orient-MTW problem. The input to Orient-MTW consists of a weighted graph G(V, E) where for each vertex v ∊ V we are given a set of time instants Tv ⊆ [T], and a source vertex s. A tour starting at s is said to visit a vertex v if it transits through v at any time in the set Tv. The goal is to find a tour starting at the source vertex that maximizes the number of vertices visited. It is known that this problem admits a quasi-polynomial time O(log OPT)-approximation ratio where OPT is the optimal solution value but until now no hardness better than an APX-hardness was known for this problem.Our main result is an -hardness for this problem that holds even when the underlying graph G is an undirected tree. This is the first super-constant hardness result for the Orient-MTW problem. The starting point for our result is the hardness of the SetCover problem which is known to hold on instances with a special structure. We exploit this special structure of the hard SetCover instances to first obtain a new proof of the APX-hardness result for Orient-MTW that holds even on trees of depth 2. We then recursively amplify this constant factor hardness to an -hardness, while keeping the resulting topology to be a tree. Our amplified hardness proof crucially utilizes a delicate concavity property which shows that in our encoding of SetCover instances as instances of the Orient-MTW problem, whenever the optimal cost for SetCover instance is large, any tour, no matter how it allocates its time across different sub-trees, can not visit too many vertices overall. We believe that this reduction template may also prove useful in showing hardness of other vehicle routing problems.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.177"}, {"primary_key": "2237357", "vector": [], "sparse_vector": [], "title": "Shorter Labels for Routing in Trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A routing labeling scheme assigns a binary string, called a label, to each node in a network, and chooses a distinct port number from {1, …, d} for every edge outgoing from a node of degree d. Then, given the labels of u and w and no other information about the network, it should be possible to determine the port number corresponding to the first edge on the shortest path from u to w. In their seminal paper, <PERSON><PERSON> <PERSON> [SPAA 2001] designed several routing methods for general weighted networks. An important technical ingredient in their paper that according to the authors \"may be of independent practical and theoretical interest\" is a routing labeling scheme for trees of arbitrary degrees. For a tree on n nodes, their scheme constructs labels consisting of (1 + o(1)) log n bits such that the sought port number can be computed in constant time. Looking closer at their construction, the labels consist of bits. Given that the only known lower bound is log n + Ω(log log n), a natural question that has been asked for other labeling problems in trees is to determine the asymptotics of the smaller-order term.We make the first (and significant) progress in 19 years on determining the correct second-order term for the length of a label in a routing labeling scheme for trees on n nodes. We design such a scheme with labels of length .", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.130"}, {"primary_key": "2237358", "vector": [], "sparse_vector": [], "title": "Planar Negative k-Cycle.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Given an edge-weighted directed graph G, the Negative-k-Cycle problem asks whether G contains a negative-weight cycle with at most k edges. For k = 3 the problem is known as the NegativeTriangle problem and is equivalent to all-pairs shortest paths (and to min-plus matrix multiplication) and solvable in O(n3) time. In this paper, we consider the case of directed planar graphs. We show that the Negative-k-Cycle problem can be solved in min{O(nk2 log n), O(n2)} time. Assuming the min-plus convolution conjecture, we then show, for k > n1/3 that there is no algorithm polynomially faster than , and for k ≤ n1/3 that our O(nk2 log n) upper bound is essentially tight. The latter gives the first non-trivial tight bounds for a planar graph problem in P. Our lower bounds are obtained by introducing a natural problem on matrices that generalizes both min-plus matrix multiplication and min-plus convolution, and whose complexity lies between the complexities of these two problems.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.161"}, {"primary_key": "2237359", "vector": [], "sparse_vector": [], "title": "Beyond Submodular Maximization via One-Sided Smoothness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The multilinear framework was developed to achieve the breakthrough 1 – 1/e approximation for maximizing a monotone submodular function subject to a matroid constraint, which includes the submodular welfare problem as special case. This framework has a continuous optimization part (solving the multilinear extension of a submodular set function) and a rounding part (rounding a fractional solution to an integral one). We extend both parts so that the resulting generalized framework may be used on a wider array of problems. In particular, we make a conceptual contribution by identifying a family of parameterized functions and their applications. As a running example we focus on solving diversity problems max , where ℳ is matroid. These diversity functions have Aij ≥ 0 as a measure of dissimilarity of i, j, and A has 0-diagonal. This family of problems ranges from intractable problems such as densest k-subgraph, to ½-approximable metric diversity problems. The multilinear extension F of such diversity functions satisfies ▿2F(x) = A ≥ 0 and hence the original multilinear framework (which assumes non-positive Hessians) does not directly apply. Instead we introduce a new parameter for functions F ∊ C2 which measures the approximability of the associated problem max{F(x) : x ∊ P}, for solvable downwards-closed polytopes P. A function F is called one-sided σ-smooth if for all u, x ≥ 0, x = 0. For σ = 0 this class includes previously studied classes such as continuous DR-submodular functions, and much more. For the multlinear extension of a diversity function, we show that it is one-sided σ-smooth whenever <PERSON><PERSON> forms a σ-semi-metric.We give an Ω(1/σ)-approximation for the continuous maximization problem of monotone, normalized one-sided σ-smooth F with an additional property: non-positive third order partial derivatives. Since the multilinear extension of a diversity function has this additional property we can apply the extended multilinear framework to this family of discrete problems. This requires new matroid rounding techniques for quadratic objectives. The result is an Ω(1/σ3/2)-approximation for maximizing a σ-semi-metric diversity function subject to matroid constraint. This improves upon the previous best bound of Ω(1/σ) and we give evidence that it may be tight. For general one-sided smooth functions, we show the continuous process gives an Ω(1/32σ)-approximation, independent of n. In this setting, by discretizing, we present a concrete poly-time algorithm for multilinear functions that satisfy the one-sided σ-smoothness condition.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.63"}, {"primary_key": "2237360", "vector": [], "sparse_vector": [], "title": "Improved Deterministic Network Decomposition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Network decomposition is a central tool in distributed graph algorithms. We present two improvements on the state of the art for network decomposition, which thus lead to improvements in the (deterministic and randomized) complexity of several well-studied graph problems.- We provide a deterministic distributed network decomposition algorithm with O(log5 n) round complexity, using O(log n)-bit messages. This improves on the O(log7 n)-round algorithm of Rozhoň and Ghaffari [STOC'20], which used large messages, and their O(log8 n)-round algorithm with O(log n)-bit messages. This directly leads to similar improvements for a wide range of deterministic and randomized distributed algorithms, whose solution relies on network decomposition, including the general distributed derandomization of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> [FOCS'18].- One drawback of the algorithm of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, in the CONGEST model, was its dependence on the length of the identifiers. Because of this, for instance, the algorithm could not be used in the shattering framework in the CONGEST model. Thus, the state of the art randomized complexity of several problems in this model remained with an additive term, which was a clear leftover of the older network decomposition complexity [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> STOC'92]. We present a modified version that remedies this, constructing a decomposition whose quality does not depend on the identifiers, and thus improves the randomized round complexity for various problems.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.173"}, {"primary_key": "2237361", "vector": [], "sparse_vector": [], "title": "A Time-Optimal Randomized Parallel Algorithm for MIS.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a randomized parallel algorithm, in the Exclusive-Read Exclusive-Write (EREW) PRAM model, that computes a Maximal Independent Set (MIS) in O(log n) time and using O(m log2 n) work, with high probability. Thus, MIS ∊ RNC1. This time complexity is optimal and it improves on the celebrated O(log2 n) time algorithms of <PERSON><PERSON> [STOC'85] and <PERSON><PERSON>, Baba<PERSON>, and <PERSON>ai [JALG'86], which had remained the state of the art for the past 35 years.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.172"}, {"primary_key": "2237362", "vector": [], "sparse_vector": [], "title": "Approximation Algorithms and Hardness for Strong Unique Games.", "authors": ["Suprovat Ghoshal", "<PERSON>"], "summary": "The Unique Games problem is a central problem in algorithms and complexity theory. Given an instance of Unique Games, the Strong Unique Games problem asks to find the largest subset of vertices, such that the Unique Games instance induced on them is completely satisfiable. In this work, we give new algorithmic and hardness results for the Strong Unique Games problem. Given an instance with label set size k where a set of 1 – ∊ fraction of the vertices induce an instance that is completely satisfiable, our first algorithm produces a set of fraction of the vertices such that the Unique Games induced on them is completely satisfiable. In the same setting, our second algorithm produces a set of (here d is the largest vertex degree of the graph) fraction of the vertices such that the Unique Games induced on them is completely satisfiable. The technical core of our results is a new connection between Strong Unique Games and small-set vertex-expansion in graphs. Complementing this, assuming the Unique Games conjecture, we prove that there exists an absolute constant C such that it is NP-hard to compute a set of size larger than such that all the constraints induced on this set are satisfied.Given an undirected graph G(V, E) the Odd cycle transversal problem, asks to delete the least fraction of vertices to make the induced graph on the remaining vertices bipartite. As a corollary to our main algorithmic results, we obtain an algorithm that outputs a set S such the graph induced on V \\ S is bipartite, and (here d is the largest vertex degree and ∊ is the optimal fraction of vertices that need to be deleted). Assuming the Unique Games conjecture, we prove a matching (up to constant factors) hardness.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.26"}, {"primary_key": "2237363", "vector": [], "sparse_vector": [], "title": "Spectral Clustering Oracles in Sublinear Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Given a graph G that can be partitioned into k disjoint expanders with outer conductance upper bounded by ∊ « 1, can we efficiently construct a small space data structure that allows quickly classifying vertices of G according to the expander (cluster) they belong to? Formally, we would like an efficient local computation algorithm that misclassifies at most an O(∊) fraction of vertices in every expander. We refer to such a data structure as a spectral clustering oracle.Our main result is a spectral clustering oracle with query time O∗(n1/2+O(∊)) and preprocessing time that provides misclassification error O(∊ log k) per cluster for any ∊ « 1/log k. More generally, query time can be reduced at the expense of increasing the preprocessing time appropriately (as long as the product is about n1+O(∊)) – this in particular gives a nearly linear time spectral clustering primitive.The main technical contribution is a sublinear time oracle that provides dot product access to the spectral embedding of G by estimating distributions of short random walks from vertices in G. The distributions themselves provide a poor approximation to the spectral embedding, but we show that an appropriate linear transformation can be used to achieve high precision dot product access. We give an estimator for this linear transformation and analyze it using spectral perturbation bounds and a novel upper bound on the leverage scores of the spectral embedding matrix of a k-clusterable graph. We then show that dot product access to the spectral embedding is sufficient to design a clustering oracle. At a high level our approach amounts to hyperplane partitioning in the spectral embedding of G, but crucially operates on a nested sequence of carefully defined subspaces in the spectral embedding to achieve per cluster recovery guarantees.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.97"}, {"primary_key": "2237364", "vector": [], "sparse_vector": [], "title": "Atomic Power in Forks: A Super-Logarithmic Lower Bound for Implementing Butterfly Networks in the Nonatomic Binary Fork-Join Model.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove an Ω (log n log log n) lower bound for the span of implementing the n input, log n-depth FFT circuit (also known as butterfly network) in the nonatomic binary fork-join model. In this model, memory-access synchronizations occur only through fork operations, which spawn two child threads, and join operations, which resume a parent thread when its child threads terminate. Our bound is asymptotically tight for the nonatomic binary fork-join model, which has been of interest of late, due to its conceptual elegance and ability to capture asynchrony. Our bound implies super-logarithmic lower bound in the nonatomic binary fork-join model for implementing the butterfly merging networks used, e.g., in <PERSON><PERSON>'s bitonic and odd-even mergesort networks. This lower bound also implies an asymptotic separation result for the atomic and nonatomic versions of the fork-join model, since, as we point out, FFT circuits can be implemented in the atomic binary fork-join model with span equal to their circuit depth.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.128"}, {"primary_key": "2237365", "vector": [], "sparse_vector": [], "title": "The Expander Hierarchy and its Applications to Dynamic Graph Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Thatchaphol <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a notion for hierarchical graph clustering which we call the expander hierarchy and show a fully dynamic algorithm for maintaining such a hierarchy on a graph with n vertices undergoing edge insertions and deletions using no(1) update time. An expander hierarchy is a tree representation of graphs that faithfully captures the cut-flow structure and consequently our dynamic algorithm almost immediately implies several results including:1.The first fully dynamic algorithm with no(1) worst-case update time that allows querying no(1)-approximate conductance, s-t maximum flows, and s-t minimum cuts for any given (s, t) in O(log1/6 n) time. Our results are deterministic and extend to multi-commodity cuts and flows. All previous fully dynamic (or even decremental) algorithms for any of these problems take Ω(n) update or query time. The key idea behind these results is a fully dynamic algorithm for maintaining a tree flow sparsifier, a notion introduced by <PERSON><PERSON><PERSON> [FOCS'02] for constructing competitive oblivious routing schemes.2.A deterministic fully dynamic connectivity algorithm with no(1) worst-case update time. This significantly simplifies the recent algorithm by <PERSON><PERSON><PERSON><PERSON> et al. that uses the framework of Nanongkai, Saranurak, and Wulff<PERSON><PERSON><PERSON>en [FOCS'17].3.A deterministic fully dynamic treewidth decomposition algorithm on constant-degree graphs with no(1) worst-case update time that maintains a treewidth decomposition of width tw(G) · no(1) where tw(G) denotes the treewidth of the current graph. This is the first non-trivial dynamic algorithm for this problem.Our technique is based on a new stronger notion of the expander decomposition, called the boundary-linked expander decomposition. This decomposition is more robust against updates and better captures clustering structure of graphs compared to the standard expander decomposition. Given that the expander decomposition has proved extremely useful in many fields, including approximation, sketching, distributed, and dynamic algorithms, we expect that our new notion will find more future applications.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.132"}, {"primary_key": "2237366", "vector": [], "sparse_vector": [], "title": "Algorithms for Persuasion with Limited Communication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Bayesian persuasion paradigm of strategic communication models interaction between a privately-informed agent, called the sender, and an ignorant but rational agent, called the receiver. The goal is typically to design a (near-)optimal communication (or signaling) scheme for the sender. It enables the sender to disclose information to the receiver in a way as to incentivize her to take an action that is preferred by the sender. Finding the optimal signaling scheme is known to be computationally difficult in general. This hardness is further exacerbated when there is also a constraint on the size of the message space, leading to NP-hardness of approximating the optimal sender utility within any constant factor.In this paper, we show that in several natural and prominent cases the optimization problem is tractable even when the message space is limited. In particular, we study signaling under a symmetry or an independence assumption on the distribution of utility values for the actions. For symmetric distributions, we provide a novel characterization of the optimal signaling scheme. It results in a polynomial-time algorithm to compute an optimal scheme for many compactly represented symmetric distributions. In the independent case, we design a constant-factor approximation algorithm, which stands in marked contrast to the hardness of approximation in the general case.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.39"}, {"primary_key": "2237367", "vector": [], "sparse_vector": [], "title": "Algorithms for weighted independent transversals and strong colouring.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "An independent transversal (IT) in a graph with a given vertex partition is an independent set consisting of one vertex in each partition class. Several sufficient conditions are known for the existence of an IT in a given graph with a given vertex partition, which have been used over the years to solve many combinatorial problems. Some of these IT existence theorems have algorithmic proofs, but there remains a gap between the best existential bounds and the bounds obtainable by efficient algorithms.Recently, <PERSON> and <PERSON> (2018) described a new (deterministic) algorithm that asymptotically closes this gap, but there are limitations on its applicability. In this paper we develop a randomized algorithm that is much more widely applicable, and demonstrate its use by giving efficient algorithms for two problems concerning the strong chromatic number of graphs.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.41"}, {"primary_key": "2237368", "vector": [], "sparse_vector": [], "title": "All-Pairs LCA in DAGs: Breaking through the O(n2.5) barrier.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Giuseppe F<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Przemys<PERSON>nanski"], "summary": "Let G = (V, E) be an n-vertex directed acyclic graph (DAG). A lowest common ancestor (LCA) of two vertices u and v is a common ancestor w of u and v such that no descendant of w has the same property. In this paper, we consider the problem of computing an LCA, if any, for all pairs of vertices in a DAG. The fastest known algorithms for this problem exploit fast matrix multiplication subroutines and have running times ranging from (n2.687) [<PERSON><PERSON> et al. SODA'01] down to (n2.615) [<PERSON><PERSON><PERSON> and Lingas ICALP'05] and (n2.569) [<PERSON><PERSON><PERSON><PERSON> et al. TCS'07]. Somewhat surprisingly, all those bounds would still be Ω(n2.5) even if matrix multiplication could be solved optimally (i.e., ω = 2). This appears to be an inherent barrier for all the currently known approaches, which raises the natural question on whether one could break through the (n2.5) barrier for this problem.In this paper, we answer this question affirmatively: in particular, we present an for ω = 2) algorithm for finding an LCA for all pairs of vertices in a DAG, which represents the first improvement on the running times for this problem in the last 13 years. A key tool in our approach is a fast algorithm to partition the vertex set of the transitive closure of G into a collection of (ℓ) chains and (n/ℓ) antichains, for a given parameter ℓ. As usual, a chain is a path while an antichain is an independent set. We then find, for all pairs of vertices, a candidate LCA among the chain and antichain vertices, separately. The first set is obtained via a reduction to (max, min) matrix multiplication. The computation of the second set can be reduced to Boolean matrix multiplication similarly to previous results on this problem. We finally combine the two solutions together in a careful (non-obvious) manner.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.18"}, {"primary_key": "2237369", "vector": [], "sparse_vector": [], "title": "Concentration bounds for almost k-wise independence with applications to non-uniform security.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Tsz Chiu Kwok", "<PERSON><PERSON><PERSON>"], "summary": "We prove a few concentration inequalities for the sum of n binary random variables under weaker conditions than k-wise independence. Namely, we consider two standard conditions that are satisfied in many applications: (a) direct product conditions (b) the XOR condition. Both conditions are weaker than mutual independence and both imply strong concentration bounds (similar to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) on the tail probability of the sum of bounded random variables ([Impagliazzo and Kabanets, APPROX-RANDOM 10], [<PERSON><PERSON>, FOCS 09]). Our inequalities can be stated as the implication of threshold direct product theorems from either k-wise direct product conditions, or the k-wise XOR condition. By proving optimality of our inequalities, we show a clear separation for k « n between k-wise product conditions and XOR condition as well as a stark contrast between k-wise and n-wise product theorems.We use these bounds in the cryptographic application that provides provable security against algorithms with S-bit advice. Namely, we show how the problem reduces to proving S-wise direct product theorems or S-wise XOR lemmas for certain ranges of parameters. Finally, we derive a new S-wise XOR lemma, which yields a tight non-uniform bound for length increasing pseudorandom generators, resolving a 10-year-old open problem from [<PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, CRYPTO 10].", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.143"}, {"primary_key": "2237370", "vector": [], "sparse_vector": [], "title": "Approximating Pathwidth for Graphs of Small Treewidth.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We describe a polynomial-time algorithm which, given a graph G with treewidth t, approximates the pathwidth of G to within a ratio of . This is the first algorithm to achieve an f(t)-approximation for some function f.Our approach builds on the following key insight: every graph with large pathwidth has large treewidth or contains a subdivision of a large complete binary tree. Specifically, we show that every graph with pathwidth at least th + 2 has treewidth at least t or contains a subdivision of a complete binary tree of height h + 1. The bound th + 2 is best possible up to a multiplicative constant. This result was motivated by, and implies (with c = 2), the following conjecture of <PERSON><PERSON> and <PERSON> (SODA'18): there exists a universal constant c such that every graph with pathwidth Ω(kc) has treewidth at least k or contains a subdivision of a complete binary tree of height k.Our main technical algorithm takes a graph G and some (not necessarily optimal) tree decomposition of G of width t′ in the input, and it computes in polynomial time an integer h, a certificate that G has pathwidth at least h, and a path decomposition of G of width at most (t′ + 1)h + 1. The certificate is closely related to (and implies) the existence of a subdivision of a complete binary tree of height h. The approximation algorithm for pathwidth is then obtained by combining this algorithm with the approximation algorithm of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON> (STOC'05) for treewidth.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.117"}, {"primary_key": "2237371", "vector": [], "sparse_vector": [], "title": "<PERSON>.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce the framework of Deep Weisfeiler Leman algorithms (DeepWL), which allows the design of purely combinatorial graph isomorphism tests that are more powerful than the well-known <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> algorithm.We prove that, as an abstract computational model, polynomial-time DeepWL-algorithms have exactly the same expressiveness as the logic Choiceless Polynomial Time (with counting) introduced by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (Ann. Pure Appl. Logic., 1999).It is a well-known open question whether the existence of a polynomial-time graph isomorphism test implies the existence of a polynomial-time canonisation algorithm. Our main technical result states that for each class of graphs (satisfying some mild closure condition), if there is a polynomial-time DeepWL isomorphism test, then there is a polynomial-time canonisation algorithm for this class. This implies that there is also a logic capturing polynomial time on this class.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.154"}, {"primary_key": "2237372", "vector": [], "sparse_vector": [], "title": "Improving the dilation of a metric graph by adding edges.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Most of the literature on spanners focuses on building the graph from scratch. This paper instead focuses on adding edges to improve an existing graph. A major open problem in this field is: given a graph embedded in a metric space, and a budget of k edges, which k edges do we add to produce a minimum-dilation graph? The special case where k = 1 has been studied in the past, but no major breakthroughs have been made for k > 1. We provide the first positive result, an O(k)-approximation algorithm that runs in O(n3 log n) time.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.42"}, {"primary_key": "2237373", "vector": [], "sparse_vector": [], "title": "The Connectivity Threshold for Dense Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>"], "summary": "Consider a random graph model where there is an underlying simple graph G = (V, E), and each edge is sampled independently with probability p ∊ [0, 1]. What is the smallest value of p such that the resulting graph Gp is connected with constant probability? This is a well-studied question for special classes of graphs, such as complete graphs and hypercubes. For instance, when G is the complete graph, we want the connectivity threshold for the Erdős-Rényi G(n, p) model: here the answer is known to be . However, the problem is not well-understood for more general graph classes.We first investigate this connectivity threshold problem for \"somewhat dense\" graphs. We show that for any and any δ-regular, δ-edge-connected graph G, the random graph Gp for is connected with probability , generalizing upon the case when G is the complete graph. Our proof also bounds the number of approximate mincuts in such a dense graph, which may be of independent interest.Next, for a general graph G with edge connectivity λ, we define an explicit parameter βG ∊ (0, 2 ln n], based on the number of approximate mincuts, and show that there is a sharp transition in the connectivity of G at p = 1 – exp(βG/λ). Moreover, we show that the width of this transition is an additive O(ln λ/λ) term; this improves upon <PERSON><PERSON><PERSON>' classical result bounding the width of the threshold by .", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.7"}, {"primary_key": "2237374", "vector": [], "sparse_vector": [], "title": "Explicit two-deletion codes with redundancy matching the existential bound.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "We give an explicit construction of length-n binary codes capable of correcting the deletion of two bits that have size 2n/n4+o(1). This matches up to lower order terms the existential result, based on an inefficient greedy choice of codewords, that guarantees such codes of size Ω(2n/n4). Our construction is based on augmenting the classic <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> construction of single deletion codes with additional check equations. We also give an explicit construction of binary codes of size Ω(2n/n3+o(1)) that can be list decoded from two deletions using lists of size two. Previously, even the existence of such codes was not clear.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.2"}, {"primary_key": "2237375", "vector": [], "sparse_vector": [], "title": "On the Competitive Analysis and High Accuracy Optimality of Profile Maximum Likelihood.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A striking result of <PERSON><PERSON> et al. [ADOS<PERSON>] showed that to estimate symmetric properties of discrete distributions, plugging in the distribution that maximizes the likelihood of observed multiset of frequencies, also known as the profile maximum likelihood (PML) distribution, is competitive compared with any estimators regardless of the symmetric property. Specifically, given n observations from the discrete distribution, if some estimator incurs an error ∊ with probability at most δ, then plugging in the PML distribution incurs an error 2∊ with probability at most . In this paper, we strengthen the above result and show that using a careful chaining argument, the error probability can be reduced to δ1 – c · exp(c′n1/3 + c) for arbitrarily small constants c > 0 and some constant c′ > 0. The improved competitive analysis leads to the optimality of the PML plug-in approach for estimating various symmetric properties within higher accuracy ∊ ≫ n–1/3. In particular, we show that the PML distribution is an optimal estimator of the sorted distribution: it is ∊-close in sorted ℓ1 distance to the true distribution with support size k for any n = Ω(k/(∊2 log k)) and ∊ ≫ n–1/3, which are the information-theoretically optimal sample complexity and the largest error regime where the classical empirical distribution is sub-optimal, respectively.In order to strengthen the analysis of the PML, a key ingredient is to employ novel “continuity” properties of the PML distributions and construct a chain of suitable quantized PMLs, or “coverings”. We also construct a novel approximation-based estimator for the sorted distribution with a near-optimal concentration property without any sample splitting, where as a byproduct we obtain better trade-offs between the polynomial approximation error and the maximum magnitude of coefficients in the Poisson approximation of 1-Lipschitz functions.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.80"}, {"primary_key": "2237376", "vector": [], "sparse_vector": [], "title": "Non-linear Hamilton cycles in linear quasi-random hypergraphs.", "authors": ["<PERSON><PERSON>", "Xichao Shu", "<PERSON><PERSON><PERSON>"], "summary": "A k-graph H is called (p, μ)-dense if for all not necessarily disjoint sets A1, …, Ak ⊆ V(H) we have e(A1, …, Ak) ≥ p|A1| ⃛ |Ak| – μ|V(H)|k. This is believed to be the weakest form of quasi-randomness in k-graphs and also known as linear quasi-randomness.In this paper we show that for ℓ < k satisfying (k – ℓ) ∤ k, (p, μ)-denseness plus a minimum (ℓ + 1)-vertex-degree αnk–ℓ–1 guarantees Hamilton ℓ-cycles, but requiring a minimum ℓ-vertex-degree Ω(nk–ℓ) instead is not sufficient. This answers a question of <PERSON><PERSON><PERSON><PERSON> and characterizes the triples (k, ℓ, d) such that degenerate choices of p and α force ℓ-Hamiltonicity.We actually prove a general result on ℓ-Hamiltonicity in quasi-random k-graphs, assuming a minimum vertex degree and essentially that every two ℓ-sets can be connected by a constant length ℓ-path. This result reduces the ℓ-Hamiltonicity problem to the study of the connection property. Moreover, we note that our proof can be turned into a deterministic polynomial-time algorithm that outputs the Hamilton ℓ-cycle. Our proof uses the lattice-based absorption method in the non-standard way and is the first one that embeds a nonlinear Hamilton cycle in linear quasi-random k-graphs.MSC codesQuasirandom hypergraphsHamilton cyclesHypergraph regularity methodAbsorbing", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.6"}, {"primary_key": "2237377", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Online Graph Partitioning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the following online optimization problem. We are given a graph G and each vertex of the graph is assigned to one of ℓ servers, where servers have capacity k and we assume that the graph has ℓ · k vertices. Initially, G does not contain any edges and then the edges of G are revealed one-by-one. The goal is to design an online algorithm ONL, which always places the connected components induced by the revealed edges on the same server and never exceeds the server capacities by more than ∊k for constant ∊ > 0. Whenever ONL learns about a new edge, the algorithm is allowed to move vertices from one server to another. Its objective is to minimize the number of vertex moves. More specifically, ONL should minimize the competitive ratio: the total cost ONL incurs compared to an optimal offline algorithm OPT.The problem was recently introduced by <PERSON><PERSON><PERSON> et al. (SIGMETRICS'2019) and is related to classic online problems such as online paging and scheduling. It finds applications in the context of resource allocation in the cloud and for optimizing distributed data structures such as union–find data structures.Our main contribution is a polynomial-time randomized algorithm, that is asymptotically optimal: we derive an upper bound of O(log ℓ + log k) on its competitive ratio and show that no randomized online algorithm can achieve a competitive ratio of less than Ω(log ℓ + log k). We also settle the open problem of the achievable competitive ratio by deterministic online algorithms, by deriving a competitive ratio of Θ(ℓ log k); to this end, we present an improved lower bound as well as a deterministic polynomial-time online algorithm.Our algorithms rely on a novel technique which combines efficient integer programming with a combinatorial approach for maintaining ILP solutions. More precisely, we use an ILP to assign the connected components induced by the revealed edges to the servers; this is similar to existing approximation schemes for scheduling algorithms. However, we cannot obtain our competitive ratios if we run the ILP after each edge insertion. Instead, we identify certain types of edge insertions, after which we can manually obtain an optimal ILP solution at zero cost without resolving the ILP. We believe this technique is of independent interest and will find further applications in the future.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.166"}, {"primary_key": "2237378", "vector": [], "sparse_vector": [], "title": "Nearly Optimal Average-Case Complexity of Counting Bicliques Under SETH.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we seek a natural problem and a natural distribution of instances such that any O(nc–∊) time algorithm fails to solve most instances drawn from the distribution, while the problem admits an nc+o(1)-time algorithm that correctly solves all instances. Specifically, we consider the Ka,b counting problem in a random bipartite graph, where Ka,b is a complete bipartite graph and a and b are constants. Our distribution consists of the binomial random bipartite graphs Bαn,βn with edge density 1/2, where α and β are drawn uniformly at random from {1, …, a} and {1, …, b}, respectively. We determine the nearly optimal average-case complexity of this counting problem by proving the following results.Conditional Tight Worst-Case Complexity. Under the Strong Exponential Time Hypothesis, for any constants a ≥ 3 and ∊ > 0, there exists a constant b = b(a, ∊) such that no O(na–∊)-time algorithm counts the number of Ka,b subgraphs in a given n-vertex graph. On the other hand, for any constant a ≥ 8 and any b = b(n), we can count all Ka,b subgraphs in time bna+o(1).Worst-to-Average Reduction. If there exists a T(n)-time randomized heuristic algorithm that solves the Ka,b subgraph counting problem on a random graph Bαn,βn with success probability 1 — 1/polylog(n), then there exists a T(n)polylog(n)-time randomized algorithm that solves the Ka,b subgraph counting problem for any input with success probability 2/3.Fine-Grained Hardness Amplification. Suppose that there is a T(n)-time algorithm with success probability n–∊ that computes the parity of the number of Ka,b subgraphs in H, where is the disjoint union of k = O(∊ log n) i.i.d. random graphs G1, …, Gk each of which is drawn from the distribution of Bαn,βn. Then there is a T(n)nO(∊)-time randomized algorithm that counts Ka,b subgraphs for any input with success probability 2/3.The central idea behind these results is colorful subgraphs. For the first result, we reduce the k-Orthogonal Vectors problem to the colorful Ka,b detection problem. In the second result, we establish a worst-case-to-average-case reduction for a colorful subgraph counting problem based on the binary-extension technique given by [Boix-Adserà, Brennan, and Bresler; FOCS19]. Then, we reduce colorful Ka,b counting to Ka,b counting. Regarding the third result, we prove the classical XOR lemma and the direct product theorem in the fine-grained setting for subgraph counting problems. The core of the proof is an O(log n)-round doubly-efficient interactive proof system for the colorful subgraph counting problem such that the honest prover is asked to solve polylog(n) instances of the counting problem. The new protocol improves the known interactive proof system for the t-clique counting problem given by [Goldreich and Rothblum; FOCS18] in terms of query complexity.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.140"}, {"primary_key": "2237379", "vector": [], "sparse_vector": [], "title": "PTAS for Minimum Cost Multi-covering with Disks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we study the following Minimum Cost Multi-Covering (MCMC) problem: Given a set of n client points C and a set of m server points S in a fixed dimensional ℝd space, determine a set of disks centered at these server points so that each client point c is covered by at least k(c) disks and the total cost of these disks is minimized, where k(-) is a function that maps every client point to some non-negative integer no more than m and the cost of each disk is measured by the α-th power of its radius for some constant α > 0. MCMC is a fundamental optimization problem with applications in many areas such as wireless/sensor networking. Despite extensive research on this problem in the past two decades, only constant approximations were known for general k. It has been an open problem for a long time to determine whether a PTAS is possible. In this paper, we give an affirmative answer to this question by presenting the first PTAS for it. Our approach is based on a number of novel techniques, such as Balanced Recursive Realization and Bubble Charging, and new insights to the problem which are somewhat counter-intuitive. Particularly, we show that instead of optimizing each disk as a whole, it is possible to further approximate each disk with a set of sub-boxes and optimize them at the sub-disk level. This allows us to first compute an approximate disk cover with minimum cost through dynamic programming, and then obtain the desired disk cover through a balanced recursive realization procedure. Our techniques have the potential to be used to other geometric (covering) problems.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.53"}, {"primary_key": "2237380", "vector": [], "sparse_vector": [], "title": "An improved procedure for colouring graphs of bounded local density.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We develop an improved bound for the chromatic number of graphs of maximum degree Δ under the assumption that the number of edges spanning any neighbourhood is at most for some fixed 0 < σ < 1. The leading term in the reduction of colours achieved through this bound is best possible as σ → 0. As two consequences, we advance the state of the art in two longstanding and well-studied graph colouring conjectures, the Erdős-Nešet<PERSON> conjecture and <PERSON>'s conjecture. We prove that the strong chromatic index is at most 1.772Δ2 for any graph G with sufficiently large maximum degree Δ. We prove that the chromatic number is at most ⌈0.881(Δ + 1) + 0.119ω⌉ for any graph G with clique number ω and sufficiently large maximum degree Δ. Additionally, we show how our methods can be adapted under the additional assumption that the codegree is at most (1 – σ) Δ, and establish what may be considered first progress towards a conjecture of Vu.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.10"}, {"primary_key": "2237381", "vector": [], "sparse_vector": [], "title": "Planar Reachability Under Single Vertex or Edge Failures.", "authors": ["Giuseppe F<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we present an efficient reachability oracle under single-edge or single-vertex failures for planar directed graphs. Specifically, we show that a planar digraph G can be preprocessed in O(n log2 n/log log n) time, producing an O(n log n)-space data structure that can answer in O(log n) time whether u can reach v in G if the vertex x (the edge f) is removed from G, for any query vertices u, v and failed vertex x (failed edge f). To the best of our knowledge, this is the first data structure for planar directed graphs with nearly optimal preprocessing time that answers all-pairs queries under any kind of failures in polylogarithmic time.We also consider 2-reachability problems, where we are given a planar digraph G and we wish to determine if there are two vertex-disjoint (edge-disjoint) paths from u to v, for query vertices u, v. In this setting we provide a nearly optimal 2-reachability oracle, which is the existential variant of the reachability oracle under single failures, with the following bounds. We can construct in O(n poly log n) time an O(n log3+o(1) n)-space data structure that can check in O(log2+o(1) n) time for any query vertices u, v whether v is 2-reachable from u, or otherwise find some separating vertex (edge) x lying on all paths from u to v in G.To obtain our results, we follow the general recursive approach of Thorup for reachability in planar graphs [J. ACM '04] and we present new data structures which generalize dominator trees and previous data structures for strong-connectivity under failures [Georgiadis et al., SODA '17]. Our new data structures work also for general digraphs and may be of independent interest.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.163"}, {"primary_key": "2237382", "vector": [], "sparse_vector": [], "title": "Optimal Oblivious Priority Queues.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we present the first asymptotically optimal oblivious priority queue, which matches the lower bound of <PERSON>, <PERSON>, and <PERSON> (SODA'19). Our construction is conceptually simple and statistically secure. We illustrate the power of our optimal oblivious priority queue by presenting a conceptually equally simple construction of statistically secure offline ORAMs with O(log n) bandwidth overhead.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.141"}, {"primary_key": "2237383", "vector": [], "sparse_vector": [], "title": "Ultrasparse Ultrasparsifiers and Faster Laplacian System Solvers.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we provide an O(mloglogO(1) n log(1/∊))-expected time algorithm for solving Laplacian systems on n-node m-edge graphs, improving improving upon the previous best expected runtime of achieved by (<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> 2014). To obtain this result we provide efficient constructions of ℓp-stretch graph approximations with improved stretch and sparsity bounds. Additionally, as motivation for this work, we show that for every set of vectors in ℝd (not just those induced by graphs) and all k > 0 there exist an ultra-sparsifiers with d – 1 + O(d/k) re-weighted vectors of relative condition number at most k2. For small k, this improves upon the previous best known multiplicative factor of k · Õ(log d), which is only known for the graph case.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.33"}, {"primary_key": "2237384", "vector": [], "sparse_vector": [], "title": "Approximating Permanent of Random Matrices with Vanishing Mean: Made Better and Simpler.", "authors": ["Zhengfeng Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The algorithm and complexity of approximating the permanent of a matrix is an extensively studied topic. Recently, its connection with quantum supremacy and more specifically BosonSampling draws a special attention to the average-case approximation problem of the permanent of random matrices with zero or small mean value for each entry. <PERSON><PERSON> and <PERSON><PERSON> (FOCS 2018) gave a quasi-polynomial time algorithm for random matrices with mean at least 1/polyloglog(n). In this paper, we improve the result by designing a deterministic quasi-polynomial time algorithm and a PTAS for random matrices whose module of mean is at least 1/ polylog(n). We note that if the algorithm can be further improved to work with a mean value that is a sufficiently small 1/poly(n), it will disprove a central conjecture for quantum supremacy.Our algorithm is also much simpler and has a better and flexible trade-off for running time. The running time can be quasi-polynomial in both n and 1/∊, or PTAS (polynomial in n but exponential in 1/∊), where ∊ is the approximation parameter.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.60"}, {"primary_key": "2237385", "vector": [], "sparse_vector": [], "title": "Minimizing Convex Functions with Integral Minimizers.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Given a separation oracle SO for a convex function f that has an integral minimizer inside a box with radius R, we show how to efficiently find a minimizer of f using at most O(n(n + log(R))) calls to SO. When the set of minimizers of f has integral extreme points, our algorithm outputs an integral minimizer of f. This improves upon the previously best oracle complexity of O(n2(n + log(R))) obtained by an elegant application of simultaneous diophantine approximation due to [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, Prog. Comb. Opt. 1984, <PERSON> 1988] over thirty years ago. We conjecture that our oracle complexity is tight up to constant factors.Our result immediately implies a strongly polynomial algorithm for the Submodular Function Minimization problem that makes at most O(n3) calls to an evaluation oracle. This improves upon the previously best O(n3 log2(n)) oracle complexity for strongly polynomial algorithms given in [<PERSON>, <PERSON> and <PERSON>, FOC<PERSON> 2015] and [<PERSON>, <PERSON> and <PERSON>, SODA 2018], and an exponential time algorithm with oracle complexity O(n3 log(n)) given in the former work, answering two open problems posted therein.Our result is achieved by an application of the LLL algorithm [<PERSON><PERSON>, <PERSON> and <PERSON>, Math. Ann. 1982] for the shortest lattice vector problem. We show how an approximately shortest vector of certain lattice can be used to reduce the dimension of the problem, and how the oracle complexity of such a procedure is advantageous compared with the <PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>hrijver approach that uses simultaneous diophantine approximation. Our analysis of the oracle complexity is based on a potential function that captures simultaneously the size of the search set and the density of the lattice. To achieve the O(n2) term in the oracle complexity, technical ingredients from convex geometry are applied.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.61"}, {"primary_key": "2237386", "vector": [], "sparse_vector": [], "title": "Fast Low-Space Algorithms for Subset Sum.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the canonical Subset Sum problem: given a list of positive integers a1, …, an and a target integer t with t > ai for all i, determine if there is an S ⊆ [n] such that Σi∊S ai = t. The well-known pseudopolynomialtime dynamic programming algorithm [<PERSON><PERSON>, 1957] solves Subset Sum in O(nt) time, while requiring Ω(t) space.In this paper we present algorithms for Subset Sum with Õ(nt) running time and much lower space requirements than <PERSON><PERSON>'s algorithm, as well as that of prior work. We show that Subset Sum can be solved in Õ(nt) time and O(log(nt)) space with access to O(log n log log n + log t) random bits. This significantly improves upon the Õ(nt1+∊)-time, Õ(n log t)-space algorithm of <PERSON><PERSON> (SODA 2017). We also give a Õ(n1+∊t)-time, O(log(nt))-space randomized algorithm, improving upon previous (nt)O(1)-time O(log(nt))-space algorithms by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (FOCS 2010), and <PERSON> (2010). In addition, we also give a poly log(nt)-space, Õ(n2t)-time deterministic algorithm.We also study time-space trade-offs for Subset Sum. For parameter 1 ≤ k ≤ min{n, t}, we present a randomized algorithm running in Õ((n+t) · k) time and O((t/k) poly log(nt)) space.As an application of our results, we give an Õ(min{n2/∊, n/∊2})-time and poly log(nt)-space algorithm for \"weak\" ∊-approximations of Subset Sum.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.106"}, {"primary_key": "2237387", "vector": [], "sparse_vector": [], "title": "Robust Learning of Mixtures of Gaussians.", "authors": ["<PERSON>"], "summary": "We resolve one of the major outstanding problems in robust statistics. In particular, if X is an evenly weighted mixture of two arbitrary d-dimensional Gaussians, we devise a polynomial time algorithm that given access to samples from X an ∊-fraction of which have been adversarially corrupted, learns X to error poly(∊) in total variation distance.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.76"}, {"primary_key": "2237388", "vector": [], "sparse_vector": [], "title": "In which matching markets does the short side enjoy an advantage?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We revisit the popular random matching market model introduced by <PERSON><PERSON><PERSON> (1976) and <PERSON><PERSON> (1989), and shown by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2013) to exhibit a \"stark effect of competition\"; in particular, with any difference in the number of agents on the two sides (\"imbalance\"), the short side agents obtain substantially better outcomes. We generalize the model to allow \"partially connected\" markets with each agent having an average degree d in a random (undirected) graph. Each agent has a (uniformly random) preference ranking over only their neighbors in the graph. We characterize stable matchings in large markets and find that the short side enjoys a significant advantage only for d exceeding log2 n where n is the number of agents on one side: For moderately connected markets with d = o(log2 n), we find that there is no advantage to being on the short side (for O(n1–∊) market imbalance), with agents on both sides getting a -ranked partner on average. Notably, this \"mild competition\" regime extends far beyond the connectivity threshold of d = Θ(log n). In contrast, for densely connected markets with d = ω(log2 n), we find a strong effect of competition, namely, short side agents get a log n-ranked partner on average, while the long side agents get a partner of (much larger) rank d/log n on average. Our results and analysis suggest that in general matching markets, being on the short side confers an advantage if and only if the number of short-side agents who remain unmatched is small relative to the market imbalance.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.83"}, {"primary_key": "2237389", "vector": [], "sparse_vector": [], "title": "Space Lower Bounds for Approximating Maximum Matching in the Edge Arrival Model.", "authors": ["<PERSON>"], "summary": "The bipartite matching problem in the online and streaming settings has received a lot of attention recently. The classical vertex arrival setting, for which the celebrated <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (KVV) algorithm achieves a 1 – 1/e approximation, is rather well understood: the 1 – 1/e approximation is optimal in both the online and semi-streaming setting, where the algorithm is constrained to use n · logO(1) n space. The more challenging the edge arrival model has seen significant progress recently in the online algorithms literature. For the strictly online model (no preemption) approximations better than trivial factor 1/2 have been ruled out [<PERSON><PERSON><PERSON><PERSON> et al'FOCS'19]. For the less restrictive online preemptive model a better than -approximation [<PERSON><PERSON><PERSON> et al'STACS'12] and even a better than -approximation[<PERSON> et al'SODA'19] have been ruled out.The recent hardness results for online preemptive matching in the edge arrival model are based on the idea of stringing together multiple copies of a KVV hard instance using edge arrivals. In this paper, we show how to implement such constructions using ideas developed in the literature on Ruzsa-Szemerédi graphs. As a result, we show that any single pass streaming algorithm that approximates the maximum matching in a bipartite graph with n vertices to a factor better than requires n1+Ω(1/ log log n) » n logO(1) n space. This gives the first separation between the classical one sided vertex arrival setting and the edge arrival setting in the semi-streaming model.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.112"}, {"primary_key": "2237390", "vector": [], "sparse_vector": [], "title": "A Deterministic Parallel APSP Algorithm and its Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we show a deterministic parallel all-pairs shortest paths algorithm for real-weighted directed graphs.The algorithm has Õ(nm + (n/d)3) work and Õ(d) depth for any depth parameter d ∊ [1, n]. To the best of our knowledge, such a trade-off has only been previously described for the real-weighted single-source shortest paths problem using randomization [<PERSON><PERSON> et al., ICALP'17]. Moreover, our result improves upon the parallelism of the state-of-the-art randomized parallel algorithm for computing transitive closure, which has Õ(nm + n3/d2) work and Õ(d) depth [<PERSON><PERSON><PERSON> and <PERSON>, SIAM J. Comput. '91].Our APSP algorithm turns out to be a powerful tool for designing efficient planar graph algorithms in both parallel and sequential regimes. By suitably adjusting the depth parameter d and applying known techniques, we obtain:•nearly work-efficient Õ(n1/6)-depth parallel algorithms for the real-weighted single-source shortest paths problem and finding a bipartite perfect matching in a planar graph,•an Õ(n9/8)-time sequential strongly polynomial algorithm for computing a minimum mean cycle or a minimum cost-to-time-ratio cycle of a planar graph,•a slightly faster algorithm for computing so-called external dense distance graphs of all pieces of a recursive decomposition of a planar graph.One notable ingredient of our parallel APSP algorithm is a simple deterministic Õ(nm)-work Õ(d)-depth procedure for computing Õ(n/d)-size hitting sets of shortest d-hop paths between all pairs of vertices of a real-weighted digraph. Such hitting sets have also been called d-hub sets. Hub sets have previously proved especially useful in designing parallel or dynamic shortest paths algorithms and are typically obtained via random sampling. Our procedure implies, for example, an Õ(nm)-time deterministic algorithm for finding a shortest negative cycle of a real-weighted digraph. Such a near-optimal bound for this problem has been so far only achieved using a randomized algorithm [Orlin et al., Discret. Appl. Math. '18].", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.17"}, {"primary_key": "2237391", "vector": [], "sparse_vector": [], "title": "How Many Vertices Does a Random Walk Miss in a Network with Moderately Increasing the Number of Vertices?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Real networks are often dynamic. In response to it, analyses of algorithms on dynamic networks attract more and more attention in network science and engineering. Random walks on dynamic graphs also have been investigated actively in more than a decade, where in most cases the edge set changes but the vertex set is static. The vertex sets are also dynamic in many real networks. Motivated by a new technology of the analysis of random walks on dynamic graphs, this paper introduces a simple model of graphs with an increasing number of vertices and presents an analysis of random walks associated with the cover time on such graphs. In particular, we reveal that a random walk asymptotically covers the vertices all but a constant number if the vertex set grows moderately.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.8"}, {"primary_key": "2237392", "vector": [], "sparse_vector": [], "title": "Solving hard cut problems via flow-augmentation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a new technique for designing fixed-parameter algorithms for graph cut problems in undirected graphs, which we call flow augmentation. Our technique is applicable to problems that can be phrased as a search for an (edge) (s, t)-cut of cardinality at most k in an undirected graph G with designated terminals s and t.More precisely, we consider problems where an (unknown) solution is a set Z ⊆ E(G) of size at most k such that•in G – Z, s and t are in distinct connected components,•every edge of Z connects two distinct connected components of G – Z, and•if we define the set Zs, t ⊆ Z as those edges e ∊ Z for which there exists an (s, t)-path Pe with E(Pe) ∩ Z = {e}, then Zs, t separates s from t.We prove that in the above scenario one can in randomized time k(1) (|V(G)| + |E(G)|) add a number of edges to the graph so that with probably at least 2–(k log k) no added edge connects two components of G – Z, and Zs, t becomes a minimum cut between s and t.This additional property becomes a handy lever in applications. For example, consider the question of an (s, t)-cut of cardinality at most k and of minimum possible weight (assuming edge weights in G). While the problem is NP-hard in general, it easily reduces to the maximum flow / minimum cut problem if we additionally assume that k is the minimum possible cardinality of an (s, t)-cut in G. Hence, we immediately obtain that the aforementioned problem admits an 2(k log k) n(1)-time randomized fixed-parameter algorithm.We apply our method to obtain a randomized fixed-parameter algorithm for a notorious \"hard nut\" graph cut problem we call Coupled Min-Cut. This problem emerges out of the study of FPT algorithms for Min CSP problems (see below), and was unamenable to other techniques for parameterized algorithms in graph cut problems, such as Randomized Contractions, Treewidth Reduction or Shadow Removal.In fact, we go one step further. To demonstrate the power of the approach, we consider more generally the Boolean Min CSP(Γ)-problems, a.k.a. Min SAT(Γ), parameterized by the solution cost. This is a framework of optimization problems that includes problems such as Almost 2-SAT and the notorious i-Chain SAT problem. We are able to show that every problem Min SAT(Γ) is either (1) FPT, (2) W[1]-hard, or (3) able to express the soft constraint (u → v), and thereby also the min-cut problem in directed graphs. All the W[1]-hard cases were known or immediate, and the main new result is an FPT algorithm for a generalization of Coupled Min-Cut. In other words, flow-augmentation is powerful enough to let us solve every fixed-parameter tractable problem in the class, except those that explicitly encompass directed graph cuts.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.11"}, {"primary_key": "2237393", "vector": [], "sparse_vector": [], "title": "Strong Connectivity Augmentation is FPT.", "authors": ["<PERSON><PERSON>", "Pranaben<PERSON> Mi<PERSON>", "<PERSON><PERSON>"], "summary": "Augmenting an undirected or a directed graph (digraph) by adding new edges or arcs, to increase its connectivity to a target value, is a fundamental problem in combinatorial optimization and graph theory. In this paper we study the basic problem of augmenting an input digraph to make it strongly connected, which is known as the Strong Connectivity Augmentation problem. Here, the input is a digraph D = (V, A), a set of links L ⊆ V × V, and a positive integer k. The objective is to decide if there exists a subset F ⊆ L, of size at most k, such that D′ = (V, A ∪ F) is strongly connected. We consider the general version of this problem where, additionally, there is a weight function w : L → ℝ+ on the links, and the goal is to find a minimum weight subset F ⊆ L of cardinality at most k, such that D′ = (V, A ∪ F) is strongly connected. We design an algorithm for this problem that runs in time 2(k log k) n(1), thereby showing that it is fixed parameter tractable (FPT). Here, n = |V|. This also resolves an open problem stated by <PERSON> and <PERSON> more than a decade ago [Networks 56(2): 131–142 (2010)].", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.15"}, {"primary_key": "2237394", "vector": [], "sparse_vector": [], "title": "Unlinking, splitting, and some other NP-hard problems in knot theory.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We prove that certain problems naturally arising in knot theory are NP-hard or NP-complete. These are the problems of determining whether a link has an unlinking or splitting number k, finding a k-component unlink as a sublink, and finding a k-component alternating sublink.The unlinking number of a link is the minimum number of crossing changes required for it to become an unlink, minimized over all diagrams of the link. Similarly, splitting number is the number of changes for the link to become split. Problems concerning unlinking and splitting numbers have a long history in topology and knot theory. At the same time, nothing is known about computability or complexity of these problems. A recent breakthrough by <PERSON><PERSON><PERSON> suggests an algorithm to determine whether a hyperbolic link satisfying certain restrictions has unlinking or splitting number one [11], but no algorithm for the general case is yet known. We therefore provide the first bounds on the complexity of the general unlinking and splitting number problems. Our proof of NP-hardness for unlinking and splitting numbers is built upon our other proof, that of NP-hardness of unlink as a sublink problem. This problem is a special case (i.e. a restriction) of the sublink problem, previously proven to be NP-hard [10]. More generally, for any property X of links one can also consider decision problems of the form \"Given a diagram of a link L and a positive integer k, is there a k component sublink of L with the property X?\" We show that this is also NP-hard if X is the property of being an alternating link.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.90"}, {"primary_key": "2237395", "vector": [], "sparse_vector": [], "title": "Polyhedral Value Iteration for Discounted Games and Energy Games.", "authors": ["<PERSON>"], "summary": "We present a deterministic algorithm, solving discounted games with n nodes in -time. For bipartite discounted games our algorithm runs in nO(1)·2n-time. Prior to our work no deterministic algorithm running in time 2o(n log n) regardless of the discount factor was known.We call our approach polyhedral value iteration. We rely on a well-known fact that the values of a discounted game can be found from the so-called optimality equations. In the algorithm we consider a polyhedron obtained by relaxing optimality equations. We iterate points on the border of this polyhedron by moving each time along a carefully chosen shift as far as possible. This continues until the current point satisfies optimality equations.Our approach is heavily inspired by a recent algorithm of <PERSON><PERSON> et al. (ICALP 2019) for energy games. For completeness, we present their algorithm in terms of polyhedral value iteration. Our exposition, unlike the original algorithm, does not require edge weights to be integers and works for arbitrary real weights.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.37"}, {"primary_key": "2237396", "vector": [], "sparse_vector": [], "title": "Optimal Discretization is Fixed-parameter Tractable.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Given two disjoint sets W1 and W2 of points in the plane, the Optimal Discretization problem asks for the minimum size of a family of horizontal and vertical lines that separate W1 from W2, that is, in every region into which the lines partition the plane there are either only points of W1, or only points of W2, or the region is empty. Equivalently, Optimal Discretization can be phrased as a task of discretizing continuous variables: We would like to discretize the range of x-coordinates and the range of y-coordinates into as few segments as possible, maintaining that no pair of points from W1 × W2 are projected onto the same pair of segments under this discretization.We provide a fixed-parameter algorithm for the problem, parameterized by the number of lines in the solution. Our algorithm works in time , where k is the bound on the number of lines to find and n is the number of points in the input.Our result answers in positive a question of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> [IPEC 2017] and of <PERSON><PERSON> (PhD thesis, 2018) and is in contrast with the known intractability of two closely related generalizations: the Rectangle Stabbing problem and the generalization in which the selected lines are not required to be axis-parallel.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.103"}, {"primary_key": "2237397", "vector": [], "sparse_vector": [], "title": "A Constant Factor Approximation for Navigating Through Connected Obstacles in the Plane.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given two points s and t in the plane and a set of obstacles defined by closed curves, what is the minimum number of obstacles touched by a path connecting s and t? This is a fundamental and well-studied problem arising naturally in computational geometry, graph theory (under the names Min-Color Path and Minimum Label Path), wireless sensor networks (Barrier Resilience) and motion planning (Minimum Constraint Removal). It remains NP-hard even for very simple-shaped obstacles such as unit-length line segments. In this paper we give the first constant factor approximation algorithm for this problem, resolving an open problem of [<PERSON> and <PERSON>, TCS, 2014] and [<PERSON><PERSON><PERSON> et al., CGTA, 2020]. We also obtain a constant factor approximation for the Minimum Color Prize Collecting Steiner Forest where the goal is to connect multiple request pairs (s1, t1), …, (sk, tk) while minimizing the number of obstacles touched by any (si, ti) path plus a fixed cost of wi for each pair (si, ti) left disconnected. This generalizes the classic Steiner Forest and Prize-Collecting Steiner Forest problems on planar graphs, for which intricate PTASes are known. In contrast, no PTAS is possible for Min-Color Path even on planar graphs since the problem is known to be APX-hard [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, TALG, 2020]. Additionally, we show that generalizations of the problem to disconnected obstacles in the plane or connected obstacles in higher dimensions are strongly inapproximable assuming some well-known hardness conjectures.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.52"}, {"primary_key": "2237398", "vector": [], "sparse_vector": [], "title": "An O(n5/4) Time ∊-Approximation Algorithm for RMS Matching in a Plane.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The 2-Wasserstein distance (or RMS distance) is a useful measure of similarity between probability distributions with exciting applications in machine learning. For discrete distributions, the problem of computing this distance can be expressed in terms of finding a minimum-cost perfect matching on a complete bipartite graph given by two multisets of points A, B ⊂ ℝ2, with |A| = |B| = n, where the ground distance between any two points is the squared Euclidean distance between them. Although there is a near-linear time relative ∊-approximation algorithm for the case where the ground distance is Euclidean (<PERSON><PERSON><PERSON> and <PERSON>, JACM 2020), all existing relative ∊-approximation algorithms for the RMS distance take Ω(n3/2) time. This is primarily because, unlike Euclidean distance, squared Euclidean distance is not a metric. In this paper, for the RMS distance, we present a new ∊-approximation algorithm that runs in O(n5/4 poly{log n, 1/∊}) time. Our algorithm is inspired by a recent approach for finding a minimum-cost perfect matching in bipartite planar graphs (<PERSON><PERSON><PERSON><PERSON> et al, TALG 2020). Their algorithm depends heavily on the existence of sublinear sized vertex separators as well as shortest path data structures that require planarity. Surprisingly, we are able to design a similar algorithm for a complete geometric graph that is far from planar and does not have any vertex separators. Central components of our algorithm include a quadtree-based distance that approximates the squared Euclidean distance and a data structure that supports both Hungarian search and augmentation in sublinear time.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.55"}, {"primary_key": "2237399", "vector": [], "sparse_vector": [], "title": "A Local Search Framework for Experimental Design.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present a local search framework to design and analyze both combinatorial algorithms and rounding algorithms for experimental design problems. This framework provides a unifying approach to match and improve all known results in D/A/E-design and to obtain new results in previously unknown settings.•For combinatorial algorithms, we provide a new analysis of the classical <PERSON><PERSON><PERSON>'s exchange method. We prove that this simple local search algorithm works well as long as there exists an almost optimal solution with good condition number. Moreover, we design a new combinatorial local search algorithm for E-design using the regret minimization framework.•For rounding algorithms, we provide a unified randomized exchange algorithm to match and improve previous results for D/A/E-design. Furthermore, the algorithm works in the more general setting to approximately satisfy multiple knapsack constraints, which can be used for weighted experimental design and for incorporating fairness constraints into experimental design.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.65"}, {"primary_key": "2237400", "vector": [], "sparse_vector": [], "title": "Quantum algorithms for graph problems with cut queries.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Let G be an n-vertex graph with m edges. When asked a subset S of vertices, a cut query on G returns the number of edges of G that have exactly one endpoint in S. We show that there is a bounded-error quantum algorithm that determines all connected components of G after making O(log(n)6) many cut queries. In contrast, it follows from results in communication complexity that any randomized algorithm even just to decide whether the graph is connected or not must make at least Ω(n/log(n)) many cut queries. We further show that with O(log(n)8) many cut queries a quantum algorithm can with high probability output a spanning forest for G.En route to proving these results, we design quantum algorithms for learning a graph using cut queries. We show that a quantum algorithm can learn a graph with maximum degree d after O(d log(n)2) many cut queries, and can learn a general graph with many cut queries. These two upper bounds are tight up to the poly-logarithmic factors, and compare to Ω(dn) and Ω(m/log(n)) lower bounds on the number of cut queries needed by a randomized algorithm for the same problems, respectively.The key ingredients in our results are the Bernstein<PERSON> algorithm, approximate counting with \"OR queries\", and learning sparse vectors from inner products as in compressed sensing.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.59"}, {"primary_key": "2237401", "vector": [], "sparse_vector": [], "title": "Uncertainty about Uncertainty: Optimal Adaptive Algorithms for Estimating Mixtures of Unknown Coins.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Given a mixture between two populations of coins, \"positive\" coins that each have|unknown and potentially different—bias ≥ ½ + Δ and \"negative\" coins with bias ≤ ½ – Δ, we consider the task of estimating the fraction ρ of positive coins to within additive error ∊. We achieve an upper and lower bound of samples for a 1 – δ probability of success, where crucially, our lower bound applies to all fully-adaptive algorithms. Thus, our sample complexity bounds have tight dependence for every relevant problem parameter. A crucial component of our lower bound proof is a decomposition lemma (Lemma 5.2) showing how to assemble partially-adaptive bounds into a fully-adaptive bound, which may be of independent interest: though we invoke it for the special case of <PERSON>oulli random variables (coins), it applies to general distributions. We present simulation results to demonstrate the practical efficacy of our approach for realistic problem parameters for crowdsourcing applications, focusing on the \"rare events\" regime where ρ is small. The fine-grained adaptive avor of both our algorithm and lower bound contrasts with much previous work in distributional testing and learning.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.25"}, {"primary_key": "2237402", "vector": [], "sparse_vector": [], "title": "Streaming Submodular Matching Meets the Primal-Dual Method.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study streaming submodular maximization subject to matching/b-matching constraints (MSM/MSbM), and present improved upper and lower bounds for these problems. On the upper bounds front, we give primaldual algorithms achieving the following approximation ratios.• for monotone MSM, improving the previous best ratio of 7.75.• for non-monotone MSM, improving the previous best ratio of 9.899.• for maximum weight b-matching, improving the previous best ratio of 4 + ∊.On the lower bounds front, we improve on the previous best lower bound of for MSM, and show ETH-based lower bounds of ≈ 1.914 for polytime monotone MSM streaming algorithms.Our most substantial contributions are our algorithmic techniques. We show that the (randomized) primal-dual method, which originated in the study of maximum weight matching (MWM), is also useful in the context of MSM. To our knowledge, this is the first use of primal-dual based analysis for streaming submodular optimization. We also show how to reinterpret previous algorithms for MSM in our framework; hence, we hope our work is a step towards unifying old and new techniques for streaming submodular maximization, and that it paves the way for further new results.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.114"}, {"primary_key": "2237403", "vector": [], "sparse_vector": [], "title": "Towards PTAS for Precedence Constrained Scheduling via Combinatorial Algorithms.", "authors": ["<PERSON>"], "summary": "We study the classic problem of scheduling n precedence constrained unit-size jobs on m = O(1) machines so as to minimize the makespan. In a recent breakthrough, <PERSON><PERSON> and <PERSON> [11] developed a (1 +∊)-approximation for the problem with running time , via the Sherali-Adams lift of the basic linear programming relaxation for the problem by levels. <PERSON>ar<PERSON> [6] recently improved the number of levels to , and thus the running time to , which is quasi-polynomial for constant m and ∊.In this paper we present a (1 + ∊)-approximation algorithm for the problem with running time , which is very close to a polynomial for constant m and ∊. Unlike the algorithms of <PERSON><PERSON><PERSON> and <PERSON>g, which are based on the linear-programming hierarchy, our algorithm is purely combinatorial. We show that the conditioning operations on the lifted LP solution can be replaced by making guesses about the optimum schedule.Compared to the LP hierarchy framework, our guessing framework has two advantages, both playing important roles in deriving the improved running time. First, we can guess any information about the optimum schedule, as long as it can be described using a few bits, while in the conditioning framework, we can only condition on the variables in the basic LP. Second, the guessing framework can save a factor of log n in the exponent of running time. Roughly speaking, most of the time, the information we try to guess is binary and thus each nested guess only contributes to a multiplicative factor of 2 in the running time. In contrast, each conditioning operation in a sequence incurs a multiplicative factor of poly(n).", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.178"}, {"primary_key": "2237404", "vector": [], "sparse_vector": [], "title": "Estimating the Nash Social Welfare for coverage and other submodular valuations.", "authors": ["Wenzheng Li", "<PERSON>"], "summary": "We study the Nash Social Welfare problem: Given n agents with valuation functions vi : 2[m] → ℝ+, partition [m] into S1, …, Sn so as to maximize . The problem has been shown to admit a constant-factor approximation for additive, budget-additive, and piecewise linear concave separable valuations; the case of submodular valuations is open.We provide a -approximation of the optimal value for several classes of submodular valuations: coverage, sums of matroid rank functions, and certain matching-based valuations.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.69"}, {"primary_key": "2237405", "vector": [], "sparse_vector": [], "title": "Asymptotic dimension of minor-closed families and beyond.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The asymptotic dimension of metric spaces is an important notion in geometric group theory introduced by <PERSON><PERSON><PERSON>. The metric spaces considered in this paper are the ones whose underlying spaces are the vertex-sets of graphs and whose metrics are the distance functions in graphs. A standard compactness argument shows that it suffices to consider the asymptotic dimension of classes of finite graphs. In this paper we prove that the asymptotic dimension of any proper minor-closed family, any class of graphs of bounded tree-width, and any class of graphs of bounded layered tree-width are at most 2, 1, and 2, respectively. The first result solves a question of <PERSON><PERSON> and <PERSON>; the second and third results solve a number of questions of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>. These bounds for asymptotic dimension are optimal and improve a number of results in the literature. Our proofs can be transformed into linear or quadratic time algorithms for finding coverings witnessing the asymptotic dimension which is equivalent to finding weak diameter colorings for graphs. The key ingredient of our proof is a unified machinery about the asymptotic dimension of classes of graphs that have tree-decompositions of bounded adhesion over hereditary classes with known asymptotic dimension, which might be of independent interest.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.119"}, {"primary_key": "2237406", "vector": [], "sparse_vector": [], "title": "Optimal Contextual Pricing and Extensions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the contextual pricing problem a seller repeatedly obtains products described by an adversarially chosen feature vector in ℝd and only observes the purchasing decisions of a buyer with a fixed but unknown linear valuation over the products. The regret measures the difference between the revenue the seller could have obtained knowing the buyer valuation and what can be obtained by the learning algorithm.We give a poly-time algorithm for contextual pricing with O(d log log T + d log d) regret which matches the Ω(d log log T) lower bound up to the d log d additive factor. If we replace pricing loss by the symmetric loss, we obtain an algorithm with nearly optimal regret of O(d log d) matching the Ω(d) lower bound up to log d. These algorithms are based on a novel technique of bounding the value of the Steiner polynomial of a convex region at various scales. The Steiner polynomial is a degree d polynomial with intrinsic volumes as the coefficients.We also study a generalized version of contextual search where the hidden linear function over the Euclidean space is replaced by a hidden function f : → in a certain hypothesis class ℋ. We provide a generic algorithm with O(d2) regret where d is the covering dimension of this class. This leads in particular to a Õ(s2) regret algorithm for linear contextual search if the linear function is guaranteed to be s-sparse. Finally we also extend our results to the noisy feedback model, where each round our feedback is flipped with a fixed probability p < 1/2.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.66"}, {"primary_key": "2237407", "vector": [], "sparse_vector": [], "title": "A Polynomial Time Algorithm for the k-Disjoint Shortest Paths Problem.", "authors": ["<PERSON>"], "summary": "The disjoint paths problem is a fundamental problem in algorithmic graph theory and combinatorial optimization. For a given graph G and a set of k pairs of terminals in G, it asks for the existence of k vertex-disjoint paths connecting each pair of terminals. The proof of <PERSON> and <PERSON> [<PERSON> 1995] of the existence of an n3 algorithm for any fixed k is one of the highlights of their Graph Minors project. In this paper, we focus on the version of the problem where all the paths are required to be shortest paths. This problem, called the disjoint shortest paths problem, was introduced by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [DAM 1998] where she proved that the case k = 2 admits a polynomial time algorithm. This problem has received some attention lately, especially since the proof of the existence of a polynomial time algorithm in the directed case when k = 2 by <PERSON><PERSON><PERSON><PERSON> and <PERSON> [ESA 2017]. However, the existence of a polynomial algorithm when k = 3 in the undirected version remained open since 1998. In this paper we show that for any fixed k, the disjoint shortest paths problem admits a polynomial time algorithm. In fact for any fixed C, the algorithm can be extended to treat the case where each path connecting the pair (s, t) has length at most d(s, t) + C.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.12"}, {"primary_key": "2237408", "vector": [], "sparse_vector": [], "title": "FPT-approximation for FPT Problems.", "authors": ["<PERSON>", "Pranaben<PERSON> Mi<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over the past decade, many results have focused on the design of parameterized approximation algorithms for W[1]-hard problems. However, there are fundamental problems within the class FPT for which the best known algorithms have seen no progress over the course of the decade; some of them have even been proved not to admit algorithms that run in time 2(k) n(1) under the Exponential Time Hypothesis (ETH) or (c – ∊)k n(1) under the Strong ETH (SETH). In this paper, we expand the study of FPT-approximation and initiate a systematic study of FPT-approximation for problems that are FPT. We design FPT-approximation algorithms for problems that are FPT, with running times that are significantly faster than the corresponding best known FPT-algorithm, and while achieving approximation ratios that are significantly better than what is possible in polynomial time.•We present a general scheme to design 2(k) n(1)-time 2-approximation algorithms for cut problems. In particular, we exemplify it for Directed Feedback Vertex Set, Directed Subset Feedback Vertex Set, Directed Odd Cycle Transversal and Undirected Multicut.•Further, we extend our scheme to obtain FPT-time (1)-approximation algorithms for weighted cut problems, where the objective is to obtain a solution of size at most k and of minimum weight. Here, we present two approaches. The first approach achieves 2(k) n(1)-time constant-factor approximation, which we exemplify for all problems mentioned in the first bullet. The other leads to an FPT-approximation Scheme (FPT-AS) for Weighted Directed Feedback Vertex Set.•Additionally, we present a combinatorial lemma that yields a partition of the vertex set of a graph to roughly equal sized sets so that the removal of each set reduces its treewidth substantially, which may be of independent interest. For several graph problems, use this lemma to design cwn(1)-time (1 + ∊)-approximation algorithms that are faster than known SETH lower bounds, where w is the treewidth of the input graph. Examples of such problems include Vertex Cover, Component Order Connectivity, Bounded-Degree Vertex Deletion and ℱ-Packing for any family ℱ of bounded sized graphs.•Lastly, we present a general reduction of problems parameterized by treewidth to their versions parameterized by solution size. Combined with our first scheme, we exemplify it to obtain cwn(1)-time bicriteria approximation algorithms for all problems mentioned in the first bullet.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.14"}, {"primary_key": "2237409", "vector": [], "sparse_vector": [], "title": "Efficient Computation of Representative Weight Functions with Applications to Parameterized Counting (Extended Version).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we prove an analogue of the classic <PERSON><PERSON><PERSON><PERSON> lemma for approximate counting. In fact, we match an analogous result of <PERSON><PERSON><PERSON> et al. [JACM 2016] for decision. This immediately yields, for a number of fundamental problems, parameterized approximate counting algorithms with the same running times as what is obtained for the decision variant using the representative family technique of <PERSON><PERSON><PERSON> et al. [JACM 2016]. For example, we devise an algorithm for approximately counting (a factor (1 ± ∊) approximation algorithm) k-paths in an n-vertex directed graph (#k-Path) running in time (n + m)). This improves over an earlier algorithm of <PERSON> et al. [STOC 2018] that runs in time .Additionally, we obtain an approximate counting analogue of the efficient computation of representative families for product families of <PERSON><PERSON>n et al. [TALG 2017], again essentially matching the running time for decision. This results in an algorithm with running time for computing a (1 + ∊) approximation of the sum of the coefficients of the multilinear monomials in a degree-k homogeneous n-variate polynomial encoded by a monotone circuit (#Multilinear Monomial Detection). When restricted to monotone circuits (rather than polynomials of non-negative coefficients), this improves upon an earlier algorithm of Pratt [FOCS 2019] that runs in time .", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.13"}, {"primary_key": "2237410", "vector": [], "sparse_vector": [], "title": "Planar Distance Oracles with Better Time-Space Tradeoffs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In a recent breakthrough, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> [9] showed that exact distance queries on planar graphs could be answered in no(1) time by a data structure occupying n1+o(1) space, i.e., up to o(1) terms, optimal exponents in time (0) and space (1) can be achieved simultaneously. Their distance query algorithm is recursive: it makes successive calls to a point-location algorithm for planar Voronoi diagrams, which involves many recursive distance queries. The depth of this recursion is non-constant and the branching factor logarithmic, leading to (log n)ω(1) = no(1) query times.In this paper we present a new way to do point-location in planar Voronoi diagrams, which leads to a new exact distance oracle. At the two extremes of our space-time tradeoff curve we can achieve either n1+o(1) space and log2+o(1) n query time, or n log2+o(1) n space and no(1) query time.All previous oracles with Õ(1) query time occupy space n1+Ω(1), and all previous oracles with space Õ(n) answer queries in nΩ(1) time.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.149"}, {"primary_key": "2237411", "vector": [], "sparse_vector": [], "title": "Optimal ℓ1 Column Subset Selection and a Fast PTAS for Low Rank Approximation.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "We study the problem of entrywise ℓ1 low rank approximation. We give the first polynomial time column subset selection-based ℓ1 low rank approximation algorithm sampling Õ(k) columns and achieving an Õ(k1/2)-approximation for any k, improving upon the previous best Õ(k)-approximation and matching a prior lower bound for column subset selection-based ℓ1-low rank approximation which holds for any poly(k) number of columns. We extend our results to obtain tight upper and lower bounds for column subset selection-based ℓp low rank approximation for any 1 < p < 2, closing a long line of work on this problem.We next give a (1 + ∊)-approximation algorithm for entrywise ℓp low rank approximation of an n × d matrix, for 1 ≤ p < 2, that is not a column subset selection algorithm. First, we obtain an algorithm which, given a matrix A ∊ ℝn × d, returns a rank-k matrix in 2poly(k/∊) + poly(nd) running time that achieves the following guarantee:where . Using this algorithm, in the same running time we give an algorithm which obtains error at most (1 + ∊) · OPT and outputs a matrix of rank at most 3k — these algorithms significantly improve upon all previous (1 + ∊)- and O(1)-approximation algorithms for the ℓp low rank approximation problem, which required at least npoly(k/∊) or npoly(k) running time, and either required strong bit complexity assumptions (our algorithms do not) or had bicriteria rank 3k. Finally, we show hardness results which nearly match our 2poly(k) +poly(nd) running time and the above additive error guarantee.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.34"}, {"primary_key": "2237412", "vector": [], "sparse_vector": [], "title": "Competitive Data-Structure Dynamization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Data-structure dynamizationis a general approach for making static data structures dynamic. It is used extensively in geometric settings and in the guise of so-called merge (or compaction) policies in big-data databases such as Google Bigtable and LevelDB (our focus). Previous theoretical work is based on worst-case analyses for uniform inputs — insertions of one item at a time and constant read rate. In practice, merge policies must not only handle batch insertions and varying read/write ratios, they can take advantage of such non-uniformity to reduce cost on a per-input basis.To model this, we initiate the study of data-structure dynamization through the lens of competitive analysis, via two new online set-cover problems. For each, the input is a sequence of disjoint sets of weighted items. The sets are revealed one at a time. The algorithm must respond to each with a set cover that covers all items revealed so far. It obtains the cover incrementally from the previous cover by adding one or more sets and optionally removing existing sets. For each new set the algorithm incurs build cost equal to the weight of the items in the set. In the first problem the objective is to minimize total build cost plus total query cost, where the algorithm incurs a query cost at each time t equal to the current cover size. In the second problem, the objective is to minimize the build cost while keeping the query cost from exceeding k (a given parameter) at any time. We give deterministic online algorithms for both variants, with competitive ratios of Θ(log∗ n) and k, respectively. The latter ratio is optimal for the second variant.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.135"}, {"primary_key": "2237413", "vector": [], "sparse_vector": [], "title": "On a combinatorial generation problem of <PERSON><PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The well-known middle levels conjecture asserts that for every integer n ≥ 1, all binary strings of length 2(n + 1) with exactly n + 1 many 0s and 1s can be ordered cyclically so that any two consecutive strings differ in swapping the first bit with a complementary bit at some later position. In his book 'The Art of Computer Programming Vol. 4A' <PERSON><PERSON><PERSON> raised a stronger form of this conjecture (Problem 56 in Section 7.2.1.3), which requires that the sequence of positions with which the first bit is swapped in each step of such an ordering has 2n + 1 blocks of the same length, and each block is obtained by adding s = 1 (modulo 2n + 1) to the previous block. In this work, we prove <PERSON><PERSON><PERSON>'s conjecture in a more general form, allowing for arbitrary shifts s ≥ 1 that are coprime to 2n + 1. We also present an algorithm to compute this ordering, generating each new bitstring in (n) time, using (n) memory in total.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.46"}, {"primary_key": "2237414", "vector": [], "sparse_vector": [], "title": "Improved Sublinear Time Algorithm for Longest Increasing Subsequence.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel sublinear time algorithm for approximating LIS. If we denote the ratio of the solution size over the input size by λ, our approach yields an algorithm with an approximation factor of Ω(λ∊) for any constant ∊ > 0, and a truly sublinear runtime. This improves over for example the recent work of <PERSON><PERSON> et al. [RSSS19] that approximates LIS within a factor Ω(λ3) in truly sublinear time. Our work makes use of a grid packing technique recently introduced by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> to approximate LIS in the dynamic setting [MS20], providing another application for this technique.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.115"}, {"primary_key": "2237415", "vector": [], "sparse_vector": [], "title": "Robust Algorithms for Online Convex Problems via Primal-Dual.", "authors": ["<PERSON>"], "summary": "The importance of primal-dual methods in online optimization can hardly be overstated, and they give several of the state-of-the art results in both of the most common models for online algorithms: the adversarial and the stochastic/random order models. Here we try to provide a more unified analysis of primal-dual algorithms to better understand the mechanisms behind this important method. With this we are able of recover and extend in one goal several results of the literature.In particular we obtain robust online algorithm for fairly general online convex problems: we consider the Mixed model where in some of the time steps the data is stochastic and in the others the data is adversarial. Both the quantity and location of the adversarial time steps are unknown to the algorithm. The guarantees of our algorithms interpolate between the (close to) best guarantees for each of the pure models. In particular, the presence of adversarial times does not degrade the guarantee relative to the stochastic part of the instance.More concretely, we first consider online convex programming: in each time step a feasible set Vt is revealed, and the algorithm needs to select vt ∊ Vt to minimize the total cost ψ (Σt vt), for a convex function ψ. Our robust primal-dual algorithm for this problem on the Mixed model recovers and extends, for example, a result of <PERSON> et al. [15] as well as the recent work on ℓp-norm load balancing [29]. We also consider the problem of welfare maximization with convex production costs: in each time a customer presents a value ct and resource consumption vector at, and the goal is to fractionally select customers to maximize the profit Σt ctxt – ψ(Σt atxt). Our robust primal-dual algorithm for this problem on the Mixed model recovers and extends the result of Azar et al. [3].Given the ubiquity of primal-dual algorithms, we hope that the ideas of the analyses presented here will be useful in obtaining other robust algorithm in the Mixed or related models.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.124"}, {"primary_key": "2237416", "vector": [], "sparse_vector": [], "title": "Minimum-cost integer circulations in given homology classes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Let D be a directed graph cellularly embedded in a surface together with non-negative cost on its arcs. Given any integer circulation in D, we study the problem of finding a minimum-cost non-negative integer circulation in D that is homologous over the integers to the given circulation. A special case of this problem arises in recent work on the stable set problem for graphs with bounded odd cycle packing number, in which the surface is non-orientable (<PERSON><PERSON> et al., SODA'20). For orientable surfaces, polynomial-time algorithms have been obtained for different variants of this problem. We complement these results by showing that the convex hull of feasible solutions has a very simple polyhedral description. In contrast, only little seems to be known about the case of non-orientable surfaces. We show that the problem is strongly NP-hard for general non-orientable surfaces, and give the first polynomial-time algorithm for surfaces of fixed genus. For the latter, we provide a characterization of ℤ-homology that allows us to recast the problem as a special integer program, which can be efficiently solved using proximity results and dynamic programming.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.162"}, {"primary_key": "2237417", "vector": [], "sparse_vector": [], "title": "A tight condition for triangle factors in pseudorandom graphs.", "authors": ["<PERSON>"], "summary": "An (n, d, λ)-graph is an n vertex, d-regular graph with second eigenvalue in absolute value λ. When λ is small compared to d, such graphs have pseudorandom properties and make good expander graphs. A fundamental question in the study of pseudorandom graphs is to find conditions on the parameters that guarantee the existence of a certain subgraph. A celebrated construction due to <PERSON><PERSON> gives a triangle-free (n, d, λ)-graph with d = Θ(n2/3) and λ = Θ(d2/n). This construction is optimal as having λ = o(d2/n) guarantees the existence of a triangle in an (n, d, λ)-graph. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2004) conjectured that if n ∊ 3ℕ and λ = o(d2/n) then an (n, d, λ)-graph G in fact contains a triangle factor: vertex disjoint triangles covering the whole vertex set. This conjecture has attracted the attention of many authors but until now has evaded a full solution.In this paper1, we confirm the conjecture of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and our proof gives a randomised algorithm that finds a triangle factor. The result can be seen as a clear distinction between pseudorandom graphs and random graphs, showing that essentially the same pseudorandom condition that ensures a triangle in a graph actually guarantees a triangle factor. In fact, even more is true: as a corollary to this result and a result of <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> and the author, we can conclude that the same condition actually guarantees that such a graph G contains every graph on n vertices with maximum degree at most 2.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.57"}, {"primary_key": "2237418", "vector": [], "sparse_vector": [], "title": "Online Generalized Network Design Under (Dis)Economies of Scale.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider a general online network design problem where a sequence of N requests arrive over time, each of which needs to use a subset of the available resources E. The cost incurred by a resource e ∊ E is some function fe of its total load ℓe. The objective is to minimize the total cost Σe∊E fe(ℓe). We focus on cost functions that exhibit (dis)economies of scale, which are of the form if x > 0 (and zero if x = 0), where the exponent αe ≥ 1.Our main result is a deterministic online algorithm with tight competitive ratio when αe is constant. This framework is applicable to many network design problems, including multicommodity routing, Steiner tree/forest connectivity and set-connectivity Even in special cases such as multicommodity routing in undirected graphs with edge-costs, this is the first online algorithm to handle non-uniform resource cost and with a competitive ratio independent of the network size and number of requests. Our online competitive ratio also matches the previous-best offline approximation ratio. Our approach is based on the online primal-dual method for convex programs.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.167"}, {"primary_key": "2237419", "vector": [], "sparse_vector": [], "title": "On Tolerant Distribution Testing in the Conditional Sampling Model.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Recently, there has been significant work studying distribution testing under the Conditional Sampling model. In this model, a query specifies a subset S of the domain, and the output received is a sample drawn from the distribution conditioned on being in S. In this paper, we improve query complexity bounds for several classic distribution testing problems in this model.First, we prove that tolerant uniformity testing in the conditional sampling model can be solved using Õ(∊–2) queries, which is optimal and improves upon the Õ(∊–20)-query algorithm of <PERSON> et al. [CRS15]. This bound even holds under a restricted version of the conditional sampling model called the Pair Conditional Sampling model. Next, we prove that tolerant identity testing in the conditional sampling model can be solved in Õ(∊–4) queries, which is the first known bound independent of the support size of the distribution for this problem. Next, we use our algorithm for tolerant uniformity testing to get an Õ(∊–4)-query algorithm for monotonicity testing in the conditional sampling model, improving on the Õ(∊–22)-query algorithm of Canonne [Can15]. Finally, we study (non-tolerant) identity testing under the pair conditional sampling model, and provide a tight bound of for the query complexity, where the domain of the distribution has size N. This improves upon both the known upper and lower bounds in [CRS15].", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.23"}, {"primary_key": "2237420", "vector": [], "sparse_vector": [], "title": "Improved Algorithms for Population Recovery from the Deletion Channel.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The population recovery problem asks one to recover an unknown distribution over n-bit strings given access to independent noisy samples of strings drawn from the distribution. Recently, <PERSON> et al. [BCF+ 19] studied the problem where the noise is induced through the deletion channel. This problem generalizes the famous trace reconstruction problem, where one wishes to learn a single string under the deletion channel.<PERSON> et al. showed how to learn ℓ-sparse distributions over strings using exp (n1/2 · (log n)O(ℓ)) samples. In this work, we learn the distribution using only exp (Õ(n1/3) · ℓ2) samples, by developing a higher-moment analog of the algorithms of [DOS17a, NP17], which solve trace reconstruction in exp (Õ(n1/3)) samples. We also give the first algorithm with a runtime subexponential in n, solving population recovery in exp (Õ(n1/3) · ℓ3) samples and time.Notably, our dependence on n nearly matches the upper bound of [DOS17a, NP17] when ℓ = O(1), and we reduce the dependence on ℓ from doubly to singly exponential. Therefore, we are able to learn large mixtures of strings: while <PERSON> et al.'s algorithm can only learn a mixture of O(log n/ log log n) strings with a subexponential number of samples, we are able to learn a mixture of no(1) strings in exp (n1/3+o(1)) samples and time.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.77"}, {"primary_key": "2237421", "vector": [], "sparse_vector": [], "title": "A Faster Exponential Time Algorithm for Bin Packing With a Constant Number of Bins via Additive Combinatorics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Céline M. F. <PERSON>", "<PERSON><PERSON>"], "summary": "In the Bin Packing problem one is given n items with weights w1, …, wn and m bins with capacities c1, …, cm. The goal is to find a partition of the items into sets S1, …, Sm such that w(Sj) ≤ cj for every bin j, where w(X) denotes Σi∊xwi.<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (SICOMP 2009) presented an time algorithm for Bin Packing. In this paper, we show that for every m ∊ ℕ there exists a constant σm > 0 such that an instance of Bin Packing with m bins can be solved in randomized time. Before our work, such improved algorithms were not known even for m equals 4.A key step in our approach is the following new result in Littlewood-Offord theory on the additive combinatorics of subset sums: For every δ > 0 there exists an ∊ > 0 such that if |{X ⊆ {1, …, n} : w(X) = v}| ≥ 2(1–∊)n for some v then |{w(X) : X ⊆ {1, …, n}}| ≤ 2δn.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.102"}, {"primary_key": "2237422", "vector": [], "sparse_vector": [], "title": "New Data Structures for Orthogonal Range Reporting and Range Minima Queries.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper we present new data structures for two extensively studied variants of the orthogonal range searching problem.First, we describe a data structure that supports two-dimensional orthogonal range minima queries in O(n) space and O(log∊ n) time, where n is the number of points in the data structure and ∊ is an arbitrarily small positive constant. Previously known linear-space solutions for this problem require O(log1+∊ n) time (<PERSON><PERSON><PERSON>, 1988) or O(log n log log n) time (<PERSON><PERSON> et al., 2012). A modification of our data structure uses space O(n log log n) and supports range minima queries in time O(log log n). Both results can be extended to support three-dimensional five-sided reporting queries.Next, we turn to the four-dimensional orthogonal range reporting problem and present a data structure that answers queries in optimal O(log n/log log n + k) time, where k is the number of points in the answer. This is the first data structure that achieves the optimal query time for this problem.Our results are obtained by exploiting the properties of three-dimensional shallow cuttings.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.73"}, {"primary_key": "2237423", "vector": [], "sparse_vector": [], "title": "Rankwidth meets stability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study two notions of being well-structured for classes of graphs that are inspired by classic model theory. A class of graphs is monadically stable if it is impossible to define arbitrarily long linear orders in vertex-colored graphs from using a fixed first-order formula. Similarly, monadic dependence corresponds to the impossibility of defining all graphs in this way. Examples of monadically stable graph classes are nowhere dense classes, which provide a robust theory of sparsity. Examples of monadically dependent classes are classes of bounded rankwidth (or equivalently, bounded cliquewidth), which can be seen as a dense analog of classes of bounded treewidth. Thus, monadic stability and monadic dependence extend classical structural notions for graphs by viewing them in a wider, model-theoretical context. We explore this emerging theory by proving the following: 1) A class of graphs is a first-order transduction of a class with bounded treewidth if and only if has bounded rankwidth and a stable edge relation (i.e. graphs from exclude some half-graph as a semi-induced subgraph). 2) If a class of graphs is monadically dependent and not monadically stable, then has in fact an unstable edge relation.As a consequence, we show that classes with bounded rankwidth excluding some half-graph as a semi-induced subgraph are linearly χ-bounded. Our proofs are effective and lead to polynomial time algorithms.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.120"}, {"primary_key": "2237424", "vector": [], "sparse_vector": [], "title": "The Demand Query Model for Bipartite Matching.", "authors": ["<PERSON><PERSON>"], "summary": "We introduce a \"concrete complexity\" model for studying algorithms for matching in bipartite graphs. The model is based on the \"demand query\" model used for combinatorial auctions. Most (but not all) known algorithms for bipartite matching seem to be translatable into this model including exact, approximate, sequential, parallel, and online ones.A perfect matching in a bipartite graph can be found in this model with O(n3/2) demand queries (in a bipartite graph with n vertices on each side) and our main open problem is to either improve the upper bound or prove a lower bound. An improved upper bound could yield \"normal\" algorithms whose running time is better than the fastest ones known, while a lower bound would rule out a faster algorithm for bipartite matching from within a large class of algorithms.Our main result is a lower bound for finding an approximately maximum size matching in parallel: A deterministic algorithm that runs in no(1) rounds, where each round can make at most n1.99 demand queries cannot find a matching whose size is within no(1) factor of the maximum. This is in contrast to randomized algorithms that can find a matching whose size is 99% of the maximum in O(log n) rounds, each making n demand queries.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.36"}, {"primary_key": "2237425", "vector": [], "sparse_vector": [], "title": "Dynamic Graph Algorithms with <PERSON><PERSON> Updates in the Massively Parallel Computation Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We study dynamic graph algorithms in the Massively Parallel Computation model, which was inspired by practical data processing systems. Our goal is to provide algorithms that can efficiently handle large batches of edge insertions and deletions.We show algorithms that require fewer rounds to update a solution to problems such as Minimum Spanning Forest, 2-Edge Connected Components, and Maximal Matching than would be required by their static counterparts to compute it from scratch. They work in the most restrictive memory regime, in which local memory per machine is strongly sublinear in the number of graph vertices. Improving on the size of the batch they can handle efficiently would improve on the round complexity of known static algorithms on sparse graphs.Our algorithms can process batches of updates of size Θ(S), for Minimum Spanning Forest and 2-Edge Connected Components, and Θ(S1–∊), for Maximal Matching, in O(1) rounds, where S is the local memory of a single machine.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.175"}, {"primary_key": "2237426", "vector": [], "sparse_vector": [], "title": "Directed Shortest Paths via Approximate Cost Balancing.", "authors": ["<PERSON>", "László A. <PERSON>"], "summary": "We present an O(nm) algorithm for all-pairs shortest paths computations in a directed graph with n nodes, m arcs, and nonnegative integer arc costs. This matches the complexity bound attained by <PERSON><PERSON> [26] for the all-pairs problems in undirected graphs. Our main insight is that shortest paths problems with approximately balanced directed cost functions can be solved similarly to the undirected case. Our algorithm starts with an preprocessing step that finds a 3-min-balanced reduced cost function. Using these reduced costs, every shortest path query can be solved in O(m) time using an adaptation of <PERSON><PERSON>'s component hierarchy method. The balancing result is of independent interest, and gives the best currently known approximate balancing algorithm for the problem.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.16"}, {"primary_key": "2237427", "vector": [], "sparse_vector": [], "title": "Solving Sparse Linear Systems Faster than Matrix Multiplication.", "authors": ["<PERSON>", "Santosh S<PERSON>"], "summary": "Can linear systems be solved faster than matrix multiplication? While there has been remarkable progress for the special cases of graph structured linear systems, in the general setting, the bit complexity of solving an n × n linear system Ax = b is Õ(nω), where ω 2. This speedup holds for any input matrix A with o(nω–1/log(κ(A))) non-zeros, where κ(A) is the condition number of A. For poly(n)-conditioned matrices with Õ(n) nonzeros, and the current value of ω, the bit complexity of our algorithm to solve to within any 1/poly(n) error is O(n2.331645).Our algorithm can be viewed as an efficient, randomized implementation of the block Krylov method via recursive low displacement rank factorizations. It is inspired by the algorithm of [<PERSON><PERSON><PERSON> et al. ISSAC '06 '07] for inverting matrices over finite fields. In our analysis of numerical stability, we develop matrix anti-concentration techniques to bound the smallest eigenvalue and the smallest gap in eigenvalues of semi-random matrices.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.31"}, {"primary_key": "2237428", "vector": [], "sparse_vector": [], "title": "On Locating Paths in Compressed Tries.", "authors": ["<PERSON>"], "summary": "In this paper, we consider the problem of compressing a trie while supporting the powerful locate queries: to return the pre-order identifiers of all nodes reached by a path labeled with a given query pattern. Our result builds on top of the XBW tree transform of <PERSON><PERSON><PERSON><PERSON> et al. [FOCS 2005] and generalizes the r-index locate machinery of <PERSON><PERSON><PERSON> et al. [SODA 2018, JACM 2020] based on the run-length encoded Burrows-Wheeler transform (BWT). Our first contribution is to propose a suitable generalization of the run-length BWT to tries. We show that this natural generalization enjoys several of the useful properties of its counterpart on strings: in particular, the transform natively supports counting occurrences of a query pattern on the trie's paths and its size r captures the trie's repetitiveness and lower-bounds a natural notion of trie entropy. Our main contribution is a much deeper insight into the combinatorial structure of this object. In detail, we show that a data structure of O(r log n) + 2n + o(n) bits, where n is the number of nodes, allows locating the occ occurrences of a pattern of length m in nearly-optimal O(m log σ + occ) time, where σ is the alphabet's size. Our solution consists in sampling O(r) nodes that can be used as \"anchor points\" during the locate process. Once obtained the pre-order identifier of the first pattern occurrence (in co-lexicographic order), we show that a constant number of constant-time jumps between those anchor points lead to the identifier of the next pattern occurrence, thus enabling locating in optimal O(1) time per occurrence.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.47"}, {"primary_key": "2237429", "vector": [], "sparse_vector": [], "title": "Spectral Sparsification of Metrics and Kernels.", "authors": ["<PERSON>"], "summary": "A set of n points in a geometric space implicitly induces a complete graph where the weight of an edge between two points is a function of the distance between the endpoints. There are many natural problems that arise from such a geometric graph and many standard geometric problems can be recast as simple properties of this graph. A basic algorithmic obstacle that arises is that the explicit size of the graph is quadratic in the size of the input. There is a long line of research overcoming this obstacle in low-dimensional spaces, as well as some positive results in high-dimensional and more abstract models for specific applications. Here we consider graph problems in general and address the issue of constructing the geometric graph. Rather than constructing these graphs exactly, we ask if it is possible to explicitly construct a sparse approximation of these geometric graphs in nearly linear time.We consider geometric graphs where the edge weights are given as either as a metric (via an oracle), or given by a smooth kernel function in a Euclidean space. For both of these settings, we show that for any ∊ > 0, one can compute an explicit (1 + ∊)-approximate spectral approximation of the geometric graph with Õ(n/∊2) edges in Õ(n/∊2) randomized time. Some of these algorithms are extremely simple. Composed with nearly linear time graph algorithms, this allows for a broad class of applications on geometric graphs with running times proportional to the number of points.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.87"}, {"primary_key": "2237430", "vector": [], "sparse_vector": [], "title": "Being Fast Means Being Chatty: The Local Information Cost of Graph Spanners.", "authors": ["<PERSON>"], "summary": "We introduce a new measure for quantifying the amount of information that the nodes in a network need to learn to solve a graph problem. We show that the local information cost (LIC) presents a natural lower bound on the communication complexity of distributed algorithms. For the synchronous CONGEST-KT1 model, where each node has initial knowledge of its neighbors' IDs, we prove that bits are required for solving a graph problem P with a τ-round algorithm that errs with probability at most γ. Our result is the first lower bound that yields a general trade-off between communication and time for graph problems in the CONGEST-KT1 model.We demonstrate how to apply the local information cost by deriving a lower bound on the communication complexity of computing a multiplicative spanner with stretch 2t – 1 that consists of at most edges, where ∊ = O(1/t2). Our main result is that any O(poly(n))-time algorithm must send at least bits in the CONGEST model under the KT1 assumption. Previously, only a trivial lower bound of bits was known for this problem; in fact, this is the first nontrivial lower bound on the communication complexity of a sparse subgraph problem in this setting.A consequence of our lower bound is that achieving both time- and communication-optimality is impossible when designing a distributed spanner algorithm. In light of the work of <PERSON>, <PERSON>, and <PERSON><PERSON> (2015), this shows that computing a minimum spanning tree can be done significantly faster than finding a spanner when considering algorithms with Õ(n) communication complexity. Our result also implies time complexity lower bounds for constructing a spanner in the node-congested clique of <PERSON> et al. (2019) and in the push-pull gossip model with limited bandwidth.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.126"}, {"primary_key": "2237431", "vector": [], "sparse_vector": [], "title": "Treewidth-Pliability and PTAS for Max-CSPs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We identify a sufficient condition, treewidth-pliability, that gives a polynomial-time approximation scheme (PTAS) for a large class of Max-2-CSPs parametrised by the class of allowed constraint graphs (with arbitrary constraints on an unbounded alphabet). Our result applies more generally to the maximum homomorphism problem between two rational-valued structures.The condition unifies the two main approaches for designing PTASes. One is <PERSON>'s layering technique, which applies to sparse graphs such as planar or excluded-minor graphs. The other is based on <PERSON><PERSON><PERSON><PERSON><PERSON>'s regularity lemma and applies to dense graphs. We extend the applicability of both techniques to new classes of Max-CSPs.Treewidth-pliability turns out to be a robust notion that can be defined in several equivalent ways, including characterisations via size, treedepth, or the <PERSON><PERSON><PERSON> number. We show connections to the notions of fractional-treewidth-fragility from structural graph theory, hyperfiniteness from the area of property testing, and regularity partitions from the theory of dense graph limits. These may be of independent interest. In particular we show that a monotone class of graphs is hyperfinite if and only if it is fractionally-treewidth-fragile and has bounded degree.The full version [59] containing detailed proofs is available at https://arxiv.org/abs/1911.03204.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.29"}, {"primary_key": "2237432", "vector": [], "sparse_vector": [], "title": "Optimal Distribution-Free Sample-Based Testing of Subsequence-Freeness.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we study the problem of testing subsequence-freeness. For a given subsequence (word) w = w1 … wk, a sequence (text) T = t1 … tn is said to contain w if there exist indices 1 ≤ i1 < ⃛ < ik ≤ n such that for every 1 ≤ j ≤ k. Otherwise, T is w-free. While a large majority of the research in property testing deals with algorithms that perform queries, here we consider sample-based testing (with one-sided error). In the \"standard\" sample-based model (i.e., under the uniform distribution), the algorithm is given samples (i, ti) where i is distributed uniformly independently at random. The algorithm should distinguish between the case that T is w-free, and the case that T is ∊-far from being w-free (i.e., more than an ∊-fraction of its symbols should be modified so as to make it w-free). <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (Proceedings of RANDOM, 2017) showed that O(k2 log k/∊) samples suffice for this testing task. We obtain the following results.•The number of samples sufficient for sample-based testing (under the uniform distribution) is O(k/∊). This upper bound builds on a characterization that we present for the distance of a text T from w-freeness in terms of the maximum number of copies of w in T, where these copies should obey certain restrictions.•We prove a matching lower bound, which holds for every word w. This implies that the above upper bound is tight.•The same upper bound holds in the more general distribution-free sample-based model. In this model the algorithm receives samples (i, ti) where i is distributed according to an arbitrary distribution p (and the distance from w-freeness is measured with respect to p).We highlight the fact that while we require that the testing algorithm work for every distribution and when only provided with samples, the complexity we get matches a known lower bound for a special case of the seemingly easier problem of testing subsequence-freeness under the uniform distribution and with queries (Canonne et al., Theory of Computing, 2019).", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.22"}, {"primary_key": "2237433", "vector": [], "sparse_vector": [], "title": "The Growth Rate Over Trees Of Any Family Of Sets Defined By A Monadic Second Order Formula Is Semi-computable.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Monadic second order logic can be used to express many classical notions of sets of vertices of a graph as for instance: dominating sets, induced matchings, perfect codes, independent sets or irredundant sets. Bounds on the number of sets of any such family of sets are interesting from a combinatorial point of view and have algorithmic applications. Many such bounds on different families of sets over different classes of graphs are already provided in the literature. In particular, <PERSON><PERSON> recently showed that the number of minimal dominating sets in trees of order n is at most and that this bound is asymptotically sharp up to a multiplicative constant. We build on his work to show that what he did for minimal dominating sets can be done for any family of sets definable by a monadic second order formula.We first show that, for any monadic second order formula over graphs that characterizes a given kind of subset of its vertices, the maximal number of such sets in a tree can be expressed as the growth rate of a bilinear system. This mostly relies on well known links between monadic second order logic over trees and tree automata and basic tree automata manipulations. Then we show that this \"growth rate\" of a bilinear system can be approximated from above. We then use our implementation of this result to provide bounds (some sharp and some almost sharp) on the number of independent dominating sets, total perfect dominating sets, induced matchings, maximal induced matchings, minimal perfect dominating sets, perfect codes and maximal irredundant sets on trees. We also solve a question from <PERSON><PERSON> <PERSON><PERSON> et al. regarding r-matchings and obtain a sharp upper-bound on the number of maximal matchings on trees. Remark that this approach is easily generalizable to graphs of bounded tree width or clique width (or any similar class of graphs where tree automata are meaningful).", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.49"}, {"primary_key": "2237434", "vector": [], "sparse_vector": [], "title": "Deterministic Replacement Path Covering.", "authors": ["<PERSON><PERSON><PERSON> C. S.", "<PERSON><PERSON><PERSON>"], "summary": "In this article, we provide a unified and simplified approach to derandomize central results in the area of fault-tolerant graph algorithms. Given a graph G, a vertex pair (s, t) ∊ V(G) × V(G), and a set of edge faults F ⊆ E(G), a replacement path P(s, t, F) is an s-t shortest path in G \\ F. For integer parameters L, f, a replacement path covering (RPC) is a collection of subgraphs of G, denoted by ∊L,f = {G1, …, Gr}, such that for every set F of at most f faults (i.e., |F| ≤ f) and every replacement path P(s, t, F) of at most L edges, there exists a subgraph Gi ∊ ∊L,f that contains all the edges of P and does not contain any of the edges of F. The covering value of the RPC GL,f is then defined to be the number of subgraphs in ∊L,f.In the randomized setting, it is easy to build an (L, f)-RPC with covering value of O(max{L, f}min{L,f} ·min{L, f}· log n), but to this date, there is no efficient deterministic algorithm with matching bounds. As noted recently by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (ICALP 2019) this poses the key barrier for derandomizing known constructions of distance sensitivity oracles and fault-tolerant spanners. We show the following:•There exist efficient deterministic constructions of (L, f)-RPCs whose covering values almost match the randomized ones, for a wide range of parameters. Our time and value bounds improve considerably over the previous construction of Parter (DISC 2019). Our algorithms are based on the introduction of a novel notion of hash families that we call Hit and Miss hash families. We then show how to construct these hash families from (algebraic) error correcting codes such as Reed-Solomon codes and Algebraic-Geometric codes.•For every L, f, and n, there exists an n-vertex graph G whose (L, f)-RPC covering value is Ω(Lf). This lower bound is obtained by exploiting connections to the problem of designing sparse fault-tolerant BFS structures.An applications of our above deterministic constructions is the derandomization of the algebraic construction of the distance sensitivity oracle by Weimann and Yuster (FOCS 2010). The preprocessing and query time of our deterministic algorithm nearly match the randomized bounds. This resolves the open problem of Alon, Chechik and Cohen (ICALP 2019).Additionally, we show a derandomization of the randomized construction of vertex fault-tolerant spanners by Dinitz and Krauthgamer (PODC 2011) and Braunschvig et al. (Theor. Comput. Sci., 2015). The time complexity and the size bounds of the output spanners nearly match the randomized counterparts.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.44"}, {"primary_key": "2237435", "vector": [], "sparse_vector": [], "title": "SoS Degree Reduction with Applications to Clustering and Robust Moment Estimation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We develop a general framework to significantly reduce the degree of sum-of-squares proofs by introducing new variables. To illustrate the power of this framework, we use it to speed up previous algorithms based on sum-of-squares for two important estimation problems, clustering and robust moment estimation. The resulting algorithms offer the same statistical guarantees as the previous best algorithms but have significantly faster running times. Roughly speaking, given a sample of n points in dimension d, our algorithms can exploit order-ℓ moments in time dO(ℓ) · nO(1), whereas a naive implementation requires time (d · n)O(ℓ). Since for the aforementioned applications, the typical sample size is dΘ(ℓ), our framework improves running times from to dO(ℓ).", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.24"}, {"primary_key": "2237436", "vector": [], "sparse_vector": [], "title": "Average Sensitivity of Graph Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In modern applications of graph algorithms, where the graphs of interest are large and dynamic, it is unrealistic to assume that an input representation contains the full information of a graph being studied. Hence, it is desirable to use algorithms that, even when provided with only a (large) subgraph, output solutions that are close to the solutions output when the whole graph is available. We formalize this feature by introducing the notion of average sensitivity of graph algorithms, which is the average earth mover's distance between the output distributions of an algorithm on a graph and its subgraph obtained by removing an edge, where the average is over the edges removed and the distance between two outputs is the Hamming distance.In this work, we initiate a systematic study of average sensitivity. After deriving basic properties of average sensitivity such as composition, we provide efficient approximation algorithms with low average sensitivities for concrete graph problems, including the minimum spanning forest problem, the global minimum cut problem, the minimum s-t cut problem, and the maximum matching problem. In addition, we prove that the average sensitivity of our global minimum cut algorithm is almost optimal, by showing a nearly matching lower bound. We also show that every algorithm for the 2-coloring problem has average sensitivity linear in the number of vertices. One of the main ideas involved in designing our algorithms with low average sensitivity is the following fact; if the presence of a vertex or an edge in the solution output by an algorithm can be decided locally, then the algorithm has a low average sensitivity, allowing us to reuse the analyses of known sublinear-time algorithms and local computation algorithms. Using this fact in conjunction with our average sensitivity lower bound for 2-coloring, we show that every local computation algorithm for 2-coloring has query complexity linear in the number of vertices, thereby answering an open question.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.43"}, {"primary_key": "2237437", "vector": [], "sparse_vector": [], "title": "Peeling Close to the Orientability Threshold - Spatial Coupling in Hashing-Based Data Structures.", "authors": ["<PERSON>"], "summary": "In multiple-choice data structures each element x in a set S of m keys is associated with a random set e(x) ⊆ [n] of buckets with capacity ℓ ≥ 1 by hash functions. This setting is captured by the hypergraph H = ([n], {e(x) | x ∊ S}). Accomodating each key in an associated bucket amounts to finding an ℓ-orientation of H assigning to each hyperedge an incident vertex such that each vertex is assigned at most ℓ hyperedges. If each subhypergraph of H has minimum degree at most ℓ, then an ℓ-orientation can be found greedily and H is called ℓ-peelable. Peelability has a central role in invertible Bloom lookup tables and can speed up the construction of retrieval data structures, perfect hash functions and cuckoo hash tables.Many hypergraphs exhibit sharp density thresholds with respect to ℓ-orientability and ℓ-peelability, i.e. as the density grows past a critical value, the probability of these properties drops from almost 1 to almost 0. In fully random k-uniform hypergraphs the thresholds for ℓ-orientability significantly exceed the thresholds for ℓ-peelability. In this paper, for every k ≥ 2 and ℓ ≥ 1 with (k, ℓ) ≠ (2, 1) and every z > 0, we construct a new family of random k-uniform hypergraphs with i.i.d. random hyperedges such that both the ℓ-peelability and the ℓ-orientability thresholds approach as z → ∞. In particular we achieve 1-peelability at densities arbitrarily close to 1, extending the reach of greedy algorithms.Our construction is simple: The n vertices are linearly ordered and each hyperedge selects its k elements uniformly at random from a random range of consecutive vertices.We thus exploit the phenomenon of threshold saturation via spatial coupling discovered in the context of low-density parity-check codes. Once the connection to data structures is in plain sight, a framework by Kudekar, Richardson and Urbanke [39] does the heavy lifting in our proof.We demonstrate the usefulness of our construction using our hypergraphs as a drop-in replacement in a retrieval data structure by Botelho et al. [8]. This reduces memory usage from ≈ 1.23m bits to ≈ 1.12m bits (for input size m). Using k > 3 attains, at small sacrifices in running time, further improvements to memory usage.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.131"}, {"primary_key": "2237438", "vector": [], "sparse_vector": [], "title": "Shortest Paths Among Obstacles in the Plane Revisited.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Given a set of pairwise disjoint polygonal obstacles in the plane, finding an obstacle-avoiding Euclidean shortest path between two points is a classical problem in computational geometry and has been studied extensively. The previous best algorithm was given by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [FOCS 1993, SIAM J. Comput. 1999] and the algorithm runs in O(n log n) time and O(n log n) space, where n is the total number of vertices of all obstacles. The algorithm is time-optimal because Ω(n log n) is a lower bound. It has been an open problem for over two decades whether the space can be reduced to O(n). In this paper, we settle it by solving the problem in O(n log n) time and O(n) space, which is optimal in both time and space; we achieve this by modifying the algorithm of <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Like their original algorithm, our new algorithm can build a shortest path map for a source point s in O(n log n) time and O(n) space, such that given any query point t, the length of a shortest path from s to t can be computed in O(log n) time and a shortest path can be produced in additional time linear in the number of edges of the path.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.51"}, {"primary_key": "2237440", "vector": [], "sparse_vector": [], "title": "Beating the probabilistic lower bound on perfect hashing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "For an integer q ≥ 2, a perfect q-hash code C is a block code over [q] ≔ {1, …, q} of length n in which every subset {c1, c2, …, cq} of q elements is separated, i.e., there exists i ∊ [n] such that {proji(c1), …, proji(cq)} = [q], where proji(cj) denotes the ith position of cj. Finding the maximum size M(n, q) of perfect q-hash codes of length n, for given q and n, is a fundamental problem in combinatorics, information theory, and computer science. In this paper, we are interested in asymptotical behavior of this problem. More precisely speaking, we will focus on the quantity .A well-known probabilistic argument indicates [10, 12]. This is still the best-known lower bound so far except for the case q = 3 for which <PERSON><PERSON><PERSON> and <PERSON><PERSON> [13] found that the concatenation technique could lead to perfect 3-hash codes that could beat this probabilistic lower bound. This improved lower bound on R3 was discovered in 1988 and there has been no progress of this lower bound on Rq for more than 30 years despite of some work on upper bounds on Rq. In this paper we show that this probabilistic lower bound can be improved for q = 4, 8 and all odd integers between 5 and 25,1 and all sufficiently large q with q (mod 4) ≠ 2. Although we are not able to prove that our construction can beat the probabilistic method for all q with q (mod 4) ≠ 2, the fact that our construction beat the probabilistic method for both small and large q sheds light on that our new construction might beat the previous lower bound for all q with q (mod 4) ≠ 2. Our idea is based on a modified concatenation differing from the concatenation [10] where both the inner and outer codes are separated. In our concatenation, the inner code is not necessarily a perfect q-hash code. This gives a more flexible choice of inner codes and hence we are able to improve the lower bound on Rq.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.3"}, {"primary_key": "2237441", "vector": [], "sparse_vector": [], "title": "Tight Distributed Sketching Lower Bound for Connectivity.", "authors": ["Huacheng Yu"], "summary": "In this paper, we study the distributed sketching complexity of connectivity. In distributed graph sketching, an n-node graph G is distributed to n players such that each player sees the neighborhood of one vertex. The players then simultaneously send one message to the referee, who must compute some function of G with high probability. For connectivity, the referee must output whether <PERSON> is connected. The goal is to minimize the message lengths. Such sketching schemes are equivalent to one-round protocols in the broadcast congested clique model.We prove that the expected average message length must be at least Ω(log3 n) bits, if the error probability is at most 1/4. It matches the upper bound obtained by the AGM sketch [AGM12], which even allows the referee to output a spanning forest of G with probability 1 – 1/poly n. Our lower bound strengthens the previous Ω(log3 n) lower bound for spanning forest computation [NY19]. Hence, it implies that connectivity, a decision problem, is as hard as its \"search\" version in this model.", "published": "2021-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.111"}]