[{"primary_key": "713661", "vector": [], "sparse_vector": [], "title": "Visualization of 2D Scalar Field Ensembles Using Volume Visualization of the Empirical Distribution Function.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Analyzing uncertainty in spatial data is a vital task in many domains, as for example with climate and weather simulation ensembles. Although many methods support the analysis of uncertain 2D data, such as uncertain isocontours or overlaying of statistical information on plots of the actual data, it is still a challenge to get a more detailed overview of 2D data together with its statistical properties. We present cumulative height fields, a visualization method for 2D scalar field ensembles using the marginal empirical distribution function and show preliminary results using volume rendering and slicing for the Max Planck Institute Grand Ensemble.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00046"}, {"primary_key": "713685", "vector": [], "sparse_vector": [], "title": "Investigating the Apple Vision Pro Spatial Computing Platform for GPU-Based Volume Visualization.", "authors": ["Camilla Hrycak", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we analyze the Apple Vision Pro hardware and the visionOS software platform, assessing their capabilities for volume rendering of structured grids—a prevalent technique across various applications. The Apple Vision Pro supports multiple display modes, from classical augmented reality (AR) using video see-through technology to immersive virtual reality (VR) environments that exclusively render virtual objects. These modes utilize different APIs and exhibit distinct capabilities. Our focus is on direct volume rendering, selected for its implementation challenges due to the native graphics APIs being predominantly oriented towards surface shading. Volume rendering is particularly vital in fields where AR and VR visualizations offer substantial benefits, such as in medicine and manufacturing. Despite its initial high cost, we anticipate that the Vision Pro will become more accessible and affordable over time, following Apple’s track record of market expansion. As these devices become more prevalent, understanding how to effectively program and utilize them becomes increasingly important, offering significant opportunities for innovation and practical applications in various sectors.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00044"}, {"primary_key": "713776", "vector": [], "sparse_vector": [], "title": "Exploring the Capability of LLMs in Performing Low-Level Visual Analytic Tasks on SVG Data Visualizations.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Xu", "<PERSON>"], "summary": "Data visualizations help extract insights from datasets, but reaching these insights requires decomposing high level goals into low-level analytic tasks that can be complex due to varying degrees of data literacy and visualization experience. Recent advancements in large language models (LLMs) have shown promise for lowering barriers for users to achieve tasks such as writing code and may likewise facilitate visualization insight. Scalable Vector Graphics (SVG), a text-based image format common in data visualizations, matches well with the text sequence processing of transformer-based LLMs. In this paper, we explore the capability of LLMs to perform 10 low-level visual analytic tasks defined by <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> directly on SVG-based visualizations [2]. Using zero-shot prompts, we instruct the models to provide responses or modify the SVG code based on given visualizations. Our findings demonstrate that LLMs can effectively modify existing SVG visualizations for some tasks like Cluster but perform poorly on tasks requiring mathematical operations like Compute Derived Value. We also discovered that LLM performance can vary based on factors such as the number of data points, the presence of value labels, and the chart type. Our findings contribute to gauging the general capabilities of LLMs and highlight the need for further exploration and development to fully harness their potential in supporting visual analytic tasks.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00033"}, {"primary_key": "713639", "vector": [], "sparse_vector": [], "title": "Diffusion Explainer: Visual Explanation for Text-to-image Stable Diffusion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Haekyu Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (Polo) Chau"], "summary": "Diffusion-based generative models’ impressive ability to create convincing images has garnered global attention. However, their complex structures and operations often pose challenges for non-experts to grasp. We present Diffusion Explainer, the first interactive visualization tool that explains how Stable Diffusion transforms text prompts into images. Diffusion Explainer tightly integrates a visual overview of Stable Diffusion’s complex structure with explanations of the underlying operations. By comparing image generation of prompt variants, users can discover the impact of keyword changes on image generation. A 56-participant user study demonstrates that Diffusion Explainer offers substantial learning benefits to non-experts. Our tool has been used by over 10,300 users from 124 countries at https://poloclub.github.io/diffusion-explainer/.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00027"}, {"primary_key": "713641", "vector": [], "sparse_vector": [], "title": "Design Patterns in Rightto-Left Visualizations: The Case of Arabic Content.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Data visualizations are reaching global audiences. As people who use Right-to-left (RTL) scripts constitute over a billion potential data visualization users, a need emerges to investigate how visualizations are communicated to them. Web design guidelines exist to assist designers in adapting different reading directions, yet we lack a similar standard for visualization design. This paper investigates the design patterns of visualizations with RTL scripts. We collected 128 visualizations from data-driven articles published in Arabic news outlets and analyzed their chart composition, textual elements, and sources. Our analysis suggests that designers tend to apply RTL approaches more frequently for categorical data. In other situations, we observed a mix of Left-to-right (LTR) and RTL approaches for chart directions and structures, sometimes inconsistently utilized within the same article. We reflect on this lack of clear guidelines for RTL data visualizations and derive implications for visualization authoring tools and future research directions.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00058"}, {"primary_key": "713642", "vector": [], "sparse_vector": [], "title": "Can GPT-4 Models Detect Misleading Visualizations?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The proliferation of misleading visualizations online, particularly during critical events like public health crises and elections, poses a significant risk. This study investigates the capability of GPT-4 models (4V, 4o, and 4o mini) to detect misleading visualizations. Utilizing a dataset of tweet-visualization pairs containing various visual misleaders, we test these models under four experimental conditions with different levels of guidance. We show that GPT-4 models can detect misleading visualizations with moderate accuracy without prior training (naive zero-shot) and that performance notably improves when provided with definitions of misleaders (guided zero-shot). However, a single prompt engineering technique does not yield the best results for all misleader types. Specifically, providing the models with misleader definitions and examples (guided few-shot) proves more effective for reasoning misleaders, while guided zero-shot performs better for design misleaders. This study underscores the feasibility of using large vision-language models to detect visual misinformation and the importance of prompt engineering for optimized detection accuracy.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00029"}, {"primary_key": "713645", "vector": [], "sparse_vector": [], "title": "Evaluating the Semantic Profiling Abilities of LLMs for Natural Language Utterances in Data Visualization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kwesi <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Automatically generating data visualizations in response to human utterances on datasets necessitates a deep semantic understanding of the utterance, including implicit and explicit references to data attributes, visualization tasks, and necessary data preparation steps. Natural Language Interfaces (NLIs) for data visualization have explored ways to infer such information, yet challenges persist due to inherent uncertainty in human speech. Recent advances in Large Language Models (LLMs) provide an avenue to address these challenges, but their ability to extract the relevant semantic information remains unexplored. In this study, we evaluate four publicly available LLMs (GPT-4, Gemini-Pro, Llama3, and Mixtral), investigating their ability to comprehend utterances even in the presence of uncertainty and identify the relevant data context and visual tasks. Our findings reveal that LLMs are sensitive to uncertainties in utterances. Despite this sensitivity, they are able to extract the relevant data context. However, LLMs struggle with inferring visualization tasks. Based on these results, we highlight future research directions on using LLMs for visualization generation. Our supplementary materials have been shared on GitHub: https://github.com/hdi-umd/Semantic_Profiling_LLM_Evaluation.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00060"}, {"primary_key": "713649", "vector": [], "sparse_vector": [], "title": "The Comic Construction Kit: An Activity for Students to Learn and Explain Data Visualizations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As visualization literacy and its implications gain prominence, we need effective methods to prepare students for the variety of visualizations in an increasingly data-driven world. Recently, the potential of comics has been recognized in various data visualization contexts, including educational settings. We describe the development of a workshop in which we use our \"comic construction kit\" as a tool for students to understand various data visualization techniques through an interactive creative approach of creating explanatory comics. We report on our insights from holding eight workshops with high school students and teachers, university students, and lecturers, aiming to enhance the landscape of handson visualization activities that can enrich the visualization classroom. The comic construction kit and all supplemental materials are open source under a CC-BY license and available at https://fhstp.github.io/comixplain/vis4schools.html.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00025"}, {"primary_key": "713651", "vector": [], "sparse_vector": [], "title": "Dash: A Bimodal Data Exploration Tool for Interactive Text and Visualizations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Integrating textual content, such as titles, annotations, and captions, with visualizations facilitates comprehension and takeaways during data exploration. Yet current tools often lack mechanisms for integrating meaningful long-form prose with visual data. This paper introduces DASH, a bimodal data exploration tool that supports integrating semantic levels into the interactive process of visualization and text-based analysis. DASH operationalizes a modified version of <PERSON><PERSON> et al.’s semantic hierarchy model that catego-rizes data descriptions into four levels ranging from basic encodings to high-level insights. By leveraging this structured semantic level framework and a large language model’s text generation capabilities, DASH enables the creation of data-driven narratives via drag-and-drop user interaction. Through a preliminary user evaluation, we discuss the utility of DASH’s text and chart integration capabilities when participants perform data exploration with the tool.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00059"}, {"primary_key": "713657", "vector": [], "sparse_vector": [], "title": "Fields, Bridges, and Foundations: How Researchers Browse Citation Network Visualizations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Sangwon Park", "Jinwook Seo"], "summary": "Visualizing citation relations with network structures is widely used, but the visual complexity can make it challenging for individual researchers trying to navigate them. We collected data from 18 researchers with an interface that we designed using network simplification methods and analyzed how users browsed and identified important papers. Our analysis reveals six major patterns used for identifying papers of interest, which can be categorized into three key components: Fields, Bridges, and Foundations, each viewed from two distinct perspectives: layout-oriented and connection-oriented. The connection-oriented approach was found to be more reliable for selecting relevant papers, but the layout-oriented method was adopted more often, even though it led to unexpected results and user frustration. Our findings emphasize the importance of integrating these components and the necessity to balance visual layouts with meaningful connections to enhance the effectiveness of citation networks in academic browsing systems.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00037"}, {"primary_key": "713658", "vector": [], "sparse_vector": [], "title": "Bavisitter: Integrating Design Guidelines into Large Language Models for Visualization Authoring.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large Language Models (LLMs) have demonstrated remarkable versatility in visualization authoring, but often generate suboptimal designs that are invalid or fail to adhere to design guidelines for effective visualization. We present Bavisitter, a natural language interface that integrates established visualization design guidelines into LLMs. Based on our survey on the design issues in LLM-generated visualizations, Bavisitter monitors the generated visualizations during a visualization authoring dialogue to detect an issue. When an issue is detected, it intervenes in the dialogue, suggesting possible solutions to the issue by modifying the prompts. We also demonstrate two use cases where <PERSON>visitter detects and resolves design issues from the actual LLM-generated visualizations.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00032"}, {"primary_key": "713659", "vector": [], "sparse_vector": [], "title": "Intuitive Design of Deep Learning Models through Visual Feedback.", "authors": ["<PERSON><PERSON><PERSON>", "Sohee Park", "GaYeon Koh", "<PERSON><PERSON><PERSON>", "Won-<PERSON>"], "summary": "In the rapidly evolving field of deep learning, traditional methodologies for designing models predominantly rely on code-based frameworks. While these approaches provide flexibility, they create a significant barrier to entry for non-experts and obscure the immediate impact of architectural decisions on model performance. In response to this challenge, recent no-code approaches have been developed with the aim of enabling easy model development through graphical interfaces. However, both traditional and no-code methodologies share a common limitation that the inability to predict model outcomes or identify issues without executing the model. To address this limitation, we introduce an intuitive visual feedback-based no-code approach to visualize and analyze deep learning models during the design phase. This approach utilizes dataflow-based visual programming with dynamic visual encoding of model architecture. A user study was conducted with deep learning developers to demonstrate the effectiveness of our approach in enhancing the model design process, improving model understanding, and facilitating a more intuitive development experience. The findings of this study suggest that real-time architectural visualization significantly contributes to more efficient model development and a deeper understanding of model behaviors.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00030"}, {"primary_key": "713660", "vector": [], "sparse_vector": [], "title": "Building and Eroding: Exogenous and Endogenous Factors that Influence Subjective Trust in Visualization.", "authors": ["<PERSON><PERSON> Crouser", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Trust is a subjective yet fundamental component of human-computer interaction, and is a determining factor in shaping the efficacy of data visualizations. Prior research has identified five dimensions of trust assessment in visualizations (credibility, clarity, reliability, familiarity, and confidence), and observed that these dimensions tend to vary predictably along with certain features of the visualization being evaluated. This raises a further question: how do the design features driving viewers’ trust assessment vary with the characteristics of the viewers themselves? By reanalyzing data from these studies through the lens of individual differences, we build a more detailed map of the relationships between design features, individual characteristics, and trust behaviors. In particular, we model the distinct contributions of endogenous design features (such as visualization type, or the use of color) and exogenous user characteristics (such as visualization literacy), as well as the interactions between them. We then use these findings to make recommendations for individualized and adaptive visualization design.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00069"}, {"primary_key": "713672", "vector": [], "sparse_vector": [], "title": "A Ridge-based Approach for Extraction and Visualization of 3D Atmospheric Fronts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "An atmospheric front is an imaginary surface that separates two distinct air masses and is commonly defined as the warm-air side of a frontal zone with high gradients of atmospheric temperature and humidity (Fig. 1, left). These fronts are a widely used conceptual model in meteorology, which are often encountered in the literature as two-dimensional (2D) front lines on surface analysis charts. This paper presents a method for computing three-dimensional (3D) atmospheric fronts as surfaces that is capable of extracting continuous and well-confined features suitable for 3D visual analysis, spatiotemporal tracking, and statistical analyses (Fig. 1, middle, right). Recently developed contour-based methods for 3D front extraction rely on computing the third derivative of a moist potential temperature field. Additionally, they require the field to be smoothed to obtain continuous large-scale structures. This paper demonstrates the feasibility of an alternative method to front extraction using ridge surface computation. The proposed method requires only the second derivative of the input field and produces accurate structures even from unsmoothed data. An application of the ridge-based method to a data set corresponding to Cyclone Friederike demonstrates its benefits and utility towards visual analysis of the full 3D structure of fronts.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00043"}, {"primary_key": "713673", "vector": [], "sparse_vector": [], "title": "Micro Visualizations on a Smartwatch: Assessing Reading Performance While Walking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With two studies, we assess how different walking trajectories (straight line, circular, and infinity) and speeds (2 km/h, 4 km/h, and 6 km/h) influence the accuracy and response time of participants reading micro visualizations on a smartwatch.We showed our participants common watch face micro visualizations including date, time, weather information, and four complications showing progress charts of fitness data.Our findings suggest that while walking trajectories did not significantly affect reading performance, overall walking activity, especially at high speeds, hurt reading accuracy and, to some extent, response time.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00017"}, {"primary_key": "713674", "vector": [], "sparse_vector": [], "title": "AEye: A Visualization Tool for Image Datasets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Image datasets serve as the foundation for machine learning models in computer vision, significantly influencing model capabilities, performance, and biases alongside architectural considerations. Therefore, understanding the composition and distribution of these datasets has become increasingly crucial. To address the need for intuitive exploration of these datasets, we propose AEye, an extensible and scalable visualization tool tailored to image datasets. AEye utilizes a contrastively trained model to embed images into semantically meaningful high-dimensional representations, facilitating data clustering and organization. To visualize the high-dimensional representations, we project them onto a two-dimensional plane and arrange images in layers so users can seamlessly navigate and explore them interactively. AEye facilitates semantic search functionalities for both text and image queries, enabling users to search for content. We open-source the codebase for AEye, and provide a simple configuration to add datasets.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00064"}, {"primary_key": "713675", "vector": [], "sparse_vector": [], "title": "Towards Reusable and Reactive Widgets for Information Visualization Research and Dissemination.", "authors": ["<PERSON>-<PERSON>"], "summary": "The information visualization research community commonly produces supporting software to demonstrate technical contributions to the field. However, developing this software tends to be an overwhelming task. The final product tends to be a research prototype without much thought for modularization and re-usability, which makes it harder to replicate and adopt. This paper presents a design pattern for facilitating the creation, dissemination, and re-utilization of visualization techniques using reactive widgets. The design pattern features basic concepts that leverage modern front-end development best practices and standards, which facilitate development and replication. The paper presents several usage examples of the pattern, templates for implementation, and even a wrapper for facilitating the conversion of any Vega [27], [28] specification into a reactive widget. https://johnguerra.co/reactiveWidgets", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00071"}, {"primary_key": "713678", "vector": [], "sparse_vector": [], "title": "Confides: A Visual Analytics Solution for Automated Speech Recognition Analysis and Exploration.", "authors": ["Sunwoo Ha", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Crouser", "<PERSON><PERSON><PERSON>"], "summary": "Confidence scores of automatic speech recognition (ASR) outputs are often inadequately communicated, preventing its seamless integration into analytical workflows. In this paper, we introduce Confides, a visual analytic system developed in collaboration with intelligence analysts to address this issue. Confides aims to aid exploration and post-AI-transcription editing by visually representing the confidence associated with the transcription. We demonstrate how our tool can assist intelligence analysts who use ASR outputs in their analytical and exploratory tasks and how it can help mitigate misinterpretation of crucial information. We also discuss opportunities for improving textual data cleaning and model transparency for human-machine collaboration.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00062"}, {"primary_key": "713680", "vector": [], "sparse_vector": [], "title": "An Overview+Detail Layout for Visualizing Compound Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Compound graphs are networks in which vertices can be grouped into larger subsets, with these subsets capable of further grouping, resulting in a nesting that can be many levels deep. In several applications, including biological workflows, chemical equations, and computational data flow analysis, these graphs often exhibit a tree-like nesting structure, where sibling clusters are disjoint. Common compound graph layouts prioritize the lowest level of the grouping, down to the individual ungrouped vertices, which can make the higher level grouped structures more difficult to discern, especially in deeply nested networks. Leveraging the additional structure of the tree-like nesting, we contribute an overview+detail layout for this class of compound graphs that preserves the saliency of the higher level network structure when groups are expanded to show internal nested structure. Our layout draws inner structures adjacent to their parents, using a modified tree layout to place substructures. We describe our algorithm and then present case studies demonstrating the layout's utility to a domain expert working on data flow analysis. Finally, we discuss network parameters and analysis situations in which our layout is well suited.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00035"}, {"primary_key": "713684", "vector": [], "sparse_vector": [], "title": "&quot;Must Be a Tuesday&quot;: Affect, Attribution, and Geographic Variability in Equity-Oriented Visualizations of Population Health Disparities.", "authors": ["<PERSON>", "Lace M. K<PERSON>"], "summary": "This study examines the impacts of public health communications visualizing risk disparities between racial and other social groups. It compares the effects of traditional bar charts to an alternative design emphasizing geographic variability with differing annotations and jitter plots. Whereas both visualization designs increased perceived vulnerability, behavioral intent, and policy support, the geo-emphasized charts were significantly more effective in reducing personal attribution biases. The findings also reveal emotionally taxing experiences for chart viewers from marginalized communities. This work suggests a need for strategic reevaluation of visual communication tools in public health to enhance understanding and engagement without reinforcing stereotypes or emotional distress.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00021"}, {"primary_key": "713692", "vector": [], "sparse_vector": [], "title": "Visualizations on Smart Watches while Running: It Actually Helps!", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Millions of runners rely on smart watches that display running-related metrics such as pace, heart rate and distance for training and racing — mostly with text and numbers. Although research tells us that visualizations are a good alternative to text on smart watches, we know little about how visualizations can help in realistic running scenarios. We conducted a study in which 20 runners completed running-related tasks on an outdoor track using both text and visualizations. Our results show that runners are 1.5 to 8 times faster in completing those tasks with visualizations than with text, prefer visualizations to text, and would use such visualizations while running — if available on their smart watch.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00016"}, {"primary_key": "713698", "vector": [], "sparse_vector": [], "title": "DaVE - A Curated Database of Visualization Examples.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Visualization, from simple line plots to complex high-dimensional visual analysis systems, has established itself throughout numerous domains to explore, analyze, and evaluate data. Applying such visualizations in the context of simulation science where High-Performance Computing (HPC) produces ever-growing amounts of data that is more complex, potentially multidimensional, and multimodal, takes up resources and a high level of technological experience often not available to domain experts. In this work, we present DaVE -- a curated database of visualization examples, which aims to provide state-of-the-art and advanced visualization methods that arise in the context of HPC applications. Based on domain- or data-specific descriptors entered by the user, DaVE provides a list of appropriate visualization techniques, each accompanied by descriptions, examples, references, and resources. Sample code, adaptable container templates, and recipes for easy integration in HPC applications can be downloaded for easy access to high-fidelity visualizations. While the database is currently filled with a limited number of entries based on a broad evaluation of needs and challenges of current HPC users, DaVE is designed to be easily extended by experts from both the visualization and HPC communities.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00010"}, {"primary_key": "713700", "vector": [], "sparse_vector": [], "title": "Active Appearance and Spatial Variation Can Improve Visibility in Area Labels for Augmented Reality.", "authors": ["<PERSON><PERSON><PERSON>", "Yuanbo Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Augmented reality (AR) area labels can visualize real world regions with arbitrary boundaries and show invisible objects or features. But environment conditions such as lighting and clutter can decrease fixed or passive label visibility, and labels that have high opacity levels can occlude crucial details in the environment. We design and evaluate active AR area label visualization modes to enhance visibility across real-life environments, while still retaining environment details within the label. For this, we define a distant characteristic color from the environment in perceptual CIELAB space, then introduce spatial variations among label pixel colors based on the underlying environment variation. In a user study with 18 participants, we found that our active label visualization modes can be comparable in visibility to a fixed green baseline by <PERSON><PERSON> et al., and can outperform it with added spatial variation in cluttered environments, across varying levels of lighting (e.g., nighttime), and in environments with colors similar to the fixed baseline color.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00049"}, {"primary_key": "713703", "vector": [], "sparse_vector": [], "title": "LinkQ: An LLM-Assisted Visual Interface for Knowledge Graph Question-Answering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present LinkQ, a system that leverages a large language model (LLM) to facilitate knowledge graph (KG) query construction through natural language question-answering. Traditional approaches often require detailed knowledge of a graph querying language, limiting the ability for users – even experts – to acquire valuable insights from KGs. LinkQ simplifies this process by implementing a multistep protocol in which the LLM interprets a user’s question, then systematically converts it into a well-formed query. LinkQ helps users iteratively refine any open-ended questions into precise ones, supporting both targeted and exploratory analysis. Further, LinkQ guards against the LLM hallucinating outputs by ensuring users’ questions are only ever answered from ground truth KG data. We demonstrate the efficacy of LinkQ through a qualitative study with five KG practitioners. Our results indicate that practitioners find LinkQ effective for KG question-answering, and desire future LLM-assisted exploratory data analysis systems.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00031"}, {"primary_key": "713711", "vector": [], "sparse_vector": [], "title": "FAVis: Visual Analytics of Factor Analysis for Psychological Research.", "authors": ["Yikai Lu", "<PERSON><PERSON>"], "summary": "Psychological research often involves understanding psychological constructs through conducting factor analysis on data collected by a questionnaire, which can comprise hundreds of questions. Without interactive systems for interpreting factor models, researchers are frequently exposed to subjectivity, potentially leading to misinterpretations or overlooked crucial information. This paper introduces FAVis, a novel interactive visualization tool designed to aid researchers in interpreting and evaluating factor analysis results. FAVis enhances the understanding of relationships between variables and factors by supporting multiple views for visualizing factor loadings and correlations, allowing users to analyze information from various perspectives. The primary feature of FAVis is to enable users to set optimal thresholds for factor loadings to balance clarity and information retention. FAVis also allows users to assign tags to variables, enhancing the understanding of factors by linking them to their associated psychological constructs. Our user study demonstrates the utility of FAVis in various tasks.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00018"}, {"primary_key": "713712", "vector": [], "sparse_vector": [], "title": "FCNR: Fast Compressive Neural Representation of Visualization Images.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present FCNR, a fast compressive neural representation for tens of thousands of visualization images under varying viewpoints and timesteps. The existing NeRVI solution, albeit enjoying a high compression ratio, incurs slow speeds in encoding and decoding. Built on the recent advances in stereo image compression, FCNR assimilates stereo context modules and joint context transfer modules to compress image pairs. Our solution significantly improves encoding and decoding speed while maintaining high reconstruction quality and satisfying compression ratio. To demonstrate its effectiveness, we compare FCNR with state-of-the-art neural compression methods, including E-NeRV, HNeRV, NeRVI, and ECSIC. The source code can be found at https://github.com/YunfeiLu0112/FCNR.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00014"}, {"primary_key": "713717", "vector": [], "sparse_vector": [], "title": "Visualizing an Exascale Data Center Digital Twin: Considerations, Challenges and Opportunities.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Digital twins are an excellent tool to model, visualize, and simulate complex systems, to understand and optimize their operation. In this work, we present the technical challenges of real-time visualization of a digital twin of the Frontier supercomputer.We show the initial prototype and current state of the twin and highlight technical design challenges of visualizing such a large High Performance Computing (HPC) system. The goal is to understand the use of augmented reality as a primary way to extract information and collaborate on digital twins of complex systems. This leverages the spatio-temporal aspect of a 3D representation of a digital twin, with the ability to view historical and real-time telemetry, triggering simulations of a system state and viewing the results, which can be augmented via dashboards for details. Finally, we discuss considerations and opportunities for augmented reality of digital twins of large-scale, parallel computers.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00012"}, {"primary_key": "713719", "vector": [], "sparse_vector": [], "title": "Demystifying Spatial Dependence: Interactive Visualizations for Interpreting Local Spatial Autocorrelation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Local Moran’s I statistic is a valuable tool for identifying localized patterns of spatial autocorrelation. Understanding these patterns is crucial in spatial analysis, but interpreting the statistic can be difficult. To simplify this process, we introduce three novel visualizations that enhance the interpretation of Local Moran’s I results. These visualizations can be interactively linked to one another, and to established visualizations, to offer a more holistic exploration of the results. We provide a JavaScript library with implementations of these new visual elements, along with a web dashboard that demonstrates their integrated use.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00022"}, {"primary_key": "713732", "vector": [], "sparse_vector": [], "title": "A Two-Phase Visualization System for Continuous Human-AI Collaboration in Sequelae Analysis and Modeling.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Yuheng Yan", "Zuoqin Yan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In healthcare, AI techniques are widely used for tasks like risk assessment and anomaly detection. Despite AI’s potential as a valuable assistant, its role in complex medical data analysis often over-simplifies human-AI collaboration dynamics. To address this, we collaborated with a local hospital, engaging six physicians and one data scientist in a formative study. From this collaboration, we propose a framework integrating two-phase interactive visualization systems: one for Human-Led, AI-Assisted Retrospective Analysis and another for AI-Mediated, Human-Reviewed Iterative Modeling. This framework aims to enhance understanding and discussion around effective human-AI collaboration in healthcare.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00028"}, {"primary_key": "713737", "vector": [], "sparse_vector": [], "title": "On Combined Visual Cluster and Set Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Real-world datasets often consist of quantitative and categorical variables. The analyst needs to focus on either kind separately or both jointly. We proposed a visualization technique tackling these challenges that supports visual cluster and set analysis. In this paper, we investigate how its visualization parameters affect the accuracy and speed of cluster and set analysis tasks in a controlled experiment. Our findings show that, with the proper settings, our visualization can support both task types well. However, we did not find settings suitable for the joint task, which provides opportunities for future research.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00034"}, {"primary_key": "713745", "vector": [], "sparse_vector": [], "title": "Hypertrix: An indicatrix for high-dimensional visualizations.", "authors": ["<PERSON><PERSON>", "Fernanda B. Viégas", "<PERSON>"], "summary": "Visualizing high dimensional data is challenging, since any dimensionality reduction technique will distort distances. A classic method in cartography–<PERSON><PERSON><PERSON>’s Indicatrix, specific to sphere-to-plane maps– visualizes distortion using ellipses. Inspired by this idea, we describe the hypertrix: a method for representing distortions that occur when data is projected from arbitrarily high dimensions onto a 2D plane. We demonstrate our technique through synthetic and real-world datasets, and describe how this indicatrix can guide interpretations of nonlinear dimensionality reduction.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00073"}, {"primary_key": "713750", "vector": [], "sparse_vector": [], "title": "A Literature-based Visualization Task Taxonomy for Gantt Charts.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Gantt charts are a widely-used idiom for visualizing temporal discrete event sequence data where dependencies exist between events. They are popular in domains such as manufacturing and computing for their intuitive layout of such data. However, these domains frequently generate data at scales which tax both the visual representation and the ability to render it at interactive speeds. To aid visualization developers who use Gantt charts in these situations, we develop a task taxonomy of low level visualization tasks supported by Gantt charts and connect them to the data queries needed to support them. Our taxonomy is derived through a literature survey of visualizations using Gantt charts over the past 30 years.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00055"}, {"primary_key": "713767", "vector": [], "sparse_vector": [], "title": "Connections Beyond Data: Exploring Homophily With Visualizations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Oded Nov"], "summary": "Homophily refers to the tendency of individuals to associate with others who are similar to them in characteristics, such as, race, ethnicity, age, gender, or interests. In this paper, we investigate if individuals exhibit racial homophily when viewing visualizations, using mass shooting data in the United States as the example topic. We conducted a crowdsourced experiment (N=450) where each participant was shown a visualization displaying the counts of mass shooting victims, highlighting the counts for one of three racial groups (White, Black, or Hispanic). Participants were assigned to view visualizations highlighting their own race or a different race to assess the influence of racial concordance on changes in affect (emotion) and attitude towards gun control. While we did not find evidence of homophily, the results showed a significant negative shift in affect across all visualization conditions. Notably, political ideology significantly impacted changes in affect, with more liberal views correlating with a more negative affect change. Our findings underscore the complexity of reactions to mass shooting visualizations and suggest that future research should consider various methodological improvements to better assess homophily effects.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00054"}, {"primary_key": "713773", "vector": [], "sparse_vector": [], "title": "Dark Mode or Light Mode? Exploring the Impact of Contrast Polarity on Visualization Performance Between Age Groups.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This study examines the impact of positive and negative contrast polarities (i.e., light and dark modes) on the performance of younger adults and people in their late adulthood (PLA). In a crowdsourced study with 134 participants (69 below age 60, 66 aged 60 and above), we assessed their accuracy and time performing analysis tasks across three common visualization types (Bar, Line, Scatterplot) and two contrast polarities (positive and negative). We observed that, across both age groups, the polarity that led to better performance and the resulting amount of improvement varied on an individual basis, with each polarity benefiting comparable proportions of participants. However, the contrast polarity that led to better performance did not always match their preferred polarity. Additionally, we observed that the choice of contrast polarity can have an impact on time similar to that of the choice of visualization type, resulting in an average percent difference of around 36%. These findings indicate that, overall, the effects of contrast polarity on visual analysis performance do not noticeably change with age. Furthermore, they underscore the importance of making visualizations available in both contrast polarities to better-support a broad audience with differing needs. Supplementary materials for this work can be found at https://osf.io/539a4/.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00050"}, {"primary_key": "713777", "vector": [], "sparse_vector": [], "title": "Animating the Narrative: A Review of Animation Styles in Narrative Visualization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Narrative visualization has become a crucial tool in data presentation, merging storytelling with data visualization to convey complex information in an engaging and accessible manner. In this study, we review the design space for narrative visualizations, focusing on animation style, through a comprehensive analysis of 80 papers from key visualization venues. We categorize these papers into six broad themes: Animation Style, Interactivity, Technology Usage, Methodology Development, Evaluation Type, and Application Domain. Our findings reveal a significant evolution in the field, marked by a growing preference for animated and non-interactive techniques. This trend reflects a shift towards minimizing user interaction while enhancing the clarity and impact of data presentation. We also identified key trends and technologies shaping the field, highlighting the role of technologies, such as machine learning in driving these changes. We offer insights into the dynamic interrelations within the narrative visualization domains, and suggest future research directions, including exploring non-interactive techniques, examining the interplay between different visualization elements, and developing domain-specific visualizations.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00074"}, {"primary_key": "713779", "vector": [], "sparse_vector": [], "title": "A Comparative Study of Neural Surface Reconstruction for Scientific Visualization.", "authors": ["<PERSON><PERSON> Yao", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This comparative study evaluates various neural surface reconstruction methods, particularly focusing on their implications for scientific visualization through reconstructing 3D surfaces via multi-view rendering images. We categorize ten methods into neural radiance fields and neural implicit surfaces, uncovering the benefits of leveraging distance functions (i.e., SDFs and UDFs) to enhance the accuracy and smoothness of the reconstructed surfaces. Our findings highlight the efficiency and quality of NeuS2 for reconstructing closed surfaces and identify NeUDF as a promising candidate for reconstructing open surfaces despite some limitations. By sharing our benchmark dataset, we invite researchers to test the performance of their methods, contributing to the advancement of surface reconstruction solutions for scientific visualization.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00045"}, {"primary_key": "713780", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: On-the-fly Assistant for Exploratory Visual Data Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Exploratory visual data analysis tools empower data analysts to efficiently and intuitively explore data insights throughout the entire analysis cycle. However, the gap between common programmatic analysis (e.g., within computational notebooks) and exploratory visual analysis leads to a disjointed and inefficient data analysis experience. To bridge this gap, we developed PyGWalker, a Python library that offers on-the-fly assistance for exploratory visual data analysis. It features a lightweight and intuitive GUI with a shelf builder modality. Its loosely coupled architecture supports multiple computational environments to accommodate varying data sizes. Since its release in February 2023, PyGWalker has gained much attention, with 612k downloads on PyPI and over 10.5k stars on GitHub as of June 2024. This demonstrates its value to the data science and visualization community, with researchers and developers integrating it into their own applications and studies.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00009"}, {"primary_key": "713783", "vector": [], "sparse_vector": [], "title": "Design of a Real-Time Visual Analytics Decision Support Interface to Manage Air Traffic Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Carl <PERSON>", "<PERSON>", "<PERSON>"], "summary": "An essential task of an air traffic controller is to manage the traffic flow by predicting future trajectories. Complex traffic patterns are difficult to predict and manage and impose cognitive load on the air traffic controllers. In this work we present an interactive visual analytics interface which facilitates detection and resolution of complex traffic patterns for air traffic controllers. The interface supports air traffic controllers in detecting complex clusters of aircraft and further enables them to visualize and simultaneously compare how different re-routing strategies for each individual aircraft yield reduction of complexity in the entire sector for the next hour. The development of the concepts was supported by the domain-specific feedback we received from six fully licensed and operational air traffic controllers in an iterative design process over a period of 14 months.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00068"}, {"primary_key": "713655", "vector": [], "sparse_vector": [], "title": "Representing Charts as Text for Language Models: An In-Depth Study of Question Answering for Bar Charts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Machine Learning models for chart-grounded Q&A (CQA) often treat charts as images, but performing CQA on pixel values has proven challenging. We thus investigate a resource overlooked by current ML-based approaches: the declarative documents describing how charts should visually encode data (i.e., chart specifications). In this work, we use chart specifications to enhance language models (LMs) for chart-reading tasks, such that the resulting system can robustly understand language for CQA. Through a case study with 359 bar charts, we test novel fine tuning schemes on both GPT-3 and T5 using a new dataset curated for two CQA tasks: question-answering and visual explanation generation. Our text-only approaches strongly outperform vision-based GPT-4 on explanation generation (99% vs. 63% accuracy), and show promising results for question-answering (57–67% accuracy). Through in-depth experiments, we also show that our text-only approaches are mostly robust to natural language variation.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00061"}, {"primary_key": "713669", "vector": [], "sparse_vector": [], "title": "Two-point Equidistant Projection and Degree-of-interest Filtering for Smooth Exploration of Geo-referenced Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The visualization and interactive exploration of geo-referenced networks poses challenges if the network’s nodes are not evenly distributed. Our approach proposes new ways of realizing animated transitions for exploring such networks from an ego-perspective. We aim to reduce the required screen estate while maintaining the viewers’ mental map of distances and directions. A preliminary study provides first insights of the comprehensiveness of animated geographic transitions regarding directional relationships between start and end point in different projections. Two use cases showcase how ego-perspective graph exploration can be supported using less screen space than previous approaches.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00023"}, {"primary_key": "713670", "vector": [], "sparse_vector": [], "title": "Opening the Black Box of 3D Reconstruction Error Analysis with VECTOR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reconstruction of 3D scenes from 2D images is a technical challenge that impacts domains from Earth and planetary sciences and space exploration to augmented and virtual reality. Typically, reconstruction algorithms first identify common features across images and then minimize reconstruction errors after estimating the shape of the terrain. This bundle adjustment (BA) step optimizes around a single, simplifying scalar value that obfuscates many possible causes of reconstruction errors (e.g., initial estimate of the position and orientation of the camera, lighting conditions, ease of feature detection in the terrain). Reconstruction errors can lead to inaccurate scientific inferences or endanger a spacecraft exploring a remote environment. To address this challenge, we present VECTOR, a visual analysis tool that improves error inspection for stereo reconstruction BA. VECTOR provides analysts with previously unavailable visibility into feature locations, camera pose, and computed 3D points. VECTOR was developed in partnership with the Perseverance Mars Rover and Ingenuity Mars Helicopter terrain reconstruction team at the NASA Jet Propulsion Laboratory. We report on how this tool was used to debug and improve terrain reconstruction for the Mars 2020 mission.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00065"}, {"primary_key": "713671", "vector": [], "sparse_vector": [], "title": "Groot: A System for Editing and Configuring Automated Data Insights.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Visualization tools now commonly present automated insights highlighting salient data patterns, including correlations, distributions, outliers, and differences, among others. While these insights are valuable for data exploration and chart interpretation, users currently only have a binary choice of accepting or rejecting them, lacking the flexibility to refine the system logic or customize the insight generation process. To address this limitation, we present Groot, a prototype system that allows users to proactively specify and refine automated data insights. The system allows users to directly manipulate chart elements to receive insight recommendations based on their selections. Additionally, Groot provides users with a manual editing interface to customize, reconfigure, or add new insights to individual charts and propagate them to future explorations. We describe a usage scenario to illustrate how these features collectively support insight editing and configuration and discuss opportunities for future work, including incorporating Large Language Models (LLMs), improving semantic data and visualization search, and supporting insight management.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00015"}, {"primary_key": "713687", "vector": [], "sparse_vector": [], "title": "Text-based transfer function design for semantic volume rendering.", "authors": ["Sangwon Jeong", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Transfer function design is crucial in volume rendering, as it directly influences the visual representation and interpretation of volumetric data. However, creating effective transfer functions that align with users’ visual objectives is often challenging due to the complex parameter space and the semantic gap between transfer function values and features of interest within the volume. In this work, we propose a novel approach that leverages recent advancements in language-vision models to bridge this semantic gap. By employing a fully differentiable rendering pipeline and an image-based loss function guided by language descriptions, our method generates transfer functions that yield volume-rendered images closely matching the user’s intent. We demonstrate the effectiveness of our approach in creating meaningful transfer functions from simple descriptions, empowering users to intuitively express their desired visual outcomes with minimal effort. This advancement streamlines the transfer function design process and makes volume rendering more accessible to a wider range of users.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00047"}, {"primary_key": "713691", "vector": [], "sparse_vector": [], "title": "GhostUMAP: Measuring Pointwise Instability in Dimensionality Reduction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although many dimensionality reduction (DR) techniques employ stochastic methods for computational efficiency, such as negative sampling or stochastic gradient descent, their impact on the projection has been underexplored. In this work, we investigate how such stochasticity affects the stability of projections and present a novel DR technique, GhostUMAP, to measure the pointwise instability of projections. Our idea is to introduce clones of data points, \"ghosts\", into UMAP’s layout optimization process. Ghosts are designed to be completely passive: they do not affect any others but are influenced by attractive and repulsive forces from the original data points. After a single optimization run, GhostUMAP can capture the projection instability of data points by measuring the variance with the projected positions of their ghosts. We also present a successive halving technique to reduce the computation of GhostUMAP. Our results suggest that Ghost-UMAP can reveal unstable data points with a reasonable computational overhead.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00040"}, {"primary_key": "713693", "vector": [], "sparse_vector": [], "title": "Use-Coordination: Model, Grammar, and Library for Implementation of Coordinated Multiple Views.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Coordinated multiple views (CMV) in a visual analytics system can help users explore multiple data representations simultaneously with linked interactions. However, the implementation of coordinated multiple views can be challenging. Without standard software libraries, visualization designers need to re-implement CMV during the development of each system. We introduce use-coordination, a grammar and software library that supports the efficient implementation of CMV. The grammar defines a JSON-based representation for an abstract coordination model from the information visualization literature. We contribute an optional extension to the model and grammar that allows for hierarchical coordination. Through three use cases, we show that use-coordinationenables implementation of CMV in systems containing not only basic statistical charts but also more complex visualizations such as medical imaging volumes. We describe six software extensions, including a graphical editor for manipulation of coordination, which showcase the potential to build upon our coordination-focused declarative approach. The software is open-source and available at https://use-coordination.dev.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00041"}, {"primary_key": "713696", "vector": [], "sparse_vector": [], "title": "Bringing Data into the Conversation: Adapting Content from Business Intelligence Dashboards for Threaded Collaboration Platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "To enable data-driven decision-making across organizations, data professionals need to share insights with their colleagues in context-appropriate communication channels. Many of their colleagues rely on data but are not themselves analysts; furthermore, their colleagues are reluctant or unable to use dedicated analytical applications or dashboards, and they expect communication to take place within threaded collaboration platforms such as Slack or Microsoft Teams. In this paper, we introduce a set of six strategies for adapting content from business intelligence (BI) dashboards into appropriate formats for sharing on collaboration platforms, formats that we refer to as dashboard snapshots. Informed by prior studies of enterprise communication around data, these strategies go beyond redesigning or restyling by considering varying levels of data literacy across an organization, introducing affordances for self-service question-answering, and anticipating the post-sharing lifecycle of data artifacts. These strategies involve the use of templates that are matched to common communicative intents, serving to reduce the workload of data professionals. We contribute a formal representation of these strategies and demonstrate their applicability in a comprehensive enterprise communication scenario featuring multiple stakeholders that unfolds over the span of months.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00024"}, {"primary_key": "713697", "vector": [], "sparse_vector": [], "title": "Gridlines Mitigate Sine Illusion in Line Charts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The sine illusion is an underestimation of the difference between two lines when both lines have increasing slopes. We evaluate three visual manipulations on mitigating sine illusions: dotted lines, aligned gridlines, and offset gridlines via a user study. We asked participants to compare the deltas between two lines at two time points and found aligned gridlines to be the most effective in mitigating sine illusions. Using data from the user study, we produced a model that predicts the impact of the sine illusion in line charts by accounting for the ratio of the vertical distance between the two points of comparison. When the ratio is less than 50%, participants begin to be influenced by the sine illusion. This effect can be significantly exacerbated when the difference between the two deltas falls under 30%. We compared two explanations for the sine illusion based on our data: either participants were mistakenly using the perpendicular distance between the two lines to make their comparison (the perpendicular explanation), or they incorrectly relied on the length of the line segment perpendicular to the angle bisector of the bottom and top lines (the equal triangle explanation). We found the equal triangle explanation to be the more predictive model explaining participant behaviors.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00057"}, {"primary_key": "713701", "vector": [], "sparse_vector": [], "title": "Assessing Graphical Perception of Image Embedding Models using Channel Effectiveness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Seokhyeon Park", "Jinwook Seo"], "summary": "Recent advancements in vision models have greatly improved their ability to handle complex chart understanding tasks, like chart captioning and question answering. However, it remains challenging to assess how these models process charts. Existing benchmarks only roughly evaluate model performance without evaluating the underlying mechanisms, such as how models extract image embeddings. This limits our understanding of the model’s ability to perceive fundamental graphical components. To address this, we introduce a novel evaluation framework to assess the graphical perception of image embedding models. For chart comprehension, we examine two main aspects of channel effectiveness: accuracy and discriminability of various visual channels. Channel accuracy is assessed through the linearity of embeddings, measuring how well the perceived magnitude aligns with the size of the stimulus. Discrim-inability is evaluated based on the distances between embeddings, indicating their distinctness. Our experiments with the CLIP model show that it perceives channel accuracy differently from humans and shows unique discriminability in channels like length, tilt, and curvature. We aim to develop this work into a broader benchmark for reliable visual encoders, enhancing models for precise chart comprehension and human-like perception in future applications.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00053"}, {"primary_key": "713705", "vector": [], "sparse_vector": [], "title": "AltGeoViz: Facilitating Accessible Geovisualization.", "authors": ["<PERSON>", "Rock Yu<PERSON>", "<PERSON><PERSON>", "Arnavi Chheda-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Geovisualizations are powerful tools for exploratory spatial analysis, enabling sighted users to discern patterns, trends, and relationships within geographic data. However, these visual tools have remained largely inaccessible to screen-reader users. We introduce AltGeoViz, a new interactive geovisualization approach that dynamically generates alt-text descriptions based on the user’s current map view, providing voiceover summaries of spatial patterns and descriptive statistics. In a remote user study with five screen-reader users, we found that participants were able to interact with spatial data in previously infeasible ways, demonstrated a clear understanding of data summaries and their location context, and could synthesize spatial understandings of their explorations. Moreover, we identified key areas for improvement, such as the addition of spatial navigation controls and comparative analysis features.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00020"}, {"primary_key": "713707", "vector": [], "sparse_vector": [], "title": "ImageSI: Semantic Interaction for Deep Learning Image Projections.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Semantic interaction (SI) in Dimension Reduction (DR) of images allows users to incorporate feedback through direct manipulation of the 2D positions of images. Through interaction, users specify a set of pairwise relationships that the DR should aim to capture. Existing methods for images incorporate feedback into the DR through feature weights on abstract embedding features. However, if the original embedding features do not suitably capture the users’ task then the DR cannot either. We propose ImageSI, an SI method for image DR that incorporates user feedback directly into the image model to update the underlying embeddings, rather than weighting them. In doing so, ImageSI ensures that the embeddings suitably capture the features necessary for the task so that the DR can subsequently organize images using those features. We present two variations of ImageSI using different loss functions - ${\\text{ImageS}}{{\\text{I}}_{{\\text{MD}}{{\\text{S}}^{ - 1}}}}$, which prioritizes the explicit pairwise relationships from the interaction and ImageSITriplet, which prioritizes clustering, using the interaction to define groups of images. Finally, we present a usage scenario and a simulation-based evaluation to demonstrate the utility of ImageSI and compare it to current methods.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00026"}, {"primary_key": "713716", "vector": [], "sparse_vector": [], "title": "Improving Property Graph Layouts by Leveraging Attribute Similarity for Structurally Equivalent Nodes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many real-world networks contain structurally-equivalent nodes. These are defined as vertices that share the same set of neighboring nodes, making them interchangeable with a traditional graph layout approach. However, many real-world graphs also have properties associated with nodes, adding additional meaning to them. We present an approach for swapping locations of structurally-equivalent nodes in graph layout so that those with more similar properties have closer proximity to each other. This improves the usefulness of the visualization from an attribute perspective without negatively impacting the visualization from a structural perspective. We include an algorithm for finding these sets of nodes in linear time, as well as methodologies for ordering nodes based on their attribute similarity, which works for scalar, ordinal, multidimensional, and categorical data.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00036"}, {"primary_key": "713720", "vector": [], "sparse_vector": [], "title": "Science in a Blink: Supporting Ensemble Perception in Scalar Fields.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Visualizations support rapid analysis of scientific datasets, allowing viewers to glean aggregate information (e.g., the mean) within split-seconds. While prior research has explored this ability in conventional charts, it is unclear if spatial visualizations used by computational scientists afford a similar ensemble perception capacity. We investigate people’s ability to estimate two summary statistics, mean and variance, from pseudocolor scalar fields. In a crowd- sourced experiment, we find that participants can reliably characterize both statistics, although variance discrimination requires a much stronger signal. Multi-hue and diverging colormaps outperformed monochromatic, luminance ramps in aiding this extraction. Analysis of qualitative responses suggests that participants often estimate the distribution of hotspots and valleys as visual proxies for data statistics. These findings suggest that people’s summary interpretation of spatial datasets is likely driven by the appearance of discrete color segments, rather than assessments of overall luminance. Implicit color segmentation in quantitative displays could thus prove more useful than previously assumed by facilitating quick, gist- level judgments about color-coded visualizations.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00051"}, {"primary_key": "713722", "vector": [], "sparse_vector": [], "title": "Multi-User Mobile Augmented Reality for Cardiovascular Surgical Planning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Faw<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Collaborative planning for congenital heart diseases typically involves creating physical heart models through 3D printing, which are then examined by both surgeons and cardiologists. Recent developments in mobile augmented reality (AR) technologies have presented a viable alternative, known for their ease of use and portability. However, there is still a lack of research examining the utilization of multi-user mobile AR environments to support collaborative planning for cardiovascular surgeries. We created ARCOLLAB, an iOS AR app designed for enabling multiple surgeons and cardiologists to interact with a patient’s 3D heart model in a shared environment. ARCOLLAB enables surgeons and cardiologists to import heart models, manipulate them through gestures and collaborate with other users, eliminating the need for fabricating physical heart models. Our evaluation of ARCOL-LAB’s usability and usefulness in enhancing collaboration, conducted with three cardiothoracic surgeons and two cardiologists, marks the first human evaluation of a multi-user mobile AR tool for surgical planning. ARCOLLAB is open-source, available at https://github.com/poloclub/arcollab.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00048"}, {"primary_key": "713723", "vector": [], "sparse_vector": [], "title": "Towards a Quality Approach to Hierarchical Color Maps.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To improve the perception of hierarchical structures in data sets, several color map generation algorithms have been proposed to take this structure into account. But the design of hierarchical color maps elicits different requirements to those of color maps for tabular data. Within this paper, we make an initial effort to put design rules from the color map literature into the context of hierarchical color maps. We investigate the impact of several design decisions and provide recommendations for various analysis scenarios. Thus, we lay the foundation for objective quality criteria to evaluate hierarchical color maps.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00052"}, {"primary_key": "713728", "vector": [], "sparse_vector": [], "title": "What Color Scheme is More Effective in Assisting Readers to Locate Information in a Color-Coded Article?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Color coding, a technique assigning specific colors to cluster information types, has proven advantages in aiding human cognitive activities, especially reading and comprehension. The rise of Large Language Models (LLMs) has streamlined document coding, enabling simple automatic text labeling with various schemes. This has the potential to make color-coding more accessible and benefit more users. However, the impact of color choice on information seeking is understudied. We conducted a user study assessing various color schemes’ effectiveness in LLM-coded text documents, standardizing contrast ratios to approximately 5.55:1 across schemes. Participants performed timed information-seeking tasks in color-coded scholarly abstracts. Results showed non-analogous and yellow-inclusive color schemes improved performance, with the latter also being more preferred by participants. These findings can inform better color scheme choices for text annotation. As LLMs advance document coding, we advocate for more research focusing on the \"color\" aspect of color-coding techniques.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00066"}, {"primary_key": "713733", "vector": [], "sparse_vector": [], "title": "Feature Clock: High-Dimensional Effects in Two-Dimensional Plots.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Humans struggle to perceive and interpret high-dimensional data. Therefore, high-dimensional data are often projected into two dimensions for visualization. Many applications benefit from complex nonlinear dimensionality reduction techniques, but the effects of individual high-dimensional features are hard to explain in the two-dimensional space. Most visualization solutions use multiple two-dimensional plots, each showing the effect of one high-dimensional feature in two dimensions; this approach creates a need for a visual inspection of k plots for a k-dimensional input space. Our solution, Feature Clock, provides a novel approach that reduces the need to inspect these k plots to grasp the influence of original features on the data structure depicted in two dimensions. Feature Clock enhances the explainability and compactness of visualizations of embedded data and is available in an open-source Python library1.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00038"}, {"primary_key": "713736", "vector": [], "sparse_vector": [], "title": "Curve Segment Neighborhood-based Vector Field Exploration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Integral curves have been widely used to represent and analyze various vector fields. In this paper, we propose a Curve Segment Neighborhood Graph (CSNG) to capture the relationships between neighboring curve segments. This graph representation enables us to adapt the fast community detection algorithm, i.e., the <PERSON><PERSON><PERSON> algorithm, to identify individual graph communities from CSNG. Our results show that these communities often correspond to the features of the flow. To achieve a multi-level interactive exploration of the detected communities, we adapt a force-directed layout that allows users to refine and re-group communities based on their domain knowledge. We incorporate the proposed techniques into an interactive system to enable effective analysis and interpretation of complex patterns in large-scale integral curve datasets.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00075"}, {"primary_key": "713744", "vector": [], "sparse_vector": [], "title": "Accelerating Transfer Function Update for Distance Map based Volume Rendering.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Direct volume rendering using ray-casting is widely used in practice. By using GPUs and applying acceleration techniques as empty space skipping, high frame rates are possible on modern hardware. This enables performance-critical use-cases such as virtual reality volume rendering. The currently fastest known technique uses volumetric distance maps to skip empty sections of the volume during ray-casting but requires the distance map to be updated per transfer function change. In this paper, we demonstrate a technique for subdividing the volume intensity range into partitions and deriving what we call partitioned distance maps. These can be used to accelerate the distance map computation for a newly changed transfer function by a factor up to 30. This allows the currently fastest known empty space skipping approach to be used while maintaining high frame rates even when the transfer function is changed frequently.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00042"}, {"primary_key": "713746", "vector": [], "sparse_vector": [], "title": "Uniform Sample Distribution in Scatterplots via Sector-based Transformation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A high number of samples often leads to occlusion in scatter-plots, which hinders data perception and analysis. De-cluttering approaches based on spatial transformation reduce visual clutter by remapping samples using the entire available scatterplot domain. Such regularized scatterplots may still be used for data analysis tasks, if the spatial transformation is smooth and preserves the original neighborhood relations of samples. Recently, <PERSON><PERSON> et al. [21] proposed an efficient regularization method based on integral images. We propose a generalization of their regularization scheme using sector-based transformations with the aim of increasing sample uniformity of the resulting scatterplot. We document the improvement of our approach using various uniformity measures.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00039"}, {"primary_key": "713762", "vector": [], "sparse_vector": [], "title": "Zoomable Level-of-Detail ChartTables for Interpreting Probabilistic Model Outputs for Reactionary Train Delays.", "authors": ["<PERSON>", "<PERSON>"], "summary": "\"Reactionary delay\" is a result of the accumulated cascading effects of knock-on train delays which is increasing on UK railways due to increasing utilisation of the railway infrastructure. The chaotic nature of its effects on train lateness is notoriously hard to predict. We use a stochastic Monte-Carto-style simulation of reactionary delay that produces whole distributions of likely reactionary delay and delays this causes. We demonstrate how Zoomable Level-of-Detail ChartTables – case-by-variable tables where cases are rows, variables are columns, variables are complex composite metrics that incorporate distributions, and cells contain mini-charts that depict these as different levels of detail through zoom interaction – help interpret whole distributions of model outputs to help understand the causes and effects of reactionary delay, how they inform timetable robustness testing, and how they could be used in other contexts.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00056"}, {"primary_key": "713764", "vector": [], "sparse_vector": [], "title": "Integrating Annotations for Sonifications and Physicalizations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-Brock"], "summary": "Annotations are a critical component of visualizations, helping viewers interpret the visual representation and highlighting critical data insights. Despite their significant role, we lack an understanding of how annotations can be incorporated into other data representations, such as physicalizations and sonifications. Given the emergent nature of these representations, sonifications, and physicalizations lack formalized conventions (e.g., design space, vocabulary) that can introduce challenges for audiences to interpret the intended data encoding. To address this challenge, this work focuses on how annotations can be more tightly integrated into the design process of creating sonifications and physicalizations. In an exploratory study with 13 designers, we explore how visualization annotation techniques can be adapted to sonic and physical modalities. Our work highlights how annotations for sonification and physicalizations are inseparable from their data encodings.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00063"}, {"primary_key": "713768", "vector": [], "sparse_vector": [], "title": "Data Guards: Challenges and Solutions for Fostering Trust in Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "From dirty data to intentional deception, there are many threats to the validity of data-driven decisions. Making use of data, especially new or unfamiliar data, therefore requires a degree of trust or verification. How is this trust established? In this paper, we present the results of a series of interviews with both producers and consumers of data artifacts (outputs of data ecosystems like spreadsheets, charts, and dashboards) aimed at understanding strategies and obstacles to building trust in data. We find a recurring need, but lack of existing standards, for data validation and verification, especially among data consumers. We therefore propose a set of data guards: methods and tools for fostering trust in data artifacts.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00019"}, {"primary_key": "713775", "vector": [], "sparse_vector": [], "title": "From Graphs to Words: A Computer-Assisted Framework for the Production of Accessible Text Descriptions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In the digital landscape, the ubiquity of data visualizations in media underscores the necessity for accessibility to ensure inclusivity for all users, including those with visual impairments. Current visual content often fails to cater to the needs of screen reader users due to the absence of comprehensive textual descriptions. To address this gap, we propose in this paper a framework designed to empower media content creators to transform charts into descriptive narratives. This tool not only facilitates the understanding of complex visual data through text but also fosters a broader awareness of accessibility in digital content creation. Through the application of this framework, users can interpret and convey the insights of data visualizations more effectively, accommodating a diverse audience. Our evaluations reveal that this tool not only enhances the comprehension of data visualizations but also promotes new perspectives on the represented data, thereby broadening the interpretative possibilities for all users.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00067"}, {"primary_key": "713781", "vector": [], "sparse_vector": [], "title": "Topological Separation of Vortices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Vortices and their analysis play a critical role in the understanding of complex phenomena in turbulent flows. Traditional vortex extraction methods, notably region-based techniques, often overlook the entanglement phenomenon, resulting in the inclusion of multiple vortices within a single extracted region. Their separation is necessary for quantifying different types of vortices and their statistics. In this study, we propose a novel vortex separation method that extends the conventional contour tree-based segmentation approach with an additional step termed \"layering\". Upon extracting a vortical region using specified vortex criteria (e.g., λ2), we initially establish topological segmentation based on the contour tree, followed by the layering process to allocate appropriate segmentation IDs to unsegmented cells, thus separating individual vortices within the region. However, these regions may still suffer from inaccurate splits, which we address statistically by leveraging the continuity of vorticity lines across the split boundaries. Our findings demonstrate a significant improvement in both the separation of vortices and the mitigation of inaccurate splits compared to prior methods.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00070"}, {"primary_key": "713782", "vector": [], "sparse_vector": [], "title": "Guided Statistical Workflows with Interactive Explanations and Assumption Checking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Statistical practices such as building regression models or running hypothesis tests rely on following rigorous procedures of steps and verifying assumptions on data to produce valid results. However, common statistical tools do not verify users’ decision choices and provide low-level statistical functions without instructions on the whole analysis practice. Users can easily misuse analysis methods, potentially decreasing the validity of results. To address this problem, we introduce GuidedStats, an interactive interface within computational notebooks that encapsulates guidance, models, visualization, and exportable results into interactive workflows. It breaks down typical analysis processes, such as linear regression and two-sample T-tests, into interactive steps supplemented with automatic visualizations and explanations for step-wise evaluation. Users can iterate on input choices to refine their models, while recommended actions and exports allow the user to continue their analysis in code. Case studies show how GuidedStats offers valuable instructions for conducting fluid statistical analyses while finding possible assumption violations in the underlying data, supporting flexible and accurate statistical analyses.", "published": "2024-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VIS55277.2024.00013"}]