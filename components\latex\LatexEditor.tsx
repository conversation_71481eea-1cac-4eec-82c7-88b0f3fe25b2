import { useRef, useEffect, useState } from "react";
import { useTheme } from "next-themes";
import Prism from 'prismjs';
import 'prismjs/components/prism-latex';
import './latex-theme.css';

interface LatexEditorProps {
  value: string;
  onChange: (value: string) => void;
}

// 增强LaTeX语法高亮
function enhanceLatexHighlighting(html: string): string {
  // 高亮LaTeX命令 \command{...}
  html = html.replace(/(\\[a-zA-Z]+)(\{)/g, '<span class="token latex-command">$1</span>$2');
  
  // 高亮数学环境 $$...$$
  html = html.replace(/(\$\$)([^$]+)(\$\$)/g, '<span class="token latex-math">$1$2$3</span>');
  
  // 高亮环境 \begin{...}...\end{...}
  html = html.replace(/(\\begin\{[^}]+\})([\s\S]*?)(\\end\{[^}]+\})/g, 
    '<span class="token latex-environment">$1</span>$2<span class="token latex-environment">$3</span>');
  
  return html;
}

export function LatexEditor({ value, onChange }: LatexEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const highlightRef = useRef<HTMLDivElement>(null);
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [progress, setProgress] = useState(0); // 进度条百分比
  const progressRef = useRef<number>(0);
  
  // 更新高亮显示
  useEffect(() => {
    if (highlightRef.current) {
      // 将内容转换为HTML并应用Prism高亮
      let html = Prism.highlight(value, Prism.languages.latex, 'latex');
      
      // 增强LaTeX特定语法高亮
      html = enhanceLatexHighlighting(html);
      
      // 确保末尾有换行，以便光标可以到达最后
      highlightRef.current.innerHTML = html + (html.endsWith('\n') ? '' : '<br/>');
    }
  }, [value]);

  // 同步滚动
  useEffect(() => {
    const textarea = textareaRef.current;
    const highlight = highlightRef.current;
    
    if (!textarea || !highlight) return;
    
    const syncScroll = () => {
      highlight.scrollTop = textarea.scrollTop;
      highlight.scrollLeft = textarea.scrollLeft;
    };
    
    textarea.addEventListener('scroll', syncScroll);
    return () => textarea.removeEventListener('scroll', syncScroll);
  }, []);
  
  // 自动调整高度
  useEffect(() => {
    const textarea = textareaRef.current;
    const editor = editorRef.current;
    
    if (!textarea || !editor) return;
    
    const adjustHeight = () => {
      const minHeight = 300;
      const newHeight = Math.max(minHeight, textarea.scrollHeight);
      editor.style.height = `${newHeight}px`;
    };
    
    adjustHeight();
    
    // 添加输入事件监听器以调整高度
    textarea.addEventListener('input', adjustHeight);
    return () => textarea.removeEventListener('input', adjustHeight);
  }, [value]);
  
  // 进度条动画控制
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isRecognizing) {
      setProgress(0);
      progressRef.current = 0;
      // 第一阶段：0-30%（0.2秒）
      timer = setTimeout(() => {
        setProgress(10);
        progressRef.current = 30;
        // 第二阶段：30%-80%（0.8秒）
        timer = setTimeout(() => {
          setProgress(60);
          progressRef.current = 60;
        }, 800);
      }, 200);
    } else {
      // 识别结束后，进度条补到100%并消失
      if (progressRef.current < 100) {
        setProgress(100);
        timer = setTimeout(() => setProgress(0), 400);
      } else {
        setProgress(0);
      }
    }
    return () => clearTimeout(timer);
  }, [isRecognizing]);

  // 粘贴图片识别latex
  const handlePaste = async (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const items = e.clipboardData.items;
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.kind === "file" && item.type.startsWith("image/")) {
        e.preventDefault();
        const file = item.getAsFile();
        if (!file) return;
        setIsRecognizing(true);
        const reader = new FileReader();
        reader.onload = async () => {
          const base64 = (reader.result as string).split(",")[1];
          const res = await fetch("/api/sc-api-chat", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ imageBase64: base64 })
          });
          const data = await res.json();
          // 先补到100%，进度条消失后再显示内容
          setIsRecognizing(false); // 触发进度条补到100%
          setTimeout(() => {
            if (data.latex) {
              // 删除所有的 $ 符号
              const cleanLatex = data.latex.replace(/\$/g, '');
              onChange(value + cleanLatex);
            } else {
              alert("图片识别失败");
            }
          }, 400); // 400ms后（进度条消失后）再更新内容
        };
        reader.readAsDataURL(file);
        break;
      }
    }
  };

  // Tab键处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab') {
      e.preventDefault();
      const textarea = e.currentTarget as HTMLTextAreaElement;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      
      // 插入两个空格
      const newValue = value.substring(0, start) + '  ' + value.substring(end);
      onChange(newValue);
      
      // 重新设置光标位置
      setTimeout(() => {
        textarea.selectionStart = start + 2;
        textarea.selectionEnd = start + 2;
      }, 0);
    }
  };

  const bgColor = isDarkMode ? '#1e1e1e' : '#ffffff';
  const textColor = isDarkMode ? '#d4d4d4' : '#333333';

  return (
    <div className="flex flex-col gap-2 relative">
      <div className="text-sm font-medium">LaTeX 编辑区</div>
      
      {/* 进度条 */}
      {progress > 0 && (
        <div style={{
          position: 'absolute',
          top: '2rem',
          left: 8,
          width: 'calc(100% - 16px)',
          height: 8,
          zIndex: 10,
          pointerEvents: 'none',
          background: 'transparent',
        }}>
          <div style={{
            height: '100%',
            width: progress + '%',
            background: 'linear-gradient(90deg, #60a5fa 0%, #2563eb 100%)',
            borderRadius: 4,
            transition: 'width 0.4s cubic-bezier(0.4,0,0.2,1)',
            boxShadow: '0 1px 4px 0 #2563eb33',
          }} />
        </div>
      )}
      
      {/* 编辑器容器 */}
      <div 
        ref={editorRef}
        className="latex-editor"
        style={{
          position: 'relative',
          minHeight: '300px',
          border: '1px solid #e2e8f0',
          borderRadius: '0.75rem',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        }}
      >
        {/* 语法高亮层 */}
        <div 
          ref={highlightRef}
          className="highlight-layer"
          data-theme={isDarkMode ? 'dark' : 'light'}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            padding: '1rem',
            backgroundColor: bgColor,
            color: textColor,
            fontFamily: 'geist-mono, Fira Mono, JetBrains Mono, monospace',
            fontSize: '1rem',
            lineHeight: 1.6,
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            overflowWrap: 'break-word',
            overflow: 'auto',
            pointerEvents: 'none',
            borderRadius: '0.75rem',
          }}
        />
        
        {/* 文本输入层 */}
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onPaste={handlePaste}
          onKeyDown={handleKeyDown}
          placeholder="在此输入 LaTeX 公式 ... 支持图片粘贴识别"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            padding: '1rem',
            backgroundColor: 'transparent',
            color: 'transparent',
            caretColor: textColor,
            fontFamily: 'geist-mono, Fira Mono, JetBrains Mono, monospace',
            fontSize: '1rem',
            lineHeight: 1.6,
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            overflowWrap: 'break-word',
            resize: 'vertical',
            outline: 'none',
            minHeight: '300px',
            borderRadius: '0.75rem',
            border: 'none',
          }}
          spellCheck={false}
        />
      </div>
    </div>
  );
}