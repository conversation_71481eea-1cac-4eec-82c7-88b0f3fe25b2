[{"primary_key": "4477341", "vector": [], "sparse_vector": [], "title": "Big Data, IoT, .... Buzz Words for Academia or Reality for Industry?", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The concepts of Big Data have became intertwined with those of the Internet of Things, creating mental pictures of a fully connected, all-encompassing, cyber-physical world, where each and every object will contribute with information to a \"fully aware\" society. Academic works are presenting this as the natural evolution for our current technologies. The panel looks at these promises from the hard perspective of reality: what is being done, how much it cost, what needs to be developed, and what can be expected in the near and mid-term.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2802150"}, {"primary_key": "4477342", "vector": [], "sparse_vector": [], "title": "Keystroke Recognition Using WiFi Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Keystroke privacy is critical for ensuring the security of computer systems and the privacy of human users as what being typed could be passwords or privacy sensitive information. In this paper, we show for the first time that WiFi signals can also be exploited to recognize keystrokes. The intuition is that while typing a certain key, the hands and fingers of a user move in a unique formation and direction and thus generate a unique pattern in the time-series of Channel State Information (CSI) values, which we call CSI-waveform for that key. In this paper, we propose a WiFi signal based keystroke recognition system called WiKey. WiKey consists of two Commercial Off-The-Shelf (COTS) WiFi devices, a sender (such as a router) and a receiver (such as a laptop). The sender continuously emits signals and the receiver continuously receives signals. When a human subject types on a keyboard, <PERSON><PERSON><PERSON><PERSON> recognizes the typed keys based on how the CSI values at the WiFi signal receiver end. We implemented the WiKey system using a TP-Link TL-WR1043ND WiFi router and a Lenovo X200 laptop. WiKey achieves more than 97.5\\% detection rate for detecting the keystroke and 96.4% recognition accuracy for classifying single keys. In real-world experiments, <PERSON><PERSON><PERSON><PERSON> can recognize keystrokes in a continuously typed sentence with an accuracy of 93.5%.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790109"}, {"primary_key": "4477348", "vector": [], "sparse_vector": [], "title": "Demo: OneLab: Major Computer Networking Testbeds for IoT and Wireless Experimentation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Gathering the required measurements to produce accurate results for mobile communications and wireless networking protocols, technologies and applications, relies on the use of expensive experimental computer networking facilities. Until very recently, large-scale testbed facilities have existed in separate silos, each with its own authentication mechanisms and experiment support tools. There lacked a viable federation model that reconciled the challenges posed by how to provide a single entry point to access heterogeneous and distributed resources, and how to federate these resources that are under the control of multiple authorities. The OneLab experimental facility, which came online in 2014, realizes this model, making a set of world-class testbeds freely available to researchers through a unique credential for each user and a common set of tools. We allow users to deploy innovative experiments across our federated platforms that include the embedded object testbeds of FIT IoT-Lab, the cognitive radio testbed of FIT CorteXlab, the wireless testbeds of NITOS-Lab, and the internet overlay testbed PlanetLab Europe (PLE), which together provide thousands of nodes for experimentation. Also federated under OneLab are the FUSECO Playground, which includes cloud, M2M, SDN, and mobile broadband; w-iLab.t wireless facilities; and the Virtual Wall testbed of wired networks and applications. Our demo describes the resources offered by the OneLab platforms, and illustrates how any member of the MobiCom community can create an account and start using these platforms today to deploy experiments for mobile and wireless testing.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789180"}, {"primary_key": "4477349", "vector": [], "sparse_vector": [], "title": "European Research towards 5G.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The next generation of wireless networks, the 'fifth generation\" or 5G, will have to cope with impressive new challenges. It includes a traffic expected to grow by up to 1000, an extremely low latency, the connection of cars, robots, smart cities, with billions of machines talking to each other and their sensors, new use of spectrum, new architectures and so on. The EU has committed €700 million of public funding over seven years to boost the research in 5G communications and a first wave of about 20 projects started this summer. The talk will address the scientific research challenges to develop 5G networks, the technology building blocks new projects are dealing with, notably as regards the Radio Access Network and the novel mobile architectures.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2802127"}, {"primary_key": "4477356", "vector": [], "sparse_vector": [], "title": "Rethinking Energy-Performance Trade-Off in Mobile Web Page Loading.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Insik Shin", "<PERSON>"], "summary": "Web browsing is a key application on mobile devices. However, mobile browsers are largely optimized for performance, imposing a significant burden on power-hungry mobile devices. In this work, we aim to reduce the energy consumed to load web pages on smartphones, preferably without increasing page load time and compromising user experience. To this end, we first study the internals of web page loading on smartphones and identify its energy-inefficient behaviors. Based on our findings, we then derive general design principles for energy-efficient web page loading, and apply these principles to the open-source Chromium browser and implement our techniques on commercial smartphones. Experimental results show that our techniques are able to achieve a 24.4% average system energy saving for Chromium on a latest-generation big.LITTLE smartphone using WiFi (a 22.5% saving when using 3G), while not increasing average page load time. We also show that our proposed techniques can bring a 10.5% system energy saving on average with a small 1.69\\% increase in page load time for mobile Firefox web browser. User study results indicate that such a small increase in page load time is hardly perceivable.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790103"}, {"primary_key": "4477358", "vector": [], "sparse_vector": [], "title": "Poster: Unified RemoteU¡ for Mobile Environments.", "authors": ["<PERSON>", "<PERSON> e <PERSON>"], "summary": "In our daily lives we assist to an exponential growth of mobile and fixed devices that surround us, though many of them having limited resources, and not even providing an interface screen. In this paper, we present remoteU¡, a middleware that allows the interaction of those devices with users, resorting to simple but expressive programming mechanisms, and providing efficient implementation and communication.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795170"}, {"primary_key": "4477360", "vector": [], "sparse_vector": [], "title": "Poster: 3D Printing Your Wireless Coverage.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "poster Share on Poster: 3D Printing Your Wireless Coverage Authors: <AUTHORS>", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795164"}, {"primary_key": "4477363", "vector": [], "sparse_vector": [], "title": "Smartphone Background Activities in the Wild: Origin, Energy Drain, and Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As new iterations of more powerful and better connected smartphones emerge, their limited battery life remains a leading factor adversely affecting the mobile experience of millions of smartphone users. While it is well-known that many apps can drain battery even while running in background, there has not been any study that quantifies the extent and severity of such background energy drain for users in the wild. To extend battery life, various new features are being incorporated within the phone, one of them being preventing applications from running in background, i.e., when the screen is off, but their impact is largely unknown. This paper makes several contributions. First, we present a large-scale measurement study that performs an in-depth analysis of the activities of various apps running in background on thousands of phones in the wild. Second, we quantify the amount of battery drain by all such background activities and possible energy saving. Third, we develop a metric to measure the usefulness of background activities that is personalized to each user. Finally, we present a system called HUSH (screen-off optimizer) that monitors the metric online and automatically identifies and suppresses background activities during screen-off periods that are not useful to the user experience. In doing so, our proposed HUSH saves screen-off energy of smartphones by 15.7% on average while incurring minimal impact on the user experience with the apps.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790107"}, {"primary_key": "4477364", "vector": [], "sparse_vector": [], "title": "AirExpress: Enabling Seamless In-band Wireless Multi-hop Transmission.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper describes the design and implementation of AirExpress, a system that enables in-band wireless cut-through transmission. Unlike wired cut-through, wireless cut-through can reduce latency and improve throughput performance of the network at the same time. In AirExpress, all the forwarders along the cut-through path forward the signal they received immediately without decoding. The hierarchical structure of AirExpress enables its interference cancellation ability to handle all kinds of interference among the radios. Novel MAC and routing algorithms based on cut-through transmission are also proposed to support the realization of AirExpress in multi-hop mesh networks.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790114"}, {"primary_key": "4477367", "vector": [], "sparse_vector": [], "title": "Poster: Evaluating Android Applications with Multipath TCP.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "poster Share on Poster: Evaluating Android Applications with Multipath TCP Authors: <AUTHORS>", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795165"}, {"primary_key": "4477369", "vector": [], "sparse_vector": [], "title": "QuickSync: Improving Synchronization Efficiency for Mobile Cloud Storage Services.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>wei Dai", "Congcong Miao"], "summary": "Mobile cloud storage services have gained phenomenal success in recent few years. In this paper, we identify, analyze and address the synchronization (sync) inefficiency problem of modern mobile cloud storage services. Our measurement results demonstrate that existing commercial sync services fail to make full use of available bandwidth, and generate a large amount of unnecessary sync traffic in certain circumstance even though the incremental sync is implemented. These issues are caused by the inherent limitations of the sync protocol and the distributed architecture. Based on our findings, we propose QuickSync, a system with three novel techniques to improve the sync efficiency for mobile cloud storage services, and build the system on two commercial sync services. Our experimental results using representative workloads show that QuickSync is able to reduce up to 52.9% sync time in our experiment settings.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790094"}, {"primary_key": "4477371", "vector": [], "sparse_vector": [], "title": "Demo: An Open-source Software Defined Platform for Collaborative and Energy-aware WiFi Offloading.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Sasu Tarkoma", "<PERSON><PERSON>", "<PERSON>"], "summary": "This demonstration presents a novel software defined platform for achieving collaborative and energy-aware WiFi offloading. The platform consists of an extensible central controller, programmable offloading agents, and offloading extensions on mobile devices. Driven by our extensive measurements of energy consumption on smartphones, we propose an effective energy-aware offloading algorithm and integrate it to our platform. By enabling collaboration between wireless networks and mobile users, our solution can make optimal offloading decisions that improve offloading efficiency for network operators and achieve energy saving for mobile users. To enhance deployability, we have released our platform under open-source licenses on GitHub.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789174"}, {"primary_key": "4477373", "vector": [], "sparse_vector": [], "title": "Poster: Privacy-Preserving Server-Driven Dynamic Spectrum Access System.", "authors": ["Yanzhi Dou", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic spectrum access (DSA) technique has been widely accepted as a crucial solution to mitigate the potential spectrum scarcity problem. As a key form of DSA, government is proposing to release more federal spectrum for sharing with commercial wireless users. However, the flourish of federal-commercial sharing hinges upon how privacy issues are managed. In current DSA proposals, the sensitive operation parameters of both federal incumbent users (IUs) and commercial secondary users (SUs) need to be shared with the dynamic spectrum access system (SAS) to realize efficient spectrum allocation. Since SAS is not necessarily operated by a trusted third party, the current proposals dissatisfy the privacy requirement of both IUs and SUs. To address the privacy issues, this paper presents a privacy-preserving SAS design, which realizes the complex spectrum allocation decision process of DSA through secure computation over ciphertext based on homomorphic encryption, thus none of the IU or SU operation parameters are exposed to SAS.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795161"}, {"primary_key": "4477375", "vector": [], "sparse_vector": [], "title": "CAreDroid: Adaptation Framework for Android Context-Aware Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Context-awareness is the ability of software systems to sense and adapt to their physical environment. Many contemporary mobile applications adapt to changing locations, connectivity states, available computational and energy resources, and proximity to other users and devices. Nevertheless, there is little systematic support for context-awareness in contemporary mobile operating systems. Because of this, application developers must build their own context-awareness adaptation engines, dealing directly with sensors and polluting application code with complex adaptation decisions. In this paper, we introduce CAreDroid, which is a framework that is designed to decouple the application logic from the complex adaptation decisions in Android context-aware applications. In this framework, developers are required- only-to focus on the application logic by providing a list of methods that are sensitive to certain contexts along with the permissible operating ranges under those contexts. At run time, CAreDroid monitors the context of the physical environment and intercepts calls to sensitive methods, activating only the blocks of code that best fit the current physical context. CAreDroid is implemented as part of the Android runtime system. By pushing context monitoring and adaptation into the runtime system, CAreDroid eases the development of context-aware applications and increases their efficiency. In particular, case study applications implemented using CAre-Droid are shown to have: (1) at least half lines of code fewer and (2) at least 10× more efficient in execution time compared to equivalent context-aware applications that use only standard Android APIs.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790108"}, {"primary_key": "4477377", "vector": [], "sparse_vector": [], "title": "Poster: TRIM: A Truthful Incentive Mechanism for Dynamic and Heterogeneous Tasks in Mobile Crowdsensing.", "authors": ["<PERSON><PERSON>", "Hailong Sun", "<PERSON><PERSON>"], "summary": "Stimulating user participation is of paramount importance for mobile crowdsensing applications to obtain high-quality data. Although many incentive mechanisms have been designed, most of them ignore the dynamic arrivals and different sensing requirements of tasks. Thus, the existing mechanisms will fail when being applied to the realistic scenario where tasks are publicized dynamically and heterogeneous with different sensing requirements of locations, time durations and sensing times. In this work, we propose an auction-based truthful mechanism for realistic mobile crowdsensing. Through extensive simulations, we demonstrate that our mechanism can satisfy the desired properties of truthfulness, individual rationality, computational efficiency with both low social cost and low total payment.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795179"}, {"primary_key": "4477378", "vector": [], "sparse_vector": [], "title": "Poster: Can Smart Devices Protect Us from Violent Crime?", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To explore the applicability of mobile smart devices to personal protection against violent crime, we propose a system that can detect the onset of hazardous situations involving violent crime by leveraging standard activity recognition strategies on smartphones and sensory inputs from wearable devices, as well as send help requests to alert the authorities.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795160"}, {"primary_key": "4477380", "vector": [], "sparse_vector": [], "title": "Poster: Use your Senses: A Smooth Multipath TCP WiFi/Mobile Handover.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The handover from WiFi to mobile networks is known to lead to TCP connection drops due to changing IP addresses. Multipath TCP (MPTCP), a recent TCP extension, enables a transparent mobile handover by combining subflows on multiple interfaces, such as WiFi and LTE, to one logical connection. MPTCP provides multiple handover modes, which differ in their energy consumption and the performance during the handover. The Full-MPTCP mode uses permanently both WiFi and the mobile network, which increases energy consumption. The Single-Path mode establishes the mobile network connection after the WiFi connection broke, which leads to a short performance degradation. In this paper, we argue that this trade-off is not necessary. We propose to use the available (sensor) information to forecast the mobile handover. This allows switching to the Full-MPTCP mode before the WiFi connection breaks, providing both low energy consumption and high performance during the handover. For a first experimental evaluation, we use a declining WiFi link quality to forecast a handover. Our real world measurements show that both low energy consumption and high performance during the handover are possible at the same time.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795171"}, {"primary_key": "4477382", "vector": [], "sparse_vector": [], "title": "Agility and Fragility in a Real-time World.", "authors": ["<PERSON>"], "summary": "No abstract available.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790090"}, {"primary_key": "4477383", "vector": [], "sparse_vector": [], "title": "Poster: ParkMaster: Leveraging Edge Computing in Visual Analytics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work we propose ParkMaster, a low-cost crowdsourcing architecture which exploits machine learning techniques and vision algorithms to evaluate parking availability in cities. While the user is normally driving ParkMaster enables off the shelf smartphones to collect information about the presence of parked vehicles by running image recognition techniques on the phones camera video streaming. The paper describes the design of ParkMaster's architecture and shows the feasibility of deploying such mobile sensor system in nowadays smartphones, in particular focusing on the practicability of running vision algorithms on phones.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795174"}, {"primary_key": "4477384", "vector": [], "sparse_vector": [], "title": "Demo: Wireless Link Selection on Smartphone: Throughput vs Battery Drain.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The method for automatic selection of wireless interface on smart phones is presented. We consider the selection between WiFi and LTE / 3G radios to maximize the throughput of the data transmission and minimize the amount of energy used to scan and measure the throughput of the network. The proposed selection algorithm defines when to measure and how to estimate the network throughput to balance between the accuracy of the selection and the battery drain caused by the measurements. The link speed estimation method optimized for LTE and WiFi networks is also demonstrated", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789177"}, {"primary_key": "4477387", "vector": [], "sparse_vector": [], "title": "Demo: FIT IoT-LABA: Large Scale Open Experimental IoT Testbed.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "FIT IoT-LAB's goal is to provide a very large scale open experimental testbed for the Internet of Things, by deploying more than 2700 experimentation nodes over 6 sites in France. Our demonstration purpose is to illustrate what the IoT-LAB platform offers through small applications involving radio communications and mobile nodes. Thanks to these examples, we will show how to run an experiment in the testbed and some of the tools it provides to help in developing, tuning and monitoring such large-scale applications.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789172"}, {"primary_key": "4477388", "vector": [], "sparse_vector": [], "title": "Optimizing Smartphone Power Consumption through Dynamic Resolution Scaling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhou"], "summary": "The extremely-high display density of modern smartphones imposes a significant burden on power consumption, yet does not always provide an improved user experience and may even lead to a compromised user experience. As human visually-perceivable ability highly depends on the user-screen distance, a reduced display resolution may still achieve the same user experience when the user-screen distance is large. This provides new power-saving opportunities. In this paper, we present a flexible dynamic resolution scaling system for smartphones. The system adopts an ultrasonic-based approach to accurately detect the user-screen distance at low-power cost and makes scaling decisions automatically for maximum user experience and power saving. App developers or users can also adjust the resolution manually as their needs. Our system is able to work on existing commercial smartphones and support legacy apps, without requiring re-building the ROM or any changes of apps. An end-to-end dynamic resolution scaling system is implemented on the Galaxy S5 LTE-A and Nexus 6 smartphones, and the correctness and effectiveness are evaluated against 30 games and benchmarks. Experimental results show that all the 30 apps can run successfully with per-frame, real-time dynamic resolution scaling. The energy per frame can be reduced by 30.1% on average and up to 60.5\\% at most when the resolution is halved, for 15 apps. A user study with 10 users indicates that our system remains good user experience, as none of the 10 users could perceive the resolution changes in the user study.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790117"}, {"primary_key": "4477389", "vector": [], "sparse_vector": [], "title": "Demo: Optimizing Smartphone Power Consumption through Dynamic Resolution Scaling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhou"], "summary": "The extremely-high display density of modern smartphones imposes a significant burden on power consumption, yet does not always provide an improved user experience and may even lead to a compromised user experience. As human visually-perceivable ability highly depends on the user-screen distance, a reduced display resolution may still achieve the same user experience when the user-screen distance is large. This provides new power-saving opportunities. We present a flexible dynamic resolution scaling system for smartphones. The system adopts an ultrasonic-based approach to detect the user-screen distance at low-power cost and makes scaling decisions automatically for maximum user experience and power saving. App developers or users can also adjust the resolution manually and dynamically as their needs. Our system is able to work on the existing commercial smartphones and support the legacy apps, without requiring re-building the ROM or any changes from apps.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789175"}, {"primary_key": "4477390", "vector": [], "sparse_vector": [], "title": "Poster: Network-Based UE Mobility Estimation in Mobile Networks.", "authors": ["Dalia-<PERSON><PERSON>", "<PERSON><PERSON>", "Véronique <PERSON>", "<PERSON>"], "summary": "The co-existence of small cells and macro cells is a key feature of 4G and future networks. This heterogeneity, together with the increased mobility of user devices can generate a high handover frequency that could lead to unreasonably high call drop probability or poor user experience. By performing smart mobility management, the network can pro-actively adapt to the user and guarantee seamless and smooth cell transitions. In this work, we introduce an algorithm that takes as input sounding reference signal (SRS) measurements available at the base station (eNodeB in 4G systems) to estimate with a low computational requirement the mobility level of the user and with no modification at the user device/equipment (UE) side. The performance of the algorithm is showcased using realistic data and mobility traces. Results show that the classification of UE speed to three mobility classes can be achieved with accuracy of 87% for low mobility, 93% for medium mobility, and 94% for high mobility, respectively.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795166"}, {"primary_key": "4477393", "vector": [], "sparse_vector": [], "title": "Poster: VPN Tunnels for Energy Efficient Multimedia Streaming.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Sasu Tarkoma"], "summary": "Minimizing the energy consumption of mobile devices for wireless network access is important. In this article, we analyze the energy efficiency of a new set of applications which use Virtual Private Network (VPN) tunnels for secure communication. First, we discuss the energy efficiency of a number of VPN applications from a large scale deployment of 500 K devices. We next measure the energy consumption of some of these applications with different use cases. Finally, we demonstrate that a VPN tunnel can be instrumented for enhanced energy efficiency with multimedia streaming applications. Our results indicate energy savings of 40% for this class of applications.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795168"}, {"primary_key": "4477394", "vector": [], "sparse_vector": [], "title": "Poster: Extremely Parallel Resource Pre-Fetching for Energy Optimized Mobile Web Browsing.", "authors": ["<PERSON>", "Sasu Tarkoma", "<PERSON><PERSON><PERSON>"], "summary": "Mobile web browsing is experienced slow because of the limited rendering capability of the mobile devices, wireless latency, and incremental rendering of the page or resource loading. The browser renders resources in between two consecutive resource downloads. However, during this period, the wireless interfaces consume energy doing nothing useful. In this work, we measure the performance of SPDY for mobile web browsing. We demonstrate that mobile devices waste energy by keeping the wireless network interface idle between consecutive resource downloads. We next show that by identifying the embedded resources in a web page and downloading those resources in parallel at the very beginning can reduce the small idle periods and thus energy consumption by 20-50%, depending on the wireless network type.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795167"}, {"primary_key": "4477397", "vector": [], "sparse_vector": [], "title": "Poster: User Location Fingerprinting at Scale.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many emerging mobile computing applications are continuous vision based. The primary challenge these applications face is computation partitioning between the phone and cloud. The indoor location information is one metadata that can help these applications in making this decision. In this extended-abstract, we propose a vision based scheme to uniquely fingerprint an environment which can in turn be used to identify user's location from the uploaded visual features. Our approach takes into account that the opportunity to identify location is fleeting and the phones are resource constrained -- therefore minimal yet sufficient computation needs to be performed to make the offloading decision. Our work aims to achieve near real-time performance while scaling to buildings of arbitrary sizes. The current work is in preliminary stages but holds promise for the future -- may apply to many applications in this area.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795175"}, {"primary_key": "4477398", "vector": [], "sparse_vector": [], "title": "Performance Characterization and Call Reliability Diagnosis Support for Voice over LTE.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "To understand VoLTE performance in a commercial deployment, in this paper we conduct the first comprehensive performance characterization of commercially deployed VoLTE, and compare with legacy call and over-the-top (OTT) VoIP call. We confirm that VoLTE excels in most metrics such as audio quality, but its call reliability still lags behind legacy call for all the three major U.S. operators. We propose an on-device VoLTE problem detection tool, which can capture new types of problems concerning audio quality with high accuracy and minimum overhead, and perform stress testing on VoLTE call's reliability. We discover 3 instances of problems in the early deployment of VoLTE lying in the protocol design and implementation. Although the identified problems are all concerned with the immature LTE coverage in the current deployment, we find that they can cause serious impairment on user experience and are urgent to be solved in the developing stage. For example, one such instance can lead to up to 50-second-long muting problem during a VoLTE call! We perform in-depth cross-layer analysis and find that the causes are rooted in the lack of coordination among protocols designed for different purposes, and invalid assumptions made by protocols used in existing infrastructure when integrated with VoLTE. We summarize learnt lessons and suggest solutions.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790095"}, {"primary_key": "4477400", "vector": [], "sparse_vector": [], "title": "Poster: An Insomnia Therapy for Clock Synchronization in Wireless Sensor Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Intermittent connection of wireless links, caused by low duty-cycle radio operation, harsh working environment, movement of sensor nodes, etc., makes clock synchronization a challenging task. Prior synchronization approaches in wireless sensor networks (WSNs) typically require that nodes exchange time messages frequently with the reference clock, which is difficult in networks with low or intermittent connectivity. This poster presents RobSync, a robust design for clock synchronization in intermittent-connected wireless networks. Having recognized that clock skew is highly correlated to the voltage supply, we use the local voltage information as a reference for clock self-calibration, which helps reduce the frequency of time-stamp exchanges. To prevent a misuse of the voltage information, leading to error accumulation, a re-synchronization interval adjustment design is developed to make a trade-off between accuracy and energy consumption. We present the theory behind RobSync, and provide preliminary results by experiments to compare our approach and the recent approach.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795184"}, {"primary_key": "4477401", "vector": [], "sparse_vector": [], "title": "EcoDrive: A Mobile Sensing and Control System for Fuel Efficient Driving.", "authors": ["<PERSON><PERSON>", "Bozhao Qi", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces EcoDrive, a fuel consumption sensing and control system for modern vehicles, implemented in an embedded platform, to improve fuel efficiency and reduce carbon emissions. EcoDrive senses vehicle dynamics through the standard vehicle On-board diagnostics (OBD) port and models various vehicle forces, i.e., propulsion, drivetrain loss, wind resistance and grade resistance, as functions of instant fuel consumption. By sensing vehicular speed and controlling air/fuel injection rate in real time, EcoDrive can adjust speed carefully to improve fuel efficiency. We have collected more than 10,000 miles of driving traces from 12 different vehicles to build models of vehicle dynamics. Based on the models, a prototype of EcoDrive is implemented in an off-the-shelf embedded platform. The prototype is installed on a regular vehicle and evaluated through test drives of more than 100 miles across both urban and highway environments. In comparison with human drivers, EcoDrive achieves an average of 20% higher fuel efficiency in urban road segments and 30% higher fuel efficiency on highways.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790111"}, {"primary_key": "4477404", "vector": [], "sparse_vector": [], "title": "FreeBee: Cross-technology Communication via Free Side-channel.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents FreeBee, which enables direct unicast as well as cross-technology/channel broadcast among three popular wireless technologies: WiFi, ZigBee, and Bluetooth. Our design aims to shed the light on the opportunities that cross-technology communication has to offer including, but not limited to, cross-technology cooperation and coordination. The key concept of FreeBee is to modulate symbol messages by shifting the timing of periodic beacon frames already mandatory for wireless standards without incurring extra traffic. Such a generic cross-technology design consumes zero additional bandwidth, allowing continuous broadcast to safely reach mobile and/or duty-cycled devices. A new \\emph{interval multiplexing} technique is proposed to enable concurrent broadcasts from multiple senders or boost the transmission rate of a single sender. Theoretical and experimental exploration reveals that FreeBee offers a reliable symbol delivery under a second and supports mobility of 30mph and low duty-cycle operations of under 5%.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790098"}, {"primary_key": "4477405", "vector": [], "sparse_vector": [], "title": "mZig: Enabling Multi-Packet Reception in ZigBee.", "authors": ["Linghe Kong", "<PERSON><PERSON>"], "summary": "This paper presents mZig, a novel physical layer design that enables a receiver to simultaneously decode multiple packets from different transmitters in ZigBee. As a low-power and low-cost wireless protocol, the promising ZigBee has been widely used in sensor networks, cyber-physical systems, and smart buildings. Since ZigBee based networks usually adopt tree or cluster topology, the convergecast scenarios are common in which multiple transmitters need to send packets to one receiver. For example, in a smart home, all appliances report data to one control plane via ZigBee. However, concurrent transmissions in convergecast lead to the severe collision problem. The conventional ZigBee avoids collisions using backoff time, which introduces additional time overhead. Advanced methods resolve collisions instead of avoidance, in which the state-of-the-art ZigZag resolves one m-packet collision requiring m retransmissions. We propose mZig to resolve one m-packet collision by this collision itself, so the theoretical throughput is improved m-fold. Leveraging the unique features in ZigBee's physical layer including its chip rate, half-sine pulse shaping and O-QPSK modulation, mZig subtly decomposes multiple packets from one collision in baseband signal processing. The practical factors of noise, multipath, and frequency offset are taken into account in mZig design. We implement mZig on USRPs and establish a seven-node testbed. Experiment results demonstrate that mZig can receive up to four concurrent packets in our testbed. The throughput of mZig is 4.5x of the conventional ZigBee and 3.2x of ZigZag in the convergecast with four or more transmitters.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790104"}, {"primary_key": "4477410", "vector": [], "sparse_vector": [], "title": "Research Challenges and Opportunities in a Mobility-centric World.", "authors": ["<PERSON>"], "summary": "The Internet recently passed an historic inflection point, with the number of broadband mobile devices surpassing the number of wired PCs and servers connected to the Internet. Mobility now profoundly affects the architecture, services and applications in both the wireless and wired domains. In this \"bottom up\" talk, we begin by discussing several specific mobility-related challenges and recent results in areas including mobility measurement (including privacy considerations) and modeling, and context-sensitive services. We then take a broader look at current and future challenges, and conclude by discussing several NSF investments in programs and projects in area of mobile networking.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790089"}, {"primary_key": "4477411", "vector": [], "sparse_vector": [], "title": "Demo: Molecular MIMO with Drift.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In molecular communication information is transferred with the use of molecules. Molecular multiple-input multiple- output (MIMO) system with drift (positive velocity) at macro- scale will be presented and the improvement against single- input single-output (SISO) molecular communication systems will be verified via our testbed. Until now it was unclear whether MIMO techniques, which are extensively used in modern radio frequency (RF) communications, could be applied to molecular communication. In the demonstration, using our MIMO testbed we will show that we can achieve nearly 1.7 times higher data rate than SISO molecular communication systems. Moreover, signal-to-inter-link-interfeence metric for one-shot signal will be depicted for a given symbol duration.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789181"}, {"primary_key": "4477412", "vector": [], "sparse_vector": [], "title": "Human Sensing Using Visible Light Communication.", "authors": ["T<PERSON>xing Li", "Chuankai An", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present LiSense, the first-of-its-kind system that enables both data communication and fine-grained, real-time human skeleton reconstruction using Visible Light Communication (VLC). LiSense uses shadows created by the human body from blocked light and reconstructs 3D human skeleton postures in real time. We overcome two key challenges to realize shadow-based human sensing. First, multiple lights on the ceiling lead to diminished and complex shadow patterns on the floor. We design light beacons enabled by VLC to separate light rays from different light sources and recover the shadow pattern cast by each individual light. Second, we design an efficient inference algorithm to reconstruct user postures using 2D shadow information with a limited resolution collected by photodiodes embedded in the floor. We build a 3 m x 3 m LiSense testbed using off-the-shelf LEDs and photodiodes. Experiments show that LiSense reconstructs the 3D user skeleton at 60 Hz in real time with 10 degrees mean angular error for five body joints.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790110"}, {"primary_key": "4477413", "vector": [], "sparse_vector": [], "title": "Recitation: Rehearsing Wireless Packet Reception in Software.", "authors": ["Zhenjiang Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents Recitation, the first software system that uses lightweight channel state information (CSI) to accurately predict error-prone bit positions in a packet so that applications atop the wireless physical layer may take the best action during subsequent transmissions. Our key insight is that although Wi-Fi wireless physical layer operations are complex, they are deterministic. This enables us to rehearse physical-layer operations on packet bits before they are transmitted. Based on this rehearsal, we calculate a hidden parameter in the decoding process, called error event probability (EVP). EVP captures fine-grained information about the receiver's convolutional or LDPC decoder, allowing Recitation to derive precise information about the likely fate of every bit in subsequent packets, without any wireless channel training. Recitation is the first system of its kind that is both software-implementable and compatible with the existing 802.11 architecture for both SISO and MIMO settings. We experiment with commodity Atheros 9580 Wi-Fi NICs to demonstrate Recitation's utility with three representative applications in static, mobile, and interference-dominated scenarios. We show that Recitation achieves 33.8% and 16% average throughput gains for bit-rate adaptation and partial packet recovery, respectively, and 6 dB PSNR quality improvement for unequal error protection-based video.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790126"}, {"primary_key": "4477416", "vector": [], "sparse_vector": [], "title": "Poster: On the Low-Cost and Distance-Adaptive Device-free Localization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Hongbo Jiang", "Xiao<PERSON> Chen", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This poster introduces JRD, a novel device-free localization system which can achieve high accuracy with low cost and little human effort, and is even robust to different scenarios. Unlike the previous Radio Signal Strength (RSS)-based systems which depend on the dense deployment to provide high accuracy, JRD extracts the fine-grained RSS distributions of a single link and presents a voting algorithm based on multi-link to identify the object location accurately while maintaining a low-cost deployment. Furthermore, JRD is flexible to different scenarios by using the transferring technique with less time-consuming and human effort. Experimental results show that JRD can improve the localization accuracy by up to 50% with less cost as compared with the existing RSS approaches.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795162"}, {"primary_key": "4477417", "vector": [], "sparse_vector": [], "title": "Snooping Keystrokes with mm-level Audio Ranging on a Single Phone.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper explores the limits of audio ranging on mobile devices in the context of a keystroke snooping scenario. Acoustic keystroke snooping is challenging because it requires distinguishing and labeling sounds generated by tens of keys in very close proximity. Existing work on acoustic keystroke recognition relies on training with labeled data, linguistic context, or multiple phones placed around a keyboard --- requirements that limit usefulness in an adversarial context. In this work, we show that mobile audio hardware advances can be exploited to discriminate mm-level position differences and that this makes it feasible to locate the origin of keystrokes from only a single phone behind the keyboard. The technique clusters keystrokes using time-difference of arrival measurements as well as acoustic features to identify multiple strokes of the same key. It then computes the origin of these sounds precise enough to identify and label each key. By locating keystrokes this technique avoids the need for labeled training data or linguistic context. Experiments with three types of keyboards and off-the-shelf smartphones demonstrate scenarios where our system can recover $94\\%$ of keystrokes, which to our knowledge, is the first single-device technique that enables acoustic snooping of passwords.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790122"}, {"primary_key": "4477419", "vector": [], "sparse_vector": [], "title": "Poster: Crowdsourced Location Aware Wi-Fi Access Control.", "authors": ["Bingxian Lu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, Wi-Fi has seen extraordinary growth; however, due to the cost, performance and security issues, many Wi-Fi hotspot owners would like to restrict the network access only to individuals inside the physical property. Unfortunately, due to the nature of wireless, this is difficult to accomplish, especially with the off-the-shelf omni-antenna devices. In this work, we develop and implement CLaWa, a Crowdsourced Location Aware Wi-Fi Access Control scheme to address this challenge. Our system is based on observations of differing characteristics of physical layer information across physical boundaries such as walls and corners. CLaWa crowdsources both channel state information (CSI) and received signal strength (RSS) of already validated users to classify future users. We have also selected an appropriate machine learning algorithm for CLaWa. Evaluation results show that CLaWa can identify the boundary around a given area precisely, thus granting network access only to users inside the area while not validating users outside the boundary. Compared to indoor localization schemes, CLaWa is a lightweight solution which does not require expensive localization operations.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795183"}, {"primary_key": "4477421", "vector": [], "sparse_vector": [], "title": "Demo: <PERSON><PERSON><PERSON>, AP-centric Health Diagnosis of WiFiNetworks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Witals, a system for WiFi performance diagnosis. In Witals, a live access point diagnoses the health of the WiFi network by examining the health of the air around it. Such diagnosis is critical functionality for sysads, and is an important addition to the state of the art. In the demo we will show WiFi performance diagnosis by <PERSON><PERSON><PERSON> live at MobiCom venue and also from past collected traces in other settings & for controlled experiments.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789179"}, {"primary_key": "4477423", "vector": [], "sparse_vector": [], "title": "Demo: Exploring Autoregressive Integrated Models for Time Synchronization in Wireless Sensor Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Time synchronization provides the basis for several applications in wireless sensor networks but the limited memory and computational power, and the use of low precision oscillators make the task of time synchronization non-trivial. In this demonstration, we present a novel time synchronization scheme that is based on time series analysis. To provide a general model for the practical behavior of low precision oscillators, autoregressive integrated moving average models are explored. Based on the analysis of experimental data, an autoregressive integrated model (ARI (1,1)) is derived. Unlike the resource hungry Kalman filter based formulations, the proposed scheme is resource efficient as it results in simple linear regression processing. Experiments are performed on real sensor devices including Zolertia and TelosB, where an accuracy below 1 clock tick 1 is achieved.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789182"}, {"primary_key": "4477425", "vector": [], "sparse_vector": [], "title": "CIDER: Enabling Robustness-Power Tradeoffs on a Computational Eyeglass.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Pan Hu", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The human eye offers a fascinating window into an individual's health, cognitive attention, and decision making, but we lack the ability to continually measure these parameters in the natural environment. The challenges lie in: a) handling the complexity of continuous high-rate sensing from a camera and processing the image stream to estimate eye parameters, and b) dealing with the wide variability in illumination conditions in the natural environment. This paper explores the power--robustness tradeoffs inherent in the design of a wearable eye tracker, and proposes a novel staged architecture that enables graceful adaptation across the spectrum of real-world illumination. We propose CIDER, a system that operates in a highly optimized low-power mode under indoor settings by using a fast Search-Refine controller to track the eye, but detects when the environment switches to more challenging outdoor sunlight and switches models to operate robustly under this condition. Our design is holistic and tackles a) power consumption in digitizing pixels, estimating pupillary parameters, and illuminating the eye via near-infrared, b) error in estimating pupil center and pupil dilation, and c) model training procedures that involve zero effort from a user. We demonstrate that CIDER can estimate pupil center with error less than two pixels (0.6O), and pupil diameter with error of one pixel (0.22mm). Our end-to-end results show that we can operate at power levels of roughly 7mW at a 4Hz eye tracking rate, or roughly 32mW at rates upwards of 250Hz.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790096"}, {"primary_key": "4477427", "vector": [], "sparse_vector": [], "title": "Demo: Modular Multi-radio Wireless Sensor Platform for IoT Trials with Plug&amp;Play Module Connection.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the paper we present and demonstrate the modular prototyping platform designed for trialing the Internet of Things (IoT) applications. The new devices are constructed by stacking together the various hardware modules encapsulating power sources, processing units, wired and wireless transceivers, sensors and actuators, or sets of those. The main processing unit automatically identifies all the attached modules and adjusts own operation accordingly. The demo will showcase how the platform can be used for building up multi-radio technology enabled wireless devices which will automatically form a heterogeneous wireless sensor and actuator network (WSAN). The possible use case scenarios and the ongoing research activities around the platform will be highlighted as well.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789176"}, {"primary_key": "4477428", "vector": [], "sparse_vector": [], "title": "Poster: Regression-based Characterization of 802.11ac Indoor Performance.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We investigate the applicability of IEEE 802.11ac for entertainment low-latency show control systems (to orchestrate free-riding vehicles in a theme-park environment) by experimentally characterizing the indoor throughput and jitter performance of 802.11ac using statistical analysis. 802.11ac is the successor of 802.11n that provides higher throughput by incorporating wider channels, more spatial streams and denser modulation. We show that multiple linear regression provides valuable insight in the influence of 802.11ac's independent features and their combinations on performance, for various links and interference scenarios. Finally, we show that 802.11ac could be used for not only delivering high throughput for multimedia streaming but also supports applications requiring minimal jitter variance in the setup investigated.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795173"}, {"primary_key": "4477431", "vector": [], "sparse_vector": [], "title": "ABSENCE: Usage-based Failure Detection in Mobile Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present our proposed ABSENCE system which detects service disruptions in mobile networks using aggregated customer usage data. ABSENCE monitors aggregated customer usage to detect when aggregated usage is lower than expected in a given geographic region (e.g., zip code), across a given customer device type, or for a given service. Such a drop in expected usage is interpreted as a sign of a potential service disruption being experienced in that region/device type/service. ABSENCE effectively deals with users' mobility and scales to detect failures in various mobile services (e.g., voice, data, SMS, MMS, etc). We perform a systematic evaluation of our proposed approach by introducing synthetic failures in measurements obtained from a US operator. We also compare our results with ground truth (real service disruptions) obtained from the mobile operator.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790127"}, {"primary_key": "4477432", "vector": [], "sparse_vector": [], "title": "Poster: Continuous and Fine-grained Respiration Volume Monitoring Using Continuous Wave Radar.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "An unobtrusive and continuous estimation of breathing volume could play a vital role in health care, such as for critically ill patients, neonatal ventilation, post-operative monitoring, just to name a few. While radar-based estimation of breathing rate has been discussed in the literature, estimating breathing volume using wireless signal remains relatively intact. With the presence of patient body movement and posture changes, long-term monitoring of breathing volume at fine granularity is even more challenging. In this work, we propose for the first time an autonomous system that monitors a patient's breathing volume with high resolution. We discuss the key research components and challenges in realizing the system. We also present an initial system design encompassing a continuous wave radar, motion tracking and control system, and a set of methods to accurately derive breathing volume from the reflected signal and to address challenges caused by body movement and posture changes. Our implementation shows promising results in estimating breathing volume with fine granularity.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795177"}, {"primary_key": "4477434", "vector": [], "sparse_vector": [], "title": "Demo: Closer to Cloud-RAN: RAN as a Service.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Commoditization and virtualization of wireless networks are changing the economics of mobile networks to help network providers (e.g., MNO, MVNO) move from proprietary and bespoke hardware and software platforms toward an open, cost-effective, and flexible cellular ecosystem. In addition, rich and innovative local services can be efficiently created through cloudification by leveraging the existing infrastructure. In this work, we present RANaaS, which is a cloudified radio access network delivered as a service. RANaaS provides the service life-cycle of an on-demand, elastic, and pay as you go 3GPP RAN instantiated on top of the cloud infrastructure. We demonstrate an example of real-time cloudified LTE network deployment using the OpenAirInterface LTE implementation and OpenStack running on commodity hardware as well as the flexibility and performance of the platform developed.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789178"}, {"primary_key": "4477438", "vector": [], "sparse_vector": [], "title": "Come and Be Served: Para<PERSON><PERSON> Decoding for COTS RFID Tags.", "authors": ["Jiajue Ou", "<PERSON>", "Yuan<PERSON> Zheng"], "summary": "Current commodity RFID systems incur high communication overhead due to severe tag-to-tag collisions. Although some recent works have been proposed to support parallel decoding for concurrent tag transmissions, they require accurate channel measurements, tight tag synchronization, or modifications to standard RFID tag operations. In this paper, we present BiGroup, a novel RFID communication paradigm that allows the reader to decode the collision from multiple COTS (commodity-off-the-shelf) RFID tags in one communication round. In BiGroup, COTS tags can directly join ongoing communication sessions and get decoded in parallel. The collision resolution intelligence is solely put at the reader side. To this end, BiGroup examines the tag collisions at RFID physical layer from constellation domain as well as time domain, exploits the under-utilized channel capacity due to low tag transmission rate, and leverages tag diversities. We implement BiGroup with USRP N210 software radio that is able to read and decode multiple concurrent transmissions from COTS passive tags. Our experimental study gives encouraging results that BiGroup greatly improves RFID communication efficiency, i.e., 11× performance improvement compared to the alternative decoding scheme for COTS tags and 6× gain in time efficiency when applied to EPC C1G2 tag identification.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790101"}, {"primary_key": "4477441", "vector": [], "sparse_vector": [], "title": "Demo: Abstract: Live Adaptations of Low-power MAC Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This demonstration aims at observing in an interactive manner the impact of modification of preamble and sampling periods at the low-power family of MAC protocols, and thus, illustrating in real-time the energy consumption and delay performance of each node accordingly. To do so, we implemented the ability for users to generate traffic at some remote nodes that are involved in two distinct deployed topologies. Those deployed networks operate with either a statically configured network, by employing X-MAC on top of the Contiki OS, or T-AAD, a lightweight traffic auto-adaptive protocol that allows live and automatic modifications of duty-cycle configurations.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789184"}, {"primary_key": "4477444", "vector": [], "sparse_vector": [], "title": "Demo: Car-Fi: Opportunistic V2I by Exploiting Dual-Access Wi-Fi Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The need for Internet access from moving vehicles has been steadily increasing in the past few years. Solutions that rely on cellular connectivity are becoming impractical to deploy due to technical and economic reasons. Car-Fi proposes an approach that leverages existing home Wi-Fi access points configured in dual-access mode, in order to offload all data traffic from the congested and expensive cellular infrastructure to whatever Wi-Fi network is available. Thanks to an improved scanning algorithm and numerous optimizations to the connection setup, Car-Fi makes downloading large amounts of data from a moving car feasible.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789171"}, {"primary_key": "4477446", "vector": [], "sparse_vector": [], "title": "Poster: Mobile Data Offloading Testbed.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent research has proposed swarming protocols as a possible approach to offload the Internet infrastructure when some content can be shared by several users. However, simulations have been generally used as experimental means. Instead, we present an application platform that allows a rapid development and testing of swarming protocols using off-the-shelf smartphones.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795159"}, {"primary_key": "4477453", "vector": [], "sparse_vector": [], "title": "Demo: Platform for Collecting Data From Urban Sensors Using Vehicular Networking.", "authors": ["<PERSON>", "Tânia <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A large-scale urban sensing platform, composed of multiple Data Collection Units (DCUs) equipped with sensor hardware scattered accross the city, allows pervasive monitoring of environmental parameters. Gathering sensor data from a number of disparate locations at a backend server can be supported by delay-tolerant services provided by existing vehicular networks. Our real-world sensing platform takes advantage of an existing vehicular network with more than 400 vehicles equipped with On-Board Units (OBUs). A purposely-developed implementation of a delay tolerant service is installed in all elements involved in the communication flow, from DCUs to the backend server. In this demo, we showcase the full end-to-end data flow with the actual equipment being used in our real-world deployment. Data produced at a DCU is collected by an OBU installed in a vehicle and delivered to a Road-Side Unit (RSU), which then forwards the data to the backend server.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789169"}, {"primary_key": "4477456", "vector": [], "sparse_vector": [], "title": "Poster: NLOS-aware Localization Based on Phase Shift Measurements.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern cars are already able to park in parking spaces adjacent to streets. Soon autonomous cars will be able to navigate themselves through parking garages to find a parking lot assigned to them. Indoor localization is essential to qualify cars and parking garages to perform this operation. Currently the reliability of many indoor localization schemes suffers from non-line-of-sight propagation paths. We present a position estimation algorithm for indoor localization systems based on phase measurements of electromagnetic signals. Our algorithm is designed to detect and exclude measurements originating from these non-line-of-sight paths to reduce their harmful influence on the localization.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795163"}, {"primary_key": "4477458", "vector": [], "sparse_vector": [], "title": "Poster: Towards Encrypted Query Processing for the Internet of Things.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Internet of Things (IoT) is envisioned to digitize the physical world, resulting in a digital representation of our proximate living space. The possibility of inferring privacy violating information from IoT data necessitates adequate security measures regarding data storage and communication. To address these privacy and security concerns, we introduce our system that stores IoT data securely in the Cloud database while still allowing query processing over the encrypted data. We enable this by encrypting IoT data with a set of cryptographic schemes such as order-preserving and partially homomorphic encryptions. To achieve this on resource-limited devices, our system relies on optimized algorithms that accelerate partial homomorphic and order-preserving encryptions by 1 to 2 orders of magnitude. Our early results show the feasibility of our system on low-power devices. We envision our system as an enabler of secure IoT applications.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795172"}, {"primary_key": "4477459", "vector": [], "sparse_vector": [], "title": "Control Channel Design for Many-Antenna MU-MIMO.", "authors": ["<PERSON>", "<PERSON><PERSON>d", "<PERSON>"], "summary": "Many-antenna MU-MIMO faces a critical, previously unaddressed challenge: it lacks a practical control channel. At the heart of this challenge is that the potential range of MU-MIMO beamforming systems scales with up to the square of the number of base-station antennas once they have channel state information (CSI), whereas the range of traditional control channel operations remains constant since they take place before or during CSI acquisition. This range gap between no-CSI and CSI modes presents a critical challenge to the efficiency and feasibility of many-antenna base stations, as their operational range is limited to the no-CSI mode. We present a novel control channel design for many-antenna MU-MIMO, Faros, that allows the number of base-station antennas to scale up to 100s in practice. Faros leverages a combination of open-loop beamforming and coding gains to bridge the range gap between the CSI and no-CSI modes. Not only does Faros provide an elegant and efficient control channel for many-antenna MU-MIMO, but on a more fundamental level it exposes flexible, fine-grained, control over space, time, and code resources, which enables previously impossible optimizations. We implement our design on the Argos many-antenna base station and evaluate its performance in bridging the range gap, synchronization, and paging. With 108 antennas, Faros can provide over 40 dB of gain, which enables it to function reliably at over 250 meters outdoors with less than 100 μW of transmit power per antenna, 10 mW total, at 2.4 GHz.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790120"}, {"primary_key": "4477461", "vector": [], "sparse_vector": [], "title": "Wireless Power Hotspot that Charges All of Your Devices.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Each year, consumers carry an increasing number of gadgets on their person: mobile phones, tablets, smartwatches, etc. As a result, users must remember to recharge each device, every day. Wireless charging promises to free users from this burden, allowing devices to remain permanently unplugged. Today's wireless charging, however, is either limited to a single device, or is highly cumbersome, requiring the user to remove all of her wearable and handheld gadgets and place them on a charging pad. This paper introduces MultiSpot, a new wireless charging technology that can charge multiple devices, even as the user is wearing them or carrying them in her pocket. A MultiSpot charger acts as an access point for wireless power. When a user enters the vicinity of the MultiSpot charger, all of her gadgets start to charge automatically. We have prototyped MultiSpot and evaluated it using off-the-shelf mobile phones, smartwatches, and tablets. Our results show that MultiSpot can charge 6 devices at distances of up to 50cm.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790092"}, {"primary_key": "4477463", "vector": [], "sparse_vector": [], "title": "Last-Mile Navigation Using Smartphones.", "authors": ["Yuanchao Shu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Although GPS has become a standard component of smartphones, providing accurate navigation during the last portion of a trip remains an important but unsolved problem. Despite extensive research on localization, the limited resolution of a map imposes restrictions on the navigation engine in both indoor and outdoor environments. To bridge the gap between the end position obtained from legacy navigation services and the real destination, we propose FollowMe, a \"last-mile\" navigation system to enable plug-and-play navigation in indoor and semi-outdoor environments. FollowMe exploits the ubiquitous, stable geomagnetic field and natural walking patterns to navigate the users to the same destination taken by an earlier traveler. Unlike existing localization and navigation systems, FollowMe is infrastructure-free, energy-efficient and cost-saving. We implemented FollowMe on smartphones, and evaluated it in a four-story campus building with a testing area of 2000m2. Our experimental results with 5 users show that 95% of spatial errors during navigation were 2m or less with at least 50% energy savings over a benchmark system.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790099"}, {"primary_key": "4477464", "vector": [], "sparse_vector": [], "title": "Demo: AntMonitor: A System for Mobile Traffic Monitoring and Real-Time Prevention of Privacy Leaks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Minas Gjoka", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile devices play an essential role in the Internet today, and there is an increasing interest in using them as a vantage point for network measurement from the edge. At the same time, these devices store personal, sensitive information, and there is a growing number of applications that leak it. We propose AntMonitor-- the first system of its kind that supports (i) collection of large-scale, semantic-rich network traffic in a way that respects users' privacy preferences and (ii) detection and prevention of leakage of private information in real time. The first property makes AntMonitor a powerful tool for network researchers who want to collect and analyze large-scale yet fine-grained mobile measurements. The second property can work as an incentive for using AntMonitor and contributing data for analysis. As a proof-of-concept, we have developed a prototype of AntMonitor, deployed it to monitor 9 users for 2 months, and collected and analyzed 20 GB of mobile data from 151 applications. Preliminary results show that fine-grained data collected from AntMonitor could enable application classification with higher accuracy than state-of-the-art approaches. In addition, we demonstrated that AntMonitor could help prevent several apps from leaking private information over unencrypted traffic, including phone numbers, emails, and device identifiers.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789170"}, {"primary_key": "4477468", "vector": [], "sparse_vector": [], "title": "FlexiWeb: Network-Aware Compaction for Accelerating Mobile Web Transfers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Harsha V. Madhyastha", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "To reduce page load times and bandwidth usage for mobile web browsing, middleboxes that compress page content are commonly used today. Unfortunately, this can hurt performance in many cases; via an extensive measurement study, we show that using middleboxes to facilitate compression results in up to 28% degradation in page load times when the client enjoys excellent wireless link conditions. We find that benefits from compression are primarily realized under bad network conditions. Guided by our study, we design and implement FlexiWeb, a framework that determines both when to use a middlebox and how to use it, based on the client's network conditions. First, FlexiWeb selectively fetches objects on a web page either directly from the source or via a middlebox, rather than fetching all objects via the middlebox. Second, instead of simply performing lossless compression of all content, FlexiWeb performs network-aware compression of images by selecting from among a range of content transformations. We implement and evaluate a prototype of FlexiWeb using Google's open source Chromium mobile browser and our implementation of a modified version of Google's open source compression proxy. Our extensive experiments show that, across a range of scenarios, FlexiWeb reduces page load times for mobile clients by 35-42% compared to the status quo.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790128"}, {"primary_key": "4477475", "vector": [], "sparse_vector": [], "title": "Demo: Implementation of Real-time WiFi Receiver in Ziria, Language for Rapid Prototyping of Wireless PHY.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software-defined radios (SDR) have the potential to bring major innovation in wireless networking design. However, their impact so far has been limited due to complex programming tools. Most of the existing tools are either too slow to achieve the full line speeds of contemporary wireless PHYs or are too complex to master. In this demo we present our novel SDR programming environment called Ziria. Ziria consists of a novel programming language and an optimizing compiler. The compiler is able to synthesize very efficient SDR code from high-level PHY descriptions written in Ziria language. To illustrate its potential, we present the design of an LTE-like PHY layer in Ziria. We run it on the Sora SDR platform and demonstrate on a test-bed that it is able to operate in real-time.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789185"}, {"primary_key": "4477476", "vector": [], "sparse_vector": [], "title": "Experience: Rethinking RRC State Machine Optimization in Light of Recent Advancements.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Broadband mobile networks utilize a radio resource control (RRC) state machine to allocate scarce radio resources. Current implementations introduce high latencies and cross-layer degradation. Recently, the RRC enhancements, continuous packet connectivity (CPC) and the enhanced forward access channel (Enhanced FACH), have emerged in UMTS. We study the availability and performance of these enhancements on a network serving a market with a population in the millions. Our experience in the wild shows these enhancements offer significant reductions in latency, mobile device energy consumption, and improved end user experience. We develop new over-the-air measurements that resolve existing limitations in measuring RRC parameters. We find CPC provides significant benefits with minimal resource costs, prompting us to rethink past optimization strategies. We examine the cross-layer performance of CPC and Enhanced FACH, concluding that CPC provides reductions in mobile device energy consumption for many applications. While the performance increase of HS-FACH is substantial, cross-layer performance is limited by the legacy uplink random access channel (RACH), and we conclude full support of Enhanced FACH is necessary to benefit most applications. Given that UMTS growth will exceed LTE for several more years and the greater worldwide deployment of UMTS, our quantitative results should be of great interest to network operators adding capacity to these networks. Finally, these results provide new insights for application developers wishing to optimize performance with these RRC enhancements.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790105"}, {"primary_key": "4477478", "vector": [], "sparse_vector": [], "title": "WiDraw: Enabling Hands-free Drawing in the Air on Commodity WiFi Devices.", "authors": ["Li Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper demonstrates that it is possible to leverage WiFi signals from commodity mobile devices to enable hands-free drawing in the air. While prior solutions require the user to hold a wireless transmitter, or require custom wireless hardware, or can only determine a pre-defined set of hand gestures, this paper introduces WiDraw, the first hand motion tracking system using commodity WiFi cards, and without any user wearables. WiDraw harnesses the Angle-of-Arrival values of incoming wireless signals at the mobile device to track the user's hand trajectory. We utilize the intuition that whenever the user's hand occludes a signal coming from a certain direction, the signal strength of the angle representing the same direction will experience a drop. Our software prototype using commodity wireless cards can track the user's hand with a median error lower than 5 cm. We use WiDraw to implement an in-air handwriting application that allows the user to draw letters, words, and sentences, and achieves a mean word recognition accuracy of 91%.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790129"}, {"primary_key": "4477480", "vector": [], "sparse_vector": [], "title": "Poster: Scoping Environment to Assist 60 GHz Link Deployment.", "authors": ["Sanjib Sur", "<PERSON><PERSON><PERSON>"], "summary": "Line-of-Sight blockage by human body is a severe challenge to enable robust 60 GHz directional links. Beamsteering is one feasible solution to overcome this problem by electronically steering phased-array beam towards Non-Line-of-Sight. However, effectiveness of beamsteering depends on the link deployment and a lack of assessment of steering effectiveness may render the link completely blacked-out during human blockage. In this poster, we propose a new technique called BeamScope, that predicts best possible location for a randomly deployed link in an indoor environment without the need of any explicit war-driving. BeamScope first characterizes the environment exploiting measurement from the randomly deployed reference location and then predicts the performance in unobserved locations to suggest a possible re-deployment. The environment characterization is captured through a novel metric and prediction is achieved via how this metric is shared between the reference location and unobserved locations. Our preliminary results show promising accuracy of identifying the best possible alternate location for 60 GHz link to achieve a robust connection during human blockage.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795182"}, {"primary_key": "4477482", "vector": [], "sparse_vector": [], "title": "Poster: Visible Light Communication in the Dark.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a new visible light communication (VLC) primitive: VLC in the dark, where the communication sustains even when the LED light appears dark. We achieve the goal by reducing the duty cycle of the light source to an extremely low level (0.0019%) such that the illuminance is imperceptible to human eyes and yet can still be sensed by photodiodes to decode data. VLC in the dark consumes much lower energy than the conventional VLC links and thus is appealing for enabling VLC uplink or communication among Internet-of-Things devices with tight energy budget. We build a preliminary prototype to demonstrate its feasibility. Our current single link achieves 190.7 bps data rate with 46.8 microwatts power consumption at the LED.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795181"}, {"primary_key": "4477484", "vector": [], "sparse_vector": [], "title": "EchoTag: Accurate Infrastructure-Free Indoor Location Tagging with Smartphones.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a novel mobile system, called EchoTag, that enables phones to tag and remember indoor locations without requiring any additional sensors or pre-installed infrastructure. The main idea behind EchoTag is to actively generate acoustic signatures by transmitting a sound signal with a phone's speakers and sensing its reflections with the phone's microphones. This active sensing provides finer-grained control of the collected signatures than the widely-used passive sensing. For example, because the sensing signal is controlled by EchoTag, it can be intentionally chosen to enrich the sensed signatures and remove noises from useless reflections. Extensive experiments show that EchoTag distinguishes 11 tags at 1cm resolution with 98% accuracy and maintains 90% accuracy even a week after its training. With this accurate location tagging, one can realize many interesting applications, such as automatically turning on the silent mode of a phone when it is placed at a pre-defined location/area near the bed or streaming favorite songs to speakers if it is placed near a home entertainment system. Most participants of our usability study agree on the usefulness of EchoTag's potential applications and the adequacy of its sensing accuracy for supporting these applications.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790102"}, {"primary_key": "4477489", "vector": [], "sparse_vector": [], "title": "Poster: Understanding YouTube QoE in Cellular Networks with YoMoApp: A QoE Monitoring Tool for YouTube Mobile.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Phuoc Tran-Gia", "<PERSON><PERSON>"], "summary": "The performance of YouTube in cellular networks is crucial to network operators, who try to find a trade-off between cost-efficient handling of the huge traffic amounts and high perceived end-user Quality of Experience (QoE). In this paper we present YoMoApp (YouTube Performance Monitoring Application), an Android application which passively monitors key performance indicators (KPIs) of YouTube adaptive video streaming on end-user smartphones. The monitored KPIs (i.e., player state/events, re-buffering, and video quality levels) can be used to analyze the QoE of mobile YouTube video sessions. YoMoApp is a valuable tool to assess the performance of cellular networks with respect to YouTube traffic, as well as to develop optimizations and QoE models for mobile HTTP adaptive streaming. We try <PERSON>MoApp through real subjective QoE lab tests showing that the tool is accurate to capture the experience of end-users watching YouTube on smartphones.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795176"}, {"primary_key": "4477490", "vector": [], "sparse_vector": [], "title": "Poster: A Low Cost People Flow Monitoring System For Sensing The Potential Danger.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For a long history, stampede is one of the high potential disaster when thousands of people gathered. Current monitoring systems, however, can only detect the presence of a small number of sparsely located targets, rather than to monitor the change of people flow where there are large number of dense crowd in the environment. This paper presents DanSen, a low-cost people flow monitoring system for sensing the potential danger using the existing wifi infrastructures. Inspired by the dynamic light scattering (DLS) theory, the designed DanSen calculates the correlations between the initial channel state information (CSI) data and all the history CSI data to monitor the changes of people flow and also estimates the sharpness of the changes. By doing so, DanSen can be utilised to perceive the potential danger. Real-world experimental results illustrate the advantage and effectiveness of DanSen.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795169"}, {"primary_key": "4477492", "vector": [], "sparse_vector": [], "title": "MoLe: Motion Leaks through Smartwatch Sensors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Imagine a user typing on a laptop keyboard while wearing a smart watch. This paper asks whether motion sensors from the watch can leak information about what the user is typing. While its not surprising that some information will be leaked, the question is how much? We find that when motion signal processing is combined with patterns in English language, the leakage is substantial. Reported results show that when a user types a word $W$, it is possible to shortlist a median of 24 words, such that $W$ is in this shortlist. When the word is longer than $6$ characters, the median shortlist drops to $10$. Of course, such leaks happen without requiring any training from the user, and also under the (obvious) condition that the watch is only on the left hand. We believe this is surprising and merits awareness, especially in light of various continuous sensing apps that are emerging in the app market. Moreover, we discover additional \"leaks\" that can further reduce the shortlist -- we leave these exploitations to future work.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790121"}, {"primary_key": "4477493", "vector": [], "sparse_vector": [], "title": "Understanding and Modeling of WiFi Signal Based Human Activity Recognition.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Some pioneer WiFi signal based human activity recognition systems have been proposed. Their key limitation lies in the lack of a model that can quantitatively correlate CSI dynamics and human activities. In this paper, we propose CARM, a CSI based human Activity Recognition and Monitoring system. CARM has two theoretical underpinnings: a CSI-speed model, which quantifies the correlation between CSI value dynamics and human movement speeds, and a CSI-activity model, which quantifies the correlation between the movement speeds of different human body parts and a specific human activity. By these two models, we quantitatively build the correlation between CSI value dynamics and a specific human activity. CARM uses this correlation as the profiling mechanism and recognizes a given activity by matching it to the best-fit profile. We implemented CARM using commercial WiFi devices and evaluated it in several different environments. Our results show that CARM achieves an average accuracy of greater than 96%.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790093"}, {"primary_key": "4477495", "vector": [], "sparse_vector": [], "title": "Demo: OpenVLC1.0 Platform for Research in Visible Light Communication Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> Yin", "Omprakash Gnawali", "<PERSON>"], "summary": "Built around a cost-effective embedded Linux platform, OpenVLC is an open source project (www.openvlc.org) for research in Visible Light Communication (VLC) Networks. In this work, we introduce and demonstrate the OpenVLC1.0 platform, a flexible, software-defined, and low-cost research platform. OpenVLC1.0 consists of a simple electronic design, and a new driver of the Linux operating system that implements the MAC, part of the PHY layers and it offers an interface to Internet protocols. The electronics of OpenVLC implement a flexible optical front-end consisting of commodity low- and high-power Light Emitting Diodes (LEDs), photodiodes (PDs), and ancillary electronic circuitry. In order to quickly start playing with VLC Networks, we have designed and developed a printed circuit board (OpenVLC1.0 cape). The cape can be plugged into the main embedded Beaglebone board. Researchers can then swiftly build PHY and MAC protocols using the software implementation (OpenVLC1.0 driver), and prototype innovative solutions in realistic network setups. In this demo, we show that OpenVLC1.0 can switch between different MAC protocols, it can choose different optical channel for data transmission and reception, and it can be employed jointly with standard TCP/IP diagnostic tools.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2789173"}, {"primary_key": "4477497", "vector": [], "sparse_vector": [], "title": "Acoustic Eavesdropping through Wireless Vibrometry.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Loudspeakers are widely used in conferencing and infotainment systems. Private information leakage from loudspeaker sound is often assumed to be preventable using sound-proof isolators like walls. In this paper, we explore a new acoustic eavesdropping attack that can subvert such protectors using radio devices. Our basic idea lies in an acoustic-radio transformation (ART) algorithm, which recovers loudspeaker sound by inspecting the subtle disturbance it causes to the radio signals generated by an adversary or by its co-located WiFi transmitter. ART builds on a modeling framework that distills key factors to determine the recovered audio quality. It incorporates diversity mechanisms and noise suppression algorithms that can boost the eavesdropping quality. We implement the ART eavesdropper on a software-radio platform and conduct experiments to verify its feasibility and threat level. When targeted at vanilla PC or smartphone loudspeakers, the attacker can successfully recover high-quality audio even when blocked by sound-proof walls. On the other hand, we propose several pragmatic countermeasures that can effectively reduce the attacker's audio recovery quality by orders of magnitude.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790119"}, {"primary_key": "4477498", "vector": [], "sparse_vector": [], "title": "mTrack: High-Precision Passive Tracking Using Millimeter Wave Radios.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Radio-based passive-object sensing can enable a new form of pervasive user-computer interface. Prior work has employed various wireless signal features to sense objects under a set of predefined, coarse motion patterns. But an operational UI, like a trackpad, often needs to identify fine-grained, arbitrary motion. This paper explores the feasibility of tracking a passive writing object (e.g., pen) at sub-centimeter precision. We approach this goal through a practical design, mTrack, which uses highly-directional 60 GHz millimeter-wave radios as key enabling technology. mTrack runs a discrete beam scanning mechanism to pinpoint the object's initial location, and tracks its trajectory using a signal-phase based model. In addition, mTrack incorporates novel mechanisms to suppress interference from background reflections, taking advantage of the short wavelength of 60 GHz signals. We prototype mTrack and evaluate its performance on a 60 GHz reconfigurable radio platform. Experimental results demonstrate that mTrack can locate/track a pen with 90-percentile error below 8 mm, enabling new applications such as wireless transcription and virtual trackpad.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790113"}, {"primary_key": "4477500", "vector": [], "sparse_vector": [], "title": "Hekaton: Efficient and Practical Large-Scale MIMO.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Large-scale multiuser MIMO (MU-MIMO) systems have the potential for multi-fold scaling of network capacity. The research community has recognized this theoretical potential and developed architectures [1,2] with large numbers of RF chains. Unfortunately, building the hardware with a large number of RF chains is challenging in practice. CSI data transport and computational overhead of MU-MIMO beamforming can also become prohibitive under large network scale. Furthermore, it is difficult to physically append extra RF chains on existing communication equipments to support such large-scale MU-MIMO architectures.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790116"}, {"primary_key": "4477501", "vector": [], "sparse_vector": [], "title": "Precise Power Delay Profiling with Commodity WiFi.", "authors": ["<PERSON><PERSON><PERSON>", "Zhenjiang Li", "<PERSON>"], "summary": "Power delay profiles characterize multipath channel features, which are widely used in motion- or localization-based applications. Recent studies show that the power delay profile may be derived from the CSI traces collected from commodity WiFi devices, but the performance is limited by two dominating factors. The resolution of the derived power delay profile is determined by the channel bandwidth, which is however limited on commodity WiFi. The collected CSI reflects the signal distortions due to both the channel attenuation and the hardware imperfection. A direct derivation of power delay profiles using raw CSI measures, as has been done in the literature, results in significant inaccuracy. In this paper, we present Splicer, a software-based system that derives high-resolution power delay profiles by splicing the CSI measurements from multiple WiFi frequency bands. We propose a set of key techniques to separate the mixed hardware errors from the collected CSI measurements. Splicer adapts its computations within stringent channel coherence time and thus can perform well in presence of mobility. Our experiments with commodity WiFi NICs show that <PERSON><PERSON><PERSON><PERSON> substantially improves the accuracy in profiling multipath characteristics, reducing the errors of multipath distance estimation to be less than $2m$. Splicer can immediately benefit upper-layer applications. Our case study with recent single-AP localization achieves a median localization error of $0.95m$.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790124"}, {"primary_key": "4477502", "vector": [], "sparse_vector": [], "title": "piStream: Physical Layer Informed Adaptive Video Streaming over LTE.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Adaptive HTTP video streaming over LTE has been gaining popularity due to LTE's high capacity. Quality of adaptive streaming depends highly on the accuracy of client's estimation of end-to-end network bandwidth, which is challenging due to LTE link dynamics. In this paper, we present piStream, that allows a client to efficiently monitor the LTE basestation's PHY-layer resource allocation, and then map such information to an estimation of available bandwidth. Given the PHY-informed bandwidth estimation, piStream uses a probabilistic algorithm to balance video quality and the risk of stalling, taking into account the burstiness of LTE downlink traffic loads. We conduct a real-time implementation of piStream on a software-radio tethered to an LTE smartphone. Comparison with state-of-the-art adaptive streaming protocols demonstrates that piStream can effectively utilize the LTE bandwidth, achieving high video quality with minimal stalling rate.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790118"}, {"primary_key": "4477503", "vector": [], "sparse_vector": [], "title": "ToneTrack: Leveraging Frequency-Agile Radios for Time-Based Indoor Wireless Localization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Indoor localization of mobile devices and tags has received much attention recently, with encouraging fine-grained localization results available with enough line-of-sight coverage and hardware infrastructure. Some of the most promising techniques analyze the time-of-arrival of incoming signals, but the limited bandwidth available to most wireless transmissions fundamentally constrains their resolution. Frequency-agile wireless networks utilize bandwidths of varying sizes and locations in a wireless band to efficiently share the wireless medium between users. ToneTrack is an indoor location system that achieves sub-meter accuracy with minimal hardware and antennas, by leveraging frequency-agile wireless networks to increase the effective bandwidth. Our novel signal combination algorithm combines time-of-arrival data from different transmissions as a mobile device hops across different channels, approaching time resolutions previously not possible with a single narrowband channel. ToneTrack's novel channel combination and spectrum identification algorithms together with the triangle inequality scheme yield superior results even in non-line-of-sight scenarios with one to two walls separating client and APs and also in the case where the direct path from mobile client to an AP is completely blocked. We implement ToneTrack on the WARP hardware radio platform and use six of them served as APs to localize Wi-Fi clients in an indoor testbed over one floor of an office building. Experimental results show that ToneTrack can achieve a median 90 cm accuracy when 20 MHz bandwidth APs overhear three packets from adjacent channels.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790125"}, {"primary_key": "4477504", "vector": [], "sparse_vector": [], "title": "See Through Walls with COTS RFID System!", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lin", "Xiangyang Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Through-wall tracking has gained a lot of attentions in civilian applications recently. Many applications would benefit from such device-free tracking, e.g. elderly people surveillance, intruder detection, gaming, etc. In this work, we present a system, named <PERSON><PERSON>, for tracking moving objects without instrumenting them us- ing COTS RFID readers and tags. It works even through walls and behind closed doors. It aims to enable a see-through-wall technology that is low-cost, compact, and accessible to civilian purpose. In traditional RFID systems, tags modulate their IDs on the backscatter signals, which is vulnerable to the interferences from the ambient reflections. Unlike past work, which considers such vulnerability as detrimental, our design exploits it to detect surrounding objects even through walls. Specifically, we attach a group of RFID tags on the outer wall and logically convert them into an antenna array, receiving the signals reflected off moving objects. This paper introduces two main innovations. First, it shows how to eliminate the flash (e.g. the stronger reflections off walls) and extract the reflections from the backscatter signals. Second, it shows how to track the moving object based on HMM (Hidden Markov Model) and its reflections. To the best of our knowledge, we are the first to implement a through-wall tracking using the COTS RFID systems. Empirical measurements with a prototype show that <PERSON><PERSON> can detect objects behind 5\" hollow wall and 8\" concrete wall, and achieve median tracking errors of 7.8cm and 20cm in the X and Y dimensions.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790100"}, {"primary_key": "4477505", "vector": [], "sparse_vector": [], "title": "SAMPLES: Self Adaptive Mining of Persistent LExical Snippets for Classifying Mobile Application Traffic.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present SAMPLES: Self Adaptive Mining of Persistent LExical Snippets; a systematic framework for classifying network traffic generated by mobile applications. SAMPLES constructs conjunctive rules, in an automated fashion, through a supervised methodology over a set of labeled flows (the training set).", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790097"}, {"primary_key": "4477507", "vector": [], "sparse_vector": [], "title": "Poster: A Multi-Drone Platform for Empowering Drones&apos; Teamwork.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rapid development of UAV technology has opened up numerous applications. Further, cooperating drones\\footnote{In this paper, we refer to all types of UAVs as drones.} possess a great potential in various areas of applications. Yet, there has not been a platform for a fleet of drones. It is rather difficult to control multiple drones through a single ground control station. In fact, drones need to consistently exchange flight information to maintain the formation of the fleet and follow the commands given from the GCS. Thus, drones need a robust network established among the fleet to provide a fleet control. In this paper, we propose Net-Drone, a multi-drone platform for applications that require cooperation of multiple drones. Net-Drone is equipped with strong network functionalities to provide a control system for a fleet of drones. Through case studies conducted with a prototype implementation, the proposed system is demonstrated. Video of the conducted case study can be found in http://youtu.be/lFaWsEmiQvw.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795180"}, {"primary_key": "4477509", "vector": [], "sparse_vector": [], "title": "Kaleido: You Can Watch It But Cannot Record It.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently a number of systems have been developed to implement and improve the visual communication over screen-camera links. In this paper we study an opposite problem: how to prevent unauthorized users from videotaping a video played on a screen, such as in a theater, while do not affect the viewing experience of legitimate audiences. We propose and develop a light-weight hardware-free system, called Kaleido, that ensures these properties by taking advantage of the limited disparities between the screen-eye channel and the screen-camera channel. Kaleido does not require any extra hardware and is purely based on re-encoding the original video frame into multiple frames used for displaying. We extensively test our system Kaleido using a variety of smartphone cameras. Our experiments confirm that Kaleido preserves the high-quality screen-eye channel while reducing the secondary screen-camera channel quality significantly.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790106"}, {"primary_key": "4477510", "vector": [], "sparse_vector": [], "title": "The Design and Implementation of a Wireless Video Surveillance System.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Internet-enabled cameras pervade daily life, generating a huge amount of data, but most of the video they generate is transmitted over wires and analyzed offline with a human in the loop. The ubiquity of cameras limits the amount of video that can be sent to the cloud, especially on wireless networks where capacity is at a premium. In this paper, we present Vigil, a real-time distributed wireless surveillance system that leverages edge computing to support real-time tracking and surveillance in enterprise campuses, retail stores, and across smart cities. Vigil intelligently partitions video processing between edge computing nodes co-located with cameras and the cloud to save wireless capacity, which can then be dedicated to Wi-Fi hotspots, offsetting their cost. Novel video frame prioritization and traffic scheduling algorithms further optimize Vigil's bandwidth utilization. We have deployed Vigil across three sites in both whitespace and Wi-Fi networks. Depending on the level of activity in the scene, experimental results show that Vigil allows a video surveillance system to support a geographical area of coverage between five and 200 times greater than an approach that simply streams video over the wireless network. For a fixed region of coverage and bandwidth, Vigil outperforms the default equal throughput allocation strategy of Wi-Fi by delivering up to 25% more objects relevant to a user's query.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790123"}, {"primary_key": "4477511", "vector": [], "sparse_vector": [], "title": "Extending Mobile Interaction Through Near-Field Visible Light Sensing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile devices are shrinking their form factors for portability, but user-mobile interaction is becoming increasingly challenging. In this paper, we propose a novel system called Okuli to meet this challenge. Okuli is a compact, low-cost system that can augment a mobile device and extend its interaction workspace to any nearby surface area. Okuli piggybacks on visible light communication modules, and uses a low-power LED and two light sensors to locate user's finger within the workspace. It is built on a light propagation/reflection model that achieves around one-centimeter location precision, with zero run-time training overhead. We have prototyped Okuli as an Android peripheral, with a 3D-printed shroud to host the LED and light sensors. Our experiments demonstrate Okuli's accuracy, stability, energy efficiency, as well as its potential in serving virtual keyboard and trackpad applications.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790115"}, {"primary_key": "4477514", "vector": [], "sparse_vector": [], "title": "Poster: Distributed Voronoi-based Acoustic Source Localization with Wireless Sensor Networks.", "authors": ["<PERSON><PERSON><PERSON>", "Naigao Jin", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents DiVA, a new acoustic source localization scheme that uses an ad-hoc network of microphone sensor nodes to produce an accurate estimate of the source's location. DiVA uses pairwise comparisons of sound detection timestamps between local Voronoi neighbors to identify the node closest to the acoustic source and then estimates the source's location. The scheme improves on the state of the art by effectively dealing with anchor nodes' position error, time stamp measurement error and time synchronization error in real world conditions. Through simulation and experimental evaluations, DiVA is shown to be more robust than existing solutions under different error conditions.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2795178"}, {"primary_key": "4477515", "vector": [], "sparse_vector": [], "title": "Reusing 60GHz Radios for Mobile Radar Imaging.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The future of mobile computing involves autonomous drones, robots and vehicles. To accurately sense their surroundings in a variety of scenarios, these mobile computers require a robust environmental mapping system. One attractive approach is to reuse millimeterwave communication hardware in these devices, e.g. 60GHz networking chipset, and capture signals reflected by the target surface. The devices can also move while collecting reflection signals, creating a large synthetic aperture radar (SAR) for high-precision RF imaging. Our experimental measurements, however, show that this approach provides poor precision in practice, as imaging results are highly sensitive to device positioning errors that translate into phase errors. We address this challenge by proposing a new 60GHz imaging algorithm, {\\em RSS Series Analysis}, which images an object using only RSS measurements recorded along the device's trajectory. In addition to object location, our algorithm can discover a rich set of object surface properties at high precision, including object surface orientation, curvature, boundaries, and surface material. We tested our system on a variety of common household objects (between 5cm--30cm in width). Results show that it achieves high accuracy (cm level) in a variety of dimensions, and is highly robust against noises in device position and trajectory tracking. We believe that this is the first practical mobile imaging system (re)using 60GHz networking devices, and provides a basic primitive towards the construction of detailed environmental mapping systems.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2789168.2790112"}, {"primary_key": "4520539", "vector": [], "sparse_vector": [], "title": "Proceedings of the 21st Annual International Conference on Mobile Computing and Networking, MobiCom 2015, Paris, France, September 7-11, 2015", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Welcome to ACM MobiCom 2015, the 21st Annual International Conference on Mobile Computing and Networking. MobiCom is the premier forum for publishing and presenting cutting-edge research in mobile systems and wireless networks. The technical program this year features 38 outstanding papers that cover a wide variety of topics including energy, sensing, security, wireless access, applications, localization, Internet of things, mobile cloud, measurement and analysis. We created a newExperiencetrack this year to encourage authors to present extensive experiences with implementation, deployment, and operations of mobile ncomputing and wireless networks. One of the accepted papers is an Experience paper on cellular networks. This year's call for papers attracted 207 qualified submissions from across the globe that were carefully reviewed by 46 Technical Program Committee (TPC) members (+2 TPC chairs) along with a selected group of external experts. The TPC was formed with the goal of covering diverse research expertise as well as diverse perspectives and approaches. The TPC included researchers from 12 countries including China, France, Germany, India, Italy, Singapore, South Korea, Spain, Sweden, Switzerland, UK, and USA. 25% of the members were female, the highest ever in the history of MobiCom. We also had broad industry participation with TPC members from Alcatel-Lucent, Google, HP, IBM, Microsoft, NEC, and Telefonica. The paper review process was double-blinded and carried out in three phases. In the first phase, each paper was reviewed by at least three TPC members, and the top 112 papers were selected for the second phase. In addition to reviewer scores, reviewer confidence and normalization with respect to other papers in a reviewer's pile, were also considered in selecting papers. In the second phase, each paper was reviewed by at least two more reviewers followed by an online, often intense, discussion, producing 68 papers for the final phase. The final TPC meeting was held on May 28th and 29th in Salt Lake City, Utah. These 68 papers were organized by their topic areas, and discussed at length at the meeting. Eventually, 38 papers were shortlisted for inclusion in the program and a shepherd from the TPC was assigned to each of these papers. As the last step, each of the shortlisted papers was shepherded through a \"blind\" process where the authors interacted with all the reviewers and the shepherd to address the review comments without knowing the reviewers' or the shepherds' identities. The end result is an exciting technical program composed of 38 very high quality papers. During the review process, Prof. Robin Kravets, the TPC co-chair of MobiCom 2013, handled the papers that were co-authored by TPC chairs, and those that had conflict-of-interest with both TPC chairs. To ensure fairness and preserve the anonymity of all authors and reviewers, the assignment of reviewers, the reviews and discussions of these papers were done out of band without any exposure to the TPC chairs.", "published": "2015-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}]