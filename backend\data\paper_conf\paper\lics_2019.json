[{"primary_key": "3085513", "vector": [], "sparse_vector": [], "title": "Matching μ-Logic.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Matching logic is a logic for specifying and reasoning about structure by means of patterns and pattern matching. This paper makes two contributions. First, it proposes a sound and complete proof system for matching logic in its full generality. Previously, sound and complete deduction for matching logic was known only for particular theories providing equality and membership. Second, it proposes matching μ -Iogic, an extension of matching logic with a least fixpoint μ -binder, It is shown that matching μ -Iogic captures as special instances many important logics in mathematics and computer science, including first-order logic with least fixpoints, modal μ -Iogic as well as dynamic logic and various temporal logics such as infinite/finite-trace linear temporal logic and computation tree logic, and notably reachability logic, the underlying logic of the \\mathbbk framework for programming language semantics and formal analysis. Matching μ -logic therefore serves as a unifying foundation for specifying and reasoning about fixpoints and induction, programming languages and program specification and verification.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785675"}, {"primary_key": "3085514", "vector": [], "sparse_vector": [], "title": "A comonadic view of simulation and quantum resources.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study simulation and quantum resources in the setting of the sheaf-theoretic approach to contextuality and nonlocality. Resources are viewed behaviourally, as empirical models. In earlier work, a notion of morphism for these empirical models was proposed and studied. We generalize and simplify the earlier approach, by starting with a very simple notion of morphism, and then extending it to a more useful one by passing to a co-K<PERSON><PERSON><PERSON> category with respect to a comonad of measurement protocols. We show that these morphisms capture notions of simulation between empirical models obtained via \"free\" operations in a resource theory of contextuality, including the type of classical control used in measurement-based quantum computation schemes.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785677"}, {"primary_key": "3085515", "vector": [], "sparse_vector": [], "title": "Algorithmic barriers to representing conditional independence.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We define a represention of conditional independence in terms of products of probability kernels, and ask when such representations are computable. We pursue this question in the context of exchangeable sequences and arrays of random variables, which arise in statistical contexts. Exchangeable sequences are conditionally i.i.d. by <PERSON>'s theorem. Known results about the computability of <PERSON>'s theorem imply that these conditional independences are computable. The conditional independences underlying exchangeable arrays are characterized by the Al<PERSON>us-<PERSON> theorem. In the special case of adjacency matrices of undirected graphs, i.e., symmetric binary arrays, this representation theorem expresses the conditional independences in terms of graphons. We prove that there exist exchangeable random graphs that can be computably sampled but whose corresponding graphons are not computable as functions or even as L 1 equivalence classes. We also give results on the approximability of graphons in certain special cases.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785762"}, {"primary_key": "3085516", "vector": [], "sparse_vector": [], "title": "Block products for algebras over countable words and applications to logic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "We propose a seamless integration of the block product operation to the recently developed algebraic framework for regular languages of countable words. A simple but subtle accompanying block product principle has been established. Building on this, we generalize the well-known algebraic characterizations of first-order logic (resp. first-order logic with two variables) in terms of strongly (resp. weakly) iterated block products. We use this to arrive at a complete analogue of <PERSON><PERSON><PERSON>-<PERSON> theorem for countable words. We also explicate the role of block products for linear temporal logic by formulating a novel algebraic characterization of a natural fragment.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785669"}, {"primary_key": "3085517", "vector": [], "sparse_vector": [], "title": "Timed Systems through the Lens of Logic.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we analyze timed systems with data structures. We start by describing behaviors of timed systems using graphs with timing constraints. Such a graph is called realizable if we can assign time-stamps to nodes or events so that they are consistent with the timing constraints. The logical definability of several graph properties [20], [10] has been a challenging problem, and we show, using a highly nontrivial argument, that the realizability property for collections of graphs with strict timing constraints is logically definable in a class of propositional dynamic logic (EQ-ICPDL), which is strictly contained in MSO. Using this result, we propose a novel, algorithmically efficient and uniform proof technique for the analysis of timed systems enriched with auxiliary data structures, like stacks and queues. Our technique unravels new results (for emptiness checking as well as model checking) for timed systems with richer features than considered so far, while also recovering existing results.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785684"}, {"primary_key": "3085518", "vector": [], "sparse_vector": [], "title": "Probabilistic Relational Reasoning via Metrics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Fuzz programming language by <PERSON> and <PERSON> uses an elegant linear type system combined with a monad-like type to express and reason about probabilistic sensitivity properties, most notably ε -differential privacy. We show how to extend Fuzz to capture more general relational properties of probabilistic programs, with approximate, or (ε, δ) differential privacy serving as a leading example. Our technical contributions are threefold. First, we introduce the categorical notion of comonadic lifting of a monad to model composition properties of probabilistic divergences. Then, we show how to express relational properties in terms of sensitivity properties via an adjunction we call the path construction. Finally, we instantiate our semantics to model the terminating fragment of Fuzz extended with types carrying information about other divergences between distributions.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785715"}, {"primary_key": "3085519", "vector": [], "sparse_vector": [], "title": "On the Power of Symmetric Linear Programs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider families of symmetric linear programs (LPs) that decide a property of graphs (or other relational structures) in the sense that, for each size of graph, there is an LP defining a polyhedral lift that separates the integer points corresponding to graphs with the property from those corresponding to graphs without the property. We show that this is equivalent, with at most polynomial blow-up in size, to families of symmetric Boolean circuits with threshold gates. In particular, when we consider polynomial-size LPs, the model is equivalent to definability in a non-uniform version of fixed-point logic with counting (FPC). Known upper and lower bounds for FPC apply to the non-uniform version. In particular, this implies that the class of graphs with perfect matchings has polynomial-size symmetric LPs while we obtain an exponential lower bound for symmetric LPs for the class of Hamiltonian graphs. We compare and contrast this with previous results (<PERSON><PERSON><PERSON> 1991) showing that any symmetric LPs for the matching and TSP polytopes have exponential size. As an application, we establish that for random, uniformly distributed graphs, polynomial-size symmetric LPs are as powerful as general Boolean circuits. We illustrate the effect of this on the well-studied planted-clique problem.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785792"}, {"primary_key": "3085520", "vector": [], "sparse_vector": [], "title": "Type-Based Complexity Analysis of Probabilistic Functional Programs.", "authors": ["<PERSON>", "Ugo <PERSON>", "<PERSON>"], "summary": "We show that complexity analysis of probabilistic higher-order functional programs can be carried out compositionally by way of a type system. The introduced type system is a significant extension of refinement types. On the one hand, the presence of probabilistic effects requires adopting a form of dynamic distribution type, subject to a coupling-based subtyping discipline. On the other hand, recursive definitions are proved terminating by way of <PERSON><PERSON><PERSON><PERSON> ranking functions. We prove not only that the obtained type system, called l\\pmbRPCF, provides a sound methodology for average case complexity analysis, but also that it is extensionally complete, in the sense that any average case nolytime Turing machines can be encoded as a term typable in l\\pmbRPCF.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785725"}, {"primary_key": "3085521", "vector": [], "sparse_vector": [], "title": "Long-run Satisfaction of Path Properties.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ocan Sankur"], "summary": "The paper introduces the concepts of long-run frequency of path properties for paths in Kripke structures, and their generalization to long-run probabilities for schedulers in Markov decision processes. We then study the natural optimization problem of computing the optimal values of these measures, when ranging over all paths or all schedulers, and the corresponding decision problem when given a threshold. The main results are as follows. For (repeated) reachability and other simple properties, optimal long-run probabilities and corresponding optimal memoryless schedulers are computable in polynomial time. When it comes to constrained reachability properties, memoryless schedulers are no longer sufficient, even in the non-probabilistic setting. Nevertheless, optimal long-run probabilities for constrained reachability are computable in pseudo-polynomial time in the probabilistic setting and in polynomial time for Kripke structures. Finally for co-safety properties expressed by NFA, we give an exponential-time algorithm to compute the optimal long-run frequency, and prove the PSPACE-completeness of the threshold problem.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785672"}, {"primary_key": "3085522", "vector": [], "sparse_vector": [], "title": "When is Ontology-Mediated Querying Efficient?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In ontology-mediated querying, description logic (DL) ontologies are used to enrich incomplete data with domain knowledge which results in more complete answers to queries. However, the evaluation of ontology-mediated queries (OMQs) over relational databases is computationally hard. This raises the question when OMQ evaluation is efficient, in the sense of being tractable in combined complexity or fixed-parameter tractable. We study this question for a range of ontology-mediated query languages based on several important and widely-used DLs, using unions of conjunctive queries as the actual queries. For the DL ELHI⊥, we provide a characterization of the classes of OMQs that are fixed-parameter tractable. For its fragment ELH⊥ dr , which restricts the use of inverse roles, we provide a characterization of the classes of OMQs that are tractable in combined complexity. Both results are in terms of equivalence to OMQs of bounded tree width and rest on a reasonable assumption from parameterized complexity theory. They are similar in spirit to <PERSON><PERSON><PERSON>'s seminal characterization of the tractable classes of conjunctive queries over relational databases. We further study the complexity of the meta problem of deciding whether a given OMQ is equivalent to an OMQ of bounded tree width, providing several completeness results that range from NP to 2ExpTIME, depending on the DL used. We also consider the DL-Lite family of DLs, including members that, unlike εLHI ⊥ , admit functional roles.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785823"}, {"primary_key": "3085523", "vector": [], "sparse_vector": [], "title": "Promises Make Finite (Constraint Satisfaction) Problems Infinitary.", "authors": ["<PERSON><PERSON>"], "summary": "The fixed template Promise Constraint Satisfaction Problem (PCSP) is a recently proposed significant generalization of the fixed template CSP, which includes approximation variants of satisfiability and graph coloring problems. All the currently known tractable (i.e., solvable in polynomial time) PCSPs over finite templates can be reduced, in a certain natural way, to tractable CSPs. However, such CSPs are often over infinite domains. We show that the infinity is in fact necessary by proving that a specific finite-domain PCSP, namely (1-in-3-SAT, Not-All-Equal-3-SAT), cannot be naturally reduced to a tractable finite-domain CSP, unless P=NP.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785671"}, {"primary_key": "3085524", "vector": [], "sparse_vector": [], "title": "Why Propositional Quantification Makes Modal Logics on Trees Robustly Hard?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Adding propositional quantification to the modal logics K, T or S4 is known to lead to undecidability but CTL with propositional quantification under the tree semantics (QCTL t ) admits a non-elementary Tower-complete satisfiability problem. We investigate the complexity of strict fragments of QCTL t as well as of the modal logic K with propositional quantification under the tree semantics. More specifically, we show that QCTL t restricted to the temporal operator EX is already Tower-hard, which is unexpected as EX can only enforce local properties. When QCTL t restricted to EX is interpreted on N-bounded trees for some N ≥ 2, we prove that the satisfiability problem is AExppol - complete; AExppol -hardness is established by reduction from a recently introduced tiling problem, instrumental for studying the model-checking problem for interval temporal logics. As consequences of our proof method, we prove Tower-hardness of QCTL t restricted to EF or to EXEF and of the well-known modal logics K, KD, GL, S4, K4 and D4, with propositional quantification under a semantics based on classes of trees.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785656"}, {"primary_key": "3085525", "vector": [], "sparse_vector": [], "title": "Learning Concepts Definable in First-Order Logic with Counting.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We study Boolean classification problems over relational background structures in the logical framework introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (TOCS 2004). It is known (<PERSON><PERSON><PERSON> and <PERSON>, LICS 2017) that classifiers definable in first-order logic over structures of polylogarithmic degree can be learned in sublinear time, where the degree of the structure and the running time are measured in terms of the size of the structure. We generalise the results to the first-order logic with counting FOCN, which was introduced by <PERSON><PERSON> and <PERSON><PERSON> (LICS 2017) as an expressive logic generalising various other counting logics. Specifically, we prove that classifiers definable in FOCN over classes of structures of polylogarithmic degree can be consistently learned in sublinear time. This can be seen as a first step towards extending the learning framework to include numerical aspects of machine learning. We extend the result to agnostic probably approximately correct (PAC) learning for classes of structures of degree at most $(\\log \\log n)^c$ for some constant $c$. Moreover, we show that bounding the degree is crucial to obtain sublinear-time learning algorithms. That is, we prove that, for structures of unbounded degree, learning is not possible in sublinear time, even for classifiers definable in plain first-order logic.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785811"}, {"primary_key": "3085526", "vector": [], "sparse_vector": [], "title": "Topology is relevant (in a dichotomy conjecture for infinite-domain constraint satisfaction problems).", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The algebraic dichotomy conjecture for Constraint Satisfaction Problems (CSPs) of reducts of (infinite) finitely bounded homogeneous structures states that such CSPs are polynomial-time tractable when the model-complete core of the template has a pseudo-Siggers polymorphism, and NP-complete otherwise. One of the important questions related to this conjecture is whether, similarly to the case of finite structures, the condition of having a pseudo-Siggers polymorphism can be replaced by the condition of having polymorphisms satisfying a fixed set of identities of height 1, i.e., identities which do not contain any nesting of functional symbols. We provide a negative answer to this question by constructing for each non-trivial set of height 1 identities a structure whose polymorphisms do not satisfy these identities, but whose CSP is tractable nevertheless. An equivalent formulation of the dichotomy conjecture characterizes tractability of the CSP via the local satisfaction of nontrivial height 1 identities by polymorphisms of the structure. We show that local satisfaction and global satisfaction of nontrivial height 1 identities differ for ω -categorical structures with less than double exponential orbit growth, thereby resolving one of the main open problems in the algebraic theory of such structures.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785883"}, {"primary_key": "3085527", "vector": [], "sparse_vector": [], "title": "MSO+∇ is undecidable.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper is about an extension of monadic second-order logic over the full binary tree, which has a quantifier saying \"almost surely a branch π ∈ {0,1} ω satisfies a formula φ(π)\", This logic was introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>; we call it MSO+∇ following notation of <PERSON><PERSON> and <PERSON>. The logic Mso+∇ subsumes many qualitative probabilistic formalisms, including qualitative probabilistic check, probabilistic LTL, or parity tree automata with probabilistic acceptance conditions. We show that it is undecidable to check if a given sentence of MSO+∇ is true in the full binary tree 11 Independently and in parallel another proof of this result was given employing different techniques in [3]..", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785892"}, {"primary_key": "3085528", "vector": [], "sparse_vector": [], "title": "Graphical Affine Algebra.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graphical linear algebra is a diagrammatic language allowing to reason compositionally about different types of linear computing devices. In this paper, we extend this formalism with a connector for affine behaviour. The extension, which we call graphical affine algebra, is simple but remarkably powerful: it can model systems with richer patterns of behaviour such as mutual exclusion-with modules over the natural numbers as semantic domain-or non-passive electrical components-when considering modules over a certain field. Our main technical contribution is a complete axiomatisation for graphical affine algebra over these two interpretations. We also show, as case studies, how graphical affine algebra captures electrical circuits and the calculus of stateless connectors-a coordination language for distributed systems.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785877"}, {"primary_key": "3085529", "vector": [], "sparse_vector": [], "title": "The Theory of Traces for Systems with Nondeterminism and Probability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper studies trace-based equivalences for systems combining nondeterministic and probabilistic choices. We show how trace semantics for such processes can be recovered by instantiating a coalgebraic construction known as the generalised powerset construction. We characterise and compare the resulting semantics to known definitions of trace equivalences appearing in the literature. Most of our results are based on the exciting interplay between monads and their presentations via algebraic theories.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785673"}, {"primary_key": "3085530", "vector": [], "sparse_vector": [], "title": "A short story of the CSP dichotomy conjecture.", "authors": ["<PERSON>"], "summary": "It has been observed long time ago that `natural' computational problems tend to be complete in `natural' complexity classes such as NL, P, NP, or PSPACE. Although <PERSON><PERSON><PERSON> in 1975 proved that if P ≠ NP then there are infinitely many complexity classes between them, all the examples of such intermediate problems are based on diagonalization constructions and are very artificial. Since the seminal work by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [8] this phenomenon is known as complexity dichotomy (for P and NP), see also <PERSON><PERSON>'s work [14] in the context of counting problems. Concerted efforts have been made to make this observation more precise, and since the concept of a `natural' problem is somewhat ambiguous, a possible research direction is to pursue dichotomy results for wide classes of problems. The Constraint Satisfaction problem (CSP) is one of such classes.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785678"}, {"primary_key": "3085531", "vector": [], "sparse_vector": [], "title": "Point-width and Max-CSPs.", "authors": ["Clément Carbonnel", "<PERSON>", "<PERSON><PERSON>"], "summary": "The complexity of (unbounded-arity) Max-CSPs under structural restrictions is poorly understood. The two most general hypergraph properties known to ensure tractability of Max-CSPs, $β$-acyclicity and bounded (incidence) MIM-width, are incomparable and lead to very different algorithms. We introduce the framework of point decompositions for hypergraphs and use it to derive a new sufficient condition for the tractability of (structurally restricted) Max-CSPs, which generalises both bounded MIM-width and \\b{eta}-acyclicity. On the way, we give a new characterisation of bounded MIM-width and discuss other hypergraph properties which are relevant to the complexity of Max-CSPs, such as $β$-hypertreewidth.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785660"}, {"primary_key": "3085532", "vector": [], "sparse_vector": [], "title": "Graph Planning with Expected Finite Horizon.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Graph planning gives rise to fundamental algorithmic questions such as shortest path, traveling salesman problem, etc. A classical problem in discrete planning is to consider a weighted graph and construct a path that maximizes the sum of weights for a given time horizon T. However, in many scenarios, the time horizon is not fixed, but the stopping time is chosen according to some distribution such that the expected stopping time is T. If the stopping time distribution is not known, then to ensure robustness, the distribution is chosen by an adversary, to represent the worst-case scenario. A stationary plan for every vertex always chooses the same outgoing edge. For fixed horizon or fixed stopping-time distribution, stationary plans are not sufficient for optimality. Quite surprisingly we show that when an adversary chooses the stopping-time distribution with expected stopping time T, then stationary plans are sufficient. While computing optimal stationary plans for fixed horizon is NP-complete, we show that computing optimal stationary plans under adversarial stopping-time distribution can be achieved in polynomial time. Consequently, our polynomial-time algorithm for adversarial stopping time also computes an optimal plan among all possible plans.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785706"}, {"primary_key": "3085533", "vector": [], "sparse_vector": [], "title": "The Hierarchy of Hyperlogics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hyperproperties, which generalize trace properties by relating multiple traces, are widely studied in information-flow security. Recently, a number of logics for hyperproperties have been proposed, and there is a need to understand their decidability and relative expressiveness. The new logics have been obtained from standard logics with two principal extensions: temporal logics, like LTL and CTL*, have been generalized to hyperproperties by adding variables for traces or paths. First-order and second-order logics, like monadic first-order logic of order and MSO, have been extended with the equal-level predicate. We study the impact of the two extensions across the spectrum of linear-time and branching-time logics, in particular for logics with quantification over propositions. The resulting hierarchy of hyperlogics differs significantly from the classical hierarchy, suggesting that the equal-level predicate adds more expressiveness than trace and path variables. Within the hierarchy of hyperlogics, we identify new boundaries on the decidability of the satisfiability problem. Specifically, we show that while HyperQPTL and HyperCTL* are both undecidable in general, formulas within their ∃ * ∀ * fragments are decidable.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785713"}, {"primary_key": "3085534", "vector": [], "sparse_vector": [], "title": "The convex hull of finitely generable subsets and its predicate transformer.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the domain of non-empty convex and compact subsets of a finite dimensional Euclidean space to represent partial or imprecise points in computational geometry. The convex hull map on such imprecise points is given domain-theoretically by an inner and an outer convex hull. We provide a practical algorithm to compute the inner convex hull when there are a finite number of convex polytopes as partial points. A notion of pre-inner support function is introduced, whose convex hull gives the support function of the inner convex hull in a general setting. We then show that the convex hull map is Scott continuous and can be extended to finitely generable subsets, represented by the Plotkin power domain of the underlying domain. This in particular allows us to compute, for the first time, the convex hull of attractors of iterated function systems in fractal geometry. Finally, we derive a program logic for the convex hull map in the sense of the weakest pre-condition for a given post-condition and show that the convex hull predicate transformer is computable.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785680"}, {"primary_key": "3085535", "vector": [], "sparse_vector": [], "title": "Realizability in the Unitary Sphere.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper we present a semantics for a linear algebraic lambda-calculus based on realizability. This semantics characterizes a notion of unitarity in the system, answering a long standing issue. We derive from the semantics a set of typing rules for a simply-typed linear algebraic lambda-calculus, and show how it extends both to classical and quantum lambda-calculi.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785834"}, {"primary_key": "3085536", "vector": [], "sparse_vector": [], "title": "History-Dependent Nominal μ-Calculus.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The μ -calculus with atoms, or nominal μ -calculus, is a temporal logic for reasoning about transition systems that operate on data atoms coming from an infinite domain and comparable only for equality. It is, however, not expressive enough to define some properties that are of interest from the perspective of system verification. To rectify this, we extend the calculus with tests for atom freshness with respect to the global history of transitions. Since global histories can grow arbitrarily large, it is not clear whether model checking for the extended calculus is decidable. We prove that it is, by showing that one can restrict attention only to locally relevant parts of the history.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785736"}, {"primary_key": "3085537", "vector": [], "sparse_vector": [], "title": "Completeness for Game Logic.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Game logic was introduced by <PERSON><PERSON><PERSON> in the 1980s as a generalisation of propositional dynamic logic (PDL) for reasoning about outcomes that players can force in determined 2-player games. Semantically, the generalisation from programs to games is mirrored by moving from Kripke models to monotone neighbourhood models. <PERSON><PERSON><PERSON> proposed a natural PDL-style Hilbert system which was easily proved to be sound, but its completeness has thus far remained an open problem. In this paper, we introduce a cut-free sequent calculus for game logic, and two cut-free sequent calculi that manipulate annotated formulas, one for game logic and one for the monotone μ -calculus, the variant of the polymodal μ -calculus where the semantics is given by monotone neighbourhood models instead of <PERSON>rip<PERSON> structures. We show these systems are sound and complete, and that completeness of <PERSON><PERSON><PERSON>'s axiomatization follows. Our approach builds on recent ideas and results by <PERSON><PERSON><PERSON> & <PERSON> (LICS 2017) in that we obtain completeness via a sequence of proof transformations between the systems. A crucial ingredient is a validity-preserving translation from game logic to the monotone μ -calculus.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785676"}, {"primary_key": "3085538", "vector": [], "sparse_vector": [], "title": "Lambda Calculus and Probabilistic Computation.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce two extensions of the λ -calculus with a probabilistic choice operator, Λ ⊕ cbv and Λ ⊕ cbn , modeling respectively call-by-value and call-by-name probabilistic computation. We prove that both enjoys confluence and standardization, in an extended way: we revisit these two fundamental notions to take into account the asymptotic behaviour of terms. The common root of the two calculi is a further calculus based on Linear Logic, Λ ⊕ ! , which allows us to develop a unified, modular approach.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785699"}, {"primary_key": "3085539", "vector": [], "sparse_vector": [], "title": "A type theory for cartesian closed bicategories (Extended Abstract).", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We construct an internal language for cartesian closed bicategories. Precisely, we introduce a type theory modelling the structure of a cartesian closed bicategory and show that its syntactic model satisfies an appropriate universal property, thereby lifting the <PERSON><PERSON><PERSON> correspondence to the bicategorical setting. Our approach is principled and practical. Weak substitution structure is constructed using a bicategori-fication of the notion of abstract clone from universal algebra, and the rules for products and exponentials are synthesised from semantic considerations. The result is a type theory that employs a novel combination of 2-dimensional type theory and explicit substitution, and directly generalises the Simply-Typed Lambda Calculus. This work is the first step in a programme aimed at proving coherence for cartesian closed bicategories.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785708"}, {"primary_key": "3085540", "vector": [], "sparse_vector": [], "title": "Backprop as Functor: A compositional perspective on supervised learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A supervised learning algorithm searches over a set of functions $A\\rightarrow B$ parametrised by a space $P$ to find the best approximation to some ideal function $f:A\\rightarrow B$ . It does this by taking examples $(a, f(a))\\in A\\times B$ , and updating the parameter according to some rule. We define a category where these update rules may be composed, and show that gradient descent-with respect to a fixed step size and an error function satisfying a certain property-defines a monoidal functor from a category of parametrised functions to this category of update rules. A key contribution is the notion of request function. This provides a structural perspective on backpropagation, giving a broad generalisation of neural networks and linking it with structures from bidirectional programming and open games.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785665"}, {"primary_key": "3085541", "vector": [], "sparse_vector": [], "title": "Describing free $\\omega$ -categories.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The notion of pasting diagram is central in the study of strict ω -categories: it encodes a collection of morphisms for which the composition is defined unambiguously. As such, we expect that a pasting diagram itself describes an ω-category which is freely generated by the cells constituting it. In practice, it seems very difficult to characterize this notion in full generality and various definitions have been proposed with the aim of being reasonably easy to compute with, and including common examples (e.g. cubes or orientals). One of the most tractable such structure is parity complexes, which uses sets of cells in order to represent the boundaries of a cell. In this work, we first show that parity complexes do not satisfy the aforementioned freeness property by providing a mechanized proof in Agda. Then, we propose a new formalism that satisfies the freeness property and which can be seen as a corrected version of parity complexes.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785687"}, {"primary_key": "3085542", "vector": [], "sparse_vector": [], "title": "A Probabilistic and Non-Deterministic Call-by-Push-Value Language.", "authors": ["<PERSON>"], "summary": "There is no known way of giving a domain-theoretic semantics to higher-order probabilistic languages, in such a way that the involved domains are continuous or quasi-continuous. We argue that the problem naturally disappears for languages with two kinds of types, where one kind is interpreted in a Cartesian-closed category of continuous dcpos, and the other is interpreted in a category that is closed under the probabilistic powerdomain functor. Such a setting is provided by <PERSON>'s call-by-push-value paradigm. Following this insight, we define a call-by-push-value language, with probabilistic choice sitting inside the value types, and where conversion from a value type to a computation type involves demonic non-determinism. We give both a domain-theoretic semantics and an operational semantics for the resulting language, and we show that they are sound and adequate. With the addition of statistical termination testers and parallel if, we show that the language is even fully abstract-and those two primitives are required for that.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785809"}, {"primary_key": "3085543", "vector": [], "sparse_vector": [], "title": "Descriptive complexity for minimal time of cellular automata.", "authors": ["<PERSON><PERSON><PERSON>", "T<PERSON>éo Grente"], "summary": "Descriptive complexity may be useful to design programs in a natural declarative way. This is important for parallel computation models such as cellular automata, because designing parallel programs is considered difficult. Our paper establishes logical characterizations of the three classical complexity classes that model minimal time, called real-time, of one-dimensional cellular automata according to their canonical variants. Our logics are natural restrictions of the existential second-order Horn logic. They correspond to the three ways of deciding a language on a square grid circuit of side n according to the three canonical placements of an input word of length n on the grid. Our key tool is a normalization method that transforms a formula into an equivalent formula that literally mimics a grid circuit.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785666"}, {"primary_key": "3085544", "vector": [], "sparse_vector": [], "title": "Canonisation and Definability for Graphs of Bounded Rank Width.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We prove that the combinatorial <PERSON><PERSON><PERSON><PERSON><PERSON> algorithm of dimension (3k+4) is a complete isomorphism test for the class of all graphs of rank width at most k. Rank width is a graph invariant that, similarly to tree width, measures the width of a certain style of hierarchical decomposition of graphs; it is equivalent to clique width. It was known that isomorphism of graphs of rank width k is decidable in polynomial time (<PERSON><PERSON><PERSON> and <PERSON>, FOCS 2015), but the best previously known algorithm has a running time n f(k) for a non-elementary function f. Our result yields an isomorphism test for graphs of rank width k running in time n O(k) . Another consequence of our result is the first polynomial time canonisation algorithm for graphs of bounded rank width. Our second main result is that fixed-point logic with counting captures polynomial time on all graph classes of bounded rank width.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785682"}, {"primary_key": "3085545", "vector": [], "sparse_vector": [], "title": "On the Existential Theories of <PERSON><PERSON><PERSON>met<PERSON> and Linear p-adic Fields.", "authors": ["Florent <PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the complexity of the satisfiability problems for the existential fragment of Büchi arithmetic and for the existential fragment of linear arithmetic over p-adic fields. Our main results are that both problems are NP-complete. The NP upper bound for existential linear arithmetic over p-adic fields resolves an open question posed by <PERSON><PERSON><PERSON><PERSON> [<PERSON><PERSON>b. Comput., 5(1/2) (1988)] and holds despite the fact that satisfying assignments in both theories may have bit-size super-polynomial in the description of the formula. A key technical contribution is to show that the existence of a path between two states of a finite-state automaton whose language encodes the set of solutions of a given system of linear Diophantine equations can be witnessed in NP.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785681"}, {"primary_key": "3085546", "vector": [], "sparse_vector": [], "title": "Presburger arithmetic with stars, rational subsets of graph groups, and nested zero tests.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study the computational complexity of existential Presburger arithmetic with (possibly nested occurrences of) a Kleene-star operator. In addition to being a natural extension of Presburger arithmetic, our investigation is motivated by two other decision problems. The first problem is the rational subset membership problem in graph groups. A graph group is an infinite group specified by a finite undirected graph. While a characterisation of graph groups with a decidable rational subset membership problem was given by <PERSON><PERSON><PERSON> and <PERSON> [J. Al<PERSON>, 320(2) (2008)], it has been an open problem (i) whether the decidable fragment has elementary complexity and (ii) what is the complexity for each fixed graph group. The second problem is the reachability problem for integer vector addition systems with states and nested zero tests. We prove that the satisfiability problem for existential Pres-burger arithmetic with stars is NEXP-complete and that all three problems are polynomially inter-reducible. Moreover, we consider for each problem a variant with a fixed parameter: We fix the star-height in the logic, a graph parameter for the membership problem, and the number of distinct zero-tests in the integer vector addition systems. We establish NP-completeness of all problems with fixed parameters. In particular, this enables us to obtain a complete description of the complexity landscape of the rational subset membership problem for fixed graph groups: If the graph is a clique, the problem is N L-complete. If the graph is a disjoint union of cliques, it is P-complete. If it is a transitive forest (and not a union of cliques), the problem is NP-complete. Otherwise, the problem is undecidable.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785850"}, {"primary_key": "3085547", "vector": [], "sparse_vector": [], "title": "Intuitionistic proofs without syntax.", "authors": ["Willem B. <PERSON>", "<PERSON>", "Lutz Straßburger"], "summary": "We present Intuitionistic Combinatorial Proofs (ICPs), a concrete geometric semantics of intuitionistic logic based on the principles of the second author's classical Combinatorial Proofs. An ICP naturally factorizes into a linear fragment, a graphical abstraction of an IMLL proof net (an arena net), and a parallel contraction-weakening fragment (a skew.fibration). ICPs relate to game semantics, and can be seen as a strategy in a Hyland-Ong arena, generalized from a tree-like to a dag-like strategy. Our first main result, Polynomial Full Completeness, is that ICPs as a semantics are complexity-aware: the translations to and from sequent calculus are size-preserving (up to a polynomial). By contrast, lambda-calculus and game semantics incur an exponential blowup. Our second main result, Local Canonicity, is that ICPs abstract fully and faithfully over the non-duplicating permutations of the sequent calculus, analogously to the first and second authors' recent result for MALL.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785827"}, {"primary_key": "3085548", "vector": [], "sparse_vector": [], "title": "Quantum channels as a categorical completion.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a categorical foundation for the connection between pure and mixed states in quantum information and quantum computation. The foundation is based on distributive monoidal categories. First, we prove that the category of all quantum channels is a canonical completion of the category of pure quantum operations (with ancilla preparations). More precisely, we prove that the category of completely positive trace-preserving maps between finite-dimensional C*-algebras is a canonical completion of the category of finite-dimensional vector spaces and isometries. Second, we extend our result to give a foundation to the topological relationships between quantum channels. We do this by generalizing our categorical foundation to the topologically-enriched setting. In particular, we show that the operator norm topology on quantum channels is the canonical topology induced by the norm topology on isometries.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785700"}, {"primary_key": "3085549", "vector": [], "sparse_vector": [], "title": "Bisimulation Equivalence of First-Order Grammars is ACKERMANN-Complete.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Checking whether two pushdown automata with restricted silent actions are weakly bisimilar was shown decidable by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (1998, 2005). We provide the first known complexity upper bound for this famous problem, in the equivalent setting of first-order grammars. This ACKERMANN upper bound is optimal, and we also show that strong bisimilarity is primitive-recursive when the number of states of the automata is fixed.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785848"}, {"primary_key": "3085550", "vector": [], "sparse_vector": [], "title": "A Generic Normal Form for ZX-Diagrams and Application to the Rational Angle Completeness.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent completeness results on the ZX-calculus used a third-party language, namely the ZW-Calculus. As a consequence, these proofs are elegant, but sadly non-constructive. We address this issue in the following. To do so, we first describe a generic normal form for ZX-diagrams in any fragment that contains Clifford+T quantum mechanics. We give sufficient conditions for an axiomatisation to be complete, and an algorithm to reach the normal form. Finally, we apply these results to the Clifford+T fragment and the general ZX-calculus - for which we already know the completeness-, but also for any fragment of rational angles: we show that the axiomatisation for Clifford+T is also complete for any fragment of dyadic angles, and that a simple new rule (called cancellation) is necessary and sufficient otherwise.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785754"}, {"primary_key": "3085551", "vector": [], "sparse_vector": [], "title": "Higher-Kinded Data Types: Syntax and Semantics.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present a grammar for a robust class of data types that includes algebraic data types (ADTs), (truly) nested types, generalized algebraic data types (GADTs), and their higher-kinded analogues. All of the data types our grammar defines, as well as their associated type constructors, are shown to have fully functorial initial algebra semantics in locally presentable categories. Since local presentability is a modest hypothesis, needed for such semantics for even the simplest ADTs, our semantic framework is actually quite conservative. Our results thus provide evidence that if a category supports fully functorial initial algebra semantics for standard ADTs, then it does so for advanced higher-kinded data types as well. To give our semantics we introduce a new type former called Lan. that captures on the syntactic level the categorical notion of a left Kan extension. We show how left Kan extensions capture propagation of a data type's syntactic generators across the entire universe of types, via a certain completion procedure, so that the type constructor associated with a data type becomes a bonafide functor with a canonical action on morphisms. A by-product of our semantics is a precise measure of the semantic complexity of data types, given by the least cardinal λ for which the functor underlying a data type is λ-accessible. The proof of our main result allows this cardinal to be read off from a data type definition without much effort. It also gives a sufficient condition for a data type to have semantic complexity ω, thus characterizing those data types whose data elements are effectively enumerable.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785657"}, {"primary_key": "3085552", "vector": [], "sparse_vector": [], "title": "Model Comparison Games for Horn Description Logics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Horn description logics are syntactically defined fragments of standard description logics that fall within the Horn fragment of first-order logic and for which ontology-mediated query answering is in PTime for data complexity. They were independently introduced in modal logic to capture the intersection of Horn first-order logic with modal logic. In this paper, we introduce model comparison games for the basic Horn description logic hornALC (corresponding to the basic Horn modal logic) and use them to obtain an Ehrenfeucht-Fraïssé type definability result and a van <PERSON> style expressive completeness result for hornALC. We also establish a finite model theory version of the latter. The Ehrenfeucht-Fraïssé type definability result is used to show that checking hornALC indistinguishability of models is ExpTime-complete, which is in sharp contrast to ALC indistinguishability (i.e., bisimulation equivalence) checkable in PTime. In addition, we explore the behavior of Horn fragments of more expressive description and modal logics by defining a Horn guarded fragment of first-order logic and introducing model comparison games for it.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785658"}, {"primary_key": "3085553", "vector": [], "sparse_vector": [], "title": "On the Termination Problem for Probabilistic Higher-Order Recursive Programs.", "authors": ["<PERSON><PERSON>", "Ugo <PERSON>", "<PERSON>"], "summary": "In the last two decades, there has been much progress on model checking of both probabilistic systems and higher-order programs. In spite of the emergence of higher-order probabilistic programming languages, not much has been done to combine those two approaches. In this paper, we initiate a study on the probabilistic higher-order model checking problem, by giving some first theoretical and experimental results. As a first step towards our goal, we introduce PHORS, a probabilistic extension of higher-order recursion schemes (HORS), as a model of probabilistic higher-order programs. The model of PHORS may alternatively be viewed as a higher-order extension of recursive Markov chains. We then investigate the probabilistic termination problem -- or, equivalently, the probabilistic reachability problem. We prove that almost sure termination of order-2 PHORS is undecidable. We also provide a fixpoint characterization of the termination probability of PHORS, and develop a sound (but possibly incomplete) procedure for approximately computing the termination probability. We have implemented the procedure for order-2 PHORSs, and confirmed that the procedure works well through preliminary experiments that are reported at the end of the article.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785679"}, {"primary_key": "3085554", "vector": [], "sparse_vector": [], "title": "Codensity Games for Bisimilarity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bisimilarity as an equivalence notion of systems has been central to process theory. Due to the recent rise of interest in quantitative systems (probabilistic, weighted, hybrid, etc.), bisimilarity has been extended in various ways: notably, bisimulation metric between probabilistic systems. An important feature of bisimilarity is its game-theoretic characterization, where <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>tor play against each other; extension of bisimilarity games to quantitative settings has been actively pursued too. In this paper, we present a general framework that uniformly describes game characterizations of bisimilarity-like notions. Our framework is formalized categorically using fibrations and coalgebras. In particular, our characterization of bisimilarity in terms of fibrational predicate transformers allows us to derive codensity bisimilarity games: a general categorical game characterization of bisimilarity. Our framework covers known bisimilarity-like notions (such as bisimulation metric) as well as new ones (including what we call bisimulation topology).", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785691"}, {"primary_key": "3085555", "vector": [], "sparse_vector": [], "title": "Path Spaces of Higher Inductive Types in Homotopy Type Theory.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The study of equality types is central to homotopy type theory. Characterizing these types is often tricky, and various strategies, such as the encode-decode method, have been developed. We prove a theorem about equality types of coequalizers and pushouts, reminiscent of an induction principle and without any restrictions on the truncation levels. This result makes it possible to reason directly about certain equality types and to streamline existing proofs by eliminating the necessity of auxiliary constructions. To demonstrate this, we give a very short argument for the calculation of the fundamental group of the circle (<PERSON><PERSON><PERSON> and <PERSON><PERSON> [1]), and for the fact that pushouts preserve embeddings. Further, our development suggests a higher version of the <PERSON><PERSON><PERSON><PERSON><PERSON> theorem, and the set-truncation operator maps it to the standard <PERSON><PERSON><PERSON><PERSON><PERSON> theorem (due to <PERSON><PERSON><PERSON> and <PERSON><PERSON> [2]). We provide a formalization of the main technical results in the proof assistant <PERSON><PERSON>.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785661"}, {"primary_key": "3085556", "vector": [], "sparse_vector": [], "title": "Perspective Games.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce and study perspective games, which model multi-agent systems in which agents can view only the parts of the system that they own. As in standard multi-player turn-based games, the vertices of the game graph are partitioned among the players. Starting from an initial vertex, the players jointly generate a computation, with each player deciding the successor vertex whenever the generated computation reaches a vertex she owns. A perspective strategy for a player depends only on the history of visits in her vertices. Thus, unlike observation-based models of partial visibility, where uncertainty is longitudinal - players partially observe all vertices in the history, uncertainty in the perspective model is transverse - players fully observe part of the vertices in the history. Perspective games are not determined, and we study the problem of deciding whether a player has a winning perspective strategy. In the pure setting, we show that the problem is EXPTIME-complete for objectives given by a deterministic or universal parity automaton over an alphabet that labels the vertices of the game, and is 2EXPTIME-complete for LTL objectives. Accordingly, so is the model-checking complexity of Perspective-ATL*- an extension of ATL* with path quantification that captures perspective strategies. In all cases, the complexity in the size of the graph is polynomial - exponentially easier than games with observation-based partial visibility. In the probabilistic setting, we show that deciding whether a player has an almost-winning randomized perspective strategy is undecidable. Finally, we study perspective games with objectives given by ω-regular conditions over the set of vertices. In particular, we compare the power of perspective and memoryless strategies and show, for example, that while the generalized-Büchi and the Streett objectives do not admit memoryless strategies, generalized Büehl admits perspective strategies, and Streett does not. We also describe a fragment of LTL that admits perspective strategies.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785662"}, {"primary_key": "3085557", "vector": [], "sparse_vector": [], "title": "The Logic of Action Lattices is Undecidable.", "authors": ["<PERSON><PERSON>"], "summary": "We prove algorithmic undecidability of the (in)equational theory of residuated Kleene lattices (action lattices), thus solving a problem left open by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785659"}, {"primary_key": "3085558", "vector": [], "sparse_vector": [], "title": "The Geometry of Bayesian Programming.", "authors": ["Ugo <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a geometry of interaction model for a typed λ -calculus endowed with operators for sampling from a continuous uniform distribution and soft conditioning, namely a paradigmatic calculus for higher-order Bayesian programming. The model is based on the category of measurable spaces and partial measurable functions, and is proved adequate with respect to both a distribution-based and a sampling-based operational semantics.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785663"}, {"primary_key": "3085559", "vector": [], "sparse_vector": [], "title": "Reachability in Vector Addition Systems is Primitive-Recursive in Fixed Dimension.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The reachability problem in vector addition systems is a central question, not only for the static verification of these systems, but also for many inter-reducible decision problems occurring in various fields. The currently best known upper bound on this problem is not primitive-recursive, even when considering systems of fixed dimension. We provide significant refinements to the classical decomposition algorithm of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> and to its termination proof, which yield an ACKERMANN upper bound in the general case, and primitive-recursive upper bounds in fixed dimension. While this does not match the currently best known TOWER lower bound for reachability, it is optimal for related problems.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785796"}, {"primary_key": "3085560", "vector": [], "sparse_vector": [], "title": "Walk refinement, walk logic, and the iteration number of the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> algorithm.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We show that the 2-dimensional <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> algorithm stabilizes n-vertex graphs after at most O(n log n) iterations. This implies that if such graphs are distinguishable in 3-variable first order logic with counting, then they can also be distinguished in this logic by a formula of quantifier depth at most O(n log n). For this we exploit a new refinement based on counting walks and argue that its iteration number differs from the classic <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> refinement by at most a logarithmic factor. We then prove matching linear upper and lower bounds on the number of iterations of the walk refinement. This is achieved with an algebraic approach by exploiting properties of semisimple matrix algebras. We also define a walk logic and a bijective walk pebble game that precisely correspond to the new walk refinement.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785694"}, {"primary_key": "3085561", "vector": [], "sparse_vector": [], "title": "Template games and differential linear logic.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We extend our recent template game model of multiplicative additive linear logic (MALL) with an exponential modality of linear logic (LL) derived from the standard categorical construction Sym of the free symmetric monoidal category. We obtain in this way the first game semantics of differential linear logic (DiLL) in its classical form. The construction of the model relies on a careful and healthy comparison with the model of generalised species designed ten years ago by <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>. Besides the resolution of an old open problem of game semantics, the study reveals an unexpected and promising convergence between linear logic and homotopy theory.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785830"}, {"primary_key": "3085562", "vector": [], "sparse_vector": [], "title": "HoCHC: A Refutationally Complete and Semantically Invariant System of Higher-order Logic Modulo Theories.", "authors": ["C.<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a simple resolution proof system for higher-order constrained Horn clauses (HoCHC) - a system of higher-order logic modulo theories - and prove its soundness and refutational completeness w.r.t. the standard semantics. As corollaries, we obtain the compactness theorem and semi-decidability of HoCHC for semi-decidable background theories, and we prove that HoCHC satisfies a canonical model property. Moreover a variant of the well-known translation from higher-order to 1st-order logic is shown to be sound and complete for HoCHC in standard semantics. We illustrate how to transfer decidability results for (fragments of) 1st-order logic modulo theories to our higher-order setting, using as example the <PERSON><PERSON>-<PERSON><PERSON>-<PERSON> fragment of HoCHC modulo a restricted form of Linear Integer Arithmetic.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785784"}, {"primary_key": "3085563", "vector": [], "sparse_vector": [], "title": "A Type Theory for Defining Logics and Proofs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We describe a Martin-Lof-style dependent type theory, called Cocon, that allows us to mix the intensional function space that is used to represent higher-order abstract syntax (HOAS) trees with the extensional function space that describes (recursive) computations. We mediate between HOAS representations and computations using contextual modal types. Our type theory also supports an infinite hierarchy of universes and hence supports type-level computation thereby providing metaprogramming and (small-scale) reflection. Our main contribution is the development of a Kripke-style model for Cocon that allows us to prove normalization. From the normalization proof, we derive subject reduction and consistency. Our work lays the foundation to incorporate the methodology of logical frameworks into systems such as Agda and bridges the longstanding gap between these two worlds.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785683"}, {"primary_key": "3085564", "vector": [], "sparse_vector": [], "title": "Categorical Semantics for Time Travel.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a general categorical framework to reason about quantum theory and other process theories living in spacetimes where chronology violation can occur-e.g. in the form of Closed Timelike Curves (CTCs)-allowing resources to travel back in time and provide computational speedups. Our framework is based on a weakening of the definition of traced symmetric monoidal categories, obtained by dropping the yanking axiom and the requirement that the trace be defined on all morphisms. We show that the two leading models for quantum theory with closed timelike curves-namely the P-CTC model of <PERSON> et al. and the D-CTC model of Deutsch-are captured by our framework, and in doing so we provide the first compositional description of the D-CTC model. Our description of the D-CTC model results in a process theory which respects the constraints of relativistic causality: this is in direct contrast to the P-CTC model, where CTCs are implemented by a trace and allow post-selection to be performed deterministically.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785664"}, {"primary_key": "3085565", "vector": [], "sparse_vector": [], "title": "Separation and covering for group based concatenation hierarchies.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Concatenation hierarchies are natural classifications of regular languages. All such hierarchies are built through the same construction process: one starts from an initial, specific class of languages (the basis) and builds new levels using two generic operations. Concatenation hierarchies have gathered a lot of interest since the early 70s, notably thanks to an alternate logical definition: each concatenation hierarchy can be defined as the quantification alternation hierarchy within a variant of first-order logic over words (while the hierarchies differ by their bases, the variants differ by their set of available predicates). Our goal is to understand these hierarchies. A typical approach is to look at two decision problems: membership and separation. In the paper we are interested in the latter, which is more general. For a class of languages C, C-separation takes two regular languages as input and asks whether there exists a third one in C including the first one and disjoint from the second one. Settling whether separation is decidable for the levels within a given concatenation hierarchy is among the most fundamental and challenging questions in formal language theory. In all prominent cases, it is open, or answered positively for low levels only. Recently, a breakthrough was made using a generic approach for a specific kind of hierarchies: those with a finite basis. In this case. separation is always decidable for levels 1/2. 1 and 3/2. Our main theorem is similar but independent: we consider hierarchies with possibly infinite bases, but that contain only group languages. An example is the group hierarchy introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON>: its basis consists of all group languages. Another example is the quantifier alternation hierarchy of first-order logic with modular predicates FO( <;, MOD): its basis consists of the languages that count the length of words modulo some number. Using a generic approach, we show that for any such hierarchy, if separation is decidable for the basis, then it is decidable as well for levels 1/2, 1 and 3/2 (we actually solve a more general problem called covering). This complements the aforementioned result nicely: all bases considered in the literature are either finite or made of group languages. Thus, one may handle the lower levels of any prominent hierarchy in a generic way.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785655"}, {"primary_key": "3085566", "vector": [], "sparse_vector": [], "title": "High-level methods for homotopy construction in associative n-categories.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A combinatorial theory of associative n-categories has recently been proposed, with strictly associative and unital composition in all dimensions, and the weak structure arising as a notion of `homotopy' with a natural geometrical interpretation. Such a theory has the potential to serve as an attractive foundation for a computer proof assistant for higher category theory, since it allows composites to be uniquely described, and relieves proofs from the bureaucracy of associators, unitors and their coherence. However, this basic theory lacks a high-level way to construct homotopies, which would be intractable to build directly in complex situations; it is not therefore immediately amenable to implementation. We tackle this problem by describing a `contraction' operation, which algorithmically constructs complex homotopies that reduce the lengths of composite terms. This contraction procedure allows building of nontrivial proofs by repeatedly contracting subterms, and also allows the contraction of those proofs themselves, yielding in some cases single-step witnesses for complex homotopies. We prove correctness of this procedure by showing that it lifts connected colimits from a base category to a category of zigzags, a procedure which is then iterated to yield a contraction mechanism in any dimension. We also present homotopy.io, an online proof assistant that implements the theory of associative n-categories, and use it to construct a range of examples that illustrate this new contraction mechanism.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785895"}, {"primary_key": "3085567", "vector": [], "sparse_vector": [], "title": "Approximate Span Liftings: Compositional Semantics for Relaxations of Differential Privacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We develop new abstractions for reasoning about three relaxations of differential privacy: Rényi differential privacy, zero-concentrated differential privacy, and truncated concentrated differential privacy, which express bounds on statistical divergences between two output probability distributions. In order to reason about such properties compositionally, we introduce approximate span-lifting, a novel construction extending the approximate relational lifting approaches previously developed for standard differential privacy to a more general class of divergences, and also to continuous distributions. As an application, we develop a program logic based on approximate span-liftings capable of proving relaxations of differential privacy and other statistical divergence properties.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785668"}, {"primary_key": "3085568", "vector": [], "sparse_vector": [], "title": "Local normal forms and their use in algorithmic meta theorems (Invited Talk).", "authors": ["<PERSON>"], "summary": "This invited talk provides a personal perspective of recent developments concerning local normal forms and their use in algorithmic meta theorems.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785748"}, {"primary_key": "3085569", "vector": [], "sparse_vector": [], "title": "Differentiable Causal Computations via Delayed Trace.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate causal computations, which take sequences of inputs to sequences of outputs such that the nth output depends on the first n inputs only. We model these in category theory via a construction taking a Cartesian category \\mathbbC to another category St(\\mathbbC) with a novel trace-like operation called \"delayed trace\", which misses yanking and dinaturality axioms of the usual trace. The delayed trace operation provides a feedback mechanism in St(\\mathbbC) with an implicit guardedness guarantee. When \\mathbbC is equipped with a Cartesian differential operator, we construct a differential operator for St (\\mathbbC) using an abstract version of backpropagation through time, a technique from machine learning based on unrolling of functions. This obtains a swath of properties for backpropagation through time, including a chain rule and Schwartz theorem. Our differential operator is also able to compute the derivative of a stateful network without requiring the network to be unrolled.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785670"}, {"primary_key": "3085570", "vector": [], "sparse_vector": [], "title": "A Sequent Calculus for Opetopes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Opetopes are algebraic descriptions of shapes corresponding to compositions in higher dimensions. As such, they offer an approach to higher-dimensional algebraic structures, and in particular, to the definition of weak ω-categories, which was the original motivation for their introduction by <PERSON><PERSON> and <PERSON><PERSON>. They are classically defined inductively (as free operads in Leinster's approach, or as zoom complexes in the formalism of <PERSON><PERSON> et al.), using abstract constructions making them difficult to manipulate with a computer. Here, we present a purely syntactic description of opetopes and opetopic sets as a sequent calculus. Our main result is that well-typed opetopes in our sense are in bijection with opetopes as defined in the more traditional approaches. We expect that the resulting structures can serve as natural foundations for mechanized tools based on opetopes.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785667"}, {"primary_key": "3085571", "vector": [], "sparse_vector": [], "title": "Quantum Hoare Logic with Ghost Variables.", "authors": ["<PERSON>"], "summary": "Quantum Hoare logic allows us to reason about quantum programs. We present an extension of quantum Hoare logic that introduces \"ghost variables\" to extend the expressive power of pre-/postconditions. Ghost variables are variables that do not actually occur in the program and are allowed to have arbitrary quantum states (in a sense, they are existentially quantified), and be entangled with program variables. Ghost variables allow us to express properties such as the distribution of a program variable or the fact that a variable has classical content. And as a case study, we show how quantum Hoare logic with ghost variables can be used to prove the security of the quantum one-time pad.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785779"}, {"primary_key": "3085572", "vector": [], "sparse_vector": [], "title": "A Near-Minimal Axiomatisation of ZX-Calculus for Pure Qubit Quantum Mechanics.", "authors": ["<PERSON><PERSON>"], "summary": "Recent developments in the ZX-Calculus have resulted in complete axiomatisations first for an approximately universal restriction of the language, and then for the whole language. The main drawbacks were that the axioms that were added to achieve completeness were numerous, tedious to manipulate and lacking a physical interpretation. We present in this paper two complete axiomatisations for the general ZX-Calculus, that we believe are optimal, in that all their equations are necessary and moreover have a nice physical interpretation. To do so, we introduce the singular-value decomposition of a ZX-diagram, and use it to show that all the rules of the former axiomatisation are provable with the new one.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785765"}, {"primary_key": "3085573", "vector": [], "sparse_vector": [], "title": "Lambda Y-Calculus With Priorities.", "authors": ["<PERSON>"], "summary": "The lambda Y-calculus with priorities is a variant of the simply-typed lambda calculus designed for higher-order model-checking. The higher-order model-checking problem asks if a given parity tree automaton accepts the Böhm tree of a given term of the simply-typed lambda calculus with recursion. We show that this problem can be reduced to the same question but for terms of lambda Y-calculus with priorities and visibly parity automata; a subclass of parity automata. The latter question can be answered by evaluating terms in a simple powerset model with least and greatest fixpoints. We prove that the recognizing power of powerset models and visibly parity automata are the same. So, up to conversion to the lambda Y-calculus with priorities, powerset models with least and greatest fixpoints are indeed the right semantic framework for the model-checking problem. The reduction to lambda Y-calculus with priorities is also efficient algorithmically: it gives an algorithm of the same complexity as direct approaches to the higher-order model-checking problem. This indicates that the task of calculating the value of a term in a powerset model is a central algorithmic problem for higher-order model-checking.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785674"}, {"primary_key": "3085574", "vector": [], "sparse_vector": [], "title": "No-Go Theorems for Distributive Laws.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Monads are commonplace in computer science, and can be composed using <PERSON>'s distributive laws. Unfortunately, finding distributive laws can be extremely difficult and error-prone. The literature contains some principles for constructing distributive laws. However, until now there have been no general techniques for establishing when no such law exists. We present two families of theorems for showing when there can be no distributive law for two monads. The first widely generalizes a counterexample attributed to <PERSON><PERSON><PERSON>. It covers all the previous known no-go results for specific pairs of monads, and includes many new results. The second family is entirely novel, encompassing various new practical situations. For example, it negatively resolves the open question of whether the list monad distributes over itself, and also reveals a previously unobserved error in the literature.", "published": "2019-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2019.8785707"}]