[{"primary_key": "1766359", "vector": [], "sparse_vector": [], "title": "Breaching the 2-approximation barrier for the forest augmentation problem.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The basic goal of survivable network design is to build cheap networks that guarantee the connectivity of certain pairs of nodes despite the failure of a few edges or nodes. A celebrated result by <PERSON> [Combinatorica'01] provides a 2-approximation for a wide class of these problems. However nothing better is known even for very basic special cases, raising the natural question whether any improved approximation factor is possible at all.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520035"}, {"primary_key": "1766360", "vector": [], "sparse_vector": [], "title": "The power of two choices in graphical allocation.", "authors": ["<PERSON><PERSON>", "Ohad N<PERSON>"], "summary": "The graphical balls-into-bins process is a generalization of the classical 2-choice balls-into-bins process, where the bins correspond to vertices of an arbitrary underlying graph G. At each time step an edge of G is chosen uniformly at random, and a ball must be assigned to either of the two endpoints of this edge. The standard 2-choice process corresponds to the case of G=Kn.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519995"}, {"primary_key": "1766361", "vector": [], "sparse_vector": [], "title": "A PTAS for unsplittable flow on a path.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the Unsplittable Flow on a Path problem (UFP) we are given a path with edge capacities, and a set of tasks where each task is characterized by a subpath, a demand, and a weight. The goal is to select a subset of tasks of maximum total weight such that the total demand of the selected tasks using each edge e is at most the capacity of e. The problem admits a QPTAS [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>'06; <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, SODA'15]. After a long sequence of improvements [<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, SODA'09; <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, FOCS'11; <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SODA'14; <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>'18], the best known polynomial time approximation algorithm for UFP has an approximation ratio of 1+1/(e+1) + epsilon < 1.269 [<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>'22]. It has been an open question whether this problem admits a PTAS. In this paper, we solve this open question and present a polynomial time (1 + epsilon)-approximation algorithm for UFP.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519959"}, {"primary_key": "1766362", "vector": [], "sparse_vector": [], "title": "Computing simple mechanisms: Lift-and-round over marginal reduced forms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study revenue maximization in multi-item multi-bidder auctions under the natural item-independence assumption – a classical problem in Multi-Dimensional Bayesian Mechanism Design. One of the biggest challenges in this area is developing algorithms to compute (approximately) optimal mechanisms that are not brute-force in the size of the bidder type space, which is usually exponential in the number of items in multi-item auctions. Unfortunately, such algorithms were only known for basic settings of our problem when bidders have unit-demand or additive valuations.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520029"}, {"primary_key": "1766363", "vector": [], "sparse_vector": [], "title": "Flow time scheduling and prefix Beck-Fiala.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We relate discrepancy theory with the classic scheduling problems of minimizing max flow time and total flow time on unrelated machines. Specifically, we give a general reduction that allows us to transfer discrepancy bounds in the prefix Beck-Fiala (bounded ℓ1-norm) setting to bounds on the flow time of an optimal schedule. Combining our reduction with a deep result proved by <PERSON><PERSON><PERSON><PERSON><PERSON> via convex geometry, give guarantees of O(√logn) and O(√logn logP) for max flow time and total flow time, respectively, improving upon the previous best guarantees of O(logn) and O(logn logP). Apart from the improved guarantees, the reduction motivates seemingly easy versions of prefix discrepancy questions: any constant bound on prefix Beck-Fiala where vectors have sparsity two (sparsity one being trivial) would already yield tight guarantees for both max flow time and total flow time. While known techniques solve this case when the entries take values in {−1,0,1}, we show that they are unlikely to transfer to the more general 2-sparse case of bounded ℓ1-norm.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520077"}, {"primary_key": "1766364", "vector": [], "sparse_vector": [], "title": "Pricing ordered items.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the revenue guarantees and approximability of item pricing. Recent work shows that with n heterogeneous items, item-pricing guarantees an O(logn) approximation to the optimal revenue achievable by any (buy-many) mechanism, even when buyers have arbitrarily combinatorial valuations. However, finding good item prices is challenging – it is known that even under unit-demand valuations, it is NP-hard to find item prices that approximate the revenue of the optimal item pricing better than O(√n).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520065"}, {"primary_key": "1766365", "vector": [], "sparse_vector": [], "title": "Directed flow-augmentation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We show a flow-augmentation algorithm in directed graphs: There exists a randomized polynomial-time algorithm that, given a directed graph G, two integers s,t ∈ V(G), and an integer k, adds (randomly) to G a number of arcs such that for every minimal st-cut Z in G of size at most k, with probability 2−poly(k) the set Z becomes a minimum st-cut in the resulting graph.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520018"}, {"primary_key": "1766366", "vector": [], "sparse_vector": [], "title": "Pseudodeterminism: promises and lowerbounds.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "A probabilistic algorithm A is pseudodeterministic if, on every input, there exists a canonical value that is output with high probability. If the algorithm outputs one of k canonical values with high probability, then it is called a k-pseudodeterministic algorithm. In the study of pseudodeterminism, the Acceptance Probability Estimation Problem (APEP), which is to additively approximate the acceptance probability of a Boolean circuit, is emerging as a central computational problem. This problem admits a 2-pseudodeterministic algorithm. Recently, it was shown that a pseudodeterministic algorithm for this problem would imply that any multi-valued function that admits a k-pseudodeterministic algorithm for a constant k (including approximation algorithms) also admits a pseudodeterministic algorithm (<PERSON>, <PERSON>, <PERSON>; ITCS 2021).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520043"}, {"primary_key": "1766367", "vector": [], "sparse_vector": [], "title": "The power of multiple choices in online stochastic matching.", "authors": ["<PERSON><PERSON><PERSON>", "Xinkai Shu", "Shuyi Yan"], "summary": "We study the power of multiple choices in online stochastic matching. Despite a long line of research, existing algorithms still only consider two choices of offline neighbors for each online vertex because of the technical challenge in analyzing multiple choices. This paper introduces two approaches for designing and analyzing algorithms that use multiple choices. For unweighted and vertex-weighted matching, we adopt the online correlated selection (OCS) technique into the stochastic setting, and improve the competitive ratios to 0.716, from 0.711 and 0.7 respectively. For edge-weighted matching with free disposal, we propose the Top Half Sampling algorithm. We directly characterize the progress of the whole matching instead of individual vertices, through a differential inequality. This improves the competitive ratio to 0.706, breaking the 1−1/e barrier in this setting for the first time in the literature. Finally, for the harder edge-weighted problem without free disposal, we prove that no algorithms can be 0.703 competitive, separating this setting from the aforementioned three.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520046"}, {"primary_key": "1766368", "vector": [], "sparse_vector": [], "title": "Ideals, determinants, and straightening: proving and using lower bounds for polynomial ideals.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We show that any nonzero polynomial in the ideal generated by the r × r minors of an n × n matrix X can be used to efficiently approximate the determinant. Specifically, for any nonzero polynomial f in this ideal, we construct a small depth-three f-oracle circuit that approximates the Θ(r1/3) × Θ(r1/3) determinant in the sense of border complexity. For many classes of algebraic circuits, this implies that every nonzero polynomial in the ideal generated by r × r minors is at least as hard to approximately compute as the Θ(r1/3) × Θ(r1/3) determinant. We also prove an analogous result for the Pfaffian of a 2n × 2n skew-symmetric matrix and the ideal generated by Pfaffians of 2r × 2r principal submatrices.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520025"}, {"primary_key": "1766369", "vector": [], "sparse_vector": [], "title": "Binary perceptron: efficient algorithms can find solutions in a rare well-connected cluster.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "It was recently shown that almost all solutions in the symmetric binary perceptron are isolated, even at low constraint densities, suggesting that finding typical solutions is hard. In contrast, some algorithms have been shown empirically to succeed in finding solutions at low density. This phenomenon has been justified numerically by the existence of subdominant and dense connected regions of solutions, which are accessible by simple learning algorithms. In this paper, we establish formally such a phenomenon for both the symmetric and asymmetric binary perceptrons. We show that at low constraint density (equivalently for overparametrized perceptrons), there exists indeed a subdominant connected cluster of solutions with almost maximal diameter, and that an efficient multiscale majority algorithm can find solutions in such a cluster with high probability, settling in particular an open problem posed by <PERSON><PERSON><PERSON> in STOC'21. In addition, even close to the critical threshold, we show that there exist clusters of linear diameter for the symmetric perceptron, as well as for the asymmetric perceptron under additional assumptions.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519975"}, {"primary_key": "1766370", "vector": [], "sparse_vector": [], "title": "Hardness of approximation in p via short cycle removal: cycle detection, distance oracles, and beyond.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a new technique for efficiently removing almost all short cycles in a graph without unintentionally removing its triangles. Consequently, triangle finding problems do not become easy even in almost k-cycle free graphs, for any constant k≥ 4.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520066"}, {"primary_key": "1766371", "vector": [], "sparse_vector": [], "title": "Deterministic, near-linear ε-approximation algorithm for geometric bipartite matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Given two point sets A and B in ℝd of size n each, for some constant dimension d≥ 1, and a parameter ε>0, we present a deterministic algorithm that computes, in n·(ε−1 logn)O(d) time, a perfect matching between A and B whose cost is within a (1+ε) factor of the optimal matching under any ℓp-norm. Although a Monte-Carlo algorithm with a similar running time is proposed by <PERSON><PERSON><PERSON><PERSON> and <PERSON> [J. ACM 2020], the best-known deterministic ε-approximation algorithm takes Ω(n3/2) time. Our algorithm constructs a (refinement of a) tree cover of ℝd, and we develop several new tools to apply a tree-cover based approach to compute an ε-approximate perfect matching.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519977"}, {"primary_key": "1766372", "vector": [], "sparse_vector": [], "title": "Rate one-third non-malleable codes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "At ITCS 2010, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> introduced Non-malleable Codes (NMCs) which protect against tampering of a codeword of a given message into the codeword of a related message. A well-studied model of tampering is the 2-split-state model where the codeword consists of two independently tamperable states. As with standard error-correcting codes, it is of great importance to build codes with high rates.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519972"}, {"primary_key": "1766373", "vector": [], "sparse_vector": [], "title": "Hamiltonian complexity in the thermodynamic limit.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Despite immense progress in quantum Hamiltonian complexity in the past decade, little is known about the computational complexity of quantum physics at the thermodynamic limit. In fact, even defining the problem properly is not straight forward. We study the complexity of estimating the ground energy of a fixed, translationally-invariant (TI) Hamiltonian in the thermodynamic limit, to within a given precision; this precision (given by n the number of bits of the approximation) is the sole input to the problem. Understanding the complexity of this problem captures how difficult it is for a physicist to measure or compute another digit in the approximation of a physical quantity in the thermodynamic limit. We show that this problem is contained in FEXPQMA-EXP and is hard for FEXPNEXP. This means that the problem is doubly exponentially hard in the size of the input.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520067"}, {"primary_key": "1766374", "vector": [], "sparse_vector": [], "title": "No self-concordant barrier interior point method is strongly polynomial.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "It is an open question to determine if the theory of self-concordant barriers can provide an interior point method with strongly polynomial complexity in linear programming. In the special case of the logarithmic barrier, it was shown in [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, SIAM J. on Applied Algebra and Geometry, 2018] that the answer is negative. In this paper, we show that none of the self-concordant barrier interior point methods is strongly polynomial. This result is obtained by establishing that, on parametric families of convex optimization problems, the log-limit of the central path degenerates to a piecewise linear curve, independently of the choice of the barrier function. We provide an explicit linear program that falls in the same class as the K<PERSON><PERSON> counterexample for the simplex method, i.e., in which the feasible region is a combinatorial cube and the number of iterations is Ω(2n).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519997"}, {"primary_key": "1766375", "vector": [], "sparse_vector": [], "title": "Optimal oblivious reconfigurable networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hakim Weatherspoon", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Oblivious routing has a long history in both the theory and practice of networking. In this work we initiate the formal study of oblivious routing in the context of reconfigurable networks, a new architecture that has recently come to the fore in datacenter networking. These networks allow a rapidly changing bounded-degree pattern of interconnections between nodes, but the network topology and the selection of routing paths must both be oblivious to the traffic demand matrix. Our focus is on the trade-off between maximizing throughput and minimizing latency in these networks. For every constant throughput rate, we characterize (up to a constant factor) the minimum latency achievable by an oblivious reconfigurable network design that satisfies the given throughput guarantee. The trade-off between these two objectives turns out to be surprisingly subtle: the curve depicting it has an unexpected scalloped shape reflecting the fact that load-balancing becomes more difficult when the average length of routing paths is not an integer because equalizing all the path lengths is not possible. The proof of our lower bound uses LP duality to verify that Valiant load balancing is the most efficient oblivious routing scheme when used in combination with an optimally-designed reconfigurable network topology. The proof of our upper bound uses an algebraic construction in which the network nodes are identified with vectors over a finite field, the network topology is described by either the elementary basis or a sequence of Van<PERSON><PERSON><PERSON> matrices, and routing paths are constructed by selecting columns of these matrices to yield the appropriate mixture of path lengths within the shortest possible time interval.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520020"}, {"primary_key": "1766376", "vector": [], "sparse_vector": [], "title": "Near-optimal no-regret learning for correlated equilibria in multi-player general-sum games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (DFG) (NeurIPS`21) showed that if all agents in a multi-player general-sum normal-form game employ Optimistic Multiplicative Weights Update (OMWU), the external regret of every player is $O(\\textrm{polylog}(T))$ after $T$ repetitions of the game. We extend their result from external regret to internal regret and swap regret, thereby establishing uncoupled learning dynamics that converge to an approximate correlated equilibrium at the rate of $\\tilde{O}(T^{-1})$. This substantially improves over the prior best rate of convergence for correlated equilibria of $O(T^{-3/4})$ due to <PERSON> and <PERSON><PERSON> (NeurIPS`20), and it is optimal -- within the no-regret framework -- up to polylogarithmic factors in $T$. To obtain these results, we develop new techniques for establishing higher-order smoothness for learning dynamics involving fixed point operations. Specifically, we establish that the no-internal-regret learning dynamics of Stoltz and Lugosi (Mach <PERSON>rn`05) are equivalently simulated by no-external-regret dynamics on a combinatorial space. This allows us to trade the computation of the stationary distribution on a polynomial-sized Markov chain for a (much more well-behaved) linear transformation on an exponential-sized set, enabling us to leverage similar techniques as DFG to near-optimally bound the internal regret. Moreover, we establish an $O(\\textrm{polylog}(T))$ no-swap-regret bound for the classic algorithm of Blum and Mansour (BM) (JMLR`07). We do so by introducing a technique based on the Cauchy Integral Formula that circumvents the more limited combinatorial arguments of DFG. In addition to shedding clarity on the near-optimal regret guarantees of BM, our arguments provide insights into the various ways in which the techniques by DFG can be extended and leveraged in the analysis of more involved learning algorithms.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520031"}, {"primary_key": "1766377", "vector": [], "sparse_vector": [], "title": "Entropic independence: optimal mixing of down-up random walks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Thuy-<PERSON>ng <PERSON>"], "summary": "We introduce a notion called entropic independence that is an entropic analog of spectral notions of high-dimensional expansion. Informally, entropic independence of a background distribution µ on k-sized subsets of a ground set of elements says that for any (possibly randomly chosen) set S, the relative entropy of a single element of S drawn uniformly at random carries at most O(1/k) fraction of the relative entropy of S. Entropic independence is the analog of the notion of spectral independence, if one replaces variance by entropy. We use entropic independence to derive tight mixing time bounds, overcoming the lossy nature of spectral analysis of Markov chains on exponential-sized state spaces.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520048"}, {"primary_key": "1766378", "vector": [], "sparse_vector": [], "title": "An area law for 2d frustration-free spin systems.", "authors": ["<PERSON><PERSON><PERSON>", "Itai Arad", "<PERSON>"], "summary": "We prove that the entanglement entropy of the ground state of a locally gapped frustration-free 2D lattice spin system satisfies an area law with respect to a vertical bipartition of the lattice into left and right regions. We first establish that the ground state projector of any locally gapped frustration-free 1D spin system can be approximated to within error є by a degree O(√nlog(є−1)) multivariate polynomial in the interaction terms of the Hamiltonian. This generalizes the optimal bound on the approximate degree of the boolean AND function, which corresponds to the special case of commuting Hamiltonian terms. For 2D spin systems we then construct an approximate ground state projector (AGSP) that employs the optimal 1D approximation in the vicinity of the boundary of the bipartition of interest. This AGSP has sufficiently low entanglement and error to establish the area law using a known technique.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519962"}, {"primary_key": "1766379", "vector": [], "sparse_vector": [], "title": "Distributed Quantum inner product estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As small quantum computers are becoming available on different physical platforms, a benchmarking task known as cross-platform verification has been proposed that aims to estimate the fidelity of states prepared on two quantum computers. This task is fundamentally distributed, as no quantum communication can be performed between the two physical platforms due to hardware constraints, which prohibits a joint SWAP test. In this paper we settle the sample complexity of this task across all measurement and communication settings. The essence of the task, which we call distributed quantum inner product estimation, involves two players <PERSON> and <PERSON> who have k copies of unknown states ρ,σ (acting on ℂd) respectively. Their goal is to estimate Tr(ρσ) up to additive error ε∈(0,1), using local quantum operations and classical communication. In the weakest setting where only non-adaptive single-copy measurements and simultaneous message passing are allowed, we show that k=O(max{1/ε2,√d/ε}) copies suffice. This achieves a savings compared to full tomography which takes Ω(d3) copies with single-copy measurements. Surprisingly, we also show that the sample complexity must be at least Ω(max{1/ε2,√d/ε}), even in the strongest setting where adaptive multi-copy measurements and arbitrary rounds of communication are allowed. This shows that the success achieved by shadow tomography, for sample-efficiently learning the properties of a single system, cannot be generalized to the distributed setting. Furthermore, the fact that the sample complexity remains the same with single and multi-copy measurements contrasts with single system quantum property testing, which often demonstrate exponential separations in sample complexity with single and multi-copy measurements.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519974"}, {"primary_key": "1766380", "vector": [], "sparse_vector": [], "title": "Positive spectrahedra: invariance principles and pseudorandom generators.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In a recent work, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (STOC 2019) gave explicit pseudorandom generators (s) for arbitrary m-facet polytopes in n variables with seed length poly-logarithmic in m,n, concluding a sequence of works in the last decade, that was started by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> (SICOMP 2010) and <PERSON><PERSON>, <PERSON> (SICOMP 2013) for fooling linear and polynomial threshold functions, respectively. In this work, we consider a natural extension of s for intersections of positive spectrahedra. A positive spectrahedron is a Boolean function f(x)=[x1A1+⋯ +xnAn ≼ B] where the Ais are k× k positive semidefinite matrices. We construct explicit s that δ-fool \"regular\" width-M positive spectrahedra (i.e., when none of the Ais are dominant) over the Boolean space with seed length (logk,logn, M, 1/δ).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519965"}, {"primary_key": "1766381", "vector": [], "sparse_vector": [], "title": "Worst-case to average-case reductions via additive combinatorics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a new framework for designing worst-case to average-case reductions. For a large class of problems, it provides an explicit transformation of algorithms running in time T that are only correct on a small (subconstant) fraction of their inputs into algorithms running in time O(T) that are correct on all inputs.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520041"}, {"primary_key": "1766382", "vector": [], "sparse_vector": [], "title": "Deterministic graph coloring in the streaming model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent breakthroughs in graph streaming have led to design of semi-streaming algorithms for various graph coloring problems such as (Δ+1)-coloring, degeneracy-coloring, coloring triangle-free graphs, and others. These algorithms are all randomized in crucial ways and whether or not there is any deterministic analogue of them has remained an important open question in this line of work.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520016"}, {"primary_key": "1766383", "vector": [], "sparse_vector": [], "title": "Brooks&apo<PERSON>; theorem in graph streams: a single-pass semi-streaming algorithm for ∆-coloring.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Mi<PERSON>l"], "summary": "Every graph with maximum degree Δ can be colored with (Δ+1) colors using a simple greedy algorithm. Remarkably, recent work has shown that one can find such a coloring even in the semi-streaming model: there exists a randomized algorithm that with high probability finds a (Δ+1)-coloring of the input graph in only O(n·logn) space assuming a single pass over the edges of the graph in any arbitrary order. But, in reality, one almost never needs (Δ+1) colors to properly color a graph. Indeed, the celebrated <PERSON> theorem states that every (connected) graph beside cliques and odd cycles can be colored with Δ colors. Can we find a Δ-coloring in the semi-streaming model as well?", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520005"}, {"primary_key": "1766384", "vector": [], "sparse_vector": [], "title": "Hypercontractivity on high dimensional expanders.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Hypercontractivity is one of the most powerful tools in Boolean function analysis. Originally studied over the discrete hypercube, recent years have seen increasing interest in extensions to settings like the p-biased cube, slice, or Grassmannian, where variants of hypercontractivity have found a number of breakthrough applications including the resolution of <PERSON><PERSON>'s 2-2 Games Conjecture (<PERSON><PERSON>, <PERSON>, Safra FOCS 2018). In this work, we develop a new theory of hypercontractivity on high dimensional expanders (HDX), an important class of expanding complexes that has recently seen similarly impressive applications in both coding theory and approximate sampling. Our results lead to a new understanding of the structure of Boolean functions on HDX, including a tight analog of the KKL Theorem and a new characterization of non-expanding sets.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520040"}, {"primary_key": "1766385", "vector": [], "sparse_vector": [], "title": "Low-rank approximation with 1/ε1/3 matrix-vector products.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study iterative methods based on Krylov subspaces for low-rank approximation under any Schatten-p norm. Here, given access to a matrix A through matrix-vector products, an accuracy parameter є, and a target rank k, the goal is to find a rank-k matrix Z with orthonormal columns such that || A (I − Z Z⊤) || Sp ≤ (1+є)min U⊤U = Ik || A (I − U U⊤) || Sp, where || M ||Sp denotes the ℓp norm of the the singular values of M. For the special cases of p=2 (Frobenius norm) and p = ∞ (Spectral norm), <PERSON><PERSON> and <PERSON><PERSON> (NeurIPS 2015) obtained an algorithm based on Krylov methods that uses Õ(k/√є) matrix-vector products, improving on the naïve Õ(k/є) dependence obtainable by the power method, where Õ(·) suppresses poly(log(dk/є)) factors.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519988"}, {"primary_key": "1766386", "vector": [], "sparse_vector": [], "title": "Robustly learning mixtures of k arbitrary Gaussians.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Santosh S<PERSON>"], "summary": "We give a polynomial-time algorithm for the problem of robustly estimating a mixture of k arbitrary Gaussians in ℝd, for any fixed k, in the presence of a constant fraction of arbitrary corruptions. This resolves the main open problem in several previous works on algorithmic robust statistics, which addressed the special cases of robustly estimating (a) a single Gaussian, (b) a mixture of TV-distance separated Gaussians, and (c) a uniform mixture of two Gaussians. Our main tools are an efficient partial clustering algorithm that relies on the sum-of-squares method, and a novel tensor decomposition algorithm that allows errors in both Frobenius norm and low-rank terms.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519953"}, {"primary_key": "1766387", "vector": [], "sparse_vector": [], "title": "Distributed ∆-coloring plays hide-and-seek.", "authors": ["Alki<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove several new tight or near-tight distributed lower bounds for classic symmetry breaking problems in graphs. As a basic tool, we first provide a new insightful proof that any deterministic distributed algorithm that computes a Δ-coloring on Δ-regular trees requires Ω(logΔn) rounds and any randomized such algorithm requires Ω(logΔlogn) rounds. We prove this by showing that a natural relaxation of the Δ-coloring problem is a fixed point in the round elimination framework.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520027"}, {"primary_key": "1766388", "vector": [], "sparse_vector": [], "title": "Dynamic algorithms against an adaptive adversary: generic constructions and lower bounds.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>", "<PERSON><PERSON>"], "summary": "Given an input that undergoes a sequence of updates, a dynamic algorithm maintains a valid solution to some predefined problem at any point in time; the goal is to design an algorithm in which computing a solution to the updated input is done more efficiently than computing the solution from scratch. A dynamic algorithm against an adaptive adversary is required to be correct when the adversary chooses the next update after seeing the previous outputs of the algorithm. We obtain faster dynamic algorithms against an adaptive adversary and separation results between what is achievable in the oblivious vs. adaptive settings. To get these results we exploit techniques from differential privacy, cryptography, and adaptive data analysis. Our results are as follows.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520064"}, {"primary_key": "1766389", "vector": [], "sparse_vector": [], "title": "On the optimal time/space tradeoff for hash tables.", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For nearly six decades, the central open question in the study of hash tables has been to determine the optimal achievable tradeoff curve between time and space. State-of-the-art hash tables offer the following guarantee: If keys/values are Θ(logn) bits each, then it is possible to achieve constant-time insertions/deletions/queries while wasting only O(loglogn) bits of space per key when compared to the information-theoretic optimum—this bound has been proven to be optimal for a number of closely related problems (e.g., stable hashing, dynamic retrieval, and dynamically-resized filters).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519969"}, {"primary_key": "1766390", "vector": [], "sparse_vector": [], "title": "An extendable data structure for incremental stable perfect hashing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of dynamically assigning n elements unique indices, known as hashcodes, in the range [(1+o(1))n]. This problem is known as perfect hashing and is considered a fundamental building block in the design of more involved data structures. The challenge we address is that of designing a data structure that meets several, seemingly opposing, requirements: (1) the range and the space of the data structure must be, at all times, proportional to the current cardinality nt of the input set, and (2) the hashcodes it assigns must be stable in that the hashcode of an element must not change while the element is continuously in the set. A simple argument shows that these two desiderata are impossible to achieve when arbitrary deletions and insertions are allowed.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520070"}, {"primary_key": "1766391", "vector": [], "sparse_vector": [], "title": "On approximability of satisfiable k-CSPs: I.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the P-CSP problem for 3-ary predicates P on satisfiable instances. We show that under certain conditions on P and a (1,s) integrality gap instance of the P-CSP problem, it can be translated into a dictatorship vs. quasirandomness test with perfect completeness and soundness s+ε, for every constant ε>0. Compared to <PERSON><PERSON><PERSON><PERSON><PERSON>'s result [STOC, 2008], we do not lose perfect completeness. This is particularly interesting as this test implies new hardness results on satisfiable constraint satisfaction problems, assuming the Rich 2-to-1 Games Conjecture by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> [ITCS, 2021]. Our result can be seen as the first step of a potentially long-term challenging program of characterizing optimal inapproximability of every satisfiable k-ary CSP.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520028"}, {"primary_key": "1766392", "vector": [], "sparse_vector": [], "title": "Fast, algebraic multivariate multipoint evaluation in small characteristic and applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Multipoint evaluation is the computational task of evaluating a polynomial given as a list of coefficients at a given set of inputs. Besides being a natural and fundamental question in computer algebra on its own, fast algorithms for this problem are also closely related to fast algorithms for other natural algebraic questions like polynomial factorization and modular composition. And while nearly linear time algorithms have been known for the univariate instance of multipoint evaluation for close to five decades due to a work of <PERSON><PERSON><PERSON> and <PERSON>, fast algorithms for the multivariate version have been much harder to come by. In a significant improvement to the state of art for this problem, <PERSON><PERSON> and Kedlaya & Umans gave nearly linear time algorithms for this problem over field of small characteristic and over all finite fields respectively, provided that the number of variables n is at most do(1) where the degree of the input polynomial in every variable is less than d. They also stated the question of designing fast algorithms for the large variable case (i.e. n ∉ do(1)) as an open problem.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519968"}, {"primary_key": "1766393", "vector": [], "sparse_vector": [], "title": "The shortest even cycle problem is tractable.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a directed graph as input, we show how to efficiently find a shortest (directed, simple) cycle on an even number of vertices. As far as we know, no polynomial-time algorithm was previously known for this problem. In fact, finding any even cycle in a directed graph in polynomial time was open for more than two decades until <PERSON>, <PERSON>, and <PERSON> (Ann. of Math. (2) 1999) and, independently, <PERSON><PERSON><PERSON><PERSON><PERSON> (Electron. J. Combin. 2004; announced jointly at STOC 1997) gave an efficiently testable structural characterisation of even-cycle-free directed graphs.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520030"}, {"primary_key": "1766394", "vector": [], "sparse_vector": [], "title": "The query complexity of certification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of certification: given queries to a function f : {0,1}n → {0,1} with certificate complexity ≤ k and an input x⋆, output a size-k certificate for f's value on x⋆.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519993"}, {"primary_key": "1766395", "vector": [], "sparse_vector": [], "title": "Twin-width IV: ordered graphs and matrices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We establish a list of characterizations of bounded twin-width for hereditary classes of totally ordered graphs: as classes of at most exponential growth studied in enumerative combinatorics, as monadically NIP classes studied in model theory, as classes that do not transduce the class of all graphs studied in finite model theory, and as classes for which model checking first-order logic is fixed-parameter tractable studied in algorithmic graph theory.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520037"}, {"primary_key": "1766396", "vector": [], "sparse_vector": [], "title": "Quantum garbled circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In classical computing, garbled circuits (and their generalization known as randomized encodings) are a versatile cryptographic tool with many applications such as secure multiparty computation, delegated computation, depth-reduction of cryptographic primitives, complexity lower-bounds, and more. Quantum analogues of garbled circuits were not known prior to this work.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520073"}, {"primary_key": "1766397", "vector": [], "sparse_vector": [], "title": "Faster maxflow via improved dynamic spectral vertex sparsifiers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We make several advances broadly related to the maintenance of electrical flows in weighted graphs undergoing dynamic resistance updates, including:", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520068"}, {"primary_key": "1766398", "vector": [], "sparse_vector": [], "title": "Sublinear time spectral density estimation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a new sublinear time algorithm for approximating the spectral density (eigenvalue distribution) of an n× n normalized graph adjacency or Laplacian matrix. The algorithm recovers the spectrum up to є accuracy in the Wasserstein-1 distance in O(n· (1/є)) time given sample access to the graph. This result compliments recent work by <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON> (2018), which obtains a solution with runtime independent of n, but exponential in 1/є. We conjecture that the trade-off between dimension dependence and accuracy is inherent.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520009"}, {"primary_key": "1766399", "vector": [], "sparse_vector": [], "title": "Almost-optimal sublinear-time edit distance in the low distance regime.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We revisit the task of computing the edit distance in sublinear time. In the (k,K)-gap edit distance problem we are given oracle access to two strings of length n and the task is to distinguish whether their edit distance is at most k or at least K. It has been established by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (FOCS '19), with improvements by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (FOCS '20), that the (k,k2)-gap problem can be solved in time O(n/k + poly(k)). One of the most natural questions in this line of research is whether the (k,k2)-gap is best-possible for the running time O(n/k + poly(k)).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519990"}, {"primary_key": "1766400", "vector": [], "sparse_vector": [], "title": "Complexity classification of counting graph homomorphisms modulo a prime number.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Counting graph homomorphisms and its generalizations such as the Counting Constraint Satisfaction Problem (CSP), its variations, and counting problems in general have been intensively studied since the pioneering work of <PERSON><PERSON>. While the complexity of exact counting of graph homomorphisms (<PERSON> and <PERSON>, 2000) and the counting CSP (<PERSON><PERSON><PERSON>, 2013, and <PERSON> and <PERSON>, 2013) is well understood, counting modulo some natural number has attracted considerable interest as well. In their 2015 paper <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> suggested a conjecture stating that counting homomorphisms to a fixed graph H modulo a prime number is hard whenever it is hard to count exactly, unless <PERSON> has automorphisms of certain kind. In this paper we confirm this conjecture. As a part of this investigation we develop techniques that widen the spectrum of reductions available for modular counting and apply to the general CSP rather than being limited to graph homomorphisms.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520075"}, {"primary_key": "1766401", "vector": [], "sparse_vector": [], "title": "On the complexity of CSP-based ideal membership problems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper we consider the Ideal Membership Problem (IMP for short), in which we are given polynomials f0,f1,…,fk and the question is to decide whether f0 belongs to the ideal generated by f1,…,fk. In the more stringent version the task is also to find a proof of this fact. The IMP underlies many proof systems based on polynomials such as Nullstellensatz, Polynomial Calculus, and Sum-of-Squares (SOS). In such applications the IMP usually involves so called combinatorial ideals that arise from a variety of discrete combinatorial problems. This restriction makes the IMP significantly easier and in some cases allows for an efficient solution algorithm.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520063"}, {"primary_key": "1766402", "vector": [], "sparse_vector": [], "title": "Computational thresholds for the fixed-magnetization Ising model.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ferromagnetic Ising model is a model of a magnetic material and a central topic in statistical physics. It also plays a starring role in the algorithmic study of approximate counting: approximating the partition function of the ferromagnetic Ising model with uniform external field is tractable at all temperatures and on all graphs, due to the randomized algorithm of <PERSON><PERSON><PERSON> and <PERSON>.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520003"}, {"primary_key": "1766403", "vector": [], "sparse_vector": [], "title": "Edge connectivity augmentation in near-linear time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We give an Õ(m)-time algorithm for the edge connectivity augmentation problem and the closely related edge splitting-off problem. This is optimal up to lower order terms and closes the long line of work on these problems.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520038"}, {"primary_key": "1766404", "vector": [], "sparse_vector": [], "title": "Hardness for triangle problems under even more believable hypotheses: reductions from real APSP, real 3SUM, and OV.", "authors": ["<PERSON>", "Virginia Vassilevska Williams", "<PERSON><PERSON><PERSON> Xu"], "summary": "The 3SUM hypothesis, the All-Pairs Shortest Paths (APSP) hypothesis and the Strong Exponential Time Hypothesis are the three main hypotheses in the area of fine-grained complexity. So far, within the area, the first two hypotheses have mainly been about integer inputs in the Word RAM model of computation. The \"Real APSP\" and \"Real 3SUM\" hypotheses, which assert that the APSP and 3SUM hypotheses hold for real-valued inputs in a reasonable version of the Real RAM model, are even more believable than their integer counterparts.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520032"}, {"primary_key": "1766405", "vector": [], "sparse_vector": [], "title": "Almost-linear ε-emulators for planar graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study vertex sparsification for distances, in the setting of planar graphs with distortion: Given a planar graph G (with edge weights) and a subset of k terminal vertices, the goal is to construct an ε-emulator, which is a small planar graph G′ that contains the terminals and preserves the distances between the terminals up to factor 1+ε.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519998"}, {"primary_key": "1766406", "vector": [], "sparse_vector": [], "title": "Approximate polymorphisms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For a function g∶{0,1}m→{0,1}, a function f∶ {0,1}n→{0,1} is called a g-polymorphism if their actions commute: f(g(row1(Z)),…,g(rown(Z))) = g(f(col1(Z)),…,f(colm(Z))) for all Z∈{0,1}n× m. The function f is called an approximate g-polymorphism if this equality holds with probability close to 1, when Z is sampled uniformly. A pair of functions f0,f1∶ {0,1}n → {0,1} are called a skew g-polymorphism if f0(g(row1(Z)),…,g(rown(Z))) = g(f1(col1(Z)),…,f1(colm(Z))) for all Z∈{0,1}n× m.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519966"}, {"primary_key": "1766407", "vector": [], "sparse_vector": [], "title": "Extractors for sum of two sources.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the problem of extracting randomness from sumset sources, a general class of weak sources introduced by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> (STOC, 2016). An (n,k,C)-sumset source X is a distribution on {0,1}n of the form X1 + X2 + … + XC, where Xi's are independent sources on n bits with min-entropy at least k. Prior extractors either required the number of sources C to be a large constant or the min-entropy k to be at least 0.51 n.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519963"}, {"primary_key": "1766408", "vector": [], "sparse_vector": [], "title": "New streaming algorithms for high dimensional EMD and MST.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study streaming algorithms for two fundamental geometric problems: computing the cost of a Minimum Spanning Tree (MST) of an n-point set X ⊂ {1,2,…,Δ}d, and computing the Earth Mover Distance (EMD) between two multi-sets A,B ⊂ {1,2,…,Δ}d of size n. We consider the turnstile model, where points can be added and removed. We give a one-pass streaming algorithm for MST and a two-pass streaming algorithm for EMD, both achieving an approximation factor of Õ(logn) and using (n,d,Δ)-space only. Furthermore, our algorithm for EMD can be compressed to a single pass with a small additive error. Previously, the best known sublinear-space streaming algorithms for either problem achieved an approximation of O(min{ logn , log(Δ d)} logn). For MST, we also prove that any constant space streaming algorithm can only achieve an approximation of Ω(logn), analogous to the Ω(logn) lower bound for EMD.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519979"}, {"primary_key": "1766409", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> filtering with adversarial corruptions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Here we revisit the classic problem of linear quadratic estimation, i.e. estimating the trajectory of a linear dynamical system from noisy measurements. The celebrated <PERSON><PERSON> filter gives an optimal estimator when the measurement noise is Gaussian, but is widely known to break down when one deviates from this assumption, e.g. when the noise is heavy-tailed. Many ad hoc heuristics have been employed in practice for dealing with outliers. In a pioneering work, <PERSON><PERSON><PERSON> and <PERSON> gave provable guarantees when the measurement noise is a known infinitesimal perturbation of a Gaussian and raised the important question of whether one can get similar guarantees for large and unknown perturbations.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520050"}, {"primary_key": "1766410", "vector": [], "sparse_vector": [], "title": "On the complexity of dynamic submodular maximization.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We study dynamic algorithms for the problem of maximizing a monotone submodular function over a stream of n insertions and deletions. We show that any algorithm that maintains a (0.5+є)-approximate solution under a cardinality constraint, for any constant є>0, must have an amortized query complexity that is polynomial in n. Moreover, a linear amortized query complexity is needed in order to maintain a 0.584-approximate solution. This is in sharp contrast with recent dynamic algorithms of [LMN+20, Mon20] that achieve (0.5−є)-approximation with a polylog(n) amortized query complexity.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519951"}, {"primary_key": "1766411", "vector": [], "sparse_vector": [], "title": "Uniform approximations for Randomized Hadamard Transforms with applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Randomized Hadamard Transforms (RHTs) have emerged as a computationally efficient alternative to the use of dense unstructured random matrices across a range of domains in computer science and machine learning. For several applications such as dimensionality reduction and compressed sensing, the theoretical guarantees for methods based on RHTs are comparable to approaches using dense random matrices with i.i.d. entries. However, several such applications are in the low-dimensional regime where the number of rows sampled from the matrix is rather small. Prior arguments are not applicable to the high-dimensional regime often found in machine learning applications like kernel approximation. Given an ensemble of RHTs with Gaussian diagonals, {Mi}i = 1m, and any 1-Lipschitz function, f: → , we prove that the average of f over the entries of {Mi v}i = 1m converges to its expectation uniformly over | v | ≤ 1 at a rate comparable to that obtained from using truly Gaussian matrices. We use our inequality to then derive improved guarantees for two applications in the high-dimensional regime: 1) kernel approximation and 2) distance estimation. For kernel approximation, we prove the first uniform approximation guarantees for random features constructed through RHTs lending theoretical justification to their empirical success while for distance estimation, our convergence result implies data structures with improved runtime guarantees over previous work by the authors. We believe our general inequality is likely to find use in other applications.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519961"}, {"primary_key": "1766412", "vector": [], "sparse_vector": [], "title": "Faster min-plus product for monotone instances.", "authors": ["Shucheng Chi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we show that the time complexity of monotone min-plus product of two n× n matrices is Õ(n(3+ω)/2)=Õ(n2.687), where ω < 2.373 is the fast matrix multiplication exponent [<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> 2021]. That is, when A is an arbitrary integer matrix and B is either row-monotone or column-monotone with integer elements bounded by O(n), computing the min-plus product C where Ci,j=mink{Ai,k+Bk,j} takes Õ(n(3+ω)/2) time, which greatly improves the previous time bound of Õ(n(12+ω)/5)=Õ(n2.875) [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> 2021]. Then by simple reductions, this means the case that A is arbitrary and the columns or rows of B are bounded-difference can also be solved in Õ(n(3+ω)/2) time, whose previous result gives time complexity of Õ(n2.922) [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> 2016]. So the case that both of A and B are bounded-difference also has Õ(n(3+ω)/2) time algorithm, whose previous results give time complexities of Õ(n2.824) [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> 2016] and Õ(n2.779) [<PERSON>, <PERSON><PERSON> and <PERSON><PERSON> 2022]. Many problems are reducible to these problems, such as language edit distance, RNA-folding, scored parsing problem on BD grammars [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>v<PERSON> 2016]. Thus, their complexities are all improved.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520057"}, {"primary_key": "1766413", "vector": [], "sparse_vector": [], "title": "Linear space streaming lower bounds for approximating CSPs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Madhu <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the approximability of constraint satisfaction problems in the streaming setting. For every constraint satisfaction problem (CSP) on n variables taking values in {0,…,q−1}, we prove that improving over the trivial approximability by a factor of q requires Ω(n) space even on instances with O(n) constraints. We also identify a broad subclass of problems for which any improvement over the trivial approximability requires Ω(n) space. The key technical core is an optimal, q−(k−1)-inapproximability for the Max k-LIN-mod q problem, which is the Max CSP problem where every constraint is given by a system of k−1 linear equations mod q over k variables.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519983"}, {"primary_key": "1766414", "vector": [], "sparse_vector": [], "title": "A subpolynomial approximation algorithm for graph crossing number in low-degree graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the classical Minimum Crossing Number problem: given an n-vertex graph G, compute a drawing of G in the plane, while minimizing the number of crossings between the images of its edges. This is a fundamental and extensively studied problem, whose approximability status is widely open. In all currently known approximation algorithms, the approximation factor depends polynomially on Δ – the maximum vertex degree in G. The best current approximation algorithm achieves an O(n1/2−· (Δ·logn))-approximation, for a small fixed constant є, while the best negative result is APX-hardness, leaving a large gap in our understanding of this basic problem. In this paper we design a randomized O(2O((logn)7/8loglogn)·(Δ))-approximation algorithm for Minimum Crossing Number. This is the first approximation algorithm for the problem that achieves a subpolynomial in n approximation factor (albeit only in graphs whose maximum vertex degree is subpolynomial in n).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519984"}, {"primary_key": "1766415", "vector": [], "sparse_vector": [], "title": "Bypassing the surface embedding: approximation schemes for network design in minor-free graphs.", "authors": ["<PERSON>"], "summary": "Since the mid 90s, the study of the complexity of classic network design problems such as the traveling salesman problem (TSP), the Steiner tree problem (ST), or the k-MST problem on metric spaces such as low-dimensional Euclidean spaces, doubling metrics, planar or minor-free graphs, has led to major improvements of our understanding of the structure of both these important metric spaces, and the underlying problems.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520049"}, {"primary_key": "1766416", "vector": [], "sparse_vector": [], "title": "Improved approximations for Euclidean k-means and k-median, via nested quasi-independent sets.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by data analysis and machine learning applications, we consider the popular high-dimensional Euclidean k-median and k-means problems. We propose a new primal-dual algorithm, inspired by the classic algorithm of <PERSON> and <PERSON> and the recent algorithm of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Our algorithm achieves an approximation ratio of 2.406 and 5.912 for Euclidean k-median and k-means, respectively, improving upon the 2.633 approximation ratio of <PERSON> et al. and the 6.1291 approximation ratio of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520011"}, {"primary_key": "1766417", "vector": [], "sparse_vector": [], "title": "Towards optimal lower bounds for k-median and k-means coresets.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The (k,z)-clustering problem consists of finding a set of k points called centers, such that the sum of distances raised to the power of z of every data point to its closest center is minimized. Among the most commonly encountered special cases are k-median problem (z=1) and k-means problem (z=2). The k-median and k-means problems are at the heart of modern data analysis and massive data applications have given raise to the notion of coreset: a small (weighted) subset of the input point set preserving the cost of any solution to the problem up to a multiplicative (1± ϵ) factor, hence reducing from large to small scale the input to the problem. While there has been an intensive effort to understand what is the best coreset size possible for both problems in various metric spaces, there is still a significant gap between the state-of-the-art upper and lower bounds. In this paper, we make progress on both upper and lower bounds, obtaining tight bounds for several cases, namely: •In finite n point general metrics, any coreset must consist of ω(k logn / ϵ2) points. This improves on the ω(k logn /ϵ) lower bound of <PERSON><PERSON>, Jiang, <PERSON>, and <PERSON> [ICML'19] and matches the upper bounds proposed for k-median by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [STOC'11] and k-means by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [STOC'21] up to polylog factors. •For doubling metrics with doubling constant D, any coreset must consist of ω(k D/ϵ2) points. This matches the k-median and k-means upper bounds by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>hwiegelshohn [ST<PERSON>'21] up to polylog factors. •In d-dimensional Euclidean space, any coreset for (k,z) clustering requires ω(k/ϵ2) points. This improves on the ω(k/ϵ) lower bound of Baker, Braverman, Huang, Jiang, Krauthgamer, and Wu [ICML'20] for k-median and complements the ω(kmin(d,2z/20)) lower bound of Huang and Vishnoi [STOC'20]. We complement our lower bound for d-dimensional Euclidean space with the construction of a coreset of size Õ(k/ϵ2· min(ϵ-z,k)). This improves over the Õ(k2 ϵ-4) upper bound for general power of z proposed by Braverman Jiang, Krauthgamer, and Wu [SODA'21] and over the Õ(k/ϵ4) upper bound for k-median by Huang and Vishnoi [STOC'20]. In fact, ours is the first construction breaking through the ϵ-2· min(d,ϵ-2) barrier inherent in all previous coreset constructions. To do this, we employ a novel chaining based analysis that may be of independent interest. Together our upper and lower bounds for k-median in Euclidean spaces are tight up to a factor O(ϵ-1 polylog k/ϵ).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519946"}, {"primary_key": "1766418", "vector": [], "sparse_vector": [], "title": "Deniable encryption in a Quantum world.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "(Sender-)Deniable encryption provides a very strong privacy guarantee: a sender who is coerced by an attacker into \"opening\" their ciphertext after-the-fact is able to generate \"fake\" local random choices that are consistent with any plaintext of their choice. The only known fully-efficient constructions of public-key deniable encryption rely on indistinguishability obfuscation (iO) (which currently can only be based on sub-exponential hardness assumptions).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520019"}, {"primary_key": "1766419", "vector": [], "sparse_vector": [], "title": "Near-optimal Quantum algorithms for multivariate mean estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose the first near-optimal quantum algorithm for estimating in Euclidean norm the mean of a vector-valued random variable with finite mean and covariance. Our result aims at extending the theory of multivariate sub-Gaussian estimators to the quantum setting. Unlike classically, where any univariate estimator can be turned into a multivariate estimator with at most a logarithmic overhead in the dimension, no similar result can be proved in the quantum setting. Indeed, <PERSON> ruled out the existence of a quantum advantage for the mean estimation problem when the sample complexity is smaller than the dimension. Our main result is to show that, outside this low-precision regime, there is a quantum estimator that outperforms any classical estimator. Our approach is substantially more involved than in the univariate setting, where most quantum estimators rely only on phase estimation. We exploit a variety of additional algorithmic techniques such as amplitude amplification, the Bernstein-<PERSON> algorithm, and quantum singular value transformation. Our analysis also uses concentration inequalities for multivariate truncated statistics. We develop our quantum estimators in two different input models that showed up in the literature before. The first one provides coherent access to the binary representation of the random variable and it encompasses the classical setting. In the second model, the random variable is directly encoded into the phases of quantum registers. This model arises naturally in many quantum algorithms but it is often incomparable to having classical samples. We adapt our techniques to these two settings and we show that the second model is strictly weaker for solving the mean estimation problem. Finally, we describe several applications of our algorithms, notably in measuring the expectation values of commuting observables and in the field of machine learning.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520045"}, {"primary_key": "1766420", "vector": [], "sparse_vector": [], "title": "Deterministic massively parallel connectivity.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of designing fundamental graph algorithms on the model of Massive Parallel Computation (MPC). The input to the problem is an undirected graph G with n vertices and m edges, and with D being the maximum diameter of any connected component in G. We consider the MPC with low local space, allowing each machine to store only Θ(nδ) words for an arbitrary constant δ>0, and with linear global space (which is the number of machines times the local space available), that is, with optimal utilization.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520055"}, {"primary_key": "1766421", "vector": [], "sparse_vector": [], "title": "A new framework for matrix discrepancy: partial coloring bounds via mirror descent.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by the <PERSON> Spencer conjecture, we study the problem of finding signed sums of matrices with a small matrix norm. A well-known strategy to obtain these signs is to prove, given matrices A1, …, An ∈ ℝm × m, a Gaussian measure lower bound of 2−O(n) for a scaling of the discrepancy body {x ∈ ℝn: || ∑i=1n xi Ai|| ≤ 1}. We show this is equivalent to covering its polar with 2O(n) translates of the cube 1/n B∞n, and construct such a cover via mirror descent. As applications of our framework, we show:", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519967"}, {"primary_key": "1766422", "vector": [], "sparse_vector": [], "title": "Fast rates for nonparametric online learning: from realizability to learning in games.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study fast rates of convergence in the setting of nonparametric online regression, namely where regret is defined with respect to an arbitrary function class which has bounded complexity. Our contributions are two-fold:", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519950"}, {"primary_key": "1766423", "vector": [], "sparse_vector": [], "title": "Constant inapproximability for PPA.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the ε-Consensus-Halving problem, we are given n probability measures v1, …, vn on the interval R = [0,1], and the goal is to partition R into two parts R+ and R− using at most n cuts, so that |vi(R+) − vi(R−)| ≤ ε for all i. This fundamental fair division problem was the first natural problem shown to be complete for the class PPA, and all subsequent PPA-completeness results for other natural problems have been obtained by reducing from it.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520079"}, {"primary_key": "1766424", "vector": [], "sparse_vector": [], "title": "Approximately efficient bilateral trade.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Balasubramanian <PERSON>", "<PERSON><PERSON>"], "summary": "We study bilateral trade between two strategic agents. The celebrated result of <PERSON><PERSON> and <PERSON> states that in general, no incentive-compatible, individually rational and weakly budget balanced mechanism can be efficient. I.e., no mechanism with these properties can guarantee a trade whenever buyer value exceeds seller cost. Given this, a natural question is whether there exists a mechanism with these properties that guarantees a constant fraction of the first-best gains-from-trade, namely a constant fraction of the gains-from-trade attainable whenever buyer's value weakly exceeds seller's cost. In this work, we positively resolve this long-standing open question on constant-factor approximation, mentioned in several previous works, using a simple mechanism that obtains a 1/8.23 ≈ 0.121 fraction of the first-best.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520054"}, {"primary_key": "1766425", "vector": [], "sparse_vector": [], "title": "Clustering mixture models in almost-linear time via list-decodable mean estimation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the problem of list-decodable mean estimation, where an adversary can corrupt a majority of the dataset. Specifically, we are given a set T of n points in ℝd and a parameter 0 0. All prior algorithms for this problem had additional polynomial factors in 1/α. We leverage this result, together with additional techniques, to obtain the first almost-linear time algorithms for clustering mixtures of k separated well-behaved distributions, nearly-matching the statistical guarantees of spectral methods. Prior clustering algorithms inherently relied on an application of k-PCA, thereby incurring runtimes of Ω(n d k). This marks the first runtime improvement for this basic statistical problem in nearly two decades.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520014"}, {"primary_key": "1766426", "vector": [], "sparse_vector": [], "title": "Learning general halfspaces with general <PERSON><PERSON> noise under the Gaussian distribution.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of PAC learning halfspaces on ℝd with <PERSON><PERSON> noise under the Gaussian distribution. In the <PERSON><PERSON> model, an adversary is allowed to flip the label of each point x with unknown probability η(x) ≤ η, for some parameter η ∈ [0,1/2]. The goal is to find a hypothesis with misclassification error of OPT + є, where OPT is the error of the target halfspace. This problem had been previously studied under two assumptions: (i) the target halfspace is homogeneous (i.e., the separating hyperplane goes through the origin), and (ii) the parameter η is strictly smaller than 1/2. Prior to this work, no nontrivial bounds were known when either of these assumptions is removed. We study the general problem and establish the following:", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519970"}, {"primary_key": "1766427", "vector": [], "sparse_vector": [], "title": "Locally testable codes with constant rate, distance, and locality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A locally testable code (LTC) is an error correcting code that has a property-tester. The tester reads q bits that are randomly chosen, and rejects words with probability proportional to their distance from the code. The parameter q is called the locality of the tester.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520024"}, {"primary_key": "1766428", "vector": [], "sparse_vector": [], "title": "On the hardness of dominant strategy mechanism design.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the communication complexity of dominant strategy implementations of combinatorial auctions. We start with two domains that are generally considered \"easy\": multi-unit auctions with decreasing marginal values and combinatorial auctions with gross substitutes valuations. For both domains we have fast algorithms that find the welfare-maximizing allocation with communication complexity that is poly-logarithmic in the input size. This immediately implies that welfare maximization can be achieved in ex-post equilibrium with no significant communication cost, by using VCG payments. In contrast, we show that in both domains the communication complexity of any dominant strategy implementation that achieves the optimal welfare is polynomial in the input size.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520013"}, {"primary_key": "1766429", "vector": [], "sparse_vector": [], "title": "Maintaining exact distances under multiple edge failures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Ren"], "summary": "We present the first compact distance oracle that tolerates multiple failures and maintains *exact* distances. Given an undirected weighted graph G = (V, E) and an arbitrarily large constant d, we construct an oracle that given vertices u, v ∈ V and a set of d edge failures D, outputs the *exact* distance between u and v in G − D (that is, G with edges in D removed). Our oracle has space complexity O(d n4) and query time dO(d). Previously, there were compact *approximate* distance oracles under multiple failures [<PERSON><PERSON><PERSON>, Cohen, Fiat, and Kaplan, SODA'17; <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, SODA'21], but the best exact distance oracles under d failures require essentially Ω(nd) space [<PERSON><PERSON> and <PERSON><PERSON>, SODA'09]. Our distance oracle seems to require nΩ(d) time to preprocess; we leave it as an open question to improve this preprocessing time.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520002"}, {"primary_key": "1766430", "vector": [], "sparse_vector": [], "title": "Circuits resilient to short-circuit errors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gillat Kol", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Given a Boolean circuit C, we wish to convert it to a circuit C′ that computes the same function as C even if some of its gates suffer from adversarial short circuit errors, i.e., their output is replaced by the value of one of their inputs. Can we design such a resilient circuit C′ whose size is roughly comparable to that of C? Prior work gave a positive answer for the special case where C is a formula.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520007"}, {"primary_key": "1766431", "vector": [], "sparse_vector": [], "title": "Improved approximation guarantees for shortest superstrings using cycle classification by overlap to length ratios.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the Shortest Superstring problem, we are given a set of strings and we are asking for a common superstring, which has the minimum number of characters. The Shortest Superstring problem is NP-hard and several constant-factor approximation algorithms are known for it. Of particular interest is the GREEDY algorithm, which repeatedly merges two strings of maximum overlap until a single string remains. The GREEDY algorithm, being simpler than other well-performing approximation algorithms for this problem, has attracted attention since the 1980s and is commonly used in practical applications.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520001"}, {"primary_key": "1766432", "vector": [], "sparse_vector": [], "title": "Learning low-degree functions from a logarithmic number of random queries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove that every bounded function f:{−1,1}n→[−1,1] of degree at most d can be learned with L2-accuracy ε and confidence 1−δ from log(n/δ) ε−d−1 Cd3/2√logd random queries, where C>1 is a universal finite constant.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519981"}, {"primary_key": "1766433", "vector": [], "sparse_vector": [], "title": "The exact complexity of pseudorandom functions and the black-box natural proof barrier for bootstrapping results in computational complexity.", "authors": ["<PERSON><PERSON><PERSON> Fan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Investigating the computational resources we need for cryptography is an essential task of both theoretical and practical interests. This paper provides answers to this problem on pseudorandom functions (PRFs). We resolve the exact complexity of PRFs by proving tight upper and lower bounds for various circuit models.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520010"}, {"primary_key": "1766434", "vector": [], "sparse_vector": [], "title": "Locality-sensitive orderings and applications to reliable spanners.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON> [2020] recently developed locality-sensitive ordering (LSO), a new tool that allows one to reduce problems in the Euclidean space ℝd to the 1-dimensional line. They used LSO's to solve a host of problems. Later, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [2019,2020] used the LSO of <PERSON> et al. to construct very sparse reliable spanners for the Euclidean space. A highly desirable feature of a reliable spanner is its ability to withstand a massive failure: the network remains functioning even if 90% of the nodes fail. In a follow-up work, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [2021] constructed reliable spanners for general and topologically structured metrics. Their construction used a different approach, and is based on sparse covers.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520042"}, {"primary_key": "1766435", "vector": [], "sparse_vector": [], "title": "Deterministic (1+ε)-approximate maximum matching with poly(1/ε) passes in the semi-streaming model and beyond.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a deterministic (1+ε)-approximate maximum matching algorithm in poly(1/ε) passes in the semi-streaming model, solving the long-standing open problem of breaking the exponential barrier in the dependence on 1/ε. Our algorithm exponentially improves on the well-known randomized (1/ε)O(1/ε)-pass algorithm from the seminal work by <PERSON> [APPROX05], the recent deterministic algorithm by <PERSON><PERSON><PERSON><PERSON> with the same pass complexity [FSTTCS18]. Up to polynomial factors in 1/ε, our work matches the state-of-the-art deterministic (logn / loglogn) · (1/ε)-pass algorithm by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [TOPC18], that is allowed a dependence on the number of nodes n. Our result also makes progress on the Open Problem 60 at sublinear.info.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520039"}, {"primary_key": "1766436", "vector": [], "sparse_vector": [], "title": "Counting small induced subgraphs with hereditary properties.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study the computational complexity of the problem #IndSub(Φ) of counting k-vertex induced subgraphs of a graph G that satisfy a graph property Φ. Our main result establishes an exhaustive and explicit classification for all hereditary properties, including tight conditional lower bounds under the Exponential Time Hypothesis (ETH): If a hereditary property Φ is true for all graphs, or if it is true only for finitely many graphs, then #IndSub(Φ) is solvable in polynomial time. Otherwise, #IndSub(Φ) is #W[1]-complete when parameterised by k, and, assuming ETH, it cannot be solved in time f(k)· |G|o(k) for any function f. This classification features a wide range of properties for which the corresponding detection problem (as classified by <PERSON><PERSON> and Rama<PERSON> [TCS 02]) is tractable but counting is hard. Moreover, even for properties which are already intractable in their decision version, our results yield significantly stronger lower bounds for the counting problem. As additional result, we also present an exhaustive and explicit parameterised complexity classification for all properties that are invariant under homomorphic equivalence. By covering one of the most natural and general notions of closure, namely, closure under vertex-deletion (hereditary), we generalise some of the earlier results on this problem. For instance, our results fully subsume and strengthen the existing classification of #IndSub(Φ) for monotone (subgraph-closed) properties due to <PERSON>, <PERSON><PERSON>, and <PERSON> [FOCS 20]. A full version of our paper, containing all proofs, is available at https://arxiv.org/abs/2111.02277.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520008"}, {"primary_key": "1766437", "vector": [], "sparse_vector": [], "title": "Fast FPT-approximation of branchwidth.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Branchwidth determines how graphs, and more generally, arbitrary connectivity (basically symmetric and submodular) functions could be decomposed into a tree-like structure by specific cuts. We develop a general framework for designing fixed-parameter tractable (FPT) 2-approximation algorithms for branchwidth of connectivity functions. The first ingredient of our framework is combinatorial. We prove a structural theorem establishing that either a sequence of particular refinement operations could decrease the width of a branch decomposition or that the width of the decomposition is already within a factor of 2 from the optimum. The second ingredient is an efficient implementation of the refinement operations for branch decompositions that support efficient dynamic programming. We present two concrete applications of our general framework.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519996"}, {"primary_key": "1766438", "vector": [], "sparse_vector": [], "title": "Dequantizing the Quantum singular value transformation: hardness and applications to Quantum chemistry and the Quantum PCP conjecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Quantum Singular Value Transformation (QSVT) is a recent technique that gives a unified framework to describe most quantum algorithms discovered so far, and may lead to the development of novel quantum algorithms. In this paper we investigate the hardness of classically simulating the QSVT. A recent result by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON> (STOC 2020) showed that the QSVT can be efficiently \"dequantized\" for low-rank matrices, and discussed its implication to quantum machine learning. In this work, motivated by establishing the superiority of quantum algorithms for quantum chemistry and making progress on the quantum PCP conjecture, we focus on the other main class of matrices considered in applications of the QSVT, sparse matrices. We first show how to efficiently \"dequantize\", with arbitrarily small constant precision, the QSVT associated with a low-degree polynomial. We apply this technique to design classical algorithms that estimate, with constant precision, the singular values of a sparse matrix. We show in particular that a central computational problem considered by quantum algorithms for quantum chemistry (estimating the ground state energy of a local Hamiltonian when given, as an additional input, a state sufficiently close to the ground state) can be solved efficiently with constant precision on a classical computer. As a complementary result, we prove that with inverse-polynomial precision, the same problem becomes BQP-complete. This gives theoretical evidence for the superiority of quantum algorithms for chemistry, and strongly suggests that said superiority stems from the improved precision achievable in the quantum setting. We also discuss how this dequantization technique may help make progress on the central quantum PCP conjecture.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519991"}, {"primary_key": "1766439", "vector": [], "sparse_vector": [], "title": "Low-temperature Ising dynamics with random initializations.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Glauber dynamics on spin systems are well known to suffer exponential slowdowns at low temperatures due to the emergence of multiple metastable phases, separated by narrow bottlenecks that are hard for the dynamics to cross. It is a folklore belief that if the dynamics is initialized from an appropriate random mixture of ground states, one for each phase, then convergence to the Gibbs distribution should be much faster. However, such phenomena have largely evaded rigorous analysis, as most tools in the study of Markov chain mixing times are tailored to worst-case initializations.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519964"}, {"primary_key": "1766440", "vector": [], "sparse_vector": [], "title": "Expanders via local edge flips in quasilinear time.", "authors": ["<PERSON>"], "summary": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (2005) proposed the following simple process, called flip-chain, for transforming any given connected d-regular graph into a d-regular expander: In each step, a random 3-path abcd is selected, and edges ab and cd are replaced by two new edges ac and bd, provided that ac and bd do not exist already. A motivation for the study of the flip-chain arises in the design of overlay networks, where it is common practice that adjacent nodes periodically exchange random neighbors, to maintain good connectivity properties. It is known that the flip-chain converges to the uniform distribution over connected d-regular graphs, and it is conjectured that an expander graph is obtained after O(ndlogn) steps, w.h.p., where n is the number of vertices. However, the best known upper bound on the number of steps is O(n2d2√logn), and the best bound on the mixing time of the chain is O(n16d36logn).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520022"}, {"primary_key": "1766441", "vector": [], "sparse_vector": [], "title": "Parallel repetition for all 3-player games over binary alphabet.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We prove that for every 3-player (3-prover) game, with binary questions and answers and value 0, such that the value of the n-fold parallel repetition of the game is at most n−c.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520071"}, {"primary_key": "1766442", "vector": [], "sparse_vector": [], "title": "Interactive error correcting codes over binary erasure channels resilient to &gt; ½ adversarial corruption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "An error correcting code (ECC) allows a sender to send a message to a receiver such that even if a constant fraction of the communicated bits are corrupted, the receiver can still learn the message correctly. Due to their importance and fundamental nature, ECC's have been extensively studied, one of the main goals being to maximize the fraction of errors that the ECC is resilient to.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519980"}, {"primary_key": "1766443", "vector": [], "sparse_vector": [], "title": "The optimal error resilience of interactive communication over binary channels.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In interactive coding, <PERSON> and <PERSON> wish to compute some function f of their individual private inputs x and y. They do this by engaging in a non-adaptive (fixed order, fixed length) interactive protocol to jointly compute f(x,y). The goal is to do this in an error-resilient way, such that even given some fraction of adversarial corruptions to the protocol, both parties still learn f(x,y).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519985"}, {"primary_key": "1766444", "vector": [], "sparse_vector": [], "title": "Hypercontractivity on high dimensional expanders.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove hypercontractive inequalities on high dimensional expanders. As in the settings of the p-biased hypercube, the symmetric group, and the <PERSON>mann scheme, our inequalities are effective for global functions, which are functions that are not significantly affected by a restriction of a small set of coordinates. As applications, we obtain Fourier concentration, small-set expansion, and <PERSON><PERSON><PERSON><PERSON> theorems for high dimensional expanders. Our techniques rely on a new approximate Efron–Stein decomposition for high dimensional link expanders.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520004"}, {"primary_key": "1766445", "vector": [], "sparse_vector": [], "title": "Algorithms and certificates for Boolean CSP refutation: smoothed is no harder than random.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present an algorithm for strongly refuting smoothed instances of all Boolean CSPs. The smoothed model is a hybrid between worst and average-case input models, where the input is an arbitrary instance of the CSP with only the negation patterns of the literals re-randomized with some small probability. For an n-variable smoothed instance of a k-arity CSP, our algorithm runs in n^O(ℓ) time, and succeeds with high probability in bounding the optimum fraction of satisfiable constraints away from 1, provided that the number of constraints is at least Õ(n) (n/ell)^(k/2 - 1). This matches, up to polylogarithmic factors in n, the trade-off between running time and the number of constraints of the state-of-the-art algorithms for refuting fully random instances of CSPs.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519955"}, {"primary_key": "1766446", "vector": [], "sparse_vector": [], "title": "Hop-constrained expander decompositions, oblivious routing, and distributed universal optimality.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper studies the fundamental task of establishing routing paths in distributed networks. We prove the existence of compact routing tables that store in each network-node few simple forwarding rules tracing out hop-constrained and oblivious routing paths for any pair of nodes. For any collection of pairs the congestion of these paths is almost-optimal, i.e., competitive with the globally optimal solution up to a sub-polynomial factor.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520026"}, {"primary_key": "1766447", "vector": [], "sparse_vector": [], "title": "On the complexity of two-party differential privacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ad <PERSON>"], "summary": "In distributed differential privacy, the parties perform analysis over their joint data while preserving the privacy for both datasets. Interestingly, for a few fundamental two-party functions such as inner product and Hamming distance, the accuracy of the distributed solution lags way behind what is achievable in the client-server setting. <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [FOCS '10] proved that this gap is inherent, showing upper bounds on the accuracy of (any) distributed solution for these functions. These limitations can be bypassed when settling for computational differential privacy, where the data is differentially private only in the eyes of a computationally bounded observer, using oblivious transfer.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519982"}, {"primary_key": "1766448", "vector": [], "sparse_vector": [], "title": "Improved communication complexity of fault-tolerant consensus.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "Consensus is one of the most thoroughly studied problems in distributed computing, yet there are still complexity gaps that have not been bridged for decades. In particular, in the classical message-passing setting with processes' crashes, since the seminal works of <PERSON><PERSON><PERSON> and <PERSON><PERSON> [PODC 1998] and As<PERSON><PERSON> and Waarts [SICOMP 1996, JACM 1998] in the previous century, there is still a fundamental unresolved question about communication complexity of fast randomized Consensus against a (strong) adaptive adversary crashing processes arbitrarily online. The best known upper bound on the number of communication bits is Θ(n3/2/√logn) per process, while the best lower bound is Ω(1). This is in contrast to randomized Consensus against a (weak) oblivious adversary, for which time-almost-optimal algorithms guarantee amortized O(1) communication bits per process. We design an algorithm against adaptive adversary that reduces the communication gap by nearly linear factor to O(√n· n) bits per process, while keeping almost-optimal (up to factor O(log3 n)) time complexity O(√n·log5/2 n).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520078"}, {"primary_key": "1766449", "vector": [], "sparse_vector": [], "title": "Near-optimal distributed degree+1 coloring.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a new approach to randomized distributed graph coloring that is simpler and more efficient than previous ones. In particular, it allows us to tackle the (deg+1)-list-coloring (D1LC) problem, where each node v of degree dv is assigned a palette of dv+1 colors, and the objective is to find a proper coloring using these palettes. While for (Δ+1)-coloring (where Δ is the maximum degree), there is a fast randomized distributed O(log3logn)-round algorithm due to <PERSON>, <PERSON>, and <PERSON><PERSON>, no o(logn)-round algorithms are known for the D1LC problem.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520023"}, {"primary_key": "1766450", "vector": [], "sparse_vector": [], "title": "Randomized communication and implicit graph representations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The most basic lower-bound question in randomized communication complexity is: Does a given problem have constant cost, or non-constant cost? We observe that this question has a deep connection to implicit graph representations in structural graph theory. Specifically, constant-cost communication problems correspond to hereditary graph families that admit constant-size adjacency sketches, or equivalently constant-size probabilistic universal graphs (PUGs), and these graph families are a subset of families that admit adjacency labeling schemes of size O(logn), which are the subject of the well-studied implicit graph question (IGQ).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519978"}, {"primary_key": "1766451", "vector": [], "sparse_vector": [], "title": "Optimizing strongly interacting fermionic Hamiltonians.", "authors": ["<PERSON>", "Ryan <PERSON>&<PERSON>;Donnell"], "summary": "The fundamental problem in much of physics and quantum chemistry is to optimize a low-degree polynomial in certain anticommuting variables. Being a quantum mechanical problem, in many cases we do not know an efficient classical witness to the optimum, or even to an approximation of the optimum. One prominent exception is when the optimum is described by a so-called \"Gaussian state\", also called a free fermion state. In this work we are interested in the complexity of this optimization problem when no good Gaussian state exists. Our primary testbed is the Sachdev–<PERSON> (SYK) model of random degree-q polynomials, a model of great current interest in condensed matter physics and string theory, and one which has remarkable properties from a computational complexity standpoint. Among other results, we give an efficient classical certification algorithm for upper-bounding the largest eigenvalue in the q=4 SYK model, and an efficient quantum certification algorithm for lower-bounding this largest eigenvalue; both algorithms achieve constant-factor approximations with high probability.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519960"}, {"primary_key": "1766452", "vector": [], "sparse_vector": [], "title": "Breaking the nk barrier for minimum k-cut on simple graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the minimum k-cut problem, we want to find the minimum number of edges whose deletion breaks the input graph into at least k connected components. The classic algorithm of <PERSON><PERSON> and <PERSON> runs in Õ(n2k−2) time, and recent, exciting developments have improved the running time to O(nk). For general, weighted graphs, this is tight assuming popular hardness conjectures.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519948"}, {"primary_key": "1766453", "vector": [], "sparse_vector": [], "title": "Verifying the unseen: interactive proofs for label-invariant distribution properties.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Given i.i.d. samples from an unknown distribution over a large domain [N], approximating several basic quantities, including the distribution's support size, its entropy, and its distance from the uniform distribution, requires Θ(N / logN) samples [Val<PERSON> and <PERSON>iant, STOC 2011].", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519987"}, {"primary_key": "1766454", "vector": [], "sparse_vector": [], "title": "A strong version of <PERSON><PERSON><PERSON>&apo<PERSON>;s theorem.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Let k,ℓ≥ 2 be two multiplicatively independent integers. <PERSON><PERSON><PERSON>'s famous theorem states that a set X⊆ ℕ is both k-recognizable and ℓ-recognizable if and only if it is definable in Presburger arithmetic. Here we show the following strengthening: let X⊆ ℕm be k-recognizable, let Y⊆ ℕn be ℓ-recognizable such that both X and Y are not definable in Presburger arithmetic. Then the first-order logical theory of (ℕ,+,X,Y) is undecidable. This is in contrast to a well-known theorem of <PERSON><PERSON><PERSON> that the first-order logical theory of (ℕ,+,X) is decidable.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519958"}, {"primary_key": "1766455", "vector": [], "sparse_vector": [], "title": "Efficient mean estimation with pure differential privacy via a sum-of-squares exponential mechanism.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We give the first polynomial-time algorithm to estimate the mean of a d-variate probability distribution with bounded covariance from Õ(d) independent samples subject to pure differential privacy. Prior algorithms for this problem either incur exponential running time, require Ω(d1.5) samples, or satisfy only the weaker concentrated or approximate differential privacy conditions. In particular, all prior polynomial-time algorithms require d1+Ω(1) samples to guarantee small privacy loss with \"cryptographically\" high probability, 1−2−dΩ(1), while our algorithm retains Õ(d) sample complexity even in this stringent setting.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519947"}, {"primary_key": "1766456", "vector": [], "sparse_vector": [], "title": "Matrix discrepancy from Quantum communication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We develop a novel connection between discrepancy minimization and (quantum) communication complexity. As an application, we resolve a substantial special case of the Matrix Spencer conjecture. In particular, we show that for every collection of symmetric n × n matrices A1,…,An with ||Ai|| ≤ 1 and ||Ai||F ≤ n1/4 there exist signs x ∈ { ± 1}n such that the maximum eigenvalue of ∑i ≤ n xi Ai is at most O(√n). We give a polynomial-time algorithm based on partial coloring and semidefinite programming to find such x.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519954"}, {"primary_key": "1766457", "vector": [], "sparse_vector": [], "title": "Byzantine agreement in polynomial time with near-optimal resilience.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "It has been known since the early 1980s that Byzantine Agreement in the full information, asynchronous model is impossible to solve deterministically against even one crash fault [FLP 1985], but that it can be solved with probability 1 [Ben-Or 1983], even against an adversary that controls the scheduling of all messages and corrupts up to f<n/3 players [<PERSON><PERSON><PERSON> 1987]. The main downside of [Ben-Or 1983, Bracha 1987] is that they terminate with 2Θ(n) latency in expectation whenever f=Θ(n).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520015"}, {"primary_key": "1766458", "vector": [], "sparse_vector": [], "title": "Robustness of average-case meta-complexity via pseudorandomness.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Ren", "<PERSON><PERSON>"], "summary": "We show broad equivalences in the average-case complexity of many different meta-complexity problems, including Kolmogorov complexity, time-bounded Kolmogorov complexity, and the Minimum Circuit Size Problem. These results hold for a wide range of parameters (various thresholds, approximation gaps, weak or strong average-case hardness, etc.) and complexity notions, showing the theory of meta-complexity is very *robust* in the average-case setting.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520051"}, {"primary_key": "1766459", "vector": [], "sparse_vector": [], "title": "Reproducibility in learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the notion of a reproducible algorithm in the context of learning. A reproducible learning algorithm is resilient to variations in its samples — with high probability, it returns the exact same output when run on two samples from the same underlying distribution. We begin by unpacking the definition, clarifying how randomness is instrumental in balancing accuracy and reproducibility. We initiate a theory of reproducible algorithms, showing how reproducibility implies desirable properties such as data reuse and efficient testability. Despite the exceedingly strong demand of reproducibility, there are efficient reproducible algorithms for several fundamental problems in statistics and learning. First, we show that any statistical query algorithm can be made reproducible with a modest increase in sample complexity, and we use this to construct reproducible algorithms for finding approximate heavy-hitters and medians. Using these ideas, we give the first reproducible algorithm for learning halfspaces via a reproducible weak learner and a reproducible boosting algorithm. Interestingly, we utilize a connection to foams as a higher-dimension randomized rounding scheme. Finally, we initiate the study of lower bounds and inherent tradeoffs for reproducible algorithms, giving nearly tight sample complexity upper and lower bounds for reproducible versus nonreproducible SQ algorithms.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519973"}, {"primary_key": "1766460", "vector": [], "sparse_vector": [], "title": "List-decodable covariance estimation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give the first polynomial time algorithm for \\emph{list-decodable covariance estimation}. For any $\\alpha > 0$, our algorithm takes input a sample $Y \\subseteq \\mathbb{R}^d$ of size $n\\geq d^{\\mathsf{poly}(1/\\alpha)}$ obtained by adversarially corrupting an $(1-\\alpha)n$ points in an i.i.d. sample $X$ of size $n$ from the Gaussian distribution with unknown mean $\\mu_*$ and covariance $\\Sigma_*$. In $n^{\\mathsf{poly}(1/\\alpha)}$ time, it outputs a constant-size list of $k = k(\\alpha)= (1/\\alpha)^{\\mathsf{poly}(1/\\alpha)}$ candidate parameters that, with high probability, contains a $(\\hat{\\mu},\\hat{\\Sigma})$ such that the total variation distance $TV(\\mathcal{N}(\\mu_*,\\Sigma_*),\\mathcal{N}(\\hat{\\mu},\\hat{\\Sigma}))<1-O_{\\alpha}(1)$. This is the statistically strongest notion of distance and implies multiplicative spectral and relative Frobenius distance approximation for parameters with dimension independent error. Our algorithm works more generally for $(1-\\alpha)$-corruptions of any distribution $D$ that possesses low-degree sum-of-squares certificates of two natural analytic properties: 1) anti-concentration of one-dimensional marginals and 2) hypercontractivity of degree 2 polynomials. Prior to our work, the only known results for estimating covariance in the list-decodable setting were for the special cases of list-decodable linear regression and subspace recovery due to Karmarkar, Klivans, and Kothari (2019), Raghavendra and Yau (2019 and 2020) and Bakshi and Kothari (2020). These results need superpolynomial time for obtaining any subconstant error in the underlying dimension. Our result implies the first polynomial-time \\emph{exact} algorithm for list-decodable linear regression and subspace recovery that allows, in particular, to obtain $2^{-\\mathsf{poly}(d)}$ error in polynomial-time. Our result also implies an improved algorithm for clustering non-spherical mixtures.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520006"}, {"primary_key": "1766461", "vector": [], "sparse_vector": [], "title": "Approximate counting and sampling via local central limit theorems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give an FPTAS for computing the number of matchings of size k in a graph G of maximum degree Δ on n vertices, for all k ≤ (1−δ)m*(G), where δ>0 is fixed and m*(G) is the matching number of G, and an FPTAS for the number of independent sets of size k ≤ (1−δ) αc(Δ) n, where αc(Δ) is the NP-hardness threshold for this problem. We also provide quasi-linear time randomized algorithms to approximately sample from the uniform distribution on matchings of size k ≤ (1−δ)m*(G) and independent sets of size k ≤ (1−δ)αc(Δ)n.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519957"}, {"primary_key": "1766462", "vector": [], "sparse_vector": [], "title": "Improved iteration complexities for overconstrained p-norm regression.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we obtain improved iteration complexities for solving ℓp regression. We provide methods which given any full-rank A ∈ ℝn × d with n ≥ d, b ∈ ℝn, and p ≥ 2 solve minx ∈ ℝd ||A x − b||p to high precision in time dominated by that of solving Op(dp−2/3p−2) linear systems in A⊤D A for positive diagonal matrices D. This improves upon the previous best iteration complexity of Op(np−2/3p−2) (<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> 2019). As a corollary, we obtain an O(d1/3є−2/3) iteration complexity for approximate ℓ∞ regression. Further, for q ∈ (1, 2] and dual norm q = p/(p−1) we provide an algorithm that solves ℓq regression in O(dp−2/2p−2) iterations.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519971"}, {"primary_key": "1766463", "vector": [], "sparse_vector": [], "title": "Lossy planarization: a constant-factor approximate kernelization for planar vertex deletion.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In the F-minor-free deletion problem we are given an undirected graph G and the goal is to find a minimum vertex set that intersects all minor models of graphs from the family F. This captures numerous important problems including Vertex cover, Feedback vertex set, Treewidth-η modulator, and Vertex planarization. In the latter one, we ask for a minimum vertex set whose removal makes the graph planar. This is a special case of F-minor-free deletion for the family F = {K5, K3,3}.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520021"}, {"primary_key": "1766464", "vector": [], "sparse_vector": [], "title": "Tight dynamic problem lower bounds from generalized BMM and OMv.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> Xu"], "summary": "Popular fine-grained hypotheses have been successful in proving conditional lower bounds for many dynamic problems. Two of the most widely applicable hypotheses in this context are the combinatorial Boolean Matrix Multiplication (BMM) hypothesis and the closely-related Online Matrix Vector Multiplication (OMv) hypothesis. The main theme of this paper is using k-dimensional generalizations of these two hypotheses to prove new tight conditional lower bounds for dynamic problems.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520036"}, {"primary_key": "1766465", "vector": [], "sparse_vector": [], "title": "Combinatorics via closed orbits: number theoretic <PERSON><PERSON><PERSON> graphs are not unique neighbor expanders.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The question of finding expander graphs with strong vertex expansion properties such as unique neighbor expansion and lossless expansion is central to computer science. A barrier to constructing these is that strong notions of expansion could not be proven via the spectral expansion paradigm.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519992"}, {"primary_key": "1766466", "vector": [], "sparse_vector": [], "title": "Subquadratic dynamic path reporting in directed graphs against an adaptive adversary.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study reachability and shortest paths problems in dynamic directed graphs. Whereas algebraic dynamic data structures supporting edge updates and reachability/distance queries have been known for quite a long time, they do not, in general, allow reporting the underlying paths within the same time bounds, especially against an adaptive adversary.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520058"}, {"primary_key": "1766467", "vector": [], "sparse_vector": [], "title": "An improved approximation algorithm for the minimum k-edge connected multi-subgraph problem.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a randomized 1+5.06/√k-approximation algorithm for the minimum k-edge connected spanning multi-subgraph problem, k-ECSM.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520062"}, {"primary_key": "1766468", "vector": [], "sparse_vector": [], "title": "Dynamic suffix array with polylogarithmic queries and updates.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The suffix array $SA[1..n]$ of a text $T$ of length $n$ is a permutation of $\\{1,\\ldots,n\\}$ describing the lexicographical ordering of suffixes of $T$, and it is considered to be among of the most important data structures in string algorithms, with dozens of applications in data compression, bioinformatics, and information retrieval. One of the biggest drawbacks of the suffix array is that it is very difficult to maintain under text updates: even a single character substitution can completely change the contents of the suffix array. Thus, the suffix array of a dynamic text is modelled using suffix array queries, which return the value $SA[i]$ given any $i\\in[1..n]$. Prior to this work, the fastest dynamic suffix array implementations were by <PERSON> and <PERSON>. At ISAAC 2020, they showed how to answer suffix array queries in $\\tilde{O}(k)$ time, where $k\\in[1..n]$ is a trade-off parameter, with $\\tilde{O}(\\frac{n}{k})$-time text updates. In a very recent preprint [2021], they also provided a solution with $O(\\log^5 n)$-time queries and $\\tilde{O}(n^{2/3})$-time updates. We propose the first data structure that supports both suffix array queries and text updates in $O({\\rm polylog}\\,n)$ time (achieving $O(\\log^4 n)$ and $O(\\log^{3+o(1)} n)$ time, respectively). Our data structure is deterministic and the running times for all operations are worst-case. In addition to the standard single-character edits (character insertions, deletions, and substitutions), we support (also in $O(\\log^{3+o(1)} n)$ time) the \"cut-paste\" operation that moves any (arbitrarily long) substring of $T$ to any place in $T$. We complement our structure by a hardness result: unless the Online Matrix-Vector Multiplication (OMv) Conjecture fails, no data structure with $O({\\rm polylog}\\,n)$-time suffix array queries can support the \"copy-paste\" operation in $O(n^{1-\\epsilon})$ time for any $\\epsilon>0$.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520061"}, {"primary_key": "1766469", "vector": [], "sparse_vector": [], "title": "Online edge coloring via tree recurrences and correlation decay.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give an online algorithm that with high probability computes a (e/e−1 + o(1))Δ edge coloring on a graph G with maximum degree Δ = ω(logn) under online edge arrivals against oblivious adversaries, making first progress on the conjecture of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> in this general setting. Our algorithm is based on reducing to a matching problem on locally treelike graphs, and then applying a tree recurrences based approach for arguing correlation decay.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519986"}, {"primary_key": "1766470", "vector": [], "sparse_vector": [], "title": "A characterization of approximability for biased CSPs.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Lee", "Suprovat Ghoshal"], "summary": "A µ-biased Max-CSP instance with predicate ψ:{0,1}r → {0,1} is an instance of Constraint Satisfaction Problem (CSP) where the objective is to find a labeling of relative weight at most µ which satisfies the maximum fraction of constraints. Biased CSPs are versatile and express several well studied problems such as Densest-k-Sub(Hyper)graph and SmallSetExpansion.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520072"}, {"primary_key": "1766471", "vector": [], "sparse_vector": [], "title": "3.1n - o(n) circuit lower bounds for explicit functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Proving circuit lower bounds has been an important but extremely hard problem for decades. Although it can be shown that almost every function f:F2n→F2 requires circuit of size Ω(2n/n) by a simple counting argument, it remains unknown whether there is an explicit function (for example, a function in NP) not computable by circuits of size 10n. In fact, a 3n−o(n) explicit lower bound by <PERSON><PERSON> (<PERSON><PERSON>, 1984) was unbeaten for over 30 years until a recent breakthrough by <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (FOCS, 2016), which proved a (3+1/86)n−o(n) lower bound for affine dispersers, a class of functions known to be constructible in P. To obtain this improvement, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (FOCS, 2016) generalized the classical gate elimination method by keeping track of a bottleneck structure called troubled gates.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519976"}, {"primary_key": "1766472", "vector": [], "sparse_vector": [], "title": "Clustering mixtures with almost optimal separation in polynomial time.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the problem of clustering mixtures of mean-separated Gaussians in high dimensions. We are given samples from a mixture of k identity covariance Gaussians, so that the minimum pairwise distance between any two pairs of means is at least Δ, for some parameter Δ > 0, and the goal is to recover the ground truth clustering of these samples. It is folklore that separation Δ = Θ (√logk) is both necessary and sufficient to recover a good clustering (say with constant or 1/poly(k) error), at least information theoretically. However, the estimators which achieve this guarantee are inefficient. We give the first algorithm which runs in polynomial time, and which almost matches this guarantee. More precisely, we give an algorithm which takes polynomially many samples and time, and which can successfully recover a good clustering, so long as the separation is Δ = Ω (log1/2 + c k), for any c > 0. Previously, polynomial time algorithms were only known for this problem when the separation was polynomial in k, and all algorithms which could tolerate poly logk separation required quasipolynomial time. We also extend our result to mixtures of translations of a distribution which satisfies the <PERSON><PERSON><PERSON><PERSON> inequality, under additional mild assumptions.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520012"}, {"primary_key": "1766473", "vector": [], "sparse_vector": [], "title": "Testing thresholds for high-dimensional sparse random geometric graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The random geometric graph model GRGd(n,p) is a distribution over graphs in which the edges capture a latent geometry. To sample G ∼ GRGd(n,p), we identify each of our n vertices with an independently and uniformly sampled vector from the d-dimensional unit sphere Sd−1, and we connect pairs of vertices whose vectors are \"sufficiently close,\" such that the marginal probability of an edge is p. Because of the underlying geometry, this model is natural for applications in data science and beyond.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519989"}, {"primary_key": "1766474", "vector": [], "sparse_vector": [], "title": "Simple parallel algorithms for single-site dynamics.", "authors": ["Hong<PERSON> Liu", "<PERSON><PERSON><PERSON>"], "summary": "The single-site dynamics are a canonical class of Markov chains for sampling from high-dimensional probability distributions, e.g. the ones represented by graphical models.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519999"}, {"primary_key": "1766475", "vector": [], "sparse_vector": [], "title": "Fixed-parameter tractability of graph isomorphism in graphs with an excluded minor.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove that Graph Isomorphism and Canonization in graphs excluding a fixed graph H as a minor can be solved by an algorithm working in time f(H)· nO(1), where f is some function. In other words, we show that these problems are fixed-parameter tractable when parameterized by the size of the excluded minor, with the caveat that the bound on the running time is not necessarily computable. The underlying approach is based on decomposing the graph in a canonical way into unbreakable (intuitively, well-connected) parts, which essentially provides a reduction to the case where the given H-minor-free graph is unbreakable itself. This is complemented by an analysis of unbreakable H-minor-free graphs, which reveals that every such graph can be canonically decomposed into a part that admits few automorphisms and a part that has bounded treewidth.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520076"}, {"primary_key": "1766476", "vector": [], "sparse_vector": [], "title": "Explainable k-means: don&apos;t be greedy, plant bigger trees!", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We provide a new bi-criteria Õ(log2 k) competitive algorithm for explainable k-means clustering. Explainable k-means was recently introduced by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> (ICML 2020). It is described by an easy to interpret and understand (threshold) decision tree or diagram. The cost of the explainable k-means clustering equals to the sum of costs of its clusters; and the cost of each cluster equals the sum of squared distances from the points in the cluster to the center of that cluster. The best non bi-criteria algorithm for explainable clustering Õ(k) competitive, and this bound is tight.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520056"}, {"primary_key": "1766477", "vector": [], "sparse_vector": [], "title": "Nonlocal games, compression theorems, and the arithmetical hierarchy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We investigate the connection between the complexity of nonlocal games and the arithmetical hierarchy, a classification of languages according to the complexity of arithmetical formulas defining them. It was recently shown by <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON> that deciding whether the (finite-dimensional) quantum value of a nonlocal game is $1$ or at most $\\frac{1}{2}$ is complete for the class $\\Sigma_1$ (i.e., $\\mathsf{RE}$). A result of <PERSON><PERSON><PERSON><PERSON> implies that deciding whether the commuting operator value of a nonlocal game is equal to $1$ is complete for the class $\\Pi_1$ (i.e., $\\mathsf{coRE}$). We prove that deciding whether the quantum value of a two-player nonlocal game is exactly equal to $1$ is complete for $\\Pi_2$; this class is in the second level of the arithmetical hierarchy and corresponds to formulas of the form \"$\\forall x \\, \\exists y \\, \\phi(x,y)$\". This shows that exactly computing the quantum value is strictly harder than approximating it, and also strictly harder than computing the commuting operator value (either exactly or approximately). We explain how results about the complexity of nonlocal games all follow in a unified manner from a technique known as compression. At the core of our $\\Pi_2$-completeness result is a new \"gapless\" compression theorem that holds for both quantum and commuting operator strategies. Our compression theorem yields as a byproduct an alternative proof of <PERSON><PERSON><PERSON><PERSON>'s result that the set of quantum correlations is not closed. We also show how a \"gap-preserving\" compression theorem for commuting operator strategies would imply that approximating the commuting operator value is complete for $\\Pi_1$.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519949"}, {"primary_key": "1766478", "vector": [], "sparse_vector": [], "title": "Matrix anti-concentration inequalities with applications.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We study m by m random matrices M with jointly Gaussian entries. Assuming a global small-ball probability bound", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520060"}, {"primary_key": "1766479", "vector": [], "sparse_vector": [], "title": "Asymptotically good Quantum and locally testable classical LDPC codes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study classical and quantum LDPC codes of constant rate obtained by the lifted product construction over non-abelian groups. We show that the obtained families of quantum LDPC codes are asymptotically good, which proves the qLDPC conjecture. Moreover, we show that the produced classical LDPC codes are also asymptotically good and locally testable with constant query and soundness parameters, which proves a well-known conjecture in the field of locally testable codes.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520017"}, {"primary_key": "1766480", "vector": [], "sparse_vector": [], "title": "Nearly optimal vertex fault-tolerant spanners in optimal time: sequential, distributed, and parallel.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We (nearly) settle the time complexity for computing vertex fault-tolerant (VFT) spanners with optimal sparsity (up to polylogarithmic factors). VFT spanners are sparse subgraphs that preserve distance information, up to a small multiplicative stretch, in the presence of vertex failures. These structures were introduced by [<PERSON><PERSON><PERSON> et al., STOC 2009] and have received a lot of attention since then.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520047"}, {"primary_key": "1766481", "vector": [], "sparse_vector": [], "title": "Sparsified block elimination for directed laplacians.", "authors": ["<PERSON>", "Zhu<PERSON><PERSON> Song"], "summary": "We show that the sparsified block elimination algorithm for solving undirected Laplacian linear systems from [Kyng-Lee-Pen<PERSON>-<PERSON>chdeva-Spielman STOC'16] directly works for directed Laplacians. Given access to a sparsification algorithm that, on graphs with n vertices and m edges, takes time TS(m) to output a sparsifier with NS(n) edges, our algorithm solves a directed Eulerian system on n vertices and m edges to є relative accuracy in time O(TS(m) + NS(n)lognlog(n/є)) + Õ(TS(NS(n)) logn), where the Õ(·) notation hides loglog(n) factors. By previous results, this implies improved runtimes for linear systems in strongly connected directed graphs, PageRank matrices, and asymmetric M-matrices. When combined with slower constructions of smaller Eulerian sparsifiers based on short cycle decompositions, it also gives a solver algorithm that, after pre-processing the matrix in O(n2 logO(1) n) time, takes O(n log5n log(n / є)) time per solve. At the core of our analyses are constructions of augmented matrices whose Schur complements encode error matrices.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520053"}, {"primary_key": "1766482", "vector": [], "sparse_vector": [], "title": "Optimal vertex connectivity oracles.", "authors": ["<PERSON>", "Thatchaphol <PERSON>", "<PERSON><PERSON>"], "summary": "A k-vertex connectivity oracle for undirected G is a data structure that, given u,v∈ V(G), reports min{k,κ(u,v)}, where κ(u,v) is the pairwise vertex connectivity between u,v. There are three main measures of efficiency: construction time, query time, and space. Prior work of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [Inf. Process. Lett. 2012] shows that a data structure of total size O(knlogn), which can even be encoded as a O(klog3 n)-bit labeling scheme, can answer vertex-connectivity queries in O(klogn) time. The construction time is polynomial, but unspecified.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519945"}, {"primary_key": "1766483", "vector": [], "sparse_vector": [], "title": "Proving as fast as computing: succinct arguments with constant prover overhead.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Succinct arguments are proof systems that allow a powerful, but untrusted, prover to convince a weak verifier that an input x belongs to a language L ∈ NP, with communication that is much shorter than the NP witness. Such arguments, which grew out of the theory literature, are now drawing immense interest also in practice, where a key bottleneck that has arisen is the high computational cost of proving correctness.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519956"}, {"primary_key": "1766484", "vector": [], "sparse_vector": [], "title": "Undirected (1+ε)-shortest paths via minor-aggregates: near-optimal deterministic parallel and distributed algorithms.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents near-optimal deterministic parallel and distributed algorithms for computing (1+eps)-approximate single-source shortest paths in any undirected weighted graph.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520074"}, {"primary_key": "1766485", "vector": [], "sparse_vector": [], "title": "The approximate degree of DNF and CNF formulas.", "authors": ["<PERSON>"], "summary": "The approximate degree of a Boolean function f∶{0,1}n→{0,1} is the minimum degree of a real polynomial p that approximates f pointwise: |f(x)−p(x)|≤1/3 for all x∈{0,1}n. For any δ>0, we construct DNF and CNF formulas of polynomial size with approximate degree Ω(n1−δ), essentially matching the trivial upper bound of n. This fully resolves the approximate degree of constant-depth circuits (AC0), a question that has seen extensive research over the past 10 years. Prior to our work, an Ω(n1−δ) lower bound was known only for AC0 circuits of depth that grows with 1/δ (<PERSON><PERSON> and <PERSON><PERSON><PERSON>, FOCS 2017). Furthermore, the DNF and CNF formulas that we construct are the simplest possible in that they have constant width.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520000"}, {"primary_key": "1766486", "vector": [], "sparse_vector": [], "title": "Public-key Quantum money with a classical bank.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Quantum money is a main primitive in quantum cryptography, that enables a bank to distribute to parties in the network, called wallets, unclonable quantum banknotes that serve as a medium of exchange between wallets. While quantum money suggests a theoretical solution to some of the fundamental problems in currency systems, it still requires a strong model to be implemented; quantum computation and a quantum communication infrastructure. A central open question in this context is whether we can have a quantum money scheme that uses \"minimal quantumness\", namely, local quantum computation and only classical communication.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519952"}, {"primary_key": "1766487", "vector": [], "sparse_vector": [], "title": "Memory bounds for the experts problem.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Online learning with expert advice is a fundamental problem of sequential prediction. In this problem, the algorithm has access to a set of n \"experts\" who make predictions on each day. The goal on each day is to process these predictions, and make a prediction with the minimum cost. After making a prediction, the algorithm sees the actual outcome on that day, updates its state, and then moves on to the next day. An algorithm is judged by how well it does compared to the best expert in the set.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520069"}, {"primary_key": "1766488", "vector": [], "sparse_vector": [], "title": "(Fractional) online stochastic matching via fine-grained offline statistics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by display advertising on the internet, the online stochastic matching problem is proposed by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (FOCS 2009). Consider a stochastic bipartite graph with offline vertices on one side and with i.i.d. online vertices on the other side. The algorithm knows the offline vertices and the distribution of the online vertices in advance. Upon the arrival of each online vertex, its type is realized and the algorithm immediately and irrevocably decides how to match it. In the vertex-weighted version of the problem, each offline vertex is associated with a weight and the goal is to maximize the total weight of the matching.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3519994"}, {"primary_key": "1766489", "vector": [], "sparse_vector": [], "title": "Set-multilinear and non-commutative formula lower bounds for iterated matrix multiplication.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An Algebraic Formula for a polynomial P [x1 xN] is an algebraic expression for P(x1 xN) using variables, field constants, additions and multiplications. Such formulas capture an algebraic analog of the Boolean complexity class NC1. Proving lower bounds against this model is thus an important problem. It is known that, to prove superpolynomial lower bounds against algebraic formulas, it suffices to prove good enough lower bounds against restricted kinds of formulas known as Set-Multilinear formulas, for computing a polynomial P(x1 xN) of degree O(logN/loglogN). In the past, many superpolynomial lower bounds were found, but they are of the form ω(f(d) poly(N)) (where f is typically a subexponential function) which is insufficient to get lower bounds for general formulas. Recently, the authors proved the first non-FPT lower bounds, i.e., a lower bound of the form Nω(f(d)), against small-depth set-multilinear formulas (and also for circuits). In this work, we extend this result in two directions. Large-depth set-multilinear formulas. In the setting of general set-multilinear formulas, we prove a lower bound of (logn)ω(logd) for computing the Iterated Matrix Multiplication polynomial IMMn,d. In particular, this implies the first superpolynomial lower bound against unbounded-depth set-multilinear formulas computing IMMn,n. As a corollary, this resolves the homogeneous version of a question of <PERSON><PERSON> (asked in 1991) regarding the relative power of Algebraic formulas and Branching programs in the non-commutative setting. Stronger bounds for homogeneous non-commutative small-depth circuits. In the small-depth homogeneous non-commutative case, we prove a lower bound of nd1/\"/2O(\"), which yields non-FPT bounds for depths up to o(logd). In comparison, our previous bound works in the harder commutative set-multilinear setting, but only up to depths o(loglogd). Moreover, our lower bound holds for all values of d, as opposed to the previous set-multilinear lower bound, which holds as long as d is small, i.e., d = O(logn).", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520044"}, {"primary_key": "1766490", "vector": [], "sparse_vector": [], "title": "Edge sampling and graph parameter estimation via vertex neighborhood accesses.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we consider the problems from the area of sublinear-time algorithms of edge sampling, edge counting, and triangle counting. Part of our contribution is that we consider three different settings, differing in the way in which one may access the neighborhood of a given vertex. In previous work, people have considered indexed neighbor access, with a query returning the i-th neighbor of a given vertex. Full neighborhood access model, which has a query that returns the entire neighborhood at a unit cost, has recently been considered in the applied community. Between these, we propose hash-ordered neighbor access, inspired by coordinated sampling, where we have a global fully random hash function, and can access neighbors in order of their hash values, paying a constant for each accessed neighbor. For edge sampling and counting, our new lower bounds are in the most powerful full neighborhood access model. We provide matching upper bounds in the weaker hash-ordered neighbor access model. Our new faster algorithms can be provably implemented efficiently on massive graphs in external memory and with the current APIs for, e.g., Twitter or Wikipedia. For triangle counting, we provide a separation: a better upper bound with full neighborhood access than the known lower bounds with indexed neighbor access. The technical core of our paper is our edge-sampling algorithm on which the other results depend. We now describe our results on the classic problems of edge and triangle counting. We give an algorithm that uses hash-ordered neighbor access to approximately count edges in time Õ(n/\"m + 1/\"2) (compare to the state of the art without hash-ordered neighbor access of Õ(n/\"2 m) by <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> [ICALP 2017]). We present an ω(n/\"m) lower bound for \"≥m/n in the full neighborhood access model. This improves the lower bound of ω(n/s\"m) by <PERSON>reich and <PERSON> [Rand. Struct. <PERSON>g. 2008]) and it matches our new upper bound for \"≥ m/n. We also show an algorithm that uses the more standard assumption of pair queries (\"are the vertices u and v adjacent?\"), with time complexity of Õ(n/\"m + 1/\"4). This matches our lower bound for \"≥ m1/6/n1/3. Finally, we focus on triangle counting. For this, we use the full power of the full neighbor access. In the indexed neighbor model, an algorithm that makes Õ(n/\"10/3 T1/3 + min(m,m3/2/\"3 T)) queries for T being the number of triangles, is known and this is known to be the best possible up to the dependency on \"(Eden, Levi, Ron, and Seshadhri [FOCS 2015]). We improve this significantly to Õ(min(n,n/\"T1/3 + n m/\"2 T)) full neighbor accesses, thus showing that the full neighbor access is fundamentally stronger for triangle counting than the weaker indexed neighbor model. We also give a lower bound, showing that this is the best possible with full neighborhood access, in terms of n,m,T.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520059"}, {"primary_key": "1766491", "vector": [], "sparse_vector": [], "title": "Computational complexity of the ground state energy density problem.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study the complexity of finding the ground state energy density of a local Hamiltonian on a lattice in the thermodynamic limit of infinite lattice size. We formulate this rigorously as a function problem, in which we request an estimate of the ground state energy density to some specified precision; and as an equivalent promise problem, GSED, in which we ask whether the ground state energy density is above or below specified thresholds.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520052"}, {"primary_key": "1766492", "vector": [], "sparse_vector": [], "title": "Explicit binary tree codes with sub-logarithmic size alphabet.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Since they were first introduced by <PERSON><PERSON><PERSON> (STOC 1993), the construction of tree codes remained an elusive open problem. The state-of-the-art construction by <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC 2018) has constant distance and (logn)e colors for some constant e > 1 that depends on the distance, where n is the depth of the tree. Insisting on a constant number of colors at the expense of having vanishing distance, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> (SODA 2016) constructed a distance Ω(1/logn) tree code.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935.3520033"}, {"primary_key": "1783347", "vector": [], "sparse_vector": [], "title": "STOC &apos;22: 54th Annual ACM SIGACT Symposium on Theory of Computing, Rome, Italy, June 20 - 24, 2022", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The papers in this volume were presented at the Fifty Fourth Annual ACM Symposium on Theory of Computing (STOC 2022), sponsored by the ACM Special Interest Group on Algorithms and Computation Theory (SIGACT). The conference was held in Rome. The papers were presented as live talks during sessions held between June 20-24, 2022. STOC 2022 was part of Theory Fest, which included six workshops with introductory tutorials, poster sessions, social events, and a special joint session with \"Accademia Nazionale dei Lincei\", the oldest and most prestigious Italian academic institution. There were keynote lectures by <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>.", "published": "2022-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3519935"}]