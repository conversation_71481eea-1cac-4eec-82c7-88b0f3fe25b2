[{"primary_key": "379532", "vector": [], "sparse_vector": [], "title": "Sensor2Text: Enabling Natural Language Interactions for Daily Activity Tracking Using Wearable Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Visual Question-Answering, a technology that generates textual responses from an image and natural language question, has progressed significantly. Notably, it can aid in tracking and inquiring about daily activities, crucial in healthcare monitoring, especially for elderly patients or those with memory disabilities. However, video poses privacy concerns and has a limited field of view. This paper presents Sensor2Text, a model proficient in tracking daily activities and engaging in conversations using wearable sensors. The approach outlined here tackles several challenges, including low information density in wearable sensor data, insufficiency of single wearable sensors in human activities recognition, and model's limited capacity for Question-Answering and interactive conversations. To resolve these obstacles, transfer learning and student-teacher networks are utilized to leverage knowledge from visual-language models. Additionally, an encoder-decoder neural network model is devised to jointly process language and sensor data for conversational purposes. Furthermore, Large Language Models are also utilized to enable interactive capabilities. The model showcases the ability to identify human activities and engage in Q&amp;A dialogues using various wearable sensor modalities. It performs comparably to or better than existing visual-language models in both captioning and conversational tasks. To our knowledge, this represents the first model capable of conversing about wearable sensor data, offering an innovative approach to daily activity tracking that addresses privacy and field-of-view limitations associated with current vision-based solutions.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699747"}, {"primary_key": "379624", "vector": [], "sparse_vector": [], "title": "Technology which Makes You Think: The Reflection, Rumination and Thought in Technology Scale.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Reflection is widely regarded as a key design goal for technologies for well-being. Yet, recent research shows that technologies for reflection may have negative consequences, in the form of rumination, i.e. negative thought cycles. Understanding how technologies support thinking about oneself, which can take the form of rumination and reflection, is key for future well-being technologies. To address this research gap, we developed the Reflection, Rumination and Thought in Technology (R2T2) scale. Contrary to past research, R2T2 addresses ways of self-focused thinking beyond reflection. This scale can quantify how a technology supports self-focused thinking and the rumination and reflection aspects of that thinking. We developed the scale through a systematic scale development process. We then evaluated the scale's test-retest reliability along with its concurrent and discriminant validity. R2T2 enables designers and researchers to compare technologies which embrace self-focused thinking and its facets as a design goal.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659615"}, {"primary_key": "379656", "vector": [], "sparse_vector": [], "title": "Move, Connect, Interact: Introducing a Design Space for Cross-Traffic Interaction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Rising diversity through novel forms of mobility and increasing connectivity through intelligent systems and wireless connection is leading to a complex traffic environment. However, traditional automotive interface research often focuses on the interaction between vehicle and driver, passenger, or pedestrian, not capturing the interconnected relationships among various traffic participants. Therefore, we developed a design space for Cross-Traffic Interaction (CTI) based on a focus group with six HCI experts, encompassing the dimensions: (1) interaction partners, (2) their traffic situations, and (3) their interaction relationship. Through a systematic literature review, we classified 116 publications, showing less-studied interaction possibilities. Illustrating the practical application of our design space, we developed three interactive prototypical applications: Shooting Stars, Flow Rider, and Road Reels. A study (N=12) shows that the applications were received well and could improve traffic experience. Overall, our design space serves as a foundational tool for understanding and exploring the challenges and diverse opportunities within CTI, bridging the gap between traditional automotive interface research and the complex realities of modern traffic environments.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678580"}, {"primary_key": "379659", "vector": [], "sparse_vector": [], "title": "TRAMBA: A Hybrid Transformer and Mamba Architecture for Practical Audio and Bone Conduction Speech Super Resolution and Enhancement on Mobile and Wearable Platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jun<PERSON> Xi<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose TRAMBA, a hybrid transformer and Mamba architecture for acoustic and bone conduction speech enhancement, suitable for mobile and wearable platforms. Bone conduction speech enhancement has been impractical to adopt in mobile and wearable platforms for several reasons: (i) data collection is labor-intensive, resulting in scarcity; (ii) there exists a performance gap between state-of-art models with memory footprints of hundreds of MBs and methods better suited for resource-constrained systems. To adapt TRAMBA to vibration-based sensing modalities, we pre-train TRAMBA with audio speech datasets that are widely available. Then, users fine-tune with a small amount of bone conduction data. TRAMBA outperforms state-of-art GANs by up to 7.3% in Perceptual Evaluation of Speech Quality (PESQ) and 1.8% in Short-Time Objective Intelligibility (STOI), with an order of magnitude smaller memory footprint and an inference speed up of up to 465 times. We integrate TRAMBA into real systems and show that TRAMBA (i) improves battery life of wearables by up to 160% by requiring less data sampling and transmission; (ii) generates higher quality voice in noisy environments than over-the-air speech; (iii) requires a memory footprint of less than 20.0 MB.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699757"}, {"primary_key": "379517", "vector": [], "sparse_vector": [], "title": "HyperHAR: Inter-sensing Device Bilateral Correlations and Hyper-correlations Learning Approach for Wearable Sensing Device Based Human Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Human activity recognition (HAR) has emerged as a prominent research field in recent years. Current HAR models are only able to model bilateral correlations between two sensing devices for feature extraction. However, for some activities, exploiting correlations among more than two sensing devices, which we call hyper-correlations in this paper, is essential for extracting discriminatory features. In this work, we propose a novel HyperHAR framework that automatically models both bilateral and hyper-correlations among sensing devices. The HyperHAR consists of three modules. The Intra-sensing Device Feature Extraction Module generates latent representation across the data of each sensing device, based on which the Inter-sensing Device Multi-order Correlations Learning Module simultaneously learns both bilateral correlations and hyper-correlations. Lastly, the Information Aggregation Module generates a representation for an individual sensing device by aggregating the bilateral correlations and hyper-correlations it involves in. It also generates the representation for a pair of sensing devices by aggregating the hyper-correlations between the pair and other different individual sensing devices. We also propose a computationally more efficient HyperHAR-Lite framework, a lightweight variant of the HyperHAR framework, at a small cost of accuracy. Both the HyperHAR and HyperHAR-Lite outperform SOTA models across three commonly used benchmark datasets with significant margins. We validate the efficiency and effectiveness of the proposed frameworks through an ablation study and quantitative and qualitative analysis.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643511"}, {"primary_key": "379518", "vector": [], "sparse_vector": [], "title": "Lateralization Effects in Electrodermal Activity Data Collected Using Wearable Devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Electrodermal activity (EDA) is a physiological signal that can be used to infer humans' affective states and stress levels. EDA can nowadays be monitored using unobtrusive wearable devices, such as smartwatches, and leveraged in personal informatics systems. A still largely uncharted issue concerning EDA is the impact on real applications of potential differences observable on signals measured concurrently on the left and right side of the human body. This phenomenon, called lateralization, originates from the distinct functions that the brain's left and right hemispheres exert on EDA. In this work, we address this issue by examining the impact of EDA lateralization in two classification tasks: a cognitive load recognition task executed in the lab and a sleep monitoring task in a real-world setting. We implement a machine learning pipeline to compare the performance obtained on both classification tasks using EDA data collected from the left and right sides of the body. Our results show that using EDA from the side that is not associated with the specific hemisphere activation leads to a significant decline in performance for the considered classification tasks. This finding highlights that researchers and practitioners relying on EDA data should consider possible EDA lateralization effects when deciding on sensor placement.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643541"}, {"primary_key": "379524", "vector": [], "sparse_vector": [], "title": "UbiHR: Resource-efficient Long-range Heart Rate Sensing on Ubiquitous Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ubiquitous on-device heart rate sensing is vital for high-stress individuals and chronic patients. Non-contact sensing, compared to contact-based tools, allows for natural user monitoring, potentially enabling more accurate and holistic data collection. However, in open and uncontrolled mobile environments, user movement and lighting introduce noises. Existing methods, such as curve-based or short-range deep learning recognition based on adjacent frames, strike the optimal balance between real-time performance and accuracy, especially under limited device resources. In this paper, we present UbiHR, a ubiquitous device-based heart rate sensing system. Key to UbiHR is a real-time long-range spatio-temporal model enabling noise-independent heart rate recognition and display on commodity mobile devices, along with a set of mechanisms for prompt and energy-efficient sampling and preprocessing. Diverse experiments and user studies involving four devices, four tasks, and 80 participants demonstrate UbiHR's superior performance, enhancing accuracy by up to 74.2% and reducing latency by 51.2%.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699771"}, {"primary_key": "379525", "vector": [], "sparse_vector": [], "title": "Body-Area Capacitive or Electric Field Sensing for Human Activity Recognition and Human-Computer Interaction: A Comprehensive Survey.", "authors": ["Sizhen Bian", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Due to the fact that roughly sixty percent of the human body is essentially composed of water, the human body is inherently a conductive object, being able to, firstly, form an inherent electric field from the body to the surroundings and secondly, deform the distribution of an existing electric field near the body. Body-area capacitive sensing, also called body-area electric field sensing, is becoming a promising alternative for wearable devices to accomplish certain tasks in human activity recognition (HAR) and human-computer interaction (HCI). Over the last decade, researchers have explored plentiful novel sensing systems backed by the body-area electric field, like the ring-form smart devices for sign language recognition, the room-size capacitive grid for indoor positioning, etc. On the other hand, despite the pervasive exploration of the body-area electric field, a comprehensive survey does not exist for an enlightening guideline. Moreover, the various hardware implementations, applied algorithms, and targeted applications result in a challenging task to achieve a systematic overview of the subject. This paper aims to fill in the gap by comprehensively summarizing the existing works on body-area capacitive sensing so that researchers can have a better view of the current exploration status. To this end, we first sorted the explorations into three domains according to the involved body forms: body-part electric field, whole-body electric field, and body-to-body electric field, and enumerated the state-of-art works in the domains with a detailed survey of the backed sensing tricks and targeted applications. We then summarized the three types of sensing frontends in circuit design, which is the most critical part in body-area capacitive sensing, and analyzed the data processing pipeline categorized into three kinds of approaches. The outcome will benefit researchers for further body-area electric field explorations. Finally, we described the challenges and outlooks of body-area electric sensing, followed by a conclusion, aiming to encourage researchers to further investigations considering the pervasive and promising usage scenarios backed by body-area capacitive sensing.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643555"}, {"primary_key": "379526", "vector": [], "sparse_vector": [], "title": "WEAR: An Outdoor Sports Dataset for Wearable and Egocentric Activity Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Research has shown the complementarity of camera- and inertial-based data for modeling human activities, yet datasets with both egocentric video and inertial-based sensor data remain scarce. In this paper, we introduce WEAR, an outdoor sports dataset for both vision- and inertial-based human activity recognition (HAR). Data from 22 participants performing a total of 18 different workout activities was collected with synchronized inertial (acceleration) and camera (egocentric video) data recorded at 11 different outside locations. WEAR provides a challenging prediction scenario in changing outdoor environments using a sensor placement, in line with recent trends in real-world applications. Benchmark results show that through our sensor placement, each modality interestingly offers complementary strengths and weaknesses in their prediction performance. Further, in light of the recent success of single-stage Temporal Action Localization (TAL) models, we demonstrate their versatility of not only being trained using visual data, but also using raw inertial data and being capable to fuse both modalities by means of simple concatenation. The dataset and code to reproduce experiments is publicly available via: mariusbock.github.io/wear/.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699776"}, {"primary_key": "379529", "vector": [], "sparse_vector": [], "title": "Characteristics of Conductive Paints and Tapes for Interactive Murals.", "authors": ["<PERSON>", "Alys<PERSON>"], "summary": "This paper analyzes a collection of conductive paints and tapes. We describe and compare their electrical conductivity, durability, appearance, and cost. We investigate different means of connecting these materials to each other and other electronic components-including connection via solder, conductive epoxy, conductive adhesives, and metal mechanical fasteners. We explore different means of insulating and protecting materials and provide the results of a range of durability tests. The results are discussed in the context of the development of interactive murals-large outdoor interactive surfaces that are intended to function for years. We identify two conductive paints, CuPro-Cote and Silver/Copper Super Shield, and two conductive tapes, Copper and Tin that are highly conductive, stable across most of our testing conditions and, we believe, suitable for interactive murals.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678587"}, {"primary_key": "379536", "vector": [], "sparse_vector": [], "title": "Animating the Crowd Mirage: A WiFi-Positioning-Based Crowd Mobility Digital Twin for Smart Campuses.", "authors": ["Chun<PERSON> Chen", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Bingqing Qu", "<PERSON><PERSON><PERSON>"], "summary": "Understanding crowd mobility is critical for many applications. In this paper, we propose CrowdMirage, a WiFi positioning-based crowd mobility digital twin for smart campuses. Specifically, we first design an end-to-end human mobility trace extraction pipeline from the comprehensive but noisy WiFi connection logs on a university campus. We then design two predictive and simulative models for the crowd flow prediction and simulation tasks, respectively. Considering the particularity of on-campus mobility, we propose a cross-grained crowd flow prediction model to forecast crowd flow at both building and floor levels. For crowd flow simulation, we design a conditional generative model based on conditional diffusion to simulate the crowd flow under given mobility-related contexts that are systematically identified. We evaluate CrowdMirage on two-year WiFi connection logs collected at our university. The results show that CrowdMirage achieves superior performance in both crowd flow prediction and simulation tasks. Our case studies show that CrowdMirage cannot only accurately forecast cross-grained crowd flow across different cases, but also simulate interpretable crowd flow under previously unseen conditions.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699792"}, {"primary_key": "379545", "vector": [], "sparse_vector": [], "title": "ContrastSense: Domain-invariant Contrastive Learning for In-the-Wild Wearable Sensing.", "authors": ["<PERSON><PERSON> Dai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Existing wearable sensing models often struggle with domain shifts and class label scarcity. Contrastive learning is a promising technique to address class label scarcity, which however captures domain-related features and suffers from low-quality negatives. To address both problems, we propose ContrastSense, a domain-invariant contrastive learning scheme for a realistic wearable sensing scenario where domain shifts and class label scarcity are presented simultaneously. To capture domain-invariant information, ContrastSense exploits unlabeled data and domain labels specifying user IDs or devices to minimize the discrepancy across domains. To improve the quality of negatives, time and domain labels are leveraged to select samples and refine negatives. In addition, ContrastSense designs a parameter-wise penalty to preserve domaininvariant knowledge during fine-tuning to further maintain model robustness. Extensive experiments show that ContrastSense outperforms the state-of-the-art baselines by 8.9% on human activity recognition with inertial measurement units and 5.6% on gesture recognition with electromyography when presented with domain shifts across users. Besides, when presented with different kinds of domain shifts across devices, on-body positions, and datasets, ContrastSense achieves consistent improvements compared with the best baselines.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699744"}, {"primary_key": "379549", "vector": [], "sparse_vector": [], "title": "PrivateGaze: Preserving User Privacy in Black-box Mobile Gaze Tracking Services.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Jia", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Eye gaze contains rich information about human attention and cognitive processes. This capability makes the underlying technology, known as gaze tracking, a critical enabler for many ubiquitous applications and has triggered the development of easy-to-use gaze estimation services. Indeed, by utilizing the ubiquitous cameras on tablets and smartphones, users can readily access many gaze estimation services. In using these services, users must provide their full-face images to the gaze estimator, which is often a black box. This poses significant privacy threats to the users, especially when a malicious service provider gathers a large collection of face images to classify sensitive user attributes. In this work, we present PrivateGaze, the first approach that can effectively preserve users' privacy in black-box gaze tracking services without compromising gaze estimation performance. Specifically, we proposed a novel framework to train a privacy preserver that converts full-face images into obfuscated counterparts, which are effective for gaze estimation while containing no privacy information. Evaluation on four datasets shows that the obfuscated image can protect users' private information, such as identity and gender, against unauthorized attribute classification. Meanwhile, when used directly by the black-box gaze estimator as inputs, the obfuscated images lead to comparable tracking performance to the conventional, unprotected full-face images.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678595"}, {"primary_key": "379550", "vector": [], "sparse_vector": [], "title": "From Classification to Clinical Insights: Towards Analyzing and Reasoning About Mobile and Behavioral Health Data With Large Language Models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Shwetak N. Patel", "<PERSON><PERSON><PERSON>"], "summary": "Passively collected behavioral health data from ubiquitous sensors could provide mental health professionals valuable insights into patient's daily lives, but such efforts are impeded by disparate metrics, lack of interoperability, and unclear correlations between the measured signals and an individual's mental health. To address these challenges, we pioneer the exploration of large language models (LLMs) to synthesize clinically relevant insights from multi-sensor data. We develop chain-of-thought prompting methods to generate LLM reasoning on how data pertaining to activity, sleep and social interaction relate to conditions such as depression and anxiety. We then prompt the LLM to perform binary classification, achieving accuracies of 61.1%, exceeding the state of the art. We find models like GPT-4 correctly reference numerical data 75% of the time. While we began our investigation by developing methods to use LLMs to output binary classifications for conditions like depression, we find instead that their greatest potential value to clinicians lies not in diagnostic classification, but rather in rigorous analysis of diverse self-tracking data to generate natural language summaries that synthesize multiple data streams and identify potential concerns. Clinicians envisioned using these insights in a variety of ways, principally for fostering collaborative investigation with patients to strengthen the therapeutic alliance and guide treatment. We describe this collaborative engagement, additional envisioned uses, and associated concerns that must be addressed before adoption in real-world contexts.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659604"}, {"primary_key": "379555", "vector": [], "sparse_vector": [], "title": "HabitSense: A Privacy-Aware, AI-Enhanced Multimodal Wearable Platform for mHealth Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Nabil <PERSON>"], "summary": "Wearable cameras provide an objective method to visually confirm and automate the detection of health-risk behaviors such as smoking and overeating, which is critical for developing and testing adaptive treatment interventions. Despite the potential of wearable camera systems, adoption is hindered by inadequate clinician input in the design, user privacy concerns, and user burden. To address these barriers, we introduced HabitSense, an open-source, multi-modal neck-worn platform developed with input from focus groups with clinicians (N=36) and user feedback from in-wild studies involving 105 participants over 35 days. Optimized for monitoring health-risk behaviors, the platform utilizes RGB, thermal, and inertial measurement unit sensors to detect eating and smoking events in real time. In a 7-day study involving 15 participants, HabitSense recorded 768 hours of footage, capturing 420.91 minutes of hand-to-mouth gestures associated with eating and smoking data crucial for training machine learning models, achieving a 92% F1-score in gesture recognition. To address privacy concerns, the platform records only during likely health-risk behavior events using SECURE, a smart activation algorithm. Additionally, HabitSense employs on-device obfuscation algorithms that selectively obfuscate the background during recording, maintaining individual privacy while leaving gestures related to health-risk behaviors unobfuscated. Our implementation of SECURE has resulted in a 48% reduction in storage needs and a 30% increase in battery life. This paper highlights the critical roles of clinician feedback, extensive field testing, and privacy-enhancing algorithms in developing an unobtrusive, lightweight, and reproducible wearable system that is both feasible and acceptable for monitoring health-risk behaviors in real-world settings.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678591"}, {"primary_key": "379579", "vector": [], "sparse_vector": [], "title": "MagDesk: Interactive Tabletop Workspace Based on Passive Magnetic Tracking.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Accurate and responsive 3D tracking enables interactive and context-aware workspaces, including mixed reality 3D interfaces and collaborative tangible interactions. However, limitations of current tracking mechanisms - line-of-sight occlusion, drifting errors, small working volumes, or instrumentation that requires maintenance - ultimately restrict their adoption. This paper introduces MagDesk, an interactive tabletop workspace capable of real-time 3D tracking of passive magnets embedded in objects. Using a sensing array of 112 low-cost magnetometers underneath a table, our custom-designed signal processing and localization engine enables simultaneous tracking of multiple magnets in 5 degrees of freedom with millimeter accuracy. MagDesk can continuously and robustly track magnets at a maximum height of 600 mm over a 1750 mm (L) × 950 mm (W) table, while achieving an average positional and orientational error of 2.49 mm and 0.72 ° near the table surface and 14.40 mm and 2.25° across the entire sensing range. To demonstrate MagDesk's object-tracking capabilities, this work presents a series of magnetic widgets for tangible interactions and explores two applications - a 3D drawing interface and augmented-reality tabletop games. By instrumenting a regular table surface, MagDesk presents a low-cost and accurate approach to 3D tracking passive objects for home and office environments.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699756"}, {"primary_key": "379580", "vector": [], "sparse_vector": [], "title": "Transportation Mode Detection Technology to Predict Wheelchair Users&apos; Life Satisfaction in Seoul, South Korea.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (<PERSON>) <PERSON>"], "summary": "Transportation mode detection (TMD) has been proposed as a computational technology to obtain mobility information. However, previous TMD studies mainly focused on improving detection performance and have not investigated the social implications of mobility information. This is the first study to use TMD to predict the life satisfaction of wheelchair users. Our goal is to develop TMD for wheelchair users (wTMD) utilizing smartphone location data and apply it to determine how transportation behaviors affect the life satisfaction of wheelchair users. First, we propose a wTMD technology by collecting a new dataset from wheelchair and non-wheelchair users. Second, we conduct regression analyses on existing in-the-wild dataset of wheelchair users. The result shows that the portion of subways in an individual's travel time is directly connected to wheelchair users' life satisfaction in Seoul, South Korea. We hope our findings are a good example for future social science studies and ultimately help to design wheelchair-friendly urban planning and accessibility.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643506"}, {"primary_key": "379584", "vector": [], "sparse_vector": [], "title": "AngleSizer: Enhancing Spatial Scale Perception for the Visually Impaired with an Interactive Smartphone Assistant.", "authors": ["Xiao<PERSON> Jing", "<PERSON>", "<PERSON><PERSON>", "Liang<PERSON>u Lu", "<PERSON>", "Weinan Shi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Spatial perception, particularly at small and medium scales, is an essential human sense but poses a significant challenge for the blind and visually impaired (BVI). Traditional learning methods for BVI individuals are often constrained by the limited availability of suitable learning environments and high associated costs. To tackle these barriers, we conducted comprehensive studies to delve into the real-world challenges faced by the BVI community. We have identified several key factors hindering their spatial perception, including the high social cost of seeking assistance, inefficient methods of information intake, cognitive and behavioral disconnects, and a lack of opportunities for hands-on exploration. As a result, we developed AngleSizer, an innovative teaching assistant that leverages smartphone technology. AngleSizer is designed to enable BVI individuals to use natural interaction gestures to try, feel, understand, and learn about sizes and angles effectively. This tool incorporates dual vibration-audio feedback, carefully crafted teaching processes, and specialized learning modules to enhance the learning experience. Extensive user experiments validated its efficacy and applicability with diverse abilities and visual conditions. Ultimately, our research not only expands the understanding of BVI behavioral patterns but also greatly improves their spatial perception capabilities, in a way that is both cost-effective and allows for independent learning.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678525"}, {"primary_key": "379588", "vector": [], "sparse_vector": [], "title": "MAPLE: Mobile App Prediction Leveraging Large Language Model Embeddings.", "authors": ["Yonchanok Khaokaew", "<PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, predicting mobile app usage has become increasingly important for areas like app recommendation, user behaviour analysis, and mobile resource management. Existing models, however, struggle with the heterogeneous nature of contextual data and the user cold start problem. This study introduces a novel prediction model, Mobile App Prediction Leveraging Large Language Model Embeddings (MAPLE), which employs Large Language Models (LLMs) and installed app similarity to overcome these challenges. MAPLE utilises the power of LLMs to process contextual data and discern intricate relationships within it effectively. Additionally, we explore the use of installed app similarity to address the cold start problem, facilitating the modelling of user preferences and habits, even for new users with limited historical data. In essence, our research presents MAPLE as a novel, potent, and practical approach to app usage prediction, making significant strides in resolving issues faced by existing models. MAPLE stands out as a comprehensive and effective solution, setting a new benchmark for more precise and personalised app usage predictions. In tests on two real-world datasets, MAPLE surpasses contemporary models in both standard and cold start scenarios. These outcomes validate MAPLE's capacity for precise app usage predictions and its resilience against the cold start problem. This enhanced performance stems from the model's proficiency in capturing complex temporal patterns and leveraging contextual information. As a result, MAPLE can potentially improve personalised mobile app usage predictions and user experiences markedly.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643514"}, {"primary_key": "379591", "vector": [], "sparse_vector": [], "title": "MediKnit: Soft Medical Making for Personalized and Clinician-Designed Wearable Devices for Hand Edema.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> (Cindy) <PERSON><PERSON>"], "summary": "Current rapid prototyping in medical domains relies on rigid 3D-printed materials, lacking flexibility, customization, and clinician-led input. This paper introduces MediKnit, a novel approach for the fabrication of soft medical devices, addressing critical limitations in existing design processes for medical devices. MediKnit provides a design tool empowering clinicians to personalize fabric-based devices for hand edema. This tool allows clinicians to adapt the design to individual patients' demands, thereby enhancing the overall effectiveness of therapy. The MediKnit device created by this tool consists of a machine-knit glove with active compression, which is programmable through a custom PCB. This device facilitates the mobilization of edema. To illustrate the practical implementation of our approach, this paper presents case studies involving six patients experiencing hand edema. The results demonstrate the adaptability and feasibility of our process for developing soft medical devices, highlighting its potential to broaden accessibility, facilitate personalized solutions, and empower clinicians as active medical makers.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678504"}, {"primary_key": "379593", "vector": [], "sparse_vector": [], "title": "Investigating Perspectives of and Experiences with Low Cost Commercial Fitness Wearables.", "authors": ["Whitney<PERSON><PERSON>", "<PERSON>"], "summary": "Consumer fitness wearables account for a growing body of personal mobile devices in the U.S. As a result, there has been increasing interest in Ubicomp in designing sensing devices which incorporate the needs of people of a broad range of backgrounds, especially of lower socioeconomic (SES) status. However, fitness trackers are expensive, and although low cost versions exist, there has not been much research on the viability of these devices for promoting their goals. To further the conversation of device use, we review a corpus of over 1,700 product reviews of 9 low cost consumer trackers, to determine perceptions and expectations of quality and general use. From this, we find that the low cost device is not currently necessarily increasing access, and that people have a wide range of expectations for what tracking will allow and represents at this price point, which colors how they describe their use experiences. We suggest that there is a need for a focused design effort for low cost devices which rectifies such discrepancies, and presents the low cost device as a counterpart to those of a higher consumer price, not an alternative.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699740"}, {"primary_key": "379594", "vector": [], "sparse_vector": [], "title": "The Personality Dimensions GPT-3 Expresses During Human-Chatbot Interactions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Large language models such as GPT-3 and ChatGPT can mimic human-to-human conversation with unprecedented fidelity, which enables many applications such as conversational agents for education and non-player characters in video games. In this work, we investigate the underlying personality structure that a GPT-3-based chatbot expresses during conversations with a human. We conducted a user study to collect 147 chatbot personality descriptors from 86 participants while they interacted with the GPT-3-based chatbot for three weeks. Then, 425 new participants rated the 147 personality descriptors in an online survey. We conducted an exploratory factor analysis on the collected descriptors and show that, though overlapping, human personality models do not fully transfer to the chatbot's personality as perceived by humans. We also show that the perceived personality is significantly different from that of virtual personal assistants, where users focus rather on serviceability and functionality. We discuss the implications of ever-evolving large language models and the change they affect in users' perception of agent personalities.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659626"}, {"primary_key": "379599", "vector": [], "sparse_vector": [], "title": "Identify, Adapt, Persist: The Journey of Blind Individuals with Personal Health Technologies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Personal health technologies (PHTs) often do not consider the accessibility needs of blind individuals, preventing access to their capabilities and data. However, despite the accessibility barriers, some blind individuals persistently use such systems and even express satisfaction with them. To obtain a deeper understanding of blind users' prolonged experiences in PHTs, we interviewed 11 individuals who continue to use such technologies, discussing and observing their past and current interactions with their systems. We report on usability issues blind users encounter and how they adapt to these situations, and theories for the persistent use of PHTs in the face of poor accessibility. We reflect on strategies to improve the accessibility and usability of PHTs for blind users, as well as ideas to aid the normalization of accessible features within these systems.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659585"}, {"primary_key": "379602", "vector": [], "sparse_vector": [], "title": "Co-Designing Sensory Feedback for Wearables to Support Physical Activity through Body Sensations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Francisco <PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Many technologies for promoting physical activity (PA) give limited importance to critical variables for engagement in PA, such as negative body perceptions. Here, we aim to address this gap by incorporating barriers and experienced body sensations into the design process for wearables and body-based devices thus expanding the design space for such technologies. We first report four co-design workshops with physically inactive participants (n=9); in these workshops, we explored tangible tools (i) to sensitize people to body sensations experienced when facing barriers to PA and (ii) to ideate how haptic and auditory feedback could transform body sensations to increase body-awareness and engagement in PA; results show several interactive sensorial patterns with potential to inform the design of body transformation wearables. These were validated through a follow-up workshop (n=13 participants) and reflections based on insights from a literature review. Our findings are significant for the design of ubiquitous technology to support and initiate PA in everyday contexts through a novel approach of transforming body perceptions/ sensations related to PA barriers. We contribute design inspirations and cards for identifying barriers to PA and to empower designers and researchers to integrate them early in the design process.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643499"}, {"primary_key": "379608", "vector": [], "sparse_vector": [], "title": "EchoPFL: Asynchronous Personalized Federated Learning on Mobile Devices with On-Demand Staleness Control.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rise of mobile devices with abundant sensory data and local computing capabilities has driven the trend of federated learning (FL) on these devices. And personalized FL (PFL) emerges to train specific deep models for each mobile device to address data heterogeneity and varying performance preferences. However, mobile training times vary significantly, resulting in either delay (when waiting for slower devices for aggregation) or accuracy decline (when aggregation proceeds without waiting). In response, we propose a shift towards asynchronous PFL, where the server aggregates updates as soon as they are available. Nevertheless, existing asynchronous protocols are unfit for PFL because they are devised for federated training of a single global model. They suffer from slow convergence and decreased accuracy when confronted with severe data heterogeneity prevalent in PFL. Furthermore, they often exclude slower devices for staleness control, which notably compromises accuracy when these devices possess critical personalized data. Therefore, we propose EchoPFL, a coordination mechanism for asynchronous PFL. Central to EchoPFL is to include updates from all mobile devices regardless of their latency. To cope with the inevitable staleness from slow devices, EchoPFL revisits model broadcasting. It intelligently converts the unscalable broadcast to on-demand broadcast, leveraging the asymmetrical bandwidth in wireless networks and the dynamic clustering-based PFL. Experiments show that compared to status quo approaches, EchoPFL achieves a reduction of up to 88.2% in convergence time, an improvement of up to 46% in accuracy, and a decrease of 37% in communication costs.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643560"}, {"primary_key": "379615", "vector": [], "sparse_vector": [], "title": "Waving Hand as Infrared Source for Ubiquitous Gas Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Gases in the environment can significantly affect our health and safety. As mobile devices gain popularity, we consider to explore a human-centered gas detection system that can be integrated into commercial mobile devices to realize ubiquitous gas detection. However, existing gas sensors either have too long response delays or are too cumbersome. This paper shows the feasibility of performing gas sensing by shining infrared (IR) signals emitted from our hands through the gas, allowing the system to rely on a single IR detector. The core opportunity arises from the fact that the human hand can provide stable, broadband, and omnidirectional IR radiation. Considering that IR signals experience distinct attenuation when passing through different gases or gases with different concentrations, we can integrate the human hand into the gas sensing system to enable extremely low-power and sustainable gas sensing. Yet, it is challenging to build up a robust system directly utilizing the hand's IR radiation. Practical issues include low IR radiation from the hand, unstable optical path, impact of environmental factors such as ambient temperature, etc. To tackle these issues, we on one hand modulate the IR radiation from the hand leveraging the controllability of the human hand, which improves the hand's IR radiation. On the other hand, we provide a dual-channel IR detector design to filter out the impact of environmental factors and gases in the environment. Extensive experiments show that our system can realize ethanol, gaseous water, and CO2 detection with 96.7%, 92.1% and 94.2%, respectively.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659605"}, {"primary_key": "379617", "vector": [], "sparse_vector": [], "title": "Pinning, Sorting, and Categorizing Notifications: A Mixed-methods Usage and Experience Study of Mobile Notification-management Features.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As smartphone notifications increase, so does the effort required to handle them effectively. Previous research has proposed various notification management features, but empirical evidence regarding their efficacy remains sparse. In response, we developed a notification management application incorporating features derived from prior studies, including both automatic and manual sorting, categorization, and manual pinning. Utilizing a mixed-methods approach, we explored how users interact with these features in their daily routines, with the aim to identify the underlying needs driving their usage. The results indicate that pinning was the most valued feature, serving diverse purposes such as deferring notifications, ensuring quick and constant access to information, preventing accidental deletions, and providing visual reminders. Conversely, manual categorization was underutilized, with participants relying on automated categories for notification access. Moreover, participants expressed a desire for automatic features to process and organize notifications based on topic and personalize them through user input. They also expected automatic sorting to adapt more dynamically to user contexts.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678579"}, {"primary_key": "379618", "vector": [], "sparse_vector": [], "title": "Uranus: Empowering Generalized Gesture Recognition with Mobility through Generating Large-scale mmWave Radar Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Kaikai Deng", "<PERSON><PERSON>", "<PERSON><PERSON>", "Huadong Ma"], "summary": "Millimeter-wave radar shows great sensing capabilities for pervasive and privacy-preserving gesture recognition. However, the lack of large-scale, dynamic radar datasets hinders the advancement of deep learning models for generalized gesture recognition in dynamic scenes. To address this problem, we opt for designing a system that employs wealthy dynamic 2D videos to generate realistic radar data, but it confronts two challenges including i) simulating the complex signal reflection characteristics of humans and the background and ii) extracting elusive gesture-relevant features from dynamic radar data. To this end, we design Uranus with two key components: (i) a dynamic data generation network (DDG-Net) combines several key modules, human reflection model, background reflection extractor, and data fitting model to simulate the signal reflection characteristics of humans and the background, followed by fitting the number and global distribution of points in point clouds to generate realistic radar data; (ii) a dynamic gesture recognition network (DGR-Net) combines two modules, spatial feature extraction and global feature fusion, to extract spatial and global features of points in point clouds, respectively, to achieve generalized gesture recognition. We implement and evaluate Uranus with dynamic video data from public video sources and self-collected radar data, demonstrating that Uranus outperforms the state-of-the-art approaches for gesture recognition in dynamic scenes.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699754"}, {"primary_key": "379619", "vector": [], "sparse_vector": [], "title": "Model Touch Pointing and Detect Parkinson&apos;s Disease via a Mobile Game.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Touch pointing is one of the primary interaction actions on mobile devices. In this research, we aim to (1) model touch pointing for people with Parkinson's Disease (PD), and (2) detect PD via touch pointing. We created a mobile game called MoleBuster in which a user performs a sequence of pointing actions. Our study with 40 participants shows that PD participants exhibited distinct pointing behavior. PD participants were much slower and had greater variances in movement time (MT), while their error rate was slightly lower than age-matched non-PD participants, indicating PD participants traded speed for accuracy. The nominal width Finger-Fitts law showed greater fitness than <PERSON>tts' law, suggesting this model should be adopted in lieu of <PERSON><PERSON>' law to guide mobile interface design for PD users. We also proposed a CNN-Transformer-based neural network model to detect PD. Taking touch pointing data and comfort rating of finger movement as input, this model achieved an AUC of 0.97 and sensitivity of 0.95 in leave-one-user-out cross-validation. Overall, our research contributes models that reveal the temporal and spatial characteristics of touch pointing for PD users, and provide a new method (CNN-Transformer model) and a mobile game (MoleBuster) for convenient PD detection.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659627"}, {"primary_key": "379626", "vector": [], "sparse_vector": [], "title": "EMooly: Supporting Autistic Children in Collaborative Social-Emotional Learning with Caregiver Participation through Interactive AI-infused and AR Activities.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Pengcheng An", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Children with autism spectrum disorder (ASD) have social-emotional deficits that lead to difficulties in recognizing emotions as well as understanding and responding to social interactions. This study presents EMooly, a tablet game that actively involves caregivers and leverages augmented reality (AR) and generative AI (GenAI) to enhance social-emotional learning for autistic children. Through a year of collaborative effort with five domain experts, we developed EMooly that engages children through personalized social stories, interactive and fun activities, and enhanced caregiver participation, focusing on emotion understanding and facial expression recognition. Compared with a baseline, a controlled study with 24 autistic children and their caregivers showed EMooly significantly improved children's emotion recognition skills and its novel features were preferred and appreciated. EMooly demonstrates the potential of AI and AR in enhancing social-emotional development for autistic children via prompt personalizing and engagement, and highlights the importance of caregiver involvement for optimal learning outcomes.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699738"}, {"primary_key": "379630", "vector": [], "sparse_vector": [], "title": "M3BAT: Unsupervised Domain Adaptation for Multimodal Mobile Sensing with Multi-Branch Adversarial Training.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "Over the years, multimodal mobile sensing has been used extensively for inferences regarding health and well-being, behavior, and context. However, a significant challenge hindering the widespread deployment of such models in real-world scenarios is the issue of distribution shift. This is the phenomenon where the distribution of data in the training set differs from the distribution of data in the real world---the deployment environment. While extensively explored in computer vision and natural language processing, and while prior research in mobile sensing briefly addresses this concern, current work primarily focuses on models dealing with a single modality of data, such as audio or accelerometer readings, and consequently, there is little research on unsupervised domain adaptation when dealing with multimodal sensor data. To address this gap, we did extensive experiments with domain adversarial neural networks (DANN) showing that they can effectively handle distribution shifts in multimodal sensor data. Moreover, we proposed a novel improvement over DANN, called M3BAT, unsupervised domain adaptation for multimodal mobile sensing with multi-branch adversarial training, to account for the multimodality of sensor data during domain adaptation with multiple branches. Through extensive experiments conducted on two multimodal mobile sensing datasets, three inference tasks, and 14 source-target domain pairs, including both regression and classification, we demonstrate that our approach performs effectively on unseen domains. Compared to directly deploying a model trained in the source domain to the target domain, the model shows performance increases up to 12% AUC (area under the receiver operating characteristics curves) on classification tasks, and up to 0.13 MAE (mean absolute error) on regression tasks.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659591"}, {"primary_key": "379632", "vector": [], "sparse_vector": [], "title": "Wind of Change: Investigating Information Visualizations for Passengers and Residents&apos; Perception of Automated Urban Air Mobility.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated Urban Air Mobility (UAM) is expected to improve passenger transportation but raises concerns about passenger trust and its impact on residents. We address these issues through three online studies using 360-degree videos. The study on adverse lighting and weather conditions (N=31) shows that bounding box visualizations of other air taxis positively impact passengers' perceived safety. Especially during fog, passengers' perceived safety is reduced when no additional information besides the path is shown. The second study (N=29) extends these findings to emergency scenarios (wind gusts, bird flocks, and rotor breakdown) and shows that visualizations of the alternate path increase perceived safety and trust. Finally, studying the impact of UAM on residents (N=29), we found significant concerns about visual pollution and privacy, especially in the suburbs but also in offices. Our findings provide actionable insights into UAM perception and societal acceptability for future aerial transportation design and infrastructure.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699753"}, {"primary_key": "379635", "vector": [], "sparse_vector": [], "title": "Smartphone Haptics Can Uncover Differences in Touch Interactions Between ASD and Neurotypicals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Utilizing touch interactions from smartphones for gathering data and identifying digital markers for screening and monitoring neurological disorders, such as Autism Spectrum Disorder (ASD), is an emerging area of research. Smartphones provide multiple benefits for this kind of study, including unobtrusive data collection via built-in sensors, integrated haptic feedback systems, and the capability to create specialized applications. Acknowledging the significant yet understudied presence of tactile processing differences in individuals with ASD, we designed and developed Feel and Touch, a mobile game that leverages the haptic capabilities of smartphones. This game provides vibrotactile feedback in response to touch interactions and collects data on these interactions. We conducted a deployment study with 83 Mexican children who played Feel and Touch to capture their interactions with the game. Our analysis, comparing touch interactions between children with ASD and neurotypical (NT) peers, uncovered three digital markers based on phone tilt and touch patterns that distinguish the two groups. Additionally, we demonstrated the ability of a machine learning model to accurately classify these interactions between ASD and NT children. Our findings discuss the implications in terms of accessibility and ubiquity, as well as the possibilities for the development of digital markers and their application in pervasive computing for healthcare.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699749"}, {"primary_key": "379636", "vector": [], "sparse_vector": [], "title": "Investigating Technology Adoption Soon After Sustaining a Spinal Cord Injury.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "A spinal cord injury (SCI) typically results in a sudden change to an individual's motor function. People's adoption of technology soon after a severe SCI is crucial, since they must relearn most technology interactions to adjust to their new physical abilities and regain independence. This study examines how individuals adopt technologies soon after sustaining a severe SCI. By qualitatively analyzing the perspectives of ten rehabilitation clinicians, three individuals who recently sustained an SCI, and two of those participants' family members, we surfaced a spectrum of individuals' motivations to adopt technology post-injury and highlight the challenges they face to adopt technology. Our findings highlight the need to incorporate the holistic experience---including technology literacy, perception of support, and acceptance of the \"new-normal\"---in technology design for individuals who have a sudden change to motor functions. Our findings show that technology adoption is a critical component for the overall adjustment of post-SCI life. Finally, we use the extended version of the Technology Acceptance Model (TAM) to make recommendations for more inclusive assistive design.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643507"}, {"primary_key": "379637", "vector": [], "sparse_vector": [], "title": "Capturing the College Experience: A Four-Year Mobile Sensing Study of Mental Health, Resilience and Behavior of College Students during the Pandemic.", "authors": ["Subigya Nepal", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Understanding the dynamics of mental health among undergraduate students across the college years is of critical importance, particularly during a global pandemic. In our study, we track two cohorts of first-year students at Dartmouth College for four years, both on and off campus, creating the longest longitudinal mobile sensing study to date. Using passive sensor data, surveys, and interviews, we capture changing behaviors before, during, and after the COVID-19 pandemic subsides. Our findings reveal the pandemic's impact on students' mental health, gender based behavioral differences, impact of changing living conditions and evidence of persistent behavioral patterns as the pandemic subsides. We observe that while some behaviors return to normal, others remain elevated. Tracking over 200 undergraduate students from high school to graduation, our study provides invaluable insights into changing behaviors, resilience and mental health in college life. Conducting a long-term study with frequent phone OS updates poses significant challenges for mobile sensing apps, data completeness and compliance. Our results offer new insights for Human-Computer Interaction researchers, educators and administrators regarding college life pressures. We also detail the public release of the de-identified College Experience Study dataset used in this paper and discuss a number of open research questions that could be studied using the public dataset.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643501"}, {"primary_key": "379661", "vector": [], "sparse_vector": [], "title": "EyeGesener: Eye Gesture Listener for Smart Glasses Interaction Using Acoustic Sensing.", "authors": ["Tao Sun", "Yankai Zhao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yong<PERSON>", "<PERSON>"], "summary": "The smart glasses market has witnessed significant growth in recent years. The interaction of commercial smart glasses mostly relies on the hand, which is unsuitable for scenarios where both hands are occupied. In this paper, we propose <PERSON><PERSON><PERSON><PERSON>, an eye gesture listener for smart glasses interaction using acoustic sensing. To mitigate the Midas touch problem, we meticulously design eye gestures for interaction as two intentional consecutive saccades in a specific direction without visual dwell. The proposed system is a glass-mounted acoustic sensing system with two pairs of commercial speakers and microphones to sense eye gestures. To capture the subtle movements of the eyelid and surrounding skin induced by eye gestures, we design an Orthogonal Frequency Division Multiplexing (OFDM)-based channel impulse response (CIR) estimation schema that allows two speakers to transmit at the same time and in the same frequency band without collision. We implement eye gesture filtering and adversarial-based eye gesture recognition to identify eye gestures for interaction, filtering out daily eye movements. To address the differences in eye size and facial structure among different users, we employ adversarial training to achieve user-independent eye gesture recognition. We evaluate the performance of our system through experiments on data collected from 16 subjects. The experimental result shows that our system can recognize eight eye gestures with an average F1-score of 0.93, and the false alarm rate of our system is 0.03. We develop an interactive real-time audio-video player based on <PERSON><PERSON><PERSON><PERSON> and then conduct a user study. The result demonstrates the high usability of the proposed system.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678541"}, {"primary_key": "379667", "vector": [], "sparse_vector": [], "title": "EStatiG: Wearable Haptic Feedback with Multi-Phalanx Electrostatic Brake for Enhanced Object Perception in VR.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Haptic gloves have enabled the immersive and realistic sense of interacting with objects in virtual reality (VR) by providing coordinated haptic sensation with visual information. However, previous approaches mainly focused on providing feedback on the fingertips or distal phalanges, with minimal attention paid to the other phalanges. We propose EStatiG, a haptic glove that delivers force feedback over all finger regions from the fingertip to the proximal phalanges. Here, we enrich the perception of object shape during grasping tasks in VR. To do this, we developed the double layer and multi-stacked electrostatic clutches (ES clutches) to form an electrostatic brake (ES brake) for each phalanx. With the lightweight structure (130 grams), we enabled high-resolution force feedback while maintaining wearability and usability. We validated the user perception performance when using the proposed device. Our results showed a significant improvement in the perception of phalanx angle positions and the overall experience of realism and immersion when interacting with various object shapes in VR.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678567"}, {"primary_key": "379679", "vector": [], "sparse_vector": [], "title": "EarSlide: a Secure Ear Wearables Biometric Authentication Based on Acoustic Fingerprint.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Ear wearables (earables) are emerging platforms that are broadly adopted in various applications. There is an increasing demand for robust earables authentication because of the growing amount of sensitive information and the IoT devices that the earable could access. Traditional authentication methods become less feasible due to the limited input interface of earables. Nevertheless, the rich head-related sensing capabilities of earables can be exploited to capture human biometrics. In this paper, we propose EarSlide, an earable biometric authentication system utilizing the advanced sensing capacities of earables and the distinctive features of acoustic fingerprints when users slide their fingers on the face. It utilizes the inward-facing microphone of the earables and the face-ear channel of the ear canal to reliably capture the acoustic fingerprint. In particular, we study the theory of friction sound and categorize the characteristics of the acoustic fingerprints into three representative classes, pattern-class, ridge-groove-class, and coupling-class. Different from traditional fingerprint authentication only utilizes 2D patterns, we incorporate the 3D information in acoustic fingerprint and indirectly sense the fingerprint for authentication. We then design representative sliding gestures that carry rich information about the acoustic fingerprint while being easy to perform. It then extracts multi-class acoustic fingerprint features to reflect the inherent acoustic fingerprint characteristic for authentication. We also adopt an adaptable authentication model and a user behavior mitigation strategy to effectively authenticate legit users from adversaries. The key advantages of EarSlide are that it is resistant to spoofing attacks and its wide acceptability. Our evaluation of EarSlide in diverse real-world environments with intervals over one year shows that EarSlide achieves an average balanced accuracy rate of 98.37% with only one sliding gesture.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643515"}, {"primary_key": "379693", "vector": [], "sparse_vector": [], "title": "Predicting Multi-dimensional Surgical Outcomes with Multi-modal Mobile Sensing: A Case Study with Patients Undergoing Lumbar Spine Surgery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>raeden C<PERSON> Benedict", "<PERSON>", "<PERSON>", "<PERSON>", "Chenyang Lu"], "summary": "Pre-operative prediction of post-surgical recovery for patients is vital for clinical decision-making and personalized treatments, especially with lumbar spine surgery, where patients exhibit highly heterogeneous outcomes. Existing predictive tools mainly rely on traditional Patient-Reported Outcome Measures (PROMs), which fail to capture the long-term dynamics of patient conditions before the surgery. Moreover, existing studies focus on predicting a single surgical outcome. However, recovery from spine surgery is multi-dimensional, including multiple distinctive but interrelated outcomes, such as pain interference, physical function, and quality of recovery. In recent years, the emergence of smartphones and wearable devices has presented new opportunities to capture longitudinal and dynamic information regarding patients' conditions outside the hospital. This paper proposes a novel machine learning approach, Multi-Modal Multi-Task Learning (M3TL), using smartphones and wristbands to predict multiple surgical outcomes after lumbar spine surgeries. We formulate the prediction of pain interference, physical function, and quality of recovery as a multi-task learning (MTL) problem. We leverage multi-modal data to capture the static and dynamic characteristics of patients, including (1) traditional features from PROMs and Electronic Health Records (EHR), (2) Ecological Momentary Assessment (EMA) collected from smartphones, and (3) sensing data from wristbands. Moreover, we introduce new features derived from the correlation of EMA and wearable features measured within the same time frame, effectively enhancing predictive performance by capturing the interdependencies between the two data modalities. Our model interpretation uncovers the complementary nature of the different data modalities and their distinctive contributions toward multiple surgical outcomes. Furthermore, through individualized decision analysis, our model identifies personal high risk factors to aid clinical decision making and approach personalized treatments. In a clinical study involving 122 patients undergoing lumbar spine surgery, our M3TL model outperforms a diverse set of baseline methods in predictive performance, demonstrating the value of integrating multi-modal data and learning from multiple surgical outcomes. This work contributes to advancing personalized peri-operative care with accurate pre-operative predictions of multi-dimensional outcomes.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659628"}, {"primary_key": "379697", "vector": [], "sparse_vector": [], "title": "KneeGuard: A Calibration-free Wearable Monitoring System for Knee Osteoarthritis Gait Re-training via Effortless Wearing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Lu", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Gait re-training is an effective approach to slow disease progression and alleviate pain in knee osteoarthritis (KOA) patients. Personalized gait re-training strategies, based on knee loading and muscle forces, have shown promise in improving rehabilitation outcomes. Laboratory systems to monitor these metrics are unsuitable for daily use owing to the complicated setup and high-cost. Recently proposed wearable solutions try to fill this gap, but their in-lab calibration requirement still hinders practical usage. This paper introduces KneeGuard, a calibration-free gait re-training monitoring system that can estimate knee loading and muscle forces via effortless wearing. We identify the main issue of current calibration-needed systems is insufficient biomechanical information retrieval and modeling. To address this, we propose a user-friendly wearable prototype incorporating inertial measurement unit (IMU) and surface electromyography (sEMG) to obtain comprehensive biomechanical information including body geometrical changes and muscle contractions. For modeling, we design a biomechanic-inspired fusion framework based on multi-task learning and cross-modality attention to capture inter-modality biomechanical correlations. Additionally, since precise sensor placement required by current sEMG-based solutions is difficult to locate, we develop a circular sEMG array and propose a spatial-aware feature extraction module, achieving effective biomechanical feature extraction under effortless wearing. We collaborate with a medical center and collect a dataset from 21 KOA patients and 17 healthy subjects at different speeds. Notably, our dataset includes six gait types for KOA gait re-training, making it the first gait dataset with comprehensive re-training strategies. Evaluation demonstrates that KneeGuard achieves an average normalized root-mean-square error (NRMSE) of 9.95% in knee loading estimation and an average NRMSE of 8.75% in the estimation of muscle forces, comparable to the with-calibration results in existing works. We have open-sourced the code and a sample dataset in https://github.com/KneeGuard/KneeGuard.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699768"}, {"primary_key": "379699", "vector": [], "sparse_vector": [], "title": "IrOnTex: Using Ironable 3D Printed Objects to Fabricate and Prototype Customizable Interactive Textiles.", "authors": ["<PERSON><PERSON><PERSON>", "Supun Kuruppu", "<PERSON><PERSON><PERSON>", "Praneeth Bimsara Perera", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose IrOnTex, an approach to design, fabricate, and prototype customizable interactive textiles by ironing 3D printed elements directly onto fabrics. Our method incorporates multiple filament types to demonstrate functionalities such as electrical conductivity, sensing, and thermo- and photo-sensitivity for interactive applications. Additionally, we integrate a tiling-like fragmented design to improve stretchability and flexibility to match the mechanical behavior of textiles in our 3D printed elements. We characterized the iron-on process through technical evaluations, which showed the durability and reversibility of the approach. We also suggested baseline parameters for prototyping and methods to enhance the functionality of certain materials. The empirical results from the studies were used to develop a design tool to assist the design process. A user study with 10 participants revealed the helpfulness of the tool. To demonstrate the capabilities of the approach, we show a set of example interactive applications. We envision the proposed approach will enable designers to rapidly prototype interactive textile applications.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678543"}, {"primary_key": "379704", "vector": [], "sparse_vector": [], "title": "A Reproducible Stress Prediction Pipeline with Mobile Sensor Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent efforts to predict stress in the wild using mobile technology have increased; however, the field lacks a common pipeline for assessing the impact of factors such as label encoding and feature selection on prediction performance. This gap hinders replication, especially because of a lack of common guidelines for reporting results or privacy concerns that limit access to open codes and datasets. Our study introduces a common pipeline based on a comprehensive literature review and offers comprehensive evaluations of key pipeline factors, promoting independent reproducibility. Our systematic evaluation aimed to validate the findings of previous studies. We identified overfitting and distribution shifts across users as the major reasons for performance limitations. We used K-EmoPhone, a public dataset, for experimentation and a new public dataset---DeepStress---to validate the findings. Furthermore, our results suggest that researchers should carefully consider temporal order in cross-validation settings. Additionally, self-report labels for target users are key to enhancing performance in user-independent scenarios.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678578"}, {"primary_key": "379721", "vector": [], "sparse_vector": [], "title": "Modeling Attentive Interaction Behavior for Web Content Identification in Exploratory Information Seeking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Extracting and collecting information during web exploration is an arduous process, demanding substantial cognitive and physical effort from users. Users must not only determine which content is worth capturing but also manually extract and save it, often disrupting the flow of their learning and exploration. To mitigate it, we propose AIbM (Attentive Interaction Behavior Modeling), which encodes and integrates multiple implicit interaction data to identify fine-grained helpful web content, providing an algorithmic foundation to facilitate automatic information extraction and collection. AIbM captures dynamic patterns of user interactions---specifically eye and mouse movements---by encoding these into images and time series. It further employs graph modeling to exploit relationships between a content block and its adjacent blocks. By incorporating graph attention networks, AIbM efficiently processes both intra-block and inter-block features, improving the identification of helpful web content. Experimental results demonstrate that AIbM achieves the highest F1 score of 82.80% in general helpful content identification, marking improvements of 10.86% over state-of-the-art models like GBT and at least 2.49% over individual modality inputs. These results underscore the potential of our approach to advance tools and systems designed for automated information extraction and collection to facilitate more effective exploratory search experiences.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699750"}, {"primary_key": "379515", "vector": [], "sparse_vector": [], "title": "RaDro: Indoor Drone Tracking Using Millimeter Wave Radar.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Core to drone design is its ability to ascertain its location by utilizing onboard inertial sensors combined with GPS data. However, GPS is not always reachable, especially in challenging environments such as indoors. This paper proposes RaDro; a system that leverages millimeter-waves (mmWave) to precisely localize and track drones in indoor environments. Unlike commonly used alternative technologies, RaDro is cost-effective and can penetrate obstacles, a bonus in non-line-of-sight (NLoS) scenarios, which enhances its reliability for tracking objects in complex environments. It does this without the need for tags or anchors to be attached to the drone, achieving 3D tracking with just a single radar point, significantly streamlining the deployment process. Comprehensive experiments are conducted in different scenarios to evaluate RaDro's performance. These include employing different drone models with different sizes to execute a range of aerial manoeuvres across different flight arenas, each with its own settings and clutter, and encountering various LoS and NLoS scenarios in dynamic environments. The experiments aimed to assess the capabilities of the system to extract coarse-grained and fine-grained information for drone detection, motion recognition, and localization. The results showcase precise localization, achieving a 50% reduction in localization error compared to the conventional baseline. This localization accuracy remains resilient even when confronted with interference from other moving sources. The results also demonstrate the system's ability to accurately localize drones in NLoS scenarios where existing state-of-the-art optical technologies cannot work.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678549"}, {"primary_key": "379516", "vector": [], "sparse_vector": [], "title": "Beyond Detection: Towards Actionable Sensing Research in Clinical Mental Healthcare.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Researchers in ubiquitous computing have long promised that passive sensing will revolutionize mental health measurement by detecting individuals in a population experiencing a mental health disorder or specific symptoms. Recent work suggests that detection tools do not generalize well when trained and tested in more heterogeneous samples. In this work, we contribute a narrative review and findings from two studies with 41 mental health clinicians to understand these generalization challenges. Our findings motivate research on actionable sensing, as an alternative to detection research, studying how passive sensing can augment traditional mental health measures to support actions in clinical care. Specifically, we identify how passive sensing can support clinical actions by revealing patients' presenting problems for treatment and identifying targets for behavior change and symptom reduction, but passive data requires additional contextual information to be appropriately interpreted and used in care. We conclude by suggesting research at the intersection of actionable sensing and mental healthcare, to align technical research in ubiquitous computing with clinical actions and needs.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699755"}, {"primary_key": "379519", "vector": [], "sparse_vector": [], "title": "End-Users Know Best: Identifying Undesired Behavior of Alexa Skills Through User Review Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Haipeng <PERSON>ai", "<PERSON><PERSON><PERSON>", "Hong<PERSON> Hu"], "summary": "The Amazon Alexa marketplace has grown rapidly in recent years due to third-party developers creating large amounts of content and publishing directly to a skills store. Despite the growth of the Amazon Alexa skills store, there have been several reported security and usability concerns, which may not be identified during the vetting phase. However, user reviews can offer valuable insights into the security &amp; privacy, quality, and usability of the skills. To better understand the effects of these problematic skills on end-users, we introduce ReviewTracker, a tool capable of discerning and classifying semantically negative user reviews to identify likely malicious, policy violating, or malfunctioning behavior on Alexa skills. ReviewTracker employs a pre-trained FastText classifier to identify different undesired skill behaviors. We collected over 700,000 user reviews spanning 6 years with more than 200,000 negative sentiment reviews. ReviewTracker was able to identify 17,820 reviews reporting violations related to Alexa policy requirements across 2,813 skills, and 131,855 reviews highlighting different types of user frustrations associated with 9,294 skills. In addition, we developed a dynamic skill testing framework using ChatGPT to conduct two distinct types of tests on Alexa skills: one using a software-based simulation for interaction to explore the actual behaviors of skills and another through actual voice commands to understand the potential factors causing discrepancies between intended skill functionalities and user experiences. Based on the number of the undesired skill behavior reviews, we tested the top identified problematic skills and detected more than 228 skills violating at least one policy requirement. Our results demonstrate that user reviews could serve as a valuable means to identify undesired skill behaviors.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678517"}, {"primary_key": "379520", "vector": [], "sparse_vector": [], "title": "Empowering IoT Developers with Privacy-Preserving End-User Development Tools.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>mer F. Rana", "<PERSON><PERSON>"], "summary": "Internet of Things applications (IoT) have the potential to derive sensitive user data, necessitating adherence to privacy and data protection laws. However, developers often struggle with privacy issues, resulting in personal data misuse. Despite the proposed Privacy by Design (PbD) approach, criticism arises due to its ambiguity and lack of practical tools for educating software engineers. We introduce Canella, an integrated IoT development ecosystem with privacy-preserving components leveraging End-User Development (EUD) tools Blockly@rduino and Node-RED, to help developers build end-to-end IoT applications that prioritize privacy and comply with regulations. It helps developers integrate privacy during the development process and rapid prototyping phases, offering real-time feedback on privacy concerns. We start by conducting a focus group study to explore the applicability of designing and implementing PbD schemes within different development environments. Based on this, we implemented a proof-of-concept prototype of Canella and evaluated it in controlled lab studies with 18 software developers. The findings reveal that developers using Canella created more privacy-preserving applications, gained a deeper understanding of personal data management, and achieved better privacy compliance. Our results also highlight Canella's role in educating and promoting privacy awareness, enhancing productivity, streamlining privacy implementation, and significantly reducing cognitive load. Overall, developers found Canella and its privacy-preserving components useful, easy to use, and easy to learn, which could potentially improve IoT application privacy. Watch the demo video.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678588"}, {"primary_key": "379521", "vector": [], "sparse_vector": [], "title": "Light and Dark Mode: A Comparison Between Android and iOS App UI Modes and Interviews with App Designers and Developers.", "authors": ["<PERSON>", "Chelsea Bishop", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Mobile app light and dark modes offer improved usability within different contexts (e.g., dark mode for easier night reading). Yet, little research has investigated the prevalence of light and dark modes across platforms, the intricacies of UI color changes, and challenges in the design and development process. Our investigation focused on comparing light and dark mode designs. We carried out a manual inspection of 120 popular Android and iOS apps to find that only 55% (Android) and 48% (iOS) of the apps included any modes. We also found significant variability in how many UI elements changed between modes. We interviewed 15 designers and developers to understand the creative process, motivations for design decisions, and what challenges exist in the processes that affect alternative mode implementation. We identified several issues that HCI researchers are equipped to solve, ultimately improving support for mobile app creators looking to implement dark modes (and beyond) for increased mobile usability in different contexts.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643539"}, {"primary_key": "379522", "vector": [], "sparse_vector": [], "title": "PrISM-Q&amp;A: Step-Aware Voice Assistant on a Smartwatch Enabled by Multimodal Procedure Tracking and Large Language Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Voice assistants capable of answering user queries during various physical tasks have shown promise in guiding users through complex procedures. However, users often find it challenging to articulate their queries precisely, especially when unfamiliar with the specific terminologies required for machine-oriented tasks. We introduce PrISM-Q&amp;A, a novel question-answering (Q&amp;A) interaction termed step-aware Q&amp;A, which enhances the functionality of voice assistants on smartwatches by incorporating Human Activity Recognition (HAR) and providing the system with user context. It continuously monitors user behavior during procedural tasks via audio and motion sensors on the watch and estimates which step the user is performing. When a question is posed, this contextual information is supplied to Large Language Models (LLMs) as part of the context used to generate a response, even in the case of inherently vague questions like \"What should I do next with this?\" Our studies confirmed that users preferred the convenience of our approach compared to existing voice assistants. Our real-time assistant represents the first Q&amp;A system that provides contextually situated support during tasks without camera use, paving the way for the ubiquitous, intelligent assistant.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699759"}, {"primary_key": "379523", "vector": [], "sparse_vector": [], "title": "Towards Reducing Continuous Emotion Annotation Effort During Video Consumption: A Physiological Response Profiling Approach.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emotion-aware video applications (e.g., gaming, online meetings, online tutoring) strive to moderate the content presentations for a more engaging and improved user experience. These services typically deploy a machine-learning model that continuously infers the user's emotion (based on different physiological signals, facial expressions, etc.) to adapt the content delivery. Therefore, to train such models, the emotion ground truth labels also need to be collected continuously. Typically, those are collated as emotion self-reports from users in a continuous manner (using an auxiliary device such as a joystick) when they watch some videos. This process of continuous emotion annotation not only increases the cognitive load and survey fatigue but also significantly deteriorates the viewing experience. To address this problem, we propose a framework, PResUP that probes a user for emotion self-reports opportunistically based on the physiological response variations of the user. Specifically, the framework implements a sequence of phases - (a) user profile construction based on physiological responses, (b) similar user clustering based on the user profile, and (c) training a parameterized activation-guided LSTM (Long short-term memory) model by sharing data among similar users to detect the opportune self-report collection moments. All these steps together help to reduce the continuous emotion annotation overhead by probing at the opportune moments without compromising the annotation quality. We evaluated PResUP by conducting a user study (N=36) during which the participants watched eight videos, and their physiological responses and continuous emotion self-reports were recorded. The key results from this evaluation reveal that PResUP reduces the annotation overhead by reducing the probing rate by an average of 34.80% and detects the opportune probing moments with an average TPR of 80.07% without compromising the annotation quality. Motivated by these findings, we deployed PResUP by performing a follow-up user study (N=18). In this deployment scenario, we also obtained similar performance in terms of the probing rate reduction (average reduction of 38.05%), and opportune moment detection performance (average TPR of 82.26%). These findings underscore the utility of PResUP in reducing the continuous emotion annotation effort during video consumption.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678569"}, {"primary_key": "379527", "vector": [], "sparse_vector": [], "title": "Temporal Action Localization for Inertial-based Human Activity Recognition.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As of today, state-of-the-art activity recognition from wearable sensors relies on algorithms being trained to classify fixed windows of data. In contrast, video-based Human Activity Recognition, known as Temporal Action Localization (TAL), has followed a segment-based prediction approach, localizing activity segments in a timeline of arbitrary length. This paper is the first to systematically demonstrate the applicability of state-of-the-art TAL models for both offline and near-online Human Activity Recognition (HAR) using raw inertial data as well as pre-extracted latent features as input. Offline prediction results show that TAL models are able to outperform popular inertial models on a multitude of HAR benchmark datasets, with improvements reaching as much as 26% in F1-score. We show that by analyzing timelines as a whole, TAL models can produce more coherent segments and achieve higher NULL-class accuracy across all datasets. We demonstrate that TAL is less suited for the immediate classification of small-sized windows of data, yet offers an interesting perspective on inertial-based HAR - alleviating the need for fixed-size windows and enabling algorithms to recognize activities of arbitrary length. With design choices and training concepts yet to be explored, we argue that TAL architectures could be of significant value to the inertial-based HAR community. The code and data download to reproduce experiments is publicly available via github.com/mariusbock/tal_for_har.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699770"}, {"primary_key": "379528", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON>: Lightweight Speech Filtering for Privacy-Preserving Activity Recognition using Audio.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Audio-based human activity recognition (HAR) is very popular because many human activities have unique sound signatures that can be detected using machine learning (ML) approaches. These audio-based ML HAR pipelines often use common featurization techniques, such as extracting various statistical and spectral features by converting time domain signals to the frequency domain (using an FFT) and using them to train ML models. Some of these approaches also claim privacy benefits by preventing the identification of human speech. However, recent deep learning-based automatic speech recognition (ASR) models pose new privacy challenges to these featurization techniques. In this paper, we systematically evaluate various featurization approaches for audio data, assessing their privacy risks through metrics like speech intelligibility (PER and WER) while considering the utility tradeoff in terms of ML-based activity recognition accuracy. Our findings reveal the susceptibility of these approaches to speech content recovery when exposed to recent ASR models, especially under re-tuning or retraining conditions. Notably, fine-tuned ASR models achieved an average Phoneme Error Rate (PER) of 39.99% and Word Error Rate (WER) of 44.43% in speech recognition for these approaches. To overcome these privacy concerns, we propose Kirigami, a lightweight machine learning-based audio speech filter that removes human speech segments reducing the efficacy of ASR models (70.48% PER and 101.40% WER) while also maintaining HAR accuracy (76.0% accuracy). We show that Kirigami can be implemented on common edge microcontrollers with limited computational capabilities and memory, providing a path to deployment on a variety of IoT devices. Finally, we conducted a real-world user study and showed the robustness of <PERSON><PERSON><PERSON> on a laptop and an ARM Cortex-M4F microcontroller under three different background noises.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643502"}, {"primary_key": "379530", "vector": [], "sparse_vector": [], "title": "Understanding Instant Social Control of Shared Devices in Public Spaces: A Field Trial.", "authors": ["<PERSON><PERSON><PERSON>", "Sookyung Han", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Public spaces are created for the convenience of visitors, but frequently, their operation does not match individual visitors' needs. We propose instant social control as a new approach for controlling devices deployed in public spaces, envisioning the democratic and inclusive operation of the spaces. It is an approach to embrace individuals in the dynamic and unpredictable situations of public spaces, providing them with a powerful tool of immediate engagement and reflection. This research explores the use of instant social control, focusing on cases where discrepancies and conflicts arise among visitors' preferences for operating public devices. We conducted a field trial by deploying a technology probe to two real-world public spaces: a university auditorium, and a cafeteria. We collected usage logs and surveyed the users' experience with this new way of device control. We further investigated their experience through in-depth interviews with participants. Our field trial revealed a rich set of findings including voting strategies, exploratory device control patterns, users' attempts to communicate with other visitors, considerations of other visitors' discomfort, and acceptance and usefulness of instant social control of public devices.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678592"}, {"primary_key": "379531", "vector": [], "sparse_vector": [], "title": "MmECare: Enabling Fine-grained Vital Sign Monitoring for Emergency Care with Handheld MmWave Radars.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Badii Jouaber", "<PERSON><PERSON> Zhang"], "summary": "Fine-grained vital sign monitoring in emergency care is crucial for accurately assessing patient conditions, predicting disease progression, and formulating effective rescue plans. In non-hospital settings, limited equipment often necessitates manual observation of respiration and heartbeat, which can lead to significant errors. Contactless monitoring using wireless signals offers a promising alternative. Unlike traditional systems that require stationary devices for contactless sensing, handheld devices are more practical for rescuers during emergency care. However, sensing performance can be severely compromised by involuntary hand movements. Previous research has achieved respiration monitoring with handheld devices, but the randomness of hand motion still prevents reliable heartbeat monitoring. In this paper, we first demonstrate that the key to mitigating the effects of device motion lies in accurately estimating the motion direction. We then introduce a novel method that uses two static objects, i.e., corner reflectors, to precisely estimate the random motion direction of the device. These reflectors can be quickly and easily deployed by the rescuer before initiating vital sign monitoring, enabling a more thorough elimination of device motion effects. Comprehensive experiments validate the effectiveness of our solution using mmWave radar. Real-world tests demonstrate that our system can accurately monitor both respiration and heartbeat with handheld devices, significantly enhancing emergency medical response by improving the accuracy and feasibility of vital sign monitoring in urgent situations.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699766"}, {"primary_key": "379533", "vector": [], "sparse_vector": [], "title": "SemiCMT: Contrastive Cross-Modal Knowledge Transfer for IoT Sensing with Semi-Paired Multi-Modal Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Hu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a novel contrastive cross-modal knowledge transfer framework, SemiCMT, for multi-modal IoT sensing applications. It effectively transfers the feature extraction capability (also called knowledge) learned from a source modality (e.g., acoustic signals) with abundant unlabeled training data, to a target modality (e.g., seismic signals) that lacks enough training data, in a self-supervised manner with the help of only a small set of synchronized multi-modal pairs. The transferred model can be quickly finetuned to downstream target-modal tasks with only limited labels. The key design constitutes of three aspects: First, we factorize the latent embedding of each modality into shared and private components and perform knowledge transfer considering both the modality information commonality and gaps. Second, we enforce structural correlation constraints between the source modality and the target modality, to push the target modal embedding space symmetric to the source modal embedding space, with the anchoring of additional source-modal samples, which expands the existing modal-matching objective in current multi-modal contrastive frameworks. Finally, we conduct downstream task finetuning in the spherical space with a KNN classifier to better align with the structured modality embedding space. Extensive evaluations on five multimodal IoT datasets are performed to validate the effectiveness of SemiCMT in cross-modal knowledge transfer, including a new self-collected dataset using seismic and acoustic signals for office activity monitoring. SemiCMT consistently outperforms existing self-supervised and knowledge transfer approaches by up to 36.47% in the finetuned target-modal classification tasks. The code and the self-collected dataset will be released at https://github.com/SJTU-RTEAS/SemiCMT.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699779"}, {"primary_key": "379534", "vector": [], "sparse_vector": [], "title": "ViObject: <PERSON><PERSON>ss Passive Vibrations for Daily Object Recognition with Commodity Smartwatches.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Seongkook Heo", "Shwetak N. Patel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Knowing the object grabbed by a hand can offer essential contextual information for interaction between the human and the physical world. This paper presents a novel system, ViObject, for passive object recognition that uses accelerometer and gyroscope sensor data from commodity smartwatches to identify untagged everyday objects. The system relies on the vibrations caused by grabbing objects and does not require additional hardware or human effort. ViObject's ability to recognize objects passively can have important implications for a wide range of applications, from smart home automation to healthcare and assistive technologies. In this paper, we present the design and implementation of ViObject, to address challenges such as motion interference, different object-touching positions, different grasp speeds/pressure, and model customization to new users and new objects. We evaluate the system's performance using a dataset of 20 objects from 20 participants and show that ViObject achieves an average accuracy of 86.4%. We also customize models for new users and new objects, achieving an average accuracy of 90.1%. Overall, ViObject demonstrates a novel technology concept of passive object recognition using commodity smartwatches and opens up new avenues for research and innovation in this area.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643547"}, {"primary_key": "379535", "vector": [], "sparse_vector": [], "title": "SilverCycling: Exploring the Impact of Bike-Based Locomotion on Spatial Orientation for Older Adults in VR.", "authors": ["<PERSON><PERSON><PERSON>", "Zhiqing Wu", "Yucheng Liu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Fan"], "summary": "Spatial orientation is essential for people to effectively navigate and interact with the environment in everyday life. With age-related cognitive decline, providing VR locomotion techniques with better spatial orientation performance for older adults becomes important. Such advancements not only make VR more accessible to older adults but also enable them to reap the potential health benefits of VR technology. Natural motion-based locomotion has been shown its effective in enhancing younger users' performance in VR navigation tasks that require spatial orientation. However, there is a lack of understanding regarding the impact of natural motion-based locomotion on spatial orientation for older adults in VR. To address this gap, we selected the SilverCycling system, a VR bike-based locomotion technique that we developed, as a representative of natural motion-based locomotion, guided by findings from our pilot study. We conducted a user study with 16 older adults to compare SilverCycling with the joystick-based controller. The findings suggest SilverCycling potential to significantly enhance spatial orientation in the open-road urban environment for older adults, offering a better user experience. Based on our findings, we identify key factors influencing spatial orientation and propose design recommendations to make VR locomotion more accessible and user-friendly for older adults.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678522"}, {"primary_key": "379537", "vector": [], "sparse_vector": [], "title": "AeroSense: Sensing Aerosol Emissions from Indoor Human Activities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The types of human activities occupants are engaged in within indoor spaces significantly contribute to the spread of airborne diseases through emitting aerosol particles. Today, ubiquitous computing technologies can inform users of common atmosphere pollutants for indoor air quality. However, they remain uninformed of the rate of aerosol generated directly from human respiratory activities, a fundamental parameter impacting the risk of airborne transmission. In this paper, we present AeroSense, a novel privacy-preserving approach using audio sensing to accurately predict the rate of aerosol generated from detecting the kinds of human respiratory activities and determining the loudness of these activities. Our system adopts a privacy-first as a key design choice; thus, it only extracts audio features that cannot be reconstructed into human audible signals using two omnidirectional microphone arrays. We employ a combination of binary classifiers using the Random Forest algorithm to detect simultaneous occurrences of activities with an average recall of 85%. It determines the level of all detected activities by estimating the distance between the microphone and the activity source. This level estimation technique yields an average of 7.74% error. Additionally, we developed a lightweight mask detection classifier to detect mask-wearing, which yields a recall score of 75%. These intermediary outputs are critical predictors needed for AeroSense to estimate the amounts of aerosol generated from an active human source. Our model to predict aerosol is a Random Forest regression model, which yields 2.34 MSE and 0.73 r2 value. We demonstrate the accuracy of AeroSense by validating our results in a cleanroom setup and using advanced microbiological technology. We present results on the efficacy of AeroSense in natural settings through controlled and in-the-wild experiments. The ability to estimate aerosol emissions from detected human activities is part of a more extensive indoor air system integration, which can capture the rate of aerosol dissipation and inform users of airborne transmission risks in real time.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659593"}, {"primary_key": "379538", "vector": [], "sparse_vector": [], "title": "U-Flash: Improving Underwater Optical Communication by Scattering Effect.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Yu Sun", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Underwater communication has been a focal point of communication research, driven by a multitude of applications including underwater facility maintenance and marine exploration. However, current systems often require expensive specialized equipment, rendering them less accessible. In this paper, we break the norm by pioneering the use of Commercial Off-The-Shelf (COTS) smartphones for underwater optical communication, leveraging their built-in flashlight and camera. Contrary to prevalent belief that scattering has a negative impact on communication, we discover that scattering in water can provide a positive gain for camera-based optical communication. Based on this insight, we present and implement U-Flash, a novel system encompassing a Fresnel lens plugin design to enhance light directionality and a tailored encoding-decoding scheme optimized for underwater environments. Comprehensive experiments demonstrate the effectiveness of U-Flash, achieving significant improvements in communication distance, reliability, and data rate. Specifically, the achievable communication distance is improved from 2.4 m to 7.6 m during daytime in the presence of strong ambient light interference and further extended to 9.8 m in low ambient light scenarios at night.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699769"}, {"primary_key": "379539", "vector": [], "sparse_vector": [], "title": "A Multi-sensory Kiosk Interface to Familiarize Users with New Foods.", "authors": ["Euns<PERSON> Choi", "<PERSON><PERSON>"], "summary": "Familiarity significantly impacts public willingness to try new foods, making novel foods unlikely to be consumed. However, exposing eaters to various sensory modalities---encompassing vision, smell, sound, and touch---prior to eating may enhance their willingness to try these foods. Although multisensory methodologies have already been developed to transmit the sensory cues of food digitally, existing technology is limited to replication, and novel technologies remain relatively scarce in traditional food practices. Through a sensory design activity, multisensory interactions were incorporated into a self-ordering kiosk used for daily food selection. Subsequently, we conducted an experiment to observe how participants perceived a novel food on a multisensory interface, and found them to exhibit a significantly increased willingness to try the food regardless of level of fear. We also observed the multisensory interface to yield statistically significant results in food familiarity and overall satisfaction. These findings suggest that technology that integrates sensory exposure into daily life can effectively educate and familiarize people with novel food products.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643545"}, {"primary_key": "379540", "vector": [], "sparse_vector": [], "title": "Changing Your Tune: Lessons for Using Music to Encourage Physical Activity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Our research investigated whether music can communicate physical activity levels in daily life. Past studies have shown that simple musical tunes can provide wellness information, but no study has examined whether musical feedback can affect daily behavior or lead to healthier habits. We conducted a within-subject study with 62 participants over a period of 76 days, providing either musical or text-based feedback on their daily physical activity. The music was built and personalized based on participants' step counts and baseline wellness perceptions. Results showed that participants were marginally more active during the music feedback compared to their baseline period, and significantly more active compared to the text-based feedback (p = 0.000). We also find that the participant's average activity may influence the musical features they find most inspiration within a song. Finally, context influenced how musical feedback was interpreted, and specific musical features correlated with higher activity levels regardless of baseline perceptions. We discuss lessons learned for designing music-based feedback systems for health communication.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659611"}, {"primary_key": "379541", "vector": [], "sparse_vector": [], "title": "AutoTherm: A Dataset and Benchmark for Thermal Comfort Estimation Indoors and in Vehicles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Thermal comfort inside buildings is a well-studied field where human judgment for thermal comfort is collected and may be used for automatic thermal comfort estimation. However, indoor scenarios are rather static in terms of thermal state changes and, thus, cannot be applied to dynamic conditions, e.g., inside a vehicle. In this work, we present our findings of a gap between building and in-vehicle scenarios regarding thermal comfort estimation. We provide evidence by comparing deep neural classifiers for thermal comfort estimation for indoor and in-vehicle conditions. Further, we introduce a temporal dataset for indoor predictions incorporating 31 input signals and self-labeled user ratings by 18 subjects in a self-built climatic chamber. For in-vehicle scenarios, we acquired a second dataset featuring human judgments from 20 subjects in a BMW 3 Series. Our experimental results indicate superior performance for estimations from time series data over single vector input. Leveraging modern machine learning architectures enables us to recognize human thermal comfort states and estimate future states automatically. We provide details on training a recurrent network-based classifier and perform an initial performance benchmark of the proposed dataset. Ultimately, we compare our collected dataset to publicly available thermal comfort datasets.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678503"}, {"primary_key": "379542", "vector": [], "sparse_vector": [], "title": "Longitudinal Effects of External Communication of Automated Vehicles in the USA and Germany: A Comparative Study in Virtual Reality and Via a Browser.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated vehicles are expected to communicate with vulnerable road users. In two longitudinal studies, we investigated the impact of external Human-Machine Interfaces (eHMI) on pedestrian safety and behavior when interacting with automated vehicles. Utilizing LED strips for communication, these studies probed various factors, including mixed traffic scenarios, presence of eHMIs, and being from Germany or the USA. Our experimental approaches included a Virtual Reality study with 24 participants in Germany and an online study with 28 participants from the USA and Germany. Results revealed that repeated interactions with automated vehicles featuring eHMI significantly enhance pedestrian Trust, Understanding, and perceived safety, while simultaneously diminishing mental workload. Notably, the positive effects of eHMI were consistent across the two countries. US participants exhibited a tendency for higher risk-taking in crossing situations and reported lower mental workloads, underscoring the importance of considering cultural nuances in designing eHMI systems for mixed-traffic environments.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699778"}, {"primary_key": "379543", "vector": [], "sparse_vector": [], "title": "ShouldAR: Detecting Shoulder Surfing Attacks Using Multimodal Eye Tracking and Augmented Reality.", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Shoulder surfing attacks (SSAs) are a type of observation attack designed to illicitly gather sensitive data from \"over the shoulder\" of victims. This attack can be directed at mobile devices, desktop screens, Personal Identification Number (PIN) pads at an Automated Teller Machine (ATM), or written text. Existing solutions are generally focused on authentication techniques (e.g., logins) and are limited to specific attack scenarios (e.g., mobile devices or PIN Pads). We present ShotjldAR, a mobile and usable system to detect SSAs using multimodal eye gaze information (i.e., from both the potential attacker and victim). ShouldAR uses an augmented reality headset as a platform to incorporate user eye gaze tracking, rear-facing image collection and eye gaze analysis, and user notification of potential attacks. In a 24-participant study, we show that the prototype is capable of detecting 87.28% of SSAs against both physical and digital targets, a two-fold improvement on the baseline solution using a rear-facing mirror, a widely used solution to the SSA problem. The ShouldAR approach provides an AR-based, active SSA defense that applies to both digital and physical information entry in sensitive environments.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678573"}, {"primary_key": "379544", "vector": [], "sparse_vector": [], "title": "Digital Forms for All: A Holistic Multimodal Large Language Model Agent for Health Data Entry.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Digital forms help us access services and opportunities, but they are not equally accessible to everyone, such as older adults or those with sensory impairments. Large language models (LLMs) and multimodal interfaces offer a unique opportunity to increase form accessibility. Informed by prior literature and needfinding, we built a holistic multimodal LLM agent for health data entry. We describe the process of designing and building our system, and the results of a study with older adults (N =10). All participants, regardless of age or disability status, were able to complete a standard 47-question form independently using our system---one blind participant said it was \"a prayer answered.\" Our video analysis revealed how different modalities provided alternative interaction paths in complementary ways (e.g., the buttons helped resolve transcription errors and speech helped provide more options when the pre-canned answer choices were insufficient). We highlight key design guidelines, such as designing systems that dynamically adapt to individual needs.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659624"}, {"primary_key": "379546", "vector": [], "sparse_vector": [], "title": "CrossGAI: A Cross-Device Generative AI Framework for Collaborative Fashion Design.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Fashion design usually requires multiple designers to discuss and collaborate to complete a set of fashion designs, and the efficiency of the sketching process is another challenge for personalized design. In this paper, we introduce a fashion design system, CrossGAI, that can support multiple designers to collaborate on different devices and provide AI-enhanced sketching assistance. Based on the design requirements analysis acquired from the formative study of designers, we develop the system framework of CrossGAI implemented by the user-side web-based cross-device design platform working along with the server-side AI-integrated backend system. The CrossGAI system can be agilely deployed in LAN networks which protects the privacy and security of user data. To further improve both the efficiency and the quality of the sketch process, we devised and exploited generative AI modules, including a sketch retrieval module to retrieve sketches according to stroke or sketch drawn, a sketch generation module enabling the generation of fashion sketches consistent with the designer's unique aesthetic, and an image synthesis module that could achieve sketch-to-image synthesis in accordance with the reference image's style. To optimise the computation offloading when multiple user processes are handled in LAN networks, L<PERSON><PERSON>nov algorithm with DNN actor is utilized to dynamically optimize the network bandwidth of different clients based on their access history to the application and reduce network latency. The performance of our modules is verified through a series of evaluations under LAN environment, which prove that our CrossGAI system owns competitive ability in AIGC-aided designing. Furthermore, the qualitative analysis on user experience and work quality demonstrates the efficiency and effectiveness of CrossGAI system in design work.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643542"}, {"primary_key": "379547", "vector": [], "sparse_vector": [], "title": "Comfort in Automated Driving: A Literature Survey and a High-Level Integrative Framework.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The advancement of automated vehicle technology and the resulting shift from active driver control to a more passive role introduce previously unexplored factors that influence drivers' comfort. Examples include the vehicle's level of automation and the driver's preferred driving style, trust in the vehicle, and situation awareness. To structure these as a resource that can support future research, we conducted a comprehensive literature review, identifying 51 works that directly or peripherally address comfort in an automated driving context. Most of these works focus on the physical component of comfort, rooted in vehicle dynamics, while only a few consider a broader concept of comfort necessary to encompass a more expansive set of factors. Based on this review, we propose an integrative framework of 27 comfort influencing factors and their interrelationships. We categorize factors into six groups, encompassing the driving environment, vehicle physical features and automation system, and the user's activity, individual characteristics, and understanding of the automated system. These six groups are organized into the three larger categories of environment, vehicle, and user-related considerations. Patterns that emerge from the framework include that: a) some factors primarily influence physical well-being (such as motion forces), b) some contribute to discomfort (automation failures) while others contribute to comfort (secondary activities), c) some are stable and known before the trip (individual characteristics) while others change over time (environment), and d) comfort or discomfort can lead users to change either the relevant factors (level of automation) or their own behavior (secondary activities).", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678583"}, {"primary_key": "379548", "vector": [], "sparse_vector": [], "title": "Densor: An Intraoral Battery-Free Sensing Platform.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The mouth offers valuable insights into the condition of the human body. Yet, deploying intraoral sensors to measure oral temperature or jaw movements poses challenges in safety and acceptability. Consequently, real-world data for intraoral research is scarce. To address this gap, we leverage the widespread use of dental retainers and enhance them with Densor: an electronic sensing platform requiring only a standard smartphone for charging and data retrieval using a Near Field Communication interface. Its low power architecture enables prolonged sensing on a single charge, making it suitable for sleep studies. It can provide practitioners with feedback on treatment compliance, and is even able to detect if the user is speaking or drinking water. Densor presents an intraoral, actively powered, battery-free platform featuring multi-modal sensors and an extended lifespan.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699746"}, {"primary_key": "379551", "vector": [], "sparse_vector": [], "title": "rTsfNet: A DNN Model with Multi-head 3D Rotation and Time Series Feature Extraction for IMU-based Human Activity Recognition.", "authors": ["<PERSON>"], "summary": "Many deep learning (DL) models have been proposed for the IMU (inertial measurement unit) based HAR (human activity recognition) domain. However, combinations of manually designed time series features (TSFs) and traditional machine learning (ML) often continue to perform well. It is not rare that combinations among TSFs and DL show better performance than the DL-only approaches. Those facts mean that TSFs have the potential to outperform automatically generated features using deep neural networks (DNNs). However, TSFs have a problem: their performances are only good if appropriate 3D bases are selected. Fortunately, DL's strengths include capturing the features of input data and adaptively deriving parameters automatically. Thus, as a new DNN model for an IMU-based HAR, this paper proposes rTsfNet, a DNN model with Multi-head 3D Rotation and Time Series Feature Extraction. rTsfNet automatically selects multiple 3D bases from which features should be derived by extracting 3D rotation parameters within the DNN. Then, TSFs are derived to achieve HAR results using multilayer perceptrons (MLPs). With this combination, rTsfNet showed higher performance than existing models under well-managed benchmark conditions and multiple datasets: UCI HAR, PAMAP2, Daphnet, and OPPORTUNITY, all of which target different activities.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699733"}, {"primary_key": "379552", "vector": [], "sparse_vector": [], "title": "Beamforming for Sensing: Hybrid Beamforming based on Transmitter-Receiver Collaboration for Millimeter-Wave Sensing.", "authors": ["<PERSON> Fan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous mmWave sensing solutions assumed good signal quality. Ensuring an unblocked or strengthened LoS path is challenging. Therefore, finding an NLoS path is crucial to enhancing perceived signal quality. This paper proposes <PERSON><PERSON><PERSON>en, a Transmitter-REceiver collaboration-based Beamforming scheme SENsing using commercial mmWave radars. Specifically, we define the hybrid beamforming problem as an optimization challenge involving beamforming angle search based on transmitter-receiver collaboration. We derive a comprehensive expression for parameter optimization by modeling the signal attenuation variations resulting from the propagation path. To comprehensively assess the perception signal quality, we design a novel metric perceived signal-to-interference-plus-noise ratio (PSINR), combining the carrier signal and baseband signal to quantify the fine-grained sensing motion signal quality. Considering the high time cost of traversing or randomly searching methods, we employ a search method based on deep reinforcement learning to quickly explore optimal beamforming angles at both transmitter and receiver. We implement <PERSON><PERSON><PERSON><PERSON> and evaluate its performance in a fine-grained sensing application (i.e., heartbeat). Experimental results show that <PERSON><PERSON><PERSON><PERSON> significantly enhances heartbeat sensing performance in blocked or misaligned LoS scenes. Comparing non-beamforming, <PERSON><PERSON><PERSON><PERSON> demonstrates a reduction of 23.6% in HR error and 27.47% in IBI error. Moreover, comparing random search, <PERSON><PERSON><PERSON><PERSON> exhibits a 90% increase in search speed.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659619"}, {"primary_key": "379553", "vector": [], "sparse_vector": [], "title": "Evaluating the Privacy Valuation of Personal Data on Smartphones.", "authors": ["Lihua Fan", "<PERSON><PERSON>", "Yan Kong", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smartphones hold a great variety of personal data during usage, which at the same time poses privacy risks. In this paper, we used the selling price to reflect users' privacy valuation of their personal data on smartphones. In a 7-day auction, they sold their data as commodities and earn money. We first designed a total of 49 commodities with 8 attributes, covering 14 common types of personal data on smartphones. Then, through a large-scale reverse second price auction (N=181), we examined students' valuation of 15 representative commodities. The average bid-price was 62.8 CNY (8.68 USD) and a regression model with 14 independent variables found the most influential factors for bid-price to be privacy risk, ethnic and gender. When validating our results on non-students (N=34), we found that despite they gave significantly higher prices (M=109.8 CNY, 15.17 USD), \"privacy risk\" was still one of the most influential factors among the 17 independent variables in the regression model. We recommended that stakeholders should provide 8 attributes of data when selling or managing it.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678509"}, {"primary_key": "379554", "vector": [], "sparse_vector": [], "title": "3D Bounding Box Estimation Based on COTS mmWave Radar via Moving Scanning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Object boundary estimation, usually achieved by bounding box estimation, is crucial in various applications, such as intelligent driving, which facilitates further interactions like obstacle avoidance and navigation. Existing solutions mainly rely on computer vision, which often performs poorly in low-visibility conditions, e.g., harsh weather, and has limited resolution for depth estimation. Recent studies show the potential of mmWave radar for object detection. However, due to the inherent drawbacks, conventional mmWave techniques suffer from the severe interference of noise points in the points cloud, leading to the position vagueness, as well as sparsity and limited spatial resolution, which leads to the boundary vagueness. In this paper, we propose a novel bounding box estimation system based on mmWave radar that sufficiently leverages the spatial features of the antenna array and the temporal features of moving scanning to detect objects and estimate their 3D bounding boxes. To mitigate the interference from noise points, we introduce a new integration metric, Reflection Saliency, which evaluates the effectiveness of each point based on signal-to-noise ratio (SNR), speed, and spatial domains, successfully reducing the majority of noise points. Moreover, we propose the Prior-Time Heuristic Point Cloud Augmentation method to enrich the point representation of objects based on the previous data. To obtain boundary information, we propose a beamforming-based model to extract the Angle-Reflection Profile (ARP), which depicts the spatial distribution of the object's reflection. Furthermore, a generative neural network is used to refine the boundary and estimate the 3D bounding box by incorporating the ARP features, SNR of cloud points, and depth information. We have implemented an actual system prototype using a robot car in real scenarios and extensive experiments show that the average position error of the proposed system in 3D bounding box estimation is 0.11m.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699758"}, {"primary_key": "379556", "vector": [], "sparse_vector": [], "title": "Pushing the Limits of Acoustic Spatial Perception via Incident Angle Encoding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Pan", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the growing popularity of smart speakers, numerous novel acoustic sensing applications have been proposed for low-frequency human speech and high-frequency inaudible sounds. Spatial information plays a crucial role in these acoustic applications, enabling various location-based services. However, typically commercial microphone arrays face limitations in spatial perception of inaudible sounds due to their sparse array geometries optimized for low-frequency speech. In this paper, we introduce MetaAng, a system designed to augment microphone arrays by enabling wideband spatial perception across both speech signals and inaudible sounds by leveraging the spatial encoding capabilities of acoustic metasurfaces. Our design is grounded in the fact that, while sensitive to high-frequency signals, acoustic metasurfaces are almost non-responsive to low-frequency speech due to significant wavelength discrepancy. This observation allows us to integrate acoustic metasurfaces with sparse array geometry, simultaneously enhancing the spatial perception of high-frequency and low-frequency acoustic signals. To achieve this, we first utilize acoustic metasurfaces and a configuration optimization algorithm to encode the unique features for each incident angle. Then, we propose an unrolling soft thresholding network that employs neural-enhanced priors and compressive sensing for high-accuracy, high-resolution multi-source angle estimation. We implement a prototype, and experimental results demonstrate that MetaAng maintains robustness across various scenarios, facilitating multiple applications, including localization and tracking.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659583"}, {"primary_key": "379557", "vector": [], "sparse_vector": [], "title": "DEDector: Smartphone-Based Noninvasive Screening of Dry Eye Disease.", "authors": ["<PERSON><PERSON><PERSON>v Ganatra", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dry Eye Disease (DED) is an eye condition characterized by abnormalities in tear film stability. Despite its high prevalence, screening for DED remains challenging, primarily due to the invasive nature of most diagnostic tests. The Fluorescein Break-Up Time (FBUT) test, which involves instilling fluorescein dye in the eye, is a widely used method for assessing tear film stability. In this work, we propose DEDector, a low-cost, smartphone-based automated Non-Invasive Break-Up Time (NIBUT) measurement for DED screening. Utilizing a 3D-printed Placido ring attachment on a smartphone's camera, DEDector projects concentric rings onto the cornea, capturing a video for subsequent analysis using our proposed video processing pipeline to identify tear film stability. We conducted a real-world evaluation on 46 eyes, comparing DEDector with the traditional FBUT method. DEDector achieved a sensitivity of 77.78% and specificity of 82.14%, outperforming FBUT.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699742"}, {"primary_key": "379558", "vector": [], "sparse_vector": [], "title": "EasyAsk: An In-App Contextual Tutorial Search Assistant for Older Adults with Voice and Touch Inputs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Weinan Shi", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An easily accessible tutorial is crucial for older adults to use mobile applications (apps) on smartphones. However, older adults often struggle to search for tutorials independently and efficiently. Through a formative study, we investigated the demands of older adults in seeking assistance and identified patterns of older adults' behaviors and verbal questions when seeking help for smartphone-related issues. Informed by the findings from the formative study, we designed EasyAsk, an app-independent method to make tutorial search accessible for older adults. This method was implemented as an Android app. Using EasyAsk, older adults can obtain interactive tutorials through voice and touch whenever they encounter problems using smartphones. To power the method, EasyAsk uses a large language model to process the voice text and contextual information provided by older adults, and another large language model to search for the tutorial. Our user experiment, involving 18 older participants, demonstrated that EasyAsk helped users obtain tutorials correctly in 98.94% of cases, making tutorial search accessible and natural.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678516"}, {"primary_key": "379559", "vector": [], "sparse_vector": [], "title": "ChatIoT: Zero-code Generation of Trigger-action Based IoT Programs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Fu Li", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Trigger-Action Program (TAP) is a simple but powerful format to realize intelligent IoT applications, especially in home automation scenarios. Existing trace-driven approaches and in-situ programming approaches depend on either customized interaction commands or well-labeled datasets, resulting in limited applicable scenarios. In this paper, we propose ChatIoT, a zero-code TAP generation system based on large language models (LLMs). With a novel context-aware compressive prompting scheme, ChatIoT is able to automatically generate TAPs from user requests in a token-efficient manner and deploy them to the TAP runtime. Further, for those TAP requests including unknown sensing abilities, ChatIoT can also generate new AI models with knowledge distillation by multimodal LLMs, with a novel model customization method based on deep reinforcement learning. We implemented ChatIoT and evaluated its performance extensively. Results show that ChatIoT can reduce token consumption by 26.1-84.9% and improve TAP generation accuracy by 4.2-65.5% compared to state-of-the-art approaches in multiple settings. We also conducted a real user study, and ChatIoT can achieve 91.57% TAP generation accuracy.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678585"}, {"primary_key": "379560", "vector": [], "sparse_vector": [], "title": "PressInPose: Integrating Pressure and Inertial Sensors for Full-Body Pose Estimation in Activities.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yingcheng Jin", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The accurate assessment of human body posture through wearable technology has significant implications for sports science, clinical diagnostics, rehabilitation, and VR interaction. Traditional methods often require complex setups or are limited by the environment's constraints. In response to these challenges, this paper presents an innovative approach to human posture estimation under complex motion scenarios through the development of an advanced shoe insole embedded with pressure sensors and an Inertial Measurement Unit (IMU). Coupled with a single wrist-mounted IMU, our system facilitates a comprehensive analysis of human biomechanics by integrating physical kinematics modeling based on pressure data with a multi-region human posture estimation network. To enhance the robustness of our system model, we employed large language models to generate virtual human motion sequences. These sequences were utilized to create synthetic IMU data for data augmentation purposes, addressing the challenge of limited real-world data availability and variability. Our approach uniquely combines physical modeling with data-driven techniques to improve the accuracy and reliability of posture estimation. Experimental results demonstrate that our integrated system significantly advances wearable technology for motion analysis. The Mean Per Joint Position Error (MPJPE) was reduced to 7.75 cm, highlighting the effectiveness of our multi-modal modeling and virtual data augmentation in refining posture estimation.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699773"}, {"primary_key": "379561", "vector": [], "sparse_vector": [], "title": "A Digital Companion Architecture for Ambient Intelligence.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ambient Intelligence (AmI) focuses on creating environments capable of proactively and transparently adapting to users and their activities. Traditionally, AmI focused on the availability of computational devices, the pervasiveness of networked environments, and means to interact with users. In this paper, we propose a renewed AmI architecture that takes into account current technological advancements while focusing on proactive adaptation for assisting and protecting users. This architecture consist of four phases: Perceive, Interpret, Decide, and Interact. The AmI systems we propose, called Digital Companions (DC), can be embodied in a variety of ways (e.g., through physical robots or virtual agents) and are structured according to these phases to assist and protect their users. We further categorize DCs into Expert DCs and Personal DCs, and show that this induces a favorable separation of concerns in AmI systems, where user concerns (including personal user data and preferences) are handled by Personal DCs and environment concerns (including interfacing with environmental artifacts) are assigned to Expert DCs; this separation has favorable privacy implications as well. Herein, we introduce this architecture and validate it through a prototype in an industrial scenario where robots and humans collaborate to perform a task.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659610"}, {"primary_key": "379562", "vector": [], "sparse_vector": [], "title": "Detecting Users&apos; Emotional States during Passive Social Media Use.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The widespread use of social media significantly impacts users' emotions. Negative emotions, in particular, are frequently produced, which can drastically affect mental health. Recognizing these emotional states is essential for implementing effective warning systems for social networks. However, detecting emotions during passive social media use---the predominant mode of engagement---is challenging. We introduce the first predictive model that estimates user emotions during passive social media consumption alone. We conducted a study with 29 participants who interacted with a controlled social media feed. Our apparatus captured participants' behavior and their physiological signals while they browsed the feed and filled out self-reports from two validated emotion models. Using this data for supervised training, our emotion classifier robustly detected up to 8 emotional states and achieved 83% peak accuracy to classify affect. Our analysis shows that behavioral features were sufficient to robustly recognize participants' emotions. It further highlights that within 8 seconds following a change in media content, objective features reveal a participant's new emotional state. We show that grounding labels in a componential emotion model outperforms dimensional models in higher-resolutional state detection. Our findings also demonstrate that using emotional properties of images, predicted by a deep learning model, further improves emotion recognition.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659606"}, {"primary_key": "379563", "vector": [], "sparse_vector": [], "title": "Passive Haptic Rehearsal for Augmented Piano Learning in the Wild.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Passive haptic learning (PHL) is a method for training motor skills via intensive repetition of haptic stimuli while a user is focused on other tasks. For the practical application of PHL to music education, we propose passive haptic rehearsal (PHR) where PHL is combined with deliberate active practice. We designed a piano teaching system that includes haptic gloves compatible with daily wear, a Casio keyboard with light-up keys, and an online learning portal that enables users to track performance, choose lessons, and connect with the gloves and keyboard. We conducted a longitudinal two-week study in the wild, where 36 participants with musical experience learned to play two piano songs with and without PHR. For 20 participants with complete and valid data, we found that PHR boosted the learning rate for the matching accuracy by 49.7% but did not have a significant effect on learning the notes' rhythm. Participants across all skill levels in the study require approximately two days less to reach mastery on the songs practiced when using PHR. We also confirmed that PHR boosts recall between active practice sessions. We hope that our results and system will enable the deployment of PHL beyond the laboratory.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699748"}, {"primary_key": "379564", "vector": [], "sparse_vector": [], "title": "BreathePulse: Peripheral Guided Breathing via Implicit Airflow Cues for Information Work.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Workplace stress contributes to poor performance and adverse health outcomes, yet current stress management tools often fall short in the fast-paced modern workforce. Guided slow breathing is a promising intervention for stress and anxiety, with peripheral breathing guides being explored for concurrent task use. However, their need for explicit user engagement underscores the need for more seamless, implicit interventions optimized for workplaces. In this mixed-method, controlled study, we examined the feasibility and effects of BreathePulse, a laptop-mounted device that delivers pulsing airflow to the nostrils as an implicit cue, on stress, anxiety, affect, and workload during two levels of a memory (N-Back) task with 23 participants. We found that <PERSON><PERSON><PERSON><PERSON><PERSON>, the first airflow-only breathing guide, effectively promoted slow breathing, particularly during the easy memory task. Participants' breathing rates aligned with BreathePulse's guidance across tasks, with the longest maintenance of slow breathing - over 40% of the time - during the easy task. Although BreathePulse increased workload and had little impact on stress, it promoted mindfulness, indicating its potential for stress management in the workplace.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3702211"}, {"primary_key": "379565", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving and Cross-Domain Human Sensing by Federated Domain Adaptation with Semantic Knowledge Correction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Federated Learning (FL) enables distributed training of human sensing models in a privacy-preserving manner. While promising, federated global models suffer from cross-domain accuracy degradation when the labeled source domains statistically differ from the unlabeled target domain. To tackle this problem, recent methods perform pairwise computation on the source and target domains to minimize the domain discrepancy by adversarial strategy. However, these methods are limited by the fact that pairwise source-target adversarial alignment alone only achieves domain-level alignment, which entails the alignment of domain-invariant as well as environment-dependent features. The misalignment of environment-dependent features may cause negative impact on the performance of the federated global model. In this paper, we introduce FDAS, a Federated adversarial Domain Adaptation with Semantic Knowledge Correction method. FDAS achieves concurrent alignment at both domain and semantic levels to improve the semantic quality of the aligned features, thereby reducing the misalignment of environment-dependent features. Moreover, we design a cross-domain semantic similarity metric and further devise feature selection and feature refinement mechanisms to enhance the two-level alignment. In addition, we propose a similarity-aware model fine-tuning strategy to further improve the target model performance. We evaluate the performance of FDAS extensively on four public and a real-world human sensing datasets. Extensive experiments demonstrate the superior effectiveness of FDAS and its potential in the real-world ubiquitous computing scenarios.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643503"}, {"primary_key": "379566", "vector": [], "sparse_vector": [], "title": "SonicVista: Towards Creating Awareness of Distant Scenes through Sonification.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>", "Sur<PERSON>"], "summary": "Spatial awareness, particularly awareness of distant environmental scenes known as vista-space, is crucial and contributes to the cognitive and aesthetic needs of People with Visual Impairments (PVI). In this work, through a formative study with PVIs, we establish the need for vista-space awareness amongst people with visual impairments, and the possible scenarios where this awareness would be helpful. We investigate the potential of existing sonification techniques as well as AI-based audio generative models to design sounds that can create awareness of vista-space scenes. Our first user study, consisting of a listening test with sighted participants as well as PVIs, suggests that current AI generative models for audio have the potential to produce sounds that are comparable to existing sonification techniques in communicating sonic objects and scenes in terms of their intuitiveness, and learnability. Furthermore, through a wizard-of-oz study with PVIs, we demonstrate the utility of AI-generated sounds as well as scene audio recordings as auditory icons to provide vista-scene awareness, in the contexts of navigation and leisure. This is the first step towards addressing the need for vista-space awareness and experience in PVIs.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659609"}, {"primary_key": "379567", "vector": [], "sparse_vector": [], "title": "EarSleep: In-ear Acoustic-based Physical and Physiological Activity Recognition for Sleep Stage Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Since sleep plays an important role in people's daily lives, sleep monitoring has attracted the attention of many researchers. Physical and physiological activities occurring in sleep exhibit unique patterns in different sleep stages. It indicates that recognizing a wide range of sleep activities (events) can provide more fine-grained information for sleep stage detection. However, most of the prior works are designed to capture limited sleep events and coarse-grained information, which cannot meet the needs of fine-grained sleep monitoring. In our work, we leverage ubiquitous in-ear microphones on sleep earbuds to design a sleep monitoring system, named EarSleep1, which interprets in-ear body sounds induced by various representative sleep events into sleep stages. Based on differences among physical occurrence mechanisms of sleep activities, EarSleep extracts unique acoustic response patterns from in-ear body sounds to recognize a wide range of sleep events, including body movements, sound activities, heartbeat, and respiration. With the help of sleep medicine knowledge, interpretable acoustic features are derived from these representative sleep activities. EarSleep leverages a carefully designed deep learning model to establish the complex correlation between acoustic features and sleep stages. We conduct extensive experiments with 48 nights of 18 participants over three months to validate the performance of our system. The experimental results show that our system can accurately detect a rich set of sleep activities. Furthermore, in terms of sleep stage detection, EarSleep outperforms state-of-the-art solutions by 7.12% and 9.32% in average precision and average recall, respectively.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659595"}, {"primary_key": "379568", "vector": [], "sparse_vector": [], "title": "EarSpeech: Exploring In-Ear Occlusion Effect on Earphones for Data-efficient Airborne Speech Enhancement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Earphones have become a popular voice input and interaction device. However, airborne speech is susceptible to ambient noise, making it necessary to improve the quality and intelligibility of speech on earphones in noisy conditions. As the dual-microphone structure (i.e., outer and in-ear microphones) has been widely adopted in earphones (especially ANC earphones), we design EarSpeech which exploits in-ear acoustic sensory as the complementary modality to enable airborne speech enhancement. The key idea of EarSpeech is that in-ear speech is less sensitive to ambient noise and exhibits a correlation with airborne speech. However, due to the occlusion effect, in-ear speech has limited bandwidth, making it challenging to directly correlate with full-band airborne speech. Therefore, we exploit the occlusion effect to carry out theoretical modeling and quantitative analysis of this cross-channel correlation and study how to leverage such cross-channel correlation for speech enhancement. Specifically, we design a series of methodologies including data augmentation, deep learning-based fusion, and noise mixture scheme, to improve the generalization, effectiveness, and robustness of EarSpeech, respectively. Lastly, we conduct real-world experiments to evaluate the performance of our system. Specifically, EarSpeech achieves an average improvement ratio of 27.23% and 13.92% in terms of PESQ and STOI, respectively, and significantly improves SI-SDR by 8.91 dB. Benefiting from data augmentation, EarSpeech can achieve comparable performance with a small-scale dataset that is 40 times less than the original dataset. In addition, we validate the generalization of different users, speech content, and language types, respectively, as well as robustness in the real world via comprehensive experiments. The audio demo of EarSpeech is available on https://github.com/EarSpeech/earspeech.github.io/.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678594"}, {"primary_key": "379569", "vector": [], "sparse_vector": [], "title": "Systematic Evaluation of Personalized Deep Learning Models for Affect Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Minseo Park", "<PERSON><PERSON><PERSON>"], "summary": "Understanding human affective states such as emotion and stress is crucial for both practical applications and theoretical research, driving advancements in the field of affective computing. While traditional approaches often rely on generalized models trained on aggregated data, recent studies highlight the importance of personalized models that account for individual differences in affective responses. However, there remains a significant gap in research regarding the comparative evaluation of various personalization techniques across multiple datasets. In this study, we address this gap by systematically evaluating widely-used deep learning-based personalization techniques for affect recognition across five open datasets (i.e., AMIGOS, ASCERTAIN, WESAD, CASE, and K-EmoCon). Our analysis focuses on realistic scenarios where models must adapt to new, unseen users with limited available data, reflecting real-world conditions. We emphasize the principles of reproducibility by utilizing open datasets and making our evaluation models and codebase publicly available. Our findings provide critical insights into the generalizability of personalization techniques, the data requirements for effective personalization, and the relative performance of different approaches. This work offers valuable contributions to the development of personalized affect recognition systems, fostering advancements in both methodology and practical application.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699724"}, {"primary_key": "379570", "vector": [], "sparse_vector": [], "title": "HCR-Auth: Reliable Bone Conduction Earphone Authentication with Head Contact Response.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Yangyang Gu", "<PERSON><PERSON>"], "summary": "Earables, or ear wearables, are increasingly being used for a variety of personal applications, prompting the development of authentication schemes to safeguard user privacy. Existing authentications are designed for traditional in-ear earphones, relying on a closed ear canal environment, which is not suited for bone conduction earphones that feature an open-ear design. In this paper, we propose HCR-Auth, a new authentication approach for bone conduction earphones based on head biometrics. It employs a modulated chirp signal, emitted by the earphone speaker, to actively sense the user's head structure, and captures the response through the earphone's accelerometer. It operates implicitly, eliminating additional efforts from the user to perform authentication. Through extensive experiments involving 60 subjects, we determine that HCR-Auth achieves a commendable balanced accuracy of 96.55% using only 10 registration samples, proving its efficacy and resilience against potential threats. Our dataset and source codes are available at https://anonymous.4open.science/r/HCR-Auth-D43B/.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699780"}, {"primary_key": "379571", "vector": [], "sparse_vector": [], "title": "CrossHAR: Generalizing Cross-dataset Human Activity Recognition via Hierarchical Self-Supervised Pretraining.", "authors": ["Zhiqing Hong", "Zelong Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The increasing availability of low-cost wearable devices and smartphones has significantly advanced the field of sensor-based human activity recognition (HAR), attracting considerable research interest. One of the major challenges in HAR is the domain shift problem in cross-dataset activity recognition, which occurs due to variations in users, device types, and sensor placements between the source dataset and the target dataset. Although domain adaptation methods have shown promise, they typically require access to the target dataset during the training process, which might not be practical in some scenarios. To address these issues, we introduce CrossHAR, a new HAR model designed to improve model performance on unseen target datasets. CrossHAR involves three main steps: (i) CrossHAR explores the sensor data generation principle to diversify the data distribution and augment the raw sensor data. (ii) CrossHAR then employs a hierarchical self-supervised pretraining approach with the augmented data to develop a generalizable representation. (iii) Finally, CrossHAR fine-tunes the pretrained model with a small set of labeled data in the source dataset, enhancing its performance in cross-dataset HAR. Our extensive experiments across multiple real-world HAR datasets demonstrate that CrossHAR outperforms current state-of-the-art methods by 10.83% in accuracy, demonstrating its effectiveness in generalizing to unseen target datasets.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659597"}, {"primary_key": "379572", "vector": [], "sparse_vector": [], "title": "SmallMap: Low-cost Community Road Map Sensing with Uncertain Delivery Behavior.", "authors": ["Zhiqing Hong", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Accurate road networks play a crucial role in modern mobile applications such as navigation and last-mile delivery. Most existing studies primarily focus on generating road networks in open areas like main roads and avenues, but little attention has been given to the generation of community road networks in closed areas such as residential areas, which becomes more and more significant due to the growing demand for door-to-door services such as food delivery. This lack of research is primarily attributed to challenges related to sensing data availability and quality. In this paper, we design a novel framework called SmallMap that leverages ubiquitous multi-modal sensing data from last-mile delivery to automatically generate community road networks with low costs. Our SmallMap consists of two key modules: (1) a Trajectory of Interest Detection module enhanced by exploiting multi-modal sensing data collected from the delivery process; and (2) a Dual Spatio-temporal Generative Adversarial Network module that incorporates Trajectory of Interest by unsupervised road network adaptation to generate road networks automatically. To evaluate the effectiveness of SmallMap, we utilize a two-month dataset from one of the largest logistics companies in China. The extensive evaluation results demonstrate that our framework significantly outperforms state-of-the-art baselines, achieving a precision of 90.5%, a recall of 87.5%, and an F1-score of 88.9%, respectively. Moreover, we conduct three case studies in Beijing City for courier workload estimation, Estimated Time of Arrival (ETA) in last-mile delivery, and fine-grained order assignment.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659596"}, {"primary_key": "379573", "vector": [], "sparse_vector": [], "title": "RFBoost: Understanding and Boosting Deep WiFi Sensing via Physical Data Augmentation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning shows promising performance in wireless sensing. However, deep wireless sensing (DWS) heavily relies on large datasets. Unfortunately, building comprehensive datasets for DWS is difficult and costly, because wireless data depends on environmental factors and cannot be labeled offline. Despite recent advances in few-shot/cross-domain learning, DWS is still facing data scarcity issues. In this paper, we investigate a distinct perspective of radio data augmentation (RDA) for WiFi sensing and present a data-space solution. Our key insight is that wireless signals inherently exhibit data diversity, contributing more information to be extracted for DWS. We present RFBoost, a simple and effective RDA framework encompassing novel physical data augmentation techniques. We implement RFBoost as a plug-and-play module integrated with existing deep models and evaluate it on multiple datasets. Experimental results demonstrate that RFBoost achieves remarkable average accuracy improvements of 5.4% on existing models without additional data collection or model modifications, and the best-boosted performance outperforms 11 state-of-the-art baseline models without RDA. RFBoost pioneers the study of RDA, an important yet currently underexplored building block for DWS, which we expect to become a standard DWS component of WiFi sensing and beyond. RFBoost is released at https://github.com/aiot-lab/RFBoost.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659620"}, {"primary_key": "379574", "vector": [], "sparse_vector": [], "title": "BreathPro: Monitoring Breathing Mode during Running with <PERSON><PERSON><PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Running is a popular and accessible form of aerobic exercise, significantly benefiting our health and wellness. By monitoring a range of running parameters with wearable devices, runners can gain a deep understanding of their running behavior, facilitating performance improvement in future runs. Among these parameters, breathing, which fuels our bodies with oxygen and expels carbon dioxide, is crucial to improving the efficiency of running. While previous studies have made substantial progress in measuring breathing rate, exploration of additional breathing monitoring during running is still lacking. In this work, we fill this gap by presenting BreathPro, the first breathing mode monitoring system for running. It leverages the in-ear microphone on earables to record breathing sounds and combines the out-ear microphone on the same device to mitigate external noises, thereby enhancing the clarity of in-ear breathing sounds. BreathPro incorporates a suite of well-designed signal processing and machine learning techniques to enable breathing mode detection with superior accuracy. We implemented BreathPro as a smartphone application and demonstrated its energy-efficient and real-time execution.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659607"}, {"primary_key": "379575", "vector": [], "sparse_vector": [], "title": "LR-Auth: Towards Practical Implementation of Implicit User Authentication on Earbuds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The increasing use of earbuds in applications like immersive entertainment and health monitoring necessitates effective implicit user authentication systems to preserve the privacy of sensitive data and provide personalized experiences. Existing approaches, which leverage physiological cues (e.g., jawbone structure) and behavioral cues (e.g., gait), face challenges such as limited usability, high delay and energy overhead, and significant computational demands, rendering them impractical for resource-constrained earbuds. To address these issues, we present LR-Auth, a lightweight, user-friendly implicit authentication system designed for various earbud usage scenarios. LR-Auth utilizes the modulation of sound frequencies by the user's unique occluded ear canal, generating user-specific templates through linear correlations between two audio streams instead of complex machine-learning models. Our prototype, evaluated with 30 subjects under diverse conditions, demonstrates over 99% balanced accuracy with five 100 ms audio segments, even in noisy environments and during music playback. LR-Auth significantly reduces system overhead, achieving a 20 × to 404 × decrease in latency and a 24 × to 410 × decrease in energy consumption compared to existing methods. These results highlight LR-Auth's potential for accurate, robust, and efficient user authentication on resource-constrained earbuds.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699793"}, {"primary_key": "379576", "vector": [], "sparse_vector": [], "title": "IOTeeth: Intra-Oral Teeth Sensing System for Dental Occlusal Diseases Recognition.", "authors": ["<PERSON><PERSON>zhang Hu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While occlusal diseases - the main cause of tooth loss -- significantly impact patients' teeth and well-being, they are the most underdiagnosed dental diseases nowadays. Experiencing occlusal diseases could result in difficulties in eating, speaking, and chronicle headaches, ultimately impacting patients' quality of life. Although attempts have been made to develop sensing systems for teeth activity monitoring, solutions that support sufficient sensing resolution for occlusal monitoring are missing. To fill that gap, this paper presents IOTeeth, a cost-effective and automated intra-oral sensing system for continuous and fine-grained monitoring of occlusal diseases. The IOTeeth system includes an intra-oral piezoelectric-based sensing array integrated into a dental retainer platform to support reliable occlusal disease recognition. IOTeeth focuses on biting and grinding activities from the canines and front teeth, which contain essential information of occlusion. IOTeeth's intra-oral wearable collects signals from the sensors and fetches them into a lightweight and robust deep learning model called Physioaware Attention Network (PAN Net) for occlusal disease recognition. We evaluate IOTeeth with 12 articulator teeth models from dental clinic patients. Evaluation results show an F1 score of 0.97 for activity recognition with leave-one-out validation and an average F1 score of 0.92 for dental disease recognition for different activities with leave-one-out validation.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643516"}, {"primary_key": "379577", "vector": [], "sparse_vector": [], "title": "Contactless Arterial Blood Pressure Waveform Monitoring with mmWave Radar.", "authors": ["Qingyong Hu", "<PERSON><PERSON>", "Hao Lu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Arterial blood pressure waveform (ABPW) offers comprehensive insights into cardiovascular health compared to discrete blood pressure measurements. However, accurately estimating shapes and pressure values of ABPW points in a beat-to-beat manner poses significant challenges. Current ABPW monitoring methods require invasive procedures or continuous skin contact, which are inconvenient and unsatisfactory. Thus, we propose WaveBP, the first contactless ABPW monitoring system utilizing a commercial mmWave radar, driven by the understanding that cardiac information serves as an implicit bridge between mmWave signals and ABPW based on a hemodynamics analysis model. To preserve waveform details, we design a hybrid Transformer model called mmFormer, incorporated with spatially-informed shortcuts. mmFormer enables consistent sequence-to-sequence transformations while accommodating different levels of personalization efforts. To mitigate the inherent instability of mmWave signals, we develop a beamforming-based data augmentation approach that has been empirically and theoretically proven to enhance robustness with multiple spatial observations. Additionally, we introduce a cross-modality knowledge transfer framework to fuse knowledge from cardiac modalities (ECG/PPG) with vibrations captured in mmWave reflections, improving accuracy without requiring extra deployment overhead. Extensive evaluations conducted on 43 subjects using a leave-one-subject-out setup validate that WaveBP achieves a high waveform correlation of 0.903 and exhibits a low (mean±standard deviation) error of point-level measurements at (-0.14±7.48) mmHg, which could be further reduced by subject-specific specialization. WaveBP demonstrates remarkable performance under challenging scenarios and exhibits potential for detailed cardiac estimations, as evidenced by our case studies on relative cardiac output estimation and cardiac abnormality detection.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699781"}, {"primary_key": "379578", "vector": [], "sparse_vector": [], "title": "SpeciFingers: Finger Identification and Error Correction on Capacitive Touchscreens.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shengfeng Qin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The inadequate use of finger properties has limited the input space of touch interaction. By leveraging the category of contacting fingers, finger-specific interaction is able to expand input vocabulary. However, accurate finger identification remains challenging, as it requires either additional sensors or limited sets of identifiable fingers to achieve ideal accuracy in previous works. We introduce SpeciFingers, a novel approach to identify fingers with the capacitive raw data on touchscreens. We apply a neural network of an encoder-decoder architecture, which captures the spatio-temporal features in capacitive image sequences. To assist users in recovering from misidentification, we propose a correction mechanism to replace the existing undo-redo process. Also, we present a design space of finger-specific interaction with example interaction techniques. In particular, we designed and implemented a use case of optimizing the performance in pointing on small targets. We evaluated our identification model and error correction mechanism in our use case.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643559"}, {"primary_key": "379581", "vector": [], "sparse_vector": [], "title": "PilotAR: Streamlining Pilot Studies with OHMDs from Concept to Insight.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sheng<PERSON> Zhao", "<PERSON>"], "summary": "Pilot studies in HCI research serve as a cost-effective approach to validate potential ideas and identify impactful findings before extensive studies. Yet, the additional requirements of AR/MR, such as multi-view observations and increased multitasking, make it challenging to conduct pilot studies effectively, hindering innovations in this field. Based on interviews with 12 AR/MR researchers, we identified the key challenges associated with conducting AR/MR pilot studies with Optical See-Through Head-Mounted Displays (OST-HMDs, OHMDs), including the inability to observe and record in-context user interactions, increased task load, and difficulties with in-context data analysis and discussion. To tackle these challenges, we introduce PilotAR, a desktop-based tool designed iteratively to enhance OHMD-based AR/MR pilot studies. PilotAR facilitates data collection via live first-person and third-person views, multi-modal annotations, and flexible wizarding interfaces. It also accommodates multi-experimenter settings, streamlines the study process with configurable workflows and shortcuts, records annotated data, and eases results sharing. Formative testing, conducted using three case studies, has highlighted the significant benefits of PilotAR, as well as its potential for further development and refinement.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678576"}, {"primary_key": "379582", "vector": [], "sparse_vector": [], "title": "PRECYSE: Predicting Cybersickness using Transformer for Multimodal Time-Series Sensor Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Han"], "summary": "Cybersickness, a factor that hinders user immersion in VR, has been the subject of ongoing attempts to predict it using AI. Previous studies have used CNN and LSTM for prediction models and used attention mechanisms and XAI for data analysis, yet none explored a transformer that can better reflect the spatial and temporal characteristics of the data, beneficial for enhancing prediction and feature importance analysis. In this paper, we propose cybersickness prediction models using multimodal time-series sensor data (i.e., eye movement, head movement, and physiological signals) based on a transformer algorithm, considering sensor data pre-processing and multimodal data fusion methods. We constructed the MSCVR dataset consisting of normalized sensor data, spectrogram formatted sensor data, and cybersickness levels collected from 45 participants through a user study. We proposed two methods for embedding multimodal time-series sensor data into the transformer: modality-specific spatial and temporal transformer encoders for normalized sensor data (MS-STTN) and modality-specific spatial-temporal transformer encoder for spectrogram (MS-STTS). MS-STTN yielded the highest performance in the ablation study and the comparison of the existing models. Furthermore, by analyzing the importance of data features, we determined their relevance to cybersickness over time, especially the salience of eye movement features. Our results and insights derived from multimodal time-series sensor data and the transformer model provide a comprehensive understanding of cybersickness and its association with sensor data. Our MSCVR dataset and code are publicly available: https://github.com/dayoung-jeong/PRECYSE.git.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659594"}, {"primary_key": "379583", "vector": [], "sparse_vector": [], "title": "HAIGEN: Towards Human-AI Collaboration for Facilitating Creativity and Style Generation in Fashion Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The process of fashion design usually involves sketching, refining, and coloring, with designers drawing inspiration from various images to fuel their creative endeavors. However, conventional image search methods often yield irrelevant results, impeding the design process. Moreover, creating and coloring sketches can be time-consuming and demanding, acting as a bottleneck in the design workflow. In this work, we introduce HAIGEN (Human-AI Collaboration for GENeration), an efficient fashion design system for Human-AI collaboration developed to aid designers. Specifically, HAIGEN consists of four modules. T2IM, located in the cloud, generates reference inspiration images directly from text prompts. With three other modules situated locally, the I2SM batch generates the image material library into a certain designer-style sketch material library. The SRM recommends similar sketches in the generated library to designers for further refinement, and the STM colors the refined sketch according to the styles of inspiration images. Through our system, any designer can perform local personalized fine-tuning and leverage the powerful generation capabilities of large models in the cloud, streamlining the entire design development process. Given that our approach integrates both cloud and local model deployment schemes, it effectively safeguards design privacy by avoiding the need to upload personalized data from local designers. We validated the effectiveness of each module through extensive qualitative and quantitative experiments. User surveys also confirmed that HAIGEN offers significant advantages in design efficiency, positioning it as a new generation of aid-tool for designers.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678518"}, {"primary_key": "379585", "vector": [], "sparse_vector": [], "title": "WatchCap: Improving Scanning Efficiency in People with Low Vision through Compensatory Head Movement Stimulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Individuals with low vision (LV) frequently face challenges in scanning performance, which in turn complicates daily activities requiring visual recognition. Although those with PVL can theoretically compensate for these scanning deficiencies through the use of active head movements, few practical applications have sought to capitalize on this potential, especially during visual recognition tasks. In this paper, we present WatchCap, a novel device that leverages the hanger reflex phenomenon to naturally elicit head movements through stimulation feedback. Our user studies, conducted with both sighted individuals in a simulated environment and people with glaucoma-related PVL, demonstrated that WatchCap's scanning-contingent stimulation enhances visual exploration. This improvement is evidenced by the fixation and saccade-related features and positive feedback from participants, which did not cause discomfort to the users. This study highlights the promise of facilitating head movements to aid those with LVs in visual recognition tasks. Critically, since WatchCap functions independently of predefined or task-specific cues, it has a wide scope of applicability, even in ambient task situations. This independence positions WatchCap to complement existing tools aimed at detailed visual information acquisition, allowing integration with existing tools and facilitating a comprehensive approach to assisting individuals with LV.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659592"}, {"primary_key": "379586", "vector": [], "sparse_vector": [], "title": "Playlogue: Dataset and Benchmarks for Analyzing Adult-Child Conversations During Play.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "There has been growing interest in developing ubiquitous technologies to analyze adult-child speech in naturalistic settings such as free play in order to support children's social and academic development, language acquisition, and parent-child interactions. However, these technologies often rely on off-the-shelf speech processing tools that have not been evaluated on child speech or child-directed adult speech, whose unique characteristics might result in significant performance gaps when using models trained on adult speech. This work introduces the Playlogue dataset containing over 33 hours of long-form, naturalistic, play-based adult-child conversations from three different corpora of preschool-aged children. Playlogue enables researchers to train and evaluate speaker diarization and automatic speech recognition models on child-centered speech. We demonstrate the lack of generalizability of existing state-of-the-art models when evaluated on Playlogue, and show how fine-tuning models on adult-child speech mitigates the performance gap to some extent but still leaves considerable room for improvement. We further annotate over 5 hours of the Playlogue dataset with 8668 validated adult and child speech act labels, which can be used to train and evaluate models to provide clinically relevant feedback on parent-child interactions. We investigate the performance of state-of-the-art language models at automatically predicting these speech act labels, achieving significant accuracy with simple chain-of-thought prompting or minimal fine-tuning. We use inhome pilot data to validate the generalizability of models trained on Playlogue, demonstrating its utility in improving speech and language technologies for child-centered conversations. The Playlogue dataset is available for download at https://huggingface.co/datasets/playlogue/playlogue-v1.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699775"}, {"primary_key": "379587", "vector": [], "sparse_vector": [], "title": "SleepNet: Attention-Enhanced Robust Sleep Prediction using Dynamic Social Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Akane Sano"], "summary": "Sleep behavior significantly impacts health and acts as an indicator of physical and mental well-being. Monitoring and predicting sleep behavior with ubiquitous sensors may therefore assist in both sleep management and tracking of related health conditions. While sleep behavior depends on, and is reflected in the physiology of a person, it is also impacted by external factors such as digital media usage, social network contagion, and the surrounding weather. In this work, we propose SleepNet, a system that exploits social contagion in sleep behavior through graph networks and integrates it with physiological and phone data extracted from ubiquitous mobile and wearable devices for predicting next-day sleep labels about sleep duration. Our architecture overcomes the limitations of large-scale graphs containing connections irrelevant to sleep behavior by devising an attention mechanism. The extensive experimental evaluation highlights the improvement provided by incorporating social networks in the model. Additionally, we conduct robustness analysis to demonstrate the system's performance in real-life conditions. The outcomes affirm the stability of SleepNet against perturbations in input data. Further analyses emphasize the significance of network topology in prediction performance revealing that users with higher eigenvalue centrality are more vulnerable to data perturbations.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643508"}, {"primary_key": "379589", "vector": [], "sparse_vector": [], "title": "exHAR: An Interface for Helping Non-Experts Develop and Debug Knowledge-based Human Activity Recognition Systems.", "authors": ["<PERSON>", "<PERSON>", "Khai N. <PERSON>"], "summary": "Human activity recognition (HAR) is crucial for ubiquitous computing systems. While HAR systems are able to recognize a predefined set of activities established during the development process, they often fail to handle users' unique ways of completing these activities and changes in their behavior over time, as well as different activities. Knowledge-based HAR models have been proposed to help individuals create new activity definitions based on common-sense rules, but little research has been done to understand how users approach this task. To investigate this process, we developed and studied how people interact with an explainable knowledge-based HAR development tool called exHAR. Our tool empowers users to define their activities as a set of factual propositions. Users can debug these definitions by soliciting explanations for model predictions (why and why-not) and candidate corrections for faulty predictions (what-if and how-to). After conducting a study to evaluate the effectiveness of exHAR in helping users design accurate HAR systems, we conducted a think-aloud study to better understand people's approach to debugging and personalizing HAR systems and the challenges they may encounter. Our findings revealed why some participants had inaccurate mental models of knowledge-based HAR systems and inefficient approaches to the debugging process.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643500"}, {"primary_key": "379590", "vector": [], "sparse_vector": [], "title": "TimelyTale: A Multimodal Dataset Approach to Assessing Passengers&apos; Explanation Demands in Highly Automated Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Explanations in automated vehicles enhance passengers' understanding of vehicle decision-making, mitigating negative experiences by increasing their sense of control. These explanations help maintain situation awareness, even when passengers are not actively driving, and calibrate trust to match vehicle capabilities, enabling safe engagement in non-driving related tasks. While design studies emphasize timing as a crucial factor affecting trust, machine learning practices for explanation generation primarily focus on content rather than delivery timing. This discrepancy could lead to mistimed explanations, causing misunderstandings or unnecessary interruptions. This gap is partly due to alack of datasets capturing passengers' real-world demands and experiences with in-vehicle explanations. We introduce TimelyTale, an approach that records passengers' demands for explanations in automated vehicles. The dataset includes environmental, driving-related, and passenger-specific sensor data for context-aware explanations. Our machine learning analysis identifies proprioceptive and physiological data as key features for predicting passengers' explanation demands, suggesting their potential for generating timely, context-aware explanations. The TimelyTale dataset is available at https://doi.org/10.7910/DVN/CQ8UB0.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678544"}, {"primary_key": "379592", "vector": [], "sparse_vector": [], "title": "Sasha: Creative Goal-Oriented Reasoning in Smart Homes with Large Language Models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Smart home assistants function best when user commands are direct and well-specified---e.g., \"turn on the kitchen light\"---or when a hard-coded routine specifies the response. In more natural communication, however, human speech is unconstrained, often describing goals (e.g., \"make it cozy in here\" or \"help me save energy\") rather than indicating specific target devices and actions to take on those devices. Current systems fail to understand these under-specified commands since they cannot reason about devices and settings as they relate to human situations. We introduce large language models (LLMs) to this problem space, exploring their use for controlling devices and creating automation routines in response to under-specified user commands in smart homes. We empirically study the baseline quality and failure modes of LLM-created action plans with a survey of age-diverse users. We find that LLMs can reason creatively to achieve challenging goals, but they experience patterns of failure that diminish their usefulness. We address these gaps with <PERSON>, a smarter smart home assistant. <PERSON> responds to loosely-constrained commands like \"make it cozy\" or \"help me sleep better\" by executing plans to achieve user goals---e.g., setting a mood with available devices, or devising automation routines. We implement and evaluate <PERSON> in a hands-on user study, showing the capabilities and limitations of LLM-driven smart homes when faced with unconstrained user-generated scenarios.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643505"}, {"primary_key": "379595", "vector": [], "sparse_vector": [], "title": "User-directed Assembly Code Transformations Enabling Efficient Batteryless Arduino Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The time for battery-free computing is now. Lithium mining depletes and pollutes local water supplies and dead batteries in landfills leak toxic metals into the ground[20][12]. Battery-free devices represent a probable future for sustainable ubiquitous computing and we will need many more new devices and programmers to bring that future into reality. Yet, energy harvesting and battery-free devices that frequently fail are challenging to program. The maker movement has organically developed a considerable variety of platforms to prototype and program ubiquitous sensing and computing devices, but only a few have been modified to be usable with energy harvesting and to hide those pesky power failures that are the norm from variable energy availability (platforms like Microsoft's Makecode and AdaFruit's CircuitPython). Many platforms, especially Arduino (the first and most famous maker platform), do not support energy harvesting devices and intermittent computing. To bridge this gap and lay a strong foundation for potential new platforms for maker programming, we build a tool called BOOTHAMMER: a lightweight assembly re-writer for ARM Thumb. <PERSON><PERSON>OTHAMMER analyzes and rewrites the low-level assembly to insert careful checkpoint and restore operations to enable programs to persist through power failures. The approach is easily insertable in existing toolchains and is general-purpose enough to be resilient to future platforms and devices/chipsets. We close the loop with the user by designing a small set of program annotations that any maker coder can use to provide extra information to this low-level tool that will significantly increase checkpoint efficiency and resolution. These optional extensions represent a way to include the user in decision-making about energy harvesting while ensuring the tool supports existing platforms. We conduct an extensive evaluation using various program benchmarks with Arduino as our chosen evaluation platform. We also demonstrate the usability of this approach by evaluating BOOTHAMMER with a user study and show that makers feel very confident in their ability to write intermittent computing programs using this tool. With this new tool, we enable maker hardware and software for sustainable, energy-harvesting-based computing for all.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659590"}, {"primary_key": "379596", "vector": [], "sparse_vector": [], "title": "ECSkin: Tessellating Electrochromic Films for Reconfigurable On-skin Displays.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Emerging electrochromic (EC) materials have advanced the frontier of thin-film, low-power, and non-emissive display technologies. While suitable for wearable or textile-based applications, current EC display systems are manufactured in fixed, pre-designed patterns that hinder the potential of reconfigurable display technologies desired by on-skin interactions. To realize the customizable and scalable EC display for skin wear, this paper introduces ECSkin, a construction toolkit composed of modular EC films. Our approach enables reconfigurable designs that display customized patterns by arranging combinations of premade EC modules. An ECSkin device can pixelate patterns and expand the display area through tessellating congruent modules. We present the fabrication of flexible EC display modules with accessible materials and tools. We performed technical evaluations to characterize the electrochromic performance and conducted user evaluations to verify the toolkit's usability and feasibility. Two example applications demonstrate the adaptiveness of the modular display on different body locations and user scenarios.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659613"}, {"primary_key": "379597", "vector": [], "sparse_vector": [], "title": "Collecting Self-reported Physical Activity and Posture Data Using Audio-based Ecological Momentary Assessment.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "μEMA is a data collection method that prompts research participants with quick, answer-at-a-glance, single-multiple-choice self-report behavioral questions, thus enabling high-temporal-density self-report of up to four times per hour when implemented on a smartwatch. However, due to the small watch screen, μEMA is better used to select among 2 to 5 multiple-choice answers versus allowing the collection of open-ended responses. We introduce an alternative and novel form of micro-interaction self-report using speech input - audio-μEMA- where a short beep or vibration cues participants to verbally report their behavioral states, allowing for open-ended, temporally dense self-reports. We conducted a one-hour usability study followed by a within-subject, 6-day to 21-day free-living feasibility study in which participants self-reported their physical activities and postures once every 2 to 5 minutes. We qualitatively explored the usability of the system and identified factors impacting the response rates of this data collection method. Despite being interrupted 12 to 20 times per hour, participants in the free-living study were highly engaged with the system, with an average response rate of 67.7% for audio-μEMA for up to 14 days. We discuss the factors that impacted feasibility; some implementation, methodological, and participant challenges we observed; and important considerations relevant to deploying audio-μEMA in real-time activity recognition systems.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678584"}, {"primary_key": "379598", "vector": [], "sparse_vector": [], "title": "GoalTrack: Supporting Personalized Goal-Setting in Stroke Rehabilitation with Multimodal Activity Journaling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Stroke is the leading cause of disability among adults, with motor impairments being the most significant complication. Stroke rehabilitation is critical for stroke survivors to regain independence in their daily activities. Central to this rehabilitation process is patient-centered goal-setting, a crucial philosophy underpinning personalized programs. However, mismatched expectations between stroke survivors and clinicians often lead to limited engagement from patients, which detracts from patient-centeredness. We envision that stroke survivors who engage in journaling activities can empower themselves to be more proactive, thereby enhancing the goal-setting process. To this end, we iteratively designed and developed GoalTrack, an activity journaling app utilizing voice and touch to support stroke survivors in articulating their rehabilitation goals. Using GoalTrack as a probe, we conducted an in-lab user study with thirteen stroke survivor participants. We present findings on how stroke survivors utilize multimodal input for different data formats, and their perspectives on its accessibility. We also report on how stroke survivors envision activity journaling in the goal-setting process, where our findings suggest that journaling supports articulating personalized rehabilitation goals and fosters enhanced engagement with therapists. We also discuss future avenues of research for designing multimodal interfaces for stroke survivors, and share lessons learned from conducting in-lab studies.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699723"}, {"primary_key": "379600", "vector": [], "sparse_vector": [], "title": "Extending EV Battery Lifetime: Digital Phenotyping Approach for Departure Time Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Battery degradation, a gradual loss of usable capacity over time, is one of the major hurdles for widespread adoption of electric vehicles (EVs). We introduce delayed full-charging (DFC) algorithm to mitigate degradation and extend the lifetime of EV batteries in battery management systems (BMS). When the EV is plugged in, the DFC algorithm charges batteries up to approximately 80% state of charge (SOC) and delays full charging until the predicted unplug time (tunplug). This approach significantly reduces the time batteries remain fully charged (t100%), thereby mitigating degradation while ensuring charging time for EV users to utilize the full battery capacity. For predicting tunplug, we propose a novel methodology that uses digital phenotyping to predict departure times. This method leverages smartphone data to capture irregular but predictable departure patterns by reflecting relevant behavioral and environmental contexts. A case study with 48 participants was conducted to empirically evaluate the departure time prediction performance using tree-based ensemble models trained on smartphone data, compared to a baseline Long Short-Term Memory (LSTM) model trained on historical data. Results reveal that models utilizing mobile passive features achieved a Mean Absolute Error (MAE) as low as 2.18 hours on weekdays and 4.46 hours on weekends, demonstrating superior effectiveness in capturing irregular patterns compared to the baseline model trained only on historical temporal features.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699725"}, {"primary_key": "379601", "vector": [], "sparse_vector": [], "title": "IMUGPT 2.0: Language-Based Cross Modality Transfer for Sensor-Based Human Activity Recognition.", "authors": ["<PERSON>ikang <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "One of the primary challenges in the field of human activity recognition (HAR) is the lack of large labeled datasets. This hinders the development of robust and generalizable models. Recently, cross modality transfer approaches have been explored that can alleviate the problem of data scarcity. These approaches convert existing datasets from a source modality, such as video, to a target modality, such as inertial measurement units (IMUs). With the emergence of generative AI models such as large language models (LLMs) and text-driven motion synthesis models, language has become a promising source data modality as well - as shown in proof of concepts such as IMUGPT. In this work, we conduct a large-scale evaluation of language-based cross modality transfer to determine their effectiveness for HAR. Based on this study, we introduce two new extensions for IMUGPT that enhance its use for practical HAR application scenarios: a motion filter capable of filtering out irrelevant motion sequences to ensure the relevance of the generated virtual IMU data, and a set of metrics that measure the diversity of the generated data facilitating the determination of when to stop generating virtual IMU data for both effective and efficient processing. We demonstrate that our diversity metrics can reduce the effort needed for the generation of virtual IMU data by at least 50%, which opens up IMUGPT for practical use cases beyond a mere proof of concept.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678545"}, {"primary_key": "379603", "vector": [], "sparse_vector": [], "title": "SonicID: User Identification on Smart Glasses with Acoustic Sensing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smart glasses have become more prevalent as they provide an increasing number of applications for users. They store various types of private information or can access it via connections established with other devices. Therefore, there is a growing need for user identification on smart glasses. In this paper, we introduce a low-power and minimally-obtrusive system called SonicID, designed to authenticate users on glasses. SonicID extracts unique biometric information from users by scanning their faces with ultrasonic waves and utilizes this information to distinguish between different users, powered by a customized binary classifier with the ResNet-18 architecture. SonicID can authenticate users by scanning their face for 0.06 seconds. A user study involving 40 participants confirms that SonicID achieves a true positive rate of 97.4%, a false positive rate of 4.3%, and a balanced accuracy of 96.6% using just 1 minute of training data collected for each new user. This performance is relatively consistent across different remounting sessions and days. Given this promising performance, we further discuss the potential applications of SonicID and methods to improve its performance in the future.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699734"}, {"primary_key": "379604", "vector": [], "sparse_vector": [], "title": "Matcha: An IDE Plugin for Creating Accurate Privacy Nutrition Labels.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Apple and Google introduced their versions of privacy nutrition labels to the mobile app stores to better inform users of the apps' data practices. However, these labels are self-reported by developers and have been found to contain many inaccuracies due to misunderstandings of the label taxonomy. In this work, we present Matcha, an IDE plugin that uses automated code analysis to help developers create accurate Google Play data safety labels. Developers can benefit from <PERSON>a's ability to detect user data accesses and transmissions while staying in control of the generated label by adding custom Java annotations and modifying an auto-generated XML specification. Our evaluation with 12 developers showed that Matcha helped our participants improved the accuracy of a label they created with Google's official tool for a real-world app they developed. We found that participants preferred Matcha for its accuracy benefits. Drawing on <PERSON><PERSON>, we discuss general design recommendations for developer tools used to create accurate standardized privacy notices.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643544"}, {"primary_key": "379605", "vector": [], "sparse_vector": [], "title": "SleepNetZero: Zero-Burden Zero-Shot Reliable Sleep Staging with Neural Networks Based on Ballistocardiograms.", "authors": ["Shuzhen Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sleep monitoring plays a crucial role in maintaining good health, with sleep staging serving as an essential metric in the monitoring process. Traditional methods, utilizing medical sensors like EEG and ECG, can be effective but often present challenges such as unnatural user experience, complex deployment, and high costs. Ballistocardiography (BCG), a type of piezoelectric sensor signal, offers a non-invasive, user-friendly, and easily deployable alternative for long-term home monitoring. However, reliable BCG-based sleep staging is challenging due to the limited sleep monitoring data available for BCG. A restricted training dataset prevents the model from generalization across populations. Additionally, transferring to BCG faces difficulty ensuring model robustness when migrating from other data sources. To address these issues, we introduce SleepNetZero, a zero-shot learning based approach for sleep staging. To tackle the generalization challenge, we propose a series of BCG feature extraction methods that align BCG components with corresponding respiratory, cardiac, and movement channels in PSG. This allows models to be trained on large-scale PSG datasets that are diverse in population. For the migration challenge, we employ data augmentation techniques, significantly enhancing generalizability. We conducted extensive training and testing on large datasets (12393 records from 9637 different subjects), achieving an accuracy of 0.803 and a Cohen's Kappa of 0.718. ZeroSleepNet was also deployed in real prototype (monitoring pads) and tested in actual hospital settings (265 users), demonstrating an accuracy of 0.697 and a Cohen's Kappa of 0.589. To the best of our knowledge, this work represents the first known reliable BCG-based sleep staging effort and marks a significant step towards in-home health monitoring.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699743"}, {"primary_key": "379606", "vector": [], "sparse_vector": [], "title": "WiFi-CSI Difference Paradigm: Achieving Efficient Doppler Speed Estimation for Passive Tracking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Passive tracking plays a fundamental role in numerous applications such as elderly care, security surveillance, and smart home. To utilize ubiquitous WiFi signals for passive tracking, the Doppler speed extracted from WiFi CSI (Channel State Information) is the key information. Despite the progress made, existing approaches still require a large number of samples to achieve accurate Doppler speed estimation. To enable WiFi sensing with minimum amount of interference on WiFi communication, accurate Doppler speed estimation with fewer CSI samples is crucial. To achieve this, we build a passive WiFi tracking system which employs a novel CSI difference paradigm instead of CSI for Doppler speed estimation. In this paper, we provide the first deep dive into the potential of CSI difference for fine-grained Doppler speed estimation. Theoretically, our new design allows us to estimate Doppler speed with just three samples. While conventional methods only adopt phase information for Doppler estimation, we creatively fuse both phase and amplitude information to improve Doppler estimation accuracy. Extensive experiments show that our solution outperforms the state-of-the-art approaches, achieving higher accuracy with fewer CSI samples. Based on this proposed WiFi-CSI difference paradigm, we build a prototype passive tracking system which can accurately track a person with a median error lower than 34 cm, achieving similar accuracy compared to the state-of-the-art systems, while significantly reducing the required number of samples to only 5%.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659608"}, {"primary_key": "379607", "vector": [], "sparse_vector": [], "title": "Hypnos: A Contactless Sleep Stage Monitoring System Using UWB Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As a fundamental physiological process, sleep plays a vital role in human health. High-quality sleep requires a reasonable distribution of sleep duration over different sleep stages. Recently, contactless solutions have been used for in-home sleep stage monitoring via wireless signals as it enables monitoring daily sleep in a non-intrusive manner. However, various factors, such as the subject's physiological characteristics during sleep, the subject's health status, and even the sleep environment, pose challenges to wireless signal analysis. In this paper, we propose Hypnos, a contactless sleep monitoring system that identifies different sleep stages using an ultra-wideband (UWB) device. Hypnos enables automated bed localization and extracts signals containing coarse-grained body movements and fine-grained chest movements due to breathing and heartbeat from the subject, which acts as the preparation step for sleep staging. The key to our system is a seq2seq deep learning model, which adopts an attention-based sequence encoder to learn the patterns and transitions within and between sleep epochs and combines with contrastive learning to improve the generalizability of the encoder. Particularly, we incorporate sleep apnea detection as an auxiliary task into the model to reduce the interference of sleep apnea with sleep staging. Moreover, we design a two-step training for better adaptation of subjects with different severities of sleep disorders. We conduct extensive experiments on 100 subjects, including healthy individuals and patients with sleep disorders, and the experimental results show that <PERSON>ypnos achieves excellent performance in multi-stage sleep classification (including 5-stage sleep classification), and outperforms other baseline methods.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678581"}, {"primary_key": "379609", "vector": [], "sparse_vector": [], "title": "Ask Less, Learn More: Adapting Ecological Momentary Assessment Survey Length by Modeling Question-Answer Information Gain.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ecological momentary assessment (EMA) is an approach to collect self-reported data repeatedly on mobile devices in natural settings. EMAs allow for temporally dense, ecologically valid data collection, but frequent interruptions with lengthy surveys on mobile devices can burden users, impacting compliance and data quality. We propose a method that reduces the length of each EMA question set measuring interrelated constructs, with only modest information loss. By estimating the potential information gain of each EMA question using question-answer prediction models, this method can prioritize the presentation of the most informative question in a question-by-question sequence and skip uninformative questions. We evaluated the proposed method by simulating question omission using four real-world datasets from three different EMA studies. When compared against the random question omission approach that skips 50% of the questions, our method reduces imputation errors by 15%-52%. In surveys with five answer options for each question, our method can reduce the mean survey length by 34%-56% with a real-time prediction accuracy of 72%-95% for the skipped questions. The proposed method may either allow more constructs to be surveyed without adding user burden or reduce response burden for more sustainable longitudinal EMA data collection.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699735"}, {"primary_key": "379610", "vector": [], "sparse_vector": [], "title": "SpaceBeat: Identity-aware Multi-person Vital Signs Monitoring Using Commodity WiFi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Vital signs monitoring has gained increasing attention due to its ability to indicate various human health and well-being conditions. The development of WiFi sensing technologies has made it possible to monitor vital signs using ubiquitous WiFi signals and devices. However, most existing approaches are dedicated to single-person scenarios. A few WiFi sensing approaches can achieve multi-person vital signs monitoring, whereas they are not identity-aware and sensitive to interferences in the environment. In this paper, we propose SpaceBeat, an identity-aware and interference-robust multi-person vital sign monitoring system using commodity WiFi. In particular, our system separates multiple people and locates each person in the spatial domain by leveraging multiple antennas. We analyze the change of signals at the location of each person to achieve identity-aware vital signs monitoring. We also design a contrastive principal component analysis-contrastive learning framework to mitigate interferences caused by other moving people. We evaluate SpaceBeat in various challenging environments, including interference scenarios, non-line-of-sight scenarios, different distances, etc. Our system achieves an average accuracy of 99.1% for breathing monitoring and 97.9% for heartbeat monitoring.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678590"}, {"primary_key": "379611", "vector": [], "sparse_vector": [], "title": "Governing Open Vocabulary Data Leaks Using an Edge LLM through Programming by Example.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A major concern with integrating large language model (LLM) services (e.g., ChatGPT) into workplaces is that employees may inadvertently leak sensitive information through their prompts. Since user prompts can involve arbitrary vocabularies, conventional data leak mitigation solutions, such as string-matching-based filtering, often fall short. We present GPTWall, a privacy firewall that helps internal admins create and manage policies to mitigate data leaks in prompts sent to external LLM services. GPTWall's key innovations are (1) introducing a lightweight LLM running on the edge to obfuscate target information in prompts and restore the information after receiving responses, and (2) helping admins author fine-grained disclosure policies through programming by example. We evaluated GPTWall with 12 participants and found that they could create an average of 17.7 policies within 30 minutes, achieving an increase of 29% in precision and 22% in recall over the state-of-the-art data de-identification tool.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699760"}, {"primary_key": "379612", "vector": [], "sparse_vector": [], "title": "Facial Landmark Detection Based on High Precision Spatial Sampling via Millimeter-wave Radar.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Qiancheng Jin", "<PERSON> Fan", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Facial landmark has become one of the most widely-used and intuitive feature of the face. Traditional methods for Facial Landmark Detection (FLD) are primarily based on cameras, which are limited by their sensitivity to light conditions, inability to penetrate obstructions, and potential privacy leaks. In this paper, we propose mmFLD to estimate the facial landmark positions using millimeter-wave (mmWave) radar with mm-level accuracy. By simultaneously applying the range estimation capability and angle estimation capability of mmWave radar, we are able to spatially sample face reflection signals with high resolution. In particular, we propose a velocity-based method for head detection and tracking, and then we build two generalized models to extract effective facial motion features from different facial regions. Moreover, we design an end-to-end neural network to extract the face structure and the motion coherence implicit in mmWave data. Experiment results show that mmFLD can estimate the facial landmark positions with high accuracy, e.g., the average Mean Absolute Error (MAE) is 2.81 mm with eight kinds of different facial expressions, and extended experiment also demonstrates the generalizability and robustness of mmFLD for different experiment conditions.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699739"}, {"primary_key": "379613", "vector": [], "sparse_vector": [], "title": "SwivelTouch: Boosting Touchscreen Input with 3D Finger Rotation Gesture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Jianjiang Feng", "<PERSON><PERSON>"], "summary": "Today, touchscreens stand as the most prevalent input devices of mobile computing devices (smartphones, tablets, smartwatches). Yet, compared with desktop or laptop computers, the limited shortcut keys and physical buttons on touchscreen devices, coupled with the fat finger problem, often lead to slower and more error-prone input and navigation, especially when dealing with text editing and other complex interaction tasks. We introduce an innovative gesture set based on finger rotations in the yaw, pitch, and roll directions on a touchscreen, diverging significantly from traditional two-dimensional interactions and promising to expand the gesture library. Despite active research in estimation of finger angles, however, the previous work faces substantial challenges, including significant estimation errors and unstable sequential outputs. Variability in user behavior further complicates the isolation of movements to a single rotational axis, leading to accidental disturbances and screen coordinate shifts that interfere with the existing sliding gestures. Consequently, the direct application of finger angle estimation algorithms for recognizing three-dimensional rotational gestures is impractical. SwivelTouch leverages the analysis of finger movement characteristics on the touchscreen captured through original capacitive image sequences, which aims to rapidly and accurately identify these advanced 3D gestures, clearly differentiating them from conventional touch interactions like tapping and sliding, thus enhancing user interaction with touch devices and meanwhile compatible with existing 2D gestures. User study further confirms that the implementation of SwivelTouch significantly enhances the efficiency of text editing on smartphones.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659584"}, {"primary_key": "379614", "vector": [], "sparse_vector": [], "title": "TeleAware Robot: Designing Awareness-augmented Telepresence Robot for Remote Collaborative Locomotion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Shanning Zhuang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Telepresence robots can be used to support users to navigate an environment remotely and share the visiting experience with their social partners. Although such systems allow users to see and hear the remote environment and communicate with their partners via live video feed, this does not provide enough awareness of the environment and their remote partner's activities. In this paper, we introduce an awareness framework for collaborative locomotion in scenarios of onsite and remote users visiting a place together. From an observational study of small groups of people visiting exhibitions, we derived four design goals for enhancing the environmental and social awareness between social partners, and developed a set of awareness-enhancing techniques to add to a standard telepresence robot - named TeleAware robot. Through a controlled experiment simulating a guided exhibition visiting task, TeleAware robot showed the ability to lower the workload, facilitate closer social proximity, and improve mutual awareness and social presence compared with the standard one. We discuss the impact of mobility and roles of local and remote users, and provide insights for the future design of awareness-enhancing telepresence robot systems that facilitate collaborative locomotion.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659622"}, {"primary_key": "379616", "vector": [], "sparse_vector": [], "title": "Users&apos; Perspectives on Multimodal Menstrual Tracking Using Consumer Health Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Khai N. <PERSON>", "<PERSON>"], "summary": "Previous menstrual health literature highlights a variety of signals not included in existing menstrual trackers because they are either difficult to gather or are not typically associated with menstrual health. Since it has become increasingly convenient to collect biomarkers through wearables and other consumer-grade devices, our work examines how people incorporate unconventional signals (e.g., blood glucose levels, heart rate) into their understanding of menstrual health. In this paper, we describe a three-month-long study on fifty participants' experiences as they tracked their health using physiological sensors and daily diaries. We analyzed their experiences with both conventional and unconventional menstrual health signals through surveys and interviews conducted throughout the study. We delve into the various aspects of menstrual health that participants sought to affirm using unconventional signals, explore how these signals influenced their daily behaviors, and examine how multimodal menstrual tracking expanded their scope of menstrual health. Finally, we provide design recommendations for future multimodal menstrual trackers.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678575"}, {"primary_key": "379620", "vector": [], "sparse_vector": [], "title": "TagSleep3D: RF-based 3D Sleep Posture Skeleton Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "Sleep posture plays a crucial role in maintaining good morpheus quality and overall health. As a result, long-term monitoring of 3D sleep postures is significant for sleep analysis and chronic disease prevention. To recognize sleep postures, traditional methods either use cameras to record image data or require the user to wear wearable devices or sleep on pressure mattresses. However, these methods could raise privacy concerns and cause discomfort during sleep. Accordingly, the RF (Radio Frequency) based method has emerged as a promising alternative. Despite most of these methods achieving high precision in classifying sleep postures, they struggle to retrieve 3D sleep postures due to difficulties in capturing 3D positions of static body joints. In this work, we propose TagSleep3D to resolve all the above issues. Specifically, inspired by the concept of RFID tag sheets, we explore the possibility of recognizing 3D sleep posture by deploying an RFID tag array under the bedsheet. When a user sleeps in bed, the signals of some tags could be blocked or reflected by the sleep posture, which can produce a body imprint. We then propose a novel deep learning model composed of the attention mechanism, convolutional neural network, and together with two data augmentation methods to retrieve the 3D sleep postures by analyzing these body imprints. We evaluate TagSleep3D with 43 users and we totally collect 27,300 sleep posture samples. Our extensive experiments demonstrate that TagSleep3D can recognize each joint on the human skeleton with a median MPJPE (Mean Per Joint Position Error) of 4.76 cm for seen users and 7.58 cm for unseen users.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643512"}, {"primary_key": "379621", "vector": [], "sparse_vector": [], "title": "View-agnostic Human Exercise Cataloging with Single MmWave Radar.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Advances in mmWave-based sensing have enabled a privacy-friendly approach to pose and gesture recognition. Yet, providing robustness with the sparsity of reflected signals has been a long-standing challenge towards its practical deployment, constraining subjects to often face the radar. We present RF-HAC- a first-of-its-kind system that brings robust, automated and real-time human activity cataloging to practice by not only classifying exercises performed by subjects in their natural environments and poses, but also tracking the corresponding number of exercise repetitions. RF-HAC's unique approach (i) brings the diversity of multiple radars to scalably train a novel, self-supervised, pose-agnostic transformer-based exercise classifier directly on 3D RF point clouds with minimal manual effort and be deployed on a single radar; and (ii) leverages the underlying doppler behavior of exercises to design a robust self-similarity based segmentation algorithm for counting the repetitions in unstructured RF point clouds. Evaluations on a comprehensive set of challenging exercises in both seen and unseen environments/subjects highlight RF-HAC's robustness with high accuracy (over 90%) and readiness for real-time, practical deployments over prior art.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678512"}, {"primary_key": "379622", "vector": [], "sparse_vector": [], "title": "SmartDampener: An Open Source Platform for Sport Analytics in Tennis.", "authors": ["<PERSON><PERSON>", "Taiting Lu", "Shengming Yuan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we introduce SmartDampener, an open-source tennis analytics platform that redefines the traditional understanding of vibration dampeners. Traditional vibration dampeners favored by both amateur and professional tennis players are utilized primarily to diminish vibration transmission and enhance racket stability. However, our platform uniquely merges wireless sensing technologies into a device that resembles a conventional vibration dampener, thereby offering a range of tennis performance metrics including ball speed, impact location, and stroke type. The design of SmartDampener adheres to the familiar form of this accessory, ensuring that (i) it is readily accepted by users and robust under real-play conditions such as ball-hitting, (ii) it has minimal impact on player performance, (iii) it is capable of providing a wide range of analytical insights, and (iv) it is extensible to other sports. Existing computer vision systems for tennis sensing such as Hawk-eye and PlaySight, rely on hardware that costs millions of US dollars to deploy with complex setup procedures and is susceptible to lighting environment. Wearable devices and other tennis sensing accessories, such as Zepp Tennis sensor and TennisEye, using intrusive mounting locations, hinder user experience and impede player performance. In contrast, SmartDampener, a low-cost and compact tennis sensing device, notable for its socially accepted, lightweight and scalable design, seamlessly melds into the form of a vibration dampener. SmartDampener exploits opportunities in SoC and form factor design of conventional dampeners to integrate the sensing units and micro-controllers on a two-layer flexible PCB, that is bent and enclosed inside a dampener case made of 3D printing TPU material, while maintaining the vibration dampening feature and further being enhanced by its extended battery life and the inclusion of wireless communication features. The overall cost is $9.42, with a dimension of 21.4 mm × 27.5 mm × 9.7 mm (W × L × H) and a weight of 6.1 g and 5.8 hours of battery life. In proof of SmartDampener's performance in tennis analytics, we present various tennis analytic applications that exploit the capability of SmartDampener in capturing the correlations across string vibration, and racket motion, including the estimation of ball speed with a median error of 3.59 mph, estimation of ball impact location with accuracy of 3.03 cm, and classification of six tennis strokes with accuracy of 96.75%. Finally, extensive usability studies with 15 tennis players indicate high levels of social acceptance of form factor design for the SmartDampener dampener in comparison with alternative form factors, as well as its capability of sensing and analyzing tennis stroke in an accurate and robust manner. We believe this platform will enable exciting applications in other sports like badminton, fitness tracking, and injury prevention.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678507"}, {"primary_key": "379623", "vector": [], "sparse_vector": [], "title": "CalibRead: Unobtrusive Eye Tracking Calibration from Natural Reading Behavior.", "authors": ["Change Liu", "<PERSON>", "<PERSON><PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present a novel, unobtrusive calibration method that leverages the association between eye-movement and text to calibrate eye-tracking devices during natural reading. The calibration process involves an iterative sequence of 3 steps: (1) matching the points of eye-tracking data with the text grids and boundary grids, (2) computing the weight for each point pair, and (3) optimizing the calibration parameters that best align point pairs through gradient descent. During this process, we assume that, from a holistic perspective, the gaze will cover the text area, effectively filling it after sufficient reading. Meanwhile, on a granular level, the gaze duration is influenced by the semantic and positional features of the text. Therefore, factors such as the presence of empty space, the positional features of tokens, and the depth of constituency parsing play important roles in calibration. Our method achieves accuracy error comparable to traditional 7-point mehtod after naturally reading 3 texts, which takes about 51.75 seconds. Moreover, we analyse the impact of different holistic and granular features on the calibration results.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699737"}, {"primary_key": "379625", "vector": [], "sparse_vector": [], "title": "Self-supervised Learning for Accelerometer-based Human Activity Recognition: A Survey.", "authors": ["Alekse<PERSON>"], "summary": "Self-supervised learning (SSL) has emerged as a promising alternative to purely supervised learning, since it can learn from labeled and unlabeled data using a pre-train-then-fine-tune strategy, achieving state-of-the-art performances across many research areas. The field of accelerometer-based human activity recognition (HAR) can benefit from SSL since unlabeled data can be collected cost-efficiently due to the ubiquitous nature of sensors embedded in smart devices, which is in contrast to labeled data, that require a costly annotation process. Motivated by the success of SSL and the lack of surveys on SSL for HAR, this survey comprehensively examines 52 SSL methods applied to HAR, and categorizes them into four SSL paradigms based on pre-training objectives. We discuss SSL strategies, evaluation protocols, and utilized datasets. We highlight limitations in current methodologies, including little large-scale pre-training, the absence of foundation models, as well as the scarcity of systematic domain shift experiments and domain knowledge utilization. Notably, the diversity in evaluation protocols across papers poses a considerable challenge when comparing methods. Future directions outlined in this survey include the development of an SSL framework for HAR to enable standardized benchmarking and large-scale pre-training, along with integrating domain knowledge to enhance model performance.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699767"}, {"primary_key": "379627", "vector": [], "sparse_vector": [], "title": "SynthCAT: Synthesizing Cellular Association Traces with Fusion of Model-Based and Data-Driven Approaches.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The scarcity of publicly available cellular association traces hinders user location-based research and various data-driven services, highlighting the importance of data synthesis in this field. In this paper, we investigate the cellular association trace synthesis (CATS) problem, aiming to generate diverse and realistic cellular association traces based on road segment-based trajectories and corresponding departure times. To substantiate our research, we first gather substantial data, including road segment-based trajectories, base station (BS) distribution, and ground truths of cellular association traces. We then perform systematic data analysis to reveal technical challenges such as disparity in geographic spaces, complex and dynamic BS handover, and poor performance of single-dimension approaches. To address these challenges, we propose SynthCAT, a novel scheme that fuses model-based and data-driven approaches. Specifically, SynthCAT includes: i) A model-based coarse-grained cellular association trace generation component, encompassing GPS reference generation, weighted historical average time generation, Bayesian decision, and time mapping modules. This component establishes a unified GPS space to map road and BS spaces, generates initial time information, synthesizes coarse-grained spatial cellular association traces by following explicit BS handover rules, and maps the corresponding arrival time for each trace point; ii) A fine-grained cellular association trace generation component, which combines model-based and data-driven approaches. This employs a two-stage Autoencoder Generative Adversarial Network (AEGAN) to refine cellular association traces based on the coarse-grained ones. Extensive field experiments validate the efficacy of SynthCAT in terms of trace similarity to ground truths and its efficiency in supporting practical downstream applications.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699730"}, {"primary_key": "379628", "vector": [], "sparse_vector": [], "title": "Push the Limit of Highly Accurate Ranging on Commercial UWB Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ranging plays a crucial role in many wireless sensing applications. Among the wireless techniques employed for ranging, Ultra-Wideband (UWB) has received much attention due to its excellent performance and widespread integration into consumer-level electronics. However, the ranging accuracy of the current UWB systems is limited to the centimeter level due to bandwidth limitation, hindering their use for applications that require a very high resolution. This paper proposes a novel system that achieves sub-millimeter-level ranging accuracy on commercial UWB devices for the first time. Our approach leverages the fine-grained phase information of commercial UWB devices. To eliminate the phase drift, we design a fine-grained phase recovery method by utilizing the bi-directional messages in UWB two-way ranging. We further present a dual-frequency switching method to resolve phase ambiguity. Building upon this, we design and implement the ranging system on commercial UWB modules. Extensive experiments demonstrate that our system achieves a median ranging error of just 0.77 mm, reducing the error by 96.54% compared to the state-of-the-art method. We also present three real-life applications to showcase the fine-grained sensing capabilities of our system, including i) smart speaker control, ii) free-style user handwriting, and iii) 3D tracking for virtual-reality (VR) controllers.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659602"}, {"primary_key": "379629", "vector": [], "sparse_vector": [], "title": "ActSonic: Recognizing Everyday Activities from Inaudible Acoustic Wave Around the Body.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present ActSonic, an intelligent, low-power active acoustic sensing system integrated into eyeglasses that can recognize 27 different everyday activities (e.g., eating, drinking, toothbrushing) from inaudible acoustic waves around the body. It requires only a pair of miniature speakers and microphones mounted on each hinge of the eyeglasses to emit ultrasonic waves, creating an acoustic aura around the body. The acoustic signals are reflected based on the position and motion of various body parts, captured by the microphones, and analyzed by a customized self-supervised deep learning framework to infer the performed activities on a remote device such as a mobile phone or cloud server. ActSonic was evaluated in user studies with 19 participants across 19 households to track its efficacy in everyday activity recognition. Without requiring any training data from new users (leave-one-participant-out evaluation), ActSonic detected 27 activities, achieving an average F1-score of 86.6% in fully unconstrained scenarios and 93.4% in prompted settings at participants' homes.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699752"}, {"primary_key": "379631", "vector": [], "sparse_vector": [], "title": "mmSpyVR: Exploiting mmWave Radar for Penetrating Obstacles to Uncover Privacy Vulnerability of Virtual Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Virtual reality (VR), while enhancing user experiences, introduces significant privacy risks. This paper reveals a novel vulnerability in VR systems that allows attackers to capture VR privacy through obstacles utilizing millimeter-wave (mmWave) signals without physical intrusion and virtual connection with the VR devices. We propose mmSpyVR, a novel attack on VR user's privacy via mmWave radar. The mmSpyVR framework encompasses two main parts: (i) A transfer learning-based feature extraction model to achieve VR feature extraction from mmWave signal. (ii) An attention-based VR privacy spying module to spy VR privacy information from the extracted feature. The mmSpyVR demonstrates the capability to extract critical VR privacy from the mmWave signals that have penetrated through obstacles. We evaluate mmSpyVR through IRB-approved user studies. Across 22 participants engaged in four experimental scenes utilizing VR devices from three different manufacturers, our system achieves an application recognition accuracy of 98.5% and keystroke recognition accuracy of 92.6%. This newly discovered vulnerability has implications across various domains, such as cybersecurity, privacy protection, and VR technology development. We also engage with VR manufacturer Meta to discuss and explore potential mitigation strategies. Data and code are publicly available for scrutiny and research.1", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699772"}, {"primary_key": "379633", "vector": [], "sparse_vector": [], "title": "Hey, What&apos;s Going On?: Conveying Traffic Information to People with Visual Impairments in Highly Automated Vehicles: Introducing OnBoard.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Highly Automated Vehicles offer a new level of independence to people who are blind or visually impaired. However, due to their limited vision, gaining knowledge of the surrounding traffic can be challenging. To address this issue, we conducted an interactive, participatory workshop (N=4) to develop an auditory interface and OnBoard- a tactile interface with expandable elements - to convey traffic information to visually impaired people. In a user study with N=14 participants, we explored usability, situation awareness, predictability, and engagement with OnBoard and the auditory interface. Our qualitative and quantitative results show that tactile cues, similar to auditory cues, are able to convey traffic information to users. In particular, there is a trend that participants with reduced visual acuity showed increased engagement with both interfaces. However, the diversity of visual impairments and individual information needs underscores the importance of a highly tailored multimodal approach as the ideal solution.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659618"}, {"primary_key": "379634", "vector": [], "sparse_vector": [], "title": "GOAT: A Generalized Cross-Dataset Activity Recognition Framework with Natural Language Supervision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Wearable human activity recognition faces challenges in cross-dataset generalization due to variations in device configurations and activity types across datasets. We present GOAT, a Generalized crOss-dataset Activity recogniTion framework that leverages learning with natural language supervision to address these challenges. GOAT utilizes textual attributes from activity labels and device on-body positions to enable multimodal pre-training, aligning wearable activity representations with corresponding textual representations. This approach enables GOAT to adapt to diverse device configurations and activity label spaces in downstream tasks. Our method incorporates a novel device position encoding technique, a Transformer-based activity encoder, and a cosine similarity loss function to enhance feature extraction and generalization capabilities. Extensive evaluations demonstrate GOAT's effectiveness across various scenarios, including comparisons with state-of-the-art baselines, component analysis, and zero-shot activity recognition. GOAT shows promise for advancing cross-dataset activity recognition, offering a flexible and scalable solution for diverse wearable sensing applications.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699736"}, {"primary_key": "379638", "vector": [], "sparse_vector": [], "title": "MindScape Study: Integrating LLM and Behavioral Sensing for Personalized AI-Driven Journaling Experiences.", "authors": ["Subigya Nepal", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Eunsol Soul Choi", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mental health concerns are prevalent among college students, highlighting the need for effective interventions that promote self-awareness and holistic well-being. MindScape explores a novel approach to AI-powered journaling by integrating passively collected behavioral patterns such as conversational engagement, sleep, and location with Large Language Models (LLMs). This integration creates a highly personalized and context-aware journaling experience, enhancing self-awareness and well-being by embedding behavioral intelligence into AI. We present an 8-week exploratory study with 20 college students, demonstrating the MindScape app's efficacy in enhancing positive affect (7%), reducing negative affect (11%), loneliness (6%), and anxiety and depression, with a significant week-over-week decrease in PHQ-4 scores (-0.25 coefficient). The study highlights the advantages of contextual AI journaling, with participants particularly appreciating the tailored prompts and insights provided by the MindScape app. Our analysis also includes a comparison of responses to AI-driven contextual versus generic prompts, participant feedback insights, and proposed strategies for leveraging contextual AI journaling to improve well-being on college campuses. By showcasing the potential of contextual AI journaling to support mental health, we provide a foundation for further investigation into the effects of contextual AI journaling on mental health and well-being.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699761"}, {"primary_key": "379639", "vector": [], "sparse_vector": [], "title": "Frailty Assessment Using a Floor Panel-Type Device by Measuring Center of Pressure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Kodai Ito", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In Japan, the demographic shift towards an older population presents significant challenges, particularly in elderly care. Early detection of frality---a precursor to more severe dependency---is essential for maintaining health in older adults. This study introduces a novel floor panel-type device that measures both weight and center of pressure oscillations, facilitating the early identification of frailty through routine activities like standing up, sitting down, and walking. Unlike video or audio monitoring, this method minimizes privacy concerns, making it more suitable for assessing activities of daily living. We evaluated these metrics in a cohort of 28 individuals aged between 64 and 89 years and utilized the k-nearest neighbor algorithm to correlate these measurements with frailty, defined by the revised J-CHS criteria. Our analysis achieved a predictive accuracy of approximately 72.6 % for frailty detection during sitting down actions. These findings underscore the utility of continuous monitoring of weight and center of pressure in daily environments such as bathrooms and hallways for predicting health deterioration.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699783"}, {"primary_key": "379640", "vector": [], "sparse_vector": [], "title": "Pioneering Cooperative Air-Ground Instant Delivery Using UAVs and Crowdsourced Couriers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Instant delivery, which has a strict time limit, has become a fundamental service in modern cities. Increasing demands and labor shortages make efficient instant delivery more challenging. Recently, the industry has recruited Unmanned Aerial Vehicles (UAVs) and part-time couriers to address this critical issue. However, the inherent delivery ability of crowdsourced couriers and the limited battery capacity of UAVs make them hard to solely meet the rapidly increasing instant delivery demands. Therefore, this paper proposes the first air-ground cooperative UAV and Courier Delivery paradigm, called UCD, to develop an optimal courier recruitment plan and assignment of instant delivery tasks for UAVs and couriers. In the UCD paradigm, courier and UAV delivery models are introduced, and a recruitment plan that considers both delivery demand and courier availability is designed. With this foundation, a data-driven algorithm is proposed to optimize delivery task assignments, maximizing the flexibility of UAVs and the capacity of couriers. Comprehensive evaluations are conducted on multiple long-term real-world datasets to demonstrate the superior performance of UCD.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699722"}, {"primary_key": "379641", "vector": [], "sparse_vector": [], "title": "Perfat: Non-contact Abdominal Obesity Assessment System.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "Extensive research has revealed a strong correlation between excess abdominal fat and chronic diseases, such as insulin resistance, hypertension and hypercholesterolemia. Therefore, the measurement of abdominal fat in daily life is significant. Many household body fat scales can provide abdominal (trunk) fat value for users, however, these scales require users to stand barefoot, gripping the electrodes with their arms straight, which is not friendly for those users who are unable to stand. Specialized equipment in hospitals, such as computed tomography (CT) and magnetic resonance imaging (MRI) machines, can provide accurate abdominal fat value with less restriction on user's posture, but those machines are costly, bulky and professional, making them impractical for home use. In this paper, we present Perfat, a system that aims to estimate the abdominal fat percentage accurately. The key insight of <PERSON><PERSON><PERSON> is that, as signals pass through the human body, the energy attenuation of signals at different frequencies varies, and the abdominal fat percentage can be calculated based on such attenuation variation. To evaluate the performance of <PERSON><PERSON><PERSON>, we developed a prototype using software-defined radio and conducted experiments with 50 participants. We compared <PERSON><PERSON><PERSON> with the Inbody S10 body composition analyzer and found a correlation of over 96.7%.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699727"}, {"primary_key": "379642", "vector": [], "sparse_vector": [], "title": "DEWS: A Distributed Measurement Scheme for Efficient Wireless Sensing.", "authors": ["<PERSON><PERSON> Pang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Yuqing Yin", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "One of the key challenges for wireless sensing systems is how to efficiently enable wireless sensing capabilities for multiple devices while leveraging existing wireless communication resources. In this paper, we propose DEWS, a distributed channel measurement scheme that allows multiple transmitters to perform sensing tasks simultaneously, which considers three key issues in wireless sensing tasks: multi-device sensing resolution, multi-device sensing reliability, and multi-device sensing accuracy. First, we use a carefully designed distributed Resource Unit (dRU) allocation scheme based on OFDMA to ensure that multiple devices perform sensing tasks simultaneously with the entire bandwidth, thereby improving the sensing resolution. Then, a subcarrier linear phase shift scheme is designed to avoid signal interference between different transmitting devices and improve the reliability of multi-device sensing. This scheme realizes the fine-grained Time Division Multiplexing (TDM) of multiple devices, which can also increase the number of simultaneous access of sensing devices. Finally, a sensing accuracy improvement algorithm combining a single antenna-based motion recovery method utilizing Independent Component Analysis (ICA) and sampling frequency offset (SFO) correction is proposed, to further help DEWS to break the inherent bandwidth limits and recover different motions at similar distances. We verify the effectiveness of DEWS through extensive experimental testing using commercial USRPs.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699728"}, {"primary_key": "379643", "vector": [], "sparse_vector": [], "title": "Hide-and-seek: Detecting Workers&apos; Emotional Workload in Emotional Labor Contexts Using Multimodal Sensing.", "authors": ["Eunji Park", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emotional labor refers to the process in which workers are required to express emotions regardless of their actual feelings by the organization. In workplaces where such display rules exist, workers experience an emotional workload. Continued exposure to emotional workload can lead to severe mental and psychological issues. Nevertheless, research on assessing emotional workload remains understudied. In this study, we propose a machine learning model to automatically evaluate workers' emotional workload in emotional labor situations through multimodal sensing. The data collection study was designed based on a call center scenario. Within the study, we manipulated customer behaviors as confederates and assessed the worker's emotional workload. As a result, this study provides a benchmark using well-known features and standard machine learning methods. We achieved an accuracy of up to 87% for binary and three-class classification cases. Finally, we discuss the significance of assessing emotional workload and considerations for its practical application in the workplace.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678593"}, {"primary_key": "379644", "vector": [], "sparse_vector": [], "title": "ClassID: Enabling Student Behavior Attribution from Ambient Classroom Sensing Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ambient classroom sensing systems offer a scalable and non-intrusive way to find connections between instructor actions and student behaviors, creating data that can improve teaching and learning. While these systems effectively provide aggregate data, getting reliable individual student-level information is difficult due to occlusion or movements. Individual data can help in understanding equitable student participation, but it requires identifiable data or individual instrumentation. We propose ClassID, a data attribution method for within a class session and across multiple sessions of a course without these constraints. For within-session, our approach assigns unique identifiers to 98% of students with 95% accuracy. It significantly reduces multiple ID assignments compared to the baseline approach (3 vs. 167) based on our testing on data from 15 classroom sessions. For across-session attributions, our approach, combined with student attendance, shows higher precision than the state-of-the-art approach (85% vs. 44%) on three courses. Finally, we present a set of four use cases to demonstrate how individual behavior attribution can enable a rich set of learning analytics, which is not possible with aggregate data alone.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659586"}, {"primary_key": "379645", "vector": [], "sparse_vector": [], "title": "Parent and Educator Concerns on the Pedagogical Use of AI-Equipped Social Robots.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Social robots equipped with conversational artificial intelligence are becoming increasingly common in educational settings. However, the long-term consequences of such uses remain relatively unknown due to their novelty. To ensure children's safe use of social robots, and proper adoption of the technology, it is crucial to scrutinize potential concerns regarding their usage. This exploration will provide insights to inform the design and development of this technology. Thus, this study investigated parents' and educators' perceptions of social robot use by children in the home and school settings. Our main objectives are to; 1) explore whether the types and/or levels of concern are tied to the role that individuals take (i.e., parents vs. educators); 2) explore if the levels of concern vary based on the gender and age of the potential child user; and 3) compile a catalogue of parents' and educators' concerns, both from the literature and those that are overlooked, surrounding children's use of SRs for learning. To address those inquiries, a cross-national online survey study was conducted with parents and educator participants (N = 396). Overall, participants indicated high levels of concern but recognized the potential in responsibly applying such technology for educational purposes.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678556"}, {"primary_key": "379646", "vector": [], "sparse_vector": [], "title": "Environment Texture Optimization for Augmented Reality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Augmented reality (AR) platforms now support persistent, markerless experiences, in which virtual content appears in the same place relative to the real world, across multiple devices and sessions. However, optimizing environments for these experiences remains challenging; virtual content stability is determined by the performance of device pose tracking, which depends on recognizable environment features, but environment texture can impair human perception of virtual content. Low-contrast 'invisible textures' have recently been proposed as a solution, but may result in poor tracking performance when combined with dynamic device motion. Here, we examine the use of invisible textures in detail, starting with the first evaluation in a realistic AR scenario. We then consider scenarios with more dynamic device motion, and conduct extensive game engine-based experiments to develop a method for optimizing invisible textures. For texture optimization in real environments, we introduce MoMAR, the first system to analyze motion data from multiple AR users, which generates guidance using situated visualizations. We show that MoMAR can be deployed while maintaining an average frame rate &gt; 59fps, for five different devices. We demonstrate the use of MoMAR in a realistic case study; our optimized environment texture allowed users to complete a task significantly faster (p=0.003) than a complex texture.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678510"}, {"primary_key": "379647", "vector": [], "sparse_vector": [], "title": "NeuroCamTags: Long-Range, Battery-free, Wireless Sensing with Neuromorphic Cameras.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this research, we introduce NeuroCamTags, a battery-free platform designed to detect a range of rich human interactions and activities in entire rooms and floors without the need for batteries. The NeuroCamTag system comprises low-cost tags that harvest ambient light energy and utilize high-frequency modulation of light-emitting diodes (LEDs) for wireless communication. These visual signals are captured by an available neuromorphic camera, which boasts temporal resolution and frame rates an order of magnitude higher than those of conventional cameras. We present an event processing pipeline that allows simultaneous localization and identification of multiple unique tags. NeuroCamTags offer a wide range of functionalities, providing battery-free wireless sensing for various physical stimuli, including changes in temperature, contact, button presses, key presses, and even sound cues. Our empirical evaluations demonstrate impressive accuracy at long ranges up to 200 feet. In addition to these findings, we consider a range of applications such as battery-free input devices, tracking of human movement, and long-range detection of human activities in various environments such as kitchens, workshops, etc. By reducing reliance on batteries, NeuroCamTags promotes eco-friendliness and opens doors to exciting possibilities in smart environment technology.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678529"}, {"primary_key": "379648", "vector": [], "sparse_vector": [], "title": "EyeTrAES: Fine-grained, Low-Latency Eye Tracking via Adaptive Event Slicing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Eye-tracking technology has gained significant attention in recent years due to its wide range of applications in human-computer interaction, virtual and augmented reality, and wearable health. Traditional RGB camera-based eye-tracking systems often struggle with poor temporal resolution and computational constraints, limiting their effectiveness in capturing rapid eye movements. To address these limitations, we propose EyeTrAES, a novel approach using neuromorphic event cameras for high-fidelity tracking of natural pupillary movement that shows significant kinematic variance. One of EyeTrAES's highlights is the use of a novel adaptive windowing/slicing algorithm that ensures just the right amount of descriptive asynchronous event data accumulation within an event frame, across a wide range of eye movement patterns. EyeTrAES then applies lightweight image processing functions over accumulated event frames from just a single eye to perform pupil segmentation and tracking (as opposed to gaze-based techniques that require simultaneous tracking of both eyes). We show that these two techniques boost pupil tracking fidelity by 6+%, achieving IoU~=92%, while incurring at least 3x lower latency than competing pure event-based eye tracking alternatives [38]. We additionally demonstrate that the microscopic pupillary motion captured by EyeTrAES exhibits distinctive variations across individuals and can thus serve as a biometric fingerprint. For robust user authentication, we train a lightweight per-user Random Forest classifier using a novel feature vector of short-term pupillary kinematics, comprising a sliding window of pupil (location, velocity, acceleration) triples. Experimental studies with two different datasets (capturing eye movement across a range of environmental contexts) demonstrate that the EyeTrAES-based authentication technique can simultaneously achieve high authentication accuracy (~=0.82) and low processing latency (~=12ms), and significantly outperform multiple state-of-the-art competitive baselines.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699745"}, {"primary_key": "379649", "vector": [], "sparse_vector": [], "title": "StethoSpeech: Speech Generation Through a Clinical Stethoscope Attached to the Skin.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce StethoSpeech, a silent speech interface that transforms flesh-conducted vibrations behind the ear into speech. This innovation is designed to improve social interactions for those with voice disorders, and furthermore enable discreet public communication. Unlike prior efforts, StethoSpeech does not require (a) paired-speech data for recorded vibrations and (b) a specialized device for recording vibrations, as it can work with an off-the-shelf clinical stethoscope. The novelty of our framework lies in the overall design, simulation of the ground-truth speech, and a sequence-to-sequence translation network, which works in the latent space. We present comprehensive experiments on the existing CSTR NAM TIMIT Plus corpus and our proposed StethoText: a large-scale synchronized database of non-audible murmur and text for speech research. Our results show that StethoSpeech provides natural-sounding and intelligible speech, significantly outperforming existing methods on several quantitative and qualitative metrics. Additionally, we showcase its capacity to extend its application to speakers not encountered during training and its effectiveness in challenging, noisy environments. Speech samples are available at https://stethospeech.github.io/StethoSpeech/.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678515"}, {"primary_key": "379650", "vector": [], "sparse_vector": [], "title": "LiquImager: Fine-grained Liquid Identification and Container Imaging System with COTS WiFi Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "WiFi has gradually developed into one of the main candidate technologies for ubiquitous sensing. Based on commercial off-the-shelf (COTS) WiFi devices, this paper proposes LiquImager, which can simultaneously identify liquid and image container regardless of container shape and position. Since the container size is close to the wavelength, diffraction makes the effect of the liquid on the signal difficult to approximate with a simple geometric model (such as ray tracking). Based on <PERSON>'s equations, we construct an electric field scattering sensing model. Using few measurements provided by COTS WiFi devices, we solve the scattering model to obtain the medium distribution of the sensing domain, which is used for identifing and imaging liquids. To suppress the signal noise, we propose LiqU-Net for image enhancement. For the centimeter-scale container that is randomly placed in an area of 25 cm × 25 cm, LiquImager can identify the liquid more than 90% accuracy. In terms of container imaging, LiquImager can accurately find the edge of the container for 4 types of containers with a volume less than 500 ml.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643509"}, {"primary_key": "379651", "vector": [], "sparse_vector": [], "title": "MetaFormer: Domain-Adaptive WiFi Sensing with Only One Labelled Target Sample.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Linqing Gui"], "summary": "WiFi based action recognition has attracted increasing attentions due to its convenience and universality in real-world applications, whereas the domain dependency leads to poor generalization ability towards new sensing environments or subjects. The majority of existing solutions fail to sufficiently extract action-related features from WiFi signals. Moreover, they are unable to make full use of the target data with only the labelled samples taken into consideration. To cope with these issues, we propose a WiFi-based sensing system, MetaFormer, which can effectively recognize actions from unseen domains with only one labelled target sample per category. Specifically, MetaFormer achieves this by firstly constructing a novel spatial-temporal transformer feature extraction structure with dense-sparse input named DS-STT to capture action primary and affiliated movements. It then designs Meta-teacher framework which meta-pre-trains source tasks and updates model parameters by dynamic pseudo label enhancement to bridge the relationship among the labelled and unlabelled target samples. In order to validate the performance of MetaFormer, we conduct comprehensive evaluations on SignFi, Widar and Wiar datasets and achieve superior performances under the one-shot case.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643550"}, {"primary_key": "379652", "vector": [], "sparse_vector": [], "title": "Exploring Uni-manual Around Ear Off-Device Gestures for Earables.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Sun", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Small form factor limits physical input space in earable (i.e., ear-mounted wearable) devices. Off-device earable inputs in alternate mid-air and on-skin around-ear interaction spaces using uni-manual gestures can address this input space limitation. Segmenting these alternate interaction spaces to create multiple gesture regions for reusing off-device gestures can expand earable input vocabulary by a large margin. Although prior earable interaction research has explored off-device gesture preferences and recognition techniques in such interaction spaces, supporting gesture reuse over multiple gesture regions needs further exploration. We collected and analyzed 7560 uni-manual gesture motion data from 18 participants to explore earable gesture reuse by segmentation of on-skin and mid-air spaces around the ear. Our results show that gesture performance degrades significantly beyond 3 mid-air and 5 on-skin around-ear gesture regions for different uni-manual gesture classes (e.g., swipe, pinch, tap). We also present qualitative findings on most and least preferred regions (and associated boundaries) by end-users for different uni-manual gesture shapes across both interaction spaces for earable devices. Our results complement earlier elicitation studies and interaction technologies for earables to help expand the gestural input vocabulary and potentially drive future commercialization of such devices.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643513"}, {"primary_key": "379653", "vector": [], "sparse_vector": [], "title": "PuppetMouse: Practical and Contactless Mouse Manipulation Attack via Intentional Electromagnetic Interference Injection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jinsong Han"], "summary": "Mouse is a ubiquitous input tool crucial for user-computer interaction in modern society. However, the inherent trust in the mouse may pose security risks. If the mouse is maliciously manipulated, the connected computer could be secretly controlled, endangering personal privacy and property. In this paper, we introduce PuppetMouse, the first intentional electromagnetic interference (IEMI) injection-based attack that can effectively manipulate mouse clicks and movements without physical contact. By adjusting the parameters of IEMI signals, PuppetMouse can precisely control the click side, as well as the movement direction and speed. We demonstrate PuppetMouse's effectiveness on 14 wired and wireless mice from popular brands. The short response delay (within 4 ms) affirms the real-time performance of PuppetMouse. Robustness analysis across different attack distances and material occlusions validate the stability and reliability of PuppetMouse. Two case studies on firewall disabling and malicious WiFi connection further prove the severe threats of PuppetMouse in the real world. We also propose an integrated set of hardware and software-based defensive mechanisms to mitigate the risks posed by PuppetMouse.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678570"}, {"primary_key": "379654", "vector": [], "sparse_vector": [], "title": "Self-Supervised Representation Learning and Temporal-Spectral Feature Fusion for Bed Occupancy Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In automated sleep monitoring systems, bed occupancy detection is the foundation or the first step before other downstream tasks, such as inferring sleep activities and vital signs. The existing methods do not generalize well to real-world environments due to single environment settings and rely on threshold-based approaches. Manually selecting thresholds requires observing a large amount of data and may not yield optimal results. In contrast, acquiring extensive labeled sensory data poses significant challenges regarding cost and time. Hence, developing models capable of generalizing across diverse environments with limited data is imperative. This paper introduces SeismoDot, which consists of a self-supervised learning module and a spectral-temporal feature fusion module for bed occupancy detection. Unlike conventional methods that require separate pre-training and fine-tuning, our self-supervised learning module is co-optimized with the primary target task, which directs learned representations toward a task-relevant embedding space while expanding the feature space. The proposed feature fusion module enables the simultaneous exploitation of temporal and spectral features, enhancing the diversity of information from both domains. By combining these techniques, SeismoDot expands the diversity of embedding space for both the temporal and spectral domains to enhance its generalizability across different environments. SeismoDot not only achieves high accuracy (98.49%) and F1 scores (98.08%) across 13 diverse environments, but it also maintains high performance (97.01% accuracy and 96.54% F1 score) even when trained with just 20% (4 days) of the total data. This demonstrates its exceptional ability to generalize across various environmental settings, even with limited data availability.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678514"}, {"primary_key": "379655", "vector": [], "sparse_vector": [], "title": "Examining Psychological Conflict-Handling Strategies for Highly Automated Vehicles to Resolve Legal User-Vehicle Conflicts.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As vehicle automation progresses, with SAE levels 3 to 5 introducing varying degrees of control transfer from driver to vehicle, a key challenge emerges: aligning driver interests with the automated vehicle's (AV) operational goals. Despite technical feasibility, drivers' tendencies to intervene due to distrust in or dissatisfaction with AVs necessitate consideration of control mechanisms in future AV designs. This study investigates the potential of persuasion strategies inspired by Human-Robot Interaction research to harmonize driver actions with AV goals in legal conflict situations. We conducted a Virtual Reality driving simulator study with 36 participants, comparing 11 persuasive conflict-handling strategies and a baseline of no persuasion. Our research helps understand the effects of persuasion on users' compliance with law-abiding AV goals, trust, and acceptance towards the AV and evaluates the effectiveness of these strategies based on their working mechanism (cognitive, emotional, social, politeness) and valence (negative, neutral, positive). Results show that none of the strategies could substantially increase compliance with the AV, but neutral and positive persuasion strategies did not decrease acceptance and trust towards the AV compared to no persuasion. These findings contribute to the discourse on cooperation design and control allocation in AVs, particularly in scenarios involving legal conflicts.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678511"}, {"primary_key": "379657", "vector": [], "sparse_vector": [], "title": "PPG-Hear: A Practical Eavesdropping Attack with Photoplethysmography Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hongbo Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Du", "<PERSON>", "Yanzhi Ren", "<PERSON><PERSON>"], "summary": "Photoplethysmography (PPG) sensors have become integral components of wearable and portable health devices in the current technological landscape. These sensors offer easy access to heart rate and blood oxygenation, facilitating continuous long-term health monitoring in clinic and non-clinic environments. While people understand that health-related information provided by PPG is private, no existing research has demonstrated that PPG sensors are dangerous devices capable of obtaining sensitive information other than health-related data. This work introduces PPG-Hear, a novel side-channel attack that exploits PPG sensors to intercept nearby audio information covertly. Specifically, PPG-Hear exploits low-frequency PPG measurements to discern and reconstruct human speech emitted from proximate speakers. This technology allows attackers to eavesdrop on sensitive conversations (e.g., audio passwords, business decisions, or intellectual properties) without being noticed. To achieve this non-trivial attack on commodity PPG-enabled devices, we employ differentiation and filtering techniques to mitigate the impact of temperature drift and user-specific gestures. We develop the first PPG-based speech reconstructor, which can identify speech patterns in the PPG spectrogram and establish the correlation between PPG and speech spectrograms. By integrating a MiniRocket-based classifier with a PixelGAN model, PPG-Hear can reconstruct human speech using low-sampling-rate PPG measurements. Through an array of real-world experiments, encompassing common eavesdropping scenarios such as surrounding speakers and the device's own speakers, we show that PPG-Hear can achieve remarkable accuracy of 90% for recognizing human speech, surpassing the current state-of-the-art side-channel eavesdropping attacks using motion sensors operating at equivalent sampling rates (i.e., 50Hz to 500Hz).", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659603"}, {"primary_key": "379658", "vector": [], "sparse_vector": [], "title": "Embracing Distributed Acoustic Sensing in Car Cabin for Children Presence Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Contactless acoustic sensing has been actively exploited in the past few years to enable a large range of applications, ranging from fine-grained vital sign monitoring to coarse-grained human tracking. However, existing acoustic sensing systems mainly work on smartphone or smart speaker platforms. In this paper, we envision an exciting new acoustic sensing platform, i.e., car cabin which is inherently embedded with a large number of speakers and microphones. We propose the new concept of distributed acoustic sensing and develop novel designs leveraging the unique characteristics of rich multi-path in car cabin to enable fine-grained sensing even when the primary reflection is totally blocked. By using child presence detection as the application example, we show that child presence can be detected through body motions or even subtle breath (when the child is sleeping or in coma) at all locations in the cabin without any blind spots. We further show that the proposed system can robustly work in different car cabins, achieving an average detection accuracy of 97% and a false alarm rate always below 2% under different scenarios including those challenging ones such as rear-facing seat blockage. We believe the proposed distributed sensing modality in car cabin pushes acoustic sensing one big step towards real-life adoption.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643548"}, {"primary_key": "379660", "vector": [], "sparse_vector": [], "title": "Multimodal Daily-Life Logging in Free-living Environment Using Non-Visual Egocentric Sensors on a Smartphone.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Egocentric non-intrusive sensing of human activities of daily living (ADL) in free-living environments represents a holy grail in ubiquitous computing. Existing approaches, such as egocentric vision and wearable motion sensors, either can be intrusive or have limitations in capturing non-ambulatory actions. To address these challenges, we propose EgoADL, the first egocentric ADL sensing system that uses an in-pocket smartphone as a multi-modal sensor hub to capture body motion, interactions with the physical environment and daily objects using non-visual sensors (audio, wireless sensing, and motion sensors). We collected a 120-hour multimodal dataset and annotated 20-hour data into 221 ADL, 70 object interactions, and 91 actions. EgoADL proposes multi-modal frame-wise slow-fast encoders to learn the feature representation of multi-sensory data that characterizes the complementary advantages of different modalities and adapt a transformer-based sequence-to-sequence model to decode the time-series sensor signals into a sequence of words that represent ADL. In addition, we introduce a self-supervised learning framework that extracts intrinsic supervisory signals from the multi-modal sensing data to overcome the lack of labeling data and achieve better generalization and extensibility. Our experiments in free-living environments demonstrate that EgoADL can achieve comparable performance with video-based approaches, bringing the vision of ambient intelligence closer to reality.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643553"}, {"primary_key": "379662", "vector": [], "sparse_vector": [], "title": "Beyond &quot;Taming Electric Scooters&quot;: Disentangling Understandings of Micromobility Naturalistic Riding.", "authors": ["<PERSON><PERSON>", "Suining He", "<PERSON><PERSON>", "<PERSON>"], "summary": "Electric(e)-scooters have emerged as a popular, ubiquitous, and first/last-mile micromobility transportation option within and across many cities worldwide. With the increasing situation-awareness and on-board computational capability, such intelligent micromobility has become a critical means of understanding the rider's interactions with other traffic constituents (called Rider-to-X Interactions, RXIs), such as pedestrians, cars, and other micromobility vehicles, as well as road environments, including curbs, road infrastructures, and traffic signs. How to interpret these complex, dynamic, and context-dependent RXIs, particularly for the rider-centric understandings across different data modalities --- such as visual, behavioral, and textual data --- is essential for enabling safer and more comfortable micromobility riding experience and the greater good of urban transportation networks. Under a naturalistic riding setting (i.e., without any unnatural constraint on rider's decision-making and maneuvering), we have designed, implemented, and evaluated a pilot Cross-modality E-scooter Naturalistic Riding Understanding System, namely CENRUS, from a human-centered AI perspective. We have conducted an extensive study with CENRUS in sensing, analyzing, and understanding the behavioral, visual, and textual annotation data of RXIs during naturalistic riding. We have also designed a novel, efficient, and usable disentanglement mechanism to conceptualize and understand the e-scooter naturalistic riding processes, and conducted extensive human-centered AI model studies. We have performed multiple downstream tasks enabled by the core model within CENRUS to derive the human-centered AI understandings and insights of complex RXIs, showcasing such downstream tasks as efficient information retrieval and scene understanding. CENRUS can serve as a foundational system for safe and easy-to-use micromobility rider assistance as well as accountable use of micromobility vehicles.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678513"}, {"primary_key": "379663", "vector": [], "sparse_vector": [], "title": "Edge-Light: Exploiting Luminescent Solar Concentrators for Ambient Light Communication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>amalloa"], "summary": "A recent advance in embedded Internet of Things (IoT) exploits ambient light for wireless communication. This new paradigm enables highly efficient links via simple light modulation, but the design space has a fundamental constraint: in most State of the Art (SoA) studies, the link can only follow the propagation direction of ambient light. Consider, for example, a swarm of drones and ground robots that want to communicate with sunlight. Drone-to-robot communication could be possible because sunlight travels downwards from the air (drone) to the ground (robot), allowing drones to modulate light to send information to robots beneath them. Robot-to-robot communication, however, is not possible because sunlight does not travel sideways (parallel to the ground). To allow 'lateral communication' with ambient light, we propose using Luminescent Solar Concentrators (LSC). These optical components receive ambient light on their surface and re-direct part of the spectra towards their edges. Considering this optical property of LSC, our work has three main contributions. First, we benchmark various optical properties of LSC to assess their performance for ambient light communication. Second, we combine LSC with liquid crystal (LC) shutters to form lateral links with ambient light. Third, we test our links indoors and outdoors with artificial and natural ambient light, by enhancing two robots with our LSC transceivers and showing that they can exchange basic commands and coordinate tasks by communicating only with sunlight.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678574"}, {"primary_key": "379664", "vector": [], "sparse_vector": [], "title": "Planning the Future in a Longer Perspective: Effects of a One-week Forecast of Mental Health.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shin&apos;<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A long-term perspective toward the future enables a more comprehensive approach to decision-making, considering a variety of potential scenarios. The forecasting of mental health was anticipated to promote proactive planning, however, it faces challenges such as a short forecasting period and a lack of intuitive understanding of the relationship between actions and the forecast. This study presents a novel mental health indicator that incorporates a long-term perspective by considering past actions. A four-week experiment was conducted with 105 participants to evaluate the effects of a one-week forecast. Qualitative analysis reveals the effects of the one-week forecast on behavioral planning, emotional states, and reasons for disregarding the forecasts. Findings indicate that conventional mood indicators prompt participants to prioritize pre-existing schedules and perceive the forecast as infeasible, whereas the proposed indicator enhances the ability to plan work schedules in advance. Our results offer valuable insights into the presentation of forecasts for effectively managing mental health, considering the time constraints of everyday life.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643538"}, {"primary_key": "379665", "vector": [], "sparse_vector": [], "title": "Symptom Detection with Text Message Log Distributions for Holistic Depression and Anxiety Screening.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Major Depressive Disorder (MDD) and Generalized Anxiety Disorder (GAD) are both heterogeneous in their clinical presentations, manifesting with unique symptom profiles. Despite this, prior digital phenotype research has primarily focused on disorder-level detection rather than symptom-level detection. In this research, we predict the existence of individual symptoms of MDD and GAD with SMS log metadata, and ensemble these symptom-level classifiers to screen for depression and anxiety, thus accounting for disorder heterogeneity. Further, we collect an additional dataset of retrospectively harvested SMS logs to augment an existing dataset collected after COVID-19 altered communication patterns, and propose two new types of distribution features: consecutive messages and conversation ratio. Our symptom-level detectors achieved a balanced accuracy of 0.7 in 13 of the 16 MDD and GAD symptoms, with reply latency distribution features achieving a balanced accuracy of 0.78 when detecting anxiety symptom trouble relaxing. When combined into disorder-level ensembles, these symptom-level detectors achieved a balanced accuracy of 0.76 for depression screening and 0.73 for anxiety screening, with tree boosting methods demonstrating particular efficacy. Accounting for disorder heterogeneity, our research provides insight into the value of SMS logs for the assessment of depression and anxiety diagnostic criteria.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643554"}, {"primary_key": "379666", "vector": [], "sparse_vector": [], "title": "Evaluating Autonomous Vehicle External Communication Using a Multi-Pedestrian VR Simulator.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "Xi<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the rise of autonomous vehicles (AVs) in transportation, a pressing concern is their seamless integration into daily life. In multi-pedestrian settings, two challenges emerge: ensuring unambiguous communication to individual pedestrians via external Human-Machine Interfaces (eHMIs), and the influence of one pedestrian over another. We conducted an experiment (N=25) using a multi-pedestrian virtual reality simulator. Participants were paired and exposed to three distinct eHMI concepts: on the vehicle, within the surrounding infrastructure, and on the pedestrian themselves, against a baseline without any eHMI. Results indicate that all eHMI concepts improved clarity of communication over the baseline, but differences in their effectiveness were observed. While pedestrian and infrastructure communications often provided more direct clarity, vehicle-based cues at times introduced uncertainty elements. Furthermore, the study identified the role of co-located pedestrians: in the absence of clear AV communication, individuals frequently sought cues from their peers.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678506"}, {"primary_key": "379668", "vector": [], "sparse_vector": [], "title": "ChromaFlash: Snapshot Hyperspectral Imaging Using Rolling Shutter Cameras.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Kiriakos N. Kutulakos", "<PERSON>"], "summary": "Hyperspectral imaging captures scene information across narrow, contiguous bands of the electromagnetic spectrum. Despite its proven utility in industrial and biomedical applications, its ubiquity has been limited by bulky form factors, slow capture times, and prohibitive costs. In this work, we propose a generalized approach to snapshot hyperspectral imaging that only requires a standard rolling shutter camera and wavelength-adjustable lighting. The crux of this approach entails using the rolling shutter as a spatiotemporal mask, varying incoming light quicker than the camera's frame rate in order for the captured image to contain rows of pixels illuminated at different wavelengths. An image reconstruction pipeline then converts this coded image into a complete hyperspectral image using sparse optimization. We demonstrate the feasibility of this approach by deploying a low-cost system called ChromaFlash, which uses a smartphone's camera for image acquisition and a series of LEDs to change the scene's illumination. We evaluated ChromaFlash through simulations on two public hyperspectral datasets and assessed its spatial and spectral accuracy across various system parameters. We also tested the real-world performance of our prototype by capturing diverse scenes under varied ambient lighting conditions. In both experiments, ChromaFlash outperformed state-of-the-art techniques that use deep learning to convert RGB images into hyperspectral ones, achieving snapshot performance not demonstrated by prior attempts at accessible hyperspectral imaging.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678582"}, {"primary_key": "379669", "vector": [], "sparse_vector": [], "title": "GrainSense: A Wireless Grain Moisture Sensing System Based on Wi-Fi Signals.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Grain moisture sensing plays a critical role in ensuring grain quality and reducing grain losses. However, existing commercial off-the-shelf (COTS) grain moisture sensing systems are either expensive, inconvenient or inaccurate, which greatly limit their widespread deployment in real-world scenarios. To fill this gap, we develop a system called GrainSense which leverages COTS Wi-Fi devices to detect the grain moisture without the need for dedicated sensors. Specifically, we propose a wireless grain moisture detection model based on the refraction phenomenon of Wi-Fi signals and the Multiple-Input-Multiple-Output (MIMO) technology. On one hand, we correlate the grain moisture with the phase difference between two refracted Wi-Fi signals that propagate along different paths, based on which grain moisture can be deduced accordingly. On the other hand, to reduce the multi-path interference in indoor environments (e.g., the granary), we adopt Wi-Fi beamforming to enhance the refracted signal. In particular, a new signal feature (i.e., the Wi-Fi CSI beamforming ratio) is designed to eliminate the effect of sub-carrier frequency bias and cumulative phase bias. To validate the effectiveness of the developed system, we conduct extensive experiments with different types of grains in both the laboratory and the granary. Results show that the system can accurately estimate the grain moisture with an mean absolute error smaller than 5%, which meets the requirements for commercial usage. To the best of our knowledge, this is the first model-based work that achieves accurate grain moisture detection based on wireless sensing.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678589"}, {"primary_key": "379670", "vector": [], "sparse_vector": [], "title": "UbiPhysio: Support Daily Functioning, Fitness, and Rehabilitation with Action Understanding and Feedback in Natural Language.", "authors": ["Chongyang Wang", "Yuan Feng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce UbiPhysio, a milestone framework that delivers fine-grained action description and feedback in natural language to support people's daily functioning, fitness, and rehabilitation activities. This expert-like capability assists users in properly executing actions and maintaining engagement in remote fitness and rehabilitation programs. Specifically, the proposed UbiPhysio framework comprises a fine-grained action descriptor and a knowledge retrieval-enhanced feedback module. The action descriptor translates action data, represented by a set of biomechanical movement features we designed based on clinical priors, into textual descriptions of action types and potential movement patterns. Building on physiotherapeutic domain knowledge, the feedback module provides clear and engaging expert feedback. We evaluated UbiPhysio's performance through extensive experiments with data from 104 diverse participants, collected in a home-like setting during 25 types of everyday activities and exercises. We assessed the quality of the language output under different tuning strategies using standard benchmarks. We conducted a user study to gather insights from clinical physiotherapists and potential users about our framework. Our initial tests show promise for deploying UbiPhysio in real-life settings without specialized devices.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643552"}, {"primary_key": "379671", "vector": [], "sparse_vector": [], "title": "Size Matters: Characterizing the Effect of Target Size on Wi-Fi Sensing Based on the Fresnel Zone Model.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The model-based Wi-Fi sensing approach has shown advantages on facilitating the development of more robust sensing systems, due to its capability of revealing the physical and mathematical sensing mechanisms without the requirement of a large set of training data. Existing models usually treat the sensing target as a particle and characterize the propagation of Wi-Fi signals accordingly, i.e., the effect of target size is ignored. However, in most real-world scenarios, the sensing targets are non-particle and different targets usually have different sizes. Considering that the size difference may have a significant impact on the sensing performance, it is necessary to develop a size-aware model to enrich the Wi-Fi sensing theory. To fill this gap, we propose a non-particle target oriented Wi-Fi sensing model, aiming to describe the relationship between the Wi-Fi signal and the target size. Specifically, by extending the classical particle target oriented Wi-Fi Fresnel zone model, we characterize and quantify the reflected signals from different parts of the non-particle target in a more fine-grained manner. We find the amplitude of the reflected Wi-Fi signals increases and decreases periodically along with the changing of the target size, which is called as the oscillation phenomenon. To validate the proposed model, we implement two sensing applications with a pair of transceivers, including a respiration detection system and a target size measurement system. Extensive experiments demonstrate the correctness and the usefulness of the proposed size-aware model. To the best of our knowledge, this is the first theoretical model that reveals the effect of target size on Wi-Fi sensing.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699726"}, {"primary_key": "379672", "vector": [], "sparse_vector": [], "title": "Moat: Adaptive Inside/Outside Detection System for Smart Homes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smart-home technology is now pervasive, demanding increased attention to the security of the devices and the privacy of the home's residents. To assist residents in making security and privacy decisions - e.g., whether to allow a new device to connect to the network, or whether to be alarmed when an unknown device is discovered - it helps to know whether the device is inside the home, or outside. In this paper we present MOAT, a system that leverages Wi-Fi sniffers to analyze the physical properties of a device's wireless transmissions to infer whether that device is located inside or outside of a home. MOAT can adaptively self-update to accommodate changes in the home indoor environment to ensure robust long-term performance. Notably, MOAT does not require prior knowledge of the home's layout or cooperation from target devices, and is easy to install and configure. We evaluated MOAT in four different homes with 21 diverse commercial smart devices and achieved an overall balanced accuracy rate of up to 95.6%. Our novel periodic adaptation technique allowed our approach to maintain high accuracy even after rearranging furniture in the home. MOAT is a practical and efficient first step for monitoring and managing devices in a smart home.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699751"}, {"primary_key": "379673", "vector": [], "sparse_vector": [], "title": "UWB-enabled Sensing for Fast and Effortless Blood Pressure Monitoring.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Blood Pressure (BP) is a critical vital sign to assess cardiovascular health. However, existing cuff-based and wearable-based BP measurement methods require direct contact between the user's skin and the device, resulting in poor user experience and limited engagement for regular daily monitoring of BP. In this paper, we propose a contactless approach using Ultra-WideBand (UWB) signals for regular daily BP monitoring. To remove components of the received signals that are not related to the pulse waves, we propose two methods that utilize peak detection and principal component analysis to identify aliased and deformed parts. Furthermore, to extract BP-related features and improve the accuracy of BP prediction, particularly for hypertensive users, we construct a deep learning model that extracts features of pulse waves at different scales and identifies the different effects of features on BP. We build the corresponding BP monitoring system named RF-BP and conduct extensive experiments on both a public dataset and a self-built dataset. The experimental results show that RF-BP can accurately predict the BP of users and provide alerts for users with hypertension. Over the self-built dataset, the mean absolute error (MAE) and standard deviation (SD) for SBP are 6.5 mmHg and 6.1 mmHg, and the MAE and SD for DBP are 4.7 mmHg and 4.9 mmHg.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659617"}, {"primary_key": "379674", "vector": [], "sparse_vector": [], "title": "MRehab: A Mixed Reality Rehabilitation System Supporting Integrated Speech and Hand Training.", "authors": ["<PERSON>", "Can <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Integrated speech and hand-motor training is an effective post-stroke rehabilitation method. However, few interactive systems and assistive technologies were developed in this field. Driven by this challenge, we leverage Mixed Reality technology, which merges immersive virtual scenarios with physical hands-on tools in the real world, to provide patients with multi-modal interactions and engaging training experiences. Following a user-centered design approach, we first interviewed seven therapists to identify user requirements and design considerations. We further designed MRehab, an interactive rehabilitation system that allows patients to regain speech and hand skills through MR scenarios that depict daily living activities. We conducted a preliminary user test with 12 patients and 5 therapists to validate the feasibility and understand the user experience with MRehab. The results confirmed its feasibility for hand-motor training. Additionally, the patients expressed high motivation, engagement, and a positive attitude toward using MRehab. Our findings demonstrate the potential of MR technology in integrated speech and hand function rehabilitation training.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678532"}, {"primary_key": "379675", "vector": [], "sparse_vector": [], "title": "XRF55: A Radio Frequency Dataset for Human Indoor Action Analysis.", "authors": ["<PERSON><PERSON>", "Yizhe Lv", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jinsong Han"], "summary": "Radio frequency (RF) devices such as Wi-Fi transceivers, radio frequency identification tags, and millimeter-wave radars have appeared in large numbers in daily lives. The presence and movement of humans can affect the propagation of RF signals, further, this phenomenon is exploited for human action recognition. Compared to camera solutions, RF approaches exhibit greater resilience to occlusions and lighting conditions, while also raising fewer privacy concerns in indoor scenarios. However, current works have many limitations, including the unavailability of datasets, insufficient training samples, and simple or limited action categories for specific applications, which seriously hinder the growth of RF solutions, presenting a significant obstacle in transitioning RF sensing research from the laboratory to a wide range of everyday life applications. To facilitate the transitioning, in this paper, we introduce and release a large-scale multiple radio frequency dataset, named XRF55, for indoor human action analysis. XRF55 encompasses 42.9K RF samples and 55 action classes of human-object interactions, human-human interactions, fitness, body motions, and human-computer interactions, collected from 39 subjects within 100 days. These actions were meticulously selected from 19 RF sensing papers and 16 video action recognition datasets. Each action is chosen to support various applications with high practical value, such as elderly fall detection, fatigue monitoring, domestic violence detection, etc. Moreover, XRF55 contains 23 RFID tags at 922.38MHz, 9 Wi-Fi links at 5.64GHz, one mmWave radar at 60-64GHz, and one Azure Kinect with RGB+D+IR sensors, covering frequency across decimeter wave, centimeter wave, and millimeter wave. In addition, we apply a mutual learning strategy over XRF55 for the task of action recognition. Unlike simple modality fusion, under mutual learning, three RF modalities are trained collaboratively and then work solely. We find these three RF modalities will promote each other. It is worth mentioning that, with synchronized Kinect, XRF55 also supports the exploration of action detection, action segmentation, pose estimation, human parsing, mesh reconstruction, etc., with RF-only or RF-Vision approaches.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643543"}, {"primary_key": "379676", "vector": [], "sparse_vector": [], "title": "Multi-Subject 3D Human Mesh Construction Using Commodity WiFi.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces MultiMesh, a multi-subject 3D human mesh construction system based on commodity WiFi. Our system can reuse commodity WiFi devices in the environment and is capable of working in non-line-of-sight (NLoS) conditions compared with the traditional computer vision-based approach. Specifically, we leverage an L-shaped antenna array to generate the two-dimensional angle of arrival (2D AoA) of reflected signals for subject separation in the physical space. We further leverage the angle of departure and time of flight of the signal to enhance the resolvability for precise separation of close subjects. Then we exploit information from various signal dimensions to mitigate the interference of indirect reflections according to different signal propagation paths. Moreover, we employ the continuity of human movement in the spatial-temporal domain to track weak reflected signals of faraway subjects. Finally, we utilize a deep learning model to digitize 2D AoA images of each subject into the 3D human mesh. We conducted extensive experiments in real-world multi-subject scenarios under various environments to evaluate the performance of our system. For example, we conduct experiments with occlusion and perform human mesh construction for different distances between two subjects and different distances between subjects and WiFi devices. The results show that MultiMesh can accurately construct 3D human meshes for multiple users with an average vertex error of 4cm. The evaluations also demonstrate that our system could achieve comparable performance for unseen environments and people. Moreover, we also evaluate the accuracy of spatial information extraction and the performance of subject detection. These evaluations demonstrate the robustness and effectiveness of our system.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643504"}, {"primary_key": "379677", "vector": [], "sparse_vector": [], "title": "Let It Snow: Designing Snowfall Experience in VR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present Snow, a cross-modal interface that integrates cold and tactile stimuli in mid-air to create snowflakes and raindrops for VR experiences. Snow uses six Peltier packs and an ultrasound haptic display to create unique cold-tactile sensations for users to experience catching snowflakes and getting rained on their bare hands. Our approach considers humans' ability to identify tactile and cold stimuli without masking each other when projected onto the same location on their skin, making illusions of snowflakes and raindrops. We design both visual and haptic renderings to be tightly coupled to present snow melting and rain droplets for realistic visuo-tactile experiences. For multiple snowflakes and raindrops rendering, we propose an aggregated haptic scheme to simulate heavy snowfall and rainfall environments with many visual particles. The results show that the aggregated haptic rendering scheme demonstrates a more realistic experience than other schemes. We also confirm that our approach of providing cold-tactile cues enhances the user experiences in both conditions compared to other modality conditions.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659587"}, {"primary_key": "379678", "vector": [], "sparse_vector": [], "title": "G-VOILA: Gaze-Facilitated Information Querying in Daily Scenarios.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern information querying systems are progressively incorporating multimodal inputs like vision and audio. However, the integration of gaze --- a modality deeply linked to user intent and increasingly accessible via gaze-tracking wearables --- remains underexplored. This paper introduces a novel gaze-facilitated information querying paradigm, named G-VOILA, which synergizes users' gaze, visual field, and voice-based natural language queries to facilitate a more intuitive querying process. In a user-enactment study involving 21 participants in 3 daily scenarios (p = 21, scene = 3), we revealed the ambiguity in users' query language and a gaze-voice coordination pattern in users' natural query behaviors with G-VOILA. Based on the quantitative and qualitative findings, we developed a design framework for the G-VOILA paradigm, which effectively integrates the gaze data with the in-situ querying context. Then we implemented a G-VOILA proof-of-concept using cutting-edge deep learning techniques. A follow-up user study (p = 16, scene = 2) demonstrates its effectiveness by achieving both higher objective score and subjective score, compared to a baseline without gaze data. We further conducted interviews and provided insights for future gaze-facilitated information querying systems.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659623"}, {"primary_key": "379680", "vector": [], "sparse_vector": [], "title": "Accurate Blood Pressure Measurement Using Smartphone&apos;s Built-in Accelerometer.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>peng Dai", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Gu"], "summary": "Efficient blood pressure (BP) monitoring in everyday contexts stands as a substantial public health challenge that has garnered considerable attention from both industry and academia. Commercial mobile phones have emerged as a promising tool for BP measurement, benefitting from their widespread popularity, portability, and ease of use. Most mobile phone-based systems leverage a combination of the built-in camera and LED to capture photoplethysmography (PPG) signals, which can be used to infer BP by analyzing the blood flow characteristics. However, due to low Signal-to-Noise (SNR), various factors such as finger motion, improper finger placement, skin tattoos, or fluctuations in environmental lighting can distort the PPG signal. These distortions consequentially affect the performance of BP estimation. In this paper, we introduce a novel sensing system that utilizes the built-in accelerometer of a mobile phone to capture seismocardiography (SCG) signals, enabling accurate BP measurement. Our system surpasses previous mobile phone-based BP measurement systems, offering advantages such as high SNR, ease of use, and power efficiency. We propose a triple-stage noise reduction scheme, integrating improved complete ensemble empirical mode decomposition with adaptive noise (ICEEMDAN), recursive least squares (RLS) adaptive filter, and soft-thresholding, to effectively reconstruct high-quality heartbeat waveforms from initially contaminated raw SCG signals. Moreover, we introduce a data augmentation technique encompassing normalization coupled with temporal-sliding, effectively augmenting the diversity of the training sample set. To enable battery efficiency on smartphone, we propose a resource-efficient deep learning model that incorporates resource-efficient convolution, shortcut connections, and Huber loss. We conduct extensive experiments with 70 volunteers, comprising 35 healthy individuals and 35 individuals diagnosed with hypertension, under a user-independent setting. The excellent performance of our system demonstrates its capacity for robust and accurate daily BP measurement.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659599"}, {"primary_key": "379681", "vector": [], "sparse_vector": [], "title": "UFace: Your Smartphone Can &quot;Hear&quot; Your Facial Expression!", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Facial expression recognition (FER) is a crucial task for human-computer interaction and a multitude of multimedia applications that typically call for friendly, unobtrusive, ubiquitous, and even long-term monitoring. Achieving such a FER system meeting these multi-requirements faces critical challenges, mainly including the tiny irregular non-periodic deformation of emotion movements, high variability in facial positions and severe self-interference caused by users' own other behavior. In this work, we present UFace, a long-term, unobtrusive and reliable FER system for daily life using acoustic signals generated by a portable smartphone. We design an innovative network model with dual-stream input based on the attention mechanism, which can leverage distance-time profile features from various viewpoints to extract fine-grained emotion-related signal changes, thus enabling accurate identification of many kinds of expressions. Meanwhile, we propose effective mechanisms to deal with a series of interference issues during actual use. We implement UFace prototype with a daily-used smartphone and conduct extensive experiments in various real-world environments. The results demonstrate that UFace can successfully recognize 7 typical facial expressions with an average accuracy of 87.8% across 20 participants. Besides, the evaluation of different distances, angles, and interferences proves the great potential of the proposed system to be employed in practical scenarios.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643546"}, {"primary_key": "379682", "vector": [], "sparse_vector": [], "title": "CrowdBot: An Open-Environment Robot Management System for On-Campus Services.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON>", "Jiawei Sun", "Junxiang Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In contemporary campus environments, the provision of timely and efficient services is increasingly challenging due to limitations in accessibility and the complexity and openness of the environment. Existing service robots, while operational, often struggle with adaptability and dynamic task management, leading to inefficiencies. To overcome these limitations, we introduce CrowdBot, a robot management system that enhances service in campus environments. Our system leverages a hierarchical reinforcement learning-based cloud-edge hybrid scheduling framework (REDIS), for efficient online streaming task assignment and dynamic action scheduling. To verify the REDIS framework, we have developed a digital twin simulation platform, which integrates large language models and hot-swapping technology. This facilitates seamless human-robot interaction, efficient task allocation, and cost-effective execution through the reuse of robot equipment. Our comprehensive simulations corroborate the system's remarkable efficacy, demonstrating significant improvements with a 24.46% reduction in task completion times, a 9.37% decrease in travel distances, and up to a 3% savings in power usage. Additionally, the system achieves a 7.95% increase in the number of tasks completed and a 9.49% reduction in response time. Real-world case studies further affirm CrowdBot's capability to adeptly execute tasks and judiciously recycle resources, thereby offering a smart and viable solution for the streamlined management of campus services.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659601"}, {"primary_key": "379683", "vector": [], "sparse_vector": [], "title": "RDGait: A mmWave Based Gait User Recognition System for Complex Indoor Environments Using Single-chip Radar.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we aim to study millimeter-wave-based gait recognition in complex indoor environments, focusing on dealing with multipath ghosts and supporting rapid deployment to new environments. We design a ghost detection algorithm based on velocity change patterns. This algorithm relies solely on velocity estimation, requiring no environmental priors or multipath modeling. Hence, it is suitable for single-chip millimeter-wave radar with low angular resolution and can be conveniently deployed in new indoor settings. In addition, we build a gait recognition network based on an attention-based Recurrent Neural Network (RNN) to extract spatiotemporal-velocity features from RD heatmaps. We have evaluated RDGait in two scenarios: a corridor scenario and a crowded office scenario, with 125 volunteers of different genders and ages ranging from 6 to 63. RDGait achieves a user recognition accuracy exceeding 95% among 125 candidates in both scenarios. We have further deployed RDGait in two additional scenarios using the pretrain-finetune approach. With minimal user registration data, RD<PERSON>ait could achieve satisfactory (&gt; 90%) recognition accuracy in these new environments considering different radar placements, heights, and number of co-existing users.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678552"}, {"primary_key": "379684", "vector": [], "sparse_vector": [], "title": "RF-GymCare: Introducing Respiratory Prior for RF Sensing in Gym Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON>", "<PERSON>"], "summary": "Exercise-induced sudden death has become an increasingly serious health issue in recent years. Fortunately, respiratory monitoring can serve as an effective early prevention solution. In particular, radio frequency (RF) sensing has emerged as a promising solution due to its contactless nature and privacy preservation. Existing RF-based respiratory monitoring solutions for homes, offices, or vehicles would suffer from significant performance degradation when deployed in typical gym workouts, where respiratory and physical movements are mixed in the RF signals in an unpredictable manner, leading to a blind source separation (BSS) problem. What is worse, the prior assumptions of source independence in existing BSS algorithms no longer hold, leaving respiratory monitoring in gym scenarios as an open issue. In this paper, we propose RF-GymCare, which introduces respiratory priors into the BSS problem and utilizes knowledge distillation to transfer these priors, aiming to learn the demixing weight from mixed signals. With the demixing weight, the respiratory signal can be obtained through a linear combination of the mixed signals. Our experiments with 13 hours of data across nine types of exercises demonstrate its superiority over existing methods, achieving a median similarity of 0.75 and a median rate error of 0.5 respiration per minute (RPM), respectively.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678568"}, {"primary_key": "379685", "vector": [], "sparse_vector": [], "title": "Design and Fabrication of Multifunctional E-Textiles by Upcycling Waste Cotton Fabrics through Carbonization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The merging of electronic materials and textiles has triggered the proliferation of wearables and interactive surfaces in the ubiquitous computing era. However, this leads to e-textile waste that is difficult to recycle and decompose. Instead, we demonstrate an eco-design approach to upcycle waste cotton fabrics into functional textile elements through carbonization without the need for additional materials. We identify optimal parameters for the carbonization process and develop encapsulation techniques to improve the response, durability, and washability of the carbonized textiles. We then configure these e-textiles into various 'design primitives' including sensors, interconnects, and heating elements, and evaluate their electromechanical properties against commercially available e-textiles. Using these primitives, we demonstrate several applications, including a haptic-transfer fabric, a joint-sensing wearable, and an intelligent sailcloth. Finally, we highlight how the sensors can be composted, re-carbonized and coated onto other fabrics, or repurposed into different sensors towards their end-of-life to promote a circular manufacturing process.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659588"}, {"primary_key": "379686", "vector": [], "sparse_vector": [], "title": "Seeing through the Tactile: 3D Human Shape Estimation from Temporal In-Bed Pressure Images.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Humans spend about one-third of their lives resting. Reconstructing human dynamics in in-bed scenarios is of considerable significance in sleep studies, bedsore monitoring, and biomedical factor extractions. However, the mainstream human pose and shape estimation methods mainly focus on visual cues, facing serious issues in non-line-of-sight environments. Since in-bed scenarios contain complicated human-environment contact, pressure-sensing bedsheets provide a non-invasive and privacy-preserving approach to capture the pressure distribution on the contact surface, and have shown prospects in many downstream tasks. However, few studies focus on in-bed human mesh recovery. To explore the potential of reconstructing human meshes from the sensed pressure distribution, we first build a high-quality temporal human in-bed pose dataset, TIP, with 152K multi-modality synchronized images. We then propose a label generation pipeline for in-bed scenarios to generate reliable 3D mesh labels with a SMPLify-based optimizer. Finally, we present P<PERSON>esh, a simple yet effective temporal human shape estimator to directly generate human meshes from pressure image sequences. We conduct various experiments to evaluate PIMesh's performance, showing that PIMesh archives 79.17mm joint position errors on our TIP dataset. The results demonstrate that the pressure-sensing bedsheet could be a promising alternative for long-term in-bed human shape estimation.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659612"}, {"primary_key": "379687", "vector": [], "sparse_vector": [], "title": "ESATED: Leveraging Extra-weak Supervision with Auxiliary Task for Enhanced Non-intrusiveness in Energy Disaggregation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Non-intrusive load monitoring (NILM) is crucial to smart grid, which enables applications such as energy conservation and human activity recognition. As a typical task of NILM, energy disaggregation is to decompose total power consumption into appliance-level ones. Despite the remarkable achievements of deep-learning-based methods, their training phase still requires intrusively collected appliance-level power data as strong labels, which are directly used for supervising predictions. In this paper, we present ESATED, a novel energy disaggregation system which instead utilizes non-intrusively collected binary on-off states of appliances as labels, thus enhancing non-intrusiveness throughout the life cycle. However, our labels are inherently weak labels due to the weak correlation between labels (binary states) and predictions (real-valued power), thus making our model struggle in terms of feasible supervision and acceptable performance. To tackle this challenge, we first explore the feasibility of binary-state-based weak supervision, and then integrate it into an auxiliary learning system, where an auxiliary subtask (i.e., state classification) is introduced to further enhance the performance of the primary task (i.e., energy disaggregation). We conduct extensive experiments on two real-world public datasets, and also implement the prototype system in a practical scenario. Corresponding results reveal that even using weak labels, ESATED could achieve performance and transferability second only to the state-of-the-art model. This result demonstrates the effectiveness of the proposed approach to extract information and train the model from extra weak labels.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699729"}, {"primary_key": "379688", "vector": [], "sparse_vector": [], "title": "ChatCam: Embracing LLMs for Contextual Chatting-to-Camera with Interest-Oriented Video Summarization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Fu Li", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cameras are ubiquitous in society, with users increasingly looking to extract insights about the physical world. Current human-to-camera interaction methods, while advanced, still need to support an intuitive, conversational interaction as one would expect in human-to-human communication. To achieve a more natural interaction between humans and cameras, we proposed a novel contextual chatting-to-camera paradigm. This paradigm allows users to interact with the camera using natural language including raising interests and questions. In response, the camera can customize specific tasks tailored to these interests and attempt to provide answers to the questions asked. We designed ChatCam, embracing LLMs for contextual chatting-to-camera with interest-oriented video summarization. With a novel prompt with the actor-critic LLMs approach, ChatCam can understand users' interests and translate them into some tasks and objects. ChatCam can also customize relevant models with the help of the multi-modal large language model and deep reinforcement learning on the resource-constrained edge and maintain high accuracy. Results show that ChatCam achieves an improvement up to 43.9% in understanding user interests and 21.1% in model accuracy compared to state-of-the-art methods in multiple settings. Various examples and the user study also prove the effectiveness of ChatCam in practice.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699731"}, {"primary_key": "379689", "vector": [], "sparse_vector": [], "title": "DeepBreath: Breathing Exercise Assessment with a Depth Camera.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Practicing breathing exercises is crucial for patients with chronic obstructive pulmonary disease (COPD) to enhance lung function. Breathing mode (chest or belly breathing) and lung volume are two important metrics for supervising breathing exercises. Previous works propose that these metrics can be sensed separately in a contactless way, but they are impractical with unrealistic assumptions such as distinguishable chest and belly breathing patterns, the requirement of calibration, and the absence of body motions. In response, this research proposes DeepBreath, a novel depth camera-based breathing exercise assessment system, to overcome the limitations of the existing methods. DeepBreath, for the first time, considers breathing mode and lung volume as two correlated measurements and estimates them cooperatively with a multitask learning framework. This design boosts the performance of breathing mode classification. To achieve calibration-free lung volume measurement, DeepBreath uses a data-driven approach with a novel UNet-based deep-learning model to achieve one-model-fit-all lung volume estimation, and it is designed with a lightweight silhouette segmentation model with knowledge transferred from a state-of-the-art large segmentation model that enhances the estimation performance. In addition, DeepBreath is designed to be resilient to involuntary motion artifacts with a temporal-aware body motion compensation algorithm. We collaborate with a clinical center and conduct experiments with 22 healthy subjects and 14 COPD patients to evaluate DeepBreath. The experimental result shows that DeepBreath can achieve high breathing metrics estimation accuracy but with a much more realistic setup compared with previous works.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678519"}, {"primary_key": "379690", "vector": [], "sparse_vector": [], "title": "AFace: Range-flexible Anti-spoofing Face Authentication via Smartphone Acoustic Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "User authentication on smartphones needs to balance both security and convenience. Many image-based face authentication methods are vulnerable to spoofing and are plagued by privacy breaches, so models based on acoustic sensing have emerged to achieve reliable user authentication. However, they can only achieve reasonable performance under specific conditions (i.e., a fixed range), and they can not resist 3D printing attacks. To address these limitations, we present a novel user authentication system, referred to as AFace. The system mainly consists of two parts: an iso-depth model and a range-adaptive (RA) algorithm. The iso-depth model establishes a connection between acoustic echoes and facial structures, while taking into account the influence of biological materials on echo energy, making it resistant to 3D printing attacks (as it's difficult to replicate material information in 3D printing). RA algorithm can adaptively compensate for the distance between the user and the smartphone, enabling flexible authentication modes. Results from experiments with 40 volunteers demonstrate that AFace achieves an average accuracy of 96.9% and an F1 score of 96.9%, and no image/video-based attack is observed to succeed in spoofing.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643510"}, {"primary_key": "379691", "vector": [], "sparse_vector": [], "title": "Can Large Language Models Be Good Companions?: An LLM-Based Eyewear System with Conversational Common Ground.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zhouyang Lu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Wang", "Mingzhi Dong", "<PERSON><PERSON>", "Qin Lv", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Developing chatbots as personal companions has long been a goal of artificial intelligence researchers. Recent advances in Large Language Models (LLMs) have delivered a practical solution for endowing chatbots with anthropomorphic language capabilities. However, it takes more than LLMs to enable chatbots that can act as companions. Humans use their understanding of individual personalities to drive conversations. Chatbots also require this capability to enable human-like companionship. They should act based on personalized, real-time, and time-evolving knowledge of their users. We define such essential knowledge as the common ground between chatbots and their users, and we propose to build a common-ground-aware dialogue system from an LLM-based module, named OS-1, to enable chatbot companionship. Hosted by eyewear, OS-1 can sense the visual and audio signals the user receives and extract real-time contextual semantics. Those semantics are categorized and recorded to formulate historical contexts from which the user's profile is distilled and evolves over time, i.e., OS-1 gradually learns about its user. OS-1 combines knowledge from real-time semantics, historical contexts, and user-specific profiles to produce a common-ground-aware prompt input into the LLM module. The LLM's output is converted to audio, spoken to the wearer when appropriate. We conduct laboratory and in-field studies to assess OS-1's ability to build common ground between the chatbot and its user. The technical feasibility and capabilities of the system are also evaluated. Our results show that by utilizing personal context, OS-1 progressively develops a better understanding of its users. This enhances user satisfaction and potentially leads to various personal service scenarios, such as emotional support and assistance.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659600"}, {"primary_key": "379692", "vector": [], "sparse_vector": [], "title": "Mental-LLM: Leveraging Large Language Models for Mental Health Prediction via Online Text Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yao", "Yuanzhe Dong", "<PERSON><PERSON>", "Hong Yu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Advances in large language models (LLMs) have empowered a variety of applications. However, there is still a significant gap in research when it comes to understanding and enhancing the capabilities of LLMs in the field of mental health. In this work, we present a comprehensive evaluation of multiple LLMs on various mental health prediction tasks via online text data, including Alpaca, Alpaca-LoRA, FLAN-T5, GPT-3.5, and GPT-4. We conduct a broad range of experiments, covering zero-shot prompting, few-shot prompting, and instruction fine-tuning. The results indicate a promising yet limited performance of LLMs with zero-shot and few-shot prompt designs for mental health tasks. More importantly, our experiments show that instruction finetuning can significantly boost the performance of LLMs for all tasks simultaneously. Our best-finetuned models, Mental-Alpaca and Mental-FLAN-T5, outperform the best prompt design of GPT-3.5 (25 and 15 times bigger) by 10.9% on balanced accuracy and the best of GPT-4 (250 and 150 times bigger) by 4.8%. They further perform on par with the state-of-the-art task-specific language model. We also conduct an exploratory case study on LLMs' capability on mental health reasoning tasks, illustrating the promising capability of certain models such as GPT-4. We summarize our findings into a set of action guidelines for potential methods to enhance LLMs' capability for mental health tasks. Meanwhile, we also emphasize the important limitations before achieving deployability in real-world mental health settings, such as known racial and gender bias. We highlight the important ethical risks accompanying this line of research.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643540"}, {"primary_key": "379694", "vector": [], "sparse_vector": [], "title": "UHead: Driver Attention Monitoring System Using UWB Radar.", "authors": ["Chongzhi Xu", "<PERSON><PERSON>", "Z<PERSON>yuan Ren", "<PERSON>", "Huadong Ma"], "summary": "The focus of Advanced driver-assistance systems (ADAS) is extending from the vehicle and road conditions to the driver because the driver's attention is critical to driving safety. Although existing sensor and camera based methods can monitor driver attention, they rely on specialised hardware and environmental conditions. In this paper, we aim to develop an effective and easy-to-use driver attention monitoring system based on UWB radar. We exploit the strong association between head motions and driver attention and propose UHead that infers driver attention by monitoring the direction and angle of the driver's head rotation. The core idea is to extract rotational time-frequency representation from reflected signals and to estimate head rotation angles from complex head reflections. To eliminate the dynamic noise generated by other body parts, UHead leverages the large magnitude and high velocity of head rotation to extract head motion information from the dynamically coupled information. UHead uses a bilinear joint time-frequency representation to avoid the loss of time and frequency resolution caused by windowing of traditional methods. We also design a head structure-based rotation angle estimation algorithm to accurately estimate the rotation angle from the time-varying rotation information of multiple reflection points in the head. Experimental results show that we achieve 12.96° median error of 3D head rotation angle estimation in real vehicle scenes.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643551"}, {"primary_key": "379695", "vector": [], "sparse_vector": [], "title": "DrHouse: An LLM-empowered Diagnostic Reasoning System through Harnessing Outcomes from Sensor Data and Expert Knowledge.", "authors": ["<PERSON><PERSON><PERSON>", "Siyang Jiang", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hai <PERSON>", "<PERSON><PERSON><PERSON>", "Hongkai Chen", "<PERSON><PERSON>", "Zhenyu Yan"], "summary": "Large language models (LLMs) have the potential to transform digital healthcare, as evidenced by recent advances in LLM-based virtual doctors. However, current approaches rely on patient's subjective descriptions of symptoms, causing increased misdiagnosis. Recognizing the value of daily data from smart devices, we introduce a novel LLM-based multi-turn consultation virtual doctor system, DrHouse, which incorporates three significant contributions: 1) It utilizes sensor data from smart devices in the diagnosis process, enhancing accuracy and reliability. 2) DrHouse leverages continuously updating medical knowledge bases to ensure its model remains at diagnostic standard's forefront. 3) DrHouse introduces a novel diagnostic algorithm that concurrently evaluates potential diseases and their likelihood, facilitating more nuanced and informed medical assessments. Through multi-turn interactions, DrHouse determines the next steps, such as accessing daily data from smart devices or requesting in-lab tests, and progressively refines its diagnoses. Evaluations on three public datasets and our self-collected datasets show that DrHouse can achieve up to an 31.5% increase in diagnosis accuracy over the state-of-the-art baselines. The results of a 32-participant user study show that 75% medical experts and 91.7% test subjects are willing to use DrHouse.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699765"}, {"primary_key": "379696", "vector": [], "sparse_vector": [], "title": "Talk2Care: An LLM-based Voice Assistant for Communication between Healthcare Providers and Older Adults.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yao", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Guo<PERSON> Gordon <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite the plethora of telehealth applications to assist home-based older adults and healthcare providers, basic messaging and phone calls are still the most common communication methods, which suffer from limited availability, information loss, and process inefficiencies. One promising solution to facilitate patient-provider communication is to leverage large language models (LLMs) with their powerful natural conversation and summarization capability. However, there is a limited understanding of LLMs' role during the communication. We first conducted two interview studies with both older adults (N=10) and healthcare providers (N=9) to understand their needs and opportunities for LLMs in patient-provider asynchronous communication. Based on the insights, we built an LLM-powered communication system, Talk2Care, and designed interactive components for both groups: (1) For older adults, we leveraged the convenience and accessibility of voice assistants (VAs) and built an LLM-powered conversational interface for effective information collection. (2) For health providers, we built an LLM-based dashboard to summarize and present important health information based on older adults' conversations with the VA. We further conducted two user studies with older adults and providers to evaluate the usability of the system. The results showed that Talk2Care could facilitate the communication process, enrich the health information collected from older adults, and considerably save providers' efforts and time. We envision our work as an initial exploration of LLMs' capability in the intersection of healthcare and interpersonal communication.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659625"}, {"primary_key": "379698", "vector": [], "sparse_vector": [], "title": "Ring-a-Pose: A Ring for Continuous Hand Pose Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hyunchul Lim", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Oh", "<PERSON>", "<PERSON>"], "summary": "We present Ring-a-Pose, a single untethered ring that tracks continuous 3D hand poses. Located in the center of the hand, the ring emits an inaudible acoustic signal that each hand pose reflects differently. Ring-a-Pose imposes minimal obtrusions on the hand, unlike multi-ring or glove systems. It is not affected by the choice of clothing that may cover wrist-worn systems. In a series of three user studies with a total of 36 participants, we evaluate Ring<PERSON><PERSON>-<PERSON><PERSON>'s performance on pose tracking and micro-finger gesture recognition. Without collecting any training data from a user, Ring-a-Pose tracks continuous hand poses with a joint error of 14.1mm. The joint error decreases to 10.3mm for fine-tuned user-dependent models. Ring-a-Pose recognizes 7-class micro-gestures with a 90.60% and 99.27% accuracy for user-independent and user-dependent models, respectively. Furthermore, the ring exhibits promising performance when worn on any finger. Ring-a-Pose enables the future of smart rings to track and recognize hand poses using relatively low-power acoustic sensing.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699741"}, {"primary_key": "379700", "vector": [], "sparse_vector": [], "title": "ToMoBrush: Exploring Dental Health Sensing Using a Sonic Toothbrush.", "authors": ["<PERSON>ang <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Early detection of dental disease is crucial to prevent adverse outcomes. Today, dental X-rays are currently the most accurate gold standard for dental disease detection. Unfortunately, regular X-ray exam is still a privilege for billions of people around the world. In this paper, we ask: \"Can we develop a low-cost sensing system that enables dental self-examination in the comfort of one's home?\" This paper presents ToMoBrush, a dental health sensing system that explores using off-the-shelf sonic toothbrushes for dental condition detection. Our solution leverages the fact that a sonic toothbrush produces rich acoustic signals when in contact with teeth, which contain important information about each tooth's status. ToMoBrush extracts tooth resonance signatures from the acoustic signals to characterize the dental condition of each tooth. We further develop a data-driven signal processing pipeline to detect and discriminate different dental conditions. We evaluate ToMoBrush on 19 participants and dental-standard models for detecting common dental problems including caries, calculus, and food impaction, achieving a detection ROC-AUC of 0.90, 0.83, and 0.88 respectively. Interviews with dental experts further validate ToMoBrush's potential in enhancing at-home dental healthcare.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678505"}, {"primary_key": "379701", "vector": [], "sparse_vector": [], "title": "&apos;Eco Is Just Marketing&apos;: Unraveling Everyday Barriers to the Adoption of Energy-Saving Features in Major Home Appliances.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Energy-saving features (ESFs) represent a simple way to reduce the resource consumption of home appliances (HAs), yet they remain under-utilized. While prior research focused on increasing the use of ESFs through behavior change interventions, there is currently no clarity on the barriers that restrict their utilization in the first place. To bridge this gap, we conducted a qualitative analysis of 349 Amazon product reviews and 98 Reddit discussions, yielding three qualitative themes that showcase how users perceive, interact with, and evaluate ESFs in HAs. Based on these themes, we derived frequent barriers to ESF adoption, which guided a subsequent expert focus group (N=5) to assess the suitability of behavior change interventions and potential alternative strategies for ESF adoption. Our findings deepen the understanding of everyday barriers surrounding ESFs and enable the targeted design and assessment of interventions for future HAs.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643558"}, {"primary_key": "379702", "vector": [], "sparse_vector": [], "title": "mP-Gait: Fine-grained Parkinson&apos;s Disease Gait Impairment Assessment with Robust Feature Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>peng Dai", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Patients with Parkinson's disease (PD) often show gait impairments including shuffling gait, festination, and lack of arm and leg coordination. Quantitative gait analysis can provide valuable insights for PD diagnosis and monitoring. Prior work has utilized 3D motion capture, foot pressure sensors, IMUs, etc. to assess the severity of gait impairment in PD patients These sensors, despite their high precision, are often expensive and cumbersome to wear which makes them not the best option for long-term monitoring and naturalistic deployment settings. In this paper, we introduce mP-Gait, a millimeter-wave (mmWave) radar-based system designed to detect the gait features in PD patients and predict the severity of their gait impairment. Leveraging the high frequency and wide bandwidth of mmWave radar signals, mP-Gait is able to capture high-resolution reflected signals from different body parts during walking. We develop a pipeline to detect walking, extract gait features using signal analysis methods, and predict patients' UPDRS-III gait scores with a machine learning model. As gait features from PD patients with gait impairment are correctly and robustly extracted, mP-Gait is able to observe the fine-grained gait impairment severity fluctuation caused by medication response. To evaluate mP-Gait, we collected gait features from 144 participants (with UPDRS-III gait scores between 0 and 2) containing over 4000 gait cycles. Our results show that mP-Gait can achieve a mean absolute error of 0.379 points in predicting UPDRS-III gait scores.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678577"}, {"primary_key": "379703", "vector": [], "sparse_vector": [], "title": "DeltaLCA: Comparative Life-Cycle Assessment for Electronics Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Shwetak N. Patel", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reducing the environmental footprint of electronics and computing devices requires new tools that empower designers to make informed decisions about sustainability during the design process itself. This is not possible with current tools for life cycle assessment (LCA) which require substantial domain expertise and time to evaluate the numerous chips and other components that make up a device. We observe first that informed decision-making does not require absolute metrics and can instead be done by comparing designs. Second, we can use domain-specific heuristics to perform these comparisons. We combine these insights to develop DeltaLCA, an open-source interactive design tool that addresses the dual challenges of automating life cycle inventory generation and data availability by performing comparative analyses of electronics designs. Users can upload standard design files from Electronic Design Automation (EDA) software and the tool will guide them through determining which one has greater carbon footprints. DeltaLCA leverages electronics-specific LCA datasets and heuristics and tries to automatically rank the two designs, prompting users to provide additional information only when necessary. We show through case studies DeltaLCA achieves the same result as evaluating full LCAs, and that it accelerates LCA comparisons from eight expert-hours to a single click for devices with ~30 components, and 15 minutes for more complex devices with ~100 components.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643561"}, {"primary_key": "379705", "vector": [], "sparse_vector": [], "title": "SARS: A Personalized Federated Learning Framework Towards Fairness and Robustness against Backdoor Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lingling An", "<PERSON>", "<PERSON><PERSON>"], "summary": "Federated Learning (FL), an emerging distributed machine learning framework that enables each client to collaboratively train a global model by sharing local knowledge without disclosing local private data, is vulnerable to backdoor model poisoning attacks. By compromising some users, the attacker manipulates their local training process, and uploads malicious gradient updates to poison the global model, resulting in the poisoned global model behaving abnormally on the sub-tasks specified by the malicious user. Prior research has proposed various strategies to mitigate backdoor attacks. However, existing FL backdoor defense methods affect the fairness of the FL system, while fair FL performance may not be robust. Motivated by these concerns, in this paper, we propose Self-Awareness Revision (SARS), a personalized FL framework designed to resist backdoor attacks and ensure the fairness of the FL system. SARS consists of two key modules: adaptation feature extraction and knowledge mapping. In the adaptation feature extraction module, benign users can adaptively extract clean global knowledge with self-awareness and self-revision of the backdoor knowledge transferred from the global model. Based on the previous module, users can effectively ensure the correct mapping of clean sample features and labels. Through extensive experimental results, SARS can defend against backdoor attacks and improve the fairness of the FL system by comparing several state-of-the-art FL backdoor defenses or fair FL methods, including FedAvg, Ditto, WeakDP, FoolsGold, and FLAME.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678571"}, {"primary_key": "379706", "vector": [], "sparse_vector": [], "title": "Lipwatch: Enabling Silent Speech Recognition on Smartwatches using Acoustic Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Silent Speech Interfaces (SSI) on mobile devices offer a privacy-friendly alternative to conventional voice input methods. Previous research has primarily focused on smartphones. In this paper, we introduce Lipwatch, a novel system that utilizes acoustic sensing techniques to enable SSI on smartwatches. Lipwatch leverages the inaudible waves emitted by the watch's speaker to capture lip movements and then analyzes the echo to enable SSI. In contrast to acoustic sensing-based SSI on smartphones, our development of Lipwatch takes into full consideration the specific scenarios and requirements associated with smartwatches. Firstly, we elaborate a wake-up-free mechanism, allowing users to interact without the need for a wake-up phrase or button presses. The mechanism utilizes the inertial sensors on the smartwatch to detect gestures, in combination with acoustic signals that detecting lip movements to determine whether SSI should be activated. Secondly, we design a flexible silent speech recognition mechanism that explores limited vocabulary recognition to comprehend a broader range of user commands, even those not present in the training dataset, relieving users from strict adherence to predefined commands. We evaluate Lipwatch on 15 individuals using a set of the 80 most common interaction commands on smartwatches. The system achieves a Word Error Rate (WER) of 13.7% in user-independent test. Even when users utter commands containing words absent in the training set, Lipwatch still demonstrates a remarkable 88.7% top-3 accuracy. We implement a real-time version of Lipwatch on a commercial smartwatch. The user study shows that Lipwatch can be a practical and promising option to enable SSI on smartwatches.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659614"}, {"primary_key": "379707", "vector": [], "sparse_vector": [], "title": "Sensing to Hear through Memory: Ultrasound Speech Enhancement without Real Ultrasound Signals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Speech enhancement on mobile devices is a very challenging task due to the complex environmental noises. Recent works using lip-induced ultrasound signals for speech enhancement open up new possibilities to solve such a problem. However, these multi-modal methods cannot be used in many scenarios where ultrasound-based lip sensing is unreliable or completely absent. In this paper, we propose a novel paradigm that can exploit the prior learned ultrasound knowledge for multi-modal speech enhancement only with the audio input and an additional pre-enrollment speaker embedding. We design a memory network to store the ultrasound memory and learn the interrelationship between the audio and ultrasound modality. During inference, the memory network is able to recall the ultrasound representations from audio input to achieve multi-modal speech enhancement without needing real ultrasound signals. Moreover, we introduce a speaker embedding module to further boost the enhancement performance as well as avoid the degradation of the recalling when the noise level is high. We adopt an end-to-end multi-task manner to train the proposed framework and perform extensive evaluations on the collected dataset. The results show that our method yields comparable performance with audio-ultrasound methods and significantly outperforms the audio-only methods.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659598"}, {"primary_key": "379708", "vector": [], "sparse_vector": [], "title": "Are We in the Zone? Exploring the Features and Method of Detecting Simultaneous Flow Experiences Based on EEG Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>xiang Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "When executing interdependent personal tasks for the team's purpose, simultaneous individual flow (simultaneous flow) is the antecedent condition of achieving shared team flow. Detecting simultaneous flow helps better understanding the status of team members, which is thus important for optimizing multi-user interaction systems. However, there is currently a lack exploration on objective features and methods for detecting simultaneous flow. Based on brain mechanism of flow in teamwork and previous studies on electroencephalogram (EEG)-based individual flow detection, this study aims to explore the significant EEG features related to simultaneous flow, as well as effective detection methods based on EEG signals. First, a two-player simultaneous flow task is designed, based on which we construct the first multi-EEG signal dataset of simultaneous flow. Then, we explore the potential EEG signal features that may be related to individual and simultaneous flow and validate their effectiveness in simultaneous flow detection with various machine learning models. The results show that 1) the inter-brain synchrony features are relevant to simultaneous flow due to enhancing the models' performance in detecting different types of simultaneous flow; 2) the features from the frontal lobe area seem to be given priority attention when detecting simultaneous flows; 3) Random Forests performed best in binary classification while Neural Network and Deep Neural Network3 performed best in ternary classification.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699774"}, {"primary_key": "379709", "vector": [], "sparse_vector": [], "title": "airTac: A Contactless Digital Tactile Receptor for Detecting Material and Roughness via Terahertz Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Huadong Ma"], "summary": "Tactile sensing is an indispensable capability for humans or intelligent devices to engage in complex physical interactions. Mainstream tactile sensors are contact-based, which show limitation in measuring deformable objects and demanding high maintenance effort. As a complementary solution, a new paradigm of contactless tactile sensing is attracting much interest. While promising, they can only identify coarse-grained single tactile perception properties, either material type or surface roughness. In this paper, we propose airTac, which includes a novel contactless digital tactile receptor model, capable of simultaneously extracting these two basic tactile properties in a fine-grained resolution, by harnessing the massive bandwidth of terahertz(THz) frequency band. airTac is designed based on our key finding: while the impact of material type and surface roughness on THz signal intertwine with each other, they are actually separable, i.e., roughness manifests a certain pattern in distorting relative high-frequency components of the whole THz spectrum. Therefore, we custom-design a bio-inspired deep neural network model to decouple the intertwined THz signal, and distill the tactile perception properties embedded underlying the signal. We prototype airTac using a THz time domain spectroscopy, and perform extensive evaluation over 9 different daily material types with 39 different surface roughness. airTac achieves a material identification accuracy of 97.43% and a roughness classification accuracy of 91.46%, which demonstrates that airTac can identify common materials in daily life and exceed a fingertip spatial resolution of 1mm.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678586"}, {"primary_key": "379710", "vector": [], "sparse_vector": [], "title": "AquaKey: Exploiting the Randomness of the Underwater Visible Light Communication Channel for Key Extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Underwater Visible Light Communication (UVLC) is promising due to its relatively strong penetration capability in water and large frequency bandwidth. Visible Light Communication (VLC) is also considered a safer wireless communication paradigm as light signals can be constrained within the area of interest with obstacles such as walls, reducing the chance of potential attack. However, this intuitional security assumption is not true anymore in underwater environment. Recent research shows that the eavesdropping risk of UVLC is more severe than we thought, attributed to the divergence and scattering effects of light beams in water. In this paper, we harness the dynamic nature of underwater environments as a true random resource to extract symmetric keys for UVLC. Specifically, the proposed AquaKey system incorporates instantaneous bidirectional channel probing, computation of relative channel characteristics, and an environment-adaptive quantization algorithm. The above design addresses unique challenges caused by the dynamic underwater environment, including self-interference, high-frequency disturbance, and mismatch, ensuring the practicality and applicability of AquaKey. Additionally, AquaKey has negligible impact on communication and has no effect on the illumination function. Through extensive real-world experiments, we show that AquaKey can achieve reliable key extraction with cheap hardware, generating a 512-bit key in just 0.5-1 seconds.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643557"}, {"primary_key": "379711", "vector": [], "sparse_vector": [], "title": "The EarSAVAS Dataset: Enabling Subject-Aware Vocal Activity Sensing on Earables.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yu<PERSON>uan <PERSON>", "<PERSON>", "<PERSON><PERSON>", "Ji<PERSON><PERSON> Tang", "<PERSON><PERSON>", "Shwetak N. Patel", "<PERSON><PERSON><PERSON>"], "summary": "Subject-aware vocal activity sensing on wearables, which specifically recognizes and monitors the wearer's distinct vocal activities, is essential in advancing personal health monitoring and enabling context-aware applications. While recent advancements in earables present new opportunities, the absence of relevant datasets and effective methods remains a significant challenge. In this paper, we introduce EarSAVAS, the first publicly available dataset constructed specifically for subject-aware human vocal activity sensing on earables. EarSAVAS encompasses eight distinct vocal activities from both the earphone wearer and bystanders, including synchronous two-channel audio and motion data collected from 42 participants totaling 44.5 hours. Further, we propose EarVAS, a lightweight multi-modal deep learning architecture that enables efficient subject-aware vocal activity recognition on earables. To validate the reliability of EarSAVAS and the efficiency of EarVAS, we implemented two advanced benchmark models. Evaluation results on EarSAVAS reveal EarVAS's effectiveness with an accuracy of 90.84% and a Macro-AUC of 89.03%. Comprehensive ablation experiments were conducted on benchmark models and demonstrated the effectiveness of feedback microphone audio and highlighted the potential value of sensor fusion in subject-aware vocal activity sensing on earables. We hope that the proposed EarSAVAS and benchmark models can inspire other researchers to further explore efficient subject-aware human vocal activity sensing on earables.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659616"}, {"primary_key": "379712", "vector": [], "sparse_vector": [], "title": "A Self-Enhancement Solution for Standard RFIDs: Software-based Cross-protocol Communication and Localization.", "authors": ["<PERSON><PERSON>", "Zhenjiang Li", "<PERSON>", "<PERSON><PERSON><PERSON> Sun", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Radio Frequency Identification (RFID) is widely recognized for its cost-effectiveness, energy efficiency, and ease of manufacturing, promising versatile applications such as material recognition, vibration detection, and localization. However, these inherent lightweight characteristics necessitate extensive computations or tailored devices for specific tasks. Consequently, there is a critical imperative to explore and augment the sensing and communication capabilities of standard RFIDs to better accommodate a broader spectrum of Internet of Things (IoT) services. In this paper, we introduce SENSO, a self-enhancement solution designed to facilitate diverse tasks without the need for hardware modifications or additional device support. Our approach introduces two key innovations to enable cross-protocol communication (RFID-LoRa) and localization. Firstly, we seamlessly integrate Chirp Spread Spectrum (CSS) modulation into RFID, enabling simultaneous communication with commercial tags and LoRa devices. Secondly, we realize the transmission of wider-band chirps to activate and localize the tag concurrently. By harnessing these techniques, we developed a prototype while addressing several challenges. Our evaluation demonstrates that when maintaining the fundamental RFID communication, SENSO can effectively deliver additional messages to LoRa devices across various indoor scenarios, e.g., through multiple floors, rooms, and corners, and extending over outdoor distances spanning kilometers. Furthermore, it achieves an average accuracy of approximately 5 to 10 centimeters in tag ranging and localization. The source codes of SENSO are available at https://github.com/Cui-<PERSON>/SENSO.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699777"}, {"primary_key": "379713", "vector": [], "sparse_vector": [], "title": "AI-Vision: A Three-Layer Accessible Image Exploration System for People with Visual Impairments in China.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Accessible image exploration systems are able to help people with visual impairments (PVI) to understand image content by providing different types of interactions. With the development of computer vision technologies, image exploration systems are supporting more fine-grained image content processing, including image segmentation, description and object recognition. However, in developing countries like China, it is still rare for PVI to widely rely on such accessible system. To better understand the usage situation of accessible image exploration system in China and improve the image understanding of PVI in China, we developed AI-Vision, an Android based hierarchical accessible image exploration system supporting the generations of image general description, local object description and metadata information. Our 7-day diary study with 10 PVI verified the usability of AI-Vision and also revealed a series of design implications for improving accessible image exploration systems similar to AI-Vision.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678537"}, {"primary_key": "379714", "vector": [], "sparse_vector": [], "title": "AirECG: Contactless Electrocardiogram for Cardiac Disease Monitoring via mmWave Sensing and Cross-domain Diffusion Model.", "authors": ["Langcheng Zhao", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Huadong Ma", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The electrocardiogram (ECG) has always served as a crucial biomedical examination for cardiac diseases monitoring and diagnosing. Typical ECG measurement requires attaching electrodes to the body, which is inconvenient for long-term monitoring. Recent wireless sensing maps wireless signals reflected from human chest into electrical activities of heart so as to reconstruct ECG contactlessly. While making great progress, we find existing works are effective only for healthy populations with normal ECG, but fall short when confronted with the most desired usage scenario: reconstructing ECG accurately for people with cardiac diseases such as atrial fibrillation, premature ventricular beat. To bridge the gap, we propose AirECG, which moves forward to reconstruct ECG for both healthy people and even cardiac patients with morbid ECG, i.e., irregular rhythm and anomalous ECG waveform, via contactless millimeter-wave sensing. To realize AirECG, we first custom-design a cross-domain diffusion model that can perform multiple iteration denoising inference, in contrast with the single-step generative models widely used in previous works. In this way, AirECG is able to identify and eliminate the distortion due to the unstable and irregular cardiac activities, so as to synthesize ECG even during abnormal beats. Furthermore, we enhance the determinacy of AirECG, i.e., to generate high-fidelity ECG, by designing a calibration guidance mechanism to combat the inherent randomness issue of the probabilistic diffusion model. Empirical evaluation demonstrates AirECG's ability of ECG synthesis with Pearson correlation coefficient (PCC) of 0.955 for normal beats. Especially for abnormal beats, the PCC still exhibits a strong correlation of 0.860, with 15.0%~21.1% improvement compared with state-of-the-art approaches.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678550"}, {"primary_key": "379715", "vector": [], "sparse_vector": [], "title": "mmArrhythmia: Contactless Arrhythmia Detection via mmWave Sensing.", "authors": ["Langcheng Zhao", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Huadong Ma", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Arrhythmia is a common problem of irregular heartbeats, which may lead to serious complications such as stroke and even mortality. Due to the paroxysmal nature of arrhythmia, its long-term monitoring and early detection in daily household scenarios, instead of depending on ECG examination only available during clinical visits, are of critical importance. While ambulatory ECG Holter and wearables like smartwatches have been used, they are still inconvenient and interfere with users' daily activities. In this paper, we bridge the gap by proposing mmArrhythmia, which employs low-cost mmWave radar to passively sense cardiac motions and detect arrhythmia, in an unobtrusive contact-less way. Different from previous mmWave cardiac sensing works focusing on healthy people, mmArrhythmia needs to distinguish the minute and transient abnormal cardiac activities of arrhythmia patients. To overcome the challenge, we custom-design an encoder-decoder model that can perform arrhythmia feature encoding, sampling and fusion over raw IQ sensing data directly, so as to discriminate normal heartbeat and arrhythmia. Furthermore, we enhance the robustness of mmArrhythmia by designing multichannel ensemble learning to solve the model bias problem caused by unbalanced arrhythmia data distribution. Empirical evaluation over 79,910 heartbeats demonstrates mmArrhythmia's ability of robust arrhythmia detection, with 97.32% accuracy, 98.63% specificity, and 92.30% sensitivity.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3643549"}, {"primary_key": "379716", "vector": [], "sparse_vector": [], "title": "Crowdsourced Geospatial Intelligence: Constructing 3D Urban Maps with Satellitic Radiance Fields.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Z<PERSON><PERSON> An", "<PERSON><PERSON>"], "summary": "In urban planning and research, 3D city maps are crucial for activities such as cellular network design, urban development, and climate research. Traditionally, creating these models has involved costly techniques like manual 3D mapping, interpretation of satellite or aerial images, or the use of sophisticated depth-sensing equipment. In this work, we propose a novel approach to develop 3D urban maps by examining the influence of urban structures on satellite signals, using GPS records crowdsourced from hundreds of smartphones during everyday user movements. We introduce the concept of satellitic radiance fields (SaRF), a novel neural scene representation technique designed to capture the distribution of GPS signals in urban settings. SaRF employs a sparse voxel octree framework to depict voxel-centric implicit fields, capturing physical properties like the density of each voxel. This model is progressively refined using a differentiable ray-marching process, ultimately leading to the reconstruction of 3D urban maps. Our thorough experimental evaluation, which incorporates approximately 27.4 million GPS records, reveals an average reconstruction accuracy of 83.1% in six varied urban scenes.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678572"}, {"primary_key": "379717", "vector": [], "sparse_vector": [], "title": "Visar: Projecting Virtual Sound Spots for Acoustic Augmented Reality Using Air Nonlinearity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Augmented reality that integrates virtual content in real-world surroundings has attracted lots of concentration as the growing trend of the metaverse. Acoustic augmented reality (AAR) applications have proliferated due to readily available earphones and speakers. AAR can provide omnidirectional engagement through the all-around sense of spatial information. Most existing AAR approaches offer immersive experiences by playing binaural spatial audios according to head-related transfer functions (HRTF). These involve complex modeling and require the user to wear a headphone. Air nonlinearity that can reproduce audible sounds from ultrasound offers opportunities to achieve device-free and omnidirectional sound source projection in AAR. This paper proposes Visar, a device-free virtual sound spots projection system leveraging air nonlinearity. <PERSON><PERSON> achieves simultaneous tracking and sound spot generation while suppressing unintended audio leakages caused by grating lobes and nonlinear effects in mixing lobes through optimization. Considering multi-user scenarios, <PERSON><PERSON> also proposed a multi-spot scheduling scheme to mitigate the mutual interference between the spots. Extensive experiments show the tracking error is 7.83cm and the orientation estimation error is 10.06°, respectively, envisioning the considerable potential of <PERSON><PERSON> in AAR applications.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678546"}, {"primary_key": "379718", "vector": [], "sparse_vector": [], "title": "Predicting Signal Reception Information from GNSS Satellites in Indoor Environments without Site Survey: Towards Opportunistic Indoor Positioning Based on GNSS Fingerprinting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While traditional GPS localization is impractical in certain indoor environments, GPS satellites offer a valuable source of indoor contextual information, particularly around window-side areas where signals are more accessible. This study introduces a new method that predicts GPS signal reception information in a target indoor environment without the need for actual data collection in the target environment. By predicting the strengths of GPS signals that are expected to be measured at various indoor positions, we can construct a GPS signal reception map of a target environment, which functions similarly to a Wi-Fi fingerprinting map surrounding window-side areas, offering a new dimension in indoor positioning systems. For instance, it aids in the correction of accumulated errors in pedestrian dead reckoning (PDR) systems. Our proposed method leverages easily accessible inputs, including satellite location data, indoor floorplan image of the target floor, and 3D mapping of the surrounding buildings, to predict signal reception at each position in the target environment. We developed a specialized neural network, named multi-scale branch fusion network (MSBF-Net), designed to process and integrate data of varying scales, such as the floorplan image of the target environment and 3D map of the surrounding area obtained from Google Earth, with a specific focus on understanding the line-of-sight (LOS) and multi-path effects caused by internal obstacles and surrounding buildings. This advanced capability enables the network to effectively interpret complex signal interactions within urban environments, enhancing its predictive accuracy for GPS signal reception. The effectiveness of our method was rigorously evaluated using real-world environments. In addition, we employed our method to implement opportunistic GPS fingerprint-based indoor positioning, where position estimates are provided when a strong signal is observed from at least one satellite. Surprisingly, the positioning method achieved a positioning error of only 2.8 meters when a smartphone is close to window-side areas even though the method does not rely on labeled training data collected in a target environment and signal infrastructures installed in the target environment.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678554"}, {"primary_key": "379719", "vector": [], "sparse_vector": [], "title": "CoplayingVR: Understanding User Experience in Shared Control in Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Shared control of avatars in virtual reality is emerging as a promising method to enhance collaborative interactions. While the literature investigates shared control as an input modality to understand the design of functional experiences, the user experience associated with shared control remains underexplored. In this paper, we investigate the user experience of shared control using a networked VR environment, \"CoplayingVR\". Through a user study comprised of three gaming tasks in VR with 48 participants, we collect both quantitative and qualitative data on the user experience and performance of shared control. Our findings reveal that the shared control mode significantly improved user experience factors and brought novelty to the play. Furthermore, shared control significantly enhanced the task efficiency for novice users. Finally, we discuss how our findings inform the implications for research and practice of shared control for the growing body of research in VR.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3678508"}, {"primary_key": "379720", "vector": [], "sparse_vector": [], "title": "AutoAugHAR: Automated Data Augmentation for Sensor-based Human Activity Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Sensor-based HAR models face challenges in cross-subject generalization due to the complexities of data collection and annotation, impacting the size and representativeness of datasets. While data augmentation has been successfully employed in domains like natural language and image processing, its application in HAR remains underexplored. This study presents AutoAugHAR, an innovative two-stage gradient-based data augmentation optimization framework. AutoAugHAR is designed to take into account the unique attributes of candidate augmentation operations and the unique nature and challenges of HAR tasks. Notably, it optimizes the augmentation pipeline during HAR model training without substantially extending the training duration. In evaluations on eight inertial-measurement-units-based benchmark datasets using five HAR models, AutoAugHAR has demonstrated superior robustness and effectiveness compared to other leading data augmentation frameworks. A salient feature of AutoAugHAR is its model-agnostic design, allowing for its seamless integration with any HAR model without the need for structural modifications. Furthermore, we also demonstrate the generalizability and flexible extensibility of AutoAugHAR on four datasets from other adjacent domains. We strongly recommend its integration as a standard protocol in HAR model training and will release it as an open-source tool1.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659589"}, {"primary_key": "379722", "vector": [], "sparse_vector": [], "title": "ShuffleFL: Addressing Heterogeneity in Multi-Device Federated Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Federated Learning (FL) has emerged as a privacy-preserving paradigm for collaborative deep learning model training across distributed data silos. Despite its importance, FL faces challenges such as high latency and less effective global models. In this paper, we propose ShuffleFL, an innovative framework stemming from the hierarchical FL, which introduces a user layer between the FL devices and the FL server. ShuffleFL naturally groups devices based on their affiliations, e.g., belonging to the same user, to ease the strict privacy restriction-\"data at the FL devices cannot be shared with others\", thereby enabling the exchange of local samples among them. The user layer assumes a multi-faceted role, not just aggregating local updates but also coordinating data shuffling within affiliated devices. We formulate this data shuffling as an optimization problem, detailing our objectives to align local data closely with device computing capabilities and to ensure a more balanced data distribution at the intra-user devices. Through extensive experiments using realistic device profiles and five non-IID datasets, we demonstrate that ShuffleFL can improve inference accuracy by 2.81% to 7.85% and speed up the convergence by 4.11x to 36.56x when reaching the target accuracy.", "published": "2024-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3659621"}]