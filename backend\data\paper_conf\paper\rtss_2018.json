[{"primary_key": "3499633", "vector": [], "sparse_vector": [], "title": "Semi-Extended Tasks: Efficient Stack Sharing Among Blocking Threads.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Memory is an expensive and, therefore, limited resource in deeply embedded real-time systems. Thread stacks substantially contribute to the RAM requirements. To reduce the system's worst-case stack consumption (WCSC), it is state of the art to exploit thread-level preemption constraints to let multiple threads share the same stack. However, deriving a tight, yet correct bound for the shared stack is a difficult undertaking and stack sharing is currently restricted to run-to-completion threads, which are preemptable, but cannot block (i.e., passively wait for an event) at run time. With semi-extended tasks (SETs), we propose a solution for efficient stack sharing among blocking and non-blocking threads on the system level. For this, we refine the stack-sharing granularity from the thread to function level. We provide an efficient intra-thread stack-switch mechanism and an ILP-based WCSC analysis that considers fine-grained preemption constraints and possible function-level switching points from the private to the shared stack. A genetic algorithm then selects switching points that lead to the reduction of the overall WCSC. Compared to systems that run only non-blocking threads on the shared stack, semi-extended tasks decrease the WCSC in our benchmarks on average by 7 percent and up to 52 percent for some systems.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00049"}, {"primary_key": "3499634", "vector": [], "sparse_vector": [], "title": "Design and Analysis of SIC: A Provably Timing-Predictable Pipelined Processor Core.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce the strictly in-order core (SIC), a timing-predictable pipelined processor core. SIC is provably timing compositional and free of timing anomalies. This enables precise and efficient worst-case execution time (WCET) and multi-core timing analysis. SIC's key underlying property is the monotonicity of its transition relation w.r.t. a natural partial order on its microarchitectural states. This monotonicity is achieved by carefully eliminating some of the dependencies between consecutive instructions from a standard in-order pipeline design. SIC preserves most of the benefits of pipelining: it is only about 6-7% slower than a conventional pipelined processor. Its timing predictability enables orders-of-magnitude faster WCET and multi-core timing analysis than conventional designs.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00060"}, {"primary_key": "3499635", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Joint Network and Computing Resource Scheduling for Wireless Networked Control Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Real-time task scheduling for wireless networked control systems provides guarantees for the quality of service. This paper introduces a new model for joint network and computing resource scheduling (JNCRS) in real-time wireless networked control systems. This new end-to-end real-time task model considers a strict execution order of segments including the sensing, the computing and the actuating segment based on the control loop of WNCSs. The general JNCRS problem is proved to be a NP-hard problem. After dividing the JNCRS problem into four subproblems, we propose a polynomial-time optimal algorithm to solve the first subproblem where each segment has unit execution time, by checking the intervals with 100% network resource utilization and modify the deadlines of tasks. To solve the second subproblem where the computing segment is larger than one unit execution time, we define the new timing parameters of each network segment by taking into account the scheduling of the computing segments. We propose a polynomial-time optimal algorithm to check the intervals with the network resource utilization larger than or equal to 100% and modify the timing parameters of tasks based on these intervals.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00035"}, {"primary_key": "3499636", "vector": [], "sparse_vector": [], "title": "Analysis of Dynamic Memory Bandwidth Regulation in Multi-core Real-Time Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "One of the primary sources of unpredictability in modern multi-core embedded systems is contention over shared memory resources, such as caches, interconnects, and DRAM. Despite significant achievements in the design and analysis of multi-core systems, there is a need for a theoretical framework that can be used to reason on the worst-case behavior of real-time workload when both processors and memory resources are subject to scheduling decisions. In this paper, we focus our attention on dynamic allocation of main memory bandwidth. In particular, we study how to determine the worst-case response time of tasks spanning through a sequence of time intervals, each with a different bandwidth-to-core assignment. We show that the response time computation can be reduced to a maximization problem over assignment of memory requests to different time intervals, and we provide an efficient way to solve such problem. As a case study, we then demonstrate how our proposed analysis can be used to improve the schedulability of Integrated Modular Avionics systems in the presence of memory-intensive workload.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00040"}, {"primary_key": "3499637", "vector": [], "sparse_vector": [], "title": "Distributed Real-Time Shortest-Paths Computations with the Field Calculus.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As the density of sensing/computation/actuation nodes is increasing, it becomes more and more feasible and useful to think at an entire network of physical devices as a single, continuous space-time computing machine. The emergent behaviour of the whole software system is then induced by local computations deployed within each node and by the dynamics of the information diffusion. A relevant example of this distribution model is given by aggregate computing and its companion language field calculus, a minimal set of purely functional constructs used to manipulate distributed data structures evolving over space and time, and resulting in robustness to changes. In this paper, we study the convergence time of an archetypal and widely used component of distributed computations expressed in field calculus, called gradient: a fully-distributed estimation of distances over a metric space by a spanning tree. We provide an analytic result linking the quality of the output of a gradient to the amount of computing resources dedicated. The resulting error bounds are then exploited for network design, suggesting an optimal density value taking broadcast interferences into account. Finally, an empirical evaluation is performed validating the theoretical results.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00013"}, {"primary_key": "3499638", "vector": [], "sparse_vector": [], "title": "Dynamic Channel Selection for Real-Time Safety Message Communication in Vehicular Networks.", "authors": ["<PERSON><PERSON> Bai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Ensuring the real-time delivery of safety messages is an important research problem for Vehicle to Vehicle (V2V) communication. Unfortunately, existing work relies only on one or two pre-selected control channels for safety message communication, which can result in poor packet delivery and potential accident when the vehicle density is high. If all the available channels can be dynamically utilized when the control channel is having severe contention, safety messages can have a much better chance to meet their real-time deadlines. In this paper, we propose MC-Safe, a multi-channel V2V communication framework that monitors all the available channels and dynamically selects the best one for safety message transmission. MC-Safe features a novel channel negotiation scheme that allows all the vehicles involved in a potential accident to work collaboratively, in a distributed manner, for identifying a communication channel that meets the delay requirement. Our evaluation results both in simulation and on a hardware testbed with scaled cars show that MC-Safe outperforms existing single-channel solutions and other well-designed multi-channel baselines by having a 12.31% lower deadline miss ratio and an 8.21% higher packet delivery ratio on average.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00016"}, {"primary_key": "3499639", "vector": [], "sparse_vector": [], "title": "Optimal Implementation of Simulink Models on Multicore Architectures with Partitioned Fixed Priority Scheduling.", "authors": ["<PERSON><PERSON><PERSON>", "Ye<PERSON> Zhao", "Haibo Zeng", "<PERSON><PERSON>"], "summary": "Model-based design using the Simulink modeling formalism and associated toolchain has gained popularity in the development of real-time embedded systems. However, the current research on software synthesis for Simulink models has a critical gap for providing a deterministic, semantics-preserving implementation on multicore architectures with partitioned fixed-priority scheduling. In this paper, we consider a semantics-preservation mechanism that combines (1) the RT blocks from Simulink, and (2) task offset assignment to separate the time windows to access shared buffers by communicating tasks. We study the software synthesis problem that optimizes control performance by judiciously assigning task offsets, task priorities, and task communication mechanisms. We develop a problem-specific exact algorithm that uses an abstraction layer to hide the complexity of timing analysis. Experimental results show that it may run a few orders of magnitude faster than a direct formulation in integer linear programming.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00041"}, {"primary_key": "3499640", "vector": [], "sparse_vector": [], "title": "Rapid Routing with Guaranteed Delay Bounds.", "authors": ["<PERSON><PERSON>"], "summary": "We consider networks in which each individual link is characterized by two delay parameters: a (usually very conservative) guaranteed upper bound on the worst-case delay, and an estimate of the delay that is typically encountered, across the link. Given a source and destination node on such a network and an upper bound on the end-to-end delay that can be tolerated, the objective is to determine routes they typically experience a small delay, while guaranteeing to respect the specified end-to-end upper bound under all circumstances. We formalize the problem of determining such routes as a shortest-paths problem on graphs, and derive algorithms for solving this problem optimally.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00012"}, {"primary_key": "3499641", "vector": [], "sparse_vector": [], "title": "ApNet: Approximation-Aware Real-Time Neural Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern embedded cyber-physical systems are becoming entangled with the realm of deep neural networks (DNNs) towards increased autonomy. While applying DNNs can significantly improve the accuracy in making autonomous control decisions, a significant challenge is that DNNs are designed and developed on advanced hardware (e.g., GPU clusters), and will not easily meet strict timing requirements if deployed in a resource-constrained embedded computing environment. One interesting characteristic of DNNs is approximation, which can be used to satisfy real-time requirements by reducing DNNs' execution costs with reasonably sacrificed accuracy. In this paper, we propose ApNet, a timing-predictable runtime system that is able to guarantee deadlines of DNN workloads via efficient approximation. Rather than straightforwardly approximating DNNs, ApNet develops a DNN layer-aware approximation approach that smartly explores the trade-off between the approximation degree and the resulting execution reduction on a per-layer basis. To further reduce approximation-induced accuracy loss at runtime, ApNet explores a rather interesting observation that resource sharing and approximation can mutually supplement one another, particularly in a multi-tasking environment. We have implemented and extensively evaluated ApNet on a mix of 8 different DNN configurations on an NVIDIA Jetson TX2. Experimental results show that ApNet can guarantee timing predictability (i.e., meeting all deadlines), while incurring a reasonable accuracy loss. Moreover, accuracy can be improved by up to 8% via a resource sharing increase of 3.5x on average for overlapping DNN layers.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00017"}, {"primary_key": "3499642", "vector": [], "sparse_vector": [], "title": "PredJoule: A Timing-Predictable Energy Optimization Framework for Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The revolution of deep neural networks (DNNs) is enabling dramatically better autonomy in autonomous driving. However, it is not straightforward to simultaneously achieve both timing predictability (i.e., meeting job latency requirements) and energy efficiency that are essential for any DNN-based autonomous driving system, as they represent two (often) conflicting goals. In this paper, we propose PredJoule, a timing-predictable energy optimization framework for running DNN workloads in a GPU-enabled automotive system. PredJoule achieves both latency guarantees and energy efficiency through a layer-aware design that explores specific performance and energy characteristics of different layers within the same neural network. We implement and evaluate <PERSON>d<PERSON><PERSON><PERSON> on the automotive-specific NVIDIA Jetson TX2 platform for five state-of-the-art DNN models with both high and low variance latency requirements. Experiments show that <PERSON>dJ<PERSON><PERSON> rarely violates job deadlines, and can improve energy by 65% on average compared to five existing approaches and 68% compared to an energy-oriented approach.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00020"}, {"primary_key": "3499643", "vector": [], "sparse_vector": [], "title": "An Efficient Knapsack-Based Approach for Calculating the Worst-Case Demand of AVR Tasks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Thidapat <PERSON>", "<PERSON>"], "summary": "Engine-triggered tasks are real-time tasks that are released when the crankshaft in an engine completes a rotation, which depends on the angular speed and acceleration of the crankshaft itself. In addition, the execution time of an engine-triggered task depends on the speed of the crankshaft. Tasks whose execution times depend on a variable period are referred to as adaptive-variable rate (AVR) tasks. Existing techniques to calculate the worst-case demand of AVR tasks are either inexact or computationally intractable. In this paper, we transform the problem of finding the worst-case demand of AVR tasks over a given time interval into a variant of the knapsack problem to efficiently find the exact solution. We then propose a framework to systematically reduce the search space associated with finding the worst-case demand of AVR tasks. Experimental results reveal that our approach is at least 10 times faster, with an average runtime improvement of 146 times, for randomly generated tasksets when compared to the state-of-the-art technique.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00053"}, {"primary_key": "3499644", "vector": [], "sparse_vector": [], "title": "Deadline-Based Scheduling for GPU with Preemption Support.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Ain<PERSON> Paramakuru"], "summary": "Modern automotive-grade embedded computing platforms feature high-performance Graphics Processing Units (GPUs) to support the massively parallel processing power needed for next-generation autonomous driving applications (e.g., Deep Neural Network (DNN) inference, sensor fusion, path planning, etc). As these workload-intensive activities are pushed to higher criticality levels, there is a stronger need for more predictable scheduling algorithms that are able to guarantee predictability without overly sacrificing GPU utilization. Unfortunately, the real-rime literature on GPU scheduling mostly considered limited (or null) preemption capabilities, while previous efforts in broader domains were often based on programming models and APIs that were not designed to support the real-rime requirements of recurring workloads. In this paper, we present the design of a prototype real-time scheduler for GPU activities on an embedded System on a Chip (SoC) featuring a cutting edge GPU architecture by NVIDIA adopted in the autonomous driving domain. The scheduler runs as a software partition on top of the NVIDIA hypervisor, and it leverages latest generation architectural features, such as pixel-level preemption and threadlevel preemption. Such a design allowed us to implement and test a preemptive Earliest Deadline First (EDF) scheduler for GPU tasks providing bandwidth isolations by means of a Constant Bandwidth Server (CBS). Our work involved investigating alternative programming models for compute APIs, allowing us to characterize CPU-to-GPU command submission with more detailed scheduling information. A detailed experimental characterization is presented to show the significant schedulability improvement of recurring real-time GPU tasks.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00021"}, {"primary_key": "3499645", "vector": [], "sparse_vector": [], "title": "NoCo: ILP-Based Worst-Case Contention Estimation for Mesh Real-Time Manycores.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Manycores are capable of providing the computational demands required by functionally-advanced critical applications in domains such as automotive and avionics. In manycores a network-on-chip (NoC) provides access to shared caches and memories and hence concentrates most of the contention that tasks suffer, with effects on the worst-case contention delay (WCD) of packets and tasks' WCET. While several proposals minimize the impact of individual NoC parameters on WCD, e.g. mapping and routing, there are strong dependences among these NoC parameters. Hence, finding the optimal NoC configurations requires optimizing all parameters simultaneously, which represents a multidimensional optimization problem. In this paper we propose NoCo, a novel approach that combines ILP and stochastic optimization to find NoC configurations in terms of packet routing, application mapping, and arbitration weight allocation. Our results show that NoCo improves other techniques that optimize a subset of NoC parameters.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00043"}, {"primary_key": "3499646", "vector": [], "sparse_vector": [], "title": "Memory Feasibility Analysis of Parallel Tasks Running on Scratchpad-Based Architectures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This work proposes solutions for bounding the worst-case memory space requirement for parallel tasks running on multicore platforms with scratchpad memories. It introduces a feasibility test that verifies whether memories are large enough to contain the maximum memory backlog that may be generated by the system. Both closed-form bounds and more accurate algorithmic techniques are proposed. It is shown how one can use max-plus algebra and solutions to the max-flow cut problem to efficiently solve the memory feasibility problem. Experimental results are presented to evaluate the efficiency of the proposed feasibility analysis techniques on synthetic workload and state-of-the-art benchmarks.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00047"}, {"primary_key": "3499647", "vector": [], "sparse_vector": [], "title": "Partitioned Fixed-Priority Scheduling of Parallel Tasks Without Preemptions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The study of parallel task models executed with predictable scheduling approaches is a fundamental problem for real-time multiprocessor systems. Nevertheless, to date, limited efforts have been spent in analyzing the combination of partitioned scheduling and non-preemptive execution, which is arguably one of the most predictable schemes that can be envisaged to handle parallel tasks. This paper fills this gap by proposing an analysis for sporadic DAG tasks under partitioned fixed-priority scheduling where the computations corresponding to the nodes of the DAG are non-preemptively executed. The analysis has been achieved by means of segmented self-suspending tasks with nonpreemptable segments, for which a new fine-grained analysis is also proposed. The latter is shown to analytically dominate state-of-the-art approaches. A partitioning algorithm for DAG tasks is finally proposed. By means of experimental results, the proposed analysis has been compared against a previouslyproposed analysis for DAG tasks with non-preemptable nodes managed by global fixed-priority scheduling. The comparison revealed important improvements in terms of schedulability performance.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00056"}, {"primary_key": "3499648", "vector": [], "sparse_vector": [], "title": "Dependency Graph Approach for Multiprocessor Real-Time Synchronization.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Over the years, many multiprocessor locking protocols have been designed and analyzed. However, the performance of these protocols highly depends on how the tasks are partitioned and prioritized, and how the resources are shared locally and globally. This paper answers a few fundamental questions when real-time tasks share resources in multiprocessor systems. We explore the fundamental difficulty of the multiprocessor synchronization problem and show that a very simplified version of this problem is NP-hard in the strong sense regardless of the number of processors and the underlying scheduling paradigm. Therefore, the allowance of preemption or migration does not reduce the computational complexity. On the positive side, we develop a dependency-graph approach that is specifically useful for frame-based real-time tasks, i.e., when all tasks have the same period and release their jobs always at the same time. We present a series of algorithms with speedup factors between 2 and 3 under semi-partitioned scheduling. We further explore methodologies for and tradeoffs between preemptive and non-preemptive scheduling algorithms, and partitioned and semi-partitioned scheduling algorithms. Our approach is extended to periodic tasks under certain conditions.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00057"}, {"primary_key": "3499649", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Incorporating Deadline-Based Scheduling in Tasking Programming Model for Extreme-Scale Parallel Computing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Processing and analyzing big data sets updated in real time in an increasing number of applications such as severe weather prediction and particle-physics experiments require the computational power of extreme-scale high-performance computing (HPC) systems. To address the scheduling of massive task/thread sets on these extreme-scale systems, current strategies rely on improving centralized, distributed, and parallel scheduling algorithms as well as virtualization developed for HPC systems which aim to reduce the makespan and balance the load among the computing nodes in these systems. However, these HPC schedulers provide no guarantees on meeting timing constraints such as deadlines that are required in an increasing number of these real-time science workflows. This paper describes a new project which departs from this established trend of best-effort scheduling of large-scale HPC Message Passing Interface (MPI) tasks and ensemble workloads found in fine-grain many-task computing (MTC) applications. The new approach brings real-time scheduling to address the demands of real-time science workloads. This new framework abstracts information about the tasks or threads, and continuously dispatch this workload to meet deadlines and other timing constraints associated with individual tasks or groups of tasks in extreme-scale HPC systems to reduce execution time and energy consumption. This paper introduces deadline-based scheduling in the tasking programming model.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00022"}, {"primary_key": "3499650", "vector": [], "sparse_vector": [], "title": "CycleTandem: Energy-Saving Scheduling for Real-Time Systems with Hardware Accelerators.", "authors": ["Sandeep D&apos;Souza", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Cyber-physical systems such as autonomous vehicles need to process and analyze multiple simultaneous streams of sensor data in real-time. Therefore, these systems require powerful multi-core platforms with hardware accelerators such as GP-GPUs. These accelerators generally consume significant amounts of power. Therefore, power management is required to ensure that task deadlines are met while staying within the energy and thermal constraints of the system. In these systems, most tasks execute using a combination of CPU and accelerator resources. Hence, the power of the CPU and the accelerator needs to be managed in tandem. To reduce energy consumption, commercially-available accelerators such as GP-GPUs and DSPs expose interfaces to scale their operating voltage and frequency. Hence, we propose the CycleTandem static frequency-scaling technique to co-optimize the operating frequencies of both the CPU and the hardware accelerator. Based on practical considerations of real-world platforms, we consider various energy-management scenarios where the accelerator or CPU frequencies may or may not be adjustable, and propose the CycleSolo family of algorithms for such contexts. Furthermore, we also study partitioning techniques to reduce the operating frequency when multi-core processors are used in conjunction with hardware accelerators. Experimental evaluations indicate that our proposed techniques can yield significant energy savings. We also present a case-study on the NVIDIA TX2 embedded platform to illustrate the energy savings delivered by our proposed techniques.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00019"}, {"primary_key": "3499651", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: New Analysis Techniques for Supporting Hard Real-Time Sporadic DAG Task Systems on Multiprocessors.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of globally scheduling hard real-time sporadic DAG task systems on multiprocessors. Existing techniques show that analyzing the DAG model is fundamentally more challenging compared to the ordinary sporadic task model, due to the complex intra-DAG precedence constraints which may cause rather pessimistic schedulability loss. However, such increased loss is counterintuitive because the DAG structure shall better exploit the parallelism provided by the multiprocessor platform. In this work, we present a set of novel scheduling and analysis techniques for better supporting hard real-time sporadic DAG tasks on multiprocessors, through smartly defining and analyzing the execution order of subtasks in each DAG. Interestingly, when each DAG task only contains a single subtask, the proposed utilization-based schedulability test becomes identical to the density test.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00027"}, {"primary_key": "3499652", "vector": [], "sparse_vector": [], "title": "A Generic Coq Proof of Typical Worst-Case Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a generic proof of Typical Worst-Case Analysis (TWCA), an analysis technique for weakly-hard real-time uniprocessor systems. TWCA was originally introduced for systems with fixed priority preemptive (FPP) schedulers and has since been extended to fixed-priority nonpreemptive (FPNP) and earliest-deadline-first (EDF) schedulers. Our generic analysis is based on an abstract model that characterizes the exact properties needed to make TWCA applicable to any system model. Our results are formalized and checked using the Coq proof assistant along with the Prosa schedulability analysis library. Our experience with formalizing real-time systems analyses shows that this is not only a way to increase confidence in our claimed results: The discipline required to obtain machine checked proofs helps understanding the exact assumptions required by a given analysis, its key intermediate steps and how this analysis can be generalized.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00039"}, {"primary_key": "3499653", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Extending Buffer-Aware Worst-Case Timing Analysis of Wormhole NoCs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Worst-case timing analysis of Networks-on-Chip (NoCs) is a crucial aspect to design safe real-time systems based on manycore architectures. In this paper, we present some potential extensions of our previously-published buffer-aware worst-case timing analysis approach to cope with bursty traffic such as real-time audio and video streams. A first promising lead is to improve the algorithm analyzing backpressure patterns to capture consecutive-packet queueing effect while keeping the information about the dependencies between flows. Furthermore, the improved algorithm may also decrease the inherent complexity of computing the indirect blocking latency due to backpressure.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00032"}, {"primary_key": "3499654", "vector": [], "sparse_vector": [], "title": "Tuned Pipes: End-to-End Throughput and Delay Guarantees for USB Devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A fundamental problem in real-time computing is handling device input and output in a timely manner. For example, a control system might require input data from a sensor to be sampled and processed at a regular rate so that output signals to actuators occur within specific delay bounds. Input/output (I/O) devices connect to the host computer using different types of bus interfaces. One of the most popular interfaces in use today is the universal serial bus (USB). USB is now ubiquitous, in part due to its support for many classes of devices with simplified hardware needed to connect to the host. However, typical USB host controller drivers suffer from potential timing delays that affect the delivery of data between tasks and devices. Consequently, this paper introduces tuned pipes, a host controller driver and system framework that guarantees end-to-end latency and throughput requirements for I/O transfers. We expand on our earlier work involving USB 2.0 to support higher bandwidth USB 3.x communication. As a case study, we show how a USB-Controller Area Network (CAN) guarantees temporal isolation and end-to-end guarantees on communication between a set of peripheral devices and host tasks. A comparable USB-CAN bus setup using Linux is not able to achieve the same level of temporal guarantees, even when using SCHED_DEADLINE.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00037"}, {"primary_key": "3499655", "vector": [], "sparse_vector": [], "title": "Uniprocessor Mixed-Criticality Scheduling with Graceful Degradation by Completion Rate.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The scheduling of mixed-criticality (MC) systems with graceful degradation is considered, where LO-criticality tasks are guaranteed some service in HI mode in the form of minimum cumulative completion rates. First, we present an easy to implement admission-control procedure to determine which LO-criticality jobs to complete in HI mode. Then, we propose a demand-bound-function-based MC schedulability test that runs in pseudo-polynomial time for such systems under EDF-VD scheduling, wherein two virtual deadline setting heuristics are considered. Furthermore, we discuss a mechanism for the system to switch back from HI to LO mode and quantify the maximum time duration such recovery process would take. Finally, we show the effectiveness of our proposed method by experimental evaluation in comparison to state-of-the-art MC schedulers.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00052"}, {"primary_key": "3499656", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Towards Real-Time Smart City Communications using Software Defined Wireless Mesh Networking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> <PERSON>"], "summary": "Effective management and provisioning of communication resources is as important in meeting the real-time requirements of smart city cyber physical systems (CPS) as managing computation resources is. The communication infrastructure in Smart cities often involves wireless mesh networks (WMNs). However, enforcing distributed and consistent control in WMNs is challenging since individual routers of a WMN maintain only local knowledge about each of its neighbors, which reflects only a partial visibility of the overall network and hence results in suboptimal resource management decisions. When WMNs must utilize emerging technologies, such as time-sensitive networking (TSN) for the most critical communication needs, e.g., controlling traffic and pedestrian lights, these challenges are further complicated. An attractive solution is to adopt Software Defined Networking (SDN), which offers a centralized, up-to-date view of the entire network by refactoring the wireless protocols into control and forwarding decisions. This paper presents ongoing work to overcome the key challenges and support the end-to-end real-time requirements of smart city CPS applications.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00034"}, {"primary_key": "3499657", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Response Time Bounds for Typed DAG Parallel Tasks on Heterogeneous Multi-cores.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Jinghao Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Heterogenerous multi-cores utilize the strength of different architectures for executing particular types of workload, and usually offer higher performance and energy efficiency. In this paper, we study the worst-case response time (WCRT) analysis of typed scheduling of parallel DAG tasks on heterogeneous multi-cores, where the workload of each vertex in the DAG is only allowed to execute on a particular type of cores. The only known WCRT bound for this problem is grossly pessimistic and suffers the non-self-sustainability problem. In this paper, we propose two new WCRT bounds. The first new bound has the same time complexity as the existing bound, but is more precise and solves its non-self-sustainability problem. The second new bound explores more detailed task graph structure information to greatly improve the precision, but is computationally more expensive. We prove that the problem of computing the second bound is strongly NP-hard if the number of types in the system is a variable, and develop an efficient algorithm which has polynomial time complexity if the number of types is a constant. Experiments with randomly generated workload show that our proposed new methods are significantly more precise than the existing bound while having good scalability.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00028"}, {"primary_key": "3499658", "vector": [], "sparse_vector": [], "title": "An Improved Speedup Factor for Sporadic Tasks with Constrained Deadlines Under Dynamic Priority Scheduling.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Schedulability is a fundamental problem in real-time scheduling, but it has to be approximated due to the intrinsic computational hardness. As the most popular algorithm for deciding schedulability on multiprocess platforms, the speedup factor of partitioned-EDF is challenging to analyze and is far from being determined. Partitioned-EDF was first proposed in 2005 by <PERSON><PERSON> and <PERSON> [1], and was shown to have a speedup factor at most 3-1/m, meaning that if the input of sporadic tasks is feasible on m processors with speed one, partitioned-EDF will always succeed on m processors with speed 3-1/m. In 2011, this upper bound was improved to 2.6322-1/m by <PERSON> and <PERSON> [2], and no more improvements have appeared ever since then. In this paper, we develop a novel method to discretize and regularize sporadic tasks, which enables us to improve, in the case of constrained deadlines, the speedup factor of partitioned-EDF to 2.5556-1/m, very close to the asymptotic lower bound 2.5 in [2].", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00058"}, {"primary_key": "3499659", "vector": [], "sparse_vector": [], "title": "On the Off-Chip Memory Latency of Real-Time Systems: Is DDR DRAM Really the Best Option?", "authors": ["<PERSON>"], "summary": "Predictable execution time upon accessing shared memories in multi-core real-time systems is a stringent requirement. A plethora of existing works focus on the analysis of Double Data Rate Dynamic Random Access Memories (DDR DRAMs), or redesigning its memory to provide predictable memory behavior. In this paper, we show that DDR DRAMs by construction suffer inherent limitations associated with achieving such predictability. These limitations lead to 1) highly variable access latencies that fluctuate based on various factors such as access patterns and memory state from previous accesses, and 2) overly pessimistic latency bounds. As a result, DDR DRAMs can be ill-suited for some real-time systems that mandate a strict predictable performance with tight timing constraints. Targeting these systems, we promote an alternative off-chip memory solution that is based on the emerging Reduced Latency DRAM (RLDRAM) protocol, and propose a predictable memory controller (RLDC) managing accesses to this memory. Comparing with the state-of-the-art predictable DDR controllers, the proposed solution provides up to 11× less timing variability and 6.4× reduction in the worst case memory latency.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00062"}, {"primary_key": "3499660", "vector": [], "sparse_vector": [], "title": "Shedding the Shackles of Time-Division Multiplexing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Multi-core architectures pose many challenges in real-time systems, which arise from contention between concurrent accesses to shared memory. Among the available memory arbitration policies, Time Division Multiplexing (TDM) ensures a predictable behavior by bounding access latencies and guaranteed bandwidth to tasks independently from the other tasks. To do so, TDM guarantees exclusive access to the shared memory in a fixed time window. TDM, however, provides a low resource utilization as it is non-work-conserving. Besides, it is very inefficient for resources having highly variable latencies, such as sharing the access to a DRAM memory. The constant length of a TDM slot is, hence, highly pessimistic and causes an underutilization of the memory. To address these limitations, we present dynamic arbitration schemes that are based on TDM. However, instead of arbitrating at the level of TDM slots, our approach operates at the granularity of clock cycles by exploiting slack time accumulated from preceding requests. This allows the arbiter to reorder memory requests, exploit the actual access latencies of requests, and thus improve memory utilization. We demonstrate that our policies are analyzable as they preserve the guarantees of TDM in the worst case, while our experiments show an improved memory utilization on average.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00059"}, {"primary_key": "3499661", "vector": [], "sparse_vector": [], "title": "RIM: Robust Intersection Management for Connected Autonomous Vehicles.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Utilizing intelligent transportation infrastructures can significantly improve the throughput of intersections of Connected Autonomous Vehicles (CAV), where an Intersection Manager (IM) assigns a target velocity to incoming CAVs in order to achieve a high throughput. Since the IM calculates the assigned velocity for a CAV based on the model of the CAV, it's vulnerable to model mismatches and possible external disturbances. As a result, IM must consider a large safety buffer around all CAVs to ensure a safe scheduling, which greatly degrades the throughput. In addition, IM has to assign a relatively lower speed to CAVs that intend to make a turn at the intersection to avoid rollover. This issue reduces the throughput of the intersection even more. In this paper, we propose a space and time-aware technique to manage intersections of CAVs that is robust against external disturbances and model mismatches. In our method, RIM, IM is responsible for assigning a safe Time of Arrival (TOA) and Velocity of Arrival (VOA) to an approaching CAV such that trajectories of CAVs before and inside the intersection does not conflict. Accordingly, CAVs are responsible for determining and tracking an optimal trajectory to reach the intersection at the assigned TOA while driving at VOA. Since CAVs track a position trajectory, the effect of bounded model mismatch and external disturbances can be compensated. In addition, CAVs that intend to make a turn at the intersection do not need to drive at a slow velocity before entering the intersection. Results from conducting experiments on a 1/10 scale intersection of CAVs show that RIM can reduce the position error at the expected TOA by 18X on average in presence of up to 10% model mismatch and an external disturbance with an amplitude of 5% of max range. In total, our technique can achieve 2.7X better throughput on average compared to velocity assignment techniques.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00014"}, {"primary_key": "3499662", "vector": [], "sparse_vector": [], "title": "Real-Time Computing and the Evolution of Embedded System Designs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Real-time computing provides insightful ways to explore the optimization in resource usages, especially from the time point of view. Nevertheless, real-time task scheduling is recognized by its high complexity when there are non-preemptive shared resources and multiple processors. When more and more practical factors in system designs are considered, such as energy consumption and memory allocation, even some sub-problems in real-time task scheduling become intractable. Although people often criticize various artificial assumptions in real-time task scheduling, they have to admit that ideas in real-time computing and their extensions, such as tradeoff in cost, performance, energy, and even the quality of service, can be applied to multi-dimensional optimization in system designs. In this direction, we witness the rapid development of the embedded system industry and join the task force in system designs, especially mobile devices and non-volatile memory systems. Resource management on mobile devices, with a special emphasis on user experience, should not only consider the response time but also the visual perception of users. Non-volatile memory has also blurred the boundary between the memory and the storage. It enables certain unified considerations of the main memory and storage and also in-memory computing. It shows the ways to break the boundaries between hardware and software layers and have better integration of computing and memory/storage units. The advances in mobile systems and memory innovations inspire the evolution of embedded system designs and have also brought us insights to solutions regarding how systems should be restructured and how computing should be done. They might also provide their feedback to real-time computing and even shape the future direction of real-time computing in various innovative ways.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00011"}, {"primary_key": "3499663", "vector": [], "sparse_vector": [], "title": "MC-SDN: Supporting Mixed-Criticality Scheduling on Switched-Ethernet Using Software-Defined Networking.", "authors": ["<PERSON><PERSON><PERSON>", "Taejune Park", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Seungwon Shin", "Insik Shin"], "summary": "In this paper, we present the first approach to support mixed-criticality (MC) flow scheduling on switched Ethernet networks leveraging an emerging network architecture, Software-Defined Networking (SDN). Though SDN provides flexible and programmatic ways to control packet forwarding and scheduling, it yet raises several challenges to enable real-time MC flow scheduling on SDN, including i) how to handle (i.e., drop or reprioritize) out-of-mode packets in the middle of the network when the criticality mode changes, and ii) how the mode change affects end-to-end transmission delays. Addressing such challenges, we develop MC-SDN that supports real-time MC flow scheduling by extending SDN-enabled switches and OpenFlow protocols. It manages and schedules MC packets in different ways depending on the system criticality mode. To this end, we carefully design the mode change protocol that provides analytic mode change delay bound, and then resolve implementation issues for system architecture. For evaluation, we implement a prototype of MC-SDN on top of Open vSwitch, and integrate it into a real world network testbed as well as a 1/10 autonomous vehicle. Our extensive evaluations with the network testbed and vehicle deployment show that MC-SDN supports MC flow scheduling with minimal delays on forwarding rule updates and it brings a significant improvement in safety in a real-world application scenario.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00045"}, {"primary_key": "3499664", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: From Logical Time Scheduling to Real-Time Scheduling.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Scheduling is a central yet challenging problem in real-time embedded systems. The Clock Constraint Specification Language (CCSL) provides a formalism to specify logical constraints of events in real-time embedded systems. A prerequisite for the events is that they must be schedulable under constraints. That is, there must be a schedule which controls all events to occur infinitely often. Schedulability analysis of CCSL raises important algorithmic problems such as computational complexity and design of efficient decision procedures. In this work, we compare the scheduling problems of CCSL specifications to the real-time scheduling problem. We show how to encode a simple task model in CCSL and discuss some benefits and differences compared to more classical scheduling strategies.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00025"}, {"primary_key": "3499665", "vector": [], "sparse_vector": [], "title": "Scheduling Multi-periodic Mixed-Criticality DAGs on Multi-core Architectures.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Thanks to Mixed-Criticality (MC) scheduling, high and low-criticality tasks can share the same execution platform, improving considerably the usage of computation resources. Even if the execution platform is shared with low-criticality tasks, deadlines of high-criticality tasks must be respected. This is usually enforced thanks to operational modes of the system: if necessary, a high-criticality execution mode allocates more time to high-criticality tasks at the expense of low-criticality tasks' execution. Nonetheless, most MC scheduling policies in the literature have only considered independent task sets. For safety-critical real-time systems, this is a strong limitation: models used to describe reactive safety-critical software often consider dependencies among tasks or jobs. In this paper, we define a meta-heuristic to schedule multiprocessor systems composed of multi-periodic Directed Acyclic Graphs of MC tasks. This meta-heuristic computes the scheduling of the system in the high-criticality mode first. The computation of the low-criticality scheduling respects a condition on high-criticality tasks' jobs, ensuring that high-criticality tasks never miss their deadlines. An efficient implementation of this meta-heuristic is presented. In high-criticality mode, high-criticality tasks are scheduled as late as possible. Then two global scheduling tables are produced, one per criticality mode. Experimental results demonstrate our method outperforms approaches of the literature in terms of acceptance rate for randomly generated systems.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00042"}, {"primary_key": "3499666", "vector": [], "sparse_vector": [], "title": "Partitioned Real-Time NAND Flash Storage.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper addresses the problem of guaranteeing performance and predictability of NAND flash memory in a real-time storage system. Our approach implements a new flash translation layer scheme that exploits internal parallelism within solid state storage devices. We describe the Partitioned Real-Time Flash Translation Layer (PaRT-FTL), which splits a set of flash chips into separate read and write sets. This ensures reads and writes to separate chips proceed in parallel. However, PaRT-FTL is also able to rebuild the data for a read request from a flash chip that is busy servicing a write request or performing garbage collection. Consequently, reads are never blocked by writes or storage space reclamation. PaRT-FTL is compared to previous real-time approaches including scheduling, over-provisioning and partial garbage collection. We show that by isolating read and write requests using encoding techniques, PaRT-FTL provides better latency guarantees for real-time applications.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00036"}, {"primary_key": "3499667", "vector": [], "sparse_vector": [], "title": "The SRP Resource Sharing Protocol for Self-Suspending Tasks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Motivated by the increasingly wide adoption of realtime workload with self-suspending behaviors, and the relevance of mechanisms to handle mutually-exclusive shared resources, this paper takes a new look at locking protocols for self-suspending tasks under uniprocessor fixed-priority scheduling. Pitfalls when integrating the widely-adopted Stack Resource Policy (SRP) with self-suspending tasks are firstly illustrated, and then a new finegrained SRP analysis is presented. Next, a new locking protocol, named SRP-SS, is proposed to overcome the limitations of the original SRP. The SRP-SS is a generalization of the SRP to cope with the specificities of self-suspending tasks. It therefore reduces to the SRP under some configurations and hence theoretically dominates the SRP. It also ensures backward compatibility for applications developed specifically for the SRP. The SRP-SS comes with its own schedulability analysis and configuration algorithm. The performances of the SRP and SRP-SS are finally studied by means of large-scale schedulability experiments.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00051"}, {"primary_key": "3499668", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Lock-Based Software Transactional Memory for Real-Time Systems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We propose a method for designing software transactional memory that relies on the use of locking protocols to ensure that transactions will never be forced to retry. We discuss our approaches to implementing this method and tunable parameters that may be able to improve schedulability on an application-specific basis.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00026"}, {"primary_key": "3499669", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Enhanced Energy-Aware Standby-Sparing Techniques for Fixed-Priority Hard Real-Time Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "For real-time computing systems, energy efficiency and reliability are two primary design concerns. In this research work, we study the problem of enhanced energy-aware standby-sparing for fixed-priority (FP) hard real-time systems under reliability requirement. The standby-sparing system adopts a primary processor and a spare processor to provide fault tolerance for both permanent and transient faults. In order to keep the energy consumption for such kind of systems under control, we explore enhanced fixed-priority scheduling schemes to minimize the overlapped concurrent executions of the workloads on the primary processor and on the spare processor, enabling energy savings. Moreover, efficient online scheduling techniques are under development to boost the energy savings during runtime while preserving the system reliability.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00031"}, {"primary_key": "3499670", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Real-Time Modeling for Intrusion Detection in Automotive Controller Area Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Security of vehicular networks has often been an afterthought since they are designed traditionally to be a closed system. An attack could lead to catastrophic effect which may include loss of human life or severe injury to the driver and passengers of the vehicle. In this paper, we propose a novel algorithm to extract the real-time model of the controller area network (CAN) and develop a specification-based intrusion detection system (IDS) using anomaly-based supervised learning with the real-time model as input. We evaluate IDS performance with real CAN logs collected from a sedan car.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00030"}, {"primary_key": "3499671", "vector": [], "sparse_vector": [], "title": "Work in Progress: Combining Real Time and Multithreading.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The existing sporadic task model is inadequate for real-time systems to take advantage of Simultaneous Multithreading (SMT), which has been shown to improve performance in many areas of computing, but has seen little application to real-time systems. A new family of task models, collectively referred to as SMART, is introduced. SMART models allow for combining SMT and real time by accounting for the variable task execution costs caused by SMT.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00024"}, {"primary_key": "3499672", "vector": [], "sparse_vector": [], "title": "Schedulability Analysis of Adaptive Variable-Rate Tasks with Dynamic Switching Speeds.", "authors": ["<PERSON>", "Ye<PERSON> Zhao", "Haibo Zeng"], "summary": "In real-time embedded systems certain tasks are activated according to a rotation source, such as angular tasks in engine control unit triggered whenever the engine crankshaft reaches a specific angular position. To reduce the workload at high speeds, these tasks also adopt different implementations at different rotation speed intervals. However, the current studies limit to the case that the switching speeds at which task implementations should change are configured at design time. In this paper, we propose to study the task model where switching speeds are dynamically adjusted. We develop schedulability analysis techniques for such systems, including a new digraph-based task model to safely approximate the workload from software tasks triggered at predefined rotation angles. Experiments on synthetic task systems demonstrate that the proposed approach provides substantial benefits on system schedulability.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00054"}, {"primary_key": "3499673", "vector": [], "sparse_vector": [], "title": "Automatic Trace Generation for Signal Temporal Logic.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we present a novel technique to automatically generate satisfying and violating traces for a Signal Temporal Logic (STL) formula. STL is a logic whose formulas are interpreted over real-valued signals that evolve over dense time, which is a natural setting for Cyber-Physical Systems (CPS) applications. However, the process of developing appropriate STL requirements can be difficult and error prone. In this work, we provide a method to assist designers in the development of STL requirements for CPS applications. Our technique automatically encodes a given STL formula into a satisfiability modulo theory (SMT) formula in an appropriate theory. Satisfying and violating traces for the STL specification can be obtained by solving satisfiability problems on the encoded SMT formulas. In particular, models returned by the SMT solver correspond to traces that satisfy/violate the STL formula, thus offering a window into the types of behaviors specified by the formula. We demonstrate how the method can be used to debug problems with STL requirements, and we evaluate the performance of the method on a collection of requirements developed for CPS applications.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00038"}, {"primary_key": "3499674", "vector": [], "sparse_vector": [], "title": "Exploiting Locality for the Performance Analysis of Shared Memory Systems in MPSoCs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The integration trend and increased required computing power is driving the advent of common embedded consumer devices like MPSoCs platforms in the safety critical domain. MPSoCs often feature a shared tightly-coupled memory system where a careful management of data storage and transfers is a key enabler for performance. However, providing real-time guarantees for these platforms is extremely challenging as they rely on exploiting data locality to improve average latencies in shared-memory architectures. This effect is often disregarded by existing real-time analysis approaches which furthermore often focus solely on a single component of the memory system. In this paper, we propose a framework for the timing analysis of shared memory systems composed of on-chip scratchpad memories, off-chip DRAMs and DMA engines. The analysis captures the effect on the performance of the system of the locality of accesses, their interleaving and granularity.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00050"}, {"primary_key": "3499675", "vector": [], "sparse_vector": [], "title": "Optimizing Network Calculus for Switched Ethernet Network with Deficit Round Robin.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Avionics Full Duplex switched Ethernet (AFDX) is the de facto standard for the transmission of critical avionics flows. It is a specific switched Ethernet solution based on First-in First-out (FIFO) scheduling. Worst-case traversal time (WCTT) analysis is mandatory for such flows, since timing constraints have to be guaranteed. A classical approach in this context is Network Calculus (NC). However, NC introduces some pessimism in the WCTT computation. Moreover, the worst-case often corresponds to very rare scenarios. Thus, the network architecture is most of the time lightly loaded. Typically, less than 10 % of the available bandwidth is used for the transmission of avionics lows on an AFDX network embedded in an aircraft. One solution to improve the utilization of the network is to introduce Quality of Service (QoS) mechanisms. Deficit Round Robin (DRR) is such a mechanism and it is envisioned for future avionics networks. A WCTT analysis has been proposed for DRR. It is based on NC. It doesn't make any assumption on the scheduling of flows by end systems. The first contribution of this paper is to identify sources of pessimism of this approach and to propose an improved solution which removes part of this pessimism. The second contribution is to show how the scheduling of flows can be integrated in this optimized DRR approach, thanks to offsets. An evaluation on a realistic case study shows that both contributions bring significantly tighter bounds on worst-case latencies.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00046"}, {"primary_key": "3499676", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Precise Scheduling of Mixed-Criticality Tasks by Varying Processor Speed.", "authors": ["Sai Sr<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The traditional mixed-criticality (MC) model does not allow less critical tasks to execute during an event of the error and exception. Recently, the imprecise MC (IMC) model has been proposed where, even for exceptional events, less critical tasks also receive some amount of (degraded) service, e.g., a task overruns its execution demand. In this work, we present our ongoing effort to extend the IMC model to the precise scheduling of tasks and integrate with the dynamic voltage and frequency scaling (DVFS) scheme to enable energy minimization. Precise scheduling of MC systems is highly challenging because of its requirement to simultaneously guarantee the timing correctness of all tasks under both pessimistic and less pessimistic assumptions. We propose an utilization-based schedulability test and sufficient schedulability conditions for such systems under earliest deadline first with virtual deadline (EDF-VD) scheduling policy. For this unified model, we present a quantitative study in the forms of speedup bound and approximation ratio. Finally, both theoretical and experimental analysis will be conducted to prove the correctness of our algorithm and to demonstrate its effectiveness.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00033"}, {"primary_key": "3499677", "vector": [], "sparse_vector": [], "title": "TDMH-MAC: Real-Time and Multi-hop in the Same Wireless MAC.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Supporting real-time communications over Wireless networks (WSNs) is a tough challenge, due to packet collisions and the non-determinism of common channel access schemes like CSMA/CA. Real-time WSN communication is even more problematic in the general case of multi-hop mesh networks. For this reason, many real-time WSN solutions are limited to simple topologies, such as star networks. We propose a real-time multi-hop WSN MAC protocol built atop the IEEE 802.15.4 physical layer. By relying on precise clock synchronization and constructive interference-based flooding, the proposed MAC builds a centralized TDMA schedule, supporting multi-hop mesh networks. The real-time multi-hop communication model is connection-oriented, using guaranteed time slots, ad enables point-to-point communications also with redundant paths. The protocol has been implemented in simulation using OMNeT++, and the performance has been verified in a real-world deployment using Wandstem WSN nodes.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00044"}, {"primary_key": "3499678", "vector": [], "sparse_vector": [], "title": "BUNDLEP: Prioritizing Conflict Free Regions in Multi-threaded Programs to Improve Cache Reuse.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In \"BUNDLE: Real-Time Multi-Threaded Scheduling to Reduce Cache Contention\", <PERSON><PERSON> and <PERSON> propose a scheduling mechanism and combined worst-case execution time calculation method that treats the instruction cache as a beneficial resource shared between threads. Object analysis produces a worst-case execution time bound and separates code segments into regions. Threads are dynamically placed in bundles associated with regions at run time by the BUNDLE scheduling algorithm where they benefit from shared cache values. In the evaluation of the previous work, tasks were created with a predetermined worst-case execution time path through the control flow graph. Apriori knowledge of the worst-case path is an impractical restriction on any analysis. At the time, the only other solution available was an all-paths search of the graph, which is an equally impractical approach due to its complexity. The primary focus of this work is to build upon BUNDLE, expanding its applicability beyond a proof of concept. We present a complete worst-case execution time calculation method that includes thread level context switch costs, operating on real programs, with representative architecture parameters, and compare our results to those produced by <PERSON><PERSON><PERSON>'s state of the art method. To these ends, we propose a modification to the BUNDLE scheduling algorithm called BUNDLEP. Bundles are assigned priorities that enforce an ordered flow of threads through the control flow graph - avoiding the need for multiple all-paths searches through the graph. In many cases, our evaluation shows a run-time and analytical benefit for BUNLDEP compared to serialized thread execution and state of the art WCET analysis.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00048"}, {"primary_key": "3499679", "vector": [], "sparse_vector": [], "title": "Reservation-Based Federated Scheduling for Parallel Real-Time Tasks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Multicore systems are increasingly utilized in real-time systems in order to address the high computational demands. To fully exploit the advantages of multicore processing, possible intra-task parallelism modeled as a directed acyclic graph (DAG) must be utilized efficiently. This paper considers the scheduling problem for parallel real-time tasks with constrained and arbitrary deadlines. In contrast to prior work in this area, it generalizes federated scheduling and proposes a novel reservation-based approach. Namely, we propose a reservation-based federated scheduling strategy that reduces the problem of scheduling arbitrary-deadline DAG task sets to the problem of scheduling arbitrary-deadline sequential task sets by allocating reservation servers. We provide the general reservation design for sporadic parallel tasks, such that any scheduling algorithm and analysis for sequential tasks with arbitrary deadlines can be used to execute the allocated reservation servers of parallel tasks. Moreover, the proposed reservation-based federated scheduling algorithms provide constant speedup factors with respect to any optimal scheduler for arbitrary-deadline DAG task sets. We demonstrate via numerical and empirical experiments that our algorithms are competitive with the state of the art.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00061"}, {"primary_key": "3499680", "vector": [], "sparse_vector": [], "title": "An Optimal Semi-Partitioned Scheduler Assuming Arbitrary Affinity Masks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Modern operating systems allow task migrations to be restricted by specifying per-task processor affinity masks. Such a mask specifies the set of processor cores upon which a task can be scheduled. In this paper, a semi-partitioned scheduler, AM-Red (affinity mask reduction), is presented for scheduling implicit-deadline sporadic tasks with arbitrary affinity masks on an identical multiprocessor. AM-Red is obtained by applying an affinity-mask-reduction method that produces affinities in accordance with those specified, without compromising feasibility, but with only a linear number of migrating tasks. It functions by employing a tunable frame of size |F|. For any choice of |F|, AM-Red is soft-real-time optimal, with tardiness bounded by |F|, but the frequency of task migrations is proportional to |F|. If |F| divides all task periods, then AM-Red is also hard-real-time-optimal (tardiness is zero). AM-Red is the first optimal scheduler proposed for arbitrary affinity masks without future knowledge of all job releases. Experiments are presented that show that AM-Red is implementable with low overhead and yields reasonable tardiness and task-migration frequency.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00055"}, {"primary_key": "3499681", "vector": [], "sparse_vector": [], "title": "bCharge: Data-Driven Real-Time Charging Scheduling for Large-Scale Electric Bus Fleets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We are witnessing a rapid growth of electrified vehicles because of the ever-increasing concerns over urban air quality and energy security. Compared with other electric vehicles, electric buses have not yet been prevailingly adopted worldwide due to the high owning and operating costs, long charging time, and the uneven distribution of charging facilities. Moreover, the highly dynamic environment factors such as the unpredictable traffic congestions, different passenger demands, and even changing weather, can significantly affect electric bus charging efficiency and potentially hinder further development of large-scale electric bus fleets. To deal with these issues, in this paper, we first analyze a real-world dataset including massive data from 16,359 electric buses, 1,400 bus lines and 5,562 bus stops, which is obtained from the Chinese city Shenzhen, who has the first and the largest full electric bus network for public transit. Then we investigate the electric bus network to understand its operating and charging patterns, and further verify the feasibility and necessity of a real-time charging scheduling. With such understanding, we design bCharge, a real-time charging scheduling system based on Markov Decision Process to reduce the overall charging and operating costs for city-scale electric bus fleets, taking the time-variant electricity pricing into account. To show the effectiveness of bCharge, we implement it with the real-world streaming dataset from Shenzhen, which includes GPS data of the electric bus fleet, the bus lines and stops data, coupled with the 376 electric bus charging stations data. The evaluation results show that bCharge can dramatically reduce the charging cost by 23.7% and 12.8% electricity usage simultaneously.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00015"}, {"primary_key": "3499682", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Preference-Oriented Scheduling in Multiprocessor Real-Time Systems.", "authors": ["<PERSON>", "Da<PERSON> Zhu", "<PERSON><PERSON>"], "summary": "For a set of real-time tasks that have mixed preference of being executed at early or late times before their deadlines, we have recently studied both earliest-deadline based and fixed-priority preference-oriented (PO) scheduling algorithms for uniprocessor systems. In this work, focusing on multiprocessor real-time systems, we study the foundational guidelines to design partition-based PO scheduling algorithms for tasks with mixed preference requirements. In particular, through a concrete example, we illustrate that the harmonicity of tasks' periods should be incorporated when making scheduling decisions in addition to their execution preferences to obtain favorable schedules that better fulfill tasks' preference requirements. Based on such guidelines, we design a period-aware preference-oriented (PAPO) partitioned scheduling algorithm and discuss several variations by considering harmonicity as well as utilization of tasks.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00023"}, {"primary_key": "3499683", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Making Machine Learning Real-Time Predictable.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Machine learning (ML) on edge computing devices is becoming popular in the industry as a means to make control systems more intelligent and autonomous. The new trend is to utilize embedded edge devices, as they boast higher computational power and larger memories than before, to perform ML tasks that had previously been limited to cloud-hosted deployments. In this work, we assess the real-time predictability and consider data privacy concerns by comparing traditional cloud services with edge-based ones for certain data analytics tasks. We identify the subset of ML problems appropriate for edge devices by investigating if they result in real-time predictable services for a set of widely used ML libraries. We specifically enhance the Caffe library to make it more suitable for real-time predictability. We then deploy ML models with high accuracy scores on an embedded system, exposing it to industry sensor data from the field, to demonstrates its efficacy and suitability for real-time processing.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00029"}, {"primary_key": "3499684", "vector": [], "sparse_vector": [], "title": "Making OpenVX Really &quot;Real Time&quot;.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Yang", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "OpenVX is a recently ratified standard that was expressly proposed to facilitate the design of computer-vision (CV) applications used in real-time embedded systems. Despite its real-time focus, OpenVX presents several challenges when validating real-time constraints. Many of these challenges are rooted in the fact that OpenVX only implicitly defines any notion of a schedulable entity. Under OpenVX, CV applications are specified in the form of processing graphs that are inherently considered to execute monolithically end-to-end. This monolithic execution hinders parallelism and can lead to significant processing-capacity loss. Prior work partially addressed this problem by treating graph nodes as schedulable entities, but under OpenVX, these nodes represent rather coarse-grained CV functions, so the available parallelism that can be obtained in this way is quite limited. In this paper, a much more fine-grained approach for scheduling OpenVX graphs is proposed. This approach was designed to enable additional parallelism and to eliminate schedulability-related processing-capacity loss that arises when programs execute on both CPUs and graphics processing units (GPUs). Response-time analysis for this new approach is presented and its efficacy is evaluated via a case study involving an actual CV application.", "published": "2018-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2018.00018"}]