// 通用 Prompt 选择项
// 每个 prompt 包含：标题、描述、prompt 内容

export interface PromptItem {
  title: string;
  description: string;
  prompt: string;
}

export const promptItems: PromptItem[] = [
  {
    title: '单篇文章详解🔍',
    description: '详细的结构化解读，仅支持单篇来源是 arxiv 的文章（或 ACL 系列文章），gemini 效果更佳。',
    prompt: `帮我详细解释一下这篇文章，包括以下部分
    1. 论文核心概念🔍（对论文核心 insight 的简要总结）
    2. 论文内名词解释🧐（对论文中出现多次，或者比较重要的名词的详细解释）
    3. 论文方法🔬
    3.1 过去方法的问题 （顺便引出方法的 motivation）
    3.2 整体框架（整个论文的方法部分的核心流程的超详细说明，需要保证通过说明可以完整复现出整个方法，包括细节，公式流程，变量说明）
    3.3 核心难点解析 （将方法中比较复杂的部分或者比较关键的部分在这里进行更加直白易懂的解释）
    4. 实验结果与分析📊
    4.1 实验设置（数据集，模型，指标，超参数设置，对比方法等的内容）
    4.2 实验结果（该方法指标提升了多少，以及其他相关效果或正面的评价）
    5. 结论💎
    5.1 论文的贡献
    5.2 论文的限制（论文在哪些方面有问题）
    5.3 未来的方向（未来可能的发展方向）

    注意：
    1. 在最开头加上一个一级标题作为文章的简单标记，让我在后续回顾的时候能根据标记快速回想起这篇文章的特点，例如 XXX（方法英文缩写）：XXX（论文中文名称）
    2. 请你对 x. 这类使用二级标题，对 x.x 使用三级标题，除了上述说明外其他所有内容都不要使用标题加粗，但可以使用序号进行罗列。
    3. 注意对于较长的公式你需要将其分为多行公式，这样更清晰，方便我理解(即公式内容不变，但从某个运算符号进行切分并展示为多行)
    4. 对单篇文章解析的时候，就不需要使用 [x] 这种代称了，只需使用论文的缩写（如果有），或者 '该论文' 来指代即可
    5. 我是一个刚入门的小白，所以你需要尽可能详细的解释关键部分`
  },
//   {
//     title: '多篇文章综合对比分析',
//     description: '对多篇论文进行横向对比，分析各自的优缺点、创新点及适用场景，帮助用户快速了解领域发展。',
//     prompt: `请对这些论文进行综合对比分析，内容包括：\n1. 各论文的主要研究内容与方法\n2. 各自的创新点与不足\n3. 适用场景与局限性\n4. 未来发展趋势\n请用表格或分点方式清晰展示对比结果。`
//   },
//   {
//     title: '趋势分析与研究热点总结',
//     description: '基于多篇论文，分析该领域的最新研究趋势、热点问题和未来可能的发展方向。',
//     prompt: `请基于这些论文，分析该领域的最新研究趋势和热点问题，包括：\n1. 当前主流研究方向\n2. 近期出现的新方法或新问题\n3. 未来可能的发展趋势\n请结合论文内容，给出简明扼要的总结。`
//   },
//   {
//     title: '论文摘要快速生成',
//     description: '根据论文内容，快速生成简明扼要的摘要，适合快速了解论文核心内容。',
//     prompt: `请根据论文内容，生成一段简明扼要的摘要，突出核心贡献和创新点，字数控制在200字以内。`
//   },
//   {
//     title: '论文优缺点分析',
//     description: '分析论文的主要优点和不足，帮助用户快速判断论文价值。',
//     prompt: `请简要分析这篇论文的主要优点和不足，内容包括：\n1. 优点（创新性、实用性、实验充分性等）\n2. 不足（局限性、实验设计、实际应用等）`
//   }
];
