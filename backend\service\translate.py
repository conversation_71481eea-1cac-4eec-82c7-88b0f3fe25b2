import os
from pdb import run
import sys

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path: sys.path.append(ROOT_DIR)


from utils.config import config

import hashlib
import time
import requests
from typing import Optional
import aiohttp
import asyncio

import re
from utils.llm import BaseLLM, ModelSelector, BasePrompt

llm_translate = BaseLLM(**ModelSelector.qwen25_32b)


async def fast_translate(text: str, max_length: int = 200) -> str:
    '''
    快速翻译长文本：将长文本切分为多个小段并发翻译，最后拼接返回
    Args:
        text: 待翻译文本
        max_length: 每段最大长度，默认200字符
    Returns:
        str: 翻译后的完整文本
    '''
    # 1. 删除多余空格以及换行
    text = text.strip().replace('\n', ' ').replace('\r', ' ')
    # 1. 按句号、换行、分号、逗号等切分并保留标点符号
    split_pattern = r'([。！？!?.；;])'  # 中文/英文句号、问号、感叹号、分号、换行
    raw_segments = re.split(split_pattern, text)
    # 将标点符号与前面的文本合并
    raw_segments = [raw_segments[i] + (raw_segments[i+1] if i+1 < len(raw_segments) else '') for i in range(0, len(raw_segments), 2)]
    segments = []
    buf = ''
    for seg in raw_segments:
        seg = seg.strip()
        if not seg:
            continue
        # 尽量合并到 max_length
        if len(buf) + len(seg) + 1 <= max_length:
            buf = buf + (' ' if buf else '') + seg
        else:
            if buf:
                segments.append(buf)
            buf = seg
    if buf:
        segments.append(buf)
    if not segments:
        return ''
    # 2. 并发翻译
    responses = await llm_translate.ask_multi_async(segments, system_prompt=BasePrompt.translate_base)
    # 3. 拼接结果
    return '\n'.join(responses)

class YoudaoTranslator:
    def __init__(self):
        self.app_key = config.get('YOUDAO_APP_KEY')
        self.app_secret = config.get('YOUDAO_APP_SECRET')
        self.base_url = "https://openapi.youdao.com/api"

    def _truncate(self, q: str) -> str:
        """截取文本"""
        length = len(q)
        if length <= 20:
            return q
        return q[:10] + str(length) + q[length-10:]

    async def translate_async(self, text: str, from_lang: str = 'zh-CHS', to_lang: str = 'en') -> Optional[str]:
        """
        异步翻译文本
        Args:
            text: 要翻译的文本
            from_lang: 源语言
            to_lang: 目标语言
        Returns:
            翻译后的文本，如果翻译失败返回 None
        """
        try:
            salt = str(int(time.time() * 1000))
            curtime = str(int(time.time()))
            
            # 计算签名
            sign_str = self.app_key + self._truncate(text) + salt + curtime + self.app_secret
            sign = hashlib.sha256(sign_str.encode()).hexdigest()
            
            # 构建请求参数
            params = {
                'q': text,
                'appKey': self.app_key,
                'salt': salt,
                'from': from_lang,
                'to': to_lang,
                'sign': sign,
                'signType': 'v3',
                'curtime': curtime,
            }
            
            # 使用异步请求
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, data=params) as response:
                    result = await response.json()
            
            # 检查响应
            if result.get('errorCode') == '0':
                return result.get('translation', [None])[0]
            else:
                print(f"翻译失败: {result.get('errorCode')} - {result.get('msg')}")
                return None
                
        except Exception as e:
            print(f"翻译出错: {str(e)}")
            return None 

    def translate(self, text: str, from_lang: str = 'zh-CHS', to_lang: str = 'en') -> Optional[str]:
        """
        翻译文本
        Args:
            text: 要翻译的文本
            from_lang: 源语言
            to_lang: 目标语言
        Returns:
            翻译后的文本，如果翻译失败返回 None
        """
        try:
            salt = str(int(time.time() * 1000))
            curtime = str(int(time.time()))
            
            # 计算签名
            sign_str = self.app_key + self._truncate(text) + salt + curtime + self.app_secret
            sign = hashlib.sha256(sign_str.encode()).hexdigest()
            
            # 构建请求参数
            params = {
                'q': text,
                'appKey': self.app_key,
                'salt': salt,
                'from': from_lang,
                'to': to_lang,
                'sign': sign,
                'signType': 'v3',
                'curtime': curtime,
            }
            
            # 发送请求
            response = requests.post(self.base_url, data=params)
            result = response.json()
            
            # 检查响应
            if result.get('errorCode') == '0':
                return result.get('translation', [None])[0]
            else:
                print(f"翻译失败: {result.get('errorCode')} - {result.get('msg')}")
                return None
                
        except Exception as e:
            print(f"翻译出错: {str(e)}")
            return None 

    async def translate_batch_async(self, texts: list[str], from_lang: str = 'zh-CHS', to_lang: str = 'en') -> list[Optional[str]]:
        """
        异步批量翻译文本
        Args:
            texts: 要翻译的文本列表
            from_lang: 源语言
            to_lang: 目标语言
        Returns:
            翻译后的文本列表，与输入顺序一致，如果翻译失败对应位置返回None
        """
        tasks = [self.translate_async(text, from_lang, to_lang) for text in texts]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常情况
        final_results = []
        for res in results:
            if isinstance(res, Exception):
                print(f"批量翻译出错: {str(res)}")
                final_results.append(None)
            else:
                final_results.append(res)
        return final_results



if __name__ == '__main__':
    abstract = '''
    Retrieval-Augmented Generation (RAG) significantly enhances the performance of large language models (LLMs) in 
    downstream tasks by integrating external knowledge. To facilitate researchers in deploying RAG systems, 
    various RAG toolkits have been introduced. However, many existing RAG toolkits lack support for knowledge 
    adaptation tailored to specific application scenarios. To address this limitation, we propose UltraRAG, 
    a RAG toolkit that automates knowledge adaptation throughout the entire workflow, 
    from data construction and training to evaluation, while ensuring ease of use. 
    UltraRAG features a user-friendly WebUI that streamlines the RAG process, 
    allowing users to build and optimize systems without coding expertise. 
    It supports multimodal input and provides comprehensive tools for managing the knowledge base. 
    With its highly modular architecture, UltraRAG delivers an end-to-end development solution, 
    enabling seamless knowledge adaptation across diverse user scenarios. The code, demonstration videos, 
    and installable package for UltraRAG are publicly available at https://github.com/OpenBMB/UltraRAG.
    '''
    out = asyncio.run(fast_translate(abstract))
    print(out)