[{"primary_key": "4377134", "vector": [], "sparse_vector": [], "title": "POSTER: In the Net of the Spider: Measuring the Anonymity-Impact of Network-level Adversaries Against Tor.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recently, the live-monitor MATor for formally analyzing user anonymity within the Tor network has been proposed (CCS'14). However, this monitor only considers adversaries that compromise part of the Tor network itself, not Internet infrastructural adversaries. In this work we present a formal technique for analyzing Tor against malicious or overly curious network infrastructure.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810115"}, {"primary_key": "4377135", "vector": [], "sparse_vector": [], "title": "POSTER: WinOver Enterprise Dark Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sutapa <PERSON>", "<PERSON><PERSON>"], "summary": "Any persistent untagged, untapped and unclassified data can be termed as dark data. It has two common traits: first, it is not possible to determine its worth, and second, in most of the scenarios it is inadequately protected. Previous work and existing solutions are restricted to cater single node system. Moreover, they perform specialized processing of selected content, for example, logs. Further, there is total negligence of stakeholders and minimal focus on the data getting generated within the enterprise. From the perspective of an enterprise it is important to understand the distribution, nature and worth of dark data, as it helps in choosing right security controls, insurance or steps needed to pre-process a system before discarding it. In this paper we demonstrate a distributed system, called File WinOver, for File Lifecycle Management (FLM). The solution operates in a distributed environment where it identifies the dormant and active files on a system, filters them as per requirement and computes their fingerprint. Moreover, the content fingerprinting is utilized to detect closed user groups. After which, it classifies the content based on configured policies, and maps them with the stakeholders. This mapping is further used for valuating the risk exposure of the file. Thus, our system helps in identifying dark data and assigns quantitative risk value.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810131"}, {"primary_key": "4377137", "vector": [], "sparse_vector": [], "title": "POSTER: Towards Compiler-Assisted Taint Tracking on the Android Runtime (ART).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Dynamic analysis and taint tracking on Android was typically implemented by instrumenting the Dalvik Virtual Machine. However, the new Android Runtime (ART) introduced in Android 5 replaces the interpreter with an on-device compiler suite. Therefore as of Android 5, the applicability of interpreter instrumentation-based approaches like TaintDroid is limited to Android versions up to 4.4 Kitkat. In this poster, we present ongoing work on re-enabling taint tracking for apps by instrumenting the Optimizing backend, used by the new ART compiler suite for code generation. As Android now compiles apps ahead-of-time from dex bytecode to platform specific native code on the device itself, an instrumented compiler provides the opportunity to emit additional instructions that enable the actual taint tracking. The result is a custom compiler that takes arbitrary app APKs and transforms them into self-taint tracking native code, executable by the Android Runtime.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810129"}, {"primary_key": "4377138", "vector": [], "sparse_vector": [], "title": "Hare Hunting in the Wild Android: A Study on the Threat of Hanging Attribute References.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Android is characterized by the complicated relations among its components and apps, through which one party interacts with the other (e.g., starting its activity) by referring to its attributes like package, activity, service, action names, authorities and permissions. Such relations can be easily compromised during a customization: e.g., when an app is removed to fit an Android version to a new device model, while references to the app remain inside that OS. This conflict between the decentralized, unregulated Android customization process and the interdependency among different Android components and apps leads to the pervasiveness of hanging attribute references (Hares), a type of vulnerabilities never investigated before. In our research, we show that popular Android devices are riddled with such flaws, which often have serious security implications: when an attribute (e.g., a package/authority/action name) is used on a device but the party defining it has been removed, a malicious app can fill the gap to acquire critical system capabilities, by simply disguising as the owner of the attribute. More specifically, we discovered in our research that on various Android devices, the malware can exploit their Hares to steal the user's voice notes, control the screen unlock process, replace Google Email's account settings activity and collect or even modify the user's contact without proper permissions. We further designed and implemented Harehunter, a new tool for automatic detection of Hares by comparing attributes defined with those used, and analyzing the references to undefined attributes to determine whether they have been protected (e.g., by signature checking). On the factory images for 97 most popular Android devices, Harehunter discovered 21557 likely Hare flaws, demonstrating the significant impacts of the problem. To mitigate the hazards, we further developed an app for detecting the attempts to exploit Hares on different devices and provide the guidance for avoiding this pitfall when building future systems.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813648"}, {"primary_key": "4377140", "vector": [], "sparse_vector": [], "title": "POSTER: Secure Chat for the Masses? User-centered Security to the Rescue.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In light of recent revelations of mass state surveillance of phone and Internet communications, many solutions now claim to provide secure messaging. This includes both a broad range of new projects and several widely adopted applications that have added security features. However, despite the demand for better solutions, there is no clear winner in the race for widespread development and deployment of messaging products. Recently, the Electronic Frontier Foundation evaluated dozens of messaging tools based on security best practices, and publicized the results via the Secure Messaging Scorecard. Our goal is to expand the scorecard by evaluating messaging tools on a range of usefulness (utility and usability) attributes.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810126"}, {"primary_key": "4377141", "vector": [], "sparse_vector": [], "title": "Imperfect Forward Secrecy: How <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Fails in Practice.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Santiago Zanella-Béguelin", "<PERSON>"], "summary": "We investigate the security of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> key exchange as used in popular Internet protocols and find it to be less secure than widely believed. First, we present Logjam, a novel flaw in TLS that lets a man-in-the-middle downgrade connections to \"export-grade\" <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. To carry out this attack, we implement the number field sieve discrete log algorithm. After a week-long precomputation for a specified 512-bit group, we can compute arbitrary discrete logs in that group in about a minute. We find that 82% of vulnerable servers use a single 512-bit group, allowing us to compromise connections to 7% of Alexa Top Million HTTPS sites. In response, major browsers are being changed to reject short groups. We go on to consider <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> with 768- and 1024-bit groups. We estimate that even in the 1024-bit case, the computations are plausible given nation-state resources. A small number of fixed or standardized groups are used by millions of servers; performing precomputation for a single 1024-bit group would allow passive eavesdropping on 18% of popular HTTPS sites, and a second group would allow decryption of traffic to 66% of IPsec VPNs and 26% of SSH servers. A close reading of published NSA leaks shows that the agency's attacks on VPNs are consistent with having achieved such a break. We conclude that moving to stronger key exchange methods should be a priority for the Internet community.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813707"}, {"primary_key": "4377142", "vector": [], "sparse_vector": [], "title": "Automating Fast and Secure Translations from Type-I to Type-III Pairing Schemes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Pairing-based cryptography has exploded over the last decade, as this algebraic setting offers good functionality and efficiency. However, there is a huge security gap between how schemes are usually analyzed in the academic literature and how they are typically implemented. The issue at play is that there exist multiple types of pairings: Type-I called \"symmetric\" is typically how schemes are presented and proven secure in the literature, because it is simpler and the complexity assumptions can be weaker; however, Type-III called \"asymmetric\" is typically the most efficient choice for an implementation in terms of bandwidth and computation time. There are two main complexities when moving from one pairing type to another. First, the change in algebraic setting invalidates the original security proof. Second, there are usually multiple (possibly thousands) of ways to translate from a Type-I to a Type-III scheme, and the \"best\" translation may depend on the application.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813601"}, {"primary_key": "4377143", "vector": [], "sparse_vector": [], "title": "SafeConfig 2015: Workshop on Automated Decision Making for Active Cyber Defense.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The 8th SafeConfig Workshop is held in Denver, Colorado USA on October 12, 2015 and being run with the conjunction of the 22nd ACM Conference on Computer and Communications Security (CCS). The title of this year's SafeConfig is \"Automated Decision Making for Cyber Security\". Today, the use of cyber technology is evolving rapidly. The computing and networking is everywhere, public to private organizations, large enterprises to individuals, and data centers to smart phones and Internet-of-Things. The highly growing use of the Internet also leads to newly evolving security threats. The automated decision making should be able to determine the security and resiliency of networked information systems and services. The integration of security requirements, capabilities, and deployment constraints in a unified framework will enable intelligent response, automated defense, and network resiliency.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812624"}, {"primary_key": "4377150", "vector": [], "sparse_vector": [], "title": "Transparent Data Deduplication in the Cloud.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cloud storage providers such as Dropbox and Google drive heavily rely on data deduplication to save storage costs by only storing one copy of each uploaded file. Although recent studies report that whole file deduplication can achieve up to 50% storage reduction, users do not directly benefit from these savings-as there is no transparent relation between effective storage costs and the prices offered to the users. In this paper, we propose a novel storage solution, ClearBox, which allows a storage service provider to transparently attest to its customers the deduplication patterns of the (encrypted) data that it is storing. By doing so, ClearBox enables cloud users to verify the effective storage space that their data is occupying in the cloud, and consequently to check whether they qualify for benefits such as price reductions, etc. ClearBox is secure against malicious users and a rational storage provider, and ensures that files can only be accessed by their legitimate owners. We evaluate a prototype implementation of ClearBox using both Amazon S3 and Dropbox as back-end cloud storage. Our findings show that our solution works with the APIs provided by existing service providers without any modifications and achieves comparable performance to existing solutions.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813630"}, {"primary_key": "4377152", "vector": [], "sparse_vector": [], "title": "SEDA: Scalable Embedded Device Attestation.", "authors": ["<PERSON>. <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today, large numbers of smart interconnected devices provide safety and security critical services for energy grids, industrial control systems, gas and oil search robots, home/office automation, transportation, and critical infrastructure. These devices often operate in swarms -- large, dynamic, and self-organizing networks. Software integrity verification of device swarms is necessary to ensure their correct and safe operation as well as to protect them against attacks. However, current device attestation schemes assume a single prover device and do not scale to swarms. We present SEDA, the first attestation scheme for device swarms. We introduce a formal security model for swarm attestation and show security of our approach in this model. We demonstrate two proof-of-concept implementations based on two recent (remote) attestation architectures for embedded systems, including an Intel research platform. We assess performance of SEDA based on these implementations and simulations of large swarms. SEDA can efficiently attest swarms with dynamic and static topologies common in automotive, avionic, industrial control and critical infrastructures settings.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813670"}, {"primary_key": "4377154", "vector": [], "sparse_vector": [], "title": "Subversion-Resilient Signature Schemes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We provide a formal treatment of security of digital signatures against subversion attacks (SAs). Our model of subversion generalizes previous work in several directions, and is inspired by the proliferation of software attacks (e.g., malware and buffer overflow attacks), and by the recent revelations of <PERSON> about intelligence agencies trying to surreptitiously sabotage cryptographic algorithms. The main security requirement we put forward demands that a signature scheme should remain unforgeable even in the presence of an attacker applying SAs (within a certain class of allowed attacks) in a fully-adaptive and continuous fashion. Previous notions---e.g., security against algorithm-substitution attacks introduced by <PERSON><PERSON> et al. (CRYPTO '14) for symmetric encryption---were non-adaptive and non-continuous.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813635"}, {"primary_key": "4377158", "vector": [], "sparse_vector": [], "title": "Automated Proofs of Pairing-Based Cryptography.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Analyzing cryptographic constructions in the computational model, or simply verifying the correctness of security proofs, are complex and error-prone tasks. Although computer tools have significant potential to increase confidence in security proofs and to reduce the time for building these proofs, existing tools are either limited in scope, or can only be used by formal methods experts, and have a significant overhead. In effect, it has remained a challenge to design usable and intuitive tools for building and verifying cryptographic proofs, especially for more advanced fields such as pairing-based or lattice-based cryptography.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813697"}, {"primary_key": "4377159", "vector": [], "sparse_vector": [], "title": "Automated Symbolic Proofs of Observational Equivalence.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many cryptographic security definitions can be naturally formulated as observational equivalence properties. However, existing automated tools for verifying the observational equivalence of cryptographic protocols are limited: they do not handle protocols with mutable state and an unbounded number of sessions. We propose a novel definition of observational equivalence for multiset rewriting systems. We then extend the <PERSON><PERSON><PERSON> prover, based on multiset rewriting, to prove the observational equivalence of protocols with mutable state, an unbounded number of sessions, and equational theories such as <PERSON><PERSON><PERSON>-<PERSON> exponentiation. We demonstrate its effectiveness on case studies, including a stateful TPM protocol.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813662"}, {"primary_key": "4377161", "vector": [], "sparse_vector": [], "title": "Mass-surveillance without the State: Strongly Undetectable Algorithm-Substitution Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present new algorithm-substitution attacks (ASAs) on symmetric encryption that improve over prior ones in two ways. First, while prior attacks only broke a sub-class of randomized schemes having a property called coin injectivity, our attacks break ALL randomized schemes. Second, while prior attacks are stateful, ours are stateless, achieving a notion of strong undetectability that we formalize. Together this shows that ASAs are an even more dangerous and powerful mass surveillance method than previously thought. Our work serves to increase awareness about what is possible with ASAs and to spur the search for deterrents and counter-measures.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813681"}, {"primary_key": "4377165", "vector": [], "sparse_vector": [], "title": "Fraud Detection through Graph-Based User Behavior Modeling.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "How do anomalies, fraud, and spam effect our models of normal user behavior? How can we modify our models to catch fraudsters? In this tutorial we will answer these questions - connecting graph analysis tools for user behavior modeling to anomaly and fraud detection. In particular, we will focus on three data mining techniques: subgraph analysis, label propagation and latent factor models; and their application to static graphs, e.g. social networks, evolving graphs, e.g. \"who-calls-whom\" networks, and attributed graphs, e.g. the \"who-reviews-what\" graphs of Amazon and Yelp. For each of these techniques we will give an explanation of the algorithms and the intuition behind them. We will then give brief examples of recent research using the techniques to model, understand and predict normal behavior. With this intuition for how these methods are applied to graphs and user behavior, we will focus on state-of-the-art research showing how the outcomes of these methods are effected by fraud, and how they have been used to catch fraudsters.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812702"}, {"primary_key": "4377167", "vector": [], "sparse_vector": [], "title": "Timely Rerandomization for Mitigating Memory Disclosures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Address Space Layout Randomization (ASLR) can increase the cost of exploiting memory corruption vulnerabilities. One major weakness of ASLR is that it assumes the secrecy of memory addresses and is thus ineffective in the face of memory disclosure vulnerabilities. Even fine-grained variants of ASLR are shown to be ineffective against memory disclosures. In this paper we present an approach that synchronizes randomization with potential runtime disclosure. By applying rerandomization to the memory layout of a process every time it generates an output, our approach renders disclosures stale by the time they can be used by attackers to hijack control flow. We have developed a fully functioning prototype for x86_64 C programs by extending the Linux kernel, GCC, and the libc dynamic linker. The prototype operates on C source code and recompiles programs with a set of augmented information required to track pointer locations and support runtime rerandomization. Using this augmented information we dynamically relocate code segments and update code pointer values during runtime. Our evaluation on the SPEC CPU2006 benchmark, along with other applications, show that our technique incurs a very low performance overhead (2.1% on average).", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813691"}, {"primary_key": "4377168", "vector": [], "sparse_vector": [], "title": "Practicing Oblivious Access on Cloud Storage: the Gap, the Fallacy, and the New Way Forward.", "authors": ["<PERSON>", "<PERSON> Naveed", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "To understand the gap between theory and practice for oblivious cloud storage, we experimentally evaluate four representative Oblivious RAM (ORAM) designs on Amazon S3. We replay realistic application traces to these ORAMs in order to understand whether they can meet the demands of various real applications using cloud storage as a backend. We find that metrics traditionally used in the ORAM literature, e.g., bandwidth overhead, fail to capture the practical needs of those applications. With a new understanding of the desirable properties, relevant metrics, and observations about the cloud services and their applications, we propose CURIOUS, a new modular partition-based ORAM framework, and show experimentally that it is thus far the most promising approach.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813649"}, {"primary_key": "4377170", "vector": [], "sparse_vector": [], "title": "White-Box Cryptography Revisited: Space-Hard Ciphers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The need for software security in untrusted environments is ever increasing. White-box cryptography aims to ensure the security of cryptographic algorithms when the attacker has full access to their implementations. However, there is no secure white-box implementation of standard block ciphers such as DES and AES known to date: All published techniques have been practically broken. In this paper, we revisit white-box cryptography and propose a family of white-box secure block ciphers SPACE with several novel features. The design of SPACE is such that the key-extraction security in the white box reduces to the well-studied problem of key recovery for block ciphers (AES in our example) in the standard black-box setting. Moreover, to mitigate code lifting, we introduce the notion of space hardness. It measures the difficulty of compressing the white-box implementation of a cipher, and quantifies security against code lifting by the amount of code that needs to be extracted from the implementation by a white-box attacker to maintain its functionality. SPACE includes several variants with different white-box code sizes. Therefore, it is applicable to a wide range of environments and use cases. One of the variants called N-SPACE can be implemented with different code sizes while keeping the cipher itself unchanged.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813699"}, {"primary_key": "4377171", "vector": [], "sparse_vector": [], "title": "CoDisasm: Medium Scale Concatic Disassembly of Self-Modifying Binaries with Overlapping Instructions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Fighting malware involves analyzing large numbers of suspicious binary files. In this context, disassembly is a crucial task in malware analysis and reverse engineering. It involves the recovery of assembly instructions from binary machine code. Correct disassembly of binaries is necessary to produce a higher level representation of the code and thus allow the analysis to develop high-level understanding of its behavior and purpose. Nonetheless, it can be problematic in the case of malicious code, as malware writers often employ techniques to thwart correct disassembly by standard tools. In this paper, we focus on the disassembly of x86 self-modifying binaries with overlapping instructions. Current state-of-the-art disassemblers fail to interpret these two common forms of obfuscation, causing an incorrect disassembly of large parts of the input. We introduce a novel disassembly method, called concatic disassembly, that combines CONCrete path execution with stATIC disassembly. We have developed a standalone disassembler called CoDisasm that implements this approach.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813627"}, {"primary_key": "4377177", "vector": [], "sparse_vector": [], "title": "Fast Non-Malleable Commitments.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The notion of non-malleability in cryptography refers to the setting where the adversary is a man-in-the-middle (MIM) who takes part in two or more protocol executions and tries to use information obtained in one, to violate the security of another. Despite two decades of research, non-malleable commitments (NMCs) have remained too inefficient to be implemented in practice, without some sort of trusted setup. In this work, we give a fast implementation of NMC in the plain model, based on the DDH assumption being hard over elliptic curve groups. Our main theoretical result is a new NMC scheme which can be thought of as a \"high dimensional\" generalization of the one in the recent work of [GRRV14]. Central to our efficiency improvements is a method of constraining challenges sent by the receiver. This new approach enables us to obtain dramatically improved parameters over those suggested in [GRRV14]. In particular, our work opens the door to implementations based on Elliptic Curves.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813721"}, {"primary_key": "4377178", "vector": [], "sparse_vector": [], "title": "Defeating IMSI Catchers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "IMSI catching is a problem on all generations of mobile telecommunication networks, i.e., 2G (GSM, GPRS), 3G (HDSPA, EDGE, UMTS) and 4G (LTE, LTE+). Currently, the SIM card of a mobile phone has to reveal its identity over an insecure plaintext transmission, before encryption is enabled. This identifier (the IMSI) can be intercepted by adversaries that mount a passive or active attack. Such identity exposure attacks are commonly referred to as 'IMSI catching'. Since the IMSI is uniquely identifying, unauthorized exposure can lead to various location privacy attacks. We propose a solution, which essentially replaces the IMSIs with changing pseudonyms that are only identifiable by the home network of the SIM's own network provider. Consequently, these pseudonyms are unlinkable by intermediate network providers and malicious adversaries, and therefore mitigate both passive and active attacks, which we also formally verified using ProVerif. Our solution is compatible with the current specifications of the mobile standards and therefore requires no change in the infrastructure or any of the already massively deployed network equipment. The proposed method only requires limited changes to the SIM and the authentication server, both of which are under control of the user's network provider. Therefore, any individual (virtual) provider that distributes SIM cards and controls its own authentication server can deploy a more privacy friendly mobile network that is resilient against IMSI catching attacks.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813615"}, {"primary_key": "4377184", "vector": [], "sparse_vector": [], "title": "(Un)linkable Pseudonyms for Governmental Databases.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "When data maintained in a decentralized fashion needs to be synchronized or exchanged between different databases, related data sets usually get associated with a unique identifier. While this approach facilitates cross-domain data exchange, it also comes with inherent drawbacks in terms of controllability. As data records can easily be linked, no central authority can limit or control the information flow. Worse, when records contain sensitive personal data, as is for instance the case in national social security systems, such linkability poses a massive security and privacy threat. An alternative approach is to use domain-specific pseudonyms, where only a central authority knows the cross-domain relation between the pseudonyms. However, current solutions require the central authority to be a fully trusted party, as otherwise it can provide false conversions and exploit the data it learns from the requests. We propose an (un)linkable pseudonym system that overcomes those limitations, and enables controlled yet privacy-friendly exchange of distributed data. We prove our protocol secure in the UC framework and provide an efficient instantiation based on discrete-logarithm related assumptions.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813658"}, {"primary_key": "4377185", "vector": [], "sparse_vector": [], "title": "Optimal Distributed Password Verification.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a highly efficient cryptographic protocol to protect user passwords against server compromise by distributing the capability to verify passwords over multiple servers. Password verification is a single-round protocol and requires from each server only one exponentiation in a prime-order group. In spite of its simplicity, our scheme boasts security against dynamic and transient corruptions, meaning that servers can be corrupted at any time and can recover from corruption by going through a non-interactive key refresh procedure. The users' passwords remain secure against offline dictionary attacks as long as not all servers are corrupted within the same time period between refreshes. The only currently known scheme to achieve such strong security guarantees incurs the considerable cost of several hundred exponentiations per server. We prove our scheme secure in the universal composability model, which is well-known to offer important benefits for password-based primitives, under the gap one-more Di<PERSON><PERSON>-<PERSON> assumption in the random-oracle model. Server initialization and refresh must take place in a trusted execution environment. Initialization additionally requires a secure message to each server, but the refresh procedure is non-interactive. We show that these requirements are easily met in practice by providing an example deployment architecture.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813722"}, {"primary_key": "4377189", "vector": [], "sparse_vector": [], "title": "Leakage-Abuse Attacks Against Searchable Encryption.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Schemes for secure outsourcing of client data with search capability are being increasingly marketed and deployed. In the literature, schemes for accomplishing this efficiently are called Searchable Encryption (SE). They achieve high efficiency with provable security by means of a quantifiable leakage profile. However, the degree to which SE leakage can be exploited by an adversary is not well understood.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813700"}, {"primary_key": "4377190", "vector": [], "sparse_vector": [], "title": "Authenticating Privately over Public Wi-Fi Hotspots.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wi-Fi connectivity using open hotspots hosted on untrusted Access Points (APs) has been a staple of mobile network deployments for many years as mobile providers seek to offload smartphone traffic to Wi-Fi. Currently, the available hotspot solutions allow for mobility patterns and client identities to be monitored by the parties hosting the APs as well as by the underlying service provider. We propose a protocol and system that allows a service provider to authenticate its clients, and hides the client identity from both AP and service provider at the time of authentication. Particularly, the client is guaranteed that either the provider cannot do better than to guess their identity randomly or they obtain proof that the provider is trying to reveal their identity by using different keys. Our protocol is based on Private Information Retrieval (PIR) with an augmented cheating detection mechanism based on our extensions to the NTRU encryption scheme. The somewhat-homomorphic encryption makes auditing of multiple rows in a single query possible, and optimizes PIR for highly parallel GPU computations with the use of the Fast Fourier Transform (FFT).", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813647"}, {"primary_key": "4377192", "vector": [], "sparse_vector": [], "title": "Using Linearly-Homomorphic Encryption to Evaluate Degree-2 Functions on Encrypted Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show a technique to transform a linearly-homomorphic encryption into a scheme capable of evaluating degree-2 computations on ciphertexts. Our transformation is surprisingly simple and requires only one very mild property on the underlying linearly-homomorphic scheme: the message space must be a public ring in which it is possible to sample elements uniformly at random. This allows us to instantiate our transformation with virtually all existing number-theoretic linearly-homomorphic schemes, such as Goldwasser-Micali, Paillier, or ElGamal. Our resulting schemes achieve circuit privacy and are compact when considering a subclass of degree-2 polynomials where the number of additions of degree-2 terms is bounded by a constant.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813624"}, {"primary_key": "4377194", "vector": [], "sparse_vector": [], "title": "HORNET: High-speed Onion Routing at the Network Layer.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present HORNET, a system that enables high-speed end-to-end anonymous channels by leveraging next-generation network architectures. HORNET is designed as a low-latency onion routing system that operates at the network layer thus enabling a wide range of applications. Our system uses only symmetric cryptography for data forwarding yet requires no per-flow state on intermediate routers. This design enables HORNET routers implemented on off-the-shelf hardware to process anonymous traffic at over 93 Gb/s. HORNET is also highly scalable, adding minimal processing overhead per additional anonymous channel.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813628"}, {"primary_key": "4377196", "vector": [], "sparse_vector": [], "title": "Perplexed Messengers from the Cloud: Automated Security Analysis of Push-Messaging Integrations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we report the first large-scale, systematic study on the security qualities of emerging push-messaging services, focusing on their app-side service integrations. We identified a set of security properties different push-messaging services (e.g., Google Cloud Messaging) need to have, and automatically verified them in different integrations using a new technique, called Seminal. Seminal is designed to extract semantic information from a service's sample code, and leverage the information to evaluate the security qualities of the service's SDKs and its integrations within different apps. Using this tool, we studied 30 leading services around the world, and scanned 35,173 apps. Our findings are astonishing: over 20% apps in Google Play and 50% apps in mainstream Chinese app markets are riddled with security-critical loopholes, putting a huge amount of sensitive user data at risk. Also, our research brought to light new types of security flaws never known before, which can be exploited to cause serious confusions among popular apps and services (e.g., Facebook, Skype, Yelp, Baidu <PERSON>ush). Taking advantage of such confusions, the adversary can post his content to the victim's apps in the name of trusted parties and intercept her private messages. The study highlights the serious challenges in securing push-messaging services and an urgent need for improving their security qualities.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813652"}, {"primary_key": "4377197", "vector": [], "sparse_vector": [], "title": "Static Detection of Packet Injection Vulnerabilities: A Case for Identifying Attacker-controlled Implicit Information Leaks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Off-path packet injection attacks are still serious threats to the Internet and network security. In recent years, a number of studies have discovered new variations of packet injection attacks, targeting critical protocols such as TCP. We argue that such recurring problems need a systematic solution. In this paper, we design and implement PacketGuardian, a precise static taint analysis tool that comprehensively checks the packet handling logic of various network protocol implementations. The analysis operates in two steps. First, it identifies the critical paths and constraints that lead to accepting an incoming packet. If paths with weak constraints exist, a vulnerability may be revealed immediately. Otherwise, based on \"secret\" protocol states in the constraints, a subsequent analysis is performed to check whether such states can be leaked to an attacker.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813643"}, {"primary_key": "4377198", "vector": [], "sparse_vector": [], "title": "POSTER: iPKI: Identity-based Private Key Infrastructure for Securing BGP Protocol.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "Jinshu Su", "<PERSON><PERSON>"], "summary": "For Securing BGP Protocol, this paper proposes an identity-based private key infrastructure (iPKI) for managing self-attested IP (sIP) addresses. An sIP address endows the current IP address self-attested characteristic, which does not rely on any credential based PKI. Based on the sIP address, we design the In-Band Self Origin Verification (IBSOV) protocol and self Route Origin Authorization (sROA) to provide a lightweight origin verification for the BGP protocol, which has a much lower overhead than the existing works.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810116"}, {"primary_key": "4377203", "vector": [], "sparse_vector": [], "title": "Equivalence-based Security for Querying Encrypted Databases: Theory and Application to Privacy Policy Audits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To reduce costs, organizations may outsource data storage and data processing to third-party clouds. This raises confidentiality concerns, since the outsourced data may have sensitive information. Although semantically secure encryption of the data prior to outsourcing alleviates these concerns, it also renders the outsourced data useless for any relational processing. Motivated by this problem, we present two database encryption schemes that reveal just enough information about structured data to support a wide-range of relational queries. Our main contribution is a definition and proof of security for the two schemes. This definition captures confidentiality offered by the schemes using a novel notion of equivalence of databases from the adversary's perspective. As a specific application, we adapt an existing algorithm for finding violations of a rich class of privacy policies to run on logs encrypted under our schemes and observe low to moderate overheads.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813638"}, {"primary_key": "4377204", "vector": [], "sparse_vector": [], "title": "Inlined Information Flow Monitoring for JavaScript.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Extant security mechanisms for web apps, notably the \"same-origin policy\", are not sufficient to achieve confidentiality and integrity goals for the many apps that manipulate sensitive information. The trend in web apps is \"mashups\" which integrate JavaScript code from multiple providers in ways that can undercut existing security mechanisms. Researchers are exploring dynamic information flow controls (IFC) for JavaScript, but there are many challenges to achieving strong IFC without excessive performance cost or impractical browser modifications. This paper presents an inlined IFC monitor for ECMAScript 5 with web support, using the no-sensitive-upgrade (NSU) technique, together with experimental evaluation using synthetic mashups and performance benchmarks. On this basis it should be possible to conduct experiments at scale to evaluate feasibility of both NSU and inlined monitoring.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813684"}, {"primary_key": "4377208", "vector": [], "sparse_vector": [], "title": "Losing Control: On the Effectiveness of Control-Flow Integrity under Stack Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Adversaries exploit memory corruption vulnerabilities to hijack a program's control flow and gain arbitrary code execution. One promising mitigation, control-flow integrity (CFI), has been the subject of extensive research in the past decade. One of the core findings is that adversaries can construct Turing-complete code-reuse attacks against coarse-grained CFI policies because they admit control flows that are not part of the original program. This insight led the research community to focus on fine-grained CFI implementations. In this paper we show how to exploit heap-based vulnerabilities to control the stack contents including security-critical values used to validate control-flow transfers. Our investigation shows that although program analysis and compiler-based mitigations reduce stack-based vulnerabilities, stack-based memory corruption remains an open problem. Using the Chromium web browser we demonstrate real-world attacks against various CFI implementations: 1)~against CFI implementations under Windows 32-bit by exploiting unprotected context switches, and 2)~against state-of-the-art fine-grained CFI implementations (IFCC and VTV) in the two premier open-source compilers under Unix-like operating systems. Both 32 and 64-bit x86 CFI checks are vulnerable to stack manipulation. Finally, we provide an exploit technique against the latest shadow stack implementation.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813671"}, {"primary_key": "4377209", "vector": [], "sparse_vector": [], "title": "It&apos;s a TRaP: Table Randomization and Protection against Function-Reuse Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Code-reuse attacks continue to evolve and remain a severe threat to modern software. Recent research has proposed a variety of defenses with differing security, efficiency, and practicality characteristics. Whereas the majority of these solutions focus on specific code-reuse attack variants such as return-oriented programming (ROP), other attack variants that reuse whole functions, such as the classic return-into-libc, have received much less attention. Mitigating function-level code reuse is highly challenging because one needs to distinguish a legitimate call to a function from an illegitimate one. In fact, the recent counterfeit object-oriented programming (COOP) attack demonstrated that the majority of code-reuse defenses can be bypassed by reusing dynamically bound functions, i.e., functions that are accessed through global offset tables and virtual function tables, respectively.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813682"}, {"primary_key": "4377211", "vector": [], "sparse_vector": [], "title": "MTD 2015: Second ACM Workshop on Moving Target Defense.", "authors": ["<PERSON>", "<PERSON><PERSON> Huang"], "summary": "The second ACM workshop on cloud data management is held in Denver, Colorado, USA on October 12, 2015 and co-located with the ACM 22nd Conference on Computer and Communications Security (CCS). The main idea of moving-target defense (MTD) is to impose an asymmetric disadvantage on attackers by making systems dynamic and therefore harder to explore and predict. This workshop seeks to bring together researchers from academia, government, and industry to report on the latest research efforts on moving-target defense, and to have productive discussion and constructive debate on this topic. We have constructed an exciting program of 12 referred papers and two invited keynote talks that will give participants a comprehensive view of emerging research.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812623"}, {"primary_key": "4377213", "vector": [], "sparse_vector": [], "title": "Provisions: Privacy-preserving Proofs of Solvency for Bitcoin Exchanges.", "authors": ["<PERSON><PERSON> <PERSON>", "Benedikt <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bitcoin exchanges function like banks, securely holding customers' bitcoins on their behalf. Several exchanges have suffered catastrophic losses with customers permanently losing their savings. A proof of solvency demonstrates cryptographically that the exchange controls sufficient reserves to settle each customer's account. We introduce Provisions, a privacy-preserving proof of solvency whereby an exchange does not have to disclose its Bitcoin addresses; total holdings or liabilities; or any information about its customers. We also propose an extension which prevents exchanges from colluding to cover for each other's losses. We have implemented Provisions and it offers practical computation times and proof sizes even for a large Bitcoin exchange with millions of customers.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813674"}, {"primary_key": "4377217", "vector": [], "sparse_vector": [], "title": "Monte Carlo Strength Evaluation: Fast and Reliable Password Checking.", "authors": ["Matteo Dell&apos;Amico", "<PERSON><PERSON><PERSON>"], "summary": "Modern password guessing attacks adopt sophisticated probabilistic techniques that allow for orders of magnitude less guesses to succeed compared to brute force. Unfortunately, best practices and password strength evaluators failed to keep up: they are generally based on heuristic rules designed to defend against obsolete brute force attacks. Many passwords can only be guessed with significant effort, and motivated attackers may be willing to invest resources to obtain valuable passwords. However, it is eminently impractical for the defender to simulate expensive attacks against each user to accurately characterize their password strength. This paper proposes a novel method to estimate the number of guesses needed to find a password using modern attacks. The proposed method requires little resources, applies to a wide set of probabilistic models, and is characterised by highly desirable convergence properties.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813631"}, {"primary_key": "4377218", "vector": [], "sparse_vector": [], "title": "Automated Synthesis of Optimized Circuits for Secure Computation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the recent years, secure computation has been the subject of intensive research, emerging from theory to practice. In order to make secure computation usable by non-experts, Fairplay (USENIX Security 2004) initiated a line of research in compilers that allow to automatically generate circuits from high-level descriptions of the functionality that is to be computed securely. Most recently, TinyGarble (IEEE S&P 2015) demonstrated that it is natural to use existing hardware synthesis tools for this task. In this work, we present how to use industrial-grade hardware synthesis tools to generate circuits that are not only optimized for size, but also for depth. These are required for secure computation protocols with non-constant round complexity. We compare a large variety of circuits generated by our toolchain with hand-optimized circuits and show reduction of depth by up to 14%.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813678"}, {"primary_key": "4377219", "vector": [], "sparse_vector": [], "title": "iRiS: Vetting Private API Abuse in iOS Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the booming sale of iOS devices, the number of iOS applications has increased significantly in recent years. To protect the security of iOS users, Apple requires every iOS application to go through a vetting process called App Review to detect uses of private APIs that provide access to sensitive user information. However, recent attacks have shown the feasibility of using private APIs without being detected during App Review. To counter such attacks, we propose a new iOS application vetting system, called iRiS, in this paper. iRiS first applies fast static analysis to resolve API calls. For those that cannot be statically resolved, iRiS uses a novel iterative dynamic analysis approach, which is slower but more powerful compared to static analysis. We have ported Valgrind to iOS and implemented a prototype of iRiS on top of it. We evaluated iRiS with 2019 applications from the official App Store. From these, iRiS identified 146 (7%) applications that use a total number of 150 different private APIs, including 25 security-critical APIs that access sensitive user information, such as device serial number. By analyzing iOS applications using iRiS, we also identified a suspicious advertisement service provider which collects user privacy information in its advertisement serving library. Our results show that, contrary to popular belief, a nontrivial number of iOS applications that violate Apple's terms of service exist in the App Store. iRiS is effective in detecting private API abuse missed by App Review.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813675"}, {"primary_key": "4377223", "vector": [], "sparse_vector": [], "title": "Workshop Summary of AISec&apos;15: 2015 Workshop on Artificial Intelligent and Security.", "authors": ["<PERSON><PERSON>", "Aikaterini <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It is our great pleasure to welcome you to the 2015 ACM Workshop Artificial Intelligence and Security (AISec 2015) - the eight annual workshop addressing technologies that fuse intelligent systems into computer security applications and the implications of these approaches. The workshop's aim is to advance research at the intersection of artificial intelligence, machine learning, privacy and security. In particular, AISec gives researchers and practitioners working within one or more of those fields a platform for interdisciplinary discussion, which would otherwise be lacking. Hopefully, the workshop leads to a high degree of cross-pollination between groups working across these areas. The papers to be presented in this year's program span topics ranging from adversarial learning, detecting fake OSN accounts, malware classification to privacy preserving data processing and game theoretic techniques in adversarial learning. We are delighted to again be co-located with the premier ACM Computer and Communication Security (CCS 2015) conference. This year we had 25 submissions from Asia, Europe and North America. After a rigorous reviewing process, involving at 2-3 referees per paper, 11 papers were accepted for presentation at the workshop, including presentation-only papers.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812619"}, {"primary_key": "4377227", "vector": [], "sparse_vector": [], "title": "A Cryptographic Analysis of the TLS 1.3 Handshake Protocol Candidates.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Douglas <PERSON>"], "summary": "The Internet Engineering Task Force (IETF) is currently developing the next version of the Transport Layer Security (TLS) protocol, version 1.3. The transparency of this standardization process allows comprehensive cryptographic analysis of the protocols prior to adoption, whereas previous TLS versions have been scrutinized in the cryptographic literature only after standardization. This is even more important as there are two related, yet slightly different, candidates in discussion for TLS 1.3, called draft-ietf-tls-tls13-05 and draft-ietf-tls-tls13-dh-based. We give a cryptographic analysis of the primary ephemeral Diffie-Hellman-based handshake protocol, which authenticates parties and establishes encryption keys, of both TLS 1.3 candidates. We show that both candidate handshakes achieve the main goal of providing secure authenticated key exchange according to an augmented multi-stage version of the Bellare-Rogaway model. Such a multi-stage approach is convenient for analyzing the design of the candidates, as they establish multiple session keys during the exchange.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813653"}, {"primary_key": "4377228", "vector": [], "sparse_vector": [], "title": "A Search Engine Backed by Internet-Wide Scanning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Fast Internet-wide scanning has opened new avenues for security research, ranging from uncovering widespread vulnerabilities in random number generators to tracking the evolving impact of Heartbleed. However, this technique still requires significant effort: even simple questions, such as, \"What models of embedded devices prefer CBC ciphers?\", require developing an application scanner, manually identifying and tagging devices, negotiating with network administrators, and responding to abuse complaints. In this paper, we introduce Censys, a public search engine and data processing facility backed by data collected from ongoing Internet-wide scans. Designed to help researchers answer security-related questions, Censys supports full-text searches on protocol banners and querying a wide range of derived fields (e.g., 443.https.cipher). It can identify specific vulnerable devices and networks and generate statistical reports on broad usage patterns and trends. Censys returns these results in sub-second time, dramatically reducing the effort of understanding the hosts that comprise the Internet. We present the search engine architecture and experimentally evaluate its performance. We also explore Censys's applications and show how questions asked in recent studies become simple to answer.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813703"}, {"primary_key": "4377229", "vector": [], "sparse_vector": [], "title": "Introduction to Cryptocurrencies.", "authors": ["<PERSON>"], "summary": "We provide a research-oriented introduction to the cryptographic currencies. We start with a description of Bitcoin and its main design principles. We then discuss some of its weaknesses, and show some ideas for dealing with them. We also talk about the mechanics of the mining pools and ideas for discouraging the mining pool creation. We provide an introduction to the smart contracts, and give some examples of them, including the multiparty lotteries.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812704"}, {"primary_key": "4377231", "vector": [], "sparse_vector": [], "title": "Control Jujutsu: On the Weaknesses of Fine-Grained Control Flow Integrity.", "authors": ["<PERSON>", "<PERSON>", "Ulziibayar Otgonbaatar", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Control flow integrity (CFI) has been proposed as an approach to defend against control-hijacking memory corruption attacks. CFI works by assigning tags to indirect branch targets statically and checking them at runtime. Coarse-grained enforcements of CFI that use a small number of tags to improve the performance overhead have been shown to be ineffective. As a result, a number of recent efforts have focused on fine-grained enforcement of CFI as it was originally proposed. In this work, we show that even a fine-grained form of CFI with unlimited number of tags and a shadow stack (to check calls and returns) is ineffective in protecting against malicious attacks. We show that many popular code bases such as Apache and Nginx use coding practices that create flexibility in their intended control flow graph (CFG) even when a strong static analyzer is used to construct the CFG. These flexibilities allow an attacker to gain control of the execution while strictly adhering to a fine-grained CFI. We then construct two proof-of-concept exploits that attack an unlimited tag CFI system with a shadow stack. We also evaluate the difficulties of generating a precise CFG using scalable static analysis for real-world applications. Finally, we perform an analysis on a number of popular applications that highlights the availability of such attacks.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813646"}, {"primary_key": "4377233", "vector": [], "sparse_vector": [], "title": "Keynote Talk.", "authors": ["<PERSON>"], "summary": "No abstract available.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2823356"}, {"primary_key": "4377234", "vector": [], "sparse_vector": [], "title": "SPRESSO: A Secure, Privacy-Respecting Single Sign-On System for the Web.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Single sign-on (SSO) systems, such as OpenID and OAuth, allow web sites, so-called relying parties (RPs), to delegate user authentication to identity providers (IdPs), such as Facebook or Google. These systems are very popular, as they provide a convenient means for users to log in at RPs and move much of the burden of user authentication from RPs to IdPs. There is, however, a downside to current systems, as they do not respect users' privacy: IdPs learn at which RP a user logs in. With one exception, namely Mozilla's BrowserID system (a.k.a. Mozilla Persona), current SSO systems were not even designed with user privacy in mind. Unfortunately, recently discovered attacks, which exploit design flaws of BrowserID, show that BrowserID does not provide user privacy either.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813726"}, {"primary_key": "4377236", "vector": [], "sparse_vector": [], "title": "Security by Any Other Name: On the Effectiveness of Provider Based Email Security.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Email as we use it today makes no guarantees about message integrity, authenticity, or confidentiality. Users must explicitly encrypt and sign message contents using tools like PGP if they wish to protect themselves against message tampering, forgery, or eavesdropping. However, few do, leaving the vast majority of users open to such attacks. Fortunately, transport-layer security mechanisms (available as extensions to SMTP, IMAP, POP3) provide some degree of protection against network-based eavesdropping attacks. At the same time, DKIM and SPF protect against network-based message forgery and tampering. In this work we evaluate the security provided by these protocols, both in theory and in practice. Using a combination of measurement techniques, we determine whether major providers supports TLS at each point in their email message path, and whether they support SPF and DKIM on incoming and outgoing mail. We found that while more than half of the top 20,000 receiving MTAs supported TLS, and support for TLS is increasing, servers do not check certificates, opening the Internet email system up to man-in-the-middle eavesdropping attacks. At the same time, while use of SPF is common, enforcement is limited. Moreover, few of the senders we examined used DKIM, and fewer still rejected invalid DKIM signatures. Our findings show that the global email system provides some protection against passive eavesdropping, limited protection against unprivileged peer message forgery, and no protection against active network-based attacks. We observe that protection even against the latter is possible using existing protocols with proper enforcement.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813607"}, {"primary_key": "4377238", "vector": [], "sparse_vector": [], "title": "Model Inversion Attacks that Exploit Confidence Information and Basic Countermeasures.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Machine-learning (ML) algorithms are increasingly utilized in privacy-sensitive applications such as predicting lifestyle choices, making medical diagnoses, and facial recognition. In a model inversion attack, recently introduced in a case study of linear classifiers in personalized medicine by <PERSON><PERSON><PERSON> et al., adversarial access to an ML model is abused to learn sensitive genomic information about individuals. Whether model inversion attacks apply to settings outside theirs, however, is unknown. We develop a new class of model inversion attack that exploits confidence values revealed along with predictions. Our new attacks are applicable in a variety of settings, and we explore two in depth: decision trees for lifestyle surveys as used on machine-learning-as-a-service systems and neural networks for facial recognition. In both cases confidence values are revealed to those with the ability to make prediction queries to models. We experimentally show attacks that are able to estimate whether a respondent in a lifestyle survey admitted to cheating on their significant other and, in the other context, show how to recover recognizable images of people's faces given only their name and access to the ML model. We also initiate experimental exploration of natural countermeasures, investigating a privacy-aware decision tree training algorithm that is a simple variant of CART learning, as well as revealing only rounded confidence values. The lesson that emerges is that one can avoid these kinds of MI attacks with negligible degradation to utility.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813677"}, {"primary_key": "4377240", "vector": [], "sparse_vector": [], "title": "Lattice Basis Reduction Attack against Physically Unclonable Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to successful modeling attacks against arbiter PUFs (Physically Unclonable Functions), the trend towards consideration of XOR arbiter PUFs has emerged. Nevertheless, it has already been demonstrated that even this new non-linear structure, with a restricted number of parallel arbiter chains, is still vulnerable to more advanced modeling attacks and side channel analyses. However, so far the security of XOR arbiter PUFs with a large number of parallel arbiter chains has not been appropriately assessed. Furthermore, as another countermeasure against modeling and physical attacks, the concept of controlled PUFs, i.e., with a limited access to challenges and responses, has also been developed. Towards a better understanding of the security of XOR arbiter PUFs, the present paper simultaneously addresses all above mentioned countermeasures by introducing a novel attack, which is a combination of a lattice basis reduction attack and a photonic side channel analysis. We present how our new attack can be successfully launched against XOR arbiter PUFs with an arbitrarily large number of parallel arbiter chains. Most interestingly, our attack does not require any access to challenges or responses. Finally, by conducting an exhaustive discussion on our experimental results, the practical feasibility of our attack scenario is proved as well.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813723"}, {"primary_key": "4377242", "vector": [], "sparse_vector": [], "title": "Cross-Site Search Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cross-site search (XS-search) attacks circumvent the same-origin policy and extract sensitive information, by using the time it takes for the browser to receive responses to search queries. This side-channel is usually considered impractical, due to the limited attack duration and high variability of delays. This may be true for naive XS-search attacks; however, we show that the use of better tools facilitates effective XS-search attacks, exposing information efficiently and precisely.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813688"}, {"primary_key": "4377243", "vector": [], "sparse_vector": [], "title": "Tampering with the Delivery of Blocks and Transactions in Bitcoin.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given the increasing adoption of Bitcoin, the number of transactions and the block sizes within the system are only expected to increase. To sustain its correct operation in spite of its ever-increasing use, Bitcoin implements a number of necessary optimizations and scalability measures. These measures limit the amount of information broadcast in the system to the minimum necessary. In this paper, we show that current scalability measures adopted by Bitcoin come at odds with the security of the system. More specifically, we show that an adversary can exploit these measures in order to effectively delay the propagation of transactions and blocks to specific nodes for a considerable amount of time---without causing a network partitioning in the system. Notice that this attack alters the information received by Bitcoin nodes, and modifies their views of the ledger state. Namely, we show that this allows the adversary to considerably increase its mining advantage in the network, and to double-spend transactions in spite of the current countermeasures adopted by Bitcoin. Based on our results, we propose a number of countermeasures in order to enhance the security of Bitcoin without deteriorating its scalability.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813655"}, {"primary_key": "4377244", "vector": [], "sparse_vector": [], "title": "The Clock is Still Ticking: Timing Attacks in the Modern Web.", "authors": ["<PERSON>", "W<PERSON><PERSON>", "<PERSON>"], "summary": "Web-based timing attacks have been known for over a decade, and it has been shown that, under optimal network conditions, an adversary can use such an attack to obtain information on the state of a user in a cross-origin website. In recent years, desktop computers have given way to laptops and mobile devices, which are mostly connected over a wireless or mobile network. These connections often do not meet the optimal conditions that are required to reliably perform cross-site timing attacks. In this paper, we show that modern browsers expose new side-channels that can be used to acquire accurate timing measurements, regardless of network conditions. Using several real-world examples, we introduce four novel web-based timing attacks against modern browsers and describe how an attacker can use them to obtain personal information based on a user's state on a cross-origin website. We evaluate our proposed attacks and demonstrate that they significantly outperform current attacks in terms of speed, reliability, and accuracy. Furthermore, we show that the nature of our attacks renders traditional defenses, i.e., those based on randomly delaying responses, moot and discuss possible server-side defense mechanisms.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813632"}, {"primary_key": "4377248", "vector": [], "sparse_vector": [], "title": "Fifth International Workshop on Trustworthy Embedded Devices (TrustED 2015).", "authors": ["<PERSON>", "<PERSON>"], "summary": "The Internet of Things (IoTS) is expected to seamlessly connect everything and everyone and bring about the promise of smart environments, industry 4.0, intelligent infrastructure management, environmental monitoring and disaster recovery, etc. The explosion in the number of interconnected devices makes it a challenge to guarantee their security, the security of their networks and the privacy of the data collected by them. The Workshop on Trustworthy Embedded Devices (TrustED) focuses on all aspects of security and privacy related to embedded systems and the IoTS. TrustED 2015 continues a successful series of workshops, which were held in conjunction with ESORICS 2011, IEEE Security & Privacy 2012, ACM CCS 2013 and ACM CCS 2014 (see http://www.trusted-workshop.de for details). The goal of this workshop is to bring together experts from academia and research institutes, industry, and government in the field of security and privacy in cyber physical systems.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812626"}, {"primary_key": "4377249", "vector": [], "sparse_vector": [], "title": "Clean Application Compartmentalization with SOAAP.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Application compartmentalization, a vulnerability mitigation technique employed in programs such as OpenSSH and the Chromium web browser, decomposes software into isolated components to limit privileges leaked or otherwise available to attackers. However, compartmentalizing applications -- and maintaining that compartmentalization -- is hindered by ad hoc methodologies and significantly increased programming effort. In practice, programmers stumble through (rather than overtly reason about) compartmentalization spaces of possible decompositions, unknowingly trading off correctness, security, complexity, and performance. We present a new conceptual framework embodied in an LLVM-based tool: the Security-Oriented Analysis of Application Programs (SOAAP) that allows programmers to reason about compartmentalization using source-code annotations (compartmentalization hypotheses). We demonstrate considerable benefit when creating new compartmentalizations for complex applications, and analyze existing compartmentalized applications to discover design faults and maintenance issues arising from application evolution.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813611"}, {"primary_key": "4377250", "vector": [], "sparse_vector": [], "title": "GCM-SIV: Full Nonce Misuse-Resistant Authenticated Encryption at Under One Cycle per Byte.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Authenticated encryption schemes guarantee both privacy and integrity, and have become the default level of encryption in modern protocols. One of the most popular authenticated encryption schemes today is AES-GCM due to its impressive speed. The current CAESAR competition is considering new modes for authenticated encryption that will improve on existing methods. One property of importance that is being considered more today -- due to multiple real-life cases of faulty sources of randomness -- is that repeating nonces and IVs can have disastrous effects on security. A (full) nonce misuse-resistant authenticated encryption scheme has the property that if the same nonce is used to encrypt the same message twice, then the same ciphertext is obtained and so the fact that the same message was encrypted is detected. Otherwise, full security is obtained -- even if the same nonce is used for different messages. In this paper, we present a new fully nonce misuse-resistant authenticated encryption scheme that is based on carefully combining the GCM building blocks into the SIV paradigm of Rogaway and Shrimpton. We provide a full proof of security of our scheme, and an optimized implementation using the AES-NI and PCLMULQDQ instruction sets. We compare our performance to the highly optimized OpenSSL 1.0.2 implementation of GCM and show that our nonce misuse-resistant scheme is only 14% slower on Haswell architecture and 19% slower on Broadwell architecture. On Broadwell, GCM-SIV encryption takes only 0.92 cycles per byte, and GCM-SIV decryption is exactly the same as GCM decryption taking only 0.77 cycles per byte. In addition, we compare to other optimized authenticated-encryption implementations carried out by Bogdanov et al., and conclude that our mode is very competitive. Beyond being very fast, our new mode of operation uses the same building blocks as GCM and so existing hardware and software can be utilized to easily deploy GCM-SIV. We conclude that GCM-SIV is a viable alternative to GCM, providing full nonce misuse-resistance at little cost.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813613"}, {"primary_key": "4377251", "vector": [], "sparse_vector": [], "title": "Fast Garbling of Circuits Under Standard Assumptions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Protocols for secure computation enable mutually distrustful parties to jointly compute on their private inputs without revealing anything but the result. Over recent years, secure computation has become practical and considerable effort has been made to make it more and more efficient. A highly important tool in the design of two-party protocols is <PERSON>'s garbled circuit construction (<PERSON> 1986), and multiple optimizations on this primitive have led to performance improvements of orders of magnitude over the last years. However, many of these improvements come at the price of making very strong assumptions on the underlying cryptographic primitives being used (e.g., that AES is secure for related keys, that it is circular secure, and even that it behaves like a random permutation when keyed with a public fixed key). The justification behind making these strong assumptions has been that otherwise it is not possible to achieve fast garbling and thus fast secure computation. In this paper, we take a step back and examine whether it is really the case that such strong assumptions are needed. We provide new methods for garbling that are secure solely under the assumption that the primitive used (e.g., AES) is a pseudorandom function. Our results show that in many cases, the penalty incurred is not significant, and so a more conservative approach to the assumptions being used can be adopted.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813619"}, {"primary_key": "4377254", "vector": [], "sparse_vector": [], "title": "Drops for Stuff: An Analysis of Reshipping <PERSON><PERSON>.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Credit card fraud has seen rampant increase in the past years, as customers use credit cards and similar financial instruments frequently. Both online and brick-and-mortar outfits repeatedly fall victim to cybercriminals who siphon off credit card information in bulk. Despite the many and creative ways that attackers use to steal and trade credit card information, the stolen information can rarely be used to withdraw money directly, due to protection mechanisms such as PINs and cash advance limits. As such, cybercriminals have had to devise more advanced monetization schemes to work around the current restrictions. One monetization scheme that has been steadily gaining traction are reshipping scams. In such scams, cybercriminals purchase high-value or highly-demanded products from online merchants using stolen payment instruments, and then ship the items to a credulous citizen. This person, who has been recruited by the scammer under the guise of \"work-from-home\" opportunities, then forwards the received products to the cybercriminals, most of whom are located overseas. Once the goods reach the cybercriminals, they are then resold on the black market for an illicit profit. Due to the intricacies of this kind of scam, it is exceedingly difficult to trace, stop, and return shipments, which is why reshipping scams have become a common means for miscreants to turn stolen credit cards into cash.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813620"}, {"primary_key": "4377259", "vector": [], "sparse_vector": [], "title": "Automated Analysis and Synthesis of Authenticated Encryption Schemes.", "authors": ["Viet Tung Hoang", "<PERSON>", "<PERSON>"], "summary": "Authenticated encryption (AE) schemes are symmetric-key encryption schemes ensuring strong notions of confidentiality and integrity. Although various AE schemes are known, there remains significant interest in developing schemes that are more efficient, meet even stronger security notions (e.g., misuse-resistance), or satisfy certain non-cryptographic properties (e.g., being patent-free).", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813636"}, {"primary_key": "4377260", "vector": [], "sparse_vector": [], "title": "CacheBrowser: Bypassing Chinese Censorship without Proxies Using Cached Content.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The cached Internet content served by content delivery networks (CDN) comprises a large fraction of today's Internet traffic, yet, there is little study on how real-world censors deal with blocking forbidden CDN-hosted Internet content. We investigate the techniques used by the Great Firewall of China to block CDN-hosted content, and demonstrate that blocking CDN content poses unique technical and non-technical challenges to the censors. We therefore design a client-side circumvention system, CacheBrowser, that leverages the censors' difficulties in blocking CDN content. We implement CacheBrowser and use it to unblock CDN-hosted content in China with a download latency significantly smaller than traditional proxy-based circumvention systems like Tor. CacheBrowser's superior quality-of-service is thanks to its publisher-centric approach, which retrieves blocked content directly from content publishers with no use of third-party proxies.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813696"}, {"primary_key": "4377261", "vector": [], "sparse_vector": [], "title": "WPES 2015: The 14th Workshop on Privacy in the Electronic Society.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present a brief summary of The 14th Workshop on Privacy in the Electronic Society, held on October 12th, 2015, in conjunction with the 22nd ACM Conference on Computer and Communications Security in Denver, Colorado, USA. The goal of this workshop is to discuss the problems of privacy in the global interconnected societies and possible solutions to them. The workshop program includes 11 full papers and 3 short papers out of 32 total submissions. Specific areas that are covered in the program include, but are not limited to: web and social network privacy, mobile and location privacy, communications privacy, and privacy-preserving data analysis.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812628"}, {"primary_key": "4377264", "vector": [], "sparse_vector": [], "title": "From System Services Freezing to System Server Shutdown in Android: All You Need Is a Loop in an App.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Android OS not only dominates 78.6% of the worldwide smartphone market in 2014, but importantly has been widely used for mission critical tasks (e.g., medical devices, auto/aircraft navigators, embedded in satellite project). The core of Android, System Server (SS), is a multi-threaded process that contains most of the system services and provides the essential functionalities to support applications (apps). Considering the complicated design of the SS and its easily-accessible system services (e.g., via Android APIs), we conjecture that the SS may face DoS attacks. As the SS plays the important role in Android, serious DoS attacks could cause single-point-of-failure to the phone system. By studying the source code, we discovered a general design trait in the concurrency control mechanism of the SS that could be vulnerable to DoS attacks. To validate our hypothesis, we design a tool to cost efficiently explore high-risk methods in the SS. After a systematic analysis of 2,154 candidate-risky methods, we found four unknown vulnerabilities in critical services (e.g., the ActivityManager and the WindowManager), which are named the Android Stroke Vulnerabilities ({\\it ASVs}). Exploiting the ASVs would continuously block all other requests for system services, followed by killing the SS and soft-rebooting the OS. Results of a further threat analysis show that by writing a loop to invoke Android APIs in an app, an attacker can continually freeze (reboot) the device at targeted critical moments (e.g., when patching vulnerable apps). Furthermore, ASVs can be exploited to enhance malware with anti-removal capability or to design the ransomware by putting the devices into continuous DoS loops. After being informed, Google confirmed our findings promptly. We also proposed to their Android framework team several improvements in their concurrency control design and a fine-grained failure recovery mechanism for the SS.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813606"}, {"primary_key": "4377265", "vector": [], "sparse_vector": [], "title": "Surpass: System-initiated User-replaceable Passwords.", "authors": ["<PERSON>", "Seongyeol Oh", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "System-generated random passwords have maximum password security and are highly resistant to guessing attacks. However, few systems use such passwords because they are difficult to remember. In this paper, we propose a system-initiated password scheme called \"Surpass\" that lets users replace few characters in a random password to make it more memorable. We conducted a large-scale online study to evaluate the usability and security of four Surpass policies, varying the number of character replacements allowed from 1 to 4 in randomly-generated 8-character passwords. The study results suggest that some Surpass policies (with 3 and 4 character replacements) outperform by 11% to 13% the original randomly-generated password policy in memorability, while showing a small increase in the percentage of cracked passwords. When compared to a user-generated password complexity policy (that mandates the use of numbers, symbols, and uppercase letters) the Surpass policy with 4-character replacements did not show statistically significant inferiority in memorability. Our qualitative lab study showed similar trends. This Surpass policy demonstrated significant superiority in security though, with 21% fewer cracked passwords than the user-generated password policy.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813622"}, {"primary_key": "4377266", "vector": [], "sparse_vector": [], "title": "POSTER: Mobile Device Identification by Leveraging Built-in Capacitive Signature.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This work presents on-top, a new device identification method that exploits off-the-shelf capacitive touchscreens to extract its capacitive signatures. The method relies on a key observation that each capacitive touch screen has a unique capacitive signature which are caused by either the difference in touch sensing technologies or the imperfections of the sensor during its fabrication. In particular, the voltage pattern generated by commercial of-the-shelf (COTS) capacitive touchscreens during finger touch sensing are uniquely identifiable. Our preliminary evaluation with actual hardware prototype on 14 mobile touchscreens shows that on-top achieves a promising performance of 100% detection rate without any false positive. We also show that on-top can be used to securely trigger wireless communication while it consumes a very little amount of power (3.5 times lower than triggering using NFC and 2 times lower than using Bluetooth low energy (BLE)).", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810118"}, {"primary_key": "4377270", "vector": [], "sparse_vector": [], "title": "Face/Off: Preventing Privacy Leakage From Photos in Social Networks.", "authors": ["Panagiotis <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The capabilities of modern devices, coupled with the almost ubiquitous availability of Internet connectivity, have resulted in photos being shared online at an unprecedented scale. This is further amplified by the popularity of social networks and the immediacy they offer in content sharing. Existing access control mechanisms are too coarse-grained to handle cases of conflicting interests between the users associated with a photo; stories of embarrassing or inappropriate photos being widely accessible have become quite common. In this paper, we propose to rethink access control when applied to photos, in a way that allows us to effectively prevent unwanted individuals from recognizing users in a photo. The core concept behind our approach is to change the granularity of access control from the level of the photo to that of a user's personally identifiable information (PII). In this work, we consider the face as the PII. When another user attempts to access a photo, the system determines which faces the user does not have the permission to view, and presents the photo with the restricted faces blurred out. Our system takes advantage of the existing face recognition functionality of social networks, and can interoperate with the current photo-level access control mechanisms. We implement a proof-of-concept application for Facebook, and demonstrate that the performance overhead of our approach is minimal. We also conduct a user study to evaluate the privacy offered by our approach, and find that it effectively prevents users from identifying their contacts in 87.35% of the restricted photos. Finally, our study reveals the misconceptions about the privacy offered by existing mechanisms, and demonstrates that users are positive towards the adoption of an intuitive, straightforward access control mechanism that allows them to manage the visibility of their face in published photos.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813603"}, {"primary_key": "4377273", "vector": [], "sparse_vector": [], "title": "POSTER: Implementing and Testing a Novel Chaotic Cryptosystem for Use in Small Satellites.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cryptography in the domain of small satellites is a relatively new area of research. Compared to typical desktop computers, small satellites have limited bandwidth, processing power, and battery power. Many of the current encryption schemes were developed for desktop computers and servers, and as such may be unsuitable for small satellites. In addition, most cryptographic research in the domain of small satellites focuses on hardware solutions, which can be problematic given the limited space requirements of small satellites. This paper investigates potential software solutions that could be used to encrypt and decrypt data on small satellites and other devices with similarly limited resources. Specifically, this paper presents an implementation of an encryption algorithm based on chaos theory and compares and contrasts it with existing cryptographic schemes used in desktop computing and lightweight applications.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810114"}, {"primary_key": "4377274", "vector": [], "sparse_vector": [], "title": "On the Security of TLS 1.3 and QUIC Against Weaknesses in PKCS#1 v1.5 Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Encrypted key transport with RSA-PKCS#1 v1.5 is the most commonly deployed key exchange method in all current versions of the Transport Layer Security (TLS) protocol, including the most recent version 1.2. However, it has several well-known issues, most importantly that it does not provide forward secrecy, and that it is prone to side channel attacks that may enable an attacker to learn the session key used for a TLS session. A long history of attacks shows that RSA-PKCS#1 v1.5 is extremely difficult to implement securely. The current draft of TLS version 1.3 dispenses with this encrypted key transport method. But is this sufficient to protect against weaknesses in RSA-PKCS#1 v1.5?", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813657"}, {"primary_key": "4377276", "vector": [], "sparse_vector": [], "title": "SafeDSA: Safeguard Dynamic Spectrum Access against Fake Secondary Users.", "authors": ["<PERSON><PERSON><PERSON> Jin", "Jingchao Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dynamic spectrum access (DSA) is the key to solving worldwide wireless spectrum shortage. In a DSA system, unlicensed secondary users can opportunistically use a spectrum band when it is not used by the licensed primary user. The open nature of the wireless medium means that any secondary user can freely use any given spectrum band. Secondary-user authentication is thus essential to ensure the proper operations of DSA systems. We propose SafeDSA, a novel PHY-based scheme for authenticating secondary users in DSA systems. In SafeDSA, the secondary user embeds his spectrum-use authorization into the cyclic prefix of each physical-layer symbol, which can be detected and authenticated by a verifier. In contrast to previous work, SafeDSA achieves robust and efficient authentication of secondary users with negligible impact on normal data transmissions. We validate the efficacy and efficiency of SafeDSA through detailed MATLAB simulations and USRP experiments. Our results show that SafeDSA can detect fake secondary users with a maximum false-positive rate of 0.091 and a negligible false-negative rate based on USRP experiments.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813717"}, {"primary_key": "4377277", "vector": [], "sparse_vector": [], "title": "Falcon Codes: Fast, Authenticated LT Codes (Or: Making Rapid Tornadoes Unstoppable).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce Falcon codes, a class of authenticated error correcting codes that are based on LT codes and achieve the following properties, for the first time simultaneously: (1) with high probability, they can correct adversarial corruptions of an encoded message, and (2) they allow very efficient encoding and decoding times, even linear in the message length.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813728"}, {"primary_key": "4377279", "vector": [], "sparse_vector": [], "title": "Frequency-Hiding Order-Preserving Encryption.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Order-preserving encryption allows encrypting data, while still enabling efficient range queries on the encrypted data. This makes its performance and functionality very suitable for data outsourcing in cloud computing scenarios, but the security of order-preserving is still debatable. We present a scheme that achieves a strictly stronger notion of security than any other scheme so far. The basic idea is to randomize the ciphertexts to hide the frequency of plaintexts. Still, the client storage size remains small, in our experiments up to 1/15 of the plaintext size. As a result, one can more securely outsource large data sets, since we can also show that our security increases with larger data sets.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813629"}, {"primary_key": "4377280", "vector": [], "sparse_vector": [], "title": "CCSW 2015: The 7th ACM Cloud Computing Security Workshop.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Notwithstanding the latest buzzwords (grid, cloud, utility computing, SaaS, etc.), large-scale computing and cloud-like deployment are the fastest growing computing infrastructures today. How exactly they will look like tomorrow is still for the markets to decide, yet one thing has already been identified: clouds have new, untested deployment, associated adversarial models and vulnerabilities and hence a very different threat landscape. It is essential that our community becomes involved in shaping the future security of cloud computing. The CCSW workshop aims to bring together researchers and practitioners in all security and privacy aspects of cloud-centric and outsourced computing.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812620"}, {"primary_key": "4377282", "vector": [], "sparse_vector": [], "title": "DEMOS-2: Scalable E2E Verifiable Elections without Random Oracles.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>proposed a new E2E verifiable e-voting system called 'DEMOS' that for the first time provides E2E verifiability without relying on external sources of randomness or the random oracle model; the main advantage of such system is in the fact that election auditors need only the election transcript and the feedback from the voters to pronounce the election process unequivocally valid. Unfortunately, DEMOS comes with a huge performance and storage penalty for the election authority (EA) compared to other e-voting systems such as Helios. The main reason is that due to the way the EA forms the proof of the tally result, it is required to {\\em precompute} a number of ciphertexts for each voter and each possible choice of the voter. This approach clearly does not scale to elections that have a complex ballot and voters have an exponential number of ways to vote in the number of candidates. The performance penalty on the EA appears to be intrinsic to the approach: voters cannot compute an enciphered ballot themselves because there seems to be no way for them to prove that it is a valid ciphertext.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813727"}, {"primary_key": "4377283", "vector": [], "sparse_vector": [], "title": "Traitor Deterring Schemes: Using Bitcoin as Collateral for Digital Content.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We put forth a new cryptographic primitive called a Traitor Deterring Scheme (TDS). A TDS is a multi-recipient public-key encryption scheme where an authority issues decryption keys to a set of users. The distinguishing feature of a TDS is that secret-keys are issued only after the users provide some private information as a form of collateral. The traitor deterring property ensures that if a malicious coalition of users (aka \"traitors\") produces an unauthorized (aka \"pirate\") decryption device, any recipient of the device will be able to recover at least one of the traitors' collaterals with only black-box access to the device. On the other hand, honest users' collaterals are guaranteed to remain hidden. In this fashion a TDS deincentivizes malicious behavior among users.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813698"}, {"primary_key": "4377284", "vector": [], "sparse_vector": [], "title": "Breaking and Fixing VoLTE: Exploiting Hidden Data Channels and Mis-implementations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Jang", "<PERSON><PERSON> Han", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Long Term Evolution (LTE) is becoming the dominant cellular networking technology, shifting the cellular network away from its circuit-switched legacy towards a packet-switched network that resembles the Internet. To support voice calls over the LTE network, operators have introduced Voice-over-LTE (VoLTE), which dramatically changes how voice calls are handled, both from user equipment and infrastructure perspectives. We find that this dramatic shift opens up a number of new attack surfaces that have not been previously explored. To call attention to this matter, this paper presents a systematic security analysis.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813718"}, {"primary_key": "4377286", "vector": [], "sparse_vector": [], "title": "MalGene: Automatic Extraction of Malware Analysis Evasion Signature.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Automated dynamic malware analysis is a common approach for detecting malicious software. However, many malware samples identify the presence of the analysis environment and evade detection by not performing any malicious activity. Recently, an approach to the automated detection of such evasive malware was proposed. In this approach, a malware sample is analyzed in multiple analysis environments, including a bare-metal environment, and its various behaviors are compared. Malware whose behavior deviates substantially is identified as evasive malware. However, a malware analyst still needs to re-analyze the identified evasive sample to understand the technique used for evasion. Different tools are available to help malware analysts in this process. However, these tools in practice require considerable manual input along with auxiliary information. This manual process is resource-intensive and not scalable. In this paper, we present MalGene, an automated technique for extracting analysis evasion signatures. MalGene leverages algorithms borrowed from bioinformatics to automatically locate evasive behavior in system call sequences. Data flow analysis and data mining techniques are used to identify call events and data comparison events used to perform the evasion. These events are used to construct a succinct evasion signature, which can be used by an analyst to quickly understand evasions. Finally, evasive malware samples are clustered based on their underlying evasive techniques. We evaluated our techniques on 2810 evasive samples. We were able to automatically extract their analysis evasion signatures and group them into 78 similar evasion techniques.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813642"}, {"primary_key": "4377289", "vector": [], "sparse_vector": [], "title": "AUTOREB: Automatically Understanding the Review-to-Behavior Fidelity in Android Applications.", "authors": ["Deguang Kong", "<PERSON><PERSON>", "Hongxia Jin"], "summary": "Along with the increasing popularity of mobile devices, there exist severe security and privacy concerns for mobile apps. On Google Play, user reviews provide a unique understanding of security/privacy issues of mobile apps from users' perspective, and in fact they are valuable feedbacks from users by considering users' expectations. To best assist the end users, in this paper, we automatically learn the security/privacy related behaviors inferred from analysis on user reviews, which we call review-to-behavior fidelity. We design the system AUTOREB that automatically assesses the review-to-behavior fidelity of mobile apps. AUTOREB employs the state-of-the-art machine learning techniques to infer the relations between users' reviews and four categories of security-related behaviors. Moreover, it uses a crowdsourcing approach to automatically aggregate the security issues from review-level to app-level. To our knowledge, AUTOREB is the first work that explores the user review information and utilizes the review semantics to predict the risky behaviors at both review-level and app-level.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813689"}, {"primary_key": "4377290", "vector": [], "sparse_vector": [], "title": "Certified PUP: Abuse in Authenticode Code Signing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Code signing is a solution to verify the integrity of software and its publisher's identity, but it can be abused by malware and potentially unwanted programs (PUP) to look benign. This work performs a systematic analysis of Windows Authenticode code signing abuse, evaluating the effectiveness of existing defenses by certification authorities. We identify a problematic scenario in Authenticode where timestamped signed malware successfully validates even after the revocation of their code signing certificate. We propose hard revocations as a solution. We build an infrastructure that automatically analyzes potentially malicious executables, selects those signed, clusters them into operations, determines if they are PUP or malware, and produces a certificate blacklist. We use our infrastructure to evaluate 356 K samples from 2006-2015. Our analysis shows that most signed samples are PUP (88%-95%) and that malware is not commonly signed (5%-12%). We observe PUP rapidly increasing over time in our corpus. We measure the effectiveness of CA defenses such as identity checks and revocation, finding that 99.8% of signed PUP and 37% of signed malware use CA-issued certificates and only 17% of malware certificates and 15% of PUP certificates have been revoked. We observe most revocations lack an accurate revocation reason. We analyze the code signing infrastructure of the 10 largest PUP operations exposing that they heavily use file and certificate polymorphism and that 7 of them have multiple certificates revoked. Our infrastructure also generates a certificate blacklist 9x larger than current ones.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813665"}, {"primary_key": "4377294", "vector": [], "sparse_vector": [], "title": "Group Signatures with Probabilistic Revocation: A Computationally-Scalable Approach for Providing Privacy-Preserving Authentication.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> &quot;<PERSON>&quot; <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Group signatures (GSs) is an elegant approach for providing privacy-preserving authentication. Unfortunately, modern GS schemes have limited practical value for use in large networks due to the high computational complexity of their revocation check procedures. We propose a novel GS scheme called the Group Signatures with Probabilistic Revocation (GSPR), which significantly improves scalability with regard to revocation. GSPR employs the novel notion of probabilistic revocation, which enables the verifier to check the revocation status of the private key of a given signature very efficiently. However, GSPR's revocation check procedure produces probabilistic results, which may include false positive results but no false negative results. GSPR includes a procedure that can be used to iteratively decrease the probability of false positives. GSPR makes an advantageous tradeoff between computational complexity and communication overhead, resulting in a GS scheme that offers a number of practical advantages over the prior art. We provide a proof of security for GSPR in the random oracle model using the decisional linear assumption and the bilinear strong <PERSON><PERSON><PERSON><PERSON> assumption.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813602"}, {"primary_key": "4377295", "vector": [], "sparse_vector": [], "title": "POSTER: Dynamic Labelling for Analyzing Security Protocols.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Security protocols are essential for establishing trustworthiness of electronic transactions over open networks. Currently used languages and logics for protocol specifications do not facilitate/force the designer to make explicit goals, intentional assumptions or the preceding history across interactions among the stakeholders. Readers-Writers Flow Model (RWFM) is a novel model for information flow control, and has a label structure that explicitly specifies the permissible readers and influencers of a message. RWFM labels succinctly capture the history of a message. In this paper, we sketch an approach to enrich protocol specifications with RWFM labels that overcomes the problem of incomplete protocol specifications, and captures the intensional specifications in a natural way. Our approach tracks information flows in a protocol and makes explicit: (i) the assumptions and goals at each stage of the protocol, (ii) the construction of new messages from components of previous messages, and (iii) the knowledge of roles at various stages. We believe that our approach leads to a robust protocol specification language, including security/cryptographic protocols, that shall be of immense aid to the designer, user and the implementer of protocols.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810113"}, {"primary_key": "4377296", "vector": [], "sparse_vector": [], "title": "How to Use Bitcoin to Play Decentralized Poker.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON> and <PERSON><PERSON> (arXiv 2014) and <PERSON><PERSON><PERSON><PERSON> et al. (Security and Privacy 2014) introduced techniques to perform secure multiparty computations on Bitcoin. Among other things, these works constructed lottery protocols that ensure that any party that aborts after learning the outcome pays a monetary penalty to all other parties. Following this, <PERSON><PERSON><PERSON><PERSON> et al. (Bitcoin Workshop 2014) and concurrently <PERSON><PERSON> and <PERSON> (Crypto 2014) extended the solution to arbitrary secure function evaluation while guaranteeing fairness in the following sense: any party that aborts after learning the output pays a monetary penalty to all parties that did not learn the output. <PERSON><PERSON><PERSON><PERSON> et al. (Bitcoin Workshop 2014) also suggested extending to scenarios where parties receive a payoff according to the output of a secure function evaluation, and outlined a 2-party protocol for the same that in addition satisfies the notion of fairness described above. In this work, we formalize, generalize, and construct multiparty protocols for the primitive suggested by <PERSON><PERSON><PERSON><PERSON> et al. We call this primitive secure cash distribution with penalties. Our formulation of secure cash distribution with penalties poses it as a multistage reactive functionality (i.e., more general than secure function evaluation) that provides a way to securely implement smart contracts in a decentralized setting, and consequently suffices to capture a wide variety of stateful computations involving data and/or money, such as decentralized auctions, market, and games such as poker, etc. Our protocol realizing secure cash distribution with penalties works in a hybrid model where parties have access to a claim-or-refund transaction functionality FCR}* which can be efficiently realized in (a variant of) Bitcoin, and is otherwise independent of the Bitcoin ecosystem. We emphasize that our protocol is dropout-tolerant in the sense that any party that drops out during the protocol is forced to pay a monetary penalty to all other parties. Our formalization and construction generalize both secure computation with penalties of Bentov and <PERSON><PERSON> (Crypto 2014), and secure lottery with penalties of Andrychowicz et al. (Security and Privacy 2014).", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813712"}, {"primary_key": "4377298", "vector": [], "sparse_vector": [], "title": "The Dropper Effect: Insights into Malware Distribution with Downloader Graph Analytics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Malware remains an important security threat, as miscreants continue to deliver a variety of malicious programs to hosts around the world. At the heart of all the malware delivery techniques are executable files (known as downloader trojans or droppers) that download other malware. Because the act of downloading software components from the Internet is not inherently malicious, benign and malicious downloaders are difficult to distinguish based only on their content and behavior. In this paper, we introduce the downloader-graph abstraction, which captures the download activity on end hosts, and we explore the growth patterns of benign and malicious graphs. Downloader graphs have the potential of exposing large parts of the malware download activity, which may otherwise remain undetected. By combining telemetry from anti-virus and intrusion-prevention systems, we reconstruct and analyze 19 million downloader graphs from 5 million real hosts. We identify several strong indicators of malicious activity, such as the growth rate, the diameter, and the Internet access patterns of downloader graphs. Building on these insights, we implement and evaluate a machine learning system for malware detection. Our system achieves a 96.0% true-positive rate, with a 1.0% false-positive rate, and detects malware an average of 9.24 days earlier than existing anti-virus products. We also perform an external validation by examining a sample of unlabeled files that our system detects as malicious, and we find that 41.41% are blocked by anti-virus products.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813724"}, {"primary_key": "4377301", "vector": [], "sparse_vector": [], "title": "A Domain-Specific Language for Low-Level Secure Multiparty Computation Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sharemind is an efficient framework for secure multiparty computations (SMC). Its efficiency is in part achieved through a large set of primitive, optimized SMC protocols that it makes available to applications built on its top. The size of this set has brought with it an issue not present in frameworks with a small number of supported operations: the set of protocols must be maintained, as new protocols are still added to it and possible optimizations for a particular sub-protocol should be propagated into larger protocols working with data of different types.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813664"}, {"primary_key": "4377302", "vector": [], "sparse_vector": [], "title": "Sunlight: Fine-grained Targeting Detection at Scale with Statistical Confidence.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present Sunlight, a system that detects the causes of targeting phenomena on the web -- such as personalized advertisements, recommendations, or content -- at large scale and with solid statistical confidence. Today's web is growing increasingly complex and impenetrable as myriad of services collect, analyze, use, and exchange users' personal information. No one can tell who has what data, for what purposes they are using it, and how those uses affect the users. The few studies that exist reveal problematic effects -- such as discriminatory pricing and advertising -- but they are either too small-scale to generalize or lack formal assessments of confidence in the results, making them difficult to trust or interpret. Sunlight brings a principled and scalable methodology to personal data measurements by adapting well-established methods from statistics for the specific problem of targeting detection. Our methodology formally separates different operations into four key phases: scalable hypothesis generation, interpretable hypothesis formation, statistical significance testing, and multiple testing correction. Each phase bears instantiations from multiple mechanisms from statistics, each making different assumptions and tradeoffs. Sunlight offers a modular design that allows exploration of this vast design space. We explore a portion of this space, thoroughly evaluating the tradeoffs both analytically and experimentally. Our exploration reveals subtle tensions between scalability and confidence. Sunlight's default functioning strikes a balance to provide the first system that can diagnose targeting at fine granularity, at scale, and with solid statistical justification of its results.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813614"}, {"primary_key": "4377303", "vector": [], "sparse_vector": [], "title": "POSTER: Page Table Manipulation Attack.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "HyoungMin Ham", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The kernel exploit attacks have recently become difficult to be launched because executing either malicious scripts or instructions is prohibited by the DEP/NX (Data Execution Prevention/Not Executable). As an alternative way, return-oriented programming (ROP) could be another option to treat the prevention. However, despite lots of cost for making ROP gadgets, it has no guarantee to assemble the proper gadgets. To overcome this limitation, we introduce Page Table Manipulation Attack (PTMA) to alter memory attribute through page table modification. This attack enables an attacker to rewrite memory attribute of protected memory. We show how to find the page table entry of interest in Master Kernel Page Table and modify its attribute in AArch32 and x86-64. The results show that PTMA effectively circumvents the existing kernel exploitation defenses that are based on memory permission.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810121"}, {"primary_key": "4377304", "vector": [], "sparse_vector": [], "title": "POSTER: A Password-based Authentication by Splitting Roles of User Interface.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Han Park", "GyeongYong Bang", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Conventional password-based authentication has been widely used due to its simplicity, familiarity, and cost effectiveness. However, the conventional password-based authentication has a fundamental weak-point that cleartext passwords are kept on client-side devices and networks. In order to acquire a user's password securely, we suggest a novel method that splits the roles of user interface onto two devices. With our method, cleartext passwords are neither stored on any devices nor transmitted over communication channels. Finally, we implement a demo application and analyze our method in the aspects of usability, deployability, and security.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810107"}, {"primary_key": "4377307", "vector": [], "sparse_vector": [], "title": "POSTER: A Hardware Fingerprint Using GPU Core Frequency Variations.", "authors": ["<PERSON><PERSON>", "<PERSON>n <PERSON>", "<PERSON>"], "summary": "Hardware primitives provide significant promises to support cryptographic primitives and security mechanisms against various forms of compromises. In this work, we study the intrinsic hardware characteristics of modern graphics processing units (GPUs) due to random manufacturing variations, and exploits the inherent randomness to generate device-specific signatures. In particular, we present a novel GPU-based hardware fingerprint scheme to generate a unique, stable, physically unclonable, unpredictable, and random bit string from the inherent hardware features of a general purpose GPU (GPGPU). The generated fingerprint can be used to implement a physically unclonable function (PUF), and thus to create a trusted computing environment with GPUs as the trust anchor.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810105"}, {"primary_key": "4377308", "vector": [], "sparse_vector": [], "title": "Seeing Your Face Is Not Enough: An Inertial Sensor-Based Liveness Detection for Face Authentication.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ang Yan", "Hancong Kong", "<PERSON>"], "summary": "Leveraging built-in cameras on smartphones and tablets, face authentication provides an attractive alternative of legacy passwords due to its memory-less authentication process. However, it has an intrinsic vulnerability against the media-based facial forgery (MFF) where adversaries use photos/videos containing victims' faces to circumvent face authentication systems. In this paper, we propose FaceLive, a practical and robust liveness detection mechanism to strengthen the face authentication on mobile devices in fighting the MFF-based attacks. FaceLive detects the MFF-based attacks by measuring the consistency between device movement data from the inertial sensors and the head pose changes from the facial video captured by built-in camera. FaceLive is practical in the sense that it does not require any additional hardware but a generic front-facing camera, an accelerometer, and a gyroscope, which are pervasively available on today's mobile devices. FaceLive is robust to complex lighting conditions, which may introduce illuminations and lead to low accuracy in detecting important facial landmarks; it is also robust to a range of cumulative errors in detecting head pose changes during face authentication.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813612"}, {"primary_key": "4377310", "vector": [], "sparse_vector": [], "title": "POSTER: Toward Energy-Wasting Misbehavior Detection Platform with Privacy Preservation in Building Energy Use.", "authors": ["Depeng Li", "<PERSON><PERSON>"], "summary": "Energy-wasting behavior is a big concern as it wastes around 1/3 of all energy consumption in buildings. Current solutions to address this issue either rarely offer privacy preservation or cannot satisfy occupants' comfort in an acceptable level. In this paper, we first propose an energy-wasting behavior detection platform. Based on that, we address a couple of privacy-related challenges in our platform through utilizing functional encryption to hide video data and by introducing noise disturbance which is mixed with metering data e.g. A/C power consumption signatures. In a privacy framework we proposed, we further quantify the privacy leakage by a set of theoretical models e.g. Hidden Markov model and differential privacy. Since our paper is still at its start phase, we plan to further extend our privacy evaluation model, assess privacy leakage on real-world dataset and accomplish experiments and we wish it could inspire colleagues' interests in this area.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810128"}, {"primary_key": "4377312", "vector": [], "sparse_vector": [], "title": "Insecurity of Voice Solution VoLTE in LTE Mobile Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "VoLTE (Voice-over-LTE) is the designated voice solution to the LTE mobile network, and its worldwide deployment is underway. It reshapes call services from the traditional circuit-switched telecom telephony to the packet-switched Internet VoIP. In this work, we conduct the first study on VoLTE security before its full rollout. We discover several vulnerabilities in both its control-plane and data-plane functions, which can be exploited to disrupt both data and voice in operational networks. In particular, we find that the adversary can easily gain free data access, shut down continuing data access, or subdue an ongoing call, etc. We validate these proof-of-concept attacks using commodity smartphones (rooted and unrooted) in two Tier-1 US mobile carriers. Our analysis reveals that, the problems stem from both the device and the network. The device OS and chipset fail to prohibit non-VoLTE apps from accessing and injecting packets into VoLTE control and data planes. The network infrastructure also lacks proper access control and runtime check.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813618"}, {"primary_key": "4377314", "vector": [], "sparse_vector": [], "title": "Blazing Fast 2PC in the Offline/Online Setting with Security for Malicious Adversaries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recently, several new techniques were presented to dramatically improve key parts of secure two-party computation (2PC) protocols that use the cut-and-choose paradigm on garbled circuits for 2PC with security against malicious adversaries. These include techniques for reducing the number of garbled circuits (<PERSON><PERSON> 13, <PERSON> et al. 13, <PERSON><PERSON> and <PERSON><PERSON> 14, <PERSON> et al. 14) and techniques for reducing the overheads besides garbled circuits (<PERSON><PERSON><PERSON> and <PERSON><PERSON> 13, <PERSON> and <PERSON>~13). We design a highly optimized protocol in the offline/online setting that makes use of all state-of-the-art techniques, along with several new techniques that we introduce. A crucial part of our protocol is a new technique for enforcing consistency of the inputs used by the party who garbles the circuits. This technique has both theoretical and practical advantages over previous methods.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813666"}, {"primary_key": "4377315", "vector": [], "sparse_vector": [], "title": "Secure Deduplication of Encrypted Data without Additional Independent Servers.", "authors": ["<PERSON><PERSON>", "<PERSON>. <PERSON>", "<PERSON>"], "summary": "Encrypting data on client-side before uploading it to a cloud storage is essential for protecting users' privacy. However client-side encryption is at odds with the standard practice of deduplication. Reconciling client-side encryption with cross-user deduplication is an active research topic. We present the first secure cross-user deduplication scheme that supports client-side encryption without requiring any additional independent servers. Interestingly, the scheme is based on using a PAKE (password authenticated key exchange) protocol. We demonstrate that our scheme provides better security guarantees than previous efforts. We show both the effectiveness and the efficiency of our scheme, via simulations using realistic datasets and an implementation.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813623"}, {"primary_key": "4377316", "vector": [], "sparse_vector": [], "title": "Exploiting Temporal Dynamics in Sybil Defenses.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sybil attacks present a significant threat to many Internet systems and applications, in which a single adversary inserts multiple colluding identities in the system to compromise its security and privacy. Recent work has advocated the use of social-network-based trust relationships to defend against Sybil attacks. However, most of the prior security analyses of such systems examine only the case of social networks at a single instant in time. In practice, social network connections change over time, and attackers can also cause limited changes to the networks. In this work, we focus on the temporal dynamics of a variety of social-network-based Sybil defenses. We describe and examine the effect of novel attacks based on: (a) the attacker's ability to modify Sybil-controlled parts of the social-network graph, (b) his ability to change the connections that his Sybil identities maintain to honest users, and (c) taking advantage of the regular dynamics of connections forming and breaking in the honest part of the social network. We find that against some defenses meant to be fully distributed, such as SybilLimit and Persea, the attacker can make dramatic gains over time and greatly undermine the security guarantees of the system. Even against centrally controlled Sybil defenses, the attacker can eventually evade detection (e.g. against SybilInfer and SybilRank) or create denial-of-service conditions (e.g. against Ostra and SumUp). After analysis and simulation of these attacks using both synthetic and real-world social network topologies, we describe possible defense strategies and the trade-offs that should be explored. It is clear from our findings that temporal dynamics need to be accounted for in Sybil defense or else the attacker will be able to undermine the system in unexpected and possibly dangerous ways.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813693"}, {"primary_key": "4377317", "vector": [], "sparse_vector": [], "title": "POSTER: The Popular Apps in Your Pocket Are Leaking Your Privacy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smartphone users are facing serious threat of privacy leakage. This privacy leakage is caused not only by malicious applications (apps), but also by the most popular apps in one's pocket. In this poster, we present our study on the issues of information leakage caused by the most widely used apps in Chinese app markets. Our goal is to find what information is exposed by each popular app, and then to focus on the following three questions in order to explore the influence of this kind of information leakage: (1) to what extent can the information leaked by an app be used to characterize the user's behaviors? (2) to what extent can the information leaked by a number of apps in the same smartphone be used to characterize the user's behaviors' and (3) whether the leaked information from a number of smartphones can be integrated to predict the social behaviors' Preliminary experimental results on the top 50 popular apps in Chinese app markets show the serious situation of this kind of information leakage.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810127"}, {"primary_key": "4377318", "vector": [], "sparse_vector": [], "title": "Thwarting Memory Disclosure with Efficient Hypervisor-enforced Intra-domain Isolation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON>"], "summary": "Exploiting memory disclosure vulnerabilities like the HeartBleed bug may cause arbitrary reading of a victim's memory, leading to leakage of critical secrets such as crypto keys, personal identity and financial information. While isolating code that manipulates critical secrets into an isolated execution environment is a promising countermeasure, existing approaches are either too coarse-grained to prevent intra-domain attacks, or require excessive intervention from low-level software (e.g., hypervisor or OS), or both. Further, few of them are applicable to large-scale software with millions of lines of code. This paper describes a new approach, namely SeCage, which retrofits commodity hardware virtualization extensions to support efficient isolation of sensitive code manipulating critical secrets from the remaining code. SeCage is designed to work under a strong adversary model where a victim application or even the OS may be controlled by the adversary, while supporting large-scale software with small deployment cost. SeCage combines static and dynamic analysis to decompose monolithic software into several compart- ments, each of which may contain different secrets and their corresponding code. Following the idea of separating control and data plane, SeCage retrofits the VMFUNC mechanism and nested paging in Intel processors to transparently provide different memory views for different compartments, while allowing low-cost and transparent invocation across domains without hypervisor intervention.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813690"}, {"primary_key": "4377319", "vector": [], "sparse_vector": [], "title": "When Good Becomes Evil: Keystroke Inference with Smartwatch.", "authors": ["Xiang<PERSON> Liu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "One rising trend in today's consumer electronics is the wearable devices, e.g., smartwatches. With tens of millions of smartwatches shipped, however, the security implications of such devices are not fully understood. Although previous studies have pointed out some privacy concerns about the data that can be collected, like personalized health information, the threat is considered low as the leaked data is not highly sensitive and there is no real attack implemented. In this paper we investigate a security problem coming from sensors in smartwatches, especially the accelerometer. The results show that the actual threat is much beyond people's awareness. Being worn on the wrist, the accelerometer built within a smartwatch can track user's hand movements, which makes inferring user inputs on keyboards possible in theory. But several challenges need to be addressed ahead in the real-world settings: e.g., small and irregular hand movements occur persistently during typing, which degrades the tracking accuracy and sometimes even overwhelms useful signals.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813668"}, {"primary_key": "4377321", "vector": [], "sparse_vector": [], "title": "ASLR-Guard: Stopping Address Space Leakage for Code Reuse Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A general prerequisite for a code reuse attack is that the attacker needs to locate code gadgets that perform the desired operations and then direct the control flow of a vulnerable application to those gadgets. Address Space Layout Randomization (ASLR) attempts to stop code reuse attacks by making the first part of the prerequisite unsatisfiable. However, research in recent years has shown that this protection is often defeated by commonly existing information leaks, which provides attackers clues about the whereabouts of certain code gadgets. In this paper, we present ASLR-Guard, a novel mechanism that completely prevents the leaks of code pointers, and render other information leaks (e.g., the ones of data pointers) useless in deriving code address. The main idea behind ASLR-Guard is to render leak of data pointer useless in deriving code address by separating code and data, provide a secure storage for code pointers, and encode the code pointers when they are treated as data. ASLR-Guard can either prevent code pointer leaks or render their leaks harmless. That is, ASLR-Guard makes it impossible to overwrite code pointers with values that point to or will hijack the control flow to a desired address when the code pointers are dereferenced. We have implemented a prototype of ASLR-Guard, including a compilation toolchain and a C/C++ runtime. Our evaluation results show that (1) ASLR-Guard supports normal operations correctly; (2) it completely stops code address leaks and can resist against recent sophisticated attacks; (3) it imposes almost no runtime overhead (< 1%) for C/C++ programs in the SPEC benchmark. Therefore, ASLR-Guard is very practical and can be applied to secure many applications.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813694"}, {"primary_key": "4377322", "vector": [], "sparse_vector": [], "title": "POSTER: PatchGen: Towards Automated Patch Detection and Generation for 1-Day Vulnerabilities.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Qing Han", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A large fraction of source code in open-source systems such as Linux contain 1-day vulnerabilities. The command \"patch\" is used to apply the patches to source codes, and returns feedback information automatically. Unfortunately, this operation is not always successful when patching directly, and two typical error scenarios may occur as follows. 1. The patch may be applied in wrong place, meaning the fix location should be adjusted in patch. 2. The patch may be applied repeatedly, meaning a verification should be executed before applying. To resolve the above scenarios, we propose PatchGen, a new system to quickly detect and generate patches for 1-day vulnerabilities in OS distributions. Comparing with the previous works on 1-day vulnerabilities detection, PatchGen is able to solve the above two error scenarios and use a quick, syntax-based approach that scales to OS distribution-sized code base no matter the code written in what types of language. We implement the PatchGen prototype, and evaluate it by checking all codes from packages in Ubuntu Maverick/Oneiric, all SourceForge C and C++ projects, and the Linux kernel source. Specifically, it takes less than 10 minutes for PatcheGen to detect 175 1-day vulnerabilities and generate 140 patches for Linux Kernel. All of the results have been manually confirmed and tested in the real systems.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810122"}, {"primary_key": "4377323", "vector": [], "sparse_vector": [], "title": "Demystifying Incentives in the Consensus Computer.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cryptocurrencies like Bitcoin and the more recent Ethereum system allow users to specify scripts in transactions and contracts to support applications beyond simple cash transactions. In this work, we analyze the extent to which these systems can enforce the correct semantics of scripts. We show that when a script execution requires nontrivial computation effort, practical attacks exist which either waste miners' computational resources or lead miners to accept incorrect script results. These attacks drive miners to an ill-fated choice, which we call the verifier's dilemma, whereby rational miners are well-incentivized to accept unvalidated blockchains. We call the framework of computation through a scriptable cryptocurrency a consensus computer and develop a model that captures incentives for verifying computation in it. We propose a resolution to the verifier's dilemma which incentivizes correct execution of certain applications, including outsourced computation, where scripts require minimal time to verify. Finally we discuss two distinct, practical implementations of our consensus computer in real cryptocurrency networks like Ethereum.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813659"}, {"primary_key": "4377329", "vector": [], "sparse_vector": [], "title": "CCFI: Cryptographically Enforced Control Flow Integrity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Control flow integrity (CFI) restricts jumps and branches within a program to prevent attackers from executing arbitrary code in vulnerable programs. However, traditional CFI still offers attackers too much freedom to chose between valid jump targets, as seen in recent attacks.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813676"}, {"primary_key": "4377330", "vector": [], "sparse_vector": [], "title": "CARONTE: Detecting Location Leaks for Deanonymizing Tor Hidden Services.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Anonymity networks such as Tor are a critical privacy-enabling technology. Tor's hidden services provide both client and server anonymity. They protect the location of the server hosting the service and provide encryption at every hop from a client to the hidden service. This paper presents Caronte, a tool to automatically identify location leaks in hidden services, i.e., sensitive information in the content served by the hidden service or its configuration that discloses the server's IP address. Compared to prior techniques that deanonymize hidden services <PERSON><PERSON> implements a novel approach that does not rely on flaws on the Tor protocol and assumes an open-world, i.e., it does not require a short list of candidate servers known in advance. <PERSON><PERSON> visits the hidden service, extracts Internet endpoints and looks up unique strings from the hidden service's content, and examines the hidden service's certificate chain to extract candidate Internet endpoints where the hidden service could be hosted. Then, it validates those candidates by connecting to them. We apply <PERSON><PERSON> to 1,974 hidden services, fully recovering the IP address of 101 (5%) of them.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813667"}, {"primary_key": "4377333", "vector": [], "sparse_vector": [], "title": "Ciphertext-only Cryptanalysis on Hardened Mifare Classic Cards.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Despite a series of attacks, MIFARE Classic is still the world's most widely deployed contactless smartcard on the market. The Classic uses a proprietary stream cipher CRYPTO1 to provide confidentiality and mutual authentication between card and reader. However, once the cipher was reverse engineered, many serious vulnerabilities surfaced. A number of passive and active attacks were proposed that exploit these vulnerabilities. The most severe key recovery attacks only require wireless interaction with a card. System integrators consider such card-only attacks as one of the most serious threat vectors to their MIFARE Classic-based systems, since it allows the adversary to avoid camera detection, which is often present at an access control entrance or public transport gate. However, all card-only attacks proposed in the literature depend on implementation mistakes which can easily be mitigated without breaking backwards compatibility with the existing reader infrastructure. Consequently, many manufactures and system integrators started to deploy \"fixed\" MIFARE Classic cards which are resilient to such vulnerabilities. However, these countermeasures are rather palliating and inadequate for a cryptographically insecure cipher such as CRYPTO1. In support of this proposition, we present a novel cipher-text card-only attack that exploits a crucial and mandatory step in the authentication protocol and which solely depends on the cryptographic weaknesses of the CRYPTO1 cipher. Hence, in order to avoid this attack, all cards and readers should be upgraded to support an alternative authentication protocol which inherently breaks their backwards compatibility. Our attack requires only a few minutes of wireless interaction with the card, in an uncontrolled environment and can be performed with consumer-grade hardware. The information obtained allows an adversary to drop the computational complexity from 2^48 to approximately 2^30, which enabled us to practically recover a secret key from a hardened MIFARE Classic card in about 5 minutes on an single core consumer laptop.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813641"}, {"primary_key": "4377335", "vector": [], "sparse_vector": [], "title": "GRECS: Graph Encryption for Approximate Shortest Distance Queries.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose graph encryption schemes that efficiently support approximate shortest distance queries on large-scale encrypted graphs. Shortest distance queries are one of the most fundamental graph operations and have a wide range of applications. Using such graph encryption schemes, a client can outsource large-scale privacy-sensitive graphs to an untrusted server without losing the ability to query it. Other applications include encrypted graph databases and controlled disclosure systems. We propose GRECS (stands for GRaph EnCryption for approximate Shortest distance queries) which includes three oracle encryption schemes that are provably secure against any semi-honest server. Our first construction makes use of only symmetric-key operations, resulting in a computationally-efficient construction. Our second scheme makes use of somewhat-homomorphic encryption and is less computationally-efficient but achieves optimal communication complexity (i.e. uses a minimal amount of bandwidth). Finally, our third scheme is both computationally-efficient and achieves optimal communication complexity at the cost of a small amount of additional leakage. We implemented and evaluated the efficiency of our constructions experimentally. The experiments demonstrate that our schemes are efficient and can be applied to graphs that scale up to 1.6 million nodes and 11 million edges.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813672"}, {"primary_key": "4377340", "vector": [], "sparse_vector": [], "title": "Nonoutsourceable Scratch-Off Puzzles to Discourage Bitcoin Mining Coalitions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "An implicit goal of Bitcoin's reward structure is to diffuse network influence over a diverse, decentralized population of individual participants. Indeed, Bitcoin's security claims rely on no single entity wielding a sufficiently large portion of the network's overall computational power. Unfortunately, rather than participating independently, most Bitcoin miners join coalitions called mining pools in which a central pool administrator largely directs the pool's activity, leading to a consolidation of power. Recently, the largest mining pool has accounted for more than half of network's total mining capacity. Relatedly, \"hosted mining\" service providers offer their clients the benefit of economies-of-scale, tempting them away from independent participation. We argue that the prevalence of mining coalitions is due to a limitation of the Bitcoin proof-of-work puzzle -- specifically, that it affords an effective mechanism for enforcing cooperation in a coalition. We present several definitions and constructions for \"nonoutsourceable\" puzzles that thwart such enforcement mechanisms, thereby deterring coalitions. We also provide an implementation and benchmark results for our schemes to show they are practical.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813621"}, {"primary_key": "4377341", "vector": [], "sparse_vector": [], "title": "LOOP: Logic-Oriented Opaque Predicate Detection in Obfuscated Binary Code.", "authors": ["Jiang <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Opaque predicates have been widely used to insert superfluous branches for control flow obfuscation. Opaque predicates can be seamlessly applied together with other obfuscation methods such as junk code to turn reverse engineering attempts into arduous work. Previous efforts in detecting opaque predicates are far from mature. They are either ad hoc, designed for a specific problem, or have a considerably high error rate. This paper introduces LOOP, a Logic Oriented Opaque Predicate detection tool for obfuscated binary code. Being different from previous work, we do not rely on any heuristics; instead we construct general logical formulas, which represent the intrinsic characteristics of opaque predicates, by symbolic execution along a trace. We then solve these formulas with a constraint solver. The result accurately answers whether the predicate under examination is opaque or not. In addition, LOOP is obfuscation resilient and able to detect previously unknown opaque predicates. We have developed a prototype of LOOP and evaluated it with a range of common utilities and obfuscated malicious programs. Our experimental results demonstrate the efficacy and generality of LOOP. By integrating LOOP with code normalization for matching metamorphic malware variants, we show that LOOP is an appealing complement to existing malware defenses.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813617"}, {"primary_key": "4377342", "vector": [], "sparse_vector": [], "title": "Constant Communication ORAM with Small Blocksize.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There have been several attempts recently at using homomorphic encryption to increase the efficiency of Oblivious RAM protocols. One of the most successful has been Onion ORAM, which achieves O(1) communication overhead with polylogarithmic server computation. However, it has two drawbacks. It requires a large block size of B = Ω(log6 N) with large constants. Moreover, while it only needs polylogarithmic computation complexity, that computation consists mostly of expensive homomorphic multiplications. In this work, we address these problems and reduce the required block size to Ω(log4 N). We remove most of the homomorphic multiplications while maintaining O(1) communication complexity. Our idea is to replace their homomorphic eviction routine with a new, much cheaper permute-and-merge eviction which eliminates homomorphic multiplications and maintains the same level of security. In turn, this removes the need for layered encryption that Onion ORAM relies on and reduces both the minimum block size and server computation.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813701"}, {"primary_key": "4377343", "vector": [], "sparse_vector": [], "title": "POSTER: Using Unit Testing to Detect Sanitization Flaws.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Input sanitization mechanisms are widely used to mitigate vulnerabilities to injection attacks such as cross-site scripting. Static analysis tools and techniques commonly used to ensure that applications utilize sanitization functions. Dynamic analysis must be to evaluate the correctness of sanitization functions. The proposed approach is based on unit testing to bring the advantages of both static and dynamic techniques to the development time. Our approach introduces a technique to automatically extract the sanitization functions and then evaluate their effectiveness against attacks using automatically generated attack vectors. The empirical results show that the proposed technique can detect security flaws cannot find by the static analysis tools.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810130"}, {"primary_key": "4377344", "vector": [], "sparse_vector": [], "title": "Fast and Secure Three-party Computation: The Garbled Circuit Approach.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many deployments of secure multi-party computation (MPC) in practice have used information-theoretic three-party protocols that tolerate a single, semi-honest corrupt party, since these protocols enjoy very high efficiency. We propose a new approach for secure three-party computation (3PC) that improves security while maintaining practical efficiency that is competitive with traditional information-theoretic protocols. Our protocol is based on garbled circuits and provides security against a single, malicious corrupt party. Unlike information-theoretic 3PC protocols, ours uses a constant number of rounds. Our protocol only uses inexpensive symmetric-key cryptography: hash functions, block ciphers, pseudorandom generators (in particular, no oblivious transfers) and has performance that is comparable to that of <PERSON>'s (semi-honest) 2PC protocol.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813705"}, {"primary_key": "4377345", "vector": [], "sparse_vector": [], "title": "Nomad: Mitigating Arbitrary Cloud Side Channels via Provider-Assisted Migration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent studies have shown a range of co-residency side channels that can be used to extract private information from cloud clients. Unfortunately, addressing these side channels often requires detailed attack-specific fixes that require significant modifications to hardware, client virtual machines (VM), or hypervisors. Furthermore, these solutions cannot be generalized to future side channels. Barring extreme solutions such as single tenancy which sacrifices the multiplexing benefits of cloud computing, such side channels will continue to affect critical services. In this work, we present Nomad, a system that offers vector-agnostic defense against known and future side channels. Nomad envisions a provider-assisted VM migration service, applying the moving target defense philosophy to bound the information leakage due to side channels. In designing Nomad, we make four key contributions: (1) a formal model to capture information leakage via side channels in shared cloud deployments; (2) identifying provider-assisted VM migration as a robust defense for arbitrary side channels; (3) a scalable online VM migration heuristic that can handle large datacenter workloads; and (4) a practical implementation in OpenStack. We show that Nomad is scalable to large cloud deployments, achieves near-optimal information leakage subject to constraints on migration overhead, and imposes minimal performance degradation for typical cloud applications such as web services and Hadoop MapReduce.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813706"}, {"primary_key": "4377347", "vector": [], "sparse_vector": [], "title": "POSTER: PsychoRithm: A Framework for Studying How Human Traits Affect User Response to Security Situations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "User studies to investigate which human traits affect a user's response to cyber security related situations are typically conducted via self-reported surveys. However, it has been observed that factors such as peer perception, socially desirable responding, and responder bias etc. frequently impact the results, which then do not necessarily reflect the actual behavior of the user when subjected to real world security incidents. To mitigate such biases, we developed PsychoRithm - a software system that presents different real-world security scenarios for the subjects and records their real-time reactions to these scenarios. This paper describes the architecture of PsychoRithm, the design choices we had to make, and the challenges we faced in the design process.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810125"}, {"primary_key": "4377349", "vector": [], "sparse_vector": [], "title": "FlowWatcher: Defending against Data Disclosure Vulnerabilities in Web Applications.", "authors": ["<PERSON><PERSON><PERSON>", "Dan O&apos;<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Bugs in the authorisation logic of web applications can expose the data of one user to another. Such data disclosure vulnerabilities are common---they can be caused by a single omitted access control check in the application. We make the observation that, while the implementation of the authorisation logic is complex and therefore error-prone, most web applications only use simple access control models, in which each piece of data is accessible by a user or a group of users. This makes it possible to validate the correct operation of the authorisation logic externally, based on the observed data in HTTP traffic to and from an application.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813639"}, {"primary_key": "4377351", "vector": [], "sparse_vector": [], "title": "Inference Attacks on Property-Preserving Encrypted Databases.", "authors": ["<PERSON> Naveed", "<PERSON><PERSON>", "<PERSON>"], "summary": "Many encrypted database (EDB) systems have been proposed in the last few years as cloud computing has grown in popularity and data breaches have increased. The state-of-the-art EDB systems for relational databases can handle SQL queries over encrypted data and are competitive with commercial database systems. These systems, most of which are based on the design of CryptDB (SOSP 2011), achieve these properties by making use of property-preserving encryption schemes such as deterministic (DTE) and order- preserving encryption (OPE). In this paper, we study the concrete security provided by such systems. We present a series of attacks that recover the plaintext from DTE- and OPE-encrypted database columns using only the encrypted column and publicly-available auxiliary information. We consider well-known attacks, including frequency analysis and sorting, as well as new attacks based on combinatorial optimization.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813651"}, {"primary_key": "4377352", "vector": [], "sparse_vector": [], "title": "WebCapsule: Towards a Lightweight Forensic Engine for Web Browsers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Performing detailed forensic analysis of real-world web security incidents targeting users, such as social engineering and phishing attacks, is a notoriously challenging and time-consuming task. To reconstruct web-based attacks, forensic analysts typically rely on browser cache files and system logs. However, cache files and logs provide only sparse information often lacking adequate detail to reconstruct a precise view of the incident. To address this problem, we need an always-on and lightweight (i.e., low overhead) forensic data collection system that can be easily integrated with a variety of popular browsers, and that allows for recording enough detailed information to enable a full reconstruction of web security incidents, including phishing attacks.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813656"}, {"primary_key": "4377353", "vector": [], "sparse_vector": [], "title": "A Multi-Modal Neuro-Physiological Study of Phishing Detection and Malware Warnings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Detecting phishing attacks (identifying fake vs. real websites) and heeding security warnings represent classical user-centered security tasks subjected to a series of prior investigations. However, our understanding of user behavior underlying these tasks is still not fully mature, motivating further work concentrating at the neuro-physiological level governing the human processing of such tasks.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813660"}, {"primary_key": "4377355", "vector": [], "sparse_vector": [], "title": "Per-Input Control-Flow Integrity.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Control-Flow Integrity (CFI) is an effective approach to mitigating control-flow hijacking attacks. Conventional CFI techniques statically extract a control-flow graph (CFG) from a program and instrument the program to enforce that CFG. The statically generated CFG includes all edges for all possible inputs; however, for a concrete input, the CFG may include many unnecessary edges.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813644"}, {"primary_key": "4377358", "vector": [], "sparse_vector": [], "title": "Observing and Preventing Leakage in MapReduce.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The use of public cloud infrastructure for storing and processing large datasets raises new security concerns. Current solutions propose encrypting all data, and accessing it in plaintext only within secure hardware. Nonetheless, the distributed processing of large amounts of data still involves intensive encrypted communications between different processing and network storage units, and those communications patterns may leak sensitive information. We consider secure implementation of MapReduce jobs, and analyze their intermediate traffic between mappers and reducers. Using datasets that include personal and geographical data, we show how an adversary that observes the runs of typical jobs can infer precise information about their input. We give a new definition of data privacy for MapReduce, and describe two provably-secure, practical solutions. We implement our solutions on top of VC3, a secure implementation of Hadoop, and evaluate their performance.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813695"}, {"primary_key": "4377360", "vector": [], "sparse_vector": [], "title": "Detecting and Exploiting Second Order Denial-of-Service Vulnerabilities in Web Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper describes a new class of denial-of-service (DoS) attack, which we refer to as Second Order DoS attacks. These attacks consist of two phases, one that pollutes a database with junk entries and another that performs a costly operation on these entries to cause resource exhaustion. The main contribution of this paper is a static analysis for detecting second-order DoS vulnerabilities in web applications. We have implemented our analysis in a tool called Torpedo, and we show that Torpedo can successfully detect second-order DoS vulnerabilities in widely used web applications written in PHP. Once our tool discovers a vulnerability, it also performs symbolic execution to generate candidate attack vectors. We evaluate Torpedo on six widely-used web applications and show that it uncovers 37 security vulnerabilities, while reporting 18 false positives.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813680"}, {"primary_key": "4377361", "vector": [], "sparse_vector": [], "title": "The Spy in the Sandbox: Practical Cache Attacks in JavaScript and their Implications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a micro-architectural side-channel attack that runs entirely in the browser. In contrast to previous work in this genre, our attack does not require the attacker to install software on the victim's machine; to facilitate the attack, the victim needs only to browse to an untrusted webpage that contains attacker-controlled content. This makes our attack model highly scalable, and extremely relevant and practical to today's Web, as most desktop browsers currently used to access the Internet are affected by such side channel threats. Our attack, which is an extension to the last-level cache attacks of <PERSON> et al., allows a remote adversary to recover information belonging to other processes, users, and even virtual machines running on the same physical host with the victim web browser. We describe the fundamentals behind our attack, and evaluate its performance characteristics. In addition, we show how it can be used to compromise user privacy in a common setting, letting an attacker spy after a victim that uses private browsing. Defending against this side channel is possible, but the required countermeasures can exact an impractical cost on benign uses of the browser.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813708"}, {"primary_key": "4377363", "vector": [], "sparse_vector": [], "title": "Micropayments for Decentralized Currencies.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Electronic financial transactions in the US, even those enabled by Bitcoin, have relatively high transaction costs. As a result, it becomes infeasible to make micropayments, i.e. payments that are pennies or fractions of a penny. In order to circumvent the cost of recording all transactions, <PERSON> (1996) and <PERSON><PERSON><PERSON> (1997) suggested the notion of a probabilistic payment, that is, one implements payments that have expected value on the order of micro pennies by running an appropriately biased lottery for a larger payment. While there have been quite a few proposed solutions to such lottery-based micropayment schemes, all these solutions rely on a trusted third party to coordinate the transactions; furthermore, to implement these systems in today's economy would require a a global change to how either banks or electronic payment companies (e.g., Visa and Mastercard) handle transactions.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813713"}, {"primary_key": "4377365", "vector": [], "sparse_vector": [], "title": "Leakage-Resilient Authentication and Encryption from Symmetric Cryptographic Primitives.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Leakage-resilient cryptosystems aim to maintain security in situations where their implementation leaks physical information about their internal secrets. Because of their efficiency and usability on a wide range of platforms, solutions based on symmetric primitives (such as block ciphers) are particularly attractive in this context. So far, the literature has mostly focused on the design of leakage-resilient pseudorandom objects (e.g. PRGs, PRFs, PRPs). In this paper, we consider the complementary and practically important problem of designing secure authentication and encryption schemes. For this purpose, we follow a pragmatic approach based on the advantages and limitations of existing leakage-resilient pseudorandom objects, and rely on the (arguably necessary, yet minimal) use of a leak-free component. The latter can typically be instantiated with a block cipher implementation protected by traditional countermeasures, and we investigate how to combine it with the more intensive use of a much more efficient (less protected) block cipher implementation. Based on these premises, we propose and analyse new constructions of leakage-resilient MAC and encryption schemes, which allow fixing security and efficiency drawbacks of previous proposals in this direction. For encryption, we additionally provide a detailed discussion of why previously proposed (indistinguishability based) security definitions cannot capture actual side-channel attacks, and suggest a relaxed and more realistic way to quantify leakage-resilience in this case, by reducing the security of many iterations of the primitive to the security of a single iteration, independent of the security notion guaranteed by this single iteration (that remains hard to define).", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813626"}, {"primary_key": "4377366", "vector": [], "sparse_vector": [], "title": "VCCFinder: Finding Potential Vulnerabilities in Open-Source Projects to Assist Code Audits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite the security community's best effort, the number of serious vulnerabilities discovered in software is increasing rapidly. In theory, security audits should find and remove the vulnerabilities before the code ever gets deployed. However, due to the enormous amount of code being produced, as well as a the lack of manpower and expertise, not all code is sufficiently audited. Thus, many vulnerabilities slip into production systems. A best-practice approach is to use a code metric analysis tool, such as Flawfinder, to flag potentially dangerous code so that it can receive special attention. However, because these tools have a very high false-positive rate, the manual effort needed to find vulnerabilities remains overwhelming. In this paper, we present a new method of finding potentially dangerous code in code repositories with a significantly lower false-positive rate than comparable systems. We combine code-metric analysis with metadata gathered from code repositories to help code review teams prioritize their work. The paper makes three contributions. First, we conducted the first large-scale mapping of CVEs to GitHub commits in order to create a vulnerable commit database. Second, based on this database, we trained a SVM classifier to flag suspicious commits. Compared to Flawfinder, our approach reduces the amount of false alarms by over 99 % at the same level of recall. Finally, we present a thorough quantitative and qualitative analysis of our approach and discuss lessons learned from the results. We will share the database as a benchmark for future research and will also provide our analysis tool as a web service.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813604"}, {"primary_key": "4377367", "vector": [], "sparse_vector": [], "title": "Program Analysis for Mobile Application Integrity and Privacy Enforcement.", "authors": ["<PERSON>"], "summary": "Program analysis has become an essential tool to verify the correctness of programs before these are deployed to end users' computers and devices. Detecting security problems in today's mobile applications by just relying on manual code inspection is unrealistic. Testing is also limited because there is often no guarantee that all the possible paths of execution of an application are tested under all the possible inputs, and so false negatives may arise. Static analysis is a very promising solution but suffers from the dual problem of false positives. A combination of static and dynamic analysis mitigates the disadvantages that arise when static and dynamic analysis are executed individually and is, therefore, the recommended solution to detect and correct application-level cyber security attacks in mobile applications. This tutorial presents both static and dynamic analysis approaches to enforce privacy of mobile applications, and includes a hands-on lab that teaches the audience how to use create a static-analysis solution that verifies the integrity and confidentiality of the data managed by the program itself.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812703"}, {"primary_key": "4377368", "vector": [], "sparse_vector": [], "title": "Where&apos;s <PERSON>?: Precise User Discovery Attacks in Location Proximity Services.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Location proximity schemes have been adopted by social networks and other smartphone apps as a means of balancing user privacy with utility. However, misconceptions about the privacy offered by proximity services have rendered users vulnerable to trilateration attacks that can expose their location. Such attacks have received major publicity. and, as a result, popular service providers have deployed countermeasures for preventing user discovery attacks.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813605"}, {"primary_key": "4377377", "vector": [], "sparse_vector": [], "title": "POSTER: Computations on Encrypted Data in the Internet of Things Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We identify and address two primary challenges for computing on encrypted data in Internet of Things applications: synchronizing encrypted data across devices and selecting an appropriate encryption scheme. We propose a caching mechanism that operates across the three devices, enabling interactive order-preserving encryption schemes on resource-constrained devices. Additionally, the system can use a high-level description of an IoT application to select automatically appropriate encryption for the data on corresponding tiers and their mathematical operations. This assists in fine-tuning and choosing the core parameters for underlying data structures.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810111"}, {"primary_key": "4377379", "vector": [], "sparse_vector": [], "title": "DEMO: Action Recommendation for Cyber Resilience.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We demonstrate an unifying graph-based model for representing the infrastructure, behavior and missions of an enterprise. We introduce an algorithm for recommending resilience establishing actions based on dynamic updates to the models and show its effectiveness both through software simulation as well as live demonstration inside a cloud testbed. Our demonstrate will illustrate the effectiveness of the algorithm for preserving latency based quality of service (QoS).", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810104"}, {"primary_key": "4377381", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>, <PERSON><PERSON>, Coins on Fire!: Penalizing Equivocation By Loss of Bitcoins.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that equivocation, i.e., making conflicting statements to others in a distributed protocol, can be monetarily disincentivized by the use of crypto-currencies such as Bitcoin. To this end, we design completely decentralized non-equivocation contracts, which make it possible to penalize an equivocating party by the loss of its money. At the core of these contracts, there is a novel cryptographic primitive called accountable assertions, which reveals the party's Bitcoin credentials if it equivocates. Non-equivocation contracts are particularly useful for distributed systems that employ public append-only logs to protect data integrity, e.g., in cloud storage and social networks. Moreover, as double-spending in Bitcoin is a special case of equivocation, the contracts enable us to design a payment protocol that allows a payee to receive funds at several unsynchronized points of sale, while being able to penalize a double-spending payer after the fact.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813686"}, {"primary_key": "4377385", "vector": [], "sparse_vector": [], "title": "GUITAR: Piecing Together Android App GUIs from Memory Images.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Zhongshu Gu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An Android app's graphical user interface (GUI) displays rich semantic and contextual information about the smartphone's owner and app's execution. Such information provides vital clues to the investigation of crimes in both cyber and physical spaces. In real-world digital forensics however, once an electronic device becomes evidence most manual interactions with it are prohibited by criminal investigation protocols. Hence investigators must resort to \"image-and-analyze\" memory forensics (instead of browsing through the subject phone) to recover the apps' GUIs. Unfortunately, GUI reconstruction is still largely impossible with state-of-the-art memory forensics techniques, which tend to focus only on individual in-memory data structures. An Android GUI, however, displays diverse visual elements each built from numerous data structure instances. Furthermore, whenever an app is sent to the background, its GUI structure will be explicitly deallocated and disintegrated by the Android framework. In this paper, we present GUITAR, an app-independent technique which automatically reassembles and redraws all apps' GUIs from the multitude of GUI data elements found in a smartphone's memory image. To do so, GUITAR involves the reconstruction of (1) GUI tree topology, (2) drawing operation mapping, and (3) runtime environment for redrawing. Our evaluation shows that GUITAR is highly accurate (80-95% similar to original screenshots) at reconstructing GUIs from memory images taken from a variety of Android apps on popular phones. Moreover, GUITAR is robust in reconstructing meaningful GUIs even when facing GUI data loss.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813650"}, {"primary_key": "4377386", "vector": [], "sparse_vector": [], "title": "VCR: App-Agnostic Recovery of Photographic Evidence from Android Device Memory Images.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Zhongshu Gu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The ubiquity of modern smartphones means that nearly everyone has easy access to a camera at all times. In the event of a crime, the photographic evidence that these cameras leave in a smartphone's memory becomes vital pieces of digital evidence, and forensic investigators are tasked with recovering and analyzing this evidence. Unfortunately, few existing forensics tools are capable of systematically recovering and inspecting such in-memory photographic evidence produced by smartphone cameras. In this paper, we present VCR, a memory forensics technique which aims to fill this void by enabling the recovery of all photographic evidence produced by an Android device's cameras. By leveraging key aspects of the Android framework, VCR extends existing memory forensics techniques to improve vendor-customized Android memory image analysis. Based on this, VCR targets application-generic artifacts in an input memory image which allow photographic evidence to be collected no matter which application produced it. Further, VCR builds upon the Android framework's existing image decoding logic to both automatically recover and render any located evidence. Our evaluation with commercially available smartphones shows that VCR is highly effective at recovering all forms of photographic evidence produced by a variety of applications across several different Android platforms.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813720"}, {"primary_key": "4377388", "vector": [], "sparse_vector": [], "title": "WISCS&apos;15: The 2nd ACM Workshop on Information Sharing and Collaborative Security.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The mission of the 2nd ACM Workshop on Information Sharing and Collaborative Security is to advance the scientific foundations for sharing threat and security-related data among organizations. The call for better information sharing continues to be an important theme in the computer security community and with policy makers. The expectation is that sharing will significantly improve the ability of defenders to detect and mitigate attacks on their networks and systems. Several commercial offerings by security vendors that enable automated sharing have gone live and existing communities have begun to use them. Sharing of security and threat data at scale raises a number of interesting research questions, including on how to best collect, analyze and make use of these data to address important security concerns. In addition sharing raises privacy and other policy issues that need to be addressed.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812627"}, {"primary_key": "4377390", "vector": [], "sparse_vector": [], "title": "TOPAS: 2-Pass Key Exchange with Full Perfect Forward Secrecy and Optimal Communication Complexity.", "authors": ["<PERSON>"], "summary": "We present TOPAS (Transmission Optimal Protocol with Active Security), the first key agreement protocol with optimal communication complexity that provides security against fully active adversaries. This solves a longstanding open problem. The size of the protocol messages (approx. 160 bits for 80-bit security) and the computational costs to generate them are comparable to the basic <PERSON><PERSON><PERSON><PERSON><PERSON>man protocol over elliptic curves (which is well-known to only provide security against passive adversaries). Session keys are indistinguishable from random keys - even under reflection and key compromise impersonation attacks - under generalizations of TOPAS stand out is that it also features a security proof of full perfect forward secrecy (PFS), where the attacker can actively modify messages sent to or from the test-session. The proof of full PFS relies on two new extraction-based security assumptions. It is well-known that existing implicitly-authenticated 2-message protocols like HMQV cannot achieve this strong form of (full) security against active attackers (<PERSON>raw<PERSON>yk, Crypto'05). We also present a variant of our protocol, TOPAS+, which, under the Strong Diffie-Hellman assumption, provides better computational efficiency in the key derivation phase.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813683"}, {"primary_key": "4377396", "vector": [], "sparse_vector": [], "title": "POSTER: Detecting Malicious Web Pages based on Structural Similarity of Redirection Chains.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Detecting malicious web pages used in attacks and building blacklists and signatures from them are done to protect users against drive-by download attacks. Gathering the content on web pages by crawling and evaluating it to check if it is malicious can help in detecting malicious web pages. Methods that apply supervised machine learning to this evaluation are proposed for detecting malicious web pages from a massive amount of web pages. However, these methods need manual inspections for preparing training data when classifiers are retrained in accordance with changes in the content on malicious web pages. In this paper, we propose a method that evaluates whether web pages are malicious and needs only the discrimination results of web pages identified by high-interaction honeyclients to prepare training data. This method evaluates maliciousness on the basis of the structural similarity of redirection chains arising from drive-by download attacks. The results of our experiments with two years of data showed that the accuracy of our method was about 20\\% higher than that of the previous method.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810112"}, {"primary_key": "4377397", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning based on artificial neural networks is a very popular approach to modeling, classifying, and recognizing complex data such as images, speech, and text. The unprecedented accuracy of deep learning methods has turned them into the foundation of new AI-based services on the Internet. Commercial companies that collect user data on a large scale have been the main beneficiaries of this trend since the success of deep learning techniques is directly proportional to the amount of data available for training. Massive data collection required for deep learning presents obvious privacy issues. Users' personal, highly sensitive data such as photos and voice recordings is kept indefinitely by the companies that collect it. Users can neither delete it, nor restrict the purposes for which it is used. Furthermore, centrally kept data is subject to legal subpoenas and extra-judicial surveillance. Many data owners--for example, medical institutions that may want to apply deep learning methods to clinical records--are prevented by privacy and confidentiality concerns from sharing the data and thus benefitting from large-scale deep learning.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813687"}, {"primary_key": "4377398", "vector": [], "sparse_vector": [], "title": "PyCRA: Physical Challenge-Response Authentication For Active Sensors Under Spoofing Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Embedded sensing systems are pervasively used in life- and security-critical systems such as those found in airplanes, automobiles, and healthcare. Traditional security mechanisms for these sensors focus on data encryption and other post-processing techniques, but the sensors themselves often remain vulnerable to attacks in the physical/analog domain. If an adversary manipulates a physical/analog signal prior to digitization, no amount of digital security mechanisms after the fact can help. Fortunately, nature imposes fundamental constraints on how these analog signals can behave. This work presents PyCRA, a physical challenge-response authentication scheme designed to protect active sensing systems against physical attacks occurring in the analog domain. PyCRA provides secure active sensing by continually challenging the surrounding environment via random but deliberate physical probes. By analyzing the responses to these probes, the system is able to ensure that the underlying physics involved are not violated, providing an authentication mechanism that not only detects malicious attacks but provides resilience against them. We demonstrate the effectiveness of PyCRA in detecting and mitigating attacks through several case studies using two sensing systems: (1) magnetic sensors like those found on gear and wheel speed sensors in robotics and automotive, and (2) commercial Radio Frequency Identification (RFID) tags used in many security-critical applications. In doing so, we evaluate both the robustness and the limitations of the PyCRA security scheme, concluding by outlining practical considerations as well as further applications for the proposed authentication mechanism.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813679"}, {"primary_key": "4377399", "vector": [], "sparse_vector": [], "title": "Unearthing Stealthy Program Attacks Buried in Extremely Long Execution Paths.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Yao", "<PERSON><PERSON>"], "summary": "Modern stealthy exploits can achieve attack goals without introducing illegal control flows, e.g., tampering with non-control data and waiting for the modified data to propagate and alter the control flow legally. Existing program anomaly detection systems focusing on legal control flow attestation and short call sequence verification are inadequate to detect such stealthy attacks. In this paper, we point out the need to analyze program execution paths and discover event correlations in large-scale execution windows among millions of instructions. We propose an anomaly detection approach with two-stage machine learning algorithms to recognize diverse normal call-correlation patterns and detect program attacks at both inter- and intra-cluster levels. We implement a prototype of our approach and demonstrate its effectiveness against three real-world attacks and four synthetic anomalies with less than 0.01% false positive rates and 0.1~1.3 ms analysis overhead per behavior instance (1k to 50k function or system calls).", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813654"}, {"primary_key": "4377400", "vector": [], "sparse_vector": [], "title": "POSTER: A Logic Based Network Forensics Model for Evidence Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern-day attackers tend to use sophisticated multi-stage/multi-host attack techniques and anti-forensics tools to cover their attack traces. Due to the current limitations of intrusion detection and forensic analysis tools, reconstructing attack scenarios from evidence left behind by the attackers of an enterprise system is challenging. In particular, reconstructing attack scenarios by using the information from IDS alerts and system logs that have a large number of false positives is a big challenge. In this paper, we present a model and an accompanying software tool that systematically addresses how to resolve the above problems to reconstruct the attack scenario. These problems include a large amount of data including non-relevant data and evidence destroyed by anti-forensic techniques. Our system is based on a Prolog system using known vulnerability databases and an anti-forensics database that we plan to extend to a standardized database like the NIST National Vulnerability Database (NVD). In this model, we use different methods, including mapping the evidence to system vulnerabilities, inductive reasoning and abductive reasoning to reconstruct attack scenarios. The goal of this work is to reduce the investigators' time and effort in reaching definite conclusion about how an attack occurred. Our results indicate that such a reasoning system can be useful for network forensics analysis.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810106"}, {"primary_key": "4377401", "vector": [], "sparse_vector": [], "title": "Moat: Verifying Confidentiality of Enclave Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Security-critical applications constantly face threats from exploits in lower computing layers such as the operating system, virtual machine monitors, or even attacks from malicious administrators. To help protect application secrets from such attacks, there is increasing interest in hardware implementations of primitives for trusted computing, such as Intel's Software Guard Extensions (SGX) instructions. These primitives enable hardware protection of memory regions containing code and data, and provide a root of trust for measurement, remote attestation, and cryptographic sealing. However, vulnerabilities in the application itself, such as the incorrect use of SGX instructions or memory safety errors, can be exploited to divulge secrets. In this paper, we introduce a new approach to formally model these primitives and formally verify properties of so-called enclave programs that use them. More specifically, we create formal models of relevant aspects of SGX, develop several adversary models, and present a sound verification methodology (based on automated theorem proving and information flow analysis) for proving that an enclave program running on SGX does not contain a vulnerability that causes it to reveal secrets to the adversary. We introduce Moat, a tool which formally verifies confidentiality properties of applications running on SGX. We evaluate Moat on several applications, including a one time password scheme, off-the-record messaging, notary service, and secure query processing.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813608"}, {"primary_key": "4377403", "vector": [], "sparse_vector": [], "title": "POSTER: OFX: Enabling OpenFlow Extensions for Switch-Level Security Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Network Security applications that run on Software Defined Networks (SDNs) often need to analyze and process traffic in advanced ways. Existing approaches to adding such functionality to SDNs suffer from either poor performance, or poor deployability. In this paper, we propose and benchmark OFX: an OpenFlow extension framework that provides a better tradeoff between performance and deployability for SDN security applications by allowing them to dynamically install software modules onto network switches.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810120"}, {"primary_key": "4377405", "vector": [], "sparse_vector": [], "title": "CrowdTarget: Target-based Detection of Crowdturfing in Online Social Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Malicious crowdsourcing, also known as crowdturfing, has become an important security problem. However, detecting accounts performing crowdturfing tasks is challenging because human workers manage the crowdturfing accounts such that their characteristics are similar with the characteristics of normal accounts. In this paper, we propose a novel crowdturfing detection method, called CrowdTarget, that aims to detect target objects of crowdturfing tasks (e.g., post, page, and URL) not accounts performing the tasks. We identify that the manipulation patterns of target objects by crowdturfing workers are unique features to distinguish them from normal objects. We apply CrowdTarget to detect collusion-based crowdturfing services to manipulate account popularity on Twitter with artificial retweets. Evaluation results show that CrowdTarget can accurately distinguish tweets receiving crowdturfing retweets from normal tweets. When we fix the false-positive rate at 0.01, the best true-positive rate is up to 0.98.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813661"}, {"primary_key": "4377406", "vector": [], "sparse_vector": [], "title": "The SICILIAN Defense: Signature-based Whitelisting of Web JavaScript.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Whitelisting has become a common practice to ensure the execution of trusted applications. However, its effectiveness in protecting client-side web application code has not yet been established. In this paper, we seek to study the efficacy of signature-based whitelisting approach in preventing script injection attacks. This includes a recently-proposed W3C recommendation called Subresource Integrity (SRI), which is based on raw text signatures of scripts. Our 3-month long measurement study shows that applying such raw signatures require signature updates at an impractical rate. We then present SICILIAN, a novel multi-layered approach for whitelisting scripts that can tolerate changes in them without sacrificing the security. Our solution comes with a deployment model called progressive lockdown, which lets browsers assist the server in composing the whitelist. Such assistance from the browser minimizes the burden of building the signature-based whitelist. Our evaluation on Alexa's top 500 sites and 15 popular PHP applications shows that SICILIAN can be fully applied to 84.7% of the sites and all the PHP applications with updates to the whitelist required roughly once in a month.SICILIAN incurs an average performance overhead of 7.02%.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813710"}, {"primary_key": "4377408", "vector": [], "sparse_vector": [], "title": "From Facepalm to Brain Bender: Exploring Client-Side Cross-Site Scripting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although studies have shown that at least one in ten Web pages contains a client-side XSS vulnerability, the prevalent causes for this class of Cross-Site Scripting have not been studied in depth. Therefore, in this paper, we present a large-scale study to gain insight into these causes. To this end, we analyze a set of 1,273 real-world vulnerabilities contained on the Alexa Top 10k domains using a specifically designed architecture, consisting of an infrastructure which allows us to persist and replay vulnerabilities to ensure a sound analysis. In combination with a taint-aware browsing engine, we can therefore collect important execution trace information for all flaws. Based on the observable characteristics of the vulnerable JavaScript, we derive a set of metrics to measure the complexity of each flaw. We subsequently classify all vulnerabilities in our data set accordingly to enable a more systematic analysis. In doing so, we find that although a large portion of all vulnerabilities have a low complexity rating, several incur a significant level of complexity and are repeatedly caused by vulnerable third-party scripts. In addition, we gain insights into other factors related to the existence of client-side XSS flaws, such as missing knowledge of browser-provided APIs, and find that the root causes for Client-Side Cross-Site Scripting range from unaware developers to incompatible first- and third-party code.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813625"}, {"primary_key": "4377409", "vector": [], "sparse_vector": [], "title": "POSTER: Blackboard-Based Electronic Warfare System.", "authors": ["<PERSON>"], "summary": "With internet-connected, SCADA and cyber-physical systems becoming the next battlefield for crime and warfare, technologies for defending and attacking these systems are growing in prevalence. For entities with significant asset collections that are prospectively vulnerable to this type of an attack, autonomous response, retaliation and attack capabilities are necessary to respond to a growing threat from numerous sectors. This paper presents a command and control technique for cyberwarfare based on the Blackboard Architecture. It discusses the utility of this approach and proposes a distributed command system that can run across multiple nodes of various types.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810109"}, {"primary_key": "4377413", "vector": [], "sparse_vector": [], "title": "TrustOTP: Transforming Smartphones into Secure One-Time Password Tokens.", "authors": ["He Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Two-factor authentication has been widely used due to the vulnerabilities associated with traditional text-based password. One-time password (OTP) plays an indispensable role on authenticating mobile users to critical web services that demand a high level of security. As the smartphones are increasingly gaining popularity nowadays, software-based OTP generators have been developed and installed into smartphones as software apps, which bring great convenience to the users without introducing extra burden. However, software-based OTP solutions cannot guarantee the confidentiality of the generated passwords or even the seeds when the mobile OS is compromised. Moreover, they also suffer from denial-of-service attacks when the mobile OS crashes. Hardware-based OTP tokens can solve these security problems in the software-based OTP solutions; however, it is inconvenient for the users to carry physical tokens with them, particularly, when there are more than one token to be carried. In this paper, we present TrustOTP, a secure one-time password solution that can achieve both the flexibility of software tokens and the security of hardware tokens by using ARM TrustZone technology. TrustOTP can not only protect the confidentiality of the OTPs against a malicious mobile OS, but also guarantee reliable OTP generation and trusted OTP display when the mobile OS is compromised or even crashes. It is flexible to integrate multiple OTP algorithms and instances for different application scenarios on the same smartphone platform without modifying the mobile OS. We develop a prototype of TrustOTP on Freescale i.MX53 QSB. The experimental results show that TrustOTP has small impacts on the mobile OS and its power consumption is low.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813692"}, {"primary_key": "4377417", "vector": [], "sparse_vector": [], "title": "Heisenbyte: Thwarting Memory Disclosure Attacks using Destructive Code Reads.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> J. <PERSON>ol<PERSON>"], "summary": "Vulnerabilities that disclose executable memory pages enable a new class of powerful code reuse attacks that build the attack payload at runtime. In this work, we present Heisenbyte, a system to protect against memory disclosure attacks. Central to Heisenbyte is the concept of destructive code reads -- code is garbled right after it is read. Garbling the code after reading it takes away from the attacker her ability to leverage memory disclosure bugs in both static code and dynamically generated just-in-time code. By leveraging existing virtualization support, <PERSON><PERSON><PERSON><PERSON><PERSON>'s novel use of destructive code reads sidesteps the problem of incomplete binary disassembly in binaries, and extends protection to close-sourced COTS binaries, which are two major limitations of prior solutions against memory disclosure vulnerabilities. Our experiments demonstrate that Heisenbyte can tolerate some degree of imperfect static analysis in disassembled binaries, while effectively thwarting dynamic code reuse exploits in both static and JIT code, at a modest 1.8% average runtime overhead due to virtualization and 16.5% average overhead due to the destructive code reads.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813685"}, {"primary_key": "4377419", "vector": [], "sparse_vector": [], "title": "First Workshop on Cyber-Physical Systems Security and PrivaCy (CPS-SPC): Challenges and Research Directions.", "authors": ["<PERSON><PERSON><PERSON>", "Alvaro <PERSON>", "Rakesh B. Bobba"], "summary": "The First International Workshop on Cyber-Physical Systems Security and PrivaCy (CPS-SPC) is being held in conjunction with the 22nd ACM CCS Conference. The workshop was motivated by several observations. First, cyber-physical systems represent the new frontier for cyber risk. The attack surface imposed by the convergence of computing, communications and physical control represents unique challenges for security researchers and practitioners. Second, majority of the published literature addressing the security and privacy of CPS reflect a field still in its infancy. As such, the overall principles, models, and theories for securing CPS have not yet emerged. Third, the organizers of this workshop strongly felt that a premiere forum associated with a premiere conference was needed for rapidly publishing diverse, multidisciplinary in-progress work on the security and privacy of CPS and galvanizing the research community. The set of accepted papers reflect this vision. Papers span cyber and control-theoretic foundations, intrusion detection, forensics management, vulnerability analysis and elimination, and field studies. We have organized an exciting program for this workshop and look forward to active participation in this and future workshops.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812621"}, {"primary_key": "4377422", "vector": [], "sparse_vector": [], "title": "Differential Privacy with Bounded Priors: Reconciling Utility and Privacy in Genome-Wide Association Studies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Differential privacy (DP) has become widely accepted as a rigorous definition of data privacy, with stronger privacy guarantees than traditional statistical methods. However, recent studies have shown that for reasonable privacy budgets, differential privacy significantly affects the expected utility. Many alternative privacy notions which aim at relaxing DP have since been proposed, with the hope of providing a better tradeoff between privacy and utility.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813610"}, {"primary_key": "4377425", "vector": [], "sparse_vector": [], "title": "Deniable Key Exchanges for Secure Messaging.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In the wake of recent revelations of mass government surveillance, secure messaging protocols have come under renewed scrutiny. A widespread weakness of existing solutions is the lack of strong deniability properties that allow users to plausibly deny sending messages or participating in conversations if the security of their communications is later compromised. Deniable authenticated key exchanges (DAKEs), the cryptographic protocols responsible for providing deniability in secure messaging applications, cannot currently provide all desirable properties simultaneously. We introduce two new DAKEs with provable security and deniability properties in the Generalized Universal Composability framework. Our primary contribution is the introduction of Spawn, the first non-interactive DAKE that offers forward secrecy and achieves deniability against both offline and online judges; Spawn can be used to improve the deniability properties of the popular TextSecure secure messaging application. We also introduce an interactive dual-receiver cryptosystem that can improve the performance of the only existing interactive DAKE with competitive security properties. To encourage adoption, we implement and evaluate the performance of our schemes while relying solely on standard-model assumptions.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813616"}, {"primary_key": "4377427", "vector": [], "sparse_vector": [], "title": "Practical Context-Sensitive CFI.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Asia Slowinska", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Current Control-Flow Integrity (CFI) implementations track control edges individually, insensitive to the context of preceding edges. Recent work demonstrates that this leaves sufficient leeway for powerful ROP attacks. Context-sensitive CFI, which can provide enhanced security, is widely considered impractical for real-world adoption. Our work shows that Context-sensitive CFI (CCFI) for both the backward and forward edge can be implemented efficiently on commodity hardware. We present PathArmor, a binary-level CCFI implementation which tracks paths to sensitive program states, and defines the set of valid control edges within the state context to yield higher precision than existing CFI implementations. Even with simple context-sensitive policies, PathArmor yields significantly stronger CFI invariants than context-insensitive CFI, with similar performance.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813673"}, {"primary_key": "4377428", "vector": [], "sparse_vector": [], "title": "Maneuvering Around Clouds: Bypassing Cloud-based Security Providers.", "authors": ["<PERSON>", "<PERSON>", "W<PERSON><PERSON>", "<PERSON>"], "summary": "The increase of Distributed Denial-of-Service (DDoS) attacks in volume, frequency, and complexity, combined with the constant required alertness for mitigating web application threats, has caused many website owners to turn to Cloud-based Security Providers (CBSPs) to protect their infrastructure. These solutions typically involve the rerouting of traffic from the original website through the CBSP's network, where malicious traffic can be detected and absorbed before it ever reaches the servers of the protected website. The most popular Cloud-based Security Providers do not require the purchase of dedicated traffic-rerouting hardware, but rely solely on changing the DNS settings of a domain name to reroute a website's traffic through their security infrastructure. Consequently, this rerouting mechanism can be completely circumvented by directly attacking the website's hosting IP address. Therefore, it is crucial for the security and availability of these websites that their real IP address remains hidden from potential attackers. In this paper, we discuss existing, as well as novel \"origin-exposing\" attack vectors which attackers can leverage to discover the IP address of the server where a website protected by a CBSP is hosted. To assess the impact of the discussed origin-exposing vectors on the security of CBSP-protected websites, we consolidate all vectors into CloudPiercer, an automated origin-exposing tool, which we then use to conduct the first large-scale analysis of the effectiveness of the origin-exposing vectors. Our results show that the problem is severe: 71.5% of the 17,877 CBSP-protected websites that we tested, expose their real IP address through at least one of the evaluated vectors. The results of our study categorically demonstrate that a comprehensive adoption of CBSPs is harder than just changing DNS records. Our findings can steer CBSPs and site administrators towards effective countermeasures, such as proactively scanning for origin exposure and using appropriate network configurations that can greatly reduce the threat.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813633"}, {"primary_key": "4377429", "vector": [], "sparse_vector": [], "title": "POSTER: Pseudonymizing Client as a Privacy-Preserving Service: A Case Study of CDN.", "authors": ["<PERSON>"], "summary": "While a number of Internet applications determine the optimal service tailored for a client based on its identifier, the client's identifier, if explicitly leaked to the Internet applications, raises critical privacy concerns. To address the privacy problem for those sender-sensitive systems, this paper proposes a privacy-preserving service of pseudonymizing Internet client. Replacing real identifier with pseudonymizing identifier in talking to Internet applications preserves the client's privacy. Delegating sender-sensitive optimization tasks to a trustworthy third party ensures the performance of Internet applications. The usage of pseudonymizing-client service in CDN is presented to improve CDN performance as well as privacy on client's IP address.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810124"}, {"primary_key": "4377431", "vector": [], "sparse_vector": [], "title": "Circuit ORAM: On Tightness of the Goldreich-Ostrovsky Lower Bound.", "authors": ["<PERSON>", "T.<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a new tree-based ORAM scheme called Circuit ORAM. Circuit ORAM makes both theoretical and practical contributions. From a theoretical perspective, Circuit ORAM shows that the well-known Goldreich-Ostrovsky logarithmic ORAM lower bound is tight under certain parameter ranges, for several performance metrics. Therefore, we are the first to give an answer to a theoretical challenge that remained open for the past twenty-seven years. Second, Circuit ORAM earns its name because it achieves (almost) optimal circuit size both in theory and in practice for realistic choices of block sizes. We demonstrate compelling practical performance and show that Circuit ORAM is an ideal candidate for secure multi-party computation applications.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813634"}, {"primary_key": "4377432", "vector": [], "sparse_vector": [], "title": "Seeing through Network-Protocol Obfuscation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Censorship-circumvention systems are designed to help users bypass Internet censorship. As more sophisticated deep-packet-inspection (DPI) mechanisms have been deployed by censors to detect circumvention tools, activists and researchers have responded by developing network protocol obfuscation tools. These have proved to be effective in practice against existing DPI and are now distributed with systems such as Tor. In this work, we provide the first in-depth investigation of the detectability of in-use protocol obfuscators by DPI. We build a framework for evaluation that uses real network traffic captures to evaluate detectability, based on metrics such as the false-positive rate against background (i.e., non obfuscated) traffic. We first exercise our framework to show that some previously proposed attacks from the literature are not as effective as a censor might like. We go on to develop new attacks against five obfuscation tools as they are configured in Tor, including: two variants of obfsproxy, FTE, and two variants of meek. We conclude by using our framework to show that all of these obfuscation mechanisms could be reliably detected by a determined censor with sufficiently low false-positive rates for use in many censorship settings.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813715"}, {"primary_key": "4377433", "vector": [], "sparse_vector": [], "title": "POSTER: PRINCESS: A Secure Cloud File Storage System for Managing Data with Hierarchical Levels of Sensitivity.", "authors": ["<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "PRINCESS (Proxy Re-encryption with INd-Cca security in an Encrypted file Storage System) is a secure storage system which utilizes special proxy re-encryption technology. With PRINCESS, the files encrypted in accordance with the confidentiality levels can be shared among appointed users while remaining encrypted. In this poster/demo, we show the efficiency of PRINCESS, which can be applied to a Body Area Network information sharing, automobile information sharing, etc. This system facilitates the potential for new services that require privacy data to be shared securely via cloud technology.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810123"}, {"primary_key": "4377434", "vector": [], "sparse_vector": [], "title": "Efficient Genome-Wide, Privacy-Preserving Similar Patient Query based on Private Edit Distance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Edit distance has been proven to be an important and frequently-used metric in many human genomic research, with Similar Patient Query (SPQ) being a particularly promising and attractive example. However, due to the widespread privacy concerns on revealing personal genomic data, the scope and scale of many novel use of genome edit distance are substantially limited. While the problem of private genomic edit distance has been studied by the research community for over a decade [6], the state-of-the-art solution [31] is far from even close to be applicable to real genome sequences. In this paper, we propose several private edit distance protocols that feature unprecedentedly high efficiency and precision. Our construction is a combination of a novel genomic edit distance ap- proximation algorithm and new construction of private set difference size protocols. With the private edit distance based secure SPQ primitive, we propose GENSETS, a genome-wide, privacy- preserving similar patient query system. It is able to support search- ing large-scale, distributed genome databases across the nation. We have implemented a prototype of GENSETS. The experimental results show that, with 100 Mbps network connection, it would take GENSETS less than 200 minutes to search through 1 million breast cancer patients (distributed nation-wide in 250 hospitals, each having 4000 patients), based on edit distances between their genomes of lengths about 75 million nucleotides each.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813725"}, {"primary_key": "4377435", "vector": [], "sparse_vector": [], "title": "Location-restricted Services Access Control Leveraging Pinpoint Waveforming.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a novel wireless technique named pinpoint waveforming to achieve the location-restricted service access control, i.e., providing wireless services to users at eligible locations only. The proposed system is inspired by the fact that when two identical wireless signals arrive at a receiver simultaneously, they will constructively interfere with each other to form a boosted signal whose amplitude is twice of that of an individual signal. As such, the location-restricted service access control can be achieved through transmitting at a weak power, so that receivers at undesired locations (where the constructive interference vanishes), will experience a low signal-to-noise ratio (SNR), and hence a high bit error rate that retards the correct decoding of received messages. At the desired location (where the constructive interference happens), the receiver obtains a boosted SNR that enables the correct message decoding. To solve the difficulty of determining an appropriate transmit power, we propose to entangle the original transmit signals with jamming signals of opposite phase. The jamming signals can significantly reduce the SNR at the undesired receivers but cancel each other at the desired receiver to cause no impact. With the jamming entanglement, the transmit power can be any value specified by the system administrator. To enable the jamming entanglement, we create the channel calibration technique that allows the synchronization of transmit signals at the desired location. We develop a prototype system using the Universal Software Defined Radio Peripherals (USRPs). The evaluation results show that the receiver at the desired location obtains a throughput ranging between 0.9 and 0.93, whereas an eavesdropper that is 0.3 meter away from a desired location has a throughput approximately equal to 0.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813709"}, {"primary_key": "4377439", "vector": [], "sparse_vector": [], "title": "Walls Have Ears! Opportunistically Communicating Secret Messages Over the Wiretap Channel: from Theory to Practice.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Guancheng Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Physical layer (PHY) security has aroused great research interest in recent years, exploiting physical uncertainty of wireless channels to provide communication secrecy without placing any computational restrictions on the adversaries under the information-theoretic security model. Particularly, researches have been focused on investigating <PERSON><PERSON><PERSON>'s Wiretap Channel for constructing practical wiretap codes that can achieve simultaneous transmission secrecy and reliability. While theoretically sound, PHY security through the wiretap channel has never been realized in practice, and the feasibility and physical limitations of implementing such channels in the real world are yet to be well understood. In this paper, we design and implement a practical opportunistic secret communication system over the wireless wiretap channel for the first time to our best knowledge. We show that, our system can achieve nearly perfect secrecy given a fixed codeword length by carefully controlling the structure of the parity-check matrix of wiretap codes to strike the proper balance between the transmission rate and secrecy. Our system is implemented and evaluated extensively on a USRP N210-based testbed. The experimental results demonstrate the physical limitations and the feasibility of building practical wiretap channels in both the worst channel case and the case where the sender has only the knowledge of instantaneous channel capacities. Our system design and implementation successfully attempts towards bridging the gap between the theoretical wiretap channel and its practice, alleviating the unrealistic and strong assumptions imposed by the theoretical model.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813702"}, {"primary_key": "4377445", "vector": [], "sparse_vector": [], "title": "POSTER: biTheft: Stealing Your Secrets by Bidirectional Covert Channel Communication with Zero-Permission Android Application.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Android has 81.5% of the smartphone market now, and it is also suffering from the explosive growth of malicious applications (or apps). These apps steal users' secret data and transmit it out of the phones. By analyzing the required permissions and the abnormal behaviors, some malicious apps may be easily detected. However, in this paper, we present a bidirectional covert channel in Android, named biTheft, which steals secrets and privacies covertly without any permission. biTheft firstly collects secret data from a set of unprotected shared resources in Android system. Then, it analyzes and infers secrets from the data. With the Intent mechanism, biTheft transmits secrets by legally launching some activities of other apps without requiring any permission itself. biTheft also monitors the usages and statuses of the shared resources to receive commands from remote server. We implement a biTheft scenario, and demonstrate that some types of secrets can be stolen and transmitted out. With pre-agreement, biTheft dynamically adjusts according with the remote server commands. Comparing with the traditional covert channels, biTheft is more practical in the real world scenarios.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810108"}, {"primary_key": "4377446", "vector": [], "sparse_vector": [], "title": "SPSM 2015: 5th Annual ACM CCS Workshop on Security and Privacy in Smartphones and Mobile Devices.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The 2015 SPSM (Security and Privacy in Smartphones and Mobile Devices) workshop is designed to bring together researchers focusing on smartphones. It is a single day workshop co-located with ACM CCS (Conference on Computer and Communications Security) 2015, designed to provide a venue for interested researchers and practitioners to get together and exchange ideas.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812625"}, {"primary_key": "4377448", "vector": [], "sparse_vector": [], "title": "Mitigating Storage Side Channels Using Statistical Privacy Mechanisms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A storage side channel occurs when an adversary accesses data objects influenced by another, victim computation and infers information about the victim that it is not permitted to learn directly. We bring advances in privacy for statistical databases to bear on storage side-channel defense, and specifically demonstrate the feasibility of applying differentially private mechanisms to mitigate storage side channels in procfs, a pseudo file system broadly used in Linux and Android kernels. Using a principled design with quantifiable security, our approach injects noise into kernel data-structure values that are used to generate procfs contents, but also reestablishes invariants on these noised values so as to not violate assumptions on which procfs or its clients depend. We show that our modifications to procfs can be configured to mitigate known storage side channels while preserving its utility for monitoring and diagnosis.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813645"}, {"primary_key": "4377449", "vector": [], "sparse_vector": [], "title": "Protecting Locations with Differential Privacy under Temporal Correlations.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Concerns on location privacy frequently arise with the rapid development of GPS enabled devices and location-based applications. While spatial transformation techniques such as location perturbation or generalization have been studied extensively, most techniques rely on syntactic privacy models without rigorous privacy guarantee. Many of them only consider static scenarios or perturb the location at single timestamps without considering temporal correlations of a moving user's locations, and hence are vulnerable to various inference attacks. While differential privacy has been accepted as a standard for privacy protection, applying differential privacy in location based applications presents new challenges, as the protection needs to be enforced on the fly for a single user and needs to incorporate temporal correlations between a user's locations. In this paper, we propose a systematic solution to preserve location privacy with rigorous privacy guarantee. First, we propose a new definition, \"$δ$-location set\" based differential privacy, to account for the temporal correlations in location data. Second, we show that the well known $\\ell_1$-norm sensitivity fails to capture the geometric sensitivity in multidimensional space and propose a new notion, sensitivity hull, based on which the error of differential privacy is bounded. Third, to obtain the optimal utility we present a planar isotropic mechanism (PIM) for location perturbation, which is the first mechanism achieving the lower bound of differential privacy. Experiments on real-world datasets also demonstrate that PIM significantly outperforms baseline approaches in data utility.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813640"}, {"primary_key": "4377451", "vector": [], "sparse_vector": [], "title": "Cracking App Isolation on Apple: Unauthorized Cross-App Resource Access on MAC OS~X and iOS.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "On modern operating systems, applications under the same user are separated from each other, for the purpose of protecting them against malware and compromised programs. Given the complexity of today's OSes, less clear is whether such isolation is effective against different kind of cross-app resource access attacks (called XARA in our research). To better understand the problem, on the less-studied Apple platforms, we conducted a systematic security analysis on MAC OS~X and iOS. Our research leads to the discovery of a series of high-impact security weaknesses, which enable a sandboxed malicious app, approved by the Apple Stores, to gain unauthorized access to other apps' sensitive data. More specifically, we found that the inter-app interaction services, including the keychain, WebSocket and NSConnection on OS~X and URL Scheme on the MAC OS and iOS, can all be exploited by the malware to steal such confidential information as the passwords for iCloud, email and bank, and the secret token of Evernote. Further, the design of the app sandbox on OS~X was found to be vulnerable, exposing an app's private directory to the sandboxed malware that hijacks its Apple Bundle ID. As a result, sensitive user data, like the notes and user contacts under Evernote and photos under WeChat, have all been disclosed. Fundamentally, these problems are caused by the lack of app-to-app and app-to-OS authentications. To better understand their impacts, we developed a scanner that automatically analyzes the binaries of MAC OS and iOS apps to determine whether proper protection is missing in their code. Running it on hundreds of binaries, we confirmed the pervasiveness of the weaknesses among high-impact Apple apps. Since the issues may not be easily fixed, we built a simple program that detects exploit attempts on OS~X, helping protect vulnerable apps before the problems can be fully addressed.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813609"}, {"primary_key": "4377453", "vector": [], "sparse_vector": [], "title": "UCognito: Private Browsing without Tears.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Jang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While private browsing is a standard feature, its implementation has been inconsistent among the major browsers. More seriously, it often fails to provide the adequate or even the intended privacy protection. For example, as shown in prior research, browser extensions and add-ons often undermine the goals of private browsing. In this paper, we first present our systematic study of private browsing. We developed a technical approach to identify browser traces left behind by a private browsing session, and showed that Chrome and Firefox do not correctly clear some of these traces. We analyzed the source code of these browsers and discovered that the current implementation approach is to decide the behaviors of a browser based on the current browsing mode (i.e., private or public); but such decision points are scattered throughout the code base. This implementation approach is very problematic because developers are prone to make mistakes given the complexities of browser components (including extensions and add-ons). Based on this observation, we propose a new and general approach to implement private browsing. The main idea is to overlay the actual filesystem with a sandbox filesystem when the browser is in private browsing mode, so that no unintended leakage is allowed and no persistent modification is stored. This approach requires no change to browsers and the OS kernel because the layered sandbox filesystem is implemented by interposing system calls. We have implemented a prototype system called Ucognito on Linux. Our evaluations show that Ucognito, when applied to Chrome and Firefox, stops all known privacy leaks identified by prior work and our current study. More importantly, Ucognito incurs only negligible performance overhead: e.g., 0%-2.5% in benchmarks for standard JavaScript and webpage loading.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813716"}, {"primary_key": "4377454", "vector": [], "sparse_vector": [], "title": "From Collision To Exploitation: Unleashing Use-After-Free Vulnerabilities in Linux Kernel.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Since vulnerabilities in Linux kernel are on the increase, attackers have turned their interests into related exploitation techniques. However, compared with numerous researches on exploiting use-after-free vulnerabilities in the user applications, few efforts studied how to exploit use-after-free vulnerabilities in Linux kernel due to the difficulties that mainly come from the uncertainty of the kernel memory layout. Without specific information leakage, attackers could only conduct a blind memory overwriting strategy trying to corrupt the critical part of the kernel, for which the success rate is negligible.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813637"}, {"primary_key": "4377456", "vector": [], "sparse_vector": [], "title": "Symbolic Execution of Obfuscated Code.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "research-article Share on Symbolic Execution of Obfuscated Code Authors: <AUTHORS>", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813663"}, {"primary_key": "4377463", "vector": [], "sparse_vector": [], "title": "MIST 2015: 7th International Workshop on Managing Insider Security Threats.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we provide a brief summary of the 7th International Workshop on Managing Insider Security Threats (MIST 2015). MIST 2015 is held in conjunction with the 22nd ACM Conference on Computer and Communications Security (ACM CCS 2015). The workshop aims to showcase the most recent challenges and advanced technologies for managing insider security threats", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812622"}, {"primary_key": "4377465", "vector": [], "sparse_vector": [], "title": "POSTER: Lightweight Streaming Authenticated Data Structures.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We develop two novel techniques, FHMT and HWMT, for streaming authenticated data structures to achieve the streaming verifiable computation. By leveraging the computing capability of fully homomorphic encryption, FHMT shifts almost all of the computation tasks to the server, reaching nearly no overhead for the client. HWMT strikes the performance balance between the client and server via the proposed tree decomposition technique over the Merkle tree. We also report our research attempt, SAMT, to construct a more efficient data structure for extremely resource-limited clients without the heavy computation burden on the server.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2810117"}, {"primary_key": "4377466", "vector": [], "sparse_vector": [], "title": "Trusted Display on Untrusted Commodity Platforms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A trusted display service assures the confidentiality and authenticity of content output by a security-sensitive application and thus prevents a compromised commodity operating system or application from surreptitiously reading or modifying the displayed output. Past approaches have failed to provide trusted display on commodity platforms that use modern graphics processing units (GPUs). For example, full GPU virtualization encourages the sharing of GPU address space with multiple virtual machines {\\em without} providing adequate hardware protection mechanisms; e.g., address-space separation and instruction execution control. This paper proposes a new trusted display service that has a minimal trusted code base and maintains full compatibility with commodity computing platforms. The service relies on a GPU separation kernel that (1) defines different types of GPU objects, (2) mediates access to security-sensitive objects, and (3) emulates object whenever required by commodity-platform compatibility. The separation kernel employs a new address-space separation mechanism that avoids the challenging problem of GPU instruction verification without adequate hardware support. The implementation of the trusted-display service has a code base that is two orders of magnitude smaller than other similar services, such as those based on full GPU virtualization. Performance measurements show that the trusted-display overhead added over and above that of the underlying trusted system is fairly modest.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813719"}, {"primary_key": "4377470", "vector": [], "sparse_vector": [], "title": "From Mental Poker to Core Business: Why and How to Deploy Secure Computation Protocols?", "authors": ["<PERSON><PERSON>"], "summary": "Technological innovations in security and privacy are critical to advancing modern computing in our time. I will present an effort involving deployment of experimental commercial applications designed and built as a 'secure multi-party computation protocol for specific tasks,' to be used repetitively to achieve a number of concrete ubiquitous business goals. In these applications, the outputs are calculated in the presence of privacy constraints which prevent parties from sharing their individual inputs directly and openly. I will also discuss what I think are the reasons for the inherent difficulty of developing such routines in general (for achieving business goals). In particular, I will survey what I believe to be the reasons that almost 40 years since secure computation protocols was invented as a basic theoretical notion, capturing specific and then general computational tasks, and in spite of its theoretical and even experimentation success, the notion has not yet been widely and seriously used in achieving routine relevant business goals (in contrast with symmetric key and public key cryptosystems and protocols, which were also proposed 40 years ago and are used extensively, primarily to implement secure authenticated channels). The presentation will also cover the general bottom up methodology used in this effort leading to the design and development process. This exemplifying methodology includes: feasibility study of the specific domain, extraction of business needs which are limited by privacy constraints, application analysis from the perspective of utility metrics and secure computing. Then, the methodology further includes design, implementation, and experimentation, guided by the analysis and employing appropriate protocols, while considering scale and performance constraints, and cost overhead that is tolerable.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2812701"}, {"primary_key": "4377473", "vector": [], "sparse_vector": [], "title": "Towards Automatic Generation of Security-Centric Descriptions for Android Apps.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To improve the security awareness of end users, Android markets directly present two classes of literal app information: 1) permission requests and 2) textual descriptions. Unfortunately, neither can serve the needs. A permission list is not only hard to understand but also inadequate; textual descriptions provided by developers are not security-centric and are significantly deviated from the permissions. To fill in this gap, we propose a novel technique to automatically generate security-centric app descriptions, based on program analysis. We implement a prototype system, DescribeME, and evaluate our system using both DroidBench and real-world Android apps. Experimental results demonstrate that DescribeME enables a promising technique which bridges the gap between descriptions and permissions. A further user study shows that automatically produced descriptions are not only readable but also effectively help users avoid malware and privacy-breaching apps.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813669"}, {"primary_key": "4377474", "vector": [], "sparse_vector": [], "title": "IntegriDB: Verifiable SQL for Outsourced Databases.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents IntegriDB, a system allowing a data owner to outsource storage of a database to an untrusted server, and then enable anyone to perform verifiable SQL queries over that database. Our system handles a rich subset of SQL queries, including multidimensional range queries, JOIN, SUM, MAX/MIN, COUNT, and AVG, as well as (limited) nestings of such queries. Even for tables with 105 entries, IntegriDB has small proofs (a few KB) that depend only logarithmically on the size of the database, low verification time (tens of milliseconds), and feasible server computation (under a minute). Efficient updates are also supported. We prove security of IntegriDB based on known cryptographic assumptions, and demonstrate its practicality and expressiveness via performance measurements and verifiable processing of SQL queries from the TPC-H and TPC-C benchmarks.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813711"}, {"primary_key": "4377477", "vector": [], "sparse_vector": [], "title": "Android Root and its Providers: A Double-Edged Sword.", "authors": ["<PERSON>", "<PERSON><PERSON> She", "<PERSON><PERSON><PERSON>"], "summary": "Android root is the voluntary and legitimate process of gaining the highest privilege and full control over a user's Android device. To facilitate the popular demand, a unique Android root ecosystem has formed where a variety of root providers begin to offer root as a service. Even though legitimate, many convenient one-click root methods operate by exploiting vulnerabilities in the Android system. If not carefully controlled, such exploits can be abused by malware author to gain unauthorized root privilege. To understand such risks, we undertake a study on a number of popular yet mysterious Android root providers focusing on 1) if their exploits are adequately protected. 2) the relationship between their proprietary exploits and publicly available ones. We find that even though protections are usually employed, the effort is substantially undermined by a few systematic and sometimes obvious weaknesses we discover. From one large provider, we are able to extract more than 160 exploit binaries that are well-engineered and up-to date, corresponding to more than 50 families, exceeding the number of exploits we can find publicly. We are able to identify at least 10 device driver exploits that are never reported in the public. Besides, for a popular kernel vulnerability (futex bug), the provider has engineered 89 variants to cover devices with different Android versions and configurations. Even worse, we find few of the exploit binaries can be detected by mobile antivirus software.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813714"}, {"primary_key": "4377478", "vector": [], "sparse_vector": [], "title": "An Empirical Study of Web Vulnerability Discovery Ecosystems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, many organizations have established bounty programs that attract white hat hackers who contribute vulnerability reports of web systems. In this paper, we collect publicly available data of two representative web vulnerability discovery ecosystems (Wooyun and HackerOne) and study their characteristics, trajectory, and impact. We find that both ecosystems include large and continuously growing white hat communities which have provided significant contributions to organizations from a wide range of business sectors. We also analyze vulnerability trends, response and resolve behaviors, and reward structures of participating organizations. Our analysis based on the HackerOne dataset reveals that a considerable number of organizations exhibit decreasing trends for reported web vulnerabilities. We further conduct a regression study which shows that monetary incentives have a significantly positive correlation with the number of vulnerabilities reported. Finally, we make recommendations aimed at increasing participation by white hats and organizations in such ecosystems.", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/2810103.2813704"}, {"primary_key": "4519072", "vector": [], "sparse_vector": [], "title": "Proceedings of the 22nd ACM SIGSAC Conference on Computer and Communications Security, Denver, CO, USA, October 12-16, 2015", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We are pleased to present herein the proceedings of the 2015 ACM Conference on Computer and Communications Security (CCS 2015), held in Denver, Colorado, USA, October 12-16, 2015. We received 660 submissions (not including some initial withdrawals). This is the largest number of submissions received to date by a computer security conference. A Program Committee comprising 120 experts from 20 countries, helped by 338 external reviewers, evaluated these submissions, employing the customary double-blind review procedure. The review process resulted in 128 papers being accepted to the program (with authors from 19 countries), representing an acceptance rate of about 19.4% and providing a very broad coverage of the entire information security area. The review process was organized in three phases. After a first review phase, the authors of each paper were sent at least two preliminary reviews (the vast majority of papers, in fact, got three reviews). Authors were given an opportunity to respond to the comments received. During the second phase, reviews were updated as necessary, and, in some cases, additional reviews were solicited. The first and third phases included comprehensive discussions, and after an intensive final debate, the acceptance decisions were made. This year, we introduced a rebuttal task force, whose 15 members were selected from the Program Committee. The goal of this task force was to go over all the authors' responses (rebuttals) received in the second phase and to ensure that the reviewers properly addressed all valid concerns that the authors had raised. We hope that most authors benefitted from the feedback received from the CCS reviewers, and that the review process has helped them improve their papers (while we are also aware that due to the short reviewing time, at a conference of CCS scale it is unavoidable that a handful of reviews may be possibly lacking in some respect).", "published": "2015-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": ""}]