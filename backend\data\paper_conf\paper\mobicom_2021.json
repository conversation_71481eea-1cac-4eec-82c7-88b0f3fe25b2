[{"primary_key": "2213554", "vector": [], "sparse_vector": [], "title": "LegoDNN: block-grained scaling of deep neural networks for mobile vision.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep neural networks (DNNs) have become ubiquitous techniques in mobile and embedded systems for applications such as image/object recognition and classification. The trend of executing multiple DNNs simultaneously exacerbate the existing limitations of meeting stringent latency/accuracy requirements on resource constrained mobile devices. The prior art sheds light on exploring the accuracy-resource tradeoff by scaling the model sizes in accordance to resource dynamics. However, such model scaling approaches face to imminent challenges: (i) large space exploration of model sizes, and (ii) prohibitively long training time for different model combinations. In this paper, we present LegoDNN, a lightweight, block-grained scaling solution for running multi-DNN workloads in mobile vision systems. LegoDNN guarantees short model training times by only extracting and training a small number of common blocks (e.g. 5 in VGG and 8 in ResNet) in a DNN. At run-time, LegoDNN optimally combines the descendant models of these blocks to maximize accuracy under specific resources and latency constraints, while reducing switching overhead via smart block-level scaling of the DNN. We implement LegoDNN in TensorFlow Lite and extensively evaluate it against state-of-the-art techniques (FLOP scaling, knowledge distillation and model compression) using a set of 12 popular DNN models. Evaluation results show that LegoDNN provides 1,296x to 279,936x more options in model sizes without increasing training time, thus achieving as much as 31.74% improvement in inference accuracy and 71.07% reduction in scaling energy consumptions.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483249"}, {"primary_key": "2213555", "vector": [], "sparse_vector": [], "title": "Lili: liquor quality monitoring based on light signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In industrialized wine production, brewing and aging are two key steps. These two processes require the liquors to be bottled for a long time, sometimes more than ten years. The liquor is vulnerable and highly susceptible to microbial contamination during storage, causing undetectable deterioration. During the production process, wineries control the indoor temperature and carbon dioxide concentration to slow down other microorganisms' reproduction speed. These methods, however, do not prevent pathogenic microorganism growth. Currently, microbial culture methods are not suitable for real-time liquor quality monitoring in wineries. Therefore, we have designed a lightweight monitoring system called Lili, which uses light signals to monitor real-time liquor quality changes. <PERSON><PERSON> detects the changes in surface tension and absorption spectrum caused by microbial metabolites and growth during deterioration. <PERSON><PERSON> employs eight LEDs and one photodiode to achieve fine-grained surface tension and absorption spectrum measurements. By analyzing these changes, <PERSON><PERSON> realizes real-time quality monitoring. In this paper, the characteristic offset degree measurement and the absorption spectrum dimension expansion are two critical technologies. In addition, we implemented countermeasures against ambient light noise and sloshing interference. <PERSON><PERSON>'s surface tension and absorption spectrum measurement errors are only 0.89 mN/m and 2.4%, respectively, making it useful to identify the contamination duration, microorganism content and microorganism composition. These two data points can be used to determine potential issues with liquor quality when the liquor becomes health-threatening or even just contaminated, with an accuracy of 97.5%.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483246"}, {"primary_key": "2213556", "vector": [], "sparse_vector": [], "title": "Vi-liquid: unknown liquid identification with your smartphone vibration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional liquid identification instruments are often unavailable to the general public. This paper shows the feasibility of identifying unknown liquids with commercial lightweight devices, such as a smartphone. The wisdom arises from the fact that different liquid molecules have various viscosity coefficients, so they need to overcome dissimilitude energy barriers during relative motion. With this intuition in mind, we introduce a novel model that measures liquids' viscosity based on active vibration. Yet, it is challenging to build up a robust system utilizing the built-in accelerometer in smartphones. Practical issues include under-sampling, self-interference, and volume change impact. Instead of machine learning, we tackle these issues through multiple signal processing stages to reconstruct the original signals and cancel out the interference. Our approach could achieve the liquid viscosity estimates with a mean relative error of 2.9% and distinguish 30 kinds of liquid with an average accuracy of 95.47%.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448621"}, {"primary_key": "2213557", "vector": [], "sparse_vector": [], "title": "Detection of evil flies: securing air-ground aviation communication.", "authors": ["<PERSON><PERSON> Khan", "<PERSON><PERSON><PERSON>", "An Braeken", "<PERSON>"], "summary": "The aviation community is employing various air traffic control and mobile communication technologies, such as ubiquitous data links, wireless communication architectures and protocols. Recently, software-defined networking (SDN) based architectures (i.e., cockpit network communications environment testing (COMET)) have been proposed for Air-Ground communication. However, an evil can break the communication between a pilot and air traffic control, resulting in a hazardous (or life-threatening) situation up in the air or failure of ground equipment. This paper proposes an efficient evil detection and prevention mechanism (called DoEF) for the COMET architecture. The proposed DoEF utilizes a deep learning-based approach, i.e., long-short term memory (LSTM), to detect the evil flies and provide possible countermeasures. Our preliminary results show that the proposed scheme reduces the detection time and increases the detection accuracy of distributed denial of service (DDoS) attacks for the aviation network.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482869"}, {"primary_key": "2213559", "vector": [], "sparse_vector": [], "title": "Face-Mic: inferring live speech and speaker identity via subtle facial dynamics captured by AR/VR motion sensors.", "authors": ["<PERSON><PERSON>", "Xiangyu Xu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Augmented reality/virtual reality (AR/VR) has extended beyond 3D immersive gaming to a broader array of applications, such as shopping, tourism, education. And recently there has been a large shift from handheld-controller dominated interactions to headset-dominated interactions via voice interfaces. In this work, we show a serious privacy risk of using voice interfaces while the user is wearing the face-mounted AR/VR devices. Specifically, we design an eavesdropping attack, Face-Mic, which leverages speech-associated subtle facial dynamics captured by zero-permission motion sensors in AR/VR headsets to infer highly sensitive information from live human speech, including speaker gender, identity, and speech content. Face-Mic is grounded on a key insight that AR/VR headsets are closely mounted on the user's face, allowing a potentially malicious app on the headset to capture underlying facial dynamics as the wearer speaks, including movements of facial muscles and bone-borne vibrations, which encode private biometrics and speech characteristics. To mitigate the impacts of body movements, we develop a signal source separation technique to identify and separate the speech-associated facial dynamics from other types of body movements. We further extract representative features with respect to the two types of facial dynamics. We successfully demonstrate the privacy leakage through AR/VR headsets by deriving the user's gender/identity and extracting speech information via the development of a deep learning-based framework. Extensive experiments using four mainstream VR headsets validate the generalizability, effectiveness, and high accuracy of Face-Mic.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483272"}, {"primary_key": "2213560", "vector": [], "sparse_vector": [], "title": "Hermes: an efficient federated learning framework for heterogeneous mobile clients.", "authors": ["<PERSON>", "Jingwei Sun", "Pengcheng Li", "<PERSON>", "Hai <PERSON>", "<PERSON><PERSON>"], "summary": "Federated learning (FL) has been a popular method to achieve distributed machine learning among numerous devices without sharing their data to a cloud server. FL aims to learn a shared global model with the participation of massive devices under the orchestration of a central server. However, mobile devices usually have limited communication bandwidth to transfer local updates to the central server. In addition, the data residing across devices is intrinsically statistically heterogeneous (i.e., non-IID data distribution). Learning a single global model may not work well for all devices participating in the FL under data heterogeneity. Such communication cost and data heterogeneity are two critical bottlenecks that hinder from applying FL in practice. Moreover, mobile devices usually have limited computational resources. Improving the inference efficiency of the learned model is critical to deploy deep learning applications on mobile devices. In this paper, we present <PERSON>mes - a communication and inference-efficient FL framework under data heterogeneity. To this end, each device finds a small subnetwork by applying the structured pruning; only the updates of these subnetworks will be communicated between the server and the devices. Instead of taking the average over all parameters of all devices as conventional FL frameworks, the server performs the average on only overlapped parameters across each subnetwork. By applying <PERSON><PERSON>, each device can learn a personalized and structured sparse deep neural network, which can run efficiently on devices. Experiment results show the remarkable advantages of <PERSON><PERSON> over the status quo approaches. <PERSON><PERSON> achieves as high as 32.17% increase in inference accuracy, 3.48× reduction on the communication cost, 1.83× speedup in inference efficiency, and 1.8× savings on energy consumption.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483278"}, {"primary_key": "2213561", "vector": [], "sparse_vector": [], "title": "Co-sense: a learning-based collaborative wireless sensing framework.", "authors": ["<PERSON>", "<PERSON><PERSON> Pang", "<PERSON><PERSON>", "Yuqing Yin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Aiming at problems of under-fitting and poor model robustness in learning-based wireless sensing methods caused by the lack of large-scale wireless sensing datasets, this paper proposes a privacy-friendly collaborative wireless sensing framework, called Co-Sense. It builds a community with multiple clients and a server, which aggregates the clients' local models into a federated model with cross-domain capability. To protect the privacy of users' local data, we innovatively introduce the idea of federated learning into the field of wireless sensing, by uploading users' local model parameters instead of their local data. Then, in response to the uneven computing power of different users' edge devices, we propose a local model update algorithm based on adaptive computing power. Furthermore, a client selection algorithm based on test nodes is designed to reduce the negative influence of malicious clients on Co-Sense. Finally, we evaluate Co-Sense on three well-known public wireless datasets, including the gesture dataset, the activity dataset, and the gait dataset. Experimental results show that the sensing accuracy of Co-Sense is more than 10% higher than that of the most advanced wireless sensing models.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482859"}, {"primary_key": "2213562", "vector": [], "sparse_vector": [], "title": "RFClock: timing, phase and frequency synchronization for distributed wireless networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Ufu<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emerging applications like distributed coordinated beamforming (DCB), intelligent reflector arrays, and networked robotic devices will transform wireless applications. However, for systems-centric work on these topics, the research community must first overcome the hurdle of implementing fine-grained, over-the-air timing synchronization, which is critical for any coordinated operation. To address this gap, this paper presents an open-source design and implementation of 'RFClock' that provides timing, frequency and phase synchronization for software defined radios (SDRs). It shows how RFClock can be used for a practical, 5-node DCB application without modifying existing physical/link layer protocols. By utilizing a leader-follower architecture, RFClock-leader allows follower clocks to synchronize with mean offset under 0.107Hz, and then corrects the time/phase alignment to be within a 5ns deviation. RFClock is designed to operate in generalized environments: as standalone unit, it generates a 10MHz/1PPS signal reference suitable for most commercial-off-the-shelf (COTS) SDRs today; it does not require custom protocol-specific headers or messaging; and it is robust to interference through a frequency-agile operation. Using RFClock for DCB, we verify significant increase in channel gain and low BER in a range of [0 -- 10--3] for different modulation schemes. We also demonstrate performance that is similar to a popular wired solution and significant improvement over a GPS-based solution, while delivering this functionality at a fractional price/power point.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448623"}, {"primary_key": "2213564", "vector": [], "sparse_vector": [], "title": "One tag, two codes: identifying optical barcodes with NFC.", "authors": ["Z<PERSON><PERSON> An", "<PERSON><PERSON><PERSON><PERSON> Lin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Wu", "<PERSON>"], "summary": "Barcodes and NFC have become the de facto standards in the field of automatic identification and data capture. These standards have been widely adopted for many applications, such as mobile payments, advertisements, social sharing, admission control, and so on. Recently, considerable demands require the integration of these two codes (barcode and NFC code) into a single tag for the functional complementation. To achieve the goal of \"one tag, two codes\" (OTTC), this work proposes CoilCode, which takes advantage of the printed electronics to fuse an NFC coil antenna into a QR code on a single layer. The proposed code could be identified by cameras and NFC readers. With the use of the conductive inks, QR code and NFC code have become an essential part of each other: the modules of the QR code facilitate the NFC chip in harvesting energy from the magnetic field, while the NFC antenna itself represents bits of the QR code. Compared to the prior dual-layer OTTC, CoilCode is more compact, cost-effective, flimsy, flexible, and environment-friendly, and also reduces the fabrication complexity considerably. We prototyped hundreds of CoilCodes and conducted comprehensive evaluations (across 4 models of NFC chips and 8 kinds of NFC readers under 13 different system configurations). CoilCode demonstrates high-quality identification results for QR code and NFC functions on a wide range of inputs and under different distortion effects.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448631"}, {"primary_key": "2213566", "vector": [], "sparse_vector": [], "title": "Towards resource-efficient detection-driven processing of multi-stream videos.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>. <PERSON>"], "summary": "Detection-driven video analytics is resource hungry as it depends on running object detectors on video frames. Running an object detection engine (i.e., deep learning models such as YOLO and EfficientDet) for each frame makes video analytics pipelines difficult to achieve real-time processing. In this paper, we leverage selective processing of frames and batching of frames to reduce the overall cost of running detection models on live videos. We discuss several factors that hinder the real-time processing of detection-driven video analytics. We propose a system with configurable knobs and show how to achieve the stability of the system using a Lyapunov-based control strategy. In our setup, heterogeneous edge devices (e.g. mobile phones, cameras) stream videos to a low-resource edge server where frames are selectively processed in batches and the detection results are sent to the cloud or to the edge device for further application-aware processing. Preliminary results on controlling different knobs, such as frame skipping, frame size, and batch size show interesting insights to achieve real-time processing of multi-stream video streams with low overhead and low overall information loss.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482866"}, {"primary_key": "2213570", "vector": [], "sparse_vector": [], "title": "FSA: fronthaul slicing architecture for 5G using dataplane programmable switches.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "5G networks are gaining pace in development and deployment in recent years. One of 5G's key objective is to support a variety of use cases with different Service Level Objectives (SLOs). Slicing is a key part of 5G that allows operators to provide a tailored set of resources to different use cases in order to meet their SLOs. Existing works focus on slicing in the frontend or the C-RAN. However, slicing is missing in the fronthaul network that connects the frontend to the C-RAN. This leads to over-provisioning in the fronthaul and the C-RAN, and also limits the scalability of the network.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483247"}, {"primary_key": "2213571", "vector": [], "sparse_vector": [], "title": "Robust indoor localization with ADS-B.", "authors": ["Alexander Canals", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Similar to satellite-based localization systems, messages sent by aircraft with the ADS-B protocol can be used to estimate the location of a mobile receiver. However, for a robust localization using a least-squares approach, ADS-B messages have to be collected over a long time. We propose a localization method based on matching the received signal with known ADS-B messages from distributed receivers. Our proposed method only requires three seconds of recording. Compared to satellite-based localization methods, this approach also works indoors as the signals sent by aircraft are much stronger.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483257"}, {"primary_key": "2213572", "vector": [], "sparse_vector": [], "title": "Crisp-BP: continuous wrist PPG-based blood pressure measurement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Arterial blood pressure (ABP) monitoring using wearables has emerged as a promising approach to empower users with self-monitoring for effective diagnosis and control of hypertension. However, existing schemes mainly monitor ABP at discrete time intervals, involve some form of user effort, have insufficient accuracy, and require collecting sufficient training data for model development. To tackle these problems, we propose Crisp-BP, a novel ABP monitoring system leveraging the PPG sensor available in commercial wrist-worn devices (e.g., smartwatches or fitness trackers). It enables continuous, accurate, user-independent ABP monitoring and requires no behavior changes during collecting PPG data. The basic idea is to illuminate a skin/tissue, measure the light absorption, and characterize ABP-related blood volume change in the artery. To obtain accurate measurements and relieve the pain of training data collection, we use an arterial pulse extraction method that removes interference caused by capillary pulses. Moreover, we design a contact pressure estimation method to combat the deficiency of PPG waveform being sensitive to the contact pressure between the sensor and the skin. In addition, we leverage the great power of Bidirectional Long Short Term Memory and design a hybrid neural network model to enable user-independent ABP monitoring, so that users do not have to provide training data for model development. Furthermore, we propose a transfer learning method that first extracts general knowledge from online PPG data, then use it to improve the learning of a new model on our target problem. Extensive experiments with 35 participants demonstrate that Crisp-BP obtains the average estimation error of 0.86 mmHg and 1.67 mmHg and the standard deviation error of 6.55 mmHg and 7.31 mmHg for diastolic pressure and systolic pressure, respectively. These errors are within the acceptable range regulated by the FDA's AAMI protocol, which allows average errors of up to 5 mmHg and a standard deviation of up to 8 mmHg. Our results demonstrate that Crisp-BP is promising for improving the diagnosis and control of hypertension as it provides continuousness, comfort, convenience, and accuracy.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483241"}, {"primary_key": "2213574", "vector": [], "sparse_vector": [], "title": "DeepAd: a deep advertising signage system with context-aware advertisement based on IoT technologies.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we design and implement a deep advertising signage system, called DeepAd, with context-aware advertisement and cyber-physical interaction based on Internet of Things (IoT) technologies. In the DeepAd system, instant sensing and diverse interacting features are integrated with an IoT signage, which can (1) transmit multimedia contents and receive specific messages to/from smartphone users, (2) sense and interact with nearby individuals through image sensors, (3) embed context-aware advertisement information in sound waves, and (4) customize the on-screen 3D doll with an audience's face on demand. Through built-in sensors and smartphone interfaces, DeepAd can interact with nearby audiences via real-time multimedia services on the IoT signage in a click-and-drag manner. In addition, DeepAd investigates data-over-sound techniques to send embedded status-related advertisement via background music/voice. Furthermore, DeepAd explores deep learning based face changing and recognition to provide innovative and customized services to smartphone users. This paper demonstrates our current prototype consisting of the Android App, advertising server, and IoT signage.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482864"}, {"primary_key": "2213576", "vector": [], "sparse_vector": [], "title": "MagX: wearable, untethered hands tracking with passive magnets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Accurate tracking of the hands and fingers allows users to employ natural gestures in various interactive applications. Hand tracking also supports health applications, such as monitoring face-touching, a common vector for infectious disease. However, for both types of applications, the utility of hand tracking is often limited by the impracticality of bulky tethered systems (e.g., instrumented gloves) or inherent limitations (e.g., Line of Sight or privacy concerns with vision-based systems). These limitations have severely restricted the adoption of hand tracking in real-world applications. We present MagX, a fully untethered on-body hand tracking system utilizing passive magnets and a novel magnetic sensing platform. Since passive magnets require no maintenance, they can be worn on the hands indefinitely, and only the sensor board needs recharging, akin to a smartwatch.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483260"}, {"primary_key": "2213577", "vector": [], "sparse_vector": [], "title": "Wearable, untethered hands tracking with passive magnets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Accurate tracking of the hands and fingers allows users to employ natural gestures in various interactive applications, e.g., controller-free interaction in augmented reality. Hand tracking also supports health applications, such as monitoring face-touching, a common vector for infectious disease. However, for both types of applications, the utility of hand tracking is often limited by the impracticality of bulky tethered systems (e.g., instrumented gloves) or inherent limitations (e.g., Line of Sight or privacy concerns with vision-based systems). These limitations have severely restricted the adoption of hand tracking in real-world applications. We demonstrate MagX, a fully untethered on-body hand tracking system utilizing passive magnets and a novel magnetic sensing platform. Since passive magnets require no maintenance, they can be worn on the hands indefinitely, and only the sensor board needs recharging, akin to a smartwatch.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3511175"}, {"primary_key": "2213578", "vector": [], "sparse_vector": [], "title": "Octopus: a practical and versatile wideband MIMO sensing platform.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Radio frequency (RF) technologies have achieved a great success in data communication. In recent years, pervasive RF signals are further exploited for sensing; RF sensing has since attracted attentions from both academia and industry. Existing developments mainly employ commodity Wi-Fi hardware or rely on sophisticated SDR platforms. While promising in many aspects, there still remains a gap between lab prototypes and real-life deployments. On one hand, due to its narrow bandwidth and communication-oriented design, Wi-Fi sensing offers a coarse sensing granularity and its performance is very unstable in harsh real-world environments. On the other hand, SDR-based designs may hardly be adopted in practice due to its large size and high cost. To this end, we propose, design, and implement Octopus, a compact and flexible wideband MIMO sensing platform, built using commercial-grade low-power impulse radio. Octopus provides a standalone and fully programmable RF sensing solution; it allows for quick algorithm design and application development, and it specifically leverages the wideband radio to achieve a competent and robust performance in practice. We evaluate the performance of Octopus via micro-benchmarking, and further demonstrate its applicability using representative RF sensing applications, including passive localization, vibration sensing, and human/object imaging.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483267"}, {"primary_key": "2213579", "vector": [], "sparse_vector": [], "title": "MoVi-Fi: motion-robust vital signs waveform recovery via deep interpreted RF sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Vital signs are crucial indicators for human health, and researchers are studying contact-free alternatives to existing wearable vital signs sensors. Unfortunately, most of these designs demand a subject human body to be relatively static, rendering them very inconvenient to adopt in practice where body movements occur frequently. In particular, radio-frequency (RF) based contact-free sensing can be severely affected by body movements that overwhelm vital signs. To this end, we introduce MoVi-Fi as a motion-robust vital signs monitoring system, capable of recovering fine-grained vital signs waveform in a contact-free manner. Being a pure software system, MoVi-Fi can be built on top of virtually any commercial-grade radars. What inspires our design is that RF reflections caused by vital signs, albeit weak, do not totally disappear but are composited with other motion-incurred reflections in a nonlinear manner. As nonlinear blind source separation is inherently hard, MoVi-Fi innovatively employs deep contrastive learning to tackle the problem; this self-supervised method requires no ground truth in training, and it exploits contrastive signal features to distinguish vital signs from body movements. Our experiments with 12 subjects and 80hour data demonstrate that MoVi-Fi accurately recovers vital signs waveform under severe body movements.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483251"}, {"primary_key": "2213582", "vector": [], "sparse_vector": [], "title": "RadioInLight: doubling the data rate of VLC systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Visible Light Communication (VLC) is considered a new paradigm for next-generation wireless communication. Recently, studies show that during the process of VLC transmission, besides the visible light signals, the transmitter also leaks out RF signals through a side channel. What is interesting is that the data transmitted in the VLC channel can be inferred from the leaked RF signals. Fundamentally, it means the leaked RF signals carry a copy of the same data in the VLC channel. In this work, we show for the first time that besides inferring the original VLC data, the leaked side channel can be smartly leveraged to carry new data, significantly increasing the data rate of current VLC systems. To realize this objective, we propose a system named RadioInLight, with designs spanning across hardware and software. Without any dedicated active RF transmission front-end which consumes power and hardware resources, RadioInLight is able to double the data rate of the VLC system by purely manipulating the free passively leaked RF signals without affecting the data rate of the original VLC transmissions.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483271"}, {"primary_key": "2213583", "vector": [], "sparse_vector": [], "title": "Verification: can wifi backscatter replace RFID?", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "WiFi backscatter communication has been proposed to enable battery-free sensors to transmit data using WiFi networks. The main advantage of WiFi backscatter technologies over RFID is that data from their tags can be read using existing WiFi infrastructures instead of specialized readers. This can potentially reduce the complexity and cost of deploying battery-free sensors. Despite extensive work in this area, none of the existing systems are in widespread use today. We hypothesize that this is because WiFi-based backscatter tags do not scale well and their range and capabilities are limited when compared with RFID. To test this hypothesis we conduct several real-world experiments.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448622"}, {"primary_key": "2213584", "vector": [], "sparse_vector": [], "title": "Sonica: an open-source NB-IoT prototyping platform.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this demo, we describe Sonica, an open-source NB-IoT prototype platform. Both radio access and core network components are designed and implemented with the features and characteristics of NB-IoT into account. With its eNB and core network (EPC) components, Sonica can function as an NB-IoT testbed which interacts with commercial off-the-shelf NB-IoT devices. Moreover, Sonica provides a flexible framework that supports quick prototyping for MAC/PHY layers.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3510589"}, {"primary_key": "2213585", "vector": [], "sparse_vector": [], "title": "HeadFi: bringing intelligence to all headphones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Headphones continue to become more intelligent as new functions (e.g., touch-based gesture control) appear. These functions usually rely on auxiliary sensors (e.g., accelerometer and gyroscope) that are available in smart headphones. However, for those headphones that do not have such sensors, supporting these functions becomes a daunting task. This paper presents HeadFi, a new design paradigm for bringing intelligence to headphones. Instead of adding auxiliary sensors into headphones, HeadFi turns the pair of drivers that are readily available inside all headphones into a versatile sensor to enable new applications spanning across mobile health, user-interface, and context-awareness. HeadFi works as a plug-in peripheral connecting the headphones and the pairing device (e.g., a smartphone). The simplicity (can be as simple as only two resistors) and small form factor of this design lend itself to be embedded into the pairing device as an integrated circuit. We envision HeadFi can serve as a vital supplementary solution to existing smart headphone design by directly transforming large amounts of existing \"dumb\" headphones into intelligent ones. We prototype HeadFi on PCB and conduct extensive experiments with 53 volunteers using 54 pairs of non-smart headphones under the institutional review board (IRB) protocols. The results show that HeadFi can achieve 97.2%--99.5% accuracy on user identification, 96.8%--99.2% accuracy on heart rate monitoring, and 97.7%--99.3% accuracy on gesture recognition.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448624"}, {"primary_key": "2213587", "vector": [], "sparse_vector": [], "title": "Heart rate trend forecasting during high-intensity interval training using consumer wearable devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>hrib<PERSON>i", "<PERSON><PERSON><PERSON><PERSON>", "Gunguk Park", "<PERSON><PERSON><PERSON>"], "summary": "High-Intensity Interval Training is one of the most popular and dynamically developing fitness innovations in recent years. Professional runners have used interval training for a long time, alternating between high intensity sprints and low intensity jogging intervals to improve their overall performance. During such exercises, the accurate monitoring and prediction of heart rate dynamics is of particular importance to control the physiological state of a person and prevent possible pathological consequences. At the same time, heart rate estimation using very popular nowadays wearable devices (like smartwatches, fitness belts, etc.) during high-intensity exercises can be quite inaccurate. This inaccuracy mostly happens since the heart rate sensors (photoplethysmogram (PPG) and electrocardiogram (ECG)) are exposed to noises due to motion artifacts. PPG sensor suffers from periodic ambient light saturation due to intensive hand motions. ECG is noisy due to electrode contact area changes by body deformation. To solve the mentioned problem, in the current paper a deep learning framework for motion resistive heart rate estimation is developed. The system combines signal processing approaches for the raw sensor data processing and a deep learning architectures (convolutional and recurrent neural networks) for a real-time heart rate measurements and forecasting future heart rate dynamics.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482870"}, {"primary_key": "2213588", "vector": [], "sparse_vector": [], "title": "RFlens: metasurface-enabled beamforming for IoT communication and sensing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "Beamforming can improve the communication and sensing capabilities for a wide range of IoT applications. However, most existing IoT devices cannot perform beamforming due to form factor, energy, and cost constraints. This paper presents RFlens, a reconfigurable metasurface that empowers low-profile IoT devices with beamforming capabilities. The metasurface consists of many unit-cells, each acting as a phase shifter for signals going through it. By encoding the phase shifting values, RFlens can manipulate electromagnetic waves to \"reshape\" and resteer the beam pattern. We prototype RFlens for 5 GHz Wi-Fi signals. Extensive experiments demonstrate that RFlens can achieve a 4.6 dB median signal strength improvement (up to 9.3 dB) even with a relatively small 16 × 16 array of unit-cells. In addition, RFlens can effectively improve the secrecy capacity of IoT links and enable passive NLoS wireless sensing applications.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483238"}, {"primary_key": "2213589", "vector": [], "sparse_vector": [], "title": "EarGate: gait-based user identification with in-ear microphones.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Human gait is a widely used biometric trait for user identification and recognition. Given the wide-spreading, steady diffusion of ear-worn wearables (Earables) as the new frontier of wearable devices, we investigate the feasibility of earable-based gait identification. Specifically, we look at gait-based identification from the sounds induced by walking and propagated through the musculoskeletal system in the body. Our system, EarGate, leverages an in-ear facing microphone which exploits the earable's occlusion effect to reliably detect the user's gait from inside the ear canal, without impairing the general usage of earphones. With data collected from 31 subjects, we show that EarGate achieves up to 97.26% Balanced Accuracy (BAC) with very low False Acceptance Rate (FAR) and False Rejection Rate (FRR) of 3.23% and 2.25%, respectively. Further, our measurement of power consumption and latency investigates how this gait identification model could live both as a stand-alone or cloud-coupled earable system.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483240"}, {"primary_key": "2213590", "vector": [], "sparse_vector": [], "title": "A nationwide census on wifi security threats: prevalence, riskiness, and the economics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Zhenhua Li", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Carrying over 75% of the last-mile mobile Internet traffic, WiFi has inevitably become an enticing target for various security threats. In this work, we characterize a wide variety of real-world WiFi threats at an unprecedented scale, involving 19 million WiFi access points (APs) mostly located in China, by deploying a crowdsourced security checking system on 14 million mobile devices in the wild. Leveraging the collected data, we reveal the landscape of nationwide WiFi threats for the first time. We find that the prevalence, riskiness, and breakdown of WiFi threats deviate significantly from common understandings and prior studies. In particular, we detect attacks at around 4% of all WiFi APs, uncover that most WiFi attacks are driven by an underground economy, and provide strong evidence of web analytics platforms being the bottleneck of its monetization chain. Further, we provide insightful guidance for defending against WiFi attacks at scale, and some of our efforts have already yielded real-world impact---effectively disrupted the WiFi attack ecosystem.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448620"}, {"primary_key": "2213591", "vector": [], "sparse_vector": [], "title": "Nuberu: reliable RAN virtualization in shared platforms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "RAN virtualization will become a key technology for the last mile of next-generation mobile networks driven by initiatives such as the O-RAN alliance. However, due to the computing fluctuations inherent to wireless dynamics and resource contention in shared computing infrastructure, the price to migrate from dedicated to shared platforms may be too high. Indeed, we show in this paper that the baseline architecture of a base station's distributed unit (DU) collapses upon moments of deficit in computing capacity. Recent solutions to accelerate some signal processing tasks certainly help but do not tackle the core problem: a DU pipeline that requires predictable computing to provide carrier-grade reliability.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483266"}, {"primary_key": "2213592", "vector": [], "sparse_vector": [], "title": "Nuberu: a reliable DU design suitable for virtualization platforms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We demonstrate Nuberu. The scenario consists of a DU under test (DuT), and one or more DUs sharing computing resources. A dashboard lets us control (𝑖) the type of DuT: \"Baseline\", implemented with a legacy full-fledged eNB, or Nuberu; (𝑖𝑖) the number of competing vDUs; and (𝑖𝑖𝑖) their SNR. A second screen shows real-time metrics: (𝑖) the processing latency of the TBs from each vDU instance; (𝑖𝑖) the throughput performance of DuT; (𝑖𝑖𝑖) the processing latency of DU jobs from DuT; and (𝑖𝑣) the ratio of latency constraint violations of DuT jobs. We show how the throughput attained by the baseline DU approach collapses upon sufficiently high computing interference from the competing DUs. Conversely, we show that the DU design introduced in [3] preserves reliability irrespective of the computing interference.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3511173"}, {"primary_key": "2213595", "vector": [], "sparse_vector": [], "title": "Video-based social distancing evaluation in the cosmos testbed pilot site.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hongz<PERSON> Ye", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Social distancing can reduce infection rates in respiratory pandemics such as COVID-19, especially in dense urban areas. Hence, we used the PAWR COSMOS wireless edge-cloud testbed in New York City to design and evaluate two different approaches for social distancing analysis. The first, \\textbf{Auto}mated video-based \\textbf{S}ocial \\textbf{D}istancing \\textbf{A}nalyzer (\\textbf{Auto-SDA}), was designed to measure pedestrians compliance with social distancing protocols using street-level cameras. However, since using street-level cameras can raise privacy concerns, we also developed the \\textbf{B}ird's eye view \\textbf{S}ocial \\textbf{D}istancing \\textbf{A}nalyzer (\\textbf{B-SDA}) which uses bird's eye view cameras, thereby preserving pedestrians' privacy. Both Auto-SDA and B-SDA consist of multiple modules. This demonstration illustrates the roles of these modules and their overall performance in evaluating the compliance of pedestrians with social distancing protocols. Moreover, we demonstrate applying Auto-SDA and B-SDA on videos recorded from cameras deployed on the 2nd and 12th floor of Columbia's Mudd building, respectively.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3510590"}, {"primary_key": "2213596", "vector": [], "sparse_vector": [], "title": "A principled design for passive light communication.", "authors": ["<PERSON><PERSON>", "<PERSON>amalloa", "<PERSON><PERSON>"], "summary": "To take advantage of Visible Light Communication (VLC) for low-power applications, such as IoT tags, researchers have been developing systems to modulate (backscatter) ambient light using LC shutters. Various approaches have been explored for single-pixel transmitters, but without following a principled approach. This has resulted in either relatively low data rates, short ranges, or the need for powerful artificial light sources. This paper takes a step back and proposes a more theoretical framework: ChromaLux. By considering the fundamental characteristics of liquid crystals (birefringence and thickness), we demonstrate that the design space is way larger than previously explored, allowing for much better systems. In particular, we uncover the existence of a transient state where the switching time can be reduced by an order of magnitude without lowering the contrast significantly, improving both range and data rate. Using a prototype, we demonstrate that our framework is applicable to different LCs. Our results show significant improvements over state-of-the-art single-pixel systems, achieving ranges of 50 meters at 1 kbps and with bit-error-rates below 1%.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448629"}, {"primary_key": "2213599", "vector": [], "sparse_vector": [], "title": "HAWK-i: a remote and lightweight thermal imaging-based crowd screening framework.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhigeng Pan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this demonstration, we present an end-to-end assistive method for human body temperature screening system starting from collecting raw data using a thermal camera to identify the suspected individual for combating communicable infectious diseases. We deploy a lightweight MobileNet v2 in resource-constrained Raspberry Pi 4B to detect the human's head and body from the thermal image and use a classifier to determine the temperature from the raw temperature data. The experiments show that although the detection accuracy is not very high, we can reduce the bottleneck from screening time and reduce the exposure for the individuals because of the reduced bottleneck.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3520260"}, {"primary_key": "2213600", "vector": [], "sparse_vector": [], "title": "VI-eye: semantic-based 3D point cloud registration for infrastructure-assisted autonomous driving.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Infrastructure-assisted autonomous driving is an emerging paradigm that aims to make affordable autonomous vehicles a reality. A key technology for realizing this vision is real-time point cloud registration which allows a vehicle to fuse the 3D point clouds generated by its own LiDAR and those on roadside infrastructures such as smart lampposts, which can deliver increased sensing range, more robust object detection, and centimeter-level navigation. Unfortunately, the existing methods for point cloud registration assume two clouds to share a similar perspective and large overlap, which result in significant delay and inaccuracy in real-world infrastructure-assisted driving settings. This paper proposes VI-Eye - the first system that can align vehicle-infrastructure point clouds at centimeter accuracy in real-time. Our key idea is to exploit traffic domain knowledge by detecting a set of key semantic objects including road, lane lines, curbs, and traffic signs. Based on the inherent regular geometries of such semantic objects, VI-Eye extracts a small number of saliency points and leverage them to achieve real-time registration of two point clouds. By allowing vehicles and infrastructures to extract the semantic information in parallel, VI-Eye leads to a highly scalable architecture for infrastructure-assisted autonomous driving. To evaluate the performance of VI-Eye, we collect two new multiview LiDAR point cloud datasets on an indoor autonomous driving testbed and a campus smart lamppost testbed, respectively. They contain total 915 point cloud pairs and cover three roads of 1.12km. Experiment results show that VI-Eye achieves centimeter-level accuracy within around 0.2s, and delivers a 5X improvement in accuracy and 2X speedup over state-of-the-art baselines.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483276"}, {"primary_key": "2213601", "vector": [], "sparse_vector": [], "title": "Design and implementation of a generic 5G user plane function development framework.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In 5G, the requirement of transmission latency is stricter than that in 4G. To enhance transmission efficiency, a user plane function (UPF) with a specific packet processing mechanism is necessary. However, UPF must communicate with the session management function (SMF), which will send the packet processing rules to UPF. Those rules will substantially occupy UPF storage. Moreover, customizing a UPF needs to reconstruct N3, N4, N6, and N9 interfaces, which takes much time for developers. To this end, we propose the user plane function development framework (UPFDF), which modularizes the functions in the UPF, supporting customization to connect different types of packet processing mechanisms. With UPFDF, we address the UPF capacity problem and improve the flexibility of the system.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482867"}, {"primary_key": "2213604", "vector": [], "sparse_vector": [], "title": "Notification privacy protection via unobtrusive gripping hand verification using media sounds.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This work proposes a media sound-based authentication method to protect smartphone notification privacy unobtrusively, which wisely hides or presents sensitive content by verifying who is holding the phone. We show that media sounds, such as the melodies of notification tones (e.g., iPhone message and Samsung whistle) can be directly used to sense and verify the user's gripping hand. Because sounds and vibrations co-exist, we capture two novel responses via the smartphone mic and accelerometer to describe how the individual's contacting palm interferes with the signals in two different domains. Based on the two responses, we develop a convolutional neural network-based algorithm to verify the user. Moreover, because the smartphone sensors are all embedded on the same motherboard, we develop a cross-domain method to validate such hard-to-forge physical relationships among the mic, speaker and accelerometer. They prevent external sounds from cheating the system. Additionally, we consider the notification vibration as a special type of media sound, which also results in two responses, and extend our method to work in the silent mode. Extensive experiments with ten notification tones and four phone models show that our system verifies users with 95% accuracy and prevents replay sounds with 100% accuracy.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483277"}, {"primary_key": "2213605", "vector": [], "sparse_vector": [], "title": "Extracting human behavioral biometrics from robot motions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhao"], "summary": "Motion-controlled robots allow a user to interact with a remote real world without physically reaching it. By connecting cyberspace to the physical world, such interactive teleoperations are promising to improve remote education, virtual social interactions and online participatory activities. This work builds up a motion-controlled robotic arm framework and proposes to verify who is controlling the robotic arm by examining the robotic arm's behavior. We show that a robotic arm's motion inherits its human controller's behavioral biometric in interactive control scenarios. Furthermore, we derive the unique robotic motion features to capture the user's behavioral biometric embedded in the robot motions and develop learning-based algorithms to verify the robotic arm user. Extensive experiments show that our system achieves high accuracy to distinguish users while using the robot's behaviors.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482860"}, {"primary_key": "2213608", "vector": [], "sparse_vector": [], "title": "Visage: enabling timely analytics for drone imagery.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Analytics with three-dimensional imagery from drones are driving the next generation of remote monitoring applications. Today, there is an unmet need in providing such analytics in an interactive manner, especially over weak Internet connections, to quickly diagnose and solve problems in the commercial industry space of monitoring assets using drones in remote parts of the world. Existing mechanisms either compromise on the quality of insights by not building 3D images and analyze individual 2D images in isolation, or spend tens of minutes building a 3D image before obtaining and uploading insights. We present Visage, a system that accelerates 3D image analytics by identifying smaller parts of the data that can actually benefit from 3D analytics and prioritizing building, and uploading the localized 3D images for those parts. To achieve this, Visa<PERSON> uses a graph to represent raw 2D images and their relative content overlap, and then identifies the various subgraphs using application knowledge that are good candidates for localized 3D image based insights. We evaluate Visage using data from multiple real deployments and show that it can reduce analytics-latency by up to four orders of magnitude.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483273"}, {"primary_key": "2213609", "vector": [], "sparse_vector": [], "title": "Flexible high-resolution object detection on edge devices with tunable latency.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yuanchao Shu", "<PERSON><PERSON>"], "summary": "Object detection is a fundamental building block of video analytics applications. While Neural Networks (NNs)-based object detection models have shown excellent accuracy on benchmark datasets, they are not well positioned for high-resolution images inference on resource-constrained edge devices. Common approaches, including down-sampling inputs and scaling up neural networks, fall short of adapting to video content changes and various latency requirements. This paper presents Remix, a flexible framework for high-resolution object detection on edge devices. <PERSON> takes as input a latency budget, and come up with an image partition and model execution plan which runs off-the-shelf neural networks on non-uniformly partitioned image blocks. As a result, it maximizes the overall detection accuracy by allocating various amount of compute power onto different areas of an image. We evaluate Remix on public dataset as well as real-world videos collected by ourselves. Experimental results show that <PERSON> can either improve the detection accuracy by 18%-120% for a given latency budget, or achieve up to 8.1× inference speedup with accuracy on par with the state-of-the-art NNs.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483274"}, {"primary_key": "2213610", "vector": [], "sparse_vector": [], "title": "Long-range ambient LoRa backscatter with parallel decoding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LoRa backscatter is a promising technology to achieve low-power and long-distance communication for connecting millions of devices in the Internet of Things. We present P2LoRa, the first ambient LoRa backscatter system with parallel decoding and long-range communication. The high level idea of P2LoRa is to modulate data by shifting ambient LoRa packets with a small frequency. To achieve long distance communication, we enhance the SNR of the backscatter signal by concentrating leaked energy in both the frequency domain and time domain. We propose a method to accurately reconstruct and cancel the in-band excitation signal, which is orders of magnitude higher than the backscatter signal. For parallel decoding, we propose a method to cancel inter-tag interference with very low overhead and address the signal misalignment problem due to different time of flight. We prototype the P2LoRa tag with customized low-cost hardware and implement the P2LoRa gateway on USRP. Through extensive evaluations, we show that P2LoRa achieves a long communication distance of 2.2 km with ambient LoRa, and supports 101 parallel tag transmissions.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483261"}, {"primary_key": "2213611", "vector": [], "sparse_vector": [], "title": "Experience: developing a usable battery drain testing and diagnostic tool for the mobile industry.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we report on our 6-year experience developing Eagle Tester (eTester for short) - a mobile battery drain testing and diagnostic tool. We show how eTester evolved from an \"academic\" prototype to a fully automated tool usable by the mobile industry.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483269"}, {"primary_key": "2213613", "vector": [], "sparse_vector": [], "title": "Physics-inspired heuristics for soft MIMO detection in 5G new radio and beyond.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Overcoming the conventional trade-off between throughput and bit error rate (BER) performance, versus computational complexity is a long-term challenge for uplink Multiple-Input Multiple-Output (MIMO) detection in base station design for the cellular 5G New Radio roadmap, as well as in next generation wireless local area networks. In this work, we present ParaMax, a MIMO detector architecture that for the first time brings to bear physics-inspired parallel tempering algorithmic techniques [28, 50, 67] on this class of problems. ParaMax can achieve near optimal maximum-likelihood (ML) throughput performance in the Large MIMO regime, Massive MIMO systems where the base station has additional RF chains, to approach the number of base station antennas, in order to support even more parallel spatial streams. ParaMax is able to achieve a near ML-BER performance up to 160 × 160 and 80 × 80 Large MIMO for low-order modulations such as BPSK and QPSK, respectively, only requiring less than tens of processing elements. With respect to Massive MIMO systems, in 12 × 24 MIMO with 16-QAM at SNR 16 dB, ParaMax achieves 330 Mbits/s near-optimal system throughput with 4--8 processing elements per subcarrier, which is approximately 1.4× throughput than linear detector-based Massive MIMO systems.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448619"}, {"primary_key": "2213614", "vector": [], "sparse_vector": [], "title": "Long-range accurate ranging of millimeter-wave retro-reflective tags in high mobility.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we demonstrate Adaptive Millimetro as an extension of Millimetro, an ultra-low power millimeter-wave (mmWave) retro-reflector presented in [1], for high mobility scenarios. Adaptive Millimetro makes use of automotive radars and enables communication with and accurate localization of roadside infrastructure overextended distances (i.e. >100m). Millimetro achieves this by designing ultra-low-power retro-reflective tags that operate in the mmWave frequency band and can be embedded in road signs, pavements, bi-cycles, or even the clothing of pedestrians. Millimetro addresses the severe path loss problem of mmWave signals by combining coding gain and retro-reflective antenna front-end to achieve long-range operation. However, highly mobile scenarios may still experience unreliable performance due to the Doppler effect changing the received signals. In this paper, we demonstrate a simple solution for robust localization in high mobility by implementing a Moving Target Indication (MTI) filter and an adaptive Kalman filter. We also present an augmented reality app, as an in-car AR platform, that uses Adaptive Millimetro's algorithms to estimate the tag positions and overlay a virtual box at the estimated locations.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3510592"}, {"primary_key": "2213617", "vector": [], "sparse_vector": [], "title": "Nervion: a cloud native RAN emulator for scalable and flexible mobile core evaluation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given the wide interest on mobile core systems and their pivotal role in the operations of current and future mobile network services, we focus on the issue of their effective evaluation, considering the radio access network (RAN) emulation methodology. While there exist a number of different RAN emulators, following different paradigms, they are limited in their scalability and flexibility, and moreover there is no one commonly accepted RAN emulator. Motivated by this, we present <PERSON>ervion, a scalable and flexible RAN emulator for mobile core system evaluation that takes a novel cloud-native approach. Nervion embeds innovations to enable scalability via abstractions and RAN element containerization, and additionally supports an even more scalable control-plane only mode. It also offers ample flexibility in terms of realizing arbitrary RAN emulation scenarios, mapping them to compute clusters, and evaluating diverse core system designs. We develop a prototype implementation of Nervion that supports 4G and 5G standard compliant RAN emulation and integrate it into the Powder platform to benefit the research community. Our experimental evaluations validate its correctness and demonstrate its scalability relative to representative set of existing RAN emulators. We also present multiple case studies using Nervion that highlight its flexibility to support diverse types of mobile core evaluations.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483248"}, {"primary_key": "2213618", "vector": [], "sparse_vector": [], "title": "Nervion: a cloud native RAN emulator for core network evaluations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the mobile networks evolving towards a software-based architecture with 5G, the research community has proposed several alternative core designs to address the issues recognized with the 4G core network architecture. It is notable, however, that these proposals are evaluated in bespoke ways which do not allow evaluate other proposals or even standard-compliant core networks, presenting several limitations in terms of the number of devices and the network load patterns that can be generated. To this end, we present Nervion, a cloud-native RAN emulator for scalable and flexible core network evaluations. Nervion leverages a compute cluster via containerization to emulate a large number of standard-compliant UEs and eNBs/gNBs generating workloads along both the control- and data-plane with a high degree of customization. This demo highlights the features of Nervion via the evaluation of a 5G core network and serves as a guide on how to use the public profile of Nervion on the Powder platform.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3510588"}, {"primary_key": "2213619", "vector": [], "sparse_vector": [], "title": "Federated mobile sensing for activity recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite advances in hardware and software enabling faster on-device inference, training Deep Neural Networks (DNN) models has largely been a long-running task over TBs of collected user data in centralised repositories. Federated Learning has emerged as an alternative, privacy-preserving paradigm to train models without accessing directly on-device data, by leveraging device resources to create per client updates and aggregate centrally. This has been applied to various tasks, ranging from next-word prediction to automatic speech recognition (ASR). In this tutorial, we recognise on-device sensing as a privacy-sensitive task and build a federated learning system from scratch to showcase how to train a model for accelerometer-based activity recognition in a federated manner. In addition, we present the current landscape and challenges in the realm of federated learning and mobile sensing and provide guidelines on how to build such systems in a privacy-preserving and scalable manner.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3488031"}, {"primary_key": "2213620", "vector": [], "sparse_vector": [], "title": "mSAIL: milligram-scale multi-modal sensor platform for monarch butterfly migration tracking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yi Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> A. <PERSON> II", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Each fall, millions of monarch butterflies across the northern US and Canada migrate up to 4,000 km to overwinter in the exact same cluster of mountain peaks in central Mexico. To track monarchs precisely and study their navigation, a monarch tracker must obtain daily localization of the butterfly as it progresses on its 3-month journey. And, the tracker must perform this task while having a weight in the tens of milligram (mg) and measuring a few millimeters (mm) in size to avoid interfering with monarch's flight. This paper proposes mSAIL, 8 × 8 × 2.6 mm and 62 mg embedded system for monarch migration tracking, constructed using 8 prior custom-designed ICs providing solar energy harvesting, an ultra-low power processor, light/temperature sensors, power management, and a wireless transceiver, all integrated and 3D stacked on a micro PCB with an 8 × 8 mm printed antenna. The proposed system is designed to record and compress light and temperature data during the migration path while harvesting solar energy for energy autonomy, and wirelessly transmit the data at the overwintering site in Mexico, from which the daily location of the butterfly can be estimated using a deep learning-based localization algorithm. A 2-day trial experiment of mSAIL attached on a live butterfly in an outdoor botanical garden demonstrates the feasibility of individual butterfly localization and tracking.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483263"}, {"primary_key": "2213621", "vector": [], "sparse_vector": [], "title": "FLUID-XP: flexible user interface distribution for cross-platform experience.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Oh", "<PERSON>", "Insik Shin"], "summary": "Being able to use a single app across multiple devices can bring novel experiences to the users in various domains including entertainment and productivity. For instance, a user of a video editing app would be able to use a smart pad as a canvas and a smartphone as a remote toolbox so that the toolbox does not occlude the canvas during editing. However, existing approaches do not properly support the single-app multi-device execution due to several limitations, including high development cost, device heterogeneity, and high performance requirement. In this paper, we introduce FLUID-XP, a novel cross-platform multi-device system that enables UIs of a single app to be executed across heterogeneous platforms, while overcoming the limitations of previous approaches. FLUID-XP provides flexible, efficient, and seamless interactions by addressing three main challenges: i) how to transparently enable a single-display app to use multiple displays, ii) how to distribute UIs across heterogeneous devices with minimal network traffic, and iii) how to optimize the UI distribution process when multiple UIs have different distribution requirements. Our experiments with a working prototype of FLUID-XP on Android confirm that FLUID-XP successfully supports a variety of unmodified real-world apps across heterogeneous platforms (Android, iOS, and Linux). We also conduct a lab study with 25 participants to demonstrate the effectiveness of FLUID-XP with real users.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483245"}, {"primary_key": "2213622", "vector": [], "sparse_vector": [], "title": "Experience: a five-year retrospective of MobileInsight.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper reports our five-year lessons of developing and using MobileInsight, an open-source community tool to enable software-defined full-stack, runtime mobile network analytics inside our phones. We present how MobileInsight evolves from a simple monitor to a community toolset with cross-layer analytics, energy-efficient real-time user-plane analytics, and extensible user-friendly analytics at the control and user planes. These features are enabled by various novel techniques, including cross-layer state machine tracking, missing data inference, and domain-specific cross-layer sampling. Their powerfulness is exemplified with a 5-year longitudinal study of operational mobile network latency using a 6.4TB dataset with 6.1 billion over-the-air messages. We further share lessons and insights of using MobileInsight by the community, as well as our visions of MobileInsight's past, present, and future.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448138"}, {"primary_key": "2213623", "vector": [], "sparse_vector": [], "title": "SMART: screen-based gesture recognition on commodity mobile devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In-air gesture control extends a touch screen and enables contactless interaction, thus has become a popular research direction in the past few years. Prior work has implemented this functionality based on cameras, acoustic signals, and Wi-Fi via existing hardware on commercial devices. However, these methods have low user acceptance. Solutions based on cameras and acoustic signals raise privacy concerns, while WiFi-based solutions are vulnerable to background noise. As a result, these methods are not commercialized and recent flagship smartphones have implemented in-air gesture recognition by adding extra hardware on-board, such as mmWave radar and depth camera. The question is, can we support in-air gesture control on legacy devices without any hardware modifications?", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3511174"}, {"primary_key": "2213624", "vector": [], "sparse_vector": [], "title": "SMART: screen-based gesture recognition on commodity mobile devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In-air gesture control extends a touch screen and enables contact-less interaction, thus has become a popular research direction in the past few years. Prior work has implemented this functionality based on cameras, acoustic signals, and Wi-Fi via existing hardware on commercial devices. However, these methods have low user acceptance. Solutions based on cameras and acoustic signals raise privacy concerns, while WiFi-based solutions are vulnerable to background noise. As a result, these methods are not commercialized and recent flagship smartphones have implemented in-air gesture recognition by adding extra hardware on-board, such as mmWave radar and depth camera. The question is, can we support in-air gesture control on legacy devices without any hardware modifications?", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483243"}, {"primary_key": "2213625", "vector": [], "sparse_vector": [], "title": "Shrimp: a robust underwater visible light communication system.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents the design, implementation, and evaluation of Shrimp, an underwater visible light communication (VLC) system. To address the unique issues in underwater environment such as water flow and scattered sunlight interference, we exploit the circularly polarized light (CPL) and double links for underwater VLC transmission. A coding scheme tailored for underwater communication based on double CPL design is developed. We prototype Shrimp on commercial-off-the-shelf (COTS) LEDs with fabricated printed circuit boards (PCBs). Extensive experiments conducted in an indoor water pool, a lake, and the sea demonstrate that Shrimp can combat against environmental interference and achieve robust communication in underwater environments. The communication distance can be up to 3 m in sea/lake water using a 3 W commodity LED, outperforming the VLC schemes designed for in-air communication.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448616"}, {"primary_key": "2213626", "vector": [], "sparse_vector": [], "title": "Seirios: leveraging multiple channels for LoRaWAN indoor and outdoor localization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Localization is important for a large number of Internet of Things (IoT) endpoint devices connected by LoRaWAN. Due to the bandwidth limitations of LoRaWAN, existing localization methods without specialized hardware (e.g., GPS) produce poor performance. To increase the localization accuracy, we propose a super-resolution localization method, called Seirios, which features a novel algorithm to synchronize multiple non-overlapped communication channels by exploiting the unique features of the radio physical layer to increase the overall bandwidth. By exploiting both the original and the conjugate of the physical layer, Seirios can resolve the direct path from multiple reflectors in both indoor and outdoor environments. We design a Seirios prototype and evaluate its performance in an outdoor area of 100 m × 60 m, and an indoor area of 25 m × 15 m, which shows that Seirios can achieve a median error of 4.4 m outdoors (80% samples < 6.4 m), and 2.4 m indoors (80% samples < 6.1 m), respectively. The results show that Seirios produces 42% less localization error than the baseline approach. Our evaluation also shows that, different to previous studies in Wi-Fi localization systems that have wider bandwidth, time-of-fight (ToF) estimation is less effective for LoRaWAN localization systems with narrowband radio signals.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483256"}, {"primary_key": "2213627", "vector": [], "sparse_vector": [], "title": "RFID and camera fusion for recognition of human-object interactions.", "authors": ["<PERSON><PERSON><PERSON>", "Dong<PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON><PERSON>"], "summary": "Recognition of human-object interactions is practically important in various human-centric sensing scenarios such as smart supermarket, factory, and home. This paper proposes an RF-Camera system by fusing RFID and Computer Vision (CV) techniques, which is the first work to recognize the human gestural interactions with physical objects in multi-subject and multi-object scenarios. In RF-Camera, we first propose a dimension reduction method to transform the subject's 3D hand trajectory captured by depth camera to a 2D image, using which the subject's gesture can be recognized. We also propose a method to extract the facial image of target subject from an image that may contain irrelevant subjects, thereby further recognizing his/her identity. Finally, we model the physical movements of the held object's tag and further predict the tag phase data, by comparing which with real phase data of each tag human-object matching can be discovered. When implementing RF-Camera, three technical challenges need to be addressed. (i) To remove noisy data corresponding to irrelevant actions from raw sensing data, we propose a state transition diagram to determine the boundary of effective data. (ii) To predict phase data of the held target tag with unknown hand-tag offset, we quantify target tag trajectory by adding a variable hand-tag vector to captured hand trajectory. (iii) To ensure high reading rates of target tags in tag-dense scenarios, we propose a CV-assisted RFID scheduling method, in which analytics on CV data can help schedule RFID readings. We conduct extensive experiments to evaluate the performance of RF-Camera. Experimental results demonstrate that RF-Camera can recognize the gestural actions, human identity and human-object matching with an average accuracy higher than 90% in most cases.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483244"}, {"primary_key": "2213628", "vector": [], "sparse_vector": [], "title": "FIRE: enabling reciprocity for FDD MIMO systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Massive MIMO forms a crucial component for 5G because of its ability to improve quality of service and support multiple streams simultaneously. However, for real-world MIMO deployments, estimating the downlink wireless channel from each antenna on the base station to every client device is a critical bottleneck, especially for the widely used frequency duplexed designs that cannot utilize reciprocity. Typically, this channel estimation requires explicit feedback from client devices and is prohibitive for large antenna deployments. In this paper, we present FIRE, a system that uses an end-to-end machine learning approach to enable accurate channel estimation without requiring any feedback from client devices. FIRE is interpretable, accurate, and has low compute overhead. We show that FIRE can successfully support MIMO transmissions in a real-world testbed and achieves SNR improvement over 10 dB in MIMO transmissions compared to the current state-of-the-art.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483275"}, {"primary_key": "2213630", "vector": [], "sparse_vector": [], "title": "WiBeacon: expanding BLE location-based services via wifi.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite the popularity of Bluetooth low energy (BLE) location-based services (LBS) in Internet of things applications, large-scale BLE LBS are extremely challenging due to the expenses of deploying and maintaining BLE beacons. To alleviate this issue, this work presents WiBeacon, which repurposes ubiquitously deployed WiFi access points (AP) into virtual BLE beacons via only moderate software upgrades. Specifically, a WiBeacon-enabled AP can broadcast elaborately designed WiFi packets that could be recognized as iBeacon-compatible location identifiers by unmodified mobile BLE devices. This offers fast deployment of BLE LBS with zero additional hardware costs and low maintenance burdens. WiBeacon is carefully integrated with native WiFi services, retaining transparency to WiFi clients. We implement WiBeacon on commodity WiFi APs (with various chipsets such as Qualcomm, Broadcom, and MediaTek) and extensively evaluate it across various scenarios, including a real commercial application for courier check-ins. During the two-week pilot study, WiBeacon provides reliable services, i.e., as robust as conventional BLE beacons, for 697 users with 150 types of smartphones.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448615"}, {"primary_key": "2213633", "vector": [], "sparse_vector": [], "title": "Colosseum, the world&apos;s largest wireless network emulator.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Practical experimentation and prototyping are core steps in the development of any wireless technology. Often times, however, this crucial step is confined to small laboratory setups that do not capture the scale of commercial deployments and do not ensure result reproducibility and replicability, or it is skipped altogether for lack of suitable hardware and testing facilities. Recent years have seen the development of publicly-available testing platforms for wireless experimentation at scale. Examples include the testbeds of the PAWR program and Colosseum, the world's largest wireless network emulator. With its 256 software-defined radios, 24 racks of powerful compute servers and first-of-its-kind channel emulator, Colosseum allows users to prototype wireless solutions at scale, and guarantees reproducibility and replicability of results. This tutorial provides an overview of the Colosseum platform. We describe the architecture and components of the testbed as a whole, and we then showcase how to run practical experiments in diverse scenarios with heterogeneous wireless technologies (e.g., Wi-Fi and cellular). We also emphasize how Colosseum experiments can be ported to different testing platforms, facilitating full-cycle experimental wireless research: design, experiments and tests at scale in a fully controlled and observable environment and testing in the field. The tutorial concludes with considerations on the flexible future of Colosseum, focusing on its planned extension to emulate larger scenarios and channels at higher frequency bands (mmWave).", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3488032"}, {"primary_key": "2213635", "vector": [], "sparse_vector": [], "title": "PassiveLiFi: rethinking LiFi for low-power and long range RF backscatter.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Light bulbs have been recently explored to design Light Fidelity (LiFi) communication to battery-free tags, thus complementing Radiofrequency (RF) backscatter in the uplink. In this paper, we show that LiFi and RF backscatter are complementary and have unexplored interactions. We introduce PassiveLiFi, a battery-free system that uses LiFi to transmit RF backscatter at a meagre power budget. We address several challenges on the system design in the LiFi transmitter, the tag and the RF receiver. We design the first LiFi transmitter that implements a chirp spread spectrum (CSS) using the visible light spectrum. We use a small bank of solar cells for communication and harvesting and reconfigure them based on the amount of harvested energy and desired data rate. We further alleviate the low responsiveness of solar cells with a new low-power receiver design in the tag. Experimental results with an RF carrier of 17 dBm show that we can generate RF backscatter with a range of 80.3 meters/μW consumed in the tag, which is almost double with respect to prior work.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483262"}, {"primary_key": "2213638", "vector": [], "sparse_vector": [], "title": "A cross-layer approach for supporting real-time multi-user video streaming over WLANs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "MU-MIMO is a high-speed technique in IEEE 802.11ac and upcoming 802.11ax technologies that improves spectral efficiency by allowing concurrent communication between one Access Point and multiple users. In this paper, we present MuVIS, a novel framework that proposes MU-MIMO-aware optimization for multi-user multimedia applications over IEEE 802.11ac/ax. Taking a cross-layer approach, MuVIS first optimizes the MU-MIMO user group selection for the users with the same characteristics in the PHY/MAC layer. It then optimizes the video bitrate for each group accordingly. We present our design and its evaluation on smartphones and laptops over 802.11ac WiFi.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482868"}, {"primary_key": "2213643", "vector": [], "sparse_vector": [], "title": "Tracking free-form activity using wifi signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "WiFi human sensing has become increasingly attractive in enabling emerging human-computer interaction applications. The corresponding technique has gradually evolved from the classification of multiple activity types to more fine-grained tracking of 3D human poses. However, existing WiFi-based 3D human pose tracking is limited to a set of predefined activities. In this work, we present Winect, a 3D human pose tracking system for free-form activity using commodity WiFi devices. Our system tracks free-form activity by estimating a 3D skeleton pose that consists of a set of joints of the human body. In particular, <PERSON><PERSON> first identifies the moving limbs by leveraging the signals reflected off the human body and separates the entangled signals for each limb. Then, our system tracks each limb and constructs a 3D skeleton of the body by modeling the inherent relationship between the movements of the limb and the corresponding joints. Our evaluation results show that Winect achieves centimeter-level accuracy for free-form activity tracking under various environments.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482857"}, {"primary_key": "2213645", "vector": [], "sparse_vector": [], "title": "MIXIQ: re-thinking ultra-low power receiver design for next-generation on-body applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A long-standing challenge in radios for wearables is to design ultra-low power, yet high performance receivers with good sensitivity and spectral efficiency while being compatible with WiFi. The vanilla envelope detector used in standard UHF RFID is the most popular receivers on backscatter tags since they are passive but suffer from poor sensitivity and cannot decode complex modulations, which makes them a poor choice for directly decoding data from WiFi packets.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483270"}, {"primary_key": "2213648", "vector": [], "sparse_vector": [], "title": "DeepRadar: a deep-learning-based environmental sensing capability sensor design for CBRS.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Aniqua <PERSON>t", "<PERSON><PERSON><PERSON>"], "summary": "We present DeepRadar, a novel deep-learning-based environmental sensing capability system for detecting radar signals and estimating their spectral occupancy. DeepRadar makes decisions in real-time and maintains continuous operability by adapting its computations based on the available computing resources. We thoroughly evaluate DeepRadar using a variety of test data at different signal-to-interference ratio (SIR) levels. Our evaluation results show that at 20 dB peak-to-average SIR, per MHz, DeepRadar detects radar signals with 99% accuracy and misses only less than 2 MHz, on average, while estimating their spectral occupancy. Our implementation of DeepRadar using a commercial-off-the-shelf software-defined radio also achieves a similarly high detection accuracy.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448632"}, {"primary_key": "2213649", "vector": [], "sparse_vector": [], "title": "Human perception-enhanced camera system for web conferences leveraging device motions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a demonstration of a human perception-enhanced camera system for web conferencing that protects the user's privacy. Given that people easily forget about their active camera during web conferences, the system advertises the camera's active status via its motions to remind users that they are being watched by others. This prevents inadvertent privacy leakage. The system is developed based on a motorized camera, which moves according to the user's head coordinates just like an eye is looking at the user's face in front of the desk rather than remotely or virtually. The basic idea is to exploit the original human body sense of environmental motions for human-camera interaction, which does not require looking straight at the camera or its LED light to actively check its status. In this demonstration, we showcase our implementation of the human perception-enhanced camera system and invite participants to use the system for web conferences (e.g., Zoom and Google Hangout), which illustrates the system's ability to extend the virtual social interaction to the physical world and the effectiveness of using the camera motion as a non-intrusive awareness indicator.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3510591"}, {"primary_key": "2213652", "vector": [], "sparse_vector": [], "title": "A community-driven approach to democratize access to satellite ground stations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Should you decide to launch a nano-satellite today in Low-Earth Orbit (LEO), the cost of renting ground station communication infrastructure is likely to significantly exceed your launch costs. While space launch costs have lowered significantly with innovative launch vehicles, private players, and smaller payloads, access to ground infrastructure remains a luxury. This is especially true for smaller LEO satellites that are only visible at any location for a few tens of minutes a day and whose signals are extremely weak, necessitating bulky and expensive ground station infrastructure.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448630"}, {"primary_key": "2213653", "vector": [], "sparse_vector": [], "title": "Millimetro: mmWave retro-reflective tags for accurate, long range localization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents Millimetro, an ultra-low-power tag that can be localized at high accuracy over extended distances. We develop Millimetro in the context of autonomous driving to efficiently localize roadside infrastructure such as lane markers and road signs, even if obscured from view, where visual sensing fails. While RF-based localization offers a natural solution, current ultra-low-power localization systems struggle to operate accurately at extended ranges under strict latency requirements. Millimetro addresses this challenge by re-using existing automotive radars that operate at mmWave frequency where plentiful bandwidth is available to ensure high accuracy and low latency. We address the crucial free space path loss problem experienced by signals from the tag at mmWave bands by building upon Van Atta Arrays that retro-reflect incident energy back towards the transmitting radar with minimal loss and low power consumption. Our experimental results indoors and outdoors demonstrate a scalable system that operates at a desirable range (over 100 m), accuracy (centimeter-level), and ultra-low-power (< 3 uW).", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448627"}, {"primary_key": "2213656", "vector": [], "sparse_vector": [], "title": "UltraSE: single-channel speech enhancement using ultrasound.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Robust speech enhancement is considered as the holy grail of audio processing and a key requirement for human-human and human-machine interaction. Solving this task with single-channel, audio-only methods remains an open challenge, especially for practical scenarios involving a mixture of competing speakers and background noise. In this paper, we propose UltraSE, which uses ultrasound sensing as a complementary modality to separate the desired speaker's voice from interferences and noise. UltraSE uses a commodity mobile device (e.g., smartphone) to emit ultrasound and capture the reflections from the speaker's articulatory gestures. It introduces a multi-modal, multi-domain deep learning framework to fuse the ultrasonic Doppler features and the audible speech spectrogram. Furthermore, it employs an adversarially trained discriminator, based on a cross-modal similarity measurement network, to learn the correlation between the two heterogeneous feature modalities. Our experiments verify that UltraSE simultaneously improves speech intelligibility and quality, and outperforms state-of-the-art solutions by a large margin.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448626"}, {"primary_key": "2213657", "vector": [], "sparse_vector": [], "title": "Data-plane signaling in cellular IoT: attacks and defense.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we devise new attacks exploiting the unprotected data-plane signaling in cellular IoT networks (aka both NB-IoT and Cat-M). We show that, despite the deployed security mechanisms on both control-plane signaling and data-plane packet forwarding, novel data-plane signaling attacks are still feasible. Such attacks exhibit a variety of attack forms beyond simplistic packet-blasting, denial-of-service (DoS) threats, including location privacy breach, packet delivery loop, prolonged data delivery, throughput limiting, radio resource draining, and connection reset. Our testbed evaluation and operational network validation have confirmed the viability. We further propose a new defense solution within the 3GPP C-IoT standard framework.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483255"}, {"primary_key": "2213659", "vector": [], "sparse_vector": [], "title": "An aerodynamic, computer vision, and network simulator for networked drone applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We develop, implement, and demonstrate an open-source simulator, called AirSimN, for evaluating drone-based wireless networks in this extended abstract. AirSimN is different from all prior attempts in the literature because it concurrently supports aerodynamic, computer vision, and network simulations. We carefully design it to minimize the effort of realizing virtually arbitrary drone applications, thanks to the active and popular AirSim and NS-3 projects. Many mobile computing and wireless networking projects on, e.g., drone feedback controllers, drone vision algorithms, and 5G/6G cellular network planning, can leverage AirSimN for large-scale evaluations.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482862"}, {"primary_key": "2213660", "vector": [], "sparse_vector": [], "title": "Large-scale vehicle trajectory reconstruction with camera sensing network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Vehicle trajectories provide essential information to understand the urban mobility and benefit a wide range of urban applications. State-of-the-art solutions for vehicle sensing may not build accurate and complete knowledge of all vehicle trajectories. In order to fill the gap, this paper proposes VeTrac, a comprehensive system that employs widely deployed traffic cameras as a sensing network to trace vehicle movements and reconstruct their trajectories in a large scale. VeTrac fuses mobility correlation and vision-based analysis to reduce uncertainties in identifying vehicles. A graph convolution process is employed to maintain the identity consistency across different camera observations, and a self-training process is invoked when aligning with the urban road network to reconstruct vehicle trajectories with confidence. Extensive experiments with real-world data input of over 7 million vehicle snapshots from over one thousand traffic cameras demonstrate that VeTrac achieves 98% accuracy for simple expressway scenario and 89% accuracy for complex urban environment. The achieved accuracy outperforms alternative solutions by 32% for expressway scenario and by 59% for complex urban environment.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448617"}, {"primary_key": "2213661", "vector": [], "sparse_vector": [], "title": "Combating link dynamics for reliable lora connection in urban settings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LoRa, as a representative Low-Power Wide-Area Network (LPWAN) technology, can provide long-range communication for battery-powered IoT devices with a 10-year lifetime. LoRa links in practice, however, experience high dynamics in various environments. When the SNR falls below the threshold (e.g., in the building), a LoRa device disconnects from the network. We propose Falcon, which addresses the link dynamics by enabling data transmission for very low SNR or even disconnected LoRa links. At the heart of Falcon, we reveal that low SNR LoRa links that cannot deliver packets can still introduce interference to other LoRa transmissions. Therefore, Falcon transmits data bits on the low SNR link by selectively interfering with other LoRa transmissions. We address practical challenges in Falcon design. We propose a low-power channel activity detection method to detect other LoRa transmissions for selective interference. To interfere with the so-called interference-resilient LoRa, we accurately estimate the time and frequency offsets on LoRa packets and propose an adaptive frequency adjusting strategy to maximize the interference. We implement Falcon, all using commercial off-the-shelf LoRa devices, and extensively evaluate its performance. The results show that Falcon can provide reliable communication links for disconnected LoRa devices and achieves the SNR boundary upto 7.5 dB lower than that of standard LoRa.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483250"}, {"primary_key": "2213663", "vector": [], "sparse_vector": [], "title": "MVP: magnetic vehicular positioning system for GNSS-denied environments.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Accurate positioning in global navigation satellite system (GNSS)-denied environments, such as tunnels and underpasses, remains a challenge. Navigation systems for such environments need to strike a balance between price and precision. In this paper, we propose magnetic vehicular positioning (MVP), a navigation system that guides drivers in GNSS-denied environments. The key idea of <PERSON> is to extract magnetic fingerprints from geomagnetic field anomalies. By comparing the measured magnetic field against a magnetic map, positioning can be achieved without GNSS signals. Our proposed matching algorithm allows <PERSON> to provide 5.14 m positioning accuracy. We conducted large-scale real-road experiments for 36 months in two countries and 56 tunnels to demonstrate the effectiveness of the proposed system. Because MVP can be deployed on off-the-shelf smartphones, our approach makes accurate navigation in GNSS-denied environments affordable.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483264"}, {"primary_key": "2213664", "vector": [], "sparse_vector": [], "title": "AsyMo: scalable and efficient deep-learning inference on asymmetric mobile CPUs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Xu"], "summary": "On-device deep learning (DL) inference has attracted vast interest. Mobile CPUs are the most common hardware for on-device inference and many inference frameworks have been developed for them. Yet, due to the hardware complexity, DL inference on mobile CPUs suffers from two common issues: the poor performance scalability on the asymmetric multiprocessor, and energy inefficiency.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448625"}, {"primary_key": "2213665", "vector": [], "sparse_vector": [], "title": "Distracted driving detection by sensing the hand gripping of the phone.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Phone usage while driving is unanimously considered a really dangerous habit due to a strong correlation with road accidents. This paper proposes a phone-use monitoring system that detects the driver's handheld phone use and eliminates the distraction at once. Specifically, the proposed system emits periodic ultrasonic pulses to sense if the phone is being held in hand or placed on support surfaces (e.g., seat and cup holder) by capturing the unique signal interference resulted from the contact object's damping, reflection and refraction. We derive the short-time Fourier transform from the microphone data to describe such impacts and develop a CNN-based binary classifier to discriminate the phone use between the handheld and the handsfree status. Additionally, we design a classification error correction filter to correct the classification errors during the monitoring. The experiments with six people, one phone and one car model show that our system achieves 99% accuracy in recognizing handheld phone-use activities.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482861"}, {"primary_key": "2213667", "vector": [], "sparse_vector": [], "title": "Insecurity of operational cellular IoT service: new vulnerabilities, attacks, and countermeasures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Po-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "More than 150 cellular networks worldwide have rolled out massive IoT services such as smart metering and environmental monitoring. Such cellular IoT services share the existing cellular network architecture with non-IoT (e.g., smartphone) ones. When they are newly integrated into the cellular network, new security vulnerabilities may happen from imprudent integration. In this work, we explore the security vulnerabilities of the cellular IoT from both system-integrated and service-integrated aspects. We discover five vulnerabilities spanning cellular standard design defects, network operation slips, and IoT device implementation flaws. Threateningly, they allow an adversary to remotely identify IP addresses and phone numbers assigned to cellular IoT devices and launch data/text spamming attacks against them. We experimentally validate these vulnerabilities and attacks with three major U.S. IoT carriers. The attack evaluation result shows that the adversary can raise an IoT data bill by up to $226 with less than 120 MB spam traffic and increase an IoT text bill at a rate of $5 per second; moreover, cellular IoT devices may suffer from denial of IoT services. We finally propose, prototype, and evaluate recommended solutions.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483239"}, {"primary_key": "2213668", "vector": [], "sparse_vector": [], "title": "An ear canal deformation based continuous user authentication using earables.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Biometric-based authentication is gaining increasing attention for wearables and mobile applications. Meanwhile, the growing adoption of sensors in wearables also provides opportunities to capture novel wearable biometrics. In this work, we propose EarDynamic, an ear canal deformation based user authentication using ear wearables (earables). EarDynamic provides continuous and passive user authentication and is transparent to users. It leverages ear canal deformation that combines the unique static geometry and dynamic motions of the ear canal when the user is speaking for authentication. It utilizes an acoustic sensing approach to capture the ear canal deformation with the built-in microphone and speaker of the earables. Specifically, it first emits well-designed inaudible beep signals and records the reflected signals from the ear canal. It then analyzes the reflected signals and extracts fine-grained acoustic features that correspond to the ear canal deformation for user authentication. Our experimental evaluation shows that EarDynamic can achieve a recall of 97.38% and an F1 score of 96.84%.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482858"}, {"primary_key": "2213671", "vector": [], "sparse_vector": [], "title": "BioFace-3D: continuous 3d facial reconstruction through lightweight single-ear biosensors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Over the last decade, facial landmark tracking and 3D reconstruction have gained considerable attention due to their numerous applications such as human-computer interactions, facial expression analysis, and emotion recognition, etc. Traditional approaches require users to be confined to a particular location and face a camera under constrained recording conditions (e.g., without occlusions and under good lighting conditions). This highly restricted setting prevents them from being deployed in many application scenarios involving human motions. In this paper, we propose the first single-earpiece lightweight biosensing system, BioFace-3D, that can unobtrusively, continuously, and reliably sense the entire facial movements, track 2D facial landmarks, and further render 3D facial animations. Our single-earpiece biosensing system takes advantage of the cross-modal transfer learning model to transfer the knowledge embodied in a high-grade visual facial landmark detection model to the low-grade biosignal domain. After training, our BioFace-3D can directly perform continuous 3D facial reconstruction from the biosignals, without any visual input. Without requiring a camera positioned in front of the user, this paradigm shift from visual sensing to biosensing would introduce new opportunities in many emerging mobile and IoT applications. Extensive experiments involving 16 participants under various settings demonstrate that BioFace-3D can accurately track 53 major facial landmarks with only 1.85 mm average error and 3.38% normalized mean error, which is comparable with most state-of-the-art camera-based solutions. The rendered 3D facial animations, which are in consistency with the real human facial movements, also validate the system's capability in continuous 3D facial reconstruction.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483252"}, {"primary_key": "2213672", "vector": [], "sparse_vector": [], "title": "PECAM: privacy-enhanced video streaming and analytics via securely-reversible transformation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON>"], "summary": "As Video Streaming and Analytics (VSA) systems become increasingly popular, serious privacy concerns have risen on exposing too much unnecessary private information to the VSA providers. Yet, it is challenging to protect privacy while still preserving desired VSA features, i.e., effective analytics, forensic support, resource efficiency, and real-time execution. In this paper, we present a VSA privacy enhancement system (PECAM), which addresses the above challenge with no change in the VSA back-end. PECAM leverages a novel Generative Adversarial Network to perform the privacy-enhanced securely-reversible video transformation. PECAM also incorporates a couple of system optimizations into its VSA workflow to reduce network bandwidth usage and enable real-time processing on cameras. We implement our PECAM prototype on commodity hardware and evaluate its performance via both security study and extensive experiments. Results demonstrate that PECAM can effectively enhance the visual privacy of VSA in the presence of an adversary, and its transformed videos, when taken as input for various VSA back-end tasks, maintain a 96% accuracy of corresponding original videos. Additionally, it performs 12.3× and 1.8× better than baseline methods in terms of the computing cost and network bandwidth usage, respectively.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448618"}, {"primary_key": "2213673", "vector": [], "sparse_vector": [], "title": "PCube: scaling LoRa concurrent transmissions with reception diversities.", "authors": ["Xianjin Xia", "<PERSON><PERSON><PERSON>", "Yuan<PERSON> Zheng", "<PERSON> Gu"], "summary": "This paper presents the design and implementation of PCube, a phase-based parallel packet decoder for concurrent transmissions of LoRa nodes. The key enabling technology behind PCube is a novel air-channel phase measurement technique which is able to extract phase differences of air-channels between LoRa nodes and multiple antennas of a gateway. PCube leverages the reception diversities of multiple receiving antennas of a gateway and scales the concurrent transmissions of a large number of LoRa nodes, even exceeding the number of receiving antennas at a gateway. As a phase-based parallel decoder, PCube provides a new dimension to resolve collisions and supports more concurrent transmissions by complementing time and frequency based parallel decoders. PCube is implemented and evaluated with synchronized software defined radios and off-the-shelf LoRa nodes in both indoors and outdoors. Results demonstrate that PCube can substantially outperform state-of-the-art works in terms of aggregated throughput by 4.9× and the number of concurrent nodes by up to 5×. More importantly, PCube scales well with the number of receiving antennas of a gateway, which is promising to break the barrier of concurrent transmissions.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483268"}, {"primary_key": "2213675", "vector": [], "sparse_vector": [], "title": "RISE: robust wireless sensing using probabilistic and statistical assessments.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen", "<PERSON>"], "summary": "Wireless sensing builds upon machine learning shows encouraging results. However, adopting wireless sensing as a large-scale solution remains challenging as experiences from deployments have shown the performance of a machine-learned model to suffer when there are changes in the environment, e.g., when furniture is moved or when other objects are added or removed from the environment. We present Rise, a novel solution for enhancing the robustness and performance of learning-based wireless sensing techniques against such changes during a deployment. Rise combines probability and statistical assessments together with anomaly detection to identify samples that are likely to be misclassified and uses feedback on these samples to update a deployed wireless sensing model. We validate Rise through extensive empirical benchmarks by considering 11 representative sensing methods covering a broad range of wireless sensing tasks. Our results show that Rise can identify 92.3% of misclassifications on average. We showcase how Rise can be combined with incremental learning to help wireless sensing models retain their performance against dynamic changes in the operating environment to reduce the maintenance cost, paving the way for learning-based wireless sensing to become capable of supporting long-term monitoring in complex everyday environments.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483253"}, {"primary_key": "2213676", "vector": [], "sparse_vector": [], "title": "A 2-FA for home voice assistants using inaudible acoustic signal.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Voice assistants have been shown to be vulnerable to replay attacks, impersonation attacks and inaudible voice commands. Existing defenses do not provide a practical solution as they either rely on external hardware or work under very constrained settings. We introduce a hand gesture-based authentication system for smart home voice assistants called HandLock, which uses built-in microphones and speakers to generate and sense inaudible acoustic signals to detect the presence of a known hand gesture. Our proposed approach can act as a second-factor authentication (2-FA) for performing specific sensitive operations like confirming online purchases through voice assistants. The experiments involving 45 participants show that HandLock can achieve on average 96.51% true-positive-rate at the expense of 0.82% false-acceptance-rate.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482863"}, {"primary_key": "2213677", "vector": [], "sparse_vector": [], "title": "Practical approximate consensus algorithms for small devices in lossy networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper studies a fundamental distributed primitive - approximate consensus - in connected things using wireless networks. It has been extensively studied in different disciplines, such as fault-tolerant computing, distributed computing, control, and robotics communities. To our surprise, we have not found any practical algorithm that is appropriate for our target scenario - a system of small things that have limited computation and storage capability, and use lossy wireless links to communicate with each other.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3482865"}, {"primary_key": "2213678", "vector": [], "sparse_vector": [], "title": "Elf: accelerate high-resolution mobile deep vision with content-aware parallel offloading.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON><PERSON>", "Zhenhua Jia", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As mobile devices continuously generate streams of images and videos, a new class of mobile deep vision applications are rapidly emerging, which usually involve running deep neural networks on these multimedia data in real-time. To support such applications, having mobile devices offload the computation, especially the neural network inference, to edge clouds has proved effective. Existing solutions often assume there exists a dedicated and powerful server, to which the entire inference can be offloaded. In reality, however, we may not be able to find such a server but need to make do with less powerful ones. To address these more practical situations, we propose to partition the video frame and offload the partial inference tasks to multiple servers for parallel processing. This paper presents the design of Elf, a framework to accelerate the mobile deep vision applications with any server provisioning through the parallel offloading. Elf employs a recurrent region proposal prediction algorithm, a region proposal centric frame partitioning, and a resource-aware multi-offloading scheme. We implement and evaluate Elf upon Linux and Android platforms using four commercial mobile devices and three deep vision applications with ten state-of-the-art models. The comprehensive experiments show that Elf can speed up the applications by 4.85× with saving bandwidth usage by 52.6%, while with <1% application accuracy sacrifice.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3448628"}, {"primary_key": "2213680", "vector": [], "sparse_vector": [], "title": "Loki: improving long tail performance of learning-based real-time video adaptation by fusing rule-based models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Huadong Ma", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Maximizing the quality of experience (QoE) for real-time video is a long-standing challenge. Traditional video transport protocols, represented by a few deterministic rules, can hardly adapt to the heterogeneous and highly dynamic modern Internet. Emerging learning-based algorithms have demonstrated potential to meet the challenge. However, our measurement study reveals an alarming long tail performance issue: these algorithms tend to be bottle-necked by occasional catastrophic events due to the built-in exploration mechanisms. In this work, we propose Loki, which improves the robustness of learning-based model by coherently integrating it with a rule-based algorithm. To enable integration at feature level, we first reverse-engineer the rule-based algorithm into an equivalent \"black-box\" neural network. Then, we design a dual-attention feature fusion mechanism to fuse it with a reinforcement learning model. We train Loki in a commercial real-time video system through online learning, and evaluate it over 101 million video sessions, in comparison to state-of-the-art rule-based and learning-based solutions. The results show that <PERSON> improves not only the average but also the tail performance substantially (26.30% to 44.24% reduction of stall rate and 1.76% to 2.17% increase in video throughput at 95-percentile).", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483259"}, {"primary_key": "2213681", "vector": [], "sparse_vector": [], "title": "EMP: edge-assisted multi-vehicle perception.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jiachen Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Connected and Autonomous Vehicles (CAVs) heavily rely on 3D sensors such as LiDARs, radars, and stereo cameras. However, 3D sensors from a single vehicle suffer from two fundamental limitations: vulnerability to occlusion and loss of details on far-away objects. To overcome both limitations, in this paper, we design, implement, and evaluate EMP, a novel edge-assisted multi-vehicle perception system for CAVs. In EMP, multiple nearby CAVs share their raw sensor data with an edge server which then merges CAVs' individual views to form a more complete view with a higher resolution. The merged view can drastically enhance the perception quality of the participating CAVs. Our core methodological contribution is to make the sensor data sharing scalable, adaptive, and resource-efficient over oftentimes highly fluctuating wireless links through a series of novel algorithms, which are then integrated into a full-fledged cooperative sensing pipeline. Extensive evaluations demonstrate that EMP can achieve real-time processing at 24 FPS and end-to-end latency of 93 ms on average. EMP reduces the end-to-end latency by 49% to 65% compared to the traditional vehicle-to-vehicle (V2V) sharing approach without edge support. Our case studies show that cooperative sensing powered by EMP can detect hazards such as blind spots faster by 0.5 to 1.1 seconds, compared to a single vehicle's perception.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483242"}, {"primary_key": "2213682", "vector": [], "sparse_vector": [], "title": "Microphone array backscatter: an application-driven design for lightweight spatial sound recording over the air.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern acoustic wearables with microphone arrays are promising to offer rich experience (e.g., 360° sound and acoustic imaging) to consumers. Realtime multi-track audio streaming with precise synchronization however poses significant challenges to the existing wireless microphone array designs that depend on complex digital synchronization as well as bulky and power-hungry hardware.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483265"}, {"primary_key": "2213683", "vector": [], "sparse_vector": [], "title": "SecureSIM: rethinking authentication and access control for SIM/eSIM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The SIM/eSIM card stores critical information for a mobile user to access the 4G/5G network. In this work, we uncover three vulnerabilities of the current SIM practice. We show that the PIN-based access control may expose the in-SIM data to an adversary through both hardware and software. Once exposed, such in-SIM information can be used to reconstruct various keys used for device authentication, data encryption, etc. They thus enable a number of attacks, including traffic eavesdropping, man-in-the-middle attack, impersonation, etc. The fundamental problem is that, the current SIM design does not offer proper authentication and fine-grained access control to hundreds of in-SIM files for various in-card applets and off-card units. We next propose a new solution that offers both authentication and fine-grained access control. Our implementation and evaluation have confirmed the viability of our proposal.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483254"}, {"primary_key": "2213684", "vector": [], "sparse_vector": [], "title": "SiWa: see into walls via deep UWB radar.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Zhao", "<PERSON><PERSON>"], "summary": "Being able to see into walls is crucial for diagnostics of building health; it enables inspections of wall structure without undermining the structural integrity. However, existing sensing devices do not seem to offer a full capability in mapping the in-wall structure while identifying their status (e.g., seepage and corrosion). In this paper, we design and implement SiWa as a low-cost and portable system for wall inspections. Built upon a customized IR-UWB radar, SiWa scans a wall as a user swipes its probe along the wall surface; it then analyzes the reflected signals to synthesize an image and also to identify the material status. Although conventional schemes exist to handle these problems individually, they require troublesome calibrations that largely prevent them from practical adoptions. To this end, we equip SiWa with a deep learning pipeline to parse the rich sensory data. With an ingenious construction and innovative training, the deep learning modules perform structural imaging and the subsequent analysis on material status, without the need for parameter tuning and calibrations. We build SiWa as a prototype and evaluate its performance via extensive experiments and field studies; results confirm that SiWa accurately maps in-wall structures, identifies their materials, and detects possible failures, suggesting a promising solution for diagnosing building health with lower effort and cost.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993.3483258"}, {"primary_key": "2255148", "vector": [], "sparse_vector": [], "title": "ACM MobiCom &apos;21: The 27th Annual International Conference on Mobile Computing and Networking, New Orleans, Louisiana, USA, October 25-29, 2021", "authors": [], "summary": "Light bulbs have been recently explored to design Light Fidelity (LiFi) communication to battery-free tags, thus complementing Radiofrequency (RF) backscatter in the uplink.In this paper, we show that LiFi and RF backscatter are complementary and have unexplored interactions.We introduce PassiveLiFi, a battery-free system that uses LiFi to transmit RF backscatter at a meagre power budget.We address several challenges on the system design in the LiFi transmitter, the tag and the RF receiver.We design the first LiFi transmitter that implements a chirp spread spectrum (CSS) using the visible light spectrum.We use a small bank of solar cells for communication and harvesting and reconfigure them based on the amount of harvested energy and desired data rate.We further alleviate the low responsiveness of solar cells with a new low-power receiver design in the tag.Experimental results with an RF carrier of 17 dBm show that we can generate RF backscatter with a range of 80.3 meters/W consumed in the tag, which is almost double with respect to prior work.", "published": "2021-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3447993"}]