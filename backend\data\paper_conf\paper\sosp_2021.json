[{"primary_key": "2237814", "vector": [], "sparse_vector": [], "title": "PRISM: Rethinking the RDMA Interface for Distributed Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Dan R. K. <PERSON>s"], "summary": "Remote Direct Memory Access (RDMA) has been used to accelerate a variety of distributed systems, by providing low-latency, CPU-bypassing access to a remote host's memory. However, most of the distributed protocols used in these systems cannot easily be expressed in terms of the simple memory READs and WRITEs provided by RDMA. As a result, designers face a choice between introducing additional protocol complexity (e.g., additional round trips) or forgoing the benefits of RDMA entirely.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483587"}, {"primary_key": "2237817", "vector": [], "sparse_vector": [], "title": "Coeus: A System for Oblivious Document Ranking and Retrieval.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Given a private string q and a remote server that holds a set of public documents D, how can one of the K most relevant documents to q in D be selected and viewed without anyone (not even the server) learning anything about q or the document? This is the oblivious document ranking and retrieval problem. In this paper, we describe Coeus, a system that solves this problem. At a high level, <PERSON><PERSON> composes two cryptographic primitives: secure matrix-vector product for scoring document relevance using the widely-used term frequency-inverse document frequency (tf-idf) method, and private information retrieval (PIR) for obliviously retrieving documents. However, <PERSON><PERSON> reduces the time to run these protocols, thereby improving the user-perceived latency, which is a key performance metric. <PERSON><PERSON> first reduces the PIR overhead by separating out private metadata retrieval from document retrieval, and it then scales secure matrix-vector product to tf-idf matrices with several hundred billion elements through a series of novel cryptographic refinements. For a corpus of English Wikipedia containing 5 million documents, a keyword dictionary with 64K keywords, and on a cluster of 143 machines on AWS, <PERSON><PERSON> enables a user to obliviously rank and retrieve a document in 3.9 seconds---a 24x improvement over a baseline system.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483586"}, {"primary_key": "2237818", "vector": [], "sparse_vector": [], "title": "Rudra: Finding Memory Safety Bugs in Rust at the Ecosystem Scale.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Rust is a promising system programming language that guarantees memory safety at compile time. To support diverse requirements for system software such as accessing low-level hardware, Rust allows programmers to perform operations that are not protected by the Rust compiler with the unsafe keyword. However, Rust's safety guarantee relies on the soundness of all unsafe code in the program as well as the standard and external libraries, making it hard to reason about their correctness. In other words, a single bug in any unsafe code breaks the whole program's safety guarantee.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483570"}, {"primary_key": "2237819", "vector": [], "sparse_vector": [], "title": "Gradient Compression Supercharged High-Performance Data Parallel DNN Training.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Feng Yan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Gradient compression is a promising approach to alleviating the communication bottleneck in data parallel deep neural network (DNN) training by significantly reducing the data volume of gradients for synchronization. While gradient compression is being actively adopted by the industry (e.g., Facebook and AWS), our study reveals that there are two critical but often overlooked challenges: 1) inefficient coordination between compression and communication during gradient synchronization incurs substantial overheads, and 2) developing, optimizing, and integrating gradient compression algorithms into DNN systems imposes heavy burdens on DNN practitioners, and ad-hoc compression implementations often yield surprisingly poor system performance.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483553"}, {"primary_key": "2237820", "vector": [], "sparse_vector": [], "title": "Log-structured Protocols in Delos.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Vidhya Venkat", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Liu", "<PERSON><PERSON>", "<PERSON>", "Rounak Tibrewal", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Developers have access to a wide range of storage APIs and functionality in large-scale systems, such as relational databases, key-value stores, and namespaces. However, this diversity comes at a cost: each API is implemented by a complex distributed system that is difficult to develop and operate. Delos amortizes this cost by enabling different APIs on a shared codebase and operational platform. The primary innovation in Delos is a log-structured protocol: a fine-grained replicated state machine executing above a shared log that can be layered into reusable protocol stacks under different databases. We built and deployed two production databases using Delos at Facebook, creating nine different log-structured protocols in the process. We show via experiments and production data that log-structured protocols impose low overhead, while allowing optimizations that can improve latency by up to 100X (e.g., via leasing) and throughput by up to 2X (e.g., via batching).", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483544"}, {"primary_key": "2237821", "vector": [], "sparse_vector": [], "title": "Bladerunner: Stream Processing at Scale for a Live View of Backend Data Mutations at the Edge.", "authors": ["<PERSON>", "<PERSON><PERSON> Yu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Consider a social media platform with hundreds of millions of online users at any time, utilizing a social graph that has many billions of nodes and edges. The problem this paper addresses is how to provide each user a continuously fresh, up-to-date view of the parts of the social graph they are currently interested in, so as to provide a positive interactive user experience. The problem is challenging because the social graph mutates at a high rate, users change their focus of interest frequently, and some mutations are of interest to many online users.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483572"}, {"primary_key": "2237822", "vector": [], "sparse_vector": [], "title": "Generating Complex, Realistic Cloud Workloads using Recurrent Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Decision-making in large-scale compute clouds relies on accurate workload modeling. Unfortunately, prior models have proven insufficient in capturing the complex correlations in real cloud workloads. We introduce the first model of large-scale cloud workloads that captures long-range inter-job correlations in arrival rates, resource requirements, and lifetimes. Our approach models workload as a three-stage generative process, with separate models for: (1) the number of batch arrivals over time, (2) the sequence of requested resources, and (3) the sequence of lifetimes. Our lifetime model is a novel extension of recent work in neural survival prediction. It represents and exploits inter-job correlations using a recurrent neural network. We validate our approach by showing it is able to accurately generate the production virtual machine workload of two real-world cloud providers.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483590"}, {"primary_key": "2237823", "vector": [], "sparse_vector": [], "title": "Using Lightweight Formal Methods to Validate a Key-Value Storage Node in Amazon S3.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper reports our experience applying lightweight formal methods to validate the correctness of ShardStore, a new key-value storage node implementation for the Amazon S3 cloud object storage service. By \"lightweight formal methods\" we mean a pragmatic approach to verifying the correctness of a production storage node that is under ongoing feature development by a full-time engineering team. We do not aim to achieve full formal verification, but instead emphasize automation, usability, and the ability to continually ensure correctness as both software and its specification evolve over time. Our approach decomposes correctness into independent properties, each checked by the most appropriate tool, and develops executable reference models as specifications to be checked against the implementation. Our work has prevented 16 issues from reaching production, including subtle crash consistency and concurrency problems, and has been extended by non-formal-methods experts to check new features and properties as ShardStore has evolved.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483540"}, {"primary_key": "2237825", "vector": [], "sparse_vector": [], "title": "CLoF: A Compositional Lock Framework for Multi-level NUMA Systems.", "authors": ["<PERSON> Cheh<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Chen"], "summary": "Efficient locking mechanisms are extremely important to support large-scale concurrency and exploit the performance promises of many-core servers. Implementing an efficient, generic, and correct lock is very challenging due to the differences between various NUMA architectures. The performance impact of architectural/NUMA hierarchy differences between x86 and Armv8 are not yet fully explored, leading to unexpected performance when simply porting NUMA-aware locks from x86 to Armv8. Moreover, due to the Armv8 Weak Memory Model (WMM), correctly implementing complicated NUMA-aware locks is very difficult.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483557"}, {"primary_key": "2237826", "vector": [], "sparse_vector": [], "title": "Forerunner: Constraint-based Speculative Transaction Execution for Ethereum.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Ethereum is an emerging distributed computing platform that supports a decentralized replicated virtual machine at a large scale. Transactions in Ethereum are specified in smart contracts, disseminated through broadcast, accepted into the chain of blocks, and then executed on each node. In this new Dissemination-Consensus-Execution (DiCE) paradigm, the time interval between when a transaction is known (during the dissemination phase) to when the transaction is executed (after the consensus phase) offers a window of opportunity to accelerate transaction processing through speculative execution. However, the traditional speculative execution, which hinges on the ability to predict the future accurately, is inadequate because of DiCE's many-future nature.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483564"}, {"primary_key": "2237828", "vector": [], "sparse_vector": [], "title": "Snoopy: Surpassing the Scalability Bottleneck of Oblivious Storage.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Existing oblivious storage systems provide strong security by hiding access patterns, but do not scale to sustain high throughput as they rely on a central point of coordination. To overcome this scalability bottleneck, we present Snoopy, an object store that is both oblivious and scalable such that adding more machines increases system throughput. Snoopy contributes techniques tailored to the high-throughput regime to securely distribute and efficiently parallelize every system component without prohibitive coordination costs. These techniques enable Snoopy to scale similarly to a plaintext storage system. Snoopy achieves 13.7x higher throughput than Obladi, a state-of-the-art oblivious storage system. Specifically, Obladi reaches a throughput of 6.7K requests/s for two million 160-byte objects and cannot scale beyond a proxy and server machine. For the same data size, Snoopy uses 18 machines to scale to 92K requests/s with average latency under 500ms.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483562"}, {"primary_key": "2237829", "vector": [], "sparse_vector": [], "title": "When Idling is Ideal: Optimizing Tail-Latency for Heavy-Tailed Datacenter Workloads with Perséphone.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces Perséphone, a kernel-bypass OS scheduler designed to minimize tail latency for applications executing at microsecond-scale and exhibiting wide service time distributions. Perséphone integrates a new scheduling policy, Dynamic Application-aware Reserved Cores (DARC), that reserves cores for requests with short processing times. Unlike existing kernel-bypass schedulers, DARC is not work conserving. DARC profiles application requests and leaves a small number of cores idle when no short requests are in the queue, so when short requests do arrive, they are not blocked by longer-running ones. Counter-intuitively, leaving cores idle lets DARC maintain lower tail latencies at higher utilization, reducing the overall number of cores needed to serve the same workloads and consequently better utilizing the datacenter resources.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483571"}, {"primary_key": "2237830", "vector": [], "sparse_vector": [], "title": "Witcher: Systematic Crash Consistency Testing for Non-Volatile Memory Key-Value Stores.", "authors": ["<PERSON>nwei Fu", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Chang<PERSON><PERSON> Min"], "summary": "The advent of non-volatile main memory (NVM) enables the development of crash-consistent software without paying storage stack overhead. However, building a correct crash-consistent program remains very challenging in the presence of a volatile cache. This paper presents Witcher, a systematic crash consistency testing framework, which detects both correctness and performance bugs in NVM-based persistent key-value stores and underlying NVM libraries, without test space explosion and without manual annotations or crash consistency checkers. To detect correctness bugs, <PERSON><PERSON> automatically infers likely correctness conditions by analyzing data and control dependencies between NVM accesses. Then <PERSON><PERSON> validates if any violation of them is a true crash consistency bug by checking output equivalence between executions with and without a crash. Moreover, <PERSON><PERSON> detects performance bugs by analyzing the execution traces. Evaluation with 20 NVM key-value stores based on Intel's PMDK library shows that <PERSON><PERSON> discovers 47 (36 new) correctness consistency bugs and 158 (113 new) performance bugs in both applications and PMDK.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483556"}, {"primary_key": "2237831", "vector": [], "sparse_vector": [], "title": "dSpace: Composable Abstractions for Smart Spaces.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present dSpace, an open and modular programming framework that aims to simplify and accelerate the development of smart space applications. To achieve this, dSpace provides two key building blocks~digivices that implement device control and actuation and digidata that process IoT data to generate events and insights. In addition, dSpace introduces novel abstractions - mount, yield, and pipe - via which digivices and digidata can be composed into higher-level abstractions. We apply dSpace to home automation systems and show how developers can easily and flexibly leverage these abstractions to support a wide range of home automation scenarios. Finally, we show how the dSpace concepts can be realized using a microservices-based architecture and implement dSpace as a Kubernetes-compatible framework.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483559"}, {"primary_key": "2237832", "vector": [], "sparse_vector": [], "title": "Exploiting Nil-Externality for Fast Replicated Storage.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>au"], "summary": "Do some storage interfaces enable higher performance than others? Can one identify and exploit such interfaces to realize high performance in storage systems? This paper answers these questions in the affirmative by identifying nil-externality, a property of storage interfaces. A nil-externalizing (nilext) interface may modify state within a storage system but does not externalize its effects or system state immediately to the outside world. As a result, a storage system can apply nilext operations lazily, improving performance.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483543"}, {"primary_key": "2237834", "vector": [], "sparse_vector": [], "title": "Snowboard: Finding Kernel Concurrency Bugs through Systematic Inter-thread Communication Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Kernel concurrency bugs are challenging to find because they depend on very specific thread interleavings and test inputs. While separately exploring kernel thread interleavings or test inputs has been closely examined, jointly exploring interleavings and test inputs has received little attention, in part due to the resulting vast search space. Using precious, limited testing resources to explore this search space and execute just the right concurrent tests in the proper order is critical.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483549"}, {"primary_key": "2237836", "vector": [], "sparse_vector": [], "title": "Regular Sequential Serializability and Regular Sequential Consistency.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Strictly serializable (linearizable) services appear to execute transactions (operations) sequentially, in an order consistent with real time. This restricts a transaction's (operation's) possible return values and in turn, simplifies application programming. In exchange, strictly serializable (linearizable) services perform worse than those with weaker consistency. But switching to such services can break applications.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483566"}, {"primary_key": "2237837", "vector": [], "sparse_vector": [], "title": "ghOSt: Fast &amp; Flexible User-Space Delegation of Linux Scheduling.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>g R<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present ghOSt, our infrastructure for delegating kernel scheduling decisions to userspace code. ghOSt is designed to support the rapidly evolving needs of our data center workloads and platforms.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483542"}, {"primary_key": "2237838", "vector": [], "sparse_vector": [], "title": "Boki: Stateful Serverless Computing with Shared Logs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Boki is a new serverless runtime that exports a shared log API to serverless functions. Boki shared logs enable stateful serverless applications to manage their state with durability, consistency, and fault tolerance. Boki shared logs achieve high throughput and low latency. The key enabler is the metalog, a novel mechanism that allows Bo<PERSON> to address ordering, consistency and fault tolerance independently. The metalog orders shared log records with high throughput and it provides read consistency while allowing service providers to optimize the write and read path of the shared log in different ways. To demonstrate the value of shared logs for stateful serverless applications, we build Boki support libraries that implement fault-tolerant workflows, durable object storage, and message queues. Our evaluation shows that shared logs can speed up important serverless workloads by up to 4.7x.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483541"}, {"primary_key": "2237839", "vector": [], "sparse_vector": [], "title": "WineFS: a hugepage-aware file system for persistent memory that ages gracefully.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern persistent-memory (PM) file systems perform well in benchmark settings, when the file system is freshly created and empty. But after being aged by usage, as will be the normal mode in practice, their memory-mapped performance degrades significantly. This paper shows that the cause is their inability to use 2MB hugepages to map files when aged, having to use 4KB pages instead and suffering many extra page faults and TLB misses as a result.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483567"}, {"primary_key": "2237840", "vector": [], "sparse_vector": [], "title": "Syrup: User-Defined Scheduling Across the Stack.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Suboptimal scheduling decisions in operating systems, networking stacks, and application runtimes are often responsible for poor application performance, including higher latency and lower throughput. These poor decisions stem from a lack of insight into the applications and requests the scheduler is handling and a lack of coherence and coordination between the various layers of the stack, including NICs, kernels, and applications.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483548"}, {"primary_key": "2237841", "vector": [], "sparse_vector": [], "title": "iGUARD: In-GPU Advanced Race Detection.", "authors": ["<PERSON><PERSON><PERSON>", "Arkap<PERSON>va <PERSON>"], "summary": "Newer use cases of GPU (Graphics Processing Unit) computing, e.g., graph analytics, look less like traditional bulk-synchronous GPU programs. To cater to the needs of emerging applications with semantically richer and finer grain sharing patterns, GPU vendors have been introducing advanced programming features, e.g., scoped synchronization and independent thread scheduling. While these features can speed up many applications and enable newer use cases, they can also introduce subtle synchronization errors if used incorrectly.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483545"}, {"primary_key": "2237843", "vector": [], "sparse_vector": [], "title": "LineFS: Efficient SmartNIC Offload of a Distributed File System with Pipeline Parallelism.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>se<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In multi-tenant systems, the CPU overhead of distributed file systems (DFSes) is increasingly a burden to application performance. CPU and memory interference cause degraded and unstable application and storage performance, in particular for operation latency. Recent client-local DFSes for persistent memory (PM) accelerate this trend. DFS offload to SmartNICs is a promising solution to these problems, but it is challenging to fit the complex demands of a DFS onto simple SmartNIC processors located across PCIe.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483565"}, {"primary_key": "2237844", "vector": [], "sparse_vector": [], "title": "PACTree: A High Performance Persistent Range Index Using PAC Guidelines.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>nwei Fu", "<PERSON><PERSON><PERSON>", "Chang<PERSON><PERSON> Min"], "summary": "Non-Volatile Memory (NVM), which provides relatively fast and byte-addressable persistence, is now commercially available. However, we cannot equate a real NVM with a slow DRAM, as it is much more complicated than we expect. In this work, we revisit and analyze both NVM and NVM-specific persistent memory indexes. We find that there is still a lot of room for improvement if we consider NVM hardware, its software stack, persistent index design, and concurrency control. Based on our analysis, we propose Packed Asynchronous Concurrency (PAC) guidelines for designing high-performance persistent index structures. The key idea behind the guidelines is to 1) access NVM hardware in a packed manner to minimize its bandwidth utilization and 2) exploit asynchronous concurrency control to decouple the long NVM latency from the critical path of the index.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483589"}, {"primary_key": "2237848", "vector": [], "sparse_vector": [], "title": "Shard Manager: A Generic Shard Management Framework for Geo-distributed Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sharding is widely used to scale an application. Despite a decade of effort to build generic sharding frameworks that can be reused across different applications, the extent of their success remains unclear. We attempt to answer a fundamental question: what barriers prevent a sharding framework from getting adopted by the majority of sharded applications?", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483546"}, {"primary_key": "2237849", "vector": [], "sparse_vector": [], "title": "MIND: In-Network Memory Management for Disaggregated Data Centers.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Memory disaggregation promises transparent elasticity, high resource utilization and hardware heterogeneity in data centers by physically separating memory and compute into network-attached resource \"blades\". However, existing designs achieve performance at the cost of resource elasticity, restricting memory sharing to a single compute blade to avoid costly memory coherence traffic over the network.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483561"}, {"primary_key": "2237850", "vector": [], "sparse_vector": [], "title": "J-NVM: Off-heap Persistent Objects in Java.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents J-NVM, a framework to access efficiently Non-Volatile Main Memory (NVMM) in Java. J-NVM offers a fully-fledged interface to persist plain Java objects using failure-atomic blocks. This interface relies internally on proxy objects that intermediate direct off-heap access to NVMM. The framework also provides a library of highly-optimized persistent data types that resist reboots and power failures. We evaluate J-NVM by implementing a persistent backend for the Infinispan data store. Our experimental results, obtained with a TPC-B like benchmark and YCSB, show that J-NVM is consistently faster than other approaches at accessing NVMM in Java.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483579"}, {"primary_key": "2237852", "vector": [], "sparse_vector": [], "title": "TwinVisor: Hardware-isolated Confidential Virtual Machines for ARM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen", "Haibing Guan"], "summary": "Confidential VM, which offers an isolated execution environment for cloud tenants with limited trust in the cloud provider, has recently been deployed in major clouds such as AWS and Azure. However, while ARM has become increasingly popular in cloud data centers, existing confidential VM designs mainly leverage specialized x86 hardware extensions (e.g., AMD SEV and Intel TDX) to isolate VMs upon a shared hypervisor.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483554"}, {"primary_key": "2237853", "vector": [], "sparse_vector": [], "title": "lODA: A Host/Device Co-Design for Strong Predictability Contract on Modern Flash Storage.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Predictable latency on flash storage is a long-pursuit goal, yet, unpredictability stays due to the unavoidable disturbance from many well-known SSD internal activities. To combat this issue, the recent NVMe IO Determinism (IOD) interface advocates host-level controls to SSD internal management tasks. While promising, challenges remain on how to exploit it for truly predictable performance.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483573"}, {"primary_key": "2237854", "vector": [], "sparse_vector": [], "title": "Crash Consistent Non-Volatile Memory Express.", "authors": ["<PERSON><PERSON><PERSON>", "Youyou Lu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents crash consistent Non-Volatile Memory Express (ccNVMe), a novel extension of the NVMe that defines how host software communicates with the non-volatile memory (e.g., solid-state drive) across a PCI Express bus with both crash consistency and performance efficiency. Existing storage systems pay a huge tax on crash consistency, and thus can not fully exploit the multi-queue parallelism and low latency of the NVMe interface. ccNVMe alleviates this major bottleneck by coupling the crash consistency to the data dissemination. This new idea allows the storage system to achieve crash consistency by taking the free rides of the data dissemination mechanism of NVMe, using only two lightweight memory-mapped I/Os (MMIO), unlike traditional systems that use complex update protocol and heavyweight block I/Os. ccNVMe introduces transaction-aware MMIO and doorbell to reduce the PCIe traffic as well as to provide atomicity. We present how to build a high-performance and crash-consistent file system namely MQFS atop ccNVMe. We experimentally show that MQFS increases the IOPS of RocksDB by 36% and 28% compared to a state-of-the-art file system and Ext4 without journaling, respectively.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483592"}, {"primary_key": "2237856", "vector": [], "sparse_vector": [], "title": "Scale and Performance in a Filesystem Semi-Microkernel.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>au"], "summary": "We present uFS, a user-level filesystem semi-microkernel. uFS takes advantage of a high-performance storage development kit to realize a fully-functional, crash-consistent, highly-scalable filesystem, with relative developer ease. uFS delivers scalable high performance with a number of novel techniques: careful partitioning of in-memory and on-disk data structures to enable concurrent access without locking, inode migration for balancing load across filesystem threads, and a dynamic scaling algorithm for determining the number of filesystem threads to serve the current workload. Through measurements, we show that uFS has good base performance and excellent scalability; for example, uFS delivers nearly twice the throughput of ext4 for LevelDB on YCSB workloads.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483581"}, {"primary_key": "2237859", "vector": [], "sparse_vector": [], "title": "Kangaroo: Caching Billions of Tiny Objects on Flash.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many social-media and IoT services have very large working sets consisting of billions of tiny (≈100 B) objects. Large, flash-based caches are important to serving these working sets at acceptable monetary cost. However, caching tiny objects on flash is challenging for two reasons: (i) SSDs can read/write data only in multi-KB \"pages\" that are much larger than a single object, stressing the limited number of times flash can be written; and (ii) very few bits per cached object can be kept in DRAM without losing flash's cost advantage. Unfortunately, existing flash-cache designs fall short of addressing these challenges: write-optimized designs require too much DRAM, and DRAM-optimized designs require too many flash writes.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483568"}, {"primary_key": "2237861", "vector": [], "sparse_vector": [], "title": "Birds of a Feather Flock Together: Scaling RDMA RPCs with Flock.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chang<PERSON><PERSON> Min"], "summary": "RDMA-capable networks are gaining traction with datacenter deployments due to their high throughput, low latency, CPU efficiency, and advanced features, such as remote memory operations. However, efficiently utilizing RDMA capability in a common setting of high fan-in, fan-out asymmetric network topology is challenging. For instance, using RDMA programming features comes at the cost of connection scalability, which does not scale with increasing cluster size. To address that, several works forgo some RDMA features by only focusing on conventional RPC APIs.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483576"}, {"primary_key": "2237862", "vector": [], "sparse_vector": [], "title": "Solving Large-Scale Granular Resource Allocation Problems Efficiently with POP.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Resource allocation problems in many computer systems can be formulated as mathematical optimization problems. However, finding exact solutions to these problems using off-the-shelf solvers is often intractable for large problem sizes with tight SLAs, leading system designers to rely on cheap, heuristic algorithms. We observe, however, that many allocation problems are granular: they consist of a large number of clients and resources, each client requests a small fraction of the total number of resources, and clients can interchangeably use different resources. For these problems, we propose an alternative approach that reuses the original optimization problem formulation and leads to better allocations than domain-specific heuristics. Our technique, Partitioned Optimization Problems (POP), randomly splits the problem into smaller problems (with a subset of the clients and resources in the system) and coalesces the resulting sub-allocations into a global allocation for all clients. We provide theoretical and empirical evidence as to why random partitioning works well. In our experiments, POP achieves allocations within 1.5% of the optimal with orders-of-magnitude improvements in runtime compared to existing systems for cluster scheduling, traffic engineering, and load balancing.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483588"}, {"primary_key": "2237863", "vector": [], "sparse_vector": [], "title": "Ka<PERSON>: Scalable BFT Consensus with Pipelined Tree-Based Dissemination and Aggregation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the growing commercial interest in blockchains, permissioned implementations have received increasing attention. Unfortunately, the BFT consensus algorithms that are the backbone of most of these blockchains scale poorly and offer limited throughput. Many state-of-the-art algorithms require a single leader process to receive and validate votes from a quorum of processes and then broadcast the result, which is inherently non-scalable. Recent approaches avoid this bottleneck by using dissemination/aggregation trees to propagate values and collect and validate votes. However, the use of trees increases the round latency, which ultimately limits the throughput for deeper trees. In this paper we propose Kauri, a BFT communication abstraction that can sustain high throughput as the system size grows, leveraging a novel pipelining technique to perform scalable dissemination and aggregation on trees. Our evaluation shows that <PERSON><PERSON> outperforms the throughput of state-of-the-art permissioned blockchain protocols, such as HotStuff, by up to 28x. Interestingly, in many scenarios, the parallelization provided by <PERSON><PERSON> can also decrease the latency.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483584"}, {"primary_key": "2237864", "vector": [], "sparse_vector": [], "title": "RAS: Continuously Optimized Region-Wide Datacenter Resource Allocation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Jing<PERSON> Fan", "<PERSON><PERSON>", "<PERSON>", "<PERSON>nk <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Capacity reservation is a common offering in public clouds and on-premise infrastructure. However, no prior work provides capacity reservation with SLO guarantees that takes into account random and correlated hardware failures, datacenter maintenance, and heterogeneous hardware. In this paper, we describe how Facebook's region-scale Resource Allowance System (RAS) addresses these issues and provides guaranteed capacity. RAS uses a capacity abstraction called reservation to represent a set of servers dynamically assigned to a logical cluster. We take a two-level approach to scale resource allocation to all datacenters in a region, where a mixed-integer-programming solver continuously optimizes server-to-reservation assignments off the critical path, and a traditional container allocator does real-time placement of containers on servers in a reservation. As a relatively new component of Facebook's 10-year old cluster manager <PERSON><PERSON>, RAS has been running in production for almost two years, continuously optimizing the allocation of millions of servers to thousands of reservations. We describe the design of RAS and share our experience of deploying it at scale.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483578"}, {"primary_key": "2237865", "vector": [], "sparse_vector": [], "title": "Rabia: Simplifying State-Machine Replication Through Randomization.", "authors": ["<PERSON><PERSON><PERSON> Pan", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Yicheng Shen", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce Rabia, a simple and high performance framework for implementing state-machine replication (SMR) within a datacenter. The main innovation of Rabia is in using randomization to simplify the design. Rabia provides the following two features: (i) It does not need any fail-over protocol and supports trivial auxiliary protocols like log compaction, snapshotting, and reconfiguration, components that are often considered the most challenging when developing SMR systems; and (ii) It provides high performance, up to 1.5x higher throughput than the closest competitor (i.e., EPaxos) in a favorable setup (same availability zone with three replicas) and is comparable with a larger number of replicas or when deployed in multiple availability zones.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483582"}, {"primary_key": "2237866", "vector": [], "sparse_vector": [], "title": "FragPicker: A New Defragmentation Tool for Modern Storage Devices.", "authors": ["Jonggyu Park", "Young Ik Eom"], "summary": "File fragmentation has been widely studied for several decades because it negatively influences various I/O activities. To eliminate fragmentation, most defragmentation tools migrate the entire content of files into a new area. Unfortunately, such methods inevitably generate a large amount of I/Os in the process of data migration. For this reason, the conventional tools (i) cause defragmentation to be time-consuming, (ii) significantly degrade the performance of co-running applications, and (iii) even curtail the lifetime of modern storage devices. Consequently, the current usage of defragmentation is very limited although it is necessary.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483593"}, {"primary_key": "2237867", "vector": [], "sparse_vector": [], "title": "Bidl: A High-throughput, Low-latency Permissioned Blockchain Framework for Datacenter Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A permissioned blockchain framework typically runs an efficient Byzantine consensus protocol and is attractive to deploy fast trading applications among a large number of mutually untrusted participants (e.g., companies). Unfortunately, all existing permissioned blockchain frameworks adopt sequential workflows for invoking the consensus protocol and executing applications' transactions, making the performance of these applications much lower than deploying them in traditional systems (e.g., in-datacenter stock exchange).", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483574"}, {"primary_key": "2237868", "vector": [], "sparse_vector": [], "title": "Caracal: Contention Management with Deterministic Concurrency Control.", "authors": ["Dai Qin", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deterministic databases offer several benefits: they ensure serializable execution while avoiding concurrency-control related aborts, and they scale well in distributed environments. Today, most deterministic database designs use partitioning to scale up and avoid contention. However, partitioning requires significant programmer effort, leads to poor performance under skewed workloads, and incurs unnecessary overheads in certain uncontended workloads.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483591"}, {"primary_key": "2237869", "vector": [], "sparse_vector": [], "title": "Automated SmartNIC Offloading Insights for Network Functions.", "authors": ["<PERSON><PERSON>", "Jiarong Xing", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The gap between CPU and networking speeds has motivated the development of SmartNICs for NF (network functions) offloading. However, offloading performance is predicated upon intricate knowledge about SmartNIC hardware and careful hand-tuning of the ported programs. Today, developers cannot easily reason about the offloading performance or the effectiveness of different porting strategies without resorting to a trial-and-error approach.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483583"}, {"primary_key": "2237870", "vector": [], "sparse_vector": [], "title": "HeMem: Scalable Tiered Memory Management for Big Data Applications and Real NVM.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "High-capacity non-volatile memory (NVM) is a new main memory tier. Tiered DRAM+NVM servers increase total memory capacity by up to 8x, but can diminish memory bandwidth by up to 7x and inflate latency by up to 63% if not managed well. We study existing hardware and software tiered memory management systems on the recently available Intel Optane DC NVM with big data applications and find that no existing system maximizes application performance on real NVM.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483550"}, {"primary_key": "2237871", "vector": [], "sparse_vector": [], "title": "Mycelium: Large-Scale Distributed Graph Queries with Differential Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper introduces Mycelium, the first system to process differentially private queries over large graphs that are distributed across millions of user devices. Such graphs occur, for instance, when tracking the spread of diseases or malware. Today, the only practical way to query such graphs is to upload them to a central aggregator, which requires a great deal of trust from users and rules out certain types of studies entirely. With Mycelium, users' private data never leaves their personal devices unencrypted, and each user receives strong privacy guarantees. Mycelium does require the help of a central aggregator with access to a data center, but the aggregator merely facilitates the computation by providing bandwidth and computation power; it never learns the topology of the graph or the underlying data. Mycelium accomplishes this with a combination of homomorphic encryption, a verifiable secret redistribution scheme, and a mix network based on telescoping circuits. Our evaluation shows that Mycelium can answer a range of different questions from the medical literature with millions of devices.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483585"}, {"primary_key": "2237872", "vector": [], "sparse_vector": [], "title": "Xenic: SmartNIC-Accelerated Distributed Transactions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "High-performance distributed transactions require efficient remote operations on database memory and protocol metadata. The high communication cost of this workload calls for hardware acceleration. Recent research has applied RDMA to this end, leveraging the network controller to manipulate host memory without consuming CPU cycles on the target server. However, the basic read/write RDMA primitives demand trade-offs in data structure and protocol design, limiting their benefits. SmartNICs are a flexible alternative for fast distributed transactions, adding programmable compute cores and on-board memory to the network interface. Applying measured performance characteristics, we design Xenic, a SmartNIC-optimized transaction processing system. Xenic applies an asynchronous, aggregated execution model to maximize network and core efficiency. Xenic's co-designed data store achieves low-overhead remote object accesses. Additionally, Xenic uses flexible, point-to-point communication patterns between SmartNICs to minimize transaction commit latency. We compare Xenic against prior RDMA- and RPC-based transaction systems with the TPC-C, Retwis, and Smallbank benchmarks. Our results for the three benchmarks show 2.42x, 2.07x, and 2.21x throughput improvement, 59%, 42%, and 22% latency reduction, while saving 2.3, 8.1, and 10.1 threads per server.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483555"}, {"primary_key": "2237873", "vector": [], "sparse_vector": [], "title": "Geometric Partitioning: Explore the Boundary of Optimal Erasure Code Repair.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Erasure coding is widely used in building reliable distributed object storage systems despite its high repair cost. Regenerating codes are a special class of erasure codes, which are proposed to minimize the amount of data needed for repair. In this paper, we assess how optimal repair can help to improve object storage systems, and we find that regenerating codes present unique challenges: regenerating codes repair at the granularity of chunks instead of bytes, and the choice of chunk size leads to the tension between streamed degraded read time and repair throughput.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483558"}, {"primary_key": "2237874", "vector": [], "sparse_vector": [], "title": "HEALER: Relation Learning Guided Kernel Fuzzing.", "authors": ["Hao Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yu <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern operating system kernels are too complex to be free of bugs. Fuzzing is a promising approach for vulnerability detection and has been applied to kernel testing. However, existing work does not consider the influence relations between system calls when generating and mutating inputs, resulting in difficulties when trying to reach into the kernel's deeper logic effectively.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483547"}, {"primary_key": "2237875", "vector": [], "sparse_vector": [], "title": "Basil: Breaking up BFT with ACID (transactions).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents <PERSON>, the first transactional, leaderless Byzantine Fault Tolerant key-value store. <PERSON> leverages ACID transactions to scalably implement the abstraction of a trusted shared log in the presence of Byzantine actors. Unlike traditional BFT approaches, <PERSON> executes non-conflicting operations in parallel and commits transactions in a single round-trip during fault-free executions. <PERSON> improves throughput over traditional BFT systems by four to five times, and is only four times slower than TAPIR, a non-Byzantine replicated system. <PERSON>'s novel recovery mechanism further minimizes the impact of failures: with 30% Byzantine clients, throughput drops by less than 25% in the worst-case.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483552"}, {"primary_key": "2237876", "vector": [], "sparse_vector": [], "title": "Formal Verification of a Multiprocessor Hypervisor on Arm Relaxed Memory Hardware.", "authors": ["Runzhou Tao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Concurrent systems software is widely-used, complex, and error-prone, posing a significant security risk. We introduce VRM, a new framework that makes it possible for the first time to verify concurrent systems software, such as operating systems and hypervisors, on Arm relaxed memory hardware. VRM defines a set of synchronization and memory access conditions such that a program that satisfies these conditions can be mostly verified on a sequentially consistent hardware model and the proofs will automatically hold on relaxed memory hardware. VRM can be used to verify concurrent kernel code that is not data race free, including code responsible for managing shared page tables in the presence of relaxed MMU hardware. Using VRM, we verify the security guarantees of a retrofitted implementation of the Linux KVM hypervisor on Arm. For multiple versions of KVM, we prove KVM's security properties on a sequentially consistent model, then prove that KVM satisfies VRM's required program conditions such that its security proofs hold on Arm relaxed memory hardware. Our experimental results show that the retrofit and VRM conditions do not adversely affect the scalability of verified KVM, as it performs similar to unmodified KVM when concurrently running many multiprocessor virtual machines with real application workloads on Arm multiprocessor server hardware. Our work is the first machine-checked proof for concurrent systems software on Arm relaxed memory hardware.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483560"}, {"primary_key": "2237877", "vector": [], "sparse_vector": [], "title": "The Aurora Single Level Store Operating System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Applications on modern operating systems manage their ephemeral state in memory and persistent state on disk. Ensuring consistency between them is a source of significant developer effort and application bugs. We present the Aurora single level store, an OS that eliminates the distinction between ephemeral and persistent application state.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483563"}, {"primary_key": "2237879", "vector": [], "sparse_vector": [], "title": "Random Walks on Huge Graphs at Cache Efficiency.", "authors": ["<PERSON>", "<PERSON>song Ma", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Data-intensive applications dominated by random accesses to large working sets fail to utilize the computing power of modern processors. Graph random walk, an indispensable workhorse for many important graph processing and learning applications, is one prominent case of such applications. Existing graph random walk systems are currently unable to match the GPU-side node embedding training speed.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483575"}, {"primary_key": "2237881", "vector": [], "sparse_vector": [], "title": "Cuckoo Trie: Exploiting Memory-Level Parallelism for Efficient DRAM Indexing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present the Cuckoo Trie, a fast, memory-efficient ordered index structure. The Cuckoo Trie is designed to have memory-level parallelism -- which a modern out-of-order processor can exploit to execute DRAM accesses in parallel -- without sacrificing memory efficiency. The Cuckoo Trie thus breaks a fundamental performance barrier faced by current indexes, whose bottleneck is a series of dependent pointer-chasing DRAM accesses -- e.g., traversing a search tree path -- which the processor cannot parallelize. Our evaluation shows that the Cuckoo Trie outperforms state-of-the-art-indexes by up to 20%--360% on a variety of datasets and workloads, typically with a smaller or comparable memory footprint.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483551"}, {"primary_key": "2237882", "vector": [], "sparse_vector": [], "title": "Faster and Cheaper Serverless Computing on Harvested Resources.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Serverless computing is becoming increasingly popular due to its ease of programming, fast elasticity, and fine-grained billing. However, the serverless provider still needs to provision, manage, and pay the IaaS provider for the virtual machines (VMs) hosting its platform. This ties the cost of the serverless platform to the cost of the underlying VMs. One way to significantly reduce cost is to use spare resources, which cloud providers rent at a massive discount. Harvest VMs offer such cheap resources: they grow and shrink to harvest all the unallocated CPU cores in their host servers, but may be evicted to make room for more expensive VMs. Thus, using Harvest VMs to run the serverless platform comes with two main challenges that must be carefully managed: VM evictions and dynamically varying resources in each VM.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483580"}, {"primary_key": "2237883", "vector": [], "sparse_vector": [], "title": "The Demikernel Datapath OS Architecture for Microsecond-scale Datacenter Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Datacenter systems and I/O devices now run at single-digit microsecond latencies, requiring ns-scale operating systems. Traditional kernel-based operating systems impose an unaffordable overhead, so recent kernel-bypass OSes [73] and libraries [23] eliminate the OS kernel from the I/O datapath. However, none of these systems offer a general-purpose datapath OS replacement that meet the needs of μs-scale systems.' [email protected] paper proposes Demikernel, a flexible datapath OS and architecture designed for heterogenous kernel-bypass devices and μs-scale datacenter systems. We build two prototype Demikernel OSes and show that minimal effort is needed to port existing μs-scale systems. Once ported, Demikernel lets applications run across heterogenous kernel-bypass devices with ns-scale overheads and no code changes.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483569"}, {"primary_key": "2237884", "vector": [], "sparse_vector": [], "title": "Understanding and Detecting Software Upgrade Failures in Distributed Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>g <PERSON>"], "summary": "Upgrade is one of the most disruptive yet unavoidable maintenance tasks that undermine the availability of distributed systems. Any failure during an upgrade is catastrophic, as it further extends the service disruption caused by the upgrade. The increasing adoption of continuous deployment further increases the frequency and burden of the upgrade task. In practice, upgrade failures have caused many of today's high-profile cloud outages. Unfortunately, there has been little understanding of their characteristics.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132.3483577"}, {"primary_key": "2255629", "vector": [], "sparse_vector": [], "title": "SOSP &apos;21: ACM SIGOPS 28th Symposium on Operating Systems Principles, Virtual Event / Koblenz, Germany, October 26-29, 2021", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Welcome to the Proceedings of the 28th ACM Symposium on Operating Systems Principles (SOSP 2021). This year's program includes 54 papers covering a broad range of topics in computer systems research. The program committee worked hard to select some of the most creative and interesting ideas in computer systems research today, and each accepted paper was shepherded by a program committee member to make sure the papers are as readable and complete as possible. We hope you will enjoy the program as much as we did in selecting it.", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477132"}, {"primary_key": "2255631", "vector": [], "sparse_vector": [], "title": "ResilientFL &apos;21: Proceedings of the First Workshop on Systems Challenges in Reliable and Secure Federated Learning, Virtual Event / Koblenz, Germany, 25 October 2021", "authors": [], "summary": "Federated learning has gained significant attention in recent years owing to the development of hardware and rapid growth in data collection. However, its ability to incorporate a large number of participating agents with various data sources makes ...", "published": "2021-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3477114"}]