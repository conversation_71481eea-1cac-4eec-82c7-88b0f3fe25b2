[{"primary_key": "2134043", "vector": [], "sparse_vector": [], "title": "Formal Verification of Intelligent Hybrid Systems that are Modeled with Simulink and the Reinforcement Learning Toolbox.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Reinforcement Learning (RL) is a powerful technique to control autonomous hybrid systems (HSs) in dynamic and uncertain environments but makes it hard to guarantee their correct behavior in safety-critical applications. To formally guarantee safe behavior, a formal system description is required, which is often not available in industrial design processes and hard to obtain for the unpredictable, trial and error learning processes of RL. In this paper, we present an approach for semi-automatic deductive verification of intelligent HSs with embedded RL components modeled in Simulink together with the RL Toolbox. Our key ideas are threefold: First, we capture the safety-relevant behavior of RL components with hybrid contracts in differential dynamic logic. Second, we verify safety properties of the overall system with the RL component replaced by its contract deductively using the interactive theorem prover KeYmaera X. To make this possible, we precisely capture the semantics of industrially designed intelligent HSs by extending an existing transformation from Simulink to differential dynamic logic to support RL components. Third, we ensure that contracts are complied with at runtime by automatically deriving runtime monitors from our hybrid contracts. We demonstrate the practical applicability, scalability, and flexibility of our approach by verifying collision freedom of an autonomous intelligent robot in a factory setting.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_19"}, {"primary_key": "2134044", "vector": [], "sparse_vector": [], "title": "Efficient Algorithms for Omega-Regular Energy Games.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> Pi<PERSON>iner", "<PERSON>"], "summary": "\\(\\omega \\)-regular energy games are two-player\\(\\omega \\)-regular games augmented with a requirement to avoid the exhaustion of a finite resource, e.g., battery or disk space.\\(\\omega \\)-regular energy games can be reduced to\\(\\omega \\)-regular games by encoding the energy level into the state space. As this approach blows up the state space, it performs poorly. Moreover, it is highly affected by the chosen energy bound denoting the resource’s capacity. In this work, we present an alternative approach for solving\\(\\omega \\)-regular energy games, with two main advantages. First, our approach is efficient: it avoids the encoding of the energy level within the state space, and its performance is independent of the engineer’s choice of the energy bound. Second, our approach is defined at the logic level, not at the algorithmic level, and thus allows solving\\(\\omega \\)-regular energy games by seamless reuse of existing symbolic fixed-point algorithms for ordinary\\(\\omega \\)-regular games. We base our work on the introduction ofenergy\\(\\mu \\)-calculus, a multi-valued extension of game\\(\\mu \\)-calculus. We have implemented our ideas and evaluated them. The empirical evaluation provides evidence for the efficiency of our work.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_9"}, {"primary_key": "2134045", "vector": [], "sparse_vector": [], "title": "Featured Team Automata.", "authors": ["<PERSON>", "Guillermina Cledou", "<PERSON>", "<PERSON>"], "summary": "We propose featured team automata to support variability in the development and analysis of teams, which are systems of reactive components that communicate according to specified synchronisation types. A featured team automaton concisely describes a family of concrete product models for specific configurations determined by feature selection. We focus on the analysis of communication-safety properties, but doing so product-wise quickly becomes impractical. Therefore, we investigate how to lift notions of receptiveness (no message loss) to the level of family models. We show that featured (weak) receptiveness of featured team automata characterises (weak) receptiveness for all product instantiations. A prototypical tool supports the developed theory.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_26"}, {"primary_key": "2134046", "vector": [], "sparse_vector": [], "title": "Integrating ADTs in KeY and Their Application to History-Based Reasoning.", "authors": ["<PERSON><PERSON> Bian", "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We discuss integrating abstract data types (ADTs) in the KeY theorem prover by a new approach to model data types using Isabelle/HOL as an interactive back-end, and translate Isabelle theorems to user-defined taclets in KeY. As a case study of this new approach, we reason about Java’sCollectioninterface using histories, and we prove the correctness of several clients that operate on multiple objects, thereby significantly improving the state-of-the-art of history-based reasoning. Open Science.Includes video material [4] and a source code artifact [5].", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_14"}, {"primary_key": "2134047", "vector": [], "sparse_vector": [], "title": "Fuel in Markov Decision Processes (FiMDP): A Practical Approach to Consumption.", "authors": ["Frantisek <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ufuk <PERSON>"], "summary": "Consumption Markov Decision Processes (CMDPs) are probabilistic decision-making models of resource-constrained systems. We introduce FiMDP, a tool for controller synthesis in CMDPs with LTL objectives expressible by deterministic Büchi automata. The tool implements the recent algorithm for polynomial-time controller synthesis in CMDPs, but extends it with many additional features. On the conceptual level, the tool implements heuristics for improving the expected reachability times of accepting states, and a support for multi-agent task allocation. On the practical level, the tool offers (among other features) a new strategy simulation framework, integration with the Storm model checker, and FiMDPEnv - a new set of CMDPs that model real-world resource-constrained systems. We also present an evaluation of FiMDP on these real-world scenarios.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_34"}, {"primary_key": "2134048", "vector": [], "sparse_vector": [], "title": "Identifying Overly Restrictive Matching Patterns in SMT-Based Program Verifiers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Universal quantifiers occur frequently in proof obligations produced by program verifiers, for instance, to axiomatize uninterpreted functions and to express properties of arrays. SMT-based verifiers typically reason about them via E-matching, an SMT algorithm that requires syntactic matching patterns to guide the quantifier instantiations. Devising good matching patterns is challenging. In particular, overly restrictive patterns may lead to spurious verification errors if the quantifiers needed for a proof are not instantiated; they may also conceal unsoundness caused by inconsistent axiomatizations. In this paper, we present the first technique that identifies and helps the users remedy the effects of overly restrictive matching patterns. We designed a novel algorithm to synthesize missing triggering terms required to complete a proof. Tool developers can use this information to refine their matching patterns and prevent similar verification errors, or to fix a detected unsoundness.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_15"}, {"primary_key": "2134049", "vector": [], "sparse_vector": [], "title": "Verification of the Incremental Merkle Tree Algorithm with <PERSON><PERSON><PERSON>.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The Deposit Smart Contract (DSC) is an instrumental component of the Ethereum 2.0 Phase 0 infrastructure. We have developed the first machine-checkable version of the incremental Merkle tree algorithm used in the DSC. We present our new and original correctness proof of the algorithm along with the Dafny machine-checkable version. The main results are: 1) a new proof of total correctness; 2) a software artefact with the proof in the form of the complete Dafny code base and 3) new provably correct optimisations of the algorithm.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_24"}, {"primary_key": "2134050", "vector": [], "sparse_vector": [], "title": "On Lexicographic Proof Rules for Probabilistic Termination.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the almost-sure (a.s.) termination problem for probabilistic programs, which are a stochastic extension of classical imperative programs. Lexicographic ranking functions provide a sound and practical approach for termination of non-probabilistic programs, and their extension to probabilistic programs is achieved via lexicographic ranking supermartingales (LexRSMs). However, LexRSMs introduced in the previous work have a limitation that impedes their automation: all of their components have to be non-negative in all reachable states. This might result in LexRSM not existing even for simple terminating programs. Our contributions are twofold: First, we introduce a generalization of LexRSMs which allows for some components to be negative. This standard feature of non-probabilistic termination proofs was hitherto not known to be sound in the probabilistic setting, as the soundness proof requires a careful analysis of the underlying stochastic process. Second, we present polynomial-time algorithms using our generalized LexRSMs for proving a.s. termination in broad classes of linear-arithmetic programs.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_33"}, {"primary_key": "2134051", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>/Guarantee Reasoning for Multicopy Atomic Weak Memory Models.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Rely/guarantee reasoning provides a compositional approach to reasoning about concurrent programs. However, such reasoning traditionally assumes a sequentially consistent memory model and hence is unsound on modern hardware in the presence of data races. In this paper, we present a rely/guarantee-based approach formulticopy atomicweak memory models, i.e., where a thread’s stores become observable to all other threads at the same time. Such memory models include those of the widely used x86-TSO and ARMv8 processor architectures, as well as the open-source RISC-V architecture. In this context, an operational semantics can be based on thread-local instruction reordering. We exploit this to provide an efficient compositional proof technique in which weak memory behaviour can be shown to preserve rely/guarantee reasoning on a sequentially consistent memory model. To achieve this, we introduce a side-condition,reordering interference freedom, reducing the complexity of weak memory to checks over pairs of reorderable instructions. To enable practical application, we also define a dataflow analysis capable of identifying a thread’s reorderable instructions. All aspects of our approach have been encoded and proved sound in Isabelle/HOL.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_16"}, {"primary_key": "2134052", "vector": [], "sparse_vector": [], "title": "Formal Verification of a JavaCard Virtual Machine with Frama-C.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Formal verification of real-life industrial software remains a challenging task. It provides strong guarantees of correctness, which are particularly important for security-critical products, such as smart cards. Security of a smart card strongly relies on the requirement that the underlying JavaCard virtual machine ensures necessary isolation properties. This case study paper presents a recent formal verification of a JavaCard Virtual Machine implementation performed by Thales using the Frama-C verification toolset. This is the first verification project for such a large-scale industrial smart card product where deductive verification is applied on the real-life C code. The target properties include common security properties such as integrity and confidentiality. The implementation contains over 7,000 lines of C code. After a formal specification in the ACSL specification language, over 52,000 verification conditions were generated and successfully proved. We present several issues identified during the project, illustrate them by representative examples and present solutions we used to solve them. Finally, we describe proof results, some lessons learned and desired tool improvements.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_23"}, {"primary_key": "2134053", "vector": [], "sparse_vector": [], "title": "HyperProb: A Model Checker for Probabilistic Hyperproperties.", "authors": ["Oyendrila <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We presentHyper<PERSON><PERSON>b, a model checker to verify probabilistic hyperproperties on Markov Decision Processes (MDP). Our tool receives as input an MDP expressed as aPRISMmodel and a formula in Hyper Probabilistic Computational Tree Logic (HyperPCTL). By restricting the domain of scheduler quantification to memoryless non-probabilistic schedulers, our tool exploits an SMT-based encoding to model check probabilistic hyperproperties inHyperPCTL. Furthermore, when the property is satisfied, the tool can provide a witness that can be used for synthesizing a DTMC that conforms with the specification.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_35"}, {"primary_key": "2134054", "vector": [], "sparse_vector": [], "title": "Hybrid Systems Verification with Isabelle/HOL: Simpler Syntax, Better Models, Faster Proofs.", "authors": ["<PERSON>", "<PERSON> y Munive", "<PERSON>", "<PERSON>"], "summary": "We extend a semantic verification framework for hybrid systems with the Isabelle<PERSON>HOL proof assistant by an algebraic model for hybrid program stores, a shallow expression model for hybrid programs and their correctness specifications, and domain-specific deductive and calculational support. The new store model yields clean separations and dynamic local views of variables, e.g. discrete/continuous, mutable/immutable, program/logical, and enhanced ways of manipulating them using combinators, projections and framing. This leads to more local inference rules, procedures and tactics for reasoning with invariant sets, certifying solutions of hybrid specifications or calculating derivatives with increased proof automation and scalability. The new expression model provides more user-friendly syntax, better control of name spaces and interfaces connecting the framework with real-world modelling languages.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_20"}, {"primary_key": "2134055", "vector": [], "sparse_vector": [], "title": "Formal Verification of Consensus in the Taurus Distributed Database.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xuechao Sun", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Distributed database services are an increasingly important part of cloud computing. They are required to satisfy several key properties, including consensus and fault tolerance. Given the highly concurrent nature of these systems, subtle errors can arise that are difficult to discover through traditional testing methods. Formal verification can help in discovering bugs and ensuring correctness of these systems. In this paper, we apply formal methods to specify and verify an industrial distributed database, Taurus, which uses a combination of several fundamental protocols, including Multi-Version Concurrency Control and Raft-based Cluster Management. TLA\\(^{+}\\)is used to model an abstraction of the system and specify its properties. The properties are verified using the TLC model checker, as well as by theorem proving using the TLA proof system (TLAPS). We show that model checking is able to reproduce a bug in Taurus that was found during testing. But our most significant result is twofold: we successfully verified an abstract model of Taurus, and convinced our industrial partners of the usefulness of formal methods to industrial systems.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_42"}, {"primary_key": "2134056", "vector": [], "sparse_vector": [], "title": "HStriver: A Very Functional Extensible Tool for the Runtime Verification of Real-Time Event Streams.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present HStriver, an extensible stream runtime verification tool for event streams. The tool consists of a runtime verification engine for (1) real-time events streams where individual observations and verdicts can occur at arbitrary times, and (2) rich data in the observations and verdicts. This rich setting allows, for example, encoding as HStriver specifications quantitative semantics of logics like STL, including different notions of robustness. The keystone of stream runtime verification (SRV) is the clean separation between temporal dependencies and data computations. To encode the data values and computations involved in the monitoring process we borrow (almost) arbitrary data-types from Haskell. These types are transparently lifted to the specification language and incorporated in the engine, so they can be used as the types of the inputs (observations), outputs (verdicts), and intermediate streams. The resulting extensible language is then embedded, alongside the temporal evaluation engine (which is agnostic to the types) into Haskell as an embedded Domain Specific Langauge (eDSL). Morever, the availability of functional features in the specification language enables the direct implementation of desirable features in HStriver like parametrization (using functions that return stream specifications), etc. The resulting tool is a flexible and extensible stream runtime verification engine for real-time streams. We illustrate the use of the tool on many sophisticated real-time specifications, including realistic signal temporal logic (STL) properties of existing designs.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_30"}, {"primary_key": "2134057", "vector": [], "sparse_vector": [], "title": "Verifying Secure Speculation in Isabelle/HOL.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Secure speculation is an information flow security hyperproperty that prevents transient execution attacks such as Spectre, Meltdown and Foreshadow. Generic compiler mitigations for secure speculation are known to be insufficient for eliminating vulnerabilities. Moreover, these mitigation techniques often overprescribe speculative fences, causing the performance of the programs to suffer. Recently <PERSON><PERSON><PERSON> et al. have developed an operational semantics of program execution capable of characterising speculative executions as well as a new class of information flow hyperproperties named TPOD that ensure secure speculation. This paper presents a framework for verifying TPOD using the Isabelle/HOL proof assistant by encoding the operational semantics of <PERSON><PERSON><PERSON> et al. We provide translation tools for automatically generating the required Isabelle/HOL theory templates from a C-like program syntax, which speeds up verification. Our framework is capable of proving the existence of vulnerabilitiesandcorrectness of secure speculation. We exemplify our framework by proving the existence of secure speculation bugs in 15 victim functions for the MSVC compiler as well as correctness of some proposed fixes.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_3"}, {"primary_key": "2134058", "vector": [], "sparse_vector": [], "title": "Model Checking Collision Avoidance of Nonlinear Autonomous Vehicles.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Autonomous vehicles are expected to be able to avoid static and dynamic obstacles automatically, along their way. However, most of the collision-avoidance functionality is not formally verified, which hinders ensuring such systems’ safety. In this paper, we introduce formal definitions of the vehicle’s movement and trajectory, based on hybrid transition systems. Since formally verifying hybrid systems algorithmically is undecidable, we reduce the verification of nonlinear vehicle behavior to verifying discrete-time vehicle behavior overapproximations. Using this result, we propose a generic approach to formally verify autonomous vehicles with nonlinear behavior against reach-avoid requirements. The approach provides aUppaaltimed-automata model of vehicle behavior, and usesUppaalSTRATEGO for verifying the model with user-programmed libraries of collision-avoidance algorithms. Our experiments show the approach’s effectiveness in discovering bugs in a state-of-the-art version of a selected collision-avoidance algorithm, as well as in proving the absence of bugs in the algorithm’s improved version.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_37"}, {"primary_key": "2134059", "vector": [], "sparse_vector": [], "title": "Model-Free Reinforcement Learning for Lexicographic Omega-Regular Objectives.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of finding optimal strategies in Markov decision processes with lexicographic\\(\\omega \\)-regular objectives, which are ordered collections of ordinary\\(\\omega \\)-regular objectives. The goal is to compute strategies that maximise the probability of satisfaction of the first\\(\\omega \\)-regular objective; subject to that, the strategy should also maximise the probability of satisfaction of the second\\(\\omega \\)-regular objective; then the third and so forth. For instance, one may want to guarantee critical requirements first, functional ones second and only then focus on the non-functional ones. We show how to harness the classic off-the-shelf model-free reinforcement learning techniques to solve this problem and evaluate their performance on four case studies.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_8"}, {"primary_key": "2134060", "vector": [], "sparse_vector": [], "title": "Combining Forces: How to Formally Verify Informally Defined Embedded Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Embedded systems are ubiquitous in our daily lives, and they are often used in safety-critical applications, such as cars, airplanes, or medical systems. As a consequence, there is a high demand for formal methods to ensure their safety. Embedded systems are, however, concurrent, real-time dependent, and highly heterogeneous. Hardware and software are deeply intertwined, and the digital control parts interact with an analogous environment. Moreover, the semantics of industrially used embedded system design languages, such as MATLAB/Simulink or SystemC, is typically only informally defined. To formally capture informally defined embedded systems requires a deep understanding of the underlying models of computation. Furthermore, a single formalism and verification tool are typically not powerful enough to cope with the heterogeneity of embedded systems. In this paper, we summarize our work on automated transformations from informal system descriptions into existing formal verification tools. We present ideas to combine the strengths of various languages, formalisms, and verification backends, and discuss promising results, challenges and limitations.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_1"}, {"primary_key": "2134061", "vector": [], "sparse_vector": [], "title": "Trace Abstraction-Based Verification for Uninterpreted Programs.", "authors": ["Weijiang Hong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The verification of uninterpreted programs is undecidable in general. This paper proposes to employ counterexample-guided abstraction refinement (CEGAR) framework for verifying uninterpreted programs. Different from the existing interpolant-based trace abstraction, we propose a congruence-based trace abstraction method for infeasible counterexample paths to refine the program’s abstraction model, which is designed specifically for uninterpreted programs. Besides, we propose an optimization method that utilizes the decidable verification result for coherent uninterpreted programs to improve the CEGAR framework’s efficiency. We have implemented our verification method and evaluated it on two kinds of benchmark programs. Compared with the state-of-the-art, our method is more effective and efficient, and achieves 3.6x speedups on average.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_29"}, {"primary_key": "2134062", "vector": [], "sparse_vector": [], "title": "Formally Verified Safety Net for Waypoint Navigation Neural Network Controllers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper describes a formal model of a “location, heading and speed” waypoint navigation task for an autonomous ground vehicle—that is, a task of navigating the vehicle towards a particular location so that it has the desired heading and speed when in that location. Our novel way of modeling this task makes formal reasoning over controller correctness tractable. We state our model in differential dynamic logic (dL), which we then use to establish a formal definition of waypoint feasibility and formally verify its validity in the KeYmaera X interactive theorem prover. The formal machine-checked proof witnesses that for any waypoint we consider feasible, the vehicle can indeed be controlled to reach it within the prescribed error bound. We also describe how we use these formal definitions and theorem statements to inform training of neural network controllers for performing this waypoint navigation task. Note that in our approach we do not need to rely on the neural network controller always being perfect—instead, the formal model allows a synthesis of a correct-by-construction safety net for the controller that checks whether the neural network output is safe to act upon and present a safe alternative if it is not.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_7"}, {"primary_key": "2134063", "vector": [], "sparse_vector": [], "title": "Formal Analysis of Neural Network-Based Systems in the Aircraft Domain.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Neural networks are being increasingly used for efficient decision making in the aircraft domain. Given the safety-critical nature of the applications involved, stringent safety requirements must be met by these networks. In this work we present a formal study of two neural network-based systems developed by Boeing. TheVenusverifier is used to analyse the conditions under which these systems can operate safely, or generate counterexamples that show when safety cannot be guaranteed. Our results confirm the applicability of formal verification to the settings considered.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_41"}, {"primary_key": "2134064", "vector": [], "sparse_vector": [], "title": "Generalizing Non-punctuality for Timed Temporal Logic with Freeze Quantifiers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>.", "Paritosh K<PERSON>"], "summary": "Metric Temporal Logic (MTL) and Timed Propositional Temporal Logic (TPTL) are prominent real-time extensions of Linear Temporal Logic (LTL). In general, the satisfiability checking problem for these extensions is undecidable when both the future U and the past S modalities are used. In a classical result, the satisfiability checking for MITL[U,S], a non-punctual fragment of MTL[U,S], is shown to be decidable with EXPSPACE complete complexity. Given that this notion of non-punctuality does not recover decidability in the case of TPTL[U,S], we propose a generalization of non-punctuality callednon-adjacencyfor TPTL[U,S], and focus on its 1-variable fragment, 1-TPTL[U,S]. While non-adjacent 1-TPTL[U,S] appears to be a very small fragment, it is strictly more expressive than MITL. As our main result, we show that the satisfiability checking problem for non-adjacent 1-TPTL[U,S] is decidable with EXPSPACE complete complexity.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_10"}, {"primary_key": "2134065", "vector": [], "sparse_vector": [], "title": "Congruence Relations for Büchi Automata.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We revisit congruence relations for Büchi automata, which play a central role in automata-based formal verification. The size of the classical congruence relation is in\\(3^{\\mathcal {O}(n^{2})}\\), wherenis the number of states of the given Büchi automaton. We present improved congruence relations that can be exponentially coarser than the classical one. We further give asymptoticallyoptimalcongruence relations of size\\(2^{\\mathcal {O}(n \\log n)}\\). Based on these optimal congruence relations, we obtain anoptimaltranslation from a Büchi automaton to a family of deterministic finite automata (FDFA), which can be made to accept either the original language or its complement. To the best of our knowledge, our construction is thefirstdirectandoptimaltranslation from Büchi automata to FDFAs.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_25"}, {"primary_key": "2134066", "vector": [], "sparse_vector": [], "title": "From Partial to Global Assume-Guarantee Contracts: Compositional Realizability Analysis in FRET.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Realizability checking refers to the formal procedure that aims to determine whether an implementation exists, always complying to a set of requirements, regardless of the stimuli provided by the system’s environment. Such a check is essential to ensure that the specification does not allow behavior that can force the system to violate safety constraints. In this paper, we present an approach that decomposes realizability checking into smaller, more tractable problems. More specifically, our approach automatically partitions specifications into sets of non-interfering requirements. We prove that checking whether a specification is realizable reduces to checking that each partition is realizable. We have integrated realizability checking and implemented our decomposition approach within the open-source Formal Requirements Elicitation Tool (FRET). A FRET user may check the realizability of a specification monolithically or compositionally. We evaluate our approach by comparing monolithic and compositional checking and showcase the strengths of our decomposition approach on a variety of industrial-level case studies.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_27"}, {"primary_key": "2134067", "vector": [], "sparse_vector": [], "title": "The Probabilistic Termination Tool Amber.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We describe theAmbertool for proving and refuting the termination of a class of probabilistic while-programs with polynomial arithmetic, in a fully automated manner.Ambercombines martingale theory with properties of asymptotic bounding functions and implements relaxed versions of existing probabilistic termination proof rules to prove/disprove (positive) almost sure termination of probabilistic loops.Ambersupports programs parameterized by symbolic constants and drawing from common probability distributions. Our experimental comparisons give practical evidence ofAmberoutperforming existing state-of-the-art tools.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_36"}, {"primary_key": "2134068", "vector": [], "sparse_vector": [], "title": "Z3str4: A Multi-armed String Solver.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We presentZ3str4, a new high-performance string SMT solver for a rich quantifier-free first-order theory of strings and length constraints. These kinds of constraints have found widespread application in analysis of string-intensive programs in general, and web applications in particular. Three key contributions underpin our solver: first, a novel length-abstraction algorithm that performs various string-length based abstractions and refinements along with a bit-vector backend; second, an arrangement-based solver with a bit-vector backend; third, an algorithm selection and constraint-sharing architecture which leverages the above-mentioned solvers along with the Z3 sequence (Z3seq) solver. We perform extensive empirical evaluation over 20 different industrial and randomly-generated benchmarks with over 120,000+ instances, and show thatZ3str4outperforms the previous best solvers, namely, CVC4, Z3seq, and Z3str3 in both total solved instances and total runtime.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_21"}, {"primary_key": "2134070", "vector": [], "sparse_vector": [], "title": "Fingerprinting Bluetooth Low Energy Devices via Active Automata Learning.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Active automata learning is a technique to automatically infer behavioral models of black-box systems. Today’s learning algorithms enable the deduction of models that describe complex system properties, e.g., timed or stochastic behavior. Despite recent improvements in the scalability of learning algorithms, their practical applicability is still an open issue. Little work exists that actually learns models of physical black-box systems. To fill this gap in the literature, we present a case study on applying automata learning on the Bluetooth Low Energy (BLE) protocol. It shows that not the size of the system limits the applicability of automata learning. Instead, the interaction with the system under learning, is a major bottleneck that is rarely discussed. In this paper, we propose a general automata learning architecture for learning a behavioral model of the BLE protocol implemented by a physical device. With this framework, we can successfully learn the behavior of five investigated BLE devices. The learned models reveal several behavioral differences. This shows that automata learning can be used for fingerprinting black-box devices, i.e., identifying systems via their specific learned models. Based on the fingerprint, an attacker may exploit vulnerabilities specific to a device.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_28"}, {"primary_key": "2134071", "vector": [], "sparse_vector": [], "title": "Formally Guaranteed Tight Dynamic Future Occupancy of Autonomous Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Autonomous Vehicles (AVs) must be able to navigate complex traffic environments without collisions with other traffic participants. Formal methods can be used to guarantee collision avoidance by using reachability analysis techniques to verify if a planned trajectory is safe or unsafe. To prevent the formal collision avoidance system from being overly conservative, a tight reachable set of the AV must be calculated. Computing the tight reachable set of an AV is a difficult and computationally expensive process. Motion primitives are used to provide tight dynamic future occupancy of autonomous systems. Motion primitives are short segments of trajectories that are precomputed along with their reachable sets. By replacing a reference trajectory with a sequence of motion primitives, the computational requirements during vehicle operation can be greatly reduced. In this paper, we provide a treatment on motion primitives, and develop an algorithm to match motion primitives to a reference trajectory in a computationally efficient manner.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_44"}, {"primary_key": "2134072", "vector": [], "sparse_vector": [], "title": "Business Processes Meet Spatial Concerns: The sBPMN Verification Framework.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "BPMN is the standard for business process modeling. It includes a rich set of constructs for control-flow, inter-process communication, and time-related concerns. However, spatial concerns are left apart while being essential to several application domains. We propose a comprehensive extension of BPMN to deal with this. Our proposal includes an integrated notation, a first-order logic semantics of the extension, and tool-supported verification means through the implementation of the semantics in TLA\\(^+\\). Our tool support and our model database are open source and freely available online.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_12"}, {"primary_key": "2134073", "vector": [], "sparse_vector": [], "title": "Divide et Impera: Efficient Synthesis of Cyber-Physical System Architectures from Formal Contracts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Generative Engineering is a new paradigm for the development of cyber-physical systems. Rather than developing a single, increasingly more detailed model of a system, multiple architectural variants are computationally generated and evaluated, which would be prohibitively expensive to do by hand. Existing synthesis approaches are geared towards finding one solution fast, but this makes them less effective for generative engineering where we are interested in enumerating many or all solutions. The common approach in generative engineering is to compute a new verification problem per generated architecture, despite all being variants of the same verification problem. This makes the tools unable to exploit commonalities and they end up doing much of the same verification work over and over again. Our work addresses this inefficiency in the synthesis ofallcorrect-by-construction logical architectures of a system with a simple but effective approach. We create only one parameterized verification problem per use case, and, by exploiting the assumption mechanism of SMT solvers, we can very efficiently and incrementally check each generated architecture. Our experimental evaluation demonstrates that this approach is orders of magnitude faster than the typical synthesis approach\n.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_45"}, {"primary_key": "2134074", "vector": [], "sparse_vector": [], "title": "Hybrid System Falsification for Multiple-Constraint Parameter Synthesis: A Gas Turbine Case Study.", "authors": ["<PERSON><PERSON>", "At<PERSON>yoshi <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We report our experience of applyinghybrid system falsification—an optimization-based method of finding a counterexample to a temporal-logic specification—to the parameter synthesis problem of an industrial gas turbine system model. We identified two major challenges unique to the target problem, namely 1) multiple requirements that are at odds, and 2) their geometric nature. These challenges are dealt with by the following two extensions of falsification, respectively: 1) the use of themultiple constraint ranking(MCR) method by <PERSON> et al., in combination with CMA-ES, for multiple requirements; and 2) the introduction of thearea modality, following the logicAvSTLby Akazaki & Hasuo, for geometric requirements.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_17"}, {"primary_key": "2134075", "vector": [], "sparse_vector": [], "title": "Verified Quadratic Virtual Substitution for Real Arithmetic.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a formally verified quantifier elimination (QE) algorithm for first-order real arithmetic by linear and quadratic virtual substitution (VS) in Isabelle/HOL. The <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> theorem established that the first-order logic of real arithmetic is decidable by QE. However, in practice, QE algorithms are highly complicated and often combine multiple methods for performance. VS is a practically successful method for QE that targets formulas with low-degree polynomials. To our knowledge, this is the first work to formalize VS for quadratic real arithmetic including inequalities. The proofs necessitate various contributions to the existing multivariate polynomial libraries in Isabelle/HOL. Our framework is modularized and easily expandable (to facilitate integrating future optimizations), and could serve as a basis for developing practical general-purpose QE algorithms. Further, as our formalization is designed with practicality in mind, we export our development to SML and test the resulting code on 378 benchmarks from the literature, comparing to Redlog, Z3, Wolfram Engine, and SMT-RAT. This identified inconsistencies in some tools, underscoring the significance of a verified approach for the intricacies of real arithmetic.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_11"}, {"primary_key": "2134076", "vector": [], "sparse_vector": [], "title": "BanditFuzz: Fuzzing SMT Solvers with Multi-agent Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present BanditFuzz, a multi-agent reinforcement learning (RL) guided performance fuzzer for state-of-the-art Satisfiability Modulo Theories (SMT) solvers. BanditFuzz constructs inputs that expose performance issues in a set of target solvers relative to a set of reference solvers, and is the first performance fuzzer that supports the entirety of the theories in the SMT-LIB initiative. Another useful feature of BanditFuzz is that users can specify the size of inputs they want, thus enabling developers to construct very small inputs that zero-in on a performance problem in their SMT solver relative to other competitive solvers. We evaluate BanditFuzz across 52 logics from SMT-COMP ’20 targeting competition-winning solvers against runner-ups. We baseline BanditFuzz against random fuzzing and a single agent algorithm and observe a significant improvement, with up to a 82.6% improvement in the margin ofPAR-2scores across baselines on their respective benchmarks. Furthermore, we reached out to developers and contributors of the CVC4, Z3, and Bitwuzla solvers and provide case studies of how BanditFuzz was able to expose surprising performance deficiencies in each of these tools.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_6"}, {"primary_key": "2134077", "vector": [], "sparse_vector": [], "title": "Formal Verification of Complex Data Paths: An Industrial Experience.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "After caches, most transistors in a modern microprocessor are devoted to wide data-paths. Due to performance and power requirements, these data-paths often use complex implementations of sophisticated algorithms. As Intel experienced in 1994, a bug in a data-path can be extremely expensive and thus needs to be avoided at almost any cost. At the same time, simulation based verification is extremely poor at verifying data-paths due to the vast data space and thus formal verification is almost a requirement. In this paper a retrospective is given of the formal verification of complex data-paths that took place at Intel from the mid 1990 s until very recently. The technology that made the effort possible, the tools developed that made it feasible, and the methodology created that made it practical will all be discussed. Finally, a few examples that illustrates the approach will be presented as well as a concluding discussion on what the goal of using formal verification should be.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_38"}, {"primary_key": "2134078", "vector": [], "sparse_vector": [], "title": "Cabean 2.0: Efficient and Efficacious Control of Asynchronous Boolean Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present a new version of the software,<PERSON><PERSON><PERSON>, integrating six source-target control methods and three target control methods for the reprogramming of asynchronous Boolean networks. The source-target control methods compute the minimal one-step and sequential control strategies that can guide the dynamics of a Boolean network from a source attractor to the desired target attractor with instantaneous, temporary, or permanent perturbations. The target control methods further identify efficacious interventions that can drive the network from any initial state to the desired target attractor with these three types of perturbations. These control methods have been applied to various real-life biological networks to demonstrate their efficacy and efficiency.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_31"}, {"primary_key": "2134079", "vector": [], "sparse_vector": [], "title": "Probabilistic Verification of Neural Networks Against Group Fairness.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fairness is crucial for neural networks which are used in applications with important societal implication. Recently, there have been multiple attempts on improving fairness of neural networks, with a focus on fairness testing (e.g., generating individual discriminatory instances) and fairness training (e.g., enhancing fairness through augmented training). In this work, we propose an approach to formally verify neural networks against fairness, with a focus on independence-based fairness such as group fairness. Our method is built upon an approach for learning Markov Chains from a user-provided neural network (i.e., a feed-forward neural network or a recurrent neural network) which is guaranteed to facilitate sound analysis. The learned Markov Chain not only allows us to verify (with Probably Approximate Correctness guarantee) whether the neural network is fair or not, but also facilities sensitivity analysis which helps to understand why fairness is violated. We demonstrate that with our analysis results, the neural weights can be optimized to improve fairness. Our approach has been evaluated with multiple models trained on benchmark datasets and the experiment results show that our approach is effective and efficient.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_5"}, {"primary_key": "2134080", "vector": [], "sparse_vector": [], "title": "Two Decades of Formal Methods in Industrial Products at BTC Embedded Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Over the last two decades, we at BTC Embedded Systems have collected experience with various applications of formal methods in our products together with our industrial partners and customers. In this paper, we give an overview of these fields of applications.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_40"}, {"primary_key": "2134081", "vector": [], "sparse_vector": [], "title": "Dynamic Reconfiguration via Typed Modalities.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern software systems are increasingly exhibiting dynamic-reconfiguration features analogous to naturally occurring phenomena where the architecture of a complex changes dynamically, at run time, on account of interactions between its components. This has led to a renewed interest in modal logics for formal system development, building on the intuitive idea that system configurations can be regarded as local models of a Kripke structure, while reconfigurations are captured by accessibility relations. We contribute to this line of research by advancing a modal logic with varying quantification domains that employs typed modalities and dedicated modal operators to specify and reason about a new generation of Kripke structures, called dynamic networks of interactions, that account for the context of a system’s dynamics, identifying which actants have triggered a reconfiguration and what are its outcomes. To illustrate the expressiveness of the formalism, we provide a specification of the biological process of membrane budding, which we then analyse using a sound and complete proof-by-translation method that links dynamic networks of interactions with partial first-order logic.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_32"}, {"primary_key": "2134082", "vector": [], "sparse_vector": [], "title": "Combined Online Checking and Control Synthesis: A Study on a Vehicle Platoon Testbed.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vehicle platoon systems are typical safety-critical cyber-physical systems (CPS), and are designed for safe and efficient transportation. However, vehicles’ complex dynamics and uncertain runtime environment make it difficult to apply conventional offline model checking methods to ensure their safety. To address this challenge, we propose an online safety assurance framework for CPS, conducting combined online model checking and control synthesis in well-scheduled cycles. In each cycle, we conduct (1) a quick online formal verification on systems’ coarse-grained hybrid automata (HA) models, as a fault prediction mechanism; (2) for potential risks, an accurate optimal control synthesis on systems’ fine-grained HA models. Furthermore, we develop a robotic vehicle platoon testbed, and implement our framework on it. We conduct a series of evaluations, and experimental results show that the systems’ safety and efficiency are significantly enhanced by our framework.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_43"}, {"primary_key": "2134083", "vector": [], "sparse_vector": [], "title": "Two Mechanisations of WebAssembly 1.0.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>ara<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "WebAssembly (Wasm) is a new bytecode language supported by all major Web browsers, designed primarily to be an efficient compilation target for low-level languages such as C/C++ and Rust. It is unusual in that it is officially specified through a formal semantics. An initial draft specification was published in 2017 [14], with an associated mechanised specification in Isabelle/HOL published by <PERSON> that found bugs in the original specification, fixed before its publication [37]. The first official W3C standard, WebAssembly 1.0, was published in 2019 [45]. Building on <PERSON>’s original mechanisation, we introduce two mechanised specifications of the WebAssembly 1.0 semantics, written in different theorem provers: WasmCert<PERSON><PERSON> and WasmCert-Coq. Wasm’s compact design and official formal semantics enable our mechanisations to be particularly complete and close to the published language standard. We present a high-level description of the language’s updated type soundness result, referencing both mechanisations. We also describe the current state of the mechanisation of language features not previously supported: WasmCert-Isabelle includes a verified executable definition of the instantiation phase as part of an executable verified interpreter; WasmCert-Coq includes executable parsing and numeric definitions as on-going work towards a more ambitious end-to-end verified interpreter which does not require an OCaml harness like <PERSON><PERSON><PERSON>ert<PERSON><PERSON>.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_4"}, {"primary_key": "2134084", "vector": [], "sparse_vector": [], "title": "Concise Outlines for a Complex Logic: A Proof Outline Checker for TaDA.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern separation logics allow one to prove rich properties of intricate code, e.g. functional correctness and linearizability of non-blocking concurrent code. However, this expressiveness leads to a complexity that makes these logics difficult to apply. Manual proofs or proofs in interactive theorem provers consist of a large number of steps, often with subtle side conditions. On the other hand, automation with dedicated verifiers typically requires sophisticated proof search algorithms that are specific to the given program logic, resulting in limited tool support that makes it difficult to experiment with program logics, e.g. when learning, improving, or comparing them. Proof outline checkers fill this gap. Their input is a program annotated with the most essential proof steps, just like the proof outlines typically presented in papers. The tool then checks automatically that this outline represents a valid proof in the program logic. In this paper, we systematically develop a proof outline checker for the TaDA logic, which reduces the checking to a simpler verification problem, for which automated tools exist. Our approach leads to proof outline checkers that provide substantially more automation than interactive provers, but are much simpler to develop than custom automatic verifiers.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_22"}, {"primary_key": "2134085", "vector": [], "sparse_vector": [], "title": "Owicki-<PERSON><PERSON> Reasoning for C11 Programs with Relaxed Dependencies.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Deductive verification techniques for C11 programs have advanced significantly in recent years with the development of operational semantics and associated logics for increasingly large fragments of C11. However, these semantics and logics have been developed in a restricted setting to avoid thethin-air-readproblem. In this paper, we propose an operational semantics that leverages an intra-thread partial order (calledsemantic dependencies) induced by a recently developed denotational event-structure-based semantics. We prove that our operational semantics is sound and complete with respect to the denotational semantics. We present an associated logic that generalises a recent Owicki-Gries framework for RC11 (repaired C11), and demonstrate the use of this logic over several example proofs.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_13"}, {"primary_key": "2134086", "vector": [], "sparse_vector": [], "title": "Apply Formal Methods in Certifying the SyberX High-Assurance Kernel.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "SyberX is an operating system microkernel used for safety and security-critical applications, such as avionics and unmanned vehicles. In this paper, we present an effective approach to apply formal methods in the development and security evaluation high-assurance level of the SyberX based on Common Criteria (CC) methodology. To achieve the evaluation under the CC at evaluation assurance level 5 augmented (EAL5+), where “+” is applying formal methods compliant to the highest evaluation assurance level, several partners from industry and academia have contributed to this effort. Our work provides a standardized formal specification, security analysis as well as formal verification proofs of the SyberX system. All results have been formalized in the Isabelle/HOL theorem prover. During the verification and code review, we find a total of 5 bugs, all confirmed and fixed by developers.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_46"}, {"primary_key": "2134087", "vector": [], "sparse_vector": [], "title": "Model Checking for Verification of Quantum Circuits.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In this survey paper, we describe a framework forassertion-based verificationof quantum circuits by applyingmodel checkingtechniques for quantum systems developed in our previous work, in which: Noiseless and noisy quantum circuits aremodelledas operator- and super-operator-valued transition systems, respectively, both of which can be further represented by tensor networks. Quantumassertionsare specified by a temporal extension of Birk<PERSON>-<PERSON> quantum logic. Their semantics is defined based on the following design decision: they will be used in verification of quantum circuits by simulation on classical computers or human reasoning rather than by quantum physics experiments (e.g. testing through measurements); Algorithmsfor reachability analysis and model checking of quantum circuits are developed based on contraction of tensor networks. We observe that many optimisation techniques for computing relational products used in BDD-based model checking algorithms can be generalised for contracting tensor networks of quantum circuits.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_2"}, {"primary_key": "2134088", "vector": [], "sparse_vector": [], "title": "Gaussian Process-Based Confidence Estimation for Hybrid System Falsification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cyber-Physical Systems (CPSs) are widely adopted in safety-critical domains, raising great demands on their quality assurance. However, the application of formal verification is limited due to the continuous dynamics of CPSs. Instead, simulation-based falsification, which aims at finding a counterexample to refute the system specification, is a more feasible and hence actively pursued approach. Falsification adopts an optimization approach, treatingrobustness, given by the quantitative semantics of the specification language (usually Signal Temporal Logic (STL)), as the objective function. However, similarly to traditional testing, in the absence of found counterexamples, falsification does not give any guarantee on the system safety. To fill this gap, in this paper, we propose aconfidence measurethat estimates the probability that a formal specification is indeed not falsifiable, by relying on the information encapsulated in the simulation data collected during falsification. Methodologically, we approximate the robustness domain by feeding simulation data into a Gaussian Process (GP) Regression process; we then do a minimization sampling on the trained GP, and then estimate the probability that all the robustness values inferred from these sampled points are positive; we take this probability as the confidence measure. We experimentally study the properties of monotonicity and soundness of the proposed confidence measure. We also apply the measure to several state-of-the-art falsification algorithms to assess the maximum confidence they provide when they do not find a falsifying input, and the stability of such confidence across different repetitions.", "published": "2021-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-030-90870-6_18"}]