[{"primary_key": "1261307", "vector": [], "sparse_vector": [], "title": "Faster Deterministic Distributed MIS and Approximate Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present an Õ(log2 n) round deterministic distributed algorithm for the maximal independent set problem. By known reductions, this round complexity extends also to maximal matching, Δ+1 vertex coloring, and 2Δ−1 edge coloring. These four problems are among the most central problems in distributed graph algorithms and have been studied extensively for the past four decades. This improved round complexity comes closer to the Ω(logn) lower bound of maximal independent set and maximal matching [<PERSON><PERSON> et al. FOC<PERSON> '19]. The previous best known deterministic complexity for all of these problems was Θ(log3 n). Via the shattering technique, the improvement permeates also to the corresponding randomized complexities, e.g., the new randomized complexity of Δ+1 vertex coloring is now Õ(log2logn) rounds.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585243"}, {"primary_key": "1261308", "vector": [], "sparse_vector": [], "title": "Weighted Edit Distance Computation: Strings, Trees, and <PERSON>yck.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given two strings of length n over alphabet Σ, and an upper bound k on their edit distance, the algorithm of <PERSON> (Algorithmica'86) and <PERSON><PERSON> and <PERSON> (JCSS'88) from almost forty years back computes the unweighted string edit distance in O(n+k2) time. To date, it remains the fastest algorithm for exact edit distance computation, and it is optimal under the Strong Exponential Hypothesis (<PERSON><PERSON> and <PERSON>; STOC'15). Over the years, this result has inspired many developments, including fast approximation algorithms for string edit distance as well as similar Õ(n+poly(k))-time algorithms for generalizations to tree and Dyck edit distances. Surprisingly, all these results hold only for unweighted instances.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585178"}, {"primary_key": "1261309", "vector": [], "sparse_vector": [], "title": "Approximate Max-Flow Min-Multicut Theorem for Graphs of Bounded Treewidth.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove an approximate max-multiflow min-multicut theorem for bounded treewidth graphs. In particular, we show the following: Given a treewidth-r graph, there exists a (fractional) multicommodity flow of value f, and a multicut of capacity c such that f ≤ c ≤ O(ln(r+1)) · f. It is well known that the multiflow-multicut gap on an r-vertex (constant degree) expander graph can be Ω(lnr), and hence our result is tight up to constant factors. Our proof is constructive, and we also obtain a polynomial time O(ln(r+1))-approximation algorithm for the minimum multicut problem on treewidth-r graphs. Our algorithm proceeds by rounding the optimal fractional solution to the natural linear programming relaxation of the multicut problem. We introduce novel modifications to the well-known region growing algorithm to facilitate the rounding while guaranteeing at most a logarithmic factor loss in the treewidth.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585150"}, {"primary_key": "1261310", "vector": [], "sparse_vector": [], "title": "Resolving Matrix Spencer Conjecture Up to Poly-logarithmic Rank.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a simple proof of the matrix <PERSON> conjecture up to poly-logarithmic rank: given symmetric d × d matrices A1,…,An each with ||Ai||op ≤ 1 and rank at most n/log3 n, one can efficiently find ± 1 signs x1,…,xn such that their signed sum has spectral norm ||∑i=1n xi Ai||op = O(√n). This result also implies a logn − Ω( loglogn) qubit lower bound for quantum random access codes encoding n classical bits with advantage ≫ 1/√n.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585103"}, {"primary_key": "1261311", "vector": [], "sparse_vector": [], "title": "A Proof of the Nisan-Ronen Conjecture.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> and <PERSON> conjectured that the best approximation ratio of deterministic truthful mechanisms for makespan-minimization for n unrelated machines is n. This work validates the conjecture.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585176"}, {"primary_key": "1261312", "vector": [], "sparse_vector": [], "title": "When <PERSON> Has Neither Random Coins Nor Time to Spare: Superfast Derandomization of Proof Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "What is the actual cost of derandomization? And can we get it for free? These questions were recently raised by <PERSON><PERSON> et. al (FOCS 2020) and have been attracting considerable interest. In this work we extend the study of these questions to the setting of derandomizing interactive proofs systems.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585215"}, {"primary_key": "1261313", "vector": [], "sparse_vector": [], "title": "Optimal Explicit Small-Depth Formulas for the Coin Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The-Coin Problem is the problem of distinguishing between a sequence of coin tosses that come up Heads with probability either 1+-/2 or 1-/2. The computational complexity of this problem in various models has been studied in many previous works with various applications related to derandomization, hierarchy theorems, cryptography and meta-complexity. In this paper, we construct improved small-depth explicit formulas for the coin problem. Specifically, we construct explicit formulas of optimal size exp(O(d(1/-)d-1)) and information-theoretically optimal sample complexity O(1/-2) (the sample complexity is the number of coin tosses supplied to the formulas) for this problem, as long as 1/δ≥ dC· d for a large enough absolute constant C. Previous constructions of size-optimal AC0 formulas for the coin problem were either randomized (and hence non-explicit) or had a much worse sample complexity of (1/-)ω(d). Our improved construction yields better Fixed-Depth Size Hierarchy theorems for uniform classes of small-depth circuits with AND, OR and • gates. Our techniques deviate considerably from previous explicit constructions with non-trivial sample complexity due to <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and the two authors (SICOMP 2021). While the approach there was to derandomize randomized formula constructions based on results of <PERSON><PERSON><PERSON> and <PERSON> (ICALP 2007) and <PERSON><PERSON> (ICALP 2009), we instead look to derandomize a randomized circuit construction due to <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (Theory of Computing 2019). This leads us to the problem of constructing certain pseudorandom graphs, which we do explicitly using ideas of <PERSON> (Computational Complexity 2014) involving an iterative use of expander graphs. The constructions of these graphs, which are related to dispersers, may be independently interesting.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585238"}, {"primary_key": "1261314", "vector": [], "sparse_vector": [], "title": "The Complexity of Pattern Counting in Directed Graphs, Parameterised by the Outdegree.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the fixed-parameter tractability of the following fundamental problem: given two directed graphs H→ and G→, count the number of copies of H→ in G→. The standard setting, where the tractability is well understood, uses only |H→| as a parameter. In this paper we adopt as a parameter |H→|+d(G→), where d(G→) is the maximum outdegree of |G→|. Under this parameterisation, we completely characterize the fixed-parameter tractability of the problem in both its non-induced and induced versions through two novel structural parameters, the fractional cover number ρ* and the source number αs. On the one hand we give algorithms with running time f(|H→|,d(G→)) · |G→|ρ*(H→)+O(1) and f(|H→|,d(G→)) · |G→|αs(H→)+O(1) for counting respectively the copies and induced copies of H→ in G→; on the other hand we show that, unless the Exponential Time Hypothesis fails, for any class C→ of directed graphs the restriction of the problem to patterns in C→ is fixed-parameter tractable if and only if ρ*(C→) is bounded (αs(C→) for the induced version). These results explain how the orientation of the pattern can make counting easy or hard, and prove that a classic algorithm by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and its extensions (<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, SICOMP '85; <PERSON><PERSON><PERSON>, Algorithmica '21) are optimal unless ETH fails.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585204"}, {"primary_key": "1261315", "vector": [], "sparse_vector": [], "title": "Local and Global Expansion in Random Geometric Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Consider a random geometric 2-dimensional simplicial complex X sampled as follows: first, sample n vectors u1,…,un uniformly at random on Sd−1; then, for each triple i,j,k ∈ [n], add {i,j,k} and all of its subsets to X if and only if ⟨ ui,uj ⟩ ≥ τ, ⟨ ui,uk ⟩ ≥ τ, and ⟨ uj, uk ⟩ ≥ τ. We prove that for every ε > 0, there exists a choice of d = Θ(logn) and τ = τ(ε,d) so that with high probability, X is a high-dimensional expander of average degree nε in which each 1-link has spectral gap bounded away from 1/2.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585106"}, {"primary_key": "1261316", "vector": [], "sparse_vector": [], "title": "Certified Randomness from Quantum Supremacy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose an application for near-term quantum devices: namely, generating cryptographically certified random bits, to use (for example) in proof-of-stake cryptocurrencies. Our protocol repurposes the existing \"quantum supremacy\" experiments, based on random circuit sampling, that Google and USTC have successfully carried out starting in 2019. We show that, whenever the outputs of these experiments pass the now-standard Linear Cross-Entropy Benchmark (LXEB), under plausible hardness assumptions they necessarily contain Ω(n) min-entropy, where n is the number of qubits. To achieve a net gain in randomness, we use a small random seed to produce pseudorandom challenge circuits. In response to the challenge circuits, the quantum computer generates output strings that, after verification, can then be fed into a randomness extractor to produce certified nearly-uniform bits—thereby \"bootstrapping\" from pseudorandomness to genuine randomness. We prove our protocol sound in two senses: (i) under a hardness assumption called Long List Quantum Supremacy Verification, which we justify in the random oracle model, and (ii) unconditionally in the random oracle model against an eavesdropper who could share arbitrary entanglement with the device. (Note that our protocol's output is unpredictable even to a computationally unbounded adversary who can see the random oracle.) Currently, the central drawback of our protocol is the exponential cost of verification, which in practice will limit its implementation to at most n∼ 60 qubits, a regime where attacks are expensive but not impossible. Modulo that drawback, our protocol appears to be the only practical application of quantum computing that both requires a QC and is physically realizable today.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585145"}, {"primary_key": "1261317", "vector": [], "sparse_vector": [], "title": "Stronger 3-SUM Lower Bounds for Approximate Distance Oracles via Additive Combinatorics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The \"short cycle removal\" technique was recently introduced by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC '22) to prove fine-grained hardness of approximation. Its main technical result is that listing all triangles in an n1/2-regular graph is n2−o(1)-hard even when the number of short cycles is small; namely, when the number of k-cycles is O(nk/2+γ) for γ<1/2. Its corollaries are based on the 3-SUM conjecture and their strength depends on γ, i.e. on how effectively the short cycles are removed.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585240"}, {"primary_key": "1261318", "vector": [], "sparse_vector": [], "title": "Lattice Problems beyond Polynomial Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the complexity of lattice problems in a world where algorithms, reductions, and protocols can run in superpolynomial time. Specifically, we revisit four foundational results in this context—two protocols and two worst-case to average-case reductions. We show how to improve the approximation factor in each result by a factor of roughly √n/logn when running the protocol or reduction in 2є n time instead of polynomial time, and we show a novel protocol with no polynomial-time analog. Our results are as follows.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585227"}, {"primary_key": "1261319", "vector": [], "sparse_vector": [], "title": "A Polynomial-Time Classical Algorithm for Noisy Random Circuit Sampling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give a polynomial time classical algorithm for sampling from the output distribution of a noisy random quantum circuit in the regime of anti-concentration to within inverse polynomial total variation distance. This gives strong evidence that, in the presence of a constant rate of noise per gate, random circuit sampling (RCS) cannot be the basis of a scalable experimental violation of the extended Church-Turing thesis. Our algorithm is not practical in its current form, and does not address finite-size RCS based quantum supremacy experiments.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585234"}, {"primary_key": "1261320", "vector": [], "sparse_vector": [], "title": "Privately Estimating a Gaussian: Efficient, Robust, and Optimal.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we give efficient algorithms for privately estimating a Gaussian distribution in both pure and approximate differential privacy (DP) models with optimal dependence on the dimension in the sample complexity.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585194"}, {"primary_key": "1261321", "vector": [], "sparse_vector": [], "title": "Faster <PERSON> and <PERSON><PERSON><PERSON>ier Transforms from Matrix Non-rigidity.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give algorithms with lower arithmetic operation counts for both the Walsh-Hadamard Transform (WHT) and the Discrete Fourier Transform (DFT) on inputs of power-of-2 size N.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585188"}, {"primary_key": "1261322", "vector": [], "sparse_vector": [], "title": "A Near-Cubic Lower Bound for 3-Query Locally Decodable Codes from Semirandom CSP Refutation.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A code C ∶ {0,1}k → {0,1}n is a q-locally decodable code (q-LDC) if one can recover any chosen bit bi of the message b ∈ {0,1}k with good confidence by randomly querying the encoding x = C(b) on at most q coordinates. Existing constructions of 2-LDCs achieve n = exp(O(k)), and lower bounds show that this is in fact tight. However, when q = 3, far less is known: the best constructions achieve n = exp(ko(1)), while the best known results only show a quadratic lower bound n ≥ Ω(k2/log(k)) on the blocklength.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585143"}, {"primary_key": "1261323", "vector": [], "sparse_vector": [], "title": "Constant-Round Arguments from One-Way Functions.", "authors": ["Noga Amit", "<PERSON>"], "summary": "We study the following question: what cryptographic assumptions are needed for obtaining constant-round computationally-sound argument systems? We focus on argument systems with almost-linear verification time for subclasses of P, such as depth-bounded computations. <PERSON><PERSON>'s celebrated work [STOC 1992] provides such 4-message arguments for P (actually, for NP) using collision-resistant hash functions. We show that one-way functions suffice for obtaining constant-round arguments of almost-linear verification time for languages in P that have log-space uniform circuits of linear depth and polynomial size. More generally, the complexity of the verifier scales with the circuit depth. Furthermore, our argument systems (like <PERSON><PERSON>'s) are doubly-efficient; that is, the honest prover strategy can be implemented in polynomial-time. Unconditionally sound interactive proofs for this class of computations do not rely on any cryptographic assumptions, but they require a linear number of rounds [<PERSON>, <PERSON> and <PERSON>, STOC 2008]. Constant-round interactive proof systems of linear verification complexity are not known even for NC (indeed, even for AC1).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585244"}, {"primary_key": "1261324", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> Discrete Sampling via Continuous Walks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Thuy-<PERSON>ng <PERSON>", "<PERSON>", "<PERSON>"], "summary": "We develop a framework for sampling from discrete distributions µ on the hypercube {± 1}n by sampling from continuous distributions supported on ℝn obtained by convolution with spherical Gaussians. We show that for well-studied families of discrete distributions µ, the result of the convolution is well-conditioned log-concave, whenever the Gaussian's variance is above an O(1) threshold. We plug off-the-shelf continuous sampling methods into our framework to obtain novel discrete sampling algorithms. Additionally, we introduce and study a crucial notion of smoothness for discrete distributions that we call transport stability, which we use to control the propagation of error in our framework. We expect transport stability to be of independent interest, as we connect it to constructions of optimally mixing local random walks and concentration inequalities. As our main application, we resolve open questions raised by <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> on the parallel sampling of distributions which admit parallel counting. We show that determinantal point processes can be sampled via RNC algorithms, that is in time log(n)O(1) using nO(1) processors. For a wider class of distributions, we show our framework yields Quasi-RNC sampling, i.e., log(n)O(1) time using nO(logn) processors. This wider class includes non-symmetric determinantal point processes and random Eulerian tours in digraphs, the latter nearly resolving another open question raised by prior work.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585207"}, {"primary_key": "1261325", "vector": [], "sparse_vector": [], "title": "NLTS Hamiltonians from Good Quantum Codes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The NLTS (No Low-Energy Trivial State) conjecture of <PERSON><PERSON> and <PERSON> posits that there exist families of Hamiltonians with all low energy states of non-trivial complexity (with complexity measured by the quantum circuit depth preparing the state). We prove this conjecture by showing that a particular family of constant-rate and linear-distance qLDPC codes correspond to NLTS local Hamiltonians, although we believe this to be true for all current constructions of good qLDPC codes.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585114"}, {"primary_key": "1261326", "vector": [], "sparse_vector": [], "title": "Succinct Computational Secret Sharing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A secret-sharing scheme enables a dealer to share a secret s among n parties such that only authorized subsets of parties, specified by a monotone access structure f:{0,1}n→{0,1}, can reconstruct s from their shares. Other subsets of parties learn nothing about s.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585127"}, {"primary_key": "1261327", "vector": [], "sparse_vector": [], "title": "The Round Complexity of Statistical MPC with Optimal Resiliency.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In STOC 1989, <PERSON><PERSON> and <PERSON><PERSON> (RB) established an important milestone in the fields of cryptography and distributed computing by showing that every functionality can be computed with statistical (information-theoretic) security in the presence of an active (aka Byzantine) rushing adversary that controls up to half of the parties. We study the round complexity of general secure multiparty computation and several related tasks in the RB model.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585228"}, {"primary_key": "1261328", "vector": [], "sparse_vector": [], "title": "A PTAS for Minimizing Weighted Flow Time on a Single Machine.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "An important objective function in the scheduling literature is to minimize the sum of weighted flow times. We are given a set of jobs, where each job is characterized by a release time, a processing time, and a weight. Our goal is to find a preemptive schedule on a single machine that minimizes the sum of the weighted flow times of the jobs, where the flow time of a job is the time between its completion time and its release time. The currently best known polynomial time algorithm for the problem is a (2+є)-approximation by <PERSON><PERSON><PERSON><PERSON> and <PERSON> [STOC 2021], which builds on the prior break-through result by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> [FOCS 2018] who found the first pseudo-polynomial time constant factor approximation algorithm for the problem, and on the result by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> [SODA 2019] who turned the latter into a polynomial time algorithm. However, it remains open whether the problem admits a PTAS.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585146"}, {"primary_key": "1261329", "vector": [], "sparse_vector": [], "title": "Quantum Depth in the Random Oracle Model.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a comprehensive characterization of the computational power of shallow quantum circuits combined with classical computation. Specifically, for classes of search problems, we show that the following statements hold, relative to a random oracle: (a) $\\mathsf{BPP}^{\\mathsf{QNC}^{\\mathsf{BPP}}} \\neq \\mathsf{BQP}$. This refutes <PERSON><PERSON><PERSON>'s conjecture [QIP 05] in the random oracle model. As a result, this gives the first instantiatable separation between the classes by replacing the oracle with a cryptographic hash function, yielding a resolution to one of <PERSON><PERSON>'s ten semi-grand challenges in quantum computing. (b) $\\mathsf{BPP}^{\\mathsf{QNC}} \\nsubseteq \\mathsf{QNC}^{\\mathsf{BPP}}$ and $\\mathsf{QNC}^{\\mathsf{BPP}} \\nsubseteq \\mathsf{BPP}^{\\mathsf{QNC}}$. This shows that there is a subtle interplay between classical computation and shallow quantum computation. In fact, for the second separation, we establish that, for some problems, the ability to perform adaptive measurements in a single shallow quantum circuit, is more useful than the ability to perform polynomially many shallow quantum circuits without adaptive measurements. (c) There exists a 2-message proof of quantum depth protocol. Such a protocol allows a classical verifier to efficiently certify that a prover must be performing a computation of some minimum quantum depth. Our proof of quantum depth can be instantiated using the recent proof of quantumness construction by <PERSON><PERSON><PERSON> and <PERSON>handry [STOC 22].", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585153"}, {"primary_key": "1261330", "vector": [], "sparse_vector": [], "title": "On Regularity Lemma and Barriers in Streaming and Dynamic Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a new approach for finding matchings in dense graphs by building on <PERSON><PERSON><PERSON><PERSON><PERSON>'s celebrated Regularity Lemma. This allows us to obtain non-trivial albeit slight improvements over longstanding bounds for matchings in streaming and dynamic graphs. In particular, we establish the following results for n-vertex graphs:", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585110"}, {"primary_key": "1261331", "vector": [], "sparse_vector": [], "title": "(Noisy) Gap Cycle Counting Strikes Back: Random Order Streaming Lower Bounds for Connected Components and Beyond.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We continue the study of the communication complexity of gap cycle counting problems. These problems have been introduced by <PERSON><PERSON><PERSON> and <PERSON> [SODA 2011] and have found numerous applications in proving streaming lower bounds. In the noisy gap cycle counting problem (NGC), there is a small integer k ≥ 1 and an n-vertex graph consisted of vertex-disjoint union of either k-cycles or 2k-cycles, plus O(n/k) disjoint paths of length k−1 in both cases (\"noise\"). The edges of this graph are partitioned between <PERSON> and <PERSON> whose goal is to decide which case the graph belongs to with minimal communication from <PERSON> to <PERSON>.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585192"}, {"primary_key": "1261332", "vector": [], "sparse_vector": [], "title": "On the Consistency of Circuit Lower Bounds for Non-deterministic Time.", "authors": ["<PERSON>", "Sam Buss", "<PERSON><PERSON>"], "summary": "We prove the first unconditional consistency result for superpolynomial circuit lower bounds with a relatively strong theory of bounded arithmetic. Namely, we show that the theory ‍V20 is consistent with the conjecture that ‍NEXP ‍⊈ ‍P/poly, i.e., some problem that is solvable in non-deterministic exponential time does not have polynomial size circuits. We suggest this is the best currently available evidence for the truth of the conjecture. Additionally, we establish a magnification result on the hardness of proving circuit lower bounds.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585253"}, {"primary_key": "1261333", "vector": [], "sparse_vector": [], "title": "A New Approach to Learning Linear Dynamical Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Linear dynamical systems are the foundational statistical model upon which control theory is built. Both the celebrated <PERSON><PERSON> filter and the linear quadratic regulator require knowledge of the system dynamics to provide analytic guarantees. Naturally, learning the dynamics of a linear dynamical system from linear measurements has been intensively studied since <PERSON>'s pioneering work in the 1960's. Towards these ends, we provide the first polynomial time algorithm for learning a linear dynamical system from a polynomial length trajectory up to polynomial error in the system parameters under essentially minimal assumptions; observability, controllability, and marginal stability. Our algorithm is built on a method of moments estimator to directly estimate Markov parameters from which the dynamics can be extracted. Furthermore we provide statistical lower bounds when our observability and controllability assumptions are violated.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585247"}, {"primary_key": "1261334", "vector": [], "sparse_vector": [], "title": "Nearly All k-SAT Functions Are Unate.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that 1−o(1) fraction of all k-SAT functions on n Boolean variables are unate (i.e., monotone after first negating some variables), for any fixed positive integer k and as n → ∞. This resolves a conjecture by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> from 2003.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585123"}, {"primary_key": "1261335", "vector": [], "sparse_vector": [], "title": "Better Trees for Santa Claus.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We revisit the problem max-min degree arborescence, which was introduced by <PERSON><PERSON> et al. [STOC'09] as a central special case of the general Santa Claus problem, which constitutes a notorious open question in approximation algorithms. In the former problem we are given a directed graph with sources and sinks and our goal is to find vertex disjoint arborescences rooted in the sources such that at each non-sink vertex of an arborescence the out-degree is at least k, where k is to be maximized. This problem is of particular interest, since it appears to capture much of the difficulty of the Santa Claus problem: (1) like in the Santa Claus problem the configuration LP has a large integrality gap in this case and (2) previous progress by <PERSON><PERSON> et al. was quickly generalized to the Santa Claus problem (<PERSON><PERSON><PERSON><PERSON> et al. [FOCS'09]). These results remain the state-of-the-art both for the Santa Claus problem and for max-min degree arborescence and they yield a polylogarithmic approximation in quasi-polynomial time. We present an exponential improvement to this, a poly(loglogn)-approximation in quasi-polynomial time for the max-min degree arborescence problem. To the best of our knowledge, this is the first example of breaking the logarithmic barrier for a special case of the Santa Claus problem, where the configuration LP cannot be utilized. The main technical novelty of our result are locally good solutions: informally, we show that it suffices to find a poly(logn)-approximation that locally has stronger guarantees. We use a lift-and-project type of LP and randomized rounding, which were also used by <PERSON><PERSON> et al., but unlike previous work we integrate careful pruning steps in the rounding. In the proof we extensively apply Lovász Local Lemma and a local search technique, both of which were previously used only in the context of the configuration LP.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585174"}, {"primary_key": "1261336", "vector": [], "sparse_vector": [], "title": "Obfuscation of Pseudo-Deterministic Quantum Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show how to obfuscate pseudo-deterministic quantum circuits, assuming the quantum hardness of learning with errors (QLWE) and post-quantum virtual black-box (VBB) obfuscation for classical circuits. Given the classical description of a quantum circuit Q, our obfuscator outputs a quantum state Q that can be used to evaluate Q repeatedly on arbitrary inputs.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585179"}, {"primary_key": "1261337", "vector": [], "sparse_vector": [], "title": "Sublinear Time Algorithms and Complexity of Approximate Maximum Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>via<PERSON>"], "summary": "Sublinear time algorithms for approximating maximum matching size have long been studied. Much of the progress over the last two decades on this problem has been on the algorithmic side. For instance, an algorithm of [<PERSON><PERSON><PERSON><PERSON>; FOCS'21] obtains a 1/2-approximation in O(n) time for n-vertex graphs. A more recent algorithm by [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>; SODA'23] obtains a slightly-better-than-1/2 approximation in O(n1+є) time (for arbitrarily small constant ε>0). On the lower bound side, [<PERSON><PERSON><PERSON> and <PERSON>; TCS'07] showed 15 years ago that obtaining any constant approximation of maximum matching size requires Ω(n) time. Proving any super-linear in n lower bound, even for (1−є)-approximations, has remained elusive since then.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585231"}, {"primary_key": "1261338", "vector": [], "sparse_vector": [], "title": "Parameterized Inapproximability of the Minimum Distance Problem over All Fields and the Shortest Vector Problem in All ℓp Norms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "We prove that the Minimum Distance Problem (MDP) on linear codes over any fixed finite field and parameterized by the input distance bound is W[1]-hard to approximate within any constant factor. We also prove analogous results for the parameterized Shortest Vector Problem (SVP) on integer lattices. Specifically, we prove that SVP in the ℓp norm is W[1]-hard to approximate within any constant factor for any fixed p >1 and W[1]-hard to approximate within a factor approaching 2 for p=1. (We show hardness under randomized reductions in each case.)", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585214"}, {"primary_key": "1261339", "vector": [], "sparse_vector": [], "title": "Pandora&apos;s Problem with Nonobligatory Inspection: Optimal Structure and a PTAS.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON> (1979) introduced Pandora's box problem as a mathematical model of sequential search with inspection costs, in which a searcher is allowed to select a prize from one of n alternatives. Several decades later, <PERSON><PERSON> (2018) introduced a close version of the problem, where the searcher does not need to incur the inspection cost of an alternative, and can select it uninspected. Unlike the original problem, the optimal solution to the nonobligatory inspection variant is proved to need adaptivity by <PERSON><PERSON> (2018), and by recent work of <PERSON> and <PERSON> (2022), finding the optimal solution is NP-hard.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585217"}, {"primary_key": "1261340", "vector": [], "sparse_vector": [], "title": "On Approximability of Satisfiable k-CSPs: II.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let Σ be an alphabet and µ be a distribution on Σk for some k ≥ 2. Let α > 0 be the minimum probability of a tuple in the support of µ (denoted supp(µ)). Here, the support of µ is the set of all tuples in Σk that have a positive probability mass under µ. We treat the parameters Σ, k, µ, α as fixed and constant.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585120"}, {"primary_key": "1261341", "vector": [], "sparse_vector": [], "title": "On Approximability of Satisfiable k-CSPs: III.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we study functions on the Boolean hypercube that have the property that after applying certain random restrictions, the restricted function is correlated to a linear function with non-negligible probability. If the given function is correlated with a linear function then this property clearly holds. Furthermore, the property also holds for low-degree functions as low-degree functions become a constant function under a random restriction with a non-negligible probability. We show that this essentially is the only possible reason. More specifically, we show that the function must be correlated to a product of a linear function and a low-degree function. One of the main motivations of studying this question comes from the recent work of the authors towards understanding approximability of satisfiable Constraint Satisfaction Problems.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585121"}, {"primary_key": "1261342", "vector": [], "sparse_vector": [], "title": "Linear Independence, Alternants, and Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We develop a new technique for analyzing linear independence of multivariate polynomials. One of our main technical contributions is a Small Witness for Linear Independence (SWLI) lemma which states the following. If the polynomials f1,f2, …, fk ∈ F[X] over X={x1, …, xn} are F-linearly independent then there exists a subset S ⊆ X of size at most k−1 such that f1,f2, …, fk are also F(X∖ S)-linearly independent.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585149"}, {"primary_key": "1261343", "vector": [], "sparse_vector": [], "title": "Locally Consistent Decomposition of Strings with Applications to Edit Distance Sketching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we provide a new locally consistent decomposition of strings. Each string x is decomposed into blocks that can be described by grammars of size O(k) (using some amount of randomness). If we take two strings x and y of edit distance at most k then their block decomposition uses the same number of grammars and the i-th grammar of x is the same as the i-th grammar of y except for at most k indexes i. The edit distance of x and y equals to the sum of edit distances of pairs of blocks where x and y differ. Our decomposition can be used to design a sketch of size O(k2) for edit distance, and also a rolling sketch for edit distance of size O(k2). The rolling sketch allows to update the sketched string by appending a symbol or removing a symbol from the beginning of the string.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585239"}, {"primary_key": "1261344", "vector": [], "sparse_vector": [], "title": "Sublinear Algorithms for (1.5+ε)-Approximate Matching.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Thatchaphol <PERSON>"], "summary": "We study sublinear time algorithms for estimating the size of maximum matching. After a long line of research, the problem was finally settled by <PERSON><PERSON><PERSON><PERSON> ‍[FOCS'22], in the regime where one is willing to pay an approximation factor of 2. Very recently, <PERSON><PERSON><PERSON><PERSON> et al. ‍[SODA'23] improved the approximation factor to (2−1/2O(1/γ)) using n1+γ time. This improvement over the factor 2 is, however, minuscule and they asked if even 1.99-approximation is possible in n2−Ω(1) time.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585252"}, {"primary_key": "1261345", "vector": [], "sparse_vector": [], "title": "Approximate Distance Sensitivity Oracles in Subquadratic Space.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "An f-edge fault-tolerant distance sensitive oracle (f-DSO) with stretch σ ≥ 1 is a data structure that preprocesses a given undirected, unweighted graph G with n vertices and m edges, and a positive integer f. When queried with a pair of vertices s, t and a set F of at most f edges, it returns a σ-approximation of the s-t-distance in G−F.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585251"}, {"primary_key": "1261346", "vector": [], "sparse_vector": [], "title": "Directed Isoperimetric Theorems for Boolean Functions on the Hypergrid and an Õ(n√d) Monotonicity Tester.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The problem of testing monotonicity for Boolean functions on the hypergrid, f:[n]d → {0,1} is a classic topic in property testing. When n=2, the domain is the hypercube. For the hypercube case, a breakthrough result of <PERSON><PERSON><PERSON><PERSON> (FOCS 2015) gave a non-adaptive, one-sided tester making O(ε−2√d) queries. Up to polylog d and ε factors, this bound matches the Ω(√d)-query non-adaptive lower bound (<PERSON> (STOC 2015), <PERSON><PERSON><PERSON><PERSON> (STOC 2017)). For any n > 2, the optimal non-adaptive complexity was unknown. A previous result of the authors achieves a O(d5/6)-query upper bound (SODA 2020), quite far from the √d bound for the hypercube. In this paper, we resolve the non-adaptive complexity of monotonicity testing for all constant n, up to poly(ε−1logd) factors. Specifically, we give a non-adaptive, one-sided monotonicity tester making O(ε−2n√d) queries. From a technical standpoint, we prove new directed isoperimetric theorems over the hypergrid [n]d. These results generalize the celebrated directed Talagrand inequalities that were only known for the hypercube.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585167"}, {"primary_key": "1261347", "vector": [], "sparse_vector": [], "title": "Subsampling Suffices for Adaptive Data Analysis.", "authors": ["<PERSON>"], "summary": "Ensuring that analyses performed on a dataset are representative of the entire population is one of the central problems in statistics. Most classical techniques assume that the dataset is independent of the analyst's query and break down in the common setting where a dataset is reused for multiple, adaptively chosen, queries. This problem of adaptive data analysis was formalized in the seminal works of <PERSON><PERSON> et al. (STOC, 2015) and <PERSON><PERSON> and <PERSON> (FOCS, 2014).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585226"}, {"primary_key": "1261348", "vector": [], "sparse_vector": [], "title": "Lifting Uniform Learners via Distributional Decomposition.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show how any PAC learning algorithm that works under the uniform distribution can be transformed, in a blackbox fashion, into one that works under an arbitrary and unknown distribution ‍D. The efficiency of our transformation scales with the inherent complexity of ‍D, running in (n, (md)d) time for distributions over n whose pmfs are computed by depth-d decision trees, where m is the sample complexity of the original algorithm. For monotone distributions our transformation uses only samples from ‍D, and for general ones it uses subcube conditioning samples.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585212"}, {"primary_key": "1261349", "vector": [], "sparse_vector": [], "title": "A Unifying Theory of Distance from Calibration.", "authors": ["Jaroslaw Blasiok", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Preetum Nakkiran"], "summary": "We study the fundamental question of how to define and measure the distance from calibration for probabilistic predictors. While the notion of perfect calibration is well-understood, there is no consensus on how to quantify the distance from perfect calibration. Numerous calibration measures have been proposed in the literature, but it is unclear how they compare to each other, and many popular measures such as Expected Calibration Error (ECE) fail to satisfy basic properties like continuity.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585182"}, {"primary_key": "1261350", "vector": [], "sparse_vector": [], "title": "An Improved Approximation Guarantee for Prize-Collecting TSP.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present a new approximation algorithm for the (metric) prize-collecting traveling salesperson problem (PCTSP). In PCTSP, opposed to the classical traveling salesperson problem (TSP), one may choose to not include a vertex of the input graph in the returned tour at the cost of a given vertex-dependent penalty, and the objective is to balance the length of the tour and the incurred penalties for omitted vertices by minimizing the sum of the two. We present an algorithm that achieves an approximation guarantee of 1.774 with respect to the natural linear programming relaxation of the problem. This significantly reduces the gap between the approximability of classical TSP and PCTSP, beating the previously best known approximation factor of 1.915. As a key ingredient of our improvement, we present a refined decomposition technique for solutions of the LP relaxation, and show how to leverage components of that decomposition as building blocks for our tours.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585159"}, {"primary_key": "1261351", "vector": [], "sparse_vector": [], "title": "Fast Algorithms via Dynamic-Oracle Matroids.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Danupon <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We initiate the study of matroid problems in a new oracle model called dynamic oracle. Our algorithms in this model lead to new bounds for some classic problems, and a \"unified\" algorithm whose performance matches previous results developed in various papers for various problems. We also show a lower bound that answers some open problems from a few decades ago. Concretely, our results are as follows.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585219"}, {"primary_key": "1261352", "vector": [], "sparse_vector": [], "title": "Gene<PERSON> Reed-Solomon Codes Achieve List-Decoding Capacity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In a recent paper, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> introduced higher order MDS codes as a generalization of MDS codes. An order-ℓ MDS code, denoted by MDS(ℓ), has the property that any ℓ subspaces formed from columns of its generator matrix intersect as minimally as possible. An independent work by <PERSON> defined a different notion of higher order MDS codes as those achieving a generalized singleton bound for list-decoding. In this work, we show that these two notions of higher order MDS codes are (nearly) equivalent.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585128"}, {"primary_key": "1261353", "vector": [], "sparse_vector": [], "title": "SDPs and Robust Satisfiability of Promise CSP.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "For a constraint satisfaction problem (CSP), a robust satisfaction algorithm is one that outputs an assignment satisfying most of the constraints on instances that are near-satisfiable. It is known that the CSPs that admit efficient robust satisfaction algorithms are precisely those of bounded width, i.e., CSPs whose satisfiability can be checked by a simple local consistency algorithm (eg., 2-SAT or Horn-SAT in the Boolean case). While the exact satisfiability of a bounded width CSP can be checked by combinatorial algorithms, the robust algorithm is based on rounding a canonical Semi Definite Programming(SDP) relaxation.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585180"}, {"primary_key": "1261354", "vector": [], "sparse_vector": [], "title": "Dynamic Maxflow via Dynamic Interior Point Methods.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we provide an algorithm for maintaining a (1−є)-approximate maximum flow in a dynamic, capacitated graph undergoing edge insertions. Over a sequence of m insertions to an n-node graph where every edge has capacity O(poly(m)) our algorithm runs in time O(m √n · є−1). To obtain this result we design dynamic data structures for the more general problem of detecting when the value of the minimum cost circulation in a dynamic graph undergoing edge insertions achieves value at most F (exactly) for a given threshold F. Over a sequence m insertions to an n-node graph where every edge has capacity O(poly(m)) and cost O(poly(m)) we solve this thresholded minimum cost flow problem in O(m √n). Both of our algorithms succeed with high probability against an adaptive adversary. We obtain these results by dynamizing the recent interior point method by [<PERSON> et al. ‍FOCS 2022] used to obtain an almost linear time algorithm for minimum cost flow, and introducing a new dynamic data structure for maintaining minimum ratio cycles in an undirected graph that succeeds with high probability against adaptive adversaries.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585135"}, {"primary_key": "1261355", "vector": [], "sparse_vector": [], "title": "External Memory Fully Persistent Search Trees.", "authors": ["<PERSON><PERSON><PERSON>rodal", "<PERSON>", "<PERSON>"], "summary": "We present the first fully-persistent external-memory search tree achieving amortized I/O bounds matching those of the classic (ephemeral) B-tree by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>. The insertion and deletion of a value in any version requires amortized O(logB Nv) I/Os and a range reporting query in any version requires worst-case O(logB Nv + K/B) I/Os, where K is the number of values reported, Nv is the number of values in the version v of the tree queried or updated, and B is the external-memory block size. The data structure requires space linear in the total number of updates. Compared to the previous best bounds for fully persistent B-trees [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, SODA 2012], this paper eliminates from the update bound an additive term of O(log2 B) I/Os. This result matches the previous best bounds for the restricted case of partial persistent B-trees [<PERSON><PERSON>, <PERSON> and <PERSON>, J<PERSON> 2003]. Central to our approach is to consider the problem as a dynamic set of two-dimensional rectangles that can be merged and split.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585140"}, {"primary_key": "1261356", "vector": [], "sparse_vector": [], "title": "The Randomized k-Server Conjecture Is False!", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove a few new lower bounds on the randomized competitive ratio for the k-server problem and other related problems, resolving some long-standing conjectures. In particular, for metrical task systems (MTS) we asympotically settle the competitive ratio and obtain the first improvement to an existential lower bound since the introduction of the model 35 years ago (in 1987).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585132"}, {"primary_key": "1261357", "vector": [], "sparse_vector": [], "title": "Towards the Erdős-Gallai Cycle Decomposition Conjecture.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In the 1960's, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> conjectured that the edges of any n-vertex graph can be decomposed into O(n) cycles and edges. We improve upon the previous best bound of O(n loglogn) cycles and edges due to <PERSON><PERSON>, <PERSON> and <PERSON><PERSON>, by showing an n-vertex graph can always be decomposed into O(n log⋆ n) cycles and edges, where log⋆n is the iterated logarithm function. Our arguments make use and further develop the theory of robust sublinear expander graphs.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585218"}, {"primary_key": "1261358", "vector": [], "sparse_vector": [], "title": "Algorithms Approaching the Threshold for Semi-random Planted Clique.", "authors": ["Rares-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We design new polynomial-time algorithms for recovering planted cliques in the semi-random graph model introduced by <PERSON><PERSON> and <PERSON><PERSON>. The previous best algorithms for this model succeed if the planted clique has size at least n2/3 in a graph with n vertices. Our algorithms work for planted-clique sizes approaching n1/2 — the information-theoretic threshold in the semi-random model and a conjectured computational threshold even in the easier fully-random model. This result comes close to resolving open questions by <PERSON><PERSON> and <PERSON><PERSON>.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585184"}, {"primary_key": "1261359", "vector": [], "sparse_vector": [], "title": "Stability Is Stable: Connections between Replicability, Privacy, and Adaptive Generalization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The notion of replicable algorithms was introduced by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (STOC'22) to describe randomized algorithms that are stable under the resampling of their inputs. More precisely, a replicable algorithm gives the same output with high probability when its randomness is fixed and it is run on a new i.i.d. sample drawn from the same distribution. Using replicable algorithms for data analysis can facilitate the verification of published results by ensuring that the results of an analysis will be the same with high probability, even when that analysis is performed on a new data set.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585246"}, {"primary_key": "1261360", "vector": [], "sparse_vector": [], "title": "The Complexity of Counting Planar Graph Homomorphisms of Domain Size 3.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove a complexity dichotomy theorem for counting planar graph homomorphisms of domain size 3. Given any 3 by 3 real valued symmetric matrix H defining a graph homomorphism from all planar graphs G ↦ ZH(G), we completely classify the computational complexity of this problem according to the matrix H. We show that for every H, the problem is either polynomial time computable or #P-hard. The P-time computable cases consist of precisely those that are P-time computable for general graphs (a complete classification is known) or computable by <PERSON><PERSON>'s holographic algorithm via matchgates. We also prove several results about planar graph homomorphisms for general domain size q. The proof uses mainly analytic arguments.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585173"}, {"primary_key": "1261361", "vector": [], "sparse_vector": [], "title": "On the Optimal Fixed-Price Mechanism in Bilateral Trade.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of social welfare maximization in bilateral trade, where two agents, a buyer and a seller, trade an indivisible item. The seminal result of <PERSON><PERSON> and <PERSON> shows that no incentive compatible and budget balanced (i.e., the mechanism does not run a deficit) mechanism can achieve the optimal social welfare in bilateral trade. Motivated by this impossibility result, we focus on approximating the optimal social welfare. We consider arguably the simplest form of mechanisms – the fixed-price mechanisms, where the designer offers trade at a fixed price to the seller and buyer. Besides the simple form, fixed-price mechanisms are also the only dominant strategy incentive compatible and budget balanced mechanisms in bilateral trade.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585171"}, {"primary_key": "1261362", "vector": [], "sparse_vector": [], "title": "Computing Better Approximate Pure Nash Equilibria in Cut Games via Semidefinite Programming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cut games are among the most fundamental strategic games in algorithmic game theory. It is well-known that computing an exact pure Nash equilibrium in these games is PLS-hard, so research has focused on computing approximate equilibria. We present a polynomial-time algorithm that computes 2.7371-approximate pure Nash equilibria in cut games. This is the first improvement to the previously best-known bound of 3, due to the work of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> from EC 2010. Our algorithm is based on a general recipe proposed by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and S<PERSON>ik from FOCS 2011 and applied on several potential games since then. The first novelty of our work is the introduction of a phase that can identify subsets of players who can simultaneously improve their utilities considerably. This is done via semidefinite programming and randomized rounding. In particular, a negative objective value to the semidefinite program guarantees that no such considerable improvement is possible for a given set of players. Otherwise, randomized rounding of the SDP solution is used to identify a set of players who can simultaneously improve their strategies considerably and allows the algorithm to make progress. The way rounding is performed is another important novelty of our work. Here, we exploit an idea that dates back to a paper by <PERSON><PERSON> and <PERSON> from 1995, but we take it to an extreme that has not been analyzed before.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585236"}, {"primary_key": "1261363", "vector": [], "sparse_vector": [], "title": "Fredman&apos;s Trick Meets Dominance Product: Fine-Grained Complexity of Unweighted APSP, 3SUM Counting, and More.", "authors": ["<PERSON>", "Virginia Vassilevska Williams", "<PERSON><PERSON><PERSON> Xu"], "summary": "In this paper we carefully combine <PERSON><PERSON>'s trick [SICOMP'76] and <PERSON><PERSON><PERSON><PERSON>'s approach for dominance product [IPL'91] to obtain powerful results in fine-grained complexity.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585237"}, {"primary_key": "1261364", "vector": [], "sparse_vector": [], "title": "A Characterization of List Learnability.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A classical result in learning theory shows the equivalence of PAC learnability of binary hypothesis classes and the finiteness of VC dimension. Extending this to the multiclass setting was an open problem, which was settled in a recent breakthrough result characterizing multiclass PAC learnability via the DS dimension introduced earlier by <PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585190"}, {"primary_key": "1261365", "vector": [], "sparse_vector": [], "title": "Randomized versus Deterministic Decision Tree Size.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A classic result of <PERSON><PERSON> [SICOM<PERSON> '91] states that the deterministic decision tree *depth* complexity of every total Boolean function is at most the cube of its randomized decision tree *depth* complexity. The question whether randomness helps in significantly reducing the *size* of decision trees appears not to have been addressed. We show that the logarithm of the deterministic decision tree size complexity of every total Boolean function on n input variables is at most the fourth power of the logarithm of its bounded-error randomized decision tree size complexity, ignoring a polylogarithmic factor in the input size.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585199"}, {"primary_key": "1261366", "vector": [], "sparse_vector": [], "title": "Learning Polynomial Transformations via Generalized Tensor Decompositions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>zhi Li", "<PERSON><PERSON>"], "summary": "We consider the problem of learning high dimensional polynomial transformations of Gaussians. Given samples of the form f(x), where x∼N(0,Ir) is hidden and f: ℝr → ℝd is a function where every output coordinate is a low-degree polynomial, the goal is to learn the distribution over f(x). One can think of this as a simple model for learning deep generative models, namely pushforwards of Gaussians under two-layer neural networks with polynomial activations, though the learning problem is mathematically natural in its own right.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585209"}, {"primary_key": "1261367", "vector": [], "sparse_vector": [], "title": "Streaming Euclidean MST to a Constant Factor.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study streaming algorithms for the fundamental geometric problem of computing the cost of the Euclidean Minimum Spanning Tree (MST) on an n-point set X ⊂ ℝd. In the streaming model, the points in X can be added and removed arbitrarily, and the goal is to maintain an approximation in small space. In low dimensions, (1+є) approximations are possible in sublinear space [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>C<PERSON> '05]. However, for high dimensional spaces the best known approximation for this problem was Õ(logn), due to [<PERSON>, <PERSON>, <PERSON>, <PERSON>, STOC '22], improving on the prior O(log2 n) bound due to [<PERSON><PERSON>, STOC '04] and [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>gamer, SODA '08]. In this paper, we break the logarithmic barrier, and give the first constant factor sublinear space approximation to Euclidean MST. For any є≥ 1, our algorithm achieves an Õ(є−2) approximation in nO(є) space.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585168"}, {"primary_key": "1261368", "vector": [], "sparse_vector": [], "title": "Range Avoidance, Remote Point, and Hard Partial Truth Table via Satisfying-Pairs Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Ren"], "summary": "The range avoidance problem, denoted as C-Avoid, asks to find a non-output of a given C-circuit C:0,1^n -> 0,1^l with stretch l>n. This problem has recently received much attention in complexity theory for its connections with circuit lower bounds and other explicit construction problems. Inspired by the Algorithmic Method for circuit lower bounds, <PERSON>, <PERSON><PERSON>, and <PERSON> (FOCS'22) established a framework to design FP^NP algorithms for C-Avoid via slightly non-trivial data structures related to C. However, a major drawback of their approach is the lack of unconditional results even for C=AC^0.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585147"}, {"primary_key": "1261369", "vector": [], "sparse_vector": [], "title": "Streaming Euclidean Max-Cut: Dimension vs Data Reduction.", "authors": ["<PERSON><PERSON>", "Shaofeng H.-<PERSON><PERSON> Jiang", "<PERSON>"], "summary": "Max-Cut is a fundamental problem that has been studied extensively in various settings. We design an algorithm for Euclidean Max-Cut, where the input is a set of points in ℝd, in the model of dynamic geometric streams, where the input X ⊆ [Δ]d is presented as a sequence of point insertions and deletions. Previously, <PERSON><PERSON><PERSON> and <PERSON> [STOC 2005] designed a (1+є)-approximation algorithm for the low-dimensional regime, i.e., it uses space exp(d).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585170"}, {"primary_key": "1261370", "vector": [], "sparse_vector": [], "title": "Complexity of Equilibria in First-Price Auctions under General Tie-Breaking Rules.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We study the complexity of finding an approximate (pure) Bayesian Nash equilibrium in a first-price auction with common priors when the tie-breaking rule is part of the input. We show that the problem is PPAD-complete even when the tie-breaking rule is trilateral (i.e., it specifies item allocations when no more than three bidders are in tie, and adopts the uniform tie-breaking rule otherwise). This is the first hardness result for equilibrium computation in first-price auctions with common priors. On the positive side, we give a PTAS for the problem under the uniform tie-breaking rule.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585195"}, {"primary_key": "1261371", "vector": [], "sparse_vector": [], "title": "What Makes a Good Fisherman? Linear Regression under Self-Selection Bias.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Manolis Zampetakis"], "summary": "In the classical setting of self-selection, the goal is to learn k models simultaneously, from observations (x(i), y(i)) where y(i) is the output of one of k underlying models on input x(i). In contrast to mixture models, where we observe the output of a randomly selected model (and therefore the selection of which model is observed is exogenous), in self-selection models the observed model depends on the realized outputs of the underlying models themselves, as determined by some known selection criterion (e.g., we might observe the highest output, the smallest output, or the median output of the k models), and is thus endogenous. In known-index self-selection, the identity of the observed model output is observable; in unknown-index self-selection, it is not. Self-selection has a long history in Econometrics (going back to the works of <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and others) and many applications in various theoretical and applied fields, including treatment effect estimation, imitation learning, learning from strategically reported data, and learning from markets at disequilibrium.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585177"}, {"primary_key": "1261372", "vector": [], "sparse_vector": [], "title": "The Smoothed Complexity of Policy Iteration for Markov Decision Processes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show subexponential lower bounds (i.e., 2Ω (nc)) on the smoothed complexity of the classical Howard's Policy Iteration algorithm for Markov Decision Processes. The bounds hold for the total reward and the average reward criteria. The constructions are robust in the sense that the subexponential bound holds not only on the average for independent random perturbations of the MDP parameters (transition probabilities and rewards), but for all arbitrary perturbations within an inverse polynomial range. We show also an exponential lower bound on the worst-case complexity for the simple reachability objective.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585220"}, {"primary_key": "1261373", "vector": [], "sparse_vector": [], "title": "Improved Dynamic Colouring of Sparse Graphs.", "authors": ["Aleksander Bj<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Given a dynamic graph subject to edge insertions and deletions, we show how to update an implicit representation of a proper vertex colouring, such that colours of vertices are computable upon query time. We give a deterministic algorithm that uses O(α2) colours for a dynamic graph of arboricity α, and a randomised algorithm that uses O(min{α logα, α log log log n}) colours in the oblivious adversary model. Our deterministic algorithm has update- and query times polynomial in α and log n, and our randomised algorithm has amortised update- and query time that with high probability is polynomial in log n with no dependency on the arboricity. Thus, we improve the number of colours exponentially compared to the state-of-the art for implicit colouring, namely from O(2α) colours, and we approach the theoretical lower bound of Ω(α) for this arboricity-parameterised approach. Simultaneously, our randomised algorithm improves the update- and query time to run in time solely polynomial in log n with no dependency on α. Our algorithms are fully adaptive to the current value of the dynamic arboricity at query or update time.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585111"}, {"primary_key": "1261374", "vector": [], "sparse_vector": [], "title": "The Power of Multi-step Vizing Chains.", "authors": ["Aleksander Bj<PERSON><PERSON>"], "summary": "Recent papers have addressed different variants of the (Δ + 1)-edge-colouring problem by concatenating or gluing together many Vizing chains to form what <PERSON><PERSON><PERSON><PERSON> coined multi-step Vizing chains. In this paper, we consider the most general definition of this term and apply different multi-step Vizing chain constructions to prove combinatorial properties of edge-colourings that lead to (improved) algorithms for computing edge-colouring across different models of computation. This approach seems especially powerful for constructing augmenting subgraphs which respect some notion of locality. First, we construct strictly local multi-step Vizing chains and use them to show a local version of V<PERSON>’s Theorem thus confirming a recent conjecture of <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>. That is, we show that there exists a proper edge-colouring of a graph such that every edge uv receives a colour from the list {1,2, …, max{d(u),d(v)}+1}. Our proof is constructive and also implies an O(n2 Δ) time algorithm for computing such a colouring. Then, we show that for any uncoloured edge there exists an augmenting subgraph of size O(Δ7logn), answering an open problem of <PERSON><PERSON><PERSON><PERSON>. <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> show a lower bound of Ω(Δ log /Δ) for the size of augmenting subgraphs, so the upper bound is asymptotically tight up to Δ factors. These ideas also extend to give a faster deterministic LOCAL algorithm for (Δ + 1)-edge-colouring running in Õ(poly(Δ)log6 n) rounds. These results improve the dependency on logn compared to the recent breakthrough result of <PERSON>shteyn, who showed the existence of augmenting subgraphs of size O(Δ6log2 n), and used these to give the first (Δ + 1)-edge-colouring algorithm in the LOCAL model running in O(poly(Δ, logn)) rounds. Finally for dynamic graphs, we show how to maintain a(1+ε)Δ-edge-colouring fully adaptive to Δ in O(ε−6 log9 n log6 Δ) worst-case update time w.h.p without any restrictions on Δ. This should be compared to the edge-colouring algorithm of Duan, He and Zhang that runs in O(ε−4log8 n) amortised update time w.h.p under the condition that Δ = Ω(ε−2log2 n). Our algorithm avoids the use of O(ε−1logn) copies of the graph, resulting in a smaller space consumption and an algorithm with provably low recourse.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585105"}, {"primary_key": "1261375", "vector": [], "sparse_vector": [], "title": "A New Deterministic Algorithm for Fully Dynamic All-Pairs Shortest Paths.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the fully dynamic All-Pairs Shortest Paths (APSP) problem in undirected edge-weighted graphs. Given an n-vertex graph G with non-negative edge lengths, that undergoes an online sequence of edge insertions and deletions, the goal is to support approximate distance queries and shortest-path queries. We provide a deterministic algorithm for this problem, that, for a given precision parameter є, achieves approximation factor (loglogn)2O(1/є3), and has amortized update time O(nєlogL) per operation, where L is the ratio of longest to shortest edge length. Query time for distance-query is O(2O(1/є)· logn· loglogL), and query time for shortest-path query is O(|E(P)|+2O(1/є)· logn· loglogL), where P is the path that the algorithm returns. To the best of our knowledge, even allowing any o(n)-approximation factor, no adaptive-update algorithms with better than Θ(m) amortized update time and better than Θ(n) query time were known prior to this work. We also note that our guarantees are stronger than the best current guarantees for APSP in decremental graphs in the adaptive-adversary setting.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585196"}, {"primary_key": "1261376", "vector": [], "sparse_vector": [], "title": "Approximate Graph Colouring and the Hollow Shadow.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We show that approximate graph colouring is not solved by constantly many levels of the lift-and-project hierarchy for the combined basic linear programming and affine integer programming relaxation. The proof involves a construction of tensors whose fixed-dimensional projections are equal up to reflection and satisfy a sparsity condition, which may be of independent interest.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585112"}, {"primary_key": "1261377", "vector": [], "sparse_vector": [], "title": "Optimal Differentially Private Learning of Thresholds and Quasi-Concave Optimization.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The problem of learning threshold functions is a fundamental one in machine learning. Classical learning theory implies sample complexity of O(ξ−1 log(1/β)) (for generalization error ξ with confidence 1−β). The private version of the problem, however, is more challenging and in particular, the sample complexity must depend on the size |X| of the domain. Progress on quantifying this dependence, via lower and upper bounds, was made in a line of works over the past decade. In this paper, we finally close the gap for approximate-DP and provide a nearly tight upper bound of O(log* |X|), which matches a lower bound by <PERSON><PERSON> et al (that applies even with improper learning) and improves over a prior upper bound of O((log* |X|)1.5) by <PERSON> et al. We also provide matching upper and lower bounds of Θ(2log*|X|) for the additive error of private quasi-concave optimization (a related and more general problem). Our improvement is achieved via the novel Reorder-Slice-Compute paradigm for private data analysis which we believe will have further applications.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585148"}, {"primary_key": "1261378", "vector": [], "sparse_vector": [], "title": "Approximating Iterated Multiplication of Stochastic Matrices in Small Space.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Matrix powering, and more generally iterated matrix multiplication, is a fundamental linear algebraic primitive with myriad applications in computer science. Of particular interest is the problem's space complexity as it constitutes the main route towards resolving the BPL vs. L problem. The seminal work by <PERSON><PERSON> <PERSON> [JCSS '99] gives a deterministic algorithm for approximating the product of n stochastic matrices of dimension w × w in space O(log3/2n + √logn · logw). The first improvement upon <PERSON><PERSON><PERSON><PERSON> was achieved by <PERSON><PERSON> [RANDOM '21] who gave a logarithmic improvement in the n=poly(w) regime, attaining O(1/√loglogn · log3/2n) space.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585181"}, {"primary_key": "1261379", "vector": [], "sparse_vector": [], "title": "Random Walks on Rotating Expanders.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Random walks on expanders are a powerful tool which found applications in many areas of theoretical computer science, and beyond. However, they come with an inherent cost – the spectral expansion of the corresponding power graph deteriorates at a rate that is exponential in the length of the walk. As an example, when G is a d-regular Ramanujan graph, the power graph Gt has spectral expansion 2Ω(t) √D, where D = dt is the regularity of Gt, thus, Gt is 2Ω(t) away from being Ramanujan. This exponential blowup manifests itself in many applications.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585133"}, {"primary_key": "1261380", "vector": [], "sparse_vector": [], "title": "A Constant Factor Prophet Inequality for Online Combinatorial Auctions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In online combinatorial auctions m indivisible items are to be allocated to n agents who arrive online. Agents have random valuations for the different subsets of items and the goal is to allocate the items on the fly so as to maximize the total value of the assignment. A prophet inequality in this setting refers to the existence of an online algorithm guaranteed to obtain, in expectation, a certain fraction of the expected value obtained by an optimal solution in hindsight. The study of prophet inequalities for online combinatorial auctions has been an intensive area of research in recent years, and constant factor prophet inequalities are known when the agents' valuation functions are submodular or fractionally subadditive. Despite many efforts, for the more general case of subadditive valuations, the best known prophet inequality has an approximation guarantee of O(loglogm). In this paper, we prove the existence of a constant factor prophet inequality for the subadditive case, resolving a central open problem in the area.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585151"}, {"primary_key": "1261381", "vector": [], "sparse_vector": [], "title": "Mind the Gap: Achieving a Super-Grover Quantum Speedup by Jumping to the End.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Fernando G. S. L. Brandão"], "summary": "We present a quantum algorithm that has rigorous runtime guarantees for several families of binary optimization problems, including Quadratic Unconstrained Binary Optimization (QUBO), Ising spin glasses (p-spin model), and k-local constraint satisfaction problems (k-CSP). We show that either (a) the algorithm finds the optimal solution in time O*(2(0.5−c)n) for an n-independent constant c, a 2cn advantage over <PERSON><PERSON>'s algorithm; or (b) there are sufficiently many low-cost solutions such that classical random guessing produces a (1−η) approximation to the optimal cost value in sub-exponential time for arbitrarily small choice of η. Additionally, we show that for a large fraction of random instances from the k-spin model and for any fully satisfiable or slightly frustrated k-CSP formula, statement (a) is the case. The algorithm and its analysis are largely inspired by <PERSON>' short-path algorithm.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585203"}, {"primary_key": "1261382", "vector": [], "sparse_vector": [], "title": "Stochastic Minimum Vertex Cover in General Graphs: A 3/2-Approximation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the stochastic vertex cover problem. In this problem, G = (V, E) is an arbitrary known graph, and G⋆ is an unknown random subgraph of G where each edge e is realized independently with probability p. Edges of G⋆ can only be verified using edge queries. The goal in this problem is to find a minimum vertex cover of G⋆ using a small number of queries.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585230"}, {"primary_key": "1261383", "vector": [], "sparse_vector": [], "title": "A Strongly Polynomial Algorithm for Approximate Forster Transforms and Its Application to Halfspace Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Forster transform is a method of regularizing a dataset by placing it in radial isotropic position while maintaining some of its essential properties. Forster transforms have played a key role in a diverse range of settings spanning computer science and functional analysis. Prior work had given weakly polynomial time algorithms for computing Forster transforms, when they exist. Our main result is the first strongly polynomial time algorithm to compute an approximate Forster transform of a given dataset or certify that no such transformation exists. By leveraging our strongly polynomial Forster algorithm, we obtain the first strongly polynomial time algorithm for distribution-free PAC learning of halfspaces. This learning result is surprising because proper PAC learning of halfspaces is equivalent to linear programming. Our learning approach extends to give a strongly polynomial halfspace learner in the presence of random classification noise and, more generally, Massart noise.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585191"}, {"primary_key": "1261384", "vector": [], "sparse_vector": [], "title": "New High Dimensional Expanders from Covers.", "authors": ["<PERSON><PERSON>"], "summary": "We present a new construction of high dimensional expanders based on covering spaces of simplicial complexes. High dimensional expanders (HDXs) are hypergraph analogues of expander graphs. They have many uses in theoretical computer science, but unfortunately only few constructions are known which have arbitrarily small local spectral expansion.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585183"}, {"primary_key": "1261385", "vector": [], "sparse_vector": [], "title": "Good Quantum LDPC Codes with Linear Time Decoders.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We construct a new explicit family of good quantum low-density parity-check codes which additionally have linear time decoders. Our codes are based on a three-term chain (2m× m)V →δ0 (2m)E →δ1 2F where V (X-checks) are the vertices, E (qubits) are the edges, and F (Z-checks) are the squares of a left-right <PERSON><PERSON>ley complex, and where the maps are defined based on a pair of constant-size random codes CA,CB:2m→2Δ where Δ is the regularity of the underlying <PERSON><PERSON>ley graphs.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585101"}, {"primary_key": "1261386", "vector": [], "sparse_vector": [], "title": "Almost Chor-Goldreich Sources and Adversarial Random Walks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A Chor–Gold<PERSON>ich (CG) source is a sequence of random variables X = X1 ∘ … ∘ Xt, where each Xi ∼ {0,1}d and <PERSON> has δ d min-entropy conditioned on any fixing of X1 ∘ … ∘ Xi−1. The parameter 0<δ≤ 1 is the entropy rate of the source. We typically think of d as constant and t as growing. We extend this notion in several ways, defining almost CG sources. Most notably, we allow each Xi to only have conditional Shannon entropy δ d.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585134"}, {"primary_key": "1261387", "vector": [], "sparse_vector": [], "title": "First-Order Model Checking on Structurally Sparse Graph Classes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A class of graphs is structurally nowhere dense if it can be constructed from a nowhere dense class by a first-order transduction. Structurally nowhere dense classes vastly generalize nowhere dense classes and constitute important examples of monadically stable classes. We show that the first-order model checking problem is fixed-parameter tractable on every structurally nowhere dense class of graphs.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585186"}, {"primary_key": "1261388", "vector": [], "sparse_vector": [], "title": "Multi-agent Contracts.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study a natural combinatorial single-principal multi-agent contract design problem, in which a principal motivates a team of agents to exert effort toward a given task. At the heart of our model is a reward function, which maps the agent efforts to an expected reward of the principal. We seek to design computationally efficient algorithms for finding optimal (or near-optimal) linear contracts for reward functions that belong to the complement-free hierarchy.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585193"}, {"primary_key": "1261389", "vector": [], "sparse_vector": [], "title": "The Rate of Interactive Codes Is Bounded Away from 1.", "authors": ["<PERSON><PERSON>", "Gillat Kol", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> and <PERSON><PERSON> [STOC 2013] showed how to simulate any alternating two-party communication protocol designed to work over the noiseless channel, by a protocol that works over a stochastic channel that corrupts each sent symbol with probability ‍є>0 independently, with only a 1+O(√(є)) blowup to the communication. In particular, this implies that the maximum rate of such interactive codes approaches 1 as є goes to ‍0, as is also the case for the maximum rate of classical error correcting codes. Over the past decade, followup works have strengthened and generalized this result to other noisy channels, stressing on how fast the rate approaches 1 as є goes to 0, but retaining the assumption that the noiseless protocol is alternating.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585249"}, {"primary_key": "1261390", "vector": [], "sparse_vector": [], "title": "Noise Stability on the Boolean Hypercube via a Renormalized Brownian Motion.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider a variant of the classical notion of noise on the Boolean hypercube which gives rise to a new approach to inequalities regarding noise stability. We use this approach to give a new proof of the Majority is Stablest theorem by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, improving the dependence of the bound on the maximal influence of the function from logarithmic to polynomial. We also show that a variant of the conjecture by <PERSON><PERSON> and <PERSON> regarding the most informative Boolean function, where the classical noise is replaced by our notion, holds true. Our approach is based on a stochastic construction that we call the renormalized Brownian motion, which facilitates the use of inequalities in Gaussian space in the analysis of Boolean functions.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585118"}, {"primary_key": "1261391", "vector": [], "sparse_vector": [], "title": "An Optimal &quot;It Ain&apos;t Over Till It&apos;s Over&quot; Theorem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the probability of Boolean functions with small max influence to become constant under random restrictions. Let f be a Boolean function such that the variance of f is Ω(1) and all its individual influences are bounded by τ. We show that when restricting all but a ρ=Ω((log1/τ)−1) fraction of the coordinates, the restricted function remains nonconstant with overwhelming probability. This bound is essentially optimal, as witnessed by the tribes function =n/Clogn∘Clogn.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585205"}, {"primary_key": "1261392", "vector": [], "sparse_vector": [], "title": "An Analogue of Bonami&apos;s Lemma for Functions on Spaces of Linear Maps, and 2-2 Games.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove an analogue of <PERSON><PERSON>'s (hypercontractive) lemma for complex-valued functions on L (𝑉 ,𝑊 ), where 𝑉 and 𝑊 are vector spaces over a finite field. This inequality is useful for functions on L (𝑉 ,𝑊 ) whose 'generalised influences' are small, in an appropriate sense. It leads to a significant shortening of the proof of a recent seminal result by <PERSON><PERSON>, <PERSON> and <PERSON> that pseudorandom sets in Grassmann graphs have near-perfect expansion, which (in combination with the work of <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>) implies the 2-2 Games conjecture (the variant, that is, with imperfect completeness)", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585116"}, {"primary_key": "1261393", "vector": [], "sparse_vector": [], "title": "Credible Decentralized Exchange Design via Verifiable Sequencing Rules.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Trading on decentralized exchanges has been one of the primary use cases for permissionless blockchains with daily trading volume exceeding billions of U.S. ‍dollars. In the status quo, users broadcast transactions they wish to execute in the exchange and miners are responsible for composing a block of transactions and picking an execution ordering — the order in which transactions execute in the exchange. Due to the lack of a regulatory framework, it is common to observe miners exploiting their privileged position by front-running transactions and obtaining risk-fee profits. Indeed, the Flashbots service institutionalizes this exploit, with miners auctioning the right to front-run transactions. In this work, we propose to modify the interaction between miners and users and initiate the study of verifiable sequencing rules. As in the status quo, miners can determine the content of a block; however, they commit to respecting a sequencing rule that constrains the execution ordering and is verifiable (there is a polynomial time algorithm that can verify if the execution ordering satisfies such constraints). Thus in the event a miner deviates from the sequencing rule, anyone can generate a proof of non-compliance.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585233"}, {"primary_key": "1261394", "vector": [], "sparse_vector": [], "title": "Deterministic Incremental APSP with Polylogarithmic Update Time and Stretch.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We provide the first deterministic data structure that given a weighted undirected graph undergoing edge insertions, processes each update with polylogarithmic amortized update time and answers queries for the distance between any pair of vertices in the current graph with a polylogarithmic approximation in O(loglogn) time.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585213"}, {"primary_key": "1261395", "vector": [], "sparse_vector": [], "title": "Pandora Box Problem with Nonobligatory Inspection: Hardness and Approximation Scheme.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> (1979) introduced the Pandora Box problem as a model for sequential search with inspection costs, and gave an elegant index-based policy that attains provably optimal expected payoff. In various scenarios, the searching agent may select an option without making a costly inspection. The variant of the Pandora box problem with non-obligatory inspection has attracted interest from both economics and algorithms researchers. Various simple algorithms have proved suboptimal, with the best known 0.8-approximation algorithm due to <PERSON><PERSON><PERSON> et al. (2008). No hardness result for the problem was known.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585229"}, {"primary_key": "1261396", "vector": [], "sparse_vector": [], "title": "Approximating Nash Social Welfare by Matching and Local Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Wenzheng Li", "László A. <PERSON>", "<PERSON>"], "summary": "For any >0, we give a simple, deterministic (4+)-approximation algorithm for the Nash social welfare (NSW) problem under submodular valuations. The previous best approximation factor was 380 via a randomized algorithm. We also consider the asymmetric variant of the problem, where the objective is to maximize the weighted geometric mean of agents' valuations, and give an (ω + 2 + ) -approximation if the ratio between the largest weight and the average weight is at most ω.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585255"}, {"primary_key": "1261397", "vector": [], "sparse_vector": [], "title": "Hard Languages in NP ∩ coNP and NIZK Proofs from Unstructured Hardness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The existence of \"unstructured\" hard languages in NP ∩ coNP is an intriguing open question. <PERSON> and <PERSON> (SICOMP, 1981) asked whether P is separated from NP ∩ coNP relative to a random oracle, a question that remained open ever since. While a hard language in NP ∩ coNP can be constructed in a black-box way from a one-way permutation, for which only few (structured) candidates exist, <PERSON><PERSON><PERSON> et al. (SICOMP, 2021) ruled out such a construction based on an injective one-way function, an unstructured primitive that is easy to instantiate heuristically. In fact, the latter holds even with a black-box use of indistinguishability obfuscation.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585119"}, {"primary_key": "1261398", "vector": [], "sparse_vector": [], "title": "A Moment-Matching Approach to Testable Learning and a New Characterization of Rademacher Complexity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A remarkable recent paper by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (2022) initiated the study of testable learning, where the goal is to replace hard-to-verify distributional assumptions (such as Gaussianity) with efficiently testable ones and to require that the learner succeed whenever the unknown distribution passes the corresponding test. In this model, they gave an efficient algorithm for learning halfspaces under testable assumptions that are provably satisfied by Gaussians.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585206"}, {"primary_key": "1261399", "vector": [], "sparse_vector": [], "title": "A New Berry-<PERSON><PERSON><PERSON> for Expander Walks.", "authors": ["<PERSON>"], "summary": "We prove that the sum of t boolean-valued random variables sampled by a random walk on a regular expander converges in total variation distance to a discrete normal distribution at a rate of O(λ/t1/2−o(1)), where λ is the second largest eigenvalue of the random walk matrix in absolute value. To the best of our knowledge, among known Berry-Esseen bounds for Markov chains, our result is the first to show convergence in total variation distance, and is also the first to incorporate a linear dependence on expansion λ. In contrast, prior Markov chain Berry-Esseen bounds showed a convergence rate of O(1/√t) in weaker metrics such as Kolmogorov distance.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585141"}, {"primary_key": "1261400", "vector": [], "sparse_vector": [], "title": "Planning and Learning in Partially Observable Systems via Filter Stability.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Partially Observable Markov Decision Processes (POMDPs) are an important model in reinforcement learning that take into account the agent's uncertainty about its current state. In the literature on POMDPs, it is customary to assume access to a planning oracle that computes an optimal policy when the parameters are known, even though this problem is known to be computationally hard. The major obstruction is the Curse of History, which arises because optimal policies for POMDPs may depend on the entire observation history thus far. In this work, we revisit the planning problem and ask: Are there natural and well-motivated assumptions that avoid the Curse of History in POMDP planning (and beyond)?", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585099"}, {"primary_key": "1261401", "vector": [], "sparse_vector": [], "title": "An Efficient Decoder for a Linear Distance Quantum LDPC Code.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent developments have shown the existence of quantum low-density parity check (qLDPC) codes with constant rate and linear distance. A natural question concerns the efficient decodability of these codes. In this paper, we present a linear time decoder for the recent quantum Tanner codes construction of asymptotically good qLDPC codes, which can correct all errors of weight up to a constant fraction of the blocklength. Our decoder is an iterative algorithm which searches for corrections within constant-sized regions. At each step, the corrections are found by reducing a locally defined and efficiently computable cost function which serves as a proxy for the weight of the remaining error.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585169"}, {"primary_key": "1261402", "vector": [], "sparse_vector": [], "title": "Optimal Bounds for Noisy Sorting.", "authors": ["Yuzhou Gu", "<PERSON><PERSON><PERSON> Xu"], "summary": "Sorting is a fundamental problem in computer science. In the classical setting, it is well-known that (1± o(1)) nlog2 n comparisons are both necessary and sufficient to sort a list of n elements. In this paper, we study the Noisy Sorting problem, where each comparison result is flipped independently with probability p for some fixed p∈ (0, 1/2). As our main result, we show that (1± o(1)) ( 1/I(p) + 1/(1−2p) log2 (1−p/p) ) nlog2 n noisy comparisons are both necessary and sufficient to sort n elements with error probability o(1) using noisy comparisons, where I(p)=1 + plog2 p+(1−p)log2 (1−p) is capacity of BSC channel with crossover probability p. This simultaneously improves the previous best lower and upper bounds (<PERSON>, <PERSON> and <PERSON>, ISIT 2022) for this problem.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585131"}, {"primary_key": "1261403", "vector": [], "sparse_vector": [], "title": "Commitments to Quantum States.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "What does it mean to commit to a quantum state? In this work, we propose a simple answer: a commitment to quantum messages is binding if, after the commit phase, the committed state is hidden from the sender's view. We accompany this new definition with several instantiations. We build the first non-interactive succinct quantum state commitments, which can be seen as an analogue of collision-resistant hashing for quantum messages. We also show that hiding quantum state commitments (QSCs) are implied by any commitment scheme for classical messages. All of our constructions can be based on quantum-cryptographic assumptions that are implied by but are potentially weaker than one-way functions.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585198"}, {"primary_key": "1261404", "vector": [], "sparse_vector": [], "title": "Extractors for Images of Varieties.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We construct explicit deterministic extractors for polynomial images of varieties, that is, distributions sampled by applying a low-degree polynomial map f : Fqr → Fqn to an element sampled uniformly at random from a k-dimensional variety V ⊆ Fqr. This class of sources generalizes both polynomial sources, studied by <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (FOCS 2007, Comput. Complex. 2009), and variety sources, studied by <PERSON><PERSON> (CCC 2009, Comput. Complex. 2012).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585109"}, {"primary_key": "1261405", "vector": [], "sparse_vector": [], "title": "Binary Error-Correcting Codes with Minimal Noiseless Feedback.", "authors": ["<PERSON><PERSON>", "<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "In the setting of error-correcting codes with feedback, <PERSON> wishes to communicate a k-bit message x to <PERSON> by sending a sequence of bits over a channel while noiselessly receiving feedback from <PERSON>. It has been long known (<PERSON><PERSON><PERSON>, 1964) that in this model, <PERSON> can still correctly determine x even if ≈ 1/3 of <PERSON>'s bits are flipped adversarially. This improves upon the classical setting without feedback, where recovery is not possible for error fractions exceeding 1/4.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585126"}, {"primary_key": "1261406", "vector": [], "sparse_vector": [], "title": "Efficient Interactive Coding Achieving Optimal Error Resilience over the Binary Channel.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Given a noiseless protocol π0 computing a function f(x, y) of <PERSON> and <PERSON>'s private inputs x, y, the goal of interactive coding is to construct an error-resilient protocol π computing f such that even if some fraction of the communication is adversarially corrupted, both parties still learn f(x, y). Ideally, the resulting scheme π should be positive rate, computationally efficient, and achieve optimal error resilience.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585162"}, {"primary_key": "1261407", "vector": [], "sparse_vector": [], "title": "Maximum Length-Constrained Flows and Disjoint Paths: Distributed, Deterministic, and Fast.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Thatchaphol <PERSON>"], "summary": "Computing routing schemes that support both high throughput and low latency is one of the core challenges of network optimization. Such routes can be formalized as h-length flows which are defined as flows whose flow paths have length at most h. Many well-studied algorithmic primitives—such as maximal and maximum length-constrained disjoint paths—are special cases of h-length flows. Likewise the optimal h-length flow is a fundamental quantity in network optimization, characterizing, up to poly-log factors, how quickly a network can accomplish numerous distributed primitives.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585202"}, {"primary_key": "1261408", "vector": [], "sparse_vector": [], "title": "A Borsuk-Ulam Lower Bound for Sign-Rank and Its Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a new topological argument based on the <PERSON><PERSON><PERSON><PERSON> theorem to prove a lower bound on sign-rank.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585210"}, {"primary_key": "1261409", "vector": [], "sparse_vector": [], "title": "Depth-d <PERSON><PERSON><PERSON>old Circuits vs. Depth-(d+1) AND-OR Trees.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For any n ∈ ℕ and d = o(loglog(n)), we prove that there is a Boolean function F on n bits and a value γ = 2−Θ(d) such that F can be computed by a uniform depth-(d + 1) AC0 circuit with O(n) wires, but F cannot be computed by any depth-d TC0 circuit with n1 + γ wires. This bound matches the current state-of-the-art lower bounds for computing explicit functions by threshold circuits of depth d > 2, which were previously known only for functions outside AC0 such as the parity function. Furthermore, in our result, the AC0 circuit computing F is a monotone *read-once formula* (i.e., an AND-OR tree), and the lower bound holds even in the average-case setting with respect to advantage n−γ.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585216"}, {"primary_key": "1261410", "vector": [], "sparse_vector": [], "title": "Approximating Binary Longest Common Subsequence in Almost-Linear Time.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The Longest Common Subsequence (LCS) is a fundamental string similarity measure, and computing the LCS of two strings is a classic algorithms question. A textbook dynamic programming algorithm gives an exact algorithm in quadratic time, and this is essentially best possible under plausible fine-grained complexity assumptions, so a natural problem is to find faster approximation algorithms. When the inputs are two binary strings, there is a simple 1/2-approximation in linear time: compute the longest common all-0s or all-1s subsequence. It has been open whether a better approximation is possible even in truly subquadratic time. <PERSON><PERSON> and <PERSON> showed that the answer is yes under the assumption that the two input strings have equal lengths. We settle the question, generalizing their result to unequal length strings, proving that, for any ε>0, there exists δ>0 and a (1/2+δ)-approximation algorithm for binary LCS that runs in n1+ε time. As a consequence of our result and a result of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, for any ε>0, there exists a (1/q+δ)-approximation for LCS over q-ary strings in n1+ε time.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585104"}, {"primary_key": "1261411", "vector": [], "sparse_vector": [], "title": "Capturing One-Way Functions via NP-Hardness of Meta-Complexity.", "authors": ["<PERSON><PERSON>"], "summary": "A one-way function is a function that is easy to compute but hard to invert *on average*. We establish the first characterization of a one-way function by *worst-case* hardness assumptions, by introducing a natural meta-computational problem whose NP-hardness (and the worst-case hardness of NP) characterizes the existence of a one-way function. Specifically, we generalize the notion of time-bounded conditional Kolmogorov complexity to *distributional Kolmogorov complexity*, and prove that a one-way function exists if and only if it is NP-hard to approximate the distributional Kolmogorov complexity under randomized polynomial-time reductions and NP is hard in the worst case. We also propose the *Meta-Complexity Padding Conjecture*, which postulates that distributional Kolmogorov complexity is paddable by an approximation-preserving reduction. Under this conjecture, we prove that the worst-case hardness of an approximate version of the Minimum Circuit Size Problem characterizes the existence of a one-way function.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585130"}, {"primary_key": "1261412", "vector": [], "sparse_vector": [], "title": "A Duality between One-Way Functions and Average-Case Symmetry of Information.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Symmetry of Information (SoI) is a fundamental property of Kolmogorov complexity that relates the complexity of a pair of strings and their conditional complexities. Understanding if this property holds in the time-bounded setting is a longstanding open problem. In the nineties, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (1993) and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (1995) established that if SoI holds for time-bounded Kolmogorov complexity then cryptographic one-way functions do not exist, and asked if a converse holds.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585138"}, {"primary_key": "1261413", "vector": [], "sparse_vector": [], "title": "Hardness Self-Amplification: Simplified, Optimized, and Unified.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Strong (resp. weak) average-case hardness refers to the properties of a computational problem in which a large (resp. small) fraction of instances are hard to solve. We develop a general framework for proving hardness self-amplification, that is, the equivalence between strong and weak average-case hardness. Using this framework, we prove hardness self-amplification for popular problems, such as matrix multiplication, online matrix-vector multiplication, triangle counting of Erdős–<PERSON><PERSON> random graphs, and the planted clique problem. As a corollary, we obtain the first search-to-decision reduction for the planted clique problem in a high-error regime. Our framework simplifies, improves, and unifies the previous hardness self-amplification results.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585189"}, {"primary_key": "1261414", "vector": [], "sparse_vector": [], "title": "Robustness Implies Privacy in Statistical Estimation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the relationship between adversarial robustness and differential privacy in high-dimensional algorithmic statistics. We give the first black-box reduction from privacy to robustness which can produce private estimators with optimal tradeoffs among sample complexity, accuracy, and privacy for a wide range of fundamental high-dimensional parameter estimation problems, including mean and covariance estimation. We show that this reduction can be implemented in polynomial time in some important special cases. In particular, using nearly-optimal polynomial-time robust estimators for the mean and covariance of high-dimensional Gaussians which are based on the Sum-of-Squares method, we design the first polynomial-time private estimators for these problems with nearly-optimal samples-accuracy-privacy tradeoffs. Our algorithms are also robust to a nearly optimal fraction of adversarially-corrupted samples.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585115"}, {"primary_key": "1261415", "vector": [], "sparse_vector": [], "title": "NP-Hardness of Approximating Meta-Complexity: A Cryptographic Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Ren"], "summary": "It is a long-standing open problem whether the Minimum Circuit Size Problem (MCSP) and related meta-complexity problems are NP-complete. Even for the rare cases where the NP-hardness of meta-complexity problems are known, we only know very weak hardness of approximation.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585154"}, {"primary_key": "1261416", "vector": [], "sparse_vector": [], "title": "Tight Conditional Lower Bounds for Vertex Connectivity Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Thatchaphol <PERSON>", "<PERSON><PERSON>"], "summary": "We study the fine-grained complexity of graph connectivity problems in unweighted undirected graphs. Recent development shows that all variants of edge connectivity problems, including single-source-single-sink, global, Steiner, single-source, and all-pairs connectivity, are solvable in m1+o(1) time, collapsing the complexity of these problems into the almost-linear-time regime. While, historically, vertex connectivity has been much harder, the recent results showed that both single-source-single-sink and global vertex connectivity can be solved in m1+o(1) time, raising the hope of putting all variants of vertex connectivity problems into the almost-linear-time regime too.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585223"}, {"primary_key": "1261417", "vector": [], "sparse_vector": [], "title": "Upper and Lower Bounds on the Smoothed Complexity of the Simplex Method.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The simplex method for linear programming is known to be highly efficient in practice, and understanding its performance from a theoretical perspective is an active research topic. The framework of smoothed analysis, first introduced by <PERSON><PERSON><PERSON> and <PERSON> (JACM '04) for this purpose, defines the smoothed complexity of solving a linear program with d variables and n constraints as the expected running time when Gaussian noise of variance σ2 is added to the LP data. We prove that the smoothed complexity of the simplex method is O(σ−3/2 d13/4log7/4 n), improving the dependence on 1/σ compared to the previous bound of O(σ−2 d2√logn). We accomplish this through a new analysis of the shadow bound, key to earlier analyses as well. Illustrating the power of our new method, we use our method to prove a nearly tight upper bound on the smoothed complexity of two-dimensional polygons.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585124"}, {"primary_key": "1261418", "vector": [], "sparse_vector": [], "title": "Uniformly Random Colourings of Sparse Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We analyse uniformly random proper $k$-colourings of sparse graphs with maximum degree $Δ$ in the regime $Δ&lt; k\\ln k $. This regime corresponds to the lower side of the shattering threshold for random graph colouring, a paradigmatic example of the shattering threshold for random Constraint Satisfaction Problems. We prove a variety of results about the solution space geometry of colourings of fixed graphs, generalising work of <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> on random graphs, and justifying the performance of stochastic local search algorithms in this regime. Our central proof relies only on elementary techniques, namely the first-moment method and a quantitative induction, yet it strengthens list-colouring results due to <PERSON><PERSON>, and more recently <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, and generalises state-of-the-art bounds from Ramsey theory in the context of sparse graphs. It further yields an approximately tight lower bound on the number of colourings, also known as the partition function of the <PERSON>tts model, with implications for efficient approximate counting.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585242"}, {"primary_key": "1261419", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation, Range Avoidance, and Bounded Arithmetic.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The range avoidance problem (denoted by <PERSON><PERSON><PERSON>) asks to find a string outside of the range of a given circuit C:{0,1}n→{0,1}m, where m>n. Although at least half of the strings of length m are correct answers, it is not clear how to deterministically find one. Recent results of <PERSON><PERSON> (FOCS'21) and <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (FOCS' 22) show that efficient deterministic algorithms for Avoid would have far-reaching consequences, including strong circuit lower bounds and explicit constructions of combinatorial objects (e.g., Ramsey graphs, extractors, rigid matrices). This strongly motivates the question: does an efficient deterministic algorithm for Avoid actually exist?", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585187"}, {"primary_key": "1261420", "vector": [], "sparse_vector": [], "title": "Chaining, Group Leverage Score Overestimates, and Fast Spectral Hypergraph Sparsification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present an algorithm that given any n-vertex, m-edge, rank r hypergraph constructs a spectral sparsifier with O(n ε−2 logn logr) hyperedges in nearly-linear O(mr) time. This improves in both size and efficiency over a line of work [Bansal-<PERSON>-Trevisan 2019, Kapralov-Krauthgamer-Tardos-Yoshida 2021] for which the previous best size was O(min{n ε−4 log3 n,nr3 ε−2 logn}) and runtime was O(mr + nO(1)).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585136"}, {"primary_key": "1261421", "vector": [], "sparse_vector": [], "title": "Multidimensional Quantum Walks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "While the quantum query complexity of k-distinctness is known to be O(n3/4 − 1/4(2k−1)) for any constant k≥ 4 [<PERSON><PERSON><PERSON>, FOCS 2012], the best previous upper bound on the time complexity was O(n1−1/k). We give a new upper bound of O(n3/4 − 1/4(2k−1)) on the time complexity, matching the query complexity up to polylogarithmic factors. In order to achieve this upper bound, we give a new technique for designing quantum walk search algorithms, which is an extension of the electric network framework. We also show how to solve the welded trees problem in O(n) queries and O(n2) time using this new technique, showing that the new quantum walk framework can achieve exponential speedups.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585158"}, {"primary_key": "1261422", "vector": [], "sparse_vector": [], "title": "The Power of Unentangled Quantum Proofs with Non-negative Amplitudes.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Quantum entanglement is a fundamental property of quantum mechanics and it serves as a basic resource in quantum computation and information. Despite its importance, the power and limitations of quantum entanglement are far from being fully understood. Here, we study entanglement via the lens of computational complexity. This is done by studying quantum generalizations of the class NP with multiple unentangled quantum proofs, the so-called QMA(2) and its variants. The complexity of QMA(2) is known to be closely connected to a variety of problems such as deciding if a state is entangled and several classical optimization problems. However, determining the complexity of QMA(2) is a longstanding open problem, and only the trivial complexity bounds ⊆ (2) ⊆ are known.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585248"}, {"primary_key": "1261423", "vector": [], "sparse_vector": [], "title": "Finding a Small Vertex Cut on Distributed Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present an algorithm for distributed networks to efficiently find a small vertex cut in the CONGEST model. Given a positive integer κ, our algorithm can, with high probability, either find κ vertices whose removal disconnects the network or return that such κ vertices do not exist. Our algorithm takes κ3· Õ(D+√n) rounds, where n is the number of vertices in the network and D denotes the network's diameter. This implies Õ(D+√n) round complexity whenever κ=polylog(n).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585201"}, {"primary_key": "1261424", "vector": [], "sparse_vector": [], "title": "Removing Additive Structure in 3SUM-Based Reductions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> Xu"], "summary": "Our work explores the hardness of 3SUM instances without certain additive structures, and its applications. As our main technical result, we show that solving 3SUM on a size-n integer set that avoids solutions to a+b=c+d for {a, b} ≠ {c, d} still requires n2−o(1) time, under the 3SUM hypothesis. Such sets are called Sidon sets and are well-studied in the field of additive combinatorics.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585157"}, {"primary_key": "1261425", "vector": [], "sparse_vector": [], "title": "Sum-of-Squares Lower Bounds for Densest k-Subgraph.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Given a graph and an integer k, Densest k-Subgraph is the algorithmic task of finding the subgraph on k vertices with the maximum number of edges. This is a fundamental problem that has been subject to intense study for decades, with applications spanning a wide variety of fields. The state-of-the-art algorithm is an O(n1/4 + )-factor approximation (for any > 0) due to <PERSON><PERSON><PERSON> et al. [STOC '10]. Moreover, the so-called log-density framework predicts that this is optimal, i.e. it is impossible for an efficient algorithm to achieve an O(n1/4 − )-factor approximation. In the average case, Densest k-Subgraph is a prototypical noisy inference task which is conjectured to exhibit a statistical-computational gap.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585221"}, {"primary_key": "1261426", "vector": [], "sparse_vector": [], "title": "Quantum Advantage from Any Non-local Game.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We show a general method of compiling any k-prover non-local game into a single-prover (computationally sound) interactive game maintaining the same quantum completeness and classical soundness guarantees, up to a negligible additive factor in a security parameter. Our compiler uses any quantum homomorphic encryption scheme (<PERSON><PERSON><PERSON>, FOCS 2018; <PERSON><PERSON><PERSON><PERSON>, CRYPTO 2018) satisfying a natural form of correctness with respect to auxiliary quantum input. The homomorphic encryption scheme is used as a cryptographic mechanism to simulate the effect of spatial separation, and is required to evaluate k−1 prover strategies out of k on encrypted queries.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585164"}, {"primary_key": "1261427", "vector": [], "sparse_vector": [], "title": "Boosting Batch Arguments and RAM Delegation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We show how to generically improve the succinctness of non-interactive publicly verifiable batch argument (BARG) systems. In particular, we show (under a mild additional assumption) how to convert a BARG that generates proofs of length poly (m)· k1−є, where m is the length of a single instance and k is the number of instances being batched, into one that generates proofs of length poly (m, logk), which is the gold standard for succinctness of BARGs. By prior work, such BARGs imply the existence of SNARGs for deterministic time T computation with succinctness poly(logT).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585200"}, {"primary_key": "1261428", "vector": [], "sparse_vector": [], "title": "An Improved Parameterized Algorithm for Treewidth.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give an algorithm that takes as input an n-vertex graph G and an integer k, runs in time 2O(k2) nO(1), and outputs a tree decomposition of G of width at most k, if such a decomposition exists. This resolves the long-standing open problem of whether there is a 2o(k3) nO(1) time algorithm for treewidth. In particular, our algorithm is the first improvement on the dependency on k in algorithms for treewidth since the 2O(k3) nO(1) time algorithm given by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [ICALP 1991] and <PERSON><PERSON> and <PERSON><PERSON><PERSON> [ICALP 1991].", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585245"}, {"primary_key": "1261429", "vector": [], "sparse_vector": [], "title": "Quantum Cryptography in Algorithmica.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct a classical oracle relative to which P = NP yet single-copy secure pseudorandom quantum states exist. In the language of Impaglia<PERSON>'s five worlds, this is a construction of pseudorandom states in \"Algorithmica,\" and hence shows that in a black-box setting, quantum cryptography based on pseudorandom states is possible even if one-way functions do not exist. As a consequence, we demonstrate that there exists a property of a cryptographic hash function that simultaneously (1) suffices to construct pseudorandom states, (2) holds for a random oracle, and (3) is independent of P vs. NP in the black-box setting. We also introduce a conjecture that would generalize our results to multi-copy secure pseudorandom states.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585225"}, {"primary_key": "1261430", "vector": [], "sparse_vector": [], "title": "Online Unrelated-Machine Load Balancing and Generalized Flow with Recourse.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the recourse version of the classical online load balancing problem on unrelated machines, where the algorithm is allowed to re-assign prior jobs. We give a (2+є)-competitive algorithm for the problem with Oє(logn) amortized recourse per job. This is the first O(1)-competitive algorithm for the problem with non-trivial recourse, and the competitive ratio nearly matches the long-standing best-known offline approximation guarantee. We also show an O(loglogn/logloglogn)-competitive algorithm for the problem with O(1) amortized recourse. The best-known bounds from prior work are O(loglogn)-competitive algorithms with O(1) amortized recourse due to <PERSON> et al., for the special case of the restricted assignment model.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585222"}, {"primary_key": "1261431", "vector": [], "sparse_vector": [], "title": "Cheeger Inequalities for Directed Graphs and Hypergraphs using Reweighted Eigenvalues.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We derive Cheeger inequalities for directed graphs and hypergraphs using the reweighted eigenvalue approach that was recently developed for vertex expansion in undirected graphs. The goal is to develop a new spectral theory for directed graphs and an alternative spectral theory for hypergraphs.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585139"}, {"primary_key": "1261432", "vector": [], "sparse_vector": [], "title": "A Unified Framework for Light Spanners.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Seminal works on light spanners over the years provide spanners with optimal lightness in various graph classes, such as in general graphs, Euclidean spanners, and minor-free graphs. Three shortcomings of previous works on light spanners are: (i) The runtimes of these constructions are almost always sub-optimal, and usually far from optimal. (ii) These constructions are optimal in the standard and crude sense, but not in a refined sense that takes into account a wider range of involved parameters. (iii) The techniques are ad hoc per graph class, and thus can't be applied broadly.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585185"}, {"primary_key": "1261433", "vector": [], "sparse_vector": [], "title": "Spectral Hypergraph Sparsification via Chaining.", "authors": ["<PERSON>"], "summary": "In a hypergraph on n vertices where D is the maximum size of a hyperedge, there is a weighted hypergraph spectral -sparsifier with at most O(−2 log(D) · n logn) hyperedges. This improves over the bound of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2021) who achieve O(−4 n (logn)3), as well as the bound O(−2 D3 n logn) obtained by <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> (2019). The same sparsification result was obtained independently by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (2022).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585165"}, {"primary_key": "1261434", "vector": [], "sparse_vector": [], "title": "Unprovability of Strong Complexity Lower Bounds in Bounded Arithmetic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "While there has been progress in establishing the unprovability of complexity statements in lower fragments of bounded arithmetic, understanding the limits of <PERSON><PERSON><PERSON>'s theory APC1 (2007) and of higher levels of <PERSON><PERSON>'s hierarchy S2i (1986) has been a more elusive task. Even in the more restricted setting of <PERSON>'s theory PV (1975), known results often rely on a less natural formalization that encodes a complexity statement using a collection of sentences instead of a single sentence. This is done to reduce the quantifier complexity of the resulting sentences so that standard witnessing results can be invoked.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585144"}, {"primary_key": "1261435", "vector": [], "sparse_vector": [], "title": "Hausdorff and Gromov-Hausdorff Stable Subsets of the Medial Axis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper we introduce a pruning of the medial axis called the (λ,α)-medial axis (axλα). We prove that the (λ,α)-medial axis of a set K is stable in a Gromov-Hausdorff sense under weak assumptions. More formally we prove that if K and K′ are close in the Hausdorff (dH) sense then the (λ,α)-medial axes of K and K′ are close as metric spaces, that is the Gromov-Hausdorff distance (dGH) between the two is 1/4-<PERSON><PERSON>lder in the sense that dGH (axλα(K),axλα(K′)) ≲ dH(K,K′)1/4. The Hausdorff distance between the two medial axes is also bounded, by dH (axλα(K),λα(K′)) ≲ dH(K,K′)1/2. These quantified stability results provide guarantees for practical computations of medial axes from approximations. Moreover, they provide key ingredients for studying the computability of the medial axis in the context of computable analysis.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585113"}, {"primary_key": "1261436", "vector": [], "sparse_vector": [], "title": "Doubly Efficient Private Information Retrieval and Fully Homomorphic RAM Computation from Ring LWE.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A (single server) private information retrieval (PIR) allows a client to read data from a public database held on a remote server, without revealing to the server which locations she is reading. In a doubly efficient PIR (DEPIR), the database is first preprocessed, but the server can subsequently answer any client's query in time that is sub-linear in the database size. Prior work gave a plausible candidate for a public-key variant of DEPIR, where a trusted party is needed to securely preprocess the database and generate a corresponding public key for the clients; security relied on a new non-standard code-based assumption and a heuristic use of ideal obfuscation. In this work we construct the stronger unkeyed notion of DEPIR, where the preprocessing is a deterministic procedure that the server can execute on its own. Moreover, we prove security under just the standard ring learning-with-errors (RingLWE) assumption. For a database of size N and any constant ε>0, the preprocessing run-time and size is O(N1+ε), while the run-time and communication-complexity of each PIR query is polylog(N). We also show how to update the preprocessed database in time O(Nε). Our approach is to first construct a standard PIR where the server's computation consists of evaluating a multivariate polynomial; we then convert it to a DEPIR by preprocessing the polynomial to allow for fast evaluation, using the techniques of <PERSON><PERSON><PERSON> and Um<PERSON> (STOC '08).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585175"}, {"primary_key": "1261437", "vector": [], "sparse_vector": [], "title": "Optimistic MLE: A Generic Model-Based Algorithm for Partially Observable Sequential Decision Making.", "authors": ["Qing<PERSON>", "<PERSON>raneeth Netrapalli", "Csaba <PERSON>", "<PERSON>"], "summary": "This paper introduces a simple efficient learning algorithms for general sequential decision making. The algorithm combines Optimism for exploration with Maximum Likelihood Estimation for model estimation, which is thus named OMLE. We prove that OMLE learns the near-optimal policies of an enormously rich class of sequential decision making problems in a polynomial number of samples. This rich class includes not only a majority of known tractable model-based Reinforcement Learning (RL) problems (such as tabular MDPs, factored MDPs, low witness rank problems, tabular weakly-revealing/observable POMDPs and multi-step decodable POMDPs ), but also many new challenging RL problems especially in the partially observable setting that were not previously known to be tractable.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585161"}, {"primary_key": "1261438", "vector": [], "sparse_vector": [], "title": "Improved Approximation Ratios of Fixed-Price Mechanisms in Bilateral Trades.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We continue the study of the performance for fixed-price mechanisms in the bilateral trade problem, and improve approximation ratios of welfare-optimal mechanisms in several settings. Specifically, in the case where only the buyer distribution is known, we prove that there exists a distribution over different fixed-price mechanisms, such that the approximation ratio lies within the interval of [0.71, 0.7381]. Furthermore, we show that the same approximation ratio holds for the optimal fixed-price mechanism, when both buyer and seller distributions are known. As a result, the previously best-known (1 − 1/e+0.0001)-approximation can be improved to 0.71. Additionally, we examine randomized fixed-price mechanisms when we receive just one single sample from the seller distribution, for both symmetric and asymmetric settings. Our findings reveal that posting the single sample as the price remains optimal among all randomized fixed-price mechanisms.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585160"}, {"primary_key": "1261439", "vector": [], "sparse_vector": [], "title": "Memory-Sample Lower Bounds for Learning with Classical-Quantum Hybrid Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In a work by <PERSON><PERSON> (<PERSON><PERSON> and FOCS 16), it was proved that any algorithm for parity learning on n bits requires either Ω(n2) bits of classical memory or an exponential number (in ‍n) of random samples. A line of recent works continued that research direction and showed that for a large collection of classical learning tasks, either super-linear classical memory size or super-polynomially many samples are needed. All these works consider learning algorithms as classical branching programs, which perform classical computation within bounded memory. However, these results do not capture all physical computational models, remarkably, quantum computers and the use of quantum memory. It leaves the possibility that a small piece of quantum memory could significantly reduce the need for classical memory or samples and thus completely change the nature of the classical learning task. Despite the recent research on the necessity of quantum memory for intrinsic quantum learning problems like shadow tomography and purity testing, the role of quantum memory in classical learning tasks remains obscure. In this work, we study classical learning tasks in the presence of quantum memory. We prove that any quantum algorithm with both, classical memory and quantum memory, for parity learning on n bits, requires either Ω(n2) bits of classical memory or Ω(n) bits of quantum memory or an exponential number of samples. In other words, the memory-sample lower bound for parity learning remains qualitatively the same, even if the learning algorithm can use, in addition to the classical memory, a quantum memory of size c n (for some constant c>0). Our result is more general and applies to many other classical learning tasks. Following previous works, we represent by the matrix M: A × X → {−1,1} the following learning task. An unknown x is sampled uniformly at random from a concept class X, and a learning algorithm tries to uncover x by seeing streaming of random samples (ai, bi = M(ai, x)) where for every i, ai∈ A is chosen uniformly at random. Assume that k,ℓ,r are integers such that any submatrix of M of at least 2−k·|A| rows and at least 2−ℓ·|X| columns, has a bias of at most 2−r. We prove that any algorithm with classical and quantum hybrid memory for the learning problem corresponding to M needs either (1) Ω(k · ℓ) bits of classical memory, or (2) Ω(r) qubits of quantum memory, or (3) 2Ω(r) random samples, to achieve a success probability at least 2−O(r). Our results refute the possibility that a small amount of quantum memory significantly reduces the size of classical memory needed for efficient learning on these problems. Our results also imply improved security of several existing cryptographical protocols in the bounded-storage model (protocols that are based on parity learning on n bits), proving that security holds even in the presence of a quantum adversary with at most c n2 bits of classical memory and c n bits of quantum memory (for some constant c>0).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585129"}, {"primary_key": "1261440", "vector": [], "sparse_vector": [], "title": "Random Graph Matching at Otter&apos;s <PERSON><PERSON><PERSON><PERSON> via Counting Chan<PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose an efficient algorithm for graph matching based on similarity scores constructed from counting a certain family of weighted trees rooted at each vertex. For two Erdős–Rényi graphs G(n,q) whose edges are correlated through a latent vertex correspondence, we show that this algorithm correctly matches all but a vanishing fraction of the vertices with high probability, provided that nq→∞ and the edge correlation coefficient ρ satisfies ρ2>α ≈ 0.338, where α is <PERSON><PERSON>'s tree-counting constant. Moreover, this almost exact matching can be made exact under an extra condition that is information-theoretically necessary. This is the first polynomial-time graph matching algorithm that succeeds at an explicit constant correlation and applies to both sparse and dense graphs. In comparison, previous methods either require ρ=1−o(1) or are restricted to sparse graphs.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585156"}, {"primary_key": "1261441", "vector": [], "sparse_vector": [], "title": "Kneser Graphs Are Hamiltonian.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For integers ‍k≥ 1 and n≥ 2k+1, the Kneser graph ‍K(n,k) has as vertices all k-element subsets of an n-element ground set, and an edge between any two disjoint sets. It has been conjectured since the 1970s that all Kneser graphs admit a Hamilton cycle, with one notable exception, namely the Petersen graph ‍K(5,2). This problem received considerable attention in the literature, including a recent solution for the sparsest case n=2k+1. The main contribution of this paper is to prove the conjecture in full generality. We also extend this Hamiltonicity result to all connected generalized Johnson graphs (except the Petersen graph). The generalized Johnson graph ‍J(n,k,s) has as vertices all k-element subsets of an n-element ground set, and an edge between any two sets whose intersection has size exactly ‍s. Clearly, we have K(n,k)=J(n,k,0), i.e., generalized Johnson graph include Kneser graphs as a special case. Our results imply that all known families of vertex-transitive graphs defined by intersecting set systems have a Hamilton cycle, which settles an interesting special case of <PERSON><PERSON><PERSON><PERSON>' conjecture on Hamilton cycles in vertex-transitive graphs from ‍1970. Our main technical innovation is to study cycles in Kneser graphs by a kinetic system of multiple gliders that move at different speeds and that interact over time, reminiscent of the gliders in <PERSON>'s Game of Life, and to analyze this system combinatorially and via linear algebra.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585137"}, {"primary_key": "1261442", "vector": [], "sparse_vector": [], "title": "Exact Phase Transitions for Stochastic Block Models and Reconstruction on Trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Young<PERSON><PERSON>"], "summary": "In this paper, we rigorously establish the predictions in ground breaking work in statistical physics by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> (2011) regarding the block model, in particular in the case of q=3 and q=4 communities.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585155"}, {"primary_key": "1261443", "vector": [], "sparse_vector": [], "title": "Sampling from Convex Sets with a Cold Start using Multiscale Decompositions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Running a random walk in a convex body K⊆ℝn is a standard approach to sample approximately uniformly from the body. The requirement is that from a suitable initial distribution, the distribution of the walk comes close to the uniform distribution πK on K after a number of steps polynomial in n and the aspect ratio R/r (i.e., when rB2 ⊆ K ⊆ RB2). Proofs of rapid mixing of such walks often require the probability density η0 of the initial distribution with respect to πK to be at most poly(n): this is called a \"warm start\". Achieving a warm start often requires non-trivial pre-processing before starting the random walk. This motivates proving rapid mixing from a \"cold start\", wherein η0 can be as high as exp(poly(n)). Unlike warm starts, a cold start is usually trivial to achieve. However, a random walk need not mix rapidly from a cold start: an example being the well-known \"ball walk\". On the other hand, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> proved that the \"hit-and-run\" random walk mixes rapidly from a cold start. For the related coordinate hit-and-run (CHR) walk, which has been found to be promising in computational experiments, rapid mixing from a warm start was proved only recently but the question of rapid mixing from a cold start remained open.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585172"}, {"primary_key": "1261444", "vector": [], "sparse_vector": [], "title": "Quantum Free Games.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The complexity of free games with two or more classical players was essentially settled by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (CCC'14). In the quantum world, there are two complexity classes that can be considered quantum analogues of classical free games: (1) AM*, the multiprover interactive proof class corresponding to free games with entangled players, and, somewhat less obviously, (2) BellQMA(2), the class of quantum Merlin-Arthur proof systems with two unentangled Merlins, whose proof states are separately measured by <PERSON>. In this work, we make significant progress towards a tight characterization of both of these classes. (1) We show a BellQMA(2) protocol for 3SAT on n variables, where the total amount of communication is Õ(√n). This answers an open question of <PERSON> and <PERSON> (2010) and also shows, conditional on ETH, that the algorithm of <PERSON>, <PERSON> and <PERSON> (STOC'11) for optimizing over separable states is tight up to logarithmic factors. (2) We show that AM* with nprovers = 2, question length O(1), and answer-length log(n) is equal to RE, i.e. that free entangled games with constant-sized questions are as powerful as general entangled games. (In contrast, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> show that classical free games are much weaker than general classical games.) We show this using a question \"hyper-compression\" theorem that iteratively applies the introspection technique of <PERSON> et al. (2020). Our result is a significant improvement over the headline result of <PERSON> et al., whose MIP* protocol for the halting problem has (n)-sized questions and answers. (3) By the same techniques, we obtain a zero-gap AM* protocol for a Π2 complete language with constant-size questions and almost logarithmically (O(logn · log* n)) large answers, improving on the headline result of Mousavi, Nezhadi and Yuen (STOC'22). (4) Using a connection to the nonuniform complexity of the halting problem we show that any MIP* protocol for RE requires Ω(logn) bits of communication. It follows that our results in item 3 are optimal up to an O(log* n) factor, and that the gapless compression theorems of Mousavi, Nezhadi and Yuen are asymptotically optimal. We conjecture that these bounds can be saturated in the gapped case as well.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585208"}, {"primary_key": "1261445", "vector": [], "sparse_vector": [], "title": "A High Dimensional Goldreich-Levin <PERSON>.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work we prove a high dimensional analogue of the beloved <PERSON><PERSON><PERSON><PERSON><PERSON> Theo<PERSON> (STOC 1989). We consider the following algorithmic problem: given oracle access to a function f:ℤqm→ℤqn such that Prx∼ℤqm[f(x)=Ax]≥ε for some A∈ℤqn× m and ε>0, recover A (or a list of all such matrices). We focus on the case ε≤ 1/q since when ε ≥ 1/q+δ, the problem is solved by the original <PERSON><PERSON><PERSON><PERSON> Theorem. As stated, this problem cannot be efficiently solved, since when ε ≤ 1/q the list of A with good agreement with f might be exponentially large. Our main theorem gives an algorithm which efficiently recovers a list of affine maps of size (1/ε ) which have good agreement with f, and such that every linear map which has good agreement with f, also has good agreement with some affine map in our list. Our proof makes novel use of Fourier analysis. Our main theorem has applications to effective property testing.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585224"}, {"primary_key": "1261446", "vector": [], "sparse_vector": [], "title": "Shellability Is Hard Even for Balls.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The main goal of this paper is to show that shellability is NP-hard for triangulated d-balls (this also gives hardness for triangulated d-manifolds/d-pseudomanifolds with boundary) as soon as d ≥ 3. This extends our earlier work with <PERSON><PERSON>, <PERSON> and <PERSON> on hardness of shellability of 2-complexes and answers some questions implicitly raised by <PERSON> and <PERSON> in 1978 and explicitly mentioned by <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Together with the main goal, we also prove that collapsibility is NP-hard for 3-complexes embeddable in 3-space, extending an earlier work of the second author and answering an open question mentioned by <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>; and that shellability is NP-hard for 2-complexes embeddable in 3-space, answering another question of <PERSON><PERSON><PERSON> and <PERSON>roo<PERSON> (in a slightly stronger form than what is given by the main result).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585152"}, {"primary_key": "1261447", "vector": [], "sparse_vector": [], "title": "Near-Optimal Derandomization of Medium-Width Branching Programs.", "authors": ["<PERSON> (<PERSON>) Putterman", "<PERSON>"], "summary": "We give a deterministic white-box algorithm to estimate the expectation of a read-once branching program of length n and width w in space Õ(logn+√logn·logw). In particular, we obtain an almost optimal space Õ(logn) derandomization of programs up to width w=2√logn. Previously, the best known space complexity for this problem was O(min{logn· logw,log3/2n+√logn· logw}) via the classic algorithms of <PERSON><PERSON><PERSON> (JCSS 1970) and <PERSON><PERSON> and <PERSON> (JCSS 1999), which only achieve space Õ(logn) for w=polylog(n). We prove this result by showing that a variant of the Saks-Zhou algorithm developed by <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (ECCC 2022) still works without executing one of the steps in the algorithm, the so-called random shift step. This allows us to extend their algorithm from computing the nth power of a w× w stochastic matrix to multiplying n distinct w× w stochastic matrices with no degradation in space consumption. In the regime where w≥ n, we also show that our approach can achieve parameters matching those of the original Saks-Zhou algorithm (with no loglog factors). Finally, we show that for w≤ 2√logn, an algorithm even simpler than our algorithm and that of <PERSON><PERSON> and <PERSON> achieves space O(log3/2 n).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585108"}, {"primary_key": "1261448", "vector": [], "sparse_vector": [], "title": "New Algorithms for All Pairs Approximate Shortest Paths.", "authors": ["<PERSON>"], "summary": "Let G=(V,E) be an unweighted undirected graph with n vertices and m edges. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [FOCS 1996, SICOMP 2000] presented an (min{n3/2m1/2,n7/3 })-time algorithm that computes estimated distances with an additive approximation of 2 without using Fast Matrix Multiplication (FMM). Recently, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON> [ICALP 2022] improved the running time for dense graphs to (n2.29)-time, using FMM, where an exact solution can be computed with FMM in (nω) time (ω < 2.37286) using <PERSON><PERSON><PERSON>'s algorithm.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585197"}, {"primary_key": "1261449", "vector": [], "sparse_vector": [], "title": "<PERSON>llel Breadth-First Search and Exact Shortest Paths and Stronger Notions for Approximate Distances.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces stronger notions for approximate single-source shortest-path distances and gives simple reductions to compute them from weaker standard notions of approximate distances. Strongly-approximate distances isolate, capture, and address the well-known barriers for using approximate distances algorithmically and their reductions directly address these barriers in a clean and modular manner. The reductions are model-independent and require only logO(1) n black-box approximate distance computations. They apply equally to parallel, distributed, and semi-streaming settings. Strongly (1+ε)-approximate distances are equivalent to exact distances in a (1+ε)-perturbed graph and approximately satisfy the subtractive triangle inequality. In directed graphs, this is sufficient to reduce even exact distance computation to arbitrary (1+ε)-approximate ones.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585235"}, {"primary_key": "1261450", "vector": [], "sparse_vector": [], "title": "Testing Distributional Assumptions of Learning Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There are many important high dimensional function classes that have fast agnostic learning algorithms when strong assumptions on the distribution of examples can be made, such as Gaussianity or uniformity over the domain. But how can one be sufficiently confident that the data indeed satisfies the distributional assumption, so that one can trust in the output quality of the agnostic learning algorithm? We propose a model by which to systematically study the design of tester-learner pairs (A,T), such that if the distribution on examples in the data passes the tester T then one can safely trust the output of the agnostic learner A on the data.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585117"}, {"primary_key": "1261451", "vector": [], "sparse_vector": [], "title": "Dynamic ((1+ε) ln n)-Approximation Algorithms for Minimum Set Cover and Dominating Set.", "authors": ["<PERSON>", "<PERSON><PERSON>i <PERSON>"], "summary": "The minimum set cover (MSC) problem admits two classic algorithms: a greedy lnn-approximation and a primal-dual f-approximation, where n is the universe size and f is the maximum frequency of an element. Both algorithms are simple and efficient, and remarkably — one cannot improve these approximations under hardness results by more than a factor of (1+є), for any constant є > 0.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585211"}, {"primary_key": "1261452", "vector": [], "sparse_vector": [], "title": "Faster Isomorphism for 𝑝-Groups of Class 2 and Exponent 𝑝.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The group isomorphism problem determines whether two groups, given by their <PERSON><PERSON><PERSON> tables, are isomorphic. For groups with order n, an algorithm with n(logn + O(1)) running time, attributed to <PERSON><PERSON><PERSON>, was proposed in the 1970s (<PERSON>, <PERSON> 1978). Despite the extensive study over the past decades, the current best group isomorphism algorithm has an n(1 / 4 + o(1))logn running time (<PERSON> 2013).", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585250"}, {"primary_key": "1261453", "vector": [], "sparse_vector": [], "title": "Optimal Eigenvalue Approximation via Sketching.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Given a symmetric matrix A, we show from the simple sketch GAGT, where G is a Gaussian matrix with k = O(1/є2) rows, that there is a procedure for approximating all eigenvalues of A simultaneously to within є ||A||F additive error with large probability. Unlike the work of (<PERSON><PERSON>, <PERSON>, <PERSON>OD<PERSON>, 2013), we do not require that A is positive semidefinite and therefore we can recover sign information about the spectrum as well. Our result also significantly improves upon the sketching dimension of recent work for this problem (<PERSON><PERSON>, <PERSON>, Woodruff FOCS 2022), and in fact gives optimal sketching dimension. Our proof develops new properties of singular values of GA for a k × n Gaussian matrix G and an n × n matrix A which may be of independent interest. Additionally we achieve tight bounds in terms of matrix-vector queries. Our sketch can be computed using O(1/є2) matrix-vector multiplies, and by improving on lower bounds for the so-called rank estimation problem, we show that this number is optimal even for adaptive matrix-vector queries.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585102"}, {"primary_key": "1261454", "vector": [], "sparse_vector": [], "title": "Almost-Optimal Sublinear Additive Spanners.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given an undirected unweighted graph G = (V, E) on n vertices and m edges, a subgraph H⊆ G is a spanner of G with stretch function f: ℝ+ → ℝ+, iff for every pair s, t of vertices in V, distH(s, t)≤ f(distG(s, t)). When f(d) = d + o(d), H is called a sublinear additive spanner; when f(d) = d + o(n), H is called an additive spanner, and f(d) − d is usually called the additive stretch of H.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585125"}, {"primary_key": "1261455", "vector": [], "sparse_vector": [], "title": "Improved and Deterministic Online Service with Deadlines or Delay.", "authors": ["<PERSON><PERSON>"], "summary": "We consider the problem of online service with delay on a general metric space, first presented by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (STOC 2017). The best known randomized algorithm for this problem, by <PERSON><PERSON> and <PERSON><PERSON> (FOCS 2019), is O(log2 n)-competitive, where n is the number of points in the metric space. This is also the best known result for the special case of online service with deadlines, which is of independent interest.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585107"}, {"primary_key": "1261456", "vector": [], "sparse_vector": [], "title": "A (1.5+ε)-Approximation Algorithm for Weighted Connectivity Augmentation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Connectivity augmentation problems are among the most elementary questions in Network Design. Many of these problems admit natural 2-approximation algorithms, often through various classic techniques, whereas it remains open whether approximation factors below 2 can be achieved. One of the most basic examples thereof is the Weighted Connectivity Augmentation Problem (WCAP). In WCAP, one is given an undirected graph together with a set of additional weighted candidate edges, and the task is to find a cheapest set of candidate edges whose addition to the graph increases its edge-connectivity. We present a (1.5+ε)-approximation algorithm for WCAP, showing for the first time that factors below 2 are achievable.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585122"}, {"primary_key": "1261457", "vector": [], "sparse_vector": [], "title": "Concurrent Composition Theorems for Differential Privacy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the concurrent composition properties of interactive differentially private mechanisms, whereby an adversary can arbitrarily interleave its queries to the different mechanisms. We prove that all composition theorems for non-interactive differentially private mechanisms extend to the concurrent composition of interactive differentially private mechanisms, whenever differential privacy is measured using the hypothesis testing framework of $f$-DP, which captures standard $(\\eps,\\delta)$-DP as a special case. We prove the concurrent composition theorem by showing that every interactive $f$-DP mechanism can be simulated by interactive post-processing of a non-interactive $f$-DP mechanism. In concurrent and independent work, <PERSON>yu~\\cite{lyu2022composition} proves a similar result to ours for $(\\eps,\\delta)$-DP, as well as a concurrent composition theorem for R\\'enyi DP. We also provide a simple proof of <PERSON><PERSON>'s concurrent composition theorem for R\\'enyi DP. <PERSON><PERSON> leaves the general case of $f$-DP as an open problem, which we solve in this paper.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585241"}, {"primary_key": "1261458", "vector": [], "sparse_vector": [], "title": "Interior Point Methods with a Gradient Oracle.", "authors": ["<PERSON>"], "summary": "We provide an interior point method based on quasi-Newton iterations, which only requires first-order access to a strongly self-concordant barrier function. To achieve this, we extend the techniques of <PERSON><PERSON><PERSON><PERSON><PERSON> [STOC '07] to maintain a preconditioner, while using only first-order information. We measure the quality of this preconditioner in terms of its relative excentricity to the unknown Hessian matrix, and we generalize these techniques to convex functions with a slowly-changing Hessian. We combine this with an interior point method to show that, given first-order access to an appropriate barrier function for a convex set K, we can solve well-conditioned linear optimization problems over K to ε precision in time O((T+n2)√nνlog(1/ε)), where ν is the self-concordance parameter of the barrier function, and T is the time required to make a gradient query.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585142"}, {"primary_key": "1261459", "vector": [], "sparse_vector": [], "title": "Average-Case Complexity of Tensor Decomposition for Low-Degree Polynomials.", "authors": ["<PERSON>"], "summary": "Suppose we are given an $n$-dimensional order-3 symmetric tensor $T \\in (\\mathbb{R}^n)^{\\otimes 3}$ that is the sum of $r$ random rank-1 terms. The problem of recovering the rank-1 components is possible in principle when $r \\lesssim n^2$ but polynomial-time algorithms are only known in the regime $r \\ll n^{3/2}$. Similar \"statistical-computational gaps\" occur in many high-dimensional inference tasks, and in recent years there has been a flurry of work on explaining the apparent computational hardness in these problems by proving lower bounds against restricted (yet powerful) models of computation such as statistical queries (SQ), sum-of-squares (SoS), and low-degree polynomials (LDP). However, no such prior work exists for tensor decomposition, largely because its hardness does not appear to be explained by a \"planted versus null\" testing problem. We consider a model for random order-3 tensor decomposition where one component is slightly larger in norm than the rest (to break symmetry), and the components are drawn uniformly from the hypercube. We resolve the computational complexity in the LDP model: $O(\\log n)$-degree polynomial functions of the tensor entries can accurately estimate the largest component when $r \\ll n^{3/2}$ but fail to do so when $r \\gg n^{3/2}$. This provides rigorous evidence suggesting that the best known algorithms for tensor decomposition cannot be improved, at least by known approaches. A natural extension of the result holds for tensors of any fixed order $k \\ge 3$, in which case the LDP threshold is $r \\sim n^{k/2}$.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585232"}, {"primary_key": "1261460", "vector": [], "sparse_vector": [], "title": "New Subset Selection Algorithms for Low Rank Approximation: Offline and Online.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Subset selection for the rank k approximation of an n× d matrix A offers improvements in the interpretability of matrices, as well as a variety of computational savings. This problem is well-understood when the error measure is the Frobenius norm, with various tight algorithms known even in challenging models such as the online model, where an algorithm must select the column subset irrevocably when the columns arrive one by one. In sharp contrast, when the error measure is replaced by other matrix losses, optimal trade-offs between the subset size and approximation quality have not been settled, even in the standard offline setting. We give a number of results towards closing these gaps.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585100"}, {"primary_key": "1261461", "vector": [], "sparse_vector": [], "title": "Algorithmic Applications of Hypergraph and Partition Containers.", "authors": ["<PERSON>"], "summary": "We present a general method to convert algorithms into faster algorithms for almost-regular input instances. Informally, an almost-regular input is an input in which the maximum degree is larger than the average degree by at most a constant factor. This family of inputs vastly generalizes several families of inputs for which we commonly have improved algorithms, including bounded-degree inputs and random inputs. It also generalizes families of inputs for which we don't usually have faster algorithms, including regular-inputs of arbitrarily high degree and very dense inputs. We apply our method to achieve breakthroughs in exact algorithms for several central NP-Complete problems including k-SAT, Graph Coloring, and Maximum Independent Set.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246.3585163"}, {"primary_key": "1278627", "vector": [], "sparse_vector": [], "title": "Proceedings of the 55th Annual ACM Symposium on Theory of Computing, STOC 2023, Orlando, FL, USA, June 20-23, 2023", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The papers in this volume were presented at the 55th Annual ACM Symposium on Theory of Computing (STOC 2023), sponsored by the ACM Special Interest Group on Algorithms and Computation Theory (SIGACT). The conference was held in Orlando, Florida, as part of the ACM Federated Computing Research Conference (FCRC). The papers were presented as live talks during sessions held between June 20-23, 2023. STOC 2023 was part of Theory Fest, which included a range of panels, meetings, and social activities. FCRC 2023 featured plenary talks given by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.", "published": "2023-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3564246"}]