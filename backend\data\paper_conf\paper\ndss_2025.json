[{"primary_key": "189955", "vector": [], "sparse_vector": [], "title": "A Method to Facilitate Membership Inference Attacks in Deep Learning Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern machine learning (ML) ecosystems offer a surging number of ML frameworks and code repositories that can greatly facilitate the development of ML models. Today, even ordinary data holders who are not ML experts can apply off-the-shelf codebase to build high-performance ML models on their data, many of which are sensitive in nature (e.g., clinical records). In this work, we consider a malicious ML provider who supplies model-training code to the data holders, does not have access to the training process, and has only black-box query access to the resulting model. In this setting, we demonstrate a new form of membership inference attack that is strictly more powerful than prior art. Our attack empowers the adversary to reliably de-identify all the training samples (average >99% attack[email protected]% FPR), and the compromised models still maintain competitive performance as their uncorrupted counterparts (average <1% accuracy drop). Moreover, we show that the poisoned models can effectively disguise the amplified membership leakage under common membership privacy auditing, which can only be revealed by a set of secret samples known by the adversary.\n\nOverall, our study not only points to the worst-case membership privacy leakage, but also unveils a common pitfall underlying existing privacy auditing methods, which calls for future efforts to rethink the current practice of auditing membership privacy in machine learning models.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189956", "vector": [], "sparse_vector": [], "title": "Try to Poison My Deep Learning Data? Nowhere to Hide Your Trajectory Spectrum!", "authors": ["Yansong Gao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the Data as a Service (DaaS) model, data curators, such as commercial providers like Amazon Mechanical Turk, Appen, and TELUS International, aggregate quality data from numerous contributors and monetize it for deep learning (DL) model providers. However, malicious contributors can poison this data, embedding backdoors in the trained DL models. Existing methods for detecting poisoned samples face significant limitations: they often rely on reserved clean data; they are sensitive to the poisoning rate, trigger type, and backdoor type; and they are specific to classification tasks. These limitations hinder their practical adoption by data curators. This work, for the first time, investigates the textit{training trajectory} of poisoned samples in the textit{spectrum domain}, revealing distinctions from benign samples that are not apparent in the original non-spectrum domain. Building on this novel perspective, we propose TellTale to detect and sanitize poisoned samples as a one-time effort, addressing textit{all} of the aforementioned limitations of prior work. Through extensive experiments, TellTale demonstrates the ability to defeat both universal and challenging partial backdoor types without relying on any reserved clean data. TellTale is also validated to be agnostic to various trigger types, including the advanced clean-label trigger attack, Narcissus (CCS'2023). Moreover, TellTale proves effective across diverse data modalities (e.g., image, audio and text) and non-classification tasks (e.g., regression)---making it the only known training phase poisoned sample detection method applicable to non-classification tasks. In all our evaluations, TellTale achieves a detection accuracy (i.e., accurately identifying poisoned samples) of at least 95.52% and a false positive rate (i.e., falsely recognizing benign samples as poisoned ones) no higher than 0.61%. Comparisons with state-of-the-art methods, ASSET (Usenix'2023) and CT (Usenix'2023), further affirm TellTale's superior performance. More specifically, ASSET fails to handle partial backdoor types and incurs an unbearable false positive rate with clean/benign datasets common in practice, while CT fails against the Narcissus trigger.  In contrast, TellTale proves highly effective across testing scenarios where prior work fails. The source code is released at https://github.com/MPaloze/Telltale.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189957", "vector": [], "sparse_vector": [], "title": "The Road to Trust: Building Enclaves within Confidential VMs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Integrity is critical for maintaining system security, as it ensures that only genuine software is loaded onto a machine. Although confidential virtual machines (CVMs) function within isolated environments separate from the host, it is important to recognize that users still encounter challenges in maintaining control over the integrity of the code running within the trusted execution environments (TEEs). The presence of a sophisticated operating system (OS) raises the possibility of dynamically creating and executing any code, making user applications within TEEs vulnerable to interference or tampering if the guest OS is compromised. To address this issue, this paper introduces NestedSGX, a framework which leverages virtual machine privilege level (VMPL), a recent hardware feature available on AMD SEV-SNP to enable the creation of hardware enclaves within the guest VM. Similar to Intel SGX, NestedSGX considers the guest OS untrusted for loading potentially malicious code. It ensures that only trusted and measured code executed within the enclave can be remotely attested. To seamlessly protect existing applications, NestedSGX aims for compatibility with Intel SGX by simulating SGX leaf functions. We have also ported the SGX SDK and the Occlum library OS to NestedSGX, enabling the use of existing SGX toolchains and applications in the system. Performance evaluations show that context switches in NestedSGX take about 32,000 -- 34,000 cycles, approximately $1.9times$ -- $2.1times$ higher than that of Intel SGX. NestedSGX incurs minimal overhead in most real-world applications, with an average overhead below 2% for computation and memory intensive workloads and below 15.68% for I/O intensive workloads.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189958", "vector": [], "sparse_vector": [], "title": "Understanding Data Importance in Machine Learning Attacks: Does Valuable Data Pose Greater Harm?", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning has revolutionized numerous domains, playing a crucial role in driving advancements and enabling data-centric processes. The significance of data in training models and shaping their performance cannot be overstated. Recent research has highlighted the heterogeneous impact of individual data samples, particularly the presence of valuable data that significantly contributes to the utility and effectiveness of machine learning models. However, a critical question remains unanswered: are these valuable data samples more vulnerable to machine learning attacks? In this work, we investigate the relationship between data importance and machine learning attacks by analyzing five distinct attack types. Our findings reveal notable insights. For example, we observe that high importance data samples exhibit increased vulnerability in certain attacks, such as membership inference and model stealing. These findings also carry practical implications, inspiring researchers to design more efficient attacks. By analyzing the linkage between membership inference vulnerability and data importance, we demonstrate that sample characteristics can be integrated into membership metrics by introducing sample-specific criteria, therefore enhancing the membership inference performance. These findings emphasize the urgent need for innovative defense mechanisms that strike a balance between maximizing utility and safeguarding valuable data against potential exploitation.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189959", "vector": [], "sparse_vector": [], "title": "Revisiting EM-based Estimation for Locally Differentially Private Protocols.", "authors": ["Yutong Ye", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper investigates the fundamental estimation problem in local differential privacy (LDP). We categorize existing estimation methods into two approaches, the unbiased estimation approach, which, under LDP, often gives unreasonable results (negative results or the sum of estimation does not equal to the total number of participating users), due to the excessive amount of noise added in LDP, and the maximal likelihood estimation (MLE)-based approach, which, can give reasonable results, but often suffers from the overfitting issue. To address this challenge, we propose a reduction framework inspired by Gaussian mixture models (GMM). We adapt the reduction framework to LDP estimation by transferring the estimation problem to the density estimation problem of the mixture model. Through the merging operation of the smallest weight component in this mixture model, the EM algorithm converges faster and produces a more robust distribution estimation. We show this framework offers a general and efficient way of modeling various LDP protocols. Through extensive evaluations, we demonstrate the superiority of our approach in terms of mean estimation, categorical distribution estimation, and numerical distribution estimation.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189960", "vector": [], "sparse_vector": [], "title": "CENSOR: Defense Against Gradient Inversion via Orthogonal Subspace Bayesian Sampling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Cheng", "Guangyu Shen", "<PERSON>", "Shengwei An", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning collaboratively trains a neural network on a global server, where each local client receives the current global model weights and sends back parameter updates (gradients) based on its local private data.The process of sending these model updates may leak client's private data information.Existing gradient inversion attacks can exploit this vulnerability to recover private training instances from a client's gradient vectors. Recently, researchers have proposed advanced gradient inversion techniques that existing defenses struggle to handle effectively.In this work, we present a novel defense tailored for large neural network models. Our defense capitalizes on the high dimensionality of the model parameters to perturb gradients within a textit{subspace orthogonal} to the original gradient. By leveraging cold posteriors over orthogonal subspaces, our defense implements a refined gradient update mechanism. This enables the selection of an optimal gradient that not only safeguards against gradient inversion attacks but also maintains model utility.We conduct comprehensive experiments across three different datasets and evaluate our defense against various state-of-the-art attacks and defenses.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189961", "vector": [], "sparse_vector": [], "title": "Explanation as a Watermark: Towards Harmless and Multi-bit Model Ownership Verification via Watermarking Feature Attribution.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Hong<PERSON> Yao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Ownership verification is currently the most critical and widely adopted post-hoc method to safeguard model copyright. In general, model owners exploit it to identify whether a given suspicious third-party model is stolen from them by examining whether it has particular properties `inherited' from their released models. Currently, backdoor-based model watermarks are the primary and cutting-edge methods to implant such properties in the released models. However, backdoor-based methods have two fatal drawbacks, including emph{harmfulness} and emph{ambiguity}. The former indicates that they introduce maliciously controllable misclassification behaviors ($i.e.$, backdoor) to the watermarked released models. The latter denotes that malicious users can easily pass the verification by finding other misclassified samples, leading to ownership ambiguity. In this paper, we argue that both limitations stem from the 'zero-bit' nature of existing watermarking schemes, where they exploit the status ($i.e.$, misclassified) of predictions for verification. Motivated by this understanding, we design a new watermarking paradigm, $i.e.$, Explanation as a Watermark (EaaW), that implants verification behaviors into the explanation of feature attribution instead of model predictions. Specifically, EaaW embeds a `multi-bit' watermark into the feature attribution explanation of specific trigger samples without changing the original prediction. We correspondingly design the watermark embedding and extraction algorithms inspired by explainable artificial intelligence. In particular, our approach can be used for different tasks ($e.g.$, image classification and text generation). Extensive experiments verify the effectiveness and harmlessness of our EaaW and its resistance to potential attacks.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189962", "vector": [], "sparse_vector": [], "title": "Lend Me Your Beam: Privacy Implications of Plaintext Beamforming Feedback in WiFi.", "authors": ["<PERSON><PERSON>", "Xian<PERSON> Chen", "<PERSON><PERSON>", "<PERSON>", "Jinsong Han"], "summary": "In recent years, the proliferation of WiFi-connected devices and related research has led to novel techniques of utilizing WiFi as sensors, i.e., capturing human movements through channel state information (CSI) perturbations. While this enables passive occupant sensing, it also introduces privacy risks from textit{leaked WiFi signals} that attackers can intercept, leading to threats like textit{occupancy detection}, critical in scenarios such as burglaries or stalking. We propose LeakyBeam, a novel and improved textit{occupancy detection attack} that leverages a new side channel from WiFi CSI, namely beamforming feedback information (BFI). BFI retains victim's movement information, even when transmitted through walls, and is easily captured since BFI packets are unencrypted, making them a rich source of privacy-sensitive information. Furthermore, we also introduce a defense mechanism that obfuscates BFI packets, requiring minimal hardware changes. We demonstrate LeakyBeam's effectiveness through a comprehensive real-world evaluation at a distance of 20 meters, achieving true positive and negative rates of 82.7% and 96.7%, respectively.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189963", "vector": [], "sparse_vector": [], "title": "RAIFLE: Reconstruction Attacks on Interaction-based Federated Learning with Adversarial Data Manipulation.", "authors": ["D<PERSON>ng Pham", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Federated learning has emerged as a promising privacy-preserving solution for machine learning domains that rely on user interactions, particularly recommender systems and online learning to rank. While there has been substantial research on the privacy of traditional federated learning, little attention has been paid to the privacy properties of these interaction-based settings. In this work, we show that users face an elevated risk of having their private interactions reconstructed by the central server when the server can control the training features of the items that users interact with. We introduce RAIFLE, a novel optimization-based attack framework where the server actively manipulates the features of the items presented to users to increase the success rate of reconstruction. Our experiments with federated recommendation and online learning-to-rank scenarios demonstrate that RAIFLE is significantly more powerful than existing reconstruction attacks like gradient inversion, achieving high performance consistently in most settings. We discuss the pros and cons of several possible countermeasures to defend against RAIFLE in the context of interaction-based federated learning. Our code is open-sourced at https://github.com/dzungvpham/raifle.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189964", "vector": [], "sparse_vector": [], "title": "On Borrowed Time - Preventing Static Side-Channel Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In recent years a new class of side-channel attacks has emerged. Instead of targeting device emissions during dynamic computation, adversaries now frequently exploit the leakage or response behaviour of integrated circuits in a static state. Members of this class include Static Power Side-Channel Analysis (SCA), Laser Logic State Imaging (LLSI) and Impedance Analysis (IA). Despite relying on different physical phenomena, they all enable the extraction of sensitive information from circuits in a static state with high accuracy and low noise -- a trait that poses a significant threat to many established side-channel countermeasures. In this work, we point out the shortcomings of existing solutions and derive a simple yet effective countermeasure. We observe that in order to realise their full potential, static side-channel attacks require the targeted data to remain unchanged for a certain amount of time. For some cryptographic secrets this happens naturally, for others it requires stopping the target circuit's clock. Our proposal, called Borrowed Time, hinders an attacker's ability to leverage such idle conditions, even if full control over the global clock signal is obtained. For that, by design, key-dependent data may only be present in unprotected temporary storage (e.g. flip-flops) when strictly needed. Borrowed Time then continuously monitors the target circuit and upon detecting an idle state, securely wipes sensitive contents. We demonstrate the need for our countermeasure and its effectiveness by mounting practical static power SCA attacks against cryptographic systems on FPGAs, with and without Borrowed Time. In one case we attack a masked implementation and show that it is only protected with our countermeasure in place. Furthermore we demonstrate that secure on-demand wiping of sensitive data works as intended, affirming the theory that the technique also effectively hinders LLSI and IA.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189965", "vector": [], "sparse_vector": [], "title": "Understanding Miniapp Malware: Identification, Dissection, and Characterization.", "authors": ["<PERSON><PERSON> Yang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Super apps, serving as centralized platforms that manage user information and integrate third-party miniapps, have revolutionized mobile computing but also introduced significant security risks from malicious miniapps. Despite the mandatory miniapp vetting enforced to the built-in miniapp store, the threat of evolving miniapp malware persists, engaging in a continual cat-and-mouse game with platform security measures. However, compared with traditional paradigms such as mobile and web computing, there has been a lack of miniapp malware dataset available for the community to explore, hindering the generation of crucial insights and the development of robust detection techniques. In response to this, this paper addresses the scarcely explored territory of malicious miniapp analysis, dedicating  over three year to identifying, dissecting, and examining the risks posed by these miniapps, resulting in the first miniapp malware dataset now available to aid future studies to enhance the security of super app ecosystems. To build the dataset, our primary focus has been on the WeChat platform, the largest super app, hosting millions of miniapps and serving a billion users. Over an extensive period, we collected over 4.5 million miniapps, identifying a subset (19, 905) as malicious through a rigorous cross-check process: 1) applying static signatures derived from real-world cases, and 2) confirming that the miniapps were delisted and removed from the market by the platform. With these identified samples, we proceed to characterize them, focusing on their lifecycle including propagation, activation, as well as payload execution. Additionally, we analyzed the collected malware samples using real-world cases to demonstrate their practical security impact.  Our findings reveal that these malware frequently target user privacy, leverage social network sharing capabilities to disseminate unauthorized services, and manipulate the advertisement-based revenue model to illicitly generate profits. These actions result in significant privacy and financial harm to both users and the platform.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189966", "vector": [], "sparse_vector": [], "title": "Non-intrusive and Unconstrained Keystroke Inference in VR Platforms via Infrared Side Channel.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Virtual Reality (VR) technologies are increasingly employed in numerous applications across various areas. Therefore, it is essential to ensure the security of interactions between users and VR devices. In this paper, we disclose a new side-channel leakage in the constellation tracking system of mainstream VR platforms, where the infrared (IR) signals emitted from the VR controllers for controller-headset interactions can be maliciously exploited to reconstruct unconstrained input keystrokes on the virtual keyboard non-intrusively. We propose a novel keystroke inference attack named VRecKey to demonstrate the feasibility and practicality of this novel infrared side channel. Specifically, VRecKey leverages a customized 2D IR sensor array to intercept ambient IR signals emitted from VR controllers and subsequently infers (i) character-level key presses on the virtual keyboard and (ii) word-level keystrokes along with their typing trajectories. We extensively evaluate the effectiveness of VRecKey with two commercial VR devices, and the results indicate that it can achieve over 94.2% and 90.5% top-3 accuracy in inferring character-level and word-level keystrokes with varying lengths, respectively. In addition, empirical results show that VRecKey is resilient to several practical impact factors and presents effectiveness in various real-world scenarios, which provides a complementary and orthogonal attack surface for the exploration of keystroke inference attacks in VR platforms.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189967", "vector": [], "sparse_vector": [], "title": "Passive Inference Attacks on Split Learning via Adversarial Regularization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yuncheng Wu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Split Learning (SL) has emerged as a practical and efficient alternative to traditional federated learning. While previous attempts to attack SL have often relied on overly strong assumptions or targeted easily exploitable models, we seek to develop more capable attacks. We introduce SDAR, a novel attack framework against SL with an honest-but-curious server. SDAR leverages auxiliary data and adversarial regularization to learn a decodable simulator of the client's private model, which can effectively infer the client's private features under the vanilla SL, and both features and labels under the U-shaped SL. We perform extensive experiments in both configurations to validate the effectiveness of our proposed attacks. Notably, in challenging scenarios where existing passive attacks struggle to reconstruct the client's private data effectively, SDAR consistently achieves significantly superior attack performance, even comparable to active attacks. On CIFAR-10, at the deep split level of 7, SDAR achieves private feature reconstruction with less than 0.025 mean squared error in both the vanilla and the U-shaped SL, and attains a label inference accuracy of over 98% in the U-shaped setting, while existing attacks fail to produce non-trivial results.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189968", "vector": [], "sparse_vector": [], "title": "LAMP: Lightweight Approaches for Latency Minimization in Mixnets with Practical Deployment Considerations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mixnets are a type of anonymous communication system designed to provide network privacy to users. They route client messages through multiple hops, with each hop (mix)perturbing the traffic patterns, thus making message tracing difficult for a network adversary. However, privacy in mixnets comes at the cost of increased latency, limiting the applicationsthat are usable when accessed through a mixnet. In this work we present LAMP, a set of routing approaches tailored for minimizing the propagation latency in mixnets with minimalimpact on anonymity. The design of these approaches is grounded in practical deployment considerations making them lightweight, easy to integrate with existing deployed mixnets and computationally realistic. We evaluate the proposed approaches using latency data from the deployed Nym mixnet and demonstrate that LAMP can reduce latency by a factor of 7.5 (from 153.4ms to 20ms) while maintaining high anonymity. LAMP even outperforms thestate-of-the-art system LARMix, providing 3× better latency-anonymity tradeoffs and significantly reducing the computational overhead by ≈ 13900× in comparison to LARMix.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189969", "vector": [], "sparse_vector": [], "title": "TrajDeleter: Enabling Trajectory Forgetting in Offline Reinforcement Learning Agents.", "authors": ["<PERSON>", "Kecen Li", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reinforcement learning (RL) trains an agent from experiences interacting with the environment. In scenarios where online interactions are impractical, offline RL, which trains the agent using pre-collected datasets, has become popular. While this new paradigm presents remarkable effectiveness across various real-world domains, like healthcare and energy management, there is a growing demand to enable agents to rapidly and completely eliminate the influence of specific trajectories from both the training dataset and the trained agents. To meet this problem, this paper advocates TRAJDELETER, the first practical approach to trajectory unlearning for offline RL agents. The key idea of TRAJDELETER is to guide the agent to demonstrate deteriorating performance when it encounters states associated with unlearning trajectories. Simultaneously, it ensures the agent maintains its original performance level when facing other remaining trajectories. Additionally, we introduce TRAJAUDITOR, a simple yet efficient method to evaluate whether TRAJDELETER successfully eliminates the specific trajectories of influence from the offline RL agent. Extensive experiments conducted on six offline RL algorithms and three tasks demonstrate that TRAJDELETER requires only about 1.5% of the time needed for retraining from scratch. It effectively unlearns an average of 94.8% of the targeted trajectories yet still performs well in actual environment interactions after unlearning. The replication package and agent parameters are available.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189970", "vector": [], "sparse_vector": [], "title": "JBomAudit: Assessing the Landscape, Compliance, and Security Implications of Java SBOMs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Abstract—A Software Bill of Materials (SBOM) is a detailed inventory that lists the dependencies that make up a software product. Accurate, complete, and up-to-date SBOMs are essential for vulnerability management, reducing license compliance risks, and maintaining high software integrity. The US National Institute of Standards and Technology (NTIA) has established minimum requirements for SBOMs to comply with, especially the correctness and completeness of listed dependencies in SBOMs. However, these requirements remain unexamined in practice. This paper presents the first systematic study on the landscape of SBOMs, including their prevalence, release trends, and characteristics in the Java ecosystem. We developed an end-to-end tool to evaluate the completeness and accuracy of dependencies in SBOMs. Our tool analyzed 25,882 SBOMs and associated JAR files, identifying that 7,907 SBOMs failed to disclose direct dependencies, highlighting the prevalence and severity of SBOM noncompliance issues. Furthermore, 4.97% of these omitted dependencies were vulnerable, leaving software susceptible to potential exploits. Through detailed measurement studies and analysis of root causes, this research uncovers significant security implications of non-compliant SBOMs, especially concerning vulnerability management. These findings, crucial for enhancing SBOM compliance assurance, are being responsibly reported to relevant stakeholders.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189971", "vector": [], "sparse_vector": [], "title": "Automatic Library Fuzzing through API Relation Evolvement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chang<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software libraries are foundational components in modern software ecosystems. Vulnerabilities within these libraries pose significant security threats. Fuzzing is a widely used technique for uncovering software vulnerabilities. However, its application to software libraries poses considerable challenges, necessitating carefully crafted drivers that reflect diverse yet correct API usages. Existing works on automatic library fuzzing either suffer from high false positives due to API misuse caused by arbitrarily generated API sequences, or fail to produce diverse API sequences by overly relying on existing code snippets that express restricted API usages, thus missing deeper API vulnerabilities.This work proposes NEXZZER, a new fuzzer that automatically detects vulnerabilities in libraries. NEXZZER employs a hybrid relation learning strategy to continuously infer and evolve API relations, incorporating a novel driver architecture to augment the testing coverage of libraries and facilitate deep vulnerability discovery. We evaluated NEXZZER across 18 libraries and the Google Fuzzer Test Suite. The results demonstrate its considerable advantages in code coverage and vulnerability-finding capabilities compared to prior works. NEXZZER can also automatically identify and filter out most API misuse crashes. Moreover, NEXZZER discovered 27 previously unknown vulnerabilities in well-tested libraries, including OpenSSL and libpcre2. At the time of writing, developers have confirmed 24 of them, and 9 were fixed because of our reports.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189972", "vector": [], "sparse_vector": [], "title": "HADES Attack: Understanding and Evaluating Manipulation Risks of Email Blocklists.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qingfeng Pan", "<PERSON>", "<PERSON>"], "summary": "DNS-Based Blocklist (DNSBL) has been a longstanding, effective mitigation against malicious emails. While works have focused on evaluating the quality of such blocklists, much less is known about their adoption, end-to-end operation, and security problems. Powered by industrial datasets of nondelivery reports within 15 months, this paper first performs largescale measurements on the adoption of DNSBLs, reporting their prevalent usage by busy email servers. From an empirical study on the end-to-end operation of 29 DNSBL providers, we find they heavily rely on capture servers, concealed infrastructure to lure blind senders of spam, in generating blocklists. However, we find such capture servers can be exploited and report the HADES attack, where non-abusive email servers are deliberately injected into popular DNSBLs. Legitimate emails from victims will then be broadly rejected by their peers. Through field tests, we demonstrate the attack is effective at low costs: we successfully inject our experimental email servers into 14 DNSBLs, within a time frame ranging from as fast as three minutes to no longer than 24 hours. Practical assessment also uncovers significant attack potential targeting high-profile victims, e.g., large email service providers and popular websites. Upon responsible disclosure, five DNSBL providers have acknowledged the issue, and we also propose possible mitigation. Findings of this paper highlight the need for revisiting DNSBL security and guidelines in its operation.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189973", "vector": [], "sparse_vector": [], "title": "Automated Mass Malware Factory: The Convergence of Piggybacking and Adversarial Example in Android Malicious Software Generation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Adversarial example techniques have been demonstrated to be highly effective against Android malware detection systems, enabling malware to evade detection with minimal code modifications. However, existing adversarial example techniques overlook the process of malware generation, thus restricting the applicability of adversarial example techniques. In this paper, we investigate piggybacked malware, a type of malware generated in bulk by piggybacking malicious code into popular apps, and combine it with adversarial example techniques. Given a malicious code segment (i.e., a rider), we can generate adversarial perturbations tailored to it and insert them into any carrier, enabling the resulting malware to evade detection. Through exploring the mechanism by which adversarial perturbation affects piggybacked malware code, we propose an adversarial piggybacked malware generation method, which comprises three modules: Malicious Rider Extraction, Adversarial Perturbation Generation, and Benign Carrier Selection. Extensive experiments have demonstrated that our method can efficiently generate a large volume of malware in a short period, and significantly increase the likelihood of evading detection. Our method achieved an average attack success rate (ASR) of 88.3% on machine learning-based detection models (e.g., Drebin and MaMaDroid), and an ASR of 76% and 92% on commercial engines Microsoft and Kingsoft, respectively. Furthermore, we have explored potential defenses against our adversarial piggybacked malware.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189974", "vector": [], "sparse_vector": [], "title": "Statically Discover Cross-Entry Use-After-Free Vulnerabilities in the Linux Kernel.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Use-After-Free (UAF) is one of the most widely spread and severe memory safety issues, attracting lots of research efforts toward its automatic discovery. Existing UAF detection approaches include two major categories: dynamic and static. While dynamic methods like fuzzing can detect UAF issues with high precision, they are inherently limited in code coverage. Static approaches, on the other hand, can usually only discover simple sequential UAF cases, despite that many real-world UAF bugs involve intricate cross-entry control and data flows (e.g., concurrent UAFs). Limited static tools supporting cross-entry UAF detection also suffer from inaccuracy or narrowed scope (e.g., cannot handle complex codebases like the Linux kernel). In this paper, we propose UAFX, a static analyzer capable of discovering cross-entry UAF vulnerabilities in the Linux kernel and potentially extensible to general C programs. UAFX is powered by a novel escape-fetch-based cross-entry alias analysis, enabling it to accurately analyze the alias relationships between the use and free sites even when they scatter in different entry functions. UAFX is also equipped with a systematic UAF validation framework based on partial-order constraints, allowing it to reliably reason about multiple UAF-related code aspects (e.g., locks, path conditions, threads) to filter out false alarms. Our evaluation shows that UAFX can discover new cross-entry UAF vulnerabilities in the kernel and one user-space program (80 true positive warnings), with reasonable reviewer-perceived precision (more than 40%) and performance.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189975", "vector": [], "sparse_vector": [], "title": "PropertyGPT: LLM-driven Formal Verification of Smart Contracts through Retrieval-Augmented Property Generation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ang Sun", "<PERSON>", "<PERSON><PERSON><PERSON> Shi", "<PERSON>"], "summary": "Formal verification is a technique that can prove the correctness of a system with respect to a certain specification or property. It is especially valuable for security-sensitive smart contracts that manage billions in cryptocurrency assets. Although existing research has developed various static verification tools (or provers) for smart contracts, a key missing component is theautomated generation of comprehensive properties, including invariants, pre-/post-conditions, and rules. Hence, industry-leading players like Certora have to rely on their own or crowdsourced experts to manually write properties case by case. With recent advances in large language models (LLMs), this paper explores the potential of leveraging state-of-the-art LLMs, such as GPT-4, to transfer existing human-written properties (e.g., those from Certora auditing reports) and automatically generate customized properties for unknown code. To this end, we embed existing properties into a vector database and retrieve a reference property for LLM-based in-context learning to generate a new property for a given code. While this basic process is relatively straightforward, ensuring that the generated properties are (i) compilable, (ii) appropriate, and (iii) verifiable presents challenges. To address (i), we use the compilation and static analysis feedback as an external oracle to guide LLMs in iteratively revising the generated properties. For (ii), we consider multiple dimensions ofsimilarity to rank the properties and employ a weighted algorithm to identify the top-K properties as the final result. For (iii), we design a dedicated prover to formally verify the correctness of the generated properties. We have implemented these strategies into a novel LLM-based property generation tool called PropertyGPT. Our experiments show that PropertyGPT can generate comprehensive and high-quality properties, achieving an 80% recall compared to the ground truth. It successfully detected 26 CVEs/attack incidents out of 37 tested and also uncovered 12 zero-day vulnerabilities, leading to $8,256 in bug bounty rewards.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189976", "vector": [], "sparse_vector": [], "title": "SCRUTINIZER: Towards Secure Forensics on Compromised TrustZone.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The number of vulnerabilities exploited in Arm TrustZone systems has been increasing recently. The absence of digital forensics tools prevents platform owners from incident response or periodic security scans. However, the area of secure forensics for compromised TrustZone remains unexplored and presents unresolved challenges. Traditional out-of-TrustZone forensics are inherently hindered by TrustZone protection, rendering them infeasible. In-TrustZone approaches are susceptible to attacks from privileged adversaries, undermining their security. To fill these gaps, we introduce SCRUTIN<PERSON><PERSON>ER, the first secure forensics solution for compromised TrustZone systems. SCRUTINI<PERSON>ER utilizes the highest privilege domain of the recent Arm Confidential Computing Architecture (CCA), called the Root world, and extends it to build a protected SCRUTINIZER Monitor. Our design proposes a protective layer in the Monitor that decouples the memory acquisition functionality from the Monitor and integrates it into an in-TrustZone agent. This ensures that the agent is isolated from TrustZone systems and helps to minimize the codebase expansion of the Root world. Furthermore, by grafting most of the target’s page tables in the agent, <PERSON><PERSON><PERSON><PERSON><PERSON>Z<PERSON> reduces redundant translation and mapping operations during memory acquisition, ultimately reducing performance overhead. SCRUTINIZER leverages multiple standard hardware features to enable secure forensic capabilities beyond pure memory acquisition, such as memory access traps and instruction tracing, while making them impervious to hardware configuration tampering by the privileged adversary. We prototype SCR<PERSON><PERSON><PERSON><PERSON><PERSON> and evaluate it using extensive experiments. The results show that SCRUTINIZER effectively inspects TrustZone systems while immune against privileged adversaries.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189977", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Data Deduplication for Enhancing Federated Learning of Language Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deduplication is a vital preprocessing step that enhances machine learning model performance and saves training time and energy. However, enhancing federated learning through deduplication poses challenges, especially regarding scalability and potential privacy violations if deduplication involves sharing all clients' data. In this paper, we address the problem of deduplication in a federated setup by introducing a pioneering protocol, Efficient Privacy-Preserving Multi-Party Deduplication (EP-MPD). It efficiently removes duplicates from multiple clients' datasets without compromising data privacy. EP-MPD is constructed in a modular fashion, utilizing two novel variants of the Private Set Intersection protocol. Our extensive experiments demonstrate the significant benefits of deduplication in federated learning of large language models. For instance, we observe up to 19.62% improvement in perplexity and up to 27.95% reduction in running time while varying the duplication level between 10% and 30%. EP-MPD effectively balances privacy and performance in federated learning, making it a valuable solution for large-scale applications.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189978", "vector": [], "sparse_vector": [], "title": "PolicyPulse: Precision Semantic Role Extraction for Enhanced Privacy Policy Comprehension.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The effectiveness of natural language privacy policies continues to be clouded by concerns surrounding their readability, ambiguity, and accessibility. Despite multiple design alternatives proposed over the years, natural language policies are still the primary format for organizations to communicate privacy practices to users. Current NLP techniques are often drawn towards generating high-level overviews, or specialized towards a single aspect of consumer privacy communication; the flexibility to apply them for multiple tasks is missing. To this aid, we present PolicyPulse, an information extraction pipeline designed to process privacy policies into usable formats. PolicyPulse employs a specialized XLNet classifier, and leverages a BERT-based model for semantic role labeling to extract phrases from policy sentences, while maintaining the semantic relations between predicates and their arguments. Our classification model was trained on 13,946 manually annotated semantic frames, and achieves a F1-score of 0.97 on identifying privacy practices communicated using clauses within a sentence. We emphasize the versatility of PolicyPulse through prototype applications to support requirement-driven policy presentations, question-answering systems, and privacy preference checking.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189979", "vector": [], "sparse_vector": [], "title": "The Guardians of Name Street: Studying the Defensive Registration Practices of the Fortune 500.", "authors": ["Bo<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Using orthographic, phonetic, and semantic models, we study the prevalence of defensive registrations related to a wide spectrum of transformations of the base domain names of Fortune 500 companies. As part of a large-scale evaluation, we explore several questions aimed at (a) understanding whether there are explainable factors (e.g., the size of the company's security team or its domain name's popularity rank) that correlate with a company's level of engagement regarding defensive registrations; (b) identifying the main actors in the defensive registration ecosystem that Fortune 500 companies rely upon; (c) uncovering the strategies used by these actors, and d) assessing the efficacy of those strategies from the perspective of queries emanating from a large Internet Service Provider (ISP). Overall, we identified 19,523 domain names defensively registered by 447 Fortune 500 companies. These companies engage in defensive registrations sparingly, with almost 200 companies having fewer than ten defensive registrations. By analyzing the registrations, we found many similarities between the types of domain names the companies registered. For instance, they all registered many TLD-squatting domain names. As it turns out, those similarities are due to the companies' reliance on online brand protection (OBP) service providers to protect their brands. Our analysis of the efficacy of the strategies of those OBPs showed that they register domain names that receive most of the potential squatting traffic. Using regression models, we learned from those strategies to provide recommendations for future defensive registrants. Our measurement also revealed many domain names that received high proportions of traffic over long periods of time and could be registered for only 15 USD. To prevent the abusive use of such domain names, we recommend that OBP providers proactively leverage passive DNS data to identify and preemptively register highly queried available domain names.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189980", "vector": [], "sparse_vector": [], "title": "Iris: Dynamic Privacy Preserving Search in Authenticated Chord Peer-to-Peer Networks.", "authors": ["Angeliki Aktypi", "<PERSON><PERSON>"], "summary": "In structured peer-to-peer networks, like Chord, users find data byasking a number of intermediate nodes in the network. Each nodeprovides the identity of the closet known node to the address of thedata, until eventually the node responsible for the data is reached.This structure means that the intermediate nodes learn the address ofthe sought after data. Revealing this information to other nodes makesChord unsuitable for applications that require query privacy so inthis paper we present a scheme Iris to provide query privacy whilemaintaining compatibility with the existing Chord protocol. This meansthat anyone using it will be able to execute a privacy preservingquery but it does not require other nodes in the network to use it (<PERSON><PERSON> know about it). In order to better capture the privacy achieved by the iterativenature of the search we propose a new privacy notion, inspired by$k$-anonymity. This new notion called $(alpha,delta)$-privacy, allows us to formulateprivacy guarantees against adversaries that collude and take advantageof the total amount of information leaked in all iterations of thesearch. We present a security analysis of the proposed algorithm based on theprivacy notion we introduce. We also develop a prototype of thealgorithm in Matlab and evaluate its performance. Our analysis provesIris to be $(alpha,delta)$-private while introducing a modest performanceoverhead. Importantly the overhead is tunable and proportional to therequired level of privacy, so no privacy means no overhead.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189981", "vector": [], "sparse_vector": [], "title": "Welcome to Jurassic Park: A Comprehensive Study of Security Risks in Deno and its Ecosystem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Node.js and its ecosystem npm are notoriously insecure, enabling the proliferation of supply chain attacks. Deno is an emerging runtime that promises to offer a safer alternative for running untrusted JavaScript code outside of the browser. Learning from Node.js’s mistakes, Deno is written in Rust, a memory-safe programming language, and it includes a strict permission system that checks all accesses to sensitive APIs via static or runtime permissions. Deno also allows the inclusion of third-party code via URLs, which promises a more transparent way of handling dependencies, advocating for a fully decentralized software supply chain. In this paper, we study if Deno delivers on its promise of increased security. We find that indeed Deno has a smaller attack surface than Node.js, but there still are known attacks that are not addressed (ReDoS) or only partially mitigated (prototype pollution). Moreover, we find several weaknesses in Deno’s permission system, which allow sophisticated supply chain attacks. First, coarse-grained permissions allow attackers to abuse the ambient authority of the operating system to sidestep the permission system. Second, we find that URL imports are exempted from the permission checks, allowing attackers to perform unlawful network requests. We also identify time-of-check to time-of-use issues when handling symbolic links, making fine-grained file system access control ineffective. We then perform an empirical study of Deno’s main ecosystem deno.land to understand how developers consume third-party code and how permissions are used and communicated. We identify classical URL-related issues such as expired domains and reliance on insecure transport protocols, but we also find that it is challenging to guarantee uniform immutability and version control when multiple domains are involved in code distribution. We also provide initial evidence that developers poorly document required permissions on deno.land and that they tend to abuse coarse-grained permissions, reducing the benefits of the permission system. Our findings resulted in two security advisories for Deno and a redesign of its import mechanism. We also make concrete recommendations for improving Deno’s security model to further prevent supply chain attacks: add import permissions, additional access control at file system level, support for compartmentalization, and a manifest file that persists fine-grained permissions.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189982", "vector": [], "sparse_vector": [], "title": "MALintent: Coverage Guided Intent Fuzzing Framework for Android.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Intents are the primary message-passing mechanism on Android, used for both communication between intra-app and inter-app components. Intents go across the trust boundary of applications and can break the security isolation between them. Due to their shared API with intra-app communication, apps may unintentionally expose functionality leading to important security bugs. MALintent is an open-source fuzzing framework that uses novel coverage instrumentation techniques and customizable bug oracles to find security issues in Android Intent handlers. MALintent is the first Intent fuzzer that applies greybox fuzzing on compiled closed-source Android applications. We demonstrate techniques widely compatible with many versions of Android and our bug oracles were able to find several crashes, vulnerabilities with privacy implications, and memory-safety issues in the top-downloaded Android applications on the Google Play store.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189983", "vector": [], "sparse_vector": [], "title": "Mysticeti: Reaching the Latency Limits with Uncertified DAGs.", "authors": ["<PERSON><PERSON><PERSON> Babel", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce Mysticeti-C, the first DAG-based Byzantine consensus protocol to achieve the lower bounds of latency of 3 message rounds.Since Mysticeti-C is built over DAGs it also achieves high resource efficiency and censorship resistance. Mysticeti-C achieves this latency improvement by avoiding explicit certification of the DAG blocks and by proposing a novel commit rule such that every block can be committed without delays, resulting in optimal latency in the steady state and under crash failures. We further extend Mysticeti-C to Mysticeti-FPC, which incorporates a fast commit path that achieves even lower latency for transferring assets. Unlike prior fast commit path protocols, Mysticeti-FPC minimizes the number of signatures and messages by weaving the fast path transactions into the DAG. This frees up resources, which subsequently result in better performance. We prove the safety and liveness in a Byzantine context. We evaluate both Mysticeti protocols and compare them with state-of-the-art consensus and fast path protocols to demonstrate their low latency and resource efficiency, as well as their more graceful degradation under crash failures. Mysticeti-C is the first Byzantine consensus protocol to achieve WAN latency of 0.5s for consensus commit while simultaneously maintaining state-of-the-art throughput of over 100k TPS. Finally, we report on integrating Mysticeti-C as the consensus protocol into a major deployed blockchain, resulting in over 4x latency reduction.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189984", "vector": [], "sparse_vector": [], "title": "type++: Prohibiting Type Confusion with Inline Type Information.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Type confusion, or bad casting, is a common C++ attack vector. Such vulnerabilities cause a program to interpret an object as belonging to a different type, enabling powerful attacks, like control-flow hijacking. C++ limits runtime checks to polymorphic classes because only those have inline type information. The lack of runtime type information throughout an object’s lifetime makes it challenging to enforce continuous checks and thereby prevent type confusion during downcasting. Current solutions either record type information for all objects disjointly, incurring prohibitive runtime overhead, or restrict protection to a fraction of all objects.Our C++ dialect, type++, enforces the paradigm that each allocated object involved in downcasting carries type information throughout its lifetime, ensuring correctness by enabling type checks wherever and whenever necessary. As not just polymorphic objects but all objects are typed, all down-to casts can now be dynamically verified. Compared to existing solutions, our strategy greatly reduces runtime cost and enables type++ usage both during testing and as mitigation. Targeting SPEC CPU2006 and CPU2017, we compile and run 2,040 kLoC, while changing only 314 LoC. To help developers, our static analysis warns where code changes in target programs may be necessary. Running the compiled benchmarks results in negligible performance overhead (1.19% on SPEC CPU2006 and 0.82% on SPEC CPU2017) verifying a total of 90B casts (compared to 3.8B for the state-of-the-art, a 23× improvement). type++ discovers 122 type confusion issues in the SPEC CPU benchmarks among which 62 are new. Targeting Chromium, we change 229 LoC out of 35 MLoC to protect 94.6% of the classes that could be involved in downcasting vulnerabilities, while incurring only 0.98% runtime overhead compared to the baseline.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189985", "vector": [], "sparse_vector": [], "title": "PQConnect: Automated Post-Quantum End-to-End Tunnels.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces PQConnect, a post-quantum end-to-end tunneling protocol that automatically protects all packets between clients that have installed PQConnect and servers that have installed and configured PQConnect. Like VPNs, PQConnect does not require any changes to higher-level protocols and application software. PQConnect adds cryptographic protection to unencrypted applications, works in concert with existing pre-quantum applications to add post-quantum protection, and adds a second application-independent layer of defense to any applications that have begun to incorporate application-specific post-quantum protection. Unlike VPNs, PQConnect automatically creates end-to-end tunnels to any number of servers using automatic peer discovery, with no need for the client administrator to configure per-server information. Each server carries out a client-independent configuration step to publish an announcement that the server's name accepts PQConnect connections. Any PQConnect client connecting to that name efficiently finds this announcement, automatically establishes a post-quantum point-to-point IP tunnel to the server, and routes traffic for that name through that tunnel. The foundation of security in PQConnect is the server's long-term public key used to encrypt and authenticate all PQConnect packets. PQConnect makes a conservative choice of post-quantum KEM for this public key. PQConnect also uses a smaller post-quantum KEM for forward secrecy, and elliptic curves to ensure pre-quantum security even in case of security failures in KEM design or KEM software. Security of the handshake component of PQConnect has been symbolically proven using Tamarin.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189986", "vector": [], "sparse_vector": [], "title": "SCAMMAGNIFIER: Piercing the Veil of Fraudulent Shopping Website Campaigns.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In an evolving digital environment under perpetual threat from cybercriminals, phishing remains a predominant concern. However, there is a shift towards fraudulent shopping websites---fraudulent websites offering bogus products or services while mirroring the user experience of legitimate shopping websites. A key open question is how important fraudulent shopping websites in the cybercrime ecosystem are? This study introduces a novel approach to detecting and analyzing fraudulent shopping websites through large-scale analysis and collaboration with industry partners. We present ScamMagnifier, a framework that collected and analyzed 1,155,237 shopping domains from May 2023 to June 2024, identifying 46,746 fraudulent websites. Our automated checkout process completed 41,863 transactions, revealing 5,278 merchant IDs associated with these scams. The collaborative investigations with one of major financial institutions also confirmed our findings and provided additional insights, linking 14,394 domains to these fraudulent merchants. In addition, we introduce a Chromium web extension to alert users of potential fraudulent shopping websites. This study contributes to a better understanding of e-Commerce fraud and provides valuable insights for developing more effective defenses against these evolving threats.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189987", "vector": [], "sparse_vector": [], "title": "The Kids Are All Right: Investigating the Susceptibility of Teens and Adults to YouTube Giveaway Scams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Fraudsters often use the promise of free goods as a lure for victims who are convinced to complete online tasks but ultimately receive nothing. Despite much work characterizing these \"giveaway scams,\" no human subjects research has investigated how users interact with them or what factors impact victimization. We conducted a scenario-based experiment with a sample of American teenagers (n = 85) and adult crowd workers (n = 205) in order to investigate how users reason about and interact with giveaway scams advertised in YouTube videos and to determine whether teens are more susceptible than adults. We found that most participants recognized the fraudulent nature of the videos, with only 9.2% believing the scam videos offered legitimate deals. Teenagers did not fall victim to the scams more frequently than adults but reported more experience searching for terms that could lead to victimization. This study is among the first to compare the interactions of adult and teenage users with internet fraud and sheds light on an understudied area of social engineering.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189988", "vector": [], "sparse_vector": [], "title": "NodeMedic-FINE: Automatic Detection and Exploit Synthesis for Node.js Vulnerabilities.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Node.js ecosystem comprises millions of packages written in JavaScript. Many packages suffer from vulnerabilities such as arbitrary code execution (ACE) and arbitrary command injection (ACI). Prior work has developed automated tools based on dynamic taint tracking to detect potential vulnerabilities, and to synthesize proof-of-concept exploits that confirm them, with limited success. One challenge these tools face is that expected inputs to package APIs often have varied types and object structure. Failure to call these APIs with inputs of the correct type and with specific fields leads to unsuccessful exploit generation and missed vulnerabilities. Generating inputs that can successfully deliver the desired exploit payload despite manipulation performed by the package is also difficult. To address these challenges, we use a type and object-structure aware fuzzer to generate inputs to explore more execution paths during dynamic taint analysis. We leverage information generated by the taint analysis to infer the types and structure of the inputs, which are then used by the exploit synthesis engine to guide exploit generation. We implement NodeMedic-FINE and evaluate it on 33,011 npm packages that contain calls to ACE and ACI sinks. Our tool finds 2257 potential flows and automatically synthesizes working exploits in 766 packages.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189989", "vector": [], "sparse_vector": [], "title": "Speak Up, I&apos;m Listening: Extracting Speech from Zero-Permission VR Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As Virtual Reality (VR) technologies advance, their application in privacy-sensitive contexts, such as meetings, lectures, simulations, and training, expands. These environments often involve conversations that contain privacy-sensitive information about users and the individuals with whom they interact. The presence of advanced sensors in modern VR devices raises concerns about possible side-channel attacks that exploit these sensor capabilities. In this paper, we introduce IMMERSPY, a novel acoustic side-channel attack that exploits motion sensors in VR devices to extract sensitive speech content from on-device speakers. We analyze two powerful attacker scenarios: informed attacker, where the attacker possesses labeled data about the victim, and uninformed attacker, where no prior victim information is available. We design a Mel-spectrogram CNN-LSTM model to extract digit information (e.g., social security or credit card numbers) by learning the speech-induced vibrations captured by motion sensors. Our experiments show that IMMERSPY detects four consecutive digits with 74% accuracy and 16-digit sequences, such as credit card numbers, with 62% accuracy. Additionally, we leverage Generative AI text-to-speech models in our attack experiments to illustrate how the attackers can create training datasets even without the need to use the victim’s labeled data. Our findings highlight the critical need for security measures in VR domains to mitigate evolving privacy risks. To address this, we introduce a defense technique that emits inaudible tones through the Head-Mounted Display (HMD) speakers, showing its effectiveness in mitigating acoustic side-channel attacks.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189990", "vector": [], "sparse_vector": [], "title": "DiStefano: Decentralized Infrastructure for Sharing Trusted Encrypted Facts and Nothing More.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We design DiStefano: an efficient, maliciously-secure framework for generating private commitments over TLS-encrypted web traffic, for verification by a designated third-party. DiStefano provides many improvements over previous TLS commitment systems, including: a modular protocol specific to TLS 1.3, support for arbitrary verifiable claims over encrypted data, client browsing history privacy amongst pre-approved TLS servers, and various optimisations to ensure fast online performance of the TLS 1.3 session. We build a permissive open-source implementation of DiStefano integrated into the BoringSSL cryptographic library (used by Chromium-based Internet browsers). We show that DiStefano is practical in both LAN and WAN settings for committing to facts in arbitrary TLS traffic, requiring < 1 s and ≤ 80 KiB to execute the complete online phase of the protocol.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189991", "vector": [], "sparse_vector": [], "title": "Magmaw: Modality-Agnostic Adversarial Attacks on Machine Learning-Based Wireless Communication Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine Learning (ML) has been instrumental in enabling joint transceiver optimization by merging all physical layer blocks of the end-to-end wireless communication systems. Although there have been a number of adversarial attacks on ML-based wireless systems, the existing methods do not provide a comprehensive view including multi-modality of the source data, common physical layer protocols, and wireless domain constraints. This paper proposes Magmaw, a novel wireless attack methodology capable of generating universal adversarial perturbations for any multimodal signal transmitted over a wireless channel. We further introduce new objectives for adversarial attacks on downstream applications. We adopt the widely used defenses to verify the resilience of Magmaw. For proof-of-concept evaluation, we build a real-time wireless attack platform using a software-defined radio system. Experimental results demonstrate that Magmaw causes significant performance degradation even in the presence of strong defense mechanisms. Furthermore, we validate the performance of Magmaw in two case studies: encrypted communication channel and channel modality-based ML model. Our code is available at https://github.com/juc023/Magmaw.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189992", "vector": [], "sparse_vector": [], "title": "Manifoldchain: Maximizing Blockchain Throughput via Bandwidth-Clustered Sharding.", "authors": ["Chunjiang Che", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bandwidth limitation is the major bottleneck that hinders scaling throughput of proof-of-work blockchains. To guarantee security, the mining rate of the blockchain is determined by the miners with the lowest bandwidth, resulting in an inefficient bandwidth utilization among fast miners. We propose Manifoldchain, an innovative blockchain sharding protocol that alleviates the impact of slow miners to maximize blockchain throughput. Manifoldchain utilizes a bandwidth-clustered shard formation mechanism that groups miners with similar bandwidths into the same shard. Consequently, this approach enables us to set an optimal mining rate for each shard based on its bandwidth, effectively reducing the waiting time caused by slow miners. Nevertheless, the adversary could corrupt miners with similar bandwidths, thereby concentrating hashing power and potentially creating an adversarial majority within a single shard. To counter this adversarial strategy, we introduce textit{sharing mining}, allowing the honest mining power of the entire network to participate in the secure ledger formation of each shard, thereby achieving the same level of security as an unsharded blockchain. Additionally, we introduce an asynchronous atomic commitment mechanism to ensure transaction atomicity across shards with various mining rates. Our theoretical analysis demonstrates that Manifoldchain scales linearly in throughput with the increase in shard numbers and inversely with network delay in each shard. We implement a full system prototype of Manifoldchain, comprehensively evaluated on both simulated and real-world testbeds. These experiments validate its vertical scalability with network bandwidth and horizontal scalability with network size, achieving a substantial improvement of 186% in throughput over baseline sharding protocols, for scenarios where bandwidths of miners range from 5Mbps to 60Mbps.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189993", "vector": [], "sparse_vector": [], "title": "Cross-Origin Web Attacks via HTTP/2 Server Push and Signed HTTP Exchange.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we investigate the security implications of HTTP/2 server push and signed HTTP exchange (SXG) on the Same-Origin Policy (SOP), a fundamental web security mechanism designed to prevent cross-origin attacks. We identify a vulnerability introduced by these features, where the traditional strict SOP origin based on URI is undermined by a more permissive HTTP/2 authority based on the SubjectAlternativeName (SAN) list in the TLS certificate. This relaxation of origin constraints, coupled with the prevalent use of shared certificates among unrelated domains, poses significant security risks, allowing attackers to bypass SOP protections. We introduce two novel attack vectors, CrossPUSH and CrossSXG, which enable an off-path attacker to execute a wide range of cross-origin web attacks, including arbitrary cross-site scripting (XSS), cookie manipulation, and malicious file downloads, across all domains listed in a shared certificate. Our investigation reveals the practicality and prevalence of these threats, with our measurements uncovering vulnerabilities in widely-used web browsers such as Chrome and Edge, and notable websites including Microsoft. We responsibly disclose our findings to affected vendors and receive acknowledgments from Huawei, Baidu, Microsoft, etc.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189994", "vector": [], "sparse_vector": [], "title": "Dissecting Payload-based Transaction Phishing on Ethereum.", "authors": ["<PERSON><PERSON>", "Yufeng Hu", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, a more advanced form of phishing has arisen on Ethereum, surpassing early-stage, simple transaction phishing. This new form, which we refer to as payload-based transaction phishing (PTXPHISH), manipulates smart contract interactions through the execution of malicious payloads to deceive users. PTXPHISH has rapidly emerged as a significant threat, leading to incidents that caused losses exceeding $70 million in 2023 reports. Despite its substantial impact, no previous studies have systematically explored PTXPHISH. In this paper, we present the first comprehensive study of the PTXPHISH on Ethereum. Firstly, we conduct a long-term data collection and put considerable effort into establishing the first ground-truth PTXPHISH dataset, consisting of 5,000 phishing transactions. Based on the dataset, we dissect PTXPHISH, categorizing phishing tactics into four primary categories and eleven sub-categories. Secondly, we propose a rule-based multi-dimensional detection approach to identify PTXPHISH, achieving an F1-score of over 99% and processing each block in an average of 390 ms. Finally, we conduct a large-scale detection spanning 300 days and discover a total of 130,637 phishing transactions on Ethereum, resulting in losses exceeding $341.9 million. Our in-depth analysis of these phishing transactions yielded valuable and insightful findings. Scammers consume approximately 13.4 ETH daily, which accounts for 12.5% of the total Ethereum gas, to propagate address poisoning scams. Additionally, our analysis reveals patterns in the cash-out process employed by phishing scammers, and we find that the top five phishing organizationsare responsible for 40.7% of all losses. Furthermore, our work has made significant contributions to mitigating real-world threats. We have reported 1,726 phishing addresses to the community, accounting for 42.7% of total community contributions during the same period. Additionally, we have sent 2,539 on-chain alert messages, assisting 1,980 victims. This research serves as a valuable reference in combating the emerging PTXPHISH and safeguarding users’ assets.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189995", "vector": [], "sparse_vector": [], "title": "Compiled Models, Built-In Exploits: Uncovering Pervasive Bit-Flip Attack Surfaces in DNN Executables.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yuanyuan Yuan", "<PERSON><PERSON> Hu", "<PERSON><PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>"], "summary": "Recent research has shown that bit-flip attacks (BFAs) can manipulate deep neural networks (DNNs) via DRAM Rowhammer exploitations. For high-level DNN models running on deep learning (DL) frameworks like PyTorch, extensive BFAs have been conducted to flip bits in model weights and shown effective. Defenses have also been proposed to guard model weights. Nevertheless, DNNs are increasingly compiled into DNN executables by DL compilers to leverage hardware primitives. These executables manifest new and distinct computation paradigms; we find existing research failing to accurately capture and expose the attack surface of BFAs on DNN executables. To this end, we launch the first systematic study of BFAs on DNN executables and reveal new attack surfaces neglected or underestimated in previous work. Specifically, prior BFAs in DL frameworks are limited to attacking model weights and assume a strong whitebox attacker with full knowledge of victim model weights, which is unrealistic as weights are often confidential. In contrast, we find that BFAs on DNN executables can achieve high effectiveness by exploiting the model structure (usually stored in the executable code), which only requires knowing the (often public) model structure. Importantly, such structure-based BFAs are pervasive, transferable, and more severe (e.g., single-bit flips lead to successful attacks) in DNN executables; they also slip past existing defenses. To realistically demonstrate the new attack surfaces, we assume a weak and more realistic attacker with no knowledge of victim model weights. We design an automated tool to identify vulnerable bits in victim executables with high confidence (70% compared to the baseline 2%). Launching this tool on DDR4 DRAM, we show that only 1.4 flips on average are needed to fully downgrade the accuracy of victim executables, including quantized models which could require 23× more flips previously, to random guesses. We comprehensively evaluate 16 DNN executables, covering three large-scale DNN models trained on three commonly-used datasets compiled by the two most popular DL compilers. Our finding calls for incorporating security mechanisms in future DNN compilation toolchains.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189996", "vector": [], "sparse_vector": [], "title": "BitShield: Defending Against Bit-Flip Attacks on DNN Executables.", "authors": ["<PERSON><PERSON><PERSON>", "Yuanyuan Yuan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Hu", "<PERSON><PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>"], "summary": "Recent research has demonstrated the severity and prevalence of bit-flip attacks (BFAs; e.g., with Rowhammer techniques) on deep neural networks (DNNs). BFAs can manipulate DNN prediction and completely deplete DNN intelligence, and can be launched against both DNNs running on deep learning (DL) frameworks like PyTorch, as well as those compiled into standalone executables by DL compilers. While BFA defenses have been proposed for models on DL frameworks, we find them incapable of protecting DNN executables due to the new attack vectors on these executables. This paper proposes the first defense against BFA for DNN executables. We first present a motivating study to demonstrate the fragility and unique attack surfaces of DNN executables. Specifically, attackers can flip bits in the `.text` section to alter the computation logic of DNN executables and consequently manipulate DNN predictions; previous defenses guarding model weights can also be easily evaded when implemented in DNN executables. Subsequently, we propose BitShield, a full-fledged defense that detects BFAs targeting both data and `.text` sections in DNN executables. We novelly model BFA on DNN executables as a process to corrupt their semantics, and base BitShield on semantic integrity checks. Moreover, by deliberately fusing code checksum routines into a DNN’s semantics, we make BitShield highly resilient against BFAs targeting itself. BitShield is integrated in a popular DL compiler (Amazon TVM) and is compatible with all existing compilation and optimization passes. Unlike prior defenses, BitShield is designed to protect more vulnerable full-precision DNNs and does not assume specific attack methods, exhibiting high generality. BitShield also proactively detects ongoing BFA attempts instead of passively hardening DNNs. Evaluations show that BitShield provides strong protection against BFAs (average mitigation rate 97.51%) with low performance overhead (2.47% on average) even when faced with fully white-box, powerful attackers.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189997", "vector": [], "sparse_vector": [], "title": "SongBsAb: A Dual Prevention Approach against Singing Voice Conversion based Illegal Song Covers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Fu Song", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Singing voice conversion (SVC) automates song covers by converting a source singing voice from a source singer into a new singing voice with the same lyrics and melody as the source, but sounds like being covered by the target singer of some given target singing voices. However, it raises serious concerns about copyright and civil right infringements. We propose SongBsAb, the first proactive approach to tackle SVC-based illegal song covers. SongBsAb adds perturbations to singing voices before releasing them, so that when they are used, the process of SVC will be interfered, leading to unexpected singing voices. Perturbations are carefully crafted to (1) provide a dual prevention, i.e., preventing the singing voice from being used as the source and target singing voice in SVC, by proposing a gender-transformation loss and a high/low hierarchy multi-target loss, respectively; and (2) be harmless, i.e., no side-effect on the enjoyment of protected songs, by refining a psychoacoustic model-based loss with the backing track as an additional masker, a unique accompanying element for singing voices compared to ordinary speech voices. We also adopt a frame-level interaction reduction-based loss and encoder ensemble to enhance the transferability of SongBsAb to unknown SVC models. We demonstrate the prevention effectiveness, harmlessness, and robustness of SongBsAb on five diverse and promising SVC models, using both English and Chinese datasets, and both objective and human study-based subjective metrics. Our work fosters an emerging research direction for mitigating illegal automated song covers.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189998", "vector": [], "sparse_vector": [], "title": "Recurrent Private Set Intersection for Unbalanced Databases with Cuckoo Hashing and Leveled FHE.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A Private Set Intersection (PSI) protocol is a cryptographic method allowing two parties, each with a private set, to determine the intersection of their sets without revealing any information about their entries except for the intersection itself. While extensive research has focused on PSI protocols, most studies have centered on scenarios where two parties possess sets of similar sizes, assuming a semi-honest threat model.However, when the sizes of the parties' sets differ significantly, a generalized solution tends to underperform compared to a specialized one, as recent research has demonstrated. Additionally, conventional PSI protocols are typically designed for a single execution, requiring the entire protocol to be re-executed for each set intersection. This approach is suboptimal for applications such as URL denylisting and email filtering, which may involve multiple set intersections of small sets against a large set (e.g., one for each email received).In this study, we propose a novel PSI protocol optimized for the recurrent setting where parties have unbalanced set sizes. We implement our protocol using Levelled Fully Homomorphic Encryption and Cuckoo hashing, and introduce several optimizations to ensure real-time performance. By utilizing the Microsoft SEAL library, we demonstrate that our protocol can perform private set intersections in 20 ms and 240 ms on 10 Gbps and 100 Mbps networks, respectively.Compared to existing solutions, our protocol offers significant improvements, reducing set intersection times by one order of magnitude on slower networks and by two orders of magnitude on faster networks.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "189999", "vector": [], "sparse_vector": [], "title": "Crosstalk-induced Side Channel Threats in Multi-Tenant NISQ Computers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As quantum computing rapidly advances, its near-term applications are becoming increasingly evident. However, the high cost and under-utilization of quantum resources are prompting a shift from single-user to multi-user access models. In a multi-tenant environment, where multiple users share one quantum computer, protecting user confidentiality becomes crucial. The varied uses of quantum computers increase the risk that sensitive data encoded by one user could be compromised by others, rendering the protection of data integrity and confidentiality essential.In the evolving quantum computing landscape, it is imperative to study these security challenges within the scope of realistic threat model assumptions,wherein an adversarial user can mount practical attacks without relying on any heightened privileges afforded by physical access to a quantum computer or rogue cloud services.In this paper, we demonstrate the potential of crosstalk as an attack vector for the first time on a Noisy Intermediate Scale Quantum (NISQ) machine, that an adversarial user can exploit within a multi-tenant quantum computing model.The proposed side-channel attack is conducted with minimal and realistic adversarial privileges, with the overarching aim of uncovering the quantum algorithm being executed by a victim. Crosstalk signatures are used to estimate the presence of CNOT gates in the victim circuit, and subsequently, this information is encoded and classified by a graph-based learning model to identify the victim quantum algorithm. When evaluated on up to 336 benchmark circuits, our attack framework is found to be able to unveil the victim's quantum algorithm with up to 85.7% accuracy.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190000", "vector": [], "sparse_vector": [], "title": "Starshields for iOS: Navigating the Security Cosmos in Satellite Communication.", "authors": ["Jiska Classen", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Apple has integrated satellite communication into their latest iPhones, enabling emergency communication, road- side assistance, location sharing with friends, iMessage, and SMS. This technology allows communication when other wireless services are unavailable. However, the use of satellites poses restrictions on bandwidth and delay, making it difficult to use modern communication protocols with their security and privacy guarantees. To overcome these challenges, Apple designed and implemented a proprietary satellite communication protocol to address these limitations. We are the first to successfully reverse-engineer this protocol and analyze its security and privacy properties. In addition, we develop a simulation-based testbed for testing emergency services without causing emergency calls. Our tests reveal protocol and infrastructure design issues. For example, compact protocol messages come at the cost of missing integrity protection and require an internet-based setup phase. We further demonstrate various restriction bypasses, such as misusing location sharing to send arbitrary text messages on old iOS versions, and sending iMessages over satellite from region-locked countries. These bypasses allow us to overcome censorship and operator control of text messaging services.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190001", "vector": [], "sparse_vector": [], "title": "Tweezers: A Framework for Security Event Detection via Event Attribution-centric Tweet Embedding.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Seungwon Shin", "<PERSON><PERSON><PERSON>"], "summary": "Twitter is recognized as a crucial platform for the dissemination and gathering of Cyber Threat Intelligence (CTI). Its capability to provide real-time, actionable intelligence makes it a indispensable tool for detecting security events, helping security professionals cope with ever-growing threats. However, the large volume of tweets and inherent noises of human-crafted tweets pose significant challenges in accurately identifying security events. While many studies tried to filter out event-related tweets based on keywords, they are not effective due to their limitation in understanding the semantics of tweets. Another challenge in security event detection from Twitter is the comprehensive coverage of security events. Previous studies emphasized the importance of early detection of security events, but they overlooked the importance of event coverage. To cope with these challenges, in our study, we introduce a novel event attribution-centric tweet embedding method to enable the high precision and coverage of events. Our experiment result shows that the proposed method outperforms existing text and graph-based tweet embedding methods in identifying security events. Leveraging this novel embedding approach, we have developed and implemented a framework, textit{Tweezers}, that is applicable to security event detection from Twitter for CTI gathering. This framework has demonstrated its effectiveness, detecting twice as many events compared to established baselines. Additionally, we have showcased two applications, built on textit{Tweezers} for the integration and inspection of security events, i.e., security event trend analysis and informative security user identification.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190002", "vector": [], "sparse_vector": [], "title": "RACONTEUR: A Knowledgeable, Insightful, and Portable LLM-Powered Shell Command Explainer.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Xu"], "summary": "Malicious shell commands are linchpins to many cyber-attacks, but may not be easy to understand by security analysts due to complicated and often disguised code structures. Advances in large language models (LLMs) have unlocked the possibility of generating understandable explanations for shell commands. However, existing general-purpose LLMs suffer from a lack of expert knowledge and a tendency to hallucinate in the task of shell command explanation. In this paper, we present <PERSON><PERSON><PERSON><PERSON>, a knowledgeable, expressive and portable shell command explainer powered by LLM. Racon<PERSON>ur is infused with professional knowledge to provide comprehensive explanations on shell commands, including not only what the command does (i.e., behavior) but also why the command does it (i.e., purpose). To shed light on the high-level intent of the command, we also translate the natural-language-based explanation into standard technique & tactic defined by MITRE ATT&CK, the worldwide knowledge base of cybersecurity. To enable <PERSON><PERSON><PERSON><PERSON> to explain unseen private commands, we further develop a documentation retriever to obtain relevant information from complementary documentations to assist the explanation process. We have created a large-scale dataset for training and conducted extensive experiments to evaluate the capability of <PERSON><PERSON><PERSON><PERSON> in shell command explanation. The experiments verify that <PERSON><PERSON><PERSON><PERSON> is able to provide high-quality explanations and in-depth insight of the intent of the command.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190003", "vector": [], "sparse_vector": [], "title": "Time-varying Bottleneck Links in LEO Satellite Networks: Identification, Exploits, and Countermeasures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we perform a multifaceted study on the security risk involved by the unique time-varying bottleneck links in emerging Low-Earth Orbit (LEO) satellite networks (LSNs). We carry out our study in three steps. First, we profile the spatial and temporal characteristics of bottleneck links and how they might be exploited for bottleneck identification. Thus, the bottleneck links imposes a new risk of link flooding attack (LFA) on LSNs. Second, we propose SKYFALL, a new LFA risk analyzer that enables satellite network operators to simulate various LFA behaviors and comprehensively analyze the consequences on LSN services. Concretely, SKYFALL's analysis based on real-world information of operational LSNs demonstrates that the throughput of legal background traffic could be reduced by a factor of 3.4 if an attacker can manipulate a number of compromised user terminals  to continuously congest the bottleneck links. Based on our analysis, we finally discuss the limitations of traditional LFA countermeasures and propose new mitigation strategies for LSNs.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190004", "vector": [], "sparse_vector": [], "title": "Probe-Me-Not: Protecting Pre-trained Encoders from Malicious Probing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Adapting pre-trained deep learning models to customized tasks has become a popular choice for developers to cope with limited computational resources and data volume. More specifically, probing--training a classifier on a pre-trained encoder--has been widely adopted in transfer learning, which helps to prevent overfitting and catastrophic forgetting. However, such generalizability of pre-trained encoders raises concerns about the potential misuse of probing for harmful applications, such as discriminatory speculation and warfare applications. In this work, we introduce EncoderLock, a novel applicability authorization method designed to protect pre-trained encoders from malicious probing, i.e., yielding poor performance on specified prohibited domains while maintaining their utility in authorized ones. Achieving this balance is challenging because of the opposite optimization objectives and the variety of downstream heads that adversaries can utilize adaptively. To address these challenges, EncoderLock employs two techniques: domain-aware weight selection and updating to restrict applications on prohibited domains/tasks, and self-challenging training scheme that iteratively strengthens resistance against any potential downstream classifiers that adversaries may apply. Moreover, recognizing the potential lack of data from prohibited domains in practical scenarios, we introduce three EncoderLock variants with different levels of data accessibility: supervised (prohibited domain data with labels), unsupervised (prohibited domain data without labels), and zero-shot (no data or labels available). Extensive experiments across fifteen domains and three model architectures demonstrate EncoderLock's effectiveness over baseline methods using non-transferable learning. Additionally, we verify EncoderLock's effectiveness and practicality with a real-world pre-trained Vision Transformer (ViT) encoder from Facebook. These results underscore the valuable contributions Encoder<PERSON>ock brings to the development of responsible AI.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190005", "vector": [], "sparse_vector": [], "title": "The Philosopher&apos;s Stone: Trojaning Plugins of Large Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Open-source Large Language Models (LLMs) have recently gained popularity because of their comparable performance to proprietary LLMs. To efficiently fulfill domain-specialized tasks, open-source LLMs can be refined, without expensive accelerators, using low-rank adapters. However, it is still unknown whether low-rank adapters can be exploited to control LLMs. To address this gap, we demonstrate that an infected adapter can induce, on specific triggers, an LLM to output content defined by an adversary and to even maliciously use tools. To train a Trojan adapter, we propose two novel attacks, POLISHED and FUSION, that improve over prior approaches. POLISHED uses a superior LLM to align naïvely poisoned data based on our insight that it can better inject poisoning knowledge during training. In contrast, FUSION leverages a novel over-poisoning procedure to transform a benign adapter into a malicious one by magnifying the attention between trigger and target in model weights. In our experiments, we first conduct two case studies to demonstrate that a compromised LLM agent can use malware to control the system (e.g., a LLM-driven robot) or to launch a spear-phishing attack. Then, in terms of targeted misinformation, we show that our attacks provide higher attack effectiveness than the existing baseline and, for the purpose of attracting downloads, preserve or improve the adapter’s utility. Finally, we designed and evaluated three potential defenses. However, none proved entirely effective in safeguarding against our attacks, highlighting the need for more robust defenses supporting a secure LLM supply chain.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190006", "vector": [], "sparse_vector": [], "title": "Ring of Gyges: Accountable Anonymous Broadcast via Secret-Shared Shuffle.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Anonymous broadcast systems, which allow users to post messages on a public bulletin board without revealing their identities, have been of persistent interest over the years.Recent designs utilizing multi-party computation (MPC) techniques have shown competitive computational efficiency (CCS'20, NDSS'22, PETS'23).However, these systems still fall short in communication overhead, which also dominates the overall performance.Besides, they fail to adequately address threats from misbehaving users, such as repeatedly spamming the system with inappropriate, illegal content.These tangible issues usually undermine the practical adoption of anonymous systems. This work introduces _Gyges_, an MPC-based anonymous broadcast system that minimizes its inter-server communication while reconciling critical anonymity and accountability guarantees.At the crux of _Gyges_ lies an honest-majority four-party secret-shared relay.These relay parties jointly execute two key protocols: 1) a \"silent shuffling\" protocol that requires no online communication but relies solely on non-interactive, local computations to unlink users from their messages, thereby ensuring sender anonymity; 2) a companion fast and lean tracing protocol capable of relinking a specific shuffled message back to its originator when the content severely violates moderation policy, without jeopardizing others' anonymity guarantees.Additionally, _Gyges_ adheres to the private robustness to resist potential malicious disruptions, guaranteeing output delivery while preserving sender anonymity.To better support a large user base, the system also supports both vertical and horizontal scaling.Our evaluation results show that _Gyges_'s communication-efficient shuffle designs outperform state-of-the-art MPC-based anonymous broadcast solutions, such as Clarion (NDSS'22) and RPM (PETS'23), while its shared trace technique can swiftly track down the misbehaving users (when necessary), giving orders of magnitude cost reductions compared to traceable mixnets (PETS'24) that offers similar capabilities.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190007", "vector": [], "sparse_vector": [], "title": "The Power of Words: A Comprehensive Analysis of Rationales and Their Effects on Users&apos; Permission Decisions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Rationales offer a method for app developers to convey their permission needs to users. While guidelines and recommendations exist on how to request permissions, developers have the creative freedom to design and phrase these rationales. In this work, we explore the characteristics of real-world rationales and how their building blocks affect users' permission decisions and their evaluation of those decisions. Through an analysis of 720 sentences and 428 screenshots of rationales from the top apps of Google Play, we identify the various phrasing and design elements of rationales. Subsequently, in a user study involving 960 participants, we explore how different combinations of phrasings impact users' permission decision-making process. By aligning our insights with established recommendations, we offer actionable guidelines for developers, aiming to make rationales a usable security instrument for users.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190008", "vector": [], "sparse_vector": [], "title": "Deanonymizing Device Identities via Side-channel Attacks in Exclusive-use IoTs &amp; Mitigation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Wireless technologies like Bluetooth Low Energy (BLE) and Wi-Fi are essential to the Internet of Things (IoT), facilitating seamless device communication without physical connections. However, this convenience comes at a cost—exposed data exchanges that are susceptible to observation by attackers, leading to serious security and privacy threats such as device tracking. Although protocol designers have traditionally relied on strategies like address and identity randomization as a countermeasure, our research reveals that these attacks remain a significant threat due to a historically overlooked, fundamental flaw in exclusive-use wireless communication. We define _exclusive-use_ as a scenario where devices are designed to provide functionality solely to anassociated or paired device. The unique communication patterns inherent in these relationships create an observable boolean side-channel that attackers can exploit to discover whether two devices “trust” each other. This information leak allows for the deanonymization of devices, enabling tracking even in the presence of modern countermeasures. We introduce our tracking attacks as _IDBleed_ and demonstrate that BLE and Wi-Fi protocols that support confidentiality, integrity, and authentication remain vulnerable to deanonymization due to this fundamental flaw in exclusive-use communication patterns. Finally, we propose and quantitatively evaluate a generalized, privacy-preserving mitigation we call _Anonymization Layer_ to find a negligible 2% approximate overhead in performance and power consumption on tested smartphones and PCs.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190009", "vector": [], "sparse_vector": [], "title": "Wallbleed: A Memory Disclosure Vulnerability in the Great Firewall of China.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Sakamoto San", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Wedwards", "<PERSON>"], "summary": "We present textit{<PERSON><PERSON><PERSON>}, a buffer over-read vulnerability that existed in the DNS injection subsystem of the Great Firewall of China. Wallbleed caused certain nation-wide censorship middleboxes to reveal up to 125 bytes of their memory when censoring a crafted DNS query. It afforded a rare insight into one of the Great Firewall's well-known network attacks, namely DNS injection, in terms of its internal architecture and the censor's operational behaviors. To understand the causes and implications of Wallbleed, we conducted longitudinal and Internet-wide measurements for over two years from October 2021. We(1) reverse-engineered the injector's parsing logic,(2) evaluated what information was leaked and how Internet users inside and outside of China were affected, and(3) monitored the censor's patching behaviors over time.We identified possible internal traffic of the censorship system, analyzed its memory management and load-balancing mechanisms, and observed process-level changes in an injector node. We employed a new side channel to distinguish the injector's multiple processes to assist our analysis.Our monitoring revealed that the censor coordinated an incorrect patch for Wallbleed in November 2023 and fully patched it in March 2024. Wallbleed exemplifies that the harm censorship middleboxes impose on Internet users is even beyond their obvious infringement of freedom of expression. When implemented poorly, it also imposes severe privacy and confidentiality risks to Internet users.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190010", "vector": [], "sparse_vector": [], "title": "Do We Really Need to Design New Byzantine-robust Aggregation Rules?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Liu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Federated learning (FL) allows multiple clients to collaboratively train a global machine learning model through a server, without exchanging their private training data. However, the decentralized aspect of FL makes it susceptible to poisoning attacks, where malicious clients can manipulate the global model by sending altered local model updates. To counter these attacks, a variety of aggregation rules designed to be resilient to Byzantine failures have been introduced. Nonetheless, these methods can still be vulnerable to sophisticated attacks or depend on unrealistic assumptions about the server. In this paper, we demonstrate that there is no need to design new Byzantine-robust aggregation rules; instead, FL can be secured by enhancing the robustness of well-established aggregation rules. To this end, we present FoundationFL, a novel defense mechanism against poisoning attacks. FoundationFL involves the server generating synthetic updates after receiving local model updates from clients. It then applies existing Byzantine-robust foundational aggregation rules, such as Trimmed-mean or Median, to combine clients' model updates with the synthetic ones. We theoretically establish the convergence performance of FoundationFL under Byzantine settings. Comprehensive experiments across several real-world datasets validate the efficiency of our FoundationFL method.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190011", "vector": [], "sparse_vector": [], "title": "ReDAN: An Empirical Study on Remote DoS Attacks against NAT Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we conduct an empirical study on remote DoS attacks targeting NAT networks (ReDAN, short for Remote DoS Attacks targeting NAT). We show that Internet attackers operating outside local NAT networks possess the capability to remotely identify a NAT device and subsequently terminate TCP connections initiated from the identified NAT device to external servers. Our attack involves two steps. First, we identify NAT devices on the Internet by exploiting inadequacies in the Path MTU Discovery (PMTUD) mechanism within NAT specifications. This deficiency creates a fundamental side channel that allows Internet attackers to distinguish if a public IPv4 address serves a NAT device or a separate IP host, aiding in the identification of target NAT devices. Second, we launch a remote DoS attack to terminate TCP connections on the identified NAT devices. While recent NAT implementations may include protective measures, such as packet legitimacy validation to prevent malicious manipulations on NAT mappings, we discover that these safeguards are not widely adopted in real world. Consequently, attackers can send crafted packets to deceive NAT devices into erroneously removing innocent TCP connection mappings, thereby disrupting the NATed clients to access remote TCP servers. Our experimental results reveal widespread security vulnerabilities in existing NAT devices. After testing 8 types of router firmware and 30 commercial NAT devices from 14 vendors, we identify vulnerabilities in 6 firmware types and 29 NAT devices that allow off-path removal of TCP connection mappings. Moreover, our measurements reveal a stark reality: 166 out of 180 (over 92%) tested real-world NAT networks, comprising 90 4G LTE/5G networks, 60 public Wi-Fi networks, and 30 cloud VPS networks, are susceptible to exploitation. We responsibly disclosed the vulnerabilities to affected vendors and received a significant number of acknowledgments. Finally, we propose our countermeasures against the identified DoS attack.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190012", "vector": [], "sparse_vector": [], "title": "Securing BGP ASAP: ASPA and other Post-ROV Defenses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Before the adoption of Route Origin Validation (ROV), prefix and subprefix hijacks were the most effective and common attacks on BGP routing. Recent works show that ROV adoption is increasing rapidly; with sufficient ROV adoption, prefix and subprefix attacks become ineffective.We study this changing landscape and in particular the Autonomous System Provider Authorization (ASPA) proposal,which focuses on route leakage but also foils some otherattacks. Using recent measurements of real-world ROV adoption, we evaluate its security impact. Our simulations show substantial impact: emph{already today}, prefix hijacks are less effective than forged-origin hijacks, and the effectiveness of subprefix hijacks is much reduced.Therefore, we expect attackers to move to forged-origin hijacks and other emph{post-ROV attacks}; we present a new, powerful post-ROV attack, emph{spoofing}. We present extensive evaluations of different post-ROV defenses and attacks. Our results show that ASPA significantly protects against post-ROV attacks, even in partial adoption. It dramatically improves upon the use of only ROV or of BGPsec, Path-End, OTC, and EdgeFilter. BGP-iSec has even better protection but requires public-key operations to export/import announcements. We also present ASPAwN, an extension that further improves ASPA's performance. Our results show that contrary to prior works [74], [95], ASPA is effective even when tier-1 ASes are not adopting, hence motivating ASPA adoption at edge and intermediate ASes.On the other hand, we find that againstemph{accidental} route leaks, the simpler, standardized OTC mechanism is as effective as ASPA.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190013", "vector": [], "sparse_vector": [], "title": "CounterSEVeillance: Performance-Counter Attacks on AMD SEV-SNP.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Confidential virtual machines (VMs) promise higher security by running the VM inside a trusted execution environment (TEE). Recent AMD server processors support confidential VMs with the SEV-SNP processor extension. SEV-SNP provides guarantees for integrity and confidentiality for confidential VMs despite running them in a shared hosting environment. In this paper, we introduce CounterSEVeillance, a new side-channel attack leaking secret-dependent control flow and operand properties from performance counter data. Our attack is the first to exploit performance counter side-channel leakage with single-instruction resolution from SEV-SNP VMs and works on fully patched systems. We systematically analyze performance counter events in SEV-SNP VMs and find that 228 are exposed to a potentially malicious hypervisor. CounterSEVeillance builds on this analysis and records performance counter traces with an instruction-level resolution by single-stepping the victim VM using APIC interrupts in combination with page faults. We match CounterSEVeillance traces against binaries, precisely recovering the outcome of any secret-dependent conditional branch and inferring operand properties. We present four attack case studies, in which we exemplarily showcase concrete exploitable leakage with 6 of the exposed performance counters. First, we use CounterSEVeillance to extract a full RSA-4096 key from a single Mbed TLS signature process in less than 8 minutes. Second, we present the first side-channel attack on TOTP verification running in an AMD SEV-SNP VM, recovering a 6-digit TOTP with only 31.1 guesses on average. Third, we show that CounterSEVeillance can leak the secret key from which the TOTPs are derived from the underlying base32 decoder. Fourth and finally, we show that CounterSEVeillance can also be used to construct a plaintext-checking oracle in a divide-and-surrender-style attack. We conclude that moving an entire VM into a setting with a privileged adversary increases the attack surface, given the vast amounts of code not vetted for this specific security setting.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190014", "vector": [], "sparse_vector": [], "title": "FUZZUER: Enabling Fuzzing of UEFI Interfaces on EDK-2.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unified Extensible Firmware Interface (UEFI) specification describes a platform-independent pre-boot interface for an Operating System (OS). EDK-2 Vulnerabilities in UEFI interface functions have severe consequences and can lead to Bootkits and other persistent malware resilient to OS reinstallations. However, there exist no vulnerability detection techniques for UEFI interfaces. We present FUZZUER, a feedback-guided fuzzing technique for UEFI interfaces on EDK-2, an exemplary and prevalently used UEFI implementation. We designed FIRNESS that utilizes static analysis techniques to automatically generate fuzzing harnesses for interface functions. We evaluated FUZZUER on the latest version of EDK-2. Our comprehensive evaluation on 150 interface functions demonstrates that FUZZUER with FIRNESS is an effective testing technique of EDK-2’s UEFI interface functions, greatly outperforming HBFA, an existing testing tool with manually written harnesses. We found 20 new security vulnerabilities, and most of these are already acknowledged by the developers.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190015", "vector": [], "sparse_vector": [], "title": "LLMPirate: LLMs for Black-box Hardware IP Piracy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Veera Vishwa A<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The rapid advancement of large language models (LLMs) has enabled the ability to effectively analyze and generate code nearly instantaneously, resulting in their widespread adoption in software development. Following this advancement, researchers and companies have also begun integrating LLMs across the hardware design and verification process. However, these highly potent LLMs can also induce new attack scenarios upon security vulnerabilities across the hardware development process. One such attack vector that has not been explored so far is intellectual property (IP) piracy. Given that this attack can manifest as rewriting hardware designs to evade piracy detection, it is essential to thoroughly evaluate LLM capabilities in performing this task and assess the mitigation abilities of current IP piracy detection tools. Therefore, in this work, we propose *LLMPirate*, the first LLM-based technique able to generate pirated variations of circuit designs that successfully evade detection across multiple state-of-the-art piracy detection tools. We devise three solutions to overcome challenges related to integration of LLMs for hardware circuit designs, scalability to large circuits, and effectiveness, resulting in an end-to-end automated, efficient, and practical formulation. We perform an extensive experimental evaluation of *LLMPirate* using eight LLMs of varying sizes and capabilities and assess their performance in pirating various circuit designs against four state-of-the-art, widely-used piracy detection tools. Our experiments demonstrate that *LLMPirate* is able to consistently evade detection on 100% of tested circuits across every detection tool. Additionally, we showcase the ramifications of *LLMPirate* using case studies on IBEX and MOR1KX processors and a GPS module, that we successfully pirate. We envision that our work motivates and fosters the development of better IP piracy detection tools.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190016", "vector": [], "sparse_vector": [], "title": "Safety Misalignment Against Large Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Ran", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The safety alignment of Large Language Models (LLMs) is crucial to prevent unsafe content that violates human values.To ensure this, it is essential to evaluate the robustness of their alignment against diverse malicious attacks.However, the lack of a large-scale, unified measurement framework hinders a comprehensive understanding of potential vulnerabilities.To fill this gap, this paper presents the first comprehensive evaluation of existing and newly proposed safety misalignment methods for LLMs. Specifically, we investigate four research questions: (1) evaluating the robustness of LLMs with different alignment strategies, (2) identifying the most effective misalignment method, (3) determining key factors that influence misalignment effectiveness, and (4) exploring various defenses.The safety misalignment attacks in our paper include system-prompt modification, model fine-tuning, and model editing.Our findings show that Supervised Fine-Tuning is the most potent attack but requires harmful model responses.In contrast, our novel Self-Supervised Representation Attack (SSRA) achieves significant misalignment without harmful responses.We also examine defensive mechanisms such as safety data filter, model detoxification, and our proposed Self-Supervised Representation Defense (SSRD), demonstrating that SSRD can effectively re-align the model.In conclusion, our unified safety alignment evaluation framework empirically highlights the fragility of the safety alignment of LLMs.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190017", "vector": [], "sparse_vector": [], "title": "Onion Franking: Abuse Reports for Mix-Based Private Messaging.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> E<PERSON>"], "summary": "The fast-paced development and deployment of private messaging applications demands mechanisms to protect against the concomitant potential for abuse. While widely used end-to-end encrypted (E2EE) messaging systems have deployed mechanisms for users to verifiably report abusive messages without compromising the privacy of unreported messages, abuse reporting schemes for systems that additionally protect message metadata are still in their infancy. Existing solutions either focus on a relatively small portion of the design space or incur much higher communication and computation costs than their E2EE brethren. This paper introduces new abuse reporting mechanisms that work for any private messaging system based on onion encryption. This includes low-latency systems that employ heuristic or opportunistic mixing of user traffic, as well as schemes based on mixnets. Along the way, we show that design decisions and abstractions that are well-suited to the E2EE setting may actually impede security and performance improvements in the metadata-hiding setting. We also explore stronger threat models for abuse reporting and moderation not explored in prior work, showing where prior work falls short and how to strengthen both our scheme and others'—including deployed E2EE messaging platforms—to achieve higher levels of security. We implement a prototype of our scheme and find that it outperforms the best known solutions in this setting by well over an order of magnitude for each step of the message delivery and reporting process, with overheads almost matching those of message franking techniques used by E2EE encrypted messaging apps today.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190018", "vector": [], "sparse_vector": [], "title": "Sheep&apos;s <PERSON><PERSON><PERSON>, <PERSON>&apos;s Data: Detecting Server-Induced Client Vulnerabilities in Windows Remote IPC.", "authors": ["Fangming Gu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hong Li", "<PERSON><PERSON><PERSON>"], "summary": "The Windows operating system employs various inter-process communication (IPC) mechanisms, typically involving a privileged server and a less privileged client. However, scenarios exist where the client has higher privileges, such as a performance monitor running as a domain controller obtaining data from a domain member via IPC. In these cases, the server can be compromised and send crafted data to the client.Despite the increase in Windows client applications, existing research has overlooked potential client-side vulnerabilities, which can be equally harmful. This paper introduces GLEIPNIR, the first vulnerability detection tool for Windows remote IPC clients. GLEIPNIR identifies client-side vulnerabilities by fuzzing IPC call return values and introduces a snapshot technology to enhance testing efficiency. Experiments on 76 client applications demonstrate that GLEIPNIR can identify 25 vulnerabilities within 7 days, resulting in 14 CVEs and a bounty of $36,000.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190019", "vector": [], "sparse_vector": [], "title": "EvoCrawl: Exploring Web Application Code and State using Evolutionary Search.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As more critical services move onto the web, it has become increasingly important to detect and address vulnerabilities in web applications. These vulnerabilities only occur under specific conditions: when 1) the vulnerable code is executed and 2) the web application is in the required state. If the application is not in the required state, then even if the vulnerable code is executed, the vulnerability may not be triggered. Previous work naively explores the application state by filling every field and triggering every JavaScript event before submitting HTML forms. However, this simplistic approach can fail to satisfy constraints between the web page elements, as well as input format constraints. To address this, we present EvoCrawl, a web crawler that uses evolutionary search to efficiently find different sequences of web interactions. EvoCrawl finds sequences that can successfully submit inputs to web applications and thus explore more code and server-side states than previous approaches. To assess the benefits of EvoCrawl we evaluate it against three state-of-the-art vulnerability scanners on ten web applications. We find that EvoCrawl achieves better code coverage due to its ability to execute code that can only be executed when the application is in a particular state. On average, EvoCrawl achieves a 59% increase in code coverage and successfully submits HTML forms 5x more frequently than the next best tool. By integrating IDOR and XSS vulnerability scanners, we used EvoCrawl to find eight zero-day IDOR and XSS vulnerabilities in WordPress, HotCRP, Kanboard, ImpressCMS, and GitLab.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190020", "vector": [], "sparse_vector": [], "title": "BULKHEAD: Secure, Scalable, and Efficient Kernel Compartmentalization with PKS.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Weiheng Bai", "Qingkai Zeng", "<PERSON><PERSON><PERSON>"], "summary": "The endless stream of vulnerabilities urgently calls for principled mitigation to confine the effect of exploitation. However, the monolithic architecture of commodity OS kernels, like the Linux kernel, allows an attacker to compromise the entire system by exploiting a vulnerability in any kernel component. Kernel compartmentalization is a promising approach that follows the least-privilege principle. However, existing mechanisms struggle with the trade-off on security, scalability, and performance, given the challenges stemming from mutual untrustworthiness among numerous and complex components. In this paper, we present BULKHEAD, a secure, scalable, and efficient kernel compartmentalization technique that offers bi-directional isolation for unlimited compartments. It leverages Intel's new hardware feature PKS to isolate data and code into mutually untrusted compartments and benefits from its fast compartment switching. With untrust in mind, BULKHEAD introduces a lightweight in-kernel monitor that enforces multiple important security invariants, including data integrity, execute-only memory, and compartment interface integrity. In addition, it provides a locality-aware two-level scheme that scales to unlimited compartments. We implement a prototype system on Linux v6.1 to compartmentalize loadable kernel modules (LKMs). Extensive evaluation confirms the effectiveness of our approach. As the system-wide impacts, BULKHEAD incurs an average performance overhead of 2.44% for real-world applications with 160 compartmentalized LKMs. While focusing on a specific compartment, ApacheBench tests on ipv6 show an overhead of less than 2%. Moreover, the performance is almost unaffected by the number of compartments, which makes it highly scalable.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190021", "vector": [], "sparse_vector": [], "title": "The (Un)usual Suspects - Studying Reasons for Lacking Updates in WordPress.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The widespread use of Content Management Systems (CMS) like WordPress has made these systems attractive targets for adversaries, with the vulnerabilities in the code posing serious risks. Despite being the most effective way to reduce these risks, more than half of all CMS installations lack the latest security patches. Researchers have tried to notify website operators about vulnerabilities using vulnerability notifications, which often exhibit limited impact. In this paper, we use the Grounded Theory approach to investigate the reasons why website owners do not update their CMS. To gain a holistic view on lacking update behavior, we interviewed website owners with outdated WordPress-based systems as well as individuals involved in website creation and hosting. On the one hand, we could confirm issues known from other ecosystems, such as lack of risk awareness, perceived risks of updates, and update costs, as factors for lacking CMS updates. More importantly, our study identified factors that have not been explicitly addressed in the general updating behaviour and vulnerability notification literature: (1) the subjective value of a website to its owner and (2) the delegation of website operations, which influence updating behavior far more decisively. Furthermore, we showed that website owners perceive a potential compromise of their CMS only as a risk to themselves and not as a threat to the wider online community. These findings that we present as four non-update scenarios may partly explain the limited success of previous efforts to notify operators about vulnerabilities in their systems. Our study not only offers valuable insights for future research, testing the effectiveness of vulnerability notifications and studying updating behavior in general, but it also proposes practical suggestions on how to reduce the number of outdated systems on the web.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190022", "vector": [], "sparse_vector": [], "title": "Mens Sana In Corpore Sano: Sound Firmware Corpora for Vulnerability Research.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Firmware corpora for vulnerability research should be textit{scientifically sound}. Yet, several practical challenges complicate the creation of sound corpora: Sample acquisition, e.g., is hard and one must overcome the barrier of proprietary or encrypted data. As image contents are unknown prior analysis, it is hard to select textit{high-quality} samples that can satisfy scientific demands.Ideally, we help each other out by sharing data. But here, sharing is problematic due to copyright laws. Instead, papers must carefully document each step of corpus creation: If a step is unclear, replicability is jeopardized. This has cascading effects on result verifiability, representativeness, and, thus, soundness. Despite all challenges, how can we maintain the soundness of firmware corpora? This paper thoroughly analyzes the problem space and investigates its impact on research: We distill practical binary analysis challenges that significantly influence corpus creation. We use these insights to derive guidelines that help researchers to nurture corpus replicability and representativeness. We apply them to 44 top tier papers and systematically analyze scientific corpus creation practices. Our comprehensive analysis confirms that there is currently no common ground in related work. It shows the added value of our guidelines, as they discover methodical issues in corpus creation and unveil miniscule step stones in documentation. These blur visions on representativeness, hinder replicability, and, thus, negatively impact the soundness of otherwise excellent work. Finally, we show the feasibility of our guidelines and build a new corpus for large-scale analyses on Linux firmware: LFwC. We share rich meta data for good (and proven) replicability. We verify unpacking, deduplicate, identify contents, provide ground truth, and demonstrate LFwC's utility for research.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190023", "vector": [], "sparse_vector": [], "title": "Attributing Open-Source Contributions is Critical but Difficult: A Systematic Analysis of GitHub Practices and Their Impact on Software Supply Chain Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Dominik <PERSON>rmke", "<PERSON><PERSON><PERSON>"], "summary": "Critical open-source projects form the basis of many large software systems. They provide trusted and extensible implementations of important functionality for cryptography, compatibility, and security. Verifying commit authorship authenticity in open-source projects is essential and challenging. Git users can freely configure author details such as names and email addresses. Platforms like GitHub use such information to generate profile links to user accounts. We demonstrate three attack scenarios malicious actors can use to manipulate projects and profiles on GitHub to appear trustworthy. We designed a mixed-research study to assess the effect on critical open-source software projects and evaluated countermeasures. First, we conducted a large-scale measurement among 50,328 critical open-source projects on GitHub and demonstrated that contribution workflows can be abused in 85.9% of the projects. We identified 573,043 email addresses that a malicious actor can claim to hijack historic contributions and improve the trustworthiness of their accounts. When looking at commit signing as a countermeasure, we found that the majority of users (95.4%) never signed a commit, and for the majority of projects (72.1%), no commit was ever signed. In contrast, only 2.0% of the users signed all their commits, and for 0.2% of the projects all commits were signed. Commit signing is not associated with projects’ programming languages, topics, or other security measures. Second, we analyzed online security advice to explore the awareness of contributor spoofing and identify recommended countermeasures. Most documents exhibit awareness of the simple spoofing technique via Git commits but no awareness of problems with GitHub’s handling of email addresses.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190024", "vector": [], "sparse_vector": [], "title": "Exploring User Perceptions of Security Auditing in the Web3 Ecosystem.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the rapidly evolving Web3 ecosystem, transparent auditing has emerged as a critical component for both applications and users. However, there is a significant gap in understanding how users perceive this new form of auditing and its implications for Web3 security. Utilizing a mixed-methods approach that incorporates a case study, user interviews, and social media data analysis, our study leverages a risk perception model to comprehensively explore Web3 users' perceptions regarding information accessibility, the role of auditing, and its influence on user behavior. Based on these extensive findings, we discuss how this open form of auditing is shaping the security of the Web3 ecosystem, identifying current challenges, and providing design implications.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190025", "vector": [], "sparse_vector": [], "title": "DLBox: New Model Training Framework for Protecting Training Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> Lee"], "summary": "Sharing training data for deep learning raises critical concerns about data leakage, as third-party AI developers take full control over the data once it is handed over to them.  The problem becomes even worse if the model trained using the data should be returned to the third-party AI developers - e.g., healthcare startup training its own model using the medical data rented from a hospital.  In this case, the malicious developers can easily leak the training data through the model as he can construct an arbitrary data flow between them - e.g., directly encoding raw training data into the model, or stealthily biasing the model to resemble the training data.  However, current model training frameworks do not provide any protection to prevent such training data leakage, allowing the untrusted AI developers to leak the data without any restriction. This paper proposes DLBox, a new model training framework to minimize the attack vectors raised by untrusted AI developers.  Since it is infeasible to completely prevent data leakage through the model, the goal of DLBox is to allow only a benign model training such that the data leakage through invalid paths are minimized.  The key insight of DLBox is that the model training is a statistical process of learning common patterns from a dataset.  Based on it, DLBox defines DGM-Rules, which determine whether a model training code from a developer is benign or not.  Then, DLBox leverages confidential computing to redesign current model training framework, enforcing only DGM-Rules-based training.  Therefore, untrusted AI developers are strictly limited to obtain only the benignly trained model, prohibited from intentionally leaking the data. We implemented the prototype of DLBox on PyTorch with AMD SEV-SNP, and demonstrated that DLBox eliminates large attack vectors by preventing previous attacks (e.g., data encoding, and gradient inversion) while imposing minimal performance overhead.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190026", "vector": [], "sparse_vector": [], "title": "PowerRadio: Manipulate Sensor Measurement via Power GND Radiation.", "authors": ["Yan <PERSON>", "Xiaoyu Ji", "Yancheng Jiang", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Xu"], "summary": "Sensors are key components to enable various applications, e.g., home intrusion detection, and environment monitoring. While various software defenses and physical protections are used to prevent sensor manipulation, this paper introduces a new threat vector, PowerRadio, which can bypass existing protections and change the sensor readings at a distance. PowerRadio leverages interconnected ground (GND) wires, a standard practice for electrical safety at home, to inject malicious signals. The injected signal is coupled by the sensor's analog measurement wire and eventually, it survives the noise filters, inducing incorrect measurement. We present three methods that can manipulate sensors by inducing static bias, periodical signals, or pulses. For instance, we show adding stripes into the captured images of a surveillance camera or injecting inaudible voice commands into conference microphones. We study the underlying principles of PowerRadio and find its root causes: (1) the lack of shielding between ground and data signal wires and (2) the asymmetry of circuit impedance that enables interference to bypass filtering. We validate PowerRadio against a surveillance system, broadcast system, and various sensors. We believe that PowerRadio represents an emerging threat that exhibits the pros of both radiated and conducted EMI, e.g., expanding the effective attack distance of radiated EMI yet eliminating the requirement of line-of-sight or approaching physically. Our insights shall provide guidance for enhancing the sensors' security and power wiring during the design phases.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190027", "vector": [], "sparse_vector": [], "title": "Beyond Classification: Inferring Function Names in Stripped Binaries via Domain Adapted LLMs.", "authors": ["Lin<PERSON>", "Xi<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Function name inference in stripped binaries is an important yet challenging task for many security applications, such as malware analysis and vulnerability discovery, due to the need to grasp binary code semantics amidst diverse instruction sets, architectures, compiler optimizations, and obfuscations. While machine learning has made significant progress in this field, existing methods often struggle with unseen data, constrained by their reliance on a limited vocabulary-based classification approach. In this paper, we present SymGen, a novel framework employing an autoregressive generation paradigm powered by domain-adapted generative large language models (LLMs) for enhanced binary code interpretation. We have evaluated SymGen on a dataset comprising 2,237,915 binary functions across four architectures (x86-64, x86-32, ARM, MIPS) with four levels of optimizations (O0-O3) where it surpasses the state-of-the-art with up to 409.3%, 553.5%, and 489.4% advancement in precision, recall, and F1 score, respectively, showing superior effectiveness and generalizability. Our ablation and case studies also demonstrate the significant performance boosts achieved by our design, e.g., the domain adaptation approach, alongside showcasing SymGen’s practicality in analyzing real-world binaries, e.g., obfuscated binaries and malware executables.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190028", "vector": [], "sparse_vector": [], "title": "PhantomLiDAR: Cross-modality Signal Injection Attacks against LiDAR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Xiaoyu Ji", "<PERSON><PERSON> Xu"], "summary": "LiDAR (Light Detection and Ranging) is a pivotal sensor for autonomous driving, offering precise 3D spatial information.Previous signal attacks against LiDAR systems mainly exploit laser signals. In this paper, we investigate the possibility of cross-modality signal injection attacks, i.e., injecting intentional electromagnetic interference (IEMI) to manipulate LiDAR output. Our insight is that the internal modules of a LiDAR, i.e., the laser receiving circuit, the monitoring sensors, and the beam-steering modules, even with strict electromagnetic compatibility (EMC) testing, can still couple with the IEMI attack signals and result in the malfunction of LiDAR systems. Based on the above attack surfaces, we propose the alias attack, which manipulates LiDAR output in terms of textit{Points Interference}, textit{Points Injection}, textit{Points Removal}, and even textit{LiDAR Power-Off}.We evaluate and demonstrate the effectiveness of alias with both simulated and real-world experiments on five COTS LiDAR systems.We also conduct feasibility experiments in real-world moving scenarios.We provide potential defense measures that can be implemented at both the sensor level and the vehicle system level to mitigate the risks associated with IEMI attacks. Video demonstrations can be viewed at textcolor{blue}{href{https://sites.google.com/view/phantomlidar}{https://sites.google.com/view/phantomlidar}}.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190029", "vector": [], "sparse_vector": [], "title": "Repurposing Neural Networks for Efficient Cryptographic Computation.", "authors": ["Xi<PERSON>", "Shiqing Ma", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "While neural networks (NNs) are traditionally associated with tasks such as image recognition and natural language processing, this paper presents a novel application of NNs for efficient cryptographic computations. Leveraging the Turing completeness and inherent adaptability of NN models, we propose a transformative approach that efficiently accelerates cryptographic computations on various platforms. More specifically, with a program translation framework that converts traditional cryptographic algorithms into NN models, our proof-of-concept implementations in TensorFlow demonstrate substantial performance improvements: encryption speeds for AES, Chacha20, and Salsa20 show increases of up to 4.09$times$, 5.44$times$, and 5.06$times$, respectively, compared to existing GPU-based cryptographic solutions written by human experts. These enhancements are achieved without compromising the security of the original cryptographic algorithms, ensuring that our neural network-based approach maintains robust security standards. This repurposing of NNs opens new pathways for the development of scalable, efficient, and secure cryptographic systems that can adapt to the evolvingdemands of modern computing environments.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190030", "vector": [], "sparse_vector": [], "title": "Secret Spilling Drive: Leaking User Behavior through SSD Contention.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Covert channels and side channels bypass architectural security boundaries. Numerous works have studied covert channels and side channels in software and hardware. Thus, research on covert-channel and side-channel mitigations relies on the discovery of leaky hardware and software components. In this paper, we perform the first study of timing channels inside modern commodity off-the-shelf SSDs. We systematically analyze the behavior of NVMe PCIe SSDs with concurrent workloads. We observe that exceeding the maximum I/O operations of the SSD leads to significant latency spikes. We narrow down the number of I/O operations required to still induce latency spikes on 12 different SSDs. Our results show that a victim process needs to read at least 8 to 128 blocks to be still detectable by an attacker. Based on these experiments, we show that an attacker can build a covert channel, where the sender encodes secret bits into read accesses to unrelated blocks, inaccessible to the receiver. We demonstrate that this covert channel works across different systems and different SSDs, even from processes running inside a virtual machine. Our unprivileged SSD covert channel achieves a true capacity of up to 1503 bit/s while it works across virtual machines (cross-VM) and is agnostic to operating system versions, as well as other hardware characteristics such as CPU or DRAM. Given the coarse granularity of the SSD timing channel, we evaluate it as a side channel in an open-world website fingerprinting attack over the top 100 websites. We achieve an F1 score of up to 97.0. This shows that the leakage goes beyond covert communication and can leak highly sensitive information from victim users. Finally, we discuss the root cause of the SSD timing channel and how it can be mitigated.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190031", "vector": [], "sparse_vector": [], "title": "Moneta: Ex-Vivo GPU Driver Fuzzing by Recalling In-Vivo Execution States.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dokyung Song"], "summary": "Graphics Processing Units (GPUs) have become an indispensable part of modern computing infrastructure. They can execute massively parallel tasks on large data sets and have rich user space-accessible APIs for 3D rendering and general-purpose parallel programming. Unfortunately, the GPU drivers that bridge the gap between these APIs and the underlying hardware have grown increasingly large and complex over the years. Many GPU drivers now expose broad attack surfaces and pose serious security risks. Fuzzing is a proven automated testing method that mitigates these risks by identifying potential vulnerabilities. However, when applied to GPU drivers, existing fuzzers incur high costs and scale poorly because they rely on physical GPUs. Furthermore, they achieve limited effectiveness because they often fail to meet dependency and timing constraints while generating and executing input events. We present Moneta, a new ex-vivo approach to driver fuzzing that can statefully and effectively fuzz GPU drivers at scale. The key idea is (i) to recall past, in-vivo GPU driver execution states by synergistically combining snapshot-and-rehost and record-and-replay along with our proposed suite of GPU stack virtualization and introspection techniques, and (ii) to start parallel and stateful ex-vivo GPU driver fuzzing from the recalled states. We implemented a prototype of Moneta and evaluated it on three mainstream GPU drivers. Our prototype triggered deep, live GPU driver states during fuzzing, and found five previously unknown bugs in the NVIDIA GPU driver, three in the AMD Radeon GPU driver, and two in the ARM Mali GPU driver. These ten bugs were all confirmed by the respective vendors in response to our responsible disclosure, and five new CVEs were assigned.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190032", "vector": [], "sparse_vector": [], "title": "You Can Rand but You Can&apos;t Hide: A Holistic Security Analysis of Google Fuchsia&apos;s (and gVisor&apos;s) Network Stack.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This research is the first holistic analysis of the algorithmic security of the Google Fuchsia/gVisor network stack. Google Fuchsia is a new operating system developed by Google in a \"clean slate\" fashion. It is conjectured to eventually replace Android as an operating system for smartphones, tablets, and IoT devices. Fuchsia is already running in millions of Google Nest Hub consumer products. Google gVisor is an application kernel used by Google's App Engine, Cloud Functions, Cloud ML Engine, Cloud Run, and Google KubernetesEngine (GKE). Google Fuchsia uses the gVisor network stack code for its TCP/IP implementation. We report multiple vulnerabilities in the algorithms used by Fuchsia/gVisor to populate network protocol header fields, specifically the TCP initial sequence number, TCP timestamp, TCP and UDP source ports, and IPv4/IPv6 fragment ID fields. In our holistic analysis, we show how a combination of multiple attacks results in the exposure of a PRNG seed and a hashing key used to generate the above fields. This enables an attacker to predict future values of the fields, which facilitates several network attacks. Our work focuses on web-based device tracking based on the stability and relative uniqueness of the PRNG seed and the hashing key. We demonstrate our device tracking techniques over the Internet with browsers running on multiple Fuchsia devices, in multiple browser modes (regular/privacy), and over multiple networks (including IPv4 vs. IPv6). Our tests verify that device tracking for <PERSON>chs<PERSON> is practical and yields a reliable device ID. We conclude with recommendations on mitigating the attacks and their root causes. We reported our findings to Google, which issued CVEs and patches for the security vulnerabilities we disclosed.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190033", "vector": [], "sparse_vector": [], "title": "SHAFT: Secure, Handy, Accurate and Fast Transformer Inference.", "authors": ["Andes Y. L. Kei", "Sherman S. M. Chow"], "summary": "Adoption of transformer-based machine learning models is growing, raising concerns about sensitive data exposure. Nonetheless, current secure inference solutions incur substantial overhead due to their extensive reliance on non-linear protocols, such as softmax and Gaussian error linear unit (GELU). Driven by numerical stability needs, softmax approximations (e.g., NeurIPS 2021) typically extract the maximum element of an input vector, incurring logarithmic rounds (in the input length). Existing GELU protocols (e.g., S&P 2024) use piecewise approximations with high-degree polynomials that rely heavily on secure multiplications and comparisons, which are expensive. Such complexities also hinder model owners who are not familiar with cryptography from easily deploying their custom models. SHAFT, our proposed system, provides a secure, handy, accurate, and fast transformer inference framework for deployment. Highlights of our contributions include 1) the first constant-round softmax protocol for transformers, uniquely combining the benefits of input clipping and characteristics of ordinary differential equations, and 2) a highly accurate GELU protocol on a novel characterization designed for Fourier series approximation. Extending to broader contexts, our new protocols also apply to general neural networks using softmax as the final layer and to transformer architectures with different activation functions. Remarkably, SHAFT outperforms state-of-the-art SIGMA (PETS 2024), based on secret sharing, and BumbleBee (NDSS 2025), which additionally uses RLWE-based homomorphic encryption. More specifically, SHAFT minimizes communication by 25-41%. and matches SIGMA's running time while surpassing BumbleBee in running time by 4.6-5.3× on LANs and 2.9-4.4× on WANs. Alongside these improvements, SHAFT attains accuracy comparable to plaintext, confirming its numerical stability and accuracy. Next in this progression, SHAFT provides an accessible open-source framework for secure and handy deployment by smoothly integrating with the Hugging Face library (EMNLP Demos 2020).", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190034", "vector": [], "sparse_vector": [], "title": "Do (Not) Follow the White Rabbit: Challenging the Myth of Harmless Open Redirection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Open redirects are one of the oldest threats to web applications, allowing attackers to reroute users to malicious websites by exploiting a web application's redirection mechanism. The recent shift towards client-side task offloading has introduced JavaScript-based redirections, formerly handled server-side, thereby posing additional security risks to open redirections. In this paper, we re-assess the significance of open redirect vulnerabilities by focusing on client-side redirections, which despite their importance, have been largely understudied by the community due to open redirect's long-standing low impact. To address this gap, we introduce a static-dynamic system, STORK, designed to extract vulnerability indicators for open redirects. Applying STORK to the Tranco top 10K sites, we conduct a large-scale measurement, uncovering 20.8K open redirect vulnerabilities across 623 sites and compiling a catalog of 184 vulnerability indicators. Afterwards, we use our indicators to mine vulnerabilities from snapshots of live webpages, Google search and Internet Archive, identifying additionally 326 vulnerable sites, including Google WebLight and DoubleClick. Then, we explore the extent to which their exploitation can lead to more critical threats, quantifying the impact of client-side open redirections in the wild. Our study finds that over 11.5% of the open redirect vulnerabilities across 38% of the affected sites could be escalated to XSS, CSRF and information leakage, including popular sites like Adobe, WebNovel, TP-Link, and UDN, which is alarming. Finally, we review and evaluate the adoption of mitigation techniques against open redirections.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190035", "vector": [], "sparse_vector": [], "title": "Secure Data Analytics in Apache Spark with Fine-grained Policy Enforcement and Isolated Execution.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> Lee"], "summary": "Cloud based Spark platform is a tempting approach for sharing data, as it allows data users to easily analyze the data while the owners to efficiently share the large volume of data. However, the absence of a robust policy enforcement mechanism on Spark hinders the data owners from sharing their data due to the risk of private data breach. In this respect, we found that malicious data users and cloud managers can easily leak the data by constructing a policy violating physical plan, compromising the Spark libraries, or even compromising the Spark cluster itself. Nonetheless, current approaches fail to securely and generally enforce the policies on Spark, as they do not check the policies on physical plan level, and they do not protect the integrity of data analysis pipeline. This paper presents Laputa, a secure policy enforcement framework on Spark. Specifically, Laputa designs a pattern matching based policy checking on the physical plans, which is generally applicable to Spark applications with more fine-grained policies. Then, Laputa compartmentalizes Spark applications based on confidential computing, by which the entire data analysis pipeline is protected from the malicious data users and cloud managers. Meanwhile, Laputa preserves the usability as the data users can run their Spark applications on Laputa with minimal modification. We implemented Laputa, and evaluated its security and performance aspects on TPC-H, Big Data benchmarks, and real world applications using ML models. The evaluation results demonstrated that Laputa correctly blocks malicious Spark applications while imposing moderate performance overheads.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190036", "vector": [], "sparse_vector": [], "title": "SketchFeature: High-Quality Per-Flow Feature Extractor Towards Security-Aware Data Plane.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Intelligent Network Data Plane (INDP) is emerging as a promising direction for in-network security due to the advancement of machine learning technologies and the importance of fast mitigation of attacks. However, the feature extraction function still poses various challenges due to multiple hardware constraints in the data plane, especially for the advanced per-flow 3rd-order features (e.g., inter-packet delay and packet size distributions) preferred by recent security applications. In this paper, we discover novel attack surfaces of state-of-the-art data plane feature extractors that had to accommodate the hardware constraints, allowing adversaries to evade the entire attack detection loop of in-network intrusion detection systems. To eliminate the attack surfaces fundamentally, we pursue an evolution of a probabilistic (sketch) approach to enable flawless 3rd-order feature extraction, highlighting High-resolution, All-flow, and Full-range (HAF) 3rd-order feature measurement capacity. To our best knowledge, the proposed scheme, namely SketchFeature, is the first sketch-based 3rd-order feature extractor fully deployable in the data plane. Through extensive analyses, we confirmed the robust performance of SketchFeature theoretically and experimentally. Furthermore, we ran various security use cases, namely covert channel, botnet, and DDoS detections, with SketchFeature as a feature extractor, and achieved near-optimal attack detection performance.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190037", "vector": [], "sparse_vector": [], "title": "Vulnerability, Where Art Thou? An Investigation of Vulnerability Management in Android Smartphone Chipsets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Vulnerabilities in Android smartphone chipsets have severe consequences, as recent real-world attacks have demonstrated that adversaries can leverage vulnerabilities to execute arbitrary code or exfiltrate confidential information. Despite the far-reaching impact of such attacks, the lifecycle of chipset vulnerabilities has yet to be investigated, with existing papers primarily investigating vulnerabilities in the Android operating system. This paper provides a comprehensive and empirical study of the current state of smartphone chipset vulnerability management within the Android ecosystem. For the first time, we create a unified knowledge base of 3,676 chipset vulnerabilities affecting 437 chipset models from all four major chipset manufacturers, combined with 6,866 smartphone models. Our analysis revealed that the same vulnerabilities are often included in multiple generations of chipsets, providing novel empirical evidence that vulnerabilities are inherited through multiple chipset generations. Furthermore, we demonstrate that the commonly accepted 90-day responsible vulnerability disclosure period is seldom adhered to. We find that a single vulnerability often affects hundreds to thousands of different smartphone models, for which update availability is, as we show, often unclear or heavily delayed. Leveraging the new insights gained from our empirical analysis, we recommend several changes that chipset manufacturers can implement to improve the security posture of their products. At the same time, our knowledge base enables academic researchers to conduct more representative evaluations of smartphone chipsets, accurately assess the impact of vulnerabilities they discover, and identify avenues for future research.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190038", "vector": [], "sparse_vector": [], "title": "TZ-DATASHIELD: Automated Data Protection for Embedded Systems via Data-Flow-Based Compartmentalization.", "authors": ["Zelun Kong", "Minkyung Park", "Le Guan", "<PERSON><PERSON>", "<PERSON>"], "summary": "As reliance on embedded systems grows in critical domains such as healthcare, industrial automation, and unmanned vehicles, securing the data on micro-controller units (MCUs) becomes increasingly crucial. These systems face significant challenges related to computational power and energy constraints, complicating efforts to maintain the confidentiality and integrity of sensitive data. Previous methods have utilized compartmentalization techniques to protect this sensitive data, yet they remain vulnerable to breaches by strong adversaries exploiting privileged software. In this paper, we introduce TZ-DATASHIELD, a novel LLVM compiler tool that enhances ARM TrustZone with sensitive data flow (SDF) compartmentalization, offering robust protection against strong adversaries in MCU-based systems. We address three primary challenges: the limitations of existing compartment units, inadequate isolation within the Trusted Execution Environment (TEE), and the exposure of shared data to potential attacks. TZ-DATASHIELD addresses these challenges by implementing a fine-grained compartmentalization approach that focuses on sensitive data flow, ensuring data confidentiality and integrity, and developing a novel intra-TEE isolation mechanism that validates compartment access to TEE resources at runtime. Our prototype enables firmware developers to annotate source code to generate TrustZone-ready firmware images automatically. Our evaluation using real-world MCU applications demonstrates that TZ-DATASHIELD achieves up to 80.8% compartment memory and 88.6% ROP gadget reductions within the TEE address space. It incurs an average runtime overhead of 14.7% with CFI and DFI enforcement, and 7.6% without these measures.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190039", "vector": [], "sparse_vector": [], "title": "Ctrl+Alt+Deceive: Quantifying User Exposure to Online Scams.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Online scams have become a top threat for Internet users, inflicting $10 billion in losses in 2023 only in the US. Prior work has studied specific scam types, but no work has compared different scam types. In this work, we perform what we believe is the first study of the exposure of end users to different types of online scams. We examine seven popular scam types: shopping, financial, cryptocurrency, gambling, dating, funds recovery, and employment scams. To quantify end-user exposure, we search for observations of 607K scam domains over a period of several months by millions of desktop and mobile devices belonging to customers of a large cybersecurity vendor. We classify the scam domains into the seven scam types and measure for each scam type the exposure of end users, geographical variations, scam domain lifetime, and the promotion of scam websites through online advertisements. We examine 25.1M IP addresses accessing over 414K scam domains. On a daily basis, 149K devices are exposed to online scams, with an average of 101K (0.8%) of desktop devices being exposed compared to 48K (0.3%) of mobile devices. Shopping scams are the most prevalent scam type, being observed by a total of 10.2M IPs, followed by cryptocurrency scams, observed by 653K IPs. After being observed in the telemetry, the scam domains remain alive for a median of 11 days. In at least 9.2M (13.3%) of all scam observations users followed an advertisement. These ads are largely (59%) hosted on social media, with Facebook being the preferred source.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190040", "vector": [], "sparse_vector": [], "title": "VoiceRadar: Voice Deepfake Detection using Micro-Frequency and Compositional Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent advancements in synthetic speech generation, including text-to-speech (TTS) and voice conversion (VC) models, allow the generation of convincing synthetic voices, often referred to as audio deepfakes. These deepfakes pose a growing threat as adversaries can use them to impersonate individuals, particularly prominent figures, on social media or bypass voice authentication systems, thus having a broad societal impact. The inability of state-of-the-art verification systems to detect voice deepfakes effectively is alarming.We propose a novel audio deepfake detection method, VoiceRadar, that augments machine learning with physical models to approximate frequency dynamics and oscillations in audio samples. This significantly enhances detection capabilities. VoiceRadar leverages two main physical models: (i) the Doppler effect to understand frequency changes in audio samples and (ii) drumhead vibrations to decompose complex audio signals into component frequencies. VoiceRadar identifies subtle variations, or micro-frequencies, in the audio signals by applying these models. These micro-frequencies are aggregated to compute the observed frequency, capturing the unique signature of the audio. This observed frequency is integrated into the machine learning algorithm’s loss function, enabling the algorithm to recognize distinct patterns that differentiate human-produced audio from AI-generated audio.We constructed a new diverse dataset to comprehensively evaluate VoiceRadar, featuring samples from leading TTS and VC models. Our results demonstrate that VoiceRadar outperforms existing methods in accurately identifying AI-generated audio samples, showcasing its potential as a robust tool for audio deepfake detection.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190041", "vector": [], "sparse_vector": [], "title": "Balancing Privacy and Data Utilization: A Comparative Vignette Study on User Acceptance of Data Trustees in Germany and the US.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In times of big data, connected devices, and increasing self-measurement, protecting consumer privacy remains a challenge despite ongoing technological and legislative efforts. Data trustees present a promising solution, aiming to balance data utilization with privacy concerns by facilitating secure data sharing and ensuring individual control. However, successful implementation hinges on user acceptance and trust. We conducted a large-scale, vignette-based, census-representative online study examining factors influencing the acceptance of data trustees for medical, automotive, IoT, and online data. With n=714 participants from Germany and n=1036 from the US, our study reveals varied willingness to use data trustees across both countries, with notable skepticism and outright rejection from a significant portion of users. We also identified significant domain-specific differences, including the influence of user anonymity, perceived personal and societal benefits, and the recipients of the data. Contrary to common beliefs, organizational and regulatory decisions such as the storage location, the operator, and supervision appeared less relevant to users' decisions. In conclusion, while there exists a potential user base for data trustees, achieving widespread acceptance will require explicit and targeted implementation strategies tailored to address diverse user expectations. Our findings underscore the importance of understanding these nuances for effectively deploying data trustee frameworks that meet both regulatory requirements and user preferences while upholding highest security and privacy standards.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190042", "vector": [], "sparse_vector": [], "title": "ScopeVerif: Analyzing the Security of Android&apos;s Scoped Storage via Differential Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Storage on Android has evolved significantly over the years, with each new Android version introducing changes aimed at enhancing usability, security, and privacy. While these updates typically help with restricting app access to storage through various mechanisms, they may occasionally introduce new complexities and vulnerabilities. A prime example is the introduction of scoped storage in Android 10, which fundamentally changed how apps interact with files. While intended to enhance user privacy by limiting broad access to shared storage, scoped storage has also presented developers with new challenges and potential vulnerabilities to address. However, despite its significance for user privacy and app functionality, no systematic studies have been performed to study Android's scoped storage at depth from a security perspective. In this paper, we present the first systematic security analysis of the scoped storage mechanism. To this end, we design and implement a testing tool, named ScopeVerif, that relies on differential analysis to uncover security issues and implementation inconsistencies in Android's storage. Specifically, ScopeVerif takes a list of security properties and checks if there are any file operations that violate any security properties defined in the official Android documentation. Additionally, we conduct a comprehensive analysis across different Android versions as well as a cross-OEM analysis to identify discrepancies in different implementations and their security implications. Our study identifies both known and unknown issues of scoped storage. Our cross-version analysis highlights undocumented changes as well as partially fixed security loopholes across versions. Additionally, we discovered several vulnerabilities in scoped storage implementations by different OEMs. These vulnerabilities stem from deviations from the documented and correct behavior, which potentially poses security risks. The affected OEMs and Google have acknowledged our findings and offered us bug bounties in response.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190043", "vector": [], "sparse_vector": [], "title": "TWINFUZZ: Differential Testing of Video Hardware Acceleration Stacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Video hardware acceleration stacks, which include multiple complex layers that interact with software and hardware components, are designed to increase the efficiency and performance of demanding tasks such as video decoding, encoding, and transformation. Their implementation raises security concerns due to the lack of operational transparency. The complexity of their multi-layered architecture makes automated testing difficult, especially due to the lack of observability in post-silicon testing. In particular, the tests must consider five different layers, including all interoperation components: the applications, the drivers supporting the user space, the kernel, the firmware of the acceleration peripherals, and the hardware itself. The introspectability and visibility of each layer gradually decrease deeper along the stack. In this paper, we introduce our harness design and testing technique based on differential testing of hardware-accelerated video decoding stacks through an indirect proxy target. Our key insight is that we can use a white-box software implementation’s code coverage as an indirect software proxy to guide the fuzzing of the unobservable black-box hardware acceleration stack under test. We develop a differential oracle to compare software and hardware-accelerated outputs, identifying observable differences in video decoding to indirectly guide and explorethe hardware-accelerated stack’s black-box components. We also present a prototypical implementation of our approach in a tool called TWINFUZZ. Our prototype implementation focuses on video processing and demonstrates our method’s effectiveness in identifying implementation discrepancies and security vulnerabilities across seven bug classes for four different acceleration frameworks. More specifically, we discovered and responsibly disclosed two security vulnerabilities in the application layer and three in the driver layer. We also identified 15 clusters of inputs that trigger observable differences in the four platforms tested, which could be used for fingerprinting hardware-accelerated and software stacks from the device or web browser. On top of that, we identified vulnerabilities in Firefox and VLC media player, leveraging input replay. Our results highlight the need for robust testing mechanisms for secure and correct hardware acceleration implementations and underscore the importance of better fault localization in differential fuzzing.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190044", "vector": [], "sparse_vector": [], "title": "VulShield: Protecting Vulnerable Code Before Deploying Patches.", "authors": ["Yuan Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chenyang Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite the high frequency of vulnerabilities exposed in software, patching these vulnerabilities remains slow and challenging, which leaves a potential attack window. To mitigate this threat, researchers seek temporary solutions to prevent vulnerabilities from being exploited or triggered before they are officially patched. However, prior approaches have limited protection scope, often require code modification of the target vulnerable programs, and rely on recent system features. These limitations significantly reduce their usability and practicality. In this work, we introduce VulShield, an automated temporary protection system that addresses these limitations. VulShield leverages sanitizer reports, and automatically generates security policies that describe the vulnerability triggering conditions. The policies are then enforced through a Linux kernel module that can efficiently detect and prevent vulnerability from being triggered or exploited at runtime. By carefully designing the kernel module, VulShield is capable of protecting both vulnerable kernels and user-space programs running on them. It does not rely on recent system features like eBPF and Linux security modules. VulShield is also pluggable and non-invasive as it does not need to modify the code of target vulnerable software. We evaluatedVulShield’s capability in a comprehensive set of vulnerabilities in 9 different types and found that VulShield mitigated all cases in an automated and effective manner. For Nginx, the latency introduced per request does not exceed 0.001 ms, while the peak performance overhead observed in UnixBench is 1.047%.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190045", "vector": [], "sparse_vector": [], "title": "Revisiting Concept Drift in Windows Malware Detection: Adaptation to Real Drifted Malware with Minimal Sam<PERSON>.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In applying deep learning for malware classification, it is crucial to account for the prevalence of malware evolution, which can cause trained classifiers to fail on drifted malware. Existing solutions to address concept drift use active learning. They select new samples for analysts to label and then retrain the classifier with the new labels. Our key finding is that the current retraining techniques do not achieve optimal results. These techniques overlook that updating the model with scarce drifted samples requires learning features that remain consistent across pre-drift and post-drift data. The model should thus be able to disregard specific features that, while beneficial for the classification of pre-drift data, are absent in post-drift data, thereby preventing prediction degradation. In this paper, we propose a new technique for detecting and classifying drifted malware that learns drift-invariant features in malware control flow graphs by leveraging graph neural networks with adversarial domain adaptation.  We compare it with existing model retraining methods in active learning-based malware detection systems and other domain adaptation techniques from the vision domain. Our approach significantly improves drifted malware detection on publicly available benchmarks and real-world malware databases reported daily by security companies in 2024.  We also tested our approach in predicting multiple malware families drifted over time. A thorough evaluation shows that our approach outperforms the state-of-the-art approaches.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190046", "vector": [], "sparse_vector": [], "title": "Blindfold: Confidential Memory Management by Untrusted Operating System.", "authors": ["Caihua Li", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Confidential Computing (CC) has received increasing attention in recent years as a mechanism to protect user data from untrusted operating systems (OSes). Existing CC solutions hide confidential memory from the OS and/or encrypt it to achieve confidentiality. In doing so, they render OS memory optimization unusable or complicate the trusted computing base (TCB) required for optimization. This paper presents our results toward overcoming these limitations, synthesized in a CC design named Blindfold. Like many other CC solutions, <PERSON>fold relies on a small trusted software component running at a higher privilege level than the kernel, called Guardian. It features three techniques that can enhance existing CC solutions. First, instead of nesting page tables, Blindfold’s Guardian mediates how the OS accesses memory and handles exceptions by switching page and interrupt tables. Second, Blindfold employs a lightweight capability system to regulate the OS’s semantic access to user memory, unifying case-by-case approaches in previous work. Finally, Blindfold provides carefully designed secure ABI for confidential memory management without encryption. We report an implementation of Blind<PERSON> that works on ARMv8-A/Linux. Using <PERSON><PERSON>'s prototype, we are able to evaluate the cost of enabling confidential memory management by the untrusted Linux kernel. We show Blindfold has a smaller runtime TCB than related systems and enjoys competitive performance. More importantly, we show that the Linux kernel, including all of its memory optimizations except memory compression, can function properly for confidential memory. This requires only about 400 lines of kernel modifications.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190047", "vector": [], "sparse_vector": [], "title": "On the Robustness of LDP Protocols for Numerical Attributes under Data Poisoning Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Wenhai Sun"], "summary": "Recent studies reveal that local differential privacy (LDP) protocols are vulnerable to data poisoning attacks where an attacker can manipulate the final estimate on the server by leveraging the characteristics of LDP and sending carefully crafted data from a small fraction of controlled local clients. This vulnerability raises concerns regarding the robustness and reliability of LDP in hostile environments. In this paper, we conduct a systematic investigation of the robustness of state-of-the-art LDP protocols for numerical attributes, i.e.,  categorical frequency oracles (CFOs) with binning and consistency, and distribution reconstruction. We evaluate protocol robustness through an attack-driven approach and propose new metrics for cross-protocol attack gain measurement. The results indicate that Square Wave and CFO-based protocols in the textit{Server} setting are more robust against the attack compared to the CFO-based protocols in the textit{User} setting. Our evaluation also unfolds new relationships between LDP security and its inherent design choices. We found that the hash domain size in local-hashing-based LDP has a profound impact on protocol robustness beyond the well-known effect on utility. Further, we propose a textit{zero-shot attack detection} by leveraging the rich reconstructed distribution information. The experiment show that our detection significantly improves the existing methods and effectively identifies data manipulation in challenging scenarios.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190048", "vector": [], "sparse_vector": [], "title": "UI-CTX: Understanding UI Behaviors with Code Contexts for Mobile Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many mobile apps utilize UI widgets to interact with users and trigger specific operational logic, such as clicking a button to send a message. While UI widgets are designed to be intuitive and user-friendly, they can also be misused to perform harmful behaviors that violate user expectations. To address these potential threats, recent studies strive to understand the intentions of UI widgets in mobile apps. However, existing methods either concentrate on the surface-level features of UI widgets, failing to capture their underlying intentions, or involve tedious and faulty information, making it challenging to distill the core intentions. In this paper, we present UI-CTX, which demystifies UI behaviors with a concise and effective representation. For each UI widget, UI-CTX first represents its intentions with a UI Handler Graph (UHG), incorporating the code context behind the widget while eliminating irrelevant information (e.g., unreachable code blocks). Then, UI-CTX performs graph summarization and explores both the structural and semantic information in UHGs to model the core intentions of UI widgets. To systematically evaluate UI-CTX, we extract a series of UI widget behaviors, such as login and search, from a large-scale dataset and conduct extensive experiments. Experimental results show that UI-CTX can effectively represent the intentions of UI widgets and significantly outperforms existing solutions in modeling UI widget behaviors. For example, in the task of classifying UI widget intentions, UHG achieves the highest average F1-score compared to other widget representations (+95.2% and ****% compared with permission set and call sequence, respectively) used in state-of-the-art approaches. Additionally, by accurately pinpointing the code contexts of widgets, UI-CTX achieves a $mathbf{3.6times}$ improvement in widget intention clustering performance.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190049", "vector": [], "sparse_vector": [], "title": "Delay-allowed Differentially Private Data Stream Release.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Yuan Hong", "<PERSON><PERSON><PERSON>"], "summary": "The research on tasks involving differentially private data stream releases has traditionally centered around real-time scenarios. However, not all data streams inherently demand real-time releases, and achieving such releases is challenging due to network latency and processing constraints in practical settings. We delve into the advantages of introducing a delay time in stream releases. Concentrating on the event-level privacy setting, we discover that incorporating a delay can overcome limitations faced by current approaches, thereby unlocking substantial potential for improving accuracy. Building on these insights, we developed a framework for data stream releases that allows for delays. Capitalizing on data similarity and relative order characteristics, we devised two optimization strategies, group-based and order-based optimizations, to aid in reducing the added noise and post-processing of noisy data. Additionally, we introduce a novel sensitivity truncation mechanism, significantly further reducing the amount of introduced noise. Our comprehensive experimental results demonstrate that, on a data stream of length $18,319$, allowing a delay of $10$ timestamps enables the proposed approaches to achieve a remarkable up to a $30times$ improvement in accuracy compared to baseline methods.Our code is open-sourced.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190050", "vector": [], "sparse_vector": [], "title": "Translating C To Rust: Lessons from a User Study.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Rust aims to offer full memory safety for programs, a guarantee that untamed C programs do not enjoy. How difficult is it to translate existing C code to Rust? To get a complementary view from that of automatic C to Rust translators, we report on a user study asking humans to translate real-world C programs to Rust. Our participants are able to produce safe Rust translations, whereas state-of-the-art automatic tools are not able to do so. Our analysis highlights that the high-level strategy taken by users departs significantly from those of automatic tools we study. We also find that users often choose zero-cost (static) abstractions for temporal safety, which addresses a predominant component of runtime costs in other full memory safety defenses. User-provided translations showcase a rich landscape of specialized strategies to translate the same C program in different ways to safe Rust, which future automatic translators can consider.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190051", "vector": [], "sparse_vector": [], "title": "EMIRIS: Eavesdropping on Iris Information via Electromagnetic Side Channel.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Iris recognition is one of the most secure biometric methods due to the uniqueness and stability of iris patterns, as well as their resistance to forgery. Consequently, it is frequently used in high-security authentication scenarios. However, systems using Near-Infrared (NIR) sensors may expose the iris information of users, leading to significant privacy risks. Our research found that the electromagnetic (EM) emissions generated during data transmission of NIR sensors are closely related to iris data. Based on this observation, we propose EMIRIS, a method for reconstructing the iris information using EM side channels. By deconstructing the digital signal transmission format of the NIR sensors and the mapping mechanism of the iris data matrix, we can reconstruct iris information from EM signals and convert it into iris images. To improve the quality of the reconstructed iris, we model the denoising and restoration of iris texture details as a linear inverse problem and tailor a diffusion model to solve it. Extensive experimental evaluations show that EMIRIS can effectively reconstruct iris information from commercial iris recognition devices, achieving an average SSIM of 0.511 and an average FID of 7.25. Even more concerning, these reconstructed irises can effectively spoof the classical iris recognition model with an average success rate of 53.47% on more than 3,000 iris samples from 50 different users.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190052", "vector": [], "sparse_vector": [], "title": "All your (data)base are belong to us: Characterizing Database Ransom(ware) Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present the first systematic study of database ransom(ware) attacks, a class of attacks where attackers scan for database servers, log in by leveraging the lack of authentication or weak credentials, drop the database contents, and demand a ransom to return the deleted data. We examine 23,736 ransom notes collected from 60,427 compromised database servers over three years, and set up database honeypots to obtain a first-hand view of current attacks. Database ransom(ware) attacks are prevalent with 6K newly infected servers in March 2024, a 60% increase over a year earlier. Our honeypots get infected in 14 hours since they are connected to the Internet. Weak authentication issues are two orders of magnitude more frequent on Elasticsearch servers compared to MySQL servers due to slow adoption of the latest Elasticsearch versions. To analyze who is behind database ransom(ware) attacks we implement a clustering approach that first identifies campaigns using the similarity of the ransom notes text. Then, it determines which campaigns are run by the same group by leveraging indicator reuse and information from the Bitcoin blockchain. For each group, it computes properties such as the number of compromised servers, the lifetime, the revenue, and the indicators used. Our approach identifies that the 60,427 database servers are victims of 91 campaigns run by 32 groups. It uncovers a dominant group responsible for 76% of the infected servers and 90% of the financial impact. We find links between the dominant group, a nation-state, and a previous attack on Git repositories.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190053", "vector": [], "sparse_vector": [], "title": "Uncovering the iceberg from the tip: Generating API Specifications for Bug Detection via Specification Propagation Analysis.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Jing<PERSON> Liu"], "summary": "Modern software often provides diverse APIs to facilitate development. Certain APIs, when used, can affect variables and require post-handling, such as error checks and resource releases. Developers should adhere to their usage specifications when using these APIs. Failure to do so can cause serious security threats, such as memory corruption and system crashes. Detecting such misuse depends on comprehensive API specifications, as violations of these specifications indicate API misuse. Previous studies have proposed extracting API specifications from various artifacts, including API documentation, usage patterns, and bug patches. However, these artifacts are frequently incomplete or unavailable for many APIs. As a result, the lack of specifications for uncovered APIs causes many false negatives in bug detection. In this paper, we introduce the idea of API Specification Propagation, which suggests that API specifications propagate through hierarchical API call chains. In particular, modern software often adopts a hierarchical API design, where high-level APIs build on low-level ones. When high-level APIs wrap low-level ones, they may inherit the corresponding specifications. Based on this idea, we present APISpecGen, which uses known specifications as seeds and performs bidirectional propagation analysis to generate specifications for new APIs. Specifically, given the seed specifications, APISpecGen infers which APIs the specifications might propagate to or originate from. To further generate specifications for the inferred APIs, APISpecGen combines API usage and validates them using data-flow analysis based on the seed specifications. Besides, APISpecGen iteratively uses the generated specifications as new seeds to cover more APIs. For efficient and accurate analysis, APISpecGen focuses only on code relevant to the specifications, ignoring irrelevant semantics. We implemented APISpecGen and evaluated it for specification generation and API misuse detection. With 6 specifications as seeds, APISpecGen generated 7332 specifications. Most of the generated specifications could not be covered by state-of-the-art work due to the quality of their sources. With the generated specifications, APISpecGen detected 186 new bugs in the Linux kernel, 113 of them have been confirmed by the developers, with 8 CVEs assigned.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190054", "vector": [], "sparse_vector": [], "title": "From Large to Mammoth: A Comparative Evaluation of Large Language Models in Vulnerability Detection.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Large Language Models (LLMs) have demonstrated strong potential in tasks such as code understanding and generation. This study evaluates several advanced LLMs—such as LLaMA-2, CodeLLaMA, LLaMA-3, Mistral, Mixtral, Gemma, CodeGemma, Phi-2, Phi-3, and GPT-4—for vulnerability detection, primarily in Java, with additional tests in C/C++ to assess generalization. We transition from basic positive sample detection to a more challenging task involving both positive and negative samples and evaluate the LLMs’ ability to identify specific vulnerability types. Performance is analyzed using runtime and detection accuracy in zero-shot and few-shot settings with custom and generic metrics. Key insights include the strong performance of models like Gemma and LLaMA-2 in identifying vulnerabilities, though this success varies, with some configurations performing no better than random guessing. Performance also fluctuates significantly across programming languages and learning modes (zero- vs. few-shot). We further investigate the impact of model parameters, quantization methods, context window (CW) sizes, and architectural choices on vulnerability detection. While CW consistently enhances performance, benefits from other parameters, such as quantization, are more limited. Overall, our findings underscore the potential of LLMs in automated vulnerability detection, the complex interplay of model parameters, and the current limitations in varied scenarios and configurations.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190055", "vector": [], "sparse_vector": [], "title": "GadgetMeter: Quantitatively and Accurately Gauging the Exploitability of Speculative Gadgets.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON> Ren", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Since their emergence in 2018, speculative execution attacks have proven difficult to fully prevent without substantial performance overhead. This is because most mitigations hurt modern processors' speculative nature, which is essential to many optimization techniques. To address this, numerous scanners have been developed to identify vulnerable code snippets (speculative gadgets) within software applications, allowing mitigations to be applied selectively and thereby minimizing performance degradation. In this paper, we show that existing speculative gadget scanners lack accuracy, often misclassifying gadgets due to limited modeling of timing properties. Instead, we identify another fundamental condition intrinsic to all speculative attacks—the timing requirement as a race condition inside the gadget. Specifically, the attacker must optimize the race condition between speculated authorization and secret leakage to successfully exploit the gadget. Therefore, we introduce GadgetMeter, a framework designed to quantitatively gauge the exploitability of speculative gadgets based on their timing property. We systematically explore the attacker's power to optimize the race condition inside gadgets (windowing power). A Directed Acyclic Instruction Graph is used to model timing conditions and static analysis and runtime testing are combined to optimize attack patterns and quantify gadget vulnerability. We use GadgetMeter to evaluate gadgets in a wide range of software, including six real-world applications and the Linux kernel. Our result shows that GadgetMeter can accurately identify exploitable speculative gadgets and quantify their vulnerability level,  identifying 471 gadgets reported by GadgetMeter works as unexploitable.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190056", "vector": [], "sparse_vector": [], "title": "Kronos: A Secure and Generic Sharding Blockchain Consensus with Optimized Overhead.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yuan Lu", "<PERSON><PERSON>eng Pan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Sharding enhances blockchain scalability by dividing the network into shards, each managing specific unspent transaction outputs or accounts. As an introduced new transaction type, cross-shard transactions pose a critical challenge to the security and efficiency of sharding blockchains.Currently, there is a lack of a generic sharding blockchain consensus pattern that achieves both security and low overhead. In this paper, we present Kronos, a secure sharding blockchain consensus achieving optimized overhead.In particular, we propose a new textit{secure sharding blockchain consensus pattern}, based on a textit{buffer} managed jointly by shard members. Valid transactions are transferred to the payee via the buffer, while invalid ones are rejected through happy or unhappy paths.Kronos is proved to achieve textit{security} textit{with atomicity} under malicious clients while maintaining textit{optimal intra-shard overhead}. Efficient rejection even requires no Byzantine fault tolerance (BFT) protocol execution in happy paths, and the cost in unhappy paths is still not higher than a two-phase commit.Besides, we propose secure cross-shard certification methods. Handling $b$ transactions, Kronos is proved to achieve cross-shard communication with low textit{cross-shard overhead} $mathcal{O}(n b lambda)$ ($n$ for the shard size and $lambda$ for the security parameter).Notably, Kronos imposes no restrictions on BFT and does not rely on timing assumptions, offering optional constructions in various modules. Kronos could serve as a universal framework for enhancing the performance and scalability of existing BFT protocols. Kronos supports generic models, including asynchronous networks, and can increase the throughput by several orders of magnitude. We implement Kronos using two prominent BFT protocols: asynchronous Speeding Dumbo (NDSS'22) and partially synchronous Hotstuff (PODC'19). Extensive experiments (over up to 1000 AWS EC2 nodes across 4 AWS regions) demonstrate Kronos scales the consensus nodes to thousands, achieving a substantial throughput of 320 ktx/sec with 2.0 sec latency. Compared with the past solutions, Kronos outperforms, achieving up to a 12$times$ improvement in throughput and a 50% reduction in latency when cross-shard transactions dominate the workload.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190057", "vector": [], "sparse_vector": [], "title": "L-HAWK: A Controllable Physical Adversarial Patch Against a Long-Distance Target.", "authors": ["Tai<PERSON> Liu", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The vision-based perception modules in autonomous vehicles (AVs) are prone to physical adversarial patch attacks. However, most existing attacks indiscriminately affect all passing vehicles. This paper introduces L-HAWK, a novel controllable physical adversarial patch activated by long-distance laser signals. L-HAWK is designed to target specific vehicles when the adversarial patch is triggered by laser signals while remaining benign under normal conditions. To achieve this goal and address the unique challenges associated with laser signals, we propose an asynchronous learning method for L-HAWK to determine the optimal laser parameters and the corresponding adversarial patch. To enhance the attack robustness in real-world scenarios, we introduce a multi-angle and multi-position simulation mechanism, a noise approximation approach, and a progressive sampling-based method. L-HAWK has been validated through extensive experiments in both digital and physical environments. Compared to a 59% success rate of TPatch (Usenix ’23) at 7 meters, L-HAWK achieves a 91.9% average attack success rate at 50 meters. This represents a 56% improvement in attack success rate and a more than sevenfold increase in attack distance.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190058", "vector": [], "sparse_vector": [], "title": "ProvGuard: Detecting SDN Control Policy Manipulation via Contextual Semantics of Provenance Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jianwei Zhuge", "<PERSON><PERSON><PERSON>"], "summary": "Software-Defined Networking (SDN) improves network flexibility by decoupling control functions (control plane) from forwarding devices (data plane). However, the logically centralized control plane is vulnerable to Control Policy Manipulation (CPM), which introduces incorrect policies by manipulating the controller's network view. Current methods for anomaly detection and configuration verification have limitations in detecting CPM attacks because they focus solely on the data plane. Certain covert CPM attacks are indistinguishable from normal behavior without analyzing the causality of the controller's decisions. In this paper, we propose ProvGuard, a provenance graph-based detection framework that identifies CPM attacks by monitoring controller activities. ProvGuard leverages static analysis to identify data-plane-related controller operations and guide controller instrumentation, constructing a provenance graph from captured control plane activities. ProvGuard reduces redundancies and extracts paths in the provenance graph as contexts to capture concise and long-term features. Suspicious behaviors are flagged by identifying paths that cause prediction errors beyond the normal range, based on a sequence-to-sequence prediction model. We implemented a prototype of ProvGuard on the Floodlight controller. Our approach successfully identified all four typical CPM attacks that previous methods could not fully address and provided valuable insights for investigating attack behaviors.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190059", "vector": [], "sparse_vector": [], "title": "LADDER: Multi-Objective Backdoor Attack via Evolutionary Algorithm.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Current black-box backdoor attacks in convolutional neural networks formulate attack objective(s) as textit{single-objective} optimization problems in textit{single domain}.Designing triggers in single domain harms semantics and trigger robustness as well as introduces visual and spectral anomaly.This work proposes a multi-objective black-box backdoor attack in dual domains via evolutionary algorithm (LADDER), the first instance of achieving multiple attack objectives simultaneously by optimizing triggers without requiring prior knowledge about victim model.In particular, we formulate LADDER as a multi-objective optimization problem (MOP) and solve it via multi-objective evolutionary algorithm (MOEA).MOEA maintains a population of triggers with trade-offs among attack objectives and uses non-dominated sort to drive triggers toward optimal solutions.We further apply preference-based selection to MOEA to exclude impractical triggers.LADDER investigates a new dual-domain perspective for trigger stealthiness by minimizing the anomaly between clean and poisoned samples in the spectral domain.Lastly, the robustness against preprocessing operations is achieved by pushing triggers to low-frequency regions.Extensive experiments comprehensively showcase that LADDER achieves attack effectiveness of at least 99%, attack robustness with 90.23% (50.09% higher than state-of-the-art attacks on average), superior natural stealthiness (1.12$times$ to 196.74$times$ improvement) and excellent spectral stealthiness (8.45$times$ enhancement) as compared to current stealthy attacks by the average $l_2$-norm across 5 public datasets.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190060", "vector": [], "sparse_vector": [], "title": "CCTAG: Configurable and Combinable Tagged Architecture.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Chenyang Li", "<PERSON><PERSON>", "Yuan Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Memory safety violations are a significant concern in real-world programs, prompting the development of various mitigation methods. However, existing cost-efficient defenses provide limited protection and can be bypassed by sophisticated attacks, necessitating the combination of multiple defenses. Unfortunately, combining these defenses often results in performance degradation and compatibility issues. We present CCTAG, a lightweight architecture that simplifies the integration of diverse tag-based defense mechanisms. It offers configurable tag verification and modification rules to build various security policies, acting as basic protection primitives for defense applications. Its policy-centric mask design boosts flexibility and prevents conflicts, enabling multiple defense mechanisms to run concurrently. Our RISC-V prototype on an FPGA board demonstrates that CCTAG incurs minimal hardware overhead, with a slight increase in LUTs (6.77%) and FFs (8.02%). With combined protections including ret address protection, code pointer and vtable pointer integrity, and memory coloring, the SPEC CPU CINT2006 and CINT2017 benchmarks report low runtime overheads of 4.71% and 7.93%, respectively. Security assessments with CVEs covering major memory safety vulnerabilities and various exploitation techniques verify CCTAG’s effectiveness in mitigating real-world threats.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190061", "vector": [], "sparse_vector": [], "title": "Generating API Parameter Security Rules with LLM for API Misuse Detection.", "authors": ["Jing<PERSON> Liu", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "When utilizing library APIs, developers should follow the API security rules to mitigate the risk of API misuse. API Parameter Security Rule (APSR) is a common type of security rule that specifies how API parameters should be safely used and places constraints on their values. Failure to comply with the APSRs can lead to severe security issues, including null pointer dereference and memory corruption. Manually analyzing numerous APIs and their parameters to construct APSRs is labor-intensive and needs to be automated. Existing studies generate APSRs from documentation and code, but the missing information and limited analysis heuristics result in missing APSRs. Due to the superior Large Language Model’s (LLM) capability in code analysis and text generation without predefined heuristics, we attempt to utilize it to address the challenge encountered in API misuse detection. However, directly utilizing LLMs leads to incorrect APSRs which may lead to false bugs in detection, and overly general APSRs that could not generate applicable detection code resulting in many security bugs undiscovered. In this paper, we present a new framework, named GPTAid, for automatic APSRs generation by analyzing API source code with LLM and detecting API misuse caused by incorrect parameter use. To validate the correctness of the LLM-generated APSRs, we propose an execution feedback-checking approach based on the observation that security-critical API misuse is often caused by APSRs violations, and most of them result in runtime errors. Specifically, GPTAid first uses LLM to generate raw APSRs and the Right calling code, and then generates Violation code for each raw APSR by modifying the Right calling code using LLM. Subsequently, GPTAid performs dynamic execution on each piece of Violation code and further filters out the incorrect APSRs based on runtime errors. To further generate concrete APSRs, GPTAid employs a code differential analysis to refine the filtered ones. Particularly, as the programming language is more precise than natural language, GPTAid identifies the key operations within Violation code by differential analysis, and then generates the corresponding concrete APSR based on the aforementioned operations. These concrete APSRs could be precisely interpreted into applicable detection code, which proven to be effective in API misuse detection. Implementing on the dataset containing 200 randomly selected APIs from eight popular libraries, GPTAid achieves a precision of 92.3%. Moreover, it generates 6 times more APSRs than state-of-the-art detectors on a comparison dataset of previously reported bugs and APSRs. We further evaluated GPTAid on 47 applications, 210 unknown security bugs were found potentially resulting in severe security issues (e.g., system crashes), 150 of which have been confirmed by developers after our reports.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190062", "vector": [], "sparse_vector": [], "title": "EAGLEYE: Exposing Hidden Web Interfaces in IoT Devices via Routing Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Zicong Gao", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Jiang", "<PERSON><PERSON><PERSON>"], "summary": "Hidden web interfaces, i.e., undisclosed access channels in IoT devices, introduce great security risks and have resulted in severe attacks in recent years. However, the definition of such threats is vague, and few solutions are able to discover them. Due to their hidden nature, traditional bug detection solutions (e.g., taint analysis, fuzzing) are hard to detect them. In this paper, we present a novel solution EAGLEYE to automatically expose hidden web interfaces in IoT devices. By analyzing input requests to public interfaces, we first identify routing tokens within the requests, i.e., those values (e.g., actions or file names) that are referenced and used as index by the firmware code (routing mechanism) to find associated handler functions. Then, we utilize modern large language models to analyze the contexts of such routing tokens and deduce their common pattern, and then infer other candidate values (e.g., other actions or file names) of these tokens. Lastly, we perform a hidden-interface directed black-box fuzzing, which mutates the routing tokens in input requests with these candidate values as the high-quality dictionary. We have implemented a prototype of EAGLEYE and evaluated it on 13 different commercial IoT devices. EAGLEYE successfully found 79 hidden interfaces, 25X more than the state-of-the-art (SOTA) solution IoTScope. Among them, we further discovered 29 unknown vulnerabilities including backdoor, XSS (cross-site scripting), command injection, and information leakage, and have received 7 CVEs.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190063", "vector": [], "sparse_vector": [], "title": "BumbleBee: Secure Two-party Inference Framework for Large Transformers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large transformer-based models have realized state-of-the-art performance on lots of real-world tasks such as natural language processing and computer vision.However, with the increasing sensitivity of the data and tasks they handle, privacy has become a major concern during model deployment.In this work, we focus on private inference in two-party settings, where one party holds private inputs and the other holds the model.We introduce BumbleBee, a fast and communication-friendly two-party private transformer inference system.Our contributions are three-fold:First, we propose optimized protocols for matrix multiplication, which significantly reduce communication costs by 80% -- 90% compared to previous techniques.Secondly, we develop a methodology for constructing efficient protocols tailored to the non-linear activation functions employed in transformer models.The proposed activation protocols have realized a significant enhancement in processing speed, alongside a remarkable reduction in communication costs by 80% -- 95% compared with two prior methods.Lastly, we have performed extensive benchmarks on five transformer models.BumbleBee demonstrates its capability by evaluating the LLaMA-7B model, generating one token in approximately 8 minutes using CPUs.Our results further reveal that BumbleBee outperforms Iron (NeurIPS22) by over an order of magnitude and is three times faster than BOLT (Oakland24) with one-tenth communication.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190064", "vector": [], "sparse_vector": [], "title": "A New PPML Paradigm for Quantized Models.", "authors": ["Tianpei Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Model quantization has become a common practice in machine learning (ML) to improve efficiency and reduce computational/communicational overhead. However, adopting quantization in privacy-preserving machine learning (PPML) remains challenging due to the complex internal structure of quantized operators, which leads to inefficient protocols under the existing PPML frameworks. In this work, we propose a new PPML paradigm that is tailor-made for and can benefit from quantized models. Our main observation is that look-up tables can ignore the complex internal constructs of any functions which can be used to simplify the quantized operator evaluation. We view the model inference process as a sequence of quantized operators, and each operator is implemented by a look-up table. We then develop an efficient private look-up table evaluation protocol, and its online communication cost is only $log n$, where $n$ is the size of the look-up table.On a single CPU core, our protocol can evaluate $2^{26}$ tables with 8-bit input and 8-bit output per second. The resulting PPML framework for quantized models offers extremely fast online performance.The experimental results demonstrate that our quantization strategy achieves substantial speedups over SOTA PPML solutions, improving the online performance by $40sim 60 times$ w.r.t. convolutional neural network (CNN) models, such as AlexNet, VGG16, and ResNet18, and by $10sim 25 times$ w.r.t. large language models (LLMs), such as GPT-2, GPT-Neo, and Llama2.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190065", "vector": [], "sparse_vector": [], "title": "Retrofitting XoM for Stripped Binaries without Embedded Data Relocation.", "authors": ["<PERSON><PERSON>", "Jiang <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jianming Fu"], "summary": "System programs are frequently coded in memory-unsafe languages such as C/C++, rendering them susceptible to a variety of memory corruption attacks. Among these, just-in-time return-oriented programming (JIT-ROP) stands out as an advanced form of code-reuse attack designed to circumvent code randomization defenses. JIT-ROP leverages memory disclosure vulnerabilities to dynamically harvest reusable code gadgets and construct attack payloads in real-time. To counteract JIT-ROP threats, researchers have developed multiple execute-only memory (XoM) prototypes to prevent dynamic reading and disassembly of memory pages. XoM, akin to the widely deployed W$oplus$X protection, holds promise in enhancing security. However, existing XoM solutions may not be compatible with legacy and commercial off-the-shelf (COTS) programs, or they may require patching the protected binary to separate code and data areas, leading to poor reliability. In addition, some XoM methods have to modify the underlying architectural mechanism, compromising compatibility and performance. In this paper, we present emph{PXoM}, a practical technique to seamlessly retrofit XoM into stripped binaries on the x86-64 platform. As handling the mixture of code and data is a well-known challenge for XoM, most existing  methods require the strict separation of code and data areas via either compile-time transformation or binary patching, so that the unreadable permission can be safely enforced at the granularity of memory pages. In contrast to previous approaches, we provide a fine-grained memory permission control mechanism to restrict the read permission of code while allowing legitimate data reads within code pages. This novelty enables PXoM to harden stripped binaries but without resorting to error-prone embedded data relocation. We leverage Intel's hardware feature, Memory Protection Keys, to offer an efficient fine-grained permission control. We measure PXoM's performance with both micro- and macro-benchmarks, and it only introduces negligible runtime overhead. Our security evaluation shows that PXoM leaves adversaries with little wiggle room to harvest all of the required gadgets, suggesting PXoM is practical for real-world deployment.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190066", "vector": [], "sparse_vector": [], "title": "Truman: Constructing Device Behavior Models from OS Drivers to Fuzz Virtual Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tingt<PERSON> Yin", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Virtual devices are a large attack surface of hypervisors. Vulnerabilities in virtual devices may enable attackers to jailbreak hypervisors or even endanger co-located virtual machines. While fuzzing has discovered vulnerabilities in virtual devices across both open-source and closed-source hypervisors, the efficiency of these virtual device fuzzers remains limited because they are unaware of the complex behaviors of virtual devices in general. We present <PERSON>, a novel universal fuzzing engine that automatically infers dependencies from open-source OS drivers to construct device behavior models (DBMs) for virtual device fuzzing, regardless of whether target virtual devices are open-source or binaries. The DBM includes inter- and intra-message dependencies and fine-grained state dependency of virtual device messages. Based on the DBM, <PERSON> generates and mutates quality seeds that satisfy the dependencies encoded in the DBM. We evaluate the prototype of <PERSON> on the latest version of hypervisors. In terms of coverage, <PERSON> outperformed start-of-the-art fuzzers for 19/29 QEMU devices and obtained a relative coverage boost of 34% compared to Morphuzz for virtio devices. Additionally, <PERSON> discovered 54 new bugs in QEMU, VirtualBox, VMware Workstation Pro, and Parallels, with 6 CVEs assigned.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190067", "vector": [], "sparse_vector": [], "title": "Careful About What App Promotion Ads Recommend! Detecting and Explaining Malware Promotion via App Promotion Graph.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Yanfang Ye"], "summary": "In Android apps, their developers frequently place app promotion ads, namely advertisements to promote other apps. Unfortunately, the inadequate vetting of ad content allows malicious developers to exploit app promotion ads as a new distribution channel for malware. To help detect malware distributed via app promotion ads, in this paper, we propose a novel approach, named ADGPE, that synergistically integrates app user interface (UI) exploration with graph learning to automatically collect app promotion ads, detect malware promoted by these ads, and explain the promotion mechanisms employed by the detected malware. Our evaluation on 18, 627 app promotion ads demonstrates the substantial risks in the app promotion ecosystem. The probability for encountering malware when downloading from app promotion ads is hundreds of times higher than from the Google Play. Popular ad networks such as Google AdMob, Unity Ads, and Applovin are exploited by malicious developers to spread a variety of malware: aggressive adware, rogue security software, trojan, and fleeceware. Our UI exploration technique can find 24% more app promotion ads within the same time compared to the state-of-the-art techniques. We also demonstrate our technique’s usage in investigating underground economy by collecting app promotion ads in the wild. Leveraging the found app promotion relations, our malware detection model achieves a 5.17% gain in F1 score, improving the F1 score of state-of-art techniques from 90.14% to 95.31%. Our malware detection model also detects 28 apps that were initially labeled as benign apps by VirusTotal but labeled by it as malware/potentially unwanted apps (PUAs) six months later. Our path inference model unveils two malware promotion mechanisms: custom-made ad-based promotion via hardcoded ads and ad library-based promotion via interactions with ad servers (e.g., AdMob and Applovin). These findings uncover the critical security risks of app promotion ads and demonstrate the effectiveness of ADGPE in combining dynamic program analysis with graph learning to study the app promotion ad-based malware distribution.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190068", "vector": [], "sparse_vector": [], "title": "Evaluating Machine Learning-Based IoT Device Identification Models for Security Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the proliferation of IoT devices, network device identification is essential for effective network management and security. Many exhibit performance degradation despite the potential of machine learning-based IoT device identification solutions. Degradation arises from the assumption of static IoT environments that do not account for the diversity of real-world IoT networks, as devices operate in various modes and evolve over time. In this paper, we evaluate current IoT device identification solutions using curated datasets and representative features across different settings. We consider key factors that affect real-world device identification, including modes of operation, spatio-temporal variations, and traffic sampling, and organise them into a set of attributes by which we can evaluate current solutions. We then use machine learning explainability techniques to pinpoint the key causes of performance degradation. This evaluation uncovers empirical evidence of what continuously identifies devices, provides valuable insights, and practical recommendations for network operators to improve their IoT device identification in operational deployments.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190069", "vector": [], "sparse_vector": [], "title": "KernelSnitch: Side Channel-Attacks on Kernel Data Structures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The sharing of hardware elements, such as caches, is known to introduce microarchitectural side-channel leakage. One approach to eliminate this leakage is to not share hardware elements across security domains. However, even under the assumption of leakage-free hardware, it is unclear whether other critical system components, like the operating system, introduce software-caused side-channel leakage. In this paper, we present a novel generic software side-channel attack, KernelSnitch, targeting kernel data structures such as hash tables and trees. These structures are commonly used to store both kernel and user information, e.g., metadata for userspace locks. KernelSnitch exploits that these data structures are variable in size, ranging from an empty state to a theoretically arbitrary amount of elements. Accessing these structures requires a variable amount of time depending on the number of elements, i.e., the occupancy level. This variance constitutes a timing side channel, observable from user space by an unprivileged, isolated attacker. While the timing differences are very low compared to the syscall runtime, we demonstrate and evaluate methods to amplify these timing differences reliably. In three case studies, we show that KernelSnitch allows unprivileged and isolated attackers to leak sensitive information from the kernel and activities in other processes. First, we demonstrate covert channels with transmission rates up to 580 kbit/s. Second, we perform a kernel heap pointer leak in less than 65 s by exploiting the specific indexing that Linux is using in hash tables. Third, we demonstrate a website fingerprinting attack, achieving an F1 score of more than 89 %, showing that activity in other user programs can be observed using KernelSnitch. Finally, we discuss mitigations for our hardware-agnostic attacks.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190070", "vector": [], "sparse_vector": [], "title": "Spatial-Domain Wireless Jamming with Reconfigurable Intelligent Surfaces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Wireless communication infrastructure is a cornerstone of modern digital society, yet it remains vulnerable to the persistent threat of wireless jamming. Attackers can easily create radio interference to overshadow legitimate signals, leading to denial of service.The broadcast nature of radio signal propagation makes such attacks possible in the first place, but at the same time poses a challenge for the attacker: The jamming signal does not only reach the victim device but also other neighboring devices, preventing precise attack targeting. In this work, we solve this challenge by leveraging the emerging RIS technology, for the first time, for precise delivery of jamming signals. In particular, we propose a novel approach that allows for environment-adaptive spatial control of wireless jamming signals, granting a new degree of freedom to perform jamming attacks.We explore this novel method with extensive experimentation and demonstrate that our approach can disable the wireless communication of one or multiple victim devices while leaving neighboring devices unaffected. Notably, our method extends to challenging scenarios where wireless devices are very close to each other: We demonstrate complete denial-of-service of a Wi-Fi device while a second device located at a distance as close as 5 mm remains unaffected, sustaining wireless communication at a data rate of 25 Mbit/s. Lastly, we conclude by proposing potential countermeasures to thwart RIS-based spatial domain wireless jamming attacks.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190071", "vector": [], "sparse_vector": [], "title": "QMSan: Efficiently Detecting Uninitialized Memory Errors During Fuzzing.", "authors": ["<PERSON>", "Daniele Cono D&apos;Elia", "<PERSON>", "<PERSON>"], "summary": "Fuzzing evolved into the most popular technique to detect bugs in software. Its combination with sanitizers has shown tremendous efficacy in uncovering memory safety errors, such as buffer overflows, that haunt C and C++ programmers. However, an important class of such issues, the so-called use-of-uninitialized-memory (UUM) errors, struggles to gain similar benefits from fuzzing endeavors. The only fuzzer-compatible UUM sanitizer available to date, MSan, requires that all libraries are fully instrumented. Unlike address sanitization, for which partial instrumentation results in false negatives (missed detection of bugs), UUM sanitizers require complete instrumentation to avoid false positives, hampering testing at scale. Yet, full-stack compiler-based instrumentation can be a daunting prospect for compatibility and practicality. As a result, many programs are left untested for UUM bugs. In this paper, we propose an efficient multi-layer, opportunistic design that does not require (source-based) recompilation of all code without harming accuracy. The multiplicity of executions when fuzzing offers us the opportunity to learn what any encountered false positive looks like, and later ignore them when we meet them again with new test cases. Such an avenue is feasible only if one can resort to fast techniques to effectively discriminate candidate errors, or false negatives will then occur. We show how to realize this design by using the dynamic binary translation of QEMU for compatibility and lightweight code analysis techniques to achieve scalability and accuracy. As a result, we obtain a fuzzer-friendly, performant sanitizer, QMSan, that effectively tackles current practicality challenges of UUM error detection. On a collection of 10 open-source and 5 proprietary programs, QMSan exposed 44 new UUM bugs. In our tests, QMSan incurs slowdowns of 1.51x over QEMU and 1.55x over the compiler-based instrumentation of MSan, showing no false positives and false negatives. QMSan is open-source.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190072", "vector": [], "sparse_vector": [], "title": "Rondo: Scalable and Reconfiguration-Friendly Randomness Beacon.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present Ron<PERSON>, a scalable and reconfiguration-friendly distributed randomness beacon (DRB) protocol in the partially synchronous model. Rondo is the first DRB protocol that is built from batched asynchronous verifiable secret sharing (bAVSS) and meanwhile avoids the high $O(n^3)$ message cost, where $n$ is the number of nodes. Our key contribution lies in the introduction of a new variant of bAVSS called batched asynchronous verifiable secret sharing with partial output (bAVSS-PO). bAVSS-PO is a weaker primitive than bAVSS but allows us to build a secure and more scalable DRB protocol. We propose a bAVSS-PO protocol Breeze. <PERSON><PERSON> achieves the optimal $O(n)$ messages for the sharing stage and allows <PERSON><PERSON> to offer better scalability than prior DRB protocols.Additionally, to support the reconfiguration, we introduce Rondo-BFT, a dynamic and partially synchronous Byzantine fault-tolerant protocol inspired by Dyno (S&P 2022). Unlike Dyno, Rondo-BFT provides a communication pattern that generates randomness beacon output periodically, making it well-suited for DRB applications. We implement our protocols and evaluate the performance on Amazon EC2 using up to 91 instances. Our evaluation results show that <PERSON><PERSON> achieves higher throughput than existing works and meanwhile offers better scalability, where the performance does not degrade as significantly as $n$ grows.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190073", "vector": [], "sparse_vector": [], "title": "ASGARD: Protecting On-Device Deep Neural Networks with Virtualization-Based Trusted Execution Environments.", "authors": ["Myungsuk Moon", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Dokyung Song"], "summary": "On-device deep learning, increasingly popular for enhancing user privacy, now poses a serious risk to the privacy of deep neural network (DNN) models. Researchers have proposed to leverage Arm TrustZone's trusted execution environment (TEE) to protect models from attacks originating in the rich execution environment (REE). Existing solutions, however, fall short: (i) those that fully contain DNN inference within a TEE either support inference on CPUs only, or require substantial modifications to closed-source proprietary software for incorporating accelerators; (ii) those that offload part of DNN inference to the REE either leave a portion of DNNs unprotected, or incur large run-time overheads due to frequent model (de)obfuscation and TEE-to-REE exits. We present ASGARD, the first virtualization-based TEE solution designed to protect on-device DNNs on legacy Armv8-A SoCs. Unlike prior work that uses TrustZone-based TEEs for model protection, ASGARD's TEEs remain compatible with existing proprietary software, maintain the trusted computing base (TCB) minimal, and incur near-zero run-time overhead. To this end, ASGARD (i) securely extends the boundaries of an existing TEE to incorporate an SoC-integrated accelerator via secure I/O passthrough, (ii) tightly controls the size of the TCB via our aggressive yet security-preserving platform- and application-level TCB debloating techniques, and (iii) mitigates the number of costly TEE-to-REE exits via our exit-coalescing DNN execution planning. We implemented ASGARD on RK3588S, an Armv8.2-A-based commodity Android platform equipped with a Rockchip NPU, without modifying Rockchip- nor Arm-proprietary software. Our evaluation demonstrates that ASGARD effectively protects on-device DNNs in legacy SoCs with a minimal TCB size and negligible inference latency overhead.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190074", "vector": [], "sparse_vector": [], "title": "PBP: Post-training Backdoor Purification for Malware Classifiers.", "authors": ["<PERSON><PERSON>", "Ngoc N. <PERSON>", "<PERSON>", "<PERSON>"], "summary": "In recent years, the rise of machine learning (ML) in cybersecurity has brought new challenges, including the increasing threat of backdoor poisoning attacks on ML malware classifiers. These attacks aim to manipulate model behavior when provided with a particular input trigger. For instance, adversaries could inject malicious samples into public malware repositories, contaminating the training data and potentially misclassifying malware by the ML model. Current countermeasures predominantly focus on detecting poisoned samples by leveraging disagreements within the outputs of a diverse set of ensemble models on training data points.However, these methods are not applicable in scenarios involving ML-as-a-Service (MLaaS) or for users who seek to purify a backdoored model post-training. Addressing this scenario, we introduce PBP, a post-training defense for malware classifiers that mitigates various types of backdoor embeddings without assuming any specific backdoor embedding mechanism. Our method exploits the influence of backdoor attacks on the activation distribution of neural networks, independent of the trigger-embedding method.In the presence of a backdoor attack, the activation distribution of each layer is distorted into a mixture of distributions. By regulating the statistics of the batch normalization layers, we can guide a backdoored model to perform similarly to a clean one. Our method demonstrates substantial advantages over several state-of-the-art methods, as evidenced by experiments on two datasets, two types of backdoor methods, and various attack configurations. Our experiments showcase that PBP can mitigate even the SOTA backdoor attacks for malware classifiers, e.g., Jigsaw Puzzle, which was previously demonstrated to be stealthy against existing backdoor defenses. Notably, your approach requires only a small portion of the training data --- only 1% --- to purify the backdoor and reduce the attack success rate from 100% to almost 0%, a 100-fold improvement over the baseline methods. Our code is available at https://github.com/judydnguyen/pbp-backdoor-purification-official.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190075", "vector": [], "sparse_vector": [], "title": "Power-Related Side-Channel Attacks using the Android Sensor Framework.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software-based power side-channel attacks are a significant security threat to modern computer systems, enabling adversaries to extract confidential information. Existing attacks typically exploit direct power signals from dedicated interfaces, as demonstrated in the PLATYPUS attack, or power-dependent timing variations, as in the case of the Hertzbleed attack. As access to direct power signals is meanwhile restricted on more and more platforms, an important question is whether other exploitable power-related signals exist beyond timing proxies. In this paper, we show that Android mobile devices expose numerous power-related signals that allow power side-channel attacks. We systematically analyze unprivileged sensors provided by the Android sensor framework on multiple devices and show that these sensors expose parasitic influences of the power consumption. Our results include new insights into Android sensor leakage, particularly a novel leakage primitive: the rotation dependent power leakage of the geomagnetic rotation vector sensor. We extensively evaluate the exposed sensors for different information leakage types. We compare them with the corresponding ground truth, achieving correlations greater than 0.9 for some of our tested sensors. In extreme cases, we observe not only statistical results but also, e.g., changes in a compass app’s needle by approximately 30° due to CPU stress. Additionally, we evaluate the capabilities of our identified leakage primitives in two case studies: As a remote attacker via the Google Chrome web browser and as a local attacker running inside an installed app. In particular, we present an end-to-end pixel-stealing attack on different Android devices that effectively circumvents the browser’s cross-origin isolation with a leakage rate of 5 - 10 s per pixel. Lastly, we demonstrate a proof-of-concept AES attack, leaking individual key bytes using our newly discovered leakage primitive.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190076", "vector": [], "sparse_vector": [], "title": "&quot;Where Are We On Cyber?&quot; - A Qualitative Study On Boards&apos; Cybersecurity Risk Decision Making.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Boards are increasingly required to oversee the cybersecurity risks of their organizations. To make informed decisions, board members have to rely on the information given to them, which could come from their Chief Information Security Officers (CISOs), the reports of executives, audits, and regulations.However, little is known about how boards decide after receiving such information and how their relationship with other stakeholders shapes those decisions. Here, we present the results of an in-depth interview study with n=18 C-level managers, board members, CISOs, and C-level consultants of some of the largest UK-based companies.Our findings suggest that a power imbalance exists: board members will often not ask the right questions to executives and CISOs since they fear being exposed as IT novices. This ultimately makes boards highly dependent on those providing them with cybersecurity information, leading to losing their oversight function. Furthermore, cybersecurity risk is abstracted to budget decisions with no further involvement in cybersecurity strategies through boards.We discuss possible ways to strengthen boards' oversight functions, such as releasing industry benchmarks through public cyber agencies or implementing support structures within the company - such as standing (cybersecurity) risk and audit committees.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190077", "vector": [], "sparse_vector": [], "title": "Black-box Membership Inference Attacks against Fine-tuned Diffusion Models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the rapid advancement of diffusion-based image-generative models, the quality of generated images has become increasingly photorealistic. Moreover, with the release of high-quality pre-trained image-generative models, a growing number of users are downloading these pre-trained models to fine-tune them with downstream datasets for various image-generation tasks. However, employing such powerful pre-trained models in downstream tasks presents significant privacy leakage risks. In this paper, we propose the first scores-based membership inference attack framework tailored for recent diffusion models, and in the more stringent black-box access setting. Considering four distinct attack scenarios and three types of attacks, this framework is capable of targeting any popular conditional generator model, achieving high precision, evidenced by an impressive AUC of 0.95.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190078", "vector": [], "sparse_vector": [], "title": "Towards Understanding Unsafe Video Generation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Video generation models (VGMs) have demonstrated the capability to synthesize high-quality output. It is important to understand their potential to produce unsafe content, such as violent or terrifying videos. In this work, we provide a comprehensive understanding of unsafe video generation. First, to confirm the possibility that these models could indeed generate unsafe videos, we choose unsafe content generation prompts collected from 4chan and Lexica, and three open-source SOTA VGMs to generate unsafe videos.After filtering out duplicates and poorly generated content, we created an initial set of $2112$ unsafe videos from an original pool of $5607$ videos. Through clustering and thematic coding analysis of these generated videos, we identify $5$ unsafe video categories: textit{Distorted/Weird}, textit{Terrifying}, textit{Pornographic}, textit{Violent/Bloody}, and textit{Political}. With IRB approval, we then recruit online participants to help label the generated videos. Based on the annotations submitted by $403$ participants, we identified $937$ unsafe videos from the initial video set. With the labeled information and the corresponding prompts, we created the first dataset of unsafe videos generated by VGMs. We then study possible defense mechanisms to prevent the generation of unsafe videos. Existing defense methods in image generation focus on filtering either input prompt or output results. We propose a new approach called fullsysname (sysname), which works within the model’s internal sampling process. sysname can achieve $0.90$ defense accuracy while reducing time and computing resources by $10times$ when sampling a large number of unsafe prompts. Our experiment includes three open-source SOTA video diffusion models, each achieving accuracy rates of $0.99$, $0.92$, and $0.91$, respectively. Additionally, our method was tested with adversarial prompts and on image-to-video diffusion models, and achieved nearly $1.0$ accuracy on both settings. Our method also shows its interoperability by improving the performance of other defenses when combined with them.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190079", "vector": [], "sparse_vector": [], "title": "Characterizing the Impact of Audio Deepfakes in the Presence of Cochlear Implant.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cochlear implants (CIs) allow deaf and hard-of-hearing individuals to use audio devices, such as phones or voice assistants. However, the advent of increasingly sophisticated synthetic audio (i.e., deepfakes) potentially threatens these users. Yet, this population's susceptibility to such attacks is unclear. In this paper, we perform the first study of the impact of audio deepfakes on CI populations. We examine the use of CI-simulated audio within deepfake detectors. Based on these results, we conduct a user study with 35 CI users and 87 hearing persons (HPs) to determine differences in how CI users perceive deepfake audio. We show that CI users can, similarly to HPs, identify text-to-speech generated deepfakes. Yet, they perform substantially worse for voice conversion deepfake generation algorithms, achieving only 67% correct audio classification. We also evaluate how detection models trained on a CI-simulated audio compare to CI users and investigate if they can effectively act as proxies for CI users. This work begins an investigation into the intersection between adversarial audio and CI users to identify and mitigate threats against this marginalized group.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190080", "vector": [], "sparse_vector": [], "title": "Secure IP Address Allocation at Cloud Scale.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Public clouds necessitate dynamic resource allocation and sharing. However, the dynamic allocation of IP addresses can be abused by adversaries to source malicious traffic, bypass rate limiting systems, and even capture traffic intended for other cloud tenants. As a result, both the cloud provider and their customers are put at risk, and defending against these threats requires a rigorous analysis of tenant behavior, adversarial strategies, and cloud provider policies. In this paper, we develop a practical defense for IP address allocation through such an analysis. We first develop a statistical model of cloud tenant deployment behavior based on literature and measurement of deployed systems. Through this, we analyze IP allocation policies under existing and novel threat models. In response to our stronger proposed threat model, we design IP scan segmentation, an IP allocation policy that protects the address pool against adversarial scanning even when an adversary is not limited by number of cloud tenants. Through empirical evaluation on both synthetic and real-world allocation traces, we show that IP scan segmentation reduces adversaries' ability to rapidly allocate addresses, protecting both address space reputation and cloud tenant data. In this way, we show that principled analysis and implementation of cloud IP address allocation can lead to substantial security gains for tenants and their users.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190081", "vector": [], "sparse_vector": [], "title": "Diffence: Fencing Membership Privacy With Diffusion Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep learning models, while achieving remarkable performances across various tasks, are vulnerable to membership inference attacks (MIAs), wherein adversaries identify if a specific data point was part of the model's training set. This susceptibility raises substantial privacy concerns, especially when models are trained on sensitive datasets. Although various defenses have been proposed, there is still substantial room for improvement in the privacy-utility trade-off. In this work, we introduce a novel defense framework against MIAs by leveraging generative models. The key intuition of our defense is to *remove the differences between member and non-member inputs*, which is exploited by MIAs, by re-generating input samples before feeding them to the target model. Therefore, our defense, called Diffence, works *pre inference*, which is unlike prior defenses that are either training-time (modify the model) or post-inference time (modify the model's output). A unique feature of Diffence is that it works on input samples only, without modifying the training or inference phase of the target model. Therefore, it can be *cascaded with other defense mechanisms* as we demonstrate through experiments. Diffence is specifically designed to preserve the model's prediction labels for each sample, thereby not affecting accuracy. Furthermore, we have empirically demonstrated that it does not reduce the usefulness of the confidence vectors. Through extensive experimentation, we show that Diffence can serve as a robust plug-n-play defense mechanism, enhancing membership privacy without compromising model utility—both in terms of accuracy and the usefulness of confidence vectors—across standard and defended settings. For instance, <PERSON>ffence reduces MIA attack accuracy against an undefended model by 15.8% and attack AUC by 14.0% on average across three datasets, all without impacting model utility. By integrating Diffence with prior defenses, we can achieve new state-of-the-art performances in the privacy-utility trade-off. For example, when combined with the state-of-the-art SELENA defense it reduces attack accuracy by 9.3%, and attack AUC by 10.0%. Diffence achieves this by imposing a negligible computation overhead, adding only 57ms to the inference time per sample processed on average.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190082", "vector": [], "sparse_vector": [], "title": "A Large-Scale Measurement Study of the PROXY Protocol and its Security Implications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reverse proxy servers play a critical role in optimizing Internet services, offering benefits ranging from load balancing to Denial of Service (DoS) protection. A known shortcoming of such proxies is that the backend server becomes oblivious to the IP address of the client who initiated the connection since all requests are forwarded by the proxy server. For HTTP, this issue is trivially solved by the X-Forwarded-For header, which allows the proxy server to pass to the backend server the IP address of the client that originated the request. Unfortunately, no such equivalent exists for many other protocols. To solve this issue, HAProxy created the PROXY protocol, which communicates client information from a proxy server to a backend server at a lower level in the network stack (Layer 4), making it protocol-agnostic.In this work, we are the first to study the use of the PROXY protocol at Internet scale and investigate the security impact of its misconfigurations. We launched a measurement study on the full IPv4 address range and found that, over HTTP, more than 170,000 hosts accept PROXY protocol data from arbitrary sources. We demonstrate how to abuse this protocol to bypass on-path proxies (and their protections) and leak sensitive information from backend infrastructures. We discovered over 10,000 servers that are vulnerable to an access bypass, triggered by injecting a (spoofed) PROXY protocol header. Using this technique, we obtained access to over 500 internal servers providing control over IoT monitoring platforms and smart home automation devices, allowing us to, for example, regulate remote controlled window blinds or control security cameras and alarm systems. Beyond HTTP, we demonstrate how the PROXY protocol can be used to turn over 350 SMTP servers into open relays, enabling an attacker to send arbitrary emails from any email address. In sum, our study exposes how PROXY protocol misconfigurations lead to severe security issues that affect multiple protocols prominently used in the wild.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190083", "vector": [], "sparse_vector": [], "title": "Automated Expansion of Privacy Data Taxonomy for Compliant Data Breach Notification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In privacy compliance research, a significant challenge lies in comparing specific data items in actual data usage practices with the privacy data defined in laws, regulations, or policies. This task is complex due to the diversity of data items used by various applications, as well as the different interpretations of privacy data across jurisdictions. To address this challenge, privacy data taxonomies have been constructed to capture relationships between privacy data types and granularity levels, facilitating privacy compliance analysis. However, existing taxonomy construction approaches are limited by manual efforts or heuristic rules, hindering their ability to incorporate new terms from diverse domains. In this paper, we present the design of GRASP, a scalable and efficient methodology for automatically constructing and expanding privacy data taxonomies. GRASP incorporates a novel hypernym prediction model based on granularity-aware semantic projection, which outperforms existing state-of-the-art hypernym prediction methods. Additionally, we design and implement <PERSON>, a privacy professional assistant to recognize and interpret private data in incident reports for GDPR-compliant data breach notification. We evaluate <PERSON> in a usability study with 15 privacy professionals, yielding high-level usability and satisfaction.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190084", "vector": [], "sparse_vector": [], "title": "A Systematic Evaluation of Novel and Existing Cache Side Channels.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "CPU caches are among the most widely studied side-channel targets, with Prime+Probe and Flush+Reload being the most prominent techniques. These generic cache attack techniques can leak cryptographic keys, user input, and are a building block of many microarchitectural attacks. In this paper, we present the first systematic evaluation using 9 characteristics of the 4 most relevant cache attacks, Flush+Reload, Flush+Flush, Evict+Reload, and Prime+Probe, as well as three new attacks that we introduce: Demote+Reload, Demote+Demote, and DemoteContention. We evaluate hit-miss margins, temporal precision, spatial precision, topological scope, attack time, blind spot length, channel capacity, noise resilience, and detectability on recent Intel microarchitectures. Demote+Reload and Demote+Demote perform similar to previous attacks and slightly better in some cases, e.g., Demote+Reload has a 60.7 % smaller blind spot than Flush+Reload. With 15.48 Mbit/s, Demote+Reload has a 64.3 % higher channel capacity than Flush+Reload. We also compare all attacks in an AES T-table attack and compare Demote+Reload and Flush+Reload in an inter-keystroke timing attack. Beyond the scope of the prior attack techniques, we demonstrate a KASLR break with Demote+Demote and the amplification of power side-channel leakage with Demote+Reload. Finally, Sapphire Rapids and Emerald Rapids CPUs use a non-inclusive L3 cache, effectively limiting eviction-based cross-core attacks, e.g., Prime+Probe and Evict+Reload, to rare cases where the victim’s activity reaches the L3 cache. Hence, we show that in a cross-core attack, DemoteContention can be used as a reliable alternative to Prime+Probe and Evict+Reload that does not require reverse-engineering of addressing functions and cache replacement policy.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190085", "vector": [], "sparse_vector": [], "title": "GhostShot: Manipulating the Image of CCD Cameras with Electromagnetic Interference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Xiaoyu Ji", "<PERSON><PERSON> Xu"], "summary": "CCD cameras are critical in professional and scientific applications where high-quality image data are required, and the reliability of the captured images forms the basis for trustworthy computer vision systems. Previous work shows the feasibility of using intentional electromagnetic interference (IEMI) to inject unnoticeable image changes into CCD cameras. In this work, we design an attack of enhanced capability, GhostShot, that can inject any grayscale or colored images into CCD cameras under normal light conditions with IEMI. We conduct a schematic analysis of the causality of the IEMI effect on the shapes, brightness, and colors of the injected images, and achieve effective control of the injected pattern through amplitude-phase modulation. We design an end-to-end attack workflow and successfully validate the attack on 15 commercial CCD cameras. We demonstrate the potential impact of GhostShot on medical diagnosis, fire detection, QR code scanning and object detection and find that the falsified images can successfully mislead computer vision systems and even human eyes.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190086", "vector": [], "sparse_vector": [], "title": "SafeSplit: A Novel Defense Against Client-Side Backdoor Attacks in Split Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Split Learning (SL) is a distributed deep learning approach enabling multiple clients and a server to collaboratively train and infer on a shared deep neural network (DNN) without requiring clients to share their private local data. The DNN is partitioned in SL, with most layers residing on the server and a few initial layers and inputs on the client side. This configuration allows resource-constrained clients to participate in training and inference. However, the distributed architecture exposes SL to backdoor attacks, where malicious clients can manipulate local datasets to alter the DNN's behavior. Existing defenses from other distributed frameworks like Federated Learning are not applicable, and there is a lack of effective backdoor defenses specifically designed for SL. We present SafeSplit, the first defense against client-side backdoor attacks in Split Learning (SL). SafeSplit enables the server to detect and filter out malicious client behavior by employing circular backward analysis after a client's training is completed, iteratively reverting to a trained checkpoint where the model under examination is found to be benign. It uses a two-fold analysis to identify client-induced changes and detect poisoned models. First, a static analysis in the frequency domain measures the differences in the layer's parameters at the server. Second, a dynamic analysis introduces a novel rotational distance metric that assesses the orientation shifts of the server's layer parameters during training. Our comprehensive evaluation across various data distributions, client counts, and attack scenarios demonstrates the high efficacy of this dual analysis in mitigating backdoor attacks while preserving model utility.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190087", "vector": [], "sparse_vector": [], "title": "LeakLess: Selective Data Protection against Memory Leakage Attacks for Serverless Platforms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the use of language-level sandboxing for running untrusted code grows, the risks associated with memory disclosure vulnerabilities and transient execution attacks become increasingly significant. Besides the execution of untrusted JavaScript or WebAssembly code in web browsers, serverless environments have also started relying on language-level isolation to improve scalability by running multiple functions from different customers within a single process. Web browsers have adopted process-level sandboxing to mitigate memory leakage attacks, but this solution is not applicable in serverless environments, as running each function as a separate process would negate the performance benefits of language-level isolation. In this paper we present LeakLess, a selective data protection approach for serverless computing platforms. LeakLess alleviates the limitations of previous selective data  protection techniques by combining in-memory encryption with a separate I/O module to enable the safe transmission of the protected data between serverless functions and external hosts. We implemented LeakLess on top of the Spin serverless platform, and evaluated it with real-world serverless applications. Our results demonstrate that LeakLess offers robust protection while incurring a minor throughput decrease under stress-testing conditions of up to 2.8% when the I/O module runs on a different host than the Spin runtime, and up to 8.5% when it runs on the same host.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190088", "vector": [], "sparse_vector": [], "title": "Was This You? Investigating the Design Considerations for Suspicious Login Notifications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many online platforms monitor the account login activities of their users to detect unauthorized login attempts. Upon detecting anomalous activity, these platforms send suspicious login notifications to their users. These notifications serve to inform users about the login activity in sufficient detail for them to ascertain its legitimacy and take remedial actions if necessary. Despite the prevalence of these notifications, limited research has explored how users engage with them and how they can be effectively designed. In this paper, we examine user engagement with email-based suspicious login notifications, focusing on real-world practices. We collect and analyze notifications currently in use to establishan empirical foundation for common design elements. We focus our study on designs used by online platforms rather than exploring all possible design options. Thus, these design optionsare likely supported by real-world online platforms based on the login data they can realistically provide. Then, we investigate how these design elements influence users to read the notification, validate its authenticity, diagnose the login attempt, and determine appropriate remedial steps. By conducting online semi-structured interviews with 20 US-based participants, we investigate theirpast experiences and present them with design elements employed by top online platforms to identify what design elements work best. Our findings highlight the practical design options thatenhance users’ understanding and engagement, providing recommendations for deploying effective notifications and identifying future directions for the security community.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190089", "vector": [], "sparse_vector": [], "title": "On the Realism of LiDAR Spoofing Attacks against Autonomous Driving Vehicle at High Speed and Long Distance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The rapid deployment of Autonomous Driving (AD) technologies on public roads presents significant social challenges. The security of LiDAR (Light Detection and Ranging) is one of the emerging challenges in AD deployment, given its crucial role in enabling Level 4 autonomy through accurate 3D environmental sensing. Recent lines of research have demonstrated that LiDAR can be compromised by LiDAR spoofing attacks that overwrite legitimate sensing by emitting malicious lasers to the LiDAR. However, previous studies have successfully demonstrated their attacks in controlled environments, yet gaps exist in the feasibility of their attacks in realistic high-speed, long-distance AD scenarios. To bridge these gaps, we design a novel Moving Vehicle Spoofing (MVS) system consisting of 3 subsystems: the LiDAR detection and tracking system, the auto-aiming system, and the LiDAR spoofing system. Furthermore, we design a new object removal attack, an adaptive high-frequency removal (A-HFR) attack, that can be effective even against recent LiDARs with pulse fingerprinting features, by leveraging gray-box knowledge of the scan timing of target LiDARs. With our MVS system, we are not only the first to demonstrate LiDAR spoofing attacks against practical AD scenarios where the victim vehicle is driving at high speeds (60 km/h) and the attack is launched from long distances (110 meters), but we are also the first to perform LiDAR spoofing attacks against a vehicle actually operated by a popular AD stack. Our object removal attack achieves ≥96% attack success rates against the vehicle driving at 60 km/h to the braking distances (20 meters). Finally, we discuss possible countermeasures against attacks with our MVS system. This study not only bridges critical gaps between LiDAR security and AD security research but also sets a foundation for developing robust countermeasures against emerging threats.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190090", "vector": [], "sparse_vector": [], "title": "Alba: The Dawn of Scalable Bridges for Blockchains.", "authors": ["Giulia <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zeta Avarikioti", "<PERSON>"], "summary": "Over the past decade, cryptocurrencies have garnered attention from academia and industry alike, fostering a diverse blockchain ecosystem and novel applications. The inception of bridges improved interoperability, enabling asset transfers across different blockchains to capitalize on their unique features. Despite their surge in popularity and the emergence of Decentralized Finance (DeFi), trustless bridge protocols remain inefficient, either relaying too much information (e.g., light-client-based bridges) or demanding expensive computation (e.g., zk-based bridges). These inefficiencies arise because existing bridges securely prove a transaction's on-chain inclusion on another blockchain. Yet this is unnecessary as off-chain solutions, like payment and state channels, permit safe transactions without on-chain publication. However, existing bridges do not support the verification of off-chain payments. This paper fills this gap by introducing the concept of Pay2Chain bridges that leverage the advantages of off-chain solutions like payment channels to overcome current bridges' limitations. Our proposed Pay2Chain bridge, named Alba, facilitates the efficient, secure, and trustless execution of conditional payments or smart contracts on a target blockchain based on off-chain events. Alba, besides its technical advantages, enriches the source blockchain's ecosystem by facilitating DeFi applications, multi-asset payment channels, and optimistic stateful off-chain computation. We formalize the security of Alba against Byzantine adversaries in the UC framework and complement it with a game theoretic analysis. We further introduce formal scalability metrics to demonstrate Alba's efficiency. Our empirical evaluation confirms Alba's efficiency in terms of communication complexity and on-chain costs, with its optimistic case incurring only twice the cost of a standard Ethereum transaction of token ownership transfer.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190091", "vector": [], "sparse_vector": [], "title": "CHAOS: Exploiting Station Time Synchronization in 802.11 Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Many locations, especially in urban areas, are quite noisy with WiFi traffic. In addition to data traffic, WiFi stations send management and control frames that can easily exceed several hundred frames per second just in one small area. These WiFi environments present the opportunity to transmit data through hiding it within the noise components that can be normal parts of benign transmissions.In this paper, we show how one particular feature of WiFi, the Timing Synchronization Function (TSF), can be exploited to create a fertile and robust channel for embedding secret signals. We take advantage of the fact that there is always some degree of imprecision reflected in time synchronization of WiFi stations.We present CHAOS, a new covert channel strategy to embed data bits in WiFi beacon frames using unmodified standard WiFi hardware. CHAOS makes use of the noise properties inherent in WiFi in two ways: First, it encodes information in the ordering of beacon frames, taking advantage of the fact that there is no natural or required ordering of beacons. Second, it makes use of a timing channel in the form of the TSF timestamp in management headers, imitating the natural imprecision of timing in real base stations to encode data in a way that is statistically similar to unmodified frames. CHAOS's parameters can be adjusted to configure data rate, the covert channel stability and frame miss rate; using our suggested settings, it is able to robustly broadcast secret data at 520 bits/s. We also show that TSF has substantial potential for further exploitation, sketching a correlation attack that uses it to map clients to base stations.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190092", "vector": [], "sparse_vector": [], "title": "Defending Against Membership Inference Attacks on Iteratively Pruned Deep Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Zim<PERSON>"], "summary": "Model pruning is a technique for compressing deep learning models, and using an iterative way to prune the model can achieve better compression effects with lower utility loss. However, our analysis reveals that iterative pruning significantly increases model memorization, making the pruned models more vulnerable to membership inference attacks (MIAs). Unfortunately, the vast majority of existing defenses against MIAs are designed for original and unpruned models. In this paper, we propose a new framework WeMem to weaken memorization in the iterative pruning process. Specifically, our analysis identifies two important factors that increase memorization in iterative pruning, namely data reuse and inherent memorability. We consider the individual and combined impacts of both factors, forming three scenarios that lead to increased memorization in iteratively pruned models. We design three defense primitives based on these factors' characteristics. By combining these primitives, we propose methods tailored to each scenario to weaken memorization effectively. Comprehensive experiments under ten adaptive MIAs demonstrate the effectiveness of the proposed defenses. Moreover, our defenses outperform five existing defenses in terms of privacy-utility tradeoff and efficiency. Additionally, we enhance the proposed defenses to automatically adjust settings for optimal defense, improving their practicability.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190093", "vector": [], "sparse_vector": [], "title": "Scale-MIA: A Scalable Model Inversion Attack against Secure Federated Learning via Latent Space Reconstruction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning is known for its capability to safeguard the participants' data privacy. However, recently emerged model inversion attacks (MIAs) have shown that a malicious parameter server can reconstruct individual users' local data samples from model updates. The state-of-the-art attacks either rely on computation-intensive iterative optimization methods to reconstruct each input batch, making scaling difficult, or involve the malicious parameter server adding extra modules before the global model architecture, rendering the attacks too conspicuous and easily detectable. To overcome these limitations, we propose Scale-MIA, a novel MIA capable of efficiently and accurately reconstructing local training samples from the aggregated model updates, even when the system is protected by a robust secure aggregation (SA) protocol. Scale-MIA utilizes the inner architecture of models and identifies the latent space as the critical layer for breaching privacy. Scale-MIA decomposes the complex reconstruction task into an innovative two-step process. The first step is to reconstruct the latent space representations (LSRs) from the aggregated model updates using a closed-form inversion mechanism, leveraging specially crafted linear layers. Then in the second step, the LSRs are fed into a fine-tuned generative decoder to reconstruct the whole input batch. We implemented Scale-MIA on commonly used machine learning models and conducted comprehensive experiments across various settings. The results demonstrate that Scale-MIA achieves excellent performance on different datasets, exhibiting high reconstruction rates, accuracy, and attack efficiency on a larger scale compared to state-of-the-art MIAs. Our code is available at https://github.com/unknown123489/Scale-MIA.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190094", "vector": [], "sparse_vector": [], "title": "Eclipse Attacks on Monero&apos;s Peer-to-Peer Network.", "authors": ["R<PERSON>sheng Shi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Eclipse attack is a major threat to the blockchain network layer, wherein an attacker isolates a target node by monopolizing all its connections, cutting it off from the rest of the network. Despite the attack's demonstrated effectiveness in Bitcoin (Usenix'15, SP'20, Usenix'21, CCS'21, SP'23) and partially in Ethereum (NDSS'23, SP'23), its applicability to a wider range of blockchain systems remains uncertain. In this paper, we investigate eclipse attacks against Monero, a blockchain system known for its strong anonymity and pioneering the use of Dandelion++ (the state-of-the-art blockchain network layer protocol for transaction privacy protection). Our analysis of Monero's connection management mechanism reveals that existing eclipse attacks are surprisingly ineffective against Monero. We accordingly introduce the first practical eclipse attack against Monero by proposing a connection reset approach, which forces the target node to drop all benign connections and reconnect with malicious nodes. Specifically, we outline two methods for executing such an attack. The first one exploits the private transaction mechanisms, while the second method leverages the differences in propagation between stem transactions and fluff transactions under Dandelion++. Our attack is not only applicable to Monero but to all blockchain systems utilizing Dandelion++ and similar connection management strategies. We conduct experiments on the Monero mainnet. Evaluation results confirm the feasibility of our attack. Unlike existing eclipse attacks, our connection reset-based approach does not require restarting the target node, significantly accelerating the attack process and making it more controllable. We also provide countermeasures to mitigate the proposed eclipse attack while minimizing the impact on Monero. In addition, we have ethically reported our investigation to Monero official team.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190095", "vector": [], "sparse_vector": [], "title": "The Skeleton Keys: A Large Scale Analysis of Credential Leakage in Mini-apps.", "authors": ["Yiz<PERSON> Shi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, the app-in-app paradigm, involving super-app and mini-app, has been becoming increasingly popular in the mobile ecosystem. Super-app platforms offer mini-app servers access to a suite of powerful and sensitive services, including payment processing and mini-app analytics. This access empowers mini-app servers to enhance their offerings with robust and practical functionalities and better serve their mini-apps. To safeguard these essential services, a credential-based authentication system has been implemented, facilitating secure access between super-app platforms and mini-app servers. However, the design and workflow of the crucial credential mechanism still remain unclear. More importantly, its security has not been comprehensively understood or explored to date. In this paper, we conduct the first systematic study of the credential system in the app-in-app paradigm and draw the security landscape of credential leakage risks. Consequently, our study shows that 21 popular super-app platforms delegate sensitive services to mini-app servers with seven types of credentials. Unfortunately, these credentials may suffer from leakage threats caused by malicious mini-app users, posing serious security threats to both super-app platforms and mini-app servers. Then, we design and implement a novel credential security verification tool, called KeyMagnet, that can effectively assess the security implications of credential leakage. To tackle unstructured and dynamically retrieved credentials in the app-in-app paradigm, KeyMagnet extracts and understands the semantics of credential-use in mini-apps and verifies their security. Last, by applying KeyMagnet on 413,775 real-world mini-apps of 6 super-app platforms, 84,491 credential leaks are detected, spanning over 54,728 mini-apps. We confirm credential leakage can cause serious security hazards, such as hijacking the accounts of all mini-app users and stealing users' sensitive data. In response, we have engaged in responsible vulnerability disclosure with the corresponding developers and are actively helping them resolve these issues. At the time of writing, 89 reported issues have been assigned with CVE IDs.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190096", "vector": [], "sparse_vector": [], "title": "Duumviri: Detecting Trackers and Mixed Trackers with a Breakage Detector.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Web tracking harms user privacy. As a result, the use of tracker detection and blocking tools is a common practice among Internet users. However, no such tool can be perfect, and thus there is a trade-off between avoiding breakage (caused by unintentionally blocking some required functionality) and neglecting to block some trackers. State-of-the-art tools usually rely on user reports and developer effort to detect breakages, which can be broadly categorized into two causes: 1) misidentifying non-trackers as trackers, and 2) blocking mixed trackers which blend tracking with functional components. We propose incorporating a machine learning-based break- age detector into the tracker detection pipeline to automatically avoid misidentification of functional resources. For both tracker detection and breakage detection, we propose using differential features that can more clearly elucidate the differences caused by blocking a request. We designed and implemented a prototype of our proposed approach, Duumviri, for non-mixed trackers. We then adopt it to automatically identify mixed trackers, drawing differential features at partial-request granularity. In the case of non-mixed trackers, evaluating Duumviri on 15K pages shows its ability to replicate the labels of human-generated filter lists, EasyPrivacy, with an accuracy of 97.44%. Through a manual analysis, we find that Duumviri can identify previously unreported trackers and its breakage detector can identify overly strict EasyPrivacy rules that cause breakage. In the case of mixed trackers, <PERSON><PERSON><PERSON><PERSON> is the first automated mixed tracker detector, and achieves a lower bound accuracy of 74.19%. Duumviri has enabled us to detect and confirm 22 previously unreported unique trackers and 26 unique mixed trackers.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190097", "vector": [], "sparse_vector": [], "title": "Detecting Ransomware Despite I/O Overhead: A Practical Multi-Staged Approach.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ransomware attacks have become one of the most widely feared cyber attacks for businesses and home users.Since attacks are evolving and use advanced phishing campaigns and zero-day exploits, everyone is at risk, ranging from novice users to experts.As a result, much research has focused on preventing and detecting ransomware attacks, with real-time monitoring of I/O activity being the most prominent approach for detection.These approaches have in common that they inject code into the execution of the operating system's I/O stack, a more and more optimized system.However, they seemingly do not consider the impact the integration of such mechanisms would have on system performance or only consider slow storage mediums, such as rotational hard disk drives.This paper analyzes the impact of monitoring different features of relevant I/O operations for Windows and Linux.We find that even simple features, such as the entropy of a buffer, can increase execution time by 350% and reduce SSD performance by up to 75%.To combat this degradation, we propose adjusting the number of monitored features based on a process's behavior in real-time.To this end, we design and implement a multi-staged IDS that can adjust overhead by moving a process between stages that monitor different numbers of features.By moving seemingly benign processes to stages with fewer features and less overhead while moving suspicious processes to stages with more features to confirm the suspicion, the average time a system requires to perform I/O operations can be reduced drastically.We evaluate the effectiveness of our design by combining actual I/O behavior from a public dataset with the measurements we gathered for each I/O operation and found that a multi-staged design can reduce the overhead to I/O operations by an order of magnitude while maintaining similar detection accuracy of traditional single-staged approaches.As a result, real-time behavior monitoring for ransomware detection becomes feasible despite its inherent overhead impacts.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190098", "vector": [], "sparse_vector": [], "title": "Silence False Alarms: Identifying Anti-Reentrancy Patterns on Ethereum to Refine Smart Contract Reentrancy Detection.", "authors": ["<PERSON><PERSON> Song", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reentrancy vulnerabilities in Ethereum smart contracts have caused significant financial losses, prompting the creation of several automated reentrancy detectors. However, these detectors frequently yield a high rate of false positives due to coarse detection rules, often misclassifying contracts protected by anti-reentrancy patterns as vulnerable. Thus, there is a critical need for the development of specialized automated tools to assist these detectors in accurately identifying anti-reentrancy patterns. While existing code analysis techniques show promise for this specific task, they still face significant challenges in recognizing anti-reentrancy patterns. These challenges are primarily due to the complex and varied features of anti-reentrancy patterns, compounded by insufficient prior knowledge about these features. This paper introduces AutoAR, an automated recognition system designed to explore and identify prevalent anti-reentrancy patterns in Ethereum contracts. AutoAR utilizes a specialized graph representation, RentPDG, combined with a data filtration approach, to effectively capture anti-reentrancy-related semantics from a large pool of contracts. Based on RentPDGs extracted from these contracts, AutoAR employs a recognition model that integrates a graph auto-encoder with a clustering technique, specifically tailored for precise anti-reentrancy pattern identification. Experimental results show AutoAR can assist existing detectors in identifying 12 prevalent anti-reentrancy patterns with 89% accuracy, and when integrated into the detection workflow, it significantly reduces false positives by over 85%.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190099", "vector": [], "sparse_vector": [], "title": "Oreo: Protecting ASLR Against Microarchitectural Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Address Space Layout Randomization (ASLR) is one of the most prominently deployed mitigations against memory corruption attacks. ASLR randomly shuffles program virtual addresses to prevent attackers from knowing the location of program contents in memory. Microarchitectural side channels have been shown to defeat ASLR through various hardware mechanisms. We systematically analyze existing microarchitectural attacks and identify multiple leakage paths. Given the vast attack surface exposed by ASLR, it is challenging to effectively prevent leaking the ASLR secret against microarchitectural attacks. Motivated by this, we present <PERSON><PERSON>, a software-hardware co-design mitigation that strengthens ASLR against these attacks. <PERSON><PERSON> uses a new memory mapping interface to remove secret randomized bits in virtual addresses before translating them to their corresponding physical addresses. This extra step hides randomized virtual addresses from microarchitecture structures, preventing side channels from leaking ASLR secrets. Oreo is transparent to user programs and incurs low overhead. We prototyped and evaluated our design on Linux using the hardware simulator gem5.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190100", "vector": [], "sparse_vector": [], "title": "YuraScanner: Leveraging LLMs for Task-driven Web App Scanning.", "authors": ["Alek<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Web application scanners are popular and effective black-box testing tools, automating the detection of vulnerabilities by exploring and interacting with user interfaces. Despite their effectiveness, these scanners struggle with discovering deeper states in modern web applications due to their limited understanding of workflows. This study addresses this limitation by introducing <PERSON>raScanner, a task-driven web application scanner that leverages large-language models (LLMs) to autonomously execute tasks and workflows. YuraScanner operates as a goal-based agent, suggesting actions to achieve predefined objectives by processing webpages to extract semantic information. Unlike traditional methods that rely on user-provided traces, YuraScanner uses LLMs to bridge the semantic gap, making it web application-agnostic. Using the XSS engine of Black Widow, YuraScanner tests discovered input points for vulnerabilities, enhancing the scanning process's comprehensiveness and accuracy. We evaluated YuraScanner on 20 diverse web applications, focusing on task extraction, execution accuracy, and vulnerability detection. The results demonstrate <PERSON>raScanner's superiority in discovering new attack surfaces and deeper states, significantly improving vulnerability detection. Notably, <PERSON>raScanner identified 12 unique zero-day XSS vulnerabilities, compared to three by <PERSON> Widow. This study highlights YuraScanner's potential to revolutionize web application scanning with its automated, task-driven approach.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190101", "vector": [], "sparse_vector": [], "title": "A Multifaceted Study on the Use of TLS and Auto-detect in Email Ecosystems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Various email protocols, including IMAP, POP3, and SMTP, were originally designed as “plaintext” protocols without inbuilt confidentiality and integrity guarantees. To protect the communication traffic, TLS can either be used implicitly before the start of those email protocols, or introduced as an opportunistic upgrade in a post-hoc fashion. In order to improve user experience, many email clients nowadays provide a so-called “auto-detect” feature to automatically determine a functional set of configuration parameters for the users. In this paper, we present a multifaceted study on the security of the use of TLS and auto-detect in email clients. First, to evaluate the design and implementation of client-side TLS and auto-detect, we tested 49 email clients and uncovered various flaws that can lead to covert security downgrade and exposure of user credentials to attackers. Second, to understand whether current deployment practices adequately avoid the security traps introduced by opportunistic TLS and auto-detect, we collected and analyzed 1102 email setup guides from academic institutes across the world, and observed problems that can drive users to adopt insecure email settings. Finally, with the server addresses obtained from the setup guides, we evaluate the sever-side support for implicit and opportunistic TLS, as well as the characteristics of their certificates. Our results suggest that many users suffer from an inadvertent loss of security due to careless handling of TLS and auto-detect, and organizations in general are better off prescribing concrete and detailed manual configuration to their users.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190102", "vector": [], "sparse_vector": [], "title": "Be Careful of What You Embed: Demystifying OLE Vulnerabilities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Microsoft Office is a comprehensive suite of productivity tools and Object Linking & Embedding (OLE) is a specification that standardizes the linking and embedding of a diverse set of objects across different applications.OLE facilitates data interchange and streamlines user experience when dealing with composite documents (e.g., an embedded Excel sheet in a Word document). However, inherent security weaknesses within the design of OLE present risks, as the design of OLE inherently blurs the trust boundary between first-party and third-party code, which may lead to unintended library loading and parsing vulnerabilities which could be exploited by malicious actors. Addressing this issue, this paper introduces OLExplore, a novel tool designed for security assessment of Office OLE objects.With an in-depth examination of historical OLE vulnerabilities, we have identified three key categories of vulnerabilities and subjected them to dynamic analysis and verification. Our evaluation of various Windows operating system versions has led to the discovery of 26 confirmed vulnerabilities, with 17 assigned CVE numbers that all have remote code execution potential.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190103", "vector": [], "sparse_vector": [], "title": "Density Boosts Everything: A One-stop Strategy for Improving Performance, Robustness, and Sustainability of Malware Detectors.", "authors": ["<PERSON><PERSON><PERSON>", "Wei Kong", "<PERSON><PERSON>", "<PERSON>", "Taotao Gu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the contemporary landscape of cybersecurity, AI-driven detectors have emerged as pivotal in the realm of malware detection. However, existing AI-driven detectors encounter a myriad of challenges, including poisoning attacks, evasion attacks, and concept drift, which stem from the inherent characteristics of AI methodologies. While numerous solutions have been proposed to address these issues, they often concentrate on isolated problems, neglecting the broader implications for other facets of malware detection. This paper diverges from the conventional approach by not targeting a singular issue but instead identifying one of the fundamental causes of these challenges, sparsity. Sparsity refers to a scenario where certain feature values occur with low frequency, being represented only a minimal number of times across the dataset. The authors are the first to elevate the significance of sparsity and link it to core challenges in the domain of malware detection, and then aim to improve performance, robustness, and sustainability simultaneously by solving sparsity problems. To address the sparsity problems, a novel compression technique is designed to effectively alleviate the sparsity. Concurrently, a density boosting training method is proposed to consistently fill sparse regions. Empirical results demonstrate that the proposed methodologies not only successfully bolster the model's resilience against different attacks but also enhance the performance and sustainability over time. Moreover, the proposals are complementary to existing defensive technologies and successfully demonstrate practical classifiers with improved performance and robustness to attacks.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190104", "vector": [], "sparse_vector": [], "title": "Horcrux: Synthesize, Split, Shift and Stay Alive; Preventing Channel Depletion via Universal and Enhanced Multi-hop Payments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Payment Channel Networks (PCNs) have been highlighted as viable solutions to address the scalability issues in current permissionless blockchains. They facilitate off-chain transactions, significantly reducing the load on the blockchain. However, the extensive reuse of multi-hop routes in the same direction poses a risk of channel depletion, resulting in involved channels becoming unidirectional or even closing, thereby compromising the sustainability and scalability of PCNs. Even more concerning, existing rebalancing protocol solutions heavily rely on trust assumptions and scripting languages, resulting in compromised universality and reliability. In this paper, we present Horcrux, a universal and efficient multi-party virtual channel protocol without relying on extra trust assumptions, scripting languages, or the perpetual online requirement. Horcrux fundamentally addresses the channel depletion problem using a novel approach termed textit{flow neutrality}, which minimizes the impact on channel balance allocations during multi-hop payments (MHPs). Additionally, we formalize the security properties of Horcrux by modeling it within the Global Universal Composability framework and provide a formal security proof. We implement Horcrux on a real Lightning Network dataset, comprising 10,529 nodes and 38,910 channels, and compare it to the state-of-the-art rebalancing schemes such as Shaduf [NDSS'22], Thora [CCS'22], and Revive [CCS'17]. The experimental results demonstrate that (1) the entire process of Horcrux costs less than 1 USD, significantly lower than <PERSON><PERSON><PERSON>; (2) Horcrux achieves a $12%$-$30%$ increase in payment success ratio and reduces user deposits required for channels by $70%$-$91%$; (3) the performance of Horcrux improves by $1.2x$-$1.5x$ under long-term operation; and (4) Horcrux maintains a nearly zero channel depletion rate, whereas both Revive and Shaduf result in thousands of depleted channels.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190105", "vector": [], "sparse_vector": [], "title": "Cascading Spy Sheets: Exploiting the Complexity of Modern CSS for Email and Browser Fingerprinting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In an attempt to combat user tracking, both privacy-aware browsers (e.g., Tor) and email applications usually disable JavaScript. This effectively closes a major angle for user fingerprinting.However, recent findings hint at the potential for privacy leakage through selected Cascading Style Sheets (CSS) features. Nevertheless, the full fingerprinting potential of CSS remains unknown, and it is unclear if attacks apply to more restrictive settings such as email. In this paper, we systematically investigate the modern dynamic features of CSS and their applicability for script-less fingerprinting, bypassing many state-of-the-art mitigations. We present three innovative techniques based on fuzzing and templating that exploit nuances in CSS container queries, arithmetic functions, and complex selectors. This allows us to infer detailed application, OS, and hardware configurations at high accuracy. For browsers, we can distinguish 97.95% of 1176 tested browser-OS combinations. Our methods also apply to email applications - as shown for 8 out of 21 tested web, desktop or mobile email applications. This demonstrates that fingerprinting is possible in the highly restrictive setting of HTML emails and expands the scope of tracking beyond traditional web environments. In response to these and potential future CSS-based tracking capabilities, we propose two defense mechanisms that eliminate the root causes of privacy leakage. For browsers, we propose to preload conditional resources, which eliminates feature-dependent leakage. For the email setting, we design an email proxy service that retains privacy and email integrity while largely preserving feature compatibility. Our work provides new insights and solutions to the ongoing privacy debate, highlighting the importance of robust defenses against emerging tracking methods.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190106", "vector": [], "sparse_vector": [], "title": "Rediscovering Method Confusion in Proposed Security Fixes for Bluetooth.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous works have shown that Bluetooth is susceptible to so-called Method Confusion attacks. These attacks manipulate devices into conducting conflicting key establishment methods, leading to compromised keys. An increasing amount of security-sensitive applications, like payment terminals, organizational asset tracking systems and conferencing technologies now rely on the availability of a technology like Bluetooth.It is thus an urgent goal to find and validate a mitigation to these attacks or to provide an appropriate replacement for Bluetooth without introducing additional requirementsthat exclude device or user groups.Despite recent solution proposals, existing threat models overlook certain attack vectors or dismiss important scenarios and consequently suffer under new variants of Method Confusion. We first propose an extended threat model that appreciates the root issue of Method Confusion and also considers multiple pairing attempts and one-sided pairings as security risks.Evaluating existing solution proposals with our threat model, we are able to detect known Method Confusion attacks, and identify new vulnerabilities in previous solution proposals.We demonstrate the viability of these attacks on real-world Bluetooth devices. We further discuss a novel solution approach offering enhanced security, while maintaining compatibility with existing hardware and Bluetooth user behavior.We conduct a formal security proof of our proposal and implement it on commonplace Bluetooth hardware, positioning it as the currently most promising update proposal for Bluetooth.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190107", "vector": [], "sparse_vector": [], "title": "Detecting IMSI-Catchers by Characterizing Identity Exposing Messages in Cellular Traffic.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "IMSI-Catchers allow parties other than cellular network providers to covertly track mobile device users. While the research community has developed many tools to combat this problem, current solutions focus on correlated behavior and are therefore subject to substantial false classifications. In this paper, we present a standards-driven methodology that focuses on the messages an IMSI-Catcher textit{must} use to cause mobile devices to provide their permanent identifiers. That is, our approach focuses on causal attributes rather than correlated ones. We systematically analyze message flows that would lead to IMSI exposure (most of which have not been previously considered in the research community), and identify 53 messages an IMSI-Catcher can use for its attack. We then perform a measurement study on two continents to characterize the ratio in which connections use these messages in normal operations. We use these benchmarks to compare against open-source IMSI-Catcher implementations and then observe anomalous behavior at a large-scale event with significant media attention. Our analysis strongly implies the presence of an IMSI-Catcher at said public event ($p << 0.005$), thus representing the first publication to provide evidence of the statistical significance of its findings.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190108", "vector": [], "sparse_vector": [], "title": "TME-Box: Scalable In-Process Isolation through Intel TME-MK Memory Encryption.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Efficient cloud computing relies on in-process isolation to optimize performance by running workloads within a single process. Without heavy-weight process isolation, memory safety errors pose a significant security threat by allowing an adversary to extract or corrupt the private data of other co-located tenants. Existing in-process isolation mechanisms are not suitable for modern cloud requirements, e.g., MPK’s 16 protection domains are insufficient to isolate thousands of cloud workers per process. Consequently, cloud service providers have a strong need for lightweight in-process isolation on commodity x86 machines. This paper presents TME-Box, a novel isolation technique that enables fine-grained and scalable sandboxing on commodity x86 CPUs. By repurposing Intel TME-MK, which is intended for the encryption of virtual machines, TME-Box offers lightweight and efficient in-process isolation. TME-Box enforces that sandboxes use their designated encryption keys for memory interactions through compiler instrumentation. This cryptographic isolation enables fine-grained access control, from single cache lines to full pages, and supports flexible data relocation. In addition, the design of TME-Box allows the efficient isolation of up to 32K concurrent sandboxes. We present a performance-optimized TME-Box prototype, utilizing x86 segment-based addressing, that showcases geomean performance overheads of 5.2 % for data isolation and 9.7 % for code and data isolation, evaluated with the SPEC CPU2017 benchmark suite.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190109", "vector": [], "sparse_vector": [], "title": "ICSQuartz: Scan Cycle-Aware and Vendor-Agnostic Fuzzing for Industrial Control Systems.", "authors": ["Corban Villa", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Industrial Control Systems (ICS) ensure the automation and safe operation of critical industry, energy, and commerce processes. Despite its importance, ICS code often cannot be evaluated as rigorously as software on traditional computing platforms, as existing code evaluation tools cannot readily interface with the closed ICS ecosystem. Moreover, the use of domain-specific languages, the lack of open and extensible compilers, and the deficiency of techniques developed for ICS-specific nuances, among other challenges, hinder the creation of specialized tools. This paper addresses these challenges by introducing ICSQuartz, the first native fuzzer for IEC 61131-3 Structured Text (ST), a standardized Programmable Logic Controller (PLC) programming language. Native support eliminates the necessity of any vendor or architecture-specific requirements. ICSQuartz outperforms the fastest state-of-the-art fuzzers in the ICS space by textit{more than an order of magnitude in executions per second}. In addition to natively fuzzing ST code, we introduce novel mutation strategies to ICSQuartz that uncover vulnerabilities due to the scan cycle architecture of ST programs--a nuance that traditional fuzzers do not consider. Using ICSQuartz, we perform the first large-scale fuzzing campaign of real-world ICS libraries, resulting in multiple vulnerability disclosures and bug fixes. In addition to vulnerabilities, ICSQuartz discovered a bug in an open-source ST compiler. These findings underscore the imperative impact of ICSQuartz in the ICS domain.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190110", "vector": [], "sparse_vector": [], "title": "DUMPLING: Fine-grained Differential JavaScript Engine Fuzzing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Web browsers are ubiquitous and execute untrusted JavaScript (JS) code. JS engines optimize frequently executed code through just-in-time (JIT) compilation. Subtly conflicting assumptions between optimizations frequently result in JS engine vulnerabilities. Attackers can take advantage of such diverging assumptions and use the flexibility of JS to craft exploits that produce a miscalculation, remove bounds checks in JIT compiled code, and ultimately gain arbitrary code execution. Classical fuzzing approaches for JS engines only detect bugs if the engine crashes or a runtime assertion fails. Differential fuzzing can compare interpreted code against optimized JIT compiled code to detect differences in execution. Recent approaches probe the execution states of JS programs through ad-hoc JS functions that read the value of variables at runtime. However, these approaches have limited capabilities to detect diverging executions and inhibitoptimizations during JIT compilation, thus leaving JS engines under-tested. We propose DUMPLING, a differential fuzzer that compares the full state of optimized and unoptimized execution for arbitrary JS programs. Instead of instrumenting the JS input, DUMPLING instruments the JS engine itself, enabling deep and precise introspection. These extracted fine-grained execution states, coined as (frame) dumps, are extracted at a high frequency even in the middle of JIT compiled functions. DUMPLING finds eight new bugs in the thoroughly tested V8 engine, where previous differential fuzzing approaches struggled to discover new bugs. We receive $11,000 from Google’s Vulnerability Rewards Program for reporting the vulnerabilities found by DUMPLING.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190111", "vector": [], "sparse_vector": [], "title": "Provably Unlearnable Data Examples.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The exploitation of publicly accessible data has led to escalating concerns regarding data privacy and intellectual property (IP) breaches in the age of artificial intelligence. To safeguard both data privacy and IP-related domain knowledge, efforts have been undertaken to render shared data unlearnable for unauthorized models in the wild. Existing methods apply empirically optimized perturbations to the data in the hope of disrupting the correlation between the inputs and the corresponding labels such that the data samples are converted into Unlearnable Examples (UEs). Nevertheless, the absence of mechanisms to verify the robustness of UEs against uncertainty in unauthorized models and their training procedures engenders several under-explored challenges. First, it is hard to quantify the unlearnability of UEs against unauthorized adversaries from different runs of training, leaving the soundness of the defense in obscurity. Particularly, as a prevailing evaluation metric, empirical test accuracy faces generalization errors and may not plausibly represent the quality of UEs. This also leaves room for attackers, as there is no rigid guarantee of the maximal test accuracy achievable by attackers. Furthermore, we find that a simple recovery attack can restore the clean-task performance of the classifiers trained on UEs by slightly perturbing the learned weights. To mitigate the aforementioned problems, in this paper, we propose a mechanism for certifying the so-called $(q, eta)$-Learnability of an unlearnable dataset via parametric smoothing. A lower certified $(q, eta)$-Learnability indicates a more robust and effective protection over the dataset. Concretely, we 1) improve the tightness of certified $(q, eta)$-Learnability and 2) design Provably Unlearnable Examples (PUEs) which have reduced $(q, eta)$-Learnability. According to experimental results, PUEs demonstrate both decreased certified $(q, eta)$-Learnability and enhanced empirical robustness compared to existing UEs. Compared to the competitors on classifiers with uncertainty in parameters, PUEs reduce at most $18.9%$ of certified $(q, eta)$-Learnability on ImageNet and $54.4%$ of the empirical test accuracy score on CIFAR-100. Our source code is available at https://github.com/NeuralSec/certified-data-learnability.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190112", "vector": [], "sparse_vector": [], "title": "A Key-Driven Framework for Identity-Preserving Face Anonymization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Virtual faces are crucial content in the metaverse. Recently, attempts have been made to generate virtual faces for privacy protection. Nevertheless, these virtual faces either permanently remove the identifiable information or map the original identity into a virtual one, which loses the original identity forever. In this study, we first attempt to address the conflict between privacy and identifiability in virtual faces, where a key-driven face anonymization and authentication recognition (KFAAR) framework is proposed. Concretely, the KFAAR framework consists of a head posture-preserving virtual face generation (HPVFG) module and a key-controllable virtual face authentication (KVFA) module. The HPVFG module uses a user key to project the latent vector of the original face into a virtual one. Then it maps the virtual vectors to obtain an extended encoding, based on which the virtual face is generated. By simultaneously adding a head posture and facial expression correction module, the virtual face has the same head posture and facial expression as the original face. During the authentication, we propose a KVFA module to directly recognize the virtual faces using the correct user key, which can obtain the original identity without exposing the original face image. We also propose a multi-task learning objective to train HPVFG and KVFA. Extensive experiments demonstrate the advantages of the proposed HPVFG and KVFA modules, which effectively achieve both facial anonymity and identifiability.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190113", "vector": [], "sparse_vector": [], "title": "Impact Tracing: Identifying the Culprit of Misinformation in Encrypted Messaging Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Tao <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Encrypted messaging systems obstruct content moderation, although they provide end-to-end security. As a result, misinformation proliferates in these systems, thereby exacerbating online hate and harassment. The paradigm of ``Reporting-then-Tracing\" shows great potential in mitigating the spread of misinformation. For instance, textit{message traceback} (CCS'19) traces all the dissemination paths of a message, while textit{source tracing} (CCS'21) traces its originator.However, message traceback lacks privacy preservation for non-influential users (e.g., users who only receive the message once), while source tracing maintains privacy but only provides limited traceability. In this paper, we initiate the study of textit{impact tracing}. Intuitively, impact tracing traces influential spreaders central to disseminating misinformation while providing privacy protection for non-influential users. We introduce noises to hide non-influential users and demonstrate that these noises do not hinder the identification of influential spreaders. Then, we formally prove our scheme's security and show it achieves differential privacy protection for non-influential users.Additionally, we define three metrics to evaluate its traceability, correctness, and privacy using real-world datasets. The experimental results show that our scheme identifies the most influential spreaders with accuracy from 82% to 99% as the amount of noise varies. Meanwhile, our scheme requires only a 6-byte platform storage overhead for each message while maintaining a low messaging latency ($<$ 0.25ms).", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190114", "vector": [], "sparse_vector": [], "title": "BinEnhance: An Enhancement Framework Based on External Environment Semantics for Binary Code Search.", "authors": ["<PERSON><PERSON>", "Hong Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Kangyuan Qin"], "summary": "Binary code search plays a crucial role in applications like software reuse detection, and vulnerability identification. Currently, existing models are typically based on either internal code semantics or a combination of function call graphs (CG) and internal code semantics. However, these models have limitations. Internal code semantic models only consider the semantics within the function, ignoring the inter-function semantics, making it difficult to handle situations such as function inlining. The combination of CG and internal code semantics is insufficient for addressing complex real-world scenarios. To address these limitations, we propose BINENHANCE, a novel framework designed to leverage the inter-function semantics to enhance the expression of internal code semantics for binary code search. Specifically, BINENHANCE constructs an External Environment Semantic Graph (EESG), which establishes a stable and analogous external environment for homologous functions by using different inter-function semantic relation (textit{e.g.}, textit{call}, textit{location}, textit{data-co-use}). After the construction of EESG, we utilize the embeddings generated by existing internal code semantic models to initialize EESG nodes. Finally, we design a Semantic Enhancement Model (SEM) that uses Relational Graph Convolutional Networks (RGCNs) and a residual block to learn valuable external semantics on the EESG for generating the enhanced semantics embedding. In addition, BinEnhance utilizes data feature similarity to refine the cosine similarity of semantic embeddings. We conduct experiments under six different tasks (textit{e.g.}, under textit{function inlining} scenario) and the results illustrate the performance and robustness of BINENHANCE. The application of BinEnhance to HermesSim, Asm2vec, TREX, Gemini, and Asteria on two public datasets results in an improvement of Mean Average Precision (MAP) from 53.6% to 69.7%. Moreover, the efficiency increases fourfold.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190115", "vector": [], "sparse_vector": [], "title": "Off-Path TCP Hijacking in Wi-Fi Networks: A Packet-Size Side Channel Attack.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we unveil a fundamental side channel in Wi-Fi networks, specifically the observable frame size, which can be exploited by attackers to conduct TCP hijacking attacks. Despite the various security mechanisms (e.g., WEP and WPA2/WPA3) implemented to safeguard Wi-Fi networks, our study reveals that an off-path attacker can still extract sufficient information from the frame size side channel to hijack the victim's TCP connection. Our side channel attack is based on two significant findings: (i) response packets (e.g., ACK and RST) generated by TCP receivers vary in size, and (ii) the encrypted frames containing these response packets have consistent and distinguishable sizes. By observing the size of the victim's encrypted frames, the attacker can detect and hijack the victim's TCP connections. We validate the effectiveness of this side channel attack through two case studies, i.e., SSH DoS and web traffic manipulation. Precisely, our attack can terminate the victim's SSH session in 19 seconds and inject malicious data into the victim's web traffic within 28 seconds. Furthermore, we conduct extensive measurements to evaluate the impact of our attack on real-world Wi-Fi networks. We test 30 popular wireless routers from 9 well-known vendors, and none of these routers can protect victims from our attack. Besides, we implement our attack in 80 real-world Wi-Fi networks and successfully hijack the victim's TCP connections in 75 (93.75%) evaluated Wi-Fi networks. We have responsibly disclosed the vulnerability to the Wi-Fi Alliance and proposed several mitigation strategies to address this issue.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190116", "vector": [], "sparse_vector": [], "title": "Hidden and Lost Control: on Security Design Risks in IoT User-Facing Matter Controller.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Xiaojiang Du", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Matter is emerging as an IoT industry–unifying standard, aiming to enhance the interoperability among diverse smart home products, enabling them to work securely and seamlessly together. With many popular IoT vendors increasingly supporting Matter in consumer IoT products, we perform a systematic study to investigate how and whether vendors can integrate Matter securely into IoT systems and how well Matter as a standard supports vendors’ secure integration. By analyzing Matter development model in the wild, we reveal a new kind of design flaw in user-facing Matter control capabilities and interfaces, called UMCCI flaws, which are exploitable vulnerabilities in the design space and seriously jeopardize necessary control and surveillance capabilities of Matter-enabled devices for IoT users. Therefore we built an automatic tool called UMCCI Checker, enhanced by the large-language model in UI analysis, which enables automatically detecting UMCCI flaws without relying on real IoT devices. Our tool assisted us with studying and performing proof-of-concept attacks on 11 real Matter devices of 8 popular vendors to confirm that the UMCCI flaws are practical and common. We reported UMCCI flaws to related vendors, which have been acknowledged by CSA, Apple, Tuya, Aqara, etc. To help CSA and vendors better understand and avoid security flaws in developing and integrating IoT standards like Matter, we identify two categories of root causes and propose immediate fix recommendations.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190117", "vector": [], "sparse_vector": [], "title": "WAVEN: WebAssembly Memory Virtualization for Enclaves.", "authors": ["<PERSON><PERSON>", "Honghan Ji", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The advancement of trusted execution environments (TEEs) has enabled the confidential computing paradigm and created new application scenarios for WebAssembly (Wasm). \"Wasm+TEE\" designs achieve in-enclave multi-tenancy with strong isolation, facilitating concurrent execution of untrusted code instances from multiple users. However, the linear memory model of Wasm lacks efficient cross-module data sharing and fine-grained memory access control, significantly restricting its applications in certain confidential computing scenarios where secure data sharing is essential (e.g., confidential stateful FaaS and data marketplaces). In this paper, we propose WAVEN (WebAssembly Memory Virtualization for ENclaves), a novel WebAssembly memory virtualization scheme, to enable memory sharing among Wasm modules and page-level access control. We implement WAVEN atop WAMR, a popular Wasm runtime for TEEs, and empirically demonstrate its efficiency and effectiveness. To the best of our knowledge, our work represents the first approach that enables cross-module memory sharing with fine-grained memory access control in Wasm.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190118", "vector": [], "sparse_vector": [], "title": "SIGuard: Guarding Secure Inference with Post Data Privacy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>g Yuan"], "summary": "Secure inference is designed to enable encrypted machine learning model prediction over encrypted data. It will ease privacy concerns when models are deployed in Machine Learning as a Service (MLaaS). For efficiency, most of recent secure inference protocols are constructed using secure multi-party computation (MPC) techniques. They can ensure that MLaaS computes inference without knowing the inputs of users and model owners. However, MPC-based protocols do not hide information revealed from their output. In the context of secure inference, prediction outputs (i.e., inference results of encrypted user inputs) are revealed to the users. As a result, adversaries can compromise textbf{output privacy} of secure inference, i.e., launching Membership Inference Attacks (MIAs) by querying encrypted models, just like MIAs in plaintext inference. We observe that MPC-based secure inference often yields perturbed predictions due to approximations of nonlinear functions like softmax compared to its plaintext version on identical user inputs. Thus, we evaluate whether or not MIAs can still exploit such perturbed predictions on known secure inference protocols. Our results show that secure inference remains vulnerable to MIAs. The adversary can steal membership information with high successful rates comparable to plaintext MIAs. To tackle this open challenge, we propose textbf{SIGuard}, a framework to guard the output privacy of secure inference from being exploited by MIAs. textbf{SIGuard}'s protocol can seamlessly be integrated into existing MPC-based secure inference protocols without intruding on their computation. It proceeds with encrypted predictions outputted from secure inference, and then crafts noise for perturbing encrypted predictions without compromising inference accuracy; only the perturbed predictions are revealed to users at the end of protocol execution. textbf{SIGuard} achieves stringent privacy guarantees via a co-design of MPC techniques and machine learning. We further conduct comprehensive evaluations to find the optimal hyper-parameters for balanced efficiency and defense effectiveness against MIAs. Together, our evaluation shows textbf{SIGuard} effectively defends against MIAs by reducing the attack accuracy to be around the random guess with overhead (1.1s), occupying ~24.8% of secure inference (3.29s) on widely used ResNet34 over CIFAR-10.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190119", "vector": [], "sparse_vector": [], "title": "Heimdall: Towards Risk-Aware Network Management Outsourcing.", "authors": ["<PERSON><PERSON><PERSON>", "Qiutong Men", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Enterprises are increasingly outsourcing network management (e.g., troubleshooting routing issues) to reduce cost and improve efficiency, either by hiring third-party contractors or by outsourcing to third-party vendors. Unfortunately, recent events have shown that this outsourcing model has become a new source of network incidents in customer networks. In this work, we argue that a risk-aware outsourcing approach is needed that enables customers to measure and assess risk transparently and make informed decisions to minimize harm. We first concretely define the notion of risk in the context of outsourced network management and then present an end-to-end framework, called Heimdall, which enables enterprises to assess, monitor, and respond to risk. <PERSON><PERSON><PERSON><PERSON> automatically builds a dependency graph to accurately assess the risk of an outsourced task, and uses a fine-grained reference monitor to monitor and mitigate potential risks during operation. Our expert validation results show that <PERSON><PERSON><PERSON><PERSON> effectively controls risk for outsourced network operations, resolving 92% of practical issues at the minimal risk level while incurring only a marginal timing overhead of approximately 7%.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190120", "vector": [], "sparse_vector": [], "title": "Incorporating Gradients to Rules: Towards Lightweight, Adaptive Provenance-based Intrusion Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As cyber attacks grow increasingly sophisticated and stealthy, it becomes more imperative and challenging to detect intrusion from normal behaviors. Through fine-grained causality analysis, provenance-based intrusion detection systems (PIDS) demonstrated a promising capacity to distinguish benign and malicious behaviors, attracting widespread attention from both industry and academia. Among diverse approaches, rule-based PIDS stands out due to its lightweight overhead, real-time capabilities, and explainability. However, existing rule-based systems suffer low detection accuracy, especially the high false alarms, due to the lack of fine-grained rules and environment-specific configurations.In this paper, we propose CAPTAIN, a rule-based PIDS capable of automatically adapting to diverse environments. Specifically, we propose three adaptive parameters to adjust the detection configuration with respect to nodes, edges, and alarm generation thresholds. We build a differentiable tag propagation framework and utilize the gradient descent algorithm to optimize these adaptive parameters based on the training data. We evaluate our system using data from DARPA Engagements and simulated environments. The evaluation results demonstrate that CAPTAIN enhances rule-based PIDS with learning capabilities, resulting in improved detection accuracy, reduced detection latency, lower runtime overhead, and more interpretable detection procedures and results compared to the state-of-the-art (SOTA) PIDS.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190121", "vector": [], "sparse_vector": [], "title": "A Comprehensive Memory Safety Analysis of Bootloaders.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The bootloader plays an important role during the boot process, as it connects two crucial components: the firmware and the operating system. After powering on, the bootloader takes control from the firmware, prepares the early boot environment, and then hands control over to the operating system. Modern computers often use a feature called secure boot to prevent malicious software from loading at startup. As a key part of the secure boot chain, the bootloader is responsible for verifying the operating system, loading its image into memory, and launching it. Therefore, the bootloader must be designed and implemented in a secure manner. However, bootloaders have increasingly provided more features and functionalities for end users. As the code base grows, bootloaders inevitably expose more attack surfaces. In recent years, vulnerabilities, particularly memory safety violations, have been discovered in various bootloaders. Some of these vulnerabilities can lead to denial of service or even bypass secure boot protections. Despite the bootloader’s critical role in the secure boot chain, a comprehensive memory safety analysis of bootloaders has yet to be conducted. In this paper, we present the first comprehensive and systematic memory safety analysis of bootloaders, based on a survey of previous bootloader vulnerabilities. We examine the potential attack surfaces of various bootloaders and how these surfaces lead to vulnerabilities. We observe that malicious input from peripherals such as storage devices and networks is a primary method attackers use to exploit bootloader vulnerabilities. To assist bootloader developers in detecting vulnerabilities at scale, we designed and implemented a bootloader fuzzing framework based on our analysis. In our experiments, we discovered 39 vulnerabilities in nine bootloaders, of which 38 are new vulnerabilities. In particular, 14 vulnerabilities were found in the widely used Linux standard bootloader GRUB, some of which can even lead to secure boot bypass if properly exploited. So far, five CVEs have been assigned to our findings.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190122", "vector": [], "sparse_vector": [], "title": "Revisiting Physical-World Adversarial Attack on Traffic Sign Recognition: A Commercial Systems Perspective.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xi<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Traffic Sign Recognition (TSR) is crucial for safe and correct driving automation. Recent works revealed a general vulnerability of TSR models to physical-world adversarial attacks, which can be low-cost, highly deployable, and capable of causing severe attack effects such as hiding a critical traffic sign or spoofing a fake one. However, so far existing works generally only considered evaluating the attack effects on academic TSR models, leaving the impacts of such attacks on real-world commercial TSR systems largely unclear. In this paper, we conduct the first large-scale measurement of physical-world adversarial attacks against commercial TSR systems. Our testing results reveal that it is possible for existing attack works from academia to have highly reliable (100%) attack success against certain commercial TSR system functionality, but such attack capabilities are not generalizable, leading to much lower-than-expected attack success rates overall. We find that one potential major factor is a spatial memorization design that commonly exists in today's commercial TSR systems. We design new attack success metrics that can mathematically model the impacts of such design on the TSR system-level attack success, and use them to revisit existing attacks. Through these efforts, we uncover 7 novel observations, some of which directly challenge the observations or claims in prior works due to the introduction of the new metrics.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190123", "vector": [], "sparse_vector": [], "title": "Interventional Root Cause Analysis of Failures in Multi-Sensor Fusion Perception Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Autonomous driving systems (ADS) heavily depend on multi-sensor fusion (MSF) perception systems to process sensor data and improve the accuracy of environmental perception. However, MSF cannot completely eliminate uncertainties, and faults in multiple modules will lead to perception failures. Thus, identifying the root causes of these perception failures is crucial to ensure the reliability of MSF perception systems. Traditional methods for identifying perception failures, such as anomaly detection and runtime monitoring, are limited because they do not account for causal relationships between faults in multiple modules and overall system failure. To overcome these limitations, we propose a novel approach called interventional root cause analysis (IRCA). IRCA leverages the directed acyclic graph (DAG) structure of MSF to develop a hierarchical structural causal model (H-SCM), which effectively addresses the complexities of causal relationships. Our approach uses a divide-and-conquer pruning algorithm to encompass multiple causal modules within a causal path and to pinpoint intervention targets. We implement IRCA and evaluate its performance using real fault scenarios and synthetic scenarios with injected faults in the ADS Autoware. The average F1-score of IRCA in real fault scenarios is over $95%$. We also illustrate the effectiveness of IRCA on an autonomous vehicle testbed equipped with Autoware, as well as a cross-platform evaluation using Apollo. The results show that IRCA can efficiently identify the causal paths leading to failures and significantly enhance the safety of ADS.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190124", "vector": [], "sparse_vector": [], "title": "&quot;Who is Trying to Access My Account?&quot; Exploring User Perceptions and Reactions to Risk-based Authentication Notifications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yu<PERSON>g Li", "<PERSON><PERSON><PERSON>"], "summary": "Risk-based authentication (RBA) is gaining popularity and RBA notifications promptly alert users to protect their accounts from unauthorized access. Recent research indicates that users can identify legitimate login notifications triggered by themselves. However, little attention has been paid to whether RBA notifications triggered by non-account holders can effectively raise users' awareness of crises and prevent potential attacks. In this paper, we invite 258 online participants and 15 offline participants to explore users' perceptions, reactions, and expectations for three types of RBA notifications (i.e., RBA notifications triggered by correct passwords, incorrect passwords, and password resets). The results show that over 90% of participants consider RBA notifications important. Users do not show significant differences in their feelings and behaviors towards the three types of RBA notifications, but they have distinct expectations for each type. Most participants feel suspicious, nervous, and anxious upon receiving the three types of RBA notifications not triggered by themselves. Consequently, users immediately review the full content of the notification. 46% of users suspect that RBA notifications might be phishing attempts, while categorizing them as potential phishing attacks or spam may lead to ineffective account protection. Despite these suspicions, 65% of users still log into their accounts to check for suspicious activities and take no further action if no abnormalities are found. Additionally, the current format of RBA notifications fails to gain users' trust and meet their expectations. Our findings indicate that RBA notifications need to provide more detailed information about suspicious access, offer additional security measures, and clearly explain the risks involved. Finally, we offer five design recommendations for RBA notifications to better mitigate potential risks and enhance account security.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190125", "vector": [], "sparse_vector": [], "title": "Automatic Insecurity: Exploring Email Auto-configuration in the Wild.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>yu Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Email clients that support auto-configuration mechanisms automatically retrieve server configuration information, such as the hostname, port number, and connection type, allowing users to log in by simply entering email addresses and passwords. Auto-configuration mechanisms are being increasingly adopted. However, the security implications of these mechanisms, both in terms of implementation and deployment, have not yet been thoroughly studied. In this paper, we present the first systematic analysis of security threats associated with email auto-configuration and evaluate their impacts. We summarize 10 attack scenarios, covering 17 defects (including 8 newly identified ones), along with 4 inadequate client UI notifications. These attack scenarios can either cause a victim to connect to an attacker-controlled server or establish an insecure connection, putting the victim’s credentials at risk. Moreover, our large-scale measurements and in-depth analysis revealed serious insecurity of auto-configuration applications in the wild. On the server-side, we discovered 49,013 domains, including 19 of the Top-1K popular domains, were misconfigured. On the client-side, 22 out of 29 clients were vulnerable to those threats. Moreover, 27 out of 29 clients exhibited at least one UI-notification defect that facilitates silent attacks. These defects arise from misconfiguration, mismanagement, flawed implementation and compatibility. We hope this paper raises attention to email auto-configuration security.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190126", "vector": [], "sparse_vector": [], "title": "The Forking Way: When TEEs Meet Consensus.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An increasing number of distributed platforms combine Trusted Execution Environments (TEEs) with blockchains. Indeed, many hail the combination of TEEs and blockchains a good “marriage”: TEEs bring confidential computing to the blockchain while the consensus layer could help defend TEEs from forking attacks. In this paper, we systemize how current blockchain solutions integrate TEEs and to what extent they are secure against forking attacks. To do so, we thoroughly analyze 29 proposals for TEE-based blockchains, ranging from academic proposals to production-ready platforms. We uncover a lack of consensus in the community on how to combine TEEs and blockchains. In particular, we identify four broad means to interconnect TEEs with consensus, analyze their limitations, and discuss possible remedies. Our analysis also reveals previously undocumented forking attacks on three production-ready TEE-based blockchains: Ten, Phala, and the Secret Network. We leverage our analysis to propose effective countermeasures against those vulnerabilities; we responsibly disclosed our findings to the developers of each affected platform.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190127", "vector": [], "sparse_vector": [], "title": "Revealing the Black Box of Device Search Engine: Scanning Assets, Strategies, and Ethical Consideration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Chen", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the digital age, device search engines such as Censys and Shodan play crucial roles by scanning the internet to catalog online devices, aiding in the understanding and mitigation of network security risks. While previous research has used these tools to detect devices and assess vulnerabilities, there remains uncertainty regarding the assets they scan, the strategies they employ, and whether they adhere to ethical guidelines. This study presents the first comprehensive examination of these engines’ operational and ethical dimensions. We developed a novel framework to trace the IP addresses utilized by these engines and collected 1,407 scanner IPs. By uncovering their IPs, we gain deep insights into the actions of device search engines for the first time and gain original findings. By employing 28 honeypots to monitor their scanning activities extensively in one year, we demonstrate that users can hardly evade scans by blocklisting scanner IPs or migrating service ports. Our findings reveal significant ethical concerns, including a lack of transparency, harmlessness, and anonymity. Notably, these engines often fail to provide transparency and do not allow users to opt out of scans. Further, the engines send malformed requests, attempt to access excessive details without authorization, and even publish personally identifiable information(PII) and screenshots on search results. These practices compromise user privacy and expose devices to further risks by potentially aiding malicious entities. This paper emphasizes the urgent need for stricter ethical standards and enhanced transparency in the operations of device search engines, offering crucial insights into safeguarding against invasive scanning practices and protecting digital infrastructures.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190128", "vector": [], "sparse_vector": [], "title": "Transparency or Information Overload? Evaluating Users&apos; Comprehension and Perceptions of the iOS App Privacy Report.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Apple's App Privacy Report (``privacy report''), released in 2021, aims toinform iOS users about apps' access to their data and sensors (e.g., contacts,camera) and, unlike other privacy dashboards, what domains are contacted by apps and websites. To evaluate theeffectiveness of the privacy report, we conducted semi-structured interviews(textit{n} = 20) to examine users' reactions to the information, their understanding of relevant privacyimplications, and how they might changetheir behavior to address privacy concerns. Participants easily understood whichapps accessed data and sensors at certain times on their phones, and knew how toremove an app's permissions in case of unexpected access. In contrast,participants had difficulty understanding apps' and websites' networkactivities. They were confused about how and why network activities occurred,overwhelmed by the number of domains their apps contacted, and uncertain aboutwhat remedial actions they could take against potential privacy threats. Whilethe privacy report and similar tools can increase transparency by presentingusers with details about how their data is handled, we recommend providing moreinterpretation or aggregation of technical details, such as the purpose ofcontacting domains, to help users make informed decisions.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190129", "vector": [], "sparse_vector": [], "title": "IsolateGPT: An Execution Isolation Architecture for LLM-Based Agentic Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large language models (LLMs) extended as systems, such as ChatGPT, have begun supporting third-party applications. These LLM apps leverage the de facto natural language-based automated execution paradigm of LLMs: that is, apps and their interactions are defined in natural language, provided access to user data, and allowed to freely interact with each other and the system. These LLM app ecosystems resemble the settings of earlier computing platforms, where there was insufficient isolation between apps and the system. Because third-party apps may not be trustworthy, and exacerbated by the imprecision of natural language interfaces, the current designs pose security and privacy risks for users. In this paper, we evaluate whether these issues can be addressed through execution isolation and what that isolation might look like in the context of LLM-based systems, where there are arbitrary natural language-based interactions between system components, between LLM and apps, and between apps. To that end, we propose IsolateGPT, a design architecture that demonstrates the feasibility of execution isolation and provides a blueprint for implementing isolation, in LLM-based systems. We evaluate IsolateGPT against a number of attacks and demonstrate that it protects against many security, privacy, and safety issues that exist in non-isolated LLM-based systems, without any loss of functionality. The performance overhead incurred by IsolateGPT to improve security is under 30% for three-quarters of tested queries.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190130", "vector": [], "sparse_vector": [], "title": "VeriBin: Adaptive Verification of Patches at the Binary Level.", "authors": ["Hong<PERSON> Wu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Vendors are often provided with updated versions of a piece of software, fixing known security issues.However, the inability to have any guarantee that the provided patched software does not break the functionality of its original version often hinders patch deployment.This issue is particularly severe when the patched software is only provided in its compiled binary form.In this case, manual analysis of the patch's source code is impossible, and existing automated patch analysis techniques, which rely on source code, are not applicable.Even when the source code is accessible, the necessity of binary-level patch verification is still crucial, as highlighted by the recent XZ Utils backdoor. To tackle this issue, we propose VeriBin, a system able to compare a binary with its patched version and determine whether the patch is ''Safe to Apply'', meaning it does not introduce any modification that could potentially break the functionality of the original binary.To achieve this goal, VeriBin checks functional equivalence between the original and patched binaries.In particular, VeriBin first uses symbolic execution to systematically identify patch-introduced modifications.Then, it checks if the detected patch-introduced modifications respect specific properties that guarantee they will not break the original binary's functionality.To work without source code, VeriBin's design solves several challenges related to the absence of semantic information (removed during the compilation process) about the analyzed code and the complexity of symbolically executing large functions precisely.Our evaluation of VeriBin on a dataset of 86 samples shows that it achieves an accuracy of 93.0% with no false positives, requiring only minimal analyst input.Additionally, we showcase how VeriBin can be used to detect the recently discovered XZ Utils backdoor.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190131", "vector": [], "sparse_vector": [], "title": "THEMIS: Regulating Textual Inversion for Personalized Concept Censorship.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Personalization has become a crucial demand in the Generative AI technology. As the pre-trained generative model (*e.g.*, stable diffusion) has fixed and limited capability, it is desirable for users to customize the model to generate output with new or specific concepts. Fine-tuning the pre-trained model is not a promising solution, due to its high requirements of computation resources and data. Instead, the emerging personalization approaches make it feasible to augment the generative model in a lightweight manner. However, this also induces severe threats if such advanced techniques are misused by malicious users, such as spreading fake news or defaming individual reputations. Thus, it is necessary to regulate personalization models (*i.e.*, achieve *concept censorship*) for their development and advancement. In this paper, we focus on the regulation of a popular personalization technique dubbedtextbf{Textual Inversion (TI)}, which can customize Text-to-Image (T2I) generative models with excellent performance. TI crafts the word embedding that contains detailed information about a specific object. Users can easily add the word embedding to their local T2I model, like the public Stable Diffusion (SD) model, to generate personalized images. The advent of TI has brought about a new business model, evidenced by the public platforms for sharing and selling word embeddings (*e.g.*, Civitai [1]). Unfortunately, such platforms also allow malicious users to misuse the word embeddings to generate unsafe content, causing damages to the concept owners. We propose *THEMIS* to achieve the ***personalized concept censorship***. Its key idea is to leverage the backdoor technique for good by injecting positive backdoors into the TI embeddings. Briefly, the concept owner selects some sensitive words as triggers during the training of TI, which will be censored for normal use. In the subsequent generation stage, if a malicious user combines the sensitive words with the personalized embeddings as final prompts, the T2I model will output a pre-defined target image rather than images including the desired malicious content. To demonstrate the effectiveness of *THEMIS*, we conduct extensive experiments on the TI embeddings with Latent Diffusion and Stable Diffusion, two prevailing open-sourced T2I models.The results demonstrate that *THEMIS* is capable of preventing Textual Inversion from cooperating with sensitive words meanwhile guaranteeing its pristine utility. Furthermore, *THEMIS* is general to different uses of sensitive words, including different locations, synonyms, and combinations of sensitive words. It can also resist different types of potential and adaptive attacks. Ablation studies are also conducted to verify our design.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190132", "vector": [], "sparse_vector": [], "title": "I Know What You Asked: Prompt Leakage via KV-Cache Sharing in Multi-Tenant LLM Serving.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large Language Models (LLMs), which laid the groundwork for Artificial General Intelligence (AGI), have recently gained significant traction in academia and industry due to their disruptive applications. In order to enable scalable applications and efficient resource management, various multi-tenant LLM serving frameworks have been proposed, in which the LLM caters to the needs of multiple users simultaneously. One notable mechanism in recent works, such as SGLang and vLLM, is sharing the Key-Value (KV) cache for identical token sequences among multiple users, saving both memory and computation. This paper presents the first investigation on security risksassociated with multi-tenant LLM serving. We show that the state-of-the-art mechanisms of KV cache sharing may lead to new side channel attack vectors, allowing unauthorized reconstructionof user prompts and compromising sensitive user information among mutually distrustful users. Specifically, we introduce our attack, PROMPTPEEK, and apply it to three scenarios where theadversary, with varying degrees of prior knowledge, is capable of reverse-engineering prompts from other users. This study underscores the need for careful resource management in multi-tenant LLM serving and provides critical insights for future security enhancement.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190133", "vector": [], "sparse_vector": [], "title": "MineShark: Cryptomining Traffic Detection at Scale.", "authors": ["Shaoke Xi", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Shen", "<PERSON><PERSON>"], "summary": "The rapid growth of cryptojacking and the increase in regulatory bans on cryptomining have prompted organizations to enhance detection ability within their networks. Traditional methods, including rule-based detection and deep packet inspection, fall short in timely and comprehensively identifying new and encrypted mining threats. In contrast, learning-based techniques show promise by identifying content-agnostic traffic patterns, adapting to a wide range of cryptomining configurations. However, existing learning-based systems often lack scalability in real-world detection, primarily due to challenges with unlabeled, imbalanced, and high-speed traffic inputs. To address these issues, we introduce MineShark, a system that identifies robust patterns of mining traffic to distinguish between vast quantities of benign traffic and automates the confirmation of model outcomes through active probing to prevent an overload of model alarms. As model inference labels are progressively confirmed, MineShark conducts self-improving updates to enhance model accuracy. MineShark is capable of line-rate detection at various traffic volume scales with the allocation of different amounts of CPU and GPU resources. In a 10 Gbps campus network deployment lasting ten months, MineShark detected cryptomining connections toward 105 mining pools ahead of concurrently deployed commercial systems, 17.6% of which were encrypted. It automatically filtered over 99.3% of false alarms and achieved an average packet processing throughput of 1.3 Mpps, meeting the line-rate demands of a 10 Gbps network, with a negligible loss rate of 0.2%. We publicize MineShark for broader use.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190134", "vector": [], "sparse_vector": [], "title": "AlphaDog: No-Box Camouflage Attacks via Alpha Channel Oversight.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Traditional black-box adversarial attacks on computer vision models face significant limitations, including intensive querying requirements, time-consuming iterative processes, a lack of universality, and low attack success rates (ASR) and confidence levels (CL) due to subtle perturbations. This paper introduces AlphaDog, an Alpha channel attack, the first universally efficient targeted no-box attack, exploiting the often overlooked Alpha channel in RGBA images to create visual disparities between human perception and machine interpretation, efficiently deceiving both. Specifically, AlphaDog maliciously sets the RGB channels to represent the desired object for AI recognition, while crafting the Alpha channel to create a different perception for humans when blended with a standard or default background color of digital media (thumbnail or image viewer apps). Leveraging differences in how AI models and human vision process transparency, AlphaDog outperforms existing adversarial attacks in four key ways: (i) as a no-box attack, it requires zero queries; (ii) it achieves highly efficient generation, taking milliseconds to produce arbitrary attack images; (iii) AlphaDog can be universally applied, compromising most AI models with a single attack image; (iv) it guarantees 100% ASR and CL. The assessment of 6,500 AlphaDog attack examples across 100 state-of-the-art image recognition systems demonstrates AlphaDog's effectiveness, and an IRB-approved experiment involving 20 college-age participants validates AlphaDog's stealthiness. AlphaDog can be applied in data poisoning, evasion attacks, and content moderation. Additionally, a novel pixel-intensity histogram-based detection method is introduced to identify AlphaDog, achieving 100% effectiveness in detecting and protecting computer vision models against AlphaDog. Demos are available on the AlphaDog website (https://sites.google.com/view/alphachannelattack/home).", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190135", "vector": [], "sparse_vector": [], "title": "MTZK: Testing and Exploring Bugs in Zero-Knowledge (ZK) Compilers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Zero-knowledge (ZK) proofs have been increasingly popular in privacy-preserving applications and blockchain systems. To facilitate handy and efficient ZK proof generation for normal users, the industry has designed domain-specific languages (DSLs) and ZK compilers. Given a program in ZK DSL, a ZK compiler compiles it into a circuit, which is then passed to the prover and verifier for ZK checking. However, the correctness of ZK compilers is not well studied, and recent works have shown that de facto ZK compilers are buggy, which can allow malicious users to generate invalid proofs that are accepted by the verifier, causing security breaches and financial losses in cryptocurrency. In this paper, we propose MTZK, a metamorphic testing framework to test ZK compilers and uncover incorrect compilations. Our approach leverages deliberately designed metamorphic relations (MRs) to mutate ZK compiler inputs. This way, ZK compilers can be automatically tested for compilation correctness using inputs and mutated variants. We propose a set of design considerations and optimizations to deliver an efficient and effective testing framework. In the evaluation of four industrial ZK compilers, we successfully uncovered 21 bugs, out of which the developers have promptly patched 15. We also show possible exploitations of the uncovered bugs to demonstrate their severe security implications.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190136", "vector": [], "sparse_vector": [], "title": "CASPR: Context-Aware Security Policy Recommendation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Nowadays, SELinux has been widely used to provide flexible mandatory access control and security policies are critical to maintain the security of operating systems. Strictly speaking, all access requests must be restricted by appropriate policy rules to satisfy the functional requirements of the software or application. However, manually configuring security policy rules is an error-prone and time-consuming task that often requires expert knowledge. Therefore, it is a challenging task to recommend policy rules without anomalies effectively due to the numerous policy rules and the complexity of semantics. The majority of previous research mined information from policies to recommend rules but did not apply to the newly defined types without any rules. In this paper, we propose a context-aware security policy recommendation (CASPR) method that can automatically analyze and refine security policy rules. Context-aware information in CASPR includes policy rules, file locations, audit logs, and attribute information. According to these context-aware information, multiple features are extracted to calculate the similarity of privilege sets. Based on the calculation results, CASPR clusters types by the K-means model and then recommends rules automatically. The method automatically detects anomalies in security policy, namely, constraint conflicts, policy inconsistencies, and permission incompleteness. Further, the detected anomalous policies are refined so that the authorization rules can be effectively enforced. The experiment results confirm the feasibility of the proposed method for recommending effective rules for different versions of policies. We demonstrate the effectiveness of clustering by CASPR and calculate the contribution of each context-aware feature based on SHAP. CASPR not only recommends rules for newly defined types based on context-aware information but also enhances the accuracy of security policy recommendations for existing types, compared to other rule recommendation models. CASPR has an average accuracy of 91.582% and F1-score of 93.761% in recommending rules. Further, three kinds of anomalies in the policies can be detected and automatically repaired. We employ CASPR in multiple operating systems to illustrate the universality. The research has significant implications for security policy recommendation and provides a novel method for policy analysis with great potential.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190137", "vector": [], "sparse_vector": [], "title": "Distributed Function Secret Sharing and Applications.", "authors": ["Peng<PERSON>ng", "Hongwei Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Function Secret Sharing (FSS) has emerged as a pivotal cryptographic tool for secure computation, delivering exceptional online efficiency with constant interaction rounds. However, the reliance on a trusted third party for key generation in existing FSS works compromises both security and practical deployment. In this paper, we introduce efficient distributed key generation schemes for FSS-based distributed point function and distributed comparison function, supporting both input and output to be arithmetic-shared. We further design crucial FSS-based components optimized for online efficiency, serving as the building blocks for advanced protocols. Finally, we propose an efficient framework for evaluating complex trigonometric functions, ubiquitous in scientific computations. Our framework leverages the periodic property of trigonometric functions, which reduces the bit length of input during FSS evaluation. This mitigates the potential performance bottleneck for FSS-based protocols incurred by bit length. Extensive empirical evaluations on real-world applications demonstrate a latency reduction of up to $14.73times$ and a communication cost decrease ranging from $27.67sim 184.42 times$ over the state-of-the-art work.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190138", "vector": [], "sparse_vector": [], "title": "Unleashing the Power of Generative Model in Recovering Variable Names from Stripped Binary.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>g <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Cheng", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Decompilation aims to recover the source code form of a binary executable. It has many security applications, such as malware analysis, vulnerability detection, and code hardening. A prominent challenge in decompilation is to recover variable names. We propose a novel technique that leverages the strengths of generative models while mitigating model biases. We build a prototype, GenNm, from pre-trained generative models CodeGemma-2B, CodeLlama-7B, and CodeLlama-34B. We finetune GenNm on decompiled functions and teach models to leverage contextual information. GenNm includes names from callers and callees while querying a function, providing rich contextual information within the model's input token limitation. We mitigate model biases by aligning the output distribution of models with symbol preferences of developers. Our results show that GenNm improves the state-of-the-art name recovery precision by 5.6-11.4 percentage points on two commonly used datasets and improves the state-of-the-art by 32% (from 17.3% to 22.8%) in the most challenging setup where ground-truth variable names are not seen in the training dataset.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190139", "vector": [], "sparse_vector": [], "title": "Enhancing Security in Third-Party Library Reuse - Comprehensive Detection of 1-day Vulnerability through Code Patch Analysis.", "authors": ["<PERSON><PERSON><PERSON> Xu", "Jialiang Dong", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Nowadays, software development progressesrapidly to incorporate new features. To facilitate such growthand provide convenience for developers when creating andupdating software, reusing open-source software (i.e., thirdpartylibrary reuses) has become one of the most effectiveand efficient methods. Unfortunately, the practice of reusingthird-party libraries (TPLs) can also introduce vulnerabilities(known as 1-day vulnerabilities) because of the low maintenanceof TPLs, resulting in many vulnerable versions remaining inuse. If the software incorporating these TPLs fails to detect theintroduced vulnerabilities and leads to delayed updates, it willexacerbate the security risks. However, the complicated codedependencies and flexibility of TPL reuses make the detection of1-day vulnerability a challenging task. To support developers insecurely reusing TPLs during software development, we designand implement VULTURE, an effective and efficient detectiontool, aiming at identifying 1-day vulnerabilities that arise fromthe reuse of vulnerable TPLs. It first executes a database creationmethod, TPLFILTER, which leverages the Large LanguageModel (LLM) to automatically build a unique database for thetargeted platform. Instead of relying on code-level similaritycomparison, VULTURE employs hashing-based comparison toexplore the dependencies among the collected TPLs and identifythe similarities between the TPLs and the target projects.Recognizing that developers have the flexibility to reuse TPLsexactly or in a custom manner, VULTURE separately conductsversion-based comparison and chunk-based analysis to capturefine-grained semantic features at the function levels. We appliedVULTURE to 10 real-world projects to assess its effectivenessand efficiency in detecting 1-day vulnerabilities. VULTUREsuccessfully identified 175 vulnerabilities from 178 reused TPLs.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190140", "vector": [], "sparse_vector": [], "title": "The Discriminative Power of Cross-layer RTTs in Fingerprinting Proxy Traffic.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The escalating global trend of Internet censorship has necessitated an increased adoption of proxy tools, especially obfuscated circumvention proxies. These proxies serve a fundamental need for access and connectivity among millions in heavily censored regions. However, as the use of proxies expands, so do censors' dedicated efforts to detect and disrupt such circumvention traffic to enforce their information control policies. In this paper, we bring out the presence of an inherent fingerprint for detecting obfuscated proxy traffic. The fingerprint is created by the misalignment of transport- and application-layer sessions in proxy routing, which is reflected in the discrepancy in Round Trip Times (RTTs) across network layers. Importantly, being protocol-agnostic, the fingerprint enables an adversary to effectively target multiple proxy protocols simultaneously. We conduct an extensive evaluation using both controlled testbeds and real-world traffic, collected from a partner ISP, to assess the fingerprint's potential for exploitation by censors. In addition to being of interest on its own, our timing-based fingerprinting vulnerability highlights the deficiencies in existing obfuscation approaches. We hope our study brings the attention of the circumvention community to packet timing as an area of concern and leads to the development of more sustainable countermeasures.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190141", "vector": [], "sparse_vector": [], "title": "SKILLPoV: Towards Accessible and Effective Privacy Notice for Amazon Alexa Skills.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Yao", "<PERSON>"], "summary": "Despite the popularity and many convenient features of Amazon Alexa, concerns about privacy risks to users are rising since many Alexa voice-apps (called skills) may collect user data during the interaction with Alexa devices. Informing users about data collection in skills is essential for addressing their privacy concerns. However, the constrained interfaces of Alexa pose a challenge to effective privacy notices, where currently Alexa users can only access privacy policies of skills over the Web or smartphone apps. This in particular creates a challenge for visually impaired users to make informed privacy decisions. In this work, we propose the concept of Privacy Notice over Voice, an accessible and inclusive mechanism to make users aware of the data practices of Alexa skills through the conversational interface: for each skill, we will generate a short and easily understandable privacy notice and play it to users at the beginning of the skill in voice. We first conduct a user study involving 52 smart speaker users and 21 Alexa skill developers to understand their attitudes toward data collection and the Privacy Notice over Voice mechanism. 92.3% of participants liked the design of Privacy Notice over Voice and 70.2% of participants agreed that such mechanism provides better accessibility and readability than traditional privacy policies for Alexa users. Informed by our user study results, we design and develop a tool named SKILLPoV (Skill’s Privacy Notice over Voice) to automatically generate a reference implementation of Privacy Notice over Voice through static code analysis and instrumentation. With comprehensive evaluation, we demonstrate the effectiveness of SKILLPoV in capturing data collection (91.3% accuracy and 96.4% completeness) from skill code, generating concise and accurate privacy notice content using ChatGPT, and instrumenting skill code with the new privacy notice mechanism without altering the original functionality. In particular, SKILLPoV receives positive and encouraging feedback after real-world testing conducted by skill developers.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190142", "vector": [], "sparse_vector": [], "title": "LightAntenna: Characterizing the Limits of Fluorescent Lamp-Induced Electromagnetic Interference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON>", "Xiaoyu Ji", "<PERSON><PERSON> Xu"], "summary": "Fluorescent lamps are almost everywhere for electric lighting in daily life, across private and public scenarios. Our study uncovers a new electromagnetic interference (EMI) attack surface that these light sources are actually able to manipulate nearby IoT devices in a contactless way. Different from previous EMI attempts requiring a specialized metal antenna as the emission source, which can easily alert victims, we introduce LightAntenna that leverages unaltered everyday fluorescent lamps to launch concealed EMI attacks. To understand why and how fluorescent lamps can be exploited as malicious antennas, we systematically characterize the rationale of EMI emission from fluorescent lamps and identify their capabilities and limits in terms of intensity and frequency response. Moreover, we carefully design a covert method of injecting high-frequency signals into the fluorescent tube via power line transmission. In this way, LightAntenna can realize controllable EMI attacks even across rooms and at a distance of up to 20 m. Our extensive experiments demonstrate the generality, practicality, tunability, and remote attack capability of LightAntenna, which successfully interferes with various types of sensors and IoT devices. In summary, our study provides a comprehensive analysis of the LightAntenna mechanism and proposes defensive strategies to mitigate this emerging attack surface.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190143", "vector": [], "sparse_vector": [], "title": "Siniel: Distributed Privacy-Preserving zkSNARK.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Zero-knowledge Succinct Non-interactive Argument of Knowledge (zkSNARK) is a powerful cryptographic primitive, in which a prover convinces a verifier that a given statement is true without leaking any additional information. However, existing zkSNARKs suffer from high computation overhead in the proof generation. This limits the applications of zkSNARKs, such as private payments, private smart contracts, and anonymous credentials. Private delegation has become a prominent way to accelerate proof generation. In this work, we propose Siniel, an efficient private delegation framework for zkSNARKs constructed from polynomial interactive oracle proof (PIOP) and polynomial commitment scheme (PCS). Our protocol allows a computationally limited prover (a.k.a. delegator) to delegate its expensive prover computation to several workers without leaking any information about the private witness. Most importantly, compared with the recent work EOS (USENIX'23), the state-of-the-art zkSNARK prover delegation framework, a prover in Siniel needs not to engage in the MPC protocol after sending its shares of private witness. This means that a Siniel prover can outsource the entire computation to the workers. We compare Siniel with EOS and show significant performance advantages of the former. The experimental results show that, under low bandwidth conditions (10MBps), Siniel saves about 16% time for delegators than that of EOS, whereas under high bandwidth conditions (1000MBps), Siniel saves about 80% than EOS.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190144", "vector": [], "sparse_vector": [], "title": "ReThink: Reveal the Threat of Electromagnetic Interference on Power Inverters.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kaikai Pan", "<PERSON>", "Xiaoyu Ji", "<PERSON><PERSON> Xu"], "summary": "With the boom of renewable energy sources (RES), the number of power inverters proliferates. Power inverters are the key electronic devices that transform the direct current (DC) power from RES to the alternating current (AC) power on the grids, and their security can affect the stable operation of RES and even power grids. This paper analyzes the security of photovoltaic (PV) inverters from the aspects of internal sensors since they serve as the foundation for safe power conversion. We discover that both the embedded current sensors and voltage sensors are vulnerable to electromagnetic interference (EMI) of 1 GHz or higher, despite electromagnetic compatibility (EMC) countermeasures. Such vulnerabilities can lead to incorrect measurements and deceiving the control algorithms, and we design ReThink that could produce three types of consequences on PV inverters by emitting carefully crafted EMI, i.e., Denial of Service (DoS), damaging inverters physically or damping the power output. We successfully validate these consequences on 5 off-the-shelf PV inverters, and even in a real-world microgrid, by transmitting EMI signals at a distance of 100 ∼ 150cm and a total power within 20W. Our work aims to raise awareness of the security of power electronic devices of RES, as they represent an emerging Cyber-Physical attack surface to the future RES-dominated grid. Finally, to cope with such threats, we provide hardware and software-based countermeasures.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190145", "vector": [], "sparse_vector": [], "title": "The Midas Touch: Triggering the Capability of LLMs for RM-API Misuse Detection.", "authors": ["<PERSON>", "Jing<PERSON> Liu", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As the basis of software resource management (RM), strictly following the RM-API constraints guarantees secure resource management and software. To enhance the RM-API application, researchers find it effective in detecting RM-API misuse on open-source software according to RM-API constraints retrieved from documentation and code. However, the current pattern-matching constraint retrieval methods have limitations: the documentation-based methods leave many API constraints irregularly distributed or involving neutral sentiment undiscovered; the code-based methods result in many false bugs due to incorrect API usage since not all high-frequency usages are correct.Therefore, people propose to utilize Large Language Models (LLMs) for RM-API constraint retrieval with their potential on text analysis and generation. However, directly using LLMs has limitations due to the hallucinations. The LLMs fabricate answers without expertise leaving many RM APIs undiscovered and generating incorrect answers even with evidence introducing incorrect RM-API constraints and false bugs. In this paper, we propose an LLM-empowered RM-API misuse detection solution,ChatDetector, which fully automates LLMs for documentation understanding which helps RM-API constraints retrieval and RM-API misuse detection. To correctly retrieve the RM-API constraints,ChatDetectoris inspired by the ReAct framework which is optimized based on Chain-of-Thought (CoT) to decompose the complex task into allocation APIs identification, RM-object (allocated/released by RM APIs) extraction and RM-APIs pairing (RM APIs usually exist in pairs). It first verifies the semantics of allocation APIs based on the retrieved RM sentences from API documentation through LLMs.Inspired by the LLMs' performance on various prompting methods,ChatDetectoradopts a two-dimensional prompting approach for cross-validation. At the same time, an inconsistency-checking approach between the LLMs' output and the reasoning process is adopted for the allocation APIs confirmation with an off-the-shelf Natural Language Processing (NLP) tool. To accurately pair the RM-APIs,ChatDetectordecomposes the task again and identifies the RM-object type first, with which it can then accurately pair the releasing APIs and further construct the RM-API constraints for misuse detection. With the diminished hallucinations,ChatDetectoridentifies 165 pairs of RM-APIs with a precision of 98.21% compared with the state-of-the-art API detectors. By employing a static detector CodeQL, we ethically report 115 security bugs on the applications integrating on six popular libraries to the developers, which may result in severe issues, such as Denial-of-Services (DoS) and memory corruption. Compared with the end-to-end benchmark method, the result shows thatChatDetectorcan retrieve at least 47% more RM sentences and 80.85% more RM-API constraints. Since no work exists specified in utilizing LLMs for RM-API misuse detection to our best knowledge, the inspiring results show that LLMs can assist in generating more constraints beyond expertise and can be used for bug detection. It also indicates that future research could transfer from overcoming the bottlenecks of traditional NLP tools to creatively utilizing LLMs for security research.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190146", "vector": [], "sparse_vector": [], "title": "A Formal Approach to Multi-Layered Privileges for Enclaves.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hongfei Fu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Trusted Execution Environments (TEE) have been widely adopted as a protection approach for security-critical applications. Although feature extensions have been previously proposed to improve the usability of enclaves, their provision patterns are still confronted with security challenges. This paper presents Palantir, a verifiable multi-layered inter-enclave privilege model for secure feature extensions to enclaves. Specifically, a parent-children inter-enclave relationship, with which a parent enclave is granted two privileged permissions, the Execution Control and Spatial Control, over its children enclaves to facilitate secure feature extensions, is introduced. Moreover, by enabling nesting parent-children relationships, Palantir achieves multi-layered privileges (MLP) that allow feature extensions to be placed in various privilege layers following the Principle of Least Privilege. To prove the security of Palantir, we verified that our privilege model does not break or weaken the security guarantees of enclaves by building and verifying a formal model named $text{TAP}^{infty}$. Furthermore, We implemented a prototype of Palantir on Penglai, an open-sourced RISC-V TEE platform. The evaluation demonstrates the promising performance of Palantir in runtime overhead $(<5%)$ and startup latencies.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190147", "vector": [], "sparse_vector": [], "title": "URVFL: Undetectable Data Reconstruction Attack on Vertical Federated Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>u", "Gaoning Pan"], "summary": "Vertical Federated Learning (VFL) is a collaborative learning paradigm designed for scenarios where multiple clients share disjoint features of the same set of data samples. Albeit a wide range of applications, VFL is faced with privacy leakage from data reconstruction attacks. These attacks generally fall into two categories: honest-but-curious (HBC), where adversaries steal data while adhering to the protocol; and malicious attacks, where adversaries breach the training protocol for significant data leakage. While most research has focused on HBC scenarios, the exploration of malicious attacks remains limited. Launching effective malicious attacks in VFL presents unique challenges: 1) Firstly, given the distributed nature of clients’ data features and models, each client rigorously guards its privacy and prohibits direct querying, complicating any attempts to steal data; 2) Existing malicious attacks alter the underlying VFL training task, and are hence easily detected by comparing the received gradients with the ones received in honest training. To overcome these challenges, we develop URVFL, a novel attack strategy that evades current detection mechanisms. The key idea is to integrate a discriminator with auxiliary classifier that takes a full advantage of the label information and generates malicious gradients to the victim clients: on one hand, label information helps to better characterize embeddings of samples from distinct classes, yielding an improved reconstruction performance; on the other hand, computing malicious gradients with label information better mimics the honest training, making the malicious gradients indistinguishable from the honest ones, and the attack much more stealthy. Our comprehensive experiments demonstrate that URVFL significantly outperforms existing attacks, and successfully circumvents SOTA detection methods for malicious attacks. Additional ablation studies and evaluations on defenses further underscore the robustness and effectiveness of URVFL.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190148", "vector": [], "sparse_vector": [], "title": "Too Subtle to Notice: Investigating Executable Stack Issues in Linux Systems.", "authors": ["Hengkai Ye", "Hong Hu"], "summary": "Code injection was a favored technique for attackers to exploit buffer overflow vulnerabilities decades ago. Subsequently, the widespread adoption of lightweight solutions like write-xor-execute (W⊕X) effectively mitigated most of these attacks by disallowing writable-and-executable memory. However, we observe multiple concerning cases where software developers accidentally disabled W⊕X and reintroduced executable stacks to popular applications. Although each violation has been properly fixed, a lingering question remains: what underlying factors contribute to these recurrent mistakes among developers, even in contemporary software development practices? In this paper, we conduct two investigations to gain a comprehensive understanding of the challenges associated with properly enforcing W⊕X in Linux systems. First, we delve into program-hardening tools to assess whether experienced security developers consistently catch the necessary steps to avoid executable stacks. Second, we analyze the enforcement of W⊕X on Linux by inspecting the source code of the compilation toolchain, the kernel, and the loader. Our investigation reveals that properly enforcing W⊕X on Linux requires close collaboration among multiple components. These tools form a complex chain of trust and dependency to safeguard the program stack. However, developers, including security researchers, may overlook the subtle yet essential .note.GNU-stack section when writing assembly code for various purposes, and inadvertently introduce executable stacks. For example, 11 program-hardening tools implemented as inlined reference monitors (IRM) introduce executable stacks to all “hardened” applications. Based on these findings, we discuss potential exploitation scenarios by attackers and provide suggestions to mitigate this issue.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190149", "vector": [], "sparse_vector": [], "title": "Reinforcement Unlearning.", "authors": ["<PERSON><PERSON>", "T<PERSON><PERSON> Zhu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Machine unlearning refers to the process of mitigating the influence of specific training data on machine learning models based on removal requests from data owners. However, one important area that has been largely overlooked in the research of unlearning is reinforcement learning. Reinforcement learning focuses on training an agent to make optimal decisions within an environment to maximize its cumulative rewards. During the training, the agent tends to memorize the features of the environment, which raises a significant concern about privacy. As per data protection regulations, the owner of the environment holds the right to revoke access to the agent's training data, thus necessitating the development of a novel and pressing research field, termed emph{reinforcement unlearning}. Reinforcement unlearning focuses on revoking entire environments rather than individual data samples. This unique characteristic presents three distinct challenges: 1) how to propose unlearning schemes for environments; 2) how to avoid degrading the agent's performance in remaining environments; and 3) how to evaluate the effectiveness of unlearning. To tackle these challenges, we propose two reinforcement unlearning methods. The first method is based on decremental reinforcement learning, which aims to erase the agent's previously acquired knowledge gradually. The second method leverages environment poisoning attacks, which encourage the agent to learn new, albeit incorrect, knowledge to remove the unlearning environment. Particularly, to tackle the third challenge, we introduce the concept of ``environment inference'' to evaluate the unlearning outcomes. The source code is available at url{https://github.com/cp-lab-uts/Reinforcement-Unlearning}.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190150", "vector": [], "sparse_vector": [], "title": "Rethinking Trust in Forge-Based Git Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Git is the most popular version control system today, with Git forges such as GitHub, GitLab, and Bitbucket used to add functionality. Significantly, these forges are used to enforce security controls. However, due to the lack of an open protocol for ensuring a repository's integrity, forges cannot prove themselves to be trustworthy, and have to carry the responsibility of being non-verifiable trusted third parties in modern software supply chains. In this paper, we present textbf{gittuf}, a system that decentralizes Git security and enables every user to contribute to collectively enforcing the repository's security. First, gittuf enables distributing of policy declaration and management responsibilities among more parties such that no single user is trusted entirely or unilaterally.  Second, gittuf decentralizes the tracking of repository activity, ensuring that a single entity cannot manipulate repository events. Third, gittuf decentralizes policy enforcement by enabling all developers to independently verify the policy, eliminating the single point of trust placed in the forge as the only arbiter for whether a change in the repository is authorized.  Thus, gittuf can provide strong security guarantees in the event of a compromise of the centralized forge, the underlying infrastructure, or a subset of privileged developers trusted to set policy.  gittuf also implements policy features that can protect against unauthorized changes to branches and tags (emph{i.e.}, pushes) as well as files/folders (emph{i.e.}, commits).  Our analysis of gittuf shows that its properties and policy features provide protections against previously seen version control system attacks. In addition, our evaluation of gittuf shows it is viable even for large repositories with a high volume of activity such as those of Git and Kubernetes (less than 4% storage overhead and under 0.59s of time to verify each push). Currently, gittuf is an OpenSSF sandbox project hosted by the Linux Foundation. gittuf is being used in projects hosted by the OpenSSF and the CNCF, and an enterprise pilot at Bloomberg is underway.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190151", "vector": [], "sparse_vector": [], "title": "DShield: Defending against Backdoor Attacks on Graph Neural Networks via Discrepancy Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Xinhang Wan", "<PERSON>", "Tao <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph Neural Networks (GNNs) are vulnerable to backdoor attacks, where triggers inserted into original graphs cause adversary-determined predictions. Backdoor attacks on GNNs, typically focusing on node classification tasks, are categorized by dirty- and clean-label attacks and pose challenges due to the interconnected nature of normal and poisoned nodes. Current defenses are indeed circumvented by sophisticated triggers and often rely on strong assumptions borrowed from other domains (e.g., rapid loss drops on poisoned images). They lead to high attack risks, failing to effectively protect against both dirty- and clean-label attacks simultaneously. To tackle these challenges, we propose DShield, a comprehensive defense framework with a discrepancy learning mechanism to defend against various graph backdoor attacks. Specifically, we reveal two vital facts during the attacking process: *semantic drift* where dirty-label attacks modify the semantic information of poisoned nodes, and *attribute over-emphasis* where clean-label attacks exaggerate specific attributes to enforce adversary-determined predictions. Motivated by those, DShield employs a self-supervised learning framework to construct a model without relying on manipulated label information. Subsequently, it utilizes both the self-supervised and backdoored models to analyze discrepancies in semantic information and attribute importance, effectively filtering out poisoned nodes. Finally, DShield trains normal models using the preserved nodes, thereby minimizing the impact of poisoned nodes. Compared with 6 state-of-the-art defenses under 21 backdoor attacks, we conduct evaluations on 7 datasets with 2 victim models to demonstrate that DShield effectively mitigates backdoor threats with minimal degradation in performance on normal nodes. For instance, on the Cora dataset, DShield reduces the attack success rate to 1.33% from 54.47% achieved by the second-best defense Prune while maintaining an 82.15% performance on normal nodes. The source code is available at https://github.com/csyuhao/DShield.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190152", "vector": [], "sparse_vector": [], "title": "What&apos;s Done Is Not What&apos;s Claimed: Detecting and Interpreting Inconsistencies in App Behaviors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The widespread use of mobile apps meets user needs but also raises security concerns. Current security analysis methods often fall short in addressing user concerns as they do not parse app behavior from the user's standpoint, leading to users not fully understanding the risks within the apps and unknowingly exposing themselves to privacy breaches. On one hand, their analysis and results are usually presented at the code level, which may not be comprehensible to users. On the other hand, they neglect to account for the users' perceptions of the app behavior. In this paper, we aim to extract user-related behaviors from apps and explain them to users in a comprehensible natural language form, enabling users to perceive the gap between their expectations and the app's actual behavior, and assess the risks within the inconsistencies independently. Through experiments, our tool emph{InconPreter} is shown to effectively extract inconsistent behaviors from apps and provide accurate and reasonable explanations. InconPreter achieves an inconsistency identification precision of 94.89% on our labeled dataset, and a risk analysis accuracy of 94.56% on widely used Android malware datasets. When applied to real-world (wild) apps, InconPreter identifies 1,664 risky inconsistent behaviors from 413 apps out of 10,878 apps crawled from Google Play, including the leakage of location, SMS, and contact information, as well as unauthorized audio recording, etc., potentially affecting millions of users. Moreover, InconPreter can detect some behaviors that are not identified by previous tools, such as unauthorized location disclosure in various scenarios (e.g. taking photos, chatting, and enabling mobile hotspots, etc.). We conduct a thorough analysis of the discovered behaviors to deepen the understanding of inconsistent behaviors, thereby helping users better manage their privacy and providing insights for privacy design in further app development.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190153", "vector": [], "sparse_vector": [], "title": "CLIBE: Detecting Dynamic Backdoors in Transformer-based NLP Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shouling Ji"], "summary": "Backdoors can be injected into NLP models to induce misbehavior when the input text contains a specific feature, known as a trigger, which the attacker secretly selects. Unlike fixed tokens, words, phrases, or sentences used in the textit{static} text trigger, textit{dynamic} backdoor attacks on NLP models design triggers associated with abstract and latent text features (e.g., style), making them considerably stealthier than traditional static backdoor attacks. However, existing research on NLP backdoor detection primarily focuses on defending against static backdoor attacks, while research on detecting dynamic backdoors in NLP models remains largely unexplored. This paper presents CLIBE, the first framework to detect dynamic backdoors in Transformer-based NLP models. At a high level, CLIBE injects a textit{\"few-shot perturbation\"} into the suspect Transformer model by crafting an optimized weight perturbation in the attention layers to make the perturbed model classify a limited number of reference samples as a target label. Subsequently, CLIBE leverages the textit{generalization} capability of this \"few-shot perturbation\" to determine whether the original suspect model contains a dynamic backdoor. Extensive evaluation on three advanced NLP dynamic backdoor attacks, two widely-used Transformer frameworks, and four real-world classification tasks strongly validates the effectiveness and generality of CLIBE. We also demonstrate the robustness of CLIBE against various adaptive attacks. Furthermore, we employ CLIBE to scrutinize 49 popular Transformer models on Hugging Face and discover one model exhibiting a high probability of containing a dynamic backdoor. We have contacted Hugging Face and provided detailed evidence of the backdoor behavior of this model. Moreover, we show that CLIBE can be easily extended to detect backdoor text generation models (e.g., GPT-Neo-1.3B) that are modified to exhibit toxic behavior. To the best of our knowledge, CLIBE is the first framework capable of detecting backdoors in text generation models without requiring access to trigger input test samples. The code is available at https://github.com/Raytsang123/CLIBE.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190154", "vector": [], "sparse_vector": [], "title": "MingledPie: A Cluster Mingling Approach for Mitigating Preference Profiling in CFL.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Clustered federated learning (CFL) serves as a promising framework to address the challenges of non-IID (non-Independent and Identically Distributed) data and heterogeneity in federated learning. It involves grouping clients into clusters based on the similarity of their data distributions or model updates. However, classic CFL frameworks pose severe threats to clients' privacy since the honest-but-curious server can easily know the bias of clients' data distributions (its preferences). In this work, we propose a privacy-enhanced clustered federated learning framework, MingledPie, aiming to resist against servers' preference profiling capabilities by allowing clients to be grouped into multiple clusters spontaneously. Specifically, within a given cluster, we mingled two types of clients in which a major type of clients share similar data distributions while a small portion of them do not (false positive clients). Such that, the CFL server fails to link clients' data preferences based on their belonged cluster categories. To achieve this, we design an indistinguishable cluster identity generation approach to enable clients to form clusters with a certain proportion of false positive members without the assistance of a CFL server.  Meanwhile, training with mingled false positive clients will inevitably degrade the performances of the cluster's global model. To rebuild an accurate cluster model, we represent the mingled cluster models as a system of linear equations consisting of the accurate models and solve it. Rigid theoretical analyses are conducted to evaluate the usability and security of the proposed designs. In addition, extensive evaluations of MingledPie on six open-sourced datasets show that it defends against preference profiling attacks with an accuracy of 69.4% on average. Besides, the model accuracy loss is limited to between 0.02% and 3.00%.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190155", "vector": [], "sparse_vector": [], "title": "Misdirection of Trust: Demystifying the Abuse of Dedicated URL Shortening Service.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "underline{D}edicated underline{U}RL underline{s}hortening underline{s}ervices (DUSSs) are designed to transform textit{trusted} long URLs into the shortened links.Since DUSSs are widely used in famous corporations to better serve their large number of users (especially mobile users), cyber criminals attempt to exploit DUSS to transform their malicious links and abuse the inherited implicit trust, which is defined as textit{Misdirection Attack} in this paper.However, little effort has been made to systematically understand such attacks. To fulfill the research gap, we present the first systematic study of the textit{Misdirection Attack} in abusing DUSS to demystify its attack surface, exploitable scope, and security impacts in the real world. Our study reveals that real-world DUSSs commonly rely on custom URL checks, yet they exhibit unreliable security assumptions regarding web domains and lack adherence to security standards.We design and implement a novel tool, <PERSON><PERSON>, for empirically studying vulnerable DUSSs from a mobile perspective.Our large-scale study reveals that a quarter of the DUSSs are susceptible to textit{Misdirection Attack}.More importantly, we find that DUSSs hold implicit trust from both their users and domain-based checkers, extending the consequences of the attack to stealthy phishing and code injection on users' mobile phones.We have responsibly reported all of our findings to corporations of the affected DUSS and helped them fix their vulnerabilities.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190156", "vector": [], "sparse_vector": [], "title": "BARBIE: Robust Backdoor Detection Based on Latent Separability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhongming Ma", "<PERSON><PERSON> Xu"], "summary": "Backdoor attacks are an essential risk to deep learning model sharing. Fundamentally, backdoored models are different from benign models considering latent separability, i.e., distinguishable differences in model latent representations. However, existing methods quantify latent separability by clustering latent representations or computing distances between latent representations, which are easy to be compromised by adaptive attacks. In this paper, we propose BARBIE, a backdoor detection approach that can pinpoint latent separability under adaptive backdoor attacks. To achieve this goal, we propose a new latent separability metric, named relative competition score (RCS), by characterizing the dominance of latent representations over model output, which is robust against various backdoor attacks and is hard to compromise. Without the need to access any benign or backdoored sample, we invert two sets of latent representations of each label, reflecting the normal latent representations of benign models and intensifying the abnormal ones of backdoored models, to calculate RCS. We compute a series of RCS-based indicators to comprehensively reflect the differences between backdoored models and benign models. We validate the effectiveness of BARBIE on more than 10,000 models on 4 datasets against 14 types of backdoor attacks, including the adaptive attacks against latent separability. Compared with 7 baselines, BARBIE improves the average true positive rate by 17.05% against source-agnostic attacks, 27.72% against source-specific attacks, 43.17% against sample-specific attacks and 11.48% against clean-label attacks. BARBIE also maintains lower false positive rates than baselines. The source code is available at: https://github.com/Forliqr/BARBIE.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190157", "vector": [], "sparse_vector": [], "title": "RadSee: See Your Handwriting Through Walls Using FMCW Radar.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper aims to design and implement a radio device capable of detecting a person's handwriting through a wall. Although there is extensive research on radio frequency (RF) based human activity recognition, this task is particularly challenging due to the textit{through-wall} requirement and the textit{tiny-scale} handwriting movements. To address these challenges, we present RadSee---a 6 GHz frequency modulated continuous wave (FMCW) radar system designed for detecting handwriting content behind a wall. RadSee is realized through a joint hardware and software design. On the hardware side, RadSee features a 6 GHz FMCW radar device equipped with two custom-designed, high-gain patch antennas. These two antennas provide a sufficient link power budget, allowing <PERSON><PERSON><PERSON><PERSON> to \"see'' through most walls with a small transmission power. On the software side, <PERSON>d<PERSON><PERSON> extracts effective phase features corresponding to the writer's hand movements and employs a bidirectional LSTM (BiLSTM) model with an attention mechanism to classify handwriting letters. As a result, RadSee can detect millimeter-level handwriting movements and recognize most letters based on their unique phase patterns. Additionally, it is resilient to interference from other moving objects and in-band radio devices. We have built a prototype of RadSee and evaluated its performance in various scenarios. Extensive experimental results demonstrate that Rad<PERSON><PERSON> achieves 75% letter recognition accuracy when victims write 62 random letters, and 87% word recognition accuracy when they write articles.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190158", "vector": [], "sparse_vector": [], "title": "Secure Transformer Inference Made Non-interactive.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Secure transformer inference has emerged as a prominent research topic following the proliferation of ChatGPT. Existing solutions are typically interactive, involving substantial communication load and numerous interaction rounds between the client and the server. In this paper, we propose NEXUS, the first non-interactive protocol for secure transformer inference. The protocol requires the client to engage in just one round of communication with the server during the whole inference process: submitting an encrypted input and receiving an encrypted result.NEXUS introduces several novel primitives, including SIMD ciphertext compression/decompression, SIMD slot folding, and secure Argmax, which enable it to significantly surpass the state-of-the-art in communication while maintaining comparable runtime. Specifically, it reduces bandwidth consumption by 372.5$times$ compared to BOLT (Oakland~'24) and 53.6$times$ compared to Bumblebee (NDSS~'25). Furthermore, its non-interactive property allows for optimal hardware acceleration, with the GPU version achieving a 42.3$times$ speedup in runtime. This enables NEXUS to run inference on a BERT-based model in just 37.3 seconds, consuming only 164~MB of bandwidth.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190159", "vector": [], "sparse_vector": [], "title": "Hitchhiking Vaccine: Enhancing Botnet Remediation With Remote Code Deployment Reuse.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jeman Park", "<PERSON>"], "summary": "For decades, law enforcement and commercial entities have attempted botnet takedowns with mixed success. These efforts, relying on DNS sink-holing or seizing C&C infrastructure, require months of preparation and often omit the cleanup of left-over infected machines. This allows botnet operators to push updates to the bots and re-establish their control. In this paper, we expand the goal of malware takedowns to include the covert and timely removal of frontend bots from infected devices. Specifically, this work proposes seizing the malware's built-in update mechanism to distribute crafted remediation payloads. Our research aims to enable this necessary but challenging remediation step after obtaining legal permission. We developed ECHO, an automated malware forensics pipeline that extracts payload deployment routines and generates remediation payloads to disable or remove the frontend bots on infected devices. Our study of 702 Android malware shows that 523 malware can be remediated via ECHO's takedown approach, ranging from covertly warning users about malware infection to uninstalling the malware.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190160", "vector": [], "sparse_vector": [], "title": "An Empirical Study on Fingerprint API Misuse with Lifecycle Analysis in Real-world Android Apps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Fingerprint-based authentication (FpAuth) is increasingly utilized by Android apps, particularly in highly sensitive scenarios such as account login and payment, as it can provide a convenient method for verifying user identity. However, the correct and secure use of Android fingerprint APIs (FpAPIs) in real-world mobile apps remains a challenge due to their complex and evolving nature. This paper presents the first systematic empirical analysis of FpAPI misuses in Android apps from the perspective of the FpAuth lifecycle. First, we develop specialized tools to identify and analyze apps employing FpAPIs, examining their characteristics. Then we define the threat models and categorize four prevalent types of FpAPI misuses through a detailed lifecycle analysis in practical settings. Finally, we develop tools to automatically detect these misuse types in 1,333 apps that use FpAuth and find alarming results: 97.15% of them are vulnerable to at least one type of misuse, with 18.83% susceptible to all identified misuse types. The consequences of such misuses are significant, including unauthorized data access, account compromise, and even financial loss, impacting a broad user base. We have responsibly reported these vulnerabilities, resulting in the issuance of 184 CVE IDs and 19 China National Vulnerability Database (CNVD) IDs, as well as acknowledgment from 15 vendors. We hope this work can raise awareness and emphasize the importance of proper usage of FpAPIs.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190161", "vector": [], "sparse_vector": [], "title": "ERW-Radar: An Adaptive Detection System against Evasive Ransomware by Contextual Behavior Detection and Fine-grained Content Analysis.", "authors": ["Ling<PERSON> Zhao", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Fengkai Yuan", "<PERSON><PERSON>"], "summary": "To evade existing antivirus software and detection systems, ransomware authors tend to obscure behavior differences with benign programs by imitating them or by weakening malicious behaviors during encryption. Existing defense solutions have limited effects on defending against evasive ransomware. Fortunately, through extensive observation, we find I/O behaviors of evasive ransomware exhibit a unique repetitiveness during encryption. This is rarely observed in benign programs. Besides, the $chi^2$ test and the probability distribution of byte streams can effectively distinguish encrypted files from benignly modified files. Inspired by these, we first propose ERW-Radar, a detection system, to detect evasive ransomware accurately and efficiently. We make three breakthroughs: 1) a contextual emph{Correlation} mechanism to detect malicious behaviors; 2) a fine-grained content emph{Analysis} mechanism to identify encrypted files; and 3) adaptive mechanisms to achieve a better trade-off between accuracy and efficiency. Experiments show that ERW-Radar detects evasive ransomware with an accuracy of 96.18% while maintaining a FPR of 5.36%. The average overhead of ERW-Radar is 5.09% in CPU utilization and 3.80% in memory utilization.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190162", "vector": [], "sparse_vector": [], "title": "RContainer: A Secure Container Architecture through Extending ARM CCA Hardware Primitives.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Containers have become widely adopted in cloud platforms due to their efficient deployment and high resource utilization. However, their weak isolation has always posed a significant security concern. In this paper, we propose RContainer, a novel secure container architecture that protects containers from untrusted operating systems and enforces strong isolation among containers by extending ARM Confidential Computing Architecture (CCA) hardware primitives. RContainer introduces a small, trusted mini-OS that runs alongside the deprivileged OS, responsible for monitoring the control flow between the operating system and containers. Additionally, RContainer uses shim-style isolation, creating an isolated physical address space called con-shim for each container at the kernel layer through the Granule Protection Check mechanism. We have implemented RContainer on ARMv9-A Fixed Virtual Platform and ARMv8 hardware SoC for security analysis and performance evaluation. Experimental results demonstrate that RContainer can significantly enhance container security with a modest performance overhead and a minimal Trusted Computing Base (TCB).", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190163", "vector": [], "sparse_vector": [], "title": "GAP-Diff: Protecting JPEG-Compressed Images from Diffusion-based Facial Customization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhigang Lu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Text-to-image diffusion model's fine-tuning technology allows people to easily generate a large number of customized photos using limited identity images. Although this technology is easy to use, its misuse could lead to violations of personal portraits and privacy, with false information and harmful content potentially causing further harm to individuals. Several methods have been proposed to protect faces from customization via adding protective noise to user images by disrupting the fine-tuned models. Unfortunately, simple pre-processing techniques like JPEG compression,  a normal pre-processing operation performed by modern social networks, can easily erase the protective effects of existing methods. To counter JPEG compression and other potential pre-processing, we propose GAP-Diff, a framework of underline{G}enerating data with underline{A}dversarial underline{P}erturbations for text-to-image underline{Diff}usion models using unsupervised learning-based optimization, including three functional modules. Specifically, our framework learns robust representations against JPEG compression by backpropagating gradient information through a pre-processing simulation module while learning adversarial characteristics for disrupting fine-tuned text-to-image diffusion models. Furthermore, we achieve an adversarial mapping from clean images to protected images by designing adversarial losses against these fine-tuning methods and JPEG compression, with stronger protective noises within milliseconds. Facial benchmark experiments, compared to state-of-the-art protective methods, demonstrate that GAP-Diff significantly enhances the resistance of protective noise to JPEG compression, thereby better safeguarding user privacy and copyrights in the digital world.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190164", "vector": [], "sparse_vector": [], "title": "I know what you MEME! Understanding and Detecting Harmful Memes with Multimodal Large Language Models.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Jing", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Hong<PERSON> Hu"], "summary": "Memes have become a double-edged sword on social media platforms. On one hand, they facilitate the rapid dissemination of information and enhance communication. On the other hand, memes pose a risk of spreading harmful content under the guise of humor and virality. This duality highlights the need to develop effective moderation tools capable of identifying harmful memes. Current detection methods, however, face significant challenges in identifying harmful memes due to their inherent complexity. This complexity arises from the diverse forms of expression, intricate compositions, sophisticated propaganda techniques, and varied cultural contexts in which memes are created and circulated. These factors make it difficult for existing algorithms to distinguish between harmless and harmful content accurately. To understand and address these challenges, we first conduct a comprehensive study on harmful memes from two novel perspectives: visual arts and propaganda techniques. It aims to assess existing tools for detecting harmful memes and understand the complexities inherent in them. Our findings demonstrate that meme compositions and propaganda techniques can significantly diminish the effectiveness of current harmful meme detection methods. Inspired by our observations and understanding of harmful memes, we propose a novel framework called HMGUARD for effective detection of harmful memes. HMGUARD utilizes adaptive prompting and chain-of-thought (CoT) reasoning in multimodal large language models (MLLMs). HMGUARD has demonstrated remarkable performance on the public harmful meme dataset, achieving an accuracy of 0.92. Compared to the baseline, HMGUARD represents a substantial improvement, with accuracy exceeding the baselines by 15% to 79.17%. Additionally, HMGUARD outperforms existing detection tools, achieving an impressive accuracy of 0.88 in real-world scenarios.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "190165", "vector": [], "sparse_vector": [], "title": "Blackbox Fuzzing of Distributed Systems with Multi-Dimensional Inputs and Symmetry-Based Feedback Pruning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents DistFuzz, which, to our knowledge, is the first feedback-guided blackbox fuzzing framework for distributed systems. The novelty of DistFuzz comes from two conceptual contributions on key aspects of distributed system fuzzing: the input space and feedback metrics. Specifically, unlike prior work that focuses on systematically mutating faults, exploiting the request-driven and timing-dependence nature of distributed systems, DistFuzz proposes a multi-dimensional input space by incorporating regular events and relative timing among events as the other two dimensions. Furthermore, observing that important state changes in distributed systems can be indicated by network messages among nodes, DistFuzz utilizes the sequences of network messages with symmetry-based pruning as program feedback, which departs from the conventional wisdom that effective feedback requires code instrumentation/analysis and/or user inputs. DistFuzz finds 52 real bugs in ten popular distributed systems in C/C++, Go, and Java. Among these bugs, 28 have been confirmed by the developers, 20 were unknown before, and 4 have been assigned with CVEs.", "published": "2025-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}]