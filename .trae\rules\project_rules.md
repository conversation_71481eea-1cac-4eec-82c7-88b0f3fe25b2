# 1. 项目通用开发规范
## 目录结构
```
    arxiv-insight/
    ├── app/                    # Next.js 应用主目录
    │   ├── api/               # API 路由接口
    │   ├── chat/             # 临时聊天功能验证页面
    │   ├── map/              # 图模式，目前测试中
    │   ├── survey/           # 网站主界面，用于论文搜索和对话综述等功能
    │   ├── config/           # 应用配置
    │   ├── layout.tsx        # 全局布局组件
    │   └── globals.css       # 全局样式文件
    │
    ├── components/            # 共享组件
    │   ├── ui/               # UI 基础组件
    │   ├── latex/            # Latex 公式编辑基础组件
    │   ├── survey/           # survey 页面相关组件
    │   ├── markdown.tsx      # Markdown 渲染组件
    │   ├── icons.tsx         # 图标组件
    │   └── theme-provider.tsx # 主题提供者组件
    │
    ├── lib/                   # 工具库和共享代码（如 LLM 的 id name，预设参数等）
    │
    ├── backend/               # 后端服务及python脚本相关代码
    │   ├── data/             # 论文数据存储和处理相关代码
    │   ├── service/          # 主要服务逻辑代码（直接服务于 controller）
    │   ├── utils/            # 工具代码（乱七八糟的都放这里）
    │   └── controller.py     # API 接口相关代码
    │
    ├── public/                # 静态资源文件
    │
    ├── hooks/                 # React Hooks
    │
    ├── 配置文件
    │   ├── next.config.ts     # Next.js 配置
    │   ├── tailwind.config.ts # Tailwind CSS 配置
    │   ├── tsconfig.json      # TypeScript 配置
    │   ├── biome.jsonc        # Biome 代码格式化配置
    │   ├── postcss.config.mjs # PostCSS 配置
    │   └── .eslintrc.json    # ESLint 配置
    │
    ├── 脚本文件
    │   ├── cron_arxiv.sh      # ArXiv 数据同步脚本
    │   └── cron_arxiv_daemon.sh # ArXiv 数据同步守护进程
    │
    ├── package.json           # 项目依赖配置
    └── README.md             # 项目说明文档
```

## 组织原则
── 保持项目结构清晰，遵循摸块化原则
── 相关功能应放在同一目录下
── 使用适当的目录命名，反映其包含内容
── 单个代码文件行数不宜超过三百行
── 单个函数不宜超过 50 行

## 命名规范

类名：PascalCase(大驼峰)
── 函数名：camelCase(小驼蜂) 或 snake_case
── 常量：UPPER SNAKE CASE
── 函数参数：使用语义化名称（如 `userInput` 而非 `input1`）
── 接口/类型：`IProps` → `ChatComponentProps`（具体化命名）
── 布尔变量：以 is/has/can 开头（`isLoading`, `hasPermission`）
── 事件处理：handle[元素][事件]（`handleInputChange`, `handleSubmitClick`）

## 注释规范
── 关键部分代码应有良好且简介的中文注释，但不宜过多，适量即可
── 所有注释之间应该形成良好的层次感和协同
── 函数注释示例如下：

'''获取指定日期的论文
Detail:
    这里对这个函数进行更详细的解释
Args:
    day: 日期字符串，格式为 YYYYMMDD，如 20240101
    save_path: 保存数据的目录，默认为['data']
Returns (Yields):
    json_path: 保存数据的路径
'''

── 类注释示例如下：

"""大语言模型服务处理类
Attributes:
    model_name (str): 当前使用的LLM模型名称
    temperature (float): 生成温度参数
    
Methods:
    generate_stream: 创建流式响应生成器
"""

# 2. 前端规范

## 前端架构

样式主要使用 Tailwind 风格
尽可能以黑白色调为主，所有实现都要考虑暗黑模式下的表现，以及在不同屏幕尺寸下的适配
前端使用 pnpm 作为包管理工具，使用 React 框架

前端代码尽可能将一类功能的代码放在一个文件中，如一个组件的所有代码放在一个文件中
组件代码尽可能保持简洁，避免过于复杂的逻辑
组件代码尽可能保持单一职责，避免一个组件承担过多的职责
组件代码尽可能保持可维护性，避免维护困难
所有关键部分代码都要加入中文注释

前端 UI 尽可能简洁，且友好，多用圆角，以及合理的阴影

## 前端组件