[{"primary_key": "4468790", "vector": [], "sparse_vector": [], "title": "Optimistic Shared Memory Dependence Tracing (T).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Ma", "<PERSON><PERSON>"], "summary": "Inter-thread shared memory dependences are crucial to understanding the behavior of concurrent systems, as such dependences are the cornerstone of time-travel debugging and further predictive trace analyses. To enable effective and efficient shared memory dependence tracing, we present an optimistic scheme addressing the challenge of capturing exact dependences between unsynchronized events to reduce the probe effect of program instrumentation. Specifically, our approach achieved a wait-free fast path for thread-local reads on x86-TSO relaxed memory systems, and simultaneously achieved precise tracing of exact read-after-write, write-after-write and write-after-read dependences on the fly. We implemented an open-source RWTrace tool, and evaluation results show that our approach not only achieves efficient shared memory dependence tracing, but also scales well on a multi-core computer system.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.11"}, {"primary_key": "4468791", "vector": [], "sparse_vector": [], "title": "Extracting Visual Contracts from Java Programs (T).", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Visual contracts model the operations of components or services by pre-and post-conditions formalised as graph transformation rules. They provide a precise intuitive notation to support testing, understanding and analysis of software. However, due to their detailed specification of data states and transformations, modelling real applications is an error-prone process. In this paper we propose a dynamic approach to reverse engineering visual contracts from Java based on tracing the execution of Java operations. The resulting contracts give an accurate description of the observed object transformations, their effects and preconditions in terms of object structures, parameter and attribute values, and their generalised specification by universally quantified (multi) objects. While this paper focusses on the fundamental technique rather than a particular application, we explore potential uses in our evaluation, including in program understanding, review of test reports and debugging.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.63"}, {"primary_key": "4468794", "vector": [], "sparse_vector": [], "title": "Configuration-Aware Change Impact Analysis (T).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Understanding variability is essential to allow the configuration of software systems to diverse requirements. Variability-aware program analysis techniques have been proposed for analyzing the space of program variants. Such techniques are highly beneficial, e.g., to determine the potential impact of changes during maintenance. This paper presents an interprocedural and configuration-aware change impact analysis (CIA) approach for determining possibly impacted products when changing source code of a product family. The approach further supports engineers, who are adapting specific product variants after an initial pre-configuration. The approach can be adapted to work with different variability mechanism, it provides more precise results than existing CIA approaches, and it can be implemented using standard control flow and data flow analysis. Using an industrial product line we report evaluation results on the benefit and performance of the approach.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.58"}, {"primary_key": "4468795", "vector": [], "sparse_vector": [], "title": "Model-Based Testing of Stateful APIs with Modbat.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modbat makes testing easier by providing a user-friendly modeling language to describe the behavior of systems, from such a model, test cases are generated and executed. Modbat's domain-specific language is based on Scala, its features include probabilistic and non-deterministic transitions, component models with inheritance, and exceptions. We demonstrate the versatility of Mod<PERSON> by finding a confirmed defect in the currently latest version of Java, and by testing SAT solvers.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.95"}, {"primary_key": "4468796", "vector": [], "sparse_vector": [], "title": "Synthesizing Web Element Locators (T).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "To programmatically interact with the user interface of a web application, element locators are used to select and retrieve elements from the Document Object Model (DOM). Element locators are used in JavaScript code, Cascading stylesheets, and test cases to interact with the runtime DOM of the webpage. Constructing these element locators is, however, challenging due to the dynamic nature of the DOM. We find that locators written by web developers can be quite complex, and involve selecting multiple DOM elements. We present an automated technique for synthesizing DOM element locators using examples provided interactively by the developer. The main insight in our approach is that the problem of synthesizing complex multi-element locators can be expressed as a constraint solving problem over the domain of valid DOM states in a web application. We implemented our synthesis technique in a tool called LED, which provides an interactive drag and drop support inside the browser for selecting positive and negative examples. We find that LED supports at least 86% of the locators used in the JavaScript code of deployed web applications, and that the locators synthesized by LED have a recall of 98% and a precision of 63%. LED is fast, taking only 0.23 seconds on average to synthesize a locator.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.23"}, {"primary_key": "4468797", "vector": [], "sparse_vector": [], "title": "LED: Tool for Synthesizing Web Element Locators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Web applications are growing fast in popularity and complexity. One of the major problems faced by web developers is writing JavaScript code that can retrieve Document Object Model (DOM) tree elements, and is consistent among multiple DOM states. We attempt to solve this problem by automatically synthesizing JavaScript code that interacts with the DOM. We present an automated tool called LED, to analyze the DOM elements, and synthesize code to select the DOM elements based on the DOM hierarchy as well as the nature of task that the user wants to perform. LED provides an interactive drag and drop support inside the browser for selecting positive and negative examples of DOM elements. We find that LED supports at least 86% of the locators used in the JavaScript code of deployed web applications, and that the locators synthesized by LED have a recall of 98% and a precision of 63%. LED is fast, taking only 0.23 seconds on average to synthesize a locator.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.110"}, {"primary_key": "4468798", "vector": [], "sparse_vector": [], "title": "Tracking and Analyzing Cross-Cutting Activities in Developers&apos; Daily Work (N).", "authors": ["Ling<PERSON> Bao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Developers use many software applications to process large amounts of diverse information in their daily work. The information is usually meaningful beyond the context of an application that manages it. However, as different applications function independently, developers have to manually track, correlate and re-find cross-cutting information across separate applications. We refer to this difficulty as information fragmentation problem. In this paper, we present ActivitySpace, an interapplication activity tracking and analysis framework for tackling information fragmentation problem in software development. ActivitySpace can monitor the developer's activity in many applications at a low enough level to obviate application-specific support while accounting for the ways by which low-level activity information can be effectively aggregated to reflect the developer's activity at higher-level of abstraction. A system prototype has been implemented on Microsoft Windows. Our preliminary user study showed that the ActivitySpace system is promising in supporting interapplication information needs in developers' daily work.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.43"}, {"primary_key": "4468799", "vector": [], "sparse_vector": [], "title": "ActivitySpace: A Remembrance Framework to Support Interapplication Information Needs.", "authors": ["Ling<PERSON> Bao", "<PERSON>heng Ye", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Developers' daily work produces, transforms, and communicates cross-cutting information across applications, including IDEs, emails, Q&A sites, Twitter, and many others. However, these applications function independently of one another. Even though each application has their own effective information management mechanisms, cross-cutting information across separate applications creates a problem of information fragmentation, forcing developers to manually track, correlate, and re-find cross-cutting information across applications. In this paper, we present ActivitySpace, a remembrance framework that unobtrusively tracks and analyze a developer's daily work in separate applications, and provides various semantic and episodic UIs that help developers correlate and re-find cross-cutting information across applications based on information content, time and place of his/her activities. Through a user study of 8 participants, we demonstrate how ActivitySpace helps to tackle information fragmentation problem in developers' daily work.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.90"}, {"primary_key": "4468800", "vector": [], "sparse_vector": [], "title": "Static Analysis of Implicit Control Flow: Resolving Java Reflection and Android Intents (T).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> d&a<PERSON>;<PERSON><PERSON>", "<PERSON>"], "summary": "Implicit or indirect control flow is a transfer of control between procedures using some mechanism other than an explicit procedure call. Implicit control flow is a staple design pattern that adds flexibility to system design. However, it is challenging for a static analysis to compute or verify properties about a system that uses implicit control flow. This paper presents static analyses for two types of implicit control flow that frequently appear in Android apps: Java reflection and Android intents. Our analyses help to resolve where control flows and what data is passed. This information improves the precision of downstream analyses, which no longer need to make conservative assumptions about implicit control flow. We have implemented our techniques for Java. We enhanced an existing security analysis with a more precise treatment of reflection and intents. In a case study involving ten real-world Android apps that use both intents and reflection, the precision of the security analysis was increased on average by two orders of magnitude. The precision of two other downstream analyses was also improved.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.69"}, {"primary_key": "4468801", "vector": [], "sparse_vector": [], "title": "Efficient Data Model Verification with Many-Sorted Logic (T).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Misuse or loss of web application data can have catastrophic consequences in today's Internet oriented world. Hence, verification of web application data models is of paramount importance. We have developed a framework for verification of web application data models via translation to First Order Logic (FOL), followed by automated theorem proving. Due to the undecidability of FOL, this automated approach does not always produce a conclusive answer. In this paper, we investigate the use of many-sorted logic in data model verification in order to improve the effectiveness of this approach. Many-sorted logic allows us to specify type information explicitly, thus lightening the burden of reasoning about type information during theorem proving. Our experiments demonstrate that using many-sorted logic improves the verification performance significantly, and completely eliminates inconclusive results in all cases over 7 real world web applications, down from an 17% inconclusive rate.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.48"}, {"primary_key": "4468803", "vector": [], "sparse_vector": [], "title": "Testing Cross-Platform Mobile App Development Frameworks (T).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile app developers often wish to make their apps available on a wide variety of platforms, e.g., Android, iOS, and Windows devices. Each of these platforms uses a different programming environment, each with its own language and APIs for app development. Small app development teams lack the resources and the expertise to build and maintain separate code bases of the app customized for each platform. As a result, we are beginning to see a number of cross-platform mobile app development frameworks. These frameworks allow the app developers to specify the business logic of the app once, using the language and APIs of a home platform (e.g., Windows Phone), and automatically produce versions of the app for multiple target platforms (e.g., iOS and Android). In this paper, we focus on the problem of testing cross-platform app development frameworks. Such frameworks are challenging to develop because they must correctly translate the home platform API to the (possibly disparate) target platform API while providing the same behavior. We develop a differential testing methodology to identify inconsistencies in the way that these frameworks handle the APIs of the home and target platforms. We have built a prototype testing tool, called X-Checker, and have applied it to test Xamarin, a popular framework that allows Windows Phone apps to be cross-compiled into native Android (and iOS) apps. To date, X-Checker has found 47 bugs in Xamarin, corresponding to inconsistencies in the way that Xamarin translates between the semantics of the Windows Phone and the Android APIs. We have reported these bugs to the Xamarin developers, who have already committed patches for twelve of them.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.21"}, {"primary_key": "4468804", "vector": [], "sparse_vector": [], "title": "FLYAQ: Enabling Non-expert Users to Specify and Generate Missions of Autonomous Multicopters.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multicopters are increasingly popular since they promise to simplify a myriad of everyday tasks. Currently, vendors provide low-level APIs and basic primitives to program multicopters, making mission development a task-specific and error-prone activity. As a consequence, current approaches are affordable only for users that have a strong technical expertise. Then, software engineering techniques are needed to support the definition, development, and realization of missions at the right level of abstraction and involving teams of autonomous multicopters that guarantee the safety today's users expect. In this paper we describe a tool that enables end-users with no technical expertise, e.g., firefighters and rescue workers, to specify missions for a team of multicopters. The detailed flight plan that each multicopter must perform to accomplish the specified mission is automatically generated by preventing collisions between multicopters and obstacles, and ensuring the preservation of no-fly zones.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.104"}, {"primary_key": "4468805", "vector": [], "sparse_vector": [], "title": "Measuring Object-Oriented Design Principles.", "authors": ["<PERSON>"], "summary": "The idea of automatizing the assessment of object-oriented design is not new. Different approaches define and apply their own quality models, which are composed of single metrics or combinations thereof, to operationalize software design. However, single metrics are too fine-grained to identify core design flaws and they cannot provide hints for making design improvements. In order to deal with these weaknesses of metric-based models, rules-based approaches have proven successful in the realm of source-code quality. Moreover, for developing a well-designed software system, design principles play a key role, as they define fundamental guidelines and help to avoid pitfalls. Therefore, this thesis will enhance and complete a rule-based quality reference model for operationalizing design principles and will provide a measuring tool that implements these rules. The validation of the design quality model and the measurement tool will be based on various industrial projects. Additionally, quantitative and qualitative surveys will be conducted in order to get validated results on the value of object-oriented design principles for software development.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.17"}, {"primary_key": "4468806", "vector": [], "sparse_vector": [], "title": "Dynamically Testing GUIs Using Ant Colony Optimization (T).", "authors": ["Santo <PERSON>", "<PERSON>"], "summary": "In this paper we introduce a dynamic GUI test generator that incorporates ant colony optimization. We created two ant systems for generating tests. Our first ant system implements the normal ant colony optimization algorithm in order to traverse the GUI and find good event sequences. Our second ant system, called AntQ, implements the antq algorithm that incorporates Q-Learning, which is a behavioral reinforcement learning technique. Both systems use the same fitness function in order to determine good paths through the GUI. Our fitness function looks at the amount of change in the GUI state that each event causes. Events that have a larger impact on the GUI state will be favored in future tests. We compared our two ant systems to random selection. We ran experiments on six subject applications and report on the code coverage and fault finding abilities of all three algorithms.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.70"}, {"primary_key": "4468807", "vector": [], "sparse_vector": [], "title": "Synthesising Interprocedural Bit-Precise Termination Proofs (T).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Proving program termination is key to guaranteeing absence of undesirable behaviour, such as hanging programs and even security vulnerabilities such as denial-of-service attacks. To make termination checks scale to large systems, interprocedural termination analysis seems essential, which is a largely unexplored area of research in termination analysis, where most effort has focussed on difficult single-procedure problems. We present a modular termination analysis for C programs using template-based interprocedural summarisation. Our analysis combines a context-sensitive, over-approximating forward analysis with the inference of under-approximating preconditions for termination. Bit-precise termination arguments are synthesised over lexicographic linear ranking function templates. Our experimental results show that our tool 2LS outperforms state-of-the-art alternatives, and demonstrate the clear advantage of interprocedural reasoning over monolithic analysis in terms of efficiency, while retaining comparable precision.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.10"}, {"primary_key": "4468808", "vector": [], "sparse_vector": [], "title": "Predicting Delays in Software Projects Using Networked Classification (T).", "authors": ["Morakot Choetkiertikul", "Hoa Khanh Dam", "Truyen Tran", "<PERSON><PERSON><PERSON>"], "summary": "Software projects have a high risk of cost and schedule overruns, which has been a source of concern for the software engineering community for a long time. One of the challenges in software project management is to make reliable prediction of delays in the context of constant and rapid changes inherent in software projects. This paper presents a novel approach to providing automated support for project managers and other decision makers in predicting whether a subset of software tasks (among the hundreds to thousands of ongoing tasks) in a software project have a risk of being delayed. Our approach makes use of not only features specific to individual software tasks (i.e. local data) -- as done in previous work -- but also their relationships (i.e. networked data). In addition, using collective classification, our approach can simultaneously predict the degree of delay for a group of related tasks. Our evaluation results show a significant improvement over traditional approaches which perform classification on each task independently: achieving 46% -- 97% precision (49% improved), 46% -- 97% recall (28% improved), 56% -- 75% F-measure (39% improved), and 78% -- 95% Area Under the ROC Curve (16% improved).", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.55"}, {"primary_key": "4468809", "vector": [], "sparse_vector": [], "title": "Automated Test Input Generation for Android: Are We There Yet? (E).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Like all software, mobile applications (\"apps\") must be adequately tested to gain confidence that they behave correctly. Therefore, in recent years, researchers and practitioners alike have begun to investigate ways to automate apps testing. In particular, because of Android's open source nature and its large share of the market, a great deal of research has been performed on input generation techniques for apps that run on the Android operating systems. At this point in time, there are in fact a number of such techniques in the literature, which differ in the way they generate inputs, the strategy they use to explore the behavior of the app under test, and the specific heuristics they use. To better understand the strengths and weaknesses of these existing approaches, and get general insight on ways they could be made more effective, in this paper we perform a thorough comparison of the main existing test input generation tools for Android. In our comparison, we evaluate the effectiveness of these tools, and their corresponding techniques, according to four metrics: ease of use, ability to work on multiple platforms, code coverage, and ability to detect faults. Our results provide a clear picture of the state of the art in input generation for Android apps and identify future research directions that, if suitably investigated, could lead to more effective and efficient testing tools for Android.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.89"}, {"primary_key": "4468811", "vector": [], "sparse_vector": [], "title": "Have We Seen Enough Traces? (T).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic specification mining extracts candidate specifications from logs of execution traces. Existing algorithms differ in the kinds of traces they take as input and in the kinds of candidate specification they present as output. One challenge common to all approaches relates to the faithfulness of the mining results: how can we be confident that the extracted specifications faithfully characterize the program we investigate? Since producing and analyzing traces is costly, how would we know we have seen enough traces? And, how would we know we have not wasted resources and seen too many of them?In this paper we address these important questions by presenting a novel, black box, probabilistic framework based on a notion of log completeness, and by applying it to three different well-known specification mining algorithms from the literature: k-Tails, Synoptic, and mining of scenario-based triggers and effects. Extensive evaluation over 24 models taken from 9 different sources shows the soundness, generalizability, and usefulness of the framework and its contribution to the state-of-the-art in dynamic specification mining.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.62"}, {"primary_key": "4468812", "vector": [], "sparse_vector": [], "title": "Fast and Precise Symbolic Analysis of Concurrency Bugs in Device Drivers (T).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Alastair F<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Concurrency errors, such as data races, make device drivers notoriously hard to develop and debug without automated tool support. We present Whoop, a new automated approach that statically analyzes drivers for data races. <PERSON><PERSON> is empowered by symbolic pairwise lockset analysis, a novel analysis that can soundly detect all potential races in a driver. Our analysis avoids reasoning about thread interleavings and thus scales well. Exploiting the race-freedom guarantees provided by <PERSON><PERSON>, we achieve a sound partial-order reduction that significantly accelerates Corral, an industrial-strength bug-finder for concurrent programs. Using the combination of Whoop and Corral, we analyzed 16 drivers from the Linux 4.0 kernel, achieving 1.5 -- 20× speedups over standalone Corral.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.30"}, {"primary_key": "4468813", "vector": [], "sparse_vector": [], "title": "Fuzzing the Rust Typechecker Using CLP (T).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Language fuzzing is a bug-finding technique for testing compilers and interpreters, its effectiveness depends upon the ability to automatically generate valid programs in the language under test. Despite the proven success of language fuzzing, there is a severe lack of tool support for fuzzing statically-typed languages with advanced type systems because existing fuzzing techniques cannot effectively and automatically generate well-typed programs that use sophisticated types. In this work we describe how to automatically generate well-typed programs that use sophisticated type systems by phrasing the problem of well-typed program generation in terms of Constraint Logic Programming (CLP). In addition, we describe how to specifically target the typechecker implementation for testing, unlike all existing work which ignores the typechecker. We focus on typechecker precision bugs, soundness bugs, and consistency bugs. We apply our techniques to Rust, a complex, industrial-strength language with a sophisticated type system.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.65"}, {"primary_key": "4468815", "vector": [], "sparse_vector": [], "title": "Generating Fixtures for JavaScript Unit Testing (T).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In today's web applications, JavaScript code interacts with the Document Object Model (DOM) at runtime. This runtime interaction between JavaScript and the DOM is error-prone and challenging to test. In order to unit test a JavaScript function that has read/write DOM operations, a DOM instance has to be provided as a test fixture. This DOM fixture needs to be in the exact structure expected by the function under test. Otherwise, the test case can terminate prematurely due to a null exception. Generating these fixtures is challenging due to the dynamic nature of JavaScript and the hierarchical structure of the DOM. We present an automated technique, based on dynamic symbolic execution, which generates test fixtures for unit testing JavaScript functions. Our approach is implemented in a tool called ConFix. Our empirical evaluation shows that ConFix can effectively generate tests that cover DOM-dependent paths. We also find that ConFix yields considerably higher coverage compared to an existing JavaScript input generation technique.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.26"}, {"primary_key": "4468816", "vector": [], "sparse_vector": [], "title": "Quantification of Software Changes through Probabilistic Symbolic Execution (N).", "authors": ["<PERSON>", "Corina S<PERSON>", "<PERSON><PERSON>"], "summary": "Characterizing software changes is fundamental for software maintenance. However existing techniques are imprecise leading to unnecessary maintenance efforts. We introduce a novel approach that computes a precise numeric characterization of program changes, which quantifies the likelihood of reaching target program events (e.g., assert violations or successful termination) and how that evolves with each program update, together with the percentage of inputs impacted by the change. This precise characterization leads to a natural ranking of different program changes based on their probability of execution and their impact on target events. The approach is based on model counting over the constraints collected with a symbolic execution of the program, and exploits the similarity between program versions to reduce cost and improve the quality of analysis results. We implemented our approach in the Symbolic PathFinder tool and illustrate it on several Java case studies, including the evaluation of different program repairs, mutants used in testing, or incremental analysis after a change.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.78"}, {"primary_key": "4468817", "vector": [], "sparse_vector": [], "title": "DRIVER - A Platform for Collaborative Framework Understanding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Application frameworks are a powerful technique for large-scale reuse but often very hard to learn from scratch. Although good documentation helps on reducing the learning curve, it is often found lacking, and costly, as it needs to attend different audiences with disparate learning needs. When code and documentation prove insufficient, developers turn to their network of experts. The lack of awareness about the experts, interrupting the wrong people, and experts unavailability are well known hindrances to effective collaboration. This paper presents the DRIVER platform, a collaborative learning environment for framework users to share their knowledge. It provides the documentation on a wiki, where the learning paths of the community of learners can be captured, shared, rated, and recommended, thus tapping into the collective knowledge of the community of framework users. The tool can be obtained at http://bit.ly/driverTool.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.105"}, {"primary_key": "4468818", "vector": [], "sparse_vector": [], "title": "Pseudogen: A Tool to Automatically Generate Pseudo-Code from Source Code.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Hidea<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Understanding the behavior of source code written in an unfamiliar programming language is difficult. One way to aid understanding of difficult code is to add corresponding pseudo-code, which describes in detail the workings of the code in a natural language such as English. In spite of its usefulness, most source code does not have corresponding pseudo-code because it is tedious to create. This paper demonstrates a tool Pseudogen that makes it possible to automatically generate pseudo-code from source code using statistical machine translation (SMT). Pseudogen currently supports generation of English or Japanese pseudo-code from Python source code, and the SMT framework makes it easy for users to create new generators for their preferred source code/pseudo-code pairs.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.107"}, {"primary_key": "4468820", "vector": [], "sparse_vector": [], "title": "Fixing Recurring Crash Bugs via Analyzing Q&amp;A Sites (T).", "authors": ["Qing Gao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Hong Mei"], "summary": "Recurring bugs are common in software systems, especially in client programs that depend on the same framework. Existing research uses human-written templates, and is limited to certain types of bugs. In this paper, we propose a fully automatic approach to fixing recurring crash bugs via analyzing Q&A sites. By extracting queries from crash traces and retrieving a list of Q&A pages, we analyze the pages and generate edit scripts. Then we apply these scripts to target source code and filter out the incorrect patches. The empirical results show that our approach is accurate in fixing real-world crash bugs, and can complement existing bug-fixing approaches.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.81"}, {"primary_key": "4468821", "vector": [], "sparse_vector": [], "title": "Search-Based Synthesis of Probabilistic Models for Quality-of-Service Software Engineering (T).", "authors": ["Simos G<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The formal verification of finite-state probabilistic models supports the engineering of software with strict quality-of-service (QoS) requirements. However, its use in software design is currently a tedious process of manual multiobjective optimisation. Software designers must build and verify probabilistic models for numerous alternative architectures and instantiations of the system parameters. When successful, they end up with feasible but often suboptimal models. The EvoChecker search-based software engineering approach and tool introduced in our paper employ multiobjective optimisation genetic algorithms to automate this process and considerably improve its outcome. We evaluate EvoChecker for six variants of two software systems from the domains of dynamic power management and foreign exchange trading. These systems are characterised by different types of design parameters and QoS requirements, and their design spaces comprise between 2E+14 and 7.22E+86 relevant alternative designs. Our results provide strong evidence that EvoChecker significantly outperforms the current practice and yields actionable insights for software designers.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.22"}, {"primary_key": "4468822", "vector": [], "sparse_vector": [], "title": "A Generic Framework for Concept-Based Exploration of Semi-Structured Software Engineering Data.", "authors": ["<PERSON>"], "summary": "Software engineering meta-data (SE data), such as revision control data, Github project data or test reports, is typically semi-structured, it comprises a mixture of formatted and free-text fields and is often self-describing. Semi-structured SE data cannot be queried in a SQL-like manner because of its lack of structure. Consequently, there are a variety of customized tools built to analyze specific datasets but these do not generalize. We propose to develop a generic framework for exploration and querying of semi-structured SE data. Our approach investigates the use of a formal concept lattice as a universal data structure and a tag cloud as an intuitive interface to support data exploration.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.34"}, {"primary_key": "4468823", "vector": [], "sparse_vector": [], "title": "How Verified is My Code? Falsification-Driven Verification (T).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Formal verification has advanced to the point that developers can verify the correctness of small, critical modules. Unfortunately, despite considerable efforts, determining if a \"verification\" verifies what the author intends is still difficult. Previous approaches are difficult to understand and often limited in applicability. Developers need verification coverage in terms of the software they are verifying, not model checking diagnostics. We propose a methodology to allow developers to determine (and correct) what it is that they have verified, and tools to support that methodology. Our basic approach is based on a novel variation of mutation analysis and the idea of verification driven by falsification. We use the CBMC model checker to show that this approach is applicable not only to simple data structures and sorting routines, and verification of a routine in Mozilla's JavaScript engine, but to understanding an ongoing effort to verify the Linux kernel Read-Copy-Update (RCU) mechanism.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.40"}, {"primary_key": "4468824", "vector": [], "sparse_vector": [], "title": "&quot;What Parts of Your Apps are Loved by Users?&quot; (T).", "authors": ["Xiaodong Gu", "<PERSON><PERSON>"], "summary": "Recently, <PERSON><PERSON> et al. found that one of the most important questions software developers ask is \"what parts of software are used/loved by users.\" User reviews provide an effective channel to address this question. However, most existing review summarization tools treat reviews as bags-of-words (i.e., mixed review categories) and are limited to extract software aspects and user preferences. We present a novel review summarization framework, SUR-Miner. Instead of a bags-of-words assumption, it classifies reviews into five categories and extracts aspects for sentences which include aspect evaluation using a pattern-based parser. Then, SUR-Miner visualizes the summaries using two interactive diagrams. Our evaluation on seventeen popular apps shows that SUR-Miner summarizes more accurate and clearer aspects than state-of-the-art techniques, with an F1-score of 0.81, significantly greater than that of ReviewSpotlight (0.56) and <PERSON><PERSON><PERSON>' method (0.55). Feedback from developers shows that 88% developers agreed with the usefulness of the summaries from SUR-Miner.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.57"}, {"primary_key": "4468825", "vector": [], "sparse_vector": [], "title": "Ensemble Methods for App Review Classification: An Approach for Software Evolution (N).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "App marketplaces are distribution platforms for mobile applications that serve as a communication channel between users and developers. These platforms allow users to write reviews about downloaded apps. Recent studies found that such reviews include information that is useful for software evolution. However, the manual analysis of a large amount of user reviews is a tedious and time consuming task. In this work we propose a taxonomy for classifying app reviews into categories relevant for software evolution. Additionally, we describe an experiment that investigates the performance of individual machine learning algorithms and its ensembles for automatically classifying the app reviews. We evaluated the performance of the machine learning techniques on 4550 reviews that were systematically labeled using content analysis methods. Overall, the ensembles had a better performance than the individual classifiers, with an average precision of 0.74 and 0.59 recall.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.88"}, {"primary_key": "4468826", "vector": [], "sparse_vector": [], "title": "Tracking the Software Quality of Android Applications Along Their Evolution (T).", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mobile apps are becoming complex software systems that must be developed quickly and evolve continuously to fit new user requirements and execution contexts. However, addressing these requirements may result in poor design choices, also known as antipatterns, which may incidentally degrade software quality and performance. Thus, the automatic detection and tracking of antipatterns in this apps are important activities in order to ease both maintenance and evolution. Moreover, they guide developers to refactor their applications and thus, to improve their quality. While antipatterns are well-known in object-oriented applications, their study in mobile applications is still in its infancy. In this paper, we analyze the evolution of mobile apps quality on 3, 568 versions of 106 popular Android applications downloaded from the Google Play Store. For this purpose, we use a tooled approach, called PAPRIKA, to identify 3 object-oriented and 4 Android-specific antipatterns from binaries of mobile apps, and to analyze their quality along evolutions.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.46"}, {"primary_key": "4468829", "vector": [], "sparse_vector": [], "title": "Variable Feature Usage Patterns in PHP (T).", "authors": ["<PERSON>"], "summary": "PHP allows the names of variables, classes, functions, methods, and properties to be given dynamically, as expressions that, when evaluated, return an identifier as a string. While this provides greater flexibility for programmers, it also makes PHP programs harder to precisely analyze and understand. In this paper we present a number of patterns designed to recognize idiomatic uses of these features that can be statically resolved to a precise set of possible names. We then evaluate these patterns across a corpus of 20 open-source systems totaling more than 3.7 million lines of PHP, showing how often these patterns occur in actual PHP code, demonstrating their effectiveness at statically determining the names that can be used at runtime, and exploring anti-patterns that indicate when the identifier computation is truly dynamic.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.72"}, {"primary_key": "4468830", "vector": [], "sparse_vector": [], "title": "Mutation-Based Fault Localization for Real-World Multilingual Programs (T).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Bongsuk Ko", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Programmers maintain and evolve their software in a variety of programming languages to take advantage of various control/data abstractions and legacy libraries. The programming language ecosystem has diversified over the last few decades, and non-trivial programs are likely to be written in more than a single language. Unfortunately, language interfaces such as Java Native Interface and Python/C are difficult to use correctly and the scope of fault localization goes beyond language boundaries, which makes debugging multilingual bugs challenging. To overcome the aforementioned limitations, we propose a mutation-based fault localization technique for real-world multilingual programs. To improve the accuracy of locating multilingual bugs, we have developed and applied new mutation operators as well as conventional mutation operators. The results of the empirical evaluation for six non-trivial real-world multilingual bugs are promising in that the proposed technique identifies the buggy statements as the most suspicious statements for all six bugs.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.14"}, {"primary_key": "4468832", "vector": [], "sparse_vector": [], "title": "Lazy-CSeq: A Context-Bounded Model Checking Tool for Multi-threaded C-Programs.", "authors": ["<PERSON>", "Truc <PERSON>", "<PERSON><PERSON>", "Salvatore <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Lazy-CSeq is a context-bounded verification tool for sequentially consistent C programs using POSIX threads. It first translates a multi-threaded C program into a bounded nondeterministic sequential C program that preserves bounded reachability for all round-robin schedules up to a given number of rounds. It then reuses existing high-performance bounded model checkers as sequential verification backends. Lazy-CSeq handles the full C language and the main parts of the POSIX thread API, such as dynamic thread creation and deletion, and synchronization via thread join, locks, and condition variables. It supports assertion checking and deadlock detection, and returns counterexamples in case of errors. Lazy-CSeq outperforms other concurrency verification tools and has won the concurrency category of the last two SV-COMP verification competitions.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.108"}, {"primary_key": "4468833", "vector": [], "sparse_vector": [], "title": "Experiences from Designing and Validating a Software Modernization Transformation (E).", "authors": ["Alexandru F. <PERSON>", "<PERSON>", "Aleksandar S. Dimovski", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software modernization often involves complex code transformations that convert legacy code to new architectures or platforms, while preserving the semantics of the original programs. We present the lessons learnt from an industrial software modernization project of considerable size. This includes collecting requirements for a code-to-model transformation, designing and implementing the transformation algorithm, and then validating correctness of this transformation for the code-base at hand. Our transformation is implemented in the TXL rewriting language and assumes specifically structured C++ code as input, which it translates to a declarative configuration model. The correctness criterion for the transformation is that the produced model admits the same configurations as the input code. The transformation converts C++ functions specifying around a thousand configuration parameters. We verify the correctness for each run individually, using translation validation and symbolic execution. The technique is formally specified and is applicable automatically for most of the code-base.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.84"}, {"primary_key": "4468835", "vector": [], "sparse_vector": [], "title": "Repairing Programs with Semantic Code Search (T).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Automated program repair can potentially reduce debugging costs and improve software quality but recent studies have drawn attention to shortcomings in the quality of automatically generated repairs. We propose a new kind of repair that uses the large body of existing open-source code to find potential fixes. The key challenges lie in efficiently finding code semantically similar (but not identical) to defective code and then appropriately integrating that code into a buggy program. We present SearchRepair, a repair technique that addresses these challenges by(1) encoding a large database of human-written code fragments as SMT constraints on input-output behavior, (2) localizing a given defect to likely buggy program fragments and deriving the desired input-output behavior for code to replace those fragments, (3) using state-of-the-art constraint solvers to search the database for fragments that satisfy that desired behavior and replacing the likely buggy code with these potential patches, and (4) validating that the patches repair the bug against program testsuites. We find that SearchRepair repairs 150 (19%) of 778 benchmark C defects written by novice students, 20 of which are not repaired by GenProg, TrpAutoRepair, and AE. We compare the quality of the patches generated by the four techniques by measuring how many independent, not-used-during-repairtests they pass, and find that SearchRepair-repaired programs pass 97.3% ofthe tests, on average, whereas GenProg-, TrpAutoRepair-, and AE-repaired programs pass 68.7%, 72.1%, and 64.2% of the tests, respectively. We concludethat SearchRepair produces higher-quality repairs than GenProg, TrpAutoRepair, and AE, and repairs some defects those tools cannot.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.60"}, {"primary_key": "4468836", "vector": [], "sparse_vector": [], "title": "Detecting Broken Pointcuts Using Structural Commonality and Degree of Interest (N).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Pointcut fragility is a well-documented problem in Aspect-Oriented Programming, changes to the base-code can lead to join points incorrectly falling in or out of the scope of pointcuts. Deciding which pointcuts have broken due to base-code changes is a daunting venture, especially in large and complex systems. We present an automated approach that recommends pointcuts that are likely to require modification due to a particular base-code change, as well as ones that do not. Our hypothesis is that join points selected by a pointcut exhibit common structural characteristics. Patterns describing such commonality are used to recommend pointcuts that have potentially broken to the developer. The approach is implemented as an extension to the popular Mylyn Eclipse IDE plug-in, which maintains focused contexts of entities relevant to the task at hand using a Degree of Interest (DOI) model.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.80"}, {"primary_key": "4468837", "vector": [], "sparse_vector": [], "title": "Practically Tunable Static Analysis Framework for Large-Scale JavaScript Applications (T).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sukyoung Ryu"], "summary": "We present a novel approach to analyze large-scale JavaScript applications statically by tuning the analysis scalability possibly giving up its soundness. For a given sound static baseline analysis of JavaScript programs, our framework allows users to define a sound approximation of selected executions that they are interested in analyzing, and it derives a tuned static analysis that can analyze the selected executions practically. The selected executions serve as parameters of the framework by taking trade-off between the scalability and the soundness of derived analyses. We formally describe our framework in abstract interpretation, and implement two instances of the framework. We evaluate them by analyzing large-scale real-world JavaScript applications, and the evaluation results show that the framework indeed empowers users to experiment with different levels of scalability and soundness. Our implementation provides an extra level of scalability by deriving sparse versions of derived analyses, and the implementation is publicly available.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.28"}, {"primary_key": "4468838", "vector": [], "sparse_vector": [], "title": "Scaling Size and Parameter Spaces in Variability-Aware Software Performance Models (T).", "authors": ["<PERSON>", "<PERSON>", "Mirco Tribastone", "<PERSON><PERSON>"], "summary": "In software performance engineering, what-if scenarios, architecture optimization, capacity planning, run-time adaptation, and uncertainty management of realistic models typically require the evaluation of many instances. Effective analysis is however hindered by two orthogonal sources of complexity. The first is the infamous problem of state space explosion -- the analysis of a single model becomes intractable with its size. The second is due to massive parameter spaces to be explored, but such that computations cannot be reused across model instances. In this paper, we efficiently analyze many queuing models with the distinctive feature of more accurately capturing variability and uncertainty of execution rates by incorporating general (i.e., non-exponential) distributions. Applying product-line engineering methods, we consider a family of models generated by a core that evolves into concrete instances by applying simple delta operations affecting both the topology and the model's parameters. State explosion is tackled by turning to a scalable approximation based on ordinary differential equations. The entire model space is analyzed in a family-based fashion, i.e., at once using an efficient symbolic solution of a super-model that subsumes every concrete instance. Extensive numerical tests show that this is orders of magnitude faster than a naive instance-by-instance analysis.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.16"}, {"primary_key": "4468842", "vector": [], "sparse_vector": [], "title": "Combining Deep Learning with Information Retrieval to Localize Buggy Files for Bug Reports (N).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Bug localization refers to the automated process of locating the potential buggy files for a given bug report. To help developers focus their attention to those files is crucial. Several existing automated approaches for bug localization from a bug report face a key challenge, called lexical mismatch, in which the terms used in bug reports to describe a bug are different from the terms and code tokens used in source files. This paper presents a novel approach that uses deep neural network (DNN) in combination with rVSM, an information retrieval (IR) technique. rVSM collects the feature on the textual similarity between bug reports and source files. DNN is used to learn to relate the terms in bug reports to potentially different code tokens and terms in source files and documentation if they appear frequently enough in the pairs of reports and buggy files. Our empirical evaluation on real-world projects shows that DNN and IR complement well to each other to achieve higher bug localization accuracy than individual models. Importantly, our new model, HyLoc, with a combination of the features built from DNN, rVSM, and project's bug-fixing history, achieves higher accuracy than the state-of-the-art IR and machine learning techniques. In half of the cases, it is correct with just a single suggested file. Two out of three cases, a correct buggy file is in the list of three suggested files.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.73"}, {"primary_key": "4468843", "vector": [], "sparse_vector": [], "title": "Synergizing Specification Miners through Model Fissions and Fusions (T).", "authors": ["Tien-Duy B. Le", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software systems are often developed and released without formal specifications. For those systems that are formally specified, developers have to continuously maintain and update the specifications or have them fall out of date. To deal with the absence of formal specifications, researchers have proposed techniques to infer the missing specifications of an implementation in a variety of forms, such as finite state automaton (FSA). Despite the progress in this area, the efficacy of the proposed specification miners needs to improve if these miners are to be adopted. We propose SpecForge, a new specification mining approach that synergizes many existing specification miners. SpecForge decomposes FSAs that are inferred by existing miners into simple constraints, through a process we refer to as model fission. It then filters the outlier constraints and fuses the constraints back together into a single FSA (i.e., model fusion). We have evaluated SpecForge on execution traces of 10 programs, which includes 5 programs from DaCapo benchmark, to infer behavioral models of 13 library classes. Our results show that SpecForge achieves an average precision, recall and F-measure of 90.57%, 54.58%, and 64.21% respectively. SpecForge outperforms the best performing baseline by 13.75% in terms of F-measure.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.83"}, {"primary_key": "4468844", "vector": [], "sparse_vector": [], "title": "Investigating Program Behavior Using the Texada LTL Specifications Miner.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Temporal specifications, relating program events through time, are useful for tasks ranging from bug detection to program comprehension. Unfortunately, such specifications are often lacking from system descriptions, leading researchers to investigate methods for inferring these specifications from code, execution traces, code comments, and other artifacts. This paper describes Texada, a tool to dynamically mine temporal specifications in LTL from traces of program activity. We review Texada's key features and demonstrate how it can be used to investigate program behavior through two scenarios: validating an implementation that solves the dining philosophers problem and supporting comprehension of a stack implementation. We also detail Texada's other, more advanced, usage options. Texada is an open source tool: https://bitbucket.org/bestchai/texada.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.94"}, {"primary_key": "4468845", "vector": [], "sparse_vector": [], "title": "General LTL Specification Mining (T).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Temporal properties are useful for describing and reasoning about software behavior, but developers rarely write down temporal specifications of their systems. Prior work on inferring specifications developed tools to extract likely program specifications that fit particular kinds of tool-specific templates. This paper introduces Texada, a new temporal specification mining tool for extracting specifications in linear temporal logic (LTL) of arbitrary length and complexity. Texada takes a user-defined LTL property type template and a log of traces as input and outputs a set of instantiations of the property type (i.e., LTL formulas) that are true on the traces in the log. Texada also supports mining of almost invariants: properties with imperfect confidence. We formally describe Texada's algorithms and evaluate the tool's performance and utility.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.71"}, {"primary_key": "4468846", "vector": [], "sparse_vector": [], "title": "Access-Path Abstraction: Scaling Field-Sensitive Data-Flow Analysis with Unbounded Access Paths (T).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Precise data-flow analyses frequently model field accesses through access paths with varying length. While using longer access paths increases precision, their size must be bounded to assure termination, and should anyway be small to enable a scalable analysis. We present Access-Path Abstraction, which for the first time combines efficiency with maximal precision. At control-flow merge points Access-Path Abstraction represents all those access paths that are rooted at the same base variable through this base variable only. The full access paths are reconstructed on demand where required. This makes it unnecessary to bound access paths to a fixed maximal length. Experiments with Stanford SecuriBench and the Java Class Library compare our open-source implementation against a field-based approach and against a field-sensitive approach that uses bounded access paths. The results show that the proposed approach scales as well as a field-based approach, whereas the approach using bounded access paths runs out of memory.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.9"}, {"primary_key": "4468847", "vector": [], "sparse_vector": [], "title": "Semantic Slicing of Software Version Histories (T).", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Software developers often need to transfer func-tionality, e.g., a set of commits implementing a new feature or a bug fix, from one branch of a configuration management system to another. That can be a challenging task as the existing configuration management tools lack support for matching high-level semantic functionality with low-level version histories. The developer thus has to either manually identify the exact set of semantically-related commits implementing the functionality of interest or sequentially port a specific subset of the change history, \"inheriting\" additional, unwanted functionality. In this paper, we tackle this problem by providing automated support for identifying the set of semantically-related commits implementing a particular functionality, which is defined by a set of tests. We refer to our approach, CSLICER, as semantic slicing of version histories. We formally define the semantic slicing problem, provide an algorithm for identifying a set of commits that constitute a slice, and instantiate it in a specific implementation for Java projects managed in Git. We evaluate the correctness and effectiveness of our approach on a set of open-source software repositories. We show that it allows to identify subsets of change histories that maintain the functionality of interest but are substantially smaller than the original ones.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.47"}, {"primary_key": "4468848", "vector": [], "sparse_vector": [], "title": "Refactorings for Android Asynchronous Programming.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Running compute-intensive or blocking I/O operationsin the UI event thread of smartphone apps can severelydegrade responsiveness. Despite the fact that Android provides several async constructs that developers can use, developers can still miss opportunities to encapsulate long-running operations in async constructs. On the other hand, they can use the inappropriate async constructs, which result in memory leaks, lost results, and wasted energy. Fortunately, refactoring tools can eliminate these problems by retrofitting asynchrony to sequential code and transforming async code to use the appropriate constructs. This demo presents two refactoring tools for Android apps: (i) ASYNCHRONIZER, a refactoring tool that enables developers to extract long-running operations into Android AsyncTask. (ii) ASYNCDROID, a refactoring tool which enables developers to transform existing improperly-used AsyncTask into Android IntentService.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.100"}, {"primary_key": "4468849", "vector": [], "sparse_vector": [], "title": "TCA: An Efficient Two-Mode Meta-Heuristic Algorithm for Combinatorial Test Generation (T).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "S<PERSON>wei <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Covering arrays (CAs) are often used as test suites for combinatorial interaction testing to discover interaction faults of real-world systems. Most real-world systems involve constraints, so improving algorithms for covering array generation (CAG) with constraints is beneficial. Two popular methods for constrained CAG are greedy construction and meta-heuristic search. Recently, a meta-heuristic framework called two-mode local search has shown great success in solving classic NPhard problems. We are interested whether this method is also powerful in solving the constrained CAG problem. This work proposes a two-mode meta-heuristic framework for constrained CAG efficiently and presents a new meta-heuristic algorithm called TCA. Experiments show that TCA significantly outperforms state-of-the-art solvers on 3-way constrained CAG. Further experiments demonstrate that TCA also performs much better than its competitors on 2-way constrained CAG.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.61"}, {"primary_key": "4468850", "vector": [], "sparse_vector": [], "title": "JaConTeBe: A Benchmark Suite of Real-World Java Concurrency Bugs (T).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Researchers have proposed various approaches to detect concurrency bugs and improve multi-threaded programs, but performing evaluations of the effectiveness of these approaches still remains a substantial challenge. We survey the existing evaluations and find out that they often use code or bugs not representative of real world. To improve representativeness, we have prepared JaConTeBe, a benchmark suite of 47 confirmed concurrency bugs from 8 popular open-source projects, supplemented with test cases for reproducing buggy behaviors. Running three approaches on JaConTeBe shows that our benchmark suite confirms some limitations of the three approaches. We submitted JaConTeBe to the SIR repository (a software-artifact repository for rigorous controlled experiments), and it was included as a part of SIR.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.87"}, {"primary_key": "4468851", "vector": [], "sparse_vector": [], "title": "Study and Refactoring of Android Asynchronous Programming (T).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "To avoid unresponsiveness, a core part of mobile development is asynchronous programming. Android providesseveral async constructs that developers can use. However, developers can still use the inappropriate async constructs, which result in memory leaks, lost results, and wasted energy. Fortunately, refactoring tools can eliminate these problems by transforming async code to use the appropriate constructs. In this paper we conducted a formative study on a corpusof 611 widely-used Android apps to map the asynchronouslandscape of Android apps, understand how developers retrofit asynchrony, and learn about barriers encountered by developers. Based on this study, we designed, implemented, and evaluated ASYNCDROID, a refactoring tool which enables Android developers to transform existing improperly-used async constructs into correct constructs. Our empirical evaluation shows that ASYNCDROID is applicable, accurate, and saves developers effort. We submitted 45 refactoring patches, and developers consider that the refactorings are useful.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.50"}, {"primary_key": "4468852", "vector": [], "sparse_vector": [], "title": "Interpolation Guided Compositional Verification (T).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Model checking suffers from the state space explosion problem. Compositional verification techniques such as assume-guarantee reasoning (AGR) have been proposed to alleviate the problem. However, there are at least three challenges in applying AGR. Firstly, given a system M1 ? M2, how do we automatically construct and refine (in the presence of spurious counterexamples) an assumption A2, which must be an abstraction of M2? Previous approaches suggest to incrementally learn and modify the assumption through multiple invocations of a model checker, which could be often time consuming. Secondly, how do we keep the state space small when checking M1 ? A2 = f if multiple refinements of A2 are necessary? Lastly, in the presence of multiple parallel components, how do we partition the components? In this work, we propose interpolation-guided compositional verification. The idea is to tackle three challenges by using interpolations to generate and refine the abstraction of M2, to abstract M1 at the same time (so that the state space is reduced even if A2 is refined all the way to M2), and to find good partitions. Experimental results show that the proposed approach outperforms existing approaches consistently.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.33"}, {"primary_key": "4468853", "vector": [], "sparse_vector": [], "title": "CodeHow: Effective Code Search Based on API Understanding and Extended Boolean Model (E).", "authors": ["Fei Lv", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over the years of software development, a vast amount of source code has been accumulated. Many code search tools were proposed to help programmers reuse previously-written code by performing free-text queries over a large-scale codebase. Our experience shows that the accuracy of these code search tools are often unsatisfactory. One major reason is that existing tools lack of query understanding ability. In this paper, we propose CodeHow, a code search technique that can recognize potential APIs a user query refers to. Having understood the potentially relevant APIs, CodeHow expands the query with the APIs and performs code retrieval by applying the Extended Boolean model, which considers the impact of both text similarity and potential APIs on code search. We deploy the backend of CodeHow as a Microsoft Azure service and implement the front-end as a Visual Studio extension. We evaluate CodeHow on a large-scale codebase consisting of 26K C# projects downloaded from GitHub. The experimental results show that when the top 1 results are inspected, CodeHow achieves a precision score of 0.794 (i.e., 79.4% of the first returned results are relevant code snippets). The results also show that CodeHow outperforms conventional code search tools. Furthermore, we perform a controlled experiment and a survey of Microsoft developers. The results confirm the usefulness and effectiveness of CodeHow in programming practices.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.42"}, {"primary_key": "4468854", "vector": [], "sparse_vector": [], "title": "GRT: Program-Analysis-Guided Random Testing (T).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose Guided Random Testing (GRT), which uses static and dynamic analysis to include information on program types, data, and dependencies in various stages of automated test generation. Static analysis extracts knowledge from the system under test. Test coverage is further improved through state fuzzing and continuous coverage analysis. We evaluated GRT on 32 real-world projects and found that GRT outperforms major peer techniques in terms of code coverage (by 13 %) and mutation score (by 9 %). On the four studied benchmarks of Defects4J, which contain 224 real faults, GRT also shows better fault detection capability than peer techniques, finding 147 faults (66 %). Furthermore, in an in-depth evaluation on the latest versions of ten popular real-world projects, GRT successfully detects over 20 unknown defects that were confirmed by developers.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.49"}, {"primary_key": "4468855", "vector": [], "sparse_vector": [], "title": "GRT: An Automated Test Generator Using Orchestrated Program Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "While being highly automated and easy to use, existing techniques of random testing suffer from low code coverage and defect detection ability for practical software applications. Most tools use a pure black-box approach, which does not use knowledge specific to the software under test. Mining and leveraging the information of the software under test can be promising to guide random testing to overcome such limitations. Guided Random Testing (GRT) implements this idea. GRT performs static analysis on software under test to extract relevant knowledge and further combines the information extracted at run-time to guide the whole test generation procedure. GRT is highly configurable, with each of its six program analysis components implemented as a pluggable module whose parameters can be adjusted. Besides generating test cases, GRT also automatically creates a test coverage report. We show our experience in GRT tool development and demonstrate its practical usage using two concrete application scenarios.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.102"}, {"primary_key": "4468856", "vector": [], "sparse_vector": [], "title": "CodeExchange: Supporting Reformulation of Internet-Scale Code Queries in Context (T).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Programming today regularly involves searching for source code online, whether through a general search engine such as Google or a specialized code search engine such as SearchCode, Ohloh, or GitHub. Searching typically is an iterative process, with developers adjusting the keywords they use based on the results of the previous query. However, searching in this manner is not ideal, because just using keywords places limits on what developers can express as well as the overall interaction that is required. Based on the observation that the results from one query create a con-text in which a next is formulated, we present CodeExchange, a new code search engine that we developed to explicitly leverage this context to support fluid, expressive reformulation of queries. We motivate the need for CodeExchange, highlight its key design decisions and overall architecture, and evaluate its use in both a field deployment and a laboratory study.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.51"}, {"primary_key": "4468857", "vector": [], "sparse_vector": [], "title": "Automating the Extraction of Model-Based Software Product Lines from Model Variants (T).", "authors": ["<PERSON><PERSON><PERSON>", "Tewfik <PERSON>", "Tegawendé F. Bissyandé", "<PERSON>", "<PERSON>"], "summary": "We address the problem of automating 1) the analysis of existing similar model variants and 2) migrating them into a software product line. Our approach, named MoVaPL, considers the identification of variability and commonality in model variants, as well as the extraction of a CVL-compliant Model-based Software Product Line (MSPL) from the features identified on these variants. MoVaPL builds on a generic representation of models making it suitable to any MOF-based models. We apply our approach on variants of the open source ArgoUML UML modeling tool as well as on variants of an In-flight Entertainment System. Evaluation with these large and complex case studies contributed to show how our feature identification with structural constraints discovery and the MSPL generation process are implemented to make the approach valid (i.e., the extracted software product line can be used to regenerate all variants considered) and sound (i.e., derived variants which did not exist are at least structurally valid).", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.44"}, {"primary_key": "4468859", "vector": [], "sparse_vector": [], "title": "Model Checking Task Parallel Programs Using Gradual Permissions (N).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Habanero is a task parallel programming model that provides correctness guarantees to the programmer. Even so, programs may contain data races that lead to non-determinism, which complicates debugging and verification. This paper presents a sound algorithm based on permission regions to prove data race and deadlock freedom in Habanero programs. Permission regions are user annotations to indicate the use of shared variables over spans of code. The verification algorithm restricts scheduling to permission region boundaries and isolation to reduce verification cost. The effectiveness of the algorithm is shown in benchmarks with an implementation in the Java Pathfinder (JPF) model checker. The implementation uses a verification specific library for Habanero that is tested using JPF for correctness. The results show significant reductions in cost, where cost is controlled with the size of the permission regions, at the risk of rejecting programs that are actually free of any data race or deadlock.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.75"}, {"primary_key": "4468860", "vector": [], "sparse_vector": [], "title": "The iMPAcT Tool: Testing UI Patterns on Mobile Applications.", "authors": ["Ines Coimbra Morgado", "Ana C. R. Paiva"], "summary": "This paper presents the iMPAcT tool that tests recurring behaviour, i.e., UI Patterns, on mobile applications. This tool is implemented in Java and makes use of Android's APIs UI Automator and UiAutomation. The tool automatically explores a mobile application in order to automatically identify and test UI Patterns. Each UI Pattern has a test strategy, Test Patterns, associated, which is applied when an UI Pattern is found. The approach works on top of a catalogue of UI Patterns, which determines which UI Patterns are to be tested, and what should their correct behaviour be, and may be used for any application.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.96"}, {"primary_key": "4468862", "vector": [], "sparse_vector": [], "title": "Development History Granularity Transformations (N).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Development histories can simplify some software engineering tasks, butdifferent tasks require different history granularities. For example, a history that includes every edit that resulted in compiling code is needed when searching for the cause of a regression, whereas a history that contains only changes relevant to a feature is needed for understanding the evolution of the feature. Unfortunately, today, both manual and automated history generation result in a single-granularity history. This paper introduces the concept of multi-grained development history views and the architecture of Codebase Manipulation, a tool that automatically records a fine-grained history and manages its granularity by applying granularity transformations.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.53"}, {"primary_key": "4468864", "vector": [], "sparse_vector": [], "title": "CLAMI: Defect Prediction on Unlabeled Datasets (T).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Defect prediction on new projects or projects with limited historical data is an interesting problem in software engineering. This is largely because it is difficult to collect defect information to label a dataset for training a prediction model. Cross-project defect prediction (CPDP) has tried to address this problem by reusing prediction models built by other projects that have enough historical data. However, CPDP does not always build a strong prediction model because of the different distributions among datasets. Approaches for defect prediction on unlabeled datasets have also tried to address the problem by adopting unsupervised learning but it has one major limitation, the necessity for manual effort. In this study, we propose novel approaches, CLA and CLAMI, that show the potential for defect prediction on unlabeled datasets in an automated manner without need for manual effort. The key idea of the CLA and CLAMI approaches is to label an unlabeled dataset by using the magnitude of metric values. In our empirical study on seven open-source projects, the CLAMI approach led to the promising prediction performances, 0.636 and 0.723 in average f-measure and AUC, that are comparable to those of defect prediction based on supervised learning.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.56"}, {"primary_key": "4468865", "vector": [], "sparse_vector": [], "title": "Clone Merge - An Eclipse Plugin to Abstract Near-Clone C++ Methods.", "authors": ["<PERSON>"], "summary": "Software clones are prevalent. In the work of <PERSON><PERSON><PERSON> et al. [2], they observe that 6.4% and 7.5% of the source code in different versions of a large, mature code base are clones. The work of <PERSON> et al. [1] reports even higher numbers, sometimes exceeding 25%. We consider the prevalence of such near miss clones to be strong indicators that copy-paste-modify is a wide-spread development methodology. Even though clones are prevalent, they are a significant development headache. Specially, if bugs arise in one of the clones, they need to be fixed in all of the clones. This problem is acknowledged in the work of <PERSON><PERSON><PERSON> et al. [4] who say in their work that \"cloning can be a substantial problem during development and maintenance\", since \"inconsistent clones constitute a major source of faults\". A similar concern is raised in practitioner literature [3] suggesting that clones should be removed in some form or the other. We present a tool that can be installed as a plugin to Eclipse CDT, the development environment for C/C++. The research prototype comes with a refactoring option called \"Copy Paste merge\" refactoring, which is available as a menu option in the modified version of the Eclipse CDT.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.103"}, {"primary_key": "4468866", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> and <PERSON><PERSON>med (T).", "authors": ["<PERSON>", "<PERSON>"], "summary": "Modern software development relies on code reuse, which software engineers typically realise through handwritten abstractions, such as functions, methods, or classes. However, such abstractions can be challenging to develop and maintain. One alternative form of re-use is copy-paste-modify, a methodology in which developers explicitly duplicate source code to adapt the duplicate for a new purpose. We observe that copy-paste-modify can be substantially faster to use than manual abstraction, and past research strongly suggests that it is a popular technique among software developers. We therefore propose that software engineers should forego hand-written abstractions in favour of copying and pasting. However, empirical evidence also shows that copy-paste-modify complicates software maintenance and increases the frequency of bugs. To address this concern, we propose a software tool that merges together similar pieces of code and automatically creates suitable abstractions. This allows software developers to get the best of both worlds: custom abstraction together with easy re-use. To demonstrate the feasibility of our approach, we have implemented and evaluated a prototype merging tool for C++ on a number of near-miss clones (clones with some modifications) in popular Open Source packages. We found that maintainers find our algorithmically created abstractions to be largely preferable to existing duplicated code.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.39"}, {"primary_key": "4468867", "vector": [], "sparse_vector": [], "title": "Evolutionary Robustness Testing of Data Processing Systems Using Models and Data Mutation (T).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "System level testing of industrial data processing software poses several challenges. Input data can be very large, even in the order of gigabytes, and with complex constraints that define when an input is valid. Generating the right input data to stress the system for robustness properties (e.g. to test how faulty data is handled) is hence very complex, tedious and error prone when done manually. Unfortunately, this is the current practice in industry. In previous work, we defined a methodology to model the structure and the constraints of input data by using UML class diagrams and OCL constraints. Tests were automatically derived to cover predefined fault types in a fault model. In this paper, to obtain more effective system level test cases, we developed a novel search-based test generation tool. Experiments on a real-world, large industrial data processing system show that our automated approach can not only achieve better code coverage, but also accomplishes this using significantly smaller test suites.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.13"}, {"primary_key": "4468869", "vector": [], "sparse_vector": [], "title": "Reverse Engineering Mobile Application User Interfaces with REMAUI (T).", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "When developing the user interface code of a mobile application, in practice a big gap exists between the digital conceptual drawings of graphic artists and working user interface code. Currently, programmers bridge this gap manually, by reimplementing the conceptual drawings in code, which is cumbersome and expensive. To bridge this gap, we introduce the first technique to automatically Reverse Engineer Mobile Application User Interfaces (REMAUI). On a given input bitmap REMAUI identifies user interface elements such as images, texts, containers, and lists, via computer vision and optical character recognition (OCR) techniques. In our experiments on 488 screenshots of over 100 popular third-party Android and iOS applications, REMAUI-generated user interfaces were similar to the originals, both pixel-by-pixel and in terms of their runtime user interface hierarchies. REMAUI's average overall runtime on a standard desktop computer was 9 seconds.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.32"}, {"primary_key": "4468870", "vector": [], "sparse_vector": [], "title": "Divide-and-Conquer Approach for Multi-phase Statistical Migration for Source Code (T).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Prior research shows that directly applying phrase-based SMT on lexical tokens to migrate Java to C# produces much semantically incorrect code. A key limitation is the use of sequences in phrase-based SMT to model and translate source code with well-formed structures. We propose mppSMT, a divide-and-conquer technique to address that with novel training and migration algorithms using phrase-based SMT in three phases. First, mppSMT treats a program as a sequence of syntactic units and maps/translates such sequences in two languages to one another. Second, with a syntax-directed fashion, it deals with the tokens within syntactic units by encoding them with semantic symbols to represent their data and token types. This encoding via semantic symbols helps better migration of API usages. Third, the lexical tokens corresponding to each sememe are mapped or migrated. The resulting sequences of tokens are merged together to form the final migrated code. Such divide-and-conquer and syntax-direction strategies enable phrase-based SMT to adapt well to syntactical structures in source code, thus, improving migration accuracy. Our empirical evaluation on several real-world systems shows that 84.8 -- 97.9% and 70 -- 83% of the migrated methods are syntactically and semantically correct, respectively. 26.3 -- 51.2% of total migrated methods are exactly matched to the human-written C# code in the oracle. Compared to Java2CSharp, a rule-based migration tool, it achieves higher semantic accuracy from 6.6 -- 57.7% relatively. Importantly, it does not require manual labeling for training data or manual definition of rules.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.74"}, {"primary_key": "4468871", "vector": [], "sparse_vector": [], "title": "Recommending API Usages for Mobile Apps with Hidden Markov Model.", "authors": ["<PERSON> Ng<PERSON>en", "Hung V<PERSON> Pham", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile apps often rely heavily on standard API frameworks and libraries. However, learning to use those APIs is often challenging due to the fast-changing nature of API frameworks and the insufficiency of documentation and code examples. This paper introduces DroidAssist, a recommendation tool for API usages of Android mobile apps. The core of DroidAssist is HAPI, a statistical, generative model of API usages based on Hidden Markov Model. With HAPIs trained from existing mobile apps, DroidAssist could perform code completion for method calls. It can also check existing call sequences to detect and repair suspicious (i.e. unpopular) API usages.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.109"}, {"primary_key": "4468872", "vector": [], "sparse_vector": [], "title": "Learning to Generate Pseudo-Code from Source Code Using Statistical Machine Translation (T).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Hidea<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Pseudo-code written in natural language can aid the comprehension of source code in unfamiliar programming languages. However, the great majority of source code has no corresponding pseudo-code, because pseudo-code is redundant and laborious to create. If pseudo-code could be generated automatically and instantly from given source code, we could allow for on-demand production of pseudo-code without human effort. In this paper, we propose a method to automatically generate pseudo-code from source code, specifically adopting the statistical machine translation (SMT) framework. SMT, which was originally designed to translate between two natural languages, allows us to automatically learn the relationship between source code/pseudo-code pairs, making it possible to create a pseudo-code generator with less human effort. In experiments, we generated English or Japanese pseudo-code from Python statements using SMT, and find that the generated pseudo-code is largely accurate, and aids code understanding.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.36"}, {"primary_key": "4468873", "vector": [], "sparse_vector": [], "title": "Understanding, Refactoring, and Fixing Concurrency in C#.", "authors": ["<PERSON><PERSON>"], "summary": "Industry leaders provide concurrent libraries because asynchronous & parallel programming are increasingly in demand: responsiveness, scalability, and high-throughput are key elements of all modern applications. However, we know little about how developers use these concurrent libraries in practice and the developer's toolbox for concurrency is very limited. We present the first study that analyzes the usage of concurrent libraries in large codebases, such as 2258 open-source C# apps comprising 54M SLOC and 1378 open-source Windows Phone apps comprising 12M SLOC. Using this data, we find important problems about use and misuse of concurrency. Inspired by our findings, we designed, evaluated, and implemented several static analyses and refactoring tools.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.82"}, {"primary_key": "4468874", "vector": [], "sparse_vector": [], "title": "Static Analysis of JavaScript Web Applications in the Wild via Practical DOM Modeling (T).", "authors": ["Changhee Park", "Sooncheol Won", "<PERSON><PERSON><PERSON>", "Sukyoung Ryu"], "summary": "We present SAFEWapp, an open-source static analysis framework for JavaScript web applications. It provides a faithful (partial) model of web application execution environments of various browsers, based on empirical data from the main web pages of the 9,465 most popular websites. A main feature of SAFEWapp is the configurability of DOM tree abstraction levels to allow users to adjust a trade-off between analysis performance and precision depending on their applications. We evaluate SAFEWapp on the 5 most popular JavaScript libraries and the main web pages of the 10 most popular websites in terms of analysis performance, precision, and modeling coverage. Additionally, as an application of SAFEWapp, we build a bug detector for JavaScript web applications that uses static analysis results from SAFEWapp. Our bug detector found previously undiscovered bugs including ones from wikipedia.org and amazon.com.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.27"}, {"primary_key": "4468875", "vector": [], "sparse_vector": [], "title": "SiPL - A Delta-Based Modeling Framework for Software Product Line Engineering.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Model-based development has become a widely-used approach to implement software, e.g. for embedded systems. Models replace source code as primary executable artifacts in these cases. Software product line technologies for these domains must be able to generate models as instances of an SPL. This need is addressed among others by an implementation technology for SPLs known as delta modeling. Current approaches to delta modeling require deltas to be written manually using delta languages, and they offer only very limited support for creating and testing a network of deltas. This paper presents a new approach to delta modeling and a supporting tool suite: the abstract notion of a delta is refined to be a consistency-preserving edit script which is generated by comparing two models. The rich structure of edit scripts allows us to detect conflicts and further relations between deltas statically and to implement restructurings in delta sets such as the merging of two deltas. We illustrate the tooling using a case study.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.106"}, {"primary_key": "4468876", "vector": [], "sparse_vector": [], "title": "Model-Driven Allocation Engineering (T).", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Cyber-physical systems (CPSs) provide sophisticated functionality and are controlled by networked electronic control units (ECUs). Nowadays, software engineers use component-based development approaches to develop their software. Moreover, software components have to be allocated to an ECU to be executed. Engineers have to cope with topology-, software-, and timing-dependencies and memory-, scheduling-, and routing-constraints. Currently, engineers use linear programs to specify allocation constraints and to derive a feasible allocation automatically. However, encoding the allocation problem as a linear program is a complex and error-prone task. This paper contributes a model-driven, OCL-based allocation engineering approach for reducing the engineering effort and to avoid failures. We validate our approach with an automotive case study modeled with MechatronicUML. Our validation shows that we can specify allocation constraints with less engineering effort and are able to derive feasible allocations automatically.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.18"}, {"primary_key": "4468878", "vector": [], "sparse_vector": [], "title": "Covert Communication in Mobile Applications (T).", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper studies communication patterns in mobile applications. Our analysis shows that 63% of the external communication made by top-popular free Android applications from Google Play has no effect on the user-observable application functionality. To detect such covert communication in an efficient manner, we propose a highly precise and scalable static analysis technique: it achieves 93% precision and 61% recall compared to the empirically determined \"ground truth\", and runs in a matter of a few minutes. Furthermore, according to human evaluators, in 42 out of 47 cases, disabling connections deemed covert by our analysis leaves the delivered application experience either completely intact or with only insignificant interference. We conclude that our technique is effective for identifying and disabling covert communication. We then use it to investigate communication patterns in the 500 top-popular applications from Google Play.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.66"}, {"primary_key": "4468879", "vector": [], "sparse_vector": [], "title": "Stability of Self-Adaptive Software Architectures.", "authors": ["<PERSON>"], "summary": "Stakeholders and organisations are increasingly looking for long-lived software. As architectures have a profound effect on the operational life-time of the software and the quality of the service provision, architectural stability could be considered a primary criterion towards achieving the long-livety of the software. Architectural stability is envisioned as the next step in quality attributes, combining many inter-related qualities. This research suggests the notion of behavioural stability as a primary criterion for evaluating whether the architecture maintains achieving the expected quality attributes, maintaining architecture robustness, and evaluating how well the architecture accommodates run-time evolutionary changes. The research investigates the notion of architecture stability at run-time in the context of self-adaptive software architectures. We expect to define, characterise and analyse this intuitive concept, as well as identify the consequent trade-offs to be dynamically managed and enhance the self-adaptation process for a long-lived software.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.93"}, {"primary_key": "4468881", "vector": [], "sparse_vector": [], "title": "Cost-Efficient Sampling for Performance Prediction of Configurable Systems (T).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "A key challenge of the development and maintenanceof configurable systems is to predict the performance ofindividual system variants based on the features selected. It isusually infeasible to measure the performance of all possible variants, due to feature combinatorics. Previous approaches predictperformance based on small samples of measured variants, butit is still open how to dynamically determine an ideal samplethat balances prediction accuracy and measurement effort. Inthis paper, we adapt two widely-used sampling strategies forperformance prediction to the domain of configurable systemsand evaluate them in terms of sampling cost, which considersprediction accuracy and measurement effort simultaneously. Togenerate an initial sample, we introduce a new heuristic based onfeature frequencies and compare it to a traditional method basedon t-way feature coverage. We conduct experiments on six realworldsystems and provide guidelines for stakeholders to predictperformance by sampling.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.45"}, {"primary_key": "4468883", "vector": [], "sparse_vector": [], "title": "Do Automatically Generated Unit Tests Find Real Faults? An Empirical Study of Effectiveness and Challenges (T).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Rather than tediously writing unit tests manually, tools can be used to generate them automatically - sometimes even resulting in higher code coverage than manual testing. But how good are these tests at actually finding faults? To answer this question, we applied three state-of-the-art unit test generation tools for Java (Randoop, EvoSuite, and Agitar) to the 357 real faults in the Defects4J dataset and investigated how well the generated test suites perform at detecting these faults. Although the automatically generated test suites detected 55.7% of the faults overall, only 19.9% of all the individual test suites detected a fault. By studying the effectiveness and problems of the individual tools and the tests they generate, we derive insights to support the development of automated unit test generators that achieve a higher fault detection rate. These insights include 1) improving the obtained code coverage so that faulty statements are executed in the first instance, 2) improving the propagation of faulty program states to an observable output, coupled with the generation of more sensitive assertions, and 3) improving the simulation of the execution environment to detect faults that are dependent on external factors such as date and time.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.86"}, {"primary_key": "4468885", "vector": [], "sparse_vector": [], "title": "Exploiting Domain and Program Structure to Synthesize Efficient and Precise Data Flow Analyses (T).", "authors": ["<PERSON>", "<PERSON>"], "summary": "A key challenge in implementing an efficient and precise data flow analysis is determining how to abstract the domain of values that a program variable can take on and how to update abstracted values to reflect program semantics. Such updates are performed by a transfer function and recent work by <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> defined the bilateral algorithm for computing the most precise transfer function for a given abstract domain. In this paper, we identify and exploit the special case where abstract domains are comprised of disjoint subsets. For such domains, transfer functions computed using a customized algorithm can improve performance and in combination with symbolic modeling of block-level transfer functions improve precision as well. We implemented these algorithms in Soot and used them to perform data flow analysis on more than 100 non-trivial Java methods drawn from open source projects. Our experimental data are promising as they demonstrate that a 25-fold reduction in analysis time can be achieved and precision can be increased relative to existing methods.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.41"}, {"primary_key": "4468886", "vector": [], "sparse_vector": [], "title": "SpyREST: Automated RESTful API Documentation Using an HTTP Proxy Server (N).", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "RESTful API documentation is expensive to produce and maintain due to the lack of reusable tools and automated solutions. Most RESTful APIs are documented manually and the API developers are responsible for keeping the documentation up to date as the API evolves making the process both costly and error-prone. In this paper we introduce a novel technique using an HTTP proxy server that can be used to automatically generate RESTful API documentation and demonstrate SpyREST, an example implementation of the proposed technique. SpyREST uses a proxy to intercept example API calls and intelligently produces API documentation for RESTful Web APIs by processing the request and response data. Using the proposed HTTP proxy server based technique, RESTful API developers can significantly reduce the cost of producing and maintaining API documentation by replacing a large manual process with an automated process.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.52"}, {"primary_key": "4468887", "vector": [], "sparse_vector": [], "title": "SpyREST in Action: An Automated RESTful API Documentation Tool.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "RESTful APIs are often manually documented. As a result, the process of maintaining the documentation of RESTful APIs is both expensive and error-prone. In this demonstration paper, we present SpyREST as an automated software as a service tool that can be used to document RESTful APIs. SpyREST leverages an HTTP Proxy server to intercept real API calls to automatically collect and generate RESTful API documentation by processing HTTP traffic involved in API calls. SpyREST provides an automated yet customizable example based documentation solution for RESTful APIs. RESTful API developers can use SpyREST to automatically generate and maintain updated API documentation.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.92"}, {"primary_key": "4468888", "vector": [], "sparse_vector": [], "title": "Development Emails Content Analyzer: Intention Mining in Developer Discussions (T).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Written development communication (e.g. mailing lists, issue trackers) constitutes a precious source of information to build recommenders for software engineers, for example aimed at suggesting experts, or at redocumenting existing source code. In this paper we propose a novel, semi-supervised approach named DECA (Development Emails Content Analyzer) that uses Natural Language Parsing to classify the content of development emails according to their purpose (e.g. feature request, opinion asking, problem discovery, solution proposal, information giving etc), identifying email elements that can be used for specific tasks. A study based on data from Qt and Ubuntu, highlights a high precision (90%) and recall (70%) of DECA in classifying email content, outperforming traditional machine learning strategies. Moreover, we successfully used DECA for re-documenting source code of Eclipse and Lucene, improving the recall, while keeping high precision, of a previous approach based on ad-hoc heuristics.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.12"}, {"primary_key": "4468889", "vector": [], "sparse_vector": [], "title": "MetaMod: A Modeling Formalism with Modularity at Its Core.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Because modern engineering products require more and more functionality, the models used in the design of these products get larger and more complex. A way to handle this complexity would be a suitable mechanism to modularize models. However, current approaches in the Model Driven Engineering field have limited support for modularity. This is the gap that our research addresses. We want to tackle the gap by designing and creating a modeling formalism with modularity at its core - MetaMod. We are including the modeling formalism into a prototype such that we can experiment with it. Our evaluation plan includes bootstrapping MetaMod (defining MetaMod in MetaMod) and creating an industrial DSL in MetaMod.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.29"}, {"primary_key": "4468890", "vector": [], "sparse_vector": [], "title": "Crust: A Bounded Verifier for Rust (N).", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Rust is a modern systems language that provides guaranteed memory safety through static analysis. However, Rust includes an escape hatch in the form of \"unsafe code,\" which the compiler assumes to be memory safe and to preserve crucial pointer aliasing invariants. Unsafe code appears in many data structure implementations and other essential libraries, and bugs in this code can lead to memory safety violations in parts of the program that the compiler otherwise proved safe. We present CRUST, a tool combining exhaustive test generation and bounded model checking to detect memory safety errors, as well as violations of Rust's pointer aliasing invariants within unsafe library code. CRUST requires no programmer annotations, only an indication of the modules to check. We evaluate CRUSTon data structures from the Rust standard library. It detects memory safety bugs that arose during the library's development and remained undetected for several months.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.77"}, {"primary_key": "4468891", "vector": [], "sparse_vector": [], "title": "Region and Effect Inference for Safe Parallelism (T).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present the first full regions-and-effects inference algorithm for explicitly parallel fork-join programs. We infer annotations inspired by Deterministic Parallel Java (DPJ) for a type-safe subset of C++. We chose the DPJ annotations because they give the strongest safety guarantees of any existing concurrency-checking approach we know of, static or dynamic, and it is also the most expressive static checking system we know of that gives strong safety guarantees. This expressiveness, however, makes manual annotation difficult and tedious, which motivates the need for automatic inference, but it also makes the inference problem very challenging: the code may use region polymorphism, imperative updates with complex aliasing, arbitrary recursion, hierarchical region specifications, and wildcard elements to describe potentially infinite sets of regions. We express the inference as a constraint satisfaction problem and develop, implement, and evaluate an algorithm for solving it. The region and effect annotations inferred by the algorithm constitute a checkable proof of safe parallelism, and it can be recorded both for documentation and for fast and modular safety checking.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.59"}, {"primary_key": "4468892", "vector": [], "sparse_vector": [], "title": "Automated Tagging of Software Projects Using Bytecode and Dependencies (N).", "authors": ["<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Several open and closed source repositories group software systems and libraries to allow members of particular organizations or the open source community to take advantage of them. However, to make this possible, it is necessary to have effective ways of searching and browsing the repositories. Software tagging is the process of assigning terms (i.e., tags or labels) to software assets in order to describe features and internal details, making the task of understanding software easier and potentially browsing and searching through a repository more effective. We present <PERSON>, an automatic software tagging approach that is able to produce meaningful tags for Maven-based software projects by analyzing their bytecode and dependency relations without any special requirements from developers. We compared tags generated by <PERSON> to the ones in two widely used online repositories, and the tags generated by a state-of-the-art categorization approach. The results suggest that <PERSON> is able to generate expressive tags without relying on machine learning-based models.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.38"}, {"primary_key": "4468893", "vector": [], "sparse_vector": [], "title": "How do Developers Document Database Usages in Source Code? (N).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Database-centric applications (DCAs) usually contain a large number of tables, attributes, and constraints describing the underlying data model. Understanding how database tables and attributes are used in the source code along with the constraints related to these usages is an important component of DCA maintenance. However, documenting database-related operations and their constraints in the source code is neither easy nor common in practice. In this paper, we present a two-fold empirical study aimed at identifying how developers document database usages at source code method level. In particular, (i) we surveyed open source developers to understand their practices on documenting database usages in source code, and (ii) we mined a large set of open source projects to measure to what extent database-related methods are commented and if these comments are updated during evolution. Although 58% of the developers claimed to find value in method comments describing database usages, our findings suggest that 77% of 33K+ methods in 3.1K+ open-source Java projects with database accesses were completely undocumented.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.67"}, {"primary_key": "4468894", "vector": [], "sparse_vector": [], "title": "String Analysis of Android Applications (N).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The desire to understand mobile applications has resulted in researchers adapting classical static analysis techniques to the mobile domain. Examination of data and control flows in Android apps is now a common practice to classify them. Important to these analyses is a fine-grained examination and understanding of strings, since in Android they are heavily used in intents, URLs, reflection, and content providers. Rigorous analysis of string creation, usage, and value characteristics offers additional information to increase precision of app classification. This paper shows that inter-procedural static analysis that specifically targets string construction and usage can be used to reveal valuable insights for classifying Android apps. To this end, we first present case studies to illustrate typical uses of strings in Android apps. We then present the results of our analysis on real-world malicious and benign apps. Our analysis examines how strings are created and used for URL objects, Java reflection, and Android intents, and infers the actual string values used as much as possible. Our results demonstrate that string disambiguation based on creation, usage, and value indeed provides additional information that may be used to improve precision of classifying application behaviors.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.20"}, {"primary_key": "4468895", "vector": [], "sparse_vector": [], "title": "Developing a DSL-Based Approach for Event-Based Monitoring of Systems of Systems: Experiences and Lessons Learned (E).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Complex software-intensive systems are often described as systems of systems (SoS) comprising heterogeneous architectural elements. As SoS behavior fully emerges during operation only, runtime monitoring is needed to detect deviations from requirements. Today, diverse approaches exist to define and check runtime behavior and performance characteristics. However, existing approaches often focus on specific types of systems and address certain kinds of checks, thus impeding their use in industrial SoS. Furthermore, as many SoS need to run continuously for long periods, the dynamic definition and deployment of constraints needs to be supported. In this paper we describe experiences of developing and applying a DSL-based approach for monitoring an SoS in the domain of industrial automation software. We evaluate both the expressiveness of our DSL as well as the scalability of the constraint checker. We also describe lessons learned.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.25"}, {"primary_key": "4468896", "vector": [], "sparse_vector": [], "title": "The ReMinds Tool Suite for Runtime Monitoring of Systems of Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The behavior of systems of systems (SoS) emerges only fully during operation and is hard to predict. SoS thus need to be monitored at runtime to detect deviations from important requirements. However, existing approaches for checking runtime behavior and performance characteristics are limited with respect to the kinds of checks and the types of technologies supported, which impedes their use in industrial SoS. In this tool demonstration paper we describe the ReMinds tool suite for runtime monitoring of SoS developed in response to industrial monitoring scenarios. ReMinds provides comprehensive tool support for instrumenting systems, extracting events and data at runtime, defining constraints to check expected behavior and properties, and visualizing constraint violations to facilitate diagnosis.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.91"}, {"primary_key": "4468897", "vector": [], "sparse_vector": [], "title": "Mining User Opinions in Mobile App Reviews: A Keyword-Based Approach (T).", "authors": ["<PERSON><PERSON>", "<PERSON> Ng<PERSON>en", "Hung V<PERSON> Pham", "<PERSON><PERSON>"], "summary": "User reviews of mobile apps often contain complaints or suggestions which are valuable for app developers to improve user experience and satisfaction. However, due to the large volume and noisy-nature of those reviews, manually analyzing them for useful opinions is inherently challenging. To address this problem, we propose MARK, a keyword-based framework for semi-automated review analysis. MARK allows an analyst describing his interests in one or some mobile apps by a set of keywords. It then finds and lists the reviews most relevant to those keywords for further analysis. It can also draw the trends over time of those keywords and detect their sudden changes, which might indicate the occurrences of serious issues. To help analysts describe their interests more effectively, MARK can automatically extract keywords from raw reviews and rank them by their associations with negative reviews. In addition, based on a vector-based semantic representation of keywords, MARK can divide a large set of keywords into more cohesive subsets, or suggest keywords similar to the selected ones.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.85"}, {"primary_key": "4468898", "vector": [], "sparse_vector": [], "title": "Tool Support for Analyzing Mobile App Reviews.", "authors": ["<PERSON><PERSON>", "Hung V<PERSON> Pham", "<PERSON> Ng<PERSON>en", "<PERSON><PERSON>"], "summary": "Mobile app reviews often contain useful user opinions for app developers. However, manual analysis of those reviews is challenging due to their large volume and noisynature. This paper introduces MARK, a supporting tool for review analysis of mobile apps. With MARK, an analyst can describe her interests of one or more apps via a set of keywords. MARK then lists the reviews most relevant to those keywords for further analyses. It can also draw the trends over time of the selected keywords, which might help the analyst to detect sudden changes in the related user reviews. To help the analyst describe her interests more effectively, MARK can automatically extract and rank the keywords by their associations with negative reviews, divide a large set of keywords into more cohesive subgroups, or expand a small set into a broader one.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.101"}, {"primary_key": "4468899", "vector": [], "sparse_vector": [], "title": "Automatic Detection of Potential Layout Faults Following Changes to Responsive Web Pages (N).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Due to the exponential increase in the number ofmobile devices being used to access the World Wide Web, it iscrucial that Web sites are functional and user-friendly across awide range of Web-enabled devices. This necessity has resulted in the introduction of responsive Web design (RWD), which usescomplex cascading style sheets (CSS) to fluidly modify a Web site's appearance depending on the viewport width of the device in use. Although existing tools may support the testing of responsive Web sites, they are time consuming and error-prone to use because theyrequire manual screenshot inspection at specified viewport widths. Addressing these concerns, this paper presents a method thatcan automatically detect potential layout faults in responsively designed Web sites. To experimentally evaluate this approach, weimplemented it as a tool, called ReDeCheck, and applied itto 5 real-world web sites that vary in both their approach toresponsive design and their complexity. The experiments revealthat ReDeCheck finds 91% of the inserted layout faults.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.31"}, {"primary_key": "4468900", "vector": [], "sparse_vector": [], "title": "A Message-Passing Architecture without Public Ids Using Send-to-Behavior.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We explore a novel model of computation based on nodes that have no public addresses (ids). We define nodes as concurrent, message-passing computational entities in an abstract communication medium, similar to the Actor model, but with all public node ids elided. Instead, drawing inspiration from biological systems, we postulate a send-to-behavior language construct to enable anonymous one-way communication. A behavior, defined as a function of input to actions, is also an intensional definition of the subset of nodes that express it. Sending to a behavior is defined to deliver the message to one or more nodes that implement that behavior.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.79"}, {"primary_key": "4468901", "vector": [], "sparse_vector": [], "title": "Test Analysis: Searching for Faults in Tests (N).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Tests are increasingly specified as programs. Expressing tests as code is advantageous in that developers are comfortable writing and running code, and tests can be automated and reused as the software evolves. Tests expressed as code, however, can also contain faults. Some test faults are similar to those found in application code, while others are more subtle, caused by incorrect implementation of testing concepts and processes. These faults may cause a test to fail when it should not, or allow program faults to go undetected. In this work we explore whether lightweight static analyses can be cost-effective in pinpointing patterns associated with faults tests. Our exploration includes a categorization and explanation of test patterns, and their application to 12 open source projects that include over 40K tests. We found that several patterns, detectable through simple and efficient static analyses of just the test code, can detect faults with a low false positive rate, while other patterns would require a more sophisticated and extensive code analysis to be useful.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.37"}, {"primary_key": "4468902", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> Shadow State Compression for Precise Dynamic Race Detection (T).", "authors": ["<PERSON>", "<PERSON>", "Cormac Flanagan", "<PERSON>"], "summary": "Precise dynamic race detectors incur significant time and space overheads, particularly for array-intensive programs, due to the need to store and manipulate analysis (or shadow) state for every element of every array. This paper presents SlimState, a precise dynamic race detector that uses an adaptive, online algorithm to optimize array shadow state representations. SlimState is based on the insight that common array access patterns lead to analogous patterns in array shadow state, enabling optimized, space efficient representations of array shadow state with no loss in precision. We have implemented SlimState for Java. Experiments on a variety of benchmarks show that array shadow compression reduces the space and time overhead of race detection by 27% and 9%, respectively. It is particularly effective for array-intensive programs, reducing space and time overheads by 35% and 17%, respectively, on these programs.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.19"}, {"primary_key": "4468903", "vector": [], "sparse_vector": [], "title": "Generating Qualifiable Avionics Software: An Experience Report (E).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We report on our experience with enhancing the data-management component in the avionics software of the NH90 helicopter at Airbus Helicopters. We describe challenges regarding the evolution of avionics software by means of real-world evolution scenarios that arise in industrial practice. A key role plays a legally-binding certification process, called qualification, which is responsible for most of the development effort and cost. To reduce effort and cost, we propose a novel generative approach to develop qualifiable avionics software by combining model-based and product-line technology. Using this approach, we have already generated code that is running on the NH90 helicopter and that is in the process of replacing the current system code. Based on an interview with two professional developers at Airbus and an analysis of the software repository of the NH90, we systematically compare our approach with established development approaches in the avionics domain, in terms of implementation and qualification effort.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.35"}, {"primary_key": "4468904", "vector": [], "sparse_vector": [], "title": "Static Window Transition Graphs for Android (T).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Dacong Yan", "<PERSON><PERSON><PERSON>"], "summary": "This work develops a static analysis to create a model of the behavior of an Android application's GUI. We propose the window transition graph (WTG), a model representing the possible GUI window sequences and their associated events and callbacks. A key component and contribution of our work is the careful modeling of the stack of currently-active windows, the changes to this stack, and the effects of callbacks related to these changes. To the best of our knowledge, this is the first detailed study of this important static analysis problem for Android. We develop novel analysis algorithms for WTG construction and traversal, based on this modeling of the window stack. We also describe an application of the WTG for GUI test generation, using path traversals. The evaluation of the proposed algorithms indicates their effectiveness and practicality.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.76"}, {"primary_key": "4468905", "vector": [], "sparse_vector": [], "title": "Executing Model-Based Tests on Platform-Specific Implementations (T).", "authors": ["Dongjiang You", "<PERSON><PERSON><PERSON>", "Mats <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Model-based testing of embedded real-time systems is challenging because platform-specific details are often abstracted away to make the models amenable to various analyses. Testing an implementation to expose non-conformance to such a model requires reconciling differences arising from these abstractions. Due to stateful behavior, naive comparisons of model and system behaviors often fail causing numerous false positives. Previously proposed approaches address this by being reactively permissive: passing criteria are relaxed to reduce false positives, but may increase false negatives, which is particularly bothersome for safety-critical systems. To address this concern, we propose an automated approach that is proactively adaptive: test stimuli and system responses are suitably modified taking into account platform-specific aspects so that the modified test when executed on the platform-specific implementation exercises the intended scenario captured in the original model-based test. We show that the new framework eliminates false negatives while keeping the number of false positives low for a variety of platform-specific configurations.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.64"}, {"primary_key": "4468906", "vector": [], "sparse_vector": [], "title": "Performance Prediction of Configurable Software Systems by Fourier Learning (T).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Understanding how performance varies across a large number of variants of a configurable software system is important for helping stakeholders to choose a desirable variant. Given a software system with n optional features, measuring all its 2 n possible configurations to determine their performances is usually infeasible. Thus, various techniques have been proposed to predict software performances based on a small sample of measured configurations. We propose a novel algorithm based on Fourier transform that is able to make predictions of any configurable software system with theoretical guarantees of accuracy and confidence level specified by the user, while using minimum number of samples up to a constant factor. Empirical results on the case studies constructed from real-world configurable systems demonstrate the effectiveness of our algorithm.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.15"}, {"primary_key": "4468907", "vector": [], "sparse_vector": [], "title": "Automatically Generating Test Templates from Test Names (N).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Existing specification-based testing techniques require specifications that either do not exist or are too difficult to create. As a result, they often fall short of their goal of helping developers test expected behaviors. In this paper we present a novel, natural language-based approach that exploits the descriptive nature of test names to generate test templates. Similar to how modern IDEs simplify development by providing templates for common constructs such as loops, test templates can save time and lower the cognitive barrier for writing tests. The results of our evaluation show that the approach is feasible: despite the difficulty of the task, when test names contain a sufficient amount of information, the approach's accuracy is over 80% when parsing the relevant information from the test name and generating the template.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.68"}, {"primary_key": "4468909", "vector": [], "sparse_vector": [], "title": "CIVL: Formal Verification of Parallel Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "CIVL is a framework for static analysis and verification of concurrent programs. One of the main challenges to practical application of these techniques is the large number of ways to express concurrency: MPI, OpenMP, CUDA, and Pthreads, for example, are just a few of many \"concurrency dialects\" in wide use today. These dialects are constantly evolving and it is increasingly common to use several of them in a single \"hybrid\" program. CIVL addresses these problems by providing a concurrency intermediate verification language, CIVL-C, as well as translators that consume C programs using these dialects and produce CIVL-C. Analysis and verification tools which operate on CIVL-C can then be applied easily to a wide variety of concurrent C programs. We demonstrate CIVL's error detection and verification capabilities on (1) an MPI+OpenMP program that estimates π and contains a subtle race condition, and (2) an MPI-based 1d-wave simulator that fails to conform to a simple sequential implementation.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.99"}, {"primary_key": "4468910", "vector": [], "sparse_vector": [], "title": "An Automated Framework for Recommending Program Elements to Novices (N).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Novice programmers often learn programming by implementing well-known algorithms. There are several challenges in the process. Recommendation systems in software currently focus on programmer productivity and ease of development. Teaching aides for such novice programmers based on recommendation systems still remain an under-explored area. In this paper, we present a general framework for recognizing the desired target for partially-written code and recommending a reliable series of edits to transform the input program into the target solution. Our code analysis is based on graph matching and tree edit algorithms. Our experimental results show that efficient graph comparison techniques can accurately match two portions of source code and produce an accurate set of source code edits. We provide details on implementation of our framework, which is developed as a plugin for Java in Eclipse IDE.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.54"}, {"primary_key": "4468911", "vector": [], "sparse_vector": [], "title": "Learning to Rank for Question-Oriented Software Text Retrieval (T).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yangyang Lu", "<PERSON>", "<PERSON>"], "summary": "Question-oriented text retrieval, aka natural language-based text retrieval, has been widely used in software engineering. Earlier work has concluded that questions with the same keywords but different interrogatives (such as how, what) should result in different answers. But what is the difference? How to identify the right answers to a question? In this paper, we propose to investigate the \"answer style\" of software questions with different interrogatives. Towards this end, we build classifiers in a software text repository and propose a re-ranking approach to refine search results. The classifiers are trained by over 16,000 answers from the StackOverflow forum. Each answer is labeled accurately by its question's explicit or implicit interrogatives. We have evaluated the performance of our classifiers and the refinement of our re-ranking approach in software text retrieval. Our approach results in 13.1% and 12.6% respectively improvement with respect to text retrieval criteria nDCG@1 and nDCG@10 compared to the baseline. We also apply our approach to FAQs of 7 open source projects and show 13.2% improvement with respect to nDCG@1. The results of our experiments suggest that our approach could find answers to FAQs more precisely.", "published": "2015-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE.2015.24"}]