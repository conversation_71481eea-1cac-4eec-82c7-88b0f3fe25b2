[{"primary_key": "2130972", "vector": [], "sparse_vector": [], "title": "Home, safehome: smart home reliability with visibility and atomicity.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Smart environments (homes, factories, hospitals, buildings) contain an increasing number of IoT devices, making them complex to manage. Today, in smart homes when users or triggers initiate routines (i.e., a sequence of commands), concurrent routines and device failures can cause incongruent outcomes. We describe SafeHome, a system that provides notions of atomicity and serial equivalence for smart homes. Due to the human-facing nature of smart homes, SafeHome offers a spectrum of visibility models which trade off between responsiveness vs. isolation of the smart home. We implemented SafeHome and performed workload-driven experiments. We find that a weak visibility model, called eventual visibility, is almost as fast as today's status quo (up to 23% slower) and yet guarantees serially-equivalent end states.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456261"}, {"primary_key": "2130974", "vector": [], "sparse_vector": [], "title": "Take it to the limit: peak prediction-driven resource overcommitment in datacenters.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To increase utilization, datacenter schedulers often overcommit resources where the sum of resources allocated to the tasks on a machine exceeds its physical capacity. Setting the right level of overcommitment is a challenging problem: low overcommitment leads to wasted resources, while high overcommitment leads to task performance degradation. In this paper, we take a first principles approach to designing and evaluating overcommit policies by asking a basic question: assuming complete knowledge of each task's future resource usage, what is the safest overcommit policy that yields the highest utilization? We call this policy the peak oracle. We then devise practical overcommit policies that mimic this peak oracle by predicting future machine resource usage. We simulate our overcommit policies using the recently-released Google cluster trace, and show that they result in higher utilization and less overcommit errors than policies based on per-task allocations. We also deploy these policies to machines inside Google's datacenters serving its internal production workload. We show that our overcommit policies increase these machines' usable CPU capacity by 10-16% compared to no overcommitment.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456259"}, {"primary_key": "2130976", "vector": [], "sparse_vector": [], "title": "Profiling dataflow systems on multiple abstraction levels.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Dataflow graphs are a popular abstraction for describing computation, used in many systems for high-level optimization. For execution, dataflow graphs are lowered and optimized through layers of program representations down to machine instructions. Unfortunately, performance profiling such systems is cumbersome, as today's profilers present results merely at instruction and function granularity. This obfuscates the connection between profiles and high-level constructs, such as operators and pipelines, making interpretation of profiles an exercise in puzzling and deduction.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456254"}, {"primary_key": "2130977", "vector": [], "sparse_vector": [], "title": "Tesseract: distributed, general graph pattern mining on evolving graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Tesseract is the first distributed system for executing general graph mining algorithms on evolving graphs. Tesseract scales out by decomposing a stream of graph updates into per-update mining tasks and dynamically assigning these tasks to a set of distributed workers. We present a novel approach to change detection that efficiently determines the exact modifications to the algorithm's output for each update to the input graph. We use a disaggregated, multiversioned graph store to allow workers to process updates independently, without producing duplicates. Moreover, Tesseract provides interactive mining insights for complex applications using an incremental aggregation API. Finally, we implement and evaluate Tesseract and demonstrate that it achieves orders-of-magnitude improvements over state-of-the-art systems.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456253"}, {"primary_key": "2130979", "vector": [], "sparse_vector": [], "title": "DGCL: an efficient communication library for distributed GNN training.", "authors": ["Zhenkun <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Graph neural networks (GNNs) have gained increasing popularity in many areas such as e-commerce, social networks and bio-informatics. Distributed GNN training is essential for handling large graphs and reducing the execution time. However, for distributed GNN training, a peer-to-peer communication strategy suffers from high communication overheads. Also, different GPUs require different remote vertex embeddings, which leads to an irregular communication pattern and renders existing communication planning solutions unsuitable. We propose the distributed graph communication library (DGCL) for efficient GNN training on multiple GPUs. At the heart of DGCL is a communication planning algorithm tailored for GNN training, which jointly considers fully utilizing fast links, fusing communication, avoiding contention and balancing loads on different links. DGCL can be easily adopted to extend existing single-GPU GNN systems to distributed training. We conducted extensive experiments on different datasets and network configurations to compare DGCL with alternative communication schemes. In our experiments, DGCL reduces the communication time of the peer-to-peer communication by 77.5% on average and the training time for an epoch by up to 47%.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456233"}, {"primary_key": "2130980", "vector": [], "sparse_vector": [], "title": "Site-to-site internet traffic control.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Queues allow network operators to control traffic: where queues build, they can enforce scheduling and shaping policies. In the Internet today, however, there is a mismatch between where queues build and where control is most effectively enforced; queues build at bottleneck links that are often not under the control of the data sender. To resolve this mismatch, we propose a new kind of middlebox, called Bundler. <PERSON>und<PERSON> uses a novel inner control loop between a sendbox (in the sender's site) and a receivebox (in the receiver's site) to determine the aggregate rate for the bundle, leaving the end-to-end connections and their control loops intact. Enforcing this sending rate ensures that bottleneck queues that would have built up from the bundle's packets now shift from the bottleneck to the sendbox. The sendbox then exercises control over its traffic by scheduling packets to achieve higher-level objectives. We have implemented <PERSON>undler in Linux and evaluated it with real-world and emulation experiments. We find that Bundler allows the sender-chosen policy to be effective: when configured to implement Stochastic Fairness Queueing (SFQ), it improves median flow completion time (FCT) by between 28% and 97% across various scenarios.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456260"}, {"primary_key": "2130984", "vector": [], "sparse_vector": [], "title": "Achieving low tail-latency and high scalability for serializable transactions in edge computing.", "authors": ["<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A distributed database utilizing the wide-spread edge computing servers to provide low-latency data access with the serializability guarantee is highly desirable for emerging edge computing applications. In an edge database, nodes are divided into regions, and a transaction can be categorized as intra-region (IRT) or cross-region (CRT) based on whether it accesses data in different regions. In addition to serializability, we insist that a practical edge database should provide low tail latency for both IRTs and CRTs, and such low latency must be scalable to a large number of regions. Unfortunately, none of existing geo-replicated serializable databases or edge databases can meet such requirements.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456238"}, {"primary_key": "2130985", "vector": [], "sparse_vector": [], "title": "Understanding and dealing with hard faults in persistent memory systems.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The advent of Persistent Memory (PM) devices enables systems to actively persist information at low costs, including program state traditionally in volatile memory. However, this trend poses a reliability challenge in which multiple classes of soft faults that go away after restart in traditional systems turn into hard (recurring) faults in PM systems. In this paper, we first characterize this rising problem with an empirical study of 28 real-world bugs. We analyze how they cause hard faults in PM systems. We then propose Arthas, a tool to effectively recover PM systems from hard faults. Arthas checkpoints PM states via fine-grained versioning and uses program slicing of fault instructions to revert problematic PM states to good versions. We evaluate Arthas on 12 real-world hard faults from five large PM systems. <PERSON><PERSON> successfully recovers the systems for all cases while discarding 10× less data on average compared to state-of-the-art checkpoint-rollback solutions.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456252"}, {"primary_key": "2130988", "vector": [], "sparse_vector": [], "title": "Efficient replication via timestamp stability.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern web applications replicate their data across the globe and require strong consistency guarantees for their most critical data. These guarantees are usually provided via state-machine replication (SMR). Recent advances in SMR have focused on leaderless protocols, which improve the availability and performance of traditional Paxos-based solutions. We propose Tempo - a leaderless SMR protocol that, in comparison to prior solutions, achieves superior throughput and offers predictable performance even in contended workloads. To achieve these benefits, Tempo timestamps each application command and executes it only after the timestamp becomes stable, i.e., all commands with a lower timestamp are known. Both the timestamping and stability detection mechanisms are fully decentralized, thus obviating the need for a leader replica. Our protocol furthermore generalizes to partial replication settings, enabling scalability in highly parallel workloads. We evaluate the protocol in both real and simulated geo-distributed environments and demonstrate that it outperforms state-of-the-art alternatives.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456236"}, {"primary_key": "2130990", "vector": [], "sparse_vector": [], "title": "REBOUND: defending distributed systems against attacks with bounded-time recovery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper shows how to use bounded-time recovery (BTR) to defend distributed systems against non-crash faults and attacks. Unlike many existing fault-tolerance techniques, BTR does not attempt to completely mask all symptoms of a fault; instead, it ensures that the system returns to the correct behavior within a bounded amount of time. This weaker guarantee is sufficient, e.g., for many cyber-physical systems, where physical properties - such as inertia and thermal capacity - prevent quick state changes and thus limit the damage that can result from a brief period of undefined behavior.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456257"}, {"primary_key": "2130991", "vector": [], "sparse_vector": [], "title": "Odyssey: the impact of modern hardware on strongly-consistent replication protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Get/Put Key-Value Stores (KVSes) rely on replication protocols to enforce consistency and guarantee availability. Today's modern hardware, with manycore servers and RDMA-capable networks, challenges the conventional wisdom on protocol design. In this paper, we investigate the impact of modern hardware on the performance of strongly-consistent replication protocols.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456240"}, {"primary_key": "2130996", "vector": [], "sparse_vector": [], "title": "Confidential computing for OpenPOWER.", "authors": ["<PERSON><PERSON><PERSON> D. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents Protected Execution Facility (PEF), a virtual machine-based Trusted Execution Environment (TEE) for confidential computing on Power ISA. PEF enables protected secure virtual machines (SVMs). Like other TEEs, PEF verifies the SVM prior to execution. PEF utilizes a Trusted Platform Module (TPM), secure boot, and trusted boot as well as newly introduced architectural changes for Power ISA systems. Exploiting these architectural changes requires new firmware, the Protected Execution Ultravisor. PEF is supported in the latest version of the POWER9 chip. PEF demonstrates that access control for isolation and cryptography for confidentiality is an effective approach to confidential computing. We particularly focus on how our design (i) balances between access control and cryptography, (ii) maximizes the use of existing security components, and (iii) simplifies the management of the SVM life cycle. Finally, we evaluate the performance of SVMs in comparison to normal virtual machines on OpenPOWER systems.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456243"}, {"primary_key": "2130997", "vector": [], "sparse_vector": [], "title": "Accelerating graph sampling for graph machine learning using GPUs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Representation learning algorithms automatically learn the features of data. Several representation learning algorithms for graph data, such as DeepWalk, node2vec, and Graph-SAGE, sample the graph to produce mini-batches that are suitable for training a DNN. However, sampling time can be a significant fraction of training time, and existing systems do not efficiently parallelize sampling.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456244"}, {"primary_key": "2131000", "vector": [], "sparse_vector": [], "title": "<PERSON>ne: generalized incremental graph processing via graph triangle inequality.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For compute-intensive iterative queries over a streaming graph, it is critical to evaluate the queries continuously and incrementally for best efficiency. However, the existing incremental graph processing requires a priori knowledge of the query (e.g., the source vertex of a vertex-specific query); otherwise, it has to fall back to the expensive full evaluation that starts from scratch.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456226"}, {"primary_key": "2131001", "vector": [], "sparse_vector": [], "title": "Zeus: locality-aware distributed transactions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "State-of-the-art distributed in-memory datastores (FaRM, FaSST, DrTM) provide strongly-consistent distributed transactions with high performance and availability. Transactions in those systems are fully general; they can atomically manipulate any set of objects in the store, regardless of their location. To achieve this, these systems use complex distributed transactional protocols. Meanwhile, many workloads have a high degree of locality. For such workloads, distributed transactions are an overkill as most operations only access objects located on the same server -- if sharded appropriately. In this paper, we show that for these workloads, a single-node transactional protocol combined with dynamic object re-sharding and asynchronously pipelined replication can provide the same level of generality with better performance, simpler protocols, and lower developer effort. We present Zeus, an in-memory distributed datastore that provides general transactions by acquiring all objects involved in the transaction to the same server and executing a single-node transaction on them. Zeus is fault-tolerant and strongly-consistent. At the heart of Zeus is a reliable dynamic object sharding protocol that can move 250K objects per second per server, allowing Zeus to process millions of transactions per second and outperform more traditional distributed transactions on a wide range of workloads that exhibit locality.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456234"}, {"primary_key": "2131002", "vector": [], "sparse_vector": [], "title": "Ethanos: efficient bootstrapping for full nodes on account-based blockchain.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sang-Hyeon Park", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Ethereum is a popular account-based blockchain whose number of accounts and transactions has skyrocketed, causing its data explosion. As a result, ordinary clients using PCs or smartphones cannot easily bootstrap as a full node, but rely on other full nodes to verify transactions, thus being exposed to security risks. The most serious overhead is caused by synchronizing the state of all accounts in the block's state trie, which takes several tens of gigabytes. Observing that more than 95% of the accounts are dormant, we propose a novel state optimization technique, named Ethanos. Ethanos downsizes the state trie by periodically emptying it, and then re-build it only with the active accounts used in the period's transactions. Ethanos runs transactions using the accounts available in the current period's state trie as well as those available at the end of the previous period's state trie. For an account in neither of the tries, the account first restores itself by transmitting a restore transaction. One important result of this state management is that a node can now bootstrap only with the latest period's state trie, yet can fully verify all transactions thereafter. We evaluated Ethanos with real Ethereum transactions for 300,000 blocks from the 7.0 million block, with a one-week period of emptying the state trie. Our result shows that Ethanos can sharply reduce the state trie, with only a tiny fraction of the restore transactions. More importantly, unlike the Ethereum state trie which continues to grow as time goes on, the Ethanos state trie size at the end of each period is bounded by a few hundred MB, when there are more than one million, one-week-active accounts.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456231"}, {"primary_key": "2131003", "vector": [], "sparse_vector": [], "title": "Unikraft: fast, specialized unikernels the easy way.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Unikernels are famous for providing excellent performance in terms of boot times, throughput and memory consumption, to name a few metrics. However, they are infamous for making it hard and extremely time consuming to extract such performance, and for needing significant engineering effort in order to port applications to them. We introduce Unikraft, a novel micro-library OS that (1) fully modularizes OS primitives so that it is easy to customize the unikernel and include only relevant components and (2) exposes a set of composable, performance-oriented APIs in order to make it easy for developers to obtain high performance.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456248"}, {"primary_key": "2131004", "vector": [], "sparse_vector": [], "title": "Parallelizing packet processing in container overlay networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Container networking, which provides connectivity among containers on multiple hosts, is crucial to building and scaling container-based microservices. While overlay networks are widely adopted in production systems, they cause significant performance degradation in both throughput and latency compared to physical networks. This paper seeks to understand the bottlenecks of in-kernel networking when running container overlay networks. Through profiling and code analysis, we find that a prolonged data path, due to packet transformation in overlay networks, is the culprit of performance loss. Furthermore, existing scaling techniques in the Linux network stack are ineffective for parallelizing the prolonged data path of a single network flow.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456241"}, {"primary_key": "2131006", "vector": [], "sparse_vector": [], "title": "Towards timeout-less transport in commodity datacenter networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Han"], "summary": "Despite recent advances in datacenter networks, timeouts caused by congestion packet losses still remain a major cause of high tail latency. Priority-based Flow Control (PFC) was introduced to make the network lossless, but its Head-of-Line blocking nature causes various performance and management problems. In this paper, we ask if it is possible to design a network that achieves (near) zero timeout only using commodity hardware in datacenters.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456227"}, {"primary_key": "2131007", "vector": [], "sparse_vector": [], "title": "M3: end-to-end memory management in elastic system software stacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>g <PERSON>"], "summary": "This paper proposes M3, an end-to-end system that dynamically distributes memory resources among competing applications to maximize their overall performance. Today's data center workloads, can adapt to a wide range of memory sizes, and they are built on complex software stacks.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456256"}, {"primary_key": "2131010", "vector": [], "sparse_vector": [], "title": "Finding heterogeneous-unsafe configuration parameters in cloud systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the increasing prevalence of heterogeneous hardware and the increasing need for online reconfiguration, there is increasing demand for heterogeneous configurations. However, allowing different nodes to have different configurations may cause errors when these nodes communicate, even if the configuration of each node uses valid values.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456250"}, {"primary_key": "2131012", "vector": [], "sparse_vector": [], "title": "DZiG: sparsity-aware incremental processing of streaming graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "State-of-the-art streaming graph processing systems that provide Bulk Synchronous Parallel (BSP) guarantees remain oblivious to the computation sparsity present in iterative graph algorithms, which severely limits their performance. In this paper we propose DZiG, a high-performance streaming graph processing system that retains efficiency in presence of sparse computations while still guaranteeing BSP semantics. At the heart of DZiG is: (1) a sparsity-aware incremental processing technique that expresses computations in a recursive manner to be able to safely identify and prune updates (hence retaining sparsity); (2) a simple change-driven programming model that naturally exposes sparsity in iterative computations; and, (3) an adaptive processing model that automatically changes the incremental computation strategy to limit its overheads when computations become very sparse. DZiG outperforms state-of-the-art streaming graph processing systems, and pushes the boundary of dependency-driven processing for streaming graphs to over 10 million simultaneous mutations, which is orders of magnitude higher compared to the state-of-the-art systems.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456230"}, {"primary_key": "2131013", "vector": [], "sparse_vector": [], "title": "Characterizing, exploiting, and detecting DMA code injection vulnerabilities in the presence of an IOMMU.", "authors": ["<PERSON>", "Shay <PERSON>argaftik", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Direct memory access (DMA) renders a system vulnerable to DMA attacks, in which I/O devices access memory regions not intended for their use. Hardware input-output memory management units (IOMMU) can be used to provide protection. However, an IOMMU cannot prevent all DMA attacks because it only restricts DMA at page-level granularity, leading to sub-page vulnerabilities.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456249"}, {"primary_key": "2131015", "vector": [], "sparse_vector": [], "title": "RubberBand: cloud-based hyperparameter tuning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Ion <PERSON>", "<PERSON><PERSON>"], "summary": "Hyperparameter tuning is essential to achieving state-of-the-art accuracy in machine learning (ML), but requires substantial compute resources to perform. Existing systems primarily focus on effectively allocating resources for a hyperparameter tuning job under fixed resource constraints. We show that the available parallelism in such jobs changes dynamically over the course of execution and, therefore, presents an opportunity to leverage the elasticity of the cloud.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456245"}, {"primary_key": "2131018", "vector": [], "sparse_vector": [], "title": "OFC: an opportunistic caching system for FaaS platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cloud applications based on the \"Functions as a Service\" (FaaS) paradigm have become very popular. Yet, due to their stateless nature, they must frequently interact with an external data store, which limits their performance. To mitigate this issue, we introduce OFC, a transparent, vertically and horizontally elastic in-memory caching system for FaaS platforms, distributed over the worker nodes. OFC provides these benefits cost-effectively by exploiting two common sources of resource waste: (i) most cloud tenants overprovision the memory resources reserved for their functions because their footprint is non-trivially input-dependent and (ii) FaaS providers keep function sandboxes alive for several minutes to avoid cold starts. Using machine learning models adjusted for typical function input data categories (e.g., multimedia formats), OFC estimates the actual memory resources required by each function invocation and hoards the remaining capacity to feed the cache. We build our OFC prototype based on enhancements to the OpenWhisk FaaS platform, the Swift persistent object store, and the RAM-Cloud in-memory store. Using a diverse set of workloads, we show that OFC improves by up to 82 % and 60 % respectively the execution time of single-stage and pipelined functions.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456239"}, {"primary_key": "2131020", "vector": [], "sparse_vector": [], "title": "Mitigating vulnerability windows with hypervisor transplant.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The vulnerability window of a hypervisor regarding a given security flaw is the time between the identification of the flaw and the integration of a correction/patch in the running hypervisor. Most vulnerability windows, regardless of severity, are long enough (several days) that attackers have time to perform exploits. Nevertheless, the number of critical vulnerabilities per year is low enough to allow an exceptional solution. This paper introduces hypervisor transplant, a solution for addressing vulnerability window of critical flaws. It involves temporarily replacing the current datacenter hypervisor (e.g., Xen) which is subject to a critical security flaw, by a different hypervisor (e.g., KVM) which is not subject to the same vulnerability.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456235"}, {"primary_key": "2131024", "vector": [], "sparse_vector": [], "title": "Memory-mapped I/O on steroids.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Manolis Marazakis", "<PERSON><PERSON>"], "summary": "With current technology trends for fast storage devices, the host-level I/O path is emerging as a main bottleneck for modern, data-intensive servers and applications. The need to improve I/O performance requires customizing various aspects of the I/O path, including the page cache and the method to access the storage devices.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456242"}, {"primary_key": "2131028", "vector": [], "sparse_vector": [], "title": "Virtual machine preserving host updates for zero day patching in public cloud.", "authors": ["<PERSON>", "Naga <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Host software updates are critical to ensure the security, reliability and compliance of public clouds. Many updates require a virtualization component restart or operating system reboot. Virtual machines (VMs) running on the updated servers must either be restarted or live migrated off. Reboots can result in downtime for the VMs on the order of ten minutes, and has further impact on the workloads running in the VMs because cached state is lost. Live migration (LM) is a technology that can avoid the need to shutdown VMs. However, LM requires turn space in the form of already-patched hosts, consumes network, CPU and other resources that scale with the amount of and level of activity of VM, and has variable impact on VM performance and availability, making it too expensive and disruptive for zero-day security updates that must be applied across an entire fleet on the order of hours. We present a novel update technology, virtual machine preserving host updates (VM-PHU), that does not require turn space, consumes no network and little CPU, preserves VM state, and causes minimal VM blackout time that does not scale with VM resource usage. VM-PHU persists the memory and device state of all running guest VMs, reboots the host and virtualization components into updated code, restores the state of the VMs, and then resumes them. VM-PHU makes use of several techniques to minimize VM blackout time. One is to use kernel soft reboot (KSR) to directly transition to an updated host operating system, bypassing firmware reset of the server and attached devices. To minimize resource consumption and VM disruption, VM-PHU leaves VM memory in physical memory pages and other state in persisted pages across the soft reboot, and VM-PHU implements a mechanism called fast close to enable a reboot to proceed without waiting for the completion of in-flight VM I/Os to remote storage devices. We have implemented VM-PHU in Microsoft Azure hosting millions of servers and show results of several zero-day updates that demonstrate VM blackout times on the order of seconds. VM-PHU provides significant benefits to both customers and public cloud vendors by minimizing application downtime while enabling fast and resource efficient updates, including zero-day patches.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456232"}, {"primary_key": "2131029", "vector": [], "sparse_vector": [], "title": "TraceSplitter: a new paradigm for downscaling traces.", "authors": ["Sultan <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Realistic experimentation is a key component of systems research and industry prototyping, but experimental clusters are often too small to replay the high traffic rates found in production traces. Thus, it is often necessary to downscale traces to lower their arrival rate, and researchers/practitioners generally do this in an ad-hoc manner. For example, one practice is to multiply all arrival timestamps in a trace by a scaling factor to spread the load across a longer timespan. However, temporal patterns are skewed by this approach, which may lead to inappropriate conclusions about some system properties (e.g., the agility of auto-scaling). Another popular approach is to count the number of arrivals in fixed-sized time intervals and scale it according to some modeling assumptions. However, such approaches can eliminate or exaggerate the fine-grained burstiness in the trace depending on the time interval length.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456262"}, {"primary_key": "2131039", "vector": [], "sparse_vector": [], "title": "rkt-io: a direct I/O stack for shielded execution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The shielding of applications using trusted execution environments (TEEs) can provide strong security guarantees in untrusted cloud environments. When executing I/O operations, today's shielded execution frameworks, however, exhibit performance and security limitations: they assign resources to the I/O path inefficiently, perform redundant data copies, use untrusted host I/O stacks with security risks and performance overheads. This prevents TEEs from running modern I/O-intensive applications that require high-performance networking and storage.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456255"}, {"primary_key": "2131041", "vector": [], "sparse_vector": [], "title": "PaSh: light-touch data-parallel shell processing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents {\\scshape PaSh}, a system for parallelizing POSIX shell scripts. Given a script, {\\scshape PaSh} converts it to a dataflow graph, performs a series of semantics-preserving program transformations that expose parallelism, and then converts the dataflow graph back into a script -- one that adds POSIX constructs to explicitly guide parallelism coupled with {\\scshape PaSh}-provided {\\scshape Unix}-aware runtime primitives for addressing performance- and correctness-related issues. A lightweight annotation language allows command developers to express key parallelizability properties about their commands. An accompanying parallelizability study of POSIX and GNU commands -- two large and commonly used groups -- guides the annotation language and optimized aggregator library that {\\scshape PaSh} uses. Finally, {\\scshape PaSh}'s {\\scshape PaSh}'s extensive evaluation over 44 unmodified {\\scshape Unix} scripts shows significant speedups ($0.89$--$61.1\\times$, avg: $6.7\\times$) stemming from the combination of its program transformations and runtime primitives.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456228"}, {"primary_key": "2131042", "vector": [], "sparse_vector": [], "title": "SmartHarvest: harvesting idle CPUs safely and efficiently in the cloud.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We can increase the efficiency of public cloud datacenters by harvesting allocated but temporarily idling CPU cores from customer virtual machines (VMs) to run batch or analytics workloads. Even small efficiency gains translate into substantial savings, since provisioning and operating a datacenter costs hundreds of millions of dollars per year. The main challenge is to harvest idle cores with little or no impact on customer VMs, which could be running latency-sensitive services and are essentially black-boxes to the cloud provider.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456225"}, {"primary_key": "2131045", "vector": [], "sparse_vector": [], "title": "FlexGraph: a flexible and efficient distributed framework for GNN training.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Yu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graph neural networks (GNNs) aim to learn a low-dimensional feature for each vertex in the graph from its input high-dimensional feature, by aggregating the features of the vertex's neighbors iteratively. This paper presents Flex-Graph, a distributed framework for training GNN models. FlexGraph is able to efficiently train GNN models with flexible definitions of neighborhood and hierarchical aggregation schemes, which are the two main characteristics associated with GNNs. In contrast, existing GNN frameworks are usually designed for GNNs having fixed definitions and aggregation schemes. They cannot support different kinds of GNN models well simultaneously. Underlying FlexGraph are a simple GNN programming abstraction called NAU and a compact data structure for modeling various aggregation operations. To achieve better performance, FlexGraph is equipped with a hybrid execution strategy to select proper and efficient operations according to different contexts during aggregating neighborhood features, an application-driven workload balancing strategy to balance GNN training workload and reduce synchronization overhead, and a pipeline processing strategy to overlap computations and communications. Using real-life datasets and GNN models GCN, PinSage and MAGNN, we verify that NAU makes FlexGraph more expressive than prior frameworks (e.g., DGL and Euler) which adopt GAS-like programming abstractions, e.g., it can handle MAGNN that is beyond the reach of DGL and Euler. The evaluation further shows that FlexGraph outperforms the state-of-the-art GNN frameworks such as DGL and Euler in training time by on average 8.5× on GCN and PinSage.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456229"}, {"primary_key": "2131048", "vector": [], "sparse_vector": [], "title": "Seastar: vertex-centric programming for graph neural networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Zhenkun <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Graph neural networks (GNNs) have achieved breakthrough performance in graph analytics such as node classification, link prediction and graph clustering. Many GNN training frameworks have been developed, but they are usually designed as a set of manually written, GNN-specific operators plugged into existing deep learning systems, which incurs high memory consumption, poor data locality, and large semantic gap between algorithm design and implementation. This paper proposes the Seastar system, which presents a vertex-centric programming model for GNN training on GPU and provides idiomatic python constructs to enable easy development of novel homogeneous and heterogeneous GNN models. We also propose novel optimizations to produce highly efficient fused GPU kernels for forward and backward passes in GNN training. Compared with the state-of-the art GNN systems, DGL and PyG, Seastar achieves better usability, up to 2 and 8 times less memory consumption, and 14 and 3 times faster execution, respectively.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456247"}, {"primary_key": "2131049", "vector": [], "sparse_vector": [], "title": "Tahoe: tree structure-aware high performance inference engine for decision tree ensemble on GPU.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Decision trees are widely used and often assembled as a forest to boost prediction accuracy. However, using decision trees for inference on GPU is challenging, because of irregular memory access patterns and imbalance workloads across threads. This paper proposes Tahoe, a tree structure-aware high performance inference engine for decision tree ensemble. <PERSON><PERSON> rearranges tree nodes to enable efficient and coalesced memory accesses; <PERSON><PERSON> also rearranges trees, such that trees with similar structures are grouped together in memory and assigned to threads in a balanced way. Besides memory access efficiency, we introduce a set of inference strategies, each of which uses shared memory differently and has different implications on reduction overhead. We introduce performance models to guide the selection of the inference strategies for arbitrary forests and data set. <PERSON><PERSON> consistently outperforms the state-of-the-art industry-quality library FIL by 3.82x, 2.59x, and 2.75x on three generations of NVIDIA GPUs (Ke<PERSON>, Pascal, and Volta), respectively.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456251"}, {"primary_key": "2131052", "vector": [], "sparse_vector": [], "title": "Bridging the performance gap for copy-based garbage collectors atop non-volatile memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON>"], "summary": "Non-volatile memory (NVM) is expected to revolutionize the memory hierarchy with not only non-volatility but also large capacity and power efficiency. Memory-intensive applications, which are often written in managed languages like Java, would run atop NVM for better cost-efficiency. Unfortunately, such applications may suffer from performance slowdown due to the unmanaged performance gap between DRAM and NVM. This paper studies the performance of a series of Java applications atop NVM and uncovers that the copy-based garbage collection (GC), the mainstream GC algorithm, is an NVM-unfriendly component in JVM. GC becomes a severe performance bottleneck especially when memory resource is scarce. To this end, this paper analyzes the memory behavior of copy-based GC and uncovers that its inappropriate usage on NVM bandwidth is the main reason for its performance slowdown. This paper thus proposes two NVM-aware optimizations: write cache and header map, to effectively manage the limited NVM bandwidth. It further improves the GC performance with hardware instructions like non-temporal memory accesses and prefetching. We have implemented the optimizations on two mainstream copy-based garbage collectors in OpenJDK. Evaluation with various memory-intensive applications shows that our optimizations can improve the GC time, application execution time, application tail latency by up to 2.69×, 11.0%, and 5.09×, respectively.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456246"}, {"primary_key": "2131054", "vector": [], "sparse_vector": [], "title": "ChameleonDB: a key-value store for optane persistent memory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Hong Jiang"], "summary": "The emergence of Intel's Optane DC persistent memory (Optane Pmem) draws much interest in building persistent key-value (KV) stores to take advantage of its high throughput and low latency. A major challenge in the efforts stems from the fact that Optane Pmem is essentially a hybrid storage device with two distinct properties. On one hand, it is a high-speed byte-addressable device similar to DRAM. On the other hand, the write to the Optane media is conducted at the unit of 256 bytes, much like a block storage device. Existing KV store designs for persistent memory do not take into account of the latter property, leading to high write amplification and constraining both write and read throughput. In the meantime, a direct re-use of a KV store design intended for block devices, such as LSM-based ones, would cause much higher read latency due to the former property.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456237"}, {"primary_key": "2131055", "vector": [], "sparse_vector": [], "title": "On-demand-fork: a microsecond fork for memory-intensive and latency-sensitive applications.", "authors": ["<PERSON><PERSON> Zhao", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Fork has long been the process creation system call for Unix. At its inception, fork was hailed as an efficient system call due to its use of copy-on-write on memory shared between parent and child processes. However, application memory demand has increased drastically since the early days and the cost incurred by fork to simply set up virtual memory (e.g., copy page tables) is now a concern, even for applications that only require hundreds of MBs of memory. In practice, fork performance already holds back system efficiency and latency across a range of uses cases that fork large processes, such as fault-tolerant systems, serverless frameworks, and testing frameworks.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786.3456258"}, {"primary_key": "2253948", "vector": [], "sparse_vector": [], "title": "EuroSys &apos;21: Sixteenth European Conference on Computer Systems, Online Event, United Kingdom, April 26-28, 2021", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This year's program includes exciting work along many different aspects of systems research, including cloud computing and datacenter systems; database systems and data analytics frameworks; distributed systems; language support and runtime systems; machine learning for systems and systems for machine learning; networking; operating systems; testing, verification and dependability; security; and virtualization.", "published": "2021-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3447786"}]