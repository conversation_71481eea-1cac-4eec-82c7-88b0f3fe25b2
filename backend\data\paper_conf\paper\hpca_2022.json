[{"primary_key": "1669072", "vector": [], "sparse_vector": [], "title": "GBDI: Going Beyond Base-Delta-Immediate Compression with Global Bases.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Memory bandwidth is limiting performance for many emerging applications. While compression techniques can unlock a higher memory bandwidth, prior art offers only modestly better bandwidth. This paper contributes with a new compression method – Global Base Delta Immediate compression (GBDI) – that offers substantially higher memory bandwidth by, unlike prior art, selecting base values across memory blocks. GBDI uses a novel clustering algorithm through data analysis in the background. The presented accelerator infrastructure offers low area overhead and latency. This paper shows that GBDI offers a compression ratio of 2.3×, and yields 1.5× higher bandwidth and 1.1× higher performance compared with a baseline without compression support, on average, for SPEC2017 benchmarks requiring medium to high memory bandwidth.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00085"}, {"primary_key": "1669073", "vector": [], "sparse_vector": [], "title": "Improving Locality of Irregular Updates with Hardware Assisted Propagation Blocking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many application domains perform irregular memory updates. Irregular accesses lead to inefficient use of conventional cache hierarchies. To make better use of the cache, we focus on Propagation Blocking (PB), a software-based cache locality optimization initially designed for graph processing applications. We make two contributions in this work. First, we show that PB generalizes beyond graph processing applications to any application with unordered parallelism and irregular memory updates. Second, we identify the inefficiencies of a PB execution on conventional multicore processors and propose architecture support to further improve the performance gains from PB. Our proposed architecture, COBRA, optimizes the PB execution of a range of applications with irregular memory updates, offering speedups of up to 3.78x compared to PB (1.74x on average).", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00047"}, {"primary_key": "1669074", "vector": [], "sparse_vector": [], "title": "DR-STRaNGe: End-to-End System Design for DRAM-based True Random Number Generators.", "authors": ["<PERSON><PERSON>", "Ataberk Olgun", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Random number generation is an important task in a wide variety of critical applications including cryptographic algorithms, scientific simulations, and industrial testing tools. True Random Number Generators (TRNGs) produce cryptographically-secure truly random data by sampling a physical entropy source that typically requires custom hardware and suffers from long latency. To enable high-bandwidth and low-latency TRNGs on widely-available commodity devices, recent works propose hardware TRNGs that generate random numbers using commodity DRAM as an entropy source. Although prior works demonstrate promising TRNG mechanisms using DRAM, practical integration of such mechanisms into real systems poses various challenges.We identify three key challenges for using DRAM-based TRNGs in current systems: (1) generating random numbers with DRAM-based TRNGs can degrade overall system performance by slowing down concurrently-running applications due to the interference between RNG and regular memory operations in the memory controller (i.e., RNG interference), (2) this RNG interference can degrade system fairness by causing unfair prioritization of applications that intensively use random numbers (i.e., RNG applications), and (3) RNG applications can experience significant slowdown due to the high latency of DRAM-based TRNGs.To address these challenges, we propose DR-STRaNGe, an end-to-end system design for DRAM-based TRNGs that (1) reduces the RNG interference by separating RNG requests from regular memory requests in the memory controller, (2) improves fairness across applications with an RNG-aware memory request scheduler, and (3) hides the large TRNG latencies using a random number buffering mechanism combined with a new DRAM idleness predictor that accurately identifies idle DRAM periods.We evaluate DR-STRaNGe using a comprehensive set of 186 multi-programmed workloads. Compared to an RNG-oblivious baseline system, DR-STRaNGe improves the performance of non-RNG and RNG applications on average by 17.9% and 25.1%, respectively. DR-STRaNGe improves system fairness by 32.1% on average when generating random numbers at a 5 Gb/s throughput. DR-STRaNGe reduces energy consumption by 21% compared to the RNG-oblivious baseline design by reducing the time spent for RNG and non-RNG memory accesses by 15.8%.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00087"}, {"primary_key": "1669075", "vector": [], "sparse_vector": [], "title": "CoopMC: Algorithm-Architecture Co-Optimization for Markov Chain Monte Carlo Accelerators.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Bayesian machine learning is useful for applications that may make high-risk decisions with limited, noisy, or unlabeled data, as it provides great data efficiency and uncertainty estimation. Building on previous efforts, this work presents CoopMC, an algorithm-architecture co-optimization for developing more efficient MCMC-based Bayesian inference accelerators. CoopMC utilizes dynamic normalization (DyNorm), LUT-based exponential kernels (TableExp), and log-domain kernel fusion (LogFusion) to reduce computational precision and shrink ALU area by 7.5× without noticeable reduction in model performance. Also, a Tree-based Gibbs sampler (TreeSampler) improves hardware runtime from $\\mathcal{O}$(N) to $\\mathcal{O}$(log(N)), an 8.7× speedup, and yields 1.9× better area efficiency than the existing state-of-the-art Gibbs sampling architecture. These methods have been tested on 10 diverse workloads using 3 different types of Bayesian models, demonstrating applicability to many Bayesian algorithms. In an end-to-end case study, these optimizations achieve a 33% logic area reduction, 62% power reduction, and 1.53× speedup over previous state-of-the-art end-to-end MCMC accelerators.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00012"}, {"primary_key": "1669076", "vector": [], "sparse_vector": [], "title": "ReTail: Opting for Learning Simplicity to Enable QoS-Aware Power Management in the Cloud.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many cloud services have Quality-of-Service (QoS) requirements; most requests have to to complete within a given latency constraint. Recently, researchers have begun to investigate whether it is possible to meet QoS while attempting to save power on a per-request basis. Existing work shows that one can indeed hand-tune a request latency predictor offline for a particular cloud application, and consult it at runtime to modulate CPU voltage and frequency, resulting in substantial power savings.In this paper, we propose ReTail, an automated and general solution for request-level power management of latency-critical services with QoS constraints. We present a systematic process to select the features of any given application that best correlate with its request latency. ReTail uses these features to predict latency, and adjust CPU's power consumption. ReTail's predictor is trained fully at runtime. We show that unlike previous findings, simple techniques perform better than complex machine learning models, when using the right input features. For a web search engine, ReTail outperforms prior mechanisms based on complex hand-tuned predictors for that application domain. Furthermore, ReTail's systematic approach also yields superior power savings across a diverse set of cloud applications.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00020"}, {"primary_key": "1669077", "vector": [], "sparse_vector": [], "title": "PIMCloud: QoS-Aware Resource Management of Latency-Critical Applications in Clouds with Processing-in-Memory.", "authors": ["<PERSON><PERSON>", "Yi <PERSON>", "<PERSON>", "<PERSON>"], "summary": "The slowdown of <PERSON>'s Law, combined with advances in 3D stacking of logic and memory, have pushed architects to revisit the concept of processing-in-memory (PIM) to overcome the memory wall bottleneck. This PIM renaissance finds itself in a very different computing landscape from the one twenty years ago, as more and more computation shifts to the cloud. Most PIM architecture papers still focus on best-effort applications, while PIM's impact on latency-critical cloud applications is not well understood.This paper explores how datacenters can exploit PIM architectures in the context of latency-critical applications. We adopt a general-purpose cloud server with HBM-based, 3D-stacked logic+memory modules, and study the impact of PIM on six diverse interactive cloud applications. We reveal the previously neglected opportunity that PIM presents to these services, and show the importance of properly managing PIM-related resources to meet the QoS targets of interactive services and maximize resource efficiency. Then, we present PIMCloud, a QoS-aware resource manager designed for cloud systems with PIM allowing colocation of multiple latency-critical and best-effort applications. We show that PIMCloud efficiently manages PIM resources: it (1) improves effective machine utilization by up to 70% and 85% (average 24% and 33%) under 2-app and 3-app mixes, compared to the best state-of-the-art manager; (2) helps latency-critical applications meet QoS; and (3) adapts to varying load patterns.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00083"}, {"primary_key": "1669078", "vector": [], "sparse_vector": [], "title": "ReGNN: A Redundancy-Eliminated Graph Neural Networks Accelerator.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graph neural networks (GNNs), which extend conventional deep learning technologies to process graph-structured data, have shown its powerful graph representation learning ability. Existing typical GNNs utilize neighborhood message passing mechanism based on neural networks that updates target vertex representations by aggregating feature messages from neighboring source vertices. To accelerate the computations of GNNs, some customized accelerators, which follow the neighborhood aggregation computation pattern for each vertex, have been proposed. Through analysis, we observe that a naive implementation of the neighborhood aggregation results in redundant computations and communications.In this paper, we propose a novel redundancy-eliminated GNN accelerator, shortly termed as ReGNN. ReGNN is supported by an algorithm and architecture co-design. We first propose a dynamic redundancy-eliminated neighborhood message passing algorithm for GNNs. Then a novel architecture is designed to support the proposed algorithm and transform the redundancy elimination into performance improvement. ReGNN is also a configurable pipelined architecture that can be configured to support different GNN variants. In terms of the same computations, ReGNN provides the same accuracy as traditional GNNs. To the best of our knowledge, ReGNN is the first accelerator that can eliminate computation redundancy in GNNs. Our proposed ReGNN system gains an average of 9.1&#x00D7; speedup and 8.9&#x00D7; energy efficiency over state-of-the-art GNN accelerators.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00039"}, {"primary_key": "1669079", "vector": [], "sparse_vector": [], "title": "Abusing Cache Line Dirty States to Leak Information in Commercial Processors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Caches have been used to construct various types of covert and side channels to leak information. Most existing cache channels exploit the timing difference between cache hits and cache misses. However, we introduce a new and broader classification of cache covert channel attacks: Hit+Miss, Hit+Hit, and Miss+Miss. We highlight that cache misses (or cache hits) for cache lines in different states may have more significant time differences, and these can be used as timing channels. Based on this classification, we propose a new stable and stealthy Miss+Miss cache channel. Write-back caches are widely deployed in modern processors. This paper presents in detail a way in which replacement latency differences can be used to construct timing-based channels (called WB channels) to leak information in a write-back cache. Any modification to a cache line by a sender will set it to the dirty state, and the receiver can observe this through measuring the latency of replacing this cache set. We also demonstrate how senders could exploit a different number of dirty cache lines in a cache set to improve transmission bandwidth with symbols encoding multiple bits. The peak transmission bandwidths of the WB channels in commercial systems can vary between 1300 and 4400 kbps per cache set in a hyper-threaded setting without shared memory between the sender and the receiver. In contrast to most existing cache channels, which always target specific memory addresses, the new WB channels focus on the cache set and cache line states, making it difficult for the channel to be disturbed by other processes on the core, and they can still work in a cache using a random replacement policy. We also analyzed the stealthiness of WB channels from the perspective of the number of cache loads and cache miss rates. We discuss and evaluate possible defenses. The paper finishes by discussing various forms of side-channel attack.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00015"}, {"primary_key": "1669080", "vector": [], "sparse_vector": [], "title": "Only Buffer When You Need To: Reducing On-chip GPU Traffic with Reconfigurable Local Atomic Buffers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, due to their wide availability and ease of programming, GPUs have emerged as the accelerator of choice for a wide variety of applications including graph analytics and machine learning training. These applications use atomics to update shared global variables. However, since GPUs do not efficiently support atomics, this limits scalability. We propose to use hardware-software co-design to address this bottleneck and improve scalability. At the software level, we leverage recently proposed extensions to the GPU memory consistency model to identify atomic updates where the ordering can be relaxed. For example, in these algorithms the updates are commutative. At the hardware level, we propose a buffering mechanism that extends the reconfigurable local SRAM per SM. By buffering partial updates of these atomics locally, our design increases reuse, reduces atomic serialization cost, and minimizes overhead. Thus, our mechanism alleviates the impact of global atomic updates and improves performance by 28%, energy by 19%, and network traffic by 19% on average and outperforms hLRC and PHI.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00056"}, {"primary_key": "1669081", "vector": [], "sparse_vector": [], "title": "GPU Subwarp Interleaving.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Raytracing applications have naturally high thread divergence, low warp occupancy and are limited by memory latency. In this paper, we present an architectural enhancement called Subwarp Interleaving that exploits thread divergence to hide pipeline stalls in divergent sections of low warp occupancy workloads. Subwarp Interleaving allows for fine-grained interleaved execution of diverged paths within a warp with the goal of increasing hardware utilization and reducing warp latency. However, notwithstanding the promise shown by early microbenchmark studies and an average performance upside of 6.3% (up to 20%) on a simulator across a suite of raytradng application traces, the Subwarp Interleaving design feature has shortcomings that preclude its near-term implementation. This paper introduces the reader to the challenges of raytradng and discusses a novel micro-architectural approach that, on paper, addresses many of the challenges. A thorough analysis of the idea on a production simulator reveals that the high-level motivating statistics are optimistic, and second-order effects, along with other architectural sharp edges, limit the idea's potential. We identify Subwarp Interleaving's primary limiters for an NVIDIA Tbring-like architecture, and we outline the conditions under which the approach could be more effective.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00090"}, {"primary_key": "1669082", "vector": [], "sparse_vector": [], "title": "AFS: Accurate, Fast, and Scalable Error-Decoding for Fault-Tolerant Quantum Computers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Quantum computers promise computational advantages for many important problems across various application domains. Unfortunately, physical quantum devices are highly susceptible to errors that limit us from running most of these quantum applications. Quantum Error Correction (QEC) codes are required to implement Fault-Tolerant Quantum Computers (FTQC) on which computations can be performed without encountering errors. Error decoding is a critical component of quantum error correction and is responsible for transforming a set of qubit measurements generated by the QEC code, called the syndrome, into error locations and error types. For the feasibility of implementation, error decoders must not only identify errors with high accuracy, but also be fast and scalable to a large number of qubits. Unfortunately, most of the prior works on error decoding have focused primarily only on the accuracy and have relied on software implementations that are too slow to be of practical use. Furthermore, these studies only look at designing a single decoder and do not analyze the challenges involved in scaling the storage and bandwidth requirements when performing error correction in large systems with thousands of qubits.In this paper, we present AFS, an accurate, fast, and scalable decoder architecture that is designed to operate in the context of systems with hundreds of logical qubits. We present the hardware implementation of AFS, which is based on the Union Find decoding algorithm and employs a three-stage pipelined design. AFS provides orders of magnitude higher accuracy compared to recent SFQ-based hardware decoders (logical error rate of 6&#x00D7;10&#x2212;10 for physical error rate of 10&#x2212;3) and low decoding latency (42ns on average), while being robust to measurement errors introduced while extracting syndromes during the QEC cycles. We also reduce the amount of decoding hardware required to perform QEC simultaneously on all the logical qubits by co-designing the micro-architecture across multiple decoding units. Our proposed Conjoined-Decoder Architecture (CDA) reduces the storage overhead by 70% (10MB to 2.8MB). Finally, we reduce the bandwidth overheads required to transmit syndromes from the qubits to the decoders by exploiting the sparsity in the syndromes and compressing the data. Our proposed Syndrome Compression reduces the bandwidth requirement by 30x, on an average.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00027"}, {"primary_key": "1669083", "vector": [], "sparse_vector": [], "title": "Leaky Frontends: Security Vulnerabilities in Processor Frontends.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper evaluates new security threats due to the processor frontend in modern Intel processors. The root causes of the security threats are the multiple paths in the processor frontend that the micro-operations can take: through the Micro-Instruction Translation Engine (MITE), through the Decode Stream Buffer (DSB), also called the Micro-operation Cache, or through the Loop Stream Detector (LSD). Each path has its own unique timing and power signatures, which lead to the side- and covert-channel attacks presented in this work. Especially, the switching between the different paths leads to observable timing or power differences which, as this work demonstrates, could be exploited by attackers. Because of the different paths, the switching, and way the components are shared in the frontend between hardware threads, two separate threads are able to be mutually influenced and timing or power can reveal activity on the other thread. The security threats are not limited to multi-threading, and this work further demonstrates new ways for leaking execution information about SGX enclaves or a new in-domain Spectre variant in single-thread setting. Finally, this work demonstrates a new method for fingerprinting the microcode patches of the processor by analyzing the behavior of different paths in the frontend. The findings of this work highlight the security threats associated with the processor frontend and the need for deployment of defenses for the modern processor frontend.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00013"}, {"primary_key": "1669084", "vector": [], "sparse_vector": [], "title": "Direct Spatial Implementation of Sparse Matrix Multipliers for Reservoir Computing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Reservoir computing is a nascent sub-field of machine learning which relies on the recurrent multiplication of a very large, sparse, fixed matrix. We argue that direct spatial implementation of these fixed matrices minimizes the work performed in the computation, and allows for significant reduction in latency and power through constant propagation and logic minimization. Bit-serial arithmetic enables massive static matrices to be implemented. We present the structure of our bit-serial matrix multiplier, and evaluate using canonical signed digit representation to further reduce logic utilization. We have implemented these matrices on a large FPGA and provide a cost model that is simple and extensible. These FPGA implementations, on average, reduce latency by 50x up to 86x versus GPU libraries. Comparing against a recent sparse DNN accelerator, we measure a 4.1x to 47x reduction in latency depending on matrix dimension and sparsity.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00009"}, {"primary_key": "1669085", "vector": [], "sparse_vector": [], "title": "FastTrackNoC: A NoC with FastTrack Router Datapaths.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces FastTrackNoC, a Network-on-Chip (NoC) router architecture that reduces packet latency by bypassing its switch traversal (ST) stage. It is based on the observation that there is a bias in the direction a flit takes through a router, e.g., in a 2D mesh network, non-turning hops are preferred, especially when dimension order routing is used. FastTrackNoC capitalizes on this observation and adds to a 2D mesh router a fast-track path between the head of a single input virtual channel (VC) buffer and its most popular, opposite output. This allows non-turning flits to bypass ST logic, i.e., buffer-, input-and output multiplexing, when the required router resources are available. FastTrackNoC combines ST bypassing with existing techniques for reducing latency, namely, allocation bypassing, precomputed routing, and lookahead control signaling to allow at best incoming flits to proceed directly to link traversal (LT). Moreover, it is applied to a Dual Data Rate (DDR) router in order to maximize network throughput. Post place and route results in 28nm show the following: compared to previous DDR NoCs, FastTrackNoC offers 13-32% lower average packet latency; compared to previous multi-VC Single Data Rate (SDR) NoCs, FastTrackNoC reduces latency by 10-40% and achieves 18-21% higher throughput, and compared to single-channel SDR NoC offers up to 50% higher throughput and similar latency.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00075"}, {"primary_key": "1669086", "vector": [], "sparse_vector": [], "title": "SafeGuard: Reducing the Security Risk from Row-Hammer via Low-Cost Integrity Protection.", "authors": ["<PERSON>", "Yale N. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Row-Hammer (RH) is a DRAM data-disturbance failure that occurs when a row is activated frequently, which causes bit-flips in nearby rows. Row-Hammer is a significant security threat as an attacker can exploit the bit-flips to do privilege escalation and leak confidential data. While several solutions aim to mitigate RH, such solutions depend on the RH threshold and adversarial access patterns. Solutions developed for a given threshold become ineffective for newer devices with lower thresholds, and new attack patterns continue to break existing mitigations. Currently, there is no guaranteed solution for RH, which means that the system remains vulnerable to security threats even in the presence of RH mitigation.In this paper, we contend that simply relying on RH mitigation is insufficient to provide security in the presence of reducing threshold and motivated attackers. We propose SafeGuard, which equips the system with low-cost integrity protection as a defense against potential attacks that break the RH mitigation. As SafeGuard can detect arbitrary failures, it converts the problem of RH bit-flips from a security threat (silent consumption of corrupted data) to a reliability concern (detectable uncorrectable errors caused by integrity violation). We develop SafeGuard for systems that employ ECC modules and show that SafeGuard can provide strong detection to both SECDED (46-bit MAC per cache-line) and Chipkill (32-bit MAC per cache-line) while retaining the correction capability of conventional designs. SafeGuard avoids incurring any storage overheads in DRAM by simply reorganizing the ECC code to operate at a cache-line granularity (64 bytes) instead of a word granularity (8 bytes). Our evaluations show that SafeGuard has a negligible impact on both the system performance (0.7%) and the system reliability due to naturally occurring errors while still providing a strong defense against the security risk of RH by detecting arbitrary bit-flips.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00035"}, {"primary_key": "1669087", "vector": [], "sparse_vector": [], "title": "Stay in your Lane: A NoC with Low-overhead Multi-packet Bypassing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "NoCs are over-provisioned with large virtual channels to provide deadlock freedom and performance improvement. This use of virtual channels leads to considerable power and area overhead. In this paper, we introduce a novel flow control, called FastFlow, to enhance performance and avoid both protocol- and network-level deadlocks with an impressive reduction in number of virtual channels compared to the state-of-the-art NoCs. FastPass promotes a packet to traverse the network bufferlessly; the packet bypasses the routers to reach its destination. During the traversals, the packet is guaranteed to make forward progress every cycle. As a result, such a packet cannot be blocked by congestion nor deadlock. Promoting more packets to FastPass will provide higher throughput. To this end, FastPass allows multiple packets to be upgraded as FastPass packets simultaneously. To avoid any collision between these packets, FastPass provides multiple pre-defined non-overlapping lanes. Each lane is allowed to propagate only one FastPass packet. In a time-division multiplexed way, each router gets a chance to upgrade its packets to the FastPass packets and then transfer them via the pre-defined non-overlapping lanes. FastPass not only provides high throughput but also resolves both protocol-and network-level deadlocks. Compared to the state-of-the-art, FastPass presents a 1.8 &#x00D7; increase in throughput for synthetic traffic, 46% improvement in average packet latency for real applications, and 40% reduction in power and area consumption.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00074"}, {"primary_key": "1669088", "vector": [], "sparse_vector": [], "title": "CANDLES: Channel-Aware Novel Dataflow-Microarchitecture Co-Design for Low Energy Sparse Neural Network Acceleration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Several deep neural network (DNN) accelerators have been designed to exploit the sparsity exhibited by DNN activations and weights. State-of-the-art sparse accelerators can be described as either Pixel-first or Channel-first accelerators, each with its unique dataflow and compression format aiding its dataflow. The former expends significant energy updating neuron partial sums, while the latter expends significant energy in handling the index metadata. This work introduces a novel microarchitecture and dataflow that reconciles these trade-offs by adopting a Pixel-first compression and Channel-first dataflow. The proposed microarchitecture has a simpler index-generation logic combined with an accumulator buffer hierarchy and crossbar with low wiring overhead. The compression format and dataflow promote high temporal locality in neuron updates, further lowering energy. Finally, we introduce work partitions across processing elements that naturally lead to load balance without offline analysis. Compared to four state-of-the-art baselines, the proposed architecture, CANDLES, significantly outperforms three and matches the performance of the fourth. In terms of energy, CANDLES is between 2.5× and 5.6× more energy-efficient than these four baselines.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00069"}, {"primary_key": "1669089", "vector": [], "sparse_vector": [], "title": "DarkGates: A Hybrid Power-Gating Architecture to Mitigate the Performance Impact of Dark-Silicon in High Performance Processors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jisung Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To reduce the leakage power of inactive (dark) silicon components, modern processor systems shut-off these components' power supply using low-leakage transistors, called power-gates. Unfortunately, power-gates increase the system's power-delivery impedance and voltage guardband, limiting the system's maximum attainable voltage (i.e., Vmax) and, thus, the CPU core's maximum attainable frequency (i.e., Fmax). As a result, systems that are performance constrained by the CPU frequency (i.e., Fmax -constrained), such as high-end desktops, suffer significant performance loss due to power-gates.To mitigate this performance loss, we propose DarkGates, a hybrid system architecture that increases the performance of Fmax -constrained systems while fulfilling their power efficiency requirements. DarkGates is based on three key techniques: i) bypassing on-chip power-gates using package-level resources (called bypass mode), ii) extending power management firmware to support operation either in bypass mode or normal mode, and iii) introducing deeper idle power states.We implement DarkGates on an Intel Skylake microprocessor for client devices and evaluate it using a wide variety of workloads. On a real 4-core Skylake system with integrated graphics, DarkGates improves the average performance of SPEC CPU2006 workloads across all thermal design power (TDP) levels (35W&#x2013;91W) between 4.2% and 5.3%. DarkGates maintains the performance of 3DMark workloads for desktop systems with TDP greater than 45W while for a 35W-TDP (the lowest TDP) desktop it experiences only a 2% degradation. In addition, DarkGates fulfills the requirements of the ENERGY STAR and the Intel Ready Mode energy efficiency benchmarks of desktop systems.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00089"}, {"primary_key": "1669090", "vector": [], "sparse_vector": [], "title": "CAMA: Energy and Memory Efficient Automata Processing in Content-Addressable Memories.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Dai Li", "<PERSON><PERSON>"], "summary": "Accelerating finite automata processing is critical for advancing real-time analytic in pattern matching, data mining, bioinformatics, intrusion detection, and machine learning. Recent in-memory automata accelerators leveraging SRAMs and DRAMs have shown exciting improvements over conventional digital designs. However, the bit-vector representation of state transitions used by all state-of-the-art (SOTA) designs is only optimal in processing worst-case completely random patterns, while a significant amount of memory and energy is wasted in running most real-world benchmarks.We present CAMA, a Content-Addressable Memory (CAM) enabled Automata accelerator for processing homogeneous non-deterministic finite automata (NFA). A radically different state representation scheme, along with co-designed novel circuits and data encoding schemes, greatly reduces energy, memory, and chip area for most realistic NFAs. CAMA is holistically optimized with the following major contributions: (1) a 16 × 256 8-transistor (8T) CAM array for state matching, replacing the 256 × 256 6T SRAM array or two 16×256 6T SRAM banks in state-of-the-art (SOTA) designs; (2) a novel encoding scheme that enables content searching within 8T SRAMs and adapts to different applications; (3) a reconfigurable and scalable architecture that improves efficiency on all tested benchmarks, without losing support for any NFA that's compatible with SOTA designs; (4) an optimization framework that automates the choice of encoding schemes and maps a given NFA to the proposed hardware.Two versions of CAMA, one optimized for energy (CAMA-E) and the other for throughput (CAMA-T), are comprehensively evaluated in a 28nm CMOS process, and across 21 real-world and synthetic benchmarks. CAMA-E achieves 2.1×, 2.8 ×, and 2.04× lower energy than CA, 2-stride Impala, and eAP. CAMA-T shows 2.68×, 3.87× and 2.62 × higher average compute density than 2-stride Impala, CA, and eAP. Both versions reduce the chip area required for the largest tested benchmark by 2.48× over CA, 1.91× over 2-stride Impala, and 1.78× over eAP.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00011"}, {"primary_key": "1669091", "vector": [], "sparse_vector": [], "title": "Accelerating Graph Convolutional Networks Using Crossbar-based Processing-In-Memory Architectures.", "authors": ["<PERSON>", "<PERSON>", "Pengcheng Yao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jingling Xue"], "summary": "Graph convolutional networks (GCNs) are promising to enable machine learning on graphs. GCNs exhibit mixed computational kernels, involving regular neural-network-like computing and irregular graph-analytics-like processing. Existing GCN accelerators obey a divide-and-conquer philosophy to architect two separate types of hardware to accelerate these two types of GCN kernels, respectively. This hybrid architecture improves intra-kernel efficiency but considers little inter-kernel interactions in a holistic view for improving overall efficiency.In this paper, we present a new GCN accelerator, RE-FLIP, with three key innovations in terms of architecture design, algorithm mappings, and practical implementations. First, ReFlip leverages PIM-featured crossbar architectures to build a unified architecture for supporting the two types of GCN kernels simultaneously. Second, ReFlip adopts novel algorithm mappings that can maximize potential performance gains reaped from the unified architecture by exploiting the massive crossbar-structured parallelism. Third, ReFlip assembles software/hardware co-optimizations to process real-world graphs efficiently. Compared to the state-of-the-art software frameworks running on Intel Xeon E5-2680v4 CPU and NVIDIA Tesla V100 GPU, ReFlip achieves the average speedups of 6,432× and 86.32× and the average energy savings of 9,817× and 302.44×, respectively. In addition, ReFlip also outperforms a state-of-the-art GCN hardware accelerator, AWB-GCN, by achieving an average speedup of 5.06× and an average energy saving of 15.63×.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00079"}, {"primary_key": "1669092", "vector": [], "sparse_vector": [], "title": "Reducing Load Latency with Cache Level Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "High load latency that results from deep cache hierarchies and relatively slow main memory is an important limiter of single-thread performance. Data prefetch helps reduce this latency by fetching data up the hierarchy before it is requested by load instructions. However, data prefetching has shown to be imperfect in many situations. We propose cache-level prediction to complement prefetchers. Our method predicts which memory hierarchy level a load will access allowing the memory loads to start earlier, and thereby saves many cycles. The predictor provides high prediction accuracy at the cost of just one cycle added latency to L1 misses. Level prediction reduces the memory access latency by 20% on average, and provides speedup of 10.3% over a conventional baseline, and 6.1% over a boosted baseline on generic, graph, and HPC applications.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00054"}, {"primary_key": "1669093", "vector": [], "sparse_vector": [], "title": "DigiQ: A Scalable Digital Controller for Quantum Computers Using SFQ Logic.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Yunong Shi", "<PERSON><PERSON>", "<PERSON>"], "summary": "The control of cryogenic qubits in today&#x2019;s super-conducting quantum computer prototypes presents significant scalability challenges due to the massive costs of generating/routing the analog control signals that need to be sent from a classical controller at room temperature to the quantum chip inside the dilution refrigerator. Thus, researchers in industry and academia have focused on designing in-fridge classical controllers in order to mitigate these challenges. Due to the maturity of CMOS logic, many industrial efforts (Microsoft, Intel) have focused on Cryo-CMOS as a near-term solution to design in-fridge classical controllers. Meanwhile, Supercon-ducting Single Flux Quantum (SFQ) is an alternative, less mature classical logic family proposed for large-scale in-fridge controllers. SFQ logic has the potential to maximize scalability thanks to its ultra-high speed and very low power consumption. However, architecture design for SFQ logic poses challenges due to its unconventional pulse-driven nature and lack of dense memory and logic. Thus, research at the architecture level is essential to guide architects to design SFQ-based classical controllers for large-scale quantum machines.In this paper, we present DigiQ, the first system-level design of a Noisy Intermediate Scale Quantum (NISQ)-friendly SFQ-based classical controller. We perform a design space exploration of SFQ-based controllers and co-design the quantum gate decompositions and SFQ-based implementation of those decompositions to find an optimal SFQ-friendly design point that trades area and power for latency and control while ensuring good quantum algorithmic performance. Our co-design results in a single instruction, multiple data (SIMD) controller architecture, which has high scalability, but imposes new challenges on the calibration of control pulses. We present software-level solutions to address these challenges, which if unaddressed would degrade quantum circuit fidelity given the imperfections of qubit hardware.To validate and characterize DigiQ, we first implement it using hardware description languages and synthesize it using state-of-the-art/validated SFQ synthesis tools. Our synthesis results show that DigiQ can operate within the tight power and area budget of dilution refrigerators at &#x003E;42,000-qubit scales. Second, we confirm the effectiveness of DigiQ in running quantum algorithms by modeling the execution time and fidelity of a variety of NISQ applications. We hope that the promising results of this paper motivate experimentalists to further explore SFQ-based quantum controllers to realize large-scale quantum machines with maximized scalability.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00037"}, {"primary_key": "1669094", "vector": [], "sparse_vector": [], "title": "TCOR: A Tile Cache with Optimal Replacement.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cache Replacement Policies are known to have an important impact on hit rates. The OPT replacement policy [27] has been formally proven as optimal for minimizing misses. Due to its need to look far ahead for future memory accesses, it is often reduced to a yardstick for measuring the efficacy of other practical caches. In this paper, we bring the OPT to life, in architectures for mobile GPUs, for which energy efficiency is of great consequence. We also mold other factors in the memory hierarchy to enhance its impact. The end results are a 13.8% decrease in the memory hierarchy energy consumption and an increased throughput in the Tiling Engine. We also observe a 5.5% decrease in the total GPU energy and a 3.7% increase in frames per second (FPS).", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00055"}, {"primary_key": "1669095", "vector": [], "sparse_vector": [], "title": "MAGMA: An Optimization Framework for Mapping Multiple DNNs on Multiple Accelerator Cores.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As Deep Learning continues to drive a variety of applications in edge and cloud data centers, there is a growing trend towards building large accelerators with several sub-accelerator cores/chiplets. This work looks at the problem of supporting multi-tenancy on such accelerators. In particular, we focus on the problem of mapping jobs from several DNNs simultaneously on an accelerator. Given the extremely large search space, we formulate the search as an optimization problem and develop an optimization framework called M3E. In addition, we develop a specialized optimization algorithm called MAGMA with custom operators to enable structured sampleefficient exploration. We quantitatively compare MAGMA with several state-of-the-art methods, black-box optimization, and reinforcement learning methods across different accelerator settings (large/small accelerators) and different sub-accelerator configurations (homogeneous/heterogeneous), and observe MAGMA can consistently find better mappings.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00065"}, {"primary_key": "1669096", "vector": [], "sparse_vector": [], "title": "Hercules: Heterogeneity-Aware Inference Serving for At-Scale Personalized Recommendation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Hsien<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Personalized recommendation is an important class of deep-learning applications that powers a large collection of internet services and consumes a considerable amount of datacenter resources. As the scale of production-grade recommendation systems continues to grow, optimizing their serving performance and efficiency in a heterogeneous datacenter is important and can translate into infrastructure capacity saving. In this paper, we propose Hercules, an optimized framework for personalized recommendation inference serving that targets diverse industry-representative models and cloud-scale heterogeneous systems. Hercules performs a two-stage optimization procedure &#x2014; offline profiling and online serving. The first stage searches the large under-explored task scheduling space with a gradient-based search algorithm achieving up to 9.0&#x00D7; latency-bounded throughput improvement on individual servers; it also identifies the optimal heterogeneous server architecture for each recommendation workload. The second stage performs heterogeneity-aware cluster provisioning to optimize resource mapping and allocation in response to fluctuating diurnal loads. The proposed cluster scheduler in Hercules achieves 47.7% cluster capacity saving and reduces the provisioned power by 23.7% over a state-of-the-art greedy scheduler.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00019"}, {"primary_key": "1669097", "vector": [], "sparse_vector": [], "title": "DPrime+DAbort: A High-Precision and Timer-Free Directory-Based Side-Channel Attack in Non-Inclusive Cache Hierarchies using Intel TSX.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent CPUs have begun to adopt non-inclusive cache hierarchies for more effective cache utilization. Non-inclusive cache hierarchies have an additional advantage in that they eliminate the vulnerability to cache-based side-channel attacks. In addition, precise timers are often disabled or added with noise to defeat timer-based side-channel attacks. With the combination of such countermeasures, existing cache- and directory-based side-channel attacks can robustly be defeated on commodity systems.In this work, we discover the vulnerability caused by the undocumented interactions between the coherence directories and Intel TSX transactions in latest Intel CPUs with non-inclusive cache hierarchies. Guided by the observation, we propose a high-precision and timer-free directory attack called DPrime+DAbort in non-inclusive cache hierarchies using Intel TSX, which nullifies the aforementioned countermeasures. Our quantitative evaluation conducted on real systems equipped with latest Intel CPUs in three different generations demonstrates the practicality of the DPrime+DAbort attack in that it can be used to attack cryptographic and genomesequencing applications. We also discuss potential countermeasures and evaluate the feasibility of an Intel TSX-based countermeasure against the DPrime+DAbort attack.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00014"}, {"primary_key": "1669098", "vector": [], "sparse_vector": [], "title": "Exploiting Inter-block Entropy to Enhance the Compressibility of Blocks with Diverse Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jeongkyu Hong", "<PERSON><PERSON><PERSON>"], "summary": "As higher memory bandwidth is required for data-intensive environments, memory compression can be a simple but effective solution to increase memory bandwidth. However, previous intra-block compression techniques do not provide sufficient bandwidth improvement owing to the incompressibility of blocks with diverse data while previous inter-block compression techniques suffer from huge additional memory access overheads or low compression coverages. To overcome the limitations of the previous intra-and inter-block compression techniques, we leverage both the naturally observed low-entropy among blocks and the artificially generated low-entropy resulting from our optimization techniques. Based on these two low-entropies, we propose an Entropy-based Pattern Compression (EPC), which generates an inter-block pattern from the same low-entropy region in numerous blocks and then compresses these blocks by using the selected pattern. Our evaluations show that EPC achieves up to 13% (3% on average) higher speedup and 13% (4% on average) DRAM energy consumption reduction with 160x (20x on average) fewer patterns(groups) compared to the state-of-the-art inter-block compression technique.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00084"}, {"primary_key": "1669099", "vector": [], "sparse_vector": [], "title": "Mithril: Cooperative Row Hammer Protection on Commodity DRAM Leveraging Managed Refresh.", "authors": ["<PERSON>", "Jaehyun Park", "Yeonhong Park", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Since its public introduction in the mid-2010s, the Row Hammer (RH) phenomenon has drawn significant attention from the research community due to its security implications. Although many RH-protection schemes have been proposed by processor vendors, DRAM manufacturers, and academia, they still have shortcomings. Solutions implemented in the memory controller (MC) incur increasingly higher costs due to their conservative design for the worst case in terms of the number of DRAM banks and RH threshold to support. Meanwhile, DRAM-side implementation either has a limited time margin for RH-protection measures or requires extensive modifications to the standard DRAM interface. Recently, a new command for RH-protection has been introduced in the DDR5/LPDDR5 standards, referred to as refresh management (RFM). RFM enables the separation of the tasks for RH-protection to both MC and DRAM by having the former generate an RFM command at a specific activation frequency and the latter take proper RH-protection measures within a given time window. Although promising, no existing study presents and analyzes RFM-based solutions for RH-protection. In this paper, we propose Mithril, the first RFM interface-compatible, DRAM-MC cooperative RH-protection scheme providing deterministic protection guarantees. Mithril has minimal energy overheads for common use cases without adversarial memory access patterns. We also introduce Mithril+, an optional extension to provide minimal performance overheads at the expense of a tiny modification to the MC, while utilizing existing DRAM commands.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00088"}, {"primary_key": "1669100", "vector": [], "sparse_vector": [], "title": "Adaptable Register File Organization for Vector Processors.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Contemporary Vector Processors (VPs) are de-signed either for short vector lengths, e.g., Fujitsu A64FX with 512-bit ARM SVE vector support, or long vectors, e.g., NEC Aurora Tsubasa with 16Kbits Maximum Vector Length (MVL1). Unfortunately, both approaches have drawbacks. On the one hand, short vector length VP designs struggle to provide high efficiency for applications featuring long vectors with high Data Level Parallelism (DLP). On the other hand, long vector VP designs waste resources and underutilize the Vector Register File (VRF) when executing low DLP applications with short vector lengths. Therefore, those long vector VP implementations are limited to a specialized subset of applications, where relatively high DLP must be present to achieve excellent performance with high efficiency. Modern scientific applications are getting more diverse, and the vector lengths in those applications vary widely. To overcome these limitations, we propose an Adaptable Vector Architecture (AVA) that leads to having the best of both worlds. AVA is designed for short vectors (MVL=16 elements) and is thus area and energy-efficient. However, AVA has the functionality to reconfigure the MVL, thereby allowing to exploit the benefits of having a longer vector of up to 128 elements microarchitecture when abundant DLP is present. We model AVA on the gem5 simulator and evaluate AVA performance with six applications taken from the RiVEC Benchmark Suite. To obtain area and power consumption metrics, we model AVA on McPAT for 22nm technology. Our results show that by reconfiguring our small VRF (8KB) plus our novel issue queue scheme, AVA yields a 2X speedup over the default configuration for short vectors. Additionally, AVA shows competitive performance when compared to a long vector VP, while saving 50% of area.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00063"}, {"primary_key": "1669101", "vector": [], "sparse_vector": [], "title": "ANNA: Specialized Architecture for Approximate Nearest Neighbor Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Similarity search or nearest neighbor search is a task of retrieving a set of vectors in the (vector) database that are most similar to the provided query vector. It has been a key kernel for many applications for a long time. However, it is becoming especially more important in recent days as modern neural networks and machine learning models represent the semantics of images, videos, and documents as high-dimensional vectors called embeddings. Finding a set of similar embeddings for the provided query embedding is now the critical operation for modern recommender systems and semantic search engines. Since exhaustively searching for the most similar vectors out of billion vectors is such a prohibitive task, approximate nearest neighbor search (ANNS) is often utilized in many real-world use cases. Unfortunately, we find that utilizing the server-class CPUs and GPUs for the ANNS task leads to suboptimal performance and energy efficiency. To address such limitations, we propose a specialized architecture named ANNA (Approximate Nearest Neighbor search Accelerator), which is compatible with state-of-the-art ANNS algorithms such as Google ScaNN and Facebook Faiss. By combining the benefits of a specialized dataflow pipeline and efficient data reuse, ANNA achieves multiple orders of magnitude higher energy efficiency, 2.3-61.6× higher throughput, and 4.3-82.1× lower latency than the conventional CPU or GPU for both million- and billion-scale datasets.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00021"}, {"primary_key": "1669102", "vector": [], "sparse_vector": [], "title": "NeuroSync: A Scalable and Accurate Brain Simulator Using Safe and Efficient Speculation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yu<PERSON> Chung", "<PERSON><PERSON><PERSON>"], "summary": "To understand and mimic the working mechanism of the brain, neuroscientists rely on brain simulations that operate in a time-driven manner. The simulation involves evaluating how the neurons change their states over time and transferring spikes to the connected neurons through synapses. It also simulates learning by evaluating how the synapses change their weights according to the spiking activity of the neurons. To explore various behaviors of the brain and thus make great advances, neuroscientists need a methodology to support large-scale simulations in both an accurate and efficient manner. For accurate simulations, existing simulators adopt a time-precise simulation methodology where the simulator computes all the neuronal and the synaptic state changes in time order. Unfortunately, they suffer from significant underutilization and energy inefficiency as the simulator scales.In this paper, we present NeuroSync, a fast, energy-efficient, and scalable hardware-based accelerator for accurate brain simulations. The key idea is to adopt a speculative simulation methodology at a minimum overhead along with architectural support. NeuroSync achieves high efficiency using an optimal dataflow for the speculative simulations. At the same time, it ensures simulation accuracy by carefully designing a rollback and recovery mechanism to handle mis-speculations. To implement the methodology at a low cost, NeuroSync further proposes a speculation-optimal learning simulation method. Our evaluations show that 64-chip NeuroSync achieves 3.37× speedup and 3.81× higher energy efficiency with only 10.96% area overhead. The evaluations also show that NeuroSync is extremely scalable with higher speedup as the system scales.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00053"}, {"primary_key": "1669103", "vector": [], "sparse_vector": [], "title": "TNPU: Supporting Trusted Execution with Tree-less Integrity Protection for Neural Processing Unit.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Na", "Jongse Park", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As neural processing units (NPUs) for machine learning inference have been incorporated into a wide range of system-on-a-chips, NPUs are processing more and more mission-critical computations such as autonomous driving. With the increasing application scenarios, securing NPU operations from potential attacks has become crucial for the safety of the entire system. To address the security challenges of NPU operations, this study investigates how the trusted execution technology can be extended to harden the NPU execution by hardware supports. This paper proposes trusted NPU (TNPU) which supports trusted execution for NPUs integrated in a processor. For securing NPUs, a key performance challenge is in the encryption and integrity protection for external memory. This work proposes a novel tree-less integrity protection by exploiting the data flow semantics of DNN computation. The tree-less integrity protection maintains a version number for each tensor or sub-tensor inside the CPU enclave which drives the NPU computation. By exploiting the data flow of tensor updates, a per-tensor version number can efficiently verify the recency of the data in the tensor. The tree-less integrity protection eliminates performance losses by counter and hash cache misses, which are major performance overheads of hardware-based memory protection. Our evaluation with simulated NPUs shows that the performance overheads for trusted NPUs can be significantly reduced from the prior tree-based design, improving the performance of a single NPU by 10.0% and 7.5% on average over the prior one with two different NPU configurations. When the number of NPUs is increased to three, the performance gains further improve, achieving on average 13.3% and 8.7% improvements.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00025"}, {"primary_key": "1669104", "vector": [], "sparse_vector": [], "title": "Parallel Time Batching: Systolic-Array Acceleration of Sparse Spiking Neural Computation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Spiking Neural Networks (SNNs) are brain- inspired computing models incorporating unique temporal dynamics and event-driven processing. Rich dynamics in both space and time offer great challenges and opportunities for efficient processing of sparse spatiotemporal data compared with conventional artificial neural networks (ANNs). Specifically, the additional overheads for handling the added temporal dimension limit the computational capabilities of neuromorphic accelerators. Iterative processing at every time-point with sparse inputs in a temporally sequential manner not only degrades the utilization of the systolic array but also intensifies data movement.In this work, we propose a novel technique and architecture that significantly improve utilization and data movement while efficiently handling temporal sparsity of SNNs on systolic arrays. Unlike time-sequential processing in conventional SNN accelerators, we pack multiple time points into a single time window (TW) and process the computations induced by active synaptic inputs falling under several TWs in parallel, leading to the proposed parallel time batching. It allows weight reuse across multiple time points and enhances the utilization of the systolic array with reduced idling of processing elements, overcoming the irregularity of sparse firing activities. We optimize the granularity of time-domain processing, i.e., the TW size, which significantly impacts the data reuse and utilization. We further boost the utilization efficiency by simultaneously scheduling non-overlapping sparse spiking activities onto the array. The proposed architectures offer a unifying solution for general spiking neural networks with commonly exhibited temporal sparsity, a key challenge in hardware acceleration, delivering 248X energy-delay product (EDP) improvement on average compared to an SNN baseline for accelerating various networks. Compared to ANN based accelerators, our approach improves EDP by 47X on the CIFAR10 dataset.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00031"}, {"primary_key": "1669105", "vector": [], "sparse_vector": [], "title": "AI-Enabling Workloads on Large-Scale GPU-Accelerated System: Characterization, Opportunities, and Implications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Production high-performance computing (HPC) systems are adopting and integrating GPUs into their design to accommodate artificial intelligence (AI), machine learning, and data visualization workloads. To aid with the design and operations of new and existing GPU-based large-scale systems, we provide a detailed characterization of system operations, job characteristics, user behavior, and trends on a contemporary GPU-accelerated production HPC system. Our insights indicate that the pre-mature phases in modern AI workflow take up significant GPU hours while underutilizing GPUs, which opens up the opportunity for a multi-tier system. Finally, we provide various potential recommendations and areas for future investment for system architects, operators, and users.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00093"}, {"primary_key": "1669106", "vector": [], "sparse_vector": [], "title": "Using Psychophysics to Guide Power Adaptation for Input Methods on Mobile Architectures.", "authors": ["<PERSON><PERSON><PERSON>", "Shicong Hong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The predominant user activities on mobile architectures (e.g., smartphones) involve entering text in instant messaging apps, short message services, and social networking services. Recent research reveals that the normal use of input methods drains approximately half of the battery capacity due to their above-average power requirements and frequent use. In this paper, we first study the power characteristics of mobile input methods and find that they consistently over-provision resources to satisfy users. For example, the psychophysical evidence available indicates the response of spell-checking features within a time threshold makes users feel they have instant feedback. However, current systems perform it very quickly, which is imperceptible to users and costly in terms of energy use. Given this over- provisioning, the system can be slowed down to save energy while retaining the feeling of instant response. Inspired by this observation, we also exploit several other psychophysical facts to identify the exact criteria to satisfy users. As a result, we present a user experience-oriented technology, utexia, to optimize the energy use of mobile input methods. The evaluation shows that utexia conserves up to 42.9% in energy use while strictly ensuring a good user experience.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00045"}, {"primary_key": "1669107", "vector": [], "sparse_vector": [], "title": "SPACX: Silicon Photonics-based Scalable Chiplet Accelerator for DNN Inference.", "authors": ["Yuan Li", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In pursuit of higher inference accuracy, deep neural network (DNN) models have significantly increased in complexity and size. To overcome the consequent computational challenges, scalable chiplet-based accelerators have been proposed. However, data communication using metallic-based interconnects in these chiplet-based DNN accelerators is becoming a primary obstacle to performance, energy efficiency, and scalability. The photonic interconnects can provide adequate data communication support due to some superior properties like low latency, high bandwidth and energy efficiency, and ease of broadcast communication. In this paper, we propose SPACX: a Silicon Photonics-based Chiplet ACcelerator for DNN inference applications. Specifically, SPACX includes a photonic network design that enables seamless single-chiplet and cross-chiplet broadcast communications, and a tailored dataflow that promotes data broadcast and maximizes parallelism. Furthermore, we explore the broadcast granularities of the photonic network and implications on system performance and energy efficiency. A flexible bandwidth allocation scheme is also proposed to dynamically adjust communication bandwidths for different types of data. Simulation results using several DNN models show that SPACX can achieve 78% and 75% reduction in execution time and energy, respectively, as compared to other state-of-the-art chiplet-based DNN accelerators.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00066"}, {"primary_key": "1669108", "vector": [], "sparse_vector": [], "title": "unXpec: Breaking Undo-based Safe Speculation.", "authors": ["<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Speculative execution attacks exploiting speculative execution to leak secrets have aroused significant concerns in both industry and academia. They mainly exploit covert or side channels over microarchitectural states left by mis-speculated and squashed instructions (i.e., transient instructions). Most such attacks target cache states. Existing cache-based defenses against speculative execution attacks fall into two categories, Invisible and Undo. Most Invisible defenses buffer execution metadata of speculative instructions and place them into the cache only if the speculatively executed instructions become determined. Motivated by the fact that mis-speculations are rare cases, Undo defenses allow speculative instructions to modify cache states. Upon a mis-speculation, they rollback cache states to the ones prior to the execution of transient instructions. However, Invisible defenses have been recently found insecure by the speculative interference attack. This calls for a deep security inspection of Undo defenses against speculative execution attacks.In this paper, we present unXpec as the first attack against Undo-based safe speculation. It exploits the secret-dependent timing channel exhibited through the rollback operations of Undo defenses. Specifically, the rollback process requires both invalidating cache lines brought into the cache by transient instructions and restoring evicted cache lines from the cache by transiently loaded data. This opens up a channel that encodes secret via the timing difference between when rollback involves much invalidation and restoration or not. We further leverage eviction sets to enforce more restoration operations. This yields a longer rollback time and thus a larger secret-dependent timing difference. We demonstrate the timing channel over the open-source CleanupSpec, a representative Undo solution. A single transient load can trigger a secret-dependent timing difference of 22 cycles (without eviction sets) of 32 cycles (with eviction sets), which is sufficiently exploitable for constructing a covert channel for speculative execution attacks. We run unXpec on the gem5 simulator with CleanupSpec enabled. The results show that unXpec can leak secrets at a high rate of 140 Kbps with an accuracy over 90%. Simply enforcing constant-time rollback to mitigate unXpec may induce an over 70% performance overhead.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00016"}, {"primary_key": "1669109", "vector": [], "sparse_vector": [], "title": "Enabling High-Quality Uncertainty Quantification in a PIM Designed for Bayesian Neural Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Guangyu Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Meng-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Uncertainty quantification measures the prediction uncertainty of a neural network facing out-of-training-distribution samples. Bayesian Neural Networks (BNNs) can provide high-quality uncertainty quantification by introducing specific noise to the weights during inference. To accelerate BNN inference, ReRAM processing-in-memory (PIM) architecture is a competitive solution to provide both high-efficient computing and in-situ noise generation at the same time. However, there normally exists a huge gap between the generated noise in PIM hardware and that required by a BNN model. We demonstrate that the quality of uncertainty quantification is substantially degraded due to this gap. To solve this problem, we propose a holistic framework called W2W-PIM. We first introduce an efficient method to generate noise in ReRAM PIM design according to the demand of a BNN model. In addition, the PIM architecture is carefully modified to enable the noise generation and evaluate uncertainty quality. Moreover, a calibration unit is further introduced to reduce the noise gap caused by imperfection of the noise model. Comprehensive evaluation results demonstrate that W2W-PIM framework can achieve high-quality uncertainty quantification and high energy-efficiency at the same time.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00080"}, {"primary_key": "1669110", "vector": [], "sparse_vector": [], "title": "LISA: Graph Neural Network based Portable Mapping on Spatial Accelerators.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Spatial accelerators, such as Coarse-Grained Reconfigurable Arrays (CGRA), provide a promising pathway to scale the performance and power efficiency of computing systems. These accelerators depend on effective compilers to take advantage of the parallelism offered by the underlying architecture. Currently, the compilers are handcrafted for spatial accelerators, which is challenging from time to market perspective, especially with the rapid increase of diverse accelerators. In this paper, we present a portable compilation framework, called LISA, that can be tuned automatically to generate quality mapping for varied spatial accelerators. Our key contribution is to automatically identify the impact of the dataflow graph (DFG) structure characteristics (representing an application) on the mapping for a new accelerator. Towards this end, we abstract the DFG structure in graph attributes, use Graph Neural Network (GNN) to analyze the graph attributes, and identify the mapping impact for an accelerator architecture with an all-encompassing global view. Finally, we augment a simulated annealing-based mapping approach to take into account the impact of DFG structure in guiding the placement of the dataflow graph nodes and the routing of the dependencies on the accelerator. Our experimental evaluation concretely demonstrates the substantial benefit of our approach compared to the state-of-the-art solutions.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00040"}, {"primary_key": "1669111", "vector": [], "sparse_vector": [], "title": "Compiler-Driven Simulation of Reconfigurable Hardware Accelerators.", "authors": ["Z<PERSON><PERSON>g Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As customized accelerator design has become increasingly popular to keep up with the demand for high performance computing, it poses challenges for modern simulator design to adapt to such a large variety of accelerators. Existing simulators tend to two extremes: low-level and general approaches, such as RTL simulation, that can model any hardware but require substantial effort and long execution times; and higher-level application-specific models that can be much faster and easier to use but require one-off engineering effort.This work proposes a compiler-driven simulation workflow that can model configurable hardware accelerator. The key idea is to separate structure representation from simulation by developing an intermediate language that can flexibly represent a wide variety of hardware constructs. We design the Event Queue (EQueue) dialect of MLIR, a dialect that can model arbitrary hardware accelerators with explicit data movement and distributed event-based control; we also implement a generic simulation engine to model EQueue programs with hybrid MLIR dialects representing different abstraction levels. We demonstrate two case studies of EQueue-implemented accelerators: the systolic array of convolution and SIMD processors in a modern FPGA. In the former we show EQueue simulation is as accurate as a state-of-the-art simulator, while offering higher extensibility and lower iteration cost via compiler passes. In the latter we demonstrate our simulation flow can guide designer efficiently improve their design using visualizable simulation outputs.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00052"}, {"primary_key": "1669112", "vector": [], "sparse_vector": [], "title": "Not All SWAPs Have the Same Cost: A Case for Optimization-Aware Qubit Routing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhou"], "summary": "Despite rapid advances in quantum computing technologies, the qubit connectivity limitation remains to be a critical challenge. Both near-term NISQ quantum computers and relatively long-term scalable quantum architectures do not offer full connectivity. As a result, quantum circuits may not be directly executed on quantum hardware, and a quantum compiler needs to perform qubit routing to make the circuit compatible with the device layout. During the qubit routing step, the compiler inserts SWAP gates and performs circuit transformations. Given the connectivity topology of the target hardware, there are typically multiple qubit routing candidates. The state-of-the-art compilers use a cost function to evaluate the number of SWAP gates for different routes and then select the one with the minimum number of SWAP gates. After qubit routing, the quantum compiler performs gate optimizations upon the circuit with the newly inserted SWAP gates.In this paper, we observe that the aforementioned qubit routing is not optimal, and qubit routing should not be independent on subsequent gate optimizations. We find that with the consideration of gate optimizations, not all of the SWAP gates have the same basis-gate cost. These insights lead to the development of our qubit routing algorithm, NASSC (Not All Swaps have the Same Cost). NASSC is the first algorithm that considers the subsequent optimizations during the routing step. Our optimization-aware qubit routing leads to better routing decisions and benefits subsequent optimizations. We also propose a new optimization-aware decomposition for the inserted SWAP gates. Our experiments show that the routing overhead compiled with our routing algorithm is reduced by up to 69.30% (21.30% on average) in the number of CNOT gates and up to 43.50% (7.61% on average) in the circuit depth compared with the state-of-the-art scheme, SABRE.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00058"}, {"primary_key": "1669113", "vector": [], "sparse_vector": [], "title": "S2TA: Exploiting Structured Sparsity for Energy-Efficient Mobile CNN Acceleration.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Exploiting sparsity is a key technique in accelerating quantized convolutional neural network (CNN) inference on mobile devices. Prior sparse CNN accelerators largely exploit unstructured sparsity and achieve significant speedups. Due to the unbounded, largely unpredictable sparsity patterns, however, exploiting unstructured sparsity requires complicated hardware design with significant energy and area overhead, which is particularly detrimental to mobile/IoT inference scenarios where energy and area efficiency are crucial.We propose to exploit structured sparsity, more specifically, Density Bound Block (DBB) sparsity for both weights and activations. DBB block tensors bound the maximum number of non-zeros per block. DBB thus exposes statically predictable sparsity patterns that enable lean sparsity-exploiting hardware and efficient memory access. We propose new hardware primitives to implement DBB sparsity for (static) weights and (dynamic) activations, respectively, with very low overheads.Building on top of the primitives, we describe S2TA, a systolic array-based CNN accelerator that exploits joint weight and activation DBB sparsity and new dimensions of data reuse unavailable on the traditional systolic array. S2TA in 16nm achieves more than 2&#x00D7; speedup and energy reduction compared to a strong baseline of a systolic array with zero-value clock gating, over five popular CNN benchmarks. Compared to two recent non-systolic sparse accelerators, Eyeriss v2 (65nm) and SparTen (45nm), S2TA in 65nm uses about 2.2&#x00D7; per and 3.1&#x00D7; less energy inference, respectively.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00049"}, {"primary_key": "1669114", "vector": [], "sparse_vector": [], "title": "Virtual Coset Coding for Encrypted Non-Volatile Memories with Multi-Level Cells.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recently, Phase-Change Memory (PCM) has become a popular commercialized non-volatile memory (NVM), which has been deployed as a backing memory for DRAM main memory, secondary storage, or even as a DRAM main memory replacement. Like other NVMs, PCM has asymmetric access energy; writes dominate reads. When considering multilevel cells (MLC), this asymmetry can vary by an order of magnitude. Many schemes have been developed to take advantage of the asymmetric patterns of ‘0’s and ‘1’s in the data to reduce write energy. Because the memory is non-volatile, data can be recovered via physical attack or across system reboot cycles. To protect information stored in PCM against these attacks requires encryption. Unfortunately, most encryption algorithms scramble ‘0’s and ‘1’s in the data, effectively removing any patterns and negatively impacting schemes that leverage data bias and similarity to reduce write energy. In this paper, we introduce Virtual Coset Coding (VCC) as a workload-independent approach that reduces costly symbol transitions for storing encrypted data. VCC is based on two ideas. First, using coset encoding with random coset candidates, it is possible to effectively reduce the frequency of costly bit/symbol transitions when writing encrypted data. Second, a small set of random substrings can be used to achieve the same encoding efficiency as a large number of random coset candidates, but at a much lower encoding/decoding cost. Additionally, we demonstrate how VCC can be leveraged for energy reduction in combination with fault-mitigation and fault-tolerance to dramatically increase the lifetimes of endurance-limited NVMs, such as PCM. We evaluate the design of VCC and demonstrate that it can be implemented on-chip with only a nominal area overhead. VCC reduces dynamic energy by 22-28% while maintaining the same performance. Using our multi-objective optimization approach achieves at least a 36% improvement in lifetime over the state-of-the-art and at least a 50% improvement in lifetime vs. an unencoded memory, while maintaining its energy savings and system performance.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00086"}, {"primary_key": "1669115", "vector": [], "sparse_vector": [], "title": "Detecting Qubit-coupling Faults in Ion-trap Quantum Computers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Vandiver Chaplin", "<PERSON>", "<PERSON>"], "summary": "Ion-trap quantum computers offer a large number of possible qubit couplings, each of which requires individual calibration and can be misconfigured. To enhance the duty cycle of an ion trap, we develop a strategy that diagnoses individual miscalibrated couplings using only log-many tests. This strategy is validated on a commercial ion-trap quantum computer, where we illustrate the process of debugging faulty quantum gates. Our methodology provides a scalable pathway towards fault detections on a larger scale ion-trap quantum computers, confirmed by simulations up to 32 qubits.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00036"}, {"primary_key": "1669116", "vector": [], "sparse_vector": [], "title": "MULTI-CLOCK: Dynamic Tiering for Hybrid Memory Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The rapid growth of i-memory computing powered by data-intensive applications has increased demand for DRAM in servers. However, a DRAM-based system can be limiting for modern workloads because of its capacity, cost, and power consumption characteristics. Hybrid memory systems, which consist of different types of memory, such as DRAM and persistent memory, can help address many of these limitations. One promising direction that has been explored in the recent literature involves introducing persistent memory devices as a second memory tier that is directly exposed to the CPU. The resulting tiered memory design must address the fundamental challenge of placing the right data in the right memory tier at the right time while minimizing overhead. We present MULTI -CLOCK, an efficient, low-overhead hybrid memory system that relies on a unique page selection technique for tier placement. MULTl-CLOCK&#x2019;s page selection captures both page access recency and frequency, and enables moving pages to appropriate tiers at the right time within hybrid memory systems. We implemented a Linux-based, NUMA-aware version of MULTI-CLOCK that is entirely transparent and backward compatible with any existing application. Our evaluation with diverse real-world applications such as graph processing and key-value stores shows that MULTI -CLOCK can improve the average throughput by as much as 352% when compared with several state-of-the-art techniques for tiered memory.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00072"}, {"primary_key": "1669117", "vector": [], "sparse_vector": [], "title": "Reliability-Aware Runahead.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Decreasing voltage levels and continued transistor scaling have drastically increased the chance of a processor bit encountering a soft error. We find that the microarchitecture state in an out-of-order core is vulnerable to soft errors especially while waiting for data to return from memory. The severity of the problem is further aggravated by the increasingly large size of microarchitecture state with every new processor generation. Prior solutions are ineffective as they incur too high overhead in terms of chip area, energy consumption and/or performance.In this paper, we make the observation that runahead execution, which was originally conceived to improve performance, also improves soft-error reliability as an unintended side effect. While the state-of-the-art runahead technique, Precise Runahead Execution (PRE), leads to substantial performance improvements, reliability is suboptimal still. We propose Reliability-Aware Runahead (RAR) which substantially improves soft-error reliability over the current state-of-the-art by rendering the microarchitecture state non-vulnerable during runahead execution and by initiating runahead execution early. Across a set of memory-intensive applications — the primary target for runahead execution — RAR improves the mean-time-to-failure (MTTF) by on average 4.8× (and up to 35.8×) relative to an out-of-order baseline while at the same time improving performance by 33.5% on average (and up to 2.6×). Across a broader set of compute- and memory-intensive benchmarks, RAR improves MTTF by on average 2.5× while at the same time improving performance by 11.9% on average. We explore the runahead design space and conclude that RAR is the only design point that improves both reliability and performance by such a significant margin. We find that RAR is more effective for increasingly large processor architectures, making RAR an effective microarchitecture technique for future high-reliability high-performance microprocessors.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00062"}, {"primary_key": "1669118", "vector": [], "sparse_vector": [], "title": "Saving PAM4 Bus Energy with SMOREs: Sparse Multi-level Opportunistic Restricted Encodings.", "authors": ["<PERSON>;<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Pulse Amplitude Modulation (PAM) uses multiple voltage levels as different data symbols, transferring multiple bits of data simultaneously, thereby enabling higher communication bandwidth without increased operating frequencies. However, dividing the voltage into more symbols leads to a smaller voltage difference between adjacent symbols, making the interface more vulnerable to crosstalk and power noise. GDDR6X adopts four-level symbols (PAM4) with Maximum Transition Avoidance (MTA) coding, which reduces the effects of crosstalk. However, current coding approaches can consume excess energy and produce excess power noise. This paper introduces novel energy reduction techniques for PAM interfaces, specifically demonstrating them for GDDR6X PAM4. Inspired by prior work on conventional single-ended I/O interfaces, we leverage the unused idle periods in DRAM channels between data transmissions to apply longer but more energy-efficient codes. To maximize the energy savings, we build multiple sparse encoding schemes to fit different sized gaps in the DRAM traffic. These sparse encodings can provide energy reductions of up to 52% when transferring 4-bit data using a 3-symbol sequence. We evaluate these coding techniques using an NVIDIA RTX 3090 baseline, a recent GPU which uses GDDR6X with PAM4 signaling. Our evaluation shows the opportunity for large energy savings at the DRAM I/O interface (28.2% on average) over many HPC/DL applications with minimal performance degradation.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00077"}, {"primary_key": "1669119", "vector": [], "sparse_vector": [], "title": "HeteroGen: Automatic Synthesis of Heterogeneous Cache Coherence Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We solve the two challenges architects face when designing heterogeneous processors with cache coherent shared memory. First, we develop an automated tool, called HeteroGen, for composing clusters of cores, each with its own coherence protocol. Second, we show that the output of HeteroGen adheres to a precisely defined memory consistency model that we call a compound consistency model. For a wide variety of protocols—including the MOESI variants, as well as those that are targeted towards Total Store Order and Release Consistency—we show that HeteroGen can correctly fuse them. To validate HeteroGen, we develop the first litmus tests for verifying that heterogeneous protocols satisfy compound consistency models. To understand the possible performance implications of automatic protocol generation, we compared against a publicly available manually-generated heterogeneous protocol. Our results show that performance is comparable.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00061"}, {"primary_key": "1669120", "vector": [], "sparse_vector": [], "title": "NVMExplorer: A Framework for Cross-Stack Comparisons of Embedded Non-Volatile Memories.", "authors": ["Lillian Pentecost", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The current computing landscape is dominated by data-intensive applications, making data movement one of the most prominent performance bottlenecks. With repeated off-chip memory access to DRAM driving up power, and SRAM technology scaling and leakage power limiting the efficiency of embedded memories, there is a need for new memory systems that can enable denser, more energy-efficient future on-chip storage. The actively expanding field of emerging, embeddable non-volatile memory (eNVM) technologies is providing many potential candidates to satisfy this need. However, eNVM cell technologies are in vastly different stages of development and introduce distinct trade-offs in terms of density, read, write, and reliability characteristics.We present NVMExplorer (http://nvmexplorer.seas.harvard.edu/): a cross-stack design space exploration framework to compare and evaluate future on-chip memory solutions with system constraints and application-level impacts in-the-loop. This work uses NVMExplorer to evaluate eNVM-based storage for a range of application and system contexts including machine learning on the edge, graph analytics, and general purpose cache. Additionally, NVMExplorer provides an interactive and easily navigable set of data visualizations, which allow users to quickly answer their specific questions regarding eNVMs, filter according to system and application constraints, and efficiently iterate and refine the design space.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00073"}, {"primary_key": "1669121", "vector": [], "sparse_vector": [], "title": "IR-ORAM: Path Access Type Based Memory Intensity Reduction for Path-ORAM.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Path ORAM is an effective ORAM (Oblivious RAM) primitive for protecting memory access patterns. Path ORAM converts each off-chip memory request from user program to tens to hundreds of memory accesses. While several schemes have been proposed to mitigate the total number of memory accesses, Path ORAM remains a highly memory intensive primitive that leads to large memory bandwidth occupation and performance degradation.In this paper, we propose IR-ORAM to reduce the memory intensity based on path access types in Path ORAM. Path accesses in Path ORAM, while being kept oblivious to ensure privacy protection, can be categorized to three types: paths for requested data blocks, paths for position map blocks, and dummy paths. We develop a set of techniques to reduce the memory intensity of each type while ensuring the obliviousness at the same time &#x2014; we reduce the number of data blocks to access for each tree path, reduce the number of path accesses for position maps, and convert many dummy path accesses to early write-backs of dirty data in LLC. Our experimental results show that IR-ORAM achieves on average 42% performance improvement over the state-of-the-art while effectively enforcing the memory access obliviousness and the same level of security protection.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00034"}, {"primary_key": "1669122", "vector": [], "sparse_vector": [], "title": "VAQEM: A Variational Approach to Quantum Error Mitigation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Variational Quantum Algorithms (VQA) are one of the most promising candidates for near-term quantum advantage. Traditionally, these algorithms are parameterized by rotational gate angles whose values are tuned over iterative execution on quantum machines. The iterative tuning of these gate angle parameters make VQAs more robust to a quantum machine&#x2019;s noise profile. However, the effect of noise is still a significant detriment to VQA&#x2019;s target estimations on real quantum machines &#x2014; they are far from ideal. Thus, it is imperative to employ effective error mitigation strategies to improve the fidelity of these quantum algorithms on near-term machines.While existing error mitigation techniques built from theory do provide substantial gains, the disconnect between theory and real machine execution characteristics limit the scope of these improvements. Thus, it is critical to optimize mitigation techniques to explicitly suit the target application as well as the noise characteristics of the target machine.We propose VAQEM, which dynamically tailors existing error mitigation techniques to the actual, dynamic noisy execution characteristics of VQAs on a target quantum machine. We do so by tuning specific features of these mitigation techniques similar to the traditional rotation angle parameters -by targeting improvements towards a specific objective function which represents the VQA problem at hand. In this paper, we target two types of error mitigation techniques which are suited to idle times in quantum circuits: single qubit gate scheduling and the insertion of dynamical decoupling sequences. We gain substantial improvements to VQA objective measurements &#x2014; a mean of over 3x across a variety of VQA applications, run on IBM Quantum machines.More importantly, while we study two specific error mitigation techniques, the proposed variational approach is general and can be extended to many other error mitigation techniques whose specific configurations are hard to select a priori. Integrating more mitigation techniques into the VAQEM framework in the future can lead to further formidable gains, potentially realizing practically useful VQA benefits on today&#x2019;s noisy quantum machines.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00029"}, {"primary_key": "1669123", "vector": [], "sparse_vector": [], "title": "LoopPoint: Checkpoint-driven Sampled Simulation for Multi-threaded Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Generic multi-threaded sampled simulation has been a long-standing, challenging problem with the potential to help change how researchers study modern, complex computing systems. Yet, a practical solution for reducing complex multi-threaded applications into a representative sample has been elusive. Existing techniques either do not provide significant speedups to be useful (Time-based Sampling techniques can show less than a 10× speedup compared to a fully-detailed simulation) or apply only to particular synchronization types (BarrierPoint for barrier-based workloads). In addition, workload-specific solutions can be rigid with respect to region selection, which can limit the overall simulation speedup when regions are large. A solution is needed that both supports generic multi-threaded applications, no matter the synchronization primitives used, as well as allows for ease of deployment and fast evaluation.In this work, we aim to solve these challenges and propose a novel sampling technique for multi-threaded applications, called LoopPoint, that is both agnostic to the type of synchronization primitives used and scales by the similarity exhibited by the application. The proposed methodology combines several vital features, including (1) repeatable, up-front application analysis, (2) a novel clustering approach to take into account run-time parallelism, and (3) the use of loop-based simulation markers to divide the work into measurable chunks, even in the presence of spin-loops. LoopPoint identifies representative simulation regions that can be simulated in parallel to achieve speedups of up to 801× for the train input set of the multi-threaded SPEC CPU2017 benchmarks with an average simulation error of just 2.33%. For the ref inputs of CPU2017, we calculate the speedup with LoopPoint to be 11,587× on average (for parallel simulation), and up to 31,253×, demonstrating how the identification of application regularity and loops can lead to significant simulation improvements compared to state-of-the-art solutions.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00051"}, {"primary_key": "1669124", "vector": [], "sparse_vector": [], "title": "Effective Mimicry of Belady&apos;s MIN Policy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The past decade has seen the rise of highly successful cache replacement policies that are based on binary prediction. For example, the Hawkeye policy learns whether lines loaded by a given PC are Cache Friendly (likely to remain in the cache if Belady&#x2019;s MIN policy had been used) or Cache Averse (likely to be evicted by Belady&#x2019;s MIN policy). In this paper, we instead present a cache replacement policy that is based on multiclass prediction, which allows it to directly mimic Belady&#x2019;s MIN policy in a surprisingly simple and effective way. Our policy uses a PC-based predictor to learn each cache line&#x2019;s reuse distance; it then evicts lines based on their predicted time of reuse. We show that our use of multiclass prediction is more effective than binary prediction because it allows for a finer-grained ordering of cache lines during eviction and because it is more robust to prediction errors.Our empirical results show that our new policy, which we refer to as Mockingjay, outperforms the previous state-of-the-art on both single-core and multi-core platforms and both with and without a prefetcher. For example, with no prefetcher, on a mix of 100 multi-core workloads from the SPEC 2006, SPEC 2017, and GAP benchmark suites, Mockingjay sees an average improvement over LRU of 15.2%, compared to 7.6% for SHiP and 12.9% for Hawkeye. On a single-core platform, Mockingjay&#x2019;s improvement over LRU is 5.7%, which approaches the 6.0% improvement of Belady MIN&#x2019;s unrealizable policy. On a single-core platform (with a prefetcher) running the high-MPKI CVP workloads, Mockingjay&#x2019;s improvement over LRU is 20.1%, compared to 13.4% for Hawkeye.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00048"}, {"primary_key": "1669125", "vector": [], "sparse_vector": [], "title": "HD-CPS: Hardware-assisted Drift-aware Concurrent Priority Scheduler for Shared Memory Multicores.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Efficiently exploiting parallelism remains a challenging problem in multicore processors. For many algorithms, executing tasks in some priority order results in a work-efficient execution. However, searching high-priority tasks requires communication that hampers performance. A concurrent priority scheduler (CPS) selects high-priority tasks and schedules them on different cores. Modern CPS designs offer various strategies to select high-priority tasks at low communication cost for improved performance. However, they do not explicitly track the priority of tasks and cannot adjust task distribution if the cores are processing low-priority tasks. Moreover, they cannot estimate the right amount of communication required to select high-priority tasks. This paper critically observes that the cores' priority drift can be quantified and used for better performance. A novel CPS design, HD-CPS is proposed to use priority as a signal to optimize drift and communication at runtime. Furthermore, compute-intensive task transfer and processing aspects of the CPS are offloaded to per-core local hardware at a low cost to enhance performance. HD-CPS is shown to consistently improve performance over several state-of-the-art software-based and hardware-assisted CPS designs. With hardware-assist, it approaches near-linear performance scaling as a function of core counts for large shared-memory multicores.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00046"}, {"primary_key": "1669126", "vector": [], "sparse_vector": [], "title": "The Specialized High-Performance Network on Anton 3.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "Molecular dynamics (MD) simulation, a computationally intensive method that provides invaluable insights into the behavior of biomolecules, typically requires large-scale parallelization. Implementation of fast parallel MD simulation demands both high bandwidth and low latency for inter-node communication, but in current semiconductor technology, neither of these properties is scaling as quickly as intra-node computational capacity. This disparity in scaling necessitates architectural innovations to maximize the utilization of computational units. For Anton 3, the latest in a family of highly successful special-purpose supercomputers designed for MD simulations, we thus designed and built a completely new specialized network as part of our ASIC. Tightly integrating this network with specialized computation pipelines enables Anton 3 to perform simulations orders of magnitude faster than any general-purpose supercomputer, and to outperform its predecessor, Anton 2 (the state of the art prior to Anton 3), by an order of magnitude. In this paper, we present the three key features of the network that contribute to the high performance of Anton 3. First, through architectural optimizations, the network achieves very low end-to-end inter-node communication latency for fine-grained messages, allowing for better overlap of computation and communication. Second, novel application-specific compression techniques reduce the size of most messages sent between nodes, thereby increasing effective inter-node bandwidth. Lastly, a new hardware synchronization primitive, called a network fence, supports fast fine-grained synchronization tailored to the data flow within a parallel MD application. These application-driven specializations to the network are critical for Anton 3&#x2019;s MD simulation performance advantage over all other machines.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00092"}, {"primary_key": "1669127", "vector": [], "sparse_vector": [], "title": "<PERSON>: Rethinking Sparse Optimization for Deep Learning Architectures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper examines the design space trade-offs of DNNs accelerators aiming to achieve competitive performance and efficiency metrics for all four combinations of dense or sparse activation/weight tensors. To do so, we systematically examine the overheads of supporting sparsity on top of an optimized dense core. These overheads are modeled based on parameters that indicate how a multiplier can borrow a nonzero operation from the neighboring multipliers or future cycles. As a result of this exploration, we identify a few promising designs that perform better than prior work. Our findings suggest that even the best design targeting dual sparsity yields a 20%-30% drop in power efficiency when performing on single sparse models, i.e., those with only sparse weight or sparse activation tensors. We found that one can reuse resources of the same core to maintain high performance and efficiency when running single sparsity or dense models. We call this hybrid architecture Griffin. Griffin is 1.2, 3.0, 3.1, and 1.4x more power-efficient than state-of-the-art sparse architectures, for dense, weight-only sparse, activation-only sparse, and dual sparse models, respectively.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00068"}, {"primary_key": "1669128", "vector": [], "sparse_vector": [], "title": "RM-SSD: In-Storage Computing for Large-Scale Recommendation Inference.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "To meet the strict service level agreement requirements of recommendation systems, the entire set of embeddings in recommendation systems needs to be loaded into the memory. However, as the model and dataset for production-scale recommendation systems scale up, the size of the embeddings is approaching the limit of memory capacity. Limited physical memory constrains the algorithms that can be trained and deployed, posing a severe challenge for deploying advanced recommendation systems. Recent studies offload the embedding lookups into SSDs, which targets the embedding-dominated recommendation models. This paper takes it one step further and proposes to offload the entire recommendation system into SSD with in-storage computing capability. The proposed SSD-side FPGA solution leverages a low-end FPGA to speed up both the embedding-dominated and MLP-dominated models with high resource efficiency. We evaluate the performance of the proposed solution with a prototype SSD. Results show that we can achieve 20-100× throughput improvement compared with the baseline SSD and 1.5-15× improvement compared with the state-of-art.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00081"}, {"primary_key": "1669129", "vector": [], "sparse_vector": [], "title": "DRIPS: Dynamic Rebalancing of Pipelined Streaming Applications on CGRAs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Coarse-grained reconfigurable arrays (CGRAs) provide higher flexibility than application-specific integrated circuits (ASICs) and higher efficiency than fine-grained reconfigurable devices such as Field Programmable Gate Arrays (FPGAs). However, CGRAs are generally designed to support offloading of a single kernel. While the CGRA design, based on communicating functional units, appears to naturally suit data streaming applications composed of multiple cooperating kernels, current approaches only statically partition the resources across application kernels. However, emerging streaming applications at the edge (scientific instruments, sensor networks, network processing) perform much more than digital signal processing and often are data and input dependent. This leads to extremely variable kernel execution times, severely impacting the throughput of the entire pipeline if resources are only statically allocated. Therefore, in this paper, we propose DRIPS — a novel CGRA architecture that can dynamically rebalance the pipeline of data-dependent streaming applications. We present a unified compiler framework to facilitate the mapping of a given streaming application onto the DRIPS CGRA architecture. The experimental results show that DRIPS achieves an average throughput improvement of 1.46× across a set of representative applications over a statically partitioned solution. The additional area overhead to enable dynamic rebalancing consumes 16.34% of the entire area for a 5×5 CGRA prototype.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00030"}, {"primary_key": "1669130", "vector": [], "sparse_vector": [], "title": "SupermarQ: A Scalable Quantum Benchmark Suite.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Victory Omole", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The emergence of quantum computers as a new computational paradigm has been accompanied by speculation concerning the scope and timeline of their anticipated revolutionary changes. While quantum computing is still in its infancy, the variety of different architectures used to implement quantum computations make it difficult to reliably measure and compare performance. This problem motivates our introduction of SupermarQ, a scalable, hardware-agnostic quantum benchmark suite which uses application-level metrics to measure performance. SupermarQ is the first attempt to systematically apply techniques from classical benchmarking methodology to the quantum domain. We define a set of feature vectors to quantify coverage, select applications from a variety of domains to ensure the suite is representative of real workloads, and collect benchmark results from the IBM, IonQ, and AQT@LBNL platforms. Looking forward, we envision that quantum benchmarking will encompass a large cross-community effort built on open source, constantly evolving benchmark suites. We introduce SupermarQ as an important step in this direction.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00050"}, {"primary_key": "1669131", "vector": [], "sparse_vector": [], "title": "QULATIS: A Quantum Error Correction Methodology toward Lattice Surgery.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the high error rate of a qubit, detecting and correcting errors on it is essential for fault-tolerant quantum computing (FTQC). Surface code (SC) associated with its decoding algorithm is one of the most promising quantum error correction (QEC) methods because it has high fidelity and requires only nearest neighbor qubits connectivity. To realize FTQC, we need a decoder circuit capable of not only QEC in a 3-D lattice to deal with errors in measurement on ancillary qubits but also quantum operations on logically constructed qubits. Whereas several methods to perform logical operations on SC, such as lattice surgery (LS), are known, no practical decoders supporting them have been proposed yet.One of the most promising QC implementations today is made up of superconducting qubits that are located in a cryogenic environment. To reduce the hardware complexity of QC and latency of QEC, we are supposed to perform QEC in a cryogenic environment. Hence a power-efficient decoder is required due to the limited power budget inside a dilution refrigerator.In this paper, we propose an online-QEC algorithm that supports LS with a practical decoder circuit, as well as a new FTQC architecture. We design a key building block of the proposed architecture with a hybrid of SFQ- and Cryo-CMOS-based digital circuits and evaluate it with a SPICE-level simulation. Each logic element includes about 2400 Josephson junctions, and power consumption is estimated to be 2.07 μW when operating with a 2 GHz clock frequency. We evaluate the decoder performance by a quantum error simulator for an essential operation of LS with code distances up to 11, and it achieves a 0.6% accuracy threshold. In an LS-based architecture further supporting a magic-state distillation protocol, which is expected to run for near-term universal quantum computing, we evaluate the QEC performance and power consumption of the architecture and show that it is practical to be operated in 4-K temperature region of a dilution refrigerator.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00028"}, {"primary_key": "1669132", "vector": [], "sparse_vector": [], "title": "QuantumNAS: Noise-Adaptive Search for Robust Quantum Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Quantum noise is the key challenge in Noisy Intermediate-Scale Quantum (NISQ) computers. Previous work for mitigating noise has primarily focused on gate-level or pulse-level noise-adaptive compilation. However, limited research has explored a higher level of optimization by making the quantum circuits themselves resilient to noise.In this paper, we propose QuantumNAS, a comprehensive framework for noise-adaptive co-search of the variational circuit and qubit mapping. Variational quantum circuits are a promising approach for constructing quantum neural networks for machine learning and variational ansatzes for quantum simulation. However, finding the best variational circuit and its optimal parameters is challenging due to the large design space and parameter training cost. We propose to decouple the circuit search from parameter training by introducing a novel SuperCircuit. The SuperCircuit is constructed with multiple layers of pre-defined parameterized gates (e.g., U3 and CU3) and trained by iteratively sampling and updating the parameter subsets (SubCircuits) of it. It provides an accurate estimation of SubCircuits performance trained from scratch. Then we perform an evolutionary co-search of SubCircuit and its qubit mapping. The SubCircuit performance is estimated with parameters inherited from SuperCircuit and simulated with real device noise models. Finally, we perform iterative gate pruning and finetuning to remove redundant gates in a fine-grained manner.Extensively evaluated with 12 quantum machine learning (QML) and variational quantum eigensolver (VQE) benchmarks on 14 quantum computers, QuantumNAS significantly outperforms noise-unaware search, human, random, and existing noise-adaptive qubit mapping baselines. For QML tasks, QuantumNAS is the first to demonstrate over 95% 2-class, 85% 4-class, and 32% 10-class classification accuracy on real quantum computers. It also achieves the lowest eigenvalue for VQE tasks on H2, H2O, LiH, CH4, BeH2 compared with UCCSD baselines. We also open-source the TorchQuantum library for fast training of parameterized quantum circuits to facilitate future research.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00057"}, {"primary_key": "1669133", "vector": [], "sparse_vector": [], "title": "Application Defined On-chip Networks for Heterogeneous Chiplets: An Implementation Perspective.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Shaolin Xiang", "<PERSON>", "<PERSON>"], "summary": "With the help of advanced packaging technologies to integrate multiple chips (e.g., CPU, AI, IO), a chiplet-based SoC design process can enable fast system construction. However, the design of network-on-chip used within the individual chiplets and across chiplets is an extremly challenge. We introduce the design process and methodology of a bufferless multi-ring NoC for heterogeneous chiplet-based SoC. Our design is portable and can be used in diverse scenarios, like Server-CPU, AI-Processor, and Baseband-Processor.The co-design of the application, architecture, and implementation is the key to make the system power efficient and high performance. We determined many architectural design choices by reflecting an analysis of a set of target applications by application teams and several physical implementation constraints provided by development teams. In this paper, we present the pragmatic practice of our co-design effort for the NoC. As a result, the system has been proven to achieve 16TB/s bandwidth in an AI processor and low latency, in a server CPU with nearly one hundred cores.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00091"}, {"primary_key": "1669134", "vector": [], "sparse_vector": [], "title": "Enabling Efficient Large-Scale Deep Learning Training with Cache Coherent Disaggregated Memory Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Joonseop <PERSON>", "Euicheol Lim", "<PERSON><PERSON><PERSON>"], "summary": "Modern deep learning (DL) training is memory-consuming, constrained by the memory capacity of each computation component and cross-device communication bandwidth. In response to such constraints, current approaches include increasing parallelism in distributed training and optimizing inter-device communication. However, model parameter communication is becoming a key performance bottleneck in distributed DL training. To improve parameter communication performance, we propose COARSE, a disaggregated memory extension for distributed DL training. COARSE is built on modern cache-coherent interconnect (CCI) protocols and MPI-like collective communication for synchronization, to allow low-latency and parallel access to training data and model parameters shared among worker GPUs. To enable high bandwidth transfers between GPUs and the disaggregated memory system, we propose a decentralized parameter communication scheme to decouple and localize parameter synchronization traffic. Furthermore, we propose dynamic tensor routing and partitioning to fully utilize the non-uniform serial bus bandwidth varied across different cloud computing systems. Finally, we design a deadlock avoidance and dual synchronization to ensure high-performance parameter synchronization. Our evaluation shows that COARSE achieves up to 48.3% faster DL training compared to the state-of-the-art MPI AllReduce communication.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00018"}, {"primary_key": "1669135", "vector": [], "sparse_vector": [], "title": "Near-Stream Computing: General and Transparent Near-Cache Acceleration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Data movement and communication have become the primary bottlenecks in large multicore systems. The near-data computing paradigm provides a solution: move computation to where the data resides on-chip. Two challenges keep near-data computing from the mainstream: lack of programmer transparency and applicability. Programmer transparency requires providing sequential memory semantics with distributed computation, which requires burdensome coordination. Broad applicability requires support for combinations of address patterns (e.g. affine, indirect, multi-operand) and computation types (loads, stores, reductions, atomics).We find that streams – coarse grain memory access patterns – are a powerful ISA abstraction for near data offloading. Tracking data access at stream-granularity heavily reduces the burden of coordination for providing sequential semantics. Decomposing the problem using streams means that arbitrary combinations of address and computation patterns can be combined for broad generality.With this insight, we develop a paradigm called near-stream computing, comprising a compiler, CPU ISA extension, and a microarchitecture that facilitate programmer transparent computation offloading to shared caches. We evaluate our system on OpenMP kernels that stress broad addressing and compute behavior, and find that 46% of dynamic instructions can be offloaded to remote banks, reducing the network traffic by 76%. Overall it achieves 2.13× speedup over a state-of-the-art near-data computing technique, with a 1.90× energy efficiency gain.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00032"}, {"primary_key": "1669136", "vector": [], "sparse_vector": [], "title": "Hardware-Accelerated Hypergraph Processing with Chain-Driven Scheduling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Pengcheng Yao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Beyond ordinary graphs, hypergraphs are a graph representation to flexibly express complex multilateral relationships between entities. Hypergraph processing can be used to solve many real-world problems, e.g., machine learning, VLSI design, and image retrieval. Existing hypergraph processing systems handle a hypergraph in order of its hyperedge and vertex indices. This makes processing hypergraphs on generalpurpose architectures suffer significantly from excessive offchip memory accesses, most of which however are redundant in frequently accessing overlapped hyperedges and vertices, but the index-ordered scheduling destroys this potential locality.In this paper, we propose a novel Generate-Load-Apply (GLA) execution model to improve locality in hypergraph processing. The key insight of GLA is to use a concept of chain to characterize the overlapped feature of a hypergraph, exposing data reuse opportunities missed in existing hypergraph systems. The precondition of driving GLA model is to generate expected chains on the fly, but the software solution is so expensive that its overheads may outweigh the benefits achieved from the chain-driven scheduling. We further present ChGraph, the first hardware-accelerated hypergraph processing engine near each core. ChGraph is specialized in accelerating the chain generation and the chain-guided data loading (to hide memory access latency) while the general-purpose cores are responsible only for handling the apply operations of GLA. We evaluate ChGraph against a state-of-the-art hypergraph processing system Hygra on six hypergraph algorithms using five large real-world hypergraphs. Results on a simulated 16core system show that ChGraph reduces the number of offchip memory accesses by up to 4.56× and achieves up to 4.73× speedup while introducing only 0.26% area overhead.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00022"}, {"primary_key": "1669137", "vector": [], "sparse_vector": [], "title": "uSystolic: Byte-Crawling Unary Systolic Array.", "authors": ["<PERSON>", "<PERSON>"], "summary": "General matrix multiply (GEMM) is an important operation in broad applications, especially the thriving deep neural networks. To achieve low power consumption for GEMM, researchers have already leveraged unary computing, which manipulates bitstreams with extremely simple logic. However, existing unary architectures are not well generalizable to varying GEMM configurations in versatile applications and incompatible to the binary computing stack, imposing challenges to execute unary GEMM effortlessly. In this work, we address the problem by architecting a hybrid unary-binary systolic array, uSystolic, to inherit the legacy-binary data scheduling with slow (thus power-efficient) data movement, i.e., data bytes are crawling out from memory to drive uSystolic. uSystolic exhibits tremendous area and power improvements as a joint effect of 1) low-power computing kernel, 2) spatial-temporal bitstream reuse, and 3) on-chip SRAM elimination. For the evaluated edge computing scenario, compared with the binary parallel design, the rated-coded uSystolic reduces the systolic array area and total on-chip area by 59.0% and 91.3%, with the on-chip energy and power efficiency improved by up to 112.2× and 44.8× for AlexNet.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00010"}, {"primary_key": "1669138", "vector": [], "sparse_vector": [], "title": "Upward Packet Popup for Deadlock Freedom in Modular Chiplet-Based Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Jiang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Monolithic SoCs can be decomposed into disparate chiplets that support integration with advanced pack-aging technologies. This concept is promising in reducing the manufacturing cost of large scale SoCs due to the higher yield rate and reusability of chiplets. The chiplets should be designed in a modular manner without holistic system knowledge so that they can be reused in different SoCs. However, the design modularity is a major challenge to the networks-on-chip (NoCs) of chiplets.New deadlocks may occur across both the chiplets and the interposer due to the integration, even if the NoC of each individually designed chiplet is deadlock free. However, conventional deadlock freedom approaches are unsuitable to handle such deadlocks because they require holistic knowledge and violate the modularity. Although there are several modular approaches that specifically target at integration-induced deadlocks, their routing is overly restricted and the injection control incurs additional latency. They also lack flexibility in dynamically changing topologies due to their complex software algorithm and the hard-wired components.In this paper, a key insight on the chiplet integration-induced deadlocks is gained, inspired by which a deadlock recovery framework (named UPP) is proposed. Specifically, it is verified that an integration-induced deadlock always involves a stalled upward packet moving from the interposer to the connected chiplet via the vertical link. Thus, UPP detects a deadlock by discovering the upward packet and recovers the system from deadlock by transmitting the upward packet to its destination. Hybrid flow control mechanisms are proposed to enable the upward packet to bypass the buffers and be transmitted via the normal router datapath. To guarantee the ejection of the upward packet after transmission, a lightweight protocol is proposed to reserve ejection queue entries of the network interface. Experimental results show that while adhering to design modularity, UPP provides an average runtime speedup of 3.1%&#x223C;10.3% with an area overhead of less than 4%.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00076"}, {"primary_key": "1669139", "vector": [], "sparse_vector": [], "title": "SecNDP: Secure Near-Data Processing with Untrusted Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hsien<PERSON><PERSON><PERSON>"], "summary": "Today's data-intensive applications increasingly suffer from significant performance bottlenecks due to the limited memory bandwidth of the classical von <PERSON> architecture. Near-Data Processing (NDP) has been proposed to perform computation near memory or data storage to reduce data movement for improving performance and energy consumption. However, the untrusted NDP processing units (PUs) bring in new threats to workloads that are private and sensitive, such as private database queries and private machine learning inferences. Meanwhile, most existing secure hardware designs do not consider off-chip components trustworthy. Once data leaving the processor, they must be protected, e.g., via block cipher encryption. Unfortunately, current encryption schemes do not support computation over encrypted data stored in memory or storage, hindering the adoption of NDP techniques for sensitive workloads.In this paper, we propose SecNDP, a lightweight encryption and verification scheme for untrusted NDP devices to perform computation over ciphertext and verify the correctness of linear operations. Our encryption scheme leverages arithmetic secret sharing in secure Multi-Party Computation (MPC) to support operations over ciphertext, and uses counter-mode encryption to reduce the decryption latency. The security of the encryption and verification algorithm is formally proven. Compared with a non-NDP baseline, secure computation with SecNDP significantly reduces the memory bandwidth usage while providing security guarantees. We evaluate SecNDP for two workloads of distinct memory access patterns. In the setting of eight NDP units, we show a speedup up to 7.46× and energy savings of 18% over an unprotected non-NDP baseline, approaching the performance gain attained by native NDP without protection. Furthermore, SecNDP does not require any security assumption on NDP to hold, thus, using the same threat model as existing secure processors. SecNDP can be implemented without changing the NDP protocols and their inherent hardware design.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00026"}, {"primary_key": "1669140", "vector": [], "sparse_vector": [], "title": "Temporal Exposure Reduction Protection for Persistent Memory.", "authors": ["<PERSON><PERSON><PERSON>", "Chencheng Ye", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The long-living nature and byte-addressability of persistent memory (PM) amplifies the importance of strong memory protections. This paper develops temporal exposure reduction protection (TERP) as a framework for enforcing memory safety. Aiming to minimize the time when a PM region is accessible, TERP offers a complementary dimension of memory protection. The paper gives a formal definition of TERP, explores the semantics space of TERP constructs, and the relations with security and composability in both sequential and parallel executions. It proposes programming system and architecture solutions for the key challenges for the adoption of TERP, which draws on novel supports in both compilers and hardware to efficiently meet the exposure time target. Experiments validate the efficacy of the proposed support of TERP, in both efficiency and exposure time minimization.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00071"}, {"primary_key": "1669141", "vector": [], "sparse_vector": [], "title": "ASAP: A Speculative Approach to Persistence.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Persistent memory enables a new class of applications that have persistent in-memory data structures. Recoverability of these applications imposes constraints on the ordering of writes to persistent memory. But, the cache hierarchy and memory controllers in modern systems may reorder writes to persistent memory. Therefore, programmers have to use expensive flush and fence instructions that stall the processor to enforce such ordering. While prior efforts circumvent stalling on long latency flush instructions, these designs under-perform in large-scale systems with many cores and multiple memory controllers.We propose ASAP, an architectural model in which the hardware takes an optimistic approach by persisting data eagerly, thereby avoiding any ordering stalls and utilizing the total system bandwidth efficiently. ASAP avoids stalling by allowing writes to be persisted out-of-order, speculating that all writes will eventually be persisted. For correctness, ASAP saves recovery information in the memory controllers which is used to undo the effects of speculative writes to memory in the event of a crash.Over a large number of representative workloads, ASAP improves performance over current Intel systems by 2.3 on average and performs within 3.9% of an ideal system.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00070"}, {"primary_key": "1669142", "vector": [], "sparse_vector": [], "title": "ScalaGraph: A Scalable Accelerator for Massively Parallel Graph Processing.", "authors": ["Pengcheng Yao", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jingling Xue"], "summary": "Graph processing is promising to extract valuable insights in graphs. Nowadays, emerging 3D-stacked memories and silicon technologies can provide over terabytes per second memory bandwidth and thousands of processing elements (PEs) to meet the high hardware demand of graph applications. However, this leap in hardware capability does not result in a huge increase but even a degradation sometimes in performance for graph processing. In this paper, we discover that the centralized on-chip memory hierarchy adopted in existing graph accelerators is the villain causing poor scalability due to its quadratic increase of hardware overheads with respect to the number of PEs.We present a novel distributed on-chip memory hierarchy by leveraging the network-on-chip (NoC) to enable massively parallel graph processing. We architect ScalaGraph, a brand new graph processing accelerator, to exploit this insight. ScalaGraph adopts a software-hardware co-design to minimize NoC communication overheads via an efficient row-oriented dataflow mapping and runtime aggregation. A specialized scheduling mechanism is also proposed to improve load imbalance. Our results on a Xilinx Alveo U280 FPGA card show that ScalaGraph on a modest configuration of 512 PEs achieves 2.2&#x00D7; and 3.2&#x00D7; speedups over a state-of-theart graph accelerator GraphDyns and a GPU-based graph system Gunrock, respectively. Moreover, ScalaGraph enables supporting at least 1,024 PEs with nearly linear performance scaling while GraphDyns fails to work.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00023"}, {"primary_key": "1669143", "vector": [], "sparse_vector": [], "title": "ScaleHLS: A New Scalable High-Level Synthesis Framework on Multi-Level Intermediate Representation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "High-level synthesis (HLS) has been widely adopted as it significantly improves the hardware design productivity and enables efficient design space exploration (DSE). Existing HLS tools are built using compiler infrastructures largely based on a single-level abstraction, such as LLVM. How-ever, as HLS designs typically come with intrinsic structural or functional hierarchies, different HLS optimization problems are often better solved with different levels of abstractions. This paper proposes ScaleHLS 1, a new scalable and customizable HLS framework, on top of a multi-level compiler infrastructure called MLIR. ScaleHLS represents HLS designs at multiple representation levels and provides an HLS-dedicated analysis and transform library to solve the optimization problems at the suitable levels. Using this library, we provide a DSE engine to generate optimized HLS designs automatically. In addition, we develop an HLS C front-end and a C/C++ emission back-end to translate HLS designs into/from MLIR for enabling an end-to-end compilation flow. Experimental results show that, comparing to the baseline designs without manual directives insertion and code-rewriting, that are only optimized by Xilinx Vivado HLS, ScaleHLS improves the performances with amazing quality-of-results &#x2013; up to 768.1&#x00D7; better on computation kernel level programs and up to 3825.0&#x00D7; better on neural network models.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00060"}, {"primary_key": "1669144", "vector": [], "sparse_vector": [], "title": "Efficient Bad Block Management with Cluster Similarity.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>sian<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Process variation in the 3D flash memory architecture raises the difficulty of bad block management. Since the error characteristics vary among different blocks, it is difficult for the existing P/E cycle-based bad block management policies to decide a suitable cycle threshold. This increases the possibility of data loss and decreases the SSD's lifetime. In this work, we characterize the 3D flash memory and observe spatial correlation among flash blocks in the aspect of error behaviors. This phenomenon is referred to as cluster similarity. A novel cluster-based bad block management policy is proposed, which treats the failure of a block as an indicator of near-future failures of its neighboring blocks. Moreover, we provide quantitative methods to enable judicious selection of the cluster size to meet the desired tradeoff between the SSD lifetime and reliability. Compared with the commonly-used cycle-based bad block management policy, our cluster-based management policy has a lifetime improvement of 2x with comparable failure rates. And with comparable lifetime, the failure rate of the cycle-based policy is 9x higher than our method. To alleviate the I/O performance impact caused by the cluster retirement, we proposes a critical-block first reallocation scheduling. Our experiments show up to two times improvement of the 95th percentile latency compared to the naive scheduling of cluster reallocation.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00044"}, {"primary_key": "1669145", "vector": [], "sparse_vector": [], "title": "GCoD: Graph Convolutional Network Acceleration via Dedicated Algorithm and Accelerator Co-Design.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Graph Convolutional Networks (GCNs) have emerged as the state-of-the-art graph learning model. However, it can be notoriously challenging to inference GCNs over large graph datasets, limiting their application to large real-world graphs and hindering the exploration of deeper and more sophisticated GCN graphs. This is because real-world graphs can be extremely large and sparse. Furthermore, the node degree of GCNs tends to follow the power-law distribution and therefore have highly irregular adjacency matrices, resulting in prohibitive inefficiencies in both data processing and movement and thus substantially limiting the achievable GCN acceleration efficiency. To this end, this paper proposes a GCN algorithm and accelerator Co-Design framework dubbed GCoD which can largely alleviate the aforementioned GCN irregularity and boost GCNs&#x2019; inference efficiency. Specifically, on the algorithm level, GCoD integrates a split and conquer GCN training strategy that polarizes the graphs to be either denser or sparser in local neighborhoods without compromising the model accuracy, resulting in graph adjacency matrices that (mostly) have merely two levels of workload and enjoys largely enhanced regularity and thus ease of acceleration. On the hardware level, we further develop a dedicated two-pronged accelerator with a separated engine to process each of the aforementioned denser and sparser workloads, further boosting the overall utilization and acceleration efficiency. Extensive experiments and ablation studies validate that our GCoD consistently reduces the number of off-chip accesses, leading to speedups 15286&#x00D7;, 294&#x00D7;, 7.8&#x00D7;, and 2.5&#x00D7; as compared to CPUs, GPUs, and prior-art GCN accelerators including HyGCN and AWB-GCN, respectively, while maintaining or even improving the task accuracy. Additionally, we visualize GCoD trained graph adjacency matrices for a better understanding of its advantages.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00041"}, {"primary_key": "1669146", "vector": [], "sparse_vector": [], "title": "Adaptive Security Support for Heterogeneous Memory on GPUs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhou"], "summary": "The wide use of accelerators such as GPUs necessities their security support Recent works [17], [33], [34] pointed out that directly adopting the CPU secure memory design to GPUs could incur significant performance overheads due to the memory bandwidth contention between regular data and security metadata. In this paper, we analyze the security guarantees that used to defend against physical attacks, and make the observation that heterogeneous GPU memory system may not always need all the security mechanisms to achieve the security guarantees. Based on the memory types as well as memory access patterns either explicitly specified in the GPU programming model or implicitly detected at run time, we propose adaptive security memory support for heterogeneous memory on GPUs. Specifically, we first identify the read-only data and propose to only use MAC (Message Authentication Code) to protect their integrity. By eliminating the freshness checks on read-only data, we can use an on-chip shared counter for such data regions and remove the corresponding parts in the Bonsai Merkel Tree (BMT), thereby reducing the traffic due to encryption counters and the BMT. Second, we detect the common streaming data access pattern and propose coarse- grain MACs for such stream data to reduce the MAC access bandwidth. With the hardware-based detection of memory type (read-only or not) and memory access patterns (streaming or not), our proposed approach adapts the security support to significantly reduce the performance overhead without sacrificing the security guarantees. Our evaluation shows that our scheme can achieve secure memory on GPUs with low overheads for memory-intensive workloads. Among the fifteen memory-intensive workloads in our evaluation, our design reduces the performance overheads of secure GPU memory from 53.9% to 8.09% on average. Compared to the state-of- the-art secure memory designs for GPU [17], [33], our scheme outperforms PSSM by up to 41.63% and 9.5% on average and outperforms Common counters by 84.04% on average for memory-intensive workloads. We further propose to use the L2 cache as a victim cache for security metadata when the L2 is either underutilized or suffers from very high miss rates, which further reduces the overheads by up to 4% and 0.65% on average.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00024"}, {"primary_key": "1669147", "vector": [], "sparse_vector": [], "title": "HiPerRF: A Dual-Bit Dense Storage SFQ Register File.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Single Flux Quantum (SFQ) superconducting technology provides significant power and performance benefits in the era of diminishing CMOS scaling. Recent advances in design tools and fabrication facilities have brought SFQ based computing to the forefront. One challenge faced by SFQ technology is to have a compact and robust on-chip memory, which can be used for implementing register files and cache memory. While dense memories are being investigated through the development of three-terminal devices such as Nanocryotrons, in this work, we build on a novel memory cell built using traditional Josephson junctions (JJs). In particular, we design a high capacity register file, called HiPerRF, that builds on a High Capacity Destructive ReadOut (HC-DRO) cell in SFQ technology. HC-DRO design can store up to three fluxon pulses, thereby providing the equivalent of 2-bit storage in a single cell. However, these cells provide only destructive readout capability, namely each value can be read only once. However, CPU register file contents are read multiple times in any program, and hence a destructive readout complicates register file design. HiPerRF provides the non-destructive property using a loopback write mechanism, thereby preserving the higher density of HC-DRO cells without compromising the multi-read demands of a register file. HiPerRF reduces the JJ count of the register file design, after accounting for all the peripheral access circuitry costs, by 56.1% and reduces the static power by 46.2%. Furthermore, HiPerRF reduces the JJ count by 16.3% even when considering an entire in-order RISC-V CPU core.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00038"}, {"primary_key": "1669148", "vector": [], "sparse_vector": [], "title": "FAST: DNN Training Under Variable Precision Block Floating Point with Stochastic Rounding.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Block Floating Point (BFP) can efficiently support quantization for Deep Neural Network (DNN) training by providing a wide dynamic range via a shared exponent across a group of values. In this paper, we propose a Fast First, Accurate Second Training (FAST) system for DNNs, where the weights, activations, and gradients are represented in BFP. FAST supports matrix multiplication with variable precision BFP input operands, enabling incremental increases in DNN precision throughout training. By increasing the BFP precision across both training iterations and DNN layers, FAST can greatly shorten the training time while reducing overall hardware resource usage. Our FAST Multipler-Accumulator (fMAC) supports dot product computations under multiple BFP precisions. We validate our FAST system on multiple DNNs with different datasets, demonstrating a 2-6× speedup in training on a single-chip platform over prior work based on mixed-precision or block floating point number systems while achieving similar performance in validation accuracy.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00067"}, {"primary_key": "1669149", "vector": [], "sparse_vector": [], "title": "Tacker: Tensor-CUDA Core Kernel Fusion for Improving the GPU Utilization while Ensuring QoS.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yanchao Lu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The proliferation of machine learning applications has promoted both CUDA Cores and Tensor Cores&#x2019; integration to meet their acceleration demands. While studies have shown that co-locating multiple tasks on the same GPU can effectively improve system throughput and resource utilization, existing schemes focus on scheduling the resources of traditional CUDA Cores and thus lack the ability to exploit the parallelism between Tensor Cores and CUDA Cores.In this paper, we propose Tacker, a static kernel fusion and scheduling approach to improve GPU utilization of both types of cores while ensuring the QoS (Quality-of-Service) of co-located tasks. <PERSON><PERSON> consists of a Tensor-CUDA Core kernel fuser, a duration predictor for fused kernels, and a runtime QoS-aware kernel manager. The kernel fuser enables the flexible fusion of kernels that use Tensor Cores and CUDA Cores, respectively. The duration predictor precisely predicts the duration of the fused kernels. Finally, the kernel manager invokes the fused kernel or the original kernel based on the QoS headroom of latency-critical tasks to improve the system throughput. Our experimental results show that <PERSON><PERSON> improves the throughput of best-effort applications compared with state-of-the-art solutions by 18.6% on average, while ensuring the QoS of latency-critical tasks.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00064"}, {"primary_key": "1669150", "vector": [], "sparse_vector": [], "title": "Delegated Replies: Alleviating Network Clogging in Heterogeneous Architectures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Heterogeneous architectures with latency-sensitive CPU cores and bandwidth-intensive accelerators are attractive as they deliver high performance at favorable cost. These architectures typically have significantly more compute cores than memory nodes. The many bandwidth-intensive accelerators hence overwhelm the few memory nodes, resulting in suboptimal accelerator performance — as their bandwidth needs are not met — and poor CPU performance — because memory node blocking creates high latencies. We call this phenomenon network clogging. Since network clogging is a widespread issue in heterogeneous architectures, we first investigate if existing state-of-the-art approaches can address it. We find that the most effective prior approach, called Realistic Probing (RP), is suboptimal because it searches the local caches of other cores for missing data.We propose Delegated Replies which lets memory nodes speculatively delegate the responsibility of replying to last-level cache hits to the private cache that last accessed the requested cache block, hence avoiding the search that fundamentally limits RP. Moreover, Delegated Replies uses the (typically) under-utilized request network for delegation; it is the reply network links of the memory nodes that commonly clog because replies include complete cache blocks in addition to metadata. We evaluate Delegated Replies in the context of heterogeneous architectures with latency-sensitive CPU cores and bandwidth-intensive GPU cores and find that it improves GPU (CPU) performance by 14.2% (5.2%) and 25.7% (8.8%) on average compared to RP and our baseline, respectively.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00078"}, {"primary_key": "1669151", "vector": [], "sparse_vector": [], "title": "Q-GPU: A Recipe of Optimizations for Quantum Circuit Simulation Using GPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, quantum computing has undergone significant developments and has established its supremacy in many application domains. While quantum hardware is accessible to the public through the cloud environment, a robust and efficient quantum circuit simulator is necessary to investigate the constraints and foster quantum computing development, such as quantum algorithm development and quantum device architecture exploration. In this paper, we observe that most of the publicly available quantum circuit simulators (e.g., QISKit from IBM, QDK from Microsoft, and Qsim-Cirq from Google) suffer from slow simulation and poor scalability when the number of qubits increases. To this end, we systematically investigate the deficiencies in quantum circuit simulation (QCS) and propose Q-GPU, a framework that leverages GPUs with comprehensive optimizations to allow efficient and scalable QCS. Specifically, Q-GPU features i) proactive state amplitude transfer, ii) zero state amplitude pruning, iii) delayed qubit involvement, and iv) lossless nonzero state amplitude compression. Experimental results across nine representative quantum circuits indicate that Q-GPU significantly reduces the execution time of the state-of-the-art GPU-based QCS by 71.89% (3.55× speedup). Q-GPU also outperforms the state-of-the-art OpenMP CPU implementation, the Google Qsim-Cirq simulator, and the Microsoft QDK simulator by 1.49×, 2.02×, and 10.82×, respectively.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00059"}, {"primary_key": "1669152", "vector": [], "sparse_vector": [], "title": "HyBP: Hybrid Isolation-Randomization Secure Branch Predictor.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recently exposed vulnerabilities reveal the necessity to improve the security of branch predictors. Branch predictors record history about the execution of different processes, and such information from different processes are stored in the same structure and thus accessible to each other. This leaves the attackers with the opportunities for malicious training and malicious perception. Physical or logical isolation mechanisms such as using dedicated tables and flushing during context-switch can provide security but incur non-trivial costs in space and/or execution time. Randomization mechanisms incurs the performance cost in a different way: those with higher securities add latency to the critical path of the pipeline, while the simpler alternatives leave vulnerabilities to more sophisticated attacks.This paper proposes HyBP, a practical hybrid protection and effective mechanism for building secure branch predictors. The design applies the physical isolation and randomization in the right component to achieve the best of both worlds. We propose to protect the smaller tables with physically isolation based on (thread, privilege) combination; and protect the large tables with randomization. Surprisingly, the physical isolation also significantly enhances the security of the last-level tables by naturally filtering out accesses, reducing the information flow to these bigger tables. As a result, key changes can happen less frequently and be performed conveniently at context switches. Moreover, we propose a latency hiding design for a strong cipher by precomputing the \"code book\" with a validated, cryptographically strong cipher. Overall, our design incurs a performance penalty of 0.5% compared to 5.1% of physical isolation under the default context switching interval in Linux.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00033"}, {"primary_key": "1669153", "vector": [], "sparse_vector": [], "title": "Atomic Dataflow based Graph-Level Workload Orchestration for Scalable DNN Accelerators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To efficiently deploy state-of-the-art deep neural network (DNN) workloads with growing computational intensity and structural complexity, scalable DNN accelerators have been proposed in recent years, which are featured by multi-tensor engines and distributed on-chip buffers. Such spatial architectures have significantly expanded scheduling space in terms of parallelism and data reuse potentials, which demands for delicate workload orchestration. Previous works on DNN&#x2019;s hardware mapping problem mainly focus on operator-level loop transformation for single array, which are insufficient for this new challenge. Resource partitioning methods for multi-engines such as CNN-partition and inter-layer pipelining have been studied. However, their intrinsic disadvantages of workload unbalance and pipeline delay still prevent scalable accelerators from releasing full potentials.In this paper, we propose atomic dataflow, a novel graph-level scheduling and mapping approach developed for DNN inference. Instead of partitioning hardware resources into fixed regions and binding each DNN layer to a certain region sequentially, atomic dataflow schedules the DNN computation graph in workload-specific granularity (atoms) to ensure PE-array utilization, supports flexible atom ordering to exploit parallelism, and orchestrates atom-engine mapping to optimize data reuse between spatially connected tensor engines. Firstly, we propose a simulated annealing based atomic tensor generation algorithm to minimize load unbalance. Secondly, we develop a dynamic programming based atomic DAG scheduling algorithm to systematically explore massive ordering potentials. Finally, to facilitate data locality and reduce expensive off-chip memory access, we present mapping and buffering strategies to efficiently utilize distributed on-chip storage. With an automated optimization framework being established, experimental results show significant improvements over baseline approaches in terms of performance, hardware utilization, and energy consumption.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00042"}, {"primary_key": "1669154", "vector": [], "sparse_vector": [], "title": "Cottage: Coordinated Time Budget Assignment for Latency, Quality and Power Optimization in Web Search.", "authors": ["<PERSON>", "Laxmi <PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Most CPU power management techniques for web search assume that the time budget for a query is given a priori. However, determining the time budget on a per query granularity is challenging, because a difficult trade-off between the search latency, quality and power consumption has to be made. In this paper, we present Cottage, a coordinated time budget assignment framework between the aggregator and Index Serving Nodes (ISNs), which employs two distinct distributed search latency and quality predictors. The prediction results are integrated at a centralized optimizer for selecting the proper search time budget, while cutting off slow and low quality ISNs. Cottage also accelerates slow ISNs that have a high quality contribution, thus improving search quality. The implementation results on the Solr search engine show that Cottage outperforms state-of-the-art approaches with a 54% latency reduction and 41.3% less consumed power. In addition, the P@10 search quality with Cottage can still be as good as 0.947.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00017"}, {"primary_key": "1669155", "vector": [], "sparse_vector": [], "title": "TransPIM: A Memory-based Acceleration via Software-Hardware Co-Design for Transformer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Transformer-based models are state-of-the-art for many machine learning (ML) tasks. Executing Transformer usually requires a long execution time due to the large memory footprint and the low data reuse rate, stressing the memory system while under-utilizing the computing resources. Memory-based processing technologies, including processing in-memory (PIM) and near-memory computing (NMC), are promising to accelerate Transformer since they provide high memory bandwidth utilization and extensive computation parallelism. However, the previous memory-based ML accelerators mainly target at optimizing dataflow and hardware for compute-intensive ML models (e.g., CNNs), which do not fit the memory-intensive characteristics of Transformer. In this work, we propose TransPIM, a memory-based acceleration for Transformer using software and hardware co-design. In the software-level, TransPIM adopts a token-based dataflow to avoid the expensive inter-layer data movements introduced by previous layer-based dataflow. In the hardware-level, TransPIM introduces lightweight modifications in the conventional high bandwidth memory (HBM) architecture to support PIM-NMC hybrid processing and efficient data communication for accelerating Transformer-based models. Our experiments show that TransPIM is 3.7× to 9.1× faster than existing memory-based acceleration. As compared to conventional accelerators, TransPIM is 22.1× to 114.9× faster than GPUs and provides 2.0× more throughput than existing ASIC-based accelerators.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00082"}, {"primary_key": "1669156", "vector": [], "sparse_vector": [], "title": "Filesystem Encryption or Direct-Access for NVM Filesystems? Let&apos;s Have Both!", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Emerging Non-Volatile Memories (NVMs) are promising candidates to build ultra-low idle power memory and storage devices in future computing systems. Unlike DRAM, NVMs do not require frequent refresh operations, and they can retain data after crashes and power loss. With such features, NVM memory modules can be used partly as a conventional memory to host memory pages and partly as file storage to host filesystems and persistent data. Most importantly, and unlike current storage technologies, NVMs can be directly attached to the memory bus and accessed through conventional load/store operations.As NVMs feature ultra-low access latency, it is necessary to minimize software overheads for accessing files to enable the full potential. In legacy storage devices, e.g., Flash and Harddisk drives, access latency dominates the software overheads. However, emerging NVMs' performance can be burdened by the software overheads since memory access latency is minimal. Modern Operating Systems (OSes) allow direct-access (DAX) for NVM-hosted files through direct load/store operations by eliminating intermediate software layers. Unfortunately, we observe that such a direction ignores filesystem encryption and renders most of the current filesystem encryption implementations inapplicable to future NVM systems. In this paper, we propose a novel hardware/software co-design architecture that enables transparent filesystem encryption without sacrificing the direct-access feature of files in emerging NVMs with minimal change in OS and memory controller. Our proposed model incurs a negligible overall slowdown of 3.8% for workloads representative of real-world applications, while software-based encryption can incur as high as 5x slowdown for some applications.", "published": "2022-01-01", "category": "hpca", "pdf_url": "", "sub_summary": "", "source": "hpca", "doi": "10.1109/HPCA53966.2022.00043"}]