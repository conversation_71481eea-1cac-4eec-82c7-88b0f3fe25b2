[{"primary_key": "3270946", "vector": [], "sparse_vector": [], "title": "Measuring Interaction Proxemics with Wearable Light Tags.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The proxemics of social interactions (e.g., body distance, relative orientation) influences many aspects of our everyday life: from patients' reactions to interaction with physicians, successes in job interviews, to effective teamwork. Traditionally, interaction proxemics has been studied via questionnaires and participant observations, imposing high burden on users, low scalability and precision, and often biases. In this paper we present Protractor, a novel wearable technology for measuring interaction proxemics as part of non-verbal behavior cues with fine granularity. Protractor employs near-infrared light to monitor both the distance and relative body orientation of interacting users. We leverage the characteristics of near-infrared light (i.e., line-of-sight propagation) to accurately and reliably identify interactions; a pair of collocated photodiodes aid the inference of relative interaction angle and distance. We achieve robustness against temporary blockage of the light channel (e.g., by the user's hand or clothes) by designing sensor fusion algorithms that exploit inertial sensors to obviate the absence of light tracking results. We fabricated Protractor tags and conducted real-world experiments. Results show its accuracy in tracking body distances and relative angles. The framework achieves less than 6° error 95% of the time for measuring relative body orientation and 2.3-cm - 4.9-cm mean error in estimating interaction distance. We deployed Protractor tags to track user's non-verbal behaviors when conducting collaborative group tasks. Results with 64 participants show that distance and angle data from Protractor tags can help assess individual's task role with 84.9% accuracy, and identify task timeline with 93.2% accuracy.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191757"}, {"primary_key": "3270964", "vector": [], "sparse_vector": [], "title": "Remotion: A Motion-Based Capture and Replay Platform of Mobile Device Interaction for Remote Usability Testing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Remotion is an end-to-end system for capturing and replaying rich mobile device interactions, comprising both on-screen video and physical device motions. The blueprints and software provided here allow an interface to be instrumented with Remotion's capture and visualization system. Remotion is able to mimic mobile device motion through a software 3D graphical visualization and a robotic mount that replicates the movements of a mobile device from afar. Deployed together, experimenters can emulate the mobile device postures of a remote user as if they were in the room. This is important since many usability studies are carried remotely and the contribution and scale of those studies are irreplaceable. We compared how HCI experts (\"analysts\") observed remote users behavioral data across three replay platforms: a traditional live time-series of motion, Remotion's software visualization, and Remotion's hardware visualization. We found that Remotion can assist analysts to infer the user's attention, emotional state, habits, and active hand posture; Remotion also has a reduced effect on mental demand for analysts when analyzing the remote user's contextual information.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214280"}, {"primary_key": "3270973", "vector": [], "sparse_vector": [], "title": "Effect of Distinct Ambient Noise Types on Mobile Interaction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The adverse effect of ambient noise on humans has been extensively studied in fields like cognitive science, indicating a significant impact on cognitive performance, behaviour, and emotional state. Surprisingly, the effect of ambient noise has not been studied in the context of mobile interaction. As smartphones are ubiquitous by design, smartphone users are exposed to a wide variety of ambient noises while interacting with their devices. In this paper, we present a structured analysis of the effect of six distinct ambient noise types on typical smartphone usage tasks. The evaluated ambient noise types include variants of music, urban noise and speech. We analyse task completion time and errors, and find that different ambient noises affect users differently. For example, while speech and urban noise slow down text entry, being exposed to music reduces completion time in target acquisition tasks. Our study contributes to the growing research area on situational impairments, and we compare our results to previous work on the effect of cold-induced situational impairments. Our results can be used to support smartphone users through adaptive interfaces which respond to the ongoing context of the user.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214285"}, {"primary_key": "3271003", "vector": [], "sparse_vector": [], "title": "Tracking Depression Dynamics in College Students Using Mobile Phone and Wearable Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "There are rising rates of depression on college campuses. Mental health services on our campuses are working at full stretch. In response researchers have proposed using mobile sensing for continuous mental health assessment. Existing work on understanding the relationship between mobile sensing and depression, however, focuses on generic behavioral features that do not map to major depressive disorder symptoms defined in the standard mental disorders diagnostic manual (DSM-5). We propose a new approach to predicting depression using passive sensing data from students' smartphones and wearables. We propose a set of", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191775"}, {"primary_key": "3271031", "vector": [], "sparse_vector": [], "title": "TeamSense: Assessing Personal Affect and Group Cohesion in Small Teams through Dyadic Interaction and Behavior Analysis with Wearable Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Continuous monitoring with unobtrusive wearable social sensors is becoming a popular method to assess individual affect states and team effectiveness in human research. A large number of applications have demonstrated the effectiveness of applying wearable sensing in corporate settings; for example, in short periodic social events or in a university campus. However, little is known of how we can automatically detect individual affect and group cohesion for long duration missions. Predicting negative affect states and low cohesiveness is vital for team missions. Knowing team members' negative states allows timely interventions to enhance their effectiveness. This work investigates whether sensing social interactions and individual behaviors with wearable sensors can provide insights into assessing individual affect states and group cohesion. We analyzed wearable sensor data from a team of six crew members who were deployed on a four-month simulation of a space exploration mission at a remote location. Our work proposes to recognize team members' affect states and group cohesion as a binary classification problem using novel behavior features that represent dyadic interaction and individual activities. Our method aggregates features from individual members into group levels to predict team cohesion. Our results show that the behavior features extracted from the wearable social sensors provide useful information in assessing personal affect and team cohesion. Group task cohesion can be predicted with a high performance of over 0.8 AUC. Our work demonstrates that we can extract social interactions from sensor data to predict group cohesion in longitudinal missions. We found that quantifying behavior patterns including dyadic interactions and face-to-face communications are important in assessing team process.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264960"}, {"primary_key": "3270839", "vector": [], "sparse_vector": [], "title": "Up to a Limit?: Privacy Concerns of Bystanders and Their Willingness to Share Additional Information with Visually Impaired Users of Assistive Technologies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The emergence of augmented reality and computer vision based tools offer new opportunities to visually impaired persons (VIPs). Solutions that help VIPs in social interactions by providing information (age, gender, attire, expressions etc.) about people in the vicinity are becoming available. Although such assistive technologies are already collecting and sharing such information with VIPs, the views, perceptions, and preferences of sighted bystanders about such information sharing remain unexplored. Although bystanders may be willing to share more information for assistive uses it remains to be explored to what degree bystanders are willing to share various kinds of information and what might encourage additional sharing of information based on the contextual needs of VIPs. In this paper we describe the first empirical study of information sharing preferences of sighted bystanders of assistive devices. We conducted a survey based study using a contextual method of inquiry with 62 participants followed by nine semi-structured interviews to shed more insight on our key quantitative findings. We find that bystanders are more willing to share some kinds of personal information with VIPs and are willing to share additional information if higher security assurances can be made by improving their control over how their information is shared.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264899"}, {"primary_key": "3270840", "vector": [], "sparse_vector": [], "title": "EyeSpyVR: Interactive Eye Sensing Using Off-the-Shelf, Smartphone-Based VR Headsets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Low cost virtual reality (VR) headsets powered by smartphones are becoming ubiquitous. Their unique position on the user's face opens interesting opportunities for interactive sensing. In this paper, we describe EyeSpyVR, a software-only eye sensing approach for smartphone-based VR, which uses a phone's front facing camera as a sensor and its display as a passive illuminator. Our proof-of-concept system, using a commodity Apple iPhone, enables four sensing modalities: detecting when the VR head set is worn, detecting blinks, recognizing the wearer's identity, and coarse gaze tracking - features typically found in high-end or specialty VR headsets. We demonstrate the utility and accuracy of EyeSpyVR in a series of studies with 70 participants, finding a worn detection of 100%, blink detection rate of 95.3%, family user identification accuracy of 81.4%, and mean gaze tracking error of 10.8° when calibrated to the wearer (12.9° without calibration). These sensing abilities can be used by developers to enable new interactive features and more immersive VR experiences on existing, off-the-shelf hardware.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214260"}, {"primary_key": "3270841", "vector": [], "sparse_vector": [], "title": "I Can&apos;t <PERSON>: Effects of Wearable Cameras on the Capture of Authentic Behavior in the Wild.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Bonnie <PERSON>", "Nabil <PERSON>"], "summary": "Wearable sensors can provide reliable, automated measures of health behaviors in free-living populations. However, validation of these measures is impossible without observable confirmation of behaviors. Participants have expressed discomfort during the use of ego-centric wearable cameras with first-person view. We argue that mounting the camera on different body locations with a different lens orientation, gives a device recording affordance that has the effect of reducing surveillance and social discomfort compared to ego-centric cameras. We call these types of cameras \"activity-oriented\" because they are designed to capture a particular activity, rather than the field of view of the wearer. We conducted an experiment of three camera designs with 24 participants, collecting qualitative data on participants' experience while wearing these devices in the wild. We provide a model explaining factors that lead to an increase in social presence and social stigma, which, therefore, create social and surveillance discomfort for the wearer. Wearers' attempts to reduce this discomfort by modifying their behavior or abandoning the device threatens the validity of observations of authentic behaviors. We discuss design implications and provide recommendations to help reduce social presence and stigma in order to improve the validity of observations with cameras in the wild.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264900"}, {"primary_key": "3270844", "vector": [], "sparse_vector": [], "title": "A Survey of Attention Management Systems in Ubiquitous Computing Environments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Today's information and communication devices provide always-on connectivity, instant access to an endless repository of information, and represent the most direct point of contact to almost any person in the world. Despite these advantages, devices such as smartphones or personal computers lead to the phenomenon of attention fragmentation, continuously interrupting individuals' activities and tasks with notifications. Attention management systems aim to provide active support in such scenarios, managing interruptions, for example, by postponing notifications to opportune moments for information delivery. In this article, we review attention management system research with a particular focus on ubiquitous computing environments. We first examine cognitive theories of attention and extract guidelines for practical attention management systems. Mathematical models of human attention are at the core of these systems, and in this article, we review sensing and machine learning techniques that make such models possible. We then discuss design challenges towards the implementation of such systems, and finally, we investigate future directions in this area, paving the way for new approaches and systems supporting users in their attention management.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214261"}, {"primary_key": "3270847", "vector": [], "sparse_vector": [], "title": "The Design Space of 3D Printable Interactivity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The capabilities of 3D printers are rapidly progressing towards fabrication of fully interactive products. For designers to reason about the best way to achieve their interaction design goals, it is helpful to not only know what exists in the literature, but to also understand the design space of options. Such an understanding can help in comparison, analysis, selection of suitable technology, and also in the generation of new ideas. In this article, we survey the state of the art in 3D printing fully functional sensors and actuators to support explicit interaction techniques. We classify and organize the surveyed works around the following parameters: mechanism, designed affordances, interaction primitives, and output modality. The design space is presented in the form of a multidimensional matrix known as a Zwicky box. Using the tables, we can make observations about the existing literature and also identify gaps in this design space. Many such gaps can potentially lead to exciting new research or engineering opportunities.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214264"}, {"primary_key": "3270848", "vector": [], "sparse_vector": [], "title": "rConverse: Moment by Moment Conversation Detection Using a Mobile Respiration Sensor.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Monitoring of in-person conversations has largely been done using acoustic sensors. In this paper, we propose a new method to detect moment-by-moment conversation episodes by analyzing breathing patterns captured by a mobile respiration sensor. Since breathing is affected by physical and cognitive activities, we develop a comprehensive method for cleaning, screening, and analyzing noisy respiration data captured in the field environment at individual breath cycle level. Using training data collected from a speech dynamics lab study with 12 participants, we show that our algorithm can identify each respiration cycle with 96.34% accuracy even in presence of walking. We present a Conditional Random Field, Context-Free Grammar (CRF-CFG) based conversation model, called rConverse, to classify respiration cycles into speech or non-speech, and subsequently infer conversation episodes. Our model achieves 82.7% accuracy for speech/non-speech classification and it identifies conversation episodes with 95.9% accuracy on lab data using a leave-one-subject-out cross-validation. Finally, the system is validated against audio ground-truth in a field study with 32 participants. rConverse identifies conversation episodes with 71.7% accuracy on 254 hours of field data. For comparison, the accuracy from a high-quality audio-recorder on the same data is 71.9%.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191734"}, {"primary_key": "3270852", "vector": [], "sparse_vector": [], "title": "FingerReader2.0: Designing and Evaluating a Wearable Finger-Worn Camera to Assist People with Visual Impairments while Shopping.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sur<PERSON>"], "summary": "People with Visual Impairments (PVI) experience greater difficulties with daily tasks, such as supermarket shopping. Identifying and purchasing an item proves challenging for PVI. Using a user-centered design process, we understand the difficulties PVI encounter in their daily routines. Consequently, the previous FingerReader model was elevated to a new level. In contrast, FingerReader2.0 incorporates a highly integrated hardware design, as it is standalone, wearable, and not tethered to a computer. Software-wise, the prototype utilizes a deep learning system, relying on a hybrid, an on-board and a cloud-based model. The advanced design significantly extends the range of mobile assistive technology, particularly for shopping purposes. This paper presents the findings from interviews, several iterative studies, and a field study in supermarkets to demonstrate the FingerReader2.0's enhanced capabilities for those with varied levels of visual impairment.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264904"}, {"primary_key": "3270859", "vector": [], "sparse_vector": [], "title": "Mobile Money: Understanding and Predicting its Adoption and Use in a Developing Economy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Access to financial institutions is difficult in developing economies and especially for the poor. However, the widespread adoption of mobile phones has enabled the development of mobile money systems that deliver financial services through the mobile phone network. Despite the success of mobile money, there is a lack of quantitative studies that unveil which factors contribute to the adoption and sustained usage of such services. In this paper, we describe the results of a quantitative study that analyzes data from the world's leading mobile money service, M-Pesa. We analyzed millions of anonymized mobile phone communications and M-Pesa transactions in an African country. Our contributions are threefold: (1) we analyze the customers' usage of M-Pesa and report large-scale patterns of behavior; (2) we present the results of applying machine learning models to predict mobile money adoption (AUC=0.691), and mobile money spending (AUC=0.619) using multiple data sources: mobile phone data, M-Pesa agent information, the number of M-Pesa friends in the user's social network, and the characterization of the user's geographic location; (3) we discuss the most predictive features in both models and draw key implications for the design of mobile money services in a developing country. We find that the most predictive features are related to mobile phone activity, to the presence of M-Pesa users in a customer's ego-network and to mobility. We believe that our work will contribute to the understanding of the factors playing a role in the adoption and sustained usage of mobile money services in developing economies.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287035"}, {"primary_key": "3270866", "vector": [], "sparse_vector": [], "title": "Detecting Eating Episodes by Tracking Jawbone Movements with a Non-Contact Wearable Sensor.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>az"], "summary": "Eating is one of the most fundamental human activities, and because of the important role it plays in our lives, it has been extensively studied. However, an objective and usable method for dietary intake tracking remains unrealized despite numerous efforts by researchers over the last decade. In this work, we present a new wearable computing approach for detecting eating episodes. Using a novel multimodal sensing strategy combining accelerometer and range sensing, the approach centers on a discreet and lightweight instrumented necklace that captures head and jawbone movements without direct contact with the skin. An evaluation of the system with 32 participants comprised of three phases resulted in eating episodes detected with 95.2% precision and 81.9% recall in controlled studies and 78.2% precision and 72.5% recall in the free-living study. This research add technical contributions to the fields of wearable computing, human activity recognition, and mobile health.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191736"}, {"primary_key": "3270869", "vector": [], "sparse_vector": [], "title": "The Role of Urban Mobility in Retail Business Survival.", "authors": ["Krittika D&apos;Silva", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Economic and urban planning agencies have strong interest in tackling the hard problem of predicting the odds of survival of individual retail businesses. In this work, we tap urban mobility data available both from a location-based intelligence platform, Foursquare, and from public transportation agencies, and investigate whether mobility-derived features can help foretell the failure of such retail businesses, over a 6 month horizon, across 10 distinct cities spanning the globe. We hypothesise that the survival of such a retail outlet is correlated with not only venue-specific characteristics but also broader neighbourhood-level effects. Through careful statistical analysis of Foursquare and taxi mobility data, we uncover a set of discriminative features, belonging to the neighbourhood's static characteristics, the venue-specific customer visit dynamics, and the neighbourhood's mobility dynamics. We demonstrate that classifiers trained on such features can predict such survival with high accuracy, achieving approximately 80% precision and recall across the cities. We also show that the impact of such features varies across new and established venues and across different cities. Besides achieving a significant improvement over past work on business vitality prediction, our work demonstrates the vital role that mobility dynamics plays in the economic evolution of a city.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264910"}, {"primary_key": "3270871", "vector": [], "sparse_vector": [], "title": "Epidermal Robots: Wearable Sensors That Climb on the Skin.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Epidermal sensing has enabled significant advancements towards the measurement and understanding of health. Most of the existing medical instruments require direct expert manipulation of a doctor, measure a single parameter, and/or have limited sensing coverage. In contrast, this work demonstrates the first epidermal robot with the ability to move over the surface of the skin and capture a large range of body parameters. In particular, we developed SkinBot, a 2x4x2 centimeter-size robot that moves over the skin surface with a two-legged suction-based locomotion. We demonstrate three of the potential medical sensing applications which include the measurement of body biopotentials (e.g., electrodermal activity, electrocardiography) through modified suction cups that serve as electrodes, skin imaging through a skin-facing camera that can capture skin anomalies, and inertial body motions through a 6-axis accelerometer and gyroscope that can capture changes of body posture and subtle cardiorespiratory vibrations.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264912"}, {"primary_key": "3270874", "vector": [], "sparse_vector": [], "title": "HyRise: A Robust and Ubiquitous Multi-Sensor Fusion-based Floor Localization System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Floor localization is an integral part of indoor localization systems that are deployed in any typical high-rise building. Nevertheless, while many efforts have been made to detect floor change events leveraging phone-embedded sensors, there are still a number of pitfalls that need to be overcome to provide robust and accurate localization in the 3D space. In this paper, we present HyRise: a robust and ubiquitous probabilistic crowdsourcing-based floor determination system. HyRise is a hybrid system that combines the barometer sensor and the ubiquitous Wi-Fi access points installed in the building into a probabilistic framework to identify the user's floor. In particular, HyRise incorporates a discrete Markov localization algorithm where the motion model is based on the vertical transitions detected from the sampled pressure readings and the observation model is based on the overheard Wi-Fi access points (APs) to find the most probable floor of the user. HyRise also has provisions to handle practical deployment issues including handling the inherent drift in the barometer readings, the noisy wireless environment, heterogeneous devices, among others. HyRise is implemented on Android phones and evaluated using three different testbeds: a campus building, a shopping mall, and a residential building with different floorplan layouts and APs densities. The results show that HyRise can identify the exact user's floor correctly in 93%, 92% and 77% of the cases for the campus building, the shopping mall, and the more challenging residential building; respectively. In addition, it can identify the floor with at most 1-floor error in 100% of the cases for all three testbeds. Moreover, the floor localization accuracy outperforms that achieved by other state-of-the-art techniques by at least 79% and up to 278%. This accuracy is achieved with no training overhead, is robust to the different user devices, and is consistent in buildings with different structures and APs densities.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264914"}, {"primary_key": "3270878", "vector": [], "sparse_vector": [], "title": "Online Deep Ensemble Learning for Predicting Citywide Human Mobility.", "authors": ["Zipei Fan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Predicting citywide human mobility is critical to an effective management and regulation of city governance, especially during a rare event (e.g. large event such as New Year's celebration or Comiket). Classical models can effectively predict routine human mobility, but irregular mobility during a rare event (precedented or unprecedented), which is much more difficult to model, has not drawn sufficient attention. Moreover, the complexity and non-linearity of human mobility hinders a simple model from making an accurate prediction. Bearing these facts in mind, we propose a novel online gating neural network framework with two phases. In the offline training phase, we train a gated recurrent unit-based human mobility predictor for each day in our training set, while in the online predicting phase, we construct an online adaptive human mobility predictor as well as a gating neural network that switches among the pre-trained predictors and the online adaptive human predictor. Our approach was evaluated using a real-world GPS-log dataset from Tokyo and Osaka and achieved a higher prediction accuracy than baseline models.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264915"}, {"primary_key": "3270881", "vector": [], "sparse_vector": [], "title": "Mobile Device Type Substitution.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Mobile users today interact with a variety of mobile device types including smartphones, tablets, smartwatches, and others. However research on mobile device type substitution has been limited in several respects including a lack of detailed and robust analyses. Therefore, in this work we study mobile device type substitution through analysis of multidevice usage data from a large US-based user panel. Specifically, we use regression analysis over paired user groups to test five device type substitution hypotheses. We find that both tablets and PCs are partial substitutes for smartphones with tablet and PC ownership decreasing smartphone usage by about 12.5 and 13 hours/month respectively. Additionally, we find that tablets and PCs also prompt about 20 and 57 hours/month respectively of additional (non-substituted) usage. We also illustrate significant inter-user diversity in substituted and additional usage. Overall, our results can help in understanding the relative positioning of different mobile device types and in parameterizing higher level mobile ecosystem models.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191740"}, {"primary_key": "3270884", "vector": [], "sparse_vector": [], "title": "Detecting Conversing Groups Using Social Dynamics from Wearable Acceleration: Group Size Awareness.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose a method for detecting conversing groups. More specifically, we detect pairwise F-formation membership using a single worn accelerometer. We focus on crowded real life scenarios, specifically mingling events, where groups of different sizes naturally occur and evolve over time. Our method uses the dynamics of interaction, derived from people's coordinated social actions and movements. The social actions, speaking, head and hand gesturing, are inferred from wearable acceleration with a transfer learning approach. These automatically labeled actions, together with the raw acceleration, are used to define joint representations of interaction between people through the extraction of pairwise features. We present a new feature set based on the overlap patterns of social actions and utilize some others that were previously proposed in other domains. Our approach considers various interaction patterns of different sized groups by training multiple classifiers with respect to cardinality. The final estimation is then dynamically performed by meta-classifier learning using the local neighborhood of the current test sample. We experimentally show that the proposed method outperforms state of the art approaches. Finally, we show how the accuracy of the social action detection affects group detection performance, analyze the effectiveness of features for different group sizes in detail, discuss how different types of features contribute to the final performance and evaluate the effects of using the local neighborhood for meta-classifier learning.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287041"}, {"primary_key": "3270889", "vector": [], "sparse_vector": [], "title": "You Are Sensing, but Are You Biased?: A User Unaided Sensor Calibration Approach for Mobile Sensing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile devices are becoming pervasive to our daily lives: they follow us everywhere and we use them for much more than just communication. These devices are also equipped with a myriad of different sensors that have the potential to allow the tracking of human activities, user patterns, location, direction and much more. Following this direction, many movements including sports, quantified self, and mobile health ones are starting to heavily rely on this technology, making it pivotal that the sensors offer high accuracy. However, heterogeneity in hardware manufacturing, slight substrate differences, electronic interference as well as external disturbances are just few of the reasons that limit sensor output accuracy which in turn hinders sensor usage in applications which need very high granularity and precision, such as quantified-self applications. Although, calibration of sensors is a widely studied topic in literature to the best of our knowledge no publicly available research exists that specifically tackles the calibration of mobile phones and existing methods that can be adapted for use in mobile devices not only require user interaction but they are also not adaptive to changes. Additionally, alternative approaches for performing more granular and accurate sensing exploit body-wide sensor networks using mobile phones and additional sensors; as one can imagine these techniques can be bulky, tedious, and not particularly user friendly. Moreover, existing techniques for performing data corrections post-acquisition can produce inconsistent results as they miss important context information provided from the device itself; which when used, has been shown to produce better results without a imposing a significant power-penalty. In this paper we introduce a novel multiposition calibration scheme that is specifically targeted at mobile devices Our scheme exploits machine learning techniques to perform an adaptive, power-efficient auto-calibration procedure with which achieves high output sensor accuracy when compared to state of the art techniques without requiring any user interaction or special equipment beyond device itself Moreover, the energy costs associated with our approach are lower than the alternatives (such as Kalman filter based solutions) and the overall power penalty is &lt; 5% when compared against power usage that is exhibited when using uncalibrated traces, thus, enabling our technique to be used efficiently on a wide variety of devices Finally, our evaluation illustrates that calibrated signals offer a tangible benefit in classification accuracy, ranging from 3 to 10%, over uncalibrated ones when using state of the art classifiers, on the other hand when using simpler SVM classifiers the classification improvement is boosted ranging from 8% to 12% making lower performing classifiers much more reliable Additionally, we show that for similar activities which are hard to distinguish otherwise, we reach an accuracy of &gt; 95% when using neural network classifiers and &gt; 88% when using SVM classifiers where uncalibrated data classification only reaches ~ 85% and ~ 80% respectively This can be a make or break factor in the use of accelerometer and gyroscope data in applications requiring high accuracy e g sports, health, games and others", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191743"}, {"primary_key": "3270903", "vector": [], "sparse_vector": [], "title": "EngageMon: Multi-Modal Engagement Sensing for Mobile Games.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "JeongGil Ko", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Understanding the engagement levels players have with a game is a useful proxy for evaluating the game design and user experience. This is particularly important for mobile games as an alternative game is always just an easy download away. However, engagement is a subjective concept and usually requires fine-grained highly disruptive interviews or surveys to determine accurately. In this paper, we present EngageMon, a first-of-its-kind system that uses a combination of sensors from the smartphone (touch events), a wristband (photoplethysmography and electrodermal activity sensor readings), and an external depth camera (skeletal motion information) to accurately determine the engagement level of a mobile game player. Our design was guided by feedback obtained from interviewing 22 mobile game developers, testers, and designers. We evaluated EngageMon using data collected from 64 participants (54 in a lab-setting study and another 10 in a more natural setting study) playing six games from three different categories including endless runner, 3D motorcycle racing, and casual puzzle. Using all three sets of sensors, EngageMon was able to achieve an average accuracy of 85% and 77% under cross-sample and cross-subject evaluations respectively. Overall, EngageMon can accurately determine the engagement level of mobiles users while they are actively playing a game.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191745"}, {"primary_key": "3270908", "vector": [], "sparse_vector": [], "title": "Predicting Episodes of Non-Conformant Mobility in Indoor Environments.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Traditional mobility prediction literature focuses primarily on improved methods to extract latent patterns from individual-specific movement data. When such predictions are incorrect, we ascribe it to 'random' or 'unpredictable' changes in a user's movement behavior. Our hypothesis, however, is that such apparently-random deviations from daily movement patterns can, in fact, of ten be anticipated. In particular, we develop a methodology for predicting Likelihood of Future Non-Conformance (LFNC), based on two central hypotheses: (a) the likelihood of future deviations in movement behavior is positively correlated to the intensity of such trajectory deviations observed in the user's recent past, and (b) the likelihood of such future deviations increases if the user's strong-ties have also recently exhibited such non-conformant movement behavior. We use extensive longitudinal indoor location data (spanning 4+ months) from an urban university campus to validate these hypotheses, and then show that these features can be used to build an accurate non-conformance predictor: it can predict non-conformant mobility behavior two hours in advance with an AUC ≥ 0.85, significantly outperforming the baseline. We also show that this prediction methodology holds for a representative outdoor public-transport based mobility dataset. Finally, we use a real-world mobile crowd-sourcing application to show the practical impact of such non-conformance: failure to identify such likely anomalous movement behavior causes workers to suffer a noticeable drop in task completion rates and reduces the spatial spread of successfully completed tasks.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287050"}, {"primary_key": "3270909", "vector": [], "sparse_vector": [], "title": "Deep ROI-Based Modeling for Urban Human Mobility Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Zipei Fan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Rapidly developing location acquisition technologies have provided us with big GPS trajectory data, which offers a new means of understanding people's daily behaviors as well as urban dynamics. With such data, predicting human mobility at the city level will be of great significance for transportation scheduling, urban regulation, and emergency management. In particular, most urban human behaviors are related to a small number of important regions, referred to as Regions-of-Interest (ROIs). Therefore, in this study, a deep ROI-based modeling approach is proposed for effectively predicting urban human mobility. Urban ROIs are first discovered from historical trajectory data, and urban human mobility is designated using two types of ROI labels (ISROI and WHICHROI). Then, urban mobility prediction is modeled as a sequence classification problem for each type of label. Finally, a deep-learning architecture built with recurrent neural networks is designed as an effective sequence classifier. Experimental results demonstrate that the superior performance of our proposed approach to the baseline models and several real-world practices show the applicability of our approach to real-world urban computing problems.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191746"}, {"primary_key": "3270910", "vector": [], "sparse_vector": [], "title": "Why Are They Collecting My Data?: Inferring the Purposes of Network Traffic in Mobile Apps.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many smartphone apps collect potentially sensitive personal data and send it to cloud servers. However, most mobile users have a poor understanding of why their data is being collected. We present MobiPurpose, a novel technique that can take a network request made by an Android app and then classify the data collection purposes, as one step towards making it possible to explain to non-experts the data disclosure contexts. Our purpose inference works by leveraging two observations: 1) developer naming conventions (e.g., URL paths) of ten offer hints as to data collection purposes, and 2) external knowledge, such as app metadata and information about the domain name, are meaningful cues that can be used to infer the behavior of different traffic requests. MobiPurpose parses each traffic request body into key-value pairs, and infers the data type and data collection purpose of each key-value pair using a combination of supervised learning and text pattern bootstrapping. We evaluated MobiPurpose's effectiveness using a dataset cross-labeled by ten human experts. Our results show that MobiPurpose can predict the data collection purpose with an average precision of 84% (among 19 unique categories).", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287051"}, {"primary_key": "3270912", "vector": [], "sparse_vector": [], "title": "Obfuscation At-Source: Privacy in Context-Aware Mobile Crowd-Sourcing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "By effectively reaching out to and engaging larger population of mobile users, mobile crowd-sourcing has become a strategy to perform large amount of urban tasks. The recent empirical studies have shown that compared to the pull-based approach, which expects the users to browse through the list of tasks to perform, the push-based approach that actively recommends tasks can greatly improve the overall system performance. As the efficiency of the push-based approach is achieved by incorporating worker's mobility traces, privacy is naturally a concern. In this paper, we propose a novel, 2-stage and user-controlled obfuscation technique that provides a trade off-amenable framework that caters to multi-attribute privacy measures (considering the per-user sensitivity and global uniqueness of locations). We demonstrate the effectiveness of our approach by testing it using the real-world data collected from the well-established TA$Ker platform. More specifically, we show that one can increase its location entropy by 23% with only modest changes to the real trajectories while imposing an additional 24% (&lt; 1 min) of detour overhead on average. Finally, we present insights derived by carefully inspecting various parameters that control the whole obfuscation process.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191748"}, {"primary_key": "3270917", "vector": [], "sparse_vector": [], "title": "Interrupting Drivers for Interactions: Predicting Opportune Moments for In-vehicle Proactive Auditory-verbal Tasks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Auditory-verbal interactions with in-vehicle information systems have become increasingly popular for improving driver safety because they obviate the need for distractive visual-manual operations. This opens up new possibilities for enabling proactive auditory-verbal services where intelligent agents proactively provide contextualized recommendations and interactive decision-making. However, prior studies have warned that such interactions may consume considerable attentional resources, thus negatively affecting driving performance. This work aims to develop a machine learning model that can find opportune moments for the driver to engage in proactive auditory-verbal tasks by using the vehicle and environment sensor data. Given that there is a lack of definition about what constitutes interruptibility for auditory-verbal tasks, we first define interruptible moments by considering multiple dimensions and then iteratively develop the experimental framework through an extensive literature review and four pilot studies. We integrate our framework into OsmAnd, an open-source navigation service, and perform a real-road field study with 29 drivers to collect sensor data and user responses. Our machine learning analysis shows that opportune moments for interruption can be conservatively inferred with an accuracy of 0.74. We discuss how our experimental framework and machine learning models can be used to design intelligent auditory-verbal services in practical deployment contexts.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287053"}, {"primary_key": "3270932", "vector": [], "sparse_vector": [], "title": "Just-in-Time but Not Too Much: Determining Treatment Timing in Mobile Health.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Mustafa al&apos;<PERSON><PERSON><PERSON>", "Predrag <PERSON>", "<PERSON>"], "summary": "There is a growing scientific interest in the use and development of just-in-time adaptive interventions in mobile health. These mobile interventions typically involve treatments, such as reminders, activity suggestions and motivational messages, delivered via notifications on a smartphone or a wearable to help users make healthy decisions in the moment. To be effective in influencing health, the combination of the right treatment and right delivery time is likely critical. A variety of prediction/detection algorithms have been developed with the goal of pinpointing the best delivery times. The best delivery times might be times of greatest risk and/or times at which the user might be most receptive to the treatment notifications. In addition, to avoid over burdening users, there is often a constraint on the number of treatments that should be provided per time interval (e.g., day or week). Yet there may be many more times at which the user is predicted or detected to be at risk and/or receptive. The goal then is to spread treatment uniformly across all of these times. In this paper, we introduce a method that spreads the treatment uniformly across the delivery times. This method can also be used to provide data for learning whether the treatments are effective at the delivery times. This work is motivated by our work on two mobile health studies, a smoking cessation study and a physical activity study.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287057"}, {"primary_key": "3270934", "vector": [], "sparse_vector": [], "title": "Vocal Resonance: Using Internal Body Voice for Wearable Authentication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We observe the advent of body-area networks of pervasive wearable devices, whether for health monitoring, personal assistance, entertainment, or home automation. For many devices, it is critical to identify the wearer, allowing sensor data to be properly labeled or personalized behavior to be properly achieved. In this paper we propose the use of vocal resonance, that is, the sound of the person's voice as it travels through the person's body -- a method we anticipate would be suitable for devices worn on the head, neck, or chest. In this regard, we go well beyond the simple challenge of speaker recognition: we want to know who is wearing the device. We explore two machine-learning approaches that analyze voice samples from a small throat-mounted microphone and allow the device to determine whether (a) the speaker is indeed the expected person, and (b) the microphone-enabled device is physically on the speaker's body. We collected data from 29 subjects, demonstrate the feasibility of a prototype, and show that our DNN method achieved balanced accuracy 0.914 for identification and 0.961 for verification by using an LSTM-based deep-learning model, while our efficient GMM method achieved balanced accuracy 0.875 for identification and 0.942 for verification.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191751"}, {"primary_key": "3270935", "vector": [], "sparse_vector": [], "title": "Third-Eye: A Mobilephone-Enabled Crowdsensing System for Air Quality Monitoring.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Huadong Ma", "<PERSON>"], "summary": "Air pollution has raised people's public health concerns in major cities, especially for Particulate Matter under 2.5μm (PM2.5) due to its significant impact on human respiratory and circulation systems. In this paper, we present the design, implementation, and evaluation of a mobile application, Third-Eye, that can turn mobile phones into high-quality PM2.5 monitors, thereby enabling a crowdsensing way for fine-grained PM2.5 monitoring in the city. We explore two ways, crowdsensing and web crawling, to efficiently build large-scale datasets of the outdoor images taken by mobile phone, weather data, and air-pollution data. Then, we leverage two deep learning models, Convolutional Neural Network (CNN) for images and Long Short Term Memory (LSTM) network for weather and air-pollution data, to build an end-to-end framework for training PM2.5 inference models. Our App has been downloaded more than 2,000 times and runs more than 1 year. The real user data based evaluation shows that Third-Eye achieves 17.38 μg/m3 average error and 81.55% classification accuracy, which outperforms 5 state-of-the-art methods, including three scattered interpolations and two image based estimation methods. The results also demonstrate how Third-Eye offers substantial enhancements over typical portable PM2.5 monitors by simultaneously improving accessibility, portability, and accuracy.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191752"}, {"primary_key": "3270940", "vector": [], "sparse_vector": [], "title": "W-Air: Enabling Personal Air Pollution Monitoring on Wearables.", "authors": ["Balz Maag", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Accurate, portable and personal air pollution sensing devices enable quantification of individual exposure to air pollution, personalized health advice and assistance applications. Wearables are promising (e.g., on wristbands, attached to belts or backpacks) to integrate commercial off-the-shelf gas sensors for personal air pollution sensing. Yet previous research lacks comprehensive investigations on the accuracies of air pollution sensing on wearables. In response, we proposed W-Air, an accurate personal multi-pollutant monitoring platform for wearables. We discovered that human emissions introduce non-linear interference when low-cost gas sensors are integrated into wearables, which is overlooked in existing studies. W-Air adopts a sensor-fusion calibration scheme to recover high-fidelity ambient pollutant concentrations from the human interference. It also leverages a neural network with shared hidden layers to boost calibration parameter training with fewer measurements and utilizes semi-supervised regression for calibration parameter updating with little user intervention. We prototyped W-Air on a wristband with low-cost gas sensors. Evaluations demonstrated that W-Air reports accurate measurements both with and without human interference and is able to automatically learn and adapt to new environments.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191756"}, {"primary_key": "3270941", "vector": [], "sparse_vector": [], "title": "HMC: Robust Privacy Protection of Mobility Data against Multiple Re-Identification Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the wide propagation of handheld devices, more and more mobile sensors are being used by end users on a daily basis. Those sensors could be leveraged to gather useful mobility data for city planners, business analysts and researches. However, gathering and exploiting mobility data raises many privacy threats. Sensitive information such as one's home or work place, hobbies, religious beliefs, political or sexual preferences can be inferred from the gathered data. In the last decade, Location Privacy Protection Mechanisms (LPPMs) have been proposed to protect user data privacy. However existing LPPMs fail at effectively protecting the users as most of them reason on local mobility features: micro-mobility (e.g., individual geographical coordinates) while ignoring higher level mobility features, which may allow attackers to discriminate between users. In this paper we propose HMC the first LPPM that reasons on the overall user mobility abstracted using heat maps. We evaluate HMC using four real mobility traces and multiple privacy and utility metrics. The results show that with HMC, across all the datasets 87% of mobile users are successfully protected against re-identification attacks, while others LPPMs only achieve a protection ranging from 43% to 79%. By considering only users protected with a high utility, the proportion of users stays high for HMC with 75%, while for others LPPMs it goes down to proportions between 4% and 43%.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264934"}, {"primary_key": "3270945", "vector": [], "sparse_vector": [], "title": "Using Autoencoders to Automatically Extract Mobility Features for Predicting Depressive States.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent studies have shown the potential of exploiting GPS data for passively inferring people's mental health conditions. However, feature extraction for characterizing human mobility remains a heuristic process that relies on the domain knowledge of the condition under consideration. Moreover, we do not have guarantees that these \"hand-crafted\" metrics are able to effectively capture mobility behavior of users. Indeed, informative emerging patterns in the data might not be characterized by them. This is also a complex and often time-consuming task, since it usually consists of a lengthy trial-and-error process. In this paper, we investigate the potential of using autoencoders for automatically extracting features from the raw input data. Through a series of experiments we show the effectiveness of autoencoder-based features for predicting depressive states of individuals compared to \"hand-crafted\" ones. Our results show that automatically extracted features lead to an improvement of the performance of the prediction models, while, at the same time, reducing the complexity of the feature design task. Moreover, through an extensive experimental performance analysis, we demonstrate the optimal configuration of the key parameters at the basis of the proposed approach.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264937"}, {"primary_key": "3270949", "vector": [], "sparse_vector": [], "title": "A Tale of Two Interactions: Inferring Performance in Hospitality Encounters from Cross-Situation Social Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "People behave differently in different situations. With the advances in ubiquitous sensing technologies, it is now easier to capture human behavior across multiple situations automatically and unobtrusively. We investigate human behavior across two situations that are ubiquitous in hospitality (job interview and reception desk) with the objective of inferring performance on the job. Utilizing a dataset of 338 dyadic interactions, played by students from a hospitality management school, we first study the connections between automatically extracted nonverbal cues, linguistic content, and various perceived variables of soft skills and performance in these two situations. A correlation analysis reveals connection between perceived variables and nonverbal cues displayed during job interviews, and perceived performance on the job. We then propose a computational framework, with nonverbal cues and linguistic style from the two interactions as features, to infer the perceived performance and soft skills in the reception desk situation as a regression task. The best inference performance, with R2 = 0.40, is achieved using a combination of nonverbal cues extracted from the reception desk setting and the human-rated interview scores. We observe that some behavioral cues (greater speaking turn duration and head nods) are positively correlated to higher ratings for all perceived variables across both situations. The best performance using verbal content is achieved by fusion of LIWC and Doc2Vec features with R2 = 0.25 for perceived performance. Our work has implications for the creation of behavioral training systems with focus on specific behaviors for hospitality students.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264939"}, {"primary_key": "3270954", "vector": [], "sparse_vector": [], "title": "Modeling and Forecasting the Popularity Evolution of Mobile Apps: A Multivariate Hawkes Process Approach.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Cao", "<PERSON><PERSON><PERSON>"], "summary": "In recent years, with the rapid development of mobile app ecosystem, the number and categories of mobile apps have grown tremendously. However, the global prevalence of mobile apps also leads to fierce competition. As a result, many apps will disappear. To thrive in this competitive app market, it is vital for app developers to understand the popularity evolution of their mobile apps, and inform strategic decision-making for better mobile app development. Therefore, it is significant and necessary to model and forecast the future popularity evolution of mobile apps. The popularity evolution of mobile apps is usually a long-term process, affected by various complex factors. However, existing works lack the capabilities to model such complex factors. To better understand the popularity evolution, in this paper, we aim to forecast the popularity evolution of mobile apps by incorporating complex factors, i.e., exogenous stimulis and endogenous excitations. Specifically, we propose a model based on the Multivariate Hawkes Process (MHP), which is an exogenous stimulis-driven self-exciting point process, to model the exogenous stimulis and endogenous excitations simultaneously. Extensive experimental studies on a real-world dataset from app store demonstrate that MHP outperforms the state-of-the-art methods regarding popularity evolution forecasting.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287060"}, {"primary_key": "3270961", "vector": [], "sparse_vector": [], "title": "AROMA: A Deep Multi-Task Learning Based Simple and Complex Human Activity Recognition Method Using Wearable Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Human activity recognition (HAR) is a promising research issue in ubiquitous and wearable computing. However, there are some problems existing in traditional methods: 1) They treat HAR as a single label classification task, and ignore the information from other related tasks, which is helpful for the original task. 2) They need to predesign features artificially, which are heuristic and not tightly related to HAR task. To address these problems, we propose AROMA (human activity recognition using deep multi-task learning). Human activities can be divided into simple and complex activities. They are closely linked. Simple and complex activity recognitions are two related tasks in AROMA. For simple activity recognition task, AROMA utilizes a convolutional neural network (CNN) to extract deep features, which are task dependent and non-handcrafted. For complex activity recognition task, AROMA applies a long short-term memory (LSTM) network to learn the temporal context of activity data. In addition, there is a shared structure between the two tasks, and the object functions of these two tasks are optimized jointly. We evaluate AROMA on two public datasets, and the experimental results show that AROMA is able to yield a competitive performance in both simple and complex activity recognitions.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214277"}, {"primary_key": "3270963", "vector": [], "sparse_vector": [], "title": "PrivacyShield: A Mobile System for Supporting Subtle Just-in-time Privacy Provisioning through Off-Screen-based Touch Gestures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Changyoung Koh", "<PERSON><PERSON>"], "summary": "Current in-situ privacy solution approaches are inadequate in protecting sensitive information. They either require extra configuration effort or lack the ability to configure user desired privacy settings. Based on in-depth discussions during a design workshop, we propose PrivacyShield, a mobile system for providing subtle just-in-time privacy provisioning. PrivacyShield leverages the screen I/O device (screen digitizer) of smartphones to recognize gesture commands, even when the phone's screen is turned off. Based on gesture command inputs, various privacy-protection policies can be configured on-the-fly. We develop a novel stroke-based approach to address the challenges in segmenting and recognizing gesture command inputs, which helps the system in achieving good usability and performance. PrivacyShield also provides developers with APIs to enable just-in-time privacy provisioning in their applications. We have implemented an energy efficient PrivacyShield prototype on the Android platform, including smartphones with and without a low-power co-processor. Evaluation results show that our gesture segmentation algorithm is fast enough for real-time performance (introducing less than 200ms processing latency) and accurate (achieving an accuracy of 95% for single-character gestures and 89% for even three-character gestures). We also build a non-touch-screen-based just-in-time privacy provisioning prototype called the wrist gesture method. We compare the performance of the two prototypes by doing a 6-week field study with 12 participants and show why a simplistic solution falls short in providing privacy configurations. We also report the participants' perceptions and reactions after the field study.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214279"}, {"primary_key": "3270965", "vector": [], "sparse_vector": [], "title": "When Virtual Reality Meets Internet of Things in the Gym: Enabling Immersive Interactive Machine Exercises.", "authors": ["<PERSON><PERSON><PERSON>", "Taiwoo Park", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the advent of immersive virtual reality (VR) head-mounted displays (HMD), we envision that immersive VR will revolutionize the personal fitness experience in our daily lives. Toward this vision, we present <PERSON><PERSON><PERSON><PERSON>, a virtual exercise assistant that is able to provide an immersive and interactive gym exercise experience to a user. JARVIS is enabled by the synergy between Internet of Things (IoT) and immersive VR. JARVIS employs miniature IoT sensing devices removably attachable to exercise machines to track a multitude of exercise information including exercise types, repetition counts, and progress within each repetition in real time. Based on the tracked exercise information, JARVIS shows the user the proper way of doing the exercise in the virtual exercise environment, thereby helping the user to better focus on the target muscle group. We have conducted both in-lab experiments and a pilot user study to evaluate the performance and effectiveness of JARVIS, respectively. Our in-lab experiments with fifteen participants show that JARVIS is able to segment exercise repetitions with an average accuracy of 97.96% and recognize exercise types with an average accuracy of 99.08%. Our pilot user study with ten participants shows statistically significant improvements in perceived enjoyment, competence, and usefulness with JARVIS compared to a traditional machine exercise setting (p &lt; 0.05). Finally, our surface electromyography (sEMG) signal analysis conducted during the pilot user study shows statistically significant improvement in terms of muscle activation (p &lt; 0.01), indicating the potential of JARVIS in providing an engaging and effective guidance for machine exercises.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214281"}, {"primary_key": "3270966", "vector": [], "sparse_vector": [], "title": "Visualizing Location Uncertainty on Mobile Devices: Cross-Cultural Differences in Perceptions and Preferences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Location uncertainty is often ignored but a key context parameter for location-based services. The standard way of visualizing location uncertainty on mobile devices is using a concentric circle. However, the impact of different visual variables (shape, size, boundary, middle dot, color) of this standard visualization on users is not well understood. There is a potential for misinterpretation, particularly across cultures. We ran a study that was previously conducted in Germany (N=32) in Sri Lanka (N=20) to investigate how users perceive different visualizations of location uncertainty on mobile devices. In particular, we investigated the impact of the four graphic dimensions, shape, boundary, middle dot and size. We identified consistencies and inconsistencies concerning perceptions of users regarding visualizations of location uncertainty across cultures. We also quantified the impact of different visualizations on the perception of users. Based on the consistencies between different visualizations and between the two cultures, we derived guidelines for visualizing location uncertainty that help developers in aligning location uncertainty with the perceptions of users. We also highlight the need for further research on cultural differences (and similarities) regarding how visualizations of location uncertainty impact the perceptions of users.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191762"}, {"primary_key": "3270967", "vector": [], "sparse_vector": [], "title": "RF Bandaid: A Fully-Analog and Passive Wireless Interface for Wearable Sensors.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a passive wireless RF sensor platform (RFSP), with only analog components, that harvests energy from an RF source and reflects data as a direct subcarrier modulation, thus making it battery free. A fully-analog architecture results in an ultra-low power device (under 200 μW) with a low component count, reducing the physical footprint. We envision such a platform to enable medical sensing systems that fit on a small bandaid like flexible structure, require no-battery, or charging and are able to provide continuous physiological monitoring. To realize this vision, we have developed and optimized a novel RF architecture that 1) directly maps sensor output to frequency modulation and transmits it to a remote receiver processing unit (RPU). This direct frequency mapping allows all further digitization and computation to be moved to the RPU -- reducing power and size requirements on the RFSP; 2) harvests energy from the carrier signal transmitted by a simple continuous wave transmitter, thereby requiring no batteries or supercap; and 3) uses backscatter to communicate with the RPU enabling ultra-low power requirements. The total power consumption of our prototype device leveraging this architecture was measured to be between 35 μW and 160 μW. We demonstrate that the RFSP can harvest sufficient power, sense, and communicate continuously without necessity for energy storage at a distance of 4 m from a transmitter emitting a 915 MHz continuous wave at 26 dBm (0.39 W). Prior backscatter systems typically have power budgets of 1 mW and require energy storage (battery or supercap), RFSP's sub 200 μW power consumption provides a significant improvement and longer range for a given TX power. To demonstrate applicability to real-world health sensing and the flexibility to adapt to different sensors, this paper presents results from breathing, heart rate, temperature, and sound sensing applications.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214282"}, {"primary_key": "3270968", "vector": [], "sparse_vector": [], "title": "FlowPut: Environment-Aware Interactivity for Tangible 3D Objects.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Tangible interaction has shown to be beneficial in a wide variety of scenarios since it provides more direct manipulation and haptic feedback. Further, inherently three-dimensional information is represented more naturally by a 3D object than by a flat picture on a screen. Yet, today's tangibles have often pre-defined form factors and limited input and output facilities. To overcome this issue, the combination of projection and depth cameras is used as a fast and flexible way of non-intrusively adding input and output to tangibles. However, tangibles are often quite small and hence the space for output and interaction on their surface is limited. Therefore, we propose FlowPut: an environment-aware framework that utilizes the space available on and around a tangible object for projected visual output. By means of an optimization-based layout approach, FlowPut considers the environment of the objects to avoid interference between projection and real-world objects. Moreover, we contribute an occlusion resilient object recognition and tracking for tangible objects based on their 3D model and a point-cloud based multi-touch detection, that allows sensing touches also on the side of a tangible. Flowput is validated through a series of technical experiments, a user study, and two example applications.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191763"}, {"primary_key": "3270975", "vector": [], "sparse_vector": [], "title": "Employing Consumer Wearables to Detect Office Workers&apos; Cognitive Load for Interruption Management.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Office workers' productivity and well-being are reduced by interruptions, especially if they occur during an inconvenient moment. Interruptions in phases of high cognitive load are more disruptive than in phases of low cognitive load. Based on an explorative study, we suppose the presence of social codes that signal office workers' interruptibility. We propose a system that utilizes the cognitive load of an office worker to indicate situations suitable for interruptions. The cognitive load is inferred from office workers' physiological state measured by a consumer smartwatch. The system adapts an externally mounted smart device to indicate if the office worker is interruptible. To predict the cognitive load, we trained a classifier with ten office workers and achieved an accuracy between 66% and 86%. In order to validate the classifier's accurateness in an office setting, we performed a verification study with five office workers: We systematically triggered interruptions for each subject over an interval of half a day of office work. The classifier was able to infer the level of cognitive load for three office workers. This result supports our hypotheses that inferring cognitive load using a consumer smartwatch is a viable concept.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191764"}, {"primary_key": "3270987", "vector": [], "sparse_vector": [], "title": "Cooperative Target Tracking and Signal Propagation Learning Using Mobile Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "S.<PERSON><PERSON><PERSON>"], "summary": "Target tracking refers to positioning mobile objects over time. The targets may be hospital patients, park visitors, mall shoppers, warehouse assets, etc. We consider a novel cooperative system to track targets, where a target carries low-cost RF tag which not only beacons its ID, but also receives and rebroadcasts beacons of tags within a certain hop away. Mobile sensors, equipped with localization and communication modules, are used to capture and forward the beacons to a server to track the targets. Such multi-hop approach greatly extends the sensing range of the mobile sensors, or equivalently, the beaconing range of the tags, leading to cost-effective deployment. We propose Mosent, a highly accurate multi-hop system using mobile sensors for target tracking. To account for complex signal propagation in different indoor and outdoor environment, we represent the received signal strength (RSS) matrix overcoming the assumption on propagation model. Given sensor locations, beacons detected by the sensors and RSS matrix, Mosent jointly considers temporal and spatial information to track targets using a modified particle filter. Mosent has an optional, independent and offline module to learn spatial signal propagation in terms of RSS matrix using cooperative mobile sensors equipped with beaconing transceivers. We have implemented Mosent and conducted extensive experiments. Our results show that Mosent achieves 4.37m and 9.46m tracking error in the campus and the shopping mall, respectively, which outperforms other state-of-the-art approaches with significantly lower tracking error (often by more than 30%).", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264946"}, {"primary_key": "3270991", "vector": [], "sparse_vector": [], "title": "Your Apps Give You Away: Distinguishing Mobile Users by Their App Usage Fingerprints.", "authors": ["<PERSON><PERSON>", "Runtong Li", "<PERSON>", "<PERSON>", "<PERSON>", "Pan Hui", "Li Su", "<PERSON><PERSON><PERSON>"], "summary": "Understanding mobile app usage has become instrumental to service providers to optimize their online services. Meanwhile, there is a growing privacy concern that users' app usage may uniquely reveal who they are. In this paper, we seek to understand how likely a user can be uniquely re-identified in the crowd by the apps she uses. We systematically quantify the uniqueness of app usage via large-scale empirical measurements. By collaborating with a major cellular network provider, we obtained a city-scale anonymized dataset on mobile app traffic (1.37 million users, 2000 apps, 9.4 billion network connection records). Through extensive analysis, we show that the set of apps that a user has installed is already highly unique. For users with more than 10 apps, 88% of them can be uniquely re-identified by 4 random apps. The uniqueness level is even higher if we consider when and where the apps are used. We also observe that user attributes (e.g., gender, social activity, and mobility patterns) all have an impact on the uniqueness of app usage. Our work takes the first step towards understanding the unique app usage patterns for a large user population, paving the way for further research to develop privacy-protection techniques and building personalized online services.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264948"}, {"primary_key": "3270995", "vector": [], "sparse_vector": [], "title": "Smartwatch-based Early Gesture Detection 8 Trajectory Tracking for Interactive Gesture-Driven Applications.", "authors": ["Tran Huy Vu", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The paper explores the possibility of using wrist-worn devices (specifically, a smartwatch) to accurately track the hand movement and gestures for a new class of immersive, interactive gesture-driven applications. These interactive applications need two special features: (a) the ability to identify gestures from a continuous stream of sensor data early--i.e., even before the gesture is complete, and (b) the ability to precisely track the hand's trajectory, even though the underlying inertial sensor data is noisy. We develop a new approach that tackles these requirements by first building a HMM-based gesture recognition framework that does not need an explicit segmentation step, and then using a per-gesture trajectory tracking solution that tracks the hand movement only during these predefined gestures. Using an elaborate setup that allows us to realistically study the table-tennis related hand movements of users, we show that our approach works: (a) it can achieve 95% stroke recognition accuracy. Within 50% of gesture, it can achieve a recall value of 92% for 10 novice users and 93% for 15 experienced users from a continuous sensor stream; (b) it can track hand movement during such strokeplay with a median accuracy of 6.2 cm.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191771"}, {"primary_key": "3270998", "vector": [], "sparse_vector": [], "title": "Unlock with Your Heart: Heartbeat-based Authentication on Commercial Mobile Phones.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Qing Gu"], "summary": "In this paper, we propose to use the vibration of the chest in response to the heartbeat as a biometric feature to authenticate the user on mobile devices. We use the built-in accelerometer to capture the heartbeat signals on commercial mobile phones. The user only needs to press the phone on his/her chest, and the system can identify the user within a few heartbeats. To reliably extract heartbeat features, we design a two-step alignment scheme that can handle the natural variability in human heart rates. We further use an adaptive template selection scheme to authenticate the user under different body postures and body states. Based on heartbeat signals collected on twenty users, the experimental results show that our method can achieve an authentication accuracy of 96.49% and the heartbeat features are stable over a period of three months.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264950"}, {"primary_key": "3270999", "vector": [], "sparse_vector": [], "title": "Sensing Behavioral Change over Time: Using Within-Person Variability Features from Mobile Sensing to Predict Personality Traits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Personality traits describe individual differences in patterns of thinking, feeling, and behaving (\"between-person\" variability). But individuals also show changes in their own patterns over time (\"within-person\" variability). Existing approaches to measuring within-person variability typically rely on self-report methods that do not account for fine-grained behavior change patterns (e.g., hour-by-hour). In this paper, we use passive sensing data from mobile phones to examine the extent to which within-person variability in behavioral patterns can predict self-reported personality traits. Data were collected from 646 college students who participated in a self-tracking assignment for 14 days. To measure variability in behavior, we focused on 5 sensed behaviors (ambient audio amplitude, exposure to human voice, physical activity, phone usage, and location data) and computed 4 within-person variability features (simple standard deviation, circadian rhythm, regularity index, and flexible regularity index). We identified a number of significant correlations between the within-person variability features and the self-reported personality traits. Finally, we designed a model to predict the personality traits from the within-person variability features. Our results show that we can predict personality traits with good accuracy. The resulting predictions correlate with self-reported personality traits in the range of r = 0.32, MAE = 0.45 (for Openness in iOS users) to r = 0.69, MAE = 0.55 (for Extraversion in Android users). Our results suggest that within-person variability features from smartphone data has potential for passive personality assessment.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264951"}, {"primary_key": "3271001", "vector": [], "sparse_vector": [], "title": "RF-Kinect: A Wearable RFID-based Approach Towards 3D Body Movement Tracking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hongbo Liu", "<PERSON><PERSON>"], "summary": "The rising popularity of electronic devices with gesture recognition capabilities makes the gesture-based human-computer interaction more attractive. Along this direction, tracking the body movement in 3D space is desirable to further facilitate behavior recognition in various scenarios. Existing solutions attempt to track the body movement based on computer version or wearable sensors, but they are either dependent on the light or incurring high energy consumption. This paper presents RF-Kinect, a training-free system which tracks the body movement in 3D space by analyzing the phase information of wearable RFID tags attached on the limb. Instead of locating each tag independently in 3D space to recover the body postures, RF-Kinect treats each limb as a whole, and estimates the corresponding orientations through extracting two types of phase features, Phase Difference between Tags (PDT) on the same part of a limb and Phase Difference between Antennas (PDA) of the same tag. It then reconstructs the body posture based on the determined orientation of limbs grounded on the human body geometric model, and exploits Kalman filter to smooth the body movement results, which is the temporal sequence of the body postures. The real experiments with 5 volunteers show that RF-Kinect achieves 8.7° angle error for determining the orientation of limbs and 4.4cm relative position error for the position estimation of joints compared with Kinect 2.0 testbed.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191773"}, {"primary_key": "3271020", "vector": [], "sparse_vector": [], "title": "Exploring Tangible Interactions with Radar Sensing.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Research has explored miniature radar as a promising sensing technique for the recognition of gestures, objects, users' presence and activity. However, within Human-Computer Interaction (HCI), its use remains underexplored, in particular in Tangible User Interface (TUI). In this paper, we explore two research questions with radar as a platform for sensing tangible interaction with the counting, ordering, identification of objects and tracking the orientation, movement and distance of these objects. We detail the design space and practical use-cases for such interaction which allows us to identify a series of design patterns, beyond static interaction, which are continuous and dynamic. With a focus on planar objects, we report on a series of studies which demonstrate the suitability of this approach. This exploration is grounded in both a characterization of the radar sensing and our rigorous experiments which show that such sensing is accurate with minimal training. With these techniques, we envision both realistic and future applications and scenarios. The motivation for what we refer to as Solinteraction, is to demonstrate the potential for radar-based interaction with objects in HCI and TUI.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287078"}, {"primary_key": "3271023", "vector": [], "sparse_vector": [], "title": "Inferring Mobility Relationship via Graph Embedding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Inferring social relationships from user location data has become increasingly important for real-world applications, such as recommendation, advertisement targeting, and transportation scheduling. Most existing mobility relationship measures are based on pairwise meeting frequency, that it, the more frequently two users meet (i.e., co-locate at the same time), the more likely that they are friends. However, such frequency-based methods suffer greatly from data sparsity challenge. Due to data collection limitation and bias in the real world (e.g., check-in data), the observed meeting events between two users might be very few. On the other hand, existing methods focus too much on the interactions between two users, but fail to incorporate the whole social network structure. For example, the relationship propagation is not well utilized in existing methods. In this paper, we propose to construct a user graph based on their spatial-temporal interactions and employ graph embedding technique to learn user representations from such a graph. The similarity measure of such representations can well describe mobility relationship and it is particularly useful to describe the similarity for user pairs with low or even zero meeting frequency. Furthermore, we introduce semantic information on meeting events by using point-of-interest (POI) categorical information. Additionally, when part of the social graph is available as friendship ground truth, we can easily encode such online social network information through a joint graph embedding. Experiments on two real-world datasets demonstrate the effectiveness of our proposed method.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264957"}, {"primary_key": "3271028", "vector": [], "sparse_vector": [], "title": "iDial: Enabling a Virtual Dial Plate on the Hand Back for Around-Device Interaction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chaocan <PERSON>"], "summary": "Smart wearable devices have become pervasive and are playing a more important role in our everyday lives. However, the small screen size and very few buttons make the interaction and control cumbersome and inconvenient. Previous solutions to mitigate this problem either require extra dedicated hardware, or instrument the user's fingers with special purpose sensors, limiting their real-life applications. We present iDial, a novel real time approach that enables a virtual dial plate on the hand back, extending the interaction beyond the small screen of wearable devices. iDial only employs the already built-in microphone and motion sensors of the commercial-off-the-shelf (COTS) device to facilitate interactions between user and wearable, without any extra hardware involved. The key idea is to exploit the acoustic signatures extracted from passive subtle acoustic signals to accurately recognize the virtual keys input on the skin of the hand back. We innovatively locate the virtual keys on the 4 pieces of metacarpal bones to significantly reduce the possibility of casual inputs. iDial also takes advantages of the motion sensor fusion already available inside the wearable to achieve robustness against the ambient noise and human voices efficiently. We design and implement iDial on the Samsung Gear S2 smartwatch. Our extensive experiments show that iDial is able to achieve an average recognition accuracy of 96.7%, and maintain high accuracies across varying user behaviors and different environments. iDial achieves a below 0.5s end-to-end latency with all the computations and processes happening at the cheap commodity wearable.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191787"}, {"primary_key": "3271037", "vector": [], "sparse_vector": [], "title": "Location Privacy-Preserving Data Recovery for Mobile Crowdsensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data recovery techniques such as compressive sensing are commonly used in mobile crowdsensing (MCS) applications to infer the information of unsensed regions based on data from nearby participants. However, the participants' locations are exposed when they report geo-tagged data to an application server. While there are considerable location protection approaches for MCS, they fail to maintain the correlation of sensory data, leading to the existence of unrecoverable data. None of the previous approaches can achieve both data recovery and data privacy preservation. We propose a novel location privacy-preserving data recovery method in this paper. Based on our discovery that the adjacency relations of non-zero elements are key to the missing data recovery in a crowdsensing data matrix, we design a correlation-preserving location obfuscation scheme to hide the participants' locations under effective camouflage. We also design an encrypted data recovery scheme based on the homomorphic encryption in order to avoid location privacy leakage from sensory data. Location obfuscation and data encryption preserve the participants' privacy, while the correlation-preserving and homomorphic properties of our method ensure data recovery accuracy. Evaluations of real-world datasets show that our privacy-preserving method can effectively obfuscate locations (e.g., yielding an average location distortion of 1.7km in a 2.4km x 4km area for successful location hiding), and it can efficiently achieve similar data recovery accuracy to compressive sensing (which has no privacy protection).", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264961"}, {"primary_key": "3271038", "vector": [], "sparse_vector": [], "title": "EIS: A Wearable Device for Epidermal American Sign Language Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tingrui Pan", "<PERSON>"], "summary": "American Sign Language (ASL) is widely used among hearing impaired individuals in English-speaking countries. Various technologies have been developed to perform ASL recognition, including optical signal sensing, electrical signal sensing, and mechanical signal sensing. However, wearable devices using those methods have bulky and complex sensing modules that lead to long-term discomfort as well as poor accuracy. In this paper, we present an epidermal-iontronic sensing (EIS)-based wearable device that wears on finger joints for 35 fingerspelling ASL recognitions (i.e., 26 alphabets from A to Z and 9 digits from one to nine). Compared to current on-market devices, such design is lighter, comfortable to wear and has better appearance according to user comments. When bending the finger, a physical contact forms between the ionic material and the epidermis of skin, leading to an electric double layer (EDL) established at the interface. Therefore, a significant capacitive change can be achieved with various finger gestures. By using Nafion as the ionic sensing material, we developed a sensing device to provide excellent flexibility and optical transparency. We used machine learning methods, such as neural networks to track and perform ASL recognition using the signals obtained from the designed device. The algorithm achieved a within-user accuracy of 99.6% and a cross-user accuracy of 76.1% when adapted the model to different users. This wearable device is low-cost and has broad potential to be integrated in future application of human-machine interactions (HMI), smart home controls, and nonverbal communications.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287080"}, {"primary_key": "3270838", "vector": [], "sparse_vector": [], "title": "ComNSense: Grammar-Driven Crowd-Sourcing of Point Clouds for Automatic Indoor Mapping.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, point clouds have been efficiently utilized for medical imaging, modeling urban environments, and indoor modeling. In this realm, several mobile platforms, such as Google Tango and Apple ARKit, have been released leveraging 3D mapping, augmented reality, etc. In modeling applications, these modern mobile devices opened the door for crowd-sourcing point clouds to distribute the overhead of data collection. However, uploading these large points clouds from resources-constrained mobile devices to the back-end servers consumes excessive energy. Accordingly, participation rates in such crowd-sensing systems can be negatively influenced. To tackle this challenge, this paper introduces our ComNSense approach that dramatically reduces the energy consumption of processing and uploading point clouds. To this end, ComNSense reports only a set of extracted geometrical data to the servers. To optimize the geometry extraction, ComNSense leverages formal grammars which encode design-time knowledge, i.e. structural information. To demonstrate the effectiveness of ComNSense, we performed several experiments of collecting point clouds from two different buildings to extract the walls location, as a case study. We also assess the performance of ComNSense relative to a grammar-free method. The results showed a significant reduction of the energy consumption while achieving a comparable detection accuracy.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191733"}, {"primary_key": "3270842", "vector": [], "sparse_vector": [], "title": "Is More Always Better?: Discovering Incentivized mHealth Intervention Engagement Related to Health Behavior Trends.", "authors": ["Nabil <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bonnie <PERSON>", "<PERSON>"], "summary": "Behavioral medicine is devoting increasing attention to the topic of participant engagement and its role in effective mobile health (mHealth) behavioral interventions. Several definitions of the term \"engagement\" have been proposed and discussed, especially in the context of digital health behavioral interventions. We consider that engagement refers to specific interaction and use patterns with the mHealth tools such as smartphone applications for intervention, whereas adherence refers to compliance with the directives of the health intervention, independent of the mHealth tools. Through our analysis of participant interaction and self-reported behavioral data in a college student health study with incentives, we demonstrate an example of measuring \"effective engagement\" as engagement behaviors that can be linked to the goals of the desired intervention. We demonstrate how clustering of one year of weekly health behavior self-reports generate four interpretable clusters related to participants' adherence to the desired health behaviors: healthy and steady, unhealthy and steady, decliners, and improvers. Based on the intervention goals of this study (health promotion and behavioral change), we show that not all app usage metrics are indicative of the desired outcomes that create effective engagement. As such, mHealth intervention design might consider eliciting not just more engagement or use overall, but rather, effective engagement defined by use patterns related to the desired behavioral outcome.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287031"}, {"primary_key": "3270843", "vector": [], "sparse_vector": [], "title": "SoundSignaling: Realtime, Stylistic Modification of a Personal Music Corpus for Information Delivery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Drawing inspiration from the notion of cognitive incongruence associated with <PERSON><PERSON><PERSON>'s famous experiment, from musical principles, and from the observation that music consumption on an individual basis is becoming increasingly ubiquitous, we present the SoundSignaling system -- a software platform designed to make real-time, stylistically relevant modifications to a personal corpus of music as a means of conveying information or notifications. In this work, we discuss in detail the system's technical implementation and its motivation from a musical perspective, and validate these design choices through a crowd-sourced signal identification experiment consisting of 200 independent tasks performed by 50 online participants. We then qualitatively discuss the potential implications of such a system from the standpoint of switch cost, cognitive load, and listening behavior by considering the anecdotal outcomes of a small-scale, in-the-wild experiment consisting of over 180 hours of usage from 6 participants. Through this work, we suggest a re-evaluation of the age-old paradigm of binary audio notifications in favor of a system designed to operate upon the relatively unexplored medium of a user's musical preferences.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287032"}, {"primary_key": "3270845", "vector": [], "sparse_vector": [], "title": "Discovering Smart Home Internet of Things Privacy Norms Using Contextual Integrity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The proliferation of Internet of Things (IoT) devices for consumer \"smart\" homes raises concerns about user privacy. We present a survey method based on the Contextual Integrity (CI) privacy framework that can quickly and efficiently discover privacy norms at scale. We apply the method to discover privacy norms in the smart home context, surveying 1,731 American adults on Amazon Mechanical Turk. For $2,800 and in less than six hours, we measured the acceptability of 3,840 information flows representing a combinatorial space of smart home devices sending consumer information to first and third-party recipients under various conditions. Our results provide actionable recommendations for IoT device manufacturers, including design best practices and instructions for adopting our method for further research.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214262"}, {"primary_key": "3270846", "vector": [], "sparse_vector": [], "title": "SATURN: A Thin and Flexible Self-powered Microphone Leveraging Triboelectric Nanogenerator.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We demonstrate the design, fabrication, evaluation, and use of a self-powered microphone that is thin, flexible, and easily manufactured. Our technology is referred to as a Self-powered Audio Triboelectric Ultra-thin Rollable Nanogenerator (SATURN) microphone. This acoustic sensor takes advantage of the triboelectric nanogenerator (TENG) to transform vibrations into an electric signal without applying an external power source. The sound quality of the SATURN mic, in terms of acoustic sensitivity, frequency response, and directivity, is affected by a set of design parameters that we explore based on both theoretical simulation and empirical evaluation. The major advantage of this audio material sensor is that it can be manufactured simply and deployed easily to convert every-day objects and physical surfaces into microphones which can sense audio. We explore the space of potential applications for such a material as part of a self-sustainable interactive system.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214263"}, {"primary_key": "3270849", "vector": [], "sparse_vector": [], "title": "Understanding the Long-Term Use of Smart Speaker Assistants.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Over the past two years the Ubicomp vision of ambient voice assistants, in the form of smart speakers such as the Amazon Echo and Google Home, has been integrated into tens of millions of homes. However, the use of these systems over time in the home has not been studied in depth. We set out to understand exactly what users are doing with these devices over time through analyzing voice history logs of 65,499 interactions with existing Google Home devices from 88 diverse homes over an average of 110 days. We found that specific types of commands were made more often at particular times of day and that commands in some domains increased in length over time as participants tried out new ways to interact with their devices, yet exploration of new topics was low. Four distinct user groups also emerged based on using the device more or less during the day vs. in the evening or using particular categories. We conclude by comparing smart speaker use to a similar study of smartphone use and offer implications for the design of new smart speaker assistants and skills, highlighting specific areas where both manufacturers and skill providers can focus in this domain.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264901"}, {"primary_key": "3270850", "vector": [], "sparse_vector": [], "title": "Auracle: Detecting Eating Episodes with an Ear-mounted Sensor.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we propose <PERSON><PERSON><PERSON>, a wearable earpiece that can automatically recognize eating behavior. More specifically, in free-living conditions, we can recognize when and for how long a person is eating. Using an off-the-shelf contact microphone placed behind the ear, <PERSON><PERSON><PERSON> captures the sound of a person chewing as it passes through the bone and tissue of the head. This audio data is then processed by a custom analog/digital circuit board. To ensure reliable (yet comfortable) contact between microphone and skin, all hardware components are incorporated into a 3D-printed behind-the-head framework. We collected field data with 14 participants for 32 hours in free-living conditions and additional eating data with 10 participants for 2 hours in a laboratory setting. We achieved accuracy exceeding 92.8% and F1 score exceeding 77.5% for eating detection. Moreover, <PERSON><PERSON><PERSON> successfully detected 20-24 eating episodes (depending on the metrics) out of 26 in free-living conditions. We demonstrate that our custom device could sense, process, and classify audio data in real time. Additionally, we estimate <PERSON><PERSON><PERSON> can last 28.1 hours with a 110 mAh battery while communicating its observations of eating behavior to a smartphone over Bluetooth.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264902"}, {"primary_key": "3270851", "vector": [], "sparse_vector": [], "title": "Sprintz: Time Series Compression for the Internet of Things.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Thanks to the rapid proliferation of connected devices, sensor-generated time series constitute a large and growing portion of the world's data. Often, this data is collected from distributed, resource-constrained devices and centralized at one or more servers. A key challenge in this setup is reducing the size of the transmitted data without sacrificing its quality. Lower quality reduces the data's utility, but smaller size enables both reduced network and storage costs at the servers and reduced power consumption in sensing devices. A natural solution is to compress the data at the sensing devices. Unfortunately, existing compression algorithms either violate the memory and latency constraints common for these devices or, as we show experimentally, perform poorly on sensor-generated time series. We introduce a time series compression algorithm that achieves state-of-the-art compression ratios while requiring less than 1KB of memory and adding virtually no latency. This method is suitable not only for low-power devices collecting data, but also for servers storing and querying data; in the latter context, it can decompress at over 3GB/s in a single thread, even faster than many algorithms with much lower compression ratios. A key component of our method is a high-speed forecasting algorithm that can be trained online and significantly outperforms alternatives such as delta coding. Extensive experiments on datasets from many domains show that these results hold not only for sensor data but also across a wide array of other time series.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264903"}, {"primary_key": "3270853", "vector": [], "sparse_vector": [], "title": "Controlling Fine-Grain Sharing in Natural Language with a Virtual Assistant.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper proposes a novel approach to let consumers share data from their existing web accounts and devices easily, securely, and with fine granularity of control. Our proposal is to have our personal virtual assistant be responsible for sharing our digital assets. The owner can specify fine-grain access control in natural language; the virtual assistant executes access requests on behalf of the requesters and returns the results, if the requests conform to the owner's access control policies. Specifically, we allow a virtual assistant to share any ThingTalk command--an event-driven task composed of skills drawn from Thingpedia, a crowdsourced repository with over 200 functions currently. Access control in natural language is translated into TACL, a formal language we introduce to let users express for whom, what, when, where, and how ThingTalk commands can be executed. TACL policies are in turn translated into SMT (Satisfiability Modulo Theories) formulas and enforced using a provably correct algorithm. Our Distributed ThingTalk Protocol lets users access their own and others' data through their own virtual assistant, while enabling sharing without disclosing information to a third party. The proposed ideas have been incorporated and released in the open-source Almond virtual assistant. 18 of the 20 users in a study say that they like the concept proposed, and 14 like the prototype. We show that users are more willing to share their data given the ability to impose TACL constraints, that 90% of enforceable use cases suggested by 60 users are supported by TACL, and that static and dynamic conformance of policies can be enforced efficiently.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264905"}, {"primary_key": "3270854", "vector": [], "sparse_vector": [], "title": "Revisitation in Urban Space vs. Online: A Comparison across POIs, Websites, and Smartphone Apps.", "authors": ["Hancheng Cao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the first large-scale analysis of POI revisitation patterns, which aims to model the periodic behavior in human mobility. We apply the revisitation analysis technique, which has previously been used to understand website revisitation, and smartphone app revisitations. We analyze a 1.5-year-long Foursquare check-in dataset with 266,909 users in 415 cities around the globe, as well as a Chinese social networking dataset on continuous localization of 15,000 users in Beijing. Our analysis identifies four major POI revisitation patterns and four user revisitation patterns of distinct characteristics, and demonstrates the role of POI functions and geographic constraints in shaping these patterns. We compare our results to previous analysis on website and app revisitation, and highlight the similarities and differences between physical and cyber revisitation activities. These point to fundamental characteristics of human behavior.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287034"}, {"primary_key": "3270855", "vector": [], "sparse_vector": [], "title": "Uniqueness in the City: Urban Morphology and Location Privacy.", "authors": ["Hancheng Cao", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate the potential for privacy leaks when users reveal their nearby Points-of-Interest (POIs). Specifically, we investigate whether and how a person's location can be reverse-engineered when that person simply reveals their nearby POI types (e.g. 2 schools and 3 restaurants). We approach our analysis by introducing a \"Location Re-identification\" algorithm that is computationally efficient. Using data from Open Street Map, we conduct our analysis on datasets of multiple representative cities: New York City, Melbourne, Vancouver, Zurich and Shanghai. Our analysis indicates that urban morphology has a clear link to location privacy, and highlights a number of urban factors that contribute to location privacy. Our findings can be used in any systems or platforms where users reveal their proximal POIs, such as recommendation systems, advertising platforms, and appstores.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214265"}, {"primary_key": "3270856", "vector": [], "sparse_vector": [], "title": "Amateur: Augmented Reality Based Vehicle Navigation System.", "authors": ["<PERSON>", "Zhenjiang Li", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents Amateur, an augmented reality based vehicle navigation system using commodity smart phones. Amateur reads the navigation information from a digital map, matches it into live road condition video captured by smart phone, and directly annotates the navigation instructions on the video stream. The Amateur design entails two major challenges, including the lane identification and the intersection inference so as to correctly annotate navigation instructions for lane-changing and intersection-turning. In this paper, we propose a particle filter based design, assisted by inertial motion sensors and lane markers, to tolerate incomplete and even erroneous detection of road conditions. We further leverage traffic lights as land markers to estimate the position of each intersection to accurately annotate the navigation instructions. We develop a prototype system on Android mobile phones and test our system in a total number of more than 300km travel distance on different taxi cabs in a city. The evaluation results suggest that our system can timely provide correct instructions to navigate drivers. Our system can identify lanes in 2s with 92.7% accuracy and detect traffic lights with 95.29% accuracy. Overall, the accuracy of the navigation signs placement is less than 105 pixels on the screen throughout the experiments. The feedback from 50 taxi drivers indicates that Amateur provides an improved experience compared to traditional navigation systems.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287033"}, {"primary_key": "3270857", "vector": [], "sparse_vector": [], "title": "Enabling Public Cameras to Talk to the Public.", "authors": ["<PERSON><PERSON> Cao", "<PERSON>"], "summary": "This paper asks: Is it possible for cameras in public areas, say ceiling cameras in a museum, to send personalized messages to people without knowing any addresses of their phones? We define this kind of problem as Private Human Addressing and develop a real-time end-to-end system called PHADE to solve it. Unlike traditional data transmission protocols that need to first learn the destination's address, our cameras rely on viewing user's motion patterns, and use the uniqueness of these patterns as the address for communication. Once receiving the wireless broadcast from the cameras, the user's phone can locally compare the \"motion address\" of the packet against its own motion sensor data, and accept the packet upon a \"good\" match. In addition to requiring no data from users, our system transforms the motion patterns into low-dimensional codes to prevent leakage of user's walking behaviors. Thus, a hacker who collects all the broadcast messages would still not be able to infer the motion patterns of users. Real-world experiments show that PHADE discriminates 2, 4, 6, 8, 10 people with 98%, 95%, 90%, 90%, 87% correctness and about 3 seconds constant delay. Since abundant and accurate information can be extracted from videos, PHADE would find applications in various contexts. Extended to localization system and audio guide, PHADE achieves a median error of 0.19m and 99.7% matching correctness, respectively. PHADE can also deliver messages based on human gestures. There is no need to deploy any extra infrastructures or to require users to rent any dedicated device.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214266"}, {"primary_key": "3270858", "vector": [], "sparse_vector": [], "title": "Naptics: Convenient and Continuous Blood Pressure Monitoring during Sleep.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Normal circadian rhythm mediates blood pressure during sleep, decreasing in value in healthy subjects. Current methods to monitor nocturnal blood pressure use an active blood pressure cuff that repeatedly auto-inflates while the subject sleeps. Since these inflations happen in intervals of thirty minutes to one hour, they cause considerable sleep disturbances that lead to false measurements and impact the person's quality of sleep. These blood pressure samples are also just spot checks and rarely exceed 10-15 values per night. We present Naptics, a wearable device woven into shorts. Naptics passively monitors the wearer's blood pressure throughout the night---continuously and unobtrusively---without disturbing the user during sleep. Naptic<PERSON> detects the micro-vibrations of the wearer's body that stem from the heartbeat and senses the optical reflections from the pulse wave as it propagates down the wearer's leg. From the timing between these two events, Naptics computes the pulse transit time, which correlates strongly with the user's blood pressure. Naptics' key novelty is its unobtrusive approach in tracking blood pressure during the night. Our controlled evaluation of six subjects showed a high correlation (r = 0.89) between Naptic<PERSON>' calibrated mean arterial pressure and cuff-based blood pressure. Our in-the-wild evaluation validates Naptics in tracking five participants' blood pressure patterns throughout four nights and compares them to before and after cuff measurements. In a majority of the nights, Naptics correctly followed the trend of the cuff measurements while providing insights into the behavior and the patterns of participants' nocturnal blood pressure. Participants reported high sleep quality in sleep diaries after each night, validating Naptics as a convenient monitoring apparatus.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264906"}, {"primary_key": "3270860", "vector": [], "sparse_vector": [], "title": "ID&apos;em: Inductive Sensing for Embedding and Extracting Information in Robust Materials.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Khai N. <PERSON>", "<PERSON>"], "summary": "We present ID'em, a novel tagging and localization method that employs an array of Inductive Sensors to 'image' patterns of electrically conductive dots that are embedded underneath the surfaces of materials that cover the environments that we inhabit. ID'em addresses drawbacks found with existing tagging/localization technologies, while drawing on some of their attributes and strengths, thus creating a cost-effective, scalable system that is robust enough to be deployed pervasively. We present a detailed description of the system, applications that leverage ID'em's unique affordances, and address ID'em's strengths and limitations. With ID'em, we envision a future where the materials that we use to cover and build our everyday environments come imbued with information that can provide valuable context for rich, diverse interactions and capabilities.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264907"}, {"primary_key": "3270861", "vector": [], "sparse_vector": [], "title": "Students&apos; Experiences with Ecological Momentary Assessment Tools to Report on Emotional Well-being.", "authors": ["<PERSON>", "Vedant <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ecological Momentary Assessment (EMA) methods have emerged as an approach that enhances the ecological validity of data collected for the study of human behavior and experience. In particular, EMA methods are used to capture individuals' experiences (e.g., symptoms, affect, and behaviors) in real-world contexts and in near-real time. However, work investigating participants' experiences in EMA studies and in particular, how these experiences may influence the collected data, is limited. We conducted in-depth focus groups with 32 participants following an EMA study on mental well-being in college students. In doing so, we probed how the elicitation of high-quality, reflective responses is related to the design of EMA interactions. Through our study, we distilled three primary considerations for designing EMA interactions, based on observations of 1) response strategies to repeated questions, 2) the perceived burden of EMA prompts, and 3) challenges to the validity and robustness of EMA data. We present these considerations in the context of two microinteraction-based EMA approaches that we tested: lock-screen EMA and image-based question prompts. We conclude by characterizing design tensions in the presentation and delivery of EMA prompts, and outline directions for future work to address these tensions.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191735"}, {"primary_key": "3270862", "vector": [], "sparse_vector": [], "title": "SleepGuard: Capturing Rich Sleep Information Using Smartwatch Sensing Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Xiao<PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Sleep is an important part of our daily routine -- we spend about one-third of our time doing it. By tracking sleep-related events and activities, sleep monitoring provides decision support to help us understand sleep quality and causes of poor sleep. Wearable devices provide a new way for sleep monitoring, allowing us to monitor sleep from the comfort of our own home. However, existing solutions do not take full advantage of the rich sensor data provided by these devices. In this paper, we present the design and development of SleepGuard, a novel approach to track a wide range of sleep-related events using smartwatches. We show that using merely a single smartwatch, it is possible to capture a rich amount of information about sleep events and sleeping context, including body posture and movements, acoustic events, and illumination conditions. We demonstrate that through these events it is possible to estimate sleep quality and identify factors affecting it most. We evaluate our approach by conducting extensive experiments involved fifteen users across a 2-week period. Our experimental results show that our approach can track a richer set of sleep events, provide better decision support for evaluating sleep quality, and help to identify causes for sleep problems compared to prior work.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264908"}, {"primary_key": "3270863", "vector": [], "sparse_vector": [], "title": "Performance Characterization of Deep Learning Models for Breathing-based Authentication on Resource-Constrained Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Suranga Seneviratne", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Providing secure access to smart devices such as smartphones, wearables and various other IoT devices is becoming increasingly important, especially as these devices store a range of sensitive personal information. Breathing acoustics-based authentication offers a highly usable and possibly a secondary authentication mechanism for secure access. Executing sophisticated machine learning pipelines for such authentication on such devices remains an open problem, given their resource limitations in terms of storage, memory and computational power. To investigate this challenge, we compare the performance of an end-to-end system for both user identification and user verification tasks based on breathing acoustics on three type of smart devices: smartphone, smartwatch and Raspberry Pi using both shallow classifiers (i.e., SVM, GMM, Logistic Regression) and deep learning based classifiers (e.g., LSTM, MLP). Via detailed analysis, we conclude that LSTM models for acoustic classification are the smallest in size, have the lowest inference time and are more accurate than all other compared classifiers. An uncompressed LSTM model provides an average f-score of 80%-94% while requiring only 50--180 KB of storage (depending on the breathing gesture). The resulting inference can be done on smartphones and smartwatches within approximately 7--10 ms and 18--66 ms respectively, thereby making them suitable for resource-constrained devices. Further memory and computational savings can be achieved using model compression methods such as weight quantization and fully connected layer factorization: in particular, a combination of quantization and factorization achieves 25%--55% reduction in LSTM model size, with almost no loss in performance. We also compare the performance on GPUs and show that the use of GPU can reduce the inference time of LSTM models by a factor of 300%. These results provide a practical way to deploy breathing based biometrics, and more broadly LSTM-based classifiers, in future ubiquitous computing applications.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287036"}, {"primary_key": "3270864", "vector": [], "sparse_vector": [], "title": "CrowdX: Enhancing Automatic Construction of Indoor Floorplan with Opportunistic Encounters.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The lack of floorplan limits the spread of pervasive indoor location-based services. Existing crowdsourcing based approaches mostly rely on identifying, locating landmarks in the environment and utilizing the spatial relationship between the landmarks and traces for efficiently constructing fine-grained floorplan. However, these methods are always restricted by the sparse landmark distribution or may cause privacy leakage. In this paper, we propose CrowdX, a crowdsourcing system for accurate, low-cost indoor floorplan construction enhanced with opportunistic encounters among mobile users. The key insight is that the spatial relation (i.e., the displacement of each user and the distance between each other during the encounter) will be extracted from the audio and inertia data, which are aligned by the proposed vibration event-based method. Such information can be used to calibrate the drift of encounter position. The calibrated encounter position is beneficial to most of the floorplan generation steps, such as trace drift elimination, landmark positioning, hallway assembling, and room area estimation. Our experiments in three shopping malls show that CrowdX achieves an average F-measure around 89.4%. In addition, the average estimated room area error within about 20%. The evaluation results demonstrate a significant improvement of accuracy enhanced with opportunistic encounters.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287037"}, {"primary_key": "3270865", "vector": [], "sparse_vector": [], "title": "ParkLoc: Light-weight Graph-based Vehicular Localization in Parking Garages.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Locating a vehicle indoors (e.g., underground parking garages) has been a difficult problem to tackle, due to the unavailability of GPS and/or Wi-Fi signals. Current GPS-free indoor localization efforts often rely on infrastructure supports such as Wi-Fi or BLE beacons, whereas the smartphone-only proposals mostly require significant data collection and training efforts per garage. In this context, we propose ParkLoc, a novel lightweight smartphone-only solution for vehicular localization in GPS/Wi-Fi-deprived environments such as indoor parking garages. ParkLoc exploits the inherent planar graph structure of the navigable paths in parking facilities, in order to match a vehicle trajectory onto a sub-section of the map, by modeling these as sparse directed graphs. Exploiting an approximate graph matching method, ParkLoc is able to track a vehicle in real-time with a median error of 4.8m and localize a parked vehicle with a median error of 2m from the nearest parking space. Furthermore, ParkLoc adopts the popular GraphSLAM algorithm from robotics research; it learns the map graph from the observed trajectory graphs and a given set of bootstrap (seed) landmark nodes, in a semi-supervised manner. A key benefit of our approach is that ParkLoc works off-the-shelf without any expensive on-site training or sensor data collection per garage. A comprehensive evaluation of ParkLoc through extensive experiments performed in 4 different parking facilities reveals the promising performance of our graph-based approach for both localization and mapping.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264909"}, {"primary_key": "3270867", "vector": [], "sparse_vector": [], "title": "Anatomy of a Vulnerable Fitness Tracking System: Dissecting the Fitbit Cloud, App, and Firmware.", "authors": ["Jiska Classen", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fitbit fitness trackers record sensitive personal information, including daily step counts, heart rate profiles, and locations visited. By design, these devices gather and upload activity data to a cloud service, which provides aggregate statistics to mobile app users. The same principles govern numerous other Internet-of-Things (IoT) services that target different applications. As a market leader, Fitbit has developed perhaps the most secure wearables architecture that guards communication with end-to-end encryption. In this article, we analyze the complete Fitbit ecosystem and, despite the brand's continuous efforts to harden its products, we demonstrate a series of vulnerabilities with potentially severe implications to user privacy and device security. We employ a range of techniques, such as protocol analysis, software decompiling, and both static and dynamic embedded code analysis, to reverse engineer previously undocumented communication semantics, the official smartphone app, and the tracker firmware. Through this interplay and in-depth analysis, we reveal how attackers can exploit the Fitbit protocol to extract private information from victims without leaving a trace, and wirelessly flash malware without user consent. We demonstrate that users can tamper with both the app and firmware to selfishly manipulate records or circumvent Fitbit's walled garden business model, making the case for an independent, user-controlled, and more secure ecosystem. Finally, based on the insights gained, we make specific design recommendations that can not only mitigate the identified vulnerabilities, but are also broadly applicable to securing future wearable system architectures.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191737"}, {"primary_key": "3270868", "vector": [], "sparse_vector": [], "title": "Comparing a Single-Touch Whiteboard and a Multi-Touch Tabletop for Collaboration in School Museum Visits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper explores two important classes of large screen displays, single-touch whiteboards and multi-touch tabletops, for the context of collaborative learning by school groups at a museum. To do this, we designed MuseWork, as a worksheet activity, with two phases: first, students explore the museum, individually or in pairs, guided by our tablet worksheet app; then, in groups, they collaborate to create a poster at a large-screen display, using our device-customised MuseWork interfaces. Our goal was to gain insights about the implications for engagement and collaboration when groups use these devices; single-touch whiteboards are important as they are widely available in classrooms and multi-touch tabletops are an emerging technology. Our research questions asked: 1) whether MuseWork enabled groups to complete the collaborative task at both devices and 2) how the whiteboard and tabletop each affect key aspects of collaboration. We report a between-subjects study of 67 students, aged 10--14 years, from 2 schools, in 12 groups. Our results, based on qualitative and quantitative data, indicate the MuseWork interface for each device proved effective, with groups completing the activity and satisfied with the result and the experience (RQ1). Comparisons of groups using each device (RQ2) give new insights in terms of the products of the collaborative activity, and the strategies groups spontaneously developed for group co-ordination and device use. Our contributions are insights from the first in-the-field study of children collaborating at single-touch interactive whiteboards and multi-touch tabletops.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191738"}, {"primary_key": "3270870", "vector": [], "sparse_vector": [], "title": "Investigating the Effectiveness of Cohort-Based Sleep Recommendations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Existing sleep-tracking apps and devices provide simple descriptive statistics or generic recommendations for everyone. In this work, we aim to leverage cohort-based sleep data to provide recommendations to improve an individual's sleep. We report a 4-week study (N = 39) conducted to compare three alternatives: 1) no recommendation, 2) general recommendation, and 3) cohort-based recommendation, using six sleep quality metrics. For the cohort-based recommendation, recommendations were generated based on \"similar users\" using about 40 million sleep events from Microsoft Band users. Our results indicate that cohort-based systems for health recommendations can prompt a desire for behavior change inspired by social comparison and increased awareness about sleep habits. However, in order to be effective, such systems need to establish their credibility and to be able to generate cohorts based on features that are important to users. Finally, we provide further suggestions and design implications for future cohort-based recommendation systems for healthy sleep.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264911"}, {"primary_key": "3270872", "vector": [], "sparse_vector": [], "title": "Detecting Door Events Using a Smartphone via Active Sound Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Event detection of indoor objects, including doors, has a wide variety of applications, including intruder detection, HVAC control, and surveillance of independently living elderly people. Hence, this has been the focus of multiple research projects in the UbiComp research community. Herein, we propose a method to accurately detect door events in an indoor environment, without the installation and maintenance costs of using distributed ubiquitous sensors. In particular, we recognize the events of multiple doors existing in the environment via active sound probing using a disused smartphone installed in the environment. We perform event recognition by fusing the analysis of the Doppler shift caused by the moving doors with the acoustic characteristics describing the open/close states of the doors acquired via impulse response. To accurately distinguish between the events of different doors via sound probing, our method employs the time-series analysis of the Doppler shift as well as the active sound probing using directional high-frequency sine waves and stereo sound recording. In addition, by incorporating prior knowledge about the state transitions of a door object into a recognition model, we attempt to improve the accuracy of event recognition. Moreover, our method is capable of recognizing walking activities of a person related to door events in the environment, which are necessary information for applications such as HVAC control that require information about both door events and human presence.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287038"}, {"primary_key": "3270873", "vector": [], "sparse_vector": [], "title": "MuscleIO: Muscle-Based Input and Output for Casual Notifications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Receiving and reacting to notifications on mobile devices can be cumbersome. We propose MuscleIO, the use of electrical muscle stimulation (EMS) for notification output and electromyography (EMG) for reacting to notifications. Our approach provides a one-handed, eyes-free, and low-effort way of dealing with notifications. We built a prototype that interleaves muscle input and muscle output signals using the same electrodes. EMS and EMG alternate such that the EMG input signal is measured in the gaps of the EMS output signal, so voluntary muscle contraction is measured during muscle stimulation. Notifications are represented as EMS signals and are accepted or refused either by a directional or a time-based EMG response. A lab user study with 12 participants shows that the directional EMG response is superior to the time-based response in terms of reaction time, error rate, and user preference. Furthermore, the directional approach is the fastest and the most intuitive for users compared to a button-based smartwatch interface as a baseline.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214267"}, {"primary_key": "3270875", "vector": [], "sparse_vector": [], "title": "Energy-Ball: Wireless Power Transfer for Batteryless Internet of Things through Distributed Beamforming.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Wireless power transfer (WPT) promises to deliver energy to devices that are otherwise hard to charge or replace batteries for. This paper presents a new power transfer approach by aligning the phases of a collection of radio frequency (RF) energy chargers at the target receiver device. Our approach can ship energy over tens of meters and to mobile targets. More importantly, our approach leads to a highly asymmetric energy density distribution in the charging area: the energy density at the target receiver is much higher than the energy density at other locations. It is a departure from existing beamforming based WPT systems that have high energy along the energy beam path. Such a technology can enable a large array of batteryless Internet of Things applications and render them much more robust and long-running. Thanks to its asymmetric energy distribution, our approach potentially can be scaled up to ship higher level of energy over longer distances. In this paper, we design, prototype, and evaluate the proposed energy transfer approach, referred to as Energy-Ball. We implement an Energy-Ball testbed that consists of 17 N210 and 4 B210 Universal Software Radio Peripheral (USRP) nodes, yielding a 20 x 20 m2 energy delivery area. We conduct carefully designed experiments on the testbed. We demo that the energy density of Energy-Ball at the target spot is considerably higher than the energy density elsewhere, with the peak to average power ratio of 8.72. We show that Energy-Ball can transfer energy to any point within the area. When the receiver moves at a speed of 0.5 m/s, Energy-Ball can transfer 80% of optimal power to the mobile receiver. Further, our results also show Energy-Ball can deliver over 0.6mw RF power that enables batteryless sensors at any point across the area.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214268"}, {"primary_key": "3270876", "vector": [], "sparse_vector": [], "title": "What is That in Your Hand?: Recognizing Grasped Objects via Forearm Electromyography Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Knowing the object in hand can offer essential contextual information revealing a user's fine-grained activities. In this paper, we investigate the feasibility, accuracy, and robustness of recognizing the uninstrumented object in a user's hand by sensing and decoding her forearm muscular activities via off-the-shelf electromyography (EMG) sensors. We present results from three studies to advance our fundamental understanding of the opportunities that EMG brings in object interaction recognition. In the first study, we investigated the influence of physical properties of objects such as shape, size, and weight on EMG signals. We also conducted a thorough exploration of the feature spaces and sensor positions which can provide a solid base to rely on for future designers and practitioners for such interactive technique. In the second study, we assessed the feasibility and accuracy of inferring the types of grasped objects via using forearm muscular activity as a cue. Our results indicate that the types of objects can be recognized with up to 94.2% accuracy by employing user-dependent training. In the third study, we investigated the robustness of this approach in a realistic office setting where users were allowed to interact with objects as they would naturally. Our approach achieved up to 82.5% accuracy in discriminating 15 types of objects, even when training and testing phrases were purposefully performed on different days to incorporate changes in EMG patterns over time. Overall, this work contributes a set of fundamental findings and guidelines on using EMG technologies for object-based activity tracking.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287039"}, {"primary_key": "3270877", "vector": [], "sparse_vector": [], "title": "TagFree Activity Identification with RFIDs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human activity identification plays a critical role in many Internet-of-Things applications, which is typically achieved through attaching tracking devices, e.g., RFID tags, to human bodies. The attachment can be inconvenient and considered intrusive. A tag-free solution instead deploys stationary tags as references, and analyzes the backscattered signals that could be affected by human activities in close proximity. The information offered by today's RFID tags however are quite limited, and the typical raw data (RSSI and phase angles) are not necessarily good indicators of human activities (being either insensitive or unreliable as revealed by our realworld experiments). As such, existing tag-based activity identification solutions are far from being satisfactory, not to mention tag-free. It is also well known that the accuracy of the readings can be noticeably affected by multipath, which unfortunately is inevitable in an indoor environment and is complicated with multiple reference tags. In this paper, we however argue that multipath indeed brings rich information that can be explored to identify fine-grained human activities. Our experiments suggest that both the backscattered signal power and angle are correlated with human activities, impacting multiple paths with different levels. We present TagFree, the first RFID-based device-free activity identification system by analyzing the multipath signals. Different from conventional solutions that directly rely on the unreliable raw data, TagFree gathers massive angle information as spectrum frames from multiple tags, and preprocesses them to extract key features. It then analyzes their patterns through a deep learning framework. Our TagFree is readily deployable using off-the-shelf RFID devices and a prototype has been implemented using a commercial Impinj reader. Our extensive experiments demonstrate the superiority of our TagFree on activity identification in multipath-rich environments.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191739"}, {"primary_key": "3270879", "vector": [], "sparse_vector": [], "title": "MultiCell: Urban Population Modeling Based on Multiple Cellphone Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Exploring cellphone network data has been proved to be a very effective way to understand urban populations because of the high penetration rate of cellphones. However, the state-of-the-art population models driven by cellphone data are typically built upon single cellphone networks, assuming the users in a particular cellphone network used are representative of all residents in the studied city with multiple cellphone networks. This assumption usually does not hold in the real world due to strategic spatial coverages and business concentrations of cellphone companies, which lead to data biases, and thus overfitting of resultant population models. To address this issue, we design a model called MultiCell to model real-time urban populations from multiple cellphone networks with two novel techniques: (i) a network realignment technique to integrate individual cell-tower spatial distributions from multiple cellphone networks for finer granular population modeling; (ii) a data fusion technique based on cross-network training to design a population model based on multiple network data. We implement MultiCell in the Chinese city Shenzhen based on three cellphone networks with 10 million active users and their daily data records at 11 thousand cell towers. We evaluate MultiCell by comparing it to the state-of-the-art models driven by single cellphone networks, and the evaluation results show that MultiCell outperforms them by 27% in terms of accuracy. Finally, we cross-validate MultiCell with three transportation systems with more than 8 million passengers to investigate its performances.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264916"}, {"primary_key": "3270880", "vector": [], "sparse_vector": [], "title": "People Like Me: Designing for Reflection on Aggregate Cohort Data in Personal Informatics Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Increases in data complexity in personal informatics systems require new ways of contextualizing personal data to facilitate meaningful reflection. An emerging approach for providing such context includes augmenting one's personal data with the data of others \"like them\" to help individuals make sense of their data. However, we do not yet understand how an individual's self-reflection process is affected when the data of others is made available. In this paper, we investigate how people reflect on three types of personal data when presented alongside a large set of aggregated data of multiple cohorts. We conducted personal and cohort data reviews using a subset of participants from a mobile-sensing study that collected physical activity, digital social activity, and perceived stress, from 47 students over three weeks. Participants preferred to use characteristics of the data (e.g., maxima, minima) and graphical presentation (e.g., appearance of trends) along with demographic identities (e.g., age, gender) when relating to cohorts. We further characterize how participants incorporated cohort data into their self-reflection process, and conclude with discussion of the implications for personal informatics systems that leverage the data of \"people like me\" to enable meaningful reflection.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264917"}, {"primary_key": "3270882", "vector": [], "sparse_vector": [], "title": "Painting an Apple with an Apple: A Tangible Tabletop Interface for Painting with Physical Objects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce UnicrePaint, a digital painting system that allows the user to paint with physical objects by acquiring three parameters from the interacting object: the form, the color pattern and the contact pressure. The design of the system is motivated by a hypothesis that integrating direct input from physical objects with digital painting offers unique creative experiences to the user. A major technical challenge in implementing UnicrePaint is to resolve the conflict between input and output, i.e., to be able to capture the form and color pattern of contacting objects from a camera, while at the same time be able to present the captured data using a projector. We present a solution for this problem. We implemented a prototype and carried out a user study with fifteen novice users. Additionally, five professional users with art-related backgrounds participated in a user study to obtain insights into how professionals might view our system. The results show that UnicrePaint offers unique experiences with painting in a creative manner. Also, its potentials beyond mere artwork are suggested.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287040"}, {"primary_key": "3270883", "vector": [], "sparse_vector": [], "title": "Watching and Safeguarding Your 3D Printer: Online Process Monitoring Against Cyber-Physical Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The increasing adoption of 3D printing in many safety and mission critical applications exposes 3D printers to a variety of cyber attacks that may result in catastrophic consequences if the printing process is compromised. For example, the mechanical properties (e.g., physical strength, thermal resistance, dimensional stability) of 3D printed objects could be significantly affected and degraded if a simple printing setting is maliciously changed. To address this challenge, this study proposes a model-free real-time online process monitoring approach that is capable of detecting and defending against the cyber-physical attacks on the firmwares of 3D printers. Specifically, we explore the potential attacks and consequences of four key printing attributes (including infill path, printing speed, layer thickness, and fan speed) and then formulate the attack models. Based on the intrinsic relation between the printing attributes and the physical observations, our defense model is established by systematically analyzing the multi-faceted, real-time measurement collected from the accelerometer, magnetometer and camera. The Kalman filter and Canny filter are used to map and estimate three aforementioned critical toolpath information that might affect the printing quality. Mel-frequency Cepstrum Coefficients are used to extract features for fan speed estimation. Experimental results show that, for a complex 3D printed design, our method can achieve 4% Hausdorff distance compared with the model dimension for infill path estimate, 6.07% Mean Absolute Percentage Error (MAPE) for speed estimate, 9.57% MAPE for layer thickness estimate, and 96.8% accuracy for fan speed identification. Our study demonstrates that, this new approach can effectively defend against the cyber-physical attacks on 3D printers and 3D printing process.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264918"}, {"primary_key": "3270885", "vector": [], "sparse_vector": [], "title": "Fin<PERSON> and Ang<PERSON>: Exploring the Comfort of Touch Input on Smartwatches.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Smartwatches present a unique touch input context: small, fixed to one wrist and approachable from a limited range of angles by the touching hand. Techniques to expand their input expressivity often involve variations in how a watch must be touched, such as with different fingers, poses or from specific angles. While objective performance with such systems is commonly reported, subjective qualities such as comfort remain overlooked. We argue that techniques that involve uncomfortable input will be of limited value and contribute the first data on the comfort of input on smartwatches via two studies that combine subjective ratings of comfort with objective performance data. We examine both static and dynamic touches and three finger poses. Based on the study results, we contribute a set of design recommendations for comfortable, effective smartwatch input. We close by instantiating the recommendations in interface prototypes that we evaluate in a final qualitative study.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287042"}, {"primary_key": "3270886", "vector": [], "sparse_vector": [], "title": "Crowdsourcing the Installation and Maintenance of Indoor Localization Infrastructure to Support Blind Navigation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Indoor navigation systems can make unfamiliar buildings more accessible for people with vision impairments, but their adoption is hampered by the effort of installing infrastructure and maintaining it over time. Most solutions in this space require augmenting the environment with add-ons, such as Bluetooth beacons. Installing and calibrating such infrastructure requires time and expertise. Once installed, localization accuracy often degrades over time as batteries die, beacons go missing, or otherwise stop working. Even localization systems installed by experts can become unreliable weeks, months, or years after the installation. To address this problem, we created LuzDeploy: a physical crowdsourcing system that organizes non-experts for the installation and long-term maintenance of a Bluetooth-based navigation system. LuzDeploy simplifies the tasks required to install and maintain the localization infrastructure, thus making a crowdsourcing approach feasible for non-experts. We report on a field deployment where 127 participants installed and maintained a blind navigation system over several months in a 7-story building, completing 455 tasks in total. We compare the accuracy of the system installed by participants to an installation completed by experts with specialized equipment. LuzDeploy aims to improve the sustainability of indoor navigation systems to encourage widespread adoption outside of research settings.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191741"}, {"primary_key": "3270887", "vector": [], "sparse_vector": [], "title": "FootNotes: Geo-referenced Audio Annotations for Nonvisual Exploration.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The majority of information in the physical environment is conveyed visually, meaning that people with vision impairments often lack access to the shared cultural, historical, and practical features that define a city. How can someone who is blind find out about the sleek skyscrapers that dot a modern city's skyline, historic cannons that have been remade into traffic pillars, or ancient trees that uproot a neighborhood's sidewalks? We present FootNotes, a system that embeds rich textual descriptions of objects and locations in OpenStreetMap, a popular geowiki. Both sighted and blind users can annotate the physical environment with functional, visual, historical, and social descriptions. We report on the experience of ten participants with vision impairments who used a spatialized audio application to interact with these annotations while exploring a city. By sharing rich annotations of physical objects and areas, FootNotes helps people thoroughly explore a new location or serendipitously discover previously unknown features of familiar environments.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264919"}, {"primary_key": "3270888", "vector": [], "sparse_vector": [], "title": "SiFi: Pushing the Limit of Time-Based WiFi Localization Using a Single Commodity Access Point.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There has been a booming interest in developing WiFi localization using multi-antenna (MIMO) access points (APs). Recent advances have demonstrated promising results that break the meter-accuracy barrier using commodity APs. Yet these state-of-the-art solutions require either multiple APs that are not necessarily available in practice, or multiple-channel measurements that disrupt normal data communication. In this paper, we present SiFi, a single AP-based indoor localization system that for the first time achieves sub-meter accuracy with a single channel only. The SiFi design is based on a key observation: with MIMO, the multiple (typically three) antennas of an AP are frequency-locked; although the accurate Time-of-Arrival (ToA) estimation on commodity APs is fundamentally limited by the imperfect time and frequency synchronization between the transmitter and receiver, there should be only one value for the ToA distortion that can cause three direct-path ToAs of the antennas to intersect at a single point, i.e., the position of the target. We develop the theoretical foundations of SiFi and demonstrate its realworld implementation with off-the-shelf WiFi cards. Our implementation introduces no hardware modification and is fully compatible with concurrent data transmission. It achieves a median accuracy of 0.93 m, which significantly outperforms the best known single AP single channel solution.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191742"}, {"primary_key": "3270890", "vector": [], "sparse_vector": [], "title": "CapHarvester: A Stick-on Capacitive Energy Harvester Using Stray Electric Field from AC Power Lines.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shwetak N. Patel"], "summary": "Internet of Things (IoT) applications and platforms are becoming increasingly prevalent. Alongside this growth of smart devices comes added costs for deployment, maintenance, and the need to manage power consumption so as to reduce recurrent costs of replacing batteries. To alleviate recurrent battery replacement and maintenance, we propose a novel battery-free, stick-on capacitive energy harvester that harvests the stray electric field generated around AC power lines (110 V/230 V) without an ohmic connection to earth ground reference, thereby obviating the need for cumbersome scraping of paint on concrete walls or digging a earth ground plate. Furthermore, our harvester does not require any appliance or load to be operating on the power line and can continuously harvest power after deployment. In effect, end-users are expected to simply stick the proposed harvester onto any existing power-line cord in order to power a sensing platform. Our controlled lab measurements and real-world deployments demonstrate that our device can harvest 270.6 μJ of energy from a 14 cm long interface in 12 min. We also demonstrate several applications, such as distributed temperature monitoring, appliance state monitoring, and environmental parameter logging for indoor farming.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264920"}, {"primary_key": "3270891", "vector": [], "sparse_vector": [], "title": "A Simple but Quantifiable Approach to Dynamic Price Prediction in Ride-on-demand Services Leveraging Multi-source Urban Data.", "authors": ["Su<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>"], "summary": "Ride-on-demand (RoD) services such as Uber and Didi are becoming increasingly popular, and in these services dynamic prices play an important role in balancing the supply and demand to benefit both drivers and passengers. However, dynamic prices also create concerns. For passengers, the \"unpredictable\" prices sometimes prevent them from making quick decisions: one may wonder if it is possible to get a lower price if s/he chooses to wait a while. It is necessary to provide more information to them, and predicting the dynamic prices is a possible solution. For the transportation industry and policy makers, there are also concerns about the relationship between RoD services and their more traditional counterparts such as metro, bus, and taxi: whether they affect each other and how. In this paper we tackle these two concerns by predicting the dynamic prices using multi-source urban data. Price prediction could help passengers understand whether they could get a lower price in neighboring locations or within a short time, thus alleviating their concerns. The prediction is based on urban data from multiple sources, including the RoD service itself, taxi service, public transportation, weather, the map of a city, etc. We train a simple linear regression model with high-dimensional composite features to perform the prediction. By combining simple basic features into composite features, we compensate for the loss of expressiveness in a linear model due to the lack of non-linearity. Additionally, the use of multi-source data and a linear model enables us to quantify and explain the relationship between multiple means of transportation by examining the weights of different features in the model. Our hope is that the study not only serves as an accurate prediction to make passengers more satisfied, but also sheds light on the concern about the relationship between different means of transportation for either the industry or policy makers.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264922"}, {"primary_key": "3270892", "vector": [], "sparse_vector": [], "title": "Crowd-AI Camera Sensing in the Real World.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shomiron Ghose", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smart appliances with built-in cameras, such as the Nest Cam and Amazon Echo Look, are becoming pervasive. They hold the promise of bringing high fidelity, contextually rich sensing into our homes, workplaces and other environments. Despite recent and impressive advances, computer vision systems are still limited in the types of sensing questions they can answer, and more importantly, do not easily generalize across diverse human environments. In response, researchers have investigated hybrid crowd- and AI-powered methods that collect human labels to bootstrap automatic processes. However, deployments have been small and mostly confined to institutional settings, leaving open questions about the scalability and generality of the approach. In this work, we describe our iterative development of Zensors++, a full-stack crowd-AI camera-based sensing system that moves significantly beyond prior work in terms of scale, question diversity, accuracy, latency, and economic feasibility. We deployed Zensors++ in the wild, with real users, over many months and environments, generating 1.6 million answers for nearly 200 questions created by our participants, costing roughly 6/10ths of a cent per answer delivered. We share lessons learned, insights gleaned, and implications for future crowd-AI vision systems.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264921"}, {"primary_key": "3270893", "vector": [], "sparse_vector": [], "title": "Device-free Personalized Fitness Assistant Using WiFi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hongbo Liu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There is a growing trend for people to perform regular workouts in home/office environments because work-at-home people or office workers can barely squeeze in time to go to dedicated exercise places (e.g., gym). To provide personalized fitness assistance in home/office environments, traditional solutions, e.g., hiring personal coaches incur extra cost and are not always available, while new trends requiring wearing smart devices around the clock are cumbersome. In order to overcome these limitations, we develop a device-free fitness assistant system in home/office environments using existing WiFi infrastructure. Our system aims to provide personalized fitness assistance by differentiating individuals, automatically recording fine-grained workout statistics, and assessing workout dynamics. In particular, our system performs individual identification via deep learning techniques on top of workout interpretation. It further assesses the workout by analyzing both short and long-term workout quality, and provides workout reviews for users to improve their daily exercises. Additionally, our system adopts a spectrogram-based workout detection algorithm along with a Cumulative Short Time Energy (CSTE)-based workout segmentation method to ensure its robustness. Extensive experiments involving 20 participants demonstrate that our system can achieve a 93% accuracy on workout recognition and a 97% accuracy for individual identification.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287043"}, {"primary_key": "3270894", "vector": [], "sparse_vector": [], "title": "FluidMeter: Gauging the Human Daily Fluid Intake Using Smartwatches.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Water is the most vital nutrient in the human body accounting for about 60% of the body weight. To maintain optimal health, it is important for humans to consume a sufficient amount of fluids daily. Therefore, tracking the amount of human daily fluid intake has a myriad of health applications like dehydration prevention. In this paper, we present FluidMeter: a ubiquitous and unobtrusive system to track the amount of fluid intake leveraging the inertial sensors embedded in smartwatches. To achieve this, FluidMeter first separates the drinking activities from other human activities (playing, running, eating, etc.). Thereafter, it analyzes the sampled sensors data during the extracted drinking episodes to recognize the sequence of micro-activities (lift the bottle, sip, release the bottle) that constitute the drinking activity. Finally, it applies some machine learning algorithms on some features extracted from sampled sensor data during the sipping period to gauge the amount of fluid intake in the designated drinking episode. FluidMeter is evaluated by collecting more than 260 hours of different human activities by 70 different participants using different smartwatch models. The results show that FluidMeter can recognize the drinking activity and its micro-activities accurately which is comparable to that achieved by the state-of-the-art techniques. Finally, FluidMeter can estimate the overall amount of fluid intake in grams accurately with a estimation error limited to 15%, highlighting its promise as a ubiquitous health service.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264923"}, {"primary_key": "3270895", "vector": [], "sparse_vector": [], "title": "Butterfly: Environment-Independent Physical-Layer Authentication for Passive RFID.", "authors": ["Jinsong Han", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "RFID tag authentication is challenging because most commodity tags cannot run cryptographic algorithms. Prior research demonstrates that physical layer information based authentication is a promising solution, which uses special features from the physical backscatter signals from tags as their fingerprints. However, our recent studies show that existing physical-layer authentication may fail if feature collection and authentication are conducted in different locations, due to location-dependent noises, environmental factors, or reader hardware differences. This paper presents a new physical layer authentication scheme, called Butterfly, which is resilient to environment and location changes. <PERSON> utilizes a pair of adjacent tags as an identifier of each object. By using the difference between the RF signals of the two tags as their fingerprint, the environmental factors can be effectively canceled. <PERSON> is fully compatible with commodity RFID systems and standards. We set up a prototype Butterfly using commodity readers, tags, and RF devices. Extensive experiments show that <PERSON> achieves high authentication accuracy for substantially different environments and device changes.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287044"}, {"primary_key": "3270896", "vector": [], "sparse_vector": [], "title": "Crowd-enabled Processing of Trustworthy, Privacy-Enhanced and Personalised Location Based Services with Quality Guarantee.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a novel approach for enabling trustworthy, privacy-enhanced and personalised location based services (LBSs) that find nearby points of interests (POIs) such as restaurants, ATM booths, and hospitals in a crowdsourced manner. In our crowdsourced approach, a user forms a group from the crowd and processes the LBS using the POI knowledge of the group members without involving an external service provider. We use personalised rating in addition to the distance of a POI for finding the answers of the location based queries. The personalised rating of a POI is computed using individual POI ratings given by the group members and the query requestor's trust and similarity scores for the group members. The major challenges for the crowdsourced data are incompleteness and inaccuracy, which may result in lower quality answer for the LBS. In this paper, we first present techniques to select knowledgeable group members for processing LBSs and thereby increase the accuracy and the confidence level of the query answers. We then develop efficient algorithms to process LBSs in real time and enhance privacy by reducing the number of the group members' POIs shared with the query requestor. Finally, we run extensive experiments using real datasets to show the efficiency and effectiveness of our approach.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287045"}, {"primary_key": "3270897", "vector": [], "sparse_vector": [], "title": "Multi-view Commercial Hotness Prediction Using Context-aware Neural Network Ensemble.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Prediction over heterogeneous data attracts much attention in urban computing. Recently, satellite imagery provides a new chance for urban perception but raises the problem of how to fuse visual and non-visual features. So far, the practice is to concatenate the multimodal features into a vector, which may suppress important features. Therefore, we propose a new ensemble learning framework: (1) An estimator is developed for each predictor to score its confidence, which is input adaptive. (2) By applying the output of each predictor to the input of the corresponding estimator as feedback, the estimator learns the performance of the predictor in the input-output space. When a new input is applied to produce a prediction, the similar situations will be recalled by the estimator to score the confidence of the prediction. (3) Using end-to-end training, the estimator learns the weights automatically to minimize the total loss of the neural networks. With the proposed method, data mining based urban computing and computer vision rendered urban perception can be bridged at the task of commercial activeness prediction, where the prediction based on satellite images and social context data are fused to yield better prediction than those based on single view data in the experiments.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287046"}, {"primary_key": "3270898", "vector": [], "sparse_vector": [], "title": "On Being Told How We Feel: How Algorithmic Sensor Feedback Influences Emotion Perception.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Algorithms and sensors are increasingly deployed for highly personal aspects of our everyday lives. Recent work suggests people have imperfect understanding of system outputs, often assuming sophisticated capabilities and deferring to feedback. We explore how people construe algorithmic interpretations of emotional data in personal informatics systems. A survey (n=188) showed strong interest in automatic stress and emotion tracking, but many respondents expected these systems to provide objective measurements for their emotional experiences. A second study examined how algorithmic sensor feedback influences emotional self-judgments, by comparing three system framings of physiological ElectroDermal Activity data (EDA): Positive (\"alert and engaged\"), Negative (\"stressed\"), and Control (no frame) in a mixed-methods study with 64 participants. Despite users reporting strategies to test system outputs, users still deferred to feedback and their perceived emotions were significantly influenced by feedback frames. Some users overrode personal judgments, believing the system had access to privileged information about their emotions. Based on these findings, we explore design implications for personal informatics including risks of users trusting systems that seemingly \"unlock\" hidden aspects of the self. We propose design approaches that provide opportunities for future emotion-monitoring systems to exploit these framing effects, and for users to more actively construe emotional states.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264924"}, {"primary_key": "3270899", "vector": [], "sparse_vector": [], "title": "CrowdProbe: Non-invasive Crowd Monitoring with Wi-Fi Probe.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Devices with integrated Wi-Fi chips broadcast beacons for network connection management purposes. Such information can be captured with inexpensive monitors and used to extract user behavior. To understand the behavior of visitors, we deployed our passive monitoring system---CrowdProbe, in a multi-floor museum for six months. We used a Hidden Markov Models (HMM) based trajectory inference algorithm to infer crowd movement using more than 1.7 million opportunistically obtained probe request frames. However, as more devices adopt schemes to randomize their MAC addresses in the passive probe session to protect user privacy, it becomes more difficult to track crowd and understand their behavior. In this paper, we try to make use of historical transition probability to reason about the movement of those randomized devices with spatial and temporal constraints. With CrowdProbe, we are able to achieve sufficient accuracy to understand the movement of visitors carrying devices with randomized MAC addresses.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264925"}, {"primary_key": "3270900", "vector": [], "sparse_vector": [], "title": "DeActive: Scaling Activity Recognition with Active Deep Learning.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning architectures have been applied increasingly in multi-modal problems which has empowered a large number of application domains needing much less human supervision in the process. As unlabeled data are abundant in most of the application domains, deep architectures are getting increasingly popular to extract meaningful information out of these large volume of data. One of the major caveat of these architectures is that the training phase demands both computational time and system resources much higher than shallow learning algorithms and it is posing a difficult challenge for the researchers to implement the architectures in low-power resource constrained devices. In this paper, we propose a deep and active learning enabled activity recognition model, DeActive, which is optimized according to our problem domain and reduce the resource requirements. We incorporate active learning in the process to minimize the human supervision along with the effort needed for compiling ground truth. The DeActive model has been validated using real data traces from a retirement community center (IRB #HP-00064387) and 4 public datasets. Our experimental results show that our model can contribute better accuracy while ensuring less amount of resource usages in reduced time compared to other traditional deep learning approaches in activity recognition.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214269"}, {"primary_key": "3270901", "vector": [], "sparse_vector": [], "title": "Lightitude: Indoor Positioning Using Uneven Light Intensity Distribution.", "authors": ["Yiqing Hu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose an indoor positioning system, Lightitude, which utilizes the already existed, uneven indoor light intensity distribution established by densely deployed indoor lights as the medium. As common indoor lights cannot act as landmarks due to lack of unique features (e.g., unique intensity or flicker frequency), systems that exploiting the received light intensity (RLI) usually impose strong constraints on user's motion and make ideal assumptions about the indoor environment. Different from these approaches, we first propose a realistic light intensity model to reconstruct the RLI distribution given any motion (position, orientation) of the receiver, thus RLI collected with every motion of the receiver could be used for positioning. Then we design a particle-filter-based positioning module, which harnesses user's natural mobility to eliminate the ambiguity of a single RLI. Experiment results show that Lightitude achieves mean accuracy 1.93m and 1.98m in an office (720m2) and a library (960m2) respectively. Lightitude is still robust against interferences like sunlight, shading of human-body and several user behaviors.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214270"}, {"primary_key": "3270902", "vector": [], "sparse_vector": [], "title": "BreathLive: Liveness Detection for Heart Sound Authentication with Deep Breathing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Nowadays, considerable number of devices have been proposed to monitor cardiovascular health. To protect medical data on these devices from unauthorized access, researchers have proposed ECG-based and heart sound-based authentication methods. However, their vulnerabilities to replay attacks have recently been revealed. In this paper, we leverage liveness detection to enhance heart sound-based authentication against replay attacks. We utilize the inherent correlation between sounds and chest motion caused by deep breathing to realize a reliable liveness detection system, BreathLive. To be specific, BreathLive captures breathing sounds and chest motion simultaneously, and then eliminates signal delay caused by any imperfections of device components. Next, it extracts a set of features to characterize the correlation between sounds and motion signals, and uses them to train the classifier. We implement and evaluated BreathLive under different attacking scenarios and contexts. The results show that BreathLive achieves an equal error rate of 4.0%, 6.4% and 8.3% for random impersonation attacks, advanced impersonation attacks and advanced replay attacks respectively, which indicates its effectiveness in defending against different attacks. Also the extensive experiments prove the system can be robust to different contexts with a small training set.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191744"}, {"primary_key": "3270904", "vector": [], "sparse_vector": [], "title": "Passerby Crowdsourcing: Workers&apos; Behavior and Data Quality Management.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Worker recruitment is one of the important problems in crowdsourcing, and many proposals have been presented for placing equipment in physical spaces for recruiting workers. One of the essential challenges of the approach is how to keep people attracted because those who perform tasks at first gradually lose interest and do not access the equipment. This study uses a different approach to the worker recruitment problem. In our approach, we dive into people's personal spaces by projecting task images on the floor, thereby allowing the passersby to effortlessly access tasks while walking. The problem then changes from how to keep people engaged to how to manage data quality because many passersby unconsciously or intentionally walk through the task screen on the floor without doing the task, which produces unintended results. We explore a machine-learning approach to select only the intended results and manage the data quality. The system assesses the workers' intention from their behavior. We identify the features for classifiers based on our observations of the passersby. We then conduct extensive evaluations with real data. The results show that the features are effective in practice, and the classifiers improve the data quality.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287047"}, {"primary_key": "3270905", "vector": [], "sparse_vector": [], "title": "Addressing The Privacy Paradox through Personalized Privacy Notifications.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Privacy behaviors of individuals are often inconsistent with their stated attitudes, a phenomenon known as the \"privacy paradox.\" These inconsistencies may lead to troublesome or regrettable experiences. To help people address these privacy inconsistencies, we propose a personalized privacy notification approach that juxtaposes users' general privacy attitudes towards specific technologies and the potential privacy riskiness of particular instances of such technology, right when users make decisions about whether and/or how to use the technology under consideration. Highlighting the privacy inconsistencies to users was designed to nudge them in making decisions in a way that aligns with their privacy attitudes. To illustrate this approach, we chose the domain of mobile apps and designed a privacy discrepancy interface that highlights this discrepancy between users' general privacy attitudes towards mobile apps and the potential privacy riskiness of a particular app, nudging them to make app installation and/or permission granting decisions reflecting their privacy attitudes. To evaluate this interface, we conducted an online experiment simulating the process of installing Android apps. We compared the privacy discrepancy approach with several existing privacy notification approaches. Our results suggest that the behaviors of participants who used the privacy discrepancy interface better reflected their privacy attitudes than the other approaches.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214271"}, {"primary_key": "3270906", "vector": [], "sparse_vector": [], "title": "FarmChat: A Conversational Agent to Answer Farmer Queries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Khai N. <PERSON>", "Shwetak N. Patel"], "summary": "Farmers constitute 54.6% of the Indian population, but earn only 13.9% of the national GDP. This gross mismatch can be alleviated by improving farmers' access to information and expert advice (e.g., knowing which seeds to sow and how to treat pests can significantly impact yield). In this paper, we report our experience of designing a conversational agent, called FarmChat, to meet the information needs of farmers in rural India. We conducted an evaluative study with 34 farmers near Ranchi in India, focusing on assessing the usability of the system, acceptability of the information provided, and understanding the user population's unique preferences, needs, and challenges in using the technology. We performed a comparative study with two different modalities: audio-only and audio+text. Our results provide a detailed understanding on how literacy level, digital literacy, and other factors impact users' preferences for the interaction modality. We found that a conversational agent has the potential to effectively meet the information needs of farmers at scale. More broadly, our results could inform future work on designing conversational agents for user populations with limited literacy and technology experience.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287048"}, {"primary_key": "3270907", "vector": [], "sparse_vector": [], "title": "Evolving Needs in IoT Control and Accountability: A Longitudinal Study on Smart Home Intelligibility.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A key issue for smart home systems is supporting non-expert users in their management. Whereas feedback design on use cases (such as energy feedback) have gained attention, current approaches to providing awareness on the system state typically provide a rather technical view. Long-term investigations of the practices and resources needed for maintaining Do-It-Yourself smart home systems, are particularly scarce. We report on a design case study in which we equipped 12 households with DIY smart home systems for two years and studied participants' strategies for maintaining system awareness, from learning about its workings to monitoring its behavior. We find that people's needs regarding system accountability changed over time. Their privacy needs were also affected over the same period. We found that participants initially looked for in-depth awareness information from the dedicated web-based dashboard. In the later phases of appropriation, however, their interaction and information needs shifted towards management by exception on mobile or ambient displays -- only focusing on the system when things were 'going wrong'. In terms of system accountability, we find that a system's self-declaration should focus on being socially meaningful rather than technically complete, for instance by relating itself to people's activities and the home routines.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287049"}, {"primary_key": "3270911", "vector": [], "sparse_vector": [], "title": "Robust Sensor-Orientation-Independent Feature Selection for Animal Activity Recognition on Collar Tags.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Nirvana Meratnia", "<PERSON>"], "summary": "Fundamental challenges faced by real-time animal activity recognition include variation in motion data due to changing sensor orientations, numerous features, and energy and processing constraints of animal tags. This paper aims at finding small optimal feature sets that are lightweight and robust to the sensor's orientation. Our approach comprises four main steps. First, 3D feature vectors are selected since they are theoretically independent of orientation. Second, the least interesting features are suppressed to speed up computation and increase robustness against overfitting. Third, the features are further selected through an embedded method, which selects features through simultaneous feature selection and classification. Finally, feature sets are optimized through 10-fold cross-validation. We collected real-world data through multiple sensors around the neck of five goats. The results show that activities can be accurately recognized using only accelerometer data and a few lightweight features. Additionally, we show that the performance is robust to sensor orientation and position. A simple Naive Bayes classifier using only a single feature achieved an accuracy of 94 % with our empirical dataset. Moreover, our optimal feature set yielded an average of 94 % accuracy when applied with six other classifiers. This work supports embedded, real-time, energy-efficient, and robust activity recognition for animals.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191747"}, {"primary_key": "3270913", "vector": [], "sparse_vector": [], "title": "SkinWire: Fabricating a Self-Contained On-Skin PCB for the Hand.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Kent Lyons"], "summary": "Current wearable form factors often house electronics using an enclosure that is attached to the body. This form factor, while wearable, tends to protrude from the body and therefore can limit wearability. While emerging research in on-skin interfaces from the HCI and wearable communities have generated form factors with lower profiles, they often still require support by conventional electronics and associated form factors for the microprocessor, wireless communication, and battery units. In this work, we introduce SkinWire, a fabrication approach that extends the early work in on-skin interfaces to shift wearable devices from their traditional box-like forms to a fully self-contained on-skin form factor. The Skin Wire approach starts with the placement of electronic components into individual PCB islands, which are then distributed over the body surface. The islands are connected through a novel skin-wiring approach that deposits conformal multi-stranded metallic wires on thin silicon substrates through a sewing-based technique. The process affords on-skin interfaces with the needed wiring in limited surface areas. We exemplify the capacity of this approach by shifting an IMU-based hand gesture system - which traditionally come in bulky glove-based form factors - directly onto the skin. Inspired by the emerging body art trend of body wiring, the Skin Wire approach uses readily accessible materials and affords aesthetic customization. We evaluate fabrication parameters, and conduct a user study to uncover wearability concerns.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264926"}, {"primary_key": "3270914", "vector": [], "sparse_vector": [], "title": "Beacon: Designing a Portable Device for Self-Administering a Measure of Critical Flicker Frequency.", "authors": ["<PERSON>", "Rafal Kocielnik", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Critical flicker frequency (CFF) is the minimum frequency at which a flickering light source appears fused to an observer. Measuring CFF can support early diagnosis of minimal hepatic encephalopathy (MHE), a condition affecting up to 80% of people with cirrhosis of the liver. However, adoption of CFF measurement in clinical practice has been hampered by the cost of a device for measuring CFF and the need for specialized training to administer the test. This paper presents Beacon, a portable, inexpensive device that enables people to measure their own critical flicker frequency. We adopt a mixed-methods approach to informing and evaluating the design of and potential opportunities for Beacon. We first report on a two-part formative study with 10 participants to evaluate the choice of certain parameters in the design of Beacon. We then report on a study of 41 healthy adults ranging from 18 to 99 years of age, finding that Beacon performs on par with Lafayette Flicker Fusion System, an established medical device, achieving a pearson correlation coefficient of 0.88. We finally report on a focus group with five hepatoligists who work with patients with cirrhosis of the liver, using our initial prototype development to examine their perspectives on potential opportunities and challenges in adoption of a device like Beacon. We discuss Beacon as an exploration of reframing critical flicker frequency measurement from a clinical screening tool into a self-administered self-tracking measure, thereby drawing upon and contributing to research in the health and personal informatics.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264927"}, {"primary_key": "3270915", "vector": [], "sparse_vector": [], "title": "CueAuth: Comparing Touch, Mid-Air Gestures, and Gaze for Cue-based Authentication on Situated Displays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Secure authentication on situated displays (e.g., to access sensitive information or to make purchases) is becoming increasingly important. A promising approach to resist shoulder surfing attacks is to employ cues that users respond to while authenticating; this overwhelms observers by requiring them to observe both the cue itself as well as users' response to the cue. Although previous work proposed a variety of modalities, such as gaze and mid-air gestures, to further improve security, an understanding of how they compare with regard to usability and security is still missing as of today. In this paper, we rigorously compare modalities for cue-based authentication on situated displays. In particular, we provide the first comparison between touch, mid-air gestures, and calibration-free gaze using a state-of-the-art authentication concept. In two in-depth user studies (N=20, N=17) we found that the choice of touch or gaze presents a clear tradeoff between usability and security. For example, while gaze input is more secure, it is also more demanding and requires longer authentication times. Mid-air gestures are slightly slower and more secure than touch but users hesitate to use them in public. We conclude with three significant design implications for authentication using touch, mid-air gestures, and gaze and discuss how the choice of modality creates opportunities and challenges for improved authentication in public.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287052"}, {"primary_key": "3270916", "vector": [], "sparse_vector": [], "title": "GymCam: Detecting, Recognizing and Tracking Simultaneous Exercises in Unconstrained Scenes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Worn sensors are popular for automatically tracking exercises. However, a wearable is usually attached to one part of the body, tracks only that location, and thus is inadequate for capturing a wide range of exercises, especially when other limbs are involved. Cameras, on the other hand, can fully track a user's body, but suffer from noise and occlusion. We present GymCam, a camera-based system for automatically detecting, recognizing and tracking multiple people and exercises simultaneously in unconstrained environments without any user intervention. We collected data in a varsity gym, correctly segmenting exercises from other activities with an accuracy of 84.6%, recognizing the type of exercise at 93.6% accuracy, and counting the number of repetitions to within ± 1.7 on average. GymCam advances the field of real-time exercise tracking by filling some crucial gaps, such as tracking whole body motion, handling occlusion, and enabling single-point sensing for a multitude of users.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287063"}, {"primary_key": "3270918", "vector": [], "sparse_vector": [], "title": "Assisted Medication Management in Elderly Care Using Miniaturised Near-Infrared Spectroscopy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Near-infrared spectroscopy (NIRS) measures the light reflected from objects to infer highly detailed information about their molecular composition. Traditionally, NIRS has been an instrument reserved for laboratory usage, but recently affordable and smaller devices for NIRS have proliferated. Pairing this technology with the ubiquitous smartphone opens up a plethora of new use cases. In this paper, we explore one such use case, namely medication management in a nursing home/elderly care centre. First, we conducted a qualitative user study with nurses working in an elderly care centre to examine the protocols and workflows involved in administering medication, and the nurses' perceptions on using this technology. Based on our findings, we identify the main impact areas that would benefit from introducing miniaturised NIRS. Finally, we demonstrate via a user study in a realistic scenario that miniaturised NIRS can be effectively used for medication management when leveraging appropriate machine learning techniques. Specifically, we assess the performance of multiple pre-processing and classification algorithms for a selected set of pharmaceuticals. In addition, we compare our solution with currently used methods for pharmaceutical identification in a local care centre. We hope that our reflection on the multiple aspects associated with the introduction of this device in an elderly care setting can help both academics and practitioners working on related problems.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214272"}, {"primary_key": "3270919", "vector": [], "sparse_vector": [], "title": "Experiencing Electrical Muscle Stimulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Electrical Muscle Stimulation (EMS) offers rich opportunities for interaction. By varying stimulation parameters (amplitudes, pulse widths and frequencies), EMS can be used to either trigger muscle contractions, and so convey object affordances or guide user movements, or provide rich haptic feedback. However, the way users' experience changes with these parameters, and EMS in general, is poorly understood. Using a phenomenologically inspired interview technique, the explicitation interview, we study fifteen users' experience of EMS across 48 combinations of stimulation parameters. We synthesize the descriptions of EMS and relate stimulation parameters to categories of experience, such as 'temperature', 'motion', and 'sensitivity'. From the interviews, we explore more general topics in body-based interfaces, including the experience of control, metaphors for having your body actuated, and the relation between EMS parameters and perceived depth and location of sensations. These findings provide a vocabulary of EMS experience, and an insight into the relationship between specific parameters and associated sensations. In turn, this can help designers consider the user experience of EMS when developing interfaces.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264928"}, {"primary_key": "3270920", "vector": [], "sparse_vector": [], "title": "Reflection Companion: A Conversational System for Engaging Users in Reflection on Physical Activity.", "authors": ["Rafal Kocielnik", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile, wearable and other connected devices allow people to collect and explore large amounts of data about their own activities, behavior, and well-being. Yet, learning from-, and acting upon such data remain a challenge. The process of reflection has been identified as a key component of such learning. However, most tools do not explicitly design for reflection, carrying an implicit assumption that providing access to self-tracking data is sufficient. In this paper, we present Reflection Companion, a mobile conversational system that supports engaging reflection on personal sensed data, specifically physical activity data collected with fitness trackers. Reflection Companion delivers daily adaptive mini-dialogues and graphs to users' mobile phones to promote reflection. To generate our system's mini dialogues, we conducted a set of workshops with fitness trackers users, producing a diverse corpus of 275 reflection questions synthesized into a set of 25 reflection mini dialogues. In a 2-week field deployment with 33 active Fitbit users, we examined our system's ability to engage users in reflection through dialog. Results suggest that the mini-dialogues were successful in triggering reflection and that this reflection led to increased motivation, empowerment, and adoption of new behaviors. As a strong indicator of our system's value, 16 of the 33 participants elected to continue using the system for two additional weeks without compensation. We present our findings and describe implications for the design of technology-supported dialog systems for reflection on data.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214273"}, {"primary_key": "3270921", "vector": [], "sparse_vector": [], "title": "Rethinking the Future of Wireless Emergency Alerts: A Comprehensive Study of Technical and Conceptual Improvements.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Wireless Emergency Alerting (WEA) service is a standards-based transport and presentation channel used nationwide in the United States. The service can deliver short text warnings to wireless subscribers through a cell broadcast mechanism. For emergency situations in which a broadcast modality and a single, short text message are sufficient to convey information, the WEA service can be efficient and effective. However, the content to be delivered may necessitate more than a single, unchanging short message. In this research, we first examine the WEA service from the perspective of alert originators. We then use the insights gained to explore the efficacy of a range of potential extensions to the service. The extensions mainly address the importance of user context and the ability to create awareness through careful attention to the integrity of the vital information. We evaluated these extensions using a WEA emulation testbed in two public usability trials. We present an analysis of the broad range of improvements as a basis for further research into improving the service. We conclude that (1) precise geo-targeting augmented with location information and maps is an important aspect of capturing users' context, and (2) presenting information in a digested form can markedly improve the actionability and the accuracy of interpretation.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214274"}, {"primary_key": "3270922", "vector": [], "sparse_vector": [], "title": "An Energy-efficient and Lightweight Indoor Localization System for Internet-of-Things (IoT) Environments.", "authors": ["Myeongcheol Kwak", "Youngmong Park", "<PERSON><PERSON><PERSON>", "Jinyoung Han", "Taekyoung Kwon"], "summary": "Each and every spatial point in an indoor space has its own distinct and stable fingerprint, which arises owing to the distortion of the magnetic field induced by the surrounding steel and iron structures. This phenomenon makes many indoor positioning techniques rely on the magnetic field as an important source of localization. Most of the existing studies, however, have leveraged smartphones that have a relatively high computational power and many sensors. Thus, their algorithmic complexity is usually high, especially for commercial location-based services. In this paper, we present an energy-efficient and lightweight system that utilizes the magnetic field for indoor positioning in Internet of Things (IoT) environments. We propose a new hardware design of an IoT device that has a BLE interface and two sensors (magnetometer and accelerometer), with the lifetime of one year when using a coin-size battery. We further propose an augmented particle filter framework that features a robust motion model and a localization heuristic with small sensory data. The prototype-based evaluation shows that the proposed system achieves a median accuracy of 1.62 m for an office building, while exhibiting low computational complexity and high energy efficiency.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191749"}, {"primary_key": "3270923", "vector": [], "sparse_vector": [], "title": "The Connected Shower: Studying Intimate Data in Everyday Life.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents the design and field study of the Connected Shower, a bespoke IoT device that captures water flow, temperature, shower-head movement, and shower product weight. We deployed the device in six UK homes for a week to understand the use of 'intimate data' as captured by IoT systems. Findings from our contextual interviews unpack a) how such intimate data is collaboratively made sense of by accounting for the social order of showering practices as part and parcel of everyday routines; b) how the data makes details of showering accountable to their partners; c) how people reason about sharing intimate data both with third parties and their partners. Our study shows that intimate data is not intimate per se, nor is intimacy a property of the data, but is an interactional outcome arising from the articulation of shower practices to their co-present partners. Thus, judgments as to whether the data is too sensitive, private, or intimate to share are contingent on situated sense-making and therefore subject to change; however, there was a general consensus that sharing intimate data with service providers was acceptable if the data was sufficiently abstract and anonymised. We discuss challenges in the design of trustworthy data-driven IoT systems, and how they need to be warranted to be both acceptable and adopted into our intimate practices.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287054"}, {"primary_key": "3270924", "vector": [], "sparse_vector": [], "title": "Unobtrusive Assessment of Students&apos; Emotional Engagement during Lectures Using Electrodermal Activity Sensors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern wearable devices enable the continuous and unobtrusive monitoring of human physiological parameters, including heart rate and electrodermal activity. Through the definition of adequate models these parameters allow to infer the wellbeing, empathy, or engagement of humans in different contexts. In this paper, we show that off-the-shelf wearable devices can be used to unobtrusively monitor the emotional engagement of students during lectures. We propose the use of several novel features to capture students' momentary engagement and use existing methods to characterize the general arousal of students and their physiological synchrony with the teacher. To evaluate our method we collect a data set that -- after data cleaning -- contains data from 24 students, 9 teachers, and 41 lectures. Our results show that non-engaged students can be identified with high reliability. Using a Support Vector Machine, for instance, we achieve a recall of 81% -- which is a 25 percentage points improvement with respect to a Biased Random classifier. Overall, our findings may inform the design of systems that allow students to self-monitor their engagement and act upon the obtained feedback. Teachers could profit of information about non-engaged students too to perform self-reflection and to devise and evaluate methods to (re-)engage students.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264913"}, {"primary_key": "3270925", "vector": [], "sparse_vector": [], "title": "TwistIn: Tangible Authentication of Smart Devices via Motion Co-analysis with a Smartwatch.", "authors": ["Ho-Man <PERSON><PERSON>", "Chi-Wing Fu", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Smart devices contain sensitive information that has to be guarded against unauthorized access through authentication. Existing authentication methods become obsolete as they are designed either for logging-in one device at a time or are ineffective in a multi-user multi-device environment. This paper presents TwistIn, a simple gesture that takes a smartwatch as an authentication token for fast access and control of other smart devices. Our mechanism is particularly useful for devices such as smartphones, smart glasses, and small IoT objects. To log in a device, one simply need to pick it up and twist it a few times. Then, by co-analyzing the motion data from the device and the watch, our method can extend the user authentication on the watch to the device. This is a simple and tangible interaction that takes only one to two seconds to perform. Furthermore, to account for user variation in wrist bending, we decompose wrist and forearm rotations via an optimization to improve the method accuracy. We implemented TwistIn, collected thousands of gesture samples, and conducted various experiments to evaluate our prototype system and show that it achieved over 95% detection accuracy.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214275"}, {"primary_key": "3270926", "vector": [], "sparse_vector": [], "title": "Handwritten Signature Verification Using Wrist-Worn Devices.", "authors": ["Alona Levy", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper suggests a novel verification system for handwritten signatures. The proposed system is based on capturing motion signals from the sensors of wrist-worn devices, such as smartwatches and fitness trackers, during the signing process, to train a machine learning classifier to determine whether a given signature is genuine or forged. Our system can be used to: (1) Verify signatures written on paper documents, such as checks, credit card receipts and vote by mail ballots. Unlike existing systems for signature verification, our system obtains a high degree of accuracy, without requiring an ad hoc digital signing device. (2) Authenticate a user of a secure system based on \"who you are\" traits. Unlike existing \"motion-based\" authentication methods that commonly rely on long-term user behavior, writing a signature is a relatively short-term process. In order to evaluate our system, we collected 1,980 genuine and forged signature recordings from 66 different subjects, captured using a smartwatch device. Applying our signature verification system on the collected dataset, we show that it significantly outperforms two other state-of-the-art systems, obtaining an EER of 2.36% and an AUC of 98.52%.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264929"}, {"primary_key": "3270927", "vector": [], "sparse_vector": [], "title": "Training-Free Human Vitality Monitoring Using Commodity Wi-Fi Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hong Mei"], "summary": "Device-free sensing using ubiquitous Wi-Fi signals has recently attracted lots of attention. Among the sensed information, two important basic contexts are (i) whether a target is still or not and (ii) where the target is located. Continuous monitoring of these contexts provides us with rich datasets to obtain important high-level semantics of the target such as living habits, physical conditions and emotions. However, even to obtain these two basic contexts, offline training and calibration are needed in traditional methods, limiting the real-life adoption of the proposed sensing systems. In this paper, using the commodity Wi-Fi infrastructure, we propose a training-free human vitality sensing platform, WiVit. It could capture these two contexts together with the target's movements speed information in real-time without any human effort in offline training or calibration. Based on our extensive experiments in three typical indoor environments, the precision of activity detection is higher than 98% and the area detection accuracy is close to 100%. Moreover, we implement a short-term activity recognition system on our platform to recognize 4 types of actions, and we can reach an average accuracy of 94.2%. We also take a feasibility study of monitoring long-term activities of daily living to show our platform's potential applications in practice.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264931"}, {"primary_key": "3270928", "vector": [], "sparse_vector": [], "title": "Coconut: An IDE Plugin for Developing Privacy-Friendly Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Although app developers are responsible for protecting users' privacy, this task can be very challenging. In this paper, we present Coconut, an Android Studio plugin that helps developers handle privacy requirements by engaging developers to think about privacy during the development process and providing real-time feedback on potential privacy issues. We start by presenting new findings based on a series of semi-structured interviews with Android developers, probing into the difficulties with privacy that developers face when building apps. Based on these findings, we implemented a proof-of-concept prototype of Coconut and evaluated it in a controlled lab study with 18 Android developers (including eight professional developers). Our study results suggest that apps developed with Coconut handled privacy concerns better, and the developers that used Coconut had a better understanding of their code's behavior and wrote a better privacy policy for their app. We also found that requiring developers to do a small amount of annotating work regarding their apps' personal data practices during the development process may result in a significant improvement in app privacy.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287056"}, {"primary_key": "3270929", "vector": [], "sparse_vector": [], "title": "PCIAS: Precise and Contactless Measurement of Instantaneous Angular Speed Using a Smartphone.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>peng Dai", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Measuring Instantaneous Angular Speed (IAS) of rotating objects is ubiquitous in industry and our daily life. Engineers diagnose the operation condition of engines with IAS. Anemometers obtain instantaneous wind speed with the IAS of rotating cups. Traditional IAS measurement systems have their limitations in the aspects of installation, accuracy, and cost. In this paper, we propose PCIAS, a system that uses acoustic signals of a smartphone to measure IAS of rotating objects in a contactless manner. PCIAS covers a pretty large IAS measurement range (the numerical interval of IAS) from 10 Revolutions Per Minute (RPM) to 10000 RPM, which outperforms almost all existing Commercial-Off-The-Shelf (COTS) IAS meters. In PCIAS, we first choose an appropriate measurement range according to applications. We then use the smartphone to collect acoustic signals backscattered or generated by the object. Next, we extract acoustic features of the object to eliminate interferences from the environment. After that, we propose a robust tracking algorithm to estimate IAS by matching cycle time length of acoustic features adaptively. We build two testbeds to evaluate the accuracy and the robustness of our system in different IAS ranges. Our experiments show that PCIAS achieves a relative accuracy of more than 92% in the low IAS range, more than 94% in the middle IAS range, and more than 96% in the high IAS range. Finally, We exhibit two typical cases to demonstrate the practical use of our system.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287055"}, {"primary_key": "3270930", "vector": [], "sparse_vector": [], "title": "SweepLoc: Automatic Video-based Indoor Localization by Camera Sweeping.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "S.<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Indoor localization based on visual landmarks has received much attention in commercial sites with rich features (e.g., shopping malls, museums) recently because landmarks are relatively stable over a long time. Prior arts often require a user to take multiple independent images around his/her location, and manually confirm shortlisted landmarks. The process is sophisticated, inconvenient, slow, unnatural and error-prone. To overcome these limitations, we propose SweepLoc, a novel, efficient and automatic video-based indoor localization system. SweepLoc mimics our natural scanning around to identify nearby landmarks in an unfamiliar site to localize. In SweepLoc, a user simply takes a short video clip (about 6 to 8 seconds) of his/her surroundings by sweeping the camera. Using correlation and scene continuity between successive video frames, it automatically and efficiently selects key frames (where potential landmarks are centered) and subsequently reduces the decision error on landmarks. With identified landmarks, SweepLoc formulates an optimization problem to locate the user, taking compass noise and floor map constraint into account. We have implemented SweepLoc in Android platform. Our extensive experimental results in a food plaza and a premium mall demonstrate that SweepLoc is fast (less than 1 second to localize), and achieves substantially better accuracy as compared with the state-of-the-art approaches (reducing the localization error by 30%).", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264930"}, {"primary_key": "3270931", "vector": [], "sparse_vector": [], "title": "PAvessel: Practical 3D Vessel Structure Sensing through Photoacoustic Effects with Its Applications in Palm Biometrics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Tri Vu", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The blood vessels are the most critical part of the human circulatory system. Information acquired on the structure and status of blood vessels drives the development of numerous medical and biometric applications. Therefore, it is of paramount importance to find an effective way to sense vessel structures. Traditional methods, including infrared and Doppler sensing modalities, are limited by optical diffusion and ultrasonic scattering that are not good at vessel sensing with high performance. In comparison, we argue photoacoustic (PA) sensing is an emerging technique that can image 3D vessel structure deep in tissue with high-resolution visualization, maintaining the advantages of both optical and ultrasound methods. In this work, we propose and develop PAvessel, a practical 3D vessel structure sensing system based on PA effects. The entire sensing system comprises two key components, PA sensing hardware and PA sensing software. Specifically, the hardware mainly consists of a linear ultrasound transducer array, an ultrasound data acquisition system, and a neodymium-doped yttrium aluminum garnet (Nd:YAG) laser. After receiving the PA raw data, we use the advanced image reconstruction and 3D photoacoustic vein model to establish the 3D vessel structure model. We validated its effectiveness, cost-effectiveness and high resolution of PAvessel in the evaluation. The system achieves 52% higher signal-to-noise ratio (SNR) compared to the other methods. Furthermore, considering the 3D palm vein contains high dimensional human features and is almost impossible to forge, we also explored its applications in palm biometrics. In a pilot study with 10 participants, <PERSON><PERSON><PERSON>, combined with a 3D vessel structure matching algorithm (EMD-VT), has proven to possess high accuracy and robustness as a biometric. PAvessel achieves the precision and recall of 98.33% and 97.37%, respectively.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264932"}, {"primary_key": "3270933", "vector": [], "sparse_vector": [], "title": "Calibrating Low-Cost Sensors by a Two-Phase Learning Approach for Urban Air Quality Measurement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Urban air quality information, e.g., PM2.5 concentration, is of great importance to both the government and society. Recently, there is a growing interest in developing low-cost sensors, installed on moving vehicles, for fine-grained air quality measurement. However, low-cost mobile sensors typically suffer from low accuracy and thus need careful calibration to preserve a high measurement quality. In this paper, we propose a two-phase data calibration method consisting of a linear part and a nonlinear part. We use MLS (multiple least square) to train the linear part, and use RF (random forest) to train the nonlinear part. We propose an automatic feature selection algorithm based on AIC (Akaike information criterion) for the linear model, which helps avoid overfitting due to the inclusion of inappropriate features. We evaluate our method extensively. Results show that our method outperforms existing approaches, achieving an overall accuracy improvement of 16.4% in terms of PM2.5 levels compared with state-of-the-art approach.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191750"}, {"primary_key": "3270936", "vector": [], "sparse_vector": [], "title": "Joint Modeling of Heterogeneous Sensing Data for Depression Assessment via Multi-task Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shweta Ware", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Jin<PERSON> B<PERSON>"], "summary": "Depression is a common mood disorder that causes severe medical problems and interferes negatively with daily life. Identifying human behavior patterns that are predictive or indicative of depressive disorder is important. Clinical diagnosis of depression relies on costly clinician assessment using survey instruments which may not objectively reflect the fluctuation of daily behavior. Self-administered surveys, such as the Quick Inventory of Depressive Symptomatology (QIDS) commonly used to monitor depression, may show disparities from clinical decision. Smartphones provide easy access to many behavioral parameters, and Fitbit wrist bands are becoming another important tool to assess variables such as heart rates and sleep efficiency that are complementary to smartphone sensors. However, data used to identify depression indicators have been limited to a single platform either iPhone, or Android, or Fitbit alone due to the variation in their methods of data collection. The present work represents a large-scale effort to collect and integrate data from mobile phones, wearable devices, and self reports in depression analysis by designing a new machine learning approach. This approach constructs sparse mappings from sensing variables collected by various tools to two separate targets: self-reported QIDS scores and clinical assessment of depression severity. We propose a so-called heterogeneous multi-task feature learning method that jointly builds inference models for related tasks but of different types including classification and regression tasks. The proposed method was evaluated using data collected from 103 college students and could predict the QIDS score with an R2 reaching 0.44 and depression severity with an F1-score as high as 0.77. By imposing appropriate regularizers, our approach identified strong depression indicators such as time staying at home and total time asleep.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191753"}, {"primary_key": "3270937", "vector": [], "sparse_vector": [], "title": "What Makes Smartphone Use Meaningful or Meaningless?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Prior research indicates that many people wish to limit aspects of their smartphone use. Why is it that certain smartphone use feels so meaningless? We examined this question by using interviews, the experience sampling method, and mobile logging of 86,402 sessions of app use. One motivation for use (habitual use to pass the time) and two types of use (entertainment and passive social media) were associated with a lower sense of meaningfulness. In interviews, participants reported feeling a loss of autonomy when using their phone in these ways. These reports were corroborated by experience sampling data showing that motivation to achieve a specific purpose declined over the course of app use, particularly for passive social media and entertainment usage. In interviews, participants pointed out that even when smartphone use itself was meaningless, it could sometimes still be meaningful in the context of broader life as a 'micro escape' from negative situations. We discuss implications for how mobile apps can be used and designed to reduce meaningless experiences.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191754"}, {"primary_key": "3270938", "vector": [], "sparse_vector": [], "title": "Authenticating On-Body Backscatter by Exploiting Propagation Signatures.", "authors": ["Zhiqing Luo", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The vision of battery-free communication has made backscatter a compelling technology for on-body wearable and implantable devices. Recent advances have facilitated the communication between backscatter tags and on-body smart devices. These studies have focused on the communication dimension, while the security dimension remains vulnerable. It has been demonstrated that wireless connectivity can be exploited to send unauthorized commands or fake messages that result in device malfunctioning. The key challenge in defending these attacks stems from the minimalist design in backscatter. Thus, in this paper, we explore the feasibility of authenticating an on-body backscatter tag without modifying its signal or protocol. We present SecureScatter, a physical-layer solution that delegates the security of backscatter to an on-body smart device. To this end, we profile the on-body propagation paths of backscatter links, and construct highly sensitive propagation signatures to identify on-body backscatter links. We implement our design in a software radio and evaluate it with different backscatter tags that work at 2.4 GHz and 900 MHz. Results show that our system can identify on-body devices at 93.23% average true positive rate and 3.18% average false positive rate.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3266002"}, {"primary_key": "3270939", "vector": [], "sparse_vector": [], "title": "SignFi: Sign Language Recognition Using WiFi.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hongyang Zhao", "<PERSON><PERSON><PERSON>"], "summary": "We propose SignFi to recognize sign language gestures using WiFi. SignFi uses Channel State Information (CSI) measured by WiFi packets as the input and a Convolutional Neural Network (CNN) as the classification algorithm. Existing WiFi-based sign gesture recognition technologies are tested on no more than 25 gestures that only involve hand and/or finger gestures. SignFi is able to recognize 276 sign gestures, which involve the head, arm, hand, and finger gestures, with high accuracy. SignFi collects CSI measurements to capture wireless signal characteristics of sign gestures. Raw CSI measurements are pre-processed to remove noises and recover CSI changes over sub-carriers and sampling time. Pre-processed CSI measurements are fed to a 9-layer CNN for sign gesture classification. We collect CSI traces and evaluate SignFi in the lab and home environments. There are 8,280 gesture instances, 5,520 from the lab and 2,760 from the home, for 276 sign gestures in total. For 5-fold cross validation using CSI traces of one user, the average recognition accuracy of SignFi is 98.01%, 98.91%, and 94.81% for the lab, home, and lab+home environment, respectively. We also run tests using CSI traces from 5 different users in the lab environment. The average recognition accuracy of SignFi is 86.66% for 7,500 instances of 150 sign gestures performed by 5 different users.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191755"}, {"primary_key": "3270942", "vector": [], "sparse_vector": [], "title": "SAW: Wristband-based Authentication for Desktop Computers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Token-based proximity authentication methods that authenticate users based on physical proximity are effortless, but lack explicit user intentionality, which may result in accidental logins. For example, a user may get logged in when she is near a computer or just passing by, even if she does not intend to use that computer. Lack of user intentionality in proximity-based methods makes them less suitable for multi-user shared computer environments, despite their desired usability benefits over passwords. We present an authentication method for desktops called Seamless Authentication using Wristbands (SAW), which addresses the lack of intentionality limitation of proximity-based methods. SAW uses a low-effort user input step for explicitly conveying user intentionality, while keeping the overall usability of the method better than password-based methods. In SAW, a user wears a wristband that acts as the user's identity token, and to authenticate to a desktop, the user provides a low-effort input by tapping a key on the keyboard multiple times or wiggling the mouse with the wristband hand. This input to the desktop conveys that someone wishes to log in to the desktop, and SAW verifies the user who wishes to log in by confirming the user's proximity and correlating the received keyboard or mouse inputs with the user's wrist movement, as measured by the wristband. In our feasibility user study (n=17), SAW proved quick to authenticate (within two seconds), with a low false-negative rate of 2.5% and worst-case false-positive rate of 1.8%. In our user perception study (n=16), a majority of the participants rated it as more usable than passwords.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264935"}, {"primary_key": "3270943", "vector": [], "sparse_vector": [], "title": "V-Speech: Noise-Robust Speech Capturing Glasses Using Vibration Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Hong Lu"], "summary": "Smart glasses are often used in public environments or industrial scenarios that are relatively noisy. Background noise and sound from competing speakers deteriorate voice communication or performance of automatic speech recognition (ASR). Typically, signal processing techniques are used to reduce noise and enhance voice quality, but they have limitations in performance, hardware and/or computing resources. Voice capturing techniques using bone conducting on the head have been proposed in some experimental and commercial devices, with good robustness against environmental noise, but limited by signal distortions inherent to the capturing method. We present V-Speech, a novel sensing and signal processing solution that enables speech recognition and human-to-human communication in very noisy environments. It captures the voice signal with a vibration sensor located in the nasal pads of smart glasses and performs a transformation to the sensor signal in order to mimic that of a regular microphone in low noise conditions. The signal transformation is key, as it eliminates the \"nasal distortion\" that is introduced for nasal phonemes in the speech induced vibrations of the nasal bone. The output of V-Speech has low noise, sounds natural, and can be used in voice communication or as input to an off-the-shelf ASR service. We evaluated V-Speech in noise-free and noisy conditions with 30 volunteer speakers uttering 145 phrases and validated its performance on ASR engines and with assessments of voice quality using the Perceptual Evaluation of Speech Quality (PESQ) metric. The results show in extreme noise conditions a mean improvement of 50% for Word Error Rate (WER), and 1.0 on a scale of 5.0 for PESQ. In addition, real life recordings were made under various representative noise conditions, some with sound pressure levels of 93 dBA, which require hearing protection. Subjective listening tests were conducted according to a modified ITU P.835 approach to determine intelligibility, naturalness and overall quality. Under these extreme conditions, where V-Speech achieved 30 dB SNR, subjective results show the speech is intelligible, and the naturalness of the speech is rated as fair to good. This enables clear voice communication in challenging work environments, for example in places with industrial, factory, mining and construction noise. With our proposed smart switching technique between a regular microphone signal and V-Speech, the optimal quality can be maintained from low to high noise conditions.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287058"}, {"primary_key": "3270944", "vector": [], "sparse_vector": [], "title": "Al-light: An Alcohol-Sensing Smart Ice Cube.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Inappropriate alcohol drinking may cause health and social problems. Although controlling the intake of alcohol is effective to solve the problem, it is laborious to track consumption manually. A system that automatically records the amount of alcohol consumption has a potential to improve behavior in drinking activities. Existing devices and systems support drinking activity detection and liquid intake estimation, but our target scenario requires the capability of determining the alcohol concentration of a beverage. We present Al-light, a smart ice cube to detect the alcohol concentration level of a beverage using an optical method. Al-light is the size of 31.9 x 38.6 x 52.6 mm and users can simply put it into a beverage for estimation. It embeds near infrared (1450 nm) and visible LEDs, and measures the magnitude of light absorption. Our device design integrates prior technology in a patent which exploits different light absorption properties between water and ethanol to determine alcohol concentration. Through our revisitation studies, we found that light at the wavelength of 1450 nm has strong distinguishability even with different types of commercially-available beverages. Our quantitative examinations on alcohol concentration estimation revealed that Al-light was able to achieve the estimation accuracy of approximately 2 % v/v with 13 commercially-available beverages. Although our current approach needs a regressor to be trained for a particular ambient light condition or the sensor to be calibrated using measurements with water, it does not require beverage-dependent models unlike prior work. We then discuss four applications our current prototype supports and future research directions.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264936"}, {"primary_key": "3270947", "vector": [], "sparse_vector": [], "title": "Managing In-home Environments through Sensing, Annotating, and Visualizing Air Quality Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Air quality is important, varies across time and space, and is largely invisible. Pioneering past work deploying air quality monitors in residential environments found that study participants improved their awareness of and engagement with air quality. However, these systems fielded a single monitor and did not support user-specified annotations, inhibiting their utility. We developed MAAV -- a system to Measure Air quality, Annotate data streams, and Visualize real-time PM2.5 levels -- to explore how participants engage with an air quality system addressing these challenges. MAAV supports collecting data from multiple air quality monitors, annotating that data through multiple modalities, and sending text message prompts when it detects a PM2.5 spike. MAAV also features an interactive tablet interface for displaying measurement data and annotations. Through six long-term field deployments (20-47 weeks, mean 37.7 weeks), participants found these system features important for understanding the air quality in and around their homes. Participants gained new insights from between-monitor comparisons, reflected on past PM2.5 spikes with the help of their annotations, and adapted their system usage as they familiarized themselves with their air quality data and MAAV. These results yield important insights for designing residential sensing systems that integrate into users' everyday lives.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264938"}, {"primary_key": "3270948", "vector": [], "sparse_vector": [], "title": "ProCMotive: Bringing Programmability and Connectivity into Isolated Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, numerous vehicular technologies, e.g., cruise control and steering assistant, have been proposed and deployed to improve the driving experience, passenger safety, and vehicle performance. Despite the existence of several novel vehicular applications in the literature, there still exists a significant gap between resources needed for a variety of vehicular (in particular, data-dominant, latency-sensitive, and computationally-heavy) applications and the capabilities of already-in-market vehicles. To address this gap, different smartphone-/Cloud-based approaches have been proposed that utilize the external computational/storage resources to enable new applications. However, their acceptance and application domain are still very limited due to programmability, wireless connectivity, and performance limitations, along with several security/privacy concerns. In this paper, we present a novel reference architecture that can potentially enable rapid development of various vehicular applications while addressing shortcomings of smartphone-/Cloud-based approaches. The architecture is formed around a core component, called SmartCore, a privacy/security-friendly programmable dongle that brings general-purpose computational and storage resources to the vehicle and hosts in-vehicle applications. Based on the proposed architecture, we develop an application development framework for vehicles, that we call ProCMotive. ProCMotive enables developers to build customized vehicular applications along the Cloud-to-edge continuum, i.e., different functions of an application can be distributed across SmartCore, the user's personal devices, and the Cloud. In order to highlight potential benefits that the framework provides, we design and develop two different vehicular applications based on ProCMotive, namely, Amber Response and Insurance Monitor. We evaluate these applications using real-world data and compare them with state-of-the-art technologies.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191758"}, {"primary_key": "3270950", "vector": [], "sparse_vector": [], "title": "Estimating the Physical Distance between Two Locations with Wi-Fi Received Signal Strength Information Using Obstacle-aware Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This study presents a new method for estimating the physical distance between two locations using Wi-Fi signals from APs observed by Wi-Fi signal receivers such as smartphones. We assume that a Wi-Fi signal strength vector is observed at location A and another Wi-Fi signal strength vector is observed at location B. With these two Wi-Fi signal strength vectors, we attempt to estimate the physical distance between locations A and B. In this study, we estimate the physical distance based on supervised machine learning and do not use labeled training data collected in an environment of interest. Note that, because signal propagation is greatly affected by obstacles such as walls, precisely estimating the distance between locations A and B is difficult when there is a wall between locations A and B. Our method first estimates whether or not there is a wall between locations A and B focusing on differences in signal propagation properties between 2.4 GHz and 5 GHz signals, and then estimates the physical distance using a neural network depending on the presence of walls. Because our approach is based on Wi-Fi signal strengths and does not require a site survey in an environment of interest, we believe that various context-aware applications can be easily implemented based on the distance estimation technique such as low-cost indoor navigation, the analysis and discovery of communities and groups, and Wi-Fi geo-fencing. Our experiment revealed that the proposed method achieved an MAE of about 3-4 meters and the performance is almost identical to an environment-dependent method, which is trained on labeled data collected in the same environment.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264940"}, {"primary_key": "3270951", "vector": [], "sparse_vector": [], "title": "FarSight: A Smartphone-based Vehicle Ranging System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Venkata N. <PERSON>"], "summary": "Maintaining an adequate separation from the vehicle in front is key to safe driving. While LIDAR and RADAR sensors could be used for ranging, cost considerations and the huge installed base of vehicles that lack these sensors, especially in developing regions, call for a low-cost yet robust alternative. To this end, we present FarSight, a system that performs vehicle ranging using a smartphone mounted on the windshield or the dashboard. FarSight uses the smartphone's camera to identify and draw a bounding box around vehicles in front, based on which ranging is performed. Unlike prior smartphone-based work, FarSight does not depend on any infrastructure support such as standard-width lane markers and works with a heterogeneous mix of vehicles, both of which are characteristics of developing regions. We develop a novel hybrid approach for vehicle detection and tracking, which balances accuracy and speed by combining deep neural network based vehicle detection with vision-based object tracking in a pipelined manner. We also devise data augmentation techniques to improve the effectiveness of vehicle detection, thereby increasing the ranging distance. We show that FarSight can range accurately in both daytime and nighttime conditions and up to distances of 90 m. We have implemented FarSight as an Android-app and tested it across various phones. Further, we present two ranging-based applications built on FarSight.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287059"}, {"primary_key": "3270952", "vector": [], "sparse_vector": [], "title": "Xnavi: Travel Planning System Based on Experience Flows.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Though an increasing number of people is now involved in travel planning owing to the spread of the internet, it is still difficult for travelers to plan trips on their own. It is especially difficult for tourists using automobiles because they have several choices of accessible places. To make itineraries easily, travelers require a travel planning system that suggests two types of experiences: experiences characterizing the travel area and experiences stemming from a flow between the former experiences. Existing systems do not list specific spontaneous experiences of interest to travelers. In response, Xnavi, a travel planning system for drivers based on experience flows, is proposed, which provides these types of experiences. To recommend experience flows, Xnavi extracts experience keywords related to the travel area using natural language processing based on the TF-IDF method and also extracts flows of tourist attractions' attributes based on association analysis of driving histories. Trials of the proposed method and a user study were conducted. The results show that Xnavi is effective at suggesting experiences and satisfying tourists with their plans.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191759"}, {"primary_key": "3270953", "vector": [], "sparse_vector": [], "title": "Variability in Reactions to Instructional Guidance during Smartphone-Based Assisted Navigation of Blind Users.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "'Turn slightly to the left' the navigational system announces, with the aim of directing a blind user to merge into a corridor. Yet, due to long reaction time, the user turns too late and proceeds into the wrong hallway. Observations of such user behavior in real-world navigation settings motivate us to study the manner in which blind users react to the instructional feedback of a turn-by-turn guidance system. We found little previous work analyzing the extent of the variability among blind users in reaction to different instructional guidance during assisted navigation. To gain insight into how navigational interfaces can be better designed to accommodate the information needs of different users, we conduct a data-driven analysis of reaction variability as defined by motion and timing measures. Based on continuously tracked user motion during real-world navigation with a deployed system, we find significant variability between users in their reaction characteristics. Specifically, the statistical analysis reveals significant variability during the crucial elements of the navigation (e.g., turning and encountering obstacles). With the end-user experience in mind, we identify the need to not only adjust interface timing and content to each user's personal walking pace, but also their individual navigation skill and style. The design implications of our study inform the development of assistive systems which consider such user-specific behavior to ensure successful navigation.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264941"}, {"primary_key": "3270955", "vector": [], "sparse_vector": [], "title": "The Internet of What?: Understanding Differences in Perceptions and Adoption for the Internet of Things.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This study explores people's perceptions of and attitudes towards Internet of Things (IoT) devices and their resulting (non)adoption behaviors. Based on 38 interviews (19 pairs each consisting of a Millennial and their parent), we found that few had a clear understanding of IoT, even among those who had already adopted it. Rather, they relied on two distinct conceptual models of IoT that shaped their beliefs, concerns, and adoption decisions: Many approached IoT with an \"user-centric\" technology mentality, viewing IoT devices as tools to be controlled by the end-user, and focusing on their tangible aspects (e.g. breakability). Others drew on an \"agentic\" technology perspective, where IoT behaviors were device-driven and, at times, negotiated between the user, other people, and/or the IoT devices. Our study revealed that consumer-oriented IoT currently cater towards the agentic view and raise concerns for those coming from a user-centric perspective. We also found that generational differences in attitudes towards IoT were rather explained by these differing perspectives. Instead of following the trend towards greater automation and agentic modes of interaction, we advocate for a hybrid and personalized approach that supports a spectrum of agentic and user-centric perspectives and provide design recommendations to work towards this end.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287061"}, {"primary_key": "3270956", "vector": [], "sparse_vector": [], "title": "Driving with the Fishes: Towards Calming and Mindful Virtual Reality Experiences for the Car.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present the use of in-car virtual reality (VR) as a way to create calm, mindful experiences for passengers and, someday, autonomous vehicle occupants. Specifically, we describe a series of studies aimed at exploring appropriate VR content, understanding the influence of car movement, and determining the length and other parameters of the simulation to avoid physical discomfort. Overall, our quantitative and qualitative insights suggest calm VR applications are well suited to an automotive context. Testing combinations of VR content designed to provide the participant with a static or dynamic experience versus stationary and moving vehicle modes, we find that a simulated experience of diving in the ocean while in a moving car elicited significantly lower levels of autonomic arousal as compared with a static VR plus stationary car condition. No significant motion sickness effects were subjectively reported by participants nor observable in the data, though a crossover interaction effect reveals how incongruence between the movement of the car and movement in VR could affect nausea. We conclude with recommendations for the design of calming and mindful VR experiences in moving vehicles.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287062"}, {"primary_key": "3270957", "vector": [], "sparse_vector": [], "title": "Just Breathe: In-Car Interventions for Guided Slow Breathing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>ur Al-h<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Motivated by the idea that slow breathing practices could transform the automobile commute from a depleting, mindless activity into a calming, mindful experience, we introduce the first guided slow breathing intervention for drivers. We describe a controlled in-lab experiment (N=24) that contrasts the effectiveness and impact of haptic and voice guidance modalities at slowing drivers' breathing pace, which is a known modulator of stress. The experiment was conducted in two simulated driving environments (city, highway) while driving in one of two driving modes (autonomous, manual). Results show that both haptic and voice guidance systems can reduce drivers' breathing rate and provide a sustained post-intervention effect without affecting driving safety. Subjectively, most participants (19/24) preferred the haptic stimuli as they found it more natural to follow, less distracting, and easier to engage and disengage from, compared to the voice stimuli. Finally, while most participants found guided breathing to be a positive experience, a few participants in the autonomous driving condition found slow breathing to be an unusual activity inside the car. In this paper, we discuss such considerations, offer guidelines for designing in-car breathing interventions, and propose future research that extends our work to on-road studies. Altogether, this paper serves as foundational work on guided breathing interventions for automobile drivers.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191760"}, {"primary_key": "3270958", "vector": [], "sparse_vector": [], "title": "Does the Public Still Look at Public Displays?: A Field Observation of Public Displays in the Wild.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Public displays are widely used for displaying information in public space, such as shopping centres. They are typically programmed to display advertisements or general information about the space in which they are situated. Due to recent advances in technology, public displays are becoming ubiquitous in space around cities and can potentially enable new interactions with public space. However, despite these advances, research reports that public displays are often found to be: (1) generally irrelevant to the space in which they are situated; and (2) ignored by passers-by. Although much research has focused on tackling these issues, a gap remains regarding knowledge about how public displays in the wild are currently being used at a time when people are increasingly relying on their smartphones as a main source for accessing information and for connecting with others. The study reported in this article aims to address this gap by presenting new insights about the current practices of non-research public displays and their role in a hyperconnected society. To achieve this, we provide results from a field observation study of non-research public displays and contextualise our findings within an analysis of related work. This article makes three main contributions: (1) identifying how user engagement with public displays has changed over the past 10 years; (2) understanding how the pervasiveness of smartphones and other connected devices has modified whether users notice public displays and their interactions with public displays; and (3) outlining design recommendations and opportunities towards making public displays more relevant in a hyperconnected society.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214276"}, {"primary_key": "3270959", "vector": [], "sparse_vector": [], "title": "Heed: Exploring the Design of Situated Self-Reporting Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Jeff) Huang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In-situ self-reporting is a widely used data collection technique for understanding people's behavior in context. Characteristics of smartphones such as their high proliferation, close proximity to their users, and heavy use have made them a popular choice for applications that require frequent self-reporting. Newer device categories such as wearables and voice assistants offer their own advantages, providing an opportunity to explore a wider range of self-reporting approaches. In this paper, we focus on exploring the design space of Situated Self-Reporting (SSR) devices. We present the Heed system, consisting of simple, low-cost, and low-power SSR devices that are distributed in the environment of the user and can be appropriated for reporting measures such as stress, sleepiness, and activities. In two real-world studies with 10 and 7 users, we compared and analyzed the use of smartphone and Heed devices to uncover differences in their use due to the influence of factors such as situational and social context, notification types, and physical design. Our findings show that Heed devices complemented smartphones in the coverage of activities, locations and interaction preferences. While the advantage of Heed was its single-purpose and dedicated location, smartphones provided mobility and flexibility of use.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264942"}, {"primary_key": "3270960", "vector": [], "sparse_vector": [], "title": "Finding the Sweet Spot(s): Understanding Context to Support Physical Activity Plans.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Predrag <PERSON>", "<PERSON>"], "summary": "Creating actionable plans has been shown to be helpful in promoting physical activity. However, little rèsearch has been done on how best to support the creation and execution of plans. In this paper, we interviewed 16 participants to study the role that context plays in the formulation and execution of plans for physical activity. Our findings highlight nuanced ways that contextual factors interact with each other and with individual differences to impact planning. We propose the notion of sweet spots to encapsulate how particular contextual factors converge to create optimal states for performing physical activities. The concept of sweet spots helped us to better understand the creation and execution of plans made by our participants. We present design guidelines to show how sweet spots can help support physical activity planning and guide the design of context-based tools for planning support.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191761"}, {"primary_key": "3270962", "vector": [], "sparse_vector": [], "title": "Smartphone-based Acoustic Indoor Space Mapping.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Constructing a map of indoor space has many important applications, such as indoor navigation, VR/AR, construction, safety, facility management, and network condition prediction. Existing indoor space mapping requires special hardware (e.g., indoor LiDAR equipment) and well-trained operators. In this paper, we develop a smartphone-based indoor space mapping system that lets a regular user quickly map an indoor space by simply walking around while holding a phone in his/her hand. Our system accurately measures the distance to nearby reflectors, estimates the user's trajectory, and pairs different reflectors the user encounters during the walk to automatically construct the contour. Using extensive evaluation, we show our contour construction is accurate: the median errors are 1.5 cm for a single wall and 6 cm for multiple walls (due to longer trajectory and the higher number of walls). We show that our system provides a median error of 30 cm and a 90-percentile error of 1 m, which is significantly better than the state-of-the-art smartphone acoustic mapping system BatMapper [64], whose corresponding errors are 60 cm and 2.5 m respectively, even after multiple walks. We further show that the constructed indoor contour can be used to predict wireless received signal strength (RSS).", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214278"}, {"primary_key": "3270969", "vector": [], "sparse_vector": [], "title": "IDrone: Robust Drone Identification through Motion Actuation Feedback.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Swarms of Unmanned Aerial Vehicles (drones) could provide great benefit when used for disaster response and indoor search and rescue scenarios. In these harsh environments where GPS availability cannot be ensured, prior work often relies on cameras for control and localization. This creates the challenge of identifying each drone, i.e., finding the association between each physical ID (such as their radio address) and their visual ID (such as an object tracker output). To address this problem, prior work relies on visual cues such as LEDs or colored markers to provide unique information for identification. However, these methods often increase deployment difficulty, are sensitive to environmental changes, not robust to distance and might require hardware changes. In this paper, we present IDrone, a robust physical drone identification system through motion matching and actuation feedback. The intuition is to (1) identify each drone by matching the motion detected through their inertial sensors and from an external camera, and (2) control the drones so they move in unique patterns that allow for fast identification, while minimizing the risk of collision involved in controlling drones with uncertain identification. To validate our approach, we conduct both simulation and real experimentation with autonomous drones for the simplified case of a stationary Spotter (powerful drone equipped with the camera). Overall, our initial results show that our approach offers a great tradeoff between fast identification and small collision probability. In particular, IDrone achieves faster identification time than safety-based baseline actuation (one-at-a-time), and significantly higher survival rate compared to fast, non-safety-based baseline actuation (random motion).", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214283"}, {"primary_key": "3270970", "vector": [], "sparse_vector": [], "title": "What Will You Do for the Rest of the Day?: An Approach to Continuous Trajectory Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Understanding and predicting human mobility is vital to a large number of applications, ranging from recommendations to safety and urban service planning. In some travel applications, the ability to accurately predict the user's future trajectory is vital for delivering high quality of service. The accurate prediction of detailed trajectories would empower location-based service providers with the ability to deliver more precise recommendations to users. Existing work on human mobility prediction has mainly focused on the prediction of the next location (or the set of locations) visited by the user, rather than on the prediction of the continuous trajectory (sequences of further locations and the corresponding arrival and departure times). Furthermore, existing approaches often return predicted locations as regions with coarse granularity rather than geographical coordinates, which limits the practicality of the prediction. In this paper, we introduce a novel trajectory prediction problem: given historical data and a user's initial trajectory in the morning, can we predict the user's full trajectory later in the day (e.g. the afternoon trajectory)? The predicted continuous trajectory includes the sequence of future locations, the stay times, and the departure times. We first conduct a comprehensive analysis about the relationship between morning trajectories and the corresponding afternoon trajectories, and found there is a positive correlation between them. Our proposed method combines similarity metrics over the extracted temporal sequences of locations to estimate similar informative segments across user trajectories. Our evaluation shows results on both labeled and geographical trajectories with a prediction error reduced by 10-35% in comparison to the baselines. This improvement has the potential to enable precise location services, raising usefulness to users to unprecedented levels. We also present empirical evaluations with Markov model and Long Short Term Memory (LSTM), a state-of-the-art Recurrent Neural Network model. Our proposed method is shown to be more effective when smaller number of samples are used and is exponentially more efficient than LSTM.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287064"}, {"primary_key": "3270971", "vector": [], "sparse_vector": [], "title": "A Weakly Supervised Learning Framework for Detecting Social Anxiety and Depression.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although social anxiety and depression are common, they are often underdiagnosed and undertreated, in part due to difficulties identifying and accessing individuals in need of services. Current assessments rely on client self-report and clinician judgment, which are vulnerable to social desirability and other subjective biases. Identifying objective, nonburdensome markers of these mental health problems, such as features of speech, could help advance assessment, prevention, and treatment approaches. Prior research examining speech detection methods has focused on fully supervised learning approaches employing strongly labeled data. However, strong labeling of individuals high in symptoms or state affect in speech audio data is impractical, in part because it is not possible to identify with high confidence which regions of a long speech indicate the person's symptoms or affective state. We propose a weakly supervised learning framework for detecting social anxiety and depression from long audio clips. Specifically, we present a novel feature modeling technique named NN2Vec that identifies and exploits the inherent relationship between speakers' vocal states and symptoms/affective states. Detecting speakers high in social anxiety or depression symptoms using NN2Vec features achieves F-1 scores 17% and 13% higher than those of the best available baselines. In addition, we present a new multiple instance learning adaptation of a BLSTM classifier, named BLSTM-MIL. Our novel framework of using NN2Vec features with the BLSTM-MIL classifier achieves F-1 scores of 90.1% and 85.44% in detecting speakers high in social anxiety and depression symptoms.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214284"}, {"primary_key": "3270972", "vector": [], "sparse_vector": [], "title": "A Data-Driven Misbehavior Detection System for Connected Autonomous Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In a Connected and Autonomous Vehicle (CAV) system, some malicious CAVs may send out false information in vehicle-to-vehicle communication to gain benefits or cause safety-related accidents. Previous false data detection methods are not sufficient to meet the accuracy and real-time requirements. In this paper, we propose a data-driven misbehavior detection system (MDS) (running by each CAV) that checks the consistency between the estimated and actually reported driving state (i.e., velocity, acceleration, brake status, steering angle) of an alerting CAV. First, MDS predicts the driving state using Gaussian mixture model based Mixture Density Network incorporating Recurrent Neural Network that can catch the driving behavior patterns of a CAV. Second, MDS extends the existing <PERSON><PERSON>uss traffic flow model and uses it to consider the overall traffic flow of the road to make the predicted driving state more accurate. Finally, for a given received alert, a CAV validates the alert by checking the consistency between the predicted and actually reported driving states of the alerting CAV. We conduct extensive simulation studies based on a real driving dataset we collected from 29 participants and the Simulator for Urban MObility (SUMO) traffic simulator. The experimental results show that the false information detection rate of the proposed MDS is higher than other existing systems in different alert scenarios.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287065"}, {"primary_key": "3270974", "vector": [], "sparse_vector": [], "title": "Room-Wide Wireless Charging and Load-Modulation Communication via Quasistatic Cavity Resonance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Jack) <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rise of the Internet of Things (IoT) has led to a significant increase in the number of connected devices that stream data in our homes, offices and industrial spaces. However, as the number of these devices increases, the costs of actively maintaining and replacing batteries becomes prohibitive at scale. Recent work on Quasistatic Cavity Resonance (QSCR), offers the possibility of seamless wireless power transfer (WPT) to receivers placed anywhere inside large indoor spaces. This work aims to solve two unexplored and critical missing pieces needed to realize this vision of ubiquitous WPT. First, we demonstrate a full end-to-end QSCR-based WPT system that is capable of simultaneously charging multiple custom designed nodes nearly anywhere in the 4.9 m x 4.9 m x 2.3 m test room. Second, this work utilizes the WPT mechanism as a communication channel, where nodes communicate with a centralized reader and to each other via load modulation. Through analysis and experiments, the proposed system shows that 10 receiver nodes can be safely and efficiently wirelessly charged and the end node to end node communication rate can achieve from 1 kbps without occurring any errors, up to 5 kbps with 6% BER while the end node to central unit can achieve 10 kbps without occurring any errors.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287066"}, {"primary_key": "3270976", "vector": [], "sparse_vector": [], "title": "Visual Attention-Based Access: Granting Access Based on Users&apos; Joint Attention on Shared Workspaces.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "During collaboration, individual users' capacity to maintain awareness, avoid duplicate work and prevent conflicts depends on the extent to which they are able to monitor the workspace. Existing access control models disregard this contextual information by managing access strictly based on who performs the action. As an alternative approach, we propose managing access by taking the visual attention of collaborators into account. For example, actions that require consensus can be limited to collaborators' joint attention, editing another user's personal document can require her visual supervision and private information can become unavailable when another user is looking. We prototyped visual attention-based access for 3 collaboration scenarios on a large vertical display using head orientation input as a proxy for attention. The prototype was deployed for an exploratory user study, where participants in pairs were tasked to assign visual attention-based access to various actions. The results reveal distinct motivations for their use such as preventing accidents, maintaining individual control and facilitating group awareness. Visual attention-based access has been perceived as more convenient but also less certain when compared to traditional access control. We conclude that visual attention-based access can be a useful addition to groupware to flexibly facilitate awareness and prevent conflicts.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264943"}, {"primary_key": "3270977", "vector": [], "sparse_vector": [], "title": "Augmenting User Identification with WiFi Based Gesture Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over the last few years, researchers have proposed several WiFi based gesture recognition systems that can recognize predefined gestures performed by users at runtime. As most environments are inhabited by multiple users, the true potential of WiFi based gesture recognition can be unleashed only when each user can independently define the actions that the system should take when the user performs a certain predefined gesture. To enable this, a gesture recognition system should not only be able to recognize any given predefined gesture, but should also be able to identify the user that performed it. Unfortunately, none of the prior WiFi based gesture recognition systems can identify the user performing the gesture. In this paper, we propose WiID, a WiFi and gesture based user identification system that can identify the user as soon as he/she performs a predefined gesture at runtime. WiID integrates with the WiFi based gesture recognition systems as an add-on module whose sole objective is to identify the users that perform the predefined gestures. The design of WiID is based on our novel result which states that the timeseries of the frequencies that appear in WiFi channel's frequency response while performing a given gesture are different in the samples of that gesture performed by different users, and are similar in the samples of that gesture performed by the same user. We implemented and extensively evaluated WiID in a variety of environments using a comprehensive data set comprising over 25,000 gesture samples.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264944"}, {"primary_key": "3270978", "vector": [], "sparse_vector": [], "title": "TOAST: Ten-Finger Eyes-Free Typing on Touchable Surfaces.", "authors": ["Weinan Shi", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Touch typing on flat surfaces (e.g. interactive tabletop) is challenging due to lack of tactile feedback and hand drifting. In this paper, we present TOAST, an eyes-free keyboard technique for enabling efficient touch typing on touch-sensitive surfaces. We first formalized the problem of keyboard parameter (e.g. location and size) estimation based on users' typing data. Through a user study, we then examined users' eyes-free touch typing behavior on an interactive tabletop with only asterisk feedback. We fitted the keyboard model to the typing data, results suggested that the model parameters (keyboard location and size) changed not only between different users, but also within the same user along with time. Based on the results, we proposed a Markov-Bayesian algorithm for input prediction, which considers the relative location between successive touch points within each hand respectively. Simulation results showed that based on the pooled data from all users, this model improved the top-1 accuracy of the classical statistical decoding algorithm from 86.2% to 92.1%. In a second user study, we further improved TOAST with dynamical model parameter adaptation, and evaluated users' text entry performance with TOAST using realistic text entry tasks. Participants reached a pick-up speed of 41.4 WPM with a character-level error rate of 0.6%. And with less than 10 minutes of practice, they reached 44.6 WPM without sacrificing accuracy. Participants' subjective feedback also indicated that TOAST offered a natural and efficient typing experience.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191765"}, {"primary_key": "3270979", "vector": [], "sparse_vector": [], "title": "Riskalyzer: Inferring Individual Risk-Taking Propensity Using Phone Metadata.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An individual's risk-taking propensity is \"the stable tendency to choose options with a lower probability of success, but greater rewards\". This risk propensity plays a central role in decision making by customers as well as managers, and is a mediator in behavior associated with security, privacy, health, finance, and well-being. Most common approach to understanding an individual's risk propensity remain lab-based games and surveys. Administering such surveys and games is a manual, time, and money intensive process that is also fraught with multiple biases. Recently, smartphones are increasingly seen as large-scale sensors of human activity, recording data related to physical and social aspects of people's lives. Building on this trend we investigate the potential of passive phone-based data for automatically inferring an individual's risk propensity. Specifically, we describe a novel approach to model an individual's risk propensity based on her mobile phone usage. Based on a 10-week field + lab study involving 50 participants, we report that: (1) multiple phone-based features (e.g., average gyradius) are intricately associated with participants' risk propensity; and (2) a phone-based model outperforms demography-based models by 39% in terms of accuracy of predicting risk propensity. In organizational terms, a better understanding of risk behavior could contribute significantly to risk management programs. At the same time, such results could open doors for more nuanced understanding of the underlying human risk phenomena and their interconnections with social and mobility behavior.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191766"}, {"primary_key": "3270980", "vector": [], "sparse_vector": [], "title": "Deep Room Recognition Using Inaudible Echos.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent years have seen the increasing need of location awareness by mobile applications. This paper presents a room-level indoor localization approach based on the measured room's echos in response to a two-millisecond single-tone inaudible chirp emitted by a smartphone's loudspeaker. Different from other acoustics-based room recognition systems that record full-spectrum audio for up to ten seconds, our approach records audio in a narrow inaudible band for 0.1 seconds only to preserve the user's privacy. However, the short-time and narrowband audio signal carries limited information about the room's characteristics, presenting challenges to accurate room recognition. This paper applies deep learning to effectively capture the subtle fingerprints in the rooms' acoustic responses. Our extensive experiments show that a two-layer convolutional neural network fed with the spectrogram of the inaudible echos achieve the best performance, compared with alternative designs using other raw data formats and deep models. Based on this result, we design a RoomRecognize cloud service and its mobile client library that enable the mobile application developers to readily implement the room recognition functionality without resorting to any existing infrastructures and add-on hardware. Extensive evaluation shows that RoomRecognize achieves 99.7%, 97.7%, 99%, and 89% accuracy in differentiating 22 and 50 residential/office rooms, 19 spots in a quiet museum, and 15 spots in a crowded museum, respectively. Compared with the state-of-the-art approaches based on support vector machine, RoomRecognize significantly improves the Pareto frontier of recognition accuracy versus robustness against interfering sounds (e.g., ambient music).", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264945"}, {"primary_key": "3270981", "vector": [], "sparse_vector": [], "title": "My Smartphone Recognizes Genuine QR Codes!: Practical Unclonable QR Code via 3D Printing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Additive manufacturing, or 3D printing, has been widely applied in product manufacturing. However, the emerging unauthorized access of 3D printing data, as well as the growth in the pervasiveness and capability of 3D printing devices have raised serious concerns about 3D printing product anti-counterfeit. Electronic product tags are the current standard for authentication purposes; however, often this technology is neither secure nor cost-effective, and fails to take advantage of other unique 3D printing features. Considering the great usability of the QR code, we are motivated to enhance the QR code for the practical and cost-effective 3D printing product identification. Particularly, we bring up the all-in-one design, all-in-one manufacturing concept incorporating the QR code in the complete 3D printing paradigm. In detail, we explore the possibility of leveraging the random and uncontrollable process variations in the 3D printing system to generate a unique fingerprint for the integrated QR code. To this end, we present an end-to-end 3D-printed QR code verification framework, which does not change the original QR protocol and functionality. The entire solution can be implemented with commodity 3D printers and smartphones. Specifically, we first investigate the inevitable and random process variations in the 3D printing mechanism and analyze the causality between the variations and detectable geometric deformation. We further develop a fingerprint extraction algorithm taking into account both the QR code property and the 3D printer characteristics. The system evaluation indicates that our solution is secure and robust in multiple scenarios.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214286"}, {"primary_key": "3270982", "vector": [], "sparse_vector": [], "title": "RuleSelector: Selecting Conditional Action Rules from User Behavior Patterns.", "authors": ["<PERSON>", "<PERSON>", "Hongxia Jin"], "summary": "Modern smartphones and ubiquitous computing systems collect a wealth of context data from users. Conditional action rules, as popularized by the IFTTT (If-This-Then-That) platform, are a popular way for users to automate frequently repeated tasks or receive smart reminders, due to the intelligibility and control that rules provide to users. A key drawback of IFTTT systems is that they place the burden of manually specifying action rules on the user. While multiple rule mining algorithms have been proposed in existing work to automatically discover action rules, they generate hundreds of action rules, and the problem of how to present a small subset of rules to smartphone users and allow them to interactively select action rules remains unsolved. In this work, we take the first step towards solving this problem by designing and implementing RuleSelector, the first interactive rule selection tool to allow smartphone users to browse, modify, and select action rules from a small set of summarized rules presented to the user. We propose novel rule selection metrics to address the needs of smartphone users, and analyze the performance of RuleSelector using data from 200 users. We also perform a qualitative user study in order to evaluate how users use the RuleSelector tool and perceive the selected rules, and present the insights gained and design recommendations for future rule selection systems. Our users rated the selected rules from useful to very useful, and an important finding of our study is that users prefer an interactive rule selection system such as RuleSelector that automatically suggests rules, but allows users to select and modify the suggested rules. Finally, we examine the promise of RuleSelector in other ubiquitous computing systems such as smart homes and smart TVs by applying our tool to public context datasets from these domains.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191767"}, {"primary_key": "3270983", "vector": [], "sparse_vector": [], "title": "Combining Low and Mid-Level Gaze Features for Desktop Activity Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Human activity recognition (HAR) is an important research area due to its potential for building context-aware interactive systems. Though movement-based activity recognition is an established area of research, recognising sedentary activities remains an open research question. Previous works have explored eye-based activity recognition as a potential approach for this challenge, focusing on statistical measures derived from eye movement properties---low-level gaze features---or some knowledge of the Areas-of-Interest (AOI) of the stimulus---high-level gaze features. In this paper, we extend this body of work by employing the addition of mid-level gaze features; features that add a level of abstraction over low-level features with some knowledge of the activity, but not of the stimulus. We evaluated our approach on a dataset collected from 24 participants performing eight desktop computing activities. We trained a classifier extending 26 low-level features derived from existing literature with the addition of 24 novel candidate mid-level gaze features. Our results show an overall classification performance of 0.72 (F1-Score), with up to 4% increase in accuracy when adding our mid-level gaze features. Finally, we discuss the implications of combining low- and mid-level gaze features, as well as the future directions for eye-based activity recognition.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287067"}, {"primary_key": "3270984", "vector": [], "sparse_vector": [], "title": "A Cuttable Wireless Power Transfer Sheet.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a cuttable wireless power transfer sheet which allows users to modify its size and shape. This intuitive manipulation allows users to easily add wireless power transmission capabilities to everyday objects. The properties of the sheet such as thinness, flexibility, and lightness make our sheet highly compatible with various configurations. We contribute a set of technical principles for the design of circuitry, which integrates H-tree wiring and time division power supply techniques. H-tree wiring allows the sheet to remain functional even when cut from the outside of the sheet, whereas time division power supply avoids the reduction in power transfer efficiency caused by the magnetic interference between adjacent transmitter coils. Through the evaluations, we found that our time division power supply scheme mitigates the degradation of power transfer efficiency and successfully improves the average efficiency. Furthermore, we present four applications which integrates our sheet into daily objects: wireless charging furniture, bag, jacket, and craft; these applications confirmed the feasibility of our prototype.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287068"}, {"primary_key": "3270985", "vector": [], "sparse_vector": [], "title": "Rewind: Automatically Reconstructing Everyday Memories with First-Person Perspectives.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Phucanh <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Snapping photos or videos on a smartphone makes recording visual memories convenient, but what isn't captured may still be meaningful in retrospect. In this paper, digital mementos are automatically generated for participants using the Rewind system, which assists the recall of location-based minutiae. Rewind is a video-like medium describing people's daily excursions using a sequence of street-level images determined by self-tracked location data. The Rewinds are color-processed to reflect seasonal, time-of-day and weather characteristics. Through two user studies with a combined 40 users, Rewinds were shown to be used as memory artifacts, and are especially meaningful in the many situations when photos or videos are not available. The small cues in Rewinds evoke longer fragments of memory tied to nostalgic routines or significant events, and the sequence of images provide a first-person perspective of remembrance for users. While they are more generic and can possess inaccuracies, Rewinds give people anchors for memories that feel like their own that are used to craft a narrative for that day. <PERSON><PERSON> strikes a balance between automatic logging and manually-curated memories by personalizing it in meaningful visuals and consolidating an otherwise overwhelming amount of data.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287069"}, {"primary_key": "3270986", "vector": [], "sparse_vector": [], "title": "SilentKey: A New Authentication Framework through Ultrasonic-based Lip Reading.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents SilentKey, a new authentication framework to identify mobile device users through ultrasonic-based lip reading. The main idea is to generate ultrasonic signals from a mobile phone and analyze the fine-grained impact of mouth motions on the reflected signal. The new framework is effective since people have unique characteristics when performing mouth motions, which represent not only what people input, but also how they input. SilentKey is robust against attacks since the input cannot be recorded or imitated. We implement a prototype and demonstrate the effectiveness of the system by fifty volunteers. Such a non-intrusive identification mechanism provides a natural user interface which can also be applied by people with speaking or viewing difficulties.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191768"}, {"primary_key": "3270988", "vector": [], "sparse_vector": [], "title": "Defining Adherence: Making Sense of Physical Activity Tracker Data.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Increasingly, people are collecting detailed personal activity data from commercial trackers. Such data should be able to give important insights about their activity levels. However, people do not wear or carry tracking devices all day, every day and this means that tracker data is typically incomplete. This paper aims to provide a systematic way to take account of this incompleteness, by defining adherence, a measure of data completeness, based on how much people wore their tracker. We show the impact of different adherence definitions on 12 diverse datasets, for 753 users, with over 77,000 days with data, interspersed with over 73,000 days without data. For example, in one data set, one adherence measure gives an average step count of 6,952 where another gives 9,423. Our results show the importance of adherence when analysing and reporting activity tracker data. We provide guidelines for defining adherence, analysing its impact and reporting it along with the results of the tracker data analysis. Our key contribution is the foundation for analysis of physical activity data, to take account of data incompleteness.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191769"}, {"primary_key": "3270989", "vector": [], "sparse_vector": [], "title": "RF-Based Fall Monitoring Using Convolutional Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Falls are the top reason for fatal and non-fatal injuries among seniors. Existing solutions are based on wearable fall-alert sensors, but medical research has shown that they are ineffective, mostly because seniors do not wear them. These revelations have led to new passive sensors that infer falls by analyzing Radio Frequency (RF) signals in homes. Seniors can go about their lives as usual without the need to wear any device. While passive monitoring has made major advances, current approaches still cannot deal with the complexities of real-world scenarios. They typically train and test their classifiers on the same people in the same environments, and cannot generalize to new people or new environments. Further, they cannot separate motions from different people and can easily miss a fall in the presence of other motions. To overcome these limitations, we introduce Aryokee, an RF-based fall detection system that uses convolutional neural networks governed by a state machine. Aryokee works with new people and environments unseen in the training set. It also separates different sources of motion to increase robustness. Results from testing Aryokee with over 140 people performing 40 types of activities in 57 different environments show a recall of 94% and a precision of 92% in detecting falls.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264947"}, {"primary_key": "3270990", "vector": [], "sparse_vector": [], "title": "Takes <PERSON><PERSON> to Ballet: Designing Visual and Verbal Feedback for Augmented Mirrors.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Mirrors have been a core feature in ballet studios for over five hundred years. While physical mirrors provide real-time feedback, they do not inform dancers of their errors. Thus, technologies such as motion tracking have been used to augment what a physical mirror can provide. Current augmented mirrors, however, only implement one mode of communication, usually visual, and do not provide a holistic feedback to dancers that includes all the feedback elements commonly used in ballet classes. We conducted a mixed-method study with 16 novices and 16 expert dancers in which we compared two different modes of communication (visual and verbal), two different types of feedback (value and corrective) and two levels of guidance (mirror, or no mirror). Participants' ballet technique scores were evaluated by a remote teacher on eight ballet combinations (tendue, adagio, pirouette, saute, plié, degage, frappe and battement tendue). We report quantitative and qualitative results that show how the level of guidance, mode of communication, and type of feedback, needs to be tuned in different ways for novices and experts.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191770"}, {"primary_key": "3270992", "vector": [], "sparse_vector": [], "title": "Duet: Estimating User Position and Identity in Smart Homes Using Intermittent and Incomplete RF-Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Although past work on RF-based indoor localization has delivered important advances, it typically makes assumptions that hinder its adoption in smart home applications. Most localization systems assume that users carry their phones on them at home, an assumption that has been proven highly inaccurate in past measurements. The few localization systems that do not require the user to carry a device on her, cannot tell the identity of the person; yet identification is essential to most smart home applications. This paper focuses on addressing these issues so that smart homes can benefit from recent advances in indoor localization. We introduce Duet, a multi-modal system that takes as input measurements from both device-based and device-free localization. Duet introduces a new framework that combines probabilistic inference with first order logic to reason about the users' most likely locations and identities in light of the measurements. We implement Duet and compare it with a baseline that uses state-of-art WiFi-based localization. The results of two weeks of monitoring in two smart environments show that <PERSON><PERSON> accurately localizes and identifies the users for 94% and 96% of the time in the two places. In contrast, the baseline is accurate 17% and 42% respectively.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214287"}, {"primary_key": "3270993", "vector": [], "sparse_vector": [], "title": "Gesture Recognition Using Ambient Light.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "There is a growing interest in the scientific community to develop techniques for humans to communicate with the computing that is embedding into our environments. Researchers are already exploring ubiquitous modalities, such as radio frequency signals, to develop gesture recognition systems. In this paper, we explore another such modality, namely ambient light, and develop LiGest, an ambient light based gesture recognition system. The key property of LiGest is that it is agnostic to lighting conditions, position and orientation of user, and who performs the gestures. The general idea behind LiGest is that when a user performs different gestures, the shadows of the user move in unique patterns. LiGest first learns these patterns using training samples and then recognizes unknown samples by matching them with the learnt patterns. To capture these patterns, LiGest uses a grid of light sensors deployed on floor. While the general idea behind LiGest seems straightforward, it is actually very challenging to put it into practice because the intensity, size, and number of shadows of a user are not fixed and depend highly on the position and orientation of a user as well as on the intensity, position, and number of light sources. We developed a prototype of LiGest using commercially available light sensors and extensively evaluated it with the help of 20 volunteers. Our results show that LiGest achieves an average accuracy of 96.36% across all volunteers.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191772"}, {"primary_key": "3270994", "vector": [], "sparse_vector": [], "title": "A Language for Online State Processing of Binary Sensors, Applied to Ambient Assisted Living.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "There is a large variety of binary sensors in use today, and useful context-aware services can be defined using such binary sensors. However, the currently available approaches for programming context-aware services do not conveniently support binary sensors. Indeed, no existing approach simultaneously supports a notion of state, central to binary sensors, offers a complete set of operators to compose states, allows to define reusable abstractions by means of such compositions, and implements efficient online processing of these operators. This paper proposes a new language for event processing specifically targeted to binary sensors. The central contributions of this language are a native notion of state and semi-causal operators for temporal state composition including: <PERSON>'s interval relations generalized for handling multiple intervals, and temporal filters for handling delays. Compared to other approaches such as CEP (complex event processing), our language provides less discontinued information, allows less restricted compositions, and supports reusable abstractions. We implemented an interpreter for our language and applied it to successfully rewrite a full set of real Ambient Assisted Living services. The performance of our prototype interpreter is shown to compete well with a commercial CEP engine when expressing the same services.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287070"}, {"primary_key": "3270996", "vector": [], "sparse_vector": [], "title": "Data and Expert Models for Sleep Timing and Chronotype Estimation from Smartphone Context Data and Simulations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a sleep timing estimation approach that combines data-driven estimators with an expert model and uses smartphone context data. Our data-driven methodology comprises a classifier trained on features from smartphone sensors. Another classifier uses time as input. Expert knowledge is incorporated via the human circadian and homeostatic two process model. We investigate the two process model as output filter on classifier results and as fusion method to combine sensor and time classifiers. We analyse sleep timing estimation performance, in data from a two-week free-living study of 13 participants and sensor data simulations of arbitrary sleep schedules, amounting to 98280 nights. Five intuitive sleep parameters were derived to control the simulation. Moreover, we investigate model personalisation, by retraining classifiers based on participant feedback. The joint data and expert model yields an average relative estimation error of -2±62 min for sleep onset and -5±70 min for wake (absolute errors 40±48 min and 42±57 min, mean median absolute deviation 22 min and 15 min), which significantly outperforms data-driven methods. Moreover, the data and expert models combination remains robust under varying sleep schedules. Personalising data models with user feedback from the last two days showed the largest performance gain of 57% for sleep onset and 59% for wake up. Our power-efficient smartphone app makes convenient everyday sleep monitoring finally realistic.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264949"}, {"primary_key": "3270997", "vector": [], "sparse_vector": [], "title": "SpiderWalk: Circumstance-aware Transportation Activity Detection Using a Novel Contact Vibration Sensor.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents the design and implementation of the SpiderWalk system for circumstance-aware transportation activity detection using a novel contact vibration sensor. Different from existing systems that only report the type of activity, our system detects not only the activity but also its circumstances (e.g., road surface, vehicle, and shoe types) to provide better support for applications such as activity logging, location tracking, and smart persuasive applications. Inspired by but different from existing audio-based context detection approaches using microphones, the SpiderWalk system is designed and implemented using an ultra-sensitive, flexible contact vibration sensor which mimics the spiders' sensory slit organs. By sensing vibration patterns from the soles of shoes, the system can accurately detect transportation activities with rich circumstance information while resisting undesirable external signals from other sources or speech that may cause the data assignment and privacy preserving issues. Moreover, our system is implemented by reusing existing audio devices and can be used by an unmodified smartphone, making it ready for large-scale deployments. Finally, a novel temporal and spatial correlated classification approach is proposed to accurately detect the complex combinations of transportation activities and circumstances based on the output of each individual classifiers. Experiments conducted on a real-world data set suggest our system can accurately detect different transportation activities and their circumstances with an average detection accuracy of 93.8% with resource overheads comparable to existing audio- and GPS-based systems.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191774"}, {"primary_key": "3271000", "vector": [], "sparse_vector": [], "title": "BRAVO: Improving the Rebalancing Operation in Bike Sharing with Rebalancing Range Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yuanchao Shu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Bike sharing systems, which provide a convenient commute choice for short trips, have emerged rapidly in many cities. While bike sharing has greatly facilitated people's commutes, those systems are facing a costly maintenance issue -- rebalancing bikes among stations. We observe that existing systems frequently suffer situations such as no-bike-to-borrow (empty) or no-dock-to-return (full) due to existing ad hoc rebalancing practice. To address this issue, we provide systematic analysis on user trip data, station status data, rebalancing data, and meteorology data, and propose BRAVO - the first practical data-driven bike rebalancing app for operators to improve bike sharing service while reducing the maintenance cost. Specifically, leveraging experiences from two-round round-the-clock field studies and comprehensive information from four data sets, a data-driven model is proposed to capture and predict the safe rebalancing range for each station. Based on this safe rebalancing range, BRAVO computes the optimal rebalancing amounts for the full and empty stations to minimize the rebalancing cost. BRAVO is evaluated with 24-month data from Capital, Hangzhou and NiceRide bikeshare systems. The experiment results show that given the same user demand, BRAVO reduces 28% of the station visits and 37% of the rebalancing amounts.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191776"}, {"primary_key": "3271002", "vector": [], "sparse_vector": [], "title": "ObstacleWatch: Acoustic-based Obstacle Collision Detection for Pedestrian Using Smartphone.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Walking while using a smartphone is becoming a major pedestrian safety concern as people may unknowingly bump into various obstacles that could lead to severe injuries. In this paper, we propose ObstacleWatch, an acoustic-based obstacle collision detection system to improve the safety of pedestrians who are engaged in smartphone usage while walking. ObstacleWatch leverages the advanced audio hardware of the smartphone to sense the surrounding obstacles and infers fine-grained information about the frontal obstacle for collision detection. In particular, our system emits well-designed inaudible beep signals from the smartphone built-in speaker and listens to the reflections with the stereo recording of the smartphone. By analyzing the reflected signals received at two microphones, ObstacleWatch is able to extract fine-grained information of the frontal obstacle including the distance, angle and size for detecting the possible collisions and to alert users. Our experimental evaluation under two real-world environments with different types of phones and obstacles shows that ObstacleWatch achieves over 92% accuracy in predicting obstacle collisions with distance estimation errors at about 2 cm. Results also show that ObstacleWatch is robust to different sizes of objects and is compatible to different phone models with low energy consumption.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287072"}, {"primary_key": "3271004", "vector": [], "sparse_vector": [], "title": "RF-ECG: Heart Rate Variability Assessment Based on COTS RFID Tag Array.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As an important indicator of autonomic regulation for circulatory function, Heart Rate Variability (HRV) is widely used for general health evaluation. Apart from using dedicated devices (e.g, ECG) in a wired manner, current methods search for a ubiquitous manner by either using wearable devices, which suffer from low accuracy and limited battery life, or applying wireless techniques (e.g., FMCW), which usually utilize dedicated devices (e.g., USRP) for the measurement. To address these issues, we present RF-ECG based on Commercial-Off-The-Shelf (COTS) RFID, a wireless approach to sense the human heartbeat through an RFID tag array attached on the chest area in the clothes. In particular, as the RFID reader continuously interrogates the tag array, two main effects are captured by the tag array: the reflection effect representing the RF-signal reflected from the heart movement due to heartbeat; the moving effect representing the tag movement caused by chest movement due to respiration. To extract the reflection signal from the noisy RF-signals, we develop a mechanism to capture the RF-signal variation of the tag array caused by the moving effect, aiming to eliminate the signals related to respiration. To estimate the HRV from the reflection signal, we propose a signal reflection model to depict the relationship between the RF-signal variation from the tag array and the reflection effect associated with the heartbeat. A fusing technique is developed to combine multiple reflection signals from the tag array for accurate estimation of HRV. Experiments with 15 volunteers show that RF-ECG can achieve a median error of 3% of Inter-Beat Interval (IBI), which is comparable to existing wired techniques.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214288"}, {"primary_key": "3271005", "vector": [], "sparse_vector": [], "title": "Modeling RFID Signal Reflection for Contact-free Activity Recognition.", "authors": ["<PERSON><PERSON>", "Yuan<PERSON> Zheng"], "summary": "Wireless sensing techniques for tracking human activities have been vigorously developed in recent years. Yet current RFID based human activity recognition techniques need either direct contact to human body (e.g., attaching RFIDs to users) or specialized hardware (e.g., software defined radios, antenna array). How to wirelessly track human activities using commodity RFID systems without attaching tags to users (i.e., a contact-free scenario) still faces lots of technical challenges. In this paper, we quantify the correlation between RF phase values and human activities by modeling intrinsic characteristics of signal reflection in contact-free scenarios. Based on the signal reflection model, we introduce TACT that can recognize human activities using commodity RFIDs without attaching any RFID tags to users. TACT first reliably detects the presence of human activities and segments phase values. Then, candidate phase segments are classified according to their coarse-grained features (e.g., moving speed, moving distance, activity duration) as well as their fine-grained feature of phase waveform. We deploy and leverage multiple tags to increase the coverage and enhance the robustness of the system. We implement TACT with commodity RFID systems. We invite 12 participants to evaluate our system in various scenarios. The experiment results show that TACT can recognize eight types of human activities with 93.5% precision under different and challenging experiment settings.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287071"}, {"primary_key": "3271006", "vector": [], "sparse_vector": [], "title": "Large-scale Automatic Depression Screening Using Meta-data from WiFi Infrastructure.", "authors": ["Shweta Ware", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jin<PERSON> B<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Depression is a serious public health problem. Current diagnosis techniques rely on physician-administered or patient self-administered interview tools, which are burdensome and suffer from recall bias. Recent studies have proposed new approaches that use sensing data collected on smartphones to serve as \"human sensors\" for automatic depression screening. These approaches, however, require running an app on the phones for continuous data collection. We explore a novel approach that uses data collected from WiFi infrastructure for large-scale automatic depression screening. Specifically, when smartphones connect to a WiFi network, their locations (and hence the locations of the users) can be determined by the access points that they associate with; the location information over time provides important insights into the behavior of the users, which can be used for depression screening. To investigate the feasibility of this approach, we have analyzed two datasets, each collected over several months, involving tens of participants recruited from a university. Our results demonstrate that WiFi meta-data is effective for passive depression screening: the F1 scores are as high as 0.85 for predicting depression, comparable to those obtained by using sensing data collected directly from smartphones.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287073"}, {"primary_key": "3271007", "vector": [], "sparse_vector": [], "title": "FocusVR: Effective 8 Usable VR Display Power Management.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present the design and implementation of FocusVR, a system for effectively and efficiently reducing the power consumption of Virtual Reality (VR) devices by smartly dimming their displays. These devices are becoming increasingly common with large companies such as Facebook (Oculus Rift), and HTC and Valve (Vive), recently releasing high quality VR devices to the consumer market. However, these devices require increasingly higher screen resolutions and refresh rates to be effective, and this in turn, leads to high display power consumption costs. We show how the use of smart dimming techniques, vignettes and color mapping, can significantly reduce the power consumption of VR displays with minimal impact on usability. In particular, we describe the implementation of FocusVR in both Android and the Unity game engine and then present detailed measurement results across 3 different VR devices -- the Gear VR, the DK2, and the Vive. In addition, we present the results of 3 user studies, with 68 participants in total, that tested the usability of FocusVR. Overall, we show that FocusVR is able to save up to 80% of the display power and up to 50% of the overall system power, with negligible impact to usability.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264952"}, {"primary_key": "3271008", "vector": [], "sparse_vector": [], "title": "coSense: Collaborative Urban-Scale Vehicle Sensing Based on Heterogeneous Fleets.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The real-time vehicle sensing at urban scale is essential to various urban services. To date, most existing approaches rely on static infrastructures (e.g., traffic cameras) or mobile services (e.g., smartphone apps). However, these approaches are often inadequate for urban scale vehicle sensing at the individual level because of their static natures or low penetration rates. In this paper, we design a sensing system called coSense to utilize commercial vehicular fleets (e.g., taxis, buses, and trucks) for real-time vehicle sensing at urban scale, given (i) the availability of well-equipped commercial fleets sensing other vehicles by onboard cameras or peer-to-peer communication, and (ii) an increasing trend of connected vehicles and autonomous vehicles with periodical status broadcasts for safety applications. Compared to existing solutions based on cameras and smartphones, the key features of coSense are in its high penetration rates and transparent sensing for participating drivers. The key technical challenge we addressed is how to recover spatiotemporal sensing gaps by considering various mobility patterns of commercial vehicles with deep learning. We evaluate coSense with a preliminary road test and a large-scale trace-driven evaluation based on vehicular fleets in the Chinese city Shenzhen, including 14 thousand taxis, 13 thousand buses, 13 thousand trucks, and 10 thousand regular vehicles. We compare coSense to infrastructure and cellphone-based approaches, and the results show that we increase the sensing accuracy by 10.1% and 16.6% on average.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287074"}, {"primary_key": "3271009", "vector": [], "sparse_vector": [], "title": "PrivateHunt: Multi-Source Data-Driven Dispatching in For-Hire Vehicle Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, for-hire vehicle services (FHV, e.g., Uber and Lyft) have become essential to people's daily transportation. Similar to taxis, how to effectively dispatch these FHV based on demand and supply is important for both FHV passengers and drivers. Based on real-world multi-source data, we identify two new challenges for FHV dispatching: (i) diverse demand: FHV passengers are a mix of passengers previously using taxis, buses, subways, or private vehicles; (ii) uncertain supply: FHV drivers join and leave the FHV system with spatiotemporal dynamics. As a result, the state-of-the-art taxi dispatching techniques cannot be applied to FHV systems. In this paper, we design the first FHV dispatching system PrivateHunt based on extremely large-scale urban transportation data from New York City and Shenzhen in China. In particular, we present (i) a passenger demand model based on taxi, bus, subway, and private vehicle data; (ii) a driver supply model based on small-scale FHV data; (iii) a dispatching technique for FHV vehicles based on proposed demand/supply models to reduce idle driving time. We implement PrivateHunt based on 14 thousand taxis, 13 thousand buses, and 8-line subway system and 10 thousand private vehicles. The experimental results show that our data-driven dispatching strategy significantly outperforms the state-of-the-art dispatching strategies without data-driven FHV insights.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191777"}, {"primary_key": "3271010", "vector": [], "sparse_vector": [], "title": "FreeSense: A Robust Approach for Indoor Human Detection Using Wi-Fi Signals.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human detection aims to monitor how people are moving in an area of interest. There are many potential applications such as asset security monitoring, emergency management, and elderly care, etc. With the development of wireless sensing technique, Wi-Fi-based human detection method carries great potential due to advantages of pervasive accessibility and coverage flexibility. Previous studies have investigated the detection of human movements via signal variations. However, affected by noises, such as multi-path effect and device difference, existing approaches cannot achieve high accuracy and low false alarm rate at the same time. In this paper, we propose FreeSense, a novel Wi-Fi-based approach for human detection. Different from previous studies that characterize the variation of temporal wireless signals or calculate the deviation of Channel State Information (CSIs) from a normal profile, we will detect human movements by identifying whether there is any phase difference between the amplitude waveforms of multiple receiving antennas. In addition, we also model the sensing coverage for movements of different granularities in open space and propose a method to estimate the coverage range. Extensive experiments demonstrate that FreeSense can achieve an average false positive rate (FP) of 0.53% and an average false negative rate (FN) of 1.40%. The coverage range estimation method can achieve an average accuracy of 1.36 m, sufficient to guide the deployment of devices for human detection indoors.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264953"}, {"primary_key": "3271011", "vector": [], "sparse_vector": [], "title": "DeepType: On-Device Deep Learning for Input Personalization Service with Minimal Privacy Concern.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Mobile users spend an extensive amount of time on typing. A more efficient text input instrument brings a significant enhancement of user experience. Deep learning techniques have been recently applied to suggesting the next words of input, but to achieve more accurate predictions, these models should be customized for individual users. Personalization is often at the expense of privacy concerns. Existing solutions require users to upload the historical logs of their input text to the cloud so that a deep learning predictor can be trained. In this work, we propose a novel approach, called DeepType, to personalize text input with better privacy. The basic idea is intuitive: training deep learning predictors on the device instead of on the cloud, so that the model makes personalized and private data never leaves the device to externals. With DeepType, a global model is first trained on the cloud using massive public corpora, and our personalization is done by incrementally customizing the global model with data on individual devices. We further propose a set of techniques that effectively reduce the computation cost of training deep learning models on mobile devices at the cost of negligible accuracy loss. Experiments using real-world text input from millions of users demonstrate that DeepType significantly improves the input efficiency for individual users, and its incurred computation and energy costs are within the performance and battery restrictions of typical COTS mobile devices.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287075"}, {"primary_key": "3271012", "vector": [], "sparse_vector": [], "title": "Detecting Popular Temporal Modes in Population-scale Unlabelled Trajectory Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Hancheng Cao", "<PERSON>", "Funing Sun", "<PERSON><PERSON><PERSON>"], "summary": "With the rapid process of urbanization, revealing the underlying mechanisms behind urban mobility has become a crucial research problem. The movements of urban dwellers are often constituted by their daily routines, and exhibit distinct and contextual temporal modes, i.e., the patterns of individuals allocating their time across different locations. In this paper, we investigate a novel problem of detecting popular temporal modes in population-scale unlabelled trajectory data. Our key finding is that the detected temporal modes capture the semantic feature of human's living style, and is able to unravel meaningful correlations between urban mobility and human behavior. Specifically, we represent the temporal mode of a trajectory as a partition of the time duration, where the time slices associated with same locations are partitioned into same subsets. Such abstraction decouples the temporal modes from actual physical locations, and allows individuals with similar temporal modes yet completely different physical locations to have similar representations. Based on this insight, we propose a pipeline system composed of three components: 1) noise handler that eliminates the noises in the raw mobility records, 2) representation extractor for temporal modes, and 3) popular temporal modes detector. By applying our system on three real-world mobility datasets, we demonstrate that our system effectively detects the popular temporal modes embedded in population-scale mobility datasets, which is easy to be interpreted and can be justified through the associated PoIs and mobile applications usage. More importantly, our further experiments reveal insightful correlations between the popular temporal modes and individuals' social economic status, i.e. occupation information, which sheds light on the mechanisms behind urban mobility.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191778"}, {"primary_key": "3271013", "vector": [], "sparse_vector": [], "title": "Employing Opportunistic Charging for Electric Taxicabs to Reduce Idle Time.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "For electric taxicabs, the idle time spent on cruising for passengers, seeking chargers, and charging is wasteful. Previous works can only save cruising time through better routing, or charger seeking and charging time through proper charger deployment, but not for both. With the advancement of wireless charging techniques, efficient opportunistic charging of electric vehicles at their parked positions becomes possible. This enables a taxicab to get charged while waiting for the next passenger. In this paper, we present an opportunistic wireless charger deployment scheme in a city, which both maximizes the taxicabs' opportunity of picking up passengers at the chargers and supports the taxicabs' continuous operability on roads, while minimizing the total deployment cost. We studied a metropolitan-scale taxicab dataset on several factors important for deploying wireless chargers and determining the numbers of the chargers in the regions: the number of passengers, the functionalities of buildings, and the frequency of passenger appearance in a region, and taxicab traffic flows in a city. Then, we formulate a multi-objective optimization problem and find the solution. Our trace-driven experiments demonstrate the superior performance of our scheme over other representative methods in terms of reducing idle time and supporting the operability of the taxicabs.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191779"}, {"primary_key": "3271014", "vector": [], "sparse_vector": [], "title": "HeadGesture: Hands-Free Input Approach Leveraging Head Movements for HMD Devices.", "authors": ["Yukang Yan", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose HeadGesture, a hands-free input approach to interact with Head Mounted Display (HMD) devices. Using HeadGesture, users do not need to raise their arms to perform gestures or operate remote controllers in the air. Instead, they perform simple gestures with head movement to interact with the devices. In this way, users' hands are free to perform other tasks, e.g., taking notes or manipulating tools. This approach also reduces the hand occlusion of the field of view [11] and alleviates arm fatigue [7]. However, one main challenge for HeadGesture is to distinguish the defined gestures from unintentional movements. To generate intuitive gestures and address the issue of gesture recognition, we proceed through a process of Exploration - Design - Implementation - Evaluation. We first design the gesture set through experiments on gesture space exploration and gesture elicitation with users. Then, we implement algorithms to recognize the gestures, including gesture segmentation, data reformation and unification, feature extraction, and machine learning based classification. Finally, we evaluate user performance of HeadGesture in the target selection experiment and application tests. The results demonstrate that the performance of HeadGesture is comparable to mid-air hand gestures, measured by completion time. Additionally, users feel significantly less fatigue than when using hand gestures and can learn and remember the gestures easily. Based on these findings, we expect HeadGesture to be an efficient supplementary input approach for HMD devices.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287076"}, {"primary_key": "3271015", "vector": [], "sparse_vector": [], "title": "NALoc: Nonlinear Ambient-Light-Sensor-based Localization System.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Visible light position (VLP) is a revolutionary technique which enables many promising applications. As the human eye is sensitive to low-rate changes, VLP systems often convey location information through light flickering over 1 KHz, which induces a heavy burden on the VLP receiver. Existing solutions either rely on the high-resolution cameras or a dedicated photodiode to capture the location information, but the high power consumption and extraction deployment cost hinder their wide adoption. In this paper, we present a light-weight VLP system, NALoc, which leverages the ambient light sensor (ALS) readily on many mobile devices to sense high-frequency-modulation location information. To overcome the insufficient sampling ability of ALS, we exploit the nonlinearity of ALS to sense the leaked energy from high frequency(≥ 1 KHz) at a low sampling rate(100 Hz). Extensive evaluations demonstrate that our system can achieve a decimeter-level localization accuracy with about 1 mW power consumption, which is 2000 times less than existing camera-based VLP solutions.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287077"}, {"primary_key": "3271016", "vector": [], "sparse_vector": [], "title": "Lightweight Display-to-device Communication Using Electromagnetic Radiation and FM Radio.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents Shadow, a novel display-to-device communication system working in radio frequency. It leverages Electromagnetic Radiation (EMR) signals emanating from displays to transmit information. Specifically, <PERSON> modulates the high-frequency electric signals flowing in the display interface and makes the leaked EMR signals fall into the FM band. In this way, nearby mobile devices can receive information from the display through FM receivers. Compared with other display-to-device communication approaches, <PERSON> does not rely on cameras, and is thus more lightweight and requires fewer user actions. Furthermore, <PERSON>'s transmissions do not incur any degradation in the display quality, as they only take place in the Blanking Interval, which will not be shown on the display panel. <PERSON> requires no modification to existing hardware. The prototype is implemented with commodity display systems and mobile devices. Results show that it can achieve 1.5 kbps at distances of up to 20 cm from the display panel.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191781"}, {"primary_key": "3271017", "vector": [], "sparse_vector": [], "title": "SharedEdge: GPS-Free Fine-Grained Travel Time Estimation in State-Level Highway Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Estimating travel time on the highway in real time is of great importance for transportation services. Previous work has been mainly focusing on the city scale for a particular transportation system, e.g., taxi, bus, and metro. Little research has been conducted to estimate fine-grained real-time travel time in state-level highway systems. This is because the traditional solutions based on probe vehicles or loop sensors cannot scale to state-level highway systems due to their large spatial coverage. Recently, the adoption of Electric Toll Collection (ETC) systems (e.g. EZ-pass) brings a new opportunity to estimate the real-time travel time in the highway systems with little marginal cost. However, the key challenge is that ETC data only record the coarse-grained total travel time between a pair of toll stations rather than fine-grained travel time in each individual highway edge. To address this challenge, we design SharedEdge to estimate the fine-grained edge travel time with large-scale streaming ETC data. The key novelty is that we estimate real-time fine-grained travel time (i.e., edge travel time) without using fine-grained data (i.e. GPS trajectories or loop sensor data), by a few techniques based on Bayesian Graphical models and Expectation Maximization. More importantly, we implement our SharedEdge in the Guangdong Province, China with an ETC system covering 69 highways and 773 toll stations with a length of 7, 000 km. Based on this implementation, we evaluate SharedEdge in details by comparing it with some baselines and the state-of-the-art models. The evaluation results show that SharedEdge outperforms other methods in terms of travel time estimation accuracy when compared with the ground truth obtained by 114 thousand GPS-equipped vehicles.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191780"}, {"primary_key": "3271018", "vector": [], "sparse_vector": [], "title": "SenseGAN: Enabling Deep Learning for Internet of Things with a Semi-Supervised Framework.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Lu <PERSON>", "<PERSON><PERSON>"], "summary": "Recent proliferation of Internet of Things (IoT) devices with enhanced computing and sensing capabilities has revolutionized our everyday life. The massive data from these ubiquitous devices motivate the creation of intelligent IoT systems that can collectively learn. However, labelling data for learning purposes is extremely time-consuming, which greatly hinders deployment. In this paper, we describe a semi-supervised deep learning framework, called SenseGAN, that can leverage abundant unlabelled sensing data thereby minimizing the need for labelling effort. SenseGAN jointly trains three components with an adversarial game: (i) a classifier for predicting labels of input sensing data; (ii) a generator for generating sensing data samples based on the input labels; and (iii) a discriminator for differentiating the joint data/label distribution between real samples and partially generated samples from either the classifier or the generator. The classifier and the generator try to generate fake data/labels that can fool the discriminator. The adversarial game among the three components can mutually boost their performance, which helps the classifier learn to predict correct labels with unlabelled data in return. SenseGAN can effectively handle multimodal sensing inputs and easily stabilize the adversarial training process, which helps improve the performance of the classifier. Experiments on three IoT applications demonstrate the substantial improvements in accuracy and F1 score under SenseGAN, compared with supervised counterparts trained only on the labelled portion of the data, as well as other supervised and semi-supervised baselines. For these three applications, SenseGAN requires only 10% of the originally labelled data, to attain nearly the same accuracy as a deep learning classifier trained on the fully labelled dataset.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264954"}, {"primary_key": "3271019", "vector": [], "sparse_vector": [], "title": "Accurate and Efficient Indoor Location by Dynamic Warping in Sequence-Type Radio-map.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "An efficient way to overcome the calibration challenge and RSS dynamics in radio-map-based indoor localization is to collect radio signal strength (RSS) along indoor paths and conduct localization by sequence matching. But such sequence-based indoor localization suffers problems including indoor path combinational explosion, random RSS miss-of-detection during user movement, and user moving speed disparity in online and offline phases. To address these problems, this paper proposes an undirected graph model, called WarpMap to efficiently calibrate and store the sequence-type radio-map. It reduces RSS sequence signature storage complexity from O(2N) to O(N) where N is the number of path crosses. An efficient on-line candidate path extraction algorithm is developed in it to find a set of the most possible candidate paths for matching with the on-line collected RSS sequence. Then, to determine the user's exact location, a sub-sequence dynamic time warping (SDTW) algorithm is proposed, which matches the online collected RSS sequence with the sequential RSS signatures of the candidate paths. We show the SDTW algorithm is highly efficient and adaptive, which localizes user without backtracking of warping path. Extensive experiments in office environments verified the efficiency and accuracy of WarpMap, which can be calibrated within thirty minutes by one person for 1100m2 area and provides overall nearly 20% accuracy improvements than the state-of-the-art of radio-map method.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191782"}, {"primary_key": "3271021", "vector": [], "sparse_vector": [], "title": "MultiSoft: Soft Sensor Enabling Real-Time Multimodal Sensing with Contact Localization and Deformation Classification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce MultiSoft, a multilayer soft sensor capable of sensing real-time contact localization, classification of deformation types, and estimation of deformation magnitudes. We propose a multimodal sensing pipeline that carries out both inverse problem solving and machine learning tasks. Specifically, we employ an electrical impedance tomography (EIT) for contact localization and a support vector machine (SVM) for classifying deformations and regressing their magnitudes. We propose a deformation-aware system which enables maintaining a persistent single-point contact localization throughout the deformation. By updating a time-varying distribution of conductivity change caused by deformations, a single-point contact localization can be maintained and restored to support interaction using both contact localization and deformations.We devise a multilayer structure to fabricate a highly stretchable and flexible soft sensor with a short sensor settlement after excitations. Through a series of experiments and evaluations, we validate both raw sensor and multimodal sensing performance with the proposed method. We further demonstrate applicability and feasibility of MultiSoft with example applications.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264955"}, {"primary_key": "3271022", "vector": [], "sparse_vector": [], "title": "SoberMotion: Leveraging the Force of Probation Officers to Reduce the Risk of DUI Recidivism.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this study, we sought to assist probation officers in their efforts to reduce the risk that offenders on probation would re-commit the offense of driving under the influence (DUI) of alcohol. We prototyped a support system called SoberMotion, in which a breathalyzer is connected to the offender's smart phone via Bluetooth. Development of the system was based on pilot interviews with probation officers, psychiatrists, and offenders aimed at identifying the challenges and opportunities associated with this type of technology-based support mechanism. A corresponding phone app logs alcohol use behavior and identifies situations in which offenders should evaluate their sobriety before operating a vehicle. We conducted a two-month field study involving eight DUI offenders on probation, with the aim of evaluating the feasibility of the SoberMotion system. Our results indicate that most of the participants who drank alcohol while following the program performed multiple alcohol screening tests before operating a vehicle in order to avoid re-committing DUI. Responses collected via qualitative interviews indicate that SoberMotion is an effective approach to extending the power of probation officers seeking to reduce the risk of DUI recidivism.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264956"}, {"primary_key": "3271024", "vector": [], "sparse_vector": [], "title": "QGesture: Quantifying Gesture Distance and Direction with WiFi Signals.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Lingtao Kong"], "summary": "Many HCI applications, such as volume adjustment in a gaming system, require quantitative gesture measurement for metrics such as movement distance and direction. In this paper, we propose QGesture, a gesture recognition system that uses CSI values provided by COTS WiFi devices to measure the movement distance and direction of human hands. To achieve high accuracy in measurements, we first use phase correction algorithm to remove the phase noise in CSI measurements. We then propose a robust estimation algorithm, called LEVD, to estimate and remove the impact of environmental dynamics. To separate gesture movements from daily activities, we design simple gestures with unique characteristics as preambles to determine the start of the gesture. Our experimental results show that QGesture achieves an average accuracy of 3 cm in the measurement of movement distance and more than 95% accuracy in the movement direction detection in the one-dimensional case. Furthermore, it achieves an average absolute direction error of 15 degrees and an average accuracy of 3.7 cm in the measurement of movement distance in the two-dimensional case.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191783"}, {"primary_key": "3271025", "vector": [], "sparse_vector": [], "title": "Extracting Multi-Person Respiration from Entangled RF Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent advances in wireless systems have demonstrated the possibility of tracking a person's respiration using the RF signals that bounce off her body. The resulting breathing signal can be used to infer the person's sleep quality and stages; it also allows for monitoring sleep apnea and other sleep disordered breathing (SDB); all without any body contact. Unfortunately however past work fails when people are close to each other, e.g., a couple sharing the same bed. In this case, the breathing signals of nearby individuals interfere with each other and super-impose in the received signal. This paper presents DeepBreath, the first RF-based respiration monitoring system that can recover the breathing signals of multiple individuals even when they are separated by zero distance. To design DeepBreath, we model interference due to multiple reflected RF signals and demonstrate that the original breathing can be recovered via independent component analysis (ICA). We design a full system that eliminates interference and recovers the original breathing signals. We empirically evaluate DeepBreath using 21 nights of sleep and over 150 hours of data from 13 couples who share the bed. Our results show that DeepBreath is very accurate. Specifically, the differences between the breathing signals it recovers and the ground truth are on par with the difference between the same breathing signal measured at the person's chest and belly.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214289"}, {"primary_key": "3271026", "vector": [], "sparse_vector": [], "title": "FullBreathe: Full Human Respiration Detection Exploiting Complementarity of CSI Phase and Amplitude of WiFi Signals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON> Zhang"], "summary": "Human respiration detection based on Wi-Fi signals does not require users to carry any device, hence it has drawn a lot of attention due to better user acceptance and great potential for real-world deployment. However, recent studies show that respiration sensing performance varies in different locations due to the nature of Wi-Fi radio wave propagation in indoor environments, i.e., respiration detection may experience poor performance at certain locations which we call \"blind spots\". In this paper, we aim to address the blind spot problem to ensure full coverage of respiration detection. Basically, the amplitude and phase of Wi-Fi channel state information (CSI) are orthogonal and complementary to each other, so they can be combined to eliminate the blind spots. However, accurate CSI phase cannot be obtained from commodity Wi-Fi due to the clock-unsynchronized transceivers. Thus, we apply conjugate multiplication (CM) of CSI between two antennas to remove the phase offset and construct two orthogonal signals--new \"amplitude and phase\" which are still complementary to each other. In this way, we can ensure full human respiration detection. Based on these ideas, We design and implement a real-time respiration detection system with commodity Wi-Fi devices. We conduct extensive experiments to validate our model and design. The results show that, with only one transceiver pair and without leveraging multiple sub-carriers, our system enables full location coverage with no blind spot, showing great potential for real deployment.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264958"}, {"primary_key": "3271027", "vector": [], "sparse_vector": [], "title": "Capturing the Shifting Shapes: Enabling Efficient Screen-Camera Communication with a Pattern-based Dynamic Barcode.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the increasing availability of LCD displays and phone cameras in today's environment, screen-camera communication using dynamic barcode has emerged as a convenient infrastructure-free form to establish impromptu communication channel among mobile devices. Due to the short wavelengths and narrow beams of visible light, screen-camera communication is highly directional, low-interference and secure, which envisions a wide range of application scenarios. Conventional screen-camera communication systems encode data bits with color in dynamic barcodes, which suffers from the frame mixture problem caused by the rolling shutter effect of CMOS camera in high capturing rate. In this paper, we propose a novel design of dynamic barcode called ShiftCode that encodes data bits with shifting shape patterns, which provide a new way to expand the barcode capacity for screen-camera communications. ShiftCode adopts a pattern-based layout design to embed multiple data bits in a symbol representation. With such layout, it exploits a decoding mechanism to solve the frame mixture problem and achieves high frame capturing rate. It further intruduces a two-level reliability technique for intra-frame error correction and inter-frame redundancy, which reduces the overhead and delay of retransmission. The proposed ShiftCode is implemented on the Android platform, and extensive experiments show that it achieves at least two-fold improvement on goodput compared with the conventional screen-camera communication systems.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191784"}, {"primary_key": "3271029", "vector": [], "sparse_vector": [], "title": "FinDroidHR: Smartwatch Gesture Input with Optical Heartrate Monitor.", "authors": ["<PERSON>", "<PERSON> Gu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present FinDroidHR, a novel gesture input technique for off-the-shelf smartwatches. Our technique is designed to detect 10 hand gestures on the hand wearing a smartwatch. The technique is enabled by analysing features of the Photoplethysmography (PPG) signal that optical heart-rate sensors capture. In a study with 20 participants, we show that FinDroidHR achieves 90.55% accuracy and 90.73% recall. Our work is the first study to explore the feasibility of using optical sensors on the off-the-shelf wearable devices to recognise gestures. Without requiring bespoke hardware, FinDroidHR can be readily used on existing smartwatches.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191788"}, {"primary_key": "3271030", "vector": [], "sparse_vector": [], "title": "Touch Sense: Touch Screen Based Mental Stress Sense.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Non-intrusive and sensitive measurement of users' stress is very crucial for computers to dynamically understand and respond to users' mental status while users are naturally interacting with them, such as context-aware reminding, smart assistant, health monitoring etc. Compared to others, physiological measures are known as more sensitive and reliable ones. However, most of the current physiological methods need explicit and obtrusive sensors that deter natural human-computer interactions. In this study, we propose a photoplethysmogram-based mental stress measuring method through infrared touchscreen, which, from the best of our knowledge, is the first to integrate with the touch modality to enable natural, sensitive and reliable measurement. We designed and conducted two user experiments with touch-and-hold mode and tap mode respectively. By using person-independent classifiers to handle the photoplethysmographic parameters, the results reached a 97% and 87% average stress recognition accuracy for static test and interaction test respectively.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214290"}, {"primary_key": "3271032", "vector": [], "sparse_vector": [], "title": "Watching the TV Watchers.", "authors": ["Yun <PERSON><PERSON>", "<PERSON>"], "summary": "Studies have linked excessive TV watching to obesity in adults and children. In addition, TV content represents an important source of visual exposure to cues which can effect a broad set of health-related behaviors. This paper presents a ubiquitous sensing system which can detect moments of screen-watching during daily life activities. We utilize machine learning techniques to analyze video captured by a head-mounted wearable camera. Although wearable cameras do not directly provide a measure of visual attention, we show that attention to screens can be reliably inferred by detecting and tracking the location of screens within the camera's field-of-view. We utilize a computational model of the head movements associated with TV watching to identify TV watching events. We have evaluated our method on 13 hours of TV watching videos recorded from 16 participants in a home environment. Our model achieves a precision of 0.917 and a recall of 0.945 in identifying attention to screens. We validated the third-person annotations used to determine accuracy and further evaluated our system in a multi-device environment using gold standard attention measurements obtained from a wearable eye-tracker. Finally, we tested our system in a natural environment. Our system achieves a precision of 0.87 and a recall of 0.82 on challenging videos capturing the daily life activities of participants.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3214291"}, {"primary_key": "3271033", "vector": [], "sparse_vector": [], "title": "MindID: Person Identification from Brain Waves through Attention-based Recurrent Neural Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Salil S<PERSON>", "<PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON><PERSON>"], "summary": "Person identification technology recognizes individuals by exploiting their unique, measurable physiological and behavioral characteristics. However, the state-of-the-art person identification systems have been shown to be vulnerable, e.g., contact lenses can trick iris recognition and fingerprint films can deceive fingerprint sensors. EEG (Electroencephalography)-based identification, which utilizes the users' brainwave signals for identification and offers a more resilient solution, draw a lot of attention recently. However, the accuracy still requires improvement and very little work is focusing on the robustness and adaptability of the identification system. We propose MindID, an EEG-based biometric identification approach, achieves higher accuracy and better characteristics. At first, the EEG data patterns are analyzed and the results show that the Delta pattern contains the most distinctive information for user identification. Then the decomposed Delta pattern is fed into an attention-based Encoder-Decoder RNNs (Recurrent Neural Networks) structure which assigns various attention weights to different EEG channels based on the importance of channels. The discriminative representations learned from the attention-based RNN are used to recognize the user identification through a boosting classifier. The proposed approach is evaluated over 3 datasets (two local and one public). One local dataset (EID-M) is used for performance assessment and the result illustrates that our model achieves an accuracy of 0.982 which outperforms the baselines and the state-of-the-art. Another local dataset (EID-S) and a public dataset (EEG-S) are utilized to demonstrate the robustness and adaptability, respectively. The results indicate that the proposed approach has the potential to be largely deployed in the practice environment.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264959"}, {"primary_key": "3271034", "vector": [], "sparse_vector": [], "title": "Tap-to-Pair: Associating Wireless Devices with Synchronous Tapping.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ad-hoc wireless device pairing enables impromptu interactions in smart spaces, such as resource sharing and remote control. The pairing experience is mainly determined by the device association process, during which users express their pairing intentions between the advertising device and the scanning device. Currently, most wireless devices are associated by selecting the advertiser's name from a list displayed on the scanner's screen, which becomes less efficient and often misplaced as the number of wireless devices increases. In this paper, we propose Tap-to-Pair, a spontaneous device association mechanism that initiates pairing from advertising devices without hardware or firmware modifications. Tapping an area near the advertising device's antenna can change its signal strength. Users can then associate two devices by synchronizing taps on the advertising device with the blinking pattern displayed by the scanning device. By leveraging the wireless transceiver for sensing, Tap-to-Pair does not require additional resources from advertising devices and needs only a binary display (e.g. LED) on scanning devices. We conducted a user study to test users' synchronous tapping ability and demonstrated that Tap-to-Pair can reliably detect users' taps. We ran simulations to optimize parameters for the synchronization recognition algorithm and provide pattern design guidelines. We used a second user study to evaluate the on-chip performance of Tap-to-Pair. The results show that Tap-to-Pair can achieve an overall successful pairing rate of 93.7% with three scanning devices at different distances.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3287079"}, {"primary_key": "3271035", "vector": [], "sparse_vector": [], "title": "From Fresnel Diffraction Model to Fine-grained Human Respiration Sensing with Commodity Wi-Fi Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Non-intrusive respiration sensing without any device attached to the target plays a particular important role in our everyday lives. However, existing solutions either require dedicated hardware or employ special-purpose signals which are not cost-effective, significantly limiting their real-life applications. Also very few work concerns about the theory behind and can explain the large performance variations in different scenarios. In this paper, we employ the cheap commodity Wi-Fi hardware already ubiquitously deployed around us for respiration sensing. For the first time, we utilize the Fresnel diffraction model to accurately quantify the relationship between the diffraction gain and human target's subtle chest displacement and thus successfully turn the previously considered \"destructive\" obstruction diffraction in the First Fresnel Zone (FFZ) into beneficial sensing capability. By not just considering the chest displacement at the frontside as the existing solutions, but also the subtle displacement at the backside, we achieve surprisingly matching results with respect to the theoretical plots and become the first to clearly explain the theory behind the performance distinction between lying and sitting for respiration sensing. With two cheap commodity Wi-Fi cards each equipped with just one antenna, we are able to achieve higher than 98% accuracy of respiration rate monitoring at more than 60% of the locations in the FFZ. Furthermore, we are able to present the detail heatmap of the sensing capability at each location inside the FFZ to guide the respiration sensing so users clearly know where are the good positions for respiration monitoring and if located at a bad position, how to move just slightly to reach a good position.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191785"}, {"primary_key": "3271036", "vector": [], "sparse_vector": [], "title": "Detecting Urban Anomalies Using Multiple Spatio-Temporal Data Sources.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Urban anomalies, such as abnormal movements of crowds and accidents, may result in loss of life or property if not handled properly. It would be of great value for governments if anomalies can be automatically alerted in their early stage. However, detecting anomalies in urban area has two main challenges. First, the criteria to determine an anomaly on different occasions (e.g. rainy days vs. sunny days, or holidays vs. workdays) and in different places (e.g. tourist attractions vs. office areas) are distinctly different, as these occasions and places have their own definitions on normal patterns. Second, urban anomalies often exhibit complex forms (e.g. road closure may cause decrease in taxi flow and increase in bike flow). We need an algorithm that not only models the anomaly degree of individual data source but also the combination of changes in multiple data sources. In this paper, we propose a two-step method to tackle those challenges. In the first step, we use a similarity-based algorithm to estimate an anomaly score for each individual data source in each region and time slot based on the values of historically similar regions. Those scores are fed into the second step, where we propose an algorithm based on one-class Support Vector Machine to capture rare patterns occurred in multiple data sources, nearby regions or time slots, and give a final, integrated anomaly score for each region. Evaluations based on both synthetic and real world datasets show the advantages of our method beyond baseline techniques such as distance-based, probability-based methods.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3191786"}, {"primary_key": "3271039", "vector": [], "sparse_vector": [], "title": "BiLock: User Authentication via Dental Occlusion Biometrics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "User authentication on smart devices is indispensable to keep data privacy and security. It is especially significant for emerging wearable devices such as smartwatches considering data sensitivity in them. However, conventional authentication methods are not applicable for wearables due to constraints of size and hardware, which makes present wearable devices lack convenient, secure and low-cost authentication schemes. To tackle this problem, we reveal a novel biometric authentication mechanism which makes use of sounds of human dental occlusion (i.e., tooth click). We demonstrate its feasibility by comprehensive measurement study, and design a prototype-BiLock with two Android platforms. Extensive real-world experiments have been conducted to evaluate the accuracy, robustness and security of BiLock in different environments. The results show that BiLock can achieve less than 5% average false reject rate and 0.95% average false accept rate even in a noisy environment. Comparative experiments also demonstrate that BiLock possesses advantages in robustness to noise and security against replay and observation attacks over existing voiceprinting schemes.", "published": "2018-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3264962"}]