[{"primary_key": "4192805", "vector": [], "sparse_vector": [], "title": "Separations in query complexity using cheat sheets.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show a power 2.5 separation between bounded-error randomized and quantum query complexity for a total Boolean function, refuting the widely believed conjecture that the best such separation could only be quadratic (from <PERSON><PERSON>'s algorithm). We also present a total function with a power 4 separation between quantum query complexity and approximate polynomial degree, showing severe limitations on the power of the polynomial method. Finally, we exhibit a total function with a quadratic gap between quantum query complexity and certificate complexity, which is optimal (up to log factors). These separations are shown using a new, general technique that we call the cheat sheet technique. The technique is based on a generic transformation that converts any (possibly partial) function into a new total function with desirable properties for showing separations. The framework also allows many known separations, including some recent breakthrough results of <PERSON><PERSON> et al., to be shown in a unified manner.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897644"}, {"primary_key": "4192806", "vector": [], "sparse_vector": [], "title": "The 4/3 additive spanner exponent is tight.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A spanner is a sparse subgraph that approximately preserves the pairwise distances of the original graph. It is well known that there is a smooth tradeoff between the sparsity of a spanner and the quality of its approximation, so long as distance error is measured multiplicatively. A central open question in the field is to prove or disprove whether such a tradeoff exists also in the regime of additive error. That is, is it true that for all ε>0, there is a constant kε such that every graph has a spanner on O(n1+ε) edges that preserves its pairwise distances up to +kε? Previous lower bounds are consistent with a positive resolution to this question, while previous upper bounds exhibit the beginning of a tradeoff curve: all graphs have +2 spanners on O(n3/2) edges, +4 spanners on Õ(n7/5) edges, and +6 spanners on O(n4/3) edges. However, progress has mysteriously halted at the n4/3 bound, and despite significant effort from the community, the question has remained open for all 0 < ε < 1/3.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897555"}, {"primary_key": "4192807", "vector": [], "sparse_vector": [], "title": "Simulating branching programs with edit distance and friends: or: a polylog shaved is a lower bound made.", "authors": ["<PERSON>", "<PERSON>", "Virginia Vassilevska Williams", "<PERSON>"], "summary": "A recent, active line of work achieves tight lower bounds for fundamental problems under the Strong Exponential Time Hypothesis (SETH). A celebrated result of <PERSON><PERSON> and <PERSON> (STOC'15) proves that computing the Edit Distance of two sequences of length n in truly subquadratic O(n2−ε) time, for some ε>0, is impossible under SETH. The result was extended by follow-up works to simpler looking problems like finding the Longest Common Subsequence (LCS).", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897653"}, {"primary_key": "4192808", "vector": [], "sparse_vector": [], "title": "Separations in query complexity based on pointer functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In 1986, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> conjectured that the largest separation between deterministic and zero-error randomized query complexity for a total boolean function is given by the function f on n=2k bits defined by a complete binary tree of NAND gates of depth k, which achieves R0(f) = O(D(f)0.7537…). We show this is false by giving an example of a total boolean function f on n bits whose deterministic query complexity is Ω(n/log(n)) while its zero-error randomized query complexity is Õ(√n). We further show that the quantum query complexity of the same function is Õ(n1/4), giving the first example of a total function with a super-quadratic gap between its quantum and deterministic query complexities.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897524"}, {"primary_key": "4192809", "vector": [], "sparse_vector": [], "title": "Algebraic attacks against random local functions and their countermeasures.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Suppose that you have n truly random bits x=(x1,…,xn) and you wish to use them to generate m≫ n pseudorandom bits y=(y1,…, ym) using a local mapping, i.e., each yi should depend on at most d=O(1) bits of x. In the polynomial regime of m=ns, s>1, the only known solution, originates from (<PERSON><PERSON><PERSON>, ECCC 2000), is based on Random Local Functions: Compute yi by applying some fixed (public) d-ary predicate P to a random (public) tuple of distinct inputs (xi1,…,xid). Our goal in this paper is to understand, for any value of s, how the pseudorandomness of the resulting sequence depends on the choice of the underlying predicate. We derive the following results:", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897554"}, {"primary_key": "4192810", "vector": [], "sparse_vector": [], "title": "Almost tight bounds for eliminating depth cycles in three dimensions.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Given n non-vertical lines in 3-space, their vertical depth (above/below) relation can contain cycles. We show that the lines can be cut into O(n3/2polylog n) pieces, such that the depth relation among these pieces is now a proper partial order. This bound is nearly tight in the worst case. As a consequence, we deduce that the number of pairwise non-overlapping cycles, namely, cycles whose xy-projections do not overlap, is O(n3/2polylog n); this bound too is almost tight in the worst case.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897539"}, {"primary_key": "4192811", "vector": [], "sparse_vector": [], "title": "Searchable symmetric encryption: optimal locality in linear space via two-dimensional balanced allocations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Searchable symmetric encryption (SSE) enables a client to store a database on an untrusted server while supporting keyword search in a secure manner. Despite the rapidly increasing interest in SSE technology, experiments indicate that the performance of the known schemes scales badly to large databases. Somewhat surprisingly, this is not due to their usage of cryptographic tools, but rather due to their poor locality (where locality is defined as the number of non-contiguous memory locations the server accesses with each query). The only known schemes that do not suffer from poor locality suffer either from an impractical space overhead or from an impractical read efficiency (where read efficiency is defined as the ratio between the number of bits the server reads with each query and the actual size of the answer).", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897562"}, {"primary_key": "4192812", "vector": [], "sparse_vector": [], "title": "Tight bounds for single-pass streaming complexity of the set cover problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We resolve the space complexity of single-pass streaming algorithms for approximating the classic set cover problem. For finding an α-approximate set cover (for α= o(√n)) via a single-pass streaming algorithm, we show that Θ(mn/α) space is both sufficient and necessary (up to an O(logn) factor); here m denotes number of the sets and n denotes size of the universe. This provides a strong negative answer to the open question posed by <PERSON><PERSON> (2015) regarding the possibility of having a single-pass algorithm with a small approximation factor that uses sub-linear space. We further study the problem of estimating the size of a minimum set cover (as opposed to finding the actual sets), and establish that an additional factor of α saving in the space is achievable in this case and that this is the best possible. In other words, we show that Θ(mn/α2) space is both sufficient and necessary (up to logarithmic factors) for estimating the size of a minimum set cover to within a factor of α. Our algorithm in fact works for the more general problem of estimating the optimal value of a covering integer program. On the other hand, our lower bound holds even for set cover instances where the sets are presented in a random order.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897576"}, {"primary_key": "4192813", "vector": [], "sparse_vector": [], "title": "A discrete and bounded envy-free cake cutting protocol for four agents.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the well-studied cake cutting problem in which the goal is to identify an envy-free allocation based on a minimal number of queries from the agents. The problem has attracted considerable attention within various branches of computer science, mathematics, and economics. Although, the elegant Selfridge-Conway envy-free protocol for three agents has been known since 1960, it has been a major open problem to obtain a bounded envy-free protocol for more than three agents. The problem has been termed the central open problem in cake cutting. We solve this problem by proposing a discrete and bounded envy-free protocol for four agents.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897522"}, {"primary_key": "4192814", "vector": [], "sparse_vector": [], "title": "Graph isomorphism in quasipolynomial time [extended abstract].", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We show that the Graph Isomorphism (GI) problem and the more general problems of String Isomorphism (SI) andCoset Intersection (CI) can be solved in quasipolynomial(exp((logn)O(1))) time. The best previous bound for GI was exp(O( √n log n)), where n is the number of vertices (<PERSON><PERSON>, 1983); for the other two problems, the bound was similar, exp(O~(√ n)), where n is the size of the permutation domain (<PERSON><PERSON>, 1983). Following the approach of <PERSON><PERSON>'s seminal 1980/82 paper, the problem we actually address is SI. This problem takes two strings of length n and a permutation group G of degree n (the \"ambient group\") as input (G is given by a list of generators) and asks whether or not one of the strings can be transformed into the other by some element of <PERSON><PERSON>'s divide-and-conquer algorithm for SI proceeds by recursion on the ambient group. We build on <PERSON><PERSON>'s framework and attack the obstructions to efficient <PERSON><PERSON> recurrence via an interplay between local and global symmetry. We construct group theoretic \"local certificates\" to certify the presence or absence of local symmetry, aggregate the negative certificates to canonical k-ary relations where k = O(log n), and employ combinatorial canonical partitioning techniques to split the k-ary relational structure for efficient divide-and- conquer. We show that in a well–defined sense, Johnson graphs are the only obstructions to effective canonical partitioning. The central element of the algorithm is the \"local certificates\" routine which is based on a new group theoretic result, the \"Unaffected stabilizers lemma,\" that allows us to construct global automorphisms out of local information.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897542"}, {"primary_key": "4192815", "vector": [], "sparse_vector": [], "title": "Lift-and-round to improve weighted completion time on unrelated machines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of scheduling jobs on unrelated machines so as to minimize the sum of weighted completion times. Our main result is a (3/2-c)-approximation algorithm for some fixed c>0, improving upon the long-standing bound of 3/2. To do this, we first introduce a new lift-and-project based SDP relaxation for the problem. This is necessary as the previous convex programming relaxations have an integrality gap of 3/2. Second, we give a new general bipartite-rounding procedure that produces an assignment with certain strong negative correlation properties.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897572"}, {"primary_key": "4192816", "vector": [], "sparse_vector": [], "title": "Algorithmic stability for adaptive data analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Adaptivity is an important feature of data analysis - the choice of questions to ask about a dataset often depends on previous interactions with the same dataset. However, statistical validity is typically studied in a nonadaptive model, where all questions are specified before the dataset is drawn. Recent work by <PERSON><PERSON> et al. (STOC, 2015) and <PERSON><PERSON> and <PERSON> (FOCS, 2014) initiated a general formal study of this problem, and gave the first upper and lower bounds on the achievable generalization error for adaptive data analysis.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897566"}, {"primary_key": "4192817", "vector": [], "sparse_vector": [], "title": "Fault tolerant subgraph for single source reachability: generic and optimal.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Let G=(V,E) be an n-vertices m-edges directed graph. Let s∈ V be any designated source vertex. We address the problem of single source reachability (SSR) from s in presence of failures of vertices/edges. We show that for every k≥ 1, there is a subgraph H of G with at most 2k n edges that preserves the reachability from s even after the failure of any k edges. Formally, given a set F of k edges, a vertex u∈ V is reachable from s in G∖ F if and only if u is reachable from s in H∖ F. We call H a k-Fault Tolerant Reachability Subgraph (k-FTRS). We prove also a matching lower bound of Ω(2kn) for such subgraphs. Our results extend to vertex failures without any extra overhead. The general construction of k-FTRS is interesting from several different perspectives. From the Graph theory perspective it reveals a separation between SSR and single source shortest paths (SSSP) in directed graphs. More specifically, in the case of SSSP in weighted directed graphs, there is a lower bound of Ω(m) even for a single edge failure. In the case of unweighted graphs there is a lower bound of Ω(n3/2) edges, again, even for a single edge failure. There is also a matching upper bound but nothing is known for two or more failures in the directed graphs. From the Algorithms perspective it implies fault tolerant solutions to other interesting problems, namely, (i) verifying if the strong connectivity of a graph is preserved after k edge or vertex failures, (ii) computing a dominator tree of a graph after k-failures. From the perspective of Techniques it makes an interesting usage of the concept of farthest min-cut which was already introduced by Ford and Fulkerson in their pioneering work on flows and cuts. We show that there is a close relationship between the farthest min-cut and the k-FTRS. We believe that our new technique is of independent interest.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897648"}, {"primary_key": "4192818", "vector": [], "sparse_vector": [], "title": "A PTAS for planar group Steiner tree via spanner bootstrapping and prize collecting.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the first polynomial-time approximation scheme (PTAS), i.e., (1+ε)-approximation algorithm for any constant ε> 0, for the planar group Steiner tree problem (in which each group lies on a boundary of a face). This result improves on the best previous approximation factor of O(logn (loglogn)O(1)). We achieve this result via a novel and powerful technique called spanner bootstrapping, which allows one to bootstrap from a superconstant approximation factor (even superpolynomial in the input size) all the way down to a PTAS. This is in contrast with the popular existing approach for planar PTASs of constructing light-weight spanners in one iteration, which notably requires a constant-factor approximate solution to start from. Spanner bootstrapping removes one of the main barriers for designing PTASs for problems which have no known constant-factor approximation (even on planar graphs), and thus can be used to obtain PTASs for several difficult-to-approximate problems.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897549"}, {"primary_key": "4192819", "vector": [], "sparse_vector": [], "title": "A polynomial lower bound for testing monotonicity.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We show that every algorithm for testing n-variate Boolean functions for monotonicityhas query complexity Ω(n1/4). All previous lower bounds for this problem were designed for non-adaptive algorithms and, as a result, the best previous lower bound for general (possibly adaptive) monotonicity testers was only Ω(logn). Combined with the query complexity of the non-adaptive monotonicity tester of <PERSON><PERSON>, <PERSON>, and <PERSON> (FOCS 2015), our lower bound shows that adaptivity can result in at most a quadratic reduction in the query complexity for testing monotonicity.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897567"}, {"primary_key": "4192820", "vector": [], "sparse_vector": [], "title": "Contention resolution with log-logstar channel accesses.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "For decades, randomized exponential backoff has provided a critical algorithmic building block in situations where multiple devices seek access to a shared resource. Surprisingly, despite this history, the performance of standard backoff is poor under worst-case scheduling of demands on the resource: (i) subconstant throughput can occur under plausible scenarios, and (ii) each of N devices requires Omega(log N) access attempts before obtaining the resource.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897655"}, {"primary_key": "4192821", "vector": [], "sparse_vector": [], "title": "Deterministic decremental single source shortest paths: beyond the o(mn) bound.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we consider the decremental single-source shortest paths (SSSP) problem, where given a graph G and a source node s the goal is to maintain shortest paths between s and all other nodes in G under a sequence of online adversarial edge deletions.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897521"}, {"primary_key": "4192822", "vector": [], "sparse_vector": [], "title": "New deterministic approximation algorithms for fully dynamic matching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Danupon <PERSON>"], "summary": "We present two deterministic dynamic algorithms for the maximum matching problem. (1) An algorithm that maintains a (2+є)-approximate maximum matching in general graphs with O(poly(logn, 1/є)) update time. (2) An algorithm that maintains an αK approximation of the value of the maximum matching with O(n2/K) update time in bipartite graphs, for every sufficiently large constant positive integer K. Here, 1≤ αK < 2 is a constant determined by the value of K. <PERSON><PERSON><PERSON> (1) is the first deterministic algorithm that can maintain an o(logn)-approximate maximum matching with polylogarithmic update time, improving the seminal result of <PERSON><PERSON> et al. [STOC 2010]. Its approximation guarantee almost matches the guarantee of the best randomized polylogarithmic update time algorithm [<PERSON><PERSON><PERSON> et al. FOCS 2011]. Result (2) achieves a better-than-two approximation with arbitrarily small polynomial update time on bipartite graphs. Previously the best update time for this problem was O(m1/4) [<PERSON> et al. ICALP 2015], where m is the current number of edges in the graph.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897568"}, {"primary_key": "4192823", "vector": [], "sparse_vector": [], "title": "Optimal principal component analysis in distributed and streaming models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper studies the Principal Component Analysis (PCA) problem in the distributed and streaming models of computation. Given a matrix A ∈ Rm×n, a rank parameter k<rank(A), and an accuracy parameter 0<ε<1, we want to output an m×k orthonormal matrix U for which ||A-UUTA||2F≤(1+ε)||A-Ak||2F where Ak∈Rm×n is the best rank-k approximation to A.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897646"}, {"primary_key": "4192824", "vector": [], "sparse_vector": [], "title": "A lower bound for the distributed Lovász local lemma.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that any randomised Monte Carlo distributed algorithm for the Lovász local lemma requires Omega(log log n) communication rounds, assuming that it finds a correct assignment with high probability. Our result holds even in the special case of d = O(1), where d is the maximum degree of the dependency graph. By prior work, there are distributed algorithms for the Lovász local lemma with a running time of O(log n) rounds in bounded-degree graphs, and the best lower bound before our work was Omega(log* n) rounds [<PERSON> et al. 2014].", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897570"}, {"primary_key": "4192825", "vector": [], "sparse_vector": [], "title": "Beating <PERSON><PERSON><PERSON><PERSON> for heavy hitters in insertion streams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Given a stream p1, …, pm of items from a universe U, which, without loss of generality we identify with the set of integers {1, 2, …, n}, we consider the problem of returning all ℓ2-heavy hitters, i.e., those items j for which fj ≥ є √F2, where fj is the number of occurrences of item j in the stream, and F2 = ∑i ∈ [n] fi2. Such a guarantee is considerably stronger than the ℓ1-guarantee, which finds those j for which fj ≥ є m. In 2002, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> suggested the CountSketch data structure, which finds all such j using Θ(log2 n) bits of space (for constant є > 0). The only known lower bound is Ω(logn) bits of space, which comes from the need to specify the identities of the items found.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897558"}, {"primary_key": "4192826", "vector": [], "sparse_vector": [], "title": "Constant-rate coding for multiparty interactive communication is impossible.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study coding schemes for multiparty interactive communication over synchronous networks that suffer from stochastic noise, where each bit is independently flipped with probability ε. We analyze the minimal overhead that must be added by the coding scheme in order to succeed in performing the computation despite the noise. Our main result is a lower bound on the communication of any noise-resilient protocol over a synchronous star network with n-parties (where all parties communicate in every round). Specifically, we show a task that can be solved by communicating T bits over the noise-free network, but for which any protocol with success probability of 1-o(1) must communicate at least Ω(T log n / log log n) bits when the channels are noisy. By a 1994 result of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, the slowdown we prove is the highest one can obtain on any topology, up to a log log n factor. We complete our lower bound with a matching coding scheme that achieves the same overhead; thus, the capacity of (synchronous) star networks is Θ(log log n / log n). Our bounds prove that, despite several previous coding schemes with rate Ω(1) for certain topologies, no coding scheme with constant rate Ω(1) exists for arbitrary n-party noisy networks.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897563"}, {"primary_key": "4192827", "vector": [], "sparse_vector": [], "title": "Communication lower bounds for statistical estimation problems via a distributed data processing inequality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the tradeoff between the statistical error and communication cost of distributed statistical estimation problems in high dimensions. In the distributed sparse Gaussian mean estimation problem, each of the m machines receives n data points from a d-dimensional Gaussian distribution with unknown mean θ which is promised to be k-sparse. The machines communicate by message passing and aim to estimate the mean θ. We provide a tight (up to logarithmic factors) tradeoff between the estimation error and the number of bits communicated between the machines. This directly leads to a lower bound for the distributed sparse linear regression problem: to achieve the statistical minimax error, the total communication is at least Ω(min{n,d}m), where n is the number of observations that each machine receives and d is the ambient dimension. These lower results improve upon <PERSON><PERSON><PERSON> (NIPS'14) and <PERSON><PERSON> (COLT'15) by allowing multi-round iterative communication model. We also give the first optimal simultaneous protocol in the dense case for mean estimation. As our main technique, we prove a distributed data processing inequality, as a generalization of usual data processing inequalities, which might be of independent interest and useful for other problems.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897582"}, {"primary_key": "4192828", "vector": [], "sparse_vector": [], "title": "Parallel algorithms for select and partition with noisy comparisons.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of finding the kth highest element in a totally ordered set of n elements (Select), and partitioning a totally ordered set into the top k and bottom n − k elements (Partition) using pairwise comparisons. Motivated by settings like peer grading or crowdsourcing, where multiple rounds of interaction are costly and queried comparisons may be inconsistent with the ground truth, we evaluate algorithms based both on their total runtime and the number of interactive rounds in three comparison models: noiseless (where the comparisons are correct), erasure (where comparisons are erased with probability 1 − γ), and noisy (where comparisons are correct with probability 1/2 + γ/2 and incorrect otherwise). We provide numerous matching upper and lower bounds in all three models. Even our results in the noiseless model, which is quite well-studied in the TCS literature on parallel algorithms, are novel.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897642"}, {"primary_key": "4192829", "vector": [], "sparse_vector": [], "title": "A duality based unified approach to Bayesian mechanism design.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We provide a unified view of many recent developments in Bayesian mechanism design, including the black-box reductions of Cai et. al., simple auctions for additive buyers, and posted-price mechanisms for unit-demand buyers. Additionally, we show that viewing these three previously disjoint lines of work through the same lens leads to new developments as well. First, we provide a duality framework for Bayesian mechanism design, which naturally accommodates multiple agents and arbitrary objectives/feasibility constraints. Using this, we prove that either a posted-price mechanism or the VCG auction with per-bidder entry fees achieves a constant-factor of the optimal Bayesian IC revenue whenever buyers are unit-demand or additive, unifying previous breakthroughs of <PERSON><PERSON><PERSON> et. al. and Yao, and improving both approximation ratios (from 33.75 to 24 and 69 to 8). Finally, we show that this view also leads to improved structural characterizations in the Cai et. al. framework.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897645"}, {"primary_key": "4192830", "vector": [], "sparse_vector": [], "title": "Streaming algorithms for embedding and computing edit distance in the low distance regime.", "authors": ["Diptarka Chakraborty", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Hamming and the edit metrics are two common notions of measuring distances between pairs of strings x,y lying in the Boolean hypercube. The edit distance between x and y is defined as the minimum number of character insertion, deletion, and bit flips needed for converting x into y. Whereas, the Hamming distance between x and y is the number of bit flips needed for converting x to y. In this paper we study a randomized injective embedding of the edit distance into the Hamming distance with a small distortion. We show a randomized embedding with quadratic distortion. Namely, for any x,y satisfying that their edit distance equals k, the Hamming distance between the embedding of x and y is O(k2) with high probability. This improves over the distortion ratio of O( n * n) obtained by <PERSON><PERSON><PERSON> (2012) for small values of k. Moreover, the embedding output size is linear in the input size and the embedding can be computed using a single pass over the input. We provide several applications for this embedding. Among our results we provide a one-pass (streaming) algorithm for edit distance running in space O(s) and computing edit distance exactly up-to distance s1/6. This algorithm is based on kernelization for edit distance that is of independent interest.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897577"}, {"primary_key": "4192831", "vector": [], "sparse_vector": [], "title": "Non-malleable extractors and codes, with their many tampered extensions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Randomness extractors and error correcting codes are fundamental objects in computer science. Recently, there have been several natural generalizations of these objects, in the context and study of tamper resilient cryptography. These are seeded non-malleable extractors, introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON>; seedless non-malleable extractors, introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>; and non-malleable codes, introduced by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Besides being interesting on their own, they also have important applications in cryptography, e.g, privacy amplification with an active adversary, explicit non-malleable codes etc, and often have unexpected connections to their non-tampered analogues.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897547"}, {"primary_key": "4192832", "vector": [], "sparse_vector": [], "title": "Extractors for sumset sources.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new model of weak random sources which we call sumset sources. A sumset source X is the sum of C independent sources, with each source on n bits source having min-entropy k. We show that extractors for this class of sources can be used to give extractors for most classes of weak sources that have been studied previously, including independent sources, affine sources (which generalizes oblivious bit-fixing sources), small space sources, total entropy independent sources, and interleaved sources. This provides a unified approach for randomness extraction.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897643"}, {"primary_key": "4192833", "vector": [], "sparse_vector": [], "title": "Explicit two-source extractors and resilient functions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We explicitly construct an extractor for two independent sources on n bits, each with polylogarithmic min-entropy. Our extractor outputs one bit and has polynomially small error. The best previous extractor, by <PERSON><PERSON><PERSON>, required each source to have min-entropy .499n.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897528"}, {"primary_key": "4192834", "vector": [], "sparse_vector": [], "title": "Basis collapse for holographic algorithms over all domain sizes.", "authors": ["<PERSON><PERSON>"], "summary": "The theory of holographic algorithms introduced by <PERSON><PERSON> represents a novel approach to achieving polynomial-time algorithms for seemingly intractable counting problems via a reduction to counting planar perfect matchings and a linear change of basis. Two fundamental parameters in holographic algorithms are the domain size and the basis size. Roughly, the domain size is the range of colors involved in the counting problem at hand (e.g. counting graph k-colorings is a problem over domain size k), while the basis size captures the dimensionality of the representation of those colors. A major open problem has been: for a given k, what is the smallest ℓ for which any holographic algorithm for a problem over domain size k \"collapses to\" (can be simulated by) a holographic algorithm with basis size ℓ? <PERSON><PERSON> and <PERSON> showed in 2008 that over domain size 2, basis size 1 suffices, opening the door to an extensive line of work on the structural theory of holographic algorithms over the Boolean domain. <PERSON><PERSON> and <PERSON> later showed for signatures of full rank that over domain sizes 3 and 4, basis sizes 1 and 2, respectively, suffice, and they conjectured that over domain size k there is a collapse to basis size ⌊log2 k⌋. In this work, we resolve this conjecture in the affirmative for signatures of full rank for all k.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897546"}, {"primary_key": "4192835", "vector": [], "sparse_vector": [], "title": "Near-optimal small-depth lower bounds for small distance connectivity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that any depth-d circuit for determining whether an n-node graph has an s-to-t path of length at most k must have size nΩ(k1/d/d) when k(n) ≤ n1/5, and nΩ(k1/5d/d) when k(n)≤ n. The previous best circuit size lower bounds were nkexp(−O(d)) (by <PERSON><PERSON>, Impag<PERSON>, and <PERSON><PERSON><PERSON> (Computational Complexity 1998)) and nΩ((logk)/d) (following from a recent formula size lower bound of <PERSON><PERSON> (STOC 2014)). Our lower bound is quite close to optimal, as a simple construction gives depth-d circuits of size nO(k2/d) for this problem (and strengthening our bound even to nkΩ(1/d) would require proving that undirected connectivity is not in NC1).", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897534"}, {"primary_key": "4192836", "vector": [], "sparse_vector": [], "title": "Improved approximation for node-disjoint paths in planar graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the classical Node-Disjoint Paths (NDP) problem: given an n-vertex graph G and a collection =(s1,t1),…,(sk,tk) of pairs of vertices of G called demand pairs, find a maximum-cardinality set of node-disjoint paths connecting the demand pairs. NDP is one of the most basic routing problems, that has been studied extensively. Despite this, there are still wide gaps in our understanding of its approximability: the best currently known upper bound of O(√n) on its approximation ratio is achieved via a simple greedy algorithm, while the best current negative result shows that the problem does not have a better than Ω(log1/2−δn)-approximation for any constant δ, under standard complexity assumptions. Even for planar graphs no better approximation algorithms are known, and to the best of our knowledge, the best negative bound is APX-hardness. Perhaps the biggest obstacle to obtaining better approximation algorithms for NDP is that most currently known approximation algorithms for this type of problems rely on the standard multicommodity flow relaxation, whose integrality gap is Ω(√n) for NDP, even in planar graphs. In this paper, we break the barrier of O(√n) on the approximability of NDP in planar graphs and obtain an Õ(n9/19)-approximation. We introduce a new linear programming relaxation of the problem, and a number of new techniques, that we hope will be helpful in designing more powerful algorithms for this and related problems.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897538"}, {"primary_key": "4192837", "vector": [], "sparse_vector": [], "title": "Approximating connectivity domination in weighted bounded-genus graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a framework for addressing several problems on weighted planar graphs and graphs of bounded genus. With that framework, we derive polynomial-time approximation schemes for the following problems in planar graphs or graphs of bounded genus: edge-weighted tree cover and tour cover; vertex-weighted connected dominating set, max-weight-leaf spanning tree, and connected vertex cover. In addition, we obtain a polynomial-time approximation scheme for feedback vertex set in planar graphs. These are the first polynomial-time approximation schemes for all those problems in weighted embedded graphs. (For unweighted versions of some of these problems, polynomial-time approximation schemes were previously given using bidimensionality.)", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897635"}, {"primary_key": "4192838", "vector": [], "sparse_vector": [], "title": "Two-source dispersers for polylogarithmic entropy and improved ramsey graphs.", "authors": ["<PERSON>"], "summary": "In his influential 1947 paper that inaugurated the probabilistic method, <PERSON><PERSON><PERSON><PERSON> proved the existence of 2logn-Ramsey graphs on n vertices. Matching <PERSON><PERSON><PERSON><PERSON>' result with a constructive proof is considered a central problem in combinatorics, that has gained a significant attention in the literature. The state of the art result was obtained in the celebrated paper by <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> who constructed a 22(loglogn)1−α-Ramsey graph, for some small universal constant α > 0.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897530"}, {"primary_key": "4192839", "vector": [], "sparse_vector": [], "title": "Watermarking cryptographic capabilities.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A watermarking scheme for programs embeds some information called a mark into a program while preserving its functionality. No adversary can remove the mark without damaging the functionality of the program. In this work, we study the problem of watermarking various cryptographic programs such as pseudorandom function (PRF) evaluation, decryption, and signing. For example, given a PRF key K, we create a marked program C that evaluates the PRF F(K,). An adversary that gets C cannot come up with any program C* in which the mark is removed but which still evaluates the PRF correctly on even a small fraction of the inputs. The work of Bar<PERSON>, Goldreich, Impagliazzo, R<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (CRYPTO'01 and Journal of ACM 59(2)) shows that, assuming indistinguishability obfuscation (iO), such watermarking is impossible if the marked program C evaluates the original program with perfect correctness. In this work we show that, assuming iO, such watermarking is possible if the marked program C is allowed to err with even a negligible probability, which would be undetectable to the user. Our watermarking schemes are public key, namely we use a secret marking key to embed marks in programs, and a public detection key that allows anyone to detect marks in programs. Our schemes are secure against chosen program attacks, that is even if the adversary is given oracle access to the marking functionality. We emphasize that our security notion of watermark non-removability considers arbitrary adversarial strategies to modify the marked program, in contrast to the prior works (<PERSON><PERSON><PERSON>, EUROCRYPT '13).", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897651"}, {"primary_key": "4192840", "vector": [], "sparse_vector": [], "title": "Geometric median in nearly linear time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we provide faster algorithms for solving the geometric median problem: given n points in d compute a point that minimizes the sum of Euclidean distances to the points. This is one of the oldest non-trivial problems in computational geometry yet despite a long history of research the previous fastest running times for computing a (1+є)-approximate geometric median were O(d· n4/3є−8/3) by <PERSON> et. al, Õ(dexpє−4logє−1) by <PERSON><PERSON><PERSON> et. al, O(nd+poly(d,є−1)) by <PERSON><PERSON><PERSON> and <PERSON><PERSON>, and the polynomial running time of O((nd)O(1)log1/є) by <PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON> and <PERSON>.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897647"}, {"primary_key": "4192841", "vector": [], "sparse_vector": [], "title": "Relating two property testing models for bounded degree directed graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study property testing algorithms in directed graphs (digraphs) with maximum indegree and maximum outdegree upper bounded by d. For directed graphs with bounded degree, there are two different models in property testing introduced by <PERSON><PERSON> and <PERSON> (2002). In the bidirectional model, one can access both incoming and outgoing edges while in the unidirectional model one can only access outgoing edges. In our paper we provide a new relation between the two models: we prove that if a property can be tested with constant query complexity in the bidirectional model, then it can be tested with sublinear query complexity in the unidirectional model. A corollary of this result is that in the unidirectional model (the model allowing only queries to the outgoing neighbors), every property in hyperfinite digraphs is testable with sublinear query complexity.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897575"}, {"primary_key": "4192842", "vector": [], "sparse_vector": [], "title": "Complexity theoretic limitations on learning halfspaces.", "authors": ["<PERSON><PERSON>"], "summary": "We study the problem of agnostically learning halfspaces which is defined by a fixed but unknown distribution D on Q^n X {-1,1}. We define <PERSON>rr_H(D) as the least error of a halfspace classifier for D. A learner who can access D has to return a hypothesis whose error is small compared to Err_H(D).", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897520"}, {"primary_key": "4192843", "vector": [], "sparse_vector": [], "title": "A cost function for similarity-based hierarchical clustering.", "authors": ["<PERSON><PERSON>"], "summary": "The development of algorithms for hierarchical clustering has been hampered by a shortage of precise objective functions. To help address this situation, we introduce a simple cost function on hierarchies over a set of points, given pairwise similarities between those points. We show that this criterion behaves sensibly in canonical instances and that it admits a top-down construction procedure with a provably good approximation ratio.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897527"}, {"primary_key": "4192844", "vector": [], "sparse_vector": [], "title": "A size-free CLT for poisson multinomials and its applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An (n,k)-Poisson Multinomial Distribution (PMD) is the distribution of the sum of n independent random vectors supported on the set Bk={e1,…,ek} of standard basis vectors in ℝk. We show that any (n,k)-PMD is poly(k/σ)-close in total variation distance to the (appropriately discretized) multi-dimensional Gaussian with the same first two moments, removing the dependence on n from the Central Limit Theorem of Valiant and Valiant. Interestingly, our CLT is obtained by bootstrapping the Valiant-Valiant CLT itself through the structural characterization of PMDs shown in recent work by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>. In turn, our stronger CLT can be leveraged to obtain an efficient PTAS for approximate Nash equilibria in anonymous games, significantly improving the state of the art, and matching qualitatively the running time dependence on n and 1/є of the best known algorithm for two-strategy anonymous games. Our new CLT also enables the construction of covers for the set of (n,k)-PMDs, which are proper and whose size is shown to be essentially optimal. Our cover construction combines our CLT with the <PERSON><PERSON><PERSON><PERSON><PERSON> theorem and recent sparsification results for Laplacian matrices by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Our cover size lower bound is based on an algebraic geometric construction. Finally, leveraging the structural properties of the Fourier spectrum of PMDs we show that these distributions can be learned from Ok(1/є2) samples in polyk(1/є)-time, removing the quasi-polynomial dependence of the running time on 1/є from prior work.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897519"}, {"primary_key": "4192845", "vector": [], "sparse_vector": [], "title": "On the effect of randomness on planted 3-coloring models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present the hosted coloring framework for studying al- gorithmic and hardness results for the k-coloring problem. There is a class H of host graphs. One selects a graph H ∈ H and plants in it a balanced k-coloring (by partitioning the vertex set into k roughly equal parts, and removing all edges within each part). The resulting graph G is given as input to a polynomial time algorithm that needs to k-color G (any legal k-coloring would do – the algorithm is not required to recover the planted k-coloring). Earlier planted models correspond to the case that H is the class of all n-vertex d-regular graphs, a member H ∈ H is chosen at random, and then a balanced k-coloring is planted at random. <PERSON><PERSON> and <PERSON> [1995] designed algorithms for this model when d = n δ (for 0 < δ ≤ 1), and <PERSON><PERSON> and <PERSON> [1997] managed to do so even when d is a sufficiently large constant. The new aspect in our framework is that it need not in- volve randomness. In one model within the framework (with k = 3) H is a d regular spectral expander (meaning that ex- cept for the largest eigenvalue of its adjacency matrix, every other eigenvalue has absolute value much smaller than d) chosen by an adversary, and the planted 3-coloring is ran- dom. We show that the 3-coloring algorithm of <PERSON><PERSON> and <PERSON> [1997] can be modified to apply to this case. In an- other model H is a random d-regular graph but the planted balanced 3-coloring is chosen by an adversary, after seeing H. We show that for a certain range of average degrees somewhat below √ n, finding a 3-coloring is NP-hard. To- gether these results (and other results that we have) help clarify which aspects of randomness in the planted coloring model are the key to successful 3-coloring algorithms.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897561"}, {"primary_key": "4192846", "vector": [], "sparse_vector": [], "title": "The sample complexity of auctions with side information.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Traditionally, the Bayesian optimal auction design problem has been considered either when the bidder values are i.i.d, or when each bidder is individually identifiable via her value distribution. The latter is a reasonable approach when the bidders can be classified into a few categories, but there are many instances where the classification of bidders is a continuum. For example, the classification of the bidders may be based on their annual income, their propensity to buy an item based on past behavior, or in the case of ad auctions, the click through rate of their ads. We introduce an alternate model that captures this aspect, where bidders are a priori identical, but can be distinguished based (only) on some side information the auctioneer obtains at the time of the auction. We extend the sample complexity approach of <PERSON><PERSON><PERSON><PERSON> et al. and <PERSON> and <PERSON> to this model and obtain almost matching upper and lower bounds. As an aside, we obtain a revenue monotonicity lemma which may be of independent interest. We also show how to use Empirical Risk Minimization techniques to improve the sample complexity bound of <PERSON> and <PERSON> for the non-identical but independent value distribution case.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897553"}, {"primary_key": "4192847", "vector": [], "sparse_vector": [], "title": "The fourier transform of poisson multinomial distributions and its algorithmic applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "An (n, k)-Poisson Multinomial Distribution (PMD) is a random variable of the form X = ∑i=1n Xi, where the Xi's are independent random vectors supported on the set of standard basis vectors in k. In this paper, we obtain a refined structural understanding of PMDs by analyzing their Fourier transform. As our core structural result, we prove that the Fourier transform of PMDs is approximately sparse, i.e., its L1-norm is small outside a small set. By building on this result, we obtain the following applications:", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897552"}, {"primary_key": "4192848", "vector": [], "sparse_vector": [], "title": "Breaking the logarithmic barrier for truthful combinatorial auctions with submodular bidders.", "authors": ["<PERSON><PERSON>"], "summary": "We study a central problem in Algorithmic Mechanism Design: constructing truthful mechanisms for welfare maximization in combinatorial auctions with submodular bidders. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> provided the first mechanism that guarantees a non-trivial approximation ratio of O(log^2 m) [STOC'06], where m is the number of items. This was subsequently improved to O( log m log log m) [<PERSON><PERSON><PERSON><PERSON>, APPROX'07] and then to O(m) [<PERSON><PERSON><PERSON> and Vocking, ICALP'12]. In this paper we develop the first mechanism that breaks the logarithmic barrier. Specifically, the mechanism provides an approximation ratio of O( m). Similarly to previous constructions, our mechanism uses polynomially many value and demand queries, and in fact provides the same approximation ratio for the larger class of XOS (a.k.a. fractionally subadditive) valuations. We also develop a computationally efficient implementation of the mechanism for combinatorial auctions with budget additive bidders. Although in general computing a demand query is NP-hard for budget additive valuations, we observe that the specific form of demand queries that our mechanism uses can be efficiently computed when bidders are budget additive.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897569"}, {"primary_key": "4192849", "vector": [], "sparse_vector": [], "title": "Algorithmic Bayesian persuasion.", "authors": ["<PERSON><PERSON><PERSON>", "Haifeng Xu"], "summary": "Persuasion, defined as the act of exploiting an informational advantage in order to effect the decisions of others, is ubiquitous. Indeed, persuasive communication has been estimated to account for almost a third of all economic activity in the US. This paper examines persuasion through a computational lens, focusing on what is perhaps the most basic and fundamental model in this space: the celebrated Bayesian persuasion model of Kamenica and Gentzkow. Here there are two players, a sender and a receiver. The receiver must take one of a number of actions with a-priori unknown payoff, and the sender has access to additional information regarding the payoffs of the various actions for both players. The sender can commit to revealing a noisy signal regarding the realization of the payoffs of various actions, and would like to do so as to maximize her own payoff in expectation assuming that the receiver rationally acts to maximize his own payoff. When the payoffs of various actions follow a joint distribution (the common prior), the sender's problem is nontrivial, and its computational complexity depends on the representation of this prior. We examine the sender's optimization task in three of the most natural input models for this problem, and essentially pin down its computational complexity in each. When the payoff distributions of the different actions are i.i.d. and given explicitly, we exhibit a polynomial-time (exact) algorithmic solution, and a ``simple'' (1-1/e)-approximation algorithm. Our optimal scheme for the i.i.d. setting involves an analogy to auction theory, and makes use of <PERSON>'s characterization of the space of reduced-forms for single-item auctions. When action payoffs are independent but non-identical with marginal distributions given explicitly, we show that it is #P-hard to compute the optimal expected sender utility. In doing so, we rule out a generalized Border's theorem, as defined by Gopalan et al, for this setting. Finally, we consider a general (possibly correlated) joint distribution of action payoffs presented by a black box sampling oracle, and exhibit a fully polynomial-time approximation scheme (FPTAS) with a bi-criteria guarantee. Our FPTAS is based on Monte-Carlo sampling, and its analysis relies on the principle of deferred decisions. Moreover, we show that this result is the best possible in the black-box model for information-theoretic reasons.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897583"}, {"primary_key": "4192850", "vector": [], "sparse_vector": [], "title": "Deterministic and probabilistic binary search in graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the following natural generalization of Binary Search: in a given undirected, positively weighted graph, one vertex is a target. The algorithm's task is to identify the target by adaptively querying vertices. In response to querying a node q, the algorithm learns either that q is the target, or is given an edge out of q that lies on a shortest path from q to the target. We study this problem in a general noisy model in which each query independently receives a correct answer with probability p > 1/2 (a known constant), and an (adversarial) incorrect one with probability 1 − p.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897656"}, {"primary_key": "4192851", "vector": [], "sparse_vector": [], "title": "Online matching: haste makes waste!", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper studies a new online problem, referred to as min-cost perfect matching with delays (MPMD), defined over a finite metric space (i.e., a complete graph with positive edge weights obeying the triangle inequality) M that is known to the algorithm in advance. Requests arrive in a continuous time online fashion at the points of M and should be served by matching them to each other. The algorithm is allowed to delay its request matching commitments, but this does not come for free: the total cost of the algorithm is the sum of metric distances between matched requests plus the sum of times each request waited since it arrived until it was matched. A randomized online MPMD algorithm is presented whose competitive ratio is O (log2 n + logΔ), where n is the number of points in M and Δ is its aspect ratio. The analysis is based on a machinery developed in the context of a new stochastic process that can be viewed as two interleaved Poisson processes; surprisingly, this new process captures precisely the behavior of our algorithm. A related problem in which the algorithm is allowed to clear any unmatched request at a fixed penalty is also addressed. It is suggested that the MPMD problem is merely the tip of the iceberg for a general framework of online problems with delayed service that captures many more natural problems.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897557"}, {"primary_key": "4192852", "vector": [], "sparse_vector": [], "title": "Routing under balance.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the notion of balance for directed graphs: a weighted directed graph is α-balanced if for every cut S ⊆ V, the total weight of edges going from S to V∖ S is within factor α of the total weight of edges going from V∖ S to S. Several important families of graphs are nearly balanced, in particular, Eulerian graphs (with α = 1) and residual graphs of (1+є)-approximate undirected maximum flows (with α=O(1/є)).", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897654"}, {"primary_key": "4192853", "vector": [], "sparse_vector": [], "title": "Bounded degree cosystolic expanders of every dimension.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In recent years a high dimensional theory of expanders has emerged. The notion of combinatorial expansion of graphs (i.e. the <PERSON><PERSON><PERSON> constant of a graph) has seen two generalizations to high dimensional simplicial complexes. One generalization, known as coboundary expansion, is due to <PERSON><PERSON> and <PERSON><PERSON><PERSON>; the other, which we term here cosystolic expansion, is due to <PERSON><PERSON><PERSON>, who showed that cosystolic expanders have the topological overlapping property. No construction (either random or explicit) of bounded degree combinational expanders (according to either definition) were known until a recent work of <PERSON>, <PERSON> and <PERSON>, which provided the first bounded degree cosystolic expanders of dimension two. No bounded degree combinatorial expanders are known in higher dimensions. In this work we present explicit bounded degree cosystolic expanders of every dimension. This solves affirmatively an open question raised by <PERSON><PERSON><PERSON>, who asked whether there exist bounded degree complexes with the topological overlapping property in every dimension. Moreover, we provide a local to global criterion on a complex that implies cosystolic expansion: Namely, for a d-dimensional complex, X, if its underlying graph is a good expander, and all its links are both coboundary expanders and good expander graphs, then the (d-1)-dimensional skeleton of the complex is a cosystolic expander.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897543"}, {"primary_key": "4192854", "vector": [], "sparse_vector": [], "title": "The price of anarchy in large games.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present an analysis framework for bounding the price of anarchy (POA) in games that have many players, as in many of the games most pertinent to computer science applications. We use this framework to demonstrate that, in many of the models in which the POA has been studied, the POA in large games is much smaller than the worst-case bound. Our framework also differentiates between mechanisms with similar worst-case performance, such as simultaneous uniform-price auctions and greedy combinatorial auctions, thereby providing new insights about which mechanisms are likely to perform well in realistic settings.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897580"}, {"primary_key": "4192855", "vector": [], "sparse_vector": [], "title": "Bipartite perfect matching is in quasi-NC.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that the bipartite perfect matching problem is in quasi- NC2. That is, it has uniform circuits of quasi-polynomial size nO(logn), and O(log2 n) depth. Previously, only an exponential upper bound was known on the size of such circuits with poly-logarithmic depth.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897564"}, {"primary_key": "4192856", "vector": [], "sparse_vector": [], "title": "Exact algorithms via monotone local search.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We give a new general approach for designing exact exponential-time algorithms for subset problems. In a subset problem the input implicitly describes a family of sets over a universe of size n and the task is to determine whether the family contains at least one set. A typical example of a subset problem is Weighted d-SAT. Here, the input is a CNF-formula with clauses of size at most d, and an integer W. The universe is the set of variables and the variables have integer weights. The family contains all the subsets S of variables such that the total weight of the variables in S does not exceed W, and setting the variables in S to 1 and the remaining variables to 0 satisfies the formula. Our approach is based on \"monotone local search\", where the goal is to extend a partial solution to a solution by adding as few elements as possible. More formally, in the extension problem we are also given as input a subset X of the universe and an integer k. The task is to determine whether one can add at most k elements to X to obtain a set in the (implicitly defined) family. Our main result is that a cknO(1) time algorithm for the extension problem immediately yields a randomized algorithm for finding a solution of any size with running time O((2−1/c)n).", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897551"}, {"primary_key": "4192857", "vector": [], "sparse_vector": [], "title": "Parallel exhaustive search without coordination.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We analyse parallel algorithms in the context of exhaustive search over totally ordered sets. Imagine an infinite list of \"boxes\", with a \"treasure\" hidden in one of them, where the boxes' order reflects the importance of finding the treasure in a given box. At each time step, a search protocol executed by a searcher has the ability to peek into one box, and see whether the treasure is present or not. Clearly, the best strategy of a single searcher would be to open the boxes one by one, in increasing order. Moreover, by equally dividing the workload between them, k searchers can trivially find the treasure k times faster than one searcher. However, this straightforward strategy is very sensitive to failures (e.g., crashes of processors), and overcoming this issue seems to require a large amount of communication. We therefore address the question of designing parallel search algorithms maximizing their speed-up and maintaining high levels of robustness, while minimizing the amount of resources for coordination. Based on the observation that algorithms that avoid communication are inherently robust, we focus our attention on identifying the best running time performance of non-coordinating algorithms. Specifically, we devise non-coordinating algorithms that achieve a speed-up of 9/8 for two searchers, a speed-up of 4/3 for three searchers, and in general, a speed-up of k/4(1+1/k)2 for any k≥ 1 searchers. Thus, asymptotically, the speed-up is only four times worse compared to the case of full coordination. Moreover, these bounds are tight in a strong sense as no non-coordinating search algorithm can achieve better speed-ups. Our algorithms are surprisingly simple and hence applicable. However they are memory intensive and so we suggest a practical, memory efficient version, with a speed-up of (k2 − 1)/4k. That is, it is only a factor of (k+1)/(k−1) slower than the optimal algorithm. Overall, we highlight that, in faulty contexts in which coordination between the searchers is technically difficult to implement, intrusive with respect to privacy, and/or costly in term of resources, it might well be worth giving up on coordination, and simply run our non-coordinating exhaustive search algorithms.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897541"}, {"primary_key": "4192858", "vector": [], "sparse_vector": [], "title": "Separating subadditive euclidean functionals.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The classical <PERSON><PERSON>-<PERSON><PERSON> theorem (1959) asserts the existence of an asymptotic formula of the form constant times square root n for the minimum length of a Traveling Salesperson Tour through n random points in the unit square, and in the decades since it was proved, the existence of such formulas has been shown for other such Euclidean functionals on random points in the unit square as well. Despite more than 50 years of attention, however, it remained unknown whether the minimum length TSP through n random points in the unit square was asymptotically distinct from its natural lower bounds, such as the minimum length spanning tree, the minimum length 2-factor, or, as raised by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, from its linear programming relaxation. We prove that the TSP on random points in Euclidean space is indeed asymptotically distinct from these and other natural lower bounds, and show that this separation implies that branch-and-bound algorithms based on these natural lower bounds must take nearly exponential time to solve the TSP to optimality, even in average case. This is the first average-case superpolynomial lower bound for these branch-and-bound algorithms.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897571"}, {"primary_key": "4192859", "vector": [], "sparse_vector": [], "title": "Exponential separation of communication and external information.", "authors": ["<PERSON><PERSON>", "Gillat Kol", "<PERSON><PERSON>"], "summary": "We show an exponential gap between communication complexity and external information complexity, by analyzing a communication task suggested as a candidate by <PERSON><PERSON>. Previously, only a separation of communication complexity and internal information complexity was known.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897535"}, {"primary_key": "4192860", "vector": [], "sparse_vector": [], "title": "Entangled simultaneity versus classical interactivity in communication complexity.", "authors": ["<PERSON>"], "summary": "In 1999 <PERSON><PERSON> demonstrated a partial function that had an efficient quantum two-way communication protocol but no efficient classical two-way protocol and asked, whether there existed a function with an efficient quantum one-way protocol, but still no efficient classical two-way protocol. In 2010 <PERSON><PERSON><PERSON> and <PERSON><PERSON> demonstrated such a function and asked, whether there existed a function with an efficient quantum simultaneous-messages protocol, but still no efficient classical two-way protocol. In this work we answer the latter question affirmatively and present a partial function Shape, which can be computed by a protocol sending entangled simultaneous messages of poly-logarithmic size, and whose classical two-way complexity is lower bounded by a polynomial.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897545"}, {"primary_key": "4192861", "vector": [], "sparse_vector": [], "title": "Matrix rigidity of random toeplitz matrices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that random n-by-n To<PERSON>litz (alternatively <PERSON><PERSON>) matrices over F2 have rigidity Ω(n3/r2logn) for rank r ≥ √n, with high probability. For r = o(n/logn · loglogn), this improves over the Ω(n2/r · log(n/r)) bound that is known for many explicit matrices.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897633"}, {"primary_key": "4192862", "vector": [], "sparse_vector": [], "title": "Textbook non-malleable commitments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a new non-malleable commitment protocol. Our protocol has the following features: itemize The protocol has only three rounds of interaction. Pass (TCC 2013) showed an impossibility result for a two-round non-malleable commitment scheme w.r.t. a black-box reduction to any ``standard\" intractability reduction. Thus, this resolves the round complexity of non-malleable commitment at least w.r.t. black-box security reductions. Our construction is secure as per the standard notion of non-malleability w.r.t. commitment. Our protocol is truly efficient. In our basic protocol, the entire computation of the committer is dominated by just three invocations of a non-interactive statically binding commitment scheme, while, the receiver computation (in the commitment stage) is limited to just sampling a random string. Unlike many previous works, we directly construct a protocol for large tags and hence avoid any non-malleability amplification steps. Our protocol is based on a black-box use of any non-interactive statistically binding commitment scheme. Such schemes, in turn, can be based on any one-to-one one-way function (or any one-way function at the cost of an extra initialization round). Previously, the best known black-box construction of non-malleable commitments required a larger (constant) number of rounds. Our construction is public-coin and makes use of only black-box simulation. Prior to our work, no public-coin constant round non-malleable commitment schemes were known based on black-box simulation. itemize Our techniques depart significantly from the techniques used previously to construct non-malleable commitment schemes. As a main technical tool, we rely on non-malleable codes in the split state model. Our proofs of security are purely combinatorial in nature. In addition, we also present a simple construction of constant round non-malleable commitments from any one-way function. While this result is not new, the main feature is its simplicity compared to any previous construction of non-malleable commitments (in any number of rounds). We believe the construction is simple enough to be covered in a graduate level course on cryptography. The construction uses non-malleable codes in the split state model in a black-box way.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897657"}, {"primary_key": "4192863", "vector": [], "sparse_vector": [], "title": "Repairing Reed-solomon codes.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "A fundamental fact about polynomial interpolation is that k evaluations of a degree-(k-1) polynomial f are sufficient to determine f. This is also necessary in a strong sense: given k-1 evaluations, we learn nothing about the value of f on any k'th point. In this paper, we study a variant of the polynomial interpolation problem. Instead of querying entire evaluations of f (which are elements of a large field F), we are allowed to query partial evaluations; that is, each evaluation delivers a few elements from a small subfield of F, rather than a single element from F. We show that in this model, one can do significantly better than in the traditional setting, in terms of the amount of information required to determine the missing evaluation. More precisely, we show that only O(k) bits are necessary to recover a missing evaluation. In contrast, the traditional method of looking at k evaluations requires Omega(k log(k)) bits. We also show that our result is optimal for linear methods, even up to the leading constants. Our motivation comes from the use of Reed-Solomon (RS) codes for distributed storage systems, in particular for the exact repair problem. The traditional use of RS codes in this setting is analogous to the traditional interpolation problem. Each node in a system stores an evaluation of f, and if one node fails we can recover it by reading k other nodes. However, each node is free to send less information, leading to the modified problem above. The quickly-developing field of regenerating codes has yielded several codes which take advantage of this freedom. However, these codes are not RS codes, and RS codes are still often used in practice; in 2011, <PERSON><PERSON><PERSON> et al. asked how well RS codes could perform in this setting. Our results imply that RS codes can also take advantage of this freedom to download partial symbols. In some parameter regimes---those with small levels of sub-packetization---our scheme for RS codes outperforms all known regenerating codes. Even with a high degree of sub-packetization, our methods give non-trivial schemes, and we give an improved repair scheme for a specific (14,10)-RS code used in the Facebook Hadoop Analytics cluster.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897525"}, {"primary_key": "4192864", "vector": [], "sparse_vector": [], "title": "Sample-optimal tomography of quantum states.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "It is a fundamental problem to decide how many copies of an unknown mixed quantum state are necessary and sufficient to determine the state. This is the quantum analogue of the problem of estimating a probability distribution given some number of samples.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897585"}, {"primary_key": "4192865", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> coverings of graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Let G be a finite connected graph, and let ρ be the spectral radius of its universal cover. For example, if G is k-regular then ρ=2√k−1. We show that for every r, there is an r-covering (a.k.a. an r-lift) of G where all the new eigenvalues are bounded from above by ρ. It follows that a bipartite Ramanujan graph has a Ramanujan r-covering for every r. This generalizes the r=2 case due to <PERSON>, <PERSON> and <PERSON> (2013).", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897574"}, {"primary_key": "4192866", "vector": [], "sparse_vector": [], "title": "Distributed (∆+1)-coloring in sublogarithmic rounds.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The (∆+1)-coloring problem is a fundamental symmetry breaking problem in distributed computing. We give a new randomized coloring algorithm for (∆+1)-coloring running in O(√log ∆)+ 2^O(√log log n) rounds with probability 1-1/n^Ω(1) in a graph with n nodes and maximum degree ∆. This implies that the (∆+1)-coloring problem is easier than the maximal independent set problem and the maximal matching problem, due to their lower bounds by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> [PODC'04]. Our algorithm also extends to the list-coloring problem where the palette of each node contains ∆+1 colors.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897533"}, {"primary_key": "4192867", "vector": [], "sparse_vector": [], "title": "The computational power of optimization in online learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the fundamental problem of prediction with expert advice where the experts are \"optimizable\": there is a black-box optimization oracle that can be used to compute, in constant time, the leading expert in retrospect at any point in time. In this setting, we give a novel online algorithm that attains vanishing regret with respect to N experts in total O(√N) computation time. We also give a lower bound showing that this running time cannot be improved (up to log factors) in the oracle model, thereby exhibiting a quadratic speedup as compared to the standard, oracle-free setting where the required time for vanishing regret is Θ(N). These results demonstrate an exponential gap between the power of optimization in online learning and its power in statistical learning: in the latter, an optimization oracle—i.e., an efficient empirical risk minimizer—allows to learn a finite hypothesis class of size N in time O(logN).", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897536"}, {"primary_key": "4192868", "vector": [], "sparse_vector": [], "title": "A deterministic almost-tight distributed algorithm for approximating single-source shortest paths.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Danupon <PERSON>"], "summary": "We present a deterministic $(1+o(1))$-approximation $(n^{1/2+o(1)}+D^{1+o(1)})$-time algorithm for solving the single-source shortest paths problem on distributed weighted networks (the CONGEST model); here $n$ is the number of nodes in the network and $D$ is its (hop) diameter. This is the first non-trivial deterministic algorithm for this problem. It also improves (i) the running time of the randomized $(1+o(1))$-approximation $\\tilde O(n^{1/2}D^{1/4}+D)$-time algorithm of Nanongkai [STOC 2014] by a factor of as large as $n^{1/8}$, and (ii) the $O(\\epsilon^{-1}\\log \\epsilon^{-1})$-approximation factor of <PERSON><PERSON> and <PERSON><PERSON><PERSON>Sham<PERSON>'s $\\tilde O(n^{1/2+\\epsilon}+D)$-time algorithm [STOC 2013] within the same running time. Our running time matches the known time lower bound of $\\Omega(n^{1/2}/\\log n + D)$ [Elkin STOC 2004] up to subpolynomial factors, thus essentially settling the status of this problem which was raised at least a decade ago [Elkin SIGACT News 2004]. It also implies a $(2+o(1))$-approximation $(n^{1/2+o(1)}+D^{1+o(1)})$-time algorithm for approximating a network's weighted diameter which almost matches the lower bound by Holzer and Pinsker [OPODIS 2015]. In achieving this result, we develop two techniques which might be of independent interest and useful in other settings: (i) a deterministic process that replaces the \"hitting set argument\" commonly used for shortest paths computation in various settings, and (ii) a simple, deterministic, construction of an $(n^{o(1)}, o(1))$-hop set of size $n^{1+o(1)}$. We combine these techniques with many distributed algorithmic techniques, some of which from problems that are not directly related to shortest paths, e.g., ruling sets [Goldberg et al. STOC 1987], source detection [Lenzen and Peleg PODC 2013], and partial distance estimation [Lenzen and Patt-Shamir PODC 2015].", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897638"}, {"primary_key": "4192869", "vector": [], "sparse_vector": [], "title": "Fast spectral algorithms from sum-of-squares proofs: tensor decomposition and planted sparse vectors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider two problems that arise in machine learning applications: the problem of recovering a planted sparse vector in a random linear subspace and the problem of decomposing a random low-rank overcomplete 3-tensor. For both problems, the best known guarantees are based on the sum-of-squares method. We develop new algorithms inspired by analyses of the sum-of-squares method. Our algorithms achieve the same or similar guarantees as sum-of-squares for these problems but the running time is significantly faster.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897529"}, {"primary_key": "4192870", "vector": [], "sparse_vector": [], "title": "Do prices coordinate markets?", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Walrasian equilibrium prices can be said to coordinate markets: They support a welfare optimal allocation in which each buyer is buying bundle of goods that is individually most preferred. However, this clean story has two caveats. First, the prices alone are not sufficient to coordinate the market, and buyers may need to select among their most preferred bundles in a coordinated way to find a feasible allocation. Second, we don't in practice expect to encounter exact equilibrium prices tailored to the market, but instead only approximate prices, somehow encoding \"distributional\" information about the market. How well do prices work to coordinate markets when tie-breaking is not coordinated, and they encode only distributional information? We answer this question. First, we provide a genericity condition such that for buyers with Matroid Based Valuations, overdemand with respect to equilibrium prices is at most 1, independent of the supply of goods, even when tie-breaking is done in an uncoordinated fashion. Second, we provide learning-theoretic results that show that such prices are robust to changing the buyers in the market, so long as all buyers are sampled from the same (unknown) distribution.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897559"}, {"primary_key": "4192871", "vector": [], "sparse_vector": [], "title": "Classical verification of quantum proofs.", "authors": ["Zhengfeng Ji"], "summary": "We present a classical interactive protocol that verifies the validity of a quantum witness state for the local Hamiltonian problem. It follows from this protocol that approximating the non-local value of a multi-player one-round game to inverse polynomial precision is QMA-hard. Our work makes an interesting connection between the theory of QMA-completeness and Hamiltonian complexity on one hand and the study of non-local games and Bell inequalities on the other.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897634"}, {"primary_key": "4192872", "vector": [], "sparse_vector": [], "title": "Super-linear gate and super-quadratic wire lower bounds for depth-two and depth-three threshold circuits.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In order to formally understand the power of neural computing, we first need to crack the frontier of threshold circuits with two and three layers, a regime that has been surprisingly intractable to analyze. We prove the first super-linear gate lower bounds and the first super-quadratic wire lower bounds for depth-two linear threshold circuits with arbitrary weights, and depth-three majority circuits computing an explicit function. (1) We prove that for all ε ≪ √log(n)/n, the linear-time computable <PERSON><PERSON>'s function cannot be computed on a (1/2+ε)-fraction of n-bit inputs by depth-two circuits of o(ε3 n3/2/log3 n) gates, nor can it be computed with o(ε3 n5/2/log7/2 n) wires. This establishes an average-case \"size hierarchy\" for threshold circuits, as <PERSON><PERSON>'s function is computable by uniform depth-two circuits of o(n3) linear threshold gates, and by uniform depth-three circuits of O(n) majority gates. (2) We present a new function in P based on small-biased sets, which we prove cannot be computed by a majority vote of depth-two threshold circuits of o(n3/2/log3 n) gates, nor with o(n5/2/log7/2n) wires. (3) We give tight average-case (gate and wire) complexity results for computing PARITY with depth-two threshold circuits; the answer turns out to be the same as for depth-two majority circuits. The key is a new method for analyzing random restrictions to linear threshold functions. Our main analytical tool is the Littlewood-Offord Lemma from additive combinatorics.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897636"}, {"primary_key": "4192873", "vector": [], "sparse_vector": [], "title": "Sparse fourier transform in any constant dimension with nearly-optimal sample complexity in sublinear time.", "authors": ["<PERSON>"], "summary": "We consider the problem of computing a k-sparse approximation to the Fourier transform of a length N signal. Our main result is a randomized algorithm for computing such an approximation (i.e. achieving ℓ2/ℓ2 sparse recovery guarantees using Fourier measurements) using Od(klogNloglogN) samples of the signal in time domain and Od(klogd+3 N) runtime, where d≥ 1 is the dimensionality of the Fourier transform. The sample complexity matches the Ω(klog(N/k)) lower bound for non-adaptive algorithms due to [DIPW] for any k≤ N1−δ for a constant δ>0 up to an O(loglogN) factor. Prior to our work a result with comparable sample complexity klogN logO(1)logN and sublinear runtime was known for the Fourier transform on the line [IKP], but for any dimension d≥ 2 previously known techniques either suffered from a (logN) factor loss in sample complexity or required Ω(N) runtime.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897650"}, {"primary_key": "4192874", "vector": [], "sparse_vector": [], "title": "Enumerating parametric global minimum cuts by random interleaving.", "authors": ["<PERSON>"], "summary": "Recently, <PERSON><PERSON> et al. gave new counting and algorithmic bounds for parametric minimum cuts in a graph, where each edge cost is a linear combination of multiple cost criteria and different cuts become minimum as the coefficients of the linear combination are varied. In this article, we derive better bounds using a mathematically simpler argument. We provide faster algorithms for enumerating these cuts. We give a lower bound showing our upper bounds have roughly the right degree. Our results also immediately generalize to parametric versions of other problems solved by the Contraction Algorithm, including approximate min-cuts, multi-way cuts, and a matroid optimization problem. We also give a first generalization to nonlinear parametric minimum cuts.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897578"}, {"primary_key": "4192875", "vector": [], "sparse_vector": [], "title": "On the size of homogeneous and of depth four formulas with low individual degree.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Let r be an integer. Let us call a polynomial f as a multi-r-ic polynomial if the degree of f with respect to any variable is at most r (this generalizes the notion of multilinear polynomials). We investigate arithmetic circuits in which the output is syntactically forced to be a multi-r-ic polynomial and refer to these as multi-r-ic circuits. Specifically, first define the formal degree of a node a with respect to a variable x inductively as follows. For a leaf it is 1 if a is labelled with x and zero otherwise; for an internal node labelled with * (respectively +) it is the sum of (respectively the maximum of) the formal degrees of the children with respect to x. We call an arithmetic circuit as a multi-r-ic circuit if the formal degree of the output node with respect to any variable is at most r. We prove lower bounds for various subclasses of multi-r-ic circuits.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897550"}, {"primary_key": "4192876", "vector": [], "sparse_vector": [], "title": "Candidate hard unique game.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a candidate reduction for ruling out polynomial-time algorithms for unique games, either under plausible complexity assumptions, or unconditionally for Lasserre semi-definite programs with a constant number of rounds. We analyze the completeness and Las<PERSON>re solution of our construction, and provide a soundness analysis in a certain setting of interest. Addressing general settings is tightly connected to a question on Gaussian isoperimetry. Our construction is based on our previous work on the complexity of approximately solving a system of linear equations over reals, which we suggested as an avenue towards a (positive) resolution of the Unique Games Conjecture. The construction employs a new encoding scheme that we call the real code. The real code has two useful properties: like the long code, it has a unique local test, and like the Hadamard code, it has the so-called sub-code covering property.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897531"}, {"primary_key": "4192877", "vector": [], "sparse_vector": [], "title": "Interactive compression for product distributions.", "authors": ["Gillat Kol"], "summary": "We study the interactive compression problem: Given a two-party communication protocol with small information cost, can it be compressed so that the total number of bits communicated is also small? We consider the case where the parties have inputs that are independent of each other, and give a simulation protocol that communicates I^2 * polylog(I) bits, where I is the information cost of the original protocol. Our protocol is the first simulation protocol whose communication complexity is bounded by a polynomial in the information cost of the original protocol.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897537"}, {"primary_key": "4192878", "vector": [], "sparse_vector": [], "title": "High-rate locally-correctable and locally-testable codes with sub-polynomial query complexity.", "authors": ["Swas<PERSON><PERSON>", "Or Meir", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this work, we construct the first locally-correctable codes (LCCs), and locally-testable codes (LTCs) with constant rate, constant relative distance, and sub-polynomial query complexity. Specifically, we show that there exist LCCs and LTCs with block length n, constant rate (which can even be taken arbitrarily close to 1) and constant relative distance, whose query complexity is exp(Õ(√logn)) (for LCCs) and (logn)O(loglogn) (for LTCs). Previously such codes were known to exist only with Ω(nβ) query complexity (for constant β>0).", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897523"}, {"primary_key": "4192879", "vector": [], "sparse_vector": [], "title": "Reed-Muller codes achieve capacity on erasure channels.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> L. Urbanke"], "summary": "We introduce a new approach to proving that a sequence of deterministic linear codes achieves capacity on an erasure channel under maximum a posteriori decoding. Rather than relying on the precise structure of the codes, our method exploits code symmetry. In particular, the technique applies to any sequence of linear codes where the block lengths are strictly increasing, the code rates converge, and the permutation group of each code is doubly transitive. In a nutshell, we show that symmetry alone implies near-optimal performance.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897584"}, {"primary_key": "4192880", "vector": [], "sparse_vector": [], "title": "Sparsified Cholesky and multigrid solvers for connection laplacians.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the sparsified Cholesky and sparsified multigrid algorithms for solving systems of linear equations. These algorithms accelerate Gaussian elimination by sparsifying the nonzero matrix entries created by the elimination process. We use these new algorithms to derive the first nearly linear time algorithms for solving systems of equations in connection Laplacians---a generalization of Laplacian matrices that arise in many problems in image and signal processing. We also prove that every connection Laplacian has a linear sized approximate inverse. This is an LU factorization with a linear number of nonzero entries that is a strong approximation of the original matrix. Using such a factorization one can solve systems of equations in a connection Laplacian in linear time. Such a factorization was unknown even for ordinary graph Laplacians.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897640"}, {"primary_key": "4192881", "vector": [], "sparse_vector": [], "title": "A (1+epsilon)-approximation for makespan scheduling with precedence constraints using LP hierarchies.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In a classical problem in scheduling, one has n unit size jobs with a precedence order and the goal is to find a schedule of those jobs on m identical machines as to minimize the makespan. It is one of the remaining four open problems from the book of <PERSON><PERSON><PERSON> & <PERSON> whether or not this problem is NP-hard for m=3.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897532"}, {"primary_key": "4192882", "vector": [], "sparse_vector": [], "title": "On approximating functions of the singular values in a stream.", "authors": ["<PERSON>", "<PERSON>"], "summary": "For any real number p > 0, we nearly completely characterize the space complexity of estimating ||A||pp = ∑i=1n σip for n × n matrices A in which each row and each column has O(1) non-zero entries and whose entries are presented one at a time in a data stream model. Here the σi are the singular values of A, and when p ≥ 1, ||A||pp is the p-th power of the Sc<PERSON>ten p-norm. We show that when p is not an even integer, to obtain a (1+є)-approximation to ||A||pp with constant probability, any 1-pass algorithm requires n1−g(є) bits of space, where g(є) → 0 as є → 0 and є > 0 is a constant independent of n. However, when p is an even integer, we give an upper bound of n1−2/p (є−1logn) bits of space, which holds even in the turnstile data stream model. The latter is optimal up to (є−1 logn) factors.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897581"}, {"primary_key": "4192883", "vector": [], "sparse_vector": [], "title": "How robust are reconstruction thresholds for community detection?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The stochastic block model is one of the oldest and most ubiquitous models for studying clustering and community detection. In an exciting sequence of developments, motivated by deep but non-rigorous ideas from statistical physics, <PERSON><PERSON> et al. conjectured a sharp threshold for when community detection is possible in the sparse regime. <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON> proved the conjecture and gave matching algorithms and lower bounds.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897573"}, {"primary_key": "4192884", "vector": [], "sparse_vector": [], "title": "Semidefinite programs on sparse random graphs and their application to community detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Denote by A the adjacency matrix of an <PERSON><PERSON><PERSON><PERSON> graph with bounded average degree. We consider the problem of maximizing over the set of positive semidefinite matrices X with diagonal entries X_ii=1. We prove that for large (bounded) average degree d, the value of this semidefinite program (SDP) is --with high probability-- 2n*sqrt(d) + n, o(sqrt(d))+o(n). For a random regular graph of degree d, we prove that the SDP value is 2n*sqrt(d-1)+o(n), matching a spectral upper bound. Informally, Erdos-Ren<PERSON> graphs appear to behave similarly to random regular graphs for semidefinite programming. We next consider the sparse, two-groups, symmetric community detection problem (also known as planted partition). We establish that SDP achieves the information-theoretically optimal detection threshold for large (bounded) degree. Namely, under this model, the vertex set is partitioned into subsets of size n/2, with edge probability a/n (within group) and b/n (across). We prove that SDP detects the partition with high probability provided (a-b)^2/(4d)> 1+o_d(1), with d= (a+b)/2. By comparison, the information theoretic threshold for detecting the hidden partition is (a-b)^2/(4d)> 1: SDP is nearly optimal for large bounded average degree. Our proof is based on tools from different research areas: (i) A new 'higher-rank' Groth<PERSON>ieck inequality for symmetric matrices; (ii) An interpolation method inspired from statistical physics; (iii) An analysis of the eigenvectors of deformed Gaussian random matrices.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897548"}, {"primary_key": "4192885", "vector": [], "sparse_vector": [], "title": "Maximizing determinants under partition constraints.", "authors": ["Aleksan<PERSON>", "<PERSON><PERSON>"], "summary": "Given a positive semidefinte matrix L whose columns and rows are indexed by a set U, and a partition matroid M=(U, I), we study the problem of selecting a basis B of M such that the determinant of the submatrix of L induced by the rows and columns in B is maximized. This problem appears in many areas including determinantal point processes in machine learning, experimental design, geographical placement problems, discrepancy theory and computational geometry to model subset selection problems that incorporate diversity.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897649"}, {"primary_key": "4192886", "vector": [], "sparse_vector": [], "title": "Efficient quantum tomography.", "authors": ["Ryan <PERSON>&<PERSON>;Donnell", "<PERSON>"], "summary": "In the quantum state tomography problem, one wishes to estimate an unknown d-dimensional mixed quantum state ρ, given few copies. We show that O(d/ε) copies suffice to obtain an estimate ρ that satisfies ||ρ − ρ||F2 ≤ ε (with high probability). An immediate consequence is that O((ρ) · d/ε2) ≤ O(d2/ε2) copies suffice to obtain an ε-accurate estimate in the standard trace distance. This improves on the best known prior result of O(d3/ε2) copies for full tomography, and even on the best known prior result of O(d2log(d/ε)/ε2) copies for spectrum estimation. Our result is the first to show that nontrivial tomography can be obtained using a number of copies that is just linear in the dimension.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897544"}, {"primary_key": "4192887", "vector": [], "sparse_vector": [], "title": "Poly-logarithmic Frege depth lower bounds via an expander switching lemma.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that any polynomial-size Frege refutation of a certain linear-size unsatisfiable 3-CNF formula over n variables must have depth Ω(√logn). This is an exponential improvement over the previous best results (<PERSON><PERSON><PERSON> et al. 1993, <PERSON><PERSON><PERSON> et al. 1995, <PERSON><PERSON> 2002) which give Ω(loglogn) lower bounds.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897637"}, {"primary_key": "4192888", "vector": [], "sparse_vector": [], "title": "Weighted low rank approximations with provable guarantees.", "authors": ["Ilya <PERSON>", "<PERSON>", "<PERSON>"], "summary": "The classical low rank approximation problem is: given a matrix A, find a rank-k matrix B such that the Frobenius norm of A − B is minimized. It can be solved efficiently using, for instance, the Singular Value Decomposition (SVD). If one allows randomization and approximation, it can be solved in time proportional to the number of non-zero entries of A with high probability.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897639"}, {"primary_key": "4192889", "vector": [], "sparse_vector": [], "title": "Constant-round interactive proofs for delegating computation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The celebrated IP=PSPACE Theorem of Lund et-al. (<PERSON><PERSON> 1992) and <PERSON><PERSON><PERSON> (<PERSON><PERSON> 1992), allows an all-powerful but untrusted prover to convince a polynomial-time verifier of the validity of extremely complicated statements (as long as they can be evaluated using polynomial space). The interactive proof system designed for this purpose requires a polynomial number of communication rounds and an exponential-time (polynomial-space complete) prover. In this paper, we study the power of more efficient interactive proof systems.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897652"}, {"primary_key": "4192890", "vector": [], "sparse_vector": [], "title": "Watch and learn: optimizing from revealed preferences feedback.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A Stackelberg game is played between a leader and a follower. The leader first chooses an action, then the follower plays his best response. The goal of the leader is to pick the action that will maximize his payoff given the follower's best response. In this paper we present an approach to solving for the leader's optimal strategy in certain Stackelberg games where the follower's utility function (and thus the subsequent best response of the follower) is unknown. Stackelberg games capture, for example, the following interaction between a producer and a consumer. The producer chooses the prices of the goods he produces, and then a consumer chooses to buy a utility maximizing bundle of goods. The goal of the seller here is to set prices to maximize his profit—his revenue, minus the production cost of the purchased bundle. It is quite natural that the seller in this example should not know the buyer's utility function. However, he does have access to revealed preference feedback---he can set prices, and then observe the purchased bundle and his own profit. We give algorithms for efficiently solving, in terms of both computational and query complexity, a broad class of Stackelberg games in which the follower's utility function is unknown, using only \"revealed preference\" access to it. This class includes in particular the profit maximization problem, as well as the optimal tolling problem in nonatomic congestion games, when the latency functions are unknown. Surprisingly, we are able to solve these problems even though the optimization problems are non-convex in the leader's actions.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897579"}, {"primary_key": "4192891", "vector": [], "sparse_vector": [], "title": "Beyond matroids: secretary problem and prophet inequality with general constraints.", "authors": ["<PERSON>via<PERSON>"], "summary": "We study generalizations of the ``Prophet Inequality'' and ``Secretary Problem'', where the algorithm is restricted to an arbitrary downward-closed set system. For 0,1 values, we give O(n)-competitive algorithms for both problems. This is close to the Omega(n/log n) lower bound due to <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. For general values, our results translate to O(log(n) log(r))-competitive algorithms, where r is the cardinality of the largest feasible set. This resolves (up to the O(loglog(n) log(r)) factor) an open question posed to us by <PERSON>.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897540"}, {"primary_key": "4192892", "vector": [], "sparse_vector": [], "title": "Efficiently decoding Reed-Muller codes from random errors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reed-Muller codes encode an m-variate polynomial of degree r by evaluating it on all points in {0,1}m. We denote this code by RM(m,r). The minimal distance of RM(m,r) is 2m−r and so it cannot correct more than half that number of errors in the worst case. For random errors one may hope for a better result.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897526"}, {"primary_key": "4192893", "vector": [], "sparse_vector": [], "title": "Instance optimal learning of discrete distributions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the following basic learning task: given independent draws from an unknown distribution over a discrete support, output an approximation of the distribution that is as accurate as possible in L1 distance (equivalently, total variation distance, or \"statistical distance\"). Perhaps surprisingly, it is often possible to \"de-noise\" the empirical distribution of the samples to return an approximation of the true distribution that is significantly more accurate than the empirical distribution, without relying on any prior assumptions on the distribution. We present an instance optimal learning algorithm which optimally performs this de-noising for every distribution for which such a de-noising is possible. More formally, given n independent draws from a distribution p, our algorithm returns a labelled vector whose expected distance from p is equal to the minimum possible expected error that could be obtained by any algorithm, even one that is given the true unlabeled vector of probabilities of distribution p and simply needs to assign labels---up to an additive subconstant term that is independent of p and goes to zero as n gets large. This somewhat surprising result has several conceptual implications, including the fact that, for any large sample from a distribution over discrete support, prior knowledge of the rates of decay of the tails of the distribution (e.g. power-law type assumptions) is not significantly helpful for the task of learning the distribution. As a consequence of our techniques, we also show that given a set of n samples from an arbitrary distribution, one can accurately estimate the expected number of distinct elements that will be observed in a sample of any size up to n log n. This sort of extrapolation is practically relevant, particularly to domains such as genomics where it is important to understand how much more might be discovered given larger sample sizes, and we are optimistic that our approach is practically viable.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897641"}, {"primary_key": "4192894", "vector": [], "sparse_vector": [], "title": "Base collapse of holographic algorithms.", "authors": ["<PERSON><PERSON>"], "summary": "A holographic algorithm solves a problem in a domain of size n, by reducing it to counting perfect matchings in planar graphs. It may simulate a n-value variable by a bunch of t matchgate bits, which has 2t values. The transformation in the simulation can be expressed as a n × 2t matrix M, called the base of the holographic algorithm. We wonder whether more matchgate bits bring us more powerful holographic algorithms. In another word, whether we can solve the same original problem, with a collapsed base of size n × 2r, where r<t.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897560"}, {"primary_key": "4192895", "vector": [], "sparse_vector": [], "title": "Cell-probe lower bounds for dynamic problems via a new communication model.", "authors": ["Huacheng Yu"], "summary": "In this paper, we develop a new communication model to prove a data structure lower bound for the dynamic interval union problem. The problem is to maintain a multiset of intervals I over [0, n] with integer coordinates, supporting the following operations: 1) insert(a, b), add an interval [a, b] to I, provided that a and b are integers in [0, n]; 2) delete(a, b), delete an (existing) interval [a, b] from I; 3) query(), return the total length of the union of all intervals in I.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897556"}, {"primary_key": "4192896", "vector": [], "sparse_vector": [], "title": "A tight space bound for consensus.", "authors": ["<PERSON><PERSON>"], "summary": "Existing n-process randomized wait-free (and obstruction-free) consensus protocols from registers all use at least n registers. In 1992, it was proved that such protocols must use Omega(sqrt(n)) registers. Recently, this was improved to Omega(n) registers in the anonymous setting, where processes do not have identifiers. Closing the gap in the general case, however, remained an open problem. We resolve this problem by proving that every randomized wait-free (or obstruction-free) consensus protocol for n processes must use at least n-1 registers.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/2897518.2897565"}, {"primary_key": "4211341", "vector": [], "sparse_vector": [], "title": "Proceedings of the 48th Annual ACM SIGACT Symposium on Theory of Computing, STOC 2016, Cambridge, MA, USA, June 18-21, 2016", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "No abstract available.", "published": "2016-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": ""}]