[{"primary_key": "3407016", "vector": [], "sparse_vector": [], "title": "Sublinear Algorithms for Local Graph Centrality Estimation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the complexity of local graph centrality estimation, with the goal of approximating the centrality score of a given target node while exploring only a sublinear number of nodes/arcs of the graph and performing a sublinear number of elementary operations. We develop a technique, that we apply to the PageRank and Heat Kernel centralities, for building a low-variance score estimator through a local exploration of the graph. We obtain an algorithm that, given any node in any graph of m arcs, with probability (1-δ) computes a multiplicative (1±ε)-approximation of its score by examining only Õ(min(m 2/3 Δ 1/3 d -2/3 , m 4/5 d -3/5 )) nodes/arcs, where Δ and d are respectively the maximum and average outdegree of the graph (omitting for readability poly(ε -1 ) and polylog(δ -1 ) factors). A similar bound holds for computational cost. We also prove a lower bound of Ω(min (m 1/2 Δ 1/2 d -1/2 , m 2/3 d -1/3 )) for both query complexity and computational complexity. Moreover, our technique yields a Õ(n 2/3 )-queries algorithm for an n-node graph in the access model of [<PERSON><PERSON><PERSON><PERSON> et al., 2010], widely used in social network mining; we show this algorithm is optimal up to a sublogarithmic factor. These are the first algorithms yielding worst-case sublinear bounds for general directed graphs and any choice of the target node.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00073"}, {"primary_key": "3407017", "vector": [], "sparse_vector": [], "title": "Finding Forbidden Minors in Sublinear Time: A n1/2+o(1)-Query One-Sided Tester for Minor Closed Properties on Bounded Degree Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Let G be an undirected, bounded degree graph with n vertices. Fix a finite graph H, and suppose one must remove ε n edges from G to make it H-minor free (for some small constant ε > 0). We give an n 1/2+o(1) -time randomized procedure that, with high probability, finds an H-minor in such a graph. As an application, suppose one must remove ε n edges from a bounded degree graph G to make it planar. This result implies an algorithm, with the same running time, that produces a K 3,3 or K 5 minor in G. No prior sublinear time bound was known for this problem. By the graph minor theorem, we get an analogous result for any minor-closed property. Up to n o(1) factors, this resolves a conjecture of <PERSON><PERSON><PERSON><PERSON> (STOC 2008) on the existence of one-sided property testers for minor-closed properties. Furthermore, our algorithm is nearly optimal, by an Ω(√n) lower bound of <PERSON><PERSON><PERSON><PERSON> et al (RSA 2014). Prior to this work, the only graphs H for which non-trivial one-sided property testers were known for H-minor freeness are the following: H being a forest or a cycle (<PERSON><PERSON><PERSON> et al, RSA 2014), K 2 , k , (k× 2)-grid, and the k-circus (<PERSON><PERSON> et al, Arxiv 2017).", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00055"}, {"primary_key": "3407018", "vector": [], "sparse_vector": [], "title": "Random Order Contention Resolution Schemes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Contention resolution schemes have proven to be an incredibly powerful concept which allows tackling a broad class of problems. The framework has been initially designed to handle submodular optimization under various types of constraints, that is, intersections of exchange systems (including matroids), knapsacks, and unsplittable flows on trees. Later on, it turned out that this framework perfectly extends to optimization under uncertainty, like stochastic probing and online selection problems, which further can be applied to mechanism design. We add to this line of work by showing how to create contention resolution schemes for intersection of matroids and knapsacks when we work in the random order setting. More precisely, we do know the whole universe of elements in advance, but they appear in an order given by a random permutation. Upon arrival we need to irrevocably decide whether to take an element or not. We bring a novel technique for analyzing procedures in the random order setting that is based on the martingale theory. This unified approach makes it easier to combine constraints, and we do not need to rely on the monotonicity of contention resolution schemes, as it was the case before. Our paper fills the gaps, extends, and creates connections between many previous results and techniques. The main application of our framework is a k + 4 + ε approximation ratio for the Bayesian multi-parameter unit-demand mechanism design under the constraint of k matroids intersection, which improves upon the previous bounds of 4k - 2 and e(k + 1). Other results include improved approximation ratios for stochastic k-set packing and submodular stochastic probing over arbitrary nonnegative submodular objective function, whereas previous results required the objective to be monotone.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00080"}, {"primary_key": "3407019", "vector": [], "sparse_vector": [], "title": "Limits on All Known (and Some Unknown) Approaches to Matrix Multiplication.", "authors": ["<PERSON>", "Virginia Vassilevska Williams"], "summary": "We study the known techniques for designing Matrix Multiplication algorithms. The two main approaches are the Laser method of Strassen, and the Group theoretic approach of <PERSON><PERSON> and <PERSON><PERSON>. We define a generalization based on zeroing outs which subsumes these two approaches, which we call the Solar method, and an even more general method based on monomial degenerations, which we call the Galactic method. We then design a suite of techniques for proving lower bounds on the value of omega, the exponent of matrix multiplication, which can be achieved by algorithms using many tensors T and the Galactic method. Some of our techniques exploit 'local' properties of T, like finding a sub-tensor of T which is so 'weak' that T itself couldn't be used to achieve a good bound on omega, while others exploit 'global' properties, like T being a monomial degeneration of the structural tensor of a group algebra. Our main result is that there is a universal constant ℓ>2 such that a large class of tensors generalizing the Coppersmith-Winograd tensor CW_q cannot be used within the Galactic method to show a bound on omega better than ell, for any q. We give evidence that previous lower-bounding techniques were not strong enough to show this. We also prove a number of complementary results along the way, including that for any group G, the structural tensor of C[G] can be used to recover the best bound on omega which the Coppersmith-Winograd approach gets using CW_|G|-2 as long as the asymptotic rank of the structural tensor is not too large.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00061"}, {"primary_key": "3407020", "vector": [], "sparse_vector": [], "title": "Log-Concave Polynomials, Entropy, and a Deterministic Approximation Algorithm for Counting Bases of Matroids.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We give a deterministic polynomial time 2^O(r)-approximation algorithm for the number of bases of a given matroid of rank r and the number of common bases of any two matroids of rank r. To the best of our knowledge, this is the first nontrivial deterministic approximation algorithm that works for arbitrary matroids. Based on a lower bound of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> this is almost the best possible assuming oracle access to independent sets of the matroid. There are two main ingredients in our result: For the first, we build upon recent results of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> and <PERSON> and <PERSON> on combinatorial hodge theory to derive a connection between matroids and log-concave polynomials. We expect that several new applications in approximation algorithms will be derived from this connection in future. Formally, we prove that the multivariate generating polynomial of the bases of any matroid is log-concave as a function over the positive orthant. For the second ingredient, we develop a general framework for approximate counting in discrete problems, based on convex optimization. The connection goes through subadditivity of the entropy. For matroids, we prove that an approximate superadditivity of the entropy holds by relying on the log-concavity of the corresponding polynomials.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00013"}, {"primary_key": "3407021", "vector": [], "sparse_vector": [], "title": "Planar Graph Perfect Matching Is in NC.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Is perfect matching in NC? That is, is there a deterministic fast parallel algorithm for it? This has been an outstanding open question in theoretical computer science for over three decades, ever since the discovery of RNC matching algorithms. Within this question, the case of planar graphs has remained an enigma: On the one hand, counting the number of perfect matchings is far harder than finding one (the former is #P-complete and the latter is in P), and on the other, for planar graphs, counting has long been known to be in NC whereas finding one has resisted a solution. In this paper, we give an NC algorithm for finding a perfect matching in a planar graph. Our algorithm uses the above-stated fact about counting matchings in a crucial way. Our main new idea is an NC algorithm for finding a face of the perfect matching polytope at which many new conditions, involving constraints of the polytope, are simultaneously satisfied. Several other ideas are also needed, such as finding a point in the interior of the minimum weight face of this polytope and finding a balanced tight odd set in NC.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00068"}, {"primary_key": "3407022", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>lder Homeomorphisms and Approximate Nearest Neighbors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aleksan<PERSON>", "Ilya <PERSON>", "<PERSON>"], "summary": "We study bi-Hölder homeomorphisms between the unit spheres of finite-dimensional normed spaces and use them to obtain better data structures for the high-dimensional Approximate Near Neighbor search (ANN) in general normed spaces. Our main structural result is a finite-dimensional quantitative version of the following theorem of <PERSON><PERSON> (1993) and <PERSON><PERSON> (unpublished). Every d-dimensional normed space X admits a small perturbation Y such that there is a bi-Holder homeomorphism with good parameters between the unit spheres of Y and Z, where Z is a space that is close to ℓ_2^d. Furthermore, the bulk of this article is devoted to obtaining an algorithm to compute the above homeomorphism in time polynomial in d. Along the way, we show how to compute efficiently the norm of a given vector in a space obtained by the complex interpolation between two normed spaces. We demonstrate that, despite being much weaker than bi-Lipschitz embeddings, such homeomorphisms can be efficiently utilized for the ANN problem. Specifically, we give two new data structures for ANN over a general d-dimensional normed space, which for the first time achieve approximation d^o(1), thus improving upon the previous general bound O(sqrtd) that is directly implied by <PERSON>'s theorem.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00024"}, {"primary_key": "3407023", "vector": [], "sparse_vector": [], "title": "Parallel Graph Connectivity in Log Diameter Rounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many modern parallel systems, such as MapReduce, Hadoop and Spark, can be modeled well by the MPC model. The MPC model captures well coarse-grained computation on large data — data is distributed to processors, each of which has a sublinear (in the input data) amount of memory and we alternate between rounds of computation and rounds of communication, where each machine can communicate an amount of data as large as the size of its memory. This model is stronger than the classical PRAM model, and it is an intriguing question to design algorithms whose running time is smaller than in the PRAM model. One fundamental graph problem is connectivity. On an undirected graph with n nodes and m edges, O(log n) round connectivity algorithms have been known for over 35 years. However, no algorithms with better complexity bounds were known. In this work, we give fully scalable, faster algorithms for the connectivity problem, by parameterizing the time complexity as a function of the diameter of the graph. Our main result is a O(log D log log_m/n n) time connectivity algorithm for diameter-d graphs, using Θ(m) total memory. If our algorithm can use more memory, it can terminate in fewer rounds, and there is no lower bound on the memory per processor. We extend our results to related graph problems such as spanning forest, finding a DFS sequence, exact/approximate minimum spanning forest, and bottleneck spanning forest. We also show that achieving similar bounds for reachability in directed graphs would imply faster boolean matrix multiplication algorithms. We introduce several new algorithmic ideas. We describe a general technique called double exponential speed problem size reduction which roughly means that if we can use total memory n to reduce a problem from size n to n/k, for k=(N/n)^Θ(1) in one phase, then we can solve the problem in O(loglog_N/n n) phases. In order to achieve this fast reduction for graph connectivity, we use a multistep algorithm. One key step is a carefully constructed truncated broadcasting scheme where each node broadcasts neighbor sets to its neighbors in a way that limits the size of the resulting neighbor sets. Another key step is random leader contraction, where we choose a smaller set of leaders than many previous works do.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00070"}, {"primary_key": "3407024", "vector": [], "sparse_vector": [], "title": "Towards Learning Sparsely Used Dictionaries with Arbitrary Supports.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dictionary learning is a popular approach for inferring a hidden basis in which data has a sparse representation. There is a hidden dictionary or basis A which is an n × m matrix, with m > n typically (this is called the over-complete setting). Data generated from the dictionary is given by Y = AX where X is a matrix whose columns have supports chosen from a distribution over k-sparse vectors, and the non-zero values chosen from a symmetric distribution. Given Y , the goal is to recover A and X in polynomial time (in m, n). Existing algorithms give polynomial time guarantees for recovering incoherent dictionaries, under strong distributional assumptions both on the supports of the columns of X, and on the values of the non-zero entries. In this work, we study the following question: can we design efficient algorithms for recovering dictionaries when the supports of the columns of X are arbitrary? To address this question while circumventing the issue of non-identifiability, we study a natural semirandom model for dictionary learning. In this model, there are a large number of samples y = Ax with arbitrary k-sparse supports for x, along with a few samples where the sparse supports are chosen uniformly at random. While the presence of a few samples with random supports ensures identifiability, the support distribution can look almost arbitrary in aggregate. Hence, existing algorithmic techniques seem to break down as they make strong assumptions on the supports. Our main contribution is a new polynomial time algorithm for learning incoherent over-complete dictionaries that provably works under the semirandom model. Additionally the same algorithm provides polynomial time guarantees in new parameter regimes when the supports are fully random. Finally, as a by product of our techniques, we also identify a minimal set of conditions on the supports under which the dictionary can be (information theoretically) recovered from polynomially many samples for almost linear sparsity, i.e., k = Õ(n).", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00035"}, {"primary_key": "3407025", "vector": [], "sparse_vector": [], "title": "Improved Online Algorithm for Weighted Flow Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We discuss one of the most fundamental scheduling problem of processing jobs on a single machine to minimize the weighted flow time (weighted response time). Our main result is a O(log P)-competitive algorithm, where P is the maximum-to-minimum processing time ratio, improving upon the O(log 2 P)competitive algorithm of <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (STOC 2001). We also design a O(log D)-competitive algorithm, where D is the maximum-to-minimum density ratio of jobs. Finally, we show how to combine these results with the result of <PERSON><PERSON> and <PERSON> (SODA 2003) to achieve a O(log(min(P, D, W)))competitive algorithm (where W is the maximum-tominimum weight ratio), without knowing P, D, W in advance. As shown by <PERSON><PERSON> and <PERSON> (SODA 2009), no constant-competitive algorithm is achievable for this problem.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00048"}, {"primary_key": "3407026", "vector": [], "sparse_vector": [], "title": "Efficient Density Evaluation for Smooth Kernels.", "authors": ["Arturs Backurs", "<PERSON>", "<PERSON><PERSON><PERSON>", "Paris Siminelakis"], "summary": "Given a kernel function k(.,.) and a dataset P⊂ R^d, the kernel density function of P at a point x∈ R d is equal to KDF P (x):= 1/|P| Σy∈P k(x, y). Kernel density evaluation has numerous applications, in scientific computing, statistics, computer vision, machine learning and other fields. In all of them it is necessary to evaluate KDF P(x) quickly, often for many inputs x and large point-sets P. In this paper we present a collection of algorithms for efficient KDF evaluation under the assumptions that the kernel k is \"smooth\", i.e. the value changes at most polynomially with the distance. This assumption is satisfied by several well-studied kernels, including the (generalized) t-student kernel and rational quadratic kernel. For smooth kernels, we give a data structure that, after O(dn log (Φ n)/ε^2) preprocessing, estimates KDF P(x) up to a factor of 1 ± ε in O(dlog (Φ n)/ε 2 ) time, where Phi; is the aspect ratio. The log(Φn) term can be further replaced by log n under an additional decay condition on k, which is satisfied by the aforementioned examples. We further extend the results in two ways. First, we use low-distortion embeddings to extend the results to kernels defined for spaces other than ℓ_2. The key feature of this reduction is that the distortion of the embedding affects only the running time of the algorithm, not the accuracy of the estimation. As a result, we obtain (1+ε)-approximate estimation algorithms for kernels over other ℓ p norms, Earth-Mover Distance, and other metric spaces. Second, for smooth kernels that are decreasing with distance, we present a general reduction from density estimation to approximate near neighbor in the underlying space. This allows us to construct algorithms for general doubling metrics, as well as alternative algorithms for l p norms and other spaces.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00065"}, {"primary_key": "3407027", "vector": [], "sparse_vector": [], "title": "Dispersion for Data-Driven Algorithm Design, Online Learning, and Private Optimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A crucial problem in modern data science is data-driven algorithm design, where the goal is to choose the best algorithm, or algorithm parameters, for a specific application domain. In practice, we often optimize over a parametric algorithm family, searching for parameters with high performance on a collection of typical problem instances. While effective in practice, these procedures generally have not come with provable guarantees. A recent line of work initiated by a seminal paper of <PERSON> and <PERSON> (2017) analyzes application-specific algorithm selection from a theoretical perspective. We progress this research direction in several important settings. We provide upper and lower bounds on regret for algorithm selection in online settings, where problems arrive sequentially and we must choose parameters online. We also consider differentially private algorithm selection, where the goal is to find good parameters for a set of problems without divulging too much sensitive information contained therein. We analyze several important parameterized families of algorithms, including SDP-rounding schemes for problems formulated as integer quadratic programs as well as greedy techniques for several canonical subset selection problems. The cost function that measures an algorithm's performance is often a volatile piecewise Lipschitz function of its parameters, since a small change to the parameters can lead to a cascade of different decisions made by the algorithm. We present general techniques for optimizing the sum or average of piecewise Lipschitz functions when the underlying functions satisfy a sufficient and general condition called dispersion. Intuitively, a set of piecewise Lipschitz functions is dispersed if no small region contains many of the functions' discontinuities. Using dispersion, we improve over the best-known online learning regret bounds for a variety problems, prove regret bounds for problems not previously studied, and provide matching regret lower bounds. In the private optimization setting, we show how to optimize performance while preserving privacy for several important problems, providing matching upper and lower bounds on performance loss due to privacy preservation. Though algorithm selection is our primary motivation, we believe the notion of dispersion may be of independent interest. Therefore, we present our results for the more general problem of optimizing piecewise Lipschitz functions. Finally, we uncover dispersion in domains beyond algorithm selection, namely, auction design and pricing, providing online and privacy guarantees for these problems as well.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00064"}, {"primary_key": "3407028", "vector": [], "sparse_vector": [], "title": "Non-Malleable Codes for Small-Depth Circuits.", "authors": ["<PERSON>", "<PERSON>-<PERSON>ed", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct efficient, unconditional non-malleable codes that are secure against tampering functions computed by small-depth circuits. For constant-depth circuits of polynomial size (i.e. AC 0 tampering functions), our codes have codeword length n = k 1+0(1) for a k-bit message. This is an exponential improvement of the previous best construction due to <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> (STOC 2017), which had codeword length 2 O(√k) . Our construction remains efficient for circuit depths as large as Θ(log(n)/loglog(n)) (indeed, our codeword length remains n ≤ k 1+ε ), and extending our result beyond this would require separating P from NC 1 . We obtain our codes via a new efficient non-malleable reduction from small-depth tampering to split-state tampering. A novel aspect of our work is the incorporation of techniques from unconditional derandomization into the framework of non-malleable reductions. In particular, a key ingredient in our analysis is a recent pseudorandom switching lemma of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (CCC 2013), a derandomization of the influential switching lemma from circuit complexity; the randomness-efficiency of this switching lemma translates into the rate-efficiency of our codes via our non-malleable reduction.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00083"}, {"primary_key": "3407029", "vector": [], "sparse_vector": [], "title": "Constant Factor Approximation Algorithm for Weighted Flow Time on a Single Machine in Pseudo-Polynomial Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the weighted flow-time problem on a single machine, we are given a set of n jobs, where each job has a processing requirement p_j, release date r_j and weight w_j. The goal is to find a preemptive schedule which minimizes the sum of weighted flow-time of jobs, where the flow-time of a job is the difference between its completion time and its released date. We give the first pseudo-polynomial time constant approximation algorithm for this problem. The algorithm also extends directly to the problem of minimizing the _p norm of weighted flow-times. The running time of our algorithm is polynomial in n, the number of jobs, and P, which is the ratio of the largest to the smallest processing requirement of a job. Our algorithm relies on a novel reduction of this problem to a generalization of the multi-cut problem on trees, which we call Demand MultiCut problem. Even though we do not give a constant factor approximation algorithm for the Demand MultiCut problem on trees, we show that the specific instances of Demand MultiCut obtained by reduction from weighted flow-time problem instances have more structure in them, and we are able to employ techniques based on dynamic programming. Our dynamic programming algorithm relies on showing that there are near optimal solutions which have nice smoothness properties, and we exploit these properties to reduce the size of DP table.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00079"}, {"primary_key": "3407030", "vector": [], "sparse_vector": [], "title": "Tighter <PERSON> on Multi-Party Coin Flipping via Augmented Weak Martingales and Differentially Private Sampling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In his seminal work, <PERSON><PERSON><PERSON> [STOC '86] proved that the bias of any coin-flipping protocol is inversely proportional to the number of rounds. This lower bound was met for the two-party case by <PERSON> et al. [Journal of Cryptology '16], and the three-party case (up to a polylogarithmic factor) by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [SICOMP '17], and was approached for multi-party protocols by <PERSON><PERSON><PERSON> et al. [SODA '17] when the number of rounds is at least doubly exponential in the number of parties. For the complement case, however, the best bias for multi-party coin-flipping protocols is proportional to the number of parties and inversely proportional to the square root of the number of rounds. The latter bias is achieved by the majority protocol of <PERSON>wer<PERSON><PERSON> et al. [Manuscript '85]. Our main result is a tighter lower bound on the bias of coin-flipping protocols, showing that, if the number of rounds is bounded by some polynomial in the number of parties, then the bias is lower-bounded by a quantity that is inversely proportional to the square root of the number of rounds (up to a polylogarithmic factor). As far as we know, this is the first improvement of <PERSON><PERSON><PERSON>'s bound, and is far from the aforementioned upper bound of <PERSON><PERSON><PERSON><PERSON> et al. only by a factor of the number of parties. We prove the above bound using two new results that we believe are of independent interest. The first result is that a sequence of (\"augmented\") weak martingales have large gap: with constant probability there exists two adjacent variables whose gap is at least the ratio between the gap between the first and last variables and the square root of the number of variables. This generalizes over the result of <PERSON>leve and Impagliazzo [<PERSON>uscript '93], who showed that the above holds for strong martingales, and allows in some setting to exploit this gap by efficient algorithms. We prove the above using a novel argument that does not follow the more complicated approach of Cleve and Impagliazzo. The second result is a new sampling algorithm that uses a differentially private mechanism to minimize the effect of data divergence.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00084"}, {"primary_key": "3407031", "vector": [], "sparse_vector": [], "title": "Classical Lower Bounds from Quantum Upper Bounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We prove lower bounds on complexity measures, such as the approximate degree of a Boolean function and the approximate rank of a Boolean matrix, using quantum arguments. We prove these lower bounds using a quantum query algorithm for the combinatorial group testing problem. We show that for any function f, the approximate degree of computing the OR of n copies of f is Omega(sqrt n) times the approximate degree of f, which is optimal. No such general result was known prior to our work, and even the lower bound for the OR of ANDs function was only resolved in 2013. We then prove an analogous result in communication complexity, showing that the logarithm of the approximate rank (or more precisely, the approximate gamma-2 norm) of F: X x Y to 0,1 grows by a factor of Omega (sqrtn) when we take the OR of n copies of F, which is also essentially optimal. As a corollary, we give a new proof of <PERSON><PERSON><PERSON><PERSON>'s celebrated Omega(sqrtn) lower bound on the quantum communication complexity of the disjointness problem. Finally, we generalize both these results from composition with the OR function to composition with arbitrary symmetric functions, yielding nearly optimal lower bounds in this setting as well.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00040"}, {"primary_key": "3407032", "vector": [], "sparse_vector": [], "title": "Bloom Filters, Adaptivity, and the Dictionary Problem.", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "An approximate membership query data structure (AMQ)—such as a Bloom, quotient, or cuckoo filter—maintains a compact, probabilistic representation of a set S of keys from a universe U. It supports lookups and inserts. Some AMQs also support deletes. A query for x in S returns \"present.\" A query for x not in S returns \"present\" with a tunable false positive probability epsilon, and otherwise returns \"absent.\" AMQs are widely used to speed up dictionaries that are stored remotely (e.g., on disk or across a network). The AMQ is stored locally (e.g., in memory). The remote dictionary is only accessed when the AMQ returns \"present.\" Thus, the primary performance metric of an AMQ is how often it returns \"absent\" for negative queries. Existing AMQs offer weak guarantees on the number of false positives in a sequence of queries. The false-positive probability epsilon holds only for a single query. It is easy for an adversary to drive an AMQ's false-positive rate towards 1 by simply repeating false positives. This paper shows what it takes to get strong guarantees on the number of false positives. We say that an AMQ is adaptive if it guarantees a false-positive probability of epsilon for every query, regardless of answers to previous queries. We establish upper and lower bounds for adaptive AMQs. Our lower bound shows that it is impossible to build a small adaptive AMQ, even when the AMQ is immediately told whenever a query is a false positive. On the other hand, we show that it is possible to maintain an AMQ that uses the same amount of local space as a non-adaptive AMQ (up to lower order terms), performs all queries and updates in constant time, and guarantees that each negative query to the dictionary accesses remote storage with probability epsilon, independent of the results of past queries. Thus, we show that adaptivity can be achieved effectively for free.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00026"}, {"primary_key": "3407033", "vector": [], "sparse_vector": [], "title": "An ETH-Tight Exact Algorithm for Euclidean TSP.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study exact algorithms for Euclidean TSP in R d . In the early 1990s algorithms with n O(√n) running time were presented for the planar case, and some years later an algorithm with n O(n1-1/d) running time was presented for any d ≥ 2. Despite significant interest in subexponential exact algorithms over the past decade, there has been no progress on Euclidean TSP, except for a lower bound stating that the problem admits no 2 O (n 1-1/d-ε ) algorithm unless ETH fails. Up to constant factors in the exponent, we settle the complexity of Euclidean TSP by giving a 2 O(n1-1/d) algorithm and by showing that a 2 o(n1-1/d) algorithm does not exist unless ET<PERSON> fails.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00050"}, {"primary_key": "3407034", "vector": [], "sparse_vector": [], "title": "Deterministic Factorization of Sparse Polynomials with Bounded Individual Degree.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we study the problem of deterministic factorization of sparse polynomials. We show that if f is an n-variate polynomial with s monomials, with individual degrees of its variables bounded by d, then f can be deterministically factored in time s poly(d) log n . Prior to our work, the only efficient factoring algorithms known for this class of polynomials were randomized, and other than for the cases of d = 1 and d = 2, only exponential time deterministic factoring algorithms were known. A crucial ingredient in our proof is a quasi-polynomial sparsity bound for factors of sparse polynomials of bounded individual degree. In particular we show if f is an s-sparse polynomial in n variables, with individual degrees of its variables bounded by d, then the sparsity of each factor of f is bounded by s O(d2 log n) . This is the first nontrivial bound on factor sparsity for d > 2. Our sparsity bound uses techniques from convex geometry, such as the theory of Newton polytopes and an approximate version of the classical <PERSON><PERSON><PERSON><PERSON>'s Theorem. Our work addresses and partially answers a question of <PERSON> and <PERSON><PERSON> (JCSS 1985) who asked whether a quasi-polynomial bound holds for the sparsity of factors of sparse polynomials.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00053"}, {"primary_key": "3407035", "vector": [], "sparse_vector": [], "title": "EPTAS for Max Clique on Disks and Unit Balls.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a polynomial-time algorithm which takes as input a finite set of points of R^3 and computes, up to arbitrary precision, a maximum subset with diameter at most 1. More precisely, we give the first randomized EPTAS and deterministic PTAS for Maximum Clique in unit ball graphs. Our approximation algorithm also works on disk graphs with arbitrary radii, in the plane. Almost three decades ago, an elegant polynomial-time algorithm was found for Maximum Clique on unit disk graphs [<PERSON>, <PERSON>, <PERSON>; Discrete Mathematics '90]. Since then, it has been an intriguing open question whether or not tractability can be extended to general disk graphs. Recently, it was shown that the disjoint union of two odd cycles is never the complement of a disk graph [<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>; SoCG '18]. This enabled the authors to derive a QPTAS and a subexponential algorithm for Max Clique on disk graphs. In this paper, we improve the approximability to a randomized EPTAS (and a deterministic PTAS). More precisely, we obtain a randomized EPTAS for computing the independence number on graphs having no disjoint union of two odd cycles as an induced subgraph, bounded VC-dimension, and linear independence number. We then address the question of computing Max Clique for disks in higher dimensions. We show that intersection graphs of unit balls, like disk graphs, do not admit the complement of two odd cycles as an induced subgraph. This, in combination with the first result, straightforwardly yields a randomized EPTAS for Max Clique on unit ball graphs. In stark contrast, we show that on ball graphs and unit 4-dimensional disk graphs, Max Clique is NP-hard and does not admit an approximation scheme even in subexponential-time, unless the Exponential Time Hypothesis fails.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00060"}, {"primary_key": "3407036", "vector": [], "sparse_vector": [], "title": "Revealing Network Structure, Confidentially: Improved Rates for Node-Private Graphon Estimation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by growing concerns over ensuring privacy on social networks, we develop new algorithms and impossibility results for fitting complex statistical models to network data subject to rigorous privacy guarantees. We consider the so-called node-differentially private algorithms, which compute information about a graph or network while provably revealing almost no information about the presence or absence of a particular node in the graph. We provide new algorithms for node-differentially private estimation for a popular and expressive family of network models: stochastic block models and their generalization, graphons. Our algorithms improve on prior work [15], reducing their error quadratically and matching, in many regimes, the optimal nonprivate algorithm [37]. We also show that for the simplest random graph models (G(n, p) and G(n, m)), node-private algorithms can be qualitatively more accurate than for more complex models-converging at a rate of 1/ε 2 n 3 instead of 1/ε 2 n 2 . This result uses a new extension lemma for differentially private algorithms that we hope will be broadly useful.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00057"}, {"primary_key": "3407037", "vector": [], "sparse_vector": [], "title": "A Cryptographic Test of Quantumness and Certifiable Randomness from a Single Quantum Device.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We give a protocol for producing certifiable randomness from a single untrusted quantum device that is polynomial-time bounded. The randomness is certified to be statistically close to uniform from the point of view of any computationally unbounded quantum adversary, that may share entanglement with the quantum device. The protocol relies on the existence of post-quantum secure trapdoor claw-free functions, and introduces a new primitive for constraining the power of an untrusted quantum device. We then show how to construct this primitive based on the hardness of the learning with errors (LWE) problem. The randomness protocol can also be used as the basis for an efficiently verifiable \"quantum supremacy\" proposal, thus answering an outstanding challenge in the field.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00038"}, {"primary_key": "3407038", "vector": [], "sparse_vector": [], "title": "Efficient Algorithms for Tensor Scaling, Quantum Marginals, and Moment Polytopes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a polynomial time algorithm to approximately scale tensors of any format to arbitrary prescribed marginals (whenever possible). This unifies and generalizes a sequence of past works on matrix, operator and tensor scaling. Our algorithm provides an efficient weak membership oracle for the associated moment polytopes, an important family of implicitly-defined convex polytopes with exponentially many facets and a wide range of applications. These include the entanglement polytopes from quantum information theory (in particular, we obtain an efficient solution to the notorious one-body quantum marginal problem) and the Kronecker polytopes from representation theory (which capture the asymptotic support of <PERSON>rone<PERSON> coefficients). Our algorithm can be applied to succinct descriptions of the input tensor whenever the marginals can be efficiently computed, as in the important case of matrix product states or tensor-train decompositions, widely used in computational physics and numerical mathematics. We strengthen and generalize the alternating minimization approach of previous papers by introducing the theory of highest weight vectors from representation theory into the numerical optimization framework. We show that highest weight vectors are natural potential functions for scaling algorithms and prove new bounds on their evaluations to obtain polynomial-time convergence. Our techniques are general and we believe that they will be instrumental to obtain efficient algorithms for moment polytopes beyond the ones consider here, and more broadly, for other optimization problems possessing natural symmetries.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00088"}, {"primary_key": "3407039", "vector": [], "sparse_vector": [], "title": "The Complexity of General-Valued CSPs Seen from the Other Side.", "authors": ["Clément Carbonnel", "<PERSON>", "<PERSON><PERSON>"], "summary": "The constraint satisfaction problem (CSP) is concerned with homomorphisms between two structures. For CSPs with restricted left-hand side structures, the results of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [CP'02], <PERSON><PERSON><PERSON> [FOCS'03/JACM'07], and <PERSON>ser<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [ICALP'07] establish the precise borderline of polynomial-time solvability (subject to complexity-theoretic assumptions) and of solvability by bounded-consistency algorithms (unconditionally) as bounded treewidth modulo homomorphic equivalence. The general-valued constraint satisfaction problem (VCSP) is a generalisation of the CSP concerned with homomorphisms between two valued structures. For VCSPs with restricted left-hand side valued structures, we establish the precise borderline of polynomial-time solvability (subject to complexity-theoretic assumptions) and of solvability by the k-th level of the Sherali-Adams LP hierarchy (unconditionally). We also obtain results on related problems concerned with finding a solution and recognising the tractable cases; the latter has an application in database theory.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00031"}, {"primary_key": "3407040", "vector": [], "sparse_vector": [], "title": "Approximating Edit Distance within Constant Factor in Truly Sub-Quadratic Time.", "authors": ["Diptarka Chakraborty", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Edit distance is a measure of similarity of two strings based on the minimum number of character insertions, deletions, and substitutions required to transform one string into the other. The edit distance can be computed exactly using a dynamic programming algorithm that runs in quadratic time. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2010) gave a nearly linear time algorithm that approximates edit distance within approximation factor poly(log n). In this paper, we provide an algorithm with running time Õ(n^2-2/7) that approximates the edit distance within a constant factor.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00096"}, {"primary_key": "3407041", "vector": [], "sparse_vector": [], "title": "A Short List of Equalities Induces Large Sign Rank.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We exhibit a natural function F, that can be computed by just a linear sized decision list of 'Equalities', but whose sign rank is exponentially large. This yields the following two new unconditional complexity class separations. The first is an exponential separation between the depth-two threshold circuit classes Threshold-of Majority and Threshold-of-Threshold, answering an open question posed by <PERSON><PERSON> and <PERSON> [MFCS '05] and <PERSON> and <PERSON> [CCC '10]. The second separation shows that the communication complexity class P^MA is not contained in UPP, strongly resolving a recent open problem posed by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> [ICALP '16]. In order to prove our main result, we view F as an XOR function and develop a technique to lower bound the sign rank of such functions. This requires novel approximation theoretic arguments against polynomials of unrestricted degree. Further, our work highlights for the first time the class 'decision lists of exact thresholds' as a common frontier for making progress on longstanding open problems in Threshold circuits and communication complexity.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00014"}, {"primary_key": "3407042", "vector": [], "sparse_vector": [], "title": "Near-Optimal Approximate Decremental All Pairs Shortest Paths.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper we consider the decremental approximate all-pairs shortest paths (APSP) problem, where given a graph G the goal is to maintain approximate shortest paths between all pairs of nodes in G under a sequence of online adversarial edge deletions. We present a decremental APSP algorithm for undirected weighted graphs with (2+ε)k-1 stretch, O(mn 1/k +o(1) log(n W )) total update time and O(log log(n W)) query time for a fixed constant ε, where W is the maximum edge weight (assuming the minimum edge weight is 1) and k is any integer parameter. This is an exponential improvement both in the stretch and in the query time over previous works.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00025"}, {"primary_key": "3407043", "vector": [], "sparse_vector": [], "title": "Deterministic Document Exchange Protocols, and Almost Optimal Binary Codes for Edit Errors.", "authors": ["<PERSON><PERSON>", "Zhengzhong Jin", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study two basic problems regarding edit errors (insertions and deletions). The first one is document exchange, where two parties <PERSON> and <PERSON> hold two strings x and y with a bounded edit distance k. The goal is to have <PERSON> send a short sketch to <PERSON>, so that <PERSON> can recover x based on y and the sketch. The second one is the fundamental problem of designing error correcting codes for edit errors, where the goal is to construct an explicit code to transmit a message x through a channel that can add at most k worst case insertions and deletions, so that the original message x can be successfully recovered at the other end of the channel. Both problems have been extensively studied for decades, and in this paper we focus on deterministic document exchange protocols and binary codes for insertions and deletions (insdel codes). If the length of x is n, then it is known that for small k (e.g., k ≤ n/4), in both problems the optimal sketch size or the optimal number of redundant bits is Θ(k log n/k). In particular, this implies the existence of binary codes that can correct ε fraction of insertions and deletions with rate 1-Θ(ε log (1/ε). However, known constructions are far from achieving these bounds. In this paper we significantly improve previous results on both problems. For document exchange, we give an efficient deterministic protocol with sketch size O(k log 2 n/k). This significantly improves the previous best known deterministic protocol, which has sketch size O(k 2 + k log 2 n) [2]. For binary insdel codes, we obtain the following results: 1) An explicit binary insdel code which encodes an n-bit message x against k errors with redundancy O(k log 2 n/k). In particular this implies an explicit family of binary insdel codes that can correct ε fraction of insertions and deletions with rate 1-O(ε log 2 1/(1-ε))=1-Õ(ε). This significantly improves the previous best known result which only achieves rate 1-Õ(√ε) [11], [10], and is optimal up to a log (1/ε) factor. 1) An explicit binary insdel code which encodes an n-bit message x against k errors with redundancy O(k log n). This significantly improves the previous best known result of [4], which only works for constant k and has redundancy O(k 2 log k log n); and that of [2], which has redundancy O(k 2 + k log 2 n). Our code has optimal redundancy for k ≤ n 1-α , any constant 0 <; α <; 1. This is the first explicit construction of binary insdel codes that has optimal redundancy for a wide range of error parameters k, and this brings our understanding of binary insdel codes much closer to that of standard binary error correcting codes. In obtaining our results we introduce several new techniques. Most notably, we introduce the notion of ε-self matching hash functions and ε-synchronization hash functions. We believe our techniques can have further applications in the literature.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00028"}, {"primary_key": "3407044", "vector": [], "sparse_vector": [], "title": "Spatial Isolation Implies Zero Knowledge Even in a Quantum World.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Zero knowledge plays a central role in cryptography and complexity. The seminal work of <PERSON><PERSON><PERSON> et al. (STOC 1988) shows that zero knowledge can be achieved unconditionally for any language in NEXP, as long as one is willing to make a suitable physical assumption: if the provers are spatially isolated, then they can be assumed to be playing independent strategies. Quantum mechanics, however, tells us that this assumption is unrealistic, because spatially-isolated provers could share a quantum entangled state and realize a non-local correlated strategy. The MIP* model captures this setting. In this work we study the following question: does spatial isolation still suffice to unconditionally achieve zero knowledge even in the presence of quantum entanglement? We answer this question in the affirmative: we prove that every language in NEXP has a 2-prover zero knowledge interactive proof that is sound against entangled provers; that is, NEXP ⊆ ZK-MIP*. Our proof consists of constructing a zero knowledge interactive PCP with a strong algebraic structure, and then lifting it to the MIP* model. This lifting relies on a new framework that builds on recent advances in low-degree testing against entangled strategies, and clearly separates classical and quantum tools. Our main technical contribution is the development of new algebraic techniques for obtaining unconditional zero knowledge; this includes a zero knowledge variant of the celebrated sumcheck protocol, a key building block in many probabilistic proof systems. A core component of our sumcheck protocol is a new algebraic commitment scheme, whose analysis relies on algebraic complexity theory.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00077"}, {"primary_key": "3407045", "vector": [], "sparse_vector": [], "title": "A Near-Optimal Depth-Hierarchy Theorem for Small-Depth Multilinear Circuits.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the size blow-up that is necessary to convert an algebraic circuit of product-depth Δ + 1 to one of product-depth Δ in the multilinear setting. We show that for every positive Δ = Δ(n) = o(log n/log log n), there is an explicit multilinear polynomial P(Δ) on n variables that can be computed by a multilinear formula of product-depth Δ + 1 and size O(n), but not by any multilinear circuit of product-depth Δ and size less than exp(nΩ(1/Δ)). This result is tight up to the constant implicit in the double exponent for all Δ = o(log n/log log n). This strengthens a result of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (Computational Complexity 2009) who prove a quasipolynomial separation for constant-depth multilinear circuits, and a result of <PERSON><PERSON>, <PERSON> and <PERSON><PERSON> (STACS 2016) who give an exponential separation in the case Δ = 1. Our separating examples may be viewed as algebraic analogues of variants of the Graph Reachability problem studied by <PERSON>, <PERSON>, <PERSON> and <PERSON> (STOC 2016), who used them to prove lower bounds for constant-depth Boolean circuits.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00092"}, {"primary_key": "3407046", "vector": [], "sparse_vector": [], "title": "Testing Graph Clusterability: Algorithms and Lower Bounds.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of testing graph cluster structure: given access to a graph G = (V, E), can we quickly determine whether the graph can be partitioned into a few clusters with good inner conductance, or is far from any such graph? This is a generalization of the well-studied problem of testing graph expansion, where one wants to distinguish between the graph having good expansion (i.e. being a good single cluster) and the graph having a sparse cut (i.e. being a union of at least two clusters). A recent work of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (STOC'15) gave an ingenious sublinear time algorithm for testing k-clusterability in time Õ(n^1/2 poly(k)). Their algorithm implicitly embeds a random sample of vertices of the graph into Euclidean space, and then clusters the samples based on estimates of Euclidean distances between the points. This yields a very efficient testing algorithm, but only works if the cluster structure is very strong: it is necessary to assume that the gap between conductances of accepted and rejected graphs is at least logarithmic in the size of the graph G. In this paper we show how one can leverage more refined geometric information, namely angles as opposed to distances, to obtain a sublinear time tester that works even when the gap is a sufficiently large constant. Our tester is based on the singular value decomposition of a natural matrix derived from random walk transition probabilities from a small sample of seed nodes. We complement our algorithm with a matching lower bound on the query complexity of testing clusterability. Our lower bound is based on a novel property testing problem, which we analyze using Fourier analytic tools. As a byproduct of our techniques, we also achieve new lower bounds for the problem of approximating MAX-CUT value in sublinear time.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00054"}, {"primary_key": "3407047", "vector": [], "sparse_vector": [], "title": "Graph Sparsification, Spectral Sketches, and Faster Resistance Computation, via Short Cycle Decompositions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang"], "summary": "We develop a framework for graph sparsification based on a new tool, short cycle decomposition for graphs - a decomposition of a graph into a collection of short cycles, plus a small number of extra edges. A simple observation gives that every graph G on n vertices with m edges can be decomposed in O(mn) time into cycles of length at most 2 log n, and at most 2n extra edges. We give an m 1+o(1) time algorithm for constructing a short cycle decomposition of the graph, with cycles of length n o(1) , and n 1+o(1) extra edges. Both the existential and algorithmic variants of this decomposition enable us to make progress on several open problems in randomized graph algorithms. 1. We present an algorithm that runs in time m 1+o(1) ε -1.5 and returns (1 ± ε)-approximations to effective resistances of all edges, improving over the previous best of Õ(min{mε -2 , n 2 ε -1 }) This gives an algorithm to approximate the determinant of a graph Laplacian up to a factor of (1 ± ε) in roughly m + n 15/8 ε -7/4 . 2. We show existence and efficient algorithms for constructing graphical spectral sketches - a distribution over sparse graphs H with about nε -1 edges such that for a fixed vector x, we have x T L H x = (1 ± eps) x T L G x and x T L+ H x = (1 ± ε) x T L+ G x with high probability, where L is the graph Laplacian and L+ is its pseudoinverse. This implies resistance-sparsifiers with about nε edges that preserve the effective resistances between every pair of vertices up to (1 + eps). 3. By combining short cycle decomposition with importance sampling, we show the existence of nearly-linear sized degree-preserving spectral sparsifiers, as well as significantly sparser approximations of directed graphs. The latter is critical to recent breakthroughs on faster algorithms for directed random walks and linear systems in directed Laplacian. The running time and output qualities of our spectral sketch and degree-preserving (directed) sparsification algorithms are limited by the efficiency of our routines for producing short cycle decompositions. Improved algorithms for short cycle decompositions will lead to improvements for each of these algorithms.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00042"}, {"primary_key": "3407048", "vector": [], "sparse_vector": [], "title": "Solving Directed Laplacian Systems in Nearly-Linear Time through Sparse LU Factorizations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we show how to solve directed Laplacian systems in nearly-linear time. Given a linear system in an n × n Eulerian directed Laplacian with m nonzero entries, we show how to compute an ε-approximate solution in time O(m log O(1) (n) log (1/ε)). Through reductions from [<PERSON> et al. FOCS'16], this gives the first nearly-linear time algorithms for computing ε-approximate solutions to row or column diagonally dominant linear systems (including arbitrary directed Laplacians) and computing ε-approximations to various properties of random walks on directed graphs, including stationary distributions, personalized PageRank vectors, hitting times, and escape probabilities. These bounds improve upon the recent almost-linear algorithms of [<PERSON> et al. STOC'17], which gave an algorithm to solve Eulerian Laplacian systems in time O((m+n2 O(√ log n log log n) )log O(1) (n ε -1 )). To achieve our results, we provide a structural result that we believe is of independent interest. We show that Eulerian Laplacians (and therefore the Laplacians of all strongly connected directed graphs) have sparse approximate LU-factorizations. That is, for every such directed Laplacian there are lower upper triangular matrices each with at most Õ(n) nonzero entries such that there product spectrally approximates the directed Laplacian in an appropriate norm. This claim can be viewed as an analog of recent work on sparse Cholesky factorizations of Laplacians of undirected graphs. We show how to construct such factorizations in nearly-linear time and prove that once constructed they yield nearly-linear time algorithms for solving directed Laplacian systems.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00089"}, {"primary_key": "3407049", "vector": [], "sparse_vector": [], "title": "Balancing Vectors in Any Norm.", "authors": ["<PERSON>", "Aleksan<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the vector balancing problem, we are given symmetric convex bodies C and K in R^n, and our goal is to determine the minimum number β ≥ 0, known as the vector balancing constant from C to K, such that for any sequence of vectors in C there always exists a signed combination of them lying inside β K. Many fundamental results in discrepancy theory, such as the Beck<PERSON><PERSON><PERSON> theorem (Discrete Appl.~Math '81), <PERSON>'s \"six standard deviations suffice\" theorem (Trans.~Amer.~Math.~Soc '85) and <PERSON><PERSON><PERSON><PERSON><PERSON>'s vector balancing theorem (Random Structures & Algorithms '98) correspond to bounds on vector balancing constants. The above theorems have inspired much research in recent years within theoretical computer science. In this work, we show that all vector balancing constants admit \"good\" approximate characterizations, with approximation factors depending only polylogarithmically on the dimension n. First, we show that a volumetric lower bound due to <PERSON><PERSON><PERSON><PERSON><PERSON> is tight within a O(log n) factor. Our proof is algorithmic, and we show that <PERSON><PERSON><PERSON>'s (FOCS '14) partial coloring algorithm can be analyzed to obtain these guarantees. Second, we present a novel convex program which encodes the \"best possible way\" to apply <PERSON><PERSON><PERSON><PERSON><PERSON>'s vector balancing theorem for bounding vector balancing constants from above, and show that it is tight within an O(log^2.5 n) factor. This also directly yields a corresponding polynomial time approximation algorithm both for vector balancing constants, and for the hereditary discrepancy of any sequence of vectors with respect to an arbitrary norm.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00010"}, {"primary_key": "3407050", "vector": [], "sparse_vector": [], "title": "Efficient Statistics, in High Dimensions, from Truncated Samples.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Manolis Zampetakis"], "summary": "We provide an efficient algorithm for the classical problem, going back to <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, of estimating, with arbitrary accuracy the parameters of a multivariate normal distribution from truncated samples. Truncated samples from a d-variate normal N(mu, Sigma) means a samples is only revealed if it falls in some subset S of the d-dimensional Euclidean space; otherwise the samples are hidden and their count in proportion to the revealed samples is also hidden. We show that the mean mu and covariance matrix Sigma can be estimated with arbitrary accuracy in polynomial-time, as long as we have oracle access to S, and S has non-trivial measure under the unknown d-variate normal distribution. Additionally we show that without oracle access to S, any non-trivial estimation is impossible.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00067"}, {"primary_key": "3407051", "vector": [], "sparse_vector": [], "title": "Learning Sums of Independent Random Variables with Sparse Collective Support.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the learnability of sums of independent integer random variables given a bound on the size of the union of their supports. For a A ⊂Z + ubset A of non-negative integers, a sum of independent random variables with collective support A (called an \"A-sum\" in this paper) is a distribution S = X 1 + ... + X N where the X i 's are mutually independent (but not necessarily identically distributed) integer random variables all of whose supports are contained in A. We give two main algorithmic results for learning such distributions: 1) For the case |A|=3, we give an algorithm for learning A-sums to accuracy ε that uses poly(1/ε) samples and runs in time poly(1/ε), independent of N and of the elements of A. 2) For an arbitrary constant k>=4, if A = {a 1 ,...,a k } with 01 k , we give an algorithm that uses poly(1/ε)*log log a k samples (independent of N) and runs in time poly(1/ε, log a k ). We prove an essentially matching lower bound: if |A| = 4, then any algorithm must use Ω(log log a 4 ) samples even for learning to constant accuracy. We also give similar-in-spirit (but quantitatively very different) algorithmic results, and essentially matching lower bounds, for the case in which A is not known to the learner. Our learning algorithms employ new limit theorems which may be of independent interest. Our algorithms and lower bounds together settle the question of how the sample complexity of learning sums of independent integer random variables scales with the elements in the union of their supports, both in the known-support and unknown-support settings. Finally, all our algorithms easily extend to the \"semi-agnostic\" learning model, in which training data is generated from a distribution that is only c*ε-close to some A-sum for a constant c>0.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00036"}, {"primary_key": "3407052", "vector": [], "sparse_vector": [], "title": "Approximating the Permanent of a Random Matrix with Vanishing Mean.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The permanent is #P-hard to compute exactly on average for natural random matrices including matrices over finite fields or Gaussian ensembles. Should we expect that it remains #P-hard to compute on average if we only care about approximation instead of exact computation? In this work we take a first step towards resolving this question: We present a quasi-polynomial time deterministic algorithm for approximating the permanent of a typical n × n random matrix with unit variance and vanishing mean μ = O(ln ln n) -1/8 to within inverse polynomial multiplicative error. (alternatively, one can achieve permanent approximation for matrices with mean μ = 1/polylog(n) in time 2 n(ε) , for arbitrarily small ε>0). The proposed algorithm significantly extends the regime of matrices for which efficient approximation of the permanent is known. This is because unlike previous algorithms which require a stringent correlation between the signs of the entries of the matrix [1], [2] it can tolerate random ensembles in which this correlation is negligible (albeit non-zero). Among important special cases we note: 1) Biased Gaussian: each entry is a complex Gaussian with unit variance 1 and mean μ. 2) Biased <PERSON>: each entry is -1 + μ with probability 1/2, and 1 with probability 1/2. These results counter the common intuition that the difficulty of computing the permanent, even approximately, stems merely from our inability to treat matrices with many opposing signs. The Gaussian ensemble approaches the threshold of a conjectured hardness [3] of computing the permanent of a zero mean Gaussian matrix. This conjecture is one of the baseline assumptions of the BosonSampling paradigm that has received vast attention in recent years in the context of quantum supremacy experiments. We furthermore show that the permanent of the biased Gaussian ensemble is #P-hard to compute exactly on average. To our knowledge, this is the first natural example of a counting problem that becomes easy only when average case analysis and approximation are combined. On a technical level, our approach stems from a recent approach taken by Barvinok [1], [4], [5], [6] who used Taylor series approximation of the logarithm of a certain univariate polynomial related to the permanent. Our main contribution is to introduce an average-case analysis of such related polynomials. We complement our approach with a new technique for iteratively computing a Taylor series approximation of a function that is analytical in the vicinity of a curve in the complex plane. This method can be viewed as a computational version of analytic continuation in complex analysis.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00012"}, {"primary_key": "3407053", "vector": [], "sparse_vector": [], "title": "Metric Sublinear Algorithms via Linear Sampling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We provide a new technique to design fast approximation algorithms for graph problems where the points of the graph lie in a metric space. Specifically, we present a sampling approach for such metric graphs that, using a sublinear number of edge weight queries, provides a linear sampling, where each edge is (roughly speaking) sampled proportionally to its weight. For several natural problems, such as densest subgraph and max cut, we show that by sparsifying the graph using this sampling process, we can run a suitable approximation algorithm on the sparsified graph and the result remains a good approximation for the original problem. Our results have several interesting implications, such as providing the first sublinear time approximation algorithm for densest subgraph in a metric space, and improving the running time of estimating the average distance.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00011"}, {"primary_key": "3407054", "vector": [], "sparse_vector": [], "title": "Graph Sketching against Adaptive Adversaries Applied to the Minimum Degree Algorithm.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON>"], "summary": "Motivated by the study of matrix elimination orderings in combinatorial scientific computing, we utilize graph sketching and local sampling to give a data structure that provides access to approximate fill degrees of a matrix undergoing elimination in polylogarithmic time per elimination and query. We then study the problem of using this data structure in the minimum degree algorithm, which is a widely-used heuristic for producing elimination orderings for sparse matrices by repeatedly eliminating the vertex with (approximate) minimum fill degree. This leads to a nearly-linear time algorithm for generating approximate greedy minimum degree orderings. Despite extensive studies of algorithms for elimination orderings in combinatorial scientific computing, our result is the first rigorous incorporation of randomized tools in this setting, as well as the first nearly-linear time algorithm for producing elimination orderings with provable approximation guarantees. While our sketching data structure readily works in the oblivious adversary model, by repeatedly querying and greedily updating itself, it enters the adaptive adversarial model where the underlying sketches become prone to failure due to dependency issues with their internal randomness. We show how to use an additional sampling procedure to circumvent this problem and to create an independent access sequence. Our technique for decorrelating interleaved queries and updates to this randomized data structure may be of independent interest.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00019"}, {"primary_key": "3407055", "vector": [], "sparse_vector": [], "title": "Constant Overhead Quantum Fault-Tolerance with Quantum Expander Codes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove that quantum expander codes can be combined with quantum fault-tolerance techniques to achieve constant overhead: the ratio between the total number of physical qubits required for a quantum computation with faulty hardware and the number of logical qubits involved in the ideal computation is asymptotically constant, and can even be taken arbitrarily close to 1 in the limit of small physical error rate. This improves on the polylogarithmic overhead promised by the standard threshold theorem. To achieve this, we exploit a framework introduced by <PERSON><PERSON><PERSON> together with a family of constant rate quantum codes, quantum expander codes. Our main technical contribution is to analyze an efficient decoding algorithm for these codes and prove that it remains robust in the presence of noisy syndrome measurements, a property which is crucial for fault-tolerant circuits. We also establish two additional features of the decoding algorithm that make it attractive for quantum computation: it can be parallelized to run in logarithmic depth, and is single-shot, meaning that it only requires a single round of noisy syndrome measurement.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00076"}, {"primary_key": "3407056", "vector": [], "sparse_vector": [], "title": "Privacy Amplification by Iteration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Many commonly used learning algorithms work by iteratively updating an intermediate solution using one or a few data points in each iteration. Analysis of differential privacy for such algorithms often involves ensuring privacy of each step and then reasoning about the cumulative privacy cost of the algorithm. This is enabled by composition theorems for differential privacy that allow releasing of all the intermediate results. In this work, we demonstrate that for contractive iterations, not releasing the intermediate results strongly amplifies the privacy guarantees. We describe several applications of this new analysis technique to solving convex optimization problems via noisy stochastic gradient descent. For example, we demonstrate that a relatively small number of non-private data points from the same distribution can be used to close the gap between private and non-private convex optimization. In addition, we demonstrate that we can achieve guarantees similar to those obtainable using the privacy-amplification-by-sampling technique in several natural settings where that technique cannot be applied.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00056"}, {"primary_key": "3407057", "vector": [], "sparse_vector": [], "title": "An End-to-End Argument in Mechanism Design (Prior-Independent Auctions for Budgeted Agents).", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This paper considers prior-independent mechanism design, namely identifying a single mechanism that has near optimal performance on every prior distribution. We show that mechanisms with truthtelling equilibria, a.k.a., revelation mechanisms, do not always give optimal prior-independent mechanisms and we define the revelation gap to quantify the non-optimality of revelation mechanisms. This study suggests that it is important to develop a theory for the design of non-revelation mechanisms. Our analysis focuses on welfare maximization by single-item auctions for agents with budgets and a natural regularity assumption on their distribution of values. The all-pay auction (a non-revelation mechanism) is the Bayesian optimal mechanism; as it is prior-independent it is also the prior-independent optimal mechanism (a 1-approximation). We prove a lower bound on the prior-independent approximation of revelation mechanisms of 1.013 and that the clinching auction (a revelation mechanism) is a prior-independent e ≈ 2.714 approximation. Thus the revelation gap for single-item welfare maximization with public budget agents is in [1.013, e]. Some of our analyses extend to the revenue objective, position environments, and irregular distributions.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00046"}, {"primary_key": "3407058", "vector": [], "sparse_vector": [], "title": "1-Factorizations of Pseudorandom Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A 1-factorization of a graph G is a collection of edge-disjoint perfect matchings whose union is E(G). A trivial necessary condition for <PERSON> to admit a 1-factorization is that |V (G)| is even and G is regular; the converse is easily seen to be false. In this paper, we consider the problem of finding 1-factorizations of regular, pseudorandom graphs. Specifically, we prove that for any ϵ > 0, an (n, d, λ)-graph G (that is, a d-regular graph on n vertices whose second largest eigenvalue in absolute value is at most λ) admits a 1-factorization provided that n is even, C 0 ≤ d ≤ n-1 (where C 0 = C 0 (∈) is a constant depending only on ∈), and λ ≤ d 1-∈ . In particular, since (as is well known) a typical random d-regular graph G n,d is such a graph, we obtain the existence of a 1-factorization in a typical G n,d for all C 0 ≤ d ≤ n - 1, thereby extending to all possible values of d results obtained by <PERSON><PERSON>, and independently by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> for fixed d. Moreover, we also obtain a lower bound for the number of distinct 1-factorizations of such graphs G which is off by a factor of 2 in the base of the exponent from the known upper bound. This lower bound is better by a factor of 2 nd/2 than the previously best known lower bounds, even in the simplest case where G is the complete graph. Our proofs are probabilistic and can be easily turned into polynomial time (randomized) algorithms.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00072"}, {"primary_key": "3407059", "vector": [], "sparse_vector": [], "title": "Pseudorandom Generators for Read-Once Branching Programs, in Any Order.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A central question in derandomization is whether randomized logspace (RL) equals deterministic logspace (L). To show that RL = L, it suffices to construct explicit pseudorandom generators (PRGs) that fool polynomial-size read-once (oblivious) branching programs (roBPs). Starting with the work of <PERSON><PERSON> [Nis92], pseudorandom generators with seedlength O(log 2 n) were constructed (see also [INW94], [GR14]). Unfortunately, improving on this seed-length in general has proven challenging and seems to require new ideas. A recent line of inquiry (e.g., [BV10], [GMR+12], [IMZ12], [RSV13], [SVW14], [HLV17], [LV17], [CHRT17]) has suggested focusing on a particular limitation of the existing PRGs ([Nis92], [INW94], [GR14]), which is that they only fool roBPs when the variables are read in a particular known order, such as x 1 n . In comparison, existentially one can obtain logarithmic seed-length for fooling the set of polynomial-size roBPs that read the variables under any fixed unknown permutation x π(1) xπ(n) . While recent works have established novel PRGs in this setting for subclasses of roBPs, there were no known n o(1) seed-length explicit PRGs for general polynomial-size roBPs in this setting. In this work, we follow the \"bounded independence plus noise\" paradigm of Haramaty, <PERSON> and <PERSON> [HLV17], [LV17], and give an improved analysis in the general roBP unknownorder setting. With this analysis we obtain an explicit PRG with seed-length O(log 3 n) for polynomial-size roBPs reading their bits in an unknown order. Plugging in a recent Fourier tail bound of Chattopadhyay, Hatami, Reingold, and Tal [CHRT17], we can obtain a Õ(log 2 n) seed-length when the roBP is of constant width.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00093"}, {"primary_key": "3407060", "vector": [], "sparse_vector": [], "title": "A Faster Distributed Single-Source Shortest Paths Algorithm.", "authors": ["<PERSON>", "Danupon <PERSON>"], "summary": "We devise new algorithms for the single-source shortest paths (SSSP) problem with non-negative edge weights in the CONGEST model of distributed computing. While close-to-optimal solutions, in terms of the number of rounds spent by the algorithm, have recently been developed for computing SSSP approximately, the fastest known exact algorithms are still far away from matching the lower bound of Ω (n + D) rounds by <PERSON><PERSON><PERSON> and <PERSON> [SIAM Journal on Computing 2000], where n is the number of nodes in the network and D is its diameter. The state of the art is <PERSON><PERSON>'s randomized algorithm [STOC 2017] that performs Õ(n^2/3 D^1/3 + n^5/6) rounds. We significantly improve upon this upper bound with our two new randomized algorithms for polynomially bounded integer edge weights, the first performing Õ(√n D) rounds and the second performing Õ(√n D^1/4 + n^3/5 + D) rounds. Our bounds also compare favorably to the independent result by <PERSON><PERSON><PERSON><PERSON> and <PERSON> [STOC 2018]. As side results, we obtain a (1+ε)-approximation Õ((√n D^1/4+D)/ε)-round algorithm for directed SSSP and a new work/depth trade-off for exact SSSP on directed graphs in the PRAM model.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00071"}, {"primary_key": "3407061", "vector": [], "sparse_vector": [], "title": "On Derandomizing Local Distributed Algorithms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The gap between the known randomized and deterministic local distributed algorithms underlies arguably the most fundamental and central open question in distributed graph algorithms. In this paper, we combine the method of conditional expectation with network decompositions to obtain a generic and clean recipe for derandomizing LOCAL algorithms. This leads to significant improvements on a number of problems, in cases resolving known open problems. Two main results are: - An improved deterministic distributed algorithm for hypergraph maximal matching, improving on <PERSON>, <PERSON><PERSON>, and <PERSON> [FOCS '17]. This yields improved algorithms for edge-coloring, maximum matching approximation, and low out-degree edge orientation. The last result gives the first positive resolution in the Open Problem 11.10 in the book of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>. - Improved randomized and deterministic distributed algorithms for the Lovász Local Lemma, which get closer to a conjecture of <PERSON> and <PERSON><PERSON> [FOCS '17].", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00069"}, {"primary_key": "3407062", "vector": [], "sparse_vector": [], "title": "Counting t-Cliques: Worst-Case to Average-Case Reductions and Direct Interactive Proof Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study two aspects of the complexity of counting the number of t-cliques in a graph: 1) Worst-case to average-case reductions: Our main result reduces counting t-cliques in any n-vertex graph to counting t-cliques in typical n-vertex graphs that are drawn from a simple distribution of min-entropy Ω(n2). For any constant t, the reduction runs in O(n2)-time, and yields a correct answer (w.h.p.) even when the \"average-case solver\" only succeeds with probability 1/poly(log n). 2) Direct interactive proof systems: We present a direct and simple interactive proof system for counting t-cliques in n-vertex graphs. The proof system uses t - 2 rounds, the verifier runs in O(t2n2)-time, and the prover can be implemented in O(tO(1) · n2)-time when given oracle access to counting (t - 1)-cliques in O(tO(1) · n)-vertex graphs. The results are both obtained by considering weighted versions of the t-clique problem, where weights are assigned to vertices and/or to edges, and the weight of cliques is defined as the product of the corresponding weights. These weighted problems are shown to be easily reducible to the unweighted problem.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00017"}, {"primary_key": "3407063", "vector": [], "sparse_vector": [], "title": "The Sample Complexity of Up-to-ε Multi-Dimensional Revenue Maximization.", "authors": ["Yannai <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the sample complexity of revenue maximization for multiple bidders in unrestricted multi-dimensional settings. Specifically, we study the standard model of n additive bidders whose values for m heterogeneous items are drawn independently. For any such instance and any ε>0, we show that it is possible to learn an ε-Bayesian Incentive Compatible auction whose expected revenue is within ε of the optimal ε-BIC auction from only polynomially many samples. Our approach is based on ideas that hold quite generally, and completely sidestep the difficulty of characterizing optimal (or near-optimal) auctions for these settings. Therefore, our results easily extend to general multi-dimensional settings, including valuations that aren't necessarily even subadditive, and arbitrary allocation constraints. For the cases of a single bidder and many goods, or a single parameter (good) and many bidders, our analysis yields exact incentive compatibility (and for the latter also computational efficiency). Although the single-parameter case is already well-understood, our corollary for this case extends slightly the state-of-the-art.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00047"}, {"primary_key": "3407064", "vector": [], "sparse_vector": [], "title": "Near-Optimal Communication Lower Bounds for Approximate Nash Equilibria.", "authors": ["<PERSON><PERSON>", "<PERSON>via<PERSON>"], "summary": "We prove an N 2-o(1) lower bound on the randomized communication complexity of finding an ε-approximate Nash equilibrium (for constant ε>0) in a two-player N×N game.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00045"}, {"primary_key": "3407065", "vector": [], "sparse_vector": [], "title": "Indistinguishability by Adaptive Procedures with Advice, and Lower Bounds on Hardness Amplification Proofs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study how well can q-query decision trees distinguish between the following two distributions: (i) R = (R 1 ,...,R N ) that are i.i.d. indicator random variables, (ii) X=(R|R ϵ A) where A is an event s.t. Pr[R ϵ A] ≥ 2 -a . We prove two lemmas: · Forbidden-set lemma: There exists B ⊆ [N] of size poly(a, q, 1/η) such that q-query trees that do not query variables in B cannot distinguish X from R with advantage η. · Fixed-set lemma: There exists B ⊆ [N] of size poly(a, q,1/η) and v ϵ0,1 B such that q-query trees do not distinguish (X|X B =v) from (R|R B =v) with advantage η. The first can be seen as an extension of past work by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (Computational Complexity 2001), <PERSON><PERSON> (SICOMP 1998), and <PERSON><PERSON><PERSON> and <PERSON> (SICOMP 2010) to adaptive decision trees. It is independent of recent work by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (ECCC 2017) bounding the number of i ϵ [N] for which there exists a q-query tree that predicts X i from the other bits. We use the second, fixed-set lemma to prove lower bounds on black-box proofs for hardness amplification that amplify hardness from δ to 1/2-ϵ. Specifically: · Reductions must make q=Ω(log(1/δ)/ϵ 2 ) queries, implying a \"size loss factor\" of q. We also prove the lower bound q=Ω(log(1/δ)/ϵ) for \"error-less\" hardness amplification proofs, and for direct-product lemmas. These bounds are tight. · Reductions can be used to compute Majority on Ω(1/ϵ) bits, implying that black box proofs cannot amplify hardness of functions that are hard against constant depth circuits (unless they are allowed to use Majority gates). Both items extend to pseudorandom-generator constructions. These results prove 15-year-old conjectures by Viola, and improve on three incomparable previous works (Shaltiel and Viola, SICOMP 2010; Gutfreund and Rothblum, RANDOM 2008; Artemenko and Shaltiel, Computational Complexity 2014).", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00094"}, {"primary_key": "3407066", "vector": [], "sparse_vector": [], "title": "A Faster Isomorphism Test for Graphs of Small Degree.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In a recent breakthrough, <PERSON><PERSON> (STOC 2016) gave quasipolynomial graph isomorphism test. In this work, we give an improved isomorphism test for graphs of small degree: our algorithms runs in time n^O((log d)^c), where n is the number of vertices of the input graphs, d is the maximum degree of the input graphs, and c is an absolute constant. The best previous isomorphism test for graphs of maximum degree d due to <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (FOCS 1983) runs in time n^O(d log d).", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00018"}, {"primary_key": "3407067", "vector": [], "sparse_vector": [], "title": "Faster Exact and Approximate Algorithms for k-Cut.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>"], "summary": "In the k-cut problem, we are given an edge-weighted graph G and an integer k, and have to remove a set of edges with minimum total weight so that G has at least k connected components. The current best algorithms are an O(n (2-o(1))k ) randomized algorithm due to <PERSON><PERSON> and <PERSON>, and an Õ(n 2k ) deterministic algorithm due to <PERSON><PERSON>. Moreover, several 2-approximation algorithms are known for the problem (due to <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, and <PERSON> and <PERSON>). It has remained an open problem to (a) improve the runtime of exact algorithms, and (b) to get better approximation algorithms. In this paper we show an O(k O(k) n (2Ω/3 + o(1))k )-time algorithm for k-cut. Moreover, we show an (1+ε)-approximation algorithm that runs in time O((k/ε) O(k) n k + O(1) ), and a 1.81-approximation in fixed-parameter time O(2 O(k(2)) poly(n)).", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00020"}, {"primary_key": "3407068", "vector": [], "sparse_vector": [], "title": "Quantum Algorithm for Simulating Real Time Evolution of Lattice Hamiltonians.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of simulating the time evolution of a lattice Hamiltonian, where the qubits are laid out on a lattice and the Hamiltonian only includes geometrically local interactions (i.e., a qubit may only interact with qubits in its vicinity). This class of Hamiltonians is very general and encompasses all physically reasonable Hamiltonians. Our algorithm simulates the time evolution of such a Hamiltonian on n qubits for time T up to error ε using O(T polylog(nT/ε)) gates with depth O(T polylog(nT/ε)). Our algorithm is the first simulation algorithm that achieves gate cost quasilinear in nT and polylogarithmic in 1/ε. Our algorithm also readily generalizes to time-dependent Hamiltonians and yields an algorithm with similar gate count for any piecewise slowly varying time-dependent bounded local Hamiltonian. We also prove a matching lower bound on the gate count of such a simulation, showing that any quantum algorithm that can simulate a piecewise constant bounded local Hamiltonian in one dimension to constant error requires (nT) gates in the worst case. The lower bound holds even if we only require the output state to be correct on local measurements. To our best knowledge, this is the first nontrivial lower bound on the gate complexity of the simulation problem. Our algorithm is based on a decomposition of the time-evolution unitary into a product of small unitaries using Lieb-Robinson bounds. In the appendix, we prove a Lie<PERSON>-<PERSON> bound tailored to Hamiltonians with small commutators between local terms, giving zero Lieb-Robinson velocity in the limit of commuting Hamiltonians. This improves the performance of our algorithm when the Hamiltonian is close to commuting.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00041"}, {"primary_key": "3407069", "vector": [], "sparse_vector": [], "title": "Computational Two-Party Correlation: A Dichotomy for Key-Agreement Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let π be an efficient two-party protocol that given security parameter k, both parties output single bits X k and Y k , respectively. We are interested in how (X k , Y k ) \"appears\" to an efficient adversary that only views the transcript T k . We make the following contributions: · We develop new tools to argue about this loose notion, and show (modulo some caveats) that for every such protocol π, there exists an efficient simulator such that the following holds: on input T k , the simulator outputs a pair (X' k , Y' k ) such that (X' k , Y' k , T k ) is (somewhat) computationally indistinguishable from (X k , Y k , T k ). · We use these tools to prove the following dichotomy theorem: every such protocol π is: - either uncorrelated - it is (somewhat) indistinguishable from an efficient protocol whose parties interact to produce T k , but then choose their outputs independently from some product distribution (that is determined in poly-time from T k ), - or, the protocol implies a key-agreement protocol (for infinitely many k's). Uncorrelated protocols are uninteresting from a cryptographic viewpoint, as the correlation between outputs is (computationally) trivial. Our dichotomy shows that every protocol is either completely uninteresting or implies key-agreement. ·We use the above dichotomy to make progress on open problems on minimal cryptographic assumptions required for differentially private mechanisms for the XOR function. · A subsequent work of <PERSON><PERSON><PERSON> et al. uses the above dichotomy to makes progress on a long-standing open question regarding the complexity of fair two-party coin-flipping protocols. We highlight the following ideas regarding our technique: · The simulator algorithm is obtained by a carefully designed \"competition\" between efficient algorithms attempting to forecast ((X k , Y k )|T k = t). The winner is used to simulate the outputs of the protocol. · Our key-agreement protocol uses the simulation to reduce to an information theoretic setup, and is in some sense non-black box.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00022"}, {"primary_key": "3407070", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> Prize Lecture: On the Difficulty of Approximating Boolean Max-CSPs.", "authors": ["<PERSON>"], "summary": "This is the <PERSON><PERSON><PERSON> lecture. We discuss the approximability of Boolean Constraint Satisfaction Problems (CSPs). In this situation we are given a large number of constraints, each of the form of a fixed predicate P applied to a sequence of literals. The goal is to find an assignment that satisfies the maximum number of constrains.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00063"}, {"primary_key": "3407071", "vector": [], "sparse_vector": [], "title": "Non-Black-Box Worst-Case to Average-Case Reductions within NP.", "authors": ["<PERSON><PERSON>"], "summary": "There are significant obstacles to establishing an equivalence between the worst-case and average-case hardness of NP: Several results suggest that black-box worst-case to averagecase reductions are not likely to be used for reducing any worstcase problem outside coNP to a distributional NP problem. This paper overcomes the barrier. We present the first nonblack-box worst-case to average-case reduction from a problem outside coNP (unless Random 3SAT is easy for coNP algorithms) to a distributional NP problem. Specifically, we consider the minimum time-bounded Kolmogorov complexity problem (MINKT), and prove that there exists a zero-error randomized polynomial-time algorithm approximating the minimum time bounded Kolmogorov complexity k within an additive error ̅O(√ k) if its average-case version admits an errorless heuris tic polynomial-time algorithm. (The converse direction also holds under a plausible derandomization assumption.) We also show that, given a truth table of size 2 n approximating the minimum circuit size within a factor of 2( 1-ε jn is in BPP for some constant € > 0 if and only if its average-case version is easy. Based on our results, we propose a research program for excluding Heuristica, i.e., establishing an equivalence between the worst-case and average-case hardness of NP through the lens of MINKT or the Minimum Circuit Size Problem (MCSP).", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00032"}, {"primary_key": "3407072", "vector": [], "sparse_vector": [], "title": "Cryptographic Hashing from Strong One-Way Functions (Or: One-Way Product Functions and Their Applications).", "authors": ["<PERSON>", "<PERSON>"], "summary": "Constructing collision-resistant hash families (CRHFs) from one-way functions is a long-standing open problem and source of frustration in theoretical cryptography. In fact, there are strong negative results: black-box separations from one-way functions that are 2 -(1-0(1))n -secure against polynomial time adversaries (<PERSON>, EUROCRYPT '98) and even from indistinguishability obfuscation (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, FOCS '15). In this work, we formulate a mild strengthening of exponentially secure one-way functions, and we construct CRHFs from such functions. Specifically, our security notion requires that every polynomial time algorithm has at most 2 -n · negl(n) probability of inverting two independent challenges. More generally, we consider the problem of simultaneously inverting k functions f 1 ,.. . , f k , which we say constitute a \"one-way product function\" (OWPF). We show that sufficiently hard OWPFs yield hash families that are multi-input correlation intractable (<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, STOC '98) with respect to all sparse (bounded arity) output relations. Additionally assuming indistinguishability obfuscation, we construct hash families that achieve a broader notion of correlation intractability, extending the recent work of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (CRYPTO '17). In particular, these families are sufficient to instantiate the <PERSON>-<PERSON><PERSON><PERSON> heuristic in the plain model for a natural class of interactive proofs. An interesting consequence of our results is a potential new avenue for bypassing black-box separations. In particular, proving (with necessarily non-black-box techniques) that parallel repetition amplifies the hardness of specific one-way functions - for example, all oneway permutations - suffices to directly bypass Simon's impossibility result.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00085"}, {"primary_key": "3407073", "vector": [], "sparse_vector": [], "title": "Delegating Computations with (Almost) Minimal Time and Space Overhead.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The problem of verifiable delegation of computation considers a setting in which a client wishes to outsource an expensive computation to a powerful, but untrusted, server. Since the client does not trust the server, we would like the server to certify the correctness of the result. Delegation has emerged as a central problem in cryptography, with a flurry of recent activity in both theory and practice. In all of these works, the main bottleneck is the overhead incurred by the server, both in time and in space. Assuming (sub-exponential) LWE, we construct a one-round argument-system for proving the correctness of any time T and space S RAM computation, in which both the verifier and prover are highly efficient. The verifier runs in time n ⋅ polylog(T) and space polylog(T), where n is the input length. The prover runs in time quasilinear in T, in space S + o(S), and in some cases even space S + polylog(T). Our solution uses somewhat homomorphic encryption but, surprisingly, only requires homomorphic evaluation of arithmetic circuits having multiplicative depth (which is the main efficiency bottleneck in such schemes) that is lg(lg T)+O(1). Prior works based on standard assumptions had a poly(T) time prover, with an exponent of 3 at the very least. As for the space usage, we are unaware of any work, even based on non-standard assumptions, that has space usage S + polylog(T). Along the way to constructing our delegation scheme, we introduce several technical tools that we hope will be useful for future work.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00021"}, {"primary_key": "3407074", "vector": [], "sparse_vector": [], "title": "Simple Optimal Hitting Sets for Small-Success RL.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give a simple explicit hitting set generator for read-once branching programs of width w and length r with known variable order. When r = w, our generator has seed length O(log^2 r + log(1/ε)). When r = polylog w, our generator has optimal seed length O(log w + log(1/ε)). For intermediate values of r, our generator's seed length smoothly interpolates between these two extremes. Our generator's seed length improves on recent work by <PERSON><PERSON>, <PERSON>, and <PERSON> (STOC '18). In addition, our generator and its analysis are dramatically simpler than the work by <PERSON><PERSON> et al. Our generator's seed length improves on all the classic generators for space-bounded computation (Nisan Combinatorica '92; <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>igderson STOC '94; <PERSON><PERSON> and <PERSON><PERSON><PERSON> JCSS '96) when eps is small. As a corollary of our construction, we show that every RL algorithm that uses r random bits can be simulated by an NL algorithm that uses only O(r/log^c n) nondeterministic bits, where c is an arbitrarily large constant. Finally, we show that any RL algorithm with small success probability eps can be simulated deterministically in space O(log^3/2 n + log n log log(1/ε)). This improves on work by <PERSON><PERSON> and <PERSON> (JCSS '99), who gave an algorithm that runs in space O(log^3/2 n + sqrt(log n) log(1/ε)).", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00015"}, {"primary_key": "3407075", "vector": [], "sparse_vector": [], "title": "Epsilon-Coresets for Clustering (with Outliers) in Doubling Metrics.", "authors": ["<PERSON><PERSON><PERSON>", "Shaofeng H.-<PERSON><PERSON> Jiang", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of constructing ε-coresets for the (k, z)-clustering problem in a doubling metric M(X, d). An ε-coreset is a weighted subset S ⊆ X with weight function w : S → ℝ ≥0 , such that for any k-subset C ∈ [X] k , it holds that Σ x∈S w(x) · d z (x, C) ∈ (1 ± ε) · Σ x∈X d z (x, C). We present an efficient algorithm that constructs an ε-coreset for the (k, z)-clustering problem in M(X, d), where the size of the coreset only depends on the parameters k, z, ε and the doubling dimension ddim(M). To the best of our knowledge, this is the first efficient c-coreset construction of size independent of |X| for general clustering problems in doubling metrics. To this end, we establish the first relation between the doubling dimension of M(X, d) and the shattering dimension (or VC-dimension) of the range space induced by the distance d. Such a relation is not known before, since one can easily construct instances in which neither one can be bounded by (some function of) the other. Surprisingly, we show that if we allow a small (1 ± ε)-distortion of the distance function d (the distorted distance is called the smoothed distance function), the shattering dimension can be upper bounded by O(ε -O(ddim(M)) ). For the purpose of coreset construction, the above bound does not suffice as it only works for unweighted spaces. Therefore, we introduce the notion of τ-error probabilistic shattering dimension, and prove a (drastically better) upper bound of O(ddim(M)·log(1/ε)+log log 1/τ) for the probabilistic shattering dimension for weighted doubling metrics. As it turns out, an upper bound for the probabilistic shattering dimension is enough for constructing a small coreset. We believe the new relation between doubling and shattering dimensions is of independent interest and may find other applications. Furthermore, we study robust coresets for (k, z)-clustering with outliers in a doubling metric. We show an improved connection between α-approximation and robust coresets. This also leads to improvement upon the previous best known bound of the size of robust coreset for Euclidean space [Feldman and Langberg, STOC 11]. The new bound entails a few new results in clustering and property testing. As another application, we show constant-sized (ε, k, z)centroid sets in doubling metrics can be constructed by extending our coreset construction. Prior to our result, constantsized centroid sets for general clustering problems were only known for Euclidean spaces. We can apply our centroid set to accelerate the local search algorithm (studied in [Friggstad et al., FOCS 2016]) for the (k, z)-clustering problem in doubling metrics.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00082"}, {"primary_key": "3407076", "vector": [], "sparse_vector": [], "title": "0/1/All CSPs, Half-Integral A-Path Packing, and Linear-Time FPT Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A recent trend in the design of FPT algorithms is exploiting the half-integrality of LP relaxations. In other words, starting with a half-integral optimal solution to an LP relaxation, we assign integral values to variables one-by-one by branch and bound. This technique is general and the resulting time complexity has a low dependency on the parameter. However, the time complexity often becomes a large polynomial in the input size because we need to compute half-integral optimal LP solutions. In this paper, we address this issue by providing an O(km)-time algorithm for solving the LPs arising from various FPT problems, where k is the optimal value and m is the number of edges/constraints. Our algorithm is based on interesting connections among 0/1/all constraints, which has been studied in the field of constraints satisfaction, A-path packing, which has been studied in the field of combinatorial optimization, and the LPs used in FPT algorithms. With the aid of this algorithm, we obtain linear-time FPT algorithms for various problems. The obtained running time for each problem is linear in the input size and has the current smallest dependency on the parameter. Most importantly, instead of using problem-specific approaches, we obtain all of these results by a unified approach, i.e., the branch-and-bound framework combined with the efficient computation of half-integral LPs, which demonstrates its generality.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00051"}, {"primary_key": "3407077", "vector": [], "sparse_vector": [], "title": "Perfect Lp Sampling in a Data Stream.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we resolve the one-pass space complexity of L p sampling for p ∈ (0, 2). Given a stream of updates (insertions and deletions) to the coordinates of an underlying vector f ∈ ℝ n , a perfect Lp sampler must output an index i with probability |f i | p /||f|| p p , and is allowed to fail with some probability δ. So far, for p > 0 no algorithm has been shown to solve the problem exactly using poly(log n)-bits of space. In 2010, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> introduced an approximate L p sampler, which outputs i with probability (1 ± ν)|f i | p /||f|| p p , using space polynomial in ν -1 and log(n). The space complexity was later reduced by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> to roughly O(ν -p log 2 n log δ -1 ) for p ∈ (0, 2), which tightly matches the Ω(log 2 n log δ -1 ) lower bound in terms of n and δ, but is loose in terms of ν. Given these nearly tight bounds, it is perhaps surprising that no lower bound at all exists in terms of ν-not even a bound of Ω(ν -1 ) is known. In this paper, we explain this phenomenon by demonstrating the existence of an O(log 2 n log δ -1 )-bit perfect L p sampler for p ∈ (0, 2). This shows that ν need not factor into the space of an L p sampler, which completely closes the complexity of the problem for this range of p. For p = 2, our bound is O(log 3 n log δ -1 )-bits, which matches the prior best known upper bound of O(ν -2 log 3 n log δ -1 ), but has no dependence on ν. Finally, we show that a (1 ± ε) relative error estimate of the frequency f i of the sampled index i can be obtained using an additional O(ε -p log n)-bits of space for p -2 log 2 n) bits for p = 2, which was possible before only by running the prior algorithms with ν = ε.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00058"}, {"primary_key": "3407078", "vector": [], "sparse_vector": [], "title": "The Sketching Complexity of Graph and Hypergraph Counting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Subgraph counting is a fundamental primitive in graph processing, with applications in social network analysis (e.g., estimating the clustering coefficient of a graph), database processing and other areas. The space complexity of subgraph counting has been studied extensively in the literature, but many natural settings are still not well understood. In this paper we revisit the subgraph (and hypergraph) counting problem in the sketching model, where the algorithm's state as it processes a stream of updates to the graph is a linear function of the stream. This model has recently received a lot of attention in the literature, and has become a standard model for solving dynamic graph streaming problems. In this paper we give a tight bound on the sketching complexity of counting the number of occurrences of a small subgraph H in a bounded degree graph G presented as a stream of edge updates. Specifically, we show that the space complexity of the problem is governed by the fractional vertex cover number of the graph H. Our subgraph counting algorithm implements a natural vertex sampling approach, with sampling probabilities governed by the vertex cover of H. Our main technical contribution lies in a new set of Fourier analytic tools that we develop to analyze multiplayer communication protocols in the simultaneous communication model, allowing us to prove a tight lower bound. We believe that our techniques are likely to find applications in other settings. Besides giving tight bounds for all graphs H, both our algorithm and lower bounds extend to the hypergraph setting, albeit with some loss in space complexity.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00059"}, {"primary_key": "3407079", "vector": [], "sparse_vector": [], "title": "Pseudorandom Sets in Grassmann Graph Have Near-Perfect Expansion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove that pseudorandom sets in the Grassmann graph have near-perfect expansion. This completes the last missing piece of the proof of the 2-to-2-Games Conjecture (albeit with imperfect completeness). The Grassmann graph has induced subgraphs that are themselves isomorphic to Grassmann graphs of lower orders. A set of vertices is called pseudorandom if its density within all such subgraphs (of constant order) is at most slightly higher than its density in the entire graph. We prove that pseudorandom sets have almost no edges within them. Namely, their edge-expansion is very close to 1.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00062"}, {"primary_key": "3407080", "vector": [], "sparse_vector": [], "title": "Recharging Bandits.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce a general model of bandit problems in which the expected payout of an arm is an increasing concave function of the time since it was last played. We first develop a PTAS for the underlying optimization problem of determining a reward-maximizing sequence of arm pulls. We then show how to use this PTAS in a learning setting to obtain sublinear regret.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00037"}, {"primary_key": "3407081", "vector": [], "sparse_vector": [], "title": "Improved Decoding of Folded Reed-Solomon and Multiplicity Codes.", "authors": ["Swas<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we show new and improved error-correcting properties of folded Reed-Solomon codes and multiplicity codes. Both of these families of codes are based on polynomials over finite fields, and both have been the sources of recent advances in coding theory. Folded Reed-Solomon codes were the first explicit constructions of codes known to achieve list-decoding capacity; multivariate multiplicity codes were the first constructions of high-rate locally correctable codes; and univariate multiplicity codes are also known to achieve list-decoding capacity. However, previous analyses of the error-correction properties of these codes did not yield optimal results. In particular, in the list-decoding setting, the guarantees on the list-sizes were polynomial in the block length, rather than constant; and for multivariate multiplicity codes, local list-decoding algorithms could not go beyond the Johnson bound. In this paper, we show that Folded Reed-Solomon codes and multiplicity codes are in fact better than previously known in the context of list decoding and local list-decoding. More precisely, we first show that Folded RS codes achieve list-decoding capacity with constant list sizes, independent of the block length; and that high-rate univariate multiplicity codes can also be list-recovered with constant list sizes. Using our result on univariate multiplicity codes, we show that multivariate multiplicity codes are high-rate, locally list-recoverable codes. Finally, we show how to combine the above results with standard tools to obtain capacity achieving locally list decodable codes with query complexity significantly lower than was known before.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00029"}, {"primary_key": "3407082", "vector": [], "sparse_vector": [], "title": "A Matrix Chernoff Bound for Strongly Rayleigh Distributions and Spectral Sparsifiers from a few Random Spanning Trees.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Strongly Rayleigh distributions are a class of negatively dependent distributions of binary-valued random variables [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Liggett JAMS 09]. Recently, these distributions have played a crucial role in the analysis of algorithms for fundamental graph problems, e.g. Traveling Salesman Problem [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> FOCS 11]. We prove a new matrix Chernoff bound for Strongly Rayleigh distributions. As an immediate application, we show that adding together the Laplacians of ε -2 log 2 n random spanning trees gives an (1± ε) spectral sparsifiers of graph Laplacians with high probability. Thus, we positively answer an open question posted in [<PERSON><PERSON>, <PERSON>, Sri<PERSON>va, Teng JACM 13]. Our number of spanning trees for spectral sparsifier matches the number of spanning trees required to obtain a cut sparsifier in [<PERSON><PERSON>, <PERSON>, <PERSON>, Panigraphi STOC 11]. The previous best result was by naively applying a classical matrix Chernoff bound which requires ε -2 n log n spanning trees. For the tree averaging procedure to agree with the original graph Laplacian in expectation, each edge of the tree should be reweighted by the inverse of the edge leverage score in the original graph. We also show that when using this reweighting of the edges, the Laplacian of single random tree is bounded above in the PSD order by the original graph Laplacian times a factor log n with high probability, i.e. L T ≤ O(log n) L G . We show a lower bound that almost matches our last result, namely that in some graphs, with high probability, the random spanning tree is not bounded above in the spectral order by [log n/loglog n] times the original graph Laplacian. We also show a lower bound that in ε -2 log n spanning trees are necessary to get a (1± ε) spectral sparsifier.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00043"}, {"primary_key": "3407083", "vector": [], "sparse_vector": [], "title": "Fusible HSTs and the Randomized k-Server Conjecture.", "authors": ["<PERSON>"], "summary": "We exhibit a poly(log k)-competitive randomized algorithm for the k-server problem on any metric space. The best previous result independent of the geometry of the underlying metric space is the 2k-1 competitive ratio established for the deterministic work function algorithm by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (1995). Even for the special case when the underlying metric space is the real line, the best known competitive ratio was k. Since deterministic algorithms can do no better than k on any metric space with at least k+1 points, this establishes that for every metric space on which the problem is non-trivial, randomized algorithms give an exponential improvement over deterministic algorithms. Our algorithm maintains an approximation of the underlying metric space by a distribution over HSTs. The granularity and accuracy of the approximation is adjusted dynamically according to the aggregate behavior of the HST algorithms. In short: We try to obtain more accurate approximations at the locations and scales where the \"gaction\" is happening. Thus a crucial component of our approach is the O((log k) 2 )-competitive randomized algorithm for HSTs obtained in our previous work with <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, and its \"multiscale information theory\" perspective.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00049"}, {"primary_key": "3407084", "vector": [], "sparse_vector": [], "title": "Contextual Search via Intrinsic Volumes.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of contextual search, a multidimensional generalization of binary search that captures many problems in contextual decision-making. In contextual search, a learner is trying to learn the value of a hidden vector. Every round the learner is provided an adversarially-chosen context vector, submits a guess for the inner product of the context vector with the hidden vector, learns whether their guess was too high or too low, and incurs some loss based on the quality of their guess. The learner's goal is to minimize their total loss over the course of some number of rounds. We present an algorithm for the contextual search problem for the symmetric loss function that achieves constant total loss. We present a new algorithm for the dynamic pricing problem (which can be realized as a special case of the contextual search problem) that achieves doubly-logarithmic total loss, improving exponentially on the previous best known upper bounds and matching the known lower bounds (up to a polynomial dependence on dimension). Both algorithms make significant use of ideas from the field of integral geometry, most notably the notion of intrinsic volumes of a convex set. To the best of our knowledge this is the first application of intrinsic volumes to algorithm design.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00034"}, {"primary_key": "3407085", "vector": [], "sparse_vector": [], "title": "Spectral Subspace Sparsification.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a new approach to spectral sparsification that approximates the quadratic form of the pseudoinverse of a graph Laplacian restricted to a subspace. We show that sparsifiers with a near-linear number of edges in the dimension of the subspace exist. Our setting generalizes that of Schur complement sparsifiers. Our approach produces sparsifiers by sampling a uniformly random spanning tree of the input graph and using that tree to guide an edge elimination procedure that contracts, deletes, and reweights edges. In the context of Schur complement sparsifiers, our approach has two benefits over prior work. First, it produces a sparsifier in almost-linear time with no runtime dependence on the desired error. We directly exploit this to compute approximate effective resistances for a small set of vertex pairs in faster time than prior work (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON> '17). Secondly, it yields sparsifiers that are reweighted minors of the input graph. As a result, we give a near-optimal answer to a variant of the Steiner point removal problem. A key ingredient of our algorithm is a subroutine of independent interest: a near-linear time algorithm that, given a chosen set of vertices, builds a data structure from which we can query a multiplicative approximation to the decrease in the effective resistance between two vertices after identifying all vertices in the chosen set to a single vertex with inverse polynomial additional additive error in near-constant time.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00044"}, {"primary_key": "3407086", "vector": [], "sparse_vector": [], "title": "Efficiently Learning Mixtures of Mallows Models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mixtures of Mallows models are a popular generative model for ranking data coming from a heterogeneous population. They have a variety of applications including social choice, recommendation systems and natural language processing. Here we give the first polynomial time algorithm for provably learning the parameters of a mixture of Mallows models with any constant number of components. Prior to our work, only the two component case had been settled. Our analysis revolves around a determinantal identity of <PERSON><PERSON><PERSON> which was proven in the context of mathematical physics, which we use to show polynomial identifiability and ultimately to construct test functions to peel off one component at a time. To complement our upper bounds, we show information-theoretic lower bounds on the sample complexity as well as lower bounds against restricted families of algorithms that make only local queries. Together, these results demonstrate various impediments to improving the dependence on the number of components. They also motivate the study of learning mixtures of Mallows models from the perspective of beyond worst-case analysis. In this direction, we show that when the scaling parameters of the Mallows models have separation, there are much faster learning algorithms.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00066"}, {"primary_key": "3407087", "vector": [], "sparse_vector": [], "title": "MDS Matrices over Small Fields: A Proof of the GM-MDS Conjecture.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "An MDS matrix is a matrix whose minors all have full rank. A question arising in coding theory is, what zero patterns can MDS matrices have. There is a natural combinatorial necessary condition (called the MDS condition) which is necessary over any field, and sufficient over very large fields by a probabilistic argument. <PERSON><PERSON> et al. (ISIT 2014) conjectured that the MDS condition is sufficient over small fields as well, and gave an algebraic conjecture which would imply this. In this work, we prove this conjecture.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00027"}, {"primary_key": "3407088", "vector": [], "sparse_vector": [], "title": "Classical Verification of Quantum Computations.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present the first protocol allowing a classical computer to interactively verify the result of an efficient quantum computation. We achieve this by constructing a measurement protocol, which enables a classical verifier to use a quantum prover as a trusted measurement device. The protocol forces the prover to behave as follows: the prover must construct an n qubit state of his choice, measure each qubit in the Hadamard or standard basis as directed by the verifier, and report the measurement results to the verifier. The soundness of this protocol is enforced based on the assumption that the learning with errors problem is computationally intractable for efficient quantum machines.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00033"}, {"primary_key": "3407089", "vector": [], "sparse_vector": [], "title": "Classical Homomorphic Encryption for Quantum Circuits.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present the first leveled fully homomorphic encryption scheme for quantum circuits with classical keys. The scheme allows a classical client to blindly delegate a quantum computation to a quantum server: an honest server is able to run the computation while a malicious server is unable to learn any information about the computation. We show that it is possible to construct such a scheme directly from a quantum secure classical homomorphic encryption scheme with certain properties. Finally, we show that a classical homomorphic encryption scheme with the required properties can be constructed from the learning with errors problem.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00039"}, {"primary_key": "3407090", "vector": [], "sparse_vector": [], "title": "On Subexponential Parameterized Algorithms for Steiner Tree and Directed Subset TSP on Planar Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There are numerous examples of the so-called \"square root phenomenon\" in the field of parameterized algorithms: many of the most fundamental graph problems, parameterized by some natural parameter k, become significantly simpler when restricted to planar graphs and in particular the best possible running time is exponential in O(sqrt(k)) instead of O(k) (modulo standard complexity assumptions). We consider two classic optimization problems parameterized by the number of terminals. The Steiner Tree problem asks for a minimum-weight tree connecting a given set of terminals T in an edge-weighted graph. In the Subset Traveling Salesman problem we are asked to visit all the terminals T by a minimum-weight closed walk. We investigate the parameterized complexity of these problems in planar graphs, where the number k = |T| of terminals is regarded as the parameter. Our results are the following: • Subset TSP can be solved in time 2^O(sqrt(k) log k) . n^O(1) even on edge-weighted directed planar graphs. This improves upon the algorithm of <PERSON> and <PERSON> [SODA 2014] with the same running time that worked only on undirected planar graphs with polynomially large integer weights. • Assuming the Exponential-Time Hypothesis, <PERSON> Tree on undirected planar graphs cannot be solved in time 2^o(k) . n^O(1), even in the unit-weight setting. This lower bound makes <PERSON> Tree the first \"genuinely planar\" problem (i.e., where the input is only planar graph with a set of distinguished terminals) for which we can show that the square root phenomenon does not appear. • Steiner Tree can be solved in time n^O(sqrt(k)) * W on undirected planar graphs with maximum edge weight W. Note that this result is incomparable to the fact that the problem is known to be solvable in time 2^k . n^O(1) even in general graphs. A direct corollary of the combination of our results for Steiner Tree is that this problem does not admit a parameter-preserving polynomial kernel on planar graphs unless ETH fails.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00052"}, {"primary_key": "3407091", "vector": [], "sparse_vector": [], "title": "Efficient Polynomial-Time Approximation Scheme for the Genus of Dense Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The results of this paper provide an Efficient Polynomial-Time Approximation Scheme (EPTAS) for approximating the genus (and non-orientable genus) of dense graphs. The running time of the algorithm is quadratic. Moreover, we extend the algorithm to output an embedding (rotation system), whose genus is arbitrarily close to the minimum genus. This second algorithm is an Efficient Polynomial-time Randomized Approximation Scheme (EPRAS) and the expected running time is also quadratic.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00074"}, {"primary_key": "3407092", "vector": [], "sparse_vector": [], "title": "Low-Degree Testing for Quantum States, and a Quantum Entangled Games PCP for QMA.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We show that given an explicit description of a multiplayer game, with a classical verifier and a constant number of players, it is QMA-hard, under randomized reductions, to distinguish between the cases when the players have a strategy using entanglement that succeeds with probability 1 in the game, or when no such strategy succeeds with probability larger than 1/2. This proves the \"games quantum PCP conjecture\" of <PERSON><PERSON><PERSON><PERSON> and the second author (ITCS'15), albeit under randomized reductions. The core component in our reduction is a construction of a family of two-player games for testing n-qubit maximally entangled states. For any integer n ≥ 2, we give such a game in which questions from the verifier are O(log n) bits long, and answers are poly(loglogn) bits long. We show that for any constant ε ≥ 0, any strategy that succeeds with probability at least 1 - ε in the test must use a state that is within distance δ(ε) = O(ε c ) from a state that is locally equivalent to a maximally entangled state on n qubits, for some universal constant c > 0. The construction is based on the classical plane-vs-point test for multivariate low-degree polynomials of <PERSON>z and <PERSON>fra (STOC'97). We extend the classical test to the quantum regime by executing independent copies of the test in the generalized Pauli X and Z bases over Fq, where q is a sufficiently large prime power, and combine the two through a test for the Pauli twisted commutation relations. Our main complexity-theoretic result is obtained by combining this family of games with techniques from the classical PCP literature. More specifically, we use constructions of PCPs of proximity introduced by <PERSON>-<PERSON>sson et al. (CCC'05), and crucially rely on a linear property of such PCPs. Another consequence of our results is a deterministic reduction from the games quantum PCP conjecture to a suitable formulation of the constraint satisfaction quantum PCP conjecture.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00075"}, {"primary_key": "3407093", "vector": [], "sparse_vector": [], "title": "Hardness Magnification for Natural Problems.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We show that for several natural problems of interest, complexity lower bounds that are barely non-trivial imply super-polynomial or even exponential lower bounds in strong computational models. We term this phenomenon \"hardness magnification\". Our examples of hardness magnification include: 1. Let MCSP be the decision problem whose YES instances are truth tables of functions with circuit complexity at most s(n). We show that if MCSP[2^√n] cannot be solved on average with zero error by formulas of linear (or even sub-linear) size, then NP does not have polynomial-size formulas. In contrast, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2017) recently showed that MCSP[2^√n] cannot be solved in the worst case by formulas of nearly quadratic size. 2. If there is a c > 0 such that for each positive integer d there is an ε > 0 such that the problem of checking if an n-vertex graph in the adjacency matrix representation has a vertex cover of size (log n)^c cannot be solved by depth-d AC^0 circuits of size m^1+ε, where m = Θ(n^2), then NP does not have polynomial-size formulas. 3. Let (α, β)-MCSP[s] be the promise problem whose YES instances are truth tables of functions that are α-approximable by a circuit of size s(n), and whose NO instances are truth tables of functions that are not β-approximable by a circuit of size s(n). We show that for arbitrary 1/2 c, let MKtP[c, s] be the promise problem whose YES instances are strings of Kt complexity at most c(N) and NO instances are strings of Kt complexity greater than s(N). We show that if there is a δ > 0 such that for each ε > 0, MKtP[N^ε, N^ε + 5 log(N)] requires Boolean circuits of size N^1+δ, then EXP is not contained in SIZE (poly). For each of the cases of magnification above, we observe that standard hardness assumptions imply much stronger lower bounds for these problems than we require for magnification. We further explore magnification as an avenue to proving strong lower bounds, and argue that magnification circumvents the \"natural proofs\" barrier of Razborov and Rudich (1997). Examining some standard proof techniques, we find that they fall just short of proving lower bounds via magnification. As one of our main open problems, we ask whether there are other meta-mathematical barriers to proving lower bounds that rule out approaches combining magnification with known techniques.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00016"}, {"primary_key": "3407094", "vector": [], "sparse_vector": [], "title": "PanORAMa: Oblivious <PERSON> with Logarithmic Overhead.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present PanORAMa, the first Oblivious RAM construction that achieves communication overhead O(log N log log N) for database of N blocks and for any block size B = Ω(log N) while requiring client memory of only a constant number of memory blocks. Our scheme can be instantiated in the \"balls and bins\" model in which <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [JACM 96] showed an Ω(log N) lower bound for ORAM communication. Our construction follows the hierarchical approach to ORAM design and relies on two main building blocks of independent interest: a new oblivious hash table construction with improved amortized O(log N + poly(log log λ)) communication overhead for security parameter λ and N = poly(λ), assuming its input is randomly shuffled; and a complementary new oblivious random multi-array shuffle construction, which shuffles N blocks of data with communication O(N log log λ + N log N/log λ) when the input has a certain level of entropy. We combine these two primitives to improve the shuffle time in our hierarchical ORAM construction by avoiding heavy oblivious shuffles and leveraging entropy remaining in the merged levels from previous shuffles. As a result, the amortized shuffle cost is asymptotically the same as the lookup complexity in our construction.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00087"}, {"primary_key": "3407095", "vector": [], "sparse_vector": [], "title": "Laconic Function Evaluation and Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>e", "<PERSON>"], "summary": "We introduce a new cryptographic primitive called laconic function evaluation (LFE). Using LFE, <PERSON> can compress a large circuit f into a small digest. <PERSON> can encrypt some data x under this digest in a way that enables <PERSON> to recover f(x) without learning anything else about <PERSON>'s data. For the scheme to be laconic, we require that the size of the digest, the run-time of the encryption algorithm and the size of the ciphertext should all be small, much smaller than the circuit-size of f. We construct an LFE scheme for general circuits under the learning with errors (LWE) assumption, where the above parameters only grow polynomially with the depth but not the size of the circuit. We then use LFE to construct secure 2-party and multi-party computation (2PC, MPC) protocols with novel properties: We construct a 2-round 2PC protocol between <PERSON> and <PERSON> with respective inputs x A , x B in which <PERSON> learns the output f(x A , x B ) in the second round. This is the first such protocol which is \"Bob-optimized\", meaning that <PERSON> does all the work while <PERSON>'s computation and the total communication of the protocol are smaller than the size of the circuit f or even <PERSON>'s input xA. In contrast, prior solutions based on fully homomorphic encryption are \"Alice-optimized\". . We construct an MPC protocol, which allows N parties to securely evaluate a function f(x 1 , ..., x N ) over their respective inputs, where the total amount of computation performed by the parties during the protocol execution is smaller than that of evaluating the function itself! Each party has to individually pre-process the circuit f before the protocol starts and post-process the protocol transcript to recover the output after the protocol ends, and the cost of these steps is larger than the circuit size. However, this gives the first MPC where the computation performed by each party during the actual protocol execution, from the time the first protocol message is sent until the last protocol message is received, is smaller than the circuit size.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00086"}, {"primary_key": "3407096", "vector": [], "sparse_vector": [], "title": "An Improved <PERSON><PERSON> for Weak Epsilon-Nets in the Plane.", "authors": ["<PERSON><PERSON>"], "summary": "We show that for any finite point set P in the plane and ε>0 there exist O(1/ε 3/2+γ ) points, for arbitrary small γ>0, that pierce every convex set K with |K∩ P|≥ ε |P|. This is the first improvement of the bound of O(1)ε 2 ) that was obtained in 1992 by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> for general point sets in the plane.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00030"}, {"primary_key": "3407097", "vector": [], "sparse_vector": [], "title": "Near Log-Convexity of Measured Heat in (Discrete) Time and Consequences.", "authors": ["<PERSON><PERSON>"], "summary": "Let u, v ∈ R + Ω be positive unit vectors and S ∈ R + Ω×Ω be a symmetric substochastic matrix. For an integer t ≥ 0, let m t = 〈 v, S t u〉, which we view as the heat measured by v after an initial heat configuration u is let to diffuse for t time steps according to S. Since S is entropy improving, one may intuit that m t should not change too rapidly over time. We give the following formalizations of this intuition. We prove that m t+2 ≥ m t 1+2/t , an inequality studied earlier by <PERSON><PERSON><PERSON> and <PERSON> (also <PERSON><PERSON><PERSON> and <PERSON>) for u=v and shown true under the restriction m t ≥ e -4t . Moreover we prove that for any ε>0, a stronger inequality m t+2 ≥ t 1-ε · m t 1+2/t holds unless m t+2 m t-2 ≥ δm t 2 for some δ that depends on ε only. Phrased differently, ∀ε > 0, ∃δ > 0 such that ∀S, u, v m t+2 /m t 1+2/t ≥ min{t 1-ε , δm t 1-2/t /m t-2 }, ∀t ≥ 2, which can be viewed as a truncated log-convexity statement. Using this inequality, we answer two related open questions in complexity theory: Any property tester for k-linearity requires Ω(klog k). queries and the randomized communication complexity of the k-Hamming distance problem is Ω(k log k). Further we show that any randomized parity decision tree computing k-Hamming weight has size exp(Ω(k log k)).", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00095"}, {"primary_key": "3407098", "vector": [], "sparse_vector": [], "title": "The Diameter of the Fractional Matching Polytope and Its Hardness Implications.", "authors": ["<PERSON>"], "summary": "The (combinatorial) diameter of a polytope P ⊆ ℝ d is the maximum value of a shortest path between a pair of vertices on the 1-skeleton of P, that is the graph where the nodes are given by the 0-dimensional faces of P, and the edges are given the 1-dimensional faces of P. The diameter of a polytope has been studied from many different perspectives, including a computational complexity point of view. In particular, [<PERSON><PERSON><PERSON> and <PERSON>, 1994] showed that computing the diameter of a polytope is (weakly) NP-hard. In this paper, we show that the problem of computing the diameter is strongly NP-hard even for a polytope with a very simple structure: namely, the fractional matching polytope. We also show that computing a pair of vertices at maximum shortest path distance on the 1-skeleton of this polytope is an APX-hard problem. We prove these results by giving an exact characterization of the diameter of the fractional matching polytope, that is of independent interest.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00090"}, {"primary_key": "3407099", "vector": [], "sparse_vector": [], "title": "Coordinate Methods for Accelerating ℓ∞ Regression and Faster Approximate Maximum Flow.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper we provide faster algorithms for approximately solving ℓ ∞ regression, a fundamental problem prevalent in both combinatorial and continuous optimization. In particular we provide an accelerated coordinate descent method which converges in k iterations at a O(1/k) rate independent of the dimension of the problem, and whose iterations can be implemented cheaply for many structured matrices. Our algorithm can be viewed as an alternative approach to the recent breakthrough result of Sherman [She17] which achieves a similar running time improvement over classic algorithmic approaches, i.e. smoothing and gradient descent, which either converge at a O(1/√k) rate or have running times with a worse dependence on problem parameters. Our running times match those of [She17] across a broad range of parameters and in certain cases, improves upon it. We demonstrate the efficacy of our result by providing faster algorithms for the well-studied maximum flow problem. We show how to leverage our algorithm to achieve a runtime of Õ(m + √ns/ε) to compute an ε-approximate maximum flow, for an undirected graph with m edges, n vertices, and where s is the squared ℓ 2 norm of the congestion of any optimal flow. As s = O(m) this yields a running time of Õ(m + √nm/ε), generically improving upon the previous best known runtime of Õ(m/ε) in [She17] whenever the graph is slightly dense. Moreover, we show how to leverage this result to achieve improved exact algorithms for maximum flow on a variety of unit capacity graphs. We achieve these results by providing an accelerated coordinate descent method capable of provably exploiting dynamic measures of coordinate smoothness for smoothed versions of ℓ ∞ regression. Our analysis leverages the structure of the Hessian of the smoothed problem via a simple bound on its trace, as well as techniques for exploiting column sparsity of the constraint matrix for faster sampling and improved smoothness estimates. We hope that the work of this paper can serve as an important step towards achieving even faster maximum flow algorithms.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00091"}, {"primary_key": "3407100", "vector": [], "sparse_vector": [], "title": "Strong Coresets for k-Median and Subspace Approximation: Goodbye Dimension.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We obtain the first strong coresets for the k-median and subspace approximation problems with sum of distances objective function, on n points in d dimensions, with a number of weighted points that is independent of both n and d; namely, our coresets have size poly(k/ε). A strong coreset (1+ε)-approximates the cost function for all possible sets of centers simultaneously. We also give efficient nnz(A) + (n+d) poly(k/ε) + exp(poly(k/ε)) time algorithms for computing these coresets. We obtain the result by introducing a new dimensionality reduction technique for coresets that significantly generalizes an earlier result of <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> [FSS13] for squared Euclidean distances to sums of P-th powers of Euclidean distances for constant p≥1.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00081"}, {"primary_key": "3407101", "vector": [], "sparse_vector": [], "title": "PPP-Completeness with Connections to Cryptography.", "authors": ["<PERSON><PERSON>", "Manolis Zampetakis", "<PERSON><PERSON><PERSON>"], "summary": "Polynomial Pigeonhole Principle (PPP) is an important subclass of TFNP with profound connections to the complexity of the fundamental cryptographic primitives: collision-resistant hash functions and one-way permutations. In contrast to most of the other subclasses of TFNP, no complete problem is known for PPP. Our work identifies the first PPP-complete problem without any circuit or Turing Machine given explicitly in the input, and thus we answer a longstanding open question from [Papadimitriou1994]. Specifically, we show that constrained-SIS, a generalized version of the well-known Short Integer Solution problem (SIS) from lattice-based cryptography, is PPP-complete. In order to give intuition behind our reduction for constrained-SIS, we identify another PPP-complete problem with a circuit in the input but closely related to lattice problems. We call this problem BLICHFELDT and it is the computational problem associated with <PERSON><PERSON><PERSON><PERSON>'s fundamental theorem in the theory of lattices. Building on the inherent connection of PPP with collision-resistant hash functions, we use our completeness result to construct the first natural hash function family that captures the hardness of all collision-resistant hash functions in a worst-case sense, i.e. it is natural and universal in the worst-case. The close resemblance of our hash function family with SIS, leads us to the first candidate collision-resistant hash function that is both natural and universal in an average-case sense. Finally, our results enrich our understanding of the connections between PPP, lattice problems and other concrete cryptographic assumptions, such as the discrete logarithm problem over general groups.", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00023"}, {"primary_key": "3407102", "vector": [], "sparse_vector": [], "title": "Beating the Integrality Ratio for s-t-Tours in Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Among various variants of the traveling salesman problem, the s-t-path graph TSP has the special feature that we know the exact integrality ratio, 3/2, and an approximation algorithm matching this ratio. In this paper, we go below this threshold: we devise a polynomial-time algorithm for the s-t-path graph TSP with approximation ratio 1.497. Our algorithm can be viewed as a refinement of the 3/2-approximation algorithm by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [16], but we introduce several completely new techniques. These include a new type of ear-decomposition, an enhanced ear induction that reveals a novel connection to matroid union, a stronger lower bound, and a reduction of general instances to instances in which s and t have small distance (which works for general metrics).", "published": "2018-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2018.00078"}]