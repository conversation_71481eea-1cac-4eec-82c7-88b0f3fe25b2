[{"primary_key": "4441733", "vector": [], "sparse_vector": [], "title": "Automated Program Repair in an Integrated Development Environment.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present the integration of the AutoFix automated program repair technique into the EiffelStudio Development Environment. AutoFix presents itself like a recommendation system capable of automatically finding bugs and suggesting fixes in the form of source-code patches. Its performance suggests usage scenarios where it runs in the background or during work interruptions, displaying fix suggestions as they become available. This is a contribution towards the vision of semantic Integrated Development Environments, which offer powerful automated functionality within interfaces familiar to developers. A screencast highlighting the main features of AutoFix can be found at: http://youtu.be/Ff2ULiyL-80.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.222"}, {"primary_key": "4441734", "vector": [], "sparse_vector": [], "title": "The ECCO Tool: Extraction and Composition for Clone-and-Own.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Software reuse has become mandatory for companies to compete and a wide range of reuse techniques are available today. However, ad hoc practices such as copying existing systems and customizing them to meet customer-specific needs are still pervasive, and are generically called clone-and-own. We have developed a conceptual framework to support this practice named ECCO that stands for Extraction and Composition for Clone-and-Own. In this paper we present our Eclipse-based tool to support this approach. Our tool can automatically locate reusable parts from previously developed products and subsequently compose a new product from a selection of desired features. The tools demonstration video can be found here: http://youtu.be/N6gPekuxU6o.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.218"}, {"primary_key": "4441735", "vector": [], "sparse_vector": [], "title": "IccTA: Detecting Inter-Component Privacy Leaks in Android Apps.", "authors": ["<PERSON>", "<PERSON>", "Tegawendé F. Bissyandé", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Shake Them All is a popular \"Wallpaper\" application exceeding millions of downloads on Google Play. At installation, this application is given permission to (1) access the Internet (for updating wallpapers) and (2) use the device microphone (to change background following noise changes). With these permissions, the application could silently record user conversations and upload them remotely. To give more confidence about how Shake Them All actually processes what it records, it is necessary to build a precise analysis tool that tracks the flow of any sensitive data from its source point to any sink, especially if those are in different components. Since Android applications may leak private data carelessly or maliciously, we propose IccTA, a static taint analyzer to detect privacy leaks among components in Android applications. IccTA goes beyond state-of-the-art approaches by supporting inter- component detection. By propagating context information among components, IccTA improves the precision of the analysis. IccTA outperforms existing tools on two benchmarks for ICC-leak detectors: DroidBench and ICC-Bench. Moreover, our approach detects 534 ICC leaks in 108 apps from MalGenome and 2,395 ICC leaks in 337 apps in a set of 15,000 Google Play apps.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.48"}, {"primary_key": "4441739", "vector": [], "sparse_vector": [], "title": "CodeAware: Sensor-Based Fine-Grained Monitoring and Management of Software Artifacts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Current continuous integration (CI) tools, although extensible, can be limiting in terms of flexibility. In particular, artifact analysis capabilities available through plug in mechanisms are both coarse-grained and centralized. To address this limitation, this paper introduces a new paradigm, Code Aware, for distributed and fine-grained artifact analysis. Code Aware is an ecosystem inspired by sensor networks, consisting of monitors and actuators, aimed at improving code quality and team productivity. Code ware's vision entails (a) the ability to probe software artifacts of any granularity and localization, from variables to classes or files to entire systems, (b) the ability to perform both static and dynamic analyses on these artifacts, and (c) the ability to describe targeted remediation actions, for example to notify interested developers, through automated actuators. We provide motivational examples for the use of Code Aware that leverage current CI solutions, sketch the architecture of its underlying ecosystem, and outline research challenges.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.192"}, {"primary_key": "4441741", "vector": [], "sparse_vector": [], "title": "Understanding Conflicts Arising from Collaborative Development.", "authors": ["Paola R. G. <PERSON>ccioly"], "summary": "When working in a collaborative development environment, developers implement tasks separately. Consequently, during the integration process, one might have to deal with conflicting changes. Previous studies indicate that conflicts occur frequently and impair developers' productivity. Such evidence motivates the development of tools that try to tackle this problem. However, despite the existing evidence, there are still many unanswered questions. The goal of this research is to investigate conflict characteristics in practice through empirical studies and use this body of knowledge to improve strategies that support software developers working collaboratively.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.246"}, {"primary_key": "4441743", "vector": [], "sparse_vector": [], "title": "3rd International Workshop on Release Engineering (RELENG 2015).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Release engineering deals with all activities inbetween regular development and actual usage of asoftware product by the end user, i.e., integration, build, testexecution, packaging and delivery of software. Although re-search on this topic goes back for decades, the increasing heterogeneity and variability of software products along withthe recent trend to reduce the release cycle to days or even hoursstarts to question some of the common beliefs and practicesof the field. For example, a project like Mozilla Firefox releasesevery 6 weeks, generating updates for dozens of existing Fire-fox versions on 5 desktop, 2 mobile and 3 mobile desktopplatforms, each of which for more than 80 locales. In this con-text, the International Workshop on Release Engineering(RELENG) aims to provide a highly interactive forum for re-searchers and practitioners to address the challenges of, findsolutions for and share experiences with release engineering, and to build connections between the various communities.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.321"}, {"primary_key": "4441745", "vector": [], "sparse_vector": [], "title": "Poster: Static Detection of Configuration-Dependent Bugs in Configurable Software.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Configurable software systems enable developers to configure at compile time a single variant of the system to tailor it towards specific environments and features. Although traditional static analysis tools can assist developers in software development and maintenance, they can only run on a concrete configuration of a configurable software system. Thus, it is necessary to derive many configurations so that the configuration-specific parts of the source code can be checked. To avoid this tedious and error-prone process, we propose an approach to automatically derive a set of configurations that cover as many combinations of configuration-specific blocks of code or source files as possible. We represent a C program with CPP directives (e.g., #ifdef) with a CPP control-flow graph (CPP-CFG) in which CPP expressions are condition nodes and #ifdef blocks are statement nodes. We then explore possible paths on CPP-CFG with dynamic symbolic execution and depth-first search algorithms, and correspondingly, producing possible combinations of concrete blocks of C code, on which an existing static analysis tool can run. Our preliminary evaluation on a benchmark of configuration-dependent bugs on Linux shows that our approach can detect more bugs than a state-of-the-art tool.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.252"}, {"primary_key": "4441748", "vector": [], "sparse_vector": [], "title": "Rapid Multi-Purpose, Multi-Commit Code Analysis.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Existing code- and software evolution studies typically operate on the scale of a few revisions of a small number of projects, mostly because existing tools are unsuited for performing large-scale studies. We present a novel approach, which can be used to analyze an arbitrary number of revisions of a software project simultaneously and which can be adapted for the analysis of mixed-language projects. It lays the foundation for building high-performance code analyzers for a variety of scenarios. We show that for one particular scenario, namely code metric computation, our prototype outperforms existing tools by multiple orders of magnitude when analyzing thousands of revisions.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.211"}, {"primary_key": "4441754", "vector": [], "sparse_vector": [], "title": "CS/SE Instructors Can Improve Student Writing without Reducing Class Time Devoted to Technical Content: Experimental Results.", "authors": ["<PERSON>", "<PERSON>", "Mladen <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Computer Science and Software Engineering (CS/SE) profession reports that new college graduates lack the communication skills needed for personal and organizational success. Many CS/SE faculty may omit communication instruction from their courses because they do not want to reduce technical content. We experimented in a software-engineering-intensive second-semester programming course with strategies for improving students' writing of black box test plans that included no instruction on writing the plans beyond the standard lecture on testing. The treatment version of the course used 1) a modified assignment that focused on the plan's readers, 2) a model plan students could consult online, and 3) a modified grading rubric that identified the readers' needs. Three external raters found that students in the treatment sections outperformed students in the control sections on writing for five of nine criteria on rubrics for evaluating the plans and on the raters' holistic impression of the students' technical and communication abilities from the perspectives of a manager and a tester.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.178"}, {"primary_key": "4441755", "vector": [], "sparse_vector": [], "title": "Striving for Failure: An Industrial Case Study about Test Failure Prediction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Hyunsook Do"], "summary": "Software regression testing is an important, yet very costly, part of most major software projects. When regression tests run, any failures that are found help catch bugs early and smooth the future development work. The act of executing large numbers of tests takes significant resources that could, otherwise, be applied elsewhere. If tests could be accurately classified as likely to pass or fail prior to the run, it could save significant time while maintaining the benefits of early bug detection. In this paper, we present a case study to build a classifier for regression tests based on industrial software, Microsoft Dynamics AX. In this study, we examine the effectiveness of this classification as well as which aspects of the software are the most important in predicting regression test failures.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.134"}, {"primary_key": "4441757", "vector": [], "sparse_vector": [], "title": "Software Engineering for Privacy in-the-Large.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There will be an estimated 35 zettabytes (35 × 10 21 ) of digital records worldwide by the year 2020. This effectively amounts to privacy management on an ultra-large-scale. In this briefing, we discuss the privacy challenges posed by such an ultra-large-scale ecosystem - we term this \"Privacy in the Large\". We will contrast existing approaches to privacy management, reflect on their strengths and limitations in this regard and outline key software engineering research and practice challenges to be addressed in the future.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.300"}, {"primary_key": "4441759", "vector": [], "sparse_vector": [], "title": "Effectiveness of Persona with Personality Traits on Conceptual Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Conceptual design is an important skill in Software Engineering. Teaching conceptual design that can deliver a useful product is challenging, particularly when access to real users is limited. This study explores the effects of the use of Holistic Personas (i.e. a persona enriched with personality traits) on students' performance in creating conceptual designs. Our results indicate that the students were able to identify the personality traits of personas and their ratings of the personalities match closely with the intended personalities. A majority of the participants stated that their designs were tailored to meet the needs of the given personas' personality traits. Results suggest that the Holistic Personas can help students to take into account personality traits in the conceptual design process. Further studies are warranted to assess the value of incorporating Holistic Personas in conceptual design training for imparting skills of producing in-depth design by taking personalities into account.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.155"}, {"primary_key": "4441764", "vector": [], "sparse_vector": [], "title": "The Use of Text Retrieval and Natural Language Processing in Software Engineering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This technical briefing presents the state of the art Text Retrieval and Natural Language Processing techniques used in Software Engineering and discusses their applications in the field.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.301"}, {"primary_key": "4441766", "vector": [], "sparse_vector": [], "title": "Search-Based Migration of Model Variants to Software Product Line Architectures.", "authors": ["<PERSON>"], "summary": "Software Product Lines (SPLs) are families of related software systems developed for specific market segments or domains. Commonly, SPLs emerge from sets of existing variants when their individual maintenance becomes infeasible. However, current approaches for SPL migration do not support design models, are partially automated, or do not reflect constraints from SPL domains. To tackle these limitations, the goal of this doctoral research plan is to propose an automated approach to the SPL migration process at the design level. This approach consists of three phases: detection, analysis and transformation. It uses as input the class diagrams and lists of features for each system variant, and relies on search-based algorithms to create a product line architecture that best captures the variability present in the variants. Our expected contribution is to support the adoption of SPL practices in companies that face the scenario of migrating variants to SPLs.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.286"}, {"primary_key": "4441769", "vector": [], "sparse_vector": [], "title": "A Field Study on Fostering Structural Navigation with Prodet.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Past studies show that developers who navigate code in a structural manner complete tasks faster and more correctly than those whose behavior is more opportunistic. The goal of this work is to move professional developers towards more effective program comprehension and maintenance habits by providing an approach that fosters structural code navigation. To this end, we created a Visual Studio plugin called Prodet that integrates an always-on navigable visualization of the most contextually relevant portions of the call graph. We evaluated the effectiveness of our approach by deploying it in a six week field study with professional software developers. The study results show a statistically significant increase in developers' use of structural navigation after installing Prodet. The results also show that developers continuously used the filtered and navigable call graph over the three week period in which it was deployed in production. These results indicate the maturity and value of our approach to increase developers' effectiveness in a practical and professional environment.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.151"}, {"primary_key": "4441770", "vector": [], "sparse_vector": [], "title": "Mining Patterns of Sensitive Data Usage.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "When a user downloads an Android application from a market, she does not know much about its actual behavior. A brief description, a set of screenshots, and the list of permissions, which give a high level intuition of what the application might be doing, are all the user sees before installing and running the application on his device. These elements are not enough to decide whether the application is secure, and for sure they do not indicate whether it might violate the user's privacy by leaking some sensitive data. The goal of my thesis is to employ both static and dynamic taint analyses to gather information on how Android applications use sensitive data. The main hypothesis of this work is that malicious and benign mobile applications differ in how they use sensitive data, and consequently information flow can be used effectively to identify malware.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.285"}, {"primary_key": "4441771", "vector": [], "sparse_vector": [], "title": "Mining Apps for Abnormal Usage of Sensitive Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "What is it that makes an app malicious? One important factor is that malicious apps treat sensitive data differently from benign apps. To capture such differences, we mined 2,866 benign Android applications for their data flow from sensitive sources, and compare these flows against those found in malicious apps. We find that (a) for every sensitive source, the data ends up in a small number of typical sinks; (b) these sinks differ considerably between benign and malicious apps; (c) these differences can be used to flag malicious apps due to their abnormal data flow; and (d) malicious apps can be identified by their abnormal data flow alone, without requiring known malware samples. In our evaluation, our MUDFLOW prototype correctly identified 86.4% of all novel malware, and 90.1% of novel malware leaking sensitive data.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.61"}, {"primary_key": "4441772", "vector": [], "sparse_vector": [], "title": "Tracking Static Analysis Violations over Time to Capture Developer Characteristics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many interesting questions about the software quality of a code base can only be answered adequately if fine-grained information about the evolution of quality metrics over time and the contributions of individual developers is known. We present an approach for tracking static analysis violations (which are often indicative of defects) over the revision history of a program, and for precisely attributing the introduction and elimination of these violations to individual developers. As one application, we demonstrate how this information can be used to compute ``fingerprints'' of developers that reflect which kinds of violations they tend to introduce or to fix. We have performed an experimental study on several large open-source projects written in different languages, providing evidence that these fingerprints are well-defined and capture characteristic information about the coding habits of individual developers.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.62"}, {"primary_key": "4441773", "vector": [], "sparse_vector": [], "title": "7th International Workshop on Principles of Engineering Service-Oriented and Cloud Systems (PESOS 2015).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "PESOS has established itself as a forum that brings together software engineering researchers and practitioners working in the areas of service-oriented systems to discuss research challenges, new developments and applications, as well as methods, techniques, experiences, and tools to support engineering, evolution and adaptation of service-oriented systems. The technical advances and growing adoption of Cloud computing is creating new challenges for the PESOS the software services community to explore the approaches to better engineer software systems that are designed, developed, operated and governed in the context of the Cloud. We again attracted high-quality submissions on a diverse set of relevant topics such as better approaches to engineering service-based collaborative systems, Infrastructure as a Service (IaaS), Platform as a Service (PaaS), and Software as a Service (SaaS) models of cloud computing and associated software quality attributes. PESOS 2015 will continue to be the key forum for collecting case studies and artifacts for educators and researchers in this area.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.318"}, {"primary_key": "4441775", "vector": [], "sparse_vector": [], "title": "An Integrated Multi-Agent-Based Simulation Approach to Support Software Project Management.", "authors": ["Davy de Medeiros Baia"], "summary": "Software projects often do not accomplish what is expected. They fail to comply with the planned schedule, cost more than predicted, or are simply not completed at all owing to issues such as bad planning, a poorly chosen team or an incorrect definition of the tasks to be performed. Although simulation methods and tools have been introduced to alleviate these problems, there is a lack of simulation approaches that integrate software project knowledge, software development processes, project-related situation-awareness, and learning techniques to help project managers to make more informed decisions and hence reach successful conclusions with software projects. In addition, in order to be more proactive, such approaches need to provide simulations based on both static and dynamic situation-awareness data, support (self-)adaptive project planning and execution, and recommend remedial courses of action when real-time project anomalies occur. In this context, this PhD research aims to create an integrated multi-agent-based simulation to support software project management in a more comprehensive way.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.290"}, {"primary_key": "4441778", "vector": [], "sparse_vector": [], "title": "Poster: An Efficient Equivalence Checking Method for Petri Net Based Models of Programs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The initial behavioural specification of any software programs goes through significant optimizing and parallelizing transformations, automated and also human guided, before being mapped to an architecture. Establishing validity of these transformations is crucial to ensure that they preserve the original behaviour. PRES+ model (Petri net based Representation of Embedded Systems) encompassing data processing is used to model parallel behaviours. Being value based with inherent scope of capturing parallelism, PRES+ models depict such data dependencies more directly; accordingly, they are likely to be more convenient as the intermediate representations (IRs) of both the source and the transformed codes for translation validation than strictly sequential variable-based IRs like Finite State Machines with Data path (FSMDs) (which are essentially sequential control flow graphs (CFGs)). In this work, a path based equivalence checking method for PRES+ models is presented.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.268"}, {"primary_key": "4441781", "vector": [], "sparse_vector": [], "title": "scvRipper: Video Scraping Tool for Modeling Developers&apos; Behavior Using Interaction Data.", "authors": ["Ling<PERSON> Bao", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Screen-capture tool can record a user's interaction with software and application content as a stream of screenshots which is usually stored in certain video format. Researchers have used screen-captured videos to study the programming activities that the developers carry out. In these studies, screen-captured videos had to be manually transcribed to extract software usage and application content data for the study purpose. This paper presents a computer-vision based video scraping tool (called scvRipper) that can automatically transcribe a screen-captured video into time-series interaction data according to the analyst's need. This tool can address the increasing need for automatic behavioral data collection methods in the studies of human aspects of software engineering.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.220"}, {"primary_key": "4441782", "vector": [], "sparse_vector": [], "title": "Interdisciplinary Design Patterns for Socially Aware Computing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The success of software applications that collect and process personal data does not only depend on technical aspects, but is also linked to social compatibility and user acceptance. It requires experts from different disciplines to ensure legal compliance, to foster the users' trust, to enhance the usability of the application and to finally realize the application. Multidisciplinary requirements have to be formulated, interwoven and implemented. We advocate the use of interdisciplinary design patterns that capture the design know-how of typical, recurring features in socially aware applications with particular concern for socio-technical requirements. The proposed patterns address interdisciplinary concerns in a tightly interwoven manner and are intended to facilitate the development of accepted and acceptable applications that in particular deal with sensitive user context information.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.180"}, {"primary_key": "4441785", "vector": [], "sparse_vector": [], "title": "SPF: A Middleware for Social Interaction in Mobile Proximity Environments.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Sam Guinea", "Valerio Panzica La Manna", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Smart interconnected devices are changing our lives and are turning conventional spaces into smart ones. Physical proximity, a key enabler of social interactions in the old days is not exploited by smart solutions, where the social dimension is always managed through the Internet. This paper aims to blend the two forces and proposes the idea of social smart space, where modern technologies can help regain and renew social interactions, and where proximity is seen as enabler for dedicated and customized functionality provided by users to users. A Social Proximity Framework (SPF) provides the basis for the creation of this new flavor of smart spaces. Two different versions of the SPF, based on different communication infrastructures, help explain the characteristics of the different components, and show how the SPF can benefit from emerging connection-less communication protocols. A first assessment of the two implementations concludes the paper.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.137"}, {"primary_key": "4441786", "vector": [], "sparse_vector": [], "title": "Efficient Scalable Verification of LTL Specifications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Linear Temporal Logic (LTL) has been used in computer science for decades to formally specify programs, systems, desired properties, and relevant behaviors. This paper presents a novel, efficient technique for verifying LTL specifications in a fully automated way. Our technique belongs to the category of Bounded Satisfiability Checking approaches, where LTL formulae are encoded as formulae of another decidable logic that can be solved through modern satisfiability solvers. The target logic in our approach is Bit-Vector Logic. We present our novel encoding, show its correctness, and experimentally compare it against existing encodings implemented in well-known formal verification tools.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.84"}, {"primary_key": "4441787", "vector": [], "sparse_vector": [], "title": "1st International Workshop on Big Data Software Engineering (BIGDSE 2015).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Big Data is about extracting valuable information from data in order to use it in intelligent ways such as to revolutionize decision-making in businesses, science and society. BIGDSE 2015 discusses the link between Big Data and software engineering and critically looks into issues such as cost-benefit of big data.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.308"}, {"primary_key": "4441789", "vector": [], "sparse_vector": [], "title": "Commit B<PERSON>bles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>-Hill"], "summary": "Developers who use version control are expected to produce systematic commit histories that show well-defined steps with logical forward progress. Existing version control tools assume that developers also write code systematically. Unfortunately, the process by which developers write source code is often evolutionary, or as-needed, rather than systematic. Our contribution is a fragment-oriented concept called Commit Bubbles that will allow developers to construct systematic commit histories that adhere to version control best practices with less cognitive effort, and in a way that integrates with their as-needed coding workflows.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.210"}, {"primary_key": "4441791", "vector": [], "sparse_vector": [], "title": "On the Role of Value Sensitive Concerns in Software Engineering Practice.", "authors": ["Balbir S. Barn", "<PERSON><PERSON>", "<PERSON>"], "summary": "The role of software systems on societal sustainability has generally not been the subject of substantive research activity. In this paper we examine the role of software engineering practice as an agent of change/impact for societal sustainability through the manifestation of value sensitive concerns. These concerns remain relatively neglected by software design processes except at early stages of user interface design. Here, we propose a conceptual model that can contribute to a translation of value sensitive design from its current focus in participatory design to one located in mainstream software engineering processes. Addressing this need will have an impact of societal sustainability and we outline some of the key research challenges for that journey.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.182"}, {"primary_key": "4441793", "vector": [], "sparse_vector": [], "title": "Helping Developers Help Themselves: Automatic Decomposition of Code Review Changesets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Code Reviews, an important and popular mechanism for quality assurance, are often performed on a change set, a set of modified files that are meant to be committed to a source repository as an atomic action. Understanding a code review is more difficult when the change set consists of multiple, independent, code differences. We introduce CLUSTERCHANGES, an automatic technique for decomposing change sets and evaluate its effectiveness through both a quantitative analysis and a qualitative user study.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.35"}, {"primary_key": "4441794", "vector": [], "sparse_vector": [], "title": "Bootstrapping Mobile App Development.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern IDEs provide limited support for developers when starting a new data-driven mobile app. App developers are currently required to write copious amounts of boilerplate code, scripts, organise complex directories, and author actual functionality. Although this scenario is ripe for automation, current tools are yet to address it adequately. In this paper we present RAPPT, a tool that generates the scaffolding of a mobile app based on a high level description specified in a Domain Specific Language (DSL). We demonstrate the feasibility of our approach by an example case study and feedback from a professional development team. Demo at: https://www.youtube.com/watch?v=ffquVgBYpLM.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.216"}, {"primary_key": "4441797", "vector": [], "sparse_vector": [], "title": "Sustainability Design and Software: The Karlskrona Manifesto.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sustainability has emerged as a broad concern for society. Many engineering disciplines have been grappling with challenges in how we sustain technical, social and ecological systems. In the software engineering community, for example, maintainability has been a concern for a long time. But too often, these issues are treated in isolation from one another. Misperceptions among practitioners and research communities persist, rooted in a lack of coherent understanding of sustainability, and how it relates to software systems research and practice. This article presents a cross-disciplinary initiative to create a common ground and a point of reference for the global community of research and practice in software and sustainability, to be used for effectively communicating key issues, goals, values and principles of sustainability design for software-intensive systems.The centrepiece of this effort is the Karlskrona Manifesto for Sustainability Design, a vehicle for a much needed conversation about sustainability within and beyond the software community, and an articulation of the fundamental principles underpinning design choices that affect sustainability. We describe the motivation for developing this manifesto, including some considerations of the genre of the manifesto as well as the dynamics of its creation. We illustrate the collaborative reflective writing process and present the current edition of the manifesto itself. We assess immediate implications and applications of the articulated principles, compare these to current practice, and suggest future steps.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.179"}, {"primary_key": "4441798", "vector": [], "sparse_vector": [], "title": "8th International Workshop on Cooperative and Human Aspects of Software Engineering (CHASE 2015).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> R. <PERSON>. <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Software is created for and with a wide range of stakeholders, from customers to management, from value-added providers to customer service personnel. These stakeholders work with teams of software engineers to develop and evolve software systems that support their activities. All of these people and their interactions are central to software development. Thus, it is crucial to investigate the dynamic and frequently changing Cooperative and Human Aspects of Software Engineering (CHASE), both before and after deployment, in order to understand current software practices, processes, and tools. In turn, this enables us to design tools and support mechanisms that improve software creation, software maintenance, and customer communication.Researchers and practitioners have long recognized the need to investigate these aspects, however, their articles are scattered across conferences and communities. This workshop will provide a unified forum for discussing high quality research studies, models, methods, and tools for human and cooperative aspects of software engineering. This will be the 8th in a series of workshops, which continue to be a meeting place for the academic, industrial, and practitioner communities interested in this area, and will give opportunities to present and discuss works-in-progress.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.309"}, {"primary_key": "4441801", "vector": [], "sparse_vector": [], "title": "How (Much) Do Developers Test?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "What do we know about software testing in the real world? It seems we know from <PERSON>' seminal work \"The Mythical Man-Month\" that 50% of project effort is spent on testing. However, due to the enormous advances in software engineering in the past 40 years, the question stands: Is this observation still true? In fact, was it ever true? The vision for our research is to settle the discussion about <PERSON>' estimation once and for all: How much do developers test? Does developers' estimation on how much they test match reality? How frequently do they execute their tests, and is there a relationship between test runtime and execution frequency? What are the typical reactions to failing tests? Do developers solve actual defects in the production code, or do they merely relax their test assertions? Emerging results from 40 software engineering students show that students overestimate their testing time threefold, and 50% of them test as little as 4% of their time, or less. Having proven the scalability of our infrastructure, we are now extending our case study with professional software engineers from open-source and industrial organizations.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.193"}, {"primary_key": "4441802", "vector": [], "sparse_vector": [], "title": "Symbolic Model Checking of Product-Line Requirements Using SAT-Based Methods.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Sterin", "<PERSON>", "<PERSON>"], "summary": "Product line (PL) engineering promotes the development of families of related products, where individual products are differentiated by which optional features they include. Modelling and analyzing requirements models of PLs allows for early detection and correction of requirements errors -- including unintended feature interactions, which are a serious problem in feature-rich systems. A key challenge in analyzing PL requirements is the efficient verification of the product family, given that the number of products is too large to be verified one at a time. Recently, it has been shown how the high-level design of an entire PL, that includes all possible products, can be compactly represented as a single model in the SMV language, and model checked using the NuSMV tool. The implementation in NuSMV uses BDDs, a method that has been outperformed by SAT-based algorithms. In this paper we develop PL model checking using two leading SAT-based symbolic model checking algorithms: IMC and IC3. We describe the algorithms, prove their correctness, and report on our implementation. Evaluating our methods on three PL models from the literature, we demonstrate an improvement of up to 3 orders of magnitude over the existing BDD-based method.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.40"}, {"primary_key": "4441803", "vector": [], "sparse_vector": [], "title": "A Unified Framework for the Comprehension of Software&apos;s Time.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The dimension of time in software appears in both program execution and software evolution. Much research has been devoted to the understanding of either program execution or software evolution, but these two research communities have developed tools and solutions exclusively in their respective context. In this paper, we claim that a common comprehension framework should apply to the time dimension of software. We formalize this as a meta-model that we instantiate and apply to the two different comprehension problems.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.203"}, {"primary_key": "4441806", "vector": [], "sparse_vector": [], "title": "Towards a Practical Security Analysis Methodology.", "authors": ["<PERSON>"], "summary": "The research community has proposed numerous techniques to perform security-oriented analyses based on a software design model. Such a formal analysis can provide precise security guarantees to the software designer, and facilitate the discovery of subtle flaws. Nevertheless, using such techniques in practice poses a big challenge for the average software designer, due to the narrow scope of each technique, the heterogeneous set of modelling languages that are required, and the analysis results that are often hard to interpret. Within the course of our research, we intend to provide practitioners with an integrated, easy-to-use modelling and analysis environment that enables them to work on a broad range of common security concerns without leaving the software design's level of abstraction.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.283"}, {"primary_key": "4441809", "vector": [], "sparse_vector": [], "title": "DIETs: Recommender Systems for Mobile API Developers.", "authors": ["<PERSON><PERSON>"], "summary": "The increasing number of posts related to mobile app development indicates unaddressed problems in the usage of mobile APIs. Arguing that these problems result from in- adequate documentation and shortcomings in the design and implementation of the APIs, the goal of this research is to develop and evaluate two developers' issues elimination tools (DIETs) for mobile API developers to diminish the problems of mobile applications (apps) development.After categorizing the problems, we investigate their causes, by exploring the relationships between the topics and trends of posts on Stack Overflow, the app developers' experience, the API and test code, and its changes. The results of these studies will be used to develop two DIETs that support API developers to improve the documentation, design, and implementation of their APIs.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.278"}, {"primary_key": "4441814", "vector": [], "sparse_vector": [], "title": "Post-Dominator Analysis for Precisely Handling Implicit Flows.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Most web applications today use JavaScript for including third-party scripts, advertisements etc., which pose a major security threat in the form of confidentiality and integrity violations. Dynamic information flow control helps address this issue of information stealing. Most of the approaches over-approximate when unstructured control flow comes into picture, thereby raising a lot of false alarms. We utilize the post-dominator analysis technique to determine the context of the program at a given point and prove that this approach is the most precise technique to handle implicit flows.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.250"}, {"primary_key": "4441815", "vector": [], "sparse_vector": [], "title": "4th International Workshop on Games and Software Engineering (GAS 2015).", "authors": ["<PERSON>", "<PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a summary of the 4th ICSE Workshop on Games and Software Engineering. The full day workshop is planned to include a keynote speaker, game-jam demonstration session, and paper presentations on game software engineering topics related to software engineering education, frameworks for game development and infrastructure, quality assurance, and model-based game development. The accepted papers are overviewed here.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.314"}, {"primary_key": "4441816", "vector": [], "sparse_vector": [], "title": "Code Hunt: Experience with Coding Contests at Scale.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mastering a complex skill like programming takes many hours. In order to encourage students to put in these hours, we built Code Hunt, a game that enables players to program against the computer with clues provided as unit tests. The game has become very popular and we are now running worldwide contests where students have a fixed amount of time to solve a set of puzzles. This paper describes Code Hunt and the contest experience it offers. We then show some early results that demonstrate how Code Hunt can accurately discriminate between good and bad coders. The challenges of creating and selecting puzzles for contests are covered. We end up with a short description of our course experience, and some figures that show that Code Hunt is enjoyed by women and men alike.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.172"}, {"primary_key": "4441817", "vector": [], "sparse_vector": [], "title": "2nd International Workshop on Software Engineering Research and Industrial Practice (SER&amp;IP 2015).", "authors": ["<PERSON>", "Rakesh <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Differing perceptions and expectations are obstaclesto collaboration between software engineering (SE) researchersand practitioners: Researchers often have a view thatpractitioners are reluctant to share real data. Practitionersbelieve that researchers are mostly working on topics which aredivorced from real industrial needs. Researchers believe thatpractitioners are looking for quick fixes. Practitioners have aview that case studies in research do not represent thecomplexities of real projects. Researchers may expect a few yearsto do research on a problem whereas practitioners expect a quicksolution that pays off immediately.Researchers and practitioners need to identify the gaps and todiscover the ways to collaborate to strengthen SE research andindustrial practice (IP). The main purpose of this workshop is tobring together researchers and practitioners to discuss thecurrent state of SE research and IP and to enhance collaborationbetween them. The SER&IP 2015 workshop provided a platformto share success stories of SE research-practice partnerships aswell as to discuss the challenges, through a day-long agenda ofkeynotes, paper presentations and round table discussions.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.352"}, {"primary_key": "4441819", "vector": [], "sparse_vector": [], "title": "2nd International Workshop on Requirements Engineering and Testing (RET 2015).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The RET (Requirements Engineering and Testing) workshop provides a meeting point for researchers and practitioners from the two separate fields of Requirements Engineering (RE) and Testing. The goal is to improve the connection and alignment of these two areas through an exchange of ideas, challenges, practices, experiences and results. The long term aim is to build a community and a body of knowledge within the intersection of RE and Testing. One of the main outputs of the 1st workshop was a collaboratively constructed map of the area of RET showing the topics relevant to RET for these. The 2nd workshop will continue in the same interactive vein and include a keynote, paper presentations with ample time for discussions, and a group exercise. For true impact and relevance this cross-cutting area requires contribution from both RE and Testing, and from both researchers and practitioners. For that reason we welcome a range of paper contributions from short experience papers to full research papers that both clearly cover connections between the two fields.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.351"}, {"primary_key": "4441820", "vector": [], "sparse_vector": [], "title": "2nd International Workshop on Context for Software Development (CSD 2015).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The goal of this one-day workshop is to bring together researchers interested in techniques and tools that leverage context information that accumulates around development activities. Developers continuously make use of context to make decisions, coordinate their work, understand the purpose behind their tasks, and understand how their tasks fit with the rest of the project. However, there is little research on defining what context is, how we can model it, and how we can use those models to better support software development at large. This workshop brings together scholars interested in identifying, gathering and modelling context information in software development, as well as discussing its applications.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.311"}, {"primary_key": "4441821", "vector": [], "sparse_vector": [], "title": "Coexecutability for Efficient Verification of Data Model Updates.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Modern applications use back-end data stores for persistent data. Automated verification of the code that updates the data store would prevent bugs that can cause loss or corruption of data. In this paper, we focus on the most challenging part of this problem: automated verification of code that updates the data store and contains loops. Due to dependencies between loop iterations, verification of code that contains loops is a hard problem, and typically requires manual assistance in the form of loop invariants. We present a fully automated technique that improves verifiability of loops. We first define co execution, a method for modeling loop iterations that simplifies automated reasoning about loops. Then, we present a fully automated static program analysis that detects whether the behavior of a given loop can be modeled using co execution. We provide a customized verification technique for co executable loops that results in more effective verification. In our experiments we observed that, in 45% of cases, modeling loops using co execution reduces verification time between 1 and 4 orders of magnitude. In addition, the rate of inconclusive verification results in the presence of loops is reduced from 65% down to 24%, all without requiring loop invariants or any manual intervention.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.87"}, {"primary_key": "4441824", "vector": [], "sparse_vector": [], "title": "System Thinking: Educating T-Shaped Software Engineers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With respect to system thinking, a T-shaped person is one who has technical depth in at least one aspect of the system's content, and a workable level of understanding of a fair number of the other system aspects. Many pure computer science graduates are strongly I-shaped, with a great deal of depth in software technology, but little understanding of the other disciplines involved in such areas as business, medicine, transportation, or Internets of Things. This leaves them poorly prepared to participate in the increasing numbers of projects involving multi-discipline system thinking, and in strong need of software skills. We have developed and evolved an MS-level software engineering curriculum that enables CS majors to become considerably more T-shaped than when they entered. It includes courses in software management and economics, human-computer interaction, embedded software systems, systems and software requirements, architecture, and V&V, and a two-semester, real-client team project course that gives students experience in applying these skills. We find via feedback on the students' internships and job experiences that they and their employers have high rates of success in job offers and job performance.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.166"}, {"primary_key": "4441827", "vector": [], "sparse_vector": [], "title": "The Future of Software Engineering (SEIP Keynote).", "authors": ["<PERSON>"], "summary": "Summary form only given. No matter what future we may envision, it relies on software that has not yet been written. Even now, software-intensive systems have woven themselves into the interstitial spaces of civilization, and we as individuals and as a species have slowly surrendered ourselves to computing. Looking back, we can identify several major and distinct styles whereby we have built such systems. We have come a long way, and even today, we certainly can name a number of best practices for software development that yield systems of quality. However, by no means can we stand still: the nature of the systems we build continues to change, and as they collectively weave themselves into our live, we must attend not only to the technical elements of software development, we must also attend to human needs. In this presentation we will look at the history of software engineering and offer some grand challenges for the future.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.128"}, {"primary_key": "4441829", "vector": [], "sparse_vector": [], "title": "Poster: Is <PERSON> Better than <PERSON>? Testing the Exploratory Tester Using HCI Techniques.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Exploratory software testing is an activity which can be carried out by both untrained and formally trained testers. In this paper, we propose using Human Computer Interaction (HCI) techniques to carry out a study of exploratory testing strategies used by the two groups of testers. This data will be used to make recommendations to companies with regards to the mix of skills and training required for testing teams.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.262"}, {"primary_key": "4441837", "vector": [], "sparse_vector": [], "title": "1st International Workshop on Software Engineering for Smart Cyber-Physical Systems (SEsCPS 2015).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cyber-physical system (CPS) have been recognized as a top-priority in research and development. The innovations sought for CPS demand them to deal effectively with dynamicity of their environment, to be scalable, adaptive, tolerant to threats, etc. -- i.e. they have to be smart. Although approaches in software engineering (SE) exist that individually meet these demands, their synergy to address the challenges of smart CPS (sCPS) in a holistic manner remains an open challenge. The workshop focuses on software engineering challenges for sCPS. The goals are to increase the understanding of problems of SE for sCPS, study foundational principles for engineering sCPS, and identify promising SE solutions for sCPS. Based on these goals, the workshop aims to formulate a research agenda for SE of sCPS.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.326"}, {"primary_key": "4441841", "vector": [], "sparse_vector": [], "title": "Merits of Organizational Metrics in Defect Prediction: An Industrial Replication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Defect prediction models presented in the literature lack generalization unless the original study can be replicated using new datasets and in different organizational settings. Practitioners can also benefit from replicating studies in their own environment by gaining insights and comparing their findings with those reported. In this work, we replicated an earlier study in order to investigate the merits of organizational metrics in building defect prediction models for large-scale enterprise software. We mined the organizational, code complexity, code churn and pre-release bug metrics of that large scale software and built defect prediction models for each metric set. In the original study, organizational metrics were found to achieve the highest performance. In our case, models based on organizational metrics performed better than models based on churn metrics but were outperformed by pre-release metric models. Further, we verified four individual organizational metrics as indicators for defects. We conclude that the performance of different metric sets in building defect prediction models depends on the project's characteristics and the targeted prediction level. Our replication of earlier research enabled assessing the validity and limitations of organizational metrics in a different context.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.138"}, {"primary_key": "4441844", "vector": [], "sparse_vector": [], "title": "Using GSwE2009 for the Evaluation of a Master Degree in Software Engineering in the Universidad de la República.", "authors": ["<PERSON>", "Diego <PERSON>", "<PERSON>"], "summary": "This paper presents an adoption and adaptation of the Curriculum Guidelines for Graduate Degree Programs in Software Engineering (GSwE2009) proposed by the IEEE-CS and the ACM for the creation of a curriculum for a Master's degree in software engineering at the Universidad de la República (Uruguay). A method for evaluating contents and its application is also presented. This evaluation allows us to know the obtained thematic coverage, effort and balance. It also provides information that enables the detection of numerous opportunities for the improvement in the implementation of the program.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.165"}, {"primary_key": "4441845", "vector": [], "sparse_vector": [], "title": "A Unified Approach to Automatic Testing of Architectural Constraints.", "authors": ["<PERSON>"], "summary": "Architectural decisions are often encoded in the form of constraints and guidelines. Non-functional requirements can be ensured by checking the conformance of the implementation against this kind of invariant. Conformance checking is often a costly and error-prone process that involves the use of multiple tools, differing in effectiveness, complexity and scope of applicability. To reduce the overall effort entailed by this activity, we propose a novel approach that supports verification of human-readable declarative rules through the use of adapted off-the-shelf tools. Our approach consists of a rule specification DSL, called Dicto, and a tool coordination framework, called Probo. The approach has been implemented in a soon to be evaluated prototype.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.281"}, {"primary_key": "4441849", "vector": [], "sparse_vector": [], "title": "SE4HPCS&apos;15: The 2015 International Workshop on Software Engineering for High Performance Computing in Science.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "HPC software is developed and used in a wide variety of scientific domains including nuclear physics, computational chemistry, crash simulation, satellite data processing, fluid dynamics, climate modeling, bioinformatics, and vehicle development. The increase in the importance of this software motivates the need to identify and understand appropriate software engineering (SE) practices for HPC architectures. Because of the variety of the scientific domains addressed using HPC, existing SE tools and techniques developed for the business/IT community are often not efficient or effective. Appropriate SE solutions must account for the salient characteristics of the HPC, research oriented development environment. This situation creates a need for members of the SE community to interact with members of the scientific and HPC communities to address this need. This workshop facilitates that collaboration by bringing together members of the SE, the scientific, and the HPC communities to share perspectives and present findings relevant to research, practice, and education. A significant portion of the workshop is devoted to focused interaction among the participants with the goal of generating a research agenda to improve tools, techniques, and experimental methods regarding SE for HPC science.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.324"}, {"primary_key": "4441850", "vector": [], "sparse_vector": [], "title": "Measuring Software Redundancy.", "authors": ["Antonio <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Redundancy is the presence of different elements with the same functionality. In software, redundancy is useful (and used) in many ways, for example for fault tolerance and reliability engineering, and in self-adaptive and self-checking programs. However, despite the many uses, we still do not know how to measure software redundancy to support a proper and effective design. If, for instance, the goal is to improve reliability, one might want to measure the redundancy of a solution to then estimate the reliability gained with that solution. Or one might compare alternative solutions to choose the one that expresses more redundancy and therefore, presumably, more reliability. We first formalize a notion of redundancy whereby two code fragments are considered redundant when they achieve the same functionality with different executions. On the basis of this abstract and general notion, we then develop a concrete method to obtain a meaningful quantitative measure of software redundancy. The results we obtain are very positive: we show, through an extensive experimental analysis, that it is possible to distinguish code that is only minimally different, from truly redundant code, and that it is even possible to distinguish low-level code redundancy from high-level algorithmic redundancy. We also show that the measurement is significant and useful for the designer, as it can help predict the effectiveness of techniques that exploit redundancy.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.37"}, {"primary_key": "4441852", "vector": [], "sparse_vector": [], "title": "Assert Use in GitHub Projects.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Ray"], "summary": "Asserts have long been a strongly recommended (if non-functional) adjunct to programs. They certainly don't add any user-evident feature value; and it can take quite some skill and effort to devise and add useful asserts. However, they are believed to add considerable value to the developer. Certainly, they can help with automated verification; but even in the absence of that, claimed advantages include improved understandability, maintainability, easier fault localization and diagnosis, all eventually leading to better software quality. We focus on this latter claim, and use a large dataset of asserts in C and C++ programs to explore the connection between asserts and defect occurrence. Our data suggests a connection: functions with asserts do have significantly fewer defects. This indicates that asserts do play an important role in software quality; we therefore explored further the factors that play a role in assertion placement: specifically, process factors (such as developer experience and ownership) and product factors, particularly interprocedural factors, exploring how the placement of assertions in functions are influenced by local and global network properties of the callgraph. Finally, we also conduct a differential analysis of assertion use across different application domains.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.88"}, {"primary_key": "4441853", "vector": [], "sparse_vector": [], "title": "On the Architecture-Driven Development of Software-Intensive Systems-of-Systems.", "authors": ["Everton Cavalcante"], "summary": "Nowadays, complex software-intensive systems have resulted from the integration of heterogeneous independent systems, thus leading to a new class of systems called Systems-of-Systems (SoS). As in any system, SoS architectures have been regarded as an important element for determining their success. However, the state of the art reveals shortcomings that contribute to compromise the quality of these systems, as their inherent characteristics (such as emergent behavior and evolutionary development) are often not properly addressed. In this context, this PhD research aims at investigating how SoS software architectures can be used to model and evolve these systems. As main contribution, an architecture-centric approach for developing software-intensive SoS with focus on the formal specification and dynamic reconfiguration of their architectures is proposed. Such an approach mainly intends to contribute to fill some of the relevant existing gaps regarding the development of software-intensive SoS driven by their software architectures.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.287"}, {"primary_key": "4441861", "vector": [], "sparse_vector": [], "title": "StressCloud: A Tool for Analysing Performance and Energy Consumption of Cloud Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Finding the best deployment configuration that maximises energy efficiency while guaranteeing system performance of cloud applications is an extremely challenging task. It requires the evaluation of system performance and energy consumption under a wide variety of realistic workloads and deployment configurations. This paper demonstrates StressCloud, an automatic performance and energy consumption analysis tool for cloud applications in real-world cloud environments. StressCloud supports 1) the modelling of realistic cloud application workloads, 2) the automatic generation and running of load tests, and 3) the profiling of system performance and energy consumption.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.232"}, {"primary_key": "4441863", "vector": [], "sparse_vector": [], "title": "Engineering Sustainability Through Language.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As our understanding and care for sustainability concerns increases, so does the demand for incorporating these concerns into software. Yet, existing programming language constructs are not well-aligned with concepts of the sustainability domain. This undermines what we term technical sustainability of the software due to (i) increased complexity in programming of such concerns and (ii) continuous code changes to keep up with changes in (environmental, social, legal and other) sustainability-related requirements. In this paper we present a proof-of-concept approach on how technical sustainability support for new and existing concerns can be provided through flexible language-level programming. We propose to incorporate sustainability-related behaviour into programs through micro-languages enabling such behaviour to be updated and/or redefined as and when required.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.183"}, {"primary_key": "4441866", "vector": [], "sparse_vector": [], "title": "Poster: Dynamic Analysis Using JavaScript Proxies.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "JavaScript has become a popular programming language. However, its highly dynamic nature encumbers static analysis for quality assurance purposes. Only dynamic techniques such as concolic testing seem to cope. Often, these involve an instrumentation phase in which source code is extended with analysis-specific concerns. The corresponding implementations represent a duplication of engineering efforts. To facilitate developing dynamic analyses for JavaScript, we introduce Ara<PERSON>; a general-purpose JavaScript instrumenter that takes advantage of proxies, a recent addition to the JavaScript reflection APIs.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.261"}, {"primary_key": "4441868", "vector": [], "sparse_vector": [], "title": "Information Transformation: An Underpinning Theory for Software Engineering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software engineering lacks underpinning scientific theories both for the software it produces and the processes by which it does so. We propose that an approach based on information theory can provide such a theory, or rather many theories. We envision that such a benefit will be realised primarily through research based on the quantification of information involved and a mathematical study of the limiting laws that arise. However, we also argue that less formal but more qualitative uses for information theory will be useful. The main argument in support of our vision is based on the fact that both a program and an engineering process to develop such a program are fundamentally processes that transform information. To illustrate our argument we focus on software testing and develop an initial theory in which a test suite is input/output adequate if it achieves the channel capacity of the program as measured by the mutual information between its inputs and its outputs. We outline a number of problems, metrics and concrete strategies for improving software engineering, based on information theoretical analyses. We find it likely that similar analyses and subsequent future research to detail them would be generally fruitful for software engineering.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.202"}, {"primary_key": "4441871", "vector": [], "sparse_vector": [], "title": "<PERSON>: Using Ghosts to Debug Null Deferences with Dynamic Causality Traces.", "authors": ["<PERSON><PERSON>"], "summary": "Fixing software errors requires understanding their root cause. In this paper, we introduce \"causality traces'', they are specially crafted execution traces augmented with the information needed to reconstruct a causal chain from a root cause to an execution error. We propose an approach and a tool, called Casper, for dynamically constructing causality traces for null dereference errors. The core idea of <PERSON> is to inject special values, called \"ghosts\", into the execution stream to construct the causality trace at runtime. We evaluate our contribution by providing and assessing the causality traces of 14 real null dereference bugs collected over six large, popular open-source projects.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.251"}, {"primary_key": "4441873", "vector": [], "sparse_vector": [], "title": "6th International Workshop on Emerging Trends in Software Metrics (WETSoM 2015).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "WETSoM is a gathering of researchers and practitioners to discuss the progress on software metrics knowledge. Motivations for this workshop include the low impact that software metrics have on current software development and the increased interest in research. The goals of this workshop include critically examining the evidence for the effectiveness of existing metrics and identifying new directions for metrics. Evidence for existing metrics includes how the metrics have been used in practice and studies showing their effectiveness. Identifying new directions includes use of new theories, such as complex network theory, on which to base metrics.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.347"}, {"primary_key": "4441874", "vector": [], "sparse_vector": [], "title": "Measuring Dependency Freshness in Software Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> C. J. D<PERSON>", "<PERSON><PERSON>"], "summary": "Modern software systems often make use of third-party components to speed-up development and reduce maintenance costs. In return, developers need to update to new releases of these dependencies to avoid, for example, security and compatibility risks. In practice, prioritizing these updates is difficult because the use of outdated dependencies is often opaque. In this paper we aim to make this concept more transparent by introducing metrics to quantify the use of recent versions of dependencies, i.e. The system's \"dependency freshness\". We propose and investigate a system-level metric based on an industry benchmark. We validate the usefulness of the metric using interviews, analyze the variance of the metric through time, and investigate the relationship between outdated dependencies and security vulnerabilities. The results show that the measurements are considered useful, and that systems using outdated dependencies four times as likely to have security issues as opposed to systems that are up-to-date.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.140"}, {"primary_key": "4441875", "vector": [], "sparse_vector": [], "title": "Code Reviews Do Not Find Bugs. How the Current Code Review Best Practice Slows Us Down.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Because of its many uses and benefits, code reviews are a standard part of the modern software engineering workflow. Since they require involvement of people, code reviewing is often the longest part of the code integration activities. Using experience gained at Microsoft and with support of data, we posit (1) that code reviews often do not find functionality issues that should block a code submission; (2) that effective code reviews should be performed by people with specific set of skills; and (3) that the social aspect of code reviews cannot be ignored. We find that we need to be more sophisticated with our guidelines for the code review workflow. We show how our findings from code reviewing practice influence our code review tools at Microsoft. Finally, we assert that, due to its costs, code reviewing practice is a topic deserving to be better understood, systematized and applied to software engineering workflow with more precision than the best practice currently prescribes.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.131"}, {"primary_key": "4441876", "vector": [], "sparse_vector": [], "title": "Mining Software Repositories for Social Norms.", "authors": ["Hoa Khanh Dam", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Social norms facilitate coordination and cooperation among individuals, thus enable smoother functioning of social groups such as the highly distributed and diverse open source software development (OSSD) communities. In these communities, norms are mostly implicit and hidden in huge records of human-interaction information such as emails, discussions threads, bug reports, commit messages and even source code. This paper aims to introduce a new line of research on extracting social norms from the rich data available in software repositories. Initial results include a study of coding convention violations in JEdit, Argo UML and Glassfish projects. It also presents a new life-cycle model for norms in OSSD communities and demonstrates how a number of norms extracted from the Python development community follow this life-cycle model.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.209"}, {"primary_key": "4441884", "vector": [], "sparse_vector": [], "title": "Dynamic Data Flow Testing of Object Oriented Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data flow testing has recently attracted new interest in the context of testing object oriented systems, since data flow information is well suited to capture relations among the object states, and can thus provide useful information for testing method interactions. Unfortunately, classic data flow testing, which is based on static analysis of the source code, fails to identify many important data flow relations due to the dynamic nature of object oriented systems. In this paper, we propose a new technique to generate test cases for object oriented software. The technique exploits useful inter-procedural data flow information extracted dynamically from execution traces for object oriented systems. The technique is designed to enhance an initial test suite with test cases that exercise complex state based method interactions. The experimental results indicate that dynamic data flow testing can indeed generate test cases that exercise relevant behaviors otherwise missed by both the original test suite and by test suites that satisfy classic data flow criteria.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.104"}, {"primary_key": "4441885", "vector": [], "sparse_vector": [], "title": "Dynamic Safety Cases for Through-Life Safety Assurance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We describe dynamic safety cases, a novel operationalization of the concept of through-life safety assurance, whose goal is to enable proactive safety management. Using an example from the aviation systems domain, we motivate our approach, its underlying principles, and a lifecycle. We then identify the key elements required to move towards a formalization of the associated framework.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.199"}, {"primary_key": "4441886", "vector": [], "sparse_vector": [], "title": "New Initiative: The Naturalness of Software.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This paper describes a new research consortium, studying the Naturalness of Software. This initiative is supported by a pair of grants by the US National Science Foundation, totaling $2,600,000: the first, exploratory (\"EAGER\") grant of $600,000 helped kickstart an inter-disciplinary effort, and demonstrate feasibility; a follow-on full grant of $2,000,000 was recently awarded. The initiative is led by the author, who is at UC Davis, and includes investigators from Iowa State University and Carnegie-Mellon University (Language Technologies Institute).", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.190"}, {"primary_key": "4441887", "vector": [], "sparse_vector": [], "title": "Poster: VIBeS, Transition System Mutation Made Easy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mutation testing is an established technique used to evaluate the quality of a set of test cases. As model-based testing took momentum, mutation techniques were lifted to the model level. However, as for code mutation analysis, assessing test cases on a large set of mutants can be costly. In this paper, we introduce the Variability-Intensive Behavioural teSting (VIBeS) framework. Relying on Featured Transition Systems (FTSs), we represent all possible mutants in a single model constrained by a feature model for mutant (in)activation. This allow to assess all mutants in a single test case execution. We present VIBeS implementation steps and the DSL we defined to ease model-based mutation analysis.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.263"}, {"primary_key": "4441889", "vector": [], "sparse_vector": [], "title": "Automated Data Structure Generation: Refuting Common Wisdom.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Common wisdom in the automated data structure generation community states that declarative techniques have better usability than imperative techniques, while imperative techniques have better performance. We show that this reasoning is fundamentally flawed: if we go to the declarative limit and employ constraint logic programming (CLP), the CLP data structure generation has orders of magnitude better performance than comparable imperative techniques. Conversely, we observe and argue that when it comes to realistically complex data structures and properties, the CLP specifications become more obscure, indirect, and difficult to implement and understand than their imperative counterparts. We empirically evaluate three competing generation techniques, CLP, Korat, and UDITA, to validate these observations on more complex and interesting data structures than any prior work in this area. We explain why these observations are true, and discuss possible techniques for attaining the best of both worlds.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.26"}, {"primary_key": "4441890", "vector": [], "sparse_vector": [], "title": "Correctness and Relative Correctness.", "authors": ["<PERSON><PERSON>", "<PERSON>d <PERSON>", "<PERSON>"], "summary": "In the process of trying to define what is a software fault, we have foundthat to formally define software faults we need to introduce the conceptof relative correctness, i.e. the property of a program to be more-correctthan another with respect to a given specification. A feature of a programis a fault (for a given specification)only because there exists an alternative to it that would makethe program more-correct with respect to the specification.In this paper, we explore applications of the concept of relative correctness in programtesting, program repair, and program design.Specifically, we argue that in many situations of software testing,fault removal and program repair, testing for relative correctnessrather than absolute correctness leads to clearer conclusions andbetter outcomes. Also, we find that designing programs by stepwisecorrectness-enhancing transformations rather than by stepwise correctness-preserving refinements leads to simpler programs and is more tolerant of designer mistakes.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.200"}, {"primary_key": "4441893", "vector": [], "sparse_vector": [], "title": "Safe Evolution Patterns for Software Product Lines.", "authors": ["<PERSON>"], "summary": "Despite a global recognition of the problem, and massive investment from researchers and practitioners, the evolution of complex software systems is still a major challenge for today's architects and developers. In the context of product lines, or highly configurable systems, variability in the implementation and design makes many of the pre-existing challenges even more difficult to tackle. Many approaches and tools have been designed, but developers still miss the tools and methods enabling safe evolution of complex, variable systems. In this paper, we present our research plans toward this goal: making the evolution of software product lines safer. We show, by use of two concrete examples of changes that occurred in Linux, that simple heuristics can be applied to facilitate change comprehension and avoid common mistakes, without relying on heavy tooling. Based on those observations, we present the steps we intend to take to build a framework to regroup and classify changes, run simple checks, and eventually increase the quality of code deliveries affecting the variability model, mapping and implementation of software product lines.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.282"}, {"primary_key": "4441894", "vector": [], "sparse_vector": [], "title": "FormTester: Effective Integration of Model-Based and Manually Specified Test Cases.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Whilst Model Based Testing (MBT) is an improvement over manual test specification, the leap from it to MBT can be hard. Only recently MBT tools for Web applications have emerged that can recover models from existing manually specified test cases. However, there are further requirements for supporting both MBT and manually specified tests. First, we need support for the generation of test initialization procedures. Also, we want to identify areas of the system that are not testable due to defects. We present Form Tester, a new MBT tool addressing these limitations. An evaluation with real Web applications shows that Form Tester helps to reduce the time spent on developing test cases.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.237"}, {"primary_key": "4441895", "vector": [], "sparse_vector": [], "title": "Avoiding Security Pitfalls with Functional Programming: A Report on the Development of a Secure XML Validator.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "While the use of XML is pervading all areas of IT, security challenges arise when XML files are used to transfer security data such as security policies. To tackle this issue, we have developed a lightweight secure XML validator and have chosen to base the development on the strongly typed functional language OCaml. The initial development took place as part of the LaFoSec Study which aimed at investigating the impact of using functional languages for security. We then turned the validator into an industrial application, which was successfully evaluated at EAL4+ level by independent assessors. In this paper, we explain the challenges involved in processing XML data in a critical context, we describe our choices in designing a secure XML validator, and we detail how we used features of functional languages to enforce security requirements.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.149"}, {"primary_key": "4441902", "vector": [], "sparse_vector": [], "title": "Agile Project Management: From Self-Managing Teams to Large-Scale Development.", "authors": ["<PERSON><PERSON>", "Torgeir <PERSON>ø<PERSON>"], "summary": "Agile software development represents a new approach for planning and managing software projects. It puts less emphasis on up-front plans and strict control and relies more on informal collaboration, coordination, and learning. This briefing provides a characterization and definition of agile project management based on extensive studies of large-scale industrial projects. It explains the circumstances behind the change from traditional management with its focus on direct supervision and standardization of work processes, to the newer, agile focus on self-managing teams, including its opportunities and benefits, but also its complexity and challenges. The main focus of the briefing is the four principles of agile project management: minimum critical specification, autonomous teams, redundancy, and feedback and learning. The briefing is intended for researchers, practitioners and educators in software engineering, especially project managers. For researchers, an updated state of the art will be uncovered, and the presentation will be based on current best evidence. For practitioners, principles, processes, and key success factors will be outlined and a successful large-scale case study of agile project management will be presented. For educators, the briefing will provide the basis for developing course material.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.299"}, {"primary_key": "4441905", "vector": [], "sparse_vector": [], "title": "Virtual Reality in Software Engineering: Affordances, Applications, and Challenges.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software engineers primarily interact with source code using a keyboard and mouse, and typically view software on a small number of 2D monitors. This interaction paradigm does not take advantage of many affordances of natural human movement and perception. Virtual reality (VR) can use these affordances more fully than existing developer environments to enable new creative opportunities and potentially result in higher productivity, lower learning curves, and increased user satisfaction. This paper describes the affordances offered by VR, demonstrates the benefits of VR and software engineering in prototypes for live coding and code review, and discusses future work, open questions, and the challenges of VR.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.191"}, {"primary_key": "4441906", "vector": [], "sparse_vector": [], "title": "Automatic Categorization of Software Libraries Using Bytecode.", "authors": ["<PERSON><PERSON>"], "summary": "Automatic software categorization is the task of assigning categories or tags to software libraries in order to summarize their functionality. Correctly assigning these categories is essential to ensure that relevant libraries can be easily retrieved by developers from large repositories. Current categorization approaches rely on the semantics reflected in the source code, or use supervised machine learning techniques, which require a set of labeled software as a training data. These approaches fail when such information is not available. We propose a novel unsupervised approach for the automatic categorization of Java libraries, which uses the bytecode of a library in order to determine its category. We show that the approach is able to successfully categorize libraries from the Apache Foundation Repository.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.249"}, {"primary_key": "4441908", "vector": [], "sparse_vector": [], "title": "Improving Predictability, Efficiency and Trust of Model-Based Proof Activity.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Florent <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We report on our industrial experience in using formal methods for the analysis of safety-critical systems developed in a model-based design framework. We first highlight the formal proof workflow devised for the verification and validation of embedded systems developed in Matlab/Simulink. In particular, we show that there is a need to: determine the compatibility of the model to be analysed with the proof engine, establish whether the model facilitates proof convergence or when optimisation is required, and avoid over-specification when specifying the hypotheses constraining the inputs of the model during analysis. We also stress on the importance of having a certain harness over the proof activity and present a set of tools we developed to achieve this purpose. Finally, we give a list of best practices, methods and any necessary tools aiming at guaranteeing the validity of the verification results obtained.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.142"}, {"primary_key": "4441909", "vector": [], "sparse_vector": [], "title": "1st International Workshop on Software Protection (SPRO 2015).", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "There are many reasons to protect software: your banking app needs to be protected to prevent fraud; software operating on critical infrastructures needs to be protected against vulnerability discovery; software vendors and service companies need it to protect their business; etc. In the past decade, many techniques to protect software have been presented and broken. Beyond making individual techniques better, the challenge includes to be able to deploy them in practice and be able to evaluate them. This is the objective of SPRO, the first International Workshop on Software Protection: to bring together researchers and industrial practitioners both from software protection and the wider software engineering community to share experience and provide directions for future research, in order to stimulate the use of software engineering techniques in novel aspects of software protection. This first edition of the workshop is held at ICSE 2015 in Florence (Italy) with the aim of creating a community working in this new growing area of security, and to highlight its synergies with different research fields of software engineering, like: formal models, program analysis, reverse engineering, code transformations, empirical evaluation, and software metrics. This paper presents the research themes and challenges of the workshop, describes the workshop organization, and summarizes the research papers.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.328"}, {"primary_key": "4441910", "vector": [], "sparse_vector": [], "title": "Evolution of Software Development Strategies.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Nickolas J<PERSON>"], "summary": "The development of discipline-specific cognitive and meta-cognitive skills is fundamental to the successful mastery of software development skills and processes. This development happens over time and is influenced by many factors, however its understanding by teachers is crucial in order to develop activities and materials to transform students from novice to expert software engineers. In this paper, we analyse the evolution of learning strategies of novice, first year students, to expert, final year students. We analyse reflections on software development processes from students in an introductory software development course, and compare them to those of final year students, in a distributed systems development course. Our study shows that computer science - specific strategies evolve as expected, with the majority of final year students including design before coding in their software development process, but that several areas still require scaffolding activities to assist in learning development.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.153"}, {"primary_key": "4441911", "vector": [], "sparse_vector": [], "title": "MU-MMINT: An IDE for Model Uncertainty.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Alessio <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Developers have to work with ever-present design-time uncertainty, i.e., Uncertainty about selecting among alternative design decisions. However, existing tools do not support working in the presence of uncertainty, forcing developers to either make provisional, premature decisions, or to avoid using the tools altogether until uncertainty is resolved. In this paper, we present a tool, called MU-MMINT, that allows developers to express their uncertainty within software artifacts and perform a variety of model management tasks such as reasoning, transformation and refinement in an interactive environment. In turn, this allows developers to defer the resolution of uncertainty, thus avoiding having to undo provisional decisions. See the companion video: http://youtu.be/kAWUm-iFatM.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.226"}, {"primary_key": "4441913", "vector": [], "sparse_vector": [], "title": "AutoCSP: Automatically Retrofitting CSP to Web Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Web applications often handle sensitive user data, which makes them attractive targets for attacks such as cross-site scripting (XSS). Content security policy (CSP) is a content-restriction mechanism, now supported by all major browsers, that offers thorough protection against XSS. Unfortunately, simply enabling CSP for a web application would affect the application's behavior and likely disrupt its functionality. To address this issue, we propose AutoCSP, an automated technique for retrofitting CSP to web applications. AutoCSP (1) leverages dynamic taint analysis to identify which content should be allowed to load on the dynamically-generated HTML pages of a web application and (2) automatically modifies the server-side code to generate such pages with the right permissions. Our evaluation, performed on a set of real-world web applications, shows that AutoCSP can retrofit CSP effectively and efficiently.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.53"}, {"primary_key": "4441918", "vector": [], "sparse_vector": [], "title": "Lightweight Adaptive Filtering for Efficient Learning and Updating of Probabilistic Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Adaptive software systems are designed to cope with unpredictable and evolving usage behaviors and environmental conditions. For these systems reasoning mechanisms are needed to drive evolution, which are usually based on models capturing relevant aspects of the running software. The continuous update of these models in evolving environments requires efficient learning procedures, having low overhead and being robust to changes. Most of the available approaches achieve one of these goals at the price of the other. In this paper we propose a lightweight adaptive filter to accurately learn time-varying transition probabilities of discrete time Markov models, which provides robustness to noise and fast adaptation to changes with a very low overhead. A formal stability, unbiasedness and consistency assessment of the learning approach is provided, as well as an experimental comparison with state-of-the-art alternatives.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.41"}, {"primary_key": "4441923", "vector": [], "sparse_vector": [], "title": "Poster: Filtering Code Smells Detection Results.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many tools for code smell detection have been devel- oped, providing often different results. This is due to the informal definition of code smells and to the subjective interpretation of them. Usually, aspects related to the domain, size, and design of the system are not taken into account when detecting and analyzing smells. These aspects can be used to filter out the noise and achieve more relevant results. In this paper, we propose different filters that we have identified for five code smells. We provide two kind of filters, Strong and Weak Filters, that can be integrated as part of a detection approach.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.256"}, {"primary_key": "4441925", "vector": [], "sparse_vector": [], "title": "An Industrial Case Study on the Automated Detection of Performance Regressions in Heterogeneous Environments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A key goal of performance testing is the detection of performance degradations (i.e., regressions) compared to previous releases. Prior research has proposed the automation of such analysis through the mining of historical performance data (e.g., CPU and memory usage) from prior test runs. Nevertheless, such research has had limited adoption in practice. Working with a large industrial performance testing lab, we noted that a major hurdle in the adoption of prior work (including our own work) is the incorrect assumption that prior tests are always executed in the same environment (i.e., labs). All too often, tests are performed in heterogenous environments with each test being run in a possibly different lab with different hardware and software configurations. To make automated performance regression analysis techniques work in industry, we propose to model the global expected behaviour of a system as an ensemble (combination) of individual models, one for each successful previous test run (and hence configuration). The ensemble of models of prior test runs are used to flag performance deviations (e.g., CPU counters showing higher usage) in new tests. The deviations are then aggregated using simple voting or more advanced weighting to determine whether the counters really deviate from the expected behaviour or whether it was simply due to an environment-specific variation. Case studies on two open-source systems and a very large scale industrial application show that our weighting approach outperforms a state-of-the-art environment-agnostic approach. Feedback from practitioners who used our approach over a 4 year period (across several major versions) has been very positive.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.144"}, {"primary_key": "4441928", "vector": [], "sparse_vector": [], "title": "Software Requirements Patterns - A State of the Art and the Practice.", "authors": ["<PERSON>"], "summary": "Software requirement patterns are an increasingly popular approach to knowledge reuse in the requirements engineering phase. Several research proposals have been formulated in the last years, and this technical briefing presents them. Beyond that, a report on the current adoption of these proposals (or any other ad-hoc approach) in industry is presented. This state of the practice will show that the need to pave the road to successful adoption still persists.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.298"}, {"primary_key": "4441929", "vector": [], "sparse_vector": [], "title": "3rd International Workshop on Conducting Empirical Studies in Industry (CESI 2015).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Few would deny today the importance of empirical studies in the field of Software Engineering (SE) and, indeed, an increasing number of studies are being conducted involving the software industry. While literature abounds on empirical procedures, relatively little is known about the dynamics and complexity of conducting empirical studies in the software industry. What are the impediments and how to best handle them? This driver underlies the organisation of the third in a series of workshops, CESI 2015. Apart from structured presentations and discussions from academic and industry participants, this workshop (like predecessor workshops) includes a \"wall of ideas\" session where all participants asynchronously post their ideas on the wall, literally, which are then analysed. As a tangible output, the workshop's discussions will be summarised in a post-workshop report.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.345"}, {"primary_key": "4441930", "vector": [], "sparse_vector": [], "title": "CACHECA: A Cache Language Model Based Code Suggestion Tool.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Nearly every Integrated Development Environment includes a form of code completion. The suggested completions (\"suggestions\") are typically based on information available at compile time, such as type signatures and variables in scope. A statistical approach, based on estimated models of code patterns in large code corpora, has been demonstrated to be effective at predicting tokens given a context. In this demo, we present CACHECA, an Eclipse plug in that combines the native suggestions with a statistical suggestion regime. We demonstrate that a combination of the two approaches more than doubles Eclipse's suggestion accuracy. A video demonstration is available at https://www.youtube.com/watch?v=3INk0N3JNtc.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.228"}, {"primary_key": "4441933", "vector": [], "sparse_vector": [], "title": "2nd International Workshop on Crowd Sourcing in Software Engineering (CSI-SE 2015).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Crowdsourcing is increasingly revolutionizing the ways in which software is engineered. Programmers increasingly crowdsource answering their questions through Q&A sites. Non-programmers may contribute human-intelligence to development projects, by, for example, usability testing software or even play games with a purpose to implicitly construct formal specifications. Crowdfunding helps to democratize decisions about what software to build. Software engineering researchers may even benefit from new opportunities to evaluate their work with real developers by recruiting developers from the crowd. CSI- SE will inform the software engineering community of current techniques and trends in crowdsourcing, discuss the application of crowdsourcing to software engineering to date, and identify new opportunities to apply crowdsourcing to solve software engineering problems.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.312"}, {"primary_key": "4441937", "vector": [], "sparse_vector": [], "title": "Poster: Symbolic Execution of MPI Programs.", "authors": ["Xianjin Fu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "MPI is widely used in high performance computing. In this extended abstract, we report our current status of analyzing MPI programs. Our method can provide coverage of both input and non-determinism for MPI programs with mixed blocking and non-blocking operations. In addition, to improve the scalability further, a deadlock-oriented guiding method for symbolic execution is proposed. We have implemented our methods, and the preliminary experimental results are promising.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.259"}, {"primary_key": "4441939", "vector": [], "sparse_vector": [], "title": "Poster: Conquering Uncertainty in Java Programming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Uncertainty in programming is one of the challenging issues to be tackled, because it is error-prone for many programmers to temporally avoid uncertain concerns only using simple language constructs such as comments and conditional statements. This paper proposes ucJava, a new Java programming environment for conquering uncertainty. Our environment provides a modular programming style for uncertainty and supports test-driven development taking uncertainty into consideration.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.266"}, {"primary_key": "4441941", "vector": [], "sparse_vector": [], "title": "5th International Workshop on the Twin Peaks of Requirements and Architecture (TwinPeaks 2015).", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The relationships and interdependencies between software requirements and the architectures of software-intensive systems are described in the Twin Peaks model. The fundamental idea of the Twin Peaks model is that Requirements Engineering and Software Architecture should not be treated in isolation. Instead, we need to progressively discover and specify requirements while concurrently exploring alternative architectural solutions. However, bridging the gap between Requirements Engineering and Software Architecture has mainly been discussed independently in the respective communities. Therefore, this ICSE workshop aims at bringing together researchers, practitioners and educators from the Requirements Engineering and Software Architecture fields to jointly explore the strong interdependencies between requirements and architecture. Based on the results from previous editions of the workshop, this edition focuses on agile software development contexts and on exploring lightweight techniques for integrating requirements and architectural thinking.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.330"}, {"primary_key": "4441942", "vector": [], "sparse_vector": [], "title": "Poster: Improving Cloud-Based Continuous Integration Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a novel technique for improving the efficiency of cloud-based continuous integration development environments. Our technique identifies repetitive, expensive and time-consuming setup activities that are required to run integration and system tests in the cloud, and consolidates them into preconfigured testing virtual machines such that the overall costs of test execution are minimized. We create such testing machines by reconfiguring and opportunistically snapshotting the virtual machines already registered in the cloud.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.253"}, {"primary_key": "4441944", "vector": [], "sparse_vector": [], "title": "Making System User Interactive Tests Repeatable: When and What Should We Control?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "System testing and invariant detection is usually conducted from the user interface perspective when the goal is to evaluate the behavior of an application as a whole. A large number of tools and techniques have been developed to generate and automate this process, many of which have been evaluated in the literature or internally within companies. Typical metrics for determining effectiveness of these techniques include code coverage and fault detection, however, with the assumption that there is determinism in the resulting outputs. In this paper we examine the extent to which a common set of factors such as the system platform, Java version, application starting state and tool harness configurations impact these metrics. We examine three layers of testing outputs: the code layer, the behavioral (or invariant) layer and the external (or user interaction) layer. In a study using five open source applications across three operating system platforms, manipulating several factors, we observe as many as 184 lines of code coverage difference between runs using the same test cases, and up to 96 percent false positives with respect to fault detection. We also see some a small variation among the invariants inferred. Despite our best efforts, we can reduce, but not completely eliminate all possible variation in the output. We use our findings to provide a set of best practices that should lead to better consistency and smaller differences in test outcomes, allowing more repeatable and reliable testing and experimentation.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.28"}, {"primary_key": "4441945", "vector": [], "sparse_vector": [], "title": "Safe Memory-Leak Fixing for C Programs.", "authors": ["Qing Gao", "<PERSON><PERSON><PERSON>", "Yaqing Mi", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Hong Mei"], "summary": "Automatic bug fixing has become a promising direction for reducing manual effort in debugging. However, general approaches to automatic bug fixing may face some fundamental difficulties. In this paper, we argue that automatic fixing of specific types of bugs can be a useful complement. This paper reports our first attempt towards automatically fixing memory leaks in C programs. Our approach generates only safe fixes, which are guaranteed not to interrupt normal execution of the program. To design such an approach, we have to deal with several challenging problems such as inter-procedural leaks, global variables, loops, and leaks from multiple allocations. We propose solutions to all the problems and integrate the solutions into a coherent approach. We implemented our inter-procedural memory leak fixing into a tool named Leak Fix and evaluated Leak Fix on 15 programs with 522k lines of code. Our evaluation shows that Leak Fix is able to successfully fix a substantial number of memory leaks, and Leak Fix is scalable for large applications.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.64"}, {"primary_key": "4441949", "vector": [], "sparse_vector": [], "title": "8th International Workshop on Search-Based Software Testing (SBST 2015).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper is a report on the 8th International Workshop on Search-Based Software Testing at the 37th International Conference on Sofrware Engineering (ICSE). Search-Based Software Testing (SBST) is a form of Search-Based Software Engineering (SBSE) that optimizes testing through the use of computational search. SBST is used to generate test data, prioritize test cases, minimize test suites, reduce human oracle cost, verify software models, test service-orientated architectures, construct test suites for interaction testing, and validate real time properties. The objectives of this workshop are to bring together researchers and industrial practitioners from SBST and the wider software engineering community to share experience and provide directions for future research, and to encourage the use of search techniques to combine aspects of testing with other aspects of the software engineering lifecycle.Three full research papers, three short papers, and threeposition papers will be presented in the two-day workshop. Additionally, six development groups have pitted their test generation tools against a common set of programs and benchmarks, and will present their techniques and results. This report will give the background of the workshop and detail the provisional program.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.323"}, {"primary_key": "4441953", "vector": [], "sparse_vector": [], "title": "Poster: Model-based Run-time Variability Resolution for Robotic Applications.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper we present our ongoing work on Robotics Run-time Adaptation (RRA). RRA is a model-driven approach that addresses robotics runtime adaptation by modeling and resolving run-time variability of robotic applications.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.269"}, {"primary_key": "4441955", "vector": [], "sparse_vector": [], "title": "Revisiting the Impact of Classification Techniques on the Performance of Defect Prediction Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Defect prediction models help software quality assurance teams to effectively allocate their limited resources to the most defect-prone software modules. A variety of classification techniques have been used to build defect prediction models ranging from simple (e.g., Logistic regression) to advanced techniques (e.g., Multivariate Adaptive Regression Splines (MARS)). Surprisingly, recent research on the NASA dataset suggests that the performance of a defect prediction model is not significantly impacted by the classification technique that is used to train it. However, the dataset that is used in the prior study is both: (a) noisy, i.e., Contains erroneous entries and (b) biased, i.e., Only contains software developed in one setting. Hence, we set out to replicate this prior study in two experimental settings. First, we apply the replicated procedure to the same (known-to-be noisy) NASA dataset, where we derive similar results to the prior study, i.e., The impact that classification techniques have appear to be minimal. Next, we apply the replicated procedure to two new datasets: (a) the cleaned version of the NASA dataset and (b) the PROMISE dataset, which contains open source software developed in a variety of settings (e.g., Apache, GNU). The results in these new datasets show a clear, statistically distinct separation of groups of techniques, i.e., The choice of classification technique has an impact on the performance of defect prediction models. Indeed, contrary to earlier research, our results suggest that some classification techniques tend to produce defect prediction models that outperform others.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.91"}, {"primary_key": "4441956", "vector": [], "sparse_vector": [], "title": "Automated Planning for Self-Adaptive Systems.", "authors": ["<PERSON>"], "summary": "Self-adaptation has been proposed as a viable solution to alleviate the management burden that is induced by the dynamic nature and increasing complexity of computer systems. In this context, architectural-based self-adaptation has emerged as one of the most promising approaches to automatically manage such systems, resorting to a control loop that includes monitoring, analyzing, planning, and executing adequate actions. This work addresses the challenges of adaptation planning -the decision-making process for selecting an appropriate course of action- with a focus on the problem of provisioning automated mechanisms for assembling adaptation plans, as a means to enhance adaptive capabilities under uncertainty. To this purpose, adaptations are modeled in a hierarchical manner, defining primitive actions, guarded actions, and deliberate plans, which may guide the system towards a desired state.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.273"}, {"primary_key": "4441957", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON>: Lightweight Test Selection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Regression testing is a crucial, but potentially time-consuming, part of software development. Regression test selection (RTS), which runs only a subset of tests, was proposed over three decades ago as a promising way to speed up regression testing. However, RTS has not been widely adopted in practice. We propose EKSTAZI , a lightweight RTS tool, that can integrate well with testing frameworks and build systems, increasing the chance for adoption. EKSTAZI tracks dynamic dependencies of tests on files and requires no integration with version-control systems. We implemented EKSTAZI for Java+JUnit and Scala+ScalaTest, and evaluated it on 615 revisions of 32 open-source projects (totaling almost 5M LOC). The results show that EKSTAZI reduced the end-to-end testing time by 32% on average compared to executing all tests. EKSTAZI has been adopted for day-to-day use by several Apache developers. The demo video for EKSTAZI can be found at http://www.youtube.com/watch?v=jE8K5_UCP28.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.230"}, {"primary_key": "4441958", "vector": [], "sparse_vector": [], "title": "3rd FME Workshop on Formal Methods in Software Engineering (FormaliSE 2015).", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Despite their significant advantages, formal methods are not widely used in industrial software development. Following the successful workshops we organized at ICSE 2103 in San Francisco, and ICSE 2014 in Hyderabad, we organize a third edition of the FormaliSE workshop with the main goal to promote the integration between the formal methods and the software engineering communities.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.313"}, {"primary_key": "4441959", "vector": [], "sparse_vector": [], "title": "Automatic and Continuous Software Architecture Validation.", "authors": ["<PERSON><PERSON><PERSON>", "Itai Segall"], "summary": "Software systems tend to suffer from architectural problems as they are being developed. While modern software development methodologies such as Agile and Dev-Ops suggest different ways of assuring code quality, very little attention is paid to maintaining high quality of the architecture of the evolving systems. By detecting and alerting about violations of the intended software architecture, one can often avoid code-level bad smells such as spaghetti code. Typically, if one wants to reason about the software architecture, the burden of first defining the intended architecture falls on the developer's shoulders. This includes definition of valid and invalid dependencies between software components. However, the developers are seldom familiar with the entire software system, which makes this task difficult, time consuming and error-prone. We propose and implement a solution for automatic detection of architectural violations in software artifacts. The solution, which utilizes a number of predefined and user-defined patterns, does not require prior knowledge of the system or its intended architecture. We propose to leverage this solution as part of the nightly build process used by development teams, thus achieving continuous automatic validation of the system's software architecture. As we show in multiple open-source and proprietary cases, a small set of predefined patterns can detect architectural violations as they are introduced over the course of development, and also capture deterioration in existing architectural problems. By evaluating the tool on relatively large open-source projects, we also validate its scalability and practical applicability to large software systems.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.135"}, {"primary_key": "4441961", "vector": [], "sparse_vector": [], "title": "When App Stores Listen to the Crowd to Fight Bugs in the Wild.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "App stores are digital distribution platforms that put available apps that run on mobile devices. Current stores are software repositories that deliver apps upon user requests. However, when an app has a bug, the store continues delivering defective apps until the developer uploads a fixed version, thus impacting on the reputation of both store and app developer. In this paper, we envision a new generation of app stores that: (a) reduce human intervention to maintain mobile apps; and (b) enhance store services with smart and autonomous functionalities to automatically increase the quality of the delivered apps. We sketch a prototype of our envisioned app store and we discuss the functionalities that current stores an enhance by incorporating automatic software repair techniques.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.195"}, {"primary_key": "4441963", "vector": [], "sparse_vector": [], "title": "Data-Delineation in Software Binaries and its Application to Buffer-Overrun Discovery.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Detecting memory-safety violations in binaries is complicated by the lack of knowledge of the intended data layout, i.e., the locations and sizes of objects. We present lightweight, static, heuristic analyses for recovering the intended layout of data in a stripped binary. Comparison against DWARF debugging information shows high precision and recall rates for inferring source-level object boundaries. On a collection of benchmarks, our analysis eliminates a third to a half of incorrect object boundaries identified by an IDA Pro-inspired heuristic, while retaining nearly all valid object boundaries. In addition to measuring their accuracy directly, we evaluate the effect of using the recovered data for improving the precision of static buffer-overrun detection in the defect-detection tool CodeSonar/x86. We demonstrate that CodeSonar's false-positive rate drops by about 80% across our internal evaluation suite for the tool, while our approximation of CodeSonar's recall only degrades about 25%.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.36"}, {"primary_key": "4441966", "vector": [], "sparse_vector": [], "title": "Work Practices and Challenges in Pull-Based Development: The Integrator&apos;s Perspective.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the pull-based development model, the integrator has the crucial role of managing and integrating contributions. This work focuses on the role of the integrator and investigates working habits and challenges alike. We set up an exploratory qualitative study involving a large-scale survey of 749 integrators, to which we add quantitative data from the integrator's project. Our results provide insights into the factors they consider in their decision making process to accept or reject a contribution. Our key findings are that integrators struggle to maintain the quality of their projects and have difficulties with prioritizing contributions that are to be merged. Our insights have implications for practitioners who wish to use or improve their pull-based development process, as well as for researchers striving to understand the theoretical implications of the pull-based model in software development.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.55"}, {"primary_key": "4441967", "vector": [], "sparse_vector": [], "title": "7th International Workshop on Modeling in Software Engineering (MiSE 2015).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Models are an important tool in conquering the increasing complexity of modern software systems. Key industries are strategically directing their development environments towards more extensive use of modeling techniques. MiSE 2015 aimed to understand, through critical analysis, the current and future uses of models in the engineering of software-intensive systems. The MiSE workshop series has proven to be an effective forum for discussing modeling techniques from both the MDE and software engineering perspectives. An important goal of this workshop is to foster exchange between these two communities. In 2015 the focus was on considering the current state of tool support and the challenges that need to be addressed to improve the maturity of tools. There was also analysis of successful applications of modeling techniques in specific application domains, with attempts to determine how the participants' experiences can be carried over to other domains.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.317"}, {"primary_key": "4441969", "vector": [], "sparse_vector": [], "title": "1st International Workshop on Complex faUlts and Failures in LargE Software Systems (COUFLESS 2015).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "COUFLESS is a one-day workshop that starts with keynote speaker, Prof. <PERSON> from Duke University, North Carolina, USA whose talk's title is titled: \"Why Does Software Fail and What Should be Done About It?\" A total of 15 papers were submitted to COUFLESS with 53 authors from nine countries and each paper received at least three reviews by the 26 members of the program committee from 11 countries, making it a truly International Workshop. After a long discussion, 11 papers were accepted with the acceptance rate of 73%. Accepted papers address the issues of localizing and debugging complex faults in large-scale software applications.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.310"}, {"primary_key": "4441973", "vector": [], "sparse_vector": [], "title": "Leveraging Informal Documentation to Summarize Classes and Methods in Context.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Critical information related to a software developer'scurrent task is trapped in technical developer discussions,bug reports, code reviews, and other software artefacts. Muchof this information pertains to the proper use of code elements(e.g., methods and classes) that capture vital problem domainknowledge. To understand the purpose of these code elements,software developers must either access documentation and onlineposts and understand the source code or peruse a substantialamount of text. In this paper, we use the context that surroundscode elements in StackOverflow posts to summarize the use andpurpose of code elements. To provide focus to our investigation,we consider the generation of summaries for library identifiersdiscussed in StackOverflow. Our automatic summarization approachwas evaluated on a sample of 100 randomly-selectedlibrary identifiers with respect to a benchmark of summariesprovided by two annotators. The results show that the approachattains an R-precision of 54%, which is appropriate given thediverse ways in which code elements can be used.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.212"}, {"primary_key": "4441975", "vector": [], "sparse_vector": [], "title": "Truth in Advertising: The Hidden Cost of Mobile Ads for Software Developers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The \"free app\" distribution model has been extremely popular with end users and developers. Developers use mobile ads to generate revenue and cover the cost of developing these free apps. Although the apps are ostensibly free, they in fact do come with hidden costs. Our study of 21 real world Android apps shows that the use of ads leads to mobile apps that consume significantly more network data, have increased energy consumption, and require repeated changes to ad related code. We also found that complaints about these hidden costs are significant and can impact the ratings given to an app. Our results provide actionable information and guidance to software developers in weighing the tradeoffs of incorporating ads into their mobile apps.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.32"}, {"primary_key": "4441976", "vector": [], "sparse_vector": [], "title": "StriSynth: Synthesis for Live Programming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ruzica Piskac"], "summary": "Motivated by applications in automating repetitive file manipulations, we present a tool called StriSynth, which allows end-users to perform transformations over data using examples. Based on provided examples, our tool automaticallygenerates scripts for non-trivial file manipulations. Although the current focus of StriSynth are file manipulations, it implements a more general string transformation framework. This framework builds on and further extends the functionality of Flash Fill -- a Microsoft Excel extension for string transformations. An accompanying video to this paper is available at the following website http://youtu.be/kkDZphqIdFM.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.227"}, {"primary_key": "4441977", "vector": [], "sparse_vector": [], "title": "Interactive Synthesis Using Free-Form Queries.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a new code assistance tool for integrated development environments. Our system accepts free-form queries allowing a mixture of English and Java as an input, and produces Java code fragments that take the query into account and respect syntax, types, and scoping rules of Java as well as statistical usage patterns. The returned results need not have the structure of any previously seen code fragment. As part of our system we have constructed a probabilistic context free grammar for Java constructs and library invocations, as well as an algorithm that uses a customized natural language processing tool chain to extract information from free-form text queries. The evaluation results show that our technique can tolerate much of the flexibility present in natural language, and can also be used to repair incorrect Java expressions that contain useful information about the developer's intent. Our demo video is available at http://youtu.be/tx4-XgAZkKU.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.224"}, {"primary_key": "4441978", "vector": [], "sparse_vector": [], "title": "In Search of the Emotional Design Effect in Programming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A small number of recent studies have suggested that learning is enhanced when the illustrations in instructional materials are designed to appeal to the learners' emotions through the use of color and the personification of key elements. We sought to replicate this emotional design effect in the context of introductory object-oriented programming (OOP). In this preliminary study, a group of freshmen studied a text on objects which was illustrated using anthropomorphic graphics while a control group had access to abstract graphics. We found no significant difference in the groups' scores on a comprehension post-test, but the experimental group spent substantially less time on the task than the control group. Among those participants who had no prior programming experience, the materials inspired by emotional design were perceived as less intelligible and appealing and led to lower self-reported concentration levels. Although this result does not match the pattern of results from earlier studies, it shows that the choice of illustrations in learning materials matters and calls for more research that addresses the limitations of this preliminary study.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.175"}, {"primary_key": "4441979", "vector": [], "sparse_vector": [], "title": "Towards Model Driven Architecture and Analysis of System of Systems Access Control.", "authors": ["<PERSON>"], "summary": "Nowadays there is growing awareness of the importanceof Systems of Systems (SoS) which are large-scalesystems composed of complex systems. SoS possess specificproperties when compared with monolithic complex systems, inparticular: operational independence, managerial independence,evolutionary development, emergent behavior and geographicdistribution. One of the current main challenges is the impact ofthese properties on SoS security modeling and analysis. In thisresearch proposal, we introduce a new method incorporating aprocess, a language and a software architectural tool to model,analyze and predict security architectural alternatives of SoS.Thus security will be taken into account as soon as possible inthe life cycle of the SoS, making it less expensive", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.280"}, {"primary_key": "4441981", "vector": [], "sparse_vector": [], "title": "Masters-Level Software Engineering Education and the Enriched Student Context.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Currently, adult higher education software engineering pedagogy isolates the student in a controlled environment during delivery, with application of their learning temporally distant from their professional practice. Delivering software engineering teaching that is immediately relevant to professional practice remains an open challenge. In this paper, we discuss a new pedagogical model which addresses this problem by embedding the validation of the student's learning within their rich professional context. We discuss our experience of applying the model to the design and delivery of a new post-graduate software development module, a core component in our new software engineering Masters qualification at the Open University, UK, a market leader in adult higher education at a distance.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.162"}, {"primary_key": "4441984", "vector": [], "sparse_vector": [], "title": "Contest Based Learning with Blending Software Engineering and Business Management: For Students&apos; High Motivation and High Practice Ability.", "authors": ["<PERSON><PERSON>"], "summary": "We began implementing contest-based learning with a blend of software engineering and business management 10 years ago. At first, a project subject was assigned. However, several problems occurred: For example, the students became absorbed in programming rather than design and analysis activities. Therefore, the curriculum changed from project subjects to contest-based learning. Business management, marketing, and accounting subjects were added to the new curriculum, and students made information technology (IT) business plans using their knowledge of software engineering and business management. The IT business plans were submitted to various contests held by public newspaper companies and the federation of economic organizations in Japan. As a result, in the 10 years of the contest-based learning implementation, 20 teams have received awards in various IT business plan contests. We investigated 10 persons who had experience submitting business plans. We confirmed that contest-based learning had clearer goals, such as to win the contest prize, compared to project-based learning. Further, the abilities to solve problems and to investigate increased more in comparison with lecture-style and project-style education.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.340"}, {"primary_key": "4441992", "vector": [], "sparse_vector": [], "title": "An Approach to Detect Android Antipatterns.", "authors": ["<PERSON>"], "summary": "Mobile applications are becoming complex software systems that must be developed quickly and evolve regularly to fit new user requirements and execution contexts. However, addressing these constraints may result in poor design choices, known as antipatterns, which may degrade software quality and performance. Thus, the automatic detection of antipatterns is an important activity that eases the future maintenance and evolution tasks. Moreover, it helps developers to refactor their applications and thus, to improve their quality. While antipatterns are well-known in object-oriented applications, their study in mobile applications is still in their infancy. In this paper, we presents a tooled approach, called Paprika, to analyze Android applications and to detect object-oriented and Android-specific antipatterns from binaries of applications.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.243"}, {"primary_key": "4441995", "vector": [], "sparse_vector": [], "title": "Combining Multi-Objective Search and Constraint Solving for Configuring Large Software Product Lines.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software Product Line (SPL) feature selection involves the optimization of multiple objectives in a large and highly constrained search space. We introduce SATIBEA, that augments multi-objective search-based optimization with constraint solving to address this problem, evaluating it on five large real-world SPLs, ranging from 1,244 to 6,888 features with respect to three different solution quality indicators and two diversity metrics. The results indicate that SATIBEA statistically significantly outperforms the current state-of-the-art (p", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.69"}, {"primary_key": "4442000", "vector": [], "sparse_vector": [], "title": "Enron&apos;s Spreadsheets and Related Emails: A Dataset and Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-Hill"], "summary": "Spreadsheets are used extensively in business processes around the world and as such, are a topic of research interest. Over the past few years, many spreadsheet studies have been performed on the EUSES spreadsheet corpus. While this corpus has served the spreadsheet community well, the spreadsheets it contains are mainly gathered with search engines and might therefore not represent spreadsheets used in companies. This paper presents an analysis of a new dataset, extracted from the Enron email archive, containing over 15,000 spreadsheets used within the Enron Corporation. In addition to the spreadsheets, we also present an analysis of the associated emails, where we look into spreadsheet-specific email behavior. Our analysis shows that 1) 24% of Enron spreadsheets with at least one formula contain an Excel error, 2) there is little diversity in the functions used in spreadsheets: 76% of spreadsheets in the presented corpus use the same 15 functions and, 3) the spreadsheets are substantially more smelly than the EUSES corpus, especially in terms of long calculation chains. Regarding the emails, we observe that spreadsheets 1) are a frequent topic of email conversation with 10% of emails either referring to or sending spreadsheets and 2) the emails are frequently discussing errors in and updates to spreadsheets.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.129"}, {"primary_key": "4442001", "vector": [], "sparse_vector": [], "title": "2nd International Workshop on Software Engineering Methods in Spreadsheets (SEMS 2015).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Spreadsheets are heavily used in industry, becausethey are easily written and adjusted, using an intuitive visual interface. They often start out as simple tools; however, over time spreadsheets can become increasingly complex, up to the point where they become complicated and inflexible. In many ways, spreadsheet are similar to software: both concern the storage and manipulation of data and the presentation of results to the user. Because of this similarity, many methods and techniques from software engineering can be applied to spreadsheets. The role of SEMS, the International Workshop on Software Engineering Methods in Spreadsheets is to explore the possibilities of applying successful methods from software engineering to spreadsheets. Some, like testing and visualization, have been tried before and can be built upon. For methods that have not yet been tried on spreadsheets, SEMS will serve as a platform for early feedback. The SEMS program included an industrial keynote, \"spreadsheet stories\" (success or failure), short and long research papers,a good mix of industrial and academic researchers, as well as lively discussion and debate.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.325"}, {"primary_key": "4442002", "vector": [], "sparse_vector": [], "title": "The Art of Testing Less without Sacrificing Quality.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Testing is a key element of software development processes for the management and assessment of product quality. In most development environments, the software engineers are responsible for ensuring the functional correctness of code. However, for large complex software products, there is an additional need to check that changes do not negatively impact other parts of the software and they comply with system constraints such as backward compatibility, performance, security etc. Ensuring these system constraints may require complex verification infrastructure and test procedures. Although such tests are time consuming and expensive and rarely find defects they act as an insurance process to ensure the software is compliant. However, long lasting tests increasingly conflict with strategic aims to shorten release cycles. To decrease production costs and to improve development agility, we created a generic test selection strategy called THEO that accelerates test processes without sacrificing product quality. THEO is based on a cost model, which dynamically skips tests when the expected cost of running the test exceeds the expected cost of removing it. We replayed past development periods of three major Microsoft products resulting in a reduction of 50% of test executions, saving millions of dollars per year, while maintaining product quality.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.66"}, {"primary_key": "4442003", "vector": [], "sparse_vector": [], "title": "Empirically Detecting False Test Alarms Using Association Rules.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Applying code changes to software systems and testing these code changes can be a complex task that involves many different types of software testing strategies, e.g. system and integration tests. However, not all test failures reported during code integration are hinting towards code defects. Testing large systems such as the Microsoft Windows operating system requires complex test infrastructures, which may lead to test failures caused by faulty tests and test infrastructure issues. Such false test alarms are particular annoying as they raise engineer attention and require manual inspection without providing any benefit. The goal of this work is to use empirical data to minimize the number of false test alarms reported during system and integration testing. To achieve this goal, we use association rule learning to identify patterns among failing test steps that are typically for false test alarms and can be used to automatically classify them. A successful classification of false test alarms is particularly valuable for product teams as manual test failure inspection is an expensive and time-consuming process that not only costs engineering time and money but also slows down product development. We evaluating our approach on system and integration tests executed during Windows 8.1 and Microsoft Dynamics AX development. Performing more than 10,000 classifications for each product, our model shows a mean precision between 0.85 and 0.90 predicting between 34% and 48% of all false test alarms.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.133"}, {"primary_key": "4442007", "vector": [], "sparse_vector": [], "title": "Security Toolbox for Detecting Novel and Sophisticated Android Malware.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>sh <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a demo of our Security Toolbox to detect novel malware in Android apps. This Toolbox is developed through our recent research project funded by the DARPA Automated Program Analysis for Cybersecurity (APAC) project. The adversarial challenge (\"Red\") teams in the DARPA APAC program are tasked with designing sophisticated malware to test the bounds of malware detection technology being developed by the research and development (\"Blue\") teams. Our research group, a Blue team in the DARPA APAC program, proposed a \"human-in-the-loop program analysis\" approach to detect malware given the source or Java bytecode for an Android app. Our malware detection apparatus consists of two components: a general-purpose program analysis platform called Atlas, and a Security Toolbox built on the Atlas platform. This paper describes the major design goals, the Toolbox components to achieve the goals, and the workflow for auditing Android apps. The accompanying video illustrates features of the Toolbox through a live audit.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.235"}, {"primary_key": "4442009", "vector": [], "sparse_vector": [], "title": "Statistical Learning and Software Mining for Agent Based Simulation of Software Evolution.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In the process of software development it is of high interest for a project manager to gain insights about the ongoing process and possible development trends at several points in time. Substantial factors influencing this process are, e.g., the constellation of the development team, the growth and complexity of the system, and the error-proneness of software entities. For this purpose we build an agent based simulation tool which predicts the future of a project under given circumstances, stored in parameters, which control the simulation process. We estimate these parameters with the help of software mining. Our work exposed the need for a more fine-grained model for the developer behavior. Due to this we create a learning model, which helps us to understand the contribution behavior of developers and, thereby, to determine simulation parameters close to reality. In this paper we present our agent based simulation model for software evolution and describe how methods from statistical learning and data mining serves us to estimate suitable simulation parameters.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.279"}, {"primary_key": "4442010", "vector": [], "sparse_vector": [], "title": "Does Automated Refactoring Obviate Systematic Editing?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "When developers add features and fix bugs, they often make systematic edits-similar edits to multiple locations. Systematic edits may indicate that developers should instead refactor to eliminate redundancy. This paper explores this question by designing and implementing a fully automated refactoring tool called RASE, which performs clone removal. RASE (1) extracts common code guided by a systematic edit; (2) creates new types and methods as needed; (3) parameterizes differences in types, methods, variables, and expressions; and (4) inserts return objects and exit labels based on control and data flow. To our knowledge, this functionality makes RASE the most advanced refactoring tool for automated clone removal. We evaluate RASE with real-world systematic edits and compare to method based clone removal. RASE successfully performs clone removal in 30 of 56 method pairs (n=2) and 20 of 30 method groups (n≥3) with systematic edits. We find that scoping refactoring based on systematic edits (58%), rather than the entire method (33%), increases the applicability of automated clone removal. Automated refactoring is not feasible in the other 42% cases, which indicates that automated refactoring does not obviate the need for systematic editing.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.58"}, {"primary_key": "4442011", "vector": [], "sparse_vector": [], "title": "GPredict: Generic Predictive Concurrency Analysis.", "authors": ["<PERSON>", "Qingzhou Luo", "<PERSON><PERSON><PERSON>"], "summary": "Predictive trace analysis (PTA) is an effective approach for detecting subtle bugs in concurrent programs. Existing PTA techniques, however, are typically based on adhoc algorithms tailored to low-level errors such as data races or atomicity violations, and are not applicable to high-level properties such as \"a resource must be authenticated before use\" and \"a collection cannot be modified when being iterated over\". In addition, most techniques assume as input a globally ordered trace of events, which is expensive to collect in practice as it requires synchronizing all threads. In this paper, we present GPredict: a new technique that realizes PTA for generic concurrency properties. Moreover, GPredict does not require a global trace but only the local traces of each thread, which incurs much less runtime overhead than existing techniques. Our key idea is to uniformly model violations of concurrency properties and the thread causality as constraints over events. With an existing SMT solver, GPredict is able to precisely predict property violations allowed by the causal model. Through our evaluation using both benchmarks and real world applications, we show that GPredict is effective in expressing and predicting generic property violations. Moreover, it reduces the runtime overhead of existing techniques by 54% on DaCapo benchmarks on average.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.96"}, {"primary_key": "4442012", "vector": [], "sparse_vector": [], "title": "Incorporating Human Intention into Self-Adaptive Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Self-adaptive systems are fed with contextual information from the environments in which the systems operate,from within themselves, and from the users. Traditional self-adaptive systems research has focused on inputs of systems performance, resources, exception, and error recovery that drive systems' reaction to their environments. The intelligent ability ofthese self-adaptive systems is impoverished without knowledge ofa user's covert attention (thoughts, emotions, feelings). As a result, it is difficult to build effective systems that anticipate and react to users' needs as projected by covert behavior. This paperpresents the preliminary research results on capturing users'intention through neural input, and in reaction, commanding actions from software systems (e.g., load an application) based on human intention. Further, systems can self-adapt and refine their behaviors driven by such human covert behavior. The long-term research goal is to incorporate and synergize human neural input.Thus establishing software systems with a self-adaptive capability to \"feel\" and \"anticipate\" users intentions and put the human in the loop.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.196"}, {"primary_key": "4442013", "vector": [], "sparse_vector": [], "title": "A Combined Technique of GUI Ripping and Input Perturbation Testing for Android Apps.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Mobile applications have become an integral part of the daily lives of millions of users, thus making necessary to ensure their security and reliability. Moreover the increasing number of mobile applications with rich Graphical User Interfaces (GUI) creates a growing need for automated techniques of GUI Testing for mobile applications. In this paper, the GUI Ripping Technique is combined with the Input Perturbation Testing to improve the quality of Android Application Testing. The proposed technique, based on a systematic and automatic exploration of the behavior of Android applications, creates a model of the explored GUI and then uses it to generate the perturbed text inputs. The technique was evaluated on many Android apps and its results were compared with random input tests.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.241"}, {"primary_key": "4442014", "vector": [], "sparse_vector": [], "title": "Understanding the Software Fault Introduction Process.", "authors": ["<PERSON>"], "summary": "Testing and debugging research revolves around faults, yet we have a limited understanding of the processes by which faults are introduced and removed. Previous work in this area has focused on describing faults rather than explaining the introduction and removal processes, meaning that a great deal of testing and debugging research depends on assumptions that have not been empirically validated. We propose a three-phase project to develop an explanatory theory of the fault introduction process and describe how the project will be completed.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.274"}, {"primary_key": "4442015", "vector": [], "sparse_vector": [], "title": "Enabling the Definition and Enforcement of Governance Rules in Open Source Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Governance rules in software development projects help to prioritize and manage their development tasks, and contribute to the long-term sustainability of the project by clarifying how core and external contributors should collaborate in order to advance the project during its whole lifespan. Despite their importance, specially in Open Source Software (OSS) projects, these rules are usually implicit or scattered in the project documentation/tools (e.g., Tracking-systems or forums), hampering the correct understanding of the development process. We propose to enable the explicit definition and enforcement of governance rules for OSS projects. We believe this brings several important benefits, including improvements in the transparency of the process, its traceability and the semi-automation of the governance itself. Our approach has been implemented on top of My Lyn, a project-management Eclipse plug-in supporting most popular tracking-systems.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.184"}, {"primary_key": "4442016", "vector": [], "sparse_vector": [], "title": "Cognitively Sustainable ICT with Ubiquitous Mobile Services - Challenges and Opportunities.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Information and Communication Technology (ICT) has led to an unprecedented development in almost all areas of human life. It forms the basis for what is called \"the cognitive revolution\" -- a fundamental change in the way we communicate, feel, think and learn based on an extension of individual information processing capacities by communication with other people through technology. This so-called \"extended cognition\" shapes human relations in a radically new way. It is accompanied by a decrease of shared attention and affective presence within closely related groups. This weakens the deepest and most important bonds, that used to shape human identity. Sustainability, both environmental and social (economic, technological, political and cultural) is one of the most important issues of our time. In connection with \"extended cognition\" we have identified a new, basic type of social sustainability that everyone takes for granted, and which we claim is in danger due to our changed ways of communication. We base our conclusion on a detailed analysis of the current state of the practice and observed trends. The contribution of our article consists of identifying cognitive sustainability and explaining its central role for all other aspects of sustainability, showing how it relates to the cognitive revolution, its opportunities and challenges. Complex social structures with different degrees of proximity have always functioned as mechanisms behind belongingness and identity. To create a long-term cognitive sustainability, we need to rethink and design new communication technologies that support differentiated and complex social relationships.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.189"}, {"primary_key": "4442017", "vector": [], "sparse_vector": [], "title": "Does Outside-In Teaching Improve the Learning of Object-Oriented Programming?", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Object-oriented programming (OOP) is widely used in the software industry and university introductory courses today. Following the structure of most textbooks, such courses frequently are organised starting with the concepts of imperative and structured programming and only later introducing OOP. An alternative approach is to begin directly with OOP following the Outside-In teaching method as proposed by <PERSON>. Empirical results for the effects of Outside-In teaching on students and lecturers are sparse, however. We describe the conceptual design and empirical evaluation of two OOP introductory courses from different universities based on Outside-In teaching. The evaluation results are compared to those from a third course serving as the control group, which was taught OOP the \"traditional\" way. We evaluate the initial motivation and knowledge of the participants and the learning outcomes. In addition, we analyse results of the end-term exams and qualitatively analyse the results of interviews with the lecturers and tutors. Regarding the learning outcomes, the results show no signif- icant differences between the Outside-In and the \"traditional\" teaching method. In general, students found it harder to solve and implement algorithmic problems than to understand object oriented (OO) concepts. Students taught OOP by the Outside-In method, however, were less afraid that they would not pass the exam at the end of term and understood the OO paradigm more quickly. Therefore, the Outside-In method is no silver bullet for teaching OOP regarding the learning outcomes but has positive effects on motivation and interest.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.173"}, {"primary_key": "4442020", "vector": [], "sparse_vector": [], "title": "Towards Explicitly Elastic Programming Frameworks.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>"], "summary": "It is a widely held view that software engineers should not be \"burdened\" with the responsibility of making their application components elastic, and that elasticity should be either be implicit and automatic in the programming framework; or that it is the responsibility of the cloud provider's operational staff (DevOps) to make distributed applications written for dedicated clusters elastic and execute them on cloud environments. In this paper, we argue the opposite - we present a case for explicit elasticity, where software engineers are given the flexibility to explicitly engineer elasticity into their distributed applications. We present several scenarios where elasticity retrofitted to applications by DevOps is ineffective, present preliminary empirical evidence that explicit elasticity improves efficiency, and argue for elastic programming languages and frameworks to reduce programmer effort in engineering elastic distributed applications. We also present a bird's eye view of ongoing work on two explicitly elastic programming frameworks - Elastic Thrift (based on Apache Thrift) and Elastic Java, an extension of Java with support for explicit elasticity.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.207"}, {"primary_key": "4442021", "vector": [], "sparse_vector": [], "title": "Combining Mastery Learning with Project-Based Learning in a First Programming Course: An Experience Report.", "authors": ["<PERSON><PERSON>"], "summary": "One of the challenges in teaching a first programming course is that in the same course, the students must learn basic programming techniques and high level abstraction abilities, and the application of those techniques and concepts in problem solving and (engineering) design. To confront this challenge, in previous years, we have included a project-based learning phase at the end of our course to encourage the acquisition of high level design and creativity. To address some of the shortcomings of our previous editions, we have recently included a mastery phase to the course. While project-based learning is suitable for teaching high-level skills that require design and creativity and prepare the students for the study of software engineering, mastery-based learning is suitable for concrete skills such as basic programming tasks. Our particular innovation is to allow students into the project phase only if they have demonstrated a minimum predefined competency level in programming. The combination of the two approaches seems to address most of the requirements of a first programming course. We present our motivation for combining the two pedagogical techniques and our experience with the course.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.163"}, {"primary_key": "4442024", "vector": [], "sparse_vector": [], "title": "Learning Combinatorial Interaction Test Generation Strategies Using Hyperheuristic Search.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The surge of search based software engineering research has been hampered by the need to develop customized search algorithms for different classes of the same problem. For instance, two decades of bespoke Combinatorial Interaction Testing (CIT) algorithm development, our exemplar problem, has left software engineers with a bewildering choice of CIT techniques, each specialized for a particular task. This paper proposes the use of a single hyperheuristic algorithm that learns search strategies across a broad range of problem instances, providing a single generalist approach. We have developed a Hyperheuristic algorithm for CIT, and report experiments that show that our algorithm competes with known best solutions across constrained and unconstrained problems: For all 26 real-world subjects, it equals or outperforms the best result previously reported in the literature. We also present evidence that our algorithm's strong generic performance results from its unsupervised learning. Hyperheuristic search is thus a promising way to relocate CIT design intelligence from human to machine.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.71"}, {"primary_key": "4442025", "vector": [], "sparse_vector": [], "title": "Load Testing Large-Scale Software Systems.", "authors": ["<PERSON><PERSON> (Jack) Jiang"], "summary": "Large-scale software systems (e.g., Amazon and Dropbox) must be load tested to ensure that they can service thousands or millions of concurrent requests every day. In this technical briefing, we will describe the state of research and practices in the area of load testing. We will focus on the techniques used in the three phases of a load test: (1) designing a load test, (2) executing a load test, and (3) analyzing the results of a load test. This technical briefing is targeted at load testing practitioners and software engineering researchers interested in testing and analyzing the behavior of large-scale software systems.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.304"}, {"primary_key": "4442026", "vector": [], "sparse_vector": [], "title": "From Developer Networks to Verified Communities: A Fine-Grained Approach.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Effective software engineering demands a coordinated effort. Unfortunately, a comprehensive view on developer coordination is rarely available to support software-engineering decisions, despite the significant implications on software quality, software architecture, and developer productivity. We present a fine-grained, verifiable, and fully automated approach to capture a view on developer coordination, based on commit information and source-code structure, mined from version-control systems. We apply methodology from network analysis and machine learning to identify developer communities automatically. Compared to previous work, our approach is fine-grained, and identifies statistically significant communities using order-statistics and a community-verification technique based on graph conductance. To demonstrate the scalability and generality of our approach, we analyze ten open-source projects with complex and active histories, written in various programming languages. By surveying 53 open-source developers from the ten projects, we validate the authenticity of inferred community structure with respect to reality. Our results indicate that developers of open-source projects form statistically significant community structures and this particular view on collaboration largely coincides with developers' perceptions of real-world collaboration.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.73"}, {"primary_key": "4442029", "vector": [], "sparse_vector": [], "title": "Poster: Software Development Risk Management: Using Machine Learning for Generating Risk Prompts.", "authors": ["<PERSON>"], "summary": "Software risk management is a critical component of software development management. Due to the magnitude of potential losses, risk identification and mitigation early on become paramount. Lists containing hundreds of possible risk prompts are available both in academic literature as well as in practice. Given the large number of risks documented, scanning the lists for risks and pinning down relevant risks, though comprehensive, becomes impractical. In this work, a machine learning algorithm is developed to generate risk prompts, based on software project characteristics and other factors. The work also explores the utility of post-classification tagging of risks.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.271"}, {"primary_key": "4442033", "vector": [], "sparse_vector": [], "title": "Scalable Formal Verification of UML Models.", "authors": ["<PERSON>"], "summary": "UML (Unified Modeling Language) has been used for years in diverse domains. Its notations usually come with a reasonably well-defined syntax, but its semantics is left under-specified and open to different interpretations. This freedom hampers the formal verification of produced specifications and calls for more rigor and precision. This work aims to bridge this gap and proposes a flexible and modular formalization approach based on temporal logic. We studied the different interpretations for some of its constructs, and our framework allows one to assemble the semantics of interest by composing the selected formalizations for the different pieces. However, the formalization per-se is not enough. The verification process, in general, becomes slow and impossible -as the model grows in size. To tackle the scalability problem, this work also proposes a bit-vector-based encoding of LTL formulae. The first results witness a significant increase in the size of analyzable models, not only for our formalization of UML models, but also for numerous other models that can be reduced to bounded satisfiability checking of LTL formulae.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.275"}, {"primary_key": "4442034", "vector": [], "sparse_vector": [], "title": "Open Source-Style Collaborative Development Practices in Commercial Projects Using GitHub.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Researchers are currently drawn to study projects hosted on GitHub due to its popularity, ease of obtaining data, and its distinctive built-in social features. GitHub has been found to create a transparent development environment, which together with a pull request-based workflow, provides a lightweight mechanism for committing, reviewing and managing code changes. These features impact how GitHub is used and the benefits it provides to teams' development and collaboration. While most of the evidence we have is from GitHub's use in open source software (OSS) projects, GitHub is also used in an increasing number of commercial projects. It is unknown how GitHub supports these projects given that GitHub's workflow model does not intuitively fit the commercial development way of working. In this paper, we report findings from an online survey and interviews with GitHub users on how GitHub is used for collaboration in commercial projects. We found that many commercial projects adopted practices that are more typical of OSS projects including reduced communication, more independent work, and self-organization. We discuss how GitHub's transparency and popular workflow can promote open collaboration, allowing organizations to increase code reuse and promote knowledge sharing across their teams.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.74"}, {"primary_key": "4442039", "vector": [], "sparse_vector": [], "title": "A Case Study in Locating the Architectural Roots of Technical Debt.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Our recent research has shown that, in large-scale software systems, defective files seldom exist alone. They are usually architecturally connected, and their architectural structures exhibit significant design flaws which propagate bugginess among files. We call these flawed structures the architecture roots, a type of technical debt that incurs high maintenance penalties. Removing the architecture roots of bugginess requires refactoring, but the benefits of refactoring have historically been difficult for architects to quantify or justify. In this paper, we present a case study of identifying and quantifying such architecture debts in a large-scale industrial software project. Our approach is to model and analyze software architecture as a set of design rule spaces (DRSpaces). Using data extracted from the project's development artifacts, we were able to identify the files implicated in architecture flaws and suggest refactorings based on removing these flaws. Then we built economic models of the before and (predicted) after states, which gave the organization confidence that doing the refactorings made business sense, in terms of a handsome return on investment.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.146"}, {"primary_key": "4442045", "vector": [], "sparse_vector": [], "title": "A Large-Scale Technology Evaluation Study: Effects of Model-based Analysis and Testing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Besides model-based development, model-based quality assurance and the tighter integration of static and dynamic quality assurance activities are becoming increasingly relevant in the development of software-intensive systems. Thus, this paper reports on an empirical study aimed at investigating the promises regarding quality improvements and cost savings. The evaluation comprises data from 13 industry case studies conducted during a three-year large-scale research project in the transportation domain (automotive, avionics, rail system). During the evaluation, we identified major goals and strategies associated with (integrated) model-based analysis and testing and evaluated the improvements achieved. The aggregated results indicate an average cost reduction of between 29% and 34% for verification and validation and of between 22% and 32% for defect removal. Compared with these cost savings, improvements regarding test coverage (~8%), number of remaining defects (~13%), and time to market (~8%) appear less noticeable.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.141"}, {"primary_key": "4442051", "vector": [], "sparse_vector": [], "title": "Poster: Discovering Code Dependencies by Harnessing Developer&apos;s Activity.", "authors": ["<PERSON>", "Pavol Návrat", "<PERSON><PERSON><PERSON>"], "summary": "Monitoring software developer's interactions in an integrated development environment is sought for revealing new information about developers and developed software. In this paper we present an approach for identifying potential source code dependencies solely from interaction data. We identify three kinds of potential dependencies and additionally assign them to developer's activity as well, to reveal detailed task-related connections in the source code. Interaction data as a source allow us to identify these candidates for dependencies even for dynamically typed programming languages, or across multiple languages in the source code. After first evaluations and positive results we continue with collecting data in professional environment of Web developers, and evaluating our approach.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.255"}, {"primary_key": "4442052", "vector": [], "sparse_vector": [], "title": "Improving Student Group Work with Collaboration Patterns: A Case Study.", "authors": ["<PERSON>", "<PERSON><PERSON> C. J. D<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Group work skills are essential for Computer Scientists and especially Software Engineers. Group work is included in most CS curricula in order to support students in acquiring these skills. During group work, problems can occur related to a variety of factors, such as unstable group constellations or (missing) instructor support. Students need to find strategies for solving or preventing such problems. Student collaboration patterns offer a way of supporting students by providing problem-solving strategies that other students have already applied successfully. In this work we describe how student collaboration patterns were applied in an interdisciplinary software engineering project, and show that their application was generally experienced as helpful by the students.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.160"}, {"primary_key": "4442059", "vector": [], "sparse_vector": [], "title": "Dynamic Generation of Likely Invariants for Multithreaded Programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a new method for dynamically generating likely invariants from multithreaded programs.While existing invariant generation tools work well on sequential programs, they are ineffective at reasoning about multithreaded programs both in terms of the number of real invariants generated and in terms of their usefulness in helping programmers. We address this issue by developing a new dynamic invariant generator consisting of an LLVM based code instrumentation front end, a systematic thread interleaving explorer, and a customized invariant inference engine. We show that efficient interleaving exploration strategies can be used to generate a diversified set of executions with little runtime overhead. Furthermore, we show that focusing on a small subset of thread-local transition invariants is often sufficient for reasoning about the concurrency behavior of programs. We have evaluated our new method on a set of open-source multithreaded C/C++ benchmarks. Our experiments show that our method can generate invariants that are significantly higher in quality than the previous state-of-the-art.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.95"}, {"primary_key": "4442061", "vector": [], "sparse_vector": [], "title": "Borrowing from the Crowd: A Study of Recombination in Software Design Competitions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Lu<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "One form of crowdsourcing is the competition, which poses an open call for competing solutions. Commercial systems such as TopCoder have begun to explore the application of competitions to software development, but have important limitations diminishing the potential benefits drawn from the crowd. In particular, they employ a model of independent work that ignores the opportunity for designs to arise from the ideas of multiple designers. In this paper, we examine the potential for software design competitions to incorporate recombination, in which competing designers are given the designs of others and encouraged to use them to revise their own designs. To explore this, we conducted two software design competitions in which participants were asked to produce both an initial and a revised design, drawing on lessons learned from the crowd. We found that, in both competitions, all participants borrowed ideas and most improved the quality of their designs. Our findings demonstrate the potential benefits of recombination in software design and suggest several ways in which software design competitions can be improved.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.72"}, {"primary_key": "4442062", "vector": [], "sparse_vector": [], "title": "A Vision of Crowd Development.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Crowdsourcing has had extraordinary success in solving a diverse set of problems, ranging from digitization of libraries and translation of the Internet, to scientific challenges such as classifying elements in the galaxy or determining the 3D shape of an enzyme. By leveraging the power of the masses, it is feasible to complete tasks in mere days and sometimes even hours, and to take on tasks that were previously impossible because of their sheer scale. Underlying the success of crowdsourcing is a common theme - the microtask. By breaking down the overall task at hand into microtasks providing short, self-contained pieces of work, work can be performed independently, quickly, and in parallel - enabling numerous and often untrained participants to chip in. This paper puts forth a research agenda, examining the question of whether the same kinds of successes that microtask crowdsourcing is having in revolutionizing other domains can be brought to software development. That is, we ask whether it is possible to push well beyond the open source paradigm, which still relies on traditional, coarse-grained tasks, to a model in which programming proceeds through microtasks performed by vast numbers of crowd developers.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.194"}, {"primary_key": "4442066", "vector": [], "sparse_vector": [], "title": "Mining the Metadata - and Its Consequences.", "authors": ["<PERSON>"], "summary": "Traditionally metadata, the who, when, where of a phone call, the IP address, time, date of an Internet connection, has been viewed as deserving of less privacy than the contents of the communication. But ubiquitous computing and communication has changed that equation, and such transactional information has become increasingly revelatory. In this talk, I will discuss how metadata is used in all sorts of investigations, from malware to malfeasance. I will also discuss how the ubiquity of metadata must mean a change in our approaches to it.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.23"}, {"primary_key": "4442072", "vector": [], "sparse_vector": [], "title": "Why Good Developers Write Bad Code: An Observational Case Study of the Impacts of Organizational Factors on Software Quality.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "How can organizational factors such as structure and culture have an impact on the working conditions of developers? This study is based on ten months of observation of an in-house software development project within a large telecommunications company. The observation was conducted during mandatory weekly status meetings, where technical and managerial issues were raised and discussed. Preliminary results show that many decisions made under the pressure of certain organizational factors negatively affected software quality. This paper describes cases depicting the complexity of organizational factors and reports on ten issues that have had a negative impact on quality, followed by suggested avenues for corrective action.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.83"}, {"primary_key": "4442076", "vector": [], "sparse_vector": [], "title": "Software Design Studio: A Practical Example.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We have been generally successful for transferring software engineering knowledge to industry through various forms of education. However, many challenges in software engineering training remain. A key amongst these is how best to energise software engineering education with real-world software engineering practices. This paper describes our experience of delivering a radically different approach based on the notion of a Software Design Studio. The Software Design Studio is both a lab for students engaged in conceiving, designing and developing software products as well as an approach for teaching software engineering in the lab which emphasizes practical hands-on work and experimentation. The feedback on the Software Design Studio -- from both staff and students -- has been outstanding. Although the programme is designed as a small, elite programme there is interest to see if the teaching methods can be transferred across to the much larger undergraduate programme in Computer Science. In this paper, we provide a detailed description of how our studio works in practice so that others, thinking of tak-ing a studio or studio-inspired approach, can use in designing their own courses.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.171"}, {"primary_key": "4442077", "vector": [], "sparse_vector": [], "title": "Tempura: Temporal Dimension for IDEs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern integrated development environments (IDEs) make many software engineering tasks easier by providing automated programming support such as code completion and navigation. However, such support -- and therefore IDEs as awhole -- operate on one revision of the code at a time, and leave handling of code history to external tools or plugins, such asEGit for Eclipse. For example, when a method is removed froma class, developers can no longer find the method through code completion. This forces developers to manually switch across different revisions or resort to using external tools when they need to learn about previous code revisions.We propose a novel approach of adding a temporal dimensionto IDEs, enabling code completion and navigation to operate on multiple revisions of code at a time. We previously introduced the idea of temporal code completion and navigation,and presented a vision for how that idea may be realized.This paper realizes that vision by implementing and evaluatinga prototype tool called Tempura. We describe our algorithmfor processing and indexing historical code information from repositories for Tempura, and demonstrate Tempura's scalability with three large Eclipse projects. We also evaluate Tempura's usability through a controlled user study. The study participantslearned about the code history with more accuracy when usingTempura compared to EGit. Although the sample size was notlarge enough to provide strong statistical significance, the resultsshow a promising outlook for our approach.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.42"}, {"primary_key": "4442078", "vector": [], "sparse_vector": [], "title": "Evolution-Aware Monitoring-Oriented Programming.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Monitoring-Oriented Programming (MOP) helps develop more reliable software by means of monitoring against formal specifications. While MOP showed promising results, all prior research has focused on checking a single version of software. We propose to extend MOP to support multiple software versions and thus be more relevant in the context of rapid software evolution. Our approach, called eMOP, is inspired by regression test selection -- a well studied, evolution-centered technique. The key idea in eMOP is to monitor only the parts of code that changed between versions. We illustrate eMOP by means of a running example, and show the results of preliminary experiments. eMOP opens up a new line of research on MOP -- it can significantly improve usability and performance when applied across multiple versions of software and is complementary to algorithmic MOP advances on a single version.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.206"}, {"primary_key": "4442079", "vector": [], "sparse_vector": [], "title": "Mining Temporal Properties of Data Invariants.", "authors": ["<PERSON>"], "summary": "System specifications are important in maintaining program correctness, detecting bugs, understanding systems and guiding test case generation. Often, these specifications are not explicitly written by developers. If we want to use them for analysis, we need to obtain them through other methods; for example, by mining them out of program behavior. Several tools exist to mine data invariants and temporal properties from program traces, but few examine the temporal relationships between data invariants. An example of this kind of relationship would be \"the return value of the method isFull? is false until the field size reaches the value capacity\". We propose a data-temporal property miner, Quarry, which mines Linear Temporal Logic (LTL) relations of arbitrary length and complexity between Daikon-style data invariants. We infer data invariants from systems using Daikon, recompose these data invariants into sequences, and mine temporal properties over these sequences. Our preliminary results suggest that this method may recover important system properties.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.238"}, {"primary_key": "4442083", "vector": [], "sparse_vector": [], "title": "What Makes a Great Software Engineer?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Good software engineers are essential to the creation of good software. However, most of what we know about software-engineering expertise are vague stereotypes, such as 'excellent communicators' and 'great teammates'. The lack of specificity in our understanding hinders researchers from reasoning about them, employers from identifying them, and young engineers from becoming them. Our understanding also lacks breadth: what are all the distinguishing attributes of great engineers (technical expertise and beyond)? We took a first step in addressing these gaps by interviewing 59 experienced engineers across 13 divisions at Microsoft, uncovering 53 attributes of great engineers. We explain the attributes and examine how the most salient of these impact projects and teams. We discuss implications of this knowledge on research and the hiring and training of engineers.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.335"}, {"primary_key": "4442086", "vector": [], "sparse_vector": [], "title": "Morpheus: Variability-Aware Refactoring in the Wild.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today, many software systems are configurable with conditional compilation. Just like any software system, configurable systems need to be refactored in their evolution, but their inherent variability induces an additional dimension of complexity that is not addressed well by current academic and industrial refactoring engines. To improve the state of the art, we propose a variability-aware refactoring approach that relies on a canonical variability representation and recent work on variability-aware analysis. The goal is to preserve the behavior of all variants of a configurable system, without compromising general applicability and scalability. To demonstrate practicality, we developed Morpheus, a sound, variability-aware refactoring engine for C code with preprocessor directives. We applied Morpheus to three substantial real-world systems (Busybox, OpenSSL, and SQLite) showing that it scales reasonably well, despite of its heavy reliance on satisfiability solvers. By extending a standard approach of testing refactoring engines with support for variability, we provide evidence for the correctness of the refactorings implemented.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.57"}, {"primary_key": "4442089", "vector": [], "sparse_vector": [], "title": "Metamorphic Model-Based Testing Applied on NASA DAT - An Experience Report.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Testing is necessary for all types of systems, but becomes difficult when the tester cannot easily determine whether the system delivers the correct result or not. NASA's Data Access Toolkit allows NASA analysts to query a large database of telemetry data. Since the user is unfamiliar with the data and several data transformations can occur, it is impossible to determine whether the system behaves correctly or not in full scale production situations. Small scale testing was already conducted manually by other teams and unit testing was conducted on individual functions. However, there was still a need for full scale acceptance testing on a broad scale. We describe how we addressed this testing problem by applying the idea of metamorphic testing [1]. Specifically, we base it on equivalence of queries and by using the system itself for testing. The approach is implemented using a model-based testing approach in combination with a test data generation and test case outcome analysis strategy. We also discuss some of the issues that were detected using this approach.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.348"}, {"primary_key": "4442091", "vector": [], "sparse_vector": [], "title": "Poster: Enhancing Partition Testing through Output Variation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A major test case generation approach is to divide the input domain into disjoint partitions, from which test cases can be selected. However, we observe that in some traditional approaches to partition testing, the same partition may be associated with different output scenarios. Such an observation implies that the partitioning of the input domain may not be precise enough for effective software fault detection. To solve this problem, partition testing should be fine-tuned to additionally use the information of output scenarios in test case generation, such that these test cases are more fine-grained not only with respect to the input partitions but also from the perspective of output scenarios.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.257"}, {"primary_key": "4442096", "vector": [], "sparse_vector": [], "title": "Comparing Software Architecture Recovery Techniques Using Accurate Dependencies.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many techniques have been proposed to automatically recover software architectures from software implementations. A thorough comparison among the recovery techniques is needed to understand their effectiveness and applicability. This study improves on previous studies in two ways. First, we study the impact of leveraging more accurate symbol dependencies on the accuracy of architecture recovery techniques. Previous studies have not seriously considered how the quality of the input might affect the quality of the output for architecture recovery techniques. Second, we study a system (Chromium) that is substantially larger (9.7 million lines of code) than those included in previous studies. Obtaining the ground-truth architecture of Chromium involved two years of collaboration with its developers. As part of this work we developed a new sub module-based technique to recover preliminary versions of ground-truth architectures. The other systems that we study have been examined previously. In some cases, we have updated the ground-truth architectures to newer versions, and in other cases we have corrected newly discovered inconsistencies. Our evaluation of nine variants of six state-of-the-art architecture recovery techniques shows that symbol dependencies generally produce architectures with higher accuracies than include dependencies. Despite this improvement, the overall accuracy is low for all recovery techniques. The results suggest that (1) in addition to architecture recovery techniques, the accuracy of dependencies used as their inputs is another factor to consider for high recovery accuracy, and (2) more accurate recovery techniques are needed. Our results show that some of the studied architecture recovery techniques scale to the 10M lines-of-code range (the size of Chromium), whereas others do not.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.136"}, {"primary_key": "4442097", "vector": [], "sparse_vector": [], "title": "SOA4DM: Applying an SOA Paradigm to Coordination in Humanitarian Disaster Response.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Despite efforts to achieve a sustainable state of control over the management of global crises, disasters are occurring with greater frequency, intensity, and affecting many more people than ever before while the resources to deal with them do not grow apace. As we enter 2015, with continued concerns that mega-crises may become the new normal, we need to develop novel methods to improve the efficiency and effectiveness of our management of disasters. Software engineering as a discipline has long had an impact on society beyond its role in the development of software systems. In fact, software engineers have been described as the developers of prototypes for future knowledge workers; tools such as Github and Stack Overflow have demonstrated applications beyond the domain of software engineering. In this paper, we take the potential influence of software engineering one-step further and propose using the software service engineering paradigm as a new approach to managing disasters. Specifically, we show how the underlying principles of service-oriented architectures (SOA) can be applied to the coordination of disaster response operations. We describe key challenges in coordinating disaster response and discuss how an SOA approach can address those challenges.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.186"}, {"primary_key": "4442105", "vector": [], "sparse_vector": [], "title": "Exploration, Analysis, and Manipulation of Source Code Using srcML.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This technology briefing is intended for those interested in constructing custom software analysis and manipulation tools to support research or commercial applications. srcML (srcML.org) is an infrastructure consisting of an XML representation for C/C++/C#/Java source code along with efficient parsing technology to convert source code to-and-from the srcML format. The briefing describes srcML, the toolkit, and the application of XPath and XSLT to query and modify source code. Additionally, a hands-on tutorial of how to use srcML and XML tools to construct custom analysis and manipulation tools will be conducted.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.302"}, {"primary_key": "4442108", "vector": [], "sparse_vector": [], "title": "Performance Analysis Using Subsuming Methods: An Industrial Case Study.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Large-scale object-oriented applications consist of tens of thousands of methods and exhibit highly complex runtime behaviour that is difficult to analyse for performance. Typical performance analysis approaches that aggregate performance measures in a method-centric manner result in thinly distributed costs and few easily identifiable optimisation opportunities. Subsuming methods analysis is a new approach that aggregates performance costs across repeated patterns of method calls that occur in the application's runtime behaviour. This allows automatic identification of patterns that are expensive and represent practical optimisation opportunities. To evaluate the practicality of this analysis with a real world large-scale object-oriented application we completed a case study with the developers of letterboxd.com - a social network website for movie goers. Using the results of the analysis we were able to rapidly implement changes resulting in a 54.8% reduction in CPU load and an 49.6% reduction in average response time.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.143"}, {"primary_key": "4442109", "vector": [], "sparse_vector": [], "title": "Remote Development and Distance Delivery of Innovative Courses: Challenges and Opportunities.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The Rochester Institute of Technology (RIT) offers programs of study at several of RIT's international campuses: Dubrovnik and Zagreb (Croatia), Dubai (United Arab Emirates) and Priatina (Kosovo). At RIT Croatia, some courses are delivered as distance education courses using Polycom, a video conferencing system, supported by other online education tools. Although distance learning methods and tools provide an effective way to offer instructions remotely, delivering a course that emphasizes team-based software development, with laboratory exercises and in-class team activities, creates new challenges that need to be addressed. This paper discusses the authors' experiences with the remote development and delivery of one of those courses - the SWEN-383 Software Design Principles and Patterns course in the Information Technology program at RIT Croatia. The paper first explains the role and need for offering this particular course. It then discusses how the collaborative development of this new course was conducted between the U.S. And the Croatian campuses, including remote delivery from Zagreb to Dubrovnik. The paper concludes with observations and suggestions for those who may engage in such a project in the future.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.159"}, {"primary_key": "4442112", "vector": [], "sparse_vector": [], "title": "Empirical Study Towards a Leading Indicator for Cost of Formal Software Verification.", "authors": ["<PERSON>", "<PERSON>", "June <PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Formal verification can provide the highest degree of software assurance. Demand for it is growing, but there are still few projects that have successfully applied it to sizeable, real-world systems. This lack of experience makes it hard to predict the size, effort and duration of verification projects. In this paper, we aim to better understand possible leading indicators of proof size. We present an empirical analysis of proofs from the landmark formal verification of the seL4 microkernel and the two largest software verification proof developments in the Archive of Formal Proofs. Together, these comprise 15,018 individual lemmas and approximately 215,000 lines of proof script. We find a consistent quadratic relationship between the size of the formal statement of a property, and the final size of its formal proof in the interactive theorem prover Isabelle. Combined with our prior work, which has indicated that there is a strong linear relationship between proof effort and proof size, these results pave the way for effort estimation models to support the management of large-scale formal verification projects.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.85"}, {"primary_key": "4442113", "vector": [], "sparse_vector": [], "title": "1st International Workshop on TEchnical and LEgal aspects of data pRIvacy and Security (TELERISE 2015).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper is the report on the 1st International Workshop on TEchnical and LEgal aspects of data pRIvacy and SEcurity (TELERISE 2015) at the 37th International Conference on Software Engineering (ICSE 2015). TELERISE investigates privacy and security issues in data sharing from a technical and legal perspective. Keynote speech as well as selected papers presented at the event fit the topics of the workshop. This report gives the rationale of TELERISE and it provides a provisional program.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.329"}, {"primary_key": "4442117", "vector": [], "sparse_vector": [], "title": "Automatic Documentation Generation via Source Code Summarization.", "authors": ["<PERSON>"], "summary": "Programmers need software documentation. However, documentation is expensive to produce and maintain, and often becomes outdated over time. Programmers often lack the time and resources to write documentation. Therefore, automated solutions are desirable. Designers of automatic documentation tools are limited because there is not yet a clear understanding of what characteristics are important to generating high quality summaries. I propose three specific research objectives to improving automatic documentation generation. I propose to study the similarity between source code and summary. Second, I propose studying whether or not including contextual information about source code improves summary quality. Finally, I propose to study the problem of similarity in source code structure and source code documentation. This paper discusses my work on these three objectives towards my Ph.D. dissertation, including my preliminary and proposed work.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.288"}, {"primary_key": "4442119", "vector": [], "sparse_vector": [], "title": "B<PERSON>ie: Finding and Understanding Inconsistent Code.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present <PERSON><PERSON>ie, a tool to detect inconsistencies in Java code. <PERSON><PERSON>ie detectsinconsistent code at a higher precision than previous tools and provides novelfault localization techniques to explain why code is inconsistent. Wedemonstrate the usefulness of <PERSON><PERSON><PERSON> on over one million lines of code, showthat it can detect inconsistencies at a low false alarm rate, and fix a numberof inconsistencies in popular open-source projects. Watch our Demo at http://youtu.be/QpsoUBJMxhk.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.213"}, {"primary_key": "4442120", "vector": [], "sparse_vector": [], "title": "Industry/University Collaboration in Software Engineering Education: Refreshing and Retuning Our Strategies.", "authors": ["<PERSON>"], "summary": "This panel session will explore strategies for industry/university collaboration in software engineering education. Specific discussion topics will include new strategies for successful industry/university collaboration, exploration of reasons why some of the old strategies no longer work, and regional/geographical differences noted by the international set of panelists. The panel hopes to identify new promising strategies for such collaborations. Specific industry representatives will be invited to attend and participate in the discussion.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.156"}, {"primary_key": "4442121", "vector": [], "sparse_vector": [], "title": "DirectFix: Looking for Simple Program Repairs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent advances in program repair techniques have raised the possibility of patching bugs automatically. For an automatically generated patch to be accepted by developers, it should not only resolve the bug but also satisfy certain human-related factors including readability and comprehensibility. In this paper, we focus on the simplicity of patches (the size of changes). We present a novel semantics-based repair method that generates the simplest patch such that the program structure of the buggy program is maximally preserved. To take into account the simplicity of repairs in an efficient way (i.e., Without explicitly enumerating each repair candidate for each fault location), our method fuses fault localization and repair generation into one step. We do so by leveraging partial Max SAT constraint solving and component-based program synthesis. We compare our prototype implementation, Direct Fix, with the state-of-the-art semantics-based repair tool Sem Fix, that performs fault localization before repair generation. In our experiments with SIR programs and GNU Coreutils, Direct Fix generates repairs that are simpler than those generated by Sem Fix. Since both Direct Fix and Sem Fix are test-driven repair tools, they can introduce regressions for other tests which do not drive the repair. We found that Direct Fix causes substantially less regression errors than Sem Fix.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.63"}, {"primary_key": "4442122", "vector": [], "sparse_vector": [], "title": "The Art and Science of Analyzing Software Data; Quantitative Methods.", "authors": ["<PERSON>", "Leandro L<PERSON>", "<PERSON><PERSON>"], "summary": "Using the tools of quantitative data science, software engineers that can predict useful information on new projects based on past projects. This tutorial reflects on the state-of-the-art in quantitative reasoning in this important field. This tutorial discusses the following: (a) when local data is scarce, we show how to adapt data from other organizations to local problems; (b) when working with data of dubious quality, we show how to prune spurious information; (c) when data or models seem too complex, we show how to simplify data mining results; (d) when the world changes, and old models need to be updated, we show how to handle those updates; (e) when the effect is too complex for one model, we show to how reason over ensembles.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.306"}, {"primary_key": "4442123", "vector": [], "sparse_vector": [], "title": "Verification of Android Applications.", "authors": ["<PERSON><PERSON>"], "summary": "This study investigates an alternative approach to analyze Android applications using model checking. We develop an extension to Java Path Finder (JPF) called JPF-Android to verify Android applications outside of the Android platform. JPF is a powerful Java model checker and analysis engine that is very effective at detecting corner-case and hard-to-find errors using its fine-grained analysis capabilities. JPF-Android provides a simplified model of the Android application framework on which an Android application can run and it can generate input events or parse an input script containing sequences of input events to drive the execution of the application. JPF-Android traverses all execution paths of the application by simulating these input events and can detect common property violations such as deadlocks and runtime exceptions in Android applications. It also introduces user defined execution specifications called Checklists to verify the flow of application execution.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.295"}, {"primary_key": "4442124", "vector": [], "sparse_vector": [], "title": "Supporting Scientific SE Process Improvement.", "authors": ["<PERSON><PERSON>"], "summary": "The increasing complexity of scientific software can result in significant impacts on the research itself. In traditional software development projects, teams adopt historical best practices into their development processes to mitigate the risk of such problems. In contrast, the gap that has formed between the traditional and scientific software communities leaves scientists to rely on only their own experience when facing software process improvement (SPI) decisions. Rather than expect scientists to become software engineering (SE) experts or the SE community to learn all of the intricacies involved in scientific software development projects, we seek a middle ground. The Scientific Software Process Improvement Framework (SciSPIF) will allow scientists to self-drive their own SPI efforts while leveraging the collective experiences of their peers and linking their concerns to established SE best practices. This proposal outlines the known challenges of scientific software development, relevant concepts from traditional SE research, and our planned methodology for collecting the data required to construct SciSPIF while staying grounded in the actual goals and concerns of the scientists.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.293"}, {"primary_key": "4442126", "vector": [], "sparse_vector": [], "title": "Alloy*: A General-Purpose Higher-Order Relational Constraint Solver.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The last decade has seen a dramatic growth in the use of constraint solvers as a computational mechanism, not only for analysis of software, but also at runtime. Solvers are available for a variety of logics but are generally restricted to first-order formulas. Some tasks, however, most notably those involving synthesis, are inherently higher order; these are typically handled by embedding a first-order solver (such as a SAT or SMT solver) in a domain-specific algorithm. Using strategies similar to those used in such algorithms, we show how to extend a first-order solver (in this case Kodkod, a model finder for relational logic used as the engine of the Alloy Analyzer) so that it can handle quantifications over higher-order structures. The resulting solver is sufficiently general that it can be applied to a range of problems; it is higher order, so that it can be applied directly, without embedding in another algorithm; and it performs well enough to be competitive with specialized tools. Just as the identification of first-order solvers as reusable backends advanced the performance of specialized tools and simplified their architecture, factoring out higher-order solvers may bring similar benefits to a new class of tools.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.77"}, {"primary_key": "4442127", "vector": [], "sparse_vector": [], "title": "Free Hugs - Praising Developers for Their Actions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Developing software is a complex, intrinsically intellectual, and therefore ephemeral activity, also due to the intangible nature of the end product, the source code. There is a thin red line between a productive development session, where a developer actually does something useful and productive, and a session where the developer essentially produces \"fried air\", pieces of code whose quality and usefulness are doubtful at best. We believe that well-thought mechanisms of gamification built on fine-grained interaction information mined from the IDE can crystallize and reward good coding behavior. We present our preliminary experience with the design and implementation of a micro-gamification layer built into an object-oriented IDE, which at the end of each development session not only helps the developer to understand what he actually produced, but also praises him in case the development session was productive. Building on this, we envision an environment where the IDE reflects on the deeds of the developers and by providing a historical view also helps to track and reward long-term growth in terms of development skills, not dissimilar from the mechanics of role-playing games.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.342"}, {"primary_key": "4442134", "vector": [], "sparse_vector": [], "title": "Transparently Teaching in the Context of Game-based Learning: the Case of SimulES-W.", "authors": ["<PERSON>", "<PERSON> Prado Le<PERSON>", "<PERSON>"], "summary": "This work presents a pedagogical proposal, in the context of game-based learning (GBL), that uses the concept of Transparency Pedagogy. As such, it aims to improve the quality of teaching, and the relationship between student, teacher and teaching methods. Transparency is anchored in the principle of information disclosure. In pedagogy, transparency emerges as an important issue that proposes to raise student awareness about the educational processes. Using GBL as an educational strategy we managed to make the game, a software, transparent. That is we made the inner processes of the game known to the students. As such, besides learning by playing, students had access to the game design, through intentional modeling. We collected evidence that, by disclosure of the information about the design, students better performed on learning software engineering.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.167"}, {"primary_key": "4442138", "vector": [], "sparse_vector": [], "title": "How Can I Use This Method?", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Code examples are small source code fragments whose purpose is to illustrate how a programming language construct, an API, or a specific function/method works. Since code examples are not always available in the software documentation, researchers have proposed techniques to automatically extract them from existing software or to mine them from developer discussions. In this paper we propose MUSE (Method USage Examples), an approach for mining and ranking actual code examples that show how to use a specific method. MUSE combines static slicing (to simplify examples) with clone detection (to group similar examples), and uses heuristics to select and rank the best examples in terms of reusability, understandability, and popularity. MUSE has been empirically evaluated using examples mined from six libraries, by performing three studies involving a total of 140 developers to: (i) evaluate the selection and ranking heuristics, (ii) provide their perception on the usefulness of the selected examples, and (iii) perform specific programming tasks using the MUSE examples. The results indicate that MUSE selects and ranks examples close to how humans do, most of the code examples (82%) are perceived as useful, and they actually help when performing programming tasks.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.98"}, {"primary_key": "4442140", "vector": [], "sparse_vector": [], "title": "4th International Workshop on Green and Sustainable Software (GREENS 2015).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Engineering green software-intensive systems is critical in our drive towards a sustainable, smarter planet. The goal of green software engineering is to apply green principles to the design and operation of software-intensive systems. Green and self-greening software systems have tremendous potential to decrease energy consumption. Moreover, enterprise software can and should be re-thought to address sustainability issues using innovative business models, processes, and incentives. Monitoring and measuring the greenness of software is critical towards the notion of sustainable and green software. Demonstrating improvement is paramount for users to achieve and affect change. Thus, the theme of GREENS 2015 is Towards a Green Software Body of Knowledge. The GREENS workshop series brings together researchers and practitioners to discuss both the state-of-the-art and state-of-the-practice in green software, including novel ideas, research challenges, methods, experiences, and tools to support the engineering of sustainable and energy efficient software systems.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.315"}, {"primary_key": "4442141", "vector": [], "sparse_vector": [], "title": "A Security Practices Evaluation Framework.", "authors": ["<PERSON>"], "summary": "Software development teams need guidance on choosing security practices so they can develop code securely. The academic and practitioner literature on software development security practices is large, and expanding. However, published empirical evidence for security practice use in software development is limited and fragmented, making choosing appropriate practices difficult. Measurement frameworks offer a tool for collecting and comparing software engineering data. The goal of this work is to aid software practitioners in evaluating security practice use in the development process by defining and validating a measurement framework for software development security practice use and outcomes. We define the Security Practices Evaluation Framework (SP-EF), a measurement framework for software development security practices. SP-EF supports evidence-based practice selection. To enable comparison of practices across publications and projects, we define an ontology of software development security practices. We evaluate the framework and ontology on historical data and industrial projects.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.296"}, {"primary_key": "4442142", "vector": [], "sparse_vector": [], "title": "Measuring Software Developers&apos; Perceived Difficulty with Biometric Sensors.", "authors": ["<PERSON>"], "summary": "As a developer works on a change task, he or she might perceive some parts of the task as easy and other parts as being very difficult. Currently, little is known about when a developer experiences different difficulty levels, although being able to assess these difficulty levels would be helpful for many reasons. For instance, a developer's perceived difficulty might be used to determine the likelihood of a bug being introduced into the code or the quality of the code a developer is working with. In psychology, biometric measurements, such as electro-dermal activity or heart rate, have already been extensively used to assess a person's mental state and emotions, but only little research has been conducted to investigate how these sensors can be used in the context of software engineering. In our research we want to take advantage of the insights gained in these psychological studies and investigate whether such biometric sensors can be used to measure developers' perceived difficulty while working on a change task and support them in their work.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.284"}, {"primary_key": "4442143", "vector": [], "sparse_vector": [], "title": "Stuck and Frustrated or in Flow and Happy: Sensing Developers&apos; Emotions and Progress.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Software developers working on change tasks commonly experience a broad range of emotions, ranging from happiness all the way to frustration and anger. Research, primarily in psychology, has shown that for certain kinds of tasks, emotions correlate with progress and that biometric measures, such as electro-dermal activity and electroencephalography data, might be used to distinguish between emotions. In our research, we are building on this work and investigate developers' emotions, progress and the use of biometric measures to classify them in the context of software change tasks. We conducted a lab study with 17 participants working on two change tasks each. Participants were wearing three biometric sensors and had to periodically assess their emotions and progress. The results show that the wide range of emotions experienced by developers is correlated with their perceived progress on the change tasks. Our analysis also shows that we can build a classifier to distinguish between positive and negative emotions in 71.36% and between low and high progress in 67.70% of all cases. These results open up opportunities for improving a developer's productivity. For instance, one could use such a classifier for providing recommendations at opportune moments when a developer is stuck and making no progress.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.334"}, {"primary_key": "4442147", "vector": [], "sparse_vector": [], "title": "Big(ger) Data in Software Engineering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "\"Big Data\" analytics has become the next hot topic for most companies - from financial institutions to technology companies to service providers. Likewise in software engineering, data collected about the development of software, the operation of the software in the field, and the users feedback on software have been used before. However, collecting and analyzing this information across hundreds of thousands or millions of software projects gives us the unique ability to reason about the ecosystem at large, and software in general. At no time in history has there been easier access to extremely powerful computational resources as it is today, thanks to the advances in cloud computing, both from the technology and business perspectives. In this technical briefing, we will present the state-of-the-art with respect to the research carried out in the area of big data analytics in software engineering research.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.305"}, {"primary_key": "4442151", "vector": [], "sparse_vector": [], "title": "A Comparative Study of Programming Languages in Rosetta Code.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Sometimes debates on programming languages are more religious than scientific. Questions about which language is more succinct or efficient, or makes developers more productive are discussed with fervor, and their answers are too often based on anecdotes and unsubstantiated beliefs. In this study, we use the largely untapped research potential of Rosetta Code, a code repository of solutions to common programming tasks in various languages, to draw a fair and well-founded comparison. Rosetta Code offers a large data set for analysis. Our study is based on 7087 solution programs corresponding to 745 tasks in 8 widely used languages representing the major programming paradigms (procedural: C and Go; object-oriented: C# and Java; functional: F# and Haskell; scripting: Python and Ruby). Our statistical analysis reveals, most notably, that: functional and scripting languages are more concise than procedural and object-oriented languages; C is hard to beat when it comes to raw speed on large inputs, but performance differences over inputs of moderate size are less pronounced and allow even interpreted languages to be competitive; compiled strongly-typed languages, where more defects can be caught at compile time, are less prone to runtime failures than interpreted or weakly-typed languages. We discuss implications of these results for developers, language designers, and educators.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.90"}, {"primary_key": "4442152", "vector": [], "sparse_vector": [], "title": "An Initiative to Improve Reproducibility and Empirical Evaluation of Software Testing Techniques.", "authors": ["<PERSON>", "<PERSON>", "Patrícia D. L. Machado"], "summary": "The current concern regarding quality of evaluation performed in existing studies reveals the need for methods and tools to assist in the definition and execution of empirical studies and experiments. However, when trying to apply general methods from empirical software engineering in specific fields, such as evaluation of software testing techniques, new obstacles and threats to validity appears, hindering researchers' use of empirical methods. This paper discusses those issues specific for evaluation of software testing techniques and proposes an initiative for a collaborative effort to encourage reproducibility of experiments evaluating software testing techniques (STT). We also propose the development of a tool that enables automatic execution and analysis of experiments producing a reproducible research compendia as output that is, in turn, shared among researchers. There are many expected benefits from this Endeavour, such as providing a foundation for evaluation of existing and upcoming STT, and allowing researchers to devise and publish better experiments.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.197"}, {"primary_key": "4442154", "vector": [], "sparse_vector": [], "title": "The Role of Design Thinking and Physical Prototyping in Social Software Engineering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Adrian Friday", "<PERSON>"], "summary": "Social Software Engineering (Social SE), that is SE aiming to promote positive social change, is a rapidly emerging area. Here, software and digital artefacts are seen as tools for social change, rather than end products or 'solutions'. Moreover, Social SE requires a sustained buy-in from a range of stakeholders and end-users working in partnership with multidisciplinary software development teams often at a distance. This context poses new challenges to software engineering: it requires both an agile approach for handling uncertainties in the software development process, and the application of participatory, creative design processes to bridge the knowledge asymmetries and the geographical distances in the partnership. This paper argues for the role of design thinking in Social SE and highlights its implications for software engineering in general. It does so by reporting on the contributions that design thinking -- and in particular physical design -- has brought to (1) the problem space definition, (2) user requirements capture and (3) system feature design of a renewable energy forecasting system developed in partnership with a remote Scottish Island community.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.181"}, {"primary_key": "4442155", "vector": [], "sparse_vector": [], "title": "Varis: IDE Support for Embedded Client Code in PHP Web Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In software development, IDE services are used to assist developers in programming tasks. In dynamic web applications, however, since the client-side code is embedded in the server-side program as string literals, providing IDE services for such embedded code is challenging. We introduce Varis, a tool that provides services on the embedded client-side code. We perform symbolic execution on a PHP program to approximate its output and parse it into a VarDOM that compactly represents all its DOM variations. Using the VarDOM, we implement various types of IDE services for embedded client code including syntax highlighting, code completion, and 'find declaration'.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.225"}, {"primary_key": "4442156", "vector": [], "sparse_vector": [], "title": "Graph-Based Statistical Language Model for Code.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "n-gram statistical language model has been successfully applied to capture programming patterns to support code completion and suggestion. However, the approaches using n-gram face challenges in capturing the patterns at higher levels of abstraction due to the mismatch between the sequence nature in n-grams and the structure nature of syntax and semantics in source code. This paper presents GraLan, a graph-based statistical language model and its application in code suggestion. GraLan can learn from a source code corpus and compute the appearance probabilities of any graphs given the observed (sub)graphs. We use GraLan to develop an API suggestion engine and an AST-based language model, ASTLan. ASTLan supports the suggestion of the next valid syntactic template and the detection of common syntactic templates. Our empirical evaluation on a large corpus of open-source projects has shown that our engine is more accurate in API code suggestion than the state-of-the-art approaches, and in 75% of the cases, it can correctly suggest the API with only five candidates. ASTLan has also high accuracy in suggesting the next syntactic template and is able to detect many useful and common syntactic templates.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.336"}, {"primary_key": "4442162", "vector": [], "sparse_vector": [], "title": "CARAMEL: Detecting and Fixing Performance Problems That Have Non-Intrusive Fixes.", "authors": ["<PERSON>", "Po<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Performance bugs are programming errors that slow down program execution. While existing techniques can detect various types of performance bugs, a crucial and practical aspect of performance bugs has not received the attention it deserves: how likely are developers to fix a performance bug? In practice, fixing a performance bug can have both benefits and drawbacks, and developers fix a performance bug only when the benefits outweigh the drawbacks. Unfortunately, for many performance bugs, the benefits and drawbacks are difficult to assess accurately. This paper presents CARAMEL, a novel static technique that detects and fixes performance bugs that have non-intrusive fixes likely to be adopted by developers. Each performance bug detected by CARAMEL is associated with a loop and a condition. When the condition becomes true during the loop execution, all the remaining computation performed by the loop is wasted. Developers typically fix such performance bugs because these bugs waste computation in loops and have non-intrusive fixes: when some condition becomes true dynamically, just break out of the loop. Given a program, CARAMEL detects such bugs statically and gives developers a potential source-level fix for each bug. We evaluate CARAMEL on real-world applications, including 11 popular Java applications (e.g., Groovy, Log4J, Lucene, Struts, Tomcat, etc) and 4 widely used C/C++ applications (Chromium, GCC, Mozilla, and My SQL). CARAMEL finds 61 new performance bugs in the Java applications and 89 new performance bugs in the C/C++ applications. Based on our bug reports, developers so far have fixed 51 and 65 performance bugs in the Java and C/C++ applications, respectively. Most of the remaining bugs are still under consideration by developers.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.100"}, {"primary_key": "4442163", "vector": [], "sparse_vector": [], "title": "Optimising Energy Consumption of Design Patterns.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software design patterns are widely used in software engineering to enhance productivity and maintainability.However, recent empirical studies revealed the high energy overhead in these patterns. Our vision is to automatically detect and transform design patterns during compilation for better energy efficiency without impacting existing coding practices. In this paper, we propose compiler transformations for two design patterns, Observer and Decorator, and perform an initial evaluation of their energy efficiency.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.208"}, {"primary_key": "4442165", "vector": [], "sparse_vector": [], "title": "Detecting Inconsistencies in JavaScript MVC Applications.", "authors": ["<PERSON><PERSON>.", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Higher demands for more reliable and maintainable JavaScript-based web applications have led to the recent development of MVC (Model-View-Controller) frameworks. One of the main advantages of using these frameworks is that they abstract out DOM API method calls, which are one of the leading causes of web application faults, due to their often complicated interaction patterns. However, MVC frameworks are susceptible to inconsistencies between the identifiers and types of variables and functions used throughout the application. In response to this problem, we introduce a formal consistency model for web applications made using MVC frameworks. We propose an approach -- called Aurebesh -- that automatically detects inconsistencies in such applications. We evaluate Aurebesh by conducting a fault injection experiment and by running it on real applications. Our results show that Aurebesh is accurate, with an overall recall of 96.1% and a precision of 100%. It is also useful in detecting bugs, allowing us to find 15 real-world bugs in applications built on Angular JS, a popular MVC framework.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.52"}, {"primary_key": "4442166", "vector": [], "sparse_vector": [], "title": "Composite Constant Propagation: Application to Android Inter-Component Communication Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Many program analyses require statically inferring the possible values of composite types. However, current approaches either do not account for correlations between object fields or do so in an ad hoc manner. In this paper, we introduce the problem of composite constant propagation. We develop the first generic solver that infers all possible values of complex objects in an interprocedural, flow and context-sensitive manner, taking field correlations into account. Composite constant propagation problems are specified using COAL, a declarative language. We apply our COAL solver to the problem of inferring Android Inter-Component Communication (ICC) values, which is required to understand how the components of Android applications interact. Using COAL, we model ICC objects in Android more thoroughly than the state-of-the-art. We compute ICC values for 460 applications from the Play store. The ICC values we infer are substantially more precise than previous work. The analysis is efficient, taking slightly over two minutes per application on average. While this work can be used as the basis for many whole-program analyses of Android applications, the COAL solver can also be used to infer the values of composite objects in many other contexts.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.30"}, {"primary_key": "4442169", "vector": [], "sparse_vector": [], "title": "3rd International Workshop on Software Engineering for Systems-of-Systems (SESoS 2015).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Paris Avgeriou", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Systems-of-Systems (SoS) refer to a new class of software-intensive systems, where their constituent systems work cooperatively in order to fulfill specific missions. Characterized by managerial and operational independence, geographic distribution, evolutionary development, and emergent behavior, SoS bring substantial challenges to the software engineering area. SESoS 2015, held in Florence, Italy, on May 17, 2015, as a joint workshop of the 37th International Conference on Software Engineering (ICSE), provided a forum to exchange ideas and experiences, analyze current research and development issues, discuss promising solutions, and to explore inspiring visions for the future of Software Engineering (SE) for SoS.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.327"}, {"primary_key": "4442172", "vector": [], "sparse_vector": [], "title": "Strategies for Prioritizing Test Cases Generated Through Model-Based Testing Approaches.", "authors": ["<PERSON>"], "summary": "Software testing is expensive and time consuming,especially for complex software. In order to deal with the costof testing, researchers develop Model-Based Testing (MBT). InMBT, test cases are generated automatically and a drawback isa huge generated test suite. Our research aims at studying the Test Case Prioritization problem in MBT context. So far, we already evaluated the influence of the model structure and the characteristics of the test cases that fail. Results suggest that the former does not affect significantly the performance of techniques, however, the latter indeed represents a major impact. Therefore, a worthy information in this context might be an expert who knows the crucial parts of the software, thus we propose the first version of a prioritization technique that considers hints from the expert and the distance notion in order to prioritize test cases. Evaluation and tuning of the technique are ongoing, but preliminary evaluation reveals promising results.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.338"}, {"primary_key": "4442173", "vector": [], "sparse_vector": [], "title": "Second International Workshop on Software Architecture and Metrics (SAM 2015).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Paris Avgeriou"], "summary": "Software engineers and architects of complex software systems need to balance hard quality attribute requirements while at the same time manage risks and make decisions with a system-wide and long-lasting impact. To achieve these tasks efficiently, they need quantitative information about design-time and run-time system aspects through usable and quick tools. While there is body of work focusing on code quality and metrics, their applicability at the design and architecture level and at scale are inconsistent and not proven. We are interested in exploring whether architecture can assist with better contextualizing existing system and code quality and metrics approaches. Furthermore, we ask whether we need additional architecture-level metrics to make progress and whether something as complex and subtle as software architecture can be quantified. The goal of this workshop is to discuss progress, gather empirical evidence, and identify priorities for a research agenda on architecture and metrics in the software engineering field.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.346"}, {"primary_key": "4442174", "vector": [], "sparse_vector": [], "title": "Learning Global Agile Software Engineering Using Same-Site and Cross-Site Teams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We describe an experience in teaching global software engineering (GSE) using distributed Scrum augmented with industrial best practices. Our unique instructional technique had students work in both same-site and cross-site teams to contrast the two modes of working. The course was a collaboration between Aalto University, Finland and University of Victoria, Canada. Fifteen Canadian and eight Finnish students worked on a single large project, divided into four teams, working on interdependent user stories as negotiated with the industrial product owner located in Finland. Half way through the course, we changed the teams so each student worked in both a local and a distributed team. We studied student learning using a mixed-method approach including 14 post-course interviews, pre-course and Sprint questionnaires, observations, meeting recordings, and repository data from git and Flow dock, the primary communication tool. Our results show no significant differences between working in distributed vs. Non-distributed teams, suggesting that Scrum helps alleviate many GSE problems. Our post-course interviews and survey data allows us to explain this effect, we found that students over time learned to better self-select tasks with less inter-team dependencies, to communicate more, and to work better in teams.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.157"}, {"primary_key": "4442175", "vector": [], "sparse_vector": [], "title": "Smart Programming Playgrounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern IDEs contain sophisticated components for inferring missing types, correcting bad syntax and completing partial expressions in code, but they are limited to the context that is explicitly defined in a project's configuration. These tools are ill-suited for quick prototyping of incomplete code snippets, such as those found on the Web in Q&A forums or walk-through tutorials, since such code snippets often assume the availability of external dependencies and may even contain implicit references to an execution environment that provides data or compute services. We propose an architecture for smart programming playgrounds that can facilitate rapid prototyping of incomplete code snippets through a semi-automatic context resolution that involves identifying static dependencies, provisioning external resources on the cloud and injecting resource bindings to handles in the original code fragment. Such a system could be potentially useful in a range of different scenarios, from sharing code snippets on the Web to experimenting with new ideas during traditional software development.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.204"}, {"primary_key": "4442177", "vector": [], "sparse_vector": [], "title": "Textual Analysis for Code Smell Detection.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The negative impact of smells on the quality of a software systems has been empirical investigated in several studies. This has recalled the need to have approaches for the identification and the removal of smells. While approaches to remove smells have investigated the use of both structural and conceptual information extracted from source code, approaches to identify smells are based on structural information only. In this paper, we bridge the gap analyzing to what extent conceptual information, extracted using textual analysis techniques, can be used to identify smells in source code. The proposed textual-based approach for detecting smells in source code, coined as TACO (Textual Analysis for Code smell detectiOn), has been instantiated for detecting the Long Method smell and has been evaluated on three Java open source projects. The results indicate that TACO is able to detect between 50% and 77% of the smell instances with a precision ranging between 63% and 67%. In addition, the results show that TACO identifies smells that are not identified by approaches based on solely structural information.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.244"}, {"primary_key": "4442178", "vector": [], "sparse_vector": [], "title": "Extract Package Refactoring in ARIES.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Software evolution often leads to the degradation of software design quality. In Object-Oriented (OO) systems, this often results in packages that are hard to understand and maintain, as they group together heterogeneous classes with unrelated responsibilities. In such cases, state-of-the-art re-modularization tools solve the problem by proposing a new organization of the existing classes into packages. However, as indicated by recent empirical studies, such approaches require changing thousands of lines of code to implement the new recommended modularization. In this demo, we present the implementation of an Extract Package refactoring approach in ARIES (Automated Refactoring In EclipSe), a tool supporting refactoring operations in Eclipse. Unlike state-of-the-art approaches, ARIES automatically identifies and removes single low-cohesive packages from software systems, which represent localized design flaws in the package organization, with the aim to incrementally improve the overall quality of the software modularisation.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.219"}, {"primary_key": "4442180", "vector": [], "sparse_vector": [], "title": "Gray Computing: An Analysis of Computing with Background JavaScript Tasks.", "authors": ["<PERSON>", "<PERSON>", "Yu Sun", "<PERSON>"], "summary": "Websites routinely distribute small amounts of work to visitors' browsers in order to validate forms, render animations, and perform other computations. This paper examines the feasibility, cost effectiveness, and approaches for increasing the workloads offloaded to web visitors' browsers in order to turn them into a large-scale distributed data processing engine, which we term gray computing. Past research has looked primarily at either non-browser based volunteer computing or browser-based volunteer computing where the visitors keep their browsers open to a single web page for a long period of time. This paper provides a deep analysis of the architectural, cost effectiveness, user experience, performance, security, and other issues of gray computing distributed data processing engines with high heterogeneity, non-uniform page view times, and high computing pool volatility.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.38"}, {"primary_key": "4442183", "vector": [], "sparse_vector": [], "title": "Trivial Compiler Equivalence: A Large Scale Empirical Study of a Simple, Fast and Effective Equivalent Mutant Detection Technique.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Identifying equivalent mutants remains the largest impediment to the widespread uptake of mutation testing. Despite being researched for more than three decades, the problem remains. We propose Trivial Compiler Equivalence (TCE) a technique that exploits the use of readily available compiler technology to address this long-standing challenge. TCE is directly applicable to real-world programs and can imbue existing tools with the ability to detect equivalent mutants and a special form of useless mutants called duplicated mutants. We present a thorough empirical study using 6 large open source programs, several orders of magnitude larger than those used in previous work, and 18 benchmark programs with hand-analysis equivalent mutants. Our results reveal that, on large real-world programs, TCE can discard more than 7% and 21% of all the mutants as being equivalent and duplicated mutants respectively. A human- based equivalence verification reveals that TCE has the ability to detect approximately 30% of all the existing equivalent mutants.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.103"}, {"primary_key": "4442186", "vector": [], "sparse_vector": [], "title": "Systematic Testing of Reactive Software with Non-Deterministic Events: A Case Study on LG Electric Oven.", "authors": ["Yongbae Park", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Most home appliance devices such as electric ovens are reactive systems which repeat receiving a user input/event through an event handler, updating their internal state based on the input, and generating outputs. A challenge to test a reactive program is to check if the program correctly reacts to various non-deterministic sequence of events because an unexpected sequence of events may make the system fail due to the race conditions between the main loop and asynchronous event handlers. Thus, it is important to systematically generate/test various sequences of events by controlling the order of events and relative timing of event occurrences with respect to the main loop execution. In this paper, we report our industrial experience to solve the aforementioned problem by developing a systematic event generation framework based on concolic testing technique. We have applied the framework to a LG electric oven and detected several critical bugs including one that makes the oven ignore user inputs due to the illegal state transition.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.132"}, {"primary_key": "4442187", "vector": [], "sparse_vector": [], "title": "ZoomIn: Discovering Failures by Detecting Wrong Assertions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Automatic testing, although useful, is still quite ineffective against faults that do not cause crashes or uncaught exceptions. In the majority of the cases automatic tests do not include oracles, and only in some cases they incorporate assertions that encode the observed behavior instead of the intended behavior, that is if the application under test produces a wrong result, the synthesized assertions will encode wrong expectations that match the actual behavior of the application. In this paper we present Zoom In, a technique that extends the fault-revealing capability of test case generation techniques from crash-only faults to faults that require non-trivial oracles to be detected. Zoom In exploits the knowledge encoded in the manual tests written by developers and the similarity between executions to automatically determine an extremely small set of suspicious assertions that are likely wrong and thus worth manual inspection. Early empirical results show that Zoom In has been able to detect 50% of the analyzed non-crashing faults in the Apache Commons Math library requiring the inspection of less than 1.5% of the assertions automatically generated by EvoSuite.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.29"}, {"primary_key": "4442188", "vector": [], "sparse_vector": [], "title": "AppCivist - A Service-Oriented Software Platform for Socially Sustainable Activism.", "authors": ["<PERSON><PERSON><PERSON>", "Val<PERSON>", "<PERSON>"], "summary": "The increased adoption of mobile devices and social networking is drastically changing the way people monitor and share knowledge about their environment. Here, information and communication technologies (ICT) offer significant new ways to support social activism in cities by providing residents with new digital tools to articulate projects and mobilize activities. However, the development of ICT for activism is still in its infancy, with activists using basic tools stitched together in an ad hoc manner for their needs. Still, Internet-based technologies and related software architectures feature various enablers for civic action beyond base social networking. To that end, this paper discusses the vision and initial details of AppCivist, a platform that builds on cross-domain research among social scientists and computer scientists to revisit service-oriented architecture and relevant services to further social activism. We discuss the ICT challenges inherent in this project and present our recent work to address them.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.185"}, {"primary_key": "4442189", "vector": [], "sparse_vector": [], "title": "Industry Practices and Event Logging: Assessment of a Critical Software Development Process.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Practitioners widely recognize the importance of event logging for a variety of tasks, such as accounting, system measurements and troubleshooting. Nevertheless, in spite of the importance of the tasks based on the logs collected under real workload conditions, event logging lacks systematic design and implementation practices. The implementation of the logging mechanism strongly relies on the human expertise. This paper proposes a measurement study of event logging practices in a critical industrial domain. We assess a software development process at Selex ES, a leading Finmeccanica company in electronic and information solutions for critical systems. Our study combines source code analysis, inspection of around 2.3 millions log entries, and direct feedback from the development team to gain process-wide insights ranging from programming practices, logging objectives and issues impacting log analysis. The findings of our study were extremely valuable to prioritize event logging reengineering tasks at Selex ES.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.145"}, {"primary_key": "4442192", "vector": [], "sparse_vector": [], "title": "Educating Software Engineering Managers - Revisited What Software Project Managers Need to Know Today.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In 2003, the original paper with this title was published as part of CSEET 2003. It focused on resolving communication issues between software project managers and developers and introduced a corporate strategy based means of evaluating software engineers. Now, more than a decade later, we could benefit from what we have learned in other fields about managing people involved in knowledge work and how to improve our success in software development. But are we? This paper is intended to present what Software Engineering students can be taught today that will help them to be successful as software project managers now and in the future. It is based on the premise that effective software project managers are not born but made through education.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.168"}, {"primary_key": "4442193", "vector": [], "sparse_vector": [], "title": "LACE2: Better Privacy-Preserving Data Sharing for Cross Project Defect Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Before a community can learn general principles, it must share individual experiences. Data sharing is the fundamental step of cross project defect prediction, i.e. the process of using data from one project to predict for defects in another. Prior work on secure data sharing allowed data owners to share their data on a single-party basis for defect prediction via data minimization and obfuscation. However the studied method did not consider that bigger data required the data owner to share more of their data. In this paper, we extend previous work with LACE2 which reduces the amount of data shared by using multi-party data sharing. Here data owners incrementally add data to a cache passed among them and contribute \"interesting\" data that are not similar to the current content of the cache. Also, before data owner i passes the cache to data owner j, privacy is preserved by applying obfuscation algorithms to hide project details. The experiments of this paper show that (a) LACE2 is comparatively less expensive than the single-party approach and (b) the multi-party approach of LACE2 yields higher privacy than the prior approach without damaging predictive efficacy (indeed, in some cases, LACE2 leads to better defect predictors).", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.92"}, {"primary_key": "4442196", "vector": [], "sparse_vector": [], "title": "Discovering Information Explaining API Types Using Text Classification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many software development tasks require developers to quickly learn a subset of an Application Programming Interface (API). API learning resources are crucial for helping developers learn an API, but the knowledge relevant to a particular topic of interest may easily be scattered across different documents, which makes finding the necessary information more challenging. This paper proposes an approach to discovering tutorial sections that explain a given API type. At the core of our approach, we classify fragmented tutorial sections using supervised text classification based on linguistic and structural features. Experiments conducted on five tutorials show that our approach is able to discover sections explaining an API type with precision between 0.69 and 0.87 (depending on the tutorial) when trained and tested on the same tutorial. When trained and tested across tutorials, we obtained a precision between 0.74 and 0.94 and lower recall values.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.97"}, {"primary_key": "4442200", "vector": [], "sparse_vector": [], "title": "Hercules: Reproducing Crashes in Real-World Application Binaries.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Binary analysis is a well-investigated area in software engineering and security. Given real-world program binaries, generating test inputs which cause the binaries to crash is crucial. Generation of crashing inputs has many applications including off-line analysis of software prior to deployment, or online analysis of software patches as they are inserted. In this work, we present a method for generating inputs which reach a given \"potentially crashing\" location. Such potentially crashing locations can be found by a separate static analysis (or by gleaning crash reports submitted by internal / external users) and serve as the input to our method. The test input generated by our method serves as a witness of the crash. Our method is particularly suited for binaries of programs which take in complex structured inputs. Experiments on real-life applications such as the Adobe Reader and the Windows Media Player demonstrate that our Hercules tool built on selective symbolic execution engine S2E can generate crashing inputs within few hours, where symbolic approaches (as embodied by S2E) or blackbox fuzzing approaches (as embodied by the commercial tool PeachFuzzer) failed.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.99"}, {"primary_key": "4442201", "vector": [], "sparse_vector": [], "title": "Poster: Tierless Programming in JavaScript.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Whereas \"responsive\" web applications already offered a more desktop-like experience, there is an increasing user demand for \"rich\" web applications (RIAs) that offer collaborative and even off-line functionality. Realizing these qualities requires distributing previously centralized application logic and state vertically from the server to a client tier (e.g., for desktop-like and off-line client functionality), and horizontally between instances of the same tier (e.g., for collaborative client functionality and for scaling of resource-starved services). Both bring about the essential complexity of distributing application assets and maintaining their consistency, along with the accidental complexity of reconciling a myriad of heterogenous tier-specific technology. Tierless programming languages enable developing web applications as a single artefact that is automatically split in tier-specific code - resulting in a development process akin to that of a desktop application. This relieves developers of distribution and consistency concerns, as well as the need to align different tier-specific technologies. However, programmers still have to adopt a new and perhaps esoteric language. We therefore advocate developing tierless programs in a general-purpose language instead. In this poster, we introduce our approach to tierless programming in JavaScript. We expand upon our previous work by identifying development challenges arising from this approach that could be resolved through tool support.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.270"}, {"primary_key": "4442204", "vector": [], "sparse_vector": [], "title": "TypeDevil: Dynamic Type Inconsistency Analysis for JavaScript.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dynamic languages, such as JavaScript, give programmers the freedom to ignore types, and enable them to write concise code in short time. Despite this freedom, many programs follow implicit type rules, for example, that a function has a particular signature or that a property has a particular type. Violations of such implicit type rules often correlate with problems in the program. This paper presents Type Devil, a mostly dynamic analysis that warns developers about inconsistent types. The key idea is to assign a set of observed types to each variable, property, and function, to merge types based in their structure, and to warn developers about variables, properties, and functions that have inconsistent types. To deal with the pervasiveness of polymorphic behavior in real-world JavaScript programs, we present a set of techniques to remove spurious warnings and to merge related warnings. Applying Type Devil to widely used benchmark suites and real-world web applications reveals 15 problematic type inconsistencies, including correctness problems, performance problems, and dangerous coding practices.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.51"}, {"primary_key": "4442210", "vector": [], "sparse_vector": [], "title": "The Green Lab: Experimentation in Software Energy Efficiency.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Software energy efficiency is a research topic where experimentation is widely adopted. Nevertheless, current studies and research approaches struggle to find generalizable findings that can be used to build a consistent knowledge base for energy-efficient software. To this end, we will discuss how to combine the traditional hypothesis-driven (top-down) approach with a bottom-up discovery approach. In this technical briefing, participants will learn the challenges that characterize the research in software energy efficiency. They will experience the complexity in this field and its implications for experimentation.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.297"}, {"primary_key": "4442213", "vector": [], "sparse_vector": [], "title": "Compositional Symbolic Execution with Memoized Replay.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Corina S<PERSON>", "Sarfraz Khurshid"], "summary": "Symbolic execution is a powerful, systematic analysis that has received much visibility in the last decade. Scalability however remains a major challenge for symbolic execution. Compositional analysis is a well-known general purpose methodology for increasing scalability. This paper introduces a new approach for compositional symbolic execution. Our key insight is that we can summarize each analyzed method as a memoization tree that captures the crucial elements of symbolic execution, and leverage these memoization trees to efficiently replay the symbolic execution of the corresponding methods with respect to their calling contexts. Memoization trees offer a natural way to compose in the presence of heap operations, which cannot be dealt with by previous work that uses logical formulas as summaries for compositional symbolic execution. Our approach also enables efficient target oriented symbolic execution for error detection or program coverage. Initial experimental evaluation based on a prototype implementation in Symbolic Path Finder shows that our approach can be up to an order of magnitude faster than traditional non-compositional symbolic execution.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.79"}, {"primary_key": "4442216", "vector": [], "sparse_vector": [], "title": "Capsule-Oriented Programming.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "\"Explicit concurrency should be abolished from all higher-level programming languages (i.e. everything except - perhaps- plain machine code.).\" <PERSON><PERSON><PERSON> [1] (paraphrased). A promising class of concurrency abstractions replaces explicit concurrency mechanisms with a single linguistic mechanism that combines state and control and uses asynchronous messages for communications, e.g. active objects or actors, but that doesn't remove the hurdle of understanding non-local control transfer. What if the programming model enabled programmers to simply do what they do best, that is, to describe a system in terms of its modular structure and write sequential code to implement the operations of those modules and handles details of concurrency? In a recently sponsored NSF project we are developing such a model that we call capsule-oriented programming and its realization in the Panini project. This model favors modularity over explicit concurrency, encourages concurrency correctness by construction, and exploits modular structure of programs to expose implicit concurrency.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.205"}, {"primary_key": "4442217", "vector": [], "sparse_vector": [], "title": "Inferring Behavioral Specifications from Large-scale Repositories by Leveraging Collective Intelligence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Despite their proven benefits, useful, comprehensible, and efficiently checkable specifications are not widely available. This is primarily because writing useful, non-trivial specifications from scratch is too hard, time consuming, and requires expertise that is not broadly available. Furthermore, the lack of specifications for widely-used libraries and frameworks, caused by the high cost of writing specifications, tends to have a snowball effect. Core libraries lack specifications, which makes specifying applications that use them expensive. To contain the skyrocketing development and maintenance costs of high assurance systems, this self-perpetuating cycle must be broken. The labor cost of specifying programs can be significantly decreased via advances in specification inference and synthesis, and this has been attempted several times, but with limited success. We believe that practical specification inference and synthesis is an idea whose time has come. Fundamental breakthroughs in this area can be achieved by leveraging the collective intelligence available in software artifacts from millions of open source projects. Fine-grained access to such data sets has been unprecedented, but is now easily available. We identify research directions and report our preliminary results on advances in specification inference that can be had by using such data sets to infer specifications.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.339"}, {"primary_key": "4442218", "vector": [], "sparse_vector": [], "title": "Developing and Evaluating Software Engineering Process Theories.", "authors": ["<PERSON>"], "summary": "A process theory is an explanation of how an entity changes and develops. While software engineering is fundamentally concerned with how software artifacts change and develop, little research explicitly builds and empirically evaluates software engineering process theories. This lack of theory obstructs scientific consensus by focusing the academic community on methods. Methods inevitably oversimplify and over-rationalize reality, obfuscating crucial phenomena including uncertainty, problem framing and illusory requirements. Better process theories are therefore needed to ground software engineering in empirical reality. However, poor understanding of process theory issues impedes research and publication. This paper therefore attempts to clarify the nature and types of process theories, explore their development and provide specific guidance for their empirically evaluation.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.25"}, {"primary_key": "4442219", "vector": [], "sparse_vector": [], "title": "4th SEMAT Workshop on General Theory of Software Engineering (GTSE 2015).", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "General theories explain the fundamental phenomena that constitute a research domain. They apply across a domain and often integrate many theories and concepts into a single cohesive view. While general theories are extremely important for education and research coordination, and common in many disciplines (e.g. sociology, criminology, electrical engineering, biology, physics), software engineering lacks a well-accepted general theory. The General Theory of Software Engineering workshop seeks to rectify this situation by promoting theory development in software engineering. The fourth workshop in this series, held in conjunction with the International Conference on Software Engineering, displayed a promising trend toward more theory development papers.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.316"}, {"primary_key": "4442221", "vector": [], "sparse_vector": [], "title": "Managing Emergent Ethical Concerns for Software Engineering in Society.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents an initial framework for managing emergent ethical concerns during software engineering in society projects. We argue that such emergent considerations can neither be framed as absolute rules about how to act in relation to fixed and measurable conditions. Nor can they be addressed by simply framing them as non-functional requirements to be satisficed. Instead, a continuous process is needed that accepts the 'messiness' of social life and social research, seeks to understand complexity (rather than seek clarity), demands collective (not just individual) responsibility and focuses on dialogue over solutions. The framework has been derived based on retrospective analysis of ethical considerations in four software engineering in society projects in three different domains.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.187"}, {"primary_key": "4442222", "vector": [], "sparse_vector": [], "title": "Contributor&apos;s Performance, Participation Intentions, Its Influencers and Project Performance.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Software project performance largely depends on the software development team. Studies have shown that interest and activity levels of contributors at any time significantly affect project success measures. This dissertation provides suggestions to enhance contributors' performance and participation intentions to help improve project performance. To do so, we mine historical data in software repositories from a two-pronged approach: 1) To assess contributors' performance to identify strengths and areas of improvement. 2) To measure the influence of factors on contributors' participation and performance, and provide suggestions that help advance contributor's engagement. The methodology used in this study leverage empirical techniques, both quantitative and qualitative, to conduct the analysis. We believe that the insights presented here will help contributors improve their performance. Also, we expect managers and business analysts to benefit from the suggestions to revise factors that negatively influence contributors' engagement and hence improve project performance.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.292"}, {"primary_key": "4442223", "vector": [], "sparse_vector": [], "title": "JRebel.Android: Runtime Class- and Resource Reloading for Android.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Developers writing Android applications suffer from a dreadful redeploy time every time they need to test changes to the source code. While runtime class reloading systems are widely used for the underlying programming language, Java, there is currently no support for reloading code on the Android platform. This paper presents a new tool, JRebel.Android that enables automatic runtime class- and resource reloading capabilities for Android. The target of this paper is the Android developer as well as the researcher for which dynamic updating capabilities on mobile devices can serve as a basic building block within areas such as runtime maintenance or self-adaptive systems. JRebel.Android is able to reload classes in much less than 1 second, saving more than 91% of the total redeploy time for small apps, more than 95% for medium size apps, and even more for larger apps.Accompanying video: https://www.youtube.com/watch?v=bpVwKcvNQEs", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.337"}, {"primary_key": "4442224", "vector": [], "sparse_vector": [], "title": "Specifying Event-Based Systems with a Counting Fluent Temporal Logic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nicolás D&apos;Ippolito", "<PERSON><PERSON><PERSON>"], "summary": "Fluent linear temporal logic is a formalism for specifying properties of event-based systems, based on propositions called fluents, defined in terms of activating and deactivating events. In this paper, we propose complementing the notion of fluent by the related concept of counting fluent. As opposed to the boolean nature of fluents, counting fluents are numerical values, that enumerate event occurrences, and allow us to specify naturally some properties of reactive systems. Although by extending fluent linear temporal logic with counting fluents we obtain an undecidable, strictly more expressive formalism, we develop a sound (but incomplete) model checking approach for the logic, that reduces to traditional temporal logic model checking, and allows us to automatically analyse properties involving counting fluents, on finite event-based systems. Our experiments, based on relevant models taken from the literature, show that: (i) counting fluent temporal logic is better suited than traditional temporal logic for expressing properties in which the number of occurrences of certain events is relevant, and (ii) our model checking approach on counting fluent specifications has an efficiency that is comparable to that of model checking equivalent fluent temporal logic specifications, while our approach scales better.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.86"}, {"primary_key": "4442226", "vector": [], "sparse_vector": [], "title": "Presence-Condition Simplification in Highly Configurable Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "For the analysis of highly configurable systems, analysis approaches need to take the inherent variability of these systems into account. The notion of presence conditions is central to such approaches. A presence condition specifies a subset of system configurations in which a certain artifact or a concern of interest is present (e.g., a defect associated with this subset). In this paper, we introduce and analyze the problem of presence-condition simplification. A key observation is that presence conditions often contain redundant information, which can be safely removed in the interest of simplicity and efficiency. We present a formalization of the problem, discuss application scenarios, compare different algorithms for solving the problem, and empirically evaluate the algorithms by means of a set of substantial case studies.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.39"}, {"primary_key": "4442233", "vector": [], "sparse_vector": [], "title": "Fast and Precise Statistical Code Completion.", "authors": ["<PERSON>"], "summary": "The main problem we try to solve is API code completion which is both precise and works in real-time. We describe an efficient implementation of an N-gram language model combined with several smoothing methods and a completion algorithm based on beam search. We show that our system is both fast and precise using a thorough experimental evaluation. With optimal parameters we are able to find completions in milliseconds and the desired completion is in the top 3 suggestions in 89% of the time.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.240"}, {"primary_key": "4442239", "vector": [], "sparse_vector": [], "title": "5th International Workshop on Product LinE Approaches in Software Engineering PLE for a Sustainable Society (PLEASE 2015).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper summarizes the motivation, objectives, and format of the 5th International Workshop on Product LinE Approaches in Software Engineering (PLEASE15). The main goal of the PLEASE workshop series is to encourage and promote the adoption of Software Product Line Engineering. This year's edition focuses on the link between software product line engineering (SPLE) and new challenges posed by emerging societal trends. Towards this end, we invited reports on (1) opportunities posed by societal challenges for SPLE research and practice and (2) concrete solutions exemplifying application of SPLE techniques to societal challenges.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.319"}, {"primary_key": "4442242", "vector": [], "sparse_vector": [], "title": "Teaching Software Architecture to Undergraduate Students: An Experience Report.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Software architecture lies at the heart of system thinking skills for software. Teaching software architecture requires contending with the problem of how to make the learning realistic -- most systems which students can learn quickly are too simple for them to express architectural issues. We address here the ten years' history of teaching an undergraduate software architecture course, as a part of a bachelor's program in software engineering. Included are descriptions of what we perceive the realistic goals to be, of teaching software architecture at this level. We go on to analyze the successes and issues of various approaches we have taken over the years. We finish with recommendations for others who teach this same subject, either as a standalone undergraduate course or integrated into a software engineering course.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.177"}, {"primary_key": "4442245", "vector": [], "sparse_vector": [], "title": "Analysis of Android Inter-App Security Vulnerabilities Using COVERT.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The state-of-the-art in securing mobile software systems are substantially intended to detect and mitigate vulnerabilities in a single app, but fail to identify vulnerabilities that arise due to the interaction of multiple apps, such as collusion attacks and privilege escalation chaining, shown to be quite common in the apps on the market. This paper demonstrates COVERT, a novel approach and accompanying tool-suite that relies on a hybrid static analysis and lightweight formal analysis technique to enable compositional security assessment of complex software. Through static analysis of Android application packages, it extracts relevant security specifications in an analyzable formal specification language, and checks them as a whole for inter-app vulnerabilities. To our knowledge, COVERT is the first formally-precise analysis tool for automated compositional analysis of Android apps. Our study of hundreds of Android apps revealed dozens of inter-app vulnerabilities, many of which were previously unknown.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.233"}, {"primary_key": "4442246", "vector": [], "sparse_vector": [], "title": "Tricorder: Building a Program Analysis Ecosystem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Ciera Jaspan", "<PERSON>", "<PERSON>"], "summary": "Static analysis tools help developers find bugs, improve code readability, and ensure consistent style across a project. However, these tools can be difficult to smoothly integrate with each other and into the developer workflow, particularly when scaling to large codebases. We present Tricorder, a program analysis platform aimed at building a data-driven ecosystem around program analysis. We present a set of guiding principles for our program analysis tools and a scalable architecture for an analysis platform implementing these principles. We include an empirical, in-situ evaluation of the tool as it is used by developers across Google that shows the usefulness and impact of the platform.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.76"}, {"primary_key": "4442247", "vector": [], "sparse_vector": [], "title": "An Information Retrieval Approach for Regression Test Prioritization Based on Program Changes.", "authors": ["Ripon K. Saha", "<PERSON><PERSON>", "Sarfraz Khurshid", "<PERSON><PERSON>"], "summary": "Regression testing is widely used in practice for validating program changes. However, running large regression suites can be costly. Researchers have developed several techniques for prioritizing tests such that the higher-priority tests have a higher likelihood of finding bugs. A vast majority of these techniques are based on dynamic analysis, which can be precise but can also have significant overhead (e.g., for program instrumentation and test-coverage collection). We introduce a new approach, REPiR, to address the problem of regression test prioritization by reducing it to a standard Information Retrieval problem such that the differences between two program versions form the query and the tests constitute the document collection. REPiR does not require any dynamic profiling or static program analysis. As an enabling technology we leverage the open-source IR toolkit Indri. An empirical evaluation using eight open-source Java projects shows that REPiR is computationally efficient and performs better than existing (dynamic or static) techniques for the majority of subject systems.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.47"}, {"primary_key": "4442250", "vector": [], "sparse_vector": [], "title": "Profiling Kernels Behavior to Improve CPU / GPU Interactions.", "authors": ["<PERSON><PERSON>"], "summary": "Most modern computer and mobile devices have a graphical processing unit (GPU) available for any general purpose computation. GPU supports a programming model that is radically different from traditional sequential programming. As such, programming GPU is known to be hard and error prone, despite the large number of available APIs and dedicated programming languages. In this paper we describe a profiling technique that reports on the interaction between a CPU and GPUs. The resulting execution profile may then reveal anomalies and suboptimal situations, in particular due to an improper memory configuration. Our profiler has been effective at identifying suboptimal memory allocation usage in one image processing application.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.239"}, {"primary_key": "4442251", "vector": [], "sparse_vector": [], "title": "Are Students Representatives of Professionals in Software Engineering Experiments?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Background: Most of the experiments in software engineering (SE) employ students as subjects. This raises concerns about the realism of the results acquired through students and adaptability of the results to software industry. Aim: We compare students and professionals to understand how well students represent professionals as experimental subjects in SE research. Method: The comparison was made in the context of two test-driven development experiments conducted with students in an academic setting and with professionals in a software organization. We measured the code quality of several tasks implemented by both subject groups and checked whether students and professionals perform similarly in terms of code quality metrics. Results: Except for minor differences, neither of the subject groups is better than the other. Professionals produce larger, yet less complex, methods when they use their traditional development approach, whereas both subject groups perform similarly when they apply a new approach for the first time. Conclusion: Given a carefully scoped experiment on a development approach that is new to both students and professionals, similar performances are observed. Further investigation is necessary to analyze the effects of subject demographics and level of experience on the results of SE experiments.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.82"}, {"primary_key": "4442252", "vector": [], "sparse_vector": [], "title": "Reactive Programming: A Walkthrough.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Over the last few years, Reactive Programming has emerged as the trend to support the development of reactive software through dedicated programming abstractions. Reactive Programming has been increasingly investigated in the programming languages community and it is now gaining the interest of practitioners. Conversely, it has received so far less attention from the software engineering community. This technical briefing bridges this gap through an accurate overview of Reactive Programming, discussing the available frameworks and outlining open research challenges with an emphasis on cross-field research opportunities.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.303"}, {"primary_key": "4442254", "vector": [], "sparse_vector": [], "title": "Source Code Curation on StackOverflow: The Vesperin System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The past few years have witnessed the rise of software question and answer sites like StackOverflow, where developers can pose detailed coding questions and receive quality answers. Developers using these sites engage in a complex code foraging process of understanding and adapting the code snippets they encounter. We introduce the notion of source code curation to cover the act of discovering some source code of interest, cleaning and transforming (refining) it, and then presenting it in a meaningful and organized way. In this paper, we present Vesperin, a source code curation system geared towards curating Java code examples on StackOverflow.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.217"}, {"primary_key": "4442255", "vector": [], "sparse_vector": [], "title": "Code Repurposing as an Assessment Tool.", "authors": ["<PERSON>"], "summary": "Code repurposing is often used for system development and to learn both APIs and techniques. Repurposing code typically requires that you understand the code first. This makes it an excellent candidate as an assessment tool in computer science and software engineering education. This technique might have a special application in combatting plagiarism. This paper discusses experiences using code repurposing as an assessment tool in different courses and with different sections.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.158"}, {"primary_key": "4442258", "vector": [], "sparse_vector": [], "title": "Poster: Reasoning Based on Imperfect Context Data in Adaptive Security.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Enabling software systems to adjust their protection in continuously changing environments with imperfect context information is a grand challenging problem. The issue of uncertain reasoning based on imperfect information has been overlooked in traditional logic programming with classical negation when applied to dynamic systems. This paper sketches a non-monotonic approach based on Answer Set Programming to reason with imperfect context data in adaptive security where there is little or no knowledge about certainty of the actions and events.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.272"}, {"primary_key": "4442259", "vector": [], "sparse_vector": [], "title": "Dementia and Social Sustainability: Challenges for Software Engineering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Dementia is a serious threat to social sustainability. As life expectancy increases, more people are developing dementia. At the same time, demographic change is reducing the economically active part of the population. Care of people with dementia imposes great emotional and financial strain on sufferers, their families and society at large. In response, significant research resources are being focused on dementia. One research thread is focused on using computer technology to monitor people in at-risk groups to improve rates of early diagnosis. In this paper we provide an overview of dementia monitoring research and identify a set of scientific challenges for the engineering of dementia-monitoring software, with implications for other mental health self-management systems.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.188"}, {"primary_key": "4442265", "vector": [], "sparse_vector": [], "title": "Design and Evaluation of a Customizable Multi-Domain Reference Architecture on Top of Product Lines of Self-Driving Heavy Vehicles - An Industrial Case Study.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Self-driving vehicles for commercial use cases like logistics or overcast mines increase their owners' economic competitiveness. Volvo maintains, evolves, and distributes a vehicle control product line for different brands like Volvo Trucks, Renault, and Mack in more than 190 markets world-wide. From the different application domains of their customers originates the need for a multi-domain reference architecture concerned with transport mission planning, execution, and tracking on top of the vehicle control product line. This industrial case study is the first of its kind reporting about the systematic process to design such a reference architecture involving all relevant external and internal stakeholders, development documents, low level artifacts, and literature. Quantitative and qualitative metrics were applied to evaluate non-functional requirements on the reference architecture level before a concrete variant was evaluated using a Volvo FMX truck in an exemplary construction site setting.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.147"}, {"primary_key": "4442266", "vector": [], "sparse_vector": [], "title": "VERMEER: A Tool for Tracing and Explaining Faulty C Programs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present VERMEER, a new automated debugging tool for C. VERMEER combines two functionalities: (1) a dynamic tracer that produces a linearized trace from a faulty C program and a given test input; and (2) a static analyzer that explains why the trace fails. The tool works in phases that simplify the input program to a linear trace, which is then analyzed using an automated theorem prover to produce the explanation. The output of each phase is a valid C program. VERMEER is able to produce useful explanations of non trivial traces for real C programs within a few seconds. The tool demo can be found at http://youtu.be/E5lKHNJVerU.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.236"}, {"primary_key": "4442267", "vector": [], "sparse_vector": [], "title": "Active and Inductive Learning in Software Engineering Education.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "If software engineering education is done in a traditional lecture-oriented style students have no other choice than believing that the solutions they are told actually work for a problem that they never encountered themselves. In order to overcome this problem, this paper describes an approach which allows students to better understand why software engineering and several of its core methods and techniques are needed, thus preparing them better for their professional life. This approach builds on active and inductive learning. Exercises that make students actively discover relevant software engineering issues are described in detail together with their pedagogical underpinning.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.174"}, {"primary_key": "4442271", "vector": [], "sparse_vector": [], "title": "Poster: Automatically Fixing Real-World JavaScript Performance Bugs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Programs often suffer from poor performance that can be fixed by relatively simple changes. Currently, developers either manually identify and fix such performance problems, or they rely on compilers to optimize their code. Unfortunately, manually fixing performance bugs is non-trivial, and compilers are limited to a predefined set of optimizations. This paper presents an approach for automatically finding and fixing performance bugs in JavaScript programs. To focus our work on relevant problems, we study 37 real-world performance bug fixes from eleven popular JavaScript projects and identify several recurring fix patterns. Based on the results of the study, we present a static analysis that identifies occurrences of common fix patterns and a fix generation technique that proposes to transform a given program into a more efficient program. Applying the fix generation technique to three libraries with known performance bugs yields fixes that are equal or equivalent to those proposed by the developers, and that lead to speedups between 10% and 25%.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.260"}, {"primary_key": "4442275", "vector": [], "sparse_vector": [], "title": "A Comprehensive Framework for the Development of Dynamic Smart Spaces.", "authors": ["<PERSON><PERSON>"], "summary": "The conception of reliable smart spaces requires a suitable and comprehensive framework for their design, implementation, testing, and deployment. Numerous solutions have been proposed to solve different aspects related to smart spaces, but we still lack a concrete framework that provides solutions suitable for the whole development life-cycle. This work aims to fill the gap and proposes a framework that provides: (i) well-defined abstractions for designing smart spaces, (ii) a middleware infrastructure to implement them and plug physical objects, (iii) a semantic layer to support heterogeneous elements, and (iv) plugs to integrate external simulators and be able to always work on \"complete'' systems in the different phases of the development.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.294"}, {"primary_key": "4442276", "vector": [], "sparse_vector": [], "title": "How and When to Transfer Software Engineering Research via Extensions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "It is often reported that there is a large gap between software engineering research and practice, with little transfer from research to practice. While this is true in general, one transfer technique is increasingly breaking down this barrier: extensions to integrated development environments (IDEs). With the proliferation of app stores for IDEs and increasing transfer effort from researchers several research-based extensions have seen significant adoption. In this talk we'll discuss our experience transferring code search research, which currently is in the top 5% of Visual Studio extensions with over 13,000 downloads, as well as other research techniques transferred via extensions such as NCrunch, FindBugs, Code Recommenders, Mylyn, and Instasearch. We'll use the lessons learned from our transfer experience to provide case study evidence as to best practices for successful transfer, supplementing it with the quantitative evidence offered by app store and usage data across the broader set of extensions. The goal of this 30 minute talk is to provide researchers with a realistic view on which research techniques can be transferred to practice as well as concrete steps to execute such a transfer.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.152"}, {"primary_key": "4442279", "vector": [], "sparse_vector": [], "title": "Concurrent Software Engineering and Robotics Education.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a new, multidisciplinary robotics programming course, reports initial results, and describes subsequent improvements. With equal emphasis on software engineering and robotics, the course teaches students how software engineering applies to robotics. Students learn independently and interactively and gain hands-on experience by implementing robotics algorithms on a real robot. To understand the effects of the course, we conducted an exit and an 8-month survey and measured software quality of the students' solutions. The analysis shows that the hands-on experience helped everyone learn and retain robotics well, but the students' knowledge gain in software engineering depended on their prior programming knowledge. Based on these findings, we propose improvements to the course. Lastly, we reflect our experience on andragogy, minimalism, and interactive learning.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.169"}, {"primary_key": "4442281", "vector": [], "sparse_vector": [], "title": "Views on Internal and External Validity in Empirical Software Engineering.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Empirical methods have grown common in software engineering, but there is no consensus on how to apply them properly. Is practical relevance key? Do internally valid studies have any value? Should we replicate more to address the tradeoff between internal and external validity? We asked the community how empirical research should take place in software engineering, with a focus on the tradeoff between internal and external validity and replication, complemented with a literature review about the status of empirical research in software engineering. We found that the opinions differ considerably, and that there is no consensus in the community when to focus on internal or external validity and how to conduct and review replications.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.24"}, {"primary_key": "4442283", "vector": [], "sparse_vector": [], "title": "Software Engineering in Ferrari F1.", "authors": ["<PERSON>"], "summary": "Summary form only given. The software and hardware development in Ferrari F1 is characterized by a very short cycle time. Typically during the in-season development, the fixes and new developments need to be addressed in few days, in order to be ready for the following race. At the same time the hardware, like new electronic control units or new devices need to be developed from one year to the other. In this scenario the validation procedures are very critical, because of the need to achieve the same results in a shorter time.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.22"}, {"primary_key": "4442286", "vector": [], "sparse_vector": [], "title": "Experiences in Developing and Delivering a Programme of Part-Time Education in Software and Systems Security.", "authors": ["<PERSON>", "<PERSON>", "Cas Cremers", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We report upon our experiences in developing and delivering a programme of part-time education in Software and Systems Security at the University of Oxford. The MSc in Software and Systems Security is delivered as part of the Software Engineering Programme at Oxford - a collection of one-week intensive courses aimed at individuals who are responsible for the procurement, development, deployment and maintenance of large-scale software-based systems. We expect that our experiences will be useful to those considering a similar journey.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.176"}, {"primary_key": "4442289", "vector": [], "sparse_vector": [], "title": "Build It Yourself! Homegrown Tools in a Large Software Company.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Developers sometimes take the initiative to build toolsto solve problems they face. What motivates developers to buildthese tools? What is the value for a company? Are the tools builtuseful for anyone besides their creator? We conducted a qualitativestudy of tool building, adoption, and impact within Microsoft. Thispaper presents our findings on the extrinsic and intrinsic factorslinked to toolbuilding, the value of building tools, and the factorsassociated with tool spread. We find that the majority of developersbuild tools. While most tools never spread beyond their creator'steam, most have more than one user, and many have more than onecollaborator. Organizational cultures that are receptive towardstoolbuilding produce more tools, and more collaboration on tools.When nurtured and spread, homegrown tools have the potential tocreate significant impact on organizations.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.56"}, {"primary_key": "4442292", "vector": [], "sparse_vector": [], "title": "On Architectural Diversity of Dynamic Adaptive Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce a novel concept of ``architecture diversity'' for adaptive systems and posit that increased diversity has an inverse correlation with adaptation costs. We propose an index to quantify diversity and a static method to estimate the adaptation cost, and conduct an initial experiment on an exemplar cloud-based system which reveals the posited correlation.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.201"}, {"primary_key": "4442293", "vector": [], "sparse_vector": [], "title": "Collaborative and Cooperative-Learning in Software Engineering Courses.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Collaborative learning is a key component of software engineering (SE) courses in most undergraduate computing curricula. Thus these courses include fairly intensive team projects, the intent being to ensure that not only do students develop an understanding of key software engineering concepts and practices, but also develop the skills needed to work effectively in large design and development teams. But there is a definite risk in collaborative learning in that there is a potential that individual learning gets lost in the focus on the team's success in completing the project (s). While the team's success is indeed the primary goal of an industrial SE team, ensuring individual learning is obviously an essential goal of SE courses. We have developed a novel approach that exploits the affordances of mobile and web technologies to help ensure that individual students in teams in SE courses develop a thorough understanding of the relevant concepts and practices while working on team projects, indeed, that the team contributes in an essential manner to the learning of each member of the team. We describe the learning theory underlying our approach, provide some details concerning the prototype implementation of a tool based on the approach, and describe how we are using it in an SE course in our program.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.164"}, {"primary_key": "4442295", "vector": [], "sparse_vector": [], "title": "&quot;Should We Move to Stack Overflow?&quot; Measuring the Utility of Social Media for Developer Support.", "authors": ["<PERSON>"], "summary": "Stack Overflow is an enormously popular question-and-answer web site intended for software developers to help each other with programming issues. Some software projects aimed at developers (for example, application programming interfaces, application engines, cloud services, development frameworks, and the like) are closing their self-supported developer discussion forums and mailing lists and instead directing developers to use special-purpose tags on Stack Overflow. The goals of this paper are to document the main reasons given for moving developer support to Stack Overflow, and then to collect and analyze data from a group of software projects that have done this, in order to show whether the expected quality of support was actually achieved. The analysis shows that for all four software projects in this study, two of the desired quality indicators, developer participation and response time, did show improvements on Stack Overflow as compared to mailing lists and forums. However, we also found several projects that moved back from Stack Overflow, despite achieving these desired improvements. The results of this study are applicable to a wide variety of software projects that provide developer support using social media.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.150"}, {"primary_key": "4442298", "vector": [], "sparse_vector": [], "title": "A Declarative Foundation for Comprehensive History Querying.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Researchers in the field of Mining Software Repositories perform studies about the evolution of software projects. To this end, they use the version control system storing the changes made to a single software project. Such studies are concerned with the source code characteristics in one particular revision, the commit data for that revision, how the code evolves over time and what concrete, fine-grained changes were applied to the source code between two revisions. Although tools exist to analyse an individual concern, scripts and manual work is required to combine these tools to perform a single experiment. We present a general-purpose history querying tool named QwalKeko that enables expressing these concerns in a single uniform language, and having them detected in a git repository. We have validated our work by means of replication studies as well as through MSR studies of our own.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.289"}, {"primary_key": "4442299", "vector": [], "sparse_vector": [], "title": "Poster: Static Analysis of Concurrent Higher-Order Programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Few static analyses support concurrent higher-order programs. Tools for detecting concurrency bugs such as deadlocks and race conditions are nonetheless invaluable to developers. Concurrency can be implemented using a variety of models, each supported by different synchronization primitives. Using this poster, we present an approach for analyzing concurrent higher-order programs in a precise manner through abstract interpretation. We instantiate the approach for two static analyses that are capable of detecting deadlocks and race conditions in programs that rely either on compare-and-swap (cas), or on conventional locks for synchronization. We observe few false positives and false negatives on a corpus of small concurrent programs, with better results for the lock-based analyses. We also observe that these programs lead to a smaller state space to be explored by the analyses. Our results show that the choice of synchronization primitives supported by an abstract interpreter has an important impact on the complexity of the static analyses performed with this abstract interpreter.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.265"}, {"primary_key": "4442305", "vector": [], "sparse_vector": [], "title": "Combining Symbolic Execution and Model Checking for Data Flow Testing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data flow testing (DFT) focuses on the flow of data through a program. Despite its higher fault-detection ability over other structural testing techniques, practical DFT remains a significant challenge. This paper tackles this challenge by introducing a hybrid DFT framework: (1) The core of our framework is based on dynamic symbolic execution (DSE), enhanced with a novel guided path search to improve testing performance, and (2) we systematically cast the DFT problem as reach ability checking in software model checking to complement our DSE-based approach, yielding a practical hybrid DFT technique that combines the two approaches' respective strengths. Evaluated on both open source and industrial programs, our DSE-based approach improves DFT performance by 60~80% in terms of testing time compared with state-of-the-art search strategies, while our combined technique further reduces 40% testing time and improves data-flow coverage by 20% by eliminating infeasible test objectives. This combined approach also enables the cross-checking of each component for reliable and robust testing results.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.81"}, {"primary_key": "4442306", "vector": [], "sparse_vector": [], "title": "10th International Workshop on Automation of Software Test (AST 2015).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper is a report on The 10th IEEE/ACMInternational Workshop on Automation of Software Test (AST2015) at the 37th International Conference on Software Engineering(ICSE 2015). It sets a special theme on testing oracles.Keynote speeches and charette discussions are organized aroundthis special theme. 16 full research papers and 2 keynotes willbe presented in the two-day workshop. The report will give thebackground of the workshop and the selection of the specialtheme, and report on the organization of the workshop. Theprovisional program will be presented with a list of the sessionsand papers to be presented at the workshop.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.307"}, {"primary_key": "4442308", "vector": [], "sparse_vector": [], "title": "Poster: Interactive and Collaborative Source Code Annotation.", "authors": ["<PERSON><PERSON>"], "summary": "Software documentation plays an important role in sharing the knowledge behind source code between distributed programmers. Good documentation makes source code easier to understand; on the other hand, developers have to constantly update the documentation whenever the source code changes. Developers will benefit from an automated tool that simplifies keeping documentation up-to-date and facilitates collaborative editing. In this paper, we explore the concept of collaborative code annotation by combining the idea from crowdsourcing. We introduce Cumiki, a web-based collaborative annotation tool that makes it easier for crowds of developers to collaboratively create the up-to-date documentation. This paper describes the user interface, the mechanism, and its implementation, and discusses the possible usage scenarios.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.254"}, {"primary_key": "4442309", "vector": [], "sparse_vector": [], "title": "Novice Code Understanding Strategies during a Software Maintenance Assignment.", "authors": ["<PERSON>"], "summary": "Existing efforts on teaching software maintenance have focussed on constructing adequate codebases that students with limited knowledge could maintain, with little focus on the learning outcomes of such exercises and of the approaches that students employ while performing maintenance. An analysis of the code understanding strategies employed by novice students as they perform software maintenance exercises is fundamental for the effective teaching of software maintenance. In this paper, we analyze the strategies employed by second year students in a maintenance exercise over a large codebase. We analyze student reflections on their code understanding, maintenance process and the use of tools. We show that students are generally capable of working with large codebases. Our study also finds that the majority of students follow a systematic approach to code understanding, but that their approach can be significantly improved through the use of tools and a better understanding of reverse engineering approaches.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.341"}, {"primary_key": "4442312", "vector": [], "sparse_vector": [], "title": "relifix: Automated Repair of Software Regressions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Regression occurs when code changes introduce failures in previously passing test cases. As software evolves, regressions may be introduced. Fixing regression errors manually is time-consuming and error-prone. We propose an approach of automated repair of software regressions, called relifix, that considers the regression repair problem as a problem of reconciling problematic changes. Specifically, we derive a set of code transformations obtained from our manual inspection of 73 real software regressions; this set of code transformations uses syntactical information from changed statements. Regression repair is then accomplished via a search over the code transformation operators - which operator to apply, and where. Our evaluation compares the repairability of relifix with GenProg on 35 real regression errors. relifix repairs 23 bugs, while GenProg only fixes five bugs. We also measure the likelihood of both approaches in introducing new regressions given a reduced test suite. Our experimental results shows that our approach is less likely to introduce new regressions than GenProg.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.65"}, {"primary_key": "4442313", "vector": [], "sparse_vector": [], "title": "Online Defect Prediction for Imbalanced Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Many defect prediction techniques are proposed to improve software reliability. Change classification predicts defects at the change level, where a change is the modifications to one file in a commit. In this paper, we conduct the first study of applying change classification in practice. We identify two issues in the prediction process, both of which contribute to the low prediction performance. First, the data are imbalanced -- there are much fewer buggy changes than clean changes. Second, the commonly used cross-validation approach is inappropriate for evaluating the performance of change classification. To address these challenges, we apply and adapt online change classification, resampling, and updatable classification techniques to improve the classification performance. We perform the improved change classification techniques on one proprietary and six open source projects. Our results show that these techniques improve the precision of change classification by 12.2-89.5% or 6.4 -- 34.8 percentage points (pp.) on the seven projects. In addition, we integrate change classification in the development process of the proprietary project. We have learned the following lessons: 1) new solutions are needed to convince developers to use and believe prediction results, and prediction results need to be actionable, 2) new and improved classification algorithms are needed to explain the prediction results, and insensible and unactionable explanations need to be filtered or refined, and 3) new techniques are needed to improve the relatively low precision.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.139"}, {"primary_key": "4442314", "vector": [], "sparse_vector": [], "title": "TesMa and CATG: Automated Test Generation Tools for Models of Enterprise Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present CATG, an open-source concolic test generation tool for Java and its integration with TesMa, a model-based testing tool which automatically generates test cases from formal design documents. TesMa takes as input a set of design documents of an application under test. The design documents are provided in the form of database table definitions, process-flow diagrams, and screen definitions. From these design documents, TesMa creates Java programs for the feasible execution scenarios of the application. CATG performs concolic testing on these Java programs to generate suitable databases and test inputs required to test the application under test. A demo video of the tool is available at https://www.youtube.com/watch?v=9lEvPwR7g-Q.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.231"}, {"primary_key": "4442315", "vector": [], "sparse_vector": [], "title": "The Impact of Mislabelling on the Performance and Interpretation of Defect Prediction Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The reliability of a prediction model depends on the quality of the data from which it was trained. Therefore, defect prediction models may be unreliable if they are trained using noisy data. Recent research suggests that randomly-injected noise that changes the classification (label) of software modules from defective to clean (and vice versa) can impact the performance of defect models. Yet, in reality, incorrectly labelled (i.e., mislabelled) issue reports are likely non-random. In this paper, we study whether mislabelling is random, and the impact that realistic mislabelling has on the performance and interpretation of defect models. Through a case study of 3,931 manually-curated issue reports from the Apache Jackrabbit and Lucene systems, we find that: (1) issue report mislabelling is not random; (2) precision is rarely impacted by mislabelled issue reports, suggesting that practitioners can rely on the accuracy of modules labelled as defective by models that are trained using noisy data; (3) however, models trained on noisy data typically achieve 56%-68% of the recall of models trained on clean data; and (4) only the metrics in top influence rank of our defect models are robust to the noise introduced by mislabelling, suggesting that the less influential metrics of models that are trained on noisy data should not be interpreted or used to make decisions.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.93"}, {"primary_key": "4442316", "vector": [], "sparse_vector": [], "title": "RECONTEST: Effective Regression Testing of Concurrent Programs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Concurrent programs proliferate as multi-core technologies advance. The regression testing of concurrent programs often requires running a failing test for weeks before catching a faulty interleaving, due to the myriad of possible interleavings of memory accesses arising from concurrent program executions. As a result, the conventional approach that selects a sub-set of test cases for regression testing without considering interleavings is insufficient. In this paper we present RECONTEST to address the problem by selecting the new interleavings that arise due to code changes. These interleavings must be explored in order to uncover regression bugs. RECONTEST efficiently selects new interleavings by first identifying shared memory accesses that are affected by the changes, and then exploring only those problematic interleavings that contain at least one of these accesses. We have implemented RECONTEST as an automated tool and evaluated it using 13 real-world concurrent program subjects. Our results show that RECONTEST can significantly reduce the regression testing cost without missing any faulty interleavings induced by code changes.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.45"}, {"primary_key": "4442317", "vector": [], "sparse_vector": [], "title": "Approximating Attack Surfaces with Stack Traces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Security testing and reviewing efforts are a necessity for software projects, but are time-consuming and expensive to apply. Identifying vulnerable code supports decision-making during all phases of software development. An approach for identifying vulnerable code is to identify its attack surface, the sum of all paths for untrusted data into and out of a system. Identifying the code that lies on the attack surface requires expertise and significant manual effort. This paper proposes an automated technique to empirically approximate attack surfaces through the analysis of stack traces. We hypothesize that stack traces from user-initiated crashes have several desirable attributes for measuring attack surfaces. The goal of this research is to aid software engineers in prioritizing security efforts by approximating the attack surface of a system via stack trace analysis. In a trial on Windows 8, the attack surface approximation selected 48.4% of the binaries and contained 94.6% of known vulnerabilities. Compared with vulnerability prediction models (VPMs) run on the entire codebase, VPMs run on the attack surface approximation improved recall from .07 to .1 for binaries and from .02 to .05 for source files. Precision remained at .5 for binaries, while improving from .5 to .69 for source files.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.148"}, {"primary_key": "4442318", "vector": [], "sparse_vector": [], "title": "Towards Generation of Software Development Tasks.", "authors": ["<PERSON><PERSON>"], "summary": "The presence of well defined fine-grained sub-tasks is important to the development process: having a fine-grained task context has been shown to allow developers to more efficiently resume work. However, determining how to break a high level task down into sub-tasks is not always straightforward. Sometimes developers lack experience, and at other times, the task definition is not clear enough to afford confident decomposition. In my research I intend to show that by using syntactic mining of past task descriptions and their decomposition, I can provide automatically derived sub-task suggestions to afford more confident task decomposition by developers.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.291"}, {"primary_key": "4442319", "vector": [], "sparse_vector": [], "title": "2nd International Workshop on Rapid Continuous Software Engineering (RCoSE 2015).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Continuous software engineering refers to the organizational capability to develop, release and learn from software in very short rapid cycles, typically hours, days or a very small numbers of weeks. This requires not only agile processes in teams but in the complete research and development organization. Additionally, the technology used in the different development phases, like requirements engineering and system integration, must support the quick development cycles. Finally, automatic live experimentation for different system alternatives enables fast gathering of required data for decision making. The workshop, the second in the series after the first one at ICSE 2014, aims to bring the research communities of the aforementioned areas together to exchange challenges, ideas, and solutions to bring software engineering a step further to being a holistic continuous process. The workshop program is based on eight papers selected in the peer-review process and supplemented by interaction and discussions at the workshop. The topics range from agile methods, continuous software engineering practices to specific techniques, like visualization and testing.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.343"}, {"primary_key": "4442324", "vector": [], "sparse_vector": [], "title": "TaskNav: Task-Based Navigation of Software Documentation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "To help developers navigate documentation, we introduce Task Nav, a tool that automatically discovers and indexes task descriptions in software documentation. With Task Nav, we conceptualize tasks as specific programming actions that have been described in the documentation. Task Nav presents these extracted task descriptions along with concepts, code elements, and section headers in an auto-complete search interface. Our preliminary evaluation indicates that search results identified through extracted task descriptions are more helpful to developers than those found through other means, and that they help bridge the gap between documentation structure and the information needs of software developers. Video: https://www.youtube.com/watch?v=opnGYmMGnqY.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.214"}, {"primary_key": "4442327", "vector": [], "sparse_vector": [], "title": "Ariadne: Topology Aware Adaptive Security for Cyber-Physical Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents Ariadne, a tool for engineering topology aware adaptive security for cyber-physical systems. It allows security software engineers to model security requirements together with the topology of the operational environment. This model is then used at runtime to perform speculative threat analysis to reason about the consequences that topological changes arising from the movement of agents and assets can have on the satisfaction of security requirements. Our tool also identifies an adaptation strategy that applies security controls when necessary to prevent potential security requirements violations.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.234"}, {"primary_key": "4442328", "vector": [], "sparse_vector": [], "title": "When and Why Your Code Starts to Smell Bad.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In past and recent years, the issues related to managing technical debt received significant attention by researchers from both industry and academia. There are several factors that contribute to technical debt. One of these is represented by code bad smells, i.e., Symptoms of poor design and implementation choices. While the repercussions of smells on code quality have been empirically assessed, there is still only anecdotal evidence on when and why bad smells are introduced. To fill this gap, we conducted a large empirical study over the change history of 200 open source projects from different software ecosystems and investigated when bad smells are introduced by developers, and the circumstances and reasons behind their introduction. Our study required the development of a strategy to identify smell-introducing commits, the mining of over 0.5M commits, and the manual analysis of 9,164 of them (i.e., Those identified as smell-introducing). Our findings mostly contradict common wisdom stating that smells are being introduced during evolutionary tasks. In the light of our results, we also call for the need to develop a new generation of recommendation systems aimed at properly planning smell refactoring activities.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.59"}, {"primary_key": "4442330", "vector": [], "sparse_vector": [], "title": "4th International Workshop on Realizing AI Synergies in Software Engineering (RAISE 2015).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Leandro L<PERSON>"], "summary": "This workshop is the fourth in the series and continued to build upon the work carried out at the previous iterations of the International Workshop on Realizing Artificial Intelligence Synergies in Software Engineering, which were held at ICSE in 2012, 2013 and 2014. RAISE 2015 brought together researchers and practitioners from the artificial intelligence (AI) and software engineering (SE) disciplines to build on the interdis- ciplinary synergies that exist and to stimulate further interaction across these disciplines. Mutually beneficial characteristics have appeared in the past few decades and are still evolving due to new challenges and technological advances. Hence, the question that motivates and drives the RAISE Workshop series is: \"Are SE and AI researchers ignoring important insights from AI and SE?\". To pursue this question, RAISE'15 explored not only the application of AI techniques to SE problems but also the application of SE techniques to AI problems. RAISE not only strengthens the AI- and-SE community but also continues to develop a roadmap of strategic research directions for AI and SE.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.320"}, {"primary_key": "4442331", "vector": [], "sparse_vector": [], "title": "ViDI: The Visual Design Inspector.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present <PERSON>iDI (Visual Design Inspector), a novel code review tool which focuses on quality concerns and design inspection as its cornerstones. It leverages visualization techniques to represent the reviewed software and augments the visualization with the results of quality analysis tools. To effectively understand the contribution of a reviewer in terms of the impact of her changes on the overall system quality, ViDI supports the recording and further inspection of reviewing sessions. ViDI is an advanced prototype which we will soon release to the Pharo open-source community.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.215"}, {"primary_key": "4442335", "vector": [], "sparse_vector": [], "title": "Cascade: A Universal Programmer-Assisted Type Qualifier Inference Tool.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Type qualifier inference tools usually operate in batch mode and assume that the program must not be changed except to add the type qualifiers. In practice, programs must be changed to make them type-correct, and programmers must understand them. Cascade is an interactive type qualifier inference tool that is easy to implement and universal (i.e., it can work for any type qualifier system for which a checker is implemented). It shows that qualifier inference can achieve better results by involving programmers rather than relying solely on automation.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.44"}, {"primary_key": "4442336", "vector": [], "sparse_vector": [], "title": "Automated Decomposition of Build Targets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "A (build) target specifies the information that is needed to automatically build a software artifact. This paper focuses on underutilized targets - an important dependency problem that we identified at Google. An underutilized target is one with files not needed by some of its dependents. Underutilized targets result in less modular code, overly large artifacts, slow builds, and unnecessary build and test triggers. To mitigate these problems, programmers decompose underutilized targets into smaller targets. However, manually decomposing a target is tedious and error-prone. Although we prove that finding the best target decomposition is NP-hard, we introduce a greedy algorithm that proposes a decomposition through iterative unification of the strongly connected components of the target. Our tool found that 19,994 of 40,000 Java library targets at Google can be decomposed to at least two targets. The results show that our tool is (1) efficient because it analyzes a target in two minutes on average and (2) effective because for each of 1,010 targets, it would save at least 50% of the total execution time of the tests triggered by the target.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.34"}, {"primary_key": "4442338", "vector": [], "sparse_vector": [], "title": "Enabling Testing of Android Apps.", "authors": ["<PERSON>"], "summary": "Existing approaches for automated testing of An- droid apps are designed to achieve different goals and exhibit some pros and cons that should be carefully considered by developers and testers. For instance, random testing (RT) provides a high ratio of infeasible inputs or events, and test cases generated with RT and systematic exploration-based testing (SEBT) are not representative of natural (i.e., real) application usage scenarios. In addition, collecting test scripts for automated testing is expensive. We address limitations of existing tools for GUI-based testing of Android apps in a novel hybrid approach called T+. Our approach is based on a novel framework, which is aimed at generating actionable test cases for different testing goals. The framework also enables GUI-based testing without expensive test scripts collection for the stakeholders.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.242"}, {"primary_key": "4442339", "vector": [], "sparse_vector": [], "title": "ChangeScribe: A Tool for Automatically Generating Commit Messages.", "authors": ["<PERSON>", "<PERSON>-<PERSON>y", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "During software maintenances tasks, commit messages are an important source of information, knowledge, and documentation that developers rely upon. However, the number and nature of daily activities and interruptions can influence the quality of resulting commit messages. This formal demonstration paper presents ChangeScribe, a tool for automatically generating commit messages. ChangeScribe is available at http://www.cs.wm.edu/semeru/changescribe (Eclipse plugin, instructions, demos and the source code)", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.229"}, {"primary_key": "4442341", "vector": [], "sparse_vector": [], "title": "A Large Scale Study of License Usage on GitHub.", "authors": ["<PERSON>"], "summary": "The open source community relies upon licensing in order to govern the distribution, modification, and reuse of existing code. These licenses evolve to better suit the requirements of the development communities and to cope with unaddressed or new legal issues. In this paper, we report the results of a large empirical study conducted over the change history of 16,221 open source Java projects mined from Git Hub. Our study investigates how licensing usage and adoption changes over a period of ten years. We consider both the distribution of license usage within projects of a rapidly growing forge and the extent that new versions of licenses are introduced in these projects.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.245"}, {"primary_key": "4442342", "vector": [], "sparse_vector": [], "title": "Fast Feedback Cycles in Empirical Software Engineering Research.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Background/Context: Gathering empirical knowledge is a time consuming task and the results from empirical studies often are soon outdated by new technological solutions. As a result, the impact of empirical results on software engineering practice is often not guaranteed.Objective/Aim: In this paper, we summarise the ongoing discussion on \"Empirical Software Engineering 2.0\" as a way to improve the impact of empirical results on industrial practices. We propose a way to combine data mining and analysis with domain knowledge to enable fast feedback cycles in empirical software engineering research.Method: We identify the key concepts on gathering fast feedback in empirical software engineering by following an experience-based line of reasoning by argument. Based on the identified key concepts, we design and execute a small proof of concept with a company to demonstrate potential benefits of the approach.Results: In our example, we observed that a simple double feedback mechanism notably increased the precision of the data analysis and improved the quality of the knowledge gathered.Conclusion: Our results serve as a basis to foster discussion and collaboration within the research community for a development of the idea.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.198"}, {"primary_key": "4442345", "vector": [], "sparse_vector": [], "title": "The Development of a Dashboard Tool for Visualising Online Teamwork Discussions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Nickolas J<PERSON>", "<PERSON>"], "summary": "Many software development organisations today adopt global software engineering (GSE) and agile models, requiring software engineers to collaborate and develop software in flexible, distributed, online teams. However, many employers have expressed concern that graduates lack teamwork skills and one of the most commonly occurring problems with GSE models are issues with project management. Team managers and educators often oversee a number of teams and the large corpus of data, in combination with agile models, make it difficult to efficiently assess factors such as team role distribution and emotional climate. Current methods and tools for monitoring software engineering (SE) teamwork in both industry and education settings typically focus on member contributions, reflection, or product outcomes, which are limited in terms of real-time feedback and accurate behavioural analysis. We have created a dashboard that extracts and communicates team role distribution and team emotion information in real-time. Our proof of concept provides a real-time analysis of teamwork discussions and visualises team member emotions, the roles they have adopted and overall team sentiment during the course of a collaborative problem-solving project. We demonstrate and discuss how such a tool could be useful for SE team management and training and the development of teamwork skills in SE university courses.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.170"}, {"primary_key": "4442347", "vector": [], "sparse_vector": [], "title": "Workshop on Applications of Human Error Research to Improve Software Engineering (WAHESE 2015).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Advances in the psychological understanding of the origins and manifestations of human error have led to tremendous reductions in errors in fields such as medicine, aviation, and nuclear power plants. This workshop is intended to foster a better understanding of software engineering errors and how a psychological perspective can reduce them, improving software quality and reducing maintenance costs. The workshop goal is to develop a body of knowledge that can advance our understanding of the psychological processes (of human reasoning, planning, and problem solving) and how they fail during the software development. Applying human error research to software quality improvement will provide insights to the cognitive aspects of software development. The workshop will include interactive session to discuss common themes of errors in different fields, and structure software error information to detect and prevent software errors during the development.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.353"}, {"primary_key": "4442349", "vector": [], "sparse_vector": [], "title": "Poster: MAPP: The Berkeley Model and Algorithm Prototyping Platform.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We describe the Berkeley Model and Algorithm Prototyping Platform (MAPP), designed to facilitate experimentation with numerical algorithms and models. MAPP is written entirely in MATLAB and is available as open source under the GNU GPL.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.267"}, {"primary_key": "4442353", "vector": [], "sparse_vector": [], "title": "How Much Up-Front? A Grounded theory of Agile Architecture.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The tension between software architecture and agility is not well understood by agile practitioners or researchers. If an agile software team spends too little time designing architecture up-front then the team faces increased risk and higher chance of failure, if the team spends too much time the delivery of value to the customer is delayed, and responding to change can become extremely difficult. This paper presents a grounded theory of agile architecture that describes how agile software teams answer the question of how much upfront architecture design effort is enough. This theory, based on grounded theory research involving 44 participants, presents six forces that affect the team's context and five strategies that teams use to help them determine how much effort they should put into up-front design.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.54"}, {"primary_key": "4442354", "vector": [], "sparse_vector": [], "title": "Poster: ProNat: An Agent-Based System Design for Programming in Spoken Natural Language.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The emergence of natural language interfaces has led to first attempts of programming in natural language. We present ProNat, a tool for script-like programming in spoken natural language (SNL). Its agent-based architecture unifies deep natural language understanding (NLU) with modular software design. ProNat focuses on the extraction of processing flows and control structures from spoken utterances. For evaluation we have begun to build a speech corpus. First experiments are conducted in the domain of domestic robotics, but ProNat's architecture makes domain acquisition easy. Test results with spoken utterances in ProNat seem promising, but much work has to be done to achieve deep NLU.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.264"}, {"primary_key": "4442355", "vector": [], "sparse_vector": [], "title": "Database-Backed Program Analysis for Scalable Error Propagation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software is rapidly increasing in size and complexity. Static analyses must be designed to scale well if they are to be usable with realistic applications, but prior efforts have often been limited by available memory. We propose a database-backed strategy for large program analysis based on graph algorithms, using a Semantic Web database to manage representations of the program under analysis. Our approach is applicable to a variety of interprocedural finite distributive subset (IFDS) dataflow problems; we focus on error propagation as a motivating example. Our implementation analyzes multi-million-line programs quickly and in just a fraction of the memory required by prior approaches. When memory alone is insufficient, our approach falls back on disk using several hybrid configurations tuned to put all available resources to good use.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.75"}, {"primary_key": "4442356", "vector": [], "sparse_vector": [], "title": "Teaching Software Systems Thinking at The Open University.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Open University is a distance-based higher education institution. Most of our students are in employment and study from home, contacting their tutor and fellow students via e-mail and discussion forums. In this paper, we describe our undergraduate and postgraduate modules in the software systems area, how we teach them at a distance, and our focus on shifting our students' minds into a reflective, critical, holistic socio-technical view of software systems that is relevant to their particular professional contexts.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.161"}, {"primary_key": "4442359", "vector": [], "sparse_vector": [], "title": "A Flexible and Non-intrusive Approach for Computing Complex Structural Coverage Metrics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>gt<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Software analysis tools and techniques often leverage structural code coverage information to reason about the dynamic behavior of software. Existing techniques instrument the code with the required structural obligations and then monitor the execution of the compiled code to report coverage. Instrumentation based approaches often incur considerable runtime overhead for complex structural coverage metrics such as Modified Condition/Decision (MC/DC). Code instrumentation, in general, has to be approached with great care to ensure it does not modify the behavior of the original code. Furthermore, instrumented code cannot be used in conjunction with other analyses that reason about the structure and semantics of the code under test. In this work, we introduce a non-intrusive preprocessing approach for computing structural coverage information. It uses a static partial evaluation of the decisions in the source code and a source-to-bytecode mapping to generate the information necessary to efficiently track structural coverage metrics during execution. Our technique is flexible; the results of the preprocessing can be used by a variety of coverage-driven software analysis tasks, including automated analyses that are not possible for instrumented code. Experimental results in the context of symbolic execution show the efficiency and flexibility of our non- intrusive approach for computing code coverage information.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.68"}, {"primary_key": "4442360", "vector": [], "sparse_vector": [], "title": "Deep Representations for Software Engineering.", "authors": ["<PERSON>"], "summary": "Deep learning subsumes algorithms that automatically learn compositional representations. The ability of these models to generalize well has ushered in tremendous advances in many fields. We propose that software engineering (SE) research is a unique opportunity to use these transformative approaches. Our research examines applications of deep architectures such as recurrent neural networks and stacked restricted Boltzmann machines to SE tasks.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.248"}, {"primary_key": "4442361", "vector": [], "sparse_vector": [], "title": "Drawing Insight from Student Perceptions of Reflective Design Learning.", "authors": ["<PERSON>", "<PERSON>"], "summary": "While design and designing are core elements in computer science and software engineering, conventional curricular structures do not adequately support design learning. Current methods tend to isolate the study of design within specific subject matter and lack a strong emphasis on reflection. This paper reports on insights and lessons learned from a user study in the context of ongoing work on developing an educational intervention that better supports design learning with a particular emphasis on learner-driven reflection. Insights drawn from this study relate to general aspects of design learning, such as the importance of collaborative reflection and the impact of learner perceptions regarding their abilities, as well as to specific improvements to our approach.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.154"}, {"primary_key": "4442363", "vector": [], "sparse_vector": [], "title": "No PAIN, No Gain? The Utility of PArallel Fault INjections.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Software Fault Injection (SFI) is an established technique for assessing the robustness of a software under test by exposing it to faults in its operational environment. Depending on the complexity of this operational environment, the complexity of the software under test, and the number and type of faults, a thorough SFI assessment can entail (a) numerous experiments and (b) long experiment run times, which both contribute to a considerable execution time for the tests. In order to counteract this increase when dealing with complex systems, recent works propose to exploit parallel hardware to execute multiple experiments at the same time. While Parallel fault Injections (PAIN) yield higher experiment throughput, they are based on an implicit assumption of non-interference among the simultaneously executing experiments. In this paper we investigate the validity of this assumption and determine the trade-off between increased throughput and the accuracy of experimental results obtained from PAIN experiments.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.67"}, {"primary_key": "4442364", "vector": [], "sparse_vector": [], "title": "DASE: Document-Assisted Symbolic Execution for Improving Automated Software Testing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose and implement a new approach, Document-Assisted Symbolic Execution (DASE), to improve automated test generation and bug detection. DASE leverages natural language processing techniques and heuristics to analyze program documentation to extract input constraints automatically. DASE then uses the input constraints to guide symbolic execution to focus on inputs that are semantically more important.We evaluated DASE on 88 programs from 5 mature real-world software suites: COREUTILS, FINDUTILS, GREP, BINUTILS, and ELFTOOLCHAIN. DASE detected 12 previously unknown bugs that symbolic execution without input constraints failed to detect, 6 of which have already been confirmed by the developers. In addition, DASE increases line coverage, branch coverage, and call coverage by 14.2 -- 120.3%, 2.3 -- 167.7%, and 16.9 -- 135.2% respectively, which are 6.0 -- 21.1 percentage points (pp), 1.6 -- 18.9 pp, and 2.8 -- 20.1 pp increases. The accuracies of input constraint extraction are 97.8 -- 100%.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.78"}, {"primary_key": "4442365", "vector": [], "sparse_vector": [], "title": "FLEXISKETCH TEAM: Collaborative Sketching and Notation Creation on the Fly.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "When software engineers collaborate, they frequently use whiteboards or paper for sketching diagrams. This is fast and flexible, but the resulting diagrams cannot be interpreted by software modeling tools. We present FLEXISKETCH TEAM, a tool solution consisting of a significantly extended version of our previous, single-user FLEXISKETCH tool for Android devices and a new desktop tool. Our solution for collaborative, model-based sketching of free-form diagrams allows users to define and re-use diagramming notations on the fly. Several users can work simultaneously on the same model sketch with multiple tablets. The desktop tool provides a shared view of the drawing canvas which can be projected onto an electronic whiteboard. Preliminary results from an exploratory study show that our tool motivates meeting participants to actively take part in sketching as well as defining ad-hoc notations.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.223"}, {"primary_key": "4442370", "vector": [], "sparse_vector": [], "title": "Automated Modularization of GUI Test Cases.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Test cases that drive an application under test via its graphical user interface (GUI) consist of sequences of steps that perform actions on, or verify the state of, the application user interface. Such tests can be hard to maintain, especially if they are not properly modularized - that is, common steps occur in many test cases, which can make test maintenance cumbersome and expensive. Performing modularization manually can take up considerable human effort. To address this, we present an automated approach for modularizing GUI test cases. Our approach consists of multiple phases. In the first phase, it analyzes individual test cases to partition test steps into candidate subroutines, based on how user-interface elements are accessed in the steps. This phase can analyze the test cases only or also leverage execution traces of the tests, which involves a cost-accuracy tradeoff. In the second phase, the technique compares candidate subroutines across test cases, and refines them to compute the final set of subroutines. In the last phase, it creates callable subroutines, with parameterized data and control flow, and refactors the original tests to call the subroutines with context-specific data and control parameters. Our empirical results, collected using open-source applications, illustrate the effectiveness of the approach.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.27"}, {"primary_key": "4442372", "vector": [], "sparse_vector": [], "title": "AppContext: Differentiating Malicious and Benign Mobile App Behaviors Using Context.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile malware attempts to evade detection during app analysis by mimicking security-sensitive behaviors of benign apps that provide similar functionality (e.g., sending SMS messages), and suppressing their payload to reduce the chance of being observed (e.g., executing only its payload at night). Since current approaches focus their analyses on the types of security-sensitive resources being accessed (e.g., network), these evasive techniques in malware make differentiating between malicious and benign app behaviors a difficult task during app analysis. We propose that the malicious and benign behaviors within apps can be differentiated based on the contexts that trigger security-sensitive behaviors, i.e., the events and conditions that cause the security-sensitive behaviors to occur. In this work, we introduce AppContext, an approach of static program analysis that extracts the contexts of security-sensitive behaviors to assist app analysis in differentiating between malicious and benign behaviors. We implement a prototype of AppContext and evaluate AppContext on 202 malicious apps from various malware datasets, and 633 benign apps from the Google Play Store. AppContext correctly identifies 192 malicious apps with 87.7% precision and 95% recall. Our evaluation results suggest that the maliciousness of a security-sensitive behavior is more closely related to the intention of the behavior (reflected via contexts) than the type of the security-sensitive resources that the behavior accesses.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.50"}, {"primary_key": "4442373", "vector": [], "sparse_vector": [], "title": "Static Control-Flow Analysis of User-Driven Callbacks in Android Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Dacong Yan", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Android software presents many challenges for static program analysis. In this work we focus on the fundamental problem of static control-flow analysis. Traditional analyses cannot be directly applied to Android because the applications are framework-based and event-driven. We consider user-event-driven components and the related sequences of callbacks from the Android framework to the application code, both for lifecycle callbacks and for event handler callbacks. We propose a program representation that captures such callback sequences. This representation is built using context-sensitive static analysis of callback methods. The analysis performs graph reachability by traversing context-compatible interprocedural control-flow paths and identifying statements that may trigger callbacks, as well as paths that avoid such statements. We also develop a client analysis that builds a static model of the application's GUI. Experimental evaluation shows that this context-sensitive approach leads to substantial precision improvements, while having practical cost.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.31"}, {"primary_key": "4442374", "vector": [], "sparse_vector": [], "title": "A Synergistic Analysis Method for Explaining Failed Regression Tests.", "authors": ["<PERSON><PERSON><PERSON> Yi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a new automated debugging method for regression testing based on a synergistic application of both dynamic and semantic analysis. Our method takes a failure- inducing test input, a buggy program, and an earlier correct version of the same program, and computes a minimal set of code changes responsible for the failure, as well as explaining how the code changes lead to the failure. Although this problem has been the subject of intensive research in recent years, existing methods are rarely adopted by developers in practice since they do not produce sufficiently accurate fault explanations for real applications. Our new method is significantly faster and more accurate than existing methods for explaining failed regression tests in real applications, due to its synergistic analysis framework that iteratively applies both dynamic analysis and a constraint solver based semantic analysis to leverage their complementary strengths. We have implemented our new method in a software tool based on the LLVMcompiler and the KLEE symbolic virtual machine. Our experiments on large real Linux applications show that the new method is both efficient and effective in practice.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.46"}, {"primary_key": "4442375", "vector": [], "sparse_vector": [], "title": "Supporting Selective Undo in a Code Editor.", "authors": ["Young<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Programmers often need to revert some code to an earlier state, or restore a block of code that was deleted a while ago. However, support for this backtracking in modern programming environments is limited. Many of the backtracking tasks can be accomplished by having a selective undo feature in code editors, but this has major challenges: there can be conflicts among edit operations, and it is difficult to provide usable interfaces for selective undo. In this paper, we present AZURITE, an Eclipse plug-in that allows programmers to selectively undo fine-grained code changes made in the code editor. With AZURITE, programmers can easily perform backtracking tasks, even when the desired code is not in the undo stack or a version control system. AZURITE also provides novel user interfaces specifically designed for selective undo, which were iteratively improved through user feedback gathered from actual users in a preliminary field trial. A formal lab study showed that programmers can successfully use AZURITE, and were twice as fast as when limited to conventional features.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.43"}, {"primary_key": "4442376", "vector": [], "sparse_vector": [], "title": "Do Security Patterns Really Help Designers?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "W<PERSON><PERSON>"], "summary": "Security patterns are well-known solutions to security-specific problems. They are often claimed to benefit designers without much security expertise. We have performed an empirical study to investigate whether the usage of security patterns by such an audience leads to a more secure design, or to an increased productivity of the designers. Our study involved 32 teams of master students enrolled in a course on software architecture, working on the design of a realistically-sized banking system. Irrespective of whether the teams were using security patterns, we have not been able to detect a difference between the two treatment groups. However, the teams prefer to work with the support of security patterns.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.49"}, {"primary_key": "4442377", "vector": [], "sparse_vector": [], "title": "Does the Failing Test Execute a Single or Multiple Faults? An Approach to Classifying Failing Tests.", "authors": ["Zhongxing Yu", "Chenggang Bai", "<PERSON><PERSON><PERSON>"], "summary": "Debugging is an indispensable yet frustrating activity in software development and maintenance. Thus, numerous techniques have been proposed to aid this task. Despite the demonstrated effectiveness and future potential of these techniques, many of them have the unrealistic single-fault failure assumption. To alleviate this problem, we propose a technique that can be used to distinguish failing tests that executed a single fault from those that executed multiple faults in this paper. The technique suitably combines information from (i) a set of fault localization ranked lists, each produced for a certain failing test and (ii) the distance between a failing test and the passing test that most resembles it to achieve this goal. An experiment on 5 real-life medium-sized programs with 18, 920 multiple-fault versions, which are shipped with number of faults ranging from 2 to 8, has been conducted to evaluate the technique. The results indicate that the performance of the technique in terms of evaluation measures precision, recall, and F-measure is promising. In addition, for the identified failing tests that executed a single fault, the technique can also properly cluster them.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.102"}, {"primary_key": "4442378", "vector": [], "sparse_vector": [], "title": "ReCBuLC: Reproducing Concurrency Bugs Using Local Clocks.", "authors": ["<PERSON>ang Yuan", "<PERSON><PERSON><PERSON>", "Zhen<PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Multi-threaded programs play an increasingly important role in current multi-core environments. Exposing concurrency bugs and debugging such multi-threaded programs have become quite challenging due to their inherent non-determinism. In order to eliminate such non-determinism, many approaches such as record-and-replay and other similar bug reproducing systems have been proposed. However, those approaches often suffer significant performance degradation because they require a large amount of recorded information and/or long analysis and replay time. In this paper, we propose an effective approach, ReCBuLC, to take advantage of the hardware clocks available on modern processors. The key idea is to reduce the recording overhead and analyzing events' global order by using time stamps recorded in each thread. Those timestamps are used to determine the global orders of shared accesses. To avoid the large overhead incurred in accessing system-wide global clock, we opt to use local per-core clocks that incur much less access overhead. We then propose techniques to resolve differences among local clocks and obtain an accurate global event order. By using per-core clocks, state-of-the-art bug reproducing systems such as PRES and CLAP can reduce the recording overheads by 1% ~ 85%, and the analysis time by 84.66% ~ 99.99%, respectively.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.94"}, {"primary_key": "4442379", "vector": [], "sparse_vector": [], "title": "Chiminey: Reliable Computing and Data Management Platform in the Cloud.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The enabling of scientific experiments that are embarrassingly parallel, long running and data-intensive into a cloud-based execution environment is a desirable, though complex undertaking for many researchers. The management of such virtual environments is cumbersome and not necessarily within the core skill set for scientists and engineers. We present here Chiminey, a software platform that enables researchers to (i) run applications on both traditional high-performance computing and cloud-based computing infrastructures, (ii) handle failure during execution, (iii) curate and visualise execution outputs, (iv) share such data with collaborators or the public, and (v) search for publicly available data.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.221"}, {"primary_key": "4442381", "vector": [], "sparse_vector": [], "title": "Scalability Studies on Selective Mutation Testing.", "authors": ["<PERSON><PERSON>"], "summary": "Mutation testing is a test method which is designed to evaluate a test suite's quality. Due to the expensive cost of mutation testing, selective mutation testing was first proposed in 1991 by <PERSON><PERSON>, in which a subset of mutants are selected aiming to achieve the same effectiveness as the whole set of mutants in evaluating the quality of test suites. Though selective mutation testing has been widely investigated in recent years, many people still doubt if it can suit well for large programs. Realizing that none of the existing work has systematically studied the scalability of selective mutation testing, I plan to work on the scalability of selective mutation testing through several studies.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.276"}, {"primary_key": "4442382", "vector": [], "sparse_vector": [], "title": "Regular Property Guided Dynamic Symbolic Execution.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A challenging problem in software engineering is to check if a program has an execution path satisfying a regular property. We propose a novel method of dynamic symbolic execution (DSE) to automatically find a path of a program satisfying a regular property. What makes our method distinct is when exploring the path space, DSE is guided by the synergy of static analysis and dynamic analysis to find a target path as soon as possible. We have implemented our guided DSE method for Java programs based on JPF and WALA, and applied it to 13 real-world open source Java programs, a total of 225K lines of code, for extensive experiments. The results show the effectiveness, efficiency, feasibility and scalability of the method. Compared with the pure DSE on the time to find the first target path, the average speedup of the guided DSE is more than 258X when analyzing the programs that have more than 100 paths.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.80"}, {"primary_key": "4442383", "vector": [], "sparse_vector": [], "title": "Interactive Code Review for Systematic Changes.", "authors": ["<PERSON><PERSON><PERSON>", "Myoungkyu Song", "<PERSON>", "<PERSON><PERSON>"], "summary": "Developers often inspect a diff patch during peer code reviews. Diff patches show low-level program differences per file without summarizing systematic changes -- similar, related changes to multiple contexts. We present Critics, an interactive approach for inspecting systematic changes. When a developer specifies code change within a diff patch, Critics allows developers to customize the change template by iteratively generalizing change content and context. By matching a generalized template against the codebase, it summarizes similar changes and detects potential mistakes. We evaluated Critics using two methods. First, we conducted a user study at Salesforce.com, where professional engineers used Critics to investigate diff patches authored by their own team. After using Critics, all six participants indicated that they would like Critics to be integrated into their current code review environment. This also attests to the fact that Critics scales to an industry-scale project and can be easily adopted by professional engineers. Second, we conducted a user study where twelve participants reviewed diff patches using Critics and Eclipse diff. The results show that human subjects using Critics answer questions about systematic changes 47.3% more correctly with 31.9% saving in time during code review tasks, in comparison to the baseline use of Eclipse diff. These results show that Critics should improve developer productivity in inspecting systematic changes during peer code reviews.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.33"}, {"primary_key": "4442386", "vector": [], "sparse_vector": [], "title": "An Empirical Study on Real Bug Fixes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software bugs can cause significant financial loss and even the loss of human lives. To reduce such loss, developers devote substantial efforts to fixing bugs, which generally requires much expertise and experience. Various approaches have been proposed to aid debugging. An interesting recent research direction is automatic program repair, which achieves promising results, and attracts much academic and industrial attention. However, people also cast doubt on the effectiveness and promise of this direction. A key criticism is to what extent such approaches can fix real bugs. As only research prototypes for these approaches are available, it is infeasible to address the criticism by evaluating them directly on real bugs. Instead, in this paper, we design and develop BUGSTAT, a tool that extracts and analyzes bug fixes. With BUGSTAT's support, we conduct an empirical study on more than 9,000 real-world bug fixes from six popular Java projects. Comparing the nature of manual fixes with automatic program repair, we distill 15 findings, which are further summarized into four insights on the two key ingredients of automatic program repair: fault localization and faulty code fix. In addition, we provide indirect evidence on the size of the search space to fix real bugs and find that bugs may also reside in non-source files. Our results provide useful guidance and insights for improving the state-of-the-art of automatic program repair.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.101"}, {"primary_key": "4442388", "vector": [], "sparse_vector": [], "title": "Poster: Segmentation Based Online Performance Problem Diagnosis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Currently, the performance problems of software systems gets more and more attentions. Among various diagnosis methods based on system traces, principal component analysis (PCA) based methods are widely used due to the high accuracy of the diagnosis results and requiring no specific domain knowledge. However, according to our experiments, we have validated several shortcomings existed in PCA-based methods, including requiring traces with a same call sequence, inefficiency when the traces are long, and missing performance problems. To cope with these issues, we introduce a segmentation based online diagnosis method in this poster.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.258"}, {"primary_key": "4442389", "vector": [], "sparse_vector": [], "title": "An Empirical Study on Quality Issues of Production Big Data Platform.", "authors": ["<PERSON><PERSON> Zhou", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tingting Qin"], "summary": "Big Data computing platform has evolved to be a multi-tenant service. The service quality matters because system failure or performance slowdown could adversely affect business and user experience. There is few study in literature on service quality issues of production Big Data computing platform. In this paper, we present an empirical study on the service quality issues of Microsoft ProductA, which is a company-wide multi-tenant Big Data computing platform, serving thousands of customers from hundreds of teams. ProductA has a well-defined incident management process, which helps customers report and mitigate service quality issues on 24/7 basis. This paper explores the common symptom, causes and mitigation of service quality issues in Big Data computing. We conduct an empirical study on 210 real service quality issues in ProductA. Our major findings include (1) 21.0% of escalations are caused by hardware faults; (2) 36.2% are caused by system side defects; (3) 37.2% are due to customer side faults. We also studied the general diagnosis process and the commonly adopted mitigation solutions. Our findings can help improve current development and maintenance practice of Big Data computing platform, and motivate tool support.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.130"}, {"primary_key": "4442390", "vector": [], "sparse_vector": [], "title": "Learning to Log: Helping Developers Make Informed Logging Decisions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Logging is a common programming practice of practical importance to collect system runtime information for postmortem analysis. Strategic logging placement is desired to cover necessary runtime information without incurring unintended consequences (e.g., Performance overhead, trivial logs). However, in current practice, there is a lack of rigorous specifications for developers to govern their logging behaviours. Logging has become an important yet tough decision which mostly depends on the domain knowledge of developers. To reduce the effort on making logging decisions, in this paper, we propose a \"learning to log\" framework, which aims to provide informative guidance on logging during development. As a proof of concept, we provide the design and implementation of a logging suggestion tool, Log Advisor, which automatically learns the common logging practices on where to log from existing logging instances and further leverages them for actionable suggestions to developers. Specifically, we identify the important factors for determining where to log and extract them as structural features, textual features, and syntactic features. Then, by applying machine learning techniques (e.g., Feature selection and classifier learning) and noise handling techniques, we achieve high accuracy of logging suggestions. We evaluate Log Advisor on two industrial software systems from Microsoft and two open-source software systems from Git Hub (totally 19.1M LOC and 100.6K logging statements). The encouraging experimental results, as well as a user study, demonstrate the feasibility and effectiveness of our logging suggestion tool. We believe our work can serve as an important first step towards the goal of \"learning to log\".", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.60"}, {"primary_key": "4442391", "vector": [], "sparse_vector": [], "title": "A Programming Model for Sustainable Software.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a novel energy-aware and temperature-aware programming model with first-class support for sustainability. A program written in the new language, named Eco, may adaptively adjusts its own behaviors to stay on a given (energy or temperature) budget, avoiding both deficit that would lead to battery drain or CPU overheating, and surplus that could have been used to improve the quality of results. Sustainability management in Eco is captured as a form of supply and demand matching, and the language runtime consistently maintains the equilibrium between supply and demand. Among the efforts of energy-adaptive and temperature-adaptive systems, Eco is distinctive in its role in bridging the programmer and the underlying system, and in particular, bringing both programmer knowledge and application-specific traits into energy optimization. Through a number of intuitive programming abstractions, Eco reduces challenging issues in this domain --- such as workload characterization and decision making in adaptation --- to simple programming tasks, ultimately offering fine-grained, programmable, and declarative sustainability to energy-efficient computing. Eco is an minimal extension to Java, and has been implemented as an open-source compiler. We validate the usefulness of Eco by upgrading real-world Java applications with energy awareness and temperature awareness.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.89"}, {"primary_key": "4442392", "vector": [], "sparse_vector": [], "title": "Qualitative Analysis of Knowledge Transfer in Pair Programming.", "authors": ["<PERSON>"], "summary": "Knowledge transfer in the context of pair programming is both a desired effect and a necessary precondition. There is no detailed understanding yet of how effective and efficient knowledge transfer in this particular context actually works. My qualitative research is concerned with the analysis of professional software developer's sessions to capture their specific knowledge transfer skill in the form of comprehensible, relevant, and practical patterns.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.277"}, {"primary_key": "4442396", "vector": [], "sparse_vector": [], "title": "A Genetic Algorithm for Detecting Significant Floating-Point Inaccuracies.", "authors": ["Daming Zou", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Hong Mei"], "summary": "It is well-known that using floating-point numbers may inevitably result in inaccurate results and sometimes even cause serious software failures. Safety-critical software often has strict requirements on the upper bound of inaccuracy, and a crucial task in testing is to check whether significant inaccuracies may be produced. The main existing approach to the floating-point inaccuracy problem is error analysis, which produces an upper bound of inaccuracies that may occur. However, a high upper bound does not guarantee the existence of inaccuracy defects, nor does it give developers any concrete test inputs for debugging. In this paper, we propose the first metaheuristic search-based approach to automatically generating test inputs that aim to trigger significant inaccuracies in floating-point programs. Our approach is based on the following two insights: (1) with FPDebug, a recently proposed dynamic analysis approach, we can build a reliable fitness function to guide the search; (2) two main factors -- the scales of exponents and the bit formations of significands -- may have significant impact on the accuracy of the output, but in largely different ways. We have implemented and evaluated our approach over 154 real-world floating-point functions. The results show that our approach can detect significant inaccuracies in the subjects.", "published": "2015-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2015.70"}]