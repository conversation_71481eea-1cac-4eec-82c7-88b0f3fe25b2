[{"primary_key": "3716808", "vector": [], "sparse_vector": [], "title": "Self-Crowdsourcing Training for Relation Extraction.", "authors": ["Azad Abad", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we introduce a self-training strategy for crowdsourcing. The training examples are automatically selected to train the crowd workers. Our experimental results show an impact of 5% Improvement in terms of F1 for relation extraction task, compared to the method based on distant supervision.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2082"}, {"primary_key": "3716809", "vector": [], "sparse_vector": [], "title": "EmoNet: Fine-Grained Emotion Detection with Gated Recurrent Neural Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Accurate detection of emotion from natural language has applications ranging from building emotional chatbots to better understanding individuals and their lives. However, progress on emotion detection has been hampered by the absence of large labeled datasets. In this work, we build a very large dataset for fine-grained emotions and develop deep learning models on it. We achieve a new state-of-the-art on 24 fine-grained types of emotions (with an average accuracy of 87.58%). We also extend the task beyond emotion types to model <PERSON>’s 8 primary emotion dimensions, acquiring a superior accuracy of 95.68%.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1067"}, {"primary_key": "3716810", "vector": [], "sparse_vector": [], "title": "The State of the Art in Semantic Representation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Semantic representation is receiving growing attention in NLP in the past few years, and many proposals for semantic schemes (e.g., AMR, UCCA, GMB, UDS) have been put forth. Yet, little has been done to assess the achievements and the shortcomings of these new contenders, compare them with syntactic schemes, and clarify the general goals of research on semantic representation. We address these gaps by critically surveying the state of the art in the field.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1008"}, {"primary_key": "3716811", "vector": [], "sparse_vector": [], "title": "UCCAApp: Web-application for Syntactic and Semantic Phrase-based Annotation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present UCCAApp, an open-source, flexible web-application for syntactic and semantic phrase-based annotation in general, and for UCCA annotation in particular.UCCAApp supports a variety of formal properties that have proven useful for syntactic and semantic representation, such as discontiguous phrases, multiple parents and empty elements, making it useful to a variety of other annotation schemes with similar formal properties.UCCAApp's user interface is intuitive and user friendly, so as to support annotation by users with no background in linguistics or formal representation.Indeed, a pilot version of the application has been successfully used in the compilation of the UCCA Wikipedia treebank by annotators with no previous linguistic training.The application and all accompanying resources are released as open source under the GNU public license, and are available online along with a live demo. 1", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4019"}, {"primary_key": "3716812", "vector": [], "sparse_vector": [], "title": "Automatic Generation of Jokes in Hindi.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "When it comes to computational language generation systems, humour is a relatively unexplored domain, especially more so for Hindi (or rather, for most languages other than English).Most researchers agree that a joke consists of two main parts -the setup and the punchline, with humour being encoded in the incongruity between the two.In this paper, we look at Du<PERSON> <PERSON> jokes, a restricted domain of humorous three liner poetry in Hindi.We analyze their structure to understand how humour is encoded in them and formalize it.We then develop a system which is successfully able to generate a basic form of these jokes.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3012"}, {"primary_key": "3716813", "vector": [], "sparse_vector": [], "title": "How (not) to train a dependency parser: The curious case of jackknifing part-of-speech taggers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In dependency parsing, jackknifing taggers is indiscriminately used as a simple adaptation strategy. Here, we empirically evaluate when and how (not) to use jackknifing in parsing. On 26 languages, we reveal a preference that conflicts with, and surpasses the ubiquitous ten-folding. We show no clear benefits of tagging the training data in cross-lingual parsing.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2107"}, {"primary_key": "3716814", "vector": [], "sparse_vector": [], "title": "Morphological Inflection Generation with Hard Monotonic Attention.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a neural model for morphological inflection generation which employs a hard attention mechanism, inspired by the nearly-monotonic alignment commonly found between the characters in a word and the characters in its inflection. We evaluate the model on three previously studied morphological inflection generation datasets and show that it provides state of the art results in various setups compared to previous neural and non-neural approaches. Finally we present an analysis of the continuous representations learned by both the hard and soft (<PERSON><PERSON><PERSON>, 2014) attention models for the task, shedding some light on the features such models extract.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1183"}, {"primary_key": "3716815", "vector": [], "sparse_vector": [], "title": "Towards String-To-Tree Neural Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a simple method to incorporate syntactic information about the target language in a neural machine translation system by translating into linearized, lexicalized constituency trees. An experiment on the WMT16 German-English news translation task resulted in an improved BLEU score when compared to a syntax-agnostic NMT baseline trained on the same dataset. An analysis of the translations from the syntax-aware system shows that it performs more reordering during translation in comparison to the baseline. A small-scale human evaluation also showed an advantage to the syntax-aware system.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2021"}, {"primary_key": "3716817", "vector": [], "sparse_vector": [], "title": "Chat Detection in an Intelligent Assistant: Combining Task-oriented and Non-task-oriented Spoken Dialogue Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently emerged intelligent assistants on smartphones and home electronics (e.g., <PERSON><PERSON> and <PERSON><PERSON>) can be seen as novel hybrids of domain-specific task-oriented spoken dialogue systems and open-domain non-task-oriented ones. To realize such hybrid dialogue systems, this paper investigates determining whether or not a user is going to have a chat with the system. To address the lack of benchmark datasets for this task, we construct a new dataset consisting of 15,160 utterances collected from the real log data of a commercial intelligent assistant (and will release the dataset to facilitate future research activity). In addition, we investigate using tweets and Web search queries for handling open-domain user utterances, which characterize the task of chat detection. Experimental experiments demonstrated that, while simple supervised methods are effective, the use of the tweets and search queries further improves the F1-score from 86.21 to 87.53.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1120"}, {"primary_key": "3716819", "vector": [], "sparse_vector": [], "title": "On the Distribution of Lexical Features at Multiple Levels of Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2013"}, {"primary_key": "3716820", "vector": [], "sparse_vector": [], "title": "Topical Coherence in LDA-based Models through Induced Segmentation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Massih<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1165"}, {"primary_key": "3716821", "vector": [], "sparse_vector": [], "title": "Translating Neuralese.", "authors": ["<PERSON>", "Anca D<PERSON>", "<PERSON>"], "summary": "Several approaches have recently been proposed for learning decentralized deep multiagent policies that coordinate via a differentiable communication channel. While these policies are effective for many tasks, interpretation of their induced communication strategies has remained a challenge. Here we propose to interpret agents’ messages by translating them. Unlike in typical machine translation problems, we have no parallel data to learn from. Instead we develop a translation model based on the insight that agent messages and natural language strings mean the same thing if they induce the same belief about the world in a listener. We present theoretical guarantees and empirical evidence that our approach preserves both the semantics and pragmatics of messages by ensuring that players communicating through a translation layer do not suffer a substantial loss in reward relative to players with a common language.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1022"}, {"primary_key": "3716822", "vector": [], "sparse_vector": [], "title": "Bayesian Modeling of Lexical Resources for Low-Resource Settings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Lexical resources such as dictionaries and gazetteers are often used as auxiliary data for tasks such as part-of-speech induction and named-entity recognition. However, discriminative training with lexical features requires annotated data to reliably estimate the lexical feature weights and may result in overfitting the lexical features at the expense of features which generalize better. In this paper, we investigate a more robust approach: we stipulate that the lexicon is the result of an assumed generative process. Practically, this means that we may treat the lexical resources as observations under the proposed generative model. The lexical resources provide training data for the generative model without requiring separate data to estimate lexical feature weights. We evaluate the proposed approach in two settings: part-of-speech induction and low-resource named-entity recognition.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1095"}, {"primary_key": "3716823", "vector": [], "sparse_vector": [], "title": "ESTEEM: A Novel Framework for Qualitatively Evaluating and Visualizing Spatiotemporal Embeddings in Social Media.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Analyzing and visualizing large amounts of social media communications and contrasting short-term conversation changes over time and geolocations is extremely important for commercial and government applications.Earlier approaches for largescale text stream summarization used dynamic topic models and trending words.Instead, we rely on text embeddingslow-dimensional word representations in a continuous vector space where similar words are embedded nearby each other.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4005"}, {"primary_key": "3716824", "vector": [], "sparse_vector": [], "title": "Learning bilingual word embeddings with (almost) no bilingual data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most methods to learn bilingual word embeddings rely on large parallel corpora, which is difficult to obtain for most language pairs. This has motivated an active research line to relax this requirement, with methods that use document-aligned corpora or bilingual dictionaries of a few thousand words instead. In this work, we further reduce the need of bilingual resources using a very simple self-learning approach that can be combined with any dictionary-based mapping technique. Our method exploits the structural similarity of embedding spaces, and works with as little bilingual evidence as a 25 word dictionary or even an automatically generated list of numerals, obtaining results comparable to those of systems that use richer resources.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1042"}, {"primary_key": "3716825", "vector": [], "sparse_vector": [], "title": "Multimodal Word Distributions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Word embeddings provide point representations of words containing useful semantic information. We introduce multimodal word distributions formed from Gaussian mixtures, for multiple word meanings, entailment, and rich uncertainty information. To learn these distributions, we propose an energy-based max-margin objective. We show that the resulting approach captures uniquely expressive semantic information, and outperforms alternatives, such as word2vec skip-grams, and Gaussian embeddings, on benchmark datasets such as word similarity and entailment.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1151"}, {"primary_key": "3716827", "vector": [], "sparse_vector": [], "title": "Multi-Task Learning of Keyphrase Boundary Classification.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Keyphrase boundary classification (KBC) is the task of detecting keyphrases in scientific articles and labelling them with respect to predefined types. Although important in practice, this task is so far underexplored, partly due to the lack of labelled data. To overcome this, we explore several auxiliary tasks, including semantic super-sense tagging and identification of multi-word expressions, and cast the task as a multi-task learning problem with deep recurrent neural networks. Our multi-task models perform significantly better than previous state of the art approaches on two scientific KBC datasets, particularly for long keyphrases.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2054"}, {"primary_key": "3716828", "vector": [], "sparse_vector": [], "title": "Joint Optimization of User-desired Content in Multi-document Summaries by Learning from User Feedback.", "authors": ["<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we propose an extractive multi-document summarization (MDS) system using joint optimization and active learning for content selection grounded in user feedback. Our method interactively obtains user feedback to gradually improve the results of a state-of-the-art integer linear programming (ILP) framework for MDS. Our methods complement fully automatic methods in producing high-quality summaries with a minimum number of iterations and feedbacks. We conduct multiple simulation-based experiments and analyze the effect of feedback-based concept selection in the ILP setup in order to maximize the user-desired content in the summary.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1124"}, {"primary_key": "3716829", "vector": [], "sparse_vector": [], "title": "Fast Forward Through Opportunistic Incremental Meaning Representation Construction.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "One of the challenges semantic parsers face involves upstream errors originating from pre-processing modules such as ASR and syntactic parsers, which undermine the end result from the get go.We report the work in progress on a novel incremental semantic parsing algorithm that supports simultaneous application of independent heuristics and facilitates the construction of partial but potentially actionable meaning representations to overcome this problem.Our contribution to this point is mainly theoretical.In future work we intend to evaluate the algorithm as part of a dialogue understanding system on state of the art benchmarks.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3019"}, {"primary_key": "3716830", "vector": [], "sparse_vector": [], "title": "Apples to Apples: Learning Semantics of Common Entities Through a Novel Comprehension Task.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Understanding common entities and their attributes is a primary requirement for any system that comprehends natural language. In order to enable learning about common entities, we introduce a novel machine comprehension task, GuessTwo: given a short paragraph comparing different aspects of two real-world semantically-similar entities, a system should guess what those entities are. Accomplishing this task requires deep language understanding which enables inference, connecting each comparison paragraph to different levels of knowledge about world entities and their attributes. So far we have crowdsourced a dataset of more than 14K comparison paragraphs comparing entities from a variety of categories such as fruits and animals. We have designed two schemes for evaluation: open-ended, and binary-choice prediction. For benchmarking further progress in the task, we have collected a set of paragraphs as the test set on which human can accomplish the task with an accuracy of 94.2% on open-ended prediction. We have implemented various models for tackling the task, ranging from semantic-driven to neural models. The semantic-driven approach outperforms the neural models, however, the results indicate that the task is very challenging across the models.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1084"}, {"primary_key": "3716831", "vector": [], "sparse_vector": [], "title": "What do Neural Machine Translation Models Learn about Morphology?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Neural machine translation (MT) models obtain state-of-the-art performance while maintaining a simple, end-to-end architecture. However, little is known about what these models learn about source and target languages during the training process. In this work, we analyze the representations learned by neural MT models at various levels of granularity and empirically evaluate the quality of the representations for learning morphology through extrinsic part-of-speech and morphological tagging tasks. We conduct a thorough investigation along several parameters: word-based vs. character-based representations, depth of the encoding layer, the identity of the target language, and encoder vs. decoder representations. Our data-driven, quantitative evaluation sheds light on important aspects in the neural MT system and its ability to capture word structure.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1080"}, {"primary_key": "3716832", "vector": [], "sparse_vector": [], "title": "Predicting Native Language from Gaze.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A fundamental question in language learning concerns the role of a speaker's first language in second language acquisition. We present a novel methodology for studying this question: analysis of eye-movement patterns in second language reading of free-form text. Using this methodology, we demonstrate for the first time that the native language of English learners can be predicted from their gaze fixations when reading English. We provide analysis of classifier uncertainty and learned features, which indicates that differences in English reading are likely to be rooted in linguistic divergences across native languages. The presented framework complements production studies and offers new ground for advancing research on multilingualism.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1050"}, {"primary_key": "3716833", "vector": [], "sparse_vector": [], "title": "Using Global Constraints and Reranking to Improve Cognates Detection.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Global constraints and reranking have not been used in cognates detection research to date. We propose methods for using global constraints by performing rescoring of the score matrices produced by state of the art cognates detection systems. Using global constraints to perform rescoring is complementary to state of the art methods for performing cognates detection and results in significant performance improvements beyond current state of the art performance on publicly available datasets with different language pairs and various conditions such as different levels of baseline state of the art performance and different data size conditions, including with more realistic large data size conditions than have been evaluated with in the past.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1181"}, {"primary_key": "3716834", "vector": [], "sparse_vector": [], "title": "Learning attention for historical text normalization by learning to pronounce.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated processing of historical texts often relies on pre-normalization to modern word forms. Training encoder-decoder architectures to solve such problems typically requires a lot of training data, which is not available for the named task. We address this problem by using several novel encoder-decoder architectures, including a multi-task learning (MTL) architecture using a grapheme-to-phoneme dictionary as auxiliary data, pushing the state-of-the-art by an absolute 2% increase in performance. We analyze the induced models across 44 different texts from Early New High German. Interestingly, we observe that, as previously conjectured, multi-task learning can learn to focus attention during decoding, in ways remarkably similar to recently proposed attention mechanisms. This, we believe, is an important step toward understanding how MTL works.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1031"}, {"primary_key": "3716835", "vector": [], "sparse_vector": [], "title": "EuroSense: Automatic Harvesting of Multilingual Sense Annotations from Parallel Text.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Parallel corpora are widely used in a variety of Natural Language Processing tasks, from Machine Translation to cross-lingual Word Sense Disambiguation, where parallel sentences can be exploited to automatically generate high-quality sense annotations on a large scale. In this paper we present EuroSense, a multilingual sense-annotated resource based on the joint disambiguation of the Europarl parallel corpus, with almost 123 million sense annotations for over 155 thousand distinct concepts and entities from a language-independent unified sense inventory. We evaluate the quality of our sense annotations intrinsically and extrinsically, showing their effectiveness as training data for Word Sense Disambiguation.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2094"}, {"primary_key": "3716836", "vector": [], "sparse_vector": [], "title": "Cross-lingual and cross-domain discourse segmentation of entire documents.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Discourse segmentation is a crucial step in building end-to-end discourse parsers. However, discourse segmenters only exist for a few languages and domains. Typically they only detect intra-sentential segment boundaries, assuming gold standard sentence and token segmentation, and relying on high-quality syntactic parses and rich heuristics that are not generally available across languages and domains. In this paper, we propose statistical discourse segmenters for five languages and three domains that do not rely on gold pre-annotations. We also consider the problem of learning discourse segmenters when no labeled data is available for a language. Our fully supervised system obtains 89.5% F1 for English newswire, with slight drops in performance on other domains, and we report supervised and unsupervised (cross-lingual) results for five languages in total.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2037"}, {"primary_key": "3716838", "vector": [], "sparse_vector": [], "title": "Automatic Annotation and Evaluation of Error Types for Grammatical Error Correction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Until now, error type performance for Grammatical Error Correction (GEC) systems could only be measured in terms of recall because system output is not annotated. To overcome this problem, we introduce ERRANT, a grammatical ERRor ANnotation Toolkit designed to automatically extract edits from parallel original and corrected sentences and classify them according to a new, dataset-agnostic, rule-based framework. This not only facilitates error type evaluation at different levels of granularity, but can also be used to reduce annotator workload and standardise existing GEC datasets. Human experts rated the automatic edits as “Good” or “Acceptable” in at least 95% of cases, so we applied ERRANT to the system output of the CoNLL-2014 shared task to carry out a detailed error type analysis for the first time.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1074"}, {"primary_key": "3716839", "vector": [], "sparse_vector": [], "title": "Robust Incremental Neural Semantic Graph Parsing.", "authors": ["Jan Buys", "<PERSON>"], "summary": "Parsing sentences to linguistically-expressive semantic representations is a key goal of Natural Language Processing. Yet statistical parsing has focussed almost exclusively on bilexical dependencies or domain-specific logical forms. We propose a neural encoder-decoder transition-based parser which is the first full-coverage semantic graph parser for Minimal Recursion Semantics (MRS). The model architecture uses stack-based embedding features, predicting graphs jointly with unlexicalized predicates and their token alignments. Our parser is more accurate than attention-based baselines on MRS, and on an additional Abstract Meaning Representation (AMR) benchmark, and GPU batch processing makes it an order of magnitude faster than a high-precision grammar-based parser. Further, the 86.69% Smatch score of our MRS parser is higher than the upper-bound on AMR parsing, making MRS an attractive choice as a semantic representation.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1112"}, {"primary_key": "3716840", "vector": [], "sparse_vector": [], "title": "Data-Driven Broad-Coverage Grammars for Opinionated Natural Language Generation (ONLG).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Opinionated Natural Language Generation (ONLG) is a new, challenging, task that aims to automatically generate human-like, subjective, responses to opinionated articles online. We present a data-driven architecture for ONLG that generates subjective responses triggered by users’ agendas, consisting of topics and sentiments, and based on wide-coverage automatically-acquired generative grammars. We compare three types of grammatical representations that we design for ONLG, which interleave different layers of linguistic information and are induced from a new, enriched dataset we developed. Our evaluation shows that generation with Relational-Realizational (Tsar<PERSON><PERSON> and Sima’an, 2008) inspired grammar gets better language model scores than lexicalized grammars ‘a la Collins (2003), and that the latter gets better human-evaluation scores. We also show that conditioning the generation on topic models makes generated responses more relevant to the document content.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1122"}, {"primary_key": "3716841", "vector": [], "sparse_vector": [], "title": "Pay Attention to the Ending: Strong Neural Baselines for the ROC Story Cloze Task.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the ROC story cloze task (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2016) and present several findings. We develop a model that uses hierarchical recurrent networks with attention to encode the sentences in the story and score candidate endings. By discarding the large training set and only training on the validation set, we achieve an accuracy of 74.7%. Even when we discard the story plots (sentences before the ending) and only train to choose the better of two endings, we can still reach 72.5%. We then analyze this \"ending-only\" task setting. We estimate human accuracy to be 78% and find several types of clues that lead to this high accuracy, including those related to sentiment, negation, and general ending likelihood regardless of the story context.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2097"}, {"primary_key": "3716842", "vector": [], "sparse_vector": [], "title": "Fast and Accurate Neural Word Segmentation for Chinese.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Yuan <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Neural models with minimal feature engineering have achieved competitive performance against traditional methods for the task of Chinese word segmentation. However, both training and working procedures of the current neural models are computationally inefficient. In this paper, we propose a greedy neural word segmenter with balanced word and character embedding inputs to alleviate the existing drawbacks. Our segmenter is truly end-to-end, capable of performing segmentation much faster and even more accurate than state-of-the-art neural models on Chinese benchmark datasets.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2096"}, {"primary_key": "3716843", "vector": [], "sparse_vector": [], "title": "Doubly-Attentive Decoder for Multi-modal Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a Multi-modal Neural Machine Translation model in which a doubly-attentive decoder naturally incorporates spatial visual features obtained using pre-trained convolutional neural networks, bridging the gap between image description and translation. Our decoder learns to attend to source-language words and parts of an image independently by means of two separate attention mechanisms as it generates words in the target language. We find that our model can efficiently exploit not just back-translated in-domain multi-modal data but also large general-domain text-only MT corpora. We also report state-of-the-art results on the Multi30k data set.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1175"}, {"primary_key": "3716844", "vector": [], "sparse_vector": [], "title": "Bridge Text and Knowledge by Learning Multi-Prototype Entity Mention Embedding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ng <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Integrating text and knowledge into a unified semantic space has attracted significant research interests recently. However, the ambiguity in the common space remains a challenge, namely that the same mention phrase usually refers to various entities. In this paper, to deal with the ambiguity of entity mentions, we propose a novel Multi-Prototype Mention Embedding model, which learns multiple sense embeddings for each mention by jointly modeling words from textual contexts and entities derived from a knowledge base. In addition, we further design an efficient language model based approach to disambiguate each mention to a specific sense. In experiments, both qualitative and quantitative analysis demonstrate the high quality of the word, entity and multi-prototype mention embeddings. Using entity linking as a study case, we apply our disambiguation method as well as the multi-prototype mention embeddings on the benchmark dataset, and achieve state-of-the-art performance.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1149"}, {"primary_key": "3716845", "vector": [], "sparse_vector": [], "title": "Parsing to 1-Endpoint-Crossing, Pagenumber-2 Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Weiwei Sun", "<PERSON><PERSON>"], "summary": "We study the Maximum Subgraph problem in deep dependency parsing. We consider two restrictions to deep dependency graphs: (a) 1-endpoint-crossing and (b) pagenumber-2. Our main contribution is an exact algorithm that obtains maximum subgraphs satisfying both restrictions simultaneously in time O(n5). Moreover, ignoring one linguistically-rare structure descreases the complexity to O(n4). We also extend our quartic-time algorithm into a practical parser with a discriminative disambiguation model and evaluate its performance on four linguistic data sets used in semantic dependency parsing.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1193"}, {"primary_key": "3716846", "vector": [], "sparse_vector": [], "title": "Computational Characterization of Mental States: A Natural Language Processing Approach.", "authors": ["<PERSON>acund<PERSON>"], "summary": "Psychiatry is an area of medicine that strongly bases its diagnoses on the psychiatrists subjective appreciation.The task of diagnosis loosely resembles the common pipelines used in supervised learning schema.Therefore, we propose to augment the psychiatrists diagnosis toolbox with an artificial intelligence system based on natural language processing and machine learning algorithms.This approach has been validated in many works in which the performance of the diagnosis has been increased with the use of automatic classification.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3001"}, {"primary_key": "3716848", "vector": [], "sparse_vector": [], "title": "Context Sensitive Lemmatization Using Two Successive Bidirectional Gated Recurrent Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a composite deep neural network architecture for supervised and language independent context sensitive lemmatization. The proposed method considers the task as to identify the correct edit tree representing the transformation between a word-lemma pair. To find the lemma of a surface word, we exploit two successive bidirectional gated recurrent structures - the first one is used to extract the character level dependencies and the next one captures the contextual information of the given word. The key advantages of our model compared to the state-of-the-art lemmatizers such as <PERSON><PERSON> and <PERSON><PERSON><PERSON> are - (i) it is independent of human decided features (ii) except the gold lemma, no other expensive morphological attribute is required for joint learning. We evaluate the lemmatizer on nine languages - Bengali, Catalan, Dutch, Hindi, Hungarian, Italian, Latin, Romanian and Spanish. It is found that except Bengali, the proposed method outperforms <PERSON><PERSON> and <PERSON><PERSON><PERSON> on the other languages. To train the model on Bengali, we develop a gold lemma annotated dataset (having 1,702 sentences with a total of 20,257 word tokens), which is an additional contribution of this work.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1136"}, {"primary_key": "3716849", "vector": [], "sparse_vector": [], "title": "Deep Learning for Dialogue Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ür"], "summary": "In the past decade, goal-oriented spoken dialogue systems have been the most prominent component in today's virtual personal assistants. The classic dialogue systems have rather complex and/or modular pipelines. The advance of deep learning technologies has recently risen the applications of neural models to dialogue modeling. However, how to successfully apply deep learning based approaches to a dialogue system is still challenging. Hence, this tutorial is designed to focus on an overview of the dialogue system development while describing most recent research for building dialogue systems and summarizing the challenges, in order to allow researchers to study the potential improvements of the state-of-the-art dialogue systems. The tutorial material is available at http://deepdialogue.miulab.tw.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-5004"}, {"primary_key": "3716850", "vector": [], "sparse_vector": [], "title": "Reading Wikipedia to Answer Open-Domain Questions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper proposes to tackle open-domain question answering using Wikipedia as the unique knowledge source: the answer to any factoid question is a text span in a Wikipedia article. This task of machine reading at scale combines the challenges of document retrieval (finding the relevant articles) with that of machine comprehension of text (identifying the answer spans from those articles). Our approach combines a search component based on bigram hashing and TF-IDF matching with a multi-layer recurrent neural network model trained to detect answers in Wikipedia paragraphs. Our experiments on multiple existing QA datasets indicate that (1) both modules are highly competitive with respect to existing counterparts and (2) multitask learning using distant supervision on their combination is an effective complete system on this challenging task.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1171"}, {"primary_key": "3716851", "vector": [], "sparse_vector": [], "title": "Improved Neural Machine Translation with a Syntax-Aware Encoder and Decoder.", "authors": ["<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most neural machine translation (NMT) models are based on the sequential encoder-decoder framework, which makes no use of syntactic information. In this paper, we improve this model by explicitly incorporating source-side syntactic trees. More specifically, we propose (1) a bidirectional tree encoder which learns both sequential and tree structured representations; (2) a tree-coverage model that lets the attention depend on the source-side syntax. Experiments on Chinese-English translation demonstrate that our proposed models outperform the sequential attentional model as well as a stronger baseline with a bottom-up tree encoder and word coverage.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1177"}, {"primary_key": "3716852", "vector": [], "sparse_vector": [], "title": "A Teacher-Student Framework for Zero-Resource Neural Machine Translation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While end-to-end neural machine translation (NMT) has made remarkable progress recently, it still suffers from the data scarcity problem for low-resource language pairs and domains. In this paper, we propose a method for zero-resource NMT by assuming that parallel sentences have close probabilities of generating a sentence in a third language. Based on the assumption, our method is able to train a source-to-target NMT model (“student”) without parallel corpora available guided by an existing pivot-to-target NMT model (“teacher”) on a source-pivot parallel corpus. Experimental results show that the proposed method significantly improves over a baseline pivot-based model by +3.0 BLEU points across various language pairs.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1176"}, {"primary_key": "3716853", "vector": [], "sparse_vector": [], "title": "Automatically Labeled Data Generation for Large Scale Event Extraction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern models of event extraction for tasks like ACE are based on supervised learning of events from small hand-labeled data. However, hand-labeled training data is expensive to produce, in low coverage of event types, and limited in size, which makes supervised methods hard to extract large scale of events for knowledge base population. To solve the data labeling problem, we propose to automatically label training data for event extraction via world knowledge and linguistic knowledge, which can detect key arguments and trigger words for each event type and employ them to label events in texts automatically. The experimental results show that the quality of our large scale automatically labeled data is competitive with elaborately human-labeled data. And our automatically labeled data can incorporate with human-labeled data, then improve the performance of models learned from these data.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1038"}, {"primary_key": "3716854", "vector": [], "sparse_vector": [], "title": "Improving Native Language Identification by Using Spelling Errors.", "authors": ["Ling<PERSON> Chen", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we explore spelling errors as a source of information for detecting the native language of a writer, a previously under-explored area. We note that character n-grams from misspelled words are very indicative of the native language of the author. In combination with other lexical features, spelling error features lead to 1.2% improvement in accuracy on classifying texts in the TOEFL11 corpus by the author’s native language, compared to systems participating in the NLI shared task.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2086"}, {"primary_key": "3716855", "vector": [], "sparse_vector": [], "title": "Adversarial Multi-Criteria Learning for Chinese Word Segmentation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Different linguistic perspectives causes many diverse segmentation criteria for Chinese word segmentation (CWS). Most existing methods focus on improve the performance for each single criterion. However, it is interesting to exploit these different criteria and mining their common underlying knowledge. In this paper, we propose adversarial multi-criteria learning for CWS by integrating shared knowledge from multiple heterogeneous segmentation criteria. Experiments on eight corpora with heterogeneous segmentation criteria show that the performance of each corpus obtains a significant improvement, compared to single-criterion learning. Source codes of this paper are available on Github.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1110"}, {"primary_key": "3716856", "vector": [], "sparse_vector": [], "title": "Enhanced LSTM for Natural Language Inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reasoning and inference are central to human and artificial intelligence. Modeling inference in human language is very challenging. With the availability of large annotated data (<PERSON> et al., 2015), it has recently become feasible to train neural network based inference models, which have shown to be very effective. In this paper, we present a new state-of-the-art result, achieving the accuracy of 88.6% on the Stanford Natural Language Inference Dataset. Unlike the previous top models that use very complicated network architectures, we first demonstrate that carefully designing sequential inference models based on chain LSTMs can outperform all previous models. Based on this, we further show that by explicitly considering recursive architectures in both local inference modeling and inference composition, we achieve additional improvement. Particularly, incorporating syntactic parsing information contributes to our best result---it further improves the performance even when added to the already very strong model.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1152"}, {"primary_key": "3716857", "vector": [], "sparse_vector": [], "title": "A Generative Parser with a Discriminative Recognition Algorithm.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Generative models defining joint distributions over parse trees and sentences are useful for parsing and language modeling, but impose restrictions on the scope of features and are often outperformed by discriminative models. We propose a framework for parsing and language modeling which marries a generative model with a discriminative recognition model in an encoder-decoder setting. We provide interpretations of the framework based on expectation maximization and variational inference, and show that it enables parsing and language modeling within a single implementation. On the English Penn Treen-bank, our framework obtains competitive performance on constituency parsing while matching the state-of-the-art single-model language modeling score.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2019"}, {"primary_key": "3716858", "vector": [], "sparse_vector": [], "title": "Classifying Temporal Relations by Bidirectional LSTM over Dependency Paths.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Temporal relation classification is becoming an active research field. Lots of methods have been proposed, while most of them focus on extracting features from external resources. Less attention has been paid to a significant advance in a closely related task: relation extraction. In this work, we borrow a state-of-the-art method in relation extraction by adopting bidirectional long short-term memory (Bi-LSTM) along dependency paths (DP). We make a \"common root\" assumption to extend DP representations of cross-sentence links. In the final comparison to two state-of-the-art systems on TimeBank-Dense, our model achieves comparable performance, without using external knowledge, as well as manually annotated attributes of entities (class, tense, polarity, etc.).", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2001"}, {"primary_key": "3716859", "vector": [], "sparse_vector": [], "title": "Learning Structured Natural Language Representations for Semantic Parsing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a neural semantic parser which is interpretable and scalable. Our model converts natural language utterances to intermediate, domain-general natural language representations in the form of predicate-argument structures, which are induced with a transition system and subsequently mapped to target domains. The semantic parser is trained end-to-end using annotated logical forms or their denotations. We achieve the state of the art on SPADES and GRAPHQUESTIONS and obtain competitive results on GEOQUERY and WEBQUESTIONS. The induced predicate-argument structures shed light on the types of representations useful for semantic parsing and how these are different from linguistically motivated ones.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1005"}, {"primary_key": "3716860", "vector": [], "sparse_vector": [], "title": "Coarse-to-Fine Question Answering for Long Documents.", "authors": ["Euns<PERSON> Choi", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1020"}, {"primary_key": "3716861", "vector": [], "sparse_vector": [], "title": "Representations of language in a model of visually grounded speech signal.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a visually grounded model of speech perception which projects spoken utterances and images to a joint semantic space. We use a multi-layer recurrent highway network to model the temporal nature of spoken speech, and show that it learns to extract both form and meaning-based linguistic knowledge from the input signal. We carry out an in-depth analysis of the representations used by different components of the trained model and show that encoding of semantic aspects tends to become richer as we go up the hierarchy of layers, whereas encoding of form-related aspects of the language input tends to initially increase and then plateau or decrease.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1057"}, {"primary_key": "3716862", "vector": [], "sparse_vector": [], "title": "An Empirical Comparison of Domain Adaptation Methods for Neural Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose a novel domain adaptation method named \"mixed fine tuning\" for neural machine translation (NMT). We combine two existing approaches namely fine tuning and multi domain NMT. We first train an NMT model on an out-of-domain parallel corpus, and then fine tune it on a parallel corpus which is a mix of the in-domain and out-of-domain corpora. All corpora are augmented with artificial tags to indicate specific domains. We empirically compare our proposed method against fine tuning and multi domain methods and discuss its benefits and shortcomings.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2061"}, {"primary_key": "3716863", "vector": [], "sparse_vector": [], "title": "Probabilistic Typology: Deep Generative Models of Vowel Inventories.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Linguistic typology studies the range of structures present in human language. The main goal of the field is to discover which sets of possible phenomena are universal, and which are merely frequent. For example, all languages have vowels, while most—but not all—languages have an /u/ sound. In this paper we present the first probabilistic treatment of a basic question in phonological typology: What makes a natural vowel inventory? We introduce a series of deep stochastic point processes, and contrast them with previous computational, simulation-based approaches. We provide a comprehensive suite of experiments on over 200 distinct languages.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1109"}, {"primary_key": "3716864", "vector": [], "sparse_vector": [], "title": "Deep Learning in Semantic Kernel Spaces.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Kernel methods enable the direct usage of structured representations of textual data during language learning and inference tasks. Expressive kernels, such as Tree Kernels, achieve excellent performance in NLP. On the other side, deep neural networks have been demonstrated effective in automatically learning feature representations during training. However, their input is tensor data, i.e., they can not manage rich structured information. In this paper, we show that expressive kernels and deep neural networks can be combined in a common framework in order to (i) explicitly model structured information and (ii) learn non-linear decision functions. We show that the input layer of a deep architecture can be pre-trained through the application of the Nystrom low-rank approximation of kernel spaces. The resulting \"kernelized\" neural network achieves state-of-the-art accuracy in three different tasks.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1032"}, {"primary_key": "3716866", "vector": [], "sparse_vector": [], "title": "Attention-over-Attention Neural Networks for Reading Comprehension.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cloze-style reading comprehension is a representative problem in mining relationship between document and query. In this paper, we present a simple but novel model called attention-over-attention reader for better solving cloze-style reading comprehension task. The proposed model aims to place another attention mechanism over the document-level attention and induces “attended attention” for final answer predictions. One advantage of our model is that it is simpler than related works while giving excellent performance. In addition to the primary model, we also propose an N-best re-ranking strategy to double check the validity of the candidates and further improve the performance. Experimental results show that the proposed methods significantly outperform various state-of-the-art systems by a large margin in public datasets, such as CNN and Children’s Book Test.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1055"}, {"primary_key": "3716867", "vector": [], "sparse_vector": [], "title": "SuperAgent: A Customer Service Chatbot for E-commerce Websites.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Conventional customer service chatbots are usually based on human dialogue, yet significant issues in terms of data scale and privacy.In this paper, we present SuperAgent, a customer service chatbot that leverages large-scale and publicly available ecommerce data.Distinct from existing counterparts, SuperAgent takes advantage of data from in-page product descriptions as well as user-generated content from ecommerce websites, which is more practical and cost-effective when answering repetitive questions, freeing up human support staff to answer much higher value questions.We demonstrate SuperAgent as an add-on extension to mainstream web browsers and show its usefulness to user's online shopping experience.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4017"}, {"primary_key": "3716868", "vector": [], "sparse_vector": [], "title": "On the Challenges of Translating NLP Research into Commercial Products.", "authors": ["<PERSON>"], "summary": "This paper highlights challenges in industrial research related to translating research in natural language processing into commercial products. While the interest in natural language processing from industry is significant, the transfer of research to commercial products is non-trivial and its challenges are often unknown to or underestimated by many researchers. I discuss current obstacles and provide suggestions for increasing the chances for translating research to commercial success based on my experience in industrial research.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2015"}, {"primary_key": "3716869", "vector": [], "sparse_vector": [], "title": "Question Answering on Knowledge Bases and Text using Universal Schema and Memory Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Existing question answering methods infer answers either from a knowledge base or from raw text. While knowledge base (KB) methods are good at answering compositional questions, their performance is often affected by the incompleteness of the KB. Au contraire, web text contains millions of facts that are absent in the KB, however in an unstructured form. Universal schema can support reasoning on the union of both structured KBs and unstructured text by aligning them in a common embedded space. In this paper we extend universal schema to natural language question answering, employing Memory networks to attend to the large body of facts in the combination of text and KB. Our models can be trained in an end-to-end fashion on question-answer pairs. Evaluation results on Spades fill-in-the-blank question answering dataset show that exploiting universal schema for question answering is better than using either a KB or text alone. This model also outperforms the current state-of-the-art by 8.5 F1 points.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2057"}, {"primary_key": "3716870", "vector": [], "sparse_vector": [], "title": "Ontology-Aware Token Embeddings for Prepositional Phrase Attachment.", "authors": ["<PERSON><PERSON><PERSON>", "W<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Type-level word embeddings use the same set of parameters to represent all instances of a word regardless of its context, ignoring the inherent lexical ambiguity in language. Instead, we embed semantic concepts (or synsets) as defined in WordNet and represent a word token in a particular context by estimating a distribution over relevant semantic concepts. We use the new, context-sensitive embeddings in a model for predicting prepositional phrase (PP) attachments and jointly learn the concept embeddings and model parameters. We show that using context-sensitive embeddings improves the accuracy of the PP attachment model by 5.4% absolute points, which amounts to a 34.4% relative reduction in errors.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1191"}, {"primary_key": "3716871", "vector": [], "sparse_vector": [], "title": "Towards End-to-End Reinforcement Learning of Dialogue Agents for Information Access.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1045"}, {"primary_key": "3716872", "vector": [], "sparse_vector": [], "title": "Gated-Attention Readers for Text Comprehension.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we study the problem of answering cloze-style questions over documents. Our model, the Gated-Attention (GA) Reader, integrates a multi-hop architecture with a novel attention mechanism, which is based on multiplicative interactions between the query embedding and the intermediate states of a recurrent neural network document reader. This enables the reader to build query-specific representations of tokens in the document for accurate answer selection. The GA Reader obtains state-of-the-art results on three benchmarks for this task–the CNN & Daily Mail news stories and the Who Did What dataset. The effectiveness of multiplicative interaction is demonstrated by an ablation study, and by comparing to alternative compositional operators for implementing the gated-attention.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1168"}, {"primary_key": "3716873", "vector": [], "sparse_vector": [], "title": "Visualizing and Understanding Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Maosong Sun"], "summary": "While neural machine translation (NMT) has made remarkable progress in recent years, it is hard to interpret its internal workings due to the continuous representations and non-linearity of neural networks. In this work, we propose to use layer-wise relevance propagation (LRP) to compute the contribution of each contextual word to arbitrary hidden states in the attention-based encoder-decoder framework. We show that visualization with LRP helps to interpret the internal workings of NMT and analyze translation errors.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1106"}, {"primary_key": "3716874", "vector": [], "sparse_vector": [], "title": "Alignment at Work: Using Language to Distinguish the Internalization and Self-Regulation Components of Cultural Fit in Organizations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Cultural fit is widely believed to affect the success of individuals and the groups to which they belong. Yet it remains an elusive, poorly measured construct. Recent research draws on computational linguistics to measure cultural fit but overlooks asymmetries in cultural adaptation. By contrast, we develop a directed, dynamic measure of cultural fit based on linguistic alignment, which estimates the influence of one person's word use on another's and distinguishes between two enculturation mechanisms: internalization and self-regulation. We use this measure to trace employees' enculturation trajectories over a large, multi-year corpus of corporate emails and find that patterns of alignment in the first six months of employment are predictive of individuals' downstream outcomes, especially involuntary exit. Further predictive analyses suggest referential alignment plays an overlooked role in linguistic alignment.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1056"}, {"primary_key": "3716875", "vector": [], "sparse_vector": [], "title": "Learning to Ask: Neural Question Generation for Reading Comprehension.", "authors": ["Xinya Du", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study automatic question generation for sentences from text passages in reading comprehension. We introduce an attention-based sequence learning model for the task and investigate the effect of encoding sentence- vs. paragraph-level information. In contrast to all previous work, our model does not rely on hand-crafted rules or a sophisticated NLP pipeline; it is instead trainable end-to-end via sequence-to-sequence learning. Automatic evaluation results show that our system significantly outperforms the state-of-the-art rule-based system. In human evaluations, questions generated by our system are also rated as being more natural (i.e.,, grammaticality, fluency) and as more difficult to answer (in terms of syntactic and lexical divergence from the original text and reasoning needed to answer).", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1123"}, {"primary_key": "3716876", "vector": [], "sparse_vector": [], "title": "Neural End-to-End Learning for Computational Argumentation Mining.", "authors": ["Steffen Eger", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate neural techniques for end-to-end computational argumentation mining (AM). We frame AM both as a token-based dependency parsing and as a token-based sequence tagging problem, including a multi-task learning setup. Contrary to models that operate on the argument component level, we find that framing AM as dependency parsing leads to subpar performance results. In contrast, less complex (local) tagging models based on BiLSTMs perform robustly across classification scenarios, being able to catch long-range dependencies inherent to the AM problem. Moreover, we find that jointly learning 'natural' subtasks, in a multi-task learning setup, improves performance.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1002"}, {"primary_key": "3716877", "vector": [], "sparse_vector": [], "title": "Interactive Visual Analysis of Transcribed Multi-Party Discourse.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present the first web-based Visual Analytics framework for the analysis of multi-party discourse data using verbatim text transcripts.Our framework supports a broad range of server-based processing steps, ranging from data mining and statistical analysis to deep linguistic parsing of English and German.On the client-side, browser-based Visual Analytics components enable multiple perspectives on the analyzed data.These interactive visualizations allow exploratory content analysis, argumentation pattern review and speaker interaction modeling.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4009"}, {"primary_key": "3716878", "vector": [], "sparse_vector": [], "title": "Methodical Evaluation of Arabic Word Embeddings.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many unsupervised learning techniques have been proposed to obtain meaningful representations of words from text. In this study, we evaluate these various techniques when used to generate Arabic word embeddings. We first build a benchmark for the Arabic language that can be utilized to perform intrinsic evaluation of different word embeddings. We then perform additional extrinsic evaluations of the embeddings based on two NLP tasks.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2072"}, {"primary_key": "3716879", "vector": [], "sparse_vector": [], "title": "Learning to Parse and Translate Improves Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "There has been relatively little attention to incorporating linguistic prior to neural machine translation. Much of the previous work was further constrained to considering linguistic prior on the source side. In this paper, we propose a hybrid model, called NMT+RNNG, that learns to parse and translate by combining the recurrent neural network grammar into the attention-based neural machine translation. Our approach encourages the neural machine translation model to incorporate linguistic prior during training, and lets it translate on its own afterward. Extensive experiments with four language pairs show the effectiveness of the proposed NMT+RNNG.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2012"}, {"primary_key": "3716880", "vector": [], "sparse_vector": [], "title": "An Empirical Study on End-to-End Sentence Modelling.", "authors": ["<PERSON>"], "summary": "Accurately representing the meaning of a piece of text, otherwise known as sentence modelling, is an important component in many natural language inference tasks.We survey the spectrum of these methods, which lie along two dimensions: input representation granularity and composition model complexity.Using this framework, we reveal in our quantitative and qualitative experiments the limitations of the current state-of-the-art model in the context of sentence similarity tasks.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3021"}, {"primary_key": "3716881", "vector": [], "sparse_vector": [], "title": "Learning Topic-Sensitive Word Representations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Distributed word representations are widely used for modeling words in NLP tasks. Most of the existing models generate one representation per word and do not consider different meanings of a word. We present two approaches to learn multiple topic-sensitive representations per word by using Hierarchical Dirichlet Process. We observe that by modeling topics and integrating topic distributions for each document we obtain representations that are able to distinguish between different meanings of a given word. Our models yield statistically significant improvements for the lexical substitution task indicating that commonly used single word representations, even when combined with contextual information, are insufficient for this task.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2070"}, {"primary_key": "3716882", "vector": [], "sparse_vector": [], "title": "Data Augmentation for Low-Resource Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The quality of a Neural Machine Translation system depends substantially on the availability of sizable parallel corpora. For low-resource language pairs this is not the case, resulting in poor translation quality. Inspired by work in computer vision, we propose a novel data augmentation approach that targets low-frequency words by generating new sentence pairs containing rare words in new, synthetically created contexts. Experimental results on simulated low-resource settings show that our method improves translation quality by up to 2.9 BLEU points over the baseline and up to 3.2 BLEU over back-translation.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2090"}, {"primary_key": "3716883", "vector": [], "sparse_vector": [], "title": "Semedico: A Comprehensive Semantic Search Engine for the Life Sciences.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "SEMEDICO is a semantic search engine designed to support literature search in the life sciences by integrating the semantics of the domain at all stages of the search process-from query formulation via query processing up to the presentation of results.SEMEDICO excels with an ad-hoc search approach which directly reflects relevance in terms of information density of entities and relations among them (events) and, a truly unique feature, ranks interaction events by certainty information reflecting the degree of factuality of the encountered event.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4016"}, {"primary_key": "3716884", "vector": [], "sparse_vector": [], "title": "Model Transfer for Tagging Low-resource Languages using a Bilingual Dictionary.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Cross-lingual model transfer is a compelling and popular method for predicting annotations in a low-resource language, whereby parallel corpora provide a bridge to a high-resource language, and its associated annotated corpora. However, parallel data is not readily available for many languages, limiting the applicability of these approaches. We address these drawbacks in our framework which takes advantage of cross-lingual word embeddings trained solely on a high coverage dictionary. We propose a novel neural network model for joint training from both sources of data based on cross-lingual word embeddings, and show substantial empirical improvements over baseline techniques. We also propose several active learning heuristics, which result in improvements over competitive benchmark methods.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2093"}, {"primary_key": "3716885", "vector": [], "sparse_vector": [], "title": "Generating Steganographic Text with LSTMs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by concerns for user privacy, we design a steganographic system (\"stegosystem\") that enables two users to exchange encrypted messages without an adversary detecting that such an exchange is taking place.We propose a new linguistic stegosystem based on a Long Short-Term Memory (LSTM) neural network.We demonstrate our approach on the Twitter and Enron email datasets and show that it yields high-quality steganographic text while significantly improving capacity (encrypted bits per word) relative to the state-of-the-art.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3017"}, {"primary_key": "3716886", "vector": [], "sparse_vector": [], "title": "A Full Non-Monotonic Transition System for Unrestricted Non-Projective Parsing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Restricted non-monotonicity has been shown beneficial for the projective arc-eager dependency parser in previous research, as posterior decisions can repair mistakes made in previous states due to the lack of information. In this paper, we propose a novel, fully non-monotonic transition system based on the non-projective Covington algorithm. As a non-monotonic system requires exploration of erroneous actions during the training process, we develop several non-monotonic variants of the recently defined dynamic oracle for the Covington parser, based on tight approximations of the loss. Experiments on datasets from the CoNLL-X and CoNLL-XI shared tasks show that a non-monotonic dynamic oracle outperforms the monotonic version in the majority of languages.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1027"}, {"primary_key": "3716887", "vector": [], "sparse_vector": [], "title": "PositionRank: An Unsupervised Approach to Keyphrase Extraction from Scholarly Documents.", "authors": ["<PERSON><PERSON>", "Cornelia Caragea"], "summary": "The large and growing amounts of online scholarly data present both challenges and opportunities to enhance knowledge discovery. One such challenge is to automatically extract a small set of keyphrases from a document that can accurately describe the document’s content and can facilitate fast information processing. In this paper, we propose PositionRank, an unsupervised model for keyphrase extraction from scholarly documents that incorporates information from all positions of a word’s occurrences into a biased PageRank. Our model obtains remarkable improvements in performance over PageRank models that do not take into account word positions as well as over strong baselines for this task. Specifically, on several datasets of research papers, PositionRank achieves improvements as high as 29.09%.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1102"}, {"primary_key": "3716888", "vector": [], "sparse_vector": [], "title": "Abstract Meaning Representation Parsing using LSTM Recurrent Neural Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present a system which parses sentences into Abstract Meaning Representations, improving state-of-the-art results for this task by more than 5%. AMR graphs represent semantic content using linguistic properties such as semantic roles, coreference, negation, and more. The AMR parser does not rely on a syntactic pre-parse, or heavily engineered features, and uses five recurrent neural networks as the key architectural components for inferring AMR graphs.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1043"}, {"primary_key": "3716889", "vector": [], "sparse_vector": [], "title": "Riemannian Optimization for Skip-Gram Negative Sampling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Skip-Gram Negative Sampling (SGNS) word embedding model, well known by its implementation in \"word2vec\" software, is usually optimized by stochastic gradient descent. However, the optimization of SGNS objective can be viewed as a problem of searching for a good matrix with the low-rank constraint. The most standard way to solve this type of problems is to apply Riemannian optimization framework to optimize the SGNS objective over the manifold of required low-rank matrices. In this paper, we propose an algorithm that optimizes SGNS objective using Riemannian optimization and demonstrates its superiority over popular competitors, such as the original method to train SGNS and SVD over SPPMI matrix.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1185"}, {"primary_key": "3716890", "vector": [], "sparse_vector": [], "title": "Verb Physics: Relative Physical Knowledge of Actions and Objects.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Learning commonsense knowledge from natural language text is nontrivial due to reporting bias: people rarely state the obvious, e.g., \"My house is bigger than me.\" However, while rarely stated explicitly, this trivial everyday knowledge does influence the way people talk about the world, which provides indirect clues to reason about the world. For example, a statement like, \"<PERSON> entered his house\" implies that his house is bigger than <PERSON>. In this paper, we present an approach to infer relative physical knowledge of actions and objects along five dimensions (e.g., size, weight, and strength) from unstructured natural language text. We frame knowledge acquisition as joint inference over two closely related problems: learning (1) relative physical knowledge of object pairs and (2) physical implications of actions when applied to those object pairs. Empirical results demonstrate that it is possible to extract knowledge of actions and objects from language and that joint inference over different types of knowledge improves performance.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1025"}, {"primary_key": "3716891", "vector": [], "sparse_vector": [], "title": "Improving Neural Parsing by Disentangling Model Combination and Reranking Effects.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent work has proposed several generative neural models for constituency parsing that achieve state-of-the-art results. Since direct search in these generative models is difficult, they have primarily been used to rescore candidate outputs from base parsers in which decoding is more straightforward. We first present an algorithm for direct search in these generative models. We then demonstrate that the rescoring results are at least partly due to implicit model combination rather than reranking effects. Finally, we show that explicit model combination can improve performance even further, resulting in new state-of-the-art numbers on the PTB of 94.25 F1 when training only on gold data and 94.66 F1 when using external data.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2025"}, {"primary_key": "3716892", "vector": [], "sparse_vector": [], "title": "Scalable Bayesian Learning of Recurrent Neural Networks for Language Modeling.", "authors": ["<PERSON><PERSON>", "Chunyuan Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Su", "<PERSON>"], "summary": "Recurrent neural networks (RNNs) have shown promising performance for language modeling. However, traditional training of RNNs using back-propagation through time often suffers from overfitting. One reason for this is that stochastic optimization (used for large training sets) does not provide good estimates of model uncertainty. This paper leverages recent advances in stochastic gradient Markov Chain Monte Carlo (also appropriate for large training sets) to learn weight uncertainty in RNNs. It yields a principled Bayesian learning algorithm, adding gradient noise during training (enhancing exploration of the model-parameter space) and model averaging when testing. Extensive experiments on various RNN models and across a broad range of applications demonstrate the superiority of the proposed approach relative to stochastic optimization.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1030"}, {"primary_key": "3716893", "vector": [], "sparse_vector": [], "title": "Accent Adaptation for the Air Traffic Control Domain.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Automated speech recognition (ASR) plays a significant role in training and simulation systems for air traffic controllers.However, because English is the default language used in air traffic control (ATC), ASR systems often encounter difficulty with speakers' non-native accents, for which there is a paucity of data.This paper examines the effects of accent adaptation on the recognition of non-native English speech in the ATC domain.Accent adaptation has been demonstrated to be an effective way to model under-resourced speech, and can be applied to a variety of models.We use Subspace Gaussian Mixture Models (SGMMs) with the Kaldi Speech Recognition Toolkit to adapt acoustic models from American English to German-accented English, and compare it against other adaptation methods.Our results provide additional evidence that SGMMs can be an efficient and effective way to approach this problem, particularly with smaller amounts of accented training data.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3016"}, {"primary_key": "3716894", "vector": [], "sparse_vector": [], "title": "Creating Training Corpora for NLG Micro-Planners.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we present a novel framework for semi-automatically creating linguistically challenging micro-planning data-to-text corpora from existing Knowledge Bases. Because our method pairs data of varying size and shape with texts ranging from simple clauses to short texts, a dataset created using this framework provides a challenging benchmark for microplanning. Another feature of this framework is that it can be applied to any large scale knowledge base and can therefore be used to train and learn KB verbalisers. We apply our framework to DBpedia data and compare the resulting dataset with <PERSON> et al. 2016’s. We show that while <PERSON> et al.’s dataset is more than twice larger than ours, it is less diverse both in terms of input and in terms of text. We thus propose our corpus generation framework as a novel method for creating challenging data sets from which NLG models can be learned which are capable of handling the complex interactions occurring during in micro-planning between lexicalisation, aggregation, surface realisation, referring expression generation and sentence segmentation. To encourage researchers to take up this challenge, we made available a dataset of 21,855 data/text pairs created using this framework in the context of the WebNLG shared task.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1017"}, {"primary_key": "3716895", "vector": [], "sparse_vector": [], "title": "A Convolutional Encoder Model for Neural Machine Translation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Yann <PERSON>"], "summary": "The prevalent approach to neural machine translation relies on bi-directional LSTMs to encode the source sentence. We present a faster and simpler architecture based on a succession of convolutional layers. This allows to encode the source sentence simultaneously compared to recurrent networks for which computation is constrained by temporal dependencies. On WMT’16 English-Romanian translation we achieve competitive accuracy to the state-of-the-art and on WMT’15 English-German we outperform several recently published results. Our models obtain almost the same accuracy as a very deep LSTM setup on WMT’14 English-French translation. We speed up CPU decoding by more than two times at the same or higher accuracy as a strong bi-directional LSTM.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1012"}, {"primary_key": "3716896", "vector": [], "sparse_vector": [], "title": "An Analysis of Action Recognition Datasets for Language and Vision Tasks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A large amount of recent research has focused on tasks that combine language and vision, resulting in a proliferation of datasets and methods. One such task is action recognition, whose applications include image annotation, scene understanding and image retrieval. In this survey, we categorize the existing approaches based on how they conceptualize this problem and provide a detailed review of existing datasets, highlighting their diversity as well as advantages and disadvantages. We focus on recently developed datasets which link visual information with linguistic resources and provide a fine-grained syntactic and semantic analysis of actions in images.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2011"}, {"primary_key": "3716897", "vector": [], "sparse_vector": [], "title": "Hafez: an Interactive Poetry Generation System.", "authors": ["<PERSON><PERSON>", "Xing Shi", "<PERSON>", "<PERSON>"], "summary": "Hafez is an automatic poetry generation system that integrates a Recurrent Neural Network (RNN) with a Finite State Acceptor (FSA).It generates sonnets given arbitrary topics.Furthermore, Hafez enables users to revise and polish generated poems by adjusting various style configurations.Experiments demonstrate that such \"polish\" mechanisms consider the user's intention and lead to a better poem.For evaluation, we build a web interface where users can rate the quality of each poem from 1 to 5 stars.We also speed up the whole system by a factor of 10, via vocabulary pruning and GPU computation, so that adequate feedback can be collected at a fast pace.Based on such feedback, the system learns to adjust its parameters to improve poetry quality.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4008"}, {"primary_key": "3716898", "vector": [], "sparse_vector": [], "title": "Affect-LM: A Neural Language Model for Customizable Affective Text Generation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1059"}, {"primary_key": "3716899", "vector": [], "sparse_vector": [], "title": "Skip-Gram - Zipf + Uniform = Vector Additivity.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In recent years word-embedding models have gained great popularity due to their remarkable performance on several tasks, including word analogy questions and caption generation. An unexpected \"side-effect\" of such models is that their vectors often exhibit compositionality, i.e., addingtwo word-vectors results in a vector that is only a small angle away from the vector of a word representing the semantic composite of the original words, e.g., \"man\" + \"royal\" = \"king\". This work provides a theoretical justification for the presence of additive compositionality in word vectors learned using the Skip-Gram model. In particular, it shows that additive compositionality holds in an even stricter sense (small distance rather than small angle) under certain assumptions on the process generating the corpus. As a corollary, it explains the success of vector calculus in solving word analogies. When these assumptions do not hold, this work describes the correct non-linear composition operator. Finally, this work establishes a connection between the Skip-Gram model and the Sufficient Dimensionality Reduction (SDR) framework of Globerson and Tishby: the parameters of SDR models can be obtained from those of Skip-Gram models simply by adding information on symbol frequencies. This shows that Skip-Gram embeddings are optimal in the sense of Globerson and Tishby and, further, implies that the heuristics commonly used to approximately fit Skip-Gram models can be used to fit SDR models.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1007"}, {"primary_key": "3716900", "vector": [], "sparse_vector": [], "title": "Parser Adaptation for Social Media by Integrating Normalization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This work explores different approaches of using normalization for parser adaptation. Traditionally, normalization is used as separate pre-processing step. We show that integrating the normalization model into the parsing algorithm is more beneficial. This way, multiple normalization candidates can be leveraged, which improves parsing performance on social media. We test this hypothesis by modifying the Berkeley parser; out-of-the-box it achieves an F1 score of 66.52. Our integrated approach reaches a significant improvement with an F1 score of 67.36, while using the best normalization sequence results in an F1 score of only 66.94.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2078"}, {"primary_key": "3716901", "vector": [], "sparse_vector": [], "title": "Differentiable Scheduled Sampling for Credit Assignment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We demonstrate that a continuous relaxation of the argmax operation can be used to create a differentiable approximation to greedy decoding in sequence-to-sequence (seq2seq) models. By incorporating this approximation into the scheduled sampling training procedure–a well-known technique for correcting exposure bias–we introduce a new training objective that is continuous and differentiable everywhere and can provide informative gradients near points where previous decoding decisions change their value. By using a related approximation, we also demonstrate a similar approach to sampled-based training. We show that our approach outperforms both standard cross-entropy training and scheduled sampling procedures in two sequence prediction tasks: named entity recognition and machine translation.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2058"}, {"primary_key": "3716902", "vector": [], "sparse_vector": [], "title": "Vancouver Welcomes You! Minimalist Location Metonymy Resolution.", "authors": ["Milan Gritta", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Named entities are frequently used in a metonymic manner. They serve as references to related entities such as people and organisations. Accurate identification and interpretation of metonymy can be directly beneficial to various NLP applications, such as Named Entity Recognition and Geographical Parsing. Until now, metonymy resolution (MR) methods mainly relied on parsers, taggers, dictionaries, external word lists and other handcrafted lexical resources. We show how a minimalist neural approach combined with a novel predicate window method can achieve competitive results on the SemEval 2007 task on Metonymy Resolution. Additionally, we contribute with a new Wikipedia-based MR dataset called RelocaR, which is tailored towards locations as well as improving previous deficiencies in annotation guidelines.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1115"}, {"primary_key": "3716903", "vector": [], "sparse_vector": [], "title": "Bilingual Word Embeddings with Bucketed CNN for Parallel Sentence Extraction.", "authors": ["<PERSON><PERSON><PERSON>", "Pabitra Mitra"], "summary": "We propose a novel model which can be used to align the sentences of two different languages using neural architectures.First, we train our model to get the bilingual word embeddings and then, we create a similarity matrix between the words of the two sentences.Because of different lengths of the sentences involved, we get a matrix of varying dimension.We dynamically pool the similarity matrix into a matrix of fixed dimension and use Convolutional Neural Network (CNN) to classify the sentences as aligned or not.To further improve upon this technique, we bucket the sentence pairs to be classified into different groups and train CNN's separately.Our approach not only solves sentence alignment problem but our model can be regarded as a generic bag-of-words similarity measure for monolingual or bilingual corpora.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3003"}, {"primary_key": "3716904", "vector": [], "sparse_vector": [], "title": "A Deep Network with Visual Text Composition Behavior.", "authors": ["<PERSON><PERSON>"], "summary": "While natural languages are compositional, how state-of-the-art neural models achieve compositionality is still unclear. We propose a deep network, which not only achieves competitive accuracy for text classification, but also exhibits compositional behavior. That is, while creating hierarchical representations of a piece of text, such as a sentence, the lower layers of the network distribute their layer-specific attention weights to individual words. In contrast, the higher layers compose meaningful phrases and clauses, whose lengths increase as the networks get deeper until fully composing the sentence.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2059"}, {"primary_key": "3716905", "vector": [], "sparse_vector": [], "title": "From Language to Programs: Bridging Reinforcement Learning and Maximum Marginal Likelihood.", "authors": ["<PERSON><PERSON>", "Panupong <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Our goal is to learn a semantic parser that maps natural language utterances into executable programs when only indirect supervision is available: examples are labeled with the correct execution result, but not the program itself. Consequently, we must search the space of programs for those that output the correct result, while not being misled by spurious programs: incorrect programs that coincidentally output the correct result. We connect two common learning paradigms, reinforcement learning (RL) and maximum marginal likelihood (MML), and then present a new learning algorithm that combines the strengths of both. The new algorithm guards against spurious programs by combining the systematic search traditionally employed in MML with the randomized exploration of RL, and by updating parameters such that probability is spread more evenly across consistent programs. We apply our learning algorithm to a new neural semantic parser and show significant gains over existing state-of-the-art results on a recent context-dependent semantic parsing task.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1097"}, {"primary_key": "3716906", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> linking revisited: Accelerating literature-based discovery across domains using a conceptual influence graph.", "authors": ["<PERSON>", "<PERSON>-Escárcega", "<PERSON><PERSON>"], "summary": "We introduce a modular approach for literature-based discovery consisting of a machine reading and knowledge assembly component that together produce a graph of influence relations (e.g., \"A promotes B\") from a collection of publications.A search engine is used to explore direct and indirect influence chains.Query results are substantiated with textual evidence, ranked according to their relevance, and presented in both a table-based view, as well as a network graph visualization.Our approach operates in both domain-specific settings, where there are knowledge bases and ontologies available to guide reading, and in multi-domain settings where such resources are absent.We demonstrate that this deep reading and search system reduces the effort needed to uncover \"undiscovered public knowledge\", and that with the aid of this tool a domain expert was able to drastically reduce her model building time from months to two days.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4018"}, {"primary_key": "3716907", "vector": [], "sparse_vector": [], "title": "An End-to-End Model for Question Answering over Knowledge Base with Cross-Attention Combining Global Knowledge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the rapid growth of knowledge bases (KBs) on the web, how to take full advantage of them becomes increasingly important. Question answering over knowledge base (KB-QA) is one of the promising approaches to access the substantial knowledge. Meanwhile, as the neural network-based (NN-based) methods develop, NN-based KB-QA has already achieved impressive results. However, previous work did not put more emphasis on question representation, and the question is converted into a fixed vector regardless of its candidate answers. This simple representation strategy is not easy to express the proper information in the question. Hence, we present an end-to-end neural network model to represent the questions and their corresponding scores dynamically according to the various candidate answer aspects via cross-attention mechanism. In addition, we leverage the global knowledge inside the underlying KB, aiming at integrating the rich KB information into the representation of the answers. As a result, it could alleviates the out-of-vocabulary (OOV) problem, which helps the cross-attention model to represent the question more precisely. The experimental results on WebQuestions demonstrate the effectiveness of the proposed approach.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1021"}, {"primary_key": "3716908", "vector": [], "sparse_vector": [], "title": "Don&apos;t understand a measure? Learn it: Structured Prediction for Coreference Resolution optimizing its measures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "An interesting aspect of structured prediction is the evaluation of an output structure against the gold standard. Especially in the loss-augmented setting, the need of finding the max-violating constraint has severely limited the expressivity of effective loss functions. In this paper, we trade off exact computation for enabling the use and study of more complex loss functions for coreference resolution. Most interestingly, we show that such functions can be (i) automatically learned also from controversial but commonly accepted coreference measures, e.g., MELA, and (ii) successfully used in learning algorithms. The accurate model comparison on the standard CoNLL-2012 setting shows the benefit of more expressive loss functions.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1094"}, {"primary_key": "3716909", "vector": [], "sparse_vector": [], "title": "Learning Word-Like Units from Joint Audio-Visual Analysis.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Given a collection of images and spoken audio captions, we present a method for discovering word-like acoustic units in the continuous speech signal and grounding them to semantically relevant image regions. For example, our model is able to detect spoken instances of the word ‘lighthouse’ within an utterance and associate them with image regions containing lighthouses. We do not use any form of conventional automatic speech recognition, nor do we use any text transcriptions or conventional linguistic annotations. Our model effectively implements a form of spoken language acquisition, in which the computer learns not only to recognize word categories by sound, but also to enrich the words it learns with semantics by grounding them in images.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1047"}, {"primary_key": "3716910", "vector": [], "sparse_vector": [], "title": "Temporal Orientation of Tweets for Predicting Income of Users.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2104"}, {"primary_key": "3716911", "vector": [], "sparse_vector": [], "title": "Japanese Sentence Compression with a Large Training Dataset.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In English, high-quality sentence compression models by deleting words have been trained on automatically created large training datasets. We work on Japanese sentence compression by a similar approach. To create a large Japanese training dataset, a method of creating English training dataset is modified based on the characteristics of the Japanese language. The created dataset is used to train Japanese sentence compression models based on the recurrent neural network.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2044"}, {"primary_key": "3716912", "vector": [], "sparse_vector": [], "title": "On the Equivalence of Holographic and Complex Embeddings for Link Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show the equivalence of two state-of-the-art models for link prediction/knowledge graph completion: <PERSON><PERSON> et al's holographic embeddings and <PERSON><PERSON><PERSON><PERSON> et al.'s complex embeddings. We first consider a spectral version of the holographic embeddings, exploiting the frequency domain in the Fourier transform for efficient computation. The analysis of the resulting model reveals that it can be viewed as an instance of the complex embeddings with a certain constraint imposed on the initial vectors upon training. Conversely, any set of complex embeddings can be converted to a set of equivalent holographic embeddings.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2088"}, {"primary_key": "3716913", "vector": [], "sparse_vector": [], "title": "Learning Symmetric Collaborative Dialogue Agents with Dynamic Knowledge Graph Embeddings.", "authors": ["He He", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study a symmetric collaborative dialogue setting in which two agents, each with private knowledge, must strategically communicate to achieve a common goal. The open-ended dialogue state in this setting poses new challenges for existing dialogue systems. We collected a dataset of 11K human-human dialogues, which exhibits interesting lexical, semantic, and strategic elements. To model both structured knowledge and unstructured language, we propose a neural model with dynamic knowledge graph embeddings that evolve as the dialogue progresses. Automatic and human evaluations show that our model is both more effective at achieving the goal and more human-like than baseline neural and rule-based models.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1162"}, {"primary_key": "3716914", "vector": [], "sparse_vector": [], "title": "Generating Natural Answers by Incorporating Copying and Retrieving Mechanisms in Sequence-to-Sequence Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Generating answer with natural language sentence is very important in real-world question answering systems, which needs to obtain a right answer as well as a coherent natural response. In this paper, we propose an end-to-end question answering system called COREQA in sequence-to-sequence learning, which incorporates copying and retrieving mechanisms to generate natural answers within an encoder-decoder framework. Specifically, in COREQA, the semantic units (words, phrases and entities) in a natural answer are dynamically predicted from the vocabulary, copied from the given question and/or retrieved from the corresponding knowledge base jointly. Our empirical study on both synthetic and real-world datasets demonstrates the efficiency of COREQA, which is able to generate correct, coherent and natural answers for knowledge inquired questions.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1019"}, {"primary_key": "3716915", "vector": [], "sparse_vector": [], "title": "Deep Semantic Role Labeling: What Works and What&apos;s Next.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a new deep learning model for semantic role labeling (SRL) that significantly improves the state of the art, along with detailed analyses to reveal its strengths and limitations. We use a deep highway BiLSTM architecture with constrained decoding, while observing a number of recent best practices for initialization and regularization. Our 8-layer ensemble model achieves 83.2 F1 on theCoNLL 2005 test set and 83.4 F1 on CoNLL 2012, roughly a 10% relative error reduction over the previous state of the art. Extensive empirical analysis of these gains show that (1) deep models excel at recovering long-distance dependencies but can still make surprisingly obvious errors, and (2) that there is still room for syntactic parsers to improve these results.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1044"}, {"primary_key": "3716916", "vector": [], "sparse_vector": [], "title": "An Unsupervised Neural Attention Model for Aspect Extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Aspect extraction is an important and challenging task in aspect-based sentiment analysis. Existing works tend to apply variants of topic models on this task. While fairly successful, these methods usually do not produce highly coherent aspects. In this paper, we present a novel neural approach with the aim of discovering coherent aspects. The model improves coherence by exploiting the distribution of word co-occurrences through the use of neural word embeddings. Unlike topic models which typically assume independently generated words, word embedding models encourage words that appear in similar contexts to be located close to each other in the embedding space. In addition, we use an attention mechanism to de-emphasize irrelevant words during training, further improving the coherence of aspects. Experimental results on real-life datasets demonstrate that our approach discovers more meaningful and coherent aspects, and substantially outperforms baseline methods on several evaluation tasks.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1036"}, {"primary_key": "3716917", "vector": [], "sparse_vector": [], "title": "Exploring Diachronic Lexical Semantics with JeSemE.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Recent advances in distributional semantics combined with the availability of large-scale diachronic corpora offer new research avenues for the Digital Humanities.JESEME, the Jena Semantic Explorer, renders assistance to a non-technical audience to investigate diachronic semantic topics.JESEME runs as a website with query options and interactive visualizations of results, as well as a REST API for access to the underlying diachronic data sets.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4006"}, {"primary_key": "3716918", "vector": [], "sparse_vector": [], "title": "A Transition-Based Directed Acyclic Graph Parser for UCCA.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present the first parser for UCCA, a cross-linguistically applicable framework for semantic representation, which builds on extensive typological work and supports rapid annotation. UCCA poses a challenge for existing parsing techniques, as it exhibits reentrancy (resulting in DAG structures), discontinuous structures and non-terminal nodes corresponding to complex semantic units. To our knowledge, the conjunction of these formal properties is not supported by any existing parser. Our transition-based parser, which uses a novel transition set and features based on bidirectional LSTMs, has value not just for UCCA parsing: its ability to handle more general graph structures can inform the development of parsers for other semantic DAG structures, and in languages that frequently use discontinuous structures.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1104"}, {"primary_key": "3716919", "vector": [], "sparse_vector": [], "title": "Neural Semantic Parsing over Multiple Knowledge-bases.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A fundamental challenge in developing semantic parsers is the paucity of strong supervision in the form of language utterances annotated with logical form. In this paper, we propose to exploit structural regularities in language in different domains, and train semantic parsers over multiple knowledge-bases (KBs), while sharing information across datasets. We find that we can substantially improve parsing accuracy by training a single sequence-to-sequence model over multiple KBs, when providing an encoding of the domain at decoding time. Our model achieves state-of-the-art performance on the Overnight dataset (containing eight domains), improves performance over a single KB baseline from 75.6% to 79.6%, while obtaining a 7x reduction in the number of model parameters.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2098"}, {"primary_key": "3716920", "vector": [], "sparse_vector": [], "title": "Predicting Depression for Japanese Blog Text.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This study aims to predict clinical depression, a prevalent mental disorder, from blog posts written in Japanese by using machine learning approaches.The study focuses on how data quality and various types of linguistic features (characters, tokens, and lemmas) affect prediction outcome.Depression prediction achieved 95.5% accuracy using selected lemmas as features.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3018"}, {"primary_key": "3716921", "vector": [], "sparse_vector": [], "title": "Oracle Summaries of Compressive Summarization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper derives an Integer Linear Programming (ILP) formulation to obtain an oracle summary of the compressive summarization paradigm in terms of ROUGE. The oracle summary is essential to reveal the upper bound performance of the paradigm. Experimental results on the DUC dataset showed that ROUGE scores of compressive oracles are significantly higher than those of extractive oracles and state-of-the-art summarization systems. These results reveal that compressive summarization is a promising paradigm and encourage us to continue with the research to produce informative summaries.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2043"}, {"primary_key": "3716922", "vector": [], "sparse_vector": [], "title": "Lexically Constrained Decoding for Sequence Generation Using Grid Beam Search.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We present Grid Beam Search (GBS), an algorithm which extends beam search to allow the inclusion of pre-specified lexical constraints. The algorithm can be used with any model which generates sequences token by token. Lexical constraints take the form of phrases or words that must be present in the output sequence. This is a very general way to incorporate auxillary knowledge into a model’s output without requiring any modification of the parameters or training data. We demonstrate the feasibility and flexibility of Lexically Constrained Decoding by conducting experiments on Neural Interactive-Predictive Translation, as well as Domain Adaptation for Neural Machine Translation. Experiments show that GBS can provide large improvements in translation quality in interactive scenarios, and that, even without any user input, GBS can be used to achieve significant gains in performance in domain adaptation scenarios.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1141"}, {"primary_key": "3716923", "vector": [], "sparse_vector": [], "title": "Automatically Generating Rhythmic Verse with Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose two novel methodologies for the automatic generation of rhythmic poetry in a variety of forms. The first approach uses a neural language model trained on a phonetic encoding to learn an implicit representation of both the form and content of English poetry. This model can effectively learn common poetic devices such as rhyme, rhythm and alliteration. The second approach considers poetry generation as a constraint satisfaction problem where a generative neural language model is tasked with learning a representation of content, and a discriminative weighted finite state machine constrains it on the basis of form. By manipulating the constraints of the latter model, we can generate coherent poetry with arbitrary forms and themes. A large-scale extrinsic evaluation demonstrated that participants consider machine-generated poems to be written by humans 54% of the time. In addition, participants rated a machine-generated poem to be the best amongst all evaluated.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1016"}, {"primary_key": "3716924", "vector": [], "sparse_vector": [], "title": "Joint CTC/attention decoding for end-to-end speech recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "End-to-end automatic speech recognition (ASR) has become a popular alternative to conventional DNN/HMM systems because it avoids the need for linguistic resources such as pronunciation dictionary, tokenization, and context-dependency trees, leading to a greatly simplified model-building process. There are two major types of end-to-end architectures for ASR: attention-based methods use an attention mechanism to perform alignment between acoustic frames and recognized symbols, and connectionist temporal classification (CTC), uses Markov assumptions to efficiently solve sequential problems by dynamic programming. This paper proposes joint decoding algorithm for end-to-end ASR with a hybrid CTC/attention architecture, which effectively utilizes both advantages in decoding. We have applied the proposed method to two ASR benchmarks (spontaneous Japanese and Mandarin Chinese), and showing the comparable performance to conventional state-of-the-art DNN/HMM ASR systems without linguistic resources.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1048"}, {"primary_key": "3716927", "vector": [], "sparse_vector": [], "title": "Understanding and Detecting Diverse Supporting Arguments on Controversial Issues.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We investigate the problem of sentence-level supporting argument detection from relevant documents for user-specified claims. A dataset containing claims and associated citation articles is collected from online debate website idebate.org. We then manually label sentence-level supporting arguments from the documents along with their types as study, factual, opinion, or reasoning. We further characterize arguments of different types, and explore whether leveraging type information can facilitate the supporting arguments detection task. Experimental results show that LambdaMART (<PERSON>, 2010) ranker that uses features informed by argument types yields better performance than the same ranker trained without type information.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2032"}, {"primary_key": "3716929", "vector": [], "sparse_vector": [], "title": "Chunk-based Decoder for Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1174"}, {"primary_key": "3716930", "vector": [], "sparse_vector": [], "title": "Learning a Neural Semantic Parser from User Feedback.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1089"}, {"primary_key": "3716931", "vector": [], "sparse_vector": [], "title": "Search-based Neural Structured Learning for Sequential Question Answering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent work in semantic parsing for question answering has focused on long and complicated questions, many of which would seem unnatural if asked in a normal conversation between two humans. In an effort to explore a conversational QA setting, we present a more realistic task: answering sequences of simple but inter-related questions. We collect a dataset of 6,066 question sequences that inquire about semi-structured tables from Wikipedia, with 17,553 question-answer pairs in total. To solve this sequential question answering task, we propose a novel dynamic neural semantic parsing framework trained using a weakly supervised reward-guided search. Our model effectively leverages the sequential context to outperform state-of-the-art QA systems that are designed to answer highly complex questions.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1167"}, {"primary_key": "3716932", "vector": [], "sparse_vector": [], "title": "Evaluating Compound Splitters Extrinsically with Textual Entailment.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditionally, compound splitters are evaluated intrinsically on gold-standard data or extrinsically on the task of statistical machine translation. We explore a novel way for the extrinsic evaluation of compound splitters, namely recognizing textual entailment. Compound splitting has great potential for this novel task that is both transparent and well-defined. Moreover, we show that it addresses certain aspects that are either ignored in intrinsic evaluations or compensated for by taskinternal mechanisms in statistical machine translation. We show significant improvements using different compound splitting methods on a German textual entailment dataset.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2010"}, {"primary_key": "3716933", "vector": [], "sparse_vector": [], "title": "Improving Distributed Representations of Tweets - Present and Future.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Unsupervised representation learning for tweets is an important research field which helps in solving several business applications such as sentiment analysis, hashtag prediction, paraphrase detection and microblog ranking.A good tweet representation learning model must handle the idiosyncratic nature of tweets which poses several challenges such as short length, informal words, unusual grammar and misspellings.However, there is a lack of prior work which surveys the representation learning models with a focus on tweets.In this work, we organize the models based on its objective function which aids the understanding of the literature.We also provide interesting future directions, which we believe are fruitful in advancing this field by building high-quality tweet representation learning models.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3002"}, {"primary_key": "3716934", "vector": [], "sparse_vector": [], "title": "Neural Discourse Structure for Text Categorization.", "authors": ["Yangfeng Ji", "<PERSON>"], "summary": "We show that discourse structure, as defined by Rhetorical Structure Theory and provided by an existing discourse parser, benefits text categorization. Our approach uses a recursive neural network and a newly proposed attention mechanism to compute a representation of the text that focuses on salient content, from the perspective of both RST and the task. Experiments consider variants of the approach and illustrate its strengths and weaknesses.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1092"}, {"primary_key": "3716935", "vector": [], "sparse_vector": [], "title": "A Nested Attention Neural Hybrid Model for Grammatical Error Correction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1070"}, {"primary_key": "3716936", "vector": [], "sparse_vector": [], "title": "Understanding Task Design Trade-offs in Crowdsourced Paraphrase Collection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Linguistically diverse datasets are critical for training and evaluating robust machine learning systems, but data collection is a costly process that often requires experts. Crowdsourcing the process of paraphrase generation is an effective means of expanding natural language datasets, but there has been limited analysis of the trade-offs that arise when designing tasks. In this paper, we present the first systematic study of the key factors in crowdsourcing paraphrase collection. We consider variations in instructions, incentives, data domains, and workflows. We manually analyzed paraphrases for correctness, grammaticality, and linguistic diversity. Our observations provide new insight into the trade-offs between accuracy and diversity in crowd responses that arise as a result of task design, providing guidance for future paraphrase generation procedures.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2017"}, {"primary_key": "3716937", "vector": [], "sparse_vector": [], "title": "Leveraging Behavioral and Social Information for Weakly Supervised Collective Classification of Political Discourse on Twitter.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Framing is a political strategy in which politicians carefully word their statements in order to control public perception of issues. Previous works exploring political framing typically analyze frame usage in longer texts, such as congressional speeches. We present a collection of weakly supervised models which harness collective classification to predict the frames used in political discourse on the microblogging platform, Twitter. Our global probabilistic models show that by combining both lexical features of tweets and network-based behavioral features of Twitter, we are able to increase the average, unsupervised F1 score by 21.52 points over a lexical baseline alone.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1069"}, {"primary_key": "3716938", "vector": [], "sparse_vector": [], "title": "Deep Pyramid Convolutional Neural Networks for Text Categorization.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This paper proposes a low-complexity word-level deep convolutional neural network (CNN) architecture for text categorization that can efficiently represent long-range associations in text. In the literature, several deep and complex neural networks have been proposed for this task, assuming availability of relatively large amounts of training data. However, the associated computational complexity increases as the networks go deeper, which poses serious challenges in practical applications. Moreover, it was shown recently that shallow word-level CNNs are more accurate and much faster than the state-of-the-art very deep nets such as character-level CNNs even in the setting of large training data. Motivated by these findings, we carefully studied deepening of word-level CNNs to capture global representations of text, and found a simple network architecture with which the best accuracy can be obtained by increasing the network depth without increasing computational cost by much. We call it deep pyramid CNN. The proposed model with 15 weight layers outperforms the previous best models on six benchmark datasets for sentiment classification and topic categorization.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1052"}, {"primary_key": "3716939", "vector": [], "sparse_vector": [], "title": "TriviaQA: A Large Scale Distantly Supervised Challenge Dataset for Reading Comprehension.", "authors": ["<PERSON><PERSON>", "Euns<PERSON> Choi", "<PERSON>", "<PERSON>"], "summary": "We present TriviaQA, a challenging reading comprehension dataset containing over 650K question-answer-evidence triples. TriviaQA includes 95K question-answer pairs authored by trivia enthusiasts and independently gathered evidence documents, six per question on average, that provide high quality distant supervision for answering the questions. We show that, in comparison to other recently introduced large-scale datasets, TriviaQA (1) has relatively complex, compositional questions, (2) has considerable syntactic and lexical variability between questions and corresponding answer-evidence sentences, and (3) requires more cross sentence reasoning to find answers. We also present two baseline algorithms: a feature-based classifier and a state-of-the-art neural network, that performs well on SQuAD reading comprehension. Neither approach comes close to human performance (23% and 40% vs. 80%), suggesting that TriviaQA is a challenging testbed that is worth significant future study.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1147"}, {"primary_key": "3716940", "vector": [], "sparse_vector": [], "title": "Incorporating Dialectal Variability for Socially Equitable Language Identification.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Language identification (LID) is a critical first step for processing multilingual text. Yet most LID systems are not designed to handle the linguistic diversity of global platforms like Twitter, where local dialects and rampant code-switching lead language classifiers to systematically miss minority dialect speakers and multilingual speakers. We propose a new dataset and a character-based sequence-to-sequence model for LID designed to support dialectal and multilingual language varieties. Our model achieves state-of-the-art performance on multiple LID benchmarks. Furthermore, in a case study using Twitter for health tracking, our method substantially increases the availability of texts written by underrepresented populations, enabling the development of \"socially inclusive\" NLP tools.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2009"}, {"primary_key": "3716941", "vector": [], "sparse_vector": [], "title": "One-Shot Neural Cross-Lingual Transfer for Paradigm Completion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a novel cross-lingual transfer method for paradigm completion, the task of mapping a lemma to its inflected forms, using a neural encoder-decoder model, the state of the art for the monolingual task. We use labeled data from a high-resource language to increase performance on a low-resource language. In experiments on 21 language pairs from four different language families, we obtain up to 58% higher accuracy than without transfer and show that even zero-shot and one-shot learning are possible. We further find that the degree of language relatedness strongly influences the ability to transfer morphological knowledge.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1182"}, {"primary_key": "3716943", "vector": [], "sparse_vector": [], "title": "Going out on a limb: Joint Extraction of Entity Mentions and Relations without Dependency Trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a novel attention-based recurrent neural network for joint extraction of entity mentions and relations. We show that attention along with long short term memory (LSTM) network can extract semantic relations between entity mentions without having access to dependency trees. Experiments on Automatic Content Extraction (ACE) corpora show that our model significantly outperforms feature-based joint model by <PERSON> and <PERSON> (2014). We also compare our model with an end-to-end tree-based LSTM model (SPTree) by <PERSON><PERSON> and <PERSON> (2016) and show that our model performs within 1% on entity mentions and 2% on relations. Our fine-grained analysis also shows that our model performs significantly better on Agent-Artifact relations, while SPTree performs better on Physical and Part-Whole relations.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1085"}, {"primary_key": "3716944", "vector": [], "sparse_vector": [], "title": "English Multiword Expression-aware Dependency Parsing Including Named Entities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Because syntactic structures and spans of multiword expressions (MWEs) are independently annotated in many English syntactic corpora, they are generally inconsistent with respect to one another, which is harmful to the implementation of an aggregate system. In this work, we construct a corpus that ensures consistency between dependency structures and MWEs, including named entities. Further, we explore models that predict both MWE-spans and an MWE-aware dependency structure. Experimental results show that our joint model using additional MWE-span features achieves an MWE recognition improvement of 1.35 points over a pipeline model.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2068"}, {"primary_key": "3716945", "vector": [], "sparse_vector": [], "title": "Learning to Create and Reuse Words in Open-Vocabulary Neural Language Modeling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fixed-vocabulary language models fail to account for one of the most characteristic statistical facts of natural language: the frequent creation and reuse of new word types. Although character-level language models offer a partial solution in that they can create word types not attested in the training corpus, they do not capture the “bursty” distribution of such words. In this paper, we augment a hierarchical LSTM language model that generates sequences of word tokens character by character with a caching mechanism that learns to reuse previously generated words. To validate our model we construct a new open-vocabulary language modeling corpus (the Multilingual Wikipedia Corpus; MWC) from comparable Wikipedia articles in 7 typologically diverse languages and demonstrate the effectiveness of our model across this range of languages.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1137"}, {"primary_key": "3716946", "vector": [], "sparse_vector": [], "title": "Implicitly-Defined Neural Networks for Sequence Labeling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we propose a novel, implicitly-defined neural network architecture and describe a method to compute its components. The proposed architecture forgoes the causality assumption used to formulate recurrent neural networks and instead couples the hidden states of the network, allowing improvement on problems with complex, long-distance dependencies. Initial experiments demonstrate the new architecture outperforms both the Stanford Parser and baseline bidirectional networks on the Penn Treebank Part-of-Speech tagging task and a baseline bidirectional network on an additional artificial random biased walk task.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2027"}, {"primary_key": "3716947", "vector": [], "sparse_vector": [], "title": "Scattertext: a Browser-Based Tool for Visualizing how Corpora Differ.", "authors": ["<PERSON>"], "summary": "Scattertext is an open source tool for visualizing linguistic variation between document categories in a language-independent way.The tool presents a scatterplot, where each axis corresponds to the rankfrequency a term occurs in a category of documents.Through a tie-breaking strategy, the tool is able to display thousands of visible term-representing points and find space to legibly label hundreds of them.Scattertext also lends itself to a query-based visualization of how the use of terms with similar embeddings differs between document categories, as well as a visualization for comparing the importance scores of bag-of-words features to univariate metrics.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4015"}, {"primary_key": "3716948", "vector": [], "sparse_vector": [], "title": "Answering Complex Questions Using Open Information Extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "While there has been substantial progress in factoid question-answering (QA), answering complex questions remains challenging, typically requiring both a large body of knowledge and inference techniques. Open Information Extraction (Open IE) provides a way to generate semi-structured knowledge for QA, but to date such knowledge has only been used to answer simple questions with retrieval-based methods. We overcome this limitation by presenting a method for reasoning with Open IE knowledge, allowing more complex questions to be handled. Using a recently proposed support graph optimization framework for QA, we develop a new inference model for Open IE, in particular one that can work effectively with multiple short facts, noise, and the relational structure of tuples. Our model significantly outperforms a state-of-the-art structured solver on complex questions of varying difficulty, while also removing the reliance on manually curated knowledge.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2049"}, {"primary_key": "3716949", "vector": [], "sparse_vector": [], "title": "Domain Attention with an Ensemble of Experts.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "An important problem in domain adaptation is to quickly generalize to a new domain with limited supervision given K existing domains. One approach is to retrain a global model across all K + 1 domains using standard techniques, for instance <PERSON><PERSON><PERSON> (2009). However, it is desirable to adapt without having to re-estimate a global model from scratch each time a new domain with potentially new intents and slots is added. We describe a solution based on attending an ensemble of domain experts. We assume K domain specific intent and slot models trained on respective domains. When given domain K + 1, our model uses a weighted combination of the K domain experts’ feedback along with its own opinion to make predictions on the new domain. In experiments, the model significantly outperforms baselines that do not use domain adaptation and also performs better than the full retraining approach.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1060"}, {"primary_key": "3716950", "vector": [], "sparse_vector": [], "title": "Adversarial Adaptation of Synthetic or Stale Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Two types of data shift common in practice are 1. transferring from synthetic data to live user data (a deployment shift), and 2. transferring from stale data to current data (a temporal shift). Both cause a distribution mismatch between training and evaluation, leading to a model that overfits the flawed training data and performs poorly on the test data. We propose a solution to this mismatch problem by framing it as domain adaptation, treating the flawed training dataset as a source domain and the evaluation dataset as a target domain. To this end, we use and build on several recent advances in neural domain adaptation such as adversarial training (Ganinet al., 2016) and domain separation network (<PERSON><PERSON><PERSON><PERSON> et al., 2016), proposing a new effective adversarial training scheme. In both supervised and unsupervised adaptation scenarios, our approach yields clear improvement over strong baselines.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1119"}, {"primary_key": "3716951", "vector": [], "sparse_vector": [], "title": "Demographic Inference on Twitter using Recursive Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "Qiongkai Xu", "<PERSON><PERSON>", "<PERSON>", "Cécile Paris"], "summary": "In social media, demographic inference is a critical task in order to gain a better understanding of a cohort and to facilitate interacting with one’s audience. Most previous work has made independence assumptions over topological, textual and label information on social networks. In this work, we employ recursive neural networks to break down these independence assumptions to obtain inference about demographic characteristics on Twitter. We show that our model performs better than existing models including the state-of-the-art.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2075"}, {"primary_key": "3716952", "vector": [], "sparse_vector": [], "title": "Best-Worst Scaling More Reliable than Rating Scales: A Case Study on Sentiment Intensity Annotation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Rating scales are a widely used method for data annotation; however, they present several challenges, such as difficulty in maintaining inter- and intra-annotator consistency. Best–worst scaling (BWS) is an alternative method of annotation that is claimed to produce high-quality annotations while keeping the required number of annotations similar to that of rating scales. However, the veracity of this claim has never been systematically established. Here for the first time, we set up an experiment that directly compares the rating scale method with BWS. We show that with the same total number of annotations, BWS produces significantly more reliable results than the rating scale.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2074"}, {"primary_key": "3716953", "vector": [], "sparse_vector": [], "title": "Detecting Good Arguments in a Non-Topic-Specific Way: An Oxymoron?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Automatic identification of good arguments on a controversial topic has applications in civics and education, to name a few. While in the civics context it might be acceptable to create separate models for each topic, in the context of scoring of students’ writing there is a preference for a single model that applies to all responses. Given that good arguments for one topic are likely to be irrelevant for another, is a single model for detecting good arguments a contradiction in terms? We investigate the extent to which it is possible to close the performance gap between topic-specific and across-topics models for identification of good arguments.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2038"}, {"primary_key": "3716954", "vector": [], "sparse_vector": [], "title": "OpenNMT: Open-Source Toolkit for Neural Machine Translation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We describe an open-source toolkit for neural machine translation (NMT).The toolkit prioritizes efficiency, modularity, and extensibility with the goal of supporting NMT research into model architectures, feature representations, and source modalities, while maintaining competitive performance and reasonable training requirements.The toolkit consists of modeling and translation support, as well as detailed pedagogical documentation about the underlying techniques.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4012"}, {"primary_key": "3716955", "vector": [], "sparse_vector": [], "title": "Improving Semantic Composition with Offset Inference.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Count-based distributional semantic models suffer from sparsity due to unobserved but plausible co-occurrences in any text collection. This problem is amplified for models like Anchored Packed Trees (APTs), that take the grammatical type of a co-occurrence into account. We therefore introduce a novel form of distributional inference that exploits the rich type structure in APTs and infers missing data by the same mechanism that is used for semantic composition.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2069"}, {"primary_key": "3716956", "vector": [], "sparse_vector": [], "title": "Feature-Rich Networks for Knowledge Base Completion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose jointly modelling Knowledge Bases and aligned text with Feature-Rich Networks. Our models perform Knowledge Base Completion by learning to represent and compose diverse feature types from partially aligned and noisy resources. We perform experiments on Freebase utilizing additional entity type information and syntactic textual relations. Our evaluation suggests that the proposed models can better incorporate side information than previously proposed combinations of bilinear models with convolutional neural networks, showing large improvements when scoring the plausibility of unobserved facts with associated textual mentions.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2051"}, {"primary_key": "3716957", "vector": [], "sparse_vector": [], "title": "Neural AMR: Sequence-to-Sequence Models for Parsing and Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sequence-to-sequence models have shown strong performance across a broad range of applications. However, their application to parsing and generating text using Abstract Meaning Representation (AMR) has been limited, due to the relatively limited amount of labeled data and the non-sequential nature of the AMR graphs. We present a novel training procedure that can lift this limitation using millions of unlabeled sentences and careful preprocessing of the AMR graphs. For AMR parsing, our model achieves competitive results of 62.1 SMATCH, the current best score reported without significant use of external semantic resources. For AMR generation, our model establishes a new state-of-the-art performance of BLEU 33.8. We present extensive ablative and qualitative analysis including strong evidence that sequence-based AMR models are robust against ordering variations of graph-to-sequence conversions.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1014"}, {"primary_key": "3716958", "vector": [], "sparse_vector": [], "title": "Beyond Words: Deep Learning for Multiword Expressions and Collocations.", "authors": ["<PERSON><PERSON>"], "summary": "Deep learning has recently shown much promise for NLP applications. Traditionally, in most NLP approaches, documents or sentences are represented by a sparse bag-of-words representation. There is now a lot of work which goes beyond this by adopting a distributed representation of words, by constructing a so-called ``neural embedding'' or vector space representation of each word or document. The aim of this tutorial is to go beyond the learning of word vectors and present methods for learning vector representations for Multiword Expressions and bilingual phrase pairs, all of which are useful for various NLP applications.This tutorial aims to provide attendees with a clear notion of the linguistic and distributional characteristics of Multiword Expressions (MWEs), their relevance for the intersection of deep learning and natural language processing, what methods and resources are available to support their use, and what more could be done in the future. Our target audience are researchers and practitioners in machine learning, parsing (syntactic and semantic) and language technology, not necessarily experts in MWEs, who are interested in tasks that involve or could benefit from considering MWEs as a pervasive phenomenon in human language and communication.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-5005"}, {"primary_key": "3716959", "vector": [], "sparse_vector": [], "title": "Bandit Structured Prediction for Neural Sequence-to-Sequence Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Bandit structured prediction describes a stochastic optimization framework where learning is performed from partial feedback. This feedback is received in the form of a task loss evaluation to a predicted output structure, without having access to gold standard structures. We advance this framework by lifting linear bandit learning to neural sequence-to-sequence learning problems using attention-based recurrent neural networks. Furthermore, we show how to incorporate control variates into our learning algorithms for variance reduction and improved generalization. We present an evaluation on a neural machine translation task that shows improvements of up to 5.89 BLEU points for domain adaptation from simulated bandit feedback.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1138"}, {"primary_key": "3716960", "vector": [], "sparse_vector": [], "title": "Neural Joint Model for Transition-based Chinese Syntactic Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present neural network-based joint models for Chinese word segmentation, POS tagging and dependency parsing. Our models are the first neural approaches for fully joint Chinese analysis that is known to prevent the error propagation problem of pipeline models. Although word embeddings play a key role in dependency parsing, they cannot be applied directly to the joint task in the previous work. To address this problem, we propose embeddings of character strings, in addition to words. Experiments show that our models outperform existing systems in Chinese word segmentation and POS tagging, and perform preferable accuracies in dependency parsing. We also explore bi-LSTM models with fewer features.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1111"}, {"primary_key": "3716963", "vector": [], "sparse_vector": [], "title": "Topically Driven Neural Language Model.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Language models are typically applied at the sentence level, without access to the broader document context. We present a neural language model that incorporates document context in the form of a topic model-like architecture, thus providing a succinct representation of the broader document context outside of the current sentence. Experiments over a range of datasets demonstrate that our model outperforms a pure sentence-based model in terms of language model perplexity, and leads to topics that are potentially more coherent than those produced by a standard LDA topic model. Our model also has the ability to generate related sentences for a topic, providing another way to interpret topics.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1033"}, {"primary_key": "3716964", "vector": [], "sparse_vector": [], "title": "Variation Autoencoder Based Network Representation Learning for Classification.", "authors": ["Hang Li", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Network representation is the basis of many applications and of extensive interest in various fields, such as information retrieval, social network analysis, and recommendation systems.Most previous methods for network representation only consider the incomplete aspects of a problem, including link structure, node information, and partial integration.The present study introduces a deep network representation model that seamlessly integrates the text information and structure of a network.The model captures highly non-linear relationships between nodes and complex features of a network by exploiting the variational autoencoder (VAE), which is a deep unsupervised generation algorithm.The representation learned with a paragraph vector model is merged with that learned with the VAE to obtain the network representation, which preserves both structure and text information.Comprehensive experiments is conducted on benchmark datasets and find that the introduced model performs better than state-of-the-art techniques.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3010"}, {"primary_key": "3716965", "vector": [], "sparse_vector": [], "title": "Modeling Source Syntax for Neural Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhou"], "summary": "Even though a linguistics-free sequence to sequence model in neural machine translation (NMT) has certain capability of implicitly learning syntactic information of source sentences, this paper shows that source syntax can be explicitly incorporated into NMT effectively to provide further improvements. Specifically, we linearize parse trees of source sentences to obtain structural label sequences. On the basis, we propose three different sorts of encoders to incorporate source syntax into NMT: 1) Parallel RNN encoder that learns word and label annotation vectors parallelly; 2) Hierarchical RNN encoder that learns word and label annotation vectors in a two-level hierarchy; and 3) Mixed RNN encoder that stitchingly learns word and label annotation vectors over sequences where words and labels are mixed. Experimentation on Chinese-to-English translation demonstrates that all the three proposed syntactic encoders are able to improve translation accuracy. It is interesting to note that the simplest RNN encoder, i.e., Mixed RNN encoder yields the best performance with an significant improvement of 1.4 BLEU points. Moreover, an in-depth analysis from several perspectives is provided to reveal how source syntax benefits NMT.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1064"}, {"primary_key": "3716966", "vector": [], "sparse_vector": [], "title": "Neural Symbolic Machines: Learning Semantic Parsers on Freebase with Weak Supervision.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>uo<PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Harnessing the statistical power of neural networks to perform language understanding and symbolic reasoning is difficult, when it requires executing efficient discrete operations against a large knowledge-base. In this work, we introduce a Neural Symbolic Machine, which contains (a) a neural “programmer”, i.e., a sequence-to-sequence model that maps language utterances to programs and utilizes a key-variable memory to handle compositionality (b) a symbolic “computer”, i.e., a Lisp interpreter that performs program execution, and helps find good programs by pruning the search space. We apply REINFORCE to directly optimize the task reward of this structured prediction problem. To train with weak supervision and improve the stability of REINFORCE, we augment it with an iterative maximum-likelihood training process. NSM outperforms the state-of-the-art on the WebQuestionsSP dataset when trained from question-answer pairs only, without requiring any feature engineering or domain-specific knowledge.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1003"}, {"primary_key": "3716967", "vector": [], "sparse_vector": [], "title": "Attention Strategies for Multi-Source Sequence-to-Sequence Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modeling attention in neural multi-source sequence-to-sequence learning remains a relatively unexplored area, despite its usefulness in tasks that incorporate multiple source languages or modalities. We propose two novel approaches to combine the outputs of attention mechanisms over each source sequence, flat and hierarchical. We compare the proposed methods with existing techniques and present results of systematic evaluation of those methods on the WMT16 Multimodal Translation and Automatic Post-editing tasks. We show that the proposed methods achieve competitive results on both tasks.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2031"}, {"primary_key": "3716968", "vector": [], "sparse_vector": [], "title": "MalwareTextDB: A Database for Annotated Malware Articles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Cybersecurity risks and malware threats are becoming increasingly dangerous and common. Despite the severity of the problem, there has been few NLP efforts focused on tackling cybersecurity. In this paper, we discuss the construction of a new database for annotated malware texts. An annotation framework is introduced based on the MAEC vocabulary for defining malware characteristics, along with a database consisting of 39 annotated APT reports with a total of 6,819 sentences. We also use the database to construct models that can potentially help cybersecurity researchers in their data collection and analytics efforts.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1143"}, {"primary_key": "3716969", "vector": [], "sparse_vector": [], "title": "List-only Entity Linking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ng <PERSON>"], "summary": "Traditional Entity Linking (EL) technologies rely on rich structures and properties in the target knowledge base (KB). However, in many applications, the KB may be as simple and sparse as lists of names of the same type (e.g., lists of products). We call it as List-only Entity Linking problem. Fortunately, some mentions may have more cues for linking, which can be used as seed mentions to bridge other mentions and the uninformative entities. In this work, we select most linkable mentions as seed mentions and disambiguate other mentions by comparing them with the seed mentions rather than directly with the entities. Our experiments on linking mentions to seven automatically mined lists show promising results and demonstrate the effectiveness of our approach.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2085"}, {"primary_key": "3716970", "vector": [], "sparse_vector": [], "title": "Neural Relation Extraction with Multi-lingual Attention.", "authors": ["Yankai Lin", "<PERSON><PERSON><PERSON>", "Maosong Sun"], "summary": "Relation extraction has been widely used for finding unknown relational facts from plain text. Most existing methods focus on exploiting mono-lingual data for relation extraction, ignoring massive information from the texts in various languages. To address this issue, we introduce a multi-lingual neural relation extraction framework, which employs mono-lingual attention to utilize the information within mono-lingual texts and further proposes cross-lingual attention to consider the information consistency and complementarity among cross-lingual texts. Experimental results on real-world datasets show that, our model can take advantage of multi-lingual texts and consistently achieve significant improvements on relation extraction as compared with baselines.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1004"}, {"primary_key": "3716972", "vector": [], "sparse_vector": [], "title": "Program Induction by Rationale Generation: Learning to Solve and Explain Algebraic Word Problems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Solving algebraic word problems requires executing a series of arithmetic operations—a program—to obtain a final answer. However, since programs can be arbitrarily complicated, inducing them directly from question-answer pairs is a formidable challenge. To make this task more feasible, we solve these problems by generating answer rationales, sequences of natural language and human-readable mathematical expressions that derive the final answer through a series of small steps. Although rationales do not explicitly specify programs, they provide a scaffolding for their structure via intermediate milestones. To evaluate our approach, we have created a new 100,000-sample dataset of questions, answers and rationales. Experimental results show that indirect supervision of program learning via answer rationales is a promising strategy for inducing arithmetic programs.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1015"}, {"primary_key": "3716973", "vector": [], "sparse_vector": [], "title": "Exploiting Argument Information to Improve Event Detection via Supervised Attention Mechanisms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper tackles the task of event detection (ED), which involves identifying and categorizing events. We argue that arguments provide significant clues to this task, but they are either completely ignored or exploited in an indirect manner in existing detection approaches. In this work, we propose to exploit argument information explicitly for ED via supervised attention mechanisms. In specific, we systematically investigate the proposed model under the supervision of different attention strategies. Experimental results show that our approach advances state-of-the-arts and achieves the best F1 score on ACE 2005 dataset.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1164"}, {"primary_key": "3716974", "vector": [], "sparse_vector": [], "title": "Generating and Exploiting Large-scale Pseudo Training Data for Zero Pronoun Resolution.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Qingyu Yin", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Most existing approaches for zero pronoun resolution are heavily relying on annotated data, which is often released by shared task organizers. Therefore, the lack of annotated data becomes a major obstacle in the progress of zero pronoun resolution task. Also, it is expensive to spend manpower on labeling the data for better performance. To alleviate the problem above, in this paper, we propose a simple but novel approach to automatically generate large-scale pseudo training data for zero pronoun resolution. Furthermore, we successfully transfer the cloze-style reading comprehension neural network model into zero pronoun resolution task and propose a two-step training mechanism to overcome the gap between the pseudo training data and the real one. Experimental results show that the proposed approach significantly outperforms the state-of-the-art systems with an absolute improvements of 3.1% F-score on OntoNotes 5.0 data.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1010"}, {"primary_key": "3716975", "vector": [], "sparse_vector": [], "title": "Learning Character-level Compositionality with Visual Features.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous work has modeled the compositionality of words by creating character-level models of meaning, reducing problems of sparsity for rare words. However, in many writing systems compositionality has an effect even on the character-level: the meaning of a character is derived by the sum of its parts. In this paper, we model this effect by creating embeddings for characters based on their visual characteristics, creating an image for the character and running it through a convolutional neural network to produce a visual character embedding. Experiments on a text classification task demonstrate that such model allows for better processing of instances with rare characters in languages such as Chinese, Japanese, and Korean. Additionally, qualitative analyses demonstrate that our proposed model learns to focus on the parts of characters that carry topical content which resulting in embeddings that are coherent in visual space.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1188"}, {"primary_key": "3716976", "vector": [], "sparse_vector": [], "title": "Adversarial Multi-task Learning for Text Classification.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Neural network models have shown their promising opportunities for multi-task learning, which focus on learning the shared layers to extract the common and task-invariant features. However, in most existing approaches, the extracted shared features are prone to be contaminated by task-specific features or the noise brought by other tasks. In this paper, we propose an adversarial multi-task learning framework, alleviating the shared and private latent feature spaces from interfering with each other. We conduct extensive experiments on 16 different text classification tasks, which demonstrates the benefits of our approach. Besides, we show that the shared knowledge learned by our proposed model can be regarded as off-the-shelf knowledge and easily transferred to new tasks. The datasets of all 16 tasks are publicly available at http://nlp.fudan.edu.cn/data/.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1001"}, {"primary_key": "3716977", "vector": [], "sparse_vector": [], "title": "Disfluency Detection using a Noisy Channel Model and a Deep Neural Language Model.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a model for disfluency detection in spontaneous speech transcripts called LSTM Noisy Channel Model. The model uses a Noisy Channel Model (NCM) to generate n-best candidate disfluency analyses and a Long Short-Term Memory (LSTM) language model to score the underlying fluent sentences of each analysis. The LSTM language model scores, along with other features, are used in a MaxEnt reranker to identify the most plausible analysis. We show that using an LSTM language model in the reranking process of noisy channel disfluency model improves the state-of-the-art in disfluency detection.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2087"}, {"primary_key": "3716978", "vector": [], "sparse_vector": [], "title": "Towards an Automatic Turing Test: Learning to Evaluate Dialogue Responses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1103"}, {"primary_key": "3716979", "vector": [], "sparse_vector": [], "title": "A Neural Architecture for Generating Natural Language Descriptions from Source Code Changes.", "authors": ["<PERSON>", "<PERSON>-Taylor", "<PERSON><PERSON><PERSON>"], "summary": "We propose a model to automatically describe changes introduced in the source code of a program using natural language. Our method receives as input a set of code commits, which contains both the modifications and message introduced by an user. These two modalities are used to train an encoder-decoder architecture. We evaluated our approach on twelve real world open source projects from four different programming languages. Quantitative and qualitative results showed that the proposed approach can generate feasible and semantically sound descriptions not only in standard in-project settings, but also in a cross-project setting.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2045"}, {"primary_key": "3716980", "vector": [], "sparse_vector": [], "title": "Joint Learning for Event Coreference Resolution.", "authors": ["<PERSON>", "<PERSON>"], "summary": "While joint models have been developed for many NLP tasks, the vast majority of event coreference resolvers, including the top-performing resolvers competing in the recent TAC KBP 2016 Event Nugget Detection and Coreference task, are pipeline-based, where the propagation of errors from the trigger detection component to the event coreference component is a major performance limiting factor. To address this problem, we propose a model for jointly learning event coreference, trigger detection, and event anaphoricity. Our joint model is novel in its choice of tasks and its features for capturing cross-task interactions. To our knowledge, this is the first attempt to train a mention-ranking model and employ event anaphoricity for event coreference. Our model achieves the best results to date on the KBP 2016 English and Chinese datasets.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1009"}, {"primary_key": "3716982", "vector": [], "sparse_vector": [], "title": "The Role of Prosody and Speech Register in Word Segmentation: A Computational Modelling Perspective.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ejand<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2028"}, {"primary_key": "3716983", "vector": [], "sparse_vector": [], "title": "Tandem Anchoring: a Multiword Anchor Approach for Interactive Topic Modeling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>"], "summary": "Interactive topic models are powerful tools for those seeking to understand large collections of text. However, existing sampling-based interactive topic modeling approaches scale poorly to large data sets. Anchor methods, which use a single word to uniquely identify a topic, offer the speed needed for interactive work but lack both a mechanism to inject prior knowledge and lack the intuitive semantics needed for user-facing applications. We propose combinations of words as anchors, going beyond existing single word anchor algorithms—an approach we call \"Tandem Anchors\". We begin with a synthetic investigation of this approach then apply the approach to interactive topic modeling in a user study and compare it to interactive and non-interactive approaches. Tandem anchors are faster and more intuitive than existing interactive approaches.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1083"}, {"primary_key": "3716984", "vector": [], "sparse_vector": [], "title": "Learning with Noise: <PERSON><PERSON><PERSON> Distantly Supervised Relation Extraction with Dynamic Transition Matrix.", "authors": ["<PERSON>feng Luo", "Yansong Feng", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1040"}, {"primary_key": "3716985", "vector": [], "sparse_vector": [], "title": "Detect Rumors in Microblog Posts Using Propagation Structure via Kernel Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "How fake news goes viral via social media? How does its propagation pattern differ from real stories? In this paper, we attempt to address the problem of identifying rumors, i.e., fake information, out of microblog posts based on their propagation structure. We firstly model microblog posts diffusion with propagation trees, which provide valuable clues on how an original message is transmitted and developed over time. We then propose a kernel-based method called Propagation Tree Kernel, which captures high-order patterns differentiating different types of rumors by evaluating the similarities between their propagation tree structures. Experimental results on two real-world datasets demonstrate that the proposed kernel-based approach can detect rumors more quickly and accurately than state-of-the-art rumor detection models.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1066"}, {"primary_key": "3716986", "vector": [], "sparse_vector": [], "title": "Group Sparse CNNs for Question Classification with Answer Sets.", "authors": ["Ming<PERSON> Ma", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Question classification is an important task with wide applications. However, traditional techniques treat questions as general sentences, ignoring the corresponding answer data. In order to consider answer information into question modeling, we first introduce novel group sparse autoencoders which refine question representation by utilizing group information in the answer set. We then propose novel group sparse CNNs which naturally learn question representation with respect to their answers by implanting group sparse autoencoders into traditional CNNs. The proposed model significantly outperform strong baselines on four datasets.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2053"}, {"primary_key": "3716987", "vector": [], "sparse_vector": [], "title": "Improving Semantic Relevance for Sequence-to-Sequence Learning of Chinese Social Media Text Summarization.", "authors": ["<PERSON><PERSON> Ma", "<PERSON>", "<PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Current Chinese social media text summarization models are based on an encoder-decoder framework. Although its generated summaries are similar to source texts literally, they have low semantic relevance. In this work, our goal is to improve semantic relevance between source texts and summaries for Chinese social media summarization. We introduce a Semantic Relevance Based neural model to encourage high semantic similarity between texts and summaries. In our model, the source text is represented by a gated attention encoder, while the summary representation is produced by a decoder. Besides, the similarity score between the representations is maximized during training. Our experiments show that the proposed model outperforms baseline systems on a social media corpus.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2100"}, {"primary_key": "3716988", "vector": [], "sparse_vector": [], "title": "Text-based Speaker Identification on Multiparty Dialogues Using Multi-document Convolutional Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We propose a convolutional neural network model for text-based speaker identification on multiparty dialogues extracted from the TV show, <PERSON>.While most previous works on this task rely heavily on acoustic features, our approach attempts to identify speakers in dialogues using their speech patterns as captured by transcriptions to the TV show.It has been shown that different individual speakers exhibit distinct idiolectal styles.Several convolutional neural network models are developed to discriminate between differing speech patterns.Our results confirm the promise of text-based approaches, with the best performing model showing an accuracy improvement of over 6% upon the baseline CNN model.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3009"}, {"primary_key": "3716989", "vector": [], "sparse_vector": [], "title": "Incorporating Uncertainty into Deep Learning for Spoken Language Assessment.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "There is a growing demand for automatic assessment of spoken English proficiency. These systems need to handle large variations in input data owing to the wide range of candidate skill levels and L1s, and errors from ASR. Some candidates will be a poor match to the training data set, undermining the validity of the predicted grade. For high stakes tests it is essential for such systems not only to grade well, but also to provide a measure of their uncertainty in their predictions, enabling rejection to human graders. Previous work examined Gaussian Process (GP) graders which, though successful, do not scale well with large data sets. Deep Neural Network (DNN) may also be used to provide uncertainty using Monte-Carlo Dropout (MCD). This paper proposes a novel method to yield uncertainty and compares it to GPs and DNNs with MCD. The proposed approach explicitly teaches a DNN to have low uncertainty on training data and high uncertainty on generated artificial data. On experiments conducted on data from the Business Language Testing Service (BULATS), the proposed approach is found to outperform GPs and DNNs with MCD in uncertainty-based rejection whilst achieving comparable grading performance.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2008"}, {"primary_key": "3716990", "vector": [], "sparse_vector": [], "title": "Feature Hashing for Language and Dialect Identification.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We evaluate feature hashing for language identification (LID), a method not previously used for this task. Using a standard dataset, we first show that while feature performance is high, LID data is highly dimensional and mostly sparse (>99.5%) as it includes large vocabularies for many languages; memory requirements grow as languages are added. Next we apply hashing using various hash sizes, demonstrating that there is no performance loss with dimensionality reductions of up to 86%. We also show that using an ensemble of low-dimension hash-based classifiers further boosts performance. Feature hashing is highly useful for LID and holds great promise for future work in this area.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2063"}, {"primary_key": "3716991", "vector": [], "sparse_vector": [], "title": "Unsupervised Text Segmentation Based on Native Language Characteristics.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Most work on segmenting text does so on the basis of topic changes, but it can be of interest to segment by other, stylistically expressed characteristics such as change of authorship or native language. We propose a Bayesian unsupervised text segmentation approach to the latter. While baseline models achieve essentially random segmentation on our task, indicating its difficulty, a Bayesian model that incorporates appropriately compact language models and alternating asymmetric priors can achieve scores on the standard metrics around halfway to perfect segmentation.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1134"}, {"primary_key": "3716993", "vector": [], "sparse_vector": [], "title": "Efficient Extraction of Pseudo-Parallel Sentences from Raw Monolingual Data Using Word Embeddings.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a new method for extracting pseudo-parallel sentences from a pair of large monolingual corpora, without relying on any document-level information. Our method first exploits word embeddings in order to efficiently evaluate trillions of candidate sentence pairs and then a classifier to find the most reliable ones. We report significant improvements in domain adaptation for statistical machine translation when using a translation model trained on the sentence pairs extracted from in-domain monolingual corpora.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2062"}, {"primary_key": "3716994", "vector": [], "sparse_vector": [], "title": "Automating Biomedical Evidence Synthesis: RobotReviewer.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present RobotReviewer, an opensource web-based system that uses machine learning and NLP to semi-automate biomedical evidence synthesis, to aid the practice of Evidence-Based Medicine.RobotReviewer processes full-text journal articles (PDFs) describing randomized controlled trials (RCTs).It appraises the reliability of RCTs and extracts text describing key trial characteristics (e.g., descriptions of the population) using novel NLP methods.RobotReviewer then automatically generates a report synthesising this information.Our goal is for RobotReviewer to automatically extract and synthesise the full-range of structured data needed to inform evidence-based practice.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4002"}, {"primary_key": "3716995", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> Parsing of Pre-university Math Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We have been developing an end-to-end math problem solving system that accepts natural language input. The current paper focuses on how we analyze the problem sentences to produce logical forms. We chose a hybrid approach combining a shallow syntactic analyzer and a manually-developed lexicalized grammar. A feature of the grammar is that it is extensively typed on the basis of a formal ontology for pre-university math. These types are helpful in semantic disambiguation inside and across sentences. Experimental results show that the hybrid system produces a well-formed logical form with 88% precision and 56% recall.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1195"}, {"primary_key": "3716996", "vector": [], "sparse_vector": [], "title": "Information-Theory Interpretation of the Skip-Gram Negative-Sampling Objective Function.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we define a measure of dependency between two random variables, based on the Jensen-Shannon (JS) divergence between their joint distribution and the product of their marginal distributions. Then, we show that word2vec’s skip-gram with negative sampling embedding algorithm finds the optimal low-dimensional approximation of this JS dependency measure between the words and their contexts. The gap between the optimal score and the low-dimensional approximation is demonstrated on a standard text corpus.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2026"}, {"primary_key": "3716997", "vector": [], "sparse_vector": [], "title": "Deep Keyphrase Generation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shuguang Han", "Daqing He", "<PERSON>", "<PERSON>"], "summary": "Keyphrase provides highly-summative information that can be effectively used for understanding, organizing and retrieving text content. Though previous studies have provided many workable solutions for automated keyphrase extraction, they commonly divided the to-be-summarized content into multiple text chunks, then ranked and selected the most meaningful ones. These approaches could neither identify keyphrases that do not appear in the text, nor capture the real semantic meaning behind the text. We propose a generative model for keyphrase prediction with an encoder-decoder framework, which can effectively overcome the above drawbacks. We name it as deep keyphrase generation since it attempts to capture the deep semantic meaning of the content with a deep learning method. Empirical analysis on six datasets demonstrates that our proposed model not only achieves a significant performance boost on extracting keyphrases that appear in the source text, but also can generate absent keyphrases based on the semantic meaning of the text. Code and dataset are available at https://github.com/memray/seq2seq-keyphrase.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1054"}, {"primary_key": "3716998", "vector": [], "sparse_vector": [], "title": "Blind Phoneme Segmentation With Temporal Prediction Errors.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Phonemic segmentation of speech is a critical step of speech recognition systems.We propose a novel unsupervised algorithm based on sequence prediction models such as Markov chains and recurrent neural networks.Our approach consists in analyzing the error profile of a model trained to predict speech features frameby-frame.Specifically, we try to learn the dynamics of speech in the MFCC space and hypothesize boundaries from local maxima in the prediction error.We evaluate our system on the TIMIT dataset, with improvements over similar methods.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3011"}, {"primary_key": "3716999", "vector": [], "sparse_vector": [], "title": "Question Answering through Transfer Learning from Large Fine-grained Supervision Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that the task of question answering (QA) can significantly benefit from the transfer learning of models trained on a different large, fine-grained QA dataset. We achieve the state of the art in two well-studied QA datasets, WikiQA and SemEval-2016 (Task 3A), through a basic transfer learning technique from SQuAD. For WikiQA, our model outperforms the previous best model by more than 8%. We demonstrate that finer supervision provides better guidance for learning lexical and syntactic information than coarser supervision, through quantitative results and visual analysis. We also show that a similar transfer learning procedure achieves the state of the art on an entailment task.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2081"}, {"primary_key": "3717000", "vector": [], "sparse_vector": [], "title": "Cardinal Virtues: Extracting Relation Cardinalities from Text.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Information extraction (IE) from text has largely focused on relations between individual entities, such as who has won which award. However, some facts are never fully mentioned, and no IE method has perfect recall. Thus, it is beneficial to also tap contents about the cardinalities of these relations, for example, how many awards someone has won. We introduce this novel problem of extracting cardinalities and discusses the specific challenges that set it apart from standard IE. We present a distant supervision method using conditional random fields. A preliminary evaluation results in precision between 3% and 55%, depending on the difficulty of relations.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2055"}, {"primary_key": "3717001", "vector": [], "sparse_vector": [], "title": "Learning Cognitive Features from Gaze Data for Sentiment and Sarcasm Classification using Convolutional Neural Network.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Cognitive NLP systems- i.e., NLP systems that make use of behavioral data - augment traditional text-based features with cognitive features extracted from eye-movement patterns, EEG signals, brain-imaging etc. Such extraction of features is typically manual. We contend that manual extraction of features may not be the best way to tackle text subtleties that characteristically prevail in complex classification tasks like Sentiment Analysis and Sarcasm Detection, and that even the extraction and choice of features should be delegated to the learning system. We introduce a framework to automatically extract cognitive features from the eye-movement/gaze data of human readers reading the text and use them as features along with textual features for the tasks of sentiment polarity and sarcasm detection. Our proposed framework is based on Convolutional Neural Network (CNN). The CNN learns features from both gaze and text and uses them to classify the input text. We test our technique on published sentiment and sarcasm labeled datasets, enriched with gaze information, to show that using a combination of automatically learned text and gaze features often yields better classification performance over (i) CNN based systems that rely on text input alone and (ii) existing systems that rely on handcrafted gaze and textual features.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1035"}, {"primary_key": "3717002", "vector": [], "sparse_vector": [], "title": "Unifying Text, Metadata, and User Network Representations with a Neural Network for Geolocation Prediction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a novel geolocation prediction model using a complex neural network. Geolocation prediction in social media has attracted many researchers to use information of various types. Our model unifies text, metadata, and user network representations with an attention mechanism to overcome previous ensemble approaches. In an evaluation using two open datasets, the proposed model exhibited a maximum 3.8% increase in accuracy and a maximum of 6.6% increase in accuracy@161 against previous models. We further analyzed several intermediate layers of our model, which revealed that their states capture some statistical characteristics of the datasets.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1116"}, {"primary_key": "3717003", "vector": [], "sparse_vector": [], "title": "Lexical Features in Coreference Resolution: To be Used With Caution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Lexical features are a major source of information in state-of-the-art coreference resolvers. Lexical features implicitly model some of the linguistic phenomena at a fine granularity level. They are especially useful for representing the context of mentions. In this paper we investigate a drawback of using many lexical features in state-of-the-art coreference resolvers. We show that if coreference resolvers mainly rely on lexical features, they can hardly generalize to unseen domains. Furthermore, we show that the current coreference resolution evaluation is clearly flawed by only evaluating on a specific split of a specific dataset in which there is a notable overlap between the training, development and test sets.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2003"}, {"primary_key": "3717004", "vector": [], "sparse_vector": [], "title": "Multimodal Machine Learning: Integrating Language, Vision and Speech.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multimodal machine learning is a vibrant multi-disciplinary research field which addresses some of the original goals of artificial intelligence by integrating and modeling multiple communicative modalities, including linguistic, acoustic and visual messages. With the initial research on audio-visual speech recognition and more recently with image and video captioning projects, this research field brings some unique challenges for multimodal researchers given the heterogeneity of the data and the contingency often found between modalities.This tutorial builds upon a recent course taught at Carnegie Mellon University during the Spring 2016 semester (CMU course 11-777) and two tutorials presented at CVPR 2016 and ICMI 2016. The present tutorial will review fundamental concepts of machine learning and deep neural networks before describing the five main challenges in multimodal machine learning: (1) multimodal representation learning, (2) translation & mapping, (3) modality alignment, (4) multimodal fusion and (5) co-learning. The tutorial will also present state-of-the-art algorithms that were recently proposed to solve multimodal applications such as image captioning, video descriptions and visual question-answer. We will also discuss the current and upcoming challenges.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-5002"}, {"primary_key": "3717005", "vector": [], "sparse_vector": [], "title": "TextFlow: A Text Similarity Measure based on Continuous Sequences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>-<PERSON><PERSON><PERSON>"], "summary": "Text similarity measures are used in multiple tasks such as plagiarism detection, information ranking and recognition of paraphrases and textual entailment. While recent advances in deep learning highlighted the relevance of sequential models in natural language generation, existing similarity measures do not fully exploit the sequential nature of language. Examples of such similarity measures include n-grams and skip-grams overlap which rely on distinct slices of the input texts. In this paper we present a novel text similarity measure inspired from a common representation in DNA sequence alignment algorithms. The new measure, called TextFlow, represents input text pairs as continuous curves and uses both the actual position of the words and sequence matching to compute the similarity value. Our experiments on 8 different datasets show very encouraging results in paraphrase detection, textual entailment recognition and ranking relevance.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1071"}, {"primary_key": "3717006", "vector": [], "sparse_vector": [], "title": "Neural Belief Tracker: Data-Driven Dialogue State Tracking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Tsung<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "One of the core components of modern spoken dialogue systems is the belief tracker, which estimates the user’s goal at every step of the dialogue. However, most current approaches have difficulty scaling to larger, more complex dialogue domains. This is due to their dependency on either: a) Spoken Language Understanding models that require large amounts of annotated training data; or b) hand-crafted lexicons for capturing some of the linguistic variation in users’ language. We propose a novel Neural Belief Tracking (NBT) framework which overcomes these problems by building on recent advances in representation learning. NBT models reason over pre-trained word vectors, learning to compose them into distributed representations of user utterances and dialogue context. Our evaluation on two datasets shows that this approach surpasses past limitations, matching the performance of state-of-the-art models which rely on hand-crafted semantic lexicons and outperforming them when such lexicons are not provided.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1163"}, {"primary_key": "3717007", "vector": [], "sparse_vector": [], "title": "Representing Sentences as Low-Rank Subspaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sentences are important semantic units of natural language. A generic, distributional representation of sentences that can capture the latent semantics is beneficial to multiple downstream applications. We observe a simple geometry of sentences – the word representations of a given sentence (on average 10.23 words in all SemEval datasets with a standard deviation 4.84) roughly lie in a low-rank subspace (roughly, rank 4). Motivated by this observation, we represent a sentence by the low-rank subspace spanned by its word vectors. Such an unsupervised representation is empirically validated via semantic textual similarity tasks on 19 different datasets, where it outperforms the sophisticated neural network models, including skip-thought vectors, by 15% on average.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2099"}, {"primary_key": "3717008", "vector": [], "sparse_vector": [], "title": "Learning to Generate Market Comments from Stock Prices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1126"}, {"primary_key": "3717009", "vector": [], "sparse_vector": [], "title": "Varying Linguistic Purposes of Emoji in (Twitter) Context.", "authors": ["Noa Na&apos;aman", "<PERSON>", "<PERSON>"], "summary": "Early research into emoji in textual communication has focused largely on highfrequency usages and ambiguity of interpretations.Investigation of a wide range of emoji usage shows these glyphs serving at least two very different purposes: as content and function words, or as multimodal affective markers.Identifying where an emoji is replacing textual content allows NLP tools the possibility of parsing them as any other word or phrase.Recognizing the import of non-content emoji can be a a significant part of understanding a message as well.We report on an annotation task on English Twitter data with the goal of classifying emoji uses by these categories, and on the effectiveness of a classifier trained on these annotations.We find that it is possible to train a classifier to tell the difference between those emoji used as linguistic content words and those used as paralinguistic or affective multimodal markers even with a small amount of training data, but that accurate sub-classification of these multimodal emoji into specific classes like attitude, topic, or gesture will require more data and more feature engineering.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3022"}, {"primary_key": "3717011", "vector": [], "sparse_vector": [], "title": "V for Vocab: An Intelligent Flashcard Application.", "authors": ["Nihal V. Nayak", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Students choose to use flashcard applications available on the Internet to help memorize word-meaning pairs.This is helpful for tests such as GRE, TOEFL or IELTS, which emphasize on verbal skills.However, monotonous nature of flashcard applications can be diminished with the help of Cognitive Science through Testing Effect.Experimental evidences have shown that memory tests are an important tool for long term retention (<PERSON><PERSON><PERSON> and <PERSON>, 2006).Based on these evidences, we developed a novel flashcard application called \"V for Vocab\" that implements short answer based tests for learning new words.Furthermore, we aid this by implementing our short answer grading algorithm which automatically scores the user's answer.The algorithm makes use of an alternate thesaurus instead of traditional Wordnet and delivers state-of-theart performance on popular word similarity datasets.We also look to lay the foundation for analysis based on implicit data collected from our application.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3005"}, {"primary_key": "3717012", "vector": [], "sparse_vector": [], "title": "Diversity driven attention model for query-based abstractive summarization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "ive summarization aims to generate a shorter version of the document covering all the salient points in a compact and coherent fashion. On the other hand, query-based summarization highlights those points that are relevant in the context of a given query. The encode-attend-decode paradigm has achieved notable success in machine translation, extractive summarization, dialog systems, etc. But it suffers from the drawback of generation of repeated phrases. In this work we propose a model for the query-based summarization task based on the encode-attend-decode paradigm with two key additions (i) a query attention model (in addition to document attention model) which learns to focus on different portions of the query at different time steps (instead of using a static representation for the query) and (ii) a new diversity based attention model which aims to alleviate the problem of repeating phrases in the summary. In order to enable the testing of this model we introduce a new query-based summarization dataset building on debatepedia. Our experiments show that with these two additions the proposed model clearly outperforms vanilla encode-attend-decode models with a gain of 28% (absolute) in ROUGE-L scores.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1098"}, {"primary_key": "3717013", "vector": [], "sparse_vector": [], "title": "Olelo: A Question Answering Application for Biomedicine.", "authors": ["<PERSON>. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Despite the importance of the biomedical domain, there are few reliable applications to support researchers and physicians for retrieving particular facts that fit their needs.Users typically rely on search engines that only support keywordand filter-based searches.We present <PERSON>lo, a question answering system for biomedicine.Olelo is built on top of an inmemory database, integrates domain resources, such as document collections and terminologies, and uses various natural language processing components.Olelo is fast, intuitive and easy to use.We evaluated the systems on two use cases: answering questions related to a particular gene and on the BioASQ benchmark.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4011"}, {"primary_key": "3717014", "vector": [], "sparse_vector": [], "title": "A Neural Local Coherence Model.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a local coherence model based on a convolutional neural network that operates over the entity grid representation of a text. The model captures long range entity transitions along with entity-specific features without loosing generalization, thanks to the power of distributed representation. We present a pairwise ranking method to train the model in an end-to-end fashion on a task and learn task-specific high level features. Our evaluation on three different coherence assessment tasks demonstrates that our model achieves state of the art results outperforming existing models by a good margin.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1121"}, {"primary_key": "3717015", "vector": [], "sparse_vector": [], "title": "Extended Named Entity Recognition API and Its Applications in Language Education.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Thai-<PERSON><PERSON>", "<PERSON>", "Truc-V<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an Extended Named Entity Recognition API to recognize various types of entities and classify the entities into 200 different categories.Each entity is classified into a hierarchy of entity categories, in which the categories near the root are more general than the categories near the leaves of the hierarchy.This category information can be used in various applications such as language educational applications, online news services and recommendation engines.We show an application of the API in a Japanese online news service for Japanese language learners.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4007"}, {"primary_key": "3717016", "vector": [], "sparse_vector": [], "title": "Aggregating and Predicting Sequence Labels from Crowd Annotations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Despite sequences being core to NLP, scant work has considered how to handle noisy sequence labels from multiple annotators for the same text. Given such annotations, we consider two complementary tasks: (1) aggregating sequential crowd labels to infer a best single set of consensus annotations; and (2) using crowd annotations as training data for a model that can predict sequences in unannotated text. For aggregation, we propose a novel Hidden Markov Model variant. To predict sequences in unannotated text, we propose a neural approach using Long Short Term Memory. We evaluate a suite of methods across two different applications and text genres: Named-Entity Recognition in news articles and Information Extraction from biomedical abstracts. Results show improvement over strong baselines. Our source code and data are available online.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1028"}, {"primary_key": "3717017", "vector": [], "sparse_vector": [], "title": "Weakly Supervised Cross-Lingual Named Entity Recognition via Effective Annotation and Representation Projection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The state-of-the-art named entity recognition (NER) systems are supervised machine learning models that require large amounts of manually annotated data to achieve high accuracy. However, annotating NER data by human is expensive and time-consuming, and can be quite difficult for a new language. In this paper, we present two weakly supervised approaches for cross-lingual NER with no human annotation in a target language. The first approach is to create automatically labeled NER data for a target language via annotation projection on comparable corpora, where we develop a heuristic scheme that effectively selects good-quality projection-labeled data from noisy data. The second approach is to project distributed representations of words (word embeddings) from a target language to a source language, so that the source-language NER system can be applied to the target language without re-training. We also design two co-decoding schemes that effectively combine the outputs of the two projection-based approaches. We evaluate the performance of the proposed approaches on both in-house and open NER data for several target languages. The results show that the combined systems outperform three other weakly supervised approaches on the CoNLL data.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1135"}, {"primary_key": "3717018", "vector": [], "sparse_vector": [], "title": "Argument Mining with Structured SVMs and RNNs.", "authors": ["<PERSON>", "Joonsuk Park", "<PERSON>"], "summary": "We propose a novel factor graph model for argument mining, designed for settings in which the argumentative relations in a document do not necessarily form a tree structure. (This is the case in over 20% of the web comments dataset we release.) Our model jointly learns elementary unit type classification and argumentative relation prediction. Moreover, our model supports SVM and RNN parametrizations, can enforce structure constraints (e.g., transitivity), and can express dependencies between adjacent relations and propositions. Our approaches outperform unstructured baselines in both web comments and argumentative essay datasets.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1091"}, {"primary_key": "3717019", "vector": [], "sparse_vector": [], "title": "Exploring Neural Text Simplification Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present the first attempt at using sequence to sequence neural networks to model text simplification (TS). Unlike the previously proposed automated TS systems, our neural text simplification (NTS) systems are able to simultaneously perform lexical simplification and content reduction. An extensive human evaluation of the output has shown that NTS systems achieve almost perfect grammaticality and meaning preservation of output sentences and higher level of simplification than the state-of-the-art automated TS systems", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2014"}, {"primary_key": "3717020", "vector": [], "sparse_vector": [], "title": "Improved Word Representation Learning with Sememes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Maosong Sun"], "summary": "Sememes are minimum semantic units of word meanings, and the meaning of each word sense is typically composed by several sememes. Since sememes are not explicit for each word, people manually annotate word sememes and form linguistic common-sense knowledge bases. In this paper, we present that, word sememe information can improve word representation learning (WRL), which maps words into a low-dimensional semantic space and serves as a fundamental step for many NLP tasks. The key idea is to utilize word sememes to capture exact meanings of a word within specific contexts accurately. More specifically, we follow the framework of Skip-gram and present three sememe-encoded models to learn representations of sememes, senses and words, where we apply the attention scheme to detect word senses in various contexts. We conduct experiments on two tasks including word similarity and word analogy, and our models significantly outperform baselines. The results indicate that WRL can benefit from sememes via the attention scheme, and also confirm our models being capable of correctly modeling sememe information.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1187"}, {"primary_key": "3717022", "vector": [], "sparse_vector": [], "title": "Neural Machine Translation via Binary Code Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose a new method for calculating the output layer in neural machine translation systems. The method is based on predicting a binary code for each word and can reduce computation time/memory requirements of the output layer to be logarithmic in vocabulary size in the best case. In addition, we also introduce two advanced approaches to improve the robustness of the proposed model: using error-correcting codes and combining softmax and binary codes. Experiments on two English-Japanese bidirectional translation tasks show proposed models achieve BLEU scores that approach the softmax, while reducing memory usage to the order of less than 1/10 and improving decoding speed on CPUs by x5 to x10.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1079"}, {"primary_key": "3717023", "vector": [], "sparse_vector": [], "title": "Neural Modeling of Multi-Predicate Interactions for Japanese Predicate Argument Structure Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The performance of Japanese predicate argument structure (PAS) analysis has improved in recent years thanks to the joint modeling of interactions between multiple predicates. However, this approach relies heavily on syntactic information predicted by parsers, and suffers from errorpropagation. To remedy this problem, we introduce a model that uses grid-type recurrent neural networks. The proposed model automatically induces features sensitive to multi-predicate interactions from the word sequence information of a sentence. Experiments on the NAIST Text Corpus demonstrate that without syntactic information, our model outperforms previous syntax-dependent models.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1146"}, {"primary_key": "3717024", "vector": [], "sparse_vector": [], "title": "Prerequisite Relation Learning for Concepts in MOOCs.", "authors": ["<PERSON><PERSON> Pan", "Chengjiang Li", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "What prerequisite knowledge should students achieve a level of mastery before moving forward to learn subsequent coursewares? We study the extent to which the prerequisite relation between knowledge concepts in Massive Open Online Courses (MOOCs) can be inferred automatically. In particular, what kinds of information can be leverage to uncover the potential prerequisite relation between knowledge concepts. We first propose a representation learning-based method for learning latent representations of course concepts, and then investigate how different features capture the prerequisite relations between concepts. Our experiments on three datasets form Coursera show that the proposed method achieves significant improvements (+5.9-48.0% by F1-score) comparing with existing methods.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1133"}, {"primary_key": "3717025", "vector": [], "sparse_vector": [], "title": "Cross-lingual Name Tagging and Linking for 282 Languages.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>ng <PERSON>"], "summary": "The ambitious goal of this work is to develop a cross-lingual name tagging and linking framework for 282 languages that exist in Wikipedia. Given a document in any of these languages, our framework is able to identify name mentions, assign a coarse-grained or fine-grained type to each mention, and link it to an English Knowledge Base (KB) if it is linkable. We achieve this goal by performing a series of new KB mining methods: generating “silver-standard” annotations by transferring annotations from English to other languages through cross-lingual links and KB properties, refining annotations through self-training and topic selection, deriving language-specific morphology features from anchor links, and mining word translation pairs from cross-lingual links. Both name tagging and linking results for 282 languages are promising on Wikipedia data and on-Wikipedia data.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1178"}, {"primary_key": "3717026", "vector": [], "sparse_vector": [], "title": "Multi-Task Video Captioning with Video and Entailment Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Video captioning, the task of describing the content of a video, has seen some promising improvements in recent years with sequence-to-sequence models, but accurately learning the temporal and logical dynamics involved in the task still remains a challenge, especially given the lack of sufficient annotated data. We improve video captioning by sharing knowledge with two related directed-generation tasks: a temporally-directed unsupervised video prediction task to learn richer context-aware video encoder representations, and a logically-directed language entailment generation task to learn better video-entailing caption decoder representations. For this, we present a many-to-many multi-task learning model that shares parameters across the encoders and decoders of the three tasks. We achieve significant improvements and the new state-of-the-art on several standard video captioning datasets using diverse automatic and human evaluations. We also show mutual multi-task improvements on the entailment generation task.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1117"}, {"primary_key": "3717027", "vector": [], "sparse_vector": [], "title": "A Multidimensional Lexicon for Interpersonal Stancetaking.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The sociolinguistic construct of stancetaking describes the activities through which discourse participants create and signal relationships to their interlocutors, to the topic of discussion, and to the talk itself. <PERSON><PERSON><PERSON> underlies a wide range of interactional phenomena, relating to formality, politeness, affect, and subjectivity. We present a computational approach to stancetaking, in which we build a theoretically-motivated lexicon of stance markers, and then use multidimensional analysis to identify a set of underlying stance dimensions. We validate these dimensions intrinscially and extrinsically, showing that they are internally coherent, match pre-registered hypotheses, and correlate with social phenomena.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1082"}, {"primary_key": "3717028", "vector": [], "sparse_vector": [], "title": "Identifying 1950s American Jazz Musicians: Fine-Grained IsA Extraction via Modifier Composition.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present a method for populating fine-grained classes (e.g., “1950s American jazz musicians”) with instances (e.g., <PERSON> ). While state-of-the-art methods tend to treat class labels as single lexical units, the proposed method considers each of the individual modifiers in the class label relative to the head. An evaluation on the task of reconstructing Wikipedia category pages demonstrates a >10 point increase in AUC, over a strong baseline relying on widely-used Hearst patterns.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1192"}, {"primary_key": "3717029", "vector": [], "sparse_vector": [], "title": "Sarcasm SIGN: Interpreting Sarcasm with Sentiment Based Monolingual Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sarcasm is a form of speech in which speakers say the opposite of what they truly mean in order to convey a strong sentiment. In other words, \"Sarcasm is the giant chasm between what I say, and the person who doesn't get it.\". In this paper we present the novel task of sarcasm interpretation, defined as the generation of a non-sarcastic utterance conveying the same message as the original sarcastic one. We introduce a novel dataset of 3000 sarcastic tweets, each interpreted by five human judges. Addressing the task as monolingual machine translation (MT), we experiment with MT algorithms and evaluation measures. We then present SIGN: an MT based sarcasm interpretation algorithm that targets sentiment words, a defining element of textual sarcasm. We show that while the scores of n-gram based automatic measures are similar for all interpretation models, SIGN's interpretations are scored higher by humans for adequacy and sentiment polarity. We conclude with a discussion on future research directions for our new task.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1155"}, {"primary_key": "3717030", "vector": [], "sparse_vector": [], "title": "Deep Multitask Learning for Semantic Dependency Parsing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a deep neural architecture that parses sentences into three semantic dependency graph formalisms. By using efficient, nearly arc-factored inference and a bidirectional-LSTM composed with a multi-layer perceptron, our base system is able to significantly improve the state of the art for semantic dependency parsing, without using hand-engineered features or syntax. We then explore two multitask learning approaches—one that shares parameters across formalisms, and one that uses higher-order structures to predict the graphs jointly. We find that both approaches improve performance across formalisms on average, achieving a new state of the art. Our code is open-source and available at https://github.com/Noahs-ARK/NeurboParser.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1186"}, {"primary_key": "3717031", "vector": [], "sparse_vector": [], "title": "Understanding and Predicting Empathic Behavior in Counseling Therapy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Lawrence <PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1131"}, {"primary_key": "3717032", "vector": [], "sparse_vector": [], "title": "Semi-supervised sequence tagging with bidirectional language models.", "authors": ["<PERSON>", "W<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Pre-trained word embeddings learned from unlabeled text have become a standard component of neural network architectures for NLP tasks. However, in most cases, the recurrent network that operates on word-level representations to produce context sensitive representations is trained on relatively little labeled data. In this paper, we demonstrate a general semi-supervised approach for adding pretrained context embeddings from bidirectional language models to NLP systems and apply it to sequence labeling tasks. We evaluate our model on two standard datasets for named entity recognition (NER) and chunking, and in both cases achieve state of the art results, surpassing previous systems that use other forms of transfer or joint learning with additional labeled data and task specific gazetteers.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1161"}, {"primary_key": "3717033", "vector": [], "sparse_vector": [], "title": "Supervised Learning of Automatic Pyramid for Optimization-Based Multi-Document Summarization.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present a new supervised framework that learns to estimate automatic Pyramid scores and uses them for optimization-based extractive multi-document summarization. For learning automatic Pyramid scores, we developed a method for automatic training data generation which is based on a genetic algorithm using automatic Pyramid as the fitness function. Our experimental evaluation shows that our new framework significantly outperforms strong baselines regarding automatic Pyramid, and that there is much room for improvement in comparison with the upper-bound for automatic Pyramid.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1100"}, {"primary_key": "3717034", "vector": [], "sparse_vector": [], "title": "A Principled Framework for Evaluating Summarizers: Comparing Models of Summary Quality against Human Judgments.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present a new framework for evaluating extractive summarizers, which is based on a principled representation as optimization problem. We prove that every extractive summarizer can be decomposed into an objective function and an optimization technique. We perform a comparative analysis and evaluation of several objective functions embedded in well-known summarizers regarding their correlation with human judgments. Our comparison of these correlations across two datasets yields surprising insights into the role and performance of objective functions in the different summarizers.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2005"}, {"primary_key": "3717035", "vector": [], "sparse_vector": [], "title": "Towards a Seamless Integration of Word Senses into Downstream NLP Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Lexical ambiguity can impede NLP systems from accurate understanding of semantics. Despite its potential benefits, the integration of sense-level information into NLP systems has remained understudied. By incorporating a novel disambiguation algorithm into a state-of-the-art classification model, we create a pipeline to integrate sense-level information into downstream NLP applications. We show that a simple disambiguation of the input text can lead to consistent performance improvement on multiple topic categorization and polarity detection datasets, particularly when the fine granularity of the underlying sense inventory is reduced and the document is sufficiently large. Our results also point to the need for sense representation research to focus more on in vivo evaluations which target the performance in downstream NLP applications rather than artificial benchmarks.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1170"}, {"primary_key": "3717037", "vector": [], "sparse_vector": [], "title": "NLP for Precision Medicine.", "authors": ["Hoifung Poon", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We will introduce precision medicine and showcase the vast opportunities for NLP in this burgeoning field with great societal impact. We will review pressing NLP problems, state-of-the art methods, and important applications, as well as datasets, medical resources, and practical issues. The tutorial will provide an accessible overview of biomedicine, and does not presume knowledge in biology or healthcare. The ultimate goal is to reduce the entry barrier for NLP researchers to contribute to this exciting domain.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-5001"}, {"primary_key": "3717038", "vector": [], "sparse_vector": [], "title": "Context-Dependent Sentiment Analysis in User-Generated Videos.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1081"}, {"primary_key": "3717039", "vector": [], "sparse_vector": [], "title": "Beyond Binary Labels: Political Ideology Prediction of Twitter Users.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automatic political orientation prediction from social media posts has to date proven successful only in distinguishing between publicly declared liberals and conservatives in the US. This study examines users' political ideology using a seven-point scale which enables us to identify politically moderate and neutral users – groups which are of particular interest to political scientists and pollsters. Using a novel data set with political ideology labels self-reported through surveys, our goal is two-fold: a) to characterize the groups of politically engaged users through language use on Twitter; b) to build a fine-grained model that predicts political ideology of unseen users. Our results identify differences in both political leaning and engagement and the extent to which each group tweets using political keywords. Finally, we demonstrate how to improve ideology prediction accuracy by exploiting the relationships between the user groups.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1068"}, {"primary_key": "3717040", "vector": [], "sparse_vector": [], "title": "Vector space models for evaluating semantic fluency in autism.", "authors": ["Emily <PERSON>&apo<PERSON>;hommeaux", "<PERSON>", "Douglas Gliner"], "summary": "A common test administered during neurological examination is the semantic fluency test, in which the patient must list as many examples of a given semantic category as possible under timed conditions. Poor performance is associated with neurological conditions characterized by impairments in executive function, such as dementia, schizophrenia, and autism spectrum disorder (ASD). Methods for analyzing semantic fluency responses at the level of detail necessary to uncover these differences have typically relied on subjective manual annotation. In this paper, we explore automated approaches for scoring semantic fluency responses that leverage ontological resources and distributional semantic models to characterize the semantic fluency responses produced by young children with and without ASD. Using these methods, we find significant differences in the semantic fluency responses of children with ASD, demonstrating the utility of using objective methods for clinical language analysis.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2006"}, {"primary_key": "3717041", "vector": [], "sparse_vector": [], "title": "Arc-swift: A Novel Transition System for Dependency Parsing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Transition-based dependency parsers often need sequences of local shift and reduce operations to produce certain attachments. Correct individual decisions hence require global information about the sentence context and mistakes cause error propagation. This paper proposes a novel transition system, arc-swift, that enables direct attachments between tokens farther apart with a single transition. This allows the parser to leverage lexical information more directly in transition decisions. Hence, arc-swift can achieve significantly better performance with a very small beam size. Our parsers reduce error by 3.7–7.6% relative to those using existing transition systems on the Penn Treebank dependency parsing task and English Universal Dependencies.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2018"}, {"primary_key": "3717042", "vector": [], "sparse_vector": [], "title": "Linguistically Regularized LSTM for Sentiment Classification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper deals with sentence-level sentiment classification. Though a variety of neural network models have been proposed recently, however, previous models either depend on expensive phrase-level annotation, most of which has remarkably degraded performance when trained with only sentence-level annotation; or do not fully employ linguistic resources (e.g., sentiment lexicons, negation words, intensity words). In this paper, we propose simple models trained with sentence-level annotation, but also attempt to model the linguistic role of sentiment lexicons, negation words, and intensity words. Results show that our models are able to capture the linguistic role of sentiment words, negation words, and intensity words in sentiment expression.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1154"}, {"primary_key": "3717043", "vector": [], "sparse_vector": [], "title": "Joint Modeling of Content and Discourse Relations in Dialogues.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a joint modeling approach to identify salient discussion points in spoken meetings as well as to label the discourse relations between speaker turns. A variation of our model is also discussed when discourse relations are treated as latent variables. Experimental results on two popular meeting corpora show that our joint model can outperform state-of-the-art approaches for both phrase-based content selection and discourse relation prediction tasks. We also evaluate our model on predicting the consistency among team members’ understanding of their group decisions. Classifiers trained with features constructed from our model achieve significant better predictive performance than the state-of-the-art.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1090"}, {"primary_key": "3717044", "vector": [], "sparse_vector": [], "title": "Adversarial Connective-exploiting Networks for Implicit Discourse Relation Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Implicit discourse relation classification is of great challenge due to the lack of connectives as strong linguistic cues, which motivates the use of annotated implicit connectives to improve the recognition. We propose a feature imitation framework in which an implicit relation network is driven to learn from another neural network with access to connectives, and thus encouraged to extract similarly salient features for accurate classification. We develop an adversarial model to enable an adaptive imitation scheme through competition between the implicit network and a rival feature discriminator. Our method effectively transfers discriminability of connectives to the implicit features, and achieves state-of-the-art performance on the PDTB benchmark.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1093"}, {"primary_key": "3717045", "vector": [], "sparse_vector": [], "title": "AliMe Chat: A Sequence to Sequence and Rerank based Chatbot Engine.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Hai<PERSON> Chen", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2079"}, {"primary_key": "3717046", "vector": [], "sparse_vector": [], "title": "Fine-Grained Entity Typing with High-Multiplicity Assignments.", "authors": ["<PERSON>", "<PERSON>"], "summary": "As entity type systems become richer and more fine-grained, we expect the number of types assigned to a given entity to increase. However, most fine-grained typing work has focused on datasets that exhibit a low degree of type multiplicity. In this paper, we consider the high-multiplicity regime inherent in data sources such as Wikipedia that have semi-open type systems. We introduce a set-prediction approach to this problem and show that our model outperforms unstructured baselines on a new Wikipedia-based fine-grained typing corpus.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2052"}, {"primary_key": "3717047", "vector": [], "sparse_vector": [], "title": "Found in Translation: Reconstructing Phylogenetic Language Trees from Translations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Translation has played an important role in trade, law, commerce, politics, and literature for thousands of years. Translators have always tried to be invisible; ideal translations should look as if they were written originally in the target language. We show that traces of the source language remain in the translation product to the extent that it is possible to uncover the history of the source language by looking only at the translation. Specifically, we automatically reconstruct phylogenetic language trees from monolingual texts (translated from several source languages). The signal of the source language is so powerful that it is retained even after two phases of translation. This strongly indicates that source language interference is the most dominant characteristic of translated texts, overshadowing the more subtle signals of universal properties of translation.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1049"}, {"primary_key": "3717048", "vector": [], "sparse_vector": [], "title": "Abstract Syntax Networks for Code Generation and Semantic Parsing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Tasks like code generation and semantic parsing require mapping unstructured (or partially structured) inputs to well-formed, executable outputs. We introduce abstract syntax networks, a modeling framework for these problems. The outputs are represented as abstract syntax trees (ASTs) and constructed by a decoder with a dynamically-determined modular structure paralleling the structure of the output tree. On the benchmark Hearthstone dataset for code generation, our model obtains 79.2 BLEU and 22.7% exact match accuracy, compared to previous state-of-the-art values of 67.1 and 6.1%. Furthermore, we perform competitively on the Atis, Jobs, and Geo semantic parsing datasets with no task-specific engineering.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1105"}, {"primary_key": "3717049", "vector": [], "sparse_vector": [], "title": "A Neural Model for User Geolocation and Lexical Dialectology.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a simple yet effective text-based user geolocation model based on a neural network with one hidden layer, which achieves state of the art performance over three Twitter benchmark geolocation datasets, in addition to producing word and phrase embeddings in the hidden layer that we show to be useful for detecting dialectal terms. As part of our analysis of dialectal terms, we release DAREDS, a dataset for evaluating dialect term detection methods.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2033"}, {"primary_key": "3717050", "vector": [], "sparse_vector": [], "title": "Linguistic analysis of differences in portrayal of movie characters.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1153"}, {"primary_key": "3717051", "vector": [], "sparse_vector": [], "title": "SoccEval: An Annotation Schema for Rating Soccer Players.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper describes the SoccEval Annotation Project, an annotation schema designed to support machine-learning classification efforts to evaluate the performance of soccer players based on match reports taken from online news sources.In addition to factual information about player attributes and actions, the schema annotates subjective opinions about them.After explaining the annotation schema and annotation process, we describe a machine learning experiment.Classifiers trained on features derived from annotated data performed better than a baseline trained on unigram features.Initial results suggest that improvements can be made to the annotation scheme and guidelines as well as the amount of data annotated.We believe our schema could be potentially expanded to extract more information about soccer players and teams.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3015"}, {"primary_key": "3717052", "vector": [], "sparse_vector": [], "title": "Annotating tense, mood and voice for English, French and German.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present the first open-source tool for annotating morphosyntactic tense, mood and voice for English, French and German verbal complexes.The annotation is based on a set of language-specific rules, which are applied on dependency trees and leverage information about lemmas, morphological properties and POS-tags of the verbs.Our tool has an average accuracy of about 76%.The tense, mood and voice features are useful both as features in computational modeling and for corpuslinguistic research.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4001"}, {"primary_key": "3717053", "vector": [], "sparse_vector": [], "title": "Are You Asking the Right Questions? Teaching Machines to Ask Clarification Questions.", "authors": ["<PERSON><PERSON>"], "summary": "Inquiry is fundamental to communication, and machines cannot effectively collaborate with humans unless they can ask questions.In this thesis work, we explore how can we teach machines to ask clarification questions when faced with uncertainty, a goal of increasing importance in today's automated society.We do a preliminary study using data from StackExchange, a plentiful online resource where people routinely ask clarifying questions to posts so that they can better offer assistance to the original poster.We build neural network models inspired by the idea of the expected value of perfect information: a good question is one whose expected answer is going to be most useful.To build generalizable systems, we propose two future research directions: a template-based model and a sequence-to-sequence based neural generative model.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3006"}, {"primary_key": "3717054", "vector": [], "sparse_vector": [], "title": "Multilingual Connotation Frames: A Case Study on Social Media for Targeted Sentiment Analysis and Forecast.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "People around the globe respond to major real world events through social media. To study targeted public sentiments across many languages and geographic locations, we introduce multilingual connotation frames: an extension from English connotation frames of <PERSON><PERSON><PERSON> et al. (2016) with 10 additional European languages, focusing on the implied sentiments among event participants engaged in a frame. As a case study, we present large scale analysis on targeted public sentiments toward salient events and entities using 1.2 million multilingual connotation frames extracted from Twitter.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2073"}, {"primary_key": "3717055", "vector": [], "sparse_vector": [], "title": "Learning Lexico-Functional Patterns for First-Person Affect.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Informal first-person narratives are a unique resource for computational models of everyday events and people’s affective reactions to them. People blogging about their day tend not to explicitly say I am happy. Instead they describe situations from which other humans can readily infer their affective reactions. However current sentiment dictionaries are missing much of the information needed to make similar inferences. We build on recent work that models affect in terms of lexical predicate functions and affect on the predicate’s arguments. We present a method to learn proxies for these functions from first-person narratives. We construct a novel fine-grained test set, and show that the patterns we learn improve our ability to predict first-person affective reactions to everyday events, from a Stanford sentiment baseline of .67F to .75F.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2022"}, {"primary_key": "3717056", "vector": [], "sparse_vector": [], "title": "Detecting annotation noise in automatically labelled data.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a method for error detection in automatically annotated text, aimed at supporting the creation of high-quality language resources at affordable cost. Our method combines an unsupervised generative model with human supervision from active learning. We test our approach on in-domain and out-of-domain data in two languages, in AL simulations and in a real world setting. For all settings, the results show that our method is able to detect annotation errors with high precision and high recall.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1107"}, {"primary_key": "3717058", "vector": [], "sparse_vector": [], "title": "Semi-supervised Multitask Learning for Sequence Labeling.", "authors": ["<PERSON><PERSON>"], "summary": "We propose a sequence labeling framework with a secondary training objective, learning to predict surrounding words for every word in the dataset. This language modeling objective incentivises the system to learn general-purpose patterns of semantic and syntactic composition, which are also useful for improving accuracy on different sequence labeling tasks. The architecture was evaluated on a range of datasets, covering the tasks of error detection in learner texts, named entity recognition, chunking and POS-tagging. The novel language modeling objective provided consistent performance improvements on every benchmark, without requiring any additional annotated or unannotated data.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1194"}, {"primary_key": "3717059", "vector": [], "sparse_vector": [], "title": "Volatility Prediction using Financial Disclosures Sentiments with Word Embedding-based IR Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1157"}, {"primary_key": "3717060", "vector": [], "sparse_vector": [], "title": "Life-iNet: A Structured Network-Based Knowledge Exploration and Analytics System for Life Sciences.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Fang<PERSON> Tao", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>ei<PERSON><PERSON> Ping", "<PERSON>", "Jiawei Han"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of ACL 2017, System Demonstrations. 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4010"}, {"primary_key": "3717061", "vector": [], "sparse_vector": [], "title": "Learning Semantic Correspondences in Technical Documentation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the problem of translating high-level textual descriptions to formal representations in technical documentation as part of an effort to model the meaning of such documentation. We focus specifically on the problem of learning translational correspondences between text descriptions and grounded representations in the target documentation, such as formal representation of functions or code templates. Our approach exploits the parallel nature of such documentation, or the tight coupling between high-level text and the low-level representations we aim to learn. Data is collected by mining technical documents for such parallel text-representation pairs, which we use to train a simple semantic parsing model. We report new baseline results on sixteen novel datasets, including the standard library documentation for nine popular programming languages across seven natural languages, and a small collection of Unix utility manuals.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1148"}, {"primary_key": "3717062", "vector": [], "sparse_vector": [], "title": "Estimating Code-Switching on Twitter with a Novel Generalized Word-Level Language Detection Technique.", "authors": ["<PERSON><PERSON><PERSON>", "Royal Sequiera", "<PERSON><PERSON><PERSON>", "Kalika Bali", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1180"}, {"primary_key": "3717063", "vector": [], "sparse_vector": [], "title": "A Recurrent Neural Model with Attention for the Recognition of Chinese Implicit Discourse Relations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce an attention-based Bi-LSTM for Chinese implicit discourse relations and demonstrate that modeling argument pairs as a joint sequence can outperform word order-agnostic approaches. Our model benefits from a partial sampling scheme and is conceptually simple, yet achieves state-of-the-art performance on the Chinese Discourse Treebank. We also visualize its attention activity to illustrate the model's ability to selectively focus on the relevant parts of an input sequence.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2040"}, {"primary_key": "3717064", "vector": [], "sparse_vector": [], "title": "End-to-End Non-Factoid Question Answering with an Interactive Visualization of Neural Attention Weights.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Advanced attention mechanisms are an important part of successful neural network approaches for non-factoid answer selection because they allow the models to focus on few important segments within rather long answer texts.Analyzing attention mechanisms is thus crucial for understanding strengths and weaknesses of particular models.We present an extensible, highly modular service architecture that enables the transformation of neural network models for non-factoid answer selection into fully featured end-to-end question answering systems.The primary objective of our system is to enable researchers a way to interactively explore and compare attentionbased neural networks for answer selection.Our interactive user interface helps researchers to better understand the capabilities of the different approaches and can aid qualitative analyses.The source-code of our system is publicly available.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4004"}, {"primary_key": "3717065", "vector": [], "sparse_vector": [], "title": "Automatic Compositor Attribution in the First Folio of Shakespeare.", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Compositor attribution, the clustering of pages in a historical printed document by the individual who set the type, is a bibliographic task that relies on analysis of orthographic variation and inspection of visual details of the printed page. In this paper, we introduce a novel unsupervised model that jointly describes the textual and visual features needed to distinguish compositors. Applied to images of Shakespeare’s First Folio, our model predicts attributions that agree with the manual judgements of bibliographers with an accuracy of 87%, even on text that is the output of OCR.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2065"}, {"primary_key": "3717066", "vector": [], "sparse_vector": [], "title": "Bootstrapping for Numerical Open IE.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Mausam"], "summary": "We design and release <PERSON>ON<PERSON>, the first open numerical relation extractor, for extracting Open IE tuples where one of the arguments is a number or a quantity-unit phrase. BONIE uses bootstrapping to learn the specific dependency patterns that express numerical relations in a sentence. BONIE’s novelty lies in task-specific customizations, such as inferring implicit relations, which are clear due to context such as units (for e.g., ‘square kilometers’ suggests area, even if the word ‘area’ is missing in the sentence). BONIE obtains 1.5x yield and 15 point precision gain on numerical facts over a state-of-the-art Open IE system.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2050"}, {"primary_key": "3717067", "vector": [], "sparse_vector": [], "title": "Challenging Language-Dependent Segmentation for Arabic: An Application to Machine Translation and Part-of-Speech Tagging.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2095"}, {"primary_key": "3717068", "vector": [], "sparse_vector": [], "title": "Error-repair Dependency Parsing for Ungrammatical Texts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a new dependency parsing scheme which jointly parses a sentence and repairs grammatical errors by extending the non-directional transition-based formalism of <PERSON> and <PERSON> (2010) with three additional actions: SUBSTITUTE, DELETE, INSERT. Because these actions may cause an infinite loop in derivation, we also introduce simple constraints that ensure the parser termination. We evaluate our model with respect to dependency accuracy and grammaticality improvements for ungrammatical sentences, demonstrating the robustness and applicability of our scheme.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2030"}, {"primary_key": "3717069", "vector": [], "sparse_vector": [], "title": "MORSE: Semantic-ally Drive-n MORpheme SEgment-er.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present in this paper a novel framework for morpheme segmentation which uses the morpho-syntactic regularities preserved by word representations, in addition to orthographic features, to segment words into morphemes. This framework is the first to consider vocabulary-wide syntactico-semantic information for this task. We also analyze the deficiencies of available benchmarking datasets and introduce our own dataset that was created on the basis of compositionality. We validate our algorithm across datasets and present state-of-the-art results.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1051"}, {"primary_key": "3717070", "vector": [], "sparse_vector": [], "title": "Determining Whether and When People Participate in the Events They Tweet About.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper describes an approach to determine whether people participate in the events they tweet about. Specifically, we determine whether people are participants in events with respect to the tweet timestamp. We target all events expressed by verbs in tweets, including past, present and events that may occur in the future. We present new annotations using 1,096 event mentions, and experimental results showing that the task is challenging.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2101"}, {"primary_key": "3717071", "vector": [], "sparse_vector": [], "title": "Enriching Complex Networks with Word Embeddings for Detecting Mild Cognitive Impairment from Speech Transcripts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>.", "Diego <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1118"}, {"primary_key": "3717072", "vector": [], "sparse_vector": [], "title": "Other Topics You May Also Agree or Disagree: Modeling Inter-Topic Preferences using Tweets and Matrix Factorization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We presents in this paper our approach for modeling inter-topic preferences of Twitter users: for example, “those who agree with the Trans-Pacific Partnership (TPP) also agree with free trade”. This kind of knowledge is useful not only for stance detection across multiple topics but also for various real-world applications including public opinion survey, electoral prediction, electoral campaigns, and online debates. In order to extract users’ preferences on Twitter, we design linguistic patterns in which people agree and disagree about specific topics (e.g., “A is completely wrong”). By applying these linguistic patterns to a collection of tweets, we extract statements agreeing and disagreeing with various topics. Inspired by previous work on item recommendation, we formalize the task of modeling inter-topic preferences as matrix factorization: representing users’ preference as a user-topic matrix and mapping both users and topics onto a latent feature space that abstracts the preferences. Our experimental results demonstrate both that our presented approach is useful in predicting missing preferences of users and that the latent vector representations of topics successfully encode inter-topic preferences.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1037"}, {"primary_key": "3717073", "vector": [], "sparse_vector": [], "title": "Modeling Situations in Neural Chat Bots.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Social media accumulates vast amounts of online conversations that enable datadriven modeling of chat dialogues.It is, however, still hard to utilize the neural network-based SEQ2SEQ model for dialogue modeling in spite of its acknowledged success in machine translation.The main challenge comes from the high degrees of freedom of outputs (responses).This paper presents neural conversational models that have general mechanisms for handling a variety of situations that affect our responses.Response selection tests on massive dialogue data we have collected from Twitter confirmed the effectiveness of the proposed models with situations derived from utterances, users or time.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3020"}, {"primary_key": "3717074", "vector": [], "sparse_vector": [], "title": "EviNets: Neural Networks for Combining Evidence Signals for Factoid Question Answering.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A critical task for question answering is the final answer selection stage, which has to combine multiple signals available about each answer candidate. This paper proposes EviNets: a novel neural network architecture for factoid question answering. EviNets scores candidate answer entities by combining the available supporting evidence, e.g., structured knowledge bases and unstructured text documents. EviNets represents each piece of evidence with a dense embeddings vector, scores their relevance to the question, and aggregates the support for each candidate to predict their final scores. Each of the components is generic and allows plugging in a variety of models for semantic similarity scoring and information aggregation. We demonstrate the effectiveness of EviNets in experiments on the existing TREC QA and WikiMovies benchmarks, and on the new Yahoo! Answers dataset introduced in this paper. EviNets can be extended to other information types and could facilitate future work on combining evidence signals for joint reasoning in question answering.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2047"}, {"primary_key": "3717075", "vector": [], "sparse_vector": [], "title": "Semantic Word Clusters Using Signed Spectral Clustering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Vector space representations of words capture many aspects of word similarity, but such methods tend to produce vector spaces in which antonyms (as well as synonyms) are close to each other. For spectral clustering using such word embeddings, words are points in a vector space where synonyms are linked with positive weights, while antonyms are linked with negative weights. We present a new signed spectral normalized graph cut algorithm, signed clustering, that overlays existing thesauri upon distributionally derived vector representations of words, so that antonym relationships between word pairs are represented by negative weights. Our signed clustering algorithm produces clusters of words that simultaneously capture distributional and synonym relations. By using randomized spectral decomposition (<PERSON><PERSON> et al., 2011) and sparse matrices, our method is both fast and scalable. We validate our clusters using datasets containing human judgments of word pair similarities and show the benefit of using our word clusters for sentiment prediction.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1087"}, {"primary_key": "3717076", "vector": [], "sparse_vector": [], "title": "Get To The Point: Summarization with Pointer-Generator Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Neural sequence-to-sequence models have provided a viable new approach for abstractive text summarization (meaning they are not restricted to simply selecting and rearranging passages from the original text). However, these models have two shortcomings: they are liable to reproduce factual details inaccurately, and they tend to repeat themselves. In this work we propose a novel architecture that augments the standard sequence-to-sequence attentional model in two orthogonal ways. First, we use a hybrid pointer-generator network that can copy words from the source text via pointing, which aids accurate reproduction of information, while retaining the ability to produce novel words through the generator. Second, we use coverage to keep track of what has been summarized, which discourages repetition. We apply our model to the CNN / Daily Mail summarization task, outperforming the current abstractive state-of-the-art by at least 2 ROUGE points.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1099"}, {"primary_key": "3717078", "vector": [], "sparse_vector": [], "title": "Segmentation Guided Attention Networks for Visual Question Answering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we propose to solve the problem of Visual Question Answering by using a novel segmentation guided attention based network which we call SegAttend-Net.We use image segmentation maps, generated by a Fully Convolutional Deep Neural Network to refine our attention maps and use these refined attention maps to make the model focus on the relevant parts of the image to answer a question.The refined attention maps are used by the LSTM network to learn to produce the answer.We presently train our model on the visual7W dataset and do a category wise evaluation of the 7 question categories.We achieve state of the art results on this dataset and beat the previous benchmark on this dataset by a 1.5% margin improving the question answering accuracy from 54.1% to 55.6% and demonstrate improvements in each of the question categories.We also visualize our generated attention maps and note their improvement over the attention maps generated by the previous best approach.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3008"}, {"primary_key": "3717079", "vector": [], "sparse_vector": [], "title": "Interactive Learning of Grounded Verb Semantics towards Human-Robot Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "To enable human-robot communication and collaboration, previous works represent grounded verb semantics as the potential change of state to the physical world caused by these verbs. Grounded verb semantics are acquired mainly based on the parallel data of the use of a verb phrase and its corresponding sequences of primitive actions demonstrated by humans. The rich interaction between teachers and students that is considered important in learning new skills has not yet been explored. To address this limitation, this paper presents a new interactive learning approach that allows robots to proactively engage in interaction with human partners by asking good questions to learn models for grounded verb semantics. The proposed approach uses reinforcement learning to allow the robot to acquire an optimal policy for its question-asking behaviors by maximizing the long-term reward. Our empirical results have shown that the interactive learning approach leads to more reliable models for grounded verb semantics, especially in the noisy environment which is full of uncertainties. Compared to previous work, the models acquired from interactive learning result in a 48% to 145% performance gain when applied in new situations.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1150"}, {"primary_key": "3717080", "vector": [], "sparse_vector": [], "title": "FOIL it! Find One mismatch between Image and Language caption.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we aim to understand whether current language and vision (LaVi) models truly grasp the interaction between the two modalities. To this end, we propose an extension of the MSCOCO dataset, FOIL-COCO, which associates images with both correct and \"foil\" captions, that is, descriptions of the image that are highly similar to the original ones, but contain one single mistake (\"foil word\"). We show that current LaVi models fall into the traps of this data and perform badly on three tasks: a) caption classification (correct vs. foil); b) foil word detection; c) foil word correction. Humans, in contrast, have near-perfect performance on those tasks. We demonstrate that merely utilising language cues is not enough to model FOIL-COCO and that it challenges the state-of-the-art by requiring a fine-grained understanding of the relation between text and image.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1024"}, {"primary_key": "3717081", "vector": [], "sparse_vector": [], "title": "A Conditional Variational Framework for Dialog Generation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2080"}, {"primary_key": "3717082", "vector": [], "sparse_vector": [], "title": "Speeding Up Neural Machine Translation Decoding by Shrinking Run-time Vocabulary.", "authors": ["Xing Shi", "<PERSON>"], "summary": "We speed up Neural Machine Translation (NMT) decoding by shrinking run-time target vocabulary. We experiment with two shrinking approaches: Locality Sensitive Hashing (LSH) and word alignments. Using the latter method, we get a 2x overall speed-up over a highly-optimized GPU implementation, without hurting BLEU. On certain low-resource language pairs, the same methods improve BLEU by 0.5 points. We also report a negative result for LSH on GPUs, due to relatively large overhead, though it was successful on CPUs. Compared with Locality Sensitive Hashing (LSH), decoding with word alignments is GPU-friendly, orthogonal to existing speedup methods and more robust across language pairs.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2091"}, {"primary_key": "3717083", "vector": [], "sparse_vector": [], "title": "Detection of Chinese Word Usage Errors for Non-Native Chinese Learners with Bidirectional LSTM.", "authors": ["<PERSON>w-<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Selecting appropriate words to compose a sentence is one common problem faced by non-native Chinese learners. In this paper, we propose (bidirectional) LSTM sequence labeling models and explore various features to detect word usage errors in Chinese sentences. By combining CWINDOW word embedding features and POS information, the best bidirectional LSTM model achieves accuracy 0.5138 and MRR 0.6789 on the HSK dataset. For 80.79% of the test data, the model ranks the ground-truth within the top two at position level.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2064"}, {"primary_key": "3717084", "vector": [], "sparse_vector": [], "title": "Lifelong Learning CRF for Supervised Aspect Extraction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper makes a focused contribution to supervised aspect extraction. It shows that if the system has performed aspect extraction from many past domains and retained their results as knowledge, Conditional Random Fields (CRF) can leverage this knowledge in a lifelong learning manner to extract in a new domain markedly better than the traditional CRF without using this prior knowledge. The key innovation is that even after CRF training, the model can still improve its extraction with experiences in its applications.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2023"}, {"primary_key": "3717085", "vector": [], "sparse_vector": [], "title": "Zara Returns: Improved Personality Induction and Adaptation by an Empathetic Virtual Agent.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Virtual agents need to adapt their personality to the user in order to become more empathetic.To this end, we developed <PERSON><PERSON> the Supergirl, an interactive empathetic agent, using a modular approach.In this paper, we describe the enhanced personality module with improved recognition from speech and text using deep learning frameworks.From raw audio, an average F-score of 69.6 was obtained from realtime personality assessment using a Convolutional Neural Network (CNN) model.From text, we improved personality recognition results with a CNN model on top of pre-trained word embeddings and obtained an average F-score of 71.0.Results from our Human-Agent Interaction study confirmed our assumption that people have different agent personality preferences.We use insights from this study to adapt our agent to user personality.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4021"}, {"primary_key": "3717086", "vector": [], "sparse_vector": [], "title": "Domain Specific Automatic Question Generation from Text.", "authors": ["<PERSON><PERSON>"], "summary": "The goal of my doctoral thesis is to automatically generate interrogative sentences from descriptive sentences of Turkish biology text.We employ syntactic and semantic approaches to parse descriptive sentences.Syntactic and semantic approaches utilize syntactic (constituent or dependency) parsing and semantic role labeling systems respectively.After parsing step, question statements whose answers are embedded in the descriptive sentences are going to be formulated by using some predefined rules and templates.Syntactic parsing is done using an open source dependency parser called <PERSON>tP<PERSON><PERSON> (<PERSON> et al. 2007).Whereas to accomplish semantic parsing, we will construct a biological proposition bank (BioPropBank) and a corpus annotated with semantic roles.Then we will employ supervised methods to automatic label the semantic roles of a sentence.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3014"}, {"primary_key": "3717087", "vector": [], "sparse_vector": [], "title": "Recognizing Counterfactual Thinking in Social Media Texts.", "authors": ["Youngseo Son", "<PERSON><PERSON>", "<PERSON>", "Allegra Larche", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2103"}, {"primary_key": "3717088", "vector": [], "sparse_vector": [], "title": "AMR-to-text Generation with Synchronous Node Replacement Grammar.", "authors": ["<PERSON><PERSON> Song", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper addresses the task of AMR-to-text generation by leveraging synchronous node replacement grammar. During training, graph-to-string rules are learned using a heuristic extraction algorithm. At test time, a graph transducer is applied to collapse input AMRs and generate output sentences. Evaluated on a standard benchmark, our method gives the state-of-the-art result.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2002"}, {"primary_key": "3717089", "vector": [], "sparse_vector": [], "title": "Discourse Mode Identification in Essays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Discourse modes play an important role in writing composition and evaluation. This paper presents a study on the manual and automatic identification of narration,exposition, description, argument and emotion expressing sentences in narrative essays. We annotate a corpus to study the characteristics of discourse modes and describe a neural sequence labeling model for identification. Evaluation results show that discourse modes can be identified automatically with an average F1-score of 0.7. We further demonstrate that discourse modes can be used as features that improve automatic essay scoring (AES). The impacts of discourse modes for AES are also discussed.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1011"}, {"primary_key": "3717091", "vector": [], "sparse_vector": [], "title": "An Algebra for Feature Extraction.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Though feature extraction is a necessary first step in statistical NLP, it is often seen as a mere preprocessing step. Yet, it can dominate computation time, both during training, and especially at deployment. In this paper, we formalize feature extraction from an algebraic perspective. Our formalization allows us to define a message passing algorithm that can restructure feature templates to be more computationally efficient. We show via experiments on text chunking and relation extraction that this restructuring does indeed speed up feature extraction in practice by reducing redundant computation.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1173"}, {"primary_key": "3717092", "vector": [], "sparse_vector": [], "title": "Sentence Alignment Methods for Improving Text Simplification Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2016"}, {"primary_key": "3717093", "vector": [], "sparse_vector": [], "title": "Alternative Objective Functions for Training MT Evaluation Metrics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>&apo<PERSON>;an"], "summary": "MT evaluation metrics are tested for correlation with human judgments either at the sentence- or the corpus-level. Trained metrics ignore corpus-level judgments and are trained for high sentence-level correlation only. We show that training only for one objective (sentence or corpus level), can not only harm the performance on the other objective, but it can also be suboptimal for the objective being optimized. To this end we present a metric trained for corpus-level and show empirical comparison against a metric trained for sentence-level exemplifying how their performance may vary per language pair, type and level of judgment. Subsequently we propose a model trained to optimize both objectives simultaneously and show that it is far more stable than–and on average outperforms–both models on both objectives.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2004"}, {"primary_key": "3717094", "vector": [], "sparse_vector": [], "title": "Integrating Deep Linguistic Features in Factuality Prediction over Unified Datasets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2056"}, {"primary_key": "3717095", "vector": [], "sparse_vector": [], "title": "A Minimal Span-Based Neural Constituency Parser.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we present a minimal neural model for constituency parsing based on independent scoring of labels and spans. We show that this model is not only compatible with classical dynamic programming techniques, but also admits a novel greedy top-down inference algorithm based on recursive partitioning of the input. We demonstrate empirically that both prediction schemes are competitive with recent work, and when combined with basic extensions to the scoring model are capable of achieving state-of-the-art single-model performance on the Penn Treebank (91.79 F1) and strong performance on the French Treebank (82.23 F1).", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1076"}, {"primary_key": "3717096", "vector": [], "sparse_vector": [], "title": "Evaluation Metrics for Machine Reading Comprehension: Prerequisite Skills and Readability.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Knowing the quality of reading comprehension (RC) datasets is important for the development of natural-language understanding systems. In this study, two classes of metrics were adopted for evaluating RC datasets: prerequisite skills and readability. We applied these classes to six existing datasets, including MCTest and SQuAD, and highlighted the characteristics of the datasets according to each metric and the correlation between the two classes. Our dataset analysis suggests that the readability of RC datasets does not directly affect the question difficulty and that it is possible to create an RC dataset that is easy to read but difficult to answer.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1075"}, {"primary_key": "3717097", "vector": [], "sparse_vector": [], "title": "A Corpus of Natural Language for Visual Reasoning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a new visual reasoning language dataset, containing 92,244 pairs of examples of natural statements grounded in synthetic images with 3,962 unique sentences. We describe a method of crowdsourcing linguistically-diverse data, and present an analysis of our data. The data demonstrates a broad set of linguistic phenomena, requiring visual and set-theoretic reasoning. We experiment with various models, and show the data presents a strong challenge for future research.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2034"}, {"primary_key": "3717098", "vector": [], "sparse_vector": [], "title": "nQuery - A Natural Language Statement to SQL Query Generator.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>"], "summary": "In this research, an intelligent system is designed between the user and the database system which accepts natural language input and then converts it into an SQL query.The research focuses on incorporating complex queries along with simple queries irrespective of the database.The system accommodates aggregate functions, multiple conditions in WHERE clause, advanced clauses like ORDER BY, GROUP BY and HAVING.The system handles single sentence natural language inputs, which are with respect to selected database.The research currently concentrates on MySQL database system.The natural language statement goes through various stages of Natural Language Processing like morphological, lexical, syntactic and semantic analysis resulting in SQL query formation.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3004"}, {"primary_key": "3717099", "vector": [], "sparse_vector": [], "title": "Semantic Dependency Parsing via Book Embedding.", "authors": ["Weiwei Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We model a dependency graph as a book, a particular kind of topological space, for semantic dependency parsing. The spine of the book is made up of a sequence of words, and each page contains a subset of noncrossing arcs. To build a semantic graph for a given sentence, we design new Maximum Subgraph algorithms to generate noncrossing graphs on each page, and a Lagrangian Relaxation-based algorithm tocombine pages into a book. Experiments demonstrate the effectiveness of the bookembedding framework across a wide range of conditions. Our parser obtains comparable results with a state-of-the-art transition-based parser.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1077"}, {"primary_key": "3717100", "vector": [], "sparse_vector": [], "title": "Neural Architectures for Multilingual Semantic Parsing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we address semantic parsing in a multilingual context. We train one multilingual model that is capable of parsing natural language sentences from multiple different languages into their corresponding formal semantic representations. We extend an existing sequence-to-tree model to a multi-task learning framework which shares the decoder for generating semantic representations. We report evaluation results on the multilingual GeoQuery corpus and introduce a new multilingual version of the ATIS corpus.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2007"}, {"primary_key": "3717101", "vector": [], "sparse_vector": [], "title": "Building a Non-Trivial Paraphrase Corpus Using Multiple Machine Translation Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a novel sentential paraphrase acquisition method.To build a wellbalanced corpus for Paraphrase Identification, we especially focus on acquiring both non-trivial positive and negative instances.We use multiple machine translation systems to generate positive candidates and a monolingual corpus to extract negative candidates.To collect nontrivial instances, the candidates are uniformly sampled by word overlap rate.Finally, annotators judge whether the candidates are either positive or negative.Using this method, we built and released the first evaluation corpus for Japanese paraphrase identification, which comprises 655 sentence pairs.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3007"}, {"primary_key": "3717102", "vector": [], "sparse_vector": [], "title": "Temporal Word Analogies: Identifying Lexical Replacement with Diachronic Word Embeddings.", "authors": ["<PERSON><PERSON>"], "summary": "This paper introduces the concept of temporal word analogies: pairs of words which occupy the same semantic space at different points in time. One well-known property of word embeddings is that they are able to effectively model traditional word analogies (\"word w1 is to word w2 as word w3 is to word w4\") through vector addition. Here, I show that temporal word analogies (\"word w1 at time t𝛼 is like word w2 at time t𝛽\") can effectively be modeled with diachronic word embeddings, provided that the independent embedding spaces from each time period are appropriately transformed into a common vector space. When applied to a diachronic corpus of news articles, this method is able to identify temporal word analogies such as \"<PERSON> in 1987 is like <PERSON> in 1997\", or \"<PERSON><PERSON> in 1987 is like iPod in 2007\".", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2071"}, {"primary_key": "3717103", "vector": [], "sparse_vector": [], "title": "Friendships, Rivalries, and Trysts: Characterizing Relations between Ideas in Texts.", "authors": ["<PERSON><PERSON>", "Dallas Card", "<PERSON>"], "summary": "Understanding how ideas relate to each other is a fundamental question in many domains, ranging from intellectual history to public communication. Because ideas are naturally embedded in texts, we propose the first framework to systematically characterize the relations between ideas based on their occurrence in a corpus of documents, independent of how these ideas are represented. Combining two statistics—cooccurrence within documents and prevalence correlation over time—our approach reveals a number of different ways in which ideas can cooperate and compete. For instance, two ideas can closely track each other's prevalence over time, and yet rarely cooccur, almost like a \"cold war\" scenario. We observe that pairwise cooccurrence and prevalence correlation exhibit different distributions. We further demonstrate that our approach is able to uncover intriguing relations between ideas through in-depth case studies on news articles and research papers.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1072"}, {"primary_key": "3717104", "vector": [], "sparse_vector": [], "title": "Abstractive Document Summarization with a Graph-Based Attentional Neural Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "ive summarization is the ultimate goal of document summarization research, but previously it is less investigated due to the immaturity of text generation techniques. Recently impressive progress has been made to abstractive sentence summarization using neural models. Unfortunately, attempts on abstractive document summarization are still in a primitive stage, and the evaluation results are worse than extractive methods on benchmark datasets. In this paper, we review the difficulties of neural abstractive document summarization, and propose a novel graph-based attention mechanism in the sequence-to-sequence framework. The intuition is to address the saliency factor of summarization, which has been overlooked by prior works. Experimental results demonstrate our model is able to achieve considerable improvement over previous neural abstractive models. The data-driven neural abstractive method is also competitive with state-of-the-art extractive methods.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1108"}, {"primary_key": "3717105", "vector": [], "sparse_vector": [], "title": "WebChild 2.0 : Fine-Grained Commonsense Knowledge Distillation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Despite important progress in the area of intelligent systems, most such systems still lack commonsense knowledge that appears crucial for enabling smarter, more human-like decisions.In this paper, we present a system based on a series of algorithms to distill fine-grained disambiguated commonsense knowledge from massive amounts of text.Our WebChild 2.0 knowledge base is one of the largest commonsense knowledge bases available, describing over 2 million disambiguated concepts and activities, connected by over 18 million assertions.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4020"}, {"primary_key": "3717106", "vector": [], "sparse_vector": [], "title": "Salience Rank: Efficient Keyphrase Extraction with Topic Modeling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Topical PageRank (TPR) uses latent topic distribution inferred by Latent Dirichlet Allocation (LDA) to perform ranking of noun phrases extracted from documents. The ranking procedure consists of running PageRank K times, where K is the number of topics used in the LDA model. In this paper, we propose a modification of TPR, called Salience Rank. Salience Rank only needs to run PageRank once and extracts comparable or better keyphrases on benchmark datasets. In addition to quality and efficiency benefit, our method has the flexibility to extract keyphrases with varying tradeoffs between topic specificity and corpus specificity.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2084"}, {"primary_key": "3717108", "vector": [], "sparse_vector": [], "title": "How to Make Context More Useful? An Empirical Study on Context-Aware Neural Conversational Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yansong Feng", "<PERSON><PERSON>"], "summary": "Generative conversational systems are attracting increasing attention in natural language processing (NLP). Recently, researchers have noticed the importance of context information in dialog processing, and built various models to utilize context. However, there is no systematic comparison to analyze how to use context effectively. In this paper, we conduct an empirical study to compare various models and investigate the effect of context information in dialog systems. We also propose a variant that explicitly weights context vectors by context-query relevance, outperforming the other baselines.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2036"}, {"primary_key": "3717109", "vector": [], "sparse_vector": [], "title": "Character-Aware Neural Morphological Disambiguation.", "authors": ["Alymzhan Toleu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We develop a language-independent, deep learning-based approach to the task of morphological disambiguation. Guided by the intuition that the correct analysis should be \"most similar\" to the context, we propose dense representations for morphological analyses and surface context and a simple yet effective way of combining the two to perform disambiguation. Our approach improves on the language-dependent state of the art for two agglutinative languages (Turkish and Kazakh) and can be potentially applied to other morphologically complex languages.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2105"}, {"primary_key": "3717110", "vector": [], "sparse_vector": [], "title": "Combating Human Trafficking with Multimodal Deep Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human trafficking is a global epidemic affecting millions of people across the planet. Sex trafficking, the dominant form of human trafficking, has seen a significant rise mostly due to the abundance of escort websites, where human traffickers can openly advertise among at-will escort advertisements. In this paper, we take a major step in the automatic detection of advertisements suspected to pertain to human trafficking. We present a novel dataset called Trafficking-10k, with more than 10,000 advertisements annotated for this task. The dataset contains two sources of information per advertisement: text and images. For the accurate detection of trafficking advertisements, we designed and trained a deep multimodal model called the Human Trafficking Deep Network (HTDN).", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1142"}, {"primary_key": "3717111", "vector": [], "sparse_vector": [], "title": "Neural Architecture for Temporal Relation Extraction: A Bi-LSTM Approach for Detecting Narrative Containers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a neural architecture for containment relation identification between medical events and/or temporal expressions. We experiment on a corpus of de-identified clinical notes in English from the Mayo Clinic, namely the THYME corpus. Our model achieves an F-measure of 0.613 and outperforms the best result reported on this corpus to date.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2035"}, {"primary_key": "3717112", "vector": [], "sparse_vector": [], "title": "A Generative Attentional Neural Network Model for Dialogue Act Classification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a novel generative neural network architecture for Dialogue Act classification. Building upon the Recurrent Neural Network framework, our model incorporates a novel attentional technique and a label to label connection for sequence learning, akin to Hidden Markov Models. The experiments show that both of these innovations lead our model to outperform strong baselines for dialogue act classification on MapTask and Switchboard corpora. We further empirically analyse the effectiveness of each of the new innovations.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2083"}, {"primary_key": "3717113", "vector": [], "sparse_vector": [], "title": "CANE: Context-Aware Network Embedding for Relation Modeling.", "authors": ["<PERSON>unch<PERSON> Tu", "<PERSON>", "<PERSON><PERSON><PERSON>", "Maosong Sun"], "summary": "Network embedding (NE) is playing a critical role in network analysis, due to its ability to represent vertices with efficient low-dimensional embedding vectors. However, existing NE models aim to learn a fixed context-free embedding for each vertex and neglect the diverse roles when interacting with other vertices. In this paper, we assume that one vertex usually shows different aspects when interacting with different neighbor vertices, and should own different embeddings respectively. Therefore, we present Context-Aware Network Embedding (CANE), a novel NE model to address this issue. CANE learns context-aware embeddings for vertices with mutual attention mechanism and is expected to model the semantic relationships between vertices more precisely. In experiments, we compare our model with existing NE models on three real-world datasets. Experimental results show that CANE achieves significant improvement than state-of-the-art methods on link prediction and comparable performance on vertex classification. The source code and datasets can be obtained from https://github.com/thunlp/CANE.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1158"}, {"primary_key": "3717114", "vector": [], "sparse_vector": [], "title": "RelTextRank: An Open Source Framework for Building Relational Syntactic-Semantic Text Pair Representations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Massimo <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a highly-flexible UIMA-based pipeline for developing structural kernelbased systems for relational learning from text, i.e., for generating training and test data for ranking, classifying short text pairs or measuring similarity between pieces of text.For example, the proposed pipeline can represent an input question and answer sentence pairs as syntacticsemantic structures, enriching them with relational information, e.g., links between question class, focus and named entities, and serializes them as training and test files for the tree kernel-based reranking framework.The pipeline generates a number of dependency and shallow chunkbased representations shown to achieve competitive results in previous work.It also enables easy evaluation of the models thanks to cross-validation facilities.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4014"}, {"primary_key": "3717115", "vector": [], "sparse_vector": [], "title": "PyDial: A Multi-domain Statistical Dialogue System Toolkit.", "authors": ["<PERSON>", "<PERSON><PERSON>-Bar<PERSON><PERSON>", "<PERSON><PERSON>-<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Tsung<PERSON><PERSON><PERSON><PERSON>", "Milica <PERSON>ic", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of ACL 2017, System Demonstrations. 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4013"}, {"primary_key": "3717116", "vector": [], "sparse_vector": [], "title": "Automatic Induction of Synsets from a Graph of Synonyms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a new graph-based approach that induces synsets using synonymy dictionaries and word embeddings. First, we build a weighted graph of synonyms extracted from commonly available resources, such as Wiktionary. Second, we apply word sense induction to deal with ambiguous words. Finally, we cluster the disambiguated version of the ambiguous input graph into synsets. Our meta-clustering approach lets us use an efficient hard clustering algorithm to perform a fuzzy clustering of the graph. Despite its simplicity, our approach shows excellent results, outperforming five competitive state-of-the-art methods in terms of F-score on three gold standard datasets for English and Russian derived from large-scale manually constructed lexical resources.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1145"}, {"primary_key": "3717117", "vector": [], "sparse_vector": [], "title": "From Characters to Words to in Between: Do We Capture Morphology?", "authors": ["<PERSON>", "<PERSON>"], "summary": "Words can be represented by composing the representations of subword units such as word segments, characters, and/or character n-grams. While such representations are effective and may capture the morphological regularities of words, they have not been systematically compared, and it is not understood how they interact with different morphological typologies. On a language modeling task, we present experiments that systematically vary (1) the basic unit of representation, (2) the composition of these representations, and (3) the morphological typology of the language modeled. Our results extend previous findings that character representations are effective across typologies, and we find that a previously unstudied combination of character trigram representations composed with bi-LSTMs outperforms most others. But we also find room for improvement: none of the character-level models match the predictive accuracy of a model with access to true morphological analyses, even when learned from an order of magnitude more data.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1184"}, {"primary_key": "3717119", "vector": [], "sparse_vector": [], "title": "Tutorial: Making Better Use of the Crowd.", "authors": ["<PERSON>"], "summary": "Over the last decade, crowdsourcing has been used to harness the power of human computation to solve tasks that are notoriously difficult to solve with computers alone, such as determining whether or not an image contains a tree, rating the relevance of a website, or verifying the phone number of a business. The natural language processing community was early to embrace crowdsourcing as a tool for quickly and inexpensively obtaining annotated data to train NLP systems. Once this data is collected, it can be handed off to algorithms that learn to perform basic NLP tasks such as translation or parsing. Usually this handoff is where interaction with the crowd ends. The crowd provides the data, but the ultimate goal is to eventually take humans out of the loop. Are there better ways to make use of the crowd?In this tutorial, I will begin with a showcase of innovative uses of crowdsourcing that go beyond data collection and annotation. I will discuss applications to natural language processing and machine learning, hybrid intelligence or “human in the loop” AI systems that leverage the complementary strengths of humans and machines in order to achieve more than either could achieve alone, and large scale studies of human behavior online. I will then spend the majority of the tutorial diving into recent research aimed at understanding who crowdworkers are, how they behave, and what this should teach us about best practices for interacting with the crowd.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-5006"}, {"primary_key": "3717120", "vector": [], "sparse_vector": [], "title": "Twitter Demographic Classification Using Deep Multi-modal Multi-task Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Twitter should be an ideal place to get a fresh read on how different issues are playing with the public, one that’s potentially more reflective of democracy in this new media age than traditional polls. Pollsters typically ask people a fixed set of questions, while in social media people use their own voices to speak about whatever is on their minds. However, the demographic distribution of users on Twitter is not representative of the general population. In this paper, we present a demographic classifier for gender, age, political orientation and location on Twitter. We collected and curated a robust Twitter demographic dataset for this task. Our classifier uses a deep multi-modal multi-task learning architecture to reach a state-of-the-art performance, achieving an F1-score of 0.89, 0.82, 0.86, and 0.68 for gender, age, political orientation, and location respectively.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2076"}, {"primary_key": "3717121", "vector": [], "sparse_vector": [], "title": "Generating Contrastive Referring Expressions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The referring expressions (REs) produced by a natural language generation (NLG) system can be misunderstood by the hearer, even when they are semantically correct. In an interactive setting, the NLG system can try to recognize such misunderstandings and correct them. We present an algorithm for generating corrective REs that use contrastive focus (“no, the BLUE button”) to emphasize the information the hearer most likely misunderstood. We show empirically that these contrastive REs are preferred over REs without contrast marking.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1063"}, {"primary_key": "3717122", "vector": [], "sparse_vector": [], "title": "Separating Facts from Fiction: Linguistic Models to Classify Suspicious and Trusted News Posts on Twitter.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Pew research polls report 62 percent of U.S. adults get news on social media (<PERSON><PERSON><PERSON> and <PERSON>, 2016). In a December poll, 64 percent of U.S. adults said that “made-up news” has caused a “great deal of confusion” about the facts of current events (<PERSON><PERSON> et al., 2016). Fabricated stories in social media, ranging from deliberate propaganda to hoaxes and satire, contributes to this confusion in addition to having serious effects on global stability. In this work we build predictive models to classify 130 thousand news posts as suspicious or verified, and predict four sub-types of suspicious news – satire, hoaxes, clickbait and propaganda. We show that neural network models trained on tweet content and social network interactions outperform lexical models. Unlike previous work on deception detection, we find that adding syntax and grammar features to our models does not improve performance. Incorporating linguistic features improves classification results, however, social interaction features are most informative for finer-grained separation between four types of suspicious news posts.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2102"}, {"primary_key": "3717123", "vector": [], "sparse_vector": [], "title": "Morph-fitting: Fine-Tuning Word Vector Spaces with Simple Language-Specific Rules.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1006"}, {"primary_key": "3717124", "vector": [], "sparse_vector": [], "title": "Argumentation Quality Assessment: Theory vs. Practice.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2039"}, {"primary_key": "3717125", "vector": [], "sparse_vector": [], "title": "&quot;<PERSON><PERSON>, <PERSON><PERSON> on Fire&quot;: A New Benchmark Dataset for Fake News Detection.", "authors": ["<PERSON>"], "summary": "Automatic fake news detection is a challenging problem in deception detection, and it has tremendous real-world political and social impacts. However, statistical approaches to combating fake news has been dramatically limited by the lack of labeled benchmark datasets. In this paper, we present LIAR: a new, publicly available dataset for fake news detection. We collected a decade-long, 12.8K manually labeled short statements in various contexts from PolitiFact.com, which provides detailed analysis report and links to source documents for each case. This dataset can be used for fact-checking research as well. Notably, this new dataset is an order of magnitude larger than previously largest public fake news datasets of similar type. Empirically, we investigate automatic fake news detection based on surface-level linguistic patterns. We have designed a novel, hybrid convolutional neural network to integrate meta-data with text. We show that this hybrid approach can improve a text-only deep learning model.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2067"}, {"primary_key": "3717126", "vector": [], "sparse_vector": [], "title": "Negotiation of Antibiotic Treatment in Medical Consultations: A Corpus Based Study.", "authors": ["<PERSON>"], "summary": "Doctor-patient conversation is considered a contributing factor to antibiotic overprescription.Some language practices have been identified as parent pressuring doctors for prescribing; other practices are considered as likely to engender parent resistance to non-antibiotic treatment recommendations.In social science studies, approaches such as conversation analysis have been applied to identify those language practices.Current research for dialogue systems offer an alternative approach.Past research proved that corpusbased approaches have been effectively used for research involving modeling dialogue acts and sequential relations.In this proposal, we propose a corpus-based study of doctor-patient conversations of antibiotic treatment negotiation in pediatric consultations.Based on findings from conversation analysis studies, we use a computational linguistic approach to assist annotating and modeling of doctor-patient language practices, and analyzing their influence on antibiotic over-prescribing.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3023"}, {"primary_key": "3717127", "vector": [], "sparse_vector": [], "title": "Hybrid Neural Network Alignment and Lexicon Model in Direct HMM for Statistical Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recently, the neural machine translation systems showed their promising performance and surpassed the phrase-based systems for most translation tasks. Retreating into conventional concepts machine translation while utilizing effective neural models is vital for comprehending the leap accomplished by neural machine translation over phrase-based methods. This work proposes a direct HMM with neural network-based lexicon and alignment models, which are trained jointly using the Baum-Welch algorithm. The direct HMM is applied to rerank the n-best list created by a state-of-the-art phrase-based translation system and it provides improvements by up to 1.0% Bleu scores on two different translation tasks.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2020"}, {"primary_key": "3717128", "vector": [], "sparse_vector": [], "title": "Discourse Annotation of Non-native Spontaneous Spoken Responses Using the Rhetorical Structure Theory Framework.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The availability of the Rhetorical Structure Theory (RST) Discourse Treebank has spurred substantial research into discourse analysis of written texts; however, limited research has been conducted to date on RST annotation and parsing of spoken language, in particular, non-native spontaneous speech. Considering that the measurement of discourse coherence is typically a key metric in human scoring rubrics for assessments of spoken language, we initiated a research effort to obtain RST annotations of a large number of non-native spoken responses from a standardized assessment of academic English proficiency. The resulting inter-annotator kappa agreements on the three different levels of Span, Nuclearity, and Relation are 0.848, 0.766, and 0.653, respectively. Furthermore, a set of features was explored to evaluate the discourse structure of non-native spontaneous speech based on these annotations; the highest performing feature resulted in a correlation of 0.612 with scores of discourse coherence provided by expert human raters.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2041"}, {"primary_key": "3717129", "vector": [], "sparse_vector": [], "title": "Sentence Embedding for Neural Machine Translation Domain Adaptation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Although new corpora are becoming increasingly available for machine translation, only those that belong to the same or similar domains are typically able to improve translation performance. Recently Neural Machine Translation (NMT) has become prominent in the field. However, most of the existing domain adaptation methods only focus on phrase-based machine translation. In this paper, we exploit the NMT’s internal embedding of the source sentence and use the sentence embedding similarity to select the sentences which are close to in-domain data. The empirical adaptation results on the IWSLT English-French and NIST Chinese-English tasks show that the proposed methods can substantially improve NMT performance by 2.4-9.0 BLEU points, outperforming the existing state-of-the-art baseline by 2.3-4.5 BLEU points.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2089"}, {"primary_key": "3717131", "vector": [], "sparse_vector": [], "title": "Naturalizing a Programming Language via Interactive Learning.", "authors": ["Sid<PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Our goal is to create a convenient natural language interface for performing well-specified but complex actions such as analyzing data, manipulating text, and querying databases. However, existing natural language interfaces for such tasks are quite primitive compared to the power one wields with a programming language. To bridge this gap, we start with a core programming language and allow users to \"naturalize\" the core language incrementally by defining alternative, more natural syntax and increasingly complex concepts in terms of compositions of simpler ones. In a voxel world, we show that a community of users can simultaneously teach a common system a diverse language and use it to build hundreds of complex voxel structures. Over the course of three days, these users went from using only the core language to using the naturalized language in 85.9% of the last 10K utterances.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1086"}, {"primary_key": "3717132", "vector": [], "sparse_vector": [], "title": "Can Syntax Help? Improving an LSTM-based Sentence Compression Model for New Domains.", "authors": ["<PERSON><PERSON><PERSON>", "Jing <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1127"}, {"primary_key": "3717133", "vector": [], "sparse_vector": [], "title": "A Two-Stage Parsing Method for Text-Level Discourse Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous work introduced transition-based algorithms to form a unified architecture of parsing rhetorical structures (including span, nuclearity and relation), but did not achieve satisfactory performance. In this paper, we propose that transition-based model is more appropriate for parsing the naked discourse tree (i.e., identifying span and nuclearity) due to data sparsity. At the same time, we argue that relation labeling can benefit from naked tree structure and should be treated elaborately with consideration of three kinds of relations including within-sentence, across-sentence and across-paragraph relations. Thus, we design a pipelined two-stage parsing method for generating an RST tree from text. Experimental results show that our method achieves state-of-the-art performance, especially on span and nuclearity identification.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2029"}, {"primary_key": "3717134", "vector": [], "sparse_vector": [], "title": "Handling Cold-Start Problem in Review Spam Detection by Jointly Embedding Texts and Behaviors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Solving cold-start problem in review spam detection is an urgent and significant task. It can help the on-line review websites to relieve the damage of spammers in time, but has never been investigated by previous work. This paper proposes a novel neural network model to detect review spam for cold-start problem, by learning to represent the new reviewers’ review with jointly embedded textual and behavioral information. Experimental results prove the proposed model achieves an effective performance and possesses preferable domain-adaptability. It is also applicable to a large scale dataset in an unsupervised way.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1034"}, {"primary_key": "3717135", "vector": [], "sparse_vector": [], "title": "Deep Neural Machine Translation with Linear Associative Unit.", "authors": ["<PERSON><PERSON><PERSON>", "Zhengdong Lu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) have provably enhanced the state-of-the-art Neural Machine Translation (NMT) with its capability in modeling complex functions and capturing complex linguistic structures. However NMT with deep architecture in its encoder or decoder RNNs often suffer from severe gradient diffusion due to the non-linear recurrent activations, which often makes the optimization much more difficult. To address this problem we propose a novel linear associative units (LAU) to reduce the gradient propagation path inside the recurrent unit. Different from conventional approaches (LSTM unit and GRU), LAUs uses linear associative connections between input and output of the recurrent unit, which allows unimpeded information flow through both space and time The model is quite simple, but it is surprisingly effective. Our empirical study on Chinese-English translation shows that our model with proper configuration can improve by 11.7 BLEU upon Groundhog and the best reported on results in the same setting. On WMT14 English-German task and a larger WMT14 English-French task, our model achieves comparable results with the state-of-the-art.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1013"}, {"primary_key": "3717136", "vector": [], "sparse_vector": [], "title": "Gated Self-Matching Networks for Reading Comprehension and Question Answering.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present the gated self-matching networks for reading comprehension style question answering, which aims to answer questions from a given passage. We first match the question and passage with gated attention-based recurrent networks to obtain the question-aware passage representation. Then we propose a self-matching attention mechanism to refine the representation by matching the passage against itself, which effectively encodes information from the whole passage. We finally employ the pointer networks to locate the positions of answers from the passages. We conduct extensive experiments on the SQuAD dataset. The single model achieves 71.3% on the evaluation metrics of exact match on the hidden test set, while the ensemble model further boosts the results to 75.9%. At the time of submission of the paper, our model holds the first place on the SQuAD leaderboard for both single and ensemble model.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1018"}, {"primary_key": "3717137", "vector": [], "sparse_vector": [], "title": "Transductive Non-linear Learning for Chinese Hypernym Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> He"], "summary": "Finding the correct hypernyms for entities is essential for taxonomy learning, fine-grained entity categorization, query understanding, etc. Due to the flexibility of the Chinese language, it is challenging to identify hypernyms in Chinese accurately. Rather than extracting hypernyms from texts, in this paper, we present a transductive learning approach to establish mappings from entities to hypernyms in the embedding space directly. It combines linear and non-linear embedding projection models, with the capacity of encoding arbitrary language-specific rules. Experiments on real-world datasets illustrate that our approach outperforms previous methods for Chinese hypernym prediction.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1128"}, {"primary_key": "3717138", "vector": [], "sparse_vector": [], "title": "Universal Dependencies Parsing for Colloquial Singaporean English.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Singlish can be interesting to the ACL community both linguistically as a major creole based on English, and computationally for information extraction and sentiment analysis of regional social media. We investigate dependency parsing of Singlish by constructing a dependency treebank under the Universal Dependencies scheme, and then training a neural network model by integrating English syntactic knowledge into a state-of-the-art parser trained on the Singlish treebank. Results show that English knowledge can lead to 25% relative error reduction, resulting in a parser of 84.47% accuracies. To the best of our knowledge, we are the first to use neural stacking to improve cross-lingual dependency parsing on low-resource languages. We make both our annotation and parser available for further research.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1159"}, {"primary_key": "3717139", "vector": [], "sparse_vector": [], "title": "English Event Detection With Translated Language Features.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose novel radical features from automatic translation for event extraction. Event detection is a complex language processing task for which it is expensive to collect training data, making generalisation challenging. We derive meaningful subword features from automatic translations into target language. Results suggest this method is particularly useful when using languages with writing systems that facilitate easy decomposition into subword features, e.g., logograms and Cangjie. The best result combines logogram features from Chinese and Japanese with syllable features from Korean, providing an additional 3.0 points f-score when added to state-of-the-art generalisation features on the TAC KBP 2015 Event Nugget task.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2046"}, {"primary_key": "3717140", "vector": [], "sparse_vector": [], "title": "Revisiting Recurrent Networks for Paraphrastic Sentence Embeddings.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the problem of learning general-purpose, paraphrastic sentence embeddings, revisiting the setting of <PERSON><PERSON> et al. (2016b). While they found LSTM recurrent networks to underperform word averaging, we present several developments that together produce the opposite conclusion. These include training on sentence pairs rather than phrase pairs, averaging states to represent sequences, and regularizing aggressively. These improve LSTMs in both transfer learning and supervised settings. We also introduce a new recurrent architecture, the Gated Recurrent Averaging Network, that is inspired by averaging and LSTMs while outperforming them both. We analyze our learned models, finding evidence of preferences for particular parts of speech and dependency relations.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1190"}, {"primary_key": "3717141", "vector": [], "sparse_vector": [], "title": "Hybrid Code Networks: practical and efficient end-to-end dialog control with supervised and reinforcement learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "End-to-end learning of recurrent neural networks (RNNs) is an attractive solution for dialog systems; however, current techniques are data-intensive and require thousands of dialogs to learn simple behaviors. We introduce Hybrid Code Networks (HCNs), which combine an RNN with domain-specific knowledge encoded as software and system action templates. Compared to existing end-to-end approaches, HCNs considerably reduce the amount of training data required, while retaining the key benefit of inferring a latent representation of dialog state. In addition, HCNs can be optimized with supervised learning, reinforcement learning, or a mixture of both. HCNs attain state-of-the-art performance on the bAbI dialog dataset (Bordes and Weston, 2016), and outperform two commercially deployed customer-facing dialog systems at our company.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1062"}, {"primary_key": "3717142", "vector": [], "sparse_vector": [], "title": "Pocket Knowledge Base Population.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Existing Knowledge Base Population methods extract relations from a closed relational schema with limited coverage leading to sparse KBs. We propose Pocket Knowledge Base Population (PKBP), the task of dynamically constructing a KB of entities related to a query and finding the best characterization of relationships between entities. We describe novel Open Information Extraction methods which leverage the PKB to find informative trigger words. We evaluate using existing KBP shared-task data as well anew annotations collected for this work. Our methods produce high quality KB from just text with many more entities and relationships than existing KBP systems.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2048"}, {"primary_key": "3717143", "vector": [], "sparse_vector": [], "title": "Polish evaluation dataset for compositional distributional semantics models.", "authors": ["<PERSON><PERSON>", "Katarzyna <PERSON>-Ki<PERSON>s"], "summary": "The paper presents a procedure of building an evaluation dataset. for the validation of compositional distributional semantics models estimated for languages other than English. The procedure generally builds on steps designed to assemble the SICK corpus, which contains pairs of English sentences annotated for semantic relatedness and entailment, because we aim at building a comparable dataset. However, the implementation of particular building steps significantly differs from the original SICK design assumptions, which is caused by both lack of necessary extraneous resources for an investigated language and the need for language-specific transformation rules. The designed procedure is verified on Polish, a fusional language with a relatively free word order, and contributes to building a Polish evaluation dataset. The resource consists of 10K sentence pairs which are human-annotated for semantic relatedness and entailment. The dataset may be used for the evaluation of compositional distributional semantics models of Polish.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1073"}, {"primary_key": "3717144", "vector": [], "sparse_vector": [], "title": "Active Sentiment Domain Adaptation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Domain adaptation is an important technology to handle domain dependence problem in sentiment analysis field. Existing methods usually rely on sentiment classifiers trained in source domains. However, their performance may heavily decline if the distributions of sentiment features in source and target domains have significant difference. In this paper, we propose an active sentiment domain adaptation approach to handle this problem. Instead of the source domain sentiment classifiers, our approach adapts the general-purpose sentiment lexicons to target domain with the help of a small number of labeled samples which are selected and annotated in an active learning mode, as well as the domain-specific sentiment similarities among words mined from unlabeled samples of target domain. A unified model is proposed to fuse different types of sentiment information and train sentiment classifier for target domain. Extensive experiments on benchmark datasets show that our approach can train accurate sentiment classifier with less labeled samples.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1156"}, {"primary_key": "3717145", "vector": [], "sparse_vector": [], "title": "Improving Implicit Discourse Relation Recognition with Discourse-specific Word Embeddings.", "authors": ["<PERSON><PERSON>", "Xiaodong Shi", "<PERSON><PERSON> Chen", "Jinsong Su", "<PERSON><PERSON>"], "summary": "We introduce a simple and effective method to learn discourse-specific word embeddings (DSWE) for implicit discourse relation recognition. Specifically, DSWE is learned by performing connective classification on massive explicit discourse data, and capable of capturing discourse relationships between words. On the PDTB data set, using DSWE as features achieves significant improvements over baselines.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2042"}, {"primary_key": "3717146", "vector": [], "sparse_vector": [], "title": "Sequential Matching Network: A New Architecture for Multi-turn Response Selection in Retrieval-Based Chatbots.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study response selection for multi-turn conversation in retrieval based chatbots. Existing work either concatenates utterances in context or matches a response with a highly abstract context vector finally, which may lose relationships among the utterances or important information in the context. We propose a sequential matching network (SMN) to address both problems. SMN first matches a response with each utterance in the context on multiple levels of granularity, and distills important matching information from each pair as a vector with convolution and pooling operations. The vectors are then accumulated in a chronological order through a recurrent neural network (RNN) which models relationships among the utterances. The final matching score is calculated with the hidden states of the RNN. Empirical study on two public data sets shows that SMN can significantly outperform state-of-the-art methods for response selection in multi-turn conversation.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1046"}, {"primary_key": "3717147", "vector": [], "sparse_vector": [], "title": "Sequence-to-Dependency Neural Machine Translation.", "authors": ["Shuangzhi Wu", "<PERSON><PERSON> Zhang", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Nowadays a typical Neural Machine Translation (NMT) model generates translations from left to right as a linear sequence, during which latent syntactic structures of the target sentences are not explicitly concerned. Inspired by the success of using syntactic knowledge of target language for improving statistical machine translation, in this paper we propose a novel Sequence-to-Dependency Neural Machine Translation (SD-NMT) method, in which the target word sequence and its corresponding dependency structure are jointly constructed and modeled, and this structure is used as context to facilitate word generations. Experimental results show that the proposed method significantly outperforms state-of-the-art baselines on Chinese-English and Japanese-English translation tasks.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1065"}, {"primary_key": "3717148", "vector": [], "sparse_vector": [], "title": "A Progressive Learning Approach to Chinese SRL Using Heterogeneous Data.", "authors": ["Qiaolin Xia", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous studies on Chinese semantic role labeling (SRL) have concentrated on a single semantically annotated corpus. But the training data of single corpus is often limited. Whereas the other existing semantically annotated corpora for Chinese SRL are scattered across different annotation frameworks. But still, Data sparsity remains a bottleneck. This situation calls for larger training datasets, or effective approaches which can take advantage of highly heterogeneous data. In this paper, we focus mainly on the latter, that is, to improve Chinese SRL by using heterogeneous corpora together. We propose a novel progressive learning model which augments the Progressive Neural Network with Gated Recurrent Adapters. The model can accommodate heterogeneous inputs and effectively transfer knowledge between them. We also release a new corpus, Chinese SemBank, for Chinese SRL. Experiments on CPB 1.0 show that our model outperforms state-of-the-art methods.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1189"}, {"primary_key": "3717149", "vector": [], "sparse_vector": [], "title": "An Interpretable Knowledge Transfer Model for Knowledge Base Completion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Knowledge bases are important resources for a variety of natural language processing tasks but suffer from incompleteness. We propose a novel embedding model, ITransF, to perform knowledge base completion. Equipped with a sparse attention mechanism, ITransF discovers hidden concepts of relations and transfer statistical strength through the sharing of concepts. Moreover, the learned associations between relations and concepts, which are represented by sparse attention vectors, can be interpreted easily. We evaluate ITransF on two benchmark datasets—WN18 and FB15k for knowledge base completion and obtains improvements on both the mean rank and Hits@10 metrics, over all baselines that do not use additional information.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1088"}, {"primary_key": "3717150", "vector": [], "sparse_vector": [], "title": "A Constituent-Centric Neural Architecture for Reading Comprehension.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Reading comprehension (RC), aiming to understand natural texts and answer questions therein, is a challenging task. In this paper, we study the RC problem on the Stanford Question Answering Dataset (SQuAD). Observing from the training set that most correct answers are centered around constituents in the parse tree, we design a constituent-centric neural architecture where the generation of candidate answers and their representation learning are both based on constituents and guided by the parse tree. Under this architecture, the search space of candidate answers can be greatly reduced without sacrificing the coverage of correct answers and the syntactic, hierarchical and compositional structure among constituents can be well captured, which contributes to better representation learning of the candidate answers. On SQuAD, our method achieves the state of the art performance and the ablation study corroborates the effectiveness of individual modules.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1129"}, {"primary_key": "3717151", "vector": [], "sparse_vector": [], "title": "A Local Detection Approach for Named Entity Recognition and Mention Detection.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Sedtawut Watcharawittayakul"], "summary": "In this paper, we study a novel approach for named entity recognition (NER) and mention detection (MD) in natural language processing. Instead of treating NER as a sequence labeling problem, we propose a new local detection approach, which relies on the recent fixed-size ordinally forgetting encoding (FOFE) method to fully encode each sentence fragment and its left/right contexts into a fixed-size representation. Subsequently, a simple feedforward neural network (FFNN) is learned to either reject or predict entity label for each individual text fragment. The proposed method has been evaluated in several popular NER and MD tasks, including CoNLL 2003 NER task and TAC-KBP2015 and TAC-KBP2016 Tri-lingual Entity Discovery and Linking (EDL) tasks. Our method has yielded pretty strong performance in all of these examined tasks. This local detection approach has shown many advantages over the traditional sequence labeling methods.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1114"}, {"primary_key": "3717152", "vector": [], "sparse_vector": [], "title": "Spectral Analysis of Information Density in Dialogue Predicts Collaborative Task Performance.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We propose a perspective on dialogue that focuses on relative information contributions of conversation partners as a key to successful communication. We predict the success of collaborative task in English and Danish corpora of task-oriented dialogue. Two features are extracted from the frequency domain representations of the lexical entropy series of each interlocutor, power spectrum overlap (PSO) and relative phase (RP). We find that PSO is a negative predictor of task success, while RP is a positive one. An SVM with these features significantly improved on previous task success prediction models. Our findings suggest that the strategic distribution of information density between interlocutors is relevant to task success.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1058"}, {"primary_key": "3717153", "vector": [], "sparse_vector": [], "title": "Cross-lingual Distillation for Text Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cross-lingual text classification(CLTC) is the task of classifying documents written in different languages into the same taxonomy of categories. This paper presents a novel approach to CLTC that builds on model distillation, which adapts and extends a framework originally proposed for model compression. Using soft probabilistic predictions for the documents in a label-rich language as the (induced) supervisory labels in a parallel corpus of documents, we train classifiers successfully for new languages in which labeled training data are not available. An adversarial feature adaptation technique is also applied during the model training to reduce distribution mismatch. We conducted experiments on two benchmark CLTC datasets, treating English as the source language and German, French, Japan and Chinese as the unlabeled target languages. The proposed approach had the advantageous or comparable performance of the other state-of-art methods.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1130"}, {"primary_key": "3717154", "vector": [], "sparse_vector": [], "title": "Semi-Supervised QA with Generative Domain-Adaptive Nets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of semi-supervised question answering—utilizing unlabeled text to boost the performance of question answering models. We propose a novel training framework, the Generative Domain-Adaptive Nets. In this framework, we train a generative model to generate questions based on the unlabeled text, and combine model-generated questions with human-generated questions for training question answering models. We develop novel domain adaptation algorithms, based on reinforcement learning, to alleviate the discrepancy between the model-generated data distribution and the human-generated data distribution. Experiments show that our proposed framework obtains substantial improvement from unlabeled text.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1096"}, {"primary_key": "3717155", "vector": [], "sparse_vector": [], "title": "Leveraging Knowledge Bases in LSTMs for Improving Machine Reading.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper focuses on how to take advantage of external knowledge bases (KBs) to improve recurrent neural networks for machine reading. Traditional methods that exploit knowledge from KBs encode knowledge as discrete indicator features. Not only do these features generalize poorly, but they require task-specific feature engineering to achieve good performance. We propose KBLSTM, a novel neural model that leverages continuous representations of KBs to enhance the learning of recurrent neural networks for machine reading. To effectively integrate background knowledge with information from the currently processed text, our model employs an attention mechanism with a sentinel to adaptively decide whether to attend to background knowledge and which information from KBs is useful. Experimental results show that our model achieves accuracies that surpass the previous state-of-the-art results for both entity extraction and event extraction on the widely used ACE2005 dataset.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1132"}, {"primary_key": "3717156", "vector": [], "sparse_vector": [], "title": "Neural Word Segmentation with Rich Pretraining.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Neural word segmentation research has benefited from large-scale raw texts by leveraging them for pretraining character and word embeddings. On the other hand, statistical segmentation research has exploited richer sources of external information, such as punctuation, automatic segmentation and POS. We investigate the effectiveness of a range of external training sources for neural word segmentation by building a modular segmentation model, pretraining the most important submodule using rich external sources. Results show that such pretraining significantly improves the model, leading to accuracies competitive to the best methods on six benchmarks.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1078"}, {"primary_key": "3717157", "vector": [], "sparse_vector": [], "title": "Jointly Extracting Relations with Class Ties via Effective Deep Ranking.", "authors": ["Hai Ye", "<PERSON><PERSON>", "Zhunchen Luo", "<PERSON><PERSON>"], "summary": "Connections between relations in relation extraction, which we call class ties, are common. In distantly supervised scenario, one entity tuple may have multiple relation facts. Exploiting class ties between relations of one entity tuple will be promising for distantly supervised relation extraction. However, previous models are not effective or ignore to model this property. In this work, to effectively leverage class ties, we propose to make joint relation extraction with a unified model that integrates convolutional neural network (CNN) with a general pairwise ranking framework, in which three novel ranking loss functions are introduced. Additionally, an effective method is presented to relieve the severe class imbalance problem from NR (not relation) for model training. Experiments on a widely used dataset show that leveraging class ties will enhance extraction and demonstrate the effectiveness of our model to learn class ties. Our model outperforms the baselines significantly, achieving state-of-the-art performance.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1166"}, {"primary_key": "3717158", "vector": [], "sparse_vector": [], "title": "Determining Gains Acquired from Word Embedding Quantitatively Using Discrete Distribution Clustering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Word embeddings have become widely-used in document analysis. While a large number of models for mapping words to vector spaces have been developed, it remains undetermined how much net gain can be achieved over traditional approaches based on bag-of-words. In this paper, we propose a new document clustering approach by combining any word embedding with a state-of-the-art algorithm for clustering empirical distributions. By using the <PERSON><PERSON><PERSON> distance between distributions, the word-to-word semantic relationship is taken into account in a principled way. The new clustering method is easy to use and consistently outperforms other methods on a variety of data sets. More importantly, the method provides an effective framework for determining when and how much word embeddings contribute to document analysis. Experimental results with multiple embedding models are reported.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1169"}, {"primary_key": "3717159", "vector": [], "sparse_vector": [], "title": "A Syntactic Neural Model for General-Purpose Code Generation.", "authors": ["Pengcheng Yin", "<PERSON>"], "summary": "We consider the problem of parsing natural language descriptions into source code written in a general-purpose programming language like Python. Existing data-driven methods treat this problem as a language generation task without considering the underlying syntax of the target programming language. Informed by previous work in semantic parsing, in this paper we propose a novel neural architecture powered by a grammar model to explicitly capture the target syntax as prior knowledge. Experiments find this an effective way to scale up to generation of complex programs from natural language descriptions, achieving state-of-the-art results that well outperform previous code generation and semantic parsing approaches.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1041"}, {"primary_key": "3717160", "vector": [], "sparse_vector": [], "title": "Generic Axiomatization of Families of Noncrossing Graphs in Dependency Parsing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present a simple encoding for unlabeled noncrossing graphs and show how its latent counterpart helps us to represent several families of directed and undirected graphs used in syntactic and semantic parsing of natural language as context-free languages. The families are separated purely on the basis of forbidden patterns in latent encoding, eliminating the need to differentiate the families of non-crossing graphs in inference algorithms: one algorithm works for all when the search space can be controlled in parser input.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1160"}, {"primary_key": "3717161", "vector": [], "sparse_vector": [], "title": "A* CCG Parsing with a Supertag and Dependency Factored Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new A* CCG parsing model in which the probability of a tree is decomposed into factors of CCG categories and its syntactic dependencies both defined on bi-directional LSTMs. Our factored model allows the precomputation of all probabilities and runs very efficiently, while modeling sentence structures explicitly via dependencies. Our model achieves the state-of-the-art results on English and Japanese CCG parsing.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1026"}, {"primary_key": "3717162", "vector": [], "sparse_vector": [], "title": "STAIR Captions: Constructing a Large-Scale Japanese Image Caption Dataset.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In recent years, automatic generation of image descriptions (captions), that is, image captioning, has attracted a great deal of attention. In this paper, we particularly consider generating Japanese captions for images. Since most available caption datasets have been constructed for English language, there are few datasets for Japanese. To tackle this problem, we construct a large-scale Japanese image caption dataset based on images from MS-COCO, which is called STAIR Captions. STAIR Captions consists of 820,310 Japanese captions for 164,062 images. In the experiment, we show that a neural network trained using STAIR Captions can generate more natural and better Japanese captions, compared to those generated using English-Japanese machine translation after generating English captions.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2066"}, {"primary_key": "3717164", "vector": [], "sparse_vector": [], "title": "Learning to Skim Text.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>uo<PERSON> <PERSON><PERSON>"], "summary": "Recurrent Neural Networks are showing much promise in many sub-areas of natural language processing, ranging from document classification to machine translation to automatic question answering. Despite their promise, many recurrent models have to read the whole text word by word, making it slow to handle long documents. For example, it is difficult to use a recurrent network to read a book and answer questions about it. In this paper, we present an approach of reading text while skipping irrelevant information if needed. The underlying model is a recurrent network that learns how far to jump after reading a few words of the input text. We employ a standard policy gradient method to train the model to make discrete jumping decisions. In our benchmarks on four different tasks, including number prediction, sentiment analysis, news article classification and automatic Q&A, our proposed model, a modified LSTM with jumping, is up to 6 times faster than the standard sequential LSTM, while maintaining the same or even better accuracy.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1172"}, {"primary_key": "3717165", "vector": [], "sparse_vector": [], "title": "Character Composition Model with Convolutional Neural Networks for Dependency Parsing on Morphologically Rich Languages.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a transition-based dependency parser that uses a convolutional neural network to compose word representations from characters. The character composition model shows great improvement over the word-lookup model, especially for parsing agglutinative languages. These improvements are even better than using pre-trained word embeddings from extra data. On the SPMRL data sets, our system outperforms the previous best greedy parser (<PERSON><PERSON><PERSON> et. al, 2015) by a margin of 3% on average.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2106"}, {"primary_key": "3717166", "vector": [], "sparse_vector": [], "title": "Improved Neural Relation Detection for Knowledge Base Question Answering.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Relation detection is a core component of many NLP applications including Knowledge Base Question Answering (KBQA). In this paper, we propose a hierarchical recurrent neural network enhanced by residual learning which detects KB relations given an input question. Our method uses deep residual bidirectional LSTMs to compare questions and relation names via different levels of abstraction. Additionally, we propose a simple KBQA system that integrates entity linking and our proposed relation detector to make the two components enhance each other. Our experimental results show that our approach not only achieves outstanding relation detection performance, but more importantly, it helps our KBQA system achieve state-of-the-art accuracy for both single-relation (SimpleQuestions) and multi-relation (WebQSP) QA benchmarks.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1053"}, {"primary_key": "3717167", "vector": [], "sparse_vector": [], "title": "Obtaining referential word meanings from visual and distributional information: Experiments on object naming.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We investigate object naming, which is an important sub-task of referring expression generation on real-world images. As opposed to mutually exclusive labels used in object recognition, object names are more flexible, subject to communicative preferences and semantically related to each other. Therefore, we investigate models of referential word meaning that link visual to lexical information which we assume to be given through distributional word embeddings. We present a model that learns individual predictors for object names that link visual and distributional aspects of word meaning during training. We show that this is particularly beneficial for zero-shot learning, as compared to projecting visual objects directly into the distributional space. In a standard object naming task, we find that different ways of combining lexical and visual information achieve very similar performance, though experiments on model combination suggest that they capture complementary aspects of referential meaning.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1023"}, {"primary_key": "3717168", "vector": [], "sparse_vector": [], "title": "A Network Framework for Noisy Label Aggregation in Social Media.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Qing Li", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2077"}, {"primary_key": "3717169", "vector": [], "sparse_vector": [], "title": "Flexible and Creative Chinese Poetry Generation Using Neural Memory.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2017.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1125"}, {"primary_key": "3717170", "vector": [], "sparse_vector": [], "title": "A Corpus of Annotated Revisions for Studying Argumentative Writing.", "authors": ["<PERSON>", "Homa <PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents ArgRewrite, a corpus of between-draft revisions of argumentative essays. Drafts are manually aligned at the sentence level, and the writer’s purpose for each revision is annotated with categories analogous to those used in argument mining and discourse analysis. The corpus should enable advanced research in writing comparison and revision analysis, as demonstrated via our own studies of student revision behavior and of automatic revision purpose prediction.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1144"}, {"primary_key": "3717171", "vector": [], "sparse_vector": [], "title": "Word Embedding for Response-To-Text Assessment of Evidence.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Manually grading the Response to Text Assessment (RTA) is labor intensive.Therefore, an automatic method is being developed for scoring analytical writing when the RTA is administered in large numbers of classrooms.Our long-term goal is to also use this scoring method to provide formative feedback to students and teachers about students' writing quality.As a first step towards this goal, interpretable features for automatically scoring the evidence rubric of the RTA have been developed.In this paper, we present a simple but promising method for improving evidence scoring by employing the word embedding model.We evaluate our method on corpora of responses written by upper elementary students.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-3013"}, {"primary_key": "3717172", "vector": [], "sparse_vector": [], "title": "Adversarial Training for Unsupervised Bilingual Lexicon Induction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Maosong Sun"], "summary": "Word embeddings are well known to capture linguistic regularities of the language on which they are trained. Researchers also observe that these regularities can transfer across languages. However, previous endeavors to connect separate monolingual word embeddings typically require cross-lingual signals as supervision, either in the form of parallel corpus or seed lexicon. In this work, we show that such cross-lingual connection can actually be established without any form of supervision. We achieve this end by formulating the problem as a natural adversarial game, and investigating techniques that are crucial to successful training. We carry out evaluation on the unsupervised bilingual lexicon induction task. Even though this task appears intrinsically cross-lingual, we are able to demonstrate encouraging performance without any cross-lingual clues.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1179"}, {"primary_key": "3717173", "vector": [], "sparse_vector": [], "title": "Prior Knowledge Integration for Neural Machine Translation using Posterior Regularization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Maosong Sun"], "summary": "Although neural machine translation has made significant progress recently, how to integrate multiple overlapping, arbitrary prior knowledge sources remains a challenge. In this work, we propose to use posterior regularization to provide a general framework for integrating prior knowledge into neural machine translation. We represent prior knowledge sources as features in a log-linear model, which guides the learning processing of the neural translation model. Experiments on Chinese-English dataset show that our approach leads to significant improvements.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1139"}, {"primary_key": "3717174", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>: A Chinese Intelligent Conversational Robot.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Che", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recently, conversational robots are widely used in mobile terminals as the virtual assistant or companion.The goals of prevalent conversational robots mainly focus on four categories, namely chitchat, task completion, question answering and recommendation.In this paper, we present a Chinese intelligent conversational robot, <PERSON><PERSON>, which is designed to achieve these goals in a unified architecture.Moreover, it also has some featured functions such as diet map, implicit feedback based conversation, interactive machine reading, news recommendation, etc.Since the release of <PERSON><PERSON> at June 6, 2016, there are 2,505 users (till Feb 22, 2017) and 11,107 complete humanrobot conversations, which totally contain 198,998 single turn conversation pairs.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-4003"}, {"primary_key": "3717175", "vector": [], "sparse_vector": [], "title": "Exploiting Domain Knowledge via Grouped Weight Sharing with Application to Text Categorization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A fundamental advantage of neural models for NLP is their ability to learn representations from scratch. However, in practice this often means ignoring existing external linguistic resources, e.g., WordNet or domain specific ontologies such as the Unified Medical Language System (UMLS). We propose a general, novel method for exploiting such resources via weight sharing. Prior work on weight sharing in neural networks has considered it largely as a means of model compression. In contrast, we treat weight sharing as a flexible mechanism for incorporating prior knowledge into neural models. We show that this approach consistently yields improved performance on classification tasks compared to baseline strategies that do not exploit weight sharing.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2024"}, {"primary_key": "3717176", "vector": [], "sparse_vector": [], "title": "Incorporating Word Reordering Knowledge into Attention-based Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes three distortion models to explicitly incorporate the word reordering knowledge into attention-based Neural Machine Translation (NMT) for further improving translation performance. Our proposed models enable attention mechanism to attend to source words regarding both the semantic requirement and the word reordering penalty. Experiments on Chinese-English translation show that the approaches can improve word alignment quality and achieve significant translation improvements over a basic attention-based NMT by large margins. Compared with previous works on identical corpora, our system achieves the state-of-the-art performance on translation quality.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1140"}, {"primary_key": "3717177", "vector": [], "sparse_vector": [], "title": "Learning Discourse-level Diversity for Neural Dialog Models using Conditional Variational Autoencoders.", "authors": ["Tiancheng Zhao", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While recent neural encoder-decoder models have shown great promise in modeling open-domain conversations, they often generate dull and generic responses. Unlike past work that has focused on diversifying the output of the decoder from word-level to alleviate this problem, we present a novel framework based on conditional variational autoencoders that capture the discourse-level diversity in the encoder. Our model uses latent variables to learn a distribution over potential conversational intents and generates diverse responses using only greedy decoders. We have further developed a novel variant that is integrated with linguistic prior knowledge for better performance. Finally, the training procedure is improved through introducing a bag-of-word loss. Our proposed models have been validated to generate significantly more diverse responses than baseline approaches and exhibit competence of discourse-level decision-making.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1061"}, {"primary_key": "3717178", "vector": [], "sparse_vector": [], "title": "Joint Extraction of Entities and Relations Based on a Novel Tagging Scheme.", "authors": ["<PERSON><PERSON><PERSON> Zheng", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Joint extraction of entities and relations is an important task in information extraction. To tackle this problem, we firstly propose a novel tagging scheme that can convert the joint extraction task to a tagging problem.. Then, based on our tagging scheme, we study different end-to-end models to extract entities and their relations directly, without identifying entities and relations separately. We conduct experiments on a public dataset produced by distant supervision method and the experimental results show that the tagging based methods are better than most of the existing pipelined and joint learning methods. What’s more, the end-to-end model proposed in this paper, achieves the best results on the public dataset.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1113"}, {"primary_key": "3717179", "vector": [], "sparse_vector": [], "title": "Time Expression Analysis and Recognition Using Syntactic Token Types and General Heuristic Rules.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Extracting time expressions from free text is a fundamental task for many applications. We analyze the time expressions from four datasets and find that only a small group of words are used to express time information, and the words in time expressions demonstrate similar syntactic behaviour. Based on the findings, we propose a type-based approach, named SynTime, to recognize time expressions. Specifically, we define three main syntactic token types, namely time token, modifier, and numeral, to group time-related regular expressions over tokens. On the types we design general heuristic rules to recognize time expressions. In recognition, SynTime first identifies the time tokens from raw text, then searches their surroundings for modifiers and numerals to form time segments, and finally merges the time segments to time expressions. As a light-weight rule-based tagger, SynTime runs in real time, and can be easily expanded by simply adding keywords for the text of different types and of different domains. Experiment on benchmark datasets and tweets data shows that SynTime outperforms state-of-the-art methods.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1039"}, {"primary_key": "3717180", "vector": [], "sparse_vector": [], "title": "Neural System Combination for Machine Translation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chengqing Zong"], "summary": "Neural machine translation (NMT) becomes a new approach to machine translation and generates much more fluent results compared to statistical machine translation (SMT). However, SMT is usually better than NMT in translation adequacy. It is therefore a promising direction to combine the advantages of both NMT and SMT. In this paper, we propose a neural system combination framework leveraging multi-source NMT, which takes as input the outputs of NMT and SMT systems and produces the final translation. Extensive experiments on the Chinese-to-English translation task show that our model archives significant improvement by 5.3 BLEU points over the best single system output and 3.4 BLEU points over the state-of-the-art traditional system combination methods.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2060"}, {"primary_key": "3717181", "vector": [], "sparse_vector": [], "title": "Multi-space Variational Encoder-Decoders for Semi-supervised Labeled Sequence Transduction.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Labeled sequence transduction is a task of transforming one sequence into another sequence that satisfies desiderata specified by a set of labels. In this paper we propose multi-space variational encoder-decoders, a new model for labeled sequence transduction with semi-supervised learning. The generative model can use neural networks to handle both discrete and continuous latent variables to exploit various features of data. Experiments show that our model provides not only a powerful supervised framework but also can effectively take advantage of the unlabeled data. On the SIGMORPHON morphological inflection benchmark, our model outperforms single-model state-of-art results by a large margin for the majority of languages.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1029"}, {"primary_key": "3717182", "vector": [], "sparse_vector": [], "title": "Chunk-Based Bi-Scale Decoder for Neural Machine Translation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hang Li", "<PERSON><PERSON><PERSON>"], "summary": "In typical neural machine translation (NMT), the decoder generates a sentence word by word, packing all linguistic granularities in the same time-scale of RNN. In this paper, we propose a new type of decoder for NMT, which splits the decode state into two parts and updates them in two different time-scales. Specifically, we first predict a chunk time-scale state for phrasal modeling, on top of which multiple word time-scale states are generated. In this way, the target sentence is translated hierarchically from chunks to words, with information in different granularities being leveraged. Experiments show that our proposed model significantly improves the translation performance over the state-of-the-art NMT model.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-2092"}, {"primary_key": "3717183", "vector": [], "sparse_vector": [], "title": "Selective Encoding for Abstractive Sentence Summarization.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a selective encoding model to extend the sequence-to-sequence framework for abstractive sentence summarization. It consists of a sentence encoder, a selective gate network, and an attention equipped decoder. The sentence encoder and decoder are built with recurrent neural networks. The selective gate network constructs a second level sentence representation by controlling the information flow from encoder to decoder. The second level representation is tailored for sentence summarization task, which leads to better performance. We evaluate our model on the English Gigaword, DUC 2004 and MSR abstractive sentence summarization datasets. The experimental results show that the proposed selective encoding model outperforms the state-of-the-art baseline models.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-1101"}, {"primary_key": "3717184", "vector": [], "sparse_vector": [], "title": "Deep Learning for Semantic Composition.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Learning representation to model the meaning of text has been a core problem in NLP. The last several years have seen extensive interests on distributional approaches, in which text spans of different granularities are encoded as vectors of numerical values. If properly learned, such representation has showed to achieve the state-of-the-art performance on a wide range of NLP problems.In this tutorial, we will cover the fundamentals and the state-of-the-art research on neural network-based modeling for semantic composition, which aims to learn distributed representation for different granularities of text, e.g., phrases, sentences, or even documents, from their sub-component meaning representation, e.g., word embedding.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/P17-5003"}, {"primary_key": "3880916", "vector": [], "sparse_vector": [], "title": "A Versatile, Sound Tool for Simplifying Definitions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a tool, simplify-defun, that transforms the definition of a given function into a simplified definition of a new function, providing a proof checked by ACL2 that the old and new functions are equivalent. When appropriate it also generates termination and guard proofs for the new function. We explain how the tool is engineered so that these proofs will succeed. Examples illustrate its utility, in particular for program transformation in synthesis and verification.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.249.5"}, {"primary_key": "3880918", "vector": [], "sparse_vector": [], "title": "The Cayley-Dickson Construction in ACL2.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The Cayley-<PERSON> Construction is a generalization of the familiar construction of the complex numbers from pairs of real numbers. The complex numbers can be viewed as two-dimensional vectors equipped with a multiplication. The construction can be used to construct, not only the two-dimensional Complex Numbers, but also the four-dimensional Quaternions and the eight-dimensional Octonions. Each of these vector spaces has a vector multiplication, v_1*v_2, that satisfies: 1. Each nonzero vector has a multiplicative inverse. 2. For the Euclidean length of a vector |v|, |v_1 * v_2| = |v_1| |v2|. Real numbers can also be viewed as (one-dimensional) vectors with the above two properties. ACL2(r) is used to explore this question: Given a vector space, equipped with a multiplication, satisfying the Euclidean length condition 2, given above. Make pairs of vectors into \"new\" vectors with a multiplication. When do the newly constructed vectors also satisfy condition 2?", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.249.2"}, {"primary_key": "3880940", "vector": [], "sparse_vector": [], "title": "The x86isa Books: Features, Usage, and Future Plans.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The x86isa library, incorporated in the ACL2 community books project, provides a formal model of the x86 instruction-set architecture and supports reasoning about x86 machine-code programs. However, analyzing x86 programs can be daunting -- even for those familiar with program verification, in part due to the complexity of the x86 ISA. Furthermore, the x86isa library is a large framework, and using and/or contributing to it may not seem straightforward. We present some typical ways of working with the x86isa library, and describe some of its salient features that can make the analysis of x86 machine-code programs less arduous. We also discuss some capabilities that are currently missing from these books -- we hope that this will encourage the community to get involved in this project.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.249.1"}, {"primary_key": "3880952", "vector": [], "sparse_vector": [], "title": "Meta-extract: Using Existing Facts in Meta-reasoning.", "authors": ["<PERSON>", "Sol Swords"], "summary": "ACL2 has long supported user-defined simplifiers, so-called metafunctions and clause processors, which are installed when corresponding rules of class :meta or :clause-processor are proved. Historically, such simplifiers could access the logical world at execution time and could call certain built-in proof tools, but one could not assume the soundness of the proof tools or the truth of any facts extracted from the world or context when proving a simplifier correct. Starting with ACL2 Version 6.0, released in December 2012, an additional capability was added which allows the correctness proofs of simplifiers to assume the correctness of some such proof tools and extracted facts. In this paper we explain this capability and give examples that demonstrate its utility.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.249.4"}, {"primary_key": "3880981", "vector": [], "sparse_vector": [], "title": "A Computationally Surveyable Proof of the Group Properties of an Elliptic Curve.", "authors": ["<PERSON>"], "summary": "We present an elementary proof of the group properties of the elliptic curve known as \"Curve25519\", as a component of a comprehensive proof of correctness of a hardware implementation of the associated <PERSON><PERSON><PERSON><PERSON> key agreement algorithm. The entire proof has been formalized and mechanically verified with ACL2, and is computationally surveyable in the sense that all steps that require mechanical support are presented in such a way that they may readily reproduced in any suitable programming language.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.249.3"}, {"primary_key": "3880992", "vector": [], "sparse_vector": [], "title": "Proof Reduction of Fair Stuttering Refinement of Asynchronous Systems and Applications.", "authors": ["<PERSON>"], "summary": "We present a series of definitions and theorems demonstrating how to reduce the requirements for proving system refinements ensuring containment of fair stuttering runs. A primary result of the work is the ability to reduce the requisite proofs on runs of a system of interacting state machines to a set of definitions and checks on single steps of a small number of state machines corresponding to the intuitive notions of freedom from starvation and deadlock. We further refine the definitions to afford an efficient explicit-state checking procedure in certain finite state cases. We demonstrate the proof reduction on versions of the Bakery Algorithm.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.249.6"}, {"primary_key": "3880993", "vector": [], "sparse_vector": [], "title": "Term-Level Reasoning in Support of Bit-blasting.", "authors": ["Sol Swords"], "summary": "GL is a verified tool for proving ACL2 theorems using Boolean methods such as BDD reasoning and satisfiability checking. In its typical operation, GL recursively traverses a term, computing a symbolic object representing the value of each subterm. In older versions of GL, such a symbolic object could use Boolean functions to compactly represent many possible values for integer and Boolean subfields, but otherwise needed to reflect the concrete structure of all possiblealues that its term might take. When a term has many possible values that can't share such a representation, this can easily cause blowups because GL must then case-split. To address this problem, we have added several features to GL that allow it to reason about term-like symbolic objects using various forms of rewriting. These features allow GL to be programmed with rules much like the ACL2 rewriter, so that users may choose a better normal form for terms for which the default, value-like representation would otherwise cause case explosions. In this paper we describe these new features; as a motivating example, we show how to program the rewriter to reason effectively about the theory of records.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.4204/EPTCS.249.7"}, {"primary_key": "3885441", "vector": [], "sparse_vector": [], "title": "Proceedings 14th International Workshop on the ACL2 Theorem Prover and its Applications, Austin, Texas, USA, May 22-23, 2017.", "authors": ["<PERSON>", "<PERSON>."], "summary": "This volume contains the proceedings of the Fourteenth International Workshop on the ACL2 Theorem Prover and Its Applications, ACL2 2017, a two-day workshop held in Austin, Texas, USA, on May 22-23, 2017. ACL2 workshops occur at approximately 18-month intervals, and they provide a technical forum for researchers to present and discuss improvements and extensions to the theorem prover, comparisons of ACL2 with other systems, and applications of ACL2 in formal verification.ACL2 is a state-of-the-art automated reasoning system that has been successfully applied in academia, government, and industry for specification and verification of computing systems and in teaching computer science courses. <PERSON>, <PERSON>, and <PERSON> were awarded the 2005 ACM Software System Award for their work on ACL2 and the other theorem provers in the <PERSON>-<PERSON> theorem-prover family.The proceedings of ACL2 2017 include the seven technical papers and two extended abstracts that were presented at the workshop. Each submission received two or three reviews. The workshop also included three invited talks: \"Using Mechanized Mathematics in an Organization with a Simulation-Based Mentality\", by <PERSON> of Centaur Technology, Inc.; \"Formal Verification of Financial Algorithms, Progress and Prospects\", by <PERSON> of Aesthetic Integration; and \"Verifying Oracle's SPARC Processors with ACL2\" by <PERSON> of Oracle. The workshop also included several rump sessions discussing ongoing research and the use of ACL2 within industry.", "published": "2017-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": ""}]