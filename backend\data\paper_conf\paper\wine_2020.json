[{"primary_key": "2695491", "vector": [], "sparse_vector": [], "title": "Fair Division with Binary Valuations: One Rule to Rule Them All.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study fair allocation of indivisible goods among agents. Prior research focuses on additive agent preferences, which leads to an impossibility when seeking truthfulness, fairness, and efficiency. We show that when agents have binary additive preferences, a compelling rule—maximum Nash welfare (MNW)—provides all three guarantees. Specifically, we show that deterministic MNW with lexicographic tie-breaking is group strategyproof in addition to being envy-free up to one good and Pareto optimal. We also prove that fractional MNW—known to be group strategyproof, envy-free, and Pareto optimal—can be implemented as a distribution over deterministic MNW allocations, which are envy-free up to one good. Our work establishes maximum Nash welfare as the ultimate allocation rule in the realm of binary additive preferences.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_26"}, {"primary_key": "2695492", "vector": [], "sparse_vector": [], "title": "The Price of Anarchy of Two-Buyer Sequential Multiunit Auctions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study the efficiency of sequential multiunit auctions with two buyers and complete information. For general valuation functions, we show that theprice of anarchyis exactly 1/Tfor auctions withTitems for sale. For concave valuation functions, we show that the price of anarchy is bounded below by\\(1-1/e\\simeq 0.632\\). This bound is asymptotically tight as the number of items sold tends to infinity.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_11"}, {"primary_key": "2695493", "vector": [], "sparse_vector": [], "title": "Revenue Monotonicity Under Misspecified Bidders.", "authors": ["<PERSON><PERSON>", "Odysseas Drosis", "<PERSON>"], "summary": "We investigate revenue guarantees for auction mechanisms in a model where a distribution is specified for each bidder, but only some of the distributions are correct. The subset of bidders whose distribution is correctly specified (henceforth, the “green bidders”) is unknown to the auctioneer. The question we address is whether the auctioneer can run a mechanism that is guaranteed to obtain at least as much revenue, in expectation, as would be obtained by running an optimal mechanism on the green bidders only. For single-parameter feasibility environments, we find that the answer depends on the feasibility constraint. For matroid environments, running the optimal mechanism using all the specified distributions (including the incorrect ones) guarantees at least as much revenue in expectation as running the optimal mechanism on the green bidders. For any feasibility constraint that is not a matroid, there exists a way of setting the specified distributions and the true distributions such that the opposite conclusion holds.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_14"}, {"primary_key": "2695495", "vector": [], "sparse_vector": [], "title": "Simultaneously Achieving Ex-ante and Ex-post Fairness.", "authors": ["<PERSON><PERSON>"], "summary": "We present a polynomial-time algorithm that computes an ex-ante envy-free lottery over envy-free up to one item (EF1) deterministic allocations. It has the following advantages over a recently proposed algorithm: it does not rely on the linear programming machinery including separation oracles; it is SD-efficient (both ex-ante and ex-post); and the ex-ante outcome is equivalent to the outcome returned by the well-known probabilistic serial rule. As a result, we answer a question raised by <PERSON>, <PERSON>, and <PERSON> (2020) whether the outcome of the probabilistic serial rule can be implemented by ex-post EF1 allocations. In the light of a couple of impossibility results that we prove, our algorithm can be viewed as satisfying a maximal set of properties. Under binary utilities, our algorithm is also ex-ante group-strategyproof and ex-ante Pareto optimal. Finally, we also show that checking whether a given random allocation can be implemented by a lottery over EF1 and Pareto optimal allocations is NP-hard.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_24"}, {"primary_key": "2695496", "vector": [], "sparse_vector": [], "title": "Optimal Bounds on the Price of Fairness for Indivisible Goods.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the allocation of resources to a set of agents, how do fairness guarantees impact social welfare? A quantitative measure of this impact is the price of fairness, which measures the worst-case loss of social welfare due to fairness constraints. While initially studied for divisible goods, recent work on the price of fairness also studies the setting of indivisible goods. In this paper, we resolve the price of two well-studied fairness notions in the context of indivisible goods: envy-freeness up to one good (\\(\\textsc {EF1} \\)) and approximate maximin share (\\(\\textsc {MMS} \\)). For both\\(\\textsc {EF1} \\)andwe show, via different techniques, that the price of fairness is\\(O(\\sqrt{n})\\), wherenis the number of agents. From previous work, it follows that these guarantees are tight. We, in fact, obtain the price-of-fairness results via efficient algorithms. Forour bound holds for additive valuations, whereas for\\(\\textsc {EF1} \\), it holds for the more general class of subadditive valuations. This resolves an open problem posed by <PERSON><PERSON> et al. (2019).", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_25"}, {"primary_key": "2695497", "vector": [], "sparse_vector": [], "title": "Competitively Pricing Parking in a Tree.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by demand-responsive parking pricing systems we consider posted-price algorithms for the online metrical matching problem and the online metrical searching problem in a tree metric. Our main result is a poly-log competitive posted-price algorithm for online metrical searching.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_16"}, {"primary_key": "2695498", "vector": [], "sparse_vector": [], "title": "Data-Driven Models of Selfish Routing: Why Price of Anarchy Does Depend on Network Topology.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate traffic routing both from the perspective of real world data as well as theory. First, we reveal through data analytics a natural but previously uncaptured regularity of real world routing behavior. Agents only consider, in their strategy sets, paths whose free-flow costs (informally their lengths) are within a small multiplicative\\((1+\\theta )\\)constant of the optimal free-flow cost path connecting their source and destination where\\(\\theta \\ge 0\\). In the case of Singapore,\\(\\theta =1\\)is a good estimate of agents’ route (pre)selection mechanism. In contrast, in Pigou networks the ratio of the free-flow costs of the routes and thus\\(\\theta \\)isinfinite, so although such worst case networks are mathematically simple they correspond to artificial routing scenarios with little resemblance to real world conditions, opening the possibility of proving much stronger Price of Anarchy guarantees by explicitly studying their dependency on\\(\\theta \\). We provide an exhaustive analysis of this question by providing provably tight bounds on PoA(\\(\\theta \\)) for arbitrary classes of cost functions both in the case of general congestion/routing games as well as in the special case of path-disjoint networks. For example, in the case of the standard Bureau of Public Roads (BPR) cost model,\\(c_e(x)= a_e x^4+b_e\\)and more generally quartic cost functions, the standard PoA bound for\\(\\theta =\\infty \\)is 2.1505 \n[21] and it is tight both for general networks as well as path-disjoint and even parallel-edge networks. In comparison, in the case of\\(\\theta =1\\), the PoA in the case of general networks is only 1.6994, whereas for path-disjoint/parallel-edge networks is even smaller (1.3652), showing that both the route geometries as captured by the parameter\\(\\theta \\)as well as the network topology have significant effects on PoA (Fig.1).", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_18"}, {"primary_key": "2695499", "vector": [], "sparse_vector": [], "title": "On the Power and Limits of Dynamic Pricing in Combinatorial Markets.", "authors": ["<PERSON>", "Alon Eden", "<PERSON><PERSON>"], "summary": "We study the power and limits ofoptimal dynamic pricingin combinatorial markets; i.e., dynamic pricing that leads to optimal social welfare. Previous work by <PERSON><PERSON><PERSON>.[EC’16] demonstrated the existence of optimal dynamic prices for unit-demand buyers, and showed a market with coverage valuations that admits no such prices. However, finding the most general class of markets (i.e., valuation functions) that admit optimal dynamic prices remains an open problem. In this work we establish positive and negative results that narrow the existing gap. On the positive side, we provide tools for handling markets beyond unit-demand valuations. In particular, we characterize all optimal allocations in multi-demand markets. This characterization allows us to partition the items into equivalence classes according to the role they play in achieving optimality. Using these tools, we provide a poly-time optimal dynamic pricing algorithm for up to 3 multi-demand buyers. On the negative side, we establish a maximal domain theorem, showing that for every non-gross substitutes valuation, there exist unit-demand valuations such that adding them yields a market that does not admit an optimal dynamic pricing. This result is the dynamic pricing equivalent of the seminal maximal domain theorem by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [<PERSON><PERSON>’99] for Walrasian equilibrium. <PERSON> [JET’17] discovered an error in their original proof, and established a different, incomparable version of their maximal domain theorem. En route to our maximal domain theorem for optimal dynamic pricing, we provide the first complete proof of the original theorem by <PERSON><PERSON> and <PERSON><PERSON>.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_15"}, {"primary_key": "2695500", "vector": [], "sparse_vector": [], "title": "Nash Social Welfare in Selfish and Online Load Balancing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In load balancing problems there is a set of clients, each wishing to select a resource from a set of permissible ones, in order to execute a certain task. Each resource has a latency function, which depends on its workload, and a client’s cost is the completion time of her chosen resource. Two fundamental variants of load balancing problems areselfish load balancing(aka.load balancing games), where clients are non-cooperative selfish players aimed at minimizing their own cost solely, andonline load balancing, where clients appear online and have to be irrevocably assigned to a resource without any knowledge about future requests. We revisit both selfish and online load balancing under the objective of minimizing theNash Social Welfare, i.e., the geometric mean of the clients’ costs. To the best of our knowledge, despite being a celebrated welfare estimator in many social contexts, the Nash Social Welfare has not been considered so far as a benchmarking quality measure in load balancing problems. We provide tight bounds on the price of anarchy of pure Nash equilibria and on the competitive ratio of the greedy algorithm under very general latency functions, including polynomial ones. For this particular class, we also prove that the greedy strategy is optimal as it matches the performance of any possible online algorithm.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_23"}, {"primary_key": "2695501", "vector": [], "sparse_vector": [], "title": "A Fine-Grained View on Stable Many-To-One Matching Problems with Lower and Upper Quotas.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the Hospital Residents problem with lower and upper quotas (HR-\\({Q}_{L}^{U}\\)), the goal is to find a stable matching of residents to hospitals where the number of residents matched to a hospital is either between its lower and upper quota or zero [<PERSON><PERSON><PERSON> et al., TCS 2010]. We analyze this problem from a parameterized perspective using several natural parameters such as the number of hospitals and the number of residents. Moreover, we present a polynomial-time algorithm that finds a stable matching if it exists on instances with maximum lower quota two. AlongsideHR-\\({Q}_{L}^{U}\\), we also consider two closely related models of independent interest, namely, the special case ofHR-\\({Q}_{L}^{U}\\)where each hospital has only a lower quota but no upper quota and the variation ofHR-\\({Q}_{L}^{U}\\)where hospitals do not have preferences over residents, which is also known as the House Allocation problem with lower and upper quotas.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_3"}, {"primary_key": "2695502", "vector": [], "sparse_vector": [], "title": "Multidimensional Stable Roommates with Master List.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Since the early days of research in algorithms and complexity, the computation of stable matchings is a core topic. While in the classic setting the goal is to match up two agents (either from different “gender” (this isStable Marriage) or “unrestricted” (this isStable Roommates)), <PERSON><PERSON><PERSON> [1976] triggered the study of three- or multidimensional cases. Here, we focus on the study ofMultidimensional Stable Roommates, known to be\\(\\mathsf {NP}\\)-hard since the early 1990’s. Many\\(\\mathsf {NP}\\)-hardness results, however, rely on very general input instances that do not occur in at least some of the specific application scenarios. With the quest for identifying islands of tractability, we look at the case of master lists. Here, as natural in applications where agents express their preferences based on “objective” scores, one roughly speaking assumes that all agent preferences are “derived from” a central master list, implying that the individual agent preferences shall be similar. Master lists have been frequently studied in the two-dimensional (classic) stable matching case, but seemingly almost never for the multidimensional case. This work, also relying on methods from parameterized algorithm design and complexity analysis, performs a first systematic study ofMultidimensional Stable Roommatesunder the assumption of master lists.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_5"}, {"primary_key": "2695503", "vector": [], "sparse_vector": [], "title": "Sequential Solutions in Machine Scheduling Games.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the classical machine scheduling, wherenjobs need to be scheduled onmmachines, and where jobjscheduled on machineicontributes\\(p_{i,j}\\in \\mathbb {R}\\)to the load of machinei, with the goal of minimizing the makespan, i.e., the maximum load of any machine in the schedule. We study inefficiency of schedules that are obtained when jobs arrive sequentially one by one, and the jobs choose themselves the machine on which they will be scheduled, aiming at being scheduled on a machine with small load. We measure the inefficiency of a schedule as the ratio of the makespan obtained in the worst-case equilibrium schedule, and of the optimum makespan. This ratio is known as thesequential price of anarchy (SPoA). We also introduce two alternative inefficiency measures, which allow for a favorable choice of the order in which the jobs make their decisions. As our first result, we disprove the conjecture of \n[22] claiming that the sequential price of anarchy for\\(m=2\\)machines is at most 3. We show that the sequential price of anarchy grows at least linearly with the numbernof players, assuming arbitrary tie-breaking rules. That is, we show\\(\\mathbf{SPoA} \\in \\varOmega (n)\\). Complementing this result, we show that\\(\\mathbf{SPoA} \\in O(n)\\), reducing previously known exponential bound for 2 machines. Furthermore, we show that there exists an order of the jobs, resulting in makespan that is at most linearly larger than the optimum makespan. To the end, we show that if an authority can change the order of the jobs adaptively to the decisions made by the jobs so far (but cannot influence the decisions of the jobs), then there exists an adaptive ordering in which the jobs end up in an optimum schedule.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_22"}, {"primary_key": "2695504", "vector": [], "sparse_vector": [], "title": "The Curse of Rationality in Sequential Scheduling Games.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Xu"], "summary": "Despite the emphases on computability issues in research of algorithmic game theory, the limited computational capacity of players have received far less attention. This work examines how different levels of players’ computational ability (or “rationality”) impact the outcomes ofsequential scheduling games. Surprisingly, our results show that a lower level of rationality of players may lead to better equilibria. More specifically, we characterize the sequential price of anarchy (SPoA) under two different models of bounded rationality, namely,players with k-lookaheadandsimple-minded players. The model in which players havek-lookahead interpolates between the “perfect rationality” (\\(k=n-1\\)) and “online greedy” (\\(k=0\\)). Our results show that the inefficiency of equilibria (SPoA) increases inkthe degree of lookahead:\\(\\mathrm {SPoA} = O (k^2)\\)for two machines and\\(\\mathrm {SPoA} = O\\left( 2^k \\min \\{mk,n\\}\\right) \\)formmachines, wherenis the number of players. Moreover, when players are simple-minded, the SPoA is exactlym, which coincides with the performance of “online greedy”.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_21"}, {"primary_key": "2695505", "vector": [], "sparse_vector": [], "title": "The Ad Types Problem.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we introduce theAd Types Problem, a generalization of the traditional positional auction model for ad allocation that better captures some of the challenges that arise when ads of different types need to be interspersed within a user feed of organic content. The Ad Types problem (without gap rules) is a special case of the assignment problem in which there arektypes of nodes on one side (the ads), and an ordered set of nodes on the other side (the slots). The edge weight of an adiof type\\(\\theta \\)to slotjis\\(v_i\\cdot \\alpha ^{\\theta }_j\\)where\\(v_i\\)is an advertiser-specific value and each ad type\\(\\theta \\)has a discount curve\\(\\alpha ^{(\\theta )}_{1} \\ge \\alpha ^{(\\theta )}_{2} \\ge \\ldots \\ge 0\\)over the slots that is common for ads of type\\(\\theta \\). We present two contributions for this problem: 1) we give an algorithm that finds the maximum weight matching that runs in\\(O(n^2(k + \\log n))\\)time fornslots andnads of each type—cf.\\(O(kn^3)\\)when using the Hungarian algorithm—, and 2) we show how to apply reserve prices in total time\\(O(n^3(k + \\log n))\\). The Ad Types Problem (with gap rules) includes a matrixGsuch that after we show an ad of type\\(\\theta _i\\), the next\\(G_{ij}\\)slots cannot show an ad of type\\(\\theta _j\\). We show that the problem is hard to approximate within\\(k^{1- \\epsilon }\\)for any\\(\\epsilon > 0\\)(even without discount curves) by reduction from Maximum Independent Set. On the positive side, we show a Dynamic Program formulation that solves the problem (including discount curves) optimally and runs in\\(O(k\\cdot n^{2k + 1})\\)time.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_4"}, {"primary_key": "2695506", "vector": [], "sparse_vector": [], "title": "Dynamic Weighted Matching with Heterogeneous Arrival and Departure Rates.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study a dynamic non-bipartite matching problem. There is a fixed set of agent types, and agents of a given type arrive and depart according to type-specific Poisson processes. The value of a match is determined by the types of the matched agents. We present an online algorithm that is (1/8)-competitive with respect to the value of the optimal-in-hindsight policy, for arbitrary weighted graphs. This is the first result to achieve a constant competitive ratio when both arrivals and departures are random and unannounced. Our algorithm treats agents heterogeneously, interpolating between immediate and delayed matching in order to thicken the market while still matching valuable agents opportunistically.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_2"}, {"primary_key": "2695507", "vector": [], "sparse_vector": [], "title": "Bayesian Repeated Zero-Sum Games with Persistent State, with Application to Security Games.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study infinitely-repeated two-player zero-sum games with one-sided private information and a persistent state. Here, only one of the two players learns the state of the repeated game. We consider two models: either the state is chosen by nature, or by one of the players. For the former, the equilibrium of the repeated game is known to be equivalent to that of a one-shot public signaling game, and we make this equivalence algorithmic. For the latter, we show equivalence to one-shot team max-min games, and also provide an algorithmic reduction. We apply this framework to repeated zero-sum security games with private information on the side of the defender and provide an almost complete characterization of their computational complexity.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_31"}, {"primary_key": "2695508", "vector": [], "sparse_vector": [], "title": "Robust Revenue Maximization Under Minimal Statistical Information.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON>"], "summary": "We study the problem of multi-dimensional revenue maximization when sellingmitems to a buyer that has additive valuations for them, drawn from a (possibly correlated) prior distribution. Unlike traditional Bayesian auction design, we assume that the seller has a very restricted knowledge of this prior: they only know the mean\\(\\mu _j\\)and an upper bound\\(\\sigma _j\\)on the standard deviation of each item’s marginal distribution. Our goal is to design mechanisms that achieve good revenue against an ideal optimal auction that hasfullknowledge of the distribution in advance. Informally, our main contribution is a tight quantification of the interplay between the dispersity of the priors and the aforementioned robust approximation ratio. Furthermore, this can be achieved by very simple selling mechanisms. More precisely, we show that selling the items via separate price lotteries achieves an\\(O(\\log r)\\)approximation ratio where\\(r=\\max _j(\\sigma _j/\\mu _j)\\)is the maximum coefficient of variation across the items. If forced to restrict ourselves to deterministic mechanisms, this guarantee degrades to\\(O(r^2)\\). Assuming independence of the item valuations, these ratios can be further improved by pricing the full bundle. For the case of identical means and variances, in particular, we get a guarantee of\\(O(\\log (r/m))\\)which converges to optimality as the number of items grows large. We demonstrate the optimality of the above mechanisms by providing matching lower bounds. Our tight analysis for the deterministic case resolves an open gap from the work of <PERSON><PERSON> and <PERSON><PERSON><PERSON> [ITCS’13].", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_13"}, {"primary_key": "2695509", "vector": [], "sparse_vector": [], "title": "Counteracting Inequality in Markets via Convex Pricing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study market mechanisms for allocating divisible goods to competing agents with quasilinear utilities. Forlinearpricing (i.e., the cost of a good is proportional to the quantity purchased), the First Welfare Theorem states that Walrasian equilibria maximize the sum of agent valuations. This ensures efficiency, but can lead to extreme inequality across individuals. Many real-world markets – especially for water – useconvexpricing instead, often known as increasing block tariffs (IBTs). IBTs are thought to promote equality, but there is a dearth of theoretical support for this claim. In this paper, we study a simple convex pricing rule and show that the resulting equilibria are guaranteed to maximize a CES welfare function. Furthermore, a parameter of the pricing rule directly determines which CES welfare function is implemented; by tweaking this parameter, the social planner can precisely control the tradeoff between equality and efficiency. Our result holds for any valuations that are homogeneous, differentiable, and concave. We also give an iterative algorithm for computing these pricing rules, derive a truthful mechanism for the case of a single good, and discuss Sybil attacks.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_7"}, {"primary_key": "2695510", "vector": [], "sparse_vector": [], "title": "Consensus Halving for Sets of Items.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Consensus halving refers to the problem of dividing a resource into two parts so that every agent values both parts equally. Prior work has shown that when the resource is represented by an interval, a consensus halving with at mostncuts always exists, but is hard to compute even for agents with simple valuation functions. In this paper, we study consensus halving in a natural setting where the resource consists of a set of items without a linear ordering. When agents have additive utilities, we present a polynomial-time algorithm that computes a consensus halving with at mostncuts, and show thatncuts are almost surely necessary when the agents’ utilities are drawn from probabilistic distributions. On the other hand, we show that for a simple class of monotonic utilities, the problem already becomes PPAD-hard. Furthermore, we compare and contrast consensus halving with the more general problem of consensusk-splitting, where we wish to divide the resource intokparts in possibly unequal ratios, and provide some consequences of our results on the problem of computing small agreeable sets.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_27"}, {"primary_key": "2695511", "vector": [], "sparse_vector": [], "title": "Learning Strong Substitutes Demand via Queries.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper addresses the computational challenges of learning strong substitutes demand when given access to a demand (or valuation) oracle. Strong substitutes demand generalises the well-studied gross substitutes demand to a multi-unit setting. Recent work by <PERSON> and <PERSON> shows that any such demand can be expressed in a natural way as a finite list of weighted bid vectors. A simplified version of this bidding language has been used by the Bank of England. Assuming access to a demand oracle, we provide an algorithm that computes the unique list of weighted bid vectors corresponding to a bidder’s demand preferences. In the special case where their demand can be expressed using positive bids only, we have an efficient algorithm that learns this list in linear time. We also show super-polynomial lower bounds on the query complexity of computing the list of bids in the general case where bids may be positive and negative. Our algorithms constitute the first systematic approach for bidders to construct a bid list corresponding to non-trivial demand, allowing them to participate in ‘product-mix’ auctions.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_28"}, {"primary_key": "2695512", "vector": [], "sparse_vector": [], "title": "Almost Envy-Free Repeated Matching in Two-Sided Markets.", "authors": ["Sreenivas Gollapudi", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A two-sided market consists of two sets of agents, each of whom have preferences over the other (Airbnb, Upwork, Lyft, Uber, etc.). We propose and analyze a repeated matching problem, where some set of matches occur on each time step, and our goal is to ensure fairness with respect to the cumulative allocations over an infinite time horizon. Our main result is a polynomial-time algorithm for additive, symmetric (\\(v_i(j) = v_j(i)\\)), and binary (\\(v_i(j) \\in \\{a,1\\}\\)) valuations that both (1) guaranteesenvy-freeness up to a single match(EF1) and (2) selects a maximum weight matching on each time step. Thus for this class of valuations, fairness can be achieved without sacrificing economic efficiency. This result holds even fordynamic valuations, i.e., valuations that change over time. Although symmetry is a strong assumption, we show that this result cannot be extended to asymmetric binary valuations: (1) and (2) together are impossible even when valuations do not change over time, and for dynamic valuations, even (1) alone is impossible. To our knowledge, this is the first analysis of envy-freeness in a repeated matching setting.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_1"}, {"primary_key": "2695513", "vector": [], "sparse_vector": [], "title": "The Price of Anarchy for Instantaneous Dynamic Equilibria.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We consider flows over time within the deterministic queueing model and study the solution concept of instantaneous dynamic equilibrium (IDE) in which flow particles select at every decision point a currently shortest path. The length of such a path is measured by the physical travel time plus the time spent in queues. Although IDE have been studied since the eighties, the efficiency of the solution concept is not well understood. We study the price of anarchy for this model and show an upper bound of order\\(\\mathcal {O}(U\\cdot \\tau )\\)for single-sink instances, whereUdenotes the total inflow volume and\\(\\tau \\)the sum of edge travel times. We complement this upper bound with a family of quite complex instances proving a lower bound of order\\(\\varOmega (U\\cdot \\log \\tau )\\).", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_17"}, {"primary_key": "2695514", "vector": [], "sparse_vector": [], "title": "Markets for Efficient Public Good Allocation with Social Distancing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Public goods are often either over-consumed in the absence of regulatory mechanisms, or remain completely unused, as in the Covid-19 pandemic, where social distance constraints are enforced to limit the number of people who can share public spaces. In this work, we plug this gap through market mechanisms designed to efficiently allocate capacity constrained public goods. To design these mechanisms, we leverage the theory of Fisher markets, wherein each agent is endowed with an artificial currency budget that they can spend to avail public goods. While Fisher markets provide a strong methodological backbone to model resource allocation problems, their applicability is limited to settings involving two types of constraints - budgets of individual buyers and capacities of goods. Thus, we introduce a modified Fisher market, where each individual may have additional physical constraints, characterize its solution properties and establish the existence of a market equilibrium. Furthermore, to account for additional constraints we introduce a social convex optimization problem where we perturb the budgets of agents such that the KKT conditions of the perturbed social problem establishes equilibrium prices. Finally, to compute the budget perturbations we present a fixed point scheme and illustrate convergence guarantees through numerical experiments. Thus, our mechanism, both theoretically and computationally, overcomes a fundamental limitation of classical Fisher markets, which only consider capacity and budget constraints.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_8"}, {"primary_key": "2695516", "vector": [], "sparse_vector": [], "title": "A Cardinal Comparison of Experts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In various situations, decision makers face experts that may provide conflicting advice. This advice may be in the form of probabilistic forecasts over critical future events. We consider a setting where the two forecasters provide their advice repeatedly and ask whether the decision maker can learn to compare and rank the two forecasters based on past performance. We take an axiomatic approach and propose three natural axioms that a comparison test should comply with. We propose a test that complies with our axioms. Perhaps, not surprisingly, this test is closely related to the likelihood ratio of the two forecasts over the realized sequence of events. More surprisingly, this test is essentially unique. Furthermore, using results on the rate of convergence of supermartingales, we show that whenever the two experts’ advice are sufficiently distinct, the proposed test will detect the informed expert in any desired degree of precision in some fixed finite time.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_29"}, {"primary_key": "2695520", "vector": [], "sparse_vector": [], "title": "Revenue-Optimal Deterministic Auctions for Multiple Buyers with Ordinal Preferences over Fixed-Price Items.", "authors": ["<PERSON>"], "summary": "In this paper, we introduce a Bayesian revenue-maximizing mechanism design model where the items have fixed, exogenously-given prices. Buyers are unit-demand and have an ordinal ranking over purchasing either one of these items at its given price, or purchasing nothing. This model arises naturally from the assortment optimization problem, in that the single-buyer optimization problem over deterministic mechanisms reduces to deciding on an assortment of items to “show”. We study its multi-buyer generalization in the simplest setting of single-winner auctions, or more broadly, any service-constrained environment. Our main result is that if the buyer rankings are drawn independently from Markov Chain ranking models, then the optimal mechanism is computationally tractable, and structurally a virtual welfare maximizer. We also show that for ranking distributions not induced by Markov Chains, the optimal mechanism may not be a virtual welfare maximizer.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_12"}, {"primary_key": "2695523", "vector": [], "sparse_vector": [], "title": "Minimum-Regret Contracts for Principal-Expert Problems.", "authors": ["Caspar O<PERSON>held", "<PERSON>"], "summary": "We consider a principal-expert problem in which a principal contracts one or more experts to acquire and report decision-relevant information. The principal never finds out what information is available to which expert, at what costs that information is available, or what costs the experts actually end up paying. This makes it challenging for the principal to compensate the experts in a way that incentivizes acquisition of relevant information without overpaying. We determine the payment scheme that minimizes the principal’s worst-case regret relative to the first-best solution. In particular, we show that under two different assumptions about the experts’ available information, the optimal payment scheme is a set of linear contracts.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_30"}, {"primary_key": "2695526", "vector": [], "sparse_vector": [], "title": "Optimal Nash Equilibria for Bandwidth Allocation.", "authors": ["<PERSON>"], "summary": "In bandwidth allocation, competing agents wish to transmit data along paths of links in a network, and each agent’s utility is equal to the minimum bandwidth she receives among all links in her desired path. Recent market mechanisms for this problem have either focused on only Nash welfare \n[9], or ignored strategic behavior \n[21]. We propose a nonlinear variant of the classic trading post mechanism, and show that for almost the entire family of CES welfare functions (which includes maxmin welfare, Nash welfare, and utilitarian welfare), every Nash equilibrium of our mechanism is optimal. We also prove that fully strategyproof mechanisms for this problem are impossible in general, with the exception of maxmin welfare. More broadly, our work shows that even small modifications (such as allowing nonlinear constraints) can dramatically increase the power of market mechanisms like trading post.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_6"}, {"primary_key": "2695529", "vector": [], "sparse_vector": [], "title": "Competition Alleviates Present Bias in Task Completion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We build upon recent work by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> [10,11,12] that considerspresent biasedagents, who place more weight on costs they must incur now than costs they will incur in the future. They consider a graph theoretic model where agents must complete a task and show that present biased agents can take exponentially more expensive paths than optimal. We propose a theoretical model that addscompetitioninto the mix – two agents compete to finish a task first. We show that, in a wide range of settings, a small amount of competition can alleviate the harms of present bias. This can help explain why biased agents may not perform so poorly in naturally competitive settings, and can guide task designers on how to protect present biased agents from harm. Our work thus paints a more positive picture than much of the existing literature on present bias.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_19"}, {"primary_key": "2695530", "vector": [], "sparse_vector": [], "title": "Two Strongly Truthful Mechanisms for Three Heterogeneous Agents Answering One Question.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Peer prediction mechanisms incentivize self-interested agents to truthfully report their signals even in the absence of verification, by comparing agents’ reports with their peers. We propose two new mechanisms, Source and Target Differential Peer Prediction, and prove very strong guarantees for a very general setting. Our Differential Peer Prediction mechanisms arestrongly truthful: Truth-telling a strict Bayesian Nash equilibrium. Also, truth-telling pays strictly higher than any other equilibria, excluding permutation equilibria, which pays the same amount as truth-telling. The guarantees hold forasymmetric priorswhich the mechanisms need not know (prior-free) in thesignal question setting. Moreover, they only requirethree agents, each of which submits asignal item report: one reports her forecast and the others their signals. Our proof technique is straightforward, conceptually motivated, and turns on the logarithmic scoring rule’s special properties. Moreover, we can recast the Bayesian Truth Serum mechanism \n[11] into our framework. We can also extend our results to the setting ofcontinuous signalswith a slightly weaker guarantee on the optimality of the truthful equilibrium.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_9"}, {"primary_key": "2695531", "vector": [], "sparse_vector": [], "title": "Improving Approximate Pure Nash Equilibria in Congestion Games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Congestion games constitute an important class of games to model resource allocation by different users. As computing an exact \n[18] or even an approximate \n[34] pure Nash equilibrium is in general PLS-complete, <PERSON><PERSON><PERSON><PERSON> et al. \n[9] present a polynomial-time algorithm that computes a (2 +\\(\\epsilon \\))-approximate pure Nash equilibria for games with linear cost functions and further results for polynomial cost functions. We show that this factor can be improved to\\((1.61+\\epsilon )\\)and further improved results for polynomial cost functions, by a seemingly simple modification to their algorithm by allowing for the cost functions used during the best response dynamics be different from the overall objective function. Interestingly, our modification to the algorithm also extends to efficiently computing improved approximate pure Nash equilibria in games with arbitrary non-decreasing resource cost functions. Additionally, our analysis exhibits an interesting method to optimally compute universal load dependent taxes and using linear programming duality prove tight bounds on the PoA under universal taxation, e.g., 2.012 for linear congestion games and further results for polynomial cost functions. Although our approach yield weaker results than that in Bilò and Vinci \n[6], we remark that our cost functions are locally computable and in contrast to \n[6] are independent of the actual instance of the game.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_20"}, {"primary_key": "2695532", "vector": [], "sparse_vector": [], "title": "A Generic Truthful Mechanism for Combinatorial Auctions.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We study combinatorial auctions withnagents andmitems, where the goal is to allocate the items to the agents such that the social welfare is maximized. We present a universally truthful mechanism with polynomially many queries for combinatorial auctions. Our mechanism and analysis work adaptively for all classes of valuation functions, guaranteeing\\(\\widetilde{O}(\\min (d, \\sqrt{m}))\\)-approximation (where\\(\\widetilde{O}\\)hides a polylogarithmic factor inm) of the optimal social welfare, wheredis the degree of complementarity of the valuation functions. To our knowledge, this is the first mechanism that achieves an approximation guarantee better than\\(\\varOmega (\\sqrt{m})\\), when the valuations exhibit any kind of complementarity.", "published": "2020-01-01", "category": "wine", "pdf_url": "", "sub_summary": "", "source": "wine", "doi": "10.1007/978-3-030-64946-3_10"}]