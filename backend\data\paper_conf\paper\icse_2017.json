[{"primary_key": "3802152", "vector": [], "sparse_vector": [], "title": "Towards designing effective data persistence through tradeoff space analysis.", "authors": ["<PERSON>ng Tang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Partial system specifications give rise to design spaces: sets of designs that satisfy specified constraints but that can vary in other dimensions, including non-functional properties such as performance. A tradespace is a design space where each design is paired with its relevant corresponding properties. Exploring tradespaces to find high-value designs is hard and time-consuming. The software engineering field provides inadequate support for tradespace exploration. In the context of object relational mapping (ORM), modern model-view-controller frameworks such as Django translate application-specific object models into relational database schemas with guarantees that they satisfy specified functional (data storage) requirements; however, these translators generally do not consider the range of possible schemas or the related performance tradeoffs. We present a novel approach using automated tradespace exploration to find schemas with optimal time and space tradeoffs. The engineer specifies an object model that defines objects and relationships. Our tool set then (1) synthesizes a design space of schemas for the object model, (2) profiles the time and space performance of each schema to generate the corresponding tradespace, and (3) analyzes the tradespace to identify designs on the optimal frontier, thus exposing essential tradeoffs. We achieve scalability of analysis through the use of the Spark MapReduce framework. In a set of experiments, our approach consistently found designs that were dramatically better than those produced by several widely used ORM tools.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.106"}, {"primary_key": "3802153", "vector": [], "sparse_vector": [], "title": "RACK: code search in the IDE using crowdsourced knowledge.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Traditional code search engines often do not perform well with natural language queries since they mostly apply keyword matching. These engines thus require carefully designed queries containing information about programming APIs for code search. Unfortunately, existing studies suggest that preparing an effective query for code search is both challenging and time consuming for the developers. In this paper, we propose a novel code search tool-RACK-that returns relevant source code for a given code search query written in natural language text. The tool first translates the query into a list of relevant API classes by mining keyword-API associations from the crowdsourced knowledge of Stack Overflow, and then applies the reformulated query to GitHub code search API for collecting relevant results. Once a query related to a programming task is submitted, the tool automatically mines relevant code snippets from thousands of open-source projects, and displays them as a ranked list within the context of the developer's programming environment-the IDE. Tool page: http://www.usask.ca/~masud.rahman/rack.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.11"}, {"primary_key": "3802154", "vector": [], "sparse_vector": [], "title": "SMUG: a selective MUtant generator tool.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this tool demo paper, we present a prototype of a tool for the selective generation of mutants in Java source code. We named this tool as SMUG (Selective MUtant Generator). Given two subsequent versions of a program, SMUG creates mutants by considering only those methods modified in, or added to, the second version. This is why it is a selective generator of mutants. On the basis of created mutants, SMUG generates a specified number of faulty versions of the program. We implemented SMUG as an Eclipse plug-in and employed this plug-in to assess regression test selection approaches. Therefore, SMUG has to be intended as a means to advance research in regression testing. We applied SMUG to create a total number of 200 faulty versions of 7 small-to-medium Java programs. A screencast of SMUG in action is available at www2.unibas.it/sromano/SMUG.html.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.12"}, {"primary_key": "3802156", "vector": [], "sparse_vector": [], "title": "Semantically enhanced software traceability using deep learning techniques.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "In most safety-critical domains the need for traceability is prescribed by certifying bodies. Trace links are generally created among requirements, design, source code, test cases and other artifacts, however, creating such links manually is time consuming and error prone. Automated solutions use information retrieval and machine learning techniques to generate trace links, however, current techniques fail to understand semantics of the software artifacts or to integrate domain knowledge into the tracing process and therefore tend to deliver imprecise and inaccurate results. In this paper, we present a solution that uses deep learning to incorporate requirements artifact semantics and domain knowledge into the tracing solution. We propose a tracing network architecture that utilizes Word Embedding and Recurrent Neural Network (RNN) models to generate trace links. Word embedding learns word vectors that represent knowledge of the domain corpus and RNN uses these word vectors to learn the sentence semantics of requirements artifacts. We trained 360 different configurations of the tracing network using existing trace links in the Positive Train Control domain and identified the Bidirectional Gated Recurrent Unit (BI-GRU) as the best model for the tracing task. BI-GRU significantly out-performed state-of-the-art tracing methods including the Vector Space Model and Latent Semantic Indexing.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.9"}, {"primary_key": "3802157", "vector": [], "sparse_vector": [], "title": "Analysis and testing of notifications in Android wear applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Android Wear (AW) is Google's platform for developing applications for wearable devices. Our goal is to make a first step toward a foundation for analysis and testing of AW apps. We focus on a core feature of such apps: notifications issued by a handheld device (e.g., a smartphone) and displayed on a wearable device (e.g., a smartwatch). We first define a formal semantics of AW notifications in order to capture the core features and behavior of the notification mechanism. Next, we describe a constraint-based static analysis to build a model of this run-time behavior. We then use this model to develop a novel testing tool for AW apps. The tool contains a testing framework together with components to support AW-specific coverage criteria and to automate the generation of GUI events on the wearable. These contributions advance the state of the art in the increasingly important area of software for wearable devices.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.39"}, {"primary_key": "3802158", "vector": [], "sparse_vector": [], "title": "Automatic text input generation for mobile testing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many designs have been proposed to improve the automated mobile testing. Despite these improvements, providing appropriate text inputs remains a prominent obstacle, which hinders the large-scale adoption of automated testing approaches. The key challenge is how to automatically produce the most relevant text in a use case context. For example, a valid website address should be entered in the address bar of a mobile browser app to continue the testing of the app, a singer's name should be entered in the search bar of a music recommendation app. Without the proper text inputs, the testing would get stuck. We propose a novel deep learning based approach to address the challenge, which reduces the problem to a minimization problem. Another challenge is how to make the approach generally applicable to both the trained apps and the untrained apps. We leverage the Word2Vec model to address the challenge. We have built our approaches as a tool and evaluated it with 50 iOS mobile apps including Firefox and Wikipedia. The results show that our approach significantly outperforms existing automatic text input generation methods.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.65"}, {"primary_key": "3802159", "vector": [], "sparse_vector": [], "title": "The multi-generation repackaging hypothesis.", "authors": ["<PERSON>", "Tegawendé F. Bissyandé", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "App repackaging is a common threat in the Android ecosystem. To face this threat, the literature now includes a large body of work proposing approaches for identifying repackaged apps. Unfortunately, although most research involves pairwise similarity comparison to distinguish repackaged apps from their \"original\" counterparts, no work has considered the threat to validity of not being able to discover the true original apps. We provide in this paper preliminary insights of an investigation into the Multi-Generation Repackaging Hypothesis: is the original in a repackaging process the outcome of a previous repackaging process? Leveraging the Androzoo dataset of over 5 million Android apps, we validate this hypothesis in the wild, calling upon the community to take this threat into account in new solutions for repackaged app detection.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.140"}, {"primary_key": "3802160", "vector": [], "sparse_vector": [], "title": "Understanding Android app piggybacking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Tegawendé F. Bissyandé", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Android packaging model offers adequate opportunities for attackers to inject malicious code into popular benign apps, attempting to develop new malicious apps that can then be easily spread to a large user base. Despite the fact that the literature has already presented a number of tools to detect piggybacked apps, there is still lacking a comprehensive investigation on the piggybacking processes. To fill this gap, in this work, we collect a large set of benign/piggybacked app pairs that can be taken as benchmark apps for further investigation. We manually look into these benchmark pairs for understanding the characteristics of piggybacking apps and eventually we report 20 interesting findings. We expect these findings to initiate new research directions such as practical and scalable piggybacked app detection, explainable malware detection, and malicious code location.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.109"}, {"primary_key": "3802164", "vector": [], "sparse_vector": [], "title": "Repairing event race errors by controlling nondeterminism.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern web applications are written in an event-driven style, in which event handlers execute asynchronously in response to user or system events. The nondeterminism arising from this programming style can lead to pernicious errors. Recent work focuses on detecting event races and classifying them as harmful or harmless. However, since modifying the source code to prevent harmful races can be a difficult and error-prone task, it may be preferable to steer away from the bad executions. In this paper, we present a technique for automated repair of event race errors in JavaScript web applications. Our approach relies on an event controller that restricts event handler scheduling in the browser according to a specified repair policy, by intercepting and carefully postponing or discarding selected events. We have implemented the technique in a tool called EventRaceCommander, which relies entirely on source code instrumentation, and evaluated it by repairing more than 100 event race errors that occur in the web applications from the largest 20 of the Fortune 500 companies. Our results show that application-independent repair policies usually suffice to repair event race errors without excessive negative impact on performance or user experience, though application-specific repair policies that target specific event races are sometimes desirable.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.34"}, {"primary_key": "3802166", "vector": [], "sparse_vector": [], "title": "Empirical investigation of correlation between rewards and crowdsource-based software developers.", "authors": ["<PERSON>na <PERSON>"], "summary": "Numerous reward system practices are adopted in software development companies in order to motivate their developers to perform at best level and align the management and developer's interest. However, due to lack of a central mechanism for team formation on crowdsourcing-based software development platform, it is difficult for managers to adopt effective reward system strategies in order to align the developer's interest. In order to address this issue, we exploit an approach, to empirically investigate the existing reward system practices, adopted on the crowdsourcing-based software development platforms, to motivate their developers and their perception. Subsequently, we implement a crawler to mine the characteristics of completed tasks and related reward information from the TopCoder platform of Tech Platform Inc (TPI). The promising results suggest the applicability of the proposed approach in order, 1) to investigate the reward system practices across the crowdsourcing-based platforms and, 2) to help the managers in formulating and implementing an effective reward system to incentivize their developers.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.149"}, {"primary_key": "3802189", "vector": [], "sparse_vector": [], "title": "Heuristically matching solution spaces of arithmetic formulas to efficiently reuse solutions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many symbolic program analysis techniques rely on SMT solvers to verify properties of programs. Despite the remarkable progress made in the development of such tools, SMT solvers still represent a main bottleneck to the scalability of these techniques. Recent approaches tackle this bottleneck by reusing solutions of formulas that recur during program analysis, thus reducing the number of queries to SMT solvers. Current approaches only reuse solutions across formulas that are equivalent to, contained in or implied by other formulas, as identified through a set of predefined rules, and cannot reuse solutions across formulas that differ in their structure, even if they share some potentially reusable solutions. In this paper, we propose a novel approach that can reuse solutions across formulas that share at least one solution, regardless of their structural resemblance. Our approach exploits a novel heuristic to efficiently identify solutions computed for previously solved formulas and most likely shared by new formulas. The results of an empirical evaluation of our approach on two different logics show that our approach can identify on average more reuse opportunities and is markedly faster than competing approaches.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.46"}, {"primary_key": "3802191", "vector": [], "sparse_vector": [], "title": "DevOps: introducing infrastructure-as-code.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "DevOps entails a series of software engineering tactics aimed at shortening the actionable operation of software design changes. One of these tactics is to harness infrastructure-as-code, that is, writing a blueprint that contains deployment specifications ready for orchestration in the cloud. This abstract briefly discusses all necessary elements and abstractions in writing and maintaining that blueprint, revolving around a key standard for its expression, namely, the OASIS \"Topology and Orchestration Specification for Cloud Applications\" (TOSCA) industrial standard adopted by as many as 60+ big industrial players worldwide.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.162"}, {"primary_key": "3802193", "vector": [], "sparse_vector": [], "title": "Detecting behavior anomalies in graphical user interfaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "When interacting with user interfaces, do users always get what they expect? For each user interface element in thousands of Android apps, we extracted the Android APIs they invoke as well as the text shown on their screen. This association allows us to detect outliers: User interface elements whose text, context or icon suggests one action, but which actually are tied to other actions. In our evaluation of tens of thousands of UI elements, our BACKSTAGE prototype discovered misleading random UI elements with an accuracy of 73%.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.130"}, {"primary_key": "3802196", "vector": [], "sparse_vector": [], "title": "Double-blind review in software engineering venues: the community&apos;s perspective.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The peer review process is central to the scientific method, the advancement and spread of research, as well as crucial for individual careers. However, the single-blind review mode currently used in most Software Engineering (SE) venues is susceptible to apparent and hidden biases, since reviewers know the identity of authors. We perform a study on the benefits and costs that are associated with introducing double-blind review in SE venues. We surveyed the SE community's opinion and interviewed experts on double-blind reviewing. Our results indicate that the costs, mostly logistic challenges and side effects, outnumber its benefits and mostly regard difficulty for authors in blinding papers, for reviewers in understanding the increment with respect to previous work from the same authors, and for organizers to manage a complex transition. While the surveyed community largely consents on the costs of DBR, only less than one-third disagree with a switch to DBR for SE journals, all SE conferences, and, in particular, ICSE, the analysis of a survey with authors of submitted papers at ICSE 2016 run by the program chairs of that edition corroborates our result.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.49"}, {"primary_key": "3802199", "vector": [], "sparse_vector": [], "title": "Attribution required: stack overflow code snippets in GitHub projects.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Stack Overflow (SO) is the largest Q&A website for developers, providing a huge amount of copyable code snippets. Using these snippets raises various maintenance and legal issues. The SO license requires attribution, i.e., referencing the original question or answer, and requires derived work to adopt a compatible license. While there is a heated debate on SO's license model for code snippets and the required attribution, little is known about the extent to which snippets are copied from SO without proper attribution. In this paper, we present the research design and summarized results of an empirical study analyzing attributed and unattributed usages of SO code snippets in GitHub projects. On average, 3.22% of all analyzed repositories and 7.33% of the popular ones contained a reference to SO. Further, we found that developers rather refer to the whole thread on SO than to a specific answer. For Java, at least two thirds of the copied snippets were not attributed.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.99"}, {"primary_key": "3802204", "vector": [], "sparse_vector": [], "title": "Do developers read compiler error messages?", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-Hill", "<PERSON>"], "summary": "In integrated development environments, developers receive compiler error messages through a variety of textual and visual mechanisms, such as popups and wavy red underlines. Although error messages are the primary means of communicating defects to developers, researchers have a limited understanding on how developers actually use these messages to resolve defects. To understand how developers use error messages, we conducted an eye tracking study with 56 participants from undergraduate and graduate software engineering courses at our university. The participants attempted to resolve common, yet problematic defects in a Java code base within the Eclipse development environment. We found that: 1) participants read error messages and the difficulty of reading these messages is comparable to the difficulty of reading source code, 2) difficulty reading error messages significantly predicts participants' task performance, and 3) participants allocate a substantial portion of their total task to reading error messages (13%-25%). The results of our study offer empirical justification for the need to improve compiler error messages for developers.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.59"}, {"primary_key": "3802206", "vector": [], "sparse_vector": [], "title": "Identifying Android library dependencies in the presence of code obfuscation and minimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The fast growth of the Android app market motivates the need for tools and techniques to analyze and improve Android apps. A basic capability in this context is to identify the libraries present in a given Android app, including their exact version. The problem of identifying library dependencies is made difficult by two common build-time transformations, namely code minimization and obfuscation. Minimization typically incorporates used library fragments into an app, while obfuscation renames symbols globally across an app. In this paper, we tackle both of these challenges via a unified approach, which abstracts app and library classes into summaries of their interactions with system libraries. The summarization technique is resistant to obfuscation, and is amenable to efficient similarity detection (matching). We lift the class-wise matches into a set of library dependencies by encoding this problem as a global constraint/optimization system across all app classes and available libraries. Our techniques identify the exact libraries and their versions used in the apps, for clear apps the recall is almost perfect at 98%. For obuscated/minimized apps it stands at 85%.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.79"}, {"primary_key": "3802211", "vector": [], "sparse_vector": [], "title": "PEoPL: projectional editing of product lines.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The features of a software product line - a portfolio of system variants - can be realized using various implementation techniques (a. k. a., variability mechanisms). Each technique represents the software artifacts of features differently, typically classified into annotative (e.g., C preprocessor) and modular representations (e.g., feature modules), each with distinct advantages and disadvantages. Annotative representations are easy to realize, but annotations clutter source code and hinder program comprehension. Modular representations support comprehension, but are difficult to realize. Most importantly, to engineer feature artifacts, developers need to choose one representation and adhere to it for evolving and maintaining the same artifacts. We present PEoPL, an approach to combine the advantages of annotative and modular representations. When engineering a feature artifact, developers can choose the most-suited representation and even use different representations in parallel. PEoPL relies on separating a product line into an internal and external representation, the latter by providing editable projections used by the developers. We contribute a programming-language-independent internal representation of variability, five editable projections reflecting different variability representations, a supporting IDE, and a tailoring of PEoPL to Java. We evaluate PEoPL's expressiveness, scalability, and flexibility in eight Java-based product lines, finding that all can be realized, that projections are feasible, and that variant computation is fast (<;45ms on average for our largest subject Berkeley DB).", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.58"}, {"primary_key": "3802221", "vector": [], "sparse_vector": [], "title": "What paper types are accepted at the international conference on software engineering?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the aim of identifying good structures and examples for papers in the software engineering field, we conducted a study of the type of papers accepted along four decades in the Research Track of the International Conference on Software Engineering (ICSE). We used for this purpose a categorization scheme for Software Engineering papers that was obtained by merging, extending and revising a few existing paper scheme proposals. This paper summarizes some outcomes relative to what topics and problems are addressed, what types of contribution are presented and how they are validated. Insights from the study could help ICSE authors, reviewers and conference organizers in focusing and improving future efforts.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.50"}, {"primary_key": "3802222", "vector": [], "sparse_vector": [], "title": "Adaptive coverage and operational profile-based testing for reliability improvement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce covrel, an adaptive software testing approach based on the combined use of operational profile and coverage spectrum, with the ultimate goal of improving the delivered reliability of the program under test. Operational profile-based testing is a black-box technique that selects test cases having the largest impact on failure probability in operation, as such, it is considered well suited when reliability is a major concern. Program spectrum is a characterization of a program's behavior in terms of the code entities (e.g., branches, statements, functions) that are covered as the program executes. The driving idea of covrel is to complement operational profile information with white-box coverage measures based on count spectra, so as to dynamically select the most effective test cases for reliability improvement. In particular, we bias operational profile-based test selection towards those entities covered less frequently. We assess the approach by experiments with 18 versions from 4 subjects commonly used in software testing research, comparing results with traditional operational and coverage testing. Results show that exploiting operational and coverage data in a combined adaptive way actually pays in terms of reliability improvement, with covrel overcoming conventional operational testing in more than 80% of the cases.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.56"}, {"primary_key": "3802224", "vector": [], "sparse_vector": [], "title": "Symbolic model extraction for web application verification.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Modern web applications use complex data models and access control rules which lead to data integrity and access control errors. One approach to find such errors is to use formal verification techniques. However, as a first step, most formal verification techniques require extraction of a formal model which is a difficult problem in itself due to dynamic features of modern languages, and it is typically done either manually, or using ad hoc techniques. In this paper, we present a technique called symbolic model extraction for extracting formal data models from web applications. The key ideas of symbolic model extraction are 1) to use the source language interpreter for model extraction, which enables us to handle dynamic features of the language, 2) to use code instrumentation so that execution of each instrumented piece of code returns the formal model that corresponds to that piece of code, 3) to instrument the code dynamically so that the models of methods that are created at runtime can also be extracted, and 4) to execute both sides of branches during instrumented execution so that all program behaviors can be covered in a single instrumented execution. We implemented the symbolic model extraction technique for the Rails framework and used it to extract data and access control models from web applications. Our experiments demonstrate that symbolic model extraction is scalable and extracts formal models that are precise enough to find bugs in real-world applications without reporting too many false positives.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.72"}, {"primary_key": "3802225", "vector": [], "sparse_vector": [], "title": "Software cost estimation meets software diversity.", "authors": ["<PERSON>"], "summary": "The previous goal of having a one-size-fits-all software cost (and schedule) estimation model is no longer achievable. Sources of wide variation in the nature of software development and evolution processes, products, properties, and personnel (PPPPs) require a variety of estimation models and methods best fitting their situations. This Technical Briefing will provide a short history of pattern-breaking changes in software estimation methods, a summary of the sources of variation in software PPPPs and their estimation implications, a summary of the types of estimation methods being widely used or emerging, a summary of the best estimation-types for the various PPPP-types, and a process for guiding an organization's choices of estimation methods as their PPPP-types evolve.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.159"}, {"primary_key": "3802226", "vector": [], "sparse_vector": [], "title": "How developers debug software the DbgBench dataset: poster.", "authors": ["<PERSON>", "Ezekiel O<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "How do professional software engineers debug computer programs? In an experiment with 27 real bugs that existed in several widely used programs, we invited 12 professional software engineers, who together spent one month on localizing, explaining, and fixing these bugs. This did not only allow us to study the various tools and strategies used to debug the same set of errors. We could also determine exactly which statements a developer would localize as faults, how a developer would diagnose and explain an error, and how a developer would fix an error – all of which software engineering researchers seek to automate. Until now, it has been difficult to evaluate the effectiveness and utility of automated debugging techniques without a user study. We publish the collected data, called DBGBENCH, to facilitate the effective evaluation of automated fault localization, diagnosis, and repair techniques w.r.t. the judgement of human experts.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.94"}, {"primary_key": "3802231", "vector": [], "sparse_vector": [], "title": "RADAR: a lightweight tool for requirements and architecture decision analysis.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "Uncertainty and conflicting stakeholders' objectives make many requirements and architecture decisions particularly hard. Quantitative probabilistic models allow software architects to analyse such decisions using stochastic simulation and multi-objective optimisation, but the difficulty of elaborating the models is an obstacle to the wider adoption of such techniques. To reduce this obstacle, this paper presents a novel modelling language and analysis tool, called RADAR, intended to facilitate requirements and architecture decision analysis. The language has relations to quantitative AND/OR goal models used in requirements engineering and to feature models used in software product lines. However, it simplifies such models to a minimum set of language constructs essential for decision analysis. The paper presents RADAR's modelling language, automated support for decision analysis, and evaluates its application to four real-world examples.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.57"}, {"primary_key": "3802232", "vector": [], "sparse_vector": [], "title": "Detecting and quantifying architectural debt: theory and practice.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this technical briefing, we will introduce the theory, practice, and tool support for detecting and quantifying architectural debt. We will introduce the concept of design rule space (DRSpace)-a new architectural model forming the foundation of architectural debt detection, hotspot patterns- recurring architectural flaws leading to architectural debt, and architectural debt quantification.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.165"}, {"primary_key": "3802236", "vector": [], "sparse_vector": [], "title": "Live programming the behavioral layer of robots.", "authors": ["<PERSON>"], "summary": "Robotic development suffers from a long cognitive distance between the code and the resulting behavior. This is due to the several steps necessary to build robotic behaviors: writing the code, compiling it, deploying it and finally testing it on the robot. All this slows down development and can make experimentation prohibitively expensive. In contrast, Live Programming tightens the feedback loop, minimizing the cognitive distance. As a result, programmers benefit from an immediate connection with the program that they are making thanks to an immediate, 'live' feedback of program behavior. This allows for extremely rapid creation, or variation, of robot behavior and for dramatically increased debugging speed. In this research, we fist explore the concept of live programming in the development of robot behaviors. Second, we present how we can validate our approach to improve the development of robotic behaviors.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.158"}, {"primary_key": "3802240", "vector": [], "sparse_vector": [], "title": "Quality attributes and preferences on the synthesis of reactive systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Given a model of the environment's behaviour, a set of system goals, and a set of controllable actions, the controller synthesis problem is to automatically generate a controller that only restricts controllable actions. Qualitative controller synthesis techniques yield controllers that guarantee achieving a given goal in the presence of an adversarial environment. However, synthesis produces one out of many possible solutions and no support for expressing preference over them is provided. Quantitative synthesis techniques are a natural way of optimising control with respect to preferences, however, they require quantitative modelling, which in many cases is not available, possible, or desired, and incur in high computational complexity. In this work, we aim to develop a qualitative preference framework for control problems.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.48"}, {"primary_key": "3802244", "vector": [], "sparse_vector": [], "title": "Should we replace our merge tools?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Paola R. G. <PERSON>ccioly"], "summary": "While unstructured merge tools try to automatically resolve merge conflicts via textual similarity, semistructured merge tools try to go further by partially exploiting the syntactic structure and semantics of the involved artefacts. Previous studies compare these merge approaches with respect to the number of reported conflicts, showing, for most projects and merge situations, a reduction in favor of semistructured merge. However, these studies do not investigate whether this reduction actually leads to integration effort reduction (Productivity) without negative impact on the correctness of the merging process (Quality). To analyze this, and to better understand how these tools could be improved, we propose empirical studies to identify spurious conflicts reported by one approach but not by the other, and interference reported as conflict by one approach but missed by the other.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.103"}, {"primary_key": "3802246", "vector": [], "sparse_vector": [], "title": "Improving bug reporting, duplicate detection, and localization.", "authors": ["<PERSON>"], "summary": "Software developers rely on essential textual information from bug reports (such as Observed Behavior, Expected Behavior, and Steps to Reproduce) to triage and fix software bugs. Unfortunately, while relevant and useful, this information is often missing, incomplete, superficial, ambiguous, or complex to follow. Low-quality content in bug reports causes delay and extra effort on bug triage and fixing. Current technology and research are insufficient to support users and developers on providing high-quality content in bug reports. Our research is intended to fill in this gap, as it aims at improving: (1) the quality of natural language content in bug reports, and (2) the accuracy of Text Retrieval (TR)-based bug localization and duplicate detection. To achieve such goals, our research will identify, enforce, and leverage the discourse that reporters use to describe software bugs.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.27"}, {"primary_key": "3802247", "vector": [], "sparse_vector": [], "title": "Fully-reflective VMs for ruling software adaptation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A recent survey on paradigms for software adaptation at the language level assessed contemporary reflective systems (RS), aspect-oriented programming (AOP), and context-oriented programming (COP) as three well-established approaches. The survey did not find a clear winner. Our opinion is that this is due to the fact that none of these approaches is flexible enough to handle the diversity of possible adaptation scenarios. The reason is that instead of operating directly on the entity that conceptually requires the adaptation, these approaches often require to handle the adaptations in an indirect fashion. In this paper we advocate that a suitable paradigm for software adaptation at the language level must enable direct modification to every concept at both, the application and the execution environment level. This is enabled by a Fully-Reflective Execution Environment (FREE), a flavor of virtual machine in which every component such as the interpreter and the memory is accessible for inspection and modification, programmatically, and at run time. Consequently, we advocate and illustrate how the notion of a FREE extends RS and subsumes AOP and COP.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.144"}, {"primary_key": "3802251", "vector": [], "sparse_vector": [], "title": "An empirical study on mutation, statement and branch coverage fault revelation that avoids the unreliable clean program assumption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many studies suggest using coverage concepts, such as branch coverage, as the starting point of testing, while others as the most prominent test quality indicator. Yet the relationship between coverage and fault-revelation remains unknown, yielding uncertainty and controversy. Most previous studies rely on the Clean Program Assumption, that a test suite will obtain similar coverage for both faulty and fixed ('clean') program versions. This assumption may appear intuitive, especially for bugs that denote small semantic deviations. However, we present evidence that the Clean Program Assumption does not always hold, thereby raising a critical threat to the validity of previous results. We then conducted a study using a robust experimental methodology that avoids this threat to validity, from which our primary finding is that strong mutation testing has the highest fault revelation of four widely-used criteria. Our findings also revealed that fault revelation starts to increase significantly only once relatively high levels of coverage are attained.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.61"}, {"primary_key": "3802252", "vector": [], "sparse_vector": [], "title": "Why is it important to measure maintainability, and what are the best ways to do it?", "authors": ["<PERSON>", "<PERSON><PERSON>", "Kamonphop <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Being highly maintainable is the key to reducing approximately 75% of most systems' life cycle costs. Software maintainability is defined as the ease with which a software system or a component can be modified, to correct faults, improve performance or other attributes, or adapt to a changed environment. There exist metrics that can help developers measure and analyze the maintainability level of a project objectively. Most of these metrics involve automated analysis of the code. In this extended abstract paper, we addressed the importance of understanding software maintainability, explored some of the best ways to measure maintainability and briefly described a comparison study we conducted between automated maintainability metrics and human-assessed maintainability metrics through a controlled experiment by performing change-request modifications on open source software (OSS) projects.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.75"}, {"primary_key": "3802253", "vector": [], "sparse_vector": [], "title": "Learning to prioritize test programs for compiler testing.", "authors": ["<PERSON><PERSON><PERSON>", "Yanwei Bai", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Compiler testing is a crucial way of guaranteeing the reliability of compilers (and software systems in general). Many techniques have been proposed to facilitate automated compiler testing. These techniques rely on a large number of test programs (which are test inputs of compilers) generated by some test-generation tools (e.g., CSmith). However, these compiler testing techniques have serious efficiency problems as they usually take a long period of time to find compiler bugs. To accelerate compiler testing, it is desirable to prioritize the generated test programs so that the test programs that are more likely to trigger compiler bugs are executed earlier. In this paper, we propose the idea of learning to test, which learns the characteristics of bug-revealing test programs from previous test programs that triggered bugs. Based on the idea of learning to test, we propose LET, an approach to prioritizing test programs for compiler testing acceleration. LET consists of a learning process and a scheduling process. In the learning process, LET identifies a set of features of test programs, trains a capability model to predict the probability of a new test program for triggering compiler bugs and a time model to predict the execution time of a test program. In the scheduling process, LET prioritizes new test programs according to their bug-revealing probabilities in unit time, which is calculated based on the two trained models. Our extensive experiments show that LET significantly accelerates compiler testing. In particular, LET reduces more than 50% of the testing time in 24.64% of the cases, and reduces between 25% and 50% of the testing time in 36.23% of the cases.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.70"}, {"primary_key": "3802254", "vector": [], "sparse_vector": [], "title": "Characterizing and detecting anti-patterns in the logging code.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> (Jack) Jiang"], "summary": "Snippets of logging code are output statements (e.g., LOG.info or System.out.println) that developers insert into a software system. Although more logging code can provide more execution context of the system's behavior during runtime, it is undesirable to instrument the system with too much logging code due to maintenance overhead. Furthermore, excessive logging may cause unexpected side-effects like performance slow-down or high disk I/O bandwidth. Recent studies show that there are no well-defined coding guidelines for performing effective logging. Previous research on the logging code mainly tackles the problems of where-to-log and what-to-log. There are very few works trying to address the problem of how-to-log (developing and maintaining high-quality logging code). In this paper, we study the problem of how-to-log by characterizing and detecting the anti-patterns in the logging code. As the majority of the logging code is evolved together with the feature code, the remaining set of logging code changes usually contains the fixes to the anti-patterns. We have manually examined 352 pairs of independently changed logging code snippets from three well-maintenance open source systems: ActiveMQ, Hadoop and Maven. Our analysis has resulted in six different anti-patterns in the logging code. To demonstrate the value of our findings, we have encoded these anti-patterns into a static code analysis tool, LCAnalyzer. Case studies show that LCAnalyzer has an average recall of 95% and precision of 60% and can be used to automatically detect previously unknown anti-patterns in the source code. To gather feedback, we have filed 64 representative instances of the logging code anti-patterns from the most recent releases of ten open source software systems. Among them, 46 instances (72%) have already been accepted by their developers.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.15"}, {"primary_key": "3802256", "vector": [], "sparse_vector": [], "title": "Unsupervised software-specific morphological forms inference from informal discussions.", "authors": ["Chunyang Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Informal discussions on social platforms (e.g., Stack Overflow) accumulates a large body of programming knowledge in natural language text. Natural language process (NLP) techniques can be exploited to harvest this knowledge base for software engineering tasks. To make an effective use of NLP techniques, consistent vocabulary is essential. Unfortunately, the same concepts are often intentionally or accidentally mentioned in many different morphological forms in informal discussions, such as abbreviations, synonyms and misspellings. Existing techniques to deal with such morphological forms are either designed for general English or predominantly rely on domain-specific lexical rules. A thesaurus of software-specific terms and commonly-used morphological forms is desirable for normalizing software engineering text, but very difficult to build manually. In this work, we propose an automatic approach to build such a thesaurus. Our approach identifies software-specific terms by contrasting software-specific and general corpuses, and infers morphological forms of software-specific terms by combining distributed word semantics, domain-specific lexical rules and transformations, and graph analysis of morphological relations. We evaluate the coverage and accuracy of the resulting thesaurus against community-curated lists of software-specific terms, abbreviations and synonyms. We also manually examine the correctness of the identified abbreviations and synonyms in our thesaurus. We demonstrate the usefulness of our thesaurus in a case study of normalizing questions from Stack Overflow and CodeProject.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.48"}, {"primary_key": "3802260", "vector": [], "sparse_vector": [], "title": "Efficient detection of thread safety violations via coverage-guided generation of concurrent tests.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As writing concurrent programs is challenging, developers often rely on thread-safe classes, which encapsulate most synchronization issues. Testing such classes is crucial to ensure the correctness of concurrent programs. An effective approach to uncover otherwise missed concurrency bugs is to automatically generate concurrent tests. Existing approaches either create tests randomly, which is inefficient, build on a computationally expensive analysis of potential concurrency bugs exposed by sequential tests, or focus on exposing a particular kind of concurrency bugs, such as atomicity violations. This paper presents CovCon, a coverage-guided approach to generate concurrent tests. The key idea is to measure how often pairs of methods have already been executed concurrently and to focus the test generation on infrequently or not at all covered pairs of methods. The approach is independent of any particular bug pattern, allowing it to find arbitrary concurrency bugs, and is computationally inexpensive, allowing it to generate many tests in short time. We apply CovCon to 18 thread-safe Java classes, and it detects concurrency bugs in 17 of them. Compared to five state of the art approaches, CovCon detects more bugs than any other approach while requiring less time. Specifically, our approach finds bugs faster in 38 of 47 cases, with speedups of at least 4x for 22 of 47 cases.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.32"}, {"primary_key": "3802261", "vector": [], "sparse_vector": [], "title": "A general framework for dynamic stub injection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Stub testing is a standard technique to simulate the behavior of dependencies of an application under test such as the file system. Even though existing frameworks automate the actual stub injection, testers typically have to implement manually where and when to inject stubs, in addition to the stub behavior. This paper presents a novel framework that reduces this effort. The framework provides a domain specific language to describe stub injection strategies and stub behaviors via declarative rules, as well as a tool that automatically injects stubs dynamically into binary code according to these rules. Both the domain specific language and the injection are language independent, which enables the reuse of stubs and injection strategies across applications. We implemented this framework for both unmanaged (assembly) and managed (.NET) code and used it to perform fault injection for twelve large applications, which revealed numerous crashes and bugs in error handling code. We also show how to prioritize the analysis of test failures based on a comparison of the effectiveness of stub injection rules across applications.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.60"}, {"primary_key": "3802262", "vector": [], "sparse_vector": [], "title": "Towards a model compilation framework based on a unified model execution semantics.", "authors": ["<PERSON>"], "summary": "Due to the increasing complexity of software systems, model-driven engineering has been introduced to shift the developer's focus from machine-centric program code to human-centric models of the software under development. In model-driven approaches, program code in conventional programming languages (e.g., C++, Java) is commonly generated from models and then compiled or interpreted. Intermediate translation of models to program code raises two fundamental issues: (1) semantic inconsistency and information loss between an executable and its source model, and (2) suboptimality of executables, since compilers are unable to exploit model semantics. These issues are not tolerable in embedded real-time and safety-critical applications. To tame them, we propose direct compilation of models bypassing intermediate translations to conventional programming languages.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.125"}, {"primary_key": "3802263", "vector": [], "sparse_vector": [], "title": "Engineering the software of robotic systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The production of software for robotic systems is often case-specific, without fully following established engineering approaches. Systematic approaches, methods, models, and tools are pivotal for the creation of robotic systems for real-world applications and turn-key solutions. Well-defined (software) engineering approaches are considered the \"make or break\" factor in the development of complex robotic systems. The shift towards well-defined engineering approaches will stimulate component supply-chains and significantly reshape the robotics marketplace. The goal of this technical briefing is to provide an overview on the state of the art and practice concerning solutions and open challenges in the engineering of software required to develop and manage robotic systems. Model-Driven Engineering (MDE) is discussed as a promising technology to raise the level of abstraction, promote reuse, facilitate integration, boost automation and promote early analysis in such a complex domain.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.167"}, {"primary_key": "3802271", "vector": [], "sparse_vector": [], "title": "Obsidian: a safer blockchain programming language.", "authors": ["<PERSON>"], "summary": "Blockchain platforms, such as Ethereum, promise to facilitate transactions on a decentralized computing platform among parties that have not established trust. Recognition of the unique challenges of blockchain programming has inspired developers to create domain-specific languages, such as Solidity, for programming blockchain systems. Unfortunately, bugs in Solidity programs have recently been exploited to steal money. We propose a new programming language, Obsidian, to make it easier for programmers to write correct programs.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.150"}, {"primary_key": "3802272", "vector": [], "sparse_vector": [], "title": "Principles of usable programming language design.", "authors": ["<PERSON>"], "summary": "Tools for software engineers, such as programming languages and IDEs, should reflect the needs of their users. Unfortunately, designers of programming languages lack strong guidance regarding how to make these tools most effective for users. Though there is a well-developed theory of programming languages, there is little evidence regarding how to use this theory to build languages in which software engineers are most productive. I propose to develop methods for programming language design that fuse results from programming language theory with methods from human-computer interaction so that designers can create effective tools for users.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.24"}, {"primary_key": "3802273", "vector": [], "sparse_vector": [], "title": "Glacier: transitive class immutability for Java.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Though immutability has been long-proposed as a way to prevent bugs in software, little is known about how to make immutability support in programming languages effective for software engineers. We designed a new formalism that extends Java to support transitive class immutability, the form of immutability for which there is the strongest empirical support, and implemented that formalism in a tool called Glacier. We applied Glacier successfully to two real-world systems. We also compared Glacier to Java's final in a user study of twenty participants. We found that even after being given instructions on how to express immutability with final, participants who used final were unable to express immutability correctly, whereas almost all participants who used Glacier succeeded. We also asked participants to make specific changes to immutable classes and found that participants who used final all incorrectly mutated immutable state, whereas almost all of the participants who used Glacier succeeded. Glacier represents a promising approach to enforcing immutability in Java and provides a model for enforcement in other languages.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.52"}, {"primary_key": "3802275", "vector": [], "sparse_vector": [], "title": "HAPPYNESS: an emotion-aware QoS assurance framework for enhancing user experience.", "authors": ["<PERSON><PERSON>-<PERSON>"], "summary": "In this paper, we introduce the idea of exploiting the emotional information as a key element in providing personalized context-aware software services and consequently enhancing quality of User Experience(UX). We argue that emotional measurements can be integrated in Quality of Service (QoS) assurance frameworks. The idea builds on the strength of technological advances in emotion measurement tools, nonobtrusive and ubiquitous monitoring technology.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.137"}, {"primary_key": "3802277", "vector": [], "sparse_vector": [], "title": "Peer to peer for privacy and decentralization in the internet of things.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the Internet of Things (IoT) era new connected devices will spread highly sensitive personal data. Sending this type of data to centralized companies represents a serious risk for people's privacy, since economical or political interests could lead to an illegitimate use of personal information (as shown by <PERSON><PERSON>'s revelations). With the purpose of overcoming such status-quo, our research goal is to develop software systems according to the notion of decentralized private-by-design IoT. The basic idea is that data produced by personal IoT devices are safely stored in a distributed system whose design guarantees privacy, leaving to the people-the real data owners-the decision of which of them to share and with whom. To achieve this goal, a possible solution is to leverage the use of Peer-to-Peer storage networks in combination with the blockchain. However, such architecture, despite promising, embeds still limitations, especially in terms of scalability. In this paper we discuss our research motivation, we describe our research idea applied in a possible scenario and we present the scalability problem.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.60"}, {"primary_key": "3802278", "vector": [], "sparse_vector": [], "title": "Fragility and evolution of Android test suites.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper a PhD research started in November 2015 is described. Its principal aims are an investigation of existing techniques and issues of GUI testing for Android applications, and a definition and exploration of the fragility problem for GUI test suites. The final outcomes of the work, whose end is forecasted for early 2019, will mainly be: (i) a study of the adoption of testing among open-source mobile applications, (ii) a characterization of the fragility issues and its causes, (iii) a set of metrics to evaluate the presence of fragility of existing projects: (iv) a set of best practices for developers to avoid testing fragilities for their scripted test suites.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.22"}, {"primary_key": "3802279", "vector": [], "sparse_vector": [], "title": "Flexible in-the-field monitoring.", "authors": ["<PERSON>"], "summary": "Fully assessing the robustness of a software application in-house is infeasible, especially considering the huge variety of hardly predictable stimuli, environments, and configurations that applications must handle in the field. For this reason, modern testing and analysis techniques can often process data extracted from the field, such as crash reports and profile data, or can even be executed directly in the field, for instance to diagnose and correct problems. In all these cases, collection, processing, and distribution of field data must be done seamlessly and unobstrusively while users interact with their applications. To limit the intrusiveness of in-the-field monitoring a common approach is to reduce the amount of collected data (e.g., to rare events and to crash dumps), which, however, may severely affect the effectiveness of the techniques that exploit field data. The objective of this Ph.D. thesis is to define solutions for collecting field data in a cost effective way without affecting the quality of the user experience. This result can enable a new range of testing and analysis solutions that extensively exploit field data.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.37"}, {"primary_key": "3802295", "vector": [], "sparse_vector": [], "title": "FPH: efficient detection of feature interactions through non-commutativity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Summary form only given. Feature-oriented software development (FOSD) has recently emerged as a promising approach for developing a collection of similar software products from a shared set of software assets. A well-recognized issue in FOSD is the analysis of feature interactions: cases where the integration of multiple features would alter the behavior of one or several of them. Existing approaches to detecting feature interactions require specification of correctness of individual features and operate on the entire family of software products. In this poster, we develop and evaluate a highly scalable and modular approach, called Mr. Feature Potato Head (FPH), to detect interactions stemming from non-commutativity of features, i.e., cases where behavior of products changes depending on the order in which features have been composed. We instantiate FPH for systems expressed in Java and evaluate its performance on 29 examples. Our experiments show that FPH is an efficient and effective approach for identifying commutativity-related feature interactions.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.71"}, {"primary_key": "3802297", "vector": [], "sparse_vector": [], "title": "Developing e-banking services for rural India: making use of socio-technical prototypes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Information and Communication Technology (ICT) is one of the key enablers for including underserved communities in economic and societal development across the world. Our research analyzes several banking service projects developing technical solutions for rural India. This poster presents an experience report based on systematic debriefing of involved project leaders and initiators, triangulated with additional documentation. The concept of Socio-Technical Prototype is developed and used to show how to mitigate the challenges of ICT based banking service provision for socially constrained communities. The concept of Socio-Technical Prototype extends the notion of prototypes, as it implies a full functioning implementation of the service including all relevant stakeholders. In order to not only prototype end-user functionality but also the interaction of the solution with the specific social, technical and physical environment. The implications for software engineering in the development of such large-scale prototypes and pilots are outlined.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.55"}, {"primary_key": "3802298", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON>: just-in-time taint analysis for Android apps.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-Hill"], "summary": "Current static-analysis tools are often long-running, which causes them to be sidelined into nightly build checks. As a result, developers rarely use such tools to detect bugs when writing code, because they disrupt their workflow. In this paper, we present Ch<PERSON>tah, a static taint analysis tool for Android apps that interleaves bug fixing and code development in the Eclipse integrated development environment. Cheetah is based on the novel concept of Just-in-Time static analysis that discovers and reports the most relevant results to the developer fast, and computes the more complex results incrementally later. Unlike traditional batch-style static-analysis tools, Ch<PERSON>tah causes minimal disruption to the developer's workflow. This video demo showcases the main features of Ch<PERSON>tah: https://www.youtube.com/watch?v=i_KQD-GTBdA.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.20"}, {"primary_key": "3802300", "vector": [], "sparse_vector": [], "title": "A framework to preserve confidentiality in crowdsourced software development.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a framework to preserve confidential information in a crowdsourced software development. The software industry is moving towards gig economy where majority of workforce is freelancers. The freelancers may have varying level of trust. Hence, protection of confidential information is becoming an increasingly important subject. In this paper, we discuss various challenges in protecting sensitive information in software development projects and propose a confidentiality preserving software development process. We perform a preliminary evaluation of the process. We use an information theoretic approach to protect confidential information. Results demonstrate the feasibility of the framework and uncovers several aspects that requires further research studies.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.145"}, {"primary_key": "3802306", "vector": [], "sparse_vector": [], "title": "Decision-making in self-protecting software systems: a game-theoretic approach.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Dynamic strategies used by attackers to break down the software system calls for dynamic countermeasure selection techniques. A significant challenge in engineering self-proctoring software system is selecting a proper countermeasure while the software systems undergoes a well-planned attack. To address this challenge, in this research work, we model the interactions between the attacker and the software system as a two-player game. Modeling such interaction using game theory enables the decision-making engine to model the strategies of the attackers while considers the effect of possible defense strategies in a dynamic attack scenario. In this research work, we aim at engineering a novel decision-making framework that utilizes game theoretic techniques to select the proper mitigation against an attack. The introduced framework consists of three high-level phases including: modeling quality goal, designing game-theoretic techniques, and realizing the decision-making engine. The first phase models the security goals of the system and maps goal-oriented model to the designed game-theoretic technique. Such goal model makes the decision-making engine capable of tracking the satisfaction of modeled goals before and after applying a mitigation strategy. The framework provides the steps to map the goal-oriented model to any game-theoretic techniques that is suitable to model the countermeasure selection.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.155"}, {"primary_key": "3802307", "vector": [], "sparse_vector": [], "title": "A game-theoretic decision-making framework for engineering self-protecting software systems.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Targeted and destructive nature of strategies used by attackers to break down the system require mitigation approaches with dynamic awareness. Making a right decision, when facing today's sophisticated and dynamic attacks, is one of the most challenging aspects of engineering self-protecting software systems. Inspired by game theory, in this research work, we model the interactions between the attacker and the software system as a two-player game. Using game-theoretic techniques, the self-protecting software systems is able to: (i) fuse the strategies of attackers into the decision-making model, and (ii) refine the strategies in dynamic attack scenarios by utilizing what has learned from the system's and adversary's interactions. This research introduces a novel decision-making framework with three phases: (i) modeling quality goals aiming at incorporating them into the decision model, (ii) designing game-theoretic techniques in order to build the decision model, and (iii) realizing the decisionmaking engine in the adaptation manager. Modeling quality goals provides the adaptation manager with the knowledgebase required in making a systematic adaptation decision. The framework aims at exhibiting a plug-and-play capability to adapt game-theoretic techniques that suite security goals and requirements of the software.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.43"}, {"primary_key": "3802311", "vector": [], "sparse_vector": [], "title": "Text retrieval-based tagging of software engineering video tutorials.", "authors": ["<PERSON><PERSON>", "Esteban Parra", "<PERSON>"], "summary": "Video tutorials are an emerging form of documentation in software engineering and can efficiently provide developers with useful information needed for their daily tasks. However, to get the information they need, developers have to find the right tutorial for their task at hand. Currently, there is little information available to quickly judge whether a tutorial is relevant to a topic or helpful to the task at hand, which can lead to missing the best tutorials and wasting time watching irrelevant ones. We present the first efforts towards new tagging approaches using text retrieval that describe the contents of software engineering video tutorials, making it easier and faster to understand their purpose and contents. We also present the results of a preliminary evaluation of thirteen such approaches, revealing the potential of some and limitations of others.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.121"}, {"primary_key": "3802312", "vector": [], "sparse_vector": [], "title": "JSDeodorant: class-awareness for JavaScript programs.", "authors": ["<PERSON><PERSON>", "Davood Mazinanian", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Until the recent updates to JavaScript specifications, adding syntactical support for class and namespace declaration, developers used custom solutions to emulate modular decomposition (e.g., classes and namespaces) and other object-oriented constructs, such as interfaces, and inheritance relationships. However, the lack of standards for several years led to a large variation and diversity of custom solutions for emulating object-oriented constructs, making maintenance and comprehension activities rather difficult in JavaScript projects developed based on the previous language specifications. In this paper, we present JSDEODORANT, an Eclipse plug-in that enables classaware maintenance and comprehension for JavaScript programs. (https://youtu.be/k4U2LwkL6JU).", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.6"}, {"primary_key": "3802313", "vector": [], "sparse_vector": [], "title": "The evolution of continuous experimentation in software product development: from data to a data-driven organization at scale.", "authors": ["<PERSON>ek<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software development companies are increasingly aiming to become data-driven by trying to continuously experiment with the products used by their customers. Although familiar with the competitive edge that the A/B testing technology delivers, they seldom succeed in evolving and adopting the methodology. In this paper, and based on an exhaustive and collaborative case study research in a large software-intense company with highly developed experimentation culture, we present the evolution process of moving from ad-hoc customer data analysis towards continuous controlled experimentation at scale. Our main contribution is the \"Experimentation Evolution Model\" in which we detail three phases of evolution: technical, organizational and business evolution. With our contribution, we aim to provide guidance to practitioners on how to develop and scale continuous experimentation in software organizations with the purpose of becoming data-driven at scale.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.76"}, {"primary_key": "3802315", "vector": [], "sparse_vector": [], "title": "UML diagram refinement (focusing on class- and use case diagrams).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large and complicated UML models are not useful, because they are difficult to understand. This problem can be solved by using several diagrams of the same system at different levels of abstraction. Unfortunately, UML does not define an explicit set of rules for ensuring that diagrams at different levels of abstraction are consistent. We define such a set of rules, that we call diagram refinement. Diagram refinement is intuitive, and applicable to several kinds of UML diagrams (mostly to structural diagrams but also to use case diagrams), yet it rests on a solid mathematical basis-the theory of graph homomorphisms. We illustrate its usefulness with a series of examples.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.73"}, {"primary_key": "3802316", "vector": [], "sparse_vector": [], "title": "Elicitation of delightful context-aware features: challenges and outlook.", "authors": ["<PERSON>"], "summary": "Current requirements elicitation techniques do not deal with an essential part of context awareness: comprehension of the relationships among the numerous contextual elements of a certain domain and how they can positively influence the user task. As a result, solution providers continuously miss the opportunity to delight users by identifying contextual behaviors that will lead to better recommendations or adaptations. This paper discusses this problem and proposes a data-based solution. The expected scientific contributions of the ongoing research are delineated as well.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.23"}, {"primary_key": "3802318", "vector": [], "sparse_vector": [], "title": "Workflow management systems benchmarking: unfulfilled expectations and lessons learned.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Workflow Management Systems (WfMSs) are a type of middleware that enables the execution of automated business processes. Users rely on WfMSs to construct flexible and easily maintainable software systems. Significant effort has been invested into standardising languages for business processes execution, with standards such as the Web Services Business Process Execution Language 2.0 or the Business Process Model and Notation 2.0. Standardisation aims at avoiding vendor lock-in and enabling WfMS users to compare different systems. The reality is that, despite standardisation efforts, different independent research initiatives show that objectively comparing WfMSs is still challenging. As a result, WfMS users are likely to discover unfulfilled expectations while evaluating and using these systems. In this work, we discuss the findings of two research initiatives dealing with WfMSs benchmarking, presenting unfulfilled expectations and lessons learned concerning WfMSs' usability, reliability, and portability. Our goal is to provide advice for practitioners implementing or planning to use WfMSs.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.126"}, {"primary_key": "3802319", "vector": [], "sparse_vector": [], "title": "Assisting non-specialist developers to build energy-efficient software.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we introduce CECOTOOL, a tool that analyzes the energy behavior of alternative collection implementations and provides potentially useful recommendations about good implementation options. We applied it to two real-world software systems from the DaCapo suite [1], Xalan and Tomcat. With no prior knowledge of the application domains, we were able to reduce the energy consumption up to 4.37%.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.133"}, {"primary_key": "3802320", "vector": [], "sparse_vector": [], "title": "Software certification in practice: how are standards being applied?", "authors": ["<PERSON>"], "summary": "Certification schemes exist to regulate software systems and prevent them from being deployed before they are judged fit to use. However, practitioners are often unsatisfied with the efficiency of certification standards and processes. In this study, we analyzed two certification standards, Common Criteria and DO-178C, and collected insights from literature and from interviews with subject-matter experts to identify concepts affecting the efficiency of certification processes. Our results show that evaluation time, reusability of evaluation artifacts, and composition of systems and certified artifacts are barriers to achieve efficient certification.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.156"}, {"primary_key": "3802321", "vector": [], "sparse_vector": [], "title": "From diversity by numbers to diversity as process: supporting inclusiveness in software development teams with brainstorming.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Negative experiences in diverse software development teams have the potential to turn off minority participants from future team-based software development activity. We examine the use of brainstorming as one concrete team processes that may be used to improve the satisfaction of minority developers when working in a group. Situating our study in time-intensive hackathon-like environments where engagement of all team members is particularly crucial, we use a combination of survey and interview data to test our propositions. We find that brainstorming strategies are particularly effective for team members who identify as minorities, and support satisfaction with both the process and outcomes of teamwork through different mechanisms.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.22"}, {"primary_key": "3802323", "vector": [], "sparse_vector": [], "title": "End-user software engineering for the personal web: poster.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work we present an approach for creatingPersonal Web applications by reusing existing content that canbe extracted even from third-party Web sites. Our approachstarts with the harvesting of content from diverse Web sites, byDOM manipulation. Users without programming skills areempowered with tools for transforming DOM elements intomeaningful classes of objects that can be reused to build otherdomain-specific applications, such as mashups, Webaugmentations, PIM systems, etc.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.127"}, {"primary_key": "3802324", "vector": [], "sparse_vector": [], "title": "Decoding the representation of code in the brain: an fMRI study of code review and expertise.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Subjective judgments in software engineering tasks are of critical importance but can be difficult to study with conventional means. Medical imaging techniques hold the promise of relating cognition to physical activities and brain structures. In a controlled experiment involving 29 participants, we examine code comprehension, code review and prose review using functional magnetic resonance imaging. We find that the neural representations of programming languages vs. natural languages are distinct. We can classify which task a participant is undertaking based solely on brain activity (balanced accuracy 79%, p <; 0.001). Further, we find that the same set of brain regions distinguish between code and prose (near-perfect correlation, r = 0.99, p <; 0.001). Finally, we find that task distinctions are modulated by expertise, such that greater skill predicts a less differentiated neural representation (r = -0.44, p = 0.016) indicating that more skilled participants treat code and prose more similarly at a neural activation level.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.24"}, {"primary_key": "3802335", "vector": [], "sparse_vector": [], "title": "What good is bayesian data analysis for software engineering?", "authors": ["<PERSON>"], "summary": "This article outlines the problems with classical statistical hypothesis testing and recommends using alternative techniques based on Bayesian statistics, which are largely immune to the shortcomings of statistical hypothesis testing, and which support a robust induction process.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.92"}, {"primary_key": "3802340", "vector": [], "sparse_vector": [], "title": "To type or not to type: quantifying detectable bugs in JavaScript.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "JavaScript is growing explosively and is now used in large mature projects even outside the web domain. JavaScript is also a dynamically typed language for which static type systems, notably Facebook's Flow and Microsoft's TypeScript, have been written. What benefits do these static type systems provide? Leveraging JavaScript project histories, we select a fixed bug and check out the code just prior to the fix. We manually add type annotations to the buggy code and test whether Flow and TypeScript report an error on the buggy code, thereby possibly prompting a developer to fix the bug before its public release. We then report the proportion of bugs on which these type systems reported an error. Evaluating static type systems against public bugs, which have survived testing and review, is conservative: it understates their effectiveness at detecting bugs during private development, not to mention their other benefits such as facilitating code search/completion and serving as documentation. Despite this uneven playing field, our central finding is that both static type systems find an important percentage of public bugs: both Flow 0.30 and TypeScript 2.0 successfully detect 15%!.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.75"}, {"primary_key": "3802341", "vector": [], "sparse_vector": [], "title": "IntelliAd: assisting mobile app developers in measuring ad costs automatically.", "authors": ["<PERSON><PERSON><PERSON>", "Yichuan Man", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In-app mobile advertising serves as a primary source of revenue for most free apps. Such apps are embedded with third-party SDKs for ads displaying and are monetized by user impressions. However, ad placement can sometimes spoil user experience, for example, by too much memory consumption and battery drainage, thus leading to app uninstalling and unfavorable user feedback. Therefore, ensuring user perceptions of mobile ads can be greatly beneficial to app developers. Furthermore, various ad networks and formats make ads selection a great challenge. To achieve this, we design a tool named IntelliAd to automatically measure the ads-related consumption on mobile phones. Based on the measured costs, developers can optimize the ad-embedding schemes for their apps.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.123"}, {"primary_key": "3802342", "vector": [], "sparse_vector": [], "title": "Field testing of software applications.", "authors": ["<PERSON>"], "summary": "When interacting with their software systems, users may have to deal with problems like crashes, failures, and program instability. Faulty software running in the field is not only the consequence of ineffective in-house verification and validation techniques, but it is also due to the complexity and diversity of the interactions between an application and its environment. Many of these interactions can be hardly predicted at testing time, and even when they could be predicted, often there are so many cases to be tested that they cannot be all feasibly addressed before the software is released. This Ph.D. thesis investigates the idea of addressing the faults that cannot be effectively addressed in house directly in the field, exploiting the field itself as testbed for running the test cases. An enormous number of diverse environments would then be available for testing, giving the possibility to run many test cases in many different situations, timely revealing the many failures that would be hard to detect otherwise.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.30"}, {"primary_key": "3802345", "vector": [], "sparse_vector": [], "title": "Ethos, pathos, and logos to prevent sexual harassment at workplaces: a regulatory solution based on operant conditioning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sexual harassment at workplace has been a criticalchallenge for women, especially in the service sector due to oddworking hours. Companies and Government on their part havetaken up measures to protect women employees but theproblem seems persistent. To address this, we have designed aregulatory solution based on operant conditioning. Operantconditioning argues that people's behaviors are primarilycontrolled by consequences. It is therefore possible to appeal totheir ethics (Ethos), emotions (Pathos) and logic (Logos) andshape their behavior for preventing harassment at workplace.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.131"}, {"primary_key": "3802351", "vector": [], "sparse_vector": [], "title": "DotProject+: open-source software for project management education.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Teaching the usage of Project Management (PM) tools is an important part of Software Engineering education. In this context, instructors often adopt some professional PM tool, such as MS-Project, GanttProject or dotProject. However, as such tools lack instructional features, some studies propose the adoption of educational PM tools. Yet, even these tools may be insufficient, due to their limited content coverage, and/or not support the learning of prominent professional PM tools. Attempting to improve this situation, this paper presents the enhancement of a professional open-source PM tool - dotProject - for educational purposes. The enhancements include the completion of the support for project initiating and planning, and the reorganization of the tool's interface to assist its adoption in the classroom. Results from applying the enhanced tool in a series of case studies in PM courses, indicate that dotProject+ contributes to the students' learning and facilitates the teaching of the usage of a PM tool in alignment with the PMBOK.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.128"}, {"primary_key": "3802352", "vector": [], "sparse_vector": [], "title": "Can latent topics in source code predict missing architectural tactics?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Architectural tactics such as heartbeat, resource pooling, and scheduling provide solutions to satisfy reliability, security, performance, and other critical characteristics of a software system. Current design practices advocate rigorous up-front analysis of the system's quality concerns to identify tactics and where in the code they should be used. In this paper, we explore a bottom-up approach to recommend architectural tactics based on latent topics discovered in the source code of projects. We present a recommender system developed by building predictor models which capture relationships between topical concepts in source code and the use of specific architectural tactics in that code. Based on an extensive analysis of over 116,000 open source systems, we identify significant correlations between latent topics in source code and the usage of architectural tactics. We use this information to construct a predictor for generating tactic recommendations. Our approach is validated through a series of experiments which demonstrate the ability to generate package-level tactic recommendations. We provide further validation via two large-scale studies of Apache Hive and Hadoop to illustrate that our recommender system predicts tactics that are actually implemented by developers in later releases.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.10"}, {"primary_key": "3802353", "vector": [], "sparse_vector": [], "title": "Mining software engineering data from GitHub.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "GitHub is the largest collaborative source code hosting site built on top of the Git version control system. The availability of a comprehensive API has made GitHub a target for many software engineering and online collaboration research efforts. In our work, we have discovered that a) obtaining data from GitHub is not trivial, b) the data may not be suitable for all types of research, and c) improper use can lead to biased results. In this tutorial, we analyze how data from GitHub can be used for large-scale, quantitative research, while avoiding common pitfalls. We use the GHTorrent dataset, a queryable offline mirror of the GitHub API data, to draw examples from and present pitfall avoidance strategies.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.164"}, {"primary_key": "3802354", "vector": [], "sparse_vector": [], "title": "Effective bug triage for non reproducible bugs.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The objective of this research work is to develop a proficient recommender system for effective bug triaging. To build this we initiated with introducing a novel time based model, <PERSON><PERSON><PERSON><PERSON><PERSON>, for bug report assignment. Subsequently, we propose a novel AHP based bug assignment approach, W8Prioritizer, based on bug parameter prioritization. We further extend our work for triaging Non-reproducible (NR) bugs, the bugs for which the developer faces difficulty in reproducing. However, certain fraction of these bugs gets fixed later. We propose a novel prediction model, NRFixer to predict the fixability of bug reports marked as NR. In the future, we plan to work on bug report assignment for fixable NR bugs using Visheshagya and W8Prioritizer. Overall our initial results are encouraging and shows the possibility of making a robust recommender system for effective bug report assignment for both reproducible (R) and NR bugs.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.41"}, {"primary_key": "3802355", "vector": [], "sparse_vector": [], "title": "Unhappy developers: bad for themselves, bad for process, and bad for software product.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>"], "summary": "Recent research in software engineering supports the \"happy-productive\" thesis, and the desire of flourishing happiness among programmers is often expressed by industry practitioners. Recent literature has suggested that a cost-effective way to foster happiness and productivity among workers could be to limit unhappiness of developers due to its negative impact. However, possible negative effects of unhappiness are still largely unknown in the software development context. In this paper, we present the first results from a study exploring the consequences of the unhappy developers. Using qualitative data analysis of the survey responses given by 181 participants, we identified 49 potential consequences of unhappiness while developing software. These results have several implications. While raising the awareness of the role of moods, emotions and feelings in software development, we foresee that our classification scheme will spawn new happiness studies linking causes and effects, and it can act as a guideline for developers and managers to foster happiness at work.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.104"}, {"primary_key": "3802359", "vector": [], "sparse_vector": [], "title": "Group developmental psychology and software development performance.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the fact that software development is a product of team effort it is important to investigate the influence of group developmental psychology on software development performance. In this case study we wanted to test how performance (i.e. velocity and planning effectiveness) are related to the group's maturity level. We gave the Group Development Questionnaire (the GDQ) to 19 software developers to assess their group maturity (i.e. their progress in their group development) and ran correlation analysis against the development velocity and planning effectiveness (i.e. earned points over planned points). The results show that group maturity is correlated to planning effectiveness but not velocity, meaning that group development is connected to the team's ability to plan well, but not their ability to implement tasks fast.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.85"}, {"primary_key": "3802361", "vector": [], "sparse_vector": [], "title": "Synthesizing object transformation for dynamic software updating.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Ma", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic software updating (DSU) can upgrade a running program on-the-fly by directly replacing the in-memory code and reusing existing runtime state (e.g., heap objects) for the updated execution. Additionally, it is usually necessary to transform the runtime state into a proper new state to avoid inconsistencies that arise during runtime states reuse among different versions of a program. However, such transformations mostly require human efforts, which is time-consuming and error-prone. This paper presents AOTES, an approach to automating object transformations for dynamic updating of Java programs. AOTES tries to generate the new state by re-executing a method invocation history and leverages symbolic execution to synthesize the history from the current object state without any recording. We evaluated AOTES on software updates taken from Apache Tomcat, Apache FTP Server and Apache SSHD Server. Experimental results show that AOTES successfully handled 47 of 57 object transformations of 18 updated classes, while two state-of-the-art approaches only handled 11 and 6 of 57, respectively.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.96"}, {"primary_key": "3802362", "vector": [], "sparse_vector": [], "title": "App genome: callback sequencing in Android.", "authors": ["<PERSON><PERSON> Guo", "<PERSON><PERSON><PERSON>", "Guangdong Bai", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Guannan Si"], "summary": "Recent analysis shows that the callback sequences are of great importance in the analysis of Android applications (apps for short), due to the app's event-driven nature. However, existing works only extract a part of the callback sequences, depending on the need for their specific properties. We propose App Genome sequencing, an automatic fine-grained callback extraction, covering lifecycle and non-lifecycle, inner-and inter-component callback relations, as well as related attributes, including global objects and operations, along the callback sequences. The extracted App Genome facilitates more complete analysis of Android apps, since it contains more callback sequences and data information, than existing works. We use a process algebra called CSP# to represent the App Genome. We implement our method as a tool, which takes an app as input, automatically generates the CSP# model of the App Genome and automatically invokes the model checker to verify a given property.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.82"}, {"primary_key": "3802364", "vector": [], "sparse_vector": [], "title": "Mining Twitter messages for software evolution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Twitter is a widely used social network. Previous research showed that users engage in Twitter to communicate about software applications via short messages, referred to as tweets, and that some of these tweets are relevant for software evolution. However, a manual analysis is impractical due to the large number of tweets - in the range of thousands per day for popular apps. In this work we present ALERTme, an approach to automatically classify, group and rank tweets about software applications. We apply machine learning techniques for automatically classifying tweets requesting improvements, topic modeling for grouping semantically related tweets and a weighted function for ranking tweets according to their relevance for software evolution. We ran our approach on 68,108 tweets from three different software applications and compared the results against practitioners' assessments. Our results are promising and could help incorporate short, informal user feedback with social components into the software evolution process.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.65"}, {"primary_key": "3802372", "vector": [], "sparse_vector": [], "title": "Mining readme files to support automatic building of Java projects in software repositories: poster.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automatic building of software projects provides a desirable foundation to support a large variety of software engineering research tasks based on open software repositories. In this paper, we propose the first technique to automatically extract software build commands from software readme files and Wiki pages, and combine the extracted commands for software building. Specifically, we leverage the Named Entity Recognition(NER) technique for build-command extraction, and prioritize the extracted build commands to identify which one should beused in software build. Our experiment on top Java projects from Git Hub reveals that, the proposed technique can correctly identify more than 90% of build commands, and can successfully build 84% of the projects that can be built successfully through manual inspection of software support documents.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.114"}, {"primary_key": "3802373", "vector": [], "sparse_vector": [], "title": "Efficient fuzz testing leveraging input, code, and execution.", "authors": ["<PERSON><PERSON>"], "summary": "Any kind of smart testing technique must be very efficient to be competitive with random fuzz testing. State-of the-art test generators are largely inferior to random testing in real world applications. This work proposes to gather and evaluate lightweight analyses that can enable the creation of an efficient and sufficiently effective analysis-assisted fuzz tester. The analyses shall leverage information sources apart from the program under test itself, such as e.g. descriptions of the targeted input format in the form of extended context-free grammars, or hardware counters. As the main contributions, an efficient framework for building fuzzers around given analyses will be created, and with its help analyses will be identified and categorized according to their performance.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.26"}, {"primary_key": "3802375", "vector": [], "sparse_vector": [], "title": "ZenIDS: introspective intrusion detection for PHP applications.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Since its first appearance more than 20 years ago, PHP has steadily increased in popularity, and has become the foundation of the Internet's most popular content management systems (CMS). Of the world's 1 million most visited websites, nearly half use a CMS, and WordPress alone claims 25% market share of all websites. While their easy-to-use templates and components have greatly simplified the work of developing high quality websites, it comes at the cost of software vulnerabilities that are inevitable in such large and rapidly evolving frameworks. Intrusion Detection Systems (IDS) are often used to protect Internet-facing applications, but conventional techniques struggle to keep up with the fast pace of development in today's web applications. Rapid changes to application interfaces increase the workload of maintaining an IDS whitelist, yet the broad attack surface of a web application makes for a similarly verbose blacklist. We developed ZenIDS to dynamically learn the trusted execution paths of an application during a short online training period and report execution anomalies as potential intrusions. We implement ZenIDS as a PHP extension supported by 8 hooks instrumented in the PHP interpreter. Our experiments demonstrate its effectiveness monitoring live web traffic for one year to 3 large PHP applications, detecting malicious requests with a false positive rate of less than .01% after training on fewer than 4,000 requests. ZenIDS excludes the vast majority of deployed PHP code from the whitelist because it is never used for valid requests-yet could potentially be exploited by a remote adversary. We observe 5% performance overhead (or less) for our applications vs. an optimized vanilla LAMP stack.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.29"}, {"primary_key": "3802376", "vector": [], "sparse_vector": [], "title": "Machine-learning-guided selectively unsound static analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a machine-learning-based technique for selectively applying unsoundness in static analysis. Existing bug-finding static analyzers are unsound in order to be precise and scalable in practice. However, they are uniformly unsound and hence at the risk of missing a large amount of real bugs. By being sound, we can improve the detectability of the analyzer but it often suffers from a large number of false alarms. Our approach aims to strike a balance between these two approaches by selectively allowing unsoundness only when it is likely to reduce false alarms, while retaining true alarms. We use an anomaly-detection technique to learn such harmless unsoundness. We implemented our technique in two static analyzers for full C. One is for a taint analysis for detecting format-string vulnerabilities, and the other is for an interval analysis for buffer-overflow detection. The experimental results show that our approach significantly improves the recall of the original unsound analysis without sacrificing the precision.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.54"}, {"primary_key": "3802378", "vector": [], "sparse_vector": [], "title": "Modelling and code generation for real-time embedded systems with UML-RT and papyrus-RT.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper discusses the Model-Driven Engineering (MDE) of real-time embedded (RTE) systems with soft real-time constraints using UML for Real-Time (UML-RT) and Papyrus-RT. UML-RT is a profile of UML specifically designed for RTE systems. It has a long, successful track record of application and tool support via, e.g., IBM Rational RoseRT, IBM RSA-RTE, and now Papyrus-RT. Papyrus-RT is an Eclipse-based, open-source modelling development environment for UML-RT systems. It allows the generation of complete, executable code from models and advances the state-of-art via support for model representation with mixed graphical/textual notations and an extensible code generator. This paper introduces the central modelling concepts of UML-RT and features of Papyrus-RT. It also presents some advanced features of Papyrus-RT such as import capabilities, mixed graphical/textual modelling, and incremental code generation.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.168"}, {"primary_key": "3802380", "vector": [], "sparse_vector": [], "title": "Becoming agile: a grounded theory of agile transitions in practice.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Agile adoption is typically understood as a one-off organizational process involving a staged selection of agile development practices. This view of agility fails to explain the differences in the pace and effectiveness of individual teams transitioning to agile development. Based on a Grounded Theory study of 31 agile practitioners drawn from 18 teams across five countries, we present a grounded theory of becoming agile as a network of on-going transitions across five dimensions: software development practices, team practices, management approach, reflective practices, and culture. The unique position of a software team through this network, and their pace of progress along the five dimensions, explains why individual agile teams present distinct manifestations of agility and unique transition experiences. The theory expands the current understanding of agility as a holistic and complex network of on-going multidimensional transitions, and will help software teams, their managers, and organizations better navigate their individual agile journeys.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.21"}, {"primary_key": "3802382", "vector": [], "sparse_vector": [], "title": "Mining input grammars with AUTOGRAM.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Knowledge about how a program processes its inputs can help to understand the structure of the input as well as the structure of the program. In a JSON value like [1, true, \"Alice\"], for instance the integer value 1, the boolean value true and the string value \"<PERSON>\" would be handled by different functions or stored in different variables. Our AUTOGRAM tool uses dynamic tainting to trace the data flow of each input character for a set of sample inputs and identifies syntactical entities by grouping input fragments that are handled by the same functions. The resulting context-free grammar reflects the structure of valid inputs and can be used for reverse engineering of formats and can serve as direct input for test generators. A video demonstrating AUTOGRAM is available at https://youtu.be/Iqym60iWBBk.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.14"}, {"primary_key": "3802383", "vector": [], "sparse_vector": [], "title": "An empirical examination of abstract test case prioritization techniques.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Abstract test case prioritization (ATCP) aims at ordering abstract test case in order to increase the speed at which faults are detected, potentially increasing the fault detection rate. This paper empirically examines possible ATCP techniques, according to the following four categories: non-information-guided prioritization (NIGP), interaction coverage based prioritization (ICBP), input-model mutation based prioritization (IMBP), and similarity based prioritization (SBP). We found that the ICBP category has better testing effectiveness than others, according to fault detection rates. Surprisingly, we found that NIGP can achieve similar performance to IMBP, and that SBP can sometimes achieve even better rates of fault detection than some ICBP techniques.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.105"}, {"primary_key": "3802384", "vector": [], "sparse_vector": [], "title": "Mining complex temporal API usage patterns: an evolutionary approach.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Learning to use existing or new software libraries is a difficult task for software developers, which would impede their productivity. Much existing work has provided different techniques to mine API usage patterns from client programs inorder to help developers on understanding and using existinglibraries. However, these techniques produce incomplete patterns, i.e., without temporal properties, or simple ones. In this paper, we propose a new formulation of the problem of API temporal pattern mining and a new approach to solve it. Indeed, we learn complex temporal patterns using a genetic programming approach. Our preliminary results show that across a considerable variability of client programs, our approach has been able to infer non-trivial patterns that reflect informative temporal properties.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.147"}, {"primary_key": "3802387", "vector": [], "sparse_vector": [], "title": "Advancing energy testing of mobile applications.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The rising popularity of mobile apps deployed on battery-constrained devices has motivated the need for effective energy-aware testing techniques. However, currently there is a lack of test generation tools for exercising the energy properties of apps. Automated test generation is not useful without tools that help developers to measure the quality of the tests. Additionally, the collection of tests generated for energy testing could be quite large, as it may involve a test suite that covers all the energy hotspots under different use cases. Thereby, there is a need for techniques to manage the size of test suite, while maintaining its effectiveness in revealing energy defects. Our research plan to advance energy testing for mobile applications include various techniques for energy-aware test generation, energy-aware test-suite adequacy assessment, and energy-aware test-suite minimization.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.45"}, {"primary_key": "3802393", "vector": [], "sparse_vector": [], "title": "Running software research programs: an agile approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Agile, lean processes have become the de-facto way to operate in the domain of software intensive products. Methodologies such as the lean startup are reshaping the way new companies and even well-established enterprises seek new opportunities in their operations. In contrast, in research, little has changed during that time - organizations that fund research still expect a solid, linear research plan. In this paper, we present an attempt to challenge this model in software research, based on 7 years of experiences in two large, national, industry-led projects that followed a more agile mindset. Furthermore, we also provide an insight to key learnings and best practices of running software research in agile fashion.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.59"}, {"primary_key": "3802396", "vector": [], "sparse_vector": [], "title": "What causes my test alarm?: automatic cause analysis for test alarms in system and integration testing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Driven by new software development processes and testing in clouds, system and integration testing nowadays tends to produce enormous number of alarms. Such test alarms lay an almost unbearable burden on software testing engineers who have to manually analyze the causes of these alarms. The causes are critical because they decide which stakeholders are responsible to fix the bugs detected during the testing. In this paper, we present a novel approach that aims to relieve the burden by automating the procedure. Our approach, called Cause Analysis Model, exploits information retrieval techniques to efficiently infer test alarm causes based on test logs. We have developed a prototype and evaluated our tool on two industrial datasets with more than 14,000 test alarms. Experiments on the two datasets show that our tool achieves an accuracy of 58.3% and 65.8%, respectively, which outperforms the baseline algorithms by up to 13.3%. Our algorithm is also extremely efficient, spending about 0.1s per cause analysis. Due to the attractive experimental results, our industrial partner, a leading information and communication technology company in the world, has deployed the tool and it achieves an average accuracy of 72% after two months of running, nearly three times more accurate than a previous strategy based on regular expressions.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.71"}, {"primary_key": "3802397", "vector": [], "sparse_vector": [], "title": "An unsupervised approach for discovering relevant tutorial fragments for APIs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Developers increasingly rely on API tutorials to facilitate software development. However, it remains a challenging task for them to discover relevant API tutorial fragments explaining unfamiliar APIs. Existing supervised approaches suffer from the heavy burden of manually preparing corpus-specific annotated data and features. In this study, we propose a novel unsupervised approach, namely Fragment Recommender for APIs with PageRank and Topic model (FRAPT). FRAPT can well address two main challenges lying in the task and effectively determine relevant tutorial fragments for APIs. In FRAPT, a Fragment Parser is proposed to identify APIs in tutorial fragments and replace ambiguous pronouns and variables with related ontologies and API names, so as to address the pronoun and variable resolution challenge. Then, a Fragment Filter employs a set of non-explanatory detection rules to remove non-explanatory fragments, thus address the non-explanatory fragment identification challenge. Finally, two correlation scores are achieved and aggregated to determine relevant fragments for APIs, by applying both topic model and PageRank algorithm to the retained fragments. Extensive experiments over two publicly open tutorial corpora show that, FRAPT improves the state-of-the-art approach by 8.77% and 12.32% respectively in terms of F-Measure. The effectiveness of key components of FRAPT is also validated.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.12"}, {"primary_key": "3802398", "vector": [], "sparse_vector": [], "title": "Short-term revisit during programming tasks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous studies of Web page revisitation were only focused on long-term revisit ranging from hours to days. In this paper, we study the short-term revisit of less than one hour such as the revisit behavior during a small programming task. We first perform an exploratory study to observe the short-term revisit phenomenon. We then perform controlled experiments with our designed tool support as treatment by inviting 20 biomedical software developers to perform two software change tasks. Our results show that the participants with tool support used 19.7% less time than the ones without tool support.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.93"}, {"primary_key": "3802399", "vector": [], "sparse_vector": [], "title": "Classifying developers into core and peripheral: an empirical study on count and network metrics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Knowledge about the roles developers play in a software project is crucial to understanding the project's collaborative dynamics. In practice, developers are often classified according to the dichotomy of core and peripheral roles. Typically, count-based operationalizations, which rely on simple counts of individual developer activities (e.g., number of commits), are used for this purpose, but there is concern regarding their validity and ability to elicit meaningful insights. To shed light on this issue, we investigate whether count-based operationalizations of developer roles produce consistent results, and we validate them with respect to developers' perceptions by surveying 166 developers. Improving over the state of the art, we propose a relational perspective on developer roles, using fine-grained developer networks modeling the organizational structure, and by examining developer roles in terms of developers' positions and stability within the developer network. In a study of 10 substantial open-source projects, we found that the primary difference between the count-based and our proposed network-based core-peripheral operationalizations is that the network-based ones agree more with developer perception than count-based ones. Furthermore, we demonstrate that a relational perspective can reveal further meaningful insights, such as that core developers exhibit high positional stability, upper positions in the hierarchy, and high levels of coordination with other core developers, which confirms assumptions of previous work.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.23"}, {"primary_key": "3802405", "vector": [], "sparse_vector": [], "title": "How good is a security policy against real breaches?: a HIPAA case study.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Policy design is an important part of software development. As security breaches increase in variety, designing a security policy that addresses all potential breaches becomes a nontrivial task. A complete security policy would specify rules to prevent breaches. Systematically determining which, if any, policy clause has been violated by a reported breach is a means for identifying gaps in a policy. Our research goal is to help analysts measure the gaps between security policies and reported breaches by developing a systematic process based on semantic reasoning. We propose SEMAVER, a framework for determining coverage of breaches by policies via comparison of individual policy clauses and breach descriptions. We represent a security policy as a set of norms. Norms (commitments, authorizations, and prohibitions) describe expected behaviors of users, and formalize who is accountable to whom and for what. A breach corresponds to a norm violation. We develop a semantic similarity metric for pairwise comparison between the norm that represents a policy clause and the norm that has been violated by a reported breach. We use the US Health Insurance Portability and Accountability Act (HIPAA) as a case study. Our investigation of a subset of the breaches reported by the US Department of Health and Human Services (HHS) reveals the gaps between HIPAA and reported breaches, leading to a coverage of 65%. Additionally, our classification of the 1,577 HHS breaches shows that 44% of the breaches are accidental misuses and 56% are malicious misuses. We find that HIPAA's gaps regarding accidental misuses are significantly larger than its gaps regarding malicious misuses.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.55"}, {"primary_key": "3802410", "vector": [], "sparse_vector": [], "title": "Local analysis for global inputs.", "authors": ["<PERSON>"], "summary": "Fuzz testing and symbolic test generation both face their own challenges. While symbolic testing has scalability issues, fuzzing cannot uncover faults which require carefully engineered inputs. In this paper I propose a combination of both approaches, compensating weaknesses of each approach with the strength of the other approach. I present my plans for evaluation, which include applications of the hybrid tool to programs which neither of the approaches can handle on its own.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.32"}, {"primary_key": "3802414", "vector": [], "sparse_vector": [], "title": "Causal modeling, discovery &amp; inference for software engineering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This causal discovery analysis is intended as an initial step, and is certainly not the final word. For example, one could apply multiple causal discovery algorithms to measure the sensitivity of the learned structures to the use of the PC algorithm. Moreover, software projects exhibit significant dynamics over time, as code is written, refined, refactored, and so forth. We used static datasets that provide snapshots of the projects at particular moments in time. If we collect longitudinal data about similar variables, then we could start to uncover the underlying causal dynamics. One might also suspect that those dynamics could shift over time, as the software practices and philosophies change, as project members enter and leave, etc. Longitudinal data could also enable us to test for this type of causal non-stationarity. The key point that we have established here, however, is the first demonstration of the applicability and usefulness of causal discovery algorithms applied to observational software engineering datasets.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.138"}, {"primary_key": "3802416", "vector": [], "sparse_vector": [], "title": "Towards a context dependent Java exceptions hierarchy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The role of exceptions is crucial for the robustness of modern applications and critical systems. Despite this, there is a long debate among researchers, programming language designers, and practitioners regarding the usefulness and appropriateness of the available exception types and their classification. In this paper, we examine Java exceptions and propose a new class hierarchy and compile-time mechanisms that take into account the context in which exceptions can arise. We believe that the increased specificity of exception handling based on our proposal can boost its effectiveness and lead to fewer application failures.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.134"}, {"primary_key": "3802418", "vector": [], "sparse_vector": [], "title": "Using eye gaze data to recognize task-relevant source code better and more fine-grained.", "authors": ["<PERSON><PERSON>"], "summary": "Models to assess a source code element's relevancy for a given change task are the basis of many software engineering tools, such as recommender systems, for code comprehension. To improve such relevancy models and to aid developers in finding relevant parts in the source code faster, we studied developer's fine-grained navigation patterns with eye tracking technology. By combining the captured eye gaze data with interaction data of 12 developers working on a change task, we were able to identify relevant methods with high accuracy and improve precision and recall compared to the widely used click frequency technique by 77% and 24% respectively. Furthermore, we were able to show that the captured gaze data enables to retrace which source code lines developers found relevant. Our results thus provide evidence that eye gaze data can be used to improve existing models in terms of accuracy and granularity.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.152"}, {"primary_key": "3802421", "vector": [], "sparse_vector": [], "title": "Automated refactoring of legacy Java software to default methods.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Java 8 default methods, which allow interfaces to contain (instance) method implementations, are useful for the skeletal implementation software design pattern. However, it is not easy to transform existing software to exploit default methods as it requires analyzing complex type hierarchies, resolving multiple implementation inheritance issues, reconciling differences between class and interface methods, and analyzing tie-breakers (dispatch precedence) with overriding class methods to preserve type-correctness and confirm semantics preservation. In this paper, we present an efficient, fully-automated, type constraint-based refactoring approach that assists developers in taking advantage of enhanced interfaces for their legacy Java software. The approach features an extensive rule set that covers various corner-cases where default methods cannot be used. To demonstrate applicability, we implemented our approach as an Eclipse plug-in and applied it to 19 real-world Java projects, as well as submitted pull requests to popular GitHub repositories. The indication is that it is useful in migrating skeletal implementation methods to interfaces as default methods, sheds light onto the pattern's usage, and provides insight to language designers on how this new construct applies to existing software.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.16"}, {"primary_key": "3802424", "vector": [], "sparse_vector": [], "title": "Software-related challenges of testing automated vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated vehicles are not supposed to fail at any time or in any situations during driving. Thus, vehicle manufactures and proving ground operators are challenged to complement existing test procedures with means to systematically evaluate automated driving. In this paper, we explore software related challenges from testing the safety of automated vehicles. We report on findings from conducting focus groups and interviews including 26 participants (e.g., vehicle manufacturers, suppliers, and researchers) from five countries.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.67"}, {"primary_key": "3802426", "vector": [], "sparse_vector": [], "title": "Managing assurance cases in model based software systems.", "authors": ["<PERSON><PERSON>"], "summary": "Software has emerged as a significant part of many domains, including financial service platforms, social networks and vehicle control. Standards organizations have responded to this by creating regulations to address issues such as safety and privacy. In this context, compliance of software with standards has emerged as a key issue. For software development organizations, compliance is a complex and costly goal to achieve and is often accomplished by producing so-called assurance cases, which demonstrate that the system indeed satisfies the property imposed by a standard (e.g., safety, privacy, security). As systems and standards undergo evolution for a variety of reasons, maintaining assurance cases multiplies the effort. In this work, we propose to exploit the connection between the field of model management and the problem of compliance management and propose methods that use model management techniques to address compliance scenarios such as assurance case evolution and reuse. For validation, we ground our approaches on the automotive domain and the ISO 26262 standard for functional safety of road vehicles.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.44"}, {"primary_key": "3802431", "vector": [], "sparse_vector": [], "title": "Lost in source code: physically separating features in legacy systems.", "authors": ["<PERSON>"], "summary": "Feature-oriented programming allows developers to physically separate and reuse features via composition. This promises several benefits compared to other reuse approaches, for instance, easier traceability and maintenance. However, due to their simplicity cloning and annotation-based product lines are established in practice. We aim to reduce risks and costs of migrating towards composition, lowering the adoption barrier. This includes i) processes, ii) migration approaches, and iii) assessing advantages and disadvantages. Overall, we will facilitate integrating physical separation into legacy applications.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.46"}, {"primary_key": "3802436", "vector": [], "sparse_vector": [], "title": "Artifact driven communication to improve program comprehension.", "authors": ["<PERSON><PERSON>"], "summary": "Developer communication is an important factor during program comprehension. Live programming environments encourage developers to comprehend applications through manipulation of running instances-liveness. Such application exploration is interrupted whenever programmers need to communicate an issue with dislocated co-workers. Describing the issue becomes challenging as programmers use text based communication mediums, e.g., emails or chats. The issues are magnified during bug day events, where developers around the world meet together in order to improve a project. Communication and coordination need to be increased, but the infrastructure stays the same. We target the research gap by introducing the COLLABORATIVE PLAYGROUND, a tool that exposes liveness into developer communication during programming change tasks and integrates coordination needs during bug day events. We will evaluate our approach by deploying the COLLABORATIVE PLAYGROUND during a series of Bug Day events for Pharo, one of the most active live programming platforms in existence.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.47"}, {"primary_key": "3802437", "vector": [], "sparse_vector": [], "title": "RapMOD - in situ auto-completion for graphical models: poster.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Auto-completion of textual inputs benefits software developers using IDEs. However, graphical modeling tools used to design software do not yet provide such functionality. The challenges of recommending auto-completions for graphical modeling activities are largely unexplored. Recommending such auto-completions requires detecting meaningful partly completed activities, tolerating variance in user actions, and determining the most relevant activities that a user wants to perform. We propose RapMOD, an approach that works in the background while a developer is creating or evolving models and handles all these challenges. Users' editing operations are analyzed and matched to a predefined but extensible catalog of common modeling activities for structural UML models. We found RapMOD to significantly reduce modeling effort.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.119"}, {"primary_key": "3802440", "vector": [], "sparse_vector": [], "title": "Teaching predictive modeling to junior software engineers - seminar format and its evaluation: poster.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the increased importance of machine learning in software and security engineering, effective trainings are needed that allow software engineers to learn the required basic knowledge to understand and successfully apply prediction models fast. In this paper, we present a two-days seminar to teach machine learning-based prediction in software engineering and the evaluation ofits learning effects based on <PERSON>'s taxonomy. As a teaching scenario for the practical part, we used a paper reporting a research study on the application of machine learning techniques to predict vulnerabilities in the code. The results of the evaluation showed that the seminar is an appropriate format for teaching predictive modeling to software engineers. The participants were very enthusiastic and self-motivated to learn about the topic and the empirical investigation based on <PERSON>'s taxonomy showed positive learning effects on the knowledge, comprehension, application, analysis, and evaluation level.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.62"}, {"primary_key": "3802441", "vector": [], "sparse_vector": [], "title": "Challenges for static analysis of Java reflection: literature review and empirical study.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The behavior of software that uses the Java Reflection API is fundamentally hard to predict by analyzing code. Only recent static analysis approaches can resolve reflection under unsound yet pragmatic assumptions. We survey what approaches exist and what their limitations are. We then analyze how real-world Java code uses the Reflection API, and how many Java projects contain code challenging state-of-the-art static analysis. Using a systematic literature review we collected and categorized all known methods of statically approximating reflective Java code. Next to this we constructed a representative corpus of Java systems and collected descriptive statistics of the usage of the Reflection API. We then applied an analysis on the abstract syntax trees of all source code to count code idioms which go beyond the limitation boundaries of static analysis approaches. The resulting data answers the research questions. The corpus, the tool and the results are openly available. We conclude that the need for unsound assumptions to resolve reflection is widely supported. In our corpus, reflection can not be ignored for 78% of the projects. Common challenges for analysis tools such as non-exceptional exceptions, programmatic filtering meta objects, semantics of collections, and dynamic proxies, widely occur in the corpus. For Java software engineers prioritizing on robustness, we list tactics to obtain more easy to analyze reflection code, and for static analysis tool builders we provide a list of opportunities to have significant impact on real Java code.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.53"}, {"primary_key": "3802446", "vector": [], "sparse_vector": [], "title": "A SEALANT for inter-app security holes in android.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Android's communication model has a major security weakness: malicious apps can manipulate other apps into performing unintended operations and can steal end-user data, while appearing ordinary and harmless. This paper presents SEALANT, a technique that combines static analysis of app code, which infers vulnerable communication channels, with runtime monitoring of inter-app communication through those channels, which helps to prevent attacks. SEALANT's extensive evaluation demonstrates that (1) it detects and blocks inter-app attacks with high accuracy in a corpus of over 1,100 real-world apps, (2) it suffers from fewer false alarms than existing techniques in several representative scenarios, (3) its performance overhead is negligible, and (4) end-users do not find it challenging to adopt.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.36"}, {"primary_key": "3802447", "vector": [], "sparse_vector": [], "title": "Understanding the impressions, motivations, and barriers of one time code contributors to FLOSS projects: a survey.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Successful Free/Libre Open Source Software (FLOSS) projects must attract and retain high-quality talent. Researchers have invested considerable effort in the study of core and peripheral FLOSS developers. To this point, one critical subset of developers that have not been studied are One-Time code Contributors (OTC) - those that have had exactly one patch accepted. To understand why OTCs have not contributed another patch and provide guidance to FLOSS projects on retaining OTCs, this study seeks to understand the impressions, motivations, and barriers experienced by OTCs. We conducted an online survey of OTCs from 23 popular FLOSS projects. Based on the 184 responses received, we observed that OTCs generally have positive impressions of their FLOSS project and are driven by a variety of motivations. Most OTCs primarily made contributions to fix bugs that impeded their work and did not plan on becoming long term contributors. Furthermore, OTCs encounter a number of barriers that prevent them from continuing to contribute to the project. Based on our findings, there are some concrete actions FLOSS projects can take to increase the chances of converting OTCs into long-term contributors.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.25"}, {"primary_key": "3802449", "vector": [], "sparse_vector": [], "title": "Analyzing forty years of software maintenance models.", "authors": ["Valentina <PERSON>uzzi", "<PERSON>", "<PERSON><PERSON>"], "summary": "Software maintenance has dramatically evolved in the last four decades, to cope with the continuously changing software development models, and programming languages and adopting increasingly advanced prediction models. In this work, we present the initial results of a Systematic Literature Review (SLR), highlighting the evolution of the metrics and models adopted in the last forty years.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.122"}, {"primary_key": "3802453", "vector": [], "sparse_vector": [], "title": "Relating alternate modifications to defect density in software development.", "authors": ["<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON>"], "summary": "A software developer tends to spend more effort to understand source code written by other developers than code written by herself. Existing research studying the impact of changes made by multiple developers on bug proneness did not consider quantitatively the influence of the order of the changes made by different developers. We found that a significant proportion of source files in many open source software systems had been modified alternately by different developers. We developed a metric, namely alternate modification index (AMI), to indicate the extent to which a source file is modified alternately by multiple developers. Our preliminary case study results show that AMI of source files has a strong positive correlation with their defect density.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.132"}, {"primary_key": "3802454", "vector": [], "sparse_vector": [], "title": "LibD: scalable and precise third-party library detection in android markets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the thriving of the mobile app markets, third-party libraries are pervasively integrated in the Android applications. Third-party libraries provide functionality such as advertisements, location services, and social networking services, making multi-functional app development much more productive. However, the spread of vulnerable or harmful third-party libraries may also hurt the entire mobile ecosystem, leading to various security problems. The Android platform suffers severely from such problems due to the way its ecosystem is constructed and maintained. Therefore, third-party Android library identification has emerged as an important problem which is the basis of many security applications such as repackaging detection and malware analysis. According to our investigation, existing work on Android library detection still requires improvement in many aspects, including accuracy and obfuscation resilience. In response to these limitations, we propose a novel approach to identifying third-party Android libraries. Our method utilizes the internal code dependencies of an Android app to detect and classify library candidates. Different from most previous methods which classify detected library candidates based on similarity comparison, our method is based on feature hashing and can better handle code whose package and method names are obfuscated. Based on this approach, we have developed a prototypical tool called LibD and evaluated it with an update-to-date and large-scale dataset. Our experimental results on 1,427,395 apps show that compared to existing tools, LibD can better handle multi-package third-party libraries in the presence of name-based obfuscation, leading to significantly improved precision without the loss of scalability.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.38"}, {"primary_key": "3802455", "vector": [], "sparse_vector": [], "title": "DroidBot: a lightweight UI-guided test input generator for Android.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As many automated test input generation tools for Android need to instrument the system or the app, they cannot be used in some scenarios such as compatibility testing and malware analysis. We introduce DroidBot, a lightweight UI-guided test input generator, which is able to interact with an Android app on almost any device without instrumentation. The key technique behind DroidBot is that it can generate UI-guided test inputs based on a state transition model generated on-the-fly, and allow users to integrate their own strategies or algorithms. DroidBot is lightweight as it does not require app instrumentation, thus users do not need to worry about the inconsistency between the tested version and the original version. It is compatible with most Android apps, and able to run on almost all Android-based systems, including customized sandboxes and commodity devices. Droidbot is released as an open-source tool on GitHub, and the demo video can be found at https://youtu.be/3-aHG_SazMY.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.8"}, {"primary_key": "3802459", "vector": [], "sparse_vector": [], "title": "Product line architecture recovery: an approach proposal.", "authors": ["Crescencio <PERSON>"], "summary": "The Product Line Architecture (PLA) is an important asset for the success of Software Product Line (SPL) projects. Due to the complexity of managing the architectural variability, maintain the PLA up-to-date and synchronized with the project source code is a hard problem. The systematic use of Software Architecture Recovery (SAR) techniques enables the PLA recovery and keeps the PLA aligned with the development. In this context, we present our initial proposal that consists of an approach to recover PLAs based on the use of (bottom-up) SAR techniques. We performed some studies (such as surveys, literature reviews, and exploratory studies) to investigate the relationship between SAR and PLA to identify gaps and define the research area state-of-the-art. The combination of SAR and PLA is an important strategy to address some issues of PLA design. We identified that few studies address architectural variability, PLA variability traceability, and empirical evaluation such as experiments, surveys, mixed-methods, and so on.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.38"}, {"primary_key": "3802460", "vector": [], "sparse_vector": [], "title": "Feedback-based debugging.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software debugging has long been regarded as a time and effort consuming task. In the process of debugging, developers usually need to manually inspect many program steps to see whether they deviate from their intended behaviors. Given that intended behaviors usually exist nowhere but in human mind, the automation of debugging turns out to be extremely hard, if not impossible. In this work, we propose a feedback-based debugging approach, which (1) builds on light-weight human feedbacks on a buggy program and (2) regards the feedbacks as partial program specification to infer suspicious steps of the buggy execution. Given a buggy program, we record its execution trace and allow developers to provide light-weight feedback on trace steps. Based on the feedbacks, we recommend suspicious steps on the trace. Moreover, our approach can further learn and approximate bug-free paths, which helps reduce required feedbacks to expedite the debugging process. We conduct an experiment to evaluate our approach with simulated feedbacks on 3409 mutated bugs across 3 open source projects. The results show that our feedback-based approach can detect 92.8% of the bugs and 65% of the detected bugs require less than 20 feedbacks. In addition, we implement our proof-of-concept tool, Microbat, and conduct a user study involving 16 participants on 3 debugging tasks. The results show that, compared to the participants using the baseline tool, Why<PERSON>, the ones using Microbat can spend on average 55.8% less time to locate the bugs.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.43"}, {"primary_key": "3802462", "vector": [], "sparse_vector": [], "title": "Stochastic optimization of program obfuscation.", "authors": ["<PERSON>", "Chengnian Sun", "<PERSON><PERSON><PERSON>", "Yu <PERSON>", "<PERSON>", "Jiaguang Sun"], "summary": "Program obfuscation is a common practice in software development to obscure source code or binary code, in order to prevent humans from understanding the purpose or logic of software. It protects intellectual property and deters malicious attacks. While tremendous efforts have been devoted to the development of various obfuscation techniques, we have relatively little knowledge on how to most effectively use them together. The biggest challenge lies in identifying the most effective combination of obfuscation techniques. This paper presents a unified framework to optimize program obfuscation. Given an input program P and a set T of obfuscation transformations, our technique can automatically identify a sequence seq = 〈t 1 , t 2 , ..., t n 〉 (∀i ∈ [1, n]. t i ∈ T), such that applying ti in order on P yields the optimal obfuscation performance. We model the process of searching for seq as a mathematical optimization problem. The key technical contributions of this paper are: (1) an obscurity language model to assess obfuscation effectiveness/optimality, and (2) a guided stochastic algorithm based on Markov chain Monte Carlo methods to search for the optimal solution seq. We have realized the framework in a tool Closure* for JavaScript, and evaluated it on 25 most starred JavaScript projects on GitHub (19K lines of code). Our machinery study shows that Closure* outperforms the well-known Google Closure Compiler by defending 26% of the attacks initiated by JSNice. Our human study also reveals that Closure* is practical and can reduce the human attack success rate by 30%.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.28"}, {"primary_key": "3802466", "vector": [], "sparse_vector": [], "title": "Learning graph representations for defect prediction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose to study the impact of the representation of the data in defect prediction models. For this study, we focus on the use of developer activity data, from which we structure dependency graphs. Then, instead of manually generating features, such as network metrics, we propose a model inspired in recent advances in Representation Learning which are able to automatically learn representations from graph data. These new representations are compared against manually crafted features for defect prediction in real world software projects.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.68"}, {"primary_key": "3802468", "vector": [], "sparse_vector": [], "title": "PSpec: a formal specification language for fine-grained control on distributed data analytics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Dong Yan", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Organizations often share business data with third-parties to perform data analytics. However, the business data may contain a lot of customers' private information. One major concern of these organizations is thus to ensure such private information is properly used. In this paper, we present PSpec, a formal language for specifying data usage restrictions in distributed data analytics. Compared with previous works, PSpec specializes in data analytics and provides explicit support for data desensitization and association to balance data privacy and utility. We moreover present redundancy and conflict analysis algorithms to help data owners write PSpec privacy policies. To evaluate PSpec we carry out a case study on TPC-DS benchmark. The results demonstrate applicability and practicality of the PSpec language.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.120"}, {"primary_key": "3802469", "vector": [], "sparse_vector": [], "title": "How do developers fix cross-project correlated bugs?: a case study on the GitHub scientific python ecosystem.", "authors": ["Wanwangying Ma", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "GitHub, a popular social-software-development platform, has fostered a variety of software ecosystems where projects depend on one another and practitioners interact with each other. Projects within an ecosystem often have complex inter-dependencies that impose new challenges in bug reporting and fixing. In this paper, we conduct an empirical study on cross-project correlated bugs, i.e., causally related bugs reported to different projects, focusing on two aspects: 1) how developers track the root causes across projects, and 2) how the downstream developers coordinate to deal with upstream bugs. Through manual inspection of bug reports collected from the scientific Python ecosystem and an online survey with developers, this study reveals the common practices of developers and the various factors in fixing cross-project bugs. These findings provide implications for future software bug analysis in the scope of ecosystem, as well as shed light on the requirements of issue trackers for such bugs.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.42"}, {"primary_key": "3802470", "vector": [], "sparse_vector": [], "title": "Aladdin: automating release of Android deep links to in-app content.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Unlike the Web where each web page has a global URL to reach, a specific \"content page\" inside a mobile app cannot be opened unless the user explores the app with several operations from the landing page. Recently, deep links have been advocated by major companies to enable targeting and opening a specific page of an app externally with an accessible uniform resource identifier (URI). In this paper, we present an empirical study of deep links over 20,000 Android apps, and find that deep links do not get wide adoption among current Android apps, and non-trivial manual efforts are required for app developers to support deep links. To address such an issue, we propose the Aladdin approach and supporting tool to release deep links to access arbitrary locations of existing apps. We evaluate Aladdin with popular apps and demonstrate its effectiveness and performance.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.107"}, {"primary_key": "3802473", "vector": [], "sparse_vector": [], "title": "Preventing and repairing build breakage.", "authors": ["<PERSON>"], "summary": "Build systems play a crucial role in modern software engineering. Recent studies have shown that many builds fail, mostly due to neglected maintenance. This blocks teams from continuing the development and costs time and resources to fix. The target of the thesis is to reduce build breakage by investigating changes that lead to failing builds, identifying bad and best practices for build configuration, and providing an approach to automatically repair broken builds. As a first step, we conduct empirical studies to determine changes and change patterns that lead to build breakage and reveal the reasons for build breakage. Based on these findings, we develop an approach to automatically refactor build configurations that are likely to fail and an approach to repair broken builds. We plan to evaluate our approaches first, quantitatively by measuring the performance of our approaches in open source projects and second, qualitatively by asking developers to use our approaches and give feedback on their applicability and usefulness.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.29"}, {"primary_key": "3802477", "vector": [], "sparse_vector": [], "title": "Combining machine-learning with invariants assurance techniques for autonomous systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Autonomous Systems are systems situated in some environment and are able of taking decision autonomously. The environment is not precisely known at design-time and it might be full of unforeseeable events that the autonomous system has to deal with at run-time. This brings two main problems to be addressed. One is that the uncertainty of the environment makes it difficult to model all the behaviours that the autonomous system might have at the design-time. A second problem is that, especially for safety-critical systems, maintaining the safety requirements is fundamental despite the system's adaptations. We address such problems by shifting some of the assurance tasks at run-time. We propose a method for delegating part of the decision making to agent-based algorithms using machine learning techniques. We then monitor at run-time that the decisions do not violate the autonomous system's safety-critical requirements and by doing so we also send feedback to the decision-making process so that it can learn. We plan to implement this approach using reinforcement learning for decision making and predictive monitoring for checking at run-time the preservation and/or violation of invariant properties of the system. We also plan to validate it using ROS as software middleware and miniaturized vehicles and real vehiclesas hardware.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.40"}, {"primary_key": "3802478", "vector": [], "sparse_vector": [], "title": "Selection of software components from business objectives scenarios through architectural tactics.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The architecture of a software system is result of architectural design decisions, where architects select among alternatives (architectural tactics) and software components when a stakeholders business objective is demanded. However, thereis not evidence of framework that conducts the appropriateselection of software components using architectural tactics. In this paper we present a PhD research proposal that describes a framework to obtain software components from business goal scenarios using architectural tactics supported by semantic recommendation systems. The expected results of this research is a technique and tool to acquire assemblies of software components that are more accurate in a certain context at the moment to propose solutions to the software architect in order to improve design decisions.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.35"}, {"primary_key": "3802481", "vector": [], "sparse_vector": [], "title": "Bottom-up technologies for reuse: automated extractive adoption of software product lines.", "authors": ["<PERSON><PERSON><PERSON>", "Tewfik <PERSON>", "Tegawendé F. Bissyandé", "<PERSON>", "<PERSON>"], "summary": "Adopting Software Product Line (SPL) engineering principles demands a high up-front investment. Bottom-Up Technologies for Reuse (BUT4Reuse) is a generic and extensible tool aimed to leverage existing similar software products in order to help in extractive SPL adoption. The envisioned users are 1) SPL adopters and 2) Integrators of techniques and algorithms to provide automation in SPL adoption activities. We present the methodology it implies for both types of users and we present the validation studies that were already conducted. BUT4Reuse tool and source code are publicly available under the EPL license. Website http://but4reuse.github.io Video: https://www.youtube.com/watch?v=pa62Yc9LWyk.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.15"}, {"primary_key": "3802482", "vector": [], "sparse_vector": [], "title": "A tool supporting postponable refactoring.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Failures of precondition checking when attempting to apply automated refactorings often discourage programmers from attempting to use these refactorings in the future. To alleviate this situation, the postponement of the failed refactoring instead its cancellation is beneficial. This poster paper proposes a new concept of postponable refactoring and a prototype tool that implements postponable Extract Method as an Eclipse plug-in. We believe that this refactoring tool inspires a new field of reconciliation automated and manual refactoring.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.108"}, {"primary_key": "3802486", "vector": [], "sparse_vector": [], "title": "Trends in topics at SE conferences (1993-2013).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Using topic modeling, we analyse the titles and abstracts of nearly 10,000 papers from 20 years published in 11 top-ranked Software Engineering(SE) conferences between 1993 to 2013. Seven topics are identified as the dominant themes in modern software engineering. We show that these topics are not static, rather, some of them are becoming decidedly less prominent over time (modeling) while others are become very prominent indeed (defect analysis). By clustering conferences according to the topics they publish, we identify four large groups of SE conferences, e.g. ASE, FSE and ICSE publish mostly the same work (exceptions: there are more program analysis results in FSE than in ASE or ICSE). Using these results, we offer numerous recommendations including how to plan an individual's research program, when to make or merge conferences, and how to encourage a broader range of topics at SE conferences. An extended version of this paper, that analyzes more conferences and papers, is available on https://goo.gl/mVdyfj.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.52"}, {"primary_key": "3802488", "vector": [], "sparse_vector": [], "title": "Data-directed contextual relevance in the IoT.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The relevance of data created in or about the IoT has a strong reliance on the context, especially spatiotemporal context, of the device and application perceiving it. To ensure that applications perceive data items that are relevant to the current context, it is necessary to restrict when each item is available. To control an application's perceptions of data availability, data items are often put in a group with other similar items, and a static rule is applied to determine when that data can be seen by applications. Such rules are fairly rigid, and the burden is on application developers to manage individual data items and their access policies, including making sure data distribution stays up to date relative to the context that influences data availability. We posit that the development of applications that need to access contextually relevant data can be greatly simplified by enabling a data item itself to control how and when it is available to applications. To realize this simplified programming paradigm, we introduce the datalet, an abstraction of a piece of data that understands its contextual relevance and dictates how (i.e., when and where) it is available based on that application's context. The datalet allows the application developer to focus on the application logic that relies on available data, without worrying about how to store, update, and distribute contextually-sensitive data to (distributed) application instances. To show how datalets are used by application developers to construct an application, we create an augmented-reality game that uses datalets to make elements of game play available based on the player's spatiotemporal context. The video of this demonstration is on YouTube at: https://youtu.be/snFhokswWpc.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.13"}, {"primary_key": "3802489", "vector": [], "sparse_vector": [], "title": "CSSDev: refactoring duplication in cascading style sheets.", "authors": ["Davood Mazinanian", "<PERSON><PERSON>"], "summary": "Cascading Style Sheets (CSS) is a widely-used language for defining the presentation of structured documents and user interfaces. Despite its popularity, CSS still lacks adequate tool support for everyday maintenance tasks, such as debugging and refactoring. In this paper, we present CSSDEV, a tool suite for analyzing CSS code to detect refactoring opportunities.(https://youtu.be/lu3oITi1XrQ).", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.7"}, {"primary_key": "3802495", "vector": [], "sparse_vector": [], "title": "Towards systematic spreadsheet construction processes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Spreadsheets are used in professional business contexts to make decisions based on collected data. Usually, these spreadsheets are developed by end users (e.g. accountants) in an ad-hoc way. The effect of this practice is that the business logic of a concrete spreadsheet is not explicit to them. Thus, its correctness is hard to assess and users have to trust. We present an approach where structure and computational behavior of a spreadsheet are specified by a model with a process-like notation based on combining pre-defined functional spreadsheet services with typed interfaces. This allows for a consistent construction of a spreadsheet by defining its structure and computational behavior as well as filling it with data and executing the defined computational behavior. Thus, concrete spreadsheets are equipped with a specification of their construction process. This supports their understanding and correct usage, even in case of legacy spreadsheets. The approach has been developed in cooperation with an industrial partner from the automotive industry.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.141"}, {"primary_key": "3802496", "vector": [], "sparse_vector": [], "title": "A machine learning approach for determining the validity of traceability links.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Traceability Link Recovery (TLR) is a fundamental software maintenance task in which links are established between related software artifacts of different types (e.g., source code, documentation, requirements specifications, etc.) within a system. Existing approaches to TLR often require a human to analyze a long list of potential links and distinguish valid links from invalid ones. Here we present an approach which bypasses this intermediate step and automatically classifies links as valid or invalid using a machine learning approach and features such as text retrieval (TR) rankings and query quality (QQ) metrics. We performed an evaluation on recovering traceability links in three software systems and the results show the potential of our approach, which achieved 95% accuracy on average using both types of features.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.86"}, {"primary_key": "3802500", "vector": [], "sparse_vector": [], "title": "What contributes to the success of IT projects?: success factors, challenges and lessons learned from an empirical study of software projects in the Norwegian public sector.", "authors": ["Parastoo <PERSON>", "<PERSON><PERSON>"], "summary": "Context. Each year the public sector invests large amounts of money in the development and modifications of their software systems. These investments are not always successful and many public sector software projects fail to deliver the expected benefits. Goal. This study aims at reducing the waste of resources on failed software projects through better understanding of the success factors and challenges. Method. Thirty-five completed software projects in 11 organizations in the public sector of Norway were analyzed. For each project, representatives from the project owners, project management and the user organization were interviewed. Results. Small and large software projects reported different challenges, especially related to project priority. Taking advantage of agile practices such as flexible scope and frequent delivery increased the success rate of the projects. Projects with time and material contracts and involved clients during execution were more successful than other projects. The respondents experienced that extensive involvement and good competence of the client, high priority of the project, good dialogue between client and provider and appliance of agile practices were main success factors. Main challenges were related to technical issues, project planning and management, transition of the product to the user organization, involvement and competence of the client, and benefit management. Conclusions. Success factors tend to focus on human factors, e.g., involvement, competence and collaboration. Challenges focus on human factors as well as issues of technical nature. Both aspects need to be addressed to enable successful and avoid failed software projects. Competence, client involvement and benefit management are among factors that the public sector should focus on for realizing client benefits.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.146"}, {"primary_key": "3802501", "vector": [], "sparse_vector": [], "title": "Charting the market disruptive nature of open source: experiences from Sony mobile.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Open Source Software (OSS) has substantial impact on how software-intensive firms develop products and deliver value to the customers. These companies need both strategic and operational support on how to adapt OSS as a part of their products and how to adjust processes and organizations to increase the benefits from OSS participation. This work presents the key insights from the journey that Sony Mobile has made from a company developing proprietary software to a respected member of OSS communities. We framed the experiences into an Open Source Maturity Model that includes two scenarios: engineering-driven and business-driven open source. We outline the most important decisions, roles, processes and implications.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.110"}, {"primary_key": "3802504", "vector": [], "sparse_vector": [], "title": "CrashScope: a practical tool for automated testing of Android applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Unique challenges arise when testing mobile applications due to their prevailing event-driven nature and complex contextual features (e.g. sensors, notifications). Current automated input generation approaches for Android apps are typically not practical for developers to use due to required instrumentation or platform dependence and generally do not effectively exercise contextual features. To better support developers in mobile testing tasks, in this demo we present a novel, automated tool called CrashScope. This tool explores a given Android app using systematic input generation, according to several strategies informed by static and dynamic analyses, with the intrinsic goal of triggering crashes. When a crash is detected, CrashScope generates an augmented crash report containing screenshots, detailed crash reproduction steps, the captured exception stack trace, and a fully replayable script that automatically reproduces the crash on a target device(s). Results of preliminary studies show that CrashScope is able to uncover about as many crashes as other state of the art tools, while providing detailed useful crash reports and test scripts to developers. Website: www.crashscope-android.com/crashscope-home Video url: https://youtu.be/ii6S1JF6xDw", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.16"}, {"primary_key": "3802505", "vector": [], "sparse_vector": [], "title": "Automated GUI testing of Android apps: from research to practice.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The last decade has seen tremendous proliferation of mobile computing in our society. Billions of users have access to millions of mobile apps that can be installed directly on their mobile devices, electrical appliances, and watches. Factors such as new monetization/revenue models, programming models, and distribution infrastructures contribute to an \"attractive\" movement that captivates new and traditional developers, as well as a crowd of other professionals that explore, design, and implement mobile apps. Also, the need for \"enterprise apps\" that support start-ups or serve as a new front-end for traditional companies is pushing software-related professionals to embrace mobile technologies. However, the nature of the economy (devices, apps, markets) imposes new challenges on how mobile apps are envisioned, designed, implemented, tested, released, and maintained. This technology briefing aims to help address the challenges of testing and maintaining mobile apps by providing participants from both academic and industrial backgrounds with information on the state-of-art and state-of-practice mobile testing techniques. Specifically, we aim to (i) highlight new techniques and methodologies for making effective automated testing of mobile apps practical and accessible to developers, and (ii) discuss open academic research questions related to such technology transfer.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.166"}, {"primary_key": "3802506", "vector": [], "sparse_vector": [], "title": "Automatic software summarization: the state of the art.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Automatic text summarization has been widely studied for more than fifty years. In software engineering, automatic summarization is an emerging area that shows great potential and poses new and exciting research challenges. This technical briefing provides an introduction to the state of the art and maps future research directions in automatic software summarization.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.169"}, {"primary_key": "3802510", "vector": [], "sparse_vector": [], "title": "A study on behavioral backward incompatibility bugs in Java software libraries: poster.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nowadays, due to the frequent technological innovation and market changes, software libraries are evolving very quickly. To make sure that existing client software applications are not broken after a library update, backward compatibility has always been one of the most important requirements during the evolution of software platforms and libraries. However, due to various reasons, backward compatibility is seldom fully achieved in practice, and many relevant software failures are reported. Therefore, it is important to understand the status, major reasons, and impact of backward incompatibilities in real world software. Previous studies on this topic mainly focus on API signature changes between consecutive versions of software libraries, but behavioral changes of APIs with untouched signatures are actually more dangerous and are causing most realworld bugs because they cannot be easily detected. This paper presents an empirical study on 126 real-world software bug reports on backward incompatibilities of software libraries. We find that 67% of fixed client bugs caused by backward incompatibilities in software libraries are fixed by client developers, through several simple change patterns made to the backward incompatible API invocations.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.101"}, {"primary_key": "3802512", "vector": [], "sparse_vector": [], "title": "Automating systematic mappings, adding quality to quantity: poster.", "authors": ["<PERSON>", "Genoveva Vargas-Solar", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>Oviedo", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces an approach for enhancing some of the steps of the Systematic Mapping Studies (SMS) with quality criteria1 [3], [1]. The original methodology for producing SMS defines the following workflow: (i) defining research questions; (ii) querying bibliography data sources using a key-word complex query2; (iii) selecting relevant documents from data sources; (iv) keywording of documents and defining a classification scheme; (v) classifying the documents and (vi) producing the mapping and answers to the research questions.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.111"}, {"primary_key": "3802513", "vector": [], "sparse_vector": [], "title": "Dependency-aware software release planning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The existing software release planning models aim to find a subset of software requirements with the highest value on the assumption that the value of a selected subset of requirements equals to the Accumulated Value (AV) of that subset. This assumption however, does not hold due to the Value-related Dependencies among software requirements. To address this, we have formulated an integer programming model for software release planning that finds a subset of software requirements with the highest Overall Value (OV) where overall value of a selected subset of requirements captures the impacts of value-related dependencies on the value of that subset. We have demonstrated through several simulations that maximizing the accumulated value of a selected subset of requirements may conflict with maximizing the overall value of that subset.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.74"}, {"primary_key": "3802522", "vector": [], "sparse_vector": [], "title": "Dynamic update of business process management.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Requirements and domain assumptions of Business Process Management (BPM) need to be studied. Most times, they change during BPM life unpredictably at design-time leading to a BPM update. Updating a BPM must take current state into consideration. Update process may vary depending on it. To the best of our knowledge, there is a lack of techniques for updating BPM at run-time and only few of them build BPM from its requirements, which we believe that is the most natural way for designing them. As updating processes at runtime is a critical duty, there is a need of guaranteeing correct dynamic updates. Hence, we are interested in correct-by-construction approaches rather than construct-then-verify approaches in order to automatically provide guarantees of producing only expected BPM for given requirements. Requirements must be specify in an understandable declarative language, so as to easily design BPM by writing requirements in a convenient way. Moreover, we plan to issue efficient tools supporting the developed techniques and languages, and then, evaluate them by 1) modelling known case studies from the software engineering and BPM literature, and 2) solving real BPM problems from companies or any other institution.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.25"}, {"primary_key": "3802523", "vector": [], "sparse_vector": [], "title": "A visualization of specification coverage based on document similarity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Code coverage is a metric used to represent how much code is tested when particular test cases are executed. As is code coverage, specification coverage is expected to help us to comprehend how much specification to be implemented is tested. In this study, we propose a visualization process for specification coverage. This process finds traceability links between specifications and test cases using a similarity metric and constructs two views for visualization. We develop a prototype tool for automatically executing the process and evaluate the process in a preliminary experiment on a web application development in industry. This extended abstract explains the overview of our study and the preliminary results.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.117"}, {"primary_key": "3802525", "vector": [], "sparse_vector": [], "title": "App store mining is not enough.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "App store reviews are currently the main source of information for analyzing different aspects of app development and evolution. However, app users' feedback do not only occur on the app store. In fact, a large quantity of posts about apps are made daily on social media. In this paper, we study how Twitter can provide complementary information to support mobile app development. By analysing a total of 70 apps over a period of six weeks, we show that 22.4% more feature requests and 12.89% more bug reports could be found on Twitter.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.77"}, {"primary_key": "3802528", "vector": [], "sparse_vector": [], "title": "A model-based approach towards the building of trustworthy software-intensive systems-of-systems.", "authors": ["Valdemar <PERSON>"], "summary": "Software-Intensive Systems-of-Systems (SoS) are an arrangement of interoperable systems called constituents joined together to accomplish a set of missions. They often support critical domains, such as emergency and crisis management and healthcare systems. In this sense, SoS must be trustworthy, that is, they must not fail, since they exhibit a substantial potential to cause damage and threats to human lives. In this direction, it is paramount to perform validation and verification (V&V) activities. These activities can assure that the final SoS performs its operation conforming to its specification and in a correct form, with no fails. However, SoS exhibit further complexity to the conduction of V&V, especially due to their dynamic properties (emergent behaviors and dynamic architectures), and due to the fact that constituents are not necessarily known at design-time. Thus, novel approaches are required to make SoS trustworthy. In this direction, this paper presents the research status of an ongoing PhD project that contributes to the software engineering and trustworthiness of this type of software.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.28"}, {"primary_key": "3802530", "vector": [], "sparse_vector": [], "title": "Reverse engineering object-oriented applications into high-level domain models with reoom.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Automatically pinpointing those classes in an object-oriented program that implement interesting domain concepts would be valuable for industrial software maintainers. We encode two observations of programmer behavior in Reoom, a novel light-weight static analysis. In a comparison with its most closely related competitor, <PERSON><PERSON>le, on third-party open source applications, Reoom scaled to larger applications and achieved better overall precision and recall.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.63"}, {"primary_key": "3802531", "vector": [], "sparse_vector": [], "title": "Automatic categorization with deep neural network for open-source Java projects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we introduce a Deep Neural Network model for automated software categorization. The model is ableto form high-level concepts from low-level code tokens and to distinguish important features such as API calls and identifiers in order to support software categorization. Our empirical evaluation shows that DNN outperformed other machine learning approaches with 15.9-36.4% higher accuracy in software categorization. We plan to expand further our studies to explore more features and variations of DNN, with different configurations and data sets.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.118"}, {"primary_key": "3802532", "vector": [], "sparse_vector": [], "title": "Exploring API embedding for API usages and applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Word2Vec is a class of neural network models that as being trainedfrom a large corpus of texts, they can produce for each unique word acorresponding vector in a continuous space in which linguisticcontexts of words can be observed. In this work, we study thecharacteristics of Word2Vec vectors, called API2VEC or API embeddings, for the API elements within the API sequences in source code. Ourempirical study shows that the close proximity of the API2VEC vectorsfor API elements reflects the similar usage contexts containing thesurrounding APIs of those API elements. Moreover, API2VEC can captureseveral similar semantic relations between API elements in API usagesvia vector offsets. We demonstrate the usefulness of API2VEC vectorsfor API elements in three applications. First, we build a tool thatmines the pairs of API elements that share the same usage relationsamong them. The other applications are in the code migrationdomain. We develop API2API, a tool to automatically learn the APImappings between Java and C# using a characteristic of the API2VECvectors for API elements in the two languages: semantic relationsamong API elements in their usages are observed in the two vectorspaces for the two languages as similar geometric arrangements amongtheir API2VEC vectors. Our empirical evaluation shows that API2APIrelatively improves 22.6% and 40.1% top-1 and top-5 accuracy over astate-of-the-art mining approach for API mappings. Finally, as anotherapplication in code migration, we are able to migrate equivalent APIusages from Java to C# with up to 90.6% recall and 87.2% precision.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.47"}, {"primary_key": "3802533", "vector": [], "sparse_vector": [], "title": "Combining Word2Vec with revised vector space model for better code retrieval.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "API example code search is an important applicationin software engineering. Traditional approaches to API codesearch are based on information retrieval. Recent advance inWord2Vec has been applied to support the retrieval of APIexamples. In this work, we perform a preliminary study thatcombining traditional IR with Word2Vec achieves better retrievalaccuracy. More experiments need to be done to study differenttypes of combination among two lines of approaches.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.90"}, {"primary_key": "3802534", "vector": [], "sparse_vector": [], "title": "Statistical translation of English texts to API code templates.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We develop T2API, a context-sensitive, graph-based statistical translation approach that takes as input an English description of a programming task and synthesizes the corresponding API code template for the task. We train T2API to statistically learn the alignments between English and APIs and determine the relevant API elements. The training is done on StackOverflow, which is a bilingual corpus on which developers discuss programming problems in two types of language: English and programming language. T2API considers both the context of the words in the input query and the context of API elements that often go together in the corpus. The derived API elements with their relevance scores are assembled into an API usage by GRASYN, our novel graph-based API synthesis algorithm that generates a graph representing an API usage from a large code corpus. Importantly, it is capable of generating new API usages from previously seen sub-usages.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.81"}, {"primary_key": "3802536", "vector": [], "sparse_vector": [], "title": "PETrA: a software-based tool for estimating the energy profile of Android applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Energy efficiency is a vital characteristic of any mobile application, and indeed is becoming an important factor for user satisfaction. For this reason, in recent years several approaches and tools for measuring the energy consumption of mobile devices have been proposed. Hardware-based solutions are highly precise, but at the same time they require costly hardware toolkits. Model-based techniques require a possibly difficult calibration of the parameters needed to correctly create a model on a specific hardware device. Finally, software-based solutions are easier to use, but they are possibly less precise than hardware-based solution. In this demo, we present PETrA, a novel software-based tool for measuring the energy consumption of Android apps. With respect to other tools, PETrA is compatible with all the smartphones with Android 5.0 or higher, not requiring any device specific energy profile. We also provide evidence that our tool is able to perform similarly to hardware-based solutions.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.18"}, {"primary_key": "3802537", "vector": [], "sparse_vector": [], "title": "Studying multi-threaded behavior with TSViz.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern high-performing systems make extensive use of multiple CPU cores. These multi-threaded systems are complex to design, build, and understand. Debugging performance of these multi-threaded systems is especially challenging. This requires the developer to understand the relative execution of dozens of threads and their inter-dependencies, including data-sharing and synchronization behaviors. We describe TSViz, a visualization tool to help developers study and understand the activity of complex multi-threaded systems. TSviz depicts the partial order of concurrent events in a time-space diagram, and simultaneously scales this diagram according to the physical clock timestamps that tag each event. A developer can then interact with the visualization in several ways, for example by searching for events of interest, studying the distribution of critical sections across threads and zooming the diagram in and out. We overview TSviz design and describe our experience with using it to study a high-performance multi-threaded key-value store based on MongoDB. A video demo of TSViz is online: https://youtu.be/LpuiOZ3PJCk.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.9"}, {"primary_key": "3802538", "vector": [], "sparse_vector": [], "title": "Visualizing swift projects as cities: poster.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON>"], "summary": "Human's natural ability to perform software maintenance is compromised as a project gets bigger, older, and more complex. Software visualization tools can be used to mitigate this problem, easing software understanding. However, no such tools are available for Swift, a new programming language that is experiencing widespread adoption by developers. In this paper we present SwiftCity, a software visualization tool that uses the City Metaphor. Visualizing Swift projects as cities is different from projects in other languages, such as Java and Javascript. Swift employs a number of different units of modularity that are not available in these languages, such as extensions and structs.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.115"}, {"primary_key": "3802541", "vector": [], "sparse_vector": [], "title": "Predictive analysis of cloud systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Predictive analysis methods offer the possibility ofestimating the impact of design decisions, which may help inthe accomplishment of operational optimal results, before thedeployment of the system, and therefore minimizing the requiredeffort and cost. However, current predictive methods cannot beused on cloud environments, because of their complexity anddynamic nature. The main goal of this thesis is to investigatemethods for predictive analysis of cloud systems. Given modelsof cloud systems and their environments, we will specify differentadaptation mechanisms, and techniques for the estimation ofdifferent QoS metrics that help in the analysis of cloud systems. Specifically, we will use model transformation techniques tospecify the behavior of systems and their dynamic adaptation, as well as the performance metrics and tools for their analysis. Our tools will be based on simulation, and we will explore theuse of statistical model checking tools, and in particular thosedeveloped for graph-transformation systems.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.39"}, {"primary_key": "3802544", "vector": [], "sparse_vector": [], "title": "Privacy engineering in dynamic settings.", "authors": ["Inah Omoronyia"], "summary": "Modern distributed software platforms are linking smart objects such as smartphones, cars and health devices to the internet. A frequent challenge in the design of such platforms is determining the appropriate information disclosure protocol to use when one object interacts with another. For example, how can a software architect verify that when the platform constrains the sender to obtain consent from the subject before disclosure or notifying the subject after disclosure, then the privacy needs of the subject are addressed? To this end, this research presents an analysis framework for privacy engineering. We demonstrate how the framework's outputs can help software architects achieve privacy-by-design of software platforms for smart objects.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.89"}, {"primary_key": "3802548", "vector": [], "sparse_vector": [], "title": "Travioli: a dynamic analysis for detecting data-structure traversals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traversal is one of the most fundamental operations on data structures, in which an algorithm systematically visits some or all of the data items of a data structure. We propose a dynamic analysis technique, called Travioli, for detecting data-structure traversals. We introduce the concept of acyclic execution contexts, which enables precise detection of traversals of arrays and linked data structures such as lists and trees in the presence of both loops and recursion. We describe how the information reported by <PERSON><PERSON><PERSON><PERSON> can be used for visualizing data-structure traversals, manually generating performance regression tests, and for discovering performance bugs caused by redundant traversals. We evaluate <PERSON><PERSON><PERSON><PERSON> on five real-world JavaScript programs. In our experiments, <PERSON><PERSON><PERSON><PERSON> produced fewer than 4% false positives. We were able to construct performance tests for 93.75% of the reported true traversals. <PERSON><PERSON><PERSON><PERSON> also found two asymptotic performance bugs in widely used JavaScript frameworks D3 and express.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.50"}, {"primary_key": "3802550", "vector": [], "sparse_vector": [], "title": "Recommending and localizing change requests for mobile apps based on user reviews.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Researchers have proposed several approaches to extract information from user reviews useful for maintaining and evolving mobile apps. However, most of them just perform automatic classification of user reviews according to specific keywords (e.g., bugs, features). Moreover, they do not provide any support for linking user feedback to the source code components to be changed, thus requiring a manual, time-consuming, and error-prone task. In this paper, we introduce CHANGEADVISOR, a novel approach that analyzes the structure, semantics, and sentiments of sentences contained in user reviews to extract useful (user) feedback from maintenance perspectives and recommend to developers changes to software artifacts. It relies on natural language processing and clustering algorithms to group user reviews around similar user needs and suggestions for change. Then, it involves textual based heuristics to determine the code artifacts that need to be maintained according to the recommended software changes. The quantitative and qualitative studies carried out on 44,683 user reviews of 10 open source mobile apps and their original developers showed a high accuracy of CHANGEADVISOR in (i) clustering similar user change requests and (ii) identifying the code components impacted by the suggested changes. Moreover, the obtained results show that ChangeAdvisor is more accurate than a baseline approach for linking user feedback clusters to the source code in terms of both precision (+47%) and recall (+38%).", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.18"}, {"primary_key": "3802553", "vector": [], "sparse_vector": [], "title": "Analysis of JavaScript web applications using SAFE 2.0.", "authors": ["Jihyeok Park", "<PERSON><PERSON><PERSON>", "Joonyoung Park", "Sukyoung Ryu"], "summary": "JavaScript has been the language for web applications, and the growing prevalence of web environments in various devices makes JavaScript web applications even more ubiquitous. However, because JavaScript and web environments are extremely dynamic, JavaScript web applications are often vulnerable to type-related errors and security attacks. To lessen the problem, researchers have developed various analysis techniques in different analyzers, but such analyzers are not especially aimed for ease of use by analysis developers. In this paper, we present SAFE 2.0, a scalable analysis framework for ECMAScript especially designed as a playground for advanced research in JavaScript web applications. SAFE 2.0 is light-weight, which supports pluggability, extensibility, and debuggability. Demo video: https://youtu.be/ZI_emiRMoxQ.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.4"}, {"primary_key": "3802556", "vector": [], "sparse_vector": [], "title": "Evaluating and improving fault localization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Most fault localization techniques take as input a faulty program, and produce as output a ranked list of suspicious code locations at which the program may be defective. When researchers propose a new fault localization technique, they typically evaluate it on programs with known faults. The technique is scored based on where in its output list the defective code appears. This enables the comparison of multiple fault localization techniques to determine which one is better. Previous research has evaluated fault localization techniques using artificial faults, generated either by mutation tools or manually. In other words, previous research has determined which fault localization techniques are best at finding artificial faults. However, it is not known which fault localization techniques are best at finding real faults. It is not obvious that the answer is the same, given previous work showing that artificial faults have both similarities to and differences from real faults. We performed a replication study to evaluate 10 claims in the literature that compared fault localization techniques (from the spectrum-based and mutation-based families). We used 2995 artificial faults in 6 real-world programs. Our results support 7 of the previous claims as statistically significant, but only 3 as having non-negligible effect sizes. Then, we evaluated the same 10 claims, using 310 real faults from the 6 programs. Every previous result was refuted or was statistically and practically insignificant. Our experiments show that artificial faults are not useful for predicting which fault localization techniques perform best on real faults. In light of these results, we identified a design space that includes many previously-studied fault localization techniques as well as hundreds of new techniques. We experimentally determined which factors in the design space are most important, using an overall set of 395 real faults. Then, we extended this design space with new techniques. Several of our novel techniques outperform all existing techniques, notably in terms of ranking defective code in the top-5 or top-10 reports.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.62"}, {"primary_key": "3802557", "vector": [], "sparse_vector": [], "title": "Combining quantitative and qualitative studies in empirical software engineering research.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This technical briefing provides an overview of how quantitative empirical research methods can be combined with qualitative ones generating the family of empirical software engineering approaches known as mixed-methods. The ultimate aim of such mixed-methods is supporting cause-effect claims combining multiple data types, sources and analyses that provide software practitioners and academicians solid rationale and practical value to research results. This briefing offers lessons we learned in instrumenting and executing mixed-methods approaches for the benefit of the goal above.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.163"}, {"primary_key": "3802558", "vector": [], "sparse_vector": [], "title": "Locating energy hotspots in source code.", "authors": ["<PERSON><PERSON>"], "summary": "This paper briefly presents a new approach for helping developers identify energy hot spots in their applications. Using tests cases, and statistical methods based on Spectrum-based Fault Localization, high energy consumption is related to the system's source code and a ranking of possible energy leaks are pointed. This technique is both language independent, and context level independent. Initial studies have also shown that using this technique helped developers identify and optimize energy problems in half the time while improving the energy efficiency by 18%.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.151"}, {"primary_key": "3802559", "vector": [], "sparse_vector": [], "title": "Runtime collaborative-based configuration of software product lines.", "authors": ["<PERSON>"], "summary": "Software Product Line (SPL) configuration practices have been employed by industries as a mass customization process. However, the inherent variability of large SPLs leads to configuration spaces of exponential sizes. Thus, scalability and performance concerns start to be an issue when facing runtime environments, since it is usually infeasible to explore the entire configuration space exhaustively. In this context, the aim of my research is therefore to propose an efficient collaborative-based runtime approach that relies on recommender techniques to provide accurate and scalable configurations to users. To demonstrate the efficiency of the proposed approach, I conduct series of experiments on real-world SPLs. In addition, I plan empirically verify through a user case study the usability of the proposed approach. My expected contribution is to support the adoption of SPL configuration practices in industrial scenarios.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.154"}, {"primary_key": "3802560", "vector": [], "sparse_vector": [], "title": "A collaborative-based recommender system for configuration of extended product lines.", "authors": ["<PERSON>"], "summary": "Product Line (PL) configuration practices have been employed by industries as a mass customization process. However, due to the NP-hard nature of the process, performance concerns start to be an issue when facing large-scale configuration spaces. The aim of my doctoral research is therefore to propose an efficient collaborative-based recommender system that provides accurate and scalable solutions to users. To demonstrate the efficiency of the proposed recommender system, I will conduct series of experiments on real-world extended PLs. In addition, I plan empirically to verify through a user case study the usability of the proposed approach. My expected contribution is to support the adoption of PL configuration practices in industrial scenarios.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.36"}, {"primary_key": "3802561", "vector": [], "sparse_vector": [], "title": "Helping programmers improve the energy efficiency of source code.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>go <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper briefly proposes a technique to detect energy inefficient fragments in the source code of a software system. Test cases are executed to obtain energy consumption measurements, and a statistical method, based on spectrum-basedfault localization, is introduced to relate energy consumption to the system's source code. The result of our technique is an energy ranking of source code fragments pointing developers to possible energy leaks in their code.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.80"}, {"primary_key": "3802563", "vector": [], "sparse_vector": [], "title": "A test-suite diagnosability metric for spectrum-based fault localization approaches.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Current metrics for assessing the adequacy of a test-suite plainly focus on the number of components (be it lines, branches, paths) covered by the suite, but do not explicitly check how the tests actually exercise these components and whether they provide enough information so that spectrum-based fault localization techniques can perform accurate fault isolation. We propose a metric, called DDU, aimed at complementing adequacy measurements by quantifying a test-suite's diagnosability, i.e., the effectiveness of applying spectrum-based fault localization to pinpoint faults in the code in the event of test failures. Our aim is to increase the value generated by creating thorough test-suites, so they are not only regarded as error detection mechanisms but also as effective diagnostic aids that help widely-used fault-localization techniques to accurately pinpoint the location of bugs in the system. Our experiments show that optimizing a test suite with respect to DDU yields a 34% gain in spectrum-based fault localization report accuracy when compared to the standard branch-coverage metric.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.66"}, {"primary_key": "3802564", "vector": [], "sparse_vector": [], "title": "Predicate callback summaries.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "One of the challenges to analyze, test and debug Android apps is that the potential execution orders of callbacks are missing from the apps' source code, however, bugs, vulnerabilities and refactoring transformations have been found to be related to callback sequences. Existing work on control flow analysis of Android apps focuses on analyzing GUI behaviors. Our observation is that orthogonal to GUI, the Android API methods also play an important role in determining the order of callbacks, and previously, the APIs were modeled manually in an ad-hoc way. This paper presents a complementary solution of constructing program paths for Android apps. We proposed a specification called Predicate Callback Summary (PCS) that represents the callback control flow information (including callback sequences as well as the conditions the callbacks are invoked) in Android API methods. Our experiments show we can compute PCSs with reasonable accuracy and scalability and use them to build inter-callback control flow graphs for apps. Our detailed experimental data are available at: http://www.cs.iastate.edu/~weile/toolsdata/SummarizeAndroidFramework/lithium.html.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.95"}, {"primary_key": "3802568", "vector": [], "sparse_vector": [], "title": "PaaS - black or white: an investigation into software development model for building retail industry SaaS.", "authors": ["Vu Viet Hoang Pham", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Weidong Xia", "<PERSON>", "<PERSON>"], "summary": "One of the most important goals for Software Engineering is that end users or those people who understand software requirements but without too much programming experience can build their software products or prototypes easily. The recent success of cloud computing has made a big step towards this goal where Platform as a Service (PaaS) can provide general and comprehensive software development services within an integrated online environment for building Software as a Service (SaaS). However, currently, most PaaS are in a \"white-box\" which still requires significant learning efforts for software developers and lets alone inexperienced project managers or end users. Therefore, it is high time that we should comprehensively investigate the challenges for PaaS and provide a suitable development model. In this paper, we firstly identify and analyze the challenges for current White-PaaS through literature review. Afterwards, employing the retail industry as a typical application domain, a novel \"Black-Box\" PaaS framework is proposed which requires much less learning time and supports much more flexible and speedy SaaS design and development.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.57"}, {"primary_key": "3802569", "vector": [], "sparse_vector": [], "title": "Assertion generation through active learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Program assertions are useful for many program analysis tasks. They are however often missing in practice. In this work, we develop a novel approach for generating likely assertions automatically based on active learning. Our target is complex Java programs which cannot be symbolically executed (yet). Our key idea is to generate candidate assertions based on test cases and then apply active learning techniques to iteratively improve them. The experiments show that active learning really helps to improve the generated assertions.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.87"}, {"primary_key": "3802570", "vector": [], "sparse_vector": [], "title": "Statistical migration of API usages.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To support code migration, we introduce JV2CS, a tool to generate asequence of C# API elements and related control units that are neededto migrate a given Java code fragment. First, we mine the mappingsbetween single APIs in Java and C#. To overcome the lexical mismatchbetween the names of Java and C# APIs, we represent an API by itsusages instead of its name. To characterize an API with its contextconsisting of surrounding APIs in its usages, we take advantage ofWord2Vec model to project the APIs of Java JDK and C# .NET intothe corresponding continuous vector spaces. The transformation matrixbetween the two vector spaces is learned from a small set of human-written pairs of mappings. We use the transformation matrix toderive other mappings, and then use the mappings to generate thecorresponding API sequence in C# via a phrase-based translation model. The video demo for JV2CS can be found athttps://www.youtube.com/watch?v=MjTfmr9AmR8&feature=youtu.be.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.17"}, {"primary_key": "3802575", "vector": [], "sparse_vector": [], "title": "Supporting software developers with a holistic recommender system.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The promise of recommender systems is to provide intelligent support to developers during their programming tasks. Such support ranges from suggesting program entities to taking into account pertinent Q&A pages. However, current recommender systems limit the context analysis to change history and developers' activities in the IDE, without considering what a developer has already consulted or perused, e.g., by performing searches from the Web browser. Given the faceted nature of many programming tasks, and the incompleteness of the information provided by a single artifact, several heterogeneous resources are required to obtain the broader picture needed by a developer to accomplish a task. We present Libra, a holistic recommender system. It supports the process of searching and navigating the information needed by constructing a holistic meta-information model of the resources perused by a developer, analyzing their semantic relationships, and augmenting the web browser with a dedicated interactive navigation chart. The quantitative and qualitative evaluation of Libra provides evidence that a holistic analysis of a developer's information context can indeed offer comprehensive and contextualized support to information navigation and retrieval during software development.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.17"}, {"primary_key": "3802576", "vector": [], "sparse_vector": [], "title": "Blockchain-oriented software engineering: challenges and new directions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we acknowledge the need for software engineers to devise specialized tools and techniques for blockchain-oriented software development. Ensuring effective testing activities, enhancing collaboration in large teams, and facilitating the development of smart contracts all appear as key factors in the future of blockchain-oriented software development.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.142"}, {"primary_key": "3802578", "vector": [], "sparse_vector": [], "title": "A synergistic approach for distributed symbolic execution using test ranges.", "authors": ["<PERSON><PERSON>", "Sarfraz Khurshid", "Corina S<PERSON>", "<PERSON><PERSON>"], "summary": "Symbolic execution is a systematic program analysis technique that has received a lot of attention in the research community. However, scaling symbolic execution continues to pose a major challenge. This paper introduces Synergise, a novel two-fold integration approach. One, it integrates distributed analysis and constraint re-use to enhance symbolic execution using feasible ranges, which allow sharing of constraint solving results among different workers without communicating or sharing potentially large constraint databases (as required traditionally). Two, it integrates complementary techniques for test input generation, e.g., search-based generation and symbolic execution, for creating higher quality tests using unexplored ranges, which allows symbolic execution to re-use tests created by another technique for effective distribution of exploration of previously unexplored paths.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.116"}, {"primary_key": "3802581", "vector": [], "sparse_vector": [], "title": "Making malory behave maliciously: targeted fuzzing of android execution environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Android applications, or apps, provide useful features to end-users, but many apps also contain malicious behavior. Modern malware makes understanding such behavior challenging by behaving maliciously only under particular conditions. For example, a malware app may check whether it runs on a real device and not an emulator, in a particular country, and alongside a specific target app, such as a vulnerable banking app. To observe the malicious behavior, a security analyst must find out and emulate all these app-specific constraints. This paper presents FuzzDroid, a framework for automatically generating an Android execution environment where an app exposes its malicious behavior. The key idea is to combine an extensible set of static and dynamic analyses through a search-based algorithm that steers the app toward a configurable target location. On recent malware, the approach reaches the target location in 75% of the apps. In total, we reach 240 code locations within an average time of only one minute. To reach these code locations, FuzzDroid generates 106 different environments, too many for a human analyst to create manually.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.35"}, {"primary_key": "3802582", "vector": [], "sparse_vector": [], "title": "Topic-driven testing.", "authors": ["<PERSON>"], "summary": "When manually testing Web sites humans can go with vague, yet general instructions, such as \"add the product to shopping cart and proceed to checkout\". Can we teach a robot to follow such instructions as well?In this paper I present a novel model, called semantic usage patterns which allows us to capture the general topics behind the individual steps of interactions. These models can be extracted from existing test descriptions be they in natural language or in form of system tests. Those usage patterns can be applied even on applications they were not designed for. They allow to test applications automatically in order to identify behavioral anomalies in the application model or detect missing functionalities.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.175"}, {"primary_key": "3802583", "vector": [], "sparse_vector": [], "title": "Helping software engineering students analyzing their performance data: tool support in an educational environment.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Process PAIR is a novel tool for automating the performance analysis of software developers. Based on a performance model calibrated from the performance data of many developers, it automatically identifies and ranks potential performance problems and root causes of individual developers. We present the results of a controlled experiment involving 61 software engineering master students, half of whom used ProcessPAIR in a performance analysis assignment. The results show significant benefits in terms of students' satisfaction (average score of 4.78 out of 5 for ProcessPAIR users, against 3.81 for other users), quality of the analysis outcomes (average grades achieved of 88.1 out of 100 for ProcessPAIR users, against 82.5 for other users), and time required to do the analysis (average of 252 min for ProcessPAIR users, against 262 min for other users, but with much room for improvement).", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.61"}, {"primary_key": "3802584", "vector": [], "sparse_vector": [], "title": "A framework for a programmer&apos;s minion.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Programming environments should help theprogrammer. Today's environments do a lot along these lines, yet more is possible. We are developing facilities within theCode Bubbles environment to proactively assist theprogrammer. These facilities attempt to take care of mundanetasks that the programmer otherwise would need to do, effectively acting as a programmer's minion. This paperdescribes the framework provided by the environment tosupport these tasks and an evaluation of the effectiveness of theinitial minion implementation.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.54"}, {"primary_key": "3802590", "vector": [], "sparse_vector": [], "title": "Detecting user story information in developer-client conversations to generate extractive summaries.", "authors": ["<PERSON>", "Siyuan Jiang", "Ameer Armaly", "<PERSON>"], "summary": "User stories are descriptions of functionality that a software user needs. They play an important role in determining which software requirements and bug fixes should be handled and in what order. Developers elicit user stories through meetings with customers. But user story elicitation is complex, and involves many passes to accommodate shifting and unclear customer needs. The result is that developers must take detailed notes during meetings or risk missing important information. Ideally, developers would be freed of the need to take notes themselves, and instead speak naturally with their customers. This paper is a step towards that ideal. We present a technique for automatically extracting information relevant to user stories from recorded conversations between customers and developers. We perform a qualitative study to demonstrate that user story information exists in these conversations in a sufficient quantity to extract automatically. From this, we found that roughly 10.2% of these conversations contained user story information. Then, we test our technique in a quantitative study to determine the degree to which our technique can extract user story information. In our experiment, our process obtained about 70.8% precision and 18.3% recall on the information.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.13"}, {"primary_key": "3802593", "vector": [], "sparse_vector": [], "title": "Reducing energy consumption of resource-intensive scientific mobile applications via code refactoring.", "authors": ["<PERSON>"], "summary": "The advent of new computing paradigms such as Mobile Grids and Mobile-edge Clouds, and the increasing number of mobile devices with ever-growing capabilities makes them attractive to users running scientific and HPC applications. However, mobile devices still have limited capabilities when compared to non-mobile devices. More importantly, mobile devices rely on batteries for their power supply. To overcome this problem, this PhD research studies how to reduce energy consumption in mobile devices via code refactoring for such kind of applications.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.33"}, {"primary_key": "3802597", "vector": [], "sparse_vector": [], "title": "Code defenders: crowdsourcing effective tests and subtle mutants with a mutation testing game.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Writing good software tests is difficult and not every developer's favorite occupation. Mutation testing aims to help by seeding artificial faults (mutants) that good tests should identify, and test generation tools help by providing automatically generated tests. However, mutation tools tend to produce huge numbers of mutants, many of which are trivial, redundant, or semantically equivalent to the original program, automated test generation tools tend to produce tests that achieve good code coverage, but are otherwise weak and have no clear purpose. In this paper, we present an approach based on gamification and crowdsourcing to produce better software tests and mutants: The Code Defenders web-based game lets teams of players compete over a program, where attackers try to create subtle mutants, which the defenders try to counter by writing strong tests. Experiments in controlled and crowdsourced scenarios reveal that writing tests as part of the game is more enjoyable, and that playing Code Defenders results in stronger test suites and mutants than those produced by automated tools.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.68"}, {"primary_key": "3802598", "vector": [], "sparse_vector": [], "title": "Learning syntactic program transformations from examples.", "authors": ["Reudismam Rolim", "<PERSON>", "Loris D&apos;Antoni", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Automatic program transformation tools can be valuable for programmers to help them with refactoring tasks, and for Computer Science students in the form of tutoring systems that suggest repairs to programming assignments. However, manually creating catalogs of transformations is complex and time-consuming. In this paper, we present REFAZER, a technique for automatically learning program transformations. REFAZER builds on the observation that code edits performed by developers can be used as input-output examples for learning program transformations. Example edits may share the same structure but involve different variables and subexpressions, which must be generalized in a transformation at the right level of abstraction. To learn transformations, REFAZER leverages state-of-the-art programming-by-example methodology using the following key components: (a) a novel domain-specific language (DSL) for describing program transformations, (b) domain-specific deductive algorithms for efficiently synthesizing transformations in the DSL, and (c) functions for ranking the synthesized transformations. We instantiate and evaluate REFAZER in two domains. First, given examples of code edits used by students to fix incorrect programming assignment submissions, we learn program transformations that can fix other students' submissions with similar faults. In our evaluation conducted on 4 programming tasks performed by 720 students, our technique helped to fix incorrect submissions for 87% of the students. In the second domain, we use repetitive code edits applied by developers to the same project to synthesize a program transformation that applies these edits to other locations in the code. In our evaluation conducted on 56 scenarios of repetitive edits taken from three large C# open-source projects, REFAZER learns the intended program transformation in 84% of the cases using only 2.9 examples on average.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.44"}, {"primary_key": "3802600", "vector": [], "sparse_vector": [], "title": "A hierarchical architecture for distributed security control of large scale systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the era of Big Data, software systems can be affected by its growing complexity, both with respect to functional and non-functional requirements. As more and more people use software applications over the web, the ability to recognize if some of this traffic is malicious or legitimate is a challenge. The traffic load of security controllers, as well as the complexity of security rules to detect attacks can grow to levels where current solutions may not suffice. In this work, we propose a hierarchical distributed architecture for security control in order to partition responsibility and workload among many security controllers. In addition, our architecture proposes a more simplified way of defining security rules to allow security to be enforced on an operational level, rather than a development level.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.64"}, {"primary_key": "3802602", "vector": [], "sparse_vector": [], "title": "Envisioning the future of collaborative model-driven software engineering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The adoption of Model-driven Software Engineering (MDSE) to develop complex software systems in application domains like automotive and aerospace is being supported by the maturation of model-driven platforms and tools. However, empirical studies show that a wider adoption of MDSE technologies is still an issue. One limiting factor is related to the limited support for collaborative MDSE. This paper reflects on research directions, challenges, and opportunities of collaborative MDSE.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.143"}, {"primary_key": "3802609", "vector": [], "sparse_vector": [], "title": "On cross-stack configuration errors.", "authors": ["<PERSON>", "Noured<PERSON>", "<PERSON>"], "summary": "Today's web applications are deployed on powerful software stacks such as MEAN (JavaScript) or LAMP (PHP), which consist of multiple layers such as an operating system, web server, database, execution engine and application framework, each of which provide resources to the layer just above it. These powerful software stacks unfortunately are plagued by so-called cross-stack configuration errors (CsCEs), where a higher layer in the stack suddenly starts to behave incorrectly or even crash due to incorrect configuration choices in lower layers. Due to differences in programming languages and lack of explicit links between configuration options of different layers, sysadmins and developers have a hard time identifying the cause of a CsCE, which is why this paper (1) performs a qualitative analysis of 1,082 configuration errors to understand the impact, effort and complexity of dealing with CsCEs, then (2) proposes a modular approach that plugs existing source code analysis (slicing) techniques, in order to recommend the culprit configuration option. Empirical evaluation of this approach on 36 real CsCEs of the top 3 LAMP stack layers shows that our approach reports the misconfigured option with an average rank of 2.18 for 32 of the CsCEs, and takes only few minutes, making it practically useful.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.31"}, {"primary_key": "3802610", "vector": [], "sparse_vector": [], "title": "On software odysseys and how to prevent them.", "authors": ["<PERSON>"], "summary": "Acting on a software system developed by someone else may be difficult. Performing any kind of maintenance task requires knowledge about many parts of the system. Therefore, program comprehension plays a lead role in software maintenance, above all when new resources are added to a project. At the same time, acquiring full knowledge about big codebases can be utopian, because it requires a big effort if no sufficient documentation is provided. In this paper I present TIRESIAS, an approach able to suggest a subset of important software artifacts which are good entry points for newcomers. The suggested artifacts can be used in order to acquire knowledge about the system in an initial stage. TIRESIAS uses a knowledge graph to model the references among source code artifacts and to find (i) the artifacts that lead to acquire the widest knowledge about the system and (ii) the most important artifacts that are worth keeping in mind. The approach is validated through a case study conducted on a software system and three professional software developers.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.157"}, {"primary_key": "3802613", "vector": [], "sparse_vector": [], "title": "Comparison of model size predictors in practice.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The amount of software in modern vehicles is constantly growing. However, the risk for functional and quality deficiencies increases simultaneously with size. This results in industry for example in inevitable and unexpected refactorings of software models, which is slowing down development processes in turn. In this industrial case study, we evaluate model growth predictors applied to foresee critical model size developments. We present five approaches and systematically compare them regarding prediction accuracy.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.66"}, {"primary_key": "3802614", "vector": [], "sparse_vector": [], "title": "User-centric Android flexible permissions.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON>", "Amleto Di Salle", "<PERSON><PERSON>"], "summary": "Privacy in mobile apps is a fundamental aspect to be considered, particularly with regard to meeting end user expectations. Due to the rigidities of the Android permission model, desirable trade-offs are not allowed. End users are confined into a secondary role, having the only option of choosing between either privacy or functionalities. This work proposes a user-centric approach to the flexible management of Android permissions that empowers end users to specify the desired level of permissions on a per-feature basis.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.84"}, {"primary_key": "3802615", "vector": [], "sparse_vector": [], "title": "Software development waste.", "authors": ["<PERSON>", "<PERSON>", "Cécile Péraire"], "summary": "Context: Since software development is a complex socio-technical activity that involves coordinating different disciplines and skill sets, it provides ample opportunities for waste to emerge. Waste is any activity that produces no value for the customer or user. Objective: The purpose of this paper is to identify and describe different types of waste in software development. Method: Following Constructivist Grounded Theory, we conducted a two-year five-month participant-observation study of eight software development projects at Pivotal, a software development consultancy. We also interviewed 33 software engineers, interaction designers, and product managers, and analyzed one year of retrospection topics. We iterated between analysis and theoretical sampling until achieving theoretical saturation. Results: This paper introduces the first empirical waste taxonomy. It identifies nine wastes and explores their causes, underlying tensions, and overall relationship to the waste taxonomy found in Lean Software Development. Limitations: Grounded Theory does not support statistical generalization. While the proposed taxonomy appears widely applicable, organizations with different software development cultures may experience different waste types. Conclusion: Software development projects manifest nine types of waste: building the wrong feature or product, mismanaging the backlog, rework, unnecessarily complex solutions, extraneous cognitive load, psychological distress, waiting/multitasking, knowledge loss, and ineffective communication.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.20"}, {"primary_key": "3802621", "vector": [], "sparse_vector": [], "title": "Fuzzy fine-grained code-history analysis.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Existing software-history techniques represent source-code evolution as an absolute and unambiguous mapping of lines of code in prior revisions to lines of code in subsequent revisions. However, the true evolutionary lineage of a line of code is often complex, subjective, and ambiguous. As such, existing techniques are predisposed to, both, overestimate and underestimate true evolution lineage. In this paper, we seek to address these issues by providing a more expressive model of code evolution, the fuzzy history graph, by representing code lineage as a continuous (i.e., fuzzy) metric rather than a discrete (i.e., absolute) one. Using this more descriptive model, we additionally provide a novel multi-revision code-history analysis - fuzzy history slicing. In our experiments over three real-world software systems, we found that the fuzzy history graph provides a tunable balance of precision and recall, and an overall improved accuracy over existing code-evolution models. Furthermore, we found that the use of such a fuzzy model of history provided improved accuracy for code-history analysis tasks.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.74"}, {"primary_key": "3802622", "vector": [], "sparse_vector": [], "title": "Disposable testing: avoiding maintenance of generated unit tests by throwing them away.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Developers write unit tests together with program code, and then maintain these tests as the program evolves. Since writing good tests can be difficult and tedious, unit tests can so be generated automatically. However, maintaining these tests(e.g., when APIs change, or, when tests represent outdated and changed behavior), is still a manual task. Because automatically generated tests may have no clear purpose other than covering code, maintaining them may be more difficult than maintaining manually written tests. Could this maintenance be avoided by simply generating new tests after each change, and disposing the old ones? We propose disposable testing: Tests are generated to reveal any behavioral differences caused by a code change, and are thrown away once the developer confirms whether these changes were intended or not. However, this idea raises several research challenges: First, are standard automated test generation techniques good enough to produce tests that may berelied upon to reveal changes as effectively as an incrementally built regression test suite? Second, does disposable testing reduce the overall effort, or would developers need to inspect more generated tests compared to just maintaining existing ones?", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.100"}, {"primary_key": "3802625", "vector": [], "sparse_vector": [], "title": "A formally verified sequentializer for lustre-like concurrent synchronous data-flow programs.", "authors": ["Gang Shi", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Yuan Dong", "<PERSON><PERSON><PERSON>"], "summary": "Synchronous data-flow languages (SDFL), such as Lustre [1], is a concurrent language that has been widely used in safety-critical systems. Verified compilers for such languages are crucial in generating trustworthy object code. A good approach is to first translate a concurrent SDFL program to a sequential intermediate representation, such as a Clight [2] code, and then use an existing verified compiler such as CompCert [3] to produce executable object code for the target machine. A verified Sequentializer is crucial in such a verified compiler. It produces a sequential topological order among the program statements that preserve the program dependencies and the dynamic semantics of the original program. In this paper, we show such an approach for a SDFL language such as Lustre. The approach is general enough to be applicable to other SDFLs as well. It first gives a formal specification of the operational semantics, and proves its determinism property for a Lustre-like program. It then formally proves the equivalence of the original concurrent semantics and its target sequential semantics using the well-established proof assistant <PERSON><PERSON> ([4], [5]), and extracts the certified code for such a sequentializer by <PERSON><PERSON>.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.83"}, {"primary_key": "3802626", "vector": [], "sparse_vector": [], "title": "Optimizing test placement for module-level regression testing.", "authors": ["August Shi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "Modern build systems help increase developer productivity by performing incremental building and testing. These build systems view a software project as a group of interdependent modules and perform regression test selection at the module level. However, many large software projects have imprecise dependency graphs that lead to wasteful test executions. If a test belongs to a module that has more dependencies than the actual dependencies of the test, then it is executed unnecessarily whenever a code change impacts those additional dependencies. In this paper, we formulate the problem of wasteful test executions due to suboptimal placement of tests in modules. We propose a greedy algorithm to reduce the number of test executions by suggesting test movements while considering historical build information and actual dependencies of tests. We have implemented our technique, called TestOptimizer, on top of CloudBuild, the build system developed within Microsoft over the last few years. We have evaluated the technique on five large proprietary projects. Our results show that the suggested test movements can lead to a reduction of 21.66 million test executions (17.09%) across all our subject projects. We received encouraging feedback from the developers of these projects; they accepted and intend to implement ≈80% of our reported suggestions.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.69"}, {"primary_key": "3802638", "vector": [], "sparse_vector": [], "title": "A guided genetic algorithm for automated crash reproduction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To reduce the effort developers have to make for crash debugging, researchers have proposed several solutions for automatic failure reproduction. Recent advances proposed the use of symbolic execution, mutation analysis, and directed model checking as underling techniques for post-failure analysis of crash stack traces. However, existing approaches still cannot reproduce many real-world crashes due to such limitations as environment dependencies, path explosion, and time complexity. To address these challenges, we present EvoCrash, a post-failure approach which uses a novel Guided Genetic Algorithm (GGA) to cope with the large search space characterizing real-world software programs. Our empirical study on three open-source systems shows that EvoCrash can replicate 41 (82%) of real-world crashes, 34 (89%) of which are useful reproductions for debugging purposes, outperforming the state-of-the-art in crash replication.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.27"}, {"primary_key": "3802639", "vector": [], "sparse_vector": [], "title": "How to support customisation on SaaS: a grounded theory from customisation consultants.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper reports the initial result of a qualitative research on how to support customisation of SaaS (Software as a Service). The research follows the grounded theory method, and investigates the expectation of consultants who are specialized in customising enterprise software systems. The resulting theory contributes to the understanding of how customisation on SaaS differs from the traditional one, and provides a high-level guidance for SaaS vendors to prepare effective support for customisation.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.136"}, {"primary_key": "3802640", "vector": [], "sparse_vector": [], "title": "Performance diagnosis for inefficient loops.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Writing efficient software is difficult. Design and implementation defects can cause severe performance degradation. Unfortunately, existing performance diagnosis techniques like profilers are still preliminary. They can locate code regions that consume resources, but not the ones that waste resources. In this paper, we first design a root-cause and fix-strategy taxonomy for inefficient loops, one of the most common performance problems in the field. We then design a static-dynamic hybrid analysis tool, LDoctor, to provide accurate performance diagnosis for loops. We further use sampling techniques to lower the run-time overhead without degrading the accuracy or latency of LDoctor diagnosis. Evaluation using real-world performance problems shows that LDoctor can provide better coverage and accuracy than existing techniques, with low overhead.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.41"}, {"primary_key": "3802641", "vector": [], "sparse_vector": [], "title": "SURF: summarizer of user reviews feedback.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Continuous Delivery (CD) enables mobile developers to release small, high quality chunks of working software in a rapid manner. However, faster delivery and a higher software quality do neither guarantee user satisfaction nor positive business outcomes. Previous work demonstrates that app reviews may contain crucial information that can guide developer's software maintenance efforts to obtain higher customer satisfaction. However, previous work also proves the difficulties encountered by developers in manually analyzing this rich source of data, namely (i) the huge amount of reviews an app may receive on a daily basis and (ii) the unstructured nature of their content. In this paper, we propose SURF (Summarizer of User Reviews Feedback), a tool able to (i) analyze and classify the information contained in app reviews and (ii) distill actionable change tasks for improving mobile applications. Specifically, SURF performs a systematic summarization of thousands of user reviews through the generation of an interactive, structured and condensed agenda of recommended software changes. An end-to-end evaluation of SURF, involving 2622 reviews related to 12 different mobile applications, demonstrates the high accuracy of SURF in summarizing user reviews content. In evaluating our approach we also involve the original developers of some apps, who confirm the practical usefulness of the software change recommendations made by SURF. Demo URL: https://youtu.be/Yf-U5ylJXvo Demo webpage: http://www.ifi.uzh.ch/en/seal/people/panichella/tools/SURFTool.html.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.5"}, {"primary_key": "3802642", "vector": [], "sparse_vector": [], "title": "Debugging with probabilistic event structures.", "authors": ["Ezekiel O<PERSON>"], "summary": "Debugging is a search process to find, understand and fix the root cause of software defects. Can debugging benefit from probabilistic information? We hypothesize that debugging activities can benefit from probabilistic information that capturethe statistical dependence of program features and the minorvariations of program behavior. This probabilistic informationhelps to guide the search for the root cause of the bug andprovides detailed diagnostic information (such as failure-inducinginputs and method calls leading to the fault). To realize ourhypothesis, we propose to improve debugging activities by guiding bug diagnosis using both probabilistic reasoning and program analysis. The main idea is to mine probabilistic information from program executions, then apply these information to construct probabilistic event structures (e.g. probabilistic call graphs) that guides debugging activities such as fault localization and comprehension. The resulting probabilistic model will guide bug diagnosis towards the most likely paths to the root cause of bugs and provide contextual diagnostic information.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.34"}, {"primary_key": "3802644", "vector": [], "sparse_vector": [], "title": "Balancing soundness and efficiency for practical testing of configurable systems.", "authors": ["<PERSON>", "<PERSON><PERSON> d&a<PERSON>;<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Testing configurable systems is important and challenging due to the enormous space of configurations where errors can hide. Existing approaches to test these systems are often costly or unreliable. This paper proposes S-SPLat, a technique that combines heuristic sampling with symbolic search to obtain both breadth and depth in the exploration of the configuration space. S-SPLat builds on SPLat, our previously developed technique, that explores all reachable configurations from tests. In contrast to its predecessor, S-SPLat sacrifices soundness in favor of efficiency. We evaluated our technique on eight software product lines of various sizes and on a large configurable system - GCC. Considering the results for GCC, S-SPLat was able to reproduce all five bugs that we previously found in a previous study with SPLat but much faster and it was able to find two new bugs in a recent release of GCC. Results suggest that it is preferable to use a combination of simple heuristics to drive the symbolic search as opposed to a single heuristic. S-SPLat and our experimental infrastructure are publicly available.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.64"}, {"primary_key": "3802647", "vector": [], "sparse_vector": [], "title": "SCRUMI: a board serious virtual game for teaching the SCRUM framework.", "authors": ["Adler Diniz de <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The use of serious games has emerged as a differentiated strategy to promote the teaching of essential concepts and techniques in several areas of knowledge. To contribute to the student's formation process in Software Project Management, this research presents the development and validation of an electronic board serious game, named SCRUMI, for teaching concepts inherent to the SCRUM framework. The evaluation of the proposed game was carried out according to some criteria such as usability, quality of questions and activities presentation, applicability and motivation. The main results showed that the game is presented as a good alternative to be explored in the classroom.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.124"}, {"primary_key": "3802649", "vector": [], "sparse_vector": [], "title": "Last mile end-user programmers: programming exposure, influences, and preferences of the masses.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we set out to explore the level of programming experience present among the masses (the last mile end-user programmers), the influence of various factors such as early exposure to software, as well as age, on programming experience, their effects on the types of software people mightwant to create, and the software development approaches they prefer.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.129"}, {"primary_key": "3802651", "vector": [], "sparse_vector": [], "title": "Improving test execution time with improved cache locality.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "As software systems become more complex, the number of test cases required for effective testing becomes intractable. Cache misses have been identified as a major factor that affects software execution time. In our current work we target the instruction locality problem in the context of testing.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.153"}, {"primary_key": "3802652", "vector": [], "sparse_vector": [], "title": "Uncovering features in kindred programs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The detection of similar code can support many software engineering tasks such as program understanding and API replacement. Many excellent approaches have been proposed to detect programs having similar syntactic features. However, some programs dynamically or statistically close to each other, which we call kindred programs, may be ignored. We believe the detection of kindred programs can enhance or even automate the tasks relevant to program classification. In this proposal, we will discuss our current approaches to mine kindred programs having similar functional features and behavioral features. We will also roadmap our on-going development that integrates program analysis with machine learning models to extract statistical features from codebases.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.176"}, {"primary_key": "3802653", "vector": [], "sparse_vector": [], "title": "ProEva: runtime proactive performance evaluation based on continuous-time markov chains.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yuan Feng", "<PERSON>"], "summary": "Software systems, especially service-based software systems, need to guarantee runtime performance. If their performance is degraded, some reconfiguration countermeasures should be taken. However, there is usually some latency before the countermeasures take effect. It is thus important not only to monitor the current system status passively but also to predict its future performance proactively. Continuous-time Markov chains (CTMCs) are suitable models to analyze time-bounded performance metrics (e.g., how likely a performance degradation may occur within some future period). One challenge to harness CTMCs is the measurement of model parameters (i.e., transition rates) in CTMCs at runtime. As these parameters may be updated by the system or environment frequently, it is difficult for the model builder to provide precise parameter values. In this paper, we present a framework called ProEva, which extends the conventional technique of time-bounded CTMC model checking by admitting imprecise, interval-valued estimates for transition rates. The core method of ProEva computes asymptotic expressions and bounds for the imprecise model checking output. We also present an evaluation of accuracy and computational overhead for ProEva.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.51"}, {"primary_key": "3802654", "vector": [], "sparse_vector": [], "title": "A solver for a theory of string and bit-vectors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present the Z3strBV solver for a many-sorted first-order quantifier-free theory Tw, bv of string equations, string length represented as bit-vectors, and bit-vector arithmetic aimed at formal verification, automated testing, and security analysis of C/C++ applications. Our key motivation for building such a solver is the observation that existing string solvers are not efficient at modeling the combined theory over strings and bit-vectors. We demonstrate experimentally that Z3strBV is significantly more efficient than a reduction of string/bit-vector constraints to strings/natural numbers followed by a solver for strings/natural numbers or modeling strings as bit-vectors. We also propose two optimizations. First, we explore the concept of library-aware SMT solving, which fixes summaries in the SMT solver for string library functions such as strlen in C/C++. Z3strBV is able to consume these functions directly instead of re-analyzing the functions from scratch each time. Second, we experiment with a binary search heuristic that accelerates convergence on a consistent assignment of string lengths. We also show that Z3strBV is able to detect nontrivial overflows in real-world system-level code, as confirmed against seven security vulnerabilities from the CVE and Mozilla databases.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.73"}, {"primary_key": "3802656", "vector": [], "sparse_vector": [], "title": "Fast and flexible large-scale clone detection with CloneWorks.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Clone detection in very-large inter-project repositories has numerous applications in software research and development. However, existing tools do not provide the flexibility researchers need to explore this emerging domain. We introduce CloneWorks, a fast and flexible clone detector for large-scale clone detection experiments. CloneWorks gives the user full control over the representation of the source code before clone detection, including easy plug-in of custom source transformation, normalization and filtering logic. The user can then perform targeted clone detection for any type or kind of clone of interest. CloneWorks uses our fast and scalable partitioned partial indexes approach, which can handle any input size on an average workstation using input partitioning. CloneWorks can detect Type-3 clones in an input as large as 250 million lines of code in just four hours on an average workstation, with good recall and precision as measured by our BigCloneBench.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.3"}, {"primary_key": "3802657", "vector": [], "sparse_vector": [], "title": "CloneWorks: a fast and flexible large-scale near-miss clone detection tool.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Clone detection within large inter-project source-code repositories has numerous rich applications. CloneWorks is a fast and flexible clone detector for large-scale near-miss clone detection experiments. CloneWorks gives the user full control over the processing of the source code before clone detection, enabling the user to target any clone type or perform custom clone detection experiments. Scalable clone detection is achieved, even on commodity hardware, using our partitioned partial indexes approach. CloneWorks scales to 250MLOC in just four hours on an average workstation with good recall and precision.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.78"}, {"primary_key": "3802659", "vector": [], "sparse_vector": [], "title": "JSFox: integrating static and dynamic type analysis of JavaScript programs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "JavaScript is a dynamic programming language that has been widely used nowadays. The dynamism has become a hindrance of type analysis for JavaScript. Existing works use either static or dynamic type analysis to infer variable types for JavaScript. Static type analysis of JavaScript is difficult since it is hard to predict the behavior of the language without execution. Dynamic type analysis is usually incomplete as it might not cover all paths of a JavaScript program. In this work, we propose jsFox, a browser-agnostic approach that provides integrated type analysis, based on both static and dynamic type analysis, which enables us to gain the merits of both types of analysis. We have made use of the integrated type analysis for finding type issues that could potentially lead to erroneous results. jsFox discovers 23 type issues in existing benchmark suites and real-world Web applications.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.91"}, {"primary_key": "3802660", "vector": [], "sparse_vector": [], "title": "Codeflaws: a programming competition benchmark for evaluating automated program repair tools.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Several automated program repair techniques have been proposed to reduce the time and effort spent in bug-fixing. While these repair tools are designed to be generic such that they could address many software faults, different repair tools may fix certain types of faults more effectively than other tools. Therefore, it is important to compare more objectively the effectiveness of different repair tools on various fault types. However, existing benchmarks on automated program repairs do not allow thorough investigation of the relationship between fault types and the effectiveness of repair tools. We present Codeflaws, a set of 3902 defects from 7436 programs automatically classified across 39 defect classes (we refer to different types of fault as defect classes derived from the syntactic differences between a buggy program and a patched program).", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.76"}, {"primary_key": "3802662", "vector": [], "sparse_vector": [], "title": "Writing good software engineering research papers: revisited.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the goal of helping software engineering researchers understand how to improve their papers, <PERSON> presented \"Writing Good Software Engineering Research Papers\" in 2003. <PERSON> analyzed the abstracts of the papers submitted to the 2002 International Conference of Software Engineering (ICSE) to determine trends in research question type, contribution type, and validation approach. We revisit <PERSON>'s work to see how the software engineering research community has evolved since 2002. The goal of this paper is to aid software engineering researchers in understanding trends in research question design, research question type, and validation approach by analyzing the abstracts of the papers submitted to ICSE 2016. We implemented <PERSON>'s recommendation for replicating her study through the use of multiple coders and the calculation of inter-rater reliability and demonstrate that her approach can be repeated. Our results indicate that reviewers have increased expectations that papers have solid evaluations of the research contribution. Additionally, the 2016 results include at least 17% mining software repository (MSR) papers, a category of papers not seen in 2002. The advent of MSR papers has increased the use of generalization/characterization research questions, the production of empirical report contribution, and validation by evaluation.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.51"}, {"primary_key": "3802664", "vector": [], "sparse_vector": [], "title": "Search-driven string constraint solving for vulnerability detection.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Constraint solving is an essential technique for detecting vulnerabilities in programs, since it can reason about input sanitization and validation operations performed on user inputs. However, real-world programs typically contain complex string operations that challenge vulnerability detection. State-of-the-art string constraint solvers support only a limited set of string operations and fail when they encounter an unsupported one, this leads to limited effectiveness in finding vulnerabilities. In this paper we propose a search-driven constraint solving technique that complements the support for complex string operations provided by any existing string constraint solver. Our technique uses a hybrid constraint solving procedure based on the Ant Colony Optimization meta-heuristic. The idea is to execute it as a fallback mechanism, only when a solver encounters a constraint containing an operation that it does not support. We have implemented the proposed search-driven constraint solving technique in the ACO-Solver tool, which we have evaluated in the context of injection and XSS vulnerability detection for Java Web applications. We have assessed the benefits and costs of combining the proposed technique with two state-of-the-art constraint solvers (Z3-str2 and CVC4). The experimental results, based on a benchmark with 104 constraints derived from nine realistic Web applications, show that our approach, when combined in a state-of-the-art solver, significantly improves the number of detected vulnerabilities (from 4.7% to 71.9% for Z3-str2, from 85.9% to 100.0% for CVC4), and solves several cases on which the solver fails when used stand-alone (46 more solved cases for Z3-str2, and 11 more for CVC4), while still keeping the execution time affordable in practice.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.26"}, {"primary_key": "3802668", "vector": [], "sparse_vector": [], "title": "Predicting defects using test execution logs in an industrial setting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Researchers often focus on the development process and the final product (source code) to investigate and predict software defects. Unfortunately, these models may not be applicable to software projects in which there is no access to the data sources regarding development process. For example, in cases when a company conducts tests on behalf of its business contractors, it is only possible to evaluate in-process quality of the company based on its testing process. We present an industrial case at Ericsson Turkey that illustrates such a business constraint. We define a set of in-process testing metrics that are extracted from acceptance test execution logs of a large scale software application developed at Ericsson Turkey. We measure the acceptance testing process of 15 weeks using these metrics, and predict the number of defects reported in weekly acceptance tests. We report our measurement, model construction and assessment steps in this paper.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.148"}, {"primary_key": "3802669", "vector": [], "sparse_vector": [], "title": "Students as partners in a multi-media note-taking app development: best practices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper summarises some of the best practices learned from an extended software engineering project completed through a collaboration of multidisciplinary faculty and several teams of computer science students. The collaboration delivered an advanced multimedia note-taking application, as an open educational resource (OER), capable of supporting both students and research into note-making practices. The project lasted beyond a single academic year, thus enabling multiple student cohort participation, and took place in an English medium of instruction, Sino-foreign university in China. The experiences and reflections surrounding the project were examined, with a number of resulting ideas for best practices.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.58"}, {"primary_key": "3802672", "vector": [], "sparse_vector": [], "title": "Clone refactoring with lambda expressions.", "authors": ["<PERSON><PERSON>", "Davood Mazinanian", "<PERSON><PERSON><PERSON>"], "summary": "Lambda expressions have been introduced in Java 8 to support functional programming and enable behavior parameterization by passing functions as parameters to methods. The majority of software clones (duplicated code) are known to have behavioral differences (i.e., Type-2 and Type-3 clones). However, to the best of our knowledge, there is no previous work to investigate the utility of Lambda expressions for parameterizing such behavioral differences in clones. In this paper, we propose a technique that examines the applicability of Lambda expressions for the refactoring of clones with behavioral differences. Moreover, we empirically investigate the applicability and characteristics of the Lambda expressions introduced to refactor a large dataset of clones. Our findings show that Lambda expressions enable the refactoring of a significant portion of clones that could not be refactored by any other means.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.14"}, {"primary_key": "3802673", "vector": [], "sparse_vector": [], "title": "An efficient, robust, and scalable approach for analyzing interacting android apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "When multiple apps on an Android platform interact, faults and security vulnerabilities can occur. Software engineers need to be able to analyze interacting apps to detect such problems. Current approaches for performing such analyses, however, do not scale to the numbers of apps that may need to be considered, and thus, are impractical for application to real-world scenarios. In this paper, we introduce JITANA, a program analysis framework designed to analyze multiple Android apps simultaneously. By using a classloader-based approach instead of a compiler-based approach such as SOOT, JITANA is able to simultaneously analyze large numbers of interacting apps, perform on-demand analysis of large libraries, and effectively analyze dynamically generated code. Empirical studies of JITANA show that it is substantially more efficient than a state-of-the-art approach, and that it can effectively and efficiently analyze complex apps including Facebook, Pokemon Go, and Pandora that the state-of-the-art approach cannot handle.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.37"}, {"primary_key": "3802674", "vector": [], "sparse_vector": [], "title": "Syntactic and semantic differencing for combinatorial models of test designs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Combinatorial test design (CTD) is an effective test design technique, considered to be a testing best practice. CTD provides automatic test plan generation, but it requires a manual definition of the test space in the form of a combinatorial model. As the system under test evolves, e.g., due to iterative development processes and bug fixing, so does the test space, and thus, in the context of CTD, evolution translates into frequent manual model definition updates. Manually reasoning about the differences between versions of real-world models following such updates is infeasible due to their complexity and size. Moreover, representing the differences is challenging. In this work, we propose a first syntactic and semantic differencing technique for combinatorial models of test designs. We define a concise and canonical representation for differences between two models, and suggest a scalable algorithm for automatically computing and presenting it. We use our differencing technique to analyze the evolution of 42 real-world industrial models, demonstrating its applicability and scalability. Further, a user study with 16 CTD practitioners shows that comprehension of differences between real-world combinatorial model versions is challenging and that our differencing tool significantly improves the performance of less experienced practitioners. The analysis and user study provide evidence for the potential usefulness of our differencing approach. Our work advances the state-of-the-art in CTD with better capabilities for change comprehension and management.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.63"}, {"primary_key": "3802678", "vector": [], "sparse_vector": [], "title": "GEMMA: multi-objective optimization of energy consumption of GUIs in Android apps.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This tool demonstration describes GEMMA, a tool aimed at optimizing the colors used by Android apps, with the goal of reducing the energy consumption on (AM)OLED displays while keeping the user interface visually attractive for end-users. GEMMA has been developed as a distributed architecture to ensure scalability. It is composed of a Web-based client and processing nodes that are capable of analyzing multiple requests (apps) concurrently. The underlying approach makes use of power models, color theory, and multi-objective genetic algorithms. The empirical evaluation of GEMMA indicated its ability to reduce energy consumption while producing color combinations pleasant enough for the users. Also, a qualitative analysis conducted with app developers highlighted the potential applicability of the tool in an industrial context.VIDEO: https://www.youtube.com/watch?v=k-5ReMVwK0c.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.10"}, {"primary_key": "3802679", "vector": [], "sparse_vector": [], "title": "Refactoring opportunities for replacing type code with state and subclass.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Refactoring restructures a program to improve itsreadability and maintainability, without changing its originalbehavior. One of the key steps in refactoring is the identification ofpotential refactoring opportunities. In this paper, we discuss therelevance of two popular refactorings \"Replace Type Code withSubclass\" and \"Replace Type Code with State\" in real world Javaapplications and describe some of the challenges in automaticallyidentifying these refactoring opportunities.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.97"}, {"primary_key": "3802680", "vector": [], "sparse_vector": [], "title": "Analyzing software engineering experiments: everything you always wanted to know but were afraid to ask.", "authors": ["Sira <PERSON>"], "summary": "Experimentation is a key issue in science and engineering. But it is one of software engineering's stumbling blocks. Quite a lot of experiments are run nowadays, but it is a risky business. Software engineering has some special features, leading to some experimentation issues being conceived of differently than in other disciplines. The aim of this technical briefing is to help participants to avoid common pitfalls when analyzing the results of software engineering experiments. The technical briefing is not intended as a data analysis course, because there is already plenty of literature on this subject. It reviews several key issues that we have identified in published software engineering experiments, and addresses them based on the knowledge acquired after 18 years running experiments.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.160"}, {"primary_key": "3802681", "vector": [], "sparse_vector": [], "title": "Does subject type influence software engineering experiment results?", "authors": ["Sira <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Context: A key issue when dealing with the generalization threat of software engineering experiments is to use different subject types. Objective: In this paper, we aim to investigate which subject types are used in experiments and their impact on results. Method: We have performed a systematic mapping study by manually searching experiments published from January 2014 to June 2016 in six leading software engineering conferences and journals. Results: Out of the 833 papers published in the period covered, we have identified 93 papers reporting experiments with subjects. Of these, 27 papers report experiments that have two subject types (professionals and students). We have studied the impact of subject type on the results of experiments reported in 11 of these papers. Conclusion: We have observed contradictory results. Only in some cases subject type influences experimental results. This suggests that further research is needed in order to find an explanation.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.113"}, {"primary_key": "3802682", "vector": [], "sparse_vector": [], "title": "Machine learning-based detection of open source license exceptions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "From a legal perspective, software licenses govern the redistribution, reuse, and modification of software as both source and binary code. Free and Open Source Software (FOSS) licenses vary in the degree to which they are permissive or restrictive in allowing redistribution or modification under licenses different from the original one(s). In certain cases, developers may modify the license by appending to it an exception to specifically allow reuse or modification under a particular condition. These exceptions are an important factor to consider for license compliance analysis since they modify the standard (and widely understood) terms of the original license. In this work, we first perform a large-scale empirical study on the change history of over 51K FOSS systems aimed at quantitatively investigating the prevalence of known license exceptions and identifying new ones. Subsequently, we performed a study on the detection of license exceptions by relying on machine learning. We evaluated the license exception classification with four different supervised learners and sensitivity analysis. Finally, we present a categorization of license exceptions and explain their implications.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.19"}, {"primary_key": "3802684", "vector": [], "sparse_vector": [], "title": "OctoUML: an environment for exploratory and collaborative software design.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Software architects seek efficient support for planningand designing models at multiple levels of abstraction andfrom different perspectives. For this it is desirable that softwaredesign tools support both informal and formal representation ofdesign, and also support their combination and the transitionbetween them. Furthermore, software design tools should beable to provide features for collaborative work on the design.OctoUML supports the creation of software models at variouslevels of formality, collaborative software design, and multi-modalinteraction methods. By combining these features, OctoUML isa prototype of a new generation software design environmentthat aims to better supports software architects in their actualsoftware design and modelling processes. Demo video URL: https://youtu.be/fsN3rfEAYHw. OctoUML Project URL: https://github.com/Imarcus/OctoUML.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.19"}, {"primary_key": "3802686", "vector": [], "sparse_vector": [], "title": "Keeping continuous deliveries safe.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Allowing swift release cycles, Continuous Delivery has become popular in application software development and is starting to be applied in safety-critical domains such as the automotive industry. These domains require thorough analysis regarding safety constraints, which can be achieved by the execution of safety tests resulting from a safety analysis on the product. With continuous delivery in place, such tests need to be executed with every build to ensure the latest software still fulfills all safety requirements. Even more though, the safety analysis has to be updated with every change to ensure the safety test suite is still up-to-date. We thus propose that a safety analysis should be treated no differently from other deliverables such as source-code and dependencies, propose guidelines to adopt this and formulate implications for the development process.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.135"}, {"primary_key": "3802688", "vector": [], "sparse_vector": [], "title": "Search-based adaptation planning framework for self-adaptive systems.", "authors": ["<PERSON>"], "summary": "Future-generation Self-Adaptive Systems (SASs) are required to adapt to the multiple, interrelated, and evolving changes. Current adaptation planning methods, which consider only one or two changes at a time and assume that changes are independent and the prioritization of them is static, need to be improved. Arguing that the adaptation planning is a search problem, this thesis highlights the feasibility and potential benefits of adopting Search-Based Optimization as an innovative planning method. A search-based adaptation planning framework is proposed to deal with these changes and make the best decisions for future-generation SASs.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.21"}, {"primary_key": "3802690", "vector": [], "sparse_vector": [], "title": "Understanding third-party libraries in mobile app analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Third-party libraries are widely used in mobile apps. Recent studies showed that third-party libraries account for more than 60% of the code in Android apps on average. As a result, program analysis on Android apps typically requires detecting or removing third-party libraries first, because they usually introduce significant noises and affect the analysis results. In this technical briefing, we will introduce the latest research advances related to third-party libraries used in mobile apps. The briefing will be focused on: (1) the importance of third-party libraries, including the current status, types and distribution, based on the analysis results on over 1 million Android apps, (2) how to detect third-party libraries from Android apps, including an overview of existing approaches and their limitations, (3) the implications of third-party libraries in software engineering tasks such as mobile app analysis, as well as case studies from the domain of program analysis and mobile security, (4) future challenges and research directions related to third-party libraries.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.161"}, {"primary_key": "3802692", "vector": [], "sparse_vector": [], "title": "Construct bug knowledge graph for bug resolution: poster.", "authors": ["<PERSON>", "<PERSON>bing Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper proposed to utilize bug knowledge graph for bug resolution. Bug knowledge graph provide more comprehensive and relevant bug information (i.e., bug reports, commits, relevant developers, etc.). Moreover, our approach can automatically update bug knowledge graph based on the the lifelong learning topic model. Preliminary results show that bug knowledge graph can provide more accurate and comprehensive information related to a bug issue.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.102"}, {"primary_key": "3802693", "vector": [], "sparse_vector": [], "title": "Full regular temporal property verification as dynamic program execution.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Verification of programs in code-level has attracted more and more attentions since the cost is high to extract models from source code. Most of the approaches available for code-level verification are carried on by inserting assertions into programs and then checking whether the assertions are violated. But in this way, only safety properties can be verified, other temporal properties of programs such as liveness cannot be verified. To tackle this problem, a unified model checking approach is presented in this paper where a program to be verified is written in a Modeling, Simulation and Verification Language (MSVL) program M and a desired property is specified by a Propositional Projection Temporal Logic (PPTL) formula P. Different from the traditional model checking approach, the negation of the desired property is translated into an MSVL program M' first. Then whether P is valid on M can be checked by evaluating whether there exists an acceptable execution of the new MSVL program \"M and M'\". This problem can be efficiently conducted with the compiler of MSVL namely MSV. The proposed approach has been implemented in a tool called UMC4MSVL. Experiments show that UMC4MSVL is efficient in verifying temporal properties of real-world programs.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.98"}, {"primary_key": "3802697", "vector": [], "sparse_vector": [], "title": "Mining Twitter data for a more responsive software engineering process.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Twitter has created an unprecedented opportunityfor software developers to monitor the opinions of large populationsof end-users of their software. However, automaticallyclassifying useful tweets is not a trivial task. Challenges stem fromthe scale of the data available, its unique format, diverse nature, and high percentage of spam. To overcome these challenges, thisextended abstract introduces a three-fold procedure that is aimedat leveraging Twitter as a main source of technical feedbackthat software developers can benefit from. The main objective isto enable a more responsive, interactive, and adaptive softwareengineering process. Our analysis is conducted using a dataset oftweets collected from the Twitter feeds of three software systems. Our results provide an initial proof of the technical value ofsoftware-relevant tweets and uncover several challenges to bepursued in our future work.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.53"}, {"primary_key": "3802701", "vector": [], "sparse_vector": [], "title": "Statically checking web API requests in JavaScript.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many JavaScript applications perform HTTP requests to web APIs, relying on the request URL, HTTP method, and request data to be constructed correctly by string operations. Traditional compile-time error checking, such as calling a non-existent method in Java, are not available for checking whether such requests comply with the requirements of a web API. In this paper, we propose an approach to statically check web API requests in JavaScript. Our approach first extracts a request's URL string, HTTP method, and the corresponding request data using an inter-procedural string analysis, and then checks whether the request conforms to given web API specifications. We evaluated our approach by checking whether web API requests in JavaScript files mined from GitHub are consistent or inconsistent with publicly available API specifications. From the 6575 requests in scope, our approach determined whether the request's URL and HTTP method was consistent or inconsistent with web API specifications with a precision of 96.0%. Our approach also correctly determined whether extracted request data was consistent or inconsistent with the data requirements with a precision of 87.9% for payload data and 99.9% for query data. In a systematic analysis of the inconsistent cases, we found that many of them were due to errors in the client code. The here proposed checker can be integrated with code editors or with continuous integration tools to warn programmers about code containing potentially erroneous requests.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.30"}, {"primary_key": "3802703", "vector": [], "sparse_vector": [], "title": "Continuous management of design- and run-time artifacts for self-adaptive systems.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "With the rise of smart and autonomous systems, self-adaptation plays a significant role in the capabilities of software-intensive systems. When developing and operating self-adaptive systems, a growing amount of information is required. This information forms the basis to systems' evolution and adaptation at run time, but is also used at design time to evolve and maintain the systems. To support development organizations in the future, efficient ways to manage information throughout the systems' lifecycle are needed. However, there is currently a lack of methods to continuously manage artifacts for self-adaptive systems. In our research, we aim to close this gap by developing methods and techniques to manage both design-time and run-time artifacts. We conduct empirical studies to identify practitioners' needs and challenges. Then we develop innovative solutions and technologies and evaluate them in practical scenarios.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.31"}, {"primary_key": "3802704", "vector": [], "sparse_vector": [], "title": "Scenario-based learning in a MOOC specialization capstone on software product management.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A Massive Open Online Course (MOOC) is a popular way for universities to deliver quality course content to a global audience. Furthermore, a MOOC specialization offers a series of related such courses with a capstone component. Typical software engineering capstone projects in campus courses involve teamwork and creating software. Within such a context, students experience the software development process and human dynamics. However, MOOC capstones need to work for individual learners, and scale to handle thousands of potential learners. Consequently, this paper outlines our approach in using scenario-based learning to simulate an environment of interacting with others for a learner playing the role of a software product manager.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.70"}, {"primary_key": "3802705", "vector": [], "sparse_vector": [], "title": "Cross-project and within-project semi-supervised software defect prediction problems study using a unified solution.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jicheng Cao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "When there exists not enough historical defect data for building accurate prediction model, semi-supervised defect prediction (SSDP) and cross-project defect prediction (CPDP) are two feasible solutions. Existing CPDP methods assume that the available source data is well labeled. However, due to expensive human efforts for labeling a large amount of defect data, usually, we can only make use of the suitable unlabeled source data to help build the prediction model. We call CPDP in this scenario as cross-project semi-supervised defect prediction (CSDP). As to within-project semi-supervised defect prediction (WSDP), although some WSDP methods have been developed in recent years, there still exists much room for improvement. In this paper, we aim to provide an effective solution for both CSDP and WSDP problems. We introduce the semi-supervised dictionary learning technique, an effective machine learning technique, into defect prediction and propose a semi-supervised structured dictionary learning (SSDL) approach for CSDP and WSDP. SSDL can make full use of the useful information in limited labeled defect data and a large amount of unlabeled data. Experiments on two public datasets indicate that SSDL can obtain better prediction performance than related SSDP methods in the CSDP scenario.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.72"}, {"primary_key": "3802709", "vector": [], "sparse_vector": [], "title": "CQM: coverage-constrained quality maximization in crowdsourcing test.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile app testing is challenging since each test needs to be executed in a variety of operating contexts including heterogeneous devices, various wireless networks, and different locations. Crowdsourcing enables a mobile app testing to be distributed as a crowdsourced task to leverage the crowd in a community. However, a high test quality and expected test context coverage are difficult to achieve in crowdsourcing test. Upon distributing a test task, mobile app providers can neither know who will participate and submit a high-qualified test report nor predict whether all expected test contexts can be covered during the test. To address this problem, we put forward a novel research problem called Coverage-constrained Quality Maximization (CQM) for crowdsourcing test. Given a mobile app test task, our objective is to discover and recommend a set of potential workers from available crowd workers such that they can accomplish the task achieving expected test context coverage and the possible highest test quality. We prove that the CQM problem is NP-Complete and then introduce two efficient greedy algorithms. Based on a real dataset of the largest Chinese crowdsourcing test platform, our evaluation shows that the proposed algorithms are effective and efficient, and can be potentially used as online services in practice.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.112"}, {"primary_key": "3802710", "vector": [], "sparse_vector": [], "title": "Towards addressing the patch overfitting problem.", "authors": ["<PERSON>"], "summary": "Current automatic program repair techniques often produce overfitting patches. Such a patch passes the test suite but does not actually repair the bug. In this paper, we propose two techniques to address the patch overfitting problem. First, we propose an automatic repair technique that performs syntactic code search to leverage bug-related code from a code database to produce patches that are likely to be correct. Due to the weak and incomplete program specification encoded in the test suite, a patch is still possible to be overfitting. We next propose a patch testing technique which generates test inputs uncovering the semantic differences between a patch and its original faulty program, tests if the patch is overfitting, and if so, generates test cases. Such overfitting-indicative test cases could be added to the test suite to make it stronger.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.42"}, {"primary_key": "3802711", "vector": [], "sparse_vector": [], "title": "Precise condition synthesis for program repair.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Runfa Yan", "<PERSON><PERSON><PERSON>", "Shi Han", "<PERSON>", "<PERSON>"], "summary": "Due to the difficulty of repairing defect, many research efforts have been devoted into automatic defect repair. Given a buggy program that fails some test cases, a typical automatic repair technique tries to modify the program to make all tests pass. However, since the test suites in real world projects are usually insufficient, aiming at passing the test suites often leads to incorrect patches. This problem is known as weak test suites or overfitting. In this paper we aim to produce precise patches, that is, any patch we produce has a relatively high probability to be correct. More concretely, we focus on condition synthesis, which was shown to be able to repair more than half of the defects in existing approaches. Our key insight is threefold. First, it is important to know what variables in a local context should be used in an \"if\" condition, and we propose a sorting method based on the dependency relations between variables. Second, we observe that the API document can be used to guide the repair process, and propose document analysis technique to further filter the variables. Third, it is important to know what predicates should be performed on the set of variables, and we propose to mine a set of frequently used predicates in similar contexts from existing projects. Based on the insight, we develop a novel program repair system, ACS, that could generate precise conditions at faulty locations. Furthermore, given the generated conditions are very precise, we can perform a repair operation that is previously deemed to be too overfitting: directly returning the test oracle to repair the defect. Using our approach, we successfully repaired 18 defects on four projects of Defects4J, which is the largest number of fully automatically repaired defects reported on the dataset so far. More importantly, the precision of our approach in the evaluation is 78.3%, which is significantly higher than previous approaches, which are usually less than 40%.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.45"}, {"primary_key": "3802712", "vector": [], "sparse_vector": [], "title": "SPAIN: security patch analysis for binaries towards understanding the pain and pills.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Fu Song"], "summary": "Software vulnerability is one of the major threats to software security. Once discovered, vulnerabilities are often fixed by applying security patches. In that sense, security patches carry valuable information about vulnerabilities, which could be used to discover, understand and fix (similar) vulnerabilities. However, most existing patch analysis approaches work at the source code level, while binary-level patch analysis often heavily relies on a lot of human efforts and expertise. Even worse, some vulnerabilities may be secretly patched without applying CVE numbers, or only the patched binary programs are available while the patches are not publicly released. These practices greatly hinder patch analysis and vulnerability analysis. In this paper, we propose a scalable binary-level patch analysis framework, named SPAIN, which can automatically identify security patches and summarize patch patterns and their corresponding vulnerability patterns. Specifically, given the original and patched versions of a binary program, we locate the patched functions and identify the changed traces (i.e., a sequence of basic blocks) that may contain security or non-security patches. Then we identify security patches through a semantic analysis of these traces and summarize the patterns through a taint analysis on the patched functions. The summarized patterns can be used to search similar patches or vulnerabilities in binary programs. Our experimental results on several real-world projects have shown that: i) SPAIN identified security patches with high accuracy and high scalability, ii) SPAIN summarized 5 patch patterns and their corresponding vulnerability patterns for 5 vulnerability types, and iii) SPAIN discovered security patches that were not documented, and discovered 3 zero-day vulnerabilities.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.49"}, {"primary_key": "3802713", "vector": [], "sparse_vector": [], "title": "Adaptive unpacking of Android apps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "More and more app developers use the packing services (or packers) to prevent attackers from reverse engineering and modifying the executable (or Dex files) of their apps. At the same time, malware authors also use the packers to hide the malicious component and evade the signature-based detection. Although there are a few recent studies on unpacking Android apps, it has been shown that the evolving packers can easily circumvent them because they are not adaptive to the changes of packers. In this paper, we propose a novel adaptive approach and develop a new system, named PackerGrind, to unpack Android apps. We also evaluate PackerGrind with real packed apps, and the results show that PackerGrind can successfully reveal the packers' protection mechanisms and recover the Dex files with low overhead, showing that our approach can effectively handle the evolution of packers.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.40"}, {"primary_key": "3802714", "vector": [], "sparse_vector": [], "title": "Learning to aggregate: an automated aggregation method for software quality model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Quality models are regarded as a well-accepted approach for assessing high-level abstract quality characteristics (e.g., maintainability) by aggregation from low-level metrics. However, most of the existing quality models adopt the weighted linear aggregation method which suffers from a lack of consensus in how to decide the correct weights. To address this issue, we present an automated aggregation method which adopts a kind of probabilistic weight instead of the subjective weight in previous aggregation methods. In particular, we utilize a topic modeling technique to estimate the probabilistic weight by learning from a software benchmark. In this manner, our approach can enable automated quality assessment by using the learned knowledge without manual effort. In addition, we conduct an application on the maintainability assessment of the systems in our benchmark. The result shows that our approach can reveal the maintainability well through a correlation analysis with the changed lines of code.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.139"}, {"primary_key": "3802726", "vector": [], "sparse_vector": [], "title": "Automated transplantation and differential testing for clones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Code clones are common in software. When applying similar edits to clones, developers often find it difficult to examine the runtime behavior of clones. The problem is exacerbated when some clones are tested, while their counterparts are not. To reuse tests for similar but not identical clones, <PERSON><PERSON> transplants one clone to its counterpart by (1) identifying variations in identifier names, types, and method call targets, (2) resolving compilation errors caused by such variations through code transformation, and (3) inserting stub code to transfer input data and intermediate output values for examination. To help developers examine behavioral differences between clones, <PERSON><PERSON> supports fine-grained differential testing at both the test outcome level and the intermediate program state level. In our evaluation on three open source projects, <PERSON><PERSON> successfully reuses tests in 94% of clone pairs without inducing build errors, demonstrating its automated code transplantation capability. To examine the robustness of G RAFTER, we systematically inject faults using a mutation testing tool, Major, and detect behavioral differences induced by seeded faults. Compared with a static cloning bug finder, <PERSON><PERSON> detects 31% more mutants using the test-level comparison and almost 2X more using the state-level comparison. This result indicates that <PERSON><PERSON> should effectively complement static cloning bug finders.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.67"}, {"primary_key": "3802728", "vector": [], "sparse_vector": [], "title": "Let smart ants help you reduce the delay penalty of multiple software projects.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Delays often occur in real-world software development projects and may cause significant monetary penalties to software companies. Meanwhile, industry lessons have shown that adding inexperienced employees would cause further delays due to the learning curve and communication overhead. However, if employees with same or similar skills and domain knowledge can be rescheduled from other concurrent projects to help with the delayed projects, it may be possible to reduce or even eliminate delay penalties without requesting extra employees. Here, the big challenge is how to conduct employee rescheduling without having employees working overtime, which is an NP hard problem in nature. To address such a problem, this paper proposes a novel employee rescheduling strategy based on improved ant colony optimization algorithm. Specifically, three generic rules are proposed to improve the effectiveness in generating valid solutions. Preliminary results on benchmark projects show that our strategy can achieve much better effectiveness than its genetic algorithm based counterpart in reducing the overall delay penalty of multiple software projects.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.56"}, {"primary_key": "3802729", "vector": [], "sparse_vector": [], "title": "RClassify: classifying race conditions in web applications via deterministic replay.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Race conditions are common in web applications but are difficult to diagnose and repair. Although there exist tools for detecting races in web applications, they all report a large number of false positives. That is, the races they report are either bogus, meaning they can never occur in practice, or benign, meaning they do not lead to erroneous behaviors. Since manually diagnosing them is tedious and error prone, reporting these race warnings to developers would be counter-productive. We propose a platform-agnostic, deterministic replay-based method for identifying not only the real but also the truly harmful race conditions. It relies on executing each pair of racing events in two different orders and assessing their impact on the program state: we say a race is harmful only if (1) both of the two executions arefeasible and (2) they lead to different program states. We have evaluated our evidence-based classification method on a large set of real websites from Fortune-500 companies and demonstrated that it significantly outperforms all state-of-the-art techniques.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.33"}, {"primary_key": "3802734", "vector": [], "sparse_vector": [], "title": "An empirical study on using hints from past fixes: poster.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "With the usage of version control systems, many bug fixes have accumulated over the years. Researchers have proposed various approaches that reuse past fixes to fix new bugs. However, some fundamental questions, such as how new bug fixes can be constructed from old fixes, have not been investigated. When an approach reuses past fixes to fix a new bug, the new bug fix should overlap with past fixes in terms of code structures and/or code names. Based on this intuition, we systematically design six overlap metrics, and conduct an empirical study on 5,735 bug fixes to investigate the usefulness of past fixes. For each bug fix, we create delta dependency graphs, and identify how bug fixes overlap with each other by detecting isomorphic subgraphs. Our results show Besides that above two major findings, we have additional ten findings, which can deepen the understanding on automatic program repair.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE-C.2017.88"}, {"primary_key": "3802736", "vector": [], "sparse_vector": [], "title": "Analyzing APIs documentation and code to detect directive defects.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Application Programming Interface (API) documents represent one of the most important references for API users. However, it is frequently reported that the documentation is inconsistent with the source code and deviates from the API itself. Such inconsistencies in the documents inevitably confuse the API users hampering considerably their API comprehension and the quality of software built from such APIs. In this paper, we propose an automated approach to detect defects of API documents by leveraging techniques from program comprehension and natural language processing. Particularly, we focus on the directives of the API documents which are related to parameter constraints and exception throwing declarations. A first-order logic based constraint solver is employed to detect such defects based on the obtained analysis results. We evaluate our approach on parts of well documented JDK 1.8 APIs. Experiment results show that, out of around 2000 API usage constraints, our approach can detect 1158 defective document directives, with a precision rate of 81.6%, and a recall rate of 82.0%, which demonstrates its practical feasibility.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1109/ICSE.2017.11"}, {"primary_key": "3883967", "vector": [], "sparse_vector": [], "title": "Proceedings of the 39th International Conference on Software Engineering, ICSE 2017, Buenos Aires, Argentina, May 20-28, 2017", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Welcome to the International Conference on Software Engineering (ICSE). This year ICSE takes place in Buenos Aires, Argentina. For the first time in its 42 years of history, ICSE visits Latin America. From the rich set of events and activities that we have scheduled for this week, there is no doubt that ICSE 2017 will be as lively and as interesting as its host city. As the ACM/IEEE flagship conference in the field, ICSE was met with its usual dose of enthusiasm by researchers, educators, and practitioners. Overall, we received over 1700 submissions for the different tracks, workshops, and co-located events. All tracks and events followed a rigorous evaluation procedure designed to ensure a high quality program for researchers and practitioners alike.", "published": "2017-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": ""}]