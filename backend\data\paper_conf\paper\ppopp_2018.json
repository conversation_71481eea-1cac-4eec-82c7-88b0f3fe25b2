[{"primary_key": "3495306", "vector": [], "sparse_vector": [], "title": "<PERSON>: uniting CPU and GPU in information retrieval systems for intra-query parallelism.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Interactive information retrieval services, such as enterprise search and document search, must provide relevant results with consistent, low response times in the face of rapidly growing data sets and query loads. These growing demands have led researchers to consider a wide range of optimizations to reduce response latency, including query processing parallelization and acceleration with co-processors such as GPUs. However, previous work runs queries either on GPU or CPU, ignoring the fact that the best processor for a given query depends on the query's characteristics, which may change as the processing proceeds.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178512"}, {"primary_key": "3495307", "vector": [], "sparse_vector": [], "title": "Performance challenges in modular parallel programs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Over the past decade, many programming languages and systems for parallel-computing have been developed, including Cilk, Fork/Join Java, Habanero Java, Parallel Haskell, Parallel ML, and X10. Although these systems raise the level of abstraction at which parallel code are written, performance continues to require the programmer to perform extensive optimizations and tuning, often by taking various architectural details into account. One such key optimization is granularity control, which requires the programmer to determine when and how parallel tasks should be sequentialized.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178516"}, {"primary_key": "3495310", "vector": [], "sparse_vector": [], "title": "Harnessing epoch-based reclamation for efficient range queries.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Concurrent sets with range query operations are highly desirable in applications such as in-memory databases. However, few set implementations offer range queries. Known techniques for augmenting data structures with range queries (or operations that can be used to build range queries) have numerous problems that limit their usefulness. For example, they impose high overhead or rely heavily on garbage collection. In this work, we show how to augment data structures with highly efficient range queries, without relying on garbage collection. We identify a property of epoch-based memory reclamation algorithms that makes them ideal for implementing range queries, and produce three algorithms, which use locks, transactional memory and lock-free techniques, respectively. Our algorithms are applicable to more data structures than previous work, and are shown to be highly efficient on a large scale Intel system.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178489"}, {"primary_key": "3495311", "vector": [], "sparse_vector": [], "title": "Reducing the burden of parallel loop schedulers for many-core processors.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This work proposes a low-overhead half-barrier pattern to schedule fine-grain parallel loops and considers its integration in the Intel OpenMP and Cilkplus schedulers. Experimental evaluation demonstrates that the scheduling overhead of our techniques is 43% lower than Intel OpenMP and 12.1x lower than Cilk. We observe 22% speedup on 48 threads, with a peak of 2.8x speedup.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178517"}, {"primary_key": "3495315", "vector": [], "sparse_vector": [], "title": "Graph partitioning applied to DAG scheduling to reduce NUMA effects.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The complexity of shared memory systems is becoming more relevant as the number of memory domains increases, with different access latencies and bandwidth rates depending on the proximity between the cores and the devices containing the data. In this context, techniques to manage and mitigate non-uniform memory access (NUMA) effects consist in migrating threads, memory pages or both and are typically applied by the system software.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178535"}, {"primary_key": "3495316", "vector": [], "sparse_vector": [], "title": "Juggler: a dependence-aware task-based execution framework for GPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Laxmi <PERSON>"], "summary": "Scientific applications with single instruction, multiple data (SIMD) computations show considerable performance improvements when run on today's graphics processing units (GPUs). However, the existence of data dependences across thread blocks may significantly impact the speedup by requiring global synchronization across multiprocessors (SMs) inside the GPU. To efficiently run applications with interblock data dependences, we need fine-granular task-based execution models that will treat SMs inside a GPU as stand-alone parallel processing units. Such a scheme will enable faster execution by utilizing all internal computation elements inside the GPU and eliminating unnecessary waits during device-wide global barriers.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178492"}, {"primary_key": "3495318", "vector": [], "sparse_vector": [], "title": "Featherlight on-the-fly false-sharing detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Shared-memory parallel programs routinely suffer from false sharing---a performance degradation caused by different threads accessing different variables that reside on the same CPU cacheline and at least one variable is modified. State-of-the-art tools detect false sharing via a heavyweight process of logging memory accesses and feeding the ensuing access traces to an offline cache simulator. We have developed Feather, a lightweight, on-the-fly false-sharing detection tool. Feather achieves low overhead by exploiting two hardware features ubiquitous in commodity CPUs: the performance monitoring units (PMU) and debug registers. Additionally, Feather is a first-of-its-kind tool to detect false sharing in multi-process applications that use shared memory. <PERSON>ather allowed us to scale false-sharing detection to myriad codes. <PERSON><PERSON> detected several false-sharing cases in important multi-core and multi-process codes including previous PPoPP artifacts. Eliminating false sharing resulted in dramatic (up to 16x) speedups.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178499"}, {"primary_key": "3495319", "vector": [], "sparse_vector": [], "title": "Reducing transaction aborts by looking to the future.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Transactions are widely used in database engines and they becoming increasingly useful as a general synchronization technique for multicore machines [1]. Transactional systems allow a programmer to encapsulate multiple operations inside a transaction. All these operations appear to be executed atomically or not at all.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178518"}, {"primary_key": "3495320", "vector": [], "sparse_vector": [], "title": "Strong trylocks for reader-writer locks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "A reader-writer lock provides basic methods for shared and exclusive lock acquisition. A thread calling one of these methods may have to wait indefinitely to enter its critical section, with no guarantee of completion.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178519"}, {"primary_key": "3495321", "vector": [], "sparse_vector": [], "title": "SecureMR: secure mapreduce using homomorphic encryption and program partitioning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In cloud computing customers upload data and computation to cloud providers. As they cede their data to the cloud provider, they may cede data confidentiality. We develop SecureMR, a system that analyzes and transforms MapReduce programs to operate over encrypted data. SecureMR makes use of partially homomorphic encryption and a trusted client. We evaluate SecureMR on a set of MapReduce benchmarks.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178520"}, {"primary_key": "3495322", "vector": [], "sparse_vector": [], "title": "Practical concurrent traversals in search trees.", "authors": ["<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Operations of concurrent objects often employ optimistic concurrency-control schemes that consist of a traversal followed by a validation step. The validation checks if concurrent mutations interfered with the traversal to determine if the operation should proceed or restart. A fundamental challenge is to discover a necessary and sufficient validation check that has to be performed to guarantee correctness.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178503"}, {"primary_key": "3495325", "vector": [], "sparse_vector": [], "title": "A scalable distance-1 vertex coloring algorithm for power-law graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a distributed, unordered, label-correcting distance-1 vertex coloring algorithm, called Distributed Control (DC) coloring algorithm. DC eliminates the need for vertex-centric barriers and global synchronization for color refinement, relying only on atomic operations and local termination detection to update vertex color. We implement our DC coloring algorithm and the well-known Jones<PERSON> algorithm in the AM++ AMT runtime and compare their performance. We show that, with runtime support, the elimination of waiting time of vertex-centric barriers and investing this time for local ordering results in better execution time for power-law graphs with dense local subgraphs.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178521"}, {"primary_key": "3495326", "vector": [], "sparse_vector": [], "title": "A persistent lock-free queue for non-volatile memory.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Non-volatile memory is expected to coexist with (or even displace) volatile DRAM for main memory in upcoming architectures. This has led to increasing interest in the problem of designing and specifying durable data structures that can recover from system crashes. Data structures may be designed to satisfy stricter or weaker durability guarantees to provide a balance between the strength of the provided guarantees and performance overhead. This paper proposes three novel implementations of a concurrent lock-free queue. These implementations illustrate algorithmic challenges in building persistent lock-free data structures with different levels of durability guarantees. In presenting these challenges, the proposed algorithmic designs, and the different durability guarantees, we hope to shed light on ways to build a wide variety of durable data structures. We implemented the various designs and compared their performance overhead to a simple queue design for standard (volatile) memory.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178490"}, {"primary_key": "3495327", "vector": [], "sparse_vector": [], "title": "Efficient shuffle management with SCache for DAG computing frameworks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Haibing Guan"], "summary": "In large-scale data-parallel analytics, shuffle, or the cross-network read and aggregation of partitioned data between tasks with data dependencies, usually brings in large overhead. To reduce shuffle overhead, we present SCache, an open source plug-in system that particularly focuses on shuffle optimization. By extracting and analyzing shuffle dependencies prior to the actual task execution, SCache can adopt heuristic pre-scheduling combining with shuffle size prediction to pre-fetch shuffle data and balance load on each node. Meanwhile, SCache takes full advantage of the system memory to accelerate the shuffle process. We have implemented SCache and customized Spark to use it as the external shuffle service and co-scheduler. The performance of SCache is evaluated with both simulations and testbed experiments on a 50-node Amazon EC2 cluster. Those evaluations have demonstrated that, by incorporating SCache, the shuffle overhead of Spark can be reduced by nearly 89%, and the overall completion time of TPC-DS queries improves 40% on average.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178510"}, {"primary_key": "3495328", "vector": [], "sparse_vector": [], "title": "Communication-avoiding parallel minimum cuts and connected components.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present novel scalable parallel algorithms for finding global minimum cuts and connected components, which are important and fundamental problems in graph processing. To take advantage of future massively parallel architectures, our algorithms are communication-avoiding: they reduce the costs of communication across the network and the cache hierarchy. The fundamental technique underlying our work is the randomized sparsification of a graph: removing a fraction of graph edges, deriving a solution for such a sparsified graph, and using the result to obtain a solution for the original input. We design and implement sparsification with O(1) synchronization steps. Our global minimum cut algorithm decreases communication costs and computation compared to the state-of-the-art, while our connected components algorithm incurs few cache misses and synchronization steps. We validate our approach by evaluating MPI implementations of the algorithms on a petascale supercomputer. We also provide an approximate variant of the minimum cut algorithm and show that it approximates the exact solutions well while using a fraction of cores in a fraction of time.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178504"}, {"primary_key": "3495330", "vector": [], "sparse_vector": [], "title": "Making pull-based graph processing performant.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graph processing engines following either the push-based or pull-based pattern conceptually consist of a two-level nested loop structure. Parallelizing and vectorizing these loops is critical for high overall performance and memory bandwidth utilization. Outer loop parallelization is simple for both engine types but suffers from high load imbalance. This work focuses on inner loop parallelization for pull engines, which when performed naively leads to a significant increase in conflicting memory writes that must be synchronized.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178506"}, {"primary_key": "3495331", "vector": [], "sparse_vector": [], "title": "Hierarchical memory management for mutable state.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "It is well known that modern functional programming languages are naturally amenable to parallel programming. Achieving efficient parallelism using functional languages, however, remains difficult. Perhaps the most important reason for this is their lack of support for efficient in-place updates, i.e., mutation, which is important for the implementation of both parallel algorithms and the run-time system services (e.g., schedulers and synchronization primitives) used to execute them.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178494"}, {"primary_key": "3495332", "vector": [], "sparse_vector": [], "title": "Shared-memory parallelization of MTTKRP for dense tensors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The matricized-tensor times K<PERSON><PERSON>-<PERSON> product (MTTKRP) is the computational bottleneck for algorithms computing CP decompositions of tensors. In this work, we develop shared-memory parallel algorithms for MTTKRP involving dense tensors. The algorithms cast nearly all of the computation as matrix operations in order to use optimized BLAS subroutines, and they avoid reordering tensor entries in memory. We use our parallel implementation to compute a CP decomposition of a neuroimaging data set and achieve a speedup of up to 7.4X over existing parallel software.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178522"}, {"primary_key": "3495333", "vector": [], "sparse_vector": [], "title": "Performance modeling for GPUs using abstract kernel emulation.", "authors": ["Changwan Hong", "<PERSON><PERSON><PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Performance modeling of GPU kernels is a significant challenge. In this paper, we develop a novel approach to performance modeling for GPUs through abstract kernel emulation along with latency/gap modeling of resources. Experimental results on all benchmarks from the Rodinia suite demonstrate good accuracy in predicting execution time on multiple GPU platforms.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178524"}, {"primary_key": "3495334", "vector": [], "sparse_vector": [], "title": "An effective fusion and tile size model for optimizing image processing pipelines.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Effective models for fusion of loop nests continue to remain a challenge in both general-purpose and domain-specific language (DSL) compilers. The difficulty often arises from the combinatorial explosion of grouping choices and their interaction with parallelism and locality. This paper presents a new fusion algorithm for high-performance domain-specific compilers for image processing pipelines. The fusion algorithm is driven by dynamic programming and explores spaces of fusion possibilities not covered by previous approaches, and is driven by a cost function more concrete and precise in capturing optimization criteria than prior approaches. The fusion model is particularly tailored to the transformation and optimization sequence applied by PolyMage and Halide, two recent DSLs for image processing pipelines. Our model-driven technique when implemented in PolyMage provides significant improvements (up to 4.32X) over PolyMage's approach (which uses auto-tuning to aid its model), and over Halide's automatic approach (by up to 2.46X) on two state-of-the-art shared-memory multicore architectures.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178507"}, {"primary_key": "3495335", "vector": [], "sparse_vector": [], "title": "Optimizing N-dimensional, winograd-based convolution for manycore CPUs.", "authors": ["<PERSON><PERSON>", "<PERSON>ek<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent work on Winograd-based convolution allows for a great reduction of computational complexity, but existing implementations are limited to 2D data and a single kernel size of 3 by 3. They can achieve only slightly better, and often worse performance than better optimized, direct convolution implementations. We propose and implement an algorithm for N-dimensional Winograd-based convolution that allows arbitrary kernel sizes and is optimized for manycore CPUs. Our algorithm achieves high hardware utilization through a series of optimizations. Our experiments show that on modern ConvNets, our optimized implementation, is on average more than 3 x, and sometimes 8 x faster than other state-of-the-art CPU implementations on an Intel Xeon Phi manycore processors. Moreover, our implementation on the Xeon Phi achieves competitive performance for 2D ConvNets and superior performance for 3D ConvNets, compared with the best GPU implementations.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178496"}, {"primary_key": "3495337", "vector": [], "sparse_vector": [], "title": "Revealing parallel scans and reductions in sequential loops through function reconstruction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many sequential loops are actually scans or reductions and can be parallelized across iterations despite the loop-carried dependences. In this work, we consider the parallelization of such scan/reduction loops, and propose a practical runtime approach called sampling-and-reconstruction to extract the hidden scan/reduction patterns in these loops.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178523"}, {"primary_key": "3495338", "vector": [], "sparse_vector": [], "title": "Two concurrent data structures for efficient datalog query processing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In recent years, Datalog has gained popularity for the implementation of advanced data analysis. Applications benefit from Datalog's high-level, declarative syntax, and availability of efficient algorithms for computing solutions. The efficiency of Datalog engines has reached a point where engines such as Soufflé have reported performance results comparable to low-level hand-crafted alternatives [3].", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178525"}, {"primary_key": "3495340", "vector": [], "sparse_vector": [], "title": "A scalable queue for work distribution on GPUs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Harnessing the power of massively parallel devices like the graphics processing unit (GPU) is difficult for algorithms that show dynamic or inhomogeneous workloads. To achieve high performance, such advanced algorithms require scalable, concurrent queues to collect and distribute work. We present a new concurrent work queue, the Broker Queue, a highly efficient, linearizable queue for fine-granular work distribution on the GPU. We evaluate its usability and benefits in contrast to existing queuing algorithms. Our queue is up to one order of magnitude faster than non-blocking queues, and outperforms simpler queue designs that are unfit for fine-granular work distribution.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178526"}, {"primary_key": "3495341", "vector": [], "sparse_vector": [], "title": "Safe privatization in transactional memory.", "authors": ["<PERSON><PERSON>", "Hagit Attiya", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Transactional memory (TM) facilitates the development of concurrent applications by letting the programmer designate certain code blocks as atomic. Programmers using a TM often would like to access the same data both inside and outside transactions, e.g., to improve performance or to support legacy code. In this case, programmers would ideally like the TM to guarantee strong atomicity, where transactions can be viewed as executing atomically also with respect to non-transactional accesses. Since guaranteeing strong atomicity for arbitrary programs is prohibitively expensive, researchers have suggested guaranteeing it only for certain data-race free (DRF) programs, particularly those that follow the privatization idiom: from some point on, threads agree that a given object can be accessed non-transactionally. Supporting privatization safely in a TM is nontrivial, because this often requires correctly inserting transactional fences, which wait until all active transactions complete.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178505"}, {"primary_key": "3495342", "vector": [], "sparse_vector": [], "title": "HPVM: heterogeneous parallel virtual machine.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sarita V. Adve"], "summary": "We propose a parallel program representation for heterogeneous systems, designed to enable performance portability across a wide range of popular parallel hardware, including GPUs, vector instruction sets, multicore CPUs and potentially FPGAs. Our representation, which we call HPVM, is a hierarchical dataflow graph with shared memory and vector instructions. HPVM supports three important capabilities for programming heterogeneous systems: a compiler intermediate representation (IR), a virtual instruction set (ISA), and a basis for runtime scheduling; previous systems focus on only one of these capabilities. As a compiler IR, HPVM aims to enable effective code generation and optimization for heterogeneous systems. As a virtual ISA, it can be used to ship executable programs, in order to achieve both functional portability and performance portability across such systems. At runtime, HPVM enables flexible scheduling policies, both through the graph structure and the ability to compile individual nodes in a program to any of the target devices on a system. We have implemented a prototype HPVM system, defining the HPVM IR as an extension of the LLVM compiler IR, compiler optimizations that operate directly on HPVM graphs, and code generators that translate the virtual ISA to NVIDIA GPUs, Intel's AVX vector units, and to multicore X86-64 processors. Experimental results show that HPVM optimizations achieve significant performance improvements, HPVM translators achieve performance competitive with manually developed OpenCL code for both GPUs and vector hardware, and that runtime scheduling policies can make use of both program and runtime information to exploit the flexible compilation capabilities. Overall, we conclude that the HPVM representation is a promising basis for achieving performance portability and for implementing parallelizing compilers for heterogeneous parallel systems.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178493"}, {"primary_key": "3495344", "vector": [], "sparse_vector": [], "title": "High-performance genomic analysis framework with in-memory computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Tan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Sun"], "summary": "In this paper, we propose an in-memory computing framework (called GPF) that provides a set of genomic formats, APIs and a fast genomic engine for large-scale genomic data processing. Our GPF comprises two main components: (1) scalable genomic data formats and API. (2) an advanced execution engine that supports efficient compression of genomic data and eliminates redundancies in the execution engine of our GPF. We further present both system and algorithm-specific implementations for users to build genomic analysis pipeline without any acquaintance of Spark parallel programming. To test the performance of GPF, we built a WGS pipeline on top of our GPF as a test case. Our experimental data indicate that GPF completes Whole-Genome-Sequencing (WGS) analysis of 146.9G bases Human Platinum Genome in running time of 24 minutes, with over 50% parallel efficiency when used on 2048 CPU cores. Together, our GPF framework provides a fast and general engine for large-scale genomic data processing which supports in-memory computing.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178511"}, {"primary_key": "3495345", "vector": [], "sparse_vector": [], "title": "Designing scalable FPGA architectures using high-level synthesis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Massive spatial parallelism at low energy gives FPGAs the potential to be core components in large scale high performance computing (HPC) systems. In this paper we present four major design steps that harness high-level synthesis (HLS) to implement scalable spatial FPGA algorithms. To aid productivity, we introduce the open source library hlslib to complement HLS. We evaluate kernels designed with our approach on an FPGA accelerator board, demonstrating high performance and board utilization with enhanced programmer productivity. By following our guidelines, programmers can use HLS to develop efficient parallel algorithms for FPGA, scaling their implementations with increased resources on future hardware.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178527"}, {"primary_key": "3495346", "vector": [], "sparse_vector": [], "title": "Register-based implementation of the sparse general matrix-matrix multiplication on GPUs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Tan"], "summary": "General sparse matrix-matrix multiplication (SpGEMM) is an essential building block in a number of applications. In our work, we fully utilize GPU registers and shared memory to implement an efficient and load balanced SpGEMM in comparison with the existing implementations.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178529"}, {"primary_key": "3495347", "vector": [], "sparse_vector": [], "title": "Layrub: layer-centric GPU memory reuse and data migration in extreme-scale deep learning systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Xuanhua Shi", "<PERSON>"], "summary": "Growing accuracy and robustness of Deep Neural Networks (DNN) models are accompanied by growing model capacity (going deeper or wider). However, high memory requirements of those models make it difficult to execute the training process in one GPU. To address it, we first identify the memory usage characteristics for deep and wide convolutional networks, and demonstrate the opportunities of memory reuse on both intra-layer and inter-layer levels. We then present Layrub, a runtime data placement strategy that orchestrates the execution of training process. It achieves layer-centric reuse to reduce memory consumption for extreme-scale deep learning that cannot be run on one single GPU.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178528"}, {"primary_key": "3495350", "vector": [], "sparse_vector": [], "title": "DisCVar: discovering critical variables using algorithmic differentiation for transient faults.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Aggressive technology scaling trends have made the hardware of high performance computing (HPC) systems more susceptible to faults. Some of these faults can lead to silent data corruption (SDC), and represent a serious problem because they alter the HPC simulation results. In this paper, we present a full-coverage, systematic methodology called DisCVar to identify critical variables in HPC applications for protection against SDC. DisCVar uses automatic differentiation (AD) to determine the sensitivity of the simulation output to errors in program variables. We empirically validate our approach in identifying vulnerable variables by comparing the results against a full-coverage code-level fault injection campaign. We find that our DisCVar correctly identifies the variables that are critical to ensure application SDC resilience with a high degree of accuracy compared to the results of the fault injection campaign. Additionally, DisCVar requires only two executions of the target program to generate results, whereas in our experiments we needed to perform millions of executions to get the same information from a fault injection campaign.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178502"}, {"primary_key": "3495352", "vector": [], "sparse_vector": [], "title": "Quantifying and reducing execution variance in STM via model driven commit optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON> Gavrilovska", "<PERSON><PERSON>"], "summary": "Simplified parallel programming coupled with an ability to express speculative computation is realized with Software Transactional Memory (STM). Although STMs are gaining popularity because of significant improvements in parallel performance, they exhibit enormous variation in transaction execution with non-repeatable performance behavior which is unacceptable in many application domains, especially in which frame rates and responsiveness should be predictable. Thus, reducing execution variance in STM is an important performance goal that has been mostly overlooked. In this work, we minimize the variance in execution time of threads in STM by reducing non-determinism exhibited due to speculation by first quantifying non-determinism and generating an automaton that models the behavior of STM. We used the automaton to guide the STM to a less non-deterministic execution that reduced the variance in frame rate by a maximum of 65% on a version of real-world Quake3 game.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178530"}, {"primary_key": "3495354", "vector": [], "sparse_vector": [], "title": "Transparent GPU memory management for DNNs.", "authors": ["Jung-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern DNN frameworks exploit GPU acceleration by default to achieve high performance. The limitation of GPU memory capacity becomes a serious problem because DNNs are becoming deeper and larger. This paper proposes a purely software-based transparent solution, called tvDNN, to the GPU memory capacity problem. It is based on GPU memory swapping and memory object sectioning techniques. It also provides an efficient memory-object swapping schedule based on ILP (optimal) and heuristics (suboptimal). The experimental results show that tvDNN enables Caffe to build VGG-16 with a large batch size, such as 256 or 512, using a few GB of GPU memory without significant performance degradation.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178531"}, {"primary_key": "3495356", "vector": [], "sparse_vector": [], "title": "Stamp-it, amortized constant-time memory reclamation in comparison to five other schemes.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The memory reclamation problem is to determine, for any given allocated memory node, when there are no more references to the node, allowing it to be safely returned to the memory management system. In a concurrent context, the memory reclamation problem is highly non-trivial, since there may be more than one thread referencing an allocated node unbeknownst to the other threads.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178532"}, {"primary_key": "3495357", "vector": [], "sparse_vector": [], "title": "Cache-tries: concurrent lock-free hash tries with constant-time operations.", "authors": ["Aleksandar Prokopec"], "summary": "Concurrent non-blocking hash tries have good cache locality, and horizontally scalable operations. However, operations on most existing concurrent hash tries run in O(log n) time.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178498"}, {"primary_key": "3495359", "vector": [], "sparse_vector": [], "title": "Register optimizations for stencils on GPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The recent advent of compute-intensive GPU architecture has allowed application developers to explore high-order 3D stencils for better computational accuracy. A common optimization strategy for such stencils is to expose sufficient data reuse by means such as loop unrolling, with the expectation of register-level reuse. However, the resulting code is often highly constrained by register pressure. While current state-of-the-art register allocators are satisfactory for most applications, they are unable to effectively manage register pressure for such complex high-order stencils, resulting in sub-optimal code with a large number of register spills. In this paper, we develop a statement reordering framework that models stencil computations as a DAG of trees with shared leaves, and adapts an optimal scheduling algorithm for minimizing register usage for expression trees. The effectiveness of the approach is demonstrated through experimental results on a range of stencils extracted from application codes.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178500"}, {"primary_key": "3495360", "vector": [], "sparse_vector": [], "title": "A predictable synchronisation algorithm.", "authors": ["<PERSON>", "<PERSON>-Preik<PERSON>"], "summary": "Interaction with physical objects often imposes latency requirements to multi-core embedded systems. One consequence is the need for synchronisation algorithms that provide predictable latency, in addition to high throughput. We present a synchronisation algorithm that needs at most 7 atomic memory operations per asynchronous critical section. The performance is competitive, at least, to locks.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178533"}, {"primary_key": "3495361", "vector": [], "sparse_vector": [], "title": "Automated code acceleration targeting heterogeneous openCL devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Accelerators can offer exceptional performance advantages. However, programmers need to spend considerable efforts on acceleration, without knowing how sustainable the employed programming models, languages and tools are. To tackle this challenge, we propose and demonstrate a new runtime system called HTrOP that is able to automatically generate and execute OpenCL code from sequential CPU code. HTrOP transforms suitable data-parallel loops into independent OpenCL-typical work-items and handles concrete calls to these devices through a mix of library components and application-specific OpenCL host code. Computational hotspots are identified and can be offloaded to different resources (CPU, GPGPU and Xeon Phi). We demonstrate the potential of HTrOP on a broad set of applications and are able to improve the performance by 4.3X on average.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178534"}, {"primary_key": "3495365", "vector": [], "sparse_vector": [], "title": "PAM: parallel augmented maps.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ordered (key-value) maps are an important and widely-used data type for large-scale data processing frameworks. Beyond simple search, insertion and deletion, more advanced operations such as range extraction, filtering, and bulk updates form a critical part of these frameworks. We describe an interface for ordered maps that is augmented to support fast range queries and sums, and introduce a parallel and concurrent library called PAM (Parallel Augmented Maps) that implements the interface. The interface includes a wide variety of functions on augmented maps ranging from basic insertion and deletion to more interesting functions such as union, intersection, filtering, extracting ranges, splitting, and range-sums. We describe algorithms for these functions that are efficient both in theory and practice. As examples of the use of the interface and the performance of PAM, we apply the library to four applications: simple range sums, interval trees, 2D range trees, and ranked word index searching. The interface greatly simplifies the implementation of these data structures over direct implementations. Sequentially the code achieves performance that matches or exceeds existing libraries designed specially for a single application, and in parallel our implementation gets speedups ranging from 40 to 90 on 72 cores with 2-way hyperthreading.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178509"}, {"primary_key": "3495366", "vector": [], "sparse_vector": [], "title": "vSensor: leveraging fixed-workload snippets of programs for performance variance detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Jidong Zhai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Performance variance becomes increasingly challenging on current large-scale HPC systems. Even using a fixed number of computing nodes, the execution time of several runs can vary significantly. Many parallel programs executing on supercomputers suffer from such variance. Performance variance not only causes unpredictable performance requirement violations, but also makes it unintuitive to understand the program behavior. Despite prior efforts, efficient on-line detection of performance variance remains an open problem.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178497"}, {"primary_key": "3495368", "vector": [], "sparse_vector": [], "title": "A microbenchmark to study GPU performance models.", "authors": ["<PERSON><PERSON>"], "summary": "Basic microarchitectural features of NVIDIA GPUs have been stable for a decade, and many analytic solutions were proposed to model their performance. We present a way to review, systematize, and evaluate these approaches by using a microbenchmark. In this manner, we produce a brief algebraic summary of key elements of selected performance models, identify patterns in their design, and highlight their previously unknown limitations. Also, we identify a potentially superior method for estimating performance based on classical work.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178536"}, {"primary_key": "3495370", "vector": [], "sparse_vector": [], "title": "swSpTRSV: a fast sparse triangular solve with sparse level tile layout on sunway architectures.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Sparse triangular solve (SpTRSV) is one of the most important kernels in many real-world applications. Currently, much research on parallel SpTRSV focuses on level-set construction for reducing the number of inter-level synchronizations. However, the out-of-control data reuse and high cost for global memory or shared cache access in inter-level synchronization have been largely neglected in existing work.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178513"}, {"primary_key": "3495371", "vector": [], "sparse_vector": [], "title": "Superneurons: dynamic GPU memory management for training deep neural networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Going deeper and wider in neural architectures improves the accuracy, while the limited GPU DRAM places an undesired restriction on the network design domain. Deep Learning (DL) practitioners either need change to less desired network architectures, or nontrivially dissect a network across multiGPUs. These distract DL practitioners from concentrating on their original machine learning tasks. We present SuperNeurons: a dynamic GPU memory scheduling runtime to enable the network training far beyond the GPU DRAM capacity. SuperNeurons features 3 memory optimizations, \\textit{Liveness Analysis}, \\textit{Unified Tensor Pool}, and \\textit{Cost-Aware Recomputation}, all together they effectively reduce the network-wide peak memory usage down to the maximal memory usage among layers. We also address the performance issues in those memory saving techniques. Given the limited GPU DRAM, SuperNeurons not only provisions the necessary memory for the training, but also dynamically allocates the memory for convolution workspaces to achieve the high performance. Evaluations against Caffe, Torch, MXNet and TensorFlow have demonstrated that SuperNeurons trains at least 3.2432 deeper network than current ones with the leading performance. Particularly, SuperNeurons can train ResNet2500 that has $10^4$ basic network layers on a 12GB K40c.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178491"}, {"primary_key": "3495372", "vector": [], "sparse_vector": [], "title": "Lazygraph: lazy data coherency for replicas in distributed graph-parallel computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Fang Lv", "<PERSON>", "<PERSON><PERSON>"], "summary": "Replicas 1 of a vertex play an important role in existing distributed graph processing systems which make a single vertex to be parallel processed by multiple machines and access remote neighbors locally without any remote access. However, replicas of vertices introduce data coherency problem. Existing distributed graph systems treat replicas of a vertex v as an atomic and indivisible vertex, and use an eager data coherency approach to guarantee replicas atomicity. In eager data coherency approach, any changes to vertex data must be immediately communicated to all replicas of v, thus leading to frequent global synchronizations and communications.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178508"}, {"primary_key": "3495373", "vector": [], "sparse_vector": [], "title": "Interval-based memory reclamation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we present interval-based reclamation (IBR), a new approach to safe reclamation of disconnected memory blocks in nonblocking concurrent data structures. Safe reclamation is a difficult problem: a thread, before freeing a block, must ensure that no other threads are accessing that block; the required synchronization tends to be expensive. In contrast with epoch-based reclamation, in which threads reserve all blocks created after a certain time, or pointer-based reclamation (e.g., hazard pointers), in which threads reserve individual blocks, IBR allows a thread to reserve all blocks known to have existed in a bounded interval of time. By comparing a thread's reserved interval with the lifetime of a detached but not yet reclaimed block, the system can determine if the block is safe to free. Like hazard pointers, IBR avoids the possibility that a single stalled thread may reserve an unbounded number of blocks; unlike hazard pointers, it avoids a memory fence on most pointer-following operations. It also avoids the need to explicitly \"unreserve\" a no-longer-needed pointer.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178488"}, {"primary_key": "3495375", "vector": [], "sparse_vector": [], "title": "VerifiedFT: a verified, high-performance precise dynamic race detector.", "authors": ["<PERSON>", "Cormac Flanagan", "<PERSON>"], "summary": "Dynamic data race detectors are valuable tools for testing and validating concurrent software, but to achieve good performance they are typically implemented using sophisticated concurrent algorithms. Thus, they are ironically prone to the exact same kind of concurrency bugs they are designed to detect. To address these problems, we have developed VerifiedFT, a clean slate redesign of the FastTrack race detector [19]. The VerifiedFT analysis provides the same precision guarantee as FastTrack, but is simpler to implement correctly and efficiently, enabling us to mechanically verify an implementation of its core algorithm using CIVL [27]. Moreover, VerifiedFT provides these correctness guarantees without sacrificing any performance over current state-of-the-art (but complex and unverified) FastTrack implementations for Java.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178514"}, {"primary_key": "3495376", "vector": [], "sparse_vector": [], "title": "Efficient parallel determinacy race detection for two-dimensional dags.", "authors": ["<PERSON><PERSON>", "I-<PERSON>g <PERSON>", "<PERSON><PERSON>"], "summary": "A program is said to have a determinacy race if logically parallel parts of a program access the same memory location and one of the accesses is a write. These races are generally bugs in the program since they lead to non-deterministic program behavior --- different schedules of the program can lead to different results. Most prior work on detecting these races focuses on a subclass of programs with fork-join parallelism.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178515"}, {"primary_key": "3495378", "vector": [], "sparse_vector": [], "title": "SIMD code generation for stencils on brick decompositions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a stencil library and associated compiler code generation framework designed to maximize performance on higher-order stencil computations through the use of two main technologies: a fine-grained brick data layout designed to exploit the inherent multidimensional spatial locality endemic to stencil computations, and a vector scatter associative reordering transformation that reduces vector loads and alignment operations and exposes opportunities for the backend compiler to reduce computation. For a range of stencil computations, we compare the generated code expressed in the brick library to the standard tiled code. We attain up to a 7.2X speedup on the most complex stencils when running on an Intel Knights Landing (Xeon Phi) processor.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178537"}, {"primary_key": "3495379", "vector": [], "sparse_vector": [], "title": "Bridging the gap between deep learning and sparse matrix format selection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chunhua Liao", "<PERSON><PERSON><PERSON>"], "summary": "This work presents a systematic exploration on the promise and special challenges of deep learning for sparse matrix format selection---a problem of determining the best storage format for a matrix to maximize the performance of Sparse Matrix Vector Multiplication (SpMV). It describes how to effectively bridge the gap between deep learning and the special needs of the pillar HPC problem through a set of techniques on matrix representations, deep learning structure, and cross-architecture model migrations. The new solution cuts format selection errors by two thirds, and improves SpMV performance by 1.73X on average over the state of the art.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178495"}, {"primary_key": "3495380", "vector": [], "sparse_vector": [], "title": "FlashR: parallelize and scale R for machine learning using SSDs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "R is one of the most popular programming languages for statistics and machine learning, but it is slow and unable to scale to large datasets. The general approach for having an efficient algorithm in R is to implement it in C or FORTRAN and provide an R wrapper. FlashR accelerates and scales existing R code by parallelizing a large number of matrix functions in the R base package and scaling them beyond memory capacity with solid-state drives (SSDs). FlashR performs memory hierarchy aware execution to speed up parallelized R code by (i) evaluating matrix operations lazily, (ii) performing all operations in a DAG in a single execution and with only one pass over data to increase the ratio of computation to I/O, (iii) performing two levels of matrix partitioning and reordering computation on matrix partitions to reduce data movement in the memory hierarchy. We evaluate FlashR on various machine learning and statistics algorithms on inputs of up to four billion data points. Despite the huge performance gap between SSDs and RAM, FlashR on SSDs closely tracks the performance of FlashR in memory for many algorithms. The R implementations in FlashR outperforms H2O and Spark MLlib by a factor of 3 -- 20.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487.3178501"}, {"primary_key": "3530956", "vector": [], "sparse_vector": [], "title": "Proceedings of the 23rd ACM SIGPLAN Symposium on Principles and Practice of Parallel Programming, PPoPP 2018, Vienna, Austria, February 24-28, 2018", "authors": ["<PERSON>", "<PERSON>"], "summary": "It is our great pleasure to welcome you to PPoPP 2018, the 23nd ACM SIGPLAN Symposium on Principles and Practice of Parallel Programming held in Vienna, Austria during February 24-28, 2018, and co-located with the CGO 2018 and HPCA 2018 conferences. This year's symposium continues and reinforces the PPoPP tradition of publishing leading work on all aspects of parallel programming, including foundational and theoretical aspects, techniques, languages, compilers, runtime systems, tools, and practical experiences. Given the pervasiveness of parallel architectures in the general consumer market, PPoPP, with its interest in new parallel workloads, techniques and productivity tools for parallel programming, is becoming more relevant than ever to the computer science community.", "published": "2018-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3178487"}]