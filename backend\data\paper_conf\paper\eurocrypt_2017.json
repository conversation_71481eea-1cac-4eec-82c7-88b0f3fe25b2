[{"primary_key": "3763258", "vector": [], "sparse_vector": [], "title": "0-RTT Key Exchange with Full Forward Secrecy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Reducing latency overhead while maintaining critical security guarantees like forward secrecy has become a major design goal for key exchange (KE) protocols, both in academia and industry. Of particular interest in this regard are 0-RTT protocols, a class of KE protocols which allow a client to send cryptographically protected payload in zero round-trip time (0-RTT) along with the very first KE protocol message, thereby minimizing latency. Prominent examples are Google’s QUIC protocol and the upcoming TLS protocol version 1.3. Intrinsically, the main challenge in a 0-RTT key exchange is to achieve forward secrecy and security against replay attacks for the very first payload message sent in the protocol. According to cryptographic folklore, it is impossible to achieve forward secrecy for this message, because the session key used to protect it must depend on a non-ephemeral secret of the receiver. If this secret is later leaked to an attacker, it should intuitively be possible for the attacker to compute the session key by performing the same computations as the receiver in the actual session. In this paper we show that this belief is actually false. We construct the first 0-RTT key exchange protocol which provides full forward secrecy for all transmitted payload messages and is automatically resilient to replay attacks. In our construction we leverage a puncturable key encapsulation scheme which permits each ciphertext to only be decrypted once. Fundamentally, this is achieved by evolving the secret key after each decryption operation, but without modifying the corresponding public key or relying on shared state. Our construction can be seen as an application of the puncturable encryption idea of Green and Miers (S&P 2015). We provide a new generic and standard-model construction of this tool that can be instantiated with any selectively secure hierarchical identity-based key encapsulation scheme.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_18"}, {"primary_key": "3763259", "vector": [], "sparse_vector": [], "title": "A New Structural-Differential Property of 5-Round AES.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "AES is probably the most widely studied and used block cipher. Also versions with a reduced number of rounds are used as a building block in many cryptographic schemes, e.g. several candidates of the SHA-3 and CAESAR competition are based on it. So far, non-random properties which are independent of the secret key are known for up to 4 rounds of AES. These include differential, impossible differential, and integral properties. In this paper we describe anew structural property for up to 5 rounds of AES, differential in nature and which is independent of the secret key, of the details of the MixColumns matrix (with the exception that the branch number must be maximal) and of the SubBytes operation. It is very simple: By appropriate choices of difference for a number of input pairs it is possible to make sure that the number of times that the difference of the resulting output pairs lie in a particular subspace isalwaysa multiple of 8. We not only observe this property experimentally (using a small-scale version of AES), we also give a detailed proof as to why it has to exist. As a first application of this property, we describe a way to distinguish the 5-round AES permutation (or its inverse) from a random permutation with only\\(2^{32}\\)chosen texts that has a computational cost of\\(2^{35.6}\\)look-ups into memory of size\\(2^{36}\\)bytes which has a success probability greater than 99%.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_10"}, {"primary_key": "3763260", "vector": [], "sparse_vector": [], "title": "Multi-input Inner-Product Functional Encryption from Pairings.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We present a multi-input functional encryption scheme (MIFE) for the inner product functionality based on thek-Lin assumption in prime-order bilinear groups. Our construction works for any polynomial number of encryption slots and achieves adaptive security against unbounded collusion, while relying on standard polynomial hardness assumptions. Prior to this work, we did not even have a candidate for 3-slot MIFE for inner products in the generic bilinear group model. Our work is also the first MIFE scheme for a non-trivial functionality based on standard cryptographic assumptions, as well as the first to achieve polynomial security loss for a super-constant number of slots under falsifiable assumptions. Prior works required stronger non-standard assumptions such as indistinguishability obfuscation or multi-linear maps.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_21"}, {"primary_key": "3763261", "vector": [], "sparse_vector": [], "title": "Simplifying Design and Analysis of Complex Predicate Encryption Schemes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON> (TCC’14) and <PERSON><PERSON><PERSON><PERSON> (Eurocrypt’14) introduced predicate and pair encodings, respectively, as a simple way to construct and analyze attribute-based encryption schemes, or more generally predicate encryption. However, many schemes do not satisfy the simple information theoretic property proposed in those works, and thus require much more complicated analysis. In this paper, we propose a new simple property for pair encodings calledsymbolicsecurity. Proofs that pair encodings satisfy this property are concise and easy to verify. We show that this property is inherently tied to the security of predicate encryption schemes by arguing that any scheme which is not trivially broken must satisfy it. Then we use this property to discuss several ways to convert between pair encodings to obtain encryption schemes with different properties like small ciphertexts or keys. Finally, we show that any pair encoding satisfying our new property can be used to construct a fully secure predicate encryption scheme. The resulting schemes are secure under a newq-type assumption which we show follows from several of the assumptions used to construct such schemes in previous work.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_22"}, {"primary_key": "3763262", "vector": [], "sparse_vector": [], "title": "Functional Encryption: Deterministic to Randomized Functions from Simple Assumptions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Functional encryption (FE) enables fine-grained control of sensitive data by allowing users to only compute certain functions for which they have a key. The vast majority of work in FE has focused on deterministic functions, but for several applications such as privacy-aware auditing, differentially-private data release, proxy re-encryption, and more, the functionality of interest is more naturally captured by arandomized function. Recently, <PERSON><PERSON> et al. (TCC 2015) initiated a formal study ofFE for randomized functionalitieswith security againstmalicious encrypters, and gave a selectively secure construction from indistinguishability obfuscation. To date, this is the only construction of FE for randomized functionalities in the public-key setting. This stands in stark contrast to FE for deterministic functions which has been realized from a variety of assumptions. Our key contribution in this work is ageneric transformationthat converts any general-purpose, public-key FE scheme for deterministic functionalities into one that supports randomized functionalities. Our transformation uses the underlying FE scheme in a black-box way and can be instantiated using very standard number-theoretic assumptions (for instance, the DDH and RSA assumptions suffice). When applied to existing FE constructions, we obtain severaladaptively-secure, public-key functional encryption schemes for randomized functionalities with security against malicious encrypters from many different assumptions such as concrete assumptions on multilinear maps, indistinguishability obfuscation, and in the bounded-collusion setting, the existence of public-key encryption, together with standard number-theoretic assumptions. Additionally, we introduce a new, stronger definition for malicious security as the existing one falls short of capturing an important class of correlation attacks. In realizing this definition, our compiler combines ideas from disparate domains like related-key security for pseudorandom functions and deterministic encryption in a novel way. We believe that our techniques could be useful in expanding the scope of new variants of functional encryption (e.g., multi-input, hierarchical, and others) to support randomized functionalities.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_2"}, {"primary_key": "3763263", "vector": [], "sparse_vector": [], "title": "Topology-Hiding Computation Beyond Logarithmic Diameter.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A distributed computation in which nodes are connected by a partial communication graph is calledtopology-hidingif it does not reveal information about the graph (beyond what is revealed by the output of the function). Previous results [<PERSON>, <PERSON>, <PERSON>; TCC’15] have shown that topology-hiding computation protocols exist for graphs of logarithmic diameter (in the number of nodes), but the feasibility question for graphs of larger diameter was open even for very simple graphs such as chains, cycles and trees. In this work, we take a step towards topology-hiding computation protocols for arbitrary graphs by constructing protocols that can be used in a large class oflarge-diameter networks, including cycles, trees and graphs with logarithmiccircumference. Our results use very different methods from [MOR15] and can be based on a standard assumption (such as DDH).", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_21"}, {"primary_key": "3763264", "vector": [], "sparse_vector": [], "title": "Quantum-Secure Symmetric-Key Cryptography Based on Hidden Shifts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent results of <PERSON> et al., building on work by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, have shown that a wide variety of classically-secure symmetric-key cryptosystems can be completely broken byquantum chosen-plaintext attacks(qCPA). In such an attack, the quantum adversary has the ability to query the cryptographic functionality in superposition. The vulnerable cryptosystems include the Even-Mansour block cipher, the three-round Feistel network, the Encrypted-CBC-MAC, and many others. In this article, we study simple algebraic adaptations of such schemes that replace\\((\\mathbb {Z}/2)^n\\)addition with operations over alternate finite groups—such as\\(\\mathbb {Z}/2^n\\)—and provide evidence that these adaptations are qCPA-secure. These adaptations furthermore retain the classical security properties and basic structural features enjoyed by the original schemes. We establish security by treating the (quantum) hardness of the well-studiedHidden Shift problemas a cryptographic assumption. We observe that this problem has a number of attractive features in this cryptographic context, including random self-reducibility, hardness amplification, and—in many cases of interest—a reduction from the “search version” to the “decisional version.” We then establish, under this assumption, the qCPA-security of several such Hidden Shift adaptations of symmetric-key constructions. We show that a Hidden Shift version of the Even-Mansour block cipher yields a quantum-secure pseudorandom function, and that a Hidden Shift version of the Encrypted CBC-MAC yields a collision-resistant hash function. Finally, we observe that such adaptations frustrate the direct <PERSON>’s algorithm-based attacks in more general circumstances, e.g., Feistel networks and slide attacks.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_3"}, {"primary_key": "3763265", "vector": [], "sparse_vector": [], "title": "On Dual Lattice Attacks Against Small-Secret LWE and Parameter Choices in HElib and SEAL.", "authors": ["<PERSON>"], "summary": "We present novel variants of the dual-lattice attack against LWE in the presence of an unusually short secret. These variants are informed by recent progress in BKW-style algorithms for solving LWE. Applying them to parameter sets suggested by the homomorphic encryption libraries HElib and SEAL yields revised security estimates. Our techniques scale the exponent of the dual-lattice attack by a factor of\\((2\\,L)/(2\\,L+1)\\)when\\(\\log q = \\varTheta {\\left( L \\log n\\right) }\\), when the secret has constant hamming weight\\(h\\)and where\\(L\\)is the maximum depth of supported circuits. They also allow to half the dimension of the lattice under consideration at a multiplicative cost of\\(2^{h}\\)operations. Moreover, our techniques yield revised concrete security estimates. For example, both libraries promise 80 bits of security for LWE instances with\\(n=1024\\)and\\(\\log _2 q \\approx {47}\\), while the techniques described in this work lead to estimated costs of 68 bits (SEAL) and 62 bits (HElib).", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_4"}, {"primary_key": "3763266", "vector": [], "sparse_vector": [], "title": "Depth-Robust Graphs and Their Cumulative Memory Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Data-independent Memory Hard Functions (iMHFS) are finding a growing number of applications in security; especially in the domain of password hashing. An important property of a concrete iMHF is specified by fixing a directed acyclic graph (DAG)\\(G_n\\)onnnodes. The quality of that iMHF is then captured by the following two pebbling complexities of\\(G_n\\): The parallel cumulative pebbling complexity\\(\\varPi ^{\\parallel }_{cc}(G_n)\\)must be as high as possible (to ensure that the amortized cost of computing the function on dedicated hardware is dominated by the cost of memory). The sequential space-time pebbling complexity\\(\\varPi _{st}(G_n)\\)should be as close as possible to\\(\\varPi ^{\\parallel }_{cc}(G_n)\\)(to ensure that using many cores in parallel and amortizing over many instances does not give much of an advantage). In this paper we construct a family of DAGs with best possible parameters in an asymptotic sense, i.e., where\\(\\varPi ^{\\parallel }_{cc}(G_n)=\\varOmega (n^2/\\log (n))\\)(which matches a known upper bound) and\\(\\varPi _{st}(G_n)\\)is within a constant factor of\\(\\varPi ^{\\parallel }_{cc}(G_n)\\). Our analysis relies on a new connection between the pebbling complexity of a DAG and its depth-robustness (DR) – a well studied combinatorial property. We show that high DR issufficientfor high\\(\\varPi ^{\\parallel }_{cc}\\). Alwen and Blocki (CRYPTO’16) showed that high DR isnecessaryand so, together, these results fully characterize DAGs with high\\(\\varPi ^{\\parallel }_{cc}\\)in terms of DR. Complementing these results, we provide new upper and lower bounds on the\\(\\varPi ^{\\parallel }_{cc}\\)of several important candidate iMHFs from the literature. We give the first lower bounds on the memory hardness of the Catena and Balloon Hashing functions in a parallel model of computation and we give the first lower bounds of any kind for (a version) of Argon2i. Finally we describe a new class of pebbling attacks improving on those of Alwen and Blocki (CRYPTO’16). By instantiating these attacks we upperbound the\\(\\varPi ^{\\parallel }_{cc}\\)of the Password Hashing Competition winner Argon2i and one of the Balloon Hashing functions by\\(O\\left( n^{1.71} \\right) \\). We also show an upper bound of\\(O(n^{1.625})\\)for the Catena functions and the two remaining Balloon Hashing functions.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_1"}, {"primary_key": "3763267", "vector": [], "sparse_vector": [], "title": "Scrypt Is Maximally Memory-Hard.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Memory-hard functions (MHFs) are hash algorithms whose evaluation cost is dominated by memory cost. As memory, unlike computation, costs about the same across different platforms, MHFs cannot be evaluated at significantly lower cost on dedicated hardware like ASICs. MHFs have found widespread applications including password hashing, key derivation, and proofs-of-work. This paper focuses on\\(\\mathtt{scrypt} \\), a simple candidate MHF designed by <PERSON><PERSON><PERSON>, and described in RFC 7914. It has been used within a number of cryptocurrencies (e.g., Litecoin and Dogecoin) and has been an inspiration for Argon2d, one of the winners of the recent password-hashing competition. Despite its popularity, no rigorous lower bounds on its memory complexity are known. We prove that\\(\\mathtt{scrypt} \\)isoptimally memory-hard, i.e., its cumulative memory complexity (cmc) in the parallel random oracle model is\\(\\varOmega (n^2 w)\\), wherewandnare the output length and number of invocations of the underlying hash function, respectively. High cmc is a strong security target for MHFs introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC ’15) which implies high memory cost even for adversaries who can amortize the cost over many evaluations and evaluate the underlying hash functions many times in parallel. Our proof is the first showing optimal memory-hardness for any MHF. Our result improves both quantitatively and qualitatively upon the recent work by <PERSON><PERSON><PERSON> al.(EUROCRYPT ’16) who proved aweakerlower bound of\\(\\varOmega (n^2 w /\\log ^2 n)\\)for arestrictedclass of adversaries.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_2"}, {"primary_key": "3763268", "vector": [], "sparse_vector": [], "title": "Cryptography with Updates.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Starting with the work of <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> [CRYPTO’94], a rich line of work has studied the design of updatable cryptographic primitives. For example, in an updatable signature scheme, it is possible to efficiently transform a signature over a message into a signature over a related message without recomputing a fresh signature. In this work, we continue this line of research, and perform a systematic study of updatable cryptography. We take a unified approach towards adding updatability features to recently studied cryptographic objects such as attribute-based encryption, functional encryption, witness encryption, indistinguishability obfuscation, and many others that support non-interactive computation over inputs. We, in fact, go further and extend our approach to classical protocols such as zero-knowledge proofs and secure multiparty computation. To accomplish this goal, we introduce a new notion ofupdatable randomized encodingsthat extends the standard notion of randomized encodings to incorporate updatability features. We show that updatable randomized encodings can be used to generically transform cryptographic primitives to their updatable counterparts. We provide various definitions and constructions of updatable randomized encodings based on varying assumptions, ranging from one-way functions to compact functional encryption.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_15"}, {"primary_key": "3763269", "vector": [], "sparse_vector": [], "title": "Robust Transforming Combiners from Indistinguishability Obfuscation to Functional Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Indistinguishability Obfuscation (iO) has enabled an incredible number of new and exciting applications. However, our understanding of how to actually build secure iO remains in its infancy. While many candidate constructions have been published, some have been broken, and it is unclear which of the remaining candidates are secure. This work deals with the following basic question:Can we hedge our bets when it comes to iO candidates?In other words, if we have a collection of iO candidates, and we only know that at least one of them is secure, can we still make use of these candidates? This topic was recently studied by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> [CRYPTO 2016], who showed how to construct a robust iO combiner: Specifically, they showed that given the situation above, we can construct a single iO scheme that is secure as long as (1) at least one candidate iO scheme is a subexponentially secure iO, and (2) either the subexponential DDH or LWE assumptions hold. In this work, we make three contributions: (Better robust iO combiners.) First, we work to improve the assumptions needed to obtain the same result as <PERSON><PERSON><PERSON> et al.: namely we show how to replace the DDH/LWE assumption with the assumption that subexponentially secure one-way functions exist. (Transforming Combiners from iO to FE and NIKE.) Second, we consider a broader question: what if we start with several iO candidates where only one works, but we don’t care about achieving iO itself, rather we want to achieve concrete applications of iO? In this case, we are able to work with theminimalassumption of just polynomially secure one-way functions, and where the working iO candidate only achieves polynomial security. We call such combinerstransforming combiners. More generally, a transforming combiner from primitive A to primitive B is one that takes as input many candidates of primitive A, out of which we are guaranteed that at least one is secure and outputs a secure candidate of primitive B. We can correspondingly define robust transforming combiners. We present transforming combiners from indistinguishability obfuscation tofunctional encryptionandnon-interactive multiparty key exchance (NIKE). (Correctness Amplification for iO from polynomial security and one-way functions.) Finally, along the way, we obtain a result of independent interest: Recently, Bitansky and Vaikuntanathan [TCC 2016] showed how to amplify the correctness of an iO scheme, but they needed subexponential security for the iO scheme and also require subexponentially secure DDH or LWE. We show how to achieve the same correctness amplification result, but requiring only polynomial security from the iO scheme, and assuming only polynomially secure one-way functions.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_4"}, {"primary_key": "3763270", "vector": [], "sparse_vector": [], "title": "Patchable Indistinguishability Obfuscation: iO for Evolving Software.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we introducepatchable indistinguishability obfuscation: our notion adapts the notion of indistinguishability obfuscation (\\({i\\mathcal {O}}\\)) to a very general setting where obfuscated software evolves over time. We model this broadly by considering software patchesPas arbitrary Turing Machines that take as input the description of a Turing MachineM, and output a new Turing Machine description\\(M' = P(M)\\). Thus, a short patchPcan cause changes everywhere in the description ofMand can even cause the description length of the machine to increase by an arbitrary polynomial amount. We further considermulti-programpatchable indistinguishability obfuscation where a patch is applied not just to a single machineM, but to an unbounded set of machines\\(M_1,\\dots , M_n\\)to yield\\(P(M_1), \\dots , P(M_n)\\). We consider both single-program and multi-program patchable indistinguishability obfuscation in a setting where there are an unbounded number of patches that can beadaptivelychosen by an adversary. We show that sub-exponentially secure\\({i\\mathcal {O}}\\)for circuits and sub-exponentially secure re-randomizable encryption schemes (Re-randomizable encryption schemes can be instantiated under standard assumptions such as DDH, LWE.) imply single-program patchable indistinguishability obfuscation; and we show that sub-exponentially secure\\({i\\mathcal {O}}\\)for circuits and sub-exponentially secure DDH imply multi-program patchable indistinguishability obfuscation. At the our heart of results is a new notion ofsplittable\\({i\\mathcal {O}}\\)that allows us to transform any\\({i\\mathcal {O}} \\)scheme into a patchable one. Finally, we exhibit some simple applications of patchable indistinguishability obfuscation, to demonstrate how these concepts can be applied.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_5"}, {"primary_key": "3763271", "vector": [], "sparse_vector": [], "title": "Projective Arithmetic Functional Encryption and Indistinguishability Obfuscation from Degree-5 Multilinear Maps.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we propose a variant of functional encryption calledprojective arithmetic functional encryption(PAFE). Roughly speaking, our notion is like functional encryption for arithmetic circuits, but where secret keys only yield partially decrypted values. These partially decrypted values can be linearly combined with known coefficients and the result can be tested to see if it is a small value. We give adegree-preservingconstruction of PAFE from multilinear maps. That is, we show how to achieve PAFE for arithmetic circuits of degreedusing only degree-dmultilinear maps. Our construction is based on an assumption over such multilinear maps, that we justify in a generic model. We then turn to applying our notion of PAFE to one of the most pressing open problems in the foundations of cryptography: building secure indistinguishability obfuscation (\\(\\mathsf {i}\\mathcal {O}\\)) from simpler building blocks. \\(\\mathsf {i}\\mathcal {O}\\)from degree-5 multilinear maps.Recently, the works of <PERSON> [Eurocrypt 2016] and <PERSON><PERSON><PERSON> [FOCS 2016] showed how to build\\(\\mathsf {i}\\mathcal {O}\\)from constant-degree multilinear maps. However, no explicit constant was given in these works, and an analysis of these published works shows that the degree requirement would be in excess of 30. The ultimate “dream” goal of this line of work would be to reduce the degree requirement all the way to 2, allowing for the use of well-studied bilinear maps, or barring that, to a low constant that may be supportable by alternative secure low-degree multilinear map candidates. We make substantial progress toward this goal by showing how to leverage PAFE for degree-5 arithmetic circuits to achieve\\(\\mathsf {i}\\mathcal {O}\\), thus yielding the first\\(\\mathsf {i}\\mathcal {O}\\)construction from degree-5 multilinear maps.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_6"}, {"primary_key": "3763272", "vector": [], "sparse_vector": [], "title": "Random Sampling Revisited: <PERSON><PERSON><PERSON> Enumeration with <PERSON><PERSON><PERSON>run<PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In 2003, <PERSON><PERSON><PERSON><PERSON> introduced<PERSON><PERSON><PERSON> samplingto find very short lattice vectors, as an alternative to enumeration. An improved variant has been used in the past few years by <PERSON><PERSON><PERSON><PERSON>et al.to solve the largest Darmstadt SVP challenges. However, the behaviour of random sampling and its variants is not well-understood: all analyses so far rely on a questionable heuristic assumption, namely that the lattice vectors produced by some algorithm are uniformly distributed over certain parallelepipeds. In this paper, we introduce lattice enumeration with discrete pruning, which generalizes random sampling and its variants, and provides a novel geometric description based on partitions of then-dimensional space. We obtain what is arguably the first sound analysis of random sampling, by showing how discrete pruning can be rigorously analyzed under the well-known Gaussian heuristic, in the same model as the Gama-Nguyen-Regev analysis of pruned enumeration from EUROCRYPT ’10, albeit using different tools: we show how to efficiently compute the volume of the intersection of a ball with a box, and to efficiently approximate a large sum of many such volumes, based on statistical inference. Furthermore, we show how to select good parameters for discrete pruning by enumerating integer points in an ellipsoid. Our analysis is backed up by experiments and allows for the first time to reasonably estimate the success probability of random sampling and its variants, and to make comparisons with previous forms of pruned enumeration. Our work unifies random sampling and pruned enumeration and show that they are complementary of each other: both have different characteristics and offer different trade-offs to speed up enumeration.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_3"}, {"primary_key": "3763273", "vector": [], "sparse_vector": [], "title": "Unconditional UC-Secure Computation with (Stronger-Malicious) PUFs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON> et. al. (Crypto 2011) proved that unconditional UC-secure computation is possible if parties have access to honestly generated physically unclonable functions (PUFs). <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et. al. (Crypto 2014) then showed how to obtain unconditional UC secure computation based on malicious PUFs, assuming such PUFs are stateless. They also showed that unconditional oblivious transfer is impossible against an adversary that creates malicious stateful PUFs. In this work, we go beyond this seemingly tight result, by allowing any adversary to create stateful PUFs with a-priori bounded state. This relaxes the restriction on the power of the adversary (limited to stateless PUFs in previous feasibility results), therefore achieving improved security guarantees. This is also motivated by practical scenarios, where the size of a physical object may be used to compute an upper bound on the size of its memory. As a second contribution, we introduce a new model where any adversary is allowed to generate a malicious PUF that may encapsulate other (honestly generated) PUFs within it, such that the outer PUF has oracle access to all the inner PUFs. This is again a natural scenario, and in fact, similar adversaries have been studied in the tamper-proof hardware-token model (e.g., <PERSON><PERSON> et. al. (Eurocrypt 2008)), but no such notion has ever been considered with respect to PUFs. All previous constructions of UC secure protocols suffer from explicit attacks in this stronger model. In a direct improvement over previous results, we constructUC protocols with unconditional securityin both these models.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_14"}, {"primary_key": "3763274", "vector": [], "sparse_vector": [], "title": "Parallel Implementations of Masking Schemes and the Bounded Moment Leakage Model.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we provide a necessary clarification of the good security properties that can be obtained from parallel implementations of masking schemes. For this purpose, we first argue that (i) the probing model is not straightforward to interpret, since it more naturally captures the intuitions of serial implementations, and (ii) the noisy leakage model is not always convenient, e.g. when combined with formal methods for the verification of cryptographic implementations. Therefore we introduce a new model, the bounded moment model, that formalizes a weaker notion of security order frequently used in the side-channel literature. Interestingly, we prove that probing security for a serial implementation implies bounded moment security for its parallel counterpart. This result therefore enables an accurate understanding of the links between formal security analyses of masking schemes and experimental security evaluations based on the estimation of statistical moments. Besides its consolidating nature, our work also brings useful technical contributions. First, we describe and analyze refreshing and multiplication algorithms that are well suited for parallel implementations and improve security against multivariate side-channel attacks. Second, we show that simple refreshing algorithms (with linear complexity) that are not secure in the continuous probing model are secure in the continuous bounded moment model. Eventually, we discuss the independent leakage assumption required for masking to deliver its security promises, and its specificities related to the serial or parallel nature of an implementation.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_19"}, {"primary_key": "3763275", "vector": [], "sparse_vector": [], "title": "Short Generators Without Quantum Computers: The Case of Multiquadratics.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Finding a short elementgof a number field, given the ideal generated byg, is a classic problem in computational algebraic number theory. Solving this problem recovers the private key in cryptosystems introduced by <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. Work over the last few years has shown that for some number fields this problem has a surprisingly lowpost-quantumsecurity level. This paper shows, and experimentally verifies, that for some number fields this problem has a surprisingly lowpre-quantumsecurity level.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_2"}, {"primary_key": "3763276", "vector": [], "sparse_vector": [], "title": "Ad Hoc PSM Protocols: Secure Computation Without Coordination.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the notion ofad hoc secure computation, recently introduced by <PERSON><PERSON><PERSON> et al. (ITCS 2016), in the context of thePrivate Simultaneous Messages(PSM) model of <PERSON><PERSON> et al. (STOC 2004). In ad hoc secure computation we havenparties that may potentially participate in a protocol but, at the actual time of execution, only<PERSON>f them, whose identity isnotknown in advance, actually participate. This situation is particularly challenging in the PSM setting, where protocols are non-interactive (a single message from each participating party to a special output party) and where the parties rely on pre-distributed, correlated randomness (that in the ad-hoc setting will have to take into account all possible sets of participants). We present several different constructions of ad hoc PSM protocols from standard PSM protocols. These constructions imply, in particular, that efficient information-theoretic ad hoc PSM protocols exist for NC\\(^1\\)and different classes of log-space computation, and efficient computationally-secure ad hoc PSM protocols for polynomial-time computable functions can be based on a one-way function. As an application, we obtain an information-theoretic implementation oforder-revealing encryptionwhose security holds for two messages. We also consider the case where the actual number of participating partiestmay be larger than the minimalkfor which the protocol is designed to work. In this case, it is unavoidable that the output party learns the output corresponding to each subset ofkout of thetparticipants. Therefore, a “best possible security” notion, requiring that this will be theonlyinformation that the output party learns, is needed. We present connections between this notion and the previously studied notion oft-robust PSM(also known as “non-interactive MPC”). We show that constructions in this setting for even simple functions (like AND or threshold) can be translated into non-trivial instances of program obfuscation (such aspoint function obfuscationandfuzzy point function obfuscation, respectively). We view these results as a negative indication that protocols with “best possible security” are impossible to realize efficiently in the information-theoretic setting or require strong assumptions in the computational setting.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_20"}, {"primary_key": "3763277", "vector": [], "sparse_vector": [], "title": "Computational Integrity with a Public Random String from Quasi-Linear PCPs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Madars Virza"], "summary": "A party executing a computation on behalf of others may benefit from misreporting its output. Cryptographic protocols that detect this can facilitate decentralized systems with stringent computational integrity requirements. For the computation’s result to be publicly trustworthy, it is moreover imperative to usepublicly verifiable protocols that have no “backdoors” or secret keys that enable forgery. Probabilistically Checkable Proof (PCP)systems can be used to construct such protocols, but some of the main components of such systems—proof compositionand low-degree testing viaPCPs of Proximity (PCPPs)— have been considered efficiently only asymptotically, for unrealistically large computations. Recent cryptographic alternatives suffer from a non-public setup phase, or require large verification time. This work introducesSCI, the first implementation of a scalable PCP system (that uses both PCPPs and proof composition). We usedSCIto prove correctness of executions of up to\\(2^{20}\\)cycles of a simple processor, and calculated itsbreak-even point: the minimal input size for which naïve verification via re-execution becomes more costly than PCP-based verification. This marks the transition of core PCP techniques (likeproof compositionandPCPs of Proximity) from mathematical theory to practical system engineering. The thresholds obtained are nearly achievable and hence show that PCP-supported computational integrity is closer to reality than previously assumed.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_19"}, {"primary_key": "3763278", "vector": [], "sparse_vector": [], "title": "Computing Generator in Cyclotomic Integer Rings - A Subfield Algorithm for the Principal Ideal Problem in L Δ𝕂 (½) and Application to the Cryptanalysis of a FHE Scheme.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Principal Ideal Problem (resp. Short Principal Ideal Problem), shorten as PIP (resp. SPIP), consists in finding a generator (resp. short generator) of a principal ideal in the ring of integers of a number field. Several lattice-based cryptosystems rely on the presumed hardness of these two problems. In practice, most of them do not use an arbitrary number field but a power-of-two cyclotomic field. The Smart and Vercauteren fully homomorphic encryption scheme and the multilinear map of <PERSON>arg, <PERSON><PERSON>, and <PERSON><PERSON> epitomize this common restriction. Recently, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> showed that solving the SPIP in such cyclotomic rings boiled down to solving the PIP. In this paper, we present a heuristic algorithm that solves the PIP in prime-power cyclotomic fields in subexponential time\\(L_{|\\varDelta _\\mathbb {K}|}\\left( 1/2\\right) \\), where\\(\\varDelta _\\mathbb {K}\\)denotes the discriminant of the number field. This is achieved by descending to its totally real subfield. The implementation of our algorithm allows to recover in practice the secret key of the Smart and V<PERSON>cauteren scheme, for the smallest proposed parameters (in dimension 256).", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_3"}, {"primary_key": "3763279", "vector": [], "sparse_vector": [], "title": "On Removing Graded Encodings from Functional Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Functional encryption (FE) has emerged as an outstanding concept. By now, we know that beyond the immediate application to computation over encrypted data, variants withsuccinct ciphertextsare so powerful that they yield the full might of indistinguishability obfuscation (IO). Understanding how, and under which assumptions, such succinct schemes can be constructed has become a grand challenge of current research in cryptography. Whereas the first schemes were based themselves on IO, recent progress has produced constructions based onconstant-degree graded encodings. Still, our comprehension of such graded encodings remains limited, as the instantiations given so far have exhibited different vulnerabilities. Our main result is that, assuming LWE,black-box constructionsofsufficiently succinctFE schemes from constant-degree graded encodings can be transformed to rely on a much better-understood object —bilinear groups. In particular, under anüber assumptionon bilinear groups, such constructions imply IO in the plain model. The result demonstrates that the exact level of ciphertext succinctness of FE schemes is of major importance. In particular, we draw a fine line between known FE constructions from constant-degree graded encodings, which just fall short of the required succinctness, and the holy grail of basing IO on better-understood assumptions. In the heart of our result, are new techniques for removing ideal graded encoding oracles from FE constructions. Complementing the result, for weaker ideal models, namely the generic group model and the random oracle model, we show a transformation fromcollusion-resistantFE in either of the two models directly to FE (and IO) in the plain model, without assuming bilinear groups.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_1"}, {"primary_key": "3763280", "vector": [], "sparse_vector": [], "title": "A Note on Perfect Correctness by Derandomization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show a general compiler that transforms a large class of erroneous cryptographic schemes (such as public-key encryption, indistinguishability obfuscation, and secure multiparty computation schemes) into perfectly correct ones. The transformation works for schemes that arecorrect on all inputs with probability noticeably larger than half, and are secure under parallel repetition. We assume the existence of one-way functions and of functions with deterministic (uniform) time complexity\\(2^{O(n)}\\)and non-deterministic circuit complexity\\(2^{\\varOmega (n)}\\). Our transformation complements previous results that showed how public-key encryption and indistinguishability obfuscation that err on a noticeable fraction of inputs can be turned into ones thatfor all inputsare often correct. The technique relies on the idea of “reverse randomization” (<PERSON><PERSON>, Crypto 1989) and on Nisan-<PERSON><PERSON><PERSON><PERSON> style derandomization, previously used in cryptography to remove interaction from witness-indistinguishable proofs and commitment schemes (<PERSON><PERSON>, <PERSON> and <PERSON>, Crypto 2003).", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_20"}, {"primary_key": "3763281", "vector": [], "sparse_vector": [], "title": "Lattice-Based SNARGs and Their Application to More Efficient Obfuscation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Succinct non-interactive arguments (SNARGs) enable verifying\\({{\\mathsf {NP}}}\\)computations with substantially lower complexity than that required for classical\\({{\\mathsf {NP}}}\\)verification. In this work, we give the first lattice-based SNARG candidate with quasi-optimal succinctness (where the argument size is quasilinear in the security parameter). Further extension of our methods yields the first SNARG (from any assumption) that is quasi-optimal in terms ofbothprover overhead (polylogarithmic in the security parameter) as well as succinctness. Moreover, because our constructions are lattice-based, they plausibly resist quantum attacks. Central to our construction is a new notion oflinear-only vector encryptionwhich is a generalization of the notion of linear-only encryption introduced by <PERSON><PERSON> et al. (TCC 2013). We conjecture that variants of Regev encryption satisfy our new linear-only definition. Then, together with new information-theoretic approaches for building statistically-sound linear PCPs over small finite fields, we obtain the first quasi-optimal SNARGs. We then show a surprising connection between our new lattice-based SNARGs and the concrete efficiency of program obfuscation. All existing obfuscation candidates currently rely on multilinear maps. Among the constructions that make black-box use of the multilinear map, obfuscating a circuit of even moderate depth (say, 100) requires a multilinear map with multilinearity degree in excess of\\(2^{100}\\). In this work, we show that an ideal obfuscation of both the decryption function in a fully homomorphic encryption scheme and a variant of the verification algorithm of our new lattice-based SNARG yields a general-purpose obfuscator for all circuits. Finally, we give some concrete estimates needed to obfuscate this “obfuscation-complete” primitive. We estimate that at 80-bits of security, a (black-box) multilinear map with\\(\\approx \\!2^{12}\\)levels of multilinearity suffices. This is over\\(2^{80}\\)times more efficient than existing candidates, and thus, represents an important milestone towards implementable program obfuscation for all circuits.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_9"}, {"primary_key": "3763282", "vector": [], "sparse_vector": [], "title": "Private Puncturable PRFs from Standard Lattice Assumptions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A puncturable pseudorandom function (PRF) has a master keykthat enables one to evaluate the PRF at all points of the domain, and has a punctured key\\(k_x\\)that enables one to evaluate the PRF at all points but one. The punctured key\\(k_x\\)reveals no information about the value of the PRF at the punctured pointx. Punctured PRFs play an important role in cryptography, especially in applications of indistinguishability obfuscation. However, in previous constructions, the punctured key\\(k_x\\)completely reveals the punctured pointx: given\\(k_x\\)it is easy to determinex. Aprivatepuncturable PRF is one where\\(k_x\\)reveals nothing aboutx. This concept was defined by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, who showed the usefulness of private puncturing, and gave constructions based on multilinear maps. The question is whether private puncturing can be built from a standard (weaker) cryptographic assumption. We construct the first privately puncturable PRF from standard lattice assumptions, namely learning with errors (LWE) and 1 dimensional short integer solutions (1D-SIS), which have connections to worst-case hardness of general lattice problems. Our starting point is the (non-private) PRF of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. We introduce a number of new techniques to enhance this PRF, from which we obtain a privately puncturable PRF. In addition, we also study the simulation based definition of private constrained PRFs for general circuits, and show that the definition is not satisfiable.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_15"}, {"primary_key": "3763283", "vector": [], "sparse_vector": [], "title": "Group-Based Secure Computation: Optimizing Rounds, Communication, and Computation.", "authors": ["<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>"], "summary": "A recent work of <PERSON> et al. (Crypto 2016) suggests that “group-based” cryptographic protocols, namely ones that only rely on a cryptographically hard (Abelian) group, can be surprisingly powerful. In particular, they present succinct two-party protocols for securely computing branching programs and\\({\\mathsf{NC}^1}\\)circuits under the DDH assumption, providing the first alternative to fully homomorphic encryption. In this work we further explore the power of group-based secure computation protocols, improving both their asymptotic and concrete efficiency. We obtain the following results. Black-box use of group.We modify the succinct protocols of <PERSON> et al. so that they only make a black-box use of the underlying group, eliminating an expensive non-black-box setup phase. Round complexity.For any constant number of parties, we obtain 2-round MPC protocols based on a PKI setup under the DDH assumption. Prior to our work, such protocols were only known using fully homomorphic encryption or indistinguishability obfuscation. Communication complexity.Under DDH, we present a secure 2-party protocol for any\\({\\mathsf{NC}^1}\\)or log-space computation withninput bits andmoutput bits using\\(n+(1+o(1)) m+\\mathsf{poly}(\\lambda )\\)bits of communication, where\\(\\lambda \\)is a security parameter. In particular, our protocol can generateninstances of bit-oblivious-transfer using\\((4+o(1))\\cdot n\\)bits of communication. This gives the first constant-rate OT protocol under DDH. Computation complexity.We present several techniques for improving the computational cost of the share conversion procedure of <PERSON> et al., improving the concrete efficiency of group-based protocols by several orders of magnitude.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_6"}, {"primary_key": "3763284", "vector": [], "sparse_vector": [], "title": "Concurrently Composable Security with Shielded Super-Polynomial Simulators.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a new framework for concurrently composable security that relaxes the security notion of UC security. As in previous frameworks, our notion is based on the idea of providing the simulator with super-polynomial resources. However, in our new framework simulators are only givenrestricted accessto the results computed in super-polynomial time. This is done by modeling the super-polynomial resource as a stateful oracle that may directly interact with a functionality without the simulator seeing the communication. We call these oracles “shielded oracles”. Our notion is fully compatible with the UC framework, i.e., protocols proven secure in the UC framework remain secure in our framework. Furthermore, our notion lies strictly between SPS and Angel-based security, while being closed under protocol composition. Shielding away super-polynomial resources allows us to apply new proof techniques where we can replace super-polynomial entities by indistinguishable polynomially bounded entities. This allows us to construct secure protocols in the plain model using weaker primitives than in previous Angel-based protocols. In particular, we only use non-adaptive-CCA-secure commitments as a building block in our constructions. As a feasibility result, we present a constant-round general MPC protocol in the plain model based on standard polynomial-time hardness assumptions that is secure in our framework. Our protocol can be made fully black-box. As a consequence, we obtain thefirstblack-box construction of a constant-round concurrently secure general MPC protocol in the plain model based on polynomial-time hardness assumptions.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_13"}, {"primary_key": "3763285", "vector": [], "sparse_vector": [], "title": "Constraint-Hiding Constrained PRFs for NC1 from LWE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Constraint-hiding constrained PRFs (CHCPRFs), initially studied by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (PKC 2017), are constrained PRFs where the constrained key hides the description of the constraint. Envisioned with powerful applications such as searchable encryption, private-detectable watermarking and symmetric deniable encryption, the only known candidates of CHCPRFs are based on indistinguishability obfuscation or multilinear maps with strong security properties. In this paper we construct CHCPRFs for all NC\\(^1\\)circuits from the Learning with Errors assumption. The construction draws heavily from the graph-induced multilinear maps by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (TCC 2015), as well as the existing lattice-based PRFs. In fact, our construction can be viewed as an instance of the GGH15 approach where security can be reduced to LWE. We also show how to build from CHCPRFs reusable garbled circuits (RGC), or equivalently private-key function-hiding functional encryptions with 1-key security. This provides a different approach of constructing RGC from that of <PERSON> et al. (STOC 2013).", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_16"}, {"primary_key": "3763286", "vector": [], "sparse_vector": [], "title": "Relativistic (or 2-Prover 1-Round) Zero-Knowledge Protocol for \\mathsf NP Secure Against Quantum Adversaries.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we show that the zero-knowledge construction forHamiltonian Cycleremains secure against quantum adversaries in the relativistic setting. Our main technical contribution is a tool for studying the action of consecutive measurements on a quantum state which in turn gives upper bounds on the value of some entangled games. This allows us to prove the security of our protocol against quantum adversaries. We also prove security bounds for the (single-round) relativistic string commitment and bit commitment in parallel against quantum adversaries. As an additional consequence of our result, we answer an open question from [Unr12] and show tight bounds on the quantum knowledge error of some\\(\\varSigma \\)-protocols.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_13"}, {"primary_key": "3763287", "vector": [], "sparse_vector": [], "title": "Cryptanalyses of Candidate Branching Program Obfuscators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We describe new cryptanalytic attacks on the candidate branching program obfuscator proposed by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON> (GGHRSW) using the GGH13 graded encoding, and its variant using the GGH15 graded encoding as specified by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>. All our attacks require very specific structure of the branching programs being obfuscated, which in particular must have some input-partitioning property. Common to all our attacks are techniques to extract information about the “multiplicative bundling” scalars that are used in the GGHRSW construction. For GGHRSW over GGH13, we show how to recover the ideal generating the plaintext space when the branching program has input partitioning. Combined with the information that we extract about the “multiplicative bundling” scalars, we get a distinguishing attack by an extension of the annihilation attack of <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>. Alternatively, once we have the ideal we can solve the principle-ideal problem (PIP) in classical subexponential time or quantum polynomial time, hence obtaining a total break. For the variant over GGH15, we show how to use the left-kernel technique of <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON> to recover ratios of the bundling scalars. Once we have the ratios of the scalar products, we can use factoring and PIP solvers (in classical subexponential time or quantum polynomial time) to find the scalars themselves, then run mixed-input attacks to break the obfuscation.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_10"}, {"primary_key": "3763288", "vector": [], "sparse_vector": [], "title": "Decentralized Anonymous Micropayments.", "authors": ["<PERSON>", "<PERSON>", "Jingcheng Liu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Micropayments (payments worth a few pennies) have numerous potential applications. A challenge in achieving them is that payment networks charge fees that are high compared to “micro” sums of money. <PERSON> (1996) and <PERSON><PERSON><PERSON> (1997) proposed probabilistic payments as a technique to achieve micropayments: a merchant receives a macro-value payment with a given probability so that, in expectation, he receives a micro-value payment. Despite much research and trial deployment, micropayment schemes have not seen adoption, partly because a trusted party is required to process payments and resolve disputes. The widespread adoption of decentralized currencies such as Bitcoin (2009) suggests that decentralized micropayment schemes are easier to deploy. <PERSON> and <PERSON><PERSON> (2015) proposed several micropayment schemes for Bitcoin, but their schemes provide no more privacy guarantees than Bitcoin itself, whose transactions are recorded in plaintext in a public ledger. We formulate and constructdecentralized anonymous micropayment(DAM) schemes, which enable parties with access to a ledger to conduct offline probabilistic payments with one another, directly and privately. Our techniques extend those of Zerocash (2014) with a new privacy-preserving probabilistic payment protocol. One of the key ingredients of our construction isfractional message transfer(FMT), a primitive that enables probabilistic message transmission between two parties, and for which we give an efficient instantiation. Double spending in our setting cannot be prevented. Our second contribution is an economic analysis that bounds the additional utility gain of any cheating strategy, and applies to virtually any probabilistic payment scheme with offline validation. In our construction, this bound allows us to deter double spending by way of advance deposits that are revoked when cheating is detected.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_21"}, {"primary_key": "3763289", "vector": [], "sparse_vector": [], "title": "Efficient Compression of SIDH Public Keys.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Supersingular isogeny <PERSON><PERSON> (SIDH) is an attractive candidate for post-quantum key exchange, in large part due to its relatively small public key sizes. A recent paper by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> showed that the public keys defined in <PERSON><PERSON> and <PERSON>’s original SIDH scheme can be further compressed by around a factor of two, but reported that the performance penalty in utilizing this compression blew the overall SIDH runtime out by more than an order of magnitude. Given that the runtime of SIDH key exchange is currently its main drawback in relation to its lattice- and code-based post-quantum alternatives, an order of magnitude performance penalty for a factor of two improvement in bandwidth presents a trade-off that is unlikely to favor public-key compression in many scenarios. In this paper, we propose a range of new algorithms and techniques that accelerate SIDH public-key compression by more than an order of magnitude, making it roughly as fast as a round of standalone SIDH key exchange, while further reducing the size of the compressed public keys by approximately 12.5%. These improvements enable the practical use of compression, achieving public keys of only 330 bytes for the concrete parameters used to target 128 bits of quantum security and further strengthens SIDH as a promising post-quantum primitive.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_24"}, {"primary_key": "3763290", "vector": [], "sparse_vector": [], "title": "Removing the Strong RSA Assumption from Arguments over the Integers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Committing integers and proving relations between them is an essential ingredient in many cryptographic protocols. Among them, range proofs have been shown to be fundamental. They consist in proving that a committed integer lies in a public interval, which can be seen as a particular case of the more general Diophantine relations: for the committed vector of integers\\(\\varvec{x}\\), there exists a vector of integers\\(\\varvec{w}\\)such that\\(P(\\varvec{x},\\varvec{w})=0\\), wherePis a polynomial. In this paper, we revisit the security strength of the statistically hiding commitment scheme over the integers due to Damg<PERSON>rd-Fujisaki, and the zero-knowledge proofs of knowledge of openings. Our first main contribution shows how to remove the Strong RSA assumption and replace it by the standard RSA assumption in the security proofs. This improvement naturally extends to generalized commitments and more complex proofs without modifying the original protocols. As a second contribution, we design an interactive technique turning commitment scheme over the integers into commitment scheme modulo a primep. Still under the RSA assumption, this results in more efficient proofs of relations between committed values. Our methods thus improve upon existing proof systems for Diophantine relations both in terms of performance and security. We illustrate that with more efficient range proofs under the sole RSA assumption.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_11"}, {"primary_key": "3763291", "vector": [], "sparse_vector": [], "title": "<PERSON>elberger Class Relations and Application to Ideal-SVP.", "authors": ["<PERSON>", "Léo Du<PERSON>", "<PERSON>"], "summary": "The worst-case hardness of finding short vectors in ideals of cyclotomic number fields (Ideal-SVP) is a central matter in lattice based cryptography. Assuming the worst-case hardness of Ideal-SVP allows to prove the Ring-LWE and Ring-SIS assumptions, and therefore to prove the security of numerous cryptographic schemes and protocols — including key-exchange, digital signatures, public-key encryption and fully-homomorphic encryption. A series of recent works has shown thatPrincipalIdeal-SVP is not always as hard as finding short vectors in general lattices, and some schemes were broken using quantum algorithms — theSoliloquyencryption scheme, Smart-Vercauteren fully homomorphic encryption scheme fromPKC 2010, and Gentry-Garg-Halevi cryptographic multilinear-maps fromEurocrypt 2013. Those broken schemes were using a special class of principal ideals, but these works also showed how to solve SVP for principal ideals in theworst-casein quantum polynomial time for an approximation factor of\\(\\exp (\\tilde{O}(\\sqrt{n}))\\). This exposed an unexpected hardness gap between general lattices and some structured ones, and called into question the hardness of various problems over structured lattices, such as Ideal-SVP and Ring-LWE. In this work, we generalize the previous result to general ideals. Precisely, we show how to solve the close principal multiple problem (CPM) by exploiting the classical theorem that the class-group is annihilated by the (Galois-module action of) the so-called Stickelberger ideal. Under some plausible number-theoretical hypothesis, our approach provides a close principal multiple in quantum polynomial time. Combined with the previous results, this solves Ideal-SVP in the worst case in quantum polynomial time for an approximation factor of\\(\\exp (\\tilde{O}(\\sqrt{n}))\\). Although it does not seem that the security of Ring-LWE based cryptosystems is directly affected, we contribute novel ideas to the cryptanalysis of schemes based on structured lattices. Moreover, our result shows a deepening of the gap between general lattices and structured ones.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_12"}, {"primary_key": "3763292", "vector": [], "sparse_vector": [], "title": "Amortized Complexity of Zero-Knowledge Proofs Revisited: Achieving Linear Soundness Slack.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose a new zero-knowledge protocol for proving knowledge of short preimages under additively homomorphic functions that map integer vectors to an Abelian group. The protocol achieves amortized efficiency in that it only needs to sendO(n) function values to prove knowledge ofnpreimages. Furthermore we significantly improve previous bounds on how short a secret we can extract from a dishonest prover, namely our bound is a factorO(k) larger than the size of secret used by the honest prover, wherekis the statistical security parameter. In the best previous result, the factor was\\(O(k^{\\log k} n)\\). Our protocol can be applied to give proofs of knowledge for plaintexts in (Ring-)LWE-based cryptosystems, knowledge of preimages of homomorphic hash functions as well as knowledge of committed values in some integer commitment schemes.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_17"}, {"primary_key": "3763293", "vector": [], "sparse_vector": [], "title": "Magic Adversaries Versus Individual Reduction: Science Wins Either Way.", "authors": ["<PERSON>"], "summary": "We prove that, assuming there exists an injective one-way functionf,at leastone of the following statements is true: (Infinitely-often) Non-uniform public-key encryption and key agreement exist; The Feige-Shamir protocol instantiated withfis distributional concurrent zero knowledge for a large class of distributions over any OR NP-relations with small distinguishability gap. The questions of whether we can achieve these goals are known to be subject to black-box limitations. Our win-win result also establishes an unexpected connection between the complexity of public-key encryption and the round-complexity of concurrent zero knowledge. As the main technical contribution, we introduce a dissection procedure for concurrent adversaries, which enables us to transform a magic concurrent adversary that breaks the distributional concurrent zero knowledge of the Feige-Shamir protocol into non-black-box constructions of (infinitely-often) public-key encryption and key agreement. This dissection of complex algorithms gives insight into the fundamental gap between the knownuniversalsecurity reductions/simulations, in which a single reduction algorithm or simulator works foralladversaries, and the natural security definitions (that are sufficient for almost all cryptographic primitives/protocols), which switch the order of qualifiers and only require that for every adversary thereexistsanindividualreduction or simulator.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_12"}, {"primary_key": "3763294", "vector": [], "sparse_vector": [], "title": "Fixing Cracks in the Concrete: Random Oracles with Auxiliary Input, Revisited.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We revisit the security of cryptographic primitives in the random-oracle model against attackers having a bounded amount ofauxiliary informationabout the random oracle. This situation arises most naturally when an attacker carries out offline preprocessing to generate state (namely, auxiliary information) that is later used as part of an on-line attack, with perhaps the best-known example being the use of rainbow tables for function inversion. The resulting model is also critical to obtain accurate bounds againstnon-uniformattackers when the random oracle is instantiated by a concrete hash function. <PERSON><PERSON><PERSON> (Crypto 2007) introduced a generic technique (called pre-sampling) for analyzing security in this model: a random oracle for whichSbits of arbitrary auxiliary information can be replaced by a random oracle whose value is fixed in some way onPpoints; the two are distinguishable with probability at most\\(O(\\sqrt{ST/P})\\)by attackers making at mostToracle queries. <PERSON><PERSON><PERSON> conjectured that the distinguishing advantage could be made negligible for a sufficiently large polynomialP. We show that <PERSON><PERSON><PERSON>’s conjecture isfals<PERSON>y proving that the distinguishing probability is at least\\(\\varOmega (ST/P)\\). Faced with this negative general result, we establish new security bounds, — which are nearly optimal and beat pre-sampling bounds, — for specific applications of random oracles, including one-way functions, pseudorandom functions/generators, and message authentication codes. We also explore the effectiveness ofsaltingas a mechanism to defend against offline preprocessing, and give quantitative bounds demonstrating that salting provably helps in the context of one-wayness, collision-resistance, pseudorandom generators/functions, and message authentication codes. In each case, using (at most)nbits of salt, wherenis the length of the secret key, we get the same security\\(O(T/2^n)\\)in the random oracle model with auxiliary input as we get without auxiliary input. At the heart of our results is the compression technique of Gennaro and Trevisan, and its extensions by De, Trevisan and Tulsiani.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_16"}, {"primary_key": "3763295", "vector": [], "sparse_vector": [], "title": "Hashing Garbled Circuits for Free.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduceFree Hash, a new approach to generating Garbled Circuit (GC) hash at no extra cost during GC generation. This is in contrast with state-of-the-art approaches, which hash GCs at computational cost of up to\\(6\\times \\)of GC generation. GC hashing is at the core of the cut-and-choose technique of GC-based secure function evaluation (SFE). Our main idea is to intertwine hash generation/verification with GC generation and evaluation. While weallowan adversary to generate a GC\\({\\widehat{{\\mathsf {G}}{\\mathsf {C}}}}\\)whose hash collides with an honestly generated\\({\\mathsf {G}}{\\mathsf {C}} \\), such a\\({\\widehat{{\\mathsf {G}}{\\mathsf {C}}}}\\)w.h.p. will fail evaluation and cheating will be discovered. Our GC hash is simply a (slightly modified) XOR of all the gate table rows of GC. It is compatible with Free XOR and half-gates garbling, and can be made to work with many cut-and-choose SFE protocols. With today’s network speeds being not far behind hardware-assisted fixed-key garbling throughput, eliminating the GC hashing cost will significantly improve SFE performance. Our estimates show substantial cost reduction in typical settings, and up to factor 6 in specialized applications relying on GC hashes. We implemented GC hashing algorithm and report on its performance.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_16"}, {"primary_key": "3763296", "vector": [], "sparse_vector": [], "title": "Quantum Authentication and Encryption with Key Recycling - Or: How to Re-use a One-Time Pad Even if P=NP - Safely &amp; Feasibly.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We propose an information-theoretically secure encryption scheme for classical messages with quantum ciphertexts that offersdetectionof eavesdropping attacks, andre-usability of the keyin case no eavesdropping took place: the entire key can be securely re-used for encrypting new messages as long as no attack is detected. This is known to be impossible for fully classical schemes, where there is no way to detect plain eavesdropping attacks. This particular application of quantum techniques to cryptography was originally proposed by <PERSON>, <PERSON> and <PERSON><PERSON> in 1982, even before proposing quantum-key-distribution, and a simple candidate scheme was suggested but no rigorous security analysis was given. The idea was picked up again in 2005, when <PERSON>g<PERSON><PERSON>, <PERSON><PERSON><PERSON> and Salvail suggested a new scheme for the same task, but now with a rigorous security analysis. However, their scheme is much more demanding in terms of quantum capabilities: it requires the users to have a quantum computer. In contrast, and like the original scheme by <PERSON><PERSON> al., our new scheme requires from the honest users merely to prepare and measure single BB84 qubits. As such, we not only show the first provably-secure scheme that is within reach of current technology, but we also confirm <PERSON>et al.’s original intuition that a scheme in the spirit of their original construction is indeed secure.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_11"}, {"primary_key": "3763297", "vector": [], "sparse_vector": [], "title": "A Kilobit Hidden SNFS Discrete Logarithm Computation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We perform a special number field sieve discrete logarithm computation in a 1024-bit prime field. To our knowledge, this is the first kilobit-sized discrete logarithm computation ever reported for prime fields. This computation took a little over two months of calendar time on an academic cluster using the open-source CADO-NFS software. Our chosen primeplooks random, and\\(p-1\\)has a 160-bit prime factor, in line with recommended parameters for the Digital Signature Algorithm. However, ourphas been trapdoored in such a way that the special number field sieve can be used to compute discrete logarithms in\\(\\mathbb {F}_p^*\\), yet detecting thatphas this trapdoor seems out of reach. Twenty-five years ago, there was considerable controversy around the possibility of backdoored parameters for DSA. Our computations show that trapdoored primes are entirely feasible with current computing technology. We also describe special number field sieve discrete log computations carried out for multiple conspicuously weak primes found in use in the wild. As can be expected from a trapdoor mechanism which we say is hard to detect, our research did not reveal any trapdoored prime in wide use. The only way for a user to defend against a hypothetical trapdoor of this kind is to require verifiably random primes.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_8"}, {"primary_key": "3763298", "vector": [], "sparse_vector": [], "title": "High-Throughput Secure Three-Party Computation for Malicious Adversaries and an Honest Majority.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we describe a new protocol for secure three-party computation of any functionality, with an honest majority and amaliciousadversary. Our protocol has both an information-theoretic and computational variant, and is distinguished by extremely low communication complexity and very simple computation. We start from the recent semi-honest protocol of <PERSON><PERSON> et al. (ACM CCS 2016) in which the parties communicate only a single bit per AND gate, and modify it to be secure in the presence of malicious adversaries. Our protocol follows the paradigm of first constructing Beaver multiplication triples and then using them to verify that circuit gates are correctly computed. As in previous work (e.g., the so-called TinyOT and SPDZ protocols), we rely on the cut-and-choose paradigm to verify that triples are correctly constructed. We are able to utilize the fact that at most one of three parties is corrupted in order to construct an extremely simple and efficient method of constructing such triples. We also present an improved combinatorial analysis for this cut-and-choose which can be used to achieve improvements in other protocols using this approach.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_8"}, {"primary_key": "3763299", "vector": [], "sparse_vector": [], "title": "On the Exact Round Complexity of Self-composable Two-Party Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The round complexity of secure computation has been a fundamental problem in cryptography. <PERSON> and <PERSON><PERSON><PERSON> proved that 5 rounds are both necessary and sufficient for secure computation in the stand alone setting, thus resolving theexactround complexity ofstandalonesecure computation. In contrast, round complexity of secure computation in theconcurrentsetting, where several protocols may run simultaneously, is poorly understood. Since standard polynomial time simulation is impossible in the concurrent setting, alternative security notions have been proposed, e.g.,super-polynomial simulation(SPS). While SPS security can be achieved in constant rounds, the actual constant (\\(> 20\\)) is far from optimal. In this work, we take the first steps towards studying the exact round complexity of concurrent secure computation. We focus on the two party case and present a new secure computation protocol that achieves SPS security under concurrent self-composition. Our protocol has 5 rounds assuming quasi-polynomially-hard injective one-way functions (or 7 rounds assuming standard polynomially-hard collision-resistant hash functions). We also require other standard assumptions, specifically trapdoor OWPs and lossy TDFs. This matches the rounds for standalone secure computation. More specifically, our security proof presents apolynomial timereduction from SPS security to 3-round public-coin non-malleable commitments with appropriate extractability properties. Such commitments are known based on quasi-polynomially-hard injective OWFs. (The reduction also works with a special 6-round non-malleable commitment to yield the 7-round result under CRHFs.)", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_7"}, {"primary_key": "3763300", "vector": [], "sparse_vector": [], "title": "Breaking the Sub-Exponential Barrier in Obfustopia.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Indistinguishability obfuscation (\\(i\\mathcal {O}\\)) has emerged as a surprisingly powerful notion. Almost all known cryptographic primitives can be constructed from general purpose\\(i\\mathcal {O}\\)and other minimalistic assumptions such as one-way functions. A major challenge in this direction of research is to develop novel techniques for using\\(i\\mathcal {O}\\)since\\(i\\mathcal {O}\\)by itself offers virtually no protection for secret information in the underlying programs. When dealing with complex situations, often these techniques have to consider an exponential number of hybrids (usually one per input) in the security proof. This results in asub-exponentialloss in the security reduction. Unfortunately, this scenario is becoming more and more common and appears to be a fundamental barrier to many current techniques. A parallel research challenge is building obfuscation from simpler assumptions. Unfortunately, it appears that such a construction would likely incur an exponential loss in the security reduction. Thus, achieving any application of\\(i\\mathcal {O}\\)from simpler assumptions would also require a sub-exponential loss,even if the\\(i\\mathcal {O}\\)-to-application security proof incurred a polynomial loss. Functional encryption (\\(\\mathcal {FE}\\)) is known to be equivalent to\\(i\\mathcal {O}\\)up to a sub-exponential loss in the\\(\\mathcal {FE}\\)-to-\\(i\\mathcal {O}\\)security reduction; yet, unlike\\(i\\mathcal {O}\\),\\(\\mathcal {FE}\\)canbe achieved from simpler assumptions (namely, specific multilinear map assumptions) with only a polynomial loss. In the interest of basing applications on weaker assumptions, we therefore argue for using\\(\\mathcal {FE}\\)as the starting point, rather than\\(i\\mathcal {O}\\), and restricting to reductions with only a polynomial loss. By significantly expanding on ideas developed by Garg, Pandey, and Srinivasan (CRYPTO 2016), we achieve the following early results in this line of study: We constructuniversal samplersbased only on polynomially-secure public-key\\(\\mathcal {FE}\\). As an application of this result, we construct anon-interactive multiparty key exchange(NIKE) protocol for an unbounded number of users without a trusted setup. Prior to this work, such constructions were only known from indistinguishability obfuscation. We also construct trapdoor one-way permutations (OWP) based on polynomially-secure public-key\\(\\mathcal {FE}\\). This improves upon the recent result of Bitansky, Paneth, and Wichs (TCC 2016) which requires\\(i\\mathcal {O}\\)ofsub-exponential strength. We proceed in two steps, first giving a construction requiring\\(i\\mathcal {O}\\)ofpolynomial strength, and then specializing the\\(\\mathcal {FE}\\)-to-\\(i\\mathcal {O}\\)conversion to our specific application. Many of the techniques that have been developed for using\\(i\\mathcal {O}\\), including many of those based on the “punctured programming” approach, become inapplicable when we insist on polynomial reductions to\\(\\mathcal {FE}\\). As such, our results above require many new ideas that will likely be useful for future works on basing security on\\(\\mathcal {FE}\\).", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_6"}, {"primary_key": "3763301", "vector": [], "sparse_vector": [], "title": "How Fast Can Higher-Order Masking Be in Software?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Higher-order masking is widely accepted as a sound countermeasure to protect implementations of blockciphers against side-channel attacks. The main issue while designing such a countermeasure is to deal with the nonlinear parts of the cipheri.e.the so-called s-boxes. The prevailing approach to tackle this issue consists in applying the Ishai-Sahai-Wagner (ISW) scheme from CRYPTO 2003 to some polynomial representation of the s-box. Several efficient constructions have been proposed that follow this approach, but higher-order masking is still considered as a costly (impractical) countermeasure. In this paper, we investigate efficient higher-order masking techniques by conducting a case study on ARM architectures (the most widespread architecture in embedded systems). We follow a bottom-up approach by first investigating the implementation of the base field multiplication at the assembly level. Then we describe optimized low-level implementations of the ISW scheme and its variant (CPRR) due to Coronet al.(FSE 2013) [14]. Finally we present improved state-of-the-art polynomial decomposition methods for s-boxes with custom parameters and various implementation-level optimizations. We also investigate an alternative to these methods which is based on bitslicing at the s-box level. We describe new masked bitslice implementations of the AES and PRESENT ciphers. These implementations happen to be significantly faster than (optimized) state-of-the-art polynomial methods. In particular, our bitslice AES masked at order 10 runs in 0.48 megacycles, which makes 8 ms in presence of a 60 MHz clock frequency.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_20"}, {"primary_key": "3763302", "vector": [], "sparse_vector": [], "title": "Separating Semantic and Circular Security for Symmetric-Key Bit Encryption from the Learning with Errors Assumption.", "authors": ["<PERSON><PERSON><PERSON>", "Venkata Koppula", "<PERSON>"], "summary": "In this work we separate private-key semantic security from 1-circular security for bit encryption using the Learning with Error assumption. Prior works used the less standard assumptions of multilinear maps or indistinguishability obfuscation. To achieve our results we develop new techniques for obliviously evaluating branching programs.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_18"}, {"primary_key": "3763303", "vector": [], "sparse_vector": [], "title": "Modifying an Enciphering Scheme After Deployment.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Assume that a symmetric encryption scheme has been deployed and used with a secret key. We later must change the encryption scheme in a way that preserves the ability to decrypt (a subset of) previously encrypted plaintexts. Frequent real-world examples are migrating from a token-based encryption system for credit-card numbers to a format-preserving encryption (FPE) scheme, or extending the message space of an already deployed FPE. The ciphertexts may be stored in systems for which it is not easy or not efficient to retrieve them (to re-encrypt the plaintext under the new scheme). We introduce methods for functionality-preserving modifications to encryption, focusing particularly on deterministic, length-preserving ciphers such as those used to perform format-preserving encryption. We provide a new technique, that we refer to as the Zig-Zag construction, that allows one to combine two ciphers using different domains in a way that results in a secure cipher on one domain. We explore its use in the two settings above, replacing token-based systems and extending message spaces. We develop appropriate security goals and prove security relative to them assuming the underlying ciphers are themselves secure as strong pseudorandom permutations.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_17"}, {"primary_key": "3763304", "vector": [], "sparse_vector": [], "title": "Toward Fine-Grained Blackbox Separations Between Semantic and Circular-Security Notions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We address the problems of whethert-circular-secure encryption can be based on\\((t-1)\\)-circular-secure encryption or on semantic (CPA) security, if\\(t = 1\\). While for\\(t = 1\\)a folklore construction, based on CPA-secure encryption, can be used to build a 1-circular-secure encryption with the same secret-key and message space, no such constructions are known for the bit-encryption case, which is of particular importance in fully-homomorphic encryption. Also, all constructions oft-circular encryption (bitwise or otherwise) are based on specific assumptions. We make progress toward these problems by ruling out all fully-blackbox constructions of 1-seed-circular-securebit encryption from CPA-secure encryption; t-seed-circular-secure encryption from\\((t-1)\\)-seed-circular secure encryption, for any\\(t > 1\\). Informally, seed-circular security is a variant of the circular security notion in which the seed of the key-generation algorithm, instead of the secret key, is encrypted. We also show how to extend our first result to rule out a large and non-trivial class of constructions of 1-circular-secure bit encryption, which we dubkey-isolating constructions. Our separations follow the model of <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>ing<PERSON> (FOCS’01), which is a weaker separation model than that of Impagliazzo and Rudich.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_19"}, {"primary_key": "3763305", "vector": [], "sparse_vector": [], "title": "The Multi-user Security of Double Encryption.", "authors": ["Viet Tung Hoang", "<PERSON>"], "summary": "It is widely known that double encryption does not substantially increase the security of a block cipher. Indeed, the classical meet-in-the middle attack recovers the 2k-bit secret key at the cost of roughly\\(2^k\\)off-line enciphering operations, in addition to very few known plaintext-ciphertext pairs. Thus, essentially as efficiently as for the underlying cipher with ak-bit key. This paper revisits double encryption under the lens of multi-user security. We prove that its security degrades only very mildly with an increasing number of users, as opposed to single encryption, where security drops linearly. More concretely, we give a tight bound for the multi-user security of double encryption as a pseudorandom permutation in the ideal-cipher model, and describe matching attacks. Our contribution is also conceptual: To prove our result, we enhance and generalize the generic technique recently proposed by <PERSON><PERSON> and <PERSON> for lifting single-user to multi-user security. We believe this technique to be broadly applicable.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_13"}, {"primary_key": "3763306", "vector": [], "sparse_vector": [], "title": "Adaptive Partitioning.", "authors": ["<PERSON>"], "summary": "We present a new strategy for partitioning proofs, and use it to obtain new tightly secure encryption schemes. Specifically, we provide the following two conceptual contributions: A new strategy for tight security reductions that leads to compact public keys and ciphertexts. A relaxed definition of non-interactive proof systems for non-linear (“OR-type”) languages. Our definition is strong enough to act as a central tool in our new strategy to obtain tight security, and is achievable both in pairing-friendly and DCR groups. We apply these concepts in a generic construction of a tightly secure public-key encryption scheme. When instantiated in different concrete settings, we obtain the following: A public-key encryption scheme whose chosen-ciphertext security can be tightly reduced to the DLIN assumption in a pairing-friendly group. Ciphertexts, public keys, and system parameters contain\\(6\\),\\(24\\), and\\(2\\)group elements, respectively. This improves heavily upon a recent scheme of <PERSON> et al. (Eurocrypt 2016) in terms of public key size, at the cost of using a symmetric pairing. The first public-key encryption scheme that is tightly chosen-ciphertext secure under the DCR assumption. While the scheme is not very practical (ciphertexts carry\\(28\\)group elements), it enjoys constant-size parameters, public keys, and ciphertexts.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_17"}, {"primary_key": "3763307", "vector": [], "sparse_vector": [], "title": "Conditional Cube Attack on Reduced-Round Keccak Sponge Function.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhao"], "summary": "The security analysis of <PERSON><PERSON><PERSON>, the winner of SHA-3, has attracted considerable interest. Recently, some attention has been paid to the analysis of keyed modes of Keccak sponge function. As a notable example, the most efficient key recovery attacks on Keccak-MAC and Keyak were reported at EUROCRYPT’15 where cube attacks and cube-attack-like cryptanalysis have been applied. In this paper, we develop a new type of cube distinguisher, theconditional cube tester, for Keccak sponge function. By imposing some bit conditions for certain cube variables, we are able to construct cube testers with smaller dimensions. Our conditional cube testers are used to analyse Keccak in keyed modes. For reduced-round Keccak-MAC and Keyak, our attacks greatly improve the best known attacks in key recovery in terms of the number of rounds or the complexity. Moreover, our new model can also be applied to keyless setting to distinguish Keccak sponge function from random permutation. We provide a searching algorithm to produce the most efficient conditional cube tester by modeling it as an MILP (mixed integer linear programming) problem. As a result, we improve the previous distinguishing attacks on Keccak sponge function significantly. Most of our attacks have been implemented and verified by desktop computers. Finally we remark that our attacks on the reduced-round Keccak will not threat the security margin of Keccak sponge function.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_9"}, {"primary_key": "3763308", "vector": [], "sparse_vector": [], "title": "Boolean Searchable Symmetric Encryption with Worst-Case Sub-linear Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent work on searchable symmetric encryption (SSE) has focused on increasing its expressiveness. A notable example is the OXT construction (<PERSON> et al.,CRYPTO ’13) which is the first SSE scheme to support conjunctive keyword queries with sub-linear search complexity. While OXT efficiently supports disjunctive and boolean queries that can be expressed in searchable normal form, it can only handlearbitrarydisjunctive and boolean queries in linear time. This motivates the problem of designing expressive SSE schemes withworst-casesub-linear search; that is, schemes that remain highly efficient foranykeyword query. In this work, we address this problem and propose non-interactive highly efficient SSE schemes that handlearbitrarydisjunctive and boolean queries with worst-case sub-linear search and optimal communication complexity. Our main construction, called IEX, makes black-box use of an underlying single keyword SSE scheme which we can instantiate in various ways. Our first instantiation, IEX-2Lev, makes use of the recent 2Lev construction (<PERSON> et al.,NDSS ’14) and is optimized for search at the expense of storage overhead. Our second instantiation, IEX-ZMF, relies on a new single keyword SSE scheme we introduce called ZMF and is optimized for storage overhead at the expense of efficiency (while still achieving asymptotically sub-linear search). Our ZMF construction is the first adaptively-secure highly compact SSE scheme and may be of independent interest. At a very high level, it can be viewed as an encrypted version of a new Bloom filter variant we refer to as a Matryoshka filter. In addition, we show how to extend IEX to be dynamic and forward-secure. To evaluate the practicality of our schemes, we designed and implemented a new encrypted search framework calledClusion. Our experimental results demonstrate the practicality of IEX and of its instantiations with respect to either search (for IEX-2Lev) and storage overhead (for IEX-ZMF).", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_4"}, {"primary_key": "3763309", "vector": [], "sparse_vector": [], "title": "Revisiting Lattice Attacks on Overstretched NTRU Parameters.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In 2016, <PERSON><PERSON>, <PERSON> and <PERSON> and independently <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> presented very similar attacks to break the NTRU cryptosystem with larger modulus than in theNTRUEncryptstandard. They allow to recover the secret key given the public key of Fully Homomorphic Encryption schemes based on NTRU ideas. Hopefully, these attacks do not endanger the security of theNTRUEncrypt, but shed new light on the hardness of the NTRU problem. The idea consists in decreasing the dimension of the NTRU lattice using the multiplication matrix by the norm (resp. trace) of the public key in some subfield instead of the public key itself. Since the dimension of the subfield is smaller, so is the dimension of the lattice and better lattice reduction algorithms perform. In this paper, we first propose a new variant of the subfield attacks that outperforms both of these attacks in practice. It allows to break several concrete instances of YASHE, a NTRU-based FHE scheme, but it is not as efficient as the hybrid method on smaller concrete parameters ofNTRUEncrypt. Instead of using the norm and trace, the multiplication by the public key in a subring allows to break smaller parameters and we show that in\\(\\mathbb {Q}(\\zeta _{2^n})\\), the time complexity is polynomial for\\(q=2^{\\mathrm {\\Omega }(\\sqrt{n\\log \\log n})}\\). Then, we revisit the lattice reduction part of the hybrid attack of <PERSON><PERSON><PERSON> and analyze the success probability of this attack using a new technical tool proposed by <PERSON><PERSON> and <PERSON>. We show that, under some heuristics, this attack is more efficient than the subfield attack and works in any ring for largeq, such as the NTRU Prime ring. We insist that the improvement on the analysis applies even for relatively small modulus; although if the secret is sparse, it may not be the fastest attack. We also derive a tight estimation of security for (Ring-) LWE and NTRU assumptions and perform many practical experiments.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_1"}, {"primary_key": "3763310", "vector": [], "sparse_vector": [], "title": "Computation of a 768-Bit Prime Field Discrete Logarithm.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper reports on the number field sieve computation of a 768-bit prime field discrete logarithm, describes the different parameter optimizations and resulting algorithmic changes compared to the factorization of a 768-bit RSA modulus, and briefly discusses the cryptologic relevance of the result.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_7"}, {"primary_key": "3763311", "vector": [], "sparse_vector": [], "title": "Twisted μ4-Normal Form for Elliptic Curves.", "authors": ["<PERSON>"], "summary": "We introduce the twisted\\(\\varvec{\\mu }_4\\)-normal form for elliptic curves, deriving in particular addition algorithms with complexity\\(9{\\mathbf {M}}+ 2{\\mathbf {S}}\\)and doubling algorithms with complexity\\(2{\\mathbf {M}}+ 5{\\mathbf {S}}+ 2{\\mathbf {m}}\\)over a binary field. Every ordinary elliptic curve over a finite field of characteristic 2 is isomorphic to one in this family. This improvement to the addition algorithm, applicable to a larger class of curves, is comparable to the\\(7{\\mathbf {M}}+ 2{\\mathbf {S}}\\)achieved for the\\(\\varvec{\\mu }_4\\)-normal form, and replaces the previously best known complexity of\\(13{\\mathbf {M}}+ 3{\\mathbf {S}}\\)on López-Dahab models applicable to these twisted curves. The derived doubling algorithm is essentially optimal, without any assumption of special cases. We show moreover that the Montgomery scalar multiplication with point recovery carries over to the twisted models, giving symmetric scalar multiplication adapted to protect against side channel attacks, with a cost of\\(4{\\mathbf {M}}+ 4{\\mathbf {S}}+ 1{\\mathbf {m}}_t + 2{\\mathbf {m}}_c\\)per bit. In characteristic different from 2, we establish a linear isomorphism with the twisted Edwards model over the base field. This work, in complement to the introduction of\\(\\varvec{\\mu }_4\\)-normal form, fills the lacuna in the body of work on efficient arithmetic on elliptic curves over binary fields, explained by this common framework for elliptic curves in\\(\\varvec{\\mu }_4\\)-normal form over a field of any characteristic. The improvements are analogous to those which the Edwards and twisted Edwards models achieved for elliptic curves over finite fields of odd characteristic and extend\\(\\varvec{\\mu }_4\\)-normal form to cover the binary NIST curves.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_23"}, {"primary_key": "3763312", "vector": [], "sparse_vector": [], "title": "From Minicrypt to Obfustopia via Private-Key Functional Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Private-key functional encryption enables fine-grained access to symmetrically-encrypted data. Although private-key functional encryption (supporting an unbounded number of keys and ciphertexts) seems significantly weaker than its public-key variant, its known realizations all rely on public-key functional encryption. At the same time, however, up until recently it was not known to imply any public-key primitive, demonstrating our poor understanding of this extremely-useful primitive. Recently, <PERSON><PERSON><PERSON> et al. [TCC ’16B] showed that sub-exponentially-secure private-key function encryption bridges from nearly-exponential security in Minicrypt to slightly super-polynomial security in Cryptomania, and from sub-exponential security in Cryptomania to Obfustopia. Specifically, given any sub-exponentially-secure private-key functional encryption scheme and a nearly-exponentially-secure one-way function, they constructed a public-key encryption scheme with slightly super-polynomial security. Assuming, in addition, a sub-exponentially-secure public-key encryption scheme, they then constructed an indistinguishability obfuscator. We show that quasi-polynomially-secure private-key functional encryption bridges from sub-exponential security in Minicrypt all the way to Cryptomania. First, given any quasi-polynomially-secure private-key functional encryption scheme, we construct an indistinguishability obfuscator for circuits with inputs of poly-logarithmic length. Then, we observe that such an obfuscator can be used to instantiate many natural applications of indistinguishability obfuscation. Specifically, relying on sub-exponentially-secure one-way functions, we show that quasi-polynomially-secure private-key functional encryption implies not just public-key encryption but leads all the way to public-key functional encryption for circuits with inputs of poly-logarithmic length. Moreover, relying on sub-exponentially-secure injective one-way functions, we show that quasi-polynomially-secure private-key functional encryption implies a hard-on-average distribution over instances of a PPAD-complete problem. Underlying our constructions is a new transformation from single-input functional encryption to multi-input functional encryption in the private-key setting. The previously known such transformation [Brakerski et al., EUROCRYPT ’16] required a sub-exponentially-secure single-input scheme, and obtained a scheme supporting only a slightly super-constant number of inputs. Our transformation both relaxes the underlying assumption and supports more inputs: Given any quasi-polynomially-secure single-input scheme, we obtain a scheme supporting a poly-logarithmic number of inputs.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_5"}, {"primary_key": "3763313", "vector": [], "sparse_vector": [], "title": "One-Shot Verifiable Encryption from Lattices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Verifiable encryption allows one to prove properties about encrypted data and is an important building block in the design of cryptographic protocols, e.g., group signatures, key escrow, fair exchange protocols, etc. Existing lattice-based verifiable encryption schemes, and even just proofs of knowledge of the encrypted data, require parallel composition of proofs to reduce the soundness error, resulting in proof sizes that are only truly practical when amortized over a large number of ciphertexts. In this paper, we present a new construction of a verifiable encryption scheme, based on the hardness of the Ring-LWE problem in the random-oracle model, for short solutions to linear equations over polynomial rings. Our scheme is “one-shot”, in the sense that a single instance of the proof already has negligible soundness error, yielding compact proofs even for individual ciphertexts. Whereas verifiable encryption usually guarantees that decryption can recover a witness for the original language, we relax this requirement to decrypt a witness of a related but extended language. This relaxation is sufficient for many applications and we illustrate this with example usages of our scheme in key escrow and verifiably encrypted signatures. One of the interesting aspects of our construction is that the decryption algorithm is probabilistic and uses the proof as input (rather than using only the ciphertext). The decryption time for honestly-generated ciphertexts only depends on the security parameter, while theexpectedrunning time for decrypting an adversarially-generated ciphertext is directly related to the number of random-oracle queries of the adversary who created it. This property suffices in most practical scenarios, especially in situations where the ciphertext proof is part of an interactive protocol, where the decryptor is substantially more powerful than the adversary, or where adversaries can be otherwise discouraged to submit malformed ciphertexts.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_11"}, {"primary_key": "3763314", "vector": [], "sparse_vector": [], "title": "Non-interactive Secure 2PC in the Offline/Online and Batch Settings.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In cut-and-choose protocols for two-party secure computation (2PC) the main overhead is the number of garbled circuits that must be sent. Recent work (<PERSON><PERSON> and <PERSON><PERSON>; <PERSON> et al. Crypto 2014) has shown that in a batched setting, when the parties plan to evaluate the same functionNtimes, the number of garbled circuits per execution can be reduced by a\\(O(\\log N)\\)factor compared to the single-execution setting. This improvement is significant in practice: an order of magnitude forNas low as one thousand. Besides the number of garbled circuits, communication round trips are another significant performance bottleneck. <PERSON><PERSON><PERSON> et al. (Eurocrypt 2014) proposed an efficient cut-and-choose 2PC that is round-optimal (one message from each party), but in the single-execution setting. In this work we present new malicious-secure 2PC protocols that are round-optimal and also take advantage of batching to reduce cost. Our contributions include: A 2-message protocol for batch secure computation (Ninstances of the same function). The number of garbled circuits is reduced by a\\(O(\\log N)\\)factor over the single-execution case. However, other aspects of the protocol that depend on the input/output size of the function do not benefit from the same\\(O(\\log N)\\)-factor savings. A 2-message protocol for batch secure computation, in the random oracle model. All aspects of this protocol benefit from the\\(O(\\log N)\\)-factor improvement, except for small terms that do not depend on the function being evaluated. A protocol in the offline/online setting. After an offline preprocessing phase that depends only on the functionfandN, the parties can securely evaluatef,Ntimes (not necessarily all at once). Our protocol’s online phase is only 2 messages, and the total online communication is only\\(\\ell + O(\\kappa )\\)bits, where\\(\\ell \\)is the input length offand\\(\\kappa \\)is a computational security parameter. This is only\\(O(\\kappa )\\)bits more than the information-theoretic lower bound for malicious 2PC.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_15"}, {"primary_key": "3763315", "vector": [], "sparse_vector": [], "title": "Sublinear Zero-Knowledge Arguments for RAM Programs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We describe a new succinct zero-knowledge argument protocol with the following properties. The prover commits to a large data-setM, and can thereafter prove many statements of the form\\(\\exists w : \\mathcal {R}_i(M,w)=1\\), where\\(\\mathcal {R}_i\\)is a public function. The protocol issuccinctin the sense that the cost for the verifier (in computation & communication) does not depend on |M|, not even in any initialization phase In each proof, the computation/communication cost forboththe prover and the verifier is proportional only to the running time of an oblivious RAM program implementing\\(\\mathcal {R}_i\\)(in particular, this can be sublinear in |M|). The only costs that scale with |M| are the computational costs of the prover in a one-time initial commitment toM. Known sublinear zero-knowledge proofs either require an initialization phase where the work of the verifier is proportional to |M| and are therefore sublinear only in an amortized sense, or require that the computational cost for the prover is proportional to |M| uponeach proof. Our protocol uses efficient crypto primitives in a black-box way and is UC-secure in theglobal, non-programmable random oracle, hence it does not rely on any trusted setup assumption.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_18"}, {"primary_key": "3763316", "vector": [], "sparse_vector": [], "title": "Analysis of the Blockchain Protocol in Asynchronous Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>’s famousblockchainprotocol enables achieving consensus in a so-calledpermissionless setting—anyone can join (or leave) the protocol execution, and the protocol instructions do not depend on the identities of the players. His ingenious protocol prevents “sybil attacks” (where an adversary spawns any number of new players) by relying oncomputational puzzles(a.k.a. “moderately hard functions”) introduced by <PERSON><PERSON> and <PERSON><PERSON> (Crypto’92). The analysis of the blockchain consensus protocol (a.k.a. Nakamoto consensus) has been a notoriously difficult task. Prior works that analyze it either make the simplifying assumption that network channels arefully synchronous(i.e. messages are instantly deliveredwithout delays) (Garayet al.Eurocrypt’15) or only consider specific attacks (<PERSON><PERSON><PERSON>’08; <PERSON><PERSON><PERSON> and <PERSON><PERSON>ar, FinancialCrypt’15); additionally, as far as we know, none of them deal with players joining or leaving the protocol. In this work we prove that the blockchain consensus mechanism satisfies a strong forms ofconsistencyandlivenessin anasynchronous networkwith adversarial delays that area-prioribounded, within a formal model allowing for adaptive corruption and spawning of new players, assuming that the computational puzzle is modeled as a random oracle. (We complement this result by showing a simple attack against the blockchain protocol in a fully asynchronous setting, showing that the “puzzle-hardness” needs to be appropriately set as a function of the maximum network delay; this attack applies even for static corruption.) As an independent contribution, we define an abstract blockchain protocol and identify appropriate security properties of such protocols; we prove that Nakamoto’s blockchain protocol satisfies them and that these properties are sufficient for typical applications; we hope that this abstraction may simplify further applications of blockchains.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_22"}, {"primary_key": "3763317", "vector": [], "sparse_vector": [], "title": "Formal Abstractions for Attested Execution Secure Processors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Realistic secure processors, including those built for academic and commercial purposes, commonly realize an “attested execution” abstraction. Despite being thede factostandard for modern secure processors, the “attested execution” abstraction has not received adequate formal treatment. We provide formal abstractions for “attested execution” secure processors and rigorously explore its expressive power. Our explorations show both the expected and the surprising. On one hand, we show that just like the common belief, attested execution is extremely powerful, and allows one to realize powerful cryptographic abstractions such as stateful obfuscation whose existence is otherwise impossible even when assuming virtual blackbox obfuscation andstatelesshardware tokens. On the other hand, we show that surprisingly, realizingcomposabletwo-party computation with attested execution processors is not as straightforward as one might anticipate. Specifically, only when both parties are equipped with a secure processor can we realize composable two-party computation. If one of the parties does not have a secure processor, we show that composable two-party computation is impossible. In practice, however, it would be desirable to allow multiple legacy clients (without secure processors) to leverage a server’s secure processor to perform a multi-party computation task. We show how to introduce minimal additional setup assumptions to enable this. Finally, we show thatfairmulti-party computation for general functionalities is impossible if secure processors do not have trusted clocks. When secure processors have trusted clocks, we can realize fair two-party computation if both parties are equipped with a secure processor; but if only one party has a secure processor (with a trusted clock), then fairness is still impossible for general functionalities.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_10"}, {"primary_key": "3763318", "vector": [], "sparse_vector": [], "title": "Quantum Authentication with Key Recycling.", "authors": ["<PERSON>"], "summary": "We show that a family of quantum authentication protocols introduced in [<PERSON><PERSON> et al., FOCS 2002] can be used to construct a secure quantum channel and additionally recycle all of the secret key if the message is successfully authenticated, and recycle part of the key if tampering is detected. We give a full security proof that constructs the secure channel given only insecure noisy channels and a shared secret key. We also prove that the number of recycled key bits is optimal for this family of protocols, i.e., there exists an adversarial strategy to obtain all non-recycled bits. Previous works recycled less key and only gave partial security proofs, since they did not consider all possible distinguishers (environments) that may be used to distinguish the real setting from the ideal secure quantum channel and secret key resource.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_12"}, {"primary_key": "3763319", "vector": [], "sparse_vector": [], "title": "New Collision Attacks on Round-Reduced Keccak.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Mei<PERSON> Liu", "<PERSON><PERSON>"], "summary": "In this paper, we focus on collision attacks againstKeccakhash function family and some of its variants. Following the framework developed by <PERSON><PERSON><PERSON> al.at FSE 2012 where 4-round collisions were found by combining 3-round differential trails and 1-round connectors, we extend the connectors one round further hence achieve collision attacks for up to 5 rounds. The extension is possible thanks to the large degree of freedom of the wide internal state. By linearization of all S-boxes of the first round, the problem of finding solutions of 2-round connectors are converted to that of solving a system of linear equations. However, due to the quick freedom reduction from the linearization, the system has solution only when the 3-round differential trails satisfy some additional conditions. We develop a dedicated differential trail search strategy and find such special differentials indeed exist. As a result, the first practical collision attack against 5-roundSHAKE128and two 5-round instances of theKeccakcollision challenges are found with real examples. We also give the first results against 5-roundKeccak-224 and 6-roundKeccakcollision challenges. It is remarked that the work here is still far from threatening the security of the full 24-roundKeccakfamily.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_8"}, {"primary_key": "3763320", "vector": [], "sparse_vector": [], "title": "Improved Private Set Intersection Against Malicious Adversaries.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Private set intersection (PSI) refers to a special case of secure two-party computation in which the parties each have a set of items and compute the intersection of these sets without revealing any additional information. In this paper we present improvements to practical PSI providing security in the presence ofmaliciousadversaries. Our starting point is the protocol of <PERSON>, <PERSON> & <PERSON> (CCS 2013) that is based on Bloom filters. We identify a bug in their malicious-secure variant and show how to fix it using a cut-and-choose approach that has low overhead while simultaneously avoiding one the main computational bottleneck in their original protocol. We also point out some subtleties that arise when using Bloom filters in malicious-secure cryptographic protocols. We have implemented our PSI protocols and report on its performance. Our improvements reduce the cost of <PERSON> et al.’s protocol by a factor of\\(14-110{\\times }\\)on a single thread. When compared to the previous fastest protocol of <PERSON> et al., we improve the running time by\\(8-24{\\times }\\). For instance, our protocol has an online time of 14 s and an overall time of 2.1 min to securely compute the intersection of two sets of 1 million items each.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56620-7_9"}, {"primary_key": "3763321", "vector": [], "sparse_vector": [], "title": "New Impossible Differential Search Tool from Design and Cryptanalysis Aspects - Revealing Structural Properties of Several Ciphers.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, a new tool searching for impossible differentials is presented. Our tool can detect any contradiction between input and output differences. It can also take into account the property inside the S-box when its size is small e.g. 4 bits. This is natural for ciphers with bit-wise diffusion like PRESENT, while finding such impossible differentials for ciphers with word-wise diffusion is novel. In addition, several techniques are proposed to evaluate 8-bit S-box. The tool improves the number of rounds of impossible differentials from the previous best results forMidori128,Lilliput, and Minalpher. The tool also finds new impossible differentials for ARIA and MIBS. We manually verify the impossibility of the searched results, which reveals new structural properties of those designs. The tool can be implemented by slightly modifying the previous differential search tool using Mixed Integer Linear Programming (MILP). This motivates us to discuss the usage of our tool particular for the design process. With this tool, the maximum number of rounds of impossible differentials can be proven under reasonable assumptions and the tool is applied to various concrete designs.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_7"}, {"primary_key": "3763322", "vector": [], "sparse_vector": [], "title": "Public-Seed Pseudorandom Permutations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper initiates the study of standard-model assumptions on permutations – or more precisely, on families of permutations indexed by apublicseed. We introduce and study the notion of apublic-seed pseudorandom permutation(psPRP), which is inspired by the UCE notion by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> (CRYPTO ’13). It considers a two-stage security game, where the first-stage adversary is known as the source, and is restricted to prevent trivial attacks – the security notion is consequently parameterized by the class of allowable sources. To this end, we define in particular unpredictable and reset-secure sources analogous to similar notions for UCEs. We first study the relationship between psPRPs and UCEs. To start with, we provide efficient constructions of UCEs from psPRPs for both reset-secure and unpredictable sources, thus showing that most applications of the UCE framework admit instantiations from psPRPs. We also show a converse of this statement, namely that the five-round <PERSON><PERSON><PERSON> construction yields a psPRP for reset-secure sources when the round function is built from UCEs for reset-secure sources, hence making psPRP and UCE equivalent notions for such sources. In addition to studying such reductions, we suggest generic instantiations of psPRPs from both block ciphers and (keyless) permutations, and analyze them in ideal models. Also, as an application of our notions, we show that a simple modification of a recent highly-efficient garbling scheme by <PERSON><PERSON> et al. (S&P ’13) is secure under our psPRP assumption.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_14"}, {"primary_key": "3763323", "vector": [], "sparse_vector": [], "title": "Small CRT-Exponent RSA Revisited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Since May (Crypto’02) revealed the vulnerability of the small CRT-exponent RSA using <PERSON><PERSON>’s lattice-based method, several papers have studied the problem and two major improvements have been made. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> (PKC’06) proposed an attack for small\\(d_q\\)when the prime factorpis significantly smaller than the other prime factorq; the attack works for\\(p<N^{0.468}\\). <PERSON><PERSON><PERSON><PERSON> and <PERSON> (Crypto’07) proposed an attack for small\\(d_p\\)and\\(d_q\\)where the prime factorspandqare balanced; the attack works for\\(d_p,d_q<N^{0.073}\\). Even after a decade has passed since their proposals, the above two attacks are still considered to be the state-of-the-art, and no improvements have been made thus far. A novel technique seems to be required for further improvements since the attacks have been studied with all the applicable techniques for <PERSON><PERSON>’s methods proposed by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Asiacrypt’00), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Asiacrypt’06), and <PERSON><PERSON><PERSON> (Asiacrypt’09, PKC’10). In this paper, we propose two improved attacks on the small CRT-exponent RSA: a small\\(d_q\\)attack for\\(p<N^{0.5}\\)(an improvement of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>’s) and a small\\(d_p\\)and\\(d_q\\)attack for\\(d_p,d_q<N^{0.091}\\)(an improvement of Jochemsz-May’s). We use Coppersmith’s lattice-based method to solve modular equations and obtain the improvements from a novel lattice construction by exploiting useful algebraic structures of the CRT-RSA key generation. We explicitly show proofs of our attacks and verify the validities by computer experiments. In addition to the two main attacks, we propose small\\(d_q\\)attacks on several variants of RSA.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56614-6_5"}, {"primary_key": "3763324", "vector": [], "sparse_vector": [], "title": "Faster Secure Two-Party Computation in the Single-Execution Setting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a new protocol for two-party computation, secure against malicious adversaries, that is significantly faster than prior work in the single-execution setting (i.e., non-amortized and with no pre-processing). In particular, for computational security parameter\\({\\kappa }\\)and statistical security parameter\\({\\rho }\\), our protocol uses only\\({\\rho }\\)garbled circuits and\\(O({\\rho }+ {\\kappa })\\)public-key operations, whereas previous work with the same number of garbled circuits required either\\(O({\\rho }\\cdot n+{\\kappa })\\)public-key operations (wherenis the input/output length) or a second execution of a secure-computation sub-protocol. Our protocol can be based on the decisional <PERSON><PERSON><PERSON>-<PERSON> assumption in the standard model. We implement our protocol to evaluate its performance. With\\({\\rho }=40\\), our implementation securely computes an AES evaluation in 65 ms over a local-area network using a single thread without any pre-computation,\\(22\\times \\)faster than the best prior work in the non-amortized setting. The relative performance of our protocol is even better for functions with larger input/output lengths.", "published": "2017-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-319-56617-7_14"}]