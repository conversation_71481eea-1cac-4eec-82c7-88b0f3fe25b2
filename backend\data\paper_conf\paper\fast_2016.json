[{"primary_key": "4094900", "vector": [], "sparse_vector": [], "title": "BTrDB: Optimizing Storage System Design for Timeseries Processing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The increase in high-precision, high-sample-rate telemetry timeseries poses a problem for existing timeseries databases which can neither cope with the throughput demands of these streams nor provide the necessary primitives for effective analysis of them. We present a novel abstraction for telemetry timeseries data and a data structure for providing this abstraction: a time-partitioning version-annotated copy-on-write tree. An implementation in Go is shown to outperform existing solutions, demonstrating a throughput of 53 million inserted values per second and 119 million queried values per second on a four-node cluster. The system achieves a 2.9x compression ratio and satisfies statistical queries spanning a year of data in under 200ms, as demonstrated on a year-long production deployment storing 2.1 trillion data points. The principles and design of this database are generally applicable to a large variety of timeseries types and represent a significant advance in the development of technology for the Internet of Things.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094902", "vector": [], "sparse_vector": [], "title": "Mitigating Sync Amplification for Copy-on-write Virtual Disk.", "authors": ["Qingshu Chen", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "Copy-on-write virtual disks (e.g.,qcow2images) provide many useful features like snapshot, de-duplication, and full-disk encryption. However, our study uncovers that they introduce additional metadata for block organization\nand notably more disk sync operations (e.g., more than 3X forqcow2and 4X forVMDKimages). To mitigate such sync amplification, we propose three optimizations, namelyper virtual disk internal journaling, dual-mode journaling, andadaptive-preallocation, which eliminate the extra sync operations while preserving those features in a consistent way. Our evaluation shows that the three optimizations result in up to 110% performance speedup forvarmailand 50% for TPCC.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094903", "vector": [], "sparse_vector": [], "title": "OrderMergeDedup: Efficient, Failure-Consistent Deduplication on Flash.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Flash storage is commonplace on mobile devices, sensors, and cloud servers. I/O deduplication is beneficial for saving the storage space and reducing expensive Flash writes. This paper presents a new approach, calledOrderMergeDedup, that deduplicates storage writes while realizing failure-consistency, efficiency, and persistence at the same time. We devise a soft updates-style metadata write ordering that maintains storage data consistency without consistency-induced additional I/O. We further explore opportunities of I/O delay and merging to reduce the metadata I/O writes. We evaluate our Linux device mapper-based implementation using several mobile and server workloads—package installation and update, BBench web browsing, vehicle counting, Hadoop, and Yahoo Cloud Serving Benchmark. Results show that OrderMergeDedup can realize 18–63% write reduction on workloads that exhibit 23– 73% write content duplication. It has significantly less metadata write overhead than alternative I/O shadowingbased deduplication. Our approach has a slight impact on the application latency and may even improve the performance due to reduced I/O load.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094904", "vector": [], "sparse_vector": [], "title": "Uncovering Bugs in Distributed Storage Systems during Testing (Not in Production!).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Alastair F<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Testing distributed systems is challenging due to multiple sources of nondeterminism. Conventional testing techniques, such as unit, integration and stress testing, are ineffective in preventing serious but subtle bugs from reaching production. Formal techniques, such as TLA+, can only verify high-level specifications of systems at the level of logic-based models, and fall short of checking the actual executable code. In this paper, we present a new methodology for testing distributed systems. Our approach applies advanced systematic testing techniques to thoroughly check that the executable code adheres to its high-level specifications, which significantly improves coverage of important system behaviors. Our methodology has been applied to three distributed storage systems in the Microsoft Azure cloud computing platform. In the process, numerous bugs were identified, reproduced, confirmed and fixed. These bugs required a subtle combination of concurrency and failures, making them extremely difficult to find with conventional testing techniques. An important advantage of our approach is that a bug is uncovered in a small setting and witnessed by a full system trace, which dramatically increases the productivity of debugging.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094905", "vector": [], "sparse_vector": [], "title": "The Tail at Store: A Revelation from Millions of Hours of Disk and SSD Deployments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>-<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study storage performance in over 450,000 disks and 4,000 SSDs over 87 days for an overall total of 857 million (disk) and 7 million (SSD) drive hours. We find that storage performance instability is not uncommon:\n0.2% of the time, a disk is more than 2x slower than its peer drives in the same RAID group (and 0.6% for SSD). As a consequence, disk and SSD-based RAIDs experience at least one slow drive (i.e., storage tail) 1.5% and 2.2% of the time. To understand the root causes, we correlate slowdowns with other metrics (workload I/O rate and size, drive event, age, and model). Overall, we find that the primary cause of slowdowns are the internal characteristics and idiosyncrasies of modern disk and SSD drives. We observe that storage tails can adversely impact RAID performance, motivating the design of tail-tolerant RAID. To the best of our knowledge, this work is the most extensive documentation of storage performance instability in the field.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094906", "vector": [], "sparse_vector": [], "title": "Estimating Unseen Deduplication - from Theory to Practice.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Estimating the deduplication ratio of a very large dataset is both extremely useful, but genuinely very hard to perform. In this work we present a new method for accurately estimating deduplication benefits that runs 3X to 15X faster than the state of the art to date. The level of improvement depends on the data itself and on the storage media that it resides on. The technique is based on breakthrough theoretical work by Valiant and Valiant from 2011, that give a provably accurate method for estimating various measures while seeing only a fraction of the data. However, for the use case of deduplication estimation, putting this theory into practice runs into significant obstacles. In this\nwork, we find solutions and novel techniques to enable the use of this new and exciting approach. Our contributions include a novel approach for gauging the estimation accuracy, techniques to run it with low memory consumption, a method to evaluate the combined compression and deduplication ratio, and ways to perform the actual sampling in real storage systems in order to actually reap benefits from these algorithms. We evaluated our work on a number of real world datasets.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094907", "vector": [], "sparse_vector": [], "title": "Slacker: Fast Distribution with Lazy Docker Containers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>au"], "summary": "Containerized applications are becoming increasingly popular, but unfortunately, current container-deployment methods are very slow. We develop a new container benchmark, HelloBench, to evaluate the startup times of 57 different containerized applications. We use HelloBench to analyze workloads in detail, studying the block I/O patterns exhibited during startup and compressibility of container images. Our analysis shows that pulling packages accounts for 76% of container start time, but only 6.4% of that data is read. We use this and other findings to guide the design of Slacker, a new Docker storage driver optimized for fast container startup. Slacker is based on centralized storage that is shared between all Docker workers and registries. Workers quickly provision container storage using backend clones and minimize startup latency by lazily fetching container data. Slacker speeds up the median container development cycle by 20x and deployment cycle by 5x.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094908", "vector": [], "sparse_vector": [], "title": "PCAP: Performance-aware Power Capping for the Disk Drive in the Cloud.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Power efficiency is pressing in today’s cloud systems. Datacenter architects are responding with various strategies, including capping the power available to computing systems. Throttling bandwidth has been proposed to cap the power usage of the disk drive. This work revisits throttling and addresses its shortcomings. We show that, contrary to the common belief, the disk’s power usage does not always increase as the disk’s throughput increases. Furthermore, throttling unnecessarily sacrifices I/O response times by idling the disk. We propose a technique that resizes the queues of the disk to cap its power. Resizing queues not only imposes no delays on servicing\nrequests, but also enables performance differentiation.We present the design and implementation of PCAP, an agile performance-aware power capping system for the disk drive. PCAP dynamically resizes the disk’s queues to cap power. It operates in two performanceaware\nmodes, throughput and tail-latency, making it viable for cloud systems with service-level differentiation. We evaluate PCAP for different workloads and disk drives. Our experiments show that PCAP reduces power by up to 22%. Further, under PCAP, 60% of the requests observe service times below 100 ms compared to just 10% under throttling. PCAP also reduces worst-case latency by 50% and increases throughput by 32% relative to throttling. We present the design and implementation of PCAP, an agile performance-aware power capping system for the disk drive. PCAP dynamically resizes the disk’s queues to cap power. It operates in two performanceaware\nmodes, throughput and tail-latency, making it viable for cloud systems with service-level differentiation. We evaluate PCAP for different workloads and disk drives. Our experiments show that PCAP reduces power by up to 22%. Further, under PCAP, 60% of the requests observe service times below 100 ms compared to just 10% under throttling. PCAP also reduces worst-case latency by 50% and increases throughput by 32% relative to throttling. We present the design and implementation of PCAP, an agile performance-aware power capping system for the disk drive. PCAP dynamically resizes the disk’s queues to cap power. It operates in two performanceaware\nmodes, throughput and tail-latency, making it viable for cloud systems with service-level differentiation. We evaluate PCAP for different workloads and disk drives. Our experiments show that PCAP reduces power by up to 22%. Further, under PCAP, 60% of the requests observe service times below 100 ms compared to just 10% under throttling. PCAP also reduces worst-case latency by 50% and increases throughput by 32% relative to throttling.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094909", "vector": [], "sparse_vector": [], "title": "Application-Managed Flash.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In flash storage, an FTL is a complex piece of code that resides completely inside the storage device and is provided by the manufacturer. Its principal virtue is providing interoperability with conventional HDDs. However, this virtue is also its biggest impediment in reaching the full performance of the underlying flash storage. We propose to refactor the flash storage architecture so that it relies on a new block I/O interface which does not permit overwriting of data without intervening erasures. We demonstrate how high-level applications, in particular file systems, can deal with this restriction efficiently by employingappend-onlysegments. This refactoring dramatically reduces flash management overhead and improves performance of applications, such as file systems and databases, by permitting them to directly manage flash storage. Our experiments on a machine with the new block I/O interface show that DRAM in the flash controller is reduced by 128x and the performance of the file system improves by 80% over conventional SSDs.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094910", "vector": [], "sparse_vector": [], "title": "Flamingo: Enabling Evolvable HDD-based Near-Line Storage.", "authors": ["<PERSON>", "Xiaozhou Li", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cloud providers and companies running large-scale data centers offer near-line, cold, and archival data storage, which trade access latency and throughput performance for cost. These often require physical rack-scale storage\ndesigns, e.g. Facebook/Open Compute Project (OCP) Cold Storage or Pelican, which co-design the hardware, mechanics, power, cooling and software to minimize costs to support the desired workload. A consequence is that the rack resources are restricted, requiring a software stack that can operate within the provided resources. The co-design makes it hard to understand the end-to-end performance impact of relatively small physical design changes and, worse, the software stacks are brittle to these changes. Flamingo supports the design of near-line HDD-based storage racks for cloud services. It requires a physical rack design, a set of resource constraints, and some target performance characteristics. Using these Flamingo is able to automatically parameterize a generic storage stack to allow it to operate on the physical rack. It is also able to efficiently explore the performance impact of varying the rack resources. It incorporates key principles learned from the design and deployment of cold storage systems. We demonstrate that Flamingo can rapidly reduce the time taken to design custom racks to support near-line storage.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094911", "vector": [], "sparse_vector": [], "title": "CacheDedup: In-line Deduplication for Flash Caching.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Flash caching has emerged as a promising solution to the scalability problems of storage systems by using fast flash memory devices as the cache for slower primary storage. But its adoption faces serious obstacles due to the limited capacity and endurance of flash devices. This paper presents CacheDedup, a solution that addresses these limitations using in-line deduplication. First, it proposes a novel architecture that integrates the caching of data and deduplication metadata (source addresses and fingerprints of the data) and efficiently manages these two components. Second, it proposes duplication-aware cache replacement algorithms (D-LRU, DARC) to optimize both cache performance and endurance. The paper presents a rigorous analysis of the algorithms to prove that they do not waste valuable cache space and that they are efficient in time and space usage. The paper also includes an experimental evaluation using real-world traces, which confirms that CacheDedup substantially improves I/O performance (up to 20% reduction in miss ratio and 51% in latency) and flash endurance (up to 89% reduction in writes\nsent to the cache device) compared to traditional cache management. It also shows that the proposed architecture and algorithms can be extended to support the combination of compression and deduplication for flash caching and improve its performance and endurance.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094912", "vector": [], "sparse_vector": [], "title": "Access Characteristic Guided Read and Write Cost Regulation for Performance Improvement on Flash Memory.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Qingfeng Zhuge", "<PERSON>-<PERSON>"], "summary": "The relatively high cost of write operations has become the performance bottleneck of flash memory. Write cost refers to the time needed to program a flash page using incremental-step pulse programming (ISPP), while read cost refers to the time needed to sense and transfer a page from the storage. If a flash page is written with a higher cost by using a finer step size during the ISPP process, it can be read with a relatively low cost due to the time\nsaved in sensing and transferring, and vice versa. We introduce AGCR, an access characteristic guided cost regulation scheme that exploits this tradeoff to improve flash performance. Based on workload characteristics, logical pages receiving more reads will be written using a finer step size so that their read cost is reduced. Similarly, logical pages receiving more writes will be written using a coarser step size so that their write cost is reduced. Our evaluation shows that AGCR incurs negligible overhead, while improving performance by 15% on average, compared to previous approaches.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094913", "vector": [], "sparse_vector": [], "title": "Towards Accurate and Fast Evaluation of Multi-Stage Log-structured Designs.", "authors": ["Hyeontaek Lim", "<PERSON>", "<PERSON>"], "summary": "Multi-stage log-structured (MSLS) designs, such as LevelDB, RocksDB, HBase, and Cassandra, are a family of storage system designs that exploit the high sequential write speeds of hard disks and flash drives by using multiple append-only data structures. As a first step towards accurate and fast evaluation of MSLS, we propose new analytic primitives and MSLS design models that quickly give accurate performance estimates. Our model can almost perfectly estimate the cost of inserts in LevelDB, whereas the conventional worst-case analysis gives 1.8–3.5x higher estimates than the actual cost. A few minutes of offline analysis using our model can find optimized system parameters that decrease LevelDB’s insert cost by up to 9.4–26.2%; our analytic primitives and model also suggest changes to RocksDB that reduce its insert cost by up to 32.0%, without reducing query performance or requiring extra memory.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094914", "vector": [], "sparse_vector": [], "title": "WiscKey: Separating Keys from Values in SSD-conscious Storage.", "authors": ["Lanyue Lu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>au"], "summary": "We present WiscKey, a persistent LSM-tree-based key-value store with a performance-oriented data layout that separates keys from values to minimize I/O amplification. The design of WiscKey is highly SSD optimized,\nleveraging both the sequential and random performance characteristics of the device. We demonstrate the advantages of WiscKey with both microbenchmarks and YCSB workloads. Microbenchmark results show that\nWiscKey is 2.5x–111x faster than LevelDB for loading a database and 1.6x–14x faster for random lookups. WiscKey is faster than both LevelDB and RocksDB in all six YCSB workloads.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094915", "vector": [], "sparse_vector": [], "title": "Using Hints to Improve Inline Block-layer Deduplication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Block-layer data deduplication allows file systems and applications to reap the benefits of deduplication without requiring per-system or per-application modifications. However, important information about data context (e.g., data vs. metadata writes) is lost at the block layer. Passing such context to the block layer can help improve deduplication performance and reliability. We implemented a hinting interface in an open-source block-layer deduplication system,dmdedup, that passes relevant context to the block layer, and evaluated two hints,NODEDUPandPREFETCH. To allow upper storage layers to pass hints based on the available context, we modified the VFS and file system layers to expose a hinting interface to user applications. We show that passing theNODEDUPhint speeds up applications by up to 5.3\u0002 on modern machines because the overhead of deduplication is avoided when it is unlikely to be beneficial. We also show that thePREFETCHhint accelerates applications up to 1.8\u0002 by caching hashes for data that is likely to be accessed soon.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094916", "vector": [], "sparse_vector": [], "title": "Environmental Conditions and Disk Reliability in Free-cooled Datacenters.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "Free cooling lowers datacenter costs significantly, but may also expose servers to higher and more variable temperatures and relative humidities. It is currently unclear whether these environmental conditions have a significant impact on hardware component reliability. Thus, in this paper, we use data from nine hyperscale datacenters to study the impact of environmental conditions on the reliability of server hardware, with a particular focus on disk drives and free cooling. Based on this study, we derive and validate a new model of disk lifetime as a function of environmental conditions. Furthermore, we quantify the tradeoffs between energy consumption, environmental conditions, component reliability, and datacenter costs. Finally, based on our analyses and model, we derive server and datacenter design lessons. We draw many interesting observations, including (1) relative humidity seems to have a dominant impact on component failures; (2) disk failures increase significantly when operating at high relative humidity, due to controller/adaptor malfunction; and (3) though higher relative humidity increases component failures, software availability techniques can mask them and enable free-cooled operation, resulting in significantly lower infrastructure and energy costs that far outweigh the cost of the extra component failures.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094917", "vector": [], "sparse_vector": [], "title": "The Devil Is in the Details: Implementing Flash Page Reuse with WOM Codes.", "authors": ["<PERSON><PERSON><PERSON>", "Gala Yadgar", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Flash memory is prevalent in modern servers and devices. Coupled with the scaling down of flash technology, the popularity of flash memory motivates the search for methods to increase flash reliability and lifetime. Erasures are the dominant cause of flash cell wear, but reducing them is challenging because flash is awrite-oncemedium—memory cells must be erased prior to writing. An approach that has recently received considerable attention relies onwrite-once memory (WOM)codes, designed to accommodate additional writes on write-once media. However, the techniques proposed for reusing flash pages with WOM codes are limited in their scope. Many focus on the coding theory alone, while others suggest FTL designs that are application specific, or not applicable due to their complexity, overheads, or specific constraints of MLC flash. This work is the first that addresses all aspects of page reuse within an end-to-end implementation of a general-purpose FTL on MLC flash. We use our hardware implementation to directly measure the short and long-term\neffects of page reuse on SSD durability, I/O performance and energy consumption, and show that FTL design must explicitly take them into account.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094918", "vector": [], "sparse_vector": [], "title": "Opening the Chrysalis: On the Real Repair Performance of MSR Codes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Large distributed storage systems use erasure codes to reliably store data. Compared to replication, erasure codes are capable of reducing storage overhead. However, repairing lost data in an erasure coded system requires reading from many storage devices and transferring over the network large amounts of data. Theoretically, Minimum Storage Regenerating (MSR) codes can significantly reduce this repair burden. Although several explicit MSR code constructions exist, they have not been implemented in real-world distributed storage systems. We close this gap by providing a performance analysis of Butterfly codes, systematic MSR codes with optimal repair I/O. Due to the complexity of modern distributed systems, a straightforward approach does not exist when it comes to implementing MSR codes. Instead, we show that achieving good performance requires to vertically integrate the code with multiple system layers. The encoding approach, the type of inter-node communication, the interaction between different distributed system layers, and even the programming language have a significant impact on the code repair performance. We show that with new distributed system features, and careful implementation, we can achieve the theoretically expected repair performance of MSR codes.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094919", "vector": [], "sparse_vector": [], "title": "Flash Reliability in Production: The Expected and the Unexpected.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As solid state drives based on flash technology are becoming a staple for persistent data storage in data centers, it is important to understand their reliability characteristics. While there is a large body of work based on experiments with individual flash chips in a controlled lab environment under synthetic workloads, there is a dearth of information on their behavior in the field. This paper provides a large-scale field study covering many millions\nof drive days, ten different drive models, different flash technologies (MLC, eMLC, SLC) over 6 years of production use in Google’s data centers. We study a wide range of reliability characteristics and come to a number of unexpected conclusions. For example, raw bit error rates (RBER) grow at a much slower rate with wear-out than the exponential rate commonly assumed and, more importantly, they are not predictive of uncorrectable errors or other error modes. The widely used metric UBER (uncorrectable bit error rate) is not a meaningful metric, since we see no correlation between the number of reads and the number of uncorrectable errors. We see no evidence\nthat higher-end SLC drives are more reliable than MLC drives within typical drive lifetimes. Comparing with traditional hard disk drives, flash drives have a significantly lower replacement rate in the field, however, they have a higher rate of uncorrectable errors.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094920", "vector": [], "sparse_vector": [], "title": "Isotope: Transactional Isolation for Block Storage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tudor Marian", "Hakim Weatherspoon"], "summary": "Existing storage stacks are top-heavy and expect little from block storage. As a result, new high-level storage abstractions—and new designs for existing abstractions—are difficult to realize, requiring developers to implement from scratch complex functionality such as failure atomicity and fine-grained concurrency control. In this paper, we argue that pushing transactional isolation into the block store (in addition to atomicity and durability) is both viable and broadly useful, resulting in simpler high-level storage systems that provide strong semantics without sacrificing performance. We present Isotope, a new block store that supports ACID transactions over block reads and writes. Internally, Isotope uses a new multi-version concurrency control protocol that exploits fine-grained, sub-block parallelism in workloads and offers both strict serializability and snapshot isolation guarantees. We implemented several high-level storage systems over Isotope, including two key-value stores that implement the LevelDB API over a hashtable and B-tree, respectively, and a POSIX filesystem. We show that Isotope’s block-level transactions enable systems that are simple (100s of lines of code), robust (i.e., providing ACID guarantees), and fast (e.g., 415 MB/s for random file writes). We also show that these systems can be composed using Isotope, providing applications with transactions across different high-level constructs such as files, directories and key-value pairs.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094921", "vector": [], "sparse_vector": [], "title": "sRoute: Treating the Storage Stack Like a Network.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Greg <PERSON>;<PERSON>", "<PERSON><PERSON>"], "summary": "In a data center, an IO from an application to distributed storage traverses not only the network, but also several software stages with diverse functionality. This set of ordered stages is known as the storage or IO stack. Stages\ninclude caches, hypervisors, IO schedulers, file systems, and device drivers. Indeed, in a typical data center, the number of these stages is often larger than the number of network hops to the destination. Yet, while packet routing\nis fundamental to networks, no notion of IO routing exists on the storage stack. The path of an IO to an endpoint is predetermined and hard-coded. This forces IO with different needs (e.g., requiring different caching or replica selection) to flow through a one-size-fits-all IO stack structure, resulting in an ossified IO stack. This paper proposes sRoute, an architecture that provides a routing abstraction for the storage stack. sRoute comprises a centralized control plane and “sSwitches” on the data plane. The control plane sets the forwarding rules in each sSwitch to route IO requests at runtime based on application-specific policies. A key strength of our architecture is that it works with unmodified applications and VMs. This paper shows significant benefits of customized IO routing to data center tenants (e.g., a factor of ten for tail IO latency, more than 60% better throughput for a customized replication protocol and a factor of two in throughput for customized caching).", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094922", "vector": [], "sparse_vector": [], "title": "NOVA: A Log-structured File System for Hybrid Volatile/Non-volatile Main Memories.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Fast non-volatile memories (NVMs) will soon appear on the processor memory bus alongside DRAM. The resulting hybrid memory systems will provide software with sub-microsecond, high-bandwidth access to persistent data, but managing, accessing, and maintaining consistency for data stored in NVM raises a host of challenges. Existing file systems built for spinning or solid-state disks introduce software overheads that would obscure the performance that NVMs should provide, but proposed file systems for NVMs either incur similar overheads or fail to provide the strong consistency guarantees that applications require. We present NOVA, a file system designed to maximize performance on hybrid memory systems while providing strong consistency guarantees. NOVA adapts conventional log-structured file system techniques to exploit the fast random access that NVMs provide. In particular, it maintains separate logs for each inode to improve concurrency, and stores file data outside the log to minimize log size and reduce garbage collection costs. NOVA’s logs provide metadata, data, and mmap atomicity and focus on simplicity and reliability, keeping complex metadata structures in DRAM to accelerate lookup operations. Experimental results show that in write-intensive workloads, NOVA provides 22% to 216× throughput improvement compared to state-of-the-art file systems, and 3.1× to 13.5× improvement compared to file systems that provide equally strong data consistency guarantees.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094923", "vector": [], "sparse_vector": [], "title": "Optimizing Every Operation in a Write-optimized File System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "File systems that employ write-optimized dictionaries (WODs) can perform random-writes, metadata updates, and recursive directory traversals orders of magnitude faster than conventional file systems. However, previous WOD-based file systems have not obtained all of these performance gains without sacrificing performance on other operations, such as file deletion, file or directory renaming, or sequential writes. Using three techniques,late-binding journaling, zoning, andrange deletion, we show that there is no fundamental trade-off in write-optimization. These dramatic improvements can be retained while matching conventional\nfile systems onallother operations. BetrFS 0.2 delivers order-of-magnitude better performance than conventional file systems on directory scans and small random writes and matches the performance of conventional file systems on rename, delete, and sequential I/O. For example, BetrFS 0.2 performs directory scans 2.2x\u0002 faster, and small random writes over two orders of magnitude faster, than the fastest conventional file system. But unlike BetrFS 0.1, it renames and deletes\nfiles commensurate with conventional file systems and performs large sequential I/O at nearly disk bandwidth. The performance benefits of these techniques extend to applications as well. BetrFS 0.2 continues to outperform\nconventional file systems on many applications, such as asrsync,git-diff, andtar, but improves git-clone performance by 35% over BetrFS 0.1, yielding performance comparable to other file systems.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094924", "vector": [], "sparse_vector": [], "title": "The Composite-file File System: Decoupling the One-to-One Mapping of Files and Metadata for Better Performance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Traditional file system optimizations typically retain the one-to-one mapping of logical files to their physical metadata representations. This rigid mapping results in missed opportunities for an entire class of optimizations in which such coupling is removed. We have designed, implemented, and evaluated a composite-file file system, which allows many-to-one mappings of files to metadata, and we have explored the design space of different mapping strategies. Under webserver and software development workloads, our empirical evaluation shows up to a 27% performance improvement. This result demonstrates the promise of decoupling files and their metadata.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094925", "vector": [], "sparse_vector": [], "title": "Efficient and Available In-memory KV-Store with Hybrid Erasure Coding and Replication.", "authors": ["<PERSON><PERSON>", "Mingkai Dong", "<PERSON><PERSON> Chen"], "summary": "In-memory key/value store (KV-store) is a key building block for many systems like databases and large websites. Two key requirements for such systems are efficiency and availability, which demand a KV-store to continuously\nhandle millions of requests per second. A common approach to availability is using replication such as primary-backup (PBR), which, however, requiresM+1 times memory to tolerateMfailures. This renders scarce memory unable to handle useful user jobs. This paper makes the first case of building highly available in-memory KV-store by integrating erasure coding to achieve memory efficiency, while not notably degrading performance. A main challenge is that an in-memory\nKV-store has much scattered metadata. A single KV put may cause excessive coding operations and parity updates due to numerous small updates to metadata. Our approach, namely Cocytus, addresses this challenge by using a hybrid scheme that leverages PBR for small-sized and scattered data (e.g., metadata and key), while only applying erasure coding to relatively large data (e.g., value). To mitigate well-known issues like lengthy recovery of erasure coding, Cocytus uses an online recovery scheme by leveraging the replicated metadata information to continuously serving KV requests. We have applied Cocytus to Memcached. Evaluation using YCSB with different KV configurations shows that Cocytus incurs low overhead for latency and throughput, can tolerate node failures with fast online recovery, yet saves 33% to 46% memory compared to PBR when tolerating two failures.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}, {"primary_key": "4094926", "vector": [], "sparse_vector": [], "title": "Reducing Solid-State Storage Device Write Stress through Opportunistic In-place Delta Compression.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Inside modern SSDs, a small portion of MLC/TLC NAND flash memory blocks operate in SLC-mode to serve as write buffer/cache and/or store hot data. These SLC-mode blocks absorb a large percentage of write operations. To balance memory wear-out, such MLC/TLC-to-SLC configuration rotates among all the memory blocks inside SSDs. This paper presents a simple yet effective design approach to reduce write stress on SLC-mode flash blocks and hence improve the overall SSD lifetime. The key is to implement well-known delta compression without being subject to the read latency and data management complexity penalties inherent to conventional practice. The underlying theme is to leverage the partial programmability of SLC-mode flash memory pages to ensure that the original data and all the subsequent deltas always reside in the same memory physical page. To avoid the storage capacity overhead, we further propose to combine intra-sector lossless data compression with intra-page delta compression, leading to opportunistic in-place delta compression. This paper presents specific techniques to address important issues for its practical implementation, including data error correction, and intra-page data placement and management. We carried out comprehensive experiments, simulations, and ASIC (application-specific integrated circuit) design. The results show that the proposed design solution can largely reduce the write stress on SLC-mode flash memory pages without significant latency overhead and meanwhile incurs relatively small silicon implementation cost.", "published": "2016-01-01", "category": "fast", "pdf_url": "", "sub_summary": "", "source": "fast", "doi": ""}]