[{"primary_key": "586864", "vector": [], "sparse_vector": [], "title": "Reusable Specification Patterns for Verification of Resilience in Autonomous Hybrid Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "AbstractAutonomous hybrid systems are systems that combine discrete and continuous behavior with autonomous decision-making, e.g., using reinforcement learning. Such systems are increasingly used in safety-critical applications such as self-driving cars, autonomous robots or water supply systems. Thus, it is crucial to ensure their safety and resilience, i.e., that they function correctly even in the presence of dynamic changes and disruptions. In this paper, we present an approach to obtain formal resilience guarantees for autonomous hybrid systems using the interactive theorem prover KeYmaera X. Our key ideas are threefold: First, we derive a formalization of resilience that is tailored to autonomous hybrid systems. Second, we present reusable patterns for modeling stressors, detecting disruptions, and specifying resilience as a service level response in the differential dynamic logic (d$$\\mathcal {L}$$ L ). Third, we combine these concepts with an existing approach for the safe integration of learning components using hybrid contracts, and extend it towards dynamic adaptations to stressors. By combining reusable patterns for stressors, observers, and adaptation contracts for learning components, we provide a systematic approach for the deductive verification of resilience of autonomous hybrid systems with reduced specification effort. We demonstrate the applicability of our approach with two case studies, an autonomous robot and an intelligent water distribution system.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_14"}, {"primary_key": "586865", "vector": [], "sparse_vector": [], "title": "Practical Approximate Quantifier Elimination for Non-linear Real Arithmetic.", "authors": ["<PERSON><PERSON>", "Supratik Chakraborty", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "AbstractQuantifier Elimination (QE) concerns finding a quantifier-free formula that is semantically equivalent to a quantified formula in a given logic. For the theory of non-linear arithmetic over reals (NRA), QE is known to be computationally challenging. In this paper, we show how QE over NRA can be solved approximately and efficiently in practice using a Boolean combination of constraints in the linear arithmetic over reals (LRA). Our approach works by approximating the solution space of a set of NRA constraints when all real variables are bounded. It combines adaptive dynamic gridding with application of <PERSON><PERSON>’s Theorem to obtain the approximation efficiently via a sequence of linear programs (LP). We provide rigorous approximation guarantees, and also proofs of soundness and completeness (under mild assumptions) of our algorithm. Interestingly, our work allows us to bootstrap on earlier work (viz. [38]) and solve quantified SMT problems over a combination of NRA and other theories, that are beyond the reach of state-of-the-art solvers. We have implemented our approach in a preprocessor for Z3 called POQER. Our experiments show that POQER+Z3EG outperforms state-of-the-art SMT solvers on non-trivial problems, adapted from a suite of benchmarks.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_6"}, {"primary_key": "586866", "vector": [], "sparse_vector": [], "title": "The Opacity of Timed Automata.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractOpacity serves as a critical security and confidentiality property, which concerns whether an intruder can unveil a system’s secret based on structural knowledge and observed behaviors. Opacity in timed systems presents greater complexity compared to untimed systems, and it has been established that opacity for timed automata is undecidable. However, the original proof cannot be applied to decide the opacity of one-clock timed automata directly. In this paper, we explore three types of opacity within timed automata: language-based timed opacity, initial-location timed opacity, and current-location timed opacity. We begin by formalizing these concepts and establishing transformation relations among them. Subsequently, we demonstrate the undecidability of the opacity problem for one-clock timed automata. Furthermore, we offer a constructive proof for the conjecture regarding the decidability of opacity for timed automata in discrete-time semantics. Additionally, we present a sufficient condition and a necessary condition for the decidability of opacity in specific subclasses of timed automata.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_32"}, {"primary_key": "586867", "vector": [], "sparse_vector": [], "title": "Software Verification with CPAchecker 3.0: Tutorial and User Guide.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "AbstractThis tutorial provides an introduction toCPAcheckerfor users.CPAcheckeris a flexible and configurable framework for software verification and testing. The framework provides many abstract domains, such as BDDs, explicit values, intervals, memory graphs, and predicates, and many program-analysis and model-checking algorithms, such as abstract interpretation, bounded model checking,Impact, interpolation-based model checking,k-induction, PDR, predicate abstraction, and symbolic execution. This tutorial presents basic use cases forCPAcheckerin formal software verification, focusing on its main verification techniques with their strengths and weaknesses. An extended version also shows further use cases ofCPAcheckerfor test-case generation and witness-based result validation. The envisioned readers are assumed to possess a background in automatic formal verification and program analysis, but prior knowledge ofCPAcheckeris not required. This tutorial and user guide is based onCPAcheckerin version 3.0. This user guide’s latest version and other documentation are available athttps://cpachecker.sosy-lab.org/doc.php.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_30"}, {"primary_key": "586868", "vector": [], "sparse_vector": [], "title": "Unifying Weak Memory Verification Using Potentials.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "AbstractConcurrency verification for weak memory models is inherently complex. Several deductive techniques based on proof calculi have recently been developed, but these are typically tailored towards a single memory model through specialised assertions and associated proof rules. In this paper, we propose an extension to the logic $${\\textsf{<PERSON><PERSON><PERSON>}}$$ <PERSON>ccolo to generalise reasoning across different memory models. $${\\textsf{<PERSON><PERSON><PERSON>}}$$ Piccolo is interpreted on the semantic domain of thread potentials. By deriving potentials from weak memory model states, we can define the validity of $${\\textsf{<PERSON>cco<PERSON>}}$$ Piccolo formulae for multiple memory models. We moreover propose unified proof rules for verification on top of $${\\textsf{<PERSON><PERSON><PERSON>}}$$ <PERSON><PERSON>lo . Once (a set of) such rules has been shown to be sound with respect to a memory model $${\\textsf{MM}} $$ MM , all correctness proofs employing this rule set are valid for $${\\textsf{MM}}$$ MM . We exemplify our approach on the memory models $${\\textsf{SC}}$$ SC , $${\\textsf{TSO}}$$ TSO and $${\\textsf{SRA}}$$ SRA using the standard litmus tests Message-Passing and IRIW.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_27"}, {"primary_key": "586869", "vector": [], "sparse_vector": [], "title": "Satisfiability Modulo Theories: A Beginner&apos;s Tutorial.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "AbstractGreat minds have long dreamed of creating machines that can function as general-purpose problem solvers. Satisfiability modulo theories (SMT) has emerged as one pragmatic realization of this dream, providing significant expressive power and automation. This tutorial is a beginner’s guide to SMT. It includes an overview of SMT and its formal foundations, a catalog of the main theories used in SMT solvers, and illustrations of how to obtain models and proofs. Throughout the tutorial, examples and exercises are provided as hands-on activities for the reader. They can be run using either Python or the SMT-LIB language, using either the cvc5 or the Z3 SMT solver.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_31"}, {"primary_key": "586870", "vector": [], "sparse_vector": [], "title": "Alloy Repair Hint Generation Based on Historical Data.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ana C. R. Paiva"], "summary": "AbstractPlatforms to support novices learning to program are often accompanied by automated next-step hints that guide them towards correct solutions. Many of those approaches are data-driven, building on historical data to generate higher quality hints. Formal specifications are increasingly relevant in software engineering activities, but very little support exists to help novices while learning. Alloy is a formal specification language often used in courses on formal software development methods, and a platform—Alloy4Fun—has been proposed to support autonomous learning. While non-data-driven specification repair techniques have been proposed for Alloy that could be leveraged to generate next-step hints, no data-driven hint generation approach has been proposed so far. This paper presents the first data-driven hint generation technique for Alloy and its implementation as an extension to Alloy4Fun, being based on the data collected by that platform. This historical data is processed into graphs that capture past students’ progress while solving specification challenges. Hint generation can be customized with policies that take into consideration diverse factors, such as the popularity of paths in those graphs successfully traversed by previous students. Our evaluation shows that the performance of this new technique is competitive with non-data-driven repair techniques. To assess the quality of the hints, and help select the most appropriate hint generation policy, we conducted a survey with experienced Alloy instructors.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_8"}, {"primary_key": "586871", "vector": [], "sparse_vector": [], "title": "Getting Chip Card Payments Right.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "AbstractEMV is the international protocol standard for smart card payments and is used in billions of payment cards worldwide. Despite the standard’s advertised security, various issues have been previously uncovered, deriving from logical flaws that are hard to spot in EMV’s lengthy and complex specification. We have formalized various models of EMV in Tamarin, a symbolic model checker for cryptographic protocols. <PERSON><PERSON><PERSON> was extremely effective in finding critical flaws, both known and new, and in many cases exploitable on actual cards. We report on these past problems as well as followup work where we verified the latest, improved version of the protocol, the EMV kernel C8. This work puts C8’s correctness on a firm, formal basis, and clarifies which guarantees hold for C8 and under which assumptions. Overall our work supports the thesis that cryptographic protocol model checkers like <PERSON><PERSON><PERSON> have an essential role to play in improving the security of real-world payment protocols and that they are up to this challenge.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_2"}, {"primary_key": "586872", "vector": [], "sparse_vector": [], "title": "Parameterized Verification of Round-Based Distributed Algorithms via Extended Threshold Automata.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "AbstractThreshold automata are a computational model that has proven to be versatile in modeling threshold-based distributed algorithms and enabling their completely automatic parameterized verification. We present novel techniques for the verification of threshold automata, based on well-structured transition systems, that allow us to extend the expressiveness of both the computational model and the specifications that can be verified. In particular, we extend the model to allow decrements and resets of shared variables, possibly on cycles, and the specifications to general coverability. While these extensions of the model in general lead to undecidability, our algorithms provide a semi-decision procedure. We demonstrate the benefit of our extensions by showing that we can model complex round-based algorithms such as the phase king consensus algorithm and the Red Belly Blockchain protocol (published in 2019), and verify them fully automatically for the first time.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_33"}, {"primary_key": "586873", "vector": [], "sparse_vector": [], "title": "A Tutorial on Stream-Based Monitoring.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractStream-based runtime monitoring frameworks are safety assurance tools that check the runtime behavior of a system against a formal specification. This tutorial provides a hands-on introduction to RTLola, a real-time monitoring toolkit for cyber-physical systems and networks. RTLola processes, evaluates, and aggregates streams of input data, such as sensor readings, and provides a real-time analysis in the form of comprehensive statistics and logical assessments of the system’s health. RTLola has been applied successfully in monitoring autonomous systems such as unmanned aircraft. The tutorial guides the reader through the development of a stream-based specification for an autonomous drone observing other flying objects in its flight path. Each tutorial section provides an intuitive introduction, highlighting useful language features and specification patterns, and gives a more in-depth explanation of technical details for the advanced reader. Finally, we discuss how runtime monitors generated from RTLola specifications can be integrated into a variety of systems and discuss different monitoring applications.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_33"}, {"primary_key": "586874", "vector": [], "sparse_vector": [], "title": "The Java Verification Tool KeY:A Tutorial.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "AbstractThe KeY tool is a state-of-the-art deductive program verifier for the Java language. Its verification engine is based on a sequent calculus for dynamic logic, realizing forward symbolic execution of the target program, whereby all symbolic paths through a program are explored. Method contracts make verification scalable. KeY combines auto-active and fine-grained proof interaction, which is possible both at the level of the verification target and its specification, as well as at the level of proof rules and program logic. This makes KeY well-suited for teaching program verification, but also permits proof debugging at the source code level. The latter made it possible to verify some of the most complex Java code to date. The article provides a self-contained introduction to the working principles and the practical usage of KeY for anyone with basic knowledge in logic and formal methods.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_32"}, {"primary_key": "586875", "vector": [], "sparse_vector": [], "title": "Visualizing Game-Based Certificates for Hyperproperty Verification.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "AbstractHyperproperties relate multiple executions of a system and are commonly used to specify security and information-flow policies. While many verification approaches for hyperproperties exist, providing a convincing certificate that the system satisfies a given property is still a major challenge. In this paper, we propose strategies as a suitable form of certificate for hyperproperties specified in a fragment of the temporal logic HyperLTL. Concretely, we interpret the verification of a HyperLTL property as a game between universal and existential quantification, allowing us to leverage strategies for the existential quantifiers as certificates. We present , a browser-based visualization tool that lets users interactively explore an (automatically synthesized) witness strategy by taking control over universally quantified executions.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_5"}, {"primary_key": "586876", "vector": [], "sparse_vector": [], "title": "FM-Weck: Containerized Execution of Formal-Methods Tools.", "authors": ["<PERSON>", "<PERSON>"], "summary": "AbstractSoftware is ubiquitous in the digital world, and the correct function of software systems is critical for our society, industry, and infrastructure. While testing and static analysis are long-established techniques in software-development processes, it became widely acknowledged only in the past two decades that formal methods are required for giving guarantees of functional correctness. Both academia and industry worked hard to develop tools for formal verification of software during the past two decades, with the result that many software verifiers are available now (for example, 59 freely available verifiers for C and Java programs). However, most software verifiers are challenging to find, install, and use for both external researchers and potential users. FM-Weck changes this: It provides a fully automatic, zero-configuration container-based setup and execution for more than 50 software verifiers for C and Java. Both the setup requirements and execution parameters of every supported verifier are provided by the tool developers themselves as part of the FM-Tools metadata format that was established recently and was already used by the international competitions SV-COMP and Test-Comp. With our solution FM-Weck, anyone gets fast and easy access to state-of-the-art formal verifiers, no expertise required, fully reproducible.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_3"}, {"primary_key": "586877", "vector": [], "sparse_vector": [], "title": "ASMETA Tool Set for Rigorous System Design.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractThis tutorial paper introduces ASMETA, a comprehensive suite of integrated tools around the formal method Abstract State Machines to specify and analyze the executable behavior of discrete event systems. ASMETA supports the entire system development life-cycle, from the specification of the functional requirements to the implementation of the code, in a systematic and incremental way. This tutorial provides an overview of ASMETA through an illustrative case study, the Pill-Box, related to the design of a smart pillbox device. It illustrates the practical use of the range of modeling and V&amp;V techniques available in ASMETA and C++ code generation from models, to increase the quality and reliability of behavioral system models and source code.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_28"}, {"primary_key": "586878", "vector": [], "sparse_vector": [], "title": "Learning Branching-Time Properties in CTL and ATL via Constraint Solving.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractWe address the problem of learning temporal properties from the branching-time behavior of systems. Existing research in this field has mostly focused on learning linear temporal properties specified using popular logics, such as Linear Temporal Logic (LTL) and Signal Temporal Logic (STL). Branching-time logics such as Computation Tree Logic (CTL) and Alternating-time Temporal Logic (ATL), despite being extensively used in specifying and verifying distributed and multi-agent systems, have not received adequate attention. Thus, in this paper, we investigate the problem of learning CTL and ATL formulas from examples of system behavior. As input to the learning problems, we rely on the typical representations of branching behavior as Kripke structures and concurrent game structures, respectively. Given a sample of structures, we learn concise formulas by encoding the learning problem into a satisfiability problem, most notably by symbolically encoding both the search for prospective formulas and their fixed-point based model checking algorithms. We also study the decision problem of checking the existence of prospective ATL formulas for a given sample. We implement our algorithms in a Python prototype and have evaluated them to extract several common CTL and ATL formulas used in practical applications.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_16"}, {"primary_key": "586879", "vector": [], "sparse_vector": [], "title": "Free Facts: An Alternative to Inefficient Axioms in Dafny.", "authors": ["Tabea Bordis", "<PERSON><PERSON>"], "summary": "AbstractFormal software verification relies on properties of functions and built-in operators. Unless these properties are handled directly by decision procedures, an automated verifier includes them in verification conditions by supplying them as universally quantified axioms or theorems. The use of quantifiers sometimes leads to bad performance, especially if automation causes the quantifiers to be instantiated many times.This paper proposes free facts as an alternative to some axioms. A free fact is a pre-instantiated axiom that is generated alongside the formulas in a verification condition that can benefit from the facts. Replacing an axiom with free facts thus reduces the number of quantifiers in verification conditions. Free facts are statically triggered by syntactic occurrences of certain patterns in the proof terms. This is less powerful than the dynamically triggered patterns used during proof construction. However, the paper shows that free facts perform well in practice.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_8"}, {"primary_key": "586880", "vector": [], "sparse_vector": [], "title": "A Pyramid Of (Formal) Software Verification.", "authors": ["<PERSON>", "<PERSON>"], "summary": "AbstractOver the past few years there has been significant progress in the various fields of software verification resulting in many useful tools and successful deployments, both academic and commercial. However much of the work describing these tools and ideas is written by and for the research community. The scale, diversity and focus of the literature can act as a barrier, separating industrial users and the wider academic community from the tools that could make their work more efficient, more certain and more productive. This tutorial gives a simple classification of verification techniques in terms of a pyramid and uses it to describe the six main schools of verification technologies. We have found this approach valuable for building collaborations with industry as it allows us to explain the intrinsic strengths and weaknesses of techniques and pick the right tool for any given industrial application. The model also highlights some of the cultural differences and unspoken assumptions of different areas of verification and illuminates future directions.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_24"}, {"primary_key": "586881", "vector": [], "sparse_vector": [], "title": "Reachability Analysis for Multiloop Programs Using Transition Power Abstraction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractA wide variety of algorithms is employed for the reachability analysis of programs with loops but most of them are restricted to single loop programs. Recently a new technique called Transition Power Abstraction (TPA) showed promising results for safety checks of software. In contrast to many other techniques TPA efficiently handles loops with a large number of iterations. This paper introduces an algorithm that enables the effective use of TPA for analysis of multiloop programs. The TPA-enabled loop analysis reduces the dependency on the number of possible iterations. Our approach analyses loops in a modular manner and both computes and uses transition invariants incrementally, making program analysis efficient. The new algorithm is implemented in the Golem solver. Conducted experiments demonstrate that this approach outperforms the previous implementation of TPA and other competing tools on a wide range of multiloop benchmarks.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_29"}, {"primary_key": "586882", "vector": [], "sparse_vector": [], "title": "Fast Attack Graph Defense Localization via Bisimulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Abstract System administrators, network engineers, and IT managers can learn much about the vulnerabilities of an organization’s cyber system by constructing and analyzing analytical attack graphs (AAGs). An AAG consists of logical rule nodes, fact nodes, and derived fact nodes. It provides a graph-based representation that describes ways by which an attacker can achieve progress towards a desired goal, a.k.a. a crown jewel. Given an AAG, different types of analyses can be performed to identify attacks on a target goal, measure the vulnerability of the network, and gain insights on how to make it more secure. However, as the size of the AAGs representing real-world systems may be very large, existing analyses are slow or practically impossible. In this paper, we introduce and show how to compute an AAG’s defense core : a locally minimal subset of the AAG’s rules whose removal will prevent an attacker from reaching a crown jewel. Most importantly, in order to scale-up the performance of the detection of a defense core, we introduce a novel application of the well-known notion of bisimulation to AAGs. Our experiments show that the use of bisimulation results in significantly smaller graphs and in faster detection of defense cores, making them practical.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_13"}, {"primary_key": "586883", "vector": [], "sparse_vector": [], "title": "Sound and Complete Witnesses for Template-Based Verification of LTL Properties on Polynomial Programs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "AbstractWe study the classical problem of verifying programs with respect to formal specifications given in the linear temporal logic (LTL). We first present novel sound and complete witnesses for LTL verification over imperative programs. Our witnesses are applicable to both verification (proving) and refutation (finding bugs) settings. We then consider LTL formulas in which atomic propositions can be polynomial constraints and turn our focus to polynomial arithmetic programs, i.e. programs in which every assignment and guard consists only of polynomial expressions. For this setting, we provide an efficient algorithm to automatically synthesize such LTL witnesses. Our synthesis procedure is both sound and semi-complete. Finally, we present experimental results demonstrating the effectiveness of our approach and that it can handle programs which were beyond the reach of previous state-of-the-art tools.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_31"}, {"primary_key": "586884", "vector": [], "sparse_vector": [], "title": "Introducing SWIRL: An Intermediate Representation Language for Scientific Workflows.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "AbstractIn the ever-evolving landscape of scientific computing, properly supporting the modularity and complexity of modern scientific applications requires new approaches to workflow execution, like seamless interoperability between different workflow systems, distributed-by-design workflow models, and automatic optimisation of data movements. In order to address this need, this article introduces SWIRL, an intermediate representation language for scientific workflows. In contrast with other product-agnostic workflow languages, SWIRL is not designed for human interaction but to serve as a low-level compilation target for distributed workflow execution plans. The main advantages of SWIRL semantics are low-level primitives based on the send/receive programming model and a formal framework ensuring the consistency of the semantics and the specification of translating workflow models represented by Directed Acyclic Graphs (DAGs) into SWIRL workflow descriptions. Additionally, SWIRL offers rewriting rules designed to optimise execution traces, accompanied by corresponding equivalence. An open-source SWIRL compiler toolchain has been developed using the ANTLR Python3 bindings.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_12"}, {"primary_key": "586885", "vector": [], "sparse_vector": [], "title": "Detecting Speculative Execution Vulnerabilities on Weak Memory Models.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "AbstractSpeculative execution attacks affect all modern processors and much work has been done to develop techniques for detection of associated vulnerabilities. Modern processors also operate on weak memory models which allow out-of-order execution of code. Despite this, there is little work on looking at the interplay between speculative execution and weak memory models. In this paper, we provide an information flow logic for detecting speculative execution vulnerabilities on weak memory models. The logic is general enough to be used with any modern processor, and designed to be extensible to allow detection of vulnerabilities to specific attacks. The logic has been proven sound with respect to an abstract model of speculative execution in Isabelle/HOL.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_25"}, {"primary_key": "586886", "vector": [], "sparse_vector": [], "title": "DFAMiner: Mining Minimal Separating DFAs from Labelled Samples.", "authors": ["Daniele Dell&apos;Erba", "<PERSON>", "<PERSON>"], "summary": "AbstractWe propose , a passive learning tool for learning minimal separating deterministic finite automata (DFA) from a set of labelled samples. Separating automata are an interesting class of automata that occurs generally in regular model checking and has raised interest in foundational questions of parity game solving. We first propose a simple and linear-time algorithm that incrementally constructs a three-valued DFA (3DFA) from a set of labelled samples given in the usual lexicographical order. This 3DFA has accepting and rejecting states as well as don’t-care states, so that it can exactly recognise the labelled examples. We then apply our tool to mining a minimal separating DFA for the labelled samples by minimising the constructed automata via a reduction to SAT solving. Empirical evaluation shows that our tool outperforms current state-of-the-art tools significantly on standard benchmarks for learning minimal separating DFAs from samples. Progress in the efficient construction of separating DFAs can also lead to finding the lower bound of parity game solving, where we show that can create optimal separating automata for simple languages with up to 7 colours. Future improvements might offer inroads to better data structures.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_4"}, {"primary_key": "586887", "vector": [], "sparse_vector": [], "title": "PyBDR: Set-Boundary Based Reachability Analysis Toolkit in Python.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "AbstractWe present PyBDR, a Python reachability analysis toolkit based on set-boundary analysis, which centralizes on widely-adopted set propagation techniques for formal verification, controller synthesis, state estimation, etc. It employs boundary analysis of initial sets to mitigate the wrapping effect during computations, thus improving the performance of reachability analysis algorithms without significantly increasing computational costs. Beyond offering various set representations such as polytopes and zonotopes, our toolkit particularly excels in interval arithmetic by extending operations to the tensor level, enabling efficient parallel interval arithmetic computation and unifying vector and matrix intervals into a single framework. Furthermore, it features symbolic computation of derivatives of arbitrary order and evaluates them as real or interval-valued functions, which is essential for approximating behaviours of nonlinear systems at specific time instants. Its modular architecture design offers a series of building blocks that facilitate the prototype development of reachability analysis algorithms. Comparative studies showcase its strengths in handling verification tasks with large initial sets or long time horizons. The toolkit is available at https://github.com/ASAG-ISCAS/PyBDR.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_10"}, {"primary_key": "586888", "vector": [], "sparse_vector": [], "title": "Understanding Synthesized Reactive Systems Through Invariants.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "AbstractIn many applications for which reactive synthesis is attractive, computed implementations need to have understandable behavior. While some existing synthesis approaches compute finite-state machines with a structure that supports their understandability, such approaches do not scale to specifications that can only be realized with a large number of states. Furthermore, asking the engineer to understand the internal structure of the implementation is unnecessary when only the behavior of the implementation is to be understood.In this paper, we present an approach to computing understandable safety invariants that every implementation satisfying a generalized reactivity(1) specification needs to fulfill. Together with the safety part of the specification, the invariants completely define which transitions between input and output proposition valuations any correct implementation can take. We apply the approach in two case studies and demonstrate that the computed invariants highlight the strategic decisions that implementations for the given specification need to make, which not only helps the system designer with understanding what the specification entails, but also supports specification debugging.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_9"}, {"primary_key": "586889", "vector": [], "sparse_vector": [], "title": "Staged Specification Logic for Verifying Higher-Order Imperative Programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "AbstractHigher-order functions and imperative states are language features supported by many mainstream languages. Their combination is expressive and useful, but complicates specification and reasoning, due to the use of yet-to-be-instantiated function parameters. One inherent limitation of existing specification mechanisms is its reliance ononly two stages : an initial stage to denote the precondition at the start of the method and a final stage to capture the postcondition. Such two-stage specifications forceabstract propertiesto be imposed on unknown function parameters, leading to less precise specifications for higher-order methods. To overcome this limitation, we introduce a novel extension to Hoare logic that supportsmultiple stagesfor a call-by-value higher-order language with ML-like local references. Multiple stages allow the behavior of unknown function-type parameters to be captured abstractly as uninterpreted relations; and can also model the repetitive behavior of each recursion as a separate stage. In this paper, we define our staged logic with its semantics, prove its soundness and develop a new automated higher-order verifier, calledHeifer, for a core ML-like language.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_26"}, {"primary_key": "586890", "vector": [], "sparse_vector": [], "title": "Integrating Loop Acceleration Into Bounded Model Checking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractBounded Model Checking (BMC) is a powerful technique for proving unsafety. However, finding deep counterexamples that require a large bound is challenging for BMC. On the other hand, acceleration techniques compute “shortcuts” that “compress” many execution steps into a single one. In this paper, we tightly integrate acceleration techniques into SMT-based bounded model checking. By adding suitable “shortcuts” on the fly, our approach can quickly detect deep counterexamples. Moreover, using so-called blocking clauses, our approach can prove safety of examples where BMC diverges. An empirical comparison with other state-of-the-art techniques shows that our approach is highly competitive for proving unsafety, and orthogonal to existing techniques for proving safety.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_4"}, {"primary_key": "586891", "vector": [], "sparse_vector": [], "title": "Bridging Dimensions: Confident Reachability for High-Dimensional Controllers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "AbstractAutonomous systems are increasingly implemented using end-to-end learning-based controllers. Such controllers make decisions that are executed on the real system, with images as one of the primary sensing modalities. Deep neural networks form a fundamental building block of such controllers. Unfortunately, the existing neural-network verification tools do not scale to inputs with thousands of dimensions—especially when the individual inputs (such as pixels) are devoid of clear physical meaning. This paper takes a step towards connecting exhaustive closed-loop verification with high-dimensional controllers. Our key insight is that the behavior of a high-dimensional vision-based controller can be approximated with several low-dimensional controllers. To balance the approximation accuracy and verifiability of our low-dimensional controllers, we leverage the latest verification-aware knowledge distillation. Then, we inflate low-dimensional reachability results with statistical approximation errors, yielding a high-confidence reachability guarantee for the high-dimensional controller. We investigate two inflation techniques—based on trajectories and control actions—both of which show convincing performance in three OpenAI gym benchmarks.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_20"}, {"primary_key": "586892", "vector": [], "sparse_vector": [], "title": "A Zonotopic Dempster-Shafer Approach to the Quantitative Verification of Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractThe reliability and usefulness of verification depend on the ability to represent appropriately the uncertainty. Most existing work on neural network verification relies on the hypothesis of either set-based or probabilistic information on the inputs. In this work, we rely on the framework of imprecise probabilities, specifically p-boxes, to propose a quantitative verification of ReLU neural networks, which can account for both probabilistic information and epistemic uncertainty on inputs. On classical benchmarks, including the ACAS Xu examples, we demonstrate that our approach improves the tradeoff between tightness and efficiency compared to related work on probabilistic network verification, while handling much more general classes of uncertainties on the inputs and providing fully guaranteed results.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_17"}, {"primary_key": "586893", "vector": [], "sparse_vector": [], "title": "Misconceptions in Finite-Trace and Infinite-Trace Linear Temporal Logic.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Antonio Di Stasio", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "AbstractWith the growing use of temporal logics in areas ranging from robot planning to runtime verification, it is critical that users have a clear understanding of what a specification means. Toward this end, we have been developing a catalog of semantic errors and a suite of test instruments targeting various user-groups. The catalog is of interest to educators, to logic designers, to formula authors, and to tool builders, e.g., to identify mistakes. The test instruments are suitable for classroom teaching or self-study.This paper reports on five sets of survey data collected over a three-year span. We study misconceptions about finite-trace $$\\textsc {ltl}_{f}$$ L T L f in three ltl-aware audiences, and misconceptions about standard ltl in novices. We find several mistakes, even among experts. In addition, the data supports several categories of errors in both $$\\textsc {ltl}_{f}$$ L T L f and ltl that have not been identified in prior work. These findings, based on data from actual users, offer insights into what specific ways temporal logics are tricky and provide a groundwork for future interventions.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_30"}, {"primary_key": "586894", "vector": [], "sparse_vector": [], "title": "Efficient Formally Verified Maximal End Component Decomposition for MDPs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "AbstractIdentifying a <PERSON>ov decision process’s maximal end components is a prerequisite for applying sound probabilistic model checking algorithms. In this paper, we present the first mechanized correctness proof of a maximal end component decomposition algorithm, which is an important algorithm in model checking, using the Isabelle/HOL theorem prover. We iteratively refine the high-level algorithm and proof into an imperative LLVM bytecode implementation that we integrate into the Modest Toolset ’s existing model checker. We bring the benefits of interactive theorem proving into practice by reducing the trusted code base of a popular probabilistic model checker and we experimentally show that our new verified maximal end component decomposition in performs on par with the tool’s previous unverified implementation.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_11"}, {"primary_key": "586895", "vector": [], "sparse_vector": [], "title": "A Local Search Algorithm for MaxSMT(LIA).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "S<PERSON>wei <PERSON>"], "summary": "AbstractMaxSAT modulo theories (MaxSMT) is an important generalization of Satisfiability modulo theories (SMT) with various applications. In this paper, we focus on MaxSMT with the background theory of Linear Integer Arithmetic, denoted as MaxSMT(LIA). We design the first local search algorithm for MaxSMT(LIA) called PairLS, based on the following novel ideas. A novel operator called pairwise operator is proposed for integer variables. It extends the original local search operator by simultaneously operating on two variables, enriching the search space. Moreover, a compensation-based picking heuristic is proposed to determine and distinguish the pairwise operations. Experiments are conducted to evaluate our algorithm on massive benchmarks. The results show that our solver is competitive with state-of-the-art MaxSMT solvers. Furthermore, we also apply the pairwise operation to enhance the local search algorithm of SMT, which shows its extensibility.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_3"}, {"primary_key": "586896", "vector": [], "sparse_vector": [], "title": "Beyond the Bottleneck: Enhancing High-Concurrency Systems with Lock Tuning.", "authors": ["Juntao Ji", "Yin<PERSON>u Gu", "Yubao Fu", "<PERSON><PERSON>"], "summary": "AbstractHigh-concurrency systems often suffer from performance bottlenecks [1]. This is often caused by waiting and context switching caused by fierce competition between threads for locks. As a cloud computing company, we place great emphasis on maximizing performance. In this regard, we transform the lightweight spin-lock and propose a concise parameter fine-tuning strategy, which can break through the system performance bottleneck with minimal risk conditions. This strategy has been validated in Apache RocketMQ [2], a high-throughput message queue system. Through this strategy, we achieved performance improvements of up to 37.58% on X86 CPU and up to 32.82% on ARM CPU. Furthermore, we confirmed the method’s consistent effectiveness across different code versions and IO flush strategies, demonstrating its broad applicability in real-world settings. This work offers not only a practical tool for addressing performance issues in high-concurrency systems but also highlights the practical value of formal techniques in solving engineering problems.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_20"}, {"primary_key": "586897", "vector": [], "sparse_vector": [], "title": "Discourje: Run-Time Verification of Communication Protocols in Clojure - Live at Last.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "AbstractMultiparty session typing (MPST) is a formal method to make concurrent programming simpler. The idea is to use type checking to automatically prove safety (protocol compliance) and liveness (communication deadlock freedom) of implementations relative to specifications. Discourje is an existing run-time verification library for communication protocols in Clojure, based on dynamic MPST. The original version of Discourje can detect only safety violations. In this paper, we present an extension of Discourje to detect also liveness violations.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_11"}, {"primary_key": "586898", "vector": [], "sparse_vector": [], "title": "Stochastic Games for User Journeys.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractIndustry is shifting towards service-based business models, for which user satisfaction is crucial. User satisfaction can be analyzed with user journeys, which model services from the user’s perspective. Today, these models are created manually and lack both formalization and tool-supported analysis. This limits their applicability to complex services with many users. Our goal is to overcome these limitations by automated model generation and formal analyses, enabling the analysis of user journeys for complex services and thousands of users. In this paper, we use stochastic games to model and analyze user journeys. Stochastic games can be automatically constructed from event logs and model checked to, e.g., identify interactions that most effectively help users reach their goal. Since the learned models may get large, we use property-preserving model reduction to visualize users’ pain points to convey information to business stakeholders. The applicability of the proposed method is here demonstrated on two complementary case studies.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_12"}, {"primary_key": "586899", "vector": [], "sparse_vector": [], "title": "State Matching and Multiple References in Adaptive Active Automata Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractActive automata learning (AAL) is a method to infer state machines by interacting with black-box systems. Adaptive AAL aims to reduce the sample complexity of AAL by incorporating domain specific knowledge in the form of (similar) reference models. Such reference models appear naturally when learning multiple versions or variants of a software system. In this paper, we present state matching, which allows flexible use of the structure of these reference models by the learner. State matching is the main ingredient of adaptive $$L^{\\#}$$ L # , a novel framework for adaptive learning, built on top of $$L^{\\#}$$ L # . Our empirical evaluation shows that adaptive $$L^{\\#}$$ L # improves the state of the art by up to two orders of magnitude.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_14"}, {"primary_key": "586900", "vector": [], "sparse_vector": [], "title": "Formal Semantics and Analysis of Multitask PLC ST Programs with Preemption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractProgrammable logic controllers (PLCs) are widely used in industrial applications. Ensuring the correctness of PLC programs is important due to their safety-critical nature. Structured text (ST) is an imperative programming language for PLC. Despite recent advances in executable semantics of PLC ST, existing methods neglect complex multitasking and preemption features. This paper presents an executable semantics of PLC ST with preemptive multitasking. Formal analysis of multitasking programs experiences the state explosion problem. To mitigate this problem, this paper also proposes state space reduction techniques for model checking multitask PLC ST programs.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_22"}, {"primary_key": "586901", "vector": [], "sparse_vector": [], "title": "B2SAT: A Bare-Metal Reduction of B to SAT.", "authors": ["<PERSON>"], "summary": "AbstractWe present a new SAT backend for the B-Method to enable new applications of formal methods. The new backend interleaves low-level SAT solving with high-level constraint solving. It provides a “bare metal” access to SAT solving, while pre- and post-calculations can be done in the full B language, with access to higher-order or even infinite data values. The backend is integrated into ProB, not as a general purpose backend, but as a dedicated backend for solving hard constraint satisfaction and optimisation problems on complex data. In the article we present the approach, its origin in the proof of <PERSON>’s theorem, and illustrate and evaluate it on a few novel applications of formal methods, ranging from biology to railway applications.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_9"}, {"primary_key": "586902", "vector": [], "sparse_vector": [], "title": "VeriQR: A Robustness Verification Tool for quantum Machine Learning Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Zhaofeng Su"], "summary": "AbstractAdversarial noise attacks present a significant threat to quantum machine learning (QML) models, similar to their classical counterparts. This is especially true in the current Noisy Intermediate-Scale Quantum era, where noise is unavoidable. Therefore, it is essential to ensure the robustness of QML models before their deployment. To address this challenge, we introduce VeriQR, the first tool designed specifically for formally verifying and improving the robustness of QML models, to the best of our knowledge. This tool mimics real-world quantum hardware’s noisy impacts by incorporating random noise to formally validate a QML model’s robustness. VeriQR supports exact (sound and complete) algorithms for both local and global robustness verification. For enhanced efficiency, it implements an under-approximate (complete) algorithm and a tensor network-based algorithm to verify local and global robustness, respectively. As a formal verification tool, VeriQR can detect adversarial examples and utilize them for further analysis and to enhance the local robustness through adversarial training, as demonstrated by experiments on real-world quantum machine learning models. Moreover, it permits users to incorporate customized noise. Based on this feature, we assess VeriQR using various real-world examples, and experimental outcomes confirm that the addition of specific quantum noise can enhance the global robustness of QML models. These processes are made accessible through a user-friendly graphical interface provided by VeriQR, catering to general users without requiring a deep understanding of the counter-intuitive probabilistic nature of quantum computing.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_21"}, {"primary_key": "586903", "vector": [], "sparse_vector": [], "title": "Code-Level Safety Verification for Automated Driving: A Case Study.", "authors": ["<PERSON><PERSON>", "Cal<PERSON> I<PERSON>", "Simos G<PERSON>", "<PERSON><PERSON>"], "summary": "AbstractThe formal safety analysis of automated driving vehicles poses unique challenges due to their dynamic operating conditions and significant complexity. This paper presents a case study of applying formal safety verification to adaptive cruise controllers. Unlike the majority of existing verification approaches in the automotive domain, which only analyze (potentially imperfect) controller models, employ simulation to find counter-examples or use online monitors for runtime verification, our method verifies controllers at code level by utilizing bounded model checking. Verification is performed against an invariant set derived from formal specifications and an analytical model of the required behavior. For neural network controllers, we propose a scalable three-step decomposition, which additionally uses a neural network verifier. We show that both traditionally implemented as well as neural network controllers are verified within minutes. The dual focus on formal safety and implementation verification provides a comprehensive framework applicable to similar cyber-physical systems.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_22"}, {"primary_key": "586904", "vector": [], "sparse_vector": [], "title": "cfaults: Model-Based Diagnosis for Fault Localization in C with Multiple Test Cases.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Vasco <PERSON>"], "summary": "AbstractDebugging is one of the most time-consuming and expensive tasks in software development. Several formula-based fault localization (FBFL) methods have been proposed, but they fail to guarantee a set of diagnoses across all failing tests or may produce redundant diagnoses that are not subset-minimal, particularly for programs with multiple faults.This paper introduces a novel fault localization approach for C programs with multiple faults. CFaults leverages Model-Based Diagnosis (MBD) with multiple observations and aggregates all failing test cases into a unified MaxSAT formula. Consequently, our method guarantees consistency across observations and simplifies the fault localization procedure. Experimental results on two benchmark sets of C programs, TCAS and C-Pack-IPAs, show that CFaults is faster than other FBFL approaches like BugAssist and SNIPER. Moreover, CFaults only generates subset-minimal diagnoses of faulty statements, whereas the other approaches tend to enumerate redundant diagnoses.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_24"}, {"primary_key": "586905", "vector": [], "sparse_vector": [], "title": "Practical Deductive Verification of OCaml Programs.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "AbstractIn this paper, we provide a comprehensive, hands-on tutorial on how to apply deductive verification to programs written in . In particular, we show how one can use the specification language and the tool to conduct mostly-automated verification on code. In our presentation, we focus on two main classes of programs: first, purely functional programs with no mutable state; then on imperative programs, where one can mix mutable state with subtle control-flow primitives, such as locally-defined exceptions.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_29"}, {"primary_key": "586906", "vector": [], "sparse_vector": [], "title": "Runtime Verification in Real-Time with the Copilot Language: A Tutorial.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "AbstractUltra-critical systems require high-level assurance, which cannot always be guaranteed at compile time. The use of runtime verification (RV) enables monitoring of these systems during runtime, to detect illegal states early and limit their potential consequences. This paper is a tutorial on RV using Copilot, an open-source runtime verification framework actively used by NASA to carry out experiments with robots and unmanned aerial vehicles. Copilot monitors are written in a compositional, stream-based language, which the framework automatically translates into real-time C code that satisfies static memory requirements suitable to run on embedded hardware. Copilot includes multiple libraries that extend the core functionality with higher-level constructs, Boyer-Moore majority voting, and a variety of Temporal Logics (TL), resulting in robust, high-level specifications that are easier to understand than their traditional counterparts.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_27"}, {"primary_key": "586907", "vector": [], "sparse_vector": [], "title": "Automated Static Analysis of Quality of Service Properties of Communicating Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "AbstractWe present \"Image missing\", a bounded \"Image missing\"\"Image missing\"to statically analyse Quality of Service ( \"Image missing\") properties of message-passing systems. We consider QoS properties on measurable application-level attributes as well as resource consumption metrics, for example, those relating monetary cost to memory usage. The applicability of \"Image missing\"is evaluated through case studies and experiments. A first case study is based on the AWS cloud while a second one analyses a communicating system automatically extracted from code. Additionally, we consider synthetically generated experiments to assess the scalability of \"Image missing\". These experiments showed that our model can faithfully capture and effectively analyse QoS properties in industrial-strength scenarios.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_7"}, {"primary_key": "586908", "vector": [], "sparse_vector": [], "title": "A Divide-and-Conquer Approach to Variable Elimination in Linear Real Arithmetic.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "AbstractWe introduce a novel variable elimination method for conjunctions of linear real arithmetic constraints. In prior work, we derived a variant of the <PERSON><PERSON>-<PERSON><PERSON> elimination, which uses case splitting to reduce the procedure’s complexity from doubly to singly exponential. This variant, which we call FMplex, was originally developed for satisfiability checking, and it essentially performs a depth-first search in a tree of sub-problems. It can be adapted straightforwardly for the task of quantifier elimination, but it returns disjunctions of conjunctions, even though the solution space can always be defined by a single conjunction. Our main contribution is to show how to efficiently extract an equivalent conjunction from the search tree. Besides the theoretical foundations, we explain how the procedure relates to other methods for quantifier elimination and polyhedron projection. An experimental evaluation demonstrates that our implementation is competitive with established tools.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_7"}, {"primary_key": "586909", "vector": [], "sparse_vector": [], "title": "The Nonexistence of Unicorns and Many-Sorted Löwenheim-Skolem Theorems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "AbstractStable infiniteness, strong finite witnessability, and smoothness are model-theoretic properties relevant to theory combination in satisfiability modulo theories. Theories that are strongly finitely witnessable and smooth are called strongly polite and can be effectively combined with other theories. <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> conjectured that stably infinite and strongly finitely witnessable theories are smooth and therefore strongly polite. They called counterexamples to this conjecture unicorn theories, as their existence seemed unlikely. We prove that, indeed, unicorns do not exist. We also prove versions of the <PERSON><PERSON><PERSON><PERSON> theorem and the <PERSON><PERSON><PERSON><PERSON> test for many-sorted logic.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_34"}, {"primary_key": "586910", "vector": [], "sparse_vector": [], "title": "Advancing Quantum Computing with Formal Methods.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractThis tutorial introduces quantum computing with a focus on the applicability of formal methods in this relatively new domain. We describe quantum circuits and convey an understanding of their inherent combinatorial nature and the exponential blow-up that makes them hard to analyze. Then, we show how weighted model counting (#SAT) can be used to solve hard analysis tasks for quantum circuits.This tutorial is aimed at everyone in the formal methods community with an interest in quantum computing. Familiarity with quantum computing is not required, but basic linear algebra knowledge (particularly matrix multiplication and basis vectors) is a prerequisite. The goal of the tutorial is to inspire the community to advance the development of quantum computing with formal methods.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_25"}, {"primary_key": "586911", "vector": [], "sparse_vector": [], "title": "A Case Study on Formal Equivalence Verification Between a C/C++ Model and Its RTL Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractIn the field of communication system products, most datapath Digital Signal Processing algorithms are initially developed at a high-level in MATLAB® or C/C++. Subsequently, design engineers use these models as a reference for implementing Register Transfer Level designs. The conventional approach to verify their equivalence involves extensive Universal Verification Methodology dynamic simulations, which can last for months and require significant verification efforts. However, some elusive errors might still occur because it is infeasible to explore all input combinations with this method. On the other hand, Formal Equivalence Verification aims to verify that a Register Transfer Level design is functionally equivalent to the reference high-level C/C++ model across all possible legal states. With recent advancements in formal solver technology, Formal Equivalence Verification provides a distinct benefit by using mathematical methods to ensure that the Register Transfer Level (timed) matches the original high-level C/C++ model (untimed). This drastically reduces the verification time and ensures the exhaustive coverage of the design state space. This paper presents an in-depth exploration of complex Finite State Machine with datapath verification, specifically focusing on Multiplier-Accumulator, Tone Generator, and Automatic Gain Control, by employing the formal equivalence methodology. Although these signal processing blocks were previously verified throughout Universal Verification Methodology dynamic simulations, Formal Equivalence Verification was able to identify hard-to-find bugs in just a few weeks by utilizing the new workflow, thereby streamlining the verification process.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_23"}, {"primary_key": "586912", "vector": [], "sparse_vector": [], "title": "Accurate Static Data Race Detection for C.", "authors": ["Emerson Sales", "<PERSON>", "<PERSON>"], "summary": "AbstractData races are a particular kind of subtle, unintended program behaviour arising from thread interference in shared-memory concurrency. In this paper, we propose an automated technique for static detection of data races in multi-threaded C programs with POSIX threads. The key element of our technique is a reduction to reachability. Our prototype implementation combines such reduction with context-bounded analysis. The approach proves competitive against state-of-the-art tools, finding new issues in the implementation of well-known lock-free data structures, and shows a considerably superior accuracy of analysis in the presence of complex shared-memory access patterns.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_23"}, {"primary_key": "586913", "vector": [], "sparse_vector": [], "title": "No Risk, No Fun - A Tutorial on Risk Management.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "AbstractThe aim of this tutorial is to explain to the formal methods community the area of risk management and its most prominent concepts: the definition of risk, strategies for managing risk, the risk management cycle, and the role of ISO standards.For each of these concepts, I explain how formal methods relate and contribute, making risk management more accountable: systematic, transparent, and quantitative. I will also argue that viewing Formal Methods through the lens of risk management, and making the relevance of formal methods in risk analysis explicit, helps our community to better communicate the merits of formal methods to industry.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_26"}, {"primary_key": "586914", "vector": [], "sparse_vector": [], "title": "Extending Isabelle/HOL&apos;s Code Generator with Support for the Go Programming Language.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "AbstractThe <PERSON> proof assistant includes a small functional language, which allows users to write and reason about programs. So far, these programs could be extracted into a number of functional languages: Standard ML, OCaml, Scala, and Haskell. This work adds support for Go as a fifth target language for the Code Generator. Unlike the previous targets, Go is not a functional language and encourages code in an imperative style, thus many of the features of <PERSON>’s language (particularly data types, pattern matching, and type classes) have to be emulated using imperative language constructs in Go. The developed Code Generation is provided as an add-on library that can be simply imported into existing theories.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_1"}, {"primary_key": "586915", "vector": [], "sparse_vector": [], "title": "Switching Controller Synthesis for Hybrid Systems Against STL Formulas.", "authors": ["<PERSON> Su", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractSwitching controllers play a pivotal role in directing hybrid systems (HSs) towards the desired objective, embodying a “correct-by-construction” approach to HS design. Identifying these objectives is thus crucial for the synthesis of effective switching controllers. While most of existing works focus on safety and liveness, few of them consider timing constraints. In this paper, we delves into the synthesis of switching controllers for HSs that meet system objectives given by a fragment of STL, which essentially corresponds to a reach-avoid problem with timing constraints. Our approach involves iteratively computing the state sets that can be driven to satisfy the reach-avoid specification with timing constraints. This technique supports to create switching controllers for both constant and non-constant HSs. We validate our method’s soundness, and confirm its relative completeness for a certain subclass of HSs. Experiment results affirms the efficacy of our approach.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_15"}, {"primary_key": "586916", "vector": [], "sparse_vector": [], "title": "Proving Functional Program Equivalence via Directed Lemma Synthesis.", "authors": ["Yican Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractProving equivalence between functional programs is a fundamental problem in program verification, which often amounts to reasoning about algebraic data types (ADTs) and compositions of structural recursions. Modern theorem provers provide structural induction for such reasoning, but a structural induction on the original theorem is often insufficient for many equivalence theorems. In such cases, one has to invent a set of lemmas, prove these lemmas by additional induction, and use these lemmas to prove the original theorem. There is, however, a lack of systematic understanding of what lemmas are needed for inductive proofs and how these lemmas can be synthesized automatically. This paper presents directed lemma synthesis, an effective approach to automating equivalence proofs by discovering critical lemmas using program synthesis techniques. We first identify two induction-friendly forms of propositions that give formal guarantees to the progress of the proof. We then propose two tactics that synthesize and apply lemmas, thereby transforming the proof goal into induction-friendly forms. Both tactics reduce lemma synthesis to a set of independent and typically small program synthesis problems that can be efficiently solved. Experimental results demonstrate the effectiveness of our approach: Compared to state-of-the-art equivalence checkers employing heuristic-based lemma enumeration, directed lemma synthesis saves 95.47% runtime on average and solves 38 more tasks over an extended version of the standard benchmark set.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_28"}, {"primary_key": "586917", "vector": [], "sparse_vector": [], "title": "Compositional Verification of Cryptographic Circuits Against Fault Injection Attacks.", "authors": ["Hui<PERSON> Tan", "<PERSON>", "Fu Song", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractFault injection attack is a class of active, physical attacks against cryptographic circuits. The design and implementation of countermeasures against such attacks are intricate, error-prone and laborious, necessitating formal verification to guarantee their correctness. In this paper, we propose the first compositional verification approach for round-based hardware implementations of cryptographic algorithms. Our approach decomposes a circuit into a set of single-round sub-circuits which are verified individually by either SAT/SMT- or BDD-based tools. Our approach is implemented as an open-source tool , which is evaluated extensively on realistic cryptographic circuit benchmarks. The experimental results show that our approach is significantly more effective and efficient than the state-of-the-art.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_13"}, {"primary_key": "586918", "vector": [], "sparse_vector": [], "title": "Rigorous Floating-Point Round-Off Error Analysis in PRECiSA 4.0.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "AbstractSmall round-off errors in safety-critical systems can lead to catastrophic consequences. In this context, determining if the result computed by a floating-point program is accurate enough with respect to its ideal real-number counterpart is essential. This paper presents PRECiSA 4.0, a tool that rigorously estimates the accumulated round-off error of a floating-point program. PRECiSA 4.0 combines static analysis, optimization techniques, and theorem proving to provide a modular approach for computing a provably correct round-off error estimation. PRECiSA 4.0 adds several features to previous versions of the tool that enhance its applicability and performance. These features include support for data collections such as lists, records, and tuples; support for recursion schemas; an updated floating-point formalization that closely characterizes the IEEE-754 standard; an efficient and modular analysis of function calls that improves the performances for large programs; and a new user interface integrated into Visual Studio Code.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_2"}, {"primary_key": "586919", "vector": [], "sparse_vector": [], "title": "Automated Repair of Information Flow Security in Android Implicit Inter-App Communication.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "AbstractAndroid’s intents provide a form of inter-app communication with implicit, capability-based matching of senders and receivers. Such kind of implicit addressing provides some much-needed flexibility but also increases the risk of introducing information flow security bugs and vulnerabilities—as there is no standard way to specify what permissions are required to access the data sent through intents, so that it is handled properly.To mitigate such risks of intent-based communication, this paper introduces IntentRepair, an automated technique to detect such information flow security leaks and to automatically repair them. IntentRepair first finds sender and receiver modules that may communicate via intents, and such that the sender sends sensitive information that the receiver forwards to a public channel. To prevent this flow, IntentRepair patches the sender so that it also includes information about the permissions needed to access the data; and the receiver so that it will only disclose the sensitive information if it possesses the required permissions.We evaluated a prototype implementation of IntentRepair on 869 Android open-source apps, showing that it is effective in automatically detecting and repairing information flow security bugs that originate in implicit intent-based communication, introducing only a modest overhead in terms of patch size.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_15"}, {"primary_key": "586920", "vector": [], "sparse_vector": [], "title": "Chamelon : A Delta-Debugger for OCaml.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "AbstractTools that manipulate OCaml code can sometimes fail even on correct programs. Identifying and understanding the cause of the error usually involves manually reducing the size of the program, so as to obtain a shorter program causing the same error—a long, sometimes complex and rarely interesting task. Our work consists in automating this task using a minimiser, or delta-debugger. To do so, we propose a list of unitary heuristics, i.e. small-scale reductions, applied through a dichotomy-based state-of-the-art algorithm. These proposals are implemented in the free Chamelon tool. Although designed to assist the development of an OCaml compiler, Chamelon can be adapted to all kinds of projects that manipulate OCaml code. It can analyse multifile projects and efficiently minimise real-world programs, reducing their size by one to several orders of magnitude. It is currently used to assist the industrial development of the flambda2 optimising compiler.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_6"}, {"primary_key": "586921", "vector": [], "sparse_vector": [], "title": "UnsafeCop: Towards Memory Safety for Real-World Unsafe Rust Code with Practical Bounded Model Checking.", "authors": ["<PERSON><PERSON> Wang", "Jingling Xue", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "AbstractRust has gained popularity as a safer alternative to C/C++ for low-level programming due to its memory-safety features and minimal runtime overhead. However, the use of the “unsafe” keyword allows developers to bypass safety guarantees, posing memory-safety risks. Bounded Model Checking (BMC) is commonly used to detect memory-safety problems, but it has limitations for large-scale programs, as it can only detect bugs within a bounded number of executions.In this paper, we introduce UnsafeCop that utilizes and enhances BMC for analyzing memory safety in real-world unsafe Rust code. Our methodology incorporates harness design, loop bound inference, and both loop and function stubbing for comprehensive analysis. We optimize verification efficiency through a strategic function verification order, leveraging both types of stubbing. We conducted a case study on TECC (Trusted-Environment-based Cryptographic Computing), a proprietary framework consisting of 30,174 lines of Rust code, including 3,019 lines of unsafe Rust code, developed by Ant Group. Experimental results demonstrate that UnsafeCop effectively detects and verifies dozens of memory safety issues, reducing verification time by 73.71% compared to the traditional non-stubbing approach, highlighting its practical effectiveness.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_19"}, {"primary_key": "586922", "vector": [], "sparse_vector": [], "title": "AGVTS: Automated Generation and Verification of Temporal Specifications for Aeronautics SCADE Models.", "authors": ["<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "AbstractSCADE is both a formal language and a model-based development environment, widely used to build and verify the models of safety-critical system (SCS). The SCADE Design Verifier (DV) provides SAT-based verification. However, DV cannot adequately express complex temporal specifications, and it may fail due to complexity problems such as floating numbers which are often used in the aeronautics domain. In addition, manually writing temporal specifications is not only time-consuming but also error-prone. To address these challenges, we propose an AGVTS method that can automate the task of generating temporal specifications and verifying aeronautics SCADE models. At first, we define a modular pattern language for precisely expressing Chinese natural language requirements. Then, we present a rule-based translation augmented with BERT, which translates restricted requirements into LTL and CTL. In addition, SCADE model verification is achieved by transforming it into nuXmv which supports both SMT-based and SAT-based verification. Finally, we illustrate a successful application of our methodology with an ejection seat control system, and convince our industrial partners of the usefulness of formal methods for industrial systems.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_21"}, {"primary_key": "586923", "vector": [], "sparse_vector": [], "title": "Adversarial Robustness Certification for Bayesian Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "AbstractWe study the problem of certifying the robustness of Bayesian neural networks (BNNs) to adversarial input perturbations. Specifically, we define two notions of robustness for BNNs in an adversarial setting: probabilistic robustness and decision robustness. The former deals with the probabilistic behaviour of the network, that is, it ensures robustness across different stochastic realisations of the network, while the latter provides guarantees for the overall (output) decision of the BNN. Although these robustness properties cannot be computed analytically, we present a unified computational framework for efficiently and formally bounding them. Our approach is based on weight interval sampling, integration and bound propagation techniques, and can be applied to BNNs with a large number of parameters independently of the (approximate) inference method employed to train the BNN. We evaluate the effectiveness of our method on tasks including airborne collision avoidance, medical imaging and autonomous driving, demonstrating that it can compute non-trivial guarantees on medium size images (i.e., over 16 thousand input parameters).", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_1"}, {"primary_key": "586924", "vector": [], "sparse_vector": [], "title": "On Completeness of SDP-Based Barrier Certificate Synthesis over Unbounded Domains.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Bican Xia", "<PERSON><PERSON><PERSON>"], "summary": "AbstractBarrier certificates, serving as differential invariants that witness system safety, play a crucial role in the verification of cyber-physical systems (CPS). Prevailing computational methods for synthesizing barrier certificates are based on semidefinite programming (SDP) by exploiting Putinar Positivstellensatz. Consequently, these approaches are limited by the Archimedean condition, which requires all variables to be bounded, i.e., systems are defined over bounded domains. For systems over unbounded domains, unfortunately, existing methods become incomplete and may fail to identify potential barrier certificates.In this paper, we address this limitation for the unbounded cases. We first give a complete characterization of polynomial barrier certificates by using homogenization, a recent technique in the optimization community to reduce an unbounded optimization problem to a bounded one. Furthermore, motivated by this formulation, we introduce the definition of homogenized systems and propose a complete characterization of a family of non-polynomial barrier certificates with more expressive power. Experimental results demonstrate that our two approaches are more effective while maintaining a comparable level of efficiency.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_16"}, {"primary_key": "586925", "vector": [], "sparse_vector": [], "title": "Nonlinear Craig Interpolant Generation Over Unbounded Domains by Separating Semialgebraic Sets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Bican Xia", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "AbstractInterpolation-based techniques become popular in recent years, as they can improve the scalability of existing verification techniques due to their inherent modularity and local reasoning capabilities. Synthesizing Craig interpolants is the cornerstone of these techniques. In this paper, we investigate nonlinear Craig interpolant synthesis for two polynomial formulas of the general form, essentially corresponding to the underlying mathematical problem to separate two disjoint semialgebraic sets. By combining the homogenization approach with existing techniques, we prove the existence of a novel class of non-polynomial interpolants called semialgebraic interpolants. These semialgebraic interpolants subsume polynomial interpolants as a special case. To the best of our knowledge, this is the first existence result of this kind. Furthermore, we provide complete sum-of-squares characterizations for both polynomial and semialgebraic interpolants, which can be efficiently solved as semidefinite programs. Examples are provided to demonstrate the effectiveness and efficiency of our approach.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_5"}, {"primary_key": "586926", "vector": [], "sparse_vector": [], "title": "Combining Classical and Probabilistic Independence Reasoning to Verify the Security of Oblivious Algorithms.", "authors": ["Pengbo Yan", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "AbstractWe consider the problem of how to verify the security of probabilistic oblivious algorithms formally and systematically. Unfortunately, prior program logics fail to support a number of complexities that feature in the semantics and invariants needed to verify the security of many practical probabilistic oblivious algorithms. We propose an approach based on reasoning over perfectly oblivious approximations, using a program logic that combines both classical Hoare logic reasoning and probabilistic independence reasoning to support all the needed features. We formalise and prove our new logic sound in Isabelle/HOL and apply our approach to formally verify the security of several challenging case studies beyond the reach of prior methods for proving obliviousness.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_10"}, {"primary_key": "586927", "vector": [], "sparse_vector": [], "title": "Partially Observable Stochastic Games with Neural Perception Mechanisms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "AbstractStochastic games are a well established model for multi-agent sequential decision making under uncertainty. In practical applications, though, agents often have only partial observability of their environment. Furthermore, agents increasingly perceive their environment using data-driven approaches such as neural networks trained on continuous data. We propose the model of neuro-symbolic partially-observable stochastic games (NS-POSGs), a variant of continuous-space concurrent stochastic games that explicitly incorporates neural perception mechanisms. We focus on a one-sided setting with a partially-informed agent using discrete, data-driven observations and another, fully-informed agent. We present a new method, called one-sided NS-HSVI, for approximate solution of one-sided NS-POSGs, which exploits the piecewise constant structure of the model. Using neural network pre-image analysis to construct finite polyhedral representations and particle-based representations for beliefs, we implement our approach and illustrate its practical applicability to the analysis of pedestrian-vehicle and pursuit-evasion scenarios.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_19"}, {"primary_key": "586928", "vector": [], "sparse_vector": [], "title": "CauMon: An Informative Online Monitor for Signal Temporal Logic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractIn this paper, we present a tool for monitoring the traces of cyber-physical systems (CPS) at runtime, with respect to Signal Temporal Logic (STL) specifications. Our tool is based on the recent advances of causation monitoring, which reports not only whether an executing trace violates the specification, but also how relevant the increment of the trace at each instant is to the specification violation. In this way, it can deliver more information about system evolution than classic online robust monitors. Moreover, by adapting two dynamic programming strategies, our implementation significantly improves the efficiency of causation monitoring, allowing its deployment in practice. The tool is implemented as a executable and can be easily adapted to monitor CPS in different formalisms. We evaluate the efficiency of the proposed monitoring tool, and demonstrate its superiority over existing robust monitors in terms of the information it can deliver about system evolution.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_18"}, {"primary_key": "586929", "vector": [], "sparse_vector": [], "title": "Certified Quantization Strategy Synthesis for Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Fu Song", "<PERSON>", "<PERSON>"], "summary": "Abstract Quantization plays an important role in deploying neural networks on embedded, real-time systems with limited computing and storage resources (e.g., edge devices). It significantly reduces the model storage cost and improves inference efficiency by using fewer bits to represent the parameters. However, it was recently shown that critical properties may be broken after quantization, such as robustness and backdoor-freeness. In this work, we introduce the first method for synthesizing quantization strategies that verifiably maintain desired properties after quantization, leveraging a key insight that quantization leads to a data distribution shift in each layer. We propose to compute the preimage for each layer based on which the preceding layer is quantized, ensuring that the quantized reachable region of the preceding layer remains within the preimage. To tackle the challenge of computing the exact preimage, we propose an MILP-based method to compute its under-approximation. We implement our method into a tool and demonstrate its effectiveness and efficiency by providing certified quantization that successfully preserves model robustness and backdoor-freeness.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71162-6_18"}, {"primary_key": "586930", "vector": [], "sparse_vector": [], "title": "Tolerance of Reinforcement Learning Controllers Against Deviations in Cyber Physical Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AbstractCyber-physical systems (CPS) with reinforcement learning (RL)-based controllers are increasingly being deployed in complex physical environments such as autonomous vehicles, the Internet-of-Things (IoT), and smart cities. An important property of a CPS is tolerance; i.e., its ability to function safely under possible disturbances and uncertainties in the actual operation. In this paper, we introduce a new, expressive notion of tolerance that describes how well a controller is capable of satisfying a desired system requirement, specified using Signal Temporal Logic (STL), under possible deviations in the system. Based on this definition, we propose a novel analysis problem, called the tolerance falsification problem, which involves finding small deviations that result in a violation of the given requirement. We present a novel, two-layer simulation-based analysis framework and a novel search heuristic for finding small tolerance violations. To evaluate our approach, we construct a set of benchmark problems where system parameters can be configured to represent different types of uncertainties and disturbances in the system. Our evaluation shows that our falsification approach and heuristic can effectively find small tolerance violations.", "published": "2024-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-71177-0_17"}]