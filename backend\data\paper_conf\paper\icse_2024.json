[{"primary_key": "632831", "vector": [], "sparse_vector": [], "title": "Compiler-directed Migrating API Callsite of Client Code.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "API developers evolve software libraries to fix bugs, add new features, or refactor code, but the evolution can introduce API-breaking changes (e.g., API renaming). To benefit from such evolution, the programmers of client projects have to repetitively upgrade the callsites of libraries, since API-breaking changes introduce many compilation errors. It is tedious and error-prone to resolve such errors, especially when programmers are often unfamiliar with the API usages of newer versions. To migrate client code, the prior approaches either mine API mappings or learn edit scripts, but both the research lines have inherent limitations. For example, mappings alone cannot handle complex cases, and there is no sufficient source (e.g., migration commits) for learning edit scripts.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639084"}, {"primary_key": "632832", "vector": [], "sparse_vector": [], "title": "CSChecker: Revisiting GDPR and CCPA Compliance of Cookie Banners on the Web.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Privacy regulations like GDPR and CCPA have greatly affected online advertising and tracking strategies. To comply with the regulations, websites need to display consent management UIs (i.e., cookie banners) implemented under the corresponding technical frameworks, allowing users to specify consents regarding their personal data processing. Although prior works have investigated the cookie banner compliance problems with GDPR, the technical specification has significantly changed. The compliance status under the latest framework remains unclear. There also lacks a systematic study of CCPA banner compliance. More importantly, most work have focused on detecting the regulation violations, whereas little is known about the possible culprits and causes.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639159"}, {"primary_key": "632834", "vector": [], "sparse_vector": [], "title": "Verifying Declarative Smart Contracts.", "authors": ["<PERSON><PERSON><PERSON>", "Lan <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Smart contracts manage a large number of digital assets nowadays. Bugs in these contracts have led to significant financial loss. Verifying the correctness of smart contracts is, therefore, an important task. This paper presents an automated safety verification tool, DCV, that targets declarative smart contracts written in De-Con, a logic-based domain-specific language for smart contract implementation and specification. DCV proves safety properties by mathematical induction and can automatically infer inductive invariants using heuristic patterns, without annotations from the developer. Our evaluation on 23 benchmark contracts shows that DCV is effective in verifying smart contracts adapted from public repositories, and can verify contracts not supported by other tools. Furthermore, DCV significantly outperforms baseline tools in verification time.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639203"}, {"primary_key": "632835", "vector": [], "sparse_vector": [], "title": "Raisin: Identifying Rare Sensitive Functions for Bug Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Pan Bian"], "summary": "Mastering the knowledge about the bug-prone functions (i.e., sensitive functions) is important to detect bugs. Some automated techniques have been proposed to identify the sensitive functions in large software systems, based on machine learning or natural language processing. However, the existing statistics-based techniques are not directly applicable to a special kind of sensitive functions, i.e., the rare sensitive functions, which have very few invocations even in large systems. Unfortunately, the rare ones can also introduce bugs. Therefore, how to effectively identify such functions is a problem deserving attention.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639165"}, {"primary_key": "632839", "vector": [], "sparse_vector": [], "title": "RPG: Rust Library Fuzzing with Pool-based Fuzz Target Generation and Generic Support.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Rust libraries are ubiquitous in Rust-based software development. Guaranteeing their correctness and reliability requires thorough analysis and testing. Fuzzing is a popular bug-finding solution, yet it requires writing fuzz targets for libraries. Recently, some automatic fuzz target generation methods have been proposed. However, two challenges remain: (1) how to generate diverse API sequences that prioritize unsafe code and interactions to reveal bugs in Rust libraries; (2) how to provide support for the generic APIs and verify both syntactic and semantic validity of the fuzz targets to enable more comprehensive testing of Rust libraries. In this paper, we propose RPG, an automatic fuzz target synthesis technique to support Rust library fuzzing. RPG uses a pool-based search to generate diverse and unsafe API sequences, and synthesizes fuzz targets with generic support and validity check. The experimental results demonstrate that RPG enhances both the quality of the generated fuzz targets and the bug-finding ability through pool-based generation and generic support, substantially outperforming the state-of-the-art. Moreover, RPG has discovered 25 previously unknown bugs from 50 well-known Rust libraries available on Crates.io.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639102"}, {"primary_key": "632840", "vector": [], "sparse_vector": [], "title": "GPTScan: Detecting Logic Vulnerabilities in Smart Contracts by Combining GPT with Program Analysis.", "authors": ["<PERSON><PERSON>ang Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Smart contracts are prone to various vulnerabilities, leading to substantial financial losses over time. Current analysis tools mainly target vulnerabilities with fixed control- or data-flow patterns, such as re-entrancy and integer overflow. However, a recent study on Web3 security bugs revealed that about 80% of these bugs cannot be audited by existing tools due to the lack of domain-specific property description and checking. Given recent advances in Large Language Models (LLMs), it is worth exploring how Generative Pre-training Transformer (GPT) could aid in detecting logic vulnerabilities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639117"}, {"primary_key": "632842", "vector": [], "sparse_vector": [], "title": "Energy Patterns for Web: An Exploratory Study.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As the energy footprint generated by software is increasing at an alarming rate, understanding how to develop energy-efficient applications has become a necessity. Previous work has introduced catalogs of coding practices, also known as energy patterns. These patterns are yet limited to Mobile or third-party libraries. In this study, we focus on the Web domain---a main source of energy consumption. First we investigated whether and how Mobile energy patterns can be ported to this domain and found that 20 patterns could be ported. Then, we interviewed six expert web developers from different companies to challenge the ported patterns. Most developers expressed concerns for antipatterns, specifically with functional antipatterns, and were able to formulate guidelines to locate these patterns in the source code. Finally, to quantify the effect of Web energy patterns on energy consumption, we set up an automated pipeline to evaluate two ported patterns: 'Dynamic Retry Delay' (DRD) and 'Open Only When Necessary' (OOWN). With this, we found no evidence that the DRD pattern consumes less energy than its antipattern, while the opposite is true for OOWN. Data and Material: https://doi.org/10.5281/zenodo.8404487", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640110"}, {"primary_key": "632846", "vector": [], "sparse_vector": [], "title": "Precise Sparse Abstract Execution via Cross-Domain Interaction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sparse static analysis offers a more scalable solution compared to its non-sparse counterpart. The basic idea is to first conduct a fast pointer analysis that over-approximates the value-flows and propagates the data-flow facts sparsely along only the pre-computed value-flows instead of all control flow points. Current sparse techniques focus on improving the scalability of the main analysis while maintaining its precision. However, their pointer analyses in both the offline and main phases are inherently imprecise because they rely solely on a single memory address domain without considering values from other domains like the interval domain. Consequently, this leads to conservative alias results, like arrayinsensitivity, which leaves substantial room for precision improvement of the main data-flow analysis.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639220"}, {"primary_key": "632848", "vector": [], "sparse_vector": [], "title": "Unveiling Memorization in Code Models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Han", "<PERSON>"], "summary": "The availability of large-scale datasets, advanced architectures, and powerful computational resources have led to effective code models that automate diverse software engineering activities. The datasets usually consist of billions of lines of code from both open-source and private repositories. A code model memorizes and produces source code verbatim, which potentially contains vulnerabilities, sensitive information, or code with strict licenses, leading to potential security and privacy issues. This paper investigates an important problem: to what extent do code models memorize their training data? We conduct an empirical study to explore memorization in large pre-trained code models. Our study highlights that simply extracting 20,000 outputs (each having 512 tokens) from a code model can produce over 40,125 code snippets that are memorized from the training data. To provide a better understanding, we build a taxonomy of memorized contents with 3 categories and 14 subcategories. The results show that the prompts sent to the code models affect the distribution of memorized contents. We identify several key factors of memorization. Specifically, given the same architecture, larger models suffer more from memorization problem. A code model produces more memorization when it is allowed to generate longer outputs. We also find a strong positive correlation between the number of an output's occurrences in the training data and that in the generated outputs, which indicates that a potential way to reduce memorization is to remove duplicates in the training data. We then identify effective metrics that infer whether an output contains memorization accurately. We also make suggestions to deal with memorization.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639074"}, {"primary_key": "632849", "vector": [], "sparse_vector": [], "title": "Enabling Runtime Verification of Causal Discovery Algorithms with Automated Conditional Independence Reasoning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Causal discovery is a powerful technique for identifying causal relationships among variables in data. It has been widely used in various applications in software engineering. Causal discovery extensively involves conditional independence (CI) tests. Hence, its output quality highly depends on the performance of CI tests, which can often be unreliable in practice. Moreover, privacy concerns arise when excessive CI tests are performed.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623348"}, {"primary_key": "632851", "vector": [], "sparse_vector": [], "title": "Mozi: Discovering DBMS Bugs via Configuration-Based Equivalent Transformation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jingzhou Fu", "<PERSON><PERSON><PERSON>", "Chengnian Sun", "Yu <PERSON>"], "summary": "Testing database management systems (DBMSs) is a complex task. Traditional approaches, such as metamorphic testing, need a precise comprehension of the SQL specification to create diverse inputs with equivalent semantics. The vagueness and intricacy of the SQL specification make it challenging to accurately model query semantics, thereby posing difficulties in testing the correctness and performance of DBMSs. To address this, we propose Mozi, a framework that finds DBMS bugs via configuration-based equivalent transformation. The key idea behind <PERSON><PERSON> is to compare the results of equivalent DBMSs with different configurations, rather than between semantically equivalent queries. The framework involves analyzing the query plan, changing configurations to transform the DBMS to an equivalent one, and re-executing the query to compare the results using various test oracles. For example, detecting differences in query results indicates correctness bugs, while observing faster execution times on the optimization-closed DBMS suggests performance bugs.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639112"}, {"primary_key": "632852", "vector": [], "sparse_vector": [], "title": "REDriver: Runtime Enforcement for Autonomous Vehicles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>"], "summary": "Autonomous driving systems (ADSs) integrate sensing, perception, drive control, and several other critical tasks in autonomous vehicles, motivating research into techniques for assessing their safety. While there are several approaches for testing and analysing them in high-fidelity simulators, ADSs may still encounter additional critical scenarios beyond those covered once they are deployed on real roads. An additional level of confidence can be established by monitoring and enforcing critical properties when the ADS is running. Existing work, however, is only able to monitor simple safety properties (e.g., avoidance of collisions) and is limited to blunt enforcement mechanisms such as hitting the emergency brakes. In this work, we propose REDriver, a general and modular approach to runtime enforcement, in which users can specify a broad range of properties (e.g., national traffic laws) in a specification language based on signal temporal logic (STL). REDriver monitors the planned trajectory of the ADS based on a quantitative semantics of STL, and uses a gradient-driven algorithm to repair the trajectory when a violation of the specification is likely. We implemented REDriver for two versions of Apollo (i.e., a popular ADS), and subjected it to a benchmark of violations of Chinese traffic laws. The results show that REDriver significantly improves Apollo's conformance to the specification with minimal overhead.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639151"}, {"primary_key": "632854", "vector": [], "sparse_vector": [], "title": "Make LLM a Testing Expert: Bringing Human-like Interaction to Mobile GUI Testing via Functionality-aware Decisions.", "authors": ["<PERSON><PERSON>", "Chunyang Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Automated Graphical User Interface (GUI) testing plays a crucial role in ensuring app quality, especially as mobile applications have become an integral part of our daily lives. Despite the growing popularity of learning-based techniques in automated GUI testing due to their ability to generate human-like interactions, they still suffer from several limitations, such as low testing coverage, inadequate generalization capabilities, and heavy reliance on training data. Inspired by the success of Large Language Models (LLMs) like ChatGPT in natural language understanding and question answering, we formulate the mobile GUI testing problem as a Q&A task. We propose GPTDroid, asking LLM to chat with the mobile apps by passing the GUI page information to LLM to elicit testing scripts, and executing them to keep passing the app feedback to LLM, iterating the whole process. Within this framework, we have also introduced a functionality-aware memory prompting mechanism that equips the LLM with the ability to retain testing knowledge of the whole process and conduct long-term, functionality-based reasoning to guide exploration. We evaluate it on 93 apps from Google Play and demonstrate that it outperforms the best baseline by 32% in activity coverage, and detects 31% more bugs at a faster rate. Moreover, GPTDroid identifies 53 new bugs on Google Play, of which 35 have been confirmed and fixed.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639180"}, {"primary_key": "632855", "vector": [], "sparse_vector": [], "title": "Testing the Limits: Unusual Text Inputs Generation for Mobile App Crash Detection with Large Language Model.", "authors": ["<PERSON><PERSON>", "Chunyang Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile applications have become a ubiquitous part of our daily life, providing users with access to various services and utilities. Text input, as an important interaction channel between users and applications, plays an important role in core functionality such as search queries, authentication, messaging, etc. However, certain special text (e.g., -18 for Font Size) can cause the app to crash, and generating diversified unusual inputs for fully testing the app is highly demanded. Nevertheless, this is also challenging due to the combination of explosion dilemma, high context sensitivity, and complex constraint relations. This paper proposes InputBlaster which leverages the LLM to automatically generate unusual text inputs for mobile app crash detection. It formulates the unusual inputs generation problem as a task of producing a set of test generators, each of which can yield a batch of unusual text inputs under the same mutation rule. In detail, InputBlaster leverages LLM to produce the test generators together with the mutation rules serving as the reasoning chain, and utilizes the in-context learning schema to demonstrate the LLM with examples for boosting the performance. InputBlaster is evaluated on 36 text input widgets with cash bugs involving 31 popular Android apps, and results show that it achieves 78% bug detection rate, with 136% higher than the best baseline. Besides, we integrate it with the automated GUI testing tool and detect 37 unseen crashes in real-world apps.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639118"}, {"primary_key": "632856", "vector": [], "sparse_vector": [], "title": "On the Effectiveness of Function-Level Vulnerability Detectors for Inter-Procedural Vulnerabilities.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Deqing Zou", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software vulnerabilities are a major cyber threat and it is important to detect them. One important approach to detecting vulnerabilities is to use deep learning while treating a program function as a whole, known as function-level vulnerability detectors. However, the limitation of this approach is not understood. In this paper, we investigate its limitation in detecting one class of vulnerabilities known as inter-procedural vulnerabilities, where the to-be-patched statements and the vulnerability-triggering statements belong to different functions. For this purpose, we create the first Inter-Procedural Vulnerability Dataset (InterPVD) based on C/C++ open-source software, and we propose a tool dubbed VulTrigger for identifying vulnerability-triggering statements across functions. Experimental results show that VulTrigger can effectively identify vulnerability-triggering statements and inter-procedural vulnerabilities. Our findings include: (i) inter-procedural vulnerabilities are prevalent with an average of 2.8 inter-procedural layers; and (ii) function-level vulnerability detectors are much less effective in detecting to-be-patched functions of inter-procedural vulnerabilities than detecting their counterparts of intra-procedural vulnerabilities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639218"}, {"primary_key": "632858", "vector": [], "sparse_vector": [], "title": "Causal Relationships and Programming Outcomes: A Transcranial Magnetic Stimulation Experiment.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Understanding the relationship between cognition and programming outcomes is important: it can inform interventions that help novices become experts faster. Neuroimaging techniques can measure brain activity, but prior studies of programming report only correlations. We present the first causal neurological investigation of the cognition of programming by using Transcranial Magnetic Stimulation (TMS). TMS permits temporary and noninvasive disruption of specific brain regions. By disrupting brain regions and then measuring programming outcomes, we discover whether a true causal relationship exists. To the best of our knowledge, this is the first use of TMS to study software engineering.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639096"}, {"primary_key": "632860", "vector": [], "sparse_vector": [], "title": "Inferring Data Preconditions from Deep Learning Models for Trustworthy Prediction in Deployment.", "authors": ["<PERSON><PERSON><PERSON>", "Hongyang Gao", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning models are trained with certain assumptions about the data during the development stage and then used for prediction in the deployment stage. It is important to reason about the trustworthiness of the model's predictions with unseen data during deployment. Existing methods for specifying and verifying traditional software are insufficient for this task, as they cannot handle the complexity of DNN model architecture and expected outcomes. In this work, we propose a novel technique that uses rules derived from neural network computations to infer data preconditions for a DNN model to determine the trustworthiness of its predictions. Our approach, DeepInfer involves introducing a novel abstraction for a trained DNN model that enables weakest precondition reasoning using Dijkstra's Predicate Transformer Semantics. By deriving rules over the inductive type of neural network abstract representation, we can overcome the matrix dimensionality issues that arise from the backward non-linear computation from the output layer to the input layer. We utilize the weakest precondition computation using rules of each kind of activation function to compute layer-wise precondition from the given postcondition on the final output of a deep neural network. We extensively evaluated DeepInfer on 29 real-world DNN models using four different datasets collected from five different sources and demonstrated the utility, effectiveness, and performance improvement over closely related work. DeepInfer efficiently detects correct and incorrect predictions of high-accuracy models with high recall (0.98) and high F-1 score (0.84) and has significantly improved over the prior technique, SelfChecker. The average runtime overhead of DeepInfer is low, 0.22 sec for all the unseen datasets. We also compared runtime overhead using the same hardware settings and found that DeepInfer is 3.27 times faster than SelfChecker, the state-of-the-art in this area.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623333"}, {"primary_key": "632861", "vector": [], "sparse_vector": [], "title": "Automatic Semantic Augmentation of Language Model Prompts (for Code Summarization).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Large Language Models (LLM) are a new class of computation engines, \"programmed\" via prompt engineering. Researchers are still learning how to best \"program\" these LLMs to help developers. We start with the intuition that developers tend to consciously and unconsciously collect semantics facts, from the code, while working. Mostly these are shallow, simple facts arising from a quick read. For a function, such facts might include parameter and local variable names, return expressions, simple pre- and post-conditions, and basic control and data flow, etc.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639183"}, {"primary_key": "632864", "vector": [], "sparse_vector": [], "title": "Traces of Memorisation in Large Language Models for Code.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large language models have gained significant popularity because of their ability to generate human-like text and potential applications in various fields, such as Software Engineering. Large language models for code are commonly trained on large unsanitised corpora of source code scraped from the internet. The content of these datasets is memorised and can be extracted by attackers with data extraction attacks. In this work, we explore memorisation in large language models for code and compare the rate of memorisation with large language models trained on natural language. We adopt an existing benchmark for natural language and construct a benchmark for code by identifying samples that are vulnerable to attack. We run both benchmarks against a variety of models, and perform a data extraction attack. We find that large language models for code are vulnerable to data extraction attacks, like their natural language counterparts. From the training data that was identified to be potentially extractable we were able to extract 47% from a CodeGen-Mono-16B code completion model. We also observe that models memorise more, as their parameter count grows, and that their pre-training data are also vulnerable to attack. We also find that data carriers are memorised at a higher rate than regular code or documentation and that different model architectures memorise different samples. Data leakage has severe outcomes, so we urge the research community to further investigate the extent of this phenomenon using a wider range of models and extraction techniques in order to build safeguards to mitigate this issue.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639133"}, {"primary_key": "632867", "vector": [], "sparse_vector": [], "title": "Revisiting Android App Categorization.", "authors": ["<PERSON>", "<PERSON>", "Tegawendé F. Bissyandé", "<PERSON>"], "summary": "Numerous tools rely on automatic categorization of Android apps as part of their methodology. However, incorrect categorization can lead to inaccurate outcomes, such as a malware detector wrongly flagging a benign app as malicious. One such example is the SlideIT Free Keyboard app, which has over 500 000 downloads on Google Play. Despite being a \"Keyboard\" app, it is often wrongly categorized alongside \"Language\" apps due to the app's description focusing heavily on language support, resulting in incorrect analysis outcomes, including mislabeling it as a potential malware when it is actually a benign app. Hence, there is a need to improve the categorization of Android apps to benefit all the tools relying on it.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639094"}, {"primary_key": "632869", "vector": [], "sparse_vector": [], "title": "The Classics Never Go Out of Style: An Empirical Study of Downgrades from the Bazel Build Technology.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Software build systems specify how source code is transformed into deliverables. Keeping build systems in sync with the software artifacts that they build while retaining their capacity to quickly produce updated deliverables requires a serious investment of development effort. Enticed by advanced features, several software teams have migrated their build systems to a modern generation of build technologies (e.g., <PERSON><PERSON>, <PERSON>), which aim to reduce the maintenance and execution overhead that build systems impose on development. However, not all migrations lead to perceived improvements, ultimately culminating in abandonment of the build technology. While prior work has focused on upward migration towards more advanced technologies, so-called downgrades, i.e., abandonment of a modern build technology in favour of a traditional one, remains largely unexplored.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639169"}, {"primary_key": "632878", "vector": [], "sparse_vector": [], "title": "Fast Deterministic Black-box Context-free Grammar Inference.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Black-box context-free grammar inference is a hard problem as in many practical settings it only has access to a limited number of example programs. The state-of-the-art approach Arvada heuristically generalizes grammar rules starting from flat parse trees and is non-deterministic to explore different generalization sequences. We observe that many of Arvada's generalization steps violate common language concept nesting rules. We thus propose to pre-structure input programs along these nesting rules, apply learnt rules recursively, and make black-box context-free grammar inference deterministic. The resulting TreeVada yielded faster runtime and higher-quality grammars in an empirical comparison. The TreeVada source code, scripts, evaluation parameters, and training data are open-source and publicly available (https://doi.org/10.6084/m9.figshare.23907738).", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639214"}, {"primary_key": "632879", "vector": [], "sparse_vector": [], "title": "A User-centered Security Evaluation of Copilot.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>. <PERSON>"], "summary": "Code generation tools driven by artificial intelligence have recently become more popular due to advancements in deep learning and natural language processing that have increased their capabilities. The proliferation of these tools may be a double-edged sword because while they can increase developer productivity by making it easier to write code, research has shown that they can also generate insecure code. In this paper, we perform a user-centered evaluation GitHub's Copilot to better understand its strengths and weaknesses with respect to code security. We conduct a user study where participants solve programming problems (with and without Copilot assistance) that have potentially vulnerable solutions. The main goal of the user study is to determine how the use of Copilot affects participants' security performance. In our set of participants (n=25), we find that access to Copilot accompanies a more secure solution when tackling harder problems. For the easier problem, we observe no effect of Copilot access on the security of solutions. We also observe no disproportionate impact of Copilot use on particular kinds of vulnerabilities. Our results indicate that there are potential security benefits to using Copilot, but more research is warranted on the effects of the use of code generation tools on technically complex problems with security requirements.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639154"}, {"primary_key": "632883", "vector": [], "sparse_vector": [], "title": "CERT: Finding Performance Issues in Database Systems Through the Lens of Cardinality Estimation.", "authors": ["Jinsheng Ba", "<PERSON>"], "summary": "Database Management Systems (DBMSs) process a given query by creating a query plan, which is subsequently executed, to compute the query's result. Deriving an efficient query plan is challenging, and both academia and industry have invested decades into researching query optimization. Despite this, DBMSs are prone to performance issues, where a DBMS produces an unexpectedly inefficient query plan that might lead to the slow execution of a query. Finding such issues is a longstanding problem and inherently difficult, because no ground truth information on an expected execution time exists. In this work, we propose Cardinality Estimation Restriction Testing (CERT), a novel technique that finds performance issues through the lens of cardinality estimation. Given a query on a database, CERT derives a more restrictive query (e.g., by replacing a LEFT JOIN with an INNER JOIN), whose estimated number of rows should not exceed the estimated number of rows for the original query. CERT tests cardinality estimation specifically, because it was shown to be the most important part for query optimization; thus, we expect that finding and fixing cardinality-estimation issues might result in the highest performance gains. In addition, we found that other kinds of query optimization issues can be exposed by unexpected estimated cardinalities, which can also be found by CERT. CERT is a black-box technique that does not require access to the source code; DBMSs expose query plans via the EXPLAIN statement. CERT eschews executing queries, which is costly and prone to performance fluctuations. We evaluated CERT on three widely used and mature DBMSs, MySQL, TiDB, and CockroachDB. CERT found 13 unique issues, of which 2 issues were fixed and 9 confirmed by the developers. We expect that this new angle on finding performance bugs will help DBMS developers in improving DMBSs' performance.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639076"}, {"primary_key": "632884", "vector": [], "sparse_vector": [], "title": "Tensor-Aware Energy Accounting.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "With the rapid growth of Artificial Intelligence (AI) applications supported by deep learning (DL), the energy efficiency of these applications has an increasingly large impact on sustainability. We introduce Smaragdine, a new energy accounting system for tensor-based DL programs implemented with TensorFlow. At the heart of Smaragdine is a novel white-box methodology of energy accounting: Smaragdine is aware of the internal structure of the DL program, which we call tensor-aware energy accounting. With Smaragdine, the energy consumption of a DL program can be broken down into units aligned with its logical hierarchical decomposition structure. We apply Smaragdine for understanding the energy behavior of BERT, one of the most widely used language models. Layer-by-layer and tensor-by-tensor, Smaragdine is capable of identifying the highest energy/power-consuming components of BERT. Furthermore, we conduct two case studies on how Smaragdine supports downstream toolchain building, one on the comparative energy impact of hyperparameter tuning of BERT, the other on the energy behavior evolution when BERT evolves to its next generation, ALBERT.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639156"}, {"primary_key": "632896", "vector": [], "sparse_vector": [], "title": "Resource Usage and Optimization Opportunities in Workflows of GitHub Actions.", "authors": ["Islem Bouzenia", "<PERSON>"], "summary": "Continuous integration and continuous delivery (CI/CD) has become a prevalent practice in software development. GitHub Actions is emerging as a popular platform for implementing CI/CD pipelines, called workflows, especially because the platform offers 2,000 minutes of computation for free to public repositories each month. To understand what these resources are used for and whether CI/CD could be more efficient, this paper presents the first comprehensive empirical study of resource usage and optimization opportunities of GitHub Action workflows. Our findings show that CI/CD imposes significant costs, e.g., $504 per year for an average paid-tier repository. The majority of the used resources is consumed by testing and building (91.2%), which is triggered by pull requests (50.7%), pushes (30.9%), and regularly scheduled workflows (15.5%). While existing optimizations, such as caching (adopted by 32.9% of paid-tier repositories), demonstrate a positive impact, they overall remain underutilized. This result underscores the need for enhanced documentation and tools to guide developers toward more resource-efficient workflows. Moreover, we show that relatively simple changes in the platform, such as deactivating scheduled workflows when repositories are inactive, could result in reductions of execution time between 1.1% and 31.6% over the impacted workflows. Overall, we envision our findings to help improve the resource efficiency of CI/CD pipelines.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623303"}, {"primary_key": "632897", "vector": [], "sparse_vector": [], "title": "Supporting Web-Based API Searches in the IDE Using Signatures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Developers frequently use the web to locate API examples that help them solve their programming tasks. While sites like Stack Overflow (SO) contain API examples embedded within their textual descriptions, developers cannot access this API knowledge directly. Instead they need to search for and browse results to select relevant SO posts and then read through individual posts to figure out which answers contain information about the APIs that are relevant to their task. This paper introduces an approach, called Scout, that automatically analyzes search results to extract API signature information. These signatures are used to group and rank examples and allow for a unique API-based presentation that reduces the amount of information the developer needs to consider when looking for API information on the web. This succinct representation enables <PERSON> to be integrated fully within an IDE panel so that developers can search and view API examples without losing context on their development task. <PERSON> also uses this integration to automatically augment queries with contextual information that tailors the developer's queries, and ranks the results according to the developer's needs. In an experiment with 40 developers, we found that <PERSON> reduces the number of queries developers need to perform by 19% and allows them to solve almost half their tasks directly from the API-based representation, reducing the number of complete SO posts viewed by approximately 64%.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639089"}, {"primary_key": "632904", "vector": [], "sparse_vector": [], "title": "Programming Assistant for Exception Handling with CodeBERT.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Genesis Montejo", "<PERSON><PERSON>"], "summary": "With practical code reuse, the code fragments from developers' forums often migrate to applications. Owing to the incomplete nature of such fragments, they often lack the details on exception handling. The adaptation for exception handling to the codebase is not trivial as developers must learn and memorize what API methods could cause exceptions and what exceptions need to be handled. We propose Neurex, an exception handling recommender that learns from complete code, and accepts a given Java code snippet and recommends 1) if a try-catch block is needed, 2) what statements need to be placed in a try block, and 3) what exception types need to be caught in the catch clause. Inspired by the sequence chunking techniques in natural language processing, we design Neurex via a multi-tasking model with the fine-tuning of the large language model CodeBERT for these three exception handling recommendation tasks. Via the large language model, Neurex can learn the surrounding context, leading to better learning the dependencies among the API elements, and the relations between the statements and the corresponding exception types needed to be handled.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639188"}, {"primary_key": "632906", "vector": [], "sparse_vector": [], "title": "Coca: Improving and Explaining Graph Neural Network-Based Vulnerability Detection Systems.", "authors": ["<PERSON><PERSON><PERSON> Cao", "<PERSON>bing Sun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recently, Graph Neural Network (GNN)-based vulnerability detection systems have achieved remarkable success. However, the lack of explainability poses a critical challenge to deploy black-box models in security-related domains. For this reason, several approaches have been proposed to explain the decision logic of the detection model by providing a set of crucial statements positively contributing to its predictions. Unfortunately, due to the weakly-robust detection models and suboptimal explanation strategy, they have the danger of revealing spurious correlations and redundancy issue.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639168"}, {"primary_key": "632907", "vector": [], "sparse_vector": [], "title": "Comprehensive Semantic Repair of Obsolete GUI Test Scripts for Mobile Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Minxue Pan", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graphical User Interface (GUI) testing is one of the primary approaches for testing mobile apps. Test scripts serve as the main carrier of GUI testing, yet they are prone to obsolescence when the GUIs change with the apps' evolution. Existing repair approaches based on GUI layouts or images prove effective when the GUI changes between the base and updated versions are minor, however, they may struggle with substantial changes. In this paper, a novel approach named COSER is introduced as a solution to repairing broken scripts, which is capable of addressing larger GUI changes compared to existing methods. COSER incorporates both external semantic information from the GUI elements and internal semantic information from the source code to provide a unique and comprehensive solution. The efficacy of COSER was demonstrated through experiments conducted on 20 Android apps, resulting in superior performance when compared to the state-of-the-art tools METER and GUIDER. In addition, a tool that implements the COSER approach is available for practical use and future research.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639108"}, {"primary_key": "632909", "vector": [], "sparse_vector": [], "title": "SpecBCFuzz: Fuzzing LTL Solvers with Boundary Conditions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "LTL solvers check the satisfiability of Linear-time Temporal Logic (LTL) formulas and are widely used for verifying and testing critical software systems. Thus, potential bugs in the solvers' implementations can have a significant impact. We present SpecBCFuzz, a fuzzing method for finding bugs in LTL solvers, that is guided by boundary conditions (BCs), corner cases whose (un)satisfiability depends on rare traces. SpecBCFuzz implements a search-based algorithm that fuzzes LTL formulas giving relevance to BCs. It integrates syntactic and semantic similarity metrics to explore the vicinity of the seeded formulas with BCs. We evaluate SpecBCFuzz on 21 different configurations (including the latest and past releases) of four mature and state-of-the-art LTL solvers (NuSMV, Black, Aalta, and PLTL) that implement a diverse set of satisfiability algorithms. SpecBCFuzz produces 368,716 bug-triggering formulas, detecting bugs in 18 out of the 21 solvers' configurations we study. Overall, SpecBCFuzz reveals: soundness issues (wrong answers given by a solver) in Aalta and PLTL; crashes, e.g., segmentation faults, in NuSMV, Black and Aalta; flaky behaviors (different responses across re-runs of the solver on the same formula) in NuSMV and Aalta; performance bugs (large time performance degradation between successive versions of the solver on the same formula) in Black, Aalta and PLTL; and no bug in NuSMV BDD (all versions), suggesting that the latter is currently the most robust solver.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639087"}, {"primary_key": "632911", "vector": [], "sparse_vector": [], "title": "Smart Contract and DeFi Security Tools: Do They Meet the Needs of Practitioners?", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The growth of the decentralized finance (DeFi) ecosystem built on blockchain technology and smart contracts has led to an increased demand for secure and reliable smart contract development. However, attacks targeting smart contracts are increasing, causing an estimated $6.45 billion in financial losses. Researchers have proposed various automated security tools to detect vulnerabilities, but their real-world impact remains uncertain.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623302"}, {"primary_key": "632912", "vector": [], "sparse_vector": [], "title": "Exploiting Library Vulnerability via Migration Based Automating Test Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In software development, developers extensively utilize third-party libraries to avoid implementing existing functionalities. When a new third-party library vulnerability is disclosed, project maintainers need to determine whether their projects are affected by the vulnerability, which requires developers to invest substantial effort in assessment. However, existing tools face a series of issues: static analysis tools produce false alarms, dynamic analysis tools require existing tests and test generation tools have low success rates when facing complex vulnerabilities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639583"}, {"primary_key": "632913", "vector": [], "sparse_vector": [], "title": "Improving Smart Contract Security with Contrastive Learning-based Vulnerability Detection.", "authors": ["Yizhou Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Currently, smart contract vulnerabilities (SCVs) have emerged as a major factor threatening the transaction security of blockchain. Existing state-of-the-art methods rely on deep learning to mitigate this threat. They treat each input contract as an independent entity and feed it into a deep learning model to learn vulnerability patterns by fitting vulnerability labels. It is a pity that they disregard the correlation between contracts, failing to consider the commonalities between contracts of the same type and the differences among contracts of different types. As a result, the performance of these methods falls short of the desired level.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639173"}, {"primary_key": "632914", "vector": [], "sparse_vector": [], "title": "Code Search is All You Need? Improving Code Suggestions with Code Search.", "authors": ["Junkai Chen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern integrated development environments (IDEs) provide various automated code suggestion techniques (e.g., code completion and code generation) to help developers improve their efficiency. Such techniques may retrieve similar code snippets from the code base or leverage deep learning models to provide code suggestions. However, how to effectively enhance the code suggestions using code retrieval has not been systematically investigated. In this paper, we study and explore a retrieval-augmented framework for code suggestions. Specifically, our framework leverages different retrieval approaches and search strategies to search similar code snippets. Then the retrieved code is used to further enhance the performance of language models on code suggestions. We conduct experiments by integrating different language models into our framework and compare the results with their original models. We find that our framework noticeably improves the performance of both code completion and code generation by up to 53.8% and 130.8% in terms of BLEU-4, respectively. Our study highlights that integrating the retrieval process into code suggestions can improve the performance of code suggestions by a large margin.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639085"}, {"primary_key": "632917", "vector": [], "sparse_vector": [], "title": "FlashSyn: Flash Loan Attack Synthesis via Counter Example Driven Approximation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In decentralized finance (DeFi), lenders can offer flash loans to borrowers, i.e., loans that are only valid within a blockchain transaction and must be repaid with fees by the end of that transaction. Unlike normal loans, flash loans allow borrowers to borrow large assets without upfront collaterals deposits. Malicious adversaries use flash loans to gather large assets to exploit vulnerable DeFi protocols.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639190"}, {"primary_key": "632918", "vector": [], "sparse_vector": [], "title": "EGFE: End-to-end Grouping of Fragmented Elements in UI Designs with Multimodal Learning.", "authors": ["Liu<PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "When translating UI design prototypes to code in industry, automatically generating code from design prototypes can expedite the development of applications and GUI iterations. However, in design prototypes without strict design specifications, UI components may be composed of fragmented elements. Grouping these fragmented elements can greatly improve the readability and maintainability of the generated code. Current methods employ a two-stage strategy that introduces hand-crafted rules to group fragmented elements. Unfortunately, the performance of these methods is not satisfying due to visually overlapped and tiny UI elements. In this study, we propose EGFE, a novel method for automatically End-to-end Grouping Fragmented Elements via UI sequence prediction. To facilitate the UI understanding, we innovatively construct a Transformer encoder to model the relationship between the UI elements with multi-modal representation learning. The evaluation on a dataset of 4606 UI prototypes collected from professional UI designers shows that our method outperforms the state-of-the-art baselines in the precision (by 29.75\\%), recall (by 31.07\\%), and F1-score (by 30.39\\%) at edit distance threshold of 4. In addition, we conduct an empirical study to assess the improvement of the generated front-end code. The results demonstrate the effectiveness of our method on a real software engineering application. Our end-to-end fragmented elements grouping method creates opportunities for improving UI-related software engineering tasks.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623313"}, {"primary_key": "632922", "vector": [], "sparse_vector": [], "title": "Attention! Your Copied Data is Under Monitoring: A Systematic Study of Clipboard Usage in Android Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, clipboard usage has become prevalent in mobile apps allowing users to copy and paste text within the same app or across different apps. However, insufficient access control on the clipboard in the mobile operating systems exposes its contained data to high risks where one app can read the data copied in other apps and store it locally or even send it to remote servers. Unfortunately, the literature only has ad-hoc studies in this respect and lacks a comprehensive and systematic study of the entire mobile app ecosystem. To establish the missing links, this paper proposes an automated tool, ClipboardScope, that leverages the principled static program analysis to uncover the clipboard data usage in mobile apps at scale by defining a usage as a combination of two aspects, i.e., how the clipboard data is validated and where does it go. It defines four primary categories of clipboard data operation, namely spot-on, grand-slam, selective, and cherry-pick, based on the clipboard usage in an app. ClipboardScope is evaluated on 26,201 out of a total of 2.2 million mobile apps available on Google Play as of June 2022 that access and process the clipboard text. It identifies 23,948, 848, 1,075, and 330 apps that are recognized as the four designated categories, respectively. In addition, we uncovered a prevalent programming habit of using the SharedPreferences object to store historical data, which can become an unnoticeable privacy leakage channel.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623317"}, {"primary_key": "632923", "vector": [], "sparse_vector": [], "title": "Fairness Improvement with Multiple Protected Attributes: How Far Are We?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Federica Sarro", "<PERSON>"], "summary": "Existing research mostly improves the fairness of Machine Learning (ML) software regarding a single protected attribute at a time, but this is unrealistic given that many users have multiple protected attributes. This paper conducts an extensive study of fairness improvement regarding multiple protected attributes, covering 11 state-of-the-art fairness improvement methods. We analyze the effectiveness of these methods with different datasets, metrics, and ML models when considering multiple protected attributes. The results reveal that improving fairness for a single protected attribute can largely decrease fairness regarding unconsidered protected attributes. This decrease is observed in up to 88.3% of scenarios (57.5% on average). More surprisingly, we find little difference in accuracy loss when considering single and multiple protected attributes, indicating that accuracy can be maintained in the multiple-attribute paradigm. However, the effect on precision and recall when handling multiple protected attributes is about five times and eight times that of a single attribute. This has important implications for future fairness research: reporting only accuracy as the ML performance metric, which is currently common in the literature, is inadequate.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639083"}, {"primary_key": "632925", "vector": [], "sparse_vector": [], "title": "Automatically Detecting Reflow Accessibility Issues in Responsive Web Pages.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many web applications today use responsive design to adjust the view of web pages to match the screen size of end users. People with disabilities often use an alternative view either due to zooming on a desktop device to enlarge text or viewing within a smaller viewport when using assistive technologies. When web pages are not implemented to correctly adjust the page's content across different screen sizes, it can lead to both a loss of content and functionalities between the different versions. Recent studies show that these reflow accessibility issues are among the most prevalent modern web accessibility issues. In this paper, we present a novel automated technique to automatically detect reflow accessibility issues in web pages for keyboard users. The evaluation of our approach on real-world web pages demonstrated its effectiveness in detecting reflow accessibility issues, outperforming state-of-the-art techniques.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639229"}, {"primary_key": "632926", "vector": [], "sparse_vector": [], "title": "What Can Requirements Engineering Do for Emerging System of Systems? Case of Smart Local Energy.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "As software solutions permeate the whole spectrum of human activities, many software systems previously developed to address specific issues suddenly find themselves becoming parts of interlinked and inter-dependent complex systems. This paper discusses a case study of such emergent convergence of software-rich socio-technical systems into an interconnected and integrated energy system-of-systems and outlines some areas where the Requirements Engineering discipline can help guide such a transition.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640108"}, {"primary_key": "632927", "vector": [], "sparse_vector": [], "title": "How Far Are We? The Triumphs and Trials of Generative AI in Learning Software Engineering.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Conversational Generative AI (convo-genAI) is revolutionizing Software Engineering (SE) as engineers and academics embrace this technology in their work. However, there is a gap in understanding the current potential and pitfalls of this technology, specifically in supporting students in SE tasks. In this work, we evaluate through a between-subjects study (N=22) the effectiveness of ChatGPT, a convo-genAI platform, in assisting students in SE tasks. Our study did not find statistical differences in participants' productivity or self-efficacy when using ChatGPT as compared to traditional resources, but we found significantly increased frustration levels. Our study also revealed 5 distinct faults arising from violations of Human-AI interaction guidelines, which led to 7 different (negative) consequences on participants.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639201"}, {"primary_key": "632928", "vector": [], "sparse_vector": [], "title": "PyTy: Repairing Static Type Errors in Python.", "authors": ["<PERSON><PERSON>", "Luca Di Grazia", "<PERSON>"], "summary": "Gradual typing enables developers to annotate types of their own choosing, offering a flexible middle ground between no type annotations and a fully statically typed language. As more and more code bases get type-annotated, static type checkers detect an increasingly large number of type errors. Unfortunately, fixing these errors requires manual effort, hampering the adoption of gradual typing in practice. This paper presents PyTy, an automated program repair approach targeted at statically detectable type errors in Python. The problem of repairing type errors deserves specific attention because it exposes particular repair patterns, offers a warning message with hints about where and how to apply a fix, and because gradual type checking serves as an automatic way to validate fixes. We addresses this problem through three contributions: (i) an empirical study that investigates how developers fix Python type errors, showing a diverse set of fixing strategies with some recurring patterns; (ii) an approach to automatically extract type error fixes, which enables us to create a dataset of 2,766 error-fix pairs from 176 GitHub repositories, named PyTyDefects; (iii) the first learning-based repair technique for fixing type errors in Python. Motivated by the relative data scarcity of the problem, the neural model at the core of PyTy is trained via cross-lingual transfer learning. Our evaluation shows that PyTy offers fixes for ten frequent categories of type errors, successfully addressing 85.4% of 281 real-world errors. This effectiveness outperforms state-of-the-art large language models asked to repair type errors (by 2.1x) and complements a previous technique aimed at type errors that manifest at runtime. Finally, 20 out of 30 pull requests with PyTy-suggested fixes have been merged by developers, showing the usefulness of PyTy in practice.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639184"}, {"primary_key": "632934", "vector": [], "sparse_vector": [], "title": "Understanding Transaction Bugs in Database Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Wensheng Dou", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Transactions are used to guarantee data consistency and integrity in Database Management Systems (DBMSs), and have become an indispensable component in DBMSs. However, faulty designs and implementations of DBMSs' transaction processing mechanisms can introduce transaction bugs, and lead to severe consequences, e.g., incorrect database states and DBMS crashes. An in-depth understanding of real-world transaction bugs can significantly promote effective techniques in combating transaction bugs in DBMSs.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639207"}, {"primary_key": "632935", "vector": [], "sparse_vector": [], "title": "Is unsafe an Achilles&apos; Heel? A Comprehensive Study of Safety Requirements in Unsafe Rust Programming.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Rust is an emerging, strongly-typed programming language focusing on efficiency and memory safety. With increasing projects adopting Rust, knowing how to use Unsafe Rust is crucial for Rust security. We observed that the description of safety requirements needs to be unified in Unsafe Rust programming. Current unsafe API documents in the standard library exhibited variations, including inconsistency and insufficiency. To enhance Rust security, we suggest unsafe API documents to list systematic descriptions of safety requirements for users to follow.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639136"}, {"primary_key": "632937", "vector": [], "sparse_vector": [], "title": "SCTrans: Constructing a Large Public Scenario Dataset for Simulation Testing of Autonomous Driving Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "For the safety assessment of autonomous driving systems (ADS), simulation testing has become an important complementary technique to physical road testing. In essence, simulation testing is a scenario-driven approach, whose effectiveness is highly dependent on the quality of given simulation scenarios. Moreover, simulation scenarios should be encoded into well-formatted files, otherwise, ADS simulation platforms cannot take them as inputs. Without large public datasets of simulation scenario files, both industry and academic applications of ADS simulation testing are hindered.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623350"}, {"primary_key": "632941", "vector": [], "sparse_vector": [], "title": "Exposing Algorithmic Discrimination and Its Consequences in Modern Society: Insights from a Scoping Study.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Algorithmic discrimination is a condition that arises when data-driven software unfairly treats users based on attributes like ethnicity, race, gender, sexual orientation, religion, age, disability, or other personal characteristics. Nowadays, as machine learning gains popularity, cases of algorithmic discrimination are increasingly being reported in several contexts. This study delves into various studies published over the years reporting algorithmic discrimination. We aim to support software engineering researchers and practitioners in addressing this issue by discussing key characteristics of the problem.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640098"}, {"primary_key": "632942", "vector": [], "sparse_vector": [], "title": "NuzzleBug: Debugging Block-Based Programs in Scratch.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "While professional integrated programming environments support developers with advanced debugging functionality, block-based programming environments for young learners often provide no support for debugging at all, thus inhibiting debugging and preventing debugging education. In this paper we introduce NuzzleBug, an extension of the popular block-based programming environment Scratch that provides the missing debugging support. NuzzleBug allows controlling the executions of Scratch programs with classical debugging functionality such as stepping and breakpoints, and it is an omniscient debugger that also allows reverse stepping. To support learners in deriving hypotheses that guide debugging, NuzzleBug is an interrogative debugger that enables to ask questions about executions and provides answers explaining the behavior in question. In order to evaluate NuzzleBug, we survey the opinions of teachers, and study the effects on learners in terms of debugging effectiveness and efficiency. We find that teachers consider NuzzleBug to be useful, and children can use it to debug faulty programs effectively. However, systematic debugging requires dedicated training, and even when NuzzleBug can provide correct answers learners may require further help to comprehend faults and necessary fixes, thus calling for further research on improving debugging techniques and the information they provide.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623331"}, {"primary_key": "632944", "vector": [], "sparse_vector": [], "title": "Safeguarding DeFi Smart Contracts against Oracle Deviations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents OVer, a framework designed to automatically analyze the behavior of decentralized finance (DeFi) protocols when subjected to a \"skewed\" oracle input. OVer firstly performs symbolic analysis on the given contract and constructs a model of constraints. Then, the framework leverages an SMT solver to identify parameters that allow its secure operation. Furthermore, guard statements may be generated for smart contracts that may use the oracle values, thus effectively preventing oracle manipulation attacks. Empirical results show that OVer can successfully analyze all 10 benchmarks collected, which encompass a diverse range of DeFi protocols. Additionally, this paper illustrates that current parameters utilized in the majority of benchmarks are inadequate to ensure safety when confronted with significant oracle deviations. It shows that existing ad-hoc control mechanisms such as introducing delays are often in-sufficient or even detrimental to protect the DeFi protocols against the oracle deviation in the real-world.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639225"}, {"primary_key": "632945", "vector": [], "sparse_vector": [], "title": "Large Language Models are Edge-Case Generators: Crafting Unusual Programs for Fuzzing Deep Learning Libraries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Bugs in Deep Learning (DL) libraries may affect almost all downstream DL applications, and it is crucial to ensure the quality of such systems. It is challenging to generate valid input programs for fuzzing DL libraries, since the input programs need to satisfy both the syntax/semantics of the supported languages (e.g., Python) and the tensor/operator constraints for constructing valid computational graphs. Recently, the TitanFuzz work demonstrates that modern Large Language Models (LLMs) can be directly leveraged to implicitly learn all the language and DL computation constraints to generate valid programs for fuzzing DL libraries (and beyond). However, LLMs tend to generate ordinary programs following similar patterns/tokens with typical programs seen in their massive pre-training corpora (e.g., GitHub), while fuzzing favors unusual inputs that cover edge cases or are unlikely to be manually produced.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623343"}, {"primary_key": "632950", "vector": [], "sparse_vector": [], "title": "TRACED: Execution-aware Pre-training for Source Code.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Ray"], "summary": "Most existing pre-trained language models for source code focus on learning the static code text, typically augmented with static code structures (abstract syntax tree, dependency graphs, etc.). However, program semantics will not be fully exposed before the real execution. Without an understanding of the program execution, statically pre-trained models fail to comprehensively capture the dynamic code properties, such as the branch coverage and the runtime variable values, and they are consequently less effective at code understanding tasks, such as retrieving semantic clones and detecting software vulnerabilities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608140"}, {"primary_key": "632953", "vector": [], "sparse_vector": [], "title": "CIT4DNN: Generating Diverse and Rare Inputs for Neural Networks Using Latent Space Combinatorial Testing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep neural networks (DNN) are being used in a wide range of applications including safety-critical systems. Several DNN test generation approaches have been proposed to generate fault-revealing test inputs. However, the existing test generation approaches do not systematically cover the input data distribution to test DNNs with diverse inputs, and none of the approaches investigate the relationship between rare inputs and faults. We propose cit4dnn, an automated black-box approach to generate DNN test sets that are feature-diverse and that comprise rare inputs. cit4dnn constructs diverse test sets by applying combinatorial interaction testing to the latent space of generative models and formulates constraints over the geometry of the latent space to generate rare and fault-revealing test inputs. Evaluation on a range of datasets and models shows that cit4dnn generated tests are more feature diverse than the state-of-the-art, and can target rare fault-revealing testing inputs more effectively than existing methods.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639106"}, {"primary_key": "632954", "vector": [], "sparse_vector": [], "title": "Development in times of hype: How freelancers explore Generative AI?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The rise of generative AI has led many companies to hire freelancers to harness its potential. However, this technology presents unique challenges to developers who have not previously engaged with it. Freelancers may find these challenges daunting due to the absence of organizational support and their reliance on positive client feedback. In a study involving 52 freelance developers, we identified multiple challenges associated with developing solutions based on generative AI. Freelancers often struggle with aspects they perceive as unique to generative AI such as unpredictability of its output, the occurrence of hallucinations, and the inconsistent effort required due to trial-and-error prompting cycles. Further, the limitations of specific frameworks, such as token limits and long response times, add to the complexity. Hype-related issues, such as inflated client expectations and a rapidly evolving technological ecosystem, further exacerbate the difficulties. To address these issues, we propose Software Engineering for Generative AI (SE4GenAI) and Hype-Induced Software Engineering (HypeSE) as areas where the software engineering community can provide effective guidance. This support is essential for freelancers working with generative AI and other emerging technologies.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639111"}, {"primary_key": "632955", "vector": [], "sparse_vector": [], "title": "Context-Aware Name Recommendation for Field Renaming.", "authors": ["Chun<PERSON> Dong", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Renaming is one of the most popular software refactorings. Although developers may know what the new name should be when they conduct a renaming, it remains valuable for refactoring tools to recommend new names automatically so that developers can simply hit <PERSON><PERSON> and efficiently accept the recommendation to accomplish the refactoring. Consequently, most IDEs automatically recommend new names for renaming refactorings by default. However, the recommendation made by mainstream IDEs is often incorrect. For example, the precision of IntelliJ IDEA in recommending names for field renamings is as low as 6.3%. To improve the accuracy, in this paper, we propose a context-aware lightweight approach (called CARER) to recommend new names for Java field renamings. Different from mainstream IDEs that rely heavily on initializers and data types of the to-be-renamed fields, CARER exploits both dynamic and static contexts of the renamings as well as naming conventions. We evaluate CARER on 1.1K real-world field renamings discovered from open-source applications. Our evaluation results suggest that CARER can significantly improve the state of the practice in recommending new names for field renamings, improving the precision from 6.30% to 61.15%, and recall from 6.30% to 41.50%. Our evaluation results also suggest that CARER is as efficient as IntelliJ IDEA is, making it suitable to be integrated into IDEs.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639195"}, {"primary_key": "632956", "vector": [], "sparse_vector": [], "title": "LibvDiff: Library Version Difference Guided OSS Version Identification in Binaries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Hong Li", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Open-source software (OSS) has been extensively employed to expedite software development, inevitably exposing downstream software to the peril of potential vulnerabilities. Precisely identifying the version of OSS not only facilitates the detection of vulnerabilities associated with it but also enables timely alerts upon the release of 1-day vulnerabilities. However, current methods for identifying OSS versions rely heavily on version strings or constant features, which may not be present in compiled OSS binaries or may not be representative when only function code changes are made. As a result, these methods are often imprecise in identifying the version of OSS binaries being used.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623336"}, {"primary_key": "632958", "vector": [], "sparse_vector": [], "title": "Evaluating Large Language Models in Class-Level Code Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, many large language models (LLMs) have been proposed, showing advanced proficiency in code generation. Meanwhile, many efforts have been dedicated to evaluating LLMs on code generation benchmarks such as HumanEval. Although being very helpful for comparing different LLMs, existing evaluation focuses on a simple code generation scenario (i.e., function-level or statement-level code generation), which mainly asks LLMs to generate one single code unit (e.g., a function or a statement) for the given natural language description. Such evaluation focuses on generating independent and often small-scale code units, thus leaving it unclear how LLMs perform in real-world software development scenarios.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639219"}, {"primary_key": "632960", "vector": [], "sparse_vector": [], "title": "Ripples of a Mutation - An Empirical Study of Propagation Effects in Mutation Testing.", "authors": ["<PERSON> Du", "<PERSON>", "<PERSON>"], "summary": "The mechanics of how a fault reveals itself as a test failure is of keen interest to software researchers and practitioners alike. An improved understanding of how faults translate to failures can guide improvements in broad facets of software testing, ranging from test suite design to automated program repair, which are premised on the understanding that the presence of faults would alter some test executions.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639179"}, {"primary_key": "632963", "vector": [], "sparse_vector": [], "title": "Empirical Study of the Docker Smells Impact on the Image Size.", "authors": ["<PERSON>"], "summary": "Docker, a widely adopted tool for packaging and deploying applications leverages Dockerfiles to build images. However, creating an optimal Dockerfile can be challenging, often leading to \"Docker smells\" or deviations from best practices. This paper presents a study of the impact of 14 Docker smells on the size of Docker images.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639143"}, {"primary_key": "632965", "vector": [], "sparse_vector": [], "title": "ROSInfer: Statically Inferring Behavioral Component Models for ROS-based Robotics Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Robotics systems are complex, safety-critical systems that can consist of hundreds of software components that interact with each other dynamically during run time. Software components of robotics systems often exhibit reactive, periodic, and state-dependent behavior. Incorrect component composition can lead to unexpected behavior, such as components passively waiting for initiation messages that never arrive. Model-based software analysis is a common technique to identify incorrect behavioral composition by checking desired properties of given behavioral models that are based on component state machines. However, writing state machine models for hundreds of software components manually is a labor-intensive process. This motivates work on automated model inference. In this paper, we present an approach to infer behavioral models for systems based on the Robot Operating System (ROS) using static analysis by exploiting assumptions about the usage of the ROS API and ecosystem. Our approach is based on searching for common behavioral patterns that ROS developers use for implementing reactive, periodic, and state-dependent behavior using the ROS framework API. We evaluate our approach and our tool ROSInfer on five complex real-world ROS systems with a total of 534 components. For this purpose we manually created 155 models of components from the source code to be used as a ground truth and available data set for other researchers. ROSInfer can infer causal triggers for 87% of component architectural behaviors in the 534 components.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639206"}, {"primary_key": "632967", "vector": [], "sparse_vector": [], "title": "Automated Program Repair, What Is It Good For? Not Absolutely Nothing!", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Industrial deployments of automated program repair (APR), e.g., at Facebook and Bloomberg, signal a new milestone for this exciting and potentially impactful technology. In these deployments, developers use APR-generated patch suggestions as part of a human-driven debugging process. Unfortunately, little is known about how using patch suggestions affects developers during debugging. This paper conducts a controlled user study with 40 developers with a median of 6 years of experience. The developers engage in debugging tasks on nine naturally-occurring defects in real-world, open-source, Java projects, using Recoder, SimFix, and TBar, three state-of-the-art APR tools. For each debugging task, the developers either have access to the project's tests, or, also, to code suggestions that make all the tests pass. These suggestions are either developer-written or APR-generated, which can be correct or deceptive. Deceptive suggestions, which are a common APR occurrence, make all the available tests pass but fail to generalize to the intended specification. Through a total of 160 debugging sessions, we find that access to a code suggestion significantly increases the odds of submitting a patch. Access to correct APR suggestions increase the odds of debugging success by 14,000% as compared to having access only to tests, but access to deceptive suggestions decrease the odds of success by 65%. Correct suggestions also speed up debugging. Surprisingly, we observe no significant difference in how novice and experienced developers are affected by APR, suggesting that APR may find uses across the experience spectrum. Overall, developers come away with a strong positive impression of APR, suggesting promise for APR-mediated, human-driven debugging, despite existing challenges in APR-generated repair quality.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639095"}, {"primary_key": "632971", "vector": [], "sparse_vector": [], "title": "Fine-grained, accurate and scalable source differencing.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Understanding code changes is of crucial importance in a wide range of software evolution activities. The traditional approach is to use textual differencing, as done with success since the 1970s with the ubiquitous diff tool. However, textual differencing has the important limitation of not aligning the changes to the syntax of the source code. To overcome these issues, structural (i.e. syntactic) differencing has been proposed in the literature, notably GumTree which was one of the pioneering approaches. The main drawback of GumTree's algorithm is the use of an optimal, but expensive tree-edit distance algorithm that makes it difficult to diff large ASTs. In this article, we describe a less expensive heuristic that enables GumTree to scale to large ASTs while yielding results of better quality than the original GumTree. We validate this new heuristic against 4 datasets of changes in two different languages, where we generate edit-scripts with a median size 50% smaller and a total speedup of the matching time between 50x and 281x.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639148"}, {"primary_key": "632973", "vector": [], "sparse_vector": [], "title": "&quot;My GitHub Sponsors profile is live!&quot; Investigating the Impact of Twitter/X Mentions on GitHub Sponsors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Hidea<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "GitHub Sponsors was launched in 2019, enabling donations to open-source software developers to provide financial support, as per GitHub's slogan: \"Invest in the projects you depend on\". However, a 2022 study on GitHub Sponsors found that only two-fifths of developers who were seeking sponsorship received a donation. The study found that, other than internal actions (such as offering perks to sponsors), developers had advertised their GitHub Sponsors profiles on social media, such as Twitter (also known as X). Therefore, in this work, we investigate the impact of tweets that contain links to GitHub Sponsors profiles on sponsorship, as well as their reception on Twitter/X. We further characterize these tweets to understand their context and find that (1) such tweets have the impact of increasing the number of sponsors acquired, (2) compared to other donation platforms such as Open Collective and Patreon, GitHub Sponsors has significantly fewer interactions but is more visible on Twitter/X, and (3) developers tend to contribute more to open-source software during the week of posting such tweets. Our findings are the first step toward investigating the impact of social media on obtaining funding to sustain open-source software.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639127"}, {"primary_key": "632974", "vector": [], "sparse_vector": [], "title": "Novelty Begets Popularity, But Curbs Participation - A Macroscopic View of the Python Open-Source Ecosystem.", "authors": ["Hongbo Fang", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Who creates the most innovative open-source software projects? And what fate do these projects tend to have? Building on a long history of research to understand innovation in business and other domains, as well as recent advances towards modeling innovation in scientific research from the science of science field, in this paper we adopt the analogy of innovation as emerging from the novel recombination of existing bits of knowledge. As such, we consider as innovative the software projects that recombine existing software libraries in novel ways, i.e., those built on top of atypical combinations of packages as extracted from import statements. We then report on a large-scale quantitative study of innovation in the Python open-source software ecosystem. Our results show that higher levels of innovativeness are statistically associated with higher GitHub star counts, i.e., novelty begets popularity. At the same time, we find that controlling for project size, the more innovative projects tend to involve smaller teams of contributors, as well as be at higher risk of becoming abandoned in the long term. We conclude that innovation and open source sustainability are closely related and, to some extent, antagonistic.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608142"}, {"primary_key": "632976", "vector": [], "sparse_vector": [], "title": "Prompting Is All You Need: Automated Android Bug Replay with Large Language Models.", "authors": ["<PERSON><PERSON> Feng", "Chunyang Chen"], "summary": "Bug reports are vital for software maintenance that allow users to inform developers of the problems encountered while using the software. As such, researchers have committed considerable resources toward automating bug replay to expedite the process of software maintenance. Nonetheless, the success of current automated approaches is largely dictated by the characteristics and quality of bug reports, as they are constrained by the limitations of manually-crafted patterns and pre-defined vocabulary lists. Inspired by the success of Large Language Models (LLMs) in natural language understanding, we propose AdbGPT, a new lightweight approach to automatically reproduce the bugs from bug reports through prompt engineering, without any training and hard-coding effort. AdbGPT leverages few-shot learning and chain-of-thought reasoning to elicit human knowledge and logical reasoning from LLMs to accomplish the bug replay in a manner similar to a developer. Our evaluations demonstrate the effectiveness and efficiency of our AdbGPT to reproduce 81.3% of bug reports in 253.6 seconds, outperforming the state-of-the-art baselines and ablation studies. We also conduct a small-scale user study to confirm the usefulness of AdbGPT in enhancing developers' bug replay capabilities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608137"}, {"primary_key": "632977", "vector": [], "sparse_vector": [], "title": "Analyzing and Debugging Normative Requirements via Satisfiability Checking.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Victória Oldemburgo de Mello", "Beverley A. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Cal<PERSON> I<PERSON>", "Genaín<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As software systems increasingly interact with humans in application domains such as transportation and healthcare, they raise concerns related to the social, legal, ethical, empathetic, and cultural (SLEEC) norms and values of their stakeholders. Normative non-functional requirements (N-NFRs) are used to capture these concerns by setting SLEEC-relevant boundaries for system behavior. Since N-NFRs need to be specified by multiple stakeholders with widely different, non-technical expertise (ethicists, lawyers, regulators, end users, etc.), N-NFR elicitation is very challenging. To address this difficult task, we introduce N-Check, a novel tool-supported formal approach to N-NFR analysis and debugging. N-Check employs satisfiability checking to identify a broad spectrum of N-NFR well-formedness issues, such as conflicts, redundancy, restrictiveness, and insufficiency, yielding diagnostics that pinpoint their causes in a user-friendly way that enables non-technical stakeholders to understand and fix them. We show the effectiveness and usability of our approach through nine case studies in which teams of ethicists, lawyers, philosophers, psychologists, safety analysts, and engineers used N-Check to analyse and debug 233 N-NFRs, comprising 62 issues for the software underpinning the operation of systems, such as, assistive-care robots and tree-disease detection drones to manufacturing collaborative robots.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639093"}, {"primary_key": "632979", "vector": [], "sparse_vector": [], "title": "Machine Learning is All You Need: A Simple Token-based Approach for Effective Code Clone Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Deqing Zou", "<PERSON>", "<PERSON>"], "summary": "As software engineering advances and the code demand rises, the prevalence of code clones has increased. This phenomenon poses risks like vulnerability propagation, underscoring the growing importance of code clone detection techniques. While numerous code clone detection methods have been proposed, they often fall short in real-world code environments. They either struggle to identify code clones effectively or demand substantial time and computational resources to handle complex clones. This paper introduces a code clone detection method namely Toma using tokens and machine learning. Specifically, we extract token type sequences and employ six similarity calculation methods to generate feature vectors. These vectors are then input into a trained machine learning model for classification. To evaluate the effectiveness and scalability of Toma, we conduct experiments on the widely used BigCloneBench dataset. Results show that our tool outperforms token-based code clone detectors and most tree-based clone detectors, demonstrating high effectiveness and significant time savings.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639114"}, {"primary_key": "632981", "vector": [], "sparse_vector": [], "title": "ReFAIR: Toward a Context-Aware Recommender for Fairness Requirements Engineering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine learning (ML) is increasingly being used as a key component of most software systems, yet serious concerns have been raised about the fairness of ML predictions. Researchers have been proposing novel methods to support the development of fair machine learning solutions. Nonetheless, most of them can only be used in late development stages, e.g., during model training, while there is a lack of methods that may provide practitioners with early fairness analytics enabling the treatment of fairness throughout the development lifecycle. This paper proposes ReFair, a novel context-aware requirements engineering framework that allows to classify sensitive features from User Stories. By exploiting natural language processing and word embedding techniques, our framework first identifies both the use case domain and the machine learning task to be performed in the system being developed; afterward, it recommends which are the context-specific sensitive features to be considered during the implementation. We assess the capabilities of ReFair by experimenting it against a synthetic dataset---which we built as part of our research---composed of 12,401 User Stories related to 34 application domains. Our findings showcase the high accuracy of ReFair, other than highlighting its current limitations.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639185"}, {"primary_key": "632987", "vector": [], "sparse_vector": [], "title": "Block-based Programming for Two-Armed Robots: A Comparative Study.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Programming industrial robots is difficult and expensive. Although recent work has made substantial progress in making it accessible to a wider range of users, it is often limited to simple programs and its usability remains untested in practice. In this article, we introduce Duplo, a block-based programming environment that allows end-users to program two-armed robots and solve tasks that require coordination. <PERSON><PERSON><PERSON> positions the program for each arm side-by-side, using the spatial relationship between blocks from each program to represent parallelism in a way that end-users can easily understand. This design was proposed by previous work, but not implemented or evaluated in a realistic programming setting. We performed a randomized experiment with 52 participants that evaluated <PERSON><PERSON><PERSON> on a complex programming task that contained several sub-tasks. We compared <PERSON><PERSON><PERSON> with RobotStudio Online YuMi, a commercial solution, and found that <PERSON><PERSON><PERSON> allowed participants to solve the same task faster and with greater success. By analyzing the information collected during our user study, we further identified factors that explain this performance difference, as well as remaining barriers, such as debugging issues and difficulties in interacting with the robot. This work represents another step towards allowing a wider audience of non-professionals to program, which might enable the broader deployment of robotics.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623329"}, {"primary_key": "632989", "vector": [], "sparse_vector": [], "title": "Sedar: Obtaining High-Quality Seeds for DBMS Fuzzing via Cross-DBMS SQL Transfer.", "authors": ["Jingzhou Fu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yu <PERSON>"], "summary": "Effective DBMS fuzzing relies on high-quality initial seeds, which serve as the starting point for mutation. These initial seeds should incorporate various DBMS features to explore the state space thoroughly. While built-in test cases are typically used as initial seeds, many DBMSs lack comprehensive test cases, making it difficult to apply state-of-the-art fuzzing techniques directly.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639210"}, {"primary_key": "632994", "vector": [], "sparse_vector": [], "title": "MUT: Human-in-the-Loop Unit Test Migration.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Test migration, which enables the reuse of test cases crafted with knowledge and creativity by testers across various platforms and programming languages, has exhibited effectiveness in mobile app testing. However, unit test migration at the source code level has not garnered adequate attention and exploration. In this paper, we propose a novel cross-language and cross-platform test migration methodology, named MUT, which consists of four modules: code mapping, test case filtering, test case translation, and test case adaptation. MUT initially calculates code mappings to establish associations between source and target projects, and identifies suitable unit tests for migration from the source project. Then, MUT's code translation component generates a syntax tree by parsing the code to be migrated and progressively converts each node in the tree, ultima tely generating the target tests, which are compiled and executed in the target project. Moreover, we develop a web tool to assist developers in test migration. The effectiveness of our approach has been validated on five prevalent functional domain projects within the open-source community. We migrate a total of 550 unit tests and submitted pull requests to augment test code in the target projects on GitHub. By the time of this paper submission, 253 of these tests have already been merged into the projects (including 197 unit tests in the Luliyucoordinate-LeetCode project and 56 unit tests in the Rangerlee-HtmlParser project). Through running these tests, we identify 5 bugs, and 2 functional defects, and submitted corresponding issues to the project. The evaluation substantiates that MUT's test migration is both viable and beneficial across programming languages and different projects.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639124"}, {"primary_key": "632995", "vector": [], "sparse_vector": [], "title": "A Comprehensive Study of Learning-based Android Malware Detectors under Challenging Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent years have witnessed the proliferation of learning-based Android malware detectors. These detectors can be categorized into three types, String-based, Image-based and Graph-based. Most of them have achieved good detection performance under the ideal setting. In reality, however, detectors often face out-of-distribution samples due to the factors such as code obfuscation, concept drift (e.g., software development technique evolution and new malware category emergence), and adversarial examples (AEs). This problem has attracted increasing attention, but there is a lack of comparative studies that evaluate the existing various types of detectors under these challenging environments. In order to fill this gap, we select 12 representative detectors from three types of detectors, and evaluate them in the challenging scenarios involving code obfuscation, concept drift and AEs, respectively. Experimental results reveal that none of the evaluated detectors can maintain their ideal-setting detection performance, and the performance of different types of detectors varies significantly under various challenging environments. We identify several factors contributing to the performance deterioration of detectors, including the limitations of feature extraction methods and learning models. We also analyze the reasons why the detectors of different types show significant performance differences when facing code obfuscation, concept drift and AEs. Finally, we provide practical suggestions from the perspectives of users and researchers, respectively. We hope our work can help understand the detectors of different types, and provide guidance for enhancing their performance and robustness.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623320"}, {"primary_key": "632996", "vector": [], "sparse_vector": [], "title": "An Empirical Study on Low GPU Utilization of Deep Learning Jobs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Jingzhou Wang", "Yonghua Zeng", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep learning plays a critical role in numerous intelligent software applications. Enterprise developers submit and run deep learning jobs on shared, multi-tenant platforms to efficiently train and test models. These platforms are typically equipped with a large number of graphics processing units (GPUs) to expedite deep learning computations. However, certain jobs exhibit rather low utilization of the allocated GPUs, resulting in substantial resource waste and reduced development productivity. This paper presents a comprehensive empirical study on low GPU utilization of deep learning jobs, based on 400 real jobs (with an average GPU utilization of 50% or less) collected from Microsoft's internal deep learning platform. We discover 706 low-GPU-utilization issues through meticulous examination of job metadata, execution logs, runtime metrics, scripts, and programs. Furthermore, we identify the common root causes and propose corresponding fixes. Our main findings include: (1) Low GPU utilization of deep learning jobs stems from insufficient GPU computations and interruptions caused by non-GPU tasks; (2) Approximately half (46.03%) of the issues are attributed to data operations; (3) 45.18% of the issues are related to deep learning models and manifest during both model training and evaluation stages; (4) Most (84.99%) low-GPU-utilization issues could be fixed with a small number of code/script modifications. Based on the study results, we propose potential research directions that could help developers utilize GPUs better in cloud-based platforms.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639232"}, {"primary_key": "632997", "vector": [], "sparse_vector": [], "title": "TRIAD: Automated Traceability Recovery based on Biterm-enhanced Deduction of Transitive Links among Artifacts.", "authors": ["<PERSON>", "Hong<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Ma", "<PERSON>"], "summary": "Traceability allows stakeholders to extract and comprehend the trace links among software artifacts introduced across the software life cycle, to provide significant support for software engineering tasks. Despite its proven benefits, software traceability is challenging to recover and maintain manually. Hence, plenty of approaches for automated traceability have been proposed. Most rely on textual similarities among software artifacts, such as those based on Information Retrieval (IR). However, artifacts in different abstraction levels usually have different textual descriptions, which can greatly hinder the performance of IR-based approaches (e.g., a requirement in natural language may have a small textual similarity to a Java class). In this work, we leverage the consensual biterms and transitive relationships (i.e., inner- and outer-transitive links) based on intermediate artifacts to improve IR-based traceability recovery. We first extract and filter biterms from all source, intermediate, and target artifacts. We then use the consensual biterms from the intermediate artifacts to enrich the texts of both source and target artifacts, and finally deduce outer and inner-transitive links to adjust text similarities between source and target artifacts. We conducted a comprehensive empirical evaluation based on five systems widely used in other literature to show that our approach can outperform four state-of-the-art approaches in Average Precision over 15% and Mean Average Precision over 10% on average.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639164"}, {"primary_key": "632998", "vector": [], "sparse_vector": [], "title": "Learning in the Wild: Towards Leveraging Unlabeled Data for Effectively Tuning Pre-trained Code Models.", "authors": ["Shuzheng Gao", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Pre-trained code models have recently achieved substantial improvements in many code intelligence tasks. These models are first pre-trained on large-scale unlabeled datasets in a task-agnostic manner using self-supervised learning, and then fine-tuned on labeled datasets in downstream tasks. However, the labeled datasets are usually limited in size (i.e., human intensive efforts), which may hinder the performance of pre-trained code models in specific tasks. To mitigate this, one possible solution is to leverage the large-scale unlabeled data in the tuning stage by pseudo-labeling, i.e., generating pseudo labels for unlabeled data and further training the pre-trained code models with the pseudo-labeled data. However, directly employing the pseudo-labeled data can bring a large amount of noise, i.e., incorrect labels, leading to suboptimal performance. How to effectively leverage the noisy pseudo-labeled data is a challenging yet under-explored problem.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639216"}, {"primary_key": "632999", "vector": [], "sparse_vector": [], "title": "MultiTest: Physical-Aware Object Insertion for Testing Multi-sensor Fusion Perception Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multi-sensor fusion stands as a pivotal technique in addressing numerous safety-critical tasks and applications, e.g., self-driving cars and automated robotic arms. With the continuous advancement in data-driven artificial intelligence (AI), MSF's potential for sensing and understanding intricate external environments has been further amplified, bringing a profound impact on intelligent systems and specifically on their perception systems. Similar to traditional software, adequate testing is also required for AI-enabled MSF systems. Yet, existing testing methods primarily concentrate on single-sensor perception systems (e.g., image-based and point cloud-based object detection systems). There remains a lack of emphasis on generating multi-modal test cases for MSF systems.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639191"}, {"primary_key": "633000", "vector": [], "sparse_vector": [], "title": "How to Support ML End-User Programmers through a Conversational Agent.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine Learning (ML) is increasingly gaining significance for enduser programmer (EUP) applications. However, machine learning end-user programmers (ML-EUPs) without the right background face a daunting learning curve and a heightened risk of mistakes and flaws in their models. In this work, we designed a conversational agent named \"<PERSON>\" as an expert to support ML-EUPs. <PERSON>'s design was shaped by a comprehensive review of existing literature, from which we identified six primary challenges faced by ML-EUPs and five strategies to assist them. To evaluate the efficacy of <PERSON>'s design, we conducted a Wizard of Oz within-subjects study with 12 ML-EUPs. Our findings indicate that <PERSON> effectively assisted ML-EUPs, addressing the challenges highlighted in the literature. We also proposed six design guidelines for future conversational agents, which can help other EUP applications and software engineering activities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608130"}, {"primary_key": "633002", "vector": [], "sparse_vector": [], "title": "Large Language Models are Few-Shot Summarizers: Multi-Intent Comment Generation via In-Context Learning.", "authors": ["Mingyang Geng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ge Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Code comment generation aims at generating natural language descriptions for a code snippet to facilitate developers' program comprehension activities. Despite being studied for a long time, a bottleneck for existing approaches is that given a code snippet, they can only generate one comment while developers usually need to know information from diverse perspectives such as what is the functionality of this code snippet and how to use it. To tackle this limitation, this study empirically investigates the feasibility of utilizing large language models (LLMs) to generate comments that can fulfill developers' diverse intents. Our intuition is based on the facts that (1) the code and its pairwise comment are used during the pre-training process of LLMs to build the semantic connection between the natural language and programming language, and (2) comments in the real-world projects, which are collected for the pre-training, usually contain different developers' intents. We thus postulate that the LLMs can already understand the code from different perspectives after the pre-training. Indeed, experiments on two large-scale datasets demonstrate the rationale of our insights: by adopting the in-context learning paradigm and giving adequate prompts to the LLM (e.g., providing it with ten or more examples), the LLM can significantly outperform a state-of-the-art supervised learning approach on generating comments with multiple intents. Results also show that customized strategies for constructing the prompts and post-processing strategies for reranking the results can both boost the LLM's performances, which shed light on future research directions for using LLMs to achieve comment generation.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608134"}, {"primary_key": "633004", "vector": [], "sparse_vector": [], "title": "Towards Engineering Fair and Equitable Software Systems for Managing Low-Altitude Airspace Authorizations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Agni<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "Small Unmanned Aircraft Systems (sUAS) have gained widespread adoption across a diverse range of applications. This has introduced operational complexities within shared airspaces and an increase in reported incidents, raising safety concerns. In response, the U.S. Federal Aviation Administration (FAA) is developing a UAS Traffic Management (UTM) system to control access to airspace based on an sUAS's predicted ability to safely complete its mission. However, a fully automated system capable of swiftly approving or denying flight requests can be prone to bias and must consider safety, transparency, and fairness to diverse stakeholders. In this paper, we present an initial study that explores stakeholders' perspectives on factors that should be considered in an automated system. Results indicate flight characteristics and environmental conditions were perceived as most important but pilot and drone capabilities should also be considered. Further, several respondents indicated an aversion to any AI-supported automation, highlighting the need for full transparency in automated decision-making. Results provide a societal perspective on the challenges of automating UTM flight authorization decisions and help frame the ongoing design of a solution acceptable to the broader sUAS community.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640103"}, {"primary_key": "633005", "vector": [], "sparse_vector": [], "title": "Property-Based Testing in Practice.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Property-based testing (PBT) is a testing methodology where users write executable formal specifications of software components and an automated harness checks these specifications against many automatically generated inputs. From its roots in the QuickCheck library in Haskell, PBT has made significant inroads in mainstream languages and industrial practice at companies such as Amazon, Volvo, and Stripe. As PBT extends its reach, it is important to understand how developers are using it in practice, where they see its strengths and weaknesses, and what innovations are needed to make it more effective.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639581"}, {"primary_key": "633007", "vector": [], "sparse_vector": [], "title": "Kind Controllers and Fast Heuristics for Non-Well-Separated GR(1) Specifications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Non-well-separation (NWS) is a known quality issue in specifications for reactive synthesis. The problem of NWS occurs when the synthesized system can avoid satisfying its guarantees by preventing the environment from being able to satisfy its assumptions.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608131"}, {"primary_key": "633013", "vector": [], "sparse_vector": [], "title": "Do Automatic Test Generation Tools Generate Flaky Tests?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Non-deterministic test behavior, or flakiness, is common and dreaded among developers. Researchers have studied the issue and proposed approaches to mitigate it. However, the vast majority of previous work has only considered developer-written tests. The prevalence and nature of flaky tests produced by test generation tools remain largely unknown. We ask whether such tools also produce flaky tests and how these differ from developer-written ones. Furthermore, we evaluate mechanisms that suppress flaky test generation. We sample 6 356 projects written in Java or Python. For each project, we generate tests using EvoSuite (Java) and Pynguin (Python), and execute each test 200 times, looking for inconsistent outcomes. Our results show that flakiness is at least as common in generated tests as in developer-written tests. Nevertheless, existing flakiness suppression mechanisms implemented in EvoSuite are effective in alleviating this issue (71.7 % fewer flaky tests). Compared to developer-written flaky tests, the causes of generated flaky tests are distributed differently. Their non-deterministic behavior is more frequently caused by randomness, rather than by networking and concurrency. Using flakiness suppression, the remaining flaky tests differ significantly from any flakiness previously reported, where most are attributable to runtime optimizations and EvoSuite-internal resource thresholds. These insights, with the accompanying dataset, can help maintainers to improve test generation tools, give recommendations for developers using these tools, and serve as a foundation for future research in test flakiness or test generation.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608138"}, {"primary_key": "633015", "vector": [], "sparse_vector": [], "title": "Impostor Phenomenon in Software Engineers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Impostor Phenomenon (IP) is widely discussed in Science, Technology, Engineering, and Mathematics (STEM) and has been recently evaluated in Computer and Data Science students. There has been no formal research conducted on IP in software engineers in general, even though its consequences may contribute to mental health disorders, such as depression and burnout. This study describes a survey that investigates the extent of impostor feelings in software engineers, considering aspects such as gender, race/ethnicity, and roles. Furthermore, we investigate the influence of IP on their perceived productivity. The survey instrument was designed using a theory-driven approach and included demographic questions, an internationally validated IP scale (CIPS), and questions for measuring perceived productivity based on the SPACE framework constructs. The survey was sent to companies operating in various business sectors. Data analysis used bootstrapping with resampling to calculate confidence intervals and Mann-Whitney statistical significance testing for assessing the hypotheses. We received responses from 624 software engineers distributed across 26 countries. The bootstrapping results reveal that a proportion of 52.7% of software engineers experience frequent to intense levels of IP and that women suffer at a significantly higher proportion (60.6%) than men (48.8%). Regarding race/ethnicity, we observed more frequent impostor feelings in Asian (67.9%) and Black (65.1%) than in White (50.0%) software engineers. We also observed that the presence of IP is less common among individuals who are married and have children. Moreover, the prevalence of IP showed a statistically significant negative effect on the perceived productivity for all SPACE framework constructs. The evidence relating IP to software engineers provides a starting point to help organizations find ways to raise awareness of the problem and improve the emotional skills of software professionals.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640114"}, {"primary_key": "633016", "vector": [], "sparse_vector": [], "title": "DeepSample: DNN sampling-based testing for operational accuracy assessment.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep Neural Networks (DNN) are core components for classification and regression tasks of many software systems. Companies incur in high costs for testing DNN with datasets representative of the inputs expected in operation, as these need to be manually labelled. The challenge is to select a representative set of test inputs as small as possible to reduce the labelling cost, while sufficing to yield unbiased high-confidence estimates of the expected DNN accuracy. At the same time, testers are interested in exposing as many DNN mispredictions as possible to improve the DNN, ending up in the need for techniques pursuing a threefold aim: small dataset size, trustworthy estimates, mispredictions exposure. This study presents DeepSample, a family of DNN testing techniques for cost-effective accuracy assessment based on probabilistic sampling. We investigate whether, to what extent, and under which conditions probabilistic sampling can help to tackle the outlined challenge. We implement five new sampling-based testing techniques, and perform a comprehensive comparison of such techniques and of three further state-of-the-art techniques for both DNN classification and regression tasks. Results serve as guidance for best use of sampling-based testing for faithful and high-confidence estimates of DNN accuracy in operation at low cost.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639584"}, {"primary_key": "633017", "vector": [], "sparse_vector": [], "title": "Exploring the Potential of ChatGPT in Automated Code Refinement: An Empirical Study.", "authors": ["<PERSON>", "Jun<PERSON> Cao", "<PERSON><PERSON><PERSON>", "Shang<PERSON> Liu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Code review is an essential activity for ensuring the quality and maintainability of software projects. However, it is a time-consuming and often error-prone task that can significantly impact the development process. Recently, ChatGPT, a cutting-edge language model, has demonstrated impressive performance in various natural language processing tasks, suggesting its potential to automate code review processes. However, it is still unclear how well ChatGPT performs in code review tasks. To fill this gap, in this paper, we conduct the first empirical study to understand the capabilities of ChatGPT in code review tasks, specifically focusing on automated code refinement based on given code reviews. To conduct the study, we select the existing benchmark CodeReview and construct a new code review dataset with high quality. We use CodeReviewer, a state-of-the-art code review tool, as a baseline for comparison with ChatGPT. Our results show that ChatGPT outperforms CodeReviewer in code refinement tasks. Specifically, our results show that ChatGPT achieves higher EM and BLEU scores of 22.78 and 76.44 respectively, while the state-of-the-art method achieves only 15.50 and 62.88 on a high-quality code review dataset. We further identify the root causes for ChatGPT's underperformance and propose several strategies to mitigate these challenges. Our study provides insights into the potential of ChatGPT in automating the code review process, and highlights the potential research directions.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623306"}, {"primary_key": "633018", "vector": [], "sparse_vector": [], "title": "An Empirical Study on Oculus Virtual Reality Applications: Security and Privacy Perspectives.", "authors": ["<PERSON><PERSON> Guo", "Hong-Ning Dai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although Virtual Reality (VR) has accelerated its prevalent adoption in emerging metaverse applications, it is not a fundamentally new technology. On one hand, most VR operating systems (OS) are based on off-the-shelf mobile OS (e.g., Android). As a result, VR apps also inherit privacy and security deficiencies from conventional mobile apps. On the other hand, in contrast to conventional mobile apps, VR apps can achieve immersive experience via diverse VR devices, such as head-mounted displays, body sensors, and controllers though achieving this requires the extensive collection of privacy-sensitive human biometrics (e.g., hand-tracking and face-tracking data). Moreover, VR apps have been typically implemented by 3D gaming engines (e.g., Unity), which also contain intrinsic security vulnerabilities. Inappropriate use of these technologies may incur privacy leaks and security vulnerabilities although these issues have not received significant attention compared to the proliferation of diverse VR apps. In this paper, we develop a security and privacy assessment tool, namely the VR-SP detector for VR apps. The VR-SP detector has integrated program static analysis tools and privacy-policy analysis methods. Using the VR-SP detector, we conduct a comprehensive empirical study on 500 popular VR apps. We obtain the original apps from the popular Oculus and SideQuest app stores and extract APK files via the Meta Oculus Quest 2 device. We evaluate security vulnerabilities and privacy data leaks of these VR apps by VR app analysis, taint analysis, and privacy-policy analysis. We find that a number of security vulnerabilities and privacy leaks widely exist in VR apps. Moreover, our results also reveal conflicting representations in the privacy policies of these apps and inconsistencies of the actual data collection with the privacy-policy statements of the apps. Based on these findings, we make suggestions for the future development of VR apps.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639082"}, {"primary_key": "633019", "vector": [], "sparse_vector": [], "title": "Reorder Pointer Flow in Sound Concurrency Bug Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Due to the non-determinism of thread interleaving, predicting concurrency bugs has long been an extremely difficult task. Recently, several sound bug-detecting approaches were proposed. These approaches are based on local search, i.e., mutating the sequential order of the observed trace and predicting whether the mutated sequential order can trigger a bug. Surprisingly, during this process, they never consider reordering the data flow of the pointers, which can be the key point to detecting many complex bugs. To alleviate this weakness, we propose a new flow-sensitive point-to analysis technique ConPTA to help actively reorder the pointer flow during the sequential order mutation process. Based on ConPTA, we further propose a new sound predictive bug-detecting approach Eagle to predict four types of concurrency bugs. They are null pointer dereference (NPD), uninitialized pointer use (UPU), use after free (UAF), and double free (DF). By actively reordering the pointer flow, <PERSON> can explore a larger search space of the thread interleaving during the mutation and thus detect more concurrency bugs. Our evaluation of Eagle on 10 real-world multi-threaded programs shows that Eagle significantly outperforms four state-of-the-art bug-detecting approaches UFO, ConVul, ConVulPOE and Period in both effectiveness and efficiency.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623300"}, {"primary_key": "633022", "vector": [], "sparse_vector": [], "title": "Curiosity-Driven Testing for Sequential Decision-Making Process.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sequential decision-making processes (SDPs) are fundamental for complex real-world challenges, such as autonomous driving, robotic control, and traffic management. While recent advances in Deep Learning (DL) have led to mature solutions for solving these complex problems, SDMs remain vulnerable to learning unsafe behaviors, posing significant risks in safety-critical applications. However, developing a testing framework for SDMs that can identify a diverse set of crash-triggering scenarios remains an open challenge. To address this, we propose CureFuzz, a novel curiosity-driven black-box fuzz testing approach for SDMs. CureFuzz proposes a curiosity mechanism that allows a fuzzer to effectively explore novel and diverse scenarios, leading to improved detection of crash-triggering scenarios. Additionally, we introduce a multi-objective seed selection technique to balance the exploration of novel scenarios and the generation of crash-triggering scenarios, thereby optimizing the fuzzing process. We evaluate CureFuzz on various SDMs and experimental results demonstrate that CureFuzz outperforms the state-of-the-art method by a substantial margin in the total number of faults and distinct types of crash-triggering scenarios. We also demonstrate that the crash-triggering scenarios found by CureFuzz can repair SDMs, highlighting CureFuzz as a valuable tool for testing SDMs and optimizing their performance.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639149"}, {"primary_key": "633023", "vector": [], "sparse_vector": [], "title": "MalwareTotal: Multi-Faceted and Sequence-Aware Bypass Tactics against Static Malware Detection.", "authors": ["<PERSON><PERSON>", "Cai Fu", "Hong Hu", "<PERSON><PERSON><PERSON>", "Jianqiang Lv", "<PERSON><PERSON>"], "summary": "Recent methods have demonstrated that machine learning (ML) based static malware detection models are vulnerable to adversarial attacks. However, the generated malware often fails to generalize to production-level anti-malware software (AMS), as they usually involve multiple detection methods. This calls for universal solutions to the problem of malware variants generation. In this work, we demonstrate how the proposed method, MalwareTotal, has allowed malware variants to continue to abound in ML-based, signature-based, and hybrid anti-malware software. Given a malicious binary, we develop sequential bypass tactics that enable malicious behavior to be concealed within multi-faceted manipulations. Through 12 experiments on real-world malware, we demonstrate that an attacker can consistently bypass detection (98.67%, and 100% attack success rate against ML-based methods EMBER and MalConv, respectively; 95.33%, 92.63%, and 98.52% attack success rate against production-level anti-malware software ClamAV, AMS A, and AMS B, respectively) without modifying the malware functionality. We further demonstrate that our approach outperforms state-of-the-art adversarial malware generation techniques both in attack success rate and query consumption (the number of queries to the target model). Moreover, the samples generated by our method have demonstrated transferability in the real-world integrated malware detector, VirusTotal. In addition, we show that common mitigation such as adversarial training on known attacks cannot effectively defend against the proposed attack. Finally, we investigate the value of the generated adversarial examples as a means of hardening victim models through an adversarial training procedure, and demonstrate that the accuracy of the retrained model against generated adversarial examples increases by 88.51 percentage points.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639141"}, {"primary_key": "633024", "vector": [], "sparse_vector": [], "title": "&quot;I tend to view ads almost like a pestilence&quot;: On the Accessibility Implications of Mobile Ads for Blind Users.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ads are integral to the contemporary Android ecosystem, generating revenue for free-to-use applications. However, injected as third-party content, ads are displayed on native apps in pervasive ways that affect easy navigation. Ads can prove more disruptive for blind users, who rely on screen readers for navigating an app. While the literature has looked into either the accessibility of web advertisements or the privacy and security implications of mobile ads, a research gap on the accessibility of mobile ads remains, which we aim to bridge. We conduct an empirical study analyzing 500 ad screens in Android apps to categorize and examine the accessibility issues therein. Additionally, we conduct 15 qualitative user interviews with blind Android users to better understand the impact of those accessibility issues, how users interact with ads and their preferences. Based on our findings, we discuss the design and practical strategies for developing accessible ads.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639228"}, {"primary_key": "633025", "vector": [], "sparse_vector": [], "title": "High Expectations: An Observational Study of Programming and Cannabis Intoxication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Anecdotal evidence of cannabis use by professional programmers abounds. Recent studies have found that some professionals regularly use cannabis while programming, even for work-related tasks. However, accounts of the impacts of cannabis on programming vary widely and are often contradictory. For example, some programmers claim that it impairs their ability to generate correct solutions, while others claim it enhances creativity and focus. There remains a need for an empirical understanding of the true impacts of cannabis on programming. This paper presents the first controlled observational study of cannabis's effects on programming ability. Based on a within-subjects design with over 70 participants, we find that, at ecologically valid dosages, cannabis significantly impairs programming performance. Programs implemented while high contain more bugs and take longer to write (p < 0.05) --- a small to medium effect (0.22 ≤ d ≤ 0.44). We also did not find any evidence that high programmers generate more divergent solutions. However, programmers can accurately assess differences in their programming performance (r = 0.59), even when under the influence of cannabis. We hope that this research will facilitate evidence-based policies and help developers make informed decisions regarding cannabis use while programming.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639145"}, {"primary_key": "633032", "vector": [], "sparse_vector": [], "title": "An Empirical Study of Data Disruption by Ransomware Attacks.", "authors": ["<PERSON><PERSON>", "Li<PERSON> Guo", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chengnian Sun", "Yu <PERSON>"], "summary": "The threat of ransomware to the software ecosystem has become increasingly alarming in recent years, raising a demand for large-scale and comprehensive ransomware analysis to help develop more effective countermeasures against unknown attacks. In this paper, we first collect a real-world dataset MarauderMap, consisting of 7,796 active ransomware samples, and analyze their behaviors of disrupting data in victim systems. All samples are executed in isolated testbeds to collect all perspectives of six categories of runtime behaviors, such as API calls, I/O accesses, and network traffic. The total logs volume is up to 1.98 TiB. By assessing collected behaviors, we present six critical findings throughout ransomware attacks' data reconnaissance, data tampering, and data exfiltration phases. Based on our findings, we propose three corresponding mitigation strategies to detect ransomware during each phase. Experimental results show that they can enhance the capability of state-of-the-art anti-ransomware tools. We report a preliminary result of a 41%-69% increase in detection rate with no additional false positives, showing that our insights are helpful.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639090"}, {"primary_key": "633033", "vector": [], "sparse_vector": [], "title": "Crossover in Parametric Fuzzing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Parametric fuzzing combines evolutionary and generator-based fuzzing to create structured test inputs that exercise unique execution behaviors. Parametric fuzzers internally represent inputs as bit strings referred to as \"parameter sequences\". Interesting parameter sequences are saved by the fuzzer and perturbed to create new inputs without the need for type-specific operators. However, existing work on parametric fuzzing only uses mutation operators, which modify a single input; it does not incorporate crossover, an evolutionary operator that blends multiple inputs together. Crossover operators aim to combine advantageous traits from multiple inputs. However, the nature of parametric fuzzing limits the effectiveness of traditional crossover operators. In this paper, we propose linked crossover, an approach for using dynamic execution information to identify and exchange analogous portions of parameter sequences. We created an implementation of linked crossover for Java and evaluated linked crossover's ability to preserve advantageous traits. We also evaluated linked crossover's impact on fuzzer performance on seven real-world Java projects and found that linked crossover consistently performed as well as or better than three state-of-the-art parametric fuzzers and two other forms of crossover on both long and short fuzzing campaigns.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639160"}, {"primary_key": "633035", "vector": [], "sparse_vector": [], "title": "<PERSON>: A Stochastic Asynchronous Concolic Explorer.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Concolic execution is a powerful program analysis technique for code path exploration. Despite recent advances that greatly improved the efficiency of concolic execution engines, path constraint solving remains a major bottleneck of concolic testing. An intelligent scheduler for inputs/branches becomes even more crucial. Our studies show that the previously under-studied branch-flipping policy adopted by state-of-the-art concolic execution engines has several limitations. We propose to assess each branch by its potential for new code coverage from a global view, concerning the path divergence probability at each branch. To validate this idea, we implemented a prototype Marco and evaluated it against the state-of-the-art concolic executor on 30 real-world programs from Google's Fuzzbench, Binutils, and UniBench. The result shows that <PERSON> can outperform the baseline approach and make continuous progress after the baseline approach terminates.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623301"}, {"primary_key": "633037", "vector": [], "sparse_vector": [], "title": "Empirical Analysis of Vulnerabilities Life Cycle in Golang Ecosystem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Open-source software (OSS) greatly facilitates program development for developers. However, the high number of vulnerabilities in open-source software is a major concern, including in Golang, a relatively new programming language. In contrast to other commonly used OSS package managers, Golang presents a distinctive feature whereby commits are prevalently used as dependency versions prior to their integration into official releases. This attribute can prove advantageous to users, as patch commits can be implemented in a timely manner before the releases. However, Golang employs a decentralized mechanism for managing dependencies, whereby dependencies are upheld and distributed in separate repositories. This approach can result in delays in the dissemination of patches and unresolved vulnerabilities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639230"}, {"primary_key": "633038", "vector": [], "sparse_vector": [], "title": "Constraint Based Program Repair for Persistent Memory Bugs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a constraint based method for repairing bugs associated with the use of persistent memory (PM) in application software. Our method takes a program execution trace and the violated property as input and returns a suggested repair, which is a combination of inserting new PM instructions and reordering these instructions to eliminate the property violation. Compared with the state-of-the-art approach, our method has three advantages. First, it can repair both durability and crash consistency bugs whereas the state-of-the-art approach can only repair the relatively-simple durability bugs. Second, our method can discover new repair strategies instead of relying on repair strategies hard-coded into the repair tool. Third, our method uses a novel symbolic encoding to model PM semantics, which allows our symbolic analysis to be more efficient than the explicit enumeration of possible scenarios and thus explore a large number of repairs quickly. We have evaluated our method on benchmark programs from the well-known Intel PMDK library as well as real applications such as Memcached, Recipe, and Redis. The results show that our method can repair all of the 41 known bugs in these benchmarks, while the state-of-the-art approach cannot repair any of the crash consistency bugs.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639204"}, {"primary_key": "633040", "vector": [], "sparse_vector": [], "title": "Revealing Hidden Threats: An Empirical Study of Library Misuse in Smart Contracts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart contracts are Turing-complete programs that execute on the blockchain. Developers can implement complex contracts, such as auctions and lending, on Ethereum using the Solidity programming language. As an object-oriented language, Solidity provides libraries within its syntax to facilitate code reusability and reduce development complexity. Library misuse refers to the incorrect writing or usage of libraries, resulting in unexpected results, such as introducing vulnerabilities during library development or incorporating an unsafe library during contract development. Library misuse could lead to contract defects that cause financial losses. Currently, there is a lack of research on library misuse. To fill this gap, we collected more than 500 audit reports from the official websites of five audit companies and 223,336 real-world smart contracts from Etherscan to measure library popularity and library misuse. Then, we defined eight general patterns for library misuse; three of them occurring during library development and five during library utilization, which covers the entire library lifecycle. To validate the practicality of these patterns, we manually analyzed 1,018 real-world smart contracts and publicized our dataset. We identified 905 misuse cases across 456 contracts, indicating that library misuse is a widespread issue. Three patterns of misuse are found in more than 50 contracts, primarily due to developers lacking security awareness or underestimating negative impacts. Additionally, our research revealed that vulnerable libraries on Ethereum continue to be employed even after they have been deprecated or patched. Our findings can assist contract developers in preventing library misuse and ensuring the safe use of libraries.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623335"}, {"primary_key": "633042", "vector": [], "sparse_vector": [], "title": "Generating REST API Specifications through Static Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Web Application Programming Interfaces (APIs) allow services to be accessed over the network. RESTful (or REST) APIs, which use the REpresentation State Transfer (REST) protocol, are a popular type of web API. To use or test REST APIs, developers use specifications written in standards such as OpenAPI. However, creating and maintaining these specifications is time-consuming and error-prone, especially as software evolves, leading to incomplete or inconsistent specifications that negatively affect the use and testing of the APIs. To address this problem, we present Respector (REST API specification generator), the first technique to employ static and symbolic program analysis to generate specifications for REST APIs from their source code. We evaluated Respector on 15 real-world APIs with promising results in terms of precision and recall in inferring endpoint methods, endpoint parameters, method responses, and parameter attributes, including constraints leading to successful HTTP responses or errors. Furthermore, these results could be further improved with additional engineering. Comparing the Respector-generated specifications with the developer-provided ones shows that <PERSON><PERSON><PERSON> was able to identify many missing end-point methods, parameters, constraints, and responses, along with some inconsistencies between developer-provided specifications and API implementations. Finally, Respector outperformed several techniques that infer specifications from annotations within API implementations or by invoking the APIs.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639137"}, {"primary_key": "633044", "vector": [], "sparse_vector": [], "title": "CrashTranslator: Automatically Reproducing Mobile Application Crashes Directly from Stack Trace.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chunyang Chen", "Yuanzhe Hu", "<PERSON>"], "summary": "Crash reports are vital for software maintenance since they allow the developers to be informed of the problems encountered in the mobile application. Before fixing, developers need to reproduce the crash, which is an extremely time-consuming and tedious task. Existing studies conducted the automatic crash reproduction with the natural language described reproducing steps. Yet we find a non-neglectable portion of crash reports only contain the stack trace when the crash occurs. Such stack-trace-only crashes merely reveal the last GUI page when the crash occurs, and lack step-by-step guidance. Developers tend to spend more effort in understanding the problem and reproducing the crash, and existing techniques cannot work on this, thus calling for a greater need for automatic support. This paper proposes an approach named CrashTranslator to automatically reproduce mobile application crashes directly from the stack trace. It accomplishes this by leveraging a pre-trained Large Language Model to predict the exploration steps for triggering the crash, and designing a reinforcement learning based technique to mitigate the inaccurate prediction and guide the search holistically. We evaluate CrashTranslator on 75 crash reports involving 58 popular Android apps, and it successfully reproduces 61.3% of the crashes, outperforming the state-of-the-art baselines by 109% to 206%. Besides, the average reproducing time is 68.7 seconds, outperforming the baselines by 302% to 1611%. We also evaluate the usefulness of CrashTranslator with promising results.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623298"}, {"primary_key": "633047", "vector": [], "sparse_vector": [], "title": "Uncovering the Causes of Emotions in Software Developer Communication Using Zero-shot LLMs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Understanding and identifying the causes behind developers' emotions (e.g., Frustration caused by 'delays in merging pull requests') can be crucial towards finding solutions to problems and fostering collaboration in open-source communities. Effectively identifying such information in the high volume of communications across the different project channels, such as chats, emails, and issue comments, requires automated recognition of emotions and their causes. To enable this automation, large-scale software engineering-specific datasets that can be used to train accurate machine learning models are required. However, such datasets are expensive to create with the variety and informal nature of software projects' communication channels.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639223"}, {"primary_key": "633048", "vector": [], "sparse_vector": [], "title": "She<PERSON> Light on Software Engineering-specific Metaphors and Idioms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Use of figurative language, such as metaphors and idioms, is common in our daily-life communications, and it can also be found in Software Engineering (SE) channels, such as comments on GitHub. Automatically interpreting figurative language is a challenging task, even with modern Large Language Models (LLMs), as it often involves subtle nuances. This is particularly true in the SE domain, where figurative language is frequently used to convey technical concepts, often bearing developer affect (e.g., 'spaghetti code). Surprisingly, there is a lack of studies on how figurative language in SE communications impacts the performance of automatic tools that focus on understanding developer communications, e.g., bug prioritization, incivility detection. Furthermore, it is an open question to what extent state-of-the-art LLMs interpret figurative expressions in domain-specific communication such as software engineering. To address this gap, we study the prevalence and impact of figurative language in SE communication channels. This study contributes to understanding the role of figurative language in SE, the potential of LLMs in interpreting them, and its impact on automated SE communication analysis. Our results demonstrate the effectiveness of fine-tuning LLMs with figurative language in SE and its potential impact on automated tasks that involve affect. We found that, among three state-of-the-art LLMs, the best improved fine-tuned versions have an average improvement of 6.66% on a GitHub emotion classification dataset, 7.07% on a GitHub incivility classification dataset, and 3.71% on a Bugzilla bug report prioritization dataset.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639585"}, {"primary_key": "633051", "vector": [], "sparse_vector": [], "title": "Language Models for Code Completion: A Practical Evaluation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Transformer-based language models for automatic code completion have shown great promise so far, yet the evaluation of these models rarely uses real data. This study provides both quantitative and qualitative assessments of three public code language models when completing real-world code. We first developed an open-source IDE extension, Code4Me, for the online evaluation of the models. We collected real auto-completion usage data for over a year from more than 1200 users, resulting in over 600K valid completions. These models were then evaluated using six standard metrics across twelve programming languages. Next, we conducted a qualitative study of 1690 real-world completion requests to identify the reasons behind the poor model performance. A comparative analysis of the models' performance in online and offline settings was also performed, using benchmark synthetic datasets and two masking strategies.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639138"}, {"primary_key": "633052", "vector": [], "sparse_vector": [], "title": "Co-Creation in Fully Remote Software Teams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we use the lens of co-creation---a concept originally coined and applied in the fields of management and design that denotes how groups of people collaboratively create something of meaning through an orchestration of people, activities, and tools---to study how fully remote software teams co-create digital artifacts that can be considered as a form of documentation. We report on the results of a qualitative, interview-based study with 25 software professionals working in remote teams. Our primary findings are the definition of four models of co-creation, examples of sequencing these models into work chains to produce artifacts, factors that influence how developers match tasks to models and chains, and insights into tool support for co-creation. Together, our findings illustrate how co-creation is an intentional activity that has a significant role in how remote software teams' choose to structure their collaborative activities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623297"}, {"primary_key": "633053", "vector": [], "sparse_vector": [], "title": "Predicting open source contributor turnover from value-related discussions: An analysis of GitHub issues.", "authors": ["<PERSON>", "<PERSON>", "Eureka Foong"], "summary": "Discussions about project values are important for engineering software that meets diverse human needs and positively impacts society. Because value-related discussions involve deeply held beliefs, they can lead to conflicts or other outcomes that may affect motivations to continue contributing to open source projects. However, it is unclear what kind of value-related discussions are associated with significant changes in turnover. We address this gap by identifying discussions related to important project values and investigating the extent to which those discussions predict project turnover in the following months. We collected logs of GitHub issues and commits from 52 projects that share similar ethical commitments and were identified as part of the DWeb (Decentralized Web) community. We identify issues related to DWeb's core values of respectfulness, freedom, broadmindedness, opposing centralized social power, equity & equality, and protecting the environment. We then use Granger causality analysis to examine how changes in the proportion of discussions related to those values might predict changes in incoming and outgoing turnover. We found multiple significant relationships between value-related discussions and turnover, including that discussions about respectfulness predict an increase in contributors leaving and a decrease in new contributors, while discussions about social power predicted better contributor retention. Understanding these antecedents of contributor turnover is important for managing open source projects that incorporate human-centric issues. Based on the results, we discuss implications for open source maintainers and for future research.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623340"}, {"primary_key": "633054", "vector": [], "sparse_vector": [], "title": "A Synthesis of Green Architectural Tactics for ML-Enabled Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rapid adoption of artificial intelligence (AI) and machine learning (ML) has generated growing interest in understanding their environmental impact and the challenges associated with designing environmentally friendly ML-enabled systems. While Green AI research, i.e., research that tries to minimize the energy footprint of AI, is receiving increasing attention, very few concrete guidelines are available on how ML-enabled systems can be designed to be more environmentally sustainable. In this paper, we provide a catalog of 30 green architectural tactics for ML-enabled systems to fill this gap. An architectural tactic is a high-level design technique to improve software quality, in our case environmental sustainability. We derived the tactics from the analysis of 51 peer-reviewed publications that primarily explore Green AI, and validated them using a focus group approach with three experts. The 30 tactics we identified are aimed to serve as an initial reference guide for further exploration into Green AI from a software engineering perspective, and assist in designing sustainable ML-enabled systems. To enhance transparency and facilitate their widespread use and extension, we make the tactics available online in easily consumable formats. Wide-spread adoption of these tactics has the potential to substantially reduce the societal impact of ML-enabled systems regarding their energy and carbon footprint.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640111"}, {"primary_key": "633056", "vector": [], "sparse_vector": [], "title": "Cross-Inlining Binary Function Similarity Detection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Wuxia Jin", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Binary function similarity detection plays an important role in a wide range of security applications. Existing works usually assume that the query function and target function share equal semantics and compare their full semantics to obtain the similarity. However, we find that the function mapping is more complex, especially when function inlining happens.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639080"}, {"primary_key": "633057", "vector": [], "sparse_vector": [], "title": "BinaryAI: Binary Software Composition Analysis via Intelligent Binary Source Code Matching.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While third-party libraries (TPLs) are extensively reused to enhance productivity during software development, they can also introduce potential security risks such as vulnerability propagation. Software composition analysis (SCA), proposed to identify reused TPLs for reducing such risks, has become an essential procedure within modern DevSecOps. As one of the mainstream SCA techniques, binary-to-source SCA identifies the third-party source projects contained in binary files via binary source code matching, which is a major challenge in reverse engineering since binary and source code exhibit substantial disparities after compilation. The existing binary-to-source SCA techniques leverage basic syntactic features that suffer from redundancy and lack robustness in the large-scale TPL dataset, leading to inevitable false positives and compromised recall. To mitigate these limitations, we introduce BinaryAI, a novel binary-to-source SCA technique with two-phase binary source code matching to capture both syntactic and semantic code features. First, BinaryAI trains a transformer-based model to produce function-level embeddings and obtain similar source functions for each binary function accordingly. Then by applying the link-time locality to facilitate function matching, BinaryAI detects the reused TPLs based on the ratio of matched source functions. Our experimental results demonstrate the superior performance of BinaryAI in terms of binary source code matching and the downstream SCA task. Specifically, our embedding model outperforms the state-of-the-art model CodeCMR, i.e., achieving 22.54% recall@1 and 0.34 MRR compared with 10.75% and 0.17 respectively. Additionally, BinaryAI outperforms all existing binary-to-source SCA tools in TPL detection, increasing the precision from 73.36% to 85.84% and recall from 59.81% to 64.98% compared with the well-recognized commercial SCA product Black Duck.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639100"}, {"primary_key": "633058", "vector": [], "sparse_vector": [], "title": "Detecting Logic Bugs in Graph Database Management Systems via Injective and Surjective Graph Query Transformation.", "authors": ["Yuancheng Jiang", "<PERSON><PERSON><PERSON>", "Jinsheng Ba", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Graph Database Management Systems (GDBMSs) store graphs as data. They are used naturally in applications such as social networks, recommendation systems and program analysis. However, they can be affected by logic bugs, which cause the GDBMSs to compute incorrect results and subsequently affect the applications relying on them. In this work, we propose injective and surjective Graph Query Transformation (GQT) to detect logic bugs in GDBMSs. Given a query Q, we derive a mutated query Q', so that either their result sets are: (i) semantically equivalent; or (ii) variant based on the mutation to be either a subset or superset of each other. When the expected relationship between the results does not hold, a logic bug in the GDBMS is detected. The key insight to mutate Q is that the graph pattern in graph queries enables systematic query transformations derived from injective and surjective mappings of the directed edge sets between Q and Q'. We implemented injective and surjective Graph Query Transformation (GQT) as a tool called GraphGenie and evaluated it on 6 popular and mature GDBMSs. GraphGenie has found 25 unknown bugs, comprising 16 logic bugs, 3 internal errors, and 6 performance issues. Our results demonstrate the practicality and effectiveness of GraphGenie in detecting logic bugs in GDBMSs which has the potential for improving the reliability of applications relying on these GDBMSs.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623307"}, {"primary_key": "633059", "vector": [], "sparse_vector": [], "title": "Xpert: Empowering Incident Management with Query Recommendations via Large Language Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Minghua Ma", "Si Qin", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large-scale cloud systems play a pivotal role in modern IT infrastructure. However, incidents occurring within these systems can lead to service disruptions and adversely affect user experience. To swiftly resolve such incidents, on-call engineers depend on crafting domain-specific language (DSL) queries to analyze telemetry data. However, writing these queries can be challenging and time-consuming. This paper presents a thorough empirical study on the utilization of queries of KQL, a DSL employed for incident management in a large-scale cloud management system at Microsoft. The findings obtained underscore the importance and viability of KQL queries recommendation to enhance incident management.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639081"}, {"primary_key": "633060", "vector": [], "sparse_vector": [], "title": "PyAnalyzer: An Effective and Practical Approach for Dependency Extraction from Python Code.", "authors": ["Wuxia Jin", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dependency extraction based on static analysis lays the groundwork for a wide range of applications. However, dynamic language features in Python make code behaviors obscure and nondeterministic; consequently, it poses huge challenges for static analyses to resolve symbol-level dependencies. Although prosperous techniques and tools are adequately available, they still lack sufficient capabilities to handle object changes, first-class citizens, varying call sites, and library dependencies. To address the fundamental difficulty for dynamic languages, this work proposes an effective and practical method namely PyAnalyzer for dependency extraction. PyAnalyzer uniformly models functions, classes, and modules into first-class heap objects, propagating the dynamic changes of these objects and class inheritance. This manner better simulates dynamic features like duck typing, object changes, and first-class citizens, resulting in high recall results without compromising precision. Moreover, PyAnalyzer leverages optional type annotations as a shortcut to express varying call sites and resolve library dependencies on demand. We collected two micro-benchmarks (278 small programs), two macro-benchmarks (59 real-world applications), and 191 real-world projects (10MSLOC) for comprehensive comparisons with 7 advanced techniques (i.e., Understand, Sourcetrail, Depends, ENRE19, PySonar2, PyCG, and Type4Py). The results demonstrated that PyAnalyzer achieves a high recall and hence improves the F1 by 24.7% on average, at least 1.4x faster without an obvious compromise of memory efficiency. Our work will benefit diverse client applications.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3640325"}, {"primary_key": "633061", "vector": [], "sparse_vector": [], "title": "Scaling Code Pattern Inference with Interactive What-If Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Programmers often have to search for similar code when detecting and fixing similar bugs. Prior active learning approaches take only instance-level feedback, i.e., positive and negative method instances. This limitation leads to increased labeling burden, when users try to control generality and specificity for a desired code pattern.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639193"}, {"primary_key": "633065", "vector": [], "sparse_vector": [], "title": "Symbol-Specific Sparsification of Interprocedural Distributive Environment Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous work has shown that one can often greatly speed up static analysis by computing data flows not for every edge in the program's control-flow graph but instead only along definition-use chains. This yields a so-called sparse static analysis. Recent work on SparseDroid has shown that specifically taint analysis can be \"sparsified\" with extraordinary effectiveness because the taint state of one variable does not depend on those of others. This allows one to soundly omit more flow-function computations than in the general case.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639092"}, {"primary_key": "633067", "vector": [], "sparse_vector": [], "title": "Recovering Trace Links Between Software Documentation And Code.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Introduction Software development involves creating various artifacts at different levels of abstraction and establishing relationships between them is essential. Traceability link recovery (TLR) automates this process, enhancing software quality by aiding tasks like maintenance and evolution. However, automating TLR is challenging due to semantic gaps resulting from different levels of abstraction. While automated TLR approaches exist for requirements and code, architecture documentation lacks tailored solutions, hindering the preservation of architecture knowledge and design decisions. Methods This paper presents our approach TransArC for TLR between architecture documentation and code, using component-based architecture models as intermediate artifacts to bridge the semantic gap. We create transitive trace links by combining the existing approach ArDoCo for linking architecture documentation to models with our novel approach ArCoTL for linking architecture models to code.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639130"}, {"primary_key": "633071", "vector": [], "sparse_vector": [], "title": "A First Look at the Inheritance-Induced Redundant Test Execution.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Inheritance, a fundamental aspect of object-oriented design, has been leveraged to enhance code reuse and facilitate efficient software development. However, alongside its benefits, inheritance can introduce tight coupling and complex relationships between classes, posing challenges for software maintenance. Although there are many studies on inheritance in source code, there is limited study on using inheritance in test code. In this paper, we take the first step by studying inheritance in test code, with a focus on redundant test executions caused by inherited test cases. We empirically study the prevalence of test inheritance and its characteristics. We also propose a hybrid approach that combines static and dynamic analysis to identify and locate inheritance-induced redundant test cases. Our findings reveal that (1) inheritance is widely utilized in the test code, (2) inheritance-induced redundant test executions are prevalent, accounting for 13% of all execution test cases, (3) bypassing these redundancies can help reduce 14% of the test execution time, and finally, (4) our study highlights the need for careful refactoring decisions to minimize redundant test cases and identifies the need for further research on test code quality.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639166"}, {"primary_key": "633083", "vector": [], "sparse_vector": [], "title": "Translation Validation for JIT Compiler in the V8 JavaScript Engine.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present Turbo<PERSON>, a translation validator for the JavaScript (JS) just-in-time (JIT) compiler of V8. While JS engines have become a crucial part of various software systems, their emerging adaption of JIT compilation makes it increasingly challenging to ensure their correctness. We tackle this problem with an SMT-based translation validation (TV) that checks whether a specific compilation is semantically correct. We formally define the semantics of IR of TurboFan (JIT compiler of V8) as SMT encoding. For efficient validation, we design a staged strategy for JS JIT compilers. This allows us to decompose the whole correctness checking into simpler ones. Furthermore, we utilize fuzzing to achieve practical TV. We generate a large number of JS functions using a fuzzer to trigger various optimization passes of TurboFan and validate their compilation using TurboTV. Lastly, we demonstrate that TurboTV can also be used for cross-language TV. We show that TurboTV can validate the translation chain from LLVM IR to TurboFan IR, collaborating with an off-the-shelf TV tool for LLVM. We evaluated TurboTV on various sets of JS and LLVM programs. TurboTV effectively validated a large number of compilations of TurboFan with a low false positive rate and discovered a new miscompilation in LLVM.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639189"}, {"primary_key": "633085", "vector": [], "sparse_vector": [], "title": "Dealing With Cultural Dispersion: a Novel Theoretical Framework for Software Engineering Research and Practice.", "authors": ["<PERSON>", "<PERSON>", "Bice <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software development is fundamentally a team-driven process; researchers in software engineering have identified various human and social factors that can significantly impact it. Culture emerged as a critical element, and the diversity deriving from cultural differences can be highly impactful both positively and negatively. Despite existing knowledge about how culture influences software development, limitations persist. Most importantly, a unified and comprehensive (grounded) theory of how cultural differences influence and are managed in software development has yet to exist. This lack has two significant consequences: (1) it makes research on culture fragmented, leading to the continual definition of new concepts that do not allow state of the art to advance significantly, and (2) it reduces the ability of the research to be transferred to practitioners since there is no framework designed to be understood and used by them. To address the above-mentioned limitation, this work proposed a theoretical framework of \"Dealing With Cultural Dispersion,\" which focuses on challenges and benefits originating from cultural differences and strategies for dealing with them. Such a framework was developed through a qualitative study using an iterative research approach, including interviews and socio-technical grounded theory for data analysis. The proposed framework was designed to reveal the tangible effects of practitioners' culture in software development, allowing software teams to (1) clearly understand the problem and (2) implement the correct strategy for addressing it. Additionally, researchers can use this framework as a foundation to (deductively) develop a more robust and comprehensive theory in this field.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640105"}, {"primary_key": "633086", "vector": [], "sparse_vector": [], "title": "Deeply Reinforcing Android GUI Testing with Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Minxue Pan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the scale and complexity of Android applications continue to grow in response to increasing market and user demands, quality assurance challenges become more significant. While previous studies have demonstrated the superiority of Reinforcement Learning (RL) in Android GUI testing, its effectiveness remains limited, particularly in large, complex apps. This limitation arises from the ineffectiveness of Tabular RL in learning the knowledge within the large state-action space of the App Under Test (AUT) and from the suboptimal utilization of the acquired knowledge when employing more advanced RL techniques. To address such limitations, this paper presents DQT, a novel automated Android GUI testing approach based on deep reinforcement learning. DQT preserves widgets' structural and semantic information with graph embedding techniques, building a robust foundation for identifying similar states or actions and distinguishing different ones. Moreover, a specially designed Deep Q-Network (DQN) effectively guides curiosity-driven exploration by learning testing knowledge from runtime interactions with the AUT and sharing it across states or actions. Experiments conducted on 30 diverse open-source apps demonstrate that DQT outperforms existing state-of-the-art testing approaches in both code coverage and fault detection, particularly for large, complex apps. The faults detected by DQT have been reproduced and reported to developers; so far, 21 of the reported issues have been explicitly confirmed, and 14 have been fixed.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623344"}, {"primary_key": "633090", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>, is the skill always safe? Uncover Lenient Skill Vetting Process and Protect User Privacy at Run Time.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Voice personal assistant (VPA) platforms (e.g., Amazon Alexa) allow developers to deploy their voice apps on third-party servers. However, this strategy introduces unexpected privacy risks to VPA customers. Malicious developers can dynamically change their app's behaviors to circumvent the platform's vetting process. This paper aims to systematically analyze Alexa's voice app ecosystem (i.e., Alexa skills), focusing on behavior manipulation (also referred to as skill behavior change). We identify the root causes of malicious skills getting published and propose a defense solution to effectively protect users. First, we uncover Amazon's skill vetting strategy and the privacy issues relevant to their vetting. We reveal that, in addition to the skill certification process before a skill gets published, Amazon also deploys a skill monitoring scheme after the skill is published. We further discover limitations of this monitoring scheme that have not been explored in previous research. Lastly, to address these issues, we propose a run-time skill monitoring approach to check the consistency of the skill behaviors when users interact with skills. Our findings suggest a call for action to improve the vetting process for VPA skills without placing a burden on skill developers and help developers adhere to policies.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640102"}, {"primary_key": "633096", "vector": [], "sparse_vector": [], "title": "MalCertain: Enhancing Deep Neural Network Based Android Malware Detection by Tackling Prediction Uncertainty.", "authors": ["<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The long-lasting Android malware threat has attracted significant research efforts in malware detection. In particular, by modeling malware detection as a classification problem, machine learning based approaches, especially deep neural network (DNN) based approaches, are increasingly being used for Android malware detection and have achieved significant improvements over other detection approaches such as signature-based approaches. However, as Android malware evolve rapidly and the presence of adversarial samples, DNN models trained on early constructed samples often yield poor decisions when used to detect newly emerging samples. Fundamentally, this phenomenon can be summarized as the uncertainly in the data (noise or randomness) and the weakness in the training process (insufficient training data). Overlooking these uncertainties poses risks in the model predictions. In this paper, we take the first step to estimate the prediction uncertainty of DNN models in malware detection and leverage these estimates to enhance Android malware detection techniques. Specifically, besides training a DNN model to predict malware, we employ several uncertainty estimation methods to train a Correction Model that determines whether a sample is correctly or incorrectly predicted by the DNN model. We then leverage the estimated uncertainty output by the Correction Model to correct the prediction results, improving the accuracy of the DNN model. Experimental results show that our proposed MalCertain effectively improves the accuracy of the underlying DNN models for Android malware detection by around 21% and significantly improves the detection effectiveness of adversarial Android malware samples by up to 94.38%. Our research sheds light on the promising direction that leverages prediction uncertainty to improve prediction-based software engineering tasks.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639122"}, {"primary_key": "633098", "vector": [], "sparse_vector": [], "title": "Unveiling the Life Cycle of User Feedback: Best Practices from Software Practitioners.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "User feedback has grown in importance for organizations to improve software products. Prior studies focused primarily on feedback collection and reported a high-level overview of the processes, often overlooking how practitioners reason about, and act upon this feedback through a structured set of activities. In this work, we conducted an exploratory interview study with 40 practitioners from 32 organizations of various sizes and in several domains such as e-commerce, analytics, and gaming. Our findings indicate that organizations leverage many different user feedback sources. Social media emerged as a key category of feedback that is increasingly critical for many organizations. We found that organizations actively engage in a number of non-trivial activities to curate and act on user feedback, depending on its source. We synthesize these activities into a life cycle of managing user feedback. We also report on the best practices for managing user feedback that we distilled from responses of practitioners who felt that their organization effectively understood and addressed their users' feedback. We present actionable empirical results that organizations can leverage to increase their understanding of user perception and behavior for better products thus reducing user attrition.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623309"}, {"primary_key": "633099", "vector": [], "sparse_vector": [], "title": "RUNNER: Responsible UNfair NEuron Repair for Enhancing Deep Neural Network Fairness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep Neural Networks (DNNs), an emerging software technology, have achieved impressive results in a variety of fields. However, the discriminatory behaviors towards certain groups (a.k.a. unfairness) of DNN models increasingly become a social concern, especially in high-stake applications such as loan approval and criminal risk assessment. Although there has been a number of works to improve model fairness, most of them adopt an adversary to either expand the model architecture or augment training data, which introduces excessive computational overhead. Recent work diagnoses responsible unfair neurons first and fixes them with selective retraining. Unfortunately, existing diagnosis process is time-consuming due to multi-step training sample analysis, and selective retraining may cause a performance bottleneck due to indirectly adjusting unfair neurons on biased samples. In this paper, we propose Responsible UNfair NEuron Repair (RUNNER) that improves existing works in three key aspects: (1) efficiency: we design the Importance-based Neuron Diagnosis that identifies responsible unfair neurons in one step with a novel importance criterion of neurons; (2) effectiveness: we design the Neuron Stabilizing Retraining by adding a loss term that measures the activation distance of responsible unfair neurons from different subgroups in all sources; (3) generalization: we investigate the effectiveness on both structured tabular data and large-scale unstructured image data, which is often ignored in prior studies. Our extensive experiments across 5 datasets show that RUUNER can effectively and efficiently diagnose and repair the DNNs regarding unfairness. On average, our approach significantly reduces computing overhead from 341.7s to 29.65s, and achieves improved fairness up to 79.3%. Besides, RUNNER also keeps state-of-the-art results on the unstructured dataset.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623334"}, {"primary_key": "633100", "vector": [], "sparse_vector": [], "title": "ECFuzz: Effective Configuration Fuzzing for Large-Scale Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A large-scale system contains a huge configuration space because of its large number of configuration parameters. This leads to a combination explosion among configuration parameters when exploring the configuration space. Existing configuration testing techniques first use fuzzing to generate different configuration parameters, and then directly inject them into the program under test to find configuration-induced bugs. However, they do not fully consider the complexity of large-scale systems, resulting in low testing effectiveness. In this paper, we propose ECFuzz, an effective configuration fuzzer for large-scale systems. Our core approach consists of (i) Multi-dimensional configuration generation strategy. ECFuzz first designs different mutation strategies according to different dependencies and selects multiple configuration parameters from the candidate configuration parameters to effectively generate configuration parameters; (ii) Unit-testing-oriented configuration validation strategy. ECFuzz introduces unit testing into configuration testing techniques to filter out configuration parameters that are unlikely to yield errors before executing system testing, and effectively validate generated configuration parameters. We have conducted extensive experiments in real-world large-scale systems including HCommon, HDFS, HBase, ZooKeeper and Alluxio. Our evaluation shows that ECFuzz is effective in finding configuration-induced crash bugs. Compared with the state-of-the-art configuration testing tools including ConfTest, ConfErr and ConfDiagDetector, ECFuzz finds 60.3--67 more unexpected failures when the same 1000 testcases are injected into the system with an increase of 1.87x--2.63x. Moreover, ECFuzz has exposed 14 previously unknown bugs, and 5 of them have been confirmed.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623315"}, {"primary_key": "633103", "vector": [], "sparse_vector": [], "title": "Finding XPath Bugs in XML Document Processors via Differential Testing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Extensible Markup Language (XML) is a widely used file format for data storage and transmission. Many XML processors support XPath, a query language that enables the extraction of elements from XML documents. These systems can be affected by logic bugs, which are bugs that cause the processor to return incorrect results. In order to tackle such bugs, we propose a new approach, which we realized as a system called XPress. As a test oracle, XPress relies on differential testing, which compares the results of multiple systems on the same test input, and identifies bugs through discrepancies in their outputs. As test inputs, XPress generates both XML documents and XPath queries. Aiming to generate meaningful queries that compute non-empty results, XPress selects a so-called targeted node to guide the XPath expression generation process. Using the targeted node, XPress generates XPath expressions that reference existing context related to the targeted node, such as its tag name and attributes, while also guaranteeing that a predicate evaluates to true before further expanding the query. We tested our approach on six mature XML processors, BaseX, eXist-DB, Saxon, PostgreSQL, libXML2, and a commercial database system. In total, we have found 27 unique bugs in these systems, of which 25 have been verified by the developers, and 20 of which have been fixed. XPress is efficient, as it finds 12 unique bugs in BaseX in 24 hours, which is 2× as fast as naive random generation. We expect that the effectiveness and simplicity of our approach will help to improve the robustness of many XML processors.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639208"}, {"primary_key": "633104", "vector": [], "sparse_vector": [], "title": "Fine-SE: Integrating Semantic Features and Expert Features for Software Effort Estimation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Reliable effort estimation is of paramount importance to software planning and management, especially in industry that requires effective and on-time delivery. Although various estimation approaches have been proposed (e.g., planning poker and analogy), they may be manual and/or subjective, which are difficult to apply to other projects. In recent years, deep learning approaches for effort estimation that rely on learning expert features or semantic features respectively have been extensively studied and have been found to be promising. Semantic features and expert features describe software tasks from different perspectives, however, in the literature, the best combination of these two features has not been explored to enhance effort estimation. Additionally, there are a few studies that discuss which expert features are useful for estimating effort in the industry. To this end, we investigate the potential 13 expert features that can be used to estimate effort by interviewing 26 enterprise employees. Based on that, we propose a novel model, called Fine-SE, that leverages semantic features and expert features for effort estimation. To validate our model, a series of evaluations are conducted on more than 30,000 software tasks from 17 industrial projects of a global ICT enterprise and four open-source software (OSS) projects. The evaluation results indicate that Fine-SE provides higher performance than the baselines on evaluation measures (i.e., mean absolute error, mean magnitude of relative error, and performance indicator), particularly in industrial projects with large amounts of software tasks, which implies a significant improvement in effort estimation. In comparison with expert estimation, Fine-SE improves the performance of evaluation measures by 32.0%-45.2% in within-project estimation. In comparison with the state-of-the-art models, Deep-SE and GPT2SP, it also achieves an improvement of 8.9%-91.4% in industrial projects. The experimental results reveal the value of integrating expert features with semantic features in effort estimation.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623349"}, {"primary_key": "633105", "vector": [], "sparse_vector": [], "title": "On Extracting Specialized Code Abilities from Large Language Models: A Feasibility Study.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent advances in large language models (LLMs) significantly boost their usage in software engineering. However, training a well-performing LLM demands a substantial workforce for data collection and annotation. Moreover, training datasets may be proprietary or partially open, and the process often requires a costly GPU cluster. The intellectual property value of commercial LLMs makes them attractive targets for imitation attacks, but creating an imitation model with comparable parameters still incurs high costs. This motivates us to explore a practical and novel direction: slicing commercial black-box LLMs using medium-sized backbone models.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639091"}, {"primary_key": "633106", "vector": [], "sparse_vector": [], "title": "Prism: Decomposing Program Semantics for Code Clone Detection through Compilation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Code clone detection (CCD) is of critical importance in software engineering, while semantic similarity is a key evaluation factor for CCD. The embedding technique, which represents an object using a numerical vector, is utilized to generate code representations, where code snippets with similar semantics (clone pairs) should have similar vectors. However, due to the diversity and flexibility of high-level program languages, the code representation of clone pairs may be inconsistent. Assembly code provides the program execution trace and can normalize the diversity of high-level languages in terms of the program behavior semantics. After revisiting the assembly language, we find that different assembly codes can align with the computational logic and memory access patterns of cloned pairs. Therefore, the use of multiple assembly languages can capture the behavior semantics to enhance the understanding of programs. Thus, we propose Prism, a new method for code clone detection fusing behavior semantics from multiple architecture assembly code, which directly captures multilingual domains' syntax and semantic information. Additionally, we introduce a multi-feature fusion strategy that leverages global information interaction to expand the representation space. This fusion process allows us to capture the complementary information from each feature and leverage the relationships between them to create a more expressive representation of the code. After testing the OJClone dataset, the Prism model exhibited exceptional performance with precision and recall scores of 0.999 and 0.999, respectively.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639129"}, {"primary_key": "633107", "vector": [], "sparse_vector": [], "title": "Demystifying Compiler Unstable Feature Usage and Impacts in the Rust Ecosystem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Shen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Rust programming language is gaining popularity rapidly in building reliable and secure systems due to its security guarantees and outstanding performance. To provide extra functionalities, the Rust compiler introduces Rust unstable features (RUF) to extend compiler functionality, syntax, and standard library support. However, these features are unstable and may get removed, introducing compilation failures to dependent packages. Even worse, their impacts propagate through transitive dependencies, causing large-scale failures in the whole ecosystem. Although RUF is widely used in Rust, previous research has primarily concentrated on Rust code safety, with the usage and impacts of RUF from the Rust compiler remaining unexplored. Therefore, we aim to bridge this gap by systematically analyzing the RUF usage and impacts in the Rust ecosystem. We propose novel techniques for extracting RUF precisely, and to assess its impact on the entire ecosystem quantitatively, we accurately resolve package dependencies. We have analyzed the whole Rust ecosystem with 590K package versions and 140M transitive dependencies. Our study shows that the Rust ecosystem uses 1000 different RUF, and at most 44% of package versions are affected by RUF, causing compiling failures for at most 12% of package versions. To mitigate wide RUF impacts, we further design and implement a RUF-compilation-failure recovery tool that can recover up to 90% of the failure. We believe our techniques, findings, and tools can help stabilize the Rust compiler, ultimately enhancing the security and reliability of the Rust ecosystem.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623352"}, {"primary_key": "633110", "vector": [], "sparse_vector": [], "title": "LogShrink: Effective Log Compression by Leveraging Commonality and Variability of Log Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Log data is a crucial resource for recording system events and states during system execution. However, as systems grow in scale, log data generation has become increasingly explosive, leading to an expensive overhead on log storage, such as several petabytes per day in production. To address this issue, log compression has become a crucial task in reducing disk storage while allowing for further log analysis. Unfortunately, existing general-purpose and log-specific compression methods have been limited in their ability to utilize log data characteristics. To overcome these limitations, we conduct an empirical study and obtain three major observations on the characteristics of log data that can facilitate the log compression task. Based on these observations, we propose LogShrink, a novel and effective log compression method by leveraging commonality and variability of log data. An analyzer based on longest common subsequence and entropy techniques is proposed to identify the latent commonality and variability in log messages. The key idea behind this is that the commonality and variability can be exploited to shrink log data with a shorter representation. Besides, a clustering-based sequence sampler is introduced to accelerate the commonality and variability analyzer. The extensive experimental results demonstrate that LogShrink can exceed baselines in compression ratio by 16% to 356% on average while preserving a reasonable compression speed.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608129"}, {"primary_key": "633112", "vector": [], "sparse_vector": [], "title": "A Large-Scale Survey on the Usability of AI Programming Assistants: Successes and Challenges.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The software engineering community recently has witnessed widespread deployment of AI programming assistants, such as GitHub Copilot. However, in practice, developers do not accept AI programming assistants' initial suggestions at a high frequency. This leaves a number of open questions related to the usability of these tools. To understand developers' practices while using these tools and the important usability challenges they face, we administered a survey to a large population of developers and received responses from a diverse set of 410 developers. Through a mix of qualitative and quantitative analyses, we found that developers are most motivated to use AI programming assistants because they help developers reduce key-strokes, finish programming tasks quickly, and recall syntax, but resonate less with using them to help brainstorm potential solutions. We also found the most important reasons why developers do not use these tools are because these tools do not output code that addresses certain functional or non-functional requirements and because developers have trouble controlling the tool to generate the desired output. Our findings have implications for both creators and users of AI programming assistants, such as designing minimal cognitive effort interactions with these tools to reduce distractions for users while they are programming.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608128"}, {"primary_key": "633113", "vector": [], "sparse_vector": [], "title": "PonziGuard: Detecting Ponzi Schemes on Ethereum with Contract Runtime Behavior Graph (CRBG).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Ponzi schemes, a form of scam, have been discovered in Ethereum smart contracts in recent years, causing massive financial losses. Rule-based detection approaches rely on pre-defined rules with limited capabilities and domain knowledge dependency. Additionally, using static information like opcodes and transactions for machine learning models fails to effectively characterize the Ponzi contracts, resulting in poor reliability and interpretability.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623318"}, {"primary_key": "633114", "vector": [], "sparse_vector": [], "title": "Challenges, Strengths, and Strategies of Software Engineers with ADHD: A Case Study.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Kiev Gama"], "summary": "Neurodiversity describes brain function variation in individuals, including Attention deficit hyperactivity disorder (ADHD) and Autism spectrum disorder. Neurodivergent individuals both experience challenges and exhibit strengths in the workplace. As an important disorder included under the neurodiversity term, an estimated 5.0% to 7.1% of the world population have ADHD. However, existing studies involving ADHD in the workplace are of general nature and do not focus on software engineering (SE) activities. To address this gap, we performed an exploratory qualitative case study on the experiences of people with ADHD working in SE. We find that people with ADHD struggle with several important SE-related activities, e.g., task organisation and estimation, attention to work, relation to others. Furthermore, they experience issues with physical and mental health. In terms of strengths, they exhibit, e.g., increased creative skills, perform well when solving puzzles, and have the capability to think ahead. Our findings align with clinical ADHD research, having important implications to SE practice. Lay Abstract - Neurodiversity describes brain function variation in individuals, such as Attention deficit hyperactivity disorder (ADHD) and Autism spectrum disorder. People included under this term often experience problems in their work, e.g., due to differences in communication or behaviour, but also exhibit strengths compared to people without these disorders. To better include them, it is essential that we understand how these challenges and strengths manifest in different professions. There is limited research on how neurodiversity affects professionals in software engineering (SE), an environment characterised by a rapid pace, frequent change, and intense collaborative work. Therefore, we studied the strengths, challenges, and strategies of SE professionals with ADHD, a disorder affecting approximately 5.0% to 7.1% of the world population. We find that these professionals perceive many common SE activities as challenging, e.g., estimating how long tasks take, or maintaining focus. Interestingly, they also exhibit highly relevant strengths to the SE industry, such as increased creativity and systems thinking. Based on our findings, we provide several recommendations on how SE companies can better support employees with ADHD.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640107"}, {"primary_key": "633115", "vector": [], "sparse_vector": [], "title": "On the Helpfulness of Answering Developer Questions on Discord with Similar Conversations and Posts from the Past.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A big part of software developers' time is spent finding answers to their coding-task-related questions. To answer their questions, developers usually perform web searches, ask questions on Q&A websites, or, more recently, in chat communities. Yet, many of these questions have frequently already been answered in previous chat conversations or other online communities. Automatically identifying and then suggesting these previous answers to the askers could, thus, save time and effort. In an empirical analysis, we first explored the frequency of repeating questions on the Discord chat platform and assessed our approach to identify them automatically. The approach was then evaluated with real-world developers in a field experiment, through which we received 142 ratings on the helpfulness of the suggestions we provided to help answer 277 questions that developers posted in four Discord communities. We further collected qualitative feedback through 53 surveys and 10 follow-up interviews. We found that the suggestions were considered helpful in 40% of the cases, that suggesting Stack Overflow posts is more often considered helpful than past Discord conversations, and that developers have difficulties describing their problems as search queries and, thus, prefer describing them as natural language questions in online communities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623341"}, {"primary_key": "633117", "vector": [], "sparse_vector": [], "title": "Mining Pull Requests to Detect Process Anomalies in Open Source Software Development.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Hong<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Trustworthy Open Source Software (OSS) development processes are the basis that secures the long-term trustworthiness of software projects and products. With the aim to investigate the trustworthiness of the Pull Request (PR) process, the common model of collaborative development in OSS community, we exploit process mining to identify and analyze the normal and anomalous patterns of PR processes, and propose our approach to identifying anomalies from both control-flow and semantic aspects, and then to analyze and synthesize the root causes of the identified anomalies. We analyze 17531 PRs of 18 OSS projects on GitHub, extracting 26 root causes of control-flow anomalies and 19 root causes of semantic anomalies. We find that most PRs can hardly contain both semantic anomalies and control-flow anomalies, and the internal custom rules in projects may be the key causes for the identified anomalous PRs. We further discover and analyze the patterns of normal PR processes. We find that PRs in the non-fork model (42%) are far more likely than the fork model (5%) to bypass the review process, indicating a higher potential risk. Besides, we analyzed nine poisoned projects whose PR practices were indeed worse. Given the complex and diverse PR processes in OSS community, the proposed approach can help identify and understand not only anomalous PRs but also normal PRs, which offers early risk indications of suspicious incidents (such as poisoning) to OSS supply chain.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639196"}, {"primary_key": "633118", "vector": [], "sparse_vector": [], "title": "A Framework For Inferring Properties of User-Defined Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "User-defined functions (UDFs) are widely used to enhance the capabilities of DBMSs. However, using UDFs comes with a significant performance penalty because DBMSs treat UDFs as black boxes, which hinders their ability to optimize queries that invoke such UDFs. To mitigate this problem, in this paper we present LAMBDA, a technique and framework for improving DBMSs' performance in the presence of UDFs. The core idea of LAMBDA is to statically infer properties of UDFs that facilitate UDF processing. Taking one such property as an example, if DBMSs know that a UDF is pure, that is it returns the same result given the same arguments, they can leverage a cache to avoid repetitive UDF invocations that have the same call arguments.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639147"}, {"primary_key": "633121", "vector": [], "sparse_vector": [], "title": "JLeaks: A Featured Resource Leak Repository Collected From Hundreds of Open-Source Java Projects.", "authors": ["<PERSON><PERSON><PERSON> Liu", "Weixing Ji", "<PERSON><PERSON>", "Wu<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Haiyang Peng", "<PERSON><PERSON><PERSON>"], "summary": "High-quality defect repositories are vital in defect detection, localization, and repair. However, existing repositories collected from open-source projects are either small-scale or inadequately labeled and packed. This paper systematically summarizes the programming APIs of system resources (i.e., file, socket, and thread) in Java. Additionally, this paper demonstrates the exceptions that may cause resource leaks in the chained and nested streaming operations. A semi-automatic toolchain is built to improve the efficiency of defect extraction, including automatic building for large legacy Java projects. Accordingly, 1,094 resource leaks were collected from 321 open-source projects on GitHub. This repository, named JLeaks, was built by round-by-round filtering and cross-validation, involving the review of approximately 3,185 commits from hundreds of projects. JLeaks is currently the largest resource leak repository, and each defect in JLeaks is well-labeled and packed, including causes, locations, patches, source files, and compiled bytecode files for 254 defects. We have conducted a detailed analysis of JLeaks for defect distribution, root causes, and fix approaches. We compare JLeaks with two well-known resource leak repositories, and the results show that JLeaks is more informative and complete, with high availability, uniqueness, and consistency. Additionally, we show the usability of JLeaks in two application scenarios. Future studies can leverage our repository to encourage better design and implementation of defect-related algorithms and tools.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639162"}, {"primary_key": "633123", "vector": [], "sparse_vector": [], "title": "Pre-training by Predicting Program Dependencies for Vulnerability Analysis Tasks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Vulnerability analysis is crucial for software security. Inspired by the success of pre-trained models on software engineering tasks, this work focuses on using pre-training techniques to enhance the understanding of vulnerable code and boost vulnerability analysis. The code understanding ability of a pre-trained model is highly related to its pre-training objectives. The semantic structure, e.g., control and data dependencies, of code is important for vulnerability analysis. However, existing pre-training objectives either ignore such structure or focus on learning to use it. The feasibility and benefits of learning the knowledge of analyzing semantic structure have not been investigated. To this end, this work proposes two novel pre-training objectives, namely Control Dependency Prediction (CDP) and Data Dependency Prediction (DDP), which aim to predict the statement-level control dependencies and token-level data dependencies, respectively, in a code snippet only based on its source code. During pre-training, CDP and DDP can guide the model to learn the knowledge required for analyzing fine-grained dependencies in code. After pre-training, the pre-trained model can boost the understanding of vulnerable code during fine-tuning and can directly be used to perform dependence analysis for both partial and complete functions. To demonstrate the benefits of our pre-training objectives, we pre-train a Transformer model named PDBERT with CDP and DDP, fine-tune it on three vulnerability analysis tasks, i.e., vulnerability detection, vulnerability classification, and vulnerability assessment, and also evaluate it on program dependence analysis. Experimental results show that PDBERT benefits from CDP and DDP, leading to state-of-the-art performance on the three downstream tasks. Also, PDBERT achieves F1-scores of over 99% and 94% for predicting control and data dependencies, respectively, in partial and complete functions.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639142"}, {"primary_key": "633124", "vector": [], "sparse_vector": [], "title": "FuzzInMem: Fuzzing Programs via In-memory Structures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>g <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In recent years, coverage-based greybox fuzzing has proven to be an effective and practical technique for discovering software vulnerabilities. The availability of American Fuzzy Loop (AFL) has facilitated numerous advances in overcoming challenges in fuzzing. However, the issue of mutating complex file formats, such as PDF, remains unresolved due to strict constraints. Existing fuzzers often produce mutants that fail to parse by applications, limited by bit/byte mutations performed on input files. Our observation is that most in-memory representations of file formats are simple, and well-designed applications have built-in printer functions to emit these structures as files. Thus, we propose a new technique that mutates the in-memory structures of inputs and utilizes printer functions to regenerate mutated files. Unlike prior approaches that require complex analysis to learn file format constraints, our technique leverages the printer function to preserve format constraints. We implement a prototype called FuzzInMem and compare it with AFL as well as other state-of-the-art fuzzers, including AFL++, Mopt, Weizz, and FormatFuzzer. The results show that FuzzInMem is scalable and substantially outperforms general-purpose fuzzers in terms of valid seed generation and path coverage. By applying FuzzInMem to real-world applications, we found 29 unique vulnerabilities and were awarded 5 CVEs.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639172"}, {"primary_key": "633125", "vector": [], "sparse_vector": [], "title": "MiniMon: Minimizing Android Applications with Intelligent Monitoring-Based Debloating.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>hung", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The size of Android applications is getting larger to fulfill the requirements of various users. However, not all the features of the applications are needed and desired by a specific user. The unnecessary and non-desired features can increase the attack surface and consume system resources such as storage and memory. To address this issue, we propose a framework, MiniMon, to debloat unnecessary features from an Android app based on the logs of specific users' interactions with the app.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639113"}, {"primary_key": "633126", "vector": [], "sparse_vector": [], "title": "Semantic-Enhanced Static Vulnerability Detection in Baseband Firmware.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cellular network is the infrastructure of mobile communication. Baseband firmware, which carries the implementation of cellular network, has critical security impact on its vulnerabilities. To handle the inherent complexity in cellular communication, cellular protocols are usually implemented as message-centric systems, containing the common message processing phase and message specific handling phase. Though the latter takes most of the code (99.67%) and exposed vulnerabilities (74%), it is rather under-studied: existing detectors either cannot sufficiently analyze it or focused on analyzing the former phase.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639158"}, {"primary_key": "633127", "vector": [], "sparse_vector": [], "title": "Extrapolating Coverage Rate in Greybox Fuzzing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A fuzzer can literally run forever. However, as more resources are spent, the coverage rate continuously drops, and the utility of the fuzzer declines. To tackle this coverage-resource tradeoff, we could introduce a policy to stop a campaign whenever the coverage rate drops below a certain threshold value, say 10 new branches covered per 15 minutes. During the campaign, can we predict the coverage rate at some point in the future? If so, how well can we predict the future coverage rate as the prediction horizon or the current campaign length increases? How can we tackle the statistical challenge of adaptive bias, which is inherent in greybox fuzzing (i.e., samples are not independent and identically distributed)?", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639198"}, {"primary_key": "633129", "vector": [], "sparse_vector": [], "title": "Strengthening Supply Chain Security with Fine-grained Safe Patch Identification.", "authors": ["Chang<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Enhancing supply chain security is crucial, often involving the detection of patches in upstream software. However, current security patch analysis works yield relatively low recall rates (i.e., many security patches are missed). In this work, we offer a new solution to detect safe patches and assist downstream developers in patch propagation. Specifically, we develop SPatch to detect fine-grained safe patches. SPatch leverages fine-grained patch analysis and a new differential symbolic execution technique to analyze the functional impacts of code changes.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639104"}, {"primary_key": "633131", "vector": [], "sparse_vector": [], "title": "SCVHunter: Smart Contract Vulnerability Detection Based on Heterogeneous Graph Attention Network.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yu <PERSON>", "Sixing <PERSON>"], "summary": "Smart contracts are integral to blockchain's growth, but their vulnerabilities pose a significant threat. Traditional vulnerability detection methods rely heavily on expert-defined complex rules that are labor-intensive and dificult to adapt to the explosive expansion of smart contracts. Some recent studies of neural network-based vulnerability detection also have room for improvement. Therefore, we propose SCVHunter, an extensible framework for smart contract vulnerability detection. Specifically, SCVHunter designs a heterogeneous semantic graph construction phase based on intermediate representations and a vulnerability detection phase based on a heterogeneous graph attention network for smart contracts. In particular, SCVHunter allows users to freely point out more important nodes in the graph, leveraging expert knowledge in a simpler way to aid the automatic capture of more information related to vulnerabilities. We tested SCVHunter on reentrancy, block info dependency, nested call, and transaction state dependency vulnerabilities. Results show remarkable performance, with accuracies of 93.72%, 91.07%, 85.41%, and 87.37% for these vulnerabilities, surpassing previous methods.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639213"}, {"primary_key": "633132", "vector": [], "sparse_vector": [], "title": "Breaking the Flow: A Study of Interruptions During Software Engineering Activities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In software engineering, interruptions during tasks can have significant implications for productivity and well-being. While previous studies have investigated the effect of interruptions on productivity, to the best of our knowledge, no prior work has yet distinguished the effect of different types of interruptions on software engineering activities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639079"}, {"primary_key": "633133", "vector": [], "sparse_vector": [], "title": "LLMParser: An Exploratory Study on Using Large Language Models for Log Parsing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Logs are important in modern software development with runtime information. Log parsing is the first step in many log-based analyses, that involve extracting structured information from unstructured log data. Traditional log parsers face challenges in accurately parsing logs due to the diversity of log formats, which directly impacts the performance of downstream log-analysis tasks. In this paper, we explore the potential of using Large Language Models (LLMs) for log parsing and propose LLMParser, an LLM-based log parser based on generative LLMs and few-shot tuning. We leverage four LLMs, Flan-T5-small, Flan-T5-base, LLaMA-7B, and ChatGLM-6B in LLMParsers. Our evaluation of 16 open-source systems shows that LLMParser achieves statistically significantly higher parsing accuracy than state-of-the-art parsers (a 96% average parsing accuracy). We further conduct a comprehensive empirical analysis on the effect of training size, model size, and pre-training LLM on log parsing accuracy. We find that smaller LLMs may be more effective than more complex LLMs; for instance where Flan-T5-base achieves comparable results as LLaMA-7B with a shorter inference time. We also find that using LLMs pre-trained using logs from other systems does not always improve parsing accuracy. While using pre-trained Flan-T5-base shows an improvement in accuracy, pre-trained LLaMA results in a decrease (decrease by almost 55% in group accuracy). In short, our study provides empirical evidence for using LLMs for log parsing and highlights the limitations and future research direction of LLM-based log parsers.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639150"}, {"primary_key": "633134", "vector": [], "sparse_vector": [], "title": "VeRe: Verification Guided Synthesis for Repairing Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Youcheng Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Neural network repair aims to fix the 'bugs'1 of neural networks by modifying the model's architecture or parameters. However, due to the data-driven nature of neural networks, it is difficult to explain the relationship between the internal neurons and erroneous behaviors, making further repair challenging. While several work exists to identify responsible neurons based on gradient or causality analysis, their effectiveness heavily rely on the quality of available 'bugged' data and multiple heuristics in layer or neuron selection. In this work, we address the issue utilizing the power of formal verification (in particular for neural networks). Specifically, we propose VeRe, a verification-guided neural network repair framework that performs fault localization based on linear relaxation to symbolically calculate the repair significance of neurons and furthermore optimize the parameters of problematic neurons to repair erroneous behaviors. We evaluated VeRe on various repair tasks, and our experimental results show that VeRe can efficiently and effectively repair all neural networks without degrading the model's performance. For the task of removing backdoors, VeRe successfully reduces attack success rate from 98.47% to 0.38% on average, while causing an average performance drop of 0.9%. For the task of repairing safety properties, VeRe successfully repairs all the 36 tasks and achieves 99.87% generalization on average.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623332"}, {"primary_key": "633135", "vector": [], "sparse_vector": [], "title": "KnowLog: Knowledge Enhanced Pre-trained Language Model for Log Understanding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sihang Jiang", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Logs as semi-structured text are rich in semantic information, making their comprehensive understanding crucial for automated log analysis. With the recent success of pre-trained language models in natural language processing, many studies have leveraged these models to understand logs. Despite their successes, existing pre-trained language models still suffer from three weaknesses. Firstly, these models fail to understand domain-specific terminology, especially abbreviations. Secondly, these models struggle to adequately capture the complete log context information. Thirdly, these models have difficulty in obtaining universal representations of different styles of the same logs. To address these challenges, we introduce KnowLog, a knowledge-enhanced pre-trained language model for log understanding. Specifically, to solve the previous two challenges, we exploit abbreviations and natural language descriptions of logs from public documentation as local and global knowledge, respectively, and leverage this knowledge by designing novel pre-training tasks for enhancing the model. To solve the last challenge, we design a contrastive learning-based pre-training task to obtain universal representations. We evaluate KnowLog by fine-tuning it on six different log understanding tasks. Extensive experiments demonstrate that KnowLog significantly enhances log understanding and achieves state-of-the-art results compared to existing pre-trained language models without knowledge enhancement. Moreover, we conduct additional experiments in transfer learning and low-resource scenarios, showcasing the substantial advantages of KnowLog. Our source code and detailed experimental data are available at https://github.com/LeaperOvO/KnowLog.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623304"}, {"primary_key": "633139", "vector": [], "sparse_vector": [], "title": "On Using GUI Interaction Data to Improve Text Retrieval-based Bug Localization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "One of the most important tasks related to managing bug reports is localizing the fault so that a fix can be applied. As such, prior work has aimed to automate this task of bug localization by formulating it as an information retrieval problem, where potentially buggy files are retrieved and ranked according to their textual similarity with a given bug report. However, there is often a notable semantic gap between the information contained in bug reports and identifiers or natural language contained within source code files. For user-facing software, there is currently a key source of information that could aid in bug localization, but has not been thoroughly investigated - information from the graphical user interface (GUI).", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608139"}, {"primary_key": "633140", "vector": [], "sparse_vector": [], "title": "Challenges and Opportunities in Model Checking Large-scale Distributed Systems.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The goal of the Must project is to provide design and verification support for industrial-scale distributed systems. We provide an overview of the project: its design goals, its technical features, as well as some lessons we learnt in the process of transferring academic research to an industrial tool.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3649398"}, {"primary_key": "633144", "vector": [], "sparse_vector": [], "title": "Testing Graph Database Systems via Equivalent Query Rewriting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Yu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph Database Management Systems (GDBMS), which utilize graph models for data storage and execute queries via graph traversals, have seen ubiquitous usage in real-world scenarios such as recommendation systems, knowledge graphs, and social networks. Much like Relational Database Management Systems (RDBMS), GDBMS are not immune to bugs. These bugs typically manifest as logic errors that yield incorrect results (e.g., omitting a node that should be included), performance bugs (e.g., long execution time caused by redundant graph scanning), and exception issues (e.g., unexpected or missing exceptions).", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639200"}, {"primary_key": "633147", "vector": [], "sparse_vector": [], "title": "Micro-inequities and immigration backgrounds in the software industry.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Micro-inequities are subtle, repetitive, and often unintentional forms of negative messaging, that can account for a significant burden over time. Research shows that racial and gender minority groups are more likely to experience micro-inequities, and that micro-inequities have a significant negative effect on self-esteem, work performance and career advancement. However, research on micro-inequities among software practitioners, particularly with an immigration perspective, is non-existent. To bridge this gap, we investigate the experiences of software practitioners, regarding micro-inequities from an immigration perspective. We surveyed 135 immigrant and non-immigrant software practitioners working in technical roles about verbal, nonverbal and environmental micro-inequities. Our results show that immigrants experience nine out of 27 investigated forms of micro-inequities significantly more than non-immigrants. These include not being given credit for their work, feeling excluded from key social or networking opportunities and being assumed to be less competent, assertive or intelligent. Our study can serve as an incentive for practitioners to adopt (more) inclusive work practices and to raise awareness about micro-inequities in the community.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640101"}, {"primary_key": "633150", "vector": [], "sparse_vector": [], "title": "Evaluating Code Summarization Techniques: A New Metric and an Empirical Characterization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Several code summarization techniques have been proposed in the literature to automatically document a code snippet or a function. Ideally, software developers should be involved in assessing the quality of the generated summaries. However, in most cases, researchers rely on automatic evaluation metrics such as BLEU, ROUGE, and METEOR. These metrics are all based on the same assumption: The higher the textual similarity between the generated summary and a reference summary written by developers, the higher its quality. However, there are two reasons for which this assumption falls short: (i) reference summaries, e.g., code comments collected by mining software repositories, may be of low quality or even outdated; (ii) generated summaries, while using a different wording than a reference one, could be semantically equivalent to it, thus still being suitable to document the code snippet. In this paper, we perform a thorough empirical investigation on the complementarity of different types of metrics in capturing the quality of a generated summary. Also, we propose to address the limitations of existing metrics by considering a new dimension, capturing the extent to which the generated summary aligns with the semantics of the documented code snippet, independently from the reference summary. To this end, we present a new metric based on contrastive learning to capture said aspect. We empirically show that the inclusion of this novel dimension enables a more effective representation of developers' evaluations regarding the quality of automatically generated summaries.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639174"}, {"primary_key": "633151", "vector": [], "sparse_vector": [], "title": "Toward Automatically Completing GitHub Workflows.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Continuous integration and delivery (CI/CD) are nowadays at the core of software development. Their benefits come at the cost of setting up and maintaining the CI/CD pipeline, which requires knowledge and skills often orthogonal to those entailed in other software-related tasks. While several recommender systems have been proposed to support developers across a variety of tasks, little automated support is available when it comes to setting up and maintaining CI/CD pipelines. We present GH-WCOM (GitHub Workflow COMpletion), a Transformer-based approach supporting developers in writing a specific type of CI/CD pipelines, namely GitHub workflows. To deal with such a task, we designed an abstraction process to help the learning of the transformer while still making GH-WCOM able to recommend very peculiar workflow elements such as tool options and scripting elements. Our empirical study shows that GH-WCOM provides up to 34.23% correct predictions, and the model's confidence is a reliable proxy for the recommendations' correctness likelihood.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623351"}, {"primary_key": "633153", "vector": [], "sparse_vector": [], "title": "Exploring Experiences with Automated Program Repair in Practice.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated program repair, also known as APR, is an approach for automatically repairing software faults. There is a large amount of research on automated program repair, but very little offers in-depth insights into how practitioners think about and employ APR in practice. To learn more about practitioners' perspectives and experiences with current APR tools and techniques, we administered a survey, which received valid responses from 331 software practitioners. We analyzed survey responses to gain insights regarding factors that correlate with APR awareness, experience, and use. We established a strong correlation between APR awareness and tool use and attributes including job position, company size, total coding experience, and preferred language of software practitioners. We also found that practitioners are using other forms of support, such as co-workers and ChatGPT, more frequently than APR tools when fixing software defects. We learned about the drawbacks that practitioners encounter while utilizing existing APR tools and the impact that each drawback has on their practice. Our findings provide implications for research and practice centered on development, adoption, and use of APR.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639182"}, {"primary_key": "633157", "vector": [], "sparse_vector": [], "title": "Barriers for Students During Code Change Comprehension.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern code review (MCR) is a key practice for many software engineering organizations, so undergraduate software engineering courses often teach some form of it to prepare students. However, research on MCR describes how many its professional implementations can fail, to say nothing on how these barriers manifest under students' particular contexts. To uncover barriers students face when evaluating code changes during review, we combine interviews and surveys with an observational study. In a junior-level software engineering course, we first interviewed 29 undergraduate students about their experiences in code review. Next, we performed an observational study that presented 44 students from the same course with eight code change comprehension activities. These activities provided students with pull requests of potential refactorings in a familiar code base, collecting feedback on accuracy and challenges. This was followed by a reflection survey.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639227"}, {"primary_key": "633168", "vector": [], "sparse_vector": [], "title": "FuzzSlice: Pruning False Positives in Static Analysis Warnings through Function-Level Fuzzing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Manual confirmation of static analysis reports is a daunting task. This is due to both the large number of warnings and the high density of false positives among them. Fuzzing techniques have been proposed to verify static analysis warnings. However, a major limitation is that fuzzing the whole project to reach all static analysis warnings is not feasible. This can take several days and exponential machine time to increase code coverage linearly.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623321"}, {"primary_key": "633169", "vector": [], "sparse_vector": [], "title": "GenderMag Improves Discoverability in the Field, Especially for Women: An Multi-Year Case Study of Suggest Edit, a Code Review Feature.", "authors": ["<PERSON>-Hill", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Prior research shows that the GenderMag method can help identify and address usability barriers that are more likely to affect women software users than men. However, the evidence for the effectiveness of GenderMag is limited to small lab studies. In this case study, by combining self-reported gender data from tens of thousands of users of an internal code review tool with software logs data gathered over a five-year period, we quantitatively show that GenderMag helped a team at Google (a) correctly identify discoverability as a usability barrier more likely to affect women than men, and (b) increase discoverability by 2.4x while also achieving gender parity. That is, compared to men using the original code review tool, women and men using the system redesigned with GenderMag were both 2.4x more likely to discover the \"Suggest Edit\" feature at any given time. Thus, this paper contributes the first large-scale evidence of the effectiveness of GenderMag in the field.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639097"}, {"primary_key": "633170", "vector": [], "sparse_vector": [], "title": "CNEPS: A Precise Approach for Examining Dependencies among Third-Party C/C++ Open-Source Components.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rise in open-source software (OSS) reuse has led to intricate dependencies among third-party components, increasing the demand for precise dependency analysis. However, owing to the presence of reused files that are difficult to identify the originating components (i.e., indistinguishable files) and duplicated components, precisely identifying component dependencies is becoming challenging.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639209"}, {"primary_key": "633171", "vector": [], "sparse_vector": [], "title": "Using an LLM to Help With Code Understanding.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Understanding code is challenging, especially when working in new and complex development environments. Code comments and documentation can help, but are typically scarce or hard to navigate. Large language models (LLMs) are revolutionizing the process of writing code. Can they do the same for helping understand it? In this study, we provide a first investigation of an LLM-based conversational UI built directly in the IDE that is geared towards code understanding. Our IDE plugin queries OpenAI's GPT-3.5-turbo model with four high-level requests without the user having to write explicit prompts: to explain a highlighted section of code, provide details of API calls used in the code, explain key domain-specific terms, and provide usage examples for an API. The plugin also allows for open-ended prompts, which are automatically contextualized to the LLM with the program being edited. We evaluate this system in a user study with 32 participants, which confirms that using our plugin can aid task completion more than web search. We additionally provide a thorough analysis of the ways developers use, and perceive the usefulness of, our system, among others finding that the usage and benefits differ between students and professionals. We conclude that in-IDE prompt-less interaction with LLMs is a promising future direction for tool builders.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639187"}, {"primary_key": "633174", "vector": [], "sparse_vector": [], "title": "Towards Reliable AI: Adequacy Metrics for Ensuring the Quality of System-level Testing of Autonomous Vehicles.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Aldeida Aleti"], "summary": "AI-powered systems have gained widespread popularity in various domains, including Autonomous Vehicles (AVs). However, ensuring their reliability and safety is challenging due to their complex nature. Conventional test adequacy metrics, designed to evaluate the effectiveness of traditional software testing, are often insufficient or impractical for these systems. White-box metrics, which are specifically designed for these systems, leverage neuron coverage information. These coverage metrics necessitate access to the underlying AI model and training data, which may not always be available. Furthermore, the existing adequacy metrics exhibit weak correlations with the ability to detect faults in the generated test suite, creating a gap that we aim to bridge in this study.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623314"}, {"primary_key": "633176", "vector": [], "sparse_vector": [], "title": "FAIR: Flow Type-Aware Pre-Training of Compiler Intermediate Representations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "While the majority of existing pre-trained models from code learn source code features such as code tokens and abstract syntax trees, there are some other works that focus on learning from compiler intermediate representations (IRs). Existing IR-based models typically utilize IR features such as instructions, control and data flow graphs (CDFGs), call graphs, etc. However, these methods confuse variable nodes and instruction nodes in a CDFG and fail to distinguish different types of flows, and the neural networks they use fail to capture long-distance dependencies and have over-smoothing and over-squashing problems. To address these weaknesses, we propose FAIR, a Flow type-Aware pre-trained model for IR that involves employing (1) a novel input representation of IR programs; (2) Graph Transformer to address over-smoothing, over-squashing and long-dependencies problems; and (3) five pre-training tasks that we specifically propose to enable FAIR to learn the semantics of IR tokens, flow type information, and the overall representation of IR. Experimental results show that FAIR can achieve state-of-the-art results on four code-related downstream tasks.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608136"}, {"primary_key": "633179", "vector": [], "sparse_vector": [], "title": "VGX: Large-Scale Sample Generation for Boosting Learning-Based Software Vulnerability Analyses.", "authors": ["<PERSON>", "<PERSON>", "Guangbei Yi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Haipeng <PERSON>ai"], "summary": "Accompanying the successes of learning-based defensive software vulnerability analyses is the lack of large and quality sets of labeled vulnerable program samples, which impedes further advancement of those defenses. Existing automated sample generation approaches have shown potentials yet still fall short of practical expectations due to the high noise in the generated samples. This paper proposes VGX, a new technique aimed for large-scale generation of high-quality vulnerability datasets. Given a normal program, VGX identifies the code contexts in which vulnerabilities can be injected, using a customized Transformer featured with a new value-flow-based position encoding and pre-trained against new objectives particularly for learning code structure and context. Then, VGX materializes vulnerability-injection code editing in the identified contexts using patterns of such edits obtained from both historical fixes and human knowledge about real-world vulnerabilities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639116"}, {"primary_key": "633181", "vector": [], "sparse_vector": [], "title": "Data-Driven Evidence-Based Syntactic Sugar Design.", "authors": ["David <PERSON>;<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Programming languages are essential tools for developers, and their evolution plays a crucial role in supporting the activities of developers. One instance of programming language evolution is the introduction of syntactic sugars, which are additional syntax elements that provide alternative, more readable code constructs. However, the process of designing and evolving a programming language has traditionally been guided by anecdotal experiences and intuition. Recent advances in tools and methodologies for mining open-source repositories have enabled developers to make data-driven software engineering decisions. In light of this, this paper proposes an approach for motivating data-driven programming evolution by applying frequent subgraph mining techniques to a large dataset of 166,827,154 open-source Java methods. The dataset is mined by generalizing Java control-flow graphs to capture broad programming language usages and instances of duplication. Frequent subgraphs are then extracted to identify potentially impactful opportunities for new syntactic sugars. Our diverse results demonstrate the benefits of the proposed technique by identifying new syntactic sugars involving a variety of programming constructs that could be implemented in Java, thus simplifying frequent code idioms. This approach can potentially provide valuable insights for Java language designers, and serve as a proof-of-concept for data-driven programming language design and evolution.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639580"}, {"primary_key": "633182", "vector": [], "sparse_vector": [], "title": "Are Prompt Engineering and TODO Comments Friends or Foes? An Evaluation on GitHub Copilot.", "authors": ["David <PERSON>;<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Code intelligence tools such as GitHub Copilot have begun to bridge the gap between natural language and programming language. A frequent software development task is the management of technical debts, which are suboptimal solutions or unaddressed issues which hinder future software development. Developers have been found to \"self-admit\" technical debts (SATD) in software artifacts such as source code comments. Thus, is it possible that the information present in these comments can enhance code generative prompts to repay the described SATD? Or, does the inclusion of such comments instead cause code generative tools to reproduce the harmful symptoms of described technical debt? Does the modification of SATD impact this reaction? Despite the heavy maintenance costs caused by technical debt and the recent improvements of code intelligence tools, no prior works have sought to incorporate SATD towards prompt engineering. Inspired by this, this paper contributes and analyzes a dataset consisting of 36,381 TODO comments in the latest available revisions of their respective 102,424 repositories, from which we sample and manually generate 1,140 code bodies using GitHub Copilot. Our experiments show that GitHub Copilot can generate code with the symptoms of SATD, both prompted and unprompted. Moreover, we demonstrate the tool's ability to automatically repay SATD under different circumstances and qualitatively investigate the characteristics of successful and unsuccessful comments. Finally, we discuss gaps in which GitHub Copilot's successors and future researchers can improve upon code intelligence tasks to facilitate AI-assisted software maintenance.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639176"}, {"primary_key": "633184", "vector": [], "sparse_vector": [], "title": "Navigating the Path of Women in Software Engineering: From Academia to Industry.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Context. Women remain significantly underrepresented in software engineering, leading to a lasting gender gap in the software industry. This disparity starts in education and extends into the industry, causing challenges such as hostile work environments and unequal opportunities. Addressing these issues is crucial for fostering an inclusive and diverse software engineering workforce. Aim. This study aims to enhance the literature on women in software engineering, exploring their journey from academia to industry and discussing perspectives, challenges, and support. We focus on Brazilian women to extend existing research, which has largely focused on North American and European contexts. Method. In this study, we conducted a cross-sectional survey, collecting both quantitative and qualitative data, focusing on women's experiences in software engineering to explore their journey from university to the software industry. Findings. Our findings highlight persistent challenges faced by women in software engineering, including gender bias, harassment, work-life imbalance, undervaluation, low sense of belonging, and impostor syndrome. These difficulties commonly emerge from university experiences and continue to affect women throughout their entire careers. Conclusion. In summary, our study identifies systemic challenges in women's software engineering journey, emphasizing the need for organizational commitment to address these issues. We provide actionable recommendations for practitioners.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640100"}, {"primary_key": "633187", "vector": [], "sparse_vector": [], "title": "PPT4J: Patch Presence Test for Java Binaries.", "authors": ["Zhiyuan Pan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The number of vulnerabilities reported in open source software has increased substantially in recent years. Security patches provide the necessary measures to protect software from attacks and vulnerabilities. In practice, it is difficult to identify whether patches have been integrated into software, especially if we only have binary files. Therefore, the ability to test whether a patch is applied to the target binary, a.k.a. patch presence test, is crucial for practitioners. However, it is challenging to obtain accurate semantic information from patches, which could lead to incorrect results.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639231"}, {"primary_key": "633189", "vector": [], "sparse_vector": [], "title": "Towards More Practical Automation of Vulnerability Assessment.", "authors": ["<PERSON><PERSON><PERSON> Pan", "Ling<PERSON> Bao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It is increasingly suggested to identify emerging software vulnerabilities (SVs) through relevant development activities (e.g., issue reports) to allow early warnings to open source software (OSS) users. However, the support for the following assessment of the detected SVs has not yet been explored. SV assessment characterizes the detected SVs to prioritize limited remediation resources on the critical ones. To fill this gap, we aim to enable early vulnerability assessment based on SV-related issue reports (SIR). Besides, we observe the following concerns of the existing assessment techniques: 1) the assessment output lacks rationale and practical value; 2) the associations between Common Vulnerability Scoring System (CVSS) metrics have been ignored; 3) insufficient evaluation scenarios and metrics. We address these concerns to enhance the practicality of our proposed early vulnerability assessment approach (namely proEVA). Specifically, based on the observation of strong associations between CVSS metrics, we propose a prompt-based model to exploit such relations for CVSS metrics prediction. Moreover, we design a curriculum-learning (CL) schedule to guide the model better learn such hidden associations during training. Aside from the standard classification metrics adopted in existing works, we propose two severity-aware metrics to provide a more comprehensive evaluation regarding the prioritization of the high-severe SVs. Experimental results show that proEVA significantly outperforms the baselines in both types of metrics. We further discuss the transferability of the prediction model regarding the upgrade of the assessment system, an important yet overlooked evaluation scenario in existing works. The results verify that proEVA is more efficient and flexible in migrating to different assessment systems.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639110"}, {"primary_key": "633190", "vector": [], "sparse_vector": [], "title": "EDEFuzz: A Web API Fuzzer for Excessive Data Exposures.", "authors": ["<PERSON><PERSON> Pan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "APIs often transmit far more data to client applications than they need, and in the context of web applications, often do so over public channels. This issue, termed Excessive Data Exposure (EDE), was OWASP's third most significant API vulnerability of 2019. However, there are few automated tools---either in research or industry---to effectively find and remediate such issues. This is unsurprising as the problem lacks an explicit test oracle: the vulnerability does not manifest through explicit abnormal behaviours (e.g., program crashes or memory access violations).", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608133"}, {"primary_key": "633192", "vector": [], "sparse_vector": [], "title": "Lost in Translation: A Study of Bugs Introduced by Large Language Models while Translating Code.", "authors": ["Rangeet Pan", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Code translation aims to convert source code from one programming language (PL) to another. Given the promising abilities of large language models (LLMs) in code synthesis, researchers are exploring their potential to automate code translation. The prerequisite for advancing the state of LLM-based code translation is to understand their promises and limitations over existing techniques. To that end, we present a large-scale empirical study to investigate the ability of general LLMs and code LLMs for code translation across pairs of different languages, including C, C++, Go, Java, and Python. Our study, which involves the translation of 1,700 code samples from three benchmarks and two real-world projects, reveals that LLMs are yet to be reliably used to automate code translation---with correct translations ranging from 2.1% to 47.3% for the studied LLMs. Further manual investigation of unsuccessful translations identifies 15 categories of translation bugs. We also compare LLM-based code translation with traditional non-LLM-based approaches. Our analysis shows that these two classes of techniques have their own strengths and weaknesses. Finally, insights from our study suggest that providing more context to LLMs during translation can help them produce better results. To that end, we propose a prompt-crafting approach based on the symptoms of erroneous translations; this improves the performance of LLM-based code translation by 5.5% on average. Our study is the first of its kind, in terms of scale and breadth, that provides insights into the current limitations of LLMs in code translation and opportunities for improving them. Our dataset---consisting of 1,700 code samples in five PLs with 10K+ tests, 43K+ translated code, 1,748 manually labeled bugs, and 1,365 bug-fix pairs---can help drive research in this area.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639226"}, {"primary_key": "633195", "vector": [], "sparse_vector": [], "title": "Semantic Analysis of Macro Usage for Portability.", "authors": ["<PERSON>", "<PERSON>"], "summary": "C is an unsafe language. Researchers have been developing tools to port C to safer languages such as Rust, Checked C, or Go. Existing tools, however, resort to preprocessing the source file first, then porting the resulting code, leaving barely recognizable code that loses macro abstractions. To preserve macro usage, porting tools need analyses that understand macro behavior to port to equivalent constructs. But macro semantics differ from typical functions, precluding simple syntactic transformations to port them. We introduce the first comprehensive framework for analyzing the portability of macro usage. We decompose macro behavior into 26 fine-grained properties and implement a program analysis tool, called <PERSON><PERSON>, that identifies them in real-world code with 94% accuracy. We apply <PERSON><PERSON> to 21 programs containing a total of 86,199 macro definitions. We found that real-world macros are much more portable than previously known. More than a third (37%) are easy-to-port, and <PERSON><PERSON> provides hints for porting more complicated macros. We find, on average, 2x more easy-to-port macros and up to 7x more in the best case compared to prior work. Guided by <PERSON><PERSON>'s output, we found and hand-ported macros in three real-world programs. We submitted patches to Linux maintainers that transform eleven macros, nine of which have been accepted.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623323"}, {"primary_key": "633200", "vector": [], "sparse_vector": [], "title": "Hypertesting of Programs: Theoretical Foundation and Automated Test Generation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hyperproperties are used to define correctness requirements that involve relations between multiple program executions. This allows, for instance, to model security and concurrency requirements, which cannot be expressed by means of trace properties.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3640323"}, {"primary_key": "633202", "vector": [], "sparse_vector": [], "title": "Domain Knowledge Matters: Improving Prompts with Fix Templates for Repairing Python Type Errors.", "authors": ["<PERSON>", "Shuzheng Gao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As a dynamic programming language, Python has become increasingly popular in recent years. Although the dynamic type system of Python facilitates the developers in writing Python programs, it also brings type errors at run-time which are prevalent yet not easy to fix. There exist rule-based approaches for automatically repairing Python type errors. The approaches can generate accurate patches for the type errors covered by manually defined templates, but they require domain experts to design patch synthesis rules and suffer from low template coverage of real-world type errors. Learning-based approaches alleviate the manual efforts in designing patch synthesis rules and have become prevalent due to the recent advances in deep learning. Among the learning-based approaches, the prompt-based approach which leverages the knowledge base of code pre-trained models via pre-defined prompts, obtains state-of-the-art performance in general program repair tasks. However, such prompts are manually defined and do not involve any specific clues for repairing Python type errors, resulting in limited effectiveness. How to automatically improve prompts with the domain knowledge for type error repair is challenging yet under-explored.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608132"}, {"primary_key": "633203", "vector": [], "sparse_vector": [], "title": "Less is More? An Empirical Study on Configuration Issues in Python PyPI Ecosystem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shuqing Li", "<PERSON>"], "summary": "Python is the top popular programming language used in the open-source community, largely owing to the extensive support from diverse third-party libraries within the PyPI ecosystem. Nevertheless, the utilization of third-party libraries can potentially lead to conflicts in dependencies, prompting researchers to develop dependency conflict detectors. Moreover, endeavors have been made to automatically infer dependencies. These approaches focus on version-level checks and inference, based on the assumption that configurations of libraries in the PyPI ecosystem are correct. However, our study reveals that this assumption is not universally valid, and relying solely on version-level checks proves inadequate in ensuring compatible run-time environments.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639077"}, {"primary_key": "633204", "vector": [], "sparse_vector": [], "title": "A Theory of Scientific Programming Efficacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Scientists write and maintain software artifacts to construct, validate, and apply scientific theories. Despite the centrality of software in their work, their practices differ significantly from those of professional software engineers. We sought to understand what makes scientists effective at their work and how software engineering practices and tools can be adapted to fit their workflows. We interviewed 25 scientists and support staff to understand their work. Then, we constructed a theory that relates six factors that contribute to their efficacy in creating and maintaining software systems. We present the theory in the form of a cycle of scientific computing efficacy and identify opportunities for improvement based on the six contributing factors.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639139"}, {"primary_key": "633209", "vector": [], "sparse_vector": [], "title": "Out of Context: How important is Local Context in Neural Program Repair?", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning source code models have been applied very successfully to the problem of automated program repair. One of the standing issues is the small input window of current models which often cannot fully fit the context code required for a bug fix (e.g., method or class declarations of a project). Instead, input is often restricted to the local context, that is, the lines below and above the bug location. In this work we study the importance of this local context on repair success: how much local context is needed?; is context before or after the bug location more important? how is local context tied to the bug type? To answer these questions we train and evaluate Transformer models in many different local context configurations on three datasets and two programming languages. Our results indicate that overall repair success increases with the size of the local context (albeit not for all bug types) and confirm the common practice that roughly 50--60% of the input window should be used for context leading the bug. Our results are not only relevant for researchers working on Transformer-based APR tools but also for benchmark and dataset creators who must decide what and how much context to include in their datasets.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639086"}, {"primary_key": "633210", "vector": [], "sparse_vector": [], "title": "Modularizing while Training: A New Paradigm for Modularizing DNN Models.", "authors": ["Binhang Qi", "Hailong Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep neural network (DNN) models have become increasingly crucial components of intelligent software systems. However, training a DNN model is typically expensive in terms of both time and computational resources. To address this issue, recent research has focused on reusing existing DNN models - borrowing the concept of software reuse in software engineering. However, reusing an entire model could cause extra overhead or inherit the weaknesses from the undesired functionalities. Hence, existing work proposes to decompose an already trained model into modules, i.e., modularizing-after-training, to enable module reuse. Since the trained models are not built for modularization, modularizing-after-training may incur huge overhead and model accuracy loss. In this paper, we propose a novel approach that incorporates modularization into the model training process, i.e., modularizing-while-training (MwT). We train a model to be structurally modular through two loss functions that optimize intra-module cohesion and inter-module coupling. We have implemented the proposed approach for modularizing Convolutional Neural Network (CNN) models. The evaluation results on representative models demonstrate that MwT outperforms the existing state-of-the-art modularizing-after-training approach. Specifically, the accuracy loss caused by MwT is only 1.13 percentage points, which is less than that of the existing approach. The kernel retention rate of the modules generated by MwT is only 14.58%, with a reduction of 74.31% over the existing approach. Furthermore, the total time cost required for training and modularizing is only 108 minutes, which is half the time required by the existing approach. Our work demonstrates that MwT is a new and more effective paradigm for realizing DNN model modularization, offering a fresh perspective on achieving model reuse.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608135"}, {"primary_key": "633215", "vector": [], "sparse_vector": [], "title": "Towards Causal Deep Learning for Vulnerability Detection.", "authors": ["<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Mao", "Saikat Chakraborty", "<PERSON><PERSON><PERSON><PERSON> Ray", "<PERSON>"], "summary": "Deep learning vulnerability detection has shown promising results in recent years. However, an important challenge that still blocks it from being very useful in practice is that the model is not robust under perturbation and it cannot generalize well over the out-of-distribution (OOD) data, e.g., applying a trained model to unseen projects in real world. We hypothesize that this is because the model learned non-robust features, e.g., variable names, that have spurious correlations with labels. When the perturbed and OOD datasets no longer have the same spurious features, the model prediction fails. To address the challenge, in this paper, we introduced causality into deep learning vulnerability detection. Our approach CausalVul consists of two phases. First, we designed novel perturbations to discover spurious features that the model may use to make predictions. Second, we applied the causal learning algorithms, specifically, do-calculus, on top of existing deep learning models to systematically remove the use of spurious features and thus promote causal based prediction. Our results show that CausalVul consistently improved the model accuracy, robustness and OOD performance for all the state-of-the-art models and datasets we experimented. To the best of our knowledge, this is the first work that introduces do calculus based causal learning to software engineering models and shows it's indeed useful for improving the model accuracy, robustness and generalization. Our replication package is located at https://figshare.com/s/0ffda320dcb96c249ef2.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639170"}, {"primary_key": "633216", "vector": [], "sparse_vector": [], "title": "FlakeSync: Automatically Repairing Async Flaky Tests.", "authors": ["<PERSON><PERSON>", "August Shi"], "summary": "Regression testing is an important part of the development process but suffers from the presence of flaky tests. Flaky tests nondeterministically pass or fail when run on the same code, misleading developers about the correctness of their changes. A common type of flaky tests are async flaky tests that flakily fail due to timing-related issues such as asynchronous waits that do not return in time or different thread interleavings during execution. Developers commonly try to repair async flaky tests by inserting or increasing some wait time, but such repairs are unreliable.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639115"}, {"primary_key": "633220", "vector": [], "sparse_vector": [], "title": "DeepLSH: Deep Locality-Sensitive Hash Learning for Fast and Efficient Near-Duplicate Crash Report Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Automatic crash bucketing is a crucial phase in the software development process for efficiently triaging bug reports. It generally consists in grouping similar reports through clustering techniques. However, with real-time streaming bug collection, systems are needed to quickly answer the question: What are the most similar bugs to a new one?, that is, efficiently find near-duplicates. It is thus natural to consider nearest neighbors search to tackle this problem and especially the well-known locality-sensitive hashing (LSH) to deal with large datasets due to its sublinear performance and theoretical guarantees on the similarity search accuracy. Surprisingly, LSH has not been considered in the crash bucketing literature. It is indeed not trivial to derive hash functions that satisfy the so-called locality-sensitive property for the most advanced crash bucketing metrics. Consequently, we study in this paper how to leverage LSH for this task. To be able to consider the most relevant metrics used in the literature, we introduce DeepLSH, a Siamese DNN architecture with an original loss function, that perfectly approximates the locality-sensitivity property even for Jaccard and Cosine metrics for which exact LSH solutions exist. We support this claim with a series of experiments on an original dataset, which we make available.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639146"}, {"primary_key": "633222", "vector": [], "sparse_vector": [], "title": "DEMISTIFY: Identifying On-device Machine Learning Models Stealing and Reuse Vulnerabilities in Mobile Apps.", "authors": ["Pengcheng Ren", "<PERSON><PERSON>", "<PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shan<PERSON> Guo"], "summary": "Mobile apps have become popular for providing artificial intelligence (AI) services via on-device machine learning (ML) techniques. Unlike accomplishing these AI services on remote servers traditionally, these on-device techniques process sensitive information required by AI services locally, which can mitigate the severe concerns of the sensitive data collection on the remote side. However, these on-device techniques have to push the core of ML expertise (e.g., models) to smartphones locally, which are still subject to similar vulnerabilities on the remote clouds and servers, especially when facing the model stealing attack. To defend against these attacks, developers have taken various protective measures. Unfortunately, we have found that these protections are still insufficient, and on-device ML models in mobile apps could be extracted and reused without limitation. To better demonstrate its inadequate protection and the feasibility of this attack, this paper presents DeMistify, which statically locates ML models within an app, slices relevant execution components, and finally generates scripts automatically to instrument mobile apps to successfully steal and reuse target ML models freely. To evaluate DeMistify and demonstrate its applicability, we apply it on 1,511 top mobile apps using on-device ML expertise for several ML services based on their install numbers from Google Play and DeMistify can successfully execute 1250 of them (82.73%). In addition, an in-depth study is conducted to understand the on-device ML ecosystem in the mobile application.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623325"}, {"primary_key": "633223", "vector": [], "sparse_vector": [], "title": "Software Engineering Research in a World with Generative Artificial Intelligence.", "authors": ["<PERSON>"], "summary": "Generative artificial intelligence systems such as large language models (LLMs) exhibit powerful capabilities that many see as the kind of flexible and adaptive intelligence that previously only humans could exhibit. I address directions and implications of LLMs for software engineering research.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3649399"}, {"primary_key": "633230", "vector": [], "sparse_vector": [], "title": "Detecting Automatic Software Plagiarism via Token Sequence Normalization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "While software plagiarism detectors have been used for decades, the assumption that evading detection requires programming proficiency is challenged by the emergence of automated plagiarism generators. These generators enable effortless obfuscation attacks, exploiting vulnerabilities in existing detectors by inserting statements to disrupt the matching of related programs. Thus, we present a novel, language-independent defense mechanism that leverages program dependence graphs, rendering such attacks infeasible. We evaluate our approach with multiple real-world datasets and show that it defeats plagiarism generators by offering resilience against automated obfuscation while maintaining a low rate of false positives.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639192"}, {"primary_key": "633242", "vector": [], "sparse_vector": [], "title": "Toward Improved Deep Learning-based Vulnerability Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning (DL) has been a common thread across several recent techniques for vulnerability detection. The rise of large, publicly available datasets of vulnerabilities has fueled the learning process underpinning these techniques. While these datasets help the DL-based vulnerability detectors, they also constrain these detectors' predictive abilities. Vulnerabilities in these datasets have to be represented in a certain way, e.g., code lines, functions, or program slices within which the vulnerabilities exist. We refer to this representation as a base unit. The detectors learn how base units can be vulnerable and then predict whether other base units are vulnerable. We have hypothesized that this focus on individual base units harms the ability of the detectors to properly detect those vulnerabilities that span multiple base units (or MBU vulnerabilities). For vulnerabilities such as these, a correct detection occurs when all comprising base units are detected as vulnerable. Verifying how existing techniques perform in detecting all parts of a vulnerability is important to establish their effectiveness for other downstream tasks. To evaluate our hypothesis, we conducted a study focusing on three prominent DL-based detectors: ReVeal, DeepWukong, and LineVul. Our study shows that all three detectors contain MBU vulnerabilities in their respective datasets. Further, we observed significant accuracy drops when detecting these types of vulnerabilities. We present our study and a framework that can be used to help DL-based detectors toward the proper inclusion of MBU vulnerabilities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3608141"}, {"primary_key": "633243", "vector": [], "sparse_vector": [], "title": "ChatGPT-Resistant Screening Instrument for Identifying Non-Programmers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "To ensure the validity of software engineering and IT security studies with professional programmers, it is essential to identify participants without programming skills. Existing screening questions are efficient, cheating robust, and effectively differentiate programmers from non-programmers. However, the release of ChatGPT raises concerns about their continued effectiveness in identifying non-programmers. In a simulated attack, we showed that Chat-GPT can easily solve existing screening questions. Therefore, we designed new ChatGPT-resistant screening questions using visual concepts and code comprehension tasks. We evaluated 28 screening questions in an online study with 121 participants involving programmers and non-programmers. Our results showed that questions using visualizations of well-known programming concepts performed best in differentiating between programmers and non-programmers. Participants prompted to use ChatGPT struggled to solve the tasks. They considered ChatGPT ineffective and changed their strategy after a few screening questions. In total, we present six ChatGPT-resistant screening questions that effectively identify non-programmers. We provide recommendations on setting up a ChatGPT-resistant screening instrument that takes less than three minutes to complete by excluding 99.47% of non-programmers while including 94.83% of programmers.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639075"}, {"primary_key": "633246", "vector": [], "sparse_vector": [], "title": "Greening Large Language Models of Code.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Large language models of code have shown remarkable effectiveness across various software engineering tasks. Despite the availability of many cloud services built upon these powerful models, there remain several scenarios where developers cannot take full advantage of them, stemming from factors such as restricted or unreliable internet access, institutional privacy policies that prohibit external transmission of code to third-party vendors, and more. Therefore, developing a compact, efficient, and yet energy-saving model for deployment on developers' devices becomes essential.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640097"}, {"primary_key": "633247", "vector": [], "sparse_vector": [], "title": "Optimistic Prediction of Synchronization-Reversal Data Races.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Dynamic data race detection has emerged as a key technique for ensuring reliability of concurrent software in practice. However, dynamic approaches can often miss data races owing to non-determinism in the thread scheduler. Predictive race detection techniques cater to this shortcoming by inferring alternate executions that may expose data races without re-executing the underlying program. More formally, the dynamic data race prediction problem asks, given a trace σ of an execution of a concurrent program, can σ be correctly reordered to expose a data race? Existing state-of-the art techniques for data race prediction either do not scale to executions arising from real world concurrent software, or only expose a limited class of data races, such as those that can be exposed without reversing the order of synchronization operations.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639099"}, {"primary_key": "633253", "vector": [], "sparse_vector": [], "title": "Trustworthy by Design.", "authors": ["<PERSON>"], "summary": "The relatively recent public release of generative artificial intelligence (AI) systems has ignited a significant leap in awareness of the capabilities of AI. In parallel, there has been a recognition of AI system limitations and the bias inherent in systems created by humans. Expectations are rising for more trustworthy, human-centered, and responsible software connecting humans to powerful systems that augment their abilities. There are decades of practice designing systems that work with, and for humans, that we can build upon to face the new challenges and opportunities brought by dynamic AI systems.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3649400"}, {"primary_key": "633254", "vector": [], "sparse_vector": [], "title": "RogueOne: Detecting Rogue Updates via Differential Data-flow Analysis Using Trust Domains.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Kang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Cao", "<PERSON><PERSON>", "<PERSON>"], "summary": "Rogue updates, an important type of software supply-chain attack in which attackers conceal malicious code inside updates to benign software, are a growing problem due to their stealth and effectiveness. We design and implement RogueOne, a system for detecting rogue updates to JavaScript packages. RogueOne uses a novel differential data-flow analysis to capture how an update changes a package's interactions with external APIs. Using an efficient form of abstract interpretation that can exclude unchanged code in a package, it constructs an object data-flow relationship graph (ODRG) that tracks data-flows among objects. RogueOne then maps objects to trust domains, a novel abstraction which summarizes trust relationships in a package. Objects are assigned a trust domain based on whether they originate in the target package, a dependency, or in a system API. RogueOne uses the ODRG to build a set of data-flows across trust domains. It compares data-flow sets across package versions to detect untrustworthy new interactions with external APIs. We evaluated RogueOne on hundreds of npm packages, demonstrating its effectiveness at detecting rogue updates and distinguishing them from benign ones. RogueOne achieves high accuracy and can be more than seven times as effective in detecting rogue updates and avoiding false positives compared to other systems built to detect malicious packages.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639199"}, {"primary_key": "633256", "vector": [], "sparse_vector": [], "title": "Efficiently Trimming the Fat: Streamlining Software Dependencies with Java Reflection and Dependency Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Numerous third-party libraries introduced into client projects are not actually required, resulting in modern software being gradually bloated. Software developers may spend much unnecessary effort to manage the bloated dependencies: keeping the library versions up-to-date, making sure that heterogeneous licenses are compatible, and resolving dependency conflict or vulnerability issues.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639123"}, {"primary_key": "633258", "vector": [], "sparse_vector": [], "title": "ReClues: Representing and indexing failures in parallel debugging with program variables.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Failures with different root causes can greatly disrupt multi-fault localization, therefore, categorizing failures into distinct groups according to the culprit fault is highly important. In such a failure indexing task, the crux lies in the failure proximity, which comprises two points, i.e., how to effectively represent failures (e.g., extract the signature of failures) and how to properly measure the distance between those proxies for failures. Existing research has proposed a variety of failure proximities. The majority of them extract signatures of failures from execution coverage or suspiciousness ranking lists, and accordingly employ the Euclid or the Kendall tau distances, etc. However, such strategies may not properly reflect the essential characteristics of failures, thus resulting in unsatisfactory effectiveness. In this paper, we propose a new failure proximity, namely, the program variable-based failure proximity, and further present a novel failure indexing approach, ReClues. Specifically, ReClues utilizes the run-time values of program variables to represent failures, and designs a set of rules to measure the similarity between them. Experimental results demonstrate the competitiveness of ReClues: it can achieve 44.12% and 27.59% improvements in faults number estimation, as well as 47.56% and 26.27% improvements in clustering effectiveness, compared with the state-of-the-art technique in this field, in simulated and real-world environments, respectively.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639098"}, {"primary_key": "633259", "vector": [], "sparse_vector": [], "title": "Characterizing Software Maintenance Meetings: Information Shared, Discussion Outcomes, and Information Captured.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A type of meeting that has been understudied in the software engineering literature to date is what we term the software maintenance meeting: a regularly scheduled team meeting in which emergent issues are addressed that are usually out of scope of the daily stand-up but not necessarily challenging enough to warrant an entirely separate meeting. These meetings tend to discuss a wide variety of topics and are crucial in keeping software development projects going, but little is known about these meetings and how they proceed. In this paper, we report on a single exploratory case study in which we analyzed ten consecutive maintenance meetings from a major healthcare software provider. We analyzed what kind of information is brought into the discussions held in these meetings and how, what outcomes arose from the discussions, and what information was captured for downstream use. Our findings are varied, giving rise to both practical considerations for those conducting these kinds of meetings and new research directions toward further understanding and supporting them.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623330"}, {"primary_key": "633261", "vector": [], "sparse_vector": [], "title": "An Empirical Study on Compliance with Ranking Transparency in the Software Documentation of EU Online Platforms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Compliance with the European Union's Platform-to-Business (P2B) Regulation helps fostering a fair, ethical and secure online environment. However, it is challenging for online platforms, and assessing their compliance can be difficult for public authorities. This is partly due to the lack of automated tools for assessing the information (e.g., software documentation) platforms provide concerning ranking transparency. Our study tackles this issue in two ways. First, we empirically evaluate the compliance of six major platforms (Amazon, Bing, Booking, Google, Tripadvisor, and Yahoo), revealing substantial differences in their documentation. Second, we introduce and test automated compliance assessment tools based on ChatGPT and information retrieval technology. These tools are evaluated against human judgments, showing promising results as reliable proxies for compliance assessments. Our findings could help enhance regulatory compliance and align with the United Nations Sustainable Development Goal 10.3, which seeks to reduce inequality, including business disparities, on these platforms.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640112"}, {"primary_key": "633265", "vector": [], "sparse_vector": [], "title": "BOMs Away! Inside the Minds of Stakeholders: A Comprehensive Study of Bills of Materials for Software Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Software Bills of Materials (SBOMs) have emerged as tools to facilitate the management of software dependencies, vulnerabilities, licenses, and the supply chain. While significant effort has been devoted to increasing SBOM awareness and developing SBOM formats and tools, recent studies have shown that SBOMs are still an early technology not yet adequately adopted in practice. Expanding on previous research, this paper reports a comprehensive study that investigates the current challenges stakeholders encounter when creating and using SBOMs. The study surveyed 138 practitioners belonging to five stakeholder groups (practitioners familiar with SBOMs, members of critical open source projects, AI/ML, cyberphysical systems, and legal practitioners) using differentiated questionnaires, and interviewed 8 survey respondents to gather further insights about their experience. We identified 12 major challenges facing the creation and use of SBOMs, including those related to the SBOM content, deficiencies in SBOM tools, SBOM maintenance and verification, and domain-specific challenges. We propose and discuss 4 actionable solutions to the identified challenges and present the major avenues for future research and development.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623347"}, {"primary_key": "633266", "vector": [], "sparse_vector": [], "title": "Dataflow Analysis-Inspired Deep Learning for Efficient Vulnerability Detection.", "authors": ["<PERSON>", "Hongyang Gao", "<PERSON>"], "summary": "Deep learning-based vulnerability detection has shown great performance and, in some studies, outperformed static analysis tools. However, the highest-performing approaches use token-based transformer models, which are not the most efficient to capture code semantics required for vulnerability detection. Classical program analysis techniques such as dataflow analysis can detect many types of bugs based on their root causes. In this paper, we propose to combine such causal-based vulnerability detection algorithms with deep learning, aiming to achieve more efficient and effective vulnerability detection. Specifically, we designed DeepDFA, a dataflow analysis-inspired graph learning framework and an embedding technique that enables graph learning to simulate dataflow computation. We show that DeepDFA is both performant and efficient. DeepDFA outperformed all non-transformer baselines. It was trained in 9 minutes, 75x faster than the highest-performing baseline model. When using only 50+ vulnerable and several hundreds of total examples as training data, the model retained the same performance as 100% of the dataset. DeepDFA also generalized to real-world vulnerabilities in DbgBench; it detected 8.7 out of 17 vulnerabilities on average across folds and was able to distinguish between patched and buggy versions, while the highest-performing baseline models did not detect any vulnerabilities. By combining DeepDFA with a large language model, we surpassed the state-of-the-art vulnerability detection performance on the Big-Vul dataset with 96.46 F1 score, 97.82 precision, and 95.14 recall. Our replication package is located at https://doi.org/10.6084/m9.figshare.21225413.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623345"}, {"primary_key": "633267", "vector": [], "sparse_vector": [], "title": "Scalable Relational Analysis via Relational Bound Propagation.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Bounded formal analysis techniques (such as bounded model checking) are incredibly powerful tools for today's software engineers. However, such techniques often suffer from scalability challenges when applied to large-scale, real-world systems. It can be very difficult to ensure the bounds are set properly, which can have a profound impact on the performance and scalability of any bounded formal analysis. In this paper, we propose a novel approach---relational bound propagation---which leverages the semantics of the underlying relational logic formula encoded by the specification to automatically tighten the bounds for any relational specification. Our approach applies two sets of semantic rules to propagate the bounds on the relations via the abstract syntax tree of the formula, first upward to higher-level expressions on those relations then downward from those higher-level expressions to the relations. Thus, relational bound propagation can reduce the number of variables examined by the analysis and decrease the cost of performing the analysis. This paper presents formal definitions of these rules, all of which have been rigorously proven. We realize our approach in an accompanying tool, <PERSON><PERSON><PERSON>, and present experimental results using <PERSON><PERSON><PERSON> that test the efficacy of relational bound propagation to decrease the cost of relational bounded model checking. Our results demonstrate that relational bound propagation reduces the number of primary variables in 63.58% of tested specifications by an average of 30.68% (N=519) and decreases the analysis time for the subject specifications by an average of 49.30%. For large-scale, real-world specifications, <PERSON><PERSON><PERSON> was able to reduce total analysis time by an average of 68.14% (N=25) while introducing comparatively little overhead (6.14% baseline analysis time).", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639171"}, {"primary_key": "633268", "vector": [], "sparse_vector": [], "title": "Improving Testing Behavior by Gamifying IntelliJ.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Testing is an important aspect of software development, but unfortunately, it is often neglected. While test quality analyses such as code coverage or mutation analysis inform developers about the quality of their tests, such reports are viewed only sporadically during continuous integration or code review, if they are considered at all, and their impact on the developers' testing behavior therefore tends to be negligible. To actually influence developer behavior, it may rather be necessary to motivate developers directly within their programming environment, while they are coding. We introduce IntelliGame, a gamified plugin for the popular IntelliJ Java Integrated Development Environment, which rewards developers for positive testing behavior using a multi-level achievement system: A total of 27 different achievements, each with incremental levels, provide affirming feedback when developers exhibit commendable testing behavior, and provide an incentive to further continue and improve this behavior. A controlled experiment with 49 participants given a Java programming task reveals substantial differences in the testing behavior triggered by IntelliGame: Incentivized developers write more tests, achieve higher coverage and mutation scores, run their tests more often, and achieve functionality earlier.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623339"}, {"primary_key": "633271", "vector": [], "sparse_vector": [], "title": "Enhancing Exploratory Testing by Large Language Model and Knowledge Graph.", "authors": ["Yanqi Su", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Qinghua Lu", "<PERSON><PERSON>"], "summary": "Exploratory testing leverages the tester's knowledge and creativity to design test cases for effectively uncovering system-level bugs from the end user's perspective. Researchers have worked on test scenario generation to support exploratory testing based on a system knowledge graph, enriched with scenario and oracle knowledge from bug reports. Nevertheless, the adoption of this approach is hindered by difficulties in handling bug reports of inconsistent quality and varied expression styles, along with the infeasibility of the generated test scenarios. To overcome these limitations, we utilize the superior natural language understanding (NLU) capabilities of Large Language Models (LLMs) to construct a System KG of User Tasks and Failures (SysKG-UTF). Leveraging the system and bug knowledge from the KG, along with the logical reasoning capabilities of LLMs, we generate test scenarios with high feasibility and coherence. Particularly, we design chain-of-thought (CoT) reasoning to extract human-like knowledge and logical reasoning from LLMs, simulating a developer's process of validating test scenario feasibility. Our evaluation shows that our approach significantly enhances the KG construction, particularly for bug reports with low quality. Furthermore, our approach generates test scenarios with high feasibility and coherence. The user study further proves the effectiveness of our generated test scenarios in supporting exploratory testing. Specifically, 8 participants find 36 bugs from 8 seed bugs in two hours using our test scenarios, a significant improvement over the 21 bugs found by the state-of-the-art baseline.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639157"}, {"primary_key": "633272", "vector": [], "sparse_vector": [], "title": "Concrete Constraint Guided Symbolic Execution.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Shichao Lv", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Symbolic execution is a popular program analysis technique. It systematically explores all feasible paths of a program but its scalability is largely limited by the path explosion problem, which causes the number of paths proliferates at runtime. A key idea in existing methods to mitigate this problem is to guide the selection of states for path exploration, which primarily relies on the features to represent program states. In this paper, we propose concrete constraint guided symbolic execution, which aims to cover more concrete branches and ultimately improve the overall code coverage during symbolic execution. Our key insight is based on the fact that symbolic execution strives to cover all symbolic branches while concrete branches are neglected, and directing symbolic execution toward uncovered concrete branches has a great potential to improve the overall code coverage. The experimental results demonstrate that our approach can improve the ability of KLEE to both increase code coverage and find more security violations on 10 open-source C programs.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639078"}, {"primary_key": "633273", "vector": [], "sparse_vector": [], "title": "When Neural Code Completion Models Size up the Situation: Attaining Cheaper and Faster Completion through Dynamic Model Inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Fu Song", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Leveraging recent advancements in large language models, modern neural code completion models have demonstrated the capability to generate highly accurate code suggestions. However, their massive size poses challenges in terms of computational costs and environmental impact, hindering their widespread adoption in practical scenarios. Dynamic inference emerges as a promising solution, as it allocates minimal computation during inference while maintaining the model's performance. In this research, we explore dynamic inference within the context of code completion. Initially, we conducted an empirical investigation on GPT-2, focusing on the inference capabilities of intermediate layers for code completion. We found that 54.4% of tokens can be accurately generated using just the first layer, signifying significant computational savings potential. Moreover, despite using all layers, the model still fails to predict 14.5% of tokens correctly, and the subsequent completions continued from them are rarely considered helpful, with only a 4.2% Acceptance Rate. These findings motivate our exploration of dynamic inference in code completion and inspire us to enhance it with a decision-making mechanism that stops the generation of incorrect code. We thus propose a novel dynamic inference method specifically tailored for code completion models. This method aims not only to produce correct predictions with largely reduced computation but also to prevent incorrect predictions proactively. Our extensive evaluation shows that it can averagely skip 1.7 layers out of 16 layers in the models, leading to an 11.2% speedup with only a marginal 1.1% reduction in ROUGE-L.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639120"}, {"primary_key": "633275", "vector": [], "sparse_vector": [], "title": "Where is it? Tracing the Vulnerability-relevant Files from Vulnerability Reports.", "authors": ["Ji<PERSON>u Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qinghua Lu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the widely usage of open-source software, supply-chain-based vulnerability attacks, including SolarWind and Log4Shell, have posed significant risks to software security. Currently, people rely on vulnerability advisory databases or commercial software bill of materials (SBOM) to defend against potential risks. Unfortunately, these datasets do not provide finer-grained file-level vulnerability information, compromising their effectiveness. Previous works have not adequately addressed this issue, and mainstream vulnerability detection methods have their drawbacks that hinder resolving this gap. Driven by the real needs, we propose a framework that can trace the vulnerability-relevant file for each disclosed vulnerability. Our approach uses NVD descriptions with metadata as the inputs, and employs a series of strategies with a LLM model, search engine, heuristic-based text matching method and a deep learning classifier to recommend the most likely vulnerability-relevant file, effectively enhancing the completeness of existing NVD data. Our experiments confirm that the efficiency of the proposed framework, with CodeBERT achieving 0.92 AUC and 0.85 MAP, and our user study proves our approach can help with vulnerability-relevant file detection effectively. To the best of our knowledge, our work is the first one focusing on tracing vulnerability-relevant files, laying the groundwork of building finer-grained vulnerability-aware software bill of materials.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639202"}, {"primary_key": "633277", "vector": [], "sparse_vector": [], "title": "ACAV: A Framework for Automatic Causality Analysis in Autonomous Vehicle Accident Recordings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The rapid progress of autonomous vehicles (AVs) has brought the prospect of a driverless future closer than ever. Recent fatalities, however, have emphasized the importance of safety validation through large-scale testing. Multiple approaches achieve this fully automatically using high-fidelity simulators, i.e., by generating diverse driving scenarios and evaluating autonomous driving systems (ADSs) against different test oracles. While effective at finding violations, these approaches do not identify the decisions and actions that caused them---information that is critical for improving the safety of ADSs. To address this challenge, we propose ACAV, an automated framework designed to conduct causality analyses for AV accident recordings in two stages. First, we apply feature extraction schemas based on the messages exchanged between ADS modules, and use a weighted voting method to discard frames of the recording unrelated to the accident. Second, we use safety specifications to identify safety-critical frames and deduce causal events by applying CAT---our causal analysis tool---to a station-time graph. We evaluated ACAV on the Apollo ADS, finding that it can identify five distinct types of causal events in 93.64% of 110 accident recordings generated by an AV testing engine. We further evaluated ACAV on 1206 accident recordings collected from versions of Apollo injected with specific faults, finding that it can correctly identify causal events in 96.44% of the accidents triggered by prediction errors, and 85.73% of the accidents triggered by planning errors.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639175"}, {"primary_key": "633280", "vector": [], "sparse_vector": [], "title": "ChatGPT Incorrectness Detection in Software Reviews.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We conducted a survey of 135 software engineering (SE) practitioners to understand how they use Generative AI-based chatbots like ChatGPT for SE tasks. We find that they want to use ChatGPT for SE tasks like software library selection but often worry about the truthfulness of ChatGPT responses. We developed a suite of techniques and a tool called CID (ChatGPT Incorrectness Detector) to automatically test and detect the incorrectness in ChatGPT responses. CID is based on the iterative prompting to ChatGPT by asking it contextually similar but textually divergent questions (using an approach that utilizes metamorphic relationships in texts). The underlying principle in CID is that for a given question, a response that is different from other responses (across multiple incarnations of the question) is likely an incorrect response. In a benchmark study of library selection, we show that CID can detect incorrect responses from ChatGPT with an F1-score of 0.74 -- 0.75.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639194"}, {"primary_key": "633282", "vector": [], "sparse_vector": [], "title": "Learning and Repair of Deep Reinforcement Learning Policies from Fuzz-Testing Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Reinforcement learning from demonstrations (RLfD) is a promising approach to improve the exploration efficiency of reinforcement learning (RL) by learning from expert demonstrations in addition to interactions with the environment. In this paper, we propose a framework that combines techniques from search-based testing with RLfD with the goal to raise the level of dependability of RL policies and to reduce human engineering effort. Within our framework, we provide methods for efficiently training, evaluating, and repairing RL policies. Instead of relying on the costly collection of demonstrations from (human) experts, we automatically compute a diverse set of demonstrations via search-based fuzzing methods and use the fuzz demonstrations for RLfD. To evaluate the safety and robustness of the trained RL agent, we search for safety-critical scenarios in the black-box environment. Finally, when unsafe behavior is detected, we compute demonstrations through fuzz testing that represent safe behavior and use them to repair the policy. Our experiments show that our framework is able to efficiently learn high-performing and safe policies without requiring any expert knowledge.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623311"}, {"primary_key": "633283", "vector": [], "sparse_vector": [], "title": "Object Graph Programming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce Object Graph Programming (OGO), which enables reading and modifying an object graph (i.e., the entire state of the object heap) via declarative queries. OGO models the objects and their relations in the heap as an object graph thereby treating the heap as a graph database: each node in the graph is an object (e.g., an instance of a class or an instance of a metadata class) and each edge is a relation between objects (e.g., a field of one object references another object). We leverage Cypher, the most popular query language for graph databases, as OGO's query language. Unlike LINQ, which uses collections (e.g., List) as a source of data, OGO views the entire object graph as a single \"collection\". OGO is ideal for querying collections (just like LINQ), introspecting the runtime system state (e.g., finding all instances of a given class or accessing fields via reflection), and writing assertions that have access to the entire program state. We prototyped OGO for Java in two ways: (a) by translating an object graph into a Neo4j database on which we run Cypher queries, and (b) by implementing our own in-memory graph query engine that directly queries the object heap. We used OGO to rewrite hundreds of statements in large open-source projects into OGO queries. We report our experience and performance of our prototypes.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623319"}, {"primary_key": "633284", "vector": [], "sparse_vector": [], "title": "DocFlow: Extracting Taint Specifications from Software Documentation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Security practitioners routinely use static analysis to detect security problems and privacy violations in Android apps. The soundness of these analyses depends on how the platform is modelled and the list of sensitive methods. Collecting these methods often becomes impractical given the number of methods available, the pace at which the Android platform is updated, and the proprietary libraries Google releases on each new version. Despite the constant evolution of the Android platform, app developers cope with all these new features thanks to the documentation that comes with each new Android release. In this work, we take advantage of the rich documentation provided by platforms like Android and propose DocFlow, a framework to generate taint specifications for a platform, directly from its documentation. DocFlow models the semantics of API methods using their documentation to detect sensitive methods (sources and sinks) and assigns them semantic labels. Our approach does not require access to source code, enabling the analysis of proprietary libraries for which the code is unavailable. We evaluate DocFlow using Android platform packages and closed-source Google Play Services libraries. Our results show that our framework detects sensitive methods with high precision, adapts to new API versions, and can be easily extended to detect other method types. Our approach provides evidence that Android documentation encodes rich semantic information to categorise sensitive methods, removing the need to analyse source code or perform feature extraction.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623312"}, {"primary_key": "633285", "vector": [], "sparse_vector": [], "title": "With Great Humor Comes Great Developer Engagement.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The worldwide collaborative effort for the creation of software is technically and socially demanding. The more engaged developers are, the more value they impart to the software they create. Engaged developers, such as <PERSON> programming Apollo 11, can succeed in tackling the most difficult engineering tasks. In this paper, we dive deep into an original vector of engagement - humor - and study how it fuels developer engagement. First, we collect qualitative and quantitative data about the humorous elements present within three significant, real-world software projects: faker, which helps developers introduce humor within their tests; lolcommits, which captures a photograph after each contribution made by a developer; and volkswagen, an exercise in satire, which accidentally led to the invention of an impactful software tool. Second, through a developer survey, we receive unique insights from 125 developers, who share their real-life experiences with humor in software.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640099"}, {"primary_key": "633290", "vector": [], "sparse_vector": [], "title": "Unraveling the Drivers of Sense of Belonging in Software Delivery Teams: Insights from a Large-Scale Survey.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Feeling part of a group is a basic human need that significantly influences an individual's behavior, long-term engagement, and job satisfaction. A strong sense of belonging holds particular importance within software delivery teams, which grapple with challenges related to well-being and employee retention. However, the specific factors closely associated with the sense of belonging in the context of software delivery teams remain largely unknown. Without a clear understanding of these factors, organizations' efforts to promote a sense of belonging and diversity and inclusion more broadly may prove ineffective. Based on existing literature, we identified key factors potentially relevant to the sense of belonging in software delivery teams, such as work appreciation and psychological safety, and investigated the interrelation among these factors. We surveyed members of software delivery teams (n=10,781) of a major software delivery organization and used Partial Least Squares-Structural Equation Modeling (PLS-SEM) to evaluate a theoretical model to understand the factors that might contribute to a sense of belonging to the team. We also conducted a multi-group analysis to evaluate how the associations change based on individuals' leadership involvement and an importance-performance map analysis to find the most critical indicators of belongingness. Our findings indicate a positive association between psychological safety and work appreciation and belonging to the team. Women feel less belonging than men, especially those not in leadership positions. Authoritativeness is negatively associated with belonging, and tenure is positively associated with belonging regardless of the role. Through this research, we seek to provide insights into the sense of belonging to the team and foster a more inclusive and cohesive work environment.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639119"}, {"primary_key": "633293", "vector": [], "sparse_vector": [], "title": "MotorEase: Automated Detection of Motor Impairment Accessibility Issues in Mobile App UIs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent research has begun to examine the potential of automatically finding and fixing accessibility issues that manifest in software. However, while recent work makes important progress, it has generally been skewed toward identifying issues that affect users with certain disabilities, such as those with visual or hearing impairments. However, there are other groups of users with different types of disabilities that also need software tooling support to improve their experience. As such, this paper aims to automatically identify accessibility issues that affect users with motor-impairments. To move toward this goal, this paper introduces a novel approach, called MotorEase, capable of identifying accessibility issues in mobile app UIs that impact motor-impaired users. Motor-impaired users often have limited ability to interact with touch-based devices, and instead may make use of a switch or other assistive mechanism -- hence UIs must be designed to support both limited touch gestures and the use of assistive devices. MotorEase adapts computer vision and text processing techniques to enable a semantic understanding of app UI screens, enabling the detection of violations related to four popular, previously unexplored UI design guidelines that support motor-impaired users, including: (i) visual touch target size, (ii) expanding sections, (iii) persisting elements, and (iv) adjacent icon visual distance. We evaluate MotorEase on a newly derived benchmark, called MotorCheck, that contains 555 manually annotated examples of violations to the above accessibility guidelines, across 1599 screens collected from 70 applications via a mobile app testing tool. Our experiments illustrate that MotorEase is able to identify violations with an average accuracy of ~90%, and a false positive rate of less than 9%, outperforming baseline techniques.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639167"}, {"primary_key": "633302", "vector": [], "sparse_vector": [], "title": "Exploring Assessment Criteria for Sustainable Software Engineering Processes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "It is our duty as software engineers to understand our contribution towards sustainability and ultimately assess and improve the sustainability of the software engineering (SE) processes we apply. However, commonly established criteria for such an assessment are currently lacking. In this experience report, we share insights from our investigation into the sustainability of software engineering processes, focusing on a collaborative project with an industry partner. Our research delves into lessons learned while exploring this critical issue. Our contribution lies in the introduction of an initial framework, which includes assessment criteria as the core element, and on the results of using this framework to assess the software engineering process of our industry partner. By sharing our experiences and findings, we aim to contribute to the understanding of sustainable software engineering practices and stimulate dialogue on how software engineering can address societal and environmental challenges. Our work underscores the significance of adopting sustainable practices and encourages the software engineering community---in both academia and industry---to embrace a proactive role in advancing sustainability for society.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640109"}, {"primary_key": "633304", "vector": [], "sparse_vector": [], "title": "It&apos;s Not a Feature, It&apos;s a Bug: Fault-Tolerant Model Mining from Noisy Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The mining of models from data finds widespread use in industry. There exists a variety of model inference methods for perfectly deterministic behaviour, however, in practice, the provided data often contains noise due to faults such as message loss or environmental factors that many of the inference algorithms have problems dealing with. We present a novel model mining approach using Partial Max-SAT solving to infer the best possible automaton from a set of noisy execution traces. This approach enables us to ignore the minimal number of presumably faulty observations to allow the construction of a deterministic automaton. No pre-processing of the data is required. The method's performance as well as a number of considerations for practical use are evaluated, including three industrial use cases, for which we inferred the correct models.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623346"}, {"primary_key": "633307", "vector": [], "sparse_vector": [], "title": "Adaptive User Interfaces for Software Supporting Chronic Disease.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "mHealth interventions hold promise for supporting the self-management of chronic diseases, yet their limited utilisation remains a problem. Given the significant variability among individuals with chronic diseases, tailored approaches are imperative. Adaptive User Interfaces (AUIs) may help to address the diverse and evolving needs of this demographic. To investigate this approach, we developed an AUI prototype informed by existing literature and used it as the basis for a focus group and interview study involving 22 participants. Concurrently, a quantitative survey was carried out to extract preferences for AUIs in chronic disease related applications with 90 participants. Our findings reveal that user engagement with AUIs is influenced by individual capabilities and disease severity. Additionally, we explore user preferences for AUIs, expanding the adaptation literature by uncovering usage challenges, proposing practical strategies for enhanced AUI design, and acknowledging potential trade-offs between usability and adaptation. Lastly, we present design considerations for AUIs in chronic disease applications, aiming to prevent user overload and maintain critical software functionality and usability aspects.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640104"}, {"primary_key": "633308", "vector": [], "sparse_vector": [], "title": "Knowledge Graph Driven Inference Testing for Question Answering Software.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the wake of developments in the field of Natural Language Processing, Question Answering (QA) software has penetrated our daily lives. Due to the data-driven programming paradigm, QA software inevitably contains bugs, i.e., misbehaving in real-world applications. Current testing techniques for testing QA software include two folds, reference-based testing and metamorphic testing.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639109"}, {"primary_key": "633309", "vector": [], "sparse_vector": [], "title": "An Empirical Study on Noisy Label Learning for Program Understanding.", "authors": ["<PERSON><PERSON>", "Yanzhou Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recently, deep learning models have been widely applied in program understanding tasks, and these models achieve state-of-the-art results on many benchmark datasets. A major challenge of deep learning for program understanding is that the effectiveness of these approaches depends on the quality of their datasets, and these datasets often contain noisy data samples. A typical kind of noise in program understanding datasets is label noise, which means that the target outputs for some inputs are incorrect.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639217"}, {"primary_key": "633311", "vector": [], "sparse_vector": [], "title": "An Exploratory Investigation of Log Anomalies in Unmanned Aerial Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "Shuqing Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Unmanned aerial vehicles (UAVs) are becoming increasingly ubiquitous in our daily lives. However, like many other complex systems, UAVs are susceptible to software bugs that can lead to abnormal system behaviors and undesirable consequences. It is crucial to study such software bug-induced UAV anomalies, which are often manifested in flight logs, to help assure the quality and safety of UAV systems. However, there has been limited research on investigating the code-level patterns of software bug-induced UAV anomalies. This impedes the development of effective tools for diagnosing and localizing bugs within UAV system code.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639186"}, {"primary_key": "633313", "vector": [], "sparse_vector": [], "title": "Predicting Performance and Accuracy of Mixed-Precision Programs for Precision Tuning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A mixed-precision program is a floating-point program that utilizes different precisions for different operations, providing the opportunity of balancing the trade-off between accuracy and performance. Precision tuning aims to find a mixed-precision version of a program that improves its performance while maintaining a given accuracy. Unfortunately, existing precision tuning approaches are either limited to small-scale programs, or suffer from efficiency issues. In this paper, we propose FPLearner, a novel approach that addresses these limitations. Our insight is to leverage a Machine Learning based technique, Graph Neural Networks, to learn the representation of mixed-precision programs to predict their performance and accuracy. Such prediction models can then be used to accelerate the process of dynamic precision tuning by reducing the number of program runs. We create a dataset of mixed-precision programs from five diverse HPC applications for training our models, which achieve 96.34% F1 score in performance prediction and 97.03% F1 score in accuracy prediction. FPLearner improves the time efficiency of two dynamic precision tuners, Precimonious and HiFPTuner, by an average of 25.54% and up to 61.07% while achieving precision tuning results of comparable or better quality.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623338"}, {"primary_key": "633314", "vector": [], "sparse_vector": [], "title": "Combining Structured Static Code Information and Dynamic Symbolic Traces for Software Vulnerability Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chunwei Xia", "<PERSON>"], "summary": "Deep learning (DL) has emerged as a viable means for identifying software bugs and vulnerabilities. The success of DL relies on having a suitable representation of the problem domain. However, existing DL-based solutions for learning program representations have limitations - they either cannot capture the deep, precise program semantics or suffer from poor scalability. We present Concoction, the first DL system to learn program presentations by combining static source code information and dynamic program execution traces. Concoction employs unsupervised active learning techniques to determine a subset of important paths to collect dynamic symbolic execution traces. By implementing a focused symbolic execution solution, Concoction brings the benefits of static and dynamic code features while reducing the expensive symbolic execution overhead. We integrate Concoction with fuzzing techniques to detect function-level code vulnerabilities in C programs from 20 open-source projects. In 200 hours of automated concurrent test runs, Concoction has successfully uncovered vulnerabilities in all tested projects, identifying 54 unique vulnerabilities and yielding 37 new, unique CVE IDs. Concoction also significantly outperforms 16 prior methods by providing higher accuracy and lower false positive rates.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639212"}, {"primary_key": "633315", "vector": [], "sparse_vector": [], "title": "MAFT: Efficient Model-Agnostic Fairness Testing for Deep Neural Networks via Zero-Order Gradient Search.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Deep neural networks (DNNs) have shown powerful performance in various applications and are increasingly being used in decisionmaking systems. However, concerns about fairness in DNNs always persist. Some efficient white-box fairness testing methods about individual fairness have been proposed. Nevertheless, the development of black-box methods has stagnated, and the performance of existing methods is far behind that of white-box methods. In this paper, we propose a novel black-box individual fairness testing method called Model-Agnostic Fairness Testing (MAFT). By leveraging MAFT, practitioners can effectively identify and address discrimination in DL models, regardless of the specific algorithm or architecture employed. Our approach adopts lightweight procedures such as gradient estimation and attribute perturbation rather than non-trivial procedures like symbol execution, rendering it significantly more scalable and applicable than existing methods. We demonstrate that MAFT achieves the same effectiveness as state-of-the-art white-box methods whilst improving the applicability to large-scale networks. Compared to existing black-box approaches, our approach demonstrates distinguished performance in discovering fairness violations w.r.t effectiveness (~ 14.69×) and efficiency (~ 32.58×).", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639181"}, {"primary_key": "633316", "vector": [], "sparse_vector": [], "title": "Demystifying and Detecting Misuses of Deep Learning APIs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Deep Learning (DL) libraries have significantly impacted various domains in computer science over the last decade. However, developers often face challenges when using the DL APIs, as the development paradigm of DL applications differs greatly from traditional software development. Existing studies on API misuse mainly focus on traditional software, leaving a gap in understanding API misuse within DL APIs. To address this gap, we present the first comprehensive study of DL API misuse in TensorFlow and PyTorch. Specifically, we first collected a dataset of 4,224 commits from the top 200 most-starred projects using these two libraries and manually identified 891 API misuses. We then investigated the characteristics of these misuses from three perspectives, i.e., types, root causes, and symptoms. We have also conducted an evaluation to assess the effectiveness of the current state-of-the-art API misuse detector on our 891 confirmed API misuses. Our results confirmed that the state-of-the-art API misuse detector is ineffective in detecting DL API misuses. To address the limitations of existing API misuse detection for DL APIs, we propose LLMAPIDet, which leverages Large Language Models (LLMs) for DL API misuse detection and repair. We build LLMAPIDet by prompt-tuning a chain of ChatGPT prompts on 600 out of 891 confirmed API misuses and reserve the rest 291 API misuses as the testing dataset. Our evaluation shows that LLMAPIDet can detect 48 out of the 291 DL API misuses while none of them can be detected by the existing API misuse detector. We further evaluate LLMAPIDet on the latest versions of 10 GitHub projects. The evaluation shows that LLMAPIDet can identify 119 previously unknown API misuses and successfully fix 46 of them.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639177"}, {"primary_key": "633320", "vector": [], "sparse_vector": [], "title": "BinAug: Enhancing Binary Similarity Analysis with Low-Cost Input Repairing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Binary code similarity analysis (BCSA) is a fundamental building block for various software security, reverse engineering, and re-engineering applications. Existing research has applied deep neural networks (DNNs) to measure the similarity between binary code, following the major breakthrough of DNNs in processing media data like images. Despite the encouraging results of DNN-based BCSA, it is however not widely deployed in the industry due to the instability and the black-box nature of DNNs.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623328"}, {"primary_key": "633321", "vector": [], "sparse_vector": [], "title": "S3C: Spatial Semantic Scene Coverage for Autonomous Vehicles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Autonomous vehicles (AVs) must be able to operate in a wide range of scenarios including those in the long tail distribution that include rare but safety-critical events. The collection of sensor input and expected output datasets from such scenarios is crucial for the development and testing of such systems. Yet, approaches to quantify the extent to which a dataset covers test specifications that capture critical scenarios remain limited in their ability to discriminate between inputs that lead to distinct behaviors, and to render interpretations that are relevant to AV domain experts. To address this challenge, we introduce S3C, a framework that abstracts sensor inputs to coverage domains that account for the spatial semantics of a scene. The approach leverages scene graphs to produce a sensor-independent abstraction of the AV environment that is interpretable and discriminating. We provide an implementation of the approach and a study for camera-based autonomous vehicles operating in simulation. The findings show that S3C outperforms existing techniques in discriminating among classes of inputs that cause failures, and offers spatial interpretations that can explain to what extent a dataset covers a test specification. Further exploration of S3C with open datasets complements the study findings, revealing the potential and shortcomings of deploying the approach in the wild.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639178"}, {"primary_key": "633322", "vector": [], "sparse_vector": [], "title": "Are We There Yet? Unraveling the State-of-the-Art Smart Contract Fuzzers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given the growing importance of smart contracts in various applications, ensuring their security and reliability is critical. Fuzzing, an effective vulnerability detection technique, has recently been widely applied to smart contracts. Despite numerous studies, a systematic investigation of smart contract fuzzing techniques remains lacking. In this paper, we fill this gap by: 1) providing a comprehensive review of current research in contract fuzzing, and 2) conducting an in-depth empirical study to evaluate state-of-the-art contract fuzzers' usability. To guarantee a fair evaluation, we employ a carefully-labeled benchmark and introduce a set of pragmatic performance metrics, evaluating fuzzers from five complementary perspectives. Based on our findings, we provide direction for the future research and development of contract fuzzers.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639152"}, {"primary_key": "633323", "vector": [], "sparse_vector": [], "title": "LibAlchemy: A Two-Layer Persistent Summary Design for Taming Third-Party Libraries in Static Bug-Finding Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Wensheng Tang", "Qingkai Shi", "<PERSON>", "<PERSON>"], "summary": "Despite the benefits of using third-party libraries (TPLs), the misuse of TPL functions raises quality and security concerns. Using traditional static analysis to detect bugs caused by TPL function is non-trivial. One promising solution would be to automatically generate and persist the summaries of TPL functions offline and then reuse these summaries in compositional static analysis online. However, when dealing with millions of lines of TPL code, the summaries designed by existing studies suffer from an unresolved paradox. That is, a highly precise form of summary leads to an unaffordable space and time overhead, while an imprecise one seriously hurts its precision or recall.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639132"}, {"primary_key": "633324", "vector": [], "sparse_vector": [], "title": "Identifying Affected Libraries and Their Ecosystems for Open Source Software Vulnerabilities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Huang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software composition analysis (SCA) tools have been widely adopted to identify vulnerable libraries used in software applications. Such SCA tools depend on a vulnerability database to know affected libraries of each vulnerability. However, it is labor-intensive and error prone for a security team to manually maintain the vulnerability database. While several approaches adopt extreme multi-label learning to predict affected libraries for vulnerabilities, they are practically ineffective due to the limited library labels and the unawareness of ecosystems.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639582"}, {"primary_key": "633326", "vector": [], "sparse_vector": [], "title": "Beyond Self-Promotion: How Software Engineering Research Is Discussed on LinkedIn.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "LinkedIn is the largest professional network in the world. As such, it can serve to build bridges between practitioners, whose daily work is software engineering (SE), and researchers, who work to advance the field of software engineering. We know that such a metaphorical bridge exists: SE research findings are sometimes shared on LinkedIn and commented on by software practitioners. Yet, we do not know what state the bridge is in. Therefore, we quantitatively and qualitatively investigate how SE practitioners and researchers approach each other via public LinkedIn discussions and what both sides can contribute to effective science communication. We found that a considerable proportion of LinkedIn posts on SE research are written by people who are not the paper authors (39%). Further, 71% of all comments in our dataset are from people in the industry, but only every second post receives at least one comment at all. Based on our findings, we formulate concrete advice for researchers and practitioners to make sharing new research findings on LinkedIn more fruitful.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640113"}, {"primary_key": "633327", "vector": [], "sparse_vector": [], "title": "Fuzz4All: Universal Fuzzing with Large Language Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Fuzzing has achieved tremendous success in discovering bugs and vulnerabilities in various software systems. Systems under test (SUTs) that take in programming or formal language as inputs, e.g., compilers, runtime engines, constraint solvers, and software libraries with accessible APIs, are especially important as they are fundamental building blocks of software development. However, existing fuzzers for such systems often target a specific language, and thus cannot be easily applied to other languages or even other versions of the same language. Moreover, the inputs generated by existing fuzzers are often limited to specific features of the input language, and thus can hardly reveal bugs related to other or new features. This paper presents Fuzz4All, the first fuzzer that is universal in the sense that it can target many different input languages and many different features of these languages. The key idea behind Fuzz4All is to leverage large language models (LLMs) as an input generation and mutation engine, which enables the approach to produce diverse and realistic inputs for any practically relevant language. To realize this potential, we present a novel autoprompting technique, which creates LLM prompts that are well-suited for fuzzing, and a novel LLM-powered fuzzing loop, which iteratively updates the prompt to create new fuzzing inputs. We evaluate Fuzz4All on nine systems under test that take in six different languages (C, C++, Go, SMT2, Java, and Python) as inputs. The evaluation shows, across all six languages, that universal fuzzing achieves higher coverage than existing, language-specific fuzzers. Furthermore, Fuzz4All has identified 98 bugs in widely used systems, such as GCC, Clang, Z3, CVC5, OpenJDK, and the Qiskit quantum computing platform, with 64 bugs already confirmed by developers as previously unknown.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639121"}, {"primary_key": "633328", "vector": [], "sparse_vector": [], "title": "Are Your Requests Your True Needs? Checking Excessive Data Collection in VPA App.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Guangdong Bai"], "summary": "Virtual personal assistants (VPA) services encompass a large number of third-party applications (or apps) to enrich their functionalities. These apps have been well examined to scrutinize their data collection behaviors against their declared privacy policies. Nonetheless, it is often overlooked that most users tend to ignore privacy policies at the installation time. Dishonest developers thus can exploit this situation by embedding excessive declarations to cover their data collection behaviors during compliance auditing.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639107"}, {"primary_key": "633329", "vector": [], "sparse_vector": [], "title": "UniLog: Automatic Logging via LLM and In-Context Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Logging, which aims to determine the position of logging statements, the verbosity levels, and the log messages, is a crucial process for software reliability enhancement. In recent years, numerous automatic logging tools have been designed to assist developers in one of the logging tasks (e.g., providing suggestions on whether to log in try-catch blocks). These tools are useful in certain situations yet cannot provide a comprehensive logging solution in general. Moreover, although recent research has started to explore end-to-end logging, it is still largely constrained by the high cost of fine-tuning, hindering its practical usefulness in software development. To address these problems, this paper proposes UniLog, an automatic logging framework based on the in-context learning (ICL) paradigm of large language models (LLMs). Specifically, UniLog can generate an appropriate logging statement with only a prompt containing five demonstration examples without any model tuning. In addition, UniLog can further enhance its logging ability after warmup with only a few hundred random samples. We evaluated UniLog on a large dataset containing 12,012 code snippets extracted from 1,465 GitHub repositories. The results show that UniLog achieved the state-of-the-art performance in automatic logging: (1) 76.9% accuracy in selecting logging positions, (2) 72.3% accuracy in predicting verbosity levels, and (3) 27.1 BLEU-4 score in generating log messages. Meanwhile, UniLog requires less than 4% of the parameter tuning time needed by fine-tuning the same LLM.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623326"}, {"primary_key": "633330", "vector": [], "sparse_vector": [], "title": "DSFM: Enhancing Functional Code Clone Detection with Deep Subtree Interactions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Hai Wan", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Functional code clone detection is important for software maintenance. In recent years, deep learning techniques are introduced to improve the performance of functional code clone detectors. By representing each code snippet as a vector containing its program semantics, syntactically dissimilar functional clones are detected. However, existing deep learning-based approaches attach too much importance to code feature learning, hoping to project all recognizable knowledge of a code snippet into a single vector. We argue that these deep learning-based approaches can be enhanced by considering the characteristics of syntactic code clone detection, where we need to compare the contents of the source code (e.g., intersection of tokens, similar flow graphs, and similar subtrees) to obtain code clones. In this paper, we propose a novel deep learning-based approach named DSFM, which incorporates comparisons between code snippets for detecting functional code clones. Specifically, we improve the typical deep clone detectors with deep subtree interactions that compare every two subtrees extracted abstract syntax trees (ASTs) of two code snippets, thereby introducing more fine-grained semantic similarity. By conducting extensive experiments on three widely-used datasets, GCJ, OJClone, and BigCloneBench, we demonstrate the great potential of deep subtree interactions in code clone detection task. The proposed DSFM outperforms the state-of-the-art approaches, including two traditional approaches, two unsupervised and four supervised deep learning-based baselines.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639215"}, {"primary_key": "633331", "vector": [], "sparse_vector": [], "title": "DivLog: Log Parsing with Prompt Enhanced In-Context Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Log parsing, which involves log template extraction from semi-structured logs to produce structured logs, is the first and the most critical step in automated log analysis. However, current log parsers suffer from limited effectiveness for two reasons. First, traditional data-driven log parsers solely rely on heuristics or handcrafted features designed by domain experts, which may not consistently perform well on logs from diverse systems. Second, existing supervised log parsers require model tuning, which is often limited to fixed training samples and causes sub-optimal performance across the entire log source. To address this limitation, we propose DivLog, an effective log parsing framework based on the in-context learning (ICL) ability of large language models (LLMs). Specifically, before log parsing, DivLog samples a small amount of offline logs as candidates by maximizing their diversity. Then, during log parsing, DivLog selects five appropriate labeled candidates as examples for each target log and constructs them into a prompt. By mining the semantics of examples in the prompt, DivLog generates a target log template in a training-free manner. In addition, we design a straightforward yet effective prompt format to extract the output and enhance the quality of the generated log templates. We conducted experiments on 16 widely-used public datasets. The results show that DivLog achieves (1) 98.1% Parsing Accuracy, (2) 92.1% Precision Template Accuracy, and (3) 92.9% Recall Template Accuracy on average, exhibiting state-of-the-art performance.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639155"}, {"primary_key": "633335", "vector": [], "sparse_vector": [], "title": "Semantic GUI Scene Learning and Video Alignment for Detecting Duplicate Video-based Bug Reports.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Video-based bug reports are increasingly being used to document bugs for programs centered around a graphical user interface (GUI). However, developing automated techniques to manage video-based reports is challenging as it requires identifying and understanding often nuanced visual patterns that capture key information about a reported bug. In this paper, we aim to overcome these challenges by advancing the bug report management task of duplicate detection for video-based reports. To this end, we introduce a new approach, called Janus, that adapts the scene-learning capabilities of vision transformers to capture subtle visual and textual patterns that manifest on app UI screens --- which is key to differentiating between similar screens for accurate duplicate report detection. <PERSON><PERSON> also makes use of a video alignment technique capable of adaptive weighting of video frames to account for typical bug manifestation patterns. In a comprehensive evaluation on a benchmark containing 7,290 duplicate detection tasks derived from 270 video-based bug reports from 90 Android app bugs, the best configuration of our approach achieves an overall mRR/mAP of 89.8%/84.7%, and for the large majority of duplicate detection tasks, outperforms prior work by ≈9% to a statistically significant degree. Finally, we qualitatively illustrate how the scene-learning capabilities provided by <PERSON><PERSON> benefits its performance.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639163"}, {"primary_key": "633336", "vector": [], "sparse_vector": [], "title": "Streamlining Java Programming: Uncovering Well-Formed Idioms with IdioMine.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Code idioms are commonly used patterns, techniques, or practices that aid in solving particular problems or specific tasks across multiple software projects. They can improve code quality, performance, and maintainability, and also promote program standardization and reuse across projects. However, identifying code idioms is significantly challenging, as existing studies have still suffered from three main limitations. First, it is difficult to recognize idioms that span non-contiguous code lines. Second, identifying idioms with intricate data flow and code structures can be challenging. Moreover, they only extract dataset-specific idioms, so common idioms or well-established code/design patterns that are rarely found in datasets cannot be identified.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639135"}, {"primary_key": "633338", "vector": [], "sparse_vector": [], "title": "Uncover the Premeditated Attacks: Detecting Exploitable Reentrancy Vulnerabilities by Identifying Attacker Contracts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Reentrancy, a notorious vulnerability in smart contracts, has led to millions of dollars in financial loss. However, current smart contract vulnerability detection tools suffer from a high false positive rate in identifying contracts with reentrancy vulnerabilities. Moreover, only a small portion of the detected reentrant contracts can actually be exploited by hackers, making these tools less effective in securing the Ethereum ecosystem in practice.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639153"}, {"primary_key": "633339", "vector": [], "sparse_vector": [], "title": "Large Language Models for Test-Free Fault Localization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Fault Localization (FL) aims to automatically localize buggy lines of code, a key first step in many manual and automatic debugging tasks. Previous FL techniques assume the provision of input tests, and often require extensive program analysis, program instrumentation, or data preprocessing. Prior work on deep learning for APR struggles to learn from small datasets and produces limited results on real-world programs. Inspired by the ability of large language models (LLMs) of code to adapt to new tasks based on very few examples, we investigate the applicability of LLMs to line level fault localization. Specifically, we propose to overcome the left-to-right nature of LLMs by fine-tuning a small set of bidirectional adapter layers on top of the representations learned by LLMs to produce LLMAO, the first language model based fault localization approach that locates buggy lines of code without any test coverage information. We fine-tune LLMs with 350 million, 6 billion, and 16 billion parameters on small, manually curated corpora of buggy programs such as the Defects4J corpus. We observe that our technique achieves substantially more confidence in fault localization when built on the larger models, with bug localization performance scaling consistently with the LLM size. Our empirical evaluation shows that LLMAO improves the Top-1 results over the state-of-the-art machine learning fault localization (MLFL) baselines by 2.3%--54.4%, and Top-5 results by 14.4%-35.6%. LLMAO is also the first FL technique trained using a language model architecture that can detect security vulnerabilities down to the code line level.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623342"}, {"primary_key": "633340", "vector": [], "sparse_vector": [], "title": "Rust-lancet: Automated Ownership-Rule-Violation Fixing with Behavior Preservation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As a relatively new programming language, Rust is designed to provide both memory safety and runtime performance. To achieve this goal, Rust conducts rigorous static checks against its safety rules during compilation, effectively eliminating memory safety issues that plague C/C++ programs. Although useful, the safety rules pose programming challenges to Rust programmers, since programmers can easily violate safety rules when coding in Rust, leading their code to be rejected by the Rust compiler, a fact underscored by a recent user study. There exists a desire to automate the process of fixing safety-rule violations to enhance Rust's programmability.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639103"}, {"primary_key": "633341", "vector": [], "sparse_vector": [], "title": "ITER: Iterative Neural Repair for Multi-Location Patches.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Automated program repair (APR) has achieved promising results, especially using neural networks. Yet, the overwhelming majority of patches produced by APR tools are confined to one single location. When looking at the patches produced with neural repair, most of them fail to compile, while a few uncompilable ones go in the right direction. In both cases, the fundamental problem is to ignore the potential of partial patches. In this paper, we propose an iterative program repair paradigm called ITER founded on the concept of improving partial patches until they become plausible and correct. First, ITER iteratively improves partial single-location patches by fixing compilation errors and further refining the previously generated code. Second, ITER iteratively improves partial patches to construct multi-location patches, with fault localization re-execution. ITER is implemented for Java based on battle-proven deep neural networks and code representation. ITER is evaluated on 476 bugs from 10 open-source projects in Defects4J 2.0. ITER succeeds in repairing 15.5% of them, including 9 uniquely repaired multi-location bugs.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623337"}, {"primary_key": "633343", "vector": [], "sparse_vector": [], "title": "Practical Non-Intrusive GUI Exploration Testing with Visual-based Robotic Arms.", "authors": ["Shengcheng Yu", "Chunrong Fang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graphical User Interface (GUI) testing has been a significant topic in the software engineering community. Most existing GUI testing frameworks are intrusive and can only support some specific platforms, which are quite limited. With the development of distinct scenarios, diverse embedded systems or customized operating systems on different devices do not support existing intrusive GUI testing frameworks. Some approaches adopt robotic arms to replace the interface invoking of mobile apps under test and use computer vision technologies to identify GUI elements. However, some challenges remain unsolved with such approaches. First, existing approaches assume that GUI screens are fixed so that they cannot be adapted to diverse systems with different screen conditions. Second, existing approaches use XY-plane robotic arm system, which cannot flexibly simulate human testing operations. Third, existing approaches ignore the compatibility bugs of apps and only focus on the crash bugs. To sum up, a more practical approach is required for the non-intrusive scenario.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639161"}, {"primary_key": "633344", "vector": [], "sparse_vector": [], "title": "CoderEval: A Benchmark of Pragmatic Code Generation with Generative Pre-trained Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Code generation models based on the pre-training and fine-tuning paradigm have been increasingly attempted by both academia and industry, resulting in well-known industrial models such as Codex, CodeGen, and PanGu-Coder. To evaluate the effectiveness of these models, multiple existing benchmarks (e.g., HumanEval and AiXBench) are proposed, including only cases of generating a standalone function, i.e., a function that may invoke or access only built-in functions and standard libraries. However, non-standalone functions, which typically are not included in the existing benchmarks, constitute more than 70% of the functions in popular open-source projects, and evaluating models' effectiveness on standalone functions cannot reflect these models' effectiveness on pragmatic code generation scenarios (i.e., code generation for real settings of open source or proprietary code).", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623316"}, {"primary_key": "633345", "vector": [], "sparse_vector": [], "title": "Deep Learning or Classical Machine Learning? An Empirical Study on Log-Based Anomaly Detection.", "authors": ["<PERSON><PERSON> Yu", "<PERSON><PERSON><PERSON>", "Qiuai Fu", "Zhiqing Zhong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While deep learning (DL) has emerged as a powerful technique, its benefits must be carefully considered in relation to computational costs. Specifically, although DL methods have achieved strong performance in log anomaly detection, they often require extended time for log preprocessing, model training, and model inference, hindering their adoption in online distributed cloud systems that require rapid deployment of log anomaly detection service.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623308"}, {"primary_key": "633348", "vector": [], "sparse_vector": [], "title": "PS3: Precise Patch Presence Test based on Semantic Symbolic Signature.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "During software development, vulnerabilities have posed a significant threat to users. Patches are the most effective way to combat vulnerabilities. In a large-scale software system, testing the presence of a security patch in every affected binary is crucial to ensure system security. Identifying whether a binary has been patched for a known vulnerability is challenging, as there may only be small differences between patched and vulnerable versions. Existing approaches mainly focus on detecting patches that are compiled in the same compiler options. However, it is common for developers to compile programs with very different compiler options in different situations, which causes inaccuracy for existing methods. In this paper, we propose a new approach named PS3, referring to precise patch presence test based on semantic-level symbolic signature. PS3 exploits symbolic emulation to extract signatures that are stable under different compiler options. Then PS3 can precisely test the presence of the patch by comparing the signatures between the reference and the target at semantic level.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639134"}, {"primary_key": "633349", "vector": [], "sparse_vector": [], "title": "Deep Combination of CDCL(T) and Local Search for Satisfiability Modulo Non-Linear Integer Arithmetic Theory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "S<PERSON>wei <PERSON>"], "summary": "Satisfiability Modulo Theory (SMT) generalizes the propositional satisfiability problem (SAT) by extending support for various first-order background theories. In this paper, we focus on the SMT problems in Non-Linear Integer Arithmetic (NIA) theory, referred to as SMT(NIA), which has wide applications in software engineering. The dominant paradigm for SMT(NIA) is the CDCL(T) framework, while recently stochastic local search (SLS) has also shown its effectiveness. However, the cooperation between the two methods has not been studied yet. Motivated by the great success of the deep cooperation of CDCL and SLS for SAT, we propose a two-layer hybrid approach for SMT(NIA). The outer-layer interleaves between the inner-layer and an independent SLS solver. In the inner-layer, we take CDCL(T) as the main body, and design DCL(T)-guided SLS solver, which is invoked at branches corresponding to skeleton solutions and returns useful information to improve the branching heuristics of CDCL(T). We implement our ideas on top of the CDCL(T) tactic of Z3 with an SLS solver called LocalSMT, resulting in a hybrid solver dubbed HybridSMT. Extensive experiments are carried out on the standard SMT(NIA) benchmarks from SMT-LIB, where most of the instances are from real-world software engineering applications of termination and non-termination analysis. Experiment results show that HybridSMT significantly improves the CDCL(T) solver in Z3. Moreover, our solver can solve 10.36% more instances than the currently best SMT(NIA) solver, and is more efficient for software verification instances.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639105"}, {"primary_key": "633350", "vector": [], "sparse_vector": [], "title": "Towards Finding Accounting Errors in Smart Contracts.", "authors": ["<PERSON>"], "summary": "Bugs in smart contracts may have devastating effects as they tend to cause financial loss. According to a recent study, accounting bugs are the most common kind of bugs in smart contracts that are beyond automated tools during pre-deployment auditing. The reason lies in that these bugs are usually in the core business logic and hence contract-specific. They are analogous to functional bugs in traditional software, which are largely beyond automated bug finding tools whose effectiveness hinges on uniform and machine checkable characteristics of bugs. It was also reported that accounting bugs are the second-most difficult to find through manual auditing, due to the need of understanding underlying business models. We observe that a large part of business logic in smart contracts can be modeled by a few primitive operations like those in a bank, such as deposit, withdraw, loan, and pay-off, or by their combinations. The properties of these operations can be clearly defined and checked by an abstract type system that models high-order information such as token units, scaling factors, and financial types. We hence develop a novel type propagation and checking system with the aim of identifying accounting bugs. Our evaluation on a large set of 57 existing accounting bugs in 29 real-world projects shows that 58% of the accounting bugs are type errors. Our system catches 87.9% of these type errors. In addition, applying our technique to auditing a large project in a very recent auditing contest has yielded the identification of 6 zero-day accounting bugs with 4 leading to direct fund loss.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639128"}, {"primary_key": "633352", "vector": [], "sparse_vector": [], "title": "When Contracts Meets Crypto: Exploring Developers&apos; Struggles with Ethereum Cryptographic APIs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zhiyuan Wan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To empower smart contracts with the promising capabilities of cryptography, Ethereum officially introduced a set of cryptographic APIs that facilitate basic cryptographic operations within smart contracts, such as elliptic curve operations. However, since developers are not necessarily cryptography experts, requiring them to directly interact with these basic APIs has caused real-world security issues and potential usability challenges. To guide future research and solutions to these challenges, we conduct the first empirical study on Ethereum cryptographic practices. Through the analysis of 91,484,856 Ethereum transactions, 500 crypto-related contracts, and 483 StackExchange posts, we provide the first in-depth look at cryptographic tasks developers need to accomplish and identify five categories of obstacles they encounter. Furthermore, we conduct an online survey with 78 smart contract practitioners to explore their perspectives on these obstacles and elicit the underlying reasons. We find that more than half of practitioners face more challenges in cryptographic tasks compared to general business logic in smart contracts. Their feedback highlights the gap between low-level cryptographic APIs and high-level tasks they need to accomplish, emphasizing the need for improved cryptographic APIs, task-based templates, and effective assistance tools. Based on these findings, we provide practical implications for further improvements and outline future research directions.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639131"}, {"primary_key": "633353", "vector": [], "sparse_vector": [], "title": "Trace-based Multi-Dimensional Root Cause Localization of Performance Issues in Microservice Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern microservice systems have become increasingly complicated due to the dynamic and complex interactions and runtime environment. It leads to the system vulnerable to performance issues caused by a variety of reasons, such as the runtime environments, communications, coordinations, or implementations of services. Traces record the detailed execution process of a request through the system and have been widely used in performance issues diagnosis in microservice systems. By identifying the execution processes and attribute value combinations that are common in anomalous traces but rare in normal traces, engineers may localize the root cause of a performance issue into a smaller scope. However, due to the complex structure of traces and the large number of attribute combinations, it is challenging to find the root cause from the huge search space. In this paper, we propose TraceContrast, a trace-based multi-dimensional root cause localization approach. TraceContrast uses a sequence representation to describe the complex structure of a trace with attributes of each span. Based on the representation, it combines contrast sequential pattern mining and spectrum analysis to localize multi-dimensional root causes efficiently. Experimental studies on a widely used microservice benchmark show that TraceContrast outperforms existing approaches in both multi-dimensional and instance-dimensional root cause localization with significant accuracy advantages. Moreover, Trace-Contrast is efficient and its efficiency can be further improved by parallel execution.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639088"}, {"primary_key": "633354", "vector": [], "sparse_vector": [], "title": "MetaLog: Generalizable Cross-System Anomaly Detection from Logs with Meta-Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Log-based anomaly detection plays a crucial role in ensuring the stability of software. However, current approaches for log-based anomaly detection heavily depend on a vast amount of labeled historical data, which is often unavailable in many real-world systems. To mitigate this problem, we leverage the features of the abundant historical labeled logs of mature systems to help construct anomaly detection models of new systems with very few labels, that is, to generalize the model ability trained from labeled logs of mature systems to achieve anomaly detection on new systems with insufficient data labels. Specifically, we propose MetaLog, a generalizable cross-system anomaly detection approach. MetaLog first incorporates a globally consistent semantic embedding module to obtain log event semantic embedding vectors in a shared global space. Then it leverages the meta-learning paradigm to improve the model's generalization ability. We evaluate MetaLog's performance on four public log datasets (HDFS, BGL, OpenStack, and Thunderbird) from four different systems. Results show that MetaLog reaches over 80% F1-score when using only 1% labeled logs of the target system, showing similar performance with state-of-the-art supervised anomaly detection models trained with 100% labeled data. Besides, it outperforms state-of-art transfer-learning-based cross-system anomaly detection models by 20% in the same settings of 1% labeled training logs of the target system.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639205"}, {"primary_key": "633357", "vector": [], "sparse_vector": [], "title": "How Are Paid and Volunteer Open Source Developers Different? A Study of the Rust Project.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Klaas<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "It is now commonplace for organizations to pay developers to work on specific open source software (OSS) projects to pursue their business goals. Such paid developers work alongside voluntary contributors, but given the different motivations of these two groups of developers, conflict may arise, which may pose a threat to a project's sustainability. This paper presents an empirical study of paid developers and volunteers in Rust, a popular open source programming language project. Rust is a particularly interesting case given considerable concerns about corporate participation. We compare volunteers and paid developers through contribution characteristics and long-term participation, and solicit volunteers' perceptions on paid developers. We find that core paid developers tend to contribute more frequently; commits contributed by onetime paid developers have bigger sizes; peripheral paid developers implement more features; and being paid plays a positive role in becoming a long-term contributor. We also find that volunteers do have some prejudices against paid developers. This study suggests that the dichotomous view of paid vs. volunteer developers is too simplistic and that further subgroups can be identified. Companies should become more sensitive to how they engage with OSS communities, in certain ways as suggested by this study.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639197"}, {"primary_key": "633358", "vector": [], "sparse_vector": [], "title": "How do Developers Talk about GitHub Actions? Evidence from Online Software Development Community.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Continuous integration, deployment and delivery (CI/CD) have become cornerstones of DevOps practices. In recent years, GitHub Action (GHA) has rapidly replaced the traditional CI/CD tools on GitHub, providing efficiently automated workflows for developers. With the widespread use and influence of GHA, it is critical to understand the existing problems that GHA developers face in their practices as well as the potential solutions to these problems. Unfortunately, we currently have relatively little knowledge in this area. To fill this gap, we conduct a large-scale empirical study of 6,590 Stack Overflow (SO) questions and 315 GitHub issues. Our study leads to the first comprehensive taxonomy of problems related to GHA, covering 4 categories and 16 sub-categories. Then, we analyze the popularity and difficulty of problem categories and their correlations. Further, we summarize 56 solution strategies for different GHA problems. We also distill practical implications of our findings from the perspective of different audiences. We believe that our study contributes to the research of emerging GHA practices and guides the future support of tools and technologies.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623327"}, {"primary_key": "633359", "vector": [], "sparse_vector": [], "title": "Hard to Read and Understand Pythonic Idioms? DeIdiom and Explain Them in Non-Idiomatic Equivalent Code.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Qinghua Lu", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Python community strives to design pythonic idioms so that Python users can achieve their intent in a more concise and efficient way. According to our analysis of 154 questions about challenges of understanding pythonic idioms on Stack Overflow, we find that Python users face various challenges in comprehending pythonic idioms. And the usage of pythonic idioms in 7,577 GitHub projects reveals the prevalence of pythonic idioms. By using a statistical sampling method, we find pythonic idioms result in not only lexical conciseness but also the creation of variables and functions, which indicates it is not straightforward to map back to non-idiomatic code. And usage of pythonic idioms may even cause potential negative effects such as code redundancy, bugs and performance degradation. To alleviate such readability issues and negative effects, we develop a transforming tool, DeIdiom, to automatically transform idiomatic code into equivalent non-idiomatic code. We test and review over 7,572 idiomatic code instances of nine pythonic idioms (list/set/dict-comprehension, chain-comparison, truth-value-test, loop-else, assign-multi-targets, for-multi-targets, star), the result shows the high accuracy of DeIdiom. Our user study with 20 participants demonstrates that explanatory non-idiomatic code generated by DeIdiom is useful for Python users to understand pythonic idioms correctly and efficiently, and leads to a more positive appreciation of pythonic idioms.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639101"}, {"primary_key": "633360", "vector": [], "sparse_vector": [], "title": "Learning-based Widget Matching for Migrating GUI Test Cases.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chengfeng Dou", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Code generation models based on the pre-training and fine-tuning paradigm have been increasingly attempted by both academia and industry, resulting in well-known industrial models such as Codex, CodeGen, and PanGu-Coder. To evaluate the effectiveness of these models, multiple existing benchmarks are proposed, including only cases of generating a standalone function, i.e., a function that may invoke or access only built-in functions and standard libraries. However, non-standalone functions, which typically are not included in the existing benchmarks, constitute more than 70% of the functions in popular open-source projects, and evaluating models' effectiveness on standalone functions cannot reflect these models' effectiveness on pragmatic code generation scenarios. To help bridge the preceding gap, in this paper, we propose a benchmark named CoderEval, consisting of 230 Python and 230 Java code generation tasks carefully curated from popular real-world open-source projects and a self-contained execution platform to automatically assess the functional correctness of generated code. CoderEval supports code generation tasks from six levels of context dependency, where context refers to code elements such as types, APIs, variables, and consts defined outside the function under generation but within the dependent third-party libraries, current class, file, or project. CoderEval can be used to evaluate the effectiveness of models in generating code beyond only standalone functions. By evaluating three code generation models on CoderEval, we find that the effectiveness of these models in generating standalone functions is substantially higher than that in generating non-standalone functions. Our analysis highlights the current progress and pinpoints future directions to further improve a model's effectiveness by leveraging contextual information for pragmatic code generation.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623322"}, {"primary_key": "633361", "vector": [], "sparse_vector": [], "title": "Early Career Software Developers - Are You Sinking or Swimming?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Background: Newbies in their early stage of software engineering careers suffer from unfitting task assignments, unclear job expectations, and insufficient communication with managers frequently, which leads to personal frustration, unsatisfactory team performance, and low employee retention. Goals: The goal of this research is to investigate new software developers' \"sink or swim\" early career experience from the following four dimensions: job assignment, newbie-manager pairing, job satisfaction, and thoughts and suggestions. Methodology: To achieve our research goal, we conducted an empirical study by distributing an online questionnaire that includes both qualitative and quantitative questions. Results: There are several factors contributing to a \"sink or swim\" early career experience, such as unclear about what to do, who to report to, lack of communication, and vague expectations, posing negative impacts on both individuals and the organization. In addition, we also propose a new community smell in our paper - Newbie Sink or Swim, based on our investigation. Conclusions: The early stage is critical to software developers' careers. A failing start phase has detrimental effects on software developers and development teams. Our study empirically examines software developers' early careers from various aspects, providing deeper insights into how to build a more supportive and productive working environment for entry-level developers in the software community.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475.3640106"}, {"primary_key": "633364", "vector": [], "sparse_vector": [], "title": "Practical Program Repair via Preference-based Ensemble Strategy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Ge", "Tegawendé F. Bissyandé", "<PERSON>", "<PERSON>"], "summary": "To date, over 40 Automated Program Repair (APR) tools have been designed with varying bug-fixing strategies, which have been demonstrated to have complementary performance in terms of being effective for different bug classes. Intuitively, it should be feasible to improve the overall bug-fixing performance of APR via assembling existing tools. Unfortunately, simply invoking all available APR tools for a given bug can result in unacceptable costs on APR execution as well as on patch validation (via expensive testing). Therefore, while assembling existing tools is appealing, it requires an efficient strategy to reconcile the need to fix more bugs and the requirements for practicality. In light of this problem, we propose a Preference-based Ensemble Program Repair framework (P-EPR), which seeks to effectively rank APR tools for repairing different bugs. P-EPR is the first non-learning-based APR ensemble method that is novel in its exploitation of repair patterns as a major source of knowledge for ranking APR tools and its reliance on a dynamic update strategy that enables it to immediately exploit and benefit from newly derived repair results. Experimental results show that P-EPR outperforms existing strategies significantly both in flexibility and effectiveness.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3623310"}, {"primary_key": "633365", "vector": [], "sparse_vector": [], "title": "PrettySmart: Detecting Permission Re-delegation Vulnerability for Token Behaviors in Smart Contracts.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hong-Ning Dai", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As an essential component in Ethereum and other blockchains, token assets have been interacted with by diverse smart contracts. Effective permission policies of smart contracts must prevent token assets from being manipulated by unauthorized adversaries. Recent efforts have studied the accessibility of privileged functions or state variables to unauthorized users. However, little attention is paid to how publicly accessible functions of smart contracts can be manipulated by adversaries to steal users' digital assets. This attack is mainly caused by the permission re-delegation (PRD) vulnerability. In this work, we propose PrettySmart, a bytecode-level Permission re-delegation vulnerability detector for Smart contracts. Our study begins with an empirical study on 0.43 million open-source smart contracts, revealing that five types of widely-used permission constraints dominate 98% of the studied contracts. Accordingly, we propose a mechanism to infer these permission constraints, as well as an algorithm to identify constraints that can be bypassed by unauthorized adversaries. Based on the identification of permission constraints, we propose to detect whether adversaries could manipulate the privileged token management functionalities of smart contracts. The experimental results on real-world datasets demonstrate the effectiveness of the proposed PrettySmart, which achieves the highest precision score and detects 118 new PRD vulnerabilities.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639140"}, {"primary_key": "633366", "vector": [], "sparse_vector": [], "title": "Investigating White-Box Attacks for On-Device Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Hailong Sun", "<PERSON>"], "summary": "Numerous mobile apps have leveraged deep learning capabilities. However, on-device models are vulnerable to attacks as they can be easily extracted from their corresponding mobile apps. Although the structure and parameters information of these models can be accessed, existing on-device attacking approaches only generate black-box attacks (i.e., indirect white-box attacks), which are less effective and efficient than white-box strategies. This is because mobile deep learning (DL) frameworks like TensorFlow Lite (TFLite) do not support gradient computing (referred to as non-debuggable models), which is necessary for white-box attacking algorithms. Thus, we argue that existing findings may underestimate the harm-fulness of on-device attacks. To validate this, we systematically analyze the difficulties of transforming the on-device model to its debuggable version and propose a Reverse Engineering framework for On-device Models (REOM), which automatically reverses the compiled on-device TFLite model to its debuggable version, enabling attackers to launch white-box attacks. Our empirical results show that our approach is effective in achieving automated transformation (i.e., 92.6%) among 244 TFLite models. Compared with previous attacks using surrogate models, REOM enables attackers to achieve higher attack success rates (10.23%→89.03%) with a hundred times smaller attack perturbations (1.0→0.01). Our findings emphasize the need for developers to carefully consider their model deployment strategies, and use white-box methods to evaluate the vulnerability of on-device models. Our artifacts 1 are available.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639144"}, {"primary_key": "633367", "vector": [], "sparse_vector": [], "title": "Out of Sight, Out of Mind: Better Automatic Vulnerability Repair by Broadening Input Ranges and Sources.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Han", "<PERSON>"], "summary": "The advances of deep learning (DL) have paved the way for automatic software vulnerability repair approaches, which effectively learn the mapping from the vulnerable code to the fixed code. Nevertheless, existing DL-based vulnerability repair methods face notable limitations: 1) they struggle to handle lengthy vulnerable code, 2) they treat code as natural language texts, neglecting its inherent structure, and 3) they do not tap into the valuable expert knowledge present in the expert system. To address this, we propose VulMaster, a Transformer-based neural network model that excels at generating vulnerability repairs by comprehensively understanding the entire vulnerable code, irrespective of its length. This model also integrates diverse information, encompassing vulnerable code structures and expert knowledge from the CWE system. We evaluated VulMaster on a real-world C/C++ vulnerability repair dataset comprising 1,754 projects with 5,800 vulnerable functions. The experimental results demonstrated that VulMaster exhibits substantial improvements compared to the learning-based state-of-the-art vulnerability repair approach. Specifically, VulMaster improves the EM, BLEU, and CodeBLEU scores from 10.2% to 20.0%, 21.3% to 29.3%, and 32.5% to 40.9%, respectively.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639222"}, {"primary_key": "633368", "vector": [], "sparse_vector": [], "title": "On Calibration of Pre-trained Code Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Pre-trained code models have achieved notable success in the field of Software Engineering (SE). However, existing studies have predominantly focused on improving model performance, with limited attention given to other critical aspects such as model calibration. Model calibration, which refers to the accurate estimation of predictive uncertainty, is a vital consideration in practical applications. Therefore, in order to advance the understanding of model calibration in SE, we conduct a comprehensive investigation into the calibration of pre-trained code models in this paper. Our investigation focuses on five pre-trained code models and four code understanding tasks, including analyses of calibration in both in-distribution and out-of-distribution settings. Several key insights are uncovered: (1) pre-trained code models may suffer from the issue of over-confidence; (2) temperature scaling and label smoothing are effective in calibrating code models in in-distribution data; (3) the issue of over-confidence in pre-trained code models worsens in different out-of-distribution settings, and the effectiveness of temperature scaling and label smoothing diminishes. All materials used in our experiments are available at https://github.com/queserasera22/Calibration-of-Pretrained-Code-Models.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639126"}, {"primary_key": "633372", "vector": [], "sparse_vector": [], "title": "GrammarT5: Grammar-Integrated Pretrained Encoder-Decoder Neural Model for Code.", "authors": ["<PERSON><PERSON>", "Qing<PERSON> Liang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Pretrained models for code have exhibited promising performance across various code-related tasks, such as code summarization, code completion, code translation, and bug detection. However, despite their success, the majority of current models still represent code as a token sequence, which may not adequately capture the essence of the underlying code structure.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639125"}, {"primary_key": "633373", "vector": [], "sparse_vector": [], "title": "ModuleGuard: Understanding and Detecting Module Conflicts in Python Ecosystem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Shen", "<PERSON><PERSON>", "<PERSON>"], "summary": "Python has become one of the most popular programming languages for software development due to its simplicity, readability, and versatility. As the Python ecosystem grows, developers face increasing challenges in avoiding module conflicts, which occur when different packages have the same namespace modules. Unfortunately, existing work has neither investigated the module conflict comprehensively nor provided tools to detect the conflict. Therefore, this paper systematically investigates the module conflict problem and its impact on the Python ecosystem. We propose a novel technique called InstSimulator, which leverages semantics and installation simulation to achieve accurate and efficient module extraction. Based on this, we implement a tool called ModuleGuard to detect module conflicts for the Python ecosystem.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639221"}, {"primary_key": "633374", "vector": [], "sparse_vector": [], "title": "A Study on the Pythonic Functional Constructs&apos; Understandability.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The use of functional constructs in programming languages such as Python has been advocated to help write more concise source code, improve parallelization, and reduce side effects. Nevertheless, their usage could lead to understandability issues. This paper reports the results of a controlled experiment conducted with 209 developers to assess the understandability of given Pythonic functional constructs---namely lambdas, comprehensions, and map/reduce/-filter functions---if compared to their procedural alternatives. To address the study's goal, we asked developers to modify code using functional constructs or not, to compare the understandability of different implementations, and to provide insights about when and where it is preferable to use such functional constructs. Results of the study indicate that code snippets with lambdas are more straightforward to modify than the procedural alternatives. However, this is not the case for comprehension. Regarding the perceived understandability, code snippets relying on procedural implementations are considered more readable than their functional alternatives. Last but not least, while functional constructs may help write compact code, improving maintainability and performance, they are considered hard to debug. Our results can lead to better education in using functional constructs, prioritizing quality assurance activities, and enhancing tool support for developers.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3597503.3639211"}, {"primary_key": "724782", "vector": [], "sparse_vector": [], "title": "Proceedings of the 46th International Conference on Software Engineering: Software Engineering in Society, ICSE-SEIS2024, Lisbon, Portugal, April 14-20, 2024", "authors": [], "summary": "The worldwide collaborative effort for the creation of software is technically and socially demanding.The more engaged developers are, the more value they impart to the software they create.Engaged developers, such as <PERSON> programming Apollo 11, can succeed in tackling the most difficult engineering tasks.In this paper, we dive deep into an original vector of engagementhumor -and study how it fuels developer engagement.First, we collect qualitative and quantitative data about the humorous elements present within three significant, real-world software projects: faker, which helps developers introduce humor within their tests; lolcommits, which captures a photograph after each contribution made by a developer; and volkswagen, an exercise in satire, which accidentally led to the invention of an impactful software tool.Second, through a developer survey, we receive unique insights from 125 developers, who share their real-life experiences with humor in software.Our analysis of the three case studies highlights the prevalence of humor in software, and unveils the worldwide community of developers who are enthusiastic about both software and humor.We also learn about the caveats of humor in software through the valuable insights shared by our survey respondents.We report clear evidence that, when practiced responsibly, humor increases developer engagement and supports them in addressing hard engineering and cognitive tasks.The most actionable highlight of our work is that software tests and documentation are the best locations in code to practice humor.", "published": "2024-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/3639475"}]