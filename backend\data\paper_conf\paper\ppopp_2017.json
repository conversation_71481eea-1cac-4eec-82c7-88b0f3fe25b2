[{"primary_key": "3851298", "vector": [], "sparse_vector": [], "title": "POSTER: <PERSON>ache-Oblivious MPI All-to-All Communications on Many-Core Architectures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the many-core era, the performance of MPI collectives is more dependent on the intra-node communication component. However, the communication algorithms generally inherit from the inter-node version and ignore the cache complexity. We propose cache-oblivious algorithms for MPI all-to-all operations, in which data blocks are copied into the receive buffers in Morton order to exploit data locality. Experimental results on different many-core architectures show that our cache-oblivious implementations significantly outperform the naive implementations based on shared heap and the highly optimized MPI libraries.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019025"}, {"primary_key": "3851299", "vector": [], "sparse_vector": [], "title": "Contention in Structured Concurrency: Provably Efficient Dynamic Non-Zero Indicators for Nested Parallelism.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Over the past two decades, many concurrent data structures have been designed and implemented. Nearly all such work analyzes concurrent data structures empirically, omitting asymptotic bounds on their efficiency, partly because of the complexity of the analysis needed, and partly because of the difficulty of obtaining relevant asymptotic bounds: when the analysis takes into account important practical factors, such as contention, it is difficult or even impossible to prove desirable bounds.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018762"}, {"primary_key": "3851303", "vector": [], "sparse_vector": [], "title": "POSTER: Reuse, don&apos;t Recycle: Transforming Algorithms that Throw Away Descriptors.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Lock-free algorithms guarantee progress by having threads help one another. Complex lock-free operations facilitate helping by creating descriptor objects that describe how other threads should help them. In many lock-free algorithms, a new descriptor is allocated for each operation. After an operation completes, its descriptor must be reclaimed by a memory reclamation scheme. Allocating and reclaiming descriptors introduces significant space and time overhead.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019035"}, {"primary_key": "3851304", "vector": [], "sparse_vector": [], "title": "S-Caffe: Co-designing MPI Runtimes and Caffe for Scalable Deep Learning on Modern GPU Clusters.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> K. Panda"], "summary": "Availability of large data sets like ImageNet and massively parallel computation support in modern HPC devices like NVIDIA GPUs have fueled a renewed interest in Deep Learning (DL) algorithms. This has triggered the development of DL frameworks like Caffe, Torch, TensorFlow, and CNTK. However, most DL frameworks have been limited to a single node. In order to scale out DL frameworks and bring HPC capabilities to the DL arena, we propose, S-Caffe; a scalable and distributed Caffe adaptation for modern multi-GPU clusters. With an in-depth analysis of new requirements brought forward by the DL frameworks and limitations of current communication runtimes, we present a co-design of the Caffe framework and the MVAPICH2-GDR MPI runtime. Using the co-design methodology, we modify Caffe's workflow to maximize the overlap of computation and communication with multi-stage data propagation and gradient aggregation schemes. We bring DL-Awareness to the MPI runtime by proposing a hierarchical reduction design that benefits from CUDA-Aware features and provides up to a massive 133x speedup over OpenMPI and 2.6x speedup over MVAPICH2 for 160 GPUs. S-Caffe successfully scales up to 160 K-80 GPUs for GoogLeNet (ImageNet) with a speedup of 2.5x over 32 GPUs. To the best of our knowledge, this is the first framework that scales up to 160 GPUs. Furthermore, even for single node training, S-Caffe shows an improvement of 14\\% and 9\\% over Nvidia's optimized Caffe for 8 and 16 GPUs, respectively. In addition, S-Caffe achieves up to 1395 samples per second for the AlexNet model, which is comparable to the performance of Microsoft CNTK.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018769"}, {"primary_key": "3851305", "vector": [], "sparse_vector": [], "title": "POSTER: An Architecture and Programming Model for Accelerating Parallel Commutative Computations via Privatization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Synchronization and data movement are the key impediments to an efficient parallel execution. To ensure that data shared by multiple threads remain consistent, the programmer must use synchronization (e.g., mutex locks) to serialize threads' accesses to data. This limits parallelism because it forces threads to sequentially access shared resources. Additionally, systems use cache coherence to ensure that processors always operate on the most up-to-date version of a value even in the presence of private caches. Coherence protocol implementations cause processors to serialize their accesses to shared data, further limiting parallelism and performance.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019030"}, {"primary_key": "3851306", "vector": [], "sparse_vector": [], "title": "KiWi: A Key-Value Map for Scalable Real-Time Analytics.", "authors": ["Dmitry <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern big data processing platforms employ huge in-memory key-value (KV) maps. Their applications simultaneously drive high-rate data ingestion and large-scale analytics. These two scenarios expect KV-map implementations that scale well with both real-time updates and large atomic scans triggered by range queries.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018761"}, {"primary_key": "3851307", "vector": [], "sparse_vector": [], "title": "Synchronized-by-Default Concurrency for Shared-Memory Systems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We explore a programming approach for concurrency that synchronizes all accesses to shared memory by default. Synchronization takes place by ensuring that all program code runs inside atomic sections even if the program code has external side effects. Threads are mapped to atomic sections that a programmer must explicitly split to increase concurrency.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018747"}, {"primary_key": "3851308", "vector": [], "sparse_vector": [], "title": "Groute: An Asynchronous Multi-GPU Programming Model for Irregular Computations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nodes with multiple GPUs are becoming the platform of choice for high-performance computing. However, most applications are written using bulk-synchronous programming models, which may not be optimal for irregular algorithms that benefit from low-latency, asynchronous communication. This paper proposes constructs for asynchronous multi-GPU programming, and describes their implementation in a thin runtime environment called Groute. Groute also implements common collective operations and distributed work-lists, enabling the development of irregular applications without substantial programming effort. We demonstrate that this approach achieves state-of-the-art performance and exhibits strong scaling for a suite of irregular applications on 8-GPU and heterogeneous systems, yielding over 7x speedup for some algorithms.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018756"}, {"primary_key": "3851309", "vector": [], "sparse_vector": [], "title": "POSTER: HythTM: Extending the Applicability of Intel TSX Hardware Transactional Support.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we introduce and experimentally evaluate a new hybrid software-hardware Transactional Memory prototype based on Intel's Haswell TSX architecture. Our prototype extends the applicability of the existing hardware support for TM by interposing a hybrid fall-back layer before the sequential, big-lock fall-back path, used by standard TSX-supported solutions in order to guarantee progress. In our experimental evaluation we use SynQuake, a realistic game benchmark modeled after Quake. Our results show that our hybrid transactional system,which we call HythTM, is able to reduce the number of transactions that go to the sequential software layer, hence avoiding hardware transaction aborts and loss of parallelism. HythTM optimizes application throughput and scalability up to 5.05x, when compared to the hardware TM with sequential fall-back path.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019027"}, {"primary_key": "3851311", "vector": [], "sparse_vector": [], "title": "An Efficient Abortable-locking Protocol for Multi-level NUMA Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The popularity of Non-Uniform Memory Access (NUMA) architectures has led to numerous locality-preserving hierarchical lock designs, such as HCLH, HMCS, and cohort locks. Locality-preserving locks trade fairness for higher throughput. Hence, some instances of acquisitions can incur long latencies, which may be intolerable for certain applications. Few locks admit a waiting thread to abandon its protocol on a timeout. State-of-the-art abortable locks are not fully locality aware, introduce high overheads, and unsuitable for frequent aborts. Enhancing locality-aware locks with lightweight timeout capability is critical for their adoption. In this paper, we design and evaluate the HMCS-T lock, a Hierarchical MCS (HMCS) lock variant that admits a timeout. HMCS-T maintains the locality benefits of HMCS while ensuring aborts to be lightweight. HMCS-T offers the progress guarantee missing in most abortable queuing locks. Our evaluations show that HMCS-T offers the timeout feature at a moderate overhead over its HMCS analog. HMCS-T, used in an MPI runtime lock, mitigated the poor scalability of an MPI+OpenMP BFS code and resulted in 4.3x superior scaling.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018768"}, {"primary_key": "3851312", "vector": [], "sparse_vector": [], "title": "EffiSha: A Software Framework for Enabling Effficient Preemptive Scheduling of GPU.", "authors": ["<PERSON><PERSON> Chen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhou"], "summary": "Modern GPUs are broadly adopted in many multitasking environments, including data centers and smartphones. However, the current support for the scheduling of multiple GPU kernels (from different applications) is limited, forming a major barrier for GPU to meet many practical needs. This work for the first time demonstrates that on existing GPUs, efficient preemptive scheduling of GPU kernels is possible even without special hardware support. Specifically, it presents EffiSha, a pure software framework that enables preemptive scheduling of GPU kernels with very low overhead. The enabled preemptive scheduler offers flexible support of kernels of different priorities, and demonstrates significant potential for reducing the average turnaround time and improving the system overall throughput of programs that time share a modern GPU.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018748"}, {"primary_key": "3851313", "vector": [], "sparse_vector": [], "title": "POSTER: Provably Efficient Scheduling of Cache-Oblivious Wavefront Algorithms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yuan Tang", "<PERSON><PERSON><PERSON>"], "summary": "Standard cache-oblivious recursive divide-and-conquer algorithms for evaluating dynamic programming recurrences have optimal serial cache complexity but often have lower parallelism compared with iterative wavefront algorithms due to artificial dependencies among subtasks. Very recently cache-oblivious recursive wavefront (COW) algorithms have been introduced which do not have any artificial dependencies. Though COW algorithms are based on fork-join primitives, they extensively use atomic operations, and as a result, performance guarantees provided by state-of-the-art schedulers for programs with fork-join primitives do not apply.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019031"}, {"primary_key": "3851314", "vector": [], "sparse_vector": [], "title": "POSTER: State Teleportation via Hardware Transactional Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "State teleportation is a new technique for exploiting hardware transactional memory (HTM) to improve existing synchronization and memory management schemes for highly-concurrent data structures. When applied to fine-grained locking, a thread holding the lock for a node launches a hardware transaction that traverses multiple successor nodes, acquires the lock for the last node reached, and releases the lock on the starting node, skipping lock acquisitions for intermediate nodes. When applied to lock-free data structures, a thread visiting a node protected by a hazard pointer launches a hardware transaction that traverses multiple successor nodes, and publishes the hazard pointer only for the last node reached, skipping the memory barriers needed to publish intermediate hazard pointers. Experimental results show that these applications of state teleportation can substantially increase the performance of both lock-based and lock-free data structures.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019026"}, {"primary_key": "3851315", "vector": [], "sparse_vector": [], "title": "Layout Lock: A Scalable Locking Paradigm for Concurrent Data Layout Modifications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data-structures can benefit from dynamic data layout modifications when the size or the shape of the data structure changes during the execution, or when different phases in the program execute different workloads. However, in a modern multi-core environment, layout modifications involve costly synchronization overhead. In this paper we propose a novel layout lock that incurs a negligible overhead for reads and a small overhead for updates of the data structure. We then demonstrate the benefits of layout changes and also the advantages of the layout lock as its supporting synchronization mechanism for two data structures. In particular, we propose a concurrent binary search tree, and a concurrent array set, that benefit from concurrent layout modifications using the proposed layout lock. Experience demonstrates performance advantages and integration simplicity.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018753"}, {"primary_key": "3851316", "vector": [], "sparse_vector": [], "title": "POSTER: IOGP: An Incremental Online Graph Partitioning for Large-Scale Distributed Graph Databases.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Large-scale graphs are becoming critical in various domains such as social network, scientific application, knowledge discovery, and even system software, etc. Many of those use cases require large-scale high-performance graph databases, which are designed for serving continuous updates from the clients, and at the same time, answering complex queries towards the current graph in an on-line manner. Those operations in graph databases, also referred as OLTP (online transaction processing) operations, need specific design and implementation in graph partitioning algorithms. In this study, we designed an incremental online graph partitioning (IOGP), optimized for OLTP workloads. It is designed to achieve better locality, generate balanced partitions, and increase the parallelism for accessing hotspots of the graph. Our evaluation results on both real world and synthetic graphs in both simulation and real system confirm a better performance on graph queries (as much as 2X) with small overheads during graph insertion (less than 10%).", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019037"}, {"primary_key": "3851318", "vector": [], "sparse_vector": [], "title": "POSTER: Distributed Control: The Benefits of Eliminating Global Synchronization via Effective Scheduling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In distributed computing, parallel overheads such as \\emph{synchronization overhead} may hinder performance. We introduce the idea of \\emph{Distributed Control} (DC) where global synchronization is reduced to \\emph{termination detection} and each worker proceeds ahead optimistically, based on the local knowledge of the global computation. To avoid \"wasted'' work, \\DC relies on local work prioritization. However, the work order obtained by local prioritization is susceptible to interference from the runtime. We show that employing effective scheduling policies and optimizations in the runtime, in conjunction with eliminating global barriers, improves performance in two graph applications: single-source shortest paths and connected components.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019036"}, {"primary_key": "3851321", "vector": [], "sparse_vector": [], "title": "Combining SIMD and Many/Multi-core Parallelism for Finite State Machines with Enumerative Speculation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Finite State Machine (FSM) is the key kernel behind many popular applications, including regular expression matching, text tokenization, and <PERSON><PERSON>man decoding. Parallelizing FSMs is extremely difficult because of the strong dependencies and unpredictable memory accesses. Previous efforts have largely focused on multi-core parallelization, and used different approaches, including {\\em speculative} and {\\em enumerative} execution, both of which have been effective but also have limitations. With increasing width and improving flexibility in SIMD instruction sets, this paper focuses on combining SIMD and multi/many-core parallelism for FSMs. We have developed a novel strategy, called {\\em enumerative speculation}. Instead of speculating on a single state as in speculative execution or enumerating all possible states as in enumerative execution, our strategy speculates transitions from several possible states, reducing the prediction overheads of speculation approach and the large amount of redundant work in the enumerative approach. A simple lookback approach produces a set of guessed states to achieve high speculation success rates in our enumerative speculation. We evaluate our method with four popular FSM applications: Huffman decoding, regular expression matching, HTML tokenization, and Div7. We obtain up to 2.5x speedup using SIMD on one core and up to 95x combining SIMD with 60 cores of an Intel Xeon Phi. On a single core, we outperform the best single-state speculative execution version by an average of 1.6x, and in combining SIMD and many-core parallelism, outperform enumerative execution by an average of 2x.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018760"}, {"primary_key": "3851322", "vector": [], "sparse_vector": [], "title": "Grammar-aware Parallelization for Scalable XPath Querying.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Semi-structured data emerge in many domains, especially in web analytics and business intelligence. However, querying such data is inherently sequential due to the nested structure of input data. Existing solutions pessimistically enumerate all execution paths to circumvent dependencies, yielding sub-optimal performance and limited scalability.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018772"}, {"primary_key": "3851323", "vector": [], "sparse_vector": [], "title": "POSTER: MAPA: An Automatic Memory Access Pattern Analyzer for GPU Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jiyoung Park", "<PERSON><PERSON><PERSON>"], "summary": "Various existing optimization and memory consistency management techniques for GPU applications rely on memory access patterns of kernels. However, they suffer from poor practicality because they require explicit user interventions to extract kernel memory access patterns. This paper proposes an automatic memory-access-pattern analysis framework called MAPA. MAPA is based on a source-level analysis technique derived from traditional symbolic analyses and a run-time pattern selection technique. The experimental results show that MAPA properly analyzes 116 real-world OpenCL kernels from Rodinia and Parboil.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019034"}, {"primary_key": "3851328", "vector": [], "sparse_vector": [], "title": "Thread Data Sharing in Cache: Theory and Measurement.", "authors": ["<PERSON><PERSON>", "Pengcheng Li", "<PERSON>"], "summary": "On modern multi-core processors, independent workloads often interfere with each other by competing for shared cache space. However, for multi-threaded workloads, where a single copy of data can be accessed by multiple threads, the threads can cooperatively share cache. Because data sharing consolidates the collective working set of threads, the effective size of shared cache becomes larger than it would have been when data are not shared. This paper presents a new theory of data sharing. It includes (1) a new metric called the shared footprint to mathematically compute the amount of data shared by any group of threads in any size cache, and (2) a linear-time algorithm to measure shared footprint by scanning the memory trace of a multi-threaded program. The paper presents the practical implementation and evaluates the new theory using 14 PARSEC and SPEC OMP benchmarks, including an example use of shared footprint in program optimization.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018759"}, {"primary_key": "3851329", "vector": [], "sparse_vector": [], "title": "A Multicore Path to Connectomics-on-Demand.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>ek<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The current design trend in large scale machine learning is to use distributed clusters of CPUs and GPUs with MapReduce-style programming. Some have been led to believe that this type of horizontal scaling can reduce or even eliminate the need for traditional algorithm development, careful parallelization, and performance engineering. This paper is a case study showing the contrary: that the benefits of algorithms, parallelization, and performance engineering, can sometimes be so vast that it is possible to solve \"cluster-scale\" problems on a single commodity multicore machine.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018766"}, {"primary_key": "3851330", "vector": [], "sparse_vector": [], "title": "POSTER: Automated Load Balancer Selection Based on Application Characteristics.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Laxmikant V. <PERSON>"], "summary": "Many HPC applications require dynamic load balancing to achieve high performance and system utilization. Different applications have different characteristics and hence require different load balancing strategies. Invocation of a suboptimal load balancing strategy can lead to inefficient execution. We propose Meta-Balancer, a framework to automatically decide the best load balancing strategy. It employs randomized decision forests, a machine learning method, to learn a model for choosing the best load balancing strategy for an application represented by a set of features that capture the application characteristics.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019033"}, {"primary_key": "3851331", "vector": [], "sparse_vector": [], "title": "Function Call Re-Vectorization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Programming languages such as C for CUDA, OpenCL or ISPC have contributed to increase the programmability of SIMD accelerators and graphics processing units. However, these languages still lack the flexibility offered by low-level SIMD programming on explicit vectors. To close this expressiveness gap while preserving performance, this paper introduces the notion of \\ourinvention{} (CREV). CREV allows changing the dimension of vectorization during the execution of a kernel, exposing it as a nested parallel kernel call. CREV affords programmability close to dynamic parallelism, a feature that allows the invocation of kernels from inside kernels, but at much lower cost. In this paper, we present a formal semantics of CREV, and an implementation of it on the ISPC compiler. We have used CREV to implement some classic algorithms, including string matching, depth first search and Bellman-Ford, with minimum effort. These algorithms, once compiled by ISPC to Intel-based vector instructions, are as fast as state-of-the-art implementations, yet much simpler. Thus, CREV gives developers the elegance of dynamic programming, and the performance of explicit SIMD programming.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018751"}, {"primary_key": "3851332", "vector": [], "sparse_vector": [], "title": "POSTER: A GPU-Friendly Skiplist Algorithm.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a design for a fine-grained lock-based skiplist optimized for Graphics Processing Units (GPUs). While GPUs are often used to accelerate streaming parallel computations, it remains a significant challenge to efficiently offload concurrent computations with more complicated data-irregular access and fine-grained synchronization. Natural building blocks for such computations would be concurrent data structures, such as skiplists, which are widely used in general purpose computations. Our design utilizes array-based nodes which are accessed and updated by warp-cooperative functions, thus taking advantage of the fact that GPUs are most efficient when memory accesses are coalesced and execution divergence is minimized. The proposed design has been implemented, and measurements demonstrate improved performance of up to 2.6x over skiplist designs for the GPU existing today.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019032"}, {"primary_key": "3851334", "vector": [], "sparse_vector": [], "title": "Checking Concurrent Data Structures Under the C/C++11 Memory Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Concurrent data structures often provide better performance on multi-core processors but are significantly more difficult to design and test than their sequential counterparts. The C/C++11 standard introduced a weak memory model with support for low-level atomic operations such as compare and swap (CAS). While low-level atomic operations can significantly improve the performance of concurrent data structures, they introduce non-intuitive behaviors that can increase the difficulty of developing code.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018749"}, {"primary_key": "3851335", "vector": [], "sparse_vector": [], "title": "Simple, Accurate, Analytical Time Modeling and Optimal Tile Size Selection for GPGPU Stencils.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Stencil computations are an important class of compute and data intensive programs that occur widely in scientific and engineeringapplications. A number of tools use sophisticated tiling, parallelization, and memory mapping strategies, and generate code that relies on vendor-supplied compilers. This code has a number of parameters, such as tile sizes, that are then tuned via empirical exploration.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018744"}, {"primary_key": "3851336", "vector": [], "sparse_vector": [], "title": "Optimizing the Four-Index Integral Transform Using Data Movement Lower Bounds Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The four-index integral transform is a fundamental and computationally demanding calculation used in many computational chemistry suites such as NWChem. It transforms a four-dimensional tensor from one basis to another. This transformation is most efficiently implemented as a sequence of four tensor contractions that each contract a four- dimensional tensor with a two-dimensional transformation matrix. Differing degrees of permutation symmetry in the intermediate and final tensors in the sequence of contractions cause intermediate tensors to be much larger than the final tensor and limit the number of electronic states in the modeled systems.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018771"}, {"primary_key": "3851337", "vector": [], "sparse_vector": [], "title": "POSTER: Poor Man&apos;s URCU.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "RCU is, among other things, a well known mechanism for memory reclamation that is meant to be used in languages without an automatic Garbage Collector, unfortunately, it requires operating system support, which is currently provided only in Linux. An alternative is to use Userspace RCU (URCU) which has two variants that can be deployed on other operating systems, named \\emph{Memory Barrier} and \\emph{Bullet Proof}.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019021"}, {"primary_key": "3851338", "vector": [], "sparse_vector": [], "title": "POSTER: A Wait-Free Queue with Wait-Free Memory Reclamation.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Queues are a widely deployed data structure. They are used extensively in many multi threaded applications, or as a communication mechanism between threads or processes. We propose a new linearizable multi-producer-multi-consumer queue we named Turn queue, with wait-free progress bounded by the number of threads, and with wait-free bounded memory reclamation. Its main characteristics are: a simple algorithm that does no memory allocation apart from creating the node that is placed in the queue, a new wait-free consensus algorithm using only the atomic instruction compare-and-swap (CAS), and is easy to plugin with other algorithms for either enqueue or dequeue methods.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019022"}, {"primary_key": "3851339", "vector": [], "sparse_vector": [], "title": "Exploiting Vector and Multicore Parallelism for Recursive, Data- and Task-Parallel Programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern hardware contains parallel execution resources that are well-suited for data-parallelism-vector units-and task parallelism-multicores. However, most work on parallel scheduling focuses on one type of hardware or the other. In this work, we present a scheduling framework that allows for a unified treatment of task- and data-parallelism. Our key insight is an abstraction, task blocks, that uniformly handles data-parallel iterations and task-parallel tasks, allowing them to be scheduled on vector units or executed independently as multicores. Our framework allows us to define schedulers that can dynamically select between executing task- blocks on vector units or multicores. We show that these schedulers are asymptotically optimal, and deliver the maximum amount of parallelism available in computation trees. To evaluate our schedulers, we develop program transformations that can convert mixed data- and task-parallel pro- grams into task block-based programs. Using a prototype instantiation of our scheduling framework, we show that, on an 8-core system, we can simultaneously exploit vector and multicore parallelism to achieve 14×-108× speedup over sequential baselines.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018763"}, {"primary_key": "3851340", "vector": [], "sparse_vector": [], "title": "Model-based Iterative CT Image Reconstruction on GPUs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Computed Tomography (CT) Image Reconstruction is an important technique used in a variety of domains, including medical imaging, electron microscopy, non-destructive testing and transportation security. Model-based Iterative Reconstruction (MBIR) using Iterative Coordinate Descent (ICD) is a CT algorithm that produces state-of-the-art results in terms of image quality. However, MBIR is highly computationally intensive and challenging to parallelize, and has traditionally been viewed as impractical in applications where reconstruction time is critical. We present the first GPU-based algorithm for ICD-based MBIR. The algorithm leverages the recently-proposed concept of SuperVoxels, and efficiently exploits the three levels of parallelism available in MBIR to better utilize the GPU hardware resources. We also explore data layout transformations to obtain more coalesced accesses and several GPU-specific optimizations for MBIR that boost performance. Across a suite of 3200 test cases, our GPU implementation obtains a geometric mean speedup of 4.43X over a state-of-the-art multi-core implementation on a 16-core iso-power CPU.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018765"}, {"primary_key": "3851341", "vector": [], "sparse_vector": [], "title": "Noise Injection Techniques to Expose Subtle and Unintended Message Races.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Ignacio <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Debugging intermittently occurring bugs within MPI applications is challenging, and message races, a condition in which two or more sends race to match with a receive, are one of the common root causes. Many debugging tools have been proposed to help programmers resolve them, but their runtime interference perturbs the timing such that subtle races often cannot be reproduced with debugging tools. We present novel noise injection techniques to expose message races even under a tool's control. We first formalize this race problem in the context of non-deterministic parallel applications and use this analysis to determine an effective noise-injection strategy to uncover them. We codified these techniques in NINJA (Noise INJection Agent) that exposes these races without modification to the application. Our evaluations on synthetic cases as well as a real-world bug in Hypre-2.10.1 show that NINJA significantly helps expose races.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018767"}, {"primary_key": "3851342", "vector": [], "sparse_vector": [], "title": "Tapir: Embedding Fork-Jo<PERSON> into LLVM&apos;s Intermediate Representation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper explores how fork-join parallelism, as supported by concurrency platforms such as Cilk and OpenMP, can be embedded into a compiler's intermediate representation (IR). Mainstream compilers typically treat parallel linguistic constructs as syntactic sugar for function calls into a parallel runtime. These calls prevent the compiler from performing optimizations across parallel control constructs. Remedying this situation is generally thought to require an extensive reworking of compiler analyses and code transformations to handle parallel semantics.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018758"}, {"primary_key": "3851344", "vector": [], "sparse_vector": [], "title": "Isoefficiency in Practice: Configuring and Understanding the Performance of Task-based Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Task-based programming offers an elegant way to express units of computation and the dependencies among them, making it easier to distribute the computational load evenly across multiple cores. However, this separation of problem decomposition and parallelism requires a sufficiently large input problem to achieve satisfactory efficiency on a given number of cores. Unfortunately, finding a good match between input size and core count usually requires significant experimentation, which is expensive and sometimes even impractical. In this paper, we propose an automated empirical method for finding the isoefficiency function of a task-based program, binding efficiency, core count, and the input size in one analytical expression. This allows the latter two to be adjusted according to given (realistic) efficiency objectives. Moreover, we not only find (i) the actual isoefficiency function but also (ii) the function one would yield if the program execution was free of resource contention and (iii) an upper bound that could only be reached if the program was able to maintain its average parallelism throughout its execution. The difference between the three helps to explain low efficiency, and in particular, it helps to differentiate between resource contention and structural conflicts related to task dependencies or scheduling. The insights gained can be used to co-design programs and shared system resources.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018770"}, {"primary_key": "3851345", "vector": [], "sparse_vector": [], "title": "It&apos;s Time for a New Old Language.", "authors": ["<PERSON> Jr."], "summary": "The most popular programming language in computer science has no compiler or interpreter. Its definition is not written down in any one place. It has changed a lot over the decades, and those changes have introduced ambiguities and inconsistencies. Today, dozens of variations are in use, and its complexity has reached the point where it needs to be re-explained, at least in part, every time it is used. Much effort has been spent in hand-translating between this language and other languages that do have compilers. The language is quite amenable to parallel computation, but this fact has gone unexploited.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018773"}, {"primary_key": "3851346", "vector": [], "sparse_vector": [], "title": "Using Butterfly-Patterned Partial Sums to Draw from Discrete Distributions.", "authors": ["<PERSON> Jr.", "<PERSON><PERSON><PERSON>"], "summary": "We describe a SIMD technique for drawing values from multiple discrete distributions, such as sampling from the random variables of a mixture model, that avoids computing a complete table of partial sums of the relative probabilities. A table of alternate (\"butterfly-patterned\") form is faster to compute, making better use of coalesced memory accesses; from this table, complete partial sums are computed on the fly during a binary search. Measurements using CUDA 7.5 on an NVIDIA Titan Black GPU show that this technique makes an entire machine-learning application that uses a Latent Dirichlet Allocation topic model with 1024 topics about about 13% faster (when using single-precision floating-point data) or about 35% faster (when using double-precision floating-point data) than doing a straightforward matrix transposition after using coalesced accesses.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018757"}, {"primary_key": "3851347", "vector": [], "sparse_vector": [], "title": "POSTER: STAR (Space-Time Adaptive and Reductive) Algorithms for Real-World Space-Time Optimality.", "authors": ["Yuan Tang", "<PERSON><PERSON><PERSON>"], "summary": "It's important to hit a space-time balance for a real-world algorithm to achieve high performance on modern shared-memory multi-core or many-core systems. However, a large class of dynamic programs with more than $O(1)$ dependency achieve optimality either in space or time, but not both. In the literature, the problem is known as the fundamental space-time tradeoff. By exploiting properly on the runtime system, we show that our STAR (Space-Time Adaptive and Reductive) technique can help these dynamic programs to achieve sublinear parallel time bounds while still maintaining work-, space-, and cache-optimality in a processor- and cache-oblivious fashion.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019029"}, {"primary_key": "3851348", "vector": [], "sparse_vector": [], "title": "Self-Checkpoint: An In-Memory Checkpoint Method Using Less Space and Its Practice on Fault-Tolerant HPL.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Jidong Zhai", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fault tolerance is increasingly important in high performance computing due to the substantial growth of system scale and decreasing system reliability. In-memory/diskless checkpoint has gained extensive attention as a solution to avoid the IO bottleneck of traditional disk-based checkpoint methods. However, applications using previous in-memory checkpoint suffer from little available memory space. To provide high reliability, previous in-memory checkpoint methods either need to keep two copies of checkpoints to tolerate failures while updating old checkpoints or trade performance for space by flushing in-memory checkpoints into disk.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018745"}, {"primary_key": "3851349", "vector": [], "sparse_vector": [], "title": "Processor-Oblivious Record and Replay.", "authors": ["<PERSON>", "<PERSON><PERSON>", "I-<PERSON>g <PERSON>", "<PERSON><PERSON>"], "summary": "Record-and-replay systems are useful tools for debugging non-deterministic parallel programs by first recording an execution and then replaying that execution to produce the same access pattern. Existing record-and-replay systems generally target thread-based execution models, and record the behaviors and interleavings of individual threads. Dynamic multithreaded languages and libraries, such as the Cilk family, OpenMP, TBB, etc., do not have a notion of threads. Instead, these languages provide a processor-oblivious model of programming, where programs expose task-parallelism using high-level constructs such as spawn/sync without regard to the number of threads/cores available to run the program. Thread-based record-and-replay would violate the processor-oblivious nature of these programs, as they incorporate the number of threads into the recorded information, constraining the replayed execution to the same number of threads.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018764"}, {"primary_key": "3851351", "vector": [], "sparse_vector": [], "title": "SC-Haskell: Sequential Consistency in Languages That Minimize Mutable Shared Heap.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A core, but often neglected, aspect of a programming language design is its memory (consistency) model. Sequential consistency~(SC) is the most intuitive memory model for programmers as it guarantees sequential composition of instructions and provides a simple abstraction of shared memory as a single global store with atomic read and writes. Unfortunately, SC is widely considered to be impractical due to its associated performance overheads.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018746"}, {"primary_key": "3851352", "vector": [], "sparse_vector": [], "title": "Eunomia: Scaling Concurrent Search Trees under Contention Using HTM.", "authors": ["<PERSON><PERSON>", "Wei<PERSON> Zhang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON>"], "summary": "While hardware transactional memory (HTM) has recently been adopted to construct efficient concurrent search tree structures, such designs fail to deliver scalable performance under contention. In this paper, we first conduct a detailed analysis on an HTM-based concurrent B+Tree, which uncovers several reasons for excessive HTM aborts induced by both false and true conflicts under contention. Based on the analysis, we advocate Eunomia, a design pattern for search trees which contains several principles to reduce HTM aborts, including splitting HTM regions with version-based concurrency control to reduce HTM working sets, partitioned data layout to reduce false conflicts, proactively detecting and avoiding true conflicts, and adaptive concurrency control. To validate their effectiveness, we apply such designs to construct a scalable concurrent B+Tree using HTM. Evaluation using key-value store benchmarks on a 20-core HTM-capable multi-core machine shows that Eunomia leads to 5X-11X speedup under high contention, while incurring small overhead under low contention.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018752"}, {"primary_key": "3851354", "vector": [], "sparse_vector": [], "title": "Silent Data Corruption Resilient Two-sided Matrix Factorizations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents an algorithm based fault tolerance method to harden three two-sided matrix factorizations against soft errors: reduction to Hessenberg form, tridiagonal form, and bidiagonal form. These two sided factorizations are usually the prerequisites to computing eigenvalues/eigenvectors and singular value decomposition. Algorithm based fault tolerance has been shown to work on three main one-sided matrix factorizations: LU, Cholesky, and QR, but extending it to cover two sided factorizations is non-trivial because there are no obvious \\textit{offline, problem} specific maintenance of checksums. We thus develop an \\textit{online, algorithm} specific checksum scheme and show how to systematically adapt the two sided factorization algorithms used in LAPACK and ScaLAPACK packages to introduce the algorithm based fault tolerance.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018750"}, {"primary_key": "3851355", "vector": [], "sparse_vector": [], "title": "POSTER: Recovering Performance for Vector-based Machine Learning on Managed Runtime.", "authors": ["<PERSON><PERSON>", "Haibing Guan", "<PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "No abstract available.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019039"}, {"primary_key": "3851356", "vector": [], "sparse_vector": [], "title": "Pagoda: Fine-Grained GPU Resource Virtualization for Narrow Tasks.", "authors": ["Tsung Tai Yeh", "<PERSON><PERSON>", "Putt Sakdhnagool", "<PERSON>", "<PERSON>"], "summary": "Massively multithreaded GPUs achieve high throughput by running thousands of threads in parallel. To fully utilize the hardware, workloads spawn work to the GPU in bulk by launching large tasks, where each task is a kernel that contains thousands of threads that occupy the entire GPU.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018754"}, {"primary_key": "3851358", "vector": [], "sparse_vector": [], "title": "POSTER: On the Problem of Consistency Exceptions in the Context of Strong Memory Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This work considers the problem of availability for memory models that throw consistency exceptions. We define a new memory model called RIx based on isolation of synchronization-free regions and a new approach called Avalon that provides RIx. Our evaluation shows that Avalon and RIx substantially reduce consistency exceptions, by 1-3 orders of magnitude and sometimes eliminate them completely. Furthermore, our exploration provides new, compelling points in the performance-availability tradeoff space.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019024"}, {"primary_key": "3851359", "vector": [], "sparse_vector": [], "title": "Understanding the GPU Microarchitecture to Achieve Bare-Metal Performance Tuning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Tan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a methodology to understand GPU microarchitectural features and improve performance for compute-intensive kernels. The methodology relies on a reverse engineering approach to crack the GPU ISA encodings in order to build a GPU assembler. An assembly microbenchmark suite correlates microarchitectural features with their performance factors to uncover instruction-level and memory hierarchy preferences. We use SGEMM as a running example to show the ways to achieve bare-metal performance tuning. The performance boost is achieved by tuning FFMA throughput by activating dual-issue, eliminating register bank conflicts, adding non-FFMA instructions with little penalty, and choosing proper width of global/shared load instructions. On NVIDIA Kepler K20m, we develop a faster SGEMM with 3.1Tflop/s performance and 88% efficiency; the performance is 15% higher than cuBLAS7.0. Applying these optimizations to convolution, the implementation gains 39%-62% performance improvement compared with cuDNN4.0. The toolchain is an attempt to automatically crack different GPU ISA encodings and build an assembler adaptively for the purpose of performance enhancements to applications on GPUs.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3018755"}, {"primary_key": "3851360", "vector": [], "sparse_vector": [], "title": "POSTER: An Infrastructure for HPC Knowledge Sharing and Reuse.", "authors": ["<PERSON><PERSON>", "Chunhua Liao", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a prototype infrastructure for addressing the barriers for effective accumulation, sharing, and reuse of the various types of knowledge for high performance parallel computing.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743.3019023"}, {"primary_key": "3884767", "vector": [], "sparse_vector": [], "title": "Proceedings of the 22nd ACM SIGPLAN Symposium on Principles and Practice of Parallel Programming, Austin, TX, USA, February 4-8, 2017", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "It is our great pleasure to welcome you to PPoPP 2017, the 22nd ACM SIGPLAN Symposium on Principles and Practice of Parallel Programming held in Austin, Texas during February 4-8, 2017, and co-located with the CGO 2017 and HPCA 2017 conferences. This year's symposium continues and reinforces the PPoPP tradition of publishing leading work on all aspects of parallel programming, including foundational and theoretical aspects, techniques, languages, compilers, runtime systems, tools, and practical experiences. Given the pervasiveness of parallel architectures in the general consumer market, PPoPP, with its interest in new parallel workloads, techniques and productivity tools for parallel programming, is becoming more relevant than ever to the computer science community. PPoPP 2017 received 132 submissions from countries all over the world. The submitted papers underwent a rigorous two-phase review process. To maintain fairness and uniform standards, the review process was double-blind, throughout. Almost all of the 132 submissions were reviewed in the first phase by four members of the combined PC and ERC. (A very small fraction received three reviews in the first phase.) All papers were assigned a discussion lead from the PC. After the first rebuttal phase and ongoing online discussions, reviewers reached a consensus to relegate half of the submissions. Their authors were subsequently notified and given the choice of withdrawing their papers. The submissions for which there was no clear consensus or that had only three reviews (very few) were retained for the second evaluation stage. In the second phase, the remaining papers received at least two additional reviews exclusively from PC members and some external specialists. After a second rebuttal period, PC and ERC members continued their online discussions and grouped papers in top, bottom and discuss categories. Finally, 35 PC members met in person over two half days from noon, November 5th through the afternoon of November 6th at the Department of Computer Science in Rice University, Houston, TX, and concluded the meeting by accepting (or conditionally accepting) a total of 29 papers. The 14 conditionally accepted papers were shepherded by volunteer PC members, and the final version was made available to all original reviewers for their approval. All in all, this process resulted in a manageable average load of 12 papers for PC members, 6 papers for ERC members, offered all authors the possibility to respond to all reviews, and helped ensure that reviewing efforts were focused on where they were needed the most. Because many quality papers could not be accommodated as regular contributions, all papers retained for the second review phase were invited to be presented as posters at the conference. As a result, 17 posters were included in the proceedings as 2 page abstracts. The posters were presented during a special two-hour late afternoon session. All authors of accepted papers were given the option of participating in a joint CGO-PPoPP Artifact Evaluation (AE) process. The AE process is intended to encourage researchers to conduct experiments in a reproducible way, to package experimental work-flows and all related materials for broad availability, and ultimately, to enable fair comparison of published techniques. This year saw a considerable increase in the amount of submitted artifacts: 27 versus 18 two years ago, almost equally split between CGO and PPoPP. The Artifact Evaluation Committee of 41 researchers and engineers spent two weeks validating and evaluating the artifacts. Each submission received at least three reviews and only eight of them fell below acceptance criteria. To help educate authors about result reproducibility, these papers were shepherded during a week's time by the AE Committee. Concurrently, the AE committee successfully tried an \"open reviewing model\", i.e., asked the community to publicly evaluate several artifacts already available at Github, Gila and other project hosting services. This enabled us to find additional external reviewers with access to HPC servers or proprietary benchmarks and tools. At the end of this process all submissions qualified to receive the AE seal and their authors were encouraged to submit a two page Artifact Appendix to document the process. The success of a major conference like PPoPP very much depends on the hard work of all members of the organizing committee who volunteer their time in this service. We thank all PC and ERC members for their thoughtful reviews and extensive online discussions, with special thanks to the PC members who came from all over the world to the meeting in Houston and deliberated for two half days and read additional papers overnight to produce the PPoPP 2017 program. Several of the PC members volunteered to shepherd papers, and deserve special thanks for their efforts. The AE process was lengthy and demanding, and the AE Chairs, Wonsun Ahn (for PPoPP) and Joe Devietti (for CGO) and their team did an amazing job on that Herculean task.", "published": "2017-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3018743"}]