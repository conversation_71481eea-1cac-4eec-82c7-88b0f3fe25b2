{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["e:\\env\\anaconda3\\envs\\axi-agent\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from llmlingua import PromptCompressor\n", "import re"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def remove_special_characters(text):\n", "    # 只保留字母、数字、空格和常用标点符号\n", "    pattern = r'[^a-zA-Z0-9\\s.,!?\\'\"]'\n", "    return re.sub(pattern, '', text)\n", "\n", "compressor = PromptCompressor(\n", "    model_name=\"microsoft/llmlingua-2-bert-base-multilingual-cased-meetingbank\",\n", "    use_llmlingua2=True,\n", "    device_map=\"cuda\"\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["motion video generation advanced parts long motions fixed resolution visual consistency propose HumanDiT Diffusion Transformer 14, 000 hours videos rendering supports video resolutions sequence lengths prefixlatent reference strategy characteristics sequences leverages KeypointDiT sequences continuation Pose Adapter pose transfer longform videos\n"]}], "source": ["ab1 = \"Human motion video generation has advanced significantly, while existing methods still struggle with accurately rendering detailed body parts like hands and faces, especially in long sequences and intricate motions. Current approaches also rely on fixed resolution and struggle to maintain visual consistency. To address these limitations, we propose HumanDiT, a pose-guided Diffusion Transformer (DiT)-based framework trained on a large and wild dataset containing 14,000 hours of high-quality video to produce high-fidelity videos with fine-grained body rendering. Specifically, (i) HumanDiT, built on DiT, supports numerous video resolutions and variable sequence lengths, facilitating learning for long-sequence video generation; (ii) we introduce a prefix-latent reference strategy to maintain personalized characteristics across extended sequences. Furthermore, during inference, HumanDiT leverages Keypoint-DiT to generate subsequent pose sequences, facilitating video continuation from static images or existing videos. It also utilizes a Pose Adapter to enable pose transfer with given sequences. Extensive experiments demonstrate its superior performance in generating long-form, pose-accurate videos across diverse scenarios.\"\n", "\n", "results = compressor.compress_prompt_llmlingua2(\n", "    remove_special_characters(ab1),\n", "    target_token=50,\n", "    # force_tokens=['.', '!', '?'],\n", "    chunk_end_tokens=['.'],\n", "    return_word_label=False,\n", "    drop_consecutive=True\n", ")\n", "\n", "\n", "print(results['compressed_prompt'])"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["e:\\env\\anaconda3\\envs\\Paper-Insight\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["e:\\project\\arxiv-insight\\.env.local\n", "acl 2020 E:\\project\\RL\\acl-anthology\\data\\xml\\2020.acl.xml 871\n", "acl 2021 E:\\project\\RL\\acl-anthology\\data\\xml\\2021.acl.xml 795\n", "acl 2022 E:\\project\\RL\\acl-anthology\\data\\xml\\2022.acl.xml 774\n", "acl 2023 E:\\project\\RL\\acl-anthology\\data\\xml\\2023.acl.xml 1249\n", "acl 2024 E:\\project\\RL\\acl-anthology\\data\\xml\\2024.acl.xml 1022\n", "emnlp 2020 E:\\project\\RL\\acl-anthology\\data\\xml\\2020.emnlp.xml 787\n", "emnlp 2021 E:\\project\\RL\\acl-anthology\\data\\xml\\2021.emnlp.xml 895\n", "emnlp 2022 E:\\project\\RL\\acl-anthology\\data\\xml\\2022.emnlp.xml 941\n", "emnlp 2023 E:\\project\\RL\\acl-anthology\\data\\xml\\2023.emnlp.xml 1182\n", "emnlp 2024 E:\\project\\RL\\acl-anthology\\data\\xml\\2024.emnlp.xml 1447\n", "naacl 2021 E:\\project\\RL\\acl-anthology\\data\\xml\\2021.naacl.xml 560\n", "naacl 2022 E:\\project\\RL\\acl-anthology\\data\\xml\\2022.naacl.xml 538\n", "naacl 2024 E:\\project\\RL\\acl-anthology\\data\\xml\\2024.naacl.xml 662\n"]}], "source": ["from tools import ACLPaper\n", "\n", "acl_paper = ACLPaper()\n", "sources = ['acl', 'emnlp', 'naacl']\n", "path_map = acl_paper.get_paper_path(start_year=2010)\n", "paper_map = {}\n", "\n", "for source, years in path_map.items():\n", "    for year, path in years.items():\n", "        papers = acl_paper.get_paper_by_path(path, source)\n", "        if len(papers) == 0: continue\n", "        paper_map[f\"{source}-{year}\"] = papers\n", "        print(source, year, path, len(papers))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "set()\n"]}], "source": ["# 检查是否有空字段\n", "for source, papers in paper_map.items():\n", "    # print(source, len(papers))\n", "    for paper in papers:\n", "        # 检查是否有字段是空的\n", "        for key, value in paper.items():\n", "            if value is None or value == '':\n", "                print(f\"{source} {key} for {paper['pdf_url']} is empty\")\n", "\n", "# 检查是否缺失 keywords 字段\n", "keys = ['title', 'authors', 'summary', 'published', 'pdf_url']\n", "sum = 0\n", "keyset = set()\n", "for source, papers in paper_map.items():\n", "    for paper in papers:\n", "        for key in keys:\n", "            if key not in paper:\n", "                print(f\"{paper['title'][0:10]} {source} {key} 缺失\")\n", "                sum += 1\n", "                keyset.add(key)\n", "print(sum)\n", "print(keyset)\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'NoneType' object has no attribute 'find'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 43\u001b[0m\n\u001b[0;32m     40\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m papers\n\u001b[0;32m     42\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m---> 43\u001b[0m     meta \u001b[38;5;241m=\u001b[39m \u001b[43mfetch_cvpr_meta\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m2024\u001b[39;49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# 示例：获取 2024 年的论文元信息\u001b[39;00m\n\u001b[0;32m     44\u001b[0m     \u001b[38;5;28mprint\u001b[39m(json\u001b[38;5;241m.\u001b[39mdumps(meta, ensure_ascii\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mF<PERSON>e\u001b[39;00m, indent\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m))\n", "Cell \u001b[1;32mIn[1], line 19\u001b[0m, in \u001b[0;36mfetch_cvpr_meta\u001b[1;34m(year)\u001b[0m\n\u001b[0;32m     16\u001b[0m \u001b[38;5;66;03m# 3. 遍历每个 <dt> 标签（包含标题和元信息）\u001b[39;00m\n\u001b[0;32m     17\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m dt \u001b[38;5;129;01min\u001b[39;00m soup\u001b[38;5;241m.\u001b[39mfind_all(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdt\u001b[39m\u001b[38;5;124m\"\u001b[39m):  \u001b[38;5;66;03m# find_all 返回列表 :contentReference[oaicite:5]{index=5}\u001b[39;00m\n\u001b[0;32m     18\u001b[0m     \u001b[38;5;66;03m# 3.1 标题和详情页链接\u001b[39;00m\n\u001b[1;32m---> 19\u001b[0m     title_tag \u001b[38;5;241m=\u001b[39m \u001b[43mdt\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfind\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mspan\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclass_\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mptitle\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfind\u001b[49m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124ma\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     20\u001b[0m     title \u001b[38;5;241m=\u001b[39m title_tag\u001b[38;5;241m.\u001b[39mget_text(strip\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)  \u001b[38;5;66;03m# 提取纯文本 :contentReference[oaicite:6]{index=6}\u001b[39;00m\n\u001b[0;32m     21\u001b[0m     paper_url \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://openaccess.thecvf.com\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m title_tag[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhref\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "\u001b[1;31mAttributeError\u001b[0m: 'NoneType' object has no attribute 'find'"]}], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "import json\n", "\n", "def fetch_cvpr_meta(year):\n", "    # 1. 构造 URL 和参数\n", "    url = f\"https://openaccess.thecvf.com/CVPR{year}\"\n", "    params = {\"day\": \"all\"}\n", "    response = requests.get(url, params=params, timeout=10)  # 发送 GET 请求 :contentReference[oaicite:3]{index=3}\n", "    response.raise_for_status()\n", "\n", "    # 2. 解析 HTML\n", "    soup = BeautifulSoup(response.text, \"html.parser\")  # 使用 html.parser 解析 :contentReference[oaicite:4]{index=4}\n", "\n", "    papers = []\n", "    # 3. 遍历每个 <dt> 标签（包含标题和元信息）\n", "    for dt in soup.find_all(\"dt\"):  # find_all 返回列表 :contentReference[oaicite:5]{index=5}\n", "        # 3.1 标题和详情页链接\n", "        title_tag = dt.find(\"span\", class_=\"ptitle\").find(\"a\")\n", "        title = title_tag.get_text(strip=True)  # 提取纯文本 :contentReference[oaicite:6]{index=6}\n", "        paper_url = \"https://openaccess.thecvf.com\" + title_tag[\"href\"]\n", "\n", "        # 3.2 作者\n", "        authors = dt.find(\"span\", class_=\"authors\").get_text(strip=True)  # CSS class 搜索 :contentReference[oaicite:7]{index=7}\n", "\n", "        # 3.3 找到对应的 <dd> 标签并提取摘要\n", "        dd = dt.find_next_sibling(\"dd\")\n", "        abstract = dd.get_text(strip=True)\n", "\n", "        # 3.4 构造 PDF 下载链接（简单替换 URL 后缀）\n", "        pdf_link = paper_url.replace(\"_paper.html\", \"_paper.pdf\")\n", "\n", "        papers.append({\n", "            \"title\": title,\n", "            \"authors\": authors,\n", "            \"abstract\": abstract,\n", "            \"pdf_link\": pdf_link\n", "        })\n", "\n", "    return papers\n", "\n", "if __name__ == \"__main__\":\n", "    meta = fetch_cvpr_meta(2024)  # 示例：获取 2024 年的论文元信息\n", "    print(json.dumps(meta, ensure_ascii=False, indent=2))"]}], "metadata": {"kernelspec": {"display_name": "Paper-Insight", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}