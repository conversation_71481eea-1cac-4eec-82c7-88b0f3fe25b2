'use client'

import { useState, useEffect } from 'react';
import { Paper } from './class-utils';
import { SiGooglescholar } from "react-icons/si";
import { Button } from '../ui/button';

const handleGoogleScholar = (title: string, e: React.MouseEvent) => {
  e.stopPropagation();
  const searchUrl = `https://scholar.google.com/scholar?q=${encodeURIComponent(title)}`;
  window.open(searchUrl, '_blank', 'noopener,noreferrer');
};


// 定义弹窗组件
const PaperModal = ({ paper, onClose, onTranslationUpdate }: { paper: Paper, onClose: () => void, onTranslationUpdate?: (translation: string) => void }) => {
    // 添加翻译状态管理
    const [translation, setTranslation] = useState(paper?.translation);
    const [isTranslating, setIsTranslating] = useState(false);

    useEffect(() => {
        const translateSummary = async () => {
            // 如果已经有翻译，就不需要重新翻译
            if (translation) return;
            
            setIsTranslating(true);
            try {
                const response = await fetch(`/api/translate`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({query: [paper?.summary]})
                });
                const data = await response.json();
                
                if (data.translation) {
                    // 翻译结果是数组，取第一个（如果是纯 str 先解析为 json）
                    const translation = Array.isArray(data.translation) ? data.translation[0] : JSON.parse(data.translation)[0];
                    setTranslation(translation);
                    paper.translation = translation; // 更新 paper 对象
                    // 通知父组件更新翻译
                    onTranslationUpdate?.(translation);
                }
            } catch (error) {
                console.error('翻译失败:', error);
            } finally {
                setIsTranslating(false);
            }
        };

        translateSummary();
    }, [paper, translation, onTranslationUpdate]);

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* 背景遮罩 */}
        <div className="fixed inset-0 bg-black/50" onClick={onClose} />
        
        {/* 对话框内容 - 添加 dark:bg-gray-800 dark:text-gray-100 用于暗黑模式 */}
        <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
          <div className="p-6 dark:text-gray-100">

            {/* 中文标题 */}
            <div className="flex justify-between items-start mb-2">
              <h2 className="text-xl font-bold">{paper?.title_translation || ''}</h2>
              <button 
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            {/* 英文标题 */}
            <div className="flex justify-between items-start mb-2">
              <h2 className="text-lg text-gray-500 font-bold">{paper?.title || ''}</h2>
            </div>
            
            <a className="text-gray-500 block mb-4 p-1">
                <span className="inline-block text-md bg-gray-100 dark:bg-gray-700 rounded px-2 py-0.5 mr-1">
                    {paper?.published?.slice(0, 7) || '暂无'}
                </span>
                <span className="inline-block text-md bg-gray-100 dark:bg-gray-700 rounded px-2 py-0.5">
                    {paper?.source || 'arxiv'}
                </span>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 text-gray-400 hover:text-gray-900"
                  onClick={(e) => handleGoogleScholar(paper?.title || '', e)}
                  title="谷歌学术页面"
                >
                  <SiGooglescholar size={24} />
                </Button>
            </a>

            <div className="space-y-4">
              {/* 论文作者 */}
              <div>
                <h3 className="font-semibold mb-2">作者</h3>
                {Array.isArray(paper?.authors) && paper.authors.length > 0 ? (
                  <p>{paper.authors.join(', ')}</p>
                ) : (
                  <p className="text-gray-500">暂无作者</p>
                )}
              </div>

              {/* 中文摘要 */}
              <div>
                <h3 className="font-semibold mb-2">中文摘要</h3>
                {isTranslating ? (
                  <p className="text-gray-500">正在翻译中...</p>
                ) : translation ? (
                  <p>{translation}</p>
                ) : (
                  <p className="text-gray-500">暂无翻译</p>
                )}
              </div>

              {/* 原文摘要 */}
              <div>
                <h3 className="font-semibold mb-2">原文摘要</h3>
                <p>{paper?.summary || '暂无摘要'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
};


export default PaperModal;