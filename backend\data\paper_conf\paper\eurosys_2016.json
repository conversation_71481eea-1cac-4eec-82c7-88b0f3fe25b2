[{"primary_key": "4093574", "vector": [], "sparse_vector": [], "title": "POSIX abstractions in modern operating systems: the old, the new, and the missing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The POSIX standard, developed 25 years ago, comprises a set of operating system (OS) abstractions that aid application portability across UNIX-based OSes. While OSes and applications have evolved tremendously over the last 25 years, POSIX, and the basic set of abstractions it provides, has remained largely unchanged. Little has been done to measure how and to what extent traditional POSIX abstractions are being used in modern OSes, and whether new abstractions are taking form, dethroning traditional ones. We explore these questions through a study of POSIX usage in modern desktop and mobile OSes: Android, OS X, and Ubuntu. Our results show that new abstractions are taking form, replacing several prominent traditional abstractions in POSIX. While the changes are driven by common needs and are conceptually similar across the three OSes, they are not converging on any new standard, increasing fragmentation.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901350"}, {"primary_key": "4093577", "vector": [], "sparse_vector": [], "title": "On the capacity of thermal covert channels in multicores.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Modern multicore processors feature easily accessible temperature sensors that provide useful information for dynamic thermal management. These sensors were recently shown to be a potential security threat, since otherwise isolated applications can exploit them to establish a thermal covert channel and leak restricted information. Previous research showed experiments that document the feasibility of (low-rate) communication over this channel, but did not further analyze its fundamental characteristics. For this reason, the important questions of quantifying the channel capacity and achievable rates remain unanswered.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901322"}, {"primary_key": "4093579", "vector": [], "sparse_vector": [], "title": "Sinter: low-bandwidth remote access for the visually-impaired.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Computer users commonly use applications designed for different operating systems (OSes). For instance, a Mac user may access a cloud-based Windows remote desktop to run an application required for her job. Current remote access protocols do not work well with screen readers, creating a disproportionate burden for users with visual impairments. These users' productivity depends on features of a specific screen reader, and readers are locked-in to a specific OS. The only current option is to run a different screen reader on each platform, which harms productivity.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901335"}, {"primary_key": "4093581", "vector": [], "sparse_vector": [], "title": "Fast and general distributed transactions using RDMA and HTM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "Recent transaction processing systems attempt to leverage advanced hardware features like RDMA and HTM to significantly boost performance, which, however, pose several limitations like requiring priori knowledge of read/write sets of transactions and providing no availability support. In this paper, we present DrTM+R, a fast in-memory transaction processing system that retains the performance benefit from advanced hardware features, while supporting general transactional workloads and high availability through replication. DrTM+R addresses the generality issue by designing a hybrid OCC and locking scheme, which leverages the strong atomicity of HTM and the strong consistency of RDMA to preserve strict serializability with high performance. To resolve the race condition between the immediate visibility of records updated by HTM transactions and the unready replication of such records, DrTM+R leverages an optimistic replication scheme that uses seqlock-like versioning to distinguish the visibility of tuples and the readiness of record replication. Evaluation using typical OLTP workloads like TPC-C and SmallBank shows that DrTM+R scales well on a 6-node cluster and achieves over 5.69 and 94 million transactions per second without replication for TPC-C and SmallBank respectively. Enabling 3-way replication on DrTM+R only incurs at most 41% overhead before reaching network bottleneck, and is still an order-of-magnitude faster than a state-of-the-art distributed transaction system (Calvin).", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901349"}, {"primary_key": "4093582", "vector": [], "sparse_vector": [], "title": "vScale: automatic and efficient processor scaling for SMP virtual machines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "SMP virtual machines (VMs) have been deployed extensively in clouds to host multithreaded applications. A widely known problem is that when CPUs are oversubscribed, the scheduling delays due to VM preemption give rise to many performance problems because of the impact of these delays on thread synchronization and I/O efficiency. Dynamically changing the number of virtual CPUs (vCPUs) by considering the available physical CPU (pCPU) cycles has been shown to be a promising approach. Unfortunately, there are currently no efficient mechanisms to support such vCPU-level elasticity.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901321"}, {"primary_key": "4093584", "vector": [], "sparse_vector": [], "title": "GeePS: scalable deep learning on distributed GPUs with a GPU-specialized parameter server.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Large-scale deep learning requires huge computational resources to train a multi-layer neural network. Recent systems propose using 100s to 1000s of machines to train networks with tens of layers and billions of connections. While the computation involved can be done more efficiently on GPUs than on more traditional CPU cores, training such networks on a single GPU is too slow and training on distributed GPUs can be inefficient, due to data movement overheads, GPU stalls, and limited GPU memory. This paper describes a new parameter server, called GeePS, that supports scalable deep learning across GPUs distributed among multiple machines, overcoming these obstacles. We show that GeePS enables a state-of-the-art single-node GPU implementation to scale well, such as to 13 times the number of training images processed per second on 16 machines (relative to the original optimized single-node code). Moreover, GeePS achieves a higher training throughput with just four GPU machines than that a state-of-the-art CPU-only system achieves with 108 machines.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901323"}, {"primary_key": "4093585", "vector": [], "sparse_vector": [], "title": "Shared address translation revisited.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern operating systems avoid duplication of code and data when they are mapped by multiple processes by sharing physical memory through mechanisms like copy-on-write. Nonetheless, a separate copy of the virtual address translation structures, such as page tables, are still maintained for each process, even if they are identical. This duplication can lead to inefficiencies in the address translation process and interference within the memory hierarchy. In this paper, we show that on Android platforms, sharing address translation structures, specifically, page tables and TLB entries, for shared libraries can improve performance. For example, at a low level, sharing address translation structures reduces the cost of fork by more than half by reducing page table construction overheads. At a higher level, application launch and IPC are faster due to page fault elimination coupled with better cache and TLB performance when context switching.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901327"}, {"primary_key": "4093586", "vector": [], "sparse_vector": [], "title": "Data tiering in heterogeneous memory systems.", "authors": ["Subramanya <PERSON>lloor", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Memory-based data center applications require increasingly large memory capacities, but face the challenges posed by the inherent difficulties in scaling DRAM and also the cost of DRAM. Future systems are attempting to address these demands with heterogeneous memory architectures coupling DRAM with high capacity, low cost, but also lower performance, non-volatile memories (NVM) such as PCM and RRAM. A key usage model intended for NVM is as cheaper high capacity volatile memory. Data center operators are bound to ask whether this model for the usage of NVM to replace the majority of DRAM memory leads to a large slowdown in their applications? It is crucial to answer this question because a large performance impact will be an impediment to the adoption of such systems.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901344"}, {"primary_key": "4093587", "vector": [], "sparse_vector": [], "title": "Hardware read-write lock elision.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hardware Lock Elision (HLE) represents a promising technique to enhance parallelism of concurrent applications relying on conventional, lock-based synchronization. The idea at the basis of current HLE approaches is to wrap critical sections into hardware transactions: this allows critical sections to be executed in parallel using a speculative approach, while leveraging on conflict detection capabilities provided by hardware transactions to ensure equivalent semantics to pessimistic lock-based synchronization.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901346"}, {"primary_key": "4093590", "vector": [], "sparse_vector": [], "title": "Yoda: a highly available layer-7 load balancer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Layer-7 load balancing is a foundational building block of online services. The lack of offerings from major public cloud providers have left online services to build their own load balancers (LB), or use third-party LB design such as HAProxy. The key problem with such proxy-based design is each proxy instance is a single point of failure, as upon its failure, the TCP flow state for the connections with the client and server is lost which breaks the user flows. This significantly affects user experience and online services revenue.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901352"}, {"primary_key": "4093591", "vector": [], "sparse_vector": [], "title": "Juggler: a practical reordering resilient network stack for datacenters.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Juggler, a practical reordering resilient network stack for datacenters that enables any packet to be sent on any path at any level of priority. Juggler adds functionality to the Generic Receive Offload layer at the entry of the network stack to put packets in order in a best-effort fashion. Juggler's design exploits the small packet delays in datacenter networks and the inherent burstiness of traffic to eliminate the negative effects of packet reordering almost entirely while keeping state for only a small number of flows at any given time. Extensive testbed experiments at 10Gb/s and 40Gb/s speeds show that Juggler is effective and lightweight: it prevents performance loss even with severe packet reordering while imposing low CPU overhead. We demonstrate the use of Juggler for per-packet multi-path load balancing and a novel system that provides bandwidth guarantees by dynamically prioritizing packets.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901334"}, {"primary_key": "4093593", "vector": [], "sparse_vector": [], "title": "Type-aware transactions for faster concurrent code.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "It is often possible to improve a concurrent system's performance by leveraging the semantics of its datatypes. We build a new software transactional memory (STM) around this observation. A conventional STM tracks read- and write-sets of memory words; even simple operations can generate large sets. Our STM, which we call STO, tracks abstract operations on transactional datatypes instead. Parts of the transactional commit protocol are delegated to these datatypes' implementations, which can use datatype semantics, and new commit protocol features, to reduce bookkeeping, limit false conflicts, and implement efficient concurrency control. We test these ideas on the STAMP benchmark suite for STM applications and on our own prior work, the Silo high-performance in-memory database, observing large performance improvements in both systems.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901348"}, {"primary_key": "4093596", "vector": [], "sparse_vector": [], "title": "NChecker: saving mobile app developers from network disruptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Most of today's mobile apps rely on the underlying networks to deliver key functions such as web browsing, file synchronization, and social networking. Compared to desktop-based networks, mobile networks are much more dynamic with frequent connectivity disruptions, network type switches, and quality changes, posing unique programming challenges for mobile app developers.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901353"}, {"primary_key": "4093598", "vector": [], "sparse_vector": [], "title": "pVM: persistent virtual memory for efficient capacity scaling and object storage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON> Gavrilovska", "<PERSON><PERSON>"], "summary": "Next-generation byte-addressable nonvolatile memories (NVMs), such as phase change memory (PCM) and Memristors, promise fast data storage, and more importantly, address DRAM scalability issues. State-of-the-art OS mechanisms for NVMs have focused on improving the block-based virtual file system (VFS) to manage both persistence and the memory capacity scaling needs of applications. However, using the VFS for capacity scaling has several limitations, such as the lack of automatic memory capacity scaling across DRAM and NVM, inefficient use of the processor cache and TLB, and high page access costs. These limitations reduce application performance and also impact applications that use NVM for persistent object storage with flat namespaces, such as photo stores, NoSQL databases, and others.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901325"}, {"primary_key": "4093600", "vector": [], "sparse_vector": [], "title": "STRADS: a distributed framework for scheduled model parallel machine learning.", "authors": ["<PERSON>", "Qi<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning (ML) algorithms are commonly applied to big data, using distributed systems that partition the data across machines and allow each machine to read and update all ML model parameters --- a strategy known as data parallelism. An alternative and complimentary strategy, model parallelism, partitions the model parameters for non-shared parallel access and updates, and may periodically repartition the parameters to facilitate communication. Model parallelism is motivated by two challenges that data-parallelism does not usually address: (1) parameters may be dependent, thus naive concurrent updates can introduce errors that slow convergence or even cause algorithm failure; (2) model parameters converge at different rates, thus a small subset of parameters can bottleneck ML algorithm completion. We propose scheduled model parallelism (SchMP), a programming approach that improves ML algorithm convergence speed by efficiently scheduling parameter updates, taking into account parameter dependencies and uneven convergence. To support SchMP at scale, we develop a distributed framework STRADS which optimizes the throughput of SchMP programs, and benchmark four common ML applications written as SchMP programs: LDA topic modeling, matrix factorization, sparse least-squares (Lasso) regression and sparse logistic regression. By improving ML progress per iteration through SchMP programming whilst improving iteration throughput through STRADS we show that SchMP programs running on STRADS outperform non-model-parallel ML implementations: for example, SchMP LDA and Sch<PERSON> Lasso respectively achieve 10x and 5x faster convergence than recent, well-established baselines.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901331"}, {"primary_key": "4093601", "vector": [], "sparse_vector": [], "title": "Flash storage disaggregation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "PCIe-based Flash is commonly deployed to provide datacenter applications with high IO rates. However, its capacity and bandwidth are often underutilized as it is difficult to design servers with the right balance of CPU, memory and Flash resources over time and for multiple applications. This work examines Flash disaggregation as a way to deal with Flash overprovisioning. We tune remote access to Flash over commodity networks and analyze its impact on workloads sampled from real datacenter applications. We show that, while remote Flash access introduces a 20% throughput drop at the application level, disaggregation allows us to make up for these overheads through resource-efficient scale-out. Hence, we show that Flash disaggregation allows scaling CPU and Flash resources independently in a cost effective manner. We use our analysis to draw conclusions about data and control plane issues in remote storage.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901337"}, {"primary_key": "4093603", "vector": [], "sparse_vector": [], "title": "Hold &apos;em or fold &apos;em?: aggregation queries under performance variations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ion <PERSON>"], "summary": "Systems are increasingly required to provide responses to queries, even if not exact, within stringent time deadlines. These systems parallelize computations over many processes and aggregate them hierarchically to get the final response (e.g., search engines and data analytics). Due to large performance variations in clusters, some processes are slower. Therefore, aggregators are faced with the question of how long to wait for outputs from processes before combining and sending them upstream. Longer waits increase the response quality as it would include outputs from more processes. However, it also increases the risk of the aggregator failing to provide its result by the deadline. This leads to all its results being ignored, degrading response quality. Our algorithm, Cedar, proposes a solution to this quandary of deciding wait durations at aggregators. It uses an online algorithm to learn distributions of durations at each level in the hierarchy and collectively optimizes the wait duration. <PERSON>'s solution is theoretically sound, fully distributed, and generically applicable across systems that use aggregation trees since it is agnostic to the causes of performance variations. Evaluation using production latency distributions from Google, Microsoft and Facebook using deployment and simulation shows that <PERSON> improves average response quality by over 100%.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901351"}, {"primary_key": "4093604", "vector": [], "sparse_vector": [], "title": "HAFT: hardware-assisted fault tolerance.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Transient hardware faults during the execution of a program can cause data corruptions. We present HAFT, a fault tolerance technique using hardware extensions of commodity CPUs to protect unmodified multithreaded applications against such corruptions. HAFT utilizes instruction-level redundancy for fault detection and hardware transactional memory for fault recovery. We evaluated HAFT with Phoenix and PARSEC benchmarks. The observed normalized runtime is 2x, with 98.9% of the injected data corruptions being detected and 91.2% being corrected. To demonstrate the effectiveness of HAFT, we applied it to real-world case studies including Memcached, Apache, and SQLite.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901339"}, {"primary_key": "4093605", "vector": [], "sparse_vector": [], "title": "Exploiting variability for energy optimization of parallel programs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we present optimizations that use DVFS mechanisms to reduce the total energy usage in scientific applications. Our main insight is that noise is intrinsic to large scale parallel executions and it appears whenever shared resources are contended. The presence of noise allows us to identify and manipulate any program regions amenable to DVFS. When compared to previous energy optimizations that make per core decisions using predictions of the running time, our scheme uses a qualitative approach to recognize the signature of executions amenable to DVFS. By recognizing the \"shape of variability\" we can optimize codes with highly dynamic behavior, which pose challenges to all existing DVFS techniques. We validate our approach using offline and online analyses for one-sided and two-sided communication paradigms. We have applied our methods to NWChem, and we show best case improvements in energy use of 12% at no loss in performance when using online optimizations running on 720 Haswell cores with one-sided communication. With NWChem on MPI two-sided and offline analysis, capturing the initialization, we find energy savings of up to 20%, with less than 1% performance cost.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901329"}, {"primary_key": "4093606", "vector": [], "sparse_vector": [], "title": "PSLO: enforcing the Xth percentile latency and throughput SLOs for consolidated VM storage.", "authors": ["<PERSON><PERSON>", "Hong Jiang", "<PERSON>", "<PERSON><PERSON>"], "summary": "It is desirable but challenging to simultaneously support latency SLO at a pre-defined percentile, i.e., the Xth percentile latency SLO, and throughput SLO for consolidated VM storage. Ensuring the Xth percentile latency contributes to accurately differentiating service levels in the metric of the application-level latency SLO compliance, especially for the application built on multiple VMs. However, the Xth percentile latency SLO and throughput SLO enforcement are the opposite sides of the same coin due to the conflicting requirements for the level of IO concurrency. To address this challenge, this paper proposes PSLO, a framework supporting the Xth percentile latency and throughput SLOs under consolidated VM environment by precisely coordinating the level of IO concurrency and arrival rate for each VM issue queue. It is noted that PSLO can take full advantage of the available IO capacity allowed by SLO constraints to improve throughput or reduce latency with the best effort. We design and implement a PSLO prototype in the real VM consolidation environment created by Xen. Our extensive trace-driven prototype evaluation shows that our system is able to optimize the Xth percentile latency and throughput for consolidated VMs under SLO constraints.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901330"}, {"primary_key": "4093607", "vector": [], "sparse_vector": [], "title": "BB: booting booster for consumer electronics with modern OS.", "authors": ["Geunsik Lim", "MyungJoo Ham"], "summary": "Unconventional computing platforms have spread widely and rapidly following smart phones and tablets: consumer electronics such as smart TVs and digital cameras. For such devices, fast booting is a critical requirement; waiting tens of seconds for a TV or a camera to boot up is not acceptable, unlike a PC or smart phone. Moreover, the software platforms of these devices have become as rich as conventional computing devices to provide comparable services. As a result, the booting procedure to start every required OS service, hardware component, and application, the quantity of which is ever increasing, may take unbearable time for most consumers. To accelerate booting, this paper introduces \\textit{Booting Booster} (BB), which is used in all 2015 Samsung Smart TV models, and which runs the Linux-based Tizen OS. BB addresses the init scheme of Linux, which launches initial user-space OS services and applications and manages the life cycles of all user processes, by identifying and isolating booting-critical tasks, deferring non-critical tasks, and enabling execution of more tasks in parallel. BB has been successfully deployed in Samsung Smart TV 2015 models achieving a cold boot in 3.5 s (compared to 8.1 s with full commercial-grade optimizations without BB) without the need for suspend-to-RAM or hibernation. After this successful deployment, we have released the source code via http://opensource.samsung.com, and BB will be included in the open-source OS, Tizen (http://tizen.org).", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901320"}, {"primary_key": "4093609", "vector": [], "sparse_vector": [], "title": "The Linux scheduler: a decade of wasted cores.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As a central part of resource management, the OS thread scheduler must maintain the following, simple, invariant: make sure that ready threads are scheduled on available cores. As simple as it may seem, we found that this invariant is often broken in Linux. Cores may stay idle for seconds while ready threads are waiting in runqueues. In our experiments, these performance bugs caused many-fold performance degradation for synchronization-heavy scientific applications, 13% higher latency for kernel make, and a 14-23% decrease in TPC-H throughput for a widely used commercial database. The main contribution of this work is the discovery and analysis of these bugs and providing the fixes. Conventional testing techniques and debugging tools are ineffective at confirming or understanding this kind of bugs, because their symptoms are often evasive. To drive our investigation, we built new tools that check for violation of the invariant online and visualize scheduling activity. They are simple, easily portable across kernel versions, and run with a negligible overhead. We believe that making these tools part of the kernel developers' tool belt can help keep this type of bug at bay.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901326"}, {"primary_key": "4093610", "vector": [], "sparse_vector": [], "title": "Partial-parallel-repair (PPR): a distributed technique for repairing erasure coded storage.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the explosion of data in applications all around us, erasure coded storage has emerged as an attractive alternative to replication because even with significantly lower storage overhead, they provide better reliability against data loss. Reed-Solomon code is the most widely used erasure code because it provides maximum reliability for a given storage overhead and is flexible in the choice of coding parameters that determine the achievable reliability. However, reconstruction time for unavailable data becomes prohibitively long mainly because of network bottlenecks. Some proposed solutions either use additional storage or limit the coding parameters that can be used. In this paper, we propose a novel distributed reconstruction technique, called Partial Parallel Repair (PPR), which divides the reconstruction operation to small partial operations and schedules them on multiple nodes already involved in the data reconstruction. Then a distributed protocol progressively combines these partial results to reconstruct the unavailable data blocks and this technique reduces the network pressure. Theoretically, our technique can complete the network transfer in ⌈(log2(k + 1))⌉ time, compared to k time needed for a (k, m) Reed-Solomon code. Our experiments show that PPR reduces repair time and degraded read time significantly. Moreover, our technique is compatible with existing erasure codes and does not require any additional storage overhead. We demonstrate this by overlaying PPR on top of two prior schemes, Local Reconstruction Code and Rotated Reed-Solomon code, to gain additional savings in reconstruction time.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901328"}, {"primary_key": "4093612", "vector": [], "sparse_vector": [], "title": "Optimizing distributed actor systems for dynamic interactive services.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Distributed actor systems are widely used for developing interactive scalable cloud services, such as social networks and on-line games. By modeling an application as a dynamic set of lightweight communicating \"actors\", developers can easily build complex distributed applications, while the underlying runtime system deals with low-level complexities of a distributed environment.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901343"}, {"primary_key": "4093613", "vector": [], "sparse_vector": [], "title": "A high performance file system for non-volatile main memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Youyou Lu"], "summary": "Emerging non-volatile main memories (NVMMs) provide data persistence at the main memory level. To avoid the double-copy overheads among the user buffer, the OS page cache, and the storage layer, state-of-the-art NVMM-aware file systems bypass the OS page cache which directly copy data between the user buffer and the NVMM storage. However, one major drawback of existing NVMM technologies is the slow writes. As a result, such direct access for all file operations can lead to suboptimal system performance.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901324"}, {"primary_key": "4093615", "vector": [], "sparse_vector": [], "title": "Efficient queue management for cluster scheduling.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Job scheduling in Big Data clusters is crucial both for cluster operators' return on investment and for overall user experience. In this context, we observe several anomalies in how modern cluster schedulers manage queues, and argue that maintaining queues of tasks at worker nodes has significant benefits. On one hand, centralized approaches do not use worker-side queues. Given the inherent feedback delays that these systems incur, they achieve suboptimal cluster utilization, particularly for workloads dominated by short tasks. On the other hand, distributed schedulers typically do employ worker-side queuing, and achieve higher cluster utilization. However, they fail to place tasks at the best possible machine, since they lack cluster-wide information, leading to worse job completion time, especially for heterogeneous workloads. To the best of our knowledge, this is the first work to provide principled solutions to the above problems by introducing queue management techniques, such as appropriate queue sizing, prioritization of task execution via queue reordering, starvation freedom, and careful placement of tasks to queues. We instantiate our techniques by extending both a centralized (YARN) and a distributed (Mercury) scheduler, and evaluate their performance on a wide variety of synthetic and production workloads derived from Microsoft clusters. Our centralized implementation, Yaq-c, achieves 1.7x improvement on median job completion time compared to YARN, and our distributed one, Yaq-d, achieves 9.3x improvement over an implementation of <PERSON><PERSON>'s batch sampling on Mercury.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901354"}, {"primary_key": "4093616", "vector": [], "sparse_vector": [], "title": "Flint: batch-interactive data-intensive processing on transient servers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cloud providers now offer transient servers, which they may revoke at anytime, for significantly lower prices than on-demand servers, which they cannot revoke. The low price of transient servers is particularly attractive for executing an emerging class of workload, which we call Batch-Interactive Data-Intensive (BIDI), that is becoming increasingly important for data analytics. BIDI workloads require large sets of servers to cache massive datasets in memory to enable low latency operation. In this paper, we illustrate the challenges of executing BIDI workloads on transient servers, where revocations (akin to failures) are the common case. To address these challenges, we design Flint, which is based on Spark and includes automated checkpointing and server selection policies that i) support batch and interactive applications and ii) dynamically adapt to application characteristics. We evaluate a prototype of Flint using EC2 spot instances, and show that it yields cost savings of up to 90% compared to using on-demand servers, while increasing running time by < 2%.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901319"}, {"primary_key": "4093617", "vector": [], "sparse_vector": [], "title": "Crayon: saving power through shape and color approximation on next-generation displays.", "authors": ["<PERSON>-<PERSON><PERSON>", "<PERSON>llers", "<PERSON>"], "summary": "We present Crayon, a library and runtime system that reduces display power dissipation by acceptably approximating displayed images via shape and color transforms. Crayon can be inserted between an application and the display to optimize dynamically generated images before they appear on the screen. It can also be applied offline to optimize stored images before they are retrieved and displayed. Crayon exploits three fundamental properties: the acceptability of small changes in shape and color, the fact that the power dissipation of OLED displays and DLP pico-projectors is different for different colors, and the relatively small energy cost of computation in comparison to display energy usage.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901347"}, {"primary_key": "4093618", "vector": [], "sparse_vector": [], "title": "Application-specific quantum for multi-core platform scheduler.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Scheduling has a significant influence on application performance. Deciding on a quantum length can be very tricky, especially when concurrent applications have various characteristics. This is actually the case in virtualized cloud computing environments where virtual machines from different users are colocated on the same physical machine. We claim that in a multi-core virtualized platform, different quantum lengths should be associated with different application types. We apply this principle in a new scheduler called AQL_Sched. We identified 5 main application types and experimentally found the best quantum length for each of them. Dynamically, AQL_Sched associates an application type with each virtual CPU (vCPU) and schedules vCPUs according to their type on physical CPU (pCPU) pools with the best quantum length. Therefore, each vCPU is scheduled on a pCPU with the best quantum length. We implemented a prototype of AQL_Sched in Xen and we evaluated it with various reference benchmarks (SPECweb2009, SPECmail2009, SPEC CPU2006, and PARSEC). The evaluation results show that AQL_Sched outperforms Xen's credit scheduler. For instance, up to 20%, 10% and 15% of performance improvements have been obtained with SPECweb2009, SPEC CPU2006 and PARSEC, respectively.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901340"}, {"primary_key": "4093620", "vector": [], "sparse_vector": [], "title": "A study of modern Linux API usage and compatibility: what to support when you&apos;re supporting.", "authors": ["Chia-Che Tsai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a study of Linux API usage across all applications and libraries in the Ubuntu Linux 15.04 distribution. We propose metrics for reasoning about the importance of various system APIs, including system calls, pseudo-files, and libc functions. Our metrics are designed for evaluating the relative maturity of a prototype system or compatibility layer, and this paper focuses on compatibility with Linux applications. This study uses a combination of static analysis to understand API usage and survey data to weight the relative importance of applications to end users.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901341"}, {"primary_key": "4093621", "vector": [], "sparse_vector": [], "title": "TetriSched: global rescheduling with adaptive plan-ahead in dynamic heterogeneous clusters.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Jun <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "TetriSched is a scheduler that works in tandem with a calendaring reservation system to continuously re-evaluate the immediate-term scheduling plan for all pending jobs (including those with reservations and best-effort jobs) on each scheduling cycle. TetriSched leverages information supplied by the reservation system about jobs' deadlines and estimated runtimes to plan ahead in deciding whether to wait for a busy preferred resource type (e.g., machine with a GPU) or fall back to less preferred placement options. Plan-ahead affords significant flexibility in handling mis-estimates in job runtimes specified at reservation time. Integrated with the main reservation system in Hadoop YARN, TetriSched is experimentally shown to achieve significantly higher SLO attainment and cluster utilization than the best-configured YARN reservation and CapacityScheduler stack deployed on a real 256 node cluster.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901355"}, {"primary_key": "4093623", "vector": [], "sparse_vector": [], "title": "Practical condition synchronization for transactional memory.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Few transactional memory implementations allow for condition synchronization among transactions. The problems are many, most notably the lack of consensus about a single appropriate linguistic construct, and the lack of mechanisms that are compatible with hardware transactional memory. In this paper, we introduce a broadly useful mechanism for supporting condition synchronization among transactions. Our mechanism supports a number of linguistic constructs for coordinating transactions, and does so without introducing overhead on in-flight hardware transactions. Experiments show that our mechanisms work well, and that the diversity of linguistic constructs allows programmers to chose the technique that is best suited to a particular application.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901342"}, {"primary_key": "4093624", "vector": [], "sparse_vector": [], "title": "Parallel sections: scaling system-level data-structures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As systems continue to increase the number of cores within cache coherency domains, traditional techniques for enabling parallel computation on data-structures are increasingly strained. A single contended cache-line bouncing between different caches can prohibit continued performance gains with additional cores. New abstractions and mechanisms are required to reassess how data-structure consistency can be provided, while maintaining stable per-core access latencies.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901356"}, {"primary_key": "4093625", "vector": [], "sparse_vector": [], "title": "Increasing large-scale data center capacity by statistical power control.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Weisong Shi", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Hu", "<PERSON><PERSON>", "Xi<PERSON>", "<PERSON>"], "summary": "Given the high cost of large-scale data centers, an important design goal is to fully utilize available power resources to maximize the computing capacity. In this paper we present Ampere, a novel power management system for data centers to increase the computing capacity by over-provisioning the number of servers. Instead of doing power capping that degrades the performance of running jobs, we use a statistical control approach to implement dynamic power management by indirectly affecting the workload scheduling, which can enormously reduce the risk of power violations. Instead of being a part of the already over-complicated scheduler, <PERSON><PERSON><PERSON> only interacts with the scheduler with two basic APIs. Instead of power control on the rack level, we impose power constraint on the row level, which leads to more room for over provisioning.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901338"}, {"primary_key": "4093627", "vector": [], "sparse_vector": [], "title": "zExpander: a key-value cache with both high performance and fewer misses.", "authors": ["<PERSON><PERSON><PERSON> Wu", "<PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "While key-value (KV) cache, such as memcached, dedicates a large volume of expensive memory to holding performance-critical data, it is important to improve memory efficiency, or to reduce cache miss ratio without adding more memory. As we find that optimizing replacement algorithms is of limited effect for this purpose, a promising approach is to use a compact data organization and data compression to increase effective cache size. However, this approach has the risk of degrading the cache's performance due to additional computation cost. A common perception is that a high-performance KV cache is not compatible with use of data compacting techniques.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901332"}, {"primary_key": "4093629", "vector": [], "sparse_vector": [], "title": "Picocenter: supporting long-lived, mostly-idle applications in cloud environments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cloud computing has evolved to meet user demands, from arbitrary VMs offered by IaaS to the narrow application interfaces of PaaS. Unfortunately, there exists an intermediate point that is not well met by today's offerings: users who wish to run arbitrary, already available binaries (as opposed to rewriting their own application for a PaaS) yet expect their applications to be long-lived but mostly idle (as opposed to the always-on VM of IaaS). For example, end users who wish to run their own email or DNS server.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901345"}, {"primary_key": "4093630", "vector": [], "sparse_vector": [], "title": "TFC: token flow control in data center networks.", "authors": ["<PERSON><PERSON>", "Fengyuan Ren", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Services in modern data center networks pose growing performance demands. However, the widely existed special traffic patterns, such as micro-burst, highly concurrent flows, on-off pattern of flow transmission, exacerbate the performance of transport protocols. In this work, an clean-slate explicit transport control mechanism, called Token Flow Control (TFC), is proposed for data center networks to achieve high link utilization, ultra-low latency, fast convergence, and rare packets dropping. TFC uses tokens to represent the link bandwidth resource and define the concept of effective flows to stand for consumers. The total tokens will be explicitly allocated to each consumer every time slot. TFC excludes in-network buffer space from the flow pipeline and thus achieves zero-queueing. Besides, a packet delay function is added at switches to prevent packets dropping with highly concurrent flows. The performance of TFC is evaluated using both experiments on a small real testbed and large-scale simulations. The results show that TFC achieves high throughput, fast convergence, near zero-queuing and rare packets loss in various scenarios.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901336"}, {"primary_key": "4093631", "vector": [], "sparse_vector": [], "title": "Oasis: energy proportionality with hybrid server consolidation.", "authors": ["<PERSON><PERSON>", "<PERSON>lton B<PERSON>", "<PERSON><PERSON>"], "summary": "Cloud data centers operate at very low utilization rates resulting in significant energy waste. Oasis is a new approach for energy-oriented cluster management that enables dense server consolidation. Oasis achieves high consolidation ratios by combining traditional full VM migration with partial VM migration. Partial VM migration is used to densely consolidate the working sets of idle VMs by migrating on-demand only the pages that are accessed by the idle VMs to a consolidation host. Full VM migration is used to dynamically adapt the placement of VMs so that hosts are free from active VMs. Oasis sizes the cluster and saves energy by placing hosts without active VMs into sleep mode. It uses a low-power memory server design to allow the sleeping hosts to continue to service memory requests. In a simulated VDI server farm, our prototype saves energy by up to 28% on weekdays and 43% on weekends with minimal impact on the user productivity.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/2901318.2901333"}, {"primary_key": "4209681", "vector": [], "sparse_vector": [], "title": "Proceedings of the Eleventh European Conference on Computer Systems, EuroSys 2016, London, United Kingdom, April 18-21, 2016", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Welcome to EuroSys 2016, held at Imperial College London, UK! This year's program includes 38 wonderful papers that cover a wide range of topics, including multicore systems and concurrency, distributed machine learning, studies of familiar operating system abstractions, heterogeneous and non-volatile memory systems, data center networking, novel techniques for energy and power optimization, and experiences from production systems.", "published": "2016-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": ""}]