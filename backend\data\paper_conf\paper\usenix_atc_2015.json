[{"primary_key": "4507508", "vector": [], "sparse_vector": [], "title": "Identifying Trends in Enterprise Data Protection Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Enterprises routinely use data protection techniques to achieve business continuity in the event of failures. To ensure that backup and recovery goals are met in the face of the steep data growth rates of modern workloads, data protection systems need to constantly evolve. Recent studies show that these systems routinely miss their goals today. However, there is little work in the literature to understand why this is the case. In this paper, we present a study of 40,000 enterprise data protection systems deploying Symantec NetBackup, a commercial backup product. In total, we analyze over a million weekly reports which have been collected over a period of three years. We discover that the main reason behind inefficiencies in data protection systems is misconfigurations. Furthermore, our analysis shows that these systems grow in bursts, leaving clients unprotected at times, and are often configured using the default parameter values. As a result, we believe there is potential in developing automated, self-healing data protection systems that achieve higher efficiency standards. To aid researchers in the development of such systems, we use our dataset to identify trends characterizing data protection systems with regards to configuration, job scheduling, and data growth.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507509", "vector": [], "sparse_vector": [], "title": "Tiered Replication: A Cost-effective Alternative to Full Cluster Geo-replication.", "authors": ["Asaf Cidon", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cloud storage systems typically use three-way random replication to guard against data loss within the cluster, and utilize cluster geo-replication to protect against correlated failures. This paper presents a much lower cost alternative to full cluster geo-replication. We demonstrate that in practical settings, using two replicas is sufficient for protecting against independent node failures, while using three random replicas is inadequate for protecting against correlated node failures. We present Tiered Replication, a replication scheme that splits the cluster into a primary and backup tier. The first two replicas are stored on the primary tier and are used to recover data in the case of independent node failures, while the third replica is stored on the backup tier and is used to protect against correlated failures. The key insight of our paper is that, since the third replicas are rarely read, we can place the backup tier on separate physical infrastructure or a remote location without affecting performance. This separation significantly increases the resilience of the storage system to correlated failures and presents a low cost alternative to geo-replication of an entire cluster. In addition, the Tiered Replication algorithm optimally minimizes the probability of data loss under correlated failures. Tiered Replication can be executed incrementally for each cluster change, which allows it to supports dynamic environments in which nodes join and leave the cluster, and it facilitates additional data placement constraints required by the storage designer, such as network and rack awareness. We have implemented Tiered Replication on HyperDex, an open-source cloud storage system, and demonstrate that it incurs a small performance overhead. Tiered Replication improves the cluster-wide MTTF by a factor of 20,000 compared to random replication and by a factor of 20 compared to previous non-random replication schemes, without increasing the amount of storage.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507510", "vector": [], "sparse_vector": [], "title": "Systematically Exploring the Behavior of Control Programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many networked systems today, ranging from home automation networks to global wide-area networks, are operated using centralized control programs. Bugs in such programs pose serious risks to system security and stability. We develop a new technique to systematically explore the behavior of control programs. Because control programs depend intimately on absolute and relative timing of inputs, a key challenge that we face is to systematically handle time. We develop an approach that models programs as timed automata and incorporates novel mechanisms to enable scalable and comprehensive exploration. We implement our approach in a tool called DeLorean and apply it to real control programs for home automation and software-defined networks. DeLorean is able to finds bugs in these programs as well as provide significantly better code coverage—up to 94% compared to 76% for existing techniques.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507511", "vector": [], "sparse_vector": [], "title": "Hawk: Hybrid Datacenter Scheduling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper addresses the problem of efficient scheduling of large clusters under high load and heterogeneous workloads. A heterogeneous workload typically consists of many short jobs and a small number of large jobs that consume the bulk of the cluster’s resources. Recent work advocates distributed scheduling to overcome the limitations of centralized schedulers for large clusters with many competing jobs. Such distributed schedulers are inherently scalable, but may make poor scheduling decisions because of limited visibility into the overall resource usage in the cluster. In particular, we demonstrate that under high load, short jobs can fare poorly with such a distributed scheduler. We propose instead a new hybrid centralized/distributed scheduler, called Hawk. In Hawk, long jobs are scheduled using a centralized scheduler, while short ones are scheduled in a fully distributed\nway. Moreover, a small portion of the cluster is reserved for the use of short jobs. In order to compensate for the occasional poor decisions made by the distributed scheduler, we propose a novel and efficient randomized work-stealing algorithm. We evaluate <PERSON> using a trace-driven simulation and a prototype implementation in Spark. In particular, using a Google trace, we show that under high load, compared to the purely distributed Sparrow scheduler, <PERSON> improves the 50th and 90th percentile runtimes by 80% and 90% for short jobs and by 35% and 10% for long jobs, respectively. Measurements of a prototype implementation using Spark on a 100-node cluster confirm the results of the simulation.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507512", "vector": [], "sparse_vector": [], "title": "Slipstream: Automatic Interprocess Communication Optimization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We presentSlipstream,a userspace solution for transparently selecting efficient local transports in distributed applications written to use TCP/IP, when such applications communicate between local processes. Slipstream is easy to deploy because it is language-agnostic, automatic, and transparent. Our design in particular (1) requires no changes to the kernel or applications, (2) correctly identifies (and optimizes) pairs of communicating local endpoints, without knowledge of routing performed locally or by the network, and (3) imposes little or no overhead when optimization is not possible, including communication with parties not using our technology. Slipstream is sufficiently general that it can not only optimize traffic between local applications, but can also be used between Docker containers residing on the same host. Our results show that Slipstream significantly improves throughput and latency, 16-100% faster throughput for server applications (and 100-200% with Docker), while imposing an overhead of around 1-3% when not in use. Overall, Slipstream enables programmers to write simpler code using TCP/IP \"everywhere\" and yet obtain the significant benefits of faster local transports whenever available.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507513", "vector": [], "sparse_vector": [], "title": "Log2: A Cost-Aware Logging Mechanism for Performance Diagnosis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Zhou", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Logging has been a common practice for monitoring and diagnosing performance issues. However, logging comes at a cost, especially for large-scale online service systems. First, the overhead incurred by intensive logging is non-negligible. Second, it is costly to diagnose a performance issue if there are a tremendous amount of redundant logs. Therefore, we believe that it is important to limit the overhead incurred by logging, without sacrificing the logging effectiveness. In this paper we proposeLog2, a cost-aware logging mechanism. Given a “budget” (defined as the maximum volume of logs allowed to be output in a time interval),Log2makes the \"whether to log\" decision through a two-phase filtering mechanism. In the first phase, a large number of irrelevant logs are discarded efficiently. In the second phase, useful logs are cached and output while complying with logging budget. In this way,Log2keeps the useful logs and discards the less useful ones. We have implementedLog2and evaluated it on an open source system as well as a real-world online service system from Microsoft. The experimental results show thatLog2can control logging overhead while preserving logging effectiveness.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507514", "vector": [], "sparse_vector": [], "title": "Boosting GPU Virtualization Performance with Hybrid Shadow Page Tables.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Haibing Guan"], "summary": "The increasing adoption of Graphic Process Unit (GPU) to computation-intensive workloads has stimulated a new computing paradigm called GPU cloud (e.g., Amazon’s GPU Cloud), which necessitates the sharing of GPU resources to multiple tenants in a cloud. However, state-of-the- art GPU virtualization techniques such as gVirt still suffer from non-trivial performance overhead for graphics memory-intensive workloads involving frequent page table updates. To understand such overhead, this paper first presents GMedia, a media benchmark, and uses it to analyze the causes of such overhead. Our analysis shows that frequent updates to guest VM’s page tables causes excessive updates to the shadow page table in the hypervisor, due to the need to guarantee the consistency between guest page table and shadow page table. To this end, this paper proposes gHyvi1, an optimized GPU virtualization scheme based on gVirt, which uses adaptive hybrid page table shadowing that combines strict and relaxed page table schemes. By significantly reducing trap-and-emulation due to page table updates, gHyvi significantly improves gVirt’s performance for memory-intensive GPU workloads. Evaluation using GMedia shows that gHyvi can achieve up to 13x performance improvement compared to gVirt, and up to 85% native performance for multithread media transcoding.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507515", "vector": [], "sparse_vector": [], "title": "Rubik: Unlocking the Power of Locality and End-point Flexibility in Cloud Scale Load Balancing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Cheng<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cloud scale load balancers, such as Ananta and Duet are critical components of the data center (DC) infrastructure, and are vital to the performance of the hosted online services. In this paper, using traffic traces from a production DC, we show that prior load balancer designs incur substantial overhead in the DC network bandwidth usage, due to the intrinsic nature of traffic redirection. Moreover, in Duet, traffic redirection results in extra bandwidth consumption in the core network and breaks the full-bisection bandwidth guarantees offered by the underlying networks such as Clos and FatTree. We present RUBIK, a load balancer that significantly lowers the DC network bandwidth usage while providing all the performance and availability benefits of Duet. RUBIKachieves its goals by applying two principles in the scale-out load balancer design—exploiting locality and applying end-point flexibility in placing the servers. We show how to jointly exploit these two principles to maximally contain the traffic load balanced to be within individual ToRs while satisfying service-specific failure domain constraints. Our evaluation using a testbed prototype and DC-scale simulation using real traffic traces shows that compared to the prior art Duet, RUBIKcan reduce the bandwidth usage by over 3x and the maximum link utilization of the DC network by 4x, while providing all the performance, scalability, and availability benefits.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507516", "vector": [], "sparse_vector": [], "title": "Bistro: Scheduling Data-Parallel Jobs Against Live Production Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Data-intensive batch jobs increasingly compete for resources with customer-facing online workloads in modern data centers. Today, the two classes of workloads run on separate infrastructures using different resource managers that pursue different objectives. Batch processing systems strive for coarse-grained throughput whereas online systems must keep the latency of fine-grained end-user requests low. Better resource management would allow both batch and online workloads to share infrastructure, reducing hardware and eliminating the inefficient and error-prone chore of creating and maintaining copies of data. This paper describes Facebook’s Bistro, a scheduler that runs data-intensive batch jobs on live, customer-facing production systems without degrading the end-user experience. Bistro employs a novel hierarchical model of data and computational resources. The model enables Bistro to schedule workloads efficiently and adapt rapidly to changing configurations. At Facebook, Bistro is replacing\nHadoop and custom-built batch schedulers, allowing batch jobs to share infrastructure with online workloads without harming the performance of either.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507517", "vector": [], "sparse_vector": [], "title": "NightWatch: Integrating Lightweight and Transparent Cache Pollution Control into Dynamic Memory Allocation Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Tan"], "summary": "Cache pollution, by which weak-locality data unduly replaces strong-locality data, may notably degrade application performance in a shared-cache multicore machine. This paper presents NightWatch, a cache management subsystem that provides general, transparent and lowoverhead pollution control to applications. NightWatch is based on the observation that data within the same memory chunk or chunks within the same allocation context often share similar locality property. NightWatch embodies this observation by online monitoring current cache locality to predict future behavior and restricting potential cache polluters proactively. We have integrated NightWatch into two popular allocators,tcmallocandptmalloc2.Experiments with SPEC CPU2006 show that NightWatch improves application performance by up to 45% (18% on average), with an average monitoring overhead of 0.57% (up to 3.02%).", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507518", "vector": [], "sparse_vector": [], "title": "LPD: Low Power Display Mechanism for Mobile and Wearable Devices.", "authors": ["MyungJoo Ham", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A plethora of mobile devices such as smartphones, wearables, and tablets have been explosively penetrated into the market in the last decade. In battery powered mobile devices, energy is a scarce resource that should be carefully managed. A mobile device consists of many components and each of them contributes to the overall power consumption. This paper focuses on the energy conservation problem in display components, the importance of which is growing as contemporary mobile devices are equipped with higher display resolutions. Prior approaches to save energy in display units either critically deteriorate user perception or depend on additional hardware. We propose a novel display energy conservation scheme calledLPD(Low Power Display) that preserves display quality without requiring specialized hardware.LPDutilizes the display update information available at the X Window system and eliminates expensive memory copies of unvaried parts.LPDcan be directly applicable to devices based on Linux and X Windows system. Numerous experimental analyses show thatLPDsaves up to 7.87% of the total device power consumption. Several commercial products such as Samsung Gear S employLPDwhose source code is disclosed to the public as open-source software athttp://opensource.samsung.comandhttp://review.tizen.org.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507519", "vector": [], "sparse_vector": [], "title": "MetaSync: File Synchronization Across Multiple Untrusted Storage Services.", "authors": ["Seungyeop Han", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cloud-based file synchronization services, such as Dropbox, are a worldwide resource for many millions of users. However, individual services often have tight resource limits, suffer from temporary outages or even shutdowns, and sometimes silently corrupt or leak user data. We design, implement, and evaluate MetaSync, a secure and reliable file synchronization service that uses multiple cloud synchronization services as untrusted storage providers. To make MetaSync work correctly, we devise a novel variant of Paxos that provides efficient and consistent updates on top of the unmodified APIs exported by existing services. Our system automatically redistributes files upon reconfiguration of providers. Our evaluation shows that MetaSync provides low update latency and high update throughput while being more trustworthy and available. MetaSync outperforms its underlying cloud services by 1.2-10\u0002 on three realistic workloads.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507520", "vector": [], "sparse_vector": [], "title": "Callisto-RTS: Fine-Grain Parallel Loops.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce Callisto-RTS, a parallel runtime system designed for multi-socket shared-memory machines. It supports very fine-grained scheduling of parallel loops—down to batches of work of around 1K cycles. Fine-grained scheduling helps avoid load imbalance while reducing the need for tuning workloads to particular machines or inputs. We use per-core iteration counts to distribute work initially, and a new asynchronous request combining technique for when threads require more work. We present results using graph analytics algorithms on a 2-socket Intel 64 machine (32 h/w contexts), and on an 8-socket SPARC machine (1024 h/w contexts). In addition to reducing the need for tuning, on the SPARC machines we improve absolute performance by up to 39% (compared with OpenMP). On both architectures Callisto-RTS provides improved scaling and performance compared with a state-of-the-art parallel runtime system (Galois).", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507521", "vector": [], "sparse_vector": [], "title": "LAMA: Optimized Locality-aware Memory Allocation for Key-value Cache.", "authors": ["Xiameng Hu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The in-memory cache system is a performance-critical layer in today’s web server architecture. Memcached is one of the most effective, representative, and prevalent among such systems. An important problem is memory allocation. The default design does not make the best use of the memory. It fails to adapt when the demand changes, a problem known asslab calcification. This paper introduces locality-aware memory allocation (LAMA), which solves the problem by first analyzing the locality of the Memcached requests and then repartitioning the memory to minimize the miss ratio and the average response time. By evaluating LAMA using various industry and academic workloads, the paper shows that LAMA outperforms existing techniques in the steady-state performance, the speed of convergence, and the ability to adapt to request pattern changes and overcome slab calcification. The new solution is close to optimal, achieving over 98% of the theoretical potential.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507522", "vector": [], "sparse_vector": [], "title": "WearDrive: Fast and Energy-Efficient Storage for Wearables.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Size and weight constraints on wearables limit their battery capacity and restrict them from providing rich functionality. The need for durable and secure storage for personal data further compounds this problem as these features incur energy-intensive operations. This paper presents WearDrive, a fast storage system for wearables based on battery-backed RAM and an efficient means to offload energy intensive tasks to the phone. WearDrive leverages low-power network connectivity available on wearables to trade the phone’s battery for the wearable’s by performing large and energy-intensive tasks on the\nphone while performing small and energy-efficient tasks locally using battery-backed RAM. WearDrive improves the performance of wearable applications by up to 8.85x and improves battery life up to 3.69x with negligible impact to the phone’s battery life.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507523", "vector": [], "sparse_vector": [], "title": "Spartan: A Distributed Array Framework with Smart Tiling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON>"], "summary": "Application programmers in domains like machine learning, scientific computing, and computational biology are accustomed to using powerful, high productivity array languages such as MatLab, R and NumPy. Distributed array frameworks aim to scale array programs across machines. However, maximizing the locality of access to distributed arrays is an unsolved problem; such locality is critical for high performance. This paper presents Spartan, a distributed array framework that automatically determines how to best partition (aka “tile”) ndimensional arrays and to co-locate data with computation to maximize locality. <PERSON> combines a lazy-evaluation based, optimizing frontend with a distributed tiled array backend. Central to Spartan’s design is a small number of carefully chosen parallelhigh-level operators,which form the expression graph captured by <PERSON>’s frontend during runtime. These operators simplify the programming of distributed applications.More importantly, their well-defined semantics allow <PERSON>’s runtime to calculate the costs of different tiling strategies and pick the best one for evaluating the entire expression graph. Using <PERSON>, we have implemented 12 applications from a variety of domains including machine learning and scientific computing. Our evaluations show that <PERSON>’s automatic tiling mechanism leads to good and scalable performance while eliminating the need for manual tiling.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507524", "vector": [], "sparse_vector": [], "title": "Shoal: Smart Allocation and Replication of Memory For Parallel Programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern NUMA multi-core machines exhibit complex latency and throughput characteristics, making it hard to allocate memory optimally for a given program’s access patterns. However, sub-optimal allocation can significantly impact performance of parallel programs. We present an array abstraction that allows data placement to be automatically inferred from program analysis, and implement the abstraction in Shoal, a runtime library for parallel programs on NUMA machines. In Shoal, arrays can be automatically replicated, distributed, or partitioned across NUMA domains based on annotating memory allocation statements to indicate access patterns. We further show how such annotations can be automatically provided by compilers for high-level domain-specific languages (for example, the Green-Marl graph language). Finally, we show how Shoal can exploit additional hardware such as programmable DMA copy engines to further improve parallel program performance. We demonstrate significant performance benefits from automatically selecting a good array implementation based on memory access patterns and machine characteristics. We present two case-studies:(i)Green-Marl, a graph analytics workload using automatically annotated code based on information extracted from the high-level program and(ii)a manually-annotated version of the PARSEC Streamcluster benchmark.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507525", "vector": [], "sparse_vector": [], "title": "SpanFS: A Scalable File System on Fast Storage Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most recent storage devices, such as NAND flash-based solid state drives (SSDs), provide low access latency and high degree of parallelism. However, conventional file systems, which are designed for slow hard disk drives, often encounter severe scalability bottlenecks in exploiting the advances of these fast storage devices on manycore architectures. To scale file systems to many cores, we propose SpanFS, a novel file system which consists of a collection of micro file system services called domains. SpanFS distributes files and directories among the domains, provides a global file system view on top of the domains and maintains consistency in case of system crashes. SpanFS is implemented based on the Ext4 file system. Experimental results evaluating SpanFS against Ext4 on a modern PCI-E SSD show that SpanFS scales much better than Ext4 on a 32-core machine. In micro-benchmarks SpanFS outperforms Ext4 by up to 1226%. In application-level benchmarks SpanFS improves the performance by up to 73% relative to Ext4.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507526", "vector": [], "sparse_vector": [], "title": "Mercury: Hybrid Centralized and Distributed Scheduling in Large Shared Clusters.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Kishore Chaliparambil", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Datacenter-scale computing for analytics workloads is increasingly common. High operational costs force heterogeneous applications to share cluster resources for achieving economy of scale. Scheduling such large and diverse workloads is inherently hard, and existing approaches tackle this in two alternative ways: 1)centralizedsolutions offer strict, secure enforcement of scheduling invariants (e.g., fairness, capacity) for heterogeneous applications, 2)distributedsolutions offer scalable, efficient scheduling for homogeneous applications. We argue that these solutions are complementary, and advocate a blended approach. Concretely, we propose Mercury, ahybrid resource management frameworkthat supports the full spectrum of scheduling, from centralized to distributed. Mercury exposes a programmatic interface that allows applications to trade-off between scheduling overhead and execution guarantees. Our framework harnesses this flexibility by opportunistically utilizing resources to improve task throughput. Experimental results on production-derived workloads show gains of over 35% in task throughput. These benefits can be translated by appropriate application and frameworkpoliciesinto job throughput or job latency improvements. We have implemented and contributed Mercury as an extension of Apache Hadoop / YARN.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507527", "vector": [], "sparse_vector": [], "title": "Request-Oriented Durable Write Caching for Application Performance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jinkyu Jeong"], "summary": "Non-volatile write cache (NVWC) can help to improve the performance of I/O-intensive tasks, especially write-dominated tasks. The benefit of NVWC, however, cannot be fully exploited if an admission policy blindly caches all writes without differentiating the criticality of each write in terms of application performance. We propose a request-oriented admission policy, which caches only writes awaited in the context of request execution. To accurately detect such writes, a critical process, which is involved in handling requests, is identified by application-level hints. Then, we devise criticality inheritance protocols in order to handle process and I/O dependencies to a critical process. The proposed scheme is implemented on the Linux kernel and is evaluated with PostgreSQL relational database and Redis NoSQL store. The evaluation results show that our scheme outperforms the policy that blindly caches all writes by up to 2.2× while reducing write traffic to NVWC by up to 87%.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507528", "vector": [], "sparse_vector": [], "title": "FloSIS: A Highly Scalable Network Flow Capture System for Fast Retrieval and Storage Efficiency.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "KyoungSoo Park"], "summary": "Network packet capture performs essential functions in network management such as attack analysis, network troubleshooting, and performance debugging. As the network edge bandwidth exceeds 10 Gbps, the demand for scalable packet capture and retrieval is rapidly increasing. However, existing software-based packet capture systems neither provide high performance nor support flow-level indexing for fast query response. This would either prevent important packets from being stored or make it too slow to retrieve relevant flows. In this paper, we present FloSIS, a highly scalable, software-based flow storing and indexing system. FloSIS is characterized as the following three aspects. First, it exercises full parallelism in multiple CPU cores and disks at all stages of packet processing. Second, it constructs two-stage flow-level indexes, which helps minimize expensive disk access for user queries. It also stores the packets in the same flow at a contiguous disk location, which maximizes disk read throughput. Third, we optimize storage usage by flow-level content deduplication at real time. Our evaluation shows that FloSIS on a dual octa-core CPU machine with 24 HDDs achieves 30 Gbps of zero-drop performance with real traffic, consuming only 0.25% of the space for indexing.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507529", "vector": [], "sparse_vector": [], "title": "WALDIO: Eliminating the Filesystem Journaling in Resolving the Journaling of Journal Anomaly.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hankeun Son", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Beomseok <PERSON>", "<PERSON><PERSON><PERSON> Won"], "summary": "This work is dedicated to resolve the Journaling of Journal Anomaly in Android IO stack.We orchestrate SQLite and EXT4 filesystem so that SQLite’s file-backed journaling activity can dispense with the expensive filesystem intervention, thejournaling,without compromising the file integrity under unexpected filesystem failure. In storing the logs, we exploit the direct IO to suppress the filesystem interference. This work consists of three key ingredients: (i)Preallocation with Explicit Journaling,(ii)Header Embedding,and (iii)Group Synchronization.Preallocation with Explicit Journaling eliminates the filesystem journaling properly protecting the file metadata against the unexpected system crash. We redesign the SQLite B-tree structure with Header Embedding to make it direct IO compatible and block IO friendly. With Group Synch, we minimize the synchronization overhead of direct IO and make the SQLite operation NAND Flash friendly. Combining the three technical ingredients, we develop a new journal mode in SQLite, the WALDIO. We implement it on the commercially available smartphone. WALDIO mode achieves 5.1x performance (insert/sec) against WAL mode which is the fastest journaling mode in SQLite. It yields 2.7x performance (inserts/ sec) against the LS-MVBT, the fastest SQLite journaling mode known to public. WALDIO mode achieves 7.4x performance (insert/sec) against WAL mode when it is relieved from the overhead of explicitly synchronizing individual log-commit operations. WALDIO mode reduces the IO volume to 1/6 compared against the WAL mode.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507530", "vector": [], "sparse_vector": [], "title": "Accurate Latency-based Congestion Feedback for Datacenters.", "authors": ["<PERSON><PERSON><PERSON>", "Chunjong Park", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Han"], "summary": "The nature of congestion feedback largely governs the behavior of congestion control. In datacenter networks, where RTTs are in hundreds of microseconds, accurate feedback is crucial to achieve both high utilization and low queueing delay. Proposals for datacenter congestion control predominantly leverage ECN or even explicit in-network feedback (e.g., RCP-type feedback) to minimize the queuing delay. In this work we explore latency-based feedback as an alternative and show its advantages over ECN. Against the common belief that such implicit feedback is noisy and inaccurate, we demonstrate that latency-based implicit feedback is accurate enough to signal a single packet’s queuing delay in 10 Gbps networks. DX enables accurate queuing delay measurements whose error falls within 1.98 and 0.53 microseconds using software-based and hardware-based latency measurements, respectively. This enables us to design a new congestion control algorithm that performs fine-grained control to adjust the congestion window just enough to achieve very low queuing delay while attaining full utilization. Our extensive evaluation shows that 1) the latency measurement accurately reflects the one-way queuing delay in single packet level; 2) the latency feedback can be used to perform practical and fine-grained congestion control in high-speed datacenter networks; and 3) DX outperforms DCTCP with 5.33x smaller median queueing delay at 1 Gbps and 1.57x at 10 Gbps.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507531", "vector": [], "sparse_vector": [], "title": "Thread and Memory Placement on NUMA Systems: Asymmetry Matters.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "It is well known that the placement of threads and memory plays a crucial role for performance on NUMA (Non-Uniform Memory-Access) systems. The conventional wisdom is to place threads close to their memory, to collocate on the same node threads that share data, and to segregate on different nodes threads that compete for memory bandwidth or cache resources. While many studies addressed thread and data placement, none of them considered a crucial property of modern NUMA systems that is likely to prevail in the future:asymmetric interconnect.When the nodes are connected by links of different bandwidth, we must consider not only whether the threads and data are placed on the same or different nodes, but how these nodes are connected. We study the effects of asymmetry on a widely available x86 system and find that performance can vary by more than 2x under the same distribution of thread and data across the nodes but different inter-node connectivity. The key new insight is that the best-performing connectivity is the one with the greatest total bandwidth as opposed to the smallest number of hops. Based on our findings we designed and implemented a dynamic thread and memory placement algorithm in Linux that delivers similar or better performance than the best static placement and up to 218% better performance than when the placement is chosen randomly.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507532", "vector": [], "sparse_vector": [], "title": "Pyro: A Spatial-Temporal Big-Data Storage System.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the rapid growth of mobile devices and applications, geo-tagged data has become a major workload for big data storage systems. In order to achieve scalability, existing solutions build an additional index layer above general purpose distributed data stores. Fulfilling the semantic level need, this approach, however, leaves a lot to be desired for execution efficiency, especially when users query for moving objects within a high resolution geometric area, which we call geometry queries. Such geometry queries translate to a much larger set of range scans, forcing the backend to handle orders of magnitude more requests. Moreover, spatial-temporal applications naturally create dynamic workload hotspots1, which pushes beyond the design scope of existing solutions. This paper presents Pyro, a spatial-temporal bigdata storage system tailored for high resolution geometry queries and dynamic hotspots. Pyro understands geometries internally, which allows range scans of a geometry query to be aggregately optimized. Moreover, Pyro employs a novel replica placement policy in the DFS layer that allows Pyro to split a region without losing data locality benefits. Our evaluations use NYC taxi trace data and an 80-server cluster. Results show that Pyro reduces the response time by 60X on 1kmx\u00021kmrectangle geometries compared to the state-of-the-art solutions. Pyro further achieves 10X throughput improvement on 100mx\u0002100mrectangle geometries.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507533", "vector": [], "sparse_vector": [], "title": "CDStore: Toward Reliable, Secure, and Cost-Efficient Cloud Storage via Convergent Dispersal.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We presentCDStore,which disperses users’ backup data across multiple clouds and provides a unified multi-cloud storage solution with reliability, security, and cost efficiency guarantees. CDStore builds on an augmented secret sharing scheme calledconvergent dispersal,which supports deduplication by using deterministic content-derived hashes as inputs to secret sharing. We present the design of CDStore, and in particular, describe how it combines convergent dispersal with two-stage deduplication to achieve both bandwidth and storage savings and be robust against side-channel attacks. We evaluate the performance of our CDStore prototype using real-world workloads on LAN and commercial cloud testbeds. Our cost analysis also demonstrates that CDStore achieves a monetary cost saving of 70% over a baseline cloud storage solution using state-of-the-art secret sharing.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507534", "vector": [], "sparse_vector": [], "title": "Fence: Protecting Device Availability With Uniform Resource Control.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Applications such as software updaters or a run-away web app, even if low priority, can cause performance degradation, loss of battery life, or other issues that reduce a computing device’savailability.The core problem is that OS resource control mechanisms unevenly apply uncoordinated policies across different resources. This paper shows how handling resources – e.g., CPU, memory, sockets, and bandwidth – in coordination, through a unifying abstraction, can be both simpler and more effective. We abstract resources along two dimensions of fungibility and renewability, to enable resource-agnostic algorithms to provide resource limits for a diverse set of applications. We demonstrate the power of our resource abstraction with a prototype resource control subsystem, Fence, which we implement for two sandbox environments running on a wide variety of operating systems (Windows, Linux, the BSDs, Mac OS X, iOS, Android, OLPC, and Nokia) and device types (servers, desktops, tablets, laptops, and smartphones). We use <PERSON>ce to provide systemwide protection against resource hogging processes that include limiting battery drain, preventing overheating, and isolating performance. Even when there is interference, <PERSON><PERSON> can double the battery life and improve the responsiveness of other applications by an order of magnitude. <PERSON><PERSON> is publicly available and has been deployed in practice for five years, protecting tens of thousands of users.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507535", "vector": [], "sparse_vector": [], "title": "NVMKV: A Scalable, Lightweight, FTL-aware Key-Value Store.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Key-value stores are ubiquitous in high performance data-intensive, scale out, and NoSQL environments. Many KV stores use flash devices for meeting their performance needs. However, by using flash as a simple block device, these KV stores are unable to fully leverage the powerful capabilities that exist within Flash Translation Layers (FTLs). NVMKV is a lightweight KV store that leverages native FTL capabilities such as sparse addressing, dynamic mapping, transactional persistence, and support for high-levels of lock free parallelism. Our evaluation of NVMKV demonstrates that it provides scalable, high-performance, and ACID compliant KV operations at close to raw device speeds.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507536", "vector": [], "sparse_vector": [], "title": "Selectively Taming Background Android Apps to Improve Battery Lifetime.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Background activities on mobile devices can cause significant battery drain with little visibility or recourse to the user. They can range from useful but sometimes overly aggressive tasks, such as polling for messages or updates from sensors and online services, to outright bugs that cause resources to be held unnecessarily. In this paper we instrument the Android OS to characterize background activities\nthat prevent the device from sleeping. We present TAMER, an OS mechanism that interposes on events and signals that cause task wakeups, and allows for their detailed monitoring, filtering, and rate-limiting. We demonstrate how TAMERcan help reduce battery drain in scenarios involving popular Android apps with background tasks. We also show how TAMERcan mitigate the effects of well-known energy bugs while maintaining most of the apps’ functionality. Finally, we elaborate on how developers and users can devise their own application-control policies for TAMERto maximize battery lifetime.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507537", "vector": [], "sparse_vector": [], "title": "Lightweight Application-Level Crash Consistency on Transactional Flash Storage.", "authors": ["Chang<PERSON><PERSON> Min", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Young Ik Eom"], "summary": "Applications implement their own update protocols to ensure consistency of data on the file system. However, since current file systems provide only a preliminary ordering guarantee, notablyfsync(), these update protocols become complex, slow, and error-prone. We present a new file system, CFS, that supports a native interface for applications to maintain crash consistency of their data. Using CFS, applications can achieve crash consistency of data by declaring code regions that must operate atomically. By utilizing transactional flash storage (SSD/X-FTL), CFS implement a lightweight mechanism for crash consistency. Without using any heavyweight mechanisms based on redundant writes and ordering, CFS can atomically write multiple data pages and their relevant metadata to storage. We made three technical contributions to develop a crash consistency interface with SSD/X-FTL in CFS: selective atomic propagation of dirty pages, in-memory metadata logging, and delayed deallocation. Our evaluation of five real-world applications shows that CFS-based applications significantly outperform ordering versions: 2–5x faster by reducing disk writes 1.9–4.1x and disk cache flushing 1.1–17.6x. Importantly, our porting effort is minimal: CFS requires 317 lines of modifications from 3.5 million lines of ported applications.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507538", "vector": [], "sparse_vector": [], "title": "U-root: A Go-based, Firmware Embeddable Root File System with On-demand Compilation.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "U-root is an embeddable root file system intended to be placed in a FLASH device as part of the firmware image, along with a Linux kernel. The program source code is installed in the root file system contained in the firmware FLASH part and compiled on demand. All the u-root utilities, roughly corresponding to standard Unix utilities, are written in Go, a modern, type-safe language with garbage collection and language-level support for concurrency and inter-process communication. Unlike most embedded root file systems, which consist largely of binaries, U-root has only five: an init program and 4 Go compiler binaries. When a program is first run, it and any not-yet-built packages it uses are compiled to a RAM-based file system. The first invocation of a program takes a fraction of a second, as it is compiled. Packages are only compiled once, so the slowest build is always the first one, on boot, which takes about 3 seconds. Subsequent invocations are very fast, usually a millisecond or so. U-root blurs the line between script-based distros such as Perl Linux and binary-based distros such as BusyBox; it has the flexibility of Perl Linux and the performance of BusyBox. Scripts and builtins are written in Go, not a shell scripting language. U-root is a new way to package and distribute file systems for embedded systems, and the use of Go promises a dramatic improvement in their security.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507539", "vector": [], "sparse_vector": [], "title": "Latency-Tolerant Software Distributed Shared Memory.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Grappa, a modern take on software distributed shared memory (DSM) for in-memory data-intensive applications. Grappa enables users to program a cluster as if it were a single, large, non-uniform memory access (NUMA) machine. Performance scales up even for applications that have poor locality and input-dependent load distribution. Grappa addresses deficiencies of previous DSM systems by exploiting application parallelism, trading off latency for throughput. We evaluate Grappa with an in-memory MapReduce framework (10x faster than Spark); a vertex-centric framework inspired by GraphLab (1.33x faster than native GraphLab); and a relational query execution engine (12.5x faster than Shark). All these frameworks required only 60-690 lines of Grappa code.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507540", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON>: Accurate Record-and-Replay for HTTP.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents Mahimahi, a framework to record traffic from HTTP-based applications, and later replay it under emulated network conditions. <PERSON><PERSON><PERSON> improves upon prior record-and-replay frameworks in three ways. First, it is more accurate because it carefully emulates themulti-servernature of Web applications, present in 98% of the Alexa US Top 500 Web pages. Second, itisolatesits own network traffic, allowing multiple Mahimahi instances emulating different networks to run concurrently without mutual interference. And third, it is designed as a set ofcomposable shells,providing ease-of-use and extensibility. We evaluate <PERSON><PERSON><PERSON> by: (1) analyzing the performance of HTTP/1.1, SPDY, and QUIC on a corpus of 500 sites, (2) using <PERSON><PERSON><PERSON> to understand the reasons why these protocols are suboptimal, (3) developing Cumulus, a cloud-based browser designed to overcome these problems, using <PERSON><PERSON><PERSON> both to implement Cumulus by extending one of its shells, and to evaluate it, (4) using <PERSON><PERSON><PERSON> to evaluate HTTP multiplexing protocols on multiple performance metrics (page load time and speed index), and (5) describing how others have used <PERSON><PERSON><PERSON>.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507541", "vector": [], "sparse_vector": [], "title": "Establishing a Base of Trust with Performance Counters for Enterprise Workloads.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Understanding the performance of large, complex enterprise-class applications is an important, yet nontrivial task. Methods using hardware performance counters, such as profiling through event-based sampling, are often favored over instrumentation for analyzing such large codes, but rarely provide good accuracy at the instruction level. This work evaluates the accuracy of multiple event-based sampling techniques and quantifies the impact of a range of improvements suggested in recent years. The evaluation is performed on instances of three modern CPU architectures, using designated kernels and full applications. We conclude that precisely distributed events considerably improve accuracy, with further improvements possible when using Last Branch Records. We also present practical recommendations for hardware architects, tool developers and performance engineers, aimed at improving the quality of results.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507542", "vector": [], "sparse_vector": [], "title": "Bolt: Faster Reconfiguration in Operating Systems.", "authors": ["Sankaralingam Panneerselvam", "<PERSON>", "<PERSON>"], "summary": "Dynamic resource scaling enables provisioning extra resources during peak loads and saving energy by reclaiming those resources during off-peak times. Scaling the number of CPU cores is particularly valuable as it allows power savings during low-usage periods. Current systems perform scaling with a slowhotplug mechanism,which was primarily designed to remove or replace faulty cores. The high cost of scaling is reflected in power management policies that perform scaling at coarser time scales to amortize the high reconfiguration latency. We describeBolt,a new mechanism built on existing hotplug infrastructure to reduce scaling latency. Bolt also supports a new bulk interface to add or remove multiple cores at once. We implemented Bolt for x86 and ARM architectures. Our evaluation shows that <PERSON>lt can achieve over 20x speedup for entering offline state. While turning on CPUs, Bolt achieves speedup up to 10x and 21x for x86 and ARM respectively.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507543", "vector": [], "sparse_vector": [], "title": "Utilizing the IOMMU Scalably.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "IOMMUs provided by modern hardware allow the OS to enforce memory protection controls on the DMA operations of its I/O devices. An IOMMU translation management design mustscalablyhandle frequent concurrent updates of IOMMU translations made by multiple cores, which occur in high throughput I/O workloads such as multi-Gb/s networking. Today, however, OSes experience performance meltdowns when using the IOMMU in such workloads. This paper exploresscalable IOMMU management designsand addresses the two main bottlenecks we find in current OSes: (1) assignment of I/O virtual addresses (IOVAs), and (2) management of the IOMMU’s TLB. We propose three approaches for scalable IOVA assignment: (1) dynamic identity mappings, which eschew IOVA allocation altogether, (2) allocating IOVAs using the kernel’skmalloc, and (3) per-core caching of IOVAs allocated by a globally-locked IOVA allocator. We further describe a scalable IOMMU TLB management scheme that is compatible with all these approaches. Evaluation of our designs under Linux shows that (1) they achieve 88.5%–100% of the performance obtained without an IOMMU, (2) they achieve similar latency to that obtainedwithoutan IOMMU, (3) scalable IOVA allocation and dynamic identity mappings perform comparably, and (4)kmallocprovides a simple solution with high performance, but can suffer from unbounded page table blowup.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507544", "vector": [], "sparse_vector": [], "title": "Memory-Centric Data Storage for Mobile Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Current data storage on smartphones mostly inherits from desktop/server systems a flash-centric design: The memory (DRAM) effectively acts as an I/O cache for the relatively slow flash. To improve both app responsiveness and energy efficiency, this paper proposes MobiFS, a memory-centric design for smartphone data storage. This design no longer exercises cache writeback at short fixed periods or on file synchronization calls. Instead, it incrementally checkpoints app data into flash at appropriate times, as calculated by a set of app/user-adaptive policies. MobiFS also introduces transactions into the cache to guarantee data consistency. This design trades off data staleness for better app responsiveness and energy efficiency, in a quantitative manner. Evaluations show that MobiFS achieves 18.8× higher write throughput and 11.2× more database transactions per second than the default Ext4 filesystem in Android. Popular real-world apps show improvements in response time and energy consumption by 51.6% and 35.8% on average, respectively.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507545", "vector": [], "sparse_vector": [], "title": "Lamassu: Storage-Efficient Host-Side Encryption.", "authors": ["<PERSON>", "Won So"], "summary": "Many storage customers are adopting encryption solutions to protect critical data. Most existing encryption solutions sit in, or near, the application that is the source of critical data, upstream of the primary storage system. Placing encryption near the source ensures that data remains encrypted throughout the storage stack, making it easier to use untrusted storage, such as public clouds. Unfortunately, such a strategy also prevents downstream storage systems from applying content-based features, such as deduplication, to the data. In this paper, we presentLamassu,an encryption solution that uses block-oriented, host-based, convergent encryption to secure data, while preserving storage-based data deduplication. Unlike past convergent encryption systems, which typically store encryption metadata in a dedicated store, our system transparently inserts its metadata into each file’s data stream. This allows us to add <PERSON>ssu to an application stack without modifying either the client application or the storage controller. In this paper, we lay out the architecture and security model used in our system, and present a new model for maintaining metadata consistency and data integrity in a convergent encryption environment. We also evaluate its storage efficiency and I/O performance by using a variety of microbenchmarks, showing that Lamassu provides excellent storage efficiency, while achieving I/O throughput on par with similar conventional encryption systems.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507546", "vector": [], "sparse_vector": [], "title": "Surviving Peripheral Failures in Embedded Systems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Peripherals fail. Yet, modern embedded systems largely leave the burden of tolerating peripheral failures to the programmer. This paper presents Phoenix, a semi-automated peripheral recovery system for resource-constrained embedded systems. Phoenix introduces lightweight checkpointing mechanisms that transparently track both the internal program state and the external peripheral state. These mechanisms enable rollback to the precise point at which any failed peripheral access occurred using as little as 6 KB of memory, minimizing both recovery latency and memory utilization.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507547", "vector": [], "sparse_vector": [], "title": "Data Sharing or Resource Contention: Toward Performance Transparency on Multicore Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern multicore platforms suffer from inefficiencies due to contention and communication caused by sharing resources or accessing shared data. In this paper, we demonstrate that information from low-cost hardware performance counters commonly available on modern processors is sufficient to identify and separate the causes of communication traffic and performance degradation. We have developed SAM, a Sharing-Aware Mapper that uses the aggregated coherence and bandwidth event counts to separate traffic caused by data sharing from that due to memory accesses. When these counts exceed pre-determined thresholds, SAM effects task to core assignments that colocate tasks that share data and distribute tasks with high demand for cache capacity and memory bandwidth. Our new mapping policies automatically improve execution speed by up to 72% for individual parallel applications compared to the default Linux scheduler, while reducing performance disparities across applications in multiprogrammed workloads.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507548", "vector": [], "sparse_vector": [], "title": "Experience with Rules-Based Programming for Distributed, Concurrent, Fault-Tolerant Code.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper describes how a rules-based approach allowed us to solve a broad class of challenging distributed system problems in the RAMCloud storage system. In the rules-based approach, behavior is described with small sections of code that trigger independently based on system state; this provides a clean separation between the deterministic and nondeterministic parts of an algorithm. To simplify the implementation of rules-based modules, we developed a task abstraction for information hiding and complexity management, pools for grouping tasks and minimizing the cost of rule evaluation, and a polling-based asynchronous RPC system. The rules-based approach is a special case of an event-based state machine, but it encourages a cleaner factoring of code.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507549", "vector": [], "sparse_vector": [], "title": "Secure Deduplication of General Computations.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The world’s fast-growing data has become highly concentrated on enterprise or cloud storage servers. Data deduplication reduces redundancy in this data, saving storage and simplifying management. While existing systems candeduplicate computationson this data by memoizing and reusing computation results, they are insecure, not general, or slow. This paper presents UNIC, a system that securely deduplicates general computations. It exports a cache service that allows applications running on behalf of mutually distrusting users on local or remote hosts to memoize and reuse computation results. Key in UNIC are three new ideas. First, through a novel use of code attestation, UNIC achieves both integrity and secrecy. Second, it provides a simple yet expressive API that enables applications to deduplicate their own rich computations. This design is much more general and flexible than existing systems that can deduplicate only specific types of computations. Third, UNIC explores a cross-layer design that allows the underlying storage system to expose data deduplication information to the applications for better performance. Evaluation of UNIC on four popular open-source applications shows that UNIC is easy to use, fast, and with little storage overhead.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507550", "vector": [], "sparse_vector": [], "title": "SecPod: a Framework for Virtualization-based Security Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The OS kernel is critical to the security of a computer system. Many systems have been proposed to improve its security. A fundamental weakness of those systems is that page tables, the data structures that control the memory protection, are not isolated from the vulnerable kernel, and thus subject to tampering. To address that, researchers have relied on virtualization for reliable kernel memory protection. Unfortunately, such memory protection requires to monitor every update to the guest’s page tables. This fundamentally conflicts with the recent advances in the hardware virtualization support. In this paper, we propose SecPod, an extensible framework for virtualization-based security systems that can provide both strong isolation and the compatibility with modern hardware. SecPod has two key techniques:paging delegationdelegates and audits the kernel’s paging operations to a secure space;execution trappingintercepts the (compromised) kernel’s attempts to subvert SecPod by misusing privileged instructions. We have implemented a prototype of SecPod based on KVM. Our experiments show that SecPod is both effective and efficient.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507551", "vector": [], "sparse_vector": [], "title": "Between Mutual Trust and Mutual Distrust: Practical Fine-grained Privilege Separation in Multithreaded Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Threads in a multithreaded process share the same address space and thus are implicitly assumed to be mutually trusted. However, one (compromised) thread attacking another is a real world threat. It remains challenging to achieve privilege separation for multithreaded applications so that the compromise or malfunction of one thread does not lead to data contamination or data leakage of other threads. The Arbiter system proposed in this paper explores the solution space. In particular, we find that page table protection bits can be leveraged to do efficient reference monitoring if data objects with the same accessibility stay in the same page. We design and implement Arbiter which consists of a new memory allocation mechanism, a policy manager, and a set of APIs. Programmers specify security policy through annotating the source code. We apply Arbiter to three applications, an in-memory key/- value store, a web server, and a userspace file system, and show how they can benefit from Arbiter in terms of security. Our experiments on the three applications show that Arbiter reduces application throughput by less than 10% and increases CPU utilization by 1.37-1.55x.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507552", "vector": [], "sparse_vector": [], "title": "GraphQ: Graph Query Processing with Abstraction Refinement - Scalable and Programmable Analytics over Very Large Graphs on a Single PC.", "authors": ["<PERSON>", "Guo<PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper introducesGraphQ,a scalable querying framework for very large graphs. GraphQ is built on a key insight that many interesting graph properties—such as finding cliques of a certain size, or finding vertices with a certain page rank—can be effectively computed by exploring only a small fraction of the graph, and traversing the complete graph is an overkill. The centerpiece of our framework is the novel idea ofabstraction refinement,where the very large graph is represented as multiple levels of abstractions, and a query is processed through iterative refinement across graph abstraction levels. As a result, GraphQ enjoys several distinctive traits unseen in existing graph processing systems: query processing is naturallybudget-aware,friendly forout-ofcore processingwhen “Big Graphs” cannot entirely fit into memory, and endowed with strong correctness properties on query answers. With GraphQ, a wide range of complexanalytical queriesover very large graphs can be answered with resources affordable toa single PC,which complies with the recent trend advocating singlemachine- based Big Data processing. Experiments show GraphQ can answer queries in graphs 4-6 times bigger than the memory capacity, only in several seconds to minutes. In contrast, GraphChi, a state-of-the-art graph processing system, takes hours to days to compute a whole-graph solution. An additional comparison with a modified version of GraphChi that terminates immediately when a query is answered shows that GraphQ is on average 1.6–13.4x faster due to its ability to process partial graphs.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507553", "vector": [], "sparse_vector": [], "title": "LSM-trie: An LSM-tree-based Ultra-Large Key-Value Store for Small Data Items.", "authors": ["<PERSON><PERSON><PERSON> Wu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Key-value (KV) stores have become a backbone of large- scale applications in today’s data centers. The data set of the store on a single server can grow to billions of KV items or many terabytes, while individual data items are often small (with their values as small as a couple of bytes). It is a daunting task to efficiently organize such an ultra-large KV store to support fast access. Current KV storage systems have one or more of the following inadequacies: (1) very high data write amplifications, (2) large index set, and (3) dramatic degradation of read performance with overspill index out of memory. To address the issue, we propose LSM-trie, a KV storage system that substantially reduces metadata for locating KV items, reduces write amplification by an order of magnitude, and needs only two disk accesses with each KV read even when only less than 10% of meta- data (Bloom filters) can be held in memory. To this end, LSM-trie constructs a trie, or a prefix tree, that stores data in a hierarchical structure and keeps re-organizing them using a compaction method much more efficient than that adopted for LSM-tree. Our experiments show that LSM-trie can improve write and read throughput of LevelDB, a state-of-the-art KV system, by up to 20 times and up to 10 times, respectively.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}, {"primary_key": "4507554", "vector": [], "sparse_vector": [], "title": "GridGraph: Large-Scale Graph Processing on a Single Machine Using 2-Level Hierarchical Partitioning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present GridGraph, a system for processing large-scale graphs on a single machine. Grid- Graph breaks graphs into 1D-partitioned vertex chunks and 2D-partitioned edge blocks using a first fine-grained level partitioning in preprocessing. A second coarsegrained level partitioning is applied in runtime. Through a novel dual sliding windows method, GridGraph can stream the edges and apply on-the-fly vertex updates, thus reduce the I/O amount required for computation. The partitioning of edges also enable selective scheduling so that some of the blocks can be skipped to reduce unnecessary I/O. This is very effective when the active vertex set shrinks with convergence. Our evaluation results show that GridGraph scales seamlessly with memory capacity and disk bandwidth, and outperforms state-of-the-art out-of-core systems, including GraphChi and X-Stream. Furthermore, we show that the performance of GridGraph is even competitive with distributed systems, and it also provides significant cost efficiency in cloud environment.", "published": "2015-01-01", "category": "usenix", "pdf_url": "", "sub_summary": "", "source": "usenix", "doi": ""}]