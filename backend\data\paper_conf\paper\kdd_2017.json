[{"primary_key": "3832374", "vector": [], "sparse_vector": [], "title": "FORA: Simple and Effective Approximate Single-Source Personalized PageRank.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Given a graph G, a source node s and a target node t, the personalized PageRank (PPR) of t with respect to s is the probability that a random walk starting from s terminates at t. A single-source PPR (SSPPR) query enumerates all nodes in G, and returns the top-k nodes with the highest PPR values with respect to a given source node s. SSPPR has important applications in web search and social networks, e.g., in Twitter's Who-To-Follow recommendation service. However, SSPPR computation is immensely expensive, and at the same time resistant to indexing and materialization. So far, existing solutions either use heuristics, which do not guarantee result quality, or rely on the strong computing power of modern data centers, which is costly.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098072"}, {"primary_key": "3832375", "vector": [], "sparse_vector": [], "title": "Constructivism Learning: A Learning Paradigm for Transparent Predictive Analytics.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Developing transparent predictive analytics has attracted significant research attention recently. There have been multiple theories on how to model learning transparency but none of them aims to understand the internal and often complicated modeling processes. In this paper we adopt a contemporary philosophical concept called \"constructivism\", which is a theory regarding how human learns. We hypothesize that a critical aspect of transparent machine learning is to \"reveal\" model construction with two key process: (1) the assimilation process where we enhance our existing learning models and (2) the accommodation process where we create new learning models. With this intuition we propose a new learning paradigm, constructivism learning, using a Bayesian nonparametric model to dynamically handle the creation of new learning tasks. Our empirical study on both synthetic and real data sets demonstrate that the new learning algorithm is capable of delivering higher quality models (as compared to base lines and state-of-the-art) and at the same time increasing the transparency of the learning process.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097994"}, {"primary_key": "3832377", "vector": [], "sparse_vector": [], "title": "Effective Evaluation Using Logged Bandit Feedback from Multiple Loggers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Accurately evaluating new policies (e.g. ad-placement models, ranking functions, recommendation functions) is one of the key prerequisites for improving interactive systems. While the conventional approach to evaluation relies on online A/B tests, recent work has shown that counterfactual estimators can provide an inexpensive and fast alternative, since they can be applied offline using log data that was collected from a different policy fielded in the past. In this paper, we address the question of how to estimate the performance of a new target policy when we have log data from multiple historic policies. This question is of great relevance in practice, since policies get updated frequently in most online systems. We show that naively combining data from multiple logging policies can be highly suboptimal. In particular, we find that the standard Inverse Propensity Score (IPS) estimator suffers especially when logging and target policies diverge -- to a point where throwing away data improves the variance of the estimator. We therefore propose two alternative estimators which we characterize theoretically and compare experimentally. We find that the new estimators can provide substantially improved estimation accuracy.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098155"}, {"primary_key": "3832378", "vector": [], "sparse_vector": [], "title": "Tripoles: A New Class of Relationships in Time Series Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mining relationships in time series data is of immense interest to several disciplines such as neuroscience, climate science, and transportation. Traditional approaches for mining relationships focus on discovering pair-wise relationships in the data. In this work, we define a novel relationship pattern involving three interacting time series, which we refer to as a tripole. We show that tripoles capture interesting relationship patterns in the data that are not possible to be captured using traditionally studied pair-wise relationships. We demonstrate the utility of tripoles in multiple real-world datasets from various domains including climate science and neuroscience. In particular, our approach is able to discover tripoles that are statistically significant, reproducible across multiple independent data sets, and lead to novel domain insights.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098099"}, {"primary_key": "3832379", "vector": [], "sparse_vector": [], "title": "A Practical Algorithm for Solving the Incoherence Problem of Topic Models In Industrial Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Topic models are often applied in industrial settings to discover user profiles from activity logs where documents correspond to users and words to complex objects such as web sites and installed apps. Standard topic models ignore the content-based similarity structure between these objects largely because of the inability of the Dirichlet prior to capture such side information of word-word correlation. Several approaches were proposed to replace the Dirichlet prior with more expressive alternatives. However, this added expressivity comes with a heavy premium: inference becomes intractable and sparsity is lost which renders these alternatives not suitable for industrial scale applications. In this paper we take a radically different approach to incorporating word-word correlation in topic models by applying this side information at the posterior level rather than at the prior level. We show that this choice preserves sparsity and results in a graph-based sampler for LDA whose computational complexity is asymptotically on bar with the state of the art Alias base sampler for LDA \\cite{aliasLDA}. We illustrate the efficacy of our approach over real industrial datasets that span up to billion of users, tens of millions of words and thousands of topics. To the best of our knowledge, our approach provides the first practical and scalable solution to this important problem.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098200"}, {"primary_key": "3832380", "vector": [], "sparse_vector": [], "title": "Using Convolutional Networks and Satellite Imagery to Identify Patterns in Urban Environments at a Large Scale.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Urban planning applications (energy audits, investment, etc.) require an understanding of built infrastructure and its environment, i.e., both low-level, physical features (amount of vegetation, building area and geometry etc.), as well as higher-level concepts such as land use classes (which encode expert understanding of socio-economic end uses). This kind of data is expensive and labor-intensive to obtain, which limits its availability (particularly in developing countries). We analyze patterns in land use in urban neighborhoods using large-scale satellite imagery data (which is available worldwide from third-party providers) and state-of-the-art computer vision techniques based on deep convolutional neural networks. For supervision, given the limited availability of standard benchmarks for remote-sensing data, we obtain ground truth land use class labels carefully sampled from open-source surveys, in particular the Urban Atlas land classification dataset of $20$ land use classes across $~300$ European cities. We use this data to train and compare deep architectures which have recently shown good performance on standard computer vision tasks (image classification and segmentation), including on geospatial data. Furthermore, we show that the deep representations extracted from satellite imagery of urban environments can be used to compare neighborhoods across several cities. We make our dataset available for other machine learning researchers to use for remote-sensing applications.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098070"}, {"primary_key": "3832381", "vector": [], "sparse_vector": [], "title": "Sparse Compositional Local Metric Learning.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Mahalanobis distance metric learning becomes an especially challenging problem as the dimension of the feature space p is scaled upwards. The number of parameters to optimize grows with space complexity of order O (p 2), making storage infeasible, interpretability poor, and causing the model to have a high tendency to overfit. Additionally, optimization while maintaining feasibility of the solution becomes prohibitively expensive, requiring a projection onto the positive semi-definite cone after every iteration. In addition to the obvious space and computational challenges, vanilla distance metric learning is unable to model complex and multi-modal trends in the data. Inspired by the recent resurgence of Frank-Wolfe style optimization, we propose a new method for sparse compositional local Mahalanobis distance metric learning. Our proposed technique learns a set of distance metrics which are composed of local and global components. We capture local interactions in the feature space, while ensuring that all metrics share a global component, which may act as a regularizer. We optimize our model using an alternating pairwise Frank-Wolfe style algorithm. This serves a dual purpose, we can control the sparsity of our solution, and altogether avoid any expensive projection operations. Finally, we conduct an empirical evaluation of our method with the current state of the art and present the results on five datasets from varying domains.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098153"}, {"primary_key": "3832383", "vector": [], "sparse_vector": [], "title": "Machine Learning for Encrypted Malware Traffic Classification: Accounting for Noisy Labels and Non-Stationarity.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The application of machine learning for the detection of malicious network traffic has been well researched over the past several decades; it is particularly appealing when the traffic is encrypted because traditional pattern-matching approaches cannot be used. Unfortunately, the promise of machine learning has been slow to materialize in the network security domain. In this paper, we highlight two primary reasons why this is the case: inaccurate ground truth and a highly non-stationary data distribution. To demonstrate and understand the effect that these pitfalls have on popular machine learning algorithms, we design and carry out experiments that show how six common algorithms perform when confronted with real network data. With our experimental results, we identify the situations in which certain classes of algorithms underperform on the task of encrypted malware traffic classification. We offer concrete recommendations for practitioners given the real-world constraints outlined. From an algorithmic perspective, we find that the random forest ensemble method outperformed competing methods. More importantly, feature engineering was decisive; we found that iterating on the initial feature set, and including features suggested by domain experts, had a much greater impact on the performance of the classification system. For example, linear regression using the more expressive feature set easily outperformed the random forest method using a standard network traffic representation on all criteria considered. Our analysis is based on millions of TLS encrypted sessions collected over 12 months from a commercial malware sandbox and two geographically distinct, large enterprise networks.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098163"}, {"primary_key": "3832384", "vector": [], "sparse_vector": [], "title": "Learning Certifiably Optimal Rule Lists.", "authors": ["<PERSON>", "<PERSON>-Stone", "<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "We present the design and implementation of a custom discrete optimization technique for building rule lists over a categorical feature space. Our algorithm provides the optimal solution, with a certificate of optimality. By leveraging algorithmic bounds, efficient data structures, and computational reuse, we achieve several orders of magnitude speedup in time and a massive reduction of memory consumption. We demonstrate that our approach produces optimal rule lists on practical problems in seconds. This framework is a novel alternative to CART and other decision tree methods.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098047"}, {"primary_key": "3832385", "vector": [], "sparse_vector": [], "title": "Post Processing Recommender Systems for Diversity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Collaborative filtering is a broad and powerful framework for building recommendation systems that has seen widespread adoption. Over the past decade, the propensity of such systems for favoring popular products and thus creating echo chambers have been observed. This has given rise to an active area of research that seeks to diversify recommendations generated by such algorithms. We address the problem of increasing diversity in recom- mendation systems that are based on collaborative filtering that use past ratings to predict a rating quality for potential recommendations. Following our earlier work, we formulate recommendation system design as a subgraph selection problem from a candidate super-graph of potential recommendations where both diversity and rating quality are explicitly optimized: (1) On the modeling side, we define a new flexible notion of diversity that allows a system designer to prescribe the number of recommendations each item should receive, and smoothly penalizes deviations from this distribution. (2) On the algorithmic side, we show that minimum-cost network flow methods yield fast algorithms in theory and practice for designing recommendation subgraphs that optimize this notion of diversity. (3) On the empirical side, we show the effectiveness of our new model and method to increase diversity while maintaining high rating quality in standard rating data sets from Netflix and MovieLens.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098173"}, {"primary_key": "3832386", "vector": [], "sparse_vector": [], "title": "Luck is Hard to Beat: The Difficulty of Sports Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Pedro O. S. Vaz de Mel<PERSON>"], "summary": "Predicting the outcome of sports events is a hard task. We quantify this difficulty with a coefficient that measures the distance between the observed final results of sports leagues and idealized perfectly balanced competitions in terms of skill. This indicates the relative presence of luck and skill. We collected and analyzed all games from 198 sports leagues comprising 1503 seasons from 84 countries of 4 different sports: basketball, soccer, volleyball and handball. We measured the competitiveness by countries and sports. We also identify in each season which teams, if removed from its league, result in a completely random tournament. Surprisingly, not many of them are needed. As another contribution of this paper, we propose a probabilistic graphical model to learn about the teams' skills and to decompose the relative weights of luck and skill in each game. We break down the skill component into factors associated with the teams' characteristics. The model also allows to estimate as 0.36 the probability that an underdog team wins in the NBA league, with a home advantage adding 0.09 to this probability. As shown in the first part of the paper, luck is substantially present even in the most competitive championships, which partially explains why sophisticated and complex feature-based models hardly beat simple models in the task of forecasting sports' outcomes.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098045"}, {"primary_key": "3832388", "vector": [], "sparse_vector": [], "title": "Improved Degree Bounds and Full Spectrum Power Laws in Preferential Attachment Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Consider a random preferential attachment model G(p) for network evolution that allows both node and edge arrivals. Starting with an arbitrary nonempty graph G0, at each time step, there are two possible events: with probability p > 0 a new node arrives and a new edge is added between the new node and an existing node, and with probability 1 - p a new edge is added between two existing nodes. In both cases, the involved existing nodes are chosen at random according to preferential attachment, i.e., with probability proportional to their degree. G(p) is known to generate power law networks, i.e., the fraction of nodes with degree k is proportional to k-β. Here β=(4-p)/(2-p) is in the range (2,3].", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098012"}, {"primary_key": "3832389", "vector": [], "sparse_vector": [], "title": "Unsupervised Network Discovery for Brain Imaging Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A common problem with spatiotemporal data is how to simplify the data to discover an underlying network that consists of cohesive spatial regions (nodes) and relationships between those regions (edges). This network discovery problem naturally exists in a multitude of domains including climate data (dipoles), astronomical data (gravitational lensing) and the focus of this paper, fMRI scans of human subjects. Whereas previous work requires strong supervision, we propose an unsupervised matrix tri-factorization formulation with complex constraints and spatial regularization. We show that this formulation works well in controlled experiments with synthetic networks and is able to recover the underlying ground-truth network. We then show that for real fMRI data our approach can reproduce well known results in neurology regarding the default mode network in resting-state healthy and Alzheimer affected individuals.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098023"}, {"primary_key": "3832390", "vector": [], "sparse_vector": [], "title": "Planning Bike Lanes based on Sharing-Bikes&apos; Trajectories.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yanhua Li", "<PERSON>"], "summary": "Cycling as a green transportation mode has been promoted by many governments all over the world. As a result, constructing effective bike lanes has become a crucial task for governments promoting the cycling life style, as well-planned bike paths can reduce traffic congestion and decrease safety risks for both cyclists and motor vehicle drivers. Unfortunately, existing trajectory mining approaches for bike lane planning do not consider key realistic government constraints: 1) budget limitations, 2) construction convenience, and 3) bike lane utilization.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098056"}, {"primary_key": "3832391", "vector": [], "sparse_vector": [], "title": "Aspect Based Recommendations: Recommending Items with the Most Valuable Aspects Based on User Reviews.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we propose a recommendation technique that not only can recommend items of interest to the user as traditional recommendation systems do but also specific aspects of consumption of the items to further enhance the user experience with those items. For example, it can recommend the user to go to a specific restaurant (item) and also order some specific foods there, e.g., seafood (an aspect of consumption). Our method is called Sentiment Utility Logistic Model (SULM). As its name suggests, SULM uses sentiment analysis of user reviews. It first predicts the sentiment that the user may have about the item based on what he/she might express about the aspects of the item and then identifies the most valuable aspects of the user's potential experience with that item. Furthermore, the method can recommend items together with those most important aspects over which the user has control and can potentially select them, such as the time to go to a restaurant, e.g. lunch vs. dinner, and what to order there, e.g., seafood. We tested the proposed method on three applications (restaurant, hotel, and beauty & spa) and experimentally showed that those users who followed our recommendations of the most valuable aspects while consuming the items, had better experiences, as defined by the overall rating.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098170"}, {"primary_key": "3832392", "vector": [], "sparse_vector": [], "title": "TFX: A TensorFlow-Based Production-Scale Machine Learning Platform.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Salem Haykal", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lu<PERSON><PERSON> Lew", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Creating and maintaining a platform for reliably producing and deploying machine learning models requires careful orchestration of many components---a learner for generating models based on training data, modules for analyzing and validating both data as well as models, and finally infrastructure for serving models in production. This becomes particularly challenging when data changes over time and fresh models need to be produced continuously. Unfortunately, such orchestration is often done ad hoc using glue code and custom scripts developed by individual teams for specific use cases, leading to duplicated effort and fragile systems with high technical debt.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098021"}, {"primary_key": "3832393", "vector": [], "sparse_vector": [], "title": "Patient Subtyping via Time-Aware LSTM Networks.", "authors": ["Inci M. Baytas", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the study of various diseases, heterogeneity among patients usually leads to different progression patterns and may require different types of therapeutic intervention. Therefore, it is important to study patient subtyping, which is grouping of patients into disease characterizing subtypes. Subtyping from complex patient data is challenging because of the information heterogeneity and temporal dynamics. Long-Short Term Memory (LSTM) has been successfully used in many domains for processing sequential data, and recently applied for analyzing longitudinal patient records. The LSTM units are designed to handle data with constant elapsed times between consecutive elements of a sequence. Given that time lapse between successive elements in patient records can vary from days to months, the design of traditional LSTM may lead to suboptimal performance. In this paper, we propose a novel LSTM unit called Time-Aware LSTM (T-LSTM) to handle irregular time intervals in longitudinal patient records. We learn a subspace decomposition of the cell memory which enables time decay to discount the memory content according to the elapsed time. We propose a patient subtyping model that leverages the proposed T-LSTM in an auto-encoder to learn a powerful single representation for sequential records of patients, which are then used to cluster patients into clinical subtypes. Experiments on synthetic and real world datasets show that the proposed T-LSTM architecture captures the underlying structures in the sequences with time irregularities.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097997"}, {"primary_key": "3832394", "vector": [], "sparse_vector": [], "title": "Mining Big Data in NeuroGenetics to Understand Muscular Dystrophy.", "authors": ["<PERSON>"], "summary": "The recent advances in genome sequencing and analyses of the billions of base pairs in genomic data have been a boon for moving forward our understanding of human disease. In this talk I will describe how genome sequencing has dramatically improved our understanding of the most common adult form of muscular dystrophy, which is myotonic dystrophy. Two different genetic mutations cause thousands of changes in the cells and tissues of myotonic dystrophy patients. Genome sequencing has allowed us to precisely determine the degree of changes across patients, correlate these changes to disease symptoms and allow us to determine quickly in cell and animal models the effectiveness of therapeutic strategies for myotonic dystrophy.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105813"}, {"primary_key": "3832395", "vector": [], "sparse_vector": [], "title": "Extremely Fast Decision Tree Mining for Evolving Data Streams.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Nowadays real-time industrial applications are generating a huge amount of data continuously every day. To process these large data streams, we need fast and efficient methodologies and systems. A useful feature desired for data scientists and analysts is to have easy to visualize and understand machine learning models. Decision trees are preferred in many real-time applications for this reason, and also, because combined in an ensemble, they are one of the most powerful methods in machine learning.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098139"}, {"primary_key": "3832396", "vector": [], "sparse_vector": [], "title": "Bolt: Accelerated Data Mining with Fast Vector Compression.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Vectors of data are at the heart of machine learning and data mining. Recently, vector quantization methods have shown great promise in reducing both the time and space costs of operating on vectors. We introduce a vector quantization algorithm that can compress vectors over 12x faster than existing techniques while also accelerating approximate vector operations such as distance and dot product computations by up to 10x. Because it can encode over 2GB of vectors per second, it makes vector quantization cheap enough to employ in many more circumstances. For example, using our technique to compute approximate dot products in a nested loop can multiply matrices faster than a state-of-the-art BLAS implementation, even when our algorithm must first compress the matrices. In addition to showing the above speedups, we demonstrate that our approach can accelerate nearest neighbor search and maximum inner product search by over 100x compared to floating point operations and up to 10x compared to other vector quantization methods. Our approximate Euclidean distance and dot product computations are not only faster than those of related algorithms with slower encodings, but also faster than Hamming distance computations, which have direct hardware support on the tested platforms. We also assess the errors of our algorithm's approximate distances and dot products, and find that it is competitive with existing, slower vector quantization algorithms.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098195"}, {"primary_key": "3832397", "vector": [], "sparse_vector": [], "title": "Industrial Machine Learning.", "authors": ["<PERSON>"], "summary": "The ongoing digitization of the industrial-scale machines that power and enable human activity is itself a major global transformation. But the real revolution-in efficiencies, in improved and saved lives-will happen as machine learning automation and insights are properly coupled to the complex systems of industrial data. Leveraging a systems view of real-world use cases from aviation to transportation, I contrast the needs and approaches of consumer versus industrial machine learning. Particularly, I focus on three key areas: combining physics-based models to data-driven models, differential privacy and secure ML (including edge-to-cloud strategies), and interpretability of model predictions.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105817"}, {"primary_key": "3832398", "vector": [], "sparse_vector": [], "title": "Robust Spectral Clustering for Noisy Data: Modeling Sparse Corruptions Improves Latent Embeddings.", "authors": ["<PERSON>ek<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Spectral clustering is one of the most prominent clustering approaches. However, it is highly sensitive to noisy input data. In this work, we propose a robust spectral clustering technique able to handle such scenarios. To achieve this goal, we propose a sparse and latent decomposition of the similarity graph used in spectral clustering. In our model, we jointly learn the spectral embedding as well as the corrupted data - thus, enhancing the clustering performance overall. We propose algorithmic solutions to all three established variants of spectral clustering, each showing linear complexity in the number of edges. Our experimental analysis confirms the significant potential of our approach for robust spectral clustering. Supplementary material is available at www.kdd.in.tum.de/RSC.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098156"}, {"primary_key": "3832399", "vector": [], "sparse_vector": [], "title": "LiJAR: A System for Job Application Redistribution towards Efficient Career Marketplace.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Online professional social networks such as LinkedIn serve as a marketplace, wherein job seekers can find right career opportunities and job providers can reach out to potential candidates. LinkedIn's job recommendations product is a key vehicle for efficient matching between potential candidates and job postings. However, we have observed in practice that a subset of job postings receive too many applications (due to several reasons such as the popularity of the company, nature of the job, etc.), while some other job postings receive too few applications. Both cases can result in job poster dissatisfaction and may lead to discontinuation of the associated job posting contracts. At the same time, if too many job seekers compete for the same job posting, each job seeker's chance of getting this job will be reduced. In the long term, this reduces the chance of users finding jobs that they really like on the site. Therefore, it becomes beneficial for the job recommendation system to consider values provided to both job seekers as well as job posters in the marketplace.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098028"}, {"primary_key": "3832400", "vector": [], "sparse_vector": [], "title": "Behavior Informatics to Discover Behavior Insight for Active and Tailored Client Management.", "authors": ["<PERSON><PERSON> Cao"], "summary": "Behavior is ubiquitous, and behavior intelligence and insight play an important role in data understanding and business problem-solving. Behavior Informatics [1,2] emerges as an important tool for discovering behavior intelligence and behavior insight. As a computational concept, behavior captures the aspects of the demographics of behavioral subjects and objects; social relationships or norms governing the interactions between behaviors of an individual or a group; behavior sequences or networks and their dynamics; and the impact or effect generated by the behaviors undertaken by subjects on objects. Accordingly, a behavior model [2] captures the subject and the object of a behavior or behavior sequence, the activities conducted by its subject on objects, and the relationships between activities; behavior subject, object, activities and relationships are characterized by their respective attributes. As a result, a behavior is represented as a behavior attributes-based vector; and a subject's behaviors at a time period form a vector-based sequence, namely, represented as a behavior attribute vector-based matrix [3]. With such behavior modeling and from the informatics perspective, behavior informatics takes a top-down approach to systematically and deeply represent, model, reason about, and aggregate behaviors [4]; and a bottom-up approach to analyze and learn behavior occurrences, non-occurrences, dynamics, impact, and utility [2].", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105818"}, {"primary_key": "3832402", "vector": [], "sparse_vector": [], "title": "DeepMood: Modeling Mobile Phone Typing Dynamics for Mood Detection.", "authors": ["Bokai Cao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The increasing use of electronic forms of communication presents new opportunities in the study of mental health, including the ability to investigate the manifestations of psychiatric diseases unobtrusively and in the setting of patients' daily lives. A pilot study to explore the possible connections between bipolar affective disorder and mobile phone usage was conducted. In this study, participants were provided a mobile phone to use as their primary phone. This phone was loaded with a custom keyboard that collected metadata consisting of keypress entry time and accelerometer movement. Individual character data with the exceptions of the backspace key and space bar were not collected due to privacy concerns. We propose an end-to-end deep architecture based on late fusion, named DeepMood, to model the multi-view metadata for the prediction of mood scores. Experimental results show that 90.31% prediction accuracy on the depression score can be achieved based on session-level mobile phone typing dynamics which is typically less than one minute. It demonstrates the feasibility of using mobile phone metadata to infer mood disturbance and severity.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098086"}, {"primary_key": "3832403", "vector": [], "sparse_vector": [], "title": "Real-Time Optimization of Web Publisher RTB Revenues.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper describes an engine to optimize web publisher revenues from second-price auctions. These auctions are widely used to sell online ad spaces in a mechanism called real-time bidding (RTB). Optimization within these auctions is crucial for web publishers, because setting appropriate reserve prices can significantly increase revenue. We consider a practical real-world setting where the only available information before an auction occurs consists of a user identifier and an ad placement identifier. The real-world challenges we had to tackle consist mainly of tracking the dependencies on both the user and placement in an highly non-stationary environment and of dealing with censored bid observations. These challenges led us to make the following design choices: (i) we adopted a relatively simple non-parametric regression model of auction revenue based on an incremental time-weighted matrix factorization which implicitly builds adaptive users' and placements' profiles; (ii) we jointly used a non-parametric model to estimate the first and second bids' distribution when they are censored, based on an on-line extension of the Aalen's Additive model. Our engine is a component of a deployed system handling hundreds of web publishers across the world, serving billions of ads a day to hundreds of millions of visitors. The engine is able to predict, for each auction, an optimal reserve price in approximately one millisecond and yields a significant revenue increase for the web publishers.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098150"}, {"primary_key": "3832404", "vector": [], "sparse_vector": [], "title": "Customer Lifetime Value Prediction Using Embeddings.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We describe the Customer LifeTime Value (CLTV) prediction system deployed at ASOS.com, a global online fashion retailer. CLTV prediction is an important problem in e-commerce where an accurate estimate of future value allows retailers to effectively allocate marketing spend, identify and nurture high value customers and mitigate exposure to losses. The system at ASOS provides daily estimates of the future value of every customer and is one of the cornerstones of the personalised shopping experience. The state of the art in this domain uses large numbers of handcrafted features and ensemble regressors to forecast value, predict churn and evaluate customer loyalty. Recently, domains including language, vision and speech have shown dramatic advances by replacing handcrafted features with features that are learned automatically from data. We detail the system deployed at ASOS and show that learning feature representations is a promising extension to the state of the art in CLTV modelling. We propose a novel way to generate embeddings of customers, which addresses the issue of the ever changing product catalogue and obtain a significant improvement over an exhaustive set of handcrafted features.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098123"}, {"primary_key": "3832405", "vector": [], "sparse_vector": [], "title": "Robust Top-k Multiclass SVM for Visual Category Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Classification problems with a large number of classes inevitably involve overlapping or similar classes. In such cases it seems reasonable to allow the learning algorithm to make mistakes on similar classes, as long as the true class is still among the top-k (say) predictions. Likewise, in applications such as search engine or ad display, we are allowed to present k predictions at a time and the customer would be satisfied as long as her interested prediction is included. Inspired by the recent work of [15], we propose a very generic, robust multiclass SVM formulation that directly aims at minimizing a weighted and truncated combination of the ordered prediction scores. Our method includes many previous works as special cases. Computationally, using the Jordan decomposition Lemma we show how to rewrite our objective as the difference of two convex functions, based on which we develop an efficient algorithm that allows incorporating many popular regularizers (such as the l2 and l1 norms). We conduct extensive experiments on four real large-scale visual category recognition datasets, and obtain very promising performances.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097991"}, {"primary_key": "3832408", "vector": [], "sparse_vector": [], "title": "Fast Newton Hard Thresholding Pursuit for Sparsity Constrained Nonconvex Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a fast Newton hard thresholding pursuit algorithm for sparsity constrained nonconvex optimization. Our proposed algorithm reduces the per-iteration time complexity to linear in the data dimension d compared with cubic time complexity in <PERSON>'s method, while preserving faster computational and statistical convergence rates. In particular, we prove that the proposed algorithm converges to the unknown sparse model parameter at a composite rate, namely quadratic at first and linear when it gets close to the true parameter, up to the minimax optimal statistical precision of the underlying model. <PERSON><PERSON> experiments on both synthetic and real datasets demonstrate that our algorithm outperforms the state-of-the-art optimization algorithms for sparsity constrained optimization.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098165"}, {"primary_key": "3832409", "vector": [], "sparse_vector": [], "title": "On Sampling Strategies for Neural Network-based Collaborative Filtering.", "authors": ["<PERSON><PERSON>", "Yizhou Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent advances in neural networks have inspired people to design hybrid recommendation algorithms that can incorporate both (1) user-item interaction information and (2) content information including image, audio, and text. Despite their promising results, neural network-based recommendation algorithms pose extensive computational costs, making it challenging to scale and improve upon. In this paper, we propose a general neural network-based recommendation framework, which subsumes several existing state-of-the-art recommendation algorithms, and address the efficiency issue by investigating sampling strategies in the stochastic gradient descent training for the framework. We tackle this issue by first establishing a connection between the loss functions and the user-item interaction bipartite graph, where the loss function terms are defined on links while major computation burdens are located at nodes. We call this type of loss functions \"graph-based\" loss functions, for which varied mini-batch sampling strategies can have different computational costs. Based on the insight, three novel sampling strategies are proposed, which can significantly improve the training efficiency of the proposed framework (up to $\\times 30$ times speedup in our experiments), as well as improving the recommendation performance. Theoretical analysis is also provided for both the computational cost and the convergence. We believe the study of sampling strategies have further implications on general graph-based loss functions, and would also enable more research under the neural network-based recommendation framework.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098202"}, {"primary_key": "3832410", "vector": [], "sparse_vector": [], "title": "KATE: K-Competitive Autoencoder for Text.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Autoencoders have been successful in learning meaningful representations from image datasets. However, their performance on text datasets has not been widely studied. Traditional autoencoders tend to learn possibly trivial representations of text documents due to their confoundin properties such as high-dimensionality, sparsity and power-law word distributions. In this paper, we propose a novel k-competitive autoencoder, called KATE, for text documents. Due to the competition between the neurons in the hidden layer, each neuron becomes specialized in recognizing specific data patterns, and overall the model can learn meaningful representations of textual data. A comprehensive set of experiments show that KATE can learn better representations than traditional autoencoders including denoising, contractive, variational, and k-sparse autoencoders. Our model also outperforms deep generative models, probabilistic topic models, and even word representation models (e.g., Word2Vec) in terms of several downstream tasks such as document classification, regression, and retrieval.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098017"}, {"primary_key": "3832411", "vector": [], "sparse_vector": [], "title": "TensorFlow Estimators: Managing Simplicity vs. Flexibility in High-Level Machine Learning Frameworks.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>chan Hong", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Yuan Tang", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a framework for specifying, training, evaluating, and deploying machine learning models. Our focus is on simplifying cutting edge machine learning for practitioners in order to bring such technologies into production. Recognizing the fast evolution of the field of deep learning, we make no attempt to capture the design space of all possible model architectures in a domain- specific language (DSL) or similar configuration language. We allow users to write code to define their models, but provide abstractions that guide develop- ers to write models in ways conducive to productionization. We also provide a unifying Estimator interface, making it possible to write downstream infrastructure (e.g. distributed training, hyperparameter tuning) independent of the model implementation. We balance the competing demands for flexibility and simplicity by offering APIs at different levels of abstraction, making common model architectures available out of the box, while providing a library of utilities designed to speed up experimentation with model architectures. To make out of the box models flexible and usable across a wide range of problems, these canned Estimators are parameterized not only over traditional hyperparameters, but also using feature columns, a declarative specification describing how to interpret input data. We discuss our experience in using this framework in re- search and production environments, and show the impact on code health, maintainability, and development speed.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098171"}, {"primary_key": "3832412", "vector": [], "sparse_vector": [], "title": "Unsupervised Feature Selection in Signed Social Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON>"], "summary": "The rapid growth of social media services brings a large amount of high-dimensional social media data at an unprecedented rate. Feature selection is powerful to prepare high-dimensional data by finding a subset of relevant features. A vast majority of existing feature selection algorithms for social media data exclusively focus on positive interactions among linked instances such as friendships and user following relations. However, in many real-world social networks, instances may also be negatively interconnected. Recent work shows that negative links have an added value over positive links in advancing many learning tasks. In this paper, we study a novel problem of unsupervised feature selection in signed social networks and propose a novel framework SignedFS. In particular, we provide a principled way to model positive and negative links for user latent representation learning. Then we embed the user latent representations into feature selection when label information is not available. Also, we revisit the principle of homophily and balance theory in signed social networks and incorporate the signed graph regularization into the feature selection framework to capture the first-order and the second-order proximity among users in signed social networks. Experiments on two real-world signed social networks demonstrate the effectiveness of our proposed framework. Further experiments are conducted to understand the impacts of different components of SignedFS.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098106"}, {"primary_key": "3832413", "vector": [], "sparse_vector": [], "title": "GRAM: Graph-based Attention Model for Healthcare Representation Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Sun"], "summary": "Deep learning methods exhibit promising performance for predictive modeling in healthcare, but two important challenges remain: -", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098126"}, {"primary_key": "3832414", "vector": [], "sparse_vector": [], "title": "A Data Science Approach to Understanding Residential Water Contamination in Flint.", "authors": ["<PERSON>", "Chengyu Dai", "<PERSON><PERSON>", "Guangsha Shi", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "When the residents of Flint learned that lead had contaminated their water system, the local government made water-testing kits available to them free of charge. The city government published the results of these tests, creating a valuable dataset that is key to understanding the causes and extent of the lead contamination event in Flint. This is the nation's largest dataset on lead in a municipal water system. In this paper, we predict the lead contamination for each household's water supply, and we study several related aspects of Flint's water troubles, many of which generalize well beyond this one city. For example, we show that elevated lead risks can be (weakly) predicted from observable home attributes. Then we explore the factors associated with elevated lead. These risk assessments were developed in part via a crowd sourced prediction challenge at the University of Michigan. To inform Flint residents of these assessments, they have been incorporated into a web and mobile application funded by \\texttt{Google.org}. We also explore questions of self-selection in the residential testing program, examining which factors are linked to when and how frequently residents voluntarily sample their water.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098078"}, {"primary_key": "3832415", "vector": [], "sparse_vector": [], "title": "A Minimal Variance Estimator for the Cardinality of Big Data Set Intersection.", "authors": ["<PERSON><PERSON><PERSON> Cohen", "<PERSON><PERSON>", "Aviv Yehezkel"], "summary": "In recent years there has been a growing interest in developing \"streaming algorithms\" for efficient processing and querying of continuous data streams. These algorithms seek to provide accurate results while minimizing the required storage and the processing time, at the price of a small inaccuracy in their output. A fundamental query of interest is the intersection size of two big data streams. This problem arises in many different application areas, such as network monitoring, database systems, data integration and information retrieval. In this paper we develop a new algorithm for this problem, based on the Maximum Likelihood (ML) method. We show that this algorithm outperforms all known schemes in terms of the estimation's quality (lower variance) and that it asymptotically achieves the optimal variance.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097999"}, {"primary_key": "3832416", "vector": [], "sparse_vector": [], "title": "HyperLogLog Hyperextended: Sketches for Concave Sublinear Frequency Statistics.", "authors": ["<PERSON>"], "summary": "One of the most common statistics computed over data elements is the number of distinct keys. A thread of research pioneered by <PERSON><PERSON><PERSON><PERSON> and <PERSON> three decades ago culminated in the design of optimal approximate counting sketches, which have size that is double logarithmic in the number of distinct keys and provide estimates with a small relative error. Moreover, the sketches are composable, and thus suitable for streamed, parallel, or distributed computation.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098020"}, {"primary_key": "3832417", "vector": [], "sparse_vector": [], "title": "Fast Enumeration of Large k-Plexes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "K-plexes are a formal yet flexible way of defining communities in networks. They generalize the notion of cliques and are more appropriate in most real cases: while a node of a clique C is connected to all other nodes of C, a node of a k-plex may miss up to k connections. Unfortunately, computing all maximal k-plexes is a gruesome task and state-of-the-art algorithms can only process small-size networks. In this paper we propose a new approach for enumerating large k-plexes in networks that speeds up the search by several orders of magnitude, leveraging on (i) methods for strongly reducing the search space and (ii) efficient techniques for the computation of maximal cliques. Several experiments show that our strategy is effective and is able to increase the size of the networks for which the computation of large k-plexes is feasible from a few hundred to several hundred thousand nodes.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098031"}, {"primary_key": "3832418", "vector": [], "sparse_vector": [], "title": "Algorithmic Decision Making and the Cost of Fairness.", "authors": ["<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Algorithms are now regularly used to decide whether defendants awaiting trial are too dangerous to be released back into the community. In some cases, black defendants are substantially more likely than white defendants to be incorrectly classified as high risk. To mitigate such disparities, several techniques have recently been proposed to achieve algorithmic fairness. Here we reformulate algorithmic fairness as constrained optimization: the objective is to maximize public safety while satisfying formal fairness constraints designed to reduce racial disparities. We show that for several past definitions of fairness, the optimal algorithms that result require detaining defendants above race-specific risk thresholds. We further show that the optimal unconstrained algorithm requires applying a single, uniform threshold to all defendants. The unconstrained algorithm thus maximizes public safety while also satisfying one important understanding of equality: that all individuals are held to the same standard, irrespective of race. Because the optimal constrained and unconstrained algorithms generally differ, there is tension between improving public safety and satisfying prevailing notions of algorithmic fairness. By examining data from Broward County, Florida, we show that this trade-off can be large in practice. We focus on algorithms for pretrial release decisions, but the principles we discuss apply to other domains, and also to human decision makers carrying out structured decision rules.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098095"}, {"primary_key": "3832420", "vector": [], "sparse_vector": [], "title": "Estimation of Recent Ancestral Origins of Individuals on a Large Scale.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The last ten years have seen an exponential growth of direct-to-consumer genomics. One popular feature of these tests is the report of a distant ancestral inference profile-a breakdown of the regions of the world where the test-taker's ancestors may have lived. While current methods and products generally focus on the more distant past (e.g., thousands of years ago), we have recently demonstrated that by leveraging network analysis tools such as community detection, more recent ancestry can be identified. However, using a network analysis tool like community detection on a large network with potentially millions of nodes is not feasible in a live production environment where hundreds or thousands of new genotypes are processed every day. In this study, we describe a classification method that leverages network features to assign individuals to communities in a large network corresponding to recent ancestry. We recently launched a beta version of this research as a new product feature at AncestryDNA.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098042"}, {"primary_key": "3832421", "vector": [], "sparse_vector": [], "title": "Learning Tree-Structured Detection Cascades for Heterogeneous Networks of Embedded Devices.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present a new approach to learning cascaded classifiers for use in computing environments that involve networks of heterogeneous and resource-constrained, low-power embedded compute and sensing nodes. We present a generalization of the classical linear detection cascade to the case of tree-structured cascades where different branches of the tree execute on different physical compute nodes in the network. Different nodes have access to different features, as well as access to potentially different computation and energy resources. We concentrate on the problem of jointly learning the parameters for all of the classifiers in the cascade given a fixed cascade architecture and a known set of costs required to carry out the computation at each node.To accomplish the objective of joint learning of all detectors, we propose a novel approach to combining classifier outputs during training that better matches the hard cascade setting in which the learned system will be deployed. This work is motivated by research in the area of mobile health where energy efficient real time detectors integrating information from multiple wireless on-body sensors and a smart phone are needed for real-time monitoring and delivering just- in-time adaptive interventions. We apply our framework to two activity recognition datasets as well as the problem of cigarette smoking detection from a combination of wrist-worn actigraphy data and respiration chest band data.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098169"}, {"primary_key": "3832422", "vector": [], "sparse_vector": [], "title": "Matrix Profile V: A Generic Technique to Incorporate Domain Knowledge into Motif Discovery.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Time series motif discovery has emerged as perhaps the most used primitive for time series data mining, and has seen applications to domains as diverse as robotics, medicine and climatology. There has been recent significant progress on the scalability of motif discovery. However, we believe that the current definitions of motif discovery are limited, and can create a mismatch between the user's intent/expectations, and the motif discovery search outcomes. In this work, we explain the reasons behind these issues, and introduce a novel and general framework to address them. Our ideas can be used with current state-of-the-art algorithms with virtually no time or space overhead, and are fast enough to allow real-time interaction and hypotheses testing on massive datasets. We demonstrate the utility of our ideas on domains as diverse as seismology and epileptic seizure monitoring.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097993"}, {"primary_key": "3832423", "vector": [], "sparse_vector": [], "title": "AESOP: Automatic Policy Learning for Predicting and Mitigating Network Service Impairments.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>e", "<PERSON><PERSON><PERSON>", "Sarat C. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Efficient management and control of modern and next-gen networks is of paramount importance as networks have to maintain highly reliable service quality whilst supporting rapid growth in traffic demand and new application services. Rapid mitigation of network service degradations is a key factor in delivering high service quality. Automation is vital to achieving rapid mitigation of issues, particularly at the network edge where the scale and diversity is the greatest. This automation involves the rapid detection, localization and (where possible) repair of service-impacting faults and performance impairments. However, the most significant challenge here is knowing what events to detect, how to correlate events to localize an issue and what mitigation actions should be performed in response to the identified issues. These are defined as policies to systems such as ECOMP.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098157"}, {"primary_key": "3832424", "vector": [], "sparse_vector": [], "title": "It Takes More than Math and Engineering to Hit the Bullseye with Data.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Adopting algorithmic decision-making in a large and complex enterprise such as a Fortune 50 retailer like Target takes much more than clean, reliable data and great data mining capabilities. Yet data practitioners too often start with advanced math and fancy algorithms, rather than working hand-in-hand with business partners to identify and understand the biggest business problems. (Then teams should move onto how algorithms can be applied to those problems.) Another key step for data scientists at large organizations: ensuring that their business partners -- the merchants, marketers and supply chain experts -- have a base-line understanding of advanced models as well as the proper analytical support tools. Obtaining widespread buy-in and enthusiasm also requires providing a user-friendly interface for business partners with optionality and flexibility that allows the intelligence to be applied to the many varied issues facing a modern retailer, from personalization to supply chain transformation to decisions on assortment and pricing. This talk will explore effective practices and processes -- the do's and don'ts -- for data scientists to succeed in large, complex organizations like a retailer with 1,800+ stores, major marketing campaigns across multiple channels and a fast growing online business.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105816"}, {"primary_key": "3832427", "vector": [], "sparse_vector": [], "title": "A Dirty Dozen: Twelve Common Metric Interpretation Pitfalls in Online Controlled Experiments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online controlled experiments (e.g., A/B tests) are now regularly used to guide product development and accelerate innovation in software. Product ideas are evaluated as scientific hypotheses, and tested in web sites, mobile applications, desktop applications, services, and operating systems. One of the key challenges for organizations that run controlled experiments is to come up with the right set of metrics [1] [2] [3]. Having good metrics, however, is not enough.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098024"}, {"primary_key": "3832428", "vector": [], "sparse_vector": [], "title": "metapath2vec: Scalable Representation Learning for Heterogeneous Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the problem of representation learning in heterogeneous networks. Its unique challenges come from the existence of multiple types of nodes and links, which limit the feasibility of the conventional network embedding techniques. We develop two scalable representation learning models, namely metapath2vec and metapath2vec++. The metapath2vec model formalizes meta-path-based random walks to construct the heterogeneous neighborhood of a node and then leverages a heterogeneous skip-gram model to perform node embeddings. The metapath2vec++ model further enables the simultaneous modeling of structural and semantic correlations in heterogeneous networks. Extensive experiments show that metapath2vec and metapath2vec++ are able to not only outperform state-of-the-art embedding models in various heterogeneous network mining tasks, such as node classification, clustering, and similarity search, but also discern the structural and semantic correlations between diverse network objects.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098036"}, {"primary_key": "3832429", "vector": [], "sparse_vector": [], "title": "Structural Diversity and Homophily: A Study Across More Than One Hundred Big Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A widely recognized organizing principle of networks is structural homophily, which suggests that people with more common neighbors are more likely to connect with each other. However, what influence the diverse structures embedded in common neighbors have on link formation is much less well-understood. To explore this problem, we begin by characterizing the structural diversity of common neighborhoods. Using a collection of 120 large-scale networks, we demonstrate that the impact of the common neighborhood diversity on link existence can vary substantially across networks. We find that its positive effect on Facebook and negative effect on LinkedIn suggest different underlying networking needs in these networks. We also discover striking cases where diversity violates the principle of homophily---that is, where fewer mutual connections may lead to a higher tendency to link with each other. We then leverage structural diversity to develop a common neighborhood signature (CNS), which we apply to a large set of networks to uncover unique network superfamilies not discoverable by conventional methods. Our findings shed light on the pursuit to understand the ways in which network structures are organized and formed, pointing to potential advancement in designing graph generation models and recommender systems.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098116"}, {"primary_key": "3832430", "vector": [], "sparse_vector": [], "title": "A Century of Science: Globalization of Scientific Collaborations, Citations, and Innovations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Progress in science has advanced the development of human society across history, with dramatic revolutions shaped by information theory, genetic cloning, and artificial intelligence, among the many scientific achievements produced in the 20th century. However, the way that science advances itself is much less well-understood. In this work, we study the evolution of scientific development over the past century by presenting an anatomy of 89 million digitalized papers published between 1900 and 2015. We find that science has benefited from the shift from individual work to collaborative effort, with over 90% of the world-leading innovations generated by collaborations in this century, nearly four times higher than they were in the 1900s. We discover that rather than the frequent myopic- and self-referencing that was common in the early 20th century, modern scientists instead tend to look for literature further back and farther around. Finally, we also observe the globalization of scientific development from 1900 to 2015, including 25-fold and 7-fold increases in international collaborations and citations, respectively, as well as a dramatic decline in the dominant accumulation of citations by the US, the UK, and Germany, from ~95% to ~50% over the same period. Our discoveries are meant to serve as a starter for exploring the visionary ways in which science has developed throughout the past century, generating insight into and an impact upon the current scientific innovations and funding policies.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098016"}, {"primary_key": "3832431", "vector": [], "sparse_vector": [], "title": "FIRST: Fast Interactive Attributed Subgraph Matching.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Hanghang Tong"], "summary": "Attributed subgraph matching is a powerful tool for explorative mining of large attributed networks. In many applications (e.g., network science of teams, intelligence analysis, finance informatics), the user might not know what exactly s/he is looking for, and thus require the user to constantly revise the initial query graph based on what s/he finds from the current matching results. A major bottleneck in such an interactive matching scenario is the efficiency, as simply rerunning the matching algorithm on the revised query graph is computationally prohibitive. In this paper, we propose a family of effective and efficient algorithms (FIRST) to support interactive attributed subgraph matching. There are two key ideas behind the proposed methods. The first is to recast the attributed subgraph matching problem as a cross-network node similarity problem, whose major computation lies in solving a Sylvester equation for the query graph and the underlying data graph. The second key idea is to explore the smoothness between the initial and revised queries, which allows us to solve the new/updated Sylvester equation incrementally, without re-solving it from scratch. Experimental results show that our method can achieve (1) up to 16x speed-up when applying on networks with 6M$+$ nodes; (2) preserving more than 90% accuracy compared with existing methods; and (3) scales linearly with respect to the size of the data graph.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098040"}, {"primary_key": "3832432", "vector": [], "sparse_vector": [], "title": "What&apos;s Fair?", "authors": ["<PERSON>"], "summary": "Data, algorithms, and systems have biases embedded within them reflecting designers' explicit and implicit choices, historical biases, and societal priorities. They form, literally and inexorably, a codification of values. \"Unfairness\" of algorithms -- for tasks ranging from advertising to recidivism prediction -- has attracted considerable attention in the popular press. The talk will discuss the nascent mathematically rigorous study of fairness in classification and scoring.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105807"}, {"primary_key": "3832433", "vector": [], "sparse_vector": [], "title": "Revisiting Power-law Distributions in Spectra of Real World Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "By studying a large number of real world graphs, we find empirical evidence that most real world graphs have a statistically significant power-law distribution with a cutoff in the singular values of the adjacency matrix and eigenvalues of the Laplacian matrix in addition to the commonly conjectured power-law in the degrees. Among these results, power-laws in the singular values appear more consistently than in the degree distribution. The exponents of the power-law distributions are much larger than previously observed. We find a surprising direct relationship between the power-law in the degree distribution and the power-law in the eigenvalues of the Laplacian that was theorized in simple models but is extremely accurate in practice. We investigate these findings in large networks by studying the cutoff value itself, which shows a scaling law for the number of elements involved in these power-laws. Using the scaling law enables us to compute only a subset of eigenvalues of large networks, up to tens of millions of vertices and billions of edges, where we find that those too show evidence of statistically significant power-laws.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098128"}, {"primary_key": "3832434", "vector": [], "sparse_vector": [], "title": "Prognosis and Diagnosis of Parkinson&apos;s Disease Using Multi-Task Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Parkinson's disease (PD) is a debilitating neurodegenerative disease excessively affecting millions of patients. Early diagnosis of PD is critical as manifestation of symptoms occur many years after the onset of neurodegenration, when more than 60\\% of dopaminergic neurons are lost. Since there is no definite diagnosis of PD, the early management of disease is a significant challenge in the field of PD therapeutics. Therefore, identifying valid biomarkers that can characterize the progression of PD has lately received growing attentions in PD research community. In this paper, we employ a multi-task learning regression framework for prediction of Parkinson's disease progression, where each task is the prediction of PD rating scales at one future time point. We then use the model to identify the important biomarkers predictive of disease progression. We adopt a graph regularization approach to capture the relationship between different tasks and penalize large variations of the model at consecutive future time points. We have carried out comprehensive experiments using different categories of measurements at baseline from Parkinson's Progression Markers Initiative (PPMI) database to predict the severity of PD, measured by unified PD rating scale. We use the learned model to identify the biomarkers with significant contribution in prediction of PD progression. Our results confirm some of the important biomarkers identified in existing medical studies, validate some of the biomarkers that have been observed as a potential marker of PD and discover new biomarkers that have not yet been investigated.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098065"}, {"primary_key": "3832435", "vector": [], "sparse_vector": [], "title": "Ego-Splitting Framework: from Non-Overlapping to Overlapping Clusters.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose ego-splitting, a new framework for detecting clusters in complex networks which leverage the local structures known as ego-nets (i.e. the subgraph induced by the neighborhood of each node) to de-couple overlapping clusters. Ego-splitting is a highly scalable and flexible framework, with provable theoretical guarantees, that reduces the complex overlapping clustering problem to a simpler and more amenable non-overlapping (partitioning) problem. We can scale community detection to graphs with tens of billions of edges and outperform previous solutions based on ego-nets analysis.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098054"}, {"primary_key": "3832436", "vector": [], "sparse_vector": [], "title": "Benchmarks and Process Management in Data Science: Will We Ever Get Over the Mess?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> Rubi<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "This panel aims to address areas that are widely acknowledged to be of critical importance to the success of Data Science projects and to the healthy growth of KDD/Data Science as a field of scientific research. However, despite this acknowledgement of their criticality, these areas receive insufficient attention in the major conferences in the field. Furthermore, there is a lack of actual actions and tools to address these areas in actual practice. These areas are summarized as follows:", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3120998"}, {"primary_key": "3832437", "vector": [], "sparse_vector": [], "title": "Foreword to the Applied Data Science: Invited Talks Track at KDD-2017.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Applied Data Science (ADS) Invited Talks Track at KDD-2017 is a continuation of what has now become a \"7-year tradition\" at KDD conferences. This is the second year the track operates under the ADS name, an evolution from its origins at KDD-2011 as the \"Industry Practice Expo\". The KDD Conference on Knowledge Discovery and Data Mining (KDD) is the world's first, largest and best conference on Data Science, Data Mining, and Knowledge Discovery. It brings together a healthy mix of academic researchers, industry and government researchers, and practitioners from a wide range of institutions and fields. The primary focus on KDD is on peer-reviewed research contributions and the academic advancement of the field. This is an important goal and in fact the KDD conference is now recognized as the most competitive and prestigious forum for presenting high quality research results. KDD, being fundamentally an applied field, needs the strong representation of applied work of big impact. Over the years of running the conference we observed that our initial speaker-selection approach needed to be re-thought because of the important contributions made to the field outside traditional academic, industrial and government research laboratories. The result of this re-thinking was to create a forum that exposes important contributions to Data Science through Big Data Applications that address strategic problems. We wanted to effectively capture the rising importance of Data Science and Machine Learning especially in the Big Data environment where structured and unstructured data create special challenges, and of course present new opportunities. The goal of the Invited Talks Track is to curate contributions from leaders in our field who have made important contributions through the development of a system, the creation of a new and important business, or the development and market introduction of a product,. Some of these important contributions may never see an academic paper or detailed peer-reviewed paper written about them, yet they are of critical importance to our very applied field. To give you an idea of how rapidly growing this area is, and how this sector of our industry and promises to be highly disruptive across many industries, we cite a couple of articles out of a plethora of such coverage: According to IDC, the global revenues from Big Data and business will grow from $130.1 billion in 2016 to more than $203 billion in 2020, at a compound annual growth rate (CAGR) of 11.7% [1]. Furthermore, to quote from a Forbes article: \"Data monetization\" will become a major source of revenues, as the world will create 180 zettabytes of data (or 180 trillion gigabytes) in 2025, up from less than 10 zettabytes in 2015.? [2]", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3121426"}, {"primary_key": "3832439", "vector": [], "sparse_vector": [], "title": "Contextual Motifs: Increasing the Utility of Motifs using Contextual Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Rodica Pop-<PERSON>ui", "<PERSON>"], "summary": "Motifs are a powerful tool for analyzing physiological waveform data. Standard motif methods, however, ignore important contextual information (e.g., what the patient was doing at the time the data were collected). We hypothesize that these additional contextual data could increase the utility of motifs. Thus, we propose an extension to motifs, contextual motifs, that incorporates context. Recognizing that, oftentimes, context may be unobserved or unavailable, we focus on methods to jointly infer motifs and context. Applied to both simulated and real physiological data, our proposed approach improves upon existing motif methods in terms of the discriminative utility of the discovered motifs. In particular, we discovered contextual motifs in continuous glucose monitor (CGM) data collected from patients with type 1 diabetes. Compared to their contextless counterparts, these contextual motifs led to better predictions of hypo- and hyperglycemic events. Our results suggest that even when inferred, context is useful in both a long- and short-term prediction horizon when processing and interpreting physiological waveform data.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098068"}, {"primary_key": "3832440", "vector": [], "sparse_vector": [], "title": "REMIX: Automated Exploration for Interactive Outlier Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ak <PERSON>", "<PERSON>"], "summary": "Outlier detection is the identification of points in a dataset that do not conform to the norm. Outlier detection is highly sensitive to the choice of the detection algorithm and the feature subspace used by the algorithm. Extracting domain-relevant insights from outliers needs systematic exploration of these choices since diverse outlier sets could lead to complementary insights. This challenge is especially acute in an interactive setting, where the choices must be explored in a time-constrained manner. In this work, we present REMIX, the first system to address the problem of outlier detection in an interactive setting. REMIX uses a novel mixed integer programming (MIP) formulation for automatically selecting and executing a diverse set of outlier detectors within a time limit. This formulation incorporates multiple aspects such as (i) an upper limit on the total execution time of detectors (ii) diversity in the space of algorithms and features, and (iii) meta-learning for evaluating the cost and utility of detectors. REMIX provides two distinct ways for the analyst to consume its results: (i) a partitioning of the detectors explored by REMIX into perspectives through low-rank non-negative matrix factorization; each perspective can be easily visualized as an intuitive heatmap of experiments versus outliers, and (ii) an ensembled set of outliers which combines outlier scores from all detectors. We demonstrate the benefits of REMIX through extensive empirical validation on real-world data.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098154"}, {"primary_key": "3832441", "vector": [], "sparse_vector": [], "title": "Unsupervised P2P Rental Recommendations via Integer Programming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Due to the sparseness of quality rating data, unsupervised recommender systems are used in many applications in Peer to Peer (P2P) rental marketplaces such as Airbnb, FlipKey, and HomeAway. We present an integer programming based recommender systems, where both accommodation benefits and community risks of lodging places are measured and incorporated into an objective function as utility measurements. More specifically, we first present an unsupervised fused scoring method for quantifying the accommodation benefits and community risks of a lodging with crowd-sourced geo-tagged data. In order to the utility of recommendations, we formulate the unsupervised P2P rental recommendations as a constrained integer programming problem, where the accommodation benefits of recommendations are maximized and the community risks of recommendations are minimized, while maintaining constraints on personalization. Furthermore, we provide an efficient solution for the optimization problem by developing a learning-to-integer-programming method for combining aggregated listwise learning to rank into branching variable selection. We apply the proposed approach to the Airbnb data of New York City and provide lodging recommendations to travelers. In our empirical experiments, we demonstrate both the efficiency and effectiveness of our method in terms of striving a trade-off between the user satisfaction, time on market, and the number of reviews, and achieving a balance between positive and negative sides.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098044"}, {"primary_key": "3832442", "vector": [], "sparse_vector": [], "title": "Anarchists, Unite: Practical Entropy Approximation for Distributed Streams.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Entropy is a fundamental property of data and a key metric in many scientific and engineering fields. Entropy estimation has been extensively studied, but almost always under the assumption that there is a single data stream, seen in its entirety by one node running the estimation algorithm. Multiple distributed data sources are becoming increasingly common, however, with applications in signal processing, computer science, medicine, physics, and more. Centralizing all data can be infeasible, for example in networks of battery or bandwidth limited sensors, so entropy estimation in distributed streams requires new, communication-efficient approaches.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098092"}, {"primary_key": "3832443", "vector": [], "sparse_vector": [], "title": "A Data Mining Framework for Valuing Large Portfolios of Variable Annuities.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "A variable annuity is a tax-deferred retirement vehicle created to address concerns that many people have about outliving their assets. In the past decade, the rapid growth of variable annuities has posed great challenges to insurance companies especially when it comes to valuing the complex guarantees embedded in these products.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098013"}, {"primary_key": "3832444", "vector": [], "sparse_vector": [], "title": "GELL: Automatic Extraction of Epidemiological Line Lists from Open Sources.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Real-time monitoring and responses to emerging public health threats rely on the availability of timely surveillance data. During the early stages of an epidemic, the ready availability of line lists with detailed tabular information about laboratory-confirmed cases can assist epidemiologists in making reliable inferences and forecasts. Such inferences are crucial to understand the epidemiology of a specific disease early enough to stop or control the outbreak. However, construction of such line lists requires considerable human supervision and therefore, difficult to generate in real-time. In this paper, we motivate Guided Epidemiological Line List (GELL), the first tool for building automated line lists (in near real-time) from open source reports of emerging disease outbreaks. Specifically, we focus on deriving epidemiological characteristics of an emerging disease and the affected population from reports of illness. GELL uses distributed vector representations (ala word2vec) to discover a set of indicators for each line list feature. This discovery of indicators is followed by the use of dependency parsing based techniques for final extraction in tabular form. We evaluate the performance of GELL against a human annotated line list provided by HealthMap corresponding to MERS outbreaks in Saudi Arabia. We demonstrate that GELL extracts line list features with increased accuracy compared to a baseline method. We further show how these automatically extracted line list features can be used for making epidemiological inferences, such as inferring demographics and symptoms-to-hospitalization period of affected individuals.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098073"}, {"primary_key": "3832445", "vector": [], "sparse_vector": [], "title": "Automated Categorization of Onion Sites for Analyzing the Darkweb Ecosystem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Onion sites on the darkweb operate using the Tor Hidden Service (HS) protocol to shield their locations on the Internet, which (among other features) enables these sites to host malicious and illegal content while being resistant to legal action and seizure. Identifying and monitoring such illicit sites in the darkweb is of high relevance to the Computer Security and Law Enforcement communities. We have developed an automated infrastructure that crawls and indexes content from onion sites into a large-scale data repository, called LIGHTS, with over 100M pages. In this paper we describe Automated Tool for Onion Labeling (ATOL), a novel scalable analysis service developed to conduct a thematic assessment of the content of onion sites in the LIGHTS repository. ATOL has three core components -- (a) a novel keyword discovery mechanism (ATOLKeyword) which extends analyst-provided keywords for different categories by suggesting new descriptive and discriminative keywords that are relevant for the categories; (b) a classification framework (ATOLClassify) that uses the discovered keywords to map onion site content to a set of categories when sufficient labeled data is available; (c) a clustering framework (ATOLCluster) that can leverage information from multiple external heterogeneous knowledge sources, ranging from domain expertise to Bitcoin transaction data, to categorize onion content in the absence of sufficient supervised data. The paper presents empirical results of ATOL on onion datasets derived from the LIGHTS repository, and additionally benchmarks ATOL's algorithms on the publicly available 20 Newsgroups dataset to demonstrate the reproducibility of its results. On the LIGHTS dataset, ATOLClassify gives a 12% performance gain over an analyst-provided baseline, while ATOLCluster gives a 7% improvement over state-of-the-art semi-supervised clustering algorithms. We also discuss how ATOL has been deployed and externally evaluated, as part of the LIGHTS system.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098193"}, {"primary_key": "3832446", "vector": [], "sparse_vector": [], "title": "Google Vizier: A Service for Black-Box Optimization.", "authors": ["<PERSON>", "<PERSON>", "Subho<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Any sufficiently complex system acts as a black box when it becomes easier to experiment with than to understand. Hence, black-box optimization has become increasingly important as systems have become more complex. In this paper we describe Google Vizier, a Google-internal service for performing black-box optimization that has become the de facto parameter tuning engine at Google. Google Vizier is used to optimize many of our machine learning models and other systems, and also provides core capabilities to Google's Cloud Machine Learning HyperTune subsystem. We discuss our requirements, infrastructure design, underlying algorithms, and advanced features such as transfer learning and automated early stopping that the service provides.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098043"}, {"primary_key": "3832447", "vector": [], "sparse_vector": [], "title": "Predicting Clinical Outcomes Across Changing Electronic Health Record Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Existing machine learning methods typically assume consistency in how semantically equivalent information is encoded. However, the way information is recorded in databases differs across institutions and over time, often rendering potentially useful data obsolescent. To address this problem, we map database-specific representations of information to a shared set of semantic concepts, thus allowing models to be built from or transition across different databases. We demonstrate our method on machine learning models developed in a healthcare setting. In particular, we evaluate our method using two different intensive care unit (ICU) databases and on two clinically relevant tasks, in-hospital mortality and prolonged length of stay. For both outcomes, a feature representation mapping EHR-specific events to a shared set of clinical concepts yields better results than using EHR-specific events alone.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098064"}, {"primary_key": "3832449", "vector": [], "sparse_vector": [], "title": "Groups-Keeping Solution Path Algorithm for Sparse Regression with Automatic Feature Grouping.", "authors": ["<PERSON>", "<PERSON><PERSON> Liu", "<PERSON><PERSON>"], "summary": "Feature selection is one of the most important data mining research topics with many applications. In practical problems, features often have group structure to effect the outcomes. Thus, it is crucial to automatically identify homogenous groups of features for high-dimensional data analysis. Octagonal shrinkage and clustering algorithm for regression (OSCAR) is an important sparse regression approach with automatic feature grouping and selection by ℓ1 norm and pairwise ℓ∞ norm. However, due to over-complex representation of the penalty (especially the pairwise ℓ∞ norm), so far OSCAR has no solution path algorithm which is mostly useful for tuning the model. To address this challenge, in this paper, we propose a groups-keeping solution path algorithm to solve the OSCAR model (OscarGKPath). Given a set of homogenous groups of features and an accuracy bound ε, OscarGKPath can fit the solutions in an interval of regularization parameters while keeping the feature groups. The entire solution path can be obtained by combining multiple such intervals. We prove that all solutions in the solution path produced by <PERSON><PERSON><PERSON>Path can strictly satisfy the given accuracy bound ε. The experimental results on benchmark datasets not only confirm the effectiveness of our OscarGKPath algorithm, but also show the superiority of our OscarGKPath in cross validation compared with the existing batch algorithm.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098010"}, {"primary_key": "3832450", "vector": [], "sparse_vector": [], "title": "The Co-Evolution Model for Social Network Evolving and Opinion Migration.", "authors": ["<PERSON><PERSON><PERSON>", "Yizhou Sun", "Jianxi Gao"], "summary": "Almost all real-world social networks are dynamic and evolving with time, where new links may form and old links may drop, largely determined by the homophily of social actors (i.e., nodes in the network). Meanwhile, (latent) properties of social actors, such as their opinions, are changing along the time, partially due to social influence received from the network, which will in turn affect the network structure. Social network evolution and node property migration are usually treated as two orthogonal problems, and have been studied separately. In this paper, we propose a co-evolution model that closes the loop by modeling the two phenomena together, which contains two major components: (1) a network generative model when the node property is known; and (2) a property migration model when the social network structure is known. Simulation shows that our model has several nice properties: (1) it can model a broad range of phenomena such as opinion convergence (i.e., herding) and community-based opinion divergence; and (2) it allows to control the evolution via a set of factors such as social influence scope, opinion leader, and noise level. Finally, the usefulness of our model is demonstrated by an application of co-sponsorship prediction for legislative bills in Congress, which outperforms several state-of-the-art baselines.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098002"}, {"primary_key": "3832451", "vector": [], "sparse_vector": [], "title": "Clustering Individual Transactional Data for Masses of Users.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mining a large number of datasets recording human activities for making sense of individual data is the key enabler of a new wave of personalized knowledge-based services. In this paper we focus on the problem of clustering individual transactional data for a large mass of users. Transactional data is a very pervasive kind of information that is collected by several services, often involving huge pools of users. We propose txmeans, a parameter-free clustering algorithm able to efficiently partitioning transactional data in a completely automatic way. Txmeans is designed for the case where clustering must be applied on a massive number of different datasets, for instance when a large set of users need to be analyzed individually and each of them has generated a long history of transactions. A deep experimentation on both real and synthetic datasets shows the practical effectiveness of txmeans for the mass clustering of different personal datasets, and suggests that txmeans outperforms existing methods in terms of quality and efficiency. Finally, we present a personal cart assistant application based on txmeans", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098034"}, {"primary_key": "3832452", "vector": [], "sparse_vector": [], "title": "Network Inference via the Time-Varying Graphical Lasso.", "authors": ["<PERSON>", "Youngsuk Park", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many important problems can be modeled as a system of interconnected entities, where each entity is recording time-dependent observations or measurements. In order to spot trends, detect anomalies, and interpret the temporal dynamics of such data, it is essential to understand the relationships between the different entities and how these relationships evolve over time. In this paper, we introduce the time-varying graphical lasso (TVGL), a method of inferring time-varying networks from raw time series data. We cast the problem in terms of estimating a sparse time-varying inverse covariance matrix, which reveals a dynamic network of interdependencies between the entities. Since dynamic network inference is a computationally expensive task, we derive a scalable message-passing algorithm based on the Alternating Direction Method of Multipliers (ADMM) to solve this problem in an efficient way. We also discuss several extensions, including a streaming algorithm to update the model and incorporate new observations in real time. Finally, we evaluate our TVGL algorithm on both real and synthetic datasets, obtaining interpretable results and outperforming state-of-the-art baselines in terms of both accuracy and scalability.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098037"}, {"primary_key": "3832453", "vector": [], "sparse_vector": [], "title": "Toeplitz Inverse Covariance-Based Clustering of Multivariate Time Series Data.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Subsequence clustering of multivariate time series is a useful tool for discovering repeated patterns in temporal data. Once these patterns have been discovered, seemingly complicated datasets can be interpreted as a temporal sequence of only a small number of states, or clusters. For example, raw sensor data from a fitness-tracking application can be expressed as a timeline of a select few actions (i.e., walking, sitting, running). However, discovering these patterns is challenging because it requires simultaneous segmentation and clustering of the time series. Furthermore, interpreting the resulting clusters is difficult, especially when the data is high-dimensional. Here we propose a new method of model-based clustering, which we call Toeplitz Inverse Covariance-based Clustering (TICC). Each cluster in the TICC method is defined by a correlation network, or Markov random field (MRF), characterizing the interdependencies between different observations in a typical subsequence of that cluster. Based on this graphical representation, TICC simultaneously segments and clusters the time series data. We solve the TICC problem through alternating minimization, using a variation of the expectation maximization (EM) algorithm. We derive closed-form solutions to efficiently solve the two resulting subproblems in a scalable way, through dynamic programming and the alternating direction method of multipliers (ADMM), respectively. We validate our approach by comparing TICC to several state-of-the-art baselines in a series of synthetic experiments, and we then demonstrate on an automobile sensor dataset how TICC can be used to learn interpretable clusters in real-world scenarios.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098060"}, {"primary_key": "3832454", "vector": [], "sparse_vector": [], "title": "Toward Automated Fact-Checking: Detecting Check-worthy Factual Claims by <PERSON>laimBuster.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chengkai Li", "<PERSON>"], "summary": "This paper introduces how ClaimBuster, a fact-checking platform, uses natural language processing and supervised learning to detect important factual claims in political discourses. The claim spotting model is built using a human-labeled dataset of check-worthy factual claims from the U.S. general election debate transcripts. The paper explains the architecture and the components of the system and the evaluation of the model. It presents a case study of how ClaimBuster live covers the 2016 U.S. presidential election debates and monitors social media and Australian Hansard for factual claims. It also describes the current status and the long-term goals of ClaimBuster as we keep developing and expanding it.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098131"}, {"primary_key": "3832455", "vector": [], "sparse_vector": [], "title": "Efficient Correlated Topic Modeling with Topic Embedding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Correlated topic modeling has been limited to small model and problem sizes due to their high computational cost and poor scaling. In this paper, we propose a new model which learns compact topic embeddings and captures topic correlations through the closeness between the topic vectors. Our method enables efficient inference in the low-dimensional embedding space, reducing previous cubic or quadratic time complexity to linear w.r.t the topic size. We further speedup variational inference with a fast sampler to exploit sparsity of topic occurrence. Extensive experiments show that our approach is capable of handling model and data scales which are several orders of magnitude larger than existing correlation results, without sacrificing modeling quality by providing competitive or superior performance in document classification and retrieval.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098074"}, {"primary_key": "3832456", "vector": [], "sparse_vector": [], "title": "An Efficient Bandit Algorithm for Realtime Multivariate Optimization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>"], "summary": "Optimization is commonly employed to determine the content of web pages, such as to maximize conversions on landing pages or click-through rates on search engine result pages. Often the layout of these pages can be decoupled into several separate decisions. For example, the composition of a landing page may involve deciding which image to show, which wording to use, what color background to display, etc. Such optimization is a combinatorial problem over an exponentially large decision space. Randomized experiments do not scale well to this setting, and therefore, in practice, one is typically limited to optimizing a single aspect of a web page at a time. This represents a missed opportunity in both the speed of experimentation and the exploitation of possible interactions between layout decisions. Here we focus on multivariate optimization of interactive web pages. We formulate an approach where the possible interactions between different components of the page are modeled explicitly. We apply bandit methodology to explore the layout space efficiently and use hill-climbing to select optimal content in realtime. Our algorithm also extends to contextualization and personalization of layout selection. Simulation results show the suitability of our approach to large decision spaces with strong interactions between content. We further apply our algorithm to optimize a message that promotes adoption of an Amazon service. After only a single week of online optimization, we saw a 21% conversion increase compared to the median layout. Our technique is currently being deployed to optimize content across several locations at Amazon.com.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098184"}, {"primary_key": "3832457", "vector": [], "sparse_vector": [], "title": "Accelerating Innovation Through Analogy Mining.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The availability of large idea repositories (e.g., the U.S. patent database) could significantly accelerate innovation and discovery by providing people with inspiration from solutions to analogous problems. However, finding useful analogies in these large, messy, real-world repositories remains a persistent challenge for either human or automated methods. Previous approaches include costly hand-created databases that have high relational structure (e.g., predicate calculus representations) but are very sparse. Simpler machine-learning/information-retrieval similarity metrics can scale to large, natural-language datasets, but struggle to account for structural similarity, which is central to analogy. In this paper we explore the viability and value of learning simpler structural representations, specifically, \"problem schemas\", which specify the purpose of a product and the mechanisms by which it achieves that purpose. Our approach combines crowdsourcing and recurrent neural networks to extract purpose and mechanism vector representations from product descriptions. We demonstrate that these learned vectors allow us to find analogies with higher precision and recall than traditional information-retrieval methods. In an ideation experiment, analogies retrieved by our models significantly increased people's likelihood of generating creative ideas compared to analogies retrieved by traditional methods. Our results suggest a promising approach to enabling computational analogy at scale is to learn and leverage weaker structural representations.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098038"}, {"primary_key": "3832458", "vector": [], "sparse_vector": [], "title": "Recurrent Poisson Factorization for Temporal Recommendation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Hongyuan Zha", "<PERSON><PERSON>"], "summary": "Poisson factorization is a probabilistic model of users and items for recommendation systems, where the so-called implicit consumer data is modeled by a factorized Poisson distribution. There are many variants of Poisson factorization methods who show state-of-the-art performance on real-world recommendation tasks. However, most of them do not explicitly take into account the temporal behavior and the recurrent activities of users which is essential to recommend the right item to the right user at the right time. In this paper, we introduce Recurrent Poisson Factorization (RPF) framework that generalizes the classical PF methods by utilizing a Poisson process for modeling the implicit feedback. RPF treats time as a natural constituent of the model and brings to the table a rich family of time-sensitive factorization models. To elaborate, we instantiate several variants of RPF who are capable of handling dynamic user preferences and item specification (DRPF), modeling the social-aspect of product adoption (SRPF), and capturing the consumption heterogeneity among users and items (HRPF). We also develop a variational algorithm for approximate posterior inference that scales up to massive data sets. Furthermore, we demonstrate RPF's superior performance over many state-of-the-art methods on synthetic dataset, and large scale real-world datasets on music streaming logs, and user-item interactions in M-Commerce platforms.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098197"}, {"primary_key": "3832459", "vector": [], "sparse_vector": [], "title": "HinDroid: An Intelligent Android Malware Detection System Based on Structured Heterogeneous Information Network.", "authors": ["<PERSON><PERSON>", "Yanfang Ye", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With explosive growth of Android malware and due to the severity of its damages to smart phone users, the detection of Android malware has become increasingly important in cybersecurity. The increasing sophistication of Android malware calls for new defensive techniques that are capable against novel threats and harder to evade. In this paper, to detect Android malware, instead of using Application Programming Interface (API) calls only, we further analyze the different relationships between them and create higher-level semantics which require more effort for attackers to evade the detection. We represent the Android applications (apps), related APIs, and their rich relationships as a structured heterogeneous information network (HIN). Then we use a meta-path based approach to characterize the semantic relatedness of apps and APIs. We use each meta-path to formulate a similarity measure over Android apps, and aggregate different similarities using multi-kernel learning. Then each meta-path is automatically weighted by the learning algorithm to make predictions. To the best of our knowledge, this is the first work to use structured HIN for Android malware detection. Comprehensive experiments on real sample collections from Comodo Cloud Security Center are conducted to compare various malware detection approaches. Promising experimental results demonstrate that our developed system HinDroid outperforms other alternative Android malware detection techniques.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098026"}, {"primary_key": "3832460", "vector": [], "sparse_vector": [], "title": "Planning and Learning under Uncertainty: Theory and Practice.", "authors": ["<PERSON>"], "summary": "This talk will describe recent progress on modeling, planning, learning, and control of autonomous systems operating in dynamic environments, with an emphasis on addressing the challenges faced on various timescales. For example, autonomous robotic agents need to plan/execute safe paths and avoid imminent collisions given noisy sensory information (short timescale), learn how to interact with other agents (possibly humans) with intents that are not known (medium timescale), and perform complex cooperative tasks given imperfect models and knowledge of the environment and teammate actions (long timescale). These tasks are often constrained to be done using onboard computation and perception, which can add significant complexity to the system. The talk will highlight several recently developed solutions to these challenges that have been implemented to demonstrate high-speed agile flight of a quadrotor in unknown, cluttered environments, autonomous navigation of a ground vehicle in complex indoor environments alongside pedestrians, and real-time cooperative multiagent planning with an onboard deep learning-based perception system.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105812"}, {"primary_key": "3832461", "vector": [], "sparse_vector": [], "title": "Communication-Efficient Distributed Block Minimization for Nonlinear Kernel Machines.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nonlinear kernel machines often yield superior predictive performance on various tasks; however, they suffer from severe computational challenges. In this paper, we show how to overcome the important challenge of speeding up kernel machines using multiple computers. In particular, we develop a parallel block minimization framework, and demonstrate its good scalability in solving nonlinear kernel SVM and logistic regression. Our framework proceeds by dividing the problem into smaller subproblems by forming a block-diagonal approximation of the Hessian matrix. The subproblems are then solved approximately in parallel. After that, a communication efficient line search procedure is developed to ensure sufficient reduction of the objective function value by exploiting the problem structure of kernel machines. We prove global linear convergence rate of the proposed method with a wide class of subproblem solvers, and our analysis covers strongly convex and some non-strongly convex functions. We apply our algorithm to solve large-scale kernel SVM problems on distributed systems, and show a significant improvement over existing parallel solvers. As an example, on the covtype dataset with half-a-million samples, our algorithm can obtain an approximate solution with 96% accuracy in 20 seconds using 32 machines, while all the other parallel kernel SVM solvers require more than 2000 seconds to achieve a solution with 95% accuracy. Moreover, our algorithm is the first distributed kernel SVM solver that can scale to massive data sets. On the KDDB dataset (20 million samples and 30 million features), our parallel solver can compute the kernel SVM solution within half an hour using 32 machines with 640 cores in total, while existing solvers can not scale to this dataset.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098080"}, {"primary_key": "3832462", "vector": [], "sparse_vector": [], "title": "SPOT: Sparse Optimal Transformations for High Dimensional Variable Selection and Exploratory Regression Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We develop a novel method called SParse Optimal Transformations (SPOT) to simultaneously select important variables and explore relationships between the response and predictor variables in high dimensional nonparametric regression analysis. Not only are the optimal transformations identified by SPOT interpretable, they can also be used for response prediction. We further show that SPOT achieves consistency in both variable selection and parameter estimation. Numerical experiments and real data applications demonstrate that SPOT outperforms other existing methods and can serve as an effective tool in practice.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098091"}, {"primary_key": "3832463", "vector": [], "sparse_vector": [], "title": "Large Scale Sentiment Learning with Limited Labels.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sentiment analysis is an important task in order to gain insights over the huge amounts of opinions that are generated in the social media on a daily basis. Although there is a lot of work on sentiment analysis, there are no many datasets available which one can use for developing new methods and for evaluation. To the best of our knowledge, the largest dataset for sentiment analysis is TSentiment [8], a 1.6 millions machine-annotated tweets dataset covering a period of about 3 months in 2009. This dataset however is too short and therefore insufficient to study heterogeneous, fast evolving streams. Therefore, we annotated the Twitter dataset of 2015 (228 million tweets without retweets and 275 million with retweets) and we make it publicly available for research. For the annotation we leverage the power of unlabeled data, together with labeled data using semi-supervised learning and in particular, Self-Learning and Co-Training. Our main contribution is the provision of the TSentiment15 dataset together with insights from the analysis, which includes a batch and a stream-processing of the data. In the former, all labeled and unlabeled data are available to the algorithms from the beginning, whereas in the later, they are revealed gradually based on their arrival time in the stream.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098159"}, {"primary_key": "3832464", "vector": [], "sparse_vector": [], "title": "Optimization Beyond Prediction: Prescriptive Price Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper addresses a novel data science problem, prescriptive price optimization, which derives the optimal price strategy to maximize future profit/revenue on the basis of massive predictive formulas produced by machine learning. The prescriptive price optimization first builds sales forecast formulas of multiple products, on the basis of historical data, which reveal complex relationships between sales and prices, such as price elasticity of demand and cannibalization. Then, it constructs a mathematical optimization problem on the basis of those predictive formulas. We present that the optimization problem can be formulated as an instance of binary quadratic programming (BQP). Although BQP problems are NP-hard in general and computationally intractable, we propose a fast approximation algorithm using a semi-definite programming (SDP) relaxation. Our experiments on simulation and real retail datasets show that our prescriptive price optimization simultaneously derives the optimal prices of tens/hundreds products with practical computational time, that potentially improve approximately 30% of gross profit of those products.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098188"}, {"primary_key": "3832465", "vector": [], "sparse_vector": [], "title": "Finding Precursors to Anomalous Drop in Airspeed During a Flight&apos;s Takeoff.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Aerodynamic stall based loss of control in flight is a major cause of fatal flight accidents. In a typical takeoff, a flight's airspeed continues to increase as it gains altitude. However, in some cases, the airspeed may drop immediately after takeoff and when left uncorrected, the flight gets close to a stall condition which is extremely risky. The takeoff is a high workload period for the flight crew involving frequent monitoring, control and communication with the ground control tower. Although there exists secondary safety systems and specialized recovery maneuvers, current technology is reactive; often based on simple threshold detection and does not provide the crew with sufficient lead time. Further, with increasing complexity of automation, the crew may not be aware of the true states of the automation to take corrective actions in time. At NASA, we aim to develop decision support tools by mining historic flight data to proactively identify and manage high risk situations encountered in flight. In this paper, we present our work on finding precursors to the anomalous drop-in-airspeed (ADA) event using the ADOPT (Automatic Discovery of Precursors in Time series) algorithm. ADOPT works by converting the precursor discovery problem into a search for sub-optimal decision making in the time series data, which is modeled using reinforcement learning. We give insights about the flight data, feature selection, ADOPT modeling and results on precursor discovery. Some improvements to ADOPT algorithm are implemented that reduces its computational complexity and enables forecasting of the adverse event. Using ADOPT analysis, we have identified some interesting precursor patterns that were validated to be operationally significant by subject matter experts. The performance of ADOPT is evaluated by using the precursor scores as features to predict the drop in airspeed events.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098097"}, {"primary_key": "3832466", "vector": [], "sparse_vector": [], "title": "Incremental Dual-memory LSTM in Land Cover Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Land cover prediction is essential for monitoring global environmental change. Unfortunately, traditional classification models are plagued by temporal variation and emergence of novel/unseen land cover classes in the prediction process. In this paper, we propose an LSTM-based spatio-temporal learning framework with a dual-memory structure. The dual-memory structure captures both long-term and short-term temporal variation patterns, and is updated incrementally to adapt the model to the ever-changing environment. Moreover, we integrate zero-shot learning to identify unseen classes even without labelled samples. Experiments on both synthetic and real-world datasets demonstrate the superiority of the proposed framework over multiple baselines in land cover prediction.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098112"}, {"primary_key": "3832467", "vector": [], "sparse_vector": [], "title": "MetaPAD: <PERSON><PERSON> from Massive Text Corpora.", "authors": ["<PERSON><PERSON>", "Jing<PERSON> Shang", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Jiawei Han"], "summary": "Mining textual patterns in news, tweets, papers, and many other kinds of text corpora has been an active theme in text mining and NLP research. Previous studies adopt a dependency parsing-based pattern discovery approach. However, the parsing results lose rich context around entities in the patterns, and the process is costly for a corpus of large scale. In this study, we propose a novel typed textual pattern structure, called meta pattern, which is extended to a frequent, informative, and precise subsequence pattern in certain context. We propose an efficient framework, called MetaPAD, which discovers meta patterns from massive corpora with three techniques: (1) it develops a context-aware segmentation method to carefully determine the boundaries of patterns with a learnt pattern quality assessment function, which avoids costly dependency parsing and generates high-quality patterns; (2) it identifies and groups synonymous meta patterns from multiple facets---their types, contexts, and extractions; and (3) it examines type distributions of entities in the instances extracted by each group of patterns, and looks for appropriate type levels to make discovered patterns precise. Experiments demonstrate that our proposed framework discovers high-quality typed textual patterns efficiently from different genres of massive corpora and facilitates information extraction.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098105"}, {"primary_key": "3832468", "vector": [], "sparse_vector": [], "title": "Peeking at A/B Tests: Why it matters, and what to do about it.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper reports on novel statistical methodology, which has been deployed by the commercial A/B testing platform Optimizely to communicate experimental results to their customers. Our methodology addresses the issue that traditional p-values and confidence intervals give unreliable inference. This is because users of A/B testing software are known to continuously monitor these measures as the experiment is running. We provide always valid p-values and confidence intervals that are provably robust to this effect. Not only does this make it safe for a user to continuously monitor, but it empowers her to detect true effects more efficiently. This paper provides simulations and numerical studies on <PERSON>ti<PERSON><PERSON>'s data, demonstrating an improvement in detection performance over traditional methods.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097992"}, {"primary_key": "3832469", "vector": [], "sparse_vector": [], "title": "Big Data in Climate: Opportunities and Challenges for Machine Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The climate and Earth sciences have recently undergone a rapid transformation from a data-poor to a data-rich environment. In particular, massive amount of data about Earth and its environment is now continuously being generated by a large number of Earth observing satellites as well as physics-based earth system models running on large-scale computational platforms. These massive and information-rich datasets offer huge potential for understanding how the Earth's climate and ecosystem have been changing and how they are being impacted by humans actions. We discuss the challenges involved in analyzing these massive data sets as well as opportunities they present for both advancing machine learning as well as the science of climate change.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105810"}, {"primary_key": "3832472", "vector": [], "sparse_vector": [], "title": "Federated Tensor Factorization for Computational Phenotyping.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tensor factorization models offer an effective approach to convert massive electronic health records into meaningful clinical concepts (phenotypes) for data analysis. These models need a large amount of diverse samples to avoid population bias. An open challenge is how to derive phenotypes jointly across multiple hospitals, in which direct patient-level data sharing is not possible (e.g., due to institutional policies). In this paper, we developed a novel solution to enable federated tensor factorization for computational phenotyping without sharing patient-level data. We developed secure data harmonization and federated computation procedures based on alternating direction method of multipliers (ADMM). Using this method, the multiple hospitals iteratively update tensors and transfer secure summarized information to a central server, and the server aggregates the information to generate phenotypes. We demonstrated with real medical datasets that our method resembles the centralized training model (based on combined datasets) in terms of accuracy and phenotypes discovery while respecting privacy.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098118"}, {"primary_key": "3832473", "vector": [], "sparse_vector": [], "title": "Ad Serving with Multiple KPIs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yongbo Zeng", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Ad-servers have to satisfy many different targeting criteria, and the combination can often result in no feasible solution. We hypothesize that advertisers may be defining these metrics to create a kind of \"proxy target\". We therefore reformulate the standard ad-serving problem to one where we attempt to get as close as possible to the advertiser's multi-dimensional target inclusive of delivery. We use a simple simulation to illustrate the behavior of this algorithm compared to Constraint and Pacing strategies. The system is then deployed in one of the largest video ad-servers in the United States and we show experimental results from live test ads, as well as 6 months of production performance across hundreds of ads. We find that the live ad-server tests match the simulation, and we report significant gains in multi-KPI performance from using the error minimization strategy.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098085"}, {"primary_key": "3832474", "vector": [], "sparse_vector": [], "title": "A Hierarchical Algorithm for Extreme Clustering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many modern clustering methods scale well to a large number of data points, N, but not to a large number of clusters, K. This paper introduces PERCH, a new non-greedy, incremental algorithm for hierarchical clustering that scales to both massive N and K---a problem setting we term extreme clustering. Our algorithm efficiently routes new data points to the leaves of an incrementally-built tree. Motivated by the desire for both accuracy and speed, our approach performs tree rotations for the sake of enhancing subtree purity and encouraging balancedness. We prove that, under a natural separability assumption, our non-greedy algorithm will produce trees with perfect dendrogram purity regardless of data arrival order. Our experiments demonstrate that PERCH constructs more accurate trees than other tree-building clustering algorithms and scales well with both N and K, achieving a higher quality clustering than the strongest flat clustering competitor in nearly half the time.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098079"}, {"primary_key": "3832475", "vector": [], "sparse_vector": [], "title": "Statistical Emerging Pattern Mining with Multiple Testing Correction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Emerging patterns are patterns whose support significantly differs between two databases. We study the problem of listing emerging patterns with a multiple testing guarantee. Recently, <PERSON><PERSON> et al., proposed the Limitless Arity Multiple-testing Procedure (LAMP) that controls the family-wise error rate (FWER) in statistical association mining. LAMP reduces the number of \"untestable\" hypotheses without compromising its statistical power. Still, FWER is restrictive, and as a result, its statistical power is inherently unsatisfying when the number of patterns is large. On the other hand, the false discovery rate (FDR) is less restrictive than FWER, and thus controlling FDR yields a larger number of significant patterns. We propose two emerging pattern mining methods: the first one controls FWER, and the second one controls FDR. The effectiveness of the methods is verified in computer simulations with real-world datasets.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098137"}, {"primary_key": "3832476", "vector": [], "sparse_vector": [], "title": "PNP: Fast Path Ensemble Method for Movie Design.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "How can we design a product or movie that will attract, for example, the interest of Pennsylvania adolescents or liberal newspaper critics? What should be the genre of that movie and who should be in the cast? In this work, we seek to identify how we can design new movies with features tailored to a specific user population. We formulate the movie design as an optimization problem over the inference of user-feature scores and selection of the features that maximize the number of attracted users. Our approach, PNP, is based on a heterogeneous, tripartite graph of users, movies, and features (e.g. actors, directors, genres), where users rate movies and features contribute to movies. We learn the preferences by leveraging user similarities defined through different types of relations, and show that our method outperforms state-of-the-art approaches, including matrix factorization and other heterogeneous graph-based analysis. We evaluate PNP on publicly available real-world data and show that it is highly scalable and effectively provides movie designs oriented towards different groups of users, including men, women, and adolescents.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098076"}, {"primary_key": "3832477", "vector": [], "sparse_vector": [], "title": "Estimating Treatment Effect in the Wild via Differentiated Confounder Balancing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Estimating treatment effect plays an important role on decision making in many fields, such as social marketing, healthcare, and public policy. The key challenge on estimating treatment effect in the wild observational studies is to handle confounding bias induced by imbalance of the confounder distributions between treated and control units. Traditional methods remove confounding bias by re-weighting units with supposedly accurate propensity score estimation under the unconfoundedness assumption. Controlling high-dimensional variables may make the unconfoundedness assumption more plausible, but poses new challenge on accurate propensity score estimation. One strand of recent literature seeks to directly optimize weights to balance confounder distributions, bypassing propensity score estimation. But existing balancing methods fail to do selection and differentiation among the pool of a large number of potential confounders, leading to possible underperformance in many high dimensional settings. In this paper, we propose a data-driven Differentiated Confounder Balancing (DCB) algorithm to jointly select confounders, differentiate weights of confounders and balance confounder distributions for treatment effect estimation in the wild high dimensional settings. The synergistic learning algorithm we proposed is more capable of reducing the confounding bias in many observational studies. To validate the effectiveness of our DCB algorithm, we conduct extensive experiments on both synthetic and real datasets. The experimental results clearly demonstrate that our DCB algorithm outperforms the state-of-the-art methods. We further show that the top features ranked by our algorithm generate accurate prediction of online advertising effect.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098032"}, {"primary_key": "3832478", "vector": [], "sparse_vector": [], "title": "Pharmacovigilance via Baseline Regularization with Large-Scale Longitudinal Observational Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Several prominent public health hazards [29] that occurred at the beginning of this century due to adverse drug events (ADEs) have raised international awareness of governments and industries about pharmacovigilance (PhV) [6,7], the science and activities to monitor and prevent adverse events caused by pharmaceutical products after they are introduced to the market. A major data source for PhV is large-scale longitudinal observational databases (LODs) [6] such as electronic health records (EHRs) and medical insurance claim databases. Inspired by the Self-Controlled Case Series (SCCS) model [27], arguably the leading method for ADE discovery from LODs, we propose baseline regularization, a regularized generalized linear model that leverages the diverse health profiles available in LODs across different individuals at different times. We apply the proposed method as well as SCCS to the Marshfield Clinic EHR. Experimental results suggest that the proposed method outperforms SCCS under various settings in identifying benchmark ADEs from the Observational Medical Outcomes Partnership ground truth [26].", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097998"}, {"primary_key": "3832480", "vector": [], "sparse_vector": [], "title": "Semi-Supervised Techniques for Mining Learning Outcomes and Prerequisites.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Daqing He"], "summary": "Educational content of today no longer only resides in textbooks and classrooms; more and more learning material is found in a free, accessible form on the Internet. Our long-standing vision is to transform this web of educational content into an adaptive, web-scale \"textbook\", that can guide its readers to most relevant \"pages\" according to their learning goal and current knowledge. In this paper, we address one core, long-standing problem towards this goal: identifying outcome and prerequisite concepts within a piece of educational content (e.g., a tutorial). Specifically, we propose a novel approach that leverages textbooks as a source of distant supervision, but learns a model that can generalize to arbitrary documents (such as those on the web). As such, our model can take advantage of any existing textbook, without requiring expert annotation. At the task of predicting outcome and prerequisite concepts, we demonstrate improvements over a number of baselines on six textbooks, especially in the regime of little to no ground-truth labels available. Finally, we demonstrate the utility of a model learned using our approach at the task of identifying prerequisite documents for adaptive content recommendation --- an important step towards our vision of the \"web as a textbook\".", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098187"}, {"primary_key": "3832481", "vector": [], "sparse_vector": [], "title": "The Selective Labels Problem: Evaluating Algorithmic Predictions in the Presence of Unobservables.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Evaluating whether machines improve on human performance is one of the central questions of machine learning. However, there are many domains where the data is selectively labeled in the sense that the observed outcomes are themselves a consequence of the existing choices of the human decision-makers. For instance, in the context of judicial bail decisions, we observe the outcome of whether a defendant fails to return for their court appearance only if the human judge decides to release the defendant on bail. This selective labeling makes it harder to evaluate predictive models as the instances for which outcomes are observed do not represent a random sample of the population. Here we propose a novel framework for evaluating the performance of predictive models on selectively labeled data. We develop an approach called contraction which allows us to compare the performance of predictive models and human decision-makers without resorting to counterfactual inference. Our methodology harnesses the heterogeneity of human decision-makers and facilitates effective evaluation of predictive models even in the presence of unmeasured confounders (unobservables) which influence both human decisions and the resulting outcomes. Experimental results on real world datasets spanning diverse domains such as health care, insurance, and criminal justice demonstrate the utility of our evaluation metric in comparing human decisions and machine predictions.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098066"}, {"primary_key": "3832483", "vector": [], "sparse_vector": [], "title": "Linearized GMM Kernels and Normalized Random Fourier Features.", "authors": ["<PERSON>"], "summary": "The method of \"random Fourier features (RFF)\" has become a popular tool for approximating the \"radial basis function (RBF)\" kernel. The variance of RFF is actually large. Interestingly, the variance can be substantially reduced by a simple normalization step as we theoretically demonstrate. We name the improved scheme as the \"normalized RFF (NRFF)\", and we provide a technical proof of the asymptotic variance of NRFF, as validated by simulations.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098081"}, {"primary_key": "3832484", "vector": [], "sparse_vector": [], "title": "Discovering Pollution Sources and Propagation Patterns in Urban Area.", "authors": ["Xiucheng Li", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Air quality is one of the most important environmental concerns in the world, and it has deteriorated substantially over the past years in many countries. For example, Chinese Academy of Social Sciences reports that the problem of haze and fog in China is hitting a record level, and China is currently suffering from the worst air pollution. Among the various causal factors of air quality, particulate matter with a diameter of 2.5 micrometers or less (i.e., PM2.5) is a very important factor; governments and people are increasingly concerned with the concentration of PM2.5. In many cities, stations for monitoring PM2.5 concentration have been built by governments or companies to monitor urban air quality. Apart from monitoring, there is a rising demand for finding pollution sources of PM2.5 and discovering the transmission of PM2.5 based on the data from PM$_{2.5}$ monitoring stations.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098090"}, {"primary_key": "3832485", "vector": [], "sparse_vector": [], "title": "Prospecting the Career Development of Talents: A Survival Analysis Perspective.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The study of career development has become more important during a time of rising competition. Even with the help of newly available big data in the field of human resources, it is challenging to prospect the career development of talents in an effective manner, since the nature and structure of talent careers can change quickly. To this end, in this paper, we propose a novel survival analysis approach to model the talent career paths, with a focus on two critical issues in talent management, namely turnover and career progression. Specifically, for modeling the talent turnover behaviors, we formulate the prediction of survival status at a sequence of time intervals as a multi-task learning problem by considering the prediction at each time interval as a task. Also, we impose the ranking constraints to model both censored and uncensored data, and capture the intrinsic properties exhibited in general lifetime modeling with non-recurrent and recurrent events. Similarly, for modeling the talent career progression, each task concerns the prediction of a relative occupational level at each time interval. The ranking constraints imposed on different occupational levels can help to reduce the prediction error. Finally, we evaluate our approach with several state-of-the-art baseline methods on real-world talent data. The experimental results clearly demonstrate the effectiveness of the proposed models for predicting the turnover and career progression of talents.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098107"}, {"primary_key": "3832486", "vector": [], "sparse_vector": [], "title": "Discovering Enterprise Concepts Using Spreadsheet Tables.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Existing work on knowledge discovery focuses on using natural language techniques to extract entities and relationships from textual documents. However, today relational tables are abundant in quantities, and are often well-structured with coherent data values. So far these rich relational tables have been largely overlooked for the purpose of knowledge discovery. In this work, we study the problem of building concept hierarchies using a large corpus of enterprise spreadsheet tables. Our method first groups distinct values from tables into a large hierarchical tre based on co-occurrence statistics. We then \"summarize\" the large tree by selecting important tree nodes that are likely good concepts based on how well they \"describe\" the original corpus. The result is a small concept hierarchy that is easy for humans to understand and curate. Our end-to-end algorithms are designed to run on Map-Reduce and to scale to large corpus. Experiments using real enterprise spreadsheet corpus show that proposed approach can generate concepts with high quality.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098102"}, {"primary_key": "3832487", "vector": [], "sparse_vector": [], "title": "FLAP: An End-to-End Event Log Analysis Platform for System Management.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many systems, such as distributed operating systems, complex networks, and high throughput web-based applications, are continuously generating large volume of event logs. These logs contain useful information to help system administrators to understand the system running status and to pinpoint the system failures. Generally, due to the scale and complexity of modern systems, the generated logs are beyond the analytic power of human beings. Therefore, it is imperative to develop a comprehensive log analysis system to support effective system management. Although a number of log mining techniques have been proposed to address specific log analysis use cases, few research and industrial efforts have been paid on providing integrated systems with an end-to-end solution to facilitate the log analysis routines.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098022"}, {"primary_key": "3832488", "vector": [], "sparse_vector": [], "title": "A Context-aware Attention Network for Interactive Question Answering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Neural network based sequence-to-sequence models in an encoder-decoder framework have been successfully applied to solve Question Answering (QA) problems, predicting answers from statements and questions. However, almost all previous models have failed to consider detailed context information and unknown states under which systems do not have enough information to answer given questions. These scenarios with incomplete or ambiguous information are very common in the setting of Interactive Question Answering (IQA). To address this challenge, we develop a novel model, employing context-dependent word-level attention for more accurate statement representations and question-guided sentence-level attention for better context modeling. We also generate unique IQA datasets to test our model, which will be made publicly available. Employing these attention mechanisms, our model accurately understands when it can output an answer or when it requires generating a supplementary question for additional input depending on different contexts. When available, user's feedback is encoded and directly applied to update sentence-level attention to infer an answer. Extensive experiments on QA and IQA datasets quantitatively demonstrate the effectiveness of our model with significant improvement over state-of-the-art conventional QA models.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098115"}, {"primary_key": "3832489", "vector": [], "sparse_vector": [], "title": "Collaborative Variational Autoencoder for Recommender Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern recommender systems usually employ collaborative filtering with rating information to recommend items to users due to its successful performance. However, because of the drawbacks of collaborative-based methods such as sparsity, cold start, etc., more attention has been drawn to hybrid methods that consider both the rating and content information. Most of the previous works in this area cannot learn a good representation from content for recommendation task or consider only text modality of the content, thus their methods are very limited in current multimedia scenario. This paper proposes a Bayesian generative model called collaborative variational autoencoder (CVAE) that considers both rating and content for recommendation in multimedia scenario. The model learns deep latent representations from content data in an unsupervised manner and also learns implicit relationships between items and users from both content and rating. Unlike previous works with denoising criteria, the proposed CVAE learns a latent distribution for content in latent space instead of observation space through an inference network and can be easily extended to other multimedia modalities other than text. Experiments show that CVAE is able to significantly outperform the state-of-the-art recommendation methods with more robust performance.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098077"}, {"primary_key": "3832490", "vector": [], "sparse_vector": [], "title": "Is the Whole Greater Than the Sum of Its Parts?", "authors": ["<PERSON><PERSON><PERSON>", "Hanghang Tong", "<PERSON>", "<PERSON><PERSON><PERSON> Shi", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The PART-WHOLE relationship routinely finds itself in many disciplines, ranging from collaborative teams, crowdsourcing, autonomous systems to networked systems. From the algorithmic perspective, the existing work has primarily focused on predicting the outcomes of the whole and parts, by either separate models or linear joint models, which assume the outcome of the parts has a linear and independent effect on the outcome of the whole. In this paper, we propose a joint predictive method named PAROLE to simultaneously and mutually predict the part and whole outcomes. The proposed method offers two distinct advantages over the existing work. First (Model Generality), we formulate joint PART-WHOLE outcome prediction as a generic optimization problem, which is able to encode a variety of complex relationships between the outcome of the whole and parts, beyond the linear independence assumption. Second (Algorithm Efficacy), we propose an effective and efficient block coordinate descent algorithm, which is able to find the coordinate-wise optimum with a linear complexity in both time and space. Extensive empirical evaluations on real-world datasets demonstrate that the proposed PAROLE (1) leads to consistent prediction performance improvement by modeling the non-linear part-whole relationship as well as part-part interdependency, and (2) scales linearly in terms of the size of the training dataset.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098006"}, {"primary_key": "3832491", "vector": [], "sparse_vector": [], "title": "Discrete Content-aware Matrix Factorization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Cao"], "summary": "Precisely recommending relevant items from massive candidates to a large number of users is an indispensable yet computationally expensive task in many online platforms (e.g., Amazon.com and Netflix.com). A promising way is to project users and items into a Hamming space and then recommend items via Hamming distance. However, previous studies didn't address the cold-start challenges and couldn't make the best use of preference data like implicit feedback. To fill this gap, we propose a Discrete Content-aware Matrix Factorization (DCMF) model, 1) to derive compact yet informative binary codes at the presence of user/item content information; 2) to support the classification task based on a local upper bound of logit loss; 3) to introduce an interaction regularization for dealing with the sparsity issue. We further develop an efficient discrete optimization algorithm for parameter learning. Based on extensive experiments on three real-world datasets, we show that DCFM outperforms the state-of-the-arts on both regression and classification tasks.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098008"}, {"primary_key": "3832492", "vector": [], "sparse_vector": [], "title": "Effective and Real-time In-App Activity Analysis in Encrypted Internet Traffic Streams.", "authors": ["<PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The mobile in-App service analysis, aiming at classifying mobile internet traffic into different types of service usages, has become a challenging and emergent task for mobile service providers due to the increasing adoption of secure protocols for in-App services. While some efforts have been made for the classification of mobile internet traffic, existing methods rely on complex feature construction and large storage cache, which lead to low processing speed, and thus not practical for online real-time scenarios. To this end, we develop an iterative analyzer for classifying encrypted mobile traffic in a real-time way. Specifically, we first select an optimal set of most discriminative features from raw features extracted from traffic packet sequences by a novel Maximizing Inner activity similarity and Minimizing Different activity similarity (MIMD) measurement. To develop the online analyzer, we first represent a traffic flow with a series of time windows, which are described by the optimal feature vector and are updated iteratively at the packet level. Instead of extracting feature elements from a series of raw traffic packets, our feature elements are updated when a new traffic packet is observed and the storage of raw traffic packets is not required. The time windows generated from the same service usage activity are grouped by our proposed method, namely, recursive time continuity constrained KMeans clustering (rCKC). The feature vectors of cluster centers are then fed into a random forest classifier to identify corresponding service usages. Finally, we provide extensive experiments on real-world Internet traffic data from Wechat, Whatsapp, and Facebook to demonstrate the effectiveness and efficiency of our approach. The results show that the proposed analyzer provides high accuracy in real-world scenarios, and has low storage cache requirement as well as fast processing speed.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098049"}, {"primary_key": "3832493", "vector": [], "sparse_vector": [], "title": "Supporting Employer Name Normalization at both Entity and Cluster Level.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Vachik S<PERSON> Dave", "<PERSON><PERSON><PERSON>"], "summary": "In the recruitment domain, the employer name normalization task, which links employer names in job postings or resumes to entities in an employer knowledge base (KB), is important to many business applications. In previous work, we proposed the CompanyDepot system, which used machine learning techniques to address the problem. After applying it to several applications at CareerBuilder, we faced several new challenges: 1) how to avoid duplicate normalization results when the KB is noisy and contains many duplicate entities; 2) how to address the vocabulary gap between query names and entity names in the KB; and 3) how to use the context available in jobs and resumes to improve normalization quality.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098093"}, {"primary_key": "3832494", "vector": [], "sparse_vector": [], "title": "Point-of-Interest Demand Modeling with Human Mobility Patterns.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xinjiang Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Point-of-Interest (POI) demand modeling in urban regions is critical for many applications such as business site selection and real estate investment. While some efforts have been made for the demand analysis of some specific POI categories, such as restaurants, it lacks systematic means to support POI demand modeling. To this end, in this paper, we develop a systematic POI demand modeling framework, named Region POI Demand Identification (RPDI), to model POI demands by exploiting the daily needs of people identified from their large-scale mobility data. Specifically, we first partition the urban space into spatially differentiated neighborhood regions formed by many small local communities. Then, the daily activity patterns of people traveling in the city will be extracted from human mobility data. Since the trip activities, even aggregated, are sparse and insufficient to directly identify the POI demands, especially for underdeveloped regions, we develop a latent factor model that integrates human mobility data, POI profiles, and demographic data to robustly model the POI demand of urban regions in a holistic way. In this model, POI preferences and supplies are used together with demographic features to estimate the POI demands simultaneously for all the urban regions interconnected in the city. Moreover, we also design efficient algorithms to optimize the latent model for large-scale data. Finally, experimental results on real-world data in New York City (NYC) show that our method is effective for identifying POI demands for different regions.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098168"}, {"primary_key": "3832495", "vector": [], "sparse_vector": [], "title": "Distributed Multi-Task Relationship Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Qi<PERSON>"], "summary": "Multi-task learning aims to learn multiple tasks jointly by exploiting their relatedness to improve the generalization performance for each task. Traditionally, to perform multi-task learning, one needs to centralize data from all the tasks to a single machine. However, in many real-world applications, data of different tasks may be geo-distributed over different local machines. Due to heavy communication caused by transmitting the data and the issue of data privacy and security, it is impossible to send data of different task to a master machine to perform multi-task learning. Therefore, in this paper, we propose a distributed multi-task learning framework that simultaneously learns predictive models for each task as well as task relationships between tasks alternatingly in the parameter server paradigm. In our framework, we first offer a general dual form for a family of regularized multi-task relationship learning methods. Subsequently, we propose a communication-efficient primal-dual distributed optimization algorithm to solve the dual problem by carefully designing local subproblems to make the dual problem decomposable. Moreover, we provide a theoretical convergence analysis for the proposed algorithm, which is specific for distributed multi-task relationship learning. We conduct extensive experiments on both synthetic and real-world datasets to evaluate our proposed framework in terms of effectiveness and convergence.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098136"}, {"primary_key": "3832496", "vector": [], "sparse_vector": [], "title": "Functional Zone Based Hierarchical Demand Prediction For Bike System Expansion.", "authors": ["<PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Bike sharing systems, aiming at providing the missing links in public transportation systems, are becoming popular in urban cities. Many providers of bike sharing systems are ready to expand their bike stations from the existing service area to surrounding regions. A key to success for a bike sharing systems expansion is the bike demand prediction for expansion areas. There are two major challenges in this demand prediction problem: First. the bike transition records are not available for the expansion area and second. station level bike demand have big variances across the urban city. Previous research efforts mainly focus on discovering global features, assuming the station bike demands react equally to the global features, which brings large prediction error when the urban area is large and highly diversified. To address these challenges, in this paper, we develop a hierarchical station bike demand predictor which analyzes bike demands from functional zone level to station level. Specifically, we first divide the studied bike stations into functional zones by a novel Bi-clustering algorithm which is designed to cluster bike stations with similar POI characteristics and close geographical distances together. Then, the hourly bike check-ins and check-outs of functional zones are predicted by integrating three influential factors: distance preference, zone-to-zone preference, and zone characteristics. The station demand is estimated by studying the demand distributions among the stations within the same functional zone. Finally, the extensive experimental results on the NYC Citi Bike system with two expansion stages show the advantages of our approach on station demand and balance prediction for bike sharing system expansions.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098180"}, {"primary_key": "3832497", "vector": [], "sparse_vector": [], "title": "Cascade Ranking for Operational E-commerce Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the 'Big Data' era, many real-world applications like search involve the ranking problem for a large number of items. It is important to obtain effective ranking results and at the same time obtain the results efficiently in a timely manner for providing good user experience and saving computational costs. Valuable prior research has been conducted for learning to efficiently rank like the cascade ranking (learning) model, which uses a sequence of ranking functions to progressively filter some items and rank the remaining items. However, most existing research of learning to efficiently rank in search is studied in a relatively small computing environments with simulated user queries. This paper presents novel research and thorough study of designing and deploying a Cascade model in a Large-scale Operational E-commerce Search application (CLOES), which deals with hundreds of millions of user queries per day with hundreds of servers. The challenge of the real-world application provides new insights for research: 1). Real-world search applications often involve multiple factors of preferences or constraints with respect to user experience and computational costs such as search accuracy, search latency, size of search results and total CPU cost, while most existing search solutions only address one or two factors; 2). Effectiveness of e-commerce search involves multiple types of user behaviors such as click and purchase, while most existing cascade ranking in search only models the click behavior. Based on these observations, a novel cascade ranking model is designed and deployed in an operational e-commerce search application. An extensive set of experiments demonstrate the advantage of the proposed work to address multiple factors of effectiveness, efficiency and user experience in the real-world application.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098011"}, {"primary_key": "3832498", "vector": [], "sparse_vector": [], "title": "BDT: Gradient Boosted Decision Tables for High Accuracy and Scoring Efficiency.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper we present gradient boosted decision tables (BDTs). A d-dimensional decision table is essentially a mapping from a sequence of d boolean tests to a real value in {R}. We propose novel algorithms to fit decision tables. Our thorough empirical study suggests that decision tables are better weak learners in the gradient boosting framework and can improve the accuracy of the boosted ensemble. In addition, we develop an efficient data structure to represent decision tables and propose a novel fast algorithm to improve the scoring efficiency for boosted ensemble of decision tables. Experiments on public classification and regression datasets demonstrate that our method is able to achieve 1.5x to 6x speedups over the boosted regression trees baseline. We complement our experimental evaluation with a bias-variance analysis that explains how different weak models influence the predictive power of the boosted ensemble. Our experiments suggest gradient boosting with randomly backfitted decision tables distinguishes itself as the most accurate method on a number of classification and regression problems. We have deployed a BDT model to LinkedIn news feed system and achieved significant lift on key metrics.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098175"}, {"primary_key": "3832501", "vector": [], "sparse_vector": [], "title": "Functional Annotation of Human Protein Coding Isoforms via Non-convex Multi-Instance Learning.", "authors": ["<PERSON>g<PERSON>o", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>yun Yi", "<PERSON><PERSON><PERSON><PERSON>", "Jieping Ye", "<PERSON><PERSON>"], "summary": "Functional annotation of human genes is fundamentally important for understanding the molecular basis of various genetic diseases. A major challenge in determining the functions of human genes lies in the functional diversity of proteins, that is, a gene can perform different functions as it may consist of multiple protein coding isoforms (PCIs). Therefore, differentiating functions of PCIs can significantly deepen our understanding of the functions of genes. However, due to the lack of isoform-level gold-standards (ground-truth annotation), many existing functional annotation approaches are developed at gene-level. In this paper, we propose a novel approach to differentiate the functions of PCIs by integrating sparse simplex projection---that is, a nonconvex sparsity-inducing regularizer---with the framework of multi-instance learning (MIL). Specifically, we label the genes that are annotated to the function under consideration as positive bags and the genes without the function as negative bags. Then, by sparse projections onto simplex, we learn a mapping that embeds the original bag space to a discriminative feature space. Our framework is flexible to incorporate various smooth and non-smooth loss functions such as logistic loss and hinge loss. To solve the resulting highly nontrivial non-convex and non-smooth optimization problem, we further develop an efficient block coordinate descent algorithm. Extensive experiments on human genome data demonstrate that the proposed approaches significantly outperform the state-of-the-art methods in terms of functional annotation accuracy of human PCIs and efficiency.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097984"}, {"primary_key": "3832502", "vector": [], "sparse_vector": [], "title": "Dipole: Diagnosis Prediction in Healthcare via Attention-based Bidirectional Recurrent Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Quanzeng You", "Tong Sun", "<PERSON>"], "summary": "Predicting the future health information of patients from the historical Electronic Health Records (EHR) is a core research task in the development of personalized healthcare. Patient EHR data consist of sequences of visits over time, where each visit contains multiple medical codes, including diagnosis, medication, and procedure codes. The most important challenges for this task are to model the temporality and high dimensionality of sequential EHR data and to interpret the prediction results. Existing work solves this problem by employing recurrent neural networks (RNNs) to model EHR data and utilizing simple attention mechanism to interpret the results. However, RNN-based approaches suffer from the problem that the performance of RNNs drops when the length of sequences is large, and the relationships between subsequent visits are ignored by current RNN-based approaches. To address these issues, we propose Dipole, an end-to-end, simple and robust model for predicting patients' future health information. <PERSON><PERSON> employs bidirectional recurrent neural networks to remember all the information of both the past visits and the future visits, and it introduces three attention mechanisms to measure the relationships of different visits for the prediction. With the attention mechanisms, <PERSON><PERSON> can interpret the prediction results effectively. <PERSON><PERSON> also allows us to interpret the learned medical code representations which are confirmed positively by medical experts. Experimental results on two real world EHR datasets show that the proposed Dipole can significantly improve the prediction accuracy compared with the state-of-the-art diagnosis prediction approaches and provide clinically meaningful interpretation.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098088"}, {"primary_key": "3832503", "vector": [], "sparse_vector": [], "title": "Unsupervised Discovery of Drug Side-Effects from Heterogeneous Data Sources.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Lu <PERSON>", "<PERSON><PERSON>"], "summary": "Drug side-effects become a worldwide public health concern, which are the fourth leading cause of death in the United States. Pharmaceutical industry has paid tremendous effort to identify drug side-effects during the drug development. However, it is impossible and impractical to identify all of them. Fortunately, drug side-effects can also be reported on heterogeneous platforms (i.e., data sources), such as FDA Adverse Event Reporting System and various online communities. However, existing supervised and semi-supervised approaches are not practical as annotating labels are expensive in the medical field. In this paper, we propose a novel and effective unsupervised model <PERSON><PERSON> to automatically discover drug side-effects. <PERSON><PERSON> enhances the estimation on drug side-effects by learning from various online platforms and measuring platform-level and user-level quality simultaneously. In this way, <PERSON><PERSON> demonstrates better performance compared with existing approaches in terms of correctly identifying drug side-effects. Experimental results on five real-world datasets show that <PERSON><PERSON> can significantly improve the performance of identifying side-effects compared with the state-of-the-art approaches.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098129"}, {"primary_key": "3832504", "vector": [], "sparse_vector": [], "title": "Internet Device Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Internet device graphs identify relationships between user-centric internet connected devices such as desktops, laptops, smartphones, tablets, gaming consoles, TV's, etc. The ability to create such graphs is compelling for online advertising, content customization, recommendation systems, security, and operations. We begin by describing an algorithm for generating a device graph based on IP-colocation, and then apply the algorithm to a corpus of over 2.5 trillion internet events collected over the period of six weeks in the United States. The resulting graph exhibits immense scale with greater than 7.3 billion edges (pair-wise relationships) between more than 1.2 billion nodes (devices), accounting for the vast majority of internet connected devices in the US. Next, we apply community detection algorithms to the graph resulting in a partitioning of internet devices into 100 million small communities representing physical households. We validate this partition with a unique ground truth dataset. We report on the characteristics of the graph and the communities. Lastly, we discuss the important issues of ethics and privacy that must be considered when creating and studying device graphs, and suggest further opportunities for device graph enrichment and application.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098114"}, {"primary_key": "3832505", "vector": [], "sparse_vector": [], "title": "Discovering Reliable Approximate Functional Dependencies.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Given a database and a target attribute of interest, how can we tell whether there exists a functional, or approximately functional dependence of the target on any set of other attributes in the data? How can we reliably, without bias to sample size or dimensionality, measure the strength of such a dependence? And, how can we efficiently discover the optimal or α-approximate top-k dependencies? These are exactly the questions we answer in this paper.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098062"}, {"primary_key": "3832506", "vector": [], "sparse_vector": [], "title": "RUSH!: Targeted Time-limited Coupons via Purchase Forecasts.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "Time-limited promotions that exploit consumers' sense of urgency to boost sales account for billions of dollars in consumer spending each year. However, it is challenging to discover the right timing and duration of a promotion to increase its chances of being redeemed. In this work, we consider the problem of delivering time-limited discount coupons, where we partner with a large national bank functioning as a commission-based third-party coupon provider. Specifically, we use large-scale anonymized transaction records to model consumer spending and forecast future purchases, based on which we generate data-driven, personalized coupons. Our proposed model RUSH! (1) predicts {both the time and category} of the next event; (2) captures correlations between purchases in different categories (such as shopping triggering dining purchases); (3) incorporates temporal dynamics of purchase behavior (such as increased spending on weekends); (4) is composed of additive factors that are easily interpretable; and finally (5) scales linearly to millions of transactions. We design a cost-benefit framework that facilitates systematic evaluation in terms of our application, and show that RUSH! provides higher expected value than various baselines that do not jointly model time and category information.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098104"}, {"primary_key": "3832507", "vector": [], "sparse_vector": [], "title": "Let&apos;s See Your Digits: Anomalous-State Detection using Benford&apos;s Law.", "authors": ["<PERSON>", "Claudia Plant"], "summary": "<PERSON><PERSON>'s Law explains a curious phenomenon in which the leading digits of \"naturally-occurring\" numerical data are distributed in a precise fashion. In this paper we begin by showing that system metrics generated by many modern information systems like Twitter, Wikipedia, YouTube and GitHub obey this law. We then propose a novel unsupervised approach called BenFound that exploits this property to detect anomalous system events. BenFound tracks the \"Benfordness\" of key system metrics, like the follower counts of tweeting Twitter users or the change deltas in Wikipedia page edits. It then applies a novel Benford-conformity test in real-time to identify \"non-Benford events\". We investigate a variety of such events, showing that they correspond to unnatural and often undesirable system interactions like spamming, hashtag-hijacking and denial-of-service attacks. The result is a technically-uncomplicated and effective \"red flagging\" technique that can be used to complement existing anomaly-detection approaches. Although not without its limitations, it is highly efficient and requires neither obscure parameters, nor text streams, nor natural-language processing.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098101"}, {"primary_key": "3832508", "vector": [], "sparse_vector": [], "title": "Towards an Optimal Subspace for K-Means.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Claudia Plant", "<PERSON>"], "summary": "Is there an optimal dimensionality reduction for k-means, revealing the prominent cluster structure hidden in the data? We propose SUBKMEANS, which extends the classic k-means algorithm. The goal of this algorithm is twofold: find a sufficient k-means-style clustering partition and transform the clusters onto a common subspace, which is optimal for the cluster structure. Our solution is able to pursue these two goals simultaneously. The dimensionality of this subspace is found automatically and therefore the algorithm comes without the burden of additional parameters. At the same time this subspace helps to mitigate the curse of dimensionality. The SUBKMEANS optimization algorithm is intriguingly simple and efficient. It is easy to implement and can readily be adopted to the current situation. Furthermore, it is compatible to many existing extensions and improvements of k-means.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097989"}, {"primary_key": "3832509", "vector": [], "sparse_vector": [], "title": "Addressing Challenges with Big Data for Media Measurement.", "authors": ["<PERSON><PERSON>"], "summary": "The digital media and TV - which is increasingly digitized, have amassed and generating enormous amount of data. While extremely useful, the big data generated by these platforms poses unique challenges for Data Scientists working on developing measurement framework and metrics. Most practitioners optimize speed and scale at the expense of accuracy, which is critical for any measurement. And, the trade-off between bias and variance is not in consideration. In this paper, we will demonstrate how Nielsen is combining proprietary ground truth data and methodologies with Big Data to address the accuracy and bias/variance challenges. We argue that high quality ground truth or training set is pre-requisite to deploying Big Data for high quality media measurement. To illustrate the point, we will share how Nielsen is combining its proprietary high quality panels with Set Top Box for TV measurement in the U.S.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105814"}, {"primary_key": "3832510", "vector": [], "sparse_vector": [], "title": "Developing a Comprehensive Framework for Multimodal Feature Extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Feature extraction is a critical component of many applied data science workflows. In recent years, rapid advances in artificial intelligence and machine learning have led to an explosion of feature extraction tools and services that allow data scientists to cheaply and effectively annotate their data along a vast array of dimensions--ranging from detecting faces in images to analyzing the sentiment expressed in coherent text. Unfortunately, the proliferation of powerful feature extraction services has been mirrored by a corresponding expansion in the number of distinct interfaces to feature extraction services. In a world where nearly every new service has its own API, documentation, and/or client library, data scientists who need to combine diverse features obtained from multiple sources are often forced to write and maintain ever more elaborate feature extraction pipelines. To address this challenge, we introduce a new open-source framework for comprehensive multimodal feature extraction. Pliers is an open-source Python package that supports standardized annotation of diverse data types (videos, images, audio, and text), and is expressly implemented with both ease-of-use and extensibility in mind. Users can apply a wide range of pre-existing feature extraction tools to their data in just a few lines of Python code, and can also easily add their own custom extractors by writing modular classes. A graph-based API enables rapid development of feature extraction pipelines that output results in a single, standardized format. We describe the package's architecture, detail its advantages over previous feature extraction toolboxes, and use a sample application to a large functional MRI dataset to illustrate how pliers can significantly reduce the time and effort required to construct simple feature extraction workflows while increasing code clarity and maintainability.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098075"}, {"primary_key": "3832511", "vector": [], "sparse_vector": [], "title": "The Future of Data Integration.", "authors": ["<PERSON><PERSON>"], "summary": "The value of data explodes when it is integrated. In this talk, I present some important innovations in data integration over the last two decades. These include data exchange [1], which provides a foundation for reasoning about the correctness of transformed data, and the use of declarative mappings in integration [2]. I discuss how data mining has been used to facilitate data integration, including constraint discovery [3], mapping discovery [4], and in schema discovery to combat database decay and facilitate integration [5,6]. I present some important new data integration challenges that arise in data science. These include the use of mining for query and visualization recommendation over massive data lakes [7] and data set search, finding datasets of interest at interactive speeds [8].", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105809"}, {"primary_key": "3832513", "vector": [], "sparse_vector": [], "title": "Deep Choice Model Using Pointer Networks for Airline Itinerary Prediction.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Travel providers such as airlines and on-line travel agents are becoming more and more interested in understanding how passengers choose among alternative itineraries when searching for flights. This knowledge helps them better display and adapt their offer, taking into account market conditions and customer needs. Some common applications are not only filtering and sorting alternatives, but also changing certain attributes in real-time (e.g., changing the price). In this paper, we concentrate with the problem of modeling air passenger choices of flight itineraries. This problem has historically been tackled using classical Discrete Choice Modelling techniques. Traditional statistical approaches, in particular the Multinomial Logit model (MNL), is widely used in industrial applications due to its simplicity and general good performance. However, MNL models present several shortcomings and assumptions that might not hold in real applications. To overcome these difficulties, we present a new choice model based on Pointer Networks. Given an input sequence, this type of deep neural architecture combines Recurrent Neural Networks with the Attention Mechanism to learn the conditional probability of an output whose values correspond to positions in an input sequence. Therefore, given a sequence of different alternatives presented to a customer, the model can learn to point to the one most likely to be chosen by the customer. The proposed method was evaluated on a real dataset that combines on-line user search logs and airline flight bookings. Experimental results show that the proposed model outperforms the traditional MNL model on several metrics.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098005"}, {"primary_key": "3832514", "vector": [], "sparse_vector": [], "title": "The Future of Artificially Intelligent Assistants.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Alborz Geramifard", "<PERSON><PERSON>"], "summary": "Artificial Intelligence has been present in literature at least since the ancient Greeks. Depictions present a wide range of perspectives of AI ranging from malefic overlords to depressive androids. Perhaps the most common recurring theme is the AI Assistant: C3PO from Star Wars; the Jetson's <PERSON> the Robot; the benign hyper-efficient Minds of <PERSON>'s Culture novels; the eerie HAL 9000 of <PERSON>'s 2001: A Space Odyssey. Today, artificially intelligent assistants are actual products in the marketplace, based on startling recent progress in technologies like speaker-independent speech recognition. These products are in their infancy, but are improving rapidly. In this panel, we will address the product and technology landscape, and will ask a series of experts in the field plus the members of the audience to take a stance on what the future of artificially intelligent assistants will look like.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3120999"}, {"primary_key": "3832515", "vector": [], "sparse_vector": [], "title": "Embedding-based News Recommendation for Millions of Users.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "It is necessary to understand the content of articles and user preferences to make effective news recommendations. While ID-based methods, such as collaborative filtering and low-rank factorization, are well known for making recommendations, they are not suitable for news recommendations because candidate articles expire quickly and are replaced with new ones within short spans of time. Word-based methods, which are often used in information retrieval settings, are good candidates in terms of system performance but have issues such as their ability to cope with synonyms and orthographical variants and define \"queries\" from users' historical activities. This paper proposes an embedding-based method to use distributed representations in a three step end-to-end manner: (i) start with distributed representations of articles based on a variant of a denoising autoencoder, (ii) generate user representations by using a recurrent neural network (RNN) with browsing histories as input sequences, and (iii) match and list articles for users based on inner-product operations by taking system performance into consideration. The proposed method performed well in an experimental offline evaluation using past access data on Yahoo! JAPAN's homepage. We implemented it on our actual news distribution system based on these experimental results and compared its online performance with a method that was conventionally incorporated into the system. As a result, the click-through rate (CTR) improved by 23% and the total duration improved by 10%, compared with the conventionally incorporated method. Services that incorporated the method we propose are already open to all users and provide recommendations to over ten million individual users per day who make billions of accesses per month.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098108"}, {"primary_key": "3832516", "vector": [], "sparse_vector": [], "title": "Learning to Count Mosquitoes for the Sterile Insect Technique.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mosquito-borne illnesses such as dengue, chikungunya, and Zika are major global health problems, which are not yet addressable with vaccines and must be countered by reducing mosquito populations. The Sterile Insect Technique (SIT) is a promising alternative to pesticides; however, effective SIT relies on minimal releases of female insects. This paper describes a multi-objective convolutional neural net to significantly streamline the process of counting male and female mosquitoes released from a SIT factory and provides a statistical basis for verifying strict contamination rate limits from these counts despite measurement noise. These results are a promising indication that such methods may dramatically reduce the cost of effective SIT methods in practice.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098204"}, {"primary_key": "3832517", "vector": [], "sparse_vector": [], "title": "Machine Learning Software in Practice: Quo Vadi<PERSON>?", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Due to the hype in our industry in the last couple of years, there is a growing mismatch between software tools machine learning practitioners wish for, what they would truly need for their work, what's available (either commercially or open source) and what tool developers and researchers focus on. In this talk we will give a couple of examples of this mismatch. Several surveys and anecdotal evidence show that most practitioners work most of the time (at least in the modeling phase) with datasets that t in the RAM of a single server, therefore distributed computing tools are very of- ten overkill. Our benchmarks (available on github [1]) of the most widely used open source tools for binary classification (various implementations of algorithms such as linear methods, random forests, gradient boosted trees and neural networks) on such data show over 10x speed and over 10x RAM usage difference between various tools, with \"big data\" tools being the most inefficient. Significant performance gains have been obtained by those tools that incorporate various low-level (close to CPU and memory architecture) optimizations. Nevertheless, we will show that even the best tools show degrading performance on the multi-socket servers featuring a high number of cores, systems that have become widely accessible more recently. Finally, while most of this talk is about performance, we will also argue that machine learning tools that feature high-level easy-to-use APIs provide increasing productivity for practitioners and therefore are preferable.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3106683"}, {"primary_key": "3832518", "vector": [], "sparse_vector": [], "title": "Deep Design: Product Aesthetics for Heterogeneous Markets.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Panos Y. <PERSON>"], "summary": "Aesthetic appeal is a primary driver of customer consideration for products such as automobiles. Product designers must accordingly convey design attributes (e.g., 'Sportiness'), a challenging proposition given the subjective nature of aesthetics and heterogeneous market segments with potentially different aesthetic preferences. We introduce a scalable deep learning approach that predicts how customers across different market segments perceive aesthetic designs and provides a visualization that can aid in product design. We tested this approach using a large-scale product design and crowdsourced customer data set with a Siamese neural network architecture containing a pair of conditional generative adversarial networks. The results show that the model predicts aesthetic design attributes of customers in heterogeneous market segments and provides a visualization of these aesthetic perceptions. This suggests that the proposed deep learning approach provides a scalable method for understanding customer aesthetic perceptions.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098176"}, {"primary_key": "3832519", "vector": [], "sparse_vector": [], "title": "An Intelligent Customer Care Assistant System for Large-Scale Cellular Network Diagnosis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the advent of cellular network technologies, mobile Internet access becomes the norm in everyday life. In the meantime, the complaints made by subscribers about unsatisfactory cellular network access also become increasingly frequent. From a network operator's perspective, achieving accurate and timely cellular network diagnosis about the causes of the complaints is critical for both improving subscriber-perceived experience and maintaining network robustness. We present the Intelligent Customer Care Assistant (ICCA), a distributed fault classification system that exploits a data-driven approach to perform large-scale cellular network diagnosis. ICCA takes massive network data as input, and realizes both offline model training and online feature computation to distinguish between user and network faults in real time. ICCA is currently deployed in a metropolitan LTE network in China that is serving around 50 million subscribers. We show via evaluation that ICCA achieves high classification accuracy (85.3%) and fast query response time (less than 2.3 seconds). We also report our experiences learned from the deployment.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098120"}, {"primary_key": "3832520", "vector": [], "sparse_vector": [], "title": "Designing AI at Scale to Power Everyday Life.", "authors": ["<PERSON><PERSON>"], "summary": "The majority of the experiences and interactions people have on Facebook today are made possible with AI. Well over 1 billion people enjoy unique, personalized experiences on Facebook that are powered by a wealth of AI and machine learning algorithms. AI is an incredibly fast-moving field: engineers and researchers across the company are turning the latest research breakthroughs into tools, platforms, and infrastructure that make it possible for anyone at Facebook to use AI in the experiences and products they build. This talk will look at how Facebook is conducting and applying industry-leading research to help drive advancements in AI disciplines like computer vision, language understanding, speech and video. We will also talk about building an infrastructure that anyone at Facebook can use to easily reuse algorithms in different products, scale to run thousands of simultaneous custom experiments, and give concrete examples of how employees across the company are able to leverage these platforms to build new AI products and services.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105815"}, {"primary_key": "3832521", "vector": [], "sparse_vector": [], "title": "Compass: <PERSON><PERSON><PERSON> Sentiment Analysis of US Election What Twitter Says!", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the widespread growth of various social network tools and platforms, analyzing and understanding societal response and crowd reaction to important and emerging social issues and events through social media data is increasingly an important problem. However, there are numerous challenges towards realizing this goal effectively and efficiently, due to the unstructured and noisy nature of social media data. The large volume of the underlying data also presents a fundamental challenge. Furthermore, in many application scenarios, it is often interesting, and in some cases critical, to discover patterns and trends based on geographical and/or temporal partitions, and keep track of how they will change overtime.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098053"}, {"primary_key": "3832523", "vector": [], "sparse_vector": [], "title": "SPARTan: Scalable PARAFAC2 for Large &amp; Sparse Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Sun"], "summary": "In exploratory tensor mining, a common problem is how to analyze a set of variables across a set of subjects whose observations do not align naturally. For example, when modeling medical features across a set of patients, the number and duration of treatments may vary widely in time, meaning there is no meaningful way to align their clinical records across time points for analysis purposes. To handle such data, the state-of-the-art tensor model is the so-called PARAFAC2, which yields interpretable and robust output and can naturally handle sparse data. However, its main limitation up to now has been the lack of efficient algorithms that can handle large-scale datasets.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098014"}, {"primary_key": "3832524", "vector": [], "sparse_vector": [], "title": "Backpage and Bitcoin: Uncovering Human Traffickers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sites for online classified ads selling sex are widely used by human traffickers to support their pernicious business. The sheer quantity of ads makes manual exploration and analysis unscalable. In addition, discerning whether an ad is advertising a trafficked victim or an independent sex worker is a very difficult task. Very little concrete ground truth (i.e., ads definitively known to be posted by a trafficker) exists in this space. In this work, we develop tools and techniques that can be used separately and in conjunction to group sex ads by their true owner (and not the claimed author in the ad). Specifically, we develop a machine learning classifier that uses stylometry to distinguish between ads posted by the same vs. different authors with 90% TPR and 1% FPR. We also design a linking technique that takes advantage of leakages from the Bitcoin mempool, blockchain and sex ad site, to link a subset of sex ads to Bitcoin public wallets and transactions. Finally, we demonstrate via a 4-week proof of concept using Backpage as the sex ad site, how an analyst can use these automated approaches to potentially find human traffickers.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098082"}, {"primary_key": "3832525", "vector": [], "sparse_vector": [], "title": "Spaceborne Data Enters the Mainstream.", "authors": ["<PERSON>"], "summary": "Thanks to a diverse constellation of Earth observing satellites, humanity is effectively looking everywhere, at everything, all the time. And we've been quietly doing so for a long time. The challenge is that for many years these massive data archives have been stranded from operational, cloud-based, modern data science. That is changing fast. In this session we'll do a rapid primer on satellite imagery as a source of novel data about our Earth and discuss how machine learning is a key force for translating all of this Earth data into real insights. We'll use the global agriculture system as a case in point, highlighting some of potential that a spaceborne perspective brings to this vital sector of the economy. Along the way, we will explore some of the beautiful imagery of our home planet that fuels this new class of insights.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105811"}, {"primary_key": "3832526", "vector": [], "sparse_vector": [], "title": "Not All Passes Are Created Equal: Objectively Measuring the Risk and Reward of Passes in Soccer from Tracking Data.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In soccer, the most frequent event that occurs is a pass. For a trained eye, there are a myriad of adjectives which could describe this event (e.g., \"majestic pass\", \"conservative\" to \"poor-ball\"). However, as these events are needed to be coded live and in real-time (most often by human annotators), the current method of grading passes is restricted to the binary labels 0 (unsuccessful) or 1 (successful). Obviously, this is sub-optimal because the quality of a pass needs to be measured on a continuous spectrum (i.e., 0 to 100%) and not a binary value. Additionally, a pass can be measured across multiple dimensions, namely: i) risk -- the likelihood of executing a pass in a given situation, and ii) reward -- the likelihood of a pass creating a chance. In this paper, we show how we estimate both the risk and reward of a pass across two seasons of tracking data captured from a recent professional soccer league with state-of-the-art performance, then showcase various use cases of our deployed passing system.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098051"}, {"primary_key": "3832527", "vector": [], "sparse_vector": [], "title": "Mixture Factorized Ornstein-Uhlenbeck Processes for Time-Series Forecasting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>"], "summary": "Forecasting the future observations of time-series data can be performed by modeling the trend and fluctuations from the observed data. Many classical time-series analysis models like Autoregressive model (AR) and its variants have been developed to achieve such forecasting ability. While they are often based on the white noise assumption to model the data fluctuations, a more general Brownian motion has been adopted that results in Ornstein-Uhlenbeck (OU) process. The OU process has gained huge successes in predicting the future observations over many genres of time series, however, it is still limited in modeling simple diffusion dynamics driven by a single persistent factor that never evolves over time. However, in many real problems, a mixture of hidden factors are usually present, and when and how frequently they appear or disappear are unknown ahead of time. This imposes a challenge that inspires us to develop a Mixture Factorized OU process (MFOUP) to model evolving factors. The new model is able to capture the changing states of multiple mixed hidden factors, from which we can infer their roles in driving the movements of time series. We conduct experiments on three forecasting problems, covering sensor and market data streams. The results show its competitive performance on predicting future observations and capturing evolution patterns of hidden factors as compared with the other algorithms.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098113"}, {"primary_key": "3832528", "vector": [], "sparse_vector": [], "title": "MARAS: Signaling Multi-Drug Adverse Reactions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There is a growing need for computing-supported methods that facilitate the automated signaling of Adverse Drug Reactions (ADRs) otherwise left undiscovered from the exploding amount of ADR reports filed by patients, medical professionals and drug manufacturers. In this research, we design a Multi-Drug Adverse Reaction Analytics Strategy, called MARAS, to signal severe unknown ADRs triggered by the usage of a combination of drugs, also known as Multi-Drug Adverse Reactions (MDAR). First, MARAS features an efficient signal generation algorithm based on association rule learning that extracts non-spurious MDAR associations. Second, MARAS incorporates contextual information to detect drug combinations that are strongly associated with a set of ADRs. It groups related associations into Contextual Association Clusters (CACs) that then avail contextual information to evaluate the significance of the discovered MDAR Associations. Lastly, we use this contextual significance to rank discoveries by their notion of interestingness to signal the most compelling MDARs. To demonstrate the utility of MARAS, it is compared with state-of-the-art techniques and evaluated via case studies on datasets collected by U.S. Food and Drug Administration Adverse Event Reporting System (FAERS).", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097986"}, {"primary_key": "3832529", "vector": [], "sparse_vector": [], "title": "Automatic Synonym Discovery with Knowledge Bases.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Jiawei Han"], "summary": "Recognizing entity synonyms from text has become a crucial task in many entity-leveraging applications. However, discovering entity synonyms from domain-specific text corpora (e.g., news articles, scientific papers) is rather challenging. Current systems take an entity name string as input to find out other names that are synonymous, ignoring the fact that often times a name string can refer to multiple entities (e.g., \"apple\" could refer to both Apple Inc. and the fruit apple). Moreover, most existing methods require training data manually created by domain experts to construct supervised-learning systems. In this paper, we study the problem of automatic synonym discovery with knowledge bases, that is, identifying synonyms for knowledge base entities in a given domain-specific corpus. The manually-curated synonyms for each entity stored in a knowledge base not only form a set of name strings to disambiguate the meaning for each other, but also can serve as \"distant\" supervision to help determine important features for the task. We propose a novel framework, called DPE, to integrate two kinds of mutually-complementing signals for synonym discovery, i.e., distributional features based on corpus-level statistics and textual patterns based on local contexts. In particular, DPE jointly optimizes the two kinds of signals in conjunction with distant supervision, so that they can mutually enhance each other in the training stage. At the inference stage, both signals will be utilized to discover synonyms for the given entities. Experimental results prove the effectiveness of the proposed framework.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098185"}, {"primary_key": "3832530", "vector": [], "sparse_vector": [], "title": "Collecting and Analyzing Millions of mHealth Data Streams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Players across the health ecosystem are initiating studies of thousands, even millions, of participants to gather diverse types of data, including biomedical, behavioral, and lifestyle in order to advance medical research. These efforts to collect multi-modal data sets on large cohorts coincide with the rise of broad activity and behavior tracking across industries, particularly in healthcare and the growing field of mobile health (mHealth). Government and pharmaceutical sponsored, as well as patient-driven group studies in this arena leverage the ability of mobile technology to continuously track behaviors and environmental factors with minimal participant burden. However, the adoption of mHealth has been constrained by the lack of robust solutions for large-scale data collection in free-living conditions and concerns around data quality. In this work, we describe the infrastructure Evidation Health has developed to collect mHealth data from millions of users through hundreds of different mobile devices and apps. Additionally, we provide evidence of the utility of the data for inferring individual traits pertaining to health, wellness, and behavior. To this end, we introduce and evaluate deep neural network models that achieve high prediction performance without requiring any feature engineering when trained directly on the densely sampled multivariate mHealth time series data. We believe that the present work substantiates both the feasibility and the utility of creating a very large mHealth research cohort, as envisioned by the many large cohort studies currently underway across therapeutic areas and conditions.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098201"}, {"primary_key": "3832531", "vector": [], "sparse_vector": [], "title": "An Alternative to NCD for Large Sequences, Lempel-Ziv Jaccard Distance.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The Normalized Compression Distance (NCD) has been used in a number of domains to compare objects with varying feature types. This flexibility comes from the use of general purpose compression algorithms as the means of computing distances between byte sequences. Such flexibility makes NCD particularly attractive for cases where the right features to use are not obvious, such as malware classification. However, NCD can be computationally demanding, thereby restricting the scale at which it can be applied. We introduce an alternative metric also inspired by compression, the Lempel-Ziv Jaccard Distance (LZJD). We show that this new distance has desirable theoretical properties, as well as comparable or superior performance for malware classification, while being easy to implement and orders of magnitude faster in practice.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098111"}, {"primary_key": "3832535", "vector": [], "sparse_vector": [], "title": "struc2vec: Learning Node Representations from Structural Identity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Structural identity is a concept of symmetry in which network nodes are identified according to the network structure and their relationship to other nodes. Structural identity has been studied in theory and practice over the past decades, but only recently has it been addressed with representational learning techniques. This work presents struc2vec, a novel and flexible framework for learning latent representations for the structural identity of nodes. struc2vec uses a hierarchy to measure node similarity at different scales, and constructs a multilayer graph to encode structural similarities and generate structural context for nodes. Numerical experiments indicate that state-of-the-art techniques for learning node representations fail in capturing stronger notions of structural identity, while struc2vec exhibits much superior performance in this task, as it overcomes limitations of prior approaches. As a consequence, numerical experiments indicate that struc2vec improves performance on classification tasks that depend more on structural identity.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098061"}, {"primary_key": "3832536", "vector": [], "sparse_vector": [], "title": "Dispatch with Confidence: Integration of Machine Learning, Optimization and Simulation for Open Pit Mines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Open pit mining operations require utilization of extremely expensive equipment such as large trucks, shovels and loaders. To remain competitive, mining companies are under pressure to increase equipment utilization and reduce operational costs. The key to this in mining operations is to have sophisticated truck assignment strategies which will ensure that equipment is utilized efficiently with minimum operating cost. To address this problem, we have implemented truck assignment approach which integrates machine learning, linear/integer programming and simulation. Our truck assignment approach takes into consideration the number of trucks and their sizes, shovels and dump locations as well as stochastic activity times during the operations. Machine learning is used to predict probability distributions of equipment activity duration. We have validated the approach using data collected from two open pit mines. Our experimental results show that our approach offers increase of 10% in efficiency. Presented results demonstrate that machine learning can bring significant value to mining industry.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098178"}, {"primary_key": "3832537", "vector": [], "sparse_vector": [], "title": "Inferring the Strength of Social Ties: A Community-Driven Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online social networks are growing and becoming denser.The social connections of a given person may have very high variability: from close friends and relatives to acquaintances to people who hardly know. Inferring the strength of social ties is an important ingredient for modeling the interaction of users in a network and understanding their behavior. Furthermore, the problem has applications in computational social science, viral marketing, and people recommendation. In this paper we study the problem of inferring the strength of social ties in a given network. Our work is motivated by a recent approach by <PERSON><PERSON> et. al [24], which leverages the Strong Triadic Closure} STC principle, a hypothesis rooted in social psychology. To guide our inference process, in addition to the network structure, we also consider as input a collection of tight communities. Those are sets of vertices that we expect to be connected via strong ties. Such communities appear in different situations, e.g., when being part of a community implies a strong connection to one of the existing members. We consider two related problem formalizations that reflect the assumptions of our setting: small number of STC violations and strong-tie connectivity in the input communities. We show that both problem formulations are NP-hard. We also show that one problem formulation is hard to approximate, while for the second we develop an algorithm with approximation guarantee. We validate the proposed method on real-world datasets by comparing with baselines that optimize STC violations and community connectivity separately.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098199"}, {"primary_key": "3832538", "vector": [], "sparse_vector": [], "title": "More than the Sum of its Parts: Building Domino Data Lab.", "authors": ["<PERSON> Rubi<PERSON>"], "summary": "Industry has always leveraged cutting edge quantitative research techniques. From finance and insurance, to marketing and manufacturing, efficiencies and advantages have been seized through measurement, prediction, and the generation of insights' but never at this scale. Organizations which previously may have employed one or two data scientists are now scaling the work to dozens if not hundreds of practitioners. Where previously only a handful of organizations could boast that they were leveraging machine learning and statistical models, now it's a rarity to find an untouched industry or player. Organizations are now faced with the challenges of empowering, scaling, and measuring this workforce to sustain the transformation to the prediction economy. In this talk, I will discuss how and why we built the Domino Data Lab platform. I will talk about the challenges we faced technologically, organizationally and culturally when bringing a system of record to data science.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3106682"}, {"primary_key": "3832539", "vector": [], "sparse_vector": [], "title": "&quot;The Leicester City Fairytale?&quot;: Utilizing New Soccer Analytics Tools to Compare Performance in the 15/16 &amp; 16/17 EPL Seasons.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The last two years have been somewhat of a rollercoaster for English Premier League (EPL) team Leicester City. In the 2015/16 season, against all odds and logic, they won the league to much fan-fare. Fast-forward nine months later, and they are battling relegation. What could describe this fluctuating form? As soccer is a very complex and strategic game, common statistics (e.g., passes, shots, possession) do not really tell the full story on how a team succeeds and fails. However, using machine learning tools and a plethora of data, it is now possible to obtain some insights into how a team performs. To showcase the utility of these new tools (i.e., expected goal value, expected save value, strategy-plots and passing quality measures), we first analyze the EPL 2015/16 season which a specific emphasis on the champions Leicester City, and then compare it to the current one. Finally, we show how these features can be used to predict future performance.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098121"}, {"primary_key": "3832540", "vector": [], "sparse_vector": [], "title": "Matching Restaurant Menus to Crowdsourced Food Data: A Scalable Machine Learning Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of how to match a formally structured restaurant menu item to a large database of less structured food items that has been collected via crowd-sourcing. At first glance, this problem scenario looks like a typical text matching problem that might possibly be solved with existing text similarity learning approaches. However, due to the unique nature of our scenario and the need for scalability, our problem imposes certain restrictions on possible machine learning approaches that we can employ. We propose a novel, practical, and scalable machine learning solution architecture, consisting of two major steps. First we use a query generation approach, based on a Markov Decision Process algorithm, to reduce the time complexity of searching for matching candidates. That is then followed by a re-ranking step, using deep learning techniques, to meet our required matching quality goals. It is important to note that our proposed solution architecture has already been deployed in a real application system serving tens of millions of users, and shows great potential for practical cases of user-entered text to structured text matching, especially when scalability is crucial.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098125"}, {"primary_key": "3832541", "vector": [], "sparse_vector": [], "title": "Similarity Forests.", "authors": ["Saket Sathe", "<PERSON><PERSON> <PERSON>"], "summary": "Random forests are among the most successful methods used in data mining because of their extraordinary accuracy and effectiveness. However, their use is primarily limited to multidimensional data because they sample features from the original data set. In this paper, we propose a method for extending random forests to work with any arbitrary set of data objects, as long as similarities can be computed among the data objects. Furthermore, since it is understood that similarity computation between all O(n2) pairs of n objects might be expensive, our method computes only a very small fraction of the O(n2) pairwise similarities between objects to construct the forests. Our results show that the proposed similarity forest approach is very efficient and accurate on a wide variety of data sets. Therefore, this paper significantly extends the applicability of random forest methods to arbitrary data domains. Furthermore, the approach even outperforms traditional random forests on multidimensional data. We show that similarity forests are robust to the noisy similarity values that are ubiquitous in real-world applications. In many practical settings, the similarity values between objects are incompletely specified because of the difficulty in collecting such values. Similarity forests can be used in such cases with straightforward modifications.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098046"}, {"primary_key": "3832542", "vector": [], "sparse_vector": [], "title": "Detecting Network Effects: Randomizing Over Randomized Experiments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Souvik Ghosh", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Randomized experiments, or A/B tests, are the standard approach for evaluating the causal effects of new product features, i.e., treatments. The validity of these tests rests on the \"stable unit treatment value assumption\" (SUTVA), which implies that the treatment only affects the behavior of treated users, and does not affect the behavior of their connections. Violations of SUTVA, common in features that exhibit network effects, result in inaccurate estimates of the causal effect of treatment. In this paper, we leverage a new experimental design for testing whether SUTVA holds, without making any assumptions on how treatment effects may spill over between the treatment and the control group. To achieve this, we simultaneously run both a completely randomized and a cluster-based randomized experiment, and then we compare the difference of the resulting estimates. We present a statistical test for measuring the significance of this difference and offer theoretical bounds on the Type I error rate. We provide practical guidelines for implementing our methodology on large-scale experimentation platforms. Importantly, the proposed methodology can be applied to settings in which a network is not necessarily observed but, if available, can be used in the analysis. Finally, we deploy this design to LinkedIn's experimentation platform and apply it to two online experiments, highlighting the presence of network effects and bias in standard A/B testing approaches in a real-world setting.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098192"}, {"primary_key": "3832543", "vector": [], "sparse_vector": [], "title": "When is a Network a Network?: Multi-Order Graphical Model Selection in Pathways and Temporal Networks.", "authors": ["<PERSON><PERSON>"], "summary": "We introduce a framework for the modeling of sequential data capturing pathways of varying lengths observed in a network. Such data are important, e.g., when studying click streams in the Web, travel patterns in transportation systems, information cascades in social networks, biological pathways, or time-stamped social interactions. While it is common to apply graph analytics and network analysis to such data, recent works have shown that temporal correlations can invalidate the results of such methods. This raises a fundamental question: When is a network abstraction of sequential data justified?Addressing this open question, we propose a framework that combines Markov chains of multiple, higher orders into a multi-layer graphical model that captures temporal correlations in pathways at multiple length scales simultaneously. We develop a model selection technique to infer the optimal number of layers of such a model and show that it outperforms baseline Markov order detection techniques. An application to eight real-world data sets on pathways and temporal networks shows that it allows to infer graphical models that capture both topological and temporal characteristics of such data. Our work highlights fallacies of network abstractions and provides a principled answer to the open question when they are justified. Generalizing network representations to multi-order graphical models, it opens perspectives for new data mining and knowledge discovery algorithms.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098145"}, {"primary_key": "3832545", "vector": [], "sparse_vector": [], "title": "Online Ranking with Constraints: A Primal-Dual Algorithm and Applications to Web Traffic-Shaping.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the online constrained ranking problem motivated by an application to web-traffic shaping: an online stream of sessions arrive in which, within each session, we are asked to rank items. The challenge involves optimizing the ranking in each session so that local vs. global objectives are controlled: within each session one wishes to maximize a reward (local) while satisfying certain constraints over the entire set of sessions (global). A typical application of this setup is that of page optimization in a web portal. We wish to rank items so that not only is user engagement maximized in each session, but also other business constraints (such as the number of views/clicks delivered to various publishing partners) are satisfied.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098025"}, {"primary_key": "3832546", "vector": [], "sparse_vector": [], "title": "A Practical Exploration System for Search Advertising.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Sachidanand Alle", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we describe an exploration system that was implemented by the search-advertising team of a prominent web-portal to address the cold ads problem. The cold ads problem refers to the situation where, when new ads are injected into the system by advertisers, the system is unable to assign an accurate quality to the ad (in our case, the click probability). As a consequence, the advertiser may suffer from low impression volumes for these cold ads, and the overall system may perform sub-optimally if the click probabilities for new ads are not learnt rapidly. We designed a new exploration system that was adapted to search advertising and the serving constraints of the system. In this paper, we define the problem, discuss the design details of the exploration system, new evaluation criteria, and present the performance metrics that were observed by us.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098041"}, {"primary_key": "3832547", "vector": [], "sparse_vector": [], "title": "The Fake vs Real Goods Problem: Microscopy and Machine Learning to the Rescue.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Counterfeiting of physical goods is a global problem amounting to nearly 7% of world trade. While there have been a variety of overt technologies like holograms and specialized barcodes and covert technologies like taggants and PUFs, these solutions have had a limited impact on the counterfeit market due to a variety of factors - clonability, cost or adoption barriers. In this paper, we introduce a new mechanism that uses machine learning algorithms on microscopic images of physical objects to distinguish between genuine and counterfeit versions of the same product. The underlying principle of our system stems from the idea that microscopic characteristics in a genuine product or a class of products (corresponding to the same larger product line), exhibit inherent similarities that can be used to distinguish these products from their corresponding counterfeit versions. A key building block for our system is a wide-angle microscopy device compatible with a mobile device that enables a user to easily capture the microscopic image of a large area of a physical object. Based on the captured microscopic images, we show that using machine learning algorithms (ConvNets and bag of words), one can generate a highly accurate classification engine for separating the genuine versions of a product from the counterfeit ones; this property also holds for \"super-fake\" counterfeits observed in the marketplace that are not easily discernible from the human eye. We describe the design of an end-to-end physical authentication system leveraging mobile devices, portable hardware and a cloud-based object verification ecosystem. We evaluate our system using a large dataset of 3 million images across various objects and materials such as fabrics, leather, pills, electronics, toys and shoes. The classification accuracy is more than 98% and we show how our system works with a cellphone to verify the authenticity of everyday objects.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098186"}, {"primary_key": "3832548", "vector": [], "sparse_vector": [], "title": "ReasoNet: Learning to Stop Reading in Machine Comprehension.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Teaching a computer to read and answer general questions pertaining to a document is a challenging yet unsolved problem. In this paper, we describe a novel neural network architecture called the Reasoning Network (ReasoNet) for machine comprehension tasks. ReasoNets make use of multiple turns to effectively exploit and then reason over the relation among queries, documents, and answers. Different from previous approaches using a fixed number of turns during inference, ReasoNets introduce a termination state to relax this constraint on the reasoning depth. With the use of reinforcement learning, ReasoNets can dynamically determine whether to continue the comprehension process after digesting intermediate results, or to terminate reading when it concludes that existing information is adequate to produce an answer. ReasoNets achieve superior performance in machine comprehension datasets, including unstructured CNN and Daily Mail datasets, the Stanford SQuAD dataset, and a structured Graph Reachability dataset.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098177"}, {"primary_key": "3832549", "vector": [], "sparse_vector": [], "title": "On Finding Socially Tenuous Groups for Online Social Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Existing research on finding social groups mostly focuses on dense subgraphs in social networks. However, finding socially tenuous groups also has many important applications. In this paper, we introduce the notion of k-triangles to measure the tenuity of a group. We then formulate a new research problem, Minimum k-Triangle Disconnected Group (MkTG), to find a socially tenuous group from online social networks. We prove that MkTG is NP-Hard and inapproximable within any ratio in arbitrary graphs but polynomial-time tractable in threshold graphs. Two algorithms, namely TERA and TERA-ADV, are designed to exploit graph-theoretical approaches for solving MkTG on general graphs effectively and efficiently. Experimental results on seven real datasets manifest that the proposed algorithms outperform existing approaches in both efficiency and solution quality.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097995"}, {"primary_key": "3832550", "vector": [], "sparse_vector": [], "title": "PReP: Path-Based Relevance from a Probabilistic Perspective in Heterogeneous Information Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jiawei Han"], "summary": "As a powerful representation paradigm for networked and multi-typed data, the heterogeneous information network (HIN) is ubiquitous. Meanwhile, defining proper relevance measures has always been a fundamental problem and of great pragmatic importance for network mining tasks. Inspired by our probabilistic interpretation of existing path-based relevance measures, we propose to study HIN relevance from a probabilistic perspective. We also identify, from real-world data, and propose to model cross-meta-path synergy, which is a characteristic important for defining path-based HIN relevance and has not been modeled by existing methods. A generative model is established to derive a novel path-based relevance measure, which is data-driven and tailored for each HIN. We develop an inference algorithm to find the maximum a posteriori (MAP) estimate of the model parameters, which entails non-trivial tricks. Experiments on two real-world datasets demonstrate the effectiveness of the proposed model and relevance measure.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097990"}, {"primary_key": "3832552", "vector": [], "sparse_vector": [], "title": "DenseAlert: Incremental Dense-Subtensor Detection in Tensor Streams.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Consider a stream of retweet events - how can we spot fraudulent lock-step behavior in such multi-aspect data (i.e., tensors) evolving over time? Can we detect it in real time, with an accuracy guarantee? Past studies have shown that dense subtensors tend to indicate anomalous or even fraudulent behavior in many tensor data, including social media, Wikipedia, and TCP dumps. Thus, several algorithms have been proposed for detecting dense subtensors rapidly and accurately. However, existing algorithms assume that tensors are static, while many real-world tensors, including those mentioned above, evolve over time.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098087"}, {"primary_key": "3832553", "vector": [], "sparse_vector": [], "title": "Anomaly Detection in Streams with Extreme Value Theory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Anomaly detection in time series has attracted considerable attention due to its importance in many real-world applications including intrusion detection, energy management and finance. Most approaches for detecting outliers rely on either manually set thresholds or assumptions on the distribution of data according to <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098144"}, {"primary_key": "3832554", "vector": [], "sparse_vector": [], "title": "Relay-Linking Models for Prominence and Obsolescence in Evolving Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Soumen Chakrabarti"], "summary": "The rate at which nodes in evolving social networks acquire links (friends, citations) shows complex temporal dynamics. Preferential attachment and link copying models, while enabling elegant analysis, only capture rich-gets-richer effects, not aging and decline. Recent aging models are complex and heavily parameterized; most involve estimating 1-3 parameters per node. These parameters are intrinsic: they explain decline in terms of events in the past of the same node, and do not explain, using the network, where the linking attention might go instead. We argue that traditional characterization of linking dynamics are insufficient to judge the faithfulness of models. We propose a new temporal sketch of an evolving graph, and introduce several new characterizations of a network's temporal dynamics. Then we propose a new family of frugal aging models with no per-node parameters and only two global parameters. Our model is based on a surprising inversion or undoing of triangle completion, where an old node relays a citation to a younger follower in its immediate vicinity. Despite very few parameters, the new family of models shows remarkably better fit with real data. Before concluding, we analyze temporal signatures for various research communities yielding further insights into their comparative dynamics. To facilitate reproducible research, we shall soon make all the codes and the processed dataset available in the public domain.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098146"}, {"primary_key": "3832556", "vector": [], "sparse_vector": [], "title": "PAMAE: Parallel k-Medoids Clustering with High Accuracy and Efficiency.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>-<PERSON>"], "summary": "The k-medoids algorithm is one of the best-known clustering algorithms. Despite this, however, it is not as widely used for big data analytics as the k-means algorithm, mainly because of its high computational complexity. Many studies have attempted to solve the efficiency problem of the k-medoids algorithm, but all such studies have improved efficiency at the expense of accuracy. In this paper, we propose a novel parallel k-medoids algorithm, which we call PAMAE, that achieves both high accuracy and high efficiency. We identify two factors---\"global search\" and \"entire data\"---that are essential to achieving high accuracy, but are also very time-consuming if considered simultaneously. Thus, our key idea is to apply them individually through two phases: parallel seeding and parallel refinement, neither of which is costly. The first phase performs global search over sampled data, and the second phase performs local search over entire data. Our theoretical analysis proves that this serial execution of the two phases leads to an accurate solution that would be achieved by global search over entire data. In order to validate the merit of our approach, we implement PAMAE on Spark as well as Hadoop and conduct extensive experiments using various real-world data sets on 12 Microsoft Azure machines (48 cores). The results show that PAMAE significantly outperforms most of recent parallel algorithms and, at the same time, produces a clustering quality as comparable as the previous most-accurate algorithm. The source code and data are available at https://github.com/jaegil/k-Medoid.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098098"}, {"primary_key": "3832557", "vector": [], "sparse_vector": [], "title": "Multi-Aspect Streaming Tensor Completion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Hancheng Ge", "<PERSON>", "<PERSON><PERSON>"], "summary": "Tensor completion has become an effective computational tool in many real-world data-driven applications. Beyond traditional static setting, with the increasing popularity of high velocity streaming data, it requires efficient online processing without reconstructing the whole model from scratch. Existing work on streaming tensor completion is usually built upon the assumption that tensors only grow in one mode. Unfortunately, the assumption does not hold in many real-world situations in which tensors may grow in multiple modes, i.e., multi-aspect streaming tensors. Efficiently modeling and completing these incremental tensors without sacrificing its effectiveness remains a challenging task due to the uncertainty of tensor mode changes and complex data structure of multi-aspect streaming tensors. To bridge this gap, we propose a Multi-Aspect Streaming Tensor completion framework (MAST) based on CANDECOMP/PARAFAC (CP) decomposition to track the subspace of general incremental tensors for completion. In addition, we investigate a special situation where time is one mode of the tensors, and leverage its extra structure information to improve the general framework towards higher effectiveness. Experimental results on four datasets collected from various real-world applications demonstrate the effectiveness and efficiency of the proposed framework.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098007"}, {"primary_key": "3832558", "vector": [], "sparse_vector": [], "title": "Automatic Application Identification from Billions of Files.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Understanding how to group a set of binary files into the piece of software they belong to is highly desirable for software profiling, malware detection, or enterprise audits, among many other applications. Unfortunately, it is also extremely challenging: there is absolutely no uniformity in the ways different applications rely on different files, in how binaries are signed, or in the versioning schemes used across different pieces of software. In this paper, we show that, by combining information gleaned from a large number of endpoints (millions of computers), we can accomplish large-scale application identification automatically and reliably. Our approach relies on collecting metadata on billions of files every day, summarizing it into much smaller \"sketches\", and performing approximate k-nearest neighbor clustering on non-metric space representations derived from these sketches. We design and implement our proposed system using Apache Spark, show that it can process billions of files in a matter of hours, and thus could be used for daily processing. We further show our system manages to successfully identify which files belong to which application with very high precision, and adequate recall.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098196"}, {"primary_key": "3832559", "vector": [], "sparse_vector": [], "title": "Scalable and Sustainable Deep Learning via Randomized Hashing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Current deep learning architectures are growing larger in order to learn from complex datasets. These architectures require giant matrix multiplication operations to train millions of parameters. Conversely, there is another growing trend to bring deep learning to low-power, embedded devices. The matrix operations, associated with the training and testing of deep networks, are very expensive from a computational and energy standpoint. We present a novel hashing-based technique to drastically reduce the amount of computation needed to train and test neural networks. Our approach combines two recent ideas, Adaptive Dropout and Randomized Hashing for Maximum Inner Product Search (MIPS), to select the nodes with the highest activations efficiently. Our new algorithm for deep learning reduces the overall computational cost of the forward and backward propagation steps by operating on significantly fewer nodes. As a consequence, our algorithm uses only 5% of the total multiplications, while keeping within 1% of the accuracy of the original model on average. A unique property of the proposed hashing-based back-propagation is that the updates are always sparse. Due to the sparse gradient updates, our algorithm is ideally suited for asynchronous, parallel training, leading to near-linear speedup, as the number of cores increases. We demonstrate the scalability and sustainability (energy efficiency) of our proposed algorithm via rigorous experimental evaluations on several datasets.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098035"}, {"primary_key": "3832560", "vector": [], "sparse_vector": [], "title": "MOLIERE: Automatic Biomedical Hypothesis Generation System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Hypothesis generation is becoming a crucial time-saving technique which allows biomedical researchers to quickly discover implicit connections between important concepts. Typically, these systems operate on domain-specific fractions of public medical data. MOLIERE, in contrast, utilizes information from over 24.5 million documents. At the heart of our approach lies a multi-modal and multi-relational network of biomedical objects extracted from several heterogeneous datasets from the National Center for Biotechnology Information (NCBI). These objects include but are not limited to scientific papers, keywords, genes, proteins, diseases, and diagnoses. We model hypotheses using Latent Dirichlet Allocation applied on abstracts found near shortest paths discovered within this network, and demonstrate the effectiveness of MOLIERE by performing hypothesis generation on historical data. Our network, implementation, and resulting data are all publicly available for the broad scientific community.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098057"}, {"primary_key": "3832561", "vector": [], "sparse_vector": [], "title": "AnnexML: Approximate Nearest Neighbor Search for Extreme Multi-label Classification.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Extreme multi-label classification methods have been widely used in Web-scale classification tasks such as Web page tagging and product recommendation. In this paper, we present a novel graph embedding method called \"AnnexML\". At the training step, AnnexML constructs a k-nearest neighbor graph of label vectors and attempts to reproduce the graph structure in the embedding space. The prediction is efficiently performed by using an approximate nearest neighbor search method that efficiently explores the learned k-nearest neighbor graph in the embedding space. We conducted evaluations on several large-scale real-world data sets and compared our method with recent state-of-the-art methods. Experimental results show that our AnnexML can significantly improve prediction accuracy, especially on data sets that have larger a label space. In addition, AnnexML improves the trade-off between prediction time and accuracy. At the same level of accuracy, the prediction time of AnnexML was up to 58 times faster than that of SLEEC, which is a state-of-the-art embedding-based method.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097987"}, {"primary_key": "3832562", "vector": [], "sparse_vector": [], "title": "End-to-end Learning for Short Text Expansion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Effectively making sense of short texts is a critical task for many real world applications such as search engines, social media services, and recommender systems. The task is particularly challenging as a short text contains very sparse information, often too sparse for a machine learning algorithm to pick up useful signals. A common practice for analyzing short text is to first expand it with external information, which is usually harvested from a large collection of longer texts. In literature, short text expansion has been done with all kinds of heuristics. We propose an end-to-end solution that automatically learns how to expand short text to optimize a given learning task. A novel deep memory network is proposed to automatically find relevant information from a collection of longer documents and reformulate the short text through a gating mechanism. Using short text classification as a demonstrating task, we show that the deep memory network significantly outperforms classical text expansion methods with comprehensive experiments on real world data sets.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098166"}, {"primary_key": "3832563", "vector": [], "sparse_vector": [], "title": "Quick Access: Building a Smart Experience for Google Drive.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Google Drive is a cloud storage and collaboration service used by hundreds of millions of users around the world. Quick Access is a new feature in Google Drive that surfaces the most relevant documents when a user visits the home screen. Our metrics show that users locate their documents in half the time with this feature compared to previous approaches. The development of Quick Access illustrates many general challenges and constraints associated with practical machine learning such as protecting user privacy, working with data services that are not designed with machine learning in mind, and evolving product definitions. We believe that the lessons learned from this experience will be useful to practitioners tackling a wide range of applied machine learning problems.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098048"}, {"primary_key": "3832564", "vector": [], "sparse_vector": [], "title": "Construction of Directed 2K Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Minas Gjoka"], "summary": "We study the problem of generating synthetic graphs that resemble real-world directed graphs in terms of their degree correlations. In order to capture degree correlation specifically for directed graphs, we define directed 2K (D2K) as those graphs with a given directed degree sequence (DDS) and a given target joint degree and attribute matrix (JDAM). We provide necessary and sufficient conditions for a target D2K to be realizable and we design an efficient algorithm that generates graph realizations with exactly the target D2K. We apply our algorithm to generate synthetic graphs that target real-world directed graphs (such as Twitter), and we demonstrate its benefits compared to state-of-the-art construction algorithms.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098119"}, {"primary_key": "3832567", "vector": [], "sparse_vector": [], "title": "Interpretable Predictions of Tree-based Ensembles via Actionable Feature Tweaking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine-learned models are often described as \"black boxes\". In many real-world applications however, models may have to sacrifice predictive power in favour of human-interpretability. When this is the case, feature engineering becomes a crucial task, which requires significant and time-consuming human effort. Whilst some features are inherently static, representing properties that cannot be influenced (e.g., the age of an individual), others capture characteristics that could be adjusted (e.g., the daily amount of carbohydrates taken). Nonetheless, once a model is learned from the data, each prediction it makes on new instances is irreversible - assuming every instance to be a static point located in the chosen feature space. There are many circumstances however where it is important to understand (i) why a model outputs a certain prediction on a given instance, (ii) which adjustable features of that instance should be modified, and finally (iii) how to alter such a prediction when the mutated instance is input back to the model. In this paper, we present a technique that exploits the internals of a tree-based ensemble classifier to offer recommendations for transforming true negative instances into positively predicted ones. We demonstrate the validity of our approach using an online advertising application. First, we design a Random Forest classifier that effectively separates between two types of ads: low (negative) and high (positive) quality ads (instances). Then, we introduce an algorithm that provides recommendations that aim to transform a low quality ad (negative instance) into a high quality one (positive instance). Finally, we evaluate our approach on a subset of the active inventory of a large ad network, Yahoo Gemini.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098039"}, {"primary_key": "3832568", "vector": [], "sparse_vector": [], "title": "The Simpler The Better: A Unified Approach to Predicting Original Taxi Demands based on Large-Scale Online Platforms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jieping Ye", "Weifeng Lv"], "summary": "Taxi-calling apps are gaining increasing popularity for their efficiency in dispatching idle taxis to passengers in need. To precisely balance the supply and the demand of taxis, online taxicab platforms need to predict the Unit Original Taxi Demand (UOTD), which refers to the number of taxi-calling requirements submitted per unit time (e.g., every hour) and per unit region (e.g., each POI). Predicting UOTD is non-trivial for large-scale industrial online taxicab platforms because both accuracy and flexibility are essential. Complex non-linear models such as GBRT and deep learning are generally accurate, yet require labor-intensive model redesign after scenario changes (e.g., extra constraints due to new regulations). To accurately predict UOTD while remaining flexible to scenario changes, we propose LinUOTD, a unified linear regression model with more than 200 million dimensions of features. The simple model structure eliminates the need of repeated model redesign, while the high-dimensional features contribute to accurate UOTD prediction. We further design a series of optimization techniques for efficient model training and updating. Evaluations on two large-scale datasets from an industrial online taxicab platform verify that LinUOTD outperforms popular non-linear models in accuracy. We envision our experiences to adopt simple linear models with high-dimensional features in UOTD prediction as a pilot study and can shed insights upon other industrial large-scale spatio-temporal prediction problems.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098018"}, {"primary_key": "3832569", "vector": [], "sparse_vector": [], "title": "Learning to Generate Rock Descriptions from Multivariate Well Logs with Hierarchical Attention.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the shale oil & gas industry, operators are looking toward big data analytics to optimize operations and reduce cost. In this paper, we mainly focus on how to assist operators in understanding the subsurface formation, thereby helping them make optimal decisions. A large number of geology reports and well logs describing the sub-surface have been accumulated over years. Issuing geology reports is more time consuming and depends more on the expertise of engineers than acquiring the well logs. To assist in issuing geology reports, we propose an encoder-decoder-based model to automatically generate rock descriptions in human-readable format from multivariate well logs. Due to the different formats of data, this task differs dramatically from image and video captioning. The challenges are how to model structured rock descriptions and leverage the information in multivariate well logs. To achieve this, we design a hierarchical structure and two forms of attention for the decoder. Extensive validations are conducted on public well data of North Dakota in the United States. We show that our model is effective in generating rock descriptions. The two forms of attention enable the provision of a better insight into relations between well-log types and rock properties with our model from a data-driven perspective.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098132"}, {"primary_key": "3832570", "vector": [], "sparse_vector": [], "title": "Multi-view Learning over Retinal Thickness and Visual Sensitivity on Glaucomatous Eyes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dense measurements of visual-field, which is necessary to detect glaucoma, is known as very costly and labor intensive. Recently, measurement of retinal-thickness can be less costly than measurement of visual-field. Thus, it is sincerely desired that the retinal-thickness could be transformed into visual-sensitivity data somehow. In this paper, we propose two novel methods to estimate the sensitivity of the visual-field with SITA-Standard mode 10-2 resolution using retinal-thickness data measured with optical coherence tomography (OCT). The first method called Affine-Structured Non-negative Matrix Factorization (ASNMF) which is able to cope with both the estimation of visual-field and the discovery of deep glaucoma knowledge. While, the second is based on Convolutional Neural Networks (CNNs) which demonstrates very high estimation performance. These methods are kinds of multi-view learning methods because they utilize visual-field and retinal thickness data simultaneously. We experimentally tested the performance of our methods from several perspectives. We found that ASNMF worked better for relatively small data size while CNNs did for relatively large data size. In addition, some clinical knowledge are discovered via ASNMF. To the best of our knowledge, this is the first paper to address the dense estimation of the visual-field based on the retinal-thickness data.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098194"}, {"primary_key": "3832571", "vector": [], "sparse_vector": [], "title": "Optimized Risk Scores.", "authors": ["Berk Ustun", "<PERSON>"], "summary": "Risk scores are simple classification models that let users quickly assess risk by adding, subtracting, and multiplying a few small numbers. Such models are widely used in healthcare and criminal justice, but are often built ad hoc. In this paper, we present a principled approach to learn risk scores that are fully optimized for feature selection, integer coefficients, and operational constraints. We formulate the risk score problem as a mixed integer nonlinear program, and present a new cutting plane algorithm to efficiently recover its optimal solution. Our approach can fit optimized risk scores in a way that scales linearly with the sample size of a dataset, provides a proof of optimality, and obeys complex constraints without parameter tuning. We illustrate these benefits through an extensive set of numerical experiments, and an application where we build a customized risk score for ICU seizure prediction.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098161"}, {"primary_key": "3832572", "vector": [], "sparse_vector": [], "title": "DeepSD: Generating High Resolution Climate Change Projections through Single Image Super-Resolution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>"], "summary": "The impacts of climate change are felt by most critical systems, such as infrastructure, ecological systems, and power-plants. However, contemporary Earth System Models (ESM) are run at spatial resolutions too coarse for assessing effects this localized. Local scale projections can be obtained using statistical downscaling, a technique which uses historical climate observations to learn a low-resolution to high-resolution mapping. Depending on statistical modeling choices, downscaled projections have been shown to vary significantly terms of accuracy and reliability. The spatio-temporal nature of the climate system motivates the adaptation of super-resolution image processing techniques to statistical downscaling. In our work, we present DeepSD, a generalized stacked super resolution convolutional neural network (SRCNN) framework for statistical downscaling of climate variables. DeepSD augments SRCNN with multi-scale input channels to maximize predictability in statistical downscaling. We provide a comparison with Bias Correction Spatial Disaggregation as well as three Automated-Statistical Downscaling approaches in downscaling daily precipitation from 1 degree (~100km) to 1/8 degrees (~12.5km) over the Continental United States. Furthermore, a framework using the NASA Earth Exchange (NEX) platform is discussed for downscaling more than 20 ESM models with multiple emission scenarios.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098004"}, {"primary_key": "3832574", "vector": [], "sparse_vector": [], "title": "Randomized Feature Engineering as a Fast and Accurate Alternative to Kernel Methods.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "Feature engineering has found increasing interest in recent years because of its ability to improve the effectiveness of various machine learning models. Although tailored feature engineering methods have been designed for various domains, there are few that simulate the consistent effectiveness of kernel methods. At the core, the success of kernel methods is achieved by using similarity functions that emphasize local variations in similarity. Unfortunately, this ability comes at the price of the high level of computational resources required and the inflexibility of the representation as it only provides the similarity of two data points instead of vector representations of each data point; while the vector representations can be readily used as input to facilitate various models for different tasks. Furthermore, kernel methods are also highly susceptible to overfitting and noise and it cannot capture the variety of data locality. In this paper, we first analyze the inner working and weaknesses of kernel method, which serves as guidance for designing feature engineering. With the guidance, we explore the use of randomized methods for feature engineering by capturing multi-granular locality of data. This approach has the merit of being time and space efficient for feature construction. Furthermore, the approach is resistant to overfitting and noise because the randomized approach naturally enables fast and robust ensemble methods. Extensive experiments on a number of real world datasets are conducted to show the effectiveness of the approach for various tasks such as clustering, classification and outlier detection.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098001"}, {"primary_key": "3832575", "vector": [], "sparse_vector": [], "title": "No Longer Sleeping with a Bomb: A Duet System for Protecting Urban Safety from Dangerous Goods.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent years have witnessed the continuous growth of megalopolises worldwide, which makes urban safety a top priority in modern city life. Among various threats, dangerous goods such as gas and hazardous chemicals transported through and around cities have increasingly become the deadly \"bomb\" we sleep with every day. In both academia and government, tremendous efforts have been dedicated to dealing with dangerous goods transportation (DGT) issues, but further study is still in great need to quantify the problem and explore its intrinsic dynamics in a big data perspective. In this paper, we present a novel system called DGeye, which features a \"duet\" between DGT trajectory data and human mobility data for risky zones identification. Moreover, DGeye innovatively takes risky patterns as the keystones in DGT management, and builds causality networks among them for pain point identification, attribution and prediction. Experiments on both Beijing and Tianjin cities demonstrate the effectiveness of DGeye. In particular, the report generated by DGeye driven the Beijing government to lay down gas pipelines for the famous Guijie food street.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097985"}, {"primary_key": "3832577", "vector": [], "sparse_vector": [], "title": "Human Mobility Synchronization and Trip Purpose Detection with Mixture of Hawkes Processes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Wenqing Hu", "<PERSON><PERSON> <PERSON>"], "summary": "While exploring human mobility can benefit many applications such as smart transportation, city planning, and urban economics, there are two key questions that need to be answered: (i) What is the nature of the spatial diffusion of human mobility across regions with different urban functions? (ii) How to spot and trace the trip purposes of human mobility trajectories? To answer these questions, we study large-scale and city-wide taxi trajectories; and furtherly organize them as arrival sequences according to the chronological arrival time. We figure out an important property across different regions from the arrival sequences, namely human mobility synchronization effect, which can be exploited to explain the phenomenon that two regions have similar arrival patterns in particular time periods if they share similar urban functions. In addition, the arrival sequences are mixed by arrival events with distinct trip purposes, which can be revealed by the regional environment of both the origins and destinations. To that end, in this paper, we develop a joint model that integrates Mixture of Hawkes Process (MHP) with a hierarchical topic model to capture the arrival sequences with mixed trip purposes. Essentially, the human mobility synchronization effect is encoded as a synchronization rate in the MHP; while the regional environment is modeled by introducing latent Trip Purpose and POI Topic to generate the Point of Interests (POIs) in the regions. Moreover, we provide an effective inference algorithm for parameter learning. Finally, we conduct intensive experiments on synthetic data and real-world data, and the experimental results have demonstrated the effectiveness of the proposed model.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098067"}, {"primary_key": "3832578", "vector": [], "sparse_vector": [], "title": "A Location-Sentiment-Aware Recommender System for Both Home-Town and Out-of-Town Users.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hong<PERSON> Yin", "<PERSON><PERSON>", "<PERSON>"], "summary": "Spatial item recommendation has become an important means to help people discover interesting locations, especially when people pay a visit to unfamiliar regions. Some current researches are focusing on modelling individual and collective geographical preferences for spatial item recommendation based on users' check-in records, but they fail to explore the phenomenon of user interest drift across geographical regions, i.e., users would show different interests when they travel to different regions. Besides, they ignore the influence of public comments for subsequent users' check-in behaviors. Specifically, it is intuitive that users would refuse to check in to a spatial item whose historical reviews seem negative overall, even though it might fit their interests. Therefore, it is necessary to recommend the right item to the right user at the right location. In this paper, we propose a latent probabilistic generative model called LSARS to mimic the decision-making process of users' check-in activities both in home-town and out-of-town scenarios by adapting to user interest drift and crowd sentiments, which can learn location-aware and sentiment-aware individual interests from the contents of spatial items and user reviews. Due to the sparsity of user activities in out-of-town regions, LSARS is further designed to incorporate the public preferences learned from local users' check-in behaviors. Finally, we deploy LSARS into two practical application scenes: spatial item recommendation and target user discovery. Extensive experiments on two large-scale location-based social networks (LBSNs) datasets show that LSARS achieves better performance than existing state-of-the-art methods.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098122"}, {"primary_key": "3832579", "vector": [], "sparse_vector": [], "title": "Adversary Resistant Deep Neural Networks with an Application to Malware Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> II", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Outside the highly publicized victories in the game of Go, there have been numerous successful applications of deep learning in the fields of information retrieval, computer vision, and speech recognition. In cybersecurity, an increasing number of companies have begun exploring the use of deep learning (DL) in a variety of security tasks with malware detection among the more popular. These companies claim that deep neural networks (DNNs) could help turn the tide in the war against malware infection. However, DNNs are vulnerable to adversarial samples, a shortcoming that plagues most, if not all, statistical and machine learning models. Recent research has demonstrated that those with malicious intent can easily circumvent deep learning-powered malware detection by exploiting this weakness.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098158"}, {"primary_key": "3832580", "vector": [], "sparse_vector": [], "title": "Structural Deep Brain Network Mining.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Bokai Cao", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mining from neuroimaging data is becoming increasingly popular in the field of healthcare and bioinformatics, due to its potential to discover clinically meaningful structure patterns that could facilitate the understanding and diagnosis of neurological and neuropsychiatric disorders. Most recent research concentrates on applying subgraph mining techniques to discover connected subgraph patterns in the brain network. However, the underlying brain network structure is complicated. As a shallow linear model, subgraph mining cannot capture the highly non-linear structures, resulting in sub-optimal patterns. Therefore, how to learn representations that can capture the highly non-linearity of brain networks and preserve the underlying structures is a critical problem.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097988"}, {"primary_key": "3832581", "vector": [], "sparse_vector": [], "title": "A Hybrid Framework for Text Modeling with Convolutional RNN.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we introduce a generic inference hybrid framework for Convolutional Recurrent Neural Network (conv-RNN) of semantic modeling of text, seamless integrating the merits on extracting different aspects of linguistic information from both convolutional and recurrent neural network structures and thus strengthening the semantic understanding power of the new framework. Besides, based on conv-RNN, we also propose a novel sentence classification model and an attention based answer selection model with strengthening power for the sentence matching and classification respectively. We validate the proposed models on a very wide variety of data sets, including two challenging tasks of answer selection (AS) and five benchmark datasets for sentence classification (SC). To the best of our knowledge, it is by far the most complete comparison results in both AS and SC. We empirically show superior performances of conv-RNN in these different challenging tasks and benchmark datasets and also summarize insights on the performances of other state-of-the-arts methodologies.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098140"}, {"primary_key": "3832582", "vector": [], "sparse_vector": [], "title": "Multi-Modality Disease Modeling via Collective Deep Matrix Factorization.", "authors": ["<PERSON>", "Mengying Sun", "<PERSON>", "<PERSON>", "Shuiwang Ji", "<PERSON><PERSON><PERSON>"], "summary": "Alzheimer's disease (AD), one of the most common causes of dementia, is a severe irreversible neurodegenerative disease that results in loss of mental functions. The transitional stage between the expected cognitive decline of normal aging and AD, mild cognitive impairment (MCI), has been widely regarded as a suitable time for possible therapeutic intervention. The challenging task of MCI detection is therefore of great clinical importance, where the key is to effectively fuse predictive information from multiple heterogeneous data sources collected from the patients. In this paper, we propose a framework to fuse multiple data modalities for predictive modeling using deep matrix factorization, which explores the non-linear interactions among the modalities and exploits such interactions to transfer knowledge and enable high performance prediction. Specifically, the proposed collective deep matrix factorization decomposes all modalities simultaneously to capture non-linear structures of the modalities in a supervised manner, and learns a modality specific component for each modality and a modality invariant component across all modalities. The modality invariant component serves as a compact feature representation of patients that has high predictive power. The modality specific components provide an effective means to explore imaging genetics, yielding insights into how imaging and genotype interact with each other non-linearly in the AD pathology. Extensive empirical studies using various data modalities provided by Alzheimer's Disease Neuroimaging Initiative (ADNI) demonstrate the effectiveness of the proposed method for fusing heterogeneous modalities.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098164"}, {"primary_key": "3832583", "vector": [], "sparse_vector": [], "title": "Dynamic Attention Deep Model for Article Recommendation by Learning Human Editors&apos; Demonstration.", "authors": ["<PERSON><PERSON><PERSON>", "Lantao Yu", "<PERSON><PERSON>", "Guanyu Tao", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As aggregators, online news portals face great challenges in continuously selecting a pool of candidate articles to be shown to their users. Typically, those candidate articles are recommended manually by platform editors from a much larger pool of articles aggregated from multiple sources. Such a hand-pick process is labor intensive and time-consuming. In this paper, we study the editor article selection behavior and propose a learning by demonstration system to automatically select a subset of articles from the large pool. Our data analysis shows that (i) editors' selection criteria are non-explicit, which are less based only on the keywords or topics, but more depend on the quality and attractiveness of the writing from the candidate article, which is hard to capture based on traditional bag-of-words article representation. And (ii) editors' article selection behaviors are dynamic: articles with different data distribution come into the pool everyday and the editors' preference varies, which are driven by some underlying periodic or occasional patterns. To address such problems, we propose a meta-attention model across multiple deep neural nets to (i) automatically catch the editors' underlying selection criteria via the automatic representation learning of each article and its interaction with the meta data and (ii) adaptively capture the change of such criteria via a hybrid attention model. The attention model strategically incorporates multiple prediction models, which are trained in previous days. The system has been deployed in a commercial article feed platform. A 9-day A/B testing has demonstrated the consistent superiority of our proposed model over several strong baselines.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098096"}, {"primary_key": "3832584", "vector": [], "sparse_vector": [], "title": "Formative Essay Feedback Using Predictive Scoring Models.", "authors": ["Bronwyn Woods", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A major component of secondary education is learning to write effectively, a skill which is bolstered by repeated practice with formative guidance. However, providing focused feedback to every student on multiple drafts of each essay throughout the school year is a challenge for even the most dedicated of teachers. This paper first establishes a new ordinal essay scoring model and its state of the art performance compared to recent results in the Automated Essay Scoring field. Extending this model, we describe a method for using prediction on realistic essay variants to give rubric-specific formative feedback to writers. This method is used in Revision Assistant, a deployed data-driven educational product that provides immediate, rubric-specific, sentence-level feedback to students to supplement teacher guidance. We present initial evaluations of this feedback generation, both offline and in deployment.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098160"}, {"primary_key": "3832585", "vector": [], "sparse_vector": [], "title": "Structural Event Detection from Log Messages.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A wide range of modern web applications are only possible because of the composable nature of the web services they are built upon. It is, therefore, often critical to ensure proper functioning of these web services. As often, the server-side of web services is not directly accessible, several log message based analysis have been developed to monitor the status of web services. Existing techniques focus on using clusters of messages (log patterns) to detect important system events. We argue that meaningful system events are often representable by groups of cohesive log messages and the relationships among these groups. We propose a novel method to mine structural events as directed workflow graphs (where nodes represent log patterns, and edges represent relations among patterns). The structural events are inclusive and correspond to interpretable episodes in the system. The problem is non-trivial due to the nature of log data: (i) Individual log messages contain limited information, and (ii) Log messages in a large scale web system are often interleaved even though the log messages from individual components are ordered. As a result, the patterns and relationships mined directly from the messages and their ordering can be erroneous and unreliable in practice. Our solution is based on the observation that meaningful log patterns and relations often form workflow structures that are connected. Our method directly models the overall quality of structural events. Through both qualitative and quantitative experiments on real world datasets, we demonstrate the effectiveness and the expressiveness of our event detection method.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098124"}, {"primary_key": "3832586", "vector": [], "sparse_vector": [], "title": "Retrospective Higher-Order Markov Processes for User Trails.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Users form information trails as they checkin with a geolocation, rate items, or consume media. A common problem is to predict what a user might do next for the purposes of guidance, recommendation, or prefetching. Markov chains models have been widely used methods to study such sequences of data. First-order Markov chains are easy to estimate, but lack accuracy when history matters. Higher-order Markov chains, in contrast, have too many parameters and suffer from overfitting the training data. Fitting these parameters with regularization and smoothing only offers mild improvements. In this paper we propose the retrospective higher-order Markov process (RHOMP) as a low-parameter model for such sequences. This model is a special case of a higher-order Markov chain where the transitions depend retrospectively on a single history state instead of an arbitrary combination of history states. There are two immediate computational advantages: the number of parameters is linear in the order of the Markov chain and the model can be fit to large state spaces. Furthermore, by providing a specific structure to the higher-order chain, RHOMPs improve the model accuracy by efficiently utilizing history states without risks of overfitting the data. We demonstrate how to estimate a RHOMP from data and we demonstrate the effectiveness of our method on various real application datasets spanning geolocation data, review sequences, and business locations. The RHOMP model uniformly outperforms higher-order Markov chains, Kneser-Ney regularization, and tensor factorizations in terms of prediction accuracy.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098127"}, {"primary_key": "3832587", "vector": [], "sparse_vector": [], "title": "Large-scale Collaborative Ranking in Near-Linear Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we consider the Collaborative Ranking (CR) problem for recommendation systems. Given a set of pairwise preferences between items for each user, collaborative ranking can be used to rank un-rated items for each user, and this ranking can be naturally used for recommendation. It is observed that collaborative ranking algorithms usually achieve better performance since they directly minimize the ranking loss; however, they are rarely used in practice due to the poor scalability. All the existing CR algorithms have time complexity at least O(|Ω|r) per iteration, where r is the target rank and |Ω| is number of pairs which grows quadratically with number of ratings per user. For example, the Netflix data contains totally 20 billion rating pairs, and at this scale all the current algorithms have to work with significant subsampling, resulting in poor prediction on testing data.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098071"}, {"primary_key": "3832588", "vector": [], "sparse_vector": [], "title": "Decomposed Normalized Maximum Likelihood Codelength Criterion for Selecting Hierarchical Latent Variable Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new model selection criterion based on the minimum description length principle in a name of the decomposed normalized maximum likelihood criterion. Our criterion can be applied to a large class of hierarchical latent variable models, such as the Naive Bayes models, stochastic block models and latent Dirichlet allocations, for which many conventional information criteria cannot be straightforwardly applied due to irregularity of latent variable models. Our method also has an advantage that it can be exactly evaluated without asymptotic approximation with small time complexity. Our experiments using synthetic and real data demonstrated validity of our method in terms of computational efficiency and model selection accuracy, while our criterion especially dominated the other criteria when sample size is small and when data are noisy.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098110"}, {"primary_key": "3832589", "vector": [], "sparse_vector": [], "title": "Learning Temporal State of Diabetes Patients via Combining Behavioral and Demographic Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Long H. Vu", "<PERSON>ak <PERSON>"], "summary": "Diabetes is a serious disease affecting a large number of people. Although there is no cure for diabetes, it can be managed. Especially, with advances in sensor technology, lots of data may lead to the improvement of diabetes management, if properly mined. However, there usually exists noise or errors in the observed behavioral data which poses challenges in extracting meaningful knowledge. To overcome this challenge, we learn the latent state which represents the patient's condition. Such states should be inferred from the behavioral data but unknown a priori. In this paper, we propose a novel framework to capture the trajectory of latent states for patients from behavioral data while exploiting their demographic differences and similarities to other patients. We conduct a hypothesis test to illustrate the importance of the demographic data in diabetes management, and validate that each behavioral feature follows an exponential or a Gaussian distribution. Integrating these aspects, we use a Demographic feature restricted hidden Markov model (DfrHMM) to estimate the trajectory of latent states by integrating the demographic and behavioral data. In DfrHMM, the latent state is mainly determined by the previous state and the demographic features in a nonlinear way. Markov Chain Monte Carlo techniques are used for model parameter estimation. Experiments on synthetic and real datasets show that DfrHMM is effective in diabetes management.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098100"}, {"primary_key": "3832590", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Distributed Multi-Task Learning with Asynchronous Updates.", "authors": ["<PERSON><PERSON>", "Inci M. Baytas", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many data mining applications involve a set of related learning tasks. Multi-task learning (MTL) is a learning paradigm that improves generalization performance by transferring knowledge among those tasks. MTL has attracted so much attention in the community, and various algorithms have been successfully developed. Recently, distributed MTL has also been studied for related tasks whose data is distributed across different geographical regions. One prominent challenge of the distributed MTL frameworks is to maintain the privacy of the data. The distributed data may contain sensitive and private information such as patients' records and registers of a company. In such cases, distributed MTL frameworks are required to preserve the privacy of the data. In this paper, we propose a novel privacy-preserving distributed MTL framework to address this challenge. A privacy-preserving proximal gradient algorithm, which asynchronously updates models of the learning tasks, is introduced to solve a general class of MTL formulations. The proposed asynchronous approach is robust against network delays and provides a guaranteed differential privacy through carefully designed perturbation. Theoretical guarantees of the proposed algorithm are derived and supported by the extensive experimental results.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098152"}, {"primary_key": "3832592", "vector": [], "sparse_vector": [], "title": "Evaluating U.S. Electoral Representation with a Joint Statistical Model of Congressional Roll-Calls, Legislative Text, and Voter Registration Data.", "authors": ["Zhengming Xing", "Sunshine Hillygus", "<PERSON>"], "summary": "Extensive information on 3 million randomly sampled United States citizens is used to construct a statistical model of constituent preferences for each U.S. congressional district. This model is linked to the legislative voting record of the legislator from each district, yielding an integrated model for constituency data, legislative roll-call votes, and the text of the legislation. The model is used to examine the extent to which legislators' voting records are aligned with constituent preferences, and the implications of that alignment (or lack thereof) on subsequent election outcomes. The analysis is based on a Bayesian formalism, with fast inference via a stochastic variational Bayesian analysis.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098151"}, {"primary_key": "3832593", "vector": [], "sparse_vector": [], "title": "HoORaYs: High-order Optimization of Rating Distance for Recommender Systems.", "authors": ["<PERSON><PERSON> Xu", "<PERSON>", "Hanghang Tong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Latent factor models have become a prevalent method in recommender systems, to predict users' preference on items based on the historical user feedback. Most of the existing methods, explicitly or implicitly, are built upon the first-order rating distance principle, which aims to minimize the difference between the estimated and real ratings. In this paper, we generalize such first-order rating distance principle and propose a new latent factor model (HoORaYs) for recommender systems. The core idea of the proposed method is to explore high-order rating distance, which aims to minimize not only (i) the difference between the estimated and real ratings of the same (user, item) pair (i.e., the first-order rating distance), but also (ii) the difference between the estimated and real rating difference of the same user across different items (i.e., the second-order rating distance). We formulate it as a regularized optimization problem, and propose an effective and scalable algorithm to solve it. Our analysis from the geometry and Bayesian perspectives indicate that by exploring the high-order rating distance, it helps to reduce the variance of the estimator, which in turns leads to better generalization performance (e.g., smaller prediction error). We evaluate the proposed method on four real-world data sets, two with explicit user feedback and the other two with implicit user feedback. Experimental results show that the proposed method consistently outperforms the state-of-the-art methods in terms of the prediction accuracy.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098019"}, {"primary_key": "3832594", "vector": [], "sparse_vector": [], "title": "Collaboratively Improving Topic Discovery and Word Embeddings by Coordinating Global and Local Contexts.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A text corpus typically contains two types of context information -- global context and local context. Global context carries topical information which can be utilized by topic models to discover topic structures from the text corpus, while local context can train word embeddings to capture semantic regularities reflected in the text corpus. This encourages us to exploit the useful information in both the global and the local context information. In this paper, we propose a unified language model based on matrix factorization techniques which 1) takes the complementary global and local context information into consideration simultaneously, and 2) models topics and learns word embeddings collaboratively. We empirically show that by incorporating both global and local context, this collaborative model can not only significantly improve the performance of topic discovery over the baseline topic models, but also learn better word embeddings than the baseline word embedding models. We also provide qualitative analysis that explains how the cooperation of global and local context information can result in better topic structures and word embeddings.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098009"}, {"primary_key": "3832595", "vector": [], "sparse_vector": [], "title": "Convex Factorization Machine for Toxicogenomics Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sul<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the convex factorization machine (CFM), which is a convex variant of the widely used Factorization Machines (FMs). Specifically, we employ a linear+quadratic model and regularize the linear term with the ℓ2-regularizer and the quadratic term with the trace norm regularizer. Then, we formulate the CFM optimization as a semidefinite programming problem and propose an efficient optimization procedure with <PERSON><PERSON>'s algorithm. A key advantage of CFM over existing FMs is that it can find a globally optimal solution, while FMs may get a poor locally optimal solution since the objective function of FMs is non-convex. In addition, the proposed algorithm is simple yet effective and can be implemented easily. Finally, CFM is a general factorization method and can also be used for other factorization problems, including multi-view matrix factorization and tensor completion problems, in various domains including toxicogenomics and bioinformatics. Through synthetic and traditionally used movielens datasets, we first show that the proposed CFM achieves results competitive to FMs. We then show in a toxicogenomics prediction task that CFM predicts the toxic outcomes of a collection of drugs better than a state-of-the-art tensor factorization method.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098103"}, {"primary_key": "3832596", "vector": [], "sparse_vector": [], "title": "Distributed Local Outlier Detection in Big Data.", "authors": ["Yizhou Yan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we present the first distributed solution for the Local Outlier Factor (LOF) method -- a popular outlier detection technique shown to be very effective for datasets with skewed distributions. As datasets increase radically in size, highly scalable LOF algorithms leveraging modern distributed infrastructures are required. This poses significant challenges due to the complexity of the LOF definition, and a lack of access to the entire dataset at any individual compute machine. Our solution features a distributed LOF pipeline framework, called DLOF. Each stage of the LOF computation is conducted in a fully distributed fashion by leveraging our invariant observation for intermediate value management. Furthermore, we propose a data assignment strategy which ensures that each machine is self-sufficient in all stages of the LOF pipeline, while minimizing the number of data replicas. Based on the convergence property derived from analyzing this strategy in the context of real world datasets, we introduce a number of data-driven optimization strategies. These strategies not only minimize the computation costs within each stage, but also eliminate unnecessary communication costs by aggressively pushing the LOF computation into the early stages of the DLOF pipeline. Our comprehensive experimental study using both real and synthetic datasets confirms the efficiency and scalability of our approach to terabyte level data.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098179"}, {"primary_key": "3832597", "vector": [], "sparse_vector": [], "title": "Scalable Top-n Local Outlier Detection.", "authors": ["Yizhou Yan", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Local Outlier Factor (LOF) method that labels all points with their respective LOF scores to indicate their status is known to be very effective for identifying outliers in datasets with a skewed distribution. Since outliers by definition are the absolute minority in a dataset, the concept of Top-N local outlier was proposed to discover the n points with the largest LOF scores. The detection of the Top-N local outliers is prohibitively expensive, since it requires huge number of high complexity k-nearest neighbor (kNN) searches. In this work, we present the first scalable Top-N local outlier detection approach called TOLF. The key innovation of TOLF is a multi-granularity pruning strategy that quickly prunes most points from the set of potential outlier candidates without computing their exact LOF scores or even without conducting any kNN search for them. Our customized density-aware indexing structure not only effectively supports the pruning strategy, but also accelerates the $k$NN search. Our extensive experimental evaluation on OpenStreetMap, SDSS, and TIGER datasets demonstrates the effectiveness of TOLF - up to 35 times faster than the state-of-the-art methods.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098191"}, {"primary_key": "3832598", "vector": [], "sparse_vector": [], "title": "Bridging Collaborative Filtering and Semi-Supervised Learning: A Neural Approach for POI Recommendation.", "authors": ["<PERSON>", "<PERSON>nx<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Jiawei Han"], "summary": "Recommender system is one of the most popular data mining topics that keep drawing extensive attention from both academia and industry. Among them, POI (point of interest) recommendation is extremely practical but challenging: it greatly benefits both users and businesses in real-world life, but it is hard due to data scarcity and various context. While a number of algorithms attempt to tackle the problem w.r.t. specific data and problem settings, they often fail when the scenarios change. In this work, we propose to devise a general and principled SSL (semi-supervised learning) framework, to alleviate data scarcity via smoothing among neighboring users and POIs, and treat various context by regularizing user preference based on context graphs. To enable such a framework, we develop PACE (Preference And Context Embedding), a deep neural architecture that jointly learns the embeddings of users and POIs to predict both user preference over POIs and various context associated with users and POIs. We show that PACE successfully bridges CF (collaborative filtering) and SSL by generalizing the de facto methods matrix factorization of CF and graph Laplacian regularization of SSL. Extensive experiments on two real location-based social network datasets demonstrate the effectiveness of PACE.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098094"}, {"primary_key": "3832599", "vector": [], "sparse_vector": [], "title": "A Data-driven Process Recommender Framework.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present an approach for improving the performance of complex knowledge-based processes by providing data-driven step-by-step recommendations. Our framework uses the associations between similar historic process performances and contextual information to determine the prototypical way of enacting the process. We introduce a novel similarity metric for grouping traces into clusters that incorporates temporal information about activity performance and handles concurrent activities. Our data-driven recommender system selects the appropriate prototype performance of the process based on user-provided context attributes. Our approach for determining the prototypes discovers the commonly performed activities and their temporal relationships. We tested our system on data from three real-world medical processes and achieved recommendation accuracy up to an F1 score of 0.77 (compared to an F1 score of 0.37 using ZeroR) with 63.2% of recommended enactments being within the first five neighbors of the actual historic enactments in a set of 87 cases. Our framework works as an interactive visual analytic tool for process mining. This work shows the feasibility of data-driven decision support system for complex knowledge-based processes.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098174"}, {"primary_key": "3832600", "vector": [], "sparse_vector": [], "title": "Visual Search at eBay.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we propose a novel end-to-end approach for scalable visual search infrastructure. We discuss the challenges we faced for a massive volatile inventory like at eBay and present our solution to overcome those. We harness the availability of large image collection of eBay listings and state-of-the-art deep learning techniques to perform visual search at scale. Supervised approach for optimized search limited to top predicted categories and also for compact binary signature are key to scale up without compromising accuracy and precision. Both use a common deep neural network requiring only a single forward inference. The system architecture is presented with in-depth discussions of its basic components and optimizations for a trade-off between search relevance and latency. This solution is currently deployed in a distributed cloud infrastructure and fuels visual search in eBay ShopBot and Close5. We show benchmark on ImageNet dataset on which our approach is faster and more accurate than several unsupervised baselines. We share our learnings with the hope that visual search becomes a first class citizen for all large scale search engines rather than an afterthought.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098162"}, {"primary_key": "3832601", "vector": [], "sparse_vector": [], "title": "Multi-task Function-on-function Regression with Co-grouping Structured Sparsity.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The growing importance of functional data has fueled the rapid development of functional data analysis, which treats the infinite-dimensional data as continuous functions rather than discrete, finite-dimensional vectors. On the other hand, heterogeneity is an intrinsic property of functional data due to the variety of sources to collect the data. In this paper, we propose a novel multi-task function-on-function regression approach to model both the functionality and heterogeneity of data. The basic idea is to simultaneously model the relatedness among tasks and correlations among basis functions by using the co-grouping structured sparsity to encourage similar tasks to behave similarly in shrinking the basis functions. The resulting optimization problem is challenging due to the non-smoothness and non-separability of the co-grouping structured sparsity. We present an efficient algorithm to solve the problem, and prove its separability, convexity, and global convergence. The proposed algorithm is applicable to a wide spectrum of structured sparsity regularized techniques, such as structured ℓ2,p norm and structured Schatten p-norm. The effectiveness of the proposed approach is verified on benchmark functional data sets collected from various domains.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098133"}, {"primary_key": "3832602", "vector": [], "sparse_vector": [], "title": "Local Algorithm for User Action Prediction Towards Display Ads.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "User behavior modeling is essential in computational advertisement, which builds users' profiles by tracking their online behaviors and then delivers the relevant ads according to each user's interests and needs. Accurate models will lead to higher targeting accuracy and thus improved advertising performance. Intuitively, similar users tend to have similar behaviors towards the displayed ads (e.g., impression, click, conversion). However, to the best of our knowledge, there is not much previous work that explicitly investigates such similarities of various types of user behaviors, and incorporates them into ad response targeting and prediction, largely due to the prohibitive scale of the problem.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098089"}, {"primary_key": "3832604", "vector": [], "sparse_vector": [], "title": "Learning from Labeled and Unlabeled Vertices in Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Claudia Plant", "<PERSON>"], "summary": "Networks such as social networks, citation networks, protein-protein interaction networks, etc., are prevalent in real world. However, only very few vertices have labels compared to large amounts of unlabeled vertices. For example, in social networks, not every user provides his/her profile information such as the personal interests which are relevant for targeted advertising. Can we leverage the limited user information and friendship network wisely to infer the labels of unlabeled users?", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098142"}, {"primary_key": "3832605", "vector": [], "sparse_vector": [], "title": "PPDsparse: A Parallel Primal-Dual Sparse Method for Extreme Classification.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Extreme Classification comprises multi-class or multi-label prediction where there is a large number of classes, and is increasingly relevant to many real-world applications such as text and image tagging. In this setting, standard classification methods, with complexity linear in the number of classes, become intractable, while enforcing structural constraints among classes (such as low-rank or tree-structure) to reduce complexity often sacrifices accuracy for efficiency. The recent PD-Sparse method addresses this via an algorithm that is sub-linear in the number of variables, by exploiting primal-dual sparsity inherent in a specific loss function, namely the max-margin loss. In this work, we extend PD-Sparse to be efficiently parallelized in large-scale distributed settings. By introducing separable loss functions, we can scale out the training, with network communication and space efficiency comparable to those in one-versus-all approaches while maintaining an overall complexity sub-linear in the number of classes. On several large-scale benchmarks our proposed method achieves accuracy competitive to the state-of-the-art while reducing the training time from days to tens of minutes compared with existing parallel or sparse methods on a cluster of 100 cores.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098083"}, {"primary_key": "3832606", "vector": [], "sparse_vector": [], "title": "Predicting Optimal Facility Location without Customer Locations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deriving meaningful insights from location data helps businesses make better decisions. One critical decision made by a business is choosing a location for its new facility. Optimal location queries ask for a location to build a new facility that optimizes an objective function. Most of the existing works on optimal location queries propose solutions to return best location when the set of existing facilities and the set of customers are given. However, most businesses do not know the locations of their customers. In this paper, we introduce a new problem setting for optimal location queries by removing the assumption that the customer locations are known. We propose an optimal location predictor which accepts partial information about customer locations and returns a location for the new facility. The predictor generates synthetic customer locations by using given partial information and it runs optimal location queries with generated location data. Experiments with real data show that the predictor can find the optimal location when sufficient information is provided.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098198"}, {"primary_key": "3832607", "vector": [], "sparse_vector": [], "title": "Local Higher-Order Graph Clustering.", "authors": ["<PERSON><PERSON>", "Austin <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Local graph clustering methods aim to find a cluster of nodes by exploring a small region of the graph. These methods are attractive because they enable targeted clustering around a given seed node and are faster than traditional global graph clustering methods because their runtime does not depend on the size of the input graph. However, current local graph partitioning methods are not designed to account for the higher-order structures crucial to the network, nor can they effectively handle directed networks. Here we introduce a new class of local graph clustering methods that address these issues by incorporating higher-order network information captured by small subgraphs, also called network motifs. We develop the Motif-based Approximate Personalized PageRank (MAPPR) algorithm that finds clusters containing a seed node with minimal", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098069"}, {"primary_key": "3832608", "vector": [], "sparse_vector": [], "title": "DeepProbe: Information Directed Sequence Understanding and Chatbot Design via Recurrent Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Information extraction and user intention identification are central topics in modern query understanding and recommendation systems. In this paper, we propose DeepProbe, a generic information-directed interaction framework which is built around an attention-based sequence to sequence (seq2seq) recurrent neural network. DeepProbe can rephrase, evaluate, and even actively ask questions, leveraging the generative ability and likelihood estimation made possible by seq2seq models. DeepProbe makes decisions based on a derived uncertainty (entropy) measure conditioned on user inputs, possibly with multiple rounds of interactions. Three applications, namely a rewritter, a relevance scorer and a chatbot for ad recommendation, were built around DeepProbe, with the first two serving as precursory building blocks for the third. We first use the seq2seq model in DeepProbe to rewrite a user query into one of standard query form, which is submitted to an ordinary recommendation system. Secondly, we evaluate DeepProbe's seq2seq model-based relevance scoring. Finally, we build a chatbot prototype capable of making active user interactions, which can ask questions that maximize information gain, allowing for a more efficient user intention idenfication process. We evaluate first two applications by 1) comparing with baselines by BLEU and AUC, and 2) human judge evaluation. Both demonstrate significant improvements compared with current state-of-the-art systems, proving their values as useful tools on their own, and at the same time laying a good foundation for the ongoing chatbot application.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098148"}, {"primary_key": "3832609", "vector": [], "sparse_vector": [], "title": "Small Batch or Large Batch?: Gaussian Walk with Rebound Can Teach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Efficiency of large-scale learning is a hot topic in both academic and industry. The stochastic gradient descent (SGD) algorithm, and its extension mini-batch SGD, allow the model to be updated without scanning the whole data set. However, the use of approximate gradient leads to the uncertainty issue, slowing down the decreasing of objective function. Furthermore, such uncertainty may result in a high frequency of meaningless update on the model, causing a communication issue in parallel learning environment. In this work, we develop a batch-adaptive stochastic gradient descent (BA-SGD) algorithm, which can dynamically choose a proper batch size as learning proceeds. Particularly on the basis of Taylor extension and central limit theorem, it models the decrease of objective value as a Gaussian random walk game with rebound. In this game, a heuristic strategy of determining batch size is adopted to maximize the utility of each incremental sampling. By evaluation on multiple real data sets, we demonstrate that by smartly choosing the batch size, the BA-SGD not only conserves the fast convergence of SGD algorithm but also avoids too frequent model updates.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098147"}, {"primary_key": "3832610", "vector": [], "sparse_vector": [], "title": "Learning from Multiple Teacher Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Dacheng Tao"], "summary": "Training thin deep networks following the student-teacher learning paradigm has received intensive attention because of its excellent performance. However, to the best of our knowledge, most existing work mainly considers one single teacher network. In practice, a student may access multiple teachers, and multiple teacher networks together provide comprehensive guidance that is beneficial for training the student network. In this paper, we present a method to train a thin deep network by incorporating multiple teacher networks not only in output layer by averaging the softened outputs (dark knowledge) from different networks, but also in the intermediate layers by imposing a constraint about the dissimilarity among examples. We suggest that the relative dissimilarity between intermediate representations of different examples serves as a more flexible and appropriate guidance from teacher networks. Then triplets are utilized to encourage the consistence of these relative dissimilarity relationships between the student network and teacher networks. Moreover, we leverage a voting strategy to unify multiple relative dissimilarity information provided by multiple teacher networks, which realizes their incorporation in the intermediate layers. Extensive experimental results demonstrated that our method is capable of generating a well-performed student network, with the classification accuracy comparable or even superior to all teacher networks, yet having much fewer parameters and being much faster in running.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098135"}, {"primary_key": "3832611", "vector": [], "sparse_vector": [], "title": "Three Principles of Data Science: Predictability, Stability and Computability.", "authors": ["<PERSON>"], "summary": "In this talk, I'd like to discuss the intertwining importance and connections of three principles of data science in the title in data-driven decisions.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3105808"}, {"primary_key": "3832612", "vector": [], "sparse_vector": [], "title": "A Temporally Heterogeneous Survival Framework with Application to Social Behavior Dynamics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Social behavior dynamics is one of the central building blocks in understanding and modeling complex social dynamic phenomena, such as information spreading, opinion formation, and social mobilization. While a wide range of models for social behavior dynamics have been proposed in recent years, the essential ingredients and the minimum model for social behavior dynamics is still largely unanswered. Here, we find that human interaction behavior dynamics exhibit rich complexities over the response time dimension and natural time dimension by exploring a large scale social communication dataset. To tackle this challenge, we develop a temporal Heterogeneous Survival framework where the regularities in response time dimension and natural time dimension can be organically integrated. We apply our model in two online social communication datasets. Our model can successfully regenerate the interaction patterns in the social communication datasets, and the results demonstrate that the proposed method can significantly outperform other state-of-the-art baselines. Meanwhile, the learnt parameters and discovered statistical regularities can lead to multiple potential applications.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098189"}, {"primary_key": "3832613", "vector": [], "sparse_vector": [], "title": "Long Short Memory Process: Modeling Growth Dynamics of Microscopic Social Connectivity.", "authors": ["Chengxi Zang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "How do people make friends dynamically in social networks? What are the temporal patterns for an individual increasing its social connectivity? What are the basic mechanisms governing the formation of these temporal patterns? No matter cyber or physical social systems, their structure and dynamics are mainly driven by the connectivity dynamics of each individual. However, due to the lack of empirical data, little is known about the empirical dynamic patterns of social connectivity at microscopic level, let alone the regularities or models governing these microscopic dynamics.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098055"}, {"primary_key": "3832614", "vector": [], "sparse_vector": [], "title": "Inductive Semi-supervised Multi-Label Learning with Co-Training.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In multi-label learning, each training example is associated with multiple class labels and the task is to learn a mapping from the feature space to the power set of label space. It is generally demanding and time-consuming to obtain labels for training examples, especially for multi-label learning task where a number of class labels need to be annotated for the instance. To circumvent this difficulty, semi-supervised multi-label learning aims to exploit the readily-available unlabeled data to help build multi-label predictive model. Nonetheless, most semi-supervised solutions to multi-label learning work under transductive setting, which only focus on making predictions on existing unlabeled data and cannot generalize to unseen instances. In this paper, a novel approach named COINS is proposed to learning from labeled and unlabeled data by adapting the well-known co-training strategy which naturally works under inductive setting. In each co-training round, a dichotomy over the feature space is learned by maximizing the diversity between the two classifiers induced on either dichotomized feature subset. After that, pairwise ranking predictions on unlabeled data are communicated between either classifier for model refinement. Extensive experiments on a number of benchmark data sets show that COINS performs favorably against state-of-the-art multi-label learning approaches.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098141"}, {"primary_key": "3832615", "vector": [], "sparse_vector": [], "title": "EmbedJoin: Efficient Edit Similarity Joins via Embeddings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of edit similarity joins, where given a set of strings and a threshold value K, we want to output all pairs of strings whose edit distances are at most K. Edit similarity join is a fundamental problem in data cleaning/integration, bioinformatics, collaborative filtering and natural language processing, and has been identified as a primitive operator for database systems. This problem has been studied extensively in the literature. However, we have observed that all the existing algorithms fall short on long strings and large distance thresholds.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098003"}, {"primary_key": "3832616", "vector": [], "sparse_vector": [], "title": "Stock Price Prediction via Discovering Multi-Frequency Trading Patterns.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Stock prices are formed based on short and/or long-term commercial and trading activities that reflect different frequencies of trading patterns. However, these patterns are often elusive as they are affected by many uncertain political-economic factors in the real world, such as corporate performances, government policies, and even breaking news circulated across markets. Moreover, time series of stock prices are non-stationary and non-linear, making the prediction of future price trends much challenging. To address them, we propose a novel State Frequency Memory (SFM) recurrent network to capture the multi-frequency trading patterns from past market data to make long and short term predictions over time. Inspired by Discrete Fourier Transform (DFT), the SFM decomposes the hidden states of memory cells into multiple frequency components, each of which models a particular frequency of latent trading pattern underlying the fluctuation of stock price. Then the future stock prices are predicted as a nonlinear mapping of the combination of these components in an Inverse Fourier Transform (IFT) fashion. Modeling multi-frequency trading patterns can enable more accurate predictions for various time ranges: while a short-term prediction usually depends on high frequency trading patterns, a long-term prediction should focus more on the low frequency trading patterns targeting at long-term return. Unfortunately, no existing model explicitly distinguishes between various frequencies of trading patterns to make dynamic predictions in literature. The experiments on the real market data also demonstrate more competitive performance by the SFM as compared with the state-of-the-art methods.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098117"}, {"primary_key": "3832617", "vector": [], "sparse_vector": [], "title": "Weisfeiler-<PERSON><PERSON> Neural Machine for Link Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose a next-generation link prediction method, Weisfeiler-Lehman Neural Machine (WLNM), which learns topological features in the form of graph patterns that promote the formation of links. WLNM has unmatched advantages including higher performance than state-of-the-art methods and universal applicability over various kinds of networks. WLNM extracts an enclosing subgraph of each target link and encodes the subgraph as an adjacency matrix. The key novelty of the encoding comes from a fast hashing-based Weisfeiler-Lehman (WL) algorithm that labels the vertices according to their structural roles in the subgraph while preserving the subgraph's intrinsic directionality. After that, a neural network is trained on these adjacency matrices to learn a predictive model. Compared with traditional link prediction methods, WLNM does not assume a particular link formation mechanism (such as common neighbors), but learns this mechanism from the graph itself. We conduct comprehensive experiments to show that WLNM not only outperforms a great number of state-of-the-art link prediction methods, but also consistently performs well across networks with different characteristics.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3097996"}, {"primary_key": "3832618", "vector": [], "sparse_vector": [], "title": "LEAP: Learning to Prescribe Effective and Safe Treatment Combinations for Multimorbidity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Sun"], "summary": "Managing patients with complex multimorbidity has long been recognized as a difficult problem due to complex disease and medication dependencies and the potential risk of adverse drug interactions. Existing work either uses complicated rule-based protocols which are hard to implement and maintain, or simple statistical models that treat each disease independently, which may lead to sub-optimal or even harmful drug combinations. In this work, we propose the LEAP (LEArn to Prescribe) algorithm to decompose the treatment recommendation into a sequential decision-making process while automatically determining the appropriate number of medications. A recurrent decoder is used to model label dependencies and content-based attention is used to capture label instance mapping. We further leverage reinforcement learning to fine tune the model parameters to ensure accuracy and completeness. We incorporate external clinical knowledge into the design of the reinforcement reward to effectively prevent generating unfavorable drug combinations. Both quantitative experiments and qualitative case studies are conducted on two real world electronic health record datasets to verify the effectiveness of our solution. On both datasets, LEAP significantly outperforms baselines by up to 10-30% in terms of mean Jaccard coefficient and removes 99.8% adverse drug interactions in the recommended treatment sets.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098109"}, {"primary_key": "3832619", "vector": [], "sparse_vector": [], "title": "A Taxi Order Dispatch Model based On Combinatorial Optimization.", "authors": ["<PERSON><PERSON>", "Tao Hu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Pengcheng Feng", "Pinghua Gong", "Jieping Ye"], "summary": "Taxi-booking apps have been very popular all over the world as they provide convenience such as fast response time to the users. The key component of a taxi-booking app is the dispatch system which aims to provide optimal matches between drivers and riders. Traditional dispatch systems sequentially dispatch taxis to riders and aim to maximize the driver acceptance rate for each individual order. However, the traditional systems may lead to a low global success rate, which degrades the rider experience when using the app. In this paper, we propose a novel system that attempts to optimally dispatch taxis to serve multiple bookings. The proposed system aims to maximize the global success rate, thus it optimizes the overall travel efficiency, leading to enhanced user experience. To further enhance users' experience, we also propose a method to predict destinations of a user once the taxi-booking APP is started. The proposed method employs the Bayesian framework to model the distribution of a user's destination based on his/her travel histories.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098138"}, {"primary_key": "3832620", "vector": [], "sparse_vector": [], "title": "A Quasi-experimental Estimate of the Impact of P2P Transportation Platforms on Urban Consumer Patterns.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the pervasiveness of mobile technology and location-based computing, new forms of smart urban transportation, such as Uber & Lyft, have become increasingly popular. These new forms of urban infrastructure can influence individuals' movement frictions and patterns, in turn influencing local consumption patterns and the economic performance of local businesses. To gain insights about future impact of urban transportation changes, in this paper, we utilize a novel dataset and econometric analysis methods to present a quasi-experimental examination of how the emerging growth of peer-to-peer car sharing services may have affected local consumer mobility and consumption patterns.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098058"}, {"primary_key": "3832621", "vector": [], "sparse_vector": [], "title": "TrioVecEvent: Embedding-Based Online Local Event Detection in Geo-Tagged Tweet Streams.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jiawei Han"], "summary": "Detecting local events (e.g., protest, disaster) at their onsets is an important task for a wide spectrum of applications, ranging from disaster control to crime monitoring and place recommendation. Recent years have witnessed growing interest in leveraging geo-tagged tweet streams for online local event detection. Nevertheless, the accuracies of existing methods still remain unsatisfactory for building reliable local event detection systems. We propose TrioVecEvent, a method that leverages multimodal embeddings to achieve accurate online local event detection. The effectiveness of TrioVecEvent is underpinned by its two-step detection scheme. First, it ensures a high coverage of the underlying local events by dividing the tweets in the query window into coherent geo-topic clusters. To generate quality geo-topic clusters, we capture short-text semantics by learning multimodal embeddings of the location, time, and text, and then perform online clustering with a novel Bayesian mixture model. Second, TrioVecEvent considers the geo-topic clusters as candidate events and extracts a set of features for classifying the candidates. Leveraging the multimodal embeddings as background knowledge, we introduce discriminative features that can well characterize local events, which enables pinpointing true local events from the candidate pool with a small amount of training data. We have used crowdsourcing to evaluate TrioVecEvent, and found that it improves the performance of the state-of-the-art method by a large margin.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098027"}, {"primary_key": "3832622", "vector": [], "sparse_vector": [], "title": "Randomization or Condensation?: Linear-Cost Matrix Sketching Via Cascaded Compression Sampling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Jieping Ye"], "summary": "Matrix sketching is aimed at finding compact representations of a matrix while simultaneously preserving most of its properties, which is a fundamental building block in modern scientific computing. Randomized algorithms represent state-of-the-art and have attracted huge interest from the fields of machine learning, data mining, and theoretic computer science. However, it still requires the use of the entire input matrix in producing desired factorizations, which can be a major computational and memory bottleneck in truly large problems. In this paper, we uncover an interesting theoretic connection between matrix low-rank decomposition and lossy signal compression, based on which a cascaded compression sampling framework is devised to approximate an m-by-n matrix in only O(m+n) time and space. Indeed, the proposed method accesses only a small number of matrix rows and columns, which significantly improves the memory footprint. Meanwhile, by sequentially teaming two rounds of approximation procedures and upgrading the sampling strategy from a uniform probability to more sophisticated, encoding-orientated sampling, significant algorithmic boosting is achieved to uncover more granular structures in the data. Empirical results on a wide spectrum of real-world, large-scale matrices show that by taking only linear time and space, the accuracy of our method rivals those state-of-the-art randomized algorithms consuming a quadratic, O(mn), amount of resources.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098050"}, {"primary_key": "3832623", "vector": [], "sparse_vector": [], "title": "Graph Edge Partitioning via Neighborhood Heuristic.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the edge partitioning problem that partitions the edges of an input graph into multiple balanced components, while minimizing the total number of vertices replicated (one vertex might appear in more than one partition). This problem is critical in minimizing communication costs and running time for several large-scale distributed graph computation platforms (e.g., PowerGraph, Spark GraphX). We first prove that this problem is NP-hard, and then present a new partitioning heuristic with polynomial running time. We provide a worst-case upper bound of replication factor for our heuristic on general graphs. To our knowledge, we are the first to provide such bound for edge partitioning algorithms on general graphs. Applying this bound to random power-law graphs greatly improves the previous bounds of expected replication factor. Extensive experiments demonstrated that our partitioning algorithm consistently produces much smaller replication factors on various benchmark data sets than the state-of-the-art. When deployed in the production graph engine, PowerGraph, in average it reduces replication factor, communication, and running time by 54%, 66%, and 21%, respectively.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098033"}, {"primary_key": "3832624", "vector": [], "sparse_vector": [], "title": "Visualizing Attributed Graphs via Terrain Metaphor.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The value proposition of a dataset often resides in the implicit interconnections or explicit relationships (patterns) among individual entities, and is often modeled as a graph. Effective visualization of such graphs can lead to key insights uncovering such value. In this article we propose a visualization method to explore attributed graphs with numerical attributes associated with nodes (or edges). Such numerical attributes can represent raw content information, similarities, or derived information reflecting important network measures such as triangle density and centrality. The proposed visualization strategy seeks to simultaneously uncover the relationship between attribute values and graph topology, and relies on transforming the network to generate a terrain map. A key objective here is to ensure that the terrain map reveals the overall distribution of components-of-interest (e.g. dense subgraphs, k-cores) and the relationships among them while being sensitive to the attribute values over the graph. We also design extensions that can capture the relationship across multiple numerical attributes. We demonstrate the efficacy of our method on several real-world data science tasks while scaling to large graphs with millions of nodes.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098130"}, {"primary_key": "3832625", "vector": [], "sparse_vector": [], "title": "Achieving Non-Discrimination in Data Release.", "authors": ["<PERSON>", "Yongkai Wu", "Xinta<PERSON> Wu"], "summary": "Discrimination discovery and prevention/removal are increasingly important tasks in data mining. Discrimination discovery aims to unveil discriminatory practices on the protected attribute (e.g., gender) by analyzing the dataset of historical decision records, and discrimination prevention aims to remove discrimination by modifying the biased data before conducting predictive analysis. In this paper, we show that the key to discrimination discovery and prevention is to find the meaningful partitions that can be used to provide quantitative evidences for the judgment of discrimination. With the support of the causal graph, we present a graphical condition for identifying a meaningful partition. Based on that, we develop a simple criterion for the claim of non-discrimination, and propose discrimination removal algorithms which accurately remove discrimination while retaining good data utility. Experiments using real datasets show the effectiveness of our approaches.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098167"}, {"primary_key": "3832626", "vector": [], "sparse_vector": [], "title": "Meta-Graph Based Recommendation Fusion over Heterogeneous Information Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Heterogeneous Information Network (HIN) is a natural and general representation of data in modern large commercial recommender systems which involve heterogeneous types of data. HIN based recommenders face two problems: how to represent the high-level semantics of recommendations and how to fuse the heterogeneous information to make recommendations. In this paper, we solve the two problems by first introducing the concept of meta-graph to HIN-based recommendation, and then solving the information fusion problem with a \"matrix factorization (MF) + factorization machine (FM)\" approach. For the similarities generated by each meta-graph, we perform standard MF to generate latent features for both users and items. With different meta-graph based features, we propose to use FM with Group lasso (FMG) to automatically learn from the observed ratings to effectively select useful meta-graph based features. Experimental results on two real-world datasets, Amazon and Yelp, show the effectiveness of our approach compared to state-of-the-art FM and other HIN-based recommendation algorithms.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098063"}, {"primary_key": "3832627", "vector": [], "sparse_vector": [], "title": "Tracking the Dynamics in Crowdfunding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Crowdfunding is an emerging Internet fundraising mechanism by raising monetary contributions from the crowd for projects or ventures. In these platforms, the dynamics, i.e., daily funding amount on campaigns and perks (backing options with rewards), are the most concerned issue for creators, backers and platforms. However, tracking the dynamics in crowdfunding is very challenging and still under-explored. To that end, in this paper, we present a focused study on this important problem. A special goal is to forecast the funding amount for a given campaign and its perks in the future days. Specifically, we formalize the dynamics in crowdfunding as a hierarchical time series, i.e., campaign level and perk level. Specific to each level, we develop a special regression by modeling the decision making process of the crowd (visitors and backing probability) and exploring various factors that impact the decision; on this basis, an enhanced switching regression is proposed at each level to address the heterogeneity of funding sequences. Further, we employ a revision matrix to combine the two-level base forecasts for the final forecasting. We conduct extensive experiments on a real-world crowdfunding data collected from Indiegogo.com. The experimental results clearly demonstrate the effectiveness of our approaches on tracking the dynamics in crowdfunding.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098030"}, {"primary_key": "3832628", "vector": [], "sparse_vector": [], "title": "Contextual Spatial Outlier Detection with Metric Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hydraulic fracturing (or \"fracking\") is a revolutionary well stimulation technique for shale gas extraction, but has spawned controversy in environmental contamination. If methane from gas wells leaks extensively, this greenhouse gas can impact drinking water wells and enhance global warming. Our work is motivated by this heated debate on environmental issue and focuses on general data analytical techniques to detect anomalous spatial data samples (e.g., water samples related to potential leakages). Specifically, we propose a spatial outlier detection method based on contextual neighbors. Different from existing work, our approach utilizes both spatial attributes and non-spatial contextual attributes to define neighbors. We further use robust metric learning to combine different contextual attributes in order to find meaningful neighbors. Our technique can be applied to any spatial dataset. Extensive experimental results on five real-world datasets demonstrate the effectiveness of our approach. We also show some interesting case studies, including one case linking to leakage of a gas well.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098143"}, {"primary_key": "3832629", "vector": [], "sparse_vector": [], "title": "Resolving the Bias in Electronic Medical Records.", "authors": ["<PERSON><PERSON>", "Jinyang Gao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Electronic Medical Records (EMR) are the most fundamental resources used in healthcare data analytics. Since people visit hospital more frequently when they feel sick and doctors prescribe lab examinations when they feel necessary, we argue that there could be a strong bias in EMR observations compared with the hidden conditions of patients. Directly using such EMR for analytical tasks without considering the bias may lead to misinterpretation. To this end, we propose a general method to resolve the bias by transforming EMR to regular patient hidden condition series using a Hidden Markov Model (HMM) variant. Compared with the biased EMR series with irregular time stamps, the unbiased regular time series is much easier to be processed by most analytical models and yields better results. Extensive experimental results demonstrate that our bias resolving method imputes missing data more accurately than baselines and improves the performance of the state-of-the-art methods on typical medical data analytics.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098149"}, {"primary_key": "3832630", "vector": [], "sparse_vector": [], "title": "Coresets for Kernel Regression.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Kernel regression is an essential and ubiquitous tool for non-parametric data analysis, particularly popular among time series and spatial data. However, the central operation which is performed many times, evaluating a kernel on the data set, takes linear time. This is impractical for modern large data sets.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098000"}, {"primary_key": "3832631", "vector": [], "sparse_vector": [], "title": "KunPeng: Parameter Server based Distributed Learning Systems and Its Applications in Alibaba and Ant Financial.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qing <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (Alan) Qi"], "summary": "In recent years, due to the emergence of Big Data (terabytes or petabytes) and Big Model (tens of billions of parameters), there has been an ever-increasing need of parallelizing machine learning (ML) algorithms in both academia and industry. Although there are some existing distributed computing systems, such as Hadoop and Spark, for parallelizing ML algorithms, they only provide synchronous and coarse-grained operators (e.g., Map, Reduce, and Join, etc.), which may hinder developers from implementing more efficient algorithms. This motivated us to design a universal distributed platform termed KunPeng, that combines both distributed systems and parallel optimization algorithms to deal with the complexities that arise from large-scale ML. Specifically, KunPeng not only encapsulates the characteristics of data/model parallelism, load balancing, model sync-up, sparse representation, industrial fault-tolerance, etc., but also provides easy-to-use interface to empower users to focus on the core ML logics. Empirical results on terabytes of real datasets with billions of samples and features demonstrate that, such a design brings compelling performance improvements on ML programs ranging from Follow-the-Regularized-Leader Proximal algorithm to Sparse Logistic Regression and Multiple Additive Regression Trees. Furthermore, KunPeng's encouraging performance is also shown for several real-world applications including the Alibaba's Double 11 Online Shopping Festival and Ant Financial's transaction risk estimation.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098029"}, {"primary_key": "3832632", "vector": [], "sparse_vector": [], "title": "Anomaly Detection with Robust Deep Autoencoders.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Deep autoencoders, and other deep neural networks, have demonstrated their effectiveness in discovering non-linear features across many problem domains. However, in many real-world problems, large outliers and pervasive noise are commonplace, and one may not have access to clean training data as required by standard deep denoising autoencoders. Herein, we demonstrate novel extensions to deep autoencoders which not only maintain a deep autoencoders' ability to discover high quality, non-linear features but can also eliminate outliers and noise without access to any clean training data. Our model is inspired by Robust Principal Component Analysis, and we split the input data X into two parts, $X = L_{D} + S$, where $L_{D}$ can be effectively reconstructed by a deep autoencoder and $S$ contains the outliers and noise in the original data X. Since such splitting increases the robustness of standard deep autoencoders, we name our model a \"Robust Deep Autoencoder (RDA)\". Further, we present generalizations of our results to grouped sparsity norms which allow one to distinguish random anomalies from other types of structured corruptions, such as a collection of features being corrupted across many instances or a collection of instances having more corruptions than their fellows. Such \"Group Robust Deep Autoencoders (GRDA)\" give rise to novel anomaly detection approaches whose superior performance we demonstrate on a selection of benchmark problems.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098052"}, {"primary_key": "3832633", "vector": [], "sparse_vector": [], "title": "STAR: A System for Ticket Analysis and Resolution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Genady <PERSON>"], "summary": "In large scale and complex IT service environments, a problematic incident is logged as a ticket and contains the ticket summary (system status and problem description). The system administrators log the step-wise resolution description when such tickets are resolved. The repeating service events are most likely resolved by inferring similar historical tickets. With the availability of reasonably large ticket datasets, we can have an automated system to recommend the best matching resolution for a given ticket summary. In this paper, we first identify the challenges in real-world ticket analysis and develop an integrated framework to efficiently handle those challenges. The framework first quantifies the quality of ticket resolutions using a regression model built on carefully designed features. The tickets, along with their quality scores obtained from the resolution quality quantification, are then used to train a deep neural network ranking model that outputs the matching scores of ticket summary and resolution pairs. This ranking model allows us to leverage the resolution quality in historical tickets when recommending resolutions for an incoming incident ticket. In addition, the feature vectors derived from the deep neural ranking model can be effectively used in other ticket analysis tasks, such as ticket classification and clustering. The proposed framework is extensively evaluated with a large real-world dataset.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098190"}, {"primary_key": "3832634", "vector": [], "sparse_vector": [], "title": "A Local Algorithm for Structure-Preserving Graph Cut.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Hanghang Tong", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nowadays, large-scale graph data is being generated in a variety of real-world applications, from social networks to co-authorship networks, from protein-protein interaction networks to road traffic networks. Many existing works on graph mining focus on the vertices and edges, with the first-order Markov chain as the underlying model. They fail to explore the high-order network structures, which are of key importance in many high impact domains. For example, in bank customer personally identifiable information (PII) networks, the star structures often correspond to a set of synthetic identities; in financial transaction networks, the loop structures may indicate the existence of money laundering. In this paper, we focus on mining user-specified high-order network structures and aim to find a structure-rich subgraph which does not break many such structures by separating the subgraph from the rest.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098015"}, {"primary_key": "3832635", "vector": [], "sparse_vector": [], "title": "Optimized Cost per Click in Taobao Display Advertising.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Taobao, as the largest online retail platform in the world, provides billions of online display advertising impressions for millions of advertisers every day. For commercial purposes, the advertisers bid for specific spots and target crowds to compete for business traffic. The platform chooses the most suitable ads to display in tens of milliseconds. Common pricing methods include cost per mille (CPM) and cost per click (CPC). Traditional advertising systems target certain traits of users and ad placements with fixed bids, essentially regarded as coarse-grained matching of bid and traffic quality. However, the fixed bids set by the advertisers competing for different quality requests cannot fully optimize the advertisers' key requirements. Moreover, the platform has to be responsible for the business revenue and user experience. Thus, we proposed a bid optimizing strategy called optimized cost per click (OCPC) which automatically adjusts the bid to achieve finer matching of bid and traffic quality of page view (PV) request granularity. Our approach optimizes advertisers' demands, platform business revenue and user experience and as a whole improves traffic allocation efficiency. We have validated our approach in Taobao display advertising system in production. The online A/B test shows our algorithm yields substantially better results than previous fixed bid manner.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098134"}, {"primary_key": "3832636", "vector": [], "sparse_vector": [], "title": "Deep Embedding Forest: Forest-based Serving with Deep Embedding Features.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Deep Neural Networks (DNN) have demonstrated superior ability to extract high level embedding vectors from low level features. Despite the success, the serving time is still the bottleneck due to expensive run-time computation of multiple layers of dense matrices. GPGPU, FPGA, or ASIC-based serving systems require additional hardware that are not in the mainstream design of most commercial applications. In contrast, tree or forest-based models are widely adopted because of low serving cost, but heavily depend on carefully engineered features. This work proposes a Deep Embedding Forest model that benefits from the best of both worlds. The model consists of a number of embedding layers and a forest/tree layer. The former maps high dimensional (hundreds of thousands to millions) and heterogeneous low-level features to the lower dimensional (thousands) vectors, and the latter ensures fast serving.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983.3098059"}, {"primary_key": "3884376", "vector": [], "sparse_vector": [], "title": "Proceedings of the 23rd ACM SIGKDD International Conference on Knowledge Discovery and Data Mining, Halifax, NS, Canada, August 13 - 17, 2017", "authors": [], "summary": "It is our great pleasure to welcome you to the 2017 ACM Conference on Knowledge Discovery and Data Mining -- KDD 2017. We hope that the content and the professional networking opportunities at KDD 2017 will help you to succeed professionally by enabling you to: identify new technology trends; learn from contributed papers, presentations, and posters; discover new tools, processes and practices; identify new job opportunities; and hire new team members. The terms \"Data Science\", \"Data Mining\" and \"Big Data\" have, in the last few years, grown out of research labs and gained presence in the media and in everyday conversations. We also hear these terms on social media and from decision makers at various level of governments and corporations. The impact of these technologies is felt in almost every walk of life. Importantly, the current rapid progress in data science is facilitated by the timely sharing of newly discovered and developed representations and algorithms between those working in research and those interested in industrial deployment. It is the hallmark of KDD conferences in the past that they have been the bridge between theory and practise, the great facilitator and catalyst for this exchange. Researchers and practitioners meet in person and interact in a meaningful way over several days. The conference program, with its three parallel tracks - the Research Track, the Applied Data Science Track and the Applied Invited Speakers Track - brings the two groups together. Participants are welcome to freely attend any track, and the events common for all tracks. The conference this year continues with its tradition of a strong tutorial and workshop program on leading edge issues of data mining during the first two days of the program. The last three days are devoted to contributed technical papers, describing both novel, important research contributions, and deployed, innovative solutions. Three keynote talks, by <PERSON>, <PERSON>, and <PERSON><PERSON> touch on some of the hard, emerging issues before the field of data mining. With a growing industry around AI assistants, our KDD Panel brings together industry experts in this field to spawn discussions and an exchanges of ideas. We have an outstanding lineup of industry speakers sharing their experiences and expertise in deploying industrial data mining solutions. We continue a strong hands-on tutorial program, in which participants will learn how to use practical data science tools. In order to broaden the impact of KDD and to increase the participation of attendees who would greatly benefit from the conference but would have otherwise found it financially challenging to attend, we reserved a substantial budget for travel grants. KDD 2017 awarded a record USD 145k for student travel and also set aside USD 25k to enable smaller startups to attend. With the new \"Meet the Experts\" sessions, KDD 2017 also gives researchers and practitioners a unique opportunity to form professional networks and to share their perspectives with others interested in the various aspects of data science. We hope that the KDD 2017 conference will serve as a meeting ground for researchers, practitioners, funding agencies and investors to help create new algorithms and commercial products.", "published": "2017-01-01", "category": "kdd", "pdf_url": "", "sub_summary": "", "source": "kdd", "doi": "10.1145/3097983"}]