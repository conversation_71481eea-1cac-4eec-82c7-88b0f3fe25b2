[{"primary_key": "2652501", "vector": [], "sparse_vector": [], "title": "On the computational content of Zorn&apos;s lemma.", "authors": ["<PERSON>"], "summary": "We give a computational interpretation to an abstract instance of <PERSON><PERSON>'s lemma formulated as a wellfoundedness principle in the language of arithmetic in all finite types. This is achieved through <PERSON>\\\"<PERSON><PERSON>'s functional interpretation, and requires the introduction of a novel form of recursion over non-wellfounded partial orders whose existence in the model of total continuous functionals is proven using domain theoretic techniques. We show that a realizer for the functional interpretation of open induction over the lexicographic ordering on sequences follows as a simple application of our main results.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394745"}, {"primary_key": "2652502", "vector": [], "sparse_vector": [], "title": "Efficient Analysis of VASS Termination Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The termination complexity of a given VASS is a function L assigning to every n the length of the longest non-terminating computation initiated in a configuration with all counters bounded by n. We show that for every VASS with demonic nondeterminism and every fixed k, the problem whether L ϵ Gk, where Gk is the k-th level in the Grzegorczyk hierarchy, is decidable in polynomial time. Furthermore, we show that if L ϵ G, then L grows at least as fast as the generator Fk+1 of Gk+1. Hence, for every terminating VASS, the growth of L can be reasonably characterized by the least k such that L ϵ Gk.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394751"}, {"primary_key": "2652503", "vector": [], "sparse_vector": [], "title": "The Complexity of Dynamic Data Race Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Writing concurrent programs is notoriously hard due to scheduling non-determinism. The most common concurrency bugs are data races, which are accesses to a shared resource that can be executed concurrently. Dynamic data-race prediction is the most standard technique for detecting data races: Given an observed, data-race-free trace t, the task is to determine whether t can be reordered to a trace t∗that exposes a data-race. Although the problem has received significant practical attention for over three decades, its complexity has remained elusive. In this work, we address this lacuna, identifying sources of intractability and conditions under which the problem is efficiently solvable. Given a trace t of size n over k threads, our main results are as follows. First, we establish a general O(k · n2·(k-1) upper-bound, as well as an O(nk) upper-bound when certain parameters of t are constant. In addition, we show that the problem is NP-hard and even W[1]-hard parameterized by k, and thus unlikely to be fixed-parameter tractable. Second, we study the problem over acyclic communication topologies, such as server-clients hierarchies. We establish an O(k2 · d · n2 · log n) upper-bound, where d is the number of shared variables accessed in t. In addition, we show that even for traces with k = 2 threads, the problem has no O(n2-) algorithm under the Orthogonal Vectors conjecture. Since any trace with 2 threads defines an acyclic topology, our upper-bound for this case is optimal up to polynomial improvements for up to moderate values of k and d. Finally, motivated by existing heuristics, we study a distance-bounded version of the problem, where the task is to expose a data race by a witness trace that is similar to t. We develop an algorithm that works in O(n) time when certain parameters of t are constant.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394783"}, {"primary_key": "2652504", "vector": [], "sparse_vector": [], "title": "Combining probabilistic and non-deterministic choice via weak distributive laws.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Combining probabilistic choice and non-determinism is a long standing problem in denotational semantics. From a category theory perspective, the problem stems from the absence of a distributive law of the powerset monad over the distribution monad. In this paper we prove the existence of a weak distributive law of the powerset monad over the finite distribution monad. As a consequence, we retrieve the well-known convex powerset monad as a weak lifting of the powerset monad to the category of convex algebras. We provide applications to the study of trace semantics and behavioral equivalences of systems with an interplay between probability and non-determinism.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394795"}, {"primary_key": "2652505", "vector": [], "sparse_vector": [], "title": "Logic Beyond Formulas: A Proof System on Graphs.", "authors": ["<PERSON>", "<PERSON>", "Lutz Straßburger"], "summary": "In this paper we present a proof system that operates on graphs instead of formulas. We begin our quest with the well-known correspondence between formulas and cographs, which are undirected graphs that do not have P4 (the four-vertex path) as vertex-induced subgraph; and then we drop that condition and look at arbitrary (undirected) graphs. The consequence is that we lose the tree structure of the formulas corresponding to the cographs. Therefore we cannot use standard proof theoretical methods that depend on that tree structure. In order to overcome this difficulty, we use a modular decomposition of graphs and some techniques from deep inference where inference rules do not rely on the main connective of a formula. For our proof system we show the admissibility of cut and a generalization of the splitting property. Finally, we show that our system is a conservative extension of multiplicative linear logic (MLL) with mix, meaning that if a graph is a cograph and provable in our system, then it is also provable in MLL+mix.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394763"}, {"primary_key": "2652506", "vector": [], "sparse_vector": [], "title": "A Higher Structure Identity Principle.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The ordinary Structure Identity Principle states that any property of set-level structures (e.g., posets, groups, rings, fields) definable in Univalent Foundations is invariant under isomorphism: more specifically, identifications of structures coincide with isomorphisms. We prove a version of this principle for a wide range of higher-categorical structures, adapting FOLDS-signatures to specify a general class of structures, and using two-level type theory to treat all categorical dimensions uniformly. As in the previously known case of 1-categories (which is an instance of our theory), the structures themselves must satisfy a local univalence principle, stating that identifications coincide with \"isomorphisms\" between elements of the structure. Our main technical achievement is a definition of such isomorphisms, which we call \"indiscernibilities,\" using only the dependency structure rather than any notion of composition.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394755"}, {"primary_key": "2652507", "vector": [], "sparse_vector": [], "title": "The Integers as a Higher Inductive Type.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of defining the integers in Homotopy Type Theory (HoTT). We can define the type of integers as signed natural numbers (i.e., using a coproduct), but its induction principle is very inconvenient to work with, since it leads to an explosion of cases. An alternative is to use set-quotients, but here we need to use set-truncation to avoid non-trivial higher equalities. This results in a recursion principle that only allows us to define function into sets (types satisfying UIP). In this paper we consider higher inductive types using either a small universe or bi-invertible maps. These types represent integers without explicit set-truncation that are equivalent to the usual coproduct representation. This is an interesting example since it shows how some coherence problems can be handled in HoTT. We discuss some open questions triggered by this work. The proofs have been formally verified using cubical Agda.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394760"}, {"primary_key": "2652508", "vector": [], "sparse_vector": [], "title": "Space-efficient Query Evaluation over Probabilistic Event Streams.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Real-time decision making in IoT applications relies upon space-efficient evaluation of queries over streaming data. To model the uncertainty in the classification of data being processed, we consider the model of probabilistic strings --- sequences of discrete probability distributions over a finite set of events, and initiate the study of space complexity of streaming computation for different classes of queries over such probabilistic strings.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394747"}, {"primary_key": "2652509", "vector": [], "sparse_vector": [], "title": "Reconciling noninterference and gradual typing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "One of the standard correctness criteria for gradual typing is the dynamic gradual guarantee, which ensures that loosening type annotations in a program does not affect its behavior in arbitrary ways. Though natural, prior work has pointed out that the guarantee does not hold of any gradual type system for information-flow control. Toro et al.'s GSLRef language, for example, had to abandon it to validate noninterference.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394778"}, {"primary_key": "2652510", "vector": [], "sparse_vector": [], "title": "Algebraic models of simple type theories: A polynomial approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We develop algebraic models of simple type theories, laying out a framework that extends universal algebra to incorporate both algebraic sorting and variable binding. Examples of simple type theories include the unityped and simply-typed λ-calculi, the computational λ-calculus, and predicate logic.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394771"}, {"primary_key": "2652511", "vector": [], "sparse_vector": [], "title": "Approximating Values of Generalized-Reachability Stochastic Games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Simple stochastic games are turn-based 2½-player games with a reachability objective. The basic question asks whether one player can ensure reaching a given target with at least a given probability. A natural extension is games with a conjunction of such conditions as objective. Despite a plethora of recent results on the analysis of systems with multiple objectives, the decidability of this basic problem remains open. In this paper, we present an algorithm approximating the Pareto frontier of the achievable values to a given precision. Moreover, it is an anytime algorithm, meaning it can be stopped at any time returning the current approximation and its error bound.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394761"}, {"primary_key": "2652512", "vector": [], "sparse_vector": [], "title": "Complexity of controlled bad sequences over finite sets of Nd.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>"], "summary": "We provide upper and lower bounds for the length of controlled bad sequences over the majoring and the minoring orderings of finite sets of Nd. The results are obtained by bounding the length of such sequences by functions from the Cichon hierarchy. This allows us to translate these results to bounds over the fast-growing complexity classes.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394753"}, {"primary_key": "2652513", "vector": [], "sparse_vector": [], "title": "Deciding Differential Privacy for Programs with Finite Inputs and Outputs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Differential privacy is a de facto standard for statistical computations over databases that contain private data. Its main and rather surprising strength is to guarantee individual privacy and yet allow for accurate statistical results. Thanks to its mathematical definition, differential privacy is also a natural target for formal analysis. A broad line of work develops and uses logical methods for proving privacy. A more recent and complementary line of work uses statistical methods for finding privacy violations. Although both lines of work are practically successful, they elide the fundamental question of decidability.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394796"}, {"primary_key": "2652514", "vector": [], "sparse_vector": [], "title": "Universal equivalence and majority of probabilistic programs over finite fields.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study decidability problems for equivalence of probabilistic programs, for a core probabilistic programming language over finite fields of fixed characteristic. The programming language supports uniform sampling, addition, multiplication and conditionals and thus is sufficiently expressive to encode boolean and arithmetic circuits. We consider two variants of equivalence: the first one considers an interpretation over the finite field Fq, while the second one, which we call universal equivalence, verifies equivalence over all extensions Fqk of Fq. The universal variant typically arises in provable cryptography when one wishes to prove equivalence for any length of bitstrings, i.e., elements of F2k for any k. While the first problem is obviously decidable, we establish its exact complexity which lies in the counting hierarchy. To show decidability, and a doubly exponential upper bound, of the universal variant we rely on results from algorithmic number theory and the possibility to compare local zeta functions associated to given polynomials. Finally we study several variants of the equivalence problem, including a problem we call majority, motivated by differential privacy.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394746"}, {"primary_key": "2652515", "vector": [], "sparse_vector": [], "title": "Modal Logics with Composition on Finite Forests: Expressivity and Complexity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the expressivity and complexity of two modal logics interpreted on finite forests and equipped with standard modalities to reason on submodels. The logic ML(|) extends the modal logic K with the composition operator | from ambient logic, whereas ML(*) features the separating conjunction * from separation logic. Both operators are second-order in nature. We show that ML(|) is as expressive as the graded modal logic GML (on trees) whereas ML(*) is strictly less expressive than GML. Moreover, we establish that the satisfiability problem is Tower-complete for ML(*), whereas it is (only) AExpPol-complete for ML(|), a result which is surprising given their relative expressivity. As by-products, we solve open problems related to sister logics such as static ambient logic and modal separation logic.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394787"}, {"primary_key": "2652516", "vector": [], "sparse_vector": [], "title": "A Hennessy-<PERSON><PERSON> for ATL with Imperfect Information.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that a history-based variant of alternating bisimulation with imperfect information allows it to be related to a variant of Alternating-time Temporal Logic (ATL) with imperfect information by a full Hennes<PERSON><PERSON><PERSON><PERSON> theorem. The variant of ATL we consider has a common knowledge semantics, which requires that the uniform strategy available for a coalition to accomplish some goal must be common knowledge inside the coalition, while other semantic variants of ATL with imperfect information do not accomodate a Hennes<PERSON>-<PERSON><PERSON> theorem. We also show that the existence of a history-based alternating bisimulation between two finite Concurrent Game Structures with imperfect information (iCGS) is undecidable.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394784"}, {"primary_key": "2652517", "vector": [], "sparse_vector": [], "title": "Mixing Probabilistic and non-Probabilistic Objectives in Markov Decision Processes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we consider algorithms to decide the existence of strategies in MDPs for Boolean combinations of objectives. These objectives are omega-regular properties that need to be enforced either surely, almost surely, existentially, or with non-zero probability. In this setting, relevant strategies are randomized infinite memory strategies: both infinite memory and randomization may be needed to play optimally. We provide algorithms to solve the general case of Boolean combinations and we also investigate relevant subcases. We further report on complexity bounds for these problems.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394805"}, {"primary_key": "2652518", "vector": [], "sparse_vector": [], "title": "Hardness Characterisations and Size-Width Lower Bounds for QBF Resolution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We provide a tight characterisation of proof size in resolution for quantified Boolean formulas (QBF) by circuit complexity. Such a characterisation was previously obtained for a hierarchy of QBF Frege systems (Beyersdorff & <PERSON>, LICS 2016), but leaving open the most important case of QBF resolution. Different from the Frege case, our characterisation uses a new version of decision lists as its circuit model, which is stronger than the CNFs the system works with. Our decision list model is well suited to compute countermodels for QBFs.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394793"}, {"primary_key": "2652519", "vector": [], "sparse_vector": [], "title": "The Complexity of Reachability in Affine Vector Addition Systems with States.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Vector addition systems with states (VASS) are widely used for the formal verification of concurrent systems. Given their tremendous computational complexity, practical approaches have relied on techniques such as reachability relaxations, e.g., allowing for negative intermediate counter values. It is natural to question their feasibility for VASS enriched with primitives that typically translate into undecidability. Spurred by this concern, we pinpoint the complexity of integer relaxations w.r.t. arbitrary classes of affine operations.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394741"}, {"primary_key": "2652520", "vector": [], "sparse_vector": [], "title": "Temporal Constraint Satisfaction Problems in Fixed-Point Logic.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Finite-domain constraint satisfaction problems are either solvable by Datalog, or not even expressible in fixed-point logic with counting. The border between the two regimes can be described by a strong height-one <PERSON><PERSON><PERSON> condition. For infinite-domain CSPs, the situation is more complicated even if the template structure of the CSP is model-theoretically tame. We prove that there is no <PERSON><PERSON><PERSON> condition that characterizes Datalog already for the CSPs of first-order reducts of (Q; <); such CSPs are called temporal CSPs and are of fundamental importance in infinite-domain constraint satisfaction. Our main result is a complete classification of temporal CSPs that can be expressed in one of the following logical formalisms: Datalog, fixed-point logic (with or without counting), or fixed-point logic with the Boolean rank operator. The classification shows that many of the equivalent conditions in the finite fail to capture expressibility in Datalog or fixed-point logic already for temporal CSPs.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394750"}, {"primary_key": "2652521", "vector": [], "sparse_vector": [], "title": "First-order tree-to-tree functions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study tree-to-tree transformations that can be defined in first-order logic or monadic second-order logic. We prove a decomposition theorem, which shows that every transformation can be obtained from prime transformations, such as tree-to-tree homomorphisms or pre-order traversal, by using combinators such as function composition.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394785"}, {"primary_key": "2652522", "vector": [], "sparse_vector": [], "title": "Extensions of ω-Regular Languages.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider extensions of monadic second-order logic over ω-words, which are obtained by adding one language that is not ω-regular. We show that if the added language L has a neutral letter, then the resulting logic is necessarily undecidable. A corollary is that the ω-regular languages are the only decidable Boolean-closed full trio over ω-words.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394779"}, {"primary_key": "2652523", "vector": [], "sparse_vector": [], "title": "A tale of intersection types.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>-Ciancaglini"], "summary": "Intersection types have come a long way since their introduction in the Seventies. They have been exploited for characterising behaviours of λ-terms and π-calculus processes, building λ-models, verifying properties of higher-order programs, synthesising code, and enriching the expressivity of programming languages. This paper is a light overview of intersection types and some of their applications.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394733"}, {"primary_key": "2652524", "vector": [], "sparse_vector": [], "title": "A Cellular Howe Theorem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a categorical framework for operational semantics, in which we define substitution-closed bisimilarity, an abstract analogue of the open extension of <PERSON><PERSON>'s applicative bisimilarity. We furthermore prove a congruence theorem for substitution-closed bisimilarity, following <PERSON>'s method. We finally demonstrate that the framework covers the call-by-name and call-by-value variants of λ-calculus in big-step style. As an intermediate result, we generalise the standard framework of <PERSON><PERSON> et al. for syntax with variable binding to the skew-monoidal case.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394738"}, {"primary_key": "2652525", "vector": [], "sparse_vector": [], "title": "On the Weisfeiler-Leman Dimension of Finite Groups.", "authors": ["<PERSON><PERSON><PERSON> Brachter", "<PERSON>"], "summary": "In comparison to graphs, combinatorial methods for the isomorphism problem of finite groups are less developed than algebraic ones. To be able to investigate the descriptive complexity of finite groups and the group isomorphism problem, we define the <PERSON><PERSON><PERSON><PERSON><PERSON> algorithm for groups. In fact we define three versions of the algorithm. In contrast to graphs, where the three analogous versions readily agree, for groups the situation is more intricate. For groups, we show that their expressive power is linearly related. We also give descriptions in terms of counting logics and bijective pebble games for each of the versions.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394786"}, {"primary_key": "2652526", "vector": [], "sparse_vector": [], "title": "A Fixed Point Theorem on Lexicographic Lattice Structures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce the notion of a lexicographic lattice structure, namely a lattice whose elements can be viewed as stratified entities and whose ordering relation compares elements in a lexicographic manner with respect to their strata. These lattices arise naturally in many non-monotonic formalisms, such as normal logic programs, higher-order logic programs with negation, and boolean grammars. We consider functions over such lattices that may overall be non-monotonic, but retain a restricted form of monotonicity inside each stratum. We demonstrate that such functions always have a least fixed point which is also their least pre-fixed point. Moreover, we prove that the sets of pre-fixed and post-fixed points of such functions, are complete lattices. For the special case of a trivial lexicographic lattice structure whose elements essentially consist of a unique stratum, our theorem gives as a special case the well-known <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fixed point theorem. Moreover, our work considerably simplifies and extends recent results on non-monotonic fixed point theory, providing in this way a useful and convenient tool in the semantic investigation of non-monotonic formalisms.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394797"}, {"primary_key": "2652527", "vector": [], "sparse_vector": [], "title": "Re-pairing brackets.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Consider the following one-player game. Take a well-formed sequence of opening and closing brackets (a <PERSON>yck word). As a move, the player can pair any opening bracket with any closing bracket to its right, erasing them. The goal is to re-pair (erase) the entire sequence, and the cost of a strategy is measured by its width: the maximum number of nonempty segments of symbols (separated by blank space) seen during the play.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394752"}, {"primary_key": "2652528", "vector": [], "sparse_vector": [], "title": "The Benefit of Being Non-Lazy in Probabilistic λ-calculus: Applicative Bisimulation is Fully Abstract for Non-Lazy Probabilistic Call-by-Name.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the probabilistic applicative bisimilarity (PAB) --- a coinductive relation comparing the applicative behaviour of probabilistic untyped λ-terms according to a specific operational semantics. This notion has been studied by <PERSON> et al. with respect to the two standard parameter passing policies, call-by-value (cbv) and call-by-name (cbn), using a lazy reduction strategy not reducing within the body of a function. In particular, PAB has been proven to be fully abstract with respect to the contextual equivalence in cbv [6] but not in lazy cbn [16].", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394806"}, {"primary_key": "2652529", "vector": [], "sparse_vector": [], "title": "An Approach to Regular Separability in Vector Addition Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of regular separability of languages of vector addition systems with states (VASS). It asks whether for two given VASS languages K and L, there exists a regular language R that includes K and is disjoint from L. While decidability of the problem in full generality remains an open question, there are several subclasses for which decidability has been shown: It is decidable for (i) one-dimensional VASS, (ii) VASS coverability languages, (iii) languages of integer VASS, and (iv) commutative VASS languages.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394776"}, {"primary_key": "2652530", "vector": [], "sparse_vector": [], "title": "Cones as a model of intuitionistic linear logic.", "authors": ["<PERSON>"], "summary": "For overcoming the limitations of probabilistic coherence spaces which do not seem to provide natural interpretations of continuous data types such as the real line, we introduced with <PERSON><PERSON><PERSON> and <PERSON> a model of probabilistic higher order computation based on (positive) cones, and a class of totally monotone functions that we called \"stable\". Then <PERSON><PERSON><PERSON><PERSON> proved that this model is a conservative extension of the earlier probabilistic coherence space model. We continue these investigations by showing that the category of cones and linear and Scott-continuous functions is a model of intuitionistic linear logic. To define the tensor product, we use the special adjoint functor theorem, and we prove that this operation is an extension of the standard tensor product of probabilistic coherence spaces. We also show that these latter are dense in cones, thus allowing to lift the main properties of the tensor product of probabilistic coherence spaces to general cones. Finally we define in the same way an exponential of cones and extend measurability to these new operations.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394758"}, {"primary_key": "2652531", "vector": [], "sparse_vector": [], "title": "Uniformisations of Regular Relations Over Bi-Infinite Words.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of deciding whether a given mso-definable relation over bi-infinite words contains an mso-definable function with the same domain. We prove that this problem is decidable. There are two obstacles to the existence of such uniformisations: the first is related to the existence of non-trivial automorphisms of bi-infinite words, whereas the second, more subtle obstacle, is related to the existence of finite, discrete dynamical systems, where no trajectory can be selected by an mso formula.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394782"}, {"primary_key": "2652532", "vector": [], "sparse_vector": [], "title": "One-Clock Priced Timed Games are PSPACE-hard.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The main result of this paper is that computing the value of a one-clock priced timed game (OCPTG) is PSPACE-hard. Along the way, we provide a family of OCPTGs that have an exponential number of event points. Both results hold even in very restricted classes of games such as DAGs with treewidth three. Finally, we provide a number of positive results, including polynomial-time algorithms for even more restricted classes of OCPTGs such as trees.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394772"}, {"primary_key": "2652533", "vector": [], "sparse_vector": [], "title": "Lower Bounds for QBFs of Bounded Treewidth.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The problem of deciding the validity (QSat) of quantified Boolean formulas (QBF) is a vivid research area in both theory and practice. In the field of parameterized algorithmics, the well-studied graph measure treewidth turned out to be a successful parameter. A well-known result by <PERSON> [9] is that QSat when parameterized by the treewidth of the primal graph and the quantifier rank of the input formula is fixed-parameter tractable. More precisely, the runtime of such an algorithm is polynomial in the formula size and exponential in the treewidth, where the exponential function in the treewidth is a tower, whose height is the quantifier rank. A natural question is whether one can significantly improve these results and decrease the tower while assuming the Exponential Time Hypothesis (ETH). In the last years, there has been a growing interest in the quest of establishing lower bounds under ETH, showing mostly problem-specific lower bounds up to the third level of the polynomial hierarchy. Still, an important question is to settle this as general as possible and to cover the whole polynomial hierarchy. In this work, we show lower bounds based on the ETH for arbitrary QBFs parameterized by treewidth and quantifier rank. More formally, we establish lower bounds for QSat and treewidth, namely, that under ETH there cannot be an algorithm that solves QSat of quantifier rank i in runtime significantly better than i-fold exponential in the treewidth and polynomial in the input size. In doing so, we provide a reduction technique to compress treewidth that encodes dynamic programming on arbitrary tree decompositions. Further, we describe a general methodology for a more finegrained analysis of problems parameterized by treewidth that are at higher levels of the polynomial hierarchy. Finally, we illustrate the usefulness of our results by discussing various applications of our results to problems that are located higher on the polynomial hierarchy, in particular, various problems from the literature such as projected model counting problems.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394756"}, {"primary_key": "2652534", "vector": [], "sparse_vector": [], "title": "Coherence and normalisation-by-evaluation for bicategorical cartesian closed structure.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present two proofs of coherence for cartesian closed bicategories. Precisely, we show that in the free cartesian closed bicategory on a set of objects there is at most one structural 2-cell between any parallel pair of 1-cells. We thereby reduce the difficulty of constructing structure in arbitrary cartesian closed bicategories to the level of 1-dimensional category theory. Our first proof follows a traditional approach using the <PERSON><PERSON><PERSON> lemma. For the second proof, we adapt <PERSON><PERSON>'s categorical analysis of normalisation-by-evaluation for the simply-typed lambda calculus. <PERSON><PERSON><PERSON> the construction of suitable bicategorical structures, the argument is not significantly more complex than its 1-categorical counterpart. It also opens the way for further proofs of coherence using (adaptations of) tools from categorical semantics.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394769"}, {"primary_key": "2652535", "vector": [], "sparse_vector": [], "title": "Linear Dependent Type Theory for Quantum Programming Languages: Extended Abstract.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern quantum programming languages integrate quantum resources and classical control. They must, on the one hand, be linearly typed to reflect the no-cloning property of quantum resources. On the other hand, high-level and practical languages should also support quantum circuits as first-class citizens, as well as families of circuits that are indexed by some classical parameters. Quantum programming languages thus need linear dependent type theory. This paper defines a general semantic structure for such a type theory via certain fibrations of monoidal categories. The categorical model of the quantum circuit description language Proto-Quipper-M in [28] constitutes an example of such a fibration, which means that the language can readily be integrated with dependent types. We then devise both a general linear dependent type system and a dependently typed extension of Proto-Quipper-M, and provide them with operational semantics as well as a prototype implementation.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394765"}, {"primary_key": "2652536", "vector": [], "sparse_vector": [], "title": "Bisimulation Finiteness of Pushdown Systems Is Elementary.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that in case a pushdown system is bisimulation equivalent to a finite system, there is already a bisimulation equivalent finite system whose size is elementarily bounded in the description size of the pushdown system. As a consequence we obtain that it is elementarily decidable if a given pushdown system is bisimulation equivalent to some finite system. This improves a previously best-known ACKERMANN upper bound for this problem.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394827"}, {"primary_key": "2652537", "vector": [], "sparse_vector": [], "title": "A Complete Proof System for 1-Free Regular Expressions Modulo Bisimilarity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON> (1984) gave a sound proof system for bisimilarity of regular expressions interpreted as processes: Basic Process Algebra with unary Kleene star iteration, deadlock 0, successful termination 1, and a fixed-point rule. He asked whether this system is complete. Despite intensive research over the last 35 years, the problem is still open.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394744"}, {"primary_key": "2652538", "vector": [], "sparse_vector": [], "title": "Automatic Structures: Twenty Years Later.", "authors": ["<PERSON>"], "summary": "Automatic structures made their appearance at LICS twenty years ago, at LICS 2000. However, their roots are much older. The idea of automata based decision procedures for logical theories can be traced back to the early days of automata theory and to the work of <PERSON><PERSON><PERSON>, Elgot, Trakht<PERSON> and <PERSON><PERSON> in the 1960s. The explicit notion of automatic structures has first been proposed in 1976 in the (unfortunately largely unnoticed) PhD thesis of <PERSON><PERSON>, and later been reinvented by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> in 1995.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394734"}, {"primary_key": "2652539", "vector": [], "sparse_vector": [], "title": "Successor-Invariant First-Order Logic on Classes of Bounded Degree.", "authors": ["<PERSON>"], "summary": "We study the expressive power of successor-invariant first-order logic, which is an extension of first-order logic where the usage of an additional successor relation on the structure is allowed, as long as the validity of formulas is independent on the choice of a particular successor.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394767"}, {"primary_key": "2652540", "vector": [], "sparse_vector": [], "title": "Multimodal Dependent Type Theory.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce MTT, a dependent type theory which supports multiple modalities. MTT is parametrized by a mode theory which specifies a collection of modes, modalities, and transformations between them. We show that different choices of mode theory allow us to use the same type theory to compute and reason in many modal situations, including guarded recursion, axiomatic cohesion, and parametric quantification. We reproduce examples from prior work in guarded recursion and axiomatic cohesion--demonstrating that MTT constitutes a simple and usable syntax whose instantiations intuitively correspond to previous handcrafted modal type theories. In some cases, instantiating MTT to a particular situation unearths a previously unknown type theory that improves upon prior systems. Finally, we investigate the metatheory of MTT. We prove the consistency of MTT and establish canonicity through an extension of recent type-theoretic gluing techniques. These results hold irrespective of the choice of mode theory, and thus apply to a wide variety of modal situations.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394736"}, {"primary_key": "2652541", "vector": [], "sparse_vector": [], "title": "Counting Bounded Tree Depth Homomorphisms.", "authors": ["<PERSON>"], "summary": "We prove that graphs G, G' satisfy the same sentences of first-order logic with counting of quantifier rank at most k if and only if they are homomorphism-indistinguishable over the class of all graphs of tree depth at most k. Here G, G' are homomorphism-indistinguishable over a class F of graphs if for each graph F ϵ F, the number of homomorphisms from F to G equals the number of homomorphisms from F to G'.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394739"}, {"primary_key": "2652542", "vector": [], "sparse_vector": [], "title": "Modal Intuitionistic Logics as Dialgebraic Logics.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Duality is one of the key techniques in the categorical treatment of modal logics. From the duality between (modal) algebras and (descriptive) frames one derives e.g. completeness (via a syntactic characterisation of algebras) or definability (using a suitable version of the <PERSON><PERSON><PERSON><PERSON> theorem). This is by now well understood for classical modal logics and modal logics based on distributive lattices, via extensions of <PERSON> and <PERSON><PERSON> duality, respectively. What is conspicuously absent is a comprehensive treatment of modal intuitionistic logic. This is the gap we are closing in this paper. Our main conceptual insight is that modal intuitionistic logics do not appear as algebra/coalgebra dualities, but instead arise naturally as dialgebras. Our technical contribution is the development of dualities for dialgebras, together with their logics, that instantiate to large class of modal intuitionistic logics and their frames as special cases. We derive completeness and expressiveness results in this general case. For modal intuitionistic logic, this systematises the existing treatment in the literature.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394807"}, {"primary_key": "2652543", "vector": [], "sparse_vector": [], "title": "A tier-based typed programming language characterizing Feasible Functionals.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The class of Basic Feasible Functionals BFF2 is the type-2 counterpart of the class FP of type-1 functions computable in polynomial time. Several characterizations have been suggested in the literature, but none of these present a programming language with a type system guaranteeing this complexity bound. We give a characterization of BFF2 based on an imperative language with oracle calls using a tier-based type system whose inference is decidable. Such a characterization should make it possible to link higher-order complexity with programming theory. The low complexity (cubic in the size of the program) of the type inference algorithm contrasts with the intractability of the aforementioned methods and does not restrain strongly the expressive power of the language.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394768"}, {"primary_key": "2652544", "vector": [], "sparse_vector": [], "title": "Descriptive complexity of real computation and probabilistic independence logic.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a novel variant of BSS machines called Separate Branching BSS machines (S-BSS in short) and develop a Fagin-type logical characterisation for languages decidable in non-deterministic polynomial time by S-BSS machines. We show that NP on S-BSS machines is strictly included in NP on BSS machines and that every NP language on S-BSS machines is a countable union of closed sets in the usual topology of R^n. Moreover, we establish that on Boolean inputs NP on S-BSS machines without real constants characterises a natural fragment of the complexity class existsR (a class of problems polynomial time reducible to the true existential theory of the reals) and hence lies between NP and PSPACE. Finally we apply our results to determine the data complexity of probabilistic independence logic.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394773"}, {"primary_key": "2652545", "vector": [], "sparse_vector": [], "title": "A calculus of expandable stores: Continuation-and-environment-passing style translations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The call-by-need evaluation strategy for the λ-calculus is an evaluation strategy that lazily evaluates arguments only if needed, and if so, shares computations across all places where it is needed. To implement this evaluation strategy, abstract machines require some form of global environment. While abstract machines usually lead to a better understanding of the flow of control during the execution, facilitating in particular the definition of continuation-passing style translations, the case of machines with global environments turns out to be much more subtle.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394792"}, {"primary_key": "2652546", "vector": [], "sparse_vector": [], "title": "Intermediate problems in modular circuits satisfiability.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In [15] a generalization of Boolean circuits to arbitrary finite algebras had been introduced and applied to sketch P versus NP-complete borderline for circuits satisfiability over algebras from congruence modular varieties. However the problem for nilpotent (which had not been shown to be NP-hard) but not supernilpotent algebras (which had been shown to be polynomial time) remained open.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394780"}, {"primary_key": "2652547", "vector": [], "sparse_vector": [], "title": "The Surprising Power of Constant Depth Algebraic Proofs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A major open problem in proof complexity is to prove superpolynomial lower bounds for AC0[p]-Frege proofs. This system is the analog of AC0 [p], the class of bounded depth circuits with prime modular counting gates. Despite strong lower bounds for this class dating back thirty years ([28, 30]), there are no significant lower bounds for AC0 [p]-Frege. Significant and extensive degree lower bounds have been obtained for a variety of subsystems of AC0[p]-Frege, including Nullstellensatz ([3]), Polynomial Calculus ([9]), and SOS ([14]). However to date there has been no progress on AC0 [p]-Frege lower bounds.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394754"}, {"primary_key": "2652548", "vector": [], "sparse_vector": [], "title": "Interaction Laws of Monads and Comonads.", "authors": ["<PERSON><PERSON><PERSON>", "Exequiel Rivas", "<PERSON><PERSON><PERSON>"], "summary": "We introduce and study functor-functor and monad-comonad interaction laws as mathematical objects to describe interaction of effectful computations with behaviors of effect-performing machines. Monad-comonad interaction laws are monoid objects of the monoidal category of functor-functor interaction laws. We show that, for suitable generalizations of the concepts of dual and <PERSON><PERSON><PERSON> dual, the greatest functor resp. monad interacting with a given functor or comonad is its dual while the greatest comonad interacting with a given monad is its <PERSON><PERSON><PERSON> dual. We relate monad-comonad interaction laws to stateful runners. We show that functor-functor interaction laws are Chu spaces over the category of endofunctors taken with the Day convolution monoidal structure. <PERSON><PERSON><PERSON>'s glueing endows the category of these Chu spaces with a monoidal structure whose monoid objects are monad-comonad interaction laws.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394808"}, {"primary_key": "2652549", "vector": [], "sparse_vector": [], "title": "Consuming and Persistent Types for Classical Logic.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We prove that type systems are able to capture exact measures related to dynamic properties of functional programs with control operators, which allow implementing intricate continuations and backtracking. Our type systems give the number of evaluation steps to normal form as well as the size of this normal form without any evaluation being needed.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394774"}, {"primary_key": "2652550", "vector": [], "sparse_vector": [], "title": "Refinement-Based Game Semantics for Certified Abstraction Layers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Formal methods have advanced to the point where the functional correctness of various large system components has been mechanically verified. However, the diversity of semantic models used across projects makes it difficult to connect these component to build larger certified systems. Given this, we seek to embed these models and proofs into a generalpurpose framework where they could interact. We believe that a synthesis of game semantics, the refinement calculus, and algebraic effects can provide such a framework.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394799"}, {"primary_key": "2652551", "vector": [], "sparse_vector": [], "title": "Large and Infinitary Quotient Inductive-Inductive Types.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quotient inductive-inductive types (QIITs) are generalized inductive types which allow sorts to be indexed over previously declared sorts, and allow usage of equality constructors. QIITs are especially useful for algebraic descriptions of type theories and constructive definitions of real, ordinal and surreal numbers. We develop new metatheory for large QIITs, large elimination, recursive equations and infinitary constructors. As in prior work, we describe QIITs using a type theory where each context represents a QIIT signature. However, in our case the theory of signatures can also describe its own signature, modulo universe sizes. We bootstrap the model theory of signatures using self-description and a Church-coded notion of signature, without using complicated raw syntax or assuming an existing internal QIIT of signatures. We give semantics to described QIITs by modeling each signature as a finitely complete CwF (category with families) of algebras. Compared to the case of finitary QIITs, we additionally need to show invariance under algebra isomorphisms in the semantics. We do this by modeling signature types as isofibrations. Finally, we show by a term model construction that every QIIT is constructible from the syntax of the theory of signatures.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394770"}, {"primary_key": "2652552", "vector": [], "sparse_vector": [], "title": "Coherence via Well-Foundedness: Taming Set-Quotients in Homotopy Type Theory.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Suppose we are given a graph and want to show a property for all its cycles (closed chains). Induction on the length of cycles does not work since sub-chains of a cycle are not necessarily closed. This paper derives a principle reminiscent of induction for cycles for the case that the graph is given as the symmetric closure of a locally confluent and (co-)well-founded relation. We show that, assuming the property in question is sufficiently nice, it is enough to prove it for the empty cycle and for cycles given by local confluence.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394800"}, {"primary_key": "2652553", "vector": [], "sparse_vector": [], "title": "Good-for-games ω-Pushdown Automata.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce good-for-games ω-pushdown automata (ω-GFG-PDA). These are automata whose nondeterminism can be resolved based on the run constructed thus far. Good-for-gameness enables automata to be composed with games, trees, and other automata, applications which otherwise require deterministic automata.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394737"}, {"primary_key": "2652554", "vector": [], "sparse_vector": [], "title": "When Reachability Meets Grzegorczyk.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Vector addition systems with states, or equivalently vector addition systems, or Petri nets are a long established model of concurrency with extensive applications in modelling and analysis of hardware, software and database systems, as well as chemical, biological and business processes. The central algorithmic problem is reachability: whether from a given initial configuration there exists a sequence of valid execution steps that reaches a given final configuration. The complexity of the problem has remained unsettled since the 1960s, and it is one of the most prominent open questions in the theory of computation.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394732"}, {"primary_key": "2652555", "vector": [], "sparse_vector": [], "title": "Pebble Minimization of Polyregular Functions.", "authors": ["<PERSON>"], "summary": "We show that a polyregular word-to-word function is regular if and only its output size is at most linear in its input size. Moreover a polyregular function can be realized by: a transducer with two pebbles if and only if its output has quadratic size in its input, a transducer with three pebbles if and only if its output has cubic size in its input, etc.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394804"}, {"primary_key": "2652556", "vector": [], "sparse_vector": [], "title": "Sparse Hashing for Scalable Approximate Model Counting: Theory and Practice.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given a CNF formula F on n variables, the problem of model counting, also referred to as #SAT, is to compute the number of models or satisfying assignments of F. Recent years have witnessed a surge of effort towards developing efficient algorithmic techniques that combine the classical strongly 2-universal hash functions (from [<PERSON> 1983]) with the remarkable progress in SAT solving over the past decade. These techniques augment the CNF formula F with random XOR constraints and invoke an NP oracle repeatedly on the resultant CNF-XOR formulas. In practice, the NP oracle calls are replaced by calls to a SAT solver and it is observed that runtime performance of modern SAT solvers (based on conflict-driven clause learning) on CNF-XOR formulas is adversely affected by the size of XOR constraints. The standard construction of 2-universal hash functions chooses every variable with probability p = 1/2 leading to XOR constraints of size n/2 in expectation. Consequently, the main challenge is to design sparse hash functions, where variables can be chosen with smaller probability and lead to smaller sized XOR constraints, which can then replace strongly 2-universal hash functions.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394809"}, {"primary_key": "2652557", "vector": [], "sparse_vector": [], "title": "Concurrent Separation Logic Meets Template Games.", "authors": ["<PERSON><PERSON><PERSON>", "Léo <PERSON>"], "summary": "An old dream of concurrency theory and programming language semantics has been to uncover the fundamental synchronization mechanisms which regulate situations as different as game semantics for higher-order programs, and Hoare logic for concurrent programs with shared memory and locks. In this paper, we establish a deep and unexpected connection between two recent lines of work on concurrent separation logic (CSL) and on template game semantics for differential linear logic (DiLL). Thanks to this connection, we reformulate in the purely conceptual style of template games for DiLL the asynchronous and interactive interpretation of CSL designed by <PERSON><PERSON>\\`es and Stefanesco. We believe that the analysis reveals something important about the secret anatomy of CSL, and more specifically about the subtle interplay, of a categorical nature, between sequential composition, parallel product, errors and locks.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394762"}, {"primary_key": "2652558", "vector": [], "sparse_vector": [], "title": "The Hidden Subgroup Problem for Universal Algebras.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The Hidden Subgroup Problem (HSP) is a computational problem which includes as special cases integer factorization, the discrete logarithm problem, graph isomorphism, and the shortest vector problem. The celebrated polynomial-time quantum algorithms for factorization and the discrete logarithm are restricted versions of a generic polynomial-time quantum solution to the HSP for abelian groups, but despite focused research no full solution has yet been found. We propose a generalization of the HSP to include arbitrary algebraic structures and analyze this new problem on powers of 2-element algebras. We prove a complete classification of every such power as quantum tractable (i.e. polynomial-time), classically tractable, quantum intractable, and classically intractable. In particular, we identify a class of algebras for which the generalized HSP exhibits super-polynomial speedup on a quantum computer compared to a classical one.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394764"}, {"primary_key": "2652559", "vector": [], "sparse_vector": [], "title": "Russian Constructivism in a Prefascist Theory.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The results from this paper are twofold. First, we give a purely syntactic presheaf model of CIC. Contrarily to similar endeavours, this variant both preserves conversion and interprets full dependent elimination.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394740"}, {"primary_key": "2652560", "vector": [], "sparse_vector": [], "title": "Contextual Types, Explained: Invited Tutorial.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Contextual objects characterize an object M together with the typing context Ψ in which it is meaningful. This idea is then also internalized within the type theory itself using the notion of a contextual type which pairs the type A of an object together with the context Ψ in which the object is well-typed. In this tutorial, we review the origins of this idea and show its power in characterizing partial programs, mechanizing meta-theory, and meta-programming. Starting from the simply typed setting, we give an overview of existing work which adopts contextual types to dependent type theories and touch on future research directions.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394735"}, {"primary_key": "2652561", "vector": [], "sparse_vector": [], "title": "Extended Kripke lemma and decidability for hypersequent substructural logics.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We establish the decidability of every axiomatic extension of the commutative Full Lambek calculus with contraction FLec that has a cut-free hypersequent calculus. The axioms include familiar properties such as linearity (fuzzy logics) and the substructural versions of bounded width and weak excluded middle. <PERSON><PERSON><PERSON> famously proved the decidability of FLec by combining structural proof theory and combinatorics. This work significantly extends both ingredients: height-preserving admissibility of contraction by internalising a fixed amount of contraction (a Curry's lemma for hypersequent calculi) and an extended <PERSON><PERSON><PERSON> lemma for hypersequents that relies on the componentwise partial order on n-tuples being an ω2-well-quasi-order.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394802"}, {"primary_key": "2652562", "vector": [], "sparse_vector": [], "title": "Partial Univalence in n-truncated Type Theory.", "authors": ["<PERSON>", "<PERSON>"], "summary": "It is well known that univalence is incompatible with uniqueness of identity proofs (UIP), the axiom that all types are h-sets. This is due to finite h-sets having non-trivial automorphisms as soon as they are not h-propositions.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394759"}, {"primary_key": "2652563", "vector": [], "sparse_vector": [], "title": "Resolving finite indeterminacy: A definitive constructive universal prime ideal theorem.", "authors": ["<PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "Dynamical methods were designed to eliminate the ideal objects abstract algebra abounds with. Typically granted by an incarnation of <PERSON><PERSON>'s Lemma, those ideal objects often serve for proving the semantic conservation of additional non-deterministic sequents, that is, with finite but not necessarily singleton succedents. Eliminating ideal objects dynamically was possible also because (finitary) coherent or geometric logic predominates in that area: the use of a non-deterministic axiom can be captured by a finite branching of the proof tree.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394777"}, {"primary_key": "2652564", "vector": [], "sparse_vector": [], "title": "An Efficient Normalisation Procedure for Linear Temporal Logic and Very Weak Alternating Automata.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the mid 80s, Lichtenstein, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> proved a classical theorem stating that every formula of Past LTL (the extension of LTL with past operators) is equivalent to a formula of the form Λni =1 GFφi ∨FGψi, where φi and ψi contain only past operators. Some years later, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> built on this result to derive a similar normal form for LTL. Both normalisation procedures have a non-elementary worst-case blow-up, and follow an involved path from formulas to counter-free automata to star-free regular expressions and back to formulas. We improve on both points. We present a direct and purely syntactic normalisation procedure for LTL yielding a normal form, comparable to the one by <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, that has only a single exponential blow-up. As an application, we derive a simple algorithm to translate LTL into deterministic Rabin automata. The algorithm normalises the formula, translates it into a special very weak alternating automaton, and applies a simple determinisation procedure, valid only for these special automata.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394743"}, {"primary_key": "2652565", "vector": [], "sparse_vector": [], "title": "Sequential Colimits in Homotopy Type Theory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sequential colimits are an important class of higher inductive types. We present a self-contained and fully formalized proof of the conjecture that in homotopy type theory sequential colimits appropriately commute with Σ-types. This result allows us to give short proofs of a number of useful corollaries, some of which were conjectured in other works: the commutativity of sequential colimits with identity types, with homotopy fibers, loop spaces, and truncations, and the preservation of the properties of truncatedness and connectedness under sequential colimits. Our entire development carries over to (∞, 1)-toposes using <PERSON><PERSON>'s recent interpretation of homotopy type theory into these structures.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394801"}, {"primary_key": "2652566", "vector": [], "sparse_vector": [], "title": "Making Streett Determinization Tight.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Optimal determinization construction of Streett automata is an important research problem because it is indispensable in numerous applications such as decision problems for tree temporal logics, logic games and system synthesis. This paper presents a transformation from nondeterministic Streett automata (NSA) with n states and k Streett pairs to equivalent deterministic Rabin transition automata (DRTA) with n5n(n!)n states, O(nn2) Rabin pairs for k = ω(n) and n5nknk states, O(knk) Rabin pairs for k = O(n). This improves the state of the art Streett determinization construction with n5n(n!)n+1 states, O(n2) Rabin pairs and n5nknkn! states, O(nk) Rabin pairs, respectively. Moreover, deterministic parity transition automata (DPTA) are obtained with 3(n(n + 1) -- 1)!(n!)n+1 states, 2n(n +1) priorities for k = ω(n) and 3(n(k +1) -- 1)!n!knk states, 2n(k + 1) priorities for k = O(n), which improves the best construction with nn(k + 1)n(k+1)(n(k + 1) -- 1)! states, 2n(k + 1) priorities. Further, we prove a lower bound state complexity for determinization construction from N-SA to deterministic Rabin (transition) automata i.e. n5n(n!)n for k = ω(n) and n5nknk for k = O(n), which matches the state complexity of the proposed determinization construction. Besides, we put forward a lower bound state complexity for determinization construction from NSA to deterministic parity (transition) automata i.e. 2ω(n2 log n) for k = ω(n) and 2ω(nk log nk) for k = O(n), which is the same as the state complexity of the proposed determinization construction in the exponent.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394757"}, {"primary_key": "2652567", "vector": [], "sparse_vector": [], "title": "Register Automata with Extrema Constraints, and an Application to Two-Variable Logic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a model of register automata over infinite trees with extrema constraints. Such an automaton can store elements of a linearly ordered domain in its registers, and can compare those values to the suprema and infima of register values in subtrees. We show that the emptiness problem for these automata is decidable.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394748"}, {"primary_key": "2652568", "vector": [], "sparse_vector": [], "title": "On Computability of Logical Approaches to Branching-Time Property Verification of Programs.", "authors": ["<PERSON><PERSON>"], "summary": "This paper studies the hardness of branching-time property verification of Turing-complete programming languages, as well as logical approaches to the verification problem. As these approaches reduce the verification problem to logical problems, e.g. the satisfiability problem of Horn clauses with certain extensions, it is natural to ask whether the logical problems are as hard as the verification problem or strictly harder. This paper reveals that logical problems used in most approaches are far more difficult than the verification problem; the only exception is the validity problem of first-order arithmetic with fixed-point operators. We also answers some other natural questions, for example, whether the extensions of Horn clauses are necessarily.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394766"}, {"primary_key": "2652569", "vector": [], "sparse_vector": [], "title": "Automata Learning: An Algebraic Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a generic categorical framework for learning unknown formal languages of various types (e.g. finite or infinite words, weighted and nominal languages). Our approach is parametric in a monad T that represents the given type of languages and their recognizing algebraic structures. Using the concept of an automata presentation of T-algebras, we demonstrate that the task of learning a T-recognizable language can be reduced to learning an abstract form of algebraic automaton whose transitions are modeled by a functor. For the important case of adjoint automata, we devise a learning algorithm generalizing <PERSON><PERSON><PERSON>'s L*. The algorithm is phrased in terms of categorically described extension steps; we provide for a termination and complexity analysis based on a dedicated notion of finiteness. Our framework applies to structures like ω-regular languages that were not within the scope of existing categorical accounts of automata learning. In addition, it yields new learning algorithms for several types of languages for which no such algorithms were previously known at all, including sorted languages, nominal languages with name binding, and cost functions.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394775"}, {"primary_key": "2652570", "vector": [], "sparse_vector": [], "title": "A Constructive Model of Directed Univalence in Bicubical Sets.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Directed type theory is an analogue of homotopy type theory where types represent categories, generalizing groupoids. A bisimplicial approach to directed type theory, developed by <PERSON><PERSON><PERSON> and <PERSON><PERSON>, is based on equipping each type with both a notion of path and a separate notion of directed morphism. In this setting, a directed analogue of the univalence axiom asserts that there is a universe of covariant discrete fibrations whose directed morphisms correspond to functions---a higher-categorical analogue of the category of sets and functions. In this paper, we give a constructive model of a directed type theory with directed univalence in bicubical, rather than bisimplicial, sets. We formalize much of this model using Agda as the internal language of a 1-topos, following <PERSON><PERSON> and <PERSON>. First, building on the cubical techniques used to give computational models of homotopy type theory, we show that there is a universe of covariant discrete fibrations, with a partial directed univalence principle asserting that functions are a retract of morphisms in this universe. To complete this retraction into an equivalence, we refine the universe of covariant fibrations using the constructive sheaf models by <PERSON><PERSON><PERSON> and <PERSON><PERSON>.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394794"}, {"primary_key": "2652571", "vector": [], "sparse_vector": [], "title": "Constructing Higher Inductive Types as Groupoid Quotients.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper, we show that all finitary 1-truncated higher inductive types (HITs) can be constructed from the groupoid quotient. We start by defining internally a notion of signatures for HITs, and for each signature, we construct a bicategory of algebras in 1-types and in groupoids. We continue by proving initial algebra semantics for our signatures. After that, we show that the groupoid quotient induces a biadjunction between the bicategories of algebras in 1-types and in groupoids. We finish by constructing a biinitial object in the bicategory of algebras in groupoids. From all this, we conclude that all finitary 1-truncated HITs can be constructed from the groupoid quotient. All the results are formalized over the UniMath library of univalent mathematics in Coq.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394803"}, {"primary_key": "2652572", "vector": [], "sparse_vector": [], "title": "A characterisation of ordered abstract probabilities.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In computer science, especially when dealing with quantum computing or other non-standard models of computation, basic notions in probability theory like \"a predicate\" vary wildly. There seems to be one constant: the only useful example of an algebra of probabilities is the real unit interval. In this paper we try to explain this phenomenon. We will show that the structure of the real unit interval naturally arises from a few reasonable assumptions. We do this by studying effect monoids, an abstraction of the algebraic structure of the real unit interval: it has an addition x + y which is only defined when x + y ≤ 1 and an involution x~1 -- x which make it an effect algebra, in combination with an associative (possibly non-commutative) multiplication. Examples include the unit intervals of ordered rings and Boolean algebras.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394742"}, {"primary_key": "2652573", "vector": [], "sparse_vector": [], "title": "On The Relational Width of First-Order Expansions of Finitely Bounded Homogeneous Binary Cores with Bounded Strict Width.", "authors": ["<PERSON><PERSON>"], "summary": "The relational width of a finite structure, if bounded, is always (1, 1) or (2, 3). In this paper we study the relational width of first-order expansions of finitely bounded homogeneous binary cores where binary cores are structures with equality and some anti-reflexive binary relations such that for any two different elements a, b in the domain there is exactly one binary relation R with (a, b) ϵ R.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718.3394781"}, {"primary_key": "2701754", "vector": [], "sparse_vector": [], "title": "LICS &apos;20: 35th Annual ACM/IEEE Symposium on Logic in Computer Science, Saarbrücken, Germany, July 8-11, 2020.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This volume contains the proceedings of the 35th Annual ACM/IEEE Symposium on Logic in Computer Science (LICS 2020). The symposium has originally been planned to be held in Beijing (China), and then moved to Saarbrücken (Germany). Due to the pandemic, the symposium was held online, in co-location with ICALP 2020, in the period July 8-11, being organized by Saarland Informatics Campus.", "published": "2020-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1145/3373718"}]