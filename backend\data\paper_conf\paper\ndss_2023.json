[{"primary_key": "1233850", "vector": [], "sparse_vector": [], "title": "REaaS: Enabling Adversarially Robust Downstream Classifiers via Robust Encoder as a Service.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Jia", "<PERSON>"], "summary": "Encoder as a service is an emerging cloud service. Specifically, a service provider first pre-trains an encoder (i.e., a general-purpose feature extractor) via either supervised learning or self-supervised learning and then deploys it as a cloud service API. A client queries the cloud service API to obtain feature vectors for its training/testing inputs when training/testing its classifier (called downstream classifier). A downstream classifier is vulnerable to adversarial examples, which are testing inputs with carefully crafted perturbation that the downstream classifier misclassifies. Therefore, in safety and security critical applications, a client aims to build a robust downstream classifier and certify its robustness guarantees against adversarial examples. What APIs should the cloud service provide, such that a client can use any certification method to certify the  robustness of its downstream classifier against adversarial examples while minimizing the number of queries to the APIs? How can a service provider pre-train an encoder such that clients can build more certifiably robust downstream classifiers? We aim to answer the two questions in this work. For the first question, we show that the cloud service only needs to provide two APIs, which we carefully design, to enable a client to certify the robustness of its downstream classifier with a minimal number of queries to the APIs. For the second question, we show that an encoder pre-trained using a spectral-norm regularization term enables clients to build more robust downstream classifiers.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233851", "vector": [], "sparse_vector": [], "title": "Brokenwire : Wireless Disruption of CCS Electric Vehicle Charging.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a novel attack against the Combined Charging System, one of the most widely used DC rapid charging technologies for electric vehicles (EVs). Our attack, Brokenwire, interrupts necessary control communication between the vehicle and charger, causing charging sessions to abort. The attack requires only temporary physical proximity and can be conducted wirelessly from a distance, allowing individual vehicles or entire fleets to be disrupted stealthily and simultaneously. In addition, it can be mounted with off-the-shelf radio hardware and minimal technical knowledge. By exploiting CSMA/CA behavior, only a very weak signal needs to be induced into the victim to disrupt communication — exceeding the effectiveness of broadband noise jamming by three orders of magnitude. The exploited behavior is a required part of the HomePlug Green PHY, DIN 70121 & ISO 15118 standards and all known implementations exhibit it. We first study the attack in a controlled testbed and then demonstrate it against eight vehicles and 20 chargers in real deployments. We find the attack to be successful in the real world, at ranges up to 47 m, for a power budget of less than 1 W. We further show that the attack can work between the floors of a building (e.g., multi-story parking), through perimeter fences, and from 'drive-by' attacks. We present a heuristic model to estimate the number of vehicles that can be attacked simultaneously for a given output power. Brokenwire has immediate implications for a substantial proportion of the around 12 million battery EVs on the roads worldwide — and profound effects on the new wave of electrification for vehicle fleets, both for private enterprise and crucial public services, as well as electric buses, trucks, and small ships. As such, we conducted a disclosure to the industry and discussed a range of mitigation techniques that could be deployed to limit the impact.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233852", "vector": [], "sparse_vector": [], "title": "BEAGLE: Forensics of Deep Learning Backdoor Attack for Better Defense.", "authors": ["<PERSON><PERSON> Cheng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shengwei An", "<PERSON><PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON>", "Guangyu Shen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shiqing Ma", "<PERSON><PERSON><PERSON>"], "summary": "Deep Learning backdoor attacks have a threat model similar to traditional cyber attacks. Attack forensics, a critical counter-measure for traditional cyber attacks, is hence of importance for defending model backdoor attacks. In this paper, we propose a novel model backdoor forensics technique. Given a few attack samples such as inputs with backdoor triggers, which may represent different types of backdoors, our technique automatically decomposes them to clean inputs and the corresponding triggers. It then clusters the triggers based on their properties to allow automatic attack categorization and summarization. Backdoor scanners can then be automatically synthesized to find other instances of the same type of backdoor in other models. Our evaluation on 2,532 pre-trained models, 10 popular attacks, and comparison with 9 baselines show that our technique is highly effective. The decomposed clean inputs and triggers closely resemble the ground truth. The synthesized scanners substantially outperform the vanilla versions of existing scanners that can hardly generalize to different kinds of attacks.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233853", "vector": [], "sparse_vector": [], "title": "StealthyIMU: Stealing Permission-protected Private Information From Smartphone Voice Assistant Using Zero-Permission Sensors.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>"], "summary": "Voice User Interfaces (VUIs) are becoming an indispensable module that enables hands-free interaction between human users and smartphones. Unfortunately, recent research revealed a side channel that allows zero-permission motion sensors to eavesdrop on the VUI voices from the co-located smartphone loudspeaker. Nonetheless, these threats are limited to leaking a small set of digits and hot words. In this paper, we propose StealthyIMU, a new threat that uses motion sensors to steal permission-protected private information from the VUIs. We develop a set of efficient models to detect and extract private information, taking advantage of the deterministic structures in the VUI responses. Our experiments show that StealthyIMU can steal private information from 23 types of frequently-used voice commands to acquire contacts, search history, calendar, home address, and even GPS trace with high accuracy. We further propose effective mechanisms to defend against StealthyIMU without noticeably impacting the user experience.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233854", "vector": [], "sparse_vector": [], "title": "Attacks as Defenses: Designing Robust Audio CAPTCHAs Using Attacks on Automatic Speech Recognition Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Audio CAPTCHAs are supposed to provide a strong defense for online resources; however, advances in speech-to-text mechanisms have rendered these defenses ineffective.  Audio CAPTCHAs cannot simply be abandoned, as they are specifically named by the W3C as important enablers of accessibility.  Accordingly, demonstrably more robust audio CAPTCHAs are important to the future of a secure and accessible Web.  We look to recent literature on attacks on speech-to-text systems for inspiration for the construction of robust, principle-driven audio defenses.  We begin by comparing 20 recent attack papers, classifying and measuring their suitability to serve as the basis of new \"robust to transcription\" but \"easy for humans to understand\" CAPTCHAs. After showing that none of these attacks alone are sufficient, we propose a new mechanism that is both comparatively intelligible (evaluated through a user study) and hard to automatically transcribe (i.e., $P({rm transcription}) = 4 times 10^{-5}$). We also demonstrate that our audio samples have a high probability of being detected as CAPTCHAs when given to speech-to-text systems ($P({rm evasion}) = 1.77 times 10^{-4}$). Finally, we show that our method is robust to WaveGuard, a popular mechanism designed to defeat adversarial examples (and enable ASRs to output the original transcript instead of the adversarial one). We show that our method can break WaveGuard with a 99% success rate. In so doing, we not only demonstrate a CAPTCHA that is approximately four orders of magnitude more difficult to crack, but that such systems can be designed based on the insights gained from attack papers using the differences between the ways that humans and computers process audio.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233855", "vector": [], "sparse_vector": [], "title": "Navigating Murky Waters: Automated Browser Feature Testing for Uncovering Tracking Vectors.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern web browsers constitute complex application platforms with a wide range of APIs and features. Critically, this includes a multitude of heterogeneous mechanisms that allow sites to store information that explicitly or implicitly alters client-side state or functionality. This behavior implicates any browser storage, cache, access control, and policy mechanism as a potential tracking vector. As demonstrated by prior work, tracking vectors can manifest through elaborate behaviors and exhibit varying characteristics that differ vastly across different browsingcontexts. In this paper we develop CanITrack, an automated, mechanism-agnostic framework for testing browser features and uncovering novel tracking vectors. Our system is designed for facilitating browser vendors and researchers by streamlining the systematic testing of browser mechanisms. It accepts methods to read and write entries for a mechanism and calls these methods across different browsing contexts to determine any potential tracking vulnerabilities that the mechanism may expose. To demonstrate our system’s capabilities we test 21 browser mechanisms and uncover a slew of tracking vectors, including 13 that enable third-party tracking and two that bypass the isolation offered by private browsing modes. Importantly, we show how two separate mechanisms from Google’s highly-publicized and widely-discussed Privacy Sandbox initiative can be leveraged for tracking. Our experimental findings have resulted in 20 disclosure reports across seven major browsers, which have set remediation efforts in motion. Overall, our study highlights the complex and formidable challenge that browsers currently face when trying to balance the adoption of new features and protecting the privacy of their users, as well as the potential benefit of incorporating CanITrack into their internal testing pipeline.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233856", "vector": [], "sparse_vector": [], "title": "Breaking and Fixing Virtual Channels: Domino Attack and Donner.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Payment channel networks (PCNs) mitigate the scalability issues of current decentralized cryptocurrencies. They allow for arbitrarily many payments between users connected through a path of intermediate payment channels, while requiring interacting with the blockchain only to open and close the channels. Unfortunately, PCNs are (i) tailored to payments, excluding more complex smart contract functionalities, such as the oracle-enabling Discreet Log Contracts and (ii) their need for active participation from intermediaries may make payments unreliable, slower, expensive, and privacy-invasive. Virtual channels are among the most promising techniques to mitigate these issues, allowing two endpoints of a path to create a direct channel over the intermediaries without any interaction with the blockchain. After such a virtual channel is constructed, (i) the endpoints can use this direct channel for applications other than payments and (ii) the intermediaries are no longer involved in updates. In this work, we first introduce the Domino attack, a new DoS/griefing style attack that leverages virtual channels to destruct the PCN itself and is inherent to the design adopted by the existing Bitcoin-compatible virtual channels. We then demonstrate its severity by a quantitative analysis on a snapshot of the Lightning Network (LN), the most widely deployed PCN at present. We finally discuss other serious drawbacks of existing virtual channel designs, such as the support for only a single intermediary, a latency and blockchain overhead linear in the path length, or a non-constant storage overhead per user. We then present <PERSON><PERSON>, the first virtual channel construction that overcomes the shortcomings above, by relying on a novel design paradigm. We formally define and prove security and privacy properties in the Universal Composability framework. Our evaluation shows that <PERSON>ner is efficient, reduces the on-chain number of transactions for disputes from linear in the path length to a single one, which is the key to prevent Domino attacks, and reduces the storage overhead from logarithmic in the path length to constant. Donner is Bitcoin-compatible and can be easily integrated in the LN.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233857", "vector": [], "sparse_vector": [], "title": "OptRand: Optimistically Responsive Reconfigurable Distributed Randomness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Public random beacons publish random numbers at regular intervals, which anyone can obtain and verify. The design of public distributed random beacons has been an exciting research direction with significant implications for blockchains, voting, and beyond. Distributed random beacons, in addition to being bias-resistant and unpredictable, also need to have low communication overhead and latency, high resilience to faults, and ease of reconfigurability. Existing synchronous random beacon protocols sacrifice one or more of these properties. In this work, we design an efficient unpredictable synchronous random beacon protocol, OptRand, with quadratic (in the number $n$ of system nodes) communication complexity per beacon output. First, we innovate by employing a novel combination of bilinear pairing based publicly verifiable secret-sharing and non-interactive zero-knowledge proofs to build a linear (in $n$) sized publicly verifiable random sharing. Second, we develop a state machine replication protocol with linear-sized inputs that is also optimistically responsive, i.e., it can progress responsively at actual network speed during optimistic conditions, despite the synchrony assumption, and thus incur low latency. In addition, we present an efficient reconfiguration mechanism for OptRand that allows nodes to leave and join the system. Our experiments show our protocols perform significantly better compared to state-of-the-art protocols under optimistic conditions and on par with state-of-the-art protocols in the normal case. We are also the first to implement a reconfiguration mechanism for distributed beacons and demonstrate that our protocol continues to be live during reconfigurations.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233858", "vector": [], "sparse_vector": [], "title": "How to Count Bots in Longitudinal Datasets of IP Addresses.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Estimating the size of a botnet is one of the most basic and important queries one can make when trying to understand the impact of a botnet. Surprisingly and unfortunately, this seemingly simple task has confounded many measurement efforts. While it may seem tempting to simply count the number of IP addresses observed to be infected, it is well-known that doing so can lead to drastic overestimates, as ISPs commonly assign new IP addresses to hosts. As a result, estimating the number of infected hosts given longitudinal datasets of IP addresses has remained an open problem. In this paper, we present a new data analysis technique, CARDCount, that provides more accurate size estimations by accounting for IP address reassignments. CARDCount can be applied on longer windows of observations than prior approaches (weeks compared to hours), and is the first technique of its kind to provide confidence intervals for its size estimations. We evaluate CARDCount on three real world datasets and show that it performs equally well to existing solutions on synthetic ideal situations, but drastically outperforms all previous work in realistic botnet situations. For the Hajime and Mirai botnets, we estimate that CARDCount, is 51.6% and 69.1% more accurate than the state of the art techniques when estimating the botnet size over a 28-day window.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233859", "vector": [], "sparse_vector": [], "title": "Fine-Grained Trackability in Protocol Executions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a new framework, TrackDev, for encoding and analysing the tracing or \"tracking\" of an entity (e.g., a device) via its executions of a protocol or its usages of a system. TrackDev considers multiple dimensions combined: whether the attacker is active or passive, whether an entity is trackable in its every single appearances or just in a compound set thereof, and whether the entity can be explicitly or implicitly identified. TrackDev can be applied to most identification-based systems. TrackDev is to be applied in practice, over actual executions of systems; to this end, we test TrackDev on real-life traffic for two well-known protocols, the LoRaWAN Join and the 5G handovers, showing new trackability attacks therein and proposing countermeasures. We study the strength of TrackDev's various trackability properties and show that many of our notions are incomparable amongst each other, thus justifying the fine-grained nature of TrackDev. Finally, we detail how the main thrust of TrackDev can be mechanised in formal-verification tools, without any loss; we exemplify this fully on the LoRaWAN Join, in  the Tamarin prover. In this process, we also uncover and discuss within two important aspects: (a) TrackDev’s separation between \"explicit\" and \"implicit\" trackability offers new formal-verification insights; (b) our analyses of the LoRaWAN Join protocol in Tamarin against TrackDev as well as against existing approximations of unlinkability by <PERSON><PERSON><PERSON> et al. concretely show that the latter approximations can be coarser than our notions.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233860", "vector": [], "sparse_vector": [], "title": "No Grammar, No Problem: Towards Fuzzing the Linux Kernel without System-Call Descriptions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The integrity of the entire computing ecosystem depends on the security of our operating systems (OSes). Unfortunately, due to the scale and complexity of OS code, hundreds of security issues are found in OSes, every year. As such, operating systems have constantly been prime use-cases for applying security-analysis tools. In recent years, fuzz-testing has appeared as the dominant technique for automatically finding security issues in software. As such, fuzzing has been adapted to find thousands of bugs in kernels. However, modern OS fuzzers, such as <PERSON>yz<PERSON><PERSON>, rely on precise, extensive, manually created harnesses and grammars for each interface fuzzed within the kernel. Due to this reliance on grammars, current OS fuzzers are faced with scaling-issues. In this paper, we present FuzzNG, our generic approach to fuzzing system-calls on OSes. Unlike Syzkaller, FuzzNG does not require intricate descriptions of system-call interfaces in order to function. Instead FuzzNG leverages fundamental Kernel design features in order to reshape and simplify the fuzzer’s input-space. As such FuzzNG only requires a small config, for each new target: essentially a list of files and system-call numbers the fuzzer should explore. We implemented FuzzNG for the Linux kernel. Testing FuzzNG over 10 Linux components with extensive descrip tions in <PERSON>yzkaller showed that, on average, FuzzNG achieves 102.5% of <PERSON>yz<PERSON><PERSON>’s coverage. FuzzNG found 9 new bugs (5 in components that <PERSON><PERSON><PERSON><PERSON><PERSON> had already fuzzed extensively, for years). Additionally, FuzzNG’s lightweight configs are less than 1.7% the size of <PERSON><PERSON><PERSON><PERSON><PERSON>’s manually-written grammars. <PERSON>rucially, <PERSON>zz<PERSON> achieves this without initial seed-inputs, or expert guidance.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233861", "vector": [], "sparse_vector": [], "title": "BinaryInferno: A Semantic-Driven Approach to Field Inference for Binary Message Formats.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present BinaryInferno, a fully automatic tool for reverse engineering binary message formats. Given a set of messages with the same format, the tool uses an ensemble of detectors to infer a collection of partial descriptions and then automatically integrates the partial descriptions into a semantically-meaningful description that can be used to parse future packets with the same format.  As its ensemble, BinaryInferno uses a modular and extensible set of targeted detectors, including detectors for identifying atomic data types such as IEEE floats, timestamps, and integer length fields; for finding boundaries between adjacent fields using Shannon entropy; and for discovering variable-length sequences by searching for common serialization idioms. We evaluate BinaryInferno's performance on sets of packets drawn from 10 binary protocols. Our semantic-driven approach significantly decreases false positive rates and increases precision when compared to the previous state of the art. For top-level protocols we identify field boundaries with an average precision of 0.69, an average recall of 0.73, and an average false positive rate of 0.04, significantly outperforming five other state-of-the-art protocol reverse engineering tools on the same data sets: AWRE (0.18, 0.03, 0.04), FIELDHUNTER (0.68, 0.37, 0.01), NEMESYS (0.31, 0.44, 0.11), NETPLIER (0.29, 0.75, 0.22), and NETZOB (0.57, 0.42, 0.03). We believe our improvements in precision and false positive rates represent what our target user most wants: semantically meaningful descriptions with fewer false positives.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233862", "vector": [], "sparse_vector": [], "title": "RoVISQ: Reduction of Video Service Quality via Adversarial Attacks on Deep Learning-based Video Compression.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Video compression plays a crucial role in video streaming and classification systems by maximizing the end-user quality of experience (QoE) at a given bandwidth budget. In this paper, we conduct the first systematic study for adversarial attacks on deep learning-based video compression and downstream classification systems. Our attack framework, dubbed RoVISQ, manipulates the Rate-Distortion (R-D) relationship of a video compression model to achieve one or both of the following goals: (1) increasing the network bandwidth, (2) degrading the video quality for end-users. We further devise new objectives for targeted and untargeted attacks to a downstream video classification service. Finally, we design an input-invariant perturbation that universally disrupts video compression and classification systems in real time. Unlike previously proposed attacks on video classification, our adversarial perturbations are the first to withstand compression. We empirically show the resilience of RoVISQ attacks against various defenses, i.e., adversarial training, video denoising, and JPEG compression. Our extensive experimental results on various video datasets show RoVISQ attacks deteriorate peak signal-to-noise ratio by up to 5.6dB and the bit-rate by up to ~ 2.4 times while achieving over 90% attack success rate on a downstream classifier. Our user study further demonstrates the effect of RoVISQ attacks on users’ QoE. We provide several example attacked videos used in our survey onhttps://sites.google.com/view/demo-of-rovisq/home.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233863", "vector": [], "sparse_vector": [], "title": "The Power of Bamboo: On the Post-Compromise Security for Searchable Symmetric Encryption.", "authors": ["<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic searchable symmetric encryption (DSSE) enables users to delegate the keyword search over dynamically updated encrypted databases to an honest-but-curious server without losing keyword privacy. This paper studies a new and practical security risk to DSSE, namely, secret key compromise (e.g., a user's secret key is leaked or stolen), which threatens all the security guarantees offered by existing DSSE schemes. To address this open problem, we introduce the notion of searchable encryption with key-update (SEKU) that provides users with the option of non-interactive key updates. We further define the notion of post-compromise secure with respect to leakage functions to study whether DSSE schemes can still provide data security after the client's secret key is compromised. We demonstrate that post-compromise security is achievable with a proposed protocol called ``Bamboo\". Interestingly, the leakage functions of Bamboo satisfy the requirements for both forward and backward security. We conduct a performance evaluation of Bamboo using a real-world dataset and compare its runtime efficiency with the existing forward-and-backward secure DSSE schemes. The result shows that Bamboo provides strong security with better or comparable performance.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233864", "vector": [], "sparse_vector": [], "title": "SynthDB: Synthesizing Database via Program Analysis for Security Testing of Web Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Testing database-backed web applications is challenging because their behaviors (e.g., control flow) are highly dependent on data returned from SQL queries. Without a database containing sufficient and realistic data, it is challenging to reach potentially vulnerable code snippets, limiting various existing dynamic-based security testing approaches. However, obtaining such a database for testing is difficult in practice as it often contains sensitive information. Sharing it can lead to data leaks and privacy issues. In this paper, we present SYNTHDB, a program analysis-based database generation technique for database-backed PHP applications. SYNTHDB leverages a concolic execution engine to identify interactions between PHP codebase and the SQL queries. It then collects and solves various constraints to reconstruct a database that can enable exploring uncovered program paths without violating database integrity. Our evaluation results show that the database generated by SYNTHDB outperforms state-of-the-arts database generation techniques  in terms of code and query coverage in 17 real-world PHP applications. Specifically, SYNTHDB generated databases achieve 62.9% code and 77.1% query coverages, which are 14.0% and 24.2% more in code and query coverages than the state-of-the-art techniques. Furthermore, our security analysis results show that SYNTHDB effectively aids existing security testing tools: Burp Suite, Wfuzz, and webFuzz. Burp Suite aided by SYNTHDB detects 76.8% of vulnerabilities while other existing techniques cover 55.7% or fewer. Impressively, with SYNTHDB, Burp Suite discovers 33 previously unknown vulnerabilities from 5 real-world applications.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233865", "vector": [], "sparse_vector": [], "title": "MetaWave: Attacking mmWave Sensing with Meta-material-enhanced Tags.", "authors": ["Xingyu Chen", "<PERSON><PERSON><PERSON>", "Baicheng Chen", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Millimeter-wave (mmWave) sensing has been applied in many critical applications, serving millions of thousands of people around the world. However, it is vulnerable to attacks in the real world. These attacks are based on expensive and professional radio frequency (RF) modulator-based instruments and can be prevented by conventional practice (e.g., RF fingerprint). In this paper, we propose and design a novel passive mmWave attack, called MetaWave, with low-cost and easily obtainable meta-material tags for both vanish and ghost attack types. These meta-material tags are made of commercial off-the-shelf (COTS) materials with customized tag designs to attack various goals, which considerably low the attack bar on mmWave sensing. Specifically, we demonstrate that tags made of ordinal material (e.g., C-RAM LF) can be leveraged to precisely tamper the mmWave echo signal and spoof the range, angle, and speed sensing measurements. Besides, to optimize the attack, a general simulator-based MetaWave attack framework is proposed and designed to simulate the tag modulation effects on the mmWave signal with advanced tag and scene parameters. We evaluate, MetaWave, the meta-material tag attack in both simulation and real-world experiments (i.e., 20 different environments) with various attack settings. Experimental results demonstrate that MetaWave can achieve up to 97% Top-1 attack accuracy on range estimation, 96% on angle estimation, and 91% on speed estimation in actual practice, 10-100X cheaper than existing mmWave attack methods. We also evaluate the usability and robustness of MetaWave under different real-world scenarios. Moreover, we conduct in-depth analysis and discussion on countermeasures for MetaWave mmWave attacks to improve wireless sensing and cyber-infrastructure security.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233866", "vector": [], "sparse_vector": [], "title": "OBSan: An Out-Of-Bound Sanitizer to Harden DNN Executables.", "authors": ["<PERSON><PERSON><PERSON>", "Yuanyuan Yuan", "<PERSON><PERSON>"], "summary": "The rapid adoption of deep neural network (DNN) models on a variety of hardware platforms has boosted the development of deep learning (DL) compilers. DL compilers take as input the high-level DNN model specifications and generate optimized DNN executables for diverse hardware architectures like CPUs and GPUs. Despite the emerging adoption of DL compilers in real-world scenarios, no solutions exist to protect DNN executables. To fill this critical gap, this paper introduces OBSAN, a fast sanitizer designed to check out-of-bound (OOB) behavior of DNN executables. From a holistic view, DNN incorporates bidirectional computation: forward propagation that predicts an output based on an input, and backward propagation that characterizes how the forward prediction is made. Both neuron activations in forward propagation and the gradients in backward propagation should fall within valid ranges, and deviations from the valid ranges would be considered as OOB. OOB is primarily related to unsafe behavior of DNNs, which root from anomalous inputs and may cause mispredictions or even exploitation via adversarial examples (AEs). We thus design OBSAN, which includes two variants, FOBSAN and BOBSAN, that can detect OOB in the forward and backward propagations, respectively. Each OBSAN is designed as extra passes of DL compilers to integrate with large-scale DNN models, and we design various optimization schemes to reduce the overhead of OBSAN. Evaluations over various anomalous inputs show that OBSAN manifests promising OOB detectability with low overhead. We further present two downstream applications to show how OBSAN prevents online AE generation and facilitates feedback-driven fuzz testing toward DNN executables.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233867", "vector": [], "sparse_vector": [], "title": "Securing Federated Sensitive Topic Classification against Poisoning Attacks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a Federated Learning (FL) based solution for building a distributed classifier capable of detecting URLs containing sensitive content, i.e., content related to categories such as health, political beliefs, sexual orientation, etc. Although such a classifier addresses the limitations of previous offline/centralised classifiers, it is still vulnerable to poisoning attacks from malicious users that may attempt to reduce the accuracy for benign users by disseminating faulty model updates. To guard against this, we develop a robust aggregation scheme based on subjective logic and residual-based attack detection. Employing a combination of theoretical analysis, trace-driven simulation, as well as experimental validation with a prototype and real users, we show that our classifier can detect sensitive content with high accuracy, learn new labels fast, and remain robust in view of poisoning attacks from malicious users, as well as imperfect input from non-malicious ones.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233868", "vector": [], "sparse_vector": [], "title": "ProbFlow : Using Probabilistic Programming in Anonymous Communication Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present ProbFlow, a probabilistic programming approach for estimating relay capacities in the Tor network. We refine previously derived probabilistic model of the network to take into account more of the complexity of the real-world Tor network. We use this model to perform inference in a probabilistic programming language called NumPyro which allows us to overcome the analytical barrier present in purely analytical approach. We integrate the implementation of ProbFlow to the current implementation of capacity estimation algorithms in the Tor network. We demonstrate the practical benefits of ProbFlow by simulating it in flow-based Python simulator and packet-based Shadow simulations, the highest fidelity simulator available for the Tor network. In both simulators, ProbFlow provides significantly more accurate estimates that results in improved user performance, with average download speeds increasing by 25% in the Shadow simulations.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233869", "vector": [], "sparse_vector": [], "title": "RR: A Fault Model for Efficient TEE Replication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Trusted Execution Environments (TEEs) ensure the confidentiality and integrity of computations in hardware. Subject to the TEE's threat model, the hardware shields a computation from most externally induced fault behavior except crashes. As a result, a crash-fault tolerant (CFT) replication protocol should be sufficient when replicating trusted code inside TEEs.  However, TEEs do not provide efficient and general means of ensuring the freshness of external, persistent state. Therefore, CFT replication is insufficient for TEE computations with external state, as this state could be rolled back to an earlier version when a TEE restarts.  Furthermore, using BFT protocols in this setting is too conservative, because these protocols are designed to tolerate arbitrary behavior, not just rollback during a restart. In this paper, we propose the restart-rollback (RR) fault model for replicating TEEs, which precisely captures the possible fault behaviors of TEEs with external state. Then, we show that existing replication protocols can be easily adapted to this fault model with few changes, while retaining their original performance. We adapted two widely used crash fault tolerant protocols - the ABD read/write register protocol and the Paxos consensus protocol - to the RR model.  Furthermore, we leverage these protocols to build a replicated metadata service called emph{TEEMS}, and then show that it can be used to add TEE-grade confidentiality, integrity, and freshness to untrusted cloud storage services.  Our evaluation shows that our protocols perform significantly better than their BFT counterparts (between $1.25$ and $55times$ better throughput), while performing identically to the CFT versions, which do not protect against rollback attacks.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233870", "vector": [], "sparse_vector": [], "title": "Copy-on-Flip: Hardening ECC Memory Against Rowhammer Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite nearly decade-long mitigation efforts in academia and industry, the community is yet to find a practical solution to the Rowhammer vulnerability. Comprehensive software mitigations require complex changes to commodity systems, yielding significant run-time overhead and deterring practical adoption. Hardware mitigations, on the other hand, have generally grown more robust and efficient, but are difficult to deploy on commodity systems. Until recently, ECC memory implemented by the memory controller on server platforms seemed to provide the best of both worlds: use hardware features already on commodity systems to efficiently turn Rowhammer into a denial-of-service attack vector. Unfortunately, researchers have recently shown that attackers can perform one-bit-at-a-time memory templating and mount ECC-aware Rowhammer attacks. In this paper, we reconsider ECC memory as an avenue for Rowhammer mitigations and show that not all hope is lost. In particular, we show that it is feasible to devise a software-based design to both efficiently and effectively harden commodity ECC memory against ECC-aware Rowhammer attacks. To support this claim, we present Copy-on-Flip (CoF), an ECC-based software mitigation which uses a combination of memory _migration_ and _offlining_ to stop Rowhammer attacks on commodity server systems in a practical way. The key idea is to let the operating system interpose on all the error correction events and offline the vulnerable victim page as soon as the attacker has successfully templated a sufficient number of bit flips---while transparently migrating the victim data to a new page. We present a CoF prototype on Linux, where we also show it is feasible to operate simple memory management changes to support migration for the relevant user and kernel memory pages. Our evaluation shows CoF incurs low performance and memory overhead, while significantly reducing the Rowhammer attack surface. On typical benchmarks such as SPEC CPU2017 and Google Chrome, CoF reports a $<1.5%$ overhead, and, on extreme I/O-intensive scenarios (saturated nginx), up to $sim11%$.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233871", "vector": [], "sparse_vector": [], "title": "Fusion: Efficient and Secure Inference Resilient to Malicious Servers.", "authors": ["Caiqin Dong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In secure machine learning inference, most of the schemes assume that the server is semi-honest (honestly following the protocol but attempting to infer additional information). However, the server may be malicious (e.g., using a low-quality model or deviating from the protocol) in the real world. Although a few studies have considered a malicious server that deviates from the protocol, they ignore the verification of model accuracy (where the malicious server uses a low-quality model) meanwhile preserving the privacy of both the server's model and the client's inputs. To address these issues, we propose textit{Fusion}, where the client mixes the public samples (which have known query results) with their own samples to be queried as the inputs of multi-party computation to jointly perform the secure inference. Since a server that uses a low-quality model or deviates from the protocol can only produce results that can be easily identified by the client, textit{Fusion} forces the server to behave honestly, thereby addressing all those aforementioned issues without leveraging expensive cryptographic techniques. Our evaluation indicates that textit{Fusion} is 48.06$times$ faster and uses 30.90$times$ less communication than the existing maliciously secure inference protocol (which currently does not support the verification of the model accuracy). In addition, to show the scalability, we conduct ImageNet-scale inference on the practical ResNet50 model and it costs 8.678 minutes and 10.117 GiB of communication in a WAN setting, which is 1.18$times$ faster and has 2.64$times$ less communication than those of the semi-honest protocol.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233872", "vector": [], "sparse_vector": [], "title": "RAI2: Responsible Identity Audit Governing the Artificial Intelligence.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Identity plays an important role in responsible artificial intelligence (AI): it acts as a unique marker for deep learning (DL) models and can be used to trace those accountable for irresponsible use of models. Consequently, effective DL identity audit is fundamental for building responsible AI. Besides models, training datasets determine what features a model can learn, and thus should be paid equal attention in identity audit. In this work, we propose the first practical scheme, named RAI2, for responsible identity audit for both datasets and models. We develop our dataset and model similarity estimation methods that can work with black-box access to suspect models. The proposed methods can quantitatively determine the identity of datasets and models by estimating the similarity between the owner's and suspect's. Finally, we realize our responsible audit scheme based on the commitment scheme, enabling the owner to register datasets and models to a trusted third party (TTP) which is in charge of dataset and model regulation and forensics of copyright infringement. Extensive evaluation on 14 model architectures and 6 visual and textual datasets shows that our scheme can accurately identify the dataset and model with the proposed similarity estimation methods. We hope that our audit methodology will not only fill the gap in achieving identity arbitration but also ride on the wave of AI governance in this chaotic world.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233873", "vector": [], "sparse_vector": [], "title": "ReScan: A Middleware Framework for Realistic and Robust Black-box Web Application Scanning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Black-box web vulnerability scanners are invaluable for security researchers and practitioners. Despite recent approaches tackling emph{some} of the inherent limitations of scanners, many have not sufficiently evolved alongside web browsers and applications, and often lack the capabilities for handling the inherent challenges of navigating and interacting with modern web applications. Instead of building an alternative scanner that could naturally only incorporate a limited set of the wide range of vulnerability-finding capabilities offered by the multitude of existing scanners, in this paper we propose an entirely different strategy. We present ReScan, a emph{scanner-agnostic} middleware framework that emph{transparently} enhances scanners' capabilities by mediating their interaction with web applications in a realistic and robust manner, using an orchestrated, fully-fledged modern browser. In essence, our framework can be used in conjunction with emph{any} vulnerability scanner, thus allowing users to benefit from the capabilities of existing and future scanners.  Our extensible and modular framework includes a collection of enhancement techniques that address limitations and obstacles commonly faced by state-of-the-art scanners. Our experimental evaluation demonstrates that despite the considerable (and expected) overhead introduced by a fully-fledged browser, our framework significantly improves the code coverage achieved by popular scanners (168% on average), resulting in a 66% and 161% increase in the number of reflected and stored XSS vulnerabilities detected, respectively.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233874", "vector": [], "sparse_vector": [], "title": "Let Me Unwind That For You: Exceptions to Backward-Edge Protection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Backward-edge control-flow hijacking via stack buffer overflow is the holy grail of software exploitation. The ability to directly control critical stack data and the hijacked target makes this exploitation strategy particularly appealing for attackers. As a result, the community has deployed strong backward-edge protections such as shadow stacks or stack canaries, forcing attackers to resort to less ideal e.g., heap-based exploitation strategies. However, such mitigations commonly rely on one key assumption, namely an attacker relying on return address corruption to directly hijack control flow upon function return. In this paper, we present *exceptions* to this assumption and show attacks based on backward-edge control-flow hijacking *without* the direct hijacking are possible. Specifically, we demonstrate that stack corruption can cause exception handling to act as a *confused deputy* and mount backward-edge control-flow hijacking attacks on the attacker’s behalf. This strategy provides overlooked opportunities to divert execution to attacker-controlled catch handlers (a paradigm we term Catch Handler Oriented Programming or CHOP) and craft powerful primitives such as arbitrary code execution or arbitrary memory writes. We find CHOP-style attacks to work across multiple platforms (Linux, Windows, macOS, Android and iOS). To analyze the uncovered attack surface, we survey popular open-source packages and study the applicability of the proposed exploitation techniques. Our analysis shows that suitable exception handling targets are ubiquitous in C++ programs and exploitable exception handlers are common. We conclude by presenting three end-to-end exploits on real-world software and proposing changes to deployed mitigations to address CHOP.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233875", "vector": [], "sparse_vector": [], "title": "Accountable Javascript Code Delivery.", "authors": ["Ilkan Esiyok", "<PERSON>", "<PERSON><PERSON>-<PERSON>", "<PERSON>"], "summary": "The Internet is a major distribution platform for web applications, but there are no effective transparency and audit mechanisms in place for the web. Due to the ephemeral nature of web applications, a client visiting a website has no guarantee that the code it receives today is the same as yesterday, or the same as other visitors receive. Despite advances in web security, it is thus challenging to audit web applications before they are rendered in the browser. We propose Accountable JS, a browser extension and opt-in protocol for accountable delivery of active content on a web page. We prototype our protocol, formally model its security properties with the Tamarin Prover, and evaluate its compatibility and performance impact with case studies including WhatsApp Web, AdSense and Nimiq. Accountability is beginning to be deployed at scale, with Meta’s recent announcement of Code Verify available to all 2 billion WhatsApp users, but there has been little formal analysis of such protocols. We formally model Code Verify using the Tamarin Prover and compare its properties to our Accountable JS protocol. We also compare Code Verify’s and Accountable JS extension's performance impacts on WhatsApp Web.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233876", "vector": [], "sparse_vector": [], "title": "HeteroScore: Evaluating and Mitigating Cloud Security Threats Brought by Heterogeneity.", "authors": ["Chongzhou Fang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Cloud computing has emerged as a critical part of commercial computing infrastructure due to its computing power, data storage capabilities, scalability, software/API integration, and convenient billing features. At the early stage of cloud computing, the majority of clouds are homogeneous, i.e., most machines are identical. It has been proven that heterogeneity in the cloud, where a variety of machine configurations exist, provides higher performance and power efficiency for applications. This is because heterogeneity enables applications to run in more suitable hardware/software environments. In recent years, the adoption of heterogeneous cloud has increased with the integration of a variety of hardware into cloud systems to serve the requirements of increasingly diversified user applications. At the same time, the emergence of security threats, such as micro-architectural attacks, is becoming a more critical problem for cloud users and providers. It has been demonstrated (e.g., Repttack and Cloak & Co-locate) that the prerequisite of micro-architectural attacks, the co-location of attack and victim instances, is easier to achieve in the heterogeneous cloud. This also means that the ease of attack is not just related to the heterogeneity of the cloud but increases with the degree of heterogeneity. However, there is a lack of numerical metrics to define, quantify or compare the heterogeneity of one cloud environment with another. In this paper, we propose a novel metric called Heterogeneity Score (HeteroScore), which quantitatively evaluates the heterogeneity of a cluster. We demonstrate that HeteroScore is closely connected to security against co-location attacks. Furthermore, we propose mitigation techniques to trade-off heterogeneity offered with security. We believe this is the first quantitative study that evaluates cloud heterogeneity and links heterogeneity to infrastructure security.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233877", "vector": [], "sparse_vector": [], "title": "AuthentiSense: A Scalable Behavioral Biometrics Authentication Scheme using Few-Shot Learning for Mobile Platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Mobile applications are widely used for online services sharing a large amount of personal data online. One-time authentication techniques such as passwords and physiological biometrics (e.g., fingerprint, face, and iris) have their own advantages but also disadvantages since they can be stolen or emulated, and do not prevent access to the underlying device, once it is unlocked. To address these challenges, complementary authentication systems based on behavioural biometrics have emerged. The goal is to continuously profile users based on their interaction with the mobile device. However, existing behavioural authentication schemes are not (i) user-agnostic meaning that they cannot dynamically handle changes in the user-base without model re-training, or (ii) do not scale well to authenticate millions of users. In this paper, we present AuthentiSense, a user-agnostic, scalable, and efficient behavioural biometrics authentication system that enables continuous authentication and utilizes only motion patterns (i.e., accelerometer, gyroscope, and magnetometer data) while users interact with mobile apps. Our approach requires neither manually engineered features nor a significant amount of data for model training. We leverage a few-shot learning technique, called Siamese network, to authenticate users at a large scale. We perform a systematic measurement study and report the impact of the parameters such as interaction time needed for authentication and n-shot verification (comparison with enrollment samples) at the recognition stage. Remarkably, AuthentiSense achieves high accuracy of up to 97% in terms of F1-score even when evaluated in a few-shot fashion that requires only a few behaviour samples per user (3 shots). Our approach accurately authenticates users only after 1 second of user interaction. For AuthentiSense, we report a FAR and FRR of 0.023 and 0.057, respectively.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233878", "vector": [], "sparse_vector": [], "title": "Automata-Based Automated Detection of State Machine Bugs in Protocol Implementations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Implementations of stateful security protocols must carefully manage the type and order of exchanged messages and cryptographic material, by maintaining a state machine which keeps track of protocol progress. Corresponding implementation flaws, called emph{state machine bugs}, can constitute serious security vulnerabilities. We present an automated black-box technique for detecting state machine bugs in implementations of stateful network protocols. It takes as input a catalogue of state machine bugs for the protocol, each specified as a finite automaton which accepts sequences of messages that exhibit the bug, and a (possibly inaccurate) model of the implementation under test, typically obtained by model learning. Our technique constructs the set of sequences that (according to the model) can be performed by the implementation and that (according to the automaton) expose the bug. These sequences are then transformed to test cases on the actual implementation to find a witness for the bug or filter out false alarms. We have applied our technique on three widely-used implementations of SSH servers and nine different DTLS server and client implementations, including their most recent versions. Our technique easily reproduced all bugs identified by security researchers before, and produced witnesses for them. More importantly, it revealed several previously unknown bugs in the same implementations, two new vulnerabilities, and a variety of new bugs and non-conformance issues in newer versions of the same SSH and DTLS implementations.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233879", "vector": [], "sparse_vector": [], "title": "REDsec: Running Encrypted Discretized Neural Networks in Seconds.", "authors": ["<PERSON>", "<PERSON>", "Nektar<PERSON>"], "summary": "Machine learning as a service (MLaaS) has risen to become a prominent technology due to the large development time, amount of data, hardware costs, and level of expertise required to develop a machine learning model. However, privacy concerns prevent the adoption of MLaaS for applications with sensitive data. A promising privacy preserving solution is to use fully homomorphic encryption (FHE) to perform the ML computations. Recent advancements have lowered computational costs by several orders of magnitude, opening doors for secure practical applications to be developed. In this work, we introduce the REDsec framework that optimizes FHE-based private machine learning inference by leveraging ternary neural networks. Such neural networks, whose weights are constrained to {-1,0,1}, have special properties that we exploit to operate efficiently in the homomorphic domain. REDsec introduces novel features, including a new data re-use scheme that enables bidirectional bridging between the integer and binary domains for the first time in FHE. This enables us to implement very efficient binary operations for multiplication and activations, as well as efficient integer domain additions. Our approach is complemented by a new GPU acceleration library, dubbed (RED)cuFHE, which supports both binary and integer operations on multiple GPUs. REDsec brings unique benefits by supporting user-defined models as input (bring-your-own-network), automation of plaintext training, and efficient evaluation of private inference leveraging TFHE. In our analysis, we perform inference experiments with the MNIST, CIFAR-10, and ImageNet datasets and report performance improvements compared to related works.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233880", "vector": [], "sparse_vector": [], "title": "POSE: Practical Off-chain Smart Contract Execution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Smart contracts enable users to execute payments depending on complex program logic. Ethereum is the most notable example of a blockchain that supports smart contracts leveraged for countless applications including games, auctions and financial products. Unfortunately, the traditional method of running contract code on-chain is very expensive, for instance, on the Ethereum platform, fees have dramatically increased, rendering the system unsuitable for complex applications. A prominent solution to address this problem is to execute code off-chain and only use the blockchain as a trust anchor. While there has been significant progress in developing off-chain systems over the last years, current off-chain solutions suffer from various drawbacks including costly blockchain interactions, lack of data privacy, huge capital costs from locked collateral, or supporting only a restricted set of applications. In this paper, we present POSE—a practical off-chain protocol for smart contracts that addresses the aforementioned shortcomings of existing solutions. POSE leverages a pool of Trusted Execution Environments (TEEs) to execute the computation efficiently and to swiftly recover from accidental or malicious failures. We show that POSE provides strong security guarantees even if a large subset of parties is corrupted. We evaluate our proof-of-concept implementation with respect to its efficiency and effectiveness.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233881", "vector": [], "sparse_vector": [], "title": "Detecting Unknown Encrypted Malicious Traffic in Real Time via Flow Interaction Graph Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Nowadays traffic on the Internet has been widely encrypted to protect its confidentiality and privacy. However, traffic encryption is always abused by attackers to conceal their malicious behaviors. Since the encrypted malicious traffic has similar features to benign flows, it can easily evade traditional detection methods. Particularly, the existing encrypted malicious traffic detection methods are supervised and they rely on the prior knowledge of known attacks (e.g., labeled datasets). Detecting unknown encrypted malicious traffic in real time, which does not require prior domain knowledge, is still an open problem. In this paper, we propose HyperVision, a realtime unsupervised machine learning (ML) based malicious traffic detection system. Particularly, HyperVision is able to detect unknown patterns of encrypted malicious traffic by utilizing a compact inmemory graph built upon the traffic patterns. The graph captures flow interaction patterns represented by the graph structural features, instead of the features of specific known attacks. We develop an unsupervised graph learning method to detect abnormal interaction patterns by analyzing the connectivity, sparsity, and statistical features of the graph, which allows HyperVision to detect various encrypted attack traffic without requiring any labeled datasets of known attacks. Moreover, we establish an information theory model to demonstrate that the information preserved by the graph approaches the ideal theoretical bound. We show the performance of HyperVision by real-world experiments with 92 datasets including 48 attacks with encrypted malicious traffic. The experimental results illustrate that HyperVision achieves at least 0.92 AUC and 0.86 F1, which significantly outperform the state-of-the-art methods. In particular, more than 50% attacks in our experiments can evade all these methods. Moreover, HyperVision achieves at least 80.6 Gb/s detection throughput with the average detection latency of 0.83s.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233882", "vector": [], "sparse_vector": [], "title": "Focusing on Pinocchio&apos;s Nose: A Gradients Scrutinizer to Thwart Split-Learning Hijacking Attacks Using Intrinsic Attributes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Split learning is privacy-preserving distributed learning that has gained momentum recently. It also faces new security challenges. FSHA is a serious threat to split learning. In FSHA, a malicious server hijacks training to trick clients to train the encoder of an autoencoder instead of a classification model. Intermediate results sent to the server by a client are actually latent codes of private training samples, which can be reconstructed with high fidelity from the received codes with the decoder of the autoencoder. SplitGuard is the only existing effective defense against hijacking attacks. It is an active method that injects falsely labeled data to incur abnormal behaviors to detect hijacking attacks. Such injection also incurs an adverse impact on honest training of intended models. In this paper, we first show that SplitGuard is vulnerable to an adaptive hijacking attack named SplitSpy. SplitSpy exploits the same property that SplitGuard exploits to detect hijacking attacks. In SplitSpy, a malicious server maintains a shadow model that performs the intended task to detect falsely labeled data and evade SplitGuard. Our experimental evaluation indicates that SplitSpy can effectively evade SplitGuard. Then we propose a novel passive detection method, named Gradients Scrutinizer, which relies on intrinsic differences between gradients from an intended model and those from a malicious model: the expected similarity among gradients of same-label samples differs from the expected similarity among gradients of different-label samples for an intended model, while they are the same for a malicious model. This intrinsic distinguishability enables <PERSON><PERSON><PERSON> Scrutinizer to effectively detect split-learning hijacking attacks without tampering with honest training of intended models. Our extensive evaluation indicates that Gradients Scrutinizer can effectively thwart both known split-learning hijacking attacks and adaptive counterattacks against it.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233883", "vector": [], "sparse_vector": [], "title": "QUICforge: Client-side Request Forgery in QUIC.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The QUIC protocol is gaining more and more traction through its recent standardization and the rising interest by various big tech companies, developing new implementations. QUIC promises to make security and privacy a first-class citizen; yet, challenging these claims is of utmost importance. To this end, this paper provides an initial analysis of client-side request forgery attacks that directly emerge from the QUIC protocol design and not from common vulnerabilities. In particular, we investigate three request forgery attack modalities with respect to their capabilities to be used for protocol impersonation and traffic amplification. We analyze the controllable attack space of the respective protocol messages and demonstrate that one of the attack modalities can indeed be utilized to impersonate other UDP-based protocols, e.g., DNS requests. Furthermore, we identify traffic amplification vectors. Although the QUIC protocol specification states anti-amplification limits, our evaluation of 13 QUIC server implementations shows that in some cases these mitigations are missing or insufficiently implemented. Lastly, we propose mitigation approaches for protocol impersonation and discuss ambiguities in the specification.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233884", "vector": [], "sparse_vector": [], "title": "Private Certifier Intersection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We initiate the study of Private Certifier Intersection (PCI), which allows mutually distrusting parties to establish a trust basis for cross-validation of claims if they have one or more trust authorities (certifiers) in common. This is one of the essential requirements for verifiable presentations in Web 3.0, since it provides additional privacy without compromising on decentralization. A PCI protocol allows two or more parties holding certificates to identify a common set of certifiers while additionally validating the certificates issued by such certifiers, without leaking any information about the certifiers not in the output intersection. In this paper, we formally define the notion of multi-party PCI in the Simplified-UC framework for two different settings depending on whether certificates are required for any of the claims (called PCI-Any) or all of the claims (called PCI-All). We then design and implement two provably secure and practically efficient PCI protocols supporting validation of digital signature-based certificates: a PCI-Any protocol for ECDSA-based certificates and a PCI-All protocol for BLS-based certificates. The technical centerpiece of our proposals is the first secretsharing-based MPC framework supporting efficient computation of elliptic curve-based arithmetic operations, including elliptic curve pairings, in a black-box way. We implement this framework by building on top of the well-known MP-SPDZ library using OpenSSL and RELIC for elliptic curve operations, and use this implementation to benchmark our proposed PCI protocols in the LAN and WAN settings. In an intercontinental WAN setup with parties located in different continents, our protocols execute in less than a minute on input sets of size 40, which demonstrates the practicality of our proposed solutions.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233885", "vector": [], "sparse_vector": [], "title": "Sometimes, You Aren&apos;t What You Do: Mimicry Attacks against Provenance Graph Host Intrusion Detection Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reliable methods for host-layer intrusion detection remained an open problem within computer security. Recent research has recast intrusion detection as a provenance graph anomaly detection problem thanks to concurrent advancements in machine learning and causal graph auditing. While these approaches show promise, their robustness against an adaptive adversary has yet to be proven. In particular, it is unclear if mimicry attacks, which plagued past approaches to host intrusion detection, have a similar effect on modern graph-based methods. In this work, we reveal that systematic design choices have allowed mimicry attacks to continue to abound in provenance graph host intrusion detection systems (Prov-HIDS). Against a corpus of exemplar Prov-HIDS, we develop evasion tactics that allow attackers to hide within benign process behaviors. Evaluating against public datasets, we demonstrate that an attacker can consistently evade detection (100% success rate) without modifying the underlying attack behaviors. We go on to show that our approach is feasible in live attack scenarios and outperforms domain-general adversarial sample techniques. Through open sourcing our code and datasets, this work will serve as a benchmark for the evaluation of future Prov-HIDS.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233886", "vector": [], "sparse_vector": [], "title": "FUZZILLI: Fuzzing for JavaScript JIT Compiler Vulnerabilities.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "JavaScript has become an essential part of the Internet infrastructure, and today's interactive web applications would be inconceivable without this programming language. On the downside, this interactivity implies that web applications rely on an ever-increasing amount of computationally intensive JavaScript code, which burdens the JavaScript engine responsible for efficiently executing the code. To meet these rising performance demands, modern JavaScript engines ship with sophisticated just-in-time (JIT) compilers. However, JIT compilers are a complex technology and, consequently, provide a broad attack surface for potential faults that might even be security-critical. Previous work on discovering software faults in JavaScript engines found many vulnerabilities, often using fuzz testing. Unfortunately, these fuzzing approaches are not designed to generate source code that actually triggers JIT semantics. Consequently, JIT vulnerabilities are unlikely to be discovered by existing methods. In this paper, we close this gap and present the first fuzzer that focuses on JIT vulnerabilities. More specifically, we present the design and implementation of an intermediate representation (IR) that focuses on discovering JIT compiler vulnerabilities. We implemented a complete prototype of the proposed approach and evaluated our fuzzer over a period of six months. In total, we discovered 17 confirmed security vulnerabilities. Our results show that targeted JIT fuzzing is possible and a dangerously neglected gap in fuzzing coverage for JavaScript engines.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233887", "vector": [], "sparse_vector": [], "title": "MyTEE: Own the Trusted Execution Environment on Embedded Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON> Han", "<PERSON><PERSON><PERSON>"], "summary": "We propose a solution, MyTEE, that enables a trusted execution environment (TEE) to be built even in worst-case environments wherein major hardware security primitives (e.g., ARM TrustZone extensions for memory access control) are absent. Crafting page tables for memory isolation, filtering DMA packets, and enabling secure IO exist at the core of MyTEE. Particularly for secure IO, we shield the IO buffers and memory-mapped registers of the controllers and securely escalate the privilege of the partial code block of the device drivers to provide permission to access the protected objects. By doing so, the need to host the device driver in the TEE (in whole or in part), which can potentially introduce a new attack surface, is exempted. The proof-of-concept (PoC) of MyTEE is implemented on the Raspberry Pi 3 board, which does not support most of the important security primitives for building the TEE. Additionally, three secure IO examples with the hardware TPM, framebuffer, and USB keyboard are demonstrated to show the feasibility of our approach.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233888", "vector": [], "sparse_vector": [], "title": "Anomaly Detection in the Open World: Normality Shift Detection, Explanation, and Adaptation.", "authors": ["Dongqi Han", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xingang Shi", "<PERSON><PERSON>"], "summary": "Concept drift is one of the most frustrating challenges for learning-based security applications built on the close-world assumption of identical distribution between training and deployment. Anomaly detection, one of the most important tasks in security domains, is instead immune to the drift of abnormal behavior due to the training without any abnormal data (known as zero-positive), which however comes at the cost of more severe impacts when normality shifts. However, existing studies mainly focus on concept drift of abnormal behaviour and/or supervised learning, leaving the normality shift for zero-positive anomaly detection largely unexplored. In this work, we are the first to explore the normality shift for deep learning-based anomaly detection in security applications, and propose OWAD, a general framework to detect, explain and adapt to normality shift in practice. In particular, OWAD outperforms prior work by detecting shift in an unsupervised fashion, reducing the overhead of manual labeling, and providing better adaptation performance through distribution-level tackling. We demonstrate the effectiveness of OWAD through several realistic experiments on three security-related anomaly detection applications with long-term practical data. Results show that OWAD can provide better adaptation performance of normality shift with less labeling overhead. We provide case studies to analyze the normality shift and provide operational recommendations for security applications. We also conduct an initial real-world deployment on a SCADA security system.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233889", "vector": [], "sparse_vector": [], "title": "Partitioning Ethereum without Eclipsing It.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>ung<PERSON> Woo", "<PERSON><PERSON><PERSON>on", "<PERSON>", "Seungwon Shin"], "summary": "We present a practical partitioning attack, which we call Gethlighting, that isolates an Ethereum full node from the rest of the network for hours without having to occupy (or eclipse) all of the target’s peer connections. In Gethlighting, an adversary controls only about a half (e.g., 25 out of total 50) of all peer connections of a target node, achieving powerful partitioning with a small attack budget of operating several inexpensive virtual machines. At the core of Gethlighting, its low-rate denial-of-service (DoS) strategy effectively stops the growth of local blockchain for hours while leaving other Ethereum node operations undisturbed. We analyze how subtle and insignificant delays incurred by a low-rate DoS can lead to a powerful blockchain partitioning attack. The practical impact of Gethlighting is discussed — i.e., the attack is scalable and low-cost (only about $5,714 for targeting all Ethereum full nodes concurrently for 24 hours), and extremely simple to launch. We demonstrate the feasibility of Gethlighting with full nodes in the Ethereum mainnet and testnet in both controlled and real-world experiments. We identify a number of fundamental system characteristics in Ethereum that enable Gethlighting attacks and propose countermeasures that require some protocol and client implementation enhancements. Ethereum Foundation has acknowledged this vulnerability in September 2022 and one of our countermeasures has been accepted as a hotfix for Geth 1.11.0.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233890", "vector": [], "sparse_vector": [], "title": "InfoMasker: Preventing Eavesdropping Using Phoneme-Based Noise.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Zhongjie Ba", "Li Lu", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the wide deployment of microphone-equipped smart devices, more and more users have concerns that their voices would be secretly recorded. Recent studies show that microphones have nonlinearity and can be jammed by inaudible ultrasound, which leads to the emergence of ultrasonic-based anti-eavesdropping research. However, existing solutions are implemented through energetic masking and require high energy to disturb human voice. Since ultrasonic noise can only remain inaudible at limited energy, such noise can merely cover a short distance and can be easily removed by adversaries, which makes these solutions impractical. In this paper, we explore the idea of informational masking, study the transmission and coverage constraints of ultrasonic jamming, and implement a highly effective anti-eavesdropping system, named InfoMasker. Specifically, we design a phoneme-based noise that is robust against denoising methods and can effectively prevent both humans and machines from understanding the jammed signals. We optimize the ultrasonic transmission method to achieve higher transmission energy and lower signal distortion, then implement a prototype of our system. Experimental results show that InfoMasker can effectively reduce the accuracy of all tested speech recognition systems to below 50% even at low energies (SNR=0), which is much better than existing noise designs.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233891", "vector": [], "sparse_vector": [], "title": "Paralyzing Drones via EMI Signal Injection on Sensory Communication Channels.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An inertial measurement unit (IMU) takes the key responsibility for the attitude control of drones. It is comprised of various sensors and transfers sensor data to the drones’ control unit. If it reports incorrect data, the drones cannot maintain their attitude and will crash down to the ground. Thus, several anti-drone studies have focused on causing significant fluctuations in the IMU sensor data by resonating the mechanical structure of the internal sensors using a crafted acoustic wave. However, this approach is limited in terms of efficacy for several reasons. As the structural details of each sensor in an IMU significantly differ by type, model, and manufacturer, the attack needs to be conducted independently for each sensor. Furthermore, it can be easily mitigated by using other supplementary sensors that are not corrupted by the attack or even with cheap plastic shielding. In this paper, we propose a novel anti-drone technique that effectively corrupts ANY IMU sensor data regardless of the sensor’s type, model, and manufacturer. Our key idea is to distort the communication channel between the IMU and control unit of a drone by using an electromagnetic interference (EMI) signal injection. Experimentally, for a given control unit board, regardlessof the sensor used, we discovered a distinct susceptible frequency at which an EMI signal can greatly distort the sensor data. Compared to a general EM pulse (EMP) attack, it requires much less power as it targets the specific susceptible frequency. It can also avoid collateral damage from the EMP attack. For practical evaluation, we demonstrate the feasibility of the attack using real drones; the attack instantly paralyzed the drones. Lastly, we conclude by presenting practical challenges for its mitigation.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233892", "vector": [], "sparse_vector": [], "title": "Extrapolating Formal Analysis to Uncover Attacks in Bluetooth Passkey Entry Pairing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Bluetooth is a leading wireless communication technology used by billions of Internet of Things (IoT) devices today. Its ubiquity demands systematic security scrutiny. A key ingredient in Bluetooth security is secure pairing, which includes Numeric comparison (NC) and Passkey Entry (PE). However, most prior formal efforts have considered only NC, and PE has not yet been formally studied in depth. In this paper, we propose a detailed formal analysis of the PE protocol. In particular, we present a generic formal model, built using <PERSON><PERSON>n, to verify the security of PE by precisely capturing the protocol behaviors and attacker capabilities. Encouragingly, it rediscovers three known attacks (confusion attacks, static passcode attacks, and reflection attacks), and more importantly, also uncovers two new attacks (group guessing attacks and ghost attacks) spanning across diverse attack vectors (e.g., static variable reuse, multi-threading, reflection, human error, and compromise device). Finally, after applying fixes to each vulnerability, our model further proves the confidentiality and authentication properties of the PE protocol using an inductive base model.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233893", "vector": [], "sparse_vector": [], "title": "DARWIN: Survival of the Fittest Fuzzing Mutators.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Fuzzing is an automated software testing technique broadly adopted by the industry. A popular variant is mutation-based fuzzing, which discovers a large number of bugs in practice. While the research community has studied mutation-based fuzzing for years now, the algorithms' interactions within the fuzzer are highly complex and can, together with the randomness in every instance of a fuzzer, lead to unpredictable effects. Most efforts to improve this fragile interaction focused on optimizing seed scheduling. However, real-world results like Google's FuzzBench highlight that these approaches do not consistently show improvements in practice. Another approach to improve the fuzzing process algorithmically is optimizing mutation scheduling. Unfortunately, existing mutation scheduling approaches also failed to convince because of missing real-world improvements or too many user-controlled parameters whose configuration requires expert knowledge about the target program. This leaves the challenging problem of cleverly processing test cases and achieving a measurable improvement unsolved. We present DARWIN, a novel mutation scheduler and the first to show fuzzing improvements in a realistic scenario without the need to introduce additional user-configurable parameters, opening this approach to the broad fuzzing community. DARWIN uses an Evolution Strategy to systematically optimize and adapt the probability distribution of the mutation operators during fuzzing.  We implemented a prototype based on the popular general-purpose fuzzer AFL. DARWIN significantly outperforms the state-of-the-art mutation scheduler and the AFL baseline in our own coverage experiment, in FuzzBench, and by finding 15 out of 21 bugs the fastest in the MAGMA benchmark. Finally, DARWIN found 20 unique bugs (including one novel bug), 66% more than AFL, in widely-used real-world applications.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233894", "vector": [], "sparse_vector": [], "title": "Un-Rocking Drones: Foundations of Acoustic Injection Attacks and Recovery Thereof.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Drones equipped with microelectromechanical system (MEMS) inertial measurement unit (IMU) sensors are exposed to acoustic injection attacks. These attacks resonate sensors, compromising their output and causing drones to crash. Several mitigation strategies have been proposed; however, they are limited in terms of practicality as they cannot make the drone fly to its planned destination in the event of an attack. To remedy this, we aim at recovering the compromised sensor values for the practical mitigation of acoustic injection attacks. To achieve this, we first constructed a realistic testbed and delved into the implications of resonant MEMS sensors on drones. We discovered that sampling jitter, which refers to the inconsistent timing delay in retrieving sensor values, has a significant impact on drone crashes during the attack. Note that while any real-time system needs to satisfy its real-time requirements, it does have sampling jitter owing to manufacturing errors or scheduling/operational overheads. The sampling jitter is negligible in terms of real-time requirements; however, we found that it became critical for drones being attacked. This is because the sampling jitter spreads the resonant sensor signals into the in-band range of the drones’ control logic, thereby neutralizing the drones’ safety mechanisms, such as a low-pass filter. Considering the resonant signals affected by sampling jitter as noise, we developed a novel mitigation strategy that leverages a noise reduction technique, namely a denoising autoencoder. This approach recovers benign sensor signals from compromised ones for the resonant MEMS IMU sensors, without requiring other supplementary sensors. We implemented this prototype, termed UNROCKER , and demonstrated its capability through a series of experiments reflecting real-world scenarios. To facilitate future research, we released our source code and experimental data.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233895", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Database Fingerprinting.", "authors": ["Tianxi Ji", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Pan Li"], "summary": "When sharing   relational databases with other parties, in addition to providing high quality (utility) database to the recipients, a database owner also aims to have (i) privacy guarantees for the data entries and (ii)  liability guarantees (via fingerprinting) in case of unauthorized redistribution. However, (i) and (ii) are orthogonal objectives, because when sharing a database with multiple recipients, privacy via data sanitization requires adding noise once (and sharing the same noisy version with all recipients), whereas liability via unique fingerprint insertion requires adding different noises to each shared copy to distinguish all recipients. Although achieving (i) and (ii) together is possible in a na\"ive way (e.g., either differentially-private  database perturbation or synthesis followed by fingerprinting), this approach  results in   significant degradation in the utility of   shared databases. In this paper, we achieve privacy and liability guarantees  simultaneously by proposing a novel entry-level differentially-private  (DP) fingerprinting mechanism for relational databases without causing large utility  degradation. The proposed mechanism fulfills the privacy and liability requirements by leveraging the randomization nature of  fingerprinting and transforming it into provable privacy guarantees.Specifically, we devise a bit-level random response scheme to achieve differential privacy guarantee for arbitrary data entries when sharing the entire database, and then, based on this, we develop an $epsilon$-entry-level DP fingerprinting mechanism. We theoretically analyze the connections between privacy, fingerprint robustness, and database utility by  deriving closed form expressions. We also propose a sparse vector technique-based solution to control the cumulative privacy loss when fingerprinted copies of a database are shared with multiple recipients. We experimentally show that our mechanism achieves strong  fingerprint robustness (e.g., the fingerprint cannot be compromised even if the malicious database recipient  modifies/distorts more than half of the entries in  its received   fingerprinted copy), and higher database utility compared to various baseline methods (e.g., application-dependent database utility of the shared database achieved  by the proposed mechanism is     higher than that of the considered baselines).", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233896", "vector": [], "sparse_vector": [], "title": "A Security Study about Electron Applications and a Programming Methodology to Tame DOM Functionalities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Electron platform represents a paradigm to develop modern desktop apps using HTML and JavaScript. Microsoft Teams, Visual Studio Code and other flagship products are examples of Electron apps. This new paradigm inherits the security challenges in web programming into the desktop-app realm, thus opens a new way for local-machine exploitation. We conducted a security study about real-world Electron apps, and discovered many vulnerabilities that are now confirmed by the app vendors. The conventional wisdom is to view these bugs as *sanitization errors*. Accordingly, secure programming requires programmers to explicitly enumerate all kinds of unexpected inputs to sanitize. We believe that secure programming should focus on specifying programmers' intentions as opposed to their non-intentions. We introduce a concept called *DOM-tree type*, which expresses the set of DOM trees that an app expects to see during execution, so an exploit will be caught as a type violation. With insights into the HTML standard and the Chromium engine, we build the DOM-tree type mechanism into the Electron platform. The evaluations show that the methodology is practical, and it secures all vulnerable apps that we found in the study.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233897", "vector": [], "sparse_vector": [], "title": "VICEROY: GDPR-/CCPA-compliant Enforcement of Verifiable Accountless Consumer Requests.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Ercan <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent data protection regulations (notably, GDPR and CCPA) grant consumers various rights, including the right to access, modify or delete any personal information collected about them (and retained) by a service provider. To exercise these rights, one must submit a verifiable consumer request proving that the collected data indeed pertains to them. This action is straightforward for consumers with active accounts with a service provider at the time of data collection, since they can use standard (e.g., password-based) means of authentication to validate their requests. However, a major conundrum arises from the need to support consumers without accounts to exercise their rights. To this end, some service providers began requiring such accountless consumers to reveal and prove their identities (e.g., using government-issued documents, utility bills, or credit card numbers) as part of issuing a verifiable consumer request. While understandable as a short-term fix, this approach is cumbersome and expensive for service providers as well as privacy-invasive for consumers. Consequently, there is a strong need to provide better means of authenticating requests from accountless consumers. To achieve this, we propose VICEROY, a privacy-preserving and scalable framework for producing proofs of data ownership, which form a basis for verifiable consumer requests. Building upon existing web techniques and features, VICEROY allows accountless consumers to interact with service providers, and later prove that they are the same person in a privacy-preserving manner, while requiring minimal changes for both parties. We design and implement VICEROY with emphasis on security/privacy, deployability and usability. We also assess its practicality via extensive experiments.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233898", "vector": [], "sparse_vector": [], "title": "Faster Secure Comparisons with Offline Phase for Efficient Private Set Intersection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In a Private section intersection (PSI) protocol, <PERSON> and <PERSON> compute the intersection of their respective sets without disclosing any element not in the intersection. PSI protocols have been extensively studied in the literature and are deployed in industry. With state-of-the-art protocols achieving optimal asymptotic complexity, performance improvements are rare and can only improve complexity constants. In this paper, we present a new private, extremely efficient comparison protocol that leads to a PSI protocol with low constants. A useful property of our comparison protocol is that it can be divided into an online and an offline phase. All expensive cryptographic operations are performed during the offline phase, and the online phase performs only four fast field operations per comparison. This leads to an incredibly fast online phase, and our evaluation shows that it outperforms related work, including KKRT (CCS'16), VOLE-PSI (EuroCrypt'21), and  OKVS (Crypto'21). We also evaluate standard approaches to implement the offline phase using different trust assumptions: cryptographic, hardware, and a third party (\"dealer model\").", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233899", "vector": [], "sparse_vector": [], "title": "A Robust Counting Sketch for Data Plane Intrusion Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Demands are increasing to measure per-flow statistics in the data plane of high-speed switches. However, the resource constraint of the data plane is the biggest challenge. Although existing in-data plane solutions improve memory efficiency by accommodating Zipfian distribution of network traffic, they cannot adapt to various flow size distributions due to their static data structure. In other words, they cannot provide robust flow measurement under complex traffic patterns (e.g. under attacks).  Recent works suggest dynamic data structure management schemes, but the high complexity is the major obstruction for the data plane deployment. In this paper, we present Count-Less sketch that enables robust and accurate network measurement under a wide variety of traffic distributions without dynamic data structure update. Count-Less applies a novel sketch update strategy, called {em minimum update}, which approximates the conservative update strategy of Count-MIN for fitting into in-network switches. Not only theoretical proof on Count-<PERSON>'s estimation but also comprehensive experimental results are presented in terms of estimation accuracy and throughput of Count-Less, compared to Count-Min (baseline), Elastic sketch, and FCM sketch. More specifically, experiment results on security applications including estimation errors under various skewness parameters are provided. Count-Less is much more accurate in all measurement tasks than Count-Min and outperforms FCM sketch and Elastic sketch, state-of-the-art algorithms without the help of any special hardware like TCAM. To prove its feasibility in the data plane of a high-speed switch, Count-Less prototype on an ASIC-based programmable switch (Tofino) is implemented in P4 language and evaluated. In terms of data plane latency, Count-Less is 1.53x faster than FCM, while consuming 1.56x less resources such as hash bits, SRAM, and ALU of a programmable switch.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233900", "vector": [], "sparse_vector": [], "title": "Adversarial Robustness for Tabular Data through Cost and Utility Awareness.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Carmela Tron<PERSON>o"], "summary": "Many safety-critical applications of machine learning, such as fraud or abuse detection, use data in tabular domains. Adversarial examples can be particularly damaging for these applications. Yet, existing works on adversarial robustness primarily focus on machine-learning models in image and text domains. We argue that, due to the differences between tabular data and images or text, existing threat models are not suitable for tabular domains. These models do not capture that the costs of an attack could be more significant than imperceptibility, or that the adversary could assign different values to the utility obtained from deploying different adversarial examples. We demonstrate that, due to these differences, the attack and defense methods used for images and text cannot be directly applied to tabular settings. We address these issues by proposing new cost and utility-aware threat models that are tailored to the adversarial capabilities and constraints of attackers targeting tabular domains. We introduce a framework that enables us to design attack and defense mechanisms that result in models protected against cost or utility-aware adversaries, for example, adversaries constrained by a certain financial budget. We show that our approach is effective on three datasets corresponding to applications for which adversarial examples can have economic and social implications.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233901", "vector": [], "sparse_vector": [], "title": "Trellis: <PERSON><PERSON> and <PERSON><PERSON><PERSON> Metadata-private Anonymous Broadcast.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Trellis is a mix-net based anonymous broadcast system with cryptographic security guarantees. <PERSON><PERSON><PERSON> can be used to anonymously publish documents or communicate with other users, all while assuming full network surveillance. In Trellis, users send messages through a set of servers in successive rounds. The servers mix and post the messages to a public bulletin board, hiding which users sent which messages. <PERSON><PERSON><PERSON> hides all network-level metadata, remains robust to changing network conditions, guarantees availability to honest users, and scales with the number of mix servers. Trellis provides three to five orders of magnitude faster performance and better network robustness compared to Atom, the state-of-the-art anonymous broadcast system with a similar threat model. In achieving these guarantees, <PERSON><PERSON><PERSON> contributes: (1) a simpler theoretical mixing analysis for a routing mix network constructed with a fraction of malicious servers, (2) anonymous routing tokens for verifiable random paths, and (3) lightweight blame protocols built on top of onion routing to identify and eliminate malicious parties. We implement and evaluate <PERSON><PERSON><PERSON> in a networked deployment. With 64 servers located across four geographic regions, <PERSON><PERSON><PERSON> achieves a throughput of 220 bits per second with 100,000 users. With 128 servers, <PERSON><PERSON><PERSON> achieves a throughput of 320 bits per second. <PERSON>rellis’s throughput is only 100 to 1000× slower compared to Tor (which has 6,000 servers and 2M daily users) and is therefore potentially deployable at a smaller “enterprise” scale. Our implementation is open-source.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233902", "vector": [], "sparse_vector": [], "title": "Efficient Dynamic Proof of Retrievability for Cold Storage.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Storage-as-a-service (STaaS) permits the client to outsource her data to the cloud thereby, reducing data management and maintenance costs. However, STaaS also brings significant data integrity and soundness concerns since the storage provider might not keep the client data intact and retrievable all the time (e.g., cost saving via deletions). Proof of Retrievability (PoR) can validate the integrity and retrievability of remote data effectively. This technique can be useful for regular audits to monitor data compromises, as well as to comply with standard data regulations. In particular, cold storage applications (e.g., MS Azure, Amazon Glacier) require regular and frequent audits but with less frequent data modification. Yet, despite their merits, existing PoR techniques generally focus on other metrics (e.g., low storage, fast update, metadata privacy) but not audit efficiency (e.g., low audit time, small proof size). Hence, there is a need to develop new PoR techniques that achieve efficient data audit while preserving update and retrieval performance. In this paper, we propose Porla, a new PoR framework that permits efficient data audit, update, and retrieval functionalities simultaneously. Porla permits data audit in both private and public settings, each of which features asymptotically (and concretely) smaller audit-proof size and lower audit time than all the prior works while retaining the same asymptotic data update overhead. Porla achieves all these properties by composing erasure codes with verifiable computation techniques which, to our knowledge, is a new approach to PoR design. We address several challenges that arise in such a composition by creating a new homomorphic authenticated commitment scheme, which can be of independent interest. We fully implemented Porla and evaluated its performance on commodity cloud (i.e., Amazon EC2) under various settings. Experimental results demonstrated that Porla achieves two to four orders of magnitude smaller audit proof size with 4× – 1,800× lower audit time than all prior schemes in both private and public audit settings at the cost of only 2× – 3× slower update.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233903", "vector": [], "sparse_vector": [], "title": "Assessing the Impact of Interface Vulnerabilities in Compartmentalized Software.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Least-privilege separation decomposes applications into compartments limited to accessing only what they need. When compartmentalizing existing software, many approaches neglect securing the new inter-compartment interfaces, although what used to be a function call from/to a trusted component is now potentially a targeted attack from a malicious compartment. This results in an entire class of security bugs: Compartment Interface Vulnerabilities (CIVs). This paper provides an in-depth study of CIVs. We taxonomize these issues and show that they affect all known compartmentalization approaches. We propose ConfFuzz, an in-memory fuzzer specialized to detect CIVs at possible compartment boundaries. We apply ConfFuzz to a set of 25 popular applications and 36 possible compartment APIs, to uncover a wide data-set of 629 vulnerabilities. We systematically study these issues, and extract numerous insights on the prevalence of CIVs, their causes, impact, and the complexity to address them. We stress the critical importance of CIVs in compartmentalization approaches, demonstrating an attack to extract isolated keys in OpenSSL and uncovering a decade-old vulnerability in sudo. We show, among others, that not all interfaces are affected in the same way, that API size is uncorrelated with CIV prevalence, and that addressing interface vulnerabilities goes beyond writing simple checks. We conclude the paper with guidelines for CIV-aware compartment interface design, and appeal for more research towards systematic CIV detection and mitigation.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233904", "vector": [], "sparse_vector": [], "title": "Do Not Give a Dog Bread Every Time He Wags His Tail: Stealing Passwords through Content Queries (CONQUER) Attacks.", "authors": ["Chongqing Lei", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Junzhou Luo", "<PERSON><PERSON><PERSON>"], "summary": "Android accessibility service was designed to assist individuals with disabilities in using Android devices. However, it has been exploited by attackers to steal user passwords due to design shortcomings. Google has implemented various countermeasures to make it difficult for these types of attacks to be successful on modern Android devices. In this paper, we present a new type of side channel attack called content queries (CONQUER) that can bypass these defenses. We discovered that Android does not prevent the content of passwords from being queried by the accessibility service, allowing malware with this service enabled to enumerate the combinations of content to brute force the password. While this attack seems simple to execute, there are several challenges that must be addressed in order to successfully launch it against real-world apps. These include the use of lazy query to differentiate targeted password strings, active query to determine the right timing for the attack, and timing- and state-based side channels to infer case-sensitive passwords. Our evaluation results demonstrate that the CONQUER attack is effective at stealing passwords, with an average one-time success rate of 64.91%. This attack also poses a threat to all Android versions from 4.1 to 12, and can be used against tens of thousands of apps. In addition, we analyzed the root cause of the CONQUER attack and discussed several countermeasures to mitigate the potential security risks it poses.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233905", "vector": [], "sparse_vector": [], "title": "Ghost Domain Reloaded: Vulnerable Links in Domain Name Delegation and Revocation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>esong Bai", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we propose textit{Phoenix Domain}, a general and novel attack that allows adversaries to maintain the revoked malicious domain continuously resolvable at scale, which enables an old, mitigated attack, Ghost Domain. textit{Phoenix Domain} has two variations and affects all mainstream DNS software and public DNS resolvers overall because it does not violate any DNS specifications and best security practices. The attack is made possible through systematically ''textit{reverse engineer}'' the cache operations of 8 DNS implementations, and new attack surfaces are revealed in the domain name delegation processes. We select 41 well-known public DNS resolvers and prove that all surveyed DNS services are vulnerable to textit{Phoenix Domain}, including Google Public DNS and Cloudflare DNS. Extensive measurement studies are performed with 210k stable and distributed DNS recursive resolvers, and results show that even after one month from domain name revocation and cache expiration, more than 25% of recursive resolvers can still resolve it. The proposed attack provides an opportunity for adversaries to evade the security practices of malicious domain take-down. We have reported discovered vulnerabilities to all affected vendors and suggested 6 types of mitigation approaches to them. Until now, 7 DNS software providers and 15 resolver vendors, including BIND, Unbound, Google, and Cloudflare, have confirmed the vulnerabilities, and some of them are implementing and publishing mitigation patches according to our suggestions. In addition, 9 CVE numbers have been assigned. The study calls for standardization to address the issue of how to revoke domain names securely and maintain cache consistency.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233906", "vector": [], "sparse_vector": [], "title": "Double and Nothing: Understanding and Detecting Cryptocurrency Giveaway Scams.", "authors": ["Xigao Li", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As cryptocurrencies increase in popularity and users obtain and manage their own assets, attackers are pivoting from just abusing cryptocurrencies as a payment mechanism, to stealing crypto assets from end users. In this paper, we report on the first large-scale analysis of cryptocurrency giveaway scams. Giveaway scams are deceptively simple scams where attackers set up webpages advertising fake events and promising users to double or triple the funds that they send to a specific wallet address. To understand the population of these scams in the wild we design and implement CryptoScamTracker, a tool that uses Certificate Transparency logs to identify likely giveaway scams. Through a 6-month-long experiment, CryptoScamTracker identified a total of 10,079 giveaway scam websites targeting users of all popular cryptocurrencies. Next to analyzing the hosting and domain preferences of giveaway scammers, we perform the first quantitative analysis of stolen funds using the public blockchains of the abused cryptocurrencies, extracting the transactions corresponding to 2,266 wallets belonging to scammers. We find that just for the scams discovered in our reporting period, attackers have stolen the equivalent of tens of millions of dollars, organizing large-scale campaigns across different cryptocurrencies. Lastly, we find evidence that attackers try to re-victimize users by offering fund-recovery services and that some victims send funds multiple times to the same scammers.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233907", "vector": [], "sparse_vector": [], "title": "Towards Automatic and Precise Heap Layout Manipulation for General-Purpose Programs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chaojing Tang"], "summary": "A critical challenge in automatic exploit generation is to find out whether an exploitable state can be constructed by manipulating the heap layout. This is usually achieved by re-arranging the objects in heap memory according to an orchestrated strategy that utilizes the program's heap operations. However, hindered by the difficulty in strategically coordinating the use of heap operations given the complexity in the program logic and heap allocation mechanisms,  the goal of precise heap layout manipulation for general-purpose programs has not been accomplished. In this paper, we present BAGUA, an innovative solution towards automatically and precisely manipulating heap layouts for general-purpose programs. Specifically, BAGUA first precisely identifies the primitives of heap layout manipulation using the heap operation dependence graph and thoroughly analyzes their dependencies and capabilities. On this basis, it models the heap layout manipulation as an integer linear programming problem and solves the constraints, in order to identify the sequence of primitives that achieves a desired heap layout. By triggering the primitives in such an order, we are able to construct new proof-of-concept inputs of target programs to achieve an exploitable heap layout. Highlights of our research include a set of new techniques that address the specific challenges of analyzing general-purpose programs, such as eliminating the side effect of heap allocators and extending the capability in manipulating heap layouts. We implemented a prototype of BAGUA and evaluated it on 27 publicly-known bugs in real-world programs. With BAGUA's strength in pinpointing primitives and handling the side effect of heap allocators, it successfully generates desired heap layouts for 23 of the bugs, which is way beyond what prior research can achieve.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233908", "vector": [], "sparse_vector": [], "title": "Backdoor Attacks Against Dataset Distillation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Dataset distillation has emerged as a prominent technique to improve data efficiency when training machine learning models. It encapsulates the knowledge from a large dataset into a smaller synthetic dataset. A model trained on this smaller distilled dataset can attain comparable performance to a model trained on the original training dataset. However, the existing dataset distillation techniques mainly aim at achieving the best trade-off between resource usage efficiency and model utility. The security risks stemming from them have not been explored. This study performs the first backdoor attack against the models trained on the data distilled by dataset distillation models in the image domain. Concretely, we inject triggers into the synthetic data during the distillation procedure rather than during the model training stage, where all previous attacks are performed. We propose two types of backdoor attacks, namely NAIVEATTACK and DOORPING. NAIVEATTACK simply adds triggers to the raw data at the initial distillation phase, while DOORPING iteratively updates the triggers during the entire distillation procedure. We conduct extensive evaluations on multiple datasets, architectures, and dataset distillation techniques. Empirical evaluation shows that NAIVEATTACK achieves decent attack success rate (ASR) scores in some cases, while DOORPING reaches higher ASR scores (close to 1.0) in all cases. Furthermore, we conduct a comprehensive ablation study to analyze the factors that may affect the attack performance. Finally, we evaluate multiple defense mechanisms against our backdoor attacks and show that our attacks can practically circumvent these defense mechanisms.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233909", "vector": [], "sparse_vector": [], "title": "VulHawk: Cross-architecture Vulnerability Detection with Entropy-based Binary Code Search.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Code reuse is widespread in software development. It brings a heavy spread of vulnerabilities, threatening software security. Unfortunately, with the development and deployment of the Internet of Things (IoT), the harms of code reuse are magnified. Binary code search is a viable way to find these hidden vulnerabilities. Facing IoT firmware images compiled by different compilers with different optimization levels from different architectures, the existing methods are hard to fit these complex scenarios. In this paper, we propose a novel intermediate representation function model, which is an architecture-agnostic model for cross-architecture binary code search. It lifts binary code into microcode and preserves the main semantics of binary functions via complementing implicit operands and pruning redundant instructions. Then, we use natural language processing techniques and graph convolutional networks to generate function embeddings. We call the combination of a compiler, architecture, and optimization level as a file environment, and take a divide-and-conquer strategy to divide a similarity calculation problem of $C_N^2$ cross-file-environment scenarios into N-1 embedding transferring sub-problems. We propose an entropy-based adapter to transfer function embeddings from different file environments into the same file environment to alleviate the differences caused by various file environments. To precisely identify vulnerable functions, we propose a progressive search strategy to supplement function embeddings with fine-grained features to reduce false positives caused by patched functions. We implement a prototype named <PERSON>ul<PERSON>aw<PERSON> and conduct experiments under seven different tasks to evaluate its performance and robustness. The experiments show VulHawk outperforms Asm2Vec, Asteria, BinDiff, GMN, PalmTree, SAFE, and Trex.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233910", "vector": [], "sparse_vector": [], "title": "A Systematic Study of the Consistency of Two-Factor Authentication User Journeys on Top-Ranked Websites.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Heuristics for user experience state that users will transfer their expectations from one product to another. A lack of consistency between products can increase users' cognitive friction, leading to frustration and rejection. This paper presents the first systematic study of the external, functional consistency of two-factor authentication user journeys on top-ranked websites. We find that these websites implement only a minimal number of design aspects consistently (e.g., naming and location of settings) but exhibit mixed design patterns for setup and usage of a second factor. Moreover, we find that some of the more consistently realized aspects, such as descriptions of two-factor authentication, have been described in the literature as problematic and adverse to user experience. Our results advocate for more general UX guidelines for 2FA implementers and raise new research questions about the 2FA user journeys.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233911", "vector": [], "sparse_vector": [], "title": "LOKI: State-Aware Fuzzing Framework for the Implementation of Blockchain Consensus Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yu <PERSON>", "<PERSON><PERSON>", "Huizhong Li", "Jiaguang Sun"], "summary": "Blockchain consensus protocols are responsible for coordinating the nodes to make agreements on the transaction results. Their implementation bugs, including  memory-related and consensus logic vulnerabilities, may pose serious threats. Fuzzing is a promising technique for protocol vulnerability detection. However, existing fuzzers cannot deal with complex consensus states of distributed nodes, thus generating a large number of useless packets, inhibiting their effectiveness in reaching the deep logic of consensus protocols. In this work, we propose LOKI, a blockchain consensus protocol fuzzing framework that detects the consensus memory-related and logic bugs. LOKI senses consensus states in real-time by masquerading as a node. First, LOKI dynamically builds a state model that records the state transition of each node. After that, LOKI adaptively generates the input targets, types, and contents according to the state model. With a bug analyzer, LOKI detects the consensus protocol implementation bugs with well-defined oracles. We implemented and evaluated LOKI on four widely used commercial blockchain systems, including Go-Ethereum, Facebook Diem, IBM Fabric, and WeBank FISCO-BCOS. LOKI has detected 20 serious previously unknown vulnerabilities with 9 CVEs assigned. 14 of them are memory-related bugs, and 6 are consensus logic bugs. Compared with state-of-the-art tools such as Peach, Fluffy, and Twins, LOKI improves the branch coverage by an average of 43.21%, 182.05%, and 291.58%.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233912", "vector": [], "sparse_vector": [], "title": "The &quot;<PERSON>rix&quot; Resurrections: Robust Backdoor Detection via Gram Matrices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ruoxi Sun", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep Neural Networks (DNNs) are susceptible to backdoor attacks during training. The model corrupted in this way functions normally, but when triggered by certain patterns in the input, produces a predefined target label. Existing defenses usually rely on the assumption of the universal backdoor setting in which poisoned samples share the same uniform trigger. However, recent advanced backdoor attacks show that this assumption is no longer valid in dynamic backdoors where the triggers vary from input to input, thereby defeating the existing defenses. In this work, we propose a novel technique, Beatrix (backdoor detection via Gram matrix). <PERSON><PERSON> utilizes Gram matrix to capture not only the feature correlations but also the appropriately high-order information of the representations. By learning class-conditional statistics from activation patterns of normal samples, <PERSON><PERSON> can identify poisoned samples by capturing the anomalies in activation patterns. To further improve the performance in identifying target labels, <PERSON><PERSON> leverages kernel-based testing without making any prior assumptions on representation distribution. We demonstrate the effectiveness of our method through extensive evaluation and comparison with state-of-the-art defensive techniques. The experimental results show that our approach achieves an F1 score of 91.1% in detecting dynamic backdoors, while the state of the art can only reach 36.9%.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233913", "vector": [], "sparse_vector": [], "title": "Cryptographic Oracle-based Conditional Payments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider a scenario where two mutually distrustful parties, <PERSON> and <PERSON>, want to perform a payment conditioned on the outcome of some real-world event. A semi-trusted oracle (or a threshold number of oracles, in a distributed trust setting) is entrusted to attest that such an outcome indeed occurred, and only then the payment is successfully made. Such oracle-based conditional (ObC) payments are ubiquitous in many real-world applications, like financial adjudication, pre-scheduled payments or trading, and are a necessary building block to introduce information about real-world events into blockchains. In this work we show how to realize ObC payments with provable security guarantees and efficient instantiations. To do this, we propose a new cryptographic primitive that we call verifiable witness encryption based on threshold signatures (VweTS): Users can encrypt signatures on payments that can be decrypted if a threshold number of signers (e.g., oracles) sign another message (e.g., the description of an event outcome). We require two security notions: (1) one-wayness that guarantees that without the threshold number of signatures, the ciphertext hides the encrypted signature, and (2) verifiability, that guarantees that a ciphertext that correctly verifies can be successfully decrypted to reveal the underlying signature. We present provably secure and efficient instantiations of VweTS where the encrypted signature can be some of the widely used schemes like Schnorr, ECDSA or BLS signatures. Our main technical innovation is a new batching technique for cut-and- choose, inspired by the work of <PERSON>dell<PERSON>Riva on garbled circuits. Our VweTS instantiations can be readily used to realize ObC payments on virtually all cryptocurrencies of today in a fungible, cost-efficient, and scalable manner. The resulting ObC payments are the first to support distributed trust (i.e., multiple oracles) without requiring any form of synchrony or coordination among the users and the oracles. To demonstrate the practicality of our scheme, we present a prototype implementation and our benchmarks in commodity hardware show that the computation overhead is less than 25 seconds even for a threshold of 4-of-7 and a payment conditioned on 1024 different real-world event outcomes, while the communication overhead is below 2.3 MB.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233914", "vector": [], "sparse_vector": [], "title": "Parakeet: Practical Key Transparency for End-to-End Encrypted Messaging.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Ercan Oztürk", "<PERSON>", "<PERSON>"], "summary": "Encryption alone is not enough for secure end-to-end encrypted messaging: a server must also honestly serve public keys to users. Key transparency has been presented as an efficient solution for detecting (and hence deterring) a server that attempts to dishonestly serve keys. Key transparency involves two major components: (1) a username to public key mapping, stored and cryptographically committed to by the server, and, (2) an out-of-band consistency protocol for serving short commitments to users. In the setting of real-world deployments and supporting production scale, new challenges must be considered for both of these components. We enumerate these challenges and provide solutions to address them. In particular, we design and implement a memory-optimized and privacy-preserving verifiable data structure for committing to the username to public key store. To make this implementation viable for production, we also integrate support for persistent and distributed storage. We also propose a future-facing solution, termed \"compaction'', as a mechanism for mitigating practical issues that arise from dealing with infinitely growing server data structures. Finally, we implement a consensusless solution that achieves the minimum requirements for a service that consistently distributes commitments for a transparency application, providing a much more efficient protocol for distributing small and consistent commitments to users. This culminates in our production-grade implementation of a key transparency system (Parakeet) which we have open-sourced, along with a demonstration of feasibility through our benchmarks.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233915", "vector": [], "sparse_vector": [], "title": "Post-GDPR Threat Hunting on Android Phones: Dissecting OS-level Safeguards of User-unresettable Identifiers.", "authors": ["<PERSON>", "<PERSON>", "Guangshuai Xia", "<PERSON><PERSON>", "<PERSON><PERSON>", "Guangdong Bai", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ever since its genesis, Android has enabled apps to access data and services on mobile devices. This however involves a wide variety of user-unresettable identifiers (UUIs), e.g., the MAC address, which are associated with a device permanently. Given their privacy sensitivity, Android has tightened its UUI access policy since its version 10, in response to the increasingly strict privacy protection regulations around the world. Non-system apps are restricted from accessing them and are required to use user-resettable alternatives such as advertising IDs. In this work, we conduct a systematic study on the effectiveness of the UUI safeguards on Android phones including both Android Open Source Project (AOSP) and Original Equipment Manufacturer (OEM) phones. To facilitate our large-scale study, we propose a set of analysis techniques that discover and assess UUI access channels. Our approach features a hybrid analysis that consists of static program analysis of Android Framework and forensic analysis of OS images to uncover access channels. These channels are then tested with differential analysis to identify weaknesses that open any attacking opportunity. We have conducted a vulnerability assessment on 13 popular phones of 9 major manufacturers, most of which are top-selling and installed with the recent Android versions. Our study reveals that UUI mishandling pervasively exists, evidenced by 51 unique vulnerabilities found (8 listed by CVE). Our work unveils the status quo of the UUI protection in Android phones, complementing the existing studies that mainly focus on apps' UUI harvesting behaviors. Our findings should raise an alert to phone manufacturers and would encourage policymakers to further extend the scope of regulations with device-level data protection.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233916", "vector": [], "sparse_vector": [], "title": "Tactics, Threats &amp; Targets: Modeling Disinformation and its Mitigation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Disinformation can be used to sway public opinion toward a certain political or economic direction, adversely impact public health, and mobilize groups to engage in violent disobedience. A major challenge in mitigation is scarcity: disinformation is widespread but its mitigators are few. In this work, we interview fact-checkers, journalists, trust and safety specialists, researchers, and analysts who work in different organizations tackling problematic information across the world. From this interview study, we develop an understanding of the reality of combating disinformation across domains, and we use our findings to derive a cybersecurity-inspired framework to characterize the threat of disinformation. While related work has developed similar frameworks for conducting analyses and assessment, our work is distinct in providing the means to thoroughly consider the attacker side, their tactics and approaches. We demonstrate the applicability of our framework on several examples of recent disinformation campaigns.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233917", "vector": [], "sparse_vector": [], "title": "ChargePrint: A Framework for Internet-Scale Discovery and Security Analysis of EV Charging Management Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Electric Vehicle Charging Management Systems (EVCMS) are a collection of specialized software that allow users to remotely operate Electric Vehicle Charging Stations (EVCS). With the increasing number of deployed EVCS to support the growing global EV fleet, the number of EVCMS are consequently growing, which introduces a new attack surface. In this paper, we propose a novel multi-stage framework, ChargePrint, to discover Internet-connected EVCMS and investigate their security posture. ChargePrint leverages identifiers extracted from a small seed of EVCMS to extend the capabilities of device search engines through iterative fingerprinting and a combination of classification and clustering approaches. Using initial seeds from 1,800 discovered hosts that deployed 9 distinct EVCMS, we identified 27,439 online EVCS instrumented by 44 unique EVCMS. Consequently, our in-depth security analysis highlights the insecurity of the deployed EVCMS by uncovering 120 0-day vulnerabilities, which shed light on the feasibility of cyber attacks against the EVCS, its users, and the connected power grid. Finally, while we recommend countermeasures to mitigate future threats, we contribute to the security of the EVCS ecosystem by conducting a Coordinated Vulnerability Disclosure (CVD) effort with system developers/vendors who acknowledged and assigned the discovered vulnerabilities more than 20 CVE-IDs.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233918", "vector": [], "sparse_vector": [], "title": "Browser Permission Mechanisms Demystified.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern Web services provide rich content by accessing resources on user devices, including hardware devices such as cameras, microphones, and GPSs. Web browser vendors have adopted permission mechanisms that achieve appropriate control over access to such resources to protect user privacy. The permission mechanism gives users the ability to grant or deny their browser access to resources for each website. Despite the importance of permission mechanisms in protecting user privacy, previous studies have not been conducted to systematically understand their behavior and implementation. In this study, we developed Permium, a web browser analysis framework that automatically analyzes the behavior of permission mechanisms implemented by various browsers. Using the Permium framework, we systematically studied the behavior of permission mechanisms for 22 major browser implementations running on five different operating systems, including mobile and desktop. We determined that the implementation and behavior of permission mechanisms are fragmented and inconsistent between operating systems, even for the same browser (i.e., Windows Chrome vs. iOS Chrome) and that the implementation inconsistencies can lead to privacy risks. Based on the behavior and implementation inconsistencies of the permission mechanism revealed by our measurement study, we developed two proof-of-concept attacks and evaluated their feasibility. The first attack uses the permission information collected by exploiting the inconsistencies to secretly track the user. The second attack aims to create a situation in which the user cannot correctly determine the origin of the permission request, and the user incorrectly grants permission to a malicious site. Finally, we clarify the technical issues that must be standardized in privacy mechanisms and provide recommendations to OS/browser vendors to mitigate the threats identified in this study.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233919", "vector": [], "sparse_vector": [], "title": "Preventing SIM Box Fraud Using Device Model Fingerprinting.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sangwook Bae", "Mincheol Son", "Yong<PERSON> Lee", "<PERSON>", "<PERSON><PERSON>"], "summary": "SIM boxes have been playing a critical role in the underground ecosystem of international-scale frauds that steal billions of dollars from individual victims and mobile network operators across the globe. Many mitigation schemes have been proposed for these frauds, mainly aiming to detect fraud call sessions; however, one direct approach to this problem---the prevention of the SIM box devices from network use---has not drawn much attention despite its highly anticipated benefit. This is exactly what we aim to achieve in this paper. We propose a simple access control logic that detects when unauthorized SIM boxes use cellular networks for communication. At the heart of our defense proposal is the precise fingerprinting of device models (eg, distinguishing an iPhone 13 from any other smartphone models on the market) and device types (ie, smartphones and IoT devices) without relying on international mobile equipment identity, which can be spoofed easily. We empirically show that fingerprints, which were constructed from network-layer auxiliary information with more than 31K features, are mostly distinct among 85 smartphones and thus can be used to prevent the vast majority of illegal SIM boxes from making unauthorized voice calls. Our proposal, as the very first practical, reliable unauthorized cellular device model detection scheme, greatly simplifies the mitigation against SIM box frauds.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233920", "vector": [], "sparse_vector": [], "title": "An OS-agnostic Approach to Memory Forensics.", "authors": ["<PERSON>", "Matteo Dell&apos;Amico", "<PERSON><PERSON>"], "summary": "The analysis of memory dumps presents unique challenges, as operating systems use a variety of (often undocumented) ways to represent data in memory. To solve this problem, forensics tools maintain collections of models that precisely describe the kernel data structures used by a handful of operating systems. However, these models cannot be generalized and developing new models may require a very long and tedious reverse engineering effort for closed source systems. In the last years, the tremendous increase in the number of IoT devices, smart-home appliances and cloud-hosted VMs resulted in a growing number of OSs which are not supported by current forensics tools. The way we have been doing memory forensics until today, based on handwritten models and rules, cannot simply keep pace with this variety of systems. To overcome this problem, in this paper we introduce the new concept of emph{OS-agnostic memory forensics}, which is based on techniques that can recover certain forensics information without emph{any} knowledge of the internals of the underlying OS. Our approach allows to automatically identify different types of data structures by using only their topological constraints and then supports two modes of investigation. In the first, it allows to traverse the recovered structures by starting from predetermined textit{seeds}, i.e., pieces of forensics-relevant information (such as a process name or an IP address) that an analyst knows emph{a priori} or that can be easily identified in the dump. Our experiments show that even a single seed can be sufficient to recover the entire list of processes and other important forensics data structures in dumps obtained from 14 different OSs, without any knowledge of the underlying kernels. In the second mode of operation, our system requires no seed but instead uses a set of heuristics to rank all memory data structures and present to the analysts only the most `promising' ones. Even in this case, our experiments show that an analyst can use our approach to easily identify forensics-relevant structured information in a truly OS-agnostic scenario.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233921", "vector": [], "sparse_vector": [], "title": "Evasion Attacks and Defenses on Smart Home Physical Event Verification.", "authors": ["Muslum Ozgur Ozmen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In smart homes, when an actuator's state changes, it sends an event notification to the IoT hub to report this change (e.g., the door is unlocked). Prior works have shown that event notifications are vulnerable to spoofing and masking attacks. In event spoofing, an adversary reports to the IoT hub a fake event notification that did not physically occur. In event masking, an adversary suppresses the notification of an event that physically occurred. These attacks create inconsistencies between physical and cyber states of actuators, enabling an adversary to indirectly gain control over safety-critical devices by triggering IoT apps. To mitigate these attacks, event verification systems (EVS), or broadly IoT anomaly detection systems, leverage physical event fingerprints that describe the relations between events and their influence on sensor readings. However, smart homes have complex physical interactions between events and sensors that characterize the event fingerprints. Our study of the recent EVS, unfortunately, has revealed that they widely ignore such interactions, which enables an adversary to evade these systems and launch successful event spoofing and masking attacks without getting detected. In this paper, we first explore the evadable physical event fingerprints and show that an adversary can realize them to bypass the EVS given the same threat model. We develop two defenses, EVS software patching and sensor placement with the interplay of physical modeling and formal analysis, to generate robust physical event fingerprints and demonstrate how they can be integrated into the EVS. We evaluate the effectiveness of our approach in two smart home settings that contain 12 actuators and 16 sensors when two different state-of-the-art EVS are deployed. Our experiments demonstrate that 71% of their physical fingerprints are vulnerable to evasion. By incorporating our approach, they build robust physical event fingerprints, and thus, properly mitigate realistic attack vectors.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233922", "vector": [], "sparse_vector": [], "title": "Your Router is My Prober: Measuring IPv6 Networks via ICMP Rate Limiting Side Channels.", "authors": ["Long Pan", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Active Internet measurements face challenges when some measurements require many remote vantage points. In this paper, we propose a novel technique for measuring remote IPv6 networks via side channels in ICMP rate limiting, a required function for IPv6 nodes to limit the rate at which ICMP error messages are generated. This technique, *iVantage*, can to some extent use 1.1M remote routers distributed in 9.5k autonomous systems and 182 countries as our “vantage points”.We apply *iVantage* to two different, but both challenging measurement tasks: 1) measuring the deployment of inbound source address validation (ISAV) and 2) measuring reachability between arbitrary Internet nodes. We accomplish these two tasks from only one local vantage point without controlling the targets or relying on other services within the target networks. Our large-scale ISAV measurements cover ~50% of all IPv6 autonomous systems and find ~79% of them are vulnerable to spoofing, which is the most large-scale measurement study of IPv6 ISAV to date. Our method for reachability measurements achieves over 80% precision and recall in our evaluation. Finally, we perform an Internet-wide measurement of the ICMP rate limiting implementations, present a detailed discussion on ICMP rate limiting, particularly the potential security and privacy risks in the mechanism of ICMP rate limiting, and provide possible mitigation measures. We make our code available to the community.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233923", "vector": [], "sparse_vector": [], "title": "EdgeTDC: On the Security of Time Difference of Arrival Measurements in CAN Bus Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A Controller Area Network (CAN bus) is a message-based protocol for intra-vehicle communication designed mainly with robustness and safety in mind. In real-world deployments, CAN bus does not offer common security features such as message authentication. Due to the fact that automotive suppliers need to guarantee interoperability, most manufacturers rely on a decade-old standard (ISO 11898) and changing the format by introducing MACs is impractical. Research has therefore suggested to address this lack of authentication with CAN bus Intrusion Detection Systems (IDSs) that augment the bus with separate modules. IDSs attribute messages to the respective sender by measuring physical-layer features of the transmitted frame. Those features are based on timings, voltage levels, transients—and, as of recently, Time Difference of Arrival (TDoA) measurements. In this work, we show that TDoA-based approaches presented in prior art are vulnerable to novel spoofing and poisoning attacks. We describe how those proposals can be fixed and present our own method called EdgeTDC. Unlike existing methods, EdgeTDC does not rely on Analog-to-digital converters (ADCs) with high sampling rate and high dynamic range to capture the signals at sample level granularity. Our method uses time-to-digital converters (TDCs) to detect the edges and measure their timings. Despite being inexpensive to implement, TDCs offer low latency, high location precision and the ability to measure every single edge (rising and falling) in a frame. Measuring each edge makes analog sampling redundant and allows the calculation of statistics that can even detect tampering with parts of a message. Through extensive experimentation, we show that EdgeTDC can successfully thwart masquerading attacks in the CAN system of modern vehicles.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233924", "vector": [], "sparse_vector": [], "title": "Drone Security and the Mysterious Case of DJI&apos;s DroneID.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>s", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Consumer drones enable high-class aerial video photography, promise to reform the logistics industry, and are already used for humanitarian rescue operations and during armed conflicts. Contrasting their widespread adoption and high popularity, the low entry barrier for air mobility---a traditionally heavily regulated sector---poses many risks to safety, security, and privacy. Malicious parties could, for example, (mis-)use drones for surveillance, transportation of illegal goods, or cause economic damage by intruding the closed airspace above airports. To prevent harm, drone manufacturers employ several countermeasures to enforce safe and secure use of drones, e.g., they impose software limits regarding speed and altitude, or use geofencing to implement no-fly zones around airports or prisons. Complementing traditional countermeasures, drones from the market leader DJ<PERSON> implement a tracking protocol called DroneID, which is designed to transmit the position of both the drone and its operator to authorized entities such as law enforcement or operators of critical infrastructures. In this paper, we analyze security and privacy claims for drones, focusing on the leading manufacturer DJI with a market share of 94%. We first systemize the drone attack surface and investigate an attacker capable of eavesdropping on the drone's over-the-air data traffic. Based on reverse engineering of DJI firmware, we design and implement a decoder for DJI's proprietary tracking protocol DroneID, using only cheap COTS hardware. We show that the transmitted data is not encrypted, but accessible to anyone, compromising the drone operator's privacy. Second, we conduct a comprehensive analysis of drone security: Using a combination of reverse engineering, a novel fuzzing approach tailored to DJI's communication protocol, and hardware analysis, we uncover several critical flaws in drone firmware that allow attackers to gain elevated privileges on two different DJI drones and their remote control. Such root access paves the way to disable or bypass countermeasures and abuse drones. In total, we found 16 vulnerabilities, ranging from denial of service to arbitrary code execution. 14 of these bugs can be triggered remotely via the operator's smartphone, allowing us to crash the drone mid-flight.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233925", "vector": [], "sparse_vector": [], "title": "Hope of Delivery: Extracting User Locations From Mobile Instant Messengers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mobile instant messengers such as WhatsApp use delivery status notifications in order to inform users if a sent message has successfully reached its destination. This is useful and important information for the sender due to the often asynchronous use of the messenger service. However, as we demonstrate in this paper, this standard feature opens up a timing side channel with unexpected consequences for user location privacy. We investigate this threat conceptually and experimentally for three widely spread instant messengers. We validate that this information leak even exists in privacy-friendly messengers such as Signal and Threema. Our results show that, after a training phase, a messenger user can distinguish different locations of the message receiver. Our analyses involving multiple rounds of measurements and evaluations show that the timing side channel persists independent of distances between receiver locations -- the attack works both for receivers in different countries as well as at small scale in one city. For instance, out of three locations within the same city, the sender can determine the correct one with more than 80% accuracy. Thus, messenger users can secretly spy on each others' whereabouts when sending instant messages. As our countermeasure evaluation shows, messenger providers could effectively disable the timing side channel by randomly delaying delivery confirmations within the range of a few seconds. For users themselves, the threat is harder to prevent since there is no option to turn off delivery confirmations.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233926", "vector": [], "sparse_vector": [], "title": "Smarter Contracts: Detecting Vulnerabilities in Smart Contracts with Deep Transfer Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ethereum smart contracts are automated decentralized applications on the blockchain that describe the terms of the agreement between buyers and sellers, reducing the need for trusted intermediaries and arbitration. However, the deployment of smart contracts introduces new attack vectors into the cryptocurrency systems. In particular, programming flaws in smart contracts have been already exploited to lead to enormous financial loss. Hence, it is crucial to detect various vulnerability types in contracts effectively and efficiently. Existing vulnerability detection methods are limited in scope as they typically focus on one or a very limited set of vulnerabilities. Also, extending them to new vulnerability types requires costly re-design. In this work, we develop ESCORT, a deep learning-based vulnerability detection method that uses a common feature extractor to learn generic bytecode semantics of smart contracts and separate branches to learn the features of each vulnerability type. As a multi-label classifier, ESCORT can detect multiple vulnerabilities of the contract at once. Compared to prior detection methods, ESCORT can be easily extended to new vulnerability types with limited data via transfer learning. When a new vulnerability type emerges, ESCORT adds a new branch to the trained feature extractor and trains it with limited data. We evaluated ESCORT on a dataset of 3.61 million smart contracts and demonstrate that it achieves an average F1 score of 98% on six vulnerability types in initial training and yields an average F1 score of 96% in transfer learning phase on five additional vulnerability types. To the best of our knowledge, ESCORT is the first deep learning-based framework that utilizes transfer learning on new vulnerability types with minimal model modification and re-training overhead. Compared with existing non-ML tools, ESCORT can be applied to contracts of arbitrary complexity and ensures 100% contract coverage. In addition, we enable concurrent detection of multiple vulnerability types using a single unified framework, thus avoiding the efforts of setting up multiple tools and greatly reducing the detection time. We will open source our dataset and the data labeling toolchain to facilitate future research.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233927", "vector": [], "sparse_vector": [], "title": "Folk Models of Misinformation on Social Media.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we investigate what textit{folk models of misinformation} exist on social media with a sample of 235 social media users. Work on social media misinformation does not investigate how ordinary users deal with it; rather, the focus is mostly on the anxiety, tensions, or divisions misinformation creates. Studying only the structural aspects also overlooks how misinformation is internalized by users on social media and thus is quick to prescribe \"inoculation\" strategies for the presumed lack of immunity to misinformation. How users grapple with social media content to develop \"natural immunity\" as a precursor to misinformation resilience, however, remains an open question. We have identified at least five textit{folk models} that conceptualize misinformation as either: textit{political (counter)argumentation}, textit{out-of-context narratives}, textit{inherently fallacious information}, textit{external propaganda}, or simply textit{entertainment}. We use the rich conceptualizations embodied in these folk models to uncover how social media users minimize adverse reactions to misinformation encounters in their everyday lives.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233928", "vector": [], "sparse_vector": [], "title": "On the Anonymity of Peer-To-Peer Network Anonymity Schemes Used by Cryptocurrencies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Cryptocurrency systems can be subject to deanonymization attacks by exploiting the network-level communication on their peer-to-peer network. Adversaries who control a set of colluding node(s) within the peer-to-peer network can observe transactions being exchanged and infer the parties involved. Thus, various network anonymity schemes have been proposed to mitigate this problem, with some solutions providing theoretical anonymity guarantees. In this work, we model such peer-to-peer network anonymity solutions and evaluate their anonymity guarantees. To do so, we propose a novel framework that uses Bayesian inference to obtain the probability distributions linking transactions to their possible originators. We characterize transaction anonymity with those distributions, using entropy as metric of adversarial uncertainty on the originator's identity. In particular, we model Dandelion, Dandelion++, and Lightning Network. We study different configurations and demonstrate that none of them offers acceptable anonymity to their users. For instance, our analysis reveals that in the widely deployed Lightning Network, with $1%$ strategically chosen colluding nodes the adversary can uniquely determine the originator for $approx50%$ of the total transactions in the network. In Dandelion, an adversary that controls $15%$ of the nodes has on average uncertainty among only $8$ possible originators. Moreover, we observe that due to the way Dandelion and Dandelion++ are designed, increasing the network size does not correspond to an increase in the anonymity set of potential originators. Alarmingly, our longitudinal analysis of Lightning Network reveals rather an inverse trend---with the growth of the network the overall anonymity decreases.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233929", "vector": [], "sparse_vector": [], "title": "CHKPLUG: Checking GDPR Compliance of WordPress Plugins via Cross-language Code Property Graph.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Kang", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Cao", "<PERSON>"], "summary": "WordPress, a well-known content management system (CMS), provides so-called plugins to augment default functionalities. One challenging problem of deploying WordPress plugins is that they may collect and process user data, such as Personal Identifiable Information (PII), which is usually regulated by laws such as General Data Protection Regulation (GDPR). To the best of our knowledge, no prior works have studied GDPR compliance in WordPress plugins, which often involve multiple program languages, such as PHP, JavaScript, HTML, and SQL. In this paper, we design CHKPLUG, the first automated GDPR checker of WordPress plugins for their compliance with GDPR articles related to PII. The key to CHKPLUG is to match WordPress plugin behavior with GDPR articles using graph queries to a novel cross-language code property graph (CCPG). Specifically, the CCPG models both inline language integration (such as PHP and HTML) and key-value-related connection (such as HTML and JavaScript). CHKPLUG reports a GDPR violation if certain patterns are found in the CCPG. We evaluated CHKPLUG with human-annotated WordPress plugins.  Our evaluation shows that CHKPLUG achieves good performance with 98.8% TNR (True Negative Rate) and 89.3% TPR (True Positive Rate) in checking whether a certain WordPress plugin complies with GDPR. To investigate the current surface of the marketplace, we perform a measurement analysis which shows that 368 plugins violate data deletion regulations, meaning plugins do not provide any functionalities to erase user information from the website.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233930", "vector": [], "sparse_vector": [], "title": "I Still Know What You Watched Last Sunday: Privacy of the HbbTV Protocol in the European Smart TV Landscape.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The ever-increasing popularity of Smart TVs and support for the Hybrid Broadcast Broadband TV (HbbTV) standard allow broadcasters to enrich content offered to users via the standard broadcast signal with Internet-delivered apps, e.g., ranging from quizzes during a TV show to targeted advertisement. HbbTV works using standard web technologies as transparent overlays over a TV channel. Despite the number of HbbTV-enabled devices rapidly growing, studies on the protocol's security and privacy aspects are scarce, and no standard protective measure is in place. We fill this gap by investigating the current state of HbbTV in the European landscape and assessing its implications for users' privacy. We shift the focus from the Smart TV's firmware and app security, already studied in-depth in related work, to the content transmission protocol itself. Contrary to traditional ``linear TV'' signals, HbbTV allows for bi-directional communication: in addition to receiving TV content, it also allows for transmitting data back to the broadcaster. We describe techniques broadcasters use to measure users' (viewing) preferences and show how the protocol's implementation can cause severe privacy risks by studying its deployment by 36 TV channels in five European countries (Italy, Germany, France, Austria, and Finland). We also survey users' awareness of Smart TV and HbbTV-related risks. Our results show little understanding of the possible threats users are exposed to. Finally, we present a denylist-based mechanism to ensure a safe experience for users when watching TV and to reduce the privacy issues that HbbTV may pose.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233931", "vector": [], "sparse_vector": [], "title": "He-HTLC: Revisiting Incentives in HTLC.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hashed Time-Locked Contracts (HTLCs) are a widely used primitive in blockchain systems such as payment channels, atomic swaps, etc. Unfortunately, HTLC is incentive-incompatible and is vulnerable to bribery attacks. The state-of-the-art solution is MAD-HTLC (Oakland'21), which proposes an elegant idea that leverages miners' profit-driven nature to defeat bribery attacks. In this paper, we show that MAD-HTLC is still vulnerable as it only considers a somewhat narrow set of passive strategies by miners. Through a family of novel reverse-bribery attacks, we show concrete active strategies that miners can take to break MAD-HTLC and profit at the loss of MAD-HTLC users. For these attacks, we present their implementation and game-theoretical profitability analysis. Based on the learnings from our attacks, we propose a new HTLC realization, He-HTLC (Our specification is lightweight and inert to incentive manipulation attacks. Hence, we call it He-HTLC where He stands for Helium.) that is provably secure against all possible strategic manipulation (passive and active). In addition to being secure in a stronger adversary model, He-HTLC achieves other desirable features such as low and user-adjustable collateral, making it more practical to implement and use the proposed schemes. We implemented He-HTLC on Bitcoin and the transaction cost of He-HTLC is comparative to average Bitcoin transaction fees.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233932", "vector": [], "sparse_vector": [], "title": "DOITRUST: Dissecting On-chain Compromised Internet Domains via Graph Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Ruoxi Sun", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Surya Nepal", "<PERSON><PERSON><PERSON>"], "summary": "Traditional block/allow lists remain a significant defense against malicious websites, by limiting end-users' access to domain names. However, such lists are often incomplete and reactive in nature. In this work, we first introduce an expansion graph which creates organically grown Internet domain allow-lists based on trust transitivity by crawling hyperlinks. Then, we highlight the gap of monitoring nodes with such an expansion graph, where malicious nodes are buried deep along the paths from the compromised websites, termed as \"on-chain compromise\". The stealthiness (evasion of detection) and large-scale issues impede the application of existing web malicious analysis methods for identifying on-chain compromises within the sparsely labeled graph. To address the unique challenges of revealing the on-chain compromises, we propose a two-step integrated scheme, DoITrust, leveraging both individual node features and topology analysis: (i) we develop a semi-supervised suspicion prediction scheme to predict the probability of a node being relevant to targets of compromise (i.e., the denied nodes), including a novel node ranking approach as an efficient global propagation scheme to incorporate the topology information, and a scalable graph learning scheme to separate the global propagation from the training of the local prediction model, and (ii) based on the suspicion prediction results, efficient pruning strategies are proposed to further remove highly suspicious nodes from the crawled graph and analyze the underlying indicator of compromise. Experimental results show that DoITrust achieves 90% accuracy using less than 1% labeled nodes for the suspicion prediction, and its learning capability outperforms existing node-based and structure-based approaches. We also demonstrate that DoITrust is portable and practical. We manually review the detected compromised nodes, finding that at least 94.55% of them have suspicious content, and investigate the underlying indicator of on-chain compromise.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233933", "vector": [], "sparse_vector": [], "title": "BARS: Local Robustness Certification for Deep Learning based Traffic Analysis Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Dongqi Han", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xingang Shi", "<PERSON><PERSON>"], "summary": "Deep learning (DL) performs well in many traffic analysis tasks. Nevertheless, the vulnerability of deep learning weakens the real-world performance of these traffic analyzers (e.g., suffering from evasion attack). Many studies in recent years focused on robustness certification for DL-based models. But existing methods perform far from perfectly in the traffic analysis domain. In this paper, we try to match three attributes of DL-based traffic analysis systems at the same time: (1) highly heterogeneous features, (2) varied model designs, (3) adversarial operating environments. Therefore, we propose BARS, a general robustness certification framework for DL-based traffic analysis systems based on boundary-adaptive randomized smoothing. To obtain tighter robustness guarantee, BARS uses optimized smoothing noise converging on the classification boundary. We firstly propose the Distribution Transformer for generating optimized smoothing noise. Then to optimize the smoothing noise, we propose some special distribution functions and two gradient based searching algorithms for noise shape and noise scale. We implement and evaluate BARS in three practical DL-based traffic analysis systems. Experiment results show that BARS can achieve tighter robustness guarantee than baseline methods. Furthermore, we illustrate the practicability of BARS through five application cases (e.g., quantitatively evaluating robustness).", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233934", "vector": [], "sparse_vector": [], "title": "Machine Unlearning of Features and Labels.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Removing information from a machine learning model is a non-trivial task that requires to partially revert the training process. This task is unavoidable when sensitive data, such as credit card numbers or passwords, accidentally enter the model and need to be removed afterwards. Recently, different concepts for machine unlearning have been proposed to address this problem. While these approaches are effective in removing individual data points, they do not scale to scenarios where larger groups of features and labels need to be reverted. In this paper, we propose the first method for unlearning features and labels. Our approach builds on the concept of influence functions and realizes unlearning through closed-form updates of model parameters. It enables to adapt the influence of training data on a learning model retrospectively, thereby correcting data leaks and privacy issues. For learning models with strongly convex loss functions, our method provides certified unlearning with theoretical guarantees. For models with non-convex losses, we empirically show that unlearning features and labels is effective and significantly faster than other strategies.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233935", "vector": [], "sparse_vector": [], "title": "Thwarting Smartphone SMS Attacks at the Radio Interface Layer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The short message service (SMS) is a cornerstone of modern smartphone communication that enables inter-personal text messaging and other SMS-based services (e.g., two-factor authentication). However, it can also be readily exploited to compromise unsuspecting remote victims. For instance, novel exploits such as <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> enable transmission of binary SMS messages that could surreptitiously execute dangerous commands on a victim device. The SMS channel may also be subverted to drive other nefarious activities (e.g., spamming, DoS, and tracking), thereby undermining end-user security and privacy. Unfortunately, neither contemporary smartphone operating systems nor existing defense techniques provide a comprehensive bulwark against the spectrum of evolving SMS-driven threats. To address this limitation, we develop a novel defense framework called RILDEFENDER, which to the best of our knowledge is the first inline prevention system integrated into the radio interface layer (RIL) of Android smartphones. We describe an implementation of RILDEFENDER on three smartphone models with five Android versions of the Android Open Source Project (AOSP), and show that it is able to protect users from six types of SMS attacks spanning four adversary models. We evaluate RILDEFENDER against 19 reproduced SMS attacks and 11 contemporary SMS malware samples and find that RILDEFENDER detects all and automatically prevents all but one of these threats without affecting normal cellular operations.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233936", "vector": [], "sparse_vector": [], "title": "DiffCSP: Finding Browser Bugs in Content Security Policy Enforcement through Differential Testing.", "authors": ["Seongil Wi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Content Security Policy (CSP) is one of the de facto security mechanisms that mitigate web threats. Many websites have been deploying CSPs mainly to mitigate cross-site scripting (XSS) attacks by instructing client browsers to constrain JavaScript (JS) execution. However, a browser bug in CSP enforcement enables an adversary to bypass a deployed CSP, posing a security threat. As the CSP specification evolves, CSP becomes more complicated in supporting an increasing number of directives, which brings additional complexity to implementing correct enforcement behaviors. Unfortunately, the finding of CSP enforcement bugs in a systematic way has been largely understudied. In this paper, we propose DiffCSP, the first differential testing framework to find CSP enforcement bugs involving JS execution. DiffCSP generates CSPs and a comprehensive set of HTML instances that exhibit all known ways of executing JS snippets. DiffCSP then executes each HTML instance for each generated policy across different browsers, thereby collecting inconsistent execution results. To analyze a large volume of the execution results, we leverage a decision tree and identify common causes of the observed inconsistencies. We demonstrate the efficacy of DiffCSP by finding 29 security bugs and eight functional bugs. We also show that three bugs are due to unclear descriptions of the CSP specification. We further identify the common root causes of CSP enforcement bugs, such as incorrect CSP inheritance and hash handling. We confirm the risky trend of client browsers deriving completely different interpretations from the same CSPs, which raises security concerns. Our study demonstrates the effectiveness of DiffCSP for identifying CSP enforcement bugs, and our findings have contributed to patching 12 security bugs in major browsers, including Chrome and Safari.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233937", "vector": [], "sparse_vector": [], "title": "Real Threshold ECDSA.", "authors": ["<PERSON>", "<PERSON>. <PERSON>", "Hoover H. F<PERSON> Yin", "Sherman S. M. Chow"], "summary": "Threshold ECDSA recently regained popularity due to decentralized applications such as DNSSEC and cryptocurrency asset custody. Latest (communication-optimizing) schemes often assume all n or at least n' >= t participating users remain honest throughout the pre-signing phase, essentially degenerating to n'-out-of-n' multiparty signing instead of t-out-of-n threshold signing. When anyone misbehaves, all signers must restart from scratch, rendering prior computation and communication in vain. This hampers the adoption of threshold ECDSA in time-critical situations and confines its use to a small signing committee. To mitigate such denial-of-service vulnerabilities prevalent in state-of-the-art, we propose a robust threshold ECDSA scheme that achieves the t-out-of-n threshold flexibility \"for real\" throughout the whole pre-signing and signing phases without assuming an honest majority. Our scheme is desirable when computational resources are scarce and in a decentralized setting where faults are easier to be induced. Our design features 4-round pre-signing, O(n) cheating identification, and self-healing machinery over distributive shares. Prior arts mandate abort after an O(n^2)-cost identification, albeit with 3-round pre-signing (<PERSON><PERSON> et al., CCS '20), or O(n) using 6 rounds (<PERSON><PERSON><PERSON> et al., TCS '23). Empirically, our scheme saves up to ~30% of the communication cost, depending on at which stage the fault occurred.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233938", "vector": [], "sparse_vector": [], "title": "OBI: a multi-path oblivious RAM for forward-and-backward-secure searchable encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic searchable encryption (DSE) is a user-cloud protocol for searching over outsourced encrypted data. Many current DSE schemes resort to oblivious RAMs (ORAM) to achieve forward privacy and backward privacy, which is a concept to describe security levels of the protocol. We show that, however, most prior ORAM-based DSE suffers from a new problem: it is inefficient to fetch/insert a large set of data blocks. We call this the large-stash eviction problem. To address the problem, we present OBI, a multi-path Oblivious RAM, which accesses multiple tree paths per query for handling a large set of data blocks. We classify traditional tree-based ORAMs as single-path ORAMs if they access a single path per query. OBI has two new high-throughtput multi-path eviction algorithms that are several orders of magnitude more efficient than the well-known PATH-ORAM eviction algorithm when the stash is large. We prove that the proposed multi-path ORAM outperforms the traditional single-path ORAM in terms of local stash size and insertion efficiency. Security analysis shows that OBI is secure under the strong forward and backward security model. OBI can protect the well-known DSE leakage, such as the search pattern and the size pattern. We also show that OBI can be applied to oblivious file systems and oblivious conjunctive-query DSE schemes. We conduct experiments on the Enron dataset. The experimental results demonstrate that OBI is far more efficient than the state-of-the-art ORAM-based DSE schemes.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233939", "vector": [], "sparse_vector": [], "title": "Him of Many Faces: Characterizing Billion-scale Adversarial and Benign Browser Fingerprints on Commercial Websites.", "authors": ["Shujiang Wu", "Pengfei Sun", "<PERSON>", "<PERSON><PERSON> Cao"], "summary": "Browser fingerprints, while traditionally being used for web tracking, have recently been adopted more and more often for defense or detection of various attacks targeting real-world websites. Faced with these situations, adversaries also upgrade their weapons to generate their own fingerprints---defined as adversarial fingerprints---to bypass existing defense or detection. Naturally, such adversarial fingerprints are different from benign ones from user browsers because they are generated intentionally for defense bypass. However, no prior works have studied such differences in the wild by comparing adversarial with benign fingerprints let alone how adversarial fingerprints are generated. In this paper, we present the first billion-scale measurement study of browser fingerprints collected from 14 major commercial websites (all ranked among Alexa/Tranco top 10,000). We further classify these fingerprints into either adversarial or benign using a learning-based, feedback-driven fraud and bot detection system from a major security company, and then study their differences. Our results draw three major observations: (i) adversarial fingerprints are significantly different from benign ones in many metrics, e.g., entropy, unique rate, and evolution speed, (ii) adversaries are adopting various tools and strategies to generate adversarial fingerprints, and (iii) adversarial fingerprints vary across different attack types, e.g., from content scraping to fraud transactions.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233940", "vector": [], "sparse_vector": [], "title": "Access Your Tesla without Your Awareness: Compromising Keyless Entry System of Model 3.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Qing Li", "<PERSON>"], "summary": "Tesla Model 3 has equipped with Phone Keys and Key Cards in addition to traditional key fobs for better driving experiences. These new features allow a driver to enter and start the vehicle without using a mechanical key through a wireless authentication process between the vehicle and the key. Unlike the requirements of swiping against the car for Key Cards, the Tesla mobile app’s Phone Key feature can unlock a Model 3 while your smartphone is still in a pocket or bag. In this paper, we performed a detailed security analysis aiming at Tesla keys, especially for Key Cards and Phone Keys. Starting with reverse engineering the mobile application and sniffing the communication data, we reestablished pairing and authentication protocols and analyzed their potential issues. Missing the certificate verification allows an unofficial Key Card to work as an official one. Using these third-party products may lead to serious security problems. Also, the weaknesses of the current protocol lead to a man-in-the-middle (MitM) attack through a Bluetooth channel. The MitM attack is an improved relay attack breaking the security of the authentication procedures for Phone Keys. We also developed an App named TESmLA installed on customized Android devices to complete the proof-of-concept. The attackers can break into Tesla Model 3 and drive it away without the awareness of the car owner. Our results bring into question the security of Passive Keyless Entry and Start (PKES) and Bluetooth implementations in security-critical applications. To mitigate the security problems, we discussed the corresponding countermeasures and feasible secure scheme in the future.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233941", "vector": [], "sparse_vector": [], "title": "BlockScope: Detecting and Investigating Propagated Vulnerabilities in Forked Blockchain Projects.", "authors": ["<PERSON>", "Yuzhou Fang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the open-source nature of the blockchain ecosystem, it is common for new blockchains to fork or partially reuse the code of classic blockchains. For example, the popular Dogecoin, Litecoin, Binance BSC, and Polygon are all variants of Bitcoin/Ethereum. These “forked” blockchains thus could encounter similar vulnerabilities that are propagated from Bitcoin/Ethereum during forking or subsequently commit fetching. In this paper, we conduct a systematic study of detecting and investigating the propagated vulnerabilities in forked blockchain projects. To facilitate this study, we propose BlockScope, a novel tool that can effectively and efficiently detect multiple types of cloned vulnerabilities given an input of existing Bitcoin/Ethereum security patches. Specifically, BlockScope adopts similarity-based code match and designs a new way of calculating code similarity to cover all the syntax-wide variant (i.e., Type-1, Type-2, and Type-3) clones. Moreover, BlockScope automatically extracts and leverages the contexts of patch code to narrow down the search scope and locate only potentially relevant code for comparison. Our evaluation shows that BlockScope achieves good precision and high recall both at 91.8% (1.8 times higher recall than that in the state-of-the-art ReDeBug while with close precision). BlockScope allows us to discover 101 previously unknown vulnerabilities in 13 out of the 16 forked projects of Bitcoin and Ethereum, including 16 from Dogecoin, 6 from Litecoin, 1 from Binance BSC, and 4 from Optimism. We have reported all the vulnerabilities to their developers; 40 of them have been patched or accepted, 66 were acknowledged or under pending, and only 4 were rejected. We further investigate the propagation and patching processes of discovered vulnerabilities, and reveal three types of vulnerability propagation from source to forked projects, as well as the long delay (mostly over 200 days) for releasing patches in Bitcoin forks (vs. ∼100 days for Ethereum forks).", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233942", "vector": [], "sparse_vector": [], "title": "PPA: Preference Profiling Attack Against Federated Learning.", "authors": ["<PERSON><PERSON>", "Yansong Gao", "<PERSON><PERSON>", "<PERSON>", "Zhiyang Dai", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Federated learning (FL) trains a global model across a number of decentralized users, each with a local dataset. Compared to traditional centralized learning, FL does not require direct access to local datasets and thus aims to mitigate data privacy concerns. However, data privacy leakage in FL still exists due to inference attacks, including membership inference, property inference, and data inversion. In this work, we propose a new type of privacy inference attack, coined Preference Profiling Attack (PPA), that accurately profiles the private preferences of a local user, e.g., most liked (disliked) items from the client's online shopping and most common expressions from the user's selfies.  In general, PPA can profile top-$k$ (i.e., $k$ = $1, 2, 3$ and $k = 1$ in particular) preferences contingent on the local client (user)'s characteristics. Our key insight is that the gradient variation of a local user's model has a distinguishable sensitivity to the sample proportion of a given class, especially the majority (minority) class. By observing a user model's gradient sensitivity to a class, PPA can profile the sample proportion of the class in the user's local dataset, and thus textit{the user's preference of the class} is exposed. The inherent statistical heterogeneity of FL further facilitates PPA. We have extensively evaluated the PPA's effectiveness using four datasets (MNIST, CIFAR10, RAF-DB and Products-10K). Our results show that PPA achieves 90% and 98% top-$1$ attack accuracy to the MNIST and CIFAR10, respectively. More importantly, in real-world commercial scenarios of shopping (i.e., Products-10K) and social network (i.e., RAF-DB), PPA gains a top-$1$ attack accuracy of 78% in the former case to infer the most ordered items (i.e., as a commercial competitor), and 88% in the latter case to infer a victim user's most often facial expressions, e.g., disgusted. The top-$3$ attack accuracy and top-$2$ accuracy is up to 88% and 100% for the Products-10K and RAF-DB, respectively. We also show that PPA is insensitive to the number of FL's local users (up to 100 we tested) and local training epochs (up to 20 we tested) used by a user. Although existing countermeasures such as dropout and differential privacy protection can lower the PPA's accuracy to some extent, they unavoidably incur notable deterioration to the global model. The source code is available at https://github.com/PPAattack.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}, {"primary_key": "1233943", "vector": [], "sparse_vector": [], "title": "SoundLock: A Novel User Authentication Scheme for VR Devices Using Auditory-Pupillary Response.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Virtual Reality (VR) has shown promising potential in many applications, such as e-business, healthcare, and social networking. Rich information regarding users' activities and online accounts is stored in VR devices. If {they are} carelessly unattended, adversarial access will cause data breaches and other critical consequences. Practical user authentication schemes for VR devices are in dire need. Current solutions, including passwords, digital PINs, and pattern locks, mostly follow conventional approaches for general personal devices. They have been criticized for deficits in both security and usability. In this work, we propose SoundLock, a novel user authentication scheme for VR devices using auditory-pupillary response as biometrics. During authentication, auditory stimuli are presented to the user via the VR headset. The corresponding pupillary response is captured by the integrated eye tracker. User's legitimacy is then determined by comparing the response with the template generated during the enrollment stage. To strike a balance between security and usability in the scheme design, an optimization problem is formulated. Due to its non-linearity, a two-stage heuristic algorithm is proposed to solve it efficiently. The solution provides necessary guidance for selecting effective auditory stimuli and determining their corresponding lengths. We demonstrate through extensive in-field experiments that SoundLock outperforms state-of-the-art biometric solutions with FAR (FRR) as low as 0.76% (0.91%) and is well received among participants in the user study.", "published": "2023-01-01", "category": "ndss", "pdf_url": "", "sub_summary": "", "source": "ndss", "doi": ""}]