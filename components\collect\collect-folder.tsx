'use client';

import { useState } from 'react';
import { FolderPlus, MoreVertical, Edit2, Trash2 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { toast } from 'react-hot-toast';
import type { Folder, CollectedPaper, CollectionState } from './collect'; // Keep this path as Folder is defined in collect.tsx
import { DownloadDialog } from './collect-download';

// 文件夹相关属性接口
export interface FolderSectionProps {
  localCollection: CollectionState;
  selectedFolder: string | null;
  draggedPaper: CollectedPaper | null;
  dragOverFolder: string | undefined;
  onSelectFolder: (folderId: string | null) => void;
  updateCollection: (updatedCollection: CollectionState) => void;
  handleDragOver: (e: React.DragEvent, folderId: string | undefined) => void;
  handleDragLeave: () => void;
  handleDrop: (e: React.DragEvent, folderId: string | undefined) => void;
}

export const FolderSection = ({
  localCollection,
  selectedFolder,
  draggedPaper,
  dragOverFolder,
  onSelectFolder,
  updateCollection,
  handleDragOver,
  handleDragLeave,
  handleDrop,
}: FolderSectionProps) => {
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [isFolderRenameDialogOpen, setIsFolderRenameDialogOpen] = useState(false);
  const [folderToRename, setFolderToRename] = useState<Folder | null>(null);
  const [newFolderNameForRename, setNewFolderNameForRename] = useState('');
  const [isDownloadDialogOpen, setIsDownloadDialogOpen] = useState(false);
  const [downloadPapers, setDownloadPapers] = useState<CollectedPaper[]>([]);
  const [downloadFolderName, setDownloadFolderName] = useState('');

  // 创建新文件夹
  const handleCreateFolder = () => {
    if (!newFolderName.trim()) {
      toast.error('文件夹名不能为空');
      return;
    }
    if (localCollection.folders.some(folder => folder.name === newFolderName.trim())) {
      toast.error('文件夹名已存在');
      return;
    }
    const newFolder: Folder = {
      id: `folder-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: newFolderName.trim(),
      createdAt: Date.now()
    };
    const updatedCollection = {
      ...localCollection,
      folders: [...localCollection.folders, newFolder]
    };
    updateCollection(updatedCollection);
    setNewFolderName('');
    setIsCreateFolderOpen(false);
    toast.success('文件夹创建成功');
  };

  // 重命名文件夹
  const handleRenameFolder = () => {
    if (!folderToRename || !newFolderNameForRename.trim()) {
      toast.error('文件夹名不能为空');
      return;
    }
    if (localCollection.folders.some(folder => 
        folder.id !== folderToRename.id && folder.name === newFolderNameForRename.trim())) {
      toast.error('文件夹名已存在');
      return;
    }
    const updatedFolders = localCollection.folders.map(folder => 
      folder.id === folderToRename.id ? { ...folder, name: newFolderNameForRename.trim() } : folder
    );
    const updatedCollection = {
      ...localCollection,
      folders: updatedFolders
    };
    updateCollection(updatedCollection);
    setIsFolderRenameDialogOpen(false);
    setFolderToRename(null);
    setNewFolderNameForRename('');
    toast.success('文件夹已重命名');
  };

  // 删除文件夹
  const handleDeleteFolder = (folderId: string) => {
    const updatedPapers = localCollection.papers.map(paper => 
      paper.folderId === folderId ? { ...paper, folderId: undefined } : paper
    );
    const updatedCollection = {
      papers: updatedPapers,
      folders: localCollection.folders.filter(folder => folder.id !== folderId)
    };
    updateCollection(updatedCollection);
    if (selectedFolder === folderId) {
      onSelectFolder(null);
    }
    toast.success('文件夹已删除');
  };

  return (
    <>
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-medium text-sm">文件夹</h3>
        <Button variant="ghost" size="icon" onClick={() => setIsCreateFolderOpen(true)}>
          <FolderPlus className="h-4 w-4" />
        </Button>
      </div>
      
      {/* 未分类选项 */}
      <div 
        className={`p-2 rounded-md cursor-pointer text-sm mb-1 flex justify-between items-center border 
          ${!selectedFolder 
            ? 'bg-gray-200 dark:bg-gray-700 border-gray-300 dark:border-gray-600' 
            : 'border-gray-200 dark:border-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700'}
          ${draggedPaper && dragOverFolder === undefined ? 'ring-2 ring-blue-500' : ''}`}
        onClick={() => onSelectFolder(null)}
        onDragOver={(e) => handleDragOver(e, undefined)}
        onDragLeave={handleDragLeave}
        onDrop={(e) => handleDrop(e, undefined)}
      >
        <div className="flex items-center">
          <span>未分类</span>
          <span className="text-xs text-gray-500 ml-1">删除文件夹内容临时存储</span>
        </div>
        <span className="text-xs text-gray-500">
          {localCollection.papers.filter(p => !p.folderId).length}
        </span>
      </div>
      
      {/* 文件夹列表 */}
      {localCollection.folders.map(folder => (
        <div 
          key={folder.id}
          className={`p-2 rounded-md cursor-pointer text-sm mb-1 flex justify-between items-center border 
            ${selectedFolder === folder.id 
              ? 'bg-gray-200 dark:bg-gray-700 border-gray-300 dark:border-gray-600' 
              : 'border-gray-200 bg-gray-50 dark:border-gray-700 hover:bg-gray-200 dark:bg-gray-900 dark:hover:bg-gray-700'}
            ${dragOverFolder === folder.id ? 'ring-2 ring-blue-500' : ''}
            ${folder.id === localCollection.defaultFolderId ? 'pl-2 before:content-[""] before:absolute before:left-0 before:h-5 before:w-1 before:bg-gray-400 dark:before:bg-white before:rounded-full before:top-1/2 before:-translate-y-1/2 relative' : ''}`}
          onClick={() => onSelectFolder(folder.id)}
          onDragOver={(e) => handleDragOver(e, folder.id)}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, folder.id)}
        >
          <span>{folder.name}</span>
          <div className="flex items-center gap-1">
            <span className="text-xs text-gray-500 mr-1">
              {localCollection.papers.filter(p => p.folderId === folder.id).length}
            </span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-6 w-6" onClick={e => e.stopPropagation()}>
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  setFolderToRename(folder);
                  setNewFolderNameForRename(folder.name);
                  setIsFolderRenameDialogOpen(true);
                }}>
                  <Edit2 className="h-4 w-4 mr-2" />
                  重命名文件夹
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  const updatedCollection = {
                    ...localCollection,
                    defaultFolderId: folder.id
                  };
                  updateCollection(updatedCollection);
                  toast.success('已设置为默认文件夹');
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                  </svg>
                  设为默认文件夹
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteFolder(folder.id);
                }}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  删除文件夹
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  // 过滤出当前文件夹下的论文
                  const papers = localCollection.papers.filter(p => p.folderId === folder.id);
                  setDownloadPapers(papers);
                  setDownloadFolderName(folder.name);
                  setIsDownloadDialogOpen(true);
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                    <polyline points="7 10 12 15 17 10"/>
                    <line x1="12" y1="15" x2="12" y2="3"/>
                  </svg>
                  下载本文件夹论文
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      ))}

      {/* 创建文件夹弹窗 */}
      <Dialog open={isCreateFolderOpen} onOpenChange={setIsCreateFolderOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>创建新文件夹</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="文件夹名称"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              className="mb-2"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleCreateFolder();
                }
              }}
            />
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">取消</Button>
            </DialogClose>
            <Button onClick={handleCreateFolder}>创建</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 重命名文件夹弹窗 */}
      <Dialog open={isFolderRenameDialogOpen} onOpenChange={setIsFolderRenameDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>重命名文件夹</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="文件夹名称"
              value={newFolderNameForRename}
              onChange={(e) => setNewFolderNameForRename(e.target.value)}
              className="mb-2"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleRenameFolder();
                }
              }}
            />
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">取消</Button>
            </DialogClose>
            <Button onClick={handleRenameFolder}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 下载对话框 */}
      <DownloadDialog
        open={isDownloadDialogOpen}
        onOpenChange={setIsDownloadDialogOpen}
        papers={downloadPapers}
        folderName={downloadFolderName}
      />
    </>
  );
}; 